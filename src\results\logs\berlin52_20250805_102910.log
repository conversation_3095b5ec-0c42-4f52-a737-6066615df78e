2025-08-05 10:29:10,429 - __main__ - INFO - berlin52 开始进化第 1 代
2025-08-05 10:29:10,429 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 10:29:10,430 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:10,433 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=8181.000, 多样性=0.980
2025-08-05 10:29:10,436 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:10,440 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.980
2025-08-05 10:29:10,442 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:10,446 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 10:29:10,446 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:10,447 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 10:29:10,447 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 10:29:10,470 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.200, 适应度梯度: -1230.120, 聚类评分: 0.000, 覆盖率: 0.149, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:10,471 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 10:29:10,471 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 10:29:10,471 - visualization.landscape_visualizer - INFO - 设置当前实例名: berlin52
2025-08-05 10:29:10,476 - visualization.landscape_visualizer - INFO - 插值约束: 1 个点被约束到最小值 8181.00
2025-08-05 10:29:10,478 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=9.2%, 梯度: 805.56 → 731.65
2025-08-05 10:29:10,620 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_berlin52\landscape_berlin52_iter_131_20250805_102910.html
2025-08-05 10:29:10,710 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_berlin52\dashboard_berlin52_iter_131_20250805_102910.html
2025-08-05 10:29:10,710 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 131
2025-08-05 10:29:10,710 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 10:29:10,710 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2639秒
2025-08-05 10:29:10,711 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 262, 'max_size': 500, 'hits': 0, 'misses': 262, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 878, 'misses': 452, 'hit_rate': 0.6601503759398496, 'evictions': 352, 'ttl': 7200}}
2025-08-05 10:29:10,711 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -1230.1200000000006, 'local_optima_density': 0.2, 'gradient_variance': 127357586.36959997, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.149, 'fitness_entropy': 0.9911594714322186, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -1230.120)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.149)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360950.4714468, 'performance_metrics': {}}}
2025-08-05 10:29:10,711 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:10,711 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:10,711 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:10,711 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:10,712 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:10,712 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 10:29:10,712 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:10,713 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:10,713 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:10,713 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:10,713 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:10,713 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:10,713 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:10,713 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:10,713 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:10,713 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:10,717 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 10:29:10,717 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,718 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10075.0, 路径长度: 52, 收集中间解: 0
2025-08-05 10:29:10,718 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 7, 20, 6, 1, 41, 29, 22, 19, 49, 15, 43, 33, 34, 35, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 48, 31, 21, 30, 17, 2, 18, 44, 40, 8, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 16], 'cur_cost': 10075.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:10,718 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 10075.00)
2025-08-05 10:29:10,718 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:10,718 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:10,719 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:10,727 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 52
2025-08-05 10:29:10,728 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,729 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20369.0, 路径长度: 52, 收集中间解: 0
2025-08-05 10:29:10,729 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [27, 3, 45, 4, 23, 18, 0, 20, 38, 49, 39, 35, 48, 34, 30, 19, 2, 44, 9, 42, 37, 43, 24, 25, 36, 21, 22, 31, 16, 47, 15, 29, 6, 17, 33, 28, 5, 32, 8, 50, 14, 7, 11, 26, 12, 13, 51, 46, 41, 1, 40, 10], 'cur_cost': 20369.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:10,730 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 20369.00)
2025-08-05 10:29:10,730 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:10,730 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:10,730 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:10,734 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 10:29:10,734 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,734 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10651.0, 路径长度: 52, 收集中间解: 0
2025-08-05 10:29:10,734 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 19, 18, 12, 26, 27, 25, 46, 13, 51, 10, 50, 11, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 22, 30, 17, 21, 48, 31, 44, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10651.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:10,735 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 10651.00)
2025-08-05 10:29:10,735 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:10,735 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:10,735 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:10,738 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 10:29:10,738 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,738 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10093.0, 路径长度: 52, 收集中间解: 0
2025-08-05 10:29:10,739 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 14, 15, 19, 49, 22, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 43, 28, 29, 20, 16, 2, 18, 44, 40, 7, 9, 8, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 41, 6, 1], 'cur_cost': 10093.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:10,739 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 10093.00)
2025-08-05 10:29:10,739 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:29:10,739 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:29:10,739 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:10,742 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 10:29:10,742 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,742 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9917.0, 路径长度: 52, 收集中间解: 0
2025-08-05 10:29:10,742 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 19, 20, 12, 26, 27, 25, 46, 13, 51, 10, 50, 11, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 41, 6, 1, 29, 28], 'cur_cost': 9917.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:10,742 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 9917.00)
2025-08-05 10:29:10,742 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:29:10,743 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:29:10,743 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:10,747 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 52
2025-08-05 10:29:10,748 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,748 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20472.0, 路径长度: 52, 收集中间解: 0
2025-08-05 10:29:10,748 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [22, 44, 18, 31, 37, 0, 20, 28, 30, 15, 45, 19, 34, 33, 43, 29, 38, 49, 41, 14, 39, 21, 3, 48, 47, 7, 8, 5, 24, 23, 50, 10, 25, 4, 36, 11, 32, 40, 9, 35, 16, 6, 1, 42, 17, 46, 12, 13, 26, 27, 51, 2], 'cur_cost': 20472.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:10,748 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 20472.00)
2025-08-05 10:29:10,748 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:10,748 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:10,748 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:10,750 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 10:29:10,750 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,750 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9935.0, 路径长度: 52, 收集中间解: 0
2025-08-05 10:29:10,750 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 3, 23, 17, 30, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 4, 14, 5, 24, 45, 43, 15, 49, 19, 22, 29, 20, 16, 2, 18, 44, 40, 7, 9, 8, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 41, 6, 1], 'cur_cost': 9935.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:10,750 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 9935.00)
2025-08-05 10:29:10,750 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:10,751 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:10,751 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:10,755 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 52
2025-08-05 10:29:10,755 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,755 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21249.0, 路径长度: 52, 收集中间解: 0
2025-08-05 10:29:10,755 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [27, 23, 47, 3, 37, 14, 39, 49, 4, 17, 7, 35, 15, 44, 42, 8, 34, 33, 18, 43, 45, 19, 24, 46, 12, 13, 38, 0, 5, 36, 2, 48, 31, 22, 41, 1, 21, 29, 30, 28, 16, 11, 26, 50, 51, 25, 32, 9, 20, 6, 40, 10], 'cur_cost': 21249.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:10,755 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 21249.00)
2025-08-05 10:29:10,755 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:29:10,756 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:10,756 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:10,756 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 33158.0
2025-08-05 10:29:10,761 - ExploitationExpert - INFO - res_population_num: 1
2025-08-05 10:29:10,761 - ExploitationExpert - INFO - res_population_costs: [7542.0]
2025-08-05 10:29:10,761 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64)]
2025-08-05 10:29:10,761 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:10,762 - ExploitationExpert - INFO - populations: [{'tour': [0, 7, 20, 6, 1, 41, 29, 22, 19, 49, 15, 43, 33, 34, 35, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 48, 31, 21, 30, 17, 2, 18, 44, 40, 8, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 16], 'cur_cost': 10075.0}, {'tour': [27, 3, 45, 4, 23, 18, 0, 20, 38, 49, 39, 35, 48, 34, 30, 19, 2, 44, 9, 42, 37, 43, 24, 25, 36, 21, 22, 31, 16, 47, 15, 29, 6, 17, 33, 28, 5, 32, 8, 50, 14, 7, 11, 26, 12, 13, 51, 46, 41, 1, 40, 10], 'cur_cost': 20369.0}, {'tour': [0, 19, 18, 12, 26, 27, 25, 46, 13, 51, 10, 50, 11, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 22, 30, 17, 21, 48, 31, 44, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10651.0}, {'tour': [0, 14, 15, 19, 49, 22, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 43, 28, 29, 20, 16, 2, 18, 44, 40, 7, 9, 8, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 41, 6, 1], 'cur_cost': 10093.0}, {'tour': [0, 19, 20, 12, 26, 27, 25, 46, 13, 51, 10, 50, 11, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 41, 6, 1, 29, 28], 'cur_cost': 9917.0}, {'tour': [22, 44, 18, 31, 37, 0, 20, 28, 30, 15, 45, 19, 34, 33, 43, 29, 38, 49, 41, 14, 39, 21, 3, 48, 47, 7, 8, 5, 24, 23, 50, 10, 25, 4, 36, 11, 32, 40, 9, 35, 16, 6, 1, 42, 17, 46, 12, 13, 26, 27, 51, 2], 'cur_cost': 20472.0}, {'tour': [0, 3, 23, 17, 30, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 4, 14, 5, 24, 45, 43, 15, 49, 19, 22, 29, 20, 16, 2, 18, 44, 40, 7, 9, 8, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 41, 6, 1], 'cur_cost': 9935.0}, {'tour': [27, 23, 47, 3, 37, 14, 39, 49, 4, 17, 7, 35, 15, 44, 42, 8, 34, 33, 18, 43, 45, 19, 24, 46, 12, 13, 38, 0, 5, 36, 2, 48, 31, 22, 41, 1, 21, 29, 30, 28, 16, 11, 26, 50, 51, 25, 32, 9, 20, 6, 40, 10], 'cur_cost': 21249.0}, {'tour': array([48, 51, 31, 50, 29, 34,  8, 19, 35, 39, 43,  4, 22, 23, 45, 27, 32,
       46, 18,  6, 33, 10,  0, 26,  2, 37, 49,  7, 12,  1, 17,  3, 25, 20,
        5, 44, 30, 38,  9, 13, 42, 24, 41, 16, 47, 11, 28, 15, 40, 21, 14,
       36], dtype=int64), 'cur_cost': 33158.0}, {'tour': array([30, 33,  2, 49, 46,  1, 36, 13, 32, 37, 44, 35, 51, 34, 22, 15, 19,
        7, 27, 38,  8,  3,  9, 21, 16, 39, 23,  6, 42, 12, 28,  5, 47, 26,
       50, 48, 45, 43, 11, 29, 20, 25, 10, 14,  4, 24, 18, 31, 17, 40,  0,
       41], dtype=int64), 'cur_cost': 28731.0}]
2025-08-05 10:29:10,762 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:10,763 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 339, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 339, 'cache_hits': 0, 'similarity_calculations': 1746, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:10,763 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([48, 51, 31, 50, 29, 34,  8, 19, 35, 39, 43,  4, 22, 23, 45, 27, 32,
       46, 18,  6, 33, 10,  0, 26,  2, 37, 49,  7, 12,  1, 17,  3, 25, 20,
        5, 44, 30, 38,  9, 13, 42, 24, 41, 16, 47, 11, 28, 15, 40, 21, 14,
       36], dtype=int64), 'cur_cost': 33158.0, 'intermediate_solutions': [{'tour': array([16, 42, 40, 12,  2,  7, 31, 37, 21, 43, 19, 27,  3, 44, 32, 51,  1,
       34, 30, 23, 11,  0, 50, 35,  5, 38, 41, 45, 46, 18, 36, 25,  6, 47,
       28, 22,  4, 14, 29, 17, 15, 49, 39, 10, 13,  8, 26, 24, 33,  9, 20,
       48], dtype=int64), 'cur_cost': 30677.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([12, 16, 42, 40,  2,  7, 31, 37, 21, 43, 19, 27,  3, 44, 32, 51,  1,
       34, 30, 23, 11,  0, 50, 35,  5, 38, 41, 45, 46, 18, 36, 25,  6, 47,
       28, 22,  4, 14, 29, 17, 15, 49, 39, 10, 13,  8, 26, 24, 33,  9, 20,
       48], dtype=int64), 'cur_cost': 30325.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 2, 12, 16, 42, 40,  7, 31, 37, 21, 43, 19, 27,  3, 44, 32, 51,  1,
       34, 30, 23, 11,  0, 50, 35,  5, 38, 41, 45, 46, 18, 36, 25,  6, 47,
       28, 22,  4, 14, 29, 17, 15, 49, 39, 10, 13,  8, 26, 24, 33,  9, 20,
       48], dtype=int64), 'cur_cost': 30411.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([40, 12, 16, 42,  2,  7, 31, 37, 21, 43, 19, 27,  3, 44, 32, 51,  1,
       34, 30, 23, 11,  0, 50, 35,  5, 38, 41, 45, 46, 18, 36, 25,  6, 47,
       28, 22,  4, 14, 29, 17, 15, 49, 39, 10, 13,  8, 26, 24, 33,  9, 20,
       48], dtype=int64), 'cur_cost': 30881.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([40,  2, 12, 16, 42,  7, 31, 37, 21, 43, 19, 27,  3, 44, 32, 51,  1,
       34, 30, 23, 11,  0, 50, 35,  5, 38, 41, 45, 46, 18, 36, 25,  6, 47,
       28, 22,  4, 14, 29, 17, 15, 49, 39, 10, 13,  8, 26, 24, 33,  9, 20,
       48], dtype=int64), 'cur_cost': 30622.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:10,763 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 33158.00)
2025-08-05 10:29:10,764 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:29:10,764 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:10,764 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:10,764 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 27642.0
2025-08-05 10:29:10,770 - ExploitationExpert - INFO - res_population_num: 1
2025-08-05 10:29:10,770 - ExploitationExpert - INFO - res_population_costs: [7542.0]
2025-08-05 10:29:10,770 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64)]
2025-08-05 10:29:10,771 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:10,771 - ExploitationExpert - INFO - populations: [{'tour': [0, 7, 20, 6, 1, 41, 29, 22, 19, 49, 15, 43, 33, 34, 35, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 48, 31, 21, 30, 17, 2, 18, 44, 40, 8, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 16], 'cur_cost': 10075.0}, {'tour': [27, 3, 45, 4, 23, 18, 0, 20, 38, 49, 39, 35, 48, 34, 30, 19, 2, 44, 9, 42, 37, 43, 24, 25, 36, 21, 22, 31, 16, 47, 15, 29, 6, 17, 33, 28, 5, 32, 8, 50, 14, 7, 11, 26, 12, 13, 51, 46, 41, 1, 40, 10], 'cur_cost': 20369.0}, {'tour': [0, 19, 18, 12, 26, 27, 25, 46, 13, 51, 10, 50, 11, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 22, 30, 17, 21, 48, 31, 44, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10651.0}, {'tour': [0, 14, 15, 19, 49, 22, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 43, 28, 29, 20, 16, 2, 18, 44, 40, 7, 9, 8, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 41, 6, 1], 'cur_cost': 10093.0}, {'tour': [0, 19, 20, 12, 26, 27, 25, 46, 13, 51, 10, 50, 11, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 41, 6, 1, 29, 28], 'cur_cost': 9917.0}, {'tour': [22, 44, 18, 31, 37, 0, 20, 28, 30, 15, 45, 19, 34, 33, 43, 29, 38, 49, 41, 14, 39, 21, 3, 48, 47, 7, 8, 5, 24, 23, 50, 10, 25, 4, 36, 11, 32, 40, 9, 35, 16, 6, 1, 42, 17, 46, 12, 13, 26, 27, 51, 2], 'cur_cost': 20472.0}, {'tour': [0, 3, 23, 17, 30, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 4, 14, 5, 24, 45, 43, 15, 49, 19, 22, 29, 20, 16, 2, 18, 44, 40, 7, 9, 8, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 41, 6, 1], 'cur_cost': 9935.0}, {'tour': [27, 23, 47, 3, 37, 14, 39, 49, 4, 17, 7, 35, 15, 44, 42, 8, 34, 33, 18, 43, 45, 19, 24, 46, 12, 13, 38, 0, 5, 36, 2, 48, 31, 22, 41, 1, 21, 29, 30, 28, 16, 11, 26, 50, 51, 25, 32, 9, 20, 6, 40, 10], 'cur_cost': 21249.0}, {'tour': array([48, 51, 31, 50, 29, 34,  8, 19, 35, 39, 43,  4, 22, 23, 45, 27, 32,
       46, 18,  6, 33, 10,  0, 26,  2, 37, 49,  7, 12,  1, 17,  3, 25, 20,
        5, 44, 30, 38,  9, 13, 42, 24, 41, 16, 47, 11, 28, 15, 40, 21, 14,
       36], dtype=int64), 'cur_cost': 33158.0}, {'tour': array([ 2, 18, 35, 43, 12, 28, 27, 25, 47, 42, 39, 23, 48,  1, 37,  9,  4,
       38, 22, 30, 19, 45, 50, 11, 26,  7, 46, 17, 36, 20, 14,  6,  0, 41,
        8, 31, 51, 40, 32, 10, 13, 16, 15, 44, 29, 21, 34, 49,  3,  5, 24,
       33], dtype=int64), 'cur_cost': 27642.0}]
2025-08-05 10:29:10,772 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:10,772 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 340, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 340, 'cache_hits': 0, 'similarity_calculations': 1747, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:10,773 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([ 2, 18, 35, 43, 12, 28, 27, 25, 47, 42, 39, 23, 48,  1, 37,  9,  4,
       38, 22, 30, 19, 45, 50, 11, 26,  7, 46, 17, 36, 20, 14,  6,  0, 41,
        8, 31, 51, 40, 32, 10, 13, 16, 15, 44, 29, 21, 34, 49,  3,  5, 24,
       33], dtype=int64), 'cur_cost': 27642.0, 'intermediate_solutions': [{'tour': array([ 2, 33, 30, 49, 46,  1, 36, 13, 32, 37, 44, 35, 51, 34, 22, 15, 19,
        7, 27, 38,  8,  3,  9, 21, 16, 39, 23,  6, 42, 12, 28,  5, 47, 26,
       50, 48, 45, 43, 11, 29, 20, 25, 10, 14,  4, 24, 18, 31, 17, 40,  0,
       41], dtype=int64), 'cur_cost': 28641.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([49,  2, 33, 30, 46,  1, 36, 13, 32, 37, 44, 35, 51, 34, 22, 15, 19,
        7, 27, 38,  8,  3,  9, 21, 16, 39, 23,  6, 42, 12, 28,  5, 47, 26,
       50, 48, 45, 43, 11, 29, 20, 25, 10, 14,  4, 24, 18, 31, 17, 40,  0,
       41], dtype=int64), 'cur_cost': 29052.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([46, 49,  2, 33, 30,  1, 36, 13, 32, 37, 44, 35, 51, 34, 22, 15, 19,
        7, 27, 38,  8,  3,  9, 21, 16, 39, 23,  6, 42, 12, 28,  5, 47, 26,
       50, 48, 45, 43, 11, 29, 20, 25, 10, 14,  4, 24, 18, 31, 17, 40,  0,
       41], dtype=int64), 'cur_cost': 28775.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([30, 49,  2, 33, 46,  1, 36, 13, 32, 37, 44, 35, 51, 34, 22, 15, 19,
        7, 27, 38,  8,  3,  9, 21, 16, 39, 23,  6, 42, 12, 28,  5, 47, 26,
       50, 48, 45, 43, 11, 29, 20, 25, 10, 14,  4, 24, 18, 31, 17, 40,  0,
       41], dtype=int64), 'cur_cost': 28763.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([30, 46, 49,  2, 33,  1, 36, 13, 32, 37, 44, 35, 51, 34, 22, 15, 19,
        7, 27, 38,  8,  3,  9, 21, 16, 39, 23,  6, 42, 12, 28,  5, 47, 26,
       50, 48, 45, 43, 11, 29, 20, 25, 10, 14,  4, 24, 18, 31, 17, 40,  0,
       41], dtype=int64), 'cur_cost': 28977.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:10,773 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 27642.00)
2025-08-05 10:29:10,773 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:10,773 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:10,775 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 20, 6, 1, 41, 29, 22, 19, 49, 15, 43, 33, 34, 35, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 48, 31, 21, 30, 17, 2, 18, 44, 40, 8, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 16], 'cur_cost': 10075.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [27, 3, 45, 4, 23, 18, 0, 20, 38, 49, 39, 35, 48, 34, 30, 19, 2, 44, 9, 42, 37, 43, 24, 25, 36, 21, 22, 31, 16, 47, 15, 29, 6, 17, 33, 28, 5, 32, 8, 50, 14, 7, 11, 26, 12, 13, 51, 46, 41, 1, 40, 10], 'cur_cost': 20369.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 18, 12, 26, 27, 25, 46, 13, 51, 10, 50, 11, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 22, 30, 17, 21, 48, 31, 44, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10651.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 15, 19, 49, 22, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 43, 28, 29, 20, 16, 2, 18, 44, 40, 7, 9, 8, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 41, 6, 1], 'cur_cost': 10093.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 20, 12, 26, 27, 25, 46, 13, 51, 10, 50, 11, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 41, 6, 1, 29, 28], 'cur_cost': 9917.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [22, 44, 18, 31, 37, 0, 20, 28, 30, 15, 45, 19, 34, 33, 43, 29, 38, 49, 41, 14, 39, 21, 3, 48, 47, 7, 8, 5, 24, 23, 50, 10, 25, 4, 36, 11, 32, 40, 9, 35, 16, 6, 1, 42, 17, 46, 12, 13, 26, 27, 51, 2], 'cur_cost': 20472.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 23, 17, 30, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 4, 14, 5, 24, 45, 43, 15, 49, 19, 22, 29, 20, 16, 2, 18, 44, 40, 7, 9, 8, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 41, 6, 1], 'cur_cost': 9935.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [27, 23, 47, 3, 37, 14, 39, 49, 4, 17, 7, 35, 15, 44, 42, 8, 34, 33, 18, 43, 45, 19, 24, 46, 12, 13, 38, 0, 5, 36, 2, 48, 31, 22, 41, 1, 21, 29, 30, 28, 16, 11, 26, 50, 51, 25, 32, 9, 20, 6, 40, 10], 'cur_cost': 21249.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([48, 51, 31, 50, 29, 34,  8, 19, 35, 39, 43,  4, 22, 23, 45, 27, 32,
       46, 18,  6, 33, 10,  0, 26,  2, 37, 49,  7, 12,  1, 17,  3, 25, 20,
        5, 44, 30, 38,  9, 13, 42, 24, 41, 16, 47, 11, 28, 15, 40, 21, 14,
       36], dtype=int64), 'cur_cost': 33158.0, 'intermediate_solutions': [{'tour': array([16, 42, 40, 12,  2,  7, 31, 37, 21, 43, 19, 27,  3, 44, 32, 51,  1,
       34, 30, 23, 11,  0, 50, 35,  5, 38, 41, 45, 46, 18, 36, 25,  6, 47,
       28, 22,  4, 14, 29, 17, 15, 49, 39, 10, 13,  8, 26, 24, 33,  9, 20,
       48], dtype=int64), 'cur_cost': 30677.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([12, 16, 42, 40,  2,  7, 31, 37, 21, 43, 19, 27,  3, 44, 32, 51,  1,
       34, 30, 23, 11,  0, 50, 35,  5, 38, 41, 45, 46, 18, 36, 25,  6, 47,
       28, 22,  4, 14, 29, 17, 15, 49, 39, 10, 13,  8, 26, 24, 33,  9, 20,
       48], dtype=int64), 'cur_cost': 30325.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 2, 12, 16, 42, 40,  7, 31, 37, 21, 43, 19, 27,  3, 44, 32, 51,  1,
       34, 30, 23, 11,  0, 50, 35,  5, 38, 41, 45, 46, 18, 36, 25,  6, 47,
       28, 22,  4, 14, 29, 17, 15, 49, 39, 10, 13,  8, 26, 24, 33,  9, 20,
       48], dtype=int64), 'cur_cost': 30411.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([40, 12, 16, 42,  2,  7, 31, 37, 21, 43, 19, 27,  3, 44, 32, 51,  1,
       34, 30, 23, 11,  0, 50, 35,  5, 38, 41, 45, 46, 18, 36, 25,  6, 47,
       28, 22,  4, 14, 29, 17, 15, 49, 39, 10, 13,  8, 26, 24, 33,  9, 20,
       48], dtype=int64), 'cur_cost': 30881.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([40,  2, 12, 16, 42,  7, 31, 37, 21, 43, 19, 27,  3, 44, 32, 51,  1,
       34, 30, 23, 11,  0, 50, 35,  5, 38, 41, 45, 46, 18, 36, 25,  6, 47,
       28, 22,  4, 14, 29, 17, 15, 49, 39, 10, 13,  8, 26, 24, 33,  9, 20,
       48], dtype=int64), 'cur_cost': 30622.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 2, 18, 35, 43, 12, 28, 27, 25, 47, 42, 39, 23, 48,  1, 37,  9,  4,
       38, 22, 30, 19, 45, 50, 11, 26,  7, 46, 17, 36, 20, 14,  6,  0, 41,
        8, 31, 51, 40, 32, 10, 13, 16, 15, 44, 29, 21, 34, 49,  3,  5, 24,
       33], dtype=int64), 'cur_cost': 27642.0, 'intermediate_solutions': [{'tour': array([ 2, 33, 30, 49, 46,  1, 36, 13, 32, 37, 44, 35, 51, 34, 22, 15, 19,
        7, 27, 38,  8,  3,  9, 21, 16, 39, 23,  6, 42, 12, 28,  5, 47, 26,
       50, 48, 45, 43, 11, 29, 20, 25, 10, 14,  4, 24, 18, 31, 17, 40,  0,
       41], dtype=int64), 'cur_cost': 28641.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([49,  2, 33, 30, 46,  1, 36, 13, 32, 37, 44, 35, 51, 34, 22, 15, 19,
        7, 27, 38,  8,  3,  9, 21, 16, 39, 23,  6, 42, 12, 28,  5, 47, 26,
       50, 48, 45, 43, 11, 29, 20, 25, 10, 14,  4, 24, 18, 31, 17, 40,  0,
       41], dtype=int64), 'cur_cost': 29052.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([46, 49,  2, 33, 30,  1, 36, 13, 32, 37, 44, 35, 51, 34, 22, 15, 19,
        7, 27, 38,  8,  3,  9, 21, 16, 39, 23,  6, 42, 12, 28,  5, 47, 26,
       50, 48, 45, 43, 11, 29, 20, 25, 10, 14,  4, 24, 18, 31, 17, 40,  0,
       41], dtype=int64), 'cur_cost': 28775.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([30, 49,  2, 33, 46,  1, 36, 13, 32, 37, 44, 35, 51, 34, 22, 15, 19,
        7, 27, 38,  8,  3,  9, 21, 16, 39, 23,  6, 42, 12, 28,  5, 47, 26,
       50, 48, 45, 43, 11, 29, 20, 25, 10, 14,  4, 24, 18, 31, 17, 40,  0,
       41], dtype=int64), 'cur_cost': 28763.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([30, 46, 49,  2, 33,  1, 36, 13, 32, 37, 44, 35, 51, 34, 22, 15, 19,
        7, 27, 38,  8,  3,  9, 21, 16, 39, 23,  6, 42, 12, 28,  5, 47, 26,
       50, 48, 45, 43, 11, 29, 20, 25, 10, 14,  4, 24, 18, 31, 17, 40,  0,
       41], dtype=int64), 'cur_cost': 28977.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:29:10,775 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:10,775 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:10,778 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9917.000, 多样性=0.952
2025-08-05 10:29:10,778 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 10:29:10,778 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 10:29:10,778 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:10,778 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.022545032570769934, 'best_improvement': -0.21219899767754552}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.029206625980819766}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.009228822766855374, 'recent_improvements': [-0.05381899370604224, 0.00736246733382087, -0.03536134817233149], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 1, 'new_count': 1, 'count_change': 0, 'old_best_cost': 7542.0, 'new_best_cost': 7542.0, 'quality_improvement': 0.0, 'old_diversity': 0.0, 'new_diversity': 0.0, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启']}
2025-08-05 10:29:10,778 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 10:29:10,779 - __main__ - INFO - berlin52 开始进化第 2 代
2025-08-05 10:29:10,779 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 10:29:10,779 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:10,779 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9917.000, 多样性=0.952
2025-08-05 10:29:10,780 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:10,782 - PathExpert - INFO - 路径结构分析完成: 公共边数量=7, 路径相似性=0.952
2025-08-05 10:29:10,782 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:10,782 - EliteExpert - INFO - 精英解分析完成: 精英解数量=1, 多样性=0.000
2025-08-05 10:29:10,784 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 10:29:10,784 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:10,784 - LandscapeExpert - INFO - 添加精英解数据: 1个精英解
2025-08-05 10:29:10,784 - LandscapeExpert - INFO - 数据提取成功: 11个路径, 11个适应度值
2025-08-05 10:29:10,802 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.091, 适应度梯度: -2444.691, 聚类评分: 0.000, 覆盖率: 0.150, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:10,802 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 10:29:10,802 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:10,802 - visualization.landscape_visualizer - INFO - 设置当前实例名: berlin52
2025-08-05 10:29:10,805 - visualization.landscape_visualizer - INFO - 插值约束: 28 个点被约束到最小值 7542.00
2025-08-05 10:29:10,807 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=5.9%, 梯度: 871.25 → 819.47
2025-08-05 10:29:10,913 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_berlin52\landscape_berlin52_iter_132_20250805_102910.html
2025-08-05 10:29:11,000 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_berlin52\dashboard_berlin52_iter_132_20250805_102910.html
2025-08-05 10:29:11,000 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 132
2025-08-05 10:29:11,000 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 10:29:11,000 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2157秒
2025-08-05 10:29:11,001 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.09090909090909091, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -2444.690909090909, 'local_optima_density': 0.09090909090909091, 'gradient_variance': 52146657.595371895, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1499, 'fitness_entropy': 0.8491853220906186, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -2444.691)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.150)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360950.802484, 'performance_metrics': {}}}
2025-08-05 10:29:11,001 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:11,001 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:11,001 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:11,002 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:11,002 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:11,002 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 10:29:11,003 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:11,003 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:11,003 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:11,003 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:11,003 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:11,004 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:11,004 - experts.management.collaboration_manager - INFO - 识别精英个体: {4, 6} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:11,004 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:11,004 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:11,004 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:11,007 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 10:29:11,007 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,007 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,008 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,008 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,008 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11757.0, 路径长度: 52, 收集中间解: 3
2025-08-05 10:29:11,009 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 15, 12, 6, 1, 41, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 13, 42, 9, 8, 7, 40, 18, 44, 2, 16, 32], 'cur_cost': 11757.0, 'intermediate_solutions': [{'tour': [0, 7, 20, 6, 1, 41, 29, 22, 19, 49, 15, 43, 33, 34, 35, 38, 39, 37, 36, 47, 50, 4, 14, 5, 3, 24, 45, 48, 31, 21, 30, 17, 2, 18, 44, 40, 8, 9, 42, 32, 23, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 16], 'cur_cost': 11396.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 20, 6, 1, 41, 29, 22, 4, 23, 47, 36, 37, 39, 38, 35, 34, 33, 43, 15, 49, 19, 14, 5, 3, 24, 45, 48, 31, 21, 30, 17, 2, 18, 44, 40, 8, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 16], 'cur_cost': 10818.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 20, 6, 1, 41, 29, 22, 19, 49, 15, 43, 33, 34, 35, 38, 39, 37, 47, 23, 4, 14, 5, 3, 24, 45, 48, 31, 21, 30, 17, 2, 18, 44, 40, 8, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 36, 51, 10, 28, 16], 'cur_cost': 11709.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:11,009 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 11757.00)
2025-08-05 10:29:11,009 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:11,009 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:11,009 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:11,015 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 52
2025-08-05 10:29:11,016 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,016 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,016 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,017 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,017 - ExplorationExpert - INFO - 探索路径生成完成，成本: 22545.0, 路径长度: 52, 收集中间解: 3
2025-08-05 10:29:11,017 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [33, 47, 49, 23, 4, 31, 44, 2, 37, 38, 17, 0, 19, 30, 28, 25, 14, 35, 7, 39, 9, 21, 18, 34, 11, 3, 42, 22, 29, 5, 36, 24, 46, 27, 10, 51, 50, 48, 20, 43, 40, 16, 6, 15, 41, 8, 1, 45, 26, 12, 13, 32], 'cur_cost': 22545.0, 'intermediate_solutions': [{'tour': [27, 3, 45, 4, 23, 18, 0, 20, 38, 49, 39, 35, 48, 34, 30, 19, 2, 44, 9, 42, 37, 43, 24, 25, 36, 21, 22, 31, 16, 47, 15, 29, 6, 17, 33, 28, 5, 46, 8, 50, 14, 7, 11, 26, 12, 13, 51, 32, 41, 1, 40, 10], 'cur_cost': 21935.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [27, 3, 45, 4, 23, 18, 0, 20, 38, 49, 39, 35, 48, 34, 30, 19, 2, 47, 16, 31, 22, 21, 36, 25, 24, 43, 37, 42, 9, 44, 15, 29, 6, 17, 33, 28, 5, 32, 8, 50, 14, 7, 11, 26, 12, 13, 51, 46, 41, 1, 40, 10], 'cur_cost': 20868.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [27, 3, 45, 4, 23, 18, 0, 38, 49, 39, 35, 48, 34, 30, 19, 2, 44, 9, 42, 37, 43, 24, 25, 36, 21, 22, 31, 16, 47, 15, 29, 6, 17, 33, 28, 5, 32, 8, 50, 14, 7, 11, 26, 12, 13, 51, 46, 41, 1, 40, 20, 10], 'cur_cost': 20454.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:11,017 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 22545.00)
2025-08-05 10:29:11,018 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:11,018 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:11,018 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:11,020 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 10:29:11,020 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,020 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,021 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,021 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,021 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9573.0, 路径长度: 52, 收集中间解: 3
2025-08-05 10:29:11,021 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 20, 16, 7, 40, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 21, 2, 8, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 41, 6, 1], 'cur_cost': 9573.0, 'intermediate_solutions': [{'tour': [0, 19, 18, 12, 26, 27, 25, 46, 13, 51, 10, 50, 11, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 22, 30, 17, 21, 48, 31, 44, 40, 7, 9, 8, 42, 20, 2, 16, 32, 29, 28, 41, 6, 1], 'cur_cost': 12203.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 18, 12, 26, 27, 25, 46, 13, 51, 10, 50, 11, 24, 3, 8, 9, 7, 40, 44, 31, 48, 21, 17, 30, 22, 49, 15, 45, 43, 33, 34, 35, 38, 36, 39, 37, 47, 23, 14, 4, 5, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 11062.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 18, 12, 26, 27, 25, 46, 13, 51, 10, 50, 11, 24, 3, 5, 4, 14, 23, 45, 47, 37, 39, 36, 38, 35, 34, 33, 43, 15, 49, 22, 30, 17, 21, 48, 31, 44, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10745.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:11,022 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 9573.00)
2025-08-05 10:29:11,022 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:11,022 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:11,022 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:11,023 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 52
2025-08-05 10:29:11,023 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,023 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,024 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,024 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,024 - ExplorationExpert - INFO - 探索路径生成完成，成本: 22413.0, 路径长度: 52, 收集中间解: 3
2025-08-05 10:29:11,024 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [33, 20, 16, 23, 40, 31, 30, 2, 17, 21, 48, 0, 35, 34, 38, 25, 14, 37, 7, 39, 9, 4, 18, 5, 3, 24, 45, 22, 49, 19, 36, 29, 28, 46, 10, 51, 27, 11, 50, 43, 26, 13, 42, 15, 8, 12, 1, 41, 44, 6, 32, 47], 'cur_cost': 22413.0, 'intermediate_solutions': [{'tour': [0, 14, 15, 19, 49, 22, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 51, 43, 28, 29, 20, 16, 2, 18, 44, 40, 7, 9, 8, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 45, 10, 41, 6, 1], 'cur_cost': 12613.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 15, 19, 49, 22, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 43, 18, 2, 16, 20, 29, 28, 44, 40, 7, 9, 8, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 41, 6, 1], 'cur_cost': 10760.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 15, 19, 49, 22, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 43, 28, 29, 20, 16, 2, 18, 44, 40, 7, 9, 8, 42, 32, 50, 11, 27, 51, 26, 25, 46, 12, 13, 10, 41, 6, 1], 'cur_cost': 10825.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:11,024 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 22413.00)
2025-08-05 10:29:11,025 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:29:11,025 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:29:11,025 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:11,028 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 10:29:11,028 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,028 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,029 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,029 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,029 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11457.0, 路径长度: 52, 收集中间解: 3
2025-08-05 10:29:11,029 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 8, 18, 22, 19, 49, 15, 43, 33, 34, 35, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 48, 31, 21, 30, 17, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 7, 40, 44, 32, 41, 6, 1], 'cur_cost': 11457.0, 'intermediate_solutions': [{'tour': [0, 19, 20, 12, 26, 27, 25, 46, 13, 51, 10, 50, 11, 24, 3, 5, 4, 35, 23, 47, 37, 39, 36, 38, 14, 34, 33, 43, 45, 15, 49, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 41, 6, 1, 29, 28], 'cur_cost': 10409.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 20, 12, 26, 27, 25, 46, 41, 16, 2, 32, 42, 8, 9, 7, 40, 18, 44, 31, 48, 21, 17, 30, 22, 49, 15, 45, 43, 33, 34, 35, 38, 36, 39, 37, 47, 23, 14, 4, 5, 3, 24, 11, 50, 10, 51, 13, 6, 1, 29, 28], 'cur_cost': 12091.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 20, 12, 26, 27, 25, 46, 13, 51, 10, 50, 11, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 22, 30, 17, 21, 48, 31, 44, 18, 40, 9, 7, 8, 42, 32, 2, 16, 41, 6, 1, 29, 28], 'cur_cost': 10197.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:11,030 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 11457.00)
2025-08-05 10:29:11,030 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:29:11,030 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:29:11,030 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:11,032 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 10:29:11,032 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,032 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,033 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,033 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,033 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11736.0, 路径长度: 52, 收集中间解: 3
2025-08-05 10:29:11,033 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 9, 12, 15, 49, 19, 22, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 28, 29, 20, 16, 2, 18, 44, 40, 7, 8, 42, 32, 50, 11, 27, 26, 25, 46, 13, 51, 10, 41, 6, 1], 'cur_cost': 11736.0, 'intermediate_solutions': [{'tour': [22, 44, 18, 31, 37, 0, 20, 28, 30, 15, 45, 19, 34, 33, 43, 29, 38, 49, 41, 12, 39, 21, 3, 48, 47, 7, 8, 5, 24, 23, 50, 10, 25, 4, 36, 11, 32, 40, 9, 35, 16, 6, 1, 42, 17, 46, 14, 13, 26, 27, 51, 2], 'cur_cost': 22857.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 44, 18, 31, 37, 0, 20, 28, 30, 15, 45, 19, 34, 33, 43, 29, 38, 49, 41, 14, 39, 21, 3, 48, 47, 7, 8, 5, 24, 23, 50, 10, 25, 4, 36, 11, 32, 40, 9, 35, 16, 6, 1, 42, 17, 46, 12, 13, 27, 26, 51, 2], 'cur_cost': 20494.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 44, 18, 31, 37, 0, 20, 28, 30, 15, 45, 19, 34, 33, 43, 29, 38, 49, 14, 39, 21, 3, 48, 47, 7, 8, 5, 24, 41, 23, 50, 10, 25, 4, 36, 11, 32, 40, 9, 35, 16, 6, 1, 42, 17, 46, 12, 13, 26, 27, 51, 2], 'cur_cost': 21122.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:11,033 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 11736.00)
2025-08-05 10:29:11,033 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:11,033 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:11,033 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:11,035 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 10:29:11,035 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,035 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,035 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,036 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,036 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11256.0, 路径长度: 52, 收集中间解: 3
2025-08-05 10:29:11,036 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 14, 21, 13, 12, 26, 27, 25, 46, 11, 50, 10, 51, 24, 3, 5, 4, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 2, 18, 44, 31, 48, 20, 29, 28, 41, 6, 1, 16, 40, 7, 9, 8, 42, 32], 'cur_cost': 11256.0, 'intermediate_solutions': [{'tour': [0, 3, 23, 17, 30, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 4, 14, 5, 24, 2, 43, 15, 49, 19, 22, 29, 20, 16, 45, 18, 44, 40, 7, 9, 8, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 41, 6, 1], 'cur_cost': 11506.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 23, 17, 30, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 4, 14, 5, 24, 45, 7, 40, 44, 18, 2, 16, 20, 29, 22, 19, 49, 15, 43, 9, 8, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 41, 6, 1], 'cur_cost': 10855.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 23, 17, 30, 21, 48, 31, 49, 35, 34, 33, 38, 39, 37, 36, 47, 4, 14, 5, 24, 45, 43, 15, 19, 22, 29, 20, 16, 2, 18, 44, 40, 7, 9, 8, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 41, 6, 1], 'cur_cost': 10384.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:11,036 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 11256.00)
2025-08-05 10:29:11,036 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:11,037 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:11,037 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:11,038 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 10:29:11,038 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,039 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,039 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,039 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,040 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10550.0, 路径长度: 52, 收集中间解: 3
2025-08-05 10:29:11,040 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 17, 8, 12, 26, 27, 25, 46, 13, 51, 10, 50, 11, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 21, 48, 31, 44, 18, 40, 7, 9, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10550.0, 'intermediate_solutions': [{'tour': [27, 23, 47, 3, 37, 14, 39, 49, 4, 17, 7, 35, 15, 44, 42, 8, 34, 33, 26, 43, 45, 19, 24, 46, 12, 13, 38, 0, 5, 36, 2, 48, 31, 22, 41, 1, 21, 29, 30, 28, 16, 11, 18, 50, 51, 25, 32, 9, 20, 6, 40, 10], 'cur_cost': 22718.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [43, 18, 33, 34, 8, 42, 44, 15, 35, 7, 17, 4, 49, 39, 14, 37, 3, 47, 23, 27, 45, 19, 24, 46, 12, 13, 38, 0, 5, 36, 2, 48, 31, 22, 41, 1, 21, 29, 30, 28, 16, 11, 26, 50, 51, 25, 32, 9, 20, 6, 40, 10], 'cur_cost': 22042.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [27, 23, 47, 3, 37, 14, 39, 49, 4, 17, 7, 35, 15, 44, 42, 34, 33, 18, 43, 45, 19, 8, 24, 46, 12, 13, 38, 0, 5, 36, 2, 48, 31, 22, 41, 1, 21, 29, 30, 28, 16, 11, 26, 50, 51, 25, 32, 9, 20, 6, 40, 10], 'cur_cost': 21703.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:11,040 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 10550.00)
2025-08-05 10:29:11,041 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:29:11,041 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:11,041 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:11,041 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 27790.0
2025-08-05 10:29:11,047 - ExploitationExpert - INFO - res_population_num: 1
2025-08-05 10:29:11,047 - ExploitationExpert - INFO - res_population_costs: [7542.0]
2025-08-05 10:29:11,047 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64)]
2025-08-05 10:29:11,048 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:11,048 - ExploitationExpert - INFO - populations: [{'tour': [0, 15, 12, 6, 1, 41, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 13, 42, 9, 8, 7, 40, 18, 44, 2, 16, 32], 'cur_cost': 11757.0}, {'tour': [33, 47, 49, 23, 4, 31, 44, 2, 37, 38, 17, 0, 19, 30, 28, 25, 14, 35, 7, 39, 9, 21, 18, 34, 11, 3, 42, 22, 29, 5, 36, 24, 46, 27, 10, 51, 50, 48, 20, 43, 40, 16, 6, 15, 41, 8, 1, 45, 26, 12, 13, 32], 'cur_cost': 22545.0}, {'tour': [0, 20, 16, 7, 40, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 21, 2, 8, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 41, 6, 1], 'cur_cost': 9573.0}, {'tour': [33, 20, 16, 23, 40, 31, 30, 2, 17, 21, 48, 0, 35, 34, 38, 25, 14, 37, 7, 39, 9, 4, 18, 5, 3, 24, 45, 22, 49, 19, 36, 29, 28, 46, 10, 51, 27, 11, 50, 43, 26, 13, 42, 15, 8, 12, 1, 41, 44, 6, 32, 47], 'cur_cost': 22413.0}, {'tour': [0, 8, 18, 22, 19, 49, 15, 43, 33, 34, 35, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 48, 31, 21, 30, 17, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 7, 40, 44, 32, 41, 6, 1], 'cur_cost': 11457.0}, {'tour': [0, 9, 12, 15, 49, 19, 22, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 28, 29, 20, 16, 2, 18, 44, 40, 7, 8, 42, 32, 50, 11, 27, 26, 25, 46, 13, 51, 10, 41, 6, 1], 'cur_cost': 11736.0}, {'tour': [0, 14, 21, 13, 12, 26, 27, 25, 46, 11, 50, 10, 51, 24, 3, 5, 4, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 2, 18, 44, 31, 48, 20, 29, 28, 41, 6, 1, 16, 40, 7, 9, 8, 42, 32], 'cur_cost': 11256.0}, {'tour': [0, 17, 8, 12, 26, 27, 25, 46, 13, 51, 10, 50, 11, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 21, 48, 31, 44, 18, 40, 7, 9, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10550.0}, {'tour': array([27, 36, 44, 38, 16,  6, 40,  7,  9,  0, 15, 51, 39,  8, 30, 41, 47,
        2, 24, 45, 10, 31, 22, 35, 49, 33, 18,  3,  5, 42, 25,  1, 43, 32,
       23, 14, 26, 19, 11, 50, 37, 34, 21, 28, 17, 29, 13, 48, 20, 46,  4,
       12], dtype=int64), 'cur_cost': 27790.0}, {'tour': [2, 18, 35, 43, 12, 28, 27, 25, 47, 42, 39, 23, 48, 1, 37, 9, 4, 38, 22, 30, 19, 45, 50, 11, 26, 7, 46, 17, 36, 20, 14, 6, 0, 41, 8, 31, 51, 40, 32, 10, 13, 16, 15, 44, 29, 21, 34, 49, 3, 5, 24, 33], 'cur_cost': 27642.0}]
2025-08-05 10:29:11,050 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:11,050 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 341, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 341, 'cache_hits': 0, 'similarity_calculations': 1749, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:11,051 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([27, 36, 44, 38, 16,  6, 40,  7,  9,  0, 15, 51, 39,  8, 30, 41, 47,
        2, 24, 45, 10, 31, 22, 35, 49, 33, 18,  3,  5, 42, 25,  1, 43, 32,
       23, 14, 26, 19, 11, 50, 37, 34, 21, 28, 17, 29, 13, 48, 20, 46,  4,
       12], dtype=int64), 'cur_cost': 27790.0, 'intermediate_solutions': [{'tour': array([31, 51, 48, 50, 29, 34,  8, 19, 35, 39, 43,  4, 22, 23, 45, 27, 32,
       46, 18,  6, 33, 10,  0, 26,  2, 37, 49,  7, 12,  1, 17,  3, 25, 20,
        5, 44, 30, 38,  9, 13, 42, 24, 41, 16, 47, 11, 28, 15, 40, 21, 14,
       36]), 'cur_cost': 33170.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([50, 31, 51, 48, 29, 34,  8, 19, 35, 39, 43,  4, 22, 23, 45, 27, 32,
       46, 18,  6, 33, 10,  0, 26,  2, 37, 49,  7, 12,  1, 17,  3, 25, 20,
        5, 44, 30, 38,  9, 13, 42, 24, 41, 16, 47, 11, 28, 15, 40, 21, 14,
       36]), 'cur_cost': 32952.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([29, 50, 31, 51, 48, 34,  8, 19, 35, 39, 43,  4, 22, 23, 45, 27, 32,
       46, 18,  6, 33, 10,  0, 26,  2, 37, 49,  7, 12,  1, 17,  3, 25, 20,
        5, 44, 30, 38,  9, 13, 42, 24, 41, 16, 47, 11, 28, 15, 40, 21, 14,
       36]), 'cur_cost': 33145.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([48, 50, 31, 51, 29, 34,  8, 19, 35, 39, 43,  4, 22, 23, 45, 27, 32,
       46, 18,  6, 33, 10,  0, 26,  2, 37, 49,  7, 12,  1, 17,  3, 25, 20,
        5, 44, 30, 38,  9, 13, 42, 24, 41, 16, 47, 11, 28, 15, 40, 21, 14,
       36]), 'cur_cost': 32989.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([48, 29, 50, 31, 51, 34,  8, 19, 35, 39, 43,  4, 22, 23, 45, 27, 32,
       46, 18,  6, 33, 10,  0, 26,  2, 37, 49,  7, 12,  1, 17,  3, 25, 20,
        5, 44, 30, 38,  9, 13, 42, 24, 41, 16, 47, 11, 28, 15, 40, 21, 14,
       36]), 'cur_cost': 33055.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:11,052 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 27790.00)
2025-08-05 10:29:11,052 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:29:11,052 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:11,052 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:11,052 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 28006.0
2025-08-05 10:29:11,064 - ExploitationExpert - INFO - res_population_num: 1
2025-08-05 10:29:11,065 - ExploitationExpert - INFO - res_population_costs: [7542.0]
2025-08-05 10:29:11,065 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64)]
2025-08-05 10:29:11,066 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:11,066 - ExploitationExpert - INFO - populations: [{'tour': [0, 15, 12, 6, 1, 41, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 13, 42, 9, 8, 7, 40, 18, 44, 2, 16, 32], 'cur_cost': 11757.0}, {'tour': [33, 47, 49, 23, 4, 31, 44, 2, 37, 38, 17, 0, 19, 30, 28, 25, 14, 35, 7, 39, 9, 21, 18, 34, 11, 3, 42, 22, 29, 5, 36, 24, 46, 27, 10, 51, 50, 48, 20, 43, 40, 16, 6, 15, 41, 8, 1, 45, 26, 12, 13, 32], 'cur_cost': 22545.0}, {'tour': [0, 20, 16, 7, 40, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 21, 2, 8, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 41, 6, 1], 'cur_cost': 9573.0}, {'tour': [33, 20, 16, 23, 40, 31, 30, 2, 17, 21, 48, 0, 35, 34, 38, 25, 14, 37, 7, 39, 9, 4, 18, 5, 3, 24, 45, 22, 49, 19, 36, 29, 28, 46, 10, 51, 27, 11, 50, 43, 26, 13, 42, 15, 8, 12, 1, 41, 44, 6, 32, 47], 'cur_cost': 22413.0}, {'tour': [0, 8, 18, 22, 19, 49, 15, 43, 33, 34, 35, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 48, 31, 21, 30, 17, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 7, 40, 44, 32, 41, 6, 1], 'cur_cost': 11457.0}, {'tour': [0, 9, 12, 15, 49, 19, 22, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 28, 29, 20, 16, 2, 18, 44, 40, 7, 8, 42, 32, 50, 11, 27, 26, 25, 46, 13, 51, 10, 41, 6, 1], 'cur_cost': 11736.0}, {'tour': [0, 14, 21, 13, 12, 26, 27, 25, 46, 11, 50, 10, 51, 24, 3, 5, 4, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 2, 18, 44, 31, 48, 20, 29, 28, 41, 6, 1, 16, 40, 7, 9, 8, 42, 32], 'cur_cost': 11256.0}, {'tour': [0, 17, 8, 12, 26, 27, 25, 46, 13, 51, 10, 50, 11, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 21, 48, 31, 44, 18, 40, 7, 9, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10550.0}, {'tour': array([27, 36, 44, 38, 16,  6, 40,  7,  9,  0, 15, 51, 39,  8, 30, 41, 47,
        2, 24, 45, 10, 31, 22, 35, 49, 33, 18,  3,  5, 42, 25,  1, 43, 32,
       23, 14, 26, 19, 11, 50, 37, 34, 21, 28, 17, 29, 13, 48, 20, 46,  4,
       12], dtype=int64), 'cur_cost': 27790.0}, {'tour': array([47,  2,  0, 40,  1, 17, 26, 46, 25, 13,  8, 30, 10, 27, 28, 34, 35,
       23, 49,  6, 18, 42,  5, 43, 15, 14, 37, 32, 11, 16,  3, 29, 12, 31,
       22,  7, 44, 19, 48, 39, 41,  9, 21, 45,  4, 24, 20, 51, 33, 50, 38,
       36], dtype=int64), 'cur_cost': 28006.0}]
2025-08-05 10:29:11,068 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:11,068 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 342, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 342, 'cache_hits': 0, 'similarity_calculations': 1752, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:11,069 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([47,  2,  0, 40,  1, 17, 26, 46, 25, 13,  8, 30, 10, 27, 28, 34, 35,
       23, 49,  6, 18, 42,  5, 43, 15, 14, 37, 32, 11, 16,  3, 29, 12, 31,
       22,  7, 44, 19, 48, 39, 41,  9, 21, 45,  4, 24, 20, 51, 33, 50, 38,
       36], dtype=int64), 'cur_cost': 28006.0, 'intermediate_solutions': [{'tour': array([35, 18,  2, 43, 12, 28, 27, 25, 47, 42, 39, 23, 48,  1, 37,  9,  4,
       38, 22, 30, 19, 45, 50, 11, 26,  7, 46, 17, 36, 20, 14,  6,  0, 41,
        8, 31, 51, 40, 32, 10, 13, 16, 15, 44, 29, 21, 34, 49,  3,  5, 24,
       33]), 'cur_cost': 27605.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([43, 35, 18,  2, 12, 28, 27, 25, 47, 42, 39, 23, 48,  1, 37,  9,  4,
       38, 22, 30, 19, 45, 50, 11, 26,  7, 46, 17, 36, 20, 14,  6,  0, 41,
        8, 31, 51, 40, 32, 10, 13, 16, 15, 44, 29, 21, 34, 49,  3,  5, 24,
       33]), 'cur_cost': 27754.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([12, 43, 35, 18,  2, 28, 27, 25, 47, 42, 39, 23, 48,  1, 37,  9,  4,
       38, 22, 30, 19, 45, 50, 11, 26,  7, 46, 17, 36, 20, 14,  6,  0, 41,
        8, 31, 51, 40, 32, 10, 13, 16, 15, 44, 29, 21, 34, 49,  3,  5, 24,
       33]), 'cur_cost': 27948.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2, 43, 35, 18, 12, 28, 27, 25, 47, 42, 39, 23, 48,  1, 37,  9,  4,
       38, 22, 30, 19, 45, 50, 11, 26,  7, 46, 17, 36, 20, 14,  6,  0, 41,
        8, 31, 51, 40, 32, 10, 13, 16, 15, 44, 29, 21, 34, 49,  3,  5, 24,
       33]), 'cur_cost': 28216.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2, 12, 43, 35, 18, 28, 27, 25, 47, 42, 39, 23, 48,  1, 37,  9,  4,
       38, 22, 30, 19, 45, 50, 11, 26,  7, 46, 17, 36, 20, 14,  6,  0, 41,
        8, 31, 51, 40, 32, 10, 13, 16, 15, 44, 29, 21, 34, 49,  3,  5, 24,
       33]), 'cur_cost': 28589.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:11,070 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 28006.00)
2025-08-05 10:29:11,070 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:11,070 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:11,072 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 12, 6, 1, 41, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 13, 42, 9, 8, 7, 40, 18, 44, 2, 16, 32], 'cur_cost': 11757.0, 'intermediate_solutions': [{'tour': [0, 7, 20, 6, 1, 41, 29, 22, 19, 49, 15, 43, 33, 34, 35, 38, 39, 37, 36, 47, 50, 4, 14, 5, 3, 24, 45, 48, 31, 21, 30, 17, 2, 18, 44, 40, 8, 9, 42, 32, 23, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 16], 'cur_cost': 11396.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 20, 6, 1, 41, 29, 22, 4, 23, 47, 36, 37, 39, 38, 35, 34, 33, 43, 15, 49, 19, 14, 5, 3, 24, 45, 48, 31, 21, 30, 17, 2, 18, 44, 40, 8, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 16], 'cur_cost': 10818.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 20, 6, 1, 41, 29, 22, 19, 49, 15, 43, 33, 34, 35, 38, 39, 37, 47, 23, 4, 14, 5, 3, 24, 45, 48, 31, 21, 30, 17, 2, 18, 44, 40, 8, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 36, 51, 10, 28, 16], 'cur_cost': 11709.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [33, 47, 49, 23, 4, 31, 44, 2, 37, 38, 17, 0, 19, 30, 28, 25, 14, 35, 7, 39, 9, 21, 18, 34, 11, 3, 42, 22, 29, 5, 36, 24, 46, 27, 10, 51, 50, 48, 20, 43, 40, 16, 6, 15, 41, 8, 1, 45, 26, 12, 13, 32], 'cur_cost': 22545.0, 'intermediate_solutions': [{'tour': [27, 3, 45, 4, 23, 18, 0, 20, 38, 49, 39, 35, 48, 34, 30, 19, 2, 44, 9, 42, 37, 43, 24, 25, 36, 21, 22, 31, 16, 47, 15, 29, 6, 17, 33, 28, 5, 46, 8, 50, 14, 7, 11, 26, 12, 13, 51, 32, 41, 1, 40, 10], 'cur_cost': 21935.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [27, 3, 45, 4, 23, 18, 0, 20, 38, 49, 39, 35, 48, 34, 30, 19, 2, 47, 16, 31, 22, 21, 36, 25, 24, 43, 37, 42, 9, 44, 15, 29, 6, 17, 33, 28, 5, 32, 8, 50, 14, 7, 11, 26, 12, 13, 51, 46, 41, 1, 40, 10], 'cur_cost': 20868.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [27, 3, 45, 4, 23, 18, 0, 38, 49, 39, 35, 48, 34, 30, 19, 2, 44, 9, 42, 37, 43, 24, 25, 36, 21, 22, 31, 16, 47, 15, 29, 6, 17, 33, 28, 5, 32, 8, 50, 14, 7, 11, 26, 12, 13, 51, 46, 41, 1, 40, 20, 10], 'cur_cost': 20454.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 16, 7, 40, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 21, 2, 8, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 41, 6, 1], 'cur_cost': 9573.0, 'intermediate_solutions': [{'tour': [0, 19, 18, 12, 26, 27, 25, 46, 13, 51, 10, 50, 11, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 22, 30, 17, 21, 48, 31, 44, 40, 7, 9, 8, 42, 20, 2, 16, 32, 29, 28, 41, 6, 1], 'cur_cost': 12203.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 18, 12, 26, 27, 25, 46, 13, 51, 10, 50, 11, 24, 3, 8, 9, 7, 40, 44, 31, 48, 21, 17, 30, 22, 49, 15, 45, 43, 33, 34, 35, 38, 36, 39, 37, 47, 23, 14, 4, 5, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 11062.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 18, 12, 26, 27, 25, 46, 13, 51, 10, 50, 11, 24, 3, 5, 4, 14, 23, 45, 47, 37, 39, 36, 38, 35, 34, 33, 43, 15, 49, 22, 30, 17, 21, 48, 31, 44, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10745.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [33, 20, 16, 23, 40, 31, 30, 2, 17, 21, 48, 0, 35, 34, 38, 25, 14, 37, 7, 39, 9, 4, 18, 5, 3, 24, 45, 22, 49, 19, 36, 29, 28, 46, 10, 51, 27, 11, 50, 43, 26, 13, 42, 15, 8, 12, 1, 41, 44, 6, 32, 47], 'cur_cost': 22413.0, 'intermediate_solutions': [{'tour': [0, 14, 15, 19, 49, 22, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 51, 43, 28, 29, 20, 16, 2, 18, 44, 40, 7, 9, 8, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 45, 10, 41, 6, 1], 'cur_cost': 12613.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 15, 19, 49, 22, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 43, 18, 2, 16, 20, 29, 28, 44, 40, 7, 9, 8, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 41, 6, 1], 'cur_cost': 10760.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 15, 19, 49, 22, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 43, 28, 29, 20, 16, 2, 18, 44, 40, 7, 9, 8, 42, 32, 50, 11, 27, 51, 26, 25, 46, 12, 13, 10, 41, 6, 1], 'cur_cost': 10825.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 18, 22, 19, 49, 15, 43, 33, 34, 35, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 48, 31, 21, 30, 17, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 7, 40, 44, 32, 41, 6, 1], 'cur_cost': 11457.0, 'intermediate_solutions': [{'tour': [0, 19, 20, 12, 26, 27, 25, 46, 13, 51, 10, 50, 11, 24, 3, 5, 4, 35, 23, 47, 37, 39, 36, 38, 14, 34, 33, 43, 45, 15, 49, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 41, 6, 1, 29, 28], 'cur_cost': 10409.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 20, 12, 26, 27, 25, 46, 41, 16, 2, 32, 42, 8, 9, 7, 40, 18, 44, 31, 48, 21, 17, 30, 22, 49, 15, 45, 43, 33, 34, 35, 38, 36, 39, 37, 47, 23, 14, 4, 5, 3, 24, 11, 50, 10, 51, 13, 6, 1, 29, 28], 'cur_cost': 12091.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 20, 12, 26, 27, 25, 46, 13, 51, 10, 50, 11, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 22, 30, 17, 21, 48, 31, 44, 18, 40, 9, 7, 8, 42, 32, 2, 16, 41, 6, 1, 29, 28], 'cur_cost': 10197.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 12, 15, 49, 19, 22, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 28, 29, 20, 16, 2, 18, 44, 40, 7, 8, 42, 32, 50, 11, 27, 26, 25, 46, 13, 51, 10, 41, 6, 1], 'cur_cost': 11736.0, 'intermediate_solutions': [{'tour': [22, 44, 18, 31, 37, 0, 20, 28, 30, 15, 45, 19, 34, 33, 43, 29, 38, 49, 41, 12, 39, 21, 3, 48, 47, 7, 8, 5, 24, 23, 50, 10, 25, 4, 36, 11, 32, 40, 9, 35, 16, 6, 1, 42, 17, 46, 14, 13, 26, 27, 51, 2], 'cur_cost': 22857.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 44, 18, 31, 37, 0, 20, 28, 30, 15, 45, 19, 34, 33, 43, 29, 38, 49, 41, 14, 39, 21, 3, 48, 47, 7, 8, 5, 24, 23, 50, 10, 25, 4, 36, 11, 32, 40, 9, 35, 16, 6, 1, 42, 17, 46, 12, 13, 27, 26, 51, 2], 'cur_cost': 20494.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 44, 18, 31, 37, 0, 20, 28, 30, 15, 45, 19, 34, 33, 43, 29, 38, 49, 14, 39, 21, 3, 48, 47, 7, 8, 5, 24, 41, 23, 50, 10, 25, 4, 36, 11, 32, 40, 9, 35, 16, 6, 1, 42, 17, 46, 12, 13, 26, 27, 51, 2], 'cur_cost': 21122.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 21, 13, 12, 26, 27, 25, 46, 11, 50, 10, 51, 24, 3, 5, 4, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 2, 18, 44, 31, 48, 20, 29, 28, 41, 6, 1, 16, 40, 7, 9, 8, 42, 32], 'cur_cost': 11256.0, 'intermediate_solutions': [{'tour': [0, 3, 23, 17, 30, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 4, 14, 5, 24, 2, 43, 15, 49, 19, 22, 29, 20, 16, 45, 18, 44, 40, 7, 9, 8, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 41, 6, 1], 'cur_cost': 11506.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 23, 17, 30, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 4, 14, 5, 24, 45, 7, 40, 44, 18, 2, 16, 20, 29, 22, 19, 49, 15, 43, 9, 8, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 41, 6, 1], 'cur_cost': 10855.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 23, 17, 30, 21, 48, 31, 49, 35, 34, 33, 38, 39, 37, 36, 47, 4, 14, 5, 24, 45, 43, 15, 19, 22, 29, 20, 16, 2, 18, 44, 40, 7, 9, 8, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 41, 6, 1], 'cur_cost': 10384.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 8, 12, 26, 27, 25, 46, 13, 51, 10, 50, 11, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 21, 48, 31, 44, 18, 40, 7, 9, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10550.0, 'intermediate_solutions': [{'tour': [27, 23, 47, 3, 37, 14, 39, 49, 4, 17, 7, 35, 15, 44, 42, 8, 34, 33, 26, 43, 45, 19, 24, 46, 12, 13, 38, 0, 5, 36, 2, 48, 31, 22, 41, 1, 21, 29, 30, 28, 16, 11, 18, 50, 51, 25, 32, 9, 20, 6, 40, 10], 'cur_cost': 22718.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [43, 18, 33, 34, 8, 42, 44, 15, 35, 7, 17, 4, 49, 39, 14, 37, 3, 47, 23, 27, 45, 19, 24, 46, 12, 13, 38, 0, 5, 36, 2, 48, 31, 22, 41, 1, 21, 29, 30, 28, 16, 11, 26, 50, 51, 25, 32, 9, 20, 6, 40, 10], 'cur_cost': 22042.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [27, 23, 47, 3, 37, 14, 39, 49, 4, 17, 7, 35, 15, 44, 42, 34, 33, 18, 43, 45, 19, 8, 24, 46, 12, 13, 38, 0, 5, 36, 2, 48, 31, 22, 41, 1, 21, 29, 30, 28, 16, 11, 26, 50, 51, 25, 32, 9, 20, 6, 40, 10], 'cur_cost': 21703.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([27, 36, 44, 38, 16,  6, 40,  7,  9,  0, 15, 51, 39,  8, 30, 41, 47,
        2, 24, 45, 10, 31, 22, 35, 49, 33, 18,  3,  5, 42, 25,  1, 43, 32,
       23, 14, 26, 19, 11, 50, 37, 34, 21, 28, 17, 29, 13, 48, 20, 46,  4,
       12], dtype=int64), 'cur_cost': 27790.0, 'intermediate_solutions': [{'tour': array([31, 51, 48, 50, 29, 34,  8, 19, 35, 39, 43,  4, 22, 23, 45, 27, 32,
       46, 18,  6, 33, 10,  0, 26,  2, 37, 49,  7, 12,  1, 17,  3, 25, 20,
        5, 44, 30, 38,  9, 13, 42, 24, 41, 16, 47, 11, 28, 15, 40, 21, 14,
       36]), 'cur_cost': 33170.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([50, 31, 51, 48, 29, 34,  8, 19, 35, 39, 43,  4, 22, 23, 45, 27, 32,
       46, 18,  6, 33, 10,  0, 26,  2, 37, 49,  7, 12,  1, 17,  3, 25, 20,
        5, 44, 30, 38,  9, 13, 42, 24, 41, 16, 47, 11, 28, 15, 40, 21, 14,
       36]), 'cur_cost': 32952.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([29, 50, 31, 51, 48, 34,  8, 19, 35, 39, 43,  4, 22, 23, 45, 27, 32,
       46, 18,  6, 33, 10,  0, 26,  2, 37, 49,  7, 12,  1, 17,  3, 25, 20,
        5, 44, 30, 38,  9, 13, 42, 24, 41, 16, 47, 11, 28, 15, 40, 21, 14,
       36]), 'cur_cost': 33145.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([48, 50, 31, 51, 29, 34,  8, 19, 35, 39, 43,  4, 22, 23, 45, 27, 32,
       46, 18,  6, 33, 10,  0, 26,  2, 37, 49,  7, 12,  1, 17,  3, 25, 20,
        5, 44, 30, 38,  9, 13, 42, 24, 41, 16, 47, 11, 28, 15, 40, 21, 14,
       36]), 'cur_cost': 32989.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([48, 29, 50, 31, 51, 34,  8, 19, 35, 39, 43,  4, 22, 23, 45, 27, 32,
       46, 18,  6, 33, 10,  0, 26,  2, 37, 49,  7, 12,  1, 17,  3, 25, 20,
        5, 44, 30, 38,  9, 13, 42, 24, 41, 16, 47, 11, 28, 15, 40, 21, 14,
       36]), 'cur_cost': 33055.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([47,  2,  0, 40,  1, 17, 26, 46, 25, 13,  8, 30, 10, 27, 28, 34, 35,
       23, 49,  6, 18, 42,  5, 43, 15, 14, 37, 32, 11, 16,  3, 29, 12, 31,
       22,  7, 44, 19, 48, 39, 41,  9, 21, 45,  4, 24, 20, 51, 33, 50, 38,
       36], dtype=int64), 'cur_cost': 28006.0, 'intermediate_solutions': [{'tour': array([35, 18,  2, 43, 12, 28, 27, 25, 47, 42, 39, 23, 48,  1, 37,  9,  4,
       38, 22, 30, 19, 45, 50, 11, 26,  7, 46, 17, 36, 20, 14,  6,  0, 41,
        8, 31, 51, 40, 32, 10, 13, 16, 15, 44, 29, 21, 34, 49,  3,  5, 24,
       33]), 'cur_cost': 27605.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([43, 35, 18,  2, 12, 28, 27, 25, 47, 42, 39, 23, 48,  1, 37,  9,  4,
       38, 22, 30, 19, 45, 50, 11, 26,  7, 46, 17, 36, 20, 14,  6,  0, 41,
        8, 31, 51, 40, 32, 10, 13, 16, 15, 44, 29, 21, 34, 49,  3,  5, 24,
       33]), 'cur_cost': 27754.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([12, 43, 35, 18,  2, 28, 27, 25, 47, 42, 39, 23, 48,  1, 37,  9,  4,
       38, 22, 30, 19, 45, 50, 11, 26,  7, 46, 17, 36, 20, 14,  6,  0, 41,
        8, 31, 51, 40, 32, 10, 13, 16, 15, 44, 29, 21, 34, 49,  3,  5, 24,
       33]), 'cur_cost': 27948.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2, 43, 35, 18, 12, 28, 27, 25, 47, 42, 39, 23, 48,  1, 37,  9,  4,
       38, 22, 30, 19, 45, 50, 11, 26,  7, 46, 17, 36, 20, 14,  6,  0, 41,
        8, 31, 51, 40, 32, 10, 13, 16, 15, 44, 29, 21, 34, 49,  3,  5, 24,
       33]), 'cur_cost': 28216.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2, 12, 43, 35, 18, 28, 27, 25, 47, 42, 39, 23, 48,  1, 37,  9,  4,
       38, 22, 30, 19, 45, 50, 11, 26,  7, 46, 17, 36, 20, 14,  6,  0, 41,
        8, 31, 51, 40, 32, 10, 13, 16, 15, 44, 29, 21, 34, 49,  3,  5, 24,
       33]), 'cur_cost': 28589.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:29:11,072 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:11,072 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:11,076 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9573.000, 多样性=0.927
2025-08-05 10:29:11,076 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 10:29:11,076 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 10:29:11,076 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:11,076 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.07579641579379091, 'best_improvement': 0.03468790965009579}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.026044005388414473}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.014953749952295403, 'recent_improvements': [0.00736246733382087, -0.03536134817233149, -0.022545032570769934], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 1, 'new_count': 1, 'count_change': 0, 'old_best_cost': 7542.0, 'new_best_cost': 7542.0, 'quality_improvement': 0.0, 'old_diversity': 0.0, 'new_diversity': 0.0, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配']}
2025-08-05 10:29:11,076 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 10:29:11,077 - __main__ - INFO - berlin52 开始进化第 3 代
2025-08-05 10:29:11,077 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 10:29:11,077 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:11,077 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9573.000, 多样性=0.927
2025-08-05 10:29:11,078 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:11,080 - PathExpert - INFO - 路径结构分析完成: 公共边数量=18, 路径相似性=0.927
2025-08-05 10:29:11,080 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:11,081 - EliteExpert - INFO - 精英解分析完成: 精英解数量=1, 多样性=0.000
2025-08-05 10:29:11,083 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 10:29:11,083 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:11,083 - LandscapeExpert - INFO - 添加精英解数据: 1个精英解
2025-08-05 10:29:11,084 - LandscapeExpert - INFO - 数据提取成功: 11个路径, 11个适应度值
2025-08-05 10:29:11,109 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.182, 适应度梯度: -2211.182, 聚类评分: 0.000, 覆盖率: 0.151, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:11,110 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 10:29:11,110 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:11,110 - visualization.landscape_visualizer - INFO - 设置当前实例名: berlin52
2025-08-05 10:29:11,114 - visualization.landscape_visualizer - INFO - 插值约束: 60 个点被约束到最小值 7542.00
2025-08-05 10:29:11,116 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=5.9%, 梯度: 827.33 → 778.90
2025-08-05 10:29:11,224 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_berlin52\landscape_berlin52_iter_133_20250805_102911.html
2025-08-05 10:29:11,271 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_berlin52\dashboard_berlin52_iter_133_20250805_102911.html
2025-08-05 10:29:11,271 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 133
2025-08-05 10:29:11,271 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 10:29:11,271 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1885秒
2025-08-05 10:29:11,271 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.18181818181818182, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -2211.181818181818, 'local_optima_density': 0.18181818181818182, 'gradient_variance': 54896792.48330579, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1509, 'fitness_entropy': 0.9905114616736054, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -2211.182)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.151)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360951.1108007, 'performance_metrics': {}}}
2025-08-05 10:29:11,272 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:11,272 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:11,272 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:11,272 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:11,272 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:11,273 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 10:29:11,273 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:11,273 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:11,273 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:11,273 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:11,273 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:11,274 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:11,274 - experts.management.collaboration_manager - INFO - 识别精英个体: {2, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:11,274 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:11,275 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:11,275 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:11,278 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 52
2025-08-05 10:29:11,279 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,279 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,279 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,279 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,279 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20366.0, 路径长度: 52, 收集中间解: 3
2025-08-05 10:29:11,280 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [28, 19, 4, 21, 36, 22, 15, 5, 34, 30, 23, 3, 48, 38, 24, 25, 51, 42, 8, 44, 35, 0, 31, 43, 33, 37, 49, 17, 2, 9, 47, 11, 10, 46, 27, 50, 12, 29, 41, 18, 7, 16, 39, 14, 45, 20, 1, 6, 40, 32, 26, 13], 'cur_cost': 20366.0, 'intermediate_solutions': [{'tour': [0, 15, 12, 6, 1, 41, 20, 30, 17, 21, 48, 31, 35, 34, 29, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 49, 19, 22, 33, 28, 46, 25, 26, 27, 11, 50, 10, 51, 13, 42, 9, 8, 7, 40, 18, 44, 2, 16, 32], 'cur_cost': 12850.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 12, 6, 1, 41, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 7, 8, 9, 42, 13, 51, 40, 18, 44, 2, 16, 32], 'cur_cost': 13892.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 12, 6, 1, 41, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 49, 19, 22, 29, 28, 46, 8, 25, 26, 27, 11, 50, 10, 51, 13, 42, 9, 7, 40, 18, 44, 2, 16, 32], 'cur_cost': 13868.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:11,280 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 20366.00)
2025-08-05 10:29:11,280 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 10:29:11,280 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:11,280 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:11,280 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 31643.0
2025-08-05 10:29:11,286 - ExploitationExpert - INFO - res_population_num: 1
2025-08-05 10:29:11,286 - ExploitationExpert - INFO - res_population_costs: [7542.0]
2025-08-05 10:29:11,287 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64)]
2025-08-05 10:29:11,287 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:11,287 - ExploitationExpert - INFO - populations: [{'tour': [28, 19, 4, 21, 36, 22, 15, 5, 34, 30, 23, 3, 48, 38, 24, 25, 51, 42, 8, 44, 35, 0, 31, 43, 33, 37, 49, 17, 2, 9, 47, 11, 10, 46, 27, 50, 12, 29, 41, 18, 7, 16, 39, 14, 45, 20, 1, 6, 40, 32, 26, 13], 'cur_cost': 20366.0}, {'tour': array([10,  0,  3, 51,  1, 22, 28, 13, 23, 43,  2, 38, 47, 39, 29, 48, 41,
       31, 20,  7,  4, 36, 11, 34, 25, 18, 45, 12,  8, 15, 27, 17, 50, 49,
       24, 37, 32,  9, 19, 40, 14, 21, 33, 16, 46, 42, 44,  6, 30, 26,  5,
       35], dtype=int64), 'cur_cost': 31643.0}, {'tour': [0, 20, 16, 7, 40, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 21, 2, 8, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 41, 6, 1], 'cur_cost': 9573.0}, {'tour': [33, 20, 16, 23, 40, 31, 30, 2, 17, 21, 48, 0, 35, 34, 38, 25, 14, 37, 7, 39, 9, 4, 18, 5, 3, 24, 45, 22, 49, 19, 36, 29, 28, 46, 10, 51, 27, 11, 50, 43, 26, 13, 42, 15, 8, 12, 1, 41, 44, 6, 32, 47], 'cur_cost': 22413.0}, {'tour': [0, 8, 18, 22, 19, 49, 15, 43, 33, 34, 35, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 48, 31, 21, 30, 17, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 7, 40, 44, 32, 41, 6, 1], 'cur_cost': 11457.0}, {'tour': [0, 9, 12, 15, 49, 19, 22, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 28, 29, 20, 16, 2, 18, 44, 40, 7, 8, 42, 32, 50, 11, 27, 26, 25, 46, 13, 51, 10, 41, 6, 1], 'cur_cost': 11736.0}, {'tour': [0, 14, 21, 13, 12, 26, 27, 25, 46, 11, 50, 10, 51, 24, 3, 5, 4, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 2, 18, 44, 31, 48, 20, 29, 28, 41, 6, 1, 16, 40, 7, 9, 8, 42, 32], 'cur_cost': 11256.0}, {'tour': [0, 17, 8, 12, 26, 27, 25, 46, 13, 51, 10, 50, 11, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 21, 48, 31, 44, 18, 40, 7, 9, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10550.0}, {'tour': [27, 36, 44, 38, 16, 6, 40, 7, 9, 0, 15, 51, 39, 8, 30, 41, 47, 2, 24, 45, 10, 31, 22, 35, 49, 33, 18, 3, 5, 42, 25, 1, 43, 32, 23, 14, 26, 19, 11, 50, 37, 34, 21, 28, 17, 29, 13, 48, 20, 46, 4, 12], 'cur_cost': 27790.0}, {'tour': [47, 2, 0, 40, 1, 17, 26, 46, 25, 13, 8, 30, 10, 27, 28, 34, 35, 23, 49, 6, 18, 42, 5, 43, 15, 14, 37, 32, 11, 16, 3, 29, 12, 31, 22, 7, 44, 19, 48, 39, 41, 9, 21, 45, 4, 24, 20, 51, 33, 50, 38, 36], 'cur_cost': 28006.0}]
2025-08-05 10:29:11,288 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:11,288 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 343, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 343, 'cache_hits': 0, 'similarity_calculations': 1756, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:11,289 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([10,  0,  3, 51,  1, 22, 28, 13, 23, 43,  2, 38, 47, 39, 29, 48, 41,
       31, 20,  7,  4, 36, 11, 34, 25, 18, 45, 12,  8, 15, 27, 17, 50, 49,
       24, 37, 32,  9, 19, 40, 14, 21, 33, 16, 46, 42, 44,  6, 30, 26,  5,
       35], dtype=int64), 'cur_cost': 31643.0, 'intermediate_solutions': [{'tour': array([49, 47, 33, 23,  4, 31, 44,  2, 37, 38, 17,  0, 19, 30, 28, 25, 14,
       35,  7, 39,  9, 21, 18, 34, 11,  3, 42, 22, 29,  5, 36, 24, 46, 27,
       10, 51, 50, 48, 20, 43, 40, 16,  6, 15, 41,  8,  1, 45, 26, 12, 13,
       32]), 'cur_cost': 22569.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([23, 49, 47, 33,  4, 31, 44,  2, 37, 38, 17,  0, 19, 30, 28, 25, 14,
       35,  7, 39,  9, 21, 18, 34, 11,  3, 42, 22, 29,  5, 36, 24, 46, 27,
       10, 51, 50, 48, 20, 43, 40, 16,  6, 15, 41,  8,  1, 45, 26, 12, 13,
       32]), 'cur_cost': 22563.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 4, 23, 49, 47, 33, 31, 44,  2, 37, 38, 17,  0, 19, 30, 28, 25, 14,
       35,  7, 39,  9, 21, 18, 34, 11,  3, 42, 22, 29,  5, 36, 24, 46, 27,
       10, 51, 50, 48, 20, 43, 40, 16,  6, 15, 41,  8,  1, 45, 26, 12, 13,
       32]), 'cur_cost': 22282.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([33, 23, 49, 47,  4, 31, 44,  2, 37, 38, 17,  0, 19, 30, 28, 25, 14,
       35,  7, 39,  9, 21, 18, 34, 11,  3, 42, 22, 29,  5, 36, 24, 46, 27,
       10, 51, 50, 48, 20, 43, 40, 16,  6, 15, 41,  8,  1, 45, 26, 12, 13,
       32]), 'cur_cost': 22569.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([33,  4, 23, 49, 47, 31, 44,  2, 37, 38, 17,  0, 19, 30, 28, 25, 14,
       35,  7, 39,  9, 21, 18, 34, 11,  3, 42, 22, 29,  5, 36, 24, 46, 27,
       10, 51, 50, 48, 20, 43, 40, 16,  6, 15, 41,  8,  1, 45, 26, 12, 13,
       32]), 'cur_cost': 22566.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:11,289 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 31643.00)
2025-08-05 10:29:11,289 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:11,290 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:11,290 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:11,292 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 10:29:11,292 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,293 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,293 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,293 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,293 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10587.0, 路径长度: 52, 收集中间解: 3
2025-08-05 10:29:11,293 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 22, 21, 6, 1, 41, 20, 30, 17, 2, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 16, 32], 'cur_cost': 10587.0, 'intermediate_solutions': [{'tour': [0, 20, 16, 7, 40, 27, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 21, 2, 8, 9, 42, 32, 50, 11, 18, 26, 25, 46, 12, 13, 51, 10, 28, 29, 41, 6, 1], 'cur_cost': 12633.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 16, 7, 40, 18, 44, 31, 48, 35, 34, 33, 38, 39, 51, 13, 12, 46, 25, 26, 27, 11, 50, 32, 42, 9, 8, 2, 21, 17, 30, 22, 19, 49, 15, 43, 45, 24, 3, 5, 14, 4, 23, 47, 36, 37, 10, 28, 29, 41, 6, 1], 'cur_cost': 11009.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 16, 7, 40, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 15, 49, 19, 22, 30, 17, 21, 2, 8, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 41, 6, 43, 1], 'cur_cost': 10893.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:11,293 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 10587.00)
2025-08-05 10:29:11,294 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:11,294 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:11,294 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:11,298 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 52
2025-08-05 10:29:11,298 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,298 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,299 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,299 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,299 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21370.0, 路径长度: 52, 收集中间解: 3
2025-08-05 10:29:11,299 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [43, 21, 31, 47, 5, 48, 38, 17, 35, 4, 19, 34, 42, 8, 23, 0, 45, 27, 46, 15, 49, 20, 44, 18, 24, 50, 10, 32, 2, 14, 9, 16, 41, 39, 3, 7, 30, 33, 40, 29, 1, 6, 37, 28, 25, 11, 36, 22, 12, 26, 13, 51], 'cur_cost': 21370.0, 'intermediate_solutions': [{'tour': [33, 20, 16, 23, 40, 31, 30, 2, 17, 21, 48, 0, 35, 46, 38, 25, 14, 37, 7, 39, 9, 4, 18, 5, 3, 24, 45, 22, 49, 19, 36, 29, 28, 34, 10, 51, 27, 11, 50, 43, 26, 13, 42, 15, 8, 12, 1, 41, 44, 6, 32, 47], 'cur_cost': 23909.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [33, 20, 16, 23, 40, 31, 30, 2, 17, 21, 48, 0, 35, 34, 38, 25, 14, 37, 7, 39, 9, 4, 18, 5, 3, 24, 45, 22, 15, 42, 13, 26, 43, 50, 11, 27, 51, 10, 46, 28, 29, 36, 19, 49, 8, 12, 1, 41, 44, 6, 32, 47], 'cur_cost': 22532.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [20, 16, 23, 40, 31, 30, 2, 17, 21, 48, 0, 35, 34, 38, 25, 14, 37, 7, 39, 9, 4, 18, 5, 3, 24, 45, 22, 49, 19, 36, 29, 28, 46, 10, 51, 27, 11, 50, 43, 26, 13, 42, 15, 33, 8, 12, 1, 41, 44, 6, 32, 47], 'cur_cost': 22413.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:11,299 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 21370.00)
2025-08-05 10:29:11,299 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:29:11,300 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:29:11,300 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:11,301 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 10:29:11,301 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,302 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,302 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,302 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,302 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10958.0, 路径长度: 52, 收集中间解: 3
2025-08-05 10:29:11,302 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 5, 21, 7, 40, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 32, 41, 6, 1], 'cur_cost': 10958.0, 'intermediate_solutions': [{'tour': [0, 8, 18, 22, 19, 49, 15, 43, 33, 34, 12, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 48, 31, 21, 30, 17, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 35, 13, 42, 9, 7, 40, 44, 32, 41, 6, 1], 'cur_cost': 14806.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 18, 22, 19, 49, 15, 43, 33, 34, 35, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 48, 31, 21, 30, 20, 16, 2, 17, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 7, 40, 44, 32, 41, 6, 1], 'cur_cost': 11670.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 18, 22, 19, 49, 15, 43, 33, 35, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 48, 31, 21, 30, 17, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 34, 50, 10, 51, 12, 13, 42, 9, 7, 40, 44, 32, 41, 6, 1], 'cur_cost': 12470.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:11,303 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 10958.00)
2025-08-05 10:29:11,303 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:29:11,303 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:29:11,303 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:11,305 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 10:29:11,305 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,305 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,305 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,305 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,306 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9361.0, 路径长度: 52, 收集中间解: 3
2025-08-05 10:29:11,306 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 21, 14, 17, 30, 20, 22, 19, 49, 15, 43, 33, 34, 35, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 41, 6, 1, 16, 2], 'cur_cost': 9361.0, 'intermediate_solutions': [{'tour': [0, 9, 12, 15, 49, 19, 22, 30, 17, 21, 48, 31, 35, 34, 33, 6, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 28, 29, 20, 16, 2, 18, 44, 40, 7, 8, 42, 32, 50, 11, 27, 26, 25, 46, 13, 51, 10, 41, 38, 1], 'cur_cost': 14677.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 12, 15, 38, 33, 34, 35, 31, 48, 21, 17, 30, 22, 19, 49, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 28, 29, 20, 16, 2, 18, 44, 40, 7, 8, 42, 32, 50, 11, 27, 26, 25, 46, 13, 51, 10, 41, 6, 1], 'cur_cost': 12162.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 12, 15, 49, 19, 22, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 45, 43, 28, 29, 20, 16, 24, 2, 18, 44, 40, 7, 8, 42, 32, 50, 11, 27, 26, 25, 46, 13, 51, 10, 41, 6, 1], 'cur_cost': 12955.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:11,306 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 9361.00)
2025-08-05 10:29:11,306 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:11,306 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:11,306 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:11,308 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 10:29:11,308 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,308 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,309 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,309 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,309 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12942.0, 路径长度: 52, 收集中间解: 3
2025-08-05 10:29:11,309 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 12, 6, 10, 50, 11, 27, 26, 25, 46, 13, 51, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 1], 'cur_cost': 12942.0, 'intermediate_solutions': [{'tour': [0, 14, 21, 36, 12, 26, 27, 25, 46, 11, 50, 10, 51, 24, 3, 5, 4, 23, 47, 37, 39, 13, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 2, 18, 44, 31, 48, 20, 29, 28, 41, 6, 1, 16, 40, 7, 9, 8, 42, 32], 'cur_cost': 12876.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 25, 27, 26, 12, 13, 21, 14, 46, 11, 50, 10, 51, 24, 3, 5, 4, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 2, 18, 44, 31, 48, 20, 29, 28, 41, 6, 1, 16, 40, 7, 9, 8, 42, 32], 'cur_cost': 12196.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 21, 13, 12, 26, 27, 25, 46, 11, 50, 10, 51, 24, 3, 5, 4, 23, 47, 37, 39, 36, 38, 18, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 2, 44, 31, 48, 20, 29, 28, 41, 6, 1, 16, 40, 7, 9, 8, 42, 32], 'cur_cost': 11788.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:11,309 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 12942.00)
2025-08-05 10:29:11,309 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:11,309 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:11,309 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:11,311 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 10:29:11,311 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,311 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,312 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,312 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,312 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10584.0, 路径长度: 52, 收集中间解: 3
2025-08-05 10:29:11,312 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 19, 8, 22, 49, 15, 43, 33, 34, 35, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 48, 31, 21, 30, 17, 2, 18, 44, 40, 7, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 20, 16, 41, 6, 1], 'cur_cost': 10584.0, 'intermediate_solutions': [{'tour': [0, 17, 8, 12, 26, 27, 6, 46, 13, 51, 10, 50, 11, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 21, 48, 31, 44, 18, 40, 7, 9, 42, 32, 2, 16, 20, 29, 28, 41, 25, 1], 'cur_cost': 14790.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 8, 12, 26, 27, 25, 46, 13, 51, 10, 50, 11, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 21, 44, 31, 48, 18, 40, 7, 9, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10881.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 17, 8, 12, 26, 27, 25, 46, 13, 51, 10, 50, 11, 24, 3, 5, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 21, 48, 31, 44, 18, 40, 7, 9, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 11100.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:11,312 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 10584.00)
2025-08-05 10:29:11,312 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:29:11,312 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:11,313 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:11,314 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 30523.0
2025-08-05 10:29:11,320 - ExploitationExpert - INFO - res_population_num: 1
2025-08-05 10:29:11,320 - ExploitationExpert - INFO - res_population_costs: [7542.0]
2025-08-05 10:29:11,320 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64)]
2025-08-05 10:29:11,321 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:11,321 - ExploitationExpert - INFO - populations: [{'tour': [28, 19, 4, 21, 36, 22, 15, 5, 34, 30, 23, 3, 48, 38, 24, 25, 51, 42, 8, 44, 35, 0, 31, 43, 33, 37, 49, 17, 2, 9, 47, 11, 10, 46, 27, 50, 12, 29, 41, 18, 7, 16, 39, 14, 45, 20, 1, 6, 40, 32, 26, 13], 'cur_cost': 20366.0}, {'tour': array([10,  0,  3, 51,  1, 22, 28, 13, 23, 43,  2, 38, 47, 39, 29, 48, 41,
       31, 20,  7,  4, 36, 11, 34, 25, 18, 45, 12,  8, 15, 27, 17, 50, 49,
       24, 37, 32,  9, 19, 40, 14, 21, 33, 16, 46, 42, 44,  6, 30, 26,  5,
       35], dtype=int64), 'cur_cost': 31643.0}, {'tour': [0, 22, 21, 6, 1, 41, 20, 30, 17, 2, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 16, 32], 'cur_cost': 10587.0}, {'tour': [43, 21, 31, 47, 5, 48, 38, 17, 35, 4, 19, 34, 42, 8, 23, 0, 45, 27, 46, 15, 49, 20, 44, 18, 24, 50, 10, 32, 2, 14, 9, 16, 41, 39, 3, 7, 30, 33, 40, 29, 1, 6, 37, 28, 25, 11, 36, 22, 12, 26, 13, 51], 'cur_cost': 21370.0}, {'tour': [0, 5, 21, 7, 40, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 32, 41, 6, 1], 'cur_cost': 10958.0}, {'tour': [0, 21, 14, 17, 30, 20, 22, 19, 49, 15, 43, 33, 34, 35, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 41, 6, 1, 16, 2], 'cur_cost': 9361.0}, {'tour': [0, 12, 6, 10, 50, 11, 27, 26, 25, 46, 13, 51, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 1], 'cur_cost': 12942.0}, {'tour': [0, 19, 8, 22, 49, 15, 43, 33, 34, 35, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 48, 31, 21, 30, 17, 2, 18, 44, 40, 7, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 20, 16, 41, 6, 1], 'cur_cost': 10584.0}, {'tour': array([ 9, 26, 47, 24, 38,  3,  8, 27, 14, 13, 48,  4, 33, 36, 50,  7,  0,
       19, 41, 18, 35, 31, 32,  6, 22, 37,  2, 17,  1, 15, 23, 21, 46, 10,
       28, 34,  5, 29, 25, 40, 11, 30, 39, 42, 44, 43, 20, 16, 51, 45, 12,
       49], dtype=int64), 'cur_cost': 30523.0}, {'tour': [47, 2, 0, 40, 1, 17, 26, 46, 25, 13, 8, 30, 10, 27, 28, 34, 35, 23, 49, 6, 18, 42, 5, 43, 15, 14, 37, 32, 11, 16, 3, 29, 12, 31, 22, 7, 44, 19, 48, 39, 41, 9, 21, 45, 4, 24, 20, 51, 33, 50, 38, 36], 'cur_cost': 28006.0}]
2025-08-05 10:29:11,322 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:11,323 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 344, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 344, 'cache_hits': 0, 'similarity_calculations': 1761, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:11,324 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 9, 26, 47, 24, 38,  3,  8, 27, 14, 13, 48,  4, 33, 36, 50,  7,  0,
       19, 41, 18, 35, 31, 32,  6, 22, 37,  2, 17,  1, 15, 23, 21, 46, 10,
       28, 34,  5, 29, 25, 40, 11, 30, 39, 42, 44, 43, 20, 16, 51, 45, 12,
       49], dtype=int64), 'cur_cost': 30523.0, 'intermediate_solutions': [{'tour': array([44, 36, 27, 38, 16,  6, 40,  7,  9,  0, 15, 51, 39,  8, 30, 41, 47,
        2, 24, 45, 10, 31, 22, 35, 49, 33, 18,  3,  5, 42, 25,  1, 43, 32,
       23, 14, 26, 19, 11, 50, 37, 34, 21, 28, 17, 29, 13, 48, 20, 46,  4,
       12]), 'cur_cost': 28930.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([38, 44, 36, 27, 16,  6, 40,  7,  9,  0, 15, 51, 39,  8, 30, 41, 47,
        2, 24, 45, 10, 31, 22, 35, 49, 33, 18,  3,  5, 42, 25,  1, 43, 32,
       23, 14, 26, 19, 11, 50, 37, 34, 21, 28, 17, 29, 13, 48, 20, 46,  4,
       12]), 'cur_cost': 28919.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([16, 38, 44, 36, 27,  6, 40,  7,  9,  0, 15, 51, 39,  8, 30, 41, 47,
        2, 24, 45, 10, 31, 22, 35, 49, 33, 18,  3,  5, 42, 25,  1, 43, 32,
       23, 14, 26, 19, 11, 50, 37, 34, 21, 28, 17, 29, 13, 48, 20, 46,  4,
       12]), 'cur_cost': 29682.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([27, 38, 44, 36, 16,  6, 40,  7,  9,  0, 15, 51, 39,  8, 30, 41, 47,
        2, 24, 45, 10, 31, 22, 35, 49, 33, 18,  3,  5, 42, 25,  1, 43, 32,
       23, 14, 26, 19, 11, 50, 37, 34, 21, 28, 17, 29, 13, 48, 20, 46,  4,
       12]), 'cur_cost': 27897.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([27, 16, 38, 44, 36,  6, 40,  7,  9,  0, 15, 51, 39,  8, 30, 41, 47,
        2, 24, 45, 10, 31, 22, 35, 49, 33, 18,  3,  5, 42, 25,  1, 43, 32,
       23, 14, 26, 19, 11, 50, 37, 34, 21, 28, 17, 29, 13, 48, 20, 46,  4,
       12]), 'cur_cost': 28787.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:11,324 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 30523.00)
2025-08-05 10:29:11,324 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:29:11,325 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:11,325 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:11,325 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 29615.0
2025-08-05 10:29:11,332 - ExploitationExpert - INFO - res_population_num: 1
2025-08-05 10:29:11,332 - ExploitationExpert - INFO - res_population_costs: [7542.0]
2025-08-05 10:29:11,332 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64)]
2025-08-05 10:29:11,333 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:11,333 - ExploitationExpert - INFO - populations: [{'tour': [28, 19, 4, 21, 36, 22, 15, 5, 34, 30, 23, 3, 48, 38, 24, 25, 51, 42, 8, 44, 35, 0, 31, 43, 33, 37, 49, 17, 2, 9, 47, 11, 10, 46, 27, 50, 12, 29, 41, 18, 7, 16, 39, 14, 45, 20, 1, 6, 40, 32, 26, 13], 'cur_cost': 20366.0}, {'tour': array([10,  0,  3, 51,  1, 22, 28, 13, 23, 43,  2, 38, 47, 39, 29, 48, 41,
       31, 20,  7,  4, 36, 11, 34, 25, 18, 45, 12,  8, 15, 27, 17, 50, 49,
       24, 37, 32,  9, 19, 40, 14, 21, 33, 16, 46, 42, 44,  6, 30, 26,  5,
       35], dtype=int64), 'cur_cost': 31643.0}, {'tour': [0, 22, 21, 6, 1, 41, 20, 30, 17, 2, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 16, 32], 'cur_cost': 10587.0}, {'tour': [43, 21, 31, 47, 5, 48, 38, 17, 35, 4, 19, 34, 42, 8, 23, 0, 45, 27, 46, 15, 49, 20, 44, 18, 24, 50, 10, 32, 2, 14, 9, 16, 41, 39, 3, 7, 30, 33, 40, 29, 1, 6, 37, 28, 25, 11, 36, 22, 12, 26, 13, 51], 'cur_cost': 21370.0}, {'tour': [0, 5, 21, 7, 40, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 32, 41, 6, 1], 'cur_cost': 10958.0}, {'tour': [0, 21, 14, 17, 30, 20, 22, 19, 49, 15, 43, 33, 34, 35, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 41, 6, 1, 16, 2], 'cur_cost': 9361.0}, {'tour': [0, 12, 6, 10, 50, 11, 27, 26, 25, 46, 13, 51, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 1], 'cur_cost': 12942.0}, {'tour': [0, 19, 8, 22, 49, 15, 43, 33, 34, 35, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 48, 31, 21, 30, 17, 2, 18, 44, 40, 7, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 20, 16, 41, 6, 1], 'cur_cost': 10584.0}, {'tour': array([ 9, 26, 47, 24, 38,  3,  8, 27, 14, 13, 48,  4, 33, 36, 50,  7,  0,
       19, 41, 18, 35, 31, 32,  6, 22, 37,  2, 17,  1, 15, 23, 21, 46, 10,
       28, 34,  5, 29, 25, 40, 11, 30, 39, 42, 44, 43, 20, 16, 51, 45, 12,
       49], dtype=int64), 'cur_cost': 30523.0}, {'tour': array([ 2, 36,  3, 47, 13, 49, 44,  8, 17,  6, 34, 20, 30, 37, 24, 32, 14,
       10, 46, 23, 19, 28, 50, 42, 38, 26,  7, 22,  1, 15, 35, 48,  5, 25,
       16,  4,  9, 11, 31, 18, 51, 29,  0, 43, 41, 21, 39, 33, 12, 40, 27,
       45], dtype=int64), 'cur_cost': 29615.0}]
2025-08-05 10:29:11,335 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:11,335 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 345, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 345, 'cache_hits': 0, 'similarity_calculations': 1767, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:11,336 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([ 2, 36,  3, 47, 13, 49, 44,  8, 17,  6, 34, 20, 30, 37, 24, 32, 14,
       10, 46, 23, 19, 28, 50, 42, 38, 26,  7, 22,  1, 15, 35, 48,  5, 25,
       16,  4,  9, 11, 31, 18, 51, 29,  0, 43, 41, 21, 39, 33, 12, 40, 27,
       45], dtype=int64), 'cur_cost': 29615.0, 'intermediate_solutions': [{'tour': array([ 0,  2, 47, 40,  1, 17, 26, 46, 25, 13,  8, 30, 10, 27, 28, 34, 35,
       23, 49,  6, 18, 42,  5, 43, 15, 14, 37, 32, 11, 16,  3, 29, 12, 31,
       22,  7, 44, 19, 48, 39, 41,  9, 21, 45,  4, 24, 20, 51, 33, 50, 38,
       36]), 'cur_cost': 28258.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([40,  0,  2, 47,  1, 17, 26, 46, 25, 13,  8, 30, 10, 27, 28, 34, 35,
       23, 49,  6, 18, 42,  5, 43, 15, 14, 37, 32, 11, 16,  3, 29, 12, 31,
       22,  7, 44, 19, 48, 39, 41,  9, 21, 45,  4, 24, 20, 51, 33, 50, 38,
       36]), 'cur_cost': 28418.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1, 40,  0,  2, 47, 17, 26, 46, 25, 13,  8, 30, 10, 27, 28, 34, 35,
       23, 49,  6, 18, 42,  5, 43, 15, 14, 37, 32, 11, 16,  3, 29, 12, 31,
       22,  7, 44, 19, 48, 39, 41,  9, 21, 45,  4, 24, 20, 51, 33, 50, 38,
       36]), 'cur_cost': 28625.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([47, 40,  0,  2,  1, 17, 26, 46, 25, 13,  8, 30, 10, 27, 28, 34, 35,
       23, 49,  6, 18, 42,  5, 43, 15, 14, 37, 32, 11, 16,  3, 29, 12, 31,
       22,  7, 44, 19, 48, 39, 41,  9, 21, 45,  4, 24, 20, 51, 33, 50, 38,
       36]), 'cur_cost': 27753.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([47,  1, 40,  0,  2, 17, 26, 46, 25, 13,  8, 30, 10, 27, 28, 34, 35,
       23, 49,  6, 18, 42,  5, 43, 15, 14, 37, 32, 11, 16,  3, 29, 12, 31,
       22,  7, 44, 19, 48, 39, 41,  9, 21, 45,  4, 24, 20, 51, 33, 50, 38,
       36]), 'cur_cost': 27951.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:11,337 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 29615.00)
2025-08-05 10:29:11,337 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:11,337 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:11,338 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [28, 19, 4, 21, 36, 22, 15, 5, 34, 30, 23, 3, 48, 38, 24, 25, 51, 42, 8, 44, 35, 0, 31, 43, 33, 37, 49, 17, 2, 9, 47, 11, 10, 46, 27, 50, 12, 29, 41, 18, 7, 16, 39, 14, 45, 20, 1, 6, 40, 32, 26, 13], 'cur_cost': 20366.0, 'intermediate_solutions': [{'tour': [0, 15, 12, 6, 1, 41, 20, 30, 17, 21, 48, 31, 35, 34, 29, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 49, 19, 22, 33, 28, 46, 25, 26, 27, 11, 50, 10, 51, 13, 42, 9, 8, 7, 40, 18, 44, 2, 16, 32], 'cur_cost': 12850.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 12, 6, 1, 41, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 7, 8, 9, 42, 13, 51, 40, 18, 44, 2, 16, 32], 'cur_cost': 13892.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 12, 6, 1, 41, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 49, 19, 22, 29, 28, 46, 8, 25, 26, 27, 11, 50, 10, 51, 13, 42, 9, 7, 40, 18, 44, 2, 16, 32], 'cur_cost': 13868.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([10,  0,  3, 51,  1, 22, 28, 13, 23, 43,  2, 38, 47, 39, 29, 48, 41,
       31, 20,  7,  4, 36, 11, 34, 25, 18, 45, 12,  8, 15, 27, 17, 50, 49,
       24, 37, 32,  9, 19, 40, 14, 21, 33, 16, 46, 42, 44,  6, 30, 26,  5,
       35], dtype=int64), 'cur_cost': 31643.0, 'intermediate_solutions': [{'tour': array([49, 47, 33, 23,  4, 31, 44,  2, 37, 38, 17,  0, 19, 30, 28, 25, 14,
       35,  7, 39,  9, 21, 18, 34, 11,  3, 42, 22, 29,  5, 36, 24, 46, 27,
       10, 51, 50, 48, 20, 43, 40, 16,  6, 15, 41,  8,  1, 45, 26, 12, 13,
       32]), 'cur_cost': 22569.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([23, 49, 47, 33,  4, 31, 44,  2, 37, 38, 17,  0, 19, 30, 28, 25, 14,
       35,  7, 39,  9, 21, 18, 34, 11,  3, 42, 22, 29,  5, 36, 24, 46, 27,
       10, 51, 50, 48, 20, 43, 40, 16,  6, 15, 41,  8,  1, 45, 26, 12, 13,
       32]), 'cur_cost': 22563.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 4, 23, 49, 47, 33, 31, 44,  2, 37, 38, 17,  0, 19, 30, 28, 25, 14,
       35,  7, 39,  9, 21, 18, 34, 11,  3, 42, 22, 29,  5, 36, 24, 46, 27,
       10, 51, 50, 48, 20, 43, 40, 16,  6, 15, 41,  8,  1, 45, 26, 12, 13,
       32]), 'cur_cost': 22282.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([33, 23, 49, 47,  4, 31, 44,  2, 37, 38, 17,  0, 19, 30, 28, 25, 14,
       35,  7, 39,  9, 21, 18, 34, 11,  3, 42, 22, 29,  5, 36, 24, 46, 27,
       10, 51, 50, 48, 20, 43, 40, 16,  6, 15, 41,  8,  1, 45, 26, 12, 13,
       32]), 'cur_cost': 22569.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([33,  4, 23, 49, 47, 31, 44,  2, 37, 38, 17,  0, 19, 30, 28, 25, 14,
       35,  7, 39,  9, 21, 18, 34, 11,  3, 42, 22, 29,  5, 36, 24, 46, 27,
       10, 51, 50, 48, 20, 43, 40, 16,  6, 15, 41,  8,  1, 45, 26, 12, 13,
       32]), 'cur_cost': 22566.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 22, 21, 6, 1, 41, 20, 30, 17, 2, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 16, 32], 'cur_cost': 10587.0, 'intermediate_solutions': [{'tour': [0, 20, 16, 7, 40, 27, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 21, 2, 8, 9, 42, 32, 50, 11, 18, 26, 25, 46, 12, 13, 51, 10, 28, 29, 41, 6, 1], 'cur_cost': 12633.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 16, 7, 40, 18, 44, 31, 48, 35, 34, 33, 38, 39, 51, 13, 12, 46, 25, 26, 27, 11, 50, 32, 42, 9, 8, 2, 21, 17, 30, 22, 19, 49, 15, 43, 45, 24, 3, 5, 14, 4, 23, 47, 36, 37, 10, 28, 29, 41, 6, 1], 'cur_cost': 11009.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 16, 7, 40, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 15, 49, 19, 22, 30, 17, 21, 2, 8, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 41, 6, 43, 1], 'cur_cost': 10893.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [43, 21, 31, 47, 5, 48, 38, 17, 35, 4, 19, 34, 42, 8, 23, 0, 45, 27, 46, 15, 49, 20, 44, 18, 24, 50, 10, 32, 2, 14, 9, 16, 41, 39, 3, 7, 30, 33, 40, 29, 1, 6, 37, 28, 25, 11, 36, 22, 12, 26, 13, 51], 'cur_cost': 21370.0, 'intermediate_solutions': [{'tour': [33, 20, 16, 23, 40, 31, 30, 2, 17, 21, 48, 0, 35, 46, 38, 25, 14, 37, 7, 39, 9, 4, 18, 5, 3, 24, 45, 22, 49, 19, 36, 29, 28, 34, 10, 51, 27, 11, 50, 43, 26, 13, 42, 15, 8, 12, 1, 41, 44, 6, 32, 47], 'cur_cost': 23909.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [33, 20, 16, 23, 40, 31, 30, 2, 17, 21, 48, 0, 35, 34, 38, 25, 14, 37, 7, 39, 9, 4, 18, 5, 3, 24, 45, 22, 15, 42, 13, 26, 43, 50, 11, 27, 51, 10, 46, 28, 29, 36, 19, 49, 8, 12, 1, 41, 44, 6, 32, 47], 'cur_cost': 22532.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [20, 16, 23, 40, 31, 30, 2, 17, 21, 48, 0, 35, 34, 38, 25, 14, 37, 7, 39, 9, 4, 18, 5, 3, 24, 45, 22, 49, 19, 36, 29, 28, 46, 10, 51, 27, 11, 50, 43, 26, 13, 42, 15, 33, 8, 12, 1, 41, 44, 6, 32, 47], 'cur_cost': 22413.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 21, 7, 40, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 32, 41, 6, 1], 'cur_cost': 10958.0, 'intermediate_solutions': [{'tour': [0, 8, 18, 22, 19, 49, 15, 43, 33, 34, 12, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 48, 31, 21, 30, 17, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 35, 13, 42, 9, 7, 40, 44, 32, 41, 6, 1], 'cur_cost': 14806.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 18, 22, 19, 49, 15, 43, 33, 34, 35, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 48, 31, 21, 30, 20, 16, 2, 17, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 7, 40, 44, 32, 41, 6, 1], 'cur_cost': 11670.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 18, 22, 19, 49, 15, 43, 33, 35, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 48, 31, 21, 30, 17, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 34, 50, 10, 51, 12, 13, 42, 9, 7, 40, 44, 32, 41, 6, 1], 'cur_cost': 12470.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 14, 17, 30, 20, 22, 19, 49, 15, 43, 33, 34, 35, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 41, 6, 1, 16, 2], 'cur_cost': 9361.0, 'intermediate_solutions': [{'tour': [0, 9, 12, 15, 49, 19, 22, 30, 17, 21, 48, 31, 35, 34, 33, 6, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 28, 29, 20, 16, 2, 18, 44, 40, 7, 8, 42, 32, 50, 11, 27, 26, 25, 46, 13, 51, 10, 41, 38, 1], 'cur_cost': 14677.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 12, 15, 38, 33, 34, 35, 31, 48, 21, 17, 30, 22, 19, 49, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 28, 29, 20, 16, 2, 18, 44, 40, 7, 8, 42, 32, 50, 11, 27, 26, 25, 46, 13, 51, 10, 41, 6, 1], 'cur_cost': 12162.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 12, 15, 49, 19, 22, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 45, 43, 28, 29, 20, 16, 24, 2, 18, 44, 40, 7, 8, 42, 32, 50, 11, 27, 26, 25, 46, 13, 51, 10, 41, 6, 1], 'cur_cost': 12955.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 6, 10, 50, 11, 27, 26, 25, 46, 13, 51, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 1], 'cur_cost': 12942.0, 'intermediate_solutions': [{'tour': [0, 14, 21, 36, 12, 26, 27, 25, 46, 11, 50, 10, 51, 24, 3, 5, 4, 23, 47, 37, 39, 13, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 2, 18, 44, 31, 48, 20, 29, 28, 41, 6, 1, 16, 40, 7, 9, 8, 42, 32], 'cur_cost': 12876.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 25, 27, 26, 12, 13, 21, 14, 46, 11, 50, 10, 51, 24, 3, 5, 4, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 2, 18, 44, 31, 48, 20, 29, 28, 41, 6, 1, 16, 40, 7, 9, 8, 42, 32], 'cur_cost': 12196.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 21, 13, 12, 26, 27, 25, 46, 11, 50, 10, 51, 24, 3, 5, 4, 23, 47, 37, 39, 36, 38, 18, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 2, 44, 31, 48, 20, 29, 28, 41, 6, 1, 16, 40, 7, 9, 8, 42, 32], 'cur_cost': 11788.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 8, 22, 49, 15, 43, 33, 34, 35, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 48, 31, 21, 30, 17, 2, 18, 44, 40, 7, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 20, 16, 41, 6, 1], 'cur_cost': 10584.0, 'intermediate_solutions': [{'tour': [0, 17, 8, 12, 26, 27, 6, 46, 13, 51, 10, 50, 11, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 21, 48, 31, 44, 18, 40, 7, 9, 42, 32, 2, 16, 20, 29, 28, 41, 25, 1], 'cur_cost': 14790.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 8, 12, 26, 27, 25, 46, 13, 51, 10, 50, 11, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 21, 44, 31, 48, 18, 40, 7, 9, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10881.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 17, 8, 12, 26, 27, 25, 46, 13, 51, 10, 50, 11, 24, 3, 5, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 21, 48, 31, 44, 18, 40, 7, 9, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 11100.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 9, 26, 47, 24, 38,  3,  8, 27, 14, 13, 48,  4, 33, 36, 50,  7,  0,
       19, 41, 18, 35, 31, 32,  6, 22, 37,  2, 17,  1, 15, 23, 21, 46, 10,
       28, 34,  5, 29, 25, 40, 11, 30, 39, 42, 44, 43, 20, 16, 51, 45, 12,
       49], dtype=int64), 'cur_cost': 30523.0, 'intermediate_solutions': [{'tour': array([44, 36, 27, 38, 16,  6, 40,  7,  9,  0, 15, 51, 39,  8, 30, 41, 47,
        2, 24, 45, 10, 31, 22, 35, 49, 33, 18,  3,  5, 42, 25,  1, 43, 32,
       23, 14, 26, 19, 11, 50, 37, 34, 21, 28, 17, 29, 13, 48, 20, 46,  4,
       12]), 'cur_cost': 28930.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([38, 44, 36, 27, 16,  6, 40,  7,  9,  0, 15, 51, 39,  8, 30, 41, 47,
        2, 24, 45, 10, 31, 22, 35, 49, 33, 18,  3,  5, 42, 25,  1, 43, 32,
       23, 14, 26, 19, 11, 50, 37, 34, 21, 28, 17, 29, 13, 48, 20, 46,  4,
       12]), 'cur_cost': 28919.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([16, 38, 44, 36, 27,  6, 40,  7,  9,  0, 15, 51, 39,  8, 30, 41, 47,
        2, 24, 45, 10, 31, 22, 35, 49, 33, 18,  3,  5, 42, 25,  1, 43, 32,
       23, 14, 26, 19, 11, 50, 37, 34, 21, 28, 17, 29, 13, 48, 20, 46,  4,
       12]), 'cur_cost': 29682.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([27, 38, 44, 36, 16,  6, 40,  7,  9,  0, 15, 51, 39,  8, 30, 41, 47,
        2, 24, 45, 10, 31, 22, 35, 49, 33, 18,  3,  5, 42, 25,  1, 43, 32,
       23, 14, 26, 19, 11, 50, 37, 34, 21, 28, 17, 29, 13, 48, 20, 46,  4,
       12]), 'cur_cost': 27897.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([27, 16, 38, 44, 36,  6, 40,  7,  9,  0, 15, 51, 39,  8, 30, 41, 47,
        2, 24, 45, 10, 31, 22, 35, 49, 33, 18,  3,  5, 42, 25,  1, 43, 32,
       23, 14, 26, 19, 11, 50, 37, 34, 21, 28, 17, 29, 13, 48, 20, 46,  4,
       12]), 'cur_cost': 28787.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 2, 36,  3, 47, 13, 49, 44,  8, 17,  6, 34, 20, 30, 37, 24, 32, 14,
       10, 46, 23, 19, 28, 50, 42, 38, 26,  7, 22,  1, 15, 35, 48,  5, 25,
       16,  4,  9, 11, 31, 18, 51, 29,  0, 43, 41, 21, 39, 33, 12, 40, 27,
       45], dtype=int64), 'cur_cost': 29615.0, 'intermediate_solutions': [{'tour': array([ 0,  2, 47, 40,  1, 17, 26, 46, 25, 13,  8, 30, 10, 27, 28, 34, 35,
       23, 49,  6, 18, 42,  5, 43, 15, 14, 37, 32, 11, 16,  3, 29, 12, 31,
       22,  7, 44, 19, 48, 39, 41,  9, 21, 45,  4, 24, 20, 51, 33, 50, 38,
       36]), 'cur_cost': 28258.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([40,  0,  2, 47,  1, 17, 26, 46, 25, 13,  8, 30, 10, 27, 28, 34, 35,
       23, 49,  6, 18, 42,  5, 43, 15, 14, 37, 32, 11, 16,  3, 29, 12, 31,
       22,  7, 44, 19, 48, 39, 41,  9, 21, 45,  4, 24, 20, 51, 33, 50, 38,
       36]), 'cur_cost': 28418.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1, 40,  0,  2, 47, 17, 26, 46, 25, 13,  8, 30, 10, 27, 28, 34, 35,
       23, 49,  6, 18, 42,  5, 43, 15, 14, 37, 32, 11, 16,  3, 29, 12, 31,
       22,  7, 44, 19, 48, 39, 41,  9, 21, 45,  4, 24, 20, 51, 33, 50, 38,
       36]), 'cur_cost': 28625.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([47, 40,  0,  2,  1, 17, 26, 46, 25, 13,  8, 30, 10, 27, 28, 34, 35,
       23, 49,  6, 18, 42,  5, 43, 15, 14, 37, 32, 11, 16,  3, 29, 12, 31,
       22,  7, 44, 19, 48, 39, 41,  9, 21, 45,  4, 24, 20, 51, 33, 50, 38,
       36]), 'cur_cost': 27753.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([47,  1, 40,  0,  2, 17, 26, 46, 25, 13,  8, 30, 10, 27, 28, 34, 35,
       23, 49,  6, 18, 42,  5, 43, 15, 14, 37, 32, 11, 16,  3, 29, 12, 31,
       22,  7, 44, 19, 48, 39, 41,  9, 21, 45,  4, 24, 20, 51, 33, 50, 38,
       36]), 'cur_cost': 27951.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:29:11,339 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:11,339 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:11,342 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9361.000, 多样性=0.957
2025-08-05 10:29:11,342 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 10:29:11,342 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 10:29:11,342 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:11,342 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.07753448356921665, 'best_improvement': 0.022145617883631046}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.03273397879206984}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.05557888198306121, 'recent_improvements': [-0.03536134817233149, -0.022545032570769934, 0.07579641579379091], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 1, 'new_count': 1, 'count_change': 0, 'old_best_cost': 7542.0, 'new_best_cost': 7542.0, 'quality_improvement': 0.0, 'old_diversity': 0.0, 'new_diversity': 0.0, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:29:11,343 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 10:29:11,343 - __main__ - INFO - berlin52 开始进化第 4 代
2025-08-05 10:29:11,343 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 10:29:11,343 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:11,343 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9361.000, 多样性=0.957
2025-08-05 10:29:11,344 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:11,346 - PathExpert - INFO - 路径结构分析完成: 公共边数量=3, 路径相似性=0.957
2025-08-05 10:29:11,346 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:11,346 - EliteExpert - INFO - 精英解分析完成: 精英解数量=1, 多样性=0.000
2025-08-05 10:29:11,348 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 10:29:11,348 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:11,348 - LandscapeExpert - INFO - 添加精英解数据: 1个精英解
2025-08-05 10:29:11,348 - LandscapeExpert - INFO - 数据提取成功: 11个路径, 11个适应度值
2025-08-05 10:29:11,366 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.091, 适应度梯度: -119.182, 聚类评分: 0.000, 覆盖率: 0.152, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:11,366 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 10:29:11,366 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:11,366 - visualization.landscape_visualizer - INFO - 设置当前实例名: berlin52
2025-08-05 10:29:11,371 - visualization.landscape_visualizer - INFO - 插值约束: 38 个点被约束到最小值 7542.00
2025-08-05 10:29:11,372 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.9%, 梯度: 749.62 → 690.07
2025-08-05 10:29:11,539 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_berlin52\landscape_berlin52_iter_134_20250805_102911.html
2025-08-05 10:29:11,579 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_berlin52\dashboard_berlin52_iter_134_20250805_102911.html
2025-08-05 10:29:11,579 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 134
2025-08-05 10:29:11,579 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 10:29:11,579 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2303秒
2025-08-05 10:29:11,580 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.09090909090909091, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -119.18181818181819, 'local_optima_density': 0.09090909090909091, 'gradient_variance': 61915694.046942145, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1517, 'fitness_entropy': 0.9609557933859342, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -119.182)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.152)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360951.3666384, 'performance_metrics': {}}}
2025-08-05 10:29:11,580 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:11,580 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:11,580 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:11,580 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:11,580 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:11,581 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 10:29:11,581 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:11,581 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:11,581 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:11,581 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:11,581 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:11,582 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:11,582 - experts.management.collaboration_manager - INFO - 识别精英个体: {5, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:11,582 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:11,582 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:11,582 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:11,583 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 52
2025-08-05 10:29:11,583 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,584 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,584 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,584 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,584 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27591.0, 路径长度: 52, 收集中间解: 3
2025-08-05 10:29:11,584 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [28, 22, 31, 21, 36, 41, 15, 30, 23, 43, 2, 3, 47, 38, 29, 25, 33, 42, 20, 44, 49, 0, 11, 4, 14, 5, 45, 12, 8, 9, 27, 17, 10, 46, 24, 37, 32, 26, 19, 18, 1, 16, 39, 51, 13, 6, 50, 48, 40, 7, 34, 35], 'cur_cost': 27591.0, 'intermediate_solutions': [{'tour': [28, 19, 4, 21, 36, 22, 15, 5, 34, 30, 23, 3, 14, 38, 24, 25, 51, 42, 8, 44, 35, 0, 31, 43, 33, 37, 49, 17, 2, 9, 47, 11, 10, 46, 27, 50, 12, 29, 41, 18, 7, 16, 39, 48, 45, 20, 1, 6, 40, 32, 26, 13], 'cur_cost': 20275.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [28, 19, 4, 21, 36, 22, 15, 5, 34, 30, 23, 3, 48, 38, 24, 25, 51, 42, 8, 44, 35, 0, 27, 46, 10, 11, 47, 9, 2, 17, 49, 37, 33, 43, 31, 50, 12, 29, 41, 18, 7, 16, 39, 14, 45, 20, 1, 6, 40, 32, 26, 13], 'cur_cost': 21412.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [28, 19, 4, 21, 36, 22, 15, 5, 34, 30, 23, 3, 48, 38, 24, 25, 51, 42, 8, 44, 35, 0, 31, 43, 33, 37, 49, 17, 2, 9, 47, 11, 10, 46, 27, 50, 12, 29, 41, 18, 7, 14, 16, 39, 45, 20, 1, 6, 40, 32, 26, 13], 'cur_cost': 20905.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:11,585 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 27591.00)
2025-08-05 10:29:11,585 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 10:29:11,585 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:11,585 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:11,585 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 31911.0
2025-08-05 10:29:11,594 - ExploitationExpert - INFO - res_population_num: 1
2025-08-05 10:29:11,594 - ExploitationExpert - INFO - res_population_costs: [7542.0]
2025-08-05 10:29:11,594 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64)]
2025-08-05 10:29:11,596 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:11,596 - ExploitationExpert - INFO - populations: [{'tour': [28, 22, 31, 21, 36, 41, 15, 30, 23, 43, 2, 3, 47, 38, 29, 25, 33, 42, 20, 44, 49, 0, 11, 4, 14, 5, 45, 12, 8, 9, 27, 17, 10, 46, 24, 37, 32, 26, 19, 18, 1, 16, 39, 51, 13, 6, 50, 48, 40, 7, 34, 35], 'cur_cost': 27591.0}, {'tour': array([14, 44, 13, 49,  4,  3, 31, 28, 33,  1, 12, 35, 26,  0, 42, 27,  2,
        5, 50,  6, 38, 32, 25, 15, 23, 10, 19, 43, 40,  9,  8, 46, 48, 21,
       24, 34, 51, 39,  7, 22, 45, 18, 41, 30, 17, 29, 47, 37, 20, 11, 36,
       16], dtype=int64), 'cur_cost': 31911.0}, {'tour': [0, 22, 21, 6, 1, 41, 20, 30, 17, 2, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 16, 32], 'cur_cost': 10587.0}, {'tour': [43, 21, 31, 47, 5, 48, 38, 17, 35, 4, 19, 34, 42, 8, 23, 0, 45, 27, 46, 15, 49, 20, 44, 18, 24, 50, 10, 32, 2, 14, 9, 16, 41, 39, 3, 7, 30, 33, 40, 29, 1, 6, 37, 28, 25, 11, 36, 22, 12, 26, 13, 51], 'cur_cost': 21370.0}, {'tour': [0, 5, 21, 7, 40, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 32, 41, 6, 1], 'cur_cost': 10958.0}, {'tour': [0, 21, 14, 17, 30, 20, 22, 19, 49, 15, 43, 33, 34, 35, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 41, 6, 1, 16, 2], 'cur_cost': 9361.0}, {'tour': [0, 12, 6, 10, 50, 11, 27, 26, 25, 46, 13, 51, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 1], 'cur_cost': 12942.0}, {'tour': [0, 19, 8, 22, 49, 15, 43, 33, 34, 35, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 48, 31, 21, 30, 17, 2, 18, 44, 40, 7, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 20, 16, 41, 6, 1], 'cur_cost': 10584.0}, {'tour': [9, 26, 47, 24, 38, 3, 8, 27, 14, 13, 48, 4, 33, 36, 50, 7, 0, 19, 41, 18, 35, 31, 32, 6, 22, 37, 2, 17, 1, 15, 23, 21, 46, 10, 28, 34, 5, 29, 25, 40, 11, 30, 39, 42, 44, 43, 20, 16, 51, 45, 12, 49], 'cur_cost': 30523.0}, {'tour': [2, 36, 3, 47, 13, 49, 44, 8, 17, 6, 34, 20, 30, 37, 24, 32, 14, 10, 46, 23, 19, 28, 50, 42, 38, 26, 7, 22, 1, 15, 35, 48, 5, 25, 16, 4, 9, 11, 31, 18, 51, 29, 0, 43, 41, 21, 39, 33, 12, 40, 27, 45], 'cur_cost': 29615.0}]
2025-08-05 10:29:11,598 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:11,598 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 346, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 346, 'cache_hits': 0, 'similarity_calculations': 1774, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:11,599 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([14, 44, 13, 49,  4,  3, 31, 28, 33,  1, 12, 35, 26,  0, 42, 27,  2,
        5, 50,  6, 38, 32, 25, 15, 23, 10, 19, 43, 40,  9,  8, 46, 48, 21,
       24, 34, 51, 39,  7, 22, 45, 18, 41, 30, 17, 29, 47, 37, 20, 11, 36,
       16], dtype=int64), 'cur_cost': 31911.0, 'intermediate_solutions': [{'tour': array([ 3,  0, 10, 51,  1, 22, 28, 13, 23, 43,  2, 38, 47, 39, 29, 48, 41,
       31, 20,  7,  4, 36, 11, 34, 25, 18, 45, 12,  8, 15, 27, 17, 50, 49,
       24, 37, 32,  9, 19, 40, 14, 21, 33, 16, 46, 42, 44,  6, 30, 26,  5,
       35]), 'cur_cost': 30484.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([51,  3,  0, 10,  1, 22, 28, 13, 23, 43,  2, 38, 47, 39, 29, 48, 41,
       31, 20,  7,  4, 36, 11, 34, 25, 18, 45, 12,  8, 15, 27, 17, 50, 49,
       24, 37, 32,  9, 19, 40, 14, 21, 33, 16, 46, 42, 44,  6, 30, 26,  5,
       35]), 'cur_cost': 31762.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1, 51,  3,  0, 10, 22, 28, 13, 23, 43,  2, 38, 47, 39, 29, 48, 41,
       31, 20,  7,  4, 36, 11, 34, 25, 18, 45, 12,  8, 15, 27, 17, 50, 49,
       24, 37, 32,  9, 19, 40, 14, 21, 33, 16, 46, 42, 44,  6, 30, 26,  5,
       35]), 'cur_cost': 32142.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([10, 51,  3,  0,  1, 22, 28, 13, 23, 43,  2, 38, 47, 39, 29, 48, 41,
       31, 20,  7,  4, 36, 11, 34, 25, 18, 45, 12,  8, 15, 27, 17, 50, 49,
       24, 37, 32,  9, 19, 40, 14, 21, 33, 16, 46, 42, 44,  6, 30, 26,  5,
       35]), 'cur_cost': 29951.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([10,  1, 51,  3,  0, 22, 28, 13, 23, 43,  2, 38, 47, 39, 29, 48, 41,
       31, 20,  7,  4, 36, 11, 34, 25, 18, 45, 12,  8, 15, 27, 17, 50, 49,
       24, 37, 32,  9, 19, 40, 14, 21, 33, 16, 46, 42, 44,  6, 30, 26,  5,
       35]), 'cur_cost': 31912.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:11,600 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 31911.00)
2025-08-05 10:29:11,600 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:11,600 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:11,600 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:11,603 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 10:29:11,603 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,603 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,603 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,604 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,604 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11228.0, 路径长度: 52, 收集中间解: 3
2025-08-05 10:29:11,604 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 16, 14, 8, 9, 7, 40, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 21, 2, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 32, 41, 6, 1], 'cur_cost': 11228.0, 'intermediate_solutions': [{'tour': [24, 22, 21, 6, 1, 41, 20, 30, 17, 2, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 0, 45, 43, 15, 49, 19, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 16, 32], 'cur_cost': 11101.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 22, 21, 6, 1, 41, 20, 30, 17, 2, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 13, 12, 51, 10, 50, 11, 27, 26, 25, 46, 28, 29, 19, 49, 15, 43, 45, 24, 3, 5, 14, 4, 23, 47, 36, 42, 9, 8, 7, 40, 16, 32], 'cur_cost': 10721.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 22, 21, 6, 1, 41, 20, 30, 17, 2, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 45, 43, 24, 15, 49, 19, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 16, 32], 'cur_cost': 11016.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:11,604 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 11228.00)
2025-08-05 10:29:11,604 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:11,605 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:11,605 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:11,607 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 10:29:11,607 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,608 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,608 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,608 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,608 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11194.0, 路径长度: 52, 收集中间解: 3
2025-08-05 10:29:11,608 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 15, 3, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 24, 45, 43, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 18, 44, 2, 16, 41, 6, 1, 32], 'cur_cost': 11194.0, 'intermediate_solutions': [{'tour': [43, 21, 31, 47, 5, 48, 38, 17, 35, 4, 19, 34, 42, 8, 23, 0, 45, 27, 46, 15, 49, 20, 44, 18, 24, 50, 10, 32, 2, 14, 9, 39, 41, 16, 3, 7, 30, 33, 40, 29, 1, 6, 37, 28, 25, 11, 36, 22, 12, 26, 13, 51], 'cur_cost': 21788.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [43, 21, 31, 47, 5, 48, 38, 17, 35, 4, 19, 34, 42, 8, 23, 0, 45, 27, 46, 15, 49, 20, 44, 18, 24, 50, 10, 32, 2, 14, 9, 16, 41, 13, 26, 12, 22, 36, 11, 25, 28, 37, 6, 1, 29, 40, 33, 30, 7, 3, 39, 51], 'cur_cost': 22797.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [43, 21, 31, 47, 5, 48, 38, 17, 35, 4, 19, 34, 42, 8, 23, 0, 45, 27, 46, 15, 49, 20, 44, 18, 24, 50, 10, 32, 2, 14, 9, 16, 41, 39, 3, 7, 30, 33, 40, 1, 6, 37, 29, 28, 25, 11, 36, 22, 12, 26, 13, 51], 'cur_cost': 21491.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:11,609 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 11194.00)
2025-08-05 10:29:11,609 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:29:11,609 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:29:11,609 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:11,613 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 52
2025-08-05 10:29:11,613 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,614 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,614 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,614 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,615 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20993.0, 路径长度: 52, 收集中间解: 3
2025-08-05 10:29:11,615 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [10, 23, 47, 21, 14, 43, 24, 38, 30, 29, 41, 18, 40, 34, 37, 3, 49, 15, 25, 50, 12, 19, 35, 36, 2, 31, 42, 4, 11, 45, 22, 17, 33, 5, 39, 9, 20, 16, 44, 7, 48, 8, 32, 0, 6, 1, 46, 27, 26, 13, 51, 28], 'cur_cost': 20993.0, 'intermediate_solutions': [{'tour': [0, 5, 17, 7, 40, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 3, 24, 45, 43, 15, 49, 19, 22, 30, 21, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 32, 41, 6, 1], 'cur_cost': 11152.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 21, 7, 40, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 32, 41, 1, 6], 'cur_cost': 10960.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 21, 7, 40, 18, 44, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 3, 24, 31, 45, 43, 15, 49, 19, 22, 30, 17, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 32, 41, 6, 1], 'cur_cost': 11501.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:11,615 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 20993.00)
2025-08-05 10:29:11,615 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:29:11,615 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:29:11,615 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:11,619 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 52
2025-08-05 10:29:11,620 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,620 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,620 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,620 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,620 - ExplorationExpert - INFO - 探索路径生成完成，成本: 22109.0, 路径长度: 52, 收集中间解: 3
2025-08-05 10:29:11,621 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [20, 43, 4, 34, 37, 21, 2, 9, 18, 38, 42, 24, 35, 3, 11, 14, 7, 39, 17, 29, 19, 48, 33, 45, 28, 23, 5, 31, 41, 15, 49, 40, 32, 44, 16, 47, 50, 26, 12, 27, 51, 10, 13, 36, 30, 0, 22, 1, 8, 6, 46, 25], 'cur_cost': 22109.0, 'intermediate_solutions': [{'tour': [0, 21, 14, 17, 2, 20, 22, 19, 49, 15, 43, 33, 34, 35, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 41, 6, 1, 16, 30], 'cur_cost': 9499.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 14, 17, 30, 20, 22, 19, 49, 15, 43, 33, 34, 35, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 50, 32, 42, 8, 9, 7, 40, 18, 44, 31, 48, 45, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 41, 6, 1, 16, 2], 'cur_cost': 9794.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 14, 17, 30, 20, 22, 19, 49, 11, 15, 43, 33, 34, 35, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 50, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 41, 6, 1, 16, 2], 'cur_cost': 10399.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:11,621 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 22109.00)
2025-08-05 10:29:11,621 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:11,621 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:11,621 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:11,625 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 10:29:11,625 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,625 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,625 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,625 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,626 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10699.0, 路径长度: 52, 收集中间解: 3
2025-08-05 10:29:11,626 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 21, 11, 4, 14, 5, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 2, 18, 44, 31, 48, 20, 29, 28, 24, 3, 42, 9, 8, 7, 40, 16, 41, 6, 1, 46, 25, 26, 27, 12, 13, 51, 10, 50, 32], 'cur_cost': 10699.0, 'intermediate_solutions': [{'tour': [0, 12, 6, 10, 50, 11, 27, 26, 25, 46, 13, 51, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 9, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 19, 8, 42, 32, 2, 16, 20, 29, 28, 41, 1], 'cur_cost': 15503.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 12, 6, 10, 50, 11, 27, 26, 25, 46, 13, 51, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 2, 32, 42, 8, 9, 7, 40, 18, 44, 31, 48, 21, 17, 16, 20, 29, 28, 41, 1], 'cur_cost': 13126.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 6, 10, 50, 11, 27, 26, 25, 46, 13, 51, 24, 3, 42, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 32, 2, 16, 20, 29, 28, 41, 1], 'cur_cost': 13192.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:11,626 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 10699.00)
2025-08-05 10:29:11,627 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:11,627 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:11,627 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:11,630 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 10:29:11,630 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,631 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,631 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,632 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,632 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9870.0, 路径长度: 52, 收集中间解: 3
2025-08-05 10:29:11,632 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 18, 3, 11, 27, 26, 25, 46, 12, 13, 51, 10, 50, 24, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 9870.0, 'intermediate_solutions': [{'tour': [0, 19, 8, 22, 49, 13, 43, 33, 34, 35, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 48, 31, 21, 30, 17, 2, 18, 44, 40, 7, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 15, 51, 10, 28, 29, 20, 16, 41, 6, 1], 'cur_cost': 13545.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [32, 42, 9, 7, 40, 44, 18, 2, 17, 30, 21, 31, 48, 45, 24, 3, 5, 14, 4, 23, 47, 36, 37, 39, 38, 35, 34, 33, 43, 15, 49, 22, 8, 19, 0, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 20, 16, 41, 6, 1], 'cur_cost': 11721.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 8, 22, 49, 21, 15, 43, 33, 34, 35, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 48, 31, 30, 17, 2, 18, 44, 40, 7, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 20, 16, 41, 6, 1], 'cur_cost': 10977.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:11,632 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 9870.00)
2025-08-05 10:29:11,633 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:29:11,633 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:11,633 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:11,633 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 28606.0
2025-08-05 10:29:11,643 - ExploitationExpert - INFO - res_population_num: 1
2025-08-05 10:29:11,644 - ExploitationExpert - INFO - res_population_costs: [7542.0]
2025-08-05 10:29:11,644 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64)]
2025-08-05 10:29:11,645 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:11,645 - ExploitationExpert - INFO - populations: [{'tour': [28, 22, 31, 21, 36, 41, 15, 30, 23, 43, 2, 3, 47, 38, 29, 25, 33, 42, 20, 44, 49, 0, 11, 4, 14, 5, 45, 12, 8, 9, 27, 17, 10, 46, 24, 37, 32, 26, 19, 18, 1, 16, 39, 51, 13, 6, 50, 48, 40, 7, 34, 35], 'cur_cost': 27591.0}, {'tour': array([14, 44, 13, 49,  4,  3, 31, 28, 33,  1, 12, 35, 26,  0, 42, 27,  2,
        5, 50,  6, 38, 32, 25, 15, 23, 10, 19, 43, 40,  9,  8, 46, 48, 21,
       24, 34, 51, 39,  7, 22, 45, 18, 41, 30, 17, 29, 47, 37, 20, 11, 36,
       16], dtype=int64), 'cur_cost': 31911.0}, {'tour': [0, 16, 14, 8, 9, 7, 40, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 21, 2, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 32, 41, 6, 1], 'cur_cost': 11228.0}, {'tour': [0, 15, 3, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 24, 45, 43, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 18, 44, 2, 16, 41, 6, 1, 32], 'cur_cost': 11194.0}, {'tour': [10, 23, 47, 21, 14, 43, 24, 38, 30, 29, 41, 18, 40, 34, 37, 3, 49, 15, 25, 50, 12, 19, 35, 36, 2, 31, 42, 4, 11, 45, 22, 17, 33, 5, 39, 9, 20, 16, 44, 7, 48, 8, 32, 0, 6, 1, 46, 27, 26, 13, 51, 28], 'cur_cost': 20993.0}, {'tour': [20, 43, 4, 34, 37, 21, 2, 9, 18, 38, 42, 24, 35, 3, 11, 14, 7, 39, 17, 29, 19, 48, 33, 45, 28, 23, 5, 31, 41, 15, 49, 40, 32, 44, 16, 47, 50, 26, 12, 27, 51, 10, 13, 36, 30, 0, 22, 1, 8, 6, 46, 25], 'cur_cost': 22109.0}, {'tour': [0, 21, 11, 4, 14, 5, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 2, 18, 44, 31, 48, 20, 29, 28, 24, 3, 42, 9, 8, 7, 40, 16, 41, 6, 1, 46, 25, 26, 27, 12, 13, 51, 10, 50, 32], 'cur_cost': 10699.0}, {'tour': [0, 18, 3, 11, 27, 26, 25, 46, 12, 13, 51, 10, 50, 24, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 9870.0}, {'tour': array([43,  9, 23, 10, 42,  2, 49, 21,  1, 15, 14, 41, 45,  6, 20, 48, 37,
        3, 32,  0, 39, 27, 24, 28, 30, 33,  8, 40, 13, 11, 29, 17, 31, 22,
       47, 35, 26,  4, 36, 44, 18, 46,  5, 51, 12,  7, 25, 34, 38, 19, 50,
       16], dtype=int64), 'cur_cost': 28606.0}, {'tour': [2, 36, 3, 47, 13, 49, 44, 8, 17, 6, 34, 20, 30, 37, 24, 32, 14, 10, 46, 23, 19, 28, 50, 42, 38, 26, 7, 22, 1, 15, 35, 48, 5, 25, 16, 4, 9, 11, 31, 18, 51, 29, 0, 43, 41, 21, 39, 33, 12, 40, 27, 45], 'cur_cost': 29615.0}]
2025-08-05 10:29:11,647 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:11,647 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 347, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 347, 'cache_hits': 0, 'similarity_calculations': 1782, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:11,648 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([43,  9, 23, 10, 42,  2, 49, 21,  1, 15, 14, 41, 45,  6, 20, 48, 37,
        3, 32,  0, 39, 27, 24, 28, 30, 33,  8, 40, 13, 11, 29, 17, 31, 22,
       47, 35, 26,  4, 36, 44, 18, 46,  5, 51, 12,  7, 25, 34, 38, 19, 50,
       16], dtype=int64), 'cur_cost': 28606.0, 'intermediate_solutions': [{'tour': array([47, 26,  9, 24, 38,  3,  8, 27, 14, 13, 48,  4, 33, 36, 50,  7,  0,
       19, 41, 18, 35, 31, 32,  6, 22, 37,  2, 17,  1, 15, 23, 21, 46, 10,
       28, 34,  5, 29, 25, 40, 11, 30, 39, 42, 44, 43, 20, 16, 51, 45, 12,
       49]), 'cur_cost': 30585.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([24, 47, 26,  9, 38,  3,  8, 27, 14, 13, 48,  4, 33, 36, 50,  7,  0,
       19, 41, 18, 35, 31, 32,  6, 22, 37,  2, 17,  1, 15, 23, 21, 46, 10,
       28, 34,  5, 29, 25, 40, 11, 30, 39, 42, 44, 43, 20, 16, 51, 45, 12,
       49]), 'cur_cost': 30429.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([38, 24, 47, 26,  9,  3,  8, 27, 14, 13, 48,  4, 33, 36, 50,  7,  0,
       19, 41, 18, 35, 31, 32,  6, 22, 37,  2, 17,  1, 15, 23, 21, 46, 10,
       28, 34,  5, 29, 25, 40, 11, 30, 39, 42, 44, 43, 20, 16, 51, 45, 12,
       49]), 'cur_cost': 30357.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 9, 24, 47, 26, 38,  3,  8, 27, 14, 13, 48,  4, 33, 36, 50,  7,  0,
       19, 41, 18, 35, 31, 32,  6, 22, 37,  2, 17,  1, 15, 23, 21, 46, 10,
       28, 34,  5, 29, 25, 40, 11, 30, 39, 42, 44, 43, 20, 16, 51, 45, 12,
       49]), 'cur_cost': 30526.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 9, 38, 24, 47, 26,  3,  8, 27, 14, 13, 48,  4, 33, 36, 50,  7,  0,
       19, 41, 18, 35, 31, 32,  6, 22, 37,  2, 17,  1, 15, 23, 21, 46, 10,
       28, 34,  5, 29, 25, 40, 11, 30, 39, 42, 44, 43, 20, 16, 51, 45, 12,
       49]), 'cur_cost': 30265.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:11,649 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 28606.00)
2025-08-05 10:29:11,649 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:29:11,649 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:11,649 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:11,650 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 31928.0
2025-08-05 10:29:11,656 - ExploitationExpert - INFO - res_population_num: 1
2025-08-05 10:29:11,656 - ExploitationExpert - INFO - res_population_costs: [7542.0]
2025-08-05 10:29:11,656 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64)]
2025-08-05 10:29:11,657 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:11,657 - ExploitationExpert - INFO - populations: [{'tour': [28, 22, 31, 21, 36, 41, 15, 30, 23, 43, 2, 3, 47, 38, 29, 25, 33, 42, 20, 44, 49, 0, 11, 4, 14, 5, 45, 12, 8, 9, 27, 17, 10, 46, 24, 37, 32, 26, 19, 18, 1, 16, 39, 51, 13, 6, 50, 48, 40, 7, 34, 35], 'cur_cost': 27591.0}, {'tour': array([14, 44, 13, 49,  4,  3, 31, 28, 33,  1, 12, 35, 26,  0, 42, 27,  2,
        5, 50,  6, 38, 32, 25, 15, 23, 10, 19, 43, 40,  9,  8, 46, 48, 21,
       24, 34, 51, 39,  7, 22, 45, 18, 41, 30, 17, 29, 47, 37, 20, 11, 36,
       16], dtype=int64), 'cur_cost': 31911.0}, {'tour': [0, 16, 14, 8, 9, 7, 40, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 21, 2, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 32, 41, 6, 1], 'cur_cost': 11228.0}, {'tour': [0, 15, 3, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 24, 45, 43, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 18, 44, 2, 16, 41, 6, 1, 32], 'cur_cost': 11194.0}, {'tour': [10, 23, 47, 21, 14, 43, 24, 38, 30, 29, 41, 18, 40, 34, 37, 3, 49, 15, 25, 50, 12, 19, 35, 36, 2, 31, 42, 4, 11, 45, 22, 17, 33, 5, 39, 9, 20, 16, 44, 7, 48, 8, 32, 0, 6, 1, 46, 27, 26, 13, 51, 28], 'cur_cost': 20993.0}, {'tour': [20, 43, 4, 34, 37, 21, 2, 9, 18, 38, 42, 24, 35, 3, 11, 14, 7, 39, 17, 29, 19, 48, 33, 45, 28, 23, 5, 31, 41, 15, 49, 40, 32, 44, 16, 47, 50, 26, 12, 27, 51, 10, 13, 36, 30, 0, 22, 1, 8, 6, 46, 25], 'cur_cost': 22109.0}, {'tour': [0, 21, 11, 4, 14, 5, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 2, 18, 44, 31, 48, 20, 29, 28, 24, 3, 42, 9, 8, 7, 40, 16, 41, 6, 1, 46, 25, 26, 27, 12, 13, 51, 10, 50, 32], 'cur_cost': 10699.0}, {'tour': [0, 18, 3, 11, 27, 26, 25, 46, 12, 13, 51, 10, 50, 24, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 9870.0}, {'tour': array([43,  9, 23, 10, 42,  2, 49, 21,  1, 15, 14, 41, 45,  6, 20, 48, 37,
        3, 32,  0, 39, 27, 24, 28, 30, 33,  8, 40, 13, 11, 29, 17, 31, 22,
       47, 35, 26,  4, 36, 44, 18, 46,  5, 51, 12,  7, 25, 34, 38, 19, 50,
       16], dtype=int64), 'cur_cost': 28606.0}, {'tour': array([18, 28, 10, 40, 45, 46, 35, 41, 27, 33, 30, 15, 44, 23,  9, 31, 12,
        2, 48, 51, 36,  5, 14, 21, 22, 11, 16, 42, 19, 13, 34,  3, 26, 38,
        6, 43, 49, 39,  4, 47, 24,  0, 17, 25, 32, 37,  1, 50, 29, 20,  7,
        8], dtype=int64), 'cur_cost': 31928.0}]
2025-08-05 10:29:11,659 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:11,659 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 348, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 348, 'cache_hits': 0, 'similarity_calculations': 1791, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:11,660 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([18, 28, 10, 40, 45, 46, 35, 41, 27, 33, 30, 15, 44, 23,  9, 31, 12,
        2, 48, 51, 36,  5, 14, 21, 22, 11, 16, 42, 19, 13, 34,  3, 26, 38,
        6, 43, 49, 39,  4, 47, 24,  0, 17, 25, 32, 37,  1, 50, 29, 20,  7,
        8], dtype=int64), 'cur_cost': 31928.0, 'intermediate_solutions': [{'tour': array([ 3, 36,  2, 47, 13, 49, 44,  8, 17,  6, 34, 20, 30, 37, 24, 32, 14,
       10, 46, 23, 19, 28, 50, 42, 38, 26,  7, 22,  1, 15, 35, 48,  5, 25,
       16,  4,  9, 11, 31, 18, 51, 29,  0, 43, 41, 21, 39, 33, 12, 40, 27,
       45]), 'cur_cost': 29661.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([47,  3, 36,  2, 13, 49, 44,  8, 17,  6, 34, 20, 30, 37, 24, 32, 14,
       10, 46, 23, 19, 28, 50, 42, 38, 26,  7, 22,  1, 15, 35, 48,  5, 25,
       16,  4,  9, 11, 31, 18, 51, 29,  0, 43, 41, 21, 39, 33, 12, 40, 27,
       45]), 'cur_cost': 29662.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([13, 47,  3, 36,  2, 49, 44,  8, 17,  6, 34, 20, 30, 37, 24, 32, 14,
       10, 46, 23, 19, 28, 50, 42, 38, 26,  7, 22,  1, 15, 35, 48,  5, 25,
       16,  4,  9, 11, 31, 18, 51, 29,  0, 43, 41, 21, 39, 33, 12, 40, 27,
       45]), 'cur_cost': 29374.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2, 47,  3, 36, 13, 49, 44,  8, 17,  6, 34, 20, 30, 37, 24, 32, 14,
       10, 46, 23, 19, 28, 50, 42, 38, 26,  7, 22,  1, 15, 35, 48,  5, 25,
       16,  4,  9, 11, 31, 18, 51, 29,  0, 43, 41, 21, 39, 33, 12, 40, 27,
       45]), 'cur_cost': 29719.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2, 13, 47,  3, 36, 49, 44,  8, 17,  6, 34, 20, 30, 37, 24, 32, 14,
       10, 46, 23, 19, 28, 50, 42, 38, 26,  7, 22,  1, 15, 35, 48,  5, 25,
       16,  4,  9, 11, 31, 18, 51, 29,  0, 43, 41, 21, 39, 33, 12, 40, 27,
       45]), 'cur_cost': 29873.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:11,660 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 31928.00)
2025-08-05 10:29:11,660 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:11,660 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:11,662 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [28, 22, 31, 21, 36, 41, 15, 30, 23, 43, 2, 3, 47, 38, 29, 25, 33, 42, 20, 44, 49, 0, 11, 4, 14, 5, 45, 12, 8, 9, 27, 17, 10, 46, 24, 37, 32, 26, 19, 18, 1, 16, 39, 51, 13, 6, 50, 48, 40, 7, 34, 35], 'cur_cost': 27591.0, 'intermediate_solutions': [{'tour': [28, 19, 4, 21, 36, 22, 15, 5, 34, 30, 23, 3, 14, 38, 24, 25, 51, 42, 8, 44, 35, 0, 31, 43, 33, 37, 49, 17, 2, 9, 47, 11, 10, 46, 27, 50, 12, 29, 41, 18, 7, 16, 39, 48, 45, 20, 1, 6, 40, 32, 26, 13], 'cur_cost': 20275.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [28, 19, 4, 21, 36, 22, 15, 5, 34, 30, 23, 3, 48, 38, 24, 25, 51, 42, 8, 44, 35, 0, 27, 46, 10, 11, 47, 9, 2, 17, 49, 37, 33, 43, 31, 50, 12, 29, 41, 18, 7, 16, 39, 14, 45, 20, 1, 6, 40, 32, 26, 13], 'cur_cost': 21412.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [28, 19, 4, 21, 36, 22, 15, 5, 34, 30, 23, 3, 48, 38, 24, 25, 51, 42, 8, 44, 35, 0, 31, 43, 33, 37, 49, 17, 2, 9, 47, 11, 10, 46, 27, 50, 12, 29, 41, 18, 7, 14, 16, 39, 45, 20, 1, 6, 40, 32, 26, 13], 'cur_cost': 20905.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([14, 44, 13, 49,  4,  3, 31, 28, 33,  1, 12, 35, 26,  0, 42, 27,  2,
        5, 50,  6, 38, 32, 25, 15, 23, 10, 19, 43, 40,  9,  8, 46, 48, 21,
       24, 34, 51, 39,  7, 22, 45, 18, 41, 30, 17, 29, 47, 37, 20, 11, 36,
       16], dtype=int64), 'cur_cost': 31911.0, 'intermediate_solutions': [{'tour': array([ 3,  0, 10, 51,  1, 22, 28, 13, 23, 43,  2, 38, 47, 39, 29, 48, 41,
       31, 20,  7,  4, 36, 11, 34, 25, 18, 45, 12,  8, 15, 27, 17, 50, 49,
       24, 37, 32,  9, 19, 40, 14, 21, 33, 16, 46, 42, 44,  6, 30, 26,  5,
       35]), 'cur_cost': 30484.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([51,  3,  0, 10,  1, 22, 28, 13, 23, 43,  2, 38, 47, 39, 29, 48, 41,
       31, 20,  7,  4, 36, 11, 34, 25, 18, 45, 12,  8, 15, 27, 17, 50, 49,
       24, 37, 32,  9, 19, 40, 14, 21, 33, 16, 46, 42, 44,  6, 30, 26,  5,
       35]), 'cur_cost': 31762.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1, 51,  3,  0, 10, 22, 28, 13, 23, 43,  2, 38, 47, 39, 29, 48, 41,
       31, 20,  7,  4, 36, 11, 34, 25, 18, 45, 12,  8, 15, 27, 17, 50, 49,
       24, 37, 32,  9, 19, 40, 14, 21, 33, 16, 46, 42, 44,  6, 30, 26,  5,
       35]), 'cur_cost': 32142.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([10, 51,  3,  0,  1, 22, 28, 13, 23, 43,  2, 38, 47, 39, 29, 48, 41,
       31, 20,  7,  4, 36, 11, 34, 25, 18, 45, 12,  8, 15, 27, 17, 50, 49,
       24, 37, 32,  9, 19, 40, 14, 21, 33, 16, 46, 42, 44,  6, 30, 26,  5,
       35]), 'cur_cost': 29951.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([10,  1, 51,  3,  0, 22, 28, 13, 23, 43,  2, 38, 47, 39, 29, 48, 41,
       31, 20,  7,  4, 36, 11, 34, 25, 18, 45, 12,  8, 15, 27, 17, 50, 49,
       24, 37, 32,  9, 19, 40, 14, 21, 33, 16, 46, 42, 44,  6, 30, 26,  5,
       35]), 'cur_cost': 31912.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 14, 8, 9, 7, 40, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 21, 2, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 32, 41, 6, 1], 'cur_cost': 11228.0, 'intermediate_solutions': [{'tour': [24, 22, 21, 6, 1, 41, 20, 30, 17, 2, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 0, 45, 43, 15, 49, 19, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 16, 32], 'cur_cost': 11101.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 22, 21, 6, 1, 41, 20, 30, 17, 2, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 13, 12, 51, 10, 50, 11, 27, 26, 25, 46, 28, 29, 19, 49, 15, 43, 45, 24, 3, 5, 14, 4, 23, 47, 36, 42, 9, 8, 7, 40, 16, 32], 'cur_cost': 10721.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 22, 21, 6, 1, 41, 20, 30, 17, 2, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 45, 43, 24, 15, 49, 19, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 16, 32], 'cur_cost': 11016.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 3, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 24, 45, 43, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 18, 44, 2, 16, 41, 6, 1, 32], 'cur_cost': 11194.0, 'intermediate_solutions': [{'tour': [43, 21, 31, 47, 5, 48, 38, 17, 35, 4, 19, 34, 42, 8, 23, 0, 45, 27, 46, 15, 49, 20, 44, 18, 24, 50, 10, 32, 2, 14, 9, 39, 41, 16, 3, 7, 30, 33, 40, 29, 1, 6, 37, 28, 25, 11, 36, 22, 12, 26, 13, 51], 'cur_cost': 21788.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [43, 21, 31, 47, 5, 48, 38, 17, 35, 4, 19, 34, 42, 8, 23, 0, 45, 27, 46, 15, 49, 20, 44, 18, 24, 50, 10, 32, 2, 14, 9, 16, 41, 13, 26, 12, 22, 36, 11, 25, 28, 37, 6, 1, 29, 40, 33, 30, 7, 3, 39, 51], 'cur_cost': 22797.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [43, 21, 31, 47, 5, 48, 38, 17, 35, 4, 19, 34, 42, 8, 23, 0, 45, 27, 46, 15, 49, 20, 44, 18, 24, 50, 10, 32, 2, 14, 9, 16, 41, 39, 3, 7, 30, 33, 40, 1, 6, 37, 29, 28, 25, 11, 36, 22, 12, 26, 13, 51], 'cur_cost': 21491.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [10, 23, 47, 21, 14, 43, 24, 38, 30, 29, 41, 18, 40, 34, 37, 3, 49, 15, 25, 50, 12, 19, 35, 36, 2, 31, 42, 4, 11, 45, 22, 17, 33, 5, 39, 9, 20, 16, 44, 7, 48, 8, 32, 0, 6, 1, 46, 27, 26, 13, 51, 28], 'cur_cost': 20993.0, 'intermediate_solutions': [{'tour': [0, 5, 17, 7, 40, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 3, 24, 45, 43, 15, 49, 19, 22, 30, 21, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 32, 41, 6, 1], 'cur_cost': 11152.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 21, 7, 40, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 32, 41, 1, 6], 'cur_cost': 10960.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 21, 7, 40, 18, 44, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 3, 24, 31, 45, 43, 15, 49, 19, 22, 30, 17, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 32, 41, 6, 1], 'cur_cost': 11501.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [20, 43, 4, 34, 37, 21, 2, 9, 18, 38, 42, 24, 35, 3, 11, 14, 7, 39, 17, 29, 19, 48, 33, 45, 28, 23, 5, 31, 41, 15, 49, 40, 32, 44, 16, 47, 50, 26, 12, 27, 51, 10, 13, 36, 30, 0, 22, 1, 8, 6, 46, 25], 'cur_cost': 22109.0, 'intermediate_solutions': [{'tour': [0, 21, 14, 17, 2, 20, 22, 19, 49, 15, 43, 33, 34, 35, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 41, 6, 1, 16, 30], 'cur_cost': 9499.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 14, 17, 30, 20, 22, 19, 49, 15, 43, 33, 34, 35, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 50, 32, 42, 8, 9, 7, 40, 18, 44, 31, 48, 45, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 41, 6, 1, 16, 2], 'cur_cost': 9794.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 14, 17, 30, 20, 22, 19, 49, 11, 15, 43, 33, 34, 35, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 50, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 41, 6, 1, 16, 2], 'cur_cost': 10399.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 11, 4, 14, 5, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 2, 18, 44, 31, 48, 20, 29, 28, 24, 3, 42, 9, 8, 7, 40, 16, 41, 6, 1, 46, 25, 26, 27, 12, 13, 51, 10, 50, 32], 'cur_cost': 10699.0, 'intermediate_solutions': [{'tour': [0, 12, 6, 10, 50, 11, 27, 26, 25, 46, 13, 51, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 9, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 19, 8, 42, 32, 2, 16, 20, 29, 28, 41, 1], 'cur_cost': 15503.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 12, 6, 10, 50, 11, 27, 26, 25, 46, 13, 51, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 2, 32, 42, 8, 9, 7, 40, 18, 44, 31, 48, 21, 17, 16, 20, 29, 28, 41, 1], 'cur_cost': 13126.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 6, 10, 50, 11, 27, 26, 25, 46, 13, 51, 24, 3, 42, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 32, 2, 16, 20, 29, 28, 41, 1], 'cur_cost': 13192.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 3, 11, 27, 26, 25, 46, 12, 13, 51, 10, 50, 24, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 9870.0, 'intermediate_solutions': [{'tour': [0, 19, 8, 22, 49, 13, 43, 33, 34, 35, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 48, 31, 21, 30, 17, 2, 18, 44, 40, 7, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 15, 51, 10, 28, 29, 20, 16, 41, 6, 1], 'cur_cost': 13545.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [32, 42, 9, 7, 40, 44, 18, 2, 17, 30, 21, 31, 48, 45, 24, 3, 5, 14, 4, 23, 47, 36, 37, 39, 38, 35, 34, 33, 43, 15, 49, 22, 8, 19, 0, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 20, 16, 41, 6, 1], 'cur_cost': 11721.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 8, 22, 49, 21, 15, 43, 33, 34, 35, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 48, 31, 30, 17, 2, 18, 44, 40, 7, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 20, 16, 41, 6, 1], 'cur_cost': 10977.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([43,  9, 23, 10, 42,  2, 49, 21,  1, 15, 14, 41, 45,  6, 20, 48, 37,
        3, 32,  0, 39, 27, 24, 28, 30, 33,  8, 40, 13, 11, 29, 17, 31, 22,
       47, 35, 26,  4, 36, 44, 18, 46,  5, 51, 12,  7, 25, 34, 38, 19, 50,
       16], dtype=int64), 'cur_cost': 28606.0, 'intermediate_solutions': [{'tour': array([47, 26,  9, 24, 38,  3,  8, 27, 14, 13, 48,  4, 33, 36, 50,  7,  0,
       19, 41, 18, 35, 31, 32,  6, 22, 37,  2, 17,  1, 15, 23, 21, 46, 10,
       28, 34,  5, 29, 25, 40, 11, 30, 39, 42, 44, 43, 20, 16, 51, 45, 12,
       49]), 'cur_cost': 30585.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([24, 47, 26,  9, 38,  3,  8, 27, 14, 13, 48,  4, 33, 36, 50,  7,  0,
       19, 41, 18, 35, 31, 32,  6, 22, 37,  2, 17,  1, 15, 23, 21, 46, 10,
       28, 34,  5, 29, 25, 40, 11, 30, 39, 42, 44, 43, 20, 16, 51, 45, 12,
       49]), 'cur_cost': 30429.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([38, 24, 47, 26,  9,  3,  8, 27, 14, 13, 48,  4, 33, 36, 50,  7,  0,
       19, 41, 18, 35, 31, 32,  6, 22, 37,  2, 17,  1, 15, 23, 21, 46, 10,
       28, 34,  5, 29, 25, 40, 11, 30, 39, 42, 44, 43, 20, 16, 51, 45, 12,
       49]), 'cur_cost': 30357.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 9, 24, 47, 26, 38,  3,  8, 27, 14, 13, 48,  4, 33, 36, 50,  7,  0,
       19, 41, 18, 35, 31, 32,  6, 22, 37,  2, 17,  1, 15, 23, 21, 46, 10,
       28, 34,  5, 29, 25, 40, 11, 30, 39, 42, 44, 43, 20, 16, 51, 45, 12,
       49]), 'cur_cost': 30526.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 9, 38, 24, 47, 26,  3,  8, 27, 14, 13, 48,  4, 33, 36, 50,  7,  0,
       19, 41, 18, 35, 31, 32,  6, 22, 37,  2, 17,  1, 15, 23, 21, 46, 10,
       28, 34,  5, 29, 25, 40, 11, 30, 39, 42, 44, 43, 20, 16, 51, 45, 12,
       49]), 'cur_cost': 30265.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([18, 28, 10, 40, 45, 46, 35, 41, 27, 33, 30, 15, 44, 23,  9, 31, 12,
        2, 48, 51, 36,  5, 14, 21, 22, 11, 16, 42, 19, 13, 34,  3, 26, 38,
        6, 43, 49, 39,  4, 47, 24,  0, 17, 25, 32, 37,  1, 50, 29, 20,  7,
        8], dtype=int64), 'cur_cost': 31928.0, 'intermediate_solutions': [{'tour': array([ 3, 36,  2, 47, 13, 49, 44,  8, 17,  6, 34, 20, 30, 37, 24, 32, 14,
       10, 46, 23, 19, 28, 50, 42, 38, 26,  7, 22,  1, 15, 35, 48,  5, 25,
       16,  4,  9, 11, 31, 18, 51, 29,  0, 43, 41, 21, 39, 33, 12, 40, 27,
       45]), 'cur_cost': 29661.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([47,  3, 36,  2, 13, 49, 44,  8, 17,  6, 34, 20, 30, 37, 24, 32, 14,
       10, 46, 23, 19, 28, 50, 42, 38, 26,  7, 22,  1, 15, 35, 48,  5, 25,
       16,  4,  9, 11, 31, 18, 51, 29,  0, 43, 41, 21, 39, 33, 12, 40, 27,
       45]), 'cur_cost': 29662.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([13, 47,  3, 36,  2, 49, 44,  8, 17,  6, 34, 20, 30, 37, 24, 32, 14,
       10, 46, 23, 19, 28, 50, 42, 38, 26,  7, 22,  1, 15, 35, 48,  5, 25,
       16,  4,  9, 11, 31, 18, 51, 29,  0, 43, 41, 21, 39, 33, 12, 40, 27,
       45]), 'cur_cost': 29374.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2, 47,  3, 36, 13, 49, 44,  8, 17,  6, 34, 20, 30, 37, 24, 32, 14,
       10, 46, 23, 19, 28, 50, 42, 38, 26,  7, 22,  1, 15, 35, 48,  5, 25,
       16,  4,  9, 11, 31, 18, 51, 29,  0, 43, 41, 21, 39, 33, 12, 40, 27,
       45]), 'cur_cost': 29719.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2, 13, 47,  3, 36, 49, 44,  8, 17,  6, 34, 20, 30, 37, 24, 32, 14,
       10, 46, 23, 19, 28, 50, 42, 38, 26,  7, 22,  1, 15, 35, 48,  5, 25,
       16,  4,  9, 11, 31, 18, 51, 29,  0, 43, 41, 21, 39, 33, 12, 40, 27,
       45]), 'cur_cost': 29873.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:29:11,663 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:11,663 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:11,666 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9870.000, 多样性=0.980
2025-08-05 10:29:11,666 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 10:29:11,666 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 10:29:11,666 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:11,666 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.053369877261297084, 'best_improvement': -0.0543745326354022}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.02410714285714289}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.027494725499223355, 'recent_improvements': [-0.022545032570769934, 0.07579641579379091, -0.07753448356921665], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 1, 'new_count': 1, 'count_change': 0, 'old_best_cost': 7542.0, 'new_best_cost': 7542.0, 'quality_improvement': 0.0, 'old_diversity': 0.0, 'new_diversity': 0.0, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:29:11,666 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 10:29:11,666 - __main__ - INFO - berlin52 开始进化第 5 代
2025-08-05 10:29:11,667 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 10:29:11,667 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:11,667 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9870.000, 多样性=0.980
2025-08-05 10:29:11,668 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:11,670 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.980
2025-08-05 10:29:11,670 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:11,670 - EliteExpert - INFO - 精英解分析完成: 精英解数量=1, 多样性=0.000
2025-08-05 10:29:11,673 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 10:29:11,673 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:11,673 - LandscapeExpert - INFO - 添加精英解数据: 1个精英解
2025-08-05 10:29:11,673 - LandscapeExpert - INFO - 数据提取成功: 11个路径, 11个适应度值
2025-08-05 10:29:11,690 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.182, 适应度梯度: -729.927, 聚类评分: 0.000, 覆盖率: 0.152, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:11,690 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 10:29:11,690 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:11,690 - visualization.landscape_visualizer - INFO - 设置当前实例名: berlin52
2025-08-05 10:29:11,695 - visualization.landscape_visualizer - INFO - 插值约束: 238 个点被约束到最小值 7542.00
2025-08-05 10:29:11,697 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=6.9%, 梯度: 997.36 → 929.00
2025-08-05 10:29:11,802 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_berlin52\landscape_berlin52_iter_135_20250805_102911.html
2025-08-05 10:29:11,874 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_berlin52\dashboard_berlin52_iter_135_20250805_102911.html
2025-08-05 10:29:11,874 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 135
2025-08-05 10:29:11,875 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 10:29:11,875 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2022秒
2025-08-05 10:29:11,875 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.18181818181818182, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -729.9272727272725, 'local_optima_density': 0.18181818181818182, 'gradient_variance': 76983344.54016529, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1525, 'fitness_entropy': 0.9905114616736054, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -729.927)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.152)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360951.6907737, 'performance_metrics': {}}}
2025-08-05 10:29:11,875 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:11,875 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:11,875 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:11,876 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:11,876 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:11,876 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 10:29:11,876 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:11,876 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:11,877 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:11,877 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:11,877 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:11,877 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:11,877 - experts.management.collaboration_manager - INFO - 识别精英个体: {6, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:11,877 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:11,877 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:11,878 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:11,880 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 10:29:11,880 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,880 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,880 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,880 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,881 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11300.0, 路径长度: 52, 收集中间解: 3
2025-08-05 10:29:11,881 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 8, 2, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 7, 40, 18, 44, 16, 41, 6, 1, 32], 'cur_cost': 11300.0, 'intermediate_solutions': [{'tour': [28, 22, 31, 21, 36, 41, 23, 30, 15, 43, 2, 3, 47, 38, 29, 25, 33, 42, 20, 44, 49, 0, 11, 4, 14, 5, 45, 12, 8, 9, 27, 17, 10, 46, 24, 37, 32, 26, 19, 18, 1, 16, 39, 51, 13, 6, 50, 48, 40, 7, 34, 35], 'cur_cost': 27724.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [28, 22, 31, 21, 36, 41, 15, 30, 23, 43, 2, 3, 47, 38, 29, 25, 33, 42, 20, 44, 49, 0, 11, 4, 14, 5, 45, 12, 46, 10, 17, 27, 9, 8, 24, 37, 32, 26, 19, 18, 1, 16, 39, 51, 13, 6, 50, 48, 40, 7, 34, 35], 'cur_cost': 26761.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [28, 22, 31, 36, 41, 15, 30, 23, 43, 2, 3, 47, 38, 29, 25, 33, 42, 20, 44, 49, 0, 11, 4, 14, 5, 45, 12, 8, 9, 27, 17, 10, 21, 46, 24, 37, 32, 26, 19, 18, 1, 16, 39, 51, 13, 6, 50, 48, 40, 7, 34, 35], 'cur_cost': 28659.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:11,881 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 11300.00)
2025-08-05 10:29:11,881 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 10:29:11,881 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:11,881 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:11,882 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 30739.0
2025-08-05 10:29:11,889 - ExploitationExpert - INFO - res_population_num: 1
2025-08-05 10:29:11,890 - ExploitationExpert - INFO - res_population_costs: [7542.0]
2025-08-05 10:29:11,890 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64)]
2025-08-05 10:29:11,890 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:11,890 - ExploitationExpert - INFO - populations: [{'tour': [0, 8, 2, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 7, 40, 18, 44, 16, 41, 6, 1, 32], 'cur_cost': 11300.0}, {'tour': array([13, 35, 41, 12,  0, 48, 32,  4, 25, 47, 44, 31,  5, 34, 21, 26, 36,
       43, 39, 28, 33, 23, 42, 49, 29, 11, 51, 22,  8, 38,  7, 27, 50, 14,
       20,  9, 17, 15, 45, 40,  3, 10, 18, 37,  6, 30, 16, 19, 24,  2, 46,
        1], dtype=int64), 'cur_cost': 30739.0}, {'tour': [0, 16, 14, 8, 9, 7, 40, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 21, 2, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 32, 41, 6, 1], 'cur_cost': 11228.0}, {'tour': [0, 15, 3, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 24, 45, 43, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 18, 44, 2, 16, 41, 6, 1, 32], 'cur_cost': 11194.0}, {'tour': [10, 23, 47, 21, 14, 43, 24, 38, 30, 29, 41, 18, 40, 34, 37, 3, 49, 15, 25, 50, 12, 19, 35, 36, 2, 31, 42, 4, 11, 45, 22, 17, 33, 5, 39, 9, 20, 16, 44, 7, 48, 8, 32, 0, 6, 1, 46, 27, 26, 13, 51, 28], 'cur_cost': 20993.0}, {'tour': [20, 43, 4, 34, 37, 21, 2, 9, 18, 38, 42, 24, 35, 3, 11, 14, 7, 39, 17, 29, 19, 48, 33, 45, 28, 23, 5, 31, 41, 15, 49, 40, 32, 44, 16, 47, 50, 26, 12, 27, 51, 10, 13, 36, 30, 0, 22, 1, 8, 6, 46, 25], 'cur_cost': 22109.0}, {'tour': [0, 21, 11, 4, 14, 5, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 2, 18, 44, 31, 48, 20, 29, 28, 24, 3, 42, 9, 8, 7, 40, 16, 41, 6, 1, 46, 25, 26, 27, 12, 13, 51, 10, 50, 32], 'cur_cost': 10699.0}, {'tour': [0, 18, 3, 11, 27, 26, 25, 46, 12, 13, 51, 10, 50, 24, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 9870.0}, {'tour': [43, 9, 23, 10, 42, 2, 49, 21, 1, 15, 14, 41, 45, 6, 20, 48, 37, 3, 32, 0, 39, 27, 24, 28, 30, 33, 8, 40, 13, 11, 29, 17, 31, 22, 47, 35, 26, 4, 36, 44, 18, 46, 5, 51, 12, 7, 25, 34, 38, 19, 50, 16], 'cur_cost': 28606.0}, {'tour': [18, 28, 10, 40, 45, 46, 35, 41, 27, 33, 30, 15, 44, 23, 9, 31, 12, 2, 48, 51, 36, 5, 14, 21, 22, 11, 16, 42, 19, 13, 34, 3, 26, 38, 6, 43, 49, 39, 4, 47, 24, 0, 17, 25, 32, 37, 1, 50, 29, 20, 7, 8], 'cur_cost': 31928.0}]
2025-08-05 10:29:11,891 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:11,891 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 349, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 349, 'cache_hits': 0, 'similarity_calculations': 1801, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:11,892 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([13, 35, 41, 12,  0, 48, 32,  4, 25, 47, 44, 31,  5, 34, 21, 26, 36,
       43, 39, 28, 33, 23, 42, 49, 29, 11, 51, 22,  8, 38,  7, 27, 50, 14,
       20,  9, 17, 15, 45, 40,  3, 10, 18, 37,  6, 30, 16, 19, 24,  2, 46,
        1], dtype=int64), 'cur_cost': 30739.0, 'intermediate_solutions': [{'tour': array([13, 44, 14, 49,  4,  3, 31, 28, 33,  1, 12, 35, 26,  0, 42, 27,  2,
        5, 50,  6, 38, 32, 25, 15, 23, 10, 19, 43, 40,  9,  8, 46, 48, 21,
       24, 34, 51, 39,  7, 22, 45, 18, 41, 30, 17, 29, 47, 37, 20, 11, 36,
       16]), 'cur_cost': 32151.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([49, 13, 44, 14,  4,  3, 31, 28, 33,  1, 12, 35, 26,  0, 42, 27,  2,
        5, 50,  6, 38, 32, 25, 15, 23, 10, 19, 43, 40,  9,  8, 46, 48, 21,
       24, 34, 51, 39,  7, 22, 45, 18, 41, 30, 17, 29, 47, 37, 20, 11, 36,
       16]), 'cur_cost': 31393.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 4, 49, 13, 44, 14,  3, 31, 28, 33,  1, 12, 35, 26,  0, 42, 27,  2,
        5, 50,  6, 38, 32, 25, 15, 23, 10, 19, 43, 40,  9,  8, 46, 48, 21,
       24, 34, 51, 39,  7, 22, 45, 18, 41, 30, 17, 29, 47, 37, 20, 11, 36,
       16]), 'cur_cost': 31907.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([14, 49, 13, 44,  4,  3, 31, 28, 33,  1, 12, 35, 26,  0, 42, 27,  2,
        5, 50,  6, 38, 32, 25, 15, 23, 10, 19, 43, 40,  9,  8, 46, 48, 21,
       24, 34, 51, 39,  7, 22, 45, 18, 41, 30, 17, 29, 47, 37, 20, 11, 36,
       16]), 'cur_cost': 31941.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([14,  4, 49, 13, 44,  3, 31, 28, 33,  1, 12, 35, 26,  0, 42, 27,  2,
        5, 50,  6, 38, 32, 25, 15, 23, 10, 19, 43, 40,  9,  8, 46, 48, 21,
       24, 34, 51, 39,  7, 22, 45, 18, 41, 30, 17, 29, 47, 37, 20, 11, 36,
       16]), 'cur_cost': 31923.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:11,893 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 30739.00)
2025-08-05 10:29:11,893 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:11,893 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:11,893 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:11,896 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 10:29:11,896 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,897 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,897 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,897 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,898 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10846.0, 路径长度: 52, 收集中间解: 3
2025-08-05 10:29:11,898 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 14, 8, 2, 17, 30, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 43, 15, 49, 19, 22, 29, 20, 16, 41, 6, 1, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 7, 40, 18, 44, 32], 'cur_cost': 10846.0, 'intermediate_solutions': [{'tour': [0, 16, 14, 8, 9, 7, 40, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 43, 15, 49, 29, 22, 30, 17, 21, 2, 20, 19, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 32, 41, 6, 1], 'cur_cost': 11480.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 16, 14, 8, 9, 7, 40, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 43, 42, 13, 12, 51, 10, 50, 11, 27, 26, 25, 46, 28, 29, 20, 2, 21, 17, 30, 22, 19, 49, 15, 32, 41, 6, 1], 'cur_cost': 12083.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [51, 0, 16, 14, 8, 9, 7, 40, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 21, 2, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 12, 13, 42, 32, 41, 6, 1], 'cur_cost': 13263.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:11,898 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 10846.00)
2025-08-05 10:29:11,898 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:11,898 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:11,898 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:11,901 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 10:29:11,902 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,902 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,902 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,902 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,903 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10310.0, 路径长度: 52, 收集中间解: 3
2025-08-05 10:29:11,903 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 7, 5, 12, 26, 27, 25, 46, 13, 51, 10, 50, 11, 24, 3, 14, 4, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 8, 9, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10310.0, 'intermediate_solutions': [{'tour': [0, 15, 3, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 24, 45, 43, 49, 19, 1, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 18, 44, 2, 16, 41, 6, 22, 32], 'cur_cost': 11834.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 3, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 24, 2, 44, 18, 40, 7, 8, 9, 42, 13, 12, 51, 10, 50, 11, 27, 26, 25, 46, 28, 29, 22, 19, 49, 43, 45, 16, 41, 6, 1, 32], 'cur_cost': 12165.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 3, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 49, 24, 45, 43, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 18, 44, 2, 16, 41, 6, 1, 32], 'cur_cost': 11907.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:11,903 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 10310.00)
2025-08-05 10:29:11,903 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:29:11,904 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:29:11,904 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:11,906 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 10:29:11,907 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,907 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,907 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,907 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,908 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11535.0, 路径长度: 52, 收集中间解: 3
2025-08-05 10:29:11,908 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 9, 18, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 44, 40, 7, 8, 2, 16, 41, 6, 1, 32], 'cur_cost': 11535.0, 'intermediate_solutions': [{'tour': [10, 23, 47, 21, 14, 43, 50, 38, 30, 29, 41, 18, 40, 34, 37, 3, 49, 15, 25, 24, 12, 19, 35, 36, 2, 31, 42, 4, 11, 45, 22, 17, 33, 5, 39, 9, 20, 16, 44, 7, 48, 8, 32, 0, 6, 1, 46, 27, 26, 13, 51, 28], 'cur_cost': 21746.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 23, 47, 21, 14, 43, 24, 38, 30, 29, 41, 18, 40, 34, 37, 3, 49, 15, 25, 50, 12, 19, 35, 36, 2, 31, 22, 45, 11, 4, 42, 17, 33, 5, 39, 9, 20, 16, 44, 7, 48, 8, 32, 0, 6, 1, 46, 27, 26, 13, 51, 28], 'cur_cost': 21178.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 23, 47, 21, 14, 43, 4, 24, 38, 30, 29, 41, 18, 40, 34, 37, 3, 49, 15, 25, 50, 12, 19, 35, 36, 2, 31, 42, 11, 45, 22, 17, 33, 5, 39, 9, 20, 16, 44, 7, 48, 8, 32, 0, 6, 1, 46, 27, 26, 13, 51, 28], 'cur_cost': 20904.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:11,908 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 11535.00)
2025-08-05 10:29:11,908 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:29:11,908 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:29:11,909 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:11,911 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 10:29:11,911 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,911 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,911 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,912 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,912 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10938.0, 路径长度: 52, 收集中间解: 3
2025-08-05 10:29:11,912 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 20, 18, 2, 17, 30, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 44, 16, 41, 6, 1, 32], 'cur_cost': 10938.0, 'intermediate_solutions': [{'tour': [20, 43, 4, 34, 37, 21, 2, 9, 18, 38, 42, 24, 35, 3, 11, 14, 7, 39, 17, 29, 19, 48, 33, 45, 28, 23, 5, 31, 51, 15, 49, 40, 32, 44, 16, 47, 50, 26, 12, 27, 41, 10, 13, 36, 30, 0, 22, 1, 8, 6, 46, 25], 'cur_cost': 24904.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [20, 43, 4, 34, 37, 21, 2, 9, 44, 32, 40, 49, 15, 41, 31, 5, 23, 28, 45, 33, 48, 19, 29, 17, 39, 7, 14, 11, 3, 35, 24, 42, 38, 18, 16, 47, 50, 26, 12, 27, 51, 10, 13, 36, 30, 0, 22, 1, 8, 6, 46, 25], 'cur_cost': 22131.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [20, 43, 4, 46, 34, 37, 21, 2, 9, 18, 38, 42, 24, 35, 3, 11, 14, 7, 39, 17, 29, 19, 48, 33, 45, 28, 23, 5, 31, 41, 15, 49, 40, 32, 44, 16, 47, 50, 26, 12, 27, 51, 10, 13, 36, 30, 0, 22, 1, 8, 6, 25], 'cur_cost': 23177.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:11,912 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 10938.00)
2025-08-05 10:29:11,912 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:11,913 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:11,913 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:11,914 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 52
2025-08-05 10:29:11,914 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,914 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,914 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,915 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,915 - ExplorationExpert - INFO - 探索路径生成完成，成本: 29400.0, 路径长度: 52, 收集中间解: 3
2025-08-05 10:29:11,915 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [2, 11, 27, 47, 51, 50, 6, 14, 15, 46, 19, 48, 41, 3, 1, 26, 18, 12, 16, 34, 29, 4, 22, 31, 20, 0, 35, 30, 38, 49, 43, 17, 33, 21, 28, 25, 39, 24, 5, 9, 7, 10, 37, 32, 13, 23, 44, 45, 36, 42, 40, 8], 'cur_cost': 29400.0, 'intermediate_solutions': [{'tour': [0, 21, 11, 4, 14, 5, 23, 47, 37, 39, 31, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 2, 18, 44, 36, 48, 20, 29, 28, 24, 3, 42, 9, 8, 7, 40, 16, 41, 6, 1, 46, 25, 26, 27, 12, 13, 51, 10, 50, 32], 'cur_cost': 11198.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 11, 4, 14, 5, 23, 47, 37, 39, 36, 38, 35, 34, 33, 51, 13, 12, 27, 26, 25, 46, 1, 6, 41, 16, 40, 7, 8, 9, 42, 3, 24, 28, 29, 20, 48, 31, 44, 18, 2, 17, 30, 22, 19, 49, 15, 45, 43, 10, 50, 32], 'cur_cost': 12226.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 11, 4, 14, 5, 23, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 2, 18, 44, 31, 48, 20, 29, 28, 24, 3, 42, 9, 8, 7, 40, 47, 16, 41, 6, 1, 46, 25, 26, 27, 12, 13, 51, 10, 50, 32], 'cur_cost': 11422.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:11,916 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 29400.00)
2025-08-05 10:29:11,916 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:11,916 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:11,916 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:11,918 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 10:29:11,918 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,918 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,918 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,918 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:11,919 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11565.0, 路径长度: 52, 收集中间解: 3
2025-08-05 10:29:11,919 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 13, 12, 7, 40, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 21, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 32, 42, 9, 8, 41, 6, 1], 'cur_cost': 11565.0, 'intermediate_solutions': [{'tour': [0, 18, 3, 11, 27, 26, 25, 46, 12, 13, 51, 10, 50, 24, 5, 4, 14, 23, 47, 9, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 40, 7, 37, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 11583.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 3, 11, 27, 26, 44, 31, 48, 21, 17, 30, 22, 19, 49, 15, 45, 43, 33, 34, 35, 38, 36, 39, 37, 47, 23, 14, 4, 5, 24, 50, 10, 51, 13, 12, 46, 25, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 11521.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 3, 11, 27, 26, 25, 46, 12, 13, 51, 10, 50, 24, 5, 4, 14, 23, 22, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 30, 17, 21, 48, 31, 44, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10657.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:11,919 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 11565.00)
2025-08-05 10:29:11,919 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:29:11,919 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:11,919 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:11,919 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 30625.0
2025-08-05 10:29:11,926 - ExploitationExpert - INFO - res_population_num: 1
2025-08-05 10:29:11,926 - ExploitationExpert - INFO - res_population_costs: [7542.0]
2025-08-05 10:29:11,927 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64)]
2025-08-05 10:29:11,927 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:11,927 - ExploitationExpert - INFO - populations: [{'tour': [0, 8, 2, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 7, 40, 18, 44, 16, 41, 6, 1, 32], 'cur_cost': 11300.0}, {'tour': array([13, 35, 41, 12,  0, 48, 32,  4, 25, 47, 44, 31,  5, 34, 21, 26, 36,
       43, 39, 28, 33, 23, 42, 49, 29, 11, 51, 22,  8, 38,  7, 27, 50, 14,
       20,  9, 17, 15, 45, 40,  3, 10, 18, 37,  6, 30, 16, 19, 24,  2, 46,
        1], dtype=int64), 'cur_cost': 30739.0}, {'tour': [0, 14, 8, 2, 17, 30, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 43, 15, 49, 19, 22, 29, 20, 16, 41, 6, 1, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 7, 40, 18, 44, 32], 'cur_cost': 10846.0}, {'tour': [0, 7, 5, 12, 26, 27, 25, 46, 13, 51, 10, 50, 11, 24, 3, 14, 4, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 8, 9, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10310.0}, {'tour': [0, 9, 18, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 44, 40, 7, 8, 2, 16, 41, 6, 1, 32], 'cur_cost': 11535.0}, {'tour': [0, 20, 18, 2, 17, 30, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 44, 16, 41, 6, 1, 32], 'cur_cost': 10938.0}, {'tour': [2, 11, 27, 47, 51, 50, 6, 14, 15, 46, 19, 48, 41, 3, 1, 26, 18, 12, 16, 34, 29, 4, 22, 31, 20, 0, 35, 30, 38, 49, 43, 17, 33, 21, 28, 25, 39, 24, 5, 9, 7, 10, 37, 32, 13, 23, 44, 45, 36, 42, 40, 8], 'cur_cost': 29400.0}, {'tour': [0, 13, 12, 7, 40, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 21, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 32, 42, 9, 8, 41, 6, 1], 'cur_cost': 11565.0}, {'tour': array([ 6, 20,  1, 35, 28, 32, 19, 17, 37, 29, 36, 49, 33, 11,  2, 24, 21,
       41, 47, 40,  5, 46, 48, 14, 45, 12, 51, 34, 23, 22, 18,  3, 31, 26,
       10,  8, 27, 15,  0,  7, 50,  4, 16, 44, 25, 30, 39,  9, 38, 13, 42,
       43], dtype=int64), 'cur_cost': 30625.0}, {'tour': [18, 28, 10, 40, 45, 46, 35, 41, 27, 33, 30, 15, 44, 23, 9, 31, 12, 2, 48, 51, 36, 5, 14, 21, 22, 11, 16, 42, 19, 13, 34, 3, 26, 38, 6, 43, 49, 39, 4, 47, 24, 0, 17, 25, 32, 37, 1, 50, 29, 20, 7, 8], 'cur_cost': 31928.0}]
2025-08-05 10:29:11,928 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:11,928 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 350, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 350, 'cache_hits': 0, 'similarity_calculations': 1812, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:11,929 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 6, 20,  1, 35, 28, 32, 19, 17, 37, 29, 36, 49, 33, 11,  2, 24, 21,
       41, 47, 40,  5, 46, 48, 14, 45, 12, 51, 34, 23, 22, 18,  3, 31, 26,
       10,  8, 27, 15,  0,  7, 50,  4, 16, 44, 25, 30, 39,  9, 38, 13, 42,
       43], dtype=int64), 'cur_cost': 30625.0, 'intermediate_solutions': [{'tour': array([23,  9, 43, 10, 42,  2, 49, 21,  1, 15, 14, 41, 45,  6, 20, 48, 37,
        3, 32,  0, 39, 27, 24, 28, 30, 33,  8, 40, 13, 11, 29, 17, 31, 22,
       47, 35, 26,  4, 36, 44, 18, 46,  5, 51, 12,  7, 25, 34, 38, 19, 50,
       16]), 'cur_cost': 28861.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([10, 23,  9, 43, 42,  2, 49, 21,  1, 15, 14, 41, 45,  6, 20, 48, 37,
        3, 32,  0, 39, 27, 24, 28, 30, 33,  8, 40, 13, 11, 29, 17, 31, 22,
       47, 35, 26,  4, 36, 44, 18, 46,  5, 51, 12,  7, 25, 34, 38, 19, 50,
       16]), 'cur_cost': 29154.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([42, 10, 23,  9, 43,  2, 49, 21,  1, 15, 14, 41, 45,  6, 20, 48, 37,
        3, 32,  0, 39, 27, 24, 28, 30, 33,  8, 40, 13, 11, 29, 17, 31, 22,
       47, 35, 26,  4, 36, 44, 18, 46,  5, 51, 12,  7, 25, 34, 38, 19, 50,
       16]), 'cur_cost': 28677.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([43, 10, 23,  9, 42,  2, 49, 21,  1, 15, 14, 41, 45,  6, 20, 48, 37,
        3, 32,  0, 39, 27, 24, 28, 30, 33,  8, 40, 13, 11, 29, 17, 31, 22,
       47, 35, 26,  4, 36, 44, 18, 46,  5, 51, 12,  7, 25, 34, 38, 19, 50,
       16]), 'cur_cost': 28406.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([43, 42, 10, 23,  9,  2, 49, 21,  1, 15, 14, 41, 45,  6, 20, 48, 37,
        3, 32,  0, 39, 27, 24, 28, 30, 33,  8, 40, 13, 11, 29, 17, 31, 22,
       47, 35, 26,  4, 36, 44, 18, 46,  5, 51, 12,  7, 25, 34, 38, 19, 50,
       16]), 'cur_cost': 28359.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:11,929 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 30625.00)
2025-08-05 10:29:11,929 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:29:11,930 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:11,930 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:11,930 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 28690.0
2025-08-05 10:29:11,935 - ExploitationExpert - INFO - res_population_num: 1
2025-08-05 10:29:11,935 - ExploitationExpert - INFO - res_population_costs: [7542.0]
2025-08-05 10:29:11,936 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64)]
2025-08-05 10:29:11,936 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:11,936 - ExploitationExpert - INFO - populations: [{'tour': [0, 8, 2, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 7, 40, 18, 44, 16, 41, 6, 1, 32], 'cur_cost': 11300.0}, {'tour': array([13, 35, 41, 12,  0, 48, 32,  4, 25, 47, 44, 31,  5, 34, 21, 26, 36,
       43, 39, 28, 33, 23, 42, 49, 29, 11, 51, 22,  8, 38,  7, 27, 50, 14,
       20,  9, 17, 15, 45, 40,  3, 10, 18, 37,  6, 30, 16, 19, 24,  2, 46,
        1], dtype=int64), 'cur_cost': 30739.0}, {'tour': [0, 14, 8, 2, 17, 30, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 43, 15, 49, 19, 22, 29, 20, 16, 41, 6, 1, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 7, 40, 18, 44, 32], 'cur_cost': 10846.0}, {'tour': [0, 7, 5, 12, 26, 27, 25, 46, 13, 51, 10, 50, 11, 24, 3, 14, 4, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 8, 9, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10310.0}, {'tour': [0, 9, 18, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 44, 40, 7, 8, 2, 16, 41, 6, 1, 32], 'cur_cost': 11535.0}, {'tour': [0, 20, 18, 2, 17, 30, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 44, 16, 41, 6, 1, 32], 'cur_cost': 10938.0}, {'tour': [2, 11, 27, 47, 51, 50, 6, 14, 15, 46, 19, 48, 41, 3, 1, 26, 18, 12, 16, 34, 29, 4, 22, 31, 20, 0, 35, 30, 38, 49, 43, 17, 33, 21, 28, 25, 39, 24, 5, 9, 7, 10, 37, 32, 13, 23, 44, 45, 36, 42, 40, 8], 'cur_cost': 29400.0}, {'tour': [0, 13, 12, 7, 40, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 21, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 32, 42, 9, 8, 41, 6, 1], 'cur_cost': 11565.0}, {'tour': array([ 6, 20,  1, 35, 28, 32, 19, 17, 37, 29, 36, 49, 33, 11,  2, 24, 21,
       41, 47, 40,  5, 46, 48, 14, 45, 12, 51, 34, 23, 22, 18,  3, 31, 26,
       10,  8, 27, 15,  0,  7, 50,  4, 16, 44, 25, 30, 39,  9, 38, 13, 42,
       43], dtype=int64), 'cur_cost': 30625.0}, {'tour': array([ 1, 16, 20,  3, 19,  4,  8, 43, 51, 34, 13, 25, 12, 23, 44,  6, 42,
       36, 18,  5, 45, 31, 22, 11, 32, 46, 33,  9,  2, 49, 21, 47, 24, 40,
       41, 14, 27, 39, 37,  7, 30, 17, 38, 50, 15, 29, 10, 28, 26,  0, 48,
       35], dtype=int64), 'cur_cost': 28690.0}]
2025-08-05 10:29:11,938 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:11,938 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 351, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 351, 'cache_hits': 0, 'similarity_calculations': 1824, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:11,939 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([ 1, 16, 20,  3, 19,  4,  8, 43, 51, 34, 13, 25, 12, 23, 44,  6, 42,
       36, 18,  5, 45, 31, 22, 11, 32, 46, 33,  9,  2, 49, 21, 47, 24, 40,
       41, 14, 27, 39, 37,  7, 30, 17, 38, 50, 15, 29, 10, 28, 26,  0, 48,
       35], dtype=int64), 'cur_cost': 28690.0, 'intermediate_solutions': [{'tour': array([10, 28, 18, 40, 45, 46, 35, 41, 27, 33, 30, 15, 44, 23,  9, 31, 12,
        2, 48, 51, 36,  5, 14, 21, 22, 11, 16, 42, 19, 13, 34,  3, 26, 38,
        6, 43, 49, 39,  4, 47, 24,  0, 17, 25, 32, 37,  1, 50, 29, 20,  7,
        8]), 'cur_cost': 31698.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([40, 10, 28, 18, 45, 46, 35, 41, 27, 33, 30, 15, 44, 23,  9, 31, 12,
        2, 48, 51, 36,  5, 14, 21, 22, 11, 16, 42, 19, 13, 34,  3, 26, 38,
        6, 43, 49, 39,  4, 47, 24,  0, 17, 25, 32, 37,  1, 50, 29, 20,  7,
        8]), 'cur_cost': 31770.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([45, 40, 10, 28, 18, 46, 35, 41, 27, 33, 30, 15, 44, 23,  9, 31, 12,
        2, 48, 51, 36,  5, 14, 21, 22, 11, 16, 42, 19, 13, 34,  3, 26, 38,
        6, 43, 49, 39,  4, 47, 24,  0, 17, 25, 32, 37,  1, 50, 29, 20,  7,
        8]), 'cur_cost': 32859.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([18, 40, 10, 28, 45, 46, 35, 41, 27, 33, 30, 15, 44, 23,  9, 31, 12,
        2, 48, 51, 36,  5, 14, 21, 22, 11, 16, 42, 19, 13, 34,  3, 26, 38,
        6, 43, 49, 39,  4, 47, 24,  0, 17, 25, 32, 37,  1, 50, 29, 20,  7,
        8]), 'cur_cost': 31065.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([18, 45, 40, 10, 28, 46, 35, 41, 27, 33, 30, 15, 44, 23,  9, 31, 12,
        2, 48, 51, 36,  5, 14, 21, 22, 11, 16, 42, 19, 13, 34,  3, 26, 38,
        6, 43, 49, 39,  4, 47, 24,  0, 17, 25, 32, 37,  1, 50, 29, 20,  7,
        8]), 'cur_cost': 31704.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:11,939 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 28690.00)
2025-08-05 10:29:11,939 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:11,939 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:11,941 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 2, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 7, 40, 18, 44, 16, 41, 6, 1, 32], 'cur_cost': 11300.0, 'intermediate_solutions': [{'tour': [28, 22, 31, 21, 36, 41, 23, 30, 15, 43, 2, 3, 47, 38, 29, 25, 33, 42, 20, 44, 49, 0, 11, 4, 14, 5, 45, 12, 8, 9, 27, 17, 10, 46, 24, 37, 32, 26, 19, 18, 1, 16, 39, 51, 13, 6, 50, 48, 40, 7, 34, 35], 'cur_cost': 27724.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [28, 22, 31, 21, 36, 41, 15, 30, 23, 43, 2, 3, 47, 38, 29, 25, 33, 42, 20, 44, 49, 0, 11, 4, 14, 5, 45, 12, 46, 10, 17, 27, 9, 8, 24, 37, 32, 26, 19, 18, 1, 16, 39, 51, 13, 6, 50, 48, 40, 7, 34, 35], 'cur_cost': 26761.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [28, 22, 31, 36, 41, 15, 30, 23, 43, 2, 3, 47, 38, 29, 25, 33, 42, 20, 44, 49, 0, 11, 4, 14, 5, 45, 12, 8, 9, 27, 17, 10, 21, 46, 24, 37, 32, 26, 19, 18, 1, 16, 39, 51, 13, 6, 50, 48, 40, 7, 34, 35], 'cur_cost': 28659.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([13, 35, 41, 12,  0, 48, 32,  4, 25, 47, 44, 31,  5, 34, 21, 26, 36,
       43, 39, 28, 33, 23, 42, 49, 29, 11, 51, 22,  8, 38,  7, 27, 50, 14,
       20,  9, 17, 15, 45, 40,  3, 10, 18, 37,  6, 30, 16, 19, 24,  2, 46,
        1], dtype=int64), 'cur_cost': 30739.0, 'intermediate_solutions': [{'tour': array([13, 44, 14, 49,  4,  3, 31, 28, 33,  1, 12, 35, 26,  0, 42, 27,  2,
        5, 50,  6, 38, 32, 25, 15, 23, 10, 19, 43, 40,  9,  8, 46, 48, 21,
       24, 34, 51, 39,  7, 22, 45, 18, 41, 30, 17, 29, 47, 37, 20, 11, 36,
       16]), 'cur_cost': 32151.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([49, 13, 44, 14,  4,  3, 31, 28, 33,  1, 12, 35, 26,  0, 42, 27,  2,
        5, 50,  6, 38, 32, 25, 15, 23, 10, 19, 43, 40,  9,  8, 46, 48, 21,
       24, 34, 51, 39,  7, 22, 45, 18, 41, 30, 17, 29, 47, 37, 20, 11, 36,
       16]), 'cur_cost': 31393.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 4, 49, 13, 44, 14,  3, 31, 28, 33,  1, 12, 35, 26,  0, 42, 27,  2,
        5, 50,  6, 38, 32, 25, 15, 23, 10, 19, 43, 40,  9,  8, 46, 48, 21,
       24, 34, 51, 39,  7, 22, 45, 18, 41, 30, 17, 29, 47, 37, 20, 11, 36,
       16]), 'cur_cost': 31907.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([14, 49, 13, 44,  4,  3, 31, 28, 33,  1, 12, 35, 26,  0, 42, 27,  2,
        5, 50,  6, 38, 32, 25, 15, 23, 10, 19, 43, 40,  9,  8, 46, 48, 21,
       24, 34, 51, 39,  7, 22, 45, 18, 41, 30, 17, 29, 47, 37, 20, 11, 36,
       16]), 'cur_cost': 31941.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([14,  4, 49, 13, 44,  3, 31, 28, 33,  1, 12, 35, 26,  0, 42, 27,  2,
        5, 50,  6, 38, 32, 25, 15, 23, 10, 19, 43, 40,  9,  8, 46, 48, 21,
       24, 34, 51, 39,  7, 22, 45, 18, 41, 30, 17, 29, 47, 37, 20, 11, 36,
       16]), 'cur_cost': 31923.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 8, 2, 17, 30, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 43, 15, 49, 19, 22, 29, 20, 16, 41, 6, 1, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 7, 40, 18, 44, 32], 'cur_cost': 10846.0, 'intermediate_solutions': [{'tour': [0, 16, 14, 8, 9, 7, 40, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 43, 15, 49, 29, 22, 30, 17, 21, 2, 20, 19, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 32, 41, 6, 1], 'cur_cost': 11480.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 16, 14, 8, 9, 7, 40, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 43, 42, 13, 12, 51, 10, 50, 11, 27, 26, 25, 46, 28, 29, 20, 2, 21, 17, 30, 22, 19, 49, 15, 32, 41, 6, 1], 'cur_cost': 12083.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [51, 0, 16, 14, 8, 9, 7, 40, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 21, 2, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 12, 13, 42, 32, 41, 6, 1], 'cur_cost': 13263.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 5, 12, 26, 27, 25, 46, 13, 51, 10, 50, 11, 24, 3, 14, 4, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 8, 9, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10310.0, 'intermediate_solutions': [{'tour': [0, 15, 3, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 24, 45, 43, 49, 19, 1, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 18, 44, 2, 16, 41, 6, 22, 32], 'cur_cost': 11834.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 3, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 24, 2, 44, 18, 40, 7, 8, 9, 42, 13, 12, 51, 10, 50, 11, 27, 26, 25, 46, 28, 29, 22, 19, 49, 43, 45, 16, 41, 6, 1, 32], 'cur_cost': 12165.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 3, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 49, 24, 45, 43, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 18, 44, 2, 16, 41, 6, 1, 32], 'cur_cost': 11907.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 18, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 44, 40, 7, 8, 2, 16, 41, 6, 1, 32], 'cur_cost': 11535.0, 'intermediate_solutions': [{'tour': [10, 23, 47, 21, 14, 43, 50, 38, 30, 29, 41, 18, 40, 34, 37, 3, 49, 15, 25, 24, 12, 19, 35, 36, 2, 31, 42, 4, 11, 45, 22, 17, 33, 5, 39, 9, 20, 16, 44, 7, 48, 8, 32, 0, 6, 1, 46, 27, 26, 13, 51, 28], 'cur_cost': 21746.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 23, 47, 21, 14, 43, 24, 38, 30, 29, 41, 18, 40, 34, 37, 3, 49, 15, 25, 50, 12, 19, 35, 36, 2, 31, 22, 45, 11, 4, 42, 17, 33, 5, 39, 9, 20, 16, 44, 7, 48, 8, 32, 0, 6, 1, 46, 27, 26, 13, 51, 28], 'cur_cost': 21178.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 23, 47, 21, 14, 43, 4, 24, 38, 30, 29, 41, 18, 40, 34, 37, 3, 49, 15, 25, 50, 12, 19, 35, 36, 2, 31, 42, 11, 45, 22, 17, 33, 5, 39, 9, 20, 16, 44, 7, 48, 8, 32, 0, 6, 1, 46, 27, 26, 13, 51, 28], 'cur_cost': 20904.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 18, 2, 17, 30, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 44, 16, 41, 6, 1, 32], 'cur_cost': 10938.0, 'intermediate_solutions': [{'tour': [20, 43, 4, 34, 37, 21, 2, 9, 18, 38, 42, 24, 35, 3, 11, 14, 7, 39, 17, 29, 19, 48, 33, 45, 28, 23, 5, 31, 51, 15, 49, 40, 32, 44, 16, 47, 50, 26, 12, 27, 41, 10, 13, 36, 30, 0, 22, 1, 8, 6, 46, 25], 'cur_cost': 24904.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [20, 43, 4, 34, 37, 21, 2, 9, 44, 32, 40, 49, 15, 41, 31, 5, 23, 28, 45, 33, 48, 19, 29, 17, 39, 7, 14, 11, 3, 35, 24, 42, 38, 18, 16, 47, 50, 26, 12, 27, 51, 10, 13, 36, 30, 0, 22, 1, 8, 6, 46, 25], 'cur_cost': 22131.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [20, 43, 4, 46, 34, 37, 21, 2, 9, 18, 38, 42, 24, 35, 3, 11, 14, 7, 39, 17, 29, 19, 48, 33, 45, 28, 23, 5, 31, 41, 15, 49, 40, 32, 44, 16, 47, 50, 26, 12, 27, 51, 10, 13, 36, 30, 0, 22, 1, 8, 6, 25], 'cur_cost': 23177.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [2, 11, 27, 47, 51, 50, 6, 14, 15, 46, 19, 48, 41, 3, 1, 26, 18, 12, 16, 34, 29, 4, 22, 31, 20, 0, 35, 30, 38, 49, 43, 17, 33, 21, 28, 25, 39, 24, 5, 9, 7, 10, 37, 32, 13, 23, 44, 45, 36, 42, 40, 8], 'cur_cost': 29400.0, 'intermediate_solutions': [{'tour': [0, 21, 11, 4, 14, 5, 23, 47, 37, 39, 31, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 2, 18, 44, 36, 48, 20, 29, 28, 24, 3, 42, 9, 8, 7, 40, 16, 41, 6, 1, 46, 25, 26, 27, 12, 13, 51, 10, 50, 32], 'cur_cost': 11198.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 11, 4, 14, 5, 23, 47, 37, 39, 36, 38, 35, 34, 33, 51, 13, 12, 27, 26, 25, 46, 1, 6, 41, 16, 40, 7, 8, 9, 42, 3, 24, 28, 29, 20, 48, 31, 44, 18, 2, 17, 30, 22, 19, 49, 15, 45, 43, 10, 50, 32], 'cur_cost': 12226.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 11, 4, 14, 5, 23, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 2, 18, 44, 31, 48, 20, 29, 28, 24, 3, 42, 9, 8, 7, 40, 47, 16, 41, 6, 1, 46, 25, 26, 27, 12, 13, 51, 10, 50, 32], 'cur_cost': 11422.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 12, 7, 40, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 21, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 32, 42, 9, 8, 41, 6, 1], 'cur_cost': 11565.0, 'intermediate_solutions': [{'tour': [0, 18, 3, 11, 27, 26, 25, 46, 12, 13, 51, 10, 50, 24, 5, 4, 14, 23, 47, 9, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 40, 7, 37, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 11583.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 3, 11, 27, 26, 44, 31, 48, 21, 17, 30, 22, 19, 49, 15, 45, 43, 33, 34, 35, 38, 36, 39, 37, 47, 23, 14, 4, 5, 24, 50, 10, 51, 13, 12, 46, 25, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 11521.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 3, 11, 27, 26, 25, 46, 12, 13, 51, 10, 50, 24, 5, 4, 14, 23, 22, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 30, 17, 21, 48, 31, 44, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10657.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 6, 20,  1, 35, 28, 32, 19, 17, 37, 29, 36, 49, 33, 11,  2, 24, 21,
       41, 47, 40,  5, 46, 48, 14, 45, 12, 51, 34, 23, 22, 18,  3, 31, 26,
       10,  8, 27, 15,  0,  7, 50,  4, 16, 44, 25, 30, 39,  9, 38, 13, 42,
       43], dtype=int64), 'cur_cost': 30625.0, 'intermediate_solutions': [{'tour': array([23,  9, 43, 10, 42,  2, 49, 21,  1, 15, 14, 41, 45,  6, 20, 48, 37,
        3, 32,  0, 39, 27, 24, 28, 30, 33,  8, 40, 13, 11, 29, 17, 31, 22,
       47, 35, 26,  4, 36, 44, 18, 46,  5, 51, 12,  7, 25, 34, 38, 19, 50,
       16]), 'cur_cost': 28861.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([10, 23,  9, 43, 42,  2, 49, 21,  1, 15, 14, 41, 45,  6, 20, 48, 37,
        3, 32,  0, 39, 27, 24, 28, 30, 33,  8, 40, 13, 11, 29, 17, 31, 22,
       47, 35, 26,  4, 36, 44, 18, 46,  5, 51, 12,  7, 25, 34, 38, 19, 50,
       16]), 'cur_cost': 29154.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([42, 10, 23,  9, 43,  2, 49, 21,  1, 15, 14, 41, 45,  6, 20, 48, 37,
        3, 32,  0, 39, 27, 24, 28, 30, 33,  8, 40, 13, 11, 29, 17, 31, 22,
       47, 35, 26,  4, 36, 44, 18, 46,  5, 51, 12,  7, 25, 34, 38, 19, 50,
       16]), 'cur_cost': 28677.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([43, 10, 23,  9, 42,  2, 49, 21,  1, 15, 14, 41, 45,  6, 20, 48, 37,
        3, 32,  0, 39, 27, 24, 28, 30, 33,  8, 40, 13, 11, 29, 17, 31, 22,
       47, 35, 26,  4, 36, 44, 18, 46,  5, 51, 12,  7, 25, 34, 38, 19, 50,
       16]), 'cur_cost': 28406.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([43, 42, 10, 23,  9,  2, 49, 21,  1, 15, 14, 41, 45,  6, 20, 48, 37,
        3, 32,  0, 39, 27, 24, 28, 30, 33,  8, 40, 13, 11, 29, 17, 31, 22,
       47, 35, 26,  4, 36, 44, 18, 46,  5, 51, 12,  7, 25, 34, 38, 19, 50,
       16]), 'cur_cost': 28359.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 1, 16, 20,  3, 19,  4,  8, 43, 51, 34, 13, 25, 12, 23, 44,  6, 42,
       36, 18,  5, 45, 31, 22, 11, 32, 46, 33,  9,  2, 49, 21, 47, 24, 40,
       41, 14, 27, 39, 37,  7, 30, 17, 38, 50, 15, 29, 10, 28, 26,  0, 48,
       35], dtype=int64), 'cur_cost': 28690.0, 'intermediate_solutions': [{'tour': array([10, 28, 18, 40, 45, 46, 35, 41, 27, 33, 30, 15, 44, 23,  9, 31, 12,
        2, 48, 51, 36,  5, 14, 21, 22, 11, 16, 42, 19, 13, 34,  3, 26, 38,
        6, 43, 49, 39,  4, 47, 24,  0, 17, 25, 32, 37,  1, 50, 29, 20,  7,
        8]), 'cur_cost': 31698.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([40, 10, 28, 18, 45, 46, 35, 41, 27, 33, 30, 15, 44, 23,  9, 31, 12,
        2, 48, 51, 36,  5, 14, 21, 22, 11, 16, 42, 19, 13, 34,  3, 26, 38,
        6, 43, 49, 39,  4, 47, 24,  0, 17, 25, 32, 37,  1, 50, 29, 20,  7,
        8]), 'cur_cost': 31770.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([45, 40, 10, 28, 18, 46, 35, 41, 27, 33, 30, 15, 44, 23,  9, 31, 12,
        2, 48, 51, 36,  5, 14, 21, 22, 11, 16, 42, 19, 13, 34,  3, 26, 38,
        6, 43, 49, 39,  4, 47, 24,  0, 17, 25, 32, 37,  1, 50, 29, 20,  7,
        8]), 'cur_cost': 32859.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([18, 40, 10, 28, 45, 46, 35, 41, 27, 33, 30, 15, 44, 23,  9, 31, 12,
        2, 48, 51, 36,  5, 14, 21, 22, 11, 16, 42, 19, 13, 34,  3, 26, 38,
        6, 43, 49, 39,  4, 47, 24,  0, 17, 25, 32, 37,  1, 50, 29, 20,  7,
        8]), 'cur_cost': 31065.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([18, 45, 40, 10, 28, 46, 35, 41, 27, 33, 30, 15, 44, 23,  9, 31, 12,
        2, 48, 51, 36,  5, 14, 21, 22, 11, 16, 42, 19, 13, 34,  3, 26, 38,
        6, 43, 49, 39,  4, 47, 24,  0, 17, 25, 32, 37,  1, 50, 29, 20,  7,
        8]), 'cur_cost': 31704.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:29:11,942 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:11,942 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:11,945 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10310.000, 多样性=0.870
2025-08-05 10:29:11,945 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 10:29:11,945 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 10:29:11,945 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:11,945 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.03018840543539954, 'best_improvement': -0.044579533941236066}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.11246730601569295}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.064583146527544, 'recent_improvements': [0.07579641579379091, -0.07753448356921665, -0.053369877261297084], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 1, 'new_count': 1, 'count_change': 0, 'old_best_cost': 7542.0, 'new_best_cost': 7542.0, 'quality_improvement': 0.0, 'old_diversity': 0.0, 'new_diversity': 0.0, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:29:11,945 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 10:29:11,948 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\berlin52_solution.json
2025-08-05 10:29:11,948 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\berlin52_20250805_102911.solution
2025-08-05 10:29:11,948 - __main__ - INFO - 实例执行完成 - 运行时间: 1.52s, 最佳成本: 7542.0
2025-08-05 10:29:11,949 - __main__ - INFO - 实例 berlin52 处理完成
