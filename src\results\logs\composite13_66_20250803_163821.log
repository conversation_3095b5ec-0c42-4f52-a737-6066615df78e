2025-08-03 16:38:21,142 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-08-03 16:38:21,143 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-03 16:38:21,146 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:38:21,164 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=9859.000, 多样性=0.972
2025-08-03 16:38:21,169 - PathExpert - INFO - 开始路径结构分析
2025-08-03 16:38:21,174 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.972
2025-08-03 16:38:21,177 - EliteExpert - INFO - 开始精英解分析
2025-08-03 16:38:21,187 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/1)
2025-08-03 16:38:21,188 - LandscapeExpert - INFO - 使用直接传入的种群数据: 20个个体
2025-08-03 16:38:21,188 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-03 16:38:21,189 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-03 16:38:21,455 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.150, 适应度梯度: -13429.950, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 1.000
2025-08-03 16:38:21,455 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-03 16:38:21,456 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-03 16:38:21,524 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记
2025-08-03 16:38:21,909 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_1_20250803_163821.html
2025-08-03 16:38:21,961 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_1_20250803_163821.html
2025-08-03 16:38:21,962 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-03 16:38:21,962 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-03 16:38:21,963 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.7759秒
2025-08-03 16:38:21,963 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-03 16:38:21,964 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.15, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -13429.949999999997, 'local_optima_density': 0.15, 'gradient_variance': 4007776429.9755006, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.002, 'fitness_entropy': 0.9372305632161296, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -13429.950)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 1, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754210301.4557853, 'performance_metrics': {}}}
2025-08-03 16:38:21,965 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-03 16:38:21,966 - experts.strategy.enhanced_strategy_expert - INFO - 开始增强策略分配分析 (迭代 0)
2025-08-03 16:38:21,966 - experts.analysis.individual_state_analyzer - INFO - 开始分析种群状态 (迭代 0, 种群大小: 20)
2025-08-03 16:38:21,967 - experts.strategy.enhanced_strategy_expert - ERROR - 策略分配过程中发生错误: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:38:21,967 - experts.strategy.enhanced_strategy_expert - WARNING - 使用回退策略分配
2025-08-03 16:38:21,968 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 16:38:21,968 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:38:21,969 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-03 16:38:21,969 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 16:38:21,969 - __main__ - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:38:21,969 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-03 16:38:21,969 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2, 3, 4} (总数: 4, 保护比例: 0.20)
2025-08-03 16:38:21,970 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-03 16:38:21,970 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-03 16:38:21,971 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:38:21,988 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:38:21,989 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:38:22,177 - ExplorationExpert - INFO - 探索路径生成完成，成本: 61220.0, 路径长度: 66
2025-08-03 16:38:22,177 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [50, 22, 30, 12, 11, 54, 5, 14, 2, 60, 4, 8, 17, 20, 16, 3, 15, 37, 13, 24, 29, 19, 6, 55, 40, 18, 49, 44, 43, 28, 10, 56, 9, 58, 65, 7, 53, 0, 52, 57, 64, 21, 27, 33, 25, 34, 46, 47, 51, 38, 45, 23, 32, 36, 31, 35, 48, 42, 26, 41, 1, 63, 61, 59, 62, 39], 'cur_cost': 61220.0}
2025-08-03 16:38:22,178 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 61220.00)
2025-08-03 16:38:22,178 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-03 16:38:22,179 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:38:22,186 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:38:22,188 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 111023.0
2025-08-03 16:38:24,258 - ExploitationExpert - INFO - res_population_num: 1
2025-08-03 16:38:24,258 - ExploitationExpert - INFO - res_population_costs: [9787.0]
2025-08-03 16:38:24,258 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 15, 14, 20, 21, 13, 19, 17, 12, 22, 23, 16,
       18, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 30, 28, 35, 34, 42, 48,
       43, 40, 49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57,
       64, 60, 58, 56, 59, 62, 53, 61, 55,  5,  4,  6,  2,  8, 10],
      dtype=int64)]
2025-08-03 16:38:24,259 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:38:24,260 - ExploitationExpert - INFO - populations: [{'tour': [50, 22, 30, 12, 11, 54, 5, 14, 2, 60, 4, 8, 17, 20, 16, 3, 15, 37, 13, 24, 29, 19, 6, 55, 40, 18, 49, 44, 43, 28, 10, 56, 9, 58, 65, 7, 53, 0, 52, 57, 64, 21, 27, 33, 25, 34, 46, 47, 51, 38, 45, 23, 32, 36, 31, 35, 48, 42, 26, 41, 1, 63, 61, 59, 62, 39], 'cur_cost': 61220.0}, {'tour': array([40,  5, 34, 52, 43, 26, 58, 27, 11, 47, 48,  0, 44,  7, 33, 54, 15,
       20, 53, 23, 32, 42, 16, 14, 65,  2, 21, 57, 31, 46,  1, 22,  4, 36,
       45, 39, 64, 63, 12, 18, 49,  6, 24, 41,  3, 10, 13, 56, 29, 60, 38,
       30, 17, 19, 50, 51, 61, 62,  8, 28, 35, 25, 37, 55, 59,  9],
      dtype=int64), 'cur_cost': 111023.0}, {'tour': array([35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 36, 27, 18, 16, 23,
       22, 12, 17, 15, 14, 20, 21, 13, 19,  9, 11,  7,  3,  1,  0, 10,  8,
        2,  6,  4,  5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9859.0}, {'tour': array([44, 45, 38, 51, 50, 41, 39, 47, 49, 40, 43, 48, 46, 42, 21, 20, 13,
       23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30,
       34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,  2,  6, 10,
        0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63],
      dtype=int64), 'cur_cost': 9946.0}, {'tour': array([38, 51, 50, 45, 44, 41, 39, 47, 49, 40, 43, 48, 46, 42, 21, 20, 13,
       23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30,
       34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,  2,  6, 10,
        0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63],
      dtype=int64), 'cur_cost': 9956.0}, {'tour': array([62, 59, 56, 58, 60, 64, 53, 65, 52, 63, 61, 55, 57, 54,  2,  8,  5,
        4,  6,  9, 11,  7,  3,  1,  0, 10, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29,
       32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42],
      dtype=int64), 'cur_cost': 10158.0}, {'tour': array([32, 37,  5,  3, 17, 12,  9, 53, 49, 45, 44, 14, 51,  7,  6, 63, 20,
       43, 10, 55, 59, 39, 58, 24, 18, 38, 40, 62, 15, 30, 56, 26, 46, 42,
       21, 34, 31, 29, 23, 13, 33, 64, 11, 41, 61, 36, 35,  2, 27, 57, 28,
       52, 47,  1, 16, 50,  0, 54,  8, 25, 22, 48, 60,  4, 65, 19],
      dtype=int64), 'cur_cost': 102728.0}, {'tour': array([34, 11, 64,  7, 26, 49,  5, 58, 28,  6, 44,  0,  3, 60,  2, 20, 30,
       17,  8, 36, 51, 10, 23, 53, 22, 25, 56, 18, 55, 15, 45, 13, 63, 37,
       38, 33, 24, 16, 40, 27,  1, 12, 59, 43,  9, 50, 46, 57, 62, 48, 42,
        4, 52, 21, 14, 41, 32, 19, 29, 65, 54, 47, 31, 61, 35, 39],
      dtype=int64), 'cur_cost': 115577.0}, {'tour': array([ 6, 16,  4, 11, 51, 13,  8, 57, 41, 53, 56, 65, 23, 28, 22,  0, 33,
       64, 10, 40, 54, 43, 52, 38, 39, 19, 30,  7,  3, 60, 62, 15, 12, 45,
       46, 26, 35, 32, 49,  9, 21, 47, 58, 55, 14, 36, 17, 20, 29, 61, 18,
       48, 50, 27, 42, 25,  2,  1, 59, 24, 44, 31, 63, 37, 34,  5],
      dtype=int64), 'cur_cost': 98081.0}, {'tour': array([50, 46, 41, 53, 55, 45,  3, 19, 25, 23,  0, 43, 35, 58, 14, 61, 22,
       44, 42, 31, 29, 65, 63, 20, 54, 24,  7, 36,  1, 52,  8, 18, 30, 37,
        5, 48, 34, 13, 51, 47,  9,  4, 57, 64, 21, 40, 10, 17, 38,  6, 49,
       16,  2, 28, 39, 62, 26, 11, 59, 60, 15, 12, 32, 27, 33, 56],
      dtype=int64), 'cur_cost': 101324.0}, {'tour': array([22, 39,  3,  8, 30, 25, 20,  4, 17, 41, 60, 32, 54,  0, 11, 10, 43,
       61, 37, 38, 15, 58, 46,  1, 47, 55,  7, 31, 12, 48, 36, 45, 64, 51,
        2, 50, 35, 40, 63, 52, 59, 56, 44, 28, 29, 53,  6, 62, 14, 26, 18,
       42, 23, 21, 27,  5, 24, 16, 33, 65, 57, 19, 49, 34,  9, 13],
      dtype=int64), 'cur_cost': 109461.0}, {'tour': array([ 2, 16, 37, 50, 65, 55,  4, 43, 40, 52, 13, 53, 24, 30,  3,  0, 10,
        8, 21, 63, 59, 42, 51, 20,  5, 41, 56, 58,  6, 17, 39, 49, 62,  9,
       12, 60, 48, 27, 46, 61, 33, 15, 19, 44, 31, 35, 28, 23, 25,  7, 54,
       11, 36, 38, 57, 26, 14, 29, 34, 47, 22, 32, 18,  1, 45, 64],
      dtype=int64), 'cur_cost': 98068.0}, {'tour': array([12, 15, 38,  9, 47,  8, 54, 36, 29, 45,  5, 64, 62, 53, 14, 18, 42,
       40, 58,  7, 28, 41,  2, 44, 49,  3, 30, 16, 48, 51, 56, 46, 23, 21,
       52, 22, 37, 63, 25, 19, 33, 39, 26, 43, 27,  1, 60, 24, 13, 31, 11,
       50, 17, 61, 34, 32, 59,  4, 57, 55,  0, 35,  6, 20, 65, 10],
      dtype=int64), 'cur_cost': 106186.0}, {'tour': array([10, 50,  0, 52, 28, 44, 34, 35, 48, 11, 15, 24, 46, 62, 53, 36, 59,
       12, 19,  4, 29, 18, 47, 39,  2, 41, 32, 21,  9, 26,  6, 56, 58, 65,
       42, 64,  5,  7, 61, 55, 14,  8, 16, 30, 54, 22, 23, 33, 63, 43,  3,
       37, 45, 20, 13, 17, 27, 60, 38, 25, 40,  1, 51, 57, 49, 31],
      dtype=int64), 'cur_cost': 113377.0}, {'tour': array([10, 58, 11,  2,  9, 21, 34, 50, 52, 43,  7, 25, 16, 13, 62,  3, 41,
       48, 35,  8, 56,  1, 47, 60, 54, 14, 36, 40, 30, 65, 18, 46, 53, 55,
       61, 17, 38, 19, 29,  0, 39, 33, 42, 51, 27,  6, 63, 26, 22, 32, 12,
       64, 23, 45, 15, 31, 37, 59,  4,  5, 57, 44, 28, 49, 24, 20],
      dtype=int64), 'cur_cost': 103181.0}, {'tour': array([38, 27, 16,  5, 43, 26, 41, 47, 40, 45, 17, 23, 32, 35, 64, 19, 31,
       28, 22, 39, 58, 30, 49, 52,  1, 36, 44, 54, 24, 25,  0, 53, 57, 46,
       56, 12, 13, 33, 42, 10, 55,  8,  9, 14, 11,  3, 15, 65, 62, 18, 51,
       29, 21,  2, 20, 50,  6, 37,  7, 60, 48,  4, 59, 34, 63, 61],
      dtype=int64), 'cur_cost': 102164.0}, {'tour': array([44, 32, 38, 47, 57, 17, 25, 52, 41, 37, 26,  0, 48, 53, 58, 10,  5,
       27, 15, 29, 55,  8, 49,  4, 56, 34, 35, 23, 63, 36, 59,  9, 14, 28,
       65, 61,  1, 64, 11, 18, 51, 62, 40, 45, 20, 60, 50, 43, 21, 39, 16,
       33, 12, 54,  2,  7, 42, 22, 19,  6, 31, 30, 13, 24, 46,  3],
      dtype=int64), 'cur_cost': 103814.0}, {'tour': array([ 4, 11, 43, 45, 23, 27, 40, 63,  1, 60, 35,  0, 25, 64, 32, 22, 38,
        2,  9, 36, 21, 48, 62, 37, 44, 39, 33, 59, 31, 55, 20, 13, 12,  6,
       65, 24,  3,  5, 53, 58, 51, 16, 49, 19, 15, 47,  8,  7, 30, 34, 57,
       61, 26, 29, 41, 18, 42, 50, 54, 56, 17, 28, 10, 14, 46, 52],
      dtype=int64), 'cur_cost': 102693.0}, {'tour': array([ 7, 34, 14, 61, 50, 49, 12, 46, 23, 35, 58, 11, 52,  9, 42, 54, 26,
       37,  6, 43, 56, 22, 15, 17, 57, 21, 41, 13, 10, 44, 20, 19,  5, 36,
       27, 45, 16, 25, 64, 63,  2, 18,  4, 59, 48, 53, 65,  1, 60, 24, 62,
       39, 40,  0, 55,  8,  3, 51, 31, 32, 28, 29, 33, 47, 30, 38],
      dtype=int64), 'cur_cost': 99450.0}, {'tour': array([55,  5, 51, 17, 10, 11,  1, 34, 36, 28, 64, 14,  3, 63, 25, 27, 32,
       54,  7, 37, 47, 33, 42, 35, 21, 31, 49,  6, 50, 44, 53, 58, 43, 30,
       62, 60, 38, 13, 56, 45, 16, 20, 59,  0, 40, 57,  4, 12, 23, 22, 52,
       18, 46,  9, 29,  2, 19,  8, 39, 41, 65, 24, 26, 48, 15, 61],
      dtype=int64), 'cur_cost': 102732.0}]
2025-08-03 16:38:24,271 - ExploitationExpert - INFO - 局部搜索耗时: 2.08秒
2025-08-03 16:38:24,271 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-03 16:38:24,271 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([40,  5, 34, 52, 43, 26, 58, 27, 11, 47, 48,  0, 44,  7, 33, 54, 15,
       20, 53, 23, 32, 42, 16, 14, 65,  2, 21, 57, 31, 46,  1, 22,  4, 36,
       45, 39, 64, 63, 12, 18, 49,  6, 24, 41,  3, 10, 13, 56, 29, 60, 38,
       30, 17, 19, 50, 51, 61, 62,  8, 28, 35, 25, 37, 55, 59,  9],
      dtype=int64), 'cur_cost': 111023.0}
2025-08-03 16:38:24,272 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 111023.00)
2025-08-03 16:38:24,272 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-03 16:38:24,272 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-03 16:38:24,273 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:38:24,277 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:38:24,278 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:38:24,278 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12868.0, 路径长度: 66
2025-08-03 16:38:24,279 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 2, 16, 8, 5, 4, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12868.0}
2025-08-03 16:38:24,279 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 12868.00)
2025-08-03 16:38:24,280 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-03 16:38:24,280 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-03 16:38:24,281 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:38:24,289 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:38:24,290 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:38:24,290 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12439.0, 路径长度: 66
2025-08-03 16:38:24,291 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 9, 21, 17, 12, 22, 23, 16, 18, 19, 13, 20, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12439.0}
2025-08-03 16:38:24,291 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 12439.00)
2025-08-03 16:38:24,291 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-03 16:38:24,291 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:38:24,292 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:38:24,292 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 96684.0
2025-08-03 16:38:26,660 - ExploitationExpert - INFO - res_population_num: 2
2025-08-03 16:38:26,660 - ExploitationExpert - INFO - res_population_costs: [9787.0, 9607.0]
2025-08-03 16:38:26,661 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 15, 14, 20, 21, 13, 19, 17, 12, 22, 23, 16,
       18, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 30, 28, 35, 34, 42, 48,
       43, 40, 49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57,
       64, 60, 58, 56, 59, 62, 53, 61, 55,  5,  4,  6,  2,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 41, 38, 51, 50, 45, 44, 39, 47, 49, 48, 46,
       42, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:38:26,663 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:38:26,663 - ExploitationExpert - INFO - populations: [{'tour': [50, 22, 30, 12, 11, 54, 5, 14, 2, 60, 4, 8, 17, 20, 16, 3, 15, 37, 13, 24, 29, 19, 6, 55, 40, 18, 49, 44, 43, 28, 10, 56, 9, 58, 65, 7, 53, 0, 52, 57, 64, 21, 27, 33, 25, 34, 46, 47, 51, 38, 45, 23, 32, 36, 31, 35, 48, 42, 26, 41, 1, 63, 61, 59, 62, 39], 'cur_cost': 61220.0}, {'tour': array([40,  5, 34, 52, 43, 26, 58, 27, 11, 47, 48,  0, 44,  7, 33, 54, 15,
       20, 53, 23, 32, 42, 16, 14, 65,  2, 21, 57, 31, 46,  1, 22,  4, 36,
       45, 39, 64, 63, 12, 18, 49,  6, 24, 41,  3, 10, 13, 56, 29, 60, 38,
       30, 17, 19, 50, 51, 61, 62,  8, 28, 35, 25, 37, 55, 59,  9],
      dtype=int64), 'cur_cost': 111023.0}, {'tour': [0, 2, 16, 8, 5, 4, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12868.0}, {'tour': [0, 9, 21, 17, 12, 22, 23, 16, 18, 19, 13, 20, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12439.0}, {'tour': array([23, 24,  2, 17,  8, 44, 40, 38, 12, 62, 29, 47, 25, 53, 46, 39, 13,
       65, 26, 60, 63, 45, 19, 10, 54,  6, 59, 61, 16, 32,  1, 30, 37, 34,
       35,  0, 21, 42,  5, 55, 52, 58, 56, 48, 31,  7, 18,  4, 22, 27, 28,
       36, 11, 50, 15, 43, 64,  9, 49, 14, 20, 51,  3, 57, 41, 33],
      dtype=int64), 'cur_cost': 96684.0}, {'tour': array([62, 59, 56, 58, 60, 64, 53, 65, 52, 63, 61, 55, 57, 54,  2,  8,  5,
        4,  6,  9, 11,  7,  3,  1,  0, 10, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29,
       32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42],
      dtype=int64), 'cur_cost': 10158.0}, {'tour': array([32, 37,  5,  3, 17, 12,  9, 53, 49, 45, 44, 14, 51,  7,  6, 63, 20,
       43, 10, 55, 59, 39, 58, 24, 18, 38, 40, 62, 15, 30, 56, 26, 46, 42,
       21, 34, 31, 29, 23, 13, 33, 64, 11, 41, 61, 36, 35,  2, 27, 57, 28,
       52, 47,  1, 16, 50,  0, 54,  8, 25, 22, 48, 60,  4, 65, 19],
      dtype=int64), 'cur_cost': 102728.0}, {'tour': array([34, 11, 64,  7, 26, 49,  5, 58, 28,  6, 44,  0,  3, 60,  2, 20, 30,
       17,  8, 36, 51, 10, 23, 53, 22, 25, 56, 18, 55, 15, 45, 13, 63, 37,
       38, 33, 24, 16, 40, 27,  1, 12, 59, 43,  9, 50, 46, 57, 62, 48, 42,
        4, 52, 21, 14, 41, 32, 19, 29, 65, 54, 47, 31, 61, 35, 39],
      dtype=int64), 'cur_cost': 115577.0}, {'tour': array([ 6, 16,  4, 11, 51, 13,  8, 57, 41, 53, 56, 65, 23, 28, 22,  0, 33,
       64, 10, 40, 54, 43, 52, 38, 39, 19, 30,  7,  3, 60, 62, 15, 12, 45,
       46, 26, 35, 32, 49,  9, 21, 47, 58, 55, 14, 36, 17, 20, 29, 61, 18,
       48, 50, 27, 42, 25,  2,  1, 59, 24, 44, 31, 63, 37, 34,  5],
      dtype=int64), 'cur_cost': 98081.0}, {'tour': array([50, 46, 41, 53, 55, 45,  3, 19, 25, 23,  0, 43, 35, 58, 14, 61, 22,
       44, 42, 31, 29, 65, 63, 20, 54, 24,  7, 36,  1, 52,  8, 18, 30, 37,
        5, 48, 34, 13, 51, 47,  9,  4, 57, 64, 21, 40, 10, 17, 38,  6, 49,
       16,  2, 28, 39, 62, 26, 11, 59, 60, 15, 12, 32, 27, 33, 56],
      dtype=int64), 'cur_cost': 101324.0}, {'tour': array([22, 39,  3,  8, 30, 25, 20,  4, 17, 41, 60, 32, 54,  0, 11, 10, 43,
       61, 37, 38, 15, 58, 46,  1, 47, 55,  7, 31, 12, 48, 36, 45, 64, 51,
        2, 50, 35, 40, 63, 52, 59, 56, 44, 28, 29, 53,  6, 62, 14, 26, 18,
       42, 23, 21, 27,  5, 24, 16, 33, 65, 57, 19, 49, 34,  9, 13],
      dtype=int64), 'cur_cost': 109461.0}, {'tour': array([ 2, 16, 37, 50, 65, 55,  4, 43, 40, 52, 13, 53, 24, 30,  3,  0, 10,
        8, 21, 63, 59, 42, 51, 20,  5, 41, 56, 58,  6, 17, 39, 49, 62,  9,
       12, 60, 48, 27, 46, 61, 33, 15, 19, 44, 31, 35, 28, 23, 25,  7, 54,
       11, 36, 38, 57, 26, 14, 29, 34, 47, 22, 32, 18,  1, 45, 64],
      dtype=int64), 'cur_cost': 98068.0}, {'tour': array([12, 15, 38,  9, 47,  8, 54, 36, 29, 45,  5, 64, 62, 53, 14, 18, 42,
       40, 58,  7, 28, 41,  2, 44, 49,  3, 30, 16, 48, 51, 56, 46, 23, 21,
       52, 22, 37, 63, 25, 19, 33, 39, 26, 43, 27,  1, 60, 24, 13, 31, 11,
       50, 17, 61, 34, 32, 59,  4, 57, 55,  0, 35,  6, 20, 65, 10],
      dtype=int64), 'cur_cost': 106186.0}, {'tour': array([10, 50,  0, 52, 28, 44, 34, 35, 48, 11, 15, 24, 46, 62, 53, 36, 59,
       12, 19,  4, 29, 18, 47, 39,  2, 41, 32, 21,  9, 26,  6, 56, 58, 65,
       42, 64,  5,  7, 61, 55, 14,  8, 16, 30, 54, 22, 23, 33, 63, 43,  3,
       37, 45, 20, 13, 17, 27, 60, 38, 25, 40,  1, 51, 57, 49, 31],
      dtype=int64), 'cur_cost': 113377.0}, {'tour': array([10, 58, 11,  2,  9, 21, 34, 50, 52, 43,  7, 25, 16, 13, 62,  3, 41,
       48, 35,  8, 56,  1, 47, 60, 54, 14, 36, 40, 30, 65, 18, 46, 53, 55,
       61, 17, 38, 19, 29,  0, 39, 33, 42, 51, 27,  6, 63, 26, 22, 32, 12,
       64, 23, 45, 15, 31, 37, 59,  4,  5, 57, 44, 28, 49, 24, 20],
      dtype=int64), 'cur_cost': 103181.0}, {'tour': array([38, 27, 16,  5, 43, 26, 41, 47, 40, 45, 17, 23, 32, 35, 64, 19, 31,
       28, 22, 39, 58, 30, 49, 52,  1, 36, 44, 54, 24, 25,  0, 53, 57, 46,
       56, 12, 13, 33, 42, 10, 55,  8,  9, 14, 11,  3, 15, 65, 62, 18, 51,
       29, 21,  2, 20, 50,  6, 37,  7, 60, 48,  4, 59, 34, 63, 61],
      dtype=int64), 'cur_cost': 102164.0}, {'tour': array([44, 32, 38, 47, 57, 17, 25, 52, 41, 37, 26,  0, 48, 53, 58, 10,  5,
       27, 15, 29, 55,  8, 49,  4, 56, 34, 35, 23, 63, 36, 59,  9, 14, 28,
       65, 61,  1, 64, 11, 18, 51, 62, 40, 45, 20, 60, 50, 43, 21, 39, 16,
       33, 12, 54,  2,  7, 42, 22, 19,  6, 31, 30, 13, 24, 46,  3],
      dtype=int64), 'cur_cost': 103814.0}, {'tour': array([ 4, 11, 43, 45, 23, 27, 40, 63,  1, 60, 35,  0, 25, 64, 32, 22, 38,
        2,  9, 36, 21, 48, 62, 37, 44, 39, 33, 59, 31, 55, 20, 13, 12,  6,
       65, 24,  3,  5, 53, 58, 51, 16, 49, 19, 15, 47,  8,  7, 30, 34, 57,
       61, 26, 29, 41, 18, 42, 50, 54, 56, 17, 28, 10, 14, 46, 52],
      dtype=int64), 'cur_cost': 102693.0}, {'tour': array([ 7, 34, 14, 61, 50, 49, 12, 46, 23, 35, 58, 11, 52,  9, 42, 54, 26,
       37,  6, 43, 56, 22, 15, 17, 57, 21, 41, 13, 10, 44, 20, 19,  5, 36,
       27, 45, 16, 25, 64, 63,  2, 18,  4, 59, 48, 53, 65,  1, 60, 24, 62,
       39, 40,  0, 55,  8,  3, 51, 31, 32, 28, 29, 33, 47, 30, 38],
      dtype=int64), 'cur_cost': 99450.0}, {'tour': array([55,  5, 51, 17, 10, 11,  1, 34, 36, 28, 64, 14,  3, 63, 25, 27, 32,
       54,  7, 37, 47, 33, 42, 35, 21, 31, 49,  6, 50, 44, 53, 58, 43, 30,
       62, 60, 38, 13, 56, 45, 16, 20, 59,  0, 40, 57,  4, 12, 23, 22, 52,
       18, 46,  9, 29,  2, 19,  8, 39, 41, 65, 24, 26, 48, 15, 61],
      dtype=int64), 'cur_cost': 102732.0}]
2025-08-03 16:38:26,672 - ExploitationExpert - INFO - 局部搜索耗时: 2.38秒
2025-08-03 16:38:26,672 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-03 16:38:26,673 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([23, 24,  2, 17,  8, 44, 40, 38, 12, 62, 29, 47, 25, 53, 46, 39, 13,
       65, 26, 60, 63, 45, 19, 10, 54,  6, 59, 61, 16, 32,  1, 30, 37, 34,
       35,  0, 21, 42,  5, 55, 52, 58, 56, 48, 31,  7, 18,  4, 22, 27, 28,
       36, 11, 50, 15, 43, 64,  9, 49, 14, 20, 51,  3, 57, 41, 33],
      dtype=int64), 'cur_cost': 96684.0}
2025-08-03 16:38:26,674 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 96684.00)
2025-08-03 16:38:26,674 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-03 16:38:26,674 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-03 16:38:26,675 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:38:26,684 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:38:26,685 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:38:26,685 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12503.0, 路径长度: 66
2025-08-03 16:38:26,686 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 19, 15, 20, 21, 13, 23, 16, 18, 12, 22, 17, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12503.0}
2025-08-03 16:38:26,687 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 12503.00)
2025-08-03 16:38:26,687 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-03 16:38:26,688 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-03 16:38:26,688 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:38:26,692 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:38:26,693 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:38:26,693 - ExplorationExpert - INFO - 探索路径生成完成，成本: 94392.0, 路径长度: 66
2025-08-03 16:38:26,693 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [2, 15, 11, 54, 19, 20, 55, 61, 26, 13, 33, 24, 18, 44, 7, 17, 53, 57, 64, 27, 25, 47, 31, 38, 49, 41, 1, 63, 22, 40, 23, 0, 45, 6, 12, 5, 50, 60, 4, 48, 65, 35, 42, 59, 28, 37, 8, 3, 52, 62, 9, 21, 10, 16, 36, 30, 56, 46, 43, 51, 39, 32, 14, 34, 29, 58], 'cur_cost': 94392.0}
2025-08-03 16:38:26,694 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 94392.00)
2025-08-03 16:38:26,694 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-03 16:38:26,695 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:38:26,695 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:38:26,695 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 117619.0
2025-08-03 16:38:27,293 - ExploitationExpert - INFO - res_population_num: 4
2025-08-03 16:38:27,293 - ExploitationExpert - INFO - res_population_costs: [9787.0, 9607.0, 9578.0, 9578]
2025-08-03 16:38:27,294 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 15, 14, 20, 21, 13, 19, 17, 12, 22, 23, 16,
       18, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 30, 28, 35, 34, 42, 48,
       43, 40, 49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57,
       64, 60, 58, 56, 59, 62, 53, 61, 55,  5,  4,  6,  2,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 41, 38, 51, 50, 45, 44, 39, 47, 49, 48, 46,
       42, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       30, 28, 35, 25, 26, 36, 37, 31, 33, 32, 29, 24, 27, 19, 18, 16, 23,
       13, 21, 20, 14, 15, 22, 12, 17,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       30, 28, 35, 25, 26, 36, 37, 31, 33, 32, 29, 24, 27, 19, 16, 18, 23,
       13, 21, 20, 14, 15, 22, 12, 17,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:38:27,297 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:38:27,297 - ExploitationExpert - INFO - populations: [{'tour': [50, 22, 30, 12, 11, 54, 5, 14, 2, 60, 4, 8, 17, 20, 16, 3, 15, 37, 13, 24, 29, 19, 6, 55, 40, 18, 49, 44, 43, 28, 10, 56, 9, 58, 65, 7, 53, 0, 52, 57, 64, 21, 27, 33, 25, 34, 46, 47, 51, 38, 45, 23, 32, 36, 31, 35, 48, 42, 26, 41, 1, 63, 61, 59, 62, 39], 'cur_cost': 61220.0}, {'tour': array([40,  5, 34, 52, 43, 26, 58, 27, 11, 47, 48,  0, 44,  7, 33, 54, 15,
       20, 53, 23, 32, 42, 16, 14, 65,  2, 21, 57, 31, 46,  1, 22,  4, 36,
       45, 39, 64, 63, 12, 18, 49,  6, 24, 41,  3, 10, 13, 56, 29, 60, 38,
       30, 17, 19, 50, 51, 61, 62,  8, 28, 35, 25, 37, 55, 59,  9],
      dtype=int64), 'cur_cost': 111023.0}, {'tour': [0, 2, 16, 8, 5, 4, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12868.0}, {'tour': [0, 9, 21, 17, 12, 22, 23, 16, 18, 19, 13, 20, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12439.0}, {'tour': array([23, 24,  2, 17,  8, 44, 40, 38, 12, 62, 29, 47, 25, 53, 46, 39, 13,
       65, 26, 60, 63, 45, 19, 10, 54,  6, 59, 61, 16, 32,  1, 30, 37, 34,
       35,  0, 21, 42,  5, 55, 52, 58, 56, 48, 31,  7, 18,  4, 22, 27, 28,
       36, 11, 50, 15, 43, 64,  9, 49, 14, 20, 51,  3, 57, 41, 33],
      dtype=int64), 'cur_cost': 96684.0}, {'tour': [0, 19, 15, 20, 21, 13, 23, 16, 18, 12, 22, 17, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12503.0}, {'tour': [2, 15, 11, 54, 19, 20, 55, 61, 26, 13, 33, 24, 18, 44, 7, 17, 53, 57, 64, 27, 25, 47, 31, 38, 49, 41, 1, 63, 22, 40, 23, 0, 45, 6, 12, 5, 50, 60, 4, 48, 65, 35, 42, 59, 28, 37, 8, 3, 52, 62, 9, 21, 10, 16, 36, 30, 56, 46, 43, 51, 39, 32, 14, 34, 29, 58], 'cur_cost': 94392.0}, {'tour': array([ 4, 20, 23,  6, 33, 63, 17, 44, 65, 43, 58, 16, 59, 19, 49, 57, 10,
       27, 62, 60,  5, 21, 14, 29,  9, 37, 35, 47, 13, 24, 55, 39, 48,  7,
       64, 41, 25, 40, 36,  2,  0, 38,  8, 50, 52, 18, 22, 51, 15, 34, 42,
       30,  1, 26, 56,  3, 28, 11, 12, 46, 31, 54, 53, 32, 45, 61],
      dtype=int64), 'cur_cost': 117619.0}, {'tour': array([ 6, 16,  4, 11, 51, 13,  8, 57, 41, 53, 56, 65, 23, 28, 22,  0, 33,
       64, 10, 40, 54, 43, 52, 38, 39, 19, 30,  7,  3, 60, 62, 15, 12, 45,
       46, 26, 35, 32, 49,  9, 21, 47, 58, 55, 14, 36, 17, 20, 29, 61, 18,
       48, 50, 27, 42, 25,  2,  1, 59, 24, 44, 31, 63, 37, 34,  5],
      dtype=int64), 'cur_cost': 98081.0}, {'tour': array([50, 46, 41, 53, 55, 45,  3, 19, 25, 23,  0, 43, 35, 58, 14, 61, 22,
       44, 42, 31, 29, 65, 63, 20, 54, 24,  7, 36,  1, 52,  8, 18, 30, 37,
        5, 48, 34, 13, 51, 47,  9,  4, 57, 64, 21, 40, 10, 17, 38,  6, 49,
       16,  2, 28, 39, 62, 26, 11, 59, 60, 15, 12, 32, 27, 33, 56],
      dtype=int64), 'cur_cost': 101324.0}, {'tour': array([22, 39,  3,  8, 30, 25, 20,  4, 17, 41, 60, 32, 54,  0, 11, 10, 43,
       61, 37, 38, 15, 58, 46,  1, 47, 55,  7, 31, 12, 48, 36, 45, 64, 51,
        2, 50, 35, 40, 63, 52, 59, 56, 44, 28, 29, 53,  6, 62, 14, 26, 18,
       42, 23, 21, 27,  5, 24, 16, 33, 65, 57, 19, 49, 34,  9, 13],
      dtype=int64), 'cur_cost': 109461.0}, {'tour': array([ 2, 16, 37, 50, 65, 55,  4, 43, 40, 52, 13, 53, 24, 30,  3,  0, 10,
        8, 21, 63, 59, 42, 51, 20,  5, 41, 56, 58,  6, 17, 39, 49, 62,  9,
       12, 60, 48, 27, 46, 61, 33, 15, 19, 44, 31, 35, 28, 23, 25,  7, 54,
       11, 36, 38, 57, 26, 14, 29, 34, 47, 22, 32, 18,  1, 45, 64],
      dtype=int64), 'cur_cost': 98068.0}, {'tour': array([12, 15, 38,  9, 47,  8, 54, 36, 29, 45,  5, 64, 62, 53, 14, 18, 42,
       40, 58,  7, 28, 41,  2, 44, 49,  3, 30, 16, 48, 51, 56, 46, 23, 21,
       52, 22, 37, 63, 25, 19, 33, 39, 26, 43, 27,  1, 60, 24, 13, 31, 11,
       50, 17, 61, 34, 32, 59,  4, 57, 55,  0, 35,  6, 20, 65, 10],
      dtype=int64), 'cur_cost': 106186.0}, {'tour': array([10, 50,  0, 52, 28, 44, 34, 35, 48, 11, 15, 24, 46, 62, 53, 36, 59,
       12, 19,  4, 29, 18, 47, 39,  2, 41, 32, 21,  9, 26,  6, 56, 58, 65,
       42, 64,  5,  7, 61, 55, 14,  8, 16, 30, 54, 22, 23, 33, 63, 43,  3,
       37, 45, 20, 13, 17, 27, 60, 38, 25, 40,  1, 51, 57, 49, 31],
      dtype=int64), 'cur_cost': 113377.0}, {'tour': array([10, 58, 11,  2,  9, 21, 34, 50, 52, 43,  7, 25, 16, 13, 62,  3, 41,
       48, 35,  8, 56,  1, 47, 60, 54, 14, 36, 40, 30, 65, 18, 46, 53, 55,
       61, 17, 38, 19, 29,  0, 39, 33, 42, 51, 27,  6, 63, 26, 22, 32, 12,
       64, 23, 45, 15, 31, 37, 59,  4,  5, 57, 44, 28, 49, 24, 20],
      dtype=int64), 'cur_cost': 103181.0}, {'tour': array([38, 27, 16,  5, 43, 26, 41, 47, 40, 45, 17, 23, 32, 35, 64, 19, 31,
       28, 22, 39, 58, 30, 49, 52,  1, 36, 44, 54, 24, 25,  0, 53, 57, 46,
       56, 12, 13, 33, 42, 10, 55,  8,  9, 14, 11,  3, 15, 65, 62, 18, 51,
       29, 21,  2, 20, 50,  6, 37,  7, 60, 48,  4, 59, 34, 63, 61],
      dtype=int64), 'cur_cost': 102164.0}, {'tour': array([44, 32, 38, 47, 57, 17, 25, 52, 41, 37, 26,  0, 48, 53, 58, 10,  5,
       27, 15, 29, 55,  8, 49,  4, 56, 34, 35, 23, 63, 36, 59,  9, 14, 28,
       65, 61,  1, 64, 11, 18, 51, 62, 40, 45, 20, 60, 50, 43, 21, 39, 16,
       33, 12, 54,  2,  7, 42, 22, 19,  6, 31, 30, 13, 24, 46,  3],
      dtype=int64), 'cur_cost': 103814.0}, {'tour': array([ 4, 11, 43, 45, 23, 27, 40, 63,  1, 60, 35,  0, 25, 64, 32, 22, 38,
        2,  9, 36, 21, 48, 62, 37, 44, 39, 33, 59, 31, 55, 20, 13, 12,  6,
       65, 24,  3,  5, 53, 58, 51, 16, 49, 19, 15, 47,  8,  7, 30, 34, 57,
       61, 26, 29, 41, 18, 42, 50, 54, 56, 17, 28, 10, 14, 46, 52],
      dtype=int64), 'cur_cost': 102693.0}, {'tour': array([ 7, 34, 14, 61, 50, 49, 12, 46, 23, 35, 58, 11, 52,  9, 42, 54, 26,
       37,  6, 43, 56, 22, 15, 17, 57, 21, 41, 13, 10, 44, 20, 19,  5, 36,
       27, 45, 16, 25, 64, 63,  2, 18,  4, 59, 48, 53, 65,  1, 60, 24, 62,
       39, 40,  0, 55,  8,  3, 51, 31, 32, 28, 29, 33, 47, 30, 38],
      dtype=int64), 'cur_cost': 99450.0}, {'tour': array([55,  5, 51, 17, 10, 11,  1, 34, 36, 28, 64, 14,  3, 63, 25, 27, 32,
       54,  7, 37, 47, 33, 42, 35, 21, 31, 49,  6, 50, 44, 53, 58, 43, 30,
       62, 60, 38, 13, 56, 45, 16, 20, 59,  0, 40, 57,  4, 12, 23, 22, 52,
       18, 46,  9, 29,  2, 19,  8, 39, 41, 65, 24, 26, 48, 15, 61],
      dtype=int64), 'cur_cost': 102732.0}]
2025-08-03 16:38:27,306 - ExploitationExpert - INFO - 局部搜索耗时: 0.61秒
2025-08-03 16:38:27,306 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-03 16:38:27,306 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([ 4, 20, 23,  6, 33, 63, 17, 44, 65, 43, 58, 16, 59, 19, 49, 57, 10,
       27, 62, 60,  5, 21, 14, 29,  9, 37, 35, 47, 13, 24, 55, 39, 48,  7,
       64, 41, 25, 40, 36,  2,  0, 38,  8, 50, 52, 18, 22, 51, 15, 34, 42,
       30,  1, 26, 56,  3, 28, 11, 12, 46, 31, 54, 53, 32, 45, 61],
      dtype=int64), 'cur_cost': 117619.0}
2025-08-03 16:38:27,307 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 117619.00)
2025-08-03 16:38:27,307 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-03 16:38:27,307 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-03 16:38:27,307 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:38:27,312 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:38:27,315 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:38:27,317 - ExplorationExpert - INFO - 探索路径生成完成，成本: 97766.0, 路径长度: 66
2025-08-03 16:38:27,317 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [2, 54, 5, 12, 20, 55, 16, 26, 27, 60, 19, 57, 33, 40, 52, 14, 43, 22, 23, 11, 53, 25, 35, 51, 29, 32, 36, 31, 30, 46, 41, 59, 50, 17, 7, 1, 56, 38, 8, 63, 28, 37, 49, 4, 15, 34, 24, 0, 48, 47, 44, 9, 10, 13, 18, 6, 39, 62, 3, 42, 64, 65, 45, 58, 21, 61], 'cur_cost': 97766.0}
2025-08-03 16:38:27,318 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 97766.00)
2025-08-03 16:38:27,318 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-03 16:38:27,318 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-03 16:38:27,318 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:38:27,325 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:38:27,326 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:38:27,326 - ExplorationExpert - INFO - 探索路径生成完成，成本: 103873.0, 路径长度: 66
2025-08-03 16:38:27,326 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [19, 21, 12, 22, 2, 7, 1, 44, 27, 25, 24, 60, 34, 6, 54, 31, 18, 14, 43, 28, 11, 23, 20, 56, 57, 26, 46, 47, 52, 62, 45, 32, 15, 16, 41, 64, 63, 58, 5, 0, 48, 8, 37, 30, 59, 50, 35, 13, 65, 51, 61, 40, 9, 33, 49, 10, 53, 4, 36, 42, 55, 38, 3, 17, 29, 39], 'cur_cost': 103873.0}
2025-08-03 16:38:27,327 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 103873.00)
2025-08-03 16:38:27,328 - experts.management.collaboration_manager - INFO - 为个体 10 生成利用路径
2025-08-03 16:38:27,328 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:38:27,328 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:38:27,329 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 10 处的路径，新成本: 95369.0
2025-08-03 16:38:27,419 - ExploitationExpert - INFO - res_population_num: 9
2025-08-03 16:38:27,419 - ExploitationExpert - INFO - res_population_costs: [9787.0, 9607.0, 9578.0, 9578, 9559.0, 9544, 9533, 9526, 9521]
2025-08-03 16:38:27,420 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 15, 14, 20, 21, 13, 19, 17, 12, 22, 23, 16,
       18, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 30, 28, 35, 34, 42, 48,
       43, 40, 49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57,
       64, 60, 58, 56, 59, 62, 53, 61, 55,  5,  4,  6,  2,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 41, 38, 51, 50, 45, 44, 39, 47, 49, 48, 46,
       42, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       30, 28, 35, 25, 26, 36, 37, 31, 33, 32, 29, 24, 27, 19, 18, 16, 23,
       13, 21, 20, 14, 15, 22, 12, 17,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       30, 28, 35, 25, 26, 36, 37, 31, 33, 32, 29, 24, 27, 19, 16, 18, 23,
       13, 21, 20, 14, 15, 22, 12, 17,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 32, 29, 33, 31, 24, 37, 25, 26, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-08-03 16:38:27,424 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:38:27,425 - ExploitationExpert - INFO - populations: [{'tour': [50, 22, 30, 12, 11, 54, 5, 14, 2, 60, 4, 8, 17, 20, 16, 3, 15, 37, 13, 24, 29, 19, 6, 55, 40, 18, 49, 44, 43, 28, 10, 56, 9, 58, 65, 7, 53, 0, 52, 57, 64, 21, 27, 33, 25, 34, 46, 47, 51, 38, 45, 23, 32, 36, 31, 35, 48, 42, 26, 41, 1, 63, 61, 59, 62, 39], 'cur_cost': 61220.0}, {'tour': array([40,  5, 34, 52, 43, 26, 58, 27, 11, 47, 48,  0, 44,  7, 33, 54, 15,
       20, 53, 23, 32, 42, 16, 14, 65,  2, 21, 57, 31, 46,  1, 22,  4, 36,
       45, 39, 64, 63, 12, 18, 49,  6, 24, 41,  3, 10, 13, 56, 29, 60, 38,
       30, 17, 19, 50, 51, 61, 62,  8, 28, 35, 25, 37, 55, 59,  9],
      dtype=int64), 'cur_cost': 111023.0}, {'tour': [0, 2, 16, 8, 5, 4, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12868.0}, {'tour': [0, 9, 21, 17, 12, 22, 23, 16, 18, 19, 13, 20, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12439.0}, {'tour': array([23, 24,  2, 17,  8, 44, 40, 38, 12, 62, 29, 47, 25, 53, 46, 39, 13,
       65, 26, 60, 63, 45, 19, 10, 54,  6, 59, 61, 16, 32,  1, 30, 37, 34,
       35,  0, 21, 42,  5, 55, 52, 58, 56, 48, 31,  7, 18,  4, 22, 27, 28,
       36, 11, 50, 15, 43, 64,  9, 49, 14, 20, 51,  3, 57, 41, 33],
      dtype=int64), 'cur_cost': 96684.0}, {'tour': [0, 19, 15, 20, 21, 13, 23, 16, 18, 12, 22, 17, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12503.0}, {'tour': [2, 15, 11, 54, 19, 20, 55, 61, 26, 13, 33, 24, 18, 44, 7, 17, 53, 57, 64, 27, 25, 47, 31, 38, 49, 41, 1, 63, 22, 40, 23, 0, 45, 6, 12, 5, 50, 60, 4, 48, 65, 35, 42, 59, 28, 37, 8, 3, 52, 62, 9, 21, 10, 16, 36, 30, 56, 46, 43, 51, 39, 32, 14, 34, 29, 58], 'cur_cost': 94392.0}, {'tour': array([ 4, 20, 23,  6, 33, 63, 17, 44, 65, 43, 58, 16, 59, 19, 49, 57, 10,
       27, 62, 60,  5, 21, 14, 29,  9, 37, 35, 47, 13, 24, 55, 39, 48,  7,
       64, 41, 25, 40, 36,  2,  0, 38,  8, 50, 52, 18, 22, 51, 15, 34, 42,
       30,  1, 26, 56,  3, 28, 11, 12, 46, 31, 54, 53, 32, 45, 61],
      dtype=int64), 'cur_cost': 117619.0}, {'tour': [2, 54, 5, 12, 20, 55, 16, 26, 27, 60, 19, 57, 33, 40, 52, 14, 43, 22, 23, 11, 53, 25, 35, 51, 29, 32, 36, 31, 30, 46, 41, 59, 50, 17, 7, 1, 56, 38, 8, 63, 28, 37, 49, 4, 15, 34, 24, 0, 48, 47, 44, 9, 10, 13, 18, 6, 39, 62, 3, 42, 64, 65, 45, 58, 21, 61], 'cur_cost': 97766.0}, {'tour': [19, 21, 12, 22, 2, 7, 1, 44, 27, 25, 24, 60, 34, 6, 54, 31, 18, 14, 43, 28, 11, 23, 20, 56, 57, 26, 46, 47, 52, 62, 45, 32, 15, 16, 41, 64, 63, 58, 5, 0, 48, 8, 37, 30, 59, 50, 35, 13, 65, 51, 61, 40, 9, 33, 49, 10, 53, 4, 36, 42, 55, 38, 3, 17, 29, 39], 'cur_cost': 103873.0}, {'tour': array([61, 20, 40, 62, 16, 37, 17, 22, 24, 15, 23, 30,  0, 11, 49, 53,  7,
       58,  2, 47, 54, 63, 52, 45, 25, 35,  6, 60, 51,  1, 38, 56, 13, 27,
       39, 57,  3, 43, 64, 46, 18, 33, 31, 19,  8, 65, 59, 42, 41,  9, 21,
       26, 32, 14, 48, 44, 28, 34, 29, 10, 50, 12, 36,  4,  5, 55],
      dtype=int64), 'cur_cost': 95369.0}, {'tour': array([ 2, 16, 37, 50, 65, 55,  4, 43, 40, 52, 13, 53, 24, 30,  3,  0, 10,
        8, 21, 63, 59, 42, 51, 20,  5, 41, 56, 58,  6, 17, 39, 49, 62,  9,
       12, 60, 48, 27, 46, 61, 33, 15, 19, 44, 31, 35, 28, 23, 25,  7, 54,
       11, 36, 38, 57, 26, 14, 29, 34, 47, 22, 32, 18,  1, 45, 64],
      dtype=int64), 'cur_cost': 98068.0}, {'tour': array([12, 15, 38,  9, 47,  8, 54, 36, 29, 45,  5, 64, 62, 53, 14, 18, 42,
       40, 58,  7, 28, 41,  2, 44, 49,  3, 30, 16, 48, 51, 56, 46, 23, 21,
       52, 22, 37, 63, 25, 19, 33, 39, 26, 43, 27,  1, 60, 24, 13, 31, 11,
       50, 17, 61, 34, 32, 59,  4, 57, 55,  0, 35,  6, 20, 65, 10],
      dtype=int64), 'cur_cost': 106186.0}, {'tour': array([10, 50,  0, 52, 28, 44, 34, 35, 48, 11, 15, 24, 46, 62, 53, 36, 59,
       12, 19,  4, 29, 18, 47, 39,  2, 41, 32, 21,  9, 26,  6, 56, 58, 65,
       42, 64,  5,  7, 61, 55, 14,  8, 16, 30, 54, 22, 23, 33, 63, 43,  3,
       37, 45, 20, 13, 17, 27, 60, 38, 25, 40,  1, 51, 57, 49, 31],
      dtype=int64), 'cur_cost': 113377.0}, {'tour': array([10, 58, 11,  2,  9, 21, 34, 50, 52, 43,  7, 25, 16, 13, 62,  3, 41,
       48, 35,  8, 56,  1, 47, 60, 54, 14, 36, 40, 30, 65, 18, 46, 53, 55,
       61, 17, 38, 19, 29,  0, 39, 33, 42, 51, 27,  6, 63, 26, 22, 32, 12,
       64, 23, 45, 15, 31, 37, 59,  4,  5, 57, 44, 28, 49, 24, 20],
      dtype=int64), 'cur_cost': 103181.0}, {'tour': array([38, 27, 16,  5, 43, 26, 41, 47, 40, 45, 17, 23, 32, 35, 64, 19, 31,
       28, 22, 39, 58, 30, 49, 52,  1, 36, 44, 54, 24, 25,  0, 53, 57, 46,
       56, 12, 13, 33, 42, 10, 55,  8,  9, 14, 11,  3, 15, 65, 62, 18, 51,
       29, 21,  2, 20, 50,  6, 37,  7, 60, 48,  4, 59, 34, 63, 61],
      dtype=int64), 'cur_cost': 102164.0}, {'tour': array([44, 32, 38, 47, 57, 17, 25, 52, 41, 37, 26,  0, 48, 53, 58, 10,  5,
       27, 15, 29, 55,  8, 49,  4, 56, 34, 35, 23, 63, 36, 59,  9, 14, 28,
       65, 61,  1, 64, 11, 18, 51, 62, 40, 45, 20, 60, 50, 43, 21, 39, 16,
       33, 12, 54,  2,  7, 42, 22, 19,  6, 31, 30, 13, 24, 46,  3],
      dtype=int64), 'cur_cost': 103814.0}, {'tour': array([ 4, 11, 43, 45, 23, 27, 40, 63,  1, 60, 35,  0, 25, 64, 32, 22, 38,
        2,  9, 36, 21, 48, 62, 37, 44, 39, 33, 59, 31, 55, 20, 13, 12,  6,
       65, 24,  3,  5, 53, 58, 51, 16, 49, 19, 15, 47,  8,  7, 30, 34, 57,
       61, 26, 29, 41, 18, 42, 50, 54, 56, 17, 28, 10, 14, 46, 52],
      dtype=int64), 'cur_cost': 102693.0}, {'tour': array([ 7, 34, 14, 61, 50, 49, 12, 46, 23, 35, 58, 11, 52,  9, 42, 54, 26,
       37,  6, 43, 56, 22, 15, 17, 57, 21, 41, 13, 10, 44, 20, 19,  5, 36,
       27, 45, 16, 25, 64, 63,  2, 18,  4, 59, 48, 53, 65,  1, 60, 24, 62,
       39, 40,  0, 55,  8,  3, 51, 31, 32, 28, 29, 33, 47, 30, 38],
      dtype=int64), 'cur_cost': 99450.0}, {'tour': array([55,  5, 51, 17, 10, 11,  1, 34, 36, 28, 64, 14,  3, 63, 25, 27, 32,
       54,  7, 37, 47, 33, 42, 35, 21, 31, 49,  6, 50, 44, 53, 58, 43, 30,
       62, 60, 38, 13, 56, 45, 16, 20, 59,  0, 40, 57,  4, 12, 23, 22, 52,
       18, 46,  9, 29,  2, 19,  8, 39, 41, 65, 24, 26, 48, 15, 61],
      dtype=int64), 'cur_cost': 102732.0}]
2025-08-03 16:38:27,436 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒
2025-08-03 16:38:27,436 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-03 16:38:27,437 - experts.management.collaboration_manager - INFO - 个体 10 利用路径生成报告: {'new_tour': array([61, 20, 40, 62, 16, 37, 17, 22, 24, 15, 23, 30,  0, 11, 49, 53,  7,
       58,  2, 47, 54, 63, 52, 45, 25, 35,  6, 60, 51,  1, 38, 56, 13, 27,
       39, 57,  3, 43, 64, 46, 18, 33, 31, 19,  8, 65, 59, 42, 41,  9, 21,
       26, 32, 14, 48, 44, 28, 34, 29, 10, 50, 12, 36,  4,  5, 55],
      dtype=int64), 'cur_cost': 95369.0}
2025-08-03 16:38:27,437 - experts.management.collaboration_manager - INFO - 个体 10 接受新路径 (成本: 95369.00)
2025-08-03 16:38:27,438 - experts.management.collaboration_manager - INFO - 为个体 11 生成探索路径
2025-08-03 16:38:27,438 - ExplorationExpert - INFO - 开始为个体 11 生成探索路径（算法实现）
2025-08-03 16:38:27,438 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:38:27,443 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:38:27,443 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:38:27,444 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12318.0, 路径长度: 66
2025-08-03 16:38:27,444 - experts.management.collaboration_manager - INFO - 个体 11 探索路径生成报告: {'new_tour': [0, 9, 11, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12318.0}
2025-08-03 16:38:27,445 - experts.management.collaboration_manager - INFO - 个体 11 接受新路径 (成本: 12318.00)
2025-08-03 16:38:27,446 - experts.management.collaboration_manager - INFO - 为个体 12 生成探索路径
2025-08-03 16:38:27,446 - ExplorationExpert - INFO - 开始为个体 12 生成探索路径（算法实现）
2025-08-03 16:38:27,447 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:38:27,455 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:38:27,457 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:38:27,458 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12750.0, 路径长度: 66
2025-08-03 16:38:27,459 - experts.management.collaboration_manager - INFO - 个体 12 探索路径生成报告: {'new_tour': [0, 4, 17, 8, 2, 6, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12750.0}
2025-08-03 16:38:27,459 - experts.management.collaboration_manager - INFO - 个体 12 接受新路径 (成本: 12750.00)
2025-08-03 16:38:27,460 - experts.management.collaboration_manager - INFO - 为个体 13 生成利用路径
2025-08-03 16:38:27,460 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:38:27,460 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:38:27,461 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 13 处的路径，新成本: 105705.0
2025-08-03 16:38:27,545 - ExploitationExpert - INFO - res_population_num: 11
2025-08-03 16:38:27,546 - ExploitationExpert - INFO - res_population_costs: [9787.0, 9607.0, 9578.0, 9578, 9559.0, 9544, 9533, 9526, 9521, 9521, 9521]
2025-08-03 16:38:27,546 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 15, 14, 20, 21, 13, 19, 17, 12, 22, 23, 16,
       18, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 30, 28, 35, 34, 42, 48,
       43, 40, 49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57,
       64, 60, 58, 56, 59, 62, 53, 61, 55,  5,  4,  6,  2,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 41, 38, 51, 50, 45, 44, 39, 47, 49, 48, 46,
       42, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       30, 28, 35, 25, 26, 36, 37, 31, 33, 32, 29, 24, 27, 19, 18, 16, 23,
       13, 21, 20, 14, 15, 22, 12, 17,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       30, 28, 35, 25, 26, 36, 37, 31, 33, 32, 29, 24, 27, 19, 16, 18, 23,
       13, 21, 20, 14, 15, 22, 12, 17,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 32, 29, 33, 31, 24, 37, 25, 26, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-08-03 16:38:27,555 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:38:27,555 - ExploitationExpert - INFO - populations: [{'tour': [50, 22, 30, 12, 11, 54, 5, 14, 2, 60, 4, 8, 17, 20, 16, 3, 15, 37, 13, 24, 29, 19, 6, 55, 40, 18, 49, 44, 43, 28, 10, 56, 9, 58, 65, 7, 53, 0, 52, 57, 64, 21, 27, 33, 25, 34, 46, 47, 51, 38, 45, 23, 32, 36, 31, 35, 48, 42, 26, 41, 1, 63, 61, 59, 62, 39], 'cur_cost': 61220.0}, {'tour': array([40,  5, 34, 52, 43, 26, 58, 27, 11, 47, 48,  0, 44,  7, 33, 54, 15,
       20, 53, 23, 32, 42, 16, 14, 65,  2, 21, 57, 31, 46,  1, 22,  4, 36,
       45, 39, 64, 63, 12, 18, 49,  6, 24, 41,  3, 10, 13, 56, 29, 60, 38,
       30, 17, 19, 50, 51, 61, 62,  8, 28, 35, 25, 37, 55, 59,  9],
      dtype=int64), 'cur_cost': 111023.0}, {'tour': [0, 2, 16, 8, 5, 4, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12868.0}, {'tour': [0, 9, 21, 17, 12, 22, 23, 16, 18, 19, 13, 20, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12439.0}, {'tour': array([23, 24,  2, 17,  8, 44, 40, 38, 12, 62, 29, 47, 25, 53, 46, 39, 13,
       65, 26, 60, 63, 45, 19, 10, 54,  6, 59, 61, 16, 32,  1, 30, 37, 34,
       35,  0, 21, 42,  5, 55, 52, 58, 56, 48, 31,  7, 18,  4, 22, 27, 28,
       36, 11, 50, 15, 43, 64,  9, 49, 14, 20, 51,  3, 57, 41, 33],
      dtype=int64), 'cur_cost': 96684.0}, {'tour': [0, 19, 15, 20, 21, 13, 23, 16, 18, 12, 22, 17, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12503.0}, {'tour': [2, 15, 11, 54, 19, 20, 55, 61, 26, 13, 33, 24, 18, 44, 7, 17, 53, 57, 64, 27, 25, 47, 31, 38, 49, 41, 1, 63, 22, 40, 23, 0, 45, 6, 12, 5, 50, 60, 4, 48, 65, 35, 42, 59, 28, 37, 8, 3, 52, 62, 9, 21, 10, 16, 36, 30, 56, 46, 43, 51, 39, 32, 14, 34, 29, 58], 'cur_cost': 94392.0}, {'tour': array([ 4, 20, 23,  6, 33, 63, 17, 44, 65, 43, 58, 16, 59, 19, 49, 57, 10,
       27, 62, 60,  5, 21, 14, 29,  9, 37, 35, 47, 13, 24, 55, 39, 48,  7,
       64, 41, 25, 40, 36,  2,  0, 38,  8, 50, 52, 18, 22, 51, 15, 34, 42,
       30,  1, 26, 56,  3, 28, 11, 12, 46, 31, 54, 53, 32, 45, 61],
      dtype=int64), 'cur_cost': 117619.0}, {'tour': [2, 54, 5, 12, 20, 55, 16, 26, 27, 60, 19, 57, 33, 40, 52, 14, 43, 22, 23, 11, 53, 25, 35, 51, 29, 32, 36, 31, 30, 46, 41, 59, 50, 17, 7, 1, 56, 38, 8, 63, 28, 37, 49, 4, 15, 34, 24, 0, 48, 47, 44, 9, 10, 13, 18, 6, 39, 62, 3, 42, 64, 65, 45, 58, 21, 61], 'cur_cost': 97766.0}, {'tour': [19, 21, 12, 22, 2, 7, 1, 44, 27, 25, 24, 60, 34, 6, 54, 31, 18, 14, 43, 28, 11, 23, 20, 56, 57, 26, 46, 47, 52, 62, 45, 32, 15, 16, 41, 64, 63, 58, 5, 0, 48, 8, 37, 30, 59, 50, 35, 13, 65, 51, 61, 40, 9, 33, 49, 10, 53, 4, 36, 42, 55, 38, 3, 17, 29, 39], 'cur_cost': 103873.0}, {'tour': array([61, 20, 40, 62, 16, 37, 17, 22, 24, 15, 23, 30,  0, 11, 49, 53,  7,
       58,  2, 47, 54, 63, 52, 45, 25, 35,  6, 60, 51,  1, 38, 56, 13, 27,
       39, 57,  3, 43, 64, 46, 18, 33, 31, 19,  8, 65, 59, 42, 41,  9, 21,
       26, 32, 14, 48, 44, 28, 34, 29, 10, 50, 12, 36,  4,  5, 55],
      dtype=int64), 'cur_cost': 95369.0}, {'tour': [0, 9, 11, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12318.0}, {'tour': [0, 4, 17, 8, 2, 6, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12750.0}, {'tour': array([15, 45, 18,  1, 25, 34, 39, 38, 20, 55, 59, 61, 62,  3, 14,  2, 33,
       22, 65, 47, 35,  9,  7, 12, 58, 31, 46, 32, 64, 63,  8, 17, 19, 11,
       54, 42,  0, 57,  4, 50, 29, 44,  6, 30, 40, 10, 21, 27, 48, 60, 43,
       49, 16, 36, 51, 28, 37, 23, 41, 13, 56, 24, 52,  5, 26, 53],
      dtype=int64), 'cur_cost': 105705.0}, {'tour': array([10, 58, 11,  2,  9, 21, 34, 50, 52, 43,  7, 25, 16, 13, 62,  3, 41,
       48, 35,  8, 56,  1, 47, 60, 54, 14, 36, 40, 30, 65, 18, 46, 53, 55,
       61, 17, 38, 19, 29,  0, 39, 33, 42, 51, 27,  6, 63, 26, 22, 32, 12,
       64, 23, 45, 15, 31, 37, 59,  4,  5, 57, 44, 28, 49, 24, 20],
      dtype=int64), 'cur_cost': 103181.0}, {'tour': array([38, 27, 16,  5, 43, 26, 41, 47, 40, 45, 17, 23, 32, 35, 64, 19, 31,
       28, 22, 39, 58, 30, 49, 52,  1, 36, 44, 54, 24, 25,  0, 53, 57, 46,
       56, 12, 13, 33, 42, 10, 55,  8,  9, 14, 11,  3, 15, 65, 62, 18, 51,
       29, 21,  2, 20, 50,  6, 37,  7, 60, 48,  4, 59, 34, 63, 61],
      dtype=int64), 'cur_cost': 102164.0}, {'tour': array([44, 32, 38, 47, 57, 17, 25, 52, 41, 37, 26,  0, 48, 53, 58, 10,  5,
       27, 15, 29, 55,  8, 49,  4, 56, 34, 35, 23, 63, 36, 59,  9, 14, 28,
       65, 61,  1, 64, 11, 18, 51, 62, 40, 45, 20, 60, 50, 43, 21, 39, 16,
       33, 12, 54,  2,  7, 42, 22, 19,  6, 31, 30, 13, 24, 46,  3],
      dtype=int64), 'cur_cost': 103814.0}, {'tour': array([ 4, 11, 43, 45, 23, 27, 40, 63,  1, 60, 35,  0, 25, 64, 32, 22, 38,
        2,  9, 36, 21, 48, 62, 37, 44, 39, 33, 59, 31, 55, 20, 13, 12,  6,
       65, 24,  3,  5, 53, 58, 51, 16, 49, 19, 15, 47,  8,  7, 30, 34, 57,
       61, 26, 29, 41, 18, 42, 50, 54, 56, 17, 28, 10, 14, 46, 52],
      dtype=int64), 'cur_cost': 102693.0}, {'tour': array([ 7, 34, 14, 61, 50, 49, 12, 46, 23, 35, 58, 11, 52,  9, 42, 54, 26,
       37,  6, 43, 56, 22, 15, 17, 57, 21, 41, 13, 10, 44, 20, 19,  5, 36,
       27, 45, 16, 25, 64, 63,  2, 18,  4, 59, 48, 53, 65,  1, 60, 24, 62,
       39, 40,  0, 55,  8,  3, 51, 31, 32, 28, 29, 33, 47, 30, 38],
      dtype=int64), 'cur_cost': 99450.0}, {'tour': array([55,  5, 51, 17, 10, 11,  1, 34, 36, 28, 64, 14,  3, 63, 25, 27, 32,
       54,  7, 37, 47, 33, 42, 35, 21, 31, 49,  6, 50, 44, 53, 58, 43, 30,
       62, 60, 38, 13, 56, 45, 16, 20, 59,  0, 40, 57,  4, 12, 23, 22, 52,
       18, 46,  9, 29,  2, 19,  8, 39, 41, 65, 24, 26, 48, 15, 61],
      dtype=int64), 'cur_cost': 102732.0}]
2025-08-03 16:38:27,565 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 16:38:27,565 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-03 16:38:27,565 - experts.management.collaboration_manager - INFO - 个体 13 利用路径生成报告: {'new_tour': array([15, 45, 18,  1, 25, 34, 39, 38, 20, 55, 59, 61, 62,  3, 14,  2, 33,
       22, 65, 47, 35,  9,  7, 12, 58, 31, 46, 32, 64, 63,  8, 17, 19, 11,
       54, 42,  0, 57,  4, 50, 29, 44,  6, 30, 40, 10, 21, 27, 48, 60, 43,
       49, 16, 36, 51, 28, 37, 23, 41, 13, 56, 24, 52,  5, 26, 53],
      dtype=int64), 'cur_cost': 105705.0}
2025-08-03 16:38:27,566 - experts.management.collaboration_manager - INFO - 个体 13 接受新路径 (成本: 105705.00)
2025-08-03 16:38:27,566 - experts.management.collaboration_manager - INFO - 为个体 14 生成探索路径
2025-08-03 16:38:27,567 - ExplorationExpert - INFO - 开始为个体 14 生成探索路径（算法实现）
2025-08-03 16:38:27,567 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:38:27,572 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:38:27,573 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:38:27,573 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12357.0, 路径长度: 66
2025-08-03 16:38:27,573 - experts.management.collaboration_manager - INFO - 个体 14 探索路径生成报告: {'new_tour': [0, 12, 20, 21, 13, 23, 16, 18, 17, 22, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12357.0}
2025-08-03 16:38:27,574 - experts.management.collaboration_manager - INFO - 个体 14 接受新路径 (成本: 12357.00)
2025-08-03 16:38:27,574 - experts.management.collaboration_manager - INFO - 为个体 15 生成探索路径
2025-08-03 16:38:27,574 - ExplorationExpert - INFO - 开始为个体 15 生成探索路径（算法实现）
2025-08-03 16:38:27,574 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:38:27,578 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:38:27,579 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:38:27,579 - ExplorationExpert - INFO - 探索路径生成完成，成本: 105944.0, 路径长度: 66
2025-08-03 16:38:27,580 - experts.management.collaboration_manager - INFO - 个体 15 探索路径生成报告: {'new_tour': [50, 2, 26, 4, 15, 52, 25, 14, 36, 35, 19, 31, 55, 24, 28, 10, 9, 58, 13, 20, 53, 48, 64, 33, 46, 65, 62, 45, 23, 18, 30, 1, 32, 40, 41, 0, 54, 3, 7, 43, 5, 17, 21, 6, 12, 57, 11, 59, 34, 63, 8, 47, 51, 56, 61, 29, 38, 44, 49, 16, 42, 37, 39, 27, 22, 60], 'cur_cost': 105944.0}
2025-08-03 16:38:27,584 - experts.management.collaboration_manager - INFO - 个体 15 接受新路径 (成本: 105944.00)
2025-08-03 16:38:27,586 - experts.management.collaboration_manager - INFO - 为个体 16 生成利用路径
2025-08-03 16:38:27,586 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:38:27,587 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:38:27,588 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 16 处的路径，新成本: 85695.0
2025-08-03 16:38:27,672 - ExploitationExpert - INFO - res_population_num: 11
2025-08-03 16:38:27,672 - ExploitationExpert - INFO - res_population_costs: [9787.0, 9607.0, 9578.0, 9578, 9559.0, 9544, 9533, 9526, 9521, 9521, 9521]
2025-08-03 16:38:27,672 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 15, 14, 20, 21, 13, 19, 17, 12, 22, 23, 16,
       18, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 30, 28, 35, 34, 42, 48,
       43, 40, 49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57,
       64, 60, 58, 56, 59, 62, 53, 61, 55,  5,  4,  6,  2,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 41, 38, 51, 50, 45, 44, 39, 47, 49, 48, 46,
       42, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       30, 28, 35, 25, 26, 36, 37, 31, 33, 32, 29, 24, 27, 19, 18, 16, 23,
       13, 21, 20, 14, 15, 22, 12, 17,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       30, 28, 35, 25, 26, 36, 37, 31, 33, 32, 29, 24, 27, 19, 16, 18, 23,
       13, 21, 20, 14, 15, 22, 12, 17,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 32, 29, 33, 31, 24, 37, 25, 26, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-08-03 16:38:27,678 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:38:27,678 - ExploitationExpert - INFO - populations: [{'tour': [50, 22, 30, 12, 11, 54, 5, 14, 2, 60, 4, 8, 17, 20, 16, 3, 15, 37, 13, 24, 29, 19, 6, 55, 40, 18, 49, 44, 43, 28, 10, 56, 9, 58, 65, 7, 53, 0, 52, 57, 64, 21, 27, 33, 25, 34, 46, 47, 51, 38, 45, 23, 32, 36, 31, 35, 48, 42, 26, 41, 1, 63, 61, 59, 62, 39], 'cur_cost': 61220.0}, {'tour': array([40,  5, 34, 52, 43, 26, 58, 27, 11, 47, 48,  0, 44,  7, 33, 54, 15,
       20, 53, 23, 32, 42, 16, 14, 65,  2, 21, 57, 31, 46,  1, 22,  4, 36,
       45, 39, 64, 63, 12, 18, 49,  6, 24, 41,  3, 10, 13, 56, 29, 60, 38,
       30, 17, 19, 50, 51, 61, 62,  8, 28, 35, 25, 37, 55, 59,  9],
      dtype=int64), 'cur_cost': 111023.0}, {'tour': [0, 2, 16, 8, 5, 4, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12868.0}, {'tour': [0, 9, 21, 17, 12, 22, 23, 16, 18, 19, 13, 20, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12439.0}, {'tour': array([23, 24,  2, 17,  8, 44, 40, 38, 12, 62, 29, 47, 25, 53, 46, 39, 13,
       65, 26, 60, 63, 45, 19, 10, 54,  6, 59, 61, 16, 32,  1, 30, 37, 34,
       35,  0, 21, 42,  5, 55, 52, 58, 56, 48, 31,  7, 18,  4, 22, 27, 28,
       36, 11, 50, 15, 43, 64,  9, 49, 14, 20, 51,  3, 57, 41, 33],
      dtype=int64), 'cur_cost': 96684.0}, {'tour': [0, 19, 15, 20, 21, 13, 23, 16, 18, 12, 22, 17, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12503.0}, {'tour': [2, 15, 11, 54, 19, 20, 55, 61, 26, 13, 33, 24, 18, 44, 7, 17, 53, 57, 64, 27, 25, 47, 31, 38, 49, 41, 1, 63, 22, 40, 23, 0, 45, 6, 12, 5, 50, 60, 4, 48, 65, 35, 42, 59, 28, 37, 8, 3, 52, 62, 9, 21, 10, 16, 36, 30, 56, 46, 43, 51, 39, 32, 14, 34, 29, 58], 'cur_cost': 94392.0}, {'tour': array([ 4, 20, 23,  6, 33, 63, 17, 44, 65, 43, 58, 16, 59, 19, 49, 57, 10,
       27, 62, 60,  5, 21, 14, 29,  9, 37, 35, 47, 13, 24, 55, 39, 48,  7,
       64, 41, 25, 40, 36,  2,  0, 38,  8, 50, 52, 18, 22, 51, 15, 34, 42,
       30,  1, 26, 56,  3, 28, 11, 12, 46, 31, 54, 53, 32, 45, 61],
      dtype=int64), 'cur_cost': 117619.0}, {'tour': [2, 54, 5, 12, 20, 55, 16, 26, 27, 60, 19, 57, 33, 40, 52, 14, 43, 22, 23, 11, 53, 25, 35, 51, 29, 32, 36, 31, 30, 46, 41, 59, 50, 17, 7, 1, 56, 38, 8, 63, 28, 37, 49, 4, 15, 34, 24, 0, 48, 47, 44, 9, 10, 13, 18, 6, 39, 62, 3, 42, 64, 65, 45, 58, 21, 61], 'cur_cost': 97766.0}, {'tour': [19, 21, 12, 22, 2, 7, 1, 44, 27, 25, 24, 60, 34, 6, 54, 31, 18, 14, 43, 28, 11, 23, 20, 56, 57, 26, 46, 47, 52, 62, 45, 32, 15, 16, 41, 64, 63, 58, 5, 0, 48, 8, 37, 30, 59, 50, 35, 13, 65, 51, 61, 40, 9, 33, 49, 10, 53, 4, 36, 42, 55, 38, 3, 17, 29, 39], 'cur_cost': 103873.0}, {'tour': array([61, 20, 40, 62, 16, 37, 17, 22, 24, 15, 23, 30,  0, 11, 49, 53,  7,
       58,  2, 47, 54, 63, 52, 45, 25, 35,  6, 60, 51,  1, 38, 56, 13, 27,
       39, 57,  3, 43, 64, 46, 18, 33, 31, 19,  8, 65, 59, 42, 41,  9, 21,
       26, 32, 14, 48, 44, 28, 34, 29, 10, 50, 12, 36,  4,  5, 55],
      dtype=int64), 'cur_cost': 95369.0}, {'tour': [0, 9, 11, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12318.0}, {'tour': [0, 4, 17, 8, 2, 6, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12750.0}, {'tour': array([15, 45, 18,  1, 25, 34, 39, 38, 20, 55, 59, 61, 62,  3, 14,  2, 33,
       22, 65, 47, 35,  9,  7, 12, 58, 31, 46, 32, 64, 63,  8, 17, 19, 11,
       54, 42,  0, 57,  4, 50, 29, 44,  6, 30, 40, 10, 21, 27, 48, 60, 43,
       49, 16, 36, 51, 28, 37, 23, 41, 13, 56, 24, 52,  5, 26, 53],
      dtype=int64), 'cur_cost': 105705.0}, {'tour': [0, 12, 20, 21, 13, 23, 16, 18, 17, 22, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12357.0}, {'tour': [50, 2, 26, 4, 15, 52, 25, 14, 36, 35, 19, 31, 55, 24, 28, 10, 9, 58, 13, 20, 53, 48, 64, 33, 46, 65, 62, 45, 23, 18, 30, 1, 32, 40, 41, 0, 54, 3, 7, 43, 5, 17, 21, 6, 12, 57, 11, 59, 34, 63, 8, 47, 51, 56, 61, 29, 38, 44, 49, 16, 42, 37, 39, 27, 22, 60], 'cur_cost': 105944.0}, {'tour': array([54,  4, 53, 55,  2, 48, 29, 25, 10, 15,  1, 57, 59, 64, 11,  0,  5,
       24, 12, 19, 52,  8,  7, 38, 18, 44, 46, 31, 16, 37, 58, 63, 49, 22,
       40, 51, 26, 65, 60, 23, 41, 47, 61, 36, 50,  3, 17, 21, 56, 45, 20,
       32, 28, 27, 35, 62, 14,  6, 30, 13,  9, 42, 39, 43, 34, 33],
      dtype=int64), 'cur_cost': 85695.0}, {'tour': array([ 4, 11, 43, 45, 23, 27, 40, 63,  1, 60, 35,  0, 25, 64, 32, 22, 38,
        2,  9, 36, 21, 48, 62, 37, 44, 39, 33, 59, 31, 55, 20, 13, 12,  6,
       65, 24,  3,  5, 53, 58, 51, 16, 49, 19, 15, 47,  8,  7, 30, 34, 57,
       61, 26, 29, 41, 18, 42, 50, 54, 56, 17, 28, 10, 14, 46, 52],
      dtype=int64), 'cur_cost': 102693.0}, {'tour': array([ 7, 34, 14, 61, 50, 49, 12, 46, 23, 35, 58, 11, 52,  9, 42, 54, 26,
       37,  6, 43, 56, 22, 15, 17, 57, 21, 41, 13, 10, 44, 20, 19,  5, 36,
       27, 45, 16, 25, 64, 63,  2, 18,  4, 59, 48, 53, 65,  1, 60, 24, 62,
       39, 40,  0, 55,  8,  3, 51, 31, 32, 28, 29, 33, 47, 30, 38],
      dtype=int64), 'cur_cost': 99450.0}, {'tour': array([55,  5, 51, 17, 10, 11,  1, 34, 36, 28, 64, 14,  3, 63, 25, 27, 32,
       54,  7, 37, 47, 33, 42, 35, 21, 31, 49,  6, 50, 44, 53, 58, 43, 30,
       62, 60, 38, 13, 56, 45, 16, 20, 59,  0, 40, 57,  4, 12, 23, 22, 52,
       18, 46,  9, 29,  2, 19,  8, 39, 41, 65, 24, 26, 48, 15, 61],
      dtype=int64), 'cur_cost': 102732.0}]
2025-08-03 16:38:27,692 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 16:38:27,692 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-03 16:38:27,692 - experts.management.collaboration_manager - INFO - 个体 16 利用路径生成报告: {'new_tour': array([54,  4, 53, 55,  2, 48, 29, 25, 10, 15,  1, 57, 59, 64, 11,  0,  5,
       24, 12, 19, 52,  8,  7, 38, 18, 44, 46, 31, 16, 37, 58, 63, 49, 22,
       40, 51, 26, 65, 60, 23, 41, 47, 61, 36, 50,  3, 17, 21, 56, 45, 20,
       32, 28, 27, 35, 62, 14,  6, 30, 13,  9, 42, 39, 43, 34, 33],
      dtype=int64), 'cur_cost': 85695.0}
2025-08-03 16:38:27,693 - experts.management.collaboration_manager - INFO - 个体 16 接受新路径 (成本: 85695.00)
2025-08-03 16:38:27,693 - experts.management.collaboration_manager - INFO - 为个体 17 生成探索路径
2025-08-03 16:38:27,694 - ExplorationExpert - INFO - 开始为个体 17 生成探索路径（算法实现）
2025-08-03 16:38:27,694 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:38:27,699 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:38:27,700 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:38:27,701 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14958.0, 路径长度: 66
2025-08-03 16:38:27,701 - experts.management.collaboration_manager - INFO - 个体 17 探索路径生成报告: {'new_tour': [0, 20, 1, 16, 18, 12, 22, 23, 13, 21, 19, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14958.0}
2025-08-03 16:38:27,702 - experts.management.collaboration_manager - INFO - 个体 17 接受新路径 (成本: 14958.00)
2025-08-03 16:38:27,702 - experts.management.collaboration_manager - INFO - 为个体 18 生成探索路径
2025-08-03 16:38:27,703 - ExplorationExpert - INFO - 开始为个体 18 生成探索路径（算法实现）
2025-08-03 16:38:27,703 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:38:27,719 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:38:27,721 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:38:27,722 - ExplorationExpert - INFO - 探索路径生成完成，成本: 59921.0, 路径长度: 66
2025-08-03 16:38:27,722 - experts.management.collaboration_manager - INFO - 个体 18 探索路径生成报告: {'new_tour': [22, 33, 3, 57, 58, 65, 9, 7, 61, 63, 21, 26, 23, 35, 36, 32, 5, 2, 56, 6, 54, 12, 8, 13, 14, 11, 60, 40, 41, 38, 46, 16, 19, 31, 27, 28, 17, 10, 18, 24, 29, 30, 4, 59, 62, 1, 34, 0, 20, 49, 45, 51, 48, 43, 44, 47, 37, 42, 39, 15, 25, 50, 53, 64, 52, 55], 'cur_cost': 59921.0}
2025-08-03 16:38:27,723 - experts.management.collaboration_manager - INFO - 个体 18 接受新路径 (成本: 59921.00)
2025-08-03 16:38:27,724 - experts.management.collaboration_manager - INFO - 为个体 19 生成利用路径
2025-08-03 16:38:27,724 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:38:27,725 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:38:27,726 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 19 处的路径，新成本: 118671.0
2025-08-03 16:38:27,808 - ExploitationExpert - INFO - res_population_num: 11
2025-08-03 16:38:27,808 - ExploitationExpert - INFO - res_population_costs: [9787.0, 9607.0, 9578.0, 9578, 9559.0, 9544, 9533, 9526, 9521, 9521, 9521]
2025-08-03 16:38:27,809 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 15, 14, 20, 21, 13, 19, 17, 12, 22, 23, 16,
       18, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 30, 28, 35, 34, 42, 48,
       43, 40, 49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57,
       64, 60, 58, 56, 59, 62, 53, 61, 55,  5,  4,  6,  2,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 41, 38, 51, 50, 45, 44, 39, 47, 49, 48, 46,
       42, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       30, 28, 35, 25, 26, 36, 37, 31, 33, 32, 29, 24, 27, 19, 18, 16, 23,
       13, 21, 20, 14, 15, 22, 12, 17,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       30, 28, 35, 25, 26, 36, 37, 31, 33, 32, 29, 24, 27, 19, 16, 18, 23,
       13, 21, 20, 14, 15, 22, 12, 17,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 32, 29, 33, 31, 24, 37, 25, 26, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-08-03 16:38:27,819 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:38:27,820 - ExploitationExpert - INFO - populations: [{'tour': [50, 22, 30, 12, 11, 54, 5, 14, 2, 60, 4, 8, 17, 20, 16, 3, 15, 37, 13, 24, 29, 19, 6, 55, 40, 18, 49, 44, 43, 28, 10, 56, 9, 58, 65, 7, 53, 0, 52, 57, 64, 21, 27, 33, 25, 34, 46, 47, 51, 38, 45, 23, 32, 36, 31, 35, 48, 42, 26, 41, 1, 63, 61, 59, 62, 39], 'cur_cost': 61220.0}, {'tour': array([40,  5, 34, 52, 43, 26, 58, 27, 11, 47, 48,  0, 44,  7, 33, 54, 15,
       20, 53, 23, 32, 42, 16, 14, 65,  2, 21, 57, 31, 46,  1, 22,  4, 36,
       45, 39, 64, 63, 12, 18, 49,  6, 24, 41,  3, 10, 13, 56, 29, 60, 38,
       30, 17, 19, 50, 51, 61, 62,  8, 28, 35, 25, 37, 55, 59,  9],
      dtype=int64), 'cur_cost': 111023.0}, {'tour': [0, 2, 16, 8, 5, 4, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12868.0}, {'tour': [0, 9, 21, 17, 12, 22, 23, 16, 18, 19, 13, 20, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12439.0}, {'tour': array([23, 24,  2, 17,  8, 44, 40, 38, 12, 62, 29, 47, 25, 53, 46, 39, 13,
       65, 26, 60, 63, 45, 19, 10, 54,  6, 59, 61, 16, 32,  1, 30, 37, 34,
       35,  0, 21, 42,  5, 55, 52, 58, 56, 48, 31,  7, 18,  4, 22, 27, 28,
       36, 11, 50, 15, 43, 64,  9, 49, 14, 20, 51,  3, 57, 41, 33],
      dtype=int64), 'cur_cost': 96684.0}, {'tour': [0, 19, 15, 20, 21, 13, 23, 16, 18, 12, 22, 17, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12503.0}, {'tour': [2, 15, 11, 54, 19, 20, 55, 61, 26, 13, 33, 24, 18, 44, 7, 17, 53, 57, 64, 27, 25, 47, 31, 38, 49, 41, 1, 63, 22, 40, 23, 0, 45, 6, 12, 5, 50, 60, 4, 48, 65, 35, 42, 59, 28, 37, 8, 3, 52, 62, 9, 21, 10, 16, 36, 30, 56, 46, 43, 51, 39, 32, 14, 34, 29, 58], 'cur_cost': 94392.0}, {'tour': array([ 4, 20, 23,  6, 33, 63, 17, 44, 65, 43, 58, 16, 59, 19, 49, 57, 10,
       27, 62, 60,  5, 21, 14, 29,  9, 37, 35, 47, 13, 24, 55, 39, 48,  7,
       64, 41, 25, 40, 36,  2,  0, 38,  8, 50, 52, 18, 22, 51, 15, 34, 42,
       30,  1, 26, 56,  3, 28, 11, 12, 46, 31, 54, 53, 32, 45, 61],
      dtype=int64), 'cur_cost': 117619.0}, {'tour': [2, 54, 5, 12, 20, 55, 16, 26, 27, 60, 19, 57, 33, 40, 52, 14, 43, 22, 23, 11, 53, 25, 35, 51, 29, 32, 36, 31, 30, 46, 41, 59, 50, 17, 7, 1, 56, 38, 8, 63, 28, 37, 49, 4, 15, 34, 24, 0, 48, 47, 44, 9, 10, 13, 18, 6, 39, 62, 3, 42, 64, 65, 45, 58, 21, 61], 'cur_cost': 97766.0}, {'tour': [19, 21, 12, 22, 2, 7, 1, 44, 27, 25, 24, 60, 34, 6, 54, 31, 18, 14, 43, 28, 11, 23, 20, 56, 57, 26, 46, 47, 52, 62, 45, 32, 15, 16, 41, 64, 63, 58, 5, 0, 48, 8, 37, 30, 59, 50, 35, 13, 65, 51, 61, 40, 9, 33, 49, 10, 53, 4, 36, 42, 55, 38, 3, 17, 29, 39], 'cur_cost': 103873.0}, {'tour': array([61, 20, 40, 62, 16, 37, 17, 22, 24, 15, 23, 30,  0, 11, 49, 53,  7,
       58,  2, 47, 54, 63, 52, 45, 25, 35,  6, 60, 51,  1, 38, 56, 13, 27,
       39, 57,  3, 43, 64, 46, 18, 33, 31, 19,  8, 65, 59, 42, 41,  9, 21,
       26, 32, 14, 48, 44, 28, 34, 29, 10, 50, 12, 36,  4,  5, 55],
      dtype=int64), 'cur_cost': 95369.0}, {'tour': [0, 9, 11, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12318.0}, {'tour': [0, 4, 17, 8, 2, 6, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12750.0}, {'tour': array([15, 45, 18,  1, 25, 34, 39, 38, 20, 55, 59, 61, 62,  3, 14,  2, 33,
       22, 65, 47, 35,  9,  7, 12, 58, 31, 46, 32, 64, 63,  8, 17, 19, 11,
       54, 42,  0, 57,  4, 50, 29, 44,  6, 30, 40, 10, 21, 27, 48, 60, 43,
       49, 16, 36, 51, 28, 37, 23, 41, 13, 56, 24, 52,  5, 26, 53],
      dtype=int64), 'cur_cost': 105705.0}, {'tour': [0, 12, 20, 21, 13, 23, 16, 18, 17, 22, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12357.0}, {'tour': [50, 2, 26, 4, 15, 52, 25, 14, 36, 35, 19, 31, 55, 24, 28, 10, 9, 58, 13, 20, 53, 48, 64, 33, 46, 65, 62, 45, 23, 18, 30, 1, 32, 40, 41, 0, 54, 3, 7, 43, 5, 17, 21, 6, 12, 57, 11, 59, 34, 63, 8, 47, 51, 56, 61, 29, 38, 44, 49, 16, 42, 37, 39, 27, 22, 60], 'cur_cost': 105944.0}, {'tour': array([54,  4, 53, 55,  2, 48, 29, 25, 10, 15,  1, 57, 59, 64, 11,  0,  5,
       24, 12, 19, 52,  8,  7, 38, 18, 44, 46, 31, 16, 37, 58, 63, 49, 22,
       40, 51, 26, 65, 60, 23, 41, 47, 61, 36, 50,  3, 17, 21, 56, 45, 20,
       32, 28, 27, 35, 62, 14,  6, 30, 13,  9, 42, 39, 43, 34, 33],
      dtype=int64), 'cur_cost': 85695.0}, {'tour': [0, 20, 1, 16, 18, 12, 22, 23, 13, 21, 19, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14958.0}, {'tour': [22, 33, 3, 57, 58, 65, 9, 7, 61, 63, 21, 26, 23, 35, 36, 32, 5, 2, 56, 6, 54, 12, 8, 13, 14, 11, 60, 40, 41, 38, 46, 16, 19, 31, 27, 28, 17, 10, 18, 24, 29, 30, 4, 59, 62, 1, 34, 0, 20, 49, 45, 51, 48, 43, 44, 47, 37, 42, 39, 15, 25, 50, 53, 64, 52, 55], 'cur_cost': 59921.0}, {'tour': array([11, 14, 22, 65, 40,  0, 48, 55, 10, 23, 42,  6, 20, 54, 27, 43, 47,
       61, 15, 53, 39, 35, 44, 28, 37,  8, 33, 13,  4, 38,  7, 12,  9, 52,
       56, 30,  2, 21, 64, 50, 18, 45,  5, 51, 36, 31, 25, 34, 49,  3, 17,
       46, 24, 58, 63, 26, 62, 29, 57,  1, 60, 16, 59, 19, 32, 41],
      dtype=int64), 'cur_cost': 118671.0}]
2025-08-03 16:38:27,825 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 16:38:27,826 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-03 16:38:27,826 - experts.management.collaboration_manager - INFO - 个体 19 利用路径生成报告: {'new_tour': array([11, 14, 22, 65, 40,  0, 48, 55, 10, 23, 42,  6, 20, 54, 27, 43, 47,
       61, 15, 53, 39, 35, 44, 28, 37,  8, 33, 13,  4, 38,  7, 12,  9, 52,
       56, 30,  2, 21, 64, 50, 18, 45,  5, 51, 36, 31, 25, 34, 49,  3, 17,
       46, 24, 58, 63, 26, 62, 29, 57,  1, 60, 16, 59, 19, 32, 41],
      dtype=int64), 'cur_cost': 118671.0}
2025-08-03 16:38:27,826 - experts.management.collaboration_manager - INFO - 个体 19 接受新路径 (成本: 118671.00)
2025-08-03 16:38:27,827 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 16, 'rejected': 4, 'elite_protected': 4}
2025-08-03 16:38:27,827 - experts.management.collaboration_manager - INFO - 接受率: 16/20 (80.0%)
2025-08-03 16:38:27,829 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [50, 22, 30, 12, 11, 54, 5, 14, 2, 60, 4, 8, 17, 20, 16, 3, 15, 37, 13, 24, 29, 19, 6, 55, 40, 18, 49, 44, 43, 28, 10, 56, 9, 58, 65, 7, 53, 0, 52, 57, 64, 21, 27, 33, 25, 34, 46, 47, 51, 38, 45, 23, 32, 36, 31, 35, 48, 42, 26, 41, 1, 63, 61, 59, 62, 39], 'cur_cost': 61220.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([40,  5, 34, 52, 43, 26, 58, 27, 11, 47, 48,  0, 44,  7, 33, 54, 15,
       20, 53, 23, 32, 42, 16, 14, 65,  2, 21, 57, 31, 46,  1, 22,  4, 36,
       45, 39, 64, 63, 12, 18, 49,  6, 24, 41,  3, 10, 13, 56, 29, 60, 38,
       30, 17, 19, 50, 51, 61, 62,  8, 28, 35, 25, 37, 55, 59,  9],
      dtype=int64), 'cur_cost': 111023.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 16, 8, 5, 4, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12868.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 21, 17, 12, 22, 23, 16, 18, 19, 13, 20, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12439.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([23, 24,  2, 17,  8, 44, 40, 38, 12, 62, 29, 47, 25, 53, 46, 39, 13,
       65, 26, 60, 63, 45, 19, 10, 54,  6, 59, 61, 16, 32,  1, 30, 37, 34,
       35,  0, 21, 42,  5, 55, 52, 58, 56, 48, 31,  7, 18,  4, 22, 27, 28,
       36, 11, 50, 15, 43, 64,  9, 49, 14, 20, 51,  3, 57, 41, 33],
      dtype=int64), 'cur_cost': 96684.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 15, 20, 21, 13, 23, 16, 18, 12, 22, 17, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12503.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [2, 15, 11, 54, 19, 20, 55, 61, 26, 13, 33, 24, 18, 44, 7, 17, 53, 57, 64, 27, 25, 47, 31, 38, 49, 41, 1, 63, 22, 40, 23, 0, 45, 6, 12, 5, 50, 60, 4, 48, 65, 35, 42, 59, 28, 37, 8, 3, 52, 62, 9, 21, 10, 16, 36, 30, 56, 46, 43, 51, 39, 32, 14, 34, 29, 58], 'cur_cost': 94392.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 4, 20, 23,  6, 33, 63, 17, 44, 65, 43, 58, 16, 59, 19, 49, 57, 10,
       27, 62, 60,  5, 21, 14, 29,  9, 37, 35, 47, 13, 24, 55, 39, 48,  7,
       64, 41, 25, 40, 36,  2,  0, 38,  8, 50, 52, 18, 22, 51, 15, 34, 42,
       30,  1, 26, 56,  3, 28, 11, 12, 46, 31, 54, 53, 32, 45, 61],
      dtype=int64), 'cur_cost': 117619.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [2, 54, 5, 12, 20, 55, 16, 26, 27, 60, 19, 57, 33, 40, 52, 14, 43, 22, 23, 11, 53, 25, 35, 51, 29, 32, 36, 31, 30, 46, 41, 59, 50, 17, 7, 1, 56, 38, 8, 63, 28, 37, 49, 4, 15, 34, 24, 0, 48, 47, 44, 9, 10, 13, 18, 6, 39, 62, 3, 42, 64, 65, 45, 58, 21, 61], 'cur_cost': 97766.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [19, 21, 12, 22, 2, 7, 1, 44, 27, 25, 24, 60, 34, 6, 54, 31, 18, 14, 43, 28, 11, 23, 20, 56, 57, 26, 46, 47, 52, 62, 45, 32, 15, 16, 41, 64, 63, 58, 5, 0, 48, 8, 37, 30, 59, 50, 35, 13, 65, 51, 61, 40, 9, 33, 49, 10, 53, 4, 36, 42, 55, 38, 3, 17, 29, 39], 'cur_cost': 103873.0}}, {'individual': 10, 'strategy': 'exploit', 'path_data': {'new_tour': array([61, 20, 40, 62, 16, 37, 17, 22, 24, 15, 23, 30,  0, 11, 49, 53,  7,
       58,  2, 47, 54, 63, 52, 45, 25, 35,  6, 60, 51,  1, 38, 56, 13, 27,
       39, 57,  3, 43, 64, 46, 18, 33, 31, 19,  8, 65, 59, 42, 41,  9, 21,
       26, 32, 14, 48, 44, 28, 34, 29, 10, 50, 12, 36,  4,  5, 55],
      dtype=int64), 'cur_cost': 95369.0}}, {'individual': 11, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 11, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12318.0}}, {'individual': 12, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 17, 8, 2, 6, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12750.0}}, {'individual': 13, 'strategy': 'exploit', 'path_data': {'new_tour': array([15, 45, 18,  1, 25, 34, 39, 38, 20, 55, 59, 61, 62,  3, 14,  2, 33,
       22, 65, 47, 35,  9,  7, 12, 58, 31, 46, 32, 64, 63,  8, 17, 19, 11,
       54, 42,  0, 57,  4, 50, 29, 44,  6, 30, 40, 10, 21, 27, 48, 60, 43,
       49, 16, 36, 51, 28, 37, 23, 41, 13, 56, 24, 52,  5, 26, 53],
      dtype=int64), 'cur_cost': 105705.0}}, {'individual': 14, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 20, 21, 13, 23, 16, 18, 17, 22, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12357.0}}, {'individual': 15, 'strategy': 'explore', 'path_data': {'new_tour': [50, 2, 26, 4, 15, 52, 25, 14, 36, 35, 19, 31, 55, 24, 28, 10, 9, 58, 13, 20, 53, 48, 64, 33, 46, 65, 62, 45, 23, 18, 30, 1, 32, 40, 41, 0, 54, 3, 7, 43, 5, 17, 21, 6, 12, 57, 11, 59, 34, 63, 8, 47, 51, 56, 61, 29, 38, 44, 49, 16, 42, 37, 39, 27, 22, 60], 'cur_cost': 105944.0}}, {'individual': 16, 'strategy': 'exploit', 'path_data': {'new_tour': array([54,  4, 53, 55,  2, 48, 29, 25, 10, 15,  1, 57, 59, 64, 11,  0,  5,
       24, 12, 19, 52,  8,  7, 38, 18, 44, 46, 31, 16, 37, 58, 63, 49, 22,
       40, 51, 26, 65, 60, 23, 41, 47, 61, 36, 50,  3, 17, 21, 56, 45, 20,
       32, 28, 27, 35, 62, 14,  6, 30, 13,  9, 42, 39, 43, 34, 33],
      dtype=int64), 'cur_cost': 85695.0}}, {'individual': 17, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 1, 16, 18, 12, 22, 23, 13, 21, 19, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14958.0}}, {'individual': 18, 'strategy': 'explore', 'path_data': {'new_tour': [22, 33, 3, 57, 58, 65, 9, 7, 61, 63, 21, 26, 23, 35, 36, 32, 5, 2, 56, 6, 54, 12, 8, 13, 14, 11, 60, 40, 41, 38, 46, 16, 19, 31, 27, 28, 17, 10, 18, 24, 29, 30, 4, 59, 62, 1, 34, 0, 20, 49, 45, 51, 48, 43, 44, 47, 37, 42, 39, 15, 25, 50, 53, 64, 52, 55], 'cur_cost': 59921.0}}, {'individual': 19, 'strategy': 'exploit', 'path_data': {'new_tour': array([11, 14, 22, 65, 40,  0, 48, 55, 10, 23, 42,  6, 20, 54, 27, 43, 47,
       61, 15, 53, 39, 35, 44, 28, 37,  8, 33, 13,  4, 38,  7, 12,  9, 52,
       56, 30,  2, 21, 64, 50, 18, 45,  5, 51, 36, 31, 25, 34, 49,  3, 17,
       46, 24, 58, 63, 26, 62, 29, 57,  1, 60, 16, 59, 19, 32, 41],
      dtype=int64), 'cur_cost': 118671.0}}]
2025-08-03 16:38:27,833 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-03 16:38:27,833 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:38:27,845 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=12318.000, 多样性=0.950
2025-08-03 16:38:27,845 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-03 16:38:27,846 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-03 16:38:27,846 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-03 16:38:27,850 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 1, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.053741720487814254, 'best_improvement': -0.24941677654934577}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.022652659225213444}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 11, 'new_count': 11, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.8807162534435261, 'new_diversity': 0.8807162534435261, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-03 16:38:27,856 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-03 16:38:27,863 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_solution.json
2025-08-03 16:38:27,864 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_20250803_163827.solution
2025-08-03 16:38:27,865 - __main__ - INFO - 实例 composite13_66 处理完成
