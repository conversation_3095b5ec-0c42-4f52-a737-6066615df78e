2025-08-05 10:28:37,166 - __main__ - INFO - geometry1_10 开始进化第 1 代
2025-08-05 10:28:37,166 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 10:28:37,166 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:37,167 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=130.000, 多样性=0.891
2025-08-05 10:28:37,168 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:37,169 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.891
2025-08-05 10:28:37,170 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:37,172 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 10:28:37,172 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:37,172 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 10:28:37,172 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 10:28:37,178 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.300, 适应度梯度: -3.840, 聚类评分: 0.000, 覆盖率: 0.034, 收敛趋势: 0.000, 多样性: 0.891
2025-08-05 10:28:37,178 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 10:28:37,178 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 10:28:37,178 - visualization.landscape_visualizer - INFO - 设置当前实例名: geometry1_10
2025-08-05 10:28:37,185 - visualization.landscape_visualizer - INFO - 插值约束: 307 个点被约束到最小值 130.00
2025-08-05 10:28:37,187 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=5.3%, 梯度: 2.57 → 2.43
2025-08-05 10:28:37,316 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry1_10\landscape_geometry1_10_iter_31_20250805_102837.html
2025-08-05 10:28:37,392 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry1_10\dashboard_geometry1_10_iter_31_20250805_102837.html
2025-08-05 10:28:37,392 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 31
2025-08-05 10:28:37,392 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 10:28:37,392 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2201秒
2025-08-05 10:28:37,393 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 62, 'max_size': 500, 'hits': 0, 'misses': 62, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 96, 'max_size': 100, 'hits': 214, 'misses': 96, 'hit_rate': 0.6903225806451613, 'evictions': 0, 'ttl': 7200}}
2025-08-05 10:28:37,393 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -3.840000000000001, 'local_optima_density': 0.3, 'gradient_variance': 873.7103999999999, 'cluster_count': 0}, 'population_state': {'diversity': 0.8911111111111112, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0337, 'fitness_entropy': 0.9464119282150146, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -3.840)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.034)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.891)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360917.1781843, 'performance_metrics': {}}}
2025-08-05 10:28:37,393 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:37,393 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:37,393 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:37,393 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:37,394 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:28:37,394 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 10:28:37,394 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:28:37,394 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:37,394 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:37,394 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:28:37,394 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:37,394 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:37,394 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:37,395 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:37,395 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:37,395 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:37,395 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:37,395 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,396 - ExplorationExpert - INFO - 探索路径生成完成，成本: 182.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:37,396 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [9, 8, 3, 7, 4, 1, 6, 0, 5, 2], 'cur_cost': 182.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:37,396 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 182.00)
2025-08-05 10:28:37,396 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:37,396 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:37,396 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:37,397 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:37,397 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,397 - ExplorationExpert - INFO - 探索路径生成完成，成本: 145.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:37,397 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [1, 8, 0, 6, 9, 7, 4, 3, 5, 2], 'cur_cost': 145.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:37,397 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 145.00)
2025-08-05 10:28:37,397 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:37,397 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:37,397 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:37,398 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:37,398 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,398 - ExplorationExpert - INFO - 探索路径生成完成，成本: 152.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:37,398 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [1, 9, 6, 5, 3, 2, 8, 7, 0, 4], 'cur_cost': 152.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:37,398 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 152.00)
2025-08-05 10:28:37,398 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:37,399 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:37,399 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:37,399 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:37,399 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,399 - ExplorationExpert - INFO - 探索路径生成完成，成本: 176.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:37,400 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 7, 9, 1, 8, 3, 6, 2, 4, 5], 'cur_cost': 176.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:37,400 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 176.00)
2025-08-05 10:28:37,400 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:37,400 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:37,400 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:37,400 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:37,401 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,401 - ExplorationExpert - INFO - 探索路径生成完成，成本: 143.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:37,401 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [3, 6, 7, 8, 1, 5, 2, 9, 0, 4], 'cur_cost': 143.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:37,401 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 143.00)
2025-08-05 10:28:37,402 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:37,402 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:37,402 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:37,403 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:37,403 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,403 - ExplorationExpert - INFO - 探索路径生成完成，成本: 189.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:37,403 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [1, 3, 0, 2, 5, 6, 7, 8, 9, 4], 'cur_cost': 189.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:37,404 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 189.00)
2025-08-05 10:28:37,404 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:28:37,404 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:37,404 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:37,404 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 168.0
2025-08-05 10:28:37,413 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:37,413 - ExploitationExpert - INFO - res_population_costs: [130.0, 130, 130, 130]
2025-08-05 10:28:37,413 - ExploitationExpert - INFO - res_populations: [array([0, 8, 1, 9, 2, 5, 6, 3, 4, 7], dtype=int64), array([0, 1, 9, 2, 5, 6, 3, 4, 7, 8], dtype=int64), array([0, 4, 3, 2, 5, 6, 7, 8, 9, 1], dtype=int64), array([0, 7, 8, 9, 1, 2, 5, 6, 3, 4], dtype=int64)]
2025-08-05 10:28:37,414 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:37,415 - ExploitationExpert - INFO - populations: [{'tour': [9, 8, 3, 7, 4, 1, 6, 0, 5, 2], 'cur_cost': 182.0}, {'tour': [1, 8, 0, 6, 9, 7, 4, 3, 5, 2], 'cur_cost': 145.0}, {'tour': [1, 9, 6, 5, 3, 2, 8, 7, 0, 4], 'cur_cost': 152.0}, {'tour': [0, 7, 9, 1, 8, 3, 6, 2, 4, 5], 'cur_cost': 176.0}, {'tour': [3, 6, 7, 8, 1, 5, 2, 9, 0, 4], 'cur_cost': 143.0}, {'tour': [1, 3, 0, 2, 5, 6, 7, 8, 9, 4], 'cur_cost': 189.0}, {'tour': array([7, 1, 8, 5, 2, 0, 4, 6, 9, 3], dtype=int64), 'cur_cost': 168.0}, {'tour': array([7, 3, 6, 8, 5, 4, 0, 9, 1, 2], dtype=int64), 'cur_cost': 165.0}, {'tour': array([3, 9, 2, 4, 8, 6, 7, 1, 5, 0], dtype=int64), 'cur_cost': 192.0}, {'tour': array([8, 0, 3, 6, 5, 9, 7, 2, 4, 1], dtype=int64), 'cur_cost': 180.0}]
2025-08-05 10:28:37,416 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:37,416 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 79, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 79, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:37,416 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([7, 1, 8, 5, 2, 0, 4, 6, 9, 3], dtype=int64), 'cur_cost': 168.0, 'intermediate_solutions': [{'tour': array([4, 7, 3, 1, 8, 2, 6, 5, 0, 9], dtype=int64), 'cur_cost': 185.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 4, 7, 3, 8, 2, 6, 5, 0, 9], dtype=int64), 'cur_cost': 185.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 1, 4, 7, 3, 2, 6, 5, 0, 9], dtype=int64), 'cur_cost': 170.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 1, 4, 7, 8, 2, 6, 5, 0, 9], dtype=int64), 'cur_cost': 188.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 8, 1, 4, 7, 2, 6, 5, 0, 9], dtype=int64), 'cur_cost': 188.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:37,416 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 168.00)
2025-08-05 10:28:37,416 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:37,417 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:37,417 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:37,417 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:37,417 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,418 - ExplorationExpert - INFO - 探索路径生成完成，成本: 146.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:37,418 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [4, 9, 5, 7, 6, 8, 0, 1, 2, 3], 'cur_cost': 146.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:37,418 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 146.00)
2025-08-05 10:28:37,418 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:37,418 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:37,418 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:37,418 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 184.0
2025-08-05 10:28:37,426 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 10:28:37,427 - ExploitationExpert - INFO - res_population_costs: [130.0, 130, 130, 130, 130.0, 130, 130]
2025-08-05 10:28:37,427 - ExploitationExpert - INFO - res_populations: [array([0, 8, 1, 9, 2, 5, 6, 3, 4, 7], dtype=int64), array([0, 1, 9, 2, 5, 6, 3, 4, 7, 8], dtype=int64), array([0, 4, 3, 2, 5, 6, 7, 8, 9, 1], dtype=int64), array([0, 7, 8, 9, 1, 2, 5, 6, 3, 4], dtype=int64), array([0, 7, 6, 4, 3, 5, 2, 9, 8, 1], dtype=int64), array([0, 4, 3, 6, 7, 8, 9, 5, 2, 1], dtype=int64), array([0, 1, 9, 5, 2, 3, 4, 6, 7, 8], dtype=int64)]
2025-08-05 10:28:37,429 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:37,429 - ExploitationExpert - INFO - populations: [{'tour': [9, 8, 3, 7, 4, 1, 6, 0, 5, 2], 'cur_cost': 182.0}, {'tour': [1, 8, 0, 6, 9, 7, 4, 3, 5, 2], 'cur_cost': 145.0}, {'tour': [1, 9, 6, 5, 3, 2, 8, 7, 0, 4], 'cur_cost': 152.0}, {'tour': [0, 7, 9, 1, 8, 3, 6, 2, 4, 5], 'cur_cost': 176.0}, {'tour': [3, 6, 7, 8, 1, 5, 2, 9, 0, 4], 'cur_cost': 143.0}, {'tour': [1, 3, 0, 2, 5, 6, 7, 8, 9, 4], 'cur_cost': 189.0}, {'tour': array([7, 1, 8, 5, 2, 0, 4, 6, 9, 3], dtype=int64), 'cur_cost': 168.0}, {'tour': [4, 9, 5, 7, 6, 8, 0, 1, 2, 3], 'cur_cost': 146.0}, {'tour': array([2, 1, 7, 5, 8, 4, 6, 9, 3, 0], dtype=int64), 'cur_cost': 184.0}, {'tour': array([8, 0, 3, 6, 5, 9, 7, 2, 4, 1], dtype=int64), 'cur_cost': 180.0}]
2025-08-05 10:28:37,430 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:37,430 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 80, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 80, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:37,431 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([2, 1, 7, 5, 8, 4, 6, 9, 3, 0], dtype=int64), 'cur_cost': 184.0, 'intermediate_solutions': [{'tour': array([2, 9, 3, 4, 8, 6, 7, 1, 5, 0], dtype=int64), 'cur_cost': 180.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 2, 9, 3, 8, 6, 7, 1, 5, 0], dtype=int64), 'cur_cost': 182.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 4, 2, 9, 3, 6, 7, 1, 5, 0], dtype=int64), 'cur_cost': 175.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 4, 2, 9, 8, 6, 7, 1, 5, 0], dtype=int64), 'cur_cost': 180.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 8, 4, 2, 9, 6, 7, 1, 5, 0], dtype=int64), 'cur_cost': 195.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:37,431 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 184.00)
2025-08-05 10:28:37,431 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:37,431 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:37,432 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:37,432 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:37,433 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,433 - ExplorationExpert - INFO - 探索路径生成完成，成本: 164.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:37,433 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [4, 5, 0, 1, 2, 7, 6, 8, 9, 3], 'cur_cost': 164.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:37,433 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 164.00)
2025-08-05 10:28:37,433 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:37,433 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:37,435 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [9, 8, 3, 7, 4, 1, 6, 0, 5, 2], 'cur_cost': 182.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [1, 8, 0, 6, 9, 7, 4, 3, 5, 2], 'cur_cost': 145.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [1, 9, 6, 5, 3, 2, 8, 7, 0, 4], 'cur_cost': 152.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 9, 1, 8, 3, 6, 2, 4, 5], 'cur_cost': 176.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [3, 6, 7, 8, 1, 5, 2, 9, 0, 4], 'cur_cost': 143.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [1, 3, 0, 2, 5, 6, 7, 8, 9, 4], 'cur_cost': 189.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 1, 8, 5, 2, 0, 4, 6, 9, 3], dtype=int64), 'cur_cost': 168.0, 'intermediate_solutions': [{'tour': array([4, 7, 3, 1, 8, 2, 6, 5, 0, 9], dtype=int64), 'cur_cost': 185.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 4, 7, 3, 8, 2, 6, 5, 0, 9], dtype=int64), 'cur_cost': 185.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 1, 4, 7, 3, 2, 6, 5, 0, 9], dtype=int64), 'cur_cost': 170.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 1, 4, 7, 8, 2, 6, 5, 0, 9], dtype=int64), 'cur_cost': 188.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 8, 1, 4, 7, 2, 6, 5, 0, 9], dtype=int64), 'cur_cost': 188.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [4, 9, 5, 7, 6, 8, 0, 1, 2, 3], 'cur_cost': 146.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 1, 7, 5, 8, 4, 6, 9, 3, 0], dtype=int64), 'cur_cost': 184.0, 'intermediate_solutions': [{'tour': array([2, 9, 3, 4, 8, 6, 7, 1, 5, 0], dtype=int64), 'cur_cost': 180.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 2, 9, 3, 8, 6, 7, 1, 5, 0], dtype=int64), 'cur_cost': 182.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 4, 2, 9, 3, 6, 7, 1, 5, 0], dtype=int64), 'cur_cost': 175.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 4, 2, 9, 8, 6, 7, 1, 5, 0], dtype=int64), 'cur_cost': 180.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 8, 4, 2, 9, 6, 7, 1, 5, 0], dtype=int64), 'cur_cost': 195.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [4, 5, 0, 1, 2, 7, 6, 8, 9, 3], 'cur_cost': 164.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:37,436 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:37,436 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:37,438 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=143.000, 多样性=0.911
2025-08-05 10:28:37,438 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 10:28:37,438 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 10:28:37,439 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:37,439 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.040276686570291244, 'best_improvement': -0.1}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.02244389027431398}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.012145984887223377, 'recent_improvements': [-0.020660905672142554, -0.021477786091970912, -0.044952875446589304], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 7, 'new_count': 7, 'count_change': 0, 'old_best_cost': 130.0, 'new_best_cost': 130.0, 'quality_improvement': 0.0, 'old_diversity': 0.819047619047619, 'new_diversity': 0.819047619047619, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配']}
2025-08-05 10:28:37,440 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 10:28:37,440 - __main__ - INFO - geometry1_10 开始进化第 2 代
2025-08-05 10:28:37,440 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 10:28:37,441 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:37,442 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=143.000, 多样性=0.911
2025-08-05 10:28:37,442 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:37,443 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.911
2025-08-05 10:28:37,443 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:37,445 - EliteExpert - INFO - 精英解分析完成: 精英解数量=7, 多样性=0.819
2025-08-05 10:28:37,448 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 10:28:37,448 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:37,448 - LandscapeExpert - INFO - 添加精英解数据: 7个精英解
2025-08-05 10:28:37,448 - LandscapeExpert - INFO - 数据提取成功: 17个路径, 17个适应度值
2025-08-05 10:28:37,461 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.412, 适应度梯度: -0.059, 聚类评分: 0.000, 覆盖率: 0.035, 收敛趋势: 0.000, 多样性: 0.526
2025-08-05 10:28:37,462 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 10:28:37,462 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:37,462 - visualization.landscape_visualizer - INFO - 设置当前实例名: geometry1_10
2025-08-05 10:28:37,467 - visualization.landscape_visualizer - INFO - 插值约束: 200 个点被约束到最小值 130.00
2025-08-05 10:28:37,470 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=4.5%, 梯度: 2.12 → 2.02
2025-08-05 10:28:37,575 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry1_10\landscape_geometry1_10_iter_32_20250805_102837.html
2025-08-05 10:28:37,640 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry1_10\dashboard_geometry1_10_iter_32_20250805_102837.html
2025-08-05 10:28:37,640 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 32
2025-08-05 10:28:37,640 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 10:28:37,640 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1927秒
2025-08-05 10:28:37,640 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.4117647058823529, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -0.0588235294117645, 'local_optima_density': 0.4117647058823529, 'gradient_variance': 585.1800692041521, 'cluster_count': 0}, 'population_state': {'diversity': 0.5259515570934257, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0354, 'fitness_entropy': 0.8447156146017466, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -0.059)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.035)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360917.4623835, 'performance_metrics': {}}}
2025-08-05 10:28:37,642 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:37,642 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:37,642 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:37,642 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:37,642 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 10:28:37,642 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 10:28:37,642 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 10:28:37,642 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:37,642 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:37,642 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 10:28:37,642 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:37,642 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:37,642 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 4} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:37,642 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:37,642 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:37,644 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:37,644 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:37,644 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,644 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,644 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,645 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,645 - ExplorationExpert - INFO - 探索路径生成完成，成本: 183.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:37,645 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 6, 3, 8, 4, 1, 2, 7, 5, 9], 'cur_cost': 183.0, 'intermediate_solutions': [{'tour': [9, 8, 3, 7, 4, 0, 6, 1, 5, 2], 'cur_cost': 167.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 8, 3, 7, 4, 1, 0, 6, 5, 2], 'cur_cost': 164.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 8, 0, 3, 7, 4, 1, 6, 5, 2], 'cur_cost': 170.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:37,645 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 183.00)
2025-08-05 10:28:37,646 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:37,646 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:37,646 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:37,647 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:37,647 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,647 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,647 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,647 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,647 - ExplorationExpert - INFO - 探索路径生成完成，成本: 145.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:37,647 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [9, 5, 8, 0, 4, 6, 7, 1, 2, 3], 'cur_cost': 145.0, 'intermediate_solutions': [{'tour': [1, 8, 0, 9, 6, 7, 4, 3, 5, 2], 'cur_cost': 141.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 0, 7, 9, 6, 4, 3, 5, 2], 'cur_cost': 138.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 8, 0, 6, 9, 7, 4, 3, 5, 2], 'cur_cost': 145.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:37,647 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 145.00)
2025-08-05 10:28:37,647 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:37,647 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:37,647 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:37,647 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:37,649 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,649 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,649 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,649 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,649 - ExplorationExpert - INFO - 探索路径生成完成，成本: 155.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:37,650 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [1, 8, 9, 6, 3, 7, 5, 2, 4, 0], 'cur_cost': 155.0, 'intermediate_solutions': [{'tour': [1, 5, 6, 9, 3, 2, 8, 7, 0, 4], 'cur_cost': 164.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 9, 6, 3, 5, 2, 8, 7, 0, 4], 'cur_cost': 152.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 1, 9, 6, 5, 3, 2, 7, 0, 4], 'cur_cost': 149.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:37,650 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 155.00)
2025-08-05 10:28:37,650 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:37,650 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:37,650 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:37,650 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:37,651 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,651 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,651 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,651 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,651 - ExplorationExpert - INFO - 探索路径生成完成，成本: 145.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:37,651 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [4, 0, 1, 9, 8, 7, 5, 6, 3, 2], 'cur_cost': 145.0, 'intermediate_solutions': [{'tour': [0, 7, 9, 8, 1, 3, 6, 2, 4, 5], 'cur_cost': 179.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 9, 7, 8, 3, 6, 2, 4, 5], 'cur_cost': 176.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 9, 1, 8, 3, 2, 6, 4, 5], 'cur_cost': 164.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:37,651 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 145.00)
2025-08-05 10:28:37,651 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:37,651 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:37,651 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:37,652 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:37,652 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,652 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,652 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,653 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,653 - ExplorationExpert - INFO - 探索路径生成完成，成本: 156.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:37,653 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [3, 7, 5, 9, 1, 8, 6, 4, 0, 2], 'cur_cost': 156.0, 'intermediate_solutions': [{'tour': [3, 6, 7, 8, 1, 5, 2, 4, 0, 9], 'cur_cost': 161.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 6, 7, 8, 1, 2, 5, 9, 0, 4], 'cur_cost': 137.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 6, 7, 8, 1, 5, 2, 9, 0, 4], 'cur_cost': 143.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:37,653 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 156.00)
2025-08-05 10:28:37,653 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:28:37,653 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:37,655 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:37,655 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 145.0
2025-08-05 10:28:37,661 - ExploitationExpert - INFO - res_population_num: 12
2025-08-05 10:28:37,661 - ExploitationExpert - INFO - res_population_costs: [130.0, 130, 130, 130, 130.0, 130, 130, 130.0, 130, 130, 130, 130]
2025-08-05 10:28:37,661 - ExploitationExpert - INFO - res_populations: [array([0, 8, 1, 9, 2, 5, 6, 3, 4, 7], dtype=int64), array([0, 1, 9, 2, 5, 6, 3, 4, 7, 8], dtype=int64), array([0, 4, 3, 2, 5, 6, 7, 8, 9, 1], dtype=int64), array([0, 7, 8, 9, 1, 2, 5, 6, 3, 4], dtype=int64), array([0, 7, 6, 4, 3, 5, 2, 9, 8, 1], dtype=int64), array([0, 4, 3, 6, 7, 8, 9, 5, 2, 1], dtype=int64), array([0, 1, 9, 5, 2, 3, 4, 6, 7, 8], dtype=int64), array([0, 1, 2, 9, 5, 3, 6, 4, 7, 8], dtype=int64), array([0, 7, 8, 1, 2, 9, 5, 3, 6, 4], dtype=int64), array([0, 7, 8, 1, 9, 2, 5, 3, 6, 4], dtype=int64), array([0, 8, 1, 9, 2, 5, 3, 4, 6, 7], dtype=int64), array([0, 8, 1, 9, 5, 2, 3, 6, 4, 7], dtype=int64)]
2025-08-05 10:28:37,666 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:37,666 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 3, 8, 4, 1, 2, 7, 5, 9], 'cur_cost': 183.0}, {'tour': [9, 5, 8, 0, 4, 6, 7, 1, 2, 3], 'cur_cost': 145.0}, {'tour': [1, 8, 9, 6, 3, 7, 5, 2, 4, 0], 'cur_cost': 155.0}, {'tour': [4, 0, 1, 9, 8, 7, 5, 6, 3, 2], 'cur_cost': 145.0}, {'tour': [3, 7, 5, 9, 1, 8, 6, 4, 0, 2], 'cur_cost': 156.0}, {'tour': array([6, 2, 8, 1, 9, 5, 7, 0, 4, 3], dtype=int64), 'cur_cost': 145.0}, {'tour': [7, 1, 8, 5, 2, 0, 4, 6, 9, 3], 'cur_cost': 168.0}, {'tour': [4, 9, 5, 7, 6, 8, 0, 1, 2, 3], 'cur_cost': 146.0}, {'tour': [2, 1, 7, 5, 8, 4, 6, 9, 3, 0], 'cur_cost': 184.0}, {'tour': [4, 5, 0, 1, 2, 7, 6, 8, 9, 3], 'cur_cost': 164.0}]
2025-08-05 10:28:37,667 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:37,667 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 81, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 81, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:37,667 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([6, 2, 8, 1, 9, 5, 7, 0, 4, 3], dtype=int64), 'cur_cost': 145.0, 'intermediate_solutions': [{'tour': array([0, 3, 1, 2, 5, 6, 7, 8, 9, 4]), 'cur_cost': 164.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 0, 3, 1, 5, 6, 7, 8, 9, 4]), 'cur_cost': 195.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 2, 0, 3, 1, 6, 7, 8, 9, 4]), 'cur_cost': 192.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 2, 0, 3, 5, 6, 7, 8, 9, 4]), 'cur_cost': 177.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 5, 2, 0, 3, 6, 7, 8, 9, 4]), 'cur_cost': 183.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:37,667 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 145.00)
2025-08-05 10:28:37,667 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:37,667 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:37,669 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:37,669 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:37,669 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,669 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,669 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,669 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,669 - ExplorationExpert - INFO - 探索路径生成完成，成本: 154.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:37,669 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [4, 8, 9, 5, 3, 2, 6, 7, 0, 1], 'cur_cost': 154.0, 'intermediate_solutions': [{'tour': [7, 1, 8, 5, 2, 0, 4, 9, 6, 3], 'cur_cost': 171.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 6, 4, 0, 2, 5, 8, 1, 7, 3], 'cur_cost': 168.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 8, 5, 1, 2, 0, 4, 6, 9, 3], 'cur_cost': 168.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:37,670 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 154.00)
2025-08-05 10:28:37,670 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:37,670 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:37,670 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:37,670 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:37,671 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,671 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,671 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,671 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,671 - ExplorationExpert - INFO - 探索路径生成完成，成本: 179.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:37,671 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [1, 3, 8, 4, 9, 5, 6, 7, 0, 2], 'cur_cost': 179.0, 'intermediate_solutions': [{'tour': [4, 9, 5, 7, 6, 8, 0, 3, 2, 1], 'cur_cost': 171.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 9, 5, 7, 6, 8, 0, 1, 3, 2], 'cur_cost': 170.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 9, 5, 7, 6, 8, 0, 1, 2, 3], 'cur_cost': 146.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:37,672 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 179.00)
2025-08-05 10:28:37,672 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:37,672 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:37,672 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:37,672 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 176.0
2025-08-05 10:28:37,682 - ExploitationExpert - INFO - res_population_num: 14
2025-08-05 10:28:37,682 - ExploitationExpert - INFO - res_population_costs: [130.0, 130, 130, 130, 130.0, 130, 130, 130.0, 130, 130, 130, 130, 130.0, 130]
2025-08-05 10:28:37,682 - ExploitationExpert - INFO - res_populations: [array([0, 8, 1, 9, 2, 5, 6, 3, 4, 7], dtype=int64), array([0, 1, 9, 2, 5, 6, 3, 4, 7, 8], dtype=int64), array([0, 4, 3, 2, 5, 6, 7, 8, 9, 1], dtype=int64), array([0, 7, 8, 9, 1, 2, 5, 6, 3, 4], dtype=int64), array([0, 7, 6, 4, 3, 5, 2, 9, 8, 1], dtype=int64), array([0, 4, 3, 6, 7, 8, 9, 5, 2, 1], dtype=int64), array([0, 1, 9, 5, 2, 3, 4, 6, 7, 8], dtype=int64), array([0, 1, 2, 9, 5, 3, 6, 4, 7, 8], dtype=int64), array([0, 7, 8, 1, 2, 9, 5, 3, 6, 4], dtype=int64), array([0, 7, 8, 1, 9, 2, 5, 3, 6, 4], dtype=int64), array([0, 8, 1, 9, 2, 5, 3, 4, 6, 7], dtype=int64), array([0, 8, 1, 9, 5, 2, 3, 6, 4, 7], dtype=int64), array([0, 1, 9, 2, 3, 5, 6, 4, 7, 8], dtype=int64), array([0, 7, 8, 9, 1, 2, 5, 3, 6, 4], dtype=int64)]
2025-08-05 10:28:37,687 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:37,687 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 3, 8, 4, 1, 2, 7, 5, 9], 'cur_cost': 183.0}, {'tour': [9, 5, 8, 0, 4, 6, 7, 1, 2, 3], 'cur_cost': 145.0}, {'tour': [1, 8, 9, 6, 3, 7, 5, 2, 4, 0], 'cur_cost': 155.0}, {'tour': [4, 0, 1, 9, 8, 7, 5, 6, 3, 2], 'cur_cost': 145.0}, {'tour': [3, 7, 5, 9, 1, 8, 6, 4, 0, 2], 'cur_cost': 156.0}, {'tour': array([6, 2, 8, 1, 9, 5, 7, 0, 4, 3], dtype=int64), 'cur_cost': 145.0}, {'tour': [4, 8, 9, 5, 3, 2, 6, 7, 0, 1], 'cur_cost': 154.0}, {'tour': [1, 3, 8, 4, 9, 5, 6, 7, 0, 2], 'cur_cost': 179.0}, {'tour': array([0, 5, 8, 9, 1, 7, 2, 4, 3, 6], dtype=int64), 'cur_cost': 176.0}, {'tour': [4, 5, 0, 1, 2, 7, 6, 8, 9, 3], 'cur_cost': 164.0}]
2025-08-05 10:28:37,687 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:37,687 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 82, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 82, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:37,689 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([0, 5, 8, 9, 1, 7, 2, 4, 3, 6], dtype=int64), 'cur_cost': 176.0, 'intermediate_solutions': [{'tour': array([7, 1, 2, 5, 8, 4, 6, 9, 3, 0]), 'cur_cost': 168.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 7, 1, 2, 8, 4, 6, 9, 3, 0]), 'cur_cost': 183.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 5, 7, 1, 2, 4, 6, 9, 3, 0]), 'cur_cost': 177.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 5, 7, 1, 8, 4, 6, 9, 3, 0]), 'cur_cost': 181.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 8, 5, 7, 1, 4, 6, 9, 3, 0]), 'cur_cost': 196.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:37,689 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 176.00)
2025-08-05 10:28:37,689 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:37,689 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:37,690 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:37,690 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:37,690 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,690 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,691 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,691 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,691 - ExplorationExpert - INFO - 探索路径生成完成，成本: 182.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:37,691 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [4, 5, 0, 3, 9, 8, 7, 6, 2, 1], 'cur_cost': 182.0, 'intermediate_solutions': [{'tour': [4, 5, 3, 1, 2, 7, 6, 8, 9, 0], 'cur_cost': 168.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 5, 0, 1, 2, 6, 7, 8, 9, 3], 'cur_cost': 157.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 5, 0, 2, 7, 6, 8, 9, 3], 'cur_cost': 201.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:37,693 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 182.00)
2025-08-05 10:28:37,693 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:37,693 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:37,695 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 3, 8, 4, 1, 2, 7, 5, 9], 'cur_cost': 183.0, 'intermediate_solutions': [{'tour': [9, 8, 3, 7, 4, 0, 6, 1, 5, 2], 'cur_cost': 167.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 8, 3, 7, 4, 1, 0, 6, 5, 2], 'cur_cost': 164.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 8, 0, 3, 7, 4, 1, 6, 5, 2], 'cur_cost': 170.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [9, 5, 8, 0, 4, 6, 7, 1, 2, 3], 'cur_cost': 145.0, 'intermediate_solutions': [{'tour': [1, 8, 0, 9, 6, 7, 4, 3, 5, 2], 'cur_cost': 141.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 0, 7, 9, 6, 4, 3, 5, 2], 'cur_cost': 138.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 8, 0, 6, 9, 7, 4, 3, 5, 2], 'cur_cost': 145.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [1, 8, 9, 6, 3, 7, 5, 2, 4, 0], 'cur_cost': 155.0, 'intermediate_solutions': [{'tour': [1, 5, 6, 9, 3, 2, 8, 7, 0, 4], 'cur_cost': 164.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 9, 6, 3, 5, 2, 8, 7, 0, 4], 'cur_cost': 152.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 1, 9, 6, 5, 3, 2, 7, 0, 4], 'cur_cost': 149.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [4, 0, 1, 9, 8, 7, 5, 6, 3, 2], 'cur_cost': 145.0, 'intermediate_solutions': [{'tour': [0, 7, 9, 8, 1, 3, 6, 2, 4, 5], 'cur_cost': 179.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 9, 7, 8, 3, 6, 2, 4, 5], 'cur_cost': 176.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 9, 1, 8, 3, 2, 6, 4, 5], 'cur_cost': 164.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [3, 7, 5, 9, 1, 8, 6, 4, 0, 2], 'cur_cost': 156.0, 'intermediate_solutions': [{'tour': [3, 6, 7, 8, 1, 5, 2, 4, 0, 9], 'cur_cost': 161.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 6, 7, 8, 1, 2, 5, 9, 0, 4], 'cur_cost': 137.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 6, 7, 8, 1, 5, 2, 9, 0, 4], 'cur_cost': 143.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 2, 8, 1, 9, 5, 7, 0, 4, 3], dtype=int64), 'cur_cost': 145.0, 'intermediate_solutions': [{'tour': array([0, 3, 1, 2, 5, 6, 7, 8, 9, 4]), 'cur_cost': 164.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 0, 3, 1, 5, 6, 7, 8, 9, 4]), 'cur_cost': 195.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 2, 0, 3, 1, 6, 7, 8, 9, 4]), 'cur_cost': 192.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 2, 0, 3, 5, 6, 7, 8, 9, 4]), 'cur_cost': 177.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 5, 2, 0, 3, 6, 7, 8, 9, 4]), 'cur_cost': 183.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [4, 8, 9, 5, 3, 2, 6, 7, 0, 1], 'cur_cost': 154.0, 'intermediate_solutions': [{'tour': [7, 1, 8, 5, 2, 0, 4, 9, 6, 3], 'cur_cost': 171.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 6, 4, 0, 2, 5, 8, 1, 7, 3], 'cur_cost': 168.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 8, 5, 1, 2, 0, 4, 6, 9, 3], 'cur_cost': 168.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [1, 3, 8, 4, 9, 5, 6, 7, 0, 2], 'cur_cost': 179.0, 'intermediate_solutions': [{'tour': [4, 9, 5, 7, 6, 8, 0, 3, 2, 1], 'cur_cost': 171.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 9, 5, 7, 6, 8, 0, 1, 3, 2], 'cur_cost': 170.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 9, 5, 7, 6, 8, 0, 1, 2, 3], 'cur_cost': 146.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 5, 8, 9, 1, 7, 2, 4, 3, 6], dtype=int64), 'cur_cost': 176.0, 'intermediate_solutions': [{'tour': array([7, 1, 2, 5, 8, 4, 6, 9, 3, 0]), 'cur_cost': 168.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 7, 1, 2, 8, 4, 6, 9, 3, 0]), 'cur_cost': 183.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 5, 7, 1, 2, 4, 6, 9, 3, 0]), 'cur_cost': 177.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 5, 7, 1, 8, 4, 6, 9, 3, 0]), 'cur_cost': 181.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 8, 5, 7, 1, 4, 6, 9, 3, 0]), 'cur_cost': 196.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [4, 5, 0, 3, 9, 8, 7, 6, 2, 1], 'cur_cost': 182.0, 'intermediate_solutions': [{'tour': [4, 5, 3, 1, 2, 7, 6, 8, 9, 0], 'cur_cost': 168.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 5, 0, 1, 2, 6, 7, 8, 9, 3], 'cur_cost': 157.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 5, 0, 2, 7, 6, 8, 9, 3], 'cur_cost': 201.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:37,695 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:37,695 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:37,697 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=145.000, 多样性=0.880
2025-08-05 10:28:37,697 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 10:28:37,697 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 10:28:37,697 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:37,698 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.011782144589906882, 'best_improvement': -0.013986013986013986}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.03414634146341461}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.009399450239160168, 'recent_improvements': [-0.021477786091970912, -0.044952875446589304, -0.040276686570291244], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 14, 'new_count': 14, 'count_change': 0, 'old_best_cost': 130.0, 'new_best_cost': 130.0, 'quality_improvement': 0.0, 'old_diversity': 0.7483516483516484, 'new_diversity': 0.7483516483516484, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启']}
2025-08-05 10:28:37,699 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 10:28:37,699 - __main__ - INFO - geometry1_10 开始进化第 3 代
2025-08-05 10:28:37,699 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 10:28:37,699 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:37,700 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=145.000, 多样性=0.880
2025-08-05 10:28:37,700 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:37,700 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.880
2025-08-05 10:28:37,701 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:37,703 - EliteExpert - INFO - 精英解分析完成: 精英解数量=14, 多样性=0.748
2025-08-05 10:28:37,704 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 10:28:37,704 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:37,704 - LandscapeExpert - INFO - 添加精英解数据: 14个精英解
2025-08-05 10:28:37,705 - LandscapeExpert - INFO - 数据提取成功: 24个路径, 24个适应度值
2025-08-05 10:28:37,727 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.583, 适应度梯度: -4.192, 聚类评分: 0.000, 覆盖率: 0.037, 收敛趋势: 0.000, 多样性: 0.349
2025-08-05 10:28:37,727 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 10:28:37,727 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:37,727 - visualization.landscape_visualizer - INFO - 设置当前实例名: geometry1_10
2025-08-05 10:28:37,733 - visualization.landscape_visualizer - INFO - 插值约束: 252 个点被约束到最小值 130.00
2025-08-05 10:28:37,734 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.5%, 梯度: 2.40 → 2.22
2025-08-05 10:28:37,862 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry1_10\landscape_geometry1_10_iter_33_20250805_102837.html
2025-08-05 10:28:37,909 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry1_10\dashboard_geometry1_10_iter_33_20250805_102837.html
2025-08-05 10:28:37,909 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 33
2025-08-05 10:28:37,909 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 10:28:37,909 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2055秒
2025-08-05 10:28:37,910 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5833333333333334, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -4.191666666666666, 'local_optima_density': 0.5833333333333334, 'gradient_variance': 299.81159722222225, 'cluster_count': 0}, 'population_state': {'diversity': 0.3488828502415459, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0371, 'fitness_entropy': 0.7621444130765097, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -4.192)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.037)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360917.7271914, 'performance_metrics': {}}}
2025-08-05 10:28:37,910 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:37,910 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:37,910 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:37,910 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:37,910 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 10:28:37,911 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 10:28:37,911 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 10:28:37,911 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:37,911 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:37,911 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 10:28:37,911 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:37,911 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:37,911 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 3} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:37,912 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-05 10:28:37,912 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:37,912 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:37,912 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 170.0
2025-08-05 10:28:37,920 - ExploitationExpert - INFO - res_population_num: 18
2025-08-05 10:28:37,920 - ExploitationExpert - INFO - res_population_costs: [130.0, 130, 130, 130, 130.0, 130, 130, 130.0, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130]
2025-08-05 10:28:37,920 - ExploitationExpert - INFO - res_populations: [array([0, 8, 1, 9, 2, 5, 6, 3, 4, 7], dtype=int64), array([0, 1, 9, 2, 5, 6, 3, 4, 7, 8], dtype=int64), array([0, 4, 3, 2, 5, 6, 7, 8, 9, 1], dtype=int64), array([0, 7, 8, 9, 1, 2, 5, 6, 3, 4], dtype=int64), array([0, 7, 6, 4, 3, 5, 2, 9, 8, 1], dtype=int64), array([0, 4, 3, 6, 7, 8, 9, 5, 2, 1], dtype=int64), array([0, 1, 9, 5, 2, 3, 4, 6, 7, 8], dtype=int64), array([0, 1, 2, 9, 5, 3, 6, 4, 7, 8], dtype=int64), array([0, 7, 8, 1, 2, 9, 5, 3, 6, 4], dtype=int64), array([0, 7, 8, 1, 9, 2, 5, 3, 6, 4], dtype=int64), array([0, 8, 1, 9, 2, 5, 3, 4, 6, 7], dtype=int64), array([0, 8, 1, 9, 5, 2, 3, 6, 4, 7], dtype=int64), array([0, 1, 9, 2, 3, 5, 6, 4, 7, 8], dtype=int64), array([0, 7, 8, 9, 1, 2, 5, 3, 6, 4], dtype=int64), array([0, 4, 7, 6, 3, 5, 2, 9, 1, 8], dtype=int64), array([0, 1, 8, 9, 2, 5, 3, 6, 7, 4], dtype=int64), array([0, 1, 8, 9, 2, 5, 3, 6, 4, 7], dtype=int64), array([0, 7, 4, 6, 3, 2, 5, 9, 8, 1], dtype=int64)]
2025-08-05 10:28:37,923 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:37,923 - ExploitationExpert - INFO - populations: [{'tour': array([8, 2, 0, 7, 6, 5, 9, 4, 3, 1], dtype=int64), 'cur_cost': 170.0}, {'tour': [9, 5, 8, 0, 4, 6, 7, 1, 2, 3], 'cur_cost': 145.0}, {'tour': [1, 8, 9, 6, 3, 7, 5, 2, 4, 0], 'cur_cost': 155.0}, {'tour': [4, 0, 1, 9, 8, 7, 5, 6, 3, 2], 'cur_cost': 145.0}, {'tour': [3, 7, 5, 9, 1, 8, 6, 4, 0, 2], 'cur_cost': 156.0}, {'tour': [6, 2, 8, 1, 9, 5, 7, 0, 4, 3], 'cur_cost': 145.0}, {'tour': [4, 8, 9, 5, 3, 2, 6, 7, 0, 1], 'cur_cost': 154.0}, {'tour': [1, 3, 8, 4, 9, 5, 6, 7, 0, 2], 'cur_cost': 179.0}, {'tour': [0, 5, 8, 9, 1, 7, 2, 4, 3, 6], 'cur_cost': 176.0}, {'tour': [4, 5, 0, 3, 9, 8, 7, 6, 2, 1], 'cur_cost': 182.0}]
2025-08-05 10:28:37,924 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:37,924 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 83, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 83, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:37,924 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([8, 2, 0, 7, 6, 5, 9, 4, 3, 1], dtype=int64), 'cur_cost': 170.0, 'intermediate_solutions': [{'tour': array([3, 6, 0, 8, 4, 1, 2, 7, 5, 9]), 'cur_cost': 173.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 3, 6, 0, 4, 1, 2, 7, 5, 9]), 'cur_cost': 170.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 8, 3, 6, 0, 1, 2, 7, 5, 9]), 'cur_cost': 173.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 8, 3, 6, 4, 1, 2, 7, 5, 9]), 'cur_cost': 170.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 4, 8, 3, 6, 1, 2, 7, 5, 9]), 'cur_cost': 173.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:37,925 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 170.00)
2025-08-05 10:28:37,925 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:37,925 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:37,925 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:37,925 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:37,925 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,925 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,926 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,926 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,926 - ExplorationExpert - INFO - 探索路径生成完成，成本: 143.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:37,926 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [8, 6, 5, 7, 0, 1, 9, 2, 3, 4], 'cur_cost': 143.0, 'intermediate_solutions': [{'tour': [9, 5, 8, 0, 3, 6, 7, 1, 2, 4], 'cur_cost': 173.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 9, 8, 0, 4, 6, 7, 1, 2, 3], 'cur_cost': 136.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 5, 8, 0, 4, 6, 1, 2, 3, 7], 'cur_cost': 152.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:37,926 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 143.00)
2025-08-05 10:28:37,926 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:37,926 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:37,926 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:37,927 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:37,927 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,927 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,927 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,928 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,928 - ExplorationExpert - INFO - 探索路径生成完成，成本: 142.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:37,928 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [4, 6, 1, 0, 8, 7, 5, 9, 2, 3], 'cur_cost': 142.0, 'intermediate_solutions': [{'tour': [1, 8, 9, 6, 3, 0, 5, 2, 4, 7], 'cur_cost': 174.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 6, 9, 3, 7, 5, 2, 4, 0], 'cur_cost': 165.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 1, 8, 9, 6, 7, 5, 2, 4, 0], 'cur_cost': 174.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:37,928 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 142.00)
2025-08-05 10:28:37,928 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:37,928 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:37,928 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:37,929 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:37,929 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,929 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,929 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,929 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,929 - ExplorationExpert - INFO - 探索路径生成完成，成本: 158.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:37,929 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 6, 3, 9, 5, 7, 8, 1, 2, 4], 'cur_cost': 158.0, 'intermediate_solutions': [{'tour': [4, 0, 1, 9, 5, 7, 8, 6, 3, 2], 'cur_cost': 149.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 0, 1, 9, 8, 7, 2, 3, 6, 5], 'cur_cost': 145.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 1, 8, 7, 5, 6, 9, 3, 2], 'cur_cost': 155.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:37,929 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 158.00)
2025-08-05 10:28:37,929 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:37,930 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:37,930 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:37,930 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:37,930 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,930 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,930 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,930 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,931 - ExplorationExpert - INFO - 探索路径生成完成，成本: 148.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:37,931 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [5, 6, 7, 0, 8, 9, 2, 3, 4, 1], 'cur_cost': 148.0, 'intermediate_solutions': [{'tour': [3, 2, 5, 9, 1, 8, 6, 4, 0, 7], 'cur_cost': 140.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 7, 5, 9, 1, 8, 6, 4, 2, 0], 'cur_cost': 181.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 7, 5, 9, 2, 1, 8, 6, 4, 0], 'cur_cost': 156.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:37,931 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 148.00)
2025-08-05 10:28:37,931 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:37,931 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:37,931 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:37,931 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:37,931 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,932 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,932 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,932 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,932 - ExplorationExpert - INFO - 探索路径生成完成，成本: 175.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:37,932 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 3, 6, 8, 9, 5, 7, 4, 1, 2], 'cur_cost': 175.0, 'intermediate_solutions': [{'tour': [6, 2, 8, 1, 9, 5, 3, 0, 4, 7], 'cur_cost': 155.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 2, 8, 1, 7, 5, 9, 0, 4, 3], 'cur_cost': 158.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 2, 8, 1, 5, 9, 7, 0, 4, 3], 'cur_cost': 152.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:37,932 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 175.00)
2025-08-05 10:28:37,932 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:37,932 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:37,932 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:37,933 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:37,933 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,933 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,933 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,933 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,933 - ExplorationExpert - INFO - 探索路径生成完成，成本: 149.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:37,933 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [3, 7, 4, 9, 5, 6, 8, 0, 1, 2], 'cur_cost': 149.0, 'intermediate_solutions': [{'tour': [4, 8, 9, 5, 3, 2, 0, 7, 6, 1], 'cur_cost': 170.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 9, 5, 3, 1, 0, 7, 6, 2], 'cur_cost': 166.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 8, 9, 5, 3, 2, 6, 7, 0, 1], 'cur_cost': 154.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:37,934 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 149.00)
2025-08-05 10:28:37,934 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:28:37,934 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:37,934 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:37,934 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 170.0
2025-08-05 10:28:37,941 - ExploitationExpert - INFO - res_population_num: 21
2025-08-05 10:28:37,941 - ExploitationExpert - INFO - res_population_costs: [130.0, 130, 130, 130, 130.0, 130, 130, 130.0, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130.0, 130, 130]
2025-08-05 10:28:37,941 - ExploitationExpert - INFO - res_populations: [array([0, 8, 1, 9, 2, 5, 6, 3, 4, 7], dtype=int64), array([0, 1, 9, 2, 5, 6, 3, 4, 7, 8], dtype=int64), array([0, 4, 3, 2, 5, 6, 7, 8, 9, 1], dtype=int64), array([0, 7, 8, 9, 1, 2, 5, 6, 3, 4], dtype=int64), array([0, 7, 6, 4, 3, 5, 2, 9, 8, 1], dtype=int64), array([0, 4, 3, 6, 7, 8, 9, 5, 2, 1], dtype=int64), array([0, 1, 9, 5, 2, 3, 4, 6, 7, 8], dtype=int64), array([0, 1, 2, 9, 5, 3, 6, 4, 7, 8], dtype=int64), array([0, 7, 8, 1, 2, 9, 5, 3, 6, 4], dtype=int64), array([0, 7, 8, 1, 9, 2, 5, 3, 6, 4], dtype=int64), array([0, 8, 1, 9, 2, 5, 3, 4, 6, 7], dtype=int64), array([0, 8, 1, 9, 5, 2, 3, 6, 4, 7], dtype=int64), array([0, 1, 9, 2, 3, 5, 6, 4, 7, 8], dtype=int64), array([0, 7, 8, 9, 1, 2, 5, 3, 6, 4], dtype=int64), array([0, 4, 7, 6, 3, 5, 2, 9, 1, 8], dtype=int64), array([0, 1, 8, 9, 2, 5, 3, 6, 7, 4], dtype=int64), array([0, 1, 8, 9, 2, 5, 3, 6, 4, 7], dtype=int64), array([0, 7, 4, 6, 3, 2, 5, 9, 8, 1], dtype=int64), array([0, 8, 9, 5, 6, 7, 4, 3, 2, 1], dtype=int64), array([0, 4, 7, 6, 5, 3, 2, 9, 1, 8], dtype=int64), array([0, 4, 6, 5, 3, 2, 9, 1, 8, 7], dtype=int64)]
2025-08-05 10:28:37,945 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:37,945 - ExploitationExpert - INFO - populations: [{'tour': array([8, 2, 0, 7, 6, 5, 9, 4, 3, 1], dtype=int64), 'cur_cost': 170.0}, {'tour': [8, 6, 5, 7, 0, 1, 9, 2, 3, 4], 'cur_cost': 143.0}, {'tour': [4, 6, 1, 0, 8, 7, 5, 9, 2, 3], 'cur_cost': 142.0}, {'tour': [0, 6, 3, 9, 5, 7, 8, 1, 2, 4], 'cur_cost': 158.0}, {'tour': [5, 6, 7, 0, 8, 9, 2, 3, 4, 1], 'cur_cost': 148.0}, {'tour': [0, 3, 6, 8, 9, 5, 7, 4, 1, 2], 'cur_cost': 175.0}, {'tour': [3, 7, 4, 9, 5, 6, 8, 0, 1, 2], 'cur_cost': 149.0}, {'tour': array([4, 2, 7, 8, 9, 5, 1, 0, 3, 6], dtype=int64), 'cur_cost': 170.0}, {'tour': [0, 5, 8, 9, 1, 7, 2, 4, 3, 6], 'cur_cost': 176.0}, {'tour': [4, 5, 0, 3, 9, 8, 7, 6, 2, 1], 'cur_cost': 182.0}]
2025-08-05 10:28:37,946 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:37,946 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 84, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 84, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:37,946 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([4, 2, 7, 8, 9, 5, 1, 0, 3, 6], dtype=int64), 'cur_cost': 170.0, 'intermediate_solutions': [{'tour': array([8, 3, 1, 4, 9, 5, 6, 7, 0, 2]), 'cur_cost': 191.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 8, 3, 1, 9, 5, 6, 7, 0, 2]), 'cur_cost': 182.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 4, 8, 3, 1, 5, 6, 7, 0, 2]), 'cur_cost': 185.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 4, 8, 3, 9, 5, 6, 7, 0, 2]), 'cur_cost': 176.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 9, 4, 8, 3, 5, 6, 7, 0, 2]), 'cur_cost': 167.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:37,946 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 170.00)
2025-08-05 10:28:37,947 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:37,947 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:37,947 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:37,947 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:37,947 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,948 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,948 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,948 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,948 - ExplorationExpert - INFO - 探索路径生成完成，成本: 155.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:37,948 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [1, 7, 6, 9, 2, 5, 8, 0, 4, 3], 'cur_cost': 155.0, 'intermediate_solutions': [{'tour': [0, 5, 8, 9, 1, 7, 2, 3, 4, 6], 'cur_cost': 164.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 8, 9, 1, 7, 2, 6, 3, 4], 'cur_cost': 163.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 8, 9, 1, 6, 7, 2, 4, 3], 'cur_cost': 185.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:37,948 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 155.00)
2025-08-05 10:28:37,948 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:28:37,948 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:37,948 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:37,949 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 169.0
2025-08-05 10:28:37,963 - ExploitationExpert - INFO - res_population_num: 24
2025-08-05 10:28:37,963 - ExploitationExpert - INFO - res_population_costs: [130.0, 130, 130, 130, 130.0, 130, 130, 130.0, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130]
2025-08-05 10:28:37,963 - ExploitationExpert - INFO - res_populations: [array([0, 8, 1, 9, 2, 5, 6, 3, 4, 7], dtype=int64), array([0, 1, 9, 2, 5, 6, 3, 4, 7, 8], dtype=int64), array([0, 4, 3, 2, 5, 6, 7, 8, 9, 1], dtype=int64), array([0, 7, 8, 9, 1, 2, 5, 6, 3, 4], dtype=int64), array([0, 7, 6, 4, 3, 5, 2, 9, 8, 1], dtype=int64), array([0, 4, 3, 6, 7, 8, 9, 5, 2, 1], dtype=int64), array([0, 1, 9, 5, 2, 3, 4, 6, 7, 8], dtype=int64), array([0, 1, 2, 9, 5, 3, 6, 4, 7, 8], dtype=int64), array([0, 7, 8, 1, 2, 9, 5, 3, 6, 4], dtype=int64), array([0, 7, 8, 1, 9, 2, 5, 3, 6, 4], dtype=int64), array([0, 8, 1, 9, 2, 5, 3, 4, 6, 7], dtype=int64), array([0, 8, 1, 9, 5, 2, 3, 6, 4, 7], dtype=int64), array([0, 1, 9, 2, 3, 5, 6, 4, 7, 8], dtype=int64), array([0, 7, 8, 9, 1, 2, 5, 3, 6, 4], dtype=int64), array([0, 4, 7, 6, 3, 5, 2, 9, 1, 8], dtype=int64), array([0, 1, 8, 9, 2, 5, 3, 6, 7, 4], dtype=int64), array([0, 1, 8, 9, 2, 5, 3, 6, 4, 7], dtype=int64), array([0, 7, 4, 6, 3, 2, 5, 9, 8, 1], dtype=int64), array([0, 8, 9, 5, 6, 7, 4, 3, 2, 1], dtype=int64), array([0, 4, 7, 6, 5, 3, 2, 9, 1, 8], dtype=int64), array([0, 4, 6, 5, 3, 2, 9, 1, 8, 7], dtype=int64), array([0, 7, 4, 6, 3, 5, 9, 2, 1, 8], dtype=int64), array([0, 8, 9, 1, 2, 3, 5, 6, 4, 7], dtype=int64), array([0, 4, 3, 6, 5, 2, 9, 1, 8, 7], dtype=int64)]
2025-08-05 10:28:37,967 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:37,968 - ExploitationExpert - INFO - populations: [{'tour': array([8, 2, 0, 7, 6, 5, 9, 4, 3, 1], dtype=int64), 'cur_cost': 170.0}, {'tour': [8, 6, 5, 7, 0, 1, 9, 2, 3, 4], 'cur_cost': 143.0}, {'tour': [4, 6, 1, 0, 8, 7, 5, 9, 2, 3], 'cur_cost': 142.0}, {'tour': [0, 6, 3, 9, 5, 7, 8, 1, 2, 4], 'cur_cost': 158.0}, {'tour': [5, 6, 7, 0, 8, 9, 2, 3, 4, 1], 'cur_cost': 148.0}, {'tour': [0, 3, 6, 8, 9, 5, 7, 4, 1, 2], 'cur_cost': 175.0}, {'tour': [3, 7, 4, 9, 5, 6, 8, 0, 1, 2], 'cur_cost': 149.0}, {'tour': array([4, 2, 7, 8, 9, 5, 1, 0, 3, 6], dtype=int64), 'cur_cost': 170.0}, {'tour': [1, 7, 6, 9, 2, 5, 8, 0, 4, 3], 'cur_cost': 155.0}, {'tour': array([8, 9, 2, 6, 1, 5, 0, 7, 4, 3], dtype=int64), 'cur_cost': 169.0}]
2025-08-05 10:28:37,968 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:37,969 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 85, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 85, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:37,969 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([8, 9, 2, 6, 1, 5, 0, 7, 4, 3], dtype=int64), 'cur_cost': 169.0, 'intermediate_solutions': [{'tour': array([0, 5, 4, 3, 9, 8, 7, 6, 2, 1]), 'cur_cost': 157.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 0, 5, 4, 9, 8, 7, 6, 2, 1]), 'cur_cost': 185.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 3, 0, 5, 4, 8, 7, 6, 2, 1]), 'cur_cost': 176.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 3, 0, 5, 9, 8, 7, 6, 2, 1]), 'cur_cost': 170.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 9, 3, 0, 5, 8, 7, 6, 2, 1]), 'cur_cost': 188.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:37,969 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 169.00)
2025-08-05 10:28:37,970 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:37,970 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:37,971 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 2, 0, 7, 6, 5, 9, 4, 3, 1], dtype=int64), 'cur_cost': 170.0, 'intermediate_solutions': [{'tour': array([3, 6, 0, 8, 4, 1, 2, 7, 5, 9]), 'cur_cost': 173.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 3, 6, 0, 4, 1, 2, 7, 5, 9]), 'cur_cost': 170.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 8, 3, 6, 0, 1, 2, 7, 5, 9]), 'cur_cost': 173.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 8, 3, 6, 4, 1, 2, 7, 5, 9]), 'cur_cost': 170.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 4, 8, 3, 6, 1, 2, 7, 5, 9]), 'cur_cost': 173.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [8, 6, 5, 7, 0, 1, 9, 2, 3, 4], 'cur_cost': 143.0, 'intermediate_solutions': [{'tour': [9, 5, 8, 0, 3, 6, 7, 1, 2, 4], 'cur_cost': 173.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 9, 8, 0, 4, 6, 7, 1, 2, 3], 'cur_cost': 136.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 5, 8, 0, 4, 6, 1, 2, 3, 7], 'cur_cost': 152.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [4, 6, 1, 0, 8, 7, 5, 9, 2, 3], 'cur_cost': 142.0, 'intermediate_solutions': [{'tour': [1, 8, 9, 6, 3, 0, 5, 2, 4, 7], 'cur_cost': 174.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 6, 9, 3, 7, 5, 2, 4, 0], 'cur_cost': 165.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 1, 8, 9, 6, 7, 5, 2, 4, 0], 'cur_cost': 174.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 3, 9, 5, 7, 8, 1, 2, 4], 'cur_cost': 158.0, 'intermediate_solutions': [{'tour': [4, 0, 1, 9, 5, 7, 8, 6, 3, 2], 'cur_cost': 149.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 0, 1, 9, 8, 7, 2, 3, 6, 5], 'cur_cost': 145.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 1, 8, 7, 5, 6, 9, 3, 2], 'cur_cost': 155.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [5, 6, 7, 0, 8, 9, 2, 3, 4, 1], 'cur_cost': 148.0, 'intermediate_solutions': [{'tour': [3, 2, 5, 9, 1, 8, 6, 4, 0, 7], 'cur_cost': 140.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 7, 5, 9, 1, 8, 6, 4, 2, 0], 'cur_cost': 181.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 7, 5, 9, 2, 1, 8, 6, 4, 0], 'cur_cost': 156.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 6, 8, 9, 5, 7, 4, 1, 2], 'cur_cost': 175.0, 'intermediate_solutions': [{'tour': [6, 2, 8, 1, 9, 5, 3, 0, 4, 7], 'cur_cost': 155.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 2, 8, 1, 7, 5, 9, 0, 4, 3], 'cur_cost': 158.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 2, 8, 1, 5, 9, 7, 0, 4, 3], 'cur_cost': 152.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [3, 7, 4, 9, 5, 6, 8, 0, 1, 2], 'cur_cost': 149.0, 'intermediate_solutions': [{'tour': [4, 8, 9, 5, 3, 2, 0, 7, 6, 1], 'cur_cost': 170.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 9, 5, 3, 1, 0, 7, 6, 2], 'cur_cost': 166.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 8, 9, 5, 3, 2, 6, 7, 0, 1], 'cur_cost': 154.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 2, 7, 8, 9, 5, 1, 0, 3, 6], dtype=int64), 'cur_cost': 170.0, 'intermediate_solutions': [{'tour': array([8, 3, 1, 4, 9, 5, 6, 7, 0, 2]), 'cur_cost': 191.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 8, 3, 1, 9, 5, 6, 7, 0, 2]), 'cur_cost': 182.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 4, 8, 3, 1, 5, 6, 7, 0, 2]), 'cur_cost': 185.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 4, 8, 3, 9, 5, 6, 7, 0, 2]), 'cur_cost': 176.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 9, 4, 8, 3, 5, 6, 7, 0, 2]), 'cur_cost': 167.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 6, 9, 2, 5, 8, 0, 4, 3], 'cur_cost': 155.0, 'intermediate_solutions': [{'tour': [0, 5, 8, 9, 1, 7, 2, 3, 4, 6], 'cur_cost': 164.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 8, 9, 1, 7, 2, 6, 3, 4], 'cur_cost': 163.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 8, 9, 1, 6, 7, 2, 4, 3], 'cur_cost': 185.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 9, 2, 6, 1, 5, 0, 7, 4, 3], dtype=int64), 'cur_cost': 169.0, 'intermediate_solutions': [{'tour': array([0, 5, 4, 3, 9, 8, 7, 6, 2, 1]), 'cur_cost': 157.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 0, 5, 4, 9, 8, 7, 6, 2, 1]), 'cur_cost': 185.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 3, 0, 5, 4, 8, 7, 6, 2, 1]), 'cur_cost': 176.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 3, 0, 5, 9, 8, 7, 6, 2, 1]), 'cur_cost': 170.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 9, 3, 0, 5, 8, 7, 6, 2, 1]), 'cur_cost': 188.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:28:37,971 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:37,972 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:37,973 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=142.000, 多样性=0.873
2025-08-05 10:28:37,973 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 10:28:37,973 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 10:28:37,973 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:37,975 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.029904714714085973, 'best_improvement': 0.020689655172413793}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.007575757575757499}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.028367510018248093, 'recent_improvements': [-0.044952875446589304, -0.040276686570291244, 0.011782144589906882], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 24, 'new_count': 24, 'count_change': 0, 'old_best_cost': 130.0, 'new_best_cost': 130.0, 'quality_improvement': 0.0, 'old_diversity': 0.7460144927536232, 'new_diversity': 0.7460144927536232, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:37,979 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 10:28:37,979 - __main__ - INFO - geometry1_10 开始进化第 4 代
2025-08-05 10:28:37,979 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 10:28:37,979 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:37,981 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=142.000, 多样性=0.873
2025-08-05 10:28:37,981 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:37,982 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.873
2025-08-05 10:28:37,982 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:37,987 - EliteExpert - INFO - 精英解分析完成: 精英解数量=24, 多样性=0.746
2025-08-05 10:28:37,989 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 10:28:37,990 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:37,990 - LandscapeExpert - INFO - 添加精英解数据: 24个精英解
2025-08-05 10:28:37,990 - LandscapeExpert - INFO - 数据提取成功: 34个路径, 34个适应度值
2025-08-05 10:28:38,050 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.706, 适应度梯度: -4.676, 聚类评分: 0.000, 覆盖率: 0.039, 收敛趋势: 0.000, 多样性: 0.241
2025-08-05 10:28:38,050 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 10:28:38,050 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:38,050 - visualization.landscape_visualizer - INFO - 设置当前实例名: geometry1_10
2025-08-05 10:28:38,058 - visualization.landscape_visualizer - INFO - 插值约束: 399 个点被约束到最小值 130.00
2025-08-05 10:28:38,060 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.3%, 梯度: 2.32 → 2.16
2025-08-05 10:28:38,177 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry1_10\landscape_geometry1_10_iter_34_20250805_102838.html
2025-08-05 10:28:38,245 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry1_10\dashboard_geometry1_10_iter_34_20250805_102838.html
2025-08-05 10:28:38,245 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 34
2025-08-05 10:28:38,245 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 10:28:38,245 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2562秒
2025-08-05 10:28:38,246 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.7058823529411765, 'modality': 'multi-modal', 'deceptiveness': 'high', 'gradient_strength': -4.676470588235294, 'local_optima_density': 0.7058823529411765, 'gradient_variance': 123.21885813148786, 'cluster_count': 0}, 'population_state': {'diversity': 0.24069413861801406, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.039, 'fitness_entropy': 0.5676240005614187, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'high_local_optima', 'severity': 'high', 'description': '高局部最优密度区域 (密度: 0.706)', 'recommendation': '增加扰动强度'}, {'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -4.676)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.039)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing', 'diversification'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100, 'diversification_strength': 0.6}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360918.0505157, 'performance_metrics': {}}}
2025-08-05 10:28:38,246 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:38,246 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:38,246 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:38,246 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:38,246 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:38,246 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 10:28:38,247 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:38,247 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:38,247 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:38,247 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:38,247 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:38,247 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:38,247 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:38,247 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-05 10:28:38,247 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:38,248 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:38,248 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 173.0
2025-08-05 10:28:38,256 - ExploitationExpert - INFO - res_population_num: 25
2025-08-05 10:28:38,256 - ExploitationExpert - INFO - res_population_costs: [130.0, 130, 130, 130, 130.0, 130, 130, 130.0, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130]
2025-08-05 10:28:38,256 - ExploitationExpert - INFO - res_populations: [array([0, 8, 1, 9, 2, 5, 6, 3, 4, 7], dtype=int64), array([0, 1, 9, 2, 5, 6, 3, 4, 7, 8], dtype=int64), array([0, 4, 3, 2, 5, 6, 7, 8, 9, 1], dtype=int64), array([0, 7, 8, 9, 1, 2, 5, 6, 3, 4], dtype=int64), array([0, 7, 6, 4, 3, 5, 2, 9, 8, 1], dtype=int64), array([0, 4, 3, 6, 7, 8, 9, 5, 2, 1], dtype=int64), array([0, 1, 9, 5, 2, 3, 4, 6, 7, 8], dtype=int64), array([0, 1, 2, 9, 5, 3, 6, 4, 7, 8], dtype=int64), array([0, 7, 8, 1, 2, 9, 5, 3, 6, 4], dtype=int64), array([0, 7, 8, 1, 9, 2, 5, 3, 6, 4], dtype=int64), array([0, 8, 1, 9, 2, 5, 3, 4, 6, 7], dtype=int64), array([0, 8, 1, 9, 5, 2, 3, 6, 4, 7], dtype=int64), array([0, 1, 9, 2, 3, 5, 6, 4, 7, 8], dtype=int64), array([0, 7, 8, 9, 1, 2, 5, 3, 6, 4], dtype=int64), array([0, 4, 7, 6, 3, 5, 2, 9, 1, 8], dtype=int64), array([0, 1, 8, 9, 2, 5, 3, 6, 7, 4], dtype=int64), array([0, 1, 8, 9, 2, 5, 3, 6, 4, 7], dtype=int64), array([0, 7, 4, 6, 3, 2, 5, 9, 8, 1], dtype=int64), array([0, 8, 9, 5, 6, 7, 4, 3, 2, 1], dtype=int64), array([0, 4, 7, 6, 5, 3, 2, 9, 1, 8], dtype=int64), array([0, 4, 6, 5, 3, 2, 9, 1, 8, 7], dtype=int64), array([0, 7, 4, 6, 3, 5, 9, 2, 1, 8], dtype=int64), array([0, 8, 9, 1, 2, 3, 5, 6, 4, 7], dtype=int64), array([0, 4, 3, 6, 5, 2, 9, 1, 8, 7], dtype=int64), array([0, 8, 9, 1, 2, 3, 5, 6, 7, 4], dtype=int64)]
2025-08-05 10:28:38,261 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:38,261 - ExploitationExpert - INFO - populations: [{'tour': array([3, 5, 2, 6, 9, 4, 8, 0, 7, 1], dtype=int64), 'cur_cost': 173.0}, {'tour': [8, 6, 5, 7, 0, 1, 9, 2, 3, 4], 'cur_cost': 143.0}, {'tour': [4, 6, 1, 0, 8, 7, 5, 9, 2, 3], 'cur_cost': 142.0}, {'tour': [0, 6, 3, 9, 5, 7, 8, 1, 2, 4], 'cur_cost': 158.0}, {'tour': [5, 6, 7, 0, 8, 9, 2, 3, 4, 1], 'cur_cost': 148.0}, {'tour': [0, 3, 6, 8, 9, 5, 7, 4, 1, 2], 'cur_cost': 175.0}, {'tour': [3, 7, 4, 9, 5, 6, 8, 0, 1, 2], 'cur_cost': 149.0}, {'tour': [4, 2, 7, 8, 9, 5, 1, 0, 3, 6], 'cur_cost': 170.0}, {'tour': [1, 7, 6, 9, 2, 5, 8, 0, 4, 3], 'cur_cost': 155.0}, {'tour': [8, 9, 2, 6, 1, 5, 0, 7, 4, 3], 'cur_cost': 169.0}]
2025-08-05 10:28:38,261 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:38,262 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 86, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 86, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:38,262 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([3, 5, 2, 6, 9, 4, 8, 0, 7, 1], dtype=int64), 'cur_cost': 173.0, 'intermediate_solutions': [{'tour': array([0, 2, 8, 7, 6, 5, 9, 4, 3, 1]), 'cur_cost': 170.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 0, 2, 8, 6, 5, 9, 4, 3, 1]), 'cur_cost': 180.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 7, 0, 2, 8, 5, 9, 4, 3, 1]), 'cur_cost': 182.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 7, 0, 2, 6, 5, 9, 4, 3, 1]), 'cur_cost': 170.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 6, 7, 0, 2, 5, 9, 4, 3, 1]), 'cur_cost': 168.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:38,262 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 173.00)
2025-08-05 10:28:38,262 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:38,262 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:38,262 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:38,263 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:38,263 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,263 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,263 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,263 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,263 - ExplorationExpert - INFO - 探索路径生成完成，成本: 195.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:38,263 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [2, 7, 3, 9, 0, 5, 6, 8, 1, 4], 'cur_cost': 195.0, 'intermediate_solutions': [{'tour': [4, 6, 5, 7, 0, 1, 9, 2, 3, 8], 'cur_cost': 148.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 6, 5, 1, 0, 7, 9, 2, 3, 4], 'cur_cost': 150.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 6, 5, 0, 1, 9, 2, 3, 4, 7], 'cur_cost': 143.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:38,263 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 195.00)
2025-08-05 10:28:38,264 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:38,264 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:38,264 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:38,264 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:38,264 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,264 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,264 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,265 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,265 - ExplorationExpert - INFO - 探索路径生成完成，成本: 143.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:38,265 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [9, 8, 5, 7, 6, 4, 3, 2, 1, 0], 'cur_cost': 143.0, 'intermediate_solutions': [{'tour': [4, 6, 8, 0, 1, 7, 5, 9, 2, 3], 'cur_cost': 143.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 6, 1, 0, 8, 7, 5, 2, 9, 3], 'cur_cost': 148.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 6, 1, 0, 8, 7, 5, 9, 2, 3], 'cur_cost': 142.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:38,265 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 143.00)
2025-08-05 10:28:38,265 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:38,265 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:38,265 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:38,266 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:38,266 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,266 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,266 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,266 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,266 - ExplorationExpert - INFO - 探索路径生成完成，成本: 152.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:38,266 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [3, 4, 6, 5, 9, 7, 0, 8, 2, 1], 'cur_cost': 152.0, 'intermediate_solutions': [{'tour': [4, 6, 3, 9, 5, 7, 8, 1, 2, 0], 'cur_cost': 152.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 5, 9, 3, 7, 8, 1, 2, 4], 'cur_cost': 161.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 9, 5, 7, 8, 1, 2, 4, 3], 'cur_cost': 169.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:38,266 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 152.00)
2025-08-05 10:28:38,266 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:38,267 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:38,267 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:38,267 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:38,267 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,267 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,267 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,267 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,268 - ExplorationExpert - INFO - 探索路径生成完成，成本: 162.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:38,268 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [2, 3, 7, 0, 9, 6, 4, 1, 8, 5], 'cur_cost': 162.0, 'intermediate_solutions': [{'tour': [5, 6, 7, 0, 1, 9, 2, 3, 4, 8], 'cur_cost': 139.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 6, 7, 0, 8, 9, 4, 3, 2, 1], 'cur_cost': 145.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 0, 8, 9, 2, 6, 3, 4, 1], 'cur_cost': 157.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:38,268 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 162.00)
2025-08-05 10:28:38,268 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:28:38,268 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:38,268 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:38,268 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 192.0
2025-08-05 10:28:38,277 - ExploitationExpert - INFO - res_population_num: 26
2025-08-05 10:28:38,277 - ExploitationExpert - INFO - res_population_costs: [130.0, 130, 130, 130, 130.0, 130, 130, 130.0, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130, 130]
2025-08-05 10:28:38,277 - ExploitationExpert - INFO - res_populations: [array([0, 8, 1, 9, 2, 5, 6, 3, 4, 7], dtype=int64), array([0, 1, 9, 2, 5, 6, 3, 4, 7, 8], dtype=int64), array([0, 4, 3, 2, 5, 6, 7, 8, 9, 1], dtype=int64), array([0, 7, 8, 9, 1, 2, 5, 6, 3, 4], dtype=int64), array([0, 7, 6, 4, 3, 5, 2, 9, 8, 1], dtype=int64), array([0, 4, 3, 6, 7, 8, 9, 5, 2, 1], dtype=int64), array([0, 1, 9, 5, 2, 3, 4, 6, 7, 8], dtype=int64), array([0, 1, 2, 9, 5, 3, 6, 4, 7, 8], dtype=int64), array([0, 7, 8, 1, 2, 9, 5, 3, 6, 4], dtype=int64), array([0, 7, 8, 1, 9, 2, 5, 3, 6, 4], dtype=int64), array([0, 8, 1, 9, 2, 5, 3, 4, 6, 7], dtype=int64), array([0, 8, 1, 9, 5, 2, 3, 6, 4, 7], dtype=int64), array([0, 1, 9, 2, 3, 5, 6, 4, 7, 8], dtype=int64), array([0, 7, 8, 9, 1, 2, 5, 3, 6, 4], dtype=int64), array([0, 4, 7, 6, 3, 5, 2, 9, 1, 8], dtype=int64), array([0, 1, 8, 9, 2, 5, 3, 6, 7, 4], dtype=int64), array([0, 1, 8, 9, 2, 5, 3, 6, 4, 7], dtype=int64), array([0, 7, 4, 6, 3, 2, 5, 9, 8, 1], dtype=int64), array([0, 8, 9, 5, 6, 7, 4, 3, 2, 1], dtype=int64), array([0, 4, 7, 6, 5, 3, 2, 9, 1, 8], dtype=int64), array([0, 4, 6, 5, 3, 2, 9, 1, 8, 7], dtype=int64), array([0, 7, 4, 6, 3, 5, 9, 2, 1, 8], dtype=int64), array([0, 8, 9, 1, 2, 3, 5, 6, 4, 7], dtype=int64), array([0, 4, 3, 6, 5, 2, 9, 1, 8, 7], dtype=int64), array([0, 8, 9, 1, 2, 3, 5, 6, 7, 4], dtype=int64), array([0, 7, 8, 9, 1, 2, 3, 5, 6, 4], dtype=int64)]
2025-08-05 10:28:38,281 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:38,281 - ExploitationExpert - INFO - populations: [{'tour': array([3, 5, 2, 6, 9, 4, 8, 0, 7, 1], dtype=int64), 'cur_cost': 173.0}, {'tour': [2, 7, 3, 9, 0, 5, 6, 8, 1, 4], 'cur_cost': 195.0}, {'tour': [9, 8, 5, 7, 6, 4, 3, 2, 1, 0], 'cur_cost': 143.0}, {'tour': [3, 4, 6, 5, 9, 7, 0, 8, 2, 1], 'cur_cost': 152.0}, {'tour': [2, 3, 7, 0, 9, 6, 4, 1, 8, 5], 'cur_cost': 162.0}, {'tour': array([2, 5, 0, 6, 8, 3, 7, 1, 9, 4], dtype=int64), 'cur_cost': 192.0}, {'tour': [3, 7, 4, 9, 5, 6, 8, 0, 1, 2], 'cur_cost': 149.0}, {'tour': [4, 2, 7, 8, 9, 5, 1, 0, 3, 6], 'cur_cost': 170.0}, {'tour': [1, 7, 6, 9, 2, 5, 8, 0, 4, 3], 'cur_cost': 155.0}, {'tour': [8, 9, 2, 6, 1, 5, 0, 7, 4, 3], 'cur_cost': 169.0}]
2025-08-05 10:28:38,282 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:38,282 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 87, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 87, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:38,282 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([2, 5, 0, 6, 8, 3, 7, 1, 9, 4], dtype=int64), 'cur_cost': 192.0, 'intermediate_solutions': [{'tour': array([6, 3, 0, 8, 9, 5, 7, 4, 1, 2]), 'cur_cost': 164.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 6, 3, 0, 9, 5, 7, 4, 1, 2]), 'cur_cost': 175.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 8, 6, 3, 0, 5, 7, 4, 1, 2]), 'cur_cost': 171.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 8, 6, 3, 9, 5, 7, 4, 1, 2]), 'cur_cost': 168.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 9, 8, 6, 3, 5, 7, 4, 1, 2]), 'cur_cost': 169.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:38,283 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 192.00)
2025-08-05 10:28:38,283 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:38,283 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:38,283 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:38,283 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:38,283 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,283 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,284 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,284 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,284 - ExplorationExpert - INFO - 探索路径生成完成，成本: 150.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:38,284 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [1, 9, 7, 4, 5, 8, 0, 6, 3, 2], 'cur_cost': 150.0, 'intermediate_solutions': [{'tour': [6, 7, 4, 9, 5, 3, 8, 0, 1, 2], 'cur_cost': 154.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 7, 4, 9, 5, 6, 1, 0, 8, 2], 'cur_cost': 160.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 7, 8, 4, 9, 5, 6, 0, 1, 2], 'cur_cost': 158.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:38,284 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 150.00)
2025-08-05 10:28:38,284 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:28:38,284 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:38,284 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:38,285 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 146.0
2025-08-05 10:28:38,295 - ExploitationExpert - INFO - res_population_num: 26
2025-08-05 10:28:38,296 - ExploitationExpert - INFO - res_population_costs: [130.0, 130, 130, 130, 130.0, 130, 130, 130.0, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130, 130]
2025-08-05 10:28:38,296 - ExploitationExpert - INFO - res_populations: [array([0, 8, 1, 9, 2, 5, 6, 3, 4, 7], dtype=int64), array([0, 1, 9, 2, 5, 6, 3, 4, 7, 8], dtype=int64), array([0, 4, 3, 2, 5, 6, 7, 8, 9, 1], dtype=int64), array([0, 7, 8, 9, 1, 2, 5, 6, 3, 4], dtype=int64), array([0, 7, 6, 4, 3, 5, 2, 9, 8, 1], dtype=int64), array([0, 4, 3, 6, 7, 8, 9, 5, 2, 1], dtype=int64), array([0, 1, 9, 5, 2, 3, 4, 6, 7, 8], dtype=int64), array([0, 1, 2, 9, 5, 3, 6, 4, 7, 8], dtype=int64), array([0, 7, 8, 1, 2, 9, 5, 3, 6, 4], dtype=int64), array([0, 7, 8, 1, 9, 2, 5, 3, 6, 4], dtype=int64), array([0, 8, 1, 9, 2, 5, 3, 4, 6, 7], dtype=int64), array([0, 8, 1, 9, 5, 2, 3, 6, 4, 7], dtype=int64), array([0, 1, 9, 2, 3, 5, 6, 4, 7, 8], dtype=int64), array([0, 7, 8, 9, 1, 2, 5, 3, 6, 4], dtype=int64), array([0, 4, 7, 6, 3, 5, 2, 9, 1, 8], dtype=int64), array([0, 1, 8, 9, 2, 5, 3, 6, 7, 4], dtype=int64), array([0, 1, 8, 9, 2, 5, 3, 6, 4, 7], dtype=int64), array([0, 7, 4, 6, 3, 2, 5, 9, 8, 1], dtype=int64), array([0, 8, 9, 5, 6, 7, 4, 3, 2, 1], dtype=int64), array([0, 4, 7, 6, 5, 3, 2, 9, 1, 8], dtype=int64), array([0, 4, 6, 5, 3, 2, 9, 1, 8, 7], dtype=int64), array([0, 7, 4, 6, 3, 5, 9, 2, 1, 8], dtype=int64), array([0, 8, 9, 1, 2, 3, 5, 6, 4, 7], dtype=int64), array([0, 4, 3, 6, 5, 2, 9, 1, 8, 7], dtype=int64), array([0, 8, 9, 1, 2, 3, 5, 6, 7, 4], dtype=int64), array([0, 7, 8, 9, 1, 2, 3, 5, 6, 4], dtype=int64)]
2025-08-05 10:28:38,302 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:38,303 - ExploitationExpert - INFO - populations: [{'tour': array([3, 5, 2, 6, 9, 4, 8, 0, 7, 1], dtype=int64), 'cur_cost': 173.0}, {'tour': [2, 7, 3, 9, 0, 5, 6, 8, 1, 4], 'cur_cost': 195.0}, {'tour': [9, 8, 5, 7, 6, 4, 3, 2, 1, 0], 'cur_cost': 143.0}, {'tour': [3, 4, 6, 5, 9, 7, 0, 8, 2, 1], 'cur_cost': 152.0}, {'tour': [2, 3, 7, 0, 9, 6, 4, 1, 8, 5], 'cur_cost': 162.0}, {'tour': array([2, 5, 0, 6, 8, 3, 7, 1, 9, 4], dtype=int64), 'cur_cost': 192.0}, {'tour': [1, 9, 7, 4, 5, 8, 0, 6, 3, 2], 'cur_cost': 150.0}, {'tour': array([0, 1, 3, 2, 9, 5, 6, 8, 7, 4], dtype=int64), 'cur_cost': 146.0}, {'tour': [1, 7, 6, 9, 2, 5, 8, 0, 4, 3], 'cur_cost': 155.0}, {'tour': [8, 9, 2, 6, 1, 5, 0, 7, 4, 3], 'cur_cost': 169.0}]
2025-08-05 10:28:38,304 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:38,304 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 88, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 88, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:38,304 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([0, 1, 3, 2, 9, 5, 6, 8, 7, 4], dtype=int64), 'cur_cost': 146.0, 'intermediate_solutions': [{'tour': array([7, 2, 4, 8, 9, 5, 1, 0, 3, 6]), 'cur_cost': 176.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 7, 2, 4, 9, 5, 1, 0, 3, 6]), 'cur_cost': 183.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 8, 7, 2, 4, 5, 1, 0, 3, 6]), 'cur_cost': 180.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 8, 7, 2, 9, 5, 1, 0, 3, 6]), 'cur_cost': 164.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 9, 8, 7, 2, 5, 1, 0, 3, 6]), 'cur_cost': 167.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:38,305 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 146.00)
2025-08-05 10:28:38,305 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:38,305 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:38,305 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:38,306 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:38,306 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,306 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,307 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,307 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,307 - ExplorationExpert - INFO - 探索路径生成完成，成本: 143.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:38,307 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [6, 7, 4, 8, 1, 0, 9, 5, 2, 3], 'cur_cost': 143.0, 'intermediate_solutions': [{'tour': [1, 7, 6, 0, 2, 5, 8, 9, 4, 3], 'cur_cost': 180.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 4, 0, 8, 5, 2, 9, 6, 7, 1], 'cur_cost': 155.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 6, 9, 2, 8, 5, 0, 4, 3], 'cur_cost': 170.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:38,307 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 143.00)
2025-08-05 10:28:38,308 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:38,308 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:38,308 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:38,308 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:38,308 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,309 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,309 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,309 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,309 - ExplorationExpert - INFO - 探索路径生成完成，成本: 191.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:38,309 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [1, 6, 5, 0, 3, 7, 8, 9, 2, 4], 'cur_cost': 191.0, 'intermediate_solutions': [{'tour': [8, 9, 6, 2, 1, 5, 0, 7, 4, 3], 'cur_cost': 164.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 9, 2, 6, 1, 5, 0, 7, 3, 4], 'cur_cost': 172.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 9, 2, 6, 1, 0, 5, 7, 4, 3], 'cur_cost': 166.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:38,309 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 191.00)
2025-08-05 10:28:38,309 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:38,309 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:38,311 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 5, 2, 6, 9, 4, 8, 0, 7, 1], dtype=int64), 'cur_cost': 173.0, 'intermediate_solutions': [{'tour': array([0, 2, 8, 7, 6, 5, 9, 4, 3, 1]), 'cur_cost': 170.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 0, 2, 8, 6, 5, 9, 4, 3, 1]), 'cur_cost': 180.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 7, 0, 2, 8, 5, 9, 4, 3, 1]), 'cur_cost': 182.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 7, 0, 2, 6, 5, 9, 4, 3, 1]), 'cur_cost': 170.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 6, 7, 0, 2, 5, 9, 4, 3, 1]), 'cur_cost': 168.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [2, 7, 3, 9, 0, 5, 6, 8, 1, 4], 'cur_cost': 195.0, 'intermediate_solutions': [{'tour': [4, 6, 5, 7, 0, 1, 9, 2, 3, 8], 'cur_cost': 148.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 6, 5, 1, 0, 7, 9, 2, 3, 4], 'cur_cost': 150.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 6, 5, 0, 1, 9, 2, 3, 4, 7], 'cur_cost': 143.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [9, 8, 5, 7, 6, 4, 3, 2, 1, 0], 'cur_cost': 143.0, 'intermediate_solutions': [{'tour': [4, 6, 8, 0, 1, 7, 5, 9, 2, 3], 'cur_cost': 143.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 6, 1, 0, 8, 7, 5, 2, 9, 3], 'cur_cost': 148.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 6, 1, 0, 8, 7, 5, 9, 2, 3], 'cur_cost': 142.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [3, 4, 6, 5, 9, 7, 0, 8, 2, 1], 'cur_cost': 152.0, 'intermediate_solutions': [{'tour': [4, 6, 3, 9, 5, 7, 8, 1, 2, 0], 'cur_cost': 152.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 5, 9, 3, 7, 8, 1, 2, 4], 'cur_cost': 161.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 9, 5, 7, 8, 1, 2, 4, 3], 'cur_cost': 169.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [2, 3, 7, 0, 9, 6, 4, 1, 8, 5], 'cur_cost': 162.0, 'intermediate_solutions': [{'tour': [5, 6, 7, 0, 1, 9, 2, 3, 4, 8], 'cur_cost': 139.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 6, 7, 0, 8, 9, 4, 3, 2, 1], 'cur_cost': 145.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 0, 8, 9, 2, 6, 3, 4, 1], 'cur_cost': 157.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 5, 0, 6, 8, 3, 7, 1, 9, 4], dtype=int64), 'cur_cost': 192.0, 'intermediate_solutions': [{'tour': array([6, 3, 0, 8, 9, 5, 7, 4, 1, 2]), 'cur_cost': 164.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 6, 3, 0, 9, 5, 7, 4, 1, 2]), 'cur_cost': 175.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 8, 6, 3, 0, 5, 7, 4, 1, 2]), 'cur_cost': 171.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 8, 6, 3, 9, 5, 7, 4, 1, 2]), 'cur_cost': 168.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 9, 8, 6, 3, 5, 7, 4, 1, 2]), 'cur_cost': 169.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [1, 9, 7, 4, 5, 8, 0, 6, 3, 2], 'cur_cost': 150.0, 'intermediate_solutions': [{'tour': [6, 7, 4, 9, 5, 3, 8, 0, 1, 2], 'cur_cost': 154.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 7, 4, 9, 5, 6, 1, 0, 8, 2], 'cur_cost': 160.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 7, 8, 4, 9, 5, 6, 0, 1, 2], 'cur_cost': 158.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 1, 3, 2, 9, 5, 6, 8, 7, 4], dtype=int64), 'cur_cost': 146.0, 'intermediate_solutions': [{'tour': array([7, 2, 4, 8, 9, 5, 1, 0, 3, 6]), 'cur_cost': 176.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 7, 2, 4, 9, 5, 1, 0, 3, 6]), 'cur_cost': 183.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 8, 7, 2, 4, 5, 1, 0, 3, 6]), 'cur_cost': 180.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 8, 7, 2, 9, 5, 1, 0, 3, 6]), 'cur_cost': 164.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 9, 8, 7, 2, 5, 1, 0, 3, 6]), 'cur_cost': 167.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [6, 7, 4, 8, 1, 0, 9, 5, 2, 3], 'cur_cost': 143.0, 'intermediate_solutions': [{'tour': [1, 7, 6, 0, 2, 5, 8, 9, 4, 3], 'cur_cost': 180.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 4, 0, 8, 5, 2, 9, 6, 7, 1], 'cur_cost': 155.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 6, 9, 2, 8, 5, 0, 4, 3], 'cur_cost': 170.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [1, 6, 5, 0, 3, 7, 8, 9, 2, 4], 'cur_cost': 191.0, 'intermediate_solutions': [{'tour': [8, 9, 6, 2, 1, 5, 0, 7, 4, 3], 'cur_cost': 164.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 9, 2, 6, 1, 5, 0, 7, 3, 4], 'cur_cost': 172.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 9, 2, 6, 1, 0, 5, 7, 4, 3], 'cur_cost': 166.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:38,311 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:38,312 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:38,313 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=143.000, 多样性=0.911
2025-08-05 10:28:38,313 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 10:28:38,313 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 10:28:38,313 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:38,316 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.054797732988600766, 'best_improvement': -0.007042253521126761}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.043256997455471013}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.03509070064218861, 'recent_improvements': [-0.040276686570291244, 0.011782144589906882, 0.029904714714085973], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 26, 'new_count': 26, 'count_change': 0, 'old_best_cost': 130.0, 'new_best_cost': 130.0, 'quality_improvement': 0.0, 'old_diversity': 0.7409230769230769, 'new_diversity': 0.7409230769230769, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:38,318 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 10:28:38,318 - __main__ - INFO - geometry1_10 开始进化第 5 代
2025-08-05 10:28:38,318 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 10:28:38,318 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:38,319 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=143.000, 多样性=0.911
2025-08-05 10:28:38,319 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:38,320 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.911
2025-08-05 10:28:38,320 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:38,329 - EliteExpert - INFO - 精英解分析完成: 精英解数量=26, 多样性=0.741
2025-08-05 10:28:38,332 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 10:28:38,332 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:38,332 - LandscapeExpert - INFO - 添加精英解数据: 26个精英解
2025-08-05 10:28:38,332 - LandscapeExpert - INFO - 数据提取成功: 36个路径, 36个适应度值
2025-08-05 10:28:38,391 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.722, 适应度梯度: -6.533, 聚类评分: 0.000, 覆盖率: 0.040, 收敛趋势: 0.000, 多样性: 0.228
2025-08-05 10:28:38,391 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 10:28:38,391 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:38,391 - visualization.landscape_visualizer - INFO - 设置当前实例名: geometry1_10
2025-08-05 10:28:38,399 - visualization.landscape_visualizer - INFO - 插值约束: 324 个点被约束到最小值 130.00
2025-08-05 10:28:38,400 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=10.4%, 梯度: 1.89 → 1.69
2025-08-05 10:28:38,506 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry1_10\landscape_geometry1_10_iter_35_20250805_102838.html
2025-08-05 10:28:38,565 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry1_10\dashboard_geometry1_10_iter_35_20250805_102838.html
2025-08-05 10:28:38,565 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 35
2025-08-05 10:28:38,565 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 10:28:38,565 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2329秒
2025-08-05 10:28:38,565 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.7222222222222222, 'modality': 'multi-modal', 'deceptiveness': 'high', 'gradient_strength': -6.533333333333333, 'local_optima_density': 0.7222222222222222, 'gradient_variance': 277.5777777777778, 'cluster_count': 0}, 'population_state': {'diversity': 0.22755731922398587, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0401, 'fitness_entropy': 0.5630437617707797, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'high_local_optima', 'severity': 'high', 'description': '高局部最优密度区域 (密度: 0.722)', 'recommendation': '增加扰动强度'}, {'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -6.533)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.040)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization', 'diversification'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50, 'diversification_strength': 0.6}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360918.3916485, 'performance_metrics': {}}}
2025-08-05 10:28:38,566 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:38,566 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:38,566 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:38,566 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:38,566 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:38,566 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 10:28:38,567 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:38,567 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:38,567 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:38,567 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:38,567 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:38,567 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:38,567 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:38,568 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:38,568 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:38,568 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:38,568 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:38,568 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,568 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,569 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,569 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,569 - ExplorationExpert - INFO - 探索路径生成完成，成本: 144.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:38,569 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [6, 8, 7, 9, 2, 1, 0, 4, 5, 3], 'cur_cost': 144.0, 'intermediate_solutions': [{'tour': [3, 1, 2, 6, 9, 4, 8, 0, 7, 5], 'cur_cost': 170.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 2, 6, 9, 4, 8, 0, 1, 7], 'cur_cost': 167.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 5, 2, 9, 6, 4, 8, 0, 7, 1], 'cur_cost': 158.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:38,570 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 144.00)
2025-08-05 10:28:38,570 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 10:28:38,570 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:38,570 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:38,570 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 162.0
2025-08-05 10:28:38,578 - ExploitationExpert - INFO - res_population_num: 28
2025-08-05 10:28:38,578 - ExploitationExpert - INFO - res_population_costs: [130.0, 130, 130, 130, 130.0, 130, 130, 130.0, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130, 130, 130.0, 130]
2025-08-05 10:28:38,578 - ExploitationExpert - INFO - res_populations: [array([0, 8, 1, 9, 2, 5, 6, 3, 4, 7], dtype=int64), array([0, 1, 9, 2, 5, 6, 3, 4, 7, 8], dtype=int64), array([0, 4, 3, 2, 5, 6, 7, 8, 9, 1], dtype=int64), array([0, 7, 8, 9, 1, 2, 5, 6, 3, 4], dtype=int64), array([0, 7, 6, 4, 3, 5, 2, 9, 8, 1], dtype=int64), array([0, 4, 3, 6, 7, 8, 9, 5, 2, 1], dtype=int64), array([0, 1, 9, 5, 2, 3, 4, 6, 7, 8], dtype=int64), array([0, 1, 2, 9, 5, 3, 6, 4, 7, 8], dtype=int64), array([0, 7, 8, 1, 2, 9, 5, 3, 6, 4], dtype=int64), array([0, 7, 8, 1, 9, 2, 5, 3, 6, 4], dtype=int64), array([0, 8, 1, 9, 2, 5, 3, 4, 6, 7], dtype=int64), array([0, 8, 1, 9, 5, 2, 3, 6, 4, 7], dtype=int64), array([0, 1, 9, 2, 3, 5, 6, 4, 7, 8], dtype=int64), array([0, 7, 8, 9, 1, 2, 5, 3, 6, 4], dtype=int64), array([0, 4, 7, 6, 3, 5, 2, 9, 1, 8], dtype=int64), array([0, 1, 8, 9, 2, 5, 3, 6, 7, 4], dtype=int64), array([0, 1, 8, 9, 2, 5, 3, 6, 4, 7], dtype=int64), array([0, 7, 4, 6, 3, 2, 5, 9, 8, 1], dtype=int64), array([0, 8, 9, 5, 6, 7, 4, 3, 2, 1], dtype=int64), array([0, 4, 7, 6, 5, 3, 2, 9, 1, 8], dtype=int64), array([0, 4, 6, 5, 3, 2, 9, 1, 8, 7], dtype=int64), array([0, 7, 4, 6, 3, 5, 9, 2, 1, 8], dtype=int64), array([0, 8, 9, 1, 2, 3, 5, 6, 4, 7], dtype=int64), array([0, 4, 3, 6, 5, 2, 9, 1, 8, 7], dtype=int64), array([0, 8, 9, 1, 2, 3, 5, 6, 7, 4], dtype=int64), array([0, 7, 8, 9, 1, 2, 3, 5, 6, 4], dtype=int64), array([0, 1, 8, 9, 2, 3, 5, 6, 7, 4], dtype=int64), array([0, 7, 6, 4, 3, 5, 2, 1, 9, 8], dtype=int64)]
2025-08-05 10:28:38,582 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:38,582 - ExploitationExpert - INFO - populations: [{'tour': [6, 8, 7, 9, 2, 1, 0, 4, 5, 3], 'cur_cost': 144.0}, {'tour': array([5, 4, 3, 2, 7, 8, 6, 9, 1, 0], dtype=int64), 'cur_cost': 162.0}, {'tour': [9, 8, 5, 7, 6, 4, 3, 2, 1, 0], 'cur_cost': 143.0}, {'tour': [3, 4, 6, 5, 9, 7, 0, 8, 2, 1], 'cur_cost': 152.0}, {'tour': [2, 3, 7, 0, 9, 6, 4, 1, 8, 5], 'cur_cost': 162.0}, {'tour': [2, 5, 0, 6, 8, 3, 7, 1, 9, 4], 'cur_cost': 192.0}, {'tour': [1, 9, 7, 4, 5, 8, 0, 6, 3, 2], 'cur_cost': 150.0}, {'tour': [0, 1, 3, 2, 9, 5, 6, 8, 7, 4], 'cur_cost': 146.0}, {'tour': [6, 7, 4, 8, 1, 0, 9, 5, 2, 3], 'cur_cost': 143.0}, {'tour': [1, 6, 5, 0, 3, 7, 8, 9, 2, 4], 'cur_cost': 191.0}]
2025-08-05 10:28:38,583 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:38,583 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 89, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 89, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:38,583 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([5, 4, 3, 2, 7, 8, 6, 9, 1, 0], dtype=int64), 'cur_cost': 162.0, 'intermediate_solutions': [{'tour': array([3, 7, 2, 9, 0, 5, 6, 8, 1, 4]), 'cur_cost': 177.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([9, 3, 7, 2, 0, 5, 6, 8, 1, 4]), 'cur_cost': 198.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 9, 3, 7, 2, 5, 6, 8, 1, 4]), 'cur_cost': 174.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 9, 3, 7, 0, 5, 6, 8, 1, 4]), 'cur_cost': 179.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 0, 9, 3, 7, 5, 6, 8, 1, 4]), 'cur_cost': 193.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:38,583 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 162.00)
2025-08-05 10:28:38,583 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:38,583 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:38,584 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:38,584 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:38,584 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,584 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,584 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,584 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,585 - ExplorationExpert - INFO - 探索路径生成完成，成本: 176.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:38,585 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [1, 5, 0, 2, 3, 9, 8, 7, 6, 4], 'cur_cost': 176.0, 'intermediate_solutions': [{'tour': [3, 8, 5, 7, 6, 4, 9, 2, 1, 0], 'cur_cost': 167.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 8, 5, 7, 6, 4, 1, 2, 3, 0], 'cur_cost': 168.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 9, 8, 5, 6, 4, 3, 2, 1, 0], 'cur_cost': 137.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:38,585 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 176.00)
2025-08-05 10:28:38,585 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:38,585 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:38,585 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:38,585 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:38,585 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,586 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,586 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,586 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,586 - ExplorationExpert - INFO - 探索路径生成完成，成本: 168.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:38,586 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [3, 8, 0, 2, 5, 6, 7, 9, 1, 4], 'cur_cost': 168.0, 'intermediate_solutions': [{'tour': [3, 9, 6, 5, 4, 7, 0, 8, 2, 1], 'cur_cost': 164.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 4, 6, 5, 9, 7, 0, 8, 1, 2], 'cur_cost': 134.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 4, 6, 2, 5, 9, 7, 0, 8, 1], 'cur_cost': 152.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:38,586 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 168.00)
2025-08-05 10:28:38,586 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:38,586 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:38,587 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:38,587 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:38,587 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,587 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,587 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,587 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,587 - ExplorationExpert - INFO - 探索路径生成完成，成本: 167.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:38,588 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [2, 7, 3, 9, 1, 8, 5, 6, 4, 0], 'cur_cost': 167.0, 'intermediate_solutions': [{'tour': [2, 3, 1, 0, 9, 6, 4, 7, 8, 5], 'cur_cost': 156.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 9, 0, 7, 3, 6, 4, 1, 8, 5], 'cur_cost': 158.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 3, 7, 0, 9, 6, 4, 1, 8, 5], 'cur_cost': 162.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:38,588 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 167.00)
2025-08-05 10:28:38,588 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:28:38,588 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:38,588 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:38,588 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 192.0
2025-08-05 10:28:38,597 - ExploitationExpert - INFO - res_population_num: 31
2025-08-05 10:28:38,597 - ExploitationExpert - INFO - res_population_costs: [130.0, 130, 130, 130, 130.0, 130, 130, 130.0, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130, 130, 130.0, 130, 130.0, 130, 130]
2025-08-05 10:28:38,597 - ExploitationExpert - INFO - res_populations: [array([0, 8, 1, 9, 2, 5, 6, 3, 4, 7], dtype=int64), array([0, 1, 9, 2, 5, 6, 3, 4, 7, 8], dtype=int64), array([0, 4, 3, 2, 5, 6, 7, 8, 9, 1], dtype=int64), array([0, 7, 8, 9, 1, 2, 5, 6, 3, 4], dtype=int64), array([0, 7, 6, 4, 3, 5, 2, 9, 8, 1], dtype=int64), array([0, 4, 3, 6, 7, 8, 9, 5, 2, 1], dtype=int64), array([0, 1, 9, 5, 2, 3, 4, 6, 7, 8], dtype=int64), array([0, 1, 2, 9, 5, 3, 6, 4, 7, 8], dtype=int64), array([0, 7, 8, 1, 2, 9, 5, 3, 6, 4], dtype=int64), array([0, 7, 8, 1, 9, 2, 5, 3, 6, 4], dtype=int64), array([0, 8, 1, 9, 2, 5, 3, 4, 6, 7], dtype=int64), array([0, 8, 1, 9, 5, 2, 3, 6, 4, 7], dtype=int64), array([0, 1, 9, 2, 3, 5, 6, 4, 7, 8], dtype=int64), array([0, 7, 8, 9, 1, 2, 5, 3, 6, 4], dtype=int64), array([0, 4, 7, 6, 3, 5, 2, 9, 1, 8], dtype=int64), array([0, 1, 8, 9, 2, 5, 3, 6, 7, 4], dtype=int64), array([0, 1, 8, 9, 2, 5, 3, 6, 4, 7], dtype=int64), array([0, 7, 4, 6, 3, 2, 5, 9, 8, 1], dtype=int64), array([0, 8, 9, 5, 6, 7, 4, 3, 2, 1], dtype=int64), array([0, 4, 7, 6, 5, 3, 2, 9, 1, 8], dtype=int64), array([0, 4, 6, 5, 3, 2, 9, 1, 8, 7], dtype=int64), array([0, 7, 4, 6, 3, 5, 9, 2, 1, 8], dtype=int64), array([0, 8, 9, 1, 2, 3, 5, 6, 4, 7], dtype=int64), array([0, 4, 3, 6, 5, 2, 9, 1, 8, 7], dtype=int64), array([0, 8, 9, 1, 2, 3, 5, 6, 7, 4], dtype=int64), array([0, 7, 8, 9, 1, 2, 3, 5, 6, 4], dtype=int64), array([0, 1, 8, 9, 2, 3, 5, 6, 7, 4], dtype=int64), array([0, 7, 6, 4, 3, 5, 2, 1, 9, 8], dtype=int64), array([0, 8, 1, 9, 5, 2, 3, 4, 6, 7], dtype=int64), array([0, 1, 8, 9, 5, 2, 3, 4, 6, 7], dtype=int64), array([0, 8, 1, 9, 2, 5, 3, 6, 4, 7], dtype=int64)]
2025-08-05 10:28:38,602 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:38,602 - ExploitationExpert - INFO - populations: [{'tour': [6, 8, 7, 9, 2, 1, 0, 4, 5, 3], 'cur_cost': 144.0}, {'tour': array([5, 4, 3, 2, 7, 8, 6, 9, 1, 0], dtype=int64), 'cur_cost': 162.0}, {'tour': [1, 5, 0, 2, 3, 9, 8, 7, 6, 4], 'cur_cost': 176.0}, {'tour': [3, 8, 0, 2, 5, 6, 7, 9, 1, 4], 'cur_cost': 168.0}, {'tour': [2, 7, 3, 9, 1, 8, 5, 6, 4, 0], 'cur_cost': 167.0}, {'tour': array([2, 9, 4, 1, 8, 6, 5, 0, 3, 7], dtype=int64), 'cur_cost': 192.0}, {'tour': [1, 9, 7, 4, 5, 8, 0, 6, 3, 2], 'cur_cost': 150.0}, {'tour': [0, 1, 3, 2, 9, 5, 6, 8, 7, 4], 'cur_cost': 146.0}, {'tour': [6, 7, 4, 8, 1, 0, 9, 5, 2, 3], 'cur_cost': 143.0}, {'tour': [1, 6, 5, 0, 3, 7, 8, 9, 2, 4], 'cur_cost': 191.0}]
2025-08-05 10:28:38,603 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:38,603 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 90, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 90, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:38,603 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([2, 9, 4, 1, 8, 6, 5, 0, 3, 7], dtype=int64), 'cur_cost': 192.0, 'intermediate_solutions': [{'tour': array([0, 5, 2, 6, 8, 3, 7, 1, 9, 4]), 'cur_cost': 179.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 0, 5, 2, 8, 3, 7, 1, 9, 4]), 'cur_cost': 182.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 6, 0, 5, 2, 3, 7, 1, 9, 4]), 'cur_cost': 177.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 6, 0, 5, 8, 3, 7, 1, 9, 4]), 'cur_cost': 197.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 8, 6, 0, 5, 3, 7, 1, 9, 4]), 'cur_cost': 189.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:38,604 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 192.00)
2025-08-05 10:28:38,604 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:38,604 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:38,604 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:38,604 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:38,604 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,605 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,605 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,605 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,605 - ExplorationExpert - INFO - 探索路径生成完成，成本: 146.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:38,605 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 1, 9, 8, 5, 6, 7, 4, 3, 2], 'cur_cost': 146.0, 'intermediate_solutions': [{'tour': [1, 4, 7, 9, 5, 8, 0, 6, 3, 2], 'cur_cost': 156.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 9, 7, 4, 5, 8, 0, 6, 2, 3], 'cur_cost': 168.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 9, 4, 5, 8, 0, 6, 3, 2], 'cur_cost': 165.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:38,606 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 146.00)
2025-08-05 10:28:38,606 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:38,606 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:38,606 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:38,606 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:38,606 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,607 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,607 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,607 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,607 - ExplorationExpert - INFO - 探索路径生成完成，成本: 162.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:38,607 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [1, 8, 5, 9, 7, 6, 3, 2, 0, 4], 'cur_cost': 162.0, 'intermediate_solutions': [{'tour': [0, 1, 3, 2, 9, 6, 5, 8, 7, 4], 'cur_cost': 149.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 3, 2, 9, 5, 6, 7, 8, 4], 'cur_cost': 148.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 4, 3, 2, 9, 5, 6, 8, 7], 'cur_cost': 146.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:38,607 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 162.00)
2025-08-05 10:28:38,607 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:38,607 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:38,607 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:38,608 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:38,608 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,608 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,608 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,608 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,608 - ExplorationExpert - INFO - 探索路径生成完成，成本: 157.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:38,609 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [4, 9, 2, 8, 7, 6, 5, 3, 1, 0], 'cur_cost': 157.0, 'intermediate_solutions': [{'tour': [6, 7, 4, 1, 8, 0, 9, 5, 2, 3], 'cur_cost': 149.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 4, 8, 1, 9, 0, 5, 2, 3], 'cur_cost': 152.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 7, 4, 8, 1, 0, 9, 5, 2, 3], 'cur_cost': 143.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:38,609 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 157.00)
2025-08-05 10:28:38,609 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:28:38,609 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:38,609 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:38,609 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 192.0
2025-08-05 10:28:38,620 - ExploitationExpert - INFO - res_population_num: 32
2025-08-05 10:28:38,620 - ExploitationExpert - INFO - res_population_costs: [130.0, 130, 130, 130, 130.0, 130, 130, 130.0, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130, 130, 130.0, 130, 130.0, 130, 130, 130.0]
2025-08-05 10:28:38,620 - ExploitationExpert - INFO - res_populations: [array([0, 8, 1, 9, 2, 5, 6, 3, 4, 7], dtype=int64), array([0, 1, 9, 2, 5, 6, 3, 4, 7, 8], dtype=int64), array([0, 4, 3, 2, 5, 6, 7, 8, 9, 1], dtype=int64), array([0, 7, 8, 9, 1, 2, 5, 6, 3, 4], dtype=int64), array([0, 7, 6, 4, 3, 5, 2, 9, 8, 1], dtype=int64), array([0, 4, 3, 6, 7, 8, 9, 5, 2, 1], dtype=int64), array([0, 1, 9, 5, 2, 3, 4, 6, 7, 8], dtype=int64), array([0, 1, 2, 9, 5, 3, 6, 4, 7, 8], dtype=int64), array([0, 7, 8, 1, 2, 9, 5, 3, 6, 4], dtype=int64), array([0, 7, 8, 1, 9, 2, 5, 3, 6, 4], dtype=int64), array([0, 8, 1, 9, 2, 5, 3, 4, 6, 7], dtype=int64), array([0, 8, 1, 9, 5, 2, 3, 6, 4, 7], dtype=int64), array([0, 1, 9, 2, 3, 5, 6, 4, 7, 8], dtype=int64), array([0, 7, 8, 9, 1, 2, 5, 3, 6, 4], dtype=int64), array([0, 4, 7, 6, 3, 5, 2, 9, 1, 8], dtype=int64), array([0, 1, 8, 9, 2, 5, 3, 6, 7, 4], dtype=int64), array([0, 1, 8, 9, 2, 5, 3, 6, 4, 7], dtype=int64), array([0, 7, 4, 6, 3, 2, 5, 9, 8, 1], dtype=int64), array([0, 8, 9, 5, 6, 7, 4, 3, 2, 1], dtype=int64), array([0, 4, 7, 6, 5, 3, 2, 9, 1, 8], dtype=int64), array([0, 4, 6, 5, 3, 2, 9, 1, 8, 7], dtype=int64), array([0, 7, 4, 6, 3, 5, 9, 2, 1, 8], dtype=int64), array([0, 8, 9, 1, 2, 3, 5, 6, 4, 7], dtype=int64), array([0, 4, 3, 6, 5, 2, 9, 1, 8, 7], dtype=int64), array([0, 8, 9, 1, 2, 3, 5, 6, 7, 4], dtype=int64), array([0, 7, 8, 9, 1, 2, 3, 5, 6, 4], dtype=int64), array([0, 1, 8, 9, 2, 3, 5, 6, 7, 4], dtype=int64), array([0, 7, 6, 4, 3, 5, 2, 1, 9, 8], dtype=int64), array([0, 8, 1, 9, 5, 2, 3, 4, 6, 7], dtype=int64), array([0, 1, 8, 9, 5, 2, 3, 4, 6, 7], dtype=int64), array([0, 8, 1, 9, 2, 5, 3, 6, 4, 7], dtype=int64), array([0, 8, 7, 4, 3, 6, 5, 9, 2, 1], dtype=int64)]
2025-08-05 10:28:38,626 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:38,626 - ExploitationExpert - INFO - populations: [{'tour': [6, 8, 7, 9, 2, 1, 0, 4, 5, 3], 'cur_cost': 144.0}, {'tour': array([5, 4, 3, 2, 7, 8, 6, 9, 1, 0], dtype=int64), 'cur_cost': 162.0}, {'tour': [1, 5, 0, 2, 3, 9, 8, 7, 6, 4], 'cur_cost': 176.0}, {'tour': [3, 8, 0, 2, 5, 6, 7, 9, 1, 4], 'cur_cost': 168.0}, {'tour': [2, 7, 3, 9, 1, 8, 5, 6, 4, 0], 'cur_cost': 167.0}, {'tour': array([2, 9, 4, 1, 8, 6, 5, 0, 3, 7], dtype=int64), 'cur_cost': 192.0}, {'tour': [0, 1, 9, 8, 5, 6, 7, 4, 3, 2], 'cur_cost': 146.0}, {'tour': [1, 8, 5, 9, 7, 6, 3, 2, 0, 4], 'cur_cost': 162.0}, {'tour': [4, 9, 2, 8, 7, 6, 5, 3, 1, 0], 'cur_cost': 157.0}, {'tour': array([7, 1, 4, 0, 3, 8, 5, 9, 6, 2], dtype=int64), 'cur_cost': 192.0}]
2025-08-05 10:28:38,627 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:38,627 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 91, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 91, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:38,627 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([7, 1, 4, 0, 3, 8, 5, 9, 6, 2], dtype=int64), 'cur_cost': 192.0, 'intermediate_solutions': [{'tour': array([5, 6, 1, 0, 3, 7, 8, 9, 2, 4]), 'cur_cost': 176.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 5, 6, 1, 3, 7, 8, 9, 2, 4]), 'cur_cost': 178.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 0, 5, 6, 1, 7, 8, 9, 2, 4]), 'cur_cost': 179.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 0, 5, 6, 3, 7, 8, 9, 2, 4]), 'cur_cost': 169.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 3, 0, 5, 6, 7, 8, 9, 2, 4]), 'cur_cost': 188.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:38,628 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 192.00)
2025-08-05 10:28:38,628 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:38,628 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:38,629 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [6, 8, 7, 9, 2, 1, 0, 4, 5, 3], 'cur_cost': 144.0, 'intermediate_solutions': [{'tour': [3, 1, 2, 6, 9, 4, 8, 0, 7, 5], 'cur_cost': 170.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 2, 6, 9, 4, 8, 0, 1, 7], 'cur_cost': 167.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 5, 2, 9, 6, 4, 8, 0, 7, 1], 'cur_cost': 158.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([5, 4, 3, 2, 7, 8, 6, 9, 1, 0], dtype=int64), 'cur_cost': 162.0, 'intermediate_solutions': [{'tour': array([3, 7, 2, 9, 0, 5, 6, 8, 1, 4]), 'cur_cost': 177.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([9, 3, 7, 2, 0, 5, 6, 8, 1, 4]), 'cur_cost': 198.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 9, 3, 7, 2, 5, 6, 8, 1, 4]), 'cur_cost': 174.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 9, 3, 7, 0, 5, 6, 8, 1, 4]), 'cur_cost': 179.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 0, 9, 3, 7, 5, 6, 8, 1, 4]), 'cur_cost': 193.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [1, 5, 0, 2, 3, 9, 8, 7, 6, 4], 'cur_cost': 176.0, 'intermediate_solutions': [{'tour': [3, 8, 5, 7, 6, 4, 9, 2, 1, 0], 'cur_cost': 167.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 8, 5, 7, 6, 4, 1, 2, 3, 0], 'cur_cost': 168.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 9, 8, 5, 6, 4, 3, 2, 1, 0], 'cur_cost': 137.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [3, 8, 0, 2, 5, 6, 7, 9, 1, 4], 'cur_cost': 168.0, 'intermediate_solutions': [{'tour': [3, 9, 6, 5, 4, 7, 0, 8, 2, 1], 'cur_cost': 164.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 4, 6, 5, 9, 7, 0, 8, 1, 2], 'cur_cost': 134.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 4, 6, 2, 5, 9, 7, 0, 8, 1], 'cur_cost': 152.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [2, 7, 3, 9, 1, 8, 5, 6, 4, 0], 'cur_cost': 167.0, 'intermediate_solutions': [{'tour': [2, 3, 1, 0, 9, 6, 4, 7, 8, 5], 'cur_cost': 156.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 9, 0, 7, 3, 6, 4, 1, 8, 5], 'cur_cost': 158.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 3, 7, 0, 9, 6, 4, 1, 8, 5], 'cur_cost': 162.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 9, 4, 1, 8, 6, 5, 0, 3, 7], dtype=int64), 'cur_cost': 192.0, 'intermediate_solutions': [{'tour': array([0, 5, 2, 6, 8, 3, 7, 1, 9, 4]), 'cur_cost': 179.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 0, 5, 2, 8, 3, 7, 1, 9, 4]), 'cur_cost': 182.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 6, 0, 5, 2, 3, 7, 1, 9, 4]), 'cur_cost': 177.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 6, 0, 5, 8, 3, 7, 1, 9, 4]), 'cur_cost': 197.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 8, 6, 0, 5, 3, 7, 1, 9, 4]), 'cur_cost': 189.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 9, 8, 5, 6, 7, 4, 3, 2], 'cur_cost': 146.0, 'intermediate_solutions': [{'tour': [1, 4, 7, 9, 5, 8, 0, 6, 3, 2], 'cur_cost': 156.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 9, 7, 4, 5, 8, 0, 6, 2, 3], 'cur_cost': 168.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 9, 4, 5, 8, 0, 6, 3, 2], 'cur_cost': 165.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [1, 8, 5, 9, 7, 6, 3, 2, 0, 4], 'cur_cost': 162.0, 'intermediate_solutions': [{'tour': [0, 1, 3, 2, 9, 6, 5, 8, 7, 4], 'cur_cost': 149.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 3, 2, 9, 5, 6, 7, 8, 4], 'cur_cost': 148.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 4, 3, 2, 9, 5, 6, 8, 7], 'cur_cost': 146.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [4, 9, 2, 8, 7, 6, 5, 3, 1, 0], 'cur_cost': 157.0, 'intermediate_solutions': [{'tour': [6, 7, 4, 1, 8, 0, 9, 5, 2, 3], 'cur_cost': 149.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 4, 8, 1, 9, 0, 5, 2, 3], 'cur_cost': 152.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 7, 4, 8, 1, 0, 9, 5, 2, 3], 'cur_cost': 143.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 1, 4, 0, 3, 8, 5, 9, 6, 2], dtype=int64), 'cur_cost': 192.0, 'intermediate_solutions': [{'tour': array([5, 6, 1, 0, 3, 7, 8, 9, 2, 4]), 'cur_cost': 176.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 5, 6, 1, 3, 7, 8, 9, 2, 4]), 'cur_cost': 178.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 0, 5, 6, 1, 7, 8, 9, 2, 4]), 'cur_cost': 179.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 0, 5, 6, 3, 7, 8, 9, 2, 4]), 'cur_cost': 169.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 3, 0, 5, 6, 7, 8, 9, 2, 4]), 'cur_cost': 188.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:28:38,629 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:38,629 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:38,630 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=144.000, 多样性=0.871
2025-08-05 10:28:38,630 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 10:28:38,631 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 10:28:38,631 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:38,634 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.0010481726328720983, 'best_improvement': -0.006993006993006993}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.04390243902439051}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.03328993878925383, 'recent_improvements': [0.011782144589906882, 0.029904714714085973, -0.054797732988600766], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 32, 'new_count': 32, 'count_change': 0, 'old_best_cost': 130.0, 'new_best_cost': 130.0, 'quality_improvement': 0.0, 'old_diversity': 0.7286290322580645, 'new_diversity': 0.7286290322580645, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:38,637 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 10:28:38,641 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\geometry1_10_solution.json
2025-08-05 10:28:38,641 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\geometry1_10_20250805_102838.solution
2025-08-05 10:28:38,641 - __main__ - INFO - 实例执行完成 - 运行时间: 1.48s, 最佳成本: 130.0
2025-08-05 10:28:38,641 - __main__ - INFO - 实例 geometry1_10 处理完成
