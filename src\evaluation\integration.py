"""
评估系统集成工具

提供自动集成和批量配置功能。
"""

import importlib
from typing import Dict, List, Any, Optional, Tuple

from .types import EvaluationType, EvaluationComplexity
from .counter import EvaluationCounter
from .decorators import EvaluationProxy
from .config import EvaluationConfig


class EvaluationIntegrator:
    """评估系统集成器"""
    
    # 预定义的集成配置
    INTEGRATION_CONFIG = {
        'core.algorithms.gls_evol_enhanced': {
            'tour_cost': {
                'evaluation_type': EvaluationType.FULL_EVALUATION,
                'count_per_call': 1,
                'complexity': EvaluationComplexity.O_N,
                'extract_cost': True,
                'priority': 'critical'
            },
            'tour_cost_2End': {
                'evaluation_type': EvaluationType.FULL_EVALUATION,
                'count_per_call': 1,
                'complexity': EvaluationComplexity.O_N,
                'extract_cost': True,
                'priority': 'high'
            }
        },
        'core.algorithms.gls_operators': {
            'two_opt_cost': {
                'evaluation_type': EvaluationType.DELTA_EVALUATION,
                'count_per_call': 4,  # 4次距离查询
                'complexity': EvaluationComplexity.O_1,
                'extract_cost': True,
                'priority': 'critical'
            },
            'relocate_cost': {
                'evaluation_type': EvaluationType.DELTA_EVALUATION,
                'count_per_call': 6,  # 6次距离查询
                'complexity': EvaluationComplexity.O_1,
                'extract_cost': True,
                'priority': 'critical'
            }
        },
        'core.data.initpop': {
            'calculate_population_costs': {
                'evaluation_type': EvaluationType.INITIALIZATION,
                'count_per_call': 'dynamic',  # 动态计算
                'complexity': EvaluationComplexity.O_N,
                'priority': 'medium'
            }
        }
    }
    
    def __init__(self, 
                 counter: Optional[EvaluationCounter] = None,
                 config: Optional[EvaluationConfig] = None):
        """
        初始化集成器
        
        Args:
            counter: 评估计数器
            config: 评估配置
        """
        self.counter = counter
        if self.counter is None:
            from . import get_global_counter
            self.counter = get_global_counter()
        
        self.config = config or EvaluationConfig()
        self.proxies: Dict[str, EvaluationProxy] = {}
        self.integration_status: Dict[str, Dict[str, bool]] = {}
        self.integration_errors: List[str] = []
    
    def setup_integration(self, 
                         modules: Optional[List[str]] = None,
                         priority_filter: Optional[str] = None) -> Dict[str, Any]:
        """
        设置集成
        
        Args:
            modules: 要集成的模块列表（None表示全部）
            priority_filter: 优先级过滤器 ('critical', 'high', 'medium', 'low')
        
        Returns:
            Dict: 集成结果
        """
        if not self.config.enabled:
            return {'status': 'disabled', 'message': '评估统计已禁用'}
        
        results = {
            'successful_modules': 0,
            'total_modules': 0,
            'successful_functions': 0,
            'total_functions': 0,
            'errors': [],
            'warnings': [],
            'integration_details': {}
        }
        
        # 确定要集成的模块
        target_modules = modules or list(self.INTEGRATION_CONFIG.keys())
        
        for module_name in target_modules:
            if module_name not in self.INTEGRATION_CONFIG:
                results['warnings'].append(f"未知模块: {module_name}")
                continue
            
            results['total_modules'] += 1
            module_result = self._integrate_module(module_name, priority_filter)
            
            if module_result['success']:
                results['successful_modules'] += 1
            
            results['successful_functions'] += module_result['successful_functions']
            results['total_functions'] += module_result['total_functions']
            results['errors'].extend(module_result['errors'])
            results['warnings'].extend(module_result['warnings'])
            results['integration_details'][module_name] = module_result
        
        # 计算成功率
        results['module_success_rate'] = (
            results['successful_modules'] / results['total_modules'] 
            if results['total_modules'] > 0 else 0
        )
        results['function_success_rate'] = (
            results['successful_functions'] / results['total_functions'] 
            if results['total_functions'] > 0 else 0
        )
        
        return results
    
    def _integrate_module(self, 
                         module_name: str,
                         priority_filter: Optional[str] = None) -> Dict[str, Any]:
        """集成单个模块"""
        result = {
            'success': False,
            'module_name': module_name,
            'successful_functions': 0,
            'total_functions': 0,
            'errors': [],
            'warnings': [],
            'function_details': {}
        }
        
        try:
            # 导入模块
            module = self._import_module(module_name)
            if module is None:
                result['errors'].append(f"无法导入模块: {module_name}")
                return result
            
            # 创建代理
            proxy = EvaluationProxy(module, self.counter)
            
            # 获取函数配置
            functions_config = self.INTEGRATION_CONFIG[module_name]
            
            # 过滤函数（如果指定了优先级）
            if priority_filter:
                functions_config = {
                    name: config for name, config in functions_config.items()
                    if config.get('priority', 'medium') == priority_filter
                }
            
            result['total_functions'] = len(functions_config)
            
            # 集成函数
            for func_name, func_config in functions_config.items():
                func_result = self._integrate_function(proxy, func_name, func_config)
                result['function_details'][func_name] = func_result
                
                if func_result['success']:
                    result['successful_functions'] += 1
                else:
                    result['errors'].extend(func_result['errors'])
            
            # 保存代理
            self.proxies[module_name] = proxy
            result['success'] = result['successful_functions'] > 0
            
        except Exception as e:
            result['errors'].append(f"模块集成异常: {str(e)}")
        
        return result
    
    def _integrate_function(self, 
                           proxy: EvaluationProxy,
                           func_name: str,
                           func_config: Dict[str, Any]) -> Dict[str, Any]:
        """集成单个函数"""
        result = {
            'success': False,
            'function_name': func_name,
            'errors': [],
            'warnings': []
        }
        
        try:
            # 处理动态计数
            count_per_call = func_config['count_per_call']
            if count_per_call == 'dynamic':
                result['warnings'].append(f"函数 {func_name} 使用动态计数，暂时使用固定值1")
                count_per_call = 1
            
            # 包装函数
            success = proxy.wrap_function(
                func_name=func_name,
                evaluation_type=func_config['evaluation_type'],
                count_per_call=count_per_call,
                complexity=func_config['complexity'],
                extract_cost=func_config.get('extract_cost', False)
            )
            
            result['success'] = success
            if not success:
                result['errors'].append(f"函数 {func_name} 包装失败")
            
        except Exception as e:
            result['errors'].append(f"函数 {func_name} 集成异常: {str(e)}")
        
        return result
    
    def _import_module(self, module_name: str) -> Optional[Any]:
        """安全导入模块"""
        try:
            # 尝试相对导入
            if not module_name.startswith('.'):
                module_name = '.' + module_name
            
            return importlib.import_module(module_name, package='src')
        except ImportError:
            try:
                # 尝试绝对导入
                return importlib.import_module(module_name.lstrip('.'))
            except ImportError:
                return None
    
    def cleanup_integration(self) -> Dict[str, Any]:
        """清理集成"""
        result = {
            'cleaned_modules': 0,
            'total_modules': len(self.proxies),
            'errors': []
        }
        
        for module_name, proxy in self.proxies.items():
            try:
                proxy.restore_all_functions()
                result['cleaned_modules'] += 1
            except Exception as e:
                result['errors'].append(f"清理模块 {module_name} 失败: {str(e)}")
        
        self.proxies.clear()
        self.integration_status.clear()
        
        return result
    
    def get_integration_status(self) -> Dict[str, Any]:
        """获取集成状态"""
        status = {
            'enabled': self.config.enabled,
            'integrated_modules': len(self.proxies),
            'total_wrapped_functions': sum(
                len(proxy.get_wrapped_functions()) for proxy in self.proxies.values()
            ),
            'module_details': {}
        }
        
        for module_name, proxy in self.proxies.items():
            wrapped_functions = proxy.get_wrapped_functions()
            status['module_details'][module_name] = {
                'wrapped_functions': wrapped_functions,
                'function_count': len(wrapped_functions)
            }
        
        return status
    
    def validate_integration(self) -> Dict[str, Any]:
        """验证集成"""
        validation_result = {
            'valid': True,
            'issues': [],
            'warnings': [],
            'statistics': {}
        }
        
        # 检查计数器是否工作
        initial_count = self.counter.get_statistics().total_count
        
        # 手动触发一次计数
        self.counter.increment(EvaluationType.FULL_EVALUATION, 1)
        
        final_count = self.counter.get_statistics().total_count
        
        if final_count != initial_count + 1:
            validation_result['valid'] = False
            validation_result['issues'].append("计数器基本功能异常")
        
        # 检查集成的函数
        for module_name, proxy in self.proxies.items():
            wrapped_functions = proxy.get_wrapped_functions()
            if not wrapped_functions:
                validation_result['warnings'].append(f"模块 {module_name} 没有成功包装任何函数")
        
        # 统计信息
        validation_result['statistics'] = {
            'total_evaluations': self.counter.get_statistics().total_count,
            'integrated_modules': len(self.proxies),
            'wrapped_functions': sum(
                len(proxy.get_wrapped_functions()) for proxy in self.proxies.values()
            )
        }
        
        return validation_result


def setup_evaluation_system(config: Optional[EvaluationConfig] = None,
                           modules: Optional[List[str]] = None,
                           priority_filter: Optional[str] = None) -> Tuple[Optional[EvaluationCounter], Optional[EvaluationIntegrator]]:
    """
    设置评估系统的便捷函数

    Args:
        config: 评估配置
        modules: 要集成的模块列表
        priority_filter: 优先级过滤器

    Returns:
        Tuple[Optional[EvaluationCounter], Optional[EvaluationIntegrator]]: 计数器和集成器（禁用时返回None）
    """
    # 创建配置
    if config is None:
        from .config import get_config
        config = get_config()

    # 检查是否启用评估统计系统
    if not config.enabled:
        print(">> 评估统计系统已禁用，跳过计数器创建和集成")
        # 确保全局计数器为None
        from . import set_global_counter
        set_global_counter(None)
        return None, None

    # 创建计数器（仅在启用时）
    if config.use_numba_counter:
        try:
            from .counter import NumbaCounterWrapper
            counter = NumbaCounterWrapper()
        except RuntimeError:
            print("Numba不可用，使用标准计数器")
            counter = EvaluationCounter(
                enable_detailed_logging=config.detailed_logging,
                max_records=config.max_records,
                batch_size=config.batch_size
            )
    else:
        counter = EvaluationCounter(
            enable_detailed_logging=config.detailed_logging,
            max_records=config.max_records,
            batch_size=config.batch_size
        )

    # 设置全局计数器（仅在启用时）
    from . import set_global_counter
    set_global_counter(counter)

    # 创建集成器
    integrator = EvaluationIntegrator(counter, config)

    # 执行集成
    integration_result = integrator.setup_integration(modules, priority_filter)

    print(f">> 评估系统集成完成:")
    print(f"   模块: {integration_result['successful_modules']}/{integration_result['total_modules']}")
    print(f"   函数: {integration_result['successful_functions']}/{integration_result['total_functions']}")

    if integration_result['errors']:
        print(">> 集成错误:")
        for error in integration_result['errors'][:5]:  # 只显示前5个错误
            print(f"   - {error}")

    return counter, integrator
