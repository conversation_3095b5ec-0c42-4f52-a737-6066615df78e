"""
Individual state monitoring system for the intelligent strategy selection system.

This module provides comprehensive monitoring and tracking of individual states,
performance metrics, and landscape features over time.
"""

from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
import numpy as np
import logging
from collections import defaultdict, deque
import time

from ..core.individual_state import IndividualState, IndividualContext, StagnationLevel
from ..core.data_structures import ExecutionResult, StrategyType
from ..core.landscape_analysis import LandscapeAnalyzer, LandscapeFeatures


@dataclass
class StateSnapshot:
    """Snapshot of system state at a specific time."""
    timestamp: float
    iteration: int
    individual_states: Dict[int, IndividualState]
    landscape_features: LandscapeFeatures
    performance_metrics: Dict[str, float]


class IndividualStateMonitor:
    """
    Comprehensive individual state monitoring system.
    
    This class tracks individual states, performance metrics, and landscape
    features over time to support intelligent strategy selection.
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize the state monitor."""
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Configuration parameters
        self.history_length = self.config.get('history_length', 100)
        self.snapshot_interval = self.config.get('snapshot_interval', 1)
        self.performance_window = self.config.get('performance_window', 10)
        
        # State tracking
        self.individual_states: Dict[int, IndividualState] = {}
        self.state_history: deque = deque(maxlen=self.history_length)
        self.performance_history: Dict[int, deque] = defaultdict(lambda: deque(maxlen=50))
        
        # Landscape analyzer
        self.landscape_analyzer = LandscapeAnalyzer(self.config.get('landscape_config', {}))
        
        # Metrics tracking
        self.iteration_counter = 0
        self.last_snapshot_time = time.time()
        
        # Performance statistics
        self.strategy_performance: Dict[str, Dict[str, float]] = defaultdict(
            lambda: {'success_count': 0, 'total_count': 0, 'total_improvement': 0.0}
        )
    
    def update_individual_state(self, 
                              individual_id: int,
                              solution: List[int],
                              fitness: float,
                              additional_info: Optional[Dict] = None) -> IndividualState:
        """
        Update the state of an individual.
        
        Args:
            individual_id: ID of the individual
            solution: Current solution
            fitness: Current fitness value
            additional_info: Additional state information
            
        Returns:
            Updated IndividualState object
        """
        current_time = time.time()
        
        # Get or create individual state
        if individual_id not in self.individual_states:
            self.individual_states[individual_id] = IndividualState(
                individual_id=individual_id,
                fitness_value=fitness,
                fitness_rank=0,
                fitness_percentile=0.0,
                current_solution=solution.copy()
            )
        
        state = self.individual_states[individual_id]
        
        # Update basic information
        old_fitness = state.fitness_value
        state.fitness_value = fitness
        state.current_solution = solution.copy()
        
        # Update improvement tracking
        improvement = old_fitness - fitness  # For minimization problems
        if improvement > 1e-6:  # Significant improvement
            state.recent_improvements.append(improvement)
            state.stagnation_duration = 0
        else:
            state.stagnation_duration += 1
        
        # Keep only recent improvements
        if len(state.recent_improvements) > 20:
            state.recent_improvements = state.recent_improvements[-20:]
        
        # Update performance trend
        state.performance_trend = state.calculate_performance_trend()
        
        # Update stagnation level
        state.update_stagnation_level()
        
        # Update additional information if provided
        if additional_info:
            for key, value in additional_info.items():
                if hasattr(state, key):
                    setattr(state, key, value)
        
        # Add to performance history
        self.performance_history[individual_id].append({
            'timestamp': current_time,
            'fitness': fitness,
            'improvement': improvement,
            'stagnation_duration': state.stagnation_duration
        })
        
        return state
    
    def update_population_states(self, 
                               population_data: List[Dict[str, Any]],
                               fitness_function: Optional[Callable] = None) -> List[IndividualState]:
        """
        Update states for the entire population.
        
        Args:
            population_data: List of population data dictionaries
            fitness_function: Optional fitness function for landscape analysis
            
        Returns:
            List of updated IndividualState objects
        """
        updated_states = []
        
        # Update individual states
        for data in population_data:
            individual_id = data['individual_id']
            solution = data['solution']
            fitness = data['fitness']
            additional_info = data.get('additional_info', {})
            
            state = self.update_individual_state(
                individual_id, solution, fitness, additional_info
            )
            updated_states.append(state)
        
        # Update population-level metrics
        self._update_population_metrics(updated_states)
        
        # Perform landscape analysis
        if fitness_function:
            landscape_features = self.landscape_analyzer.analyze_landscape(
                updated_states, fitness_function
            )
            self._update_landscape_features(updated_states, landscape_features)
        
        # Create snapshot if needed
        if self._should_create_snapshot():
            self._create_state_snapshot(updated_states)
        
        self.iteration_counter += 1
        return updated_states
    
    def record_strategy_execution(self, 
                                individual_id: int,
                                execution_result: ExecutionResult) -> None:
        """
        Record the result of strategy execution.
        
        Args:
            individual_id: ID of the individual
            execution_result: Result of strategy execution
        """
        if individual_id not in self.individual_states:
            self.logger.warning(f"Individual {individual_id} not found in state monitor")
            return
        
        state = self.individual_states[individual_id]
        
        # Update strategy execution history
        state.last_strategy_type = execution_result.strategy_type.value
        state.last_strategy_success = execution_result.success
        
        # Update strategy preferences
        state.add_exploration_record(
            strategy_type=execution_result.strategy_type.value,
            parameters=execution_result.metadata.get('parameters', {}),
            improvement=execution_result.fitness_improvement,
            success=execution_result.success
        )
        
        # Update strategy performance statistics
        strategy_name = execution_result.strategy_type.value
        stats = self.strategy_performance[strategy_name]
        stats['total_count'] += 1
        if execution_result.success:
            stats['success_count'] += 1
            stats['total_improvement'] += execution_result.fitness_improvement
        
        # Update success rate
        state.strategy_success_rate = self._calculate_recent_success_rate(individual_id)
    
    def get_individual_context(self, 
                             individual_id: int,
                             available_time_budget: float = 1.0) -> Optional[IndividualContext]:
        """
        Get context information for an individual.
        
        Args:
            individual_id: ID of the individual
            available_time_budget: Available time budget for strategy execution
            
        Returns:
            IndividualContext object or None if individual not found
        """
        if individual_id not in self.individual_states:
            return None
        
        state = self.individual_states[individual_id]
        
        # Calculate environmental context
        population_size = len(self.individual_states)
        
        # Find nearby individuals (simplified - based on fitness similarity)
        nearby_individuals = self._find_nearby_individuals(individual_id)
        
        # Create context
        context = IndividualContext(
            individual_state=state,
            current_solution=state.current_solution.copy(),
            current_fitness=state.fitness_value,
            available_time_budget=available_time_budget,
            population_size=population_size,
            current_iteration=self.iteration_counter,
            total_iterations=self.config.get('max_iterations', 1000),
            local_ruggedness=state.local_ruggedness,
            gradient_strength=np.linalg.norm(state.local_gradient) if len(state.local_gradient) > 0 else 0.0,
            nearby_individuals=nearby_individuals
        )
        
        return context
    
    def get_population_summary(self) -> Dict[str, Any]:
        """
        Get summary statistics for the entire population.
        
        Returns:
            Dictionary with population summary statistics
        """
        if not self.individual_states:
            return {}
        
        fitness_values = [state.fitness_value for state in self.individual_states.values()]
        stagnation_durations = [state.stagnation_duration for state in self.individual_states.values()]
        
        return {
            'population_size': len(self.individual_states),
            'best_fitness': min(fitness_values),
            'worst_fitness': max(fitness_values),
            'average_fitness': np.mean(fitness_values),
            'fitness_std': np.std(fitness_values),
            'average_stagnation': np.mean(stagnation_durations),
            'max_stagnation': max(stagnation_durations),
            'stagnated_individuals': sum(1 for d in stagnation_durations if d > 5),
            'strategy_performance': dict(self.strategy_performance),
            'iteration': self.iteration_counter
        }
    
    def get_state_history(self, 
                         individual_id: Optional[int] = None,
                         last_n: Optional[int] = None) -> List[Dict]:
        """
        Get historical state information.
        
        Args:
            individual_id: Specific individual ID (None for all)
            last_n: Number of recent entries to return
            
        Returns:
            List of historical state information
        """
        if individual_id is not None:
            history = list(self.performance_history[individual_id])
        else:
            history = list(self.state_history)
        
        if last_n is not None:
            history = history[-last_n:]
        
        return history
    
    def _update_population_metrics(self, states: List[IndividualState]) -> None:
        """Update population-level metrics."""
        if not states:
            return
        
        # Calculate fitness ranks and percentiles
        fitness_values = [(state.fitness_value, state.individual_id) for state in states]
        fitness_values.sort()  # Sort by fitness (ascending for minimization)
        
        for rank, (fitness, individual_id) in enumerate(fitness_values):
            state = self.individual_states[individual_id]
            state.fitness_rank = rank + 1
            state.fitness_percentile = (rank + 1) / len(fitness_values)
        
        # Calculate distances to best and worst
        best_fitness = fitness_values[0][0]
        worst_fitness = fitness_values[-1][0]
        
        for state in states:
            state.distance_to_best = abs(state.fitness_value - best_fitness)
            state.distance_to_worst = abs(state.fitness_value - worst_fitness)
    
    def _update_landscape_features(self, 
                                 states: List[IndividualState],
                                 landscape_features: LandscapeFeatures) -> None:
        """Update landscape features for individual states."""
        for state in states:
            state.local_ruggedness = landscape_features.local_ruggedness.get(
                state.individual_id, 0.0
            )
            state.local_gradient = landscape_features.local_gradients.get(
                state.individual_id, np.array([])
            )
            state.improvement_potential = landscape_features.improvement_potentials.get(
                state.individual_id, 0.0
            )
    
    def _find_nearby_individuals(self, individual_id: int, max_count: int = 5) -> List[int]:
        """Find nearby individuals based on fitness similarity."""
        if individual_id not in self.individual_states:
            return []
        
        target_fitness = self.individual_states[individual_id].fitness_value
        
        # Calculate fitness distances
        distances = []
        for other_id, other_state in self.individual_states.items():
            if other_id != individual_id:
                distance = abs(other_state.fitness_value - target_fitness)
                distances.append((distance, other_id))
        
        # Sort by distance and return closest individuals
        distances.sort()
        return [individual_id for _, individual_id in distances[:max_count]]
    
    def _calculate_recent_success_rate(self, individual_id: int, window: int = 10) -> float:
        """Calculate recent strategy success rate for an individual."""
        state = self.individual_states[individual_id]
        
        if not state.exploration_history:
            return 0.5  # Default value
        
        recent_records = state.exploration_history[-window:]
        if not recent_records:
            return 0.5
        
        successes = sum(1 for record in recent_records if record.get('success', False))
        return successes / len(recent_records)
    
    def _should_create_snapshot(self) -> bool:
        """Check if a state snapshot should be created."""
        current_time = time.time()
        time_since_last = current_time - self.last_snapshot_time
        
        return (self.iteration_counter % self.snapshot_interval == 0 or
                time_since_last > 60)  # At least every minute
    
    def _create_state_snapshot(self, states: List[IndividualState]) -> None:
        """Create a snapshot of the current system state."""
        current_time = time.time()
        
        # Create snapshot
        snapshot = StateSnapshot(
            timestamp=current_time,
            iteration=self.iteration_counter,
            individual_states={state.individual_id: state for state in states},
            landscape_features=self.landscape_analyzer.landscape_history[-1] if self.landscape_analyzer.landscape_history else LandscapeFeatures(),
            performance_metrics=self.get_population_summary()
        )
        
        self.state_history.append(snapshot)
        self.last_snapshot_time = current_time
