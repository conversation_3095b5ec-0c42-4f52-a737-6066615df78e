2025-08-05 09:51:57,328 - __main__ - INFO - composite3_22 开始进化第 1 代
2025-08-05 09:51:57,328 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 09:51:57,329 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:57,331 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9617.000, 多样性=0.958
2025-08-05 09:51:57,332 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:57,333 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.958
2025-08-05 09:51:57,334 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:57,337 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 09:51:57,337 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:57,337 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 09:51:57,337 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 09:51:57,350 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.200, 适应度梯度: -3375.100, 聚类评分: 0.000, 覆盖率: 0.088, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:51:57,351 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 09:51:57,351 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 09:51:57,351 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite3_22
2025-08-05 09:51:57,357 - visualization.landscape_visualizer - INFO - 插值约束: 278 个点被约束到最小值 9617.00
2025-08-05 09:51:57,463 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite3_22\landscape_composite3_22_iter_71_20250805_095157.html
2025-08-05 09:51:57,518 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite3_22\dashboard_composite3_22_iter_71_20250805_095157.html
2025-08-05 09:51:57,518 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 71
2025-08-05 09:51:57,518 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 09:51:57,519 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1827秒
2025-08-05 09:51:57,519 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 142, 'max_size': 500, 'hits': 0, 'misses': 142, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 468, 'misses': 250, 'hit_rate': 0.6518105849582173, 'evictions': 150, 'ttl': 7200}}
2025-08-05 09:51:57,519 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -3375.0999999999985, 'local_optima_density': 0.2, 'gradient_variance': 333372931.73, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0881, 'fitness_entropy': 0.8173454221465103, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -3375.100)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.088)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358717.3510244, 'performance_metrics': {}}}
2025-08-05 09:51:57,519 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:57,519 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:57,520 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:57,520 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:57,520 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 09:51:57,520 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 09:51:57,521 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 09:51:57,521 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:57,521 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:57,521 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 09:51:57,521 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:57,521 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:57,521 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:57,522 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:51:57,522 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:51:57,522 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:57,523 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 22
2025-08-05 09:51:57,523 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,524 - ExplorationExpert - INFO - 探索路径生成完成，成本: 24005.0, 路径长度: 22, 收集中间解: 0
2025-08-05 09:51:57,524 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [21, 1, 20, 3, 0, 18, 13, 16, 17, 2, 12, 14, 15, 10, 11, 7, 8, 4, 6, 5, 19, 9], 'cur_cost': 24005.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:57,524 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 24005.00)
2025-08-05 09:51:57,524 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:51:57,524 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:51:57,524 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:57,525 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 22
2025-08-05 09:51:57,525 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,525 - ExplorationExpert - INFO - 探索路径生成完成，成本: 33410.0, 路径长度: 22, 收集中间解: 0
2025-08-05 09:51:57,525 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [1, 20, 0, 18, 12, 14, 8, 4, 6, 9, 19, 16, 5, 13, 17, 7, 21, 15, 11, 3, 10, 2], 'cur_cost': 33410.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:57,526 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 33410.00)
2025-08-05 09:51:57,526 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:51:57,526 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:51:57,526 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:57,526 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 09:51:57,527 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,527 - ExplorationExpert - INFO - 探索路径生成完成，成本: 18014.0, 路径长度: 22, 收集中间解: 0
2025-08-05 09:51:57,527 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 7, 9, 3, 1, 2, 19, 15, 16, 17, 18, 20, 21, 13, 12, 11, 10, 14, 8, 4, 5, 6], 'cur_cost': 18014.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:57,527 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 18014.00)
2025-08-05 09:51:57,527 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:51:57,527 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:51:57,527 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:57,528 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 09:51:57,528 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,528 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21094.0, 路径长度: 22, 收集中间解: 0
2025-08-05 09:51:57,528 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [1, 5, 0, 4, 17, 15, 16, 21, 20, 19, 18, 3, 2, 13, 9, 10, 11, 12, 14, 7, 6, 8], 'cur_cost': 21094.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:57,528 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 21094.00)
2025-08-05 09:51:57,529 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:51:57,529 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:51:57,529 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:57,530 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 22
2025-08-05 09:51:57,530 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,530 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17291.0, 路径长度: 22, 收集中间解: 0
2025-08-05 09:51:57,530 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [10, 13, 12, 18, 15, 1, 20, 2, 3, 14, 9, 11, 17, 19, 21, 16, 8, 5, 4, 6, 7, 0], 'cur_cost': 17291.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:57,530 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 17291.00)
2025-08-05 09:51:57,531 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:51:57,531 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:51:57,531 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:57,531 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 22
2025-08-05 09:51:57,531 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,532 - ExplorationExpert - INFO - 探索路径生成完成，成本: 35668.0, 路径长度: 22, 收集中间解: 0
2025-08-05 09:51:57,532 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [10, 1, 20, 0, 18, 13, 4, 17, 2, 12, 3, 5, 11, 16, 21, 15, 6, 7, 8, 9, 14, 19], 'cur_cost': 35668.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:57,532 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 35668.00)
2025-08-05 09:51:57,532 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:51:57,532 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:57,532 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:57,532 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 36820.0
2025-08-05 09:51:57,538 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 09:51:57,538 - ExploitationExpert - INFO - res_population_costs: [9455.0, 9455, 9455, 9455, 9455, 9455]
2025-08-05 09:51:57,538 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  4,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64)]
2025-08-05 09:51:57,540 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:57,540 - ExploitationExpert - INFO - populations: [{'tour': [21, 1, 20, 3, 0, 18, 13, 16, 17, 2, 12, 14, 15, 10, 11, 7, 8, 4, 6, 5, 19, 9], 'cur_cost': 24005.0}, {'tour': [1, 20, 0, 18, 12, 14, 8, 4, 6, 9, 19, 16, 5, 13, 17, 7, 21, 15, 11, 3, 10, 2], 'cur_cost': 33410.0}, {'tour': [0, 7, 9, 3, 1, 2, 19, 15, 16, 17, 18, 20, 21, 13, 12, 11, 10, 14, 8, 4, 5, 6], 'cur_cost': 18014.0}, {'tour': [1, 5, 0, 4, 17, 15, 16, 21, 20, 19, 18, 3, 2, 13, 9, 10, 11, 12, 14, 7, 6, 8], 'cur_cost': 21094.0}, {'tour': [10, 13, 12, 18, 15, 1, 20, 2, 3, 14, 9, 11, 17, 19, 21, 16, 8, 5, 4, 6, 7, 0], 'cur_cost': 17291.0}, {'tour': [10, 1, 20, 0, 18, 13, 4, 17, 2, 12, 3, 5, 11, 16, 21, 15, 6, 7, 8, 9, 14, 19], 'cur_cost': 35668.0}, {'tour': array([11, 13,  6,  1,  9, 14,  7,  0,  8, 18, 19,  5, 12, 17, 16,  3, 20,
        4, 21, 15,  2, 10], dtype=int64), 'cur_cost': 36820.0}, {'tour': array([19, 17, 10, 11,  2,  9,  8, 15, 16,  6, 13, 12,  7, 18,  1, 21,  4,
       14,  0,  5,  3, 20], dtype=int64), 'cur_cost': 39592.0}, {'tour': array([11, 14,  0,  5,  1,  4, 19,  7, 21, 13, 15, 16,  6,  9, 12,  8, 17,
        3, 18, 20, 10,  2], dtype=int64), 'cur_cost': 40023.0}, {'tour': array([ 3,  7, 16, 15, 19, 11, 10, 12,  1,  4,  5,  8,  0, 21, 18,  2, 14,
        9, 20,  6, 17, 13], dtype=int64), 'cur_cost': 29688.0}]
2025-08-05 09:51:57,541 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:57,541 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 183, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 183, 'cache_hits': 0, 'similarity_calculations': 810, 'cache_hit_rate': 0.0, 'cache_size': 810}}
2025-08-05 09:51:57,541 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([11, 13,  6,  1,  9, 14,  7,  0,  8, 18, 19,  5, 12, 17, 16,  3, 20,
        4, 21, 15,  2, 10], dtype=int64), 'cur_cost': 36820.0, 'intermediate_solutions': [{'tour': array([ 8, 21,  0, 19,  5,  9,  3,  6, 20, 18, 16,  7,  1, 17, 12, 14,  4,
       10, 15,  2, 13, 11], dtype=int64), 'cur_cost': 39971.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([19,  8, 21,  0,  5,  9,  3,  6, 20, 18, 16,  7,  1, 17, 12, 14,  4,
       10, 15,  2, 13, 11], dtype=int64), 'cur_cost': 40074.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 5, 19,  8, 21,  0,  9,  3,  6, 20, 18, 16,  7,  1, 17, 12, 14,  4,
       10, 15,  2, 13, 11], dtype=int64), 'cur_cost': 40049.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 0, 19,  8, 21,  5,  9,  3,  6, 20, 18, 16,  7,  1, 17, 12, 14,  4,
       10, 15,  2, 13, 11], dtype=int64), 'cur_cost': 39954.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 0,  5, 19,  8, 21,  9,  3,  6, 20, 18, 16,  7,  1, 17, 12, 14,  4,
       10, 15,  2, 13, 11], dtype=int64), 'cur_cost': 40087.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:57,542 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 36820.00)
2025-08-05 09:51:57,542 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:51:57,542 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:51:57,542 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:57,543 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 22
2025-08-05 09:51:57,543 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,543 - ExplorationExpert - INFO - 探索路径生成完成，成本: 28287.0, 路径长度: 22, 收集中间解: 0
2025-08-05 09:51:57,543 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [7, 20, 2, 3, 0, 19, 1, 13, 10, 16, 14, 18, 12, 17, 9, 15, 8, 5, 4, 6, 21, 11], 'cur_cost': 28287.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:57,544 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 28287.00)
2025-08-05 09:51:57,544 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:51:57,544 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:57,544 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:57,544 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 29081.0
2025-08-05 09:51:57,552 - ExploitationExpert - INFO - res_population_num: 8
2025-08-05 09:51:57,552 - ExploitationExpert - INFO - res_population_costs: [9455.0, 9455, 9455, 9455, 9455, 9455, 9455, 9455]
2025-08-05 09:51:57,552 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  4,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
        9, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11, 10,  9, 14,  3], dtype=int64)]
2025-08-05 09:51:57,555 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:57,555 - ExploitationExpert - INFO - populations: [{'tour': [21, 1, 20, 3, 0, 18, 13, 16, 17, 2, 12, 14, 15, 10, 11, 7, 8, 4, 6, 5, 19, 9], 'cur_cost': 24005.0}, {'tour': [1, 20, 0, 18, 12, 14, 8, 4, 6, 9, 19, 16, 5, 13, 17, 7, 21, 15, 11, 3, 10, 2], 'cur_cost': 33410.0}, {'tour': [0, 7, 9, 3, 1, 2, 19, 15, 16, 17, 18, 20, 21, 13, 12, 11, 10, 14, 8, 4, 5, 6], 'cur_cost': 18014.0}, {'tour': [1, 5, 0, 4, 17, 15, 16, 21, 20, 19, 18, 3, 2, 13, 9, 10, 11, 12, 14, 7, 6, 8], 'cur_cost': 21094.0}, {'tour': [10, 13, 12, 18, 15, 1, 20, 2, 3, 14, 9, 11, 17, 19, 21, 16, 8, 5, 4, 6, 7, 0], 'cur_cost': 17291.0}, {'tour': [10, 1, 20, 0, 18, 13, 4, 17, 2, 12, 3, 5, 11, 16, 21, 15, 6, 7, 8, 9, 14, 19], 'cur_cost': 35668.0}, {'tour': array([11, 13,  6,  1,  9, 14,  7,  0,  8, 18, 19,  5, 12, 17, 16,  3, 20,
        4, 21, 15,  2, 10], dtype=int64), 'cur_cost': 36820.0}, {'tour': [7, 20, 2, 3, 0, 19, 1, 13, 10, 16, 14, 18, 12, 17, 9, 15, 8, 5, 4, 6, 21, 11], 'cur_cost': 28287.0}, {'tour': array([ 1,  6, 18,  4,  8, 16, 20,  0, 21,  7, 13, 11, 12, 14,  9,  2, 15,
        5, 17, 19, 10,  3], dtype=int64), 'cur_cost': 29081.0}, {'tour': array([ 3,  7, 16, 15, 19, 11, 10, 12,  1,  4,  5,  8,  0, 21, 18,  2, 14,
        9, 20,  6, 17, 13], dtype=int64), 'cur_cost': 29688.0}]
2025-08-05 09:51:57,555 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:57,555 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 184, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 184, 'cache_hits': 0, 'similarity_calculations': 811, 'cache_hit_rate': 0.0, 'cache_size': 811}}
2025-08-05 09:51:57,556 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 1,  6, 18,  4,  8, 16, 20,  0, 21,  7, 13, 11, 12, 14,  9,  2, 15,
        5, 17, 19, 10,  3], dtype=int64), 'cur_cost': 29081.0, 'intermediate_solutions': [{'tour': array([ 0, 14, 11,  5,  1,  4, 19,  7, 21, 13, 15, 16,  6,  9, 12,  8, 17,
        3, 18, 20, 10,  2], dtype=int64), 'cur_cost': 37869.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 5,  0, 14, 11,  1,  4, 19,  7, 21, 13, 15, 16,  6,  9, 12,  8, 17,
        3, 18, 20, 10,  2], dtype=int64), 'cur_cost': 40017.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1,  5,  0, 14, 11,  4, 19,  7, 21, 13, 15, 16,  6,  9, 12,  8, 17,
        3, 18, 20, 10,  2], dtype=int64), 'cur_cost': 37921.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([11,  5,  0, 14,  1,  4, 19,  7, 21, 13, 15, 16,  6,  9, 12,  8, 17,
        3, 18, 20, 10,  2], dtype=int64), 'cur_cost': 42742.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([11,  1,  5,  0, 14,  4, 19,  7, 21, 13, 15, 16,  6,  9, 12,  8, 17,
        3, 18, 20, 10,  2], dtype=int64), 'cur_cost': 42820.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:57,556 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 29081.00)
2025-08-05 09:51:57,556 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:51:57,556 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:51:57,556 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:57,557 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 09:51:57,557 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,557 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17485.0, 路径长度: 22, 收集中间解: 0
2025-08-05 09:51:57,557 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [1, 7, 5, 0, 16, 15, 17, 18, 19, 20, 21, 3, 2, 13, 9, 10, 11, 12, 14, 8, 4, 6], 'cur_cost': 17485.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:57,557 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 17485.00)
2025-08-05 09:51:57,558 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:57,558 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:57,559 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [21, 1, 20, 3, 0, 18, 13, 16, 17, 2, 12, 14, 15, 10, 11, 7, 8, 4, 6, 5, 19, 9], 'cur_cost': 24005.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [1, 20, 0, 18, 12, 14, 8, 4, 6, 9, 19, 16, 5, 13, 17, 7, 21, 15, 11, 3, 10, 2], 'cur_cost': 33410.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 9, 3, 1, 2, 19, 15, 16, 17, 18, 20, 21, 13, 12, 11, 10, 14, 8, 4, 5, 6], 'cur_cost': 18014.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [1, 5, 0, 4, 17, 15, 16, 21, 20, 19, 18, 3, 2, 13, 9, 10, 11, 12, 14, 7, 6, 8], 'cur_cost': 21094.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [10, 13, 12, 18, 15, 1, 20, 2, 3, 14, 9, 11, 17, 19, 21, 16, 8, 5, 4, 6, 7, 0], 'cur_cost': 17291.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [10, 1, 20, 0, 18, 13, 4, 17, 2, 12, 3, 5, 11, 16, 21, 15, 6, 7, 8, 9, 14, 19], 'cur_cost': 35668.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([11, 13,  6,  1,  9, 14,  7,  0,  8, 18, 19,  5, 12, 17, 16,  3, 20,
        4, 21, 15,  2, 10], dtype=int64), 'cur_cost': 36820.0, 'intermediate_solutions': [{'tour': array([ 8, 21,  0, 19,  5,  9,  3,  6, 20, 18, 16,  7,  1, 17, 12, 14,  4,
       10, 15,  2, 13, 11], dtype=int64), 'cur_cost': 39971.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([19,  8, 21,  0,  5,  9,  3,  6, 20, 18, 16,  7,  1, 17, 12, 14,  4,
       10, 15,  2, 13, 11], dtype=int64), 'cur_cost': 40074.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 5, 19,  8, 21,  0,  9,  3,  6, 20, 18, 16,  7,  1, 17, 12, 14,  4,
       10, 15,  2, 13, 11], dtype=int64), 'cur_cost': 40049.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 0, 19,  8, 21,  5,  9,  3,  6, 20, 18, 16,  7,  1, 17, 12, 14,  4,
       10, 15,  2, 13, 11], dtype=int64), 'cur_cost': 39954.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 0,  5, 19,  8, 21,  9,  3,  6, 20, 18, 16,  7,  1, 17, 12, 14,  4,
       10, 15,  2, 13, 11], dtype=int64), 'cur_cost': 40087.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [7, 20, 2, 3, 0, 19, 1, 13, 10, 16, 14, 18, 12, 17, 9, 15, 8, 5, 4, 6, 21, 11], 'cur_cost': 28287.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 1,  6, 18,  4,  8, 16, 20,  0, 21,  7, 13, 11, 12, 14,  9,  2, 15,
        5, 17, 19, 10,  3], dtype=int64), 'cur_cost': 29081.0, 'intermediate_solutions': [{'tour': array([ 0, 14, 11,  5,  1,  4, 19,  7, 21, 13, 15, 16,  6,  9, 12,  8, 17,
        3, 18, 20, 10,  2], dtype=int64), 'cur_cost': 37869.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 5,  0, 14, 11,  1,  4, 19,  7, 21, 13, 15, 16,  6,  9, 12,  8, 17,
        3, 18, 20, 10,  2], dtype=int64), 'cur_cost': 40017.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1,  5,  0, 14, 11,  4, 19,  7, 21, 13, 15, 16,  6,  9, 12,  8, 17,
        3, 18, 20, 10,  2], dtype=int64), 'cur_cost': 37921.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([11,  5,  0, 14,  1,  4, 19,  7, 21, 13, 15, 16,  6,  9, 12,  8, 17,
        3, 18, 20, 10,  2], dtype=int64), 'cur_cost': 42742.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([11,  1,  5,  0, 14,  4, 19,  7, 21, 13, 15, 16,  6,  9, 12,  8, 17,
        3, 18, 20, 10,  2], dtype=int64), 'cur_cost': 42820.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 5, 0, 16, 15, 17, 18, 19, 20, 21, 3, 2, 13, 9, 10, 11, 12, 14, 8, 4, 6], 'cur_cost': 17485.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 09:51:57,559 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:57,559 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:57,561 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=17291.000, 多样性=0.934
2025-08-05 09:51:57,561 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 09:51:57,561 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 09:51:57,562 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:57,562 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.20215401716766235, 'best_improvement': -0.7979619423936779}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.02426160337552758}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.002670507399929587, 'recent_improvements': [-0.04347507641122519, 0.0028272904018603706, -0.04881609121108437], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 8, 'new_count': 8, 'count_change': 0, 'old_best_cost': 9455.0, 'new_best_cost': 9455.0, 'quality_improvement': 0.0, 'old_diversity': 0.7337662337662337, 'new_diversity': 0.7337662337662337, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启']}
2025-08-05 09:51:57,563 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 09:51:57,563 - __main__ - INFO - composite3_22 开始进化第 2 代
2025-08-05 09:51:57,563 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 09:51:57,563 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:57,564 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=17291.000, 多样性=0.934
2025-08-05 09:51:57,564 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:57,565 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.934
2025-08-05 09:51:57,565 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:57,567 - EliteExpert - INFO - 精英解分析完成: 精英解数量=8, 多样性=0.734
2025-08-05 09:51:57,569 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 09:51:57,569 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:57,569 - LandscapeExpert - INFO - 添加精英解数据: 8个精英解
2025-08-05 09:51:57,569 - LandscapeExpert - INFO - 数据提取成功: 18个路径, 18个适应度值
2025-08-05 09:51:57,592 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.500, 适应度梯度: -1130.300, 聚类评分: 0.000, 覆盖率: 0.090, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:51:57,593 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 09:51:57,593 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:51:57,593 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite3_22
2025-08-05 09:51:57,599 - visualization.landscape_visualizer - INFO - 插值约束: 39 个点被约束到最小值 9455.00
2025-08-05 09:51:57,702 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite3_22\landscape_composite3_22_iter_72_20250805_095157.html
2025-08-05 09:51:57,749 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite3_22\dashboard_composite3_22_iter_72_20250805_095157.html
2025-08-05 09:51:57,749 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 72
2025-08-05 09:51:57,749 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 09:51:57,749 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1793秒
2025-08-05 09:51:57,750 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -1130.3000000000002, 'local_optima_density': 0.5, 'gradient_variance': 89879151.63666669, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0898, 'fitness_entropy': 0.8247417351190395, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -1130.300)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.090)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358717.593142, 'performance_metrics': {}}}
2025-08-05 09:51:57,750 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:57,750 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:57,750 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:57,750 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:57,751 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:51:57,751 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 09:51:57,751 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:51:57,751 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:57,751 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:57,751 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:51:57,752 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:57,752 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:57,752 - experts.management.collaboration_manager - INFO - 识别精英个体: {9, 4} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:57,752 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:51:57,752 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:51:57,752 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:57,753 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 22
2025-08-05 09:51:57,753 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,753 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,753 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,754 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,754 - ExplorationExpert - INFO - 探索路径生成完成，成本: 29262.0, 路径长度: 22, 收集中间解: 3
2025-08-05 09:51:57,754 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [21, 5, 12, 1, 17, 18, 13, 16, 20, 2, 9, 14, 15, 10, 11, 3, 6, 7, 8, 4, 19, 0], 'cur_cost': 29262.0, 'intermediate_solutions': [{'tour': [21, 1, 12, 3, 0, 18, 13, 16, 17, 2, 20, 14, 15, 10, 11, 7, 8, 4, 6, 5, 19, 9], 'cur_cost': 26746.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [21, 1, 20, 3, 0, 18, 13, 16, 17, 2, 12, 14, 15, 10, 11, 7, 8, 5, 6, 4, 19, 9], 'cur_cost': 23984.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 20, 21, 3, 0, 18, 13, 16, 17, 2, 12, 14, 15, 10, 11, 7, 8, 4, 6, 5, 19, 9], 'cur_cost': 23648.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:57,754 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 29262.00)
2025-08-05 09:51:57,754 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:51:57,755 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:51:57,755 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:57,756 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 22
2025-08-05 09:51:57,756 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,756 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,756 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,756 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,757 - ExplorationExpert - INFO - 探索路径生成完成，成本: 22941.0, 路径长度: 22, 收集中间解: 3
2025-08-05 09:51:57,757 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [15, 16, 20, 17, 3, 0, 9, 10, 18, 12, 19, 21, 14, 1, 11, 13, 8, 4, 6, 5, 7, 2], 'cur_cost': 22941.0, 'intermediate_solutions': [{'tour': [1, 20, 0, 18, 12, 14, 7, 4, 6, 9, 19, 16, 5, 13, 17, 8, 21, 15, 11, 3, 10, 2], 'cur_cost': 33454.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 20, 0, 18, 12, 14, 8, 4, 6, 2, 10, 3, 11, 15, 21, 7, 17, 13, 5, 16, 19, 9], 'cur_cost': 35451.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 20, 0, 18, 12, 14, 8, 4, 6, 9, 13, 19, 16, 5, 17, 7, 21, 15, 11, 3, 10, 2], 'cur_cost': 30777.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:57,757 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 22941.00)
2025-08-05 09:51:57,757 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:51:57,757 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:51:57,757 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:57,758 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 22
2025-08-05 09:51:57,758 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,758 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,759 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,759 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,759 - ExplorationExpert - INFO - 探索路径生成完成，成本: 29242.0, 路径长度: 22, 收集中间解: 3
2025-08-05 09:51:57,759 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [21, 16, 9, 17, 3, 18, 13, 10, 2, 19, 14, 20, 15, 1, 12, 11, 6, 7, 4, 5, 0, 8], 'cur_cost': 29242.0, 'intermediate_solutions': [{'tour': [0, 4, 9, 3, 1, 2, 19, 15, 16, 17, 18, 20, 21, 13, 12, 11, 10, 14, 8, 7, 5, 6], 'cur_cost': 18136.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 9, 3, 1, 2, 19, 15, 16, 17, 18, 20, 21, 13, 12, 11, 10, 6, 5, 4, 8, 14], 'cur_cost': 20791.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 9, 3, 1, 2, 19, 15, 16, 6, 17, 18, 20, 21, 13, 12, 11, 10, 14, 8, 4, 5], 'cur_cost': 22034.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:57,759 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 29242.00)
2025-08-05 09:51:57,759 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:51:57,759 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:51:57,759 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:57,760 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 22
2025-08-05 09:51:57,761 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,761 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,761 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,761 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,761 - ExplorationExpert - INFO - 探索路径生成完成，成本: 24214.0, 路径长度: 22, 收集中间解: 3
2025-08-05 09:51:57,761 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [18, 16, 1, 2, 19, 20, 13, 15, 9, 11, 21, 12, 17, 14, 7, 6, 5, 8, 4, 0, 3, 10], 'cur_cost': 24214.0, 'intermediate_solutions': [{'tour': [1, 5, 6, 4, 17, 15, 16, 21, 20, 19, 18, 3, 2, 13, 9, 10, 11, 12, 14, 7, 0, 8], 'cur_cost': 20977.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 5, 0, 4, 17, 15, 16, 21, 20, 19, 18, 13, 2, 3, 9, 10, 11, 12, 14, 7, 6, 8], 'cur_cost': 23781.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 5, 0, 4, 17, 15, 16, 21, 20, 19, 18, 3, 2, 13, 9, 10, 12, 14, 7, 11, 6, 8], 'cur_cost': 27444.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:57,761 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 24214.00)
2025-08-05 09:51:57,762 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:51:57,762 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:51:57,762 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:57,762 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 09:51:57,762 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,763 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,763 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,763 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,763 - ExplorationExpert - INFO - 探索路径生成完成，成本: 18068.0, 路径长度: 22, 收集中间解: 3
2025-08-05 09:51:57,763 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 11, 4, 1, 2, 3, 19, 15, 16, 17, 18, 20, 21, 13, 9, 10, 14, 12, 8, 5, 6, 7], 'cur_cost': 18068.0, 'intermediate_solutions': [{'tour': [10, 13, 12, 18, 15, 1, 20, 21, 3, 14, 9, 11, 17, 19, 2, 16, 8, 5, 4, 6, 7, 0], 'cur_cost': 19742.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 13, 12, 18, 15, 1, 20, 2, 3, 14, 9, 11, 17, 19, 21, 16, 8, 5, 6, 4, 7, 0], 'cur_cost': 17291.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 13, 12, 15, 18, 1, 20, 2, 3, 14, 9, 11, 17, 19, 21, 16, 8, 5, 4, 6, 7, 0], 'cur_cost': 17286.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:57,763 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 18068.00)
2025-08-05 09:51:57,763 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 09:51:57,763 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:57,764 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:57,764 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 34105.0
2025-08-05 09:51:57,773 - ExploitationExpert - INFO - res_population_num: 9
2025-08-05 09:51:57,773 - ExploitationExpert - INFO - res_population_costs: [9455.0, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455]
2025-08-05 09:51:57,773 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  4,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
        9, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11, 10,  9, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64)]
2025-08-05 09:51:57,775 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:57,775 - ExploitationExpert - INFO - populations: [{'tour': [21, 5, 12, 1, 17, 18, 13, 16, 20, 2, 9, 14, 15, 10, 11, 3, 6, 7, 8, 4, 19, 0], 'cur_cost': 29262.0}, {'tour': [15, 16, 20, 17, 3, 0, 9, 10, 18, 12, 19, 21, 14, 1, 11, 13, 8, 4, 6, 5, 7, 2], 'cur_cost': 22941.0}, {'tour': [21, 16, 9, 17, 3, 18, 13, 10, 2, 19, 14, 20, 15, 1, 12, 11, 6, 7, 4, 5, 0, 8], 'cur_cost': 29242.0}, {'tour': [18, 16, 1, 2, 19, 20, 13, 15, 9, 11, 21, 12, 17, 14, 7, 6, 5, 8, 4, 0, 3, 10], 'cur_cost': 24214.0}, {'tour': [0, 11, 4, 1, 2, 3, 19, 15, 16, 17, 18, 20, 21, 13, 9, 10, 14, 12, 8, 5, 6, 7], 'cur_cost': 18068.0}, {'tour': array([ 0, 19, 16, 15, 10, 14, 17,  3,  6, 11, 18, 12,  9,  4,  5, 13,  1,
       21,  8, 20,  7,  2], dtype=int64), 'cur_cost': 34105.0}, {'tour': [11, 13, 6, 1, 9, 14, 7, 0, 8, 18, 19, 5, 12, 17, 16, 3, 20, 4, 21, 15, 2, 10], 'cur_cost': 36820.0}, {'tour': [7, 20, 2, 3, 0, 19, 1, 13, 10, 16, 14, 18, 12, 17, 9, 15, 8, 5, 4, 6, 21, 11], 'cur_cost': 28287.0}, {'tour': [1, 6, 18, 4, 8, 16, 20, 0, 21, 7, 13, 11, 12, 14, 9, 2, 15, 5, 17, 19, 10, 3], 'cur_cost': 29081.0}, {'tour': [1, 7, 5, 0, 16, 15, 17, 18, 19, 20, 21, 3, 2, 13, 9, 10, 11, 12, 14, 8, 4, 6], 'cur_cost': 17485.0}]
2025-08-05 09:51:57,776 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:57,776 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 185, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 185, 'cache_hits': 0, 'similarity_calculations': 813, 'cache_hit_rate': 0.0, 'cache_size': 813}}
2025-08-05 09:51:57,776 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([ 0, 19, 16, 15, 10, 14, 17,  3,  6, 11, 18, 12,  9,  4,  5, 13,  1,
       21,  8, 20,  7,  2], dtype=int64), 'cur_cost': 34105.0, 'intermediate_solutions': [{'tour': array([20,  1, 10,  0, 18, 13,  4, 17,  2, 12,  3,  5, 11, 16, 21, 15,  6,
        7,  8,  9, 14, 19]), 'cur_cost': 35270.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 0, 20,  1, 10, 18, 13,  4, 17,  2, 12,  3,  5, 11, 16, 21, 15,  6,
        7,  8,  9, 14, 19]), 'cur_cost': 35611.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([18,  0, 20,  1, 10, 13,  4, 17,  2, 12,  3,  5, 11, 16, 21, 15,  6,
        7,  8,  9, 14, 19]), 'cur_cost': 32583.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([10,  0, 20,  1, 18, 13,  4, 17,  2, 12,  3,  5, 11, 16, 21, 15,  6,
        7,  8,  9, 14, 19]), 'cur_cost': 35645.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([10, 18,  0, 20,  1, 13,  4, 17,  2, 12,  3,  5, 11, 16, 21, 15,  6,
        7,  8,  9, 14, 19]), 'cur_cost': 35684.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:57,777 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 34105.00)
2025-08-05 09:51:57,777 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:51:57,777 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:57,777 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:57,777 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 27604.0
2025-08-05 09:51:57,785 - ExploitationExpert - INFO - res_population_num: 9
2025-08-05 09:51:57,786 - ExploitationExpert - INFO - res_population_costs: [9455.0, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455]
2025-08-05 09:51:57,786 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  4,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
        9, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11, 10,  9, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64)]
2025-08-05 09:51:57,788 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:57,788 - ExploitationExpert - INFO - populations: [{'tour': [21, 5, 12, 1, 17, 18, 13, 16, 20, 2, 9, 14, 15, 10, 11, 3, 6, 7, 8, 4, 19, 0], 'cur_cost': 29262.0}, {'tour': [15, 16, 20, 17, 3, 0, 9, 10, 18, 12, 19, 21, 14, 1, 11, 13, 8, 4, 6, 5, 7, 2], 'cur_cost': 22941.0}, {'tour': [21, 16, 9, 17, 3, 18, 13, 10, 2, 19, 14, 20, 15, 1, 12, 11, 6, 7, 4, 5, 0, 8], 'cur_cost': 29242.0}, {'tour': [18, 16, 1, 2, 19, 20, 13, 15, 9, 11, 21, 12, 17, 14, 7, 6, 5, 8, 4, 0, 3, 10], 'cur_cost': 24214.0}, {'tour': [0, 11, 4, 1, 2, 3, 19, 15, 16, 17, 18, 20, 21, 13, 9, 10, 14, 12, 8, 5, 6, 7], 'cur_cost': 18068.0}, {'tour': array([ 0, 19, 16, 15, 10, 14, 17,  3,  6, 11, 18, 12,  9,  4,  5, 13,  1,
       21,  8, 20,  7,  2], dtype=int64), 'cur_cost': 34105.0}, {'tour': array([13, 11,  8, 16, 10,  9,  0,  5,  4, 18, 19, 17, 14, 12,  3,  2, 15,
        1,  6,  7, 20, 21], dtype=int64), 'cur_cost': 27604.0}, {'tour': [7, 20, 2, 3, 0, 19, 1, 13, 10, 16, 14, 18, 12, 17, 9, 15, 8, 5, 4, 6, 21, 11], 'cur_cost': 28287.0}, {'tour': [1, 6, 18, 4, 8, 16, 20, 0, 21, 7, 13, 11, 12, 14, 9, 2, 15, 5, 17, 19, 10, 3], 'cur_cost': 29081.0}, {'tour': [1, 7, 5, 0, 16, 15, 17, 18, 19, 20, 21, 3, 2, 13, 9, 10, 11, 12, 14, 8, 4, 6], 'cur_cost': 17485.0}]
2025-08-05 09:51:57,789 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:57,790 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 186, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 186, 'cache_hits': 0, 'similarity_calculations': 816, 'cache_hit_rate': 0.0, 'cache_size': 816}}
2025-08-05 09:51:57,790 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([13, 11,  8, 16, 10,  9,  0,  5,  4, 18, 19, 17, 14, 12,  3,  2, 15,
        1,  6,  7, 20, 21], dtype=int64), 'cur_cost': 27604.0, 'intermediate_solutions': [{'tour': array([ 6, 13, 11,  1,  9, 14,  7,  0,  8, 18, 19,  5, 12, 17, 16,  3, 20,
        4, 21, 15,  2, 10]), 'cur_cost': 39704.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1,  6, 13, 11,  9, 14,  7,  0,  8, 18, 19,  5, 12, 17, 16,  3, 20,
        4, 21, 15,  2, 10]), 'cur_cost': 36834.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9,  1,  6, 13, 11, 14,  7,  0,  8, 18, 19,  5, 12, 17, 16,  3, 20,
        4, 21, 15,  2, 10]), 'cur_cost': 36850.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([11,  1,  6, 13,  9, 14,  7,  0,  8, 18, 19,  5, 12, 17, 16,  3, 20,
        4, 21, 15,  2, 10]), 'cur_cost': 36823.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([11,  9,  1,  6, 13, 14,  7,  0,  8, 18, 19,  5, 12, 17, 16,  3, 20,
        4, 21, 15,  2, 10]), 'cur_cost': 36790.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:57,791 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 27604.00)
2025-08-05 09:51:57,791 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:51:57,791 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:51:57,791 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:57,791 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 22
2025-08-05 09:51:57,792 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,792 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,792 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,792 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,792 - ExplorationExpert - INFO - 探索路径生成完成，成本: 28803.0, 路径长度: 22, 收集中间解: 3
2025-08-05 09:51:57,792 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [18, 20, 17, 0, 9, 16, 2, 12, 13, 14, 21, 10, 7, 3, 5, 4, 6, 8, 11, 1, 15, 19], 'cur_cost': 28803.0, 'intermediate_solutions': [{'tour': [7, 20, 6, 3, 0, 19, 1, 13, 10, 16, 14, 18, 12, 17, 9, 15, 8, 5, 4, 2, 21, 11], 'cur_cost': 33974.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 20, 2, 3, 0, 19, 1, 4, 5, 8, 15, 9, 17, 12, 18, 14, 16, 10, 13, 6, 21, 11], 'cur_cost': 31851.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 20, 2, 3, 0, 19, 1, 13, 10, 16, 14, 18, 12, 17, 9, 15, 8, 5, 6, 4, 21, 11], 'cur_cost': 28288.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:57,793 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 28803.00)
2025-08-05 09:51:57,793 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:51:57,793 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:51:57,793 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:57,793 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 09:51:57,794 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,794 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,794 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,794 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,794 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17854.0, 路径长度: 22, 收集中间解: 3
2025-08-05 09:51:57,795 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [1, 5, 0, 18, 7, 4, 6, 8, 21, 15, 16, 17, 19, 20, 3, 2, 13, 9, 10, 11, 12, 14], 'cur_cost': 17854.0, 'intermediate_solutions': [{'tour': [1, 6, 18, 4, 8, 16, 20, 0, 21, 7, 13, 11, 3, 14, 9, 2, 15, 5, 17, 19, 10, 12], 'cur_cost': 33835.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 18, 4, 8, 16, 20, 0, 21, 7, 13, 11, 12, 14, 3, 10, 19, 17, 5, 15, 2, 9], 'cur_cost': 33818.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 18, 4, 8, 16, 20, 0, 21, 7, 13, 6, 11, 12, 14, 9, 2, 15, 5, 17, 19, 10, 3], 'cur_cost': 31758.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:57,795 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 17854.00)
2025-08-05 09:51:57,795 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:51:57,795 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:51:57,795 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:57,796 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 22
2025-08-05 09:51:57,796 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,796 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,796 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,796 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,796 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32601.0, 路径长度: 22, 收集中间解: 3
2025-08-05 09:51:57,796 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [15, 20, 2, 0, 9, 18, 12, 14, 21, 17, 10, 7, 11, 8, 4, 6, 1, 5, 13, 3, 19, 16], 'cur_cost': 32601.0, 'intermediate_solutions': [{'tour': [1, 7, 5, 0, 16, 15, 17, 18, 19, 20, 21, 3, 11, 13, 9, 10, 2, 12, 14, 8, 4, 6], 'cur_cost': 22410.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 7, 5, 0, 16, 15, 17, 18, 19, 20, 21, 3, 2, 13, 9, 10, 11, 12, 14, 8, 6, 4], 'cur_cost': 17524.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 5, 0, 16, 15, 17, 18, 19, 20, 21, 3, 2, 13, 9, 10, 12, 14, 8, 4, 11, 6], 'cur_cost': 23878.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:57,797 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 32601.00)
2025-08-05 09:51:57,797 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:57,797 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:57,798 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [21, 5, 12, 1, 17, 18, 13, 16, 20, 2, 9, 14, 15, 10, 11, 3, 6, 7, 8, 4, 19, 0], 'cur_cost': 29262.0, 'intermediate_solutions': [{'tour': [21, 1, 12, 3, 0, 18, 13, 16, 17, 2, 20, 14, 15, 10, 11, 7, 8, 4, 6, 5, 19, 9], 'cur_cost': 26746.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [21, 1, 20, 3, 0, 18, 13, 16, 17, 2, 12, 14, 15, 10, 11, 7, 8, 5, 6, 4, 19, 9], 'cur_cost': 23984.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 20, 21, 3, 0, 18, 13, 16, 17, 2, 12, 14, 15, 10, 11, 7, 8, 4, 6, 5, 19, 9], 'cur_cost': 23648.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [15, 16, 20, 17, 3, 0, 9, 10, 18, 12, 19, 21, 14, 1, 11, 13, 8, 4, 6, 5, 7, 2], 'cur_cost': 22941.0, 'intermediate_solutions': [{'tour': [1, 20, 0, 18, 12, 14, 7, 4, 6, 9, 19, 16, 5, 13, 17, 8, 21, 15, 11, 3, 10, 2], 'cur_cost': 33454.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 20, 0, 18, 12, 14, 8, 4, 6, 2, 10, 3, 11, 15, 21, 7, 17, 13, 5, 16, 19, 9], 'cur_cost': 35451.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 20, 0, 18, 12, 14, 8, 4, 6, 9, 13, 19, 16, 5, 17, 7, 21, 15, 11, 3, 10, 2], 'cur_cost': 30777.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [21, 16, 9, 17, 3, 18, 13, 10, 2, 19, 14, 20, 15, 1, 12, 11, 6, 7, 4, 5, 0, 8], 'cur_cost': 29242.0, 'intermediate_solutions': [{'tour': [0, 4, 9, 3, 1, 2, 19, 15, 16, 17, 18, 20, 21, 13, 12, 11, 10, 14, 8, 7, 5, 6], 'cur_cost': 18136.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 9, 3, 1, 2, 19, 15, 16, 17, 18, 20, 21, 13, 12, 11, 10, 6, 5, 4, 8, 14], 'cur_cost': 20791.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 9, 3, 1, 2, 19, 15, 16, 6, 17, 18, 20, 21, 13, 12, 11, 10, 14, 8, 4, 5], 'cur_cost': 22034.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [18, 16, 1, 2, 19, 20, 13, 15, 9, 11, 21, 12, 17, 14, 7, 6, 5, 8, 4, 0, 3, 10], 'cur_cost': 24214.0, 'intermediate_solutions': [{'tour': [1, 5, 6, 4, 17, 15, 16, 21, 20, 19, 18, 3, 2, 13, 9, 10, 11, 12, 14, 7, 0, 8], 'cur_cost': 20977.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 5, 0, 4, 17, 15, 16, 21, 20, 19, 18, 13, 2, 3, 9, 10, 11, 12, 14, 7, 6, 8], 'cur_cost': 23781.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 5, 0, 4, 17, 15, 16, 21, 20, 19, 18, 3, 2, 13, 9, 10, 12, 14, 7, 11, 6, 8], 'cur_cost': 27444.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 4, 1, 2, 3, 19, 15, 16, 17, 18, 20, 21, 13, 9, 10, 14, 12, 8, 5, 6, 7], 'cur_cost': 18068.0, 'intermediate_solutions': [{'tour': [10, 13, 12, 18, 15, 1, 20, 21, 3, 14, 9, 11, 17, 19, 2, 16, 8, 5, 4, 6, 7, 0], 'cur_cost': 19742.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 13, 12, 18, 15, 1, 20, 2, 3, 14, 9, 11, 17, 19, 21, 16, 8, 5, 6, 4, 7, 0], 'cur_cost': 17291.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 13, 12, 15, 18, 1, 20, 2, 3, 14, 9, 11, 17, 19, 21, 16, 8, 5, 4, 6, 7, 0], 'cur_cost': 17286.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 0, 19, 16, 15, 10, 14, 17,  3,  6, 11, 18, 12,  9,  4,  5, 13,  1,
       21,  8, 20,  7,  2], dtype=int64), 'cur_cost': 34105.0, 'intermediate_solutions': [{'tour': array([20,  1, 10,  0, 18, 13,  4, 17,  2, 12,  3,  5, 11, 16, 21, 15,  6,
        7,  8,  9, 14, 19]), 'cur_cost': 35270.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 0, 20,  1, 10, 18, 13,  4, 17,  2, 12,  3,  5, 11, 16, 21, 15,  6,
        7,  8,  9, 14, 19]), 'cur_cost': 35611.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([18,  0, 20,  1, 10, 13,  4, 17,  2, 12,  3,  5, 11, 16, 21, 15,  6,
        7,  8,  9, 14, 19]), 'cur_cost': 32583.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([10,  0, 20,  1, 18, 13,  4, 17,  2, 12,  3,  5, 11, 16, 21, 15,  6,
        7,  8,  9, 14, 19]), 'cur_cost': 35645.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([10, 18,  0, 20,  1, 13,  4, 17,  2, 12,  3,  5, 11, 16, 21, 15,  6,
        7,  8,  9, 14, 19]), 'cur_cost': 35684.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([13, 11,  8, 16, 10,  9,  0,  5,  4, 18, 19, 17, 14, 12,  3,  2, 15,
        1,  6,  7, 20, 21], dtype=int64), 'cur_cost': 27604.0, 'intermediate_solutions': [{'tour': array([ 6, 13, 11,  1,  9, 14,  7,  0,  8, 18, 19,  5, 12, 17, 16,  3, 20,
        4, 21, 15,  2, 10]), 'cur_cost': 39704.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1,  6, 13, 11,  9, 14,  7,  0,  8, 18, 19,  5, 12, 17, 16,  3, 20,
        4, 21, 15,  2, 10]), 'cur_cost': 36834.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9,  1,  6, 13, 11, 14,  7,  0,  8, 18, 19,  5, 12, 17, 16,  3, 20,
        4, 21, 15,  2, 10]), 'cur_cost': 36850.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([11,  1,  6, 13,  9, 14,  7,  0,  8, 18, 19,  5, 12, 17, 16,  3, 20,
        4, 21, 15,  2, 10]), 'cur_cost': 36823.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([11,  9,  1,  6, 13, 14,  7,  0,  8, 18, 19,  5, 12, 17, 16,  3, 20,
        4, 21, 15,  2, 10]), 'cur_cost': 36790.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [18, 20, 17, 0, 9, 16, 2, 12, 13, 14, 21, 10, 7, 3, 5, 4, 6, 8, 11, 1, 15, 19], 'cur_cost': 28803.0, 'intermediate_solutions': [{'tour': [7, 20, 6, 3, 0, 19, 1, 13, 10, 16, 14, 18, 12, 17, 9, 15, 8, 5, 4, 2, 21, 11], 'cur_cost': 33974.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 20, 2, 3, 0, 19, 1, 4, 5, 8, 15, 9, 17, 12, 18, 14, 16, 10, 13, 6, 21, 11], 'cur_cost': 31851.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 20, 2, 3, 0, 19, 1, 13, 10, 16, 14, 18, 12, 17, 9, 15, 8, 5, 6, 4, 21, 11], 'cur_cost': 28288.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [1, 5, 0, 18, 7, 4, 6, 8, 21, 15, 16, 17, 19, 20, 3, 2, 13, 9, 10, 11, 12, 14], 'cur_cost': 17854.0, 'intermediate_solutions': [{'tour': [1, 6, 18, 4, 8, 16, 20, 0, 21, 7, 13, 11, 3, 14, 9, 2, 15, 5, 17, 19, 10, 12], 'cur_cost': 33835.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 18, 4, 8, 16, 20, 0, 21, 7, 13, 11, 12, 14, 3, 10, 19, 17, 5, 15, 2, 9], 'cur_cost': 33818.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 18, 4, 8, 16, 20, 0, 21, 7, 13, 6, 11, 12, 14, 9, 2, 15, 5, 17, 19, 10, 3], 'cur_cost': 31758.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [15, 20, 2, 0, 9, 18, 12, 14, 21, 17, 10, 7, 11, 8, 4, 6, 1, 5, 13, 3, 19, 16], 'cur_cost': 32601.0, 'intermediate_solutions': [{'tour': [1, 7, 5, 0, 16, 15, 17, 18, 19, 20, 21, 3, 11, 13, 9, 10, 2, 12, 14, 8, 4, 6], 'cur_cost': 22410.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 7, 5, 0, 16, 15, 17, 18, 19, 20, 21, 3, 2, 13, 9, 10, 11, 12, 14, 8, 6, 4], 'cur_cost': 17524.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 5, 0, 16, 15, 17, 18, 19, 20, 21, 3, 2, 13, 9, 10, 12, 14, 8, 4, 11, 6], 'cur_cost': 23878.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:51:57,798 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:57,798 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:57,801 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=17854.000, 多样性=0.940
2025-08-05 09:51:57,801 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 09:51:57,801 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 09:51:57,801 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:57,801 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 70, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'slight_improvement', 'improvement_rate': 0.009208489559467646, 'best_improvement': -0.032560291481117346}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.0064864864864866475}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.10249065378476135, 'recent_improvements': [0.0028272904018603706, -0.04881609121108437, -0.20215401716766235], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 9, 'new_count': 9, 'count_change': 0, 'old_best_cost': 9455.0, 'new_best_cost': 9455.0, 'quality_improvement': 0.0, 'old_diversity': 0.7512626262626263, 'new_diversity': 0.7512626262626263, 'diversity_change': 0.0}, 'suggestions': ['改进缓慢，建议调整策略平衡或增加扰动强度', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配']}
2025-08-05 09:51:57,802 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 09:51:57,802 - __main__ - INFO - composite3_22 开始进化第 3 代
2025-08-05 09:51:57,802 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 09:51:57,802 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:57,803 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=17854.000, 多样性=0.940
2025-08-05 09:51:57,803 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:57,804 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.940
2025-08-05 09:51:57,804 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:57,806 - EliteExpert - INFO - 精英解分析完成: 精英解数量=9, 多样性=0.751
2025-08-05 09:51:57,808 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 09:51:57,808 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:57,808 - LandscapeExpert - INFO - 添加精英解数据: 9个精英解
2025-08-05 09:51:57,808 - LandscapeExpert - INFO - 数据提取成功: 19个路径, 19个适应度值
2025-08-05 09:51:57,834 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.474, 适应度梯度: -2141.347, 聚类评分: 0.000, 覆盖率: 0.091, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:51:57,834 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 09:51:57,834 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:51:57,834 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite3_22
2025-08-05 09:51:57,839 - visualization.landscape_visualizer - INFO - 插值约束: 42 个点被约束到最小值 9455.00
2025-08-05 09:51:57,937 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite3_22\landscape_composite3_22_iter_73_20250805_095157.html
2025-08-05 09:51:58,015 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite3_22\dashboard_composite3_22_iter_73_20250805_095157.html
2025-08-05 09:51:58,015 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 73
2025-08-05 09:51:58,015 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 09:51:58,016 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2080秒
2025-08-05 09:51:58,016 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.47368421052631576, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -2141.347368421053, 'local_optima_density': 0.47368421052631576, 'gradient_variance': 58279234.34459833, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0907, 'fitness_entropy': 0.8588396549716725, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -2141.347)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.091)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358717.8341627, 'performance_metrics': {}}}
2025-08-05 09:51:58,016 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:58,016 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:58,017 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:58,017 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:58,017 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:51:58,017 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 09:51:58,018 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:51:58,018 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:58,018 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:58,018 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:51:58,018 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:58,019 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:58,019 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 4} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:58,019 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-05 09:51:58,019 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:58,019 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:58,020 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 32568.0
2025-08-05 09:51:58,033 - ExploitationExpert - INFO - res_population_num: 14
2025-08-05 09:51:58,034 - ExploitationExpert - INFO - res_population_costs: [9455.0, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455, 9455]
2025-08-05 09:51:58,034 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  4,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
        9, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11, 10,  9, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64)]
2025-08-05 09:51:58,038 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:58,038 - ExploitationExpert - INFO - populations: [{'tour': array([ 3, 17, 14, 15,  5, 12, 13, 21, 18,  8,  2,  6,  4, 16, 11, 10, 19,
        9,  7, 20,  1,  0], dtype=int64), 'cur_cost': 32568.0}, {'tour': [15, 16, 20, 17, 3, 0, 9, 10, 18, 12, 19, 21, 14, 1, 11, 13, 8, 4, 6, 5, 7, 2], 'cur_cost': 22941.0}, {'tour': [21, 16, 9, 17, 3, 18, 13, 10, 2, 19, 14, 20, 15, 1, 12, 11, 6, 7, 4, 5, 0, 8], 'cur_cost': 29242.0}, {'tour': [18, 16, 1, 2, 19, 20, 13, 15, 9, 11, 21, 12, 17, 14, 7, 6, 5, 8, 4, 0, 3, 10], 'cur_cost': 24214.0}, {'tour': [0, 11, 4, 1, 2, 3, 19, 15, 16, 17, 18, 20, 21, 13, 9, 10, 14, 12, 8, 5, 6, 7], 'cur_cost': 18068.0}, {'tour': [0, 19, 16, 15, 10, 14, 17, 3, 6, 11, 18, 12, 9, 4, 5, 13, 1, 21, 8, 20, 7, 2], 'cur_cost': 34105.0}, {'tour': [13, 11, 8, 16, 10, 9, 0, 5, 4, 18, 19, 17, 14, 12, 3, 2, 15, 1, 6, 7, 20, 21], 'cur_cost': 27604.0}, {'tour': [18, 20, 17, 0, 9, 16, 2, 12, 13, 14, 21, 10, 7, 3, 5, 4, 6, 8, 11, 1, 15, 19], 'cur_cost': 28803.0}, {'tour': [1, 5, 0, 18, 7, 4, 6, 8, 21, 15, 16, 17, 19, 20, 3, 2, 13, 9, 10, 11, 12, 14], 'cur_cost': 17854.0}, {'tour': [15, 20, 2, 0, 9, 18, 12, 14, 21, 17, 10, 7, 11, 8, 4, 6, 1, 5, 13, 3, 19, 16], 'cur_cost': 32601.0}]
2025-08-05 09:51:58,039 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:51:58,039 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 187, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 187, 'cache_hits': 0, 'similarity_calculations': 820, 'cache_hit_rate': 0.0, 'cache_size': 820}}
2025-08-05 09:51:58,040 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([ 3, 17, 14, 15,  5, 12, 13, 21, 18,  8,  2,  6,  4, 16, 11, 10, 19,
        9,  7, 20,  1,  0], dtype=int64), 'cur_cost': 32568.0, 'intermediate_solutions': [{'tour': array([12,  5, 21,  1, 17, 18, 13, 16, 20,  2,  9, 14, 15, 10, 11,  3,  6,
        7,  8,  4, 19,  0]), 'cur_cost': 29238.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1, 12,  5, 21, 17, 18, 13, 16, 20,  2,  9, 14, 15, 10, 11,  3,  6,
        7,  8,  4, 19,  0]), 'cur_cost': 26819.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([17,  1, 12,  5, 21, 18, 13, 16, 20,  2,  9, 14, 15, 10, 11,  3,  6,
        7,  8,  4, 19,  0]), 'cur_cost': 29296.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([21,  1, 12,  5, 17, 18, 13, 16, 20,  2,  9, 14, 15, 10, 11,  3,  6,
        7,  8,  4, 19,  0]), 'cur_cost': 29331.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([21, 17,  1, 12,  5, 18, 13, 16, 20,  2,  9, 14, 15, 10, 11,  3,  6,
        7,  8,  4, 19,  0]), 'cur_cost': 29372.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:58,040 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 32568.00)
2025-08-05 09:51:58,040 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:51:58,040 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:51:58,040 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:58,041 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 09:51:58,041 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,041 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,042 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,042 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,042 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16534.0, 路径长度: 22, 收集中间解: 3
2025-08-05 09:51:58,042 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 13, 1, 3, 8, 4, 5, 6, 7, 21, 15, 16, 17, 18, 19, 20, 2, 14, 9, 10, 11, 12], 'cur_cost': 16534.0, 'intermediate_solutions': [{'tour': [15, 16, 20, 17, 3, 0, 2, 10, 18, 12, 19, 21, 14, 1, 11, 13, 8, 4, 6, 5, 7, 9], 'cur_cost': 23667.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [15, 16, 20, 17, 3, 0, 9, 10, 18, 12, 19, 21, 14, 1, 11, 13, 8, 4, 6, 5, 2, 7], 'cur_cost': 26455.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [15, 16, 20, 21, 17, 3, 0, 9, 10, 18, 12, 19, 14, 1, 11, 13, 8, 4, 6, 5, 7, 2], 'cur_cost': 22903.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:58,043 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 16534.00)
2025-08-05 09:51:58,043 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:51:58,043 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:51:58,043 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:58,044 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 09:51:58,044 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,044 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,044 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,044 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,044 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17650.0, 路径长度: 22, 收集中间解: 3
2025-08-05 09:51:58,044 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [1, 4, 0, 18, 11, 9, 10, 14, 13, 12, 17, 15, 16, 21, 20, 19, 3, 2, 7, 6, 5, 8], 'cur_cost': 17650.0, 'intermediate_solutions': [{'tour': [21, 16, 9, 6, 3, 18, 13, 10, 2, 19, 14, 20, 15, 1, 12, 11, 17, 7, 4, 5, 0, 8], 'cur_cost': 32814.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [21, 16, 9, 17, 3, 18, 13, 10, 2, 19, 14, 20, 15, 1, 12, 11, 6, 4, 7, 5, 0, 8], 'cur_cost': 29265.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [21, 16, 0, 9, 17, 3, 18, 13, 10, 2, 19, 14, 20, 15, 1, 12, 11, 6, 7, 4, 5, 8], 'cur_cost': 25704.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:58,045 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 17650.00)
2025-08-05 09:51:58,045 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:51:58,045 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:51:58,045 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:58,045 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 22
2025-08-05 09:51:58,045 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,046 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,046 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,046 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,046 - ExplorationExpert - INFO - 探索路径生成完成，成本: 40780.0, 路径长度: 22, 收集中间解: 3
2025-08-05 09:51:58,046 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [13, 4, 2, 8, 20, 10, 12, 15, 16, 14, 19, 5, 3, 1, 9, 0, 11, 7, 21, 17, 6, 18], 'cur_cost': 40780.0, 'intermediate_solutions': [{'tour': [18, 16, 1, 2, 19, 20, 13, 15, 9, 11, 21, 12, 17, 4, 7, 6, 5, 8, 14, 0, 3, 10], 'cur_cost': 24263.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [18, 16, 1, 2, 19, 20, 13, 15, 9, 10, 3, 0, 4, 8, 5, 6, 7, 14, 17, 12, 21, 11], 'cur_cost': 24213.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [19, 18, 16, 1, 2, 20, 13, 15, 9, 11, 21, 12, 17, 14, 7, 6, 5, 8, 4, 0, 3, 10], 'cur_cost': 24277.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:58,046 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 40780.00)
2025-08-05 09:51:58,046 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:51:58,047 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:51:58,047 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:58,047 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 09:51:58,047 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,047 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,048 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,048 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,048 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14577.0, 路径长度: 22, 收集中间解: 3
2025-08-05 09:51:58,048 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 10, 20, 21, 15, 16, 17, 18, 19, 1, 2, 3, 13, 9, 11, 12, 14, 7, 4, 5, 6, 8], 'cur_cost': 14577.0, 'intermediate_solutions': [{'tour': [0, 11, 4, 1, 2, 3, 19, 15, 16, 17, 18, 9, 21, 13, 20, 10, 14, 12, 8, 5, 6, 7], 'cur_cost': 24364.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 4, 1, 2, 3, 19, 15, 16, 17, 18, 20, 21, 13, 9, 10, 14, 12, 7, 6, 5, 8], 'cur_cost': 18109.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 4, 1, 17, 2, 3, 19, 15, 16, 18, 20, 21, 13, 9, 10, 14, 12, 8, 5, 6, 7], 'cur_cost': 20581.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:58,048 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 14577.00)
2025-08-05 09:51:58,048 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 09:51:58,048 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:58,048 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:58,049 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 38899.0
2025-08-05 09:51:58,061 - ExploitationExpert - INFO - res_population_num: 18
2025-08-05 09:51:58,061 - ExploitationExpert - INFO - res_population_costs: [9455.0, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455]
2025-08-05 09:51:58,062 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  4,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
        9, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11, 10,  9, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64)]
2025-08-05 09:51:58,066 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:58,066 - ExploitationExpert - INFO - populations: [{'tour': array([ 3, 17, 14, 15,  5, 12, 13, 21, 18,  8,  2,  6,  4, 16, 11, 10, 19,
        9,  7, 20,  1,  0], dtype=int64), 'cur_cost': 32568.0}, {'tour': [0, 13, 1, 3, 8, 4, 5, 6, 7, 21, 15, 16, 17, 18, 19, 20, 2, 14, 9, 10, 11, 12], 'cur_cost': 16534.0}, {'tour': [1, 4, 0, 18, 11, 9, 10, 14, 13, 12, 17, 15, 16, 21, 20, 19, 3, 2, 7, 6, 5, 8], 'cur_cost': 17650.0}, {'tour': [13, 4, 2, 8, 20, 10, 12, 15, 16, 14, 19, 5, 3, 1, 9, 0, 11, 7, 21, 17, 6, 18], 'cur_cost': 40780.0}, {'tour': [0, 10, 20, 21, 15, 16, 17, 18, 19, 1, 2, 3, 13, 9, 11, 12, 14, 7, 4, 5, 6, 8], 'cur_cost': 14577.0}, {'tour': array([ 3, 13, 19, 14,  0,  8, 10,  9, 17, 20, 15,  2, 21, 16,  1, 12,  6,
       18,  7,  5, 11,  4], dtype=int64), 'cur_cost': 38899.0}, {'tour': [13, 11, 8, 16, 10, 9, 0, 5, 4, 18, 19, 17, 14, 12, 3, 2, 15, 1, 6, 7, 20, 21], 'cur_cost': 27604.0}, {'tour': [18, 20, 17, 0, 9, 16, 2, 12, 13, 14, 21, 10, 7, 3, 5, 4, 6, 8, 11, 1, 15, 19], 'cur_cost': 28803.0}, {'tour': [1, 5, 0, 18, 7, 4, 6, 8, 21, 15, 16, 17, 19, 20, 3, 2, 13, 9, 10, 11, 12, 14], 'cur_cost': 17854.0}, {'tour': [15, 20, 2, 0, 9, 18, 12, 14, 21, 17, 10, 7, 11, 8, 4, 6, 1, 5, 13, 3, 19, 16], 'cur_cost': 32601.0}]
2025-08-05 09:51:58,067 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:51:58,067 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 188, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 188, 'cache_hits': 0, 'similarity_calculations': 825, 'cache_hit_rate': 0.0, 'cache_size': 825}}
2025-08-05 09:51:58,068 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([ 3, 13, 19, 14,  0,  8, 10,  9, 17, 20, 15,  2, 21, 16,  1, 12,  6,
       18,  7,  5, 11,  4], dtype=int64), 'cur_cost': 38899.0, 'intermediate_solutions': [{'tour': array([16, 19,  0, 15, 10, 14, 17,  3,  6, 11, 18, 12,  9,  4,  5, 13,  1,
       21,  8, 20,  7,  2]), 'cur_cost': 36616.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([15, 16, 19,  0, 10, 14, 17,  3,  6, 11, 18, 12,  9,  4,  5, 13,  1,
       21,  8, 20,  7,  2]), 'cur_cost': 36183.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([10, 15, 16, 19,  0, 14, 17,  3,  6, 11, 18, 12,  9,  4,  5, 13,  1,
       21,  8, 20,  7,  2]), 'cur_cost': 38948.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 0, 15, 16, 19, 10, 14, 17,  3,  6, 11, 18, 12,  9,  4,  5, 13,  1,
       21,  8, 20,  7,  2]), 'cur_cost': 34166.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 0, 10, 15, 16, 19, 14, 17,  3,  6, 11, 18, 12,  9,  4,  5, 13,  1,
       21,  8, 20,  7,  2]), 'cur_cost': 36917.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:58,068 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 38899.00)
2025-08-05 09:51:58,068 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:51:58,068 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:51:58,068 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:58,069 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 09:51:58,069 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,069 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,069 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,069 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,070 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17631.0, 路径长度: 22, 收集中间解: 3
2025-08-05 09:51:58,070 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 19, 1, 7, 2, 3, 18, 15, 16, 17, 21, 20, 13, 9, 10, 11, 12, 14, 8, 4, 5, 6], 'cur_cost': 17631.0, 'intermediate_solutions': [{'tour': [13, 0, 8, 16, 10, 9, 11, 5, 4, 18, 19, 17, 14, 12, 3, 2, 15, 1, 6, 7, 20, 21], 'cur_cost': 27546.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [13, 11, 8, 16, 15, 2, 3, 12, 14, 17, 19, 18, 4, 5, 0, 9, 10, 1, 6, 7, 20, 21], 'cur_cost': 27256.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [13, 11, 8, 16, 10, 1, 9, 0, 5, 4, 18, 19, 17, 14, 12, 3, 2, 15, 6, 7, 20, 21], 'cur_cost': 30451.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:58,070 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 17631.00)
2025-08-05 09:51:58,070 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:51:58,070 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:51:58,070 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:58,071 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 22
2025-08-05 09:51:58,071 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,071 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,071 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,071 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,071 - ExplorationExpert - INFO - 探索路径生成完成，成本: 33594.0, 路径长度: 22, 收集中间解: 3
2025-08-05 09:51:58,072 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [18, 20, 3, 8, 7, 1, 19, 16, 9, 10, 0, 2, 5, 11, 6, 15, 14, 4, 12, 13, 21, 17], 'cur_cost': 33594.0, 'intermediate_solutions': [{'tour': [18, 20, 2, 0, 9, 16, 17, 12, 13, 14, 21, 10, 7, 3, 5, 4, 6, 8, 11, 1, 15, 19], 'cur_cost': 26519.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [18, 20, 17, 0, 9, 16, 2, 12, 13, 14, 21, 10, 7, 3, 6, 4, 5, 8, 11, 1, 15, 19], 'cur_cost': 28740.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [18, 20, 17, 0, 9, 2, 12, 13, 14, 21, 10, 7, 3, 5, 4, 6, 8, 11, 1, 16, 15, 19], 'cur_cost': 28463.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:58,072 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 33594.00)
2025-08-05 09:51:58,072 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:51:58,072 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:51:58,072 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:58,073 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 09:51:58,073 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,073 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,073 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,073 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,073 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14174.0, 路径长度: 22, 收集中间解: 3
2025-08-05 09:51:58,073 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 21, 1, 8, 4, 5, 6, 7, 20, 15, 16, 17, 18, 19, 3, 2, 13, 9, 10, 11, 12, 14], 'cur_cost': 14174.0, 'intermediate_solutions': [{'tour': [1, 5, 0, 18, 7, 4, 6, 8, 21, 15, 16, 17, 19, 10, 3, 2, 13, 9, 20, 11, 12, 14], 'cur_cost': 23898.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 5, 0, 18, 7, 4, 6, 8, 11, 10, 9, 13, 2, 3, 20, 19, 17, 16, 15, 21, 12, 14], 'cur_cost': 20631.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 5, 15, 0, 18, 7, 4, 6, 8, 21, 16, 17, 19, 20, 3, 2, 13, 9, 10, 11, 12, 14], 'cur_cost': 18231.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:58,074 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 14174.00)
2025-08-05 09:51:58,074 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 09:51:58,074 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:58,074 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:58,074 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 38320.0
2025-08-05 09:51:58,087 - ExploitationExpert - INFO - res_population_num: 22
2025-08-05 09:51:58,087 - ExploitationExpert - INFO - res_population_costs: [9455.0, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455]
2025-08-05 09:51:58,087 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  4,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
        9, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11, 10,  9, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64)]
2025-08-05 09:51:58,092 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:58,092 - ExploitationExpert - INFO - populations: [{'tour': array([ 3, 17, 14, 15,  5, 12, 13, 21, 18,  8,  2,  6,  4, 16, 11, 10, 19,
        9,  7, 20,  1,  0], dtype=int64), 'cur_cost': 32568.0}, {'tour': [0, 13, 1, 3, 8, 4, 5, 6, 7, 21, 15, 16, 17, 18, 19, 20, 2, 14, 9, 10, 11, 12], 'cur_cost': 16534.0}, {'tour': [1, 4, 0, 18, 11, 9, 10, 14, 13, 12, 17, 15, 16, 21, 20, 19, 3, 2, 7, 6, 5, 8], 'cur_cost': 17650.0}, {'tour': [13, 4, 2, 8, 20, 10, 12, 15, 16, 14, 19, 5, 3, 1, 9, 0, 11, 7, 21, 17, 6, 18], 'cur_cost': 40780.0}, {'tour': [0, 10, 20, 21, 15, 16, 17, 18, 19, 1, 2, 3, 13, 9, 11, 12, 14, 7, 4, 5, 6, 8], 'cur_cost': 14577.0}, {'tour': array([ 3, 13, 19, 14,  0,  8, 10,  9, 17, 20, 15,  2, 21, 16,  1, 12,  6,
       18,  7,  5, 11,  4], dtype=int64), 'cur_cost': 38899.0}, {'tour': [0, 19, 1, 7, 2, 3, 18, 15, 16, 17, 21, 20, 13, 9, 10, 11, 12, 14, 8, 4, 5, 6], 'cur_cost': 17631.0}, {'tour': [18, 20, 3, 8, 7, 1, 19, 16, 9, 10, 0, 2, 5, 11, 6, 15, 14, 4, 12, 13, 21, 17], 'cur_cost': 33594.0}, {'tour': [0, 21, 1, 8, 4, 5, 6, 7, 20, 15, 16, 17, 18, 19, 3, 2, 13, 9, 10, 11, 12, 14], 'cur_cost': 14174.0}, {'tour': array([14,  9,  1, 21, 10, 12,  3,  0, 16,  4, 15,  8,  2,  6, 18, 20,  7,
       13,  5, 19, 11, 17], dtype=int64), 'cur_cost': 38320.0}]
2025-08-05 09:51:58,093 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:51:58,093 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 189, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 189, 'cache_hits': 0, 'similarity_calculations': 831, 'cache_hit_rate': 0.0, 'cache_size': 831}}
2025-08-05 09:51:58,094 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([14,  9,  1, 21, 10, 12,  3,  0, 16,  4, 15,  8,  2,  6, 18, 20,  7,
       13,  5, 19, 11, 17], dtype=int64), 'cur_cost': 38320.0, 'intermediate_solutions': [{'tour': array([ 2, 20, 15,  0,  9, 18, 12, 14, 21, 17, 10,  7, 11,  8,  4,  6,  1,
        5, 13,  3, 19, 16]), 'cur_cost': 35112.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 0,  2, 20, 15,  9, 18, 12, 14, 21, 17, 10,  7, 11,  8,  4,  6,  1,
        5, 13,  3, 19, 16]), 'cur_cost': 33019.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9,  0,  2, 20, 15, 18, 12, 14, 21, 17, 10,  7, 11,  8,  4,  6,  1,
        5, 13,  3, 19, 16]), 'cur_cost': 32598.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([15,  0,  2, 20,  9, 18, 12, 14, 21, 17, 10,  7, 11,  8,  4,  6,  1,
        5, 13,  3, 19, 16]), 'cur_cost': 33019.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([15,  9,  0,  2, 20, 18, 12, 14, 21, 17, 10,  7, 11,  8,  4,  6,  1,
        5, 13,  3, 19, 16]), 'cur_cost': 32649.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:58,094 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 38320.00)
2025-08-05 09:51:58,094 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:58,094 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:58,096 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 3, 17, 14, 15,  5, 12, 13, 21, 18,  8,  2,  6,  4, 16, 11, 10, 19,
        9,  7, 20,  1,  0], dtype=int64), 'cur_cost': 32568.0, 'intermediate_solutions': [{'tour': array([12,  5, 21,  1, 17, 18, 13, 16, 20,  2,  9, 14, 15, 10, 11,  3,  6,
        7,  8,  4, 19,  0]), 'cur_cost': 29238.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1, 12,  5, 21, 17, 18, 13, 16, 20,  2,  9, 14, 15, 10, 11,  3,  6,
        7,  8,  4, 19,  0]), 'cur_cost': 26819.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([17,  1, 12,  5, 21, 18, 13, 16, 20,  2,  9, 14, 15, 10, 11,  3,  6,
        7,  8,  4, 19,  0]), 'cur_cost': 29296.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([21,  1, 12,  5, 17, 18, 13, 16, 20,  2,  9, 14, 15, 10, 11,  3,  6,
        7,  8,  4, 19,  0]), 'cur_cost': 29331.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([21, 17,  1, 12,  5, 18, 13, 16, 20,  2,  9, 14, 15, 10, 11,  3,  6,
        7,  8,  4, 19,  0]), 'cur_cost': 29372.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 1, 3, 8, 4, 5, 6, 7, 21, 15, 16, 17, 18, 19, 20, 2, 14, 9, 10, 11, 12], 'cur_cost': 16534.0, 'intermediate_solutions': [{'tour': [15, 16, 20, 17, 3, 0, 2, 10, 18, 12, 19, 21, 14, 1, 11, 13, 8, 4, 6, 5, 7, 9], 'cur_cost': 23667.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [15, 16, 20, 17, 3, 0, 9, 10, 18, 12, 19, 21, 14, 1, 11, 13, 8, 4, 6, 5, 2, 7], 'cur_cost': 26455.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [15, 16, 20, 21, 17, 3, 0, 9, 10, 18, 12, 19, 14, 1, 11, 13, 8, 4, 6, 5, 7, 2], 'cur_cost': 22903.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 0, 18, 11, 9, 10, 14, 13, 12, 17, 15, 16, 21, 20, 19, 3, 2, 7, 6, 5, 8], 'cur_cost': 17650.0, 'intermediate_solutions': [{'tour': [21, 16, 9, 6, 3, 18, 13, 10, 2, 19, 14, 20, 15, 1, 12, 11, 17, 7, 4, 5, 0, 8], 'cur_cost': 32814.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [21, 16, 9, 17, 3, 18, 13, 10, 2, 19, 14, 20, 15, 1, 12, 11, 6, 4, 7, 5, 0, 8], 'cur_cost': 29265.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [21, 16, 0, 9, 17, 3, 18, 13, 10, 2, 19, 14, 20, 15, 1, 12, 11, 6, 7, 4, 5, 8], 'cur_cost': 25704.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [13, 4, 2, 8, 20, 10, 12, 15, 16, 14, 19, 5, 3, 1, 9, 0, 11, 7, 21, 17, 6, 18], 'cur_cost': 40780.0, 'intermediate_solutions': [{'tour': [18, 16, 1, 2, 19, 20, 13, 15, 9, 11, 21, 12, 17, 4, 7, 6, 5, 8, 14, 0, 3, 10], 'cur_cost': 24263.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [18, 16, 1, 2, 19, 20, 13, 15, 9, 10, 3, 0, 4, 8, 5, 6, 7, 14, 17, 12, 21, 11], 'cur_cost': 24213.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [19, 18, 16, 1, 2, 20, 13, 15, 9, 11, 21, 12, 17, 14, 7, 6, 5, 8, 4, 0, 3, 10], 'cur_cost': 24277.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 20, 21, 15, 16, 17, 18, 19, 1, 2, 3, 13, 9, 11, 12, 14, 7, 4, 5, 6, 8], 'cur_cost': 14577.0, 'intermediate_solutions': [{'tour': [0, 11, 4, 1, 2, 3, 19, 15, 16, 17, 18, 9, 21, 13, 20, 10, 14, 12, 8, 5, 6, 7], 'cur_cost': 24364.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 4, 1, 2, 3, 19, 15, 16, 17, 18, 20, 21, 13, 9, 10, 14, 12, 7, 6, 5, 8], 'cur_cost': 18109.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 4, 1, 17, 2, 3, 19, 15, 16, 18, 20, 21, 13, 9, 10, 14, 12, 8, 5, 6, 7], 'cur_cost': 20581.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 3, 13, 19, 14,  0,  8, 10,  9, 17, 20, 15,  2, 21, 16,  1, 12,  6,
       18,  7,  5, 11,  4], dtype=int64), 'cur_cost': 38899.0, 'intermediate_solutions': [{'tour': array([16, 19,  0, 15, 10, 14, 17,  3,  6, 11, 18, 12,  9,  4,  5, 13,  1,
       21,  8, 20,  7,  2]), 'cur_cost': 36616.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([15, 16, 19,  0, 10, 14, 17,  3,  6, 11, 18, 12,  9,  4,  5, 13,  1,
       21,  8, 20,  7,  2]), 'cur_cost': 36183.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([10, 15, 16, 19,  0, 14, 17,  3,  6, 11, 18, 12,  9,  4,  5, 13,  1,
       21,  8, 20,  7,  2]), 'cur_cost': 38948.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 0, 15, 16, 19, 10, 14, 17,  3,  6, 11, 18, 12,  9,  4,  5, 13,  1,
       21,  8, 20,  7,  2]), 'cur_cost': 34166.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 0, 10, 15, 16, 19, 14, 17,  3,  6, 11, 18, 12,  9,  4,  5, 13,  1,
       21,  8, 20,  7,  2]), 'cur_cost': 36917.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 1, 7, 2, 3, 18, 15, 16, 17, 21, 20, 13, 9, 10, 11, 12, 14, 8, 4, 5, 6], 'cur_cost': 17631.0, 'intermediate_solutions': [{'tour': [13, 0, 8, 16, 10, 9, 11, 5, 4, 18, 19, 17, 14, 12, 3, 2, 15, 1, 6, 7, 20, 21], 'cur_cost': 27546.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [13, 11, 8, 16, 15, 2, 3, 12, 14, 17, 19, 18, 4, 5, 0, 9, 10, 1, 6, 7, 20, 21], 'cur_cost': 27256.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [13, 11, 8, 16, 10, 1, 9, 0, 5, 4, 18, 19, 17, 14, 12, 3, 2, 15, 6, 7, 20, 21], 'cur_cost': 30451.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [18, 20, 3, 8, 7, 1, 19, 16, 9, 10, 0, 2, 5, 11, 6, 15, 14, 4, 12, 13, 21, 17], 'cur_cost': 33594.0, 'intermediate_solutions': [{'tour': [18, 20, 2, 0, 9, 16, 17, 12, 13, 14, 21, 10, 7, 3, 5, 4, 6, 8, 11, 1, 15, 19], 'cur_cost': 26519.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [18, 20, 17, 0, 9, 16, 2, 12, 13, 14, 21, 10, 7, 3, 6, 4, 5, 8, 11, 1, 15, 19], 'cur_cost': 28740.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [18, 20, 17, 0, 9, 2, 12, 13, 14, 21, 10, 7, 3, 5, 4, 6, 8, 11, 1, 16, 15, 19], 'cur_cost': 28463.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 1, 8, 4, 5, 6, 7, 20, 15, 16, 17, 18, 19, 3, 2, 13, 9, 10, 11, 12, 14], 'cur_cost': 14174.0, 'intermediate_solutions': [{'tour': [1, 5, 0, 18, 7, 4, 6, 8, 21, 15, 16, 17, 19, 10, 3, 2, 13, 9, 20, 11, 12, 14], 'cur_cost': 23898.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 5, 0, 18, 7, 4, 6, 8, 11, 10, 9, 13, 2, 3, 20, 19, 17, 16, 15, 21, 12, 14], 'cur_cost': 20631.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 5, 15, 0, 18, 7, 4, 6, 8, 21, 16, 17, 19, 20, 3, 2, 13, 9, 10, 11, 12, 14], 'cur_cost': 18231.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([14,  9,  1, 21, 10, 12,  3,  0, 16,  4, 15,  8,  2,  6, 18, 20,  7,
       13,  5, 19, 11, 17], dtype=int64), 'cur_cost': 38320.0, 'intermediate_solutions': [{'tour': array([ 2, 20, 15,  0,  9, 18, 12, 14, 21, 17, 10,  7, 11,  8,  4,  6,  1,
        5, 13,  3, 19, 16]), 'cur_cost': 35112.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 0,  2, 20, 15,  9, 18, 12, 14, 21, 17, 10,  7, 11,  8,  4,  6,  1,
        5, 13,  3, 19, 16]), 'cur_cost': 33019.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9,  0,  2, 20, 15, 18, 12, 14, 21, 17, 10,  7, 11,  8,  4,  6,  1,
        5, 13,  3, 19, 16]), 'cur_cost': 32598.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([15,  0,  2, 20,  9, 18, 12, 14, 21, 17, 10,  7, 11,  8,  4,  6,  1,
        5, 13,  3, 19, 16]), 'cur_cost': 33019.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([15,  9,  0,  2, 20, 18, 12, 14, 21, 17, 10,  7, 11,  8,  4,  6,  1,
        5, 13,  3, 19, 16]), 'cur_cost': 32649.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 09:51:58,096 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:58,096 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:58,098 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=14174.000, 多样性=0.948
2025-08-05 09:51:58,098 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 09:51:58,098 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 09:51:58,099 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:58,102 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'slight_improvement', 'improvement_rate': 0.003424167143579016, 'best_improvement': 0.20611627646465777}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.008592910848549884}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.02901229038527603, 'recent_improvements': [-0.04881609121108437, -0.20215401716766235, 0.009208489559467646], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 22, 'new_count': 22, 'count_change': 0, 'old_best_cost': 9455.0, 'new_best_cost': 9455.0, 'quality_improvement': 0.0, 'old_diversity': 0.7837465564738292, 'new_diversity': 0.7837465564738292, 'diversity_change': 0.0}, 'suggestions': ['改进缓慢，建议调整策略平衡或增加扰动强度', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:51:58,105 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 09:51:58,105 - __main__ - INFO - composite3_22 开始进化第 4 代
2025-08-05 09:51:58,105 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 09:51:58,105 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:58,106 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=14174.000, 多样性=0.948
2025-08-05 09:51:58,106 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:58,107 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.948
2025-08-05 09:51:58,107 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:58,115 - EliteExpert - INFO - 精英解分析完成: 精英解数量=22, 多样性=0.784
2025-08-05 09:51:58,117 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 09:51:58,117 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:58,117 - LandscapeExpert - INFO - 添加精英解数据: 22个精英解
2025-08-05 09:51:58,117 - LandscapeExpert - INFO - 数据提取成功: 32个路径, 32个适应度值
2025-08-05 09:51:58,190 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.688, 适应度梯度: -3893.413, 聚类评分: 0.000, 覆盖率: 0.093, 收敛趋势: 0.000, 多样性: 0.596
2025-08-05 09:51:58,190 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 09:51:58,190 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:51:58,190 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite3_22
2025-08-05 09:51:58,195 - visualization.landscape_visualizer - INFO - 插值约束: 144 个点被约束到最小值 9455.00
2025-08-05 09:51:58,271 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite3_22\landscape_composite3_22_iter_74_20250805_095158.html
2025-08-05 09:51:58,313 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite3_22\dashboard_composite3_22_iter_74_20250805_095158.html
2025-08-05 09:51:58,313 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 74
2025-08-05 09:51:58,313 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 09:51:58,313 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1959秒
2025-08-05 09:51:58,313 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.6875, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -3893.4125000000004, 'local_optima_density': 0.6875, 'gradient_variance': 66333793.12734377, 'cluster_count': 0}, 'population_state': {'diversity': 0.5959551411290323, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0926, 'fitness_entropy': 0.6511658550627888, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -3893.413)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.093)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358718.190283, 'performance_metrics': {}}}
2025-08-05 09:51:58,313 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:58,314 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:58,314 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:58,314 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:58,314 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:51:58,315 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 09:51:58,315 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:51:58,315 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:58,315 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:58,315 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:51:58,315 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:58,315 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:58,316 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 4} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:58,316 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:51:58,316 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:51:58,316 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:58,317 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 09:51:58,317 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,317 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,317 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,317 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,317 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17977.0, 路径长度: 22, 收集中间解: 3
2025-08-05 09:51:58,317 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 16, 1, 4, 19, 15, 17, 18, 20, 21, 3, 2, 13, 9, 10, 11, 12, 14, 7, 6, 5, 8], 'cur_cost': 17977.0, 'intermediate_solutions': [{'tour': [3, 17, 14, 15, 5, 12, 13, 21, 18, 8, 2, 6, 4, 16, 10, 11, 19, 9, 7, 20, 1, 0], 'cur_cost': 32570.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 17, 1, 20, 7, 9, 19, 10, 11, 16, 4, 6, 2, 8, 18, 21, 13, 12, 5, 15, 14, 0], 'cur_cost': 34661.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 17, 14, 15, 5, 12, 13, 18, 8, 2, 6, 4, 16, 11, 10, 19, 9, 7, 21, 20, 1, 0], 'cur_cost': 32475.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:58,318 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 17977.00)
2025-08-05 09:51:58,318 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:51:58,318 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:51:58,318 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:58,318 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 09:51:58,318 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,319 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,319 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,319 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,319 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14944.0, 路径长度: 22, 收集中间解: 3
2025-08-05 09:51:58,319 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 19, 11, 20, 15, 16, 17, 18, 21, 1, 2, 3, 13, 9, 10, 14, 12, 8, 4, 5, 6, 7], 'cur_cost': 14944.0, 'intermediate_solutions': [{'tour': [0, 13, 1, 3, 8, 7, 5, 6, 4, 21, 15, 16, 17, 18, 19, 20, 2, 14, 9, 10, 11, 12], 'cur_cost': 16609.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 1, 3, 8, 4, 5, 6, 7, 21, 15, 16, 17, 18, 19, 20, 2, 14, 10, 9, 11, 12], 'cur_cost': 16534.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 1, 8, 4, 5, 6, 7, 21, 15, 16, 17, 3, 18, 19, 20, 2, 14, 9, 10, 11, 12], 'cur_cost': 18886.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:58,319 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 14944.00)
2025-08-05 09:51:58,319 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:51:58,319 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:51:58,319 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:58,320 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 22
2025-08-05 09:51:58,320 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,320 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,320 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,321 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,321 - ExplorationExpert - INFO - 探索路径生成完成，成本: 40757.0, 路径长度: 22, 收集中间解: 3
2025-08-05 09:51:58,321 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [1, 16, 11, 4, 19, 15, 12, 14, 21, 20, 3, 5, 18, 6, 9, 0, 13, 8, 10, 17, 7, 2], 'cur_cost': 40757.0, 'intermediate_solutions': [{'tour': [9, 4, 0, 18, 11, 1, 10, 14, 13, 12, 17, 15, 16, 21, 20, 19, 3, 2, 7, 6, 5, 8], 'cur_cost': 23244.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 0, 18, 11, 9, 10, 14, 13, 12, 17, 15, 16, 21, 20, 6, 7, 2, 3, 19, 5, 8], 'cur_cost': 21635.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 0, 18, 11, 9, 10, 14, 13, 12, 17, 15, 16, 21, 19, 3, 20, 2, 7, 6, 5, 8], 'cur_cost': 20085.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:58,321 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 40757.00)
2025-08-05 09:51:58,321 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:51:58,321 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:58,321 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:58,322 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 34230.0
2025-08-05 09:51:58,332 - ExploitationExpert - INFO - res_population_num: 22
2025-08-05 09:51:58,332 - ExploitationExpert - INFO - res_population_costs: [9455.0, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455]
2025-08-05 09:51:58,332 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  4,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
        9, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11, 10,  9, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64)]
2025-08-05 09:51:58,336 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:58,337 - ExploitationExpert - INFO - populations: [{'tour': [0, 16, 1, 4, 19, 15, 17, 18, 20, 21, 3, 2, 13, 9, 10, 11, 12, 14, 7, 6, 5, 8], 'cur_cost': 17977.0}, {'tour': [0, 19, 11, 20, 15, 16, 17, 18, 21, 1, 2, 3, 13, 9, 10, 14, 12, 8, 4, 5, 6, 7], 'cur_cost': 14944.0}, {'tour': [1, 16, 11, 4, 19, 15, 12, 14, 21, 20, 3, 5, 18, 6, 9, 0, 13, 8, 10, 17, 7, 2], 'cur_cost': 40757.0}, {'tour': array([16,  6, 11,  3, 15, 17,  4,  7,  2, 13, 14, 10,  0,  1, 18,  9, 19,
       21,  5, 20,  8, 12], dtype=int64), 'cur_cost': 34230.0}, {'tour': [0, 10, 20, 21, 15, 16, 17, 18, 19, 1, 2, 3, 13, 9, 11, 12, 14, 7, 4, 5, 6, 8], 'cur_cost': 14577.0}, {'tour': [3, 13, 19, 14, 0, 8, 10, 9, 17, 20, 15, 2, 21, 16, 1, 12, 6, 18, 7, 5, 11, 4], 'cur_cost': 38899.0}, {'tour': [0, 19, 1, 7, 2, 3, 18, 15, 16, 17, 21, 20, 13, 9, 10, 11, 12, 14, 8, 4, 5, 6], 'cur_cost': 17631.0}, {'tour': [18, 20, 3, 8, 7, 1, 19, 16, 9, 10, 0, 2, 5, 11, 6, 15, 14, 4, 12, 13, 21, 17], 'cur_cost': 33594.0}, {'tour': [0, 21, 1, 8, 4, 5, 6, 7, 20, 15, 16, 17, 18, 19, 3, 2, 13, 9, 10, 11, 12, 14], 'cur_cost': 14174.0}, {'tour': [14, 9, 1, 21, 10, 12, 3, 0, 16, 4, 15, 8, 2, 6, 18, 20, 7, 13, 5, 19, 11, 17], 'cur_cost': 38320.0}]
2025-08-05 09:51:58,337 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:51:58,337 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 190, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 190, 'cache_hits': 0, 'similarity_calculations': 838, 'cache_hit_rate': 0.0, 'cache_size': 838}}
2025-08-05 09:51:58,338 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([16,  6, 11,  3, 15, 17,  4,  7,  2, 13, 14, 10,  0,  1, 18,  9, 19,
       21,  5, 20,  8, 12], dtype=int64), 'cur_cost': 34230.0, 'intermediate_solutions': [{'tour': array([ 2,  4, 13,  8, 20, 10, 12, 15, 16, 14, 19,  5,  3,  1,  9,  0, 11,
        7, 21, 17,  6, 18]), 'cur_cost': 40776.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 8,  2,  4, 13, 20, 10, 12, 15, 16, 14, 19,  5,  3,  1,  9,  0, 11,
        7, 21, 17,  6, 18]), 'cur_cost': 40902.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([20,  8,  2,  4, 13, 10, 12, 15, 16, 14, 19,  5,  3,  1,  9,  0, 11,
        7, 21, 17,  6, 18]), 'cur_cost': 37706.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([13,  8,  2,  4, 20, 10, 12, 15, 16, 14, 19,  5,  3,  1,  9,  0, 11,
        7, 21, 17,  6, 18]), 'cur_cost': 40766.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([13, 20,  8,  2,  4, 10, 12, 15, 16, 14, 19,  5,  3,  1,  9,  0, 11,
        7, 21, 17,  6, 18]), 'cur_cost': 40780.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:58,338 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 34230.00)
2025-08-05 09:51:58,338 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:51:58,338 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:51:58,338 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:58,339 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 22
2025-08-05 09:51:58,339 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,339 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,339 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,340 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,340 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30445.0, 路径长度: 22, 收集中间解: 3
2025-08-05 09:51:58,340 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [10, 20, 2, 12, 9, 19, 21, 5, 11, 15, 6, 7, 8, 4, 13, 18, 1, 3, 17, 14, 16, 0], 'cur_cost': 30445.0, 'intermediate_solutions': [{'tour': [0, 10, 20, 21, 14, 16, 17, 18, 19, 1, 2, 3, 13, 9, 11, 12, 15, 7, 4, 5, 6, 8], 'cur_cost': 17995.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 20, 21, 15, 16, 17, 18, 19, 1, 2, 8, 6, 5, 4, 7, 14, 12, 11, 9, 13, 3], 'cur_cost': 14530.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 20, 21, 15, 16, 17, 18, 19, 1, 2, 3, 13, 9, 12, 14, 11, 7, 4, 5, 6, 8], 'cur_cost': 14610.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:58,340 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 30445.00)
2025-08-05 09:51:58,340 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 09:51:58,340 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:58,340 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:58,341 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 36155.0
2025-08-05 09:51:58,352 - ExploitationExpert - INFO - res_population_num: 23
2025-08-05 09:51:58,352 - ExploitationExpert - INFO - res_population_costs: [9455.0, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455, 9455]
2025-08-05 09:51:58,352 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  4,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
        9, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11, 10,  9, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  4,
        5,  6,  7,  1,  2], dtype=int64)]
2025-08-05 09:51:58,358 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:58,358 - ExploitationExpert - INFO - populations: [{'tour': [0, 16, 1, 4, 19, 15, 17, 18, 20, 21, 3, 2, 13, 9, 10, 11, 12, 14, 7, 6, 5, 8], 'cur_cost': 17977.0}, {'tour': [0, 19, 11, 20, 15, 16, 17, 18, 21, 1, 2, 3, 13, 9, 10, 14, 12, 8, 4, 5, 6, 7], 'cur_cost': 14944.0}, {'tour': [1, 16, 11, 4, 19, 15, 12, 14, 21, 20, 3, 5, 18, 6, 9, 0, 13, 8, 10, 17, 7, 2], 'cur_cost': 40757.0}, {'tour': array([16,  6, 11,  3, 15, 17,  4,  7,  2, 13, 14, 10,  0,  1, 18,  9, 19,
       21,  5, 20,  8, 12], dtype=int64), 'cur_cost': 34230.0}, {'tour': [10, 20, 2, 12, 9, 19, 21, 5, 11, 15, 6, 7, 8, 4, 13, 18, 1, 3, 17, 14, 16, 0], 'cur_cost': 30445.0}, {'tour': array([ 7, 15,  1,  6,  5, 13, 21,  2,  4, 12, 18, 17, 19, 11, 10,  0, 14,
        9,  3, 20,  8, 16], dtype=int64), 'cur_cost': 36155.0}, {'tour': [0, 19, 1, 7, 2, 3, 18, 15, 16, 17, 21, 20, 13, 9, 10, 11, 12, 14, 8, 4, 5, 6], 'cur_cost': 17631.0}, {'tour': [18, 20, 3, 8, 7, 1, 19, 16, 9, 10, 0, 2, 5, 11, 6, 15, 14, 4, 12, 13, 21, 17], 'cur_cost': 33594.0}, {'tour': [0, 21, 1, 8, 4, 5, 6, 7, 20, 15, 16, 17, 18, 19, 3, 2, 13, 9, 10, 11, 12, 14], 'cur_cost': 14174.0}, {'tour': [14, 9, 1, 21, 10, 12, 3, 0, 16, 4, 15, 8, 2, 6, 18, 20, 7, 13, 5, 19, 11, 17], 'cur_cost': 38320.0}]
2025-08-05 09:51:58,359 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:51:58,359 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 191, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 191, 'cache_hits': 0, 'similarity_calculations': 846, 'cache_hit_rate': 0.0, 'cache_size': 846}}
2025-08-05 09:51:58,360 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([ 7, 15,  1,  6,  5, 13, 21,  2,  4, 12, 18, 17, 19, 11, 10,  0, 14,
        9,  3, 20,  8, 16], dtype=int64), 'cur_cost': 36155.0, 'intermediate_solutions': [{'tour': array([19, 13,  3, 14,  0,  8, 10,  9, 17, 20, 15,  2, 21, 16,  1, 12,  6,
       18,  7,  5, 11,  4]), 'cur_cost': 38830.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([14, 19, 13,  3,  0,  8, 10,  9, 17, 20, 15,  2, 21, 16,  1, 12,  6,
       18,  7,  5, 11,  4]), 'cur_cost': 36831.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 0, 14, 19, 13,  3,  8, 10,  9, 17, 20, 15,  2, 21, 16,  1, 12,  6,
       18,  7,  5, 11,  4]), 'cur_cost': 38899.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 3, 14, 19, 13,  0,  8, 10,  9, 17, 20, 15,  2, 21, 16,  1, 12,  6,
       18,  7,  5, 11,  4]), 'cur_cost': 38898.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 3,  0, 14, 19, 13,  8, 10,  9, 17, 20, 15,  2, 21, 16,  1, 12,  6,
       18,  7,  5, 11,  4]), 'cur_cost': 36813.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:58,360 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 36155.00)
2025-08-05 09:51:58,361 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:51:58,361 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:51:58,361 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:58,362 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 22
2025-08-05 09:51:58,362 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,362 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,362 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,362 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,363 - ExplorationExpert - INFO - 探索路径生成完成，成本: 24741.0, 路径长度: 22, 收集中间解: 3
2025-08-05 09:51:58,363 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [6, 16, 20, 2, 1, 0, 14, 10, 18, 15, 13, 21, 19, 12, 9, 17, 7, 4, 5, 8, 3, 11], 'cur_cost': 24741.0, 'intermediate_solutions': [{'tour': [0, 19, 1, 7, 2, 3, 18, 15, 16, 17, 12, 20, 13, 9, 10, 11, 21, 14, 8, 4, 5, 6], 'cur_cost': 23840.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 1, 7, 2, 3, 18, 20, 21, 17, 16, 15, 13, 9, 10, 11, 12, 14, 8, 4, 5, 6], 'cur_cost': 17620.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 0, 19, 1, 2, 3, 18, 15, 16, 17, 21, 20, 13, 9, 10, 11, 12, 14, 8, 4, 5, 6], 'cur_cost': 12085.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:58,363 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 24741.00)
2025-08-05 09:51:58,363 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:51:58,363 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:51:58,363 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:58,364 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 09:51:58,364 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,364 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,364 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,364 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,364 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15674.0, 路径长度: 22, 收集中间解: 3
2025-08-05 09:51:58,364 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 20, 4, 13, 9, 10, 11, 12, 14, 17, 15, 16, 21, 19, 18, 1, 2, 3, 7, 6, 5, 8], 'cur_cost': 15674.0, 'intermediate_solutions': [{'tour': [0, 20, 3, 8, 7, 1, 19, 16, 9, 10, 18, 2, 5, 11, 6, 15, 14, 4, 12, 13, 21, 17], 'cur_cost': 36388.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [18, 20, 3, 8, 7, 1, 19, 15, 6, 11, 5, 2, 0, 10, 9, 16, 14, 4, 12, 13, 21, 17], 'cur_cost': 33534.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [18, 20, 3, 8, 7, 1, 19, 16, 9, 10, 0, 5, 11, 6, 15, 14, 4, 12, 13, 21, 17, 2], 'cur_cost': 36088.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:58,365 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 15674.00)
2025-08-05 09:51:58,365 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:51:58,365 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:51:58,365 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:58,365 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 09:51:58,366 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,366 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,366 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,366 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,366 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20282.0, 路径长度: 22, 收集中间解: 3
2025-08-05 09:51:58,366 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [1, 5, 0, 11, 19, 15, 16, 17, 18, 20, 21, 3, 2, 13, 9, 10, 14, 12, 8, 4, 6, 7], 'cur_cost': 20282.0, 'intermediate_solutions': [{'tour': [0, 21, 1, 8, 4, 5, 6, 7, 20, 15, 16, 3, 18, 19, 17, 2, 13, 9, 10, 11, 12, 14], 'cur_cost': 16715.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 1, 8, 19, 18, 17, 16, 15, 20, 7, 6, 5, 4, 3, 2, 13, 9, 10, 11, 12, 14], 'cur_cost': 17816.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 1, 8, 4, 5, 6, 7, 20, 15, 16, 18, 19, 3, 2, 17, 13, 9, 10, 11, 12, 14], 'cur_cost': 14538.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:58,366 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 20282.00)
2025-08-05 09:51:58,366 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 09:51:58,367 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:58,367 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:58,367 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 41424.0
2025-08-05 09:51:58,377 - ExploitationExpert - INFO - res_population_num: 27
2025-08-05 09:51:58,378 - ExploitationExpert - INFO - res_population_costs: [9455.0, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455]
2025-08-05 09:51:58,378 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  4,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
        9, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11, 10,  9, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  5,  4,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64)]
2025-08-05 09:51:58,384 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:58,384 - ExploitationExpert - INFO - populations: [{'tour': [0, 16, 1, 4, 19, 15, 17, 18, 20, 21, 3, 2, 13, 9, 10, 11, 12, 14, 7, 6, 5, 8], 'cur_cost': 17977.0}, {'tour': [0, 19, 11, 20, 15, 16, 17, 18, 21, 1, 2, 3, 13, 9, 10, 14, 12, 8, 4, 5, 6, 7], 'cur_cost': 14944.0}, {'tour': [1, 16, 11, 4, 19, 15, 12, 14, 21, 20, 3, 5, 18, 6, 9, 0, 13, 8, 10, 17, 7, 2], 'cur_cost': 40757.0}, {'tour': array([16,  6, 11,  3, 15, 17,  4,  7,  2, 13, 14, 10,  0,  1, 18,  9, 19,
       21,  5, 20,  8, 12], dtype=int64), 'cur_cost': 34230.0}, {'tour': [10, 20, 2, 12, 9, 19, 21, 5, 11, 15, 6, 7, 8, 4, 13, 18, 1, 3, 17, 14, 16, 0], 'cur_cost': 30445.0}, {'tour': array([ 7, 15,  1,  6,  5, 13, 21,  2,  4, 12, 18, 17, 19, 11, 10,  0, 14,
        9,  3, 20,  8, 16], dtype=int64), 'cur_cost': 36155.0}, {'tour': [6, 16, 20, 2, 1, 0, 14, 10, 18, 15, 13, 21, 19, 12, 9, 17, 7, 4, 5, 8, 3, 11], 'cur_cost': 24741.0}, {'tour': [0, 20, 4, 13, 9, 10, 11, 12, 14, 17, 15, 16, 21, 19, 18, 1, 2, 3, 7, 6, 5, 8], 'cur_cost': 15674.0}, {'tour': [1, 5, 0, 11, 19, 15, 16, 17, 18, 20, 21, 3, 2, 13, 9, 10, 14, 12, 8, 4, 6, 7], 'cur_cost': 20282.0}, {'tour': array([ 7, 16,  8, 13, 20, 19,  2,  9,  1, 12,  4, 10,  3, 17, 15, 18, 14,
        5,  6, 21, 11,  0], dtype=int64), 'cur_cost': 41424.0}]
2025-08-05 09:51:58,385 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:51:58,385 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 192, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 192, 'cache_hits': 0, 'similarity_calculations': 855, 'cache_hit_rate': 0.0, 'cache_size': 855}}
2025-08-05 09:51:58,386 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([ 7, 16,  8, 13, 20, 19,  2,  9,  1, 12,  4, 10,  3, 17, 15, 18, 14,
        5,  6, 21, 11,  0], dtype=int64), 'cur_cost': 41424.0, 'intermediate_solutions': [{'tour': array([ 1,  9, 14, 21, 10, 12,  3,  0, 16,  4, 15,  8,  2,  6, 18, 20,  7,
       13,  5, 19, 11, 17]), 'cur_cost': 38375.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([21,  1,  9, 14, 10, 12,  3,  0, 16,  4, 15,  8,  2,  6, 18, 20,  7,
       13,  5, 19, 11, 17]), 'cur_cost': 35228.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([10, 21,  1,  9, 14, 12,  3,  0, 16,  4, 15,  8,  2,  6, 18, 20,  7,
       13,  5, 19, 11, 17]), 'cur_cost': 38365.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([14, 21,  1,  9, 10, 12,  3,  0, 16,  4, 15,  8,  2,  6, 18, 20,  7,
       13,  5, 19, 11, 17]), 'cur_cost': 38276.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([14, 10, 21,  1,  9, 12,  3,  0, 16,  4, 15,  8,  2,  6, 18, 20,  7,
       13,  5, 19, 11, 17]), 'cur_cost': 38290.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:58,386 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 41424.00)
2025-08-05 09:51:58,386 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:58,386 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:58,388 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 1, 4, 19, 15, 17, 18, 20, 21, 3, 2, 13, 9, 10, 11, 12, 14, 7, 6, 5, 8], 'cur_cost': 17977.0, 'intermediate_solutions': [{'tour': [3, 17, 14, 15, 5, 12, 13, 21, 18, 8, 2, 6, 4, 16, 10, 11, 19, 9, 7, 20, 1, 0], 'cur_cost': 32570.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 17, 1, 20, 7, 9, 19, 10, 11, 16, 4, 6, 2, 8, 18, 21, 13, 12, 5, 15, 14, 0], 'cur_cost': 34661.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 17, 14, 15, 5, 12, 13, 18, 8, 2, 6, 4, 16, 11, 10, 19, 9, 7, 21, 20, 1, 0], 'cur_cost': 32475.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 11, 20, 15, 16, 17, 18, 21, 1, 2, 3, 13, 9, 10, 14, 12, 8, 4, 5, 6, 7], 'cur_cost': 14944.0, 'intermediate_solutions': [{'tour': [0, 13, 1, 3, 8, 7, 5, 6, 4, 21, 15, 16, 17, 18, 19, 20, 2, 14, 9, 10, 11, 12], 'cur_cost': 16609.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 1, 3, 8, 4, 5, 6, 7, 21, 15, 16, 17, 18, 19, 20, 2, 14, 10, 9, 11, 12], 'cur_cost': 16534.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 1, 8, 4, 5, 6, 7, 21, 15, 16, 17, 3, 18, 19, 20, 2, 14, 9, 10, 11, 12], 'cur_cost': 18886.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [1, 16, 11, 4, 19, 15, 12, 14, 21, 20, 3, 5, 18, 6, 9, 0, 13, 8, 10, 17, 7, 2], 'cur_cost': 40757.0, 'intermediate_solutions': [{'tour': [9, 4, 0, 18, 11, 1, 10, 14, 13, 12, 17, 15, 16, 21, 20, 19, 3, 2, 7, 6, 5, 8], 'cur_cost': 23244.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 0, 18, 11, 9, 10, 14, 13, 12, 17, 15, 16, 21, 20, 6, 7, 2, 3, 19, 5, 8], 'cur_cost': 21635.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 0, 18, 11, 9, 10, 14, 13, 12, 17, 15, 16, 21, 19, 3, 20, 2, 7, 6, 5, 8], 'cur_cost': 20085.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([16,  6, 11,  3, 15, 17,  4,  7,  2, 13, 14, 10,  0,  1, 18,  9, 19,
       21,  5, 20,  8, 12], dtype=int64), 'cur_cost': 34230.0, 'intermediate_solutions': [{'tour': array([ 2,  4, 13,  8, 20, 10, 12, 15, 16, 14, 19,  5,  3,  1,  9,  0, 11,
        7, 21, 17,  6, 18]), 'cur_cost': 40776.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 8,  2,  4, 13, 20, 10, 12, 15, 16, 14, 19,  5,  3,  1,  9,  0, 11,
        7, 21, 17,  6, 18]), 'cur_cost': 40902.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([20,  8,  2,  4, 13, 10, 12, 15, 16, 14, 19,  5,  3,  1,  9,  0, 11,
        7, 21, 17,  6, 18]), 'cur_cost': 37706.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([13,  8,  2,  4, 20, 10, 12, 15, 16, 14, 19,  5,  3,  1,  9,  0, 11,
        7, 21, 17,  6, 18]), 'cur_cost': 40766.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([13, 20,  8,  2,  4, 10, 12, 15, 16, 14, 19,  5,  3,  1,  9,  0, 11,
        7, 21, 17,  6, 18]), 'cur_cost': 40780.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [10, 20, 2, 12, 9, 19, 21, 5, 11, 15, 6, 7, 8, 4, 13, 18, 1, 3, 17, 14, 16, 0], 'cur_cost': 30445.0, 'intermediate_solutions': [{'tour': [0, 10, 20, 21, 14, 16, 17, 18, 19, 1, 2, 3, 13, 9, 11, 12, 15, 7, 4, 5, 6, 8], 'cur_cost': 17995.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 20, 21, 15, 16, 17, 18, 19, 1, 2, 8, 6, 5, 4, 7, 14, 12, 11, 9, 13, 3], 'cur_cost': 14530.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 20, 21, 15, 16, 17, 18, 19, 1, 2, 3, 13, 9, 12, 14, 11, 7, 4, 5, 6, 8], 'cur_cost': 14610.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 7, 15,  1,  6,  5, 13, 21,  2,  4, 12, 18, 17, 19, 11, 10,  0, 14,
        9,  3, 20,  8, 16], dtype=int64), 'cur_cost': 36155.0, 'intermediate_solutions': [{'tour': array([19, 13,  3, 14,  0,  8, 10,  9, 17, 20, 15,  2, 21, 16,  1, 12,  6,
       18,  7,  5, 11,  4]), 'cur_cost': 38830.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([14, 19, 13,  3,  0,  8, 10,  9, 17, 20, 15,  2, 21, 16,  1, 12,  6,
       18,  7,  5, 11,  4]), 'cur_cost': 36831.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 0, 14, 19, 13,  3,  8, 10,  9, 17, 20, 15,  2, 21, 16,  1, 12,  6,
       18,  7,  5, 11,  4]), 'cur_cost': 38899.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 3, 14, 19, 13,  0,  8, 10,  9, 17, 20, 15,  2, 21, 16,  1, 12,  6,
       18,  7,  5, 11,  4]), 'cur_cost': 38898.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 3,  0, 14, 19, 13,  8, 10,  9, 17, 20, 15,  2, 21, 16,  1, 12,  6,
       18,  7,  5, 11,  4]), 'cur_cost': 36813.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [6, 16, 20, 2, 1, 0, 14, 10, 18, 15, 13, 21, 19, 12, 9, 17, 7, 4, 5, 8, 3, 11], 'cur_cost': 24741.0, 'intermediate_solutions': [{'tour': [0, 19, 1, 7, 2, 3, 18, 15, 16, 17, 12, 20, 13, 9, 10, 11, 21, 14, 8, 4, 5, 6], 'cur_cost': 23840.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 1, 7, 2, 3, 18, 20, 21, 17, 16, 15, 13, 9, 10, 11, 12, 14, 8, 4, 5, 6], 'cur_cost': 17620.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 0, 19, 1, 2, 3, 18, 15, 16, 17, 21, 20, 13, 9, 10, 11, 12, 14, 8, 4, 5, 6], 'cur_cost': 12085.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 4, 13, 9, 10, 11, 12, 14, 17, 15, 16, 21, 19, 18, 1, 2, 3, 7, 6, 5, 8], 'cur_cost': 15674.0, 'intermediate_solutions': [{'tour': [0, 20, 3, 8, 7, 1, 19, 16, 9, 10, 18, 2, 5, 11, 6, 15, 14, 4, 12, 13, 21, 17], 'cur_cost': 36388.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [18, 20, 3, 8, 7, 1, 19, 15, 6, 11, 5, 2, 0, 10, 9, 16, 14, 4, 12, 13, 21, 17], 'cur_cost': 33534.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [18, 20, 3, 8, 7, 1, 19, 16, 9, 10, 0, 5, 11, 6, 15, 14, 4, 12, 13, 21, 17, 2], 'cur_cost': 36088.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [1, 5, 0, 11, 19, 15, 16, 17, 18, 20, 21, 3, 2, 13, 9, 10, 14, 12, 8, 4, 6, 7], 'cur_cost': 20282.0, 'intermediate_solutions': [{'tour': [0, 21, 1, 8, 4, 5, 6, 7, 20, 15, 16, 3, 18, 19, 17, 2, 13, 9, 10, 11, 12, 14], 'cur_cost': 16715.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 1, 8, 19, 18, 17, 16, 15, 20, 7, 6, 5, 4, 3, 2, 13, 9, 10, 11, 12, 14], 'cur_cost': 17816.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 1, 8, 4, 5, 6, 7, 20, 15, 16, 18, 19, 3, 2, 17, 13, 9, 10, 11, 12, 14], 'cur_cost': 14538.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 7, 16,  8, 13, 20, 19,  2,  9,  1, 12,  4, 10,  3, 17, 15, 18, 14,
        5,  6, 21, 11,  0], dtype=int64), 'cur_cost': 41424.0, 'intermediate_solutions': [{'tour': array([ 1,  9, 14, 21, 10, 12,  3,  0, 16,  4, 15,  8,  2,  6, 18, 20,  7,
       13,  5, 19, 11, 17]), 'cur_cost': 38375.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([21,  1,  9, 14, 10, 12,  3,  0, 16,  4, 15,  8,  2,  6, 18, 20,  7,
       13,  5, 19, 11, 17]), 'cur_cost': 35228.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([10, 21,  1,  9, 14, 12,  3,  0, 16,  4, 15,  8,  2,  6, 18, 20,  7,
       13,  5, 19, 11, 17]), 'cur_cost': 38365.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([14, 21,  1,  9, 10, 12,  3,  0, 16,  4, 15,  8,  2,  6, 18, 20,  7,
       13,  5, 19, 11, 17]), 'cur_cost': 38276.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([14, 10, 21,  1,  9, 12,  3,  0, 16,  4, 15,  8,  2,  6, 18, 20,  7,
       13,  5, 19, 11, 17]), 'cur_cost': 38290.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 09:51:58,388 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:58,388 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:58,390 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=14944.000, 多样性=0.933
2025-08-05 09:51:58,390 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 09:51:58,391 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 09:51:58,391 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:58,395 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.038358799787394766, 'best_improvement': -0.054324820093128265}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.015974440894568676}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.1027890921556207, 'recent_improvements': [-0.20215401716766235, 0.009208489559467646, 0.003424167143579016], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 27, 'new_count': 27, 'count_change': 0, 'old_best_cost': 9455.0, 'new_best_cost': 9455.0, 'quality_improvement': 0.0, 'old_diversity': 0.7807562807562808, 'new_diversity': 0.7807562807562808, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:51:58,399 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 09:51:58,399 - __main__ - INFO - composite3_22 开始进化第 5 代
2025-08-05 09:51:58,399 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 09:51:58,400 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:58,401 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=14944.000, 多样性=0.933
2025-08-05 09:51:58,401 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:58,402 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.933
2025-08-05 09:51:58,402 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:58,410 - EliteExpert - INFO - 精英解分析完成: 精英解数量=27, 多样性=0.781
2025-08-05 09:51:58,412 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 09:51:58,412 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:58,412 - LandscapeExpert - INFO - 添加精英解数据: 27个精英解
2025-08-05 09:51:58,412 - LandscapeExpert - INFO - 数据提取成功: 37个路径, 37个适应度值
2025-08-05 09:51:58,492 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.730, 适应度梯度: -2948.049, 聚类评分: 0.000, 覆盖率: 0.094, 收敛趋势: 0.000, 多样性: 0.512
2025-08-05 09:51:58,493 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 09:51:58,493 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:51:58,493 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite3_22
2025-08-05 09:51:58,499 - visualization.landscape_visualizer - INFO - 插值约束: 110 个点被约束到最小值 9455.00
2025-08-05 09:51:58,616 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite3_22\landscape_composite3_22_iter_75_20250805_095158.html
2025-08-05 09:51:58,668 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite3_22\dashboard_composite3_22_iter_75_20250805_095158.html
2025-08-05 09:51:58,668 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 75
2025-08-05 09:51:58,669 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 09:51:58,669 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2569秒
2025-08-05 09:51:58,669 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.7297297297297297, 'modality': 'multi-modal', 'deceptiveness': 'high', 'gradient_strength': -2948.0486486486493, 'local_optima_density': 0.7297297297297297, 'gradient_variance': 53944288.50033601, 'cluster_count': 0}, 'population_state': {'diversity': 0.5124989854719584, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0937, 'fitness_entropy': 0.5147006585106794, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'high_local_optima', 'severity': 'high', 'description': '高局部最优密度区域 (密度: 0.730)', 'recommendation': '增加扰动强度'}, {'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -2948.049)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.094)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358718.4938316, 'performance_metrics': {}}}
2025-08-05 09:51:58,670 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:58,670 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:58,670 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:58,670 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:58,670 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:51:58,671 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 09:51:58,671 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:51:58,671 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:58,671 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:58,671 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:51:58,671 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:58,672 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:58,672 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:58,672 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:51:58,672 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:51:58,672 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:58,674 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 22
2025-08-05 09:51:58,674 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,675 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,675 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,675 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,675 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20157.0, 路径长度: 22, 收集中间解: 3
2025-08-05 09:51:58,675 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [5, 7, 4, 21, 19, 15, 20, 2, 18, 16, 12, 10, 17, 13, 9, 14, 3, 1, 11, 0, 6, 8], 'cur_cost': 20157.0, 'intermediate_solutions': [{'tour': [0, 16, 1, 4, 19, 15, 2, 18, 20, 21, 3, 17, 13, 9, 10, 11, 12, 14, 7, 6, 5, 8], 'cur_cost': 20726.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 16, 1, 4, 19, 15, 17, 18, 20, 21, 3, 2, 13, 9, 10, 11, 14, 12, 7, 6, 5, 8], 'cur_cost': 17963.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 16, 1, 4, 19, 2, 15, 17, 18, 20, 21, 3, 13, 9, 10, 11, 12, 14, 7, 6, 5, 8], 'cur_cost': 20333.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:58,676 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 20157.00)
2025-08-05 09:51:58,676 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:51:58,676 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:51:58,676 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:58,677 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 22
2025-08-05 09:51:58,677 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,678 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,678 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,678 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,678 - ExplorationExpert - INFO - 探索路径生成完成，成本: 23014.0, 路径长度: 22, 收集中间解: 3
2025-08-05 09:51:58,678 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [18, 16, 20, 0, 21, 17, 15, 2, 3, 12, 19, 11, 1, 10, 9, 14, 8, 7, 6, 4, 5, 13], 'cur_cost': 23014.0, 'intermediate_solutions': [{'tour': [0, 19, 11, 20, 15, 16, 17, 18, 21, 1, 7, 3, 13, 9, 10, 14, 12, 8, 4, 5, 6, 2], 'cur_cost': 20478.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 11, 20, 15, 16, 17, 18, 21, 10, 9, 13, 3, 2, 1, 14, 12, 8, 4, 5, 6, 7], 'cur_cost': 17711.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 11, 20, 15, 17, 18, 21, 16, 1, 2, 3, 13, 9, 10, 14, 12, 8, 4, 5, 6, 7], 'cur_cost': 14961.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:58,679 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 23014.00)
2025-08-05 09:51:58,679 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-05 09:51:58,679 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:58,679 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:58,680 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 45809.0
2025-08-05 09:51:58,694 - ExploitationExpert - INFO - res_population_num: 31
2025-08-05 09:51:58,694 - ExploitationExpert - INFO - res_population_costs: [9455.0, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455]
2025-08-05 09:51:58,694 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  4,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
        9, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11, 10,  9, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  5,  4,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10,  9, 11, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64)]
2025-08-05 09:51:58,702 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:58,703 - ExploitationExpert - INFO - populations: [{'tour': [5, 7, 4, 21, 19, 15, 20, 2, 18, 16, 12, 10, 17, 13, 9, 14, 3, 1, 11, 0, 6, 8], 'cur_cost': 20157.0}, {'tour': [18, 16, 20, 0, 21, 17, 15, 2, 3, 12, 19, 11, 1, 10, 9, 14, 8, 7, 6, 4, 5, 13], 'cur_cost': 23014.0}, {'tour': array([ 8, 18,  7, 21, 16, 10, 20,  0, 15,  4,  3, 12,  5,  9, 19, 11,  2,
        6, 13, 17, 14,  1], dtype=int64), 'cur_cost': 45809.0}, {'tour': [16, 6, 11, 3, 15, 17, 4, 7, 2, 13, 14, 10, 0, 1, 18, 9, 19, 21, 5, 20, 8, 12], 'cur_cost': 34230.0}, {'tour': [10, 20, 2, 12, 9, 19, 21, 5, 11, 15, 6, 7, 8, 4, 13, 18, 1, 3, 17, 14, 16, 0], 'cur_cost': 30445.0}, {'tour': [7, 15, 1, 6, 5, 13, 21, 2, 4, 12, 18, 17, 19, 11, 10, 0, 14, 9, 3, 20, 8, 16], 'cur_cost': 36155.0}, {'tour': [6, 16, 20, 2, 1, 0, 14, 10, 18, 15, 13, 21, 19, 12, 9, 17, 7, 4, 5, 8, 3, 11], 'cur_cost': 24741.0}, {'tour': [0, 20, 4, 13, 9, 10, 11, 12, 14, 17, 15, 16, 21, 19, 18, 1, 2, 3, 7, 6, 5, 8], 'cur_cost': 15674.0}, {'tour': [1, 5, 0, 11, 19, 15, 16, 17, 18, 20, 21, 3, 2, 13, 9, 10, 14, 12, 8, 4, 6, 7], 'cur_cost': 20282.0}, {'tour': [7, 16, 8, 13, 20, 19, 2, 9, 1, 12, 4, 10, 3, 17, 15, 18, 14, 5, 6, 21, 11, 0], 'cur_cost': 41424.0}]
2025-08-05 09:51:58,703 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:51:58,703 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 193, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 193, 'cache_hits': 0, 'similarity_calculations': 865, 'cache_hit_rate': 0.0, 'cache_size': 865}}
2025-08-05 09:51:58,704 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([ 8, 18,  7, 21, 16, 10, 20,  0, 15,  4,  3, 12,  5,  9, 19, 11,  2,
        6, 13, 17, 14,  1], dtype=int64), 'cur_cost': 45809.0, 'intermediate_solutions': [{'tour': array([11, 16,  1,  4, 19, 15, 12, 14, 21, 20,  3,  5, 18,  6,  9,  0, 13,
        8, 10, 17,  7,  2]), 'cur_cost': 42859.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 4, 11, 16,  1, 19, 15, 12, 14, 21, 20,  3,  5, 18,  6,  9,  0, 13,
        8, 10, 17,  7,  2]), 'cur_cost': 42742.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([19,  4, 11, 16,  1, 15, 12, 14, 21, 20,  3,  5, 18,  6,  9,  0, 13,
        8, 10, 17,  7,  2]), 'cur_cost': 43165.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 1,  4, 11, 16, 19, 15, 12, 14, 21, 20,  3,  5, 18,  6,  9,  0, 13,
        8, 10, 17,  7,  2]), 'cur_cost': 40344.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 1, 19,  4, 11, 16, 15, 12, 14, 21, 20,  3,  5, 18,  6,  9,  0, 13,
        8, 10, 17,  7,  2]), 'cur_cost': 40677.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:58,704 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 45809.00)
2025-08-05 09:51:58,704 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:51:58,704 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:51:58,704 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:58,705 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 09:51:58,705 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,705 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,706 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,706 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,706 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17964.0, 路径长度: 22, 收集中间解: 3
2025-08-05 09:51:58,706 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 7, 17, 15, 16, 21, 20, 19, 18, 1, 2, 3, 13, 9, 10, 11, 14, 8, 4, 5, 6], 'cur_cost': 17964.0, 'intermediate_solutions': [{'tour': [16, 6, 11, 3, 15, 12, 4, 7, 2, 13, 14, 10, 0, 1, 18, 9, 19, 21, 5, 20, 8, 17], 'cur_cost': 34265.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [16, 6, 11, 3, 15, 17, 4, 7, 2, 13, 14, 10, 19, 9, 18, 1, 0, 21, 5, 20, 8, 12], 'cur_cost': 34639.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [16, 6, 7, 11, 3, 15, 17, 4, 2, 13, 14, 10, 0, 1, 18, 9, 19, 21, 5, 20, 8, 12], 'cur_cost': 34230.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:58,706 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 17964.00)
2025-08-05 09:51:58,706 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:51:58,707 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:51:58,707 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:58,707 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 09:51:58,708 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,708 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,708 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,708 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,708 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15320.0, 路径长度: 22, 收集中间解: 3
2025-08-05 09:51:58,709 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 1, 6, 2, 3, 19, 15, 16, 17, 18, 20, 21, 13, 9, 10, 11, 12, 14, 7, 4, 5, 8], 'cur_cost': 15320.0, 'intermediate_solutions': [{'tour': [10, 20, 2, 12, 9, 19, 21, 5, 11, 15, 6, 7, 8, 4, 13, 18, 1, 0, 17, 14, 16, 3], 'cur_cost': 30388.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 20, 2, 12, 9, 19, 21, 5, 11, 15, 13, 4, 8, 7, 6, 18, 1, 3, 17, 14, 16, 0], 'cur_cost': 30503.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 20, 2, 12, 9, 19, 21, 5, 11, 15, 6, 7, 8, 13, 18, 1, 3, 17, 4, 14, 16, 0], 'cur_cost': 34076.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:58,709 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 15320.00)
2025-08-05 09:51:58,709 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 09:51:58,709 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:58,709 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:58,710 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 42238.0
2025-08-05 09:51:58,725 - ExploitationExpert - INFO - res_population_num: 33
2025-08-05 09:51:58,725 - ExploitationExpert - INFO - res_population_costs: [9455.0, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455, 9455.0, 9455]
2025-08-05 09:51:58,725 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  4,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
        9, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11, 10,  9, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  5,  4,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10,  9, 11, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64)]
2025-08-05 09:51:58,736 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:58,736 - ExploitationExpert - INFO - populations: [{'tour': [5, 7, 4, 21, 19, 15, 20, 2, 18, 16, 12, 10, 17, 13, 9, 14, 3, 1, 11, 0, 6, 8], 'cur_cost': 20157.0}, {'tour': [18, 16, 20, 0, 21, 17, 15, 2, 3, 12, 19, 11, 1, 10, 9, 14, 8, 7, 6, 4, 5, 13], 'cur_cost': 23014.0}, {'tour': array([ 8, 18,  7, 21, 16, 10, 20,  0, 15,  4,  3, 12,  5,  9, 19, 11,  2,
        6, 13, 17, 14,  1], dtype=int64), 'cur_cost': 45809.0}, {'tour': [0, 12, 7, 17, 15, 16, 21, 20, 19, 18, 1, 2, 3, 13, 9, 10, 11, 14, 8, 4, 5, 6], 'cur_cost': 17964.0}, {'tour': [0, 1, 6, 2, 3, 19, 15, 16, 17, 18, 20, 21, 13, 9, 10, 11, 12, 14, 7, 4, 5, 8], 'cur_cost': 15320.0}, {'tour': array([ 9,  1,  4, 16,  2, 18,  3, 11,  8, 21,  0,  5,  7, 12, 15, 19, 13,
       20, 14, 17, 10,  6], dtype=int64), 'cur_cost': 42238.0}, {'tour': [6, 16, 20, 2, 1, 0, 14, 10, 18, 15, 13, 21, 19, 12, 9, 17, 7, 4, 5, 8, 3, 11], 'cur_cost': 24741.0}, {'tour': [0, 20, 4, 13, 9, 10, 11, 12, 14, 17, 15, 16, 21, 19, 18, 1, 2, 3, 7, 6, 5, 8], 'cur_cost': 15674.0}, {'tour': [1, 5, 0, 11, 19, 15, 16, 17, 18, 20, 21, 3, 2, 13, 9, 10, 14, 12, 8, 4, 6, 7], 'cur_cost': 20282.0}, {'tour': [7, 16, 8, 13, 20, 19, 2, 9, 1, 12, 4, 10, 3, 17, 15, 18, 14, 5, 6, 21, 11, 0], 'cur_cost': 41424.0}]
2025-08-05 09:51:58,737 - ExploitationExpert - INFO - 局部搜索耗时: 0.03秒，最大迭代次数: 10
2025-08-05 09:51:58,737 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 194, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 194, 'cache_hits': 0, 'similarity_calculations': 876, 'cache_hit_rate': 0.0, 'cache_size': 876}}
2025-08-05 09:51:58,738 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([ 9,  1,  4, 16,  2, 18,  3, 11,  8, 21,  0,  5,  7, 12, 15, 19, 13,
       20, 14, 17, 10,  6], dtype=int64), 'cur_cost': 42238.0, 'intermediate_solutions': [{'tour': array([ 1, 15,  7,  6,  5, 13, 21,  2,  4, 12, 18, 17, 19, 11, 10,  0, 14,
        9,  3, 20,  8, 16]), 'cur_cost': 32703.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 6,  1, 15,  7,  5, 13, 21,  2,  4, 12, 18, 17, 19, 11, 10,  0, 14,
        9,  3, 20,  8, 16]), 'cur_cost': 36230.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 5,  6,  1, 15,  7, 13, 21,  2,  4, 12, 18, 17, 19, 11, 10,  0, 14,
        9,  3, 20,  8, 16]), 'cur_cost': 36173.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 7,  6,  1, 15,  5, 13, 21,  2,  4, 12, 18, 17, 19, 11, 10,  0, 14,
        9,  3, 20,  8, 16]), 'cur_cost': 36228.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 7,  5,  6,  1, 15, 13, 21,  2,  4, 12, 18, 17, 19, 11, 10,  0, 14,
        9,  3, 20,  8, 16]), 'cur_cost': 32623.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:58,738 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 42238.00)
2025-08-05 09:51:58,738 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:51:58,738 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:51:58,738 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:58,739 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 22
2025-08-05 09:51:58,739 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,739 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,740 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,740 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,740 - ExplorationExpert - INFO - 探索路径生成完成，成本: 35393.0, 路径长度: 22, 收集中间解: 3
2025-08-05 09:51:58,740 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [18, 7, 6, 0, 21, 17, 14, 20, 3, 16, 19, 11, 1, 10, 4, 8, 12, 5, 2, 9, 13, 15], 'cur_cost': 35393.0, 'intermediate_solutions': [{'tour': [6, 16, 20, 2, 19, 0, 14, 10, 18, 15, 13, 21, 1, 12, 9, 17, 7, 4, 5, 8, 3, 11], 'cur_cost': 29174.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 16, 20, 2, 1, 0, 15, 18, 10, 14, 13, 21, 19, 12, 9, 17, 7, 4, 5, 8, 3, 11], 'cur_cost': 22070.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 16, 20, 2, 1, 0, 14, 10, 18, 4, 15, 13, 21, 19, 12, 9, 17, 7, 5, 8, 3, 11], 'cur_cost': 28776.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:58,740 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 35393.00)
2025-08-05 09:51:58,740 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:51:58,740 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:51:58,740 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:58,741 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 09:51:58,741 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,741 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,741 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,742 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,742 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17391.0, 路径长度: 22, 收集中间解: 3
2025-08-05 09:51:58,742 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [1, 6, 0, 17, 15, 16, 21, 20, 19, 18, 3, 2, 13, 9, 10, 11, 12, 14, 7, 4, 5, 8], 'cur_cost': 17391.0, 'intermediate_solutions': [{'tour': [21, 20, 4, 13, 9, 10, 11, 12, 14, 17, 15, 16, 0, 19, 18, 1, 2, 3, 7, 6, 5, 8], 'cur_cost': 15986.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 4, 13, 9, 6, 7, 3, 2, 1, 18, 19, 21, 16, 15, 17, 14, 12, 11, 10, 5, 8], 'cur_cost': 22067.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 3, 4, 13, 9, 10, 11, 12, 14, 17, 15, 16, 21, 19, 18, 1, 2, 7, 6, 5, 8], 'cur_cost': 17714.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:58,742 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 17391.00)
2025-08-05 09:51:58,742 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:51:58,742 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:51:58,742 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:58,743 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 09:51:58,743 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,743 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,743 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,744 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:58,744 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14432.0, 路径长度: 22, 收集中间解: 3
2025-08-05 09:51:58,744 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 14, 1, 3, 2, 19, 15, 16, 17, 18, 20, 21, 13, 9, 10, 11, 12, 8, 4, 5, 6, 7], 'cur_cost': 14432.0, 'intermediate_solutions': [{'tour': [1, 5, 0, 11, 6, 15, 16, 17, 18, 20, 21, 3, 2, 13, 9, 10, 14, 12, 8, 4, 19, 7], 'cur_cost': 27794.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 5, 0, 11, 19, 15, 16, 17, 18, 20, 21, 3, 2, 13, 9, 10, 14, 12, 8, 7, 6, 4], 'cur_cost': 20337.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 1, 5, 0, 19, 15, 16, 17, 18, 20, 21, 3, 2, 13, 9, 10, 14, 12, 8, 4, 6, 7], 'cur_cost': 20248.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:58,744 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 14432.00)
2025-08-05 09:51:58,745 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 09:51:58,745 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:58,745 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:58,745 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 33069.0
2025-08-05 09:51:58,761 - ExploitationExpert - INFO - res_population_num: 36
2025-08-05 09:51:58,761 - ExploitationExpert - INFO - res_population_costs: [9455.0, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455, 9455.0, 9455, 9455.0, 9455, 9455]
2025-08-05 09:51:58,762 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  4,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
        9, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11, 10,  9, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  5,  4,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10,  9, 11, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64)]
2025-08-05 09:51:58,771 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:58,771 - ExploitationExpert - INFO - populations: [{'tour': [5, 7, 4, 21, 19, 15, 20, 2, 18, 16, 12, 10, 17, 13, 9, 14, 3, 1, 11, 0, 6, 8], 'cur_cost': 20157.0}, {'tour': [18, 16, 20, 0, 21, 17, 15, 2, 3, 12, 19, 11, 1, 10, 9, 14, 8, 7, 6, 4, 5, 13], 'cur_cost': 23014.0}, {'tour': array([ 8, 18,  7, 21, 16, 10, 20,  0, 15,  4,  3, 12,  5,  9, 19, 11,  2,
        6, 13, 17, 14,  1], dtype=int64), 'cur_cost': 45809.0}, {'tour': [0, 12, 7, 17, 15, 16, 21, 20, 19, 18, 1, 2, 3, 13, 9, 10, 11, 14, 8, 4, 5, 6], 'cur_cost': 17964.0}, {'tour': [0, 1, 6, 2, 3, 19, 15, 16, 17, 18, 20, 21, 13, 9, 10, 11, 12, 14, 7, 4, 5, 8], 'cur_cost': 15320.0}, {'tour': array([ 9,  1,  4, 16,  2, 18,  3, 11,  8, 21,  0,  5,  7, 12, 15, 19, 13,
       20, 14, 17, 10,  6], dtype=int64), 'cur_cost': 42238.0}, {'tour': [18, 7, 6, 0, 21, 17, 14, 20, 3, 16, 19, 11, 1, 10, 4, 8, 12, 5, 2, 9, 13, 15], 'cur_cost': 35393.0}, {'tour': [1, 6, 0, 17, 15, 16, 21, 20, 19, 18, 3, 2, 13, 9, 10, 11, 12, 14, 7, 4, 5, 8], 'cur_cost': 17391.0}, {'tour': [0, 14, 1, 3, 2, 19, 15, 16, 17, 18, 20, 21, 13, 9, 10, 11, 12, 8, 4, 5, 6, 7], 'cur_cost': 14432.0}, {'tour': array([ 2, 18, 13,  0,  6, 15, 19, 20,  4,  8,  7,  9,  5, 17, 14,  3,  1,
       10, 11, 16, 21, 12], dtype=int64), 'cur_cost': 33069.0}]
2025-08-05 09:51:58,772 - ExploitationExpert - INFO - 局部搜索耗时: 0.03秒，最大迭代次数: 10
2025-08-05 09:51:58,772 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 195, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 195, 'cache_hits': 0, 'similarity_calculations': 888, 'cache_hit_rate': 0.0, 'cache_size': 888}}
2025-08-05 09:51:58,773 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([ 2, 18, 13,  0,  6, 15, 19, 20,  4,  8,  7,  9,  5, 17, 14,  3,  1,
       10, 11, 16, 21, 12], dtype=int64), 'cur_cost': 33069.0, 'intermediate_solutions': [{'tour': array([ 8, 16,  7, 13, 20, 19,  2,  9,  1, 12,  4, 10,  3, 17, 15, 18, 14,
        5,  6, 21, 11,  0]), 'cur_cost': 41465.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([13,  8, 16,  7, 20, 19,  2,  9,  1, 12,  4, 10,  3, 17, 15, 18, 14,
        5,  6, 21, 11,  0]), 'cur_cost': 41366.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([20, 13,  8, 16,  7, 19,  2,  9,  1, 12,  4, 10,  3, 17, 15, 18, 14,
        5,  6, 21, 11,  0]), 'cur_cost': 41807.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 7, 13,  8, 16, 20, 19,  2,  9,  1, 12,  4, 10,  3, 17, 15, 18, 14,
        5,  6, 21, 11,  0]), 'cur_cost': 41060.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 7, 20, 13,  8, 16, 19,  2,  9,  1, 12,  4, 10,  3, 17, 15, 18, 14,
        5,  6, 21, 11,  0]), 'cur_cost': 41455.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:58,773 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 33069.00)
2025-08-05 09:51:58,773 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:58,774 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:58,775 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [5, 7, 4, 21, 19, 15, 20, 2, 18, 16, 12, 10, 17, 13, 9, 14, 3, 1, 11, 0, 6, 8], 'cur_cost': 20157.0, 'intermediate_solutions': [{'tour': [0, 16, 1, 4, 19, 15, 2, 18, 20, 21, 3, 17, 13, 9, 10, 11, 12, 14, 7, 6, 5, 8], 'cur_cost': 20726.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 16, 1, 4, 19, 15, 17, 18, 20, 21, 3, 2, 13, 9, 10, 11, 14, 12, 7, 6, 5, 8], 'cur_cost': 17963.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 16, 1, 4, 19, 2, 15, 17, 18, 20, 21, 3, 13, 9, 10, 11, 12, 14, 7, 6, 5, 8], 'cur_cost': 20333.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [18, 16, 20, 0, 21, 17, 15, 2, 3, 12, 19, 11, 1, 10, 9, 14, 8, 7, 6, 4, 5, 13], 'cur_cost': 23014.0, 'intermediate_solutions': [{'tour': [0, 19, 11, 20, 15, 16, 17, 18, 21, 1, 7, 3, 13, 9, 10, 14, 12, 8, 4, 5, 6, 2], 'cur_cost': 20478.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 11, 20, 15, 16, 17, 18, 21, 10, 9, 13, 3, 2, 1, 14, 12, 8, 4, 5, 6, 7], 'cur_cost': 17711.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 11, 20, 15, 17, 18, 21, 16, 1, 2, 3, 13, 9, 10, 14, 12, 8, 4, 5, 6, 7], 'cur_cost': 14961.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 8, 18,  7, 21, 16, 10, 20,  0, 15,  4,  3, 12,  5,  9, 19, 11,  2,
        6, 13, 17, 14,  1], dtype=int64), 'cur_cost': 45809.0, 'intermediate_solutions': [{'tour': array([11, 16,  1,  4, 19, 15, 12, 14, 21, 20,  3,  5, 18,  6,  9,  0, 13,
        8, 10, 17,  7,  2]), 'cur_cost': 42859.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 4, 11, 16,  1, 19, 15, 12, 14, 21, 20,  3,  5, 18,  6,  9,  0, 13,
        8, 10, 17,  7,  2]), 'cur_cost': 42742.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([19,  4, 11, 16,  1, 15, 12, 14, 21, 20,  3,  5, 18,  6,  9,  0, 13,
        8, 10, 17,  7,  2]), 'cur_cost': 43165.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 1,  4, 11, 16, 19, 15, 12, 14, 21, 20,  3,  5, 18,  6,  9,  0, 13,
        8, 10, 17,  7,  2]), 'cur_cost': 40344.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 1, 19,  4, 11, 16, 15, 12, 14, 21, 20,  3,  5, 18,  6,  9,  0, 13,
        8, 10, 17,  7,  2]), 'cur_cost': 40677.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 7, 17, 15, 16, 21, 20, 19, 18, 1, 2, 3, 13, 9, 10, 11, 14, 8, 4, 5, 6], 'cur_cost': 17964.0, 'intermediate_solutions': [{'tour': [16, 6, 11, 3, 15, 12, 4, 7, 2, 13, 14, 10, 0, 1, 18, 9, 19, 21, 5, 20, 8, 17], 'cur_cost': 34265.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [16, 6, 11, 3, 15, 17, 4, 7, 2, 13, 14, 10, 19, 9, 18, 1, 0, 21, 5, 20, 8, 12], 'cur_cost': 34639.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [16, 6, 7, 11, 3, 15, 17, 4, 2, 13, 14, 10, 0, 1, 18, 9, 19, 21, 5, 20, 8, 12], 'cur_cost': 34230.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 6, 2, 3, 19, 15, 16, 17, 18, 20, 21, 13, 9, 10, 11, 12, 14, 7, 4, 5, 8], 'cur_cost': 15320.0, 'intermediate_solutions': [{'tour': [10, 20, 2, 12, 9, 19, 21, 5, 11, 15, 6, 7, 8, 4, 13, 18, 1, 0, 17, 14, 16, 3], 'cur_cost': 30388.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 20, 2, 12, 9, 19, 21, 5, 11, 15, 13, 4, 8, 7, 6, 18, 1, 3, 17, 14, 16, 0], 'cur_cost': 30503.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 20, 2, 12, 9, 19, 21, 5, 11, 15, 6, 7, 8, 13, 18, 1, 3, 17, 4, 14, 16, 0], 'cur_cost': 34076.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 9,  1,  4, 16,  2, 18,  3, 11,  8, 21,  0,  5,  7, 12, 15, 19, 13,
       20, 14, 17, 10,  6], dtype=int64), 'cur_cost': 42238.0, 'intermediate_solutions': [{'tour': array([ 1, 15,  7,  6,  5, 13, 21,  2,  4, 12, 18, 17, 19, 11, 10,  0, 14,
        9,  3, 20,  8, 16]), 'cur_cost': 32703.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 6,  1, 15,  7,  5, 13, 21,  2,  4, 12, 18, 17, 19, 11, 10,  0, 14,
        9,  3, 20,  8, 16]), 'cur_cost': 36230.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 5,  6,  1, 15,  7, 13, 21,  2,  4, 12, 18, 17, 19, 11, 10,  0, 14,
        9,  3, 20,  8, 16]), 'cur_cost': 36173.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 7,  6,  1, 15,  5, 13, 21,  2,  4, 12, 18, 17, 19, 11, 10,  0, 14,
        9,  3, 20,  8, 16]), 'cur_cost': 36228.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 7,  5,  6,  1, 15, 13, 21,  2,  4, 12, 18, 17, 19, 11, 10,  0, 14,
        9,  3, 20,  8, 16]), 'cur_cost': 32623.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [18, 7, 6, 0, 21, 17, 14, 20, 3, 16, 19, 11, 1, 10, 4, 8, 12, 5, 2, 9, 13, 15], 'cur_cost': 35393.0, 'intermediate_solutions': [{'tour': [6, 16, 20, 2, 19, 0, 14, 10, 18, 15, 13, 21, 1, 12, 9, 17, 7, 4, 5, 8, 3, 11], 'cur_cost': 29174.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 16, 20, 2, 1, 0, 15, 18, 10, 14, 13, 21, 19, 12, 9, 17, 7, 4, 5, 8, 3, 11], 'cur_cost': 22070.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 16, 20, 2, 1, 0, 14, 10, 18, 4, 15, 13, 21, 19, 12, 9, 17, 7, 5, 8, 3, 11], 'cur_cost': 28776.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [1, 6, 0, 17, 15, 16, 21, 20, 19, 18, 3, 2, 13, 9, 10, 11, 12, 14, 7, 4, 5, 8], 'cur_cost': 17391.0, 'intermediate_solutions': [{'tour': [21, 20, 4, 13, 9, 10, 11, 12, 14, 17, 15, 16, 0, 19, 18, 1, 2, 3, 7, 6, 5, 8], 'cur_cost': 15986.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 4, 13, 9, 6, 7, 3, 2, 1, 18, 19, 21, 16, 15, 17, 14, 12, 11, 10, 5, 8], 'cur_cost': 22067.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 3, 4, 13, 9, 10, 11, 12, 14, 17, 15, 16, 21, 19, 18, 1, 2, 7, 6, 5, 8], 'cur_cost': 17714.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 1, 3, 2, 19, 15, 16, 17, 18, 20, 21, 13, 9, 10, 11, 12, 8, 4, 5, 6, 7], 'cur_cost': 14432.0, 'intermediate_solutions': [{'tour': [1, 5, 0, 11, 6, 15, 16, 17, 18, 20, 21, 3, 2, 13, 9, 10, 14, 12, 8, 4, 19, 7], 'cur_cost': 27794.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 5, 0, 11, 19, 15, 16, 17, 18, 20, 21, 3, 2, 13, 9, 10, 14, 12, 8, 7, 6, 4], 'cur_cost': 20337.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 1, 5, 0, 19, 15, 16, 17, 18, 20, 21, 3, 2, 13, 9, 10, 14, 12, 8, 4, 6, 7], 'cur_cost': 20248.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 2, 18, 13,  0,  6, 15, 19, 20,  4,  8,  7,  9,  5, 17, 14,  3,  1,
       10, 11, 16, 21, 12], dtype=int64), 'cur_cost': 33069.0, 'intermediate_solutions': [{'tour': array([ 8, 16,  7, 13, 20, 19,  2,  9,  1, 12,  4, 10,  3, 17, 15, 18, 14,
        5,  6, 21, 11,  0]), 'cur_cost': 41465.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([13,  8, 16,  7, 20, 19,  2,  9,  1, 12,  4, 10,  3, 17, 15, 18, 14,
        5,  6, 21, 11,  0]), 'cur_cost': 41366.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([20, 13,  8, 16,  7, 19,  2,  9,  1, 12,  4, 10,  3, 17, 15, 18, 14,
        5,  6, 21, 11,  0]), 'cur_cost': 41807.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 7, 13,  8, 16, 20, 19,  2,  9,  1, 12,  4, 10,  3, 17, 15, 18, 14,
        5,  6, 21, 11,  0]), 'cur_cost': 41060.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 7, 20, 13,  8, 16, 19,  2,  9,  1, 12,  4, 10,  3, 17, 15, 18, 14,
        5,  6, 21, 11,  0]), 'cur_cost': 41455.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 09:51:58,776 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:58,776 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:58,777 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=14432.000, 多样性=0.893
2025-08-05 09:51:58,778 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 09:51:58,778 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 09:51:58,778 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:58,784 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.009595676195437474, 'best_improvement': 0.034261241970021415}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.04329004329004333}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.02378364467343121, 'recent_improvements': [0.009208489559467646, 0.003424167143579016, -0.038358799787394766], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 36, 'new_count': 36, 'count_change': 0, 'old_best_cost': 9455.0, 'new_best_cost': 9455.0, 'quality_improvement': 0.0, 'old_diversity': 0.7791486291486293, 'new_diversity': 0.7791486291486293, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:51:58,791 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 09:51:58,800 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite3_22_solution.json
2025-08-05 09:51:58,801 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite3_22_20250805_095158.solution
2025-08-05 09:51:58,801 - __main__ - INFO - 实例执行完成 - 运行时间: 1.48s, 最佳成本: 9455.0
2025-08-05 09:51:58,801 - __main__ - INFO - 实例 composite3_22 处理完成
