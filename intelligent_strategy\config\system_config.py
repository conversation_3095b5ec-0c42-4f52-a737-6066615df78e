"""
System configuration classes for the intelligent strategy selection system.

This module defines configuration classes for system-wide settings,
deployment configurations, and runtime parameters.
"""

from dataclasses import dataclass, field
from typing import Dict, Any, Optional
from enum import Enum


class IntegrationMode(Enum):
    """Integration modes for the system."""
    FULL = "full"
    ANALYSIS_ONLY = "analysis_only"
    STRATEGIES_ONLY = "strategies_only"


class LogLevel(Enum):
    """Logging levels."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


@dataclass
class SystemConfig:
    """Main system configuration."""
    enabled: bool = True
    integration_mode: IntegrationMode = IntegrationMode.FULL
    max_workers: int = 8
    debug_mode: bool = False
    log_level: LogLevel = LogLevel.INFO
    timeout_seconds: float = 30.0
    max_retries: int = 3
    
    # Performance settings
    enable_caching: bool = True
    cache_size: int = 1000
    enable_multiprocessing: bool = True
    max_parallel_strategies: int = 4
    
    # Resource management
    max_memory_usage_mb: int = 2048
    garbage_collection_frequency: int = 100
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'enabled': self.enabled,
            'integration_mode': self.integration_mode.value,
            'max_workers': self.max_workers,
            'debug_mode': self.debug_mode,
            'log_level': self.log_level.value,
            'timeout_seconds': self.timeout_seconds,
            'max_retries': self.max_retries,
            'enable_caching': self.enable_caching,
            'cache_size': self.cache_size,
            'enable_multiprocessing': self.enable_multiprocessing,
            'max_parallel_strategies': self.max_parallel_strategies,
            'max_memory_usage_mb': self.max_memory_usage_mb,
            'garbage_collection_frequency': self.garbage_collection_frequency
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SystemConfig':
        """Create from dictionary."""
        config = cls()
        config.enabled = data.get('enabled', config.enabled)
        config.integration_mode = IntegrationMode(data.get('integration_mode', config.integration_mode.value))
        config.max_workers = data.get('max_workers', config.max_workers)
        config.debug_mode = data.get('debug_mode', config.debug_mode)
        config.log_level = LogLevel(data.get('log_level', config.log_level.value))
        config.timeout_seconds = data.get('timeout_seconds', config.timeout_seconds)
        config.max_retries = data.get('max_retries', config.max_retries)
        config.enable_caching = data.get('enable_caching', config.enable_caching)
        config.cache_size = data.get('cache_size', config.cache_size)
        config.enable_multiprocessing = data.get('enable_multiprocessing', config.enable_multiprocessing)
        config.max_parallel_strategies = data.get('max_parallel_strategies', config.max_parallel_strategies)
        config.max_memory_usage_mb = data.get('max_memory_usage_mb', config.max_memory_usage_mb)
        config.garbage_collection_frequency = data.get('garbage_collection_frequency', config.garbage_collection_frequency)
        return config


@dataclass
class DeploymentConfig:
    """Deployment-specific configuration."""
    environment: str = "development"
    host: str = "localhost"
    port: int = 8050
    
    # Security settings
    enable_authentication: bool = False
    api_key_required: bool = False
    
    # Monitoring settings
    enable_metrics: bool = True
    metrics_port: int = 9090
    health_check_interval: int = 30
    
    # Logging settings
    log_file_path: str = "logs/intelligent_strategy.log"
    log_rotation_size: str = "10MB"
    log_retention_days: int = 30
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'environment': self.environment,
            'host': self.host,
            'port': self.port,
            'enable_authentication': self.enable_authentication,
            'api_key_required': self.api_key_required,
            'enable_metrics': self.enable_metrics,
            'metrics_port': self.metrics_port,
            'health_check_interval': self.health_check_interval,
            'log_file_path': self.log_file_path,
            'log_rotation_size': self.log_rotation_size,
            'log_retention_days': self.log_retention_days
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DeploymentConfig':
        """Create from dictionary."""
        config = cls()
        config.environment = data.get('environment', config.environment)
        config.host = data.get('host', config.host)
        config.port = data.get('port', config.port)
        config.enable_authentication = data.get('enable_authentication', config.enable_authentication)
        config.api_key_required = data.get('api_key_required', config.api_key_required)
        config.enable_metrics = data.get('enable_metrics', config.enable_metrics)
        config.metrics_port = data.get('metrics_port', config.metrics_port)
        config.health_check_interval = data.get('health_check_interval', config.health_check_interval)
        config.log_file_path = data.get('log_file_path', config.log_file_path)
        config.log_rotation_size = data.get('log_rotation_size', config.log_rotation_size)
        config.log_retention_days = data.get('log_retention_days', config.log_retention_days)
        return config


# Default configurations
DEFAULT_SYSTEM_CONFIG = SystemConfig()
DEFAULT_DEPLOYMENT_CONFIG = DeploymentConfig()
