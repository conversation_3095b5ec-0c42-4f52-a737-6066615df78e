2025-08-04 17:30:55,239 - __main__ - INFO - simple1_9 开始进化第 1 代
2025-08-04 17:30:55,242 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-04 17:30:55,246 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:30:55,248 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=681.000, 多样性=0.877
2025-08-04 17:30:55,249 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:30:55,250 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.877
2025-08-04 17:30:55,252 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:30:55,309 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-04 17:30:55,309 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:30:55,309 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-04 17:30:55,310 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-04 17:30:55,572 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -22.560, 聚类评分: 0.000, 覆盖率: 0.001, 收敛趋势: 0.000, 多样性: 0.789
2025-08-04 17:30:55,572 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-04 17:30:55,572 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-04 17:30:55,573 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:30:55,608 - visualization.landscape_visualizer - INFO - 插值约束: 123 个点被约束到最小值 681.00
2025-08-04 17:30:55,991 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记，坐标系统已统一
2025-08-04 17:31:01,774 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_1_20250804_173101.html
2025-08-04 17:31:01,818 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_1_20250804_173101.html
2025-08-04 17:31:01,819 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-04 17:31:01,819 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-04 17:31:01,819 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 6.5096秒
2025-08-04 17:31:01,819 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-04 17:31:01,820 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -22.560000000000002, 'local_optima_density': 0.1, 'gradient_variance': 21039.1504, 'cluster_count': 0}, 'population_state': {'diversity': 0.788888888888889, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.001, 'fitness_entropy': 0.8982444017039273, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -22.560)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.001)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.789)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754299855.572746, 'performance_metrics': {}}}
2025-08-04 17:31:01,820 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:31:01,821 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:31:01,821 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:31:01,821 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:31:01,821 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-04 17:31:01,821 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-04 17:31:01,822 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-04 17:31:01,822 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:31:01,822 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:31:01,822 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-04 17:31:01,822 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:31:01,823 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:31:01,823 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-04 17:31:01,823 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:31:01,823 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:31:01,823 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:01,826 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:31:01,826 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:01,968 - ExplorationExpert - INFO - 探索路径生成完成，成本: 680.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:31:01,968 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [5, 3, 7, 8, 2, 4, 0, 1, 6], 'cur_cost': 680.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:31:01,968 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 680.00)
2025-08-04 17:31:01,969 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:31:01,969 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:31:01,969 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:01,970 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:31:01,970 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:01,970 - ExplorationExpert - INFO - 探索路径生成完成，成本: 897.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:31:01,970 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [3, 6, 4, 2, 8, 7, 5, 0, 1], 'cur_cost': 897.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:31:01,970 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 897.00)
2025-08-04 17:31:01,970 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:31:01,971 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:31:01,971 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:01,971 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:31:01,971 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:01,971 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1038.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:31:01,972 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [5, 8, 3, 6, 2, 7, 0, 4, 1], 'cur_cost': 1038.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:31:01,972 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1038.00)
2025-08-04 17:31:01,972 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:31:01,972 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:31:01,972 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:01,973 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:31:01,973 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:01,973 - ExplorationExpert - INFO - 探索路径生成完成，成本: 764.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:31:01,973 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [1, 7, 5, 6, 3, 8, 4, 2, 0], 'cur_cost': 764.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:31:01,974 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 764.00)
2025-08-04 17:31:01,974 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:31:01,974 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:31:01,974 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:01,975 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:31:01,975 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:01,975 - ExplorationExpert - INFO - 探索路径生成完成，成本: 994.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:31:01,975 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [2, 3, 7, 0, 8, 4, 5, 6, 1], 'cur_cost': 994.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:31:01,975 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 994.00)
2025-08-04 17:31:01,976 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:31:01,976 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:31:01,976 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:01,976 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:31:01,976 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:01,977 - ExplorationExpert - INFO - 探索路径生成完成，成本: 985.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:31:01,977 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 8, 4, 7, 2, 6, 5, 3, 1], 'cur_cost': 985.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:31:01,977 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 985.00)
2025-08-04 17:31:01,977 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:31:01,978 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:31:01,978 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:01,979 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:31:01,979 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:01,979 - ExplorationExpert - INFO - 探索路径生成完成，成本: 989.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:31:01,980 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [3, 6, 2, 8, 4, 5, 7, 1, 0], 'cur_cost': 989.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:31:01,980 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 989.00)
2025-08-04 17:31:01,980 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-04 17:31:01,981 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:31:01,983 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:31:01,985 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 1089.0
2025-08-04 17:31:03,196 - ExploitationExpert - INFO - res_population_num: 1
2025-08-04 17:31:03,197 - ExploitationExpert - INFO - res_population_costs: [680.0]
2025-08-04 17:31:03,197 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-04 17:31:03,198 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:31:03,199 - ExploitationExpert - INFO - populations: [{'tour': [5, 3, 7, 8, 2, 4, 0, 1, 6], 'cur_cost': 680.0}, {'tour': [3, 6, 4, 2, 8, 7, 5, 0, 1], 'cur_cost': 897.0}, {'tour': [5, 8, 3, 6, 2, 7, 0, 4, 1], 'cur_cost': 1038.0}, {'tour': [1, 7, 5, 6, 3, 8, 4, 2, 0], 'cur_cost': 764.0}, {'tour': [2, 3, 7, 0, 8, 4, 5, 6, 1], 'cur_cost': 994.0}, {'tour': [0, 8, 4, 7, 2, 6, 5, 3, 1], 'cur_cost': 985.0}, {'tour': [3, 6, 2, 8, 4, 5, 7, 1, 0], 'cur_cost': 989.0}, {'tour': array([8, 4, 6, 7, 2, 1, 5, 0, 3], dtype=int64), 'cur_cost': 1089.0}, {'tour': array([4, 5, 6, 7, 1, 3, 0, 2, 8], dtype=int64), 'cur_cost': 1029.0}, {'tour': array([6, 4, 5, 3, 7, 1, 8, 2, 0], dtype=int64), 'cur_cost': 1038.0}]
2025-08-04 17:31:03,200 - ExploitationExpert - INFO - 局部搜索耗时: 1.22秒，最大迭代次数: 10
2025-08-04 17:31:03,200 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-04 17:31:03,201 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([8, 4, 6, 7, 2, 1, 5, 0, 3], dtype=int64), 'cur_cost': 1089.0, 'intermediate_solutions': [{'tour': array([6, 0, 2, 8, 3, 7, 4, 1, 5], dtype=int64), 'cur_cost': 899.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 6, 0, 2, 3, 7, 4, 1, 5], dtype=int64), 'cur_cost': 1039.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 8, 6, 0, 2, 7, 4, 1, 5], dtype=int64), 'cur_cost': 1001.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 8, 6, 0, 3, 7, 4, 1, 5], dtype=int64), 'cur_cost': 1061.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 3, 8, 6, 0, 7, 4, 1, 5], dtype=int64), 'cur_cost': 1088.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:31:03,201 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1089.00)
2025-08-04 17:31:03,201 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:31:03,202 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:31:03,202 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:03,202 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:31:03,202 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:03,203 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1097.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:31:03,203 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [2, 3, 0, 6, 4, 7, 5, 8, 1], 'cur_cost': 1097.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:31:03,203 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1097.00)
2025-08-04 17:31:03,203 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-04 17:31:03,204 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:31:03,204 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:31:03,204 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 968.0
2025-08-04 17:31:04,919 - ExploitationExpert - INFO - res_population_num: 2
2025-08-04 17:31:04,920 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0]
2025-08-04 17:31:04,920 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:31:04,920 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:31:04,921 - ExploitationExpert - INFO - populations: [{'tour': [5, 3, 7, 8, 2, 4, 0, 1, 6], 'cur_cost': 680.0}, {'tour': [3, 6, 4, 2, 8, 7, 5, 0, 1], 'cur_cost': 897.0}, {'tour': [5, 8, 3, 6, 2, 7, 0, 4, 1], 'cur_cost': 1038.0}, {'tour': [1, 7, 5, 6, 3, 8, 4, 2, 0], 'cur_cost': 764.0}, {'tour': [2, 3, 7, 0, 8, 4, 5, 6, 1], 'cur_cost': 994.0}, {'tour': [0, 8, 4, 7, 2, 6, 5, 3, 1], 'cur_cost': 985.0}, {'tour': [3, 6, 2, 8, 4, 5, 7, 1, 0], 'cur_cost': 989.0}, {'tour': array([8, 4, 6, 7, 2, 1, 5, 0, 3], dtype=int64), 'cur_cost': 1089.0}, {'tour': [2, 3, 0, 6, 4, 7, 5, 8, 1], 'cur_cost': 1097.0}, {'tour': array([1, 0, 2, 7, 6, 5, 4, 3, 8], dtype=int64), 'cur_cost': 968.0}]
2025-08-04 17:31:04,921 - ExploitationExpert - INFO - 局部搜索耗时: 1.72秒，最大迭代次数: 10
2025-08-04 17:31:04,922 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-04 17:31:04,922 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([1, 0, 2, 7, 6, 5, 4, 3, 8], dtype=int64), 'cur_cost': 968.0, 'intermediate_solutions': [{'tour': array([5, 4, 6, 3, 7, 1, 8, 2, 0], dtype=int64), 'cur_cost': 1107.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 5, 4, 6, 7, 1, 8, 2, 0], dtype=int64), 'cur_cost': 1086.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 3, 5, 4, 6, 1, 8, 2, 0], dtype=int64), 'cur_cost': 1019.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 3, 5, 4, 7, 1, 8, 2, 0], dtype=int64), 'cur_cost': 1023.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 7, 3, 5, 4, 1, 8, 2, 0], dtype=int64), 'cur_cost': 952.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:31:04,923 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 968.00)
2025-08-04 17:31:04,923 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:31:04,923 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:31:04,924 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [5, 3, 7, 8, 2, 4, 0, 1, 6], 'cur_cost': 680.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [3, 6, 4, 2, 8, 7, 5, 0, 1], 'cur_cost': 897.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [5, 8, 3, 6, 2, 7, 0, 4, 1], 'cur_cost': 1038.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 5, 6, 3, 8, 4, 2, 0], 'cur_cost': 764.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [2, 3, 7, 0, 8, 4, 5, 6, 1], 'cur_cost': 994.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 4, 7, 2, 6, 5, 3, 1], 'cur_cost': 985.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [3, 6, 2, 8, 4, 5, 7, 1, 0], 'cur_cost': 989.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 4, 6, 7, 2, 1, 5, 0, 3], dtype=int64), 'cur_cost': 1089.0, 'intermediate_solutions': [{'tour': array([6, 0, 2, 8, 3, 7, 4, 1, 5], dtype=int64), 'cur_cost': 899.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 6, 0, 2, 3, 7, 4, 1, 5], dtype=int64), 'cur_cost': 1039.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 8, 6, 0, 2, 7, 4, 1, 5], dtype=int64), 'cur_cost': 1001.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 8, 6, 0, 3, 7, 4, 1, 5], dtype=int64), 'cur_cost': 1061.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 3, 8, 6, 0, 7, 4, 1, 5], dtype=int64), 'cur_cost': 1088.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [2, 3, 0, 6, 4, 7, 5, 8, 1], 'cur_cost': 1097.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([1, 0, 2, 7, 6, 5, 4, 3, 8], dtype=int64), 'cur_cost': 968.0, 'intermediate_solutions': [{'tour': array([5, 4, 6, 3, 7, 1, 8, 2, 0], dtype=int64), 'cur_cost': 1107.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 5, 4, 6, 7, 1, 8, 2, 0], dtype=int64), 'cur_cost': 1086.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 3, 5, 4, 6, 1, 8, 2, 0], dtype=int64), 'cur_cost': 1019.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 3, 5, 4, 7, 1, 8, 2, 0], dtype=int64), 'cur_cost': 1023.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 7, 3, 5, 4, 1, 8, 2, 0], dtype=int64), 'cur_cost': 952.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-04 17:31:04,925 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:31:04,926 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:31:04,927 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=680.000, 多样性=0.857
2025-08-04 17:31:04,927 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-04 17:31:04,927 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-04 17:31:04,927 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:31:04,927 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.03340724137374906, 'best_improvement': 0.0014684287812041115}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.022535211267605583}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6666666666666666, 'new_diversity': 0.6666666666666666, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-04 17:31:04,932 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-04 17:31:04,933 - __main__ - INFO - simple1_9 开始进化第 2 代
2025-08-04 17:31:04,933 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-04 17:31:04,933 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:31:04,934 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=680.000, 多样性=0.857
2025-08-04 17:31:04,934 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:31:04,934 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.857
2025-08-04 17:31:04,935 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:31:04,935 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.667
2025-08-04 17:31:04,937 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-04 17:31:04,937 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:31:04,937 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-04 17:31:04,937 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-04 17:31:04,943 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.250, 适应度梯度: 22.267, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 0.652
2025-08-04 17:31:04,943 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-04 17:31:04,943 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:31:04,943 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:31:04,946 - visualization.landscape_visualizer - INFO - 插值约束: 185 个点被约束到最小值 680.00
2025-08-04 17:31:04,951 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:31:05,024 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_2_20250804_173104.html
2025-08-04 17:31:05,061 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_2_20250804_173104.html
2025-08-04 17:31:05,061 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 2
2025-08-04 17:31:05,062 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-04 17:31:05,062 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1252秒
2025-08-04 17:31:05,063 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.25, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 22.26666666666667, 'local_optima_density': 0.25, 'gradient_variance': 26827.24888888889, 'cluster_count': 0}, 'population_state': {'diversity': 0.6515151515151515, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0022, 'fitness_entropy': 0.9353340267248305, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 22.267)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754299864.9433477, 'performance_metrics': {}}}
2025-08-04 17:31:05,064 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:31:05,064 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:31:05,065 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:31:05,065 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:31:05,065 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-04 17:31:05,066 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-04 17:31:05,066 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-04 17:31:05,066 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:31:05,067 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:31:05,067 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-04 17:31:05,067 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:31:05,068 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:31:05,068 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 3} (总数: 2, 保护比例: 0.20)
2025-08-04 17:31:05,068 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:31:05,068 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:31:05,069 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:05,069 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:31:05,069 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,069 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,070 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,070 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,070 - ExplorationExpert - INFO - 探索路径生成完成，成本: 972.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:31:05,070 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [8, 7, 3, 2, 6, 1, 0, 4, 5], 'cur_cost': 972.0, 'intermediate_solutions': [{'tour': [2, 3, 7, 8, 5, 4, 0, 1, 6], 'cur_cost': 972.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 5, 8, 2, 4, 0, 1, 6], 'cur_cost': 704.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 3, 7, 4, 8, 2, 0, 1, 6], 'cur_cost': 770.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:31:05,071 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 972.00)
2025-08-04 17:31:05,071 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:31:05,071 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:31:05,071 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:05,071 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:31:05,072 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,072 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,072 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,072 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,072 - ExplorationExpert - INFO - 探索路径生成完成，成本: 764.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:31:05,073 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [7, 5, 6, 3, 8, 4, 2, 0, 1], 'cur_cost': 764.0, 'intermediate_solutions': [{'tour': [3, 6, 4, 8, 2, 7, 5, 0, 1], 'cur_cost': 981.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 6, 4, 1, 0, 5, 7, 8, 2], 'cur_cost': 933.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 6, 4, 2, 8, 7, 0, 1, 5], 'cur_cost': 845.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:31:05,073 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 764.00)
2025-08-04 17:31:05,073 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:31:05,073 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:31:05,073 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:05,074 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:31:05,074 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,074 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,074 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,075 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,075 - ExplorationExpert - INFO - 探索路径生成完成，成本: 973.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:31:05,075 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [7, 5, 4, 2, 3, 8, 0, 6, 1], 'cur_cost': 973.0, 'intermediate_solutions': [{'tour': [4, 8, 3, 6, 2, 7, 0, 5, 1], 'cur_cost': 1077.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 8, 3, 6, 2, 7, 0, 1, 4], 'cur_cost': 972.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 8, 3, 6, 2, 7, 0, 4, 1], 'cur_cost': 1038.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:31:05,075 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 973.00)
2025-08-04 17:31:05,076 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:31:05,076 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:31:05,076 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:05,076 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:31:05,076 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,077 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,077 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,078 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,078 - ExplorationExpert - INFO - 探索路径生成完成，成本: 846.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:31:05,078 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [1, 4, 0, 7, 3, 5, 6, 8, 2], 'cur_cost': 846.0, 'intermediate_solutions': [{'tour': [1, 7, 5, 6, 4, 8, 3, 2, 0], 'cur_cost': 907.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 1, 5, 6, 3, 8, 4, 2, 0], 'cur_cost': 900.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 5, 0, 6, 3, 8, 4, 2], 'cur_cost': 887.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:31:05,078 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 846.00)
2025-08-04 17:31:05,079 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:31:05,079 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:31:05,079 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:05,079 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:31:05,080 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,080 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,080 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,081 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,081 - ExplorationExpert - INFO - 探索路径生成完成，成本: 927.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:31:05,081 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [1, 3, 6, 8, 4, 2, 7, 5, 0], 'cur_cost': 927.0, 'intermediate_solutions': [{'tour': [2, 3, 8, 0, 7, 4, 5, 6, 1], 'cur_cost': 1011.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 0, 7, 3, 8, 4, 5, 6, 1], 'cur_cost': 913.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 3, 7, 8, 0, 4, 5, 6, 1], 'cur_cost': 986.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:31:05,082 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 927.00)
2025-08-04 17:31:05,082 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:31:05,082 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:31:05,082 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:05,082 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:31:05,083 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,083 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,083 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,083 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,083 - ExplorationExpert - INFO - 探索路径生成完成，成本: 835.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:31:05,084 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [8, 4, 7, 5, 6, 0, 1, 2, 3], 'cur_cost': 835.0, 'intermediate_solutions': [{'tour': [0, 2, 4, 7, 8, 6, 5, 3, 1], 'cur_cost': 842.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 4, 1, 3, 5, 6, 2, 7], 'cur_cost': 1039.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 4, 7, 2, 6, 5, 3, 1], 'cur_cost': 985.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:31:05,084 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 835.00)
2025-08-04 17:31:05,084 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:31:05,084 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:31:05,085 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:05,085 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:31:05,085 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,085 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,085 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,086 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,086 - ExplorationExpert - INFO - 探索路径生成完成，成本: 935.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:31:05,086 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [7, 5, 8, 3, 4, 0, 1, 6, 2], 'cur_cost': 935.0, 'intermediate_solutions': [{'tour': [3, 6, 2, 8, 4, 1, 7, 5, 0], 'cur_cost': 1039.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 0, 1, 7, 5, 4, 8, 2, 6], 'cur_cost': 989.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 2, 8, 4, 5, 7, 1, 6, 0], 'cur_cost': 1022.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:31:05,086 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 935.00)
2025-08-04 17:31:05,086 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-04 17:31:05,087 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:31:05,087 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:31:05,087 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 924.0
2025-08-04 17:31:05,147 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:31:05,147 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0]
2025-08-04 17:31:05,148 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 17:31:05,149 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:31:05,149 - ExploitationExpert - INFO - populations: [{'tour': [8, 7, 3, 2, 6, 1, 0, 4, 5], 'cur_cost': 972.0}, {'tour': [7, 5, 6, 3, 8, 4, 2, 0, 1], 'cur_cost': 764.0}, {'tour': [7, 5, 4, 2, 3, 8, 0, 6, 1], 'cur_cost': 973.0}, {'tour': [1, 4, 0, 7, 3, 5, 6, 8, 2], 'cur_cost': 846.0}, {'tour': [1, 3, 6, 8, 4, 2, 7, 5, 0], 'cur_cost': 927.0}, {'tour': [8, 4, 7, 5, 6, 0, 1, 2, 3], 'cur_cost': 835.0}, {'tour': [7, 5, 8, 3, 4, 0, 1, 6, 2], 'cur_cost': 935.0}, {'tour': array([8, 1, 6, 7, 0, 2, 4, 5, 3], dtype=int64), 'cur_cost': 924.0}, {'tour': [2, 3, 0, 6, 4, 7, 5, 8, 1], 'cur_cost': 1097.0}, {'tour': [1, 0, 2, 7, 6, 5, 4, 3, 8], 'cur_cost': 968.0}]
2025-08-04 17:31:05,150 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒，最大迭代次数: 10
2025-08-04 17:31:05,150 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-04 17:31:05,151 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([8, 1, 6, 7, 0, 2, 4, 5, 3], dtype=int64), 'cur_cost': 924.0, 'intermediate_solutions': [{'tour': array([6, 4, 8, 7, 2, 1, 5, 0, 3]), 'cur_cost': 1116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 6, 4, 8, 2, 1, 5, 0, 3]), 'cur_cost': 1041.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 7, 6, 4, 8, 1, 5, 0, 3]), 'cur_cost': 1198.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 7, 6, 4, 2, 1, 5, 0, 3]), 'cur_cost': 1005.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 2, 7, 6, 4, 1, 5, 0, 3]), 'cur_cost': 1063.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:31:05,152 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 924.00)
2025-08-04 17:31:05,153 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-04 17:31:05,153 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:31:05,153 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:31:05,153 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 1140.0
2025-08-04 17:31:05,222 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:31:05,222 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0]
2025-08-04 17:31:05,222 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 17:31:05,223 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:31:05,223 - ExploitationExpert - INFO - populations: [{'tour': [8, 7, 3, 2, 6, 1, 0, 4, 5], 'cur_cost': 972.0}, {'tour': [7, 5, 6, 3, 8, 4, 2, 0, 1], 'cur_cost': 764.0}, {'tour': [7, 5, 4, 2, 3, 8, 0, 6, 1], 'cur_cost': 973.0}, {'tour': [1, 4, 0, 7, 3, 5, 6, 8, 2], 'cur_cost': 846.0}, {'tour': [1, 3, 6, 8, 4, 2, 7, 5, 0], 'cur_cost': 927.0}, {'tour': [8, 4, 7, 5, 6, 0, 1, 2, 3], 'cur_cost': 835.0}, {'tour': [7, 5, 8, 3, 4, 0, 1, 6, 2], 'cur_cost': 935.0}, {'tour': array([8, 1, 6, 7, 0, 2, 4, 5, 3], dtype=int64), 'cur_cost': 924.0}, {'tour': array([1, 5, 8, 0, 4, 6, 3, 2, 7], dtype=int64), 'cur_cost': 1140.0}, {'tour': [1, 0, 2, 7, 6, 5, 4, 3, 8], 'cur_cost': 968.0}]
2025-08-04 17:31:05,224 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:31:05,224 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-04 17:31:05,224 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([1, 5, 8, 0, 4, 6, 3, 2, 7], dtype=int64), 'cur_cost': 1140.0, 'intermediate_solutions': [{'tour': array([0, 3, 2, 6, 4, 7, 5, 8, 1]), 'cur_cost': 1094.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 0, 3, 2, 4, 7, 5, 8, 1]), 'cur_cost': 984.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 6, 0, 3, 2, 7, 5, 8, 1]), 'cur_cost': 1102.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 6, 0, 3, 4, 7, 5, 8, 1]), 'cur_cost': 1109.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 4, 6, 0, 3, 7, 5, 8, 1]), 'cur_cost': 969.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:31:05,225 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1140.00)
2025-08-04 17:31:05,225 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:31:05,225 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:31:05,225 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:05,226 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:31:05,226 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,226 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,226 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,227 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,227 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1101.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:31:05,227 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [8, 3, 4, 5, 1, 2, 6, 0, 7], 'cur_cost': 1101.0, 'intermediate_solutions': [{'tour': [1, 0, 2, 4, 6, 5, 7, 3, 8], 'cur_cost': 829.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 0, 2, 7, 3, 4, 5, 6, 8], 'cur_cost': 1009.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 2, 6, 5, 7, 4, 3, 8], 'cur_cost': 969.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:31:05,227 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1101.00)
2025-08-04 17:31:05,228 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:31:05,228 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:31:05,228 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [8, 7, 3, 2, 6, 1, 0, 4, 5], 'cur_cost': 972.0, 'intermediate_solutions': [{'tour': [2, 3, 7, 8, 5, 4, 0, 1, 6], 'cur_cost': 972.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 5, 8, 2, 4, 0, 1, 6], 'cur_cost': 704.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 3, 7, 4, 8, 2, 0, 1, 6], 'cur_cost': 770.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [7, 5, 6, 3, 8, 4, 2, 0, 1], 'cur_cost': 764.0, 'intermediate_solutions': [{'tour': [3, 6, 4, 8, 2, 7, 5, 0, 1], 'cur_cost': 981.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 6, 4, 1, 0, 5, 7, 8, 2], 'cur_cost': 933.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 6, 4, 2, 8, 7, 0, 1, 5], 'cur_cost': 845.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [7, 5, 4, 2, 3, 8, 0, 6, 1], 'cur_cost': 973.0, 'intermediate_solutions': [{'tour': [4, 8, 3, 6, 2, 7, 0, 5, 1], 'cur_cost': 1077.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 8, 3, 6, 2, 7, 0, 1, 4], 'cur_cost': 972.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 8, 3, 6, 2, 7, 0, 4, 1], 'cur_cost': 1038.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 0, 7, 3, 5, 6, 8, 2], 'cur_cost': 846.0, 'intermediate_solutions': [{'tour': [1, 7, 5, 6, 4, 8, 3, 2, 0], 'cur_cost': 907.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 1, 5, 6, 3, 8, 4, 2, 0], 'cur_cost': 900.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 5, 0, 6, 3, 8, 4, 2], 'cur_cost': 887.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [1, 3, 6, 8, 4, 2, 7, 5, 0], 'cur_cost': 927.0, 'intermediate_solutions': [{'tour': [2, 3, 8, 0, 7, 4, 5, 6, 1], 'cur_cost': 1011.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 0, 7, 3, 8, 4, 5, 6, 1], 'cur_cost': 913.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 3, 7, 8, 0, 4, 5, 6, 1], 'cur_cost': 986.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [8, 4, 7, 5, 6, 0, 1, 2, 3], 'cur_cost': 835.0, 'intermediate_solutions': [{'tour': [0, 2, 4, 7, 8, 6, 5, 3, 1], 'cur_cost': 842.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 4, 1, 3, 5, 6, 2, 7], 'cur_cost': 1039.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 4, 7, 2, 6, 5, 3, 1], 'cur_cost': 985.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [7, 5, 8, 3, 4, 0, 1, 6, 2], 'cur_cost': 935.0, 'intermediate_solutions': [{'tour': [3, 6, 2, 8, 4, 1, 7, 5, 0], 'cur_cost': 1039.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 0, 1, 7, 5, 4, 8, 2, 6], 'cur_cost': 989.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 2, 8, 4, 5, 7, 1, 6, 0], 'cur_cost': 1022.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 1, 6, 7, 0, 2, 4, 5, 3], dtype=int64), 'cur_cost': 924.0, 'intermediate_solutions': [{'tour': array([6, 4, 8, 7, 2, 1, 5, 0, 3]), 'cur_cost': 1116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 6, 4, 8, 2, 1, 5, 0, 3]), 'cur_cost': 1041.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 7, 6, 4, 8, 1, 5, 0, 3]), 'cur_cost': 1198.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 7, 6, 4, 2, 1, 5, 0, 3]), 'cur_cost': 1005.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 2, 7, 6, 4, 1, 5, 0, 3]), 'cur_cost': 1063.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([1, 5, 8, 0, 4, 6, 3, 2, 7], dtype=int64), 'cur_cost': 1140.0, 'intermediate_solutions': [{'tour': array([0, 3, 2, 6, 4, 7, 5, 8, 1]), 'cur_cost': 1094.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 0, 3, 2, 4, 7, 5, 8, 1]), 'cur_cost': 984.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 6, 0, 3, 2, 7, 5, 8, 1]), 'cur_cost': 1102.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 6, 0, 3, 4, 7, 5, 8, 1]), 'cur_cost': 1109.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 4, 6, 0, 3, 7, 5, 8, 1]), 'cur_cost': 969.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [8, 3, 4, 5, 1, 2, 6, 0, 7], 'cur_cost': 1101.0, 'intermediate_solutions': [{'tour': [1, 0, 2, 4, 6, 5, 7, 3, 8], 'cur_cost': 829.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 0, 2, 7, 3, 4, 5, 6, 8], 'cur_cost': 1009.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 2, 6, 5, 7, 4, 3, 8], 'cur_cost': 969.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:31:05,231 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:31:05,232 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:31:05,233 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=764.000, 多样性=0.879
2025-08-04 17:31:05,233 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-04 17:31:05,234 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-04 17:31:05,234 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:31:05,234 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.051295349788879833, 'best_improvement': -0.12352941176470589}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.0259365994236309}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-04 17:31:05,234 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-04 17:31:05,235 - __main__ - INFO - simple1_9 开始进化第 3 代
2025-08-04 17:31:05,235 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-04 17:31:05,235 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:31:05,236 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=764.000, 多样性=0.879
2025-08-04 17:31:05,236 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:31:05,237 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.879
2025-08-04 17:31:05,237 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:31:05,238 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.630
2025-08-04 17:31:05,239 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-04 17:31:05,239 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:31:05,239 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-04 17:31:05,239 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-04 17:31:05,246 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.308, 适应度梯度: 3.400, 聚类评分: 0.000, 覆盖率: 0.003, 收敛趋势: 0.000, 多样性: 0.606
2025-08-04 17:31:05,247 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-04 17:31:05,247 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:31:05,247 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:31:05,252 - visualization.landscape_visualizer - INFO - 插值约束: 3 个点被约束到最小值 680.00
2025-08-04 17:31:05,255 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:31:05,331 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_3_20250804_173105.html
2025-08-04 17:31:05,365 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_3_20250804_173105.html
2025-08-04 17:31:05,365 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 3
2025-08-04 17:31:05,365 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-04 17:31:05,366 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1274秒
2025-08-04 17:31:05,366 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3076923076923077, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 3.4000000000000012, 'local_optima_density': 0.3076923076923077, 'gradient_variance': 22647.052307692313, 'cluster_count': 0}, 'population_state': {'diversity': 0.6055226824457594, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0033, 'fitness_entropy': 0.9699870314266159, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.003)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 3.400)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754299865.2474253, 'performance_metrics': {}}}
2025-08-04 17:31:05,367 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:31:05,367 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:31:05,367 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:31:05,367 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:31:05,367 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-04 17:31:05,368 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-04 17:31:05,368 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-04 17:31:05,368 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:31:05,368 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:31:05,369 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-04 17:31:05,369 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:31:05,369 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:31:05,369 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 5} (总数: 2, 保护比例: 0.20)
2025-08-04 17:31:05,370 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:31:05,370 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:31:05,370 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:05,370 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:31:05,371 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,371 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,371 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,372 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,372 - ExplorationExpert - INFO - 探索路径生成完成，成本: 975.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:31:05,372 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [7, 1, 3, 8, 0, 4, 2, 6, 5], 'cur_cost': 975.0, 'intermediate_solutions': [{'tour': [0, 7, 3, 2, 6, 1, 8, 4, 5], 'cur_cost': 1142.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 7, 3, 2, 6, 1, 0, 5, 4], 'cur_cost': 1011.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 3, 2, 6, 1, 7, 0, 4, 5], 'cur_cost': 1070.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:31:05,372 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 975.00)
2025-08-04 17:31:05,373 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:31:05,373 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:31:05,373 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:05,373 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:31:05,373 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,373 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,374 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,374 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,374 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1000.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:31:05,374 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [2, 8, 0, 3, 6, 7, 5, 4, 1], 'cur_cost': 1000.0, 'intermediate_solutions': [{'tour': [7, 5, 0, 3, 8, 4, 2, 6, 1], 'cur_cost': 984.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 6, 3, 8, 1, 0, 2, 4], 'cur_cost': 814.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 5, 6, 3, 8, 4, 2, 0, 1], 'cur_cost': 764.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:31:05,375 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 1000.00)
2025-08-04 17:31:05,375 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-04 17:31:05,375 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:31:05,375 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:31:05,375 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 979.0
2025-08-04 17:31:05,444 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:31:05,446 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0]
2025-08-04 17:31:05,446 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 17:31:05,447 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:31:05,447 - ExploitationExpert - INFO - populations: [{'tour': [7, 1, 3, 8, 0, 4, 2, 6, 5], 'cur_cost': 975.0}, {'tour': [2, 8, 0, 3, 6, 7, 5, 4, 1], 'cur_cost': 1000.0}, {'tour': array([7, 3, 8, 2, 5, 4, 0, 6, 1], dtype=int64), 'cur_cost': 979.0}, {'tour': [1, 4, 0, 7, 3, 5, 6, 8, 2], 'cur_cost': 846.0}, {'tour': [1, 3, 6, 8, 4, 2, 7, 5, 0], 'cur_cost': 927.0}, {'tour': [8, 4, 7, 5, 6, 0, 1, 2, 3], 'cur_cost': 835.0}, {'tour': [7, 5, 8, 3, 4, 0, 1, 6, 2], 'cur_cost': 935.0}, {'tour': [8, 1, 6, 7, 0, 2, 4, 5, 3], 'cur_cost': 924.0}, {'tour': [1, 5, 8, 0, 4, 6, 3, 2, 7], 'cur_cost': 1140.0}, {'tour': [8, 3, 4, 5, 1, 2, 6, 0, 7], 'cur_cost': 1101.0}]
2025-08-04 17:31:05,448 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:31:05,448 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-04 17:31:05,449 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([7, 3, 8, 2, 5, 4, 0, 6, 1], dtype=int64), 'cur_cost': 979.0, 'intermediate_solutions': [{'tour': array([4, 5, 7, 2, 3, 8, 0, 6, 1]), 'cur_cost': 1030.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 4, 5, 7, 3, 8, 0, 6, 1]), 'cur_cost': 897.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 2, 4, 5, 7, 8, 0, 6, 1]), 'cur_cost': 1021.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 2, 4, 5, 3, 8, 0, 6, 1]), 'cur_cost': 945.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 3, 2, 4, 5, 8, 0, 6, 1]), 'cur_cost': 983.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:31:05,449 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 979.00)
2025-08-04 17:31:05,449 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:31:05,450 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:31:05,450 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:05,450 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:31:05,450 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,451 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,451 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,451 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,451 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1087.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:31:05,451 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [2, 8, 3, 0, 7, 4, 6, 1, 5], 'cur_cost': 1087.0, 'intermediate_solutions': [{'tour': [6, 4, 0, 7, 3, 5, 1, 8, 2], 'cur_cost': 1071.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 6, 5, 3, 7, 0, 4, 1, 2], 'cur_cost': 846.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 8, 4, 0, 7, 3, 5, 6, 2], 'cur_cost': 959.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:31:05,452 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1087.00)
2025-08-04 17:31:05,452 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:31:05,452 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:31:05,452 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:05,453 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:31:05,453 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,453 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,453 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,453 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,454 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1018.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:31:05,454 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [2, 4, 0, 7, 6, 3, 1, 8, 5], 'cur_cost': 1018.0, 'intermediate_solutions': [{'tour': [1, 7, 6, 8, 4, 2, 3, 5, 0], 'cur_cost': 878.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 6, 3, 1, 2, 7, 5, 0], 'cur_cost': 1052.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 3, 0, 6, 8, 4, 2, 7, 5], 'cur_cost': 1043.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:31:05,454 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1018.00)
2025-08-04 17:31:05,454 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:31:05,455 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:31:05,455 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:05,455 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:31:05,455 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,456 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,456 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,456 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,456 - ExplorationExpert - INFO - 探索路径生成完成，成本: 680.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:31:05,456 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [6, 5, 3, 7, 8, 2, 4, 0, 1], 'cur_cost': 680.0, 'intermediate_solutions': [{'tour': [8, 4, 7, 5, 1, 0, 6, 2, 3], 'cur_cost': 982.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 4, 7, 5, 6, 0, 2, 1, 3], 'cur_cost': 935.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 7, 5, 6, 0, 4, 1, 2, 3], 'cur_cost': 881.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:31:05,457 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 680.00)
2025-08-04 17:31:05,457 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:31:05,457 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:31:05,457 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:05,458 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:31:05,458 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,458 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,458 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,458 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,459 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1022.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:31:05,459 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [6, 4, 0, 8, 7, 2, 1, 3, 5], 'cur_cost': 1022.0, 'intermediate_solutions': [{'tour': [7, 5, 8, 3, 4, 0, 2, 6, 1], 'cur_cost': 1014.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 8, 3, 4, 0, 1, 2, 6], 'cur_cost': 905.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 7, 5, 8, 3, 4, 0, 1, 6], 'cur_cost': 935.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:31:05,459 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1022.00)
2025-08-04 17:31:05,459 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:31:05,460 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:31:05,460 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:05,460 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:31:05,460 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,461 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,461 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,461 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,461 - ExplorationExpert - INFO - 探索路径生成完成，成本: 848.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:31:05,461 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [4, 7, 5, 3, 8, 0, 1, 6, 2], 'cur_cost': 848.0, 'intermediate_solutions': [{'tour': [8, 1, 2, 7, 0, 6, 4, 5, 3], 'cur_cost': 1056.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 0, 7, 6, 1, 8, 4, 5, 3], 'cur_cost': 1035.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 1, 6, 2, 7, 0, 4, 5, 3], 'cur_cost': 1065.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:31:05,462 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 848.00)
2025-08-04 17:31:05,462 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-04 17:31:05,462 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:31:05,462 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:31:05,462 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 1121.0
2025-08-04 17:31:05,525 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:31:05,525 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0]
2025-08-04 17:31:05,526 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 17:31:05,526 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:31:05,527 - ExploitationExpert - INFO - populations: [{'tour': [7, 1, 3, 8, 0, 4, 2, 6, 5], 'cur_cost': 975.0}, {'tour': [2, 8, 0, 3, 6, 7, 5, 4, 1], 'cur_cost': 1000.0}, {'tour': array([7, 3, 8, 2, 5, 4, 0, 6, 1], dtype=int64), 'cur_cost': 979.0}, {'tour': [2, 8, 3, 0, 7, 4, 6, 1, 5], 'cur_cost': 1087.0}, {'tour': [2, 4, 0, 7, 6, 3, 1, 8, 5], 'cur_cost': 1018.0}, {'tour': [6, 5, 3, 7, 8, 2, 4, 0, 1], 'cur_cost': 680.0}, {'tour': [6, 4, 0, 8, 7, 2, 1, 3, 5], 'cur_cost': 1022.0}, {'tour': [4, 7, 5, 3, 8, 0, 1, 6, 2], 'cur_cost': 848.0}, {'tour': array([8, 1, 7, 2, 0, 3, 4, 6, 5], dtype=int64), 'cur_cost': 1121.0}, {'tour': [8, 3, 4, 5, 1, 2, 6, 0, 7], 'cur_cost': 1101.0}]
2025-08-04 17:31:05,527 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:31:05,528 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-04 17:31:05,528 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([8, 1, 7, 2, 0, 3, 4, 6, 5], dtype=int64), 'cur_cost': 1121.0, 'intermediate_solutions': [{'tour': array([8, 5, 1, 0, 4, 6, 3, 2, 7]), 'cur_cost': 1007.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 8, 5, 1, 4, 6, 3, 2, 7]), 'cur_cost': 1121.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 0, 8, 5, 1, 6, 3, 2, 7]), 'cur_cost': 1079.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 0, 8, 5, 4, 6, 3, 2, 7]), 'cur_cost': 1074.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 4, 0, 8, 5, 6, 3, 2, 7]), 'cur_cost': 1002.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:31:05,529 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1121.00)
2025-08-04 17:31:05,529 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-04 17:31:05,530 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:31:05,530 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:31:05,530 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 949.0
2025-08-04 17:31:05,608 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:31:05,608 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0]
2025-08-04 17:31:05,609 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 17:31:05,609 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:31:05,609 - ExploitationExpert - INFO - populations: [{'tour': [7, 1, 3, 8, 0, 4, 2, 6, 5], 'cur_cost': 975.0}, {'tour': [2, 8, 0, 3, 6, 7, 5, 4, 1], 'cur_cost': 1000.0}, {'tour': array([7, 3, 8, 2, 5, 4, 0, 6, 1], dtype=int64), 'cur_cost': 979.0}, {'tour': [2, 8, 3, 0, 7, 4, 6, 1, 5], 'cur_cost': 1087.0}, {'tour': [2, 4, 0, 7, 6, 3, 1, 8, 5], 'cur_cost': 1018.0}, {'tour': [6, 5, 3, 7, 8, 2, 4, 0, 1], 'cur_cost': 680.0}, {'tour': [6, 4, 0, 8, 7, 2, 1, 3, 5], 'cur_cost': 1022.0}, {'tour': [4, 7, 5, 3, 8, 0, 1, 6, 2], 'cur_cost': 848.0}, {'tour': array([8, 1, 7, 2, 0, 3, 4, 6, 5], dtype=int64), 'cur_cost': 1121.0}, {'tour': array([4, 2, 8, 7, 3, 0, 6, 1, 5], dtype=int64), 'cur_cost': 949.0}]
2025-08-04 17:31:05,610 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒，最大迭代次数: 10
2025-08-04 17:31:05,611 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-04 17:31:05,611 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([4, 2, 8, 7, 3, 0, 6, 1, 5], dtype=int64), 'cur_cost': 949.0, 'intermediate_solutions': [{'tour': array([4, 3, 8, 5, 1, 2, 6, 0, 7]), 'cur_cost': 1066.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 4, 3, 8, 1, 2, 6, 0, 7]), 'cur_cost': 1096.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 5, 4, 3, 8, 2, 6, 0, 7]), 'cur_cost': 1102.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 5, 4, 3, 1, 2, 6, 0, 7]), 'cur_cost': 1121.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 1, 5, 4, 3, 2, 6, 0, 7]), 'cur_cost': 1210.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:31:05,612 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 949.00)
2025-08-04 17:31:05,612 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:31:05,612 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:31:05,614 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [7, 1, 3, 8, 0, 4, 2, 6, 5], 'cur_cost': 975.0, 'intermediate_solutions': [{'tour': [0, 7, 3, 2, 6, 1, 8, 4, 5], 'cur_cost': 1142.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 7, 3, 2, 6, 1, 0, 5, 4], 'cur_cost': 1011.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 3, 2, 6, 1, 7, 0, 4, 5], 'cur_cost': 1070.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [2, 8, 0, 3, 6, 7, 5, 4, 1], 'cur_cost': 1000.0, 'intermediate_solutions': [{'tour': [7, 5, 0, 3, 8, 4, 2, 6, 1], 'cur_cost': 984.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 6, 3, 8, 1, 0, 2, 4], 'cur_cost': 814.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 5, 6, 3, 8, 4, 2, 0, 1], 'cur_cost': 764.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 3, 8, 2, 5, 4, 0, 6, 1], dtype=int64), 'cur_cost': 979.0, 'intermediate_solutions': [{'tour': array([4, 5, 7, 2, 3, 8, 0, 6, 1]), 'cur_cost': 1030.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 4, 5, 7, 3, 8, 0, 6, 1]), 'cur_cost': 897.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 2, 4, 5, 7, 8, 0, 6, 1]), 'cur_cost': 1021.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 2, 4, 5, 3, 8, 0, 6, 1]), 'cur_cost': 945.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 3, 2, 4, 5, 8, 0, 6, 1]), 'cur_cost': 983.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [2, 8, 3, 0, 7, 4, 6, 1, 5], 'cur_cost': 1087.0, 'intermediate_solutions': [{'tour': [6, 4, 0, 7, 3, 5, 1, 8, 2], 'cur_cost': 1071.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 6, 5, 3, 7, 0, 4, 1, 2], 'cur_cost': 846.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 8, 4, 0, 7, 3, 5, 6, 2], 'cur_cost': 959.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [2, 4, 0, 7, 6, 3, 1, 8, 5], 'cur_cost': 1018.0, 'intermediate_solutions': [{'tour': [1, 7, 6, 8, 4, 2, 3, 5, 0], 'cur_cost': 878.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 6, 3, 1, 2, 7, 5, 0], 'cur_cost': 1052.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 3, 0, 6, 8, 4, 2, 7, 5], 'cur_cost': 1043.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [6, 5, 3, 7, 8, 2, 4, 0, 1], 'cur_cost': 680.0, 'intermediate_solutions': [{'tour': [8, 4, 7, 5, 1, 0, 6, 2, 3], 'cur_cost': 982.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 4, 7, 5, 6, 0, 2, 1, 3], 'cur_cost': 935.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 7, 5, 6, 0, 4, 1, 2, 3], 'cur_cost': 881.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [6, 4, 0, 8, 7, 2, 1, 3, 5], 'cur_cost': 1022.0, 'intermediate_solutions': [{'tour': [7, 5, 8, 3, 4, 0, 2, 6, 1], 'cur_cost': 1014.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 8, 3, 4, 0, 1, 2, 6], 'cur_cost': 905.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 7, 5, 8, 3, 4, 0, 1, 6], 'cur_cost': 935.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [4, 7, 5, 3, 8, 0, 1, 6, 2], 'cur_cost': 848.0, 'intermediate_solutions': [{'tour': [8, 1, 2, 7, 0, 6, 4, 5, 3], 'cur_cost': 1056.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 0, 7, 6, 1, 8, 4, 5, 3], 'cur_cost': 1035.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 1, 6, 2, 7, 0, 4, 5, 3], 'cur_cost': 1065.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 1, 7, 2, 0, 3, 4, 6, 5], dtype=int64), 'cur_cost': 1121.0, 'intermediate_solutions': [{'tour': array([8, 5, 1, 0, 4, 6, 3, 2, 7]), 'cur_cost': 1007.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 8, 5, 1, 4, 6, 3, 2, 7]), 'cur_cost': 1121.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 0, 8, 5, 1, 6, 3, 2, 7]), 'cur_cost': 1079.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 0, 8, 5, 4, 6, 3, 2, 7]), 'cur_cost': 1074.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 4, 0, 8, 5, 6, 3, 2, 7]), 'cur_cost': 1002.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 2, 8, 7, 3, 0, 6, 1, 5], dtype=int64), 'cur_cost': 949.0, 'intermediate_solutions': [{'tour': array([4, 3, 8, 5, 1, 2, 6, 0, 7]), 'cur_cost': 1066.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 4, 3, 8, 1, 2, 6, 0, 7]), 'cur_cost': 1096.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 5, 4, 3, 8, 2, 6, 0, 7]), 'cur_cost': 1102.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 5, 4, 3, 1, 2, 6, 0, 7]), 'cur_cost': 1121.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 1, 5, 4, 3, 2, 6, 0, 7]), 'cur_cost': 1210.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-04 17:31:05,617 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:31:05,617 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:31:05,618 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=680.000, 多样性=0.847
2025-08-04 17:31:05,618 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-04 17:31:05,619 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-04 17:31:05,619 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:31:05,619 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.03293076221552462, 'best_improvement': 0.1099476439790576}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.03651685393258403}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:31:05,620 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-04 17:31:05,620 - __main__ - INFO - simple1_9 开始进化第 4 代
2025-08-04 17:31:05,621 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-04 17:31:05,621 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:31:05,622 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=680.000, 多样性=0.847
2025-08-04 17:31:05,622 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:31:05,622 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.847
2025-08-04 17:31:05,623 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:31:05,623 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.630
2025-08-04 17:31:05,625 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-04 17:31:05,625 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:31:05,625 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-04 17:31:05,625 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-04 17:31:05,632 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.308, 适应度梯度: 49.938, 聚类评分: 0.000, 覆盖率: 0.004, 收敛趋势: 0.000, 多样性: 0.598
2025-08-04 17:31:05,632 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-04 17:31:05,633 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:31:05,633 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:31:05,638 - visualization.landscape_visualizer - INFO - 插值约束: 106 个点被约束到最小值 680.00
2025-08-04 17:31:05,641 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:31:05,716 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_4_20250804_173105.html
2025-08-04 17:31:05,752 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_4_20250804_173105.html
2025-08-04 17:31:05,753 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 4
2025-08-04 17:31:05,753 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-04 17:31:05,753 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1277秒
2025-08-04 17:31:05,753 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3076923076923077, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 49.93846153846153, 'local_optima_density': 0.3076923076923077, 'gradient_variance': 23988.45775147929, 'cluster_count': 0}, 'population_state': {'diversity': 0.5976331360946745, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0043, 'fitness_entropy': 0.9473539682709406, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.004)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 49.938)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754299865.6328669, 'performance_metrics': {}}}
2025-08-04 17:31:05,754 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:31:05,755 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:31:05,755 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:31:05,755 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:31:05,755 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-04 17:31:05,756 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-04 17:31:05,756 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-04 17:31:05,756 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:31:05,756 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:31:05,757 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-04 17:31:05,757 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:31:05,757 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:31:05,757 - experts.management.collaboration_manager - INFO - 识别精英个体: {5, 7} (总数: 2, 保护比例: 0.20)
2025-08-04 17:31:05,758 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:31:05,758 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:31:05,758 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:05,758 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:31:05,759 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,759 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,759 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,759 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,760 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1148.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:31:05,760 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [8, 5, 7, 0, 3, 2, 6, 4, 1], 'cur_cost': 1148.0, 'intermediate_solutions': [{'tour': [0, 1, 3, 8, 7, 4, 2, 6, 5], 'cur_cost': 908.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 1, 2, 4, 0, 8, 3, 6, 5], 'cur_cost': 859.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 1, 3, 8, 4, 0, 2, 6, 5], 'cur_cost': 1005.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:31:05,760 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1148.00)
2025-08-04 17:31:05,760 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:31:05,760 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:31:05,761 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:05,761 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:31:05,761 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,761 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,761 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,762 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,762 - ExplorationExpert - INFO - 探索路径生成完成，成本: 834.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:31:05,762 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [3, 6, 0, 1, 7, 5, 8, 4, 2], 'cur_cost': 834.0, 'intermediate_solutions': [{'tour': [2, 8, 0, 6, 3, 7, 5, 4, 1], 'cur_cost': 952.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 0, 7, 6, 3, 5, 4, 1], 'cur_cost': 951.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 8, 0, 7, 3, 6, 5, 4, 1], 'cur_cost': 938.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:31:05,762 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 834.00)
2025-08-04 17:31:05,763 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:31:05,763 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:31:05,763 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:05,764 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:31:05,764 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,765 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,765 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,765 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,766 - ExplorationExpert - INFO - 探索路径生成完成，成本: 833.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:31:05,766 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [6, 3, 5, 7, 4, 0, 1, 2, 8], 'cur_cost': 833.0, 'intermediate_solutions': [{'tour': [7, 3, 0, 2, 5, 4, 8, 6, 1], 'cur_cost': 1105.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 8, 2, 5, 4, 0, 1, 6], 'cur_cost': 881.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 3, 0, 8, 2, 5, 4, 6, 1], 'cur_cost': 1129.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:31:05,767 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 833.00)
2025-08-04 17:31:05,767 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-04 17:31:05,767 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:31:05,767 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:31:05,767 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1016.0
2025-08-04 17:31:05,839 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:31:05,839 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0]
2025-08-04 17:31:05,839 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 17:31:05,840 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:31:05,840 - ExploitationExpert - INFO - populations: [{'tour': [8, 5, 7, 0, 3, 2, 6, 4, 1], 'cur_cost': 1148.0}, {'tour': [3, 6, 0, 1, 7, 5, 8, 4, 2], 'cur_cost': 834.0}, {'tour': [6, 3, 5, 7, 4, 0, 1, 2, 8], 'cur_cost': 833.0}, {'tour': array([1, 5, 7, 0, 2, 8, 6, 3, 4], dtype=int64), 'cur_cost': 1016.0}, {'tour': [2, 4, 0, 7, 6, 3, 1, 8, 5], 'cur_cost': 1018.0}, {'tour': [6, 5, 3, 7, 8, 2, 4, 0, 1], 'cur_cost': 680.0}, {'tour': [6, 4, 0, 8, 7, 2, 1, 3, 5], 'cur_cost': 1022.0}, {'tour': [4, 7, 5, 3, 8, 0, 1, 6, 2], 'cur_cost': 848.0}, {'tour': [8, 1, 7, 2, 0, 3, 4, 6, 5], 'cur_cost': 1121.0}, {'tour': [4, 2, 8, 7, 3, 0, 6, 1, 5], 'cur_cost': 949.0}]
2025-08-04 17:31:05,840 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:31:05,841 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-08-04 17:31:05,841 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([1, 5, 7, 0, 2, 8, 6, 3, 4], dtype=int64), 'cur_cost': 1016.0, 'intermediate_solutions': [{'tour': array([3, 8, 2, 0, 7, 4, 6, 1, 5]), 'cur_cost': 979.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 3, 8, 2, 7, 4, 6, 1, 5]), 'cur_cost': 1088.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 0, 3, 8, 2, 4, 6, 1, 5]), 'cur_cost': 946.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 0, 3, 8, 7, 4, 6, 1, 5]), 'cur_cost': 1116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 7, 0, 3, 8, 4, 6, 1, 5]), 'cur_cost': 1118.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:31:05,842 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1016.00)
2025-08-04 17:31:05,842 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:31:05,842 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:31:05,842 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:05,843 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:31:05,843 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,843 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,843 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,843 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,844 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1081.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:31:05,844 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [3, 7, 8, 1, 4, 5, 6, 2, 0], 'cur_cost': 1081.0, 'intermediate_solutions': [{'tour': [2, 4, 0, 7, 6, 3, 5, 8, 1], 'cur_cost': 880.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 1, 3, 6, 7, 0, 4, 5], 'cur_cost': 1106.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 2, 4, 0, 7, 3, 1, 8, 5], 'cur_cost': 983.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:31:05,844 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1081.00)
2025-08-04 17:31:05,844 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:31:05,844 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:31:05,845 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:05,845 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:31:05,845 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,845 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,845 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,846 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,846 - ExplorationExpert - INFO - 探索路径生成完成，成本: 986.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:31:05,846 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [2, 5, 1, 3, 8, 7, 6, 0, 4], 'cur_cost': 986.0, 'intermediate_solutions': [{'tour': [2, 5, 3, 7, 8, 6, 4, 0, 1], 'cur_cost': 936.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 5, 3, 7, 8, 1, 0, 4, 2], 'cur_cost': 826.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 3, 7, 8, 6, 2, 4, 0, 1], 'cur_cost': 886.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:31:05,847 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 986.00)
2025-08-04 17:31:05,847 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-04 17:31:05,847 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:31:05,847 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:31:05,848 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1032.0
2025-08-04 17:31:05,915 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:31:05,915 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0]
2025-08-04 17:31:05,916 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 17:31:05,917 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:31:05,917 - ExploitationExpert - INFO - populations: [{'tour': [8, 5, 7, 0, 3, 2, 6, 4, 1], 'cur_cost': 1148.0}, {'tour': [3, 6, 0, 1, 7, 5, 8, 4, 2], 'cur_cost': 834.0}, {'tour': [6, 3, 5, 7, 4, 0, 1, 2, 8], 'cur_cost': 833.0}, {'tour': array([1, 5, 7, 0, 2, 8, 6, 3, 4], dtype=int64), 'cur_cost': 1016.0}, {'tour': [3, 7, 8, 1, 4, 5, 6, 2, 0], 'cur_cost': 1081.0}, {'tour': [2, 5, 1, 3, 8, 7, 6, 0, 4], 'cur_cost': 986.0}, {'tour': array([4, 1, 8, 5, 3, 2, 6, 7, 0], dtype=int64), 'cur_cost': 1032.0}, {'tour': [4, 7, 5, 3, 8, 0, 1, 6, 2], 'cur_cost': 848.0}, {'tour': [8, 1, 7, 2, 0, 3, 4, 6, 5], 'cur_cost': 1121.0}, {'tour': [4, 2, 8, 7, 3, 0, 6, 1, 5], 'cur_cost': 949.0}]
2025-08-04 17:31:05,918 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:31:05,918 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-08-04 17:31:05,919 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([4, 1, 8, 5, 3, 2, 6, 7, 0], dtype=int64), 'cur_cost': 1032.0, 'intermediate_solutions': [{'tour': array([0, 4, 6, 8, 7, 2, 1, 3, 5]), 'cur_cost': 1083.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 0, 4, 6, 7, 2, 1, 3, 5]), 'cur_cost': 1046.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 8, 0, 4, 6, 2, 1, 3, 5]), 'cur_cost': 1082.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 8, 0, 4, 7, 2, 1, 3, 5]), 'cur_cost': 1021.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 7, 8, 0, 4, 2, 1, 3, 5]), 'cur_cost': 879.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:31:05,920 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1032.00)
2025-08-04 17:31:05,920 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:31:05,920 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:31:05,920 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:05,921 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:31:05,921 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,921 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,921 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,921 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:05,922 - ExplorationExpert - INFO - 探索路径生成完成，成本: 916.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:31:05,922 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [6, 3, 0, 7, 4, 1, 2, 8, 5], 'cur_cost': 916.0, 'intermediate_solutions': [{'tour': [1, 7, 5, 3, 8, 0, 4, 6, 2], 'cur_cost': 1034.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 7, 1, 0, 8, 3, 5, 6, 2], 'cur_cost': 853.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 4, 7, 5, 3, 0, 1, 6, 2], 'cur_cost': 897.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:31:05,923 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 916.00)
2025-08-04 17:31:05,923 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-04 17:31:05,923 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:31:05,923 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:31:05,923 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 991.0
2025-08-04 17:31:05,994 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:31:05,994 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0]
2025-08-04 17:31:05,995 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 17:31:05,995 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:31:05,995 - ExploitationExpert - INFO - populations: [{'tour': [8, 5, 7, 0, 3, 2, 6, 4, 1], 'cur_cost': 1148.0}, {'tour': [3, 6, 0, 1, 7, 5, 8, 4, 2], 'cur_cost': 834.0}, {'tour': [6, 3, 5, 7, 4, 0, 1, 2, 8], 'cur_cost': 833.0}, {'tour': array([1, 5, 7, 0, 2, 8, 6, 3, 4], dtype=int64), 'cur_cost': 1016.0}, {'tour': [3, 7, 8, 1, 4, 5, 6, 2, 0], 'cur_cost': 1081.0}, {'tour': [2, 5, 1, 3, 8, 7, 6, 0, 4], 'cur_cost': 986.0}, {'tour': array([4, 1, 8, 5, 3, 2, 6, 7, 0], dtype=int64), 'cur_cost': 1032.0}, {'tour': [6, 3, 0, 7, 4, 1, 2, 8, 5], 'cur_cost': 916.0}, {'tour': array([4, 5, 3, 0, 6, 1, 8, 7, 2], dtype=int64), 'cur_cost': 991.0}, {'tour': [4, 2, 8, 7, 3, 0, 6, 1, 5], 'cur_cost': 949.0}]
2025-08-04 17:31:05,997 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:31:05,997 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-08-04 17:31:05,998 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([4, 5, 3, 0, 6, 1, 8, 7, 2], dtype=int64), 'cur_cost': 991.0, 'intermediate_solutions': [{'tour': array([7, 1, 8, 2, 0, 3, 4, 6, 5]), 'cur_cost': 1063.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 7, 1, 8, 0, 3, 4, 6, 5]), 'cur_cost': 1205.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 2, 7, 1, 8, 3, 4, 6, 5]), 'cur_cost': 1104.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 2, 7, 1, 0, 3, 4, 6, 5]), 'cur_cost': 961.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 0, 2, 7, 1, 3, 4, 6, 5]), 'cur_cost': 1123.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:31:05,999 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 991.00)
2025-08-04 17:31:05,999 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:31:05,999 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:31:05,999 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:06,000 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:31:06,000 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:06,000 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:06,000 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:06,000 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:06,001 - ExplorationExpert - INFO - 探索路径生成完成，成本: 892.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:31:06,001 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [3, 4, 8, 2, 7, 5, 6, 0, 1], 'cur_cost': 892.0, 'intermediate_solutions': [{'tour': [4, 2, 8, 7, 3, 0, 1, 6, 5], 'cur_cost': 799.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 2, 8, 6, 0, 3, 7, 1, 5], 'cur_cost': 1009.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 4, 2, 8, 7, 3, 0, 6, 1], 'cur_cost': 949.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:31:06,002 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 892.00)
2025-08-04 17:31:06,002 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:31:06,002 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:31:06,003 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [8, 5, 7, 0, 3, 2, 6, 4, 1], 'cur_cost': 1148.0, 'intermediate_solutions': [{'tour': [0, 1, 3, 8, 7, 4, 2, 6, 5], 'cur_cost': 908.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 1, 2, 4, 0, 8, 3, 6, 5], 'cur_cost': 859.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 1, 3, 8, 4, 0, 2, 6, 5], 'cur_cost': 1005.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [3, 6, 0, 1, 7, 5, 8, 4, 2], 'cur_cost': 834.0, 'intermediate_solutions': [{'tour': [2, 8, 0, 6, 3, 7, 5, 4, 1], 'cur_cost': 952.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 0, 7, 6, 3, 5, 4, 1], 'cur_cost': 951.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 8, 0, 7, 3, 6, 5, 4, 1], 'cur_cost': 938.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [6, 3, 5, 7, 4, 0, 1, 2, 8], 'cur_cost': 833.0, 'intermediate_solutions': [{'tour': [7, 3, 0, 2, 5, 4, 8, 6, 1], 'cur_cost': 1105.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 8, 2, 5, 4, 0, 1, 6], 'cur_cost': 881.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 3, 0, 8, 2, 5, 4, 6, 1], 'cur_cost': 1129.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([1, 5, 7, 0, 2, 8, 6, 3, 4], dtype=int64), 'cur_cost': 1016.0, 'intermediate_solutions': [{'tour': array([3, 8, 2, 0, 7, 4, 6, 1, 5]), 'cur_cost': 979.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 3, 8, 2, 7, 4, 6, 1, 5]), 'cur_cost': 1088.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 0, 3, 8, 2, 4, 6, 1, 5]), 'cur_cost': 946.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 0, 3, 8, 7, 4, 6, 1, 5]), 'cur_cost': 1116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 7, 0, 3, 8, 4, 6, 1, 5]), 'cur_cost': 1118.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [3, 7, 8, 1, 4, 5, 6, 2, 0], 'cur_cost': 1081.0, 'intermediate_solutions': [{'tour': [2, 4, 0, 7, 6, 3, 5, 8, 1], 'cur_cost': 880.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 1, 3, 6, 7, 0, 4, 5], 'cur_cost': 1106.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 2, 4, 0, 7, 3, 1, 8, 5], 'cur_cost': 983.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [2, 5, 1, 3, 8, 7, 6, 0, 4], 'cur_cost': 986.0, 'intermediate_solutions': [{'tour': [2, 5, 3, 7, 8, 6, 4, 0, 1], 'cur_cost': 936.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 5, 3, 7, 8, 1, 0, 4, 2], 'cur_cost': 826.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 3, 7, 8, 6, 2, 4, 0, 1], 'cur_cost': 886.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 1, 8, 5, 3, 2, 6, 7, 0], dtype=int64), 'cur_cost': 1032.0, 'intermediate_solutions': [{'tour': array([0, 4, 6, 8, 7, 2, 1, 3, 5]), 'cur_cost': 1083.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 0, 4, 6, 7, 2, 1, 3, 5]), 'cur_cost': 1046.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 8, 0, 4, 6, 2, 1, 3, 5]), 'cur_cost': 1082.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 8, 0, 4, 7, 2, 1, 3, 5]), 'cur_cost': 1021.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 7, 8, 0, 4, 2, 1, 3, 5]), 'cur_cost': 879.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [6, 3, 0, 7, 4, 1, 2, 8, 5], 'cur_cost': 916.0, 'intermediate_solutions': [{'tour': [1, 7, 5, 3, 8, 0, 4, 6, 2], 'cur_cost': 1034.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 7, 1, 0, 8, 3, 5, 6, 2], 'cur_cost': 853.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 4, 7, 5, 3, 0, 1, 6, 2], 'cur_cost': 897.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 5, 3, 0, 6, 1, 8, 7, 2], dtype=int64), 'cur_cost': 991.0, 'intermediate_solutions': [{'tour': array([7, 1, 8, 2, 0, 3, 4, 6, 5]), 'cur_cost': 1063.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 7, 1, 8, 0, 3, 4, 6, 5]), 'cur_cost': 1205.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 2, 7, 1, 8, 3, 4, 6, 5]), 'cur_cost': 1104.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 2, 7, 1, 0, 3, 4, 6, 5]), 'cur_cost': 961.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 0, 2, 7, 1, 3, 4, 6, 5]), 'cur_cost': 1123.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [3, 4, 8, 2, 7, 5, 6, 0, 1], 'cur_cost': 892.0, 'intermediate_solutions': [{'tour': [4, 2, 8, 7, 3, 0, 1, 6, 5], 'cur_cost': 799.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 2, 8, 6, 0, 3, 7, 1, 5], 'cur_cost': 1009.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 4, 2, 8, 7, 3, 0, 6, 1], 'cur_cost': 949.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:31:06,005 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:31:06,006 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:31:06,007 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=833.000, 多样性=0.862
2025-08-04 17:31:06,007 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-04 17:31:06,007 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-04 17:31:06,007 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:31:06,008 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.08475048691298748, 'best_improvement': -0.225}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.017492711370262284}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.03316900179463684, 'recent_improvements': [-0.03340724137374906, -0.051295349788879833, 0.03293076221552462], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:31:06,008 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-04 17:31:06,008 - __main__ - INFO - simple1_9 开始进化第 5 代
2025-08-04 17:31:06,009 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-04 17:31:06,009 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:31:06,010 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=833.000, 多样性=0.862
2025-08-04 17:31:06,010 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:31:06,010 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.862
2025-08-04 17:31:06,011 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:31:06,011 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.630
2025-08-04 17:31:06,013 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-04 17:31:06,013 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:31:06,013 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-04 17:31:06,013 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-04 17:31:06,021 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.308, 适应度梯度: 46.062, 聚类评分: 0.000, 覆盖率: 0.005, 收敛趋势: 0.000, 多样性: 0.609
2025-08-04 17:31:06,022 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-04 17:31:06,022 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:31:06,023 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:31:06,071 - visualization.landscape_visualizer - INFO - 插值约束: 107 个点被约束到最小值 680.00
2025-08-04 17:31:06,074 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:31:06,313 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_5_20250804_173106.html
2025-08-04 17:31:06,368 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_5_20250804_173106.html
2025-08-04 17:31:06,368 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 5
2025-08-04 17:31:06,369 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-04 17:31:06,369 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.3569秒
2025-08-04 17:31:06,370 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3076923076923077, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 46.06153846153846, 'local_optima_density': 0.3076923076923077, 'gradient_variance': 15696.660828402364, 'cluster_count': 0}, 'population_state': {'diversity': 0.6094674556213018, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0053, 'fitness_entropy': 0.957713559837711, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.005)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 46.062)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754299866.0220876, 'performance_metrics': {}}}
2025-08-04 17:31:06,371 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:31:06,371 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:31:06,371 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:31:06,372 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:31:06,372 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:31:06,372 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-04 17:31:06,372 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:31:06,373 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:31:06,373 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:31:06,373 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:31:06,374 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:31:06,374 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:31:06,374 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2} (总数: 2, 保护比例: 0.20)
2025-08-04 17:31:06,375 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-04 17:31:06,375 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:31:06,375 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:31:06,375 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 967.0
2025-08-04 17:31:06,452 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:31:06,452 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0]
2025-08-04 17:31:06,453 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 17:31:06,453 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:31:06,454 - ExploitationExpert - INFO - populations: [{'tour': array([2, 8, 6, 4, 5, 7, 3, 0, 1], dtype=int64), 'cur_cost': 967.0}, {'tour': [3, 6, 0, 1, 7, 5, 8, 4, 2], 'cur_cost': 834.0}, {'tour': [6, 3, 5, 7, 4, 0, 1, 2, 8], 'cur_cost': 833.0}, {'tour': [1, 5, 7, 0, 2, 8, 6, 3, 4], 'cur_cost': 1016.0}, {'tour': [3, 7, 8, 1, 4, 5, 6, 2, 0], 'cur_cost': 1081.0}, {'tour': [2, 5, 1, 3, 8, 7, 6, 0, 4], 'cur_cost': 986.0}, {'tour': [4, 1, 8, 5, 3, 2, 6, 7, 0], 'cur_cost': 1032.0}, {'tour': [6, 3, 0, 7, 4, 1, 2, 8, 5], 'cur_cost': 916.0}, {'tour': [4, 5, 3, 0, 6, 1, 8, 7, 2], 'cur_cost': 991.0}, {'tour': [3, 4, 8, 2, 7, 5, 6, 0, 1], 'cur_cost': 892.0}]
2025-08-04 17:31:06,454 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒，最大迭代次数: 10
2025-08-04 17:31:06,454 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-08-04 17:31:06,455 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([2, 8, 6, 4, 5, 7, 3, 0, 1], dtype=int64), 'cur_cost': 967.0, 'intermediate_solutions': [{'tour': array([7, 5, 8, 0, 3, 2, 6, 4, 1]), 'cur_cost': 1150.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 7, 5, 8, 3, 2, 6, 4, 1]), 'cur_cost': 969.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 0, 7, 5, 8, 2, 6, 4, 1]), 'cur_cost': 1088.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 0, 7, 5, 3, 2, 6, 4, 1]), 'cur_cost': 1124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 3, 0, 7, 5, 2, 6, 4, 1]), 'cur_cost': 1151.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:31:06,455 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 967.00)
2025-08-04 17:31:06,455 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:31:06,456 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:31:06,456 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:06,456 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:31:06,456 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:06,457 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:06,457 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:06,457 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:06,457 - ExplorationExpert - INFO - 探索路径生成完成，成本: 932.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:31:06,458 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [1, 0, 2, 7, 4, 5, 8, 3, 6], 'cur_cost': 932.0, 'intermediate_solutions': [{'tour': [3, 6, 0, 7, 1, 5, 8, 4, 2], 'cur_cost': 970.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 6, 0, 2, 4, 8, 5, 7, 1], 'cur_cost': 934.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 0, 1, 7, 5, 6, 8, 4, 2], 'cur_cost': 872.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:31:06,458 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 932.00)
2025-08-04 17:31:06,458 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:31:06,458 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:31:06,458 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:06,459 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:31:06,459 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:06,459 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:06,459 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:06,459 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:06,460 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1068.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:31:06,460 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [2, 5, 1, 7, 3, 8, 4, 0, 6], 'cur_cost': 1068.0, 'intermediate_solutions': [{'tour': [6, 3, 5, 7, 1, 0, 4, 2, 8], 'cur_cost': 781.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 7, 5, 3, 6, 0, 1, 2, 8], 'cur_cost': 800.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 5, 7, 6, 4, 0, 1, 2, 8], 'cur_cost': 807.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:31:06,460 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 1068.00)
2025-08-04 17:31:06,460 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:31:06,460 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:31:06,461 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:06,461 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:31:06,461 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:06,461 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:06,462 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:06,462 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:06,462 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1077.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:31:06,462 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [6, 0, 2, 8, 7, 4, 5, 1, 3], 'cur_cost': 1077.0, 'intermediate_solutions': [{'tour': [1, 7, 5, 0, 2, 8, 6, 3, 4], 'cur_cost': 1019.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 5, 7, 0, 2, 8, 6, 4, 3], 'cur_cost': 1123.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 5, 7, 0, 2, 8, 6, 3], 'cur_cost': 1036.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:31:06,463 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1077.00)
2025-08-04 17:31:06,463 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-04 17:31:06,463 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:31:06,463 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:31:06,464 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 888.0
2025-08-04 17:31:06,529 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:31:06,529 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0]
2025-08-04 17:31:06,529 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 17:31:06,530 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:31:06,531 - ExploitationExpert - INFO - populations: [{'tour': array([2, 8, 6, 4, 5, 7, 3, 0, 1], dtype=int64), 'cur_cost': 967.0}, {'tour': [1, 0, 2, 7, 4, 5, 8, 3, 6], 'cur_cost': 932.0}, {'tour': [2, 5, 1, 7, 3, 8, 4, 0, 6], 'cur_cost': 1068.0}, {'tour': [6, 0, 2, 8, 7, 4, 5, 1, 3], 'cur_cost': 1077.0}, {'tour': array([3, 7, 6, 2, 4, 8, 0, 1, 5], dtype=int64), 'cur_cost': 888.0}, {'tour': [2, 5, 1, 3, 8, 7, 6, 0, 4], 'cur_cost': 986.0}, {'tour': [4, 1, 8, 5, 3, 2, 6, 7, 0], 'cur_cost': 1032.0}, {'tour': [6, 3, 0, 7, 4, 1, 2, 8, 5], 'cur_cost': 916.0}, {'tour': [4, 5, 3, 0, 6, 1, 8, 7, 2], 'cur_cost': 991.0}, {'tour': [3, 4, 8, 2, 7, 5, 6, 0, 1], 'cur_cost': 892.0}]
2025-08-04 17:31:06,532 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:31:06,532 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-08-04 17:31:06,533 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([3, 7, 6, 2, 4, 8, 0, 1, 5], dtype=int64), 'cur_cost': 888.0, 'intermediate_solutions': [{'tour': array([8, 7, 3, 1, 4, 5, 6, 2, 0]), 'cur_cost': 1083.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 8, 7, 3, 4, 5, 6, 2, 0]), 'cur_cost': 1014.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 1, 8, 7, 3, 5, 6, 2, 0]), 'cur_cost': 962.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 1, 8, 7, 4, 5, 6, 2, 0]), 'cur_cost': 1173.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 4, 1, 8, 7, 5, 6, 2, 0]), 'cur_cost': 1071.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:31:06,533 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 888.00)
2025-08-04 17:31:06,533 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:31:06,534 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:31:06,534 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:06,534 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:31:06,534 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:06,535 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:06,535 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:06,535 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:06,535 - ExplorationExpert - INFO - 探索路径生成完成，成本: 972.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:31:06,535 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [1, 0, 2, 8, 6, 3, 7, 4, 5], 'cur_cost': 972.0, 'intermediate_solutions': [{'tour': [2, 5, 1, 3, 0, 7, 6, 8, 4], 'cur_cost': 1083.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 2, 1, 3, 8, 7, 6, 0, 4], 'cur_cost': 1045.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 5, 1, 3, 7, 6, 0, 8, 4], 'cur_cost': 1029.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:31:06,536 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 972.00)
2025-08-04 17:31:06,536 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-04 17:31:06,536 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:31:06,536 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:31:06,536 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1011.0
2025-08-04 17:31:06,600 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:31:06,601 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0]
2025-08-04 17:31:06,601 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 17:31:06,602 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:31:06,602 - ExploitationExpert - INFO - populations: [{'tour': array([2, 8, 6, 4, 5, 7, 3, 0, 1], dtype=int64), 'cur_cost': 967.0}, {'tour': [1, 0, 2, 7, 4, 5, 8, 3, 6], 'cur_cost': 932.0}, {'tour': [2, 5, 1, 7, 3, 8, 4, 0, 6], 'cur_cost': 1068.0}, {'tour': [6, 0, 2, 8, 7, 4, 5, 1, 3], 'cur_cost': 1077.0}, {'tour': array([3, 7, 6, 2, 4, 8, 0, 1, 5], dtype=int64), 'cur_cost': 888.0}, {'tour': [1, 0, 2, 8, 6, 3, 7, 4, 5], 'cur_cost': 972.0}, {'tour': array([2, 5, 0, 3, 8, 6, 7, 1, 4], dtype=int64), 'cur_cost': 1011.0}, {'tour': [6, 3, 0, 7, 4, 1, 2, 8, 5], 'cur_cost': 916.0}, {'tour': [4, 5, 3, 0, 6, 1, 8, 7, 2], 'cur_cost': 991.0}, {'tour': [3, 4, 8, 2, 7, 5, 6, 0, 1], 'cur_cost': 892.0}]
2025-08-04 17:31:06,603 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:31:06,603 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-08-04 17:31:06,604 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([2, 5, 0, 3, 8, 6, 7, 1, 4], dtype=int64), 'cur_cost': 1011.0, 'intermediate_solutions': [{'tour': array([8, 1, 4, 5, 3, 2, 6, 7, 0]), 'cur_cost': 1127.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 8, 1, 4, 3, 2, 6, 7, 0]), 'cur_cost': 1134.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 5, 8, 1, 4, 2, 6, 7, 0]), 'cur_cost': 977.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 5, 8, 1, 3, 2, 6, 7, 0]), 'cur_cost': 1170.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 3, 5, 8, 1, 2, 6, 7, 0]), 'cur_cost': 1035.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:31:06,604 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1011.00)
2025-08-04 17:31:06,605 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:31:06,605 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:31:06,605 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:06,605 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:31:06,606 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:06,606 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:06,606 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:06,606 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:06,607 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1047.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:31:06,607 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 5, 1, 8, 7, 3, 6, 4, 2], 'cur_cost': 1047.0, 'intermediate_solutions': [{'tour': [6, 3, 7, 0, 4, 1, 2, 8, 5], 'cur_cost': 843.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 3, 0, 7, 4, 1, 5, 8, 2], 'cur_cost': 1063.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 3, 0, 1, 7, 4, 2, 8, 5], 'cur_cost': 810.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:31:06,607 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1047.00)
2025-08-04 17:31:06,608 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:31:06,608 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:31:06,608 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:06,608 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:31:06,608 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:06,609 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:06,609 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:06,609 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:06,609 - ExplorationExpert - INFO - 探索路径生成完成，成本: 992.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:31:06,610 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [6, 1, 2, 4, 5, 0, 3, 7, 8], 'cur_cost': 992.0, 'intermediate_solutions': [{'tour': [4, 6, 3, 0, 5, 1, 8, 7, 2], 'cur_cost': 1114.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 5, 3, 0, 1, 6, 8, 7, 2], 'cur_cost': 901.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 5, 7, 3, 0, 6, 1, 8, 2], 'cur_cost': 944.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:31:06,610 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 992.00)
2025-08-04 17:31:06,610 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:31:06,610 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:31:06,611 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:31:06,611 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:31:06,611 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:06,611 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:06,612 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:06,612 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:31:06,612 - ExplorationExpert - INFO - 探索路径生成完成，成本: 734.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:31:06,612 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 7, 5, 6, 3, 8, 4, 2, 1], 'cur_cost': 734.0, 'intermediate_solutions': [{'tour': [3, 4, 2, 8, 7, 5, 6, 0, 1], 'cur_cost': 808.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 4, 8, 2, 7, 5, 1, 0, 6], 'cur_cost': 923.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 4, 8, 2, 7, 5, 6, 1, 0], 'cur_cost': 873.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:31:06,613 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 734.00)
2025-08-04 17:31:06,613 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:31:06,613 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:31:06,614 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 8, 6, 4, 5, 7, 3, 0, 1], dtype=int64), 'cur_cost': 967.0, 'intermediate_solutions': [{'tour': array([7, 5, 8, 0, 3, 2, 6, 4, 1]), 'cur_cost': 1150.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 7, 5, 8, 3, 2, 6, 4, 1]), 'cur_cost': 969.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 0, 7, 5, 8, 2, 6, 4, 1]), 'cur_cost': 1088.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 0, 7, 5, 3, 2, 6, 4, 1]), 'cur_cost': 1124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 3, 0, 7, 5, 2, 6, 4, 1]), 'cur_cost': 1151.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [1, 0, 2, 7, 4, 5, 8, 3, 6], 'cur_cost': 932.0, 'intermediate_solutions': [{'tour': [3, 6, 0, 7, 1, 5, 8, 4, 2], 'cur_cost': 970.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 6, 0, 2, 4, 8, 5, 7, 1], 'cur_cost': 934.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 0, 1, 7, 5, 6, 8, 4, 2], 'cur_cost': 872.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [2, 5, 1, 7, 3, 8, 4, 0, 6], 'cur_cost': 1068.0, 'intermediate_solutions': [{'tour': [6, 3, 5, 7, 1, 0, 4, 2, 8], 'cur_cost': 781.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 7, 5, 3, 6, 0, 1, 2, 8], 'cur_cost': 800.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 5, 7, 6, 4, 0, 1, 2, 8], 'cur_cost': 807.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [6, 0, 2, 8, 7, 4, 5, 1, 3], 'cur_cost': 1077.0, 'intermediate_solutions': [{'tour': [1, 7, 5, 0, 2, 8, 6, 3, 4], 'cur_cost': 1019.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 5, 7, 0, 2, 8, 6, 4, 3], 'cur_cost': 1123.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 5, 7, 0, 2, 8, 6, 3], 'cur_cost': 1036.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 7, 6, 2, 4, 8, 0, 1, 5], dtype=int64), 'cur_cost': 888.0, 'intermediate_solutions': [{'tour': array([8, 7, 3, 1, 4, 5, 6, 2, 0]), 'cur_cost': 1083.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 8, 7, 3, 4, 5, 6, 2, 0]), 'cur_cost': 1014.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 1, 8, 7, 3, 5, 6, 2, 0]), 'cur_cost': 962.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 1, 8, 7, 4, 5, 6, 2, 0]), 'cur_cost': 1173.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 4, 1, 8, 7, 5, 6, 2, 0]), 'cur_cost': 1071.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [1, 0, 2, 8, 6, 3, 7, 4, 5], 'cur_cost': 972.0, 'intermediate_solutions': [{'tour': [2, 5, 1, 3, 0, 7, 6, 8, 4], 'cur_cost': 1083.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 2, 1, 3, 8, 7, 6, 0, 4], 'cur_cost': 1045.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 5, 1, 3, 7, 6, 0, 8, 4], 'cur_cost': 1029.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 5, 0, 3, 8, 6, 7, 1, 4], dtype=int64), 'cur_cost': 1011.0, 'intermediate_solutions': [{'tour': array([8, 1, 4, 5, 3, 2, 6, 7, 0]), 'cur_cost': 1127.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 8, 1, 4, 3, 2, 6, 7, 0]), 'cur_cost': 1134.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 5, 8, 1, 4, 2, 6, 7, 0]), 'cur_cost': 977.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 5, 8, 1, 3, 2, 6, 7, 0]), 'cur_cost': 1170.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 3, 5, 8, 1, 2, 6, 7, 0]), 'cur_cost': 1035.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 1, 8, 7, 3, 6, 4, 2], 'cur_cost': 1047.0, 'intermediate_solutions': [{'tour': [6, 3, 7, 0, 4, 1, 2, 8, 5], 'cur_cost': 843.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 3, 0, 7, 4, 1, 5, 8, 2], 'cur_cost': 1063.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 3, 0, 1, 7, 4, 2, 8, 5], 'cur_cost': 810.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [6, 1, 2, 4, 5, 0, 3, 7, 8], 'cur_cost': 992.0, 'intermediate_solutions': [{'tour': [4, 6, 3, 0, 5, 1, 8, 7, 2], 'cur_cost': 1114.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 5, 3, 0, 1, 6, 8, 7, 2], 'cur_cost': 901.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 5, 7, 3, 0, 6, 1, 8, 2], 'cur_cost': 944.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 5, 6, 3, 8, 4, 2, 1], 'cur_cost': 734.0, 'intermediate_solutions': [{'tour': [3, 4, 2, 8, 7, 5, 6, 0, 1], 'cur_cost': 808.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 4, 8, 2, 7, 5, 1, 0, 6], 'cur_cost': 923.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 4, 8, 2, 7, 5, 6, 1, 0], 'cur_cost': 873.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:31:06,617 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:31:06,617 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:31:06,618 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=734.000, 多样性=0.889
2025-08-04 17:31:06,619 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-04 17:31:06,619 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-04 17:31:06,619 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:31:06,619 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.06163614462177904, 'best_improvement': 0.11884753901560624}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.03151862464183377}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.01672756856205382, 'recent_improvements': [-0.051295349788879833, 0.03293076221552462, -0.08475048691298748], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:31:06,620 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-04 17:31:06,667 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_solution.json
2025-08-04 17:31:06,667 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_20250804_173106.solution
2025-08-04 17:31:06,678 - __main__ - INFO - 评估统计 - 总次数: 244156.3333331239, 运行时间: 11.61s, 最佳成本: 680.0
2025-08-04 17:31:06,678 - __main__ - INFO - 实例 simple1_9 处理完成
