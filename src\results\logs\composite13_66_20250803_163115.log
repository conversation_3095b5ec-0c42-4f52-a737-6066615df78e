2025-08-03 16:31:15,819 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-08-03 16:31:15,820 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-03 16:31:15,822 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:31:15,835 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=9890.000, 多样性=0.958
2025-08-03 16:31:15,840 - PathExpert - INFO - 开始路径结构分析
2025-08-03 16:31:15,849 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.958
2025-08-03 16:31:15,893 - EliteExpert - INFO - 开始精英解分析
2025-08-03 16:31:15,895 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/1)
2025-08-03 16:31:15,896 - LandscapeExpert - INFO - 使用直接传入的种群数据: 20个个体
2025-08-03 16:31:15,897 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-03 16:31:15,897 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-03 16:31:16,170 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.250, 适应度梯度: 1406.920, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 1.000
2025-08-03 16:31:16,171 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-03 16:31:16,171 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-03 16:31:16,240 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记
2025-08-03 16:31:16,570 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_1_20250803_163116.html
2025-08-03 16:31:16,624 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_1_20250803_163116.html
2025-08-03 16:31:16,625 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-03 16:31:16,625 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-03 16:31:16,625 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.7297秒
2025-08-03 16:31:16,626 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-03 16:31:16,626 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.25, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 1406.919999999999, 'local_optima_density': 0.25, 'gradient_variance': 1971345879.2255998, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0019, 'fitness_entropy': 0.941232978764294, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 1406.920)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 1, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754209876.171264, 'performance_metrics': {}}}
2025-08-03 16:31:16,627 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-03 16:31:16,627 - experts.strategy.enhanced_strategy_expert - INFO - 开始增强策略分配分析 (迭代 0)
2025-08-03 16:31:16,628 - experts.analysis.individual_state_analyzer - INFO - 开始分析种群状态 (迭代 0, 种群大小: 20)
2025-08-03 16:31:16,629 - experts.strategy.enhanced_strategy_expert - ERROR - 策略分配过程中发生错误: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:31:16,629 - experts.strategy.enhanced_strategy_expert - WARNING - 使用回退策略分配
2025-08-03 16:31:16,629 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 16:31:16,630 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:31:16,630 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-03 16:31:16,630 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 16:31:16,631 - __main__ - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:31:16,631 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-03 16:31:16,632 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2, 4, 5} (总数: 4, 保护比例: 0.20)
2025-08-03 16:31:16,632 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-03 16:31:16,632 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-03 16:31:16,633 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:31:16,637 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:31:16,637 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:31:16,811 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12886.0, 路径长度: 66
2025-08-03 16:31:16,811 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 4, 19, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12886.0}
2025-08-03 16:31:16,812 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 12886.00)
2025-08-03 16:31:16,812 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-03 16:31:16,813 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:31:16,856 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:31:16,858 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 103961.0
2025-08-03 16:31:19,043 - ExploitationExpert - INFO - res_population_num: 1
2025-08-03 16:31:19,043 - ExploitationExpert - INFO - res_population_costs: [9833.0]
2025-08-03 16:31:19,044 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 41, 50, 51,
       38, 45, 44, 39, 47, 46, 48, 43, 40, 49, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:31:19,045 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:31:19,045 - ExploitationExpert - INFO - populations: [{'tour': [0, 4, 19, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12886.0}, {'tour': array([31,  4, 49, 32, 18, 23, 39, 14, 48, 42, 65, 27, 55, 45, 51, 34,  6,
       37, 52, 44, 54,  1,  7, 60, 20, 25, 40, 13, 16, 10, 12, 17, 35, 28,
       43, 57,  5, 36, 41, 24, 19, 58, 61, 53,  2, 15, 26, 56, 46, 62, 38,
       63, 22, 64,  0,  3, 11, 33, 47, 50, 21,  8, 29, 59,  9, 30],
      dtype=int64), 'cur_cost': 103961.0}, {'tour': array([25, 26, 36, 37, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23,
       22, 12, 17, 15, 14, 20, 21, 13, 19,  9, 11,  7,  3,  1,  0, 10,  8,
        2,  6,  4,  5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9890.0}, {'tour': array([40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 21, 20, 13,
       23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30,
       34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,  2,  6, 10,
        0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63],
      dtype=int64), 'cur_cost': 9975.0}, {'tour': array([44, 45, 38, 51, 50, 41, 39, 47, 49, 40, 43, 48, 46, 42, 21, 20, 13,
       23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30,
       34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,  2,  6, 10,
        0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63],
      dtype=int64), 'cur_cost': 9946.0}, {'tour': array([49, 40, 43, 48, 46, 47, 39, 44, 45, 38, 51, 50, 41, 42, 21, 20, 13,
       23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30,
       34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,  2,  6, 10,
        0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63],
      dtype=int64), 'cur_cost': 9945.0}, {'tour': array([57, 46, 56, 26, 11, 35, 49,  8,  7, 25, 18,  2,  5,  6, 24, 27, 61,
       51, 15, 39, 50, 63, 44, 59, 23, 43,  1,  9, 58, 36, 33,  3, 34, 40,
       20, 38, 13, 42, 54, 64, 60, 22, 29, 62, 65, 45, 55, 12, 28, 19, 52,
       53, 32, 31, 14, 10,  0, 48,  4, 41, 17, 21, 37, 47, 30, 16],
      dtype=int64), 'cur_cost': 106446.0}, {'tour': array([ 0, 29, 26, 57, 20, 25, 59, 35, 62, 45,  8,  9, 10, 49, 40, 31, 19,
       15, 46, 32, 27, 37,  2, 30,  3, 11, 52, 13, 23, 14, 63,  4,  5, 65,
       53, 47, 44, 21, 55, 36, 28, 48, 18, 24, 61, 58, 41, 42, 51, 12,  1,
       34, 33, 17, 39, 22, 38,  6, 64, 16,  7, 43, 56, 60, 50, 54],
      dtype=int64), 'cur_cost': 94015.0}, {'tour': array([22, 24, 42, 19, 65, 61, 48, 46, 27, 51, 32, 44, 41, 57, 25,  0, 52,
       35,  8, 56, 33,  7, 11, 60,  4, 50, 53, 49, 43,  1, 14, 34, 15, 18,
       23, 20, 37,  6,  5, 58, 28, 17,  3, 30, 54,  2, 45, 62, 55, 10, 36,
       29, 38, 64, 26, 47, 63, 16,  9, 39, 40, 12, 13, 31, 59, 21],
      dtype=int64), 'cur_cost': 104947.0}, {'tour': array([31, 12, 34, 58, 24, 55, 33, 61, 26,  8, 21,  9, 49, 57, 65, 50, 38,
       40, 47, 48, 16, 37, 17, 18,  6, 63, 64,  0,  5,  3, 45, 44, 20, 19,
       42, 41, 43, 28, 39, 62, 46, 59, 36, 32, 22, 53, 56,  2,  7, 15, 51,
       11, 54,  4, 60, 14, 13, 52, 29, 35, 10,  1, 30, 25, 27, 23],
      dtype=int64), 'cur_cost': 87390.0}, {'tour': array([26, 64, 56, 58, 19,  3,  1, 23, 41, 33,  7, 52, 16, 61,  6, 27,  9,
       28, 43, 25, 35, 51, 57, 14, 17, 20, 15, 48, 44, 10, 45, 65, 24, 38,
       47, 42,  5,  8,  2, 40, 39, 13, 32, 21, 50, 53, 31, 60,  4, 12, 22,
       36, 59, 29, 62, 55, 34, 63, 30, 18, 49,  0, 54, 11, 37, 46],
      dtype=int64), 'cur_cost': 104682.0}, {'tour': array([19, 58, 50, 38,  3, 12, 27, 43, 56, 47, 15, 31, 36, 14, 53, 57, 16,
       21, 61,  4, 22, 60, 46, 48, 55, 11, 23, 35, 10, 49,  5, 29, 13, 32,
       33,  6,  7, 39,  2, 40, 18, 65, 34, 41, 26, 51, 52, 25,  1,  8, 45,
       44, 20,  9, 24, 28, 64, 30, 59, 54, 17, 62, 37,  0, 42, 63],
      dtype=int64), 'cur_cost': 114468.0}, {'tour': array([16,  7, 24, 22, 57, 54, 55, 41, 46,  5, 38, 39, 61, 59,  6,  9,  0,
       47, 31, 26, 40, 52,  1, 30, 29, 28, 15, 17, 33, 25, 34, 21, 49,  8,
       35, 23, 36, 20, 43, 63, 64, 51, 56, 13, 44, 37, 14, 32, 11,  2, 10,
        3, 27, 65, 60, 42, 45, 18, 53, 62, 19, 12, 58, 48,  4, 50],
      dtype=int64), 'cur_cost': 88600.0}, {'tour': array([65, 19,  7, 12, 45, 56,  5, 30, 51, 10,  4, 42, 24, 31, 11, 39, 25,
        1, 52, 57,  6, 29, 43, 54, 22, 27, 64, 46, 50, 40,  9, 35, 20, 58,
       13, 16, 41, 14, 23,  2,  8, 15, 47, 53, 61, 37, 18, 26,  3, 44, 62,
        0, 17, 49, 32, 60, 21, 63, 36, 34, 55, 38, 59, 48, 28, 33],
      dtype=int64), 'cur_cost': 111728.0}, {'tour': array([54, 10, 48, 43, 31,  0, 23, 45, 46, 28, 62, 12, 56,  6, 41, 24, 19,
       60, 25,  7, 16, 34,  9,  5, 40, 55, 15,  1, 42, 52, 32, 20,  3, 27,
       39, 14, 59, 53, 30, 21, 29, 47, 50, 26,  2, 17, 57, 44, 11, 51, 38,
        4, 61, 37, 22,  8, 58, 65, 63, 36, 35, 18, 49, 13, 64, 33],
      dtype=int64), 'cur_cost': 111745.0}, {'tour': array([25, 54, 49, 56, 29, 13, 28, 47, 55, 18, 62,  9, 14, 35, 48,  5, 19,
       17,  7, 65, 37, 21, 38, 63, 58, 39, 24, 64, 23, 11, 45, 60, 50, 36,
       42,  3, 15, 31, 10, 46, 16, 44, 51, 12, 61, 34, 27, 52,  2, 40, 22,
       53,  8, 43,  1, 32, 57, 33, 41,  0, 30, 59, 26, 20,  4,  6],
      dtype=int64), 'cur_cost': 125967.0}, {'tour': array([11, 38, 17, 41, 14,  8, 56, 16, 20, 22, 64, 34, 26, 65,  3, 58, 30,
       42,  5, 61, 18, 52, 50,  7,  2,  0, 63, 46, 29, 31, 10, 60, 25, 28,
       21, 32, 36, 47, 55, 39, 40, 44, 49, 19,  1, 57,  6, 53, 59, 51, 62,
       13,  4, 48, 12, 23, 35, 24, 37,  9, 43, 45, 27, 15, 54, 33],
      dtype=int64), 'cur_cost': 96444.0}, {'tour': array([ 3, 14,  4, 30, 56, 20, 49, 33, 44, 63,  2,  0, 38, 35, 25, 42, 45,
       23, 57,  1, 51, 48, 62,  6, 15, 27, 43, 29, 19, 11, 59, 50, 53, 37,
       24, 46,  5, 40, 34, 28, 47, 17, 18, 61, 41, 32, 13, 31, 12, 26, 52,
       22,  8,  9, 16, 10, 64, 58, 36, 39, 21, 60, 65, 55,  7, 54],
      dtype=int64), 'cur_cost': 101412.0}, {'tour': array([45, 20, 19, 59, 15, 21, 25, 35,  6, 54, 22, 43, 55, 12,  1,  3, 62,
       39, 30, 10, 51, 18,  9, 33, 14, 17, 28, 11,  5, 13, 56, 47, 26, 37,
        4, 32, 34, 27, 36, 23, 64, 44, 46, 49, 61, 40, 24,  0, 63,  8, 41,
       48, 31,  2, 52, 57, 53, 65,  7, 60, 42, 58, 38, 16, 50, 29],
      dtype=int64), 'cur_cost': 94563.0}, {'tour': array([47, 48, 23, 59, 37,  1, 60, 44, 25, 26, 11,  0,  4, 19, 34, 32, 28,
       30,  5, 24, 43, 13,  8,  2, 14, 17, 57, 21, 16, 45, 15, 22, 29, 33,
       36, 42, 12,  9, 20,  7, 39,  6, 40, 56, 38, 10, 51, 65, 64, 49, 41,
       18, 62, 63, 58, 55, 46, 35, 27,  3, 61, 53, 31, 54, 50, 52],
      dtype=int64), 'cur_cost': 94284.0}]
2025-08-03 16:31:19,060 - ExploitationExpert - INFO - 局部搜索耗时: 2.20秒
2025-08-03 16:31:19,060 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-03 16:31:19,061 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([31,  4, 49, 32, 18, 23, 39, 14, 48, 42, 65, 27, 55, 45, 51, 34,  6,
       37, 52, 44, 54,  1,  7, 60, 20, 25, 40, 13, 16, 10, 12, 17, 35, 28,
       43, 57,  5, 36, 41, 24, 19, 58, 61, 53,  2, 15, 26, 56, 46, 62, 38,
       63, 22, 64,  0,  3, 11, 33, 47, 50, 21,  8, 29, 59,  9, 30],
      dtype=int64), 'cur_cost': 103961.0}
2025-08-03 16:31:19,061 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 103961.00)
2025-08-03 16:31:19,062 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-03 16:31:19,062 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-03 16:31:19,062 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:31:19,068 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:31:19,069 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:31:19,069 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12789.0, 路径长度: 66
2025-08-03 16:31:19,070 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 3, 16, 5, 4, 8, 2, 6, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12789.0}
2025-08-03 16:31:19,070 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 12789.00)
2025-08-03 16:31:19,070 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-03 16:31:19,070 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-03 16:31:19,071 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:31:19,075 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:31:19,075 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:31:19,076 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12675.0, 路径长度: 66
2025-08-03 16:31:19,076 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 11, 15, 1, 7, 3, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12675.0}
2025-08-03 16:31:19,076 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 12675.00)
2025-08-03 16:31:19,076 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-03 16:31:19,077 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:31:19,077 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:31:19,077 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 100859.0
2025-08-03 16:31:21,501 - ExploitationExpert - INFO - res_population_num: 2
2025-08-03 16:31:21,501 - ExploitationExpert - INFO - res_population_costs: [9833.0, 9607.0]
2025-08-03 16:31:21,501 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 41, 50, 51,
       38, 45, 44, 39, 47, 46, 48, 43, 40, 49, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 42, 46,
       48, 49, 47, 39, 44, 45, 50, 51, 38, 41, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:31:21,502 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:31:21,502 - ExploitationExpert - INFO - populations: [{'tour': [0, 4, 19, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12886.0}, {'tour': array([31,  4, 49, 32, 18, 23, 39, 14, 48, 42, 65, 27, 55, 45, 51, 34,  6,
       37, 52, 44, 54,  1,  7, 60, 20, 25, 40, 13, 16, 10, 12, 17, 35, 28,
       43, 57,  5, 36, 41, 24, 19, 58, 61, 53,  2, 15, 26, 56, 46, 62, 38,
       63, 22, 64,  0,  3, 11, 33, 47, 50, 21,  8, 29, 59,  9, 30],
      dtype=int64), 'cur_cost': 103961.0}, {'tour': [0, 3, 16, 5, 4, 8, 2, 6, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12789.0}, {'tour': [0, 11, 15, 1, 7, 3, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12675.0}, {'tour': array([62, 50, 38,  9, 64, 58, 48, 60, 44, 40, 11, 39, 56,  5, 21, 20, 26,
       36, 47,  7, 22, 28, 37, 57,  1, 32, 16, 34, 15, 27, 24, 14, 54, 63,
        6,  2, 43, 46, 51, 10,  0, 13, 17, 31, 61, 19,  3, 41, 42, 52, 49,
       23, 55, 53, 29, 59, 18, 45, 30, 65, 25,  4, 33, 12, 35,  8],
      dtype=int64), 'cur_cost': 100859.0}, {'tour': array([49, 40, 43, 48, 46, 47, 39, 44, 45, 38, 51, 50, 41, 42, 21, 20, 13,
       23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30,
       34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,  2,  6, 10,
        0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63],
      dtype=int64), 'cur_cost': 9945.0}, {'tour': array([57, 46, 56, 26, 11, 35, 49,  8,  7, 25, 18,  2,  5,  6, 24, 27, 61,
       51, 15, 39, 50, 63, 44, 59, 23, 43,  1,  9, 58, 36, 33,  3, 34, 40,
       20, 38, 13, 42, 54, 64, 60, 22, 29, 62, 65, 45, 55, 12, 28, 19, 52,
       53, 32, 31, 14, 10,  0, 48,  4, 41, 17, 21, 37, 47, 30, 16],
      dtype=int64), 'cur_cost': 106446.0}, {'tour': array([ 0, 29, 26, 57, 20, 25, 59, 35, 62, 45,  8,  9, 10, 49, 40, 31, 19,
       15, 46, 32, 27, 37,  2, 30,  3, 11, 52, 13, 23, 14, 63,  4,  5, 65,
       53, 47, 44, 21, 55, 36, 28, 48, 18, 24, 61, 58, 41, 42, 51, 12,  1,
       34, 33, 17, 39, 22, 38,  6, 64, 16,  7, 43, 56, 60, 50, 54],
      dtype=int64), 'cur_cost': 94015.0}, {'tour': array([22, 24, 42, 19, 65, 61, 48, 46, 27, 51, 32, 44, 41, 57, 25,  0, 52,
       35,  8, 56, 33,  7, 11, 60,  4, 50, 53, 49, 43,  1, 14, 34, 15, 18,
       23, 20, 37,  6,  5, 58, 28, 17,  3, 30, 54,  2, 45, 62, 55, 10, 36,
       29, 38, 64, 26, 47, 63, 16,  9, 39, 40, 12, 13, 31, 59, 21],
      dtype=int64), 'cur_cost': 104947.0}, {'tour': array([31, 12, 34, 58, 24, 55, 33, 61, 26,  8, 21,  9, 49, 57, 65, 50, 38,
       40, 47, 48, 16, 37, 17, 18,  6, 63, 64,  0,  5,  3, 45, 44, 20, 19,
       42, 41, 43, 28, 39, 62, 46, 59, 36, 32, 22, 53, 56,  2,  7, 15, 51,
       11, 54,  4, 60, 14, 13, 52, 29, 35, 10,  1, 30, 25, 27, 23],
      dtype=int64), 'cur_cost': 87390.0}, {'tour': array([26, 64, 56, 58, 19,  3,  1, 23, 41, 33,  7, 52, 16, 61,  6, 27,  9,
       28, 43, 25, 35, 51, 57, 14, 17, 20, 15, 48, 44, 10, 45, 65, 24, 38,
       47, 42,  5,  8,  2, 40, 39, 13, 32, 21, 50, 53, 31, 60,  4, 12, 22,
       36, 59, 29, 62, 55, 34, 63, 30, 18, 49,  0, 54, 11, 37, 46],
      dtype=int64), 'cur_cost': 104682.0}, {'tour': array([19, 58, 50, 38,  3, 12, 27, 43, 56, 47, 15, 31, 36, 14, 53, 57, 16,
       21, 61,  4, 22, 60, 46, 48, 55, 11, 23, 35, 10, 49,  5, 29, 13, 32,
       33,  6,  7, 39,  2, 40, 18, 65, 34, 41, 26, 51, 52, 25,  1,  8, 45,
       44, 20,  9, 24, 28, 64, 30, 59, 54, 17, 62, 37,  0, 42, 63],
      dtype=int64), 'cur_cost': 114468.0}, {'tour': array([16,  7, 24, 22, 57, 54, 55, 41, 46,  5, 38, 39, 61, 59,  6,  9,  0,
       47, 31, 26, 40, 52,  1, 30, 29, 28, 15, 17, 33, 25, 34, 21, 49,  8,
       35, 23, 36, 20, 43, 63, 64, 51, 56, 13, 44, 37, 14, 32, 11,  2, 10,
        3, 27, 65, 60, 42, 45, 18, 53, 62, 19, 12, 58, 48,  4, 50],
      dtype=int64), 'cur_cost': 88600.0}, {'tour': array([65, 19,  7, 12, 45, 56,  5, 30, 51, 10,  4, 42, 24, 31, 11, 39, 25,
        1, 52, 57,  6, 29, 43, 54, 22, 27, 64, 46, 50, 40,  9, 35, 20, 58,
       13, 16, 41, 14, 23,  2,  8, 15, 47, 53, 61, 37, 18, 26,  3, 44, 62,
        0, 17, 49, 32, 60, 21, 63, 36, 34, 55, 38, 59, 48, 28, 33],
      dtype=int64), 'cur_cost': 111728.0}, {'tour': array([54, 10, 48, 43, 31,  0, 23, 45, 46, 28, 62, 12, 56,  6, 41, 24, 19,
       60, 25,  7, 16, 34,  9,  5, 40, 55, 15,  1, 42, 52, 32, 20,  3, 27,
       39, 14, 59, 53, 30, 21, 29, 47, 50, 26,  2, 17, 57, 44, 11, 51, 38,
        4, 61, 37, 22,  8, 58, 65, 63, 36, 35, 18, 49, 13, 64, 33],
      dtype=int64), 'cur_cost': 111745.0}, {'tour': array([25, 54, 49, 56, 29, 13, 28, 47, 55, 18, 62,  9, 14, 35, 48,  5, 19,
       17,  7, 65, 37, 21, 38, 63, 58, 39, 24, 64, 23, 11, 45, 60, 50, 36,
       42,  3, 15, 31, 10, 46, 16, 44, 51, 12, 61, 34, 27, 52,  2, 40, 22,
       53,  8, 43,  1, 32, 57, 33, 41,  0, 30, 59, 26, 20,  4,  6],
      dtype=int64), 'cur_cost': 125967.0}, {'tour': array([11, 38, 17, 41, 14,  8, 56, 16, 20, 22, 64, 34, 26, 65,  3, 58, 30,
       42,  5, 61, 18, 52, 50,  7,  2,  0, 63, 46, 29, 31, 10, 60, 25, 28,
       21, 32, 36, 47, 55, 39, 40, 44, 49, 19,  1, 57,  6, 53, 59, 51, 62,
       13,  4, 48, 12, 23, 35, 24, 37,  9, 43, 45, 27, 15, 54, 33],
      dtype=int64), 'cur_cost': 96444.0}, {'tour': array([ 3, 14,  4, 30, 56, 20, 49, 33, 44, 63,  2,  0, 38, 35, 25, 42, 45,
       23, 57,  1, 51, 48, 62,  6, 15, 27, 43, 29, 19, 11, 59, 50, 53, 37,
       24, 46,  5, 40, 34, 28, 47, 17, 18, 61, 41, 32, 13, 31, 12, 26, 52,
       22,  8,  9, 16, 10, 64, 58, 36, 39, 21, 60, 65, 55,  7, 54],
      dtype=int64), 'cur_cost': 101412.0}, {'tour': array([45, 20, 19, 59, 15, 21, 25, 35,  6, 54, 22, 43, 55, 12,  1,  3, 62,
       39, 30, 10, 51, 18,  9, 33, 14, 17, 28, 11,  5, 13, 56, 47, 26, 37,
        4, 32, 34, 27, 36, 23, 64, 44, 46, 49, 61, 40, 24,  0, 63,  8, 41,
       48, 31,  2, 52, 57, 53, 65,  7, 60, 42, 58, 38, 16, 50, 29],
      dtype=int64), 'cur_cost': 94563.0}, {'tour': array([47, 48, 23, 59, 37,  1, 60, 44, 25, 26, 11,  0,  4, 19, 34, 32, 28,
       30,  5, 24, 43, 13,  8,  2, 14, 17, 57, 21, 16, 45, 15, 22, 29, 33,
       36, 42, 12,  9, 20,  7, 39,  6, 40, 56, 38, 10, 51, 65, 64, 49, 41,
       18, 62, 63, 58, 55, 46, 35, 27,  3, 61, 53, 31, 54, 50, 52],
      dtype=int64), 'cur_cost': 94284.0}]
2025-08-03 16:31:21,511 - ExploitationExpert - INFO - 局部搜索耗时: 2.43秒
2025-08-03 16:31:21,512 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-03 16:31:21,513 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([62, 50, 38,  9, 64, 58, 48, 60, 44, 40, 11, 39, 56,  5, 21, 20, 26,
       36, 47,  7, 22, 28, 37, 57,  1, 32, 16, 34, 15, 27, 24, 14, 54, 63,
        6,  2, 43, 46, 51, 10,  0, 13, 17, 31, 61, 19,  3, 41, 42, 52, 49,
       23, 55, 53, 29, 59, 18, 45, 30, 65, 25,  4, 33, 12, 35,  8],
      dtype=int64), 'cur_cost': 100859.0}
2025-08-03 16:31:21,514 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 100859.00)
2025-08-03 16:31:21,515 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-03 16:31:21,517 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-03 16:31:21,518 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:31:21,525 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:31:21,525 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:31:21,526 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12708.0, 路径长度: 66
2025-08-03 16:31:21,526 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 14, 8, 1, 7, 3, 9, 11, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12708.0}
2025-08-03 16:31:21,526 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 12708.00)
2025-08-03 16:31:21,527 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-03 16:31:21,527 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-03 16:31:21,527 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:31:21,533 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:31:21,533 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:31:21,534 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10385.0, 路径长度: 66
2025-08-03 16:31:21,534 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 1, 6, 7, 3, 9, 11, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10385.0}
2025-08-03 16:31:21,535 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 10385.00)
2025-08-03 16:31:21,536 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-03 16:31:21,536 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:31:21,537 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:31:21,537 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 116076.0
2025-08-03 16:31:22,131 - ExploitationExpert - INFO - res_population_num: 3
2025-08-03 16:31:22,132 - ExploitationExpert - INFO - res_population_costs: [9833.0, 9607.0, 9587.0]
2025-08-03 16:31:22,133 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 41, 50, 51,
       38, 45, 44, 39, 47, 46, 48, 43, 40, 49, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 42, 46,
       48, 49, 47, 39, 44, 45, 50, 51, 38, 41, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 59, 62, 64, 57,
       54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 27, 37, 36, 26,
       25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  9, 11,  7,  1],
      dtype=int64)]
2025-08-03 16:31:22,136 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:31:22,136 - ExploitationExpert - INFO - populations: [{'tour': [0, 4, 19, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12886.0}, {'tour': array([31,  4, 49, 32, 18, 23, 39, 14, 48, 42, 65, 27, 55, 45, 51, 34,  6,
       37, 52, 44, 54,  1,  7, 60, 20, 25, 40, 13, 16, 10, 12, 17, 35, 28,
       43, 57,  5, 36, 41, 24, 19, 58, 61, 53,  2, 15, 26, 56, 46, 62, 38,
       63, 22, 64,  0,  3, 11, 33, 47, 50, 21,  8, 29, 59,  9, 30],
      dtype=int64), 'cur_cost': 103961.0}, {'tour': [0, 3, 16, 5, 4, 8, 2, 6, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12789.0}, {'tour': [0, 11, 15, 1, 7, 3, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12675.0}, {'tour': array([62, 50, 38,  9, 64, 58, 48, 60, 44, 40, 11, 39, 56,  5, 21, 20, 26,
       36, 47,  7, 22, 28, 37, 57,  1, 32, 16, 34, 15, 27, 24, 14, 54, 63,
        6,  2, 43, 46, 51, 10,  0, 13, 17, 31, 61, 19,  3, 41, 42, 52, 49,
       23, 55, 53, 29, 59, 18, 45, 30, 65, 25,  4, 33, 12, 35,  8],
      dtype=int64), 'cur_cost': 100859.0}, {'tour': [0, 14, 8, 1, 7, 3, 9, 11, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12708.0}, {'tour': [0, 1, 6, 7, 3, 9, 11, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10385.0}, {'tour': array([40, 62, 63, 36, 43, 49, 65,  8, 50, 28, 24, 20, 15, 39, 42,  1, 14,
       55, 29,  4, 37,  9, 32, 53, 54, 51, 17,  5, 25, 56, 31, 38, 30, 22,
       34,  3, 16,  0, 21, 27, 33, 60, 18, 52, 44, 45, 59, 19,  6, 57, 13,
        7, 48, 61, 12, 64, 10, 26, 11, 41, 47, 23, 35, 58, 46,  2],
      dtype=int64), 'cur_cost': 116076.0}, {'tour': array([22, 24, 42, 19, 65, 61, 48, 46, 27, 51, 32, 44, 41, 57, 25,  0, 52,
       35,  8, 56, 33,  7, 11, 60,  4, 50, 53, 49, 43,  1, 14, 34, 15, 18,
       23, 20, 37,  6,  5, 58, 28, 17,  3, 30, 54,  2, 45, 62, 55, 10, 36,
       29, 38, 64, 26, 47, 63, 16,  9, 39, 40, 12, 13, 31, 59, 21],
      dtype=int64), 'cur_cost': 104947.0}, {'tour': array([31, 12, 34, 58, 24, 55, 33, 61, 26,  8, 21,  9, 49, 57, 65, 50, 38,
       40, 47, 48, 16, 37, 17, 18,  6, 63, 64,  0,  5,  3, 45, 44, 20, 19,
       42, 41, 43, 28, 39, 62, 46, 59, 36, 32, 22, 53, 56,  2,  7, 15, 51,
       11, 54,  4, 60, 14, 13, 52, 29, 35, 10,  1, 30, 25, 27, 23],
      dtype=int64), 'cur_cost': 87390.0}, {'tour': array([26, 64, 56, 58, 19,  3,  1, 23, 41, 33,  7, 52, 16, 61,  6, 27,  9,
       28, 43, 25, 35, 51, 57, 14, 17, 20, 15, 48, 44, 10, 45, 65, 24, 38,
       47, 42,  5,  8,  2, 40, 39, 13, 32, 21, 50, 53, 31, 60,  4, 12, 22,
       36, 59, 29, 62, 55, 34, 63, 30, 18, 49,  0, 54, 11, 37, 46],
      dtype=int64), 'cur_cost': 104682.0}, {'tour': array([19, 58, 50, 38,  3, 12, 27, 43, 56, 47, 15, 31, 36, 14, 53, 57, 16,
       21, 61,  4, 22, 60, 46, 48, 55, 11, 23, 35, 10, 49,  5, 29, 13, 32,
       33,  6,  7, 39,  2, 40, 18, 65, 34, 41, 26, 51, 52, 25,  1,  8, 45,
       44, 20,  9, 24, 28, 64, 30, 59, 54, 17, 62, 37,  0, 42, 63],
      dtype=int64), 'cur_cost': 114468.0}, {'tour': array([16,  7, 24, 22, 57, 54, 55, 41, 46,  5, 38, 39, 61, 59,  6,  9,  0,
       47, 31, 26, 40, 52,  1, 30, 29, 28, 15, 17, 33, 25, 34, 21, 49,  8,
       35, 23, 36, 20, 43, 63, 64, 51, 56, 13, 44, 37, 14, 32, 11,  2, 10,
        3, 27, 65, 60, 42, 45, 18, 53, 62, 19, 12, 58, 48,  4, 50],
      dtype=int64), 'cur_cost': 88600.0}, {'tour': array([65, 19,  7, 12, 45, 56,  5, 30, 51, 10,  4, 42, 24, 31, 11, 39, 25,
        1, 52, 57,  6, 29, 43, 54, 22, 27, 64, 46, 50, 40,  9, 35, 20, 58,
       13, 16, 41, 14, 23,  2,  8, 15, 47, 53, 61, 37, 18, 26,  3, 44, 62,
        0, 17, 49, 32, 60, 21, 63, 36, 34, 55, 38, 59, 48, 28, 33],
      dtype=int64), 'cur_cost': 111728.0}, {'tour': array([54, 10, 48, 43, 31,  0, 23, 45, 46, 28, 62, 12, 56,  6, 41, 24, 19,
       60, 25,  7, 16, 34,  9,  5, 40, 55, 15,  1, 42, 52, 32, 20,  3, 27,
       39, 14, 59, 53, 30, 21, 29, 47, 50, 26,  2, 17, 57, 44, 11, 51, 38,
        4, 61, 37, 22,  8, 58, 65, 63, 36, 35, 18, 49, 13, 64, 33],
      dtype=int64), 'cur_cost': 111745.0}, {'tour': array([25, 54, 49, 56, 29, 13, 28, 47, 55, 18, 62,  9, 14, 35, 48,  5, 19,
       17,  7, 65, 37, 21, 38, 63, 58, 39, 24, 64, 23, 11, 45, 60, 50, 36,
       42,  3, 15, 31, 10, 46, 16, 44, 51, 12, 61, 34, 27, 52,  2, 40, 22,
       53,  8, 43,  1, 32, 57, 33, 41,  0, 30, 59, 26, 20,  4,  6],
      dtype=int64), 'cur_cost': 125967.0}, {'tour': array([11, 38, 17, 41, 14,  8, 56, 16, 20, 22, 64, 34, 26, 65,  3, 58, 30,
       42,  5, 61, 18, 52, 50,  7,  2,  0, 63, 46, 29, 31, 10, 60, 25, 28,
       21, 32, 36, 47, 55, 39, 40, 44, 49, 19,  1, 57,  6, 53, 59, 51, 62,
       13,  4, 48, 12, 23, 35, 24, 37,  9, 43, 45, 27, 15, 54, 33],
      dtype=int64), 'cur_cost': 96444.0}, {'tour': array([ 3, 14,  4, 30, 56, 20, 49, 33, 44, 63,  2,  0, 38, 35, 25, 42, 45,
       23, 57,  1, 51, 48, 62,  6, 15, 27, 43, 29, 19, 11, 59, 50, 53, 37,
       24, 46,  5, 40, 34, 28, 47, 17, 18, 61, 41, 32, 13, 31, 12, 26, 52,
       22,  8,  9, 16, 10, 64, 58, 36, 39, 21, 60, 65, 55,  7, 54],
      dtype=int64), 'cur_cost': 101412.0}, {'tour': array([45, 20, 19, 59, 15, 21, 25, 35,  6, 54, 22, 43, 55, 12,  1,  3, 62,
       39, 30, 10, 51, 18,  9, 33, 14, 17, 28, 11,  5, 13, 56, 47, 26, 37,
        4, 32, 34, 27, 36, 23, 64, 44, 46, 49, 61, 40, 24,  0, 63,  8, 41,
       48, 31,  2, 52, 57, 53, 65,  7, 60, 42, 58, 38, 16, 50, 29],
      dtype=int64), 'cur_cost': 94563.0}, {'tour': array([47, 48, 23, 59, 37,  1, 60, 44, 25, 26, 11,  0,  4, 19, 34, 32, 28,
       30,  5, 24, 43, 13,  8,  2, 14, 17, 57, 21, 16, 45, 15, 22, 29, 33,
       36, 42, 12,  9, 20,  7, 39,  6, 40, 56, 38, 10, 51, 65, 64, 49, 41,
       18, 62, 63, 58, 55, 46, 35, 27,  3, 61, 53, 31, 54, 50, 52],
      dtype=int64), 'cur_cost': 94284.0}]
2025-08-03 16:31:22,146 - ExploitationExpert - INFO - 局部搜索耗时: 0.61秒
2025-08-03 16:31:22,147 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-03 16:31:22,149 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([40, 62, 63, 36, 43, 49, 65,  8, 50, 28, 24, 20, 15, 39, 42,  1, 14,
       55, 29,  4, 37,  9, 32, 53, 54, 51, 17,  5, 25, 56, 31, 38, 30, 22,
       34,  3, 16,  0, 21, 27, 33, 60, 18, 52, 44, 45, 59, 19,  6, 57, 13,
        7, 48, 61, 12, 64, 10, 26, 11, 41, 47, 23, 35, 58, 46,  2],
      dtype=int64), 'cur_cost': 116076.0}
2025-08-03 16:31:22,150 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 116076.00)
2025-08-03 16:31:22,152 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-03 16:31:22,154 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-03 16:31:22,154 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:31:22,160 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:31:22,161 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:31:22,161 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12822.0, 路径长度: 66
2025-08-03 16:31:22,161 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 8, 18, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 19, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 9, 11, 7, 3, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12822.0}
2025-08-03 16:31:22,162 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 12822.00)
2025-08-03 16:31:22,163 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-03 16:31:22,163 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-03 16:31:22,163 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:31:22,170 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:31:22,171 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:31:22,171 - ExplorationExpert - INFO - 探索路径生成完成，成本: 94130.0, 路径长度: 66
2025-08-03 16:31:22,171 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [16, 25, 11, 37, 27, 2, 58, 12, 13, 38, 48, 28, 57, 5, 63, 40, 29, 6, 8, 4, 3, 45, 18, 30, 65, 17, 46, 36, 20, 39, 1, 59, 55, 60, 34, 31, 32, 47, 33, 7, 64, 21, 53, 56, 44, 43, 42, 35, 41, 54, 24, 50, 62, 0, 9, 14, 10, 49, 26, 61, 52, 22, 51, 23, 19, 15], 'cur_cost': 94130.0}
2025-08-03 16:31:22,172 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 94130.00)
2025-08-03 16:31:22,172 - experts.management.collaboration_manager - INFO - 为个体 10 生成利用路径
2025-08-03 16:31:22,172 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:31:22,173 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:31:22,173 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 10 处的路径，新成本: 109087.0
2025-08-03 16:31:22,268 - ExploitationExpert - INFO - res_population_num: 6
2025-08-03 16:31:22,268 - ExploitationExpert - INFO - res_population_costs: [9833.0, 9607.0, 9587.0, 9534.0, 9532, 9521]
2025-08-03 16:31:22,268 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 41, 50, 51,
       38, 45, 44, 39, 47, 46, 48, 43, 40, 49, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 42, 46,
       48, 49, 47, 39, 44, 45, 50, 51, 38, 41, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 59, 62, 64, 57,
       54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 27, 37, 36, 26,
       25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:31:22,273 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:31:22,273 - ExploitationExpert - INFO - populations: [{'tour': [0, 4, 19, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12886.0}, {'tour': array([31,  4, 49, 32, 18, 23, 39, 14, 48, 42, 65, 27, 55, 45, 51, 34,  6,
       37, 52, 44, 54,  1,  7, 60, 20, 25, 40, 13, 16, 10, 12, 17, 35, 28,
       43, 57,  5, 36, 41, 24, 19, 58, 61, 53,  2, 15, 26, 56, 46, 62, 38,
       63, 22, 64,  0,  3, 11, 33, 47, 50, 21,  8, 29, 59,  9, 30],
      dtype=int64), 'cur_cost': 103961.0}, {'tour': [0, 3, 16, 5, 4, 8, 2, 6, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12789.0}, {'tour': [0, 11, 15, 1, 7, 3, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12675.0}, {'tour': array([62, 50, 38,  9, 64, 58, 48, 60, 44, 40, 11, 39, 56,  5, 21, 20, 26,
       36, 47,  7, 22, 28, 37, 57,  1, 32, 16, 34, 15, 27, 24, 14, 54, 63,
        6,  2, 43, 46, 51, 10,  0, 13, 17, 31, 61, 19,  3, 41, 42, 52, 49,
       23, 55, 53, 29, 59, 18, 45, 30, 65, 25,  4, 33, 12, 35,  8],
      dtype=int64), 'cur_cost': 100859.0}, {'tour': [0, 14, 8, 1, 7, 3, 9, 11, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12708.0}, {'tour': [0, 1, 6, 7, 3, 9, 11, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10385.0}, {'tour': array([40, 62, 63, 36, 43, 49, 65,  8, 50, 28, 24, 20, 15, 39, 42,  1, 14,
       55, 29,  4, 37,  9, 32, 53, 54, 51, 17,  5, 25, 56, 31, 38, 30, 22,
       34,  3, 16,  0, 21, 27, 33, 60, 18, 52, 44, 45, 59, 19,  6, 57, 13,
        7, 48, 61, 12, 64, 10, 26, 11, 41, 47, 23, 35, 58, 46,  2],
      dtype=int64), 'cur_cost': 116076.0}, {'tour': [0, 8, 18, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 19, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 9, 11, 7, 3, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12822.0}, {'tour': [16, 25, 11, 37, 27, 2, 58, 12, 13, 38, 48, 28, 57, 5, 63, 40, 29, 6, 8, 4, 3, 45, 18, 30, 65, 17, 46, 36, 20, 39, 1, 59, 55, 60, 34, 31, 32, 47, 33, 7, 64, 21, 53, 56, 44, 43, 42, 35, 41, 54, 24, 50, 62, 0, 9, 14, 10, 49, 26, 61, 52, 22, 51, 23, 19, 15], 'cur_cost': 94130.0}, {'tour': array([34, 55, 14, 37, 52,  7, 50, 19,  2, 61, 15, 43, 40, 18, 62, 33, 44,
       53, 65, 60,  1, 57, 11, 25, 28, 12, 49, 51, 29, 56, 48,  3, 35, 59,
       45, 32, 54,  9, 24, 22, 26, 13, 58, 16, 17, 27, 46, 64, 23, 30, 42,
       63,  4, 38,  8, 31,  6, 10,  5, 36, 41, 20, 47, 39,  0, 21],
      dtype=int64), 'cur_cost': 109087.0}, {'tour': array([19, 58, 50, 38,  3, 12, 27, 43, 56, 47, 15, 31, 36, 14, 53, 57, 16,
       21, 61,  4, 22, 60, 46, 48, 55, 11, 23, 35, 10, 49,  5, 29, 13, 32,
       33,  6,  7, 39,  2, 40, 18, 65, 34, 41, 26, 51, 52, 25,  1,  8, 45,
       44, 20,  9, 24, 28, 64, 30, 59, 54, 17, 62, 37,  0, 42, 63],
      dtype=int64), 'cur_cost': 114468.0}, {'tour': array([16,  7, 24, 22, 57, 54, 55, 41, 46,  5, 38, 39, 61, 59,  6,  9,  0,
       47, 31, 26, 40, 52,  1, 30, 29, 28, 15, 17, 33, 25, 34, 21, 49,  8,
       35, 23, 36, 20, 43, 63, 64, 51, 56, 13, 44, 37, 14, 32, 11,  2, 10,
        3, 27, 65, 60, 42, 45, 18, 53, 62, 19, 12, 58, 48,  4, 50],
      dtype=int64), 'cur_cost': 88600.0}, {'tour': array([65, 19,  7, 12, 45, 56,  5, 30, 51, 10,  4, 42, 24, 31, 11, 39, 25,
        1, 52, 57,  6, 29, 43, 54, 22, 27, 64, 46, 50, 40,  9, 35, 20, 58,
       13, 16, 41, 14, 23,  2,  8, 15, 47, 53, 61, 37, 18, 26,  3, 44, 62,
        0, 17, 49, 32, 60, 21, 63, 36, 34, 55, 38, 59, 48, 28, 33],
      dtype=int64), 'cur_cost': 111728.0}, {'tour': array([54, 10, 48, 43, 31,  0, 23, 45, 46, 28, 62, 12, 56,  6, 41, 24, 19,
       60, 25,  7, 16, 34,  9,  5, 40, 55, 15,  1, 42, 52, 32, 20,  3, 27,
       39, 14, 59, 53, 30, 21, 29, 47, 50, 26,  2, 17, 57, 44, 11, 51, 38,
        4, 61, 37, 22,  8, 58, 65, 63, 36, 35, 18, 49, 13, 64, 33],
      dtype=int64), 'cur_cost': 111745.0}, {'tour': array([25, 54, 49, 56, 29, 13, 28, 47, 55, 18, 62,  9, 14, 35, 48,  5, 19,
       17,  7, 65, 37, 21, 38, 63, 58, 39, 24, 64, 23, 11, 45, 60, 50, 36,
       42,  3, 15, 31, 10, 46, 16, 44, 51, 12, 61, 34, 27, 52,  2, 40, 22,
       53,  8, 43,  1, 32, 57, 33, 41,  0, 30, 59, 26, 20,  4,  6],
      dtype=int64), 'cur_cost': 125967.0}, {'tour': array([11, 38, 17, 41, 14,  8, 56, 16, 20, 22, 64, 34, 26, 65,  3, 58, 30,
       42,  5, 61, 18, 52, 50,  7,  2,  0, 63, 46, 29, 31, 10, 60, 25, 28,
       21, 32, 36, 47, 55, 39, 40, 44, 49, 19,  1, 57,  6, 53, 59, 51, 62,
       13,  4, 48, 12, 23, 35, 24, 37,  9, 43, 45, 27, 15, 54, 33],
      dtype=int64), 'cur_cost': 96444.0}, {'tour': array([ 3, 14,  4, 30, 56, 20, 49, 33, 44, 63,  2,  0, 38, 35, 25, 42, 45,
       23, 57,  1, 51, 48, 62,  6, 15, 27, 43, 29, 19, 11, 59, 50, 53, 37,
       24, 46,  5, 40, 34, 28, 47, 17, 18, 61, 41, 32, 13, 31, 12, 26, 52,
       22,  8,  9, 16, 10, 64, 58, 36, 39, 21, 60, 65, 55,  7, 54],
      dtype=int64), 'cur_cost': 101412.0}, {'tour': array([45, 20, 19, 59, 15, 21, 25, 35,  6, 54, 22, 43, 55, 12,  1,  3, 62,
       39, 30, 10, 51, 18,  9, 33, 14, 17, 28, 11,  5, 13, 56, 47, 26, 37,
        4, 32, 34, 27, 36, 23, 64, 44, 46, 49, 61, 40, 24,  0, 63,  8, 41,
       48, 31,  2, 52, 57, 53, 65,  7, 60, 42, 58, 38, 16, 50, 29],
      dtype=int64), 'cur_cost': 94563.0}, {'tour': array([47, 48, 23, 59, 37,  1, 60, 44, 25, 26, 11,  0,  4, 19, 34, 32, 28,
       30,  5, 24, 43, 13,  8,  2, 14, 17, 57, 21, 16, 45, 15, 22, 29, 33,
       36, 42, 12,  9, 20,  7, 39,  6, 40, 56, 38, 10, 51, 65, 64, 49, 41,
       18, 62, 63, 58, 55, 46, 35, 27,  3, 61, 53, 31, 54, 50, 52],
      dtype=int64), 'cur_cost': 94284.0}]
2025-08-03 16:31:22,283 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒
2025-08-03 16:31:22,284 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-03 16:31:22,285 - experts.management.collaboration_manager - INFO - 个体 10 利用路径生成报告: {'new_tour': array([34, 55, 14, 37, 52,  7, 50, 19,  2, 61, 15, 43, 40, 18, 62, 33, 44,
       53, 65, 60,  1, 57, 11, 25, 28, 12, 49, 51, 29, 56, 48,  3, 35, 59,
       45, 32, 54,  9, 24, 22, 26, 13, 58, 16, 17, 27, 46, 64, 23, 30, 42,
       63,  4, 38,  8, 31,  6, 10,  5, 36, 41, 20, 47, 39,  0, 21],
      dtype=int64), 'cur_cost': 109087.0}
2025-08-03 16:31:22,286 - experts.management.collaboration_manager - INFO - 个体 10 接受新路径 (成本: 109087.00)
2025-08-03 16:31:22,286 - experts.management.collaboration_manager - INFO - 为个体 11 生成探索路径
2025-08-03 16:31:22,287 - ExplorationExpert - INFO - 开始为个体 11 生成探索路径（算法实现）
2025-08-03 16:31:22,287 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:31:22,303 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:31:22,303 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:31:22,304 - ExplorationExpert - INFO - 探索路径生成完成，成本: 63093.0, 路径长度: 66
2025-08-03 16:31:22,304 - experts.management.collaboration_manager - INFO - 个体 11 探索路径生成报告: {'new_tour': [23, 15, 31, 22, 27, 7, 17, 34, 33, 3, 14, 30, 11, 55, 62, 12, 13, 19, 43, 20, 1, 8, 59, 0, 16, 10, 54, 52, 65, 18, 24, 49, 39, 37, 32, 36, 35, 46, 28, 2, 58, 57, 6, 56, 60, 5, 25, 40, 48, 50, 51, 44, 47, 41, 38, 26, 29, 21, 42, 9, 61, 53, 63, 64, 4, 45], 'cur_cost': 63093.0}
2025-08-03 16:31:22,305 - experts.management.collaboration_manager - INFO - 个体 11 接受新路径 (成本: 63093.00)
2025-08-03 16:31:22,305 - experts.management.collaboration_manager - INFO - 为个体 12 生成探索路径
2025-08-03 16:31:22,305 - ExplorationExpert - INFO - 开始为个体 12 生成探索路径（算法实现）
2025-08-03 16:31:22,305 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:31:22,309 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:31:22,310 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:31:22,310 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14697.0, 路径长度: 66
2025-08-03 16:31:22,310 - experts.management.collaboration_manager - INFO - 个体 12 探索路径生成报告: {'new_tour': [0, 24, 17, 26, 25, 31, 33, 28, 30, 35, 34, 36, 37, 27, 29, 32, 18, 16, 23, 22, 12, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14697.0}
2025-08-03 16:31:22,311 - experts.management.collaboration_manager - INFO - 个体 12 接受新路径 (成本: 14697.00)
2025-08-03 16:31:22,311 - experts.management.collaboration_manager - INFO - 为个体 13 生成利用路径
2025-08-03 16:31:22,311 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:31:22,311 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:31:22,312 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 13 处的路径，新成本: 114366.0
2025-08-03 16:31:22,393 - ExploitationExpert - INFO - res_population_num: 6
2025-08-03 16:31:22,394 - ExploitationExpert - INFO - res_population_costs: [9833.0, 9607.0, 9587.0, 9534.0, 9532, 9521]
2025-08-03 16:31:22,394 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 41, 50, 51,
       38, 45, 44, 39, 47, 46, 48, 43, 40, 49, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 42, 46,
       48, 49, 47, 39, 44, 45, 50, 51, 38, 41, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 59, 62, 64, 57,
       54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 27, 37, 36, 26,
       25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:31:22,398 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:31:22,398 - ExploitationExpert - INFO - populations: [{'tour': [0, 4, 19, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12886.0}, {'tour': array([31,  4, 49, 32, 18, 23, 39, 14, 48, 42, 65, 27, 55, 45, 51, 34,  6,
       37, 52, 44, 54,  1,  7, 60, 20, 25, 40, 13, 16, 10, 12, 17, 35, 28,
       43, 57,  5, 36, 41, 24, 19, 58, 61, 53,  2, 15, 26, 56, 46, 62, 38,
       63, 22, 64,  0,  3, 11, 33, 47, 50, 21,  8, 29, 59,  9, 30],
      dtype=int64), 'cur_cost': 103961.0}, {'tour': [0, 3, 16, 5, 4, 8, 2, 6, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12789.0}, {'tour': [0, 11, 15, 1, 7, 3, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12675.0}, {'tour': array([62, 50, 38,  9, 64, 58, 48, 60, 44, 40, 11, 39, 56,  5, 21, 20, 26,
       36, 47,  7, 22, 28, 37, 57,  1, 32, 16, 34, 15, 27, 24, 14, 54, 63,
        6,  2, 43, 46, 51, 10,  0, 13, 17, 31, 61, 19,  3, 41, 42, 52, 49,
       23, 55, 53, 29, 59, 18, 45, 30, 65, 25,  4, 33, 12, 35,  8],
      dtype=int64), 'cur_cost': 100859.0}, {'tour': [0, 14, 8, 1, 7, 3, 9, 11, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12708.0}, {'tour': [0, 1, 6, 7, 3, 9, 11, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10385.0}, {'tour': array([40, 62, 63, 36, 43, 49, 65,  8, 50, 28, 24, 20, 15, 39, 42,  1, 14,
       55, 29,  4, 37,  9, 32, 53, 54, 51, 17,  5, 25, 56, 31, 38, 30, 22,
       34,  3, 16,  0, 21, 27, 33, 60, 18, 52, 44, 45, 59, 19,  6, 57, 13,
        7, 48, 61, 12, 64, 10, 26, 11, 41, 47, 23, 35, 58, 46,  2],
      dtype=int64), 'cur_cost': 116076.0}, {'tour': [0, 8, 18, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 19, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 9, 11, 7, 3, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12822.0}, {'tour': [16, 25, 11, 37, 27, 2, 58, 12, 13, 38, 48, 28, 57, 5, 63, 40, 29, 6, 8, 4, 3, 45, 18, 30, 65, 17, 46, 36, 20, 39, 1, 59, 55, 60, 34, 31, 32, 47, 33, 7, 64, 21, 53, 56, 44, 43, 42, 35, 41, 54, 24, 50, 62, 0, 9, 14, 10, 49, 26, 61, 52, 22, 51, 23, 19, 15], 'cur_cost': 94130.0}, {'tour': array([34, 55, 14, 37, 52,  7, 50, 19,  2, 61, 15, 43, 40, 18, 62, 33, 44,
       53, 65, 60,  1, 57, 11, 25, 28, 12, 49, 51, 29, 56, 48,  3, 35, 59,
       45, 32, 54,  9, 24, 22, 26, 13, 58, 16, 17, 27, 46, 64, 23, 30, 42,
       63,  4, 38,  8, 31,  6, 10,  5, 36, 41, 20, 47, 39,  0, 21],
      dtype=int64), 'cur_cost': 109087.0}, {'tour': [23, 15, 31, 22, 27, 7, 17, 34, 33, 3, 14, 30, 11, 55, 62, 12, 13, 19, 43, 20, 1, 8, 59, 0, 16, 10, 54, 52, 65, 18, 24, 49, 39, 37, 32, 36, 35, 46, 28, 2, 58, 57, 6, 56, 60, 5, 25, 40, 48, 50, 51, 44, 47, 41, 38, 26, 29, 21, 42, 9, 61, 53, 63, 64, 4, 45], 'cur_cost': 63093.0}, {'tour': [0, 24, 17, 26, 25, 31, 33, 28, 30, 35, 34, 36, 37, 27, 29, 32, 18, 16, 23, 22, 12, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14697.0}, {'tour': array([29, 10, 63, 32, 36, 48, 65, 30, 43,  2, 16, 17, 55, 42, 64, 49, 59,
        3, 54, 37, 51, 12, 21, 28,  1, 26,  6, 61, 31, 15, 44, 47,  7, 24,
       39,  8,  5, 56, 13, 23, 27, 40, 22, 50, 11, 57, 34, 46, 20, 25, 19,
       38, 35, 14, 18,  0, 60, 41, 58, 33, 53, 62, 45,  4, 52,  9],
      dtype=int64), 'cur_cost': 114366.0}, {'tour': array([54, 10, 48, 43, 31,  0, 23, 45, 46, 28, 62, 12, 56,  6, 41, 24, 19,
       60, 25,  7, 16, 34,  9,  5, 40, 55, 15,  1, 42, 52, 32, 20,  3, 27,
       39, 14, 59, 53, 30, 21, 29, 47, 50, 26,  2, 17, 57, 44, 11, 51, 38,
        4, 61, 37, 22,  8, 58, 65, 63, 36, 35, 18, 49, 13, 64, 33],
      dtype=int64), 'cur_cost': 111745.0}, {'tour': array([25, 54, 49, 56, 29, 13, 28, 47, 55, 18, 62,  9, 14, 35, 48,  5, 19,
       17,  7, 65, 37, 21, 38, 63, 58, 39, 24, 64, 23, 11, 45, 60, 50, 36,
       42,  3, 15, 31, 10, 46, 16, 44, 51, 12, 61, 34, 27, 52,  2, 40, 22,
       53,  8, 43,  1, 32, 57, 33, 41,  0, 30, 59, 26, 20,  4,  6],
      dtype=int64), 'cur_cost': 125967.0}, {'tour': array([11, 38, 17, 41, 14,  8, 56, 16, 20, 22, 64, 34, 26, 65,  3, 58, 30,
       42,  5, 61, 18, 52, 50,  7,  2,  0, 63, 46, 29, 31, 10, 60, 25, 28,
       21, 32, 36, 47, 55, 39, 40, 44, 49, 19,  1, 57,  6, 53, 59, 51, 62,
       13,  4, 48, 12, 23, 35, 24, 37,  9, 43, 45, 27, 15, 54, 33],
      dtype=int64), 'cur_cost': 96444.0}, {'tour': array([ 3, 14,  4, 30, 56, 20, 49, 33, 44, 63,  2,  0, 38, 35, 25, 42, 45,
       23, 57,  1, 51, 48, 62,  6, 15, 27, 43, 29, 19, 11, 59, 50, 53, 37,
       24, 46,  5, 40, 34, 28, 47, 17, 18, 61, 41, 32, 13, 31, 12, 26, 52,
       22,  8,  9, 16, 10, 64, 58, 36, 39, 21, 60, 65, 55,  7, 54],
      dtype=int64), 'cur_cost': 101412.0}, {'tour': array([45, 20, 19, 59, 15, 21, 25, 35,  6, 54, 22, 43, 55, 12,  1,  3, 62,
       39, 30, 10, 51, 18,  9, 33, 14, 17, 28, 11,  5, 13, 56, 47, 26, 37,
        4, 32, 34, 27, 36, 23, 64, 44, 46, 49, 61, 40, 24,  0, 63,  8, 41,
       48, 31,  2, 52, 57, 53, 65,  7, 60, 42, 58, 38, 16, 50, 29],
      dtype=int64), 'cur_cost': 94563.0}, {'tour': array([47, 48, 23, 59, 37,  1, 60, 44, 25, 26, 11,  0,  4, 19, 34, 32, 28,
       30,  5, 24, 43, 13,  8,  2, 14, 17, 57, 21, 16, 45, 15, 22, 29, 33,
       36, 42, 12,  9, 20,  7, 39,  6, 40, 56, 38, 10, 51, 65, 64, 49, 41,
       18, 62, 63, 58, 55, 46, 35, 27,  3, 61, 53, 31, 54, 50, 52],
      dtype=int64), 'cur_cost': 94284.0}]
2025-08-03 16:31:22,406 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-03 16:31:22,406 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-03 16:31:22,407 - experts.management.collaboration_manager - INFO - 个体 13 利用路径生成报告: {'new_tour': array([29, 10, 63, 32, 36, 48, 65, 30, 43,  2, 16, 17, 55, 42, 64, 49, 59,
        3, 54, 37, 51, 12, 21, 28,  1, 26,  6, 61, 31, 15, 44, 47,  7, 24,
       39,  8,  5, 56, 13, 23, 27, 40, 22, 50, 11, 57, 34, 46, 20, 25, 19,
       38, 35, 14, 18,  0, 60, 41, 58, 33, 53, 62, 45,  4, 52,  9],
      dtype=int64), 'cur_cost': 114366.0}
2025-08-03 16:31:22,407 - experts.management.collaboration_manager - INFO - 个体 13 接受新路径 (成本: 114366.00)
2025-08-03 16:31:22,408 - experts.management.collaboration_manager - INFO - 为个体 14 生成探索路径
2025-08-03 16:31:22,408 - ExplorationExpert - INFO - 开始为个体 14 生成探索路径（算法实现）
2025-08-03 16:31:22,408 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:31:22,426 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:31:22,427 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:31:22,428 - ExplorationExpert - INFO - 探索路径生成完成，成本: 64428.0, 路径长度: 66
2025-08-03 16:31:22,428 - experts.management.collaboration_manager - INFO - 个体 14 探索路径生成报告: {'new_tour': [22, 27, 25, 0, 7, 9, 62, 23, 4, 18, 5, 1, 60, 54, 58, 65, 15, 35, 36, 33, 21, 19, 6, 3, 13, 37, 20, 49, 45, 14, 31, 16, 30, 8, 56, 63, 47, 41, 50, 39, 34, 17, 43, 24, 12, 28, 46, 44, 26, 10, 59, 55, 52, 61, 57, 11, 2, 53, 40, 38, 42, 51, 32, 29, 48, 64], 'cur_cost': 64428.0}
2025-08-03 16:31:22,429 - experts.management.collaboration_manager - INFO - 个体 14 接受新路径 (成本: 64428.00)
2025-08-03 16:31:22,430 - experts.management.collaboration_manager - INFO - 为个体 15 生成探索路径
2025-08-03 16:31:22,430 - ExplorationExpert - INFO - 开始为个体 15 生成探索路径（算法实现）
2025-08-03 16:31:22,431 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:31:22,445 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:31:22,446 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:31:22,447 - ExplorationExpert - INFO - 探索路径生成完成，成本: 64047.0, 路径长度: 66
2025-08-03 16:31:22,449 - experts.management.collaboration_manager - INFO - 个体 15 探索路径生成报告: {'new_tour': [51, 17, 23, 3, 64, 5, 22, 31, 11, 14, 6, 60, 8, 15, 29, 2, 54, 0, 57, 52, 20, 21, 4, 58, 63, 7, 61, 19, 16, 32, 10, 1, 33, 27, 25, 13, 43, 41, 47, 42, 34, 37, 49, 12, 40, 50, 36, 35, 9, 65, 53, 59, 55, 48, 39, 18, 26, 28, 30, 24, 46, 45, 38, 44, 56, 62], 'cur_cost': 64047.0}
2025-08-03 16:31:22,450 - experts.management.collaboration_manager - INFO - 个体 15 接受新路径 (成本: 64047.00)
2025-08-03 16:31:22,451 - experts.management.collaboration_manager - INFO - 为个体 16 生成利用路径
2025-08-03 16:31:22,452 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:31:22,453 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:31:22,454 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 16 处的路径，新成本: 128312.0
2025-08-03 16:31:22,536 - ExploitationExpert - INFO - res_population_num: 6
2025-08-03 16:31:22,536 - ExploitationExpert - INFO - res_population_costs: [9833.0, 9607.0, 9587.0, 9534.0, 9532, 9521]
2025-08-03 16:31:22,536 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 41, 50, 51,
       38, 45, 44, 39, 47, 46, 48, 43, 40, 49, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 42, 46,
       48, 49, 47, 39, 44, 45, 50, 51, 38, 41, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 59, 62, 64, 57,
       54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 27, 37, 36, 26,
       25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:31:22,540 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:31:22,540 - ExploitationExpert - INFO - populations: [{'tour': [0, 4, 19, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12886.0}, {'tour': array([31,  4, 49, 32, 18, 23, 39, 14, 48, 42, 65, 27, 55, 45, 51, 34,  6,
       37, 52, 44, 54,  1,  7, 60, 20, 25, 40, 13, 16, 10, 12, 17, 35, 28,
       43, 57,  5, 36, 41, 24, 19, 58, 61, 53,  2, 15, 26, 56, 46, 62, 38,
       63, 22, 64,  0,  3, 11, 33, 47, 50, 21,  8, 29, 59,  9, 30],
      dtype=int64), 'cur_cost': 103961.0}, {'tour': [0, 3, 16, 5, 4, 8, 2, 6, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12789.0}, {'tour': [0, 11, 15, 1, 7, 3, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12675.0}, {'tour': array([62, 50, 38,  9, 64, 58, 48, 60, 44, 40, 11, 39, 56,  5, 21, 20, 26,
       36, 47,  7, 22, 28, 37, 57,  1, 32, 16, 34, 15, 27, 24, 14, 54, 63,
        6,  2, 43, 46, 51, 10,  0, 13, 17, 31, 61, 19,  3, 41, 42, 52, 49,
       23, 55, 53, 29, 59, 18, 45, 30, 65, 25,  4, 33, 12, 35,  8],
      dtype=int64), 'cur_cost': 100859.0}, {'tour': [0, 14, 8, 1, 7, 3, 9, 11, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12708.0}, {'tour': [0, 1, 6, 7, 3, 9, 11, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10385.0}, {'tour': array([40, 62, 63, 36, 43, 49, 65,  8, 50, 28, 24, 20, 15, 39, 42,  1, 14,
       55, 29,  4, 37,  9, 32, 53, 54, 51, 17,  5, 25, 56, 31, 38, 30, 22,
       34,  3, 16,  0, 21, 27, 33, 60, 18, 52, 44, 45, 59, 19,  6, 57, 13,
        7, 48, 61, 12, 64, 10, 26, 11, 41, 47, 23, 35, 58, 46,  2],
      dtype=int64), 'cur_cost': 116076.0}, {'tour': [0, 8, 18, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 19, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 9, 11, 7, 3, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12822.0}, {'tour': [16, 25, 11, 37, 27, 2, 58, 12, 13, 38, 48, 28, 57, 5, 63, 40, 29, 6, 8, 4, 3, 45, 18, 30, 65, 17, 46, 36, 20, 39, 1, 59, 55, 60, 34, 31, 32, 47, 33, 7, 64, 21, 53, 56, 44, 43, 42, 35, 41, 54, 24, 50, 62, 0, 9, 14, 10, 49, 26, 61, 52, 22, 51, 23, 19, 15], 'cur_cost': 94130.0}, {'tour': array([34, 55, 14, 37, 52,  7, 50, 19,  2, 61, 15, 43, 40, 18, 62, 33, 44,
       53, 65, 60,  1, 57, 11, 25, 28, 12, 49, 51, 29, 56, 48,  3, 35, 59,
       45, 32, 54,  9, 24, 22, 26, 13, 58, 16, 17, 27, 46, 64, 23, 30, 42,
       63,  4, 38,  8, 31,  6, 10,  5, 36, 41, 20, 47, 39,  0, 21],
      dtype=int64), 'cur_cost': 109087.0}, {'tour': [23, 15, 31, 22, 27, 7, 17, 34, 33, 3, 14, 30, 11, 55, 62, 12, 13, 19, 43, 20, 1, 8, 59, 0, 16, 10, 54, 52, 65, 18, 24, 49, 39, 37, 32, 36, 35, 46, 28, 2, 58, 57, 6, 56, 60, 5, 25, 40, 48, 50, 51, 44, 47, 41, 38, 26, 29, 21, 42, 9, 61, 53, 63, 64, 4, 45], 'cur_cost': 63093.0}, {'tour': [0, 24, 17, 26, 25, 31, 33, 28, 30, 35, 34, 36, 37, 27, 29, 32, 18, 16, 23, 22, 12, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14697.0}, {'tour': array([29, 10, 63, 32, 36, 48, 65, 30, 43,  2, 16, 17, 55, 42, 64, 49, 59,
        3, 54, 37, 51, 12, 21, 28,  1, 26,  6, 61, 31, 15, 44, 47,  7, 24,
       39,  8,  5, 56, 13, 23, 27, 40, 22, 50, 11, 57, 34, 46, 20, 25, 19,
       38, 35, 14, 18,  0, 60, 41, 58, 33, 53, 62, 45,  4, 52,  9],
      dtype=int64), 'cur_cost': 114366.0}, {'tour': [22, 27, 25, 0, 7, 9, 62, 23, 4, 18, 5, 1, 60, 54, 58, 65, 15, 35, 36, 33, 21, 19, 6, 3, 13, 37, 20, 49, 45, 14, 31, 16, 30, 8, 56, 63, 47, 41, 50, 39, 34, 17, 43, 24, 12, 28, 46, 44, 26, 10, 59, 55, 52, 61, 57, 11, 2, 53, 40, 38, 42, 51, 32, 29, 48, 64], 'cur_cost': 64428.0}, {'tour': [51, 17, 23, 3, 64, 5, 22, 31, 11, 14, 6, 60, 8, 15, 29, 2, 54, 0, 57, 52, 20, 21, 4, 58, 63, 7, 61, 19, 16, 32, 10, 1, 33, 27, 25, 13, 43, 41, 47, 42, 34, 37, 49, 12, 40, 50, 36, 35, 9, 65, 53, 59, 55, 48, 39, 18, 26, 28, 30, 24, 46, 45, 38, 44, 56, 62], 'cur_cost': 64047.0}, {'tour': array([30, 48, 55, 22,  1, 26, 60,  6, 43, 16, 11, 59, 37, 49, 34, 62, 39,
        2, 47, 12, 42, 57, 25, 38, 64, 10, 44, 17, 54, 18, 50,  8, 52,  0,
       63, 31, 65, 46, 15, 58, 28, 14,  4, 45, 53, 20, 61, 40,  7, 56, 36,
       21,  5, 35, 19,  3, 33, 13, 29, 23, 24, 51,  9, 27, 32, 41],
      dtype=int64), 'cur_cost': 128312.0}, {'tour': array([ 3, 14,  4, 30, 56, 20, 49, 33, 44, 63,  2,  0, 38, 35, 25, 42, 45,
       23, 57,  1, 51, 48, 62,  6, 15, 27, 43, 29, 19, 11, 59, 50, 53, 37,
       24, 46,  5, 40, 34, 28, 47, 17, 18, 61, 41, 32, 13, 31, 12, 26, 52,
       22,  8,  9, 16, 10, 64, 58, 36, 39, 21, 60, 65, 55,  7, 54],
      dtype=int64), 'cur_cost': 101412.0}, {'tour': array([45, 20, 19, 59, 15, 21, 25, 35,  6, 54, 22, 43, 55, 12,  1,  3, 62,
       39, 30, 10, 51, 18,  9, 33, 14, 17, 28, 11,  5, 13, 56, 47, 26, 37,
        4, 32, 34, 27, 36, 23, 64, 44, 46, 49, 61, 40, 24,  0, 63,  8, 41,
       48, 31,  2, 52, 57, 53, 65,  7, 60, 42, 58, 38, 16, 50, 29],
      dtype=int64), 'cur_cost': 94563.0}, {'tour': array([47, 48, 23, 59, 37,  1, 60, 44, 25, 26, 11,  0,  4, 19, 34, 32, 28,
       30,  5, 24, 43, 13,  8,  2, 14, 17, 57, 21, 16, 45, 15, 22, 29, 33,
       36, 42, 12,  9, 20,  7, 39,  6, 40, 56, 38, 10, 51, 65, 64, 49, 41,
       18, 62, 63, 58, 55, 46, 35, 27,  3, 61, 53, 31, 54, 50, 52],
      dtype=int64), 'cur_cost': 94284.0}]
2025-08-03 16:31:22,547 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-03 16:31:22,548 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-03 16:31:22,550 - experts.management.collaboration_manager - INFO - 个体 16 利用路径生成报告: {'new_tour': array([30, 48, 55, 22,  1, 26, 60,  6, 43, 16, 11, 59, 37, 49, 34, 62, 39,
        2, 47, 12, 42, 57, 25, 38, 64, 10, 44, 17, 54, 18, 50,  8, 52,  0,
       63, 31, 65, 46, 15, 58, 28, 14,  4, 45, 53, 20, 61, 40,  7, 56, 36,
       21,  5, 35, 19,  3, 33, 13, 29, 23, 24, 51,  9, 27, 32, 41],
      dtype=int64), 'cur_cost': 128312.0}
2025-08-03 16:31:22,552 - experts.management.collaboration_manager - INFO - 个体 16 接受新路径 (成本: 128312.00)
2025-08-03 16:31:22,552 - experts.management.collaboration_manager - INFO - 为个体 17 生成探索路径
2025-08-03 16:31:22,553 - ExplorationExpert - INFO - 开始为个体 17 生成探索路径（算法实现）
2025-08-03 16:31:22,553 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:31:22,568 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:31:22,569 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:31:22,569 - ExplorationExpert - INFO - 探索路径生成完成，成本: 53793.0, 路径长度: 66
2025-08-03 16:31:22,569 - experts.management.collaboration_manager - INFO - 个体 17 探索路径生成报告: {'new_tour': [22, 23, 17, 21, 32, 37, 12, 9, 56, 58, 20, 33, 7, 4, 54, 52, 60, 61, 10, 5, 13, 25, 16, 30, 6, 24, 15, 11, 27, 43, 47, 40, 48, 41, 14, 1, 64, 55, 8, 62, 59, 65, 53, 2, 57, 39, 46, 50, 42, 18, 34, 28, 36, 29, 26, 35, 19, 49, 51, 45, 31, 0, 3, 63, 44, 38], 'cur_cost': 53793.0}
2025-08-03 16:31:22,570 - experts.management.collaboration_manager - INFO - 个体 17 接受新路径 (成本: 53793.00)
2025-08-03 16:31:22,570 - experts.management.collaboration_manager - INFO - 为个体 18 生成探索路径
2025-08-03 16:31:22,570 - ExplorationExpert - INFO - 开始为个体 18 生成探索路径（算法实现）
2025-08-03 16:31:22,570 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:31:22,588 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:31:22,589 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:31:22,590 - ExplorationExpert - INFO - 探索路径生成完成，成本: 62903.0, 路径长度: 66
2025-08-03 16:31:22,590 - experts.management.collaboration_manager - INFO - 个体 18 探索路径生成报告: {'new_tour': [51, 26, 37, 19, 25, 12, 13, 17, 29, 8, 0, 62, 52, 55, 23, 2, 57, 61, 10, 64, 15, 9, 7, 20, 14, 4, 24, 28, 6, 5, 63, 58, 56, 21, 34, 36, 1, 53, 3, 65, 18, 43, 47, 50, 48, 45, 16, 27, 22, 11, 60, 46, 40, 41, 42, 44, 35, 31, 33, 32, 49, 39, 30, 38, 59, 54], 'cur_cost': 62903.0}
2025-08-03 16:31:22,591 - experts.management.collaboration_manager - INFO - 个体 18 接受新路径 (成本: 62903.00)
2025-08-03 16:31:22,591 - experts.management.collaboration_manager - INFO - 为个体 19 生成利用路径
2025-08-03 16:31:22,592 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:31:22,592 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:31:22,593 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 19 处的路径，新成本: 99961.0
2025-08-03 16:31:22,682 - ExploitationExpert - INFO - res_population_num: 6
2025-08-03 16:31:22,683 - ExploitationExpert - INFO - res_population_costs: [9833.0, 9607.0, 9587.0, 9534.0, 9532, 9521]
2025-08-03 16:31:22,684 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 41, 50, 51,
       38, 45, 44, 39, 47, 46, 48, 43, 40, 49, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 42, 46,
       48, 49, 47, 39, 44, 45, 50, 51, 38, 41, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 59, 62, 64, 57,
       54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 27, 37, 36, 26,
       25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:31:22,689 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:31:22,690 - ExploitationExpert - INFO - populations: [{'tour': [0, 4, 19, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12886.0}, {'tour': array([31,  4, 49, 32, 18, 23, 39, 14, 48, 42, 65, 27, 55, 45, 51, 34,  6,
       37, 52, 44, 54,  1,  7, 60, 20, 25, 40, 13, 16, 10, 12, 17, 35, 28,
       43, 57,  5, 36, 41, 24, 19, 58, 61, 53,  2, 15, 26, 56, 46, 62, 38,
       63, 22, 64,  0,  3, 11, 33, 47, 50, 21,  8, 29, 59,  9, 30],
      dtype=int64), 'cur_cost': 103961.0}, {'tour': [0, 3, 16, 5, 4, 8, 2, 6, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12789.0}, {'tour': [0, 11, 15, 1, 7, 3, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12675.0}, {'tour': array([62, 50, 38,  9, 64, 58, 48, 60, 44, 40, 11, 39, 56,  5, 21, 20, 26,
       36, 47,  7, 22, 28, 37, 57,  1, 32, 16, 34, 15, 27, 24, 14, 54, 63,
        6,  2, 43, 46, 51, 10,  0, 13, 17, 31, 61, 19,  3, 41, 42, 52, 49,
       23, 55, 53, 29, 59, 18, 45, 30, 65, 25,  4, 33, 12, 35,  8],
      dtype=int64), 'cur_cost': 100859.0}, {'tour': [0, 14, 8, 1, 7, 3, 9, 11, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12708.0}, {'tour': [0, 1, 6, 7, 3, 9, 11, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10385.0}, {'tour': array([40, 62, 63, 36, 43, 49, 65,  8, 50, 28, 24, 20, 15, 39, 42,  1, 14,
       55, 29,  4, 37,  9, 32, 53, 54, 51, 17,  5, 25, 56, 31, 38, 30, 22,
       34,  3, 16,  0, 21, 27, 33, 60, 18, 52, 44, 45, 59, 19,  6, 57, 13,
        7, 48, 61, 12, 64, 10, 26, 11, 41, 47, 23, 35, 58, 46,  2],
      dtype=int64), 'cur_cost': 116076.0}, {'tour': [0, 8, 18, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 19, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 9, 11, 7, 3, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12822.0}, {'tour': [16, 25, 11, 37, 27, 2, 58, 12, 13, 38, 48, 28, 57, 5, 63, 40, 29, 6, 8, 4, 3, 45, 18, 30, 65, 17, 46, 36, 20, 39, 1, 59, 55, 60, 34, 31, 32, 47, 33, 7, 64, 21, 53, 56, 44, 43, 42, 35, 41, 54, 24, 50, 62, 0, 9, 14, 10, 49, 26, 61, 52, 22, 51, 23, 19, 15], 'cur_cost': 94130.0}, {'tour': array([34, 55, 14, 37, 52,  7, 50, 19,  2, 61, 15, 43, 40, 18, 62, 33, 44,
       53, 65, 60,  1, 57, 11, 25, 28, 12, 49, 51, 29, 56, 48,  3, 35, 59,
       45, 32, 54,  9, 24, 22, 26, 13, 58, 16, 17, 27, 46, 64, 23, 30, 42,
       63,  4, 38,  8, 31,  6, 10,  5, 36, 41, 20, 47, 39,  0, 21],
      dtype=int64), 'cur_cost': 109087.0}, {'tour': [23, 15, 31, 22, 27, 7, 17, 34, 33, 3, 14, 30, 11, 55, 62, 12, 13, 19, 43, 20, 1, 8, 59, 0, 16, 10, 54, 52, 65, 18, 24, 49, 39, 37, 32, 36, 35, 46, 28, 2, 58, 57, 6, 56, 60, 5, 25, 40, 48, 50, 51, 44, 47, 41, 38, 26, 29, 21, 42, 9, 61, 53, 63, 64, 4, 45], 'cur_cost': 63093.0}, {'tour': [0, 24, 17, 26, 25, 31, 33, 28, 30, 35, 34, 36, 37, 27, 29, 32, 18, 16, 23, 22, 12, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14697.0}, {'tour': array([29, 10, 63, 32, 36, 48, 65, 30, 43,  2, 16, 17, 55, 42, 64, 49, 59,
        3, 54, 37, 51, 12, 21, 28,  1, 26,  6, 61, 31, 15, 44, 47,  7, 24,
       39,  8,  5, 56, 13, 23, 27, 40, 22, 50, 11, 57, 34, 46, 20, 25, 19,
       38, 35, 14, 18,  0, 60, 41, 58, 33, 53, 62, 45,  4, 52,  9],
      dtype=int64), 'cur_cost': 114366.0}, {'tour': [22, 27, 25, 0, 7, 9, 62, 23, 4, 18, 5, 1, 60, 54, 58, 65, 15, 35, 36, 33, 21, 19, 6, 3, 13, 37, 20, 49, 45, 14, 31, 16, 30, 8, 56, 63, 47, 41, 50, 39, 34, 17, 43, 24, 12, 28, 46, 44, 26, 10, 59, 55, 52, 61, 57, 11, 2, 53, 40, 38, 42, 51, 32, 29, 48, 64], 'cur_cost': 64428.0}, {'tour': [51, 17, 23, 3, 64, 5, 22, 31, 11, 14, 6, 60, 8, 15, 29, 2, 54, 0, 57, 52, 20, 21, 4, 58, 63, 7, 61, 19, 16, 32, 10, 1, 33, 27, 25, 13, 43, 41, 47, 42, 34, 37, 49, 12, 40, 50, 36, 35, 9, 65, 53, 59, 55, 48, 39, 18, 26, 28, 30, 24, 46, 45, 38, 44, 56, 62], 'cur_cost': 64047.0}, {'tour': array([30, 48, 55, 22,  1, 26, 60,  6, 43, 16, 11, 59, 37, 49, 34, 62, 39,
        2, 47, 12, 42, 57, 25, 38, 64, 10, 44, 17, 54, 18, 50,  8, 52,  0,
       63, 31, 65, 46, 15, 58, 28, 14,  4, 45, 53, 20, 61, 40,  7, 56, 36,
       21,  5, 35, 19,  3, 33, 13, 29, 23, 24, 51,  9, 27, 32, 41],
      dtype=int64), 'cur_cost': 128312.0}, {'tour': [22, 23, 17, 21, 32, 37, 12, 9, 56, 58, 20, 33, 7, 4, 54, 52, 60, 61, 10, 5, 13, 25, 16, 30, 6, 24, 15, 11, 27, 43, 47, 40, 48, 41, 14, 1, 64, 55, 8, 62, 59, 65, 53, 2, 57, 39, 46, 50, 42, 18, 34, 28, 36, 29, 26, 35, 19, 49, 51, 45, 31, 0, 3, 63, 44, 38], 'cur_cost': 53793.0}, {'tour': [51, 26, 37, 19, 25, 12, 13, 17, 29, 8, 0, 62, 52, 55, 23, 2, 57, 61, 10, 64, 15, 9, 7, 20, 14, 4, 24, 28, 6, 5, 63, 58, 56, 21, 34, 36, 1, 53, 3, 65, 18, 43, 47, 50, 48, 45, 16, 27, 22, 11, 60, 46, 40, 41, 42, 44, 35, 31, 33, 32, 49, 39, 30, 38, 59, 54], 'cur_cost': 62903.0}, {'tour': array([26, 31, 42, 34, 56, 47, 48, 37, 64, 43,  0, 58, 61, 30, 51, 41,  2,
        3, 39, 49, 13, 60,  9,  1, 63, 28, 32, 20, 23, 55, 24, 65, 53, 36,
        5, 50, 15, 46, 19, 10, 57, 17,  6, 40, 25, 21,  4, 62, 11,  7, 22,
       29, 59, 16, 45, 33, 12, 54, 52, 35, 27, 14, 18,  8, 44, 38],
      dtype=int64), 'cur_cost': 99961.0}]
2025-08-03 16:31:22,695 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 16:31:22,696 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-03 16:31:22,696 - experts.management.collaboration_manager - INFO - 个体 19 利用路径生成报告: {'new_tour': array([26, 31, 42, 34, 56, 47, 48, 37, 64, 43,  0, 58, 61, 30, 51, 41,  2,
        3, 39, 49, 13, 60,  9,  1, 63, 28, 32, 20, 23, 55, 24, 65, 53, 36,
        5, 50, 15, 46, 19, 10, 57, 17,  6, 40, 25, 21,  4, 62, 11,  7, 22,
       29, 59, 16, 45, 33, 12, 54, 52, 35, 27, 14, 18,  8, 44, 38],
      dtype=int64), 'cur_cost': 99961.0}
2025-08-03 16:31:22,697 - experts.management.collaboration_manager - INFO - 个体 19 接受新路径 (成本: 99961.00)
2025-08-03 16:31:22,697 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 16, 'rejected': 4, 'elite_protected': 4}
2025-08-03 16:31:22,697 - experts.management.collaboration_manager - INFO - 接受率: 16/20 (80.0%)
2025-08-03 16:31:22,699 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 19, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12886.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([31,  4, 49, 32, 18, 23, 39, 14, 48, 42, 65, 27, 55, 45, 51, 34,  6,
       37, 52, 44, 54,  1,  7, 60, 20, 25, 40, 13, 16, 10, 12, 17, 35, 28,
       43, 57,  5, 36, 41, 24, 19, 58, 61, 53,  2, 15, 26, 56, 46, 62, 38,
       63, 22, 64,  0,  3, 11, 33, 47, 50, 21,  8, 29, 59,  9, 30],
      dtype=int64), 'cur_cost': 103961.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 16, 5, 4, 8, 2, 6, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12789.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 15, 1, 7, 3, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12675.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([62, 50, 38,  9, 64, 58, 48, 60, 44, 40, 11, 39, 56,  5, 21, 20, 26,
       36, 47,  7, 22, 28, 37, 57,  1, 32, 16, 34, 15, 27, 24, 14, 54, 63,
        6,  2, 43, 46, 51, 10,  0, 13, 17, 31, 61, 19,  3, 41, 42, 52, 49,
       23, 55, 53, 29, 59, 18, 45, 30, 65, 25,  4, 33, 12, 35,  8],
      dtype=int64), 'cur_cost': 100859.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 8, 1, 7, 3, 9, 11, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12708.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 6, 7, 3, 9, 11, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10385.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([40, 62, 63, 36, 43, 49, 65,  8, 50, 28, 24, 20, 15, 39, 42,  1, 14,
       55, 29,  4, 37,  9, 32, 53, 54, 51, 17,  5, 25, 56, 31, 38, 30, 22,
       34,  3, 16,  0, 21, 27, 33, 60, 18, 52, 44, 45, 59, 19,  6, 57, 13,
        7, 48, 61, 12, 64, 10, 26, 11, 41, 47, 23, 35, 58, 46,  2],
      dtype=int64), 'cur_cost': 116076.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 18, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 19, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 9, 11, 7, 3, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12822.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [16, 25, 11, 37, 27, 2, 58, 12, 13, 38, 48, 28, 57, 5, 63, 40, 29, 6, 8, 4, 3, 45, 18, 30, 65, 17, 46, 36, 20, 39, 1, 59, 55, 60, 34, 31, 32, 47, 33, 7, 64, 21, 53, 56, 44, 43, 42, 35, 41, 54, 24, 50, 62, 0, 9, 14, 10, 49, 26, 61, 52, 22, 51, 23, 19, 15], 'cur_cost': 94130.0}}, {'individual': 10, 'strategy': 'exploit', 'path_data': {'new_tour': array([34, 55, 14, 37, 52,  7, 50, 19,  2, 61, 15, 43, 40, 18, 62, 33, 44,
       53, 65, 60,  1, 57, 11, 25, 28, 12, 49, 51, 29, 56, 48,  3, 35, 59,
       45, 32, 54,  9, 24, 22, 26, 13, 58, 16, 17, 27, 46, 64, 23, 30, 42,
       63,  4, 38,  8, 31,  6, 10,  5, 36, 41, 20, 47, 39,  0, 21],
      dtype=int64), 'cur_cost': 109087.0}}, {'individual': 11, 'strategy': 'explore', 'path_data': {'new_tour': [23, 15, 31, 22, 27, 7, 17, 34, 33, 3, 14, 30, 11, 55, 62, 12, 13, 19, 43, 20, 1, 8, 59, 0, 16, 10, 54, 52, 65, 18, 24, 49, 39, 37, 32, 36, 35, 46, 28, 2, 58, 57, 6, 56, 60, 5, 25, 40, 48, 50, 51, 44, 47, 41, 38, 26, 29, 21, 42, 9, 61, 53, 63, 64, 4, 45], 'cur_cost': 63093.0}}, {'individual': 12, 'strategy': 'explore', 'path_data': {'new_tour': [0, 24, 17, 26, 25, 31, 33, 28, 30, 35, 34, 36, 37, 27, 29, 32, 18, 16, 23, 22, 12, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14697.0}}, {'individual': 13, 'strategy': 'exploit', 'path_data': {'new_tour': array([29, 10, 63, 32, 36, 48, 65, 30, 43,  2, 16, 17, 55, 42, 64, 49, 59,
        3, 54, 37, 51, 12, 21, 28,  1, 26,  6, 61, 31, 15, 44, 47,  7, 24,
       39,  8,  5, 56, 13, 23, 27, 40, 22, 50, 11, 57, 34, 46, 20, 25, 19,
       38, 35, 14, 18,  0, 60, 41, 58, 33, 53, 62, 45,  4, 52,  9],
      dtype=int64), 'cur_cost': 114366.0}}, {'individual': 14, 'strategy': 'explore', 'path_data': {'new_tour': [22, 27, 25, 0, 7, 9, 62, 23, 4, 18, 5, 1, 60, 54, 58, 65, 15, 35, 36, 33, 21, 19, 6, 3, 13, 37, 20, 49, 45, 14, 31, 16, 30, 8, 56, 63, 47, 41, 50, 39, 34, 17, 43, 24, 12, 28, 46, 44, 26, 10, 59, 55, 52, 61, 57, 11, 2, 53, 40, 38, 42, 51, 32, 29, 48, 64], 'cur_cost': 64428.0}}, {'individual': 15, 'strategy': 'explore', 'path_data': {'new_tour': [51, 17, 23, 3, 64, 5, 22, 31, 11, 14, 6, 60, 8, 15, 29, 2, 54, 0, 57, 52, 20, 21, 4, 58, 63, 7, 61, 19, 16, 32, 10, 1, 33, 27, 25, 13, 43, 41, 47, 42, 34, 37, 49, 12, 40, 50, 36, 35, 9, 65, 53, 59, 55, 48, 39, 18, 26, 28, 30, 24, 46, 45, 38, 44, 56, 62], 'cur_cost': 64047.0}}, {'individual': 16, 'strategy': 'exploit', 'path_data': {'new_tour': array([30, 48, 55, 22,  1, 26, 60,  6, 43, 16, 11, 59, 37, 49, 34, 62, 39,
        2, 47, 12, 42, 57, 25, 38, 64, 10, 44, 17, 54, 18, 50,  8, 52,  0,
       63, 31, 65, 46, 15, 58, 28, 14,  4, 45, 53, 20, 61, 40,  7, 56, 36,
       21,  5, 35, 19,  3, 33, 13, 29, 23, 24, 51,  9, 27, 32, 41],
      dtype=int64), 'cur_cost': 128312.0}}, {'individual': 17, 'strategy': 'explore', 'path_data': {'new_tour': [22, 23, 17, 21, 32, 37, 12, 9, 56, 58, 20, 33, 7, 4, 54, 52, 60, 61, 10, 5, 13, 25, 16, 30, 6, 24, 15, 11, 27, 43, 47, 40, 48, 41, 14, 1, 64, 55, 8, 62, 59, 65, 53, 2, 57, 39, 46, 50, 42, 18, 34, 28, 36, 29, 26, 35, 19, 49, 51, 45, 31, 0, 3, 63, 44, 38], 'cur_cost': 53793.0}}, {'individual': 18, 'strategy': 'explore', 'path_data': {'new_tour': [51, 26, 37, 19, 25, 12, 13, 17, 29, 8, 0, 62, 52, 55, 23, 2, 57, 61, 10, 64, 15, 9, 7, 20, 14, 4, 24, 28, 6, 5, 63, 58, 56, 21, 34, 36, 1, 53, 3, 65, 18, 43, 47, 50, 48, 45, 16, 27, 22, 11, 60, 46, 40, 41, 42, 44, 35, 31, 33, 32, 49, 39, 30, 38, 59, 54], 'cur_cost': 62903.0}}, {'individual': 19, 'strategy': 'exploit', 'path_data': {'new_tour': array([26, 31, 42, 34, 56, 47, 48, 37, 64, 43,  0, 58, 61, 30, 51, 41,  2,
        3, 39, 49, 13, 60,  9,  1, 63, 28, 32, 20, 23, 55, 24, 65, 53, 36,
        5, 50, 15, 46, 19, 10, 57, 17,  6, 40, 25, 21,  4, 62, 11,  7, 22,
       29, 59, 16, 45, 33, 12, 54, 52, 35, 27, 14, 18,  8, 44, 38],
      dtype=int64), 'cur_cost': 99961.0}}]
2025-08-03 16:31:22,704 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-03 16:31:22,704 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:31:22,723 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=10385.000, 多样性=0.947
2025-08-03 16:31:22,724 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-03 16:31:22,725 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-03 16:31:22,725 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-03 16:31:22,727 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 1, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.02888888175134575, 'best_improvement': -0.050050556117290194}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.011818560133166933}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 6, 'new_count': 6, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.8373737373737373, 'new_diversity': 0.8373737373737373, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-03 16:31:22,729 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-03 16:31:22,735 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_solution.json
2025-08-03 16:31:22,736 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_20250803_163122.solution
2025-08-03 16:31:22,737 - __main__ - INFO - 实例 composite13_66 处理完成
