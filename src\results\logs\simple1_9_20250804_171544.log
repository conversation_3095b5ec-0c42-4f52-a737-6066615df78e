2025-08-04 17:15:44,616 - __main__ - INFO - simple1_9 开始进化第 1 代
2025-08-04 17:15:44,617 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-04 17:15:44,618 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:15:44,619 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=681.000, 多样性=0.879
2025-08-04 17:15:44,621 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:15:44,621 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.879
2025-08-04 17:15:44,622 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:15:44,625 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-04 17:15:44,625 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:15:44,625 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-04 17:15:44,625 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-04 17:15:44,780 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.200, 适应度梯度: -37.560, 聚类评分: 0.000, 覆盖率: 0.001, 收敛趋势: 0.000, 多样性: 0.791
2025-08-04 17:15:44,780 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-04 17:15:44,780 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-04 17:15:44,780 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:15:44,829 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记，坐标系统已统一
2025-08-04 17:15:45,361 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_1_20250804_171545.html
2025-08-04 17:15:45,397 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_1_20250804_171545.html
2025-08-04 17:15:45,398 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-04 17:15:45,398 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-04 17:15:45,398 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.7724秒
2025-08-04 17:15:45,398 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-04 17:15:45,398 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -37.56, 'local_optima_density': 0.2, 'gradient_variance': 21959.294400000002, 'cluster_count': 0}, 'population_state': {'diversity': 0.7911111111111111, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0009, 'fitness_entropy': 0.969570350190125, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -37.560)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.001)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.791)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754298944.7809412, 'performance_metrics': {}}}
2025-08-04 17:15:45,398 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:15:45,399 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:15:45,399 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:15:45,399 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:15:45,400 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-04 17:15:45,400 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-04 17:15:45,400 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-04 17:15:45,400 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:15:45,400 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:15:45,401 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-04 17:15:45,401 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:15:45,401 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:15:45,401 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 4} (总数: 2, 保护比例: 0.20)
2025-08-04 17:15:45,401 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:15:45,401 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:15:45,402 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:45,402 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:15:45,402 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:45,519 - ExplorationExpert - INFO - 探索路径生成完成，成本: 797.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:15:45,520 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [6, 7, 3, 4, 0, 1, 2, 8, 5], 'cur_cost': 797.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:15:45,520 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 797.00)
2025-08-04 17:15:45,520 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:15:45,520 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:15:45,520 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:45,521 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:15:45,521 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:45,521 - ExplorationExpert - INFO - 探索路径生成完成，成本: 857.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:15:45,521 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [1, 6, 7, 0, 3, 5, 8, 4, 2], 'cur_cost': 857.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:15:45,521 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 857.00)
2025-08-04 17:15:45,522 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:15:45,522 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:15:45,522 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:45,522 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:15:45,522 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:45,522 - ExplorationExpert - INFO - 探索路径生成完成，成本: 739.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:15:45,523 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [1, 4, 2, 7, 3, 8, 5, 6, 0], 'cur_cost': 739.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:15:45,523 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 739.00)
2025-08-04 17:15:45,523 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-04 17:15:45,523 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:15:45,524 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:15:45,525 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1118.0
2025-08-04 17:15:46,417 - ExploitationExpert - INFO - res_population_num: 1
2025-08-04 17:15:46,418 - ExploitationExpert - INFO - res_population_costs: [680.0]
2025-08-04 17:15:46,418 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-04 17:15:46,418 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:15:46,418 - ExploitationExpert - INFO - populations: [{'tour': [6, 7, 3, 4, 0, 1, 2, 8, 5], 'cur_cost': 797.0}, {'tour': [1, 6, 7, 0, 3, 5, 8, 4, 2], 'cur_cost': 857.0}, {'tour': [1, 4, 2, 7, 3, 8, 5, 6, 0], 'cur_cost': 739.0}, {'tour': array([7, 6, 0, 8, 2, 3, 4, 5, 1], dtype=int64), 'cur_cost': 1118.0}, {'tour': array([8, 4, 0, 1, 2, 3, 5, 6, 7], dtype=int64), 'cur_cost': 809.0}, {'tour': array([8, 5, 1, 2, 7, 0, 4, 6, 3], dtype=int64), 'cur_cost': 1029.0}, {'tour': array([2, 0, 6, 3, 5, 8, 1, 7, 4], dtype=int64), 'cur_cost': 935.0}, {'tour': array([5, 0, 3, 6, 8, 2, 4, 1, 7], dtype=int64), 'cur_cost': 950.0}, {'tour': array([2, 4, 5, 1, 0, 8, 6, 3, 7], dtype=int64), 'cur_cost': 973.0}, {'tour': array([3, 1, 0, 8, 2, 7, 5, 4, 6], dtype=int64), 'cur_cost': 1037.0}]
2025-08-04 17:15:46,420 - ExploitationExpert - INFO - 局部搜索耗时: 0.90秒，最大迭代次数: 10
2025-08-04 17:15:46,420 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-04 17:15:46,421 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([7, 6, 0, 8, 2, 3, 4, 5, 1], dtype=int64), 'cur_cost': 1118.0, 'intermediate_solutions': [{'tour': array([7, 3, 1, 8, 6, 2, 0, 5, 4], dtype=int64), 'cur_cost': 1221.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 7, 3, 1, 6, 2, 0, 5, 4], dtype=int64), 'cur_cost': 1111.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 8, 7, 3, 1, 2, 0, 5, 4], dtype=int64), 'cur_cost': 1135.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 8, 7, 3, 6, 2, 0, 5, 4], dtype=int64), 'cur_cost': 1115.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 6, 8, 7, 3, 2, 0, 5, 4], dtype=int64), 'cur_cost': 1058.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:15:46,421 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1118.00)
2025-08-04 17:15:46,421 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:15:46,421 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:15:46,421 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:46,422 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:15:46,422 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:46,422 - ExplorationExpert - INFO - 探索路径生成完成，成本: 801.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:15:46,422 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [2, 4, 1, 0, 8, 5, 6, 3, 7], 'cur_cost': 801.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:15:46,422 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 801.00)
2025-08-04 17:15:46,422 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:15:46,422 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:15:46,423 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:46,423 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:15:46,423 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:46,423 - ExplorationExpert - INFO - 探索路径生成完成，成本: 902.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:15:46,423 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [4, 0, 1, 7, 5, 3, 8, 2, 6], 'cur_cost': 902.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:15:46,423 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 902.00)
2025-08-04 17:15:46,424 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:15:46,424 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:15:46,424 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:46,424 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:15:46,424 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:46,424 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1003.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:15:46,424 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 5, 7, 1, 3, 8, 4, 2, 6], 'cur_cost': 1003.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:15:46,424 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1003.00)
2025-08-04 17:15:46,425 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:15:46,425 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:15:46,425 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:46,425 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:15:46,425 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:46,426 - ExplorationExpert - INFO - 探索路径生成完成，成本: 903.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:15:46,426 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [5, 2, 4, 0, 1, 8, 6, 3, 7], 'cur_cost': 903.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:15:46,426 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 903.00)
2025-08-04 17:15:46,426 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:15:46,426 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:15:46,426 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:46,427 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:15:46,427 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:46,427 - ExplorationExpert - INFO - 探索路径生成完成，成本: 794.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:15:46,427 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [6, 7, 5, 8, 3, 4, 2, 0, 1], 'cur_cost': 794.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:15:46,427 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 794.00)
2025-08-04 17:15:46,427 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-04 17:15:46,427 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:15:46,427 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:15:46,428 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 915.0
2025-08-04 17:15:47,724 - ExploitationExpert - INFO - res_population_num: 2
2025-08-04 17:15:47,724 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0]
2025-08-04 17:15:47,724 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64)]
2025-08-04 17:15:47,725 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:15:47,725 - ExploitationExpert - INFO - populations: [{'tour': [6, 7, 3, 4, 0, 1, 2, 8, 5], 'cur_cost': 797.0}, {'tour': [1, 6, 7, 0, 3, 5, 8, 4, 2], 'cur_cost': 857.0}, {'tour': [1, 4, 2, 7, 3, 8, 5, 6, 0], 'cur_cost': 739.0}, {'tour': array([7, 6, 0, 8, 2, 3, 4, 5, 1], dtype=int64), 'cur_cost': 1118.0}, {'tour': [2, 4, 1, 0, 8, 5, 6, 3, 7], 'cur_cost': 801.0}, {'tour': [4, 0, 1, 7, 5, 3, 8, 2, 6], 'cur_cost': 902.0}, {'tour': [0, 5, 7, 1, 3, 8, 4, 2, 6], 'cur_cost': 1003.0}, {'tour': [5, 2, 4, 0, 1, 8, 6, 3, 7], 'cur_cost': 903.0}, {'tour': [6, 7, 5, 8, 3, 4, 2, 0, 1], 'cur_cost': 794.0}, {'tour': array([8, 4, 5, 6, 7, 3, 2, 1, 0], dtype=int64), 'cur_cost': 915.0}]
2025-08-04 17:15:47,726 - ExploitationExpert - INFO - 局部搜索耗时: 1.30秒，最大迭代次数: 10
2025-08-04 17:15:47,726 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-04 17:15:47,727 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([8, 4, 5, 6, 7, 3, 2, 1, 0], dtype=int64), 'cur_cost': 915.0, 'intermediate_solutions': [{'tour': array([0, 1, 3, 8, 2, 7, 5, 4, 6], dtype=int64), 'cur_cost': 975.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 0, 1, 3, 2, 7, 5, 4, 6], dtype=int64), 'cur_cost': 1126.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 8, 0, 1, 3, 7, 5, 4, 6], dtype=int64), 'cur_cost': 1056.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 8, 0, 1, 2, 7, 5, 4, 6], dtype=int64), 'cur_cost': 988.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 2, 8, 0, 1, 7, 5, 4, 6], dtype=int64), 'cur_cost': 1016.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:15:47,727 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 915.00)
2025-08-04 17:15:47,727 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:15:47,727 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:15:47,728 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [6, 7, 3, 4, 0, 1, 2, 8, 5], 'cur_cost': 797.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [1, 6, 7, 0, 3, 5, 8, 4, 2], 'cur_cost': 857.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 2, 7, 3, 8, 5, 6, 0], 'cur_cost': 739.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 6, 0, 8, 2, 3, 4, 5, 1], dtype=int64), 'cur_cost': 1118.0, 'intermediate_solutions': [{'tour': array([7, 3, 1, 8, 6, 2, 0, 5, 4], dtype=int64), 'cur_cost': 1221.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 7, 3, 1, 6, 2, 0, 5, 4], dtype=int64), 'cur_cost': 1111.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 8, 7, 3, 1, 2, 0, 5, 4], dtype=int64), 'cur_cost': 1135.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 8, 7, 3, 6, 2, 0, 5, 4], dtype=int64), 'cur_cost': 1115.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 6, 8, 7, 3, 2, 0, 5, 4], dtype=int64), 'cur_cost': 1058.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [2, 4, 1, 0, 8, 5, 6, 3, 7], 'cur_cost': 801.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [4, 0, 1, 7, 5, 3, 8, 2, 6], 'cur_cost': 902.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 7, 1, 3, 8, 4, 2, 6], 'cur_cost': 1003.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [5, 2, 4, 0, 1, 8, 6, 3, 7], 'cur_cost': 903.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [6, 7, 5, 8, 3, 4, 2, 0, 1], 'cur_cost': 794.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 4, 5, 6, 7, 3, 2, 1, 0], dtype=int64), 'cur_cost': 915.0, 'intermediate_solutions': [{'tour': array([0, 1, 3, 8, 2, 7, 5, 4, 6], dtype=int64), 'cur_cost': 975.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 0, 1, 3, 2, 7, 5, 4, 6], dtype=int64), 'cur_cost': 1126.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 8, 0, 1, 3, 7, 5, 4, 6], dtype=int64), 'cur_cost': 1056.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 8, 0, 1, 2, 7, 5, 4, 6], dtype=int64), 'cur_cost': 988.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 2, 8, 0, 1, 7, 5, 4, 6], dtype=int64), 'cur_cost': 1016.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-04 17:15:47,728 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:15:47,728 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:15:47,729 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=739.000, 多样性=0.901
2025-08-04 17:15:47,730 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-04 17:15:47,730 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-04 17:15:47,730 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:15:47,730 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.004000213846156754, 'best_improvement': -0.08516886930983847}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.025280898876404403}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.8888888888888888, 'new_diversity': 0.8888888888888888, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-04 17:15:47,730 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-04 17:15:47,730 - __main__ - INFO - simple1_9 开始进化第 2 代
2025-08-04 17:15:47,730 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-04 17:15:47,730 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:15:47,731 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=739.000, 多样性=0.901
2025-08-04 17:15:47,731 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:15:47,732 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.901
2025-08-04 17:15:47,732 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:15:47,732 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.889
2025-08-04 17:15:47,734 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-04 17:15:47,734 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:15:47,734 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-04 17:15:47,734 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-04 17:15:47,740 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.167, 适应度梯度: 22.200, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 0.672
2025-08-04 17:15:47,741 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-04 17:15:47,741 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:15:47,741 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:15:47,766 - visualization.landscape_visualizer - INFO - 插值约束: 110 个点被约束到最小值 680.00
2025-08-04 17:15:47,769 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:15:47,843 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_2_20250804_171547.html
2025-08-04 17:15:47,879 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_2_20250804_171547.html
2025-08-04 17:15:47,879 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 2
2025-08-04 17:15:47,879 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-04 17:15:47,879 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1453秒
2025-08-04 17:15:47,879 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.16666666666666666, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 22.2, 'local_optima_density': 0.16666666666666666, 'gradient_variance': 20419.066666666666, 'cluster_count': 0}, 'population_state': {'diversity': 0.6717171717171717, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0021, 'fitness_entropy': 0.9353340267248303, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 22.200)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754298947.7413754, 'performance_metrics': {}}}
2025-08-04 17:15:47,880 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:15:47,880 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:15:47,880 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:15:47,880 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:15:47,880 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:15:47,880 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-04 17:15:47,881 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:15:47,881 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:15:47,881 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:15:47,881 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:15:47,881 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:15:47,882 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:15:47,882 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 2} (总数: 2, 保护比例: 0.20)
2025-08-04 17:15:47,882 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:15:47,882 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:15:47,882 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:47,882 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:15:47,883 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:47,883 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:47,883 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:47,883 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:47,883 - ExplorationExpert - INFO - 探索路径生成完成，成本: 970.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:15:47,883 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [7, 8, 6, 1, 3, 5, 0, 4, 2], 'cur_cost': 970.0, 'intermediate_solutions': [{'tour': [6, 2, 3, 4, 0, 1, 7, 8, 5], 'cur_cost': 967.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 3, 1, 0, 4, 2, 8, 5], 'cur_cost': 758.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 7, 3, 4, 0, 1, 2, 8, 5], 'cur_cost': 797.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:15:47,884 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 970.00)
2025-08-04 17:15:47,884 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:15:47,884 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:15:47,884 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:47,884 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:15:47,884 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:47,884 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:47,885 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:47,885 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:47,885 - ExplorationExpert - INFO - 探索路径生成完成，成本: 839.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:15:47,885 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [7, 8, 4, 1, 0, 6, 5, 3, 2], 'cur_cost': 839.0, 'intermediate_solutions': [{'tour': [1, 6, 7, 0, 3, 2, 8, 4, 5], 'cur_cost': 1060.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 7, 0, 3, 2, 4, 8, 5], 'cur_cost': 972.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 5, 7, 0, 3, 8, 4, 2], 'cur_cost': 834.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:15:47,885 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 839.00)
2025-08-04 17:15:47,885 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:15:47,885 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:15:47,885 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:47,886 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:15:47,886 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:47,886 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:47,886 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:47,886 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:47,886 - ExplorationExpert - INFO - 探索路径生成完成，成本: 707.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:15:47,886 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [7, 5, 6, 0, 1, 2, 4, 8, 3], 'cur_cost': 707.0, 'intermediate_solutions': [{'tour': [1, 4, 2, 7, 3, 6, 5, 8, 0], 'cur_cost': 801.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 3, 7, 2, 4, 1, 5, 6, 0], 'cur_cost': 900.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 2, 7, 1, 3, 8, 5, 6, 0], 'cur_cost': 904.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:15:47,887 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 707.00)
2025-08-04 17:15:47,887 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-04 17:15:47,887 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:15:47,887 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:15:47,887 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1024.0
2025-08-04 17:15:47,941 - ExploitationExpert - INFO - res_population_num: 2
2025-08-04 17:15:47,941 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0]
2025-08-04 17:15:47,941 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64)]
2025-08-04 17:15:47,942 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:15:47,942 - ExploitationExpert - INFO - populations: [{'tour': [7, 8, 6, 1, 3, 5, 0, 4, 2], 'cur_cost': 970.0}, {'tour': [7, 8, 4, 1, 0, 6, 5, 3, 2], 'cur_cost': 839.0}, {'tour': [7, 5, 6, 0, 1, 2, 4, 8, 3], 'cur_cost': 707.0}, {'tour': array([8, 1, 4, 6, 0, 2, 3, 5, 7], dtype=int64), 'cur_cost': 1024.0}, {'tour': [2, 4, 1, 0, 8, 5, 6, 3, 7], 'cur_cost': 801.0}, {'tour': [4, 0, 1, 7, 5, 3, 8, 2, 6], 'cur_cost': 902.0}, {'tour': [0, 5, 7, 1, 3, 8, 4, 2, 6], 'cur_cost': 1003.0}, {'tour': [5, 2, 4, 0, 1, 8, 6, 3, 7], 'cur_cost': 903.0}, {'tour': [6, 7, 5, 8, 3, 4, 2, 0, 1], 'cur_cost': 794.0}, {'tour': [8, 4, 5, 6, 7, 3, 2, 1, 0], 'cur_cost': 915.0}]
2025-08-04 17:15:47,943 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒，最大迭代次数: 10
2025-08-04 17:15:47,943 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-04 17:15:47,943 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([8, 1, 4, 6, 0, 2, 3, 5, 7], dtype=int64), 'cur_cost': 1024.0, 'intermediate_solutions': [{'tour': array([0, 6, 7, 8, 2, 3, 4, 5, 1]), 'cur_cost': 985.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 0, 6, 7, 2, 3, 4, 5, 1]), 'cur_cost': 1199.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 8, 0, 6, 7, 3, 4, 5, 1]), 'cur_cost': 1042.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 8, 0, 6, 2, 3, 4, 5, 1]), 'cur_cost': 1212.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 2, 8, 0, 6, 3, 4, 5, 1]), 'cur_cost': 1118.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:15:47,944 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1024.00)
2025-08-04 17:15:47,944 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:15:47,944 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:15:47,944 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:47,944 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:15:47,944 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:47,945 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:47,945 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:47,945 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:47,945 - ExplorationExpert - INFO - 探索路径生成完成，成本: 991.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:15:47,945 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [2, 7, 8, 5, 6, 0, 4, 3, 1], 'cur_cost': 991.0, 'intermediate_solutions': [{'tour': [2, 4, 1, 0, 8, 5, 7, 3, 6], 'cur_cost': 861.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 4, 1, 0, 7, 3, 6, 5, 8], 'cur_cost': 718.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 1, 0, 8, 5, 3, 6, 7], 'cur_cost': 814.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:15:47,945 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 991.00)
2025-08-04 17:15:47,945 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:15:47,945 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:15:47,945 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:47,946 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:15:47,946 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:47,946 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:47,946 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:47,946 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:47,946 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1029.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:15:47,946 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [4, 8, 5, 6, 0, 2, 7, 1, 3], 'cur_cost': 1029.0, 'intermediate_solutions': [{'tour': [4, 6, 1, 7, 5, 3, 8, 2, 0], 'cur_cost': 916.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 0, 1, 7, 5, 2, 8, 3, 6], 'cur_cost': 924.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 1, 6, 7, 5, 3, 8, 2], 'cur_cost': 694.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:15:47,947 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1029.00)
2025-08-04 17:15:47,947 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-04 17:15:47,947 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:15:47,947 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:15:47,947 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1013.0
2025-08-04 17:15:48,000 - ExploitationExpert - INFO - res_population_num: 2
2025-08-04 17:15:48,001 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0]
2025-08-04 17:15:48,001 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64)]
2025-08-04 17:15:48,002 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:15:48,002 - ExploitationExpert - INFO - populations: [{'tour': [7, 8, 6, 1, 3, 5, 0, 4, 2], 'cur_cost': 970.0}, {'tour': [7, 8, 4, 1, 0, 6, 5, 3, 2], 'cur_cost': 839.0}, {'tour': [7, 5, 6, 0, 1, 2, 4, 8, 3], 'cur_cost': 707.0}, {'tour': array([8, 1, 4, 6, 0, 2, 3, 5, 7], dtype=int64), 'cur_cost': 1024.0}, {'tour': [2, 7, 8, 5, 6, 0, 4, 3, 1], 'cur_cost': 991.0}, {'tour': [4, 8, 5, 6, 0, 2, 7, 1, 3], 'cur_cost': 1029.0}, {'tour': array([0, 2, 1, 5, 3, 7, 8, 6, 4], dtype=int64), 'cur_cost': 1013.0}, {'tour': [5, 2, 4, 0, 1, 8, 6, 3, 7], 'cur_cost': 903.0}, {'tour': [6, 7, 5, 8, 3, 4, 2, 0, 1], 'cur_cost': 794.0}, {'tour': [8, 4, 5, 6, 7, 3, 2, 1, 0], 'cur_cost': 915.0}]
2025-08-04 17:15:48,002 - ExploitationExpert - INFO - 局部搜索耗时: 0.05秒，最大迭代次数: 10
2025-08-04 17:15:48,003 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-04 17:15:48,003 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([0, 2, 1, 5, 3, 7, 8, 6, 4], dtype=int64), 'cur_cost': 1013.0, 'intermediate_solutions': [{'tour': array([7, 5, 0, 1, 3, 8, 4, 2, 6]), 'cur_cost': 905.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 7, 5, 0, 3, 8, 4, 2, 6]), 'cur_cost': 984.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 1, 7, 5, 0, 8, 4, 2, 6]), 'cur_cost': 1065.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 1, 7, 5, 3, 8, 4, 2, 6]), 'cur_cost': 815.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 3, 1, 7, 5, 8, 4, 2, 6]), 'cur_cost': 1020.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:15:48,003 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1013.00)
2025-08-04 17:15:48,003 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:15:48,004 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:15:48,004 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:48,004 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:15:48,004 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,005 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,005 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,005 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,005 - ExplorationExpert - INFO - 探索路径生成完成，成本: 769.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:15:48,005 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 1, 6, 3, 7, 5, 8, 4, 2], 'cur_cost': 769.0, 'intermediate_solutions': [{'tour': [5, 2, 4, 0, 1, 8, 7, 3, 6], 'cur_cost': 848.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 2, 4, 0, 1, 7, 3, 6, 8], 'cur_cost': 880.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 2, 6, 4, 0, 1, 8, 3, 7], 'cur_cost': 1024.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:15:48,005 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 769.00)
2025-08-04 17:15:48,005 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:15:48,006 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:15:48,006 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:48,006 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:15:48,006 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,006 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,006 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,006 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,007 - ExplorationExpert - INFO - 探索路径生成完成，成本: 912.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:15:48,007 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [7, 0, 4, 8, 5, 3, 6, 1, 2], 'cur_cost': 912.0, 'intermediate_solutions': [{'tour': [6, 4, 5, 8, 3, 7, 2, 0, 1], 'cur_cost': 947.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 0, 2, 4, 3, 8, 5, 7, 1], 'cur_cost': 892.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 7, 5, 2, 8, 3, 4, 0, 1], 'cur_cost': 871.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:15:48,007 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 912.00)
2025-08-04 17:15:48,007 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:15:48,007 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:15:48,007 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:48,008 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:15:48,008 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,008 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,008 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,008 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,008 - ExplorationExpert - INFO - 探索路径生成完成，成本: 858.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:15:48,008 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [1, 5, 7, 0, 4, 2, 8, 3, 6], 'cur_cost': 858.0, 'intermediate_solutions': [{'tour': [8, 4, 5, 7, 6, 3, 2, 1, 0], 'cur_cost': 956.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 5, 6, 7, 3, 2, 1, 0], 'cur_cost': 820.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 8, 4, 5, 7, 3, 2, 1, 0], 'cur_cost': 935.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:15:48,008 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 858.00)
2025-08-04 17:15:48,009 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:15:48,009 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:15:48,010 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [7, 8, 6, 1, 3, 5, 0, 4, 2], 'cur_cost': 970.0, 'intermediate_solutions': [{'tour': [6, 2, 3, 4, 0, 1, 7, 8, 5], 'cur_cost': 967.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 3, 1, 0, 4, 2, 8, 5], 'cur_cost': 758.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 7, 3, 4, 0, 1, 2, 8, 5], 'cur_cost': 797.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [7, 8, 4, 1, 0, 6, 5, 3, 2], 'cur_cost': 839.0, 'intermediate_solutions': [{'tour': [1, 6, 7, 0, 3, 2, 8, 4, 5], 'cur_cost': 1060.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 7, 0, 3, 2, 4, 8, 5], 'cur_cost': 972.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 5, 7, 0, 3, 8, 4, 2], 'cur_cost': 834.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [7, 5, 6, 0, 1, 2, 4, 8, 3], 'cur_cost': 707.0, 'intermediate_solutions': [{'tour': [1, 4, 2, 7, 3, 6, 5, 8, 0], 'cur_cost': 801.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 3, 7, 2, 4, 1, 5, 6, 0], 'cur_cost': 900.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 2, 7, 1, 3, 8, 5, 6, 0], 'cur_cost': 904.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 1, 4, 6, 0, 2, 3, 5, 7], dtype=int64), 'cur_cost': 1024.0, 'intermediate_solutions': [{'tour': array([0, 6, 7, 8, 2, 3, 4, 5, 1]), 'cur_cost': 985.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 0, 6, 7, 2, 3, 4, 5, 1]), 'cur_cost': 1199.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 8, 0, 6, 7, 3, 4, 5, 1]), 'cur_cost': 1042.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 8, 0, 6, 2, 3, 4, 5, 1]), 'cur_cost': 1212.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 2, 8, 0, 6, 3, 4, 5, 1]), 'cur_cost': 1118.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [2, 7, 8, 5, 6, 0, 4, 3, 1], 'cur_cost': 991.0, 'intermediate_solutions': [{'tour': [2, 4, 1, 0, 8, 5, 7, 3, 6], 'cur_cost': 861.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 4, 1, 0, 7, 3, 6, 5, 8], 'cur_cost': 718.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 1, 0, 8, 5, 3, 6, 7], 'cur_cost': 814.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [4, 8, 5, 6, 0, 2, 7, 1, 3], 'cur_cost': 1029.0, 'intermediate_solutions': [{'tour': [4, 6, 1, 7, 5, 3, 8, 2, 0], 'cur_cost': 916.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 0, 1, 7, 5, 2, 8, 3, 6], 'cur_cost': 924.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 1, 6, 7, 5, 3, 8, 2], 'cur_cost': 694.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 2, 1, 5, 3, 7, 8, 6, 4], dtype=int64), 'cur_cost': 1013.0, 'intermediate_solutions': [{'tour': array([7, 5, 0, 1, 3, 8, 4, 2, 6]), 'cur_cost': 905.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 7, 5, 0, 3, 8, 4, 2, 6]), 'cur_cost': 984.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 1, 7, 5, 0, 8, 4, 2, 6]), 'cur_cost': 1065.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 1, 7, 5, 3, 8, 4, 2, 6]), 'cur_cost': 815.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 3, 1, 7, 5, 8, 4, 2, 6]), 'cur_cost': 1020.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 6, 3, 7, 5, 8, 4, 2], 'cur_cost': 769.0, 'intermediate_solutions': [{'tour': [5, 2, 4, 0, 1, 8, 7, 3, 6], 'cur_cost': 848.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 2, 4, 0, 1, 7, 3, 6, 8], 'cur_cost': 880.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 2, 6, 4, 0, 1, 8, 3, 7], 'cur_cost': 1024.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [7, 0, 4, 8, 5, 3, 6, 1, 2], 'cur_cost': 912.0, 'intermediate_solutions': [{'tour': [6, 4, 5, 8, 3, 7, 2, 0, 1], 'cur_cost': 947.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 0, 2, 4, 3, 8, 5, 7, 1], 'cur_cost': 892.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 7, 5, 2, 8, 3, 4, 0, 1], 'cur_cost': 871.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [1, 5, 7, 0, 4, 2, 8, 3, 6], 'cur_cost': 858.0, 'intermediate_solutions': [{'tour': [8, 4, 5, 7, 6, 3, 2, 1, 0], 'cur_cost': 956.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 5, 6, 7, 3, 2, 1, 0], 'cur_cost': 820.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 8, 4, 5, 7, 3, 2, 1, 0], 'cur_cost': 935.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:15:48,010 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:15:48,010 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:15:48,011 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=707.000, 多样性=0.879
2025-08-04 17:15:48,011 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-04 17:15:48,011 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-04 17:15:48,011 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:15:48,012 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.030284913005834774, 'best_improvement': 0.04330175913396482}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.0246575342465755}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.8888888888888888, 'new_diversity': 0.8888888888888888, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-04 17:15:48,012 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-04 17:15:48,012 - __main__ - INFO - simple1_9 开始进化第 3 代
2025-08-04 17:15:48,012 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-04 17:15:48,013 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:15:48,013 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=707.000, 多样性=0.879
2025-08-04 17:15:48,013 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:15:48,014 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.879
2025-08-04 17:15:48,014 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:15:48,014 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.889
2025-08-04 17:15:48,016 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-04 17:15:48,016 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:15:48,016 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-04 17:15:48,016 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-04 17:15:48,023 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.250, 适应度梯度: -0.100, 聚类评分: 0.000, 覆盖率: 0.003, 收敛趋势: 0.000, 多样性: 0.663
2025-08-04 17:15:48,023 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-04 17:15:48,023 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:15:48,023 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:15:48,027 - visualization.landscape_visualizer - INFO - 插值约束: 383 个点被约束到最小值 680.00
2025-08-04 17:15:48,031 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:15:48,106 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_3_20250804_171548.html
2025-08-04 17:15:48,147 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_3_20250804_171548.html
2025-08-04 17:15:48,147 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 3
2025-08-04 17:15:48,147 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-04 17:15:48,147 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1310秒
2025-08-04 17:15:48,147 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.25, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -0.09999999999998721, 'local_optima_density': 0.25, 'gradient_variance': 27487.71666666667, 'cluster_count': 0}, 'population_state': {'diversity': 0.6628787878787878, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0031, 'fitness_entropy': 0.9353340267248305, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -0.100)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.003)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754298948.0232272, 'performance_metrics': {}}}
2025-08-04 17:15:48,148 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:15:48,148 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:15:48,148 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:15:48,148 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:15:48,148 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:15:48,148 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-04 17:15:48,148 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:15:48,149 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:15:48,149 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:15:48,149 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:15:48,149 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:15:48,150 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:15:48,150 - experts.management.collaboration_manager - INFO - 识别精英个体: {2, 7} (总数: 2, 保护比例: 0.20)
2025-08-04 17:15:48,150 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:15:48,150 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:15:48,150 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:48,150 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:15:48,151 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,151 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,151 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,151 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,151 - ExplorationExpert - INFO - 探索路径生成完成，成本: 946.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:15:48,151 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [5, 6, 7, 0, 4, 8, 3, 2, 1], 'cur_cost': 946.0, 'intermediate_solutions': [{'tour': [7, 6, 8, 1, 3, 5, 0, 4, 2], 'cur_cost': 1022.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 8, 6, 1, 3, 5, 4, 0, 2], 'cur_cost': 1056.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 6, 1, 3, 7, 5, 0, 4, 2], 'cur_cost': 923.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:15:48,152 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 946.00)
2025-08-04 17:15:48,152 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:15:48,152 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:15:48,152 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:48,153 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:15:48,153 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,153 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,153 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,153 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,153 - ExplorationExpert - INFO - 探索路径生成完成，成本: 869.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:15:48,153 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 6, 3, 8, 5, 4, 2, 7, 1], 'cur_cost': 869.0, 'intermediate_solutions': [{'tour': [7, 8, 0, 1, 4, 6, 5, 3, 2], 'cur_cost': 933.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 8, 4, 1, 6, 0, 5, 3, 2], 'cur_cost': 973.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 8, 4, 1, 0, 6, 5, 3, 2], 'cur_cost': 839.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:15:48,154 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 869.00)
2025-08-04 17:15:48,154 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:15:48,154 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:15:48,154 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:48,154 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:15:48,154 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,154 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,155 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,155 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,155 - ExplorationExpert - INFO - 探索路径生成完成，成本: 837.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:15:48,155 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 4, 8, 7, 5, 6, 3, 2, 1], 'cur_cost': 837.0, 'intermediate_solutions': [{'tour': [7, 5, 6, 0, 4, 2, 1, 8, 3], 'cur_cost': 830.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 1, 0, 6, 5, 2, 4, 8, 3], 'cur_cost': 796.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 5, 6, 8, 0, 1, 2, 4, 3], 'cur_cost': 835.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:15:48,155 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 837.00)
2025-08-04 17:15:48,155 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-04 17:15:48,155 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:15:48,155 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:15:48,156 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1026.0
2025-08-04 17:15:48,226 - ExploitationExpert - INFO - res_population_num: 2
2025-08-04 17:15:48,226 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0]
2025-08-04 17:15:48,226 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64)]
2025-08-04 17:15:48,227 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:15:48,227 - ExploitationExpert - INFO - populations: [{'tour': [5, 6, 7, 0, 4, 8, 3, 2, 1], 'cur_cost': 946.0}, {'tour': [0, 6, 3, 8, 5, 4, 2, 7, 1], 'cur_cost': 869.0}, {'tour': [0, 4, 8, 7, 5, 6, 3, 2, 1], 'cur_cost': 837.0}, {'tour': array([4, 7, 6, 3, 0, 5, 1, 2, 8], dtype=int64), 'cur_cost': 1026.0}, {'tour': [2, 7, 8, 5, 6, 0, 4, 3, 1], 'cur_cost': 991.0}, {'tour': [4, 8, 5, 6, 0, 2, 7, 1, 3], 'cur_cost': 1029.0}, {'tour': [0, 2, 1, 5, 3, 7, 8, 6, 4], 'cur_cost': 1013.0}, {'tour': [0, 1, 6, 3, 7, 5, 8, 4, 2], 'cur_cost': 769.0}, {'tour': [7, 0, 4, 8, 5, 3, 6, 1, 2], 'cur_cost': 912.0}, {'tour': [1, 5, 7, 0, 4, 2, 8, 3, 6], 'cur_cost': 858.0}]
2025-08-04 17:15:48,228 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:15:48,228 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-04 17:15:48,228 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([4, 7, 6, 3, 0, 5, 1, 2, 8], dtype=int64), 'cur_cost': 1026.0, 'intermediate_solutions': [{'tour': array([4, 1, 8, 6, 0, 2, 3, 5, 7]), 'cur_cost': 1023.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 4, 1, 8, 0, 2, 3, 5, 7]), 'cur_cost': 1059.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 6, 4, 1, 8, 2, 3, 5, 7]), 'cur_cost': 995.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 6, 4, 1, 0, 2, 3, 5, 7]), 'cur_cost': 934.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 0, 6, 4, 1, 2, 3, 5, 7]), 'cur_cost': 996.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:15:48,228 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1026.00)
2025-08-04 17:15:48,229 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:15:48,229 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:15:48,229 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:48,229 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:15:48,229 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,229 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,230 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,230 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,230 - ExplorationExpert - INFO - 探索路径生成完成，成本: 831.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:15:48,230 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [5, 8, 3, 7, 6, 4, 2, 1, 0], 'cur_cost': 831.0, 'intermediate_solutions': [{'tour': [6, 7, 8, 5, 2, 0, 4, 3, 1], 'cur_cost': 1056.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 7, 8, 5, 6, 0, 4, 1, 3], 'cur_cost': 988.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 8, 5, 6, 0, 2, 4, 3, 1], 'cur_cost': 945.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:15:48,230 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 831.00)
2025-08-04 17:15:48,230 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-04 17:15:48,230 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:15:48,230 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:15:48,231 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 891.0
2025-08-04 17:15:48,294 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:15:48,294 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0]
2025-08-04 17:15:48,295 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 17:15:48,296 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:15:48,296 - ExploitationExpert - INFO - populations: [{'tour': [5, 6, 7, 0, 4, 8, 3, 2, 1], 'cur_cost': 946.0}, {'tour': [0, 6, 3, 8, 5, 4, 2, 7, 1], 'cur_cost': 869.0}, {'tour': [0, 4, 8, 7, 5, 6, 3, 2, 1], 'cur_cost': 837.0}, {'tour': array([4, 7, 6, 3, 0, 5, 1, 2, 8], dtype=int64), 'cur_cost': 1026.0}, {'tour': [5, 8, 3, 7, 6, 4, 2, 1, 0], 'cur_cost': 831.0}, {'tour': array([2, 5, 8, 3, 7, 1, 6, 0, 4], dtype=int64), 'cur_cost': 891.0}, {'tour': [0, 2, 1, 5, 3, 7, 8, 6, 4], 'cur_cost': 1013.0}, {'tour': [0, 1, 6, 3, 7, 5, 8, 4, 2], 'cur_cost': 769.0}, {'tour': [7, 0, 4, 8, 5, 3, 6, 1, 2], 'cur_cost': 912.0}, {'tour': [1, 5, 7, 0, 4, 2, 8, 3, 6], 'cur_cost': 858.0}]
2025-08-04 17:15:48,297 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:15:48,297 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-04 17:15:48,297 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([2, 5, 8, 3, 7, 1, 6, 0, 4], dtype=int64), 'cur_cost': 891.0, 'intermediate_solutions': [{'tour': array([5, 8, 4, 6, 0, 2, 7, 1, 3]), 'cur_cost': 1049.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 5, 8, 4, 0, 2, 7, 1, 3]), 'cur_cost': 996.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 6, 5, 8, 4, 2, 7, 1, 3]), 'cur_cost': 960.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 6, 5, 8, 0, 2, 7, 1, 3]), 'cur_cost': 1123.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 0, 6, 5, 8, 2, 7, 1, 3]), 'cur_cost': 992.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:15:48,298 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 891.00)
2025-08-04 17:15:48,298 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-04 17:15:48,298 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:15:48,298 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:15:48,298 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1126.0
2025-08-04 17:15:48,360 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:15:48,360 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0]
2025-08-04 17:15:48,360 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 17:15:48,361 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:15:48,361 - ExploitationExpert - INFO - populations: [{'tour': [5, 6, 7, 0, 4, 8, 3, 2, 1], 'cur_cost': 946.0}, {'tour': [0, 6, 3, 8, 5, 4, 2, 7, 1], 'cur_cost': 869.0}, {'tour': [0, 4, 8, 7, 5, 6, 3, 2, 1], 'cur_cost': 837.0}, {'tour': array([4, 7, 6, 3, 0, 5, 1, 2, 8], dtype=int64), 'cur_cost': 1026.0}, {'tour': [5, 8, 3, 7, 6, 4, 2, 1, 0], 'cur_cost': 831.0}, {'tour': array([2, 5, 8, 3, 7, 1, 6, 0, 4], dtype=int64), 'cur_cost': 891.0}, {'tour': array([4, 3, 0, 7, 5, 1, 8, 6, 2], dtype=int64), 'cur_cost': 1126.0}, {'tour': [0, 1, 6, 3, 7, 5, 8, 4, 2], 'cur_cost': 769.0}, {'tour': [7, 0, 4, 8, 5, 3, 6, 1, 2], 'cur_cost': 912.0}, {'tour': [1, 5, 7, 0, 4, 2, 8, 3, 6], 'cur_cost': 858.0}]
2025-08-04 17:15:48,361 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒，最大迭代次数: 10
2025-08-04 17:15:48,361 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-04 17:15:48,362 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([4, 3, 0, 7, 5, 1, 8, 6, 2], dtype=int64), 'cur_cost': 1126.0, 'intermediate_solutions': [{'tour': array([1, 2, 0, 5, 3, 7, 8, 6, 4]), 'cur_cost': 997.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 1, 2, 0, 3, 7, 8, 6, 4]), 'cur_cost': 1132.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 5, 1, 2, 0, 7, 8, 6, 4]), 'cur_cost': 1073.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 5, 1, 2, 3, 7, 8, 6, 4]), 'cur_cost': 1101.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 3, 5, 1, 2, 7, 8, 6, 4]), 'cur_cost': 1080.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:15:48,362 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1126.00)
2025-08-04 17:15:48,362 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:15:48,362 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:15:48,362 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:48,363 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:15:48,363 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,363 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,363 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,363 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,363 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1085.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:15:48,364 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [4, 6, 3, 0, 8, 7, 5, 2, 1], 'cur_cost': 1085.0, 'intermediate_solutions': [{'tour': [0, 8, 6, 3, 7, 5, 1, 4, 2], 'cur_cost': 963.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 6, 3, 7, 5, 4, 8, 2], 'cur_cost': 857.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 3, 7, 0, 5, 8, 4, 2], 'cur_cost': 878.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:15:48,364 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 1085.00)
2025-08-04 17:15:48,364 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:15:48,364 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:15:48,364 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:48,364 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:15:48,364 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,365 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,365 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,365 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,365 - ExplorationExpert - INFO - 探索路径生成完成，成本: 995.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:15:48,365 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [3, 4, 1, 2, 6, 5, 7, 0, 8], 'cur_cost': 995.0, 'intermediate_solutions': [{'tour': [7, 0, 4, 8, 5, 2, 6, 1, 3], 'cur_cost': 1069.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 0, 4, 8, 6, 3, 5, 1, 2], 'cur_cost': 1000.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 4, 8, 5, 3, 6, 1, 0, 2], 'cur_cost': 869.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:15:48,365 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 995.00)
2025-08-04 17:15:48,365 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:15:48,365 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:15:48,365 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:48,366 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:15:48,366 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,366 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,366 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,366 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,366 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1074.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:15:48,366 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [1, 8, 6, 4, 7, 3, 5, 0, 2], 'cur_cost': 1074.0, 'intermediate_solutions': [{'tour': [1, 5, 7, 0, 3, 2, 8, 4, 6], 'cur_cost': 1057.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 5, 7, 0, 6, 3, 8, 2, 4], 'cur_cost': 858.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 1, 7, 0, 4, 2, 8, 3, 6], 'cur_cost': 863.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:15:48,366 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1074.00)
2025-08-04 17:15:48,367 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:15:48,367 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:15:48,368 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [5, 6, 7, 0, 4, 8, 3, 2, 1], 'cur_cost': 946.0, 'intermediate_solutions': [{'tour': [7, 6, 8, 1, 3, 5, 0, 4, 2], 'cur_cost': 1022.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 8, 6, 1, 3, 5, 4, 0, 2], 'cur_cost': 1056.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 6, 1, 3, 7, 5, 0, 4, 2], 'cur_cost': 923.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 3, 8, 5, 4, 2, 7, 1], 'cur_cost': 869.0, 'intermediate_solutions': [{'tour': [7, 8, 0, 1, 4, 6, 5, 3, 2], 'cur_cost': 933.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 8, 4, 1, 6, 0, 5, 3, 2], 'cur_cost': 973.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 8, 4, 1, 0, 6, 5, 3, 2], 'cur_cost': 839.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 8, 7, 5, 6, 3, 2, 1], 'cur_cost': 837.0, 'intermediate_solutions': [{'tour': [7, 5, 6, 0, 4, 2, 1, 8, 3], 'cur_cost': 830.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 1, 0, 6, 5, 2, 4, 8, 3], 'cur_cost': 796.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 5, 6, 8, 0, 1, 2, 4, 3], 'cur_cost': 835.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 7, 6, 3, 0, 5, 1, 2, 8], dtype=int64), 'cur_cost': 1026.0, 'intermediate_solutions': [{'tour': array([4, 1, 8, 6, 0, 2, 3, 5, 7]), 'cur_cost': 1023.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 4, 1, 8, 0, 2, 3, 5, 7]), 'cur_cost': 1059.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 6, 4, 1, 8, 2, 3, 5, 7]), 'cur_cost': 995.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 6, 4, 1, 0, 2, 3, 5, 7]), 'cur_cost': 934.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 0, 6, 4, 1, 2, 3, 5, 7]), 'cur_cost': 996.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [5, 8, 3, 7, 6, 4, 2, 1, 0], 'cur_cost': 831.0, 'intermediate_solutions': [{'tour': [6, 7, 8, 5, 2, 0, 4, 3, 1], 'cur_cost': 1056.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 7, 8, 5, 6, 0, 4, 1, 3], 'cur_cost': 988.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 8, 5, 6, 0, 2, 4, 3, 1], 'cur_cost': 945.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 5, 8, 3, 7, 1, 6, 0, 4], dtype=int64), 'cur_cost': 891.0, 'intermediate_solutions': [{'tour': array([5, 8, 4, 6, 0, 2, 7, 1, 3]), 'cur_cost': 1049.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 5, 8, 4, 0, 2, 7, 1, 3]), 'cur_cost': 996.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 6, 5, 8, 4, 2, 7, 1, 3]), 'cur_cost': 960.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 6, 5, 8, 0, 2, 7, 1, 3]), 'cur_cost': 1123.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 0, 6, 5, 8, 2, 7, 1, 3]), 'cur_cost': 992.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 3, 0, 7, 5, 1, 8, 6, 2], dtype=int64), 'cur_cost': 1126.0, 'intermediate_solutions': [{'tour': array([1, 2, 0, 5, 3, 7, 8, 6, 4]), 'cur_cost': 997.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 1, 2, 0, 3, 7, 8, 6, 4]), 'cur_cost': 1132.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 5, 1, 2, 0, 7, 8, 6, 4]), 'cur_cost': 1073.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 5, 1, 2, 3, 7, 8, 6, 4]), 'cur_cost': 1101.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 3, 5, 1, 2, 7, 8, 6, 4]), 'cur_cost': 1080.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [4, 6, 3, 0, 8, 7, 5, 2, 1], 'cur_cost': 1085.0, 'intermediate_solutions': [{'tour': [0, 8, 6, 3, 7, 5, 1, 4, 2], 'cur_cost': 963.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 6, 3, 7, 5, 4, 8, 2], 'cur_cost': 857.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 3, 7, 0, 5, 8, 4, 2], 'cur_cost': 878.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [3, 4, 1, 2, 6, 5, 7, 0, 8], 'cur_cost': 995.0, 'intermediate_solutions': [{'tour': [7, 0, 4, 8, 5, 2, 6, 1, 3], 'cur_cost': 1069.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 0, 4, 8, 6, 3, 5, 1, 2], 'cur_cost': 1000.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 4, 8, 5, 3, 6, 1, 0, 2], 'cur_cost': 869.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [1, 8, 6, 4, 7, 3, 5, 0, 2], 'cur_cost': 1074.0, 'intermediate_solutions': [{'tour': [1, 5, 7, 0, 3, 2, 8, 4, 6], 'cur_cost': 1057.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 5, 7, 0, 6, 3, 8, 2, 4], 'cur_cost': 858.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 1, 7, 0, 4, 2, 8, 3, 6], 'cur_cost': 863.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:15:48,368 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:15:48,368 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:15:48,369 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=831.000, 多样性=0.881
2025-08-04 17:15:48,369 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-04 17:15:48,369 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-04 17:15:48,369 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:15:48,370 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.11066354244062743, 'best_improvement': -0.1753889674681754}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.002808988764044906}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:15:48,370 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-04 17:15:48,370 - __main__ - INFO - simple1_9 开始进化第 4 代
2025-08-04 17:15:48,370 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-04 17:15:48,370 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:15:48,371 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=831.000, 多样性=0.881
2025-08-04 17:15:48,371 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:15:48,371 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.881
2025-08-04 17:15:48,371 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:15:48,372 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.630
2025-08-04 17:15:48,373 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-04 17:15:48,373 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:15:48,373 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-04 17:15:48,374 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-04 17:15:48,380 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.308, 适应度梯度: 11.338, 聚类评分: 0.000, 覆盖率: 0.004, 收敛趋势: 0.000, 多样性: 0.606
2025-08-04 17:15:48,380 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-04 17:15:48,381 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:15:48,381 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:15:48,385 - visualization.landscape_visualizer - INFO - 插值约束: 159 个点被约束到最小值 680.00
2025-08-04 17:15:48,389 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:15:48,572 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_4_20250804_171548.html
2025-08-04 17:15:48,613 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_4_20250804_171548.html
2025-08-04 17:15:48,613 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 4
2025-08-04 17:15:48,613 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-04 17:15:48,614 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2407秒
2025-08-04 17:15:48,614 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3076923076923077, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 11.338461538461534, 'local_optima_density': 0.3076923076923077, 'gradient_variance': 27012.95621301775, 'cluster_count': 0}, 'population_state': {'diversity': 0.6055226824457594, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0042, 'fitness_entropy': 0.9686322539060781, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.004)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 11.338)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754298948.380852, 'performance_metrics': {}}}
2025-08-04 17:15:48,614 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:15:48,614 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:15:48,614 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:15:48,614 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:15:48,615 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit']
2025-08-04 17:15:48,615 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-04 17:15:48,615 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit']
2025-08-04 17:15:48,615 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:15:48,615 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:15:48,615 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit']
2025-08-04 17:15:48,615 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:15:48,616 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:15:48,616 - experts.management.collaboration_manager - INFO - 识别精英个体: {2, 4} (总数: 2, 保护比例: 0.20)
2025-08-04 17:15:48,616 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:15:48,617 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:15:48,617 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:48,617 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:15:48,617 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,618 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,618 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,618 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,618 - ExplorationExpert - INFO - 探索路径生成完成，成本: 727.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:15:48,618 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [7, 5, 6, 3, 8, 2, 4, 0, 1], 'cur_cost': 727.0, 'intermediate_solutions': [{'tour': [5, 6, 7, 0, 4, 3, 8, 2, 1], 'cur_cost': 923.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 3, 8, 4, 0, 7, 6, 2, 1], 'cur_cost': 978.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 6, 7, 0, 8, 3, 2, 4, 1], 'cur_cost': 927.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:15:48,618 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 727.00)
2025-08-04 17:15:48,618 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:15:48,618 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:15:48,619 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:48,619 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:15:48,619 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,619 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,619 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,619 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,619 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1010.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:15:48,620 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [5, 7, 0, 8, 6, 2, 4, 1, 3], 'cur_cost': 1010.0, 'intermediate_solutions': [{'tour': [4, 6, 3, 8, 5, 0, 2, 7, 1], 'cur_cost': 1043.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 3, 8, 5, 7, 2, 4, 1], 'cur_cost': 780.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 2, 3, 8, 5, 4, 7, 1], 'cur_cost': 1016.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:15:48,620 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1010.00)
2025-08-04 17:15:48,620 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:15:48,620 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:15:48,620 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:48,621 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:15:48,621 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,621 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,621 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,621 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,622 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1233.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:15:48,622 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [7, 3, 1, 8, 0, 6, 2, 5, 4], 'cur_cost': 1233.0, 'intermediate_solutions': [{'tour': [0, 4, 8, 7, 5, 6, 1, 2, 3], 'cur_cost': 937.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 8, 7, 5, 3, 6, 2, 1], 'cur_cost': 869.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 8, 2, 7, 5, 6, 3, 1], 'cur_cost': 859.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:15:48,622 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 1233.00)
2025-08-04 17:15:48,622 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:15:48,622 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:15:48,622 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:48,623 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:15:48,623 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,623 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,623 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,623 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,624 - ExplorationExpert - INFO - 探索路径生成完成，成本: 858.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:15:48,624 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [2, 7, 4, 8, 5, 3, 6, 0, 1], 'cur_cost': 858.0, 'intermediate_solutions': [{'tour': [4, 7, 6, 8, 0, 5, 1, 2, 3], 'cur_cost': 1154.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 7, 6, 3, 0, 5, 1, 8, 2], 'cur_cost': 1024.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 7, 6, 3, 0, 5, 8, 1, 2], 'cur_cost': 995.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:15:48,624 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 858.00)
2025-08-04 17:15:48,624 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:15:48,624 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:15:48,624 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:48,624 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:15:48,624 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,625 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,625 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,625 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,625 - ExplorationExpert - INFO - 探索路径生成完成，成本: 983.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:15:48,625 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [2, 3, 6, 7, 4, 5, 8, 0, 1], 'cur_cost': 983.0, 'intermediate_solutions': [{'tour': [5, 8, 3, 7, 6, 2, 4, 1, 0], 'cur_cost': 840.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 8, 3, 7, 4, 6, 2, 1, 0], 'cur_cost': 978.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 8, 3, 7, 1, 6, 4, 2, 0], 'cur_cost': 940.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:15:48,625 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 983.00)
2025-08-04 17:15:48,625 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:15:48,625 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:15:48,626 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:48,626 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:15:48,626 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,626 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,626 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,626 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,626 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1053.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:15:48,627 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [3, 6, 4, 0, 8, 7, 2, 1, 5], 'cur_cost': 1053.0, 'intermediate_solutions': [{'tour': [2, 4, 8, 3, 7, 1, 6, 0, 5], 'cur_cost': 930.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 6, 1, 7, 3, 8, 5, 0, 4], 'cur_cost': 938.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 5, 8, 3, 7, 1, 6, 4, 0], 'cur_cost': 1015.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:15:48,627 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1053.00)
2025-08-04 17:15:48,627 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-04 17:15:48,627 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:15:48,627 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:15:48,627 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 886.0
2025-08-04 17:15:48,686 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:15:48,686 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0]
2025-08-04 17:15:48,686 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 17:15:48,687 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:15:48,687 - ExploitationExpert - INFO - populations: [{'tour': [7, 5, 6, 3, 8, 2, 4, 0, 1], 'cur_cost': 727.0}, {'tour': [5, 7, 0, 8, 6, 2, 4, 1, 3], 'cur_cost': 1010.0}, {'tour': [7, 3, 1, 8, 0, 6, 2, 5, 4], 'cur_cost': 1233.0}, {'tour': [2, 7, 4, 8, 5, 3, 6, 0, 1], 'cur_cost': 858.0}, {'tour': [2, 3, 6, 7, 4, 5, 8, 0, 1], 'cur_cost': 983.0}, {'tour': [3, 6, 4, 0, 8, 7, 2, 1, 5], 'cur_cost': 1053.0}, {'tour': array([7, 4, 0, 1, 3, 2, 8, 5, 6], dtype=int64), 'cur_cost': 886.0}, {'tour': [4, 6, 3, 0, 8, 7, 5, 2, 1], 'cur_cost': 1085.0}, {'tour': [3, 4, 1, 2, 6, 5, 7, 0, 8], 'cur_cost': 995.0}, {'tour': [1, 8, 6, 4, 7, 3, 5, 0, 2], 'cur_cost': 1074.0}]
2025-08-04 17:15:48,688 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒，最大迭代次数: 10
2025-08-04 17:15:48,688 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-08-04 17:15:48,689 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([7, 4, 0, 1, 3, 2, 8, 5, 6], dtype=int64), 'cur_cost': 886.0, 'intermediate_solutions': [{'tour': array([0, 3, 4, 7, 5, 1, 8, 6, 2]), 'cur_cost': 1208.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 0, 3, 4, 5, 1, 8, 6, 2]), 'cur_cost': 1272.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 7, 0, 3, 4, 1, 8, 6, 2]), 'cur_cost': 1185.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 7, 0, 3, 5, 1, 8, 6, 2]), 'cur_cost': 1090.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 5, 7, 0, 3, 1, 8, 6, 2]), 'cur_cost': 1146.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:15:48,689 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 886.00)
2025-08-04 17:15:48,689 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-04 17:15:48,689 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:15:48,689 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:15:48,690 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 843.0
2025-08-04 17:15:48,747 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:15:48,748 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0]
2025-08-04 17:15:48,748 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 17:15:48,749 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:15:48,749 - ExploitationExpert - INFO - populations: [{'tour': [7, 5, 6, 3, 8, 2, 4, 0, 1], 'cur_cost': 727.0}, {'tour': [5, 7, 0, 8, 6, 2, 4, 1, 3], 'cur_cost': 1010.0}, {'tour': [7, 3, 1, 8, 0, 6, 2, 5, 4], 'cur_cost': 1233.0}, {'tour': [2, 7, 4, 8, 5, 3, 6, 0, 1], 'cur_cost': 858.0}, {'tour': [2, 3, 6, 7, 4, 5, 8, 0, 1], 'cur_cost': 983.0}, {'tour': [3, 6, 4, 0, 8, 7, 2, 1, 5], 'cur_cost': 1053.0}, {'tour': array([7, 4, 0, 1, 3, 2, 8, 5, 6], dtype=int64), 'cur_cost': 886.0}, {'tour': array([0, 1, 5, 8, 3, 6, 7, 4, 2], dtype=int64), 'cur_cost': 843.0}, {'tour': [3, 4, 1, 2, 6, 5, 7, 0, 8], 'cur_cost': 995.0}, {'tour': [1, 8, 6, 4, 7, 3, 5, 0, 2], 'cur_cost': 1074.0}]
2025-08-04 17:15:48,750 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒，最大迭代次数: 10
2025-08-04 17:15:48,750 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-08-04 17:15:48,750 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([0, 1, 5, 8, 3, 6, 7, 4, 2], dtype=int64), 'cur_cost': 843.0, 'intermediate_solutions': [{'tour': array([3, 6, 4, 0, 8, 7, 5, 2, 1]), 'cur_cost': 1104.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 3, 6, 4, 8, 7, 5, 2, 1]), 'cur_cost': 979.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 0, 3, 6, 4, 7, 5, 2, 1]), 'cur_cost': 1162.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 0, 3, 6, 8, 7, 5, 2, 1]), 'cur_cost': 1024.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 8, 0, 3, 6, 7, 5, 2, 1]), 'cur_cost': 1026.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:15:48,750 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 843.00)
2025-08-04 17:15:48,750 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:15:48,751 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:15:48,751 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:48,751 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:15:48,751 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,751 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,751 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,751 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:48,752 - ExplorationExpert - INFO - 探索路径生成完成，成本: 835.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:15:48,752 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [5, 7, 3, 4, 2, 0, 1, 6, 8], 'cur_cost': 835.0, 'intermediate_solutions': [{'tour': [5, 4, 1, 2, 6, 3, 7, 0, 8], 'cur_cost': 1056.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 4, 1, 0, 7, 5, 6, 2, 8], 'cur_cost': 863.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 3, 1, 2, 6, 5, 7, 0, 8], 'cur_cost': 1070.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:15:48,752 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 835.00)
2025-08-04 17:15:48,752 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-04 17:15:48,752 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:15:48,752 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:15:48,752 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 1121.0
2025-08-04 17:15:48,808 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:15:48,808 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0]
2025-08-04 17:15:48,808 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 17:15:48,809 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:15:48,809 - ExploitationExpert - INFO - populations: [{'tour': [7, 5, 6, 3, 8, 2, 4, 0, 1], 'cur_cost': 727.0}, {'tour': [5, 7, 0, 8, 6, 2, 4, 1, 3], 'cur_cost': 1010.0}, {'tour': [7, 3, 1, 8, 0, 6, 2, 5, 4], 'cur_cost': 1233.0}, {'tour': [2, 7, 4, 8, 5, 3, 6, 0, 1], 'cur_cost': 858.0}, {'tour': [2, 3, 6, 7, 4, 5, 8, 0, 1], 'cur_cost': 983.0}, {'tour': [3, 6, 4, 0, 8, 7, 2, 1, 5], 'cur_cost': 1053.0}, {'tour': array([7, 4, 0, 1, 3, 2, 8, 5, 6], dtype=int64), 'cur_cost': 886.0}, {'tour': array([0, 1, 5, 8, 3, 6, 7, 4, 2], dtype=int64), 'cur_cost': 843.0}, {'tour': [5, 7, 3, 4, 2, 0, 1, 6, 8], 'cur_cost': 835.0}, {'tour': array([5, 4, 8, 1, 7, 6, 2, 0, 3], dtype=int64), 'cur_cost': 1121.0}]
2025-08-04 17:15:48,810 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒，最大迭代次数: 10
2025-08-04 17:15:48,810 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-08-04 17:15:48,811 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([5, 4, 8, 1, 7, 6, 2, 0, 3], dtype=int64), 'cur_cost': 1121.0, 'intermediate_solutions': [{'tour': array([6, 8, 1, 4, 7, 3, 5, 0, 2]), 'cur_cost': 1083.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 6, 8, 1, 7, 3, 5, 0, 2]), 'cur_cost': 1022.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 4, 6, 8, 1, 3, 5, 0, 2]), 'cur_cost': 1171.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 4, 6, 8, 7, 3, 5, 0, 2]), 'cur_cost': 997.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 7, 4, 6, 8, 3, 5, 0, 2]), 'cur_cost': 1041.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:15:48,811 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1121.00)
2025-08-04 17:15:48,811 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:15:48,811 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:15:48,812 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [7, 5, 6, 3, 8, 2, 4, 0, 1], 'cur_cost': 727.0, 'intermediate_solutions': [{'tour': [5, 6, 7, 0, 4, 3, 8, 2, 1], 'cur_cost': 923.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 3, 8, 4, 0, 7, 6, 2, 1], 'cur_cost': 978.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 6, 7, 0, 8, 3, 2, 4, 1], 'cur_cost': 927.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [5, 7, 0, 8, 6, 2, 4, 1, 3], 'cur_cost': 1010.0, 'intermediate_solutions': [{'tour': [4, 6, 3, 8, 5, 0, 2, 7, 1], 'cur_cost': 1043.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 3, 8, 5, 7, 2, 4, 1], 'cur_cost': 780.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 2, 3, 8, 5, 4, 7, 1], 'cur_cost': 1016.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [7, 3, 1, 8, 0, 6, 2, 5, 4], 'cur_cost': 1233.0, 'intermediate_solutions': [{'tour': [0, 4, 8, 7, 5, 6, 1, 2, 3], 'cur_cost': 937.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 8, 7, 5, 3, 6, 2, 1], 'cur_cost': 869.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 8, 2, 7, 5, 6, 3, 1], 'cur_cost': 859.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [2, 7, 4, 8, 5, 3, 6, 0, 1], 'cur_cost': 858.0, 'intermediate_solutions': [{'tour': [4, 7, 6, 8, 0, 5, 1, 2, 3], 'cur_cost': 1154.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 7, 6, 3, 0, 5, 1, 8, 2], 'cur_cost': 1024.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 7, 6, 3, 0, 5, 8, 1, 2], 'cur_cost': 995.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [2, 3, 6, 7, 4, 5, 8, 0, 1], 'cur_cost': 983.0, 'intermediate_solutions': [{'tour': [5, 8, 3, 7, 6, 2, 4, 1, 0], 'cur_cost': 840.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 8, 3, 7, 4, 6, 2, 1, 0], 'cur_cost': 978.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 8, 3, 7, 1, 6, 4, 2, 0], 'cur_cost': 940.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [3, 6, 4, 0, 8, 7, 2, 1, 5], 'cur_cost': 1053.0, 'intermediate_solutions': [{'tour': [2, 4, 8, 3, 7, 1, 6, 0, 5], 'cur_cost': 930.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 6, 1, 7, 3, 8, 5, 0, 4], 'cur_cost': 938.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 5, 8, 3, 7, 1, 6, 4, 0], 'cur_cost': 1015.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 4, 0, 1, 3, 2, 8, 5, 6], dtype=int64), 'cur_cost': 886.0, 'intermediate_solutions': [{'tour': array([0, 3, 4, 7, 5, 1, 8, 6, 2]), 'cur_cost': 1208.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 0, 3, 4, 5, 1, 8, 6, 2]), 'cur_cost': 1272.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 7, 0, 3, 4, 1, 8, 6, 2]), 'cur_cost': 1185.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 7, 0, 3, 5, 1, 8, 6, 2]), 'cur_cost': 1090.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 5, 7, 0, 3, 1, 8, 6, 2]), 'cur_cost': 1146.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 1, 5, 8, 3, 6, 7, 4, 2], dtype=int64), 'cur_cost': 843.0, 'intermediate_solutions': [{'tour': array([3, 6, 4, 0, 8, 7, 5, 2, 1]), 'cur_cost': 1104.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 3, 6, 4, 8, 7, 5, 2, 1]), 'cur_cost': 979.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 0, 3, 6, 4, 7, 5, 2, 1]), 'cur_cost': 1162.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 0, 3, 6, 8, 7, 5, 2, 1]), 'cur_cost': 1024.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 8, 0, 3, 6, 7, 5, 2, 1]), 'cur_cost': 1026.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [5, 7, 3, 4, 2, 0, 1, 6, 8], 'cur_cost': 835.0, 'intermediate_solutions': [{'tour': [5, 4, 1, 2, 6, 3, 7, 0, 8], 'cur_cost': 1056.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 4, 1, 0, 7, 5, 6, 2, 8], 'cur_cost': 863.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 3, 1, 2, 6, 5, 7, 0, 8], 'cur_cost': 1070.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([5, 4, 8, 1, 7, 6, 2, 0, 3], dtype=int64), 'cur_cost': 1121.0, 'intermediate_solutions': [{'tour': array([6, 8, 1, 4, 7, 3, 5, 0, 2]), 'cur_cost': 1083.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 6, 8, 1, 7, 3, 5, 0, 2]), 'cur_cost': 1022.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 4, 6, 8, 1, 3, 5, 0, 2]), 'cur_cost': 1171.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 4, 6, 8, 7, 3, 5, 0, 2]), 'cur_cost': 997.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 7, 4, 6, 8, 3, 5, 0, 2]), 'cur_cost': 1041.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-04 17:15:48,813 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:15:48,813 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:15:48,814 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=727.000, 多样性=0.884
2025-08-04 17:15:48,814 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-04 17:15:48,814 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-04 17:15:48,815 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:15:48,815 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.014552278682175845, 'best_improvement': 0.12515042117930206}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.00280112044817936}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.05333166429723534, 'recent_improvements': [-0.004000213846156754, 0.030284913005834774, -0.11066354244062743], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:15:48,815 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-04 17:15:48,815 - __main__ - INFO - simple1_9 开始进化第 5 代
2025-08-04 17:15:48,815 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-04 17:15:48,815 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:15:48,816 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=727.000, 多样性=0.884
2025-08-04 17:15:48,816 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:15:48,816 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.884
2025-08-04 17:15:48,816 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:15:48,817 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.630
2025-08-04 17:15:48,818 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-04 17:15:48,818 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:15:48,819 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-04 17:15:48,819 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-04 17:15:48,826 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.308, 适应度梯度: -3.923, 聚类评分: 0.000, 覆盖率: 0.005, 收敛趋势: 0.000, 多样性: 0.606
2025-08-04 17:15:48,826 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-04 17:15:48,826 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:15:48,826 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:15:48,829 - visualization.landscape_visualizer - INFO - 插值约束: 84 个点被约束到最小值 680.00
2025-08-04 17:15:48,835 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:15:48,942 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_5_20250804_171548.html
2025-08-04 17:15:49,004 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_5_20250804_171548.html
2025-08-04 17:15:49,004 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 5
2025-08-04 17:15:49,004 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-04 17:15:49,004 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1858秒
2025-08-04 17:15:49,005 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3076923076923077, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -3.9230769230769273, 'local_optima_density': 0.3076923076923077, 'gradient_variance': 26754.957159763315, 'cluster_count': 0}, 'population_state': {'diversity': 0.6055226824457594, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0052, 'fitness_entropy': 0.9345154830993554, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -3.923)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.005)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754298948.8268032, 'performance_metrics': {}}}
2025-08-04 17:15:49,005 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:15:49,005 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:15:49,005 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:15:49,005 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:15:49,005 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-04 17:15:49,005 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-04 17:15:49,005 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-04 17:15:49,006 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:15:49,006 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:15:49,006 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-04 17:15:49,006 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:15:49,006 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:15:49,006 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 8} (总数: 2, 保护比例: 0.20)
2025-08-04 17:15:49,006 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:15:49,007 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:15:49,007 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:49,007 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:15:49,007 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:49,007 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:49,007 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:49,007 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:49,008 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1106.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:15:49,008 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [1, 4, 0, 3, 2, 5, 7, 6, 8], 'cur_cost': 1106.0, 'intermediate_solutions': [{'tour': [7, 5, 2, 3, 8, 6, 4, 0, 1], 'cur_cost': 1013.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 6, 5, 7, 8, 2, 4, 0, 1], 'cur_cost': 775.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 5, 6, 8, 2, 4, 3, 0, 1], 'cur_cost': 849.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:15:49,008 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 1106.00)
2025-08-04 17:15:49,008 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:15:49,008 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:15:49,008 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:49,008 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:15:49,008 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:49,009 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:49,009 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:49,009 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:49,009 - ExplorationExpert - INFO - 探索路径生成完成，成本: 954.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:15:49,009 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [3, 4, 1, 7, 6, 5, 8, 2, 0], 'cur_cost': 954.0, 'intermediate_solutions': [{'tour': [3, 7, 0, 8, 6, 2, 4, 1, 5], 'cur_cost': 1000.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 5, 8, 6, 2, 4, 1, 3], 'cur_cost': 1034.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 0, 6, 2, 4, 1, 8, 3], 'cur_cost': 919.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:15:49,009 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 954.00)
2025-08-04 17:15:49,009 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-04 17:15:49,009 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:15:49,010 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:15:49,010 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 880.0
2025-08-04 17:15:49,072 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:15:49,072 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0]
2025-08-04 17:15:49,072 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 17:15:49,073 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:15:49,074 - ExploitationExpert - INFO - populations: [{'tour': [1, 4, 0, 3, 2, 5, 7, 6, 8], 'cur_cost': 1106.0}, {'tour': [3, 4, 1, 7, 6, 5, 8, 2, 0], 'cur_cost': 954.0}, {'tour': array([0, 6, 7, 5, 8, 3, 1, 4, 2], dtype=int64), 'cur_cost': 880.0}, {'tour': [2, 7, 4, 8, 5, 3, 6, 0, 1], 'cur_cost': 858.0}, {'tour': [2, 3, 6, 7, 4, 5, 8, 0, 1], 'cur_cost': 983.0}, {'tour': [3, 6, 4, 0, 8, 7, 2, 1, 5], 'cur_cost': 1053.0}, {'tour': [7, 4, 0, 1, 3, 2, 8, 5, 6], 'cur_cost': 886.0}, {'tour': [0, 1, 5, 8, 3, 6, 7, 4, 2], 'cur_cost': 843.0}, {'tour': [5, 7, 3, 4, 2, 0, 1, 6, 8], 'cur_cost': 835.0}, {'tour': [5, 4, 8, 1, 7, 6, 2, 0, 3], 'cur_cost': 1121.0}]
2025-08-04 17:15:49,074 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒，最大迭代次数: 10
2025-08-04 17:15:49,074 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-08-04 17:15:49,075 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([0, 6, 7, 5, 8, 3, 1, 4, 2], dtype=int64), 'cur_cost': 880.0, 'intermediate_solutions': [{'tour': array([1, 3, 7, 8, 0, 6, 2, 5, 4]), 'cur_cost': 1156.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 1, 3, 7, 0, 6, 2, 5, 4]), 'cur_cost': 1181.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 8, 1, 3, 7, 6, 2, 5, 4]), 'cur_cost': 1208.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 8, 1, 3, 0, 6, 2, 5, 4]), 'cur_cost': 1246.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 0, 8, 1, 3, 6, 2, 5, 4]), 'cur_cost': 1260.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:15:49,075 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 880.00)
2025-08-04 17:15:49,075 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:15:49,075 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:15:49,075 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:49,076 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:15:49,076 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:49,076 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:49,076 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:49,076 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:49,076 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1024.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:15:49,076 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [2, 5, 0, 7, 3, 8, 4, 6, 1], 'cur_cost': 1024.0, 'intermediate_solutions': [{'tour': [2, 4, 7, 8, 5, 3, 6, 0, 1], 'cur_cost': 774.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 7, 4, 8, 5, 6, 3, 0, 1], 'cur_cost': 893.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 6, 7, 4, 8, 5, 3, 0, 1], 'cur_cost': 925.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:15:49,077 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1024.00)
2025-08-04 17:15:49,077 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:15:49,077 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:15:49,077 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:49,077 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:15:49,077 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:49,077 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:49,077 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:49,078 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:49,078 - ExplorationExpert - INFO - 探索路径生成完成，成本: 930.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:15:49,078 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [2, 7, 0, 5, 6, 3, 8, 4, 1], 'cur_cost': 930.0, 'intermediate_solutions': [{'tour': [7, 3, 6, 2, 4, 5, 8, 0, 1], 'cur_cost': 950.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 3, 6, 1, 0, 8, 5, 4, 7], 'cur_cost': 1013.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 6, 7, 2, 4, 5, 8, 0, 1], 'cur_cost': 952.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:15:49,078 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 930.00)
2025-08-04 17:15:49,078 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-04 17:15:49,078 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:15:49,078 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:15:49,078 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 1055.0
2025-08-04 17:15:49,144 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:15:49,145 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0]
2025-08-04 17:15:49,145 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 17:15:49,146 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:15:49,146 - ExploitationExpert - INFO - populations: [{'tour': [1, 4, 0, 3, 2, 5, 7, 6, 8], 'cur_cost': 1106.0}, {'tour': [3, 4, 1, 7, 6, 5, 8, 2, 0], 'cur_cost': 954.0}, {'tour': array([0, 6, 7, 5, 8, 3, 1, 4, 2], dtype=int64), 'cur_cost': 880.0}, {'tour': [2, 5, 0, 7, 3, 8, 4, 6, 1], 'cur_cost': 1024.0}, {'tour': [2, 7, 0, 5, 6, 3, 8, 4, 1], 'cur_cost': 930.0}, {'tour': array([8, 0, 7, 3, 4, 2, 5, 1, 6], dtype=int64), 'cur_cost': 1055.0}, {'tour': [7, 4, 0, 1, 3, 2, 8, 5, 6], 'cur_cost': 886.0}, {'tour': [0, 1, 5, 8, 3, 6, 7, 4, 2], 'cur_cost': 843.0}, {'tour': [5, 7, 3, 4, 2, 0, 1, 6, 8], 'cur_cost': 835.0}, {'tour': [5, 4, 8, 1, 7, 6, 2, 0, 3], 'cur_cost': 1121.0}]
2025-08-04 17:15:49,147 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:15:49,147 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-08-04 17:15:49,147 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([8, 0, 7, 3, 4, 2, 5, 1, 6], dtype=int64), 'cur_cost': 1055.0, 'intermediate_solutions': [{'tour': array([4, 6, 3, 0, 8, 7, 2, 1, 5]), 'cur_cost': 1172.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 4, 6, 3, 8, 7, 2, 1, 5]), 'cur_cost': 1060.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 0, 4, 6, 3, 7, 2, 1, 5]), 'cur_cost': 1064.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 0, 4, 6, 8, 7, 2, 1, 5]), 'cur_cost': 1080.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 8, 0, 4, 6, 7, 2, 1, 5]), 'cur_cost': 1026.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:15:49,148 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1055.00)
2025-08-04 17:15:49,148 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:15:49,148 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:15:49,148 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:49,148 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:15:49,149 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:49,149 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:49,149 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:49,149 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:49,149 - ExplorationExpert - INFO - 探索路径生成完成，成本: 976.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:15:49,150 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [4, 6, 2, 7, 3, 5, 8, 0, 1], 'cur_cost': 976.0, 'intermediate_solutions': [{'tour': [7, 4, 0, 1, 3, 5, 8, 2, 6], 'cur_cost': 918.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 4, 0, 1, 5, 8, 2, 3, 6], 'cur_cost': 917.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 4, 0, 1, 2, 3, 8, 5, 6], 'cur_cost': 837.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:15:49,150 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 976.00)
2025-08-04 17:15:49,150 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:15:49,150 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:15:49,150 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:49,150 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:15:49,150 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:49,151 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:49,151 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:49,151 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:49,151 - ExplorationExpert - INFO - 探索路径生成完成，成本: 912.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:15:49,151 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [3, 7, 6, 8, 5, 4, 2, 0, 1], 'cur_cost': 912.0, 'intermediate_solutions': [{'tour': [5, 1, 0, 8, 3, 6, 7, 4, 2], 'cur_cost': 927.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 7, 6, 3, 8, 5, 1, 2], 'cur_cost': 968.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 1, 5, 8, 3, 6, 4, 2], 'cur_cost': 983.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:15:49,151 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 912.00)
2025-08-04 17:15:49,151 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:15:49,151 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:15:49,151 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:15:49,152 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:15:49,152 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:49,152 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:49,152 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:49,152 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:15:49,152 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1027.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:15:49,152 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 6, 2, 3, 7, 8, 4, 5, 1], 'cur_cost': 1027.0, 'intermediate_solutions': [{'tour': [5, 4, 3, 7, 2, 0, 1, 6, 8], 'cur_cost': 981.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 7, 3, 4, 2, 0, 8, 6, 1], 'cur_cost': 996.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 4, 2, 0, 1, 6, 8, 3], 'cur_cost': 789.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:15:49,153 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 1027.00)
2025-08-04 17:15:49,153 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-04 17:15:49,153 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:15:49,153 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:15:49,153 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 1097.0
2025-08-04 17:15:49,216 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:15:49,216 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0]
2025-08-04 17:15:49,216 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 17:15:49,217 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:15:49,217 - ExploitationExpert - INFO - populations: [{'tour': [1, 4, 0, 3, 2, 5, 7, 6, 8], 'cur_cost': 1106.0}, {'tour': [3, 4, 1, 7, 6, 5, 8, 2, 0], 'cur_cost': 954.0}, {'tour': array([0, 6, 7, 5, 8, 3, 1, 4, 2], dtype=int64), 'cur_cost': 880.0}, {'tour': [2, 5, 0, 7, 3, 8, 4, 6, 1], 'cur_cost': 1024.0}, {'tour': [2, 7, 0, 5, 6, 3, 8, 4, 1], 'cur_cost': 930.0}, {'tour': array([8, 0, 7, 3, 4, 2, 5, 1, 6], dtype=int64), 'cur_cost': 1055.0}, {'tour': [4, 6, 2, 7, 3, 5, 8, 0, 1], 'cur_cost': 976.0}, {'tour': [3, 7, 6, 8, 5, 4, 2, 0, 1], 'cur_cost': 912.0}, {'tour': [0, 6, 2, 3, 7, 8, 4, 5, 1], 'cur_cost': 1027.0}, {'tour': array([8, 6, 7, 0, 2, 1, 3, 4, 5], dtype=int64), 'cur_cost': 1097.0}]
2025-08-04 17:15:49,218 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒，最大迭代次数: 10
2025-08-04 17:15:49,218 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-08-04 17:15:49,218 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([8, 6, 7, 0, 2, 1, 3, 4, 5], dtype=int64), 'cur_cost': 1097.0, 'intermediate_solutions': [{'tour': array([8, 4, 5, 1, 7, 6, 2, 0, 3]), 'cur_cost': 1127.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 8, 4, 5, 7, 6, 2, 0, 3]), 'cur_cost': 1170.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 1, 8, 4, 5, 6, 2, 0, 3]), 'cur_cost': 1108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 1, 8, 4, 7, 6, 2, 0, 3]), 'cur_cost': 1114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 7, 1, 8, 4, 6, 2, 0, 3]), 'cur_cost': 1118.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:15:49,219 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1097.00)
2025-08-04 17:15:49,219 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:15:49,219 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:15:49,220 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 0, 3, 2, 5, 7, 6, 8], 'cur_cost': 1106.0, 'intermediate_solutions': [{'tour': [7, 5, 2, 3, 8, 6, 4, 0, 1], 'cur_cost': 1013.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 6, 5, 7, 8, 2, 4, 0, 1], 'cur_cost': 775.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 5, 6, 8, 2, 4, 3, 0, 1], 'cur_cost': 849.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [3, 4, 1, 7, 6, 5, 8, 2, 0], 'cur_cost': 954.0, 'intermediate_solutions': [{'tour': [3, 7, 0, 8, 6, 2, 4, 1, 5], 'cur_cost': 1000.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 5, 8, 6, 2, 4, 1, 3], 'cur_cost': 1034.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 0, 6, 2, 4, 1, 8, 3], 'cur_cost': 919.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 6, 7, 5, 8, 3, 1, 4, 2], dtype=int64), 'cur_cost': 880.0, 'intermediate_solutions': [{'tour': array([1, 3, 7, 8, 0, 6, 2, 5, 4]), 'cur_cost': 1156.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 1, 3, 7, 0, 6, 2, 5, 4]), 'cur_cost': 1181.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 8, 1, 3, 7, 6, 2, 5, 4]), 'cur_cost': 1208.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 8, 1, 3, 0, 6, 2, 5, 4]), 'cur_cost': 1246.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 0, 8, 1, 3, 6, 2, 5, 4]), 'cur_cost': 1260.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [2, 5, 0, 7, 3, 8, 4, 6, 1], 'cur_cost': 1024.0, 'intermediate_solutions': [{'tour': [2, 4, 7, 8, 5, 3, 6, 0, 1], 'cur_cost': 774.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 7, 4, 8, 5, 6, 3, 0, 1], 'cur_cost': 893.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 6, 7, 4, 8, 5, 3, 0, 1], 'cur_cost': 925.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [2, 7, 0, 5, 6, 3, 8, 4, 1], 'cur_cost': 930.0, 'intermediate_solutions': [{'tour': [7, 3, 6, 2, 4, 5, 8, 0, 1], 'cur_cost': 950.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 3, 6, 1, 0, 8, 5, 4, 7], 'cur_cost': 1013.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 6, 7, 2, 4, 5, 8, 0, 1], 'cur_cost': 952.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 0, 7, 3, 4, 2, 5, 1, 6], dtype=int64), 'cur_cost': 1055.0, 'intermediate_solutions': [{'tour': array([4, 6, 3, 0, 8, 7, 2, 1, 5]), 'cur_cost': 1172.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 4, 6, 3, 8, 7, 2, 1, 5]), 'cur_cost': 1060.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 0, 4, 6, 3, 7, 2, 1, 5]), 'cur_cost': 1064.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 0, 4, 6, 8, 7, 2, 1, 5]), 'cur_cost': 1080.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 8, 0, 4, 6, 7, 2, 1, 5]), 'cur_cost': 1026.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [4, 6, 2, 7, 3, 5, 8, 0, 1], 'cur_cost': 976.0, 'intermediate_solutions': [{'tour': [7, 4, 0, 1, 3, 5, 8, 2, 6], 'cur_cost': 918.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 4, 0, 1, 5, 8, 2, 3, 6], 'cur_cost': 917.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 4, 0, 1, 2, 3, 8, 5, 6], 'cur_cost': 837.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [3, 7, 6, 8, 5, 4, 2, 0, 1], 'cur_cost': 912.0, 'intermediate_solutions': [{'tour': [5, 1, 0, 8, 3, 6, 7, 4, 2], 'cur_cost': 927.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 7, 6, 3, 8, 5, 1, 2], 'cur_cost': 968.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 1, 5, 8, 3, 6, 4, 2], 'cur_cost': 983.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 2, 3, 7, 8, 4, 5, 1], 'cur_cost': 1027.0, 'intermediate_solutions': [{'tour': [5, 4, 3, 7, 2, 0, 1, 6, 8], 'cur_cost': 981.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 7, 3, 4, 2, 0, 8, 6, 1], 'cur_cost': 996.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 4, 2, 0, 1, 6, 8, 3], 'cur_cost': 789.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 6, 7, 0, 2, 1, 3, 4, 5], dtype=int64), 'cur_cost': 1097.0, 'intermediate_solutions': [{'tour': array([8, 4, 5, 1, 7, 6, 2, 0, 3]), 'cur_cost': 1127.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 8, 4, 5, 7, 6, 2, 0, 3]), 'cur_cost': 1170.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 1, 8, 4, 5, 6, 2, 0, 3]), 'cur_cost': 1108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 1, 8, 4, 7, 6, 2, 0, 3]), 'cur_cost': 1114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 7, 1, 8, 4, 6, 2, 0, 3]), 'cur_cost': 1118.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-04 17:15:49,220 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:15:49,220 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:15:49,221 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=880.000, 多样性=0.869
2025-08-04 17:15:49,221 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-04 17:15:49,222 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-04 17:15:49,222 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:15:49,222 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.05019966277990789, 'best_improvement': -0.21045392022008252}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.016759776536312877}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.007866317161829464, 'recent_improvements': [0.030284913005834774, -0.11066354244062743, 0.014552278682175845], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:15:49,222 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-04 17:15:49,224 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_solution.json
2025-08-04 17:15:49,225 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_20250804_171549.solution
2025-08-04 17:15:49,238 - __main__ - INFO - 评估统计 - 总次数: 228823.33333314123, 运行时间: 4.70s, 最佳成本: 680.0
2025-08-04 17:15:49,238 - __main__ - INFO - 实例 simple1_9 处理完成
