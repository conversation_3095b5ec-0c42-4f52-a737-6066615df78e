"""
评估配置管理

提供配置文件管理和运行时配置功能。
"""

import json
import os
from dataclasses import dataclass, asdict
from typing import Dict, Any, Optional, List, Callable


@dataclass
class EvaluationConfig:
    """评估统计配置"""
    # 基础配置
    enabled: bool = False
    detailed_logging: bool = False
    max_records: int = 10000
    batch_size: int = 100
    
    # 监控配置
    enable_monitoring: bool = False
    monitoring_interval: float = 1.0
    
    # 警报阈值
    alert_thresholds: Dict[str, float] = None
    
    # 导出配置
    export_format: str = "json"  # json, csv, txt
    auto_export: bool = False
    export_interval: int = 3600  # 秒
    
    # 性能配置
    use_numba_counter: bool = False  # 默认关闭，因为可能没有numba
    thread_safe: bool = True
    
    # 清理配置
    auto_reset_interval: int = 0  # 0表示不自动重置
    max_memory_mb: float = 100.0
    
    def __post_init__(self):
        if self.alert_thresholds is None:
            self.alert_thresholds = {
                'max_evaluations': 1000000,
                'max_evaluations_per_second': 10000,
                'efficiency_threshold': 0.1,
                'memory_usage_mb': 50.0,
                'phase_duration_seconds': 300
            }


class EvaluationConfigManager:
    """评估配置管理器"""
    
    def __init__(self, config_file: str = "config/evaluation_config.json"):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config = self.load_config()
        self._watchers: List[Callable[[EvaluationConfig], None]] = []
    
    def load_config(self) -> EvaluationConfig:
        """加载配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_dict = json.load(f)
                return EvaluationConfig(**config_dict)
            else:
                # 创建默认配置
                config = EvaluationConfig()
                self.save_config(config)
                return config
        except Exception as e:
            print(f"配置加载失败，使用默认配置: {e}")
            return EvaluationConfig()
    
    def save_config(self, config: Optional[EvaluationConfig] = None) -> bool:
        """保存配置"""
        config = config or self.config
        try:
            # 确保目录存在
            config_dir = os.path.dirname(self.config_file)
            if config_dir:
                os.makedirs(config_dir, exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(config), f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"配置保存失败: {e}")
            return False
    
    def update_config(self, **kwargs) -> bool:
        """更新配置"""
        try:
            for key, value in kwargs.items():
                if hasattr(self.config, key):
                    setattr(self.config, key, value)
                else:
                    print(f"警告: 未知配置项 {key}")
                    return False
            
            success = self.save_config()
            
            if success:
                # 通知观察者
                for watcher in self._watchers:
                    try:
                        watcher(self.config)
                    except Exception as e:
                        print(f"配置观察者通知失败: {e}")
            
            return success
        except Exception as e:
            print(f"配置更新失败: {e}")
            return False
    
    def add_config_watcher(self, watcher: Callable[[EvaluationConfig], None]) -> None:
        """添加配置观察者"""
        self._watchers.append(watcher)
    
    def remove_config_watcher(self, watcher: Callable[[EvaluationConfig], None]) -> bool:
        """移除配置观察者"""
        try:
            self._watchers.remove(watcher)
            return True
        except ValueError:
            return False
    
    def validate_config(self) -> List[str]:
        """验证配置"""
        issues = []
        
        if self.config.max_records <= 0:
            issues.append("max_records 必须大于 0")
        
        if self.config.batch_size <= 0:
            issues.append("batch_size 必须大于 0")
        
        if self.config.monitoring_interval <= 0:
            issues.append("monitoring_interval 必须大于 0")
        
        if self.config.max_memory_mb <= 0:
            issues.append("max_memory_mb 必须大于 0")
        
        # 验证警报阈值
        for key, value in self.config.alert_thresholds.items():
            if not isinstance(value, (int, float)) or value <= 0:
                issues.append(f"警报阈值 {key} 必须是正数")
        
        # 验证导出格式
        valid_formats = ['json', 'csv', 'txt']
        if self.config.export_format not in valid_formats:
            issues.append(f"export_format 必须是 {valid_formats} 中的一个")
        
        return issues
    
    def reset_to_defaults(self) -> bool:
        """重置为默认配置"""
        try:
            self.config = EvaluationConfig()
            return self.save_config()
        except Exception as e:
            print(f"重置配置失败: {e}")
            return False
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            'enabled': self.config.enabled,
            'detailed_logging': self.config.detailed_logging,
            'max_records': self.config.max_records,
            'use_numba_counter': self.config.use_numba_counter,
            'enable_monitoring': self.config.enable_monitoring,
            'max_memory_mb': self.config.max_memory_mb,
            'config_file': self.config_file,
            'validation_issues': len(self.validate_config())
        }


# 全局配置管理器实例
_global_config_manager = None


def get_global_config_manager() -> EvaluationConfigManager:
    """获取全局配置管理器"""
    global _global_config_manager
    if _global_config_manager is None:
        _global_config_manager = EvaluationConfigManager()
    return _global_config_manager


def get_config() -> EvaluationConfig:
    """获取当前配置"""
    return get_global_config_manager().config


def update_global_config(**kwargs) -> bool:
    """更新全局配置"""
    return get_global_config_manager().update_config(**kwargs)


def save_global_config() -> bool:
    """保存全局配置"""
    return get_global_config_manager().save_config()


def reset_global_config() -> bool:
    """重置全局配置"""
    return get_global_config_manager().reset_to_defaults()


def validate_global_config() -> List[str]:
    """验证全局配置"""
    return get_global_config_manager().validate_config()


# 配置预设
class ConfigPresets:
    """配置预设"""
    
    @staticmethod
    def development() -> EvaluationConfig:
        """开发环境配置"""
        return EvaluationConfig(
            enabled=True,
            detailed_logging=True,
            max_records=5000,
            batch_size=50,
            enable_monitoring=True,
            monitoring_interval=0.5,
            use_numba_counter=False,
            max_memory_mb=50.0
        )
    
    @staticmethod
    def production() -> EvaluationConfig:
        """生产环境配置"""
        return EvaluationConfig(
            enabled=True,
            detailed_logging=False,
            max_records=1000,
            batch_size=200,
            enable_monitoring=False,
            use_numba_counter=True,
            max_memory_mb=20.0
        )
    
    @staticmethod
    def performance_test() -> EvaluationConfig:
        """性能测试配置"""
        return EvaluationConfig(
            enabled=True,
            detailed_logging=False,
            max_records=0,  # 不保存详细记录
            batch_size=1000,
            enable_monitoring=True,
            monitoring_interval=2.0,
            use_numba_counter=True,
            max_memory_mb=10.0
        )
    
    @staticmethod
    def debug() -> EvaluationConfig:
        """调试配置"""
        return EvaluationConfig(
            enabled=True,
            detailed_logging=True,
            max_records=10000,
            batch_size=10,
            enable_monitoring=True,
            monitoring_interval=0.1,
            use_numba_counter=False,
            max_memory_mb=100.0,
            alert_thresholds={
                'max_evaluations': 100000,
                'max_evaluations_per_second': 1000,
                'efficiency_threshold': 0.01,
                'memory_usage_mb': 100.0,
                'phase_duration_seconds': 60
            }
        )
