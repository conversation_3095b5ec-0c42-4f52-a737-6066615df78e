2025-08-03 16:41:35,908 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-08-03 16:41:35,908 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-03 16:41:35,911 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:41:35,925 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=9941.000, 多样性=0.976
2025-08-03 16:41:35,932 - PathExpert - INFO - 开始路径结构分析
2025-08-03 16:41:35,938 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.976
2025-08-03 16:41:35,941 - EliteExpert - INFO - 开始精英解分析
2025-08-03 16:41:35,943 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/1)
2025-08-03 16:41:35,943 - LandscapeExpert - INFO - 使用直接传入的种群数据: 20个个体
2025-08-03 16:41:35,943 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-03 16:41:35,943 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-03 16:41:36,210 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: 1772.580, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 1.000
2025-08-03 16:41:36,211 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-03 16:41:36,212 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-03 16:41:36,281 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记
2025-08-03 16:41:36,627 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_1_20250803_164136.html
2025-08-03 16:41:36,674 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_1_20250803_164136.html
2025-08-03 16:41:36,674 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-03 16:41:36,674 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-03 16:41:36,675 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.7317秒
2025-08-03 16:41:36,675 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-03 16:41:36,675 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 1772.579999999998, 'local_optima_density': 0.1, 'gradient_variance': 2431420011.3956, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.002, 'fitness_entropy': 0.8829786604745875, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 1772.580)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 1, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754210496.2114573, 'performance_metrics': {}}}
2025-08-03 16:41:36,676 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-03 16:41:36,676 - experts.strategy.enhanced_strategy_expert - INFO - 开始增强策略分配分析 (迭代 0)
2025-08-03 16:41:36,677 - experts.analysis.individual_state_analyzer - INFO - 开始分析种群状态 (迭代 0, 种群大小: 20)
2025-08-03 16:41:36,677 - experts.strategy.enhanced_strategy_expert - ERROR - 策略分配过程中发生错误: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:41:36,678 - experts.strategy.enhanced_strategy_expert - WARNING - 使用回退策略分配
2025-08-03 16:41:36,678 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 16:41:36,679 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:41:36,679 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-03 16:41:36,679 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 16:41:36,680 - __main__ - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:41:36,682 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-03 16:41:36,683 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 3, 4, 5} (总数: 4, 保护比例: 0.20)
2025-08-03 16:41:36,684 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-03 16:41:36,686 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-03 16:41:36,687 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:41:36,693 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:41:36,694 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:41:36,875 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12409.0, 路径长度: 66
2025-08-03 16:41:36,875 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 20, 14, 21, 13, 23, 16, 18, 12, 22, 15, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12409.0}
2025-08-03 16:41:36,876 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 12409.00)
2025-08-03 16:41:36,876 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-03 16:41:36,876 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:41:36,879 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:41:36,883 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 97611.0
2025-08-03 16:41:39,077 - ExploitationExpert - INFO - res_population_num: 1
2025-08-03 16:41:39,078 - ExploitationExpert - INFO - res_population_costs: [9906.0]
2025-08-03 16:41:39,078 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 63, 52, 65, 53, 62,
       59, 56, 58, 60, 64, 57, 54, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64)]
2025-08-03 16:41:39,083 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:41:39,083 - ExploitationExpert - INFO - populations: [{'tour': [0, 20, 14, 21, 13, 23, 16, 18, 12, 22, 15, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12409.0}, {'tour': array([42,  8, 18, 40, 51, 62, 20, 58, 35,  2, 43, 57, 44, 60, 54, 23, 41,
       45, 48, 36, 19,  5, 55,  7, 50, 33, 38, 24, 12, 30,  6,  4, 61, 56,
       10, 14, 46, 53, 16, 59, 26, 52,  3, 17, 15, 28, 11,  0, 37, 49, 34,
       65, 64, 21, 47, 22, 13,  9, 25, 29, 27, 31, 32,  1, 63, 39],
      dtype=int64), 'cur_cost': 97611.0}, {'tour': array([56, 59, 62, 53, 64, 57, 54, 60, 58, 55, 61, 65, 52, 63,  2,  8,  5,
        4,  6,  9, 11,  7,  3,  1,  0, 10, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29,
       32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42],
      dtype=int64), 'cur_cost': 10030.0}, {'tour': array([42, 50, 38, 51, 45, 44, 41, 39, 47, 49, 40, 43, 48, 46, 21, 20, 13,
       23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30,
       34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,  2,  6, 10,
        0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63],
      dtype=int64), 'cur_cost': 9961.0}, {'tour': array([40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 21, 20, 13,
       23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30,
       34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,  2,  6, 10,
        0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63],
      dtype=int64), 'cur_cost': 9975.0}, {'tour': array([27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 18, 16, 23,
       22, 12, 17, 15, 14, 20, 21, 13, 19,  9, 11,  7,  3,  1,  0, 10,  8,
        2,  6,  4,  5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9941.0}, {'tour': array([37, 16, 59, 42, 21, 53, 51, 45, 63, 47, 13, 18, 20, 48, 17, 58, 31,
       44, 61, 50,  4, 49, 11, 33, 40, 55,  3,  6, 32,  5, 64, 14, 65, 26,
        8, 24, 23, 30, 19, 10,  2, 36, 60, 41, 54,  0,  7, 52,  1, 22, 27,
       46,  9, 38, 29, 57, 43, 62, 12, 34, 15, 28, 35, 39, 25, 56],
      dtype=int64), 'cur_cost': 120824.0}, {'tour': array([11, 15, 32, 27, 62, 63, 51, 13,  0, 57, 64, 35, 53, 22,  5,  8, 16,
       10, 43, 33, 21, 48, 19, 46,  1,  7, 42,  2, 18, 47, 39, 37, 58, 24,
       45, 29, 59, 20, 30, 23,  4, 54, 28, 60, 61, 49, 44, 52, 56, 26, 55,
        9, 34,  6, 65, 41, 40, 12, 25, 14, 31, 50, 17,  3, 36, 38],
      dtype=int64), 'cur_cost': 109121.0}, {'tour': array([26, 29, 30, 33, 40, 19, 65, 16, 59, 31, 28, 48, 13, 21,  8, 43, 20,
       61, 45, 27,  3, 10,  9, 41,  2, 17,  0, 57, 35, 11, 22, 47, 46,  4,
       54,  6, 56, 49, 25, 53, 39, 15,  1, 23, 63, 51, 14, 55, 32, 42, 44,
       24, 60, 62, 50, 18, 36, 12, 34,  5, 37, 52,  7, 38, 58, 64],
      dtype=int64), 'cur_cost': 108806.0}, {'tour': array([24, 21, 49, 34, 44,  0, 54,  5, 22, 42, 25, 10, 41,  7, 11, 31, 26,
        4, 43, 55, 30, 23, 32, 64, 57, 18, 45, 27, 58, 50, 46, 28,  9, 38,
       17,  8, 65,  6, 29, 39, 56, 60, 53, 15, 36, 37, 51, 52, 19, 63, 12,
        2, 35, 59, 62, 14,  1, 48, 20,  3, 13, 16, 61, 40, 47, 33],
      dtype=int64), 'cur_cost': 107422.0}, {'tour': array([33, 37,  7, 44, 64, 32, 26, 43,  2, 25, 22, 18, 39, 31, 60, 36, 41,
        4, 19,  9,  8, 63,  1, 40, 55, 54, 53, 42, 62, 13, 16,  0, 27, 12,
        3, 14, 28, 24, 23, 57, 15, 50, 58, 11,  6, 45, 48, 65, 47, 56, 10,
       20,  5, 38, 59, 46, 30, 52, 61, 29, 51, 35, 49, 17, 21, 34],
      dtype=int64), 'cur_cost': 108566.0}, {'tour': array([ 7, 21, 65, 37, 10, 59,  2, 63, 61, 46, 50, 38, 57, 53, 15, 62, 41,
        6,  8, 18, 47, 33, 55, 12, 39, 52, 43, 48, 64, 34, 32, 14,  5, 22,
       11, 45, 23,  4,  9, 54, 51, 36, 42, 29, 58, 19, 20, 60, 49, 27, 16,
       26, 13, 56, 40,  1,  3, 25, 17,  0, 28, 31, 24, 44, 30, 35],
      dtype=int64), 'cur_cost': 104414.0}, {'tour': array([17, 47, 54, 21, 15, 56, 33, 19, 12, 52, 62,  0, 59, 18, 30,  1, 44,
        2, 24, 50, 45, 48, 22,  7, 61, 14, 55, 28, 13, 35, 58, 29, 11, 53,
       34, 10, 64, 41, 23,  8, 37, 38, 16,  6, 42, 36, 51, 32, 26,  3, 39,
       25, 20, 27, 46, 43, 57,  5, 40,  4,  9, 49, 60, 63, 65, 31],
      dtype=int64), 'cur_cost': 109390.0}, {'tour': array([17, 25, 21, 42, 48, 39, 33,  2, 11,  8,  4, 44,  0, 30, 34, 19, 31,
       60, 41, 12, 56, 27, 40, 47, 13, 64, 38, 37,  5, 35, 63, 57, 61, 36,
       62, 18, 23, 53, 49, 29, 10, 26, 65, 24, 58, 45, 20, 14, 16, 52, 59,
        3, 54, 51,  1,  7, 50,  9, 15, 22, 46, 32, 43, 28, 55,  6],
      dtype=int64), 'cur_cost': 107667.0}, {'tour': array([51, 54, 36, 14, 46,  6, 19, 23, 58, 29, 27,  1,  3, 43,  0, 21, 48,
       15, 24, 35, 47, 34, 63, 39, 44,  7, 33, 42, 32, 65, 49, 28, 52, 41,
       60,  2, 11, 64, 17, 22, 61, 37, 16, 57, 40, 53, 26, 20, 55,  4, 62,
       30, 31,  8, 25, 56, 10, 59, 13, 12, 38, 18, 45,  9,  5, 50],
      dtype=int64), 'cur_cost': 113876.0}, {'tour': array([47, 49,  0, 61, 19, 21, 38, 36, 48, 27,  3,  7, 50, 59, 39, 31, 15,
       26, 46, 34, 63,  2, 45, 29, 51, 41, 60, 16, 35, 22, 42, 32, 33,  5,
        1, 53,  9, 58,  4, 25, 64, 13, 28, 20, 40, 56, 30, 23, 57, 52, 54,
       37, 43, 11, 62,  8, 44, 18,  6, 55, 14, 65, 12, 24, 17, 10],
      dtype=int64), 'cur_cost': 107310.0}, {'tour': array([47, 54,  2, 55,  5, 23, 27, 57,  0, 19,  9, 10,  3, 21, 18, 43, 58,
       29, 53, 26, 51, 52, 12, 64, 59,  8, 60, 11, 13, 37, 65, 44,  4, 15,
       63, 32, 48, 35, 45, 33, 28, 34, 42, 36, 62, 17, 49, 46, 16, 25,  1,
       31, 56, 22, 20, 39,  6, 38, 30, 40, 61, 50, 14, 41,  7, 24],
      dtype=int64), 'cur_cost': 114186.0}, {'tour': array([19, 27,  1, 20, 23, 33, 59, 26,  9, 58, 49, 61, 21,  6, 44, 35, 25,
       22, 63, 39, 11, 14,  4, 15, 42, 18, 34, 31, 62, 48, 55, 43, 40, 12,
        5, 32, 16, 47, 65, 36, 64,  8, 41, 57,  3, 45, 24, 10, 30,  2, 37,
       46,  7, 13, 52,  0, 56, 60, 29, 54, 53, 38, 28, 50, 51, 17],
      dtype=int64), 'cur_cost': 116419.0}, {'tour': array([ 7, 24, 25, 59, 53, 60, 52, 55, 31, 30, 62,  9, 61, 56,  8, 51, 22,
        2, 13, 28, 46, 14, 58, 54, 29, 44, 38, 36,  5, 12, 10, 47, 45,  0,
       64, 39, 50, 17,  4, 11, 33, 19, 65, 34, 37, 18, 27,  3, 20, 35, 43,
       15, 16, 57, 26, 42, 40,  1, 63, 23, 32, 49, 41, 48,  6, 21],
      dtype=int64), 'cur_cost': 90490.0}, {'tour': array([55, 15, 51, 18, 54, 10,  5, 47, 29, 12, 45, 11, 64, 48, 34, 60, 40,
        3, 27, 59,  6, 16, 52, 36, 56, 31, 57,  2, 17,  0, 19, 53, 61, 20,
       23, 41, 46, 39, 14,  9, 32, 63, 30, 24, 58, 50, 22, 38,  7, 42, 13,
       44,  1, 65, 43, 21,  8, 33, 37, 26,  4, 25, 62, 35, 49, 28],
      dtype=int64), 'cur_cost': 118109.0}]
2025-08-03 16:41:39,096 - ExploitationExpert - INFO - 局部搜索耗时: 2.22秒
2025-08-03 16:41:39,097 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-03 16:41:39,097 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([42,  8, 18, 40, 51, 62, 20, 58, 35,  2, 43, 57, 44, 60, 54, 23, 41,
       45, 48, 36, 19,  5, 55,  7, 50, 33, 38, 24, 12, 30,  6,  4, 61, 56,
       10, 14, 46, 53, 16, 59, 26, 52,  3, 17, 15, 28, 11,  0, 37, 49, 34,
       65, 64, 21, 47, 22, 13,  9, 25, 29, 27, 31, 32,  1, 63, 39],
      dtype=int64), 'cur_cost': 97611.0}
2025-08-03 16:41:39,098 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 97611.00)
2025-08-03 16:41:39,098 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-03 16:41:39,099 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-03 16:41:39,099 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:41:39,105 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:41:39,105 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:41:39,106 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10356.0, 路径长度: 66
2025-08-03 16:41:39,106 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 9, 3, 8, 2, 6, 4, 5, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10356.0}
2025-08-03 16:41:39,106 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 10356.00)
2025-08-03 16:41:39,107 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-03 16:41:39,107 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-03 16:41:39,107 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:41:39,112 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:41:39,115 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:41:39,116 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12845.0, 路径长度: 66
2025-08-03 16:41:39,117 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 21, 5, 8, 2, 6, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12845.0}
2025-08-03 16:41:39,119 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 12845.00)
2025-08-03 16:41:39,121 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-03 16:41:39,121 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:41:39,122 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:41:39,122 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 112464.0
2025-08-03 16:41:41,379 - ExploitationExpert - INFO - res_population_num: 2
2025-08-03 16:41:41,380 - ExploitationExpert - INFO - res_population_costs: [9906.0, 9594.0]
2025-08-03 16:41:41,380 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 63, 52, 65, 53, 62,
       59, 56, 58, 60, 64, 57, 54, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:41:41,387 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:41:41,388 - ExploitationExpert - INFO - populations: [{'tour': [0, 20, 14, 21, 13, 23, 16, 18, 12, 22, 15, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12409.0}, {'tour': array([42,  8, 18, 40, 51, 62, 20, 58, 35,  2, 43, 57, 44, 60, 54, 23, 41,
       45, 48, 36, 19,  5, 55,  7, 50, 33, 38, 24, 12, 30,  6,  4, 61, 56,
       10, 14, 46, 53, 16, 59, 26, 52,  3, 17, 15, 28, 11,  0, 37, 49, 34,
       65, 64, 21, 47, 22, 13,  9, 25, 29, 27, 31, 32,  1, 63, 39],
      dtype=int64), 'cur_cost': 97611.0}, {'tour': [0, 9, 3, 8, 2, 6, 4, 5, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10356.0}, {'tour': [0, 21, 5, 8, 2, 6, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12845.0}, {'tour': array([34, 23, 43, 15, 24, 41, 38, 12, 56, 10, 40, 60, 14,  4, 53, 28, 50,
       65, 46, 22, 62, 18, 48,  8, 30,  6, 16, 52, 47, 29, 49, 11,  1, 21,
       57, 58,  7, 55, 42, 64, 32, 37, 25,  5, 20, 35, 54,  0, 61, 13, 19,
       51, 36, 33,  3,  2, 27, 63, 44, 31, 26, 17, 59, 39,  9, 45],
      dtype=int64), 'cur_cost': 112464.0}, {'tour': array([27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 18, 16, 23,
       22, 12, 17, 15, 14, 20, 21, 13, 19,  9, 11,  7,  3,  1,  0, 10,  8,
        2,  6,  4,  5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9941.0}, {'tour': array([37, 16, 59, 42, 21, 53, 51, 45, 63, 47, 13, 18, 20, 48, 17, 58, 31,
       44, 61, 50,  4, 49, 11, 33, 40, 55,  3,  6, 32,  5, 64, 14, 65, 26,
        8, 24, 23, 30, 19, 10,  2, 36, 60, 41, 54,  0,  7, 52,  1, 22, 27,
       46,  9, 38, 29, 57, 43, 62, 12, 34, 15, 28, 35, 39, 25, 56],
      dtype=int64), 'cur_cost': 120824.0}, {'tour': array([11, 15, 32, 27, 62, 63, 51, 13,  0, 57, 64, 35, 53, 22,  5,  8, 16,
       10, 43, 33, 21, 48, 19, 46,  1,  7, 42,  2, 18, 47, 39, 37, 58, 24,
       45, 29, 59, 20, 30, 23,  4, 54, 28, 60, 61, 49, 44, 52, 56, 26, 55,
        9, 34,  6, 65, 41, 40, 12, 25, 14, 31, 50, 17,  3, 36, 38],
      dtype=int64), 'cur_cost': 109121.0}, {'tour': array([26, 29, 30, 33, 40, 19, 65, 16, 59, 31, 28, 48, 13, 21,  8, 43, 20,
       61, 45, 27,  3, 10,  9, 41,  2, 17,  0, 57, 35, 11, 22, 47, 46,  4,
       54,  6, 56, 49, 25, 53, 39, 15,  1, 23, 63, 51, 14, 55, 32, 42, 44,
       24, 60, 62, 50, 18, 36, 12, 34,  5, 37, 52,  7, 38, 58, 64],
      dtype=int64), 'cur_cost': 108806.0}, {'tour': array([24, 21, 49, 34, 44,  0, 54,  5, 22, 42, 25, 10, 41,  7, 11, 31, 26,
        4, 43, 55, 30, 23, 32, 64, 57, 18, 45, 27, 58, 50, 46, 28,  9, 38,
       17,  8, 65,  6, 29, 39, 56, 60, 53, 15, 36, 37, 51, 52, 19, 63, 12,
        2, 35, 59, 62, 14,  1, 48, 20,  3, 13, 16, 61, 40, 47, 33],
      dtype=int64), 'cur_cost': 107422.0}, {'tour': array([33, 37,  7, 44, 64, 32, 26, 43,  2, 25, 22, 18, 39, 31, 60, 36, 41,
        4, 19,  9,  8, 63,  1, 40, 55, 54, 53, 42, 62, 13, 16,  0, 27, 12,
        3, 14, 28, 24, 23, 57, 15, 50, 58, 11,  6, 45, 48, 65, 47, 56, 10,
       20,  5, 38, 59, 46, 30, 52, 61, 29, 51, 35, 49, 17, 21, 34],
      dtype=int64), 'cur_cost': 108566.0}, {'tour': array([ 7, 21, 65, 37, 10, 59,  2, 63, 61, 46, 50, 38, 57, 53, 15, 62, 41,
        6,  8, 18, 47, 33, 55, 12, 39, 52, 43, 48, 64, 34, 32, 14,  5, 22,
       11, 45, 23,  4,  9, 54, 51, 36, 42, 29, 58, 19, 20, 60, 49, 27, 16,
       26, 13, 56, 40,  1,  3, 25, 17,  0, 28, 31, 24, 44, 30, 35],
      dtype=int64), 'cur_cost': 104414.0}, {'tour': array([17, 47, 54, 21, 15, 56, 33, 19, 12, 52, 62,  0, 59, 18, 30,  1, 44,
        2, 24, 50, 45, 48, 22,  7, 61, 14, 55, 28, 13, 35, 58, 29, 11, 53,
       34, 10, 64, 41, 23,  8, 37, 38, 16,  6, 42, 36, 51, 32, 26,  3, 39,
       25, 20, 27, 46, 43, 57,  5, 40,  4,  9, 49, 60, 63, 65, 31],
      dtype=int64), 'cur_cost': 109390.0}, {'tour': array([17, 25, 21, 42, 48, 39, 33,  2, 11,  8,  4, 44,  0, 30, 34, 19, 31,
       60, 41, 12, 56, 27, 40, 47, 13, 64, 38, 37,  5, 35, 63, 57, 61, 36,
       62, 18, 23, 53, 49, 29, 10, 26, 65, 24, 58, 45, 20, 14, 16, 52, 59,
        3, 54, 51,  1,  7, 50,  9, 15, 22, 46, 32, 43, 28, 55,  6],
      dtype=int64), 'cur_cost': 107667.0}, {'tour': array([51, 54, 36, 14, 46,  6, 19, 23, 58, 29, 27,  1,  3, 43,  0, 21, 48,
       15, 24, 35, 47, 34, 63, 39, 44,  7, 33, 42, 32, 65, 49, 28, 52, 41,
       60,  2, 11, 64, 17, 22, 61, 37, 16, 57, 40, 53, 26, 20, 55,  4, 62,
       30, 31,  8, 25, 56, 10, 59, 13, 12, 38, 18, 45,  9,  5, 50],
      dtype=int64), 'cur_cost': 113876.0}, {'tour': array([47, 49,  0, 61, 19, 21, 38, 36, 48, 27,  3,  7, 50, 59, 39, 31, 15,
       26, 46, 34, 63,  2, 45, 29, 51, 41, 60, 16, 35, 22, 42, 32, 33,  5,
        1, 53,  9, 58,  4, 25, 64, 13, 28, 20, 40, 56, 30, 23, 57, 52, 54,
       37, 43, 11, 62,  8, 44, 18,  6, 55, 14, 65, 12, 24, 17, 10],
      dtype=int64), 'cur_cost': 107310.0}, {'tour': array([47, 54,  2, 55,  5, 23, 27, 57,  0, 19,  9, 10,  3, 21, 18, 43, 58,
       29, 53, 26, 51, 52, 12, 64, 59,  8, 60, 11, 13, 37, 65, 44,  4, 15,
       63, 32, 48, 35, 45, 33, 28, 34, 42, 36, 62, 17, 49, 46, 16, 25,  1,
       31, 56, 22, 20, 39,  6, 38, 30, 40, 61, 50, 14, 41,  7, 24],
      dtype=int64), 'cur_cost': 114186.0}, {'tour': array([19, 27,  1, 20, 23, 33, 59, 26,  9, 58, 49, 61, 21,  6, 44, 35, 25,
       22, 63, 39, 11, 14,  4, 15, 42, 18, 34, 31, 62, 48, 55, 43, 40, 12,
        5, 32, 16, 47, 65, 36, 64,  8, 41, 57,  3, 45, 24, 10, 30,  2, 37,
       46,  7, 13, 52,  0, 56, 60, 29, 54, 53, 38, 28, 50, 51, 17],
      dtype=int64), 'cur_cost': 116419.0}, {'tour': array([ 7, 24, 25, 59, 53, 60, 52, 55, 31, 30, 62,  9, 61, 56,  8, 51, 22,
        2, 13, 28, 46, 14, 58, 54, 29, 44, 38, 36,  5, 12, 10, 47, 45,  0,
       64, 39, 50, 17,  4, 11, 33, 19, 65, 34, 37, 18, 27,  3, 20, 35, 43,
       15, 16, 57, 26, 42, 40,  1, 63, 23, 32, 49, 41, 48,  6, 21],
      dtype=int64), 'cur_cost': 90490.0}, {'tour': array([55, 15, 51, 18, 54, 10,  5, 47, 29, 12, 45, 11, 64, 48, 34, 60, 40,
        3, 27, 59,  6, 16, 52, 36, 56, 31, 57,  2, 17,  0, 19, 53, 61, 20,
       23, 41, 46, 39, 14,  9, 32, 63, 30, 24, 58, 50, 22, 38,  7, 42, 13,
       44,  1, 65, 43, 21,  8, 33, 37, 26,  4, 25, 62, 35, 49, 28],
      dtype=int64), 'cur_cost': 118109.0}]
2025-08-03 16:41:41,401 - ExploitationExpert - INFO - 局部搜索耗时: 2.28秒
2025-08-03 16:41:41,402 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-03 16:41:41,402 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([34, 23, 43, 15, 24, 41, 38, 12, 56, 10, 40, 60, 14,  4, 53, 28, 50,
       65, 46, 22, 62, 18, 48,  8, 30,  6, 16, 52, 47, 29, 49, 11,  1, 21,
       57, 58,  7, 55, 42, 64, 32, 37, 25,  5, 20, 35, 54,  0, 61, 13, 19,
       51, 36, 33,  3,  2, 27, 63, 44, 31, 26, 17, 59, 39,  9, 45],
      dtype=int64), 'cur_cost': 112464.0}
2025-08-03 16:41:41,403 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 112464.00)
2025-08-03 16:41:41,403 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-03 16:41:41,403 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-03 16:41:41,404 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:41:41,423 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:41:41,424 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:41:41,425 - ExplorationExpert - INFO - 探索路径生成完成，成本: 61126.0, 路径长度: 66
2025-08-03 16:41:41,425 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [38, 18, 26, 13, 9, 53, 12, 34, 35, 16, 19, 7, 22, 25, 33, 3, 58, 65, 52, 54, 57, 15, 37, 27, 21, 11, 64, 0, 4, 2, 23, 36, 40, 20, 32, 43, 46, 28, 49, 17, 48, 50, 41, 14, 8, 5, 56, 60, 62, 1, 10, 6, 24, 47, 45, 42, 51, 44, 29, 30, 31, 39, 55, 61, 59, 63], 'cur_cost': 61126.0}
2025-08-03 16:41:41,426 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 61126.00)
2025-08-03 16:41:41,426 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-03 16:41:41,427 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-03 16:41:41,427 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:41:41,436 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:41:41,437 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:41:41,437 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12973.0, 路径长度: 66
2025-08-03 16:41:41,438 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 2, 18, 10, 8, 5, 4, 6, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12973.0}
2025-08-03 16:41:41,438 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 12973.00)
2025-08-03 16:41:41,438 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-03 16:41:41,439 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:41:41,439 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:41:41,439 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 98890.0
2025-08-03 16:41:42,125 - ExploitationExpert - INFO - res_population_num: 2
2025-08-03 16:41:42,125 - ExploitationExpert - INFO - res_population_costs: [9906.0, 9594.0]
2025-08-03 16:41:42,125 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 63, 52, 65, 53, 62,
       59, 56, 58, 60, 64, 57, 54, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:41:42,126 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:41:42,126 - ExploitationExpert - INFO - populations: [{'tour': [0, 20, 14, 21, 13, 23, 16, 18, 12, 22, 15, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12409.0}, {'tour': array([42,  8, 18, 40, 51, 62, 20, 58, 35,  2, 43, 57, 44, 60, 54, 23, 41,
       45, 48, 36, 19,  5, 55,  7, 50, 33, 38, 24, 12, 30,  6,  4, 61, 56,
       10, 14, 46, 53, 16, 59, 26, 52,  3, 17, 15, 28, 11,  0, 37, 49, 34,
       65, 64, 21, 47, 22, 13,  9, 25, 29, 27, 31, 32,  1, 63, 39],
      dtype=int64), 'cur_cost': 97611.0}, {'tour': [0, 9, 3, 8, 2, 6, 4, 5, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10356.0}, {'tour': [0, 21, 5, 8, 2, 6, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12845.0}, {'tour': array([34, 23, 43, 15, 24, 41, 38, 12, 56, 10, 40, 60, 14,  4, 53, 28, 50,
       65, 46, 22, 62, 18, 48,  8, 30,  6, 16, 52, 47, 29, 49, 11,  1, 21,
       57, 58,  7, 55, 42, 64, 32, 37, 25,  5, 20, 35, 54,  0, 61, 13, 19,
       51, 36, 33,  3,  2, 27, 63, 44, 31, 26, 17, 59, 39,  9, 45],
      dtype=int64), 'cur_cost': 112464.0}, {'tour': [38, 18, 26, 13, 9, 53, 12, 34, 35, 16, 19, 7, 22, 25, 33, 3, 58, 65, 52, 54, 57, 15, 37, 27, 21, 11, 64, 0, 4, 2, 23, 36, 40, 20, 32, 43, 46, 28, 49, 17, 48, 50, 41, 14, 8, 5, 56, 60, 62, 1, 10, 6, 24, 47, 45, 42, 51, 44, 29, 30, 31, 39, 55, 61, 59, 63], 'cur_cost': 61126.0}, {'tour': [0, 2, 18, 10, 8, 5, 4, 6, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12973.0}, {'tour': array([19, 41, 29,  1, 16,  3,  2, 38, 55, 53, 44, 11, 62, 35, 60, 37, 30,
       54, 65, 51, 48, 57, 20, 15, 21, 39, 43, 58, 42, 61, 18,  9,  7, 33,
       52,  0,  6,  5, 10, 13, 47, 12, 14, 17, 63, 26, 40, 50, 56, 64, 25,
       46, 23, 45, 24, 22, 59,  4, 31, 28, 27, 32,  8, 34, 49, 36],
      dtype=int64), 'cur_cost': 98890.0}, {'tour': array([26, 29, 30, 33, 40, 19, 65, 16, 59, 31, 28, 48, 13, 21,  8, 43, 20,
       61, 45, 27,  3, 10,  9, 41,  2, 17,  0, 57, 35, 11, 22, 47, 46,  4,
       54,  6, 56, 49, 25, 53, 39, 15,  1, 23, 63, 51, 14, 55, 32, 42, 44,
       24, 60, 62, 50, 18, 36, 12, 34,  5, 37, 52,  7, 38, 58, 64],
      dtype=int64), 'cur_cost': 108806.0}, {'tour': array([24, 21, 49, 34, 44,  0, 54,  5, 22, 42, 25, 10, 41,  7, 11, 31, 26,
        4, 43, 55, 30, 23, 32, 64, 57, 18, 45, 27, 58, 50, 46, 28,  9, 38,
       17,  8, 65,  6, 29, 39, 56, 60, 53, 15, 36, 37, 51, 52, 19, 63, 12,
        2, 35, 59, 62, 14,  1, 48, 20,  3, 13, 16, 61, 40, 47, 33],
      dtype=int64), 'cur_cost': 107422.0}, {'tour': array([33, 37,  7, 44, 64, 32, 26, 43,  2, 25, 22, 18, 39, 31, 60, 36, 41,
        4, 19,  9,  8, 63,  1, 40, 55, 54, 53, 42, 62, 13, 16,  0, 27, 12,
        3, 14, 28, 24, 23, 57, 15, 50, 58, 11,  6, 45, 48, 65, 47, 56, 10,
       20,  5, 38, 59, 46, 30, 52, 61, 29, 51, 35, 49, 17, 21, 34],
      dtype=int64), 'cur_cost': 108566.0}, {'tour': array([ 7, 21, 65, 37, 10, 59,  2, 63, 61, 46, 50, 38, 57, 53, 15, 62, 41,
        6,  8, 18, 47, 33, 55, 12, 39, 52, 43, 48, 64, 34, 32, 14,  5, 22,
       11, 45, 23,  4,  9, 54, 51, 36, 42, 29, 58, 19, 20, 60, 49, 27, 16,
       26, 13, 56, 40,  1,  3, 25, 17,  0, 28, 31, 24, 44, 30, 35],
      dtype=int64), 'cur_cost': 104414.0}, {'tour': array([17, 47, 54, 21, 15, 56, 33, 19, 12, 52, 62,  0, 59, 18, 30,  1, 44,
        2, 24, 50, 45, 48, 22,  7, 61, 14, 55, 28, 13, 35, 58, 29, 11, 53,
       34, 10, 64, 41, 23,  8, 37, 38, 16,  6, 42, 36, 51, 32, 26,  3, 39,
       25, 20, 27, 46, 43, 57,  5, 40,  4,  9, 49, 60, 63, 65, 31],
      dtype=int64), 'cur_cost': 109390.0}, {'tour': array([17, 25, 21, 42, 48, 39, 33,  2, 11,  8,  4, 44,  0, 30, 34, 19, 31,
       60, 41, 12, 56, 27, 40, 47, 13, 64, 38, 37,  5, 35, 63, 57, 61, 36,
       62, 18, 23, 53, 49, 29, 10, 26, 65, 24, 58, 45, 20, 14, 16, 52, 59,
        3, 54, 51,  1,  7, 50,  9, 15, 22, 46, 32, 43, 28, 55,  6],
      dtype=int64), 'cur_cost': 107667.0}, {'tour': array([51, 54, 36, 14, 46,  6, 19, 23, 58, 29, 27,  1,  3, 43,  0, 21, 48,
       15, 24, 35, 47, 34, 63, 39, 44,  7, 33, 42, 32, 65, 49, 28, 52, 41,
       60,  2, 11, 64, 17, 22, 61, 37, 16, 57, 40, 53, 26, 20, 55,  4, 62,
       30, 31,  8, 25, 56, 10, 59, 13, 12, 38, 18, 45,  9,  5, 50],
      dtype=int64), 'cur_cost': 113876.0}, {'tour': array([47, 49,  0, 61, 19, 21, 38, 36, 48, 27,  3,  7, 50, 59, 39, 31, 15,
       26, 46, 34, 63,  2, 45, 29, 51, 41, 60, 16, 35, 22, 42, 32, 33,  5,
        1, 53,  9, 58,  4, 25, 64, 13, 28, 20, 40, 56, 30, 23, 57, 52, 54,
       37, 43, 11, 62,  8, 44, 18,  6, 55, 14, 65, 12, 24, 17, 10],
      dtype=int64), 'cur_cost': 107310.0}, {'tour': array([47, 54,  2, 55,  5, 23, 27, 57,  0, 19,  9, 10,  3, 21, 18, 43, 58,
       29, 53, 26, 51, 52, 12, 64, 59,  8, 60, 11, 13, 37, 65, 44,  4, 15,
       63, 32, 48, 35, 45, 33, 28, 34, 42, 36, 62, 17, 49, 46, 16, 25,  1,
       31, 56, 22, 20, 39,  6, 38, 30, 40, 61, 50, 14, 41,  7, 24],
      dtype=int64), 'cur_cost': 114186.0}, {'tour': array([19, 27,  1, 20, 23, 33, 59, 26,  9, 58, 49, 61, 21,  6, 44, 35, 25,
       22, 63, 39, 11, 14,  4, 15, 42, 18, 34, 31, 62, 48, 55, 43, 40, 12,
        5, 32, 16, 47, 65, 36, 64,  8, 41, 57,  3, 45, 24, 10, 30,  2, 37,
       46,  7, 13, 52,  0, 56, 60, 29, 54, 53, 38, 28, 50, 51, 17],
      dtype=int64), 'cur_cost': 116419.0}, {'tour': array([ 7, 24, 25, 59, 53, 60, 52, 55, 31, 30, 62,  9, 61, 56,  8, 51, 22,
        2, 13, 28, 46, 14, 58, 54, 29, 44, 38, 36,  5, 12, 10, 47, 45,  0,
       64, 39, 50, 17,  4, 11, 33, 19, 65, 34, 37, 18, 27,  3, 20, 35, 43,
       15, 16, 57, 26, 42, 40,  1, 63, 23, 32, 49, 41, 48,  6, 21],
      dtype=int64), 'cur_cost': 90490.0}, {'tour': array([55, 15, 51, 18, 54, 10,  5, 47, 29, 12, 45, 11, 64, 48, 34, 60, 40,
        3, 27, 59,  6, 16, 52, 36, 56, 31, 57,  2, 17,  0, 19, 53, 61, 20,
       23, 41, 46, 39, 14,  9, 32, 63, 30, 24, 58, 50, 22, 38,  7, 42, 13,
       44,  1, 65, 43, 21,  8, 33, 37, 26,  4, 25, 62, 35, 49, 28],
      dtype=int64), 'cur_cost': 118109.0}]
2025-08-03 16:41:42,136 - ExploitationExpert - INFO - 局部搜索耗时: 0.70秒
2025-08-03 16:41:42,137 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-03 16:41:42,137 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([19, 41, 29,  1, 16,  3,  2, 38, 55, 53, 44, 11, 62, 35, 60, 37, 30,
       54, 65, 51, 48, 57, 20, 15, 21, 39, 43, 58, 42, 61, 18,  9,  7, 33,
       52,  0,  6,  5, 10, 13, 47, 12, 14, 17, 63, 26, 40, 50, 56, 64, 25,
       46, 23, 45, 24, 22, 59,  4, 31, 28, 27, 32,  8, 34, 49, 36],
      dtype=int64), 'cur_cost': 98890.0}
2025-08-03 16:41:42,138 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 98890.00)
2025-08-03 16:41:42,138 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-03 16:41:42,138 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-03 16:41:42,138 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:41:42,143 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:41:42,143 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:41:42,143 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10533.0, 路径长度: 66
2025-08-03 16:41:42,144 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 11, 2, 7, 3, 9, 5, 4, 8, 6, 10, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10533.0}
2025-08-03 16:41:42,144 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 10533.00)
2025-08-03 16:41:42,145 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-03 16:41:42,145 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-03 16:41:42,145 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:41:42,154 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:41:42,155 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:41:42,155 - ExplorationExpert - INFO - 探索路径生成完成，成本: 83934.0, 路径长度: 66
2025-08-03 16:41:42,155 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [9, 3, 53, 16, 22, 19, 37, 54, 31, 29, 32, 2, 61, 56, 60, 64, 62, 63, 38, 41, 33, 43, 48, 58, 52, 46, 36, 49, 25, 18, 39, 0, 10, 5, 17, 14, 28, 11, 42, 27, 21, 47, 35, 34, 40, 8, 20, 24, 57, 55, 51, 15, 12, 30, 26, 6, 7, 13, 44, 1, 4, 59, 65, 45, 23, 50], 'cur_cost': 83934.0}
2025-08-03 16:41:42,156 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 83934.00)
2025-08-03 16:41:42,156 - experts.management.collaboration_manager - INFO - 为个体 10 生成利用路径
2025-08-03 16:41:42,156 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:41:42,157 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:41:42,157 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 10 处的路径，新成本: 112958.0
2025-08-03 16:41:42,231 - ExploitationExpert - INFO - res_population_num: 13
2025-08-03 16:41:42,231 - ExploitationExpert - INFO - res_population_costs: [9906.0, 9594.0, 9567.0, 9551, 9542, 9542, 9542, 9540, 9540, 9540, 9534, 9521, 9521]
2025-08-03 16:41:42,232 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 63, 52, 65, 53, 62,
       59, 56, 58, 60, 64, 57, 54, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 59, 62, 64, 57,
       54, 60, 58, 56, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40,
       43, 21, 20, 13, 19, 16, 23, 22, 14, 15, 17, 12, 18, 27, 37, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 56, 58, 60, 54, 57,
       64, 62, 59, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:41:42,238 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:41:42,238 - ExploitationExpert - INFO - populations: [{'tour': [0, 20, 14, 21, 13, 23, 16, 18, 12, 22, 15, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12409.0}, {'tour': array([42,  8, 18, 40, 51, 62, 20, 58, 35,  2, 43, 57, 44, 60, 54, 23, 41,
       45, 48, 36, 19,  5, 55,  7, 50, 33, 38, 24, 12, 30,  6,  4, 61, 56,
       10, 14, 46, 53, 16, 59, 26, 52,  3, 17, 15, 28, 11,  0, 37, 49, 34,
       65, 64, 21, 47, 22, 13,  9, 25, 29, 27, 31, 32,  1, 63, 39],
      dtype=int64), 'cur_cost': 97611.0}, {'tour': [0, 9, 3, 8, 2, 6, 4, 5, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10356.0}, {'tour': [0, 21, 5, 8, 2, 6, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12845.0}, {'tour': array([34, 23, 43, 15, 24, 41, 38, 12, 56, 10, 40, 60, 14,  4, 53, 28, 50,
       65, 46, 22, 62, 18, 48,  8, 30,  6, 16, 52, 47, 29, 49, 11,  1, 21,
       57, 58,  7, 55, 42, 64, 32, 37, 25,  5, 20, 35, 54,  0, 61, 13, 19,
       51, 36, 33,  3,  2, 27, 63, 44, 31, 26, 17, 59, 39,  9, 45],
      dtype=int64), 'cur_cost': 112464.0}, {'tour': [38, 18, 26, 13, 9, 53, 12, 34, 35, 16, 19, 7, 22, 25, 33, 3, 58, 65, 52, 54, 57, 15, 37, 27, 21, 11, 64, 0, 4, 2, 23, 36, 40, 20, 32, 43, 46, 28, 49, 17, 48, 50, 41, 14, 8, 5, 56, 60, 62, 1, 10, 6, 24, 47, 45, 42, 51, 44, 29, 30, 31, 39, 55, 61, 59, 63], 'cur_cost': 61126.0}, {'tour': [0, 2, 18, 10, 8, 5, 4, 6, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12973.0}, {'tour': array([19, 41, 29,  1, 16,  3,  2, 38, 55, 53, 44, 11, 62, 35, 60, 37, 30,
       54, 65, 51, 48, 57, 20, 15, 21, 39, 43, 58, 42, 61, 18,  9,  7, 33,
       52,  0,  6,  5, 10, 13, 47, 12, 14, 17, 63, 26, 40, 50, 56, 64, 25,
       46, 23, 45, 24, 22, 59,  4, 31, 28, 27, 32,  8, 34, 49, 36],
      dtype=int64), 'cur_cost': 98890.0}, {'tour': [0, 11, 2, 7, 3, 9, 5, 4, 8, 6, 10, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10533.0}, {'tour': [9, 3, 53, 16, 22, 19, 37, 54, 31, 29, 32, 2, 61, 56, 60, 64, 62, 63, 38, 41, 33, 43, 48, 58, 52, 46, 36, 49, 25, 18, 39, 0, 10, 5, 17, 14, 28, 11, 42, 27, 21, 47, 35, 34, 40, 8, 20, 24, 57, 55, 51, 15, 12, 30, 26, 6, 7, 13, 44, 1, 4, 59, 65, 45, 23, 50], 'cur_cost': 83934.0}, {'tour': array([65, 60,  8, 26,  6,  3, 10, 32, 29, 34, 44, 12, 54, 43, 19, 23, 56,
       31, 40, 42, 61, 47, 53, 38, 33, 46, 59, 25, 58, 16, 39, 13, 22, 35,
       18, 45, 63,  5,  0, 14, 50, 27, 51, 52, 48, 64, 28,  7,  1, 36, 55,
       49, 37,  2, 62, 21,  4, 11, 17, 15, 57, 24,  9, 30, 41, 20],
      dtype=int64), 'cur_cost': 112958.0}, {'tour': array([ 7, 21, 65, 37, 10, 59,  2, 63, 61, 46, 50, 38, 57, 53, 15, 62, 41,
        6,  8, 18, 47, 33, 55, 12, 39, 52, 43, 48, 64, 34, 32, 14,  5, 22,
       11, 45, 23,  4,  9, 54, 51, 36, 42, 29, 58, 19, 20, 60, 49, 27, 16,
       26, 13, 56, 40,  1,  3, 25, 17,  0, 28, 31, 24, 44, 30, 35],
      dtype=int64), 'cur_cost': 104414.0}, {'tour': array([17, 47, 54, 21, 15, 56, 33, 19, 12, 52, 62,  0, 59, 18, 30,  1, 44,
        2, 24, 50, 45, 48, 22,  7, 61, 14, 55, 28, 13, 35, 58, 29, 11, 53,
       34, 10, 64, 41, 23,  8, 37, 38, 16,  6, 42, 36, 51, 32, 26,  3, 39,
       25, 20, 27, 46, 43, 57,  5, 40,  4,  9, 49, 60, 63, 65, 31],
      dtype=int64), 'cur_cost': 109390.0}, {'tour': array([17, 25, 21, 42, 48, 39, 33,  2, 11,  8,  4, 44,  0, 30, 34, 19, 31,
       60, 41, 12, 56, 27, 40, 47, 13, 64, 38, 37,  5, 35, 63, 57, 61, 36,
       62, 18, 23, 53, 49, 29, 10, 26, 65, 24, 58, 45, 20, 14, 16, 52, 59,
        3, 54, 51,  1,  7, 50,  9, 15, 22, 46, 32, 43, 28, 55,  6],
      dtype=int64), 'cur_cost': 107667.0}, {'tour': array([51, 54, 36, 14, 46,  6, 19, 23, 58, 29, 27,  1,  3, 43,  0, 21, 48,
       15, 24, 35, 47, 34, 63, 39, 44,  7, 33, 42, 32, 65, 49, 28, 52, 41,
       60,  2, 11, 64, 17, 22, 61, 37, 16, 57, 40, 53, 26, 20, 55,  4, 62,
       30, 31,  8, 25, 56, 10, 59, 13, 12, 38, 18, 45,  9,  5, 50],
      dtype=int64), 'cur_cost': 113876.0}, {'tour': array([47, 49,  0, 61, 19, 21, 38, 36, 48, 27,  3,  7, 50, 59, 39, 31, 15,
       26, 46, 34, 63,  2, 45, 29, 51, 41, 60, 16, 35, 22, 42, 32, 33,  5,
        1, 53,  9, 58,  4, 25, 64, 13, 28, 20, 40, 56, 30, 23, 57, 52, 54,
       37, 43, 11, 62,  8, 44, 18,  6, 55, 14, 65, 12, 24, 17, 10],
      dtype=int64), 'cur_cost': 107310.0}, {'tour': array([47, 54,  2, 55,  5, 23, 27, 57,  0, 19,  9, 10,  3, 21, 18, 43, 58,
       29, 53, 26, 51, 52, 12, 64, 59,  8, 60, 11, 13, 37, 65, 44,  4, 15,
       63, 32, 48, 35, 45, 33, 28, 34, 42, 36, 62, 17, 49, 46, 16, 25,  1,
       31, 56, 22, 20, 39,  6, 38, 30, 40, 61, 50, 14, 41,  7, 24],
      dtype=int64), 'cur_cost': 114186.0}, {'tour': array([19, 27,  1, 20, 23, 33, 59, 26,  9, 58, 49, 61, 21,  6, 44, 35, 25,
       22, 63, 39, 11, 14,  4, 15, 42, 18, 34, 31, 62, 48, 55, 43, 40, 12,
        5, 32, 16, 47, 65, 36, 64,  8, 41, 57,  3, 45, 24, 10, 30,  2, 37,
       46,  7, 13, 52,  0, 56, 60, 29, 54, 53, 38, 28, 50, 51, 17],
      dtype=int64), 'cur_cost': 116419.0}, {'tour': array([ 7, 24, 25, 59, 53, 60, 52, 55, 31, 30, 62,  9, 61, 56,  8, 51, 22,
        2, 13, 28, 46, 14, 58, 54, 29, 44, 38, 36,  5, 12, 10, 47, 45,  0,
       64, 39, 50, 17,  4, 11, 33, 19, 65, 34, 37, 18, 27,  3, 20, 35, 43,
       15, 16, 57, 26, 42, 40,  1, 63, 23, 32, 49, 41, 48,  6, 21],
      dtype=int64), 'cur_cost': 90490.0}, {'tour': array([55, 15, 51, 18, 54, 10,  5, 47, 29, 12, 45, 11, 64, 48, 34, 60, 40,
        3, 27, 59,  6, 16, 52, 36, 56, 31, 57,  2, 17,  0, 19, 53, 61, 20,
       23, 41, 46, 39, 14,  9, 32, 63, 30, 24, 58, 50, 22, 38,  7, 42, 13,
       44,  1, 65, 43, 21,  8, 33, 37, 26,  4, 25, 62, 35, 49, 28],
      dtype=int64), 'cur_cost': 118109.0}]
2025-08-03 16:41:42,254 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 16:41:42,254 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-03 16:41:42,255 - experts.management.collaboration_manager - INFO - 个体 10 利用路径生成报告: {'new_tour': array([65, 60,  8, 26,  6,  3, 10, 32, 29, 34, 44, 12, 54, 43, 19, 23, 56,
       31, 40, 42, 61, 47, 53, 38, 33, 46, 59, 25, 58, 16, 39, 13, 22, 35,
       18, 45, 63,  5,  0, 14, 50, 27, 51, 52, 48, 64, 28,  7,  1, 36, 55,
       49, 37,  2, 62, 21,  4, 11, 17, 15, 57, 24,  9, 30, 41, 20],
      dtype=int64), 'cur_cost': 112958.0}
2025-08-03 16:41:42,256 - experts.management.collaboration_manager - INFO - 个体 10 接受新路径 (成本: 112958.00)
2025-08-03 16:41:42,256 - experts.management.collaboration_manager - INFO - 为个体 11 生成探索路径
2025-08-03 16:41:42,257 - ExplorationExpert - INFO - 开始为个体 11 生成探索路径（算法实现）
2025-08-03 16:41:42,257 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:41:42,262 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:41:42,263 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:41:42,264 - ExplorationExpert - INFO - 探索路径生成完成，成本: 87569.0, 路径长度: 66
2025-08-03 16:41:42,264 - experts.management.collaboration_manager - INFO - 个体 11 探索路径生成报告: {'new_tour': [9, 3, 53, 37, 34, 22, 10, 25, 30, 27, 4, 2, 32, 28, 21, 55, 61, 62, 60, 64, 65, 63, 44, 38, 41, 47, 23, 12, 42, 15, 14, 33, 5, 18, 48, 17, 29, 7, 54, 11, 20, 39, 56, 16, 45, 24, 40, 46, 57, 35, 19, 8, 36, 51, 0, 43, 49, 1, 31, 58, 13, 26, 59, 6, 52, 50], 'cur_cost': 87569.0}
2025-08-03 16:41:42,265 - experts.management.collaboration_manager - INFO - 个体 11 接受新路径 (成本: 87569.00)
2025-08-03 16:41:42,265 - experts.management.collaboration_manager - INFO - 为个体 12 生成探索路径
2025-08-03 16:41:42,266 - ExplorationExpert - INFO - 开始为个体 12 生成探索路径（算法实现）
2025-08-03 16:41:42,266 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:41:42,282 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:41:42,287 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:41:42,288 - ExplorationExpert - INFO - 探索路径生成完成，成本: 59899.0, 路径长度: 66
2025-08-03 16:41:42,288 - experts.management.collaboration_manager - INFO - 个体 12 探索路径生成报告: {'new_tour': [37, 21, 23, 31, 25, 18, 20, 32, 26, 1, 58, 52, 56, 54, 7, 53, 4, 55, 65, 9, 22, 24, 14, 10, 3, 11, 13, 36, 19, 35, 43, 15, 2, 12, 48, 44, 46, 50, 38, 30, 27, 17, 47, 34, 8, 64, 57, 0, 59, 5, 16, 40, 51, 42, 39, 41, 33, 28, 29, 49, 60, 61, 63, 62, 6, 45], 'cur_cost': 59899.0}
2025-08-03 16:41:42,290 - experts.management.collaboration_manager - INFO - 个体 12 接受新路径 (成本: 59899.00)
2025-08-03 16:41:42,290 - experts.management.collaboration_manager - INFO - 为个体 13 生成利用路径
2025-08-03 16:41:42,291 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:41:42,291 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:41:42,292 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 13 处的路径，新成本: 111631.0
2025-08-03 16:41:42,404 - ExploitationExpert - INFO - res_population_num: 13
2025-08-03 16:41:42,405 - ExploitationExpert - INFO - res_population_costs: [9906.0, 9594.0, 9567.0, 9551, 9542, 9542, 9542, 9540, 9540, 9540, 9534, 9521, 9521]
2025-08-03 16:41:42,405 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 63, 52, 65, 53, 62,
       59, 56, 58, 60, 64, 57, 54, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 59, 62, 64, 57,
       54, 60, 58, 56, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40,
       43, 21, 20, 13, 19, 16, 23, 22, 14, 15, 17, 12, 18, 27, 37, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 56, 58, 60, 54, 57,
       64, 62, 59, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:41:42,413 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:41:42,416 - ExploitationExpert - INFO - populations: [{'tour': [0, 20, 14, 21, 13, 23, 16, 18, 12, 22, 15, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12409.0}, {'tour': array([42,  8, 18, 40, 51, 62, 20, 58, 35,  2, 43, 57, 44, 60, 54, 23, 41,
       45, 48, 36, 19,  5, 55,  7, 50, 33, 38, 24, 12, 30,  6,  4, 61, 56,
       10, 14, 46, 53, 16, 59, 26, 52,  3, 17, 15, 28, 11,  0, 37, 49, 34,
       65, 64, 21, 47, 22, 13,  9, 25, 29, 27, 31, 32,  1, 63, 39],
      dtype=int64), 'cur_cost': 97611.0}, {'tour': [0, 9, 3, 8, 2, 6, 4, 5, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10356.0}, {'tour': [0, 21, 5, 8, 2, 6, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12845.0}, {'tour': array([34, 23, 43, 15, 24, 41, 38, 12, 56, 10, 40, 60, 14,  4, 53, 28, 50,
       65, 46, 22, 62, 18, 48,  8, 30,  6, 16, 52, 47, 29, 49, 11,  1, 21,
       57, 58,  7, 55, 42, 64, 32, 37, 25,  5, 20, 35, 54,  0, 61, 13, 19,
       51, 36, 33,  3,  2, 27, 63, 44, 31, 26, 17, 59, 39,  9, 45],
      dtype=int64), 'cur_cost': 112464.0}, {'tour': [38, 18, 26, 13, 9, 53, 12, 34, 35, 16, 19, 7, 22, 25, 33, 3, 58, 65, 52, 54, 57, 15, 37, 27, 21, 11, 64, 0, 4, 2, 23, 36, 40, 20, 32, 43, 46, 28, 49, 17, 48, 50, 41, 14, 8, 5, 56, 60, 62, 1, 10, 6, 24, 47, 45, 42, 51, 44, 29, 30, 31, 39, 55, 61, 59, 63], 'cur_cost': 61126.0}, {'tour': [0, 2, 18, 10, 8, 5, 4, 6, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12973.0}, {'tour': array([19, 41, 29,  1, 16,  3,  2, 38, 55, 53, 44, 11, 62, 35, 60, 37, 30,
       54, 65, 51, 48, 57, 20, 15, 21, 39, 43, 58, 42, 61, 18,  9,  7, 33,
       52,  0,  6,  5, 10, 13, 47, 12, 14, 17, 63, 26, 40, 50, 56, 64, 25,
       46, 23, 45, 24, 22, 59,  4, 31, 28, 27, 32,  8, 34, 49, 36],
      dtype=int64), 'cur_cost': 98890.0}, {'tour': [0, 11, 2, 7, 3, 9, 5, 4, 8, 6, 10, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10533.0}, {'tour': [9, 3, 53, 16, 22, 19, 37, 54, 31, 29, 32, 2, 61, 56, 60, 64, 62, 63, 38, 41, 33, 43, 48, 58, 52, 46, 36, 49, 25, 18, 39, 0, 10, 5, 17, 14, 28, 11, 42, 27, 21, 47, 35, 34, 40, 8, 20, 24, 57, 55, 51, 15, 12, 30, 26, 6, 7, 13, 44, 1, 4, 59, 65, 45, 23, 50], 'cur_cost': 83934.0}, {'tour': array([65, 60,  8, 26,  6,  3, 10, 32, 29, 34, 44, 12, 54, 43, 19, 23, 56,
       31, 40, 42, 61, 47, 53, 38, 33, 46, 59, 25, 58, 16, 39, 13, 22, 35,
       18, 45, 63,  5,  0, 14, 50, 27, 51, 52, 48, 64, 28,  7,  1, 36, 55,
       49, 37,  2, 62, 21,  4, 11, 17, 15, 57, 24,  9, 30, 41, 20],
      dtype=int64), 'cur_cost': 112958.0}, {'tour': [9, 3, 53, 37, 34, 22, 10, 25, 30, 27, 4, 2, 32, 28, 21, 55, 61, 62, 60, 64, 65, 63, 44, 38, 41, 47, 23, 12, 42, 15, 14, 33, 5, 18, 48, 17, 29, 7, 54, 11, 20, 39, 56, 16, 45, 24, 40, 46, 57, 35, 19, 8, 36, 51, 0, 43, 49, 1, 31, 58, 13, 26, 59, 6, 52, 50], 'cur_cost': 87569.0}, {'tour': [37, 21, 23, 31, 25, 18, 20, 32, 26, 1, 58, 52, 56, 54, 7, 53, 4, 55, 65, 9, 22, 24, 14, 10, 3, 11, 13, 36, 19, 35, 43, 15, 2, 12, 48, 44, 46, 50, 38, 30, 27, 17, 47, 34, 8, 64, 57, 0, 59, 5, 16, 40, 51, 42, 39, 41, 33, 28, 29, 49, 60, 61, 63, 62, 6, 45], 'cur_cost': 59899.0}, {'tour': array([63, 42, 25, 22, 40, 65, 31, 39, 10, 60, 50,  3, 24, 64, 17, 47, 56,
       49, 44,  6, 16, 53, 41, 27, 52,  9,  4, 54, 45, 33, 20, 29, 38, 36,
       51, 59, 32, 26, 12, 21, 15, 46, 30, 58,  1, 62, 35, 18, 19, 43, 13,
       23,  8, 57, 28, 11, 14,  7,  5, 61,  0,  2, 37, 48, 34, 55],
      dtype=int64), 'cur_cost': 111631.0}, {'tour': array([51, 54, 36, 14, 46,  6, 19, 23, 58, 29, 27,  1,  3, 43,  0, 21, 48,
       15, 24, 35, 47, 34, 63, 39, 44,  7, 33, 42, 32, 65, 49, 28, 52, 41,
       60,  2, 11, 64, 17, 22, 61, 37, 16, 57, 40, 53, 26, 20, 55,  4, 62,
       30, 31,  8, 25, 56, 10, 59, 13, 12, 38, 18, 45,  9,  5, 50],
      dtype=int64), 'cur_cost': 113876.0}, {'tour': array([47, 49,  0, 61, 19, 21, 38, 36, 48, 27,  3,  7, 50, 59, 39, 31, 15,
       26, 46, 34, 63,  2, 45, 29, 51, 41, 60, 16, 35, 22, 42, 32, 33,  5,
        1, 53,  9, 58,  4, 25, 64, 13, 28, 20, 40, 56, 30, 23, 57, 52, 54,
       37, 43, 11, 62,  8, 44, 18,  6, 55, 14, 65, 12, 24, 17, 10],
      dtype=int64), 'cur_cost': 107310.0}, {'tour': array([47, 54,  2, 55,  5, 23, 27, 57,  0, 19,  9, 10,  3, 21, 18, 43, 58,
       29, 53, 26, 51, 52, 12, 64, 59,  8, 60, 11, 13, 37, 65, 44,  4, 15,
       63, 32, 48, 35, 45, 33, 28, 34, 42, 36, 62, 17, 49, 46, 16, 25,  1,
       31, 56, 22, 20, 39,  6, 38, 30, 40, 61, 50, 14, 41,  7, 24],
      dtype=int64), 'cur_cost': 114186.0}, {'tour': array([19, 27,  1, 20, 23, 33, 59, 26,  9, 58, 49, 61, 21,  6, 44, 35, 25,
       22, 63, 39, 11, 14,  4, 15, 42, 18, 34, 31, 62, 48, 55, 43, 40, 12,
        5, 32, 16, 47, 65, 36, 64,  8, 41, 57,  3, 45, 24, 10, 30,  2, 37,
       46,  7, 13, 52,  0, 56, 60, 29, 54, 53, 38, 28, 50, 51, 17],
      dtype=int64), 'cur_cost': 116419.0}, {'tour': array([ 7, 24, 25, 59, 53, 60, 52, 55, 31, 30, 62,  9, 61, 56,  8, 51, 22,
        2, 13, 28, 46, 14, 58, 54, 29, 44, 38, 36,  5, 12, 10, 47, 45,  0,
       64, 39, 50, 17,  4, 11, 33, 19, 65, 34, 37, 18, 27,  3, 20, 35, 43,
       15, 16, 57, 26, 42, 40,  1, 63, 23, 32, 49, 41, 48,  6, 21],
      dtype=int64), 'cur_cost': 90490.0}, {'tour': array([55, 15, 51, 18, 54, 10,  5, 47, 29, 12, 45, 11, 64, 48, 34, 60, 40,
        3, 27, 59,  6, 16, 52, 36, 56, 31, 57,  2, 17,  0, 19, 53, 61, 20,
       23, 41, 46, 39, 14,  9, 32, 63, 30, 24, 58, 50, 22, 38,  7, 42, 13,
       44,  1, 65, 43, 21,  8, 33, 37, 26,  4, 25, 62, 35, 49, 28],
      dtype=int64), 'cur_cost': 118109.0}]
2025-08-03 16:41:42,428 - ExploitationExpert - INFO - 局部搜索耗时: 0.14秒
2025-08-03 16:41:42,429 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-03 16:41:42,430 - experts.management.collaboration_manager - INFO - 个体 13 利用路径生成报告: {'new_tour': array([63, 42, 25, 22, 40, 65, 31, 39, 10, 60, 50,  3, 24, 64, 17, 47, 56,
       49, 44,  6, 16, 53, 41, 27, 52,  9,  4, 54, 45, 33, 20, 29, 38, 36,
       51, 59, 32, 26, 12, 21, 15, 46, 30, 58,  1, 62, 35, 18, 19, 43, 13,
       23,  8, 57, 28, 11, 14,  7,  5, 61,  0,  2, 37, 48, 34, 55],
      dtype=int64), 'cur_cost': 111631.0}
2025-08-03 16:41:42,432 - experts.management.collaboration_manager - INFO - 个体 13 接受新路径 (成本: 111631.00)
2025-08-03 16:41:42,432 - experts.management.collaboration_manager - INFO - 为个体 14 生成探索路径
2025-08-03 16:41:42,432 - ExplorationExpert - INFO - 开始为个体 14 生成探索路径（算法实现）
2025-08-03 16:41:42,433 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:41:42,438 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:41:42,438 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:41:42,438 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10388.0, 路径长度: 66
2025-08-03 16:41:42,439 - experts.management.collaboration_manager - INFO - 个体 14 探索路径生成报告: {'new_tour': [0, 5, 9, 4, 8, 2, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10388.0}
2025-08-03 16:41:42,439 - experts.management.collaboration_manager - INFO - 个体 14 接受新路径 (成本: 10388.00)
2025-08-03 16:41:42,439 - experts.management.collaboration_manager - INFO - 为个体 15 生成探索路径
2025-08-03 16:41:42,439 - ExplorationExpert - INFO - 开始为个体 15 生成探索路径（算法实现）
2025-08-03 16:41:42,440 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:41:42,443 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:41:42,444 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:41:42,444 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12346.0, 路径长度: 66
2025-08-03 16:41:42,445 - experts.management.collaboration_manager - INFO - 个体 15 探索路径生成报告: {'new_tour': [0, 15, 17, 22, 12, 23, 16, 18, 19, 13, 20, 21, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12346.0}
2025-08-03 16:41:42,445 - experts.management.collaboration_manager - INFO - 个体 15 接受新路径 (成本: 12346.00)
2025-08-03 16:41:42,446 - experts.management.collaboration_manager - INFO - 为个体 16 生成利用路径
2025-08-03 16:41:42,446 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:41:42,448 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:41:42,451 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 16 处的路径，新成本: 105546.0
2025-08-03 16:41:42,548 - ExploitationExpert - INFO - res_population_num: 14
2025-08-03 16:41:42,550 - ExploitationExpert - INFO - res_population_costs: [9906.0, 9594.0, 9567.0, 9551, 9542, 9542, 9542, 9540, 9540, 9540, 9534, 9521, 9521, 9521]
2025-08-03 16:41:42,551 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 63, 52, 65, 53, 62,
       59, 56, 58, 60, 64, 57, 54, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 59, 62, 64, 57,
       54, 60, 58, 56, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40,
       43, 21, 20, 13, 19, 16, 23, 22, 14, 15, 17, 12, 18, 27, 37, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 56, 58, 60, 54, 57,
       64, 62, 59, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:41:42,559 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:41:42,560 - ExploitationExpert - INFO - populations: [{'tour': [0, 20, 14, 21, 13, 23, 16, 18, 12, 22, 15, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12409.0}, {'tour': array([42,  8, 18, 40, 51, 62, 20, 58, 35,  2, 43, 57, 44, 60, 54, 23, 41,
       45, 48, 36, 19,  5, 55,  7, 50, 33, 38, 24, 12, 30,  6,  4, 61, 56,
       10, 14, 46, 53, 16, 59, 26, 52,  3, 17, 15, 28, 11,  0, 37, 49, 34,
       65, 64, 21, 47, 22, 13,  9, 25, 29, 27, 31, 32,  1, 63, 39],
      dtype=int64), 'cur_cost': 97611.0}, {'tour': [0, 9, 3, 8, 2, 6, 4, 5, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10356.0}, {'tour': [0, 21, 5, 8, 2, 6, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12845.0}, {'tour': array([34, 23, 43, 15, 24, 41, 38, 12, 56, 10, 40, 60, 14,  4, 53, 28, 50,
       65, 46, 22, 62, 18, 48,  8, 30,  6, 16, 52, 47, 29, 49, 11,  1, 21,
       57, 58,  7, 55, 42, 64, 32, 37, 25,  5, 20, 35, 54,  0, 61, 13, 19,
       51, 36, 33,  3,  2, 27, 63, 44, 31, 26, 17, 59, 39,  9, 45],
      dtype=int64), 'cur_cost': 112464.0}, {'tour': [38, 18, 26, 13, 9, 53, 12, 34, 35, 16, 19, 7, 22, 25, 33, 3, 58, 65, 52, 54, 57, 15, 37, 27, 21, 11, 64, 0, 4, 2, 23, 36, 40, 20, 32, 43, 46, 28, 49, 17, 48, 50, 41, 14, 8, 5, 56, 60, 62, 1, 10, 6, 24, 47, 45, 42, 51, 44, 29, 30, 31, 39, 55, 61, 59, 63], 'cur_cost': 61126.0}, {'tour': [0, 2, 18, 10, 8, 5, 4, 6, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12973.0}, {'tour': array([19, 41, 29,  1, 16,  3,  2, 38, 55, 53, 44, 11, 62, 35, 60, 37, 30,
       54, 65, 51, 48, 57, 20, 15, 21, 39, 43, 58, 42, 61, 18,  9,  7, 33,
       52,  0,  6,  5, 10, 13, 47, 12, 14, 17, 63, 26, 40, 50, 56, 64, 25,
       46, 23, 45, 24, 22, 59,  4, 31, 28, 27, 32,  8, 34, 49, 36],
      dtype=int64), 'cur_cost': 98890.0}, {'tour': [0, 11, 2, 7, 3, 9, 5, 4, 8, 6, 10, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10533.0}, {'tour': [9, 3, 53, 16, 22, 19, 37, 54, 31, 29, 32, 2, 61, 56, 60, 64, 62, 63, 38, 41, 33, 43, 48, 58, 52, 46, 36, 49, 25, 18, 39, 0, 10, 5, 17, 14, 28, 11, 42, 27, 21, 47, 35, 34, 40, 8, 20, 24, 57, 55, 51, 15, 12, 30, 26, 6, 7, 13, 44, 1, 4, 59, 65, 45, 23, 50], 'cur_cost': 83934.0}, {'tour': array([65, 60,  8, 26,  6,  3, 10, 32, 29, 34, 44, 12, 54, 43, 19, 23, 56,
       31, 40, 42, 61, 47, 53, 38, 33, 46, 59, 25, 58, 16, 39, 13, 22, 35,
       18, 45, 63,  5,  0, 14, 50, 27, 51, 52, 48, 64, 28,  7,  1, 36, 55,
       49, 37,  2, 62, 21,  4, 11, 17, 15, 57, 24,  9, 30, 41, 20],
      dtype=int64), 'cur_cost': 112958.0}, {'tour': [9, 3, 53, 37, 34, 22, 10, 25, 30, 27, 4, 2, 32, 28, 21, 55, 61, 62, 60, 64, 65, 63, 44, 38, 41, 47, 23, 12, 42, 15, 14, 33, 5, 18, 48, 17, 29, 7, 54, 11, 20, 39, 56, 16, 45, 24, 40, 46, 57, 35, 19, 8, 36, 51, 0, 43, 49, 1, 31, 58, 13, 26, 59, 6, 52, 50], 'cur_cost': 87569.0}, {'tour': [37, 21, 23, 31, 25, 18, 20, 32, 26, 1, 58, 52, 56, 54, 7, 53, 4, 55, 65, 9, 22, 24, 14, 10, 3, 11, 13, 36, 19, 35, 43, 15, 2, 12, 48, 44, 46, 50, 38, 30, 27, 17, 47, 34, 8, 64, 57, 0, 59, 5, 16, 40, 51, 42, 39, 41, 33, 28, 29, 49, 60, 61, 63, 62, 6, 45], 'cur_cost': 59899.0}, {'tour': array([63, 42, 25, 22, 40, 65, 31, 39, 10, 60, 50,  3, 24, 64, 17, 47, 56,
       49, 44,  6, 16, 53, 41, 27, 52,  9,  4, 54, 45, 33, 20, 29, 38, 36,
       51, 59, 32, 26, 12, 21, 15, 46, 30, 58,  1, 62, 35, 18, 19, 43, 13,
       23,  8, 57, 28, 11, 14,  7,  5, 61,  0,  2, 37, 48, 34, 55],
      dtype=int64), 'cur_cost': 111631.0}, {'tour': [0, 5, 9, 4, 8, 2, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10388.0}, {'tour': [0, 15, 17, 22, 12, 23, 16, 18, 19, 13, 20, 21, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12346.0}, {'tour': array([21, 62, 16, 61, 34, 32, 12, 25,  4, 23, 28,  3, 14, 48,  7, 29, 58,
       51, 49, 20,  5, 59, 10, 39, 30, 54, 64, 57, 19,  0, 35, 36, 33, 65,
       11, 55, 60, 47,  8, 27, 42, 52, 22, 26, 15,  1,  2, 31, 24, 63, 43,
       56, 41, 46, 17, 45, 18, 38, 50, 53,  6, 44,  9, 40, 13, 37],
      dtype=int64), 'cur_cost': 105546.0}, {'tour': array([19, 27,  1, 20, 23, 33, 59, 26,  9, 58, 49, 61, 21,  6, 44, 35, 25,
       22, 63, 39, 11, 14,  4, 15, 42, 18, 34, 31, 62, 48, 55, 43, 40, 12,
        5, 32, 16, 47, 65, 36, 64,  8, 41, 57,  3, 45, 24, 10, 30,  2, 37,
       46,  7, 13, 52,  0, 56, 60, 29, 54, 53, 38, 28, 50, 51, 17],
      dtype=int64), 'cur_cost': 116419.0}, {'tour': array([ 7, 24, 25, 59, 53, 60, 52, 55, 31, 30, 62,  9, 61, 56,  8, 51, 22,
        2, 13, 28, 46, 14, 58, 54, 29, 44, 38, 36,  5, 12, 10, 47, 45,  0,
       64, 39, 50, 17,  4, 11, 33, 19, 65, 34, 37, 18, 27,  3, 20, 35, 43,
       15, 16, 57, 26, 42, 40,  1, 63, 23, 32, 49, 41, 48,  6, 21],
      dtype=int64), 'cur_cost': 90490.0}, {'tour': array([55, 15, 51, 18, 54, 10,  5, 47, 29, 12, 45, 11, 64, 48, 34, 60, 40,
        3, 27, 59,  6, 16, 52, 36, 56, 31, 57,  2, 17,  0, 19, 53, 61, 20,
       23, 41, 46, 39, 14,  9, 32, 63, 30, 24, 58, 50, 22, 38,  7, 42, 13,
       44,  1, 65, 43, 21,  8, 33, 37, 26,  4, 25, 62, 35, 49, 28],
      dtype=int64), 'cur_cost': 118109.0}]
2025-08-03 16:41:42,569 - ExploitationExpert - INFO - 局部搜索耗时: 0.12秒
2025-08-03 16:41:42,569 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-03 16:41:42,570 - experts.management.collaboration_manager - INFO - 个体 16 利用路径生成报告: {'new_tour': array([21, 62, 16, 61, 34, 32, 12, 25,  4, 23, 28,  3, 14, 48,  7, 29, 58,
       51, 49, 20,  5, 59, 10, 39, 30, 54, 64, 57, 19,  0, 35, 36, 33, 65,
       11, 55, 60, 47,  8, 27, 42, 52, 22, 26, 15,  1,  2, 31, 24, 63, 43,
       56, 41, 46, 17, 45, 18, 38, 50, 53,  6, 44,  9, 40, 13, 37],
      dtype=int64), 'cur_cost': 105546.0}
2025-08-03 16:41:42,571 - experts.management.collaboration_manager - INFO - 个体 16 接受新路径 (成本: 105546.00)
2025-08-03 16:41:42,571 - experts.management.collaboration_manager - INFO - 为个体 17 生成探索路径
2025-08-03 16:41:42,571 - ExplorationExpert - INFO - 开始为个体 17 生成探索路径（算法实现）
2025-08-03 16:41:42,572 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:41:42,576 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:41:42,576 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:41:42,577 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12330.0, 路径长度: 66
2025-08-03 16:41:42,577 - experts.management.collaboration_manager - INFO - 个体 17 探索路径生成报告: {'new_tour': [0, 9, 22, 17, 12, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12330.0}
2025-08-03 16:41:42,577 - experts.management.collaboration_manager - INFO - 个体 17 接受新路径 (成本: 12330.00)
2025-08-03 16:41:42,577 - experts.management.collaboration_manager - INFO - 为个体 18 生成探索路径
2025-08-03 16:41:42,578 - ExplorationExpert - INFO - 开始为个体 18 生成探索路径（算法实现）
2025-08-03 16:41:42,578 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:41:42,588 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:41:42,588 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:41:42,589 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98097.0, 路径长度: 66
2025-08-03 16:41:42,589 - experts.management.collaboration_manager - INFO - 个体 18 探索路径生成报告: {'new_tour': [38, 5, 13, 9, 6, 30, 3, 17, 19, 25, 61, 63, 57, 15, 37, 58, 47, 36, 18, 23, 40, 43, 29, 60, 62, 1, 42, 7, 44, 39, 49, 65, 56, 53, 46, 52, 59, 32, 20, 28, 51, 64, 4, 10, 55, 35, 11, 21, 34, 31, 27, 48, 2, 0, 50, 33, 16, 41, 54, 45, 12, 8, 22, 26, 14, 24], 'cur_cost': 98097.0}
2025-08-03 16:41:42,590 - experts.management.collaboration_manager - INFO - 个体 18 接受新路径 (成本: 98097.00)
2025-08-03 16:41:42,590 - experts.management.collaboration_manager - INFO - 为个体 19 生成利用路径
2025-08-03 16:41:42,590 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:41:42,590 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:41:42,591 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 19 处的路径，新成本: 102446.0
2025-08-03 16:41:42,684 - ExploitationExpert - INFO - res_population_num: 14
2025-08-03 16:41:42,685 - ExploitationExpert - INFO - res_population_costs: [9906.0, 9594.0, 9567.0, 9551, 9542, 9542, 9542, 9540, 9540, 9540, 9534, 9521, 9521, 9521]
2025-08-03 16:41:42,685 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 63, 52, 65, 53, 62,
       59, 56, 58, 60, 64, 57, 54, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 59, 62, 64, 57,
       54, 60, 58, 56, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40,
       43, 21, 20, 13, 19, 16, 23, 22, 14, 15, 17, 12, 18, 27, 37, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 56, 58, 60, 54, 57,
       64, 62, 59, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:41:42,693 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:41:42,693 - ExploitationExpert - INFO - populations: [{'tour': [0, 20, 14, 21, 13, 23, 16, 18, 12, 22, 15, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12409.0}, {'tour': array([42,  8, 18, 40, 51, 62, 20, 58, 35,  2, 43, 57, 44, 60, 54, 23, 41,
       45, 48, 36, 19,  5, 55,  7, 50, 33, 38, 24, 12, 30,  6,  4, 61, 56,
       10, 14, 46, 53, 16, 59, 26, 52,  3, 17, 15, 28, 11,  0, 37, 49, 34,
       65, 64, 21, 47, 22, 13,  9, 25, 29, 27, 31, 32,  1, 63, 39],
      dtype=int64), 'cur_cost': 97611.0}, {'tour': [0, 9, 3, 8, 2, 6, 4, 5, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10356.0}, {'tour': [0, 21, 5, 8, 2, 6, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12845.0}, {'tour': array([34, 23, 43, 15, 24, 41, 38, 12, 56, 10, 40, 60, 14,  4, 53, 28, 50,
       65, 46, 22, 62, 18, 48,  8, 30,  6, 16, 52, 47, 29, 49, 11,  1, 21,
       57, 58,  7, 55, 42, 64, 32, 37, 25,  5, 20, 35, 54,  0, 61, 13, 19,
       51, 36, 33,  3,  2, 27, 63, 44, 31, 26, 17, 59, 39,  9, 45],
      dtype=int64), 'cur_cost': 112464.0}, {'tour': [38, 18, 26, 13, 9, 53, 12, 34, 35, 16, 19, 7, 22, 25, 33, 3, 58, 65, 52, 54, 57, 15, 37, 27, 21, 11, 64, 0, 4, 2, 23, 36, 40, 20, 32, 43, 46, 28, 49, 17, 48, 50, 41, 14, 8, 5, 56, 60, 62, 1, 10, 6, 24, 47, 45, 42, 51, 44, 29, 30, 31, 39, 55, 61, 59, 63], 'cur_cost': 61126.0}, {'tour': [0, 2, 18, 10, 8, 5, 4, 6, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12973.0}, {'tour': array([19, 41, 29,  1, 16,  3,  2, 38, 55, 53, 44, 11, 62, 35, 60, 37, 30,
       54, 65, 51, 48, 57, 20, 15, 21, 39, 43, 58, 42, 61, 18,  9,  7, 33,
       52,  0,  6,  5, 10, 13, 47, 12, 14, 17, 63, 26, 40, 50, 56, 64, 25,
       46, 23, 45, 24, 22, 59,  4, 31, 28, 27, 32,  8, 34, 49, 36],
      dtype=int64), 'cur_cost': 98890.0}, {'tour': [0, 11, 2, 7, 3, 9, 5, 4, 8, 6, 10, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10533.0}, {'tour': [9, 3, 53, 16, 22, 19, 37, 54, 31, 29, 32, 2, 61, 56, 60, 64, 62, 63, 38, 41, 33, 43, 48, 58, 52, 46, 36, 49, 25, 18, 39, 0, 10, 5, 17, 14, 28, 11, 42, 27, 21, 47, 35, 34, 40, 8, 20, 24, 57, 55, 51, 15, 12, 30, 26, 6, 7, 13, 44, 1, 4, 59, 65, 45, 23, 50], 'cur_cost': 83934.0}, {'tour': array([65, 60,  8, 26,  6,  3, 10, 32, 29, 34, 44, 12, 54, 43, 19, 23, 56,
       31, 40, 42, 61, 47, 53, 38, 33, 46, 59, 25, 58, 16, 39, 13, 22, 35,
       18, 45, 63,  5,  0, 14, 50, 27, 51, 52, 48, 64, 28,  7,  1, 36, 55,
       49, 37,  2, 62, 21,  4, 11, 17, 15, 57, 24,  9, 30, 41, 20],
      dtype=int64), 'cur_cost': 112958.0}, {'tour': [9, 3, 53, 37, 34, 22, 10, 25, 30, 27, 4, 2, 32, 28, 21, 55, 61, 62, 60, 64, 65, 63, 44, 38, 41, 47, 23, 12, 42, 15, 14, 33, 5, 18, 48, 17, 29, 7, 54, 11, 20, 39, 56, 16, 45, 24, 40, 46, 57, 35, 19, 8, 36, 51, 0, 43, 49, 1, 31, 58, 13, 26, 59, 6, 52, 50], 'cur_cost': 87569.0}, {'tour': [37, 21, 23, 31, 25, 18, 20, 32, 26, 1, 58, 52, 56, 54, 7, 53, 4, 55, 65, 9, 22, 24, 14, 10, 3, 11, 13, 36, 19, 35, 43, 15, 2, 12, 48, 44, 46, 50, 38, 30, 27, 17, 47, 34, 8, 64, 57, 0, 59, 5, 16, 40, 51, 42, 39, 41, 33, 28, 29, 49, 60, 61, 63, 62, 6, 45], 'cur_cost': 59899.0}, {'tour': array([63, 42, 25, 22, 40, 65, 31, 39, 10, 60, 50,  3, 24, 64, 17, 47, 56,
       49, 44,  6, 16, 53, 41, 27, 52,  9,  4, 54, 45, 33, 20, 29, 38, 36,
       51, 59, 32, 26, 12, 21, 15, 46, 30, 58,  1, 62, 35, 18, 19, 43, 13,
       23,  8, 57, 28, 11, 14,  7,  5, 61,  0,  2, 37, 48, 34, 55],
      dtype=int64), 'cur_cost': 111631.0}, {'tour': [0, 5, 9, 4, 8, 2, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10388.0}, {'tour': [0, 15, 17, 22, 12, 23, 16, 18, 19, 13, 20, 21, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12346.0}, {'tour': array([21, 62, 16, 61, 34, 32, 12, 25,  4, 23, 28,  3, 14, 48,  7, 29, 58,
       51, 49, 20,  5, 59, 10, 39, 30, 54, 64, 57, 19,  0, 35, 36, 33, 65,
       11, 55, 60, 47,  8, 27, 42, 52, 22, 26, 15,  1,  2, 31, 24, 63, 43,
       56, 41, 46, 17, 45, 18, 38, 50, 53,  6, 44,  9, 40, 13, 37],
      dtype=int64), 'cur_cost': 105546.0}, {'tour': [0, 9, 22, 17, 12, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12330.0}, {'tour': [38, 5, 13, 9, 6, 30, 3, 17, 19, 25, 61, 63, 57, 15, 37, 58, 47, 36, 18, 23, 40, 43, 29, 60, 62, 1, 42, 7, 44, 39, 49, 65, 56, 53, 46, 52, 59, 32, 20, 28, 51, 64, 4, 10, 55, 35, 11, 21, 34, 31, 27, 48, 2, 0, 50, 33, 16, 41, 54, 45, 12, 8, 22, 26, 14, 24], 'cur_cost': 98097.0}, {'tour': array([31, 45, 14, 35, 24, 51, 53, 28, 57, 38, 29, 16,  8, 30, 15, 60, 11,
       13,  0, 41, 44,  7, 34,  3, 42, 62, 61, 26, 46, 18, 20, 40, 32, 33,
       64, 37, 23,  4, 17, 19, 43, 49, 27,  1, 63,  9, 25,  5, 48, 50, 52,
        6, 55, 12, 22, 47, 56, 54, 65, 21, 36, 39, 59, 58,  2, 10],
      dtype=int64), 'cur_cost': 102446.0}]
2025-08-03 16:41:42,701 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒
2025-08-03 16:41:42,702 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-03 16:41:42,702 - experts.management.collaboration_manager - INFO - 个体 19 利用路径生成报告: {'new_tour': array([31, 45, 14, 35, 24, 51, 53, 28, 57, 38, 29, 16,  8, 30, 15, 60, 11,
       13,  0, 41, 44,  7, 34,  3, 42, 62, 61, 26, 46, 18, 20, 40, 32, 33,
       64, 37, 23,  4, 17, 19, 43, 49, 27,  1, 63,  9, 25,  5, 48, 50, 52,
        6, 55, 12, 22, 47, 56, 54, 65, 21, 36, 39, 59, 58,  2, 10],
      dtype=int64), 'cur_cost': 102446.0}
2025-08-03 16:41:42,702 - experts.management.collaboration_manager - INFO - 个体 19 接受新路径 (成本: 102446.00)
2025-08-03 16:41:42,703 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 16, 'rejected': 4, 'elite_protected': 4}
2025-08-03 16:41:42,703 - experts.management.collaboration_manager - INFO - 接受率: 16/20 (80.0%)
2025-08-03 16:41:42,704 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 14, 21, 13, 23, 16, 18, 12, 22, 15, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12409.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([42,  8, 18, 40, 51, 62, 20, 58, 35,  2, 43, 57, 44, 60, 54, 23, 41,
       45, 48, 36, 19,  5, 55,  7, 50, 33, 38, 24, 12, 30,  6,  4, 61, 56,
       10, 14, 46, 53, 16, 59, 26, 52,  3, 17, 15, 28, 11,  0, 37, 49, 34,
       65, 64, 21, 47, 22, 13,  9, 25, 29, 27, 31, 32,  1, 63, 39],
      dtype=int64), 'cur_cost': 97611.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 3, 8, 2, 6, 4, 5, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10356.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 5, 8, 2, 6, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12845.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([34, 23, 43, 15, 24, 41, 38, 12, 56, 10, 40, 60, 14,  4, 53, 28, 50,
       65, 46, 22, 62, 18, 48,  8, 30,  6, 16, 52, 47, 29, 49, 11,  1, 21,
       57, 58,  7, 55, 42, 64, 32, 37, 25,  5, 20, 35, 54,  0, 61, 13, 19,
       51, 36, 33,  3,  2, 27, 63, 44, 31, 26, 17, 59, 39,  9, 45],
      dtype=int64), 'cur_cost': 112464.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [38, 18, 26, 13, 9, 53, 12, 34, 35, 16, 19, 7, 22, 25, 33, 3, 58, 65, 52, 54, 57, 15, 37, 27, 21, 11, 64, 0, 4, 2, 23, 36, 40, 20, 32, 43, 46, 28, 49, 17, 48, 50, 41, 14, 8, 5, 56, 60, 62, 1, 10, 6, 24, 47, 45, 42, 51, 44, 29, 30, 31, 39, 55, 61, 59, 63], 'cur_cost': 61126.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 18, 10, 8, 5, 4, 6, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12973.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([19, 41, 29,  1, 16,  3,  2, 38, 55, 53, 44, 11, 62, 35, 60, 37, 30,
       54, 65, 51, 48, 57, 20, 15, 21, 39, 43, 58, 42, 61, 18,  9,  7, 33,
       52,  0,  6,  5, 10, 13, 47, 12, 14, 17, 63, 26, 40, 50, 56, 64, 25,
       46, 23, 45, 24, 22, 59,  4, 31, 28, 27, 32,  8, 34, 49, 36],
      dtype=int64), 'cur_cost': 98890.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 2, 7, 3, 9, 5, 4, 8, 6, 10, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10533.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [9, 3, 53, 16, 22, 19, 37, 54, 31, 29, 32, 2, 61, 56, 60, 64, 62, 63, 38, 41, 33, 43, 48, 58, 52, 46, 36, 49, 25, 18, 39, 0, 10, 5, 17, 14, 28, 11, 42, 27, 21, 47, 35, 34, 40, 8, 20, 24, 57, 55, 51, 15, 12, 30, 26, 6, 7, 13, 44, 1, 4, 59, 65, 45, 23, 50], 'cur_cost': 83934.0}}, {'individual': 10, 'strategy': 'exploit', 'path_data': {'new_tour': array([65, 60,  8, 26,  6,  3, 10, 32, 29, 34, 44, 12, 54, 43, 19, 23, 56,
       31, 40, 42, 61, 47, 53, 38, 33, 46, 59, 25, 58, 16, 39, 13, 22, 35,
       18, 45, 63,  5,  0, 14, 50, 27, 51, 52, 48, 64, 28,  7,  1, 36, 55,
       49, 37,  2, 62, 21,  4, 11, 17, 15, 57, 24,  9, 30, 41, 20],
      dtype=int64), 'cur_cost': 112958.0}}, {'individual': 11, 'strategy': 'explore', 'path_data': {'new_tour': [9, 3, 53, 37, 34, 22, 10, 25, 30, 27, 4, 2, 32, 28, 21, 55, 61, 62, 60, 64, 65, 63, 44, 38, 41, 47, 23, 12, 42, 15, 14, 33, 5, 18, 48, 17, 29, 7, 54, 11, 20, 39, 56, 16, 45, 24, 40, 46, 57, 35, 19, 8, 36, 51, 0, 43, 49, 1, 31, 58, 13, 26, 59, 6, 52, 50], 'cur_cost': 87569.0}}, {'individual': 12, 'strategy': 'explore', 'path_data': {'new_tour': [37, 21, 23, 31, 25, 18, 20, 32, 26, 1, 58, 52, 56, 54, 7, 53, 4, 55, 65, 9, 22, 24, 14, 10, 3, 11, 13, 36, 19, 35, 43, 15, 2, 12, 48, 44, 46, 50, 38, 30, 27, 17, 47, 34, 8, 64, 57, 0, 59, 5, 16, 40, 51, 42, 39, 41, 33, 28, 29, 49, 60, 61, 63, 62, 6, 45], 'cur_cost': 59899.0}}, {'individual': 13, 'strategy': 'exploit', 'path_data': {'new_tour': array([63, 42, 25, 22, 40, 65, 31, 39, 10, 60, 50,  3, 24, 64, 17, 47, 56,
       49, 44,  6, 16, 53, 41, 27, 52,  9,  4, 54, 45, 33, 20, 29, 38, 36,
       51, 59, 32, 26, 12, 21, 15, 46, 30, 58,  1, 62, 35, 18, 19, 43, 13,
       23,  8, 57, 28, 11, 14,  7,  5, 61,  0,  2, 37, 48, 34, 55],
      dtype=int64), 'cur_cost': 111631.0}}, {'individual': 14, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 9, 4, 8, 2, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10388.0}}, {'individual': 15, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 17, 22, 12, 23, 16, 18, 19, 13, 20, 21, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12346.0}}, {'individual': 16, 'strategy': 'exploit', 'path_data': {'new_tour': array([21, 62, 16, 61, 34, 32, 12, 25,  4, 23, 28,  3, 14, 48,  7, 29, 58,
       51, 49, 20,  5, 59, 10, 39, 30, 54, 64, 57, 19,  0, 35, 36, 33, 65,
       11, 55, 60, 47,  8, 27, 42, 52, 22, 26, 15,  1,  2, 31, 24, 63, 43,
       56, 41, 46, 17, 45, 18, 38, 50, 53,  6, 44,  9, 40, 13, 37],
      dtype=int64), 'cur_cost': 105546.0}}, {'individual': 17, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 22, 17, 12, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12330.0}}, {'individual': 18, 'strategy': 'explore', 'path_data': {'new_tour': [38, 5, 13, 9, 6, 30, 3, 17, 19, 25, 61, 63, 57, 15, 37, 58, 47, 36, 18, 23, 40, 43, 29, 60, 62, 1, 42, 7, 44, 39, 49, 65, 56, 53, 46, 52, 59, 32, 20, 28, 51, 64, 4, 10, 55, 35, 11, 21, 34, 31, 27, 48, 2, 0, 50, 33, 16, 41, 54, 45, 12, 8, 22, 26, 14, 24], 'cur_cost': 98097.0}}, {'individual': 19, 'strategy': 'exploit', 'path_data': {'new_tour': array([31, 45, 14, 35, 24, 51, 53, 28, 57, 38, 29, 16,  8, 30, 15, 60, 11,
       13,  0, 41, 44,  7, 34,  3, 42, 62, 61, 26, 46, 18, 20, 40, 32, 33,
       64, 37, 23,  4, 17, 19, 43, 49, 27,  1, 63,  9, 25,  5, 48, 50, 52,
        6, 55, 12, 22, 47, 56, 54, 65, 21, 36, 39, 59, 58,  2, 10],
      dtype=int64), 'cur_cost': 102446.0}}]
2025-08-03 16:41:42,707 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-03 16:41:42,707 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:41:42,724 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=10356.000, 多样性=0.943
2025-08-03 16:41:42,725 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-03 16:41:42,725 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-03 16:41:42,725 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-03 16:41:42,728 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 1, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.085078286490747, 'best_improvement': -0.041746303188814005}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.03447149158634212}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 14, 'new_count': 14, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.8418248418248419, 'new_diversity': 0.8418248418248419, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-03 16:41:42,732 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-03 16:41:42,738 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_solution.json
2025-08-03 16:41:42,738 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_20250803_164142.solution
2025-08-03 16:41:42,739 - __main__ - INFO - 实例 composite13_66 处理完成
