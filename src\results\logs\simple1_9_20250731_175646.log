2025-07-31 17:56:46,397 - __main__ - INFO - simple1_9 开始进化第 1 代
2025-07-31 17:56:46,398 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-07-31 17:56:46,398 - StatsExpert - INFO - 开始统计分析
2025-07-31 17:56:46,398 - StatsExpert - INFO - 统计分析完成: 种群大小=5, 最优成本=811.0, 多样性=0.822
2025-07-31 17:56:46,398 - PathExpert - INFO - 开始路径结构分析
2025-07-31 17:56:46,399 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.100
2025-07-31 17:56:46,399 - EliteExpert - INFO - 开始精英解分析
2025-07-31 17:56:46,401 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/2)
2025-07-31 17:56:46,402 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'unknown', 'deceptiveness': 'unknown', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'balance', 'operators': ['mutation', 'crossover'], 'parameters': {}}, 'iteration_info': {'current': 0, 'total': 2, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1753955806.4016201, 'status': 'default_fallback'}}
2025-07-31 17:56:46,402 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-07-31 17:56:46,402 - StrategyExpert - INFO - 开始策略分配分析
2025-07-31 17:56:46,402 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 5 individuals
  • diversity: 0.5
  • best_cost: 811.0
  • mean_cost: 989.6
- Search-space: ruggedness 0.5, modality unknown, deceptiveness unknown
- Evolution phase: exploration
- Landscape focus suggestion: balance
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-31 17:56:46,402 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-31 17:56:46,402 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-31 17:56:47,710 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "exploit"
  },
  "rationale": "High diversity suggests exploration is needed. The best two individuals should exploit."
}
```
2025-07-31 17:56:47,711 - utils.utils - INFO - 成功从JSON中提取数据
2025-07-31 17:56:47,711 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-31 17:56:47,712 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-31 17:56:47,712 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "exploit"
  },
  "rationale": "High diversity suggests exploration is needed. The best two individuals should exploit."
}
```
2025-07-31 17:56:47,712 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-07-31 17:56:47,712 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-31 17:56:47,712 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "exploit"
  },
  "rationale": "High diversity suggests exploration is needed. The best two individuals should exploit."
}
```
2025-07-31 17:56:47,712 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-07-31 17:56:47,712 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-07-31 17:56:47,712 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-07-31 17:56:47,712 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:56:47,713 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-07-31 17:56:47,713 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:56:47,834 - ExplorationExpert - INFO - 探索路径生成完成，成本: 948.0, 路径长度: 9
2025-07-31 17:56:47,834 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [1, 4, 0, 8, 3, 5, 7, 6, 2], 'cur_cost': 948.0}
2025-07-31 17:56:47,834 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-07-31 17:56:47,834 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-07-31 17:56:47,834 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:56:47,835 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-07-31 17:56:47,835 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:56:47,835 - ExplorationExpert - INFO - 探索路径生成完成，成本: 836.0, 路径长度: 9
2025-07-31 17:56:47,835 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [4, 8, 0, 1, 7, 3, 5, 6, 2], 'cur_cost': 836.0}
2025-07-31 17:56:47,835 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-07-31 17:56:47,835 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-07-31 17:56:47,836 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:56:47,836 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-07-31 17:56:47,836 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:56:47,836 - ExplorationExpert - INFO - 探索路径生成完成，成本: 973.0, 路径长度: 9
2025-07-31 17:56:47,836 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [7, 0, 6, 8, 4, 2, 3, 5, 1], 'cur_cost': 973.0}
2025-07-31 17:56:47,836 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-07-31 17:56:47,836 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:56:47,838 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:56:47,838 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1043.0
2025-07-31 17:56:49,206 - ExploitationExpert - INFO - res_population_num: 1
2025-07-31 17:56:49,206 - ExploitationExpert - INFO - res_population_costs: [680.0]
2025-07-31 17:56:49,206 - ExploitationExpert - INFO - res_populations: [array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-07-31 17:56:49,207 - ExploitationExpert - INFO - populations_num: 5
2025-07-31 17:56:49,207 - ExploitationExpert - INFO - populations: [{'tour': [1, 4, 0, 8, 3, 5, 7, 6, 2], 'cur_cost': 948.0}, {'tour': [4, 8, 0, 1, 7, 3, 5, 6, 2], 'cur_cost': 836.0}, {'tour': [7, 0, 6, 8, 4, 2, 3, 5, 1], 'cur_cost': 973.0}, {'tour': array([6, 8, 5, 7, 3, 1, 4, 0, 2], dtype=int64), 'cur_cost': 1043.0}, {'tour': array([5, 6, 7, 2, 1, 8, 0, 3, 4], dtype=int64), 'cur_cost': 1119.0}]
2025-07-31 17:56:49,207 - ExploitationExpert - INFO - 局部搜索耗时: 1.37秒
2025-07-31 17:56:49,207 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-07-31 17:56:49,208 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([6, 8, 5, 7, 3, 1, 4, 0, 2], dtype=int64), 'cur_cost': 1043.0}
2025-07-31 17:56:49,208 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-07-31 17:56:49,208 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:56:49,208 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:56:49,208 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 975.0
2025-07-31 17:56:51,045 - ExploitationExpert - INFO - res_population_num: 2
2025-07-31 17:56:51,046 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0]
2025-07-31 17:56:51,046 - ExploitationExpert - INFO - res_populations: [array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64)]
2025-07-31 17:56:51,047 - ExploitationExpert - INFO - populations_num: 5
2025-07-31 17:56:51,047 - ExploitationExpert - INFO - populations: [{'tour': [1, 4, 0, 8, 3, 5, 7, 6, 2], 'cur_cost': 948.0}, {'tour': [4, 8, 0, 1, 7, 3, 5, 6, 2], 'cur_cost': 836.0}, {'tour': [7, 0, 6, 8, 4, 2, 3, 5, 1], 'cur_cost': 973.0}, {'tour': array([6, 8, 5, 7, 3, 1, 4, 0, 2], dtype=int64), 'cur_cost': 1043.0}, {'tour': array([8, 2, 0, 4, 3, 6, 1, 5, 7], dtype=int64), 'cur_cost': 975.0}]
2025-07-31 17:56:51,047 - ExploitationExpert - INFO - 局部搜索耗时: 1.84秒
2025-07-31 17:56:51,047 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-07-31 17:56:51,047 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([8, 2, 0, 4, 3, 6, 1, 5, 7], dtype=int64), 'cur_cost': 975.0}
2025-07-31 17:56:51,048 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 0, 8, 3, 5, 7, 6, 2], 'cur_cost': 948.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [4, 8, 0, 1, 7, 3, 5, 6, 2], 'cur_cost': 836.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [7, 0, 6, 8, 4, 2, 3, 5, 1], 'cur_cost': 973.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 8, 5, 7, 3, 1, 4, 0, 2], dtype=int64), 'cur_cost': 1043.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 2, 0, 4, 3, 6, 1, 5, 7], dtype=int64), 'cur_cost': 975.0}}]
2025-07-31 17:56:51,048 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-07-31 17:56:51,048 - StatsExpert - INFO - 开始统计分析
2025-07-31 17:56:51,048 - StatsExpert - INFO - 统计分析完成: 种群大小=5, 最优成本=836.0, 多样性=0.767
2025-07-31 17:56:51,048 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-07-31 17:56:51,048 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-07-31 17:56:51,048 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-07-31 17:56:51,048 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 2, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.024018421611198822, 'best_improvement': -0.030826140567200986}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.0675675675675676}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.5555555555555556, 'new_diversity': 0.5555555555555556, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-07-31 17:56:51,049 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-07-31 17:56:51,049 - __main__ - INFO - simple1_9 开始进化第 2 代
2025-07-31 17:56:51,049 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-07-31 17:56:51,049 - StatsExpert - INFO - 开始统计分析
2025-07-31 17:56:51,049 - StatsExpert - INFO - 统计分析完成: 种群大小=5, 最优成本=836.0, 多样性=0.767
2025-07-31 17:56:51,049 - PathExpert - INFO - 开始路径结构分析
2025-07-31 17:56:51,049 - PathExpert - INFO - 路径结构分析完成: 公共边数量=4, 路径相似性=0.144
2025-07-31 17:56:51,050 - EliteExpert - INFO - 开始精英解分析
2025-07-31 17:56:51,050 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.556
2025-07-31 17:56:51,051 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/2)
2025-07-31 17:56:51,051 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'unknown', 'deceptiveness': 'unknown', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'balance', 'operators': ['mutation', 'crossover'], 'parameters': {}}, 'iteration_info': {'current': 1, 'total': 2, 'progress': 0.5}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1753955811.0516849, 'status': 'default_fallback'}}
2025-07-31 17:56:51,051 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-07-31 17:56:51,052 - StrategyExpert - INFO - 开始策略分配分析
2025-07-31 17:56:51,052 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 5 individuals
  • diversity: 0.5
  • best_cost: 836.0
  • mean_cost: 955.0
- Search-space: ruggedness 0.5, modality unknown, deceptiveness unknown
- Evolution phase: exploitation
- Landscape focus suggestion: balance
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: {'overall_score': 100, 'iteration': 0, 'total_iterations': 2, 'cost_improvement': {'status': 'significant_improvement', ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-31 17:56:51,052 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-31 17:56:51,052 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-31 17:56:52,562 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.3,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "explore",
    "3": "exploit",
    "4": "exploit"
  },
  "rationale": "Significant cost improvement suggests exploitation. Focus on refining promising solutions. Minor exploration allowed for diversity."
}
```
2025-07-31 17:56:52,562 - utils.utils - INFO - 成功从JSON中提取数据
2025-07-31 17:56:52,562 - StrategyExpert - INFO - 策略分配完成: ['exploit', 'exploit', 'explore', 'exploit', 'exploit']
2025-07-31 17:56:52,563 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'exploit', 'explore', 'exploit', 'exploit']
2025-07-31 17:56:52,563 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.3,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "explore",
    "3": "exploit",
    "4": "exploit"
  },
  "rationale": "Significant cost improvement suggests exploitation. Focus on refining promising solutions. Minor exploration allowed for diversity."
}
```
2025-07-31 17:56:52,563 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-07-31 17:56:52,563 - __main__ - INFO - 策略分配: ['exploit', 'exploit', 'explore', 'exploit', 'exploit']
2025-07-31 17:56:52,563 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.3,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "explore",
    "3": "exploit",
    "4": "exploit"
  },
  "rationale": "Significant cost improvement suggests exploitation. Focus on refining promising solutions. Minor exploration allowed for diversity."
}
```
2025-07-31 17:56:52,564 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-07-31 17:56:52,564 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-07-31 17:56:52,564 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:56:52,564 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:56:52,564 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 1088.0
2025-07-31 17:56:52,598 - ExploitationExpert - INFO - res_population_num: 3
2025-07-31 17:56:52,598 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-07-31 17:56:52,598 - ExploitationExpert - INFO - res_populations: [array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64)]
2025-07-31 17:56:52,599 - ExploitationExpert - INFO - populations_num: 5
2025-07-31 17:56:52,599 - ExploitationExpert - INFO - populations: [{'tour': array([2, 3, 6, 4, 0, 5, 1, 7, 8], dtype=int64), 'cur_cost': 1088.0}, {'tour': [4, 8, 0, 1, 7, 3, 5, 6, 2], 'cur_cost': 836.0}, {'tour': [7, 0, 6, 8, 4, 2, 3, 5, 1], 'cur_cost': 973.0}, {'tour': [6, 8, 5, 7, 3, 1, 4, 0, 2], 'cur_cost': 1043.0}, {'tour': [8, 2, 0, 4, 3, 6, 1, 5, 7], 'cur_cost': 975.0}]
2025-07-31 17:56:52,599 - ExploitationExpert - INFO - 局部搜索耗时: 0.04秒
2025-07-31 17:56:52,599 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-07-31 17:56:52,599 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([2, 3, 6, 4, 0, 5, 1, 7, 8], dtype=int64), 'cur_cost': 1088.0}
2025-07-31 17:56:52,599 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-07-31 17:56:52,599 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:56:52,600 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:56:52,600 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 984.0
2025-07-31 17:56:52,633 - ExploitationExpert - INFO - res_population_num: 3
2025-07-31 17:56:52,633 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-07-31 17:56:52,633 - ExploitationExpert - INFO - res_populations: [array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64)]
2025-07-31 17:56:52,634 - ExploitationExpert - INFO - populations_num: 5
2025-07-31 17:56:52,634 - ExploitationExpert - INFO - populations: [{'tour': array([2, 3, 6, 4, 0, 5, 1, 7, 8], dtype=int64), 'cur_cost': 1088.0}, {'tour': array([3, 0, 1, 2, 7, 6, 4, 5, 8], dtype=int64), 'cur_cost': 984.0}, {'tour': [7, 0, 6, 8, 4, 2, 3, 5, 1], 'cur_cost': 973.0}, {'tour': [6, 8, 5, 7, 3, 1, 4, 0, 2], 'cur_cost': 1043.0}, {'tour': [8, 2, 0, 4, 3, 6, 1, 5, 7], 'cur_cost': 975.0}]
2025-07-31 17:56:52,634 - ExploitationExpert - INFO - 局部搜索耗时: 0.03秒
2025-07-31 17:56:52,634 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-07-31 17:56:52,635 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([3, 0, 1, 2, 7, 6, 4, 5, 8], dtype=int64), 'cur_cost': 984.0}
2025-07-31 17:56:52,635 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-07-31 17:56:52,635 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-07-31 17:56:52,635 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:56:52,635 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-07-31 17:56:52,635 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:56:52,635 - ExplorationExpert - INFO - 探索路径生成完成，成本: 912.0, 路径长度: 9
2025-07-31 17:56:52,635 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [1, 6, 5, 8, 7, 0, 4, 2, 3], 'cur_cost': 912.0}
2025-07-31 17:56:52,635 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-07-31 17:56:52,635 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:56:52,636 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:56:52,636 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1027.0
2025-07-31 17:56:52,669 - ExploitationExpert - INFO - res_population_num: 3
2025-07-31 17:56:52,669 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-07-31 17:56:52,669 - ExploitationExpert - INFO - res_populations: [array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64)]
2025-07-31 17:56:52,670 - ExploitationExpert - INFO - populations_num: 5
2025-07-31 17:56:52,670 - ExploitationExpert - INFO - populations: [{'tour': array([2, 3, 6, 4, 0, 5, 1, 7, 8], dtype=int64), 'cur_cost': 1088.0}, {'tour': array([3, 0, 1, 2, 7, 6, 4, 5, 8], dtype=int64), 'cur_cost': 984.0}, {'tour': [1, 6, 5, 8, 7, 0, 4, 2, 3], 'cur_cost': 912.0}, {'tour': array([3, 6, 0, 2, 7, 8, 1, 4, 5], dtype=int64), 'cur_cost': 1027.0}, {'tour': [8, 2, 0, 4, 3, 6, 1, 5, 7], 'cur_cost': 975.0}]
2025-07-31 17:56:52,670 - ExploitationExpert - INFO - 局部搜索耗时: 0.03秒
2025-07-31 17:56:52,670 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-07-31 17:56:52,670 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([3, 6, 0, 2, 7, 8, 1, 4, 5], dtype=int64), 'cur_cost': 1027.0}
2025-07-31 17:56:52,671 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-07-31 17:56:52,671 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:56:52,671 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:56:52,671 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 996.0
2025-07-31 17:56:52,703 - ExploitationExpert - INFO - res_population_num: 3
2025-07-31 17:56:52,703 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-07-31 17:56:52,703 - ExploitationExpert - INFO - res_populations: [array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64)]
2025-07-31 17:56:52,704 - ExploitationExpert - INFO - populations_num: 5
2025-07-31 17:56:52,704 - ExploitationExpert - INFO - populations: [{'tour': array([2, 3, 6, 4, 0, 5, 1, 7, 8], dtype=int64), 'cur_cost': 1088.0}, {'tour': array([3, 0, 1, 2, 7, 6, 4, 5, 8], dtype=int64), 'cur_cost': 984.0}, {'tour': [1, 6, 5, 8, 7, 0, 4, 2, 3], 'cur_cost': 912.0}, {'tour': array([3, 6, 0, 2, 7, 8, 1, 4, 5], dtype=int64), 'cur_cost': 1027.0}, {'tour': array([0, 4, 1, 3, 5, 8, 6, 7, 2], dtype=int64), 'cur_cost': 996.0}]
2025-07-31 17:56:52,704 - ExploitationExpert - INFO - 局部搜索耗时: 0.03秒
2025-07-31 17:56:52,704 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-07-31 17:56:52,705 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([0, 4, 1, 3, 5, 8, 6, 7, 2], dtype=int64), 'cur_cost': 996.0}
2025-07-31 17:56:52,705 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 3, 6, 4, 0, 5, 1, 7, 8], dtype=int64), 'cur_cost': 1088.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 0, 1, 2, 7, 6, 4, 5, 8], dtype=int64), 'cur_cost': 984.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [1, 6, 5, 8, 7, 0, 4, 2, 3], 'cur_cost': 912.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 6, 0, 2, 7, 8, 1, 4, 5], dtype=int64), 'cur_cost': 1027.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 4, 1, 3, 5, 8, 6, 7, 2], dtype=int64), 'cur_cost': 996.0}}]
2025-07-31 17:56:52,705 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-07-31 17:56:52,705 - StatsExpert - INFO - 开始统计分析
2025-07-31 17:56:52,705 - StatsExpert - INFO - 统计分析完成: 种群大小=5, 最优成本=912.0, 多样性=0.767
2025-07-31 17:56:52,705 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-07-31 17:56:52,705 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-07-31 17:56:52,706 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-07-31 17:56:52,706 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 1, 'total_iterations': 2, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.0608800843440149, 'best_improvement': -0.09090909090909091}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 1.4481169886415086e-16}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-07-31 17:56:52,706 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-07-31 17:56:52,707 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_solution.json
2025-07-31 17:56:52,708 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_20250731_175652.solution
2025-07-31 17:56:52,708 - __main__ - INFO - 实例 simple1_9 处理完成
