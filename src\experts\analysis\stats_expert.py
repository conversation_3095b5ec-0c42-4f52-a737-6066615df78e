# -*- coding: utf-8 -*-
"""
统计分析专家模块

包含StatsExpert类，负责种群的统计学特征分析。
重构版本：使用统一的工具模块，消除代码冗余。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from experts.base.expert_base import ExpertBase
from utils import utils
from utils.analysis_utils import AnalysisUtils, PathUtils, DataValidator
from utils.similarity_utils import SimilarityCalculator, DiversityAnalyzer


class StatsExpert(ExpertBase):
    """统计分析专家，负责种群的统计学特征分析"""
    
    def __init__(self):
        super().__init__()
    
    def analyze(self, populations, distance_matrix=None):
        """分析种群的统计学特征

        参数:
            populations: 种群列表
            distance_matrix: 距离矩阵（可选）

        返回:
            统计分析结果
        """
        self.logger.info("开始统计分析")

        # 数据验证
        if not DataValidator.validate_population_data(populations):
            self.logger.warning("种群数据验证失败")
            return self._get_empty_analysis_result(distance_matrix)

        # 使用统一工具提取数据
        costs = PathUtils.extract_costs_safely(populations)
        tours = PathUtils.extract_tours_safely(populations)

        if not costs:
            self.logger.warning("无法提取有效的成本数据")
            return self._get_empty_analysis_result(distance_matrix)

        # 使用统一工具计算基础统计
        cost_stats = AnalysisUtils.calculate_basic_stats(costs)

        # 计算百分位数
        percentiles = AnalysisUtils.calculate_percentiles(costs, [25, 50, 75, 90, 95])
        cost_stats.update({
            "q25": percentiles[0],
            "median": percentiles[1],
            "q75": percentiles[2],
            "q90": percentiles[3],
            "q95": percentiles[4]
        })

        # 使用统一的多样性计算
        diversity_analysis = DiversityAnalyzer.analyze_population_diversity(tours)
        diversity_level = diversity_analysis.get("hamming_diversity", 0.0)

        # 计算种群规模统计
        population_stats = {
            "size": len(populations),
            "valid_tours": len(tours),
            "avg_tour_length": sum(len(tour) for tour in tours) / len(tours) if tours else 0
        }

        analysis_result = {
            "population_size": len(populations),
            "cost_stats": cost_stats,
            "diversity_level": diversity_level,
            "diversity_analysis": diversity_analysis,
            "population_stats": population_stats,
            "distance_matrix": distance_matrix
        }

        self.logger.info(f"统计分析完成: 种群大小={len(populations)}, "
                        f"最优成本={cost_stats['min']:.3f}, "
                        f"多样性={diversity_level:.3f}")
        return analysis_result
    
    def generate_report(self, analysis_result, coordinates=None, distance_matrix=None, instance_name=None):
        """生成统计分析报告

        参数:
            analysis_result: 分析结果
            coordinates: 坐标信息（可选）
            distance_matrix: 距离矩阵（可选）
            instance_name: 实例名称（可选）

        返回:
            统计分析报告
        """
        if not analysis_result:
            return self._get_empty_report()

        cost_stats = analysis_result.get("cost_stats", {})
        diversity_analysis = analysis_result.get("diversity_analysis", {})
        population_stats = analysis_result.get("population_stats", {})

        # 生成综合报告
        report = {
            "instance_name": instance_name,  # 添加实例名信息
            "summary": {
                "population_size": population_stats.get("size", 0),
                "best_cost": cost_stats.get("min", float('inf')),
                "worst_cost": cost_stats.get("max", 0),
                "avg_cost": cost_stats.get("mean", 0),
                "cost_std": cost_stats.get("std", 0),
                "diversity_score": diversity_analysis.get("hamming_diversity", 0)
            },
            "detailed_stats": {
                "cost_statistics": cost_stats,
                "diversity_metrics": diversity_analysis,
                "population_info": population_stats
            },
            "quality_indicators": {
                "convergence_level": self._calculate_convergence_level(cost_stats),
                "diversity_level": self._classify_diversity_level(diversity_analysis),
                "population_health": self._assess_population_health(analysis_result)
            },
            "coordinates": coordinates,
            "distance_matrix": distance_matrix
        }

        # 计算额外的统计信息
        if cost_stats.get("max", 0) > 0:
            report["cost_range"] = cost_stats["max"] - cost_stats["min"]
            report["cost_variation"] = cost_stats["std"] / cost_stats["mean"] if cost_stats.get("mean", 0) > 0 else 0
        else:
            report["cost_range"] = 0
            report["cost_variation"] = 0

        # 保持向后兼容性
        report["population_size"] = analysis_result["population_size"]
        report["cost_stats"] = analysis_result["cost_stats"]
        report["diversity_level"] = analysis_result["diversity_level"]

        return report
    
    def _get_empty_analysis_result(self, distance_matrix=None):
        """返回空的分析结果"""
        return {
            "population_size": 0,
            "cost_stats": {"min": 0, "max": 0, "mean": 0, "std": 0},
            "diversity_level": 0,
            "diversity_analysis": {"hamming_diversity": 0, "edge_diversity": 0},
            "population_stats": {"size": 0, "valid_tours": 0, "avg_tour_length": 0},
            "distance_matrix": distance_matrix
        }

    def _get_empty_report(self):
        """返回空的报告"""
        return {
            "summary": {
                "population_size": 0,
                "best_cost": float('inf'),
                "worst_cost": 0,
                "avg_cost": 0,
                "cost_std": 0,
                "diversity_score": 0
            },
            "detailed_stats": {},
            "quality_indicators": {
                "convergence_level": "unknown",
                "diversity_level": "unknown",
                "population_health": "poor"
            }
        }

    def _calculate_convergence_level(self, cost_stats):
        """计算收敛水平"""
        if not cost_stats or cost_stats.get("mean", 0) == 0:
            return "unknown"

        cv = cost_stats.get("std", 0) / cost_stats.get("mean", 1)  # 变异系数

        if cv < 0.05:
            return "high"
        elif cv < 0.15:
            return "medium"
        else:
            return "low"

    def _classify_diversity_level(self, diversity_analysis):
        """分类多样性水平"""
        diversity_score = diversity_analysis.get("hamming_diversity", 0)

        if diversity_score > 0.7:
            return "high"
        elif diversity_score > 0.3:
            return "medium"
        else:
            return "low"

    def _assess_population_health(self, analysis_result):
        """评估种群健康状况"""
        cost_stats = analysis_result.get("cost_stats", {})
        diversity_analysis = analysis_result.get("diversity_analysis", {})

        # 综合评估
        convergence_score = 1.0 - min(1.0, cost_stats.get("std", 0) / max(1.0, cost_stats.get("mean", 1)))
        diversity_score = diversity_analysis.get("hamming_diversity", 0)

        health_score = (convergence_score + diversity_score) / 2

        if health_score > 0.7:
            return "excellent"
        elif health_score > 0.5:
            return "good"
        elif health_score > 0.3:
            return "fair"
        else:
            return "poor"
