2025-08-05 09:51:28,672 - __main__ - INFO - simple1_9 开始进化第 1 代
2025-08-05 09:51:28,673 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 09:51:28,675 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:28,677 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=747.000, 多样性=0.904
2025-08-05 09:51:28,678 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:28,679 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.904
2025-08-05 09:51:28,679 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:28,707 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 09:51:28,708 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:28,708 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 09:51:28,708 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 09:51:29,086 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -47.480, 聚类评分: 0.000, 覆盖率: 0.001, 收敛趋势: 0.000, 多样性: 0.813
2025-08-05 09:51:29,086 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 09:51:29,087 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 09:51:29,087 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-05 09:51:29,122 - visualization.landscape_visualizer - INFO - 插值约束: 458 个点被约束到最小值 747.00
2025-08-05 09:51:34,830 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_1_20250805_095134.html
2025-08-05 09:51:34,871 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_1_20250805_095134.html
2025-08-05 09:51:34,871 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-05 09:51:34,871 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 09:51:34,871 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 6.1638秒
2025-08-05 09:51:34,871 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-05 09:51:34,871 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -47.48000000000001, 'local_optima_density': 0.1, 'gradient_variance': 32267.193600000006, 'cluster_count': 0}, 'population_state': {'diversity': 0.8133333333333332, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.001, 'fitness_entropy': 0.8982444017039272, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -47.480)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.001)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.813)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358689.0862026, 'performance_metrics': {}}}
2025-08-05 09:51:34,871 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:34,871 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:34,872 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:34,872 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:34,872 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:51:34,873 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 09:51:34,873 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:51:34,873 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:34,873 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:34,873 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:51:34,873 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:34,873 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:34,874 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:34,874 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:51:34,874 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:51:34,874 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:34,882 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:51:34,882 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:35,001 - ExplorationExpert - INFO - 探索路径生成完成，成本: 789.0, 路径长度: 9, 收集中间解: 0
2025-08-05 09:51:35,001 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [4, 8, 3, 6, 7, 5, 0, 1, 2], 'cur_cost': 789.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:35,001 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 789.00)
2025-08-05 09:51:35,001 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:51:35,001 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:51:35,001 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:35,002 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 09:51:35,002 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:35,002 - ExplorationExpert - INFO - 探索路径生成完成，成本: 942.0, 路径长度: 9, 收集中间解: 0
2025-08-05 09:51:35,002 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [4, 8, 7, 0, 6, 3, 5, 2, 1], 'cur_cost': 942.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:35,003 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 942.00)
2025-08-05 09:51:35,003 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:51:35,003 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:51:35,003 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:35,004 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:51:35,004 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:35,004 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1092.0, 路径长度: 9, 收集中间解: 0
2025-08-05 09:51:35,004 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [3, 4, 6, 7, 0, 2, 8, 5, 1], 'cur_cost': 1092.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:35,004 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1092.00)
2025-08-05 09:51:35,005 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:51:35,005 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:51:35,005 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:35,005 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:51:35,005 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:35,005 - ExplorationExpert - INFO - 探索路径生成完成，成本: 902.0, 路径长度: 9, 收集中间解: 0
2025-08-05 09:51:35,005 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 2, 1, 7, 3, 5, 6, 8, 4], 'cur_cost': 902.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:35,006 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 902.00)
2025-08-05 09:51:35,006 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:51:35,006 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:51:35,006 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:35,006 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:51:35,006 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:35,007 - ExplorationExpert - INFO - 探索路径生成完成，成本: 838.0, 路径长度: 9, 收集中间解: 0
2025-08-05 09:51:35,007 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 3, 8, 5, 7, 6, 4, 2, 1], 'cur_cost': 838.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:35,007 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 838.00)
2025-08-05 09:51:35,007 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 09:51:35,007 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:35,011 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:35,012 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 1162.0
2025-08-05 09:51:37,412 - ExploitationExpert - INFO - res_population_num: 1
2025-08-05 09:51:37,412 - ExploitationExpert - INFO - res_population_costs: [853.0]
2025-08-05 09:51:37,412 - ExploitationExpert - INFO - res_populations: [array([0, 8, 3, 5, 6, 2, 4, 7, 1], dtype=int64)]
2025-08-05 09:51:37,413 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:37,413 - ExploitationExpert - INFO - populations: [{'tour': [4, 8, 3, 6, 7, 5, 0, 1, 2], 'cur_cost': 789.0}, {'tour': [4, 8, 7, 0, 6, 3, 5, 2, 1], 'cur_cost': 942.0}, {'tour': [3, 4, 6, 7, 0, 2, 8, 5, 1], 'cur_cost': 1092.0}, {'tour': [0, 2, 1, 7, 3, 5, 6, 8, 4], 'cur_cost': 902.0}, {'tour': [0, 3, 8, 5, 7, 6, 4, 2, 1], 'cur_cost': 838.0}, {'tour': array([3, 7, 2, 6, 1, 5, 4, 8, 0], dtype=int64), 'cur_cost': 1162.0}, {'tour': array([4, 2, 6, 0, 7, 8, 1, 3, 5], dtype=int64), 'cur_cost': 1056.0}, {'tour': array([1, 6, 2, 3, 5, 7, 4, 0, 8], dtype=int64), 'cur_cost': 1082.0}, {'tour': array([8, 6, 5, 3, 7, 2, 1, 4, 0], dtype=int64), 'cur_cost': 929.0}, {'tour': array([6, 8, 1, 5, 2, 4, 3, 7, 0], dtype=int64), 'cur_cost': 1072.0}]
2025-08-05 09:51:37,415 - ExploitationExpert - INFO - 局部搜索耗时: 2.40秒，最大迭代次数: 10
2025-08-05 09:51:37,415 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-05 09:51:37,415 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([3, 7, 2, 6, 1, 5, 4, 8, 0], dtype=int64), 'cur_cost': 1162.0, 'intermediate_solutions': [{'tour': array([1, 3, 0, 7, 4, 6, 2, 5, 8], dtype=int64), 'cur_cost': 1253.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 1, 3, 0, 4, 6, 2, 5, 8], dtype=int64), 'cur_cost': 1195.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 7, 1, 3, 0, 6, 2, 5, 8], dtype=int64), 'cur_cost': 1161.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 7, 1, 3, 4, 6, 2, 5, 8], dtype=int64), 'cur_cost': 1242.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 4, 7, 1, 3, 6, 2, 5, 8], dtype=int64), 'cur_cost': 1167.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:37,416 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1162.00)
2025-08-05 09:51:37,416 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:51:37,416 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:51:37,416 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:37,417 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:51:37,417 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:37,417 - ExplorationExpert - INFO - 探索路径生成完成，成本: 835.0, 路径长度: 9, 收集中间解: 0
2025-08-05 09:51:37,417 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [2, 3, 8, 4, 7, 5, 6, 0, 1], 'cur_cost': 835.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:37,417 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 835.00)
2025-08-05 09:51:37,417 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 09:51:37,417 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:37,417 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:37,418 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 1049.0
2025-08-05 09:51:39,317 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 09:51:39,317 - ExploitationExpert - INFO - res_population_costs: [853.0, 680.0]
2025-08-05 09:51:39,317 - ExploitationExpert - INFO - res_populations: [array([0, 8, 3, 5, 6, 2, 4, 7, 1], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64)]
2025-08-05 09:51:39,318 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:39,318 - ExploitationExpert - INFO - populations: [{'tour': [4, 8, 3, 6, 7, 5, 0, 1, 2], 'cur_cost': 789.0}, {'tour': [4, 8, 7, 0, 6, 3, 5, 2, 1], 'cur_cost': 942.0}, {'tour': [3, 4, 6, 7, 0, 2, 8, 5, 1], 'cur_cost': 1092.0}, {'tour': [0, 2, 1, 7, 3, 5, 6, 8, 4], 'cur_cost': 902.0}, {'tour': [0, 3, 8, 5, 7, 6, 4, 2, 1], 'cur_cost': 838.0}, {'tour': array([3, 7, 2, 6, 1, 5, 4, 8, 0], dtype=int64), 'cur_cost': 1162.0}, {'tour': [2, 3, 8, 4, 7, 5, 6, 0, 1], 'cur_cost': 835.0}, {'tour': array([0, 8, 6, 2, 4, 1, 5, 7, 3], dtype=int64), 'cur_cost': 1049.0}, {'tour': array([8, 6, 5, 3, 7, 2, 1, 4, 0], dtype=int64), 'cur_cost': 929.0}, {'tour': array([6, 8, 1, 5, 2, 4, 3, 7, 0], dtype=int64), 'cur_cost': 1072.0}]
2025-08-05 09:51:39,319 - ExploitationExpert - INFO - 局部搜索耗时: 1.90秒，最大迭代次数: 10
2025-08-05 09:51:39,319 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-05 09:51:39,320 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([0, 8, 6, 2, 4, 1, 5, 7, 3], dtype=int64), 'cur_cost': 1049.0, 'intermediate_solutions': [{'tour': array([2, 6, 1, 3, 5, 7, 4, 0, 8], dtype=int64), 'cur_cost': 1022.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 2, 6, 1, 5, 7, 4, 0, 8], dtype=int64), 'cur_cost': 1088.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 3, 2, 6, 1, 7, 4, 0, 8], dtype=int64), 'cur_cost': 1059.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 3, 2, 6, 5, 7, 4, 0, 8], dtype=int64), 'cur_cost': 1136.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 5, 3, 2, 6, 7, 4, 0, 8], dtype=int64), 'cur_cost': 1139.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:39,320 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1049.00)
2025-08-05 09:51:39,320 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:51:39,320 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:51:39,320 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:39,321 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:51:39,321 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,321 - ExplorationExpert - INFO - 探索路径生成完成，成本: 854.0, 路径长度: 9, 收集中间解: 0
2025-08-05 09:51:39,321 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [2, 8, 1, 0, 5, 3, 7, 6, 4], 'cur_cost': 854.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:39,321 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 854.00)
2025-08-05 09:51:39,321 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:51:39,322 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:51:39,322 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:39,322 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:51:39,322 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,322 - ExplorationExpert - INFO - 探索路径生成完成，成本: 938.0, 路径长度: 9, 收集中间解: 0
2025-08-05 09:51:39,322 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [2, 3, 1, 6, 5, 7, 0, 4, 8], 'cur_cost': 938.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:39,322 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 938.00)
2025-08-05 09:51:39,323 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:39,323 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:39,324 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [4, 8, 3, 6, 7, 5, 0, 1, 2], 'cur_cost': 789.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [4, 8, 7, 0, 6, 3, 5, 2, 1], 'cur_cost': 942.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [3, 4, 6, 7, 0, 2, 8, 5, 1], 'cur_cost': 1092.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 1, 7, 3, 5, 6, 8, 4], 'cur_cost': 902.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 8, 5, 7, 6, 4, 2, 1], 'cur_cost': 838.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 7, 2, 6, 1, 5, 4, 8, 0], dtype=int64), 'cur_cost': 1162.0, 'intermediate_solutions': [{'tour': array([1, 3, 0, 7, 4, 6, 2, 5, 8], dtype=int64), 'cur_cost': 1253.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 1, 3, 0, 4, 6, 2, 5, 8], dtype=int64), 'cur_cost': 1195.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 7, 1, 3, 0, 6, 2, 5, 8], dtype=int64), 'cur_cost': 1161.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 7, 1, 3, 4, 6, 2, 5, 8], dtype=int64), 'cur_cost': 1242.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 4, 7, 1, 3, 6, 2, 5, 8], dtype=int64), 'cur_cost': 1167.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [2, 3, 8, 4, 7, 5, 6, 0, 1], 'cur_cost': 835.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 8, 6, 2, 4, 1, 5, 7, 3], dtype=int64), 'cur_cost': 1049.0, 'intermediate_solutions': [{'tour': array([2, 6, 1, 3, 5, 7, 4, 0, 8], dtype=int64), 'cur_cost': 1022.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 2, 6, 1, 5, 7, 4, 0, 8], dtype=int64), 'cur_cost': 1088.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 3, 2, 6, 1, 7, 4, 0, 8], dtype=int64), 'cur_cost': 1059.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 3, 2, 6, 5, 7, 4, 0, 8], dtype=int64), 'cur_cost': 1136.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 5, 3, 2, 6, 7, 4, 0, 8], dtype=int64), 'cur_cost': 1139.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [2, 8, 1, 0, 5, 3, 7, 6, 4], 'cur_cost': 854.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [2, 3, 1, 6, 5, 7, 0, 4, 8], 'cur_cost': 938.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 09:51:39,324 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:39,324 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:39,325 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=789.000, 多样性=0.874
2025-08-05 09:51:39,325 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 09:51:39,325 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 09:51:39,325 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:39,326 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.01887708076539858, 'best_improvement': -0.05622489959839357}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.03278688524590168}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.7777777777777778, 'new_diversity': 0.7777777777777778, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 09:51:39,338 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 09:51:39,338 - __main__ - INFO - simple1_9 开始进化第 2 代
2025-08-05 09:51:39,338 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 09:51:39,338 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:39,339 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=789.000, 多样性=0.874
2025-08-05 09:51:39,339 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:39,340 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.874
2025-08-05 09:51:39,340 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:39,341 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.778
2025-08-05 09:51:39,343 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 09:51:39,343 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:39,343 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-05 09:51:39,343 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-05 09:51:39,350 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.167, 适应度梯度: -33.100, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 0.639
2025-08-05 09:51:39,350 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 09:51:39,350 - LandscapeExpert - INFO - 提取到 4 个精英解
2025-08-05 09:51:39,350 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-05 09:51:39,353 - visualization.landscape_visualizer - INFO - 插值约束: 101 个点被约束到最小值 680.00
2025-08-05 09:51:39,430 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_2_20250805_095139.html
2025-08-05 09:51:39,469 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_2_20250805_095139.html
2025-08-05 09:51:39,470 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 2
2025-08-05 09:51:39,470 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 09:51:39,470 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1276秒
2025-08-05 09:51:39,470 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.16666666666666666, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -33.09999999999999, 'local_optima_density': 0.16666666666666666, 'gradient_variance': 24212.91666666666, 'cluster_count': 0}, 'population_state': {'diversity': 0.638888888888889, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0022, 'fitness_entropy': 0.9172097224626077, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -33.100)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358699.350949, 'performance_metrics': {}}}
2025-08-05 09:51:39,470 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:39,471 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:39,471 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:39,471 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:39,471 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 09:51:39,471 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 09:51:39,471 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 09:51:39,472 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:39,472 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:39,472 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 09:51:39,472 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:39,472 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:39,472 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 6} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:39,472 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:51:39,473 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:51:39,473 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:39,473 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:51:39,473 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,473 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,473 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,474 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,474 - ExplorationExpert - INFO - 探索路径生成完成，成本: 964.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:51:39,474 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [1, 7, 2, 5, 3, 8, 4, 0, 6], 'cur_cost': 964.0, 'intermediate_solutions': [{'tour': [8, 4, 3, 6, 7, 5, 0, 1, 2], 'cur_cost': 877.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 5, 7, 6, 3, 8, 0, 1, 2], 'cur_cost': 845.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 3, 6, 7, 5, 0, 1, 8, 2], 'cur_cost': 875.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:39,474 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 964.00)
2025-08-05 09:51:39,474 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:51:39,474 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:51:39,474 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:39,475 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 09:51:39,475 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,475 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,475 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,475 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,475 - ExplorationExpert - INFO - 探索路径生成完成，成本: 839.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:51:39,475 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [2, 0, 1, 4, 8, 7, 3, 5, 6], 'cur_cost': 839.0, 'intermediate_solutions': [{'tour': [8, 4, 7, 0, 6, 3, 5, 2, 1], 'cur_cost': 1019.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 7, 0, 6, 3, 2, 5, 1], 'cur_cost': 1057.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 5, 8, 7, 0, 6, 3, 2, 1], 'cur_cost': 1002.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:39,475 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 839.00)
2025-08-05 09:51:39,476 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-05 09:51:39,476 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:39,476 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:39,476 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 1092.0
2025-08-05 09:51:39,581 - ExploitationExpert - INFO - res_population_num: 3
2025-08-05 09:51:39,581 - ExploitationExpert - INFO - res_population_costs: [680.0, 853.0, 680]
2025-08-05 09:51:39,581 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 8, 3, 5, 6, 2, 4, 7, 1], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64)]
2025-08-05 09:51:39,582 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:39,582 - ExploitationExpert - INFO - populations: [{'tour': [1, 7, 2, 5, 3, 8, 4, 0, 6], 'cur_cost': 964.0}, {'tour': [2, 0, 1, 4, 8, 7, 3, 5, 6], 'cur_cost': 839.0}, {'tour': array([1, 4, 6, 8, 7, 5, 0, 3, 2], dtype=int64), 'cur_cost': 1092.0}, {'tour': [0, 2, 1, 7, 3, 5, 6, 8, 4], 'cur_cost': 902.0}, {'tour': [0, 3, 8, 5, 7, 6, 4, 2, 1], 'cur_cost': 838.0}, {'tour': [3, 7, 2, 6, 1, 5, 4, 8, 0], 'cur_cost': 1162.0}, {'tour': [2, 3, 8, 4, 7, 5, 6, 0, 1], 'cur_cost': 835.0}, {'tour': [0, 8, 6, 2, 4, 1, 5, 7, 3], 'cur_cost': 1049.0}, {'tour': [2, 8, 1, 0, 5, 3, 7, 6, 4], 'cur_cost': 854.0}, {'tour': [2, 3, 1, 6, 5, 7, 0, 4, 8], 'cur_cost': 938.0}]
2025-08-05 09:51:39,583 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒，最大迭代次数: 10
2025-08-05 09:51:39,583 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-05 09:51:39,583 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([1, 4, 6, 8, 7, 5, 0, 3, 2], dtype=int64), 'cur_cost': 1092.0, 'intermediate_solutions': [{'tour': array([6, 4, 3, 7, 0, 2, 8, 5, 1]), 'cur_cost': 1025.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 6, 4, 3, 0, 2, 8, 5, 1]), 'cur_cost': 1092.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 7, 6, 4, 3, 2, 8, 5, 1]), 'cur_cost': 992.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 7, 6, 4, 0, 2, 8, 5, 1]), 'cur_cost': 1032.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 0, 7, 6, 4, 2, 8, 5, 1]), 'cur_cost': 1023.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:39,583 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1092.00)
2025-08-05 09:51:39,583 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:51:39,583 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:51:39,584 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:39,584 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:51:39,584 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,584 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,584 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,585 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,585 - ExplorationExpert - INFO - 探索路径生成完成，成本: 986.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:51:39,585 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [2, 5, 1, 3, 8, 7, 6, 0, 4], 'cur_cost': 986.0, 'intermediate_solutions': [{'tour': [2, 0, 1, 7, 3, 5, 6, 8, 4], 'cur_cost': 777.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 7, 1, 2, 5, 6, 8, 4], 'cur_cost': 1010.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 1, 0, 7, 3, 5, 6, 8, 4], 'cur_cost': 747.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:39,585 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 986.00)
2025-08-05 09:51:39,585 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:51:39,585 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:51:39,586 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:39,586 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 09:51:39,586 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,586 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,586 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,587 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,587 - ExplorationExpert - INFO - 探索路径生成完成，成本: 838.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:51:39,587 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [5, 7, 6, 3, 4, 0, 1, 2, 8], 'cur_cost': 838.0, 'intermediate_solutions': [{'tour': [0, 3, 4, 5, 7, 6, 8, 2, 1], 'cur_cost': 960.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 4, 6, 7, 5, 8, 3, 0, 1], 'cur_cost': 838.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 8, 5, 7, 0, 6, 4, 2, 1], 'cur_cost': 936.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:39,587 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 838.00)
2025-08-05 09:51:39,587 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 09:51:39,587 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:39,587 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:39,587 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 1058.0
2025-08-05 09:51:39,593 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:39,594 - ExploitationExpert - INFO - res_population_costs: [680.0, 853.0, 680, 680]
2025-08-05 09:51:39,594 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 8, 3, 5, 6, 2, 4, 7, 1], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-05 09:51:39,595 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:39,595 - ExploitationExpert - INFO - populations: [{'tour': [1, 7, 2, 5, 3, 8, 4, 0, 6], 'cur_cost': 964.0}, {'tour': [2, 0, 1, 4, 8, 7, 3, 5, 6], 'cur_cost': 839.0}, {'tour': array([1, 4, 6, 8, 7, 5, 0, 3, 2], dtype=int64), 'cur_cost': 1092.0}, {'tour': [2, 5, 1, 3, 8, 7, 6, 0, 4], 'cur_cost': 986.0}, {'tour': [5, 7, 6, 3, 4, 0, 1, 2, 8], 'cur_cost': 838.0}, {'tour': array([4, 7, 3, 6, 0, 8, 1, 5, 2], dtype=int64), 'cur_cost': 1058.0}, {'tour': [2, 3, 8, 4, 7, 5, 6, 0, 1], 'cur_cost': 835.0}, {'tour': [0, 8, 6, 2, 4, 1, 5, 7, 3], 'cur_cost': 1049.0}, {'tour': [2, 8, 1, 0, 5, 3, 7, 6, 4], 'cur_cost': 854.0}, {'tour': [2, 3, 1, 6, 5, 7, 0, 4, 8], 'cur_cost': 938.0}]
2025-08-05 09:51:39,595 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:39,595 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-05 09:51:39,596 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([4, 7, 3, 6, 0, 8, 1, 5, 2], dtype=int64), 'cur_cost': 1058.0, 'intermediate_solutions': [{'tour': array([2, 7, 3, 6, 1, 5, 4, 8, 0]), 'cur_cost': 1076.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 2, 7, 3, 1, 5, 4, 8, 0]), 'cur_cost': 1181.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 6, 2, 7, 3, 5, 4, 8, 0]), 'cur_cost': 977.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 6, 2, 7, 1, 5, 4, 8, 0]), 'cur_cost': 1208.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 1, 6, 2, 7, 5, 4, 8, 0]), 'cur_cost': 1172.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:39,596 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1058.00)
2025-08-05 09:51:39,596 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:51:39,596 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:51:39,596 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:39,597 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-05 09:51:39,597 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,597 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,597 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,597 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,597 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1090.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:51:39,597 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [5, 0, 2, 3, 1, 4, 7, 6, 8], 'cur_cost': 1090.0, 'intermediate_solutions': [{'tour': [2, 3, 8, 4, 5, 7, 6, 0, 1], 'cur_cost': 894.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 3, 8, 4, 7, 6, 5, 0, 1], 'cur_cost': 876.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 3, 6, 8, 4, 7, 5, 0, 1], 'cur_cost': 958.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:39,598 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 1090.00)
2025-08-05 09:51:39,598 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:51:39,598 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:51:39,598 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:39,598 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-05 09:51:39,598 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,598 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,599 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,599 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,599 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1117.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:51:39,599 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [5, 0, 6, 2, 1, 3, 4, 7, 8], 'cur_cost': 1117.0, 'intermediate_solutions': [{'tour': [6, 8, 0, 2, 4, 1, 5, 7, 3], 'cur_cost': 963.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 0, 6, 2, 4, 1, 5, 7, 3], 'cur_cost': 960.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 8, 2, 4, 1, 5, 7, 3], 'cur_cost': 893.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:39,599 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1117.00)
2025-08-05 09:51:39,599 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:51:39,599 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:51:39,600 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:39,600 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 09:51:39,600 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,600 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,600 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,600 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,601 - ExplorationExpert - INFO - 探索路径生成完成，成本: 869.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:51:39,601 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [1, 0, 4, 8, 7, 5, 3, 6, 2], 'cur_cost': 869.0, 'intermediate_solutions': [{'tour': [8, 2, 1, 0, 5, 3, 7, 6, 4], 'cur_cost': 856.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 1, 0, 6, 7, 3, 5, 4], 'cur_cost': 816.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 1, 0, 5, 3, 2, 7, 6, 4], 'cur_cost': 1013.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:39,601 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 869.00)
2025-08-05 09:51:39,601 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:51:39,601 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:51:39,601 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:39,601 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:51:39,602 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,602 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,602 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,602 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,602 - ExplorationExpert - INFO - 探索路径生成完成，成本: 814.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:51:39,602 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 3, 8, 7, 5, 6, 4, 2, 1], 'cur_cost': 814.0, 'intermediate_solutions': [{'tour': [2, 3, 1, 6, 8, 7, 0, 4, 5], 'cur_cost': 1143.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 6, 1, 3, 2, 7, 0, 4, 8], 'cur_cost': 996.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 3, 1, 6, 5, 7, 4, 8], 'cur_cost': 1027.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:39,602 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 814.00)
2025-08-05 09:51:39,603 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:39,603 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:39,603 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 2, 5, 3, 8, 4, 0, 6], 'cur_cost': 964.0, 'intermediate_solutions': [{'tour': [8, 4, 3, 6, 7, 5, 0, 1, 2], 'cur_cost': 877.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 5, 7, 6, 3, 8, 0, 1, 2], 'cur_cost': 845.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 3, 6, 7, 5, 0, 1, 8, 2], 'cur_cost': 875.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [2, 0, 1, 4, 8, 7, 3, 5, 6], 'cur_cost': 839.0, 'intermediate_solutions': [{'tour': [8, 4, 7, 0, 6, 3, 5, 2, 1], 'cur_cost': 1019.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 7, 0, 6, 3, 2, 5, 1], 'cur_cost': 1057.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 5, 8, 7, 0, 6, 3, 2, 1], 'cur_cost': 1002.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([1, 4, 6, 8, 7, 5, 0, 3, 2], dtype=int64), 'cur_cost': 1092.0, 'intermediate_solutions': [{'tour': array([6, 4, 3, 7, 0, 2, 8, 5, 1]), 'cur_cost': 1025.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 6, 4, 3, 0, 2, 8, 5, 1]), 'cur_cost': 1092.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 7, 6, 4, 3, 2, 8, 5, 1]), 'cur_cost': 992.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 7, 6, 4, 0, 2, 8, 5, 1]), 'cur_cost': 1032.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 0, 7, 6, 4, 2, 8, 5, 1]), 'cur_cost': 1023.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [2, 5, 1, 3, 8, 7, 6, 0, 4], 'cur_cost': 986.0, 'intermediate_solutions': [{'tour': [2, 0, 1, 7, 3, 5, 6, 8, 4], 'cur_cost': 777.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 7, 1, 2, 5, 6, 8, 4], 'cur_cost': 1010.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 1, 0, 7, 3, 5, 6, 8, 4], 'cur_cost': 747.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [5, 7, 6, 3, 4, 0, 1, 2, 8], 'cur_cost': 838.0, 'intermediate_solutions': [{'tour': [0, 3, 4, 5, 7, 6, 8, 2, 1], 'cur_cost': 960.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 4, 6, 7, 5, 8, 3, 0, 1], 'cur_cost': 838.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 8, 5, 7, 0, 6, 4, 2, 1], 'cur_cost': 936.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 7, 3, 6, 0, 8, 1, 5, 2], dtype=int64), 'cur_cost': 1058.0, 'intermediate_solutions': [{'tour': array([2, 7, 3, 6, 1, 5, 4, 8, 0]), 'cur_cost': 1076.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 2, 7, 3, 1, 5, 4, 8, 0]), 'cur_cost': 1181.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 6, 2, 7, 3, 5, 4, 8, 0]), 'cur_cost': 977.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 6, 2, 7, 1, 5, 4, 8, 0]), 'cur_cost': 1208.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 1, 6, 2, 7, 5, 4, 8, 0]), 'cur_cost': 1172.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [5, 0, 2, 3, 1, 4, 7, 6, 8], 'cur_cost': 1090.0, 'intermediate_solutions': [{'tour': [2, 3, 8, 4, 5, 7, 6, 0, 1], 'cur_cost': 894.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 3, 8, 4, 7, 6, 5, 0, 1], 'cur_cost': 876.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 3, 6, 8, 4, 7, 5, 0, 1], 'cur_cost': 958.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [5, 0, 6, 2, 1, 3, 4, 7, 8], 'cur_cost': 1117.0, 'intermediate_solutions': [{'tour': [6, 8, 0, 2, 4, 1, 5, 7, 3], 'cur_cost': 963.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 0, 6, 2, 4, 1, 5, 7, 3], 'cur_cost': 960.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 8, 2, 4, 1, 5, 7, 3], 'cur_cost': 893.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [1, 0, 4, 8, 7, 5, 3, 6, 2], 'cur_cost': 869.0, 'intermediate_solutions': [{'tour': [8, 2, 1, 0, 5, 3, 7, 6, 4], 'cur_cost': 856.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 1, 0, 6, 7, 3, 5, 4], 'cur_cost': 816.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 1, 0, 5, 3, 2, 7, 6, 4], 'cur_cost': 1013.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 8, 7, 5, 6, 4, 2, 1], 'cur_cost': 814.0, 'intermediate_solutions': [{'tour': [2, 3, 1, 6, 8, 7, 0, 4, 5], 'cur_cost': 1143.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 6, 1, 3, 2, 7, 0, 4, 8], 'cur_cost': 996.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 3, 1, 6, 5, 7, 4, 8], 'cur_cost': 1027.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:51:39,604 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:39,604 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:39,605 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=814.000, 多样性=0.884
2025-08-05 09:51:39,605 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 09:51:39,605 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 09:51:39,605 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:39,605 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.007084735471716982, 'best_improvement': -0.031685678073510776}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.011299435028248433}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.7592592592592592, 'new_diversity': 0.7592592592592592, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 09:51:39,606 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 09:51:39,606 - __main__ - INFO - simple1_9 开始进化第 3 代
2025-08-05 09:51:39,606 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 09:51:39,606 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:39,606 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=814.000, 多样性=0.884
2025-08-05 09:51:39,607 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:39,607 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.884
2025-08-05 09:51:39,607 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:39,608 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.759
2025-08-05 09:51:39,609 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 09:51:39,610 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:39,610 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 09:51:39,610 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 09:51:39,618 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.357, 适应度梯度: 8.386, 聚类评分: 0.000, 覆盖率: 0.003, 收敛趋势: 0.000, 多样性: 0.567
2025-08-05 09:51:39,618 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 09:51:39,619 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:51:39,619 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-05 09:51:39,623 - visualization.landscape_visualizer - INFO - 插值约束: 123 个点被约束到最小值 680.00
2025-08-05 09:51:39,702 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_3_20250805_095139.html
2025-08-05 09:51:39,744 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_3_20250805_095139.html
2025-08-05 09:51:39,744 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 3
2025-08-05 09:51:39,744 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 09:51:39,744 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1351秒
2025-08-05 09:51:39,745 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.35714285714285715, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 8.385714285714286, 'local_optima_density': 0.35714285714285715, 'gradient_variance': 26373.311224489797, 'cluster_count': 0}, 'population_state': {'diversity': 0.5667189952904239, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0034, 'fitness_entropy': 0.9106994941661897, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.003)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 8.386)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358699.6185884, 'performance_metrics': {}}}
2025-08-05 09:51:39,745 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:39,745 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:39,745 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:39,745 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:39,746 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore']
2025-08-05 09:51:39,746 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 09:51:39,746 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore']
2025-08-05 09:51:39,746 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:39,747 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:39,747 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore']
2025-08-05 09:51:39,747 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:39,747 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:39,747 - experts.management.collaboration_manager - INFO - 识别精英个体: {9, 4} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:39,747 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:51:39,748 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:51:39,748 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:39,748 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 09:51:39,748 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,749 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,749 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,749 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,749 - ExplorationExpert - INFO - 探索路径生成完成，成本: 906.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:51:39,749 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [7, 3, 8, 2, 0, 4, 5, 6, 1], 'cur_cost': 906.0, 'intermediate_solutions': [{'tour': [1, 7, 2, 3, 5, 8, 4, 0, 6], 'cur_cost': 961.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 2, 7, 1, 8, 4, 0, 6], 'cur_cost': 1043.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 2, 5, 6, 3, 8, 4, 0], 'cur_cost': 899.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:39,749 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 906.00)
2025-08-05 09:51:39,749 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:51:39,749 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:51:39,750 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:39,750 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-05 09:51:39,750 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,750 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,751 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,751 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,751 - ExplorationExpert - INFO - 探索路径生成完成，成本: 999.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:51:39,751 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [4, 5, 3, 6, 0, 8, 7, 2, 1], 'cur_cost': 999.0, 'intermediate_solutions': [{'tour': [2, 0, 1, 4, 8, 5, 3, 7, 6], 'cur_cost': 863.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 0, 6, 5, 3, 7, 8, 4, 1], 'cur_cost': 842.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 0, 1, 8, 7, 3, 5, 6, 4], 'cur_cost': 828.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:39,751 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 999.00)
2025-08-05 09:51:39,751 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-05 09:51:39,751 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:39,751 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:39,751 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 1021.0
2025-08-05 09:51:39,756 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:39,757 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680, 853.0]
2025-08-05 09:51:39,757 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 8, 3, 5, 6, 2, 4, 7, 1], dtype=int64)]
2025-08-05 09:51:39,758 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:39,758 - ExploitationExpert - INFO - populations: [{'tour': [7, 3, 8, 2, 0, 4, 5, 6, 1], 'cur_cost': 906.0}, {'tour': [4, 5, 3, 6, 0, 8, 7, 2, 1], 'cur_cost': 999.0}, {'tour': array([0, 1, 3, 8, 2, 6, 4, 5, 7], dtype=int64), 'cur_cost': 1021.0}, {'tour': [2, 5, 1, 3, 8, 7, 6, 0, 4], 'cur_cost': 986.0}, {'tour': [5, 7, 6, 3, 4, 0, 1, 2, 8], 'cur_cost': 838.0}, {'tour': [4, 7, 3, 6, 0, 8, 1, 5, 2], 'cur_cost': 1058.0}, {'tour': [5, 0, 2, 3, 1, 4, 7, 6, 8], 'cur_cost': 1090.0}, {'tour': [5, 0, 6, 2, 1, 3, 4, 7, 8], 'cur_cost': 1117.0}, {'tour': [1, 0, 4, 8, 7, 5, 3, 6, 2], 'cur_cost': 869.0}, {'tour': [0, 3, 8, 7, 5, 6, 4, 2, 1], 'cur_cost': 814.0}]
2025-08-05 09:51:39,758 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:39,758 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-05 09:51:39,759 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([0, 1, 3, 8, 2, 6, 4, 5, 7], dtype=int64), 'cur_cost': 1021.0, 'intermediate_solutions': [{'tour': array([6, 4, 1, 8, 7, 5, 0, 3, 2]), 'cur_cost': 1179.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 6, 4, 1, 7, 5, 0, 3, 2]), 'cur_cost': 1093.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 8, 6, 4, 1, 5, 0, 3, 2]), 'cur_cost': 1179.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 8, 6, 4, 7, 5, 0, 3, 2]), 'cur_cost': 1169.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 7, 8, 6, 4, 5, 0, 3, 2]), 'cur_cost': 1181.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:39,759 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1021.00)
2025-08-05 09:51:39,759 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:51:39,759 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:51:39,759 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:39,760 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:51:39,760 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,760 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,760 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,760 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,760 - ExplorationExpert - INFO - 探索路径生成完成，成本: 996.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:51:39,761 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 3, 2, 7, 1, 4, 8, 5, 6], 'cur_cost': 996.0, 'intermediate_solutions': [{'tour': [2, 3, 1, 5, 8, 7, 6, 0, 4], 'cur_cost': 983.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 1, 5, 2, 8, 7, 6, 0, 4], 'cur_cost': 1074.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 5, 3, 1, 8, 7, 6, 0, 4], 'cur_cost': 980.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:39,761 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 996.00)
2025-08-05 09:51:39,761 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:51:39,761 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:51:39,761 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:39,762 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 09:51:39,762 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,762 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,762 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,762 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,762 - ExplorationExpert - INFO - 探索路径生成完成，成本: 929.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:51:39,762 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [5, 6, 0, 7, 3, 4, 2, 8, 1], 'cur_cost': 929.0, 'intermediate_solutions': [{'tour': [5, 7, 8, 3, 4, 0, 1, 2, 6], 'cur_cost': 881.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 7, 6, 3, 4, 0, 2, 1, 8], 'cur_cost': 998.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 6, 4, 3, 0, 1, 2, 8], 'cur_cost': 926.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:39,763 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 929.00)
2025-08-05 09:51:39,763 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:51:39,763 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:51:39,763 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:39,763 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:51:39,763 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,763 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,763 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,763 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,764 - ExplorationExpert - INFO - 探索路径生成完成，成本: 870.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:51:39,764 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [6, 7, 8, 2, 5, 3, 4, 0, 1], 'cur_cost': 870.0, 'intermediate_solutions': [{'tour': [4, 7, 3, 6, 0, 8, 2, 5, 1], 'cur_cost': 1034.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 7, 5, 1, 8, 0, 6, 3, 2], 'cur_cost': 1045.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 3, 6, 0, 8, 1, 5, 4, 2], 'cur_cost': 1063.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:39,764 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 870.00)
2025-08-05 09:51:39,764 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:51:39,764 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:39,765 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:39,765 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1052.0
2025-08-05 09:51:39,770 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:39,771 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680, 853.0]
2025-08-05 09:51:39,771 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 8, 3, 5, 6, 2, 4, 7, 1], dtype=int64)]
2025-08-05 09:51:39,772 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:39,772 - ExploitationExpert - INFO - populations: [{'tour': [7, 3, 8, 2, 0, 4, 5, 6, 1], 'cur_cost': 906.0}, {'tour': [4, 5, 3, 6, 0, 8, 7, 2, 1], 'cur_cost': 999.0}, {'tour': array([0, 1, 3, 8, 2, 6, 4, 5, 7], dtype=int64), 'cur_cost': 1021.0}, {'tour': [0, 3, 2, 7, 1, 4, 8, 5, 6], 'cur_cost': 996.0}, {'tour': [5, 6, 0, 7, 3, 4, 2, 8, 1], 'cur_cost': 929.0}, {'tour': [6, 7, 8, 2, 5, 3, 4, 0, 1], 'cur_cost': 870.0}, {'tour': array([1, 0, 7, 6, 8, 4, 3, 2, 5], dtype=int64), 'cur_cost': 1052.0}, {'tour': [5, 0, 6, 2, 1, 3, 4, 7, 8], 'cur_cost': 1117.0}, {'tour': [1, 0, 4, 8, 7, 5, 3, 6, 2], 'cur_cost': 869.0}, {'tour': [0, 3, 8, 7, 5, 6, 4, 2, 1], 'cur_cost': 814.0}]
2025-08-05 09:51:39,772 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:39,773 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-05 09:51:39,773 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([1, 0, 7, 6, 8, 4, 3, 2, 5], dtype=int64), 'cur_cost': 1052.0, 'intermediate_solutions': [{'tour': array([2, 0, 5, 3, 1, 4, 7, 6, 8]), 'cur_cost': 1004.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 2, 0, 5, 1, 4, 7, 6, 8]), 'cur_cost': 1070.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 3, 2, 0, 5, 4, 7, 6, 8]), 'cur_cost': 1202.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 3, 2, 0, 1, 4, 7, 6, 8]), 'cur_cost': 902.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 1, 3, 2, 0, 4, 7, 6, 8]), 'cur_cost': 1106.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:39,773 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1052.00)
2025-08-05 09:51:39,773 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 09:51:39,774 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:39,774 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:39,774 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 963.0
2025-08-05 09:51:39,780 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:39,781 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680, 853.0]
2025-08-05 09:51:39,781 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 8, 3, 5, 6, 2, 4, 7, 1], dtype=int64)]
2025-08-05 09:51:39,782 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:39,782 - ExploitationExpert - INFO - populations: [{'tour': [7, 3, 8, 2, 0, 4, 5, 6, 1], 'cur_cost': 906.0}, {'tour': [4, 5, 3, 6, 0, 8, 7, 2, 1], 'cur_cost': 999.0}, {'tour': array([0, 1, 3, 8, 2, 6, 4, 5, 7], dtype=int64), 'cur_cost': 1021.0}, {'tour': [0, 3, 2, 7, 1, 4, 8, 5, 6], 'cur_cost': 996.0}, {'tour': [5, 6, 0, 7, 3, 4, 2, 8, 1], 'cur_cost': 929.0}, {'tour': [6, 7, 8, 2, 5, 3, 4, 0, 1], 'cur_cost': 870.0}, {'tour': array([1, 0, 7, 6, 8, 4, 3, 2, 5], dtype=int64), 'cur_cost': 1052.0}, {'tour': array([6, 7, 8, 4, 1, 2, 5, 3, 0], dtype=int64), 'cur_cost': 963.0}, {'tour': [1, 0, 4, 8, 7, 5, 3, 6, 2], 'cur_cost': 869.0}, {'tour': [0, 3, 8, 7, 5, 6, 4, 2, 1], 'cur_cost': 814.0}]
2025-08-05 09:51:39,783 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:39,783 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-05 09:51:39,783 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([6, 7, 8, 4, 1, 2, 5, 3, 0], dtype=int64), 'cur_cost': 963.0, 'intermediate_solutions': [{'tour': array([6, 0, 5, 2, 1, 3, 4, 7, 8]), 'cur_cost': 1142.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 6, 0, 5, 1, 3, 4, 7, 8]), 'cur_cost': 1146.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 2, 6, 0, 5, 3, 4, 7, 8]), 'cur_cost': 1091.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 2, 6, 0, 1, 3, 4, 7, 8]), 'cur_cost': 1067.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 1, 2, 6, 0, 3, 4, 7, 8]), 'cur_cost': 1114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:39,783 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 963.00)
2025-08-05 09:51:39,783 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:51:39,784 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:51:39,784 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:39,784 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 09:51:39,784 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,784 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,785 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,785 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,785 - ExplorationExpert - INFO - 探索路径生成完成，成本: 853.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:51:39,785 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 1, 4, 8, 3, 5, 7, 6, 2], 'cur_cost': 853.0, 'intermediate_solutions': [{'tour': [7, 0, 4, 8, 1, 5, 3, 6, 2], 'cur_cost': 1087.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 3, 5, 7, 8, 4, 0, 2], 'cur_cost': 883.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 2, 0, 4, 8, 7, 5, 3, 6], 'cur_cost': 883.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:39,785 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 853.00)
2025-08-05 09:51:39,785 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:51:39,785 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:51:39,785 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:39,786 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:51:39,786 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,786 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,786 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,786 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,786 - ExplorationExpert - INFO - 探索路径生成完成，成本: 917.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:51:39,786 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 5, 6, 8, 4, 7, 3, 2, 1], 'cur_cost': 917.0, 'intermediate_solutions': [{'tour': [0, 3, 8, 7, 5, 6, 4, 1, 2], 'cur_cost': 950.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 8, 2, 4, 6, 5, 7, 1], 'cur_cost': 815.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 8, 7, 5, 6, 4, 2, 1], 'cur_cost': 814.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:39,786 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 917.00)
2025-08-05 09:51:39,786 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:39,787 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:39,788 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [7, 3, 8, 2, 0, 4, 5, 6, 1], 'cur_cost': 906.0, 'intermediate_solutions': [{'tour': [1, 7, 2, 3, 5, 8, 4, 0, 6], 'cur_cost': 961.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 2, 7, 1, 8, 4, 0, 6], 'cur_cost': 1043.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 2, 5, 6, 3, 8, 4, 0], 'cur_cost': 899.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [4, 5, 3, 6, 0, 8, 7, 2, 1], 'cur_cost': 999.0, 'intermediate_solutions': [{'tour': [2, 0, 1, 4, 8, 5, 3, 7, 6], 'cur_cost': 863.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 0, 6, 5, 3, 7, 8, 4, 1], 'cur_cost': 842.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 0, 1, 8, 7, 3, 5, 6, 4], 'cur_cost': 828.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 1, 3, 8, 2, 6, 4, 5, 7], dtype=int64), 'cur_cost': 1021.0, 'intermediate_solutions': [{'tour': array([6, 4, 1, 8, 7, 5, 0, 3, 2]), 'cur_cost': 1179.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 6, 4, 1, 7, 5, 0, 3, 2]), 'cur_cost': 1093.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 8, 6, 4, 1, 5, 0, 3, 2]), 'cur_cost': 1179.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 8, 6, 4, 7, 5, 0, 3, 2]), 'cur_cost': 1169.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 7, 8, 6, 4, 5, 0, 3, 2]), 'cur_cost': 1181.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 2, 7, 1, 4, 8, 5, 6], 'cur_cost': 996.0, 'intermediate_solutions': [{'tour': [2, 3, 1, 5, 8, 7, 6, 0, 4], 'cur_cost': 983.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 1, 5, 2, 8, 7, 6, 0, 4], 'cur_cost': 1074.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 5, 3, 1, 8, 7, 6, 0, 4], 'cur_cost': 980.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [5, 6, 0, 7, 3, 4, 2, 8, 1], 'cur_cost': 929.0, 'intermediate_solutions': [{'tour': [5, 7, 8, 3, 4, 0, 1, 2, 6], 'cur_cost': 881.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 7, 6, 3, 4, 0, 2, 1, 8], 'cur_cost': 998.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 6, 4, 3, 0, 1, 2, 8], 'cur_cost': 926.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [6, 7, 8, 2, 5, 3, 4, 0, 1], 'cur_cost': 870.0, 'intermediate_solutions': [{'tour': [4, 7, 3, 6, 0, 8, 2, 5, 1], 'cur_cost': 1034.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 7, 5, 1, 8, 0, 6, 3, 2], 'cur_cost': 1045.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 3, 6, 0, 8, 1, 5, 4, 2], 'cur_cost': 1063.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([1, 0, 7, 6, 8, 4, 3, 2, 5], dtype=int64), 'cur_cost': 1052.0, 'intermediate_solutions': [{'tour': array([2, 0, 5, 3, 1, 4, 7, 6, 8]), 'cur_cost': 1004.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 2, 0, 5, 1, 4, 7, 6, 8]), 'cur_cost': 1070.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 3, 2, 0, 5, 4, 7, 6, 8]), 'cur_cost': 1202.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 3, 2, 0, 1, 4, 7, 6, 8]), 'cur_cost': 902.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 1, 3, 2, 0, 4, 7, 6, 8]), 'cur_cost': 1106.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 7, 8, 4, 1, 2, 5, 3, 0], dtype=int64), 'cur_cost': 963.0, 'intermediate_solutions': [{'tour': array([6, 0, 5, 2, 1, 3, 4, 7, 8]), 'cur_cost': 1142.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 6, 0, 5, 1, 3, 4, 7, 8]), 'cur_cost': 1146.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 2, 6, 0, 5, 3, 4, 7, 8]), 'cur_cost': 1091.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 2, 6, 0, 1, 3, 4, 7, 8]), 'cur_cost': 1067.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 1, 2, 6, 0, 3, 4, 7, 8]), 'cur_cost': 1114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 4, 8, 3, 5, 7, 6, 2], 'cur_cost': 853.0, 'intermediate_solutions': [{'tour': [7, 0, 4, 8, 1, 5, 3, 6, 2], 'cur_cost': 1087.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 3, 5, 7, 8, 4, 0, 2], 'cur_cost': 883.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 2, 0, 4, 8, 7, 5, 3, 6], 'cur_cost': 883.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 6, 8, 4, 7, 3, 2, 1], 'cur_cost': 917.0, 'intermediate_solutions': [{'tour': [0, 3, 8, 7, 5, 6, 4, 1, 2], 'cur_cost': 950.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 8, 2, 4, 6, 5, 7, 1], 'cur_cost': 815.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 8, 7, 5, 6, 4, 2, 1], 'cur_cost': 814.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:51:39,788 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:39,788 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:39,789 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=853.000, 多样性=0.879
2025-08-05 09:51:39,789 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 09:51:39,789 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 09:51:39,789 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:39,789 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 80, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'slight_improvement', 'improvement_rate': 0.008978211602456624, 'best_improvement': -0.04791154791154791}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.005586592178771126}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.7592592592592592, 'new_diversity': 0.7592592592592592, 'diversity_change': 0.0}, 'suggestions': ['改进缓慢，建议调整策略平衡或增加扰动强度', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:51:39,790 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 09:51:39,790 - __main__ - INFO - simple1_9 开始进化第 4 代
2025-08-05 09:51:39,790 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 09:51:39,790 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:39,790 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=853.000, 多样性=0.879
2025-08-05 09:51:39,791 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:39,791 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.879
2025-08-05 09:51:39,791 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:39,792 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.759
2025-08-05 09:51:39,793 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 09:51:39,794 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:39,794 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 09:51:39,794 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 09:51:39,802 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.214, 适应度梯度: -14.614, 聚类评分: 0.000, 覆盖率: 0.004, 收敛趋势: 0.000, 多样性: 0.547
2025-08-05 09:51:39,802 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 09:51:39,802 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:51:39,802 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-05 09:51:39,807 - visualization.landscape_visualizer - INFO - 插值约束: 88 个点被约束到最小值 680.00
2025-08-05 09:51:39,889 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_4_20250805_095139.html
2025-08-05 09:51:39,929 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_4_20250805_095139.html
2025-08-05 09:51:39,929 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 4
2025-08-05 09:51:39,929 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 09:51:39,929 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1356秒
2025-08-05 09:51:39,929 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.21428571428571427, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -14.614285714285714, 'local_optima_density': 0.21428571428571427, 'gradient_variance': 12241.556938775511, 'cluster_count': 0}, 'population_state': {'diversity': 0.5470957613814756, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0044, 'fitness_entropy': 0.9615862351816215, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -14.614)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.004)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358699.8027923, 'performance_metrics': {}}}
2025-08-05 09:51:39,930 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:39,930 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:39,930 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:39,930 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:39,930 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:51:39,930 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 09:51:39,931 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:51:39,931 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:39,931 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:39,931 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:51:39,931 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:39,931 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:39,932 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 5} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:39,932 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:51:39,932 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:51:39,932 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:39,932 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:51:39,932 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,933 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,933 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,933 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,933 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1020.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:51:39,933 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 5, 1, 8, 3, 7, 6, 4, 2], 'cur_cost': 1020.0, 'intermediate_solutions': [{'tour': [7, 3, 8, 2, 0, 4, 6, 5, 1], 'cur_cost': 960.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 8, 0, 2, 4, 5, 6, 1], 'cur_cost': 913.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 8, 2, 0, 3, 4, 5, 6, 1], 'cur_cost': 1014.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:39,933 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1020.00)
2025-08-05 09:51:39,933 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 09:51:39,933 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:39,933 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:39,934 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 1019.0
2025-08-05 09:51:39,938 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:39,938 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680, 853.0]
2025-08-05 09:51:39,938 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 8, 3, 5, 6, 2, 4, 7, 1], dtype=int64)]
2025-08-05 09:51:39,939 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:39,940 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 1, 8, 3, 7, 6, 4, 2], 'cur_cost': 1020.0}, {'tour': array([8, 6, 1, 4, 7, 3, 5, 2, 0], dtype=int64), 'cur_cost': 1019.0}, {'tour': [0, 1, 3, 8, 2, 6, 4, 5, 7], 'cur_cost': 1021.0}, {'tour': [0, 3, 2, 7, 1, 4, 8, 5, 6], 'cur_cost': 996.0}, {'tour': [5, 6, 0, 7, 3, 4, 2, 8, 1], 'cur_cost': 929.0}, {'tour': [6, 7, 8, 2, 5, 3, 4, 0, 1], 'cur_cost': 870.0}, {'tour': [1, 0, 7, 6, 8, 4, 3, 2, 5], 'cur_cost': 1052.0}, {'tour': [6, 7, 8, 4, 1, 2, 5, 3, 0], 'cur_cost': 963.0}, {'tour': [0, 1, 4, 8, 3, 5, 7, 6, 2], 'cur_cost': 853.0}, {'tour': [0, 5, 6, 8, 4, 7, 3, 2, 1], 'cur_cost': 917.0}]
2025-08-05 09:51:39,940 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:39,940 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-08-05 09:51:39,941 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([8, 6, 1, 4, 7, 3, 5, 2, 0], dtype=int64), 'cur_cost': 1019.0, 'intermediate_solutions': [{'tour': array([3, 5, 4, 6, 0, 8, 7, 2, 1]), 'cur_cost': 1106.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 3, 5, 4, 0, 8, 7, 2, 1]), 'cur_cost': 999.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 6, 3, 5, 4, 8, 7, 2, 1]), 'cur_cost': 893.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 6, 3, 5, 0, 8, 7, 2, 1]), 'cur_cost': 1037.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 0, 6, 3, 5, 8, 7, 2, 1]), 'cur_cost': 904.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:39,941 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1019.00)
2025-08-05 09:51:39,941 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-05 09:51:39,941 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:39,941 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:39,941 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 1011.0
2025-08-05 09:51:39,947 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:39,947 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680, 853.0]
2025-08-05 09:51:39,947 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 8, 3, 5, 6, 2, 4, 7, 1], dtype=int64)]
2025-08-05 09:51:39,948 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:39,948 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 1, 8, 3, 7, 6, 4, 2], 'cur_cost': 1020.0}, {'tour': array([8, 6, 1, 4, 7, 3, 5, 2, 0], dtype=int64), 'cur_cost': 1019.0}, {'tour': array([5, 2, 7, 3, 1, 0, 6, 4, 8], dtype=int64), 'cur_cost': 1011.0}, {'tour': [0, 3, 2, 7, 1, 4, 8, 5, 6], 'cur_cost': 996.0}, {'tour': [5, 6, 0, 7, 3, 4, 2, 8, 1], 'cur_cost': 929.0}, {'tour': [6, 7, 8, 2, 5, 3, 4, 0, 1], 'cur_cost': 870.0}, {'tour': [1, 0, 7, 6, 8, 4, 3, 2, 5], 'cur_cost': 1052.0}, {'tour': [6, 7, 8, 4, 1, 2, 5, 3, 0], 'cur_cost': 963.0}, {'tour': [0, 1, 4, 8, 3, 5, 7, 6, 2], 'cur_cost': 853.0}, {'tour': [0, 5, 6, 8, 4, 7, 3, 2, 1], 'cur_cost': 917.0}]
2025-08-05 09:51:39,949 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:39,949 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-08-05 09:51:39,950 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([5, 2, 7, 3, 1, 0, 6, 4, 8], dtype=int64), 'cur_cost': 1011.0, 'intermediate_solutions': [{'tour': array([3, 1, 0, 8, 2, 6, 4, 5, 7]), 'cur_cost': 1056.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 3, 1, 0, 2, 6, 4, 5, 7]), 'cur_cost': 1050.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 8, 3, 1, 0, 6, 4, 5, 7]), 'cur_cost': 975.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 8, 3, 1, 2, 6, 4, 5, 7]), 'cur_cost': 1153.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 2, 8, 3, 1, 6, 4, 5, 7]), 'cur_cost': 1035.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:39,950 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1011.00)
2025-08-05 09:51:39,950 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:51:39,950 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:51:39,950 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:39,951 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 09:51:39,951 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,951 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,951 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,951 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,951 - ExplorationExpert - INFO - 探索路径生成完成，成本: 978.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:51:39,951 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [5, 8, 3, 4, 0, 7, 6, 1, 2], 'cur_cost': 978.0, 'intermediate_solutions': [{'tour': [0, 1, 2, 7, 3, 4, 8, 5, 6], 'cur_cost': 853.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 1, 7, 2, 4, 8, 5, 6], 'cur_cost': 960.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 6, 2, 7, 1, 4, 8, 5], 'cur_cost': 1097.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:39,952 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 978.00)
2025-08-05 09:51:39,952 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:51:39,952 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:51:39,952 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:39,952 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:51:39,952 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,952 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,952 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,953 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,953 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1023.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:51:39,953 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 8, 2, 7, 1, 3, 5, 6, 4], 'cur_cost': 1023.0, 'intermediate_solutions': [{'tour': [5, 6, 0, 1, 3, 4, 2, 8, 7], 'cur_cost': 808.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 6, 0, 1, 8, 2, 4, 3, 7], 'cur_cost': 793.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 1, 6, 0, 7, 3, 4, 2, 8], 'cur_cost': 901.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:39,953 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1023.00)
2025-08-05 09:51:39,953 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:51:39,953 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:51:39,953 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:39,954 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:51:39,954 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,954 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,954 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,954 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,954 - ExplorationExpert - INFO - 探索路径生成完成，成本: 976.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:51:39,954 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [4, 5, 1, 7, 8, 3, 6, 0, 2], 'cur_cost': 976.0, 'intermediate_solutions': [{'tour': [6, 7, 8, 2, 5, 4, 3, 0, 1], 'cur_cost': 989.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 8, 2, 5, 3, 4, 1, 0], 'cur_cost': 870.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 7, 8, 2, 5, 3, 4, 0, 1], 'cur_cost': 870.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:39,954 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 976.00)
2025-08-05 09:51:39,955 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:51:39,955 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:39,955 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:39,955 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1022.0
2025-08-05 09:51:39,960 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:39,960 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680, 853.0]
2025-08-05 09:51:39,961 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 8, 3, 5, 6, 2, 4, 7, 1], dtype=int64)]
2025-08-05 09:51:39,962 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:39,962 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 1, 8, 3, 7, 6, 4, 2], 'cur_cost': 1020.0}, {'tour': array([8, 6, 1, 4, 7, 3, 5, 2, 0], dtype=int64), 'cur_cost': 1019.0}, {'tour': array([5, 2, 7, 3, 1, 0, 6, 4, 8], dtype=int64), 'cur_cost': 1011.0}, {'tour': [5, 8, 3, 4, 0, 7, 6, 1, 2], 'cur_cost': 978.0}, {'tour': [0, 8, 2, 7, 1, 3, 5, 6, 4], 'cur_cost': 1023.0}, {'tour': [4, 5, 1, 7, 8, 3, 6, 0, 2], 'cur_cost': 976.0}, {'tour': array([0, 8, 5, 7, 4, 3, 2, 6, 1], dtype=int64), 'cur_cost': 1022.0}, {'tour': [6, 7, 8, 4, 1, 2, 5, 3, 0], 'cur_cost': 963.0}, {'tour': [0, 1, 4, 8, 3, 5, 7, 6, 2], 'cur_cost': 853.0}, {'tour': [0, 5, 6, 8, 4, 7, 3, 2, 1], 'cur_cost': 917.0}]
2025-08-05 09:51:39,963 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:39,963 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-08-05 09:51:39,963 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([0, 8, 5, 7, 4, 3, 2, 6, 1], dtype=int64), 'cur_cost': 1022.0, 'intermediate_solutions': [{'tour': array([7, 0, 1, 6, 8, 4, 3, 2, 5]), 'cur_cost': 995.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 7, 0, 1, 8, 4, 3, 2, 5]), 'cur_cost': 992.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 6, 7, 0, 1, 4, 3, 2, 5]), 'cur_cost': 997.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 6, 7, 0, 8, 4, 3, 2, 5]), 'cur_cost': 1125.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 8, 6, 7, 0, 4, 3, 2, 5]), 'cur_cost': 1175.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:39,963 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1022.00)
2025-08-05 09:51:39,963 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:51:39,963 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:51:39,964 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:39,964 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:51:39,964 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,964 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,964 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,964 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,964 - ExplorationExpert - INFO - 探索路径生成完成，成本: 967.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:51:39,965 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [3, 6, 0, 7, 1, 8, 4, 2, 5], 'cur_cost': 967.0, 'intermediate_solutions': [{'tour': [5, 7, 8, 4, 1, 2, 6, 3, 0], 'cur_cost': 1038.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 0, 3, 5, 2, 1, 4, 8, 7], 'cur_cost': 963.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 7, 8, 1, 4, 2, 5, 3, 0], 'cur_cost': 961.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:39,965 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 967.00)
2025-08-05 09:51:39,965 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:51:39,965 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:51:39,965 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:39,965 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:51:39,965 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,966 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,966 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,966 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,966 - ExplorationExpert - INFO - 探索路径生成完成，成本: 862.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:51:39,966 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [2, 8, 7, 4, 5, 3, 6, 0, 1], 'cur_cost': 862.0, 'intermediate_solutions': [{'tour': [0, 1, 4, 8, 3, 6, 7, 5, 2], 'cur_cost': 875.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 2, 6, 7, 5, 3, 8, 4], 'cur_cost': 842.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 8, 3, 1, 5, 7, 6, 2], 'cur_cost': 1057.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:39,966 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 862.00)
2025-08-05 09:51:39,966 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:51:39,966 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:51:39,967 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:39,967 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-05 09:51:39,967 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,967 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,967 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,968 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:39,968 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1081.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:51:39,968 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [4, 6, 1, 3, 0, 5, 7, 2, 8], 'cur_cost': 1081.0, 'intermediate_solutions': [{'tour': [0, 5, 6, 2, 4, 7, 3, 8, 1], 'cur_cost': 893.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 6, 8, 1, 2, 3, 7, 4], 'cur_cost': 1040.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 8, 4, 7, 6, 3, 2, 1], 'cur_cost': 927.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:39,968 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1081.00)
2025-08-05 09:51:39,968 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:39,968 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:39,969 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 1, 8, 3, 7, 6, 4, 2], 'cur_cost': 1020.0, 'intermediate_solutions': [{'tour': [7, 3, 8, 2, 0, 4, 6, 5, 1], 'cur_cost': 960.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 8, 0, 2, 4, 5, 6, 1], 'cur_cost': 913.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 8, 2, 0, 3, 4, 5, 6, 1], 'cur_cost': 1014.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 6, 1, 4, 7, 3, 5, 2, 0], dtype=int64), 'cur_cost': 1019.0, 'intermediate_solutions': [{'tour': array([3, 5, 4, 6, 0, 8, 7, 2, 1]), 'cur_cost': 1106.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 3, 5, 4, 0, 8, 7, 2, 1]), 'cur_cost': 999.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 6, 3, 5, 4, 8, 7, 2, 1]), 'cur_cost': 893.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 6, 3, 5, 0, 8, 7, 2, 1]), 'cur_cost': 1037.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 0, 6, 3, 5, 8, 7, 2, 1]), 'cur_cost': 904.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([5, 2, 7, 3, 1, 0, 6, 4, 8], dtype=int64), 'cur_cost': 1011.0, 'intermediate_solutions': [{'tour': array([3, 1, 0, 8, 2, 6, 4, 5, 7]), 'cur_cost': 1056.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 3, 1, 0, 2, 6, 4, 5, 7]), 'cur_cost': 1050.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 8, 3, 1, 0, 6, 4, 5, 7]), 'cur_cost': 975.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 8, 3, 1, 2, 6, 4, 5, 7]), 'cur_cost': 1153.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 2, 8, 3, 1, 6, 4, 5, 7]), 'cur_cost': 1035.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [5, 8, 3, 4, 0, 7, 6, 1, 2], 'cur_cost': 978.0, 'intermediate_solutions': [{'tour': [0, 1, 2, 7, 3, 4, 8, 5, 6], 'cur_cost': 853.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 1, 7, 2, 4, 8, 5, 6], 'cur_cost': 960.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 6, 2, 7, 1, 4, 8, 5], 'cur_cost': 1097.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 2, 7, 1, 3, 5, 6, 4], 'cur_cost': 1023.0, 'intermediate_solutions': [{'tour': [5, 6, 0, 1, 3, 4, 2, 8, 7], 'cur_cost': 808.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 6, 0, 1, 8, 2, 4, 3, 7], 'cur_cost': 793.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 1, 6, 0, 7, 3, 4, 2, 8], 'cur_cost': 901.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [4, 5, 1, 7, 8, 3, 6, 0, 2], 'cur_cost': 976.0, 'intermediate_solutions': [{'tour': [6, 7, 8, 2, 5, 4, 3, 0, 1], 'cur_cost': 989.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 8, 2, 5, 3, 4, 1, 0], 'cur_cost': 870.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 7, 8, 2, 5, 3, 4, 0, 1], 'cur_cost': 870.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 8, 5, 7, 4, 3, 2, 6, 1], dtype=int64), 'cur_cost': 1022.0, 'intermediate_solutions': [{'tour': array([7, 0, 1, 6, 8, 4, 3, 2, 5]), 'cur_cost': 995.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 7, 0, 1, 8, 4, 3, 2, 5]), 'cur_cost': 992.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 6, 7, 0, 1, 4, 3, 2, 5]), 'cur_cost': 997.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 6, 7, 0, 8, 4, 3, 2, 5]), 'cur_cost': 1125.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 8, 6, 7, 0, 4, 3, 2, 5]), 'cur_cost': 1175.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [3, 6, 0, 7, 1, 8, 4, 2, 5], 'cur_cost': 967.0, 'intermediate_solutions': [{'tour': [5, 7, 8, 4, 1, 2, 6, 3, 0], 'cur_cost': 1038.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 0, 3, 5, 2, 1, 4, 8, 7], 'cur_cost': 963.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 7, 8, 1, 4, 2, 5, 3, 0], 'cur_cost': 961.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [2, 8, 7, 4, 5, 3, 6, 0, 1], 'cur_cost': 862.0, 'intermediate_solutions': [{'tour': [0, 1, 4, 8, 3, 6, 7, 5, 2], 'cur_cost': 875.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 2, 6, 7, 5, 3, 8, 4], 'cur_cost': 842.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 8, 3, 1, 5, 7, 6, 2], 'cur_cost': 1057.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [4, 6, 1, 3, 0, 5, 7, 2, 8], 'cur_cost': 1081.0, 'intermediate_solutions': [{'tour': [0, 5, 6, 2, 4, 7, 3, 8, 1], 'cur_cost': 893.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 6, 8, 1, 2, 3, 7, 4], 'cur_cost': 1040.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 8, 4, 7, 6, 3, 2, 1], 'cur_cost': 927.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:51:39,969 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:39,970 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:39,970 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=862.000, 多样性=0.830
2025-08-05 09:51:39,971 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 09:51:39,971 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 09:51:39,971 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:39,971 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.028590549866207973, 'best_improvement': -0.010550996483001172}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.056179775280898764}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.004949434581470978, 'recent_improvements': [0.01887708076539858, -0.007084735471716982, 0.008978211602456624], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.7592592592592592, 'new_diversity': 0.7592592592592592, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:51:39,971 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 09:51:39,971 - __main__ - INFO - simple1_9 开始进化第 5 代
2025-08-05 09:51:39,971 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 09:51:39,972 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:39,972 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=862.000, 多样性=0.830
2025-08-05 09:51:39,972 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:39,973 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.830
2025-08-05 09:51:39,973 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:39,973 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.759
2025-08-05 09:51:39,975 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 09:51:39,976 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:39,976 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 09:51:39,976 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 09:51:39,984 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.286, 适应度梯度: 3.900, 聚类评分: 0.000, 覆盖率: 0.005, 收敛趋势: 0.000, 多样性: 0.534
2025-08-05 09:51:39,984 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 09:51:39,984 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:51:39,985 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-05 09:51:39,989 - visualization.landscape_visualizer - INFO - 插值约束: 137 个点被约束到最小值 680.00
2025-08-05 09:51:40,062 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_5_20250805_095140.html
2025-08-05 09:51:40,103 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_5_20250805_095140.html
2025-08-05 09:51:40,103 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 5
2025-08-05 09:51:40,103 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 09:51:40,103 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1274秒
2025-08-05 09:51:40,103 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2857142857142857, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 3.9000000000000017, 'local_optima_density': 0.2857142857142857, 'gradient_variance': 24731.14142857143, 'cluster_count': 0}, 'population_state': {'diversity': 0.533751962323391, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0053, 'fitness_entropy': 0.9285249215482253, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.005)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 3.900)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358699.984266, 'performance_metrics': {}}}
2025-08-05 09:51:40,104 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:40,104 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:40,104 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:40,104 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:40,104 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-05 09:51:40,104 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 09:51:40,104 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-05 09:51:40,105 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:40,105 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:40,105 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-05 09:51:40,105 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:40,105 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:40,106 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:40,106 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:51:40,106 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:51:40,106 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,106 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 09:51:40,106 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,107 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,107 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,107 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,107 - ExplorationExpert - INFO - 探索路径生成完成，成本: 847.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:51:40,107 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 3, 5, 7, 8, 4, 2, 1, 6], 'cur_cost': 847.0, 'intermediate_solutions': [{'tour': [8, 5, 1, 0, 3, 7, 6, 4, 2], 'cur_cost': 877.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 1, 8, 3, 7, 2, 4, 6], 'cur_cost': 1039.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 1, 8, 3, 7, 6, 4, 2], 'cur_cost': 1020.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,107 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 847.00)
2025-08-05 09:51:40,107 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:51:40,107 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:51:40,107 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,108 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 09:51:40,108 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,108 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,108 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,108 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,108 - ExplorationExpert - INFO - 探索路径生成完成，成本: 889.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:51:40,108 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [6, 5, 7, 0, 4, 8, 3, 2, 1], 'cur_cost': 889.0, 'intermediate_solutions': [{'tour': [4, 6, 1, 8, 7, 3, 5, 2, 0], 'cur_cost': 1037.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 4, 1, 6, 7, 3, 5, 2, 0], 'cur_cost': 961.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 3, 6, 1, 4, 7, 5, 2, 0], 'cur_cost': 1006.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,109 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 889.00)
2025-08-05 09:51:40,109 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:51:40,109 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:51:40,109 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,109 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-05 09:51:40,109 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,109 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,110 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,110 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,110 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1028.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:51:40,110 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [3, 2, 0, 4, 5, 7, 6, 1, 8], 'cur_cost': 1028.0, 'intermediate_solutions': [{'tour': [5, 2, 6, 3, 1, 0, 7, 4, 8], 'cur_cost': 1042.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 2, 7, 3, 8, 4, 6, 0, 1], 'cur_cost': 991.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 2, 3, 1, 0, 6, 4, 8], 'cur_cost': 998.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,110 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1028.00)
2025-08-05 09:51:40,110 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:51:40,110 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:51:40,110 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,111 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 09:51:40,111 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,111 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,111 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,111 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,111 - ExplorationExpert - INFO - 探索路径生成完成，成本: 966.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:51:40,112 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [3, 6, 5, 0, 4, 7, 8, 2, 1], 'cur_cost': 966.0, 'intermediate_solutions': [{'tour': [2, 8, 3, 4, 0, 7, 6, 1, 5], 'cur_cost': 1007.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 8, 3, 4, 0, 7, 6, 2, 1], 'cur_cost': 1041.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 3, 4, 0, 7, 6, 1, 2, 5], 'cur_cost': 978.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,112 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 966.00)
2025-08-05 09:51:40,112 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 09:51:40,112 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:40,112 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:40,112 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 990.0
2025-08-05 09:51:40,118 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:40,118 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680, 853.0]
2025-08-05 09:51:40,118 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 8, 3, 5, 6, 2, 4, 7, 1], dtype=int64)]
2025-08-05 09:51:40,119 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:40,119 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 5, 7, 8, 4, 2, 1, 6], 'cur_cost': 847.0}, {'tour': [6, 5, 7, 0, 4, 8, 3, 2, 1], 'cur_cost': 889.0}, {'tour': [3, 2, 0, 4, 5, 7, 6, 1, 8], 'cur_cost': 1028.0}, {'tour': [3, 6, 5, 0, 4, 7, 8, 2, 1], 'cur_cost': 966.0}, {'tour': array([3, 1, 0, 2, 6, 7, 8, 4, 5], dtype=int64), 'cur_cost': 990.0}, {'tour': [4, 5, 1, 7, 8, 3, 6, 0, 2], 'cur_cost': 976.0}, {'tour': [0, 8, 5, 7, 4, 3, 2, 6, 1], 'cur_cost': 1022.0}, {'tour': [3, 6, 0, 7, 1, 8, 4, 2, 5], 'cur_cost': 967.0}, {'tour': [2, 8, 7, 4, 5, 3, 6, 0, 1], 'cur_cost': 862.0}, {'tour': [4, 6, 1, 3, 0, 5, 7, 2, 8], 'cur_cost': 1081.0}]
2025-08-05 09:51:40,119 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:40,119 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-08-05 09:51:40,120 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([3, 1, 0, 2, 6, 7, 8, 4, 5], dtype=int64), 'cur_cost': 990.0, 'intermediate_solutions': [{'tour': array([2, 8, 0, 7, 1, 3, 5, 6, 4]), 'cur_cost': 947.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 2, 8, 0, 1, 3, 5, 6, 4]), 'cur_cost': 950.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 7, 2, 8, 0, 3, 5, 6, 4]), 'cur_cost': 1004.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 7, 2, 8, 1, 3, 5, 6, 4]), 'cur_cost': 1021.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 1, 7, 2, 8, 3, 5, 6, 4]), 'cur_cost': 842.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:40,120 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 990.00)
2025-08-05 09:51:40,120 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:51:40,120 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:51:40,120 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,120 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:51:40,121 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,121 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,121 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,121 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,121 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1008.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:51:40,121 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [2, 7, 3, 4, 6, 8, 5, 0, 1], 'cur_cost': 1008.0, 'intermediate_solutions': [{'tour': [4, 5, 1, 6, 8, 3, 7, 0, 2], 'cur_cost': 971.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 1, 5, 4, 8, 3, 6, 0, 2], 'cur_cost': 1060.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 5, 1, 0, 7, 8, 3, 6, 2], 'cur_cost': 943.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,121 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1008.00)
2025-08-05 09:51:40,121 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:51:40,122 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:40,122 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:40,122 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1164.0
2025-08-05 09:51:40,128 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:40,129 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680, 853.0]
2025-08-05 09:51:40,129 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 8, 3, 5, 6, 2, 4, 7, 1], dtype=int64)]
2025-08-05 09:51:40,132 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:40,132 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 5, 7, 8, 4, 2, 1, 6], 'cur_cost': 847.0}, {'tour': [6, 5, 7, 0, 4, 8, 3, 2, 1], 'cur_cost': 889.0}, {'tour': [3, 2, 0, 4, 5, 7, 6, 1, 8], 'cur_cost': 1028.0}, {'tour': [3, 6, 5, 0, 4, 7, 8, 2, 1], 'cur_cost': 966.0}, {'tour': array([3, 1, 0, 2, 6, 7, 8, 4, 5], dtype=int64), 'cur_cost': 990.0}, {'tour': [2, 7, 3, 4, 6, 8, 5, 0, 1], 'cur_cost': 1008.0}, {'tour': array([1, 6, 8, 7, 2, 5, 4, 0, 3], dtype=int64), 'cur_cost': 1164.0}, {'tour': [3, 6, 0, 7, 1, 8, 4, 2, 5], 'cur_cost': 967.0}, {'tour': [2, 8, 7, 4, 5, 3, 6, 0, 1], 'cur_cost': 862.0}, {'tour': [4, 6, 1, 3, 0, 5, 7, 2, 8], 'cur_cost': 1081.0}]
2025-08-05 09:51:40,133 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:40,133 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-08-05 09:51:40,134 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([1, 6, 8, 7, 2, 5, 4, 0, 3], dtype=int64), 'cur_cost': 1164.0, 'intermediate_solutions': [{'tour': array([5, 8, 0, 7, 4, 3, 2, 6, 1]), 'cur_cost': 1158.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 5, 8, 0, 4, 3, 2, 6, 1]), 'cur_cost': 1095.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 7, 5, 8, 0, 3, 2, 6, 1]), 'cur_cost': 1089.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 7, 5, 8, 4, 3, 2, 6, 1]), 'cur_cost': 970.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 4, 7, 5, 8, 3, 2, 6, 1]), 'cur_cost': 927.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:40,134 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1164.00)
2025-08-05 09:51:40,134 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:51:40,134 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:51:40,134 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,135 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 09:51:40,135 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,135 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,135 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,136 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,136 - ExplorationExpert - INFO - 探索路径生成完成，成本: 796.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:51:40,136 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [2, 4, 8, 3, 5, 7, 0, 1, 6], 'cur_cost': 796.0, 'intermediate_solutions': [{'tour': [3, 6, 0, 7, 1, 2, 4, 8, 5], 'cur_cost': 855.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 6, 8, 1, 7, 0, 4, 2, 5], 'cur_cost': 1000.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 6, 0, 7, 1, 8, 4, 2, 5], 'cur_cost': 967.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,136 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 796.00)
2025-08-05 09:51:40,136 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:51:40,137 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:51:40,137 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,137 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-05 09:51:40,137 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,138 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,138 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,138 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,139 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1209.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:51:40,139 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [6, 3, 1, 8, 4, 5, 0, 7, 2], 'cur_cost': 1209.0, 'intermediate_solutions': [{'tour': [2, 8, 7, 4, 5, 3, 1, 0, 6], 'cur_cost': 978.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 7, 5, 4, 3, 6, 0, 1], 'cur_cost': 898.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 3, 8, 7, 4, 5, 6, 0, 1], 'cur_cost': 897.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,139 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 1209.00)
2025-08-05 09:51:40,139 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 09:51:40,139 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:40,139 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:40,139 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 946.0
2025-08-05 09:51:40,147 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:40,147 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680, 853.0]
2025-08-05 09:51:40,147 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 8, 3, 5, 6, 2, 4, 7, 1], dtype=int64)]
2025-08-05 09:51:40,149 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:40,149 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 5, 7, 8, 4, 2, 1, 6], 'cur_cost': 847.0}, {'tour': [6, 5, 7, 0, 4, 8, 3, 2, 1], 'cur_cost': 889.0}, {'tour': [3, 2, 0, 4, 5, 7, 6, 1, 8], 'cur_cost': 1028.0}, {'tour': [3, 6, 5, 0, 4, 7, 8, 2, 1], 'cur_cost': 966.0}, {'tour': array([3, 1, 0, 2, 6, 7, 8, 4, 5], dtype=int64), 'cur_cost': 990.0}, {'tour': [2, 7, 3, 4, 6, 8, 5, 0, 1], 'cur_cost': 1008.0}, {'tour': array([1, 6, 8, 7, 2, 5, 4, 0, 3], dtype=int64), 'cur_cost': 1164.0}, {'tour': [2, 4, 8, 3, 5, 7, 0, 1, 6], 'cur_cost': 796.0}, {'tour': [6, 3, 1, 8, 4, 5, 0, 7, 2], 'cur_cost': 1209.0}, {'tour': array([1, 3, 2, 8, 7, 4, 5, 6, 0], dtype=int64), 'cur_cost': 946.0}]
2025-08-05 09:51:40,150 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:40,150 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-08-05 09:51:40,151 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([1, 3, 2, 8, 7, 4, 5, 6, 0], dtype=int64), 'cur_cost': 946.0, 'intermediate_solutions': [{'tour': array([1, 6, 4, 3, 0, 5, 7, 2, 8]), 'cur_cost': 1118.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 1, 6, 4, 0, 5, 7, 2, 8]), 'cur_cost': 1025.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 3, 1, 6, 4, 5, 7, 2, 8]), 'cur_cost': 1137.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 3, 1, 6, 0, 5, 7, 2, 8]), 'cur_cost': 1026.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 0, 3, 1, 6, 5, 7, 2, 8]), 'cur_cost': 959.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:40,152 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 946.00)
2025-08-05 09:51:40,152 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:40,152 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:40,154 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 5, 7, 8, 4, 2, 1, 6], 'cur_cost': 847.0, 'intermediate_solutions': [{'tour': [8, 5, 1, 0, 3, 7, 6, 4, 2], 'cur_cost': 877.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 1, 8, 3, 7, 2, 4, 6], 'cur_cost': 1039.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 1, 8, 3, 7, 6, 4, 2], 'cur_cost': 1020.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [6, 5, 7, 0, 4, 8, 3, 2, 1], 'cur_cost': 889.0, 'intermediate_solutions': [{'tour': [4, 6, 1, 8, 7, 3, 5, 2, 0], 'cur_cost': 1037.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 4, 1, 6, 7, 3, 5, 2, 0], 'cur_cost': 961.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 3, 6, 1, 4, 7, 5, 2, 0], 'cur_cost': 1006.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [3, 2, 0, 4, 5, 7, 6, 1, 8], 'cur_cost': 1028.0, 'intermediate_solutions': [{'tour': [5, 2, 6, 3, 1, 0, 7, 4, 8], 'cur_cost': 1042.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 2, 7, 3, 8, 4, 6, 0, 1], 'cur_cost': 991.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 2, 3, 1, 0, 6, 4, 8], 'cur_cost': 998.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [3, 6, 5, 0, 4, 7, 8, 2, 1], 'cur_cost': 966.0, 'intermediate_solutions': [{'tour': [2, 8, 3, 4, 0, 7, 6, 1, 5], 'cur_cost': 1007.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 8, 3, 4, 0, 7, 6, 2, 1], 'cur_cost': 1041.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 3, 4, 0, 7, 6, 1, 2, 5], 'cur_cost': 978.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 1, 0, 2, 6, 7, 8, 4, 5], dtype=int64), 'cur_cost': 990.0, 'intermediate_solutions': [{'tour': array([2, 8, 0, 7, 1, 3, 5, 6, 4]), 'cur_cost': 947.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 2, 8, 0, 1, 3, 5, 6, 4]), 'cur_cost': 950.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 7, 2, 8, 0, 3, 5, 6, 4]), 'cur_cost': 1004.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 7, 2, 8, 1, 3, 5, 6, 4]), 'cur_cost': 1021.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 1, 7, 2, 8, 3, 5, 6, 4]), 'cur_cost': 842.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [2, 7, 3, 4, 6, 8, 5, 0, 1], 'cur_cost': 1008.0, 'intermediate_solutions': [{'tour': [4, 5, 1, 6, 8, 3, 7, 0, 2], 'cur_cost': 971.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 1, 5, 4, 8, 3, 6, 0, 2], 'cur_cost': 1060.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 5, 1, 0, 7, 8, 3, 6, 2], 'cur_cost': 943.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([1, 6, 8, 7, 2, 5, 4, 0, 3], dtype=int64), 'cur_cost': 1164.0, 'intermediate_solutions': [{'tour': array([5, 8, 0, 7, 4, 3, 2, 6, 1]), 'cur_cost': 1158.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 5, 8, 0, 4, 3, 2, 6, 1]), 'cur_cost': 1095.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 7, 5, 8, 0, 3, 2, 6, 1]), 'cur_cost': 1089.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 7, 5, 8, 4, 3, 2, 6, 1]), 'cur_cost': 970.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 4, 7, 5, 8, 3, 2, 6, 1]), 'cur_cost': 927.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [2, 4, 8, 3, 5, 7, 0, 1, 6], 'cur_cost': 796.0, 'intermediate_solutions': [{'tour': [3, 6, 0, 7, 1, 2, 4, 8, 5], 'cur_cost': 855.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 6, 8, 1, 7, 0, 4, 2, 5], 'cur_cost': 1000.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 6, 0, 7, 1, 8, 4, 2, 5], 'cur_cost': 967.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [6, 3, 1, 8, 4, 5, 0, 7, 2], 'cur_cost': 1209.0, 'intermediate_solutions': [{'tour': [2, 8, 7, 4, 5, 3, 1, 0, 6], 'cur_cost': 978.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 7, 5, 4, 3, 6, 0, 1], 'cur_cost': 898.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 3, 8, 7, 4, 5, 6, 0, 1], 'cur_cost': 897.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([1, 3, 2, 8, 7, 4, 5, 6, 0], dtype=int64), 'cur_cost': 946.0, 'intermediate_solutions': [{'tour': array([1, 6, 4, 3, 0, 5, 7, 2, 8]), 'cur_cost': 1118.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 1, 6, 4, 0, 5, 7, 2, 8]), 'cur_cost': 1025.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 3, 1, 6, 4, 5, 7, 2, 8]), 'cur_cost': 1137.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 3, 1, 6, 0, 5, 7, 2, 8]), 'cur_cost': 1026.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 0, 3, 1, 6, 5, 7, 2, 8]), 'cur_cost': 959.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 09:51:40,154 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:40,154 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:40,156 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=796.000, 多样性=0.894
2025-08-05 09:51:40,156 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 09:51:40,156 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 09:51:40,157 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:40,157 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.01006499985908392, 'best_improvement': 0.0765661252900232}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.07738095238095255}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.010752907197245495, 'recent_improvements': [-0.007084735471716982, 0.008978211602456624, -0.028590549866207973], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.7592592592592592, 'new_diversity': 0.7592592592592592, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:51:40,157 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 09:51:40,187 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_solution.json
2025-08-05 09:51:40,187 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_20250805_095140.solution
2025-08-05 09:51:40,188 - __main__ - INFO - 实例执行完成 - 运行时间: 14.46s, 最佳成本: 680.0
2025-08-05 09:51:40,188 - __main__ - INFO - 实例 simple1_9 处理完成
