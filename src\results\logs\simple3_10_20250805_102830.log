2025-08-05 10:28:30,679 - __main__ - INFO - simple3_10 开始进化第 1 代
2025-08-05 10:28:30,680 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 10:28:30,680 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:30,682 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=873.000, 多样性=0.898
2025-08-05 10:28:30,683 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:30,683 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.898
2025-08-05 10:28:30,684 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:30,686 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 10:28:30,687 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:30,687 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 10:28:30,687 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 10:28:30,693 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -20.380, 聚类评分: 0.000, 覆盖率: 0.012, 收敛趋势: 0.000, 多样性: 0.898
2025-08-05 10:28:30,694 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 10:28:30,694 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 10:28:30,694 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple3_10
2025-08-05 10:28:30,698 - visualization.landscape_visualizer - INFO - 插值约束: 1 个点被约束到最小值 873.00
2025-08-05 10:28:30,700 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=9.7%, 梯度: 19.52 → 17.63
2025-08-05 10:28:30,806 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple3_10\landscape_simple3_10_iter_11_20250805_102830.html
2025-08-05 10:28:30,881 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple3_10\dashboard_simple3_10_iter_11_20250805_102830.html
2025-08-05 10:28:30,881 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 11
2025-08-05 10:28:30,881 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 10:28:30,881 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1942秒
2025-08-05 10:28:30,882 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 22, 'max_size': 500, 'hits': 0, 'misses': 22, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 32, 'max_size': 100, 'hits': 74, 'misses': 32, 'hit_rate': 0.6981132075471698, 'evictions': 0, 'ttl': 7200}}
2025-08-05 10:28:30,882 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -20.380000000000006, 'local_optima_density': 0.1, 'gradient_variance': 87131.5156, 'cluster_count': 0}, 'population_state': {'diversity': 0.8977777777777778, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0117, 'fitness_entropy': 0.9138646883853215, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -20.380)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.012)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.898)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360910.693208, 'performance_metrics': {}}}
2025-08-05 10:28:30,882 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:30,882 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:30,882 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:30,883 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:30,883 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 10:28:30,883 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 10:28:30,884 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 10:28:30,884 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:30,884 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:30,884 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 10:28:30,884 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:30,884 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:30,885 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:30,885 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:30,885 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:30,885 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:30,886 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:30,886 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,886 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1270.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:30,886 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [6, 9, 2, 3, 1, 5, 4, 7, 0, 8], 'cur_cost': 1270.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:30,887 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 1270.00)
2025-08-05 10:28:30,887 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:30,887 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:30,887 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:30,888 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:30,888 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,889 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1596.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:30,889 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [6, 8, 7, 4, 3, 1, 2, 5, 0, 9], 'cur_cost': 1596.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:30,889 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 1596.00)
2025-08-05 10:28:30,889 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:30,890 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:30,890 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:30,890 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:30,891 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,891 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1435.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:30,891 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [8, 2, 3, 4, 7, 6, 1, 0, 5, 9], 'cur_cost': 1435.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:30,891 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1435.00)
2025-08-05 10:28:30,891 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:30,892 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:30,892 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:30,893 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:30,893 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,893 - ExplorationExpert - INFO - 探索路径生成完成，成本: 939.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:30,893 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [1, 5, 4, 7, 0, 6, 2, 3, 8, 9], 'cur_cost': 939.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:30,893 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 939.00)
2025-08-05 10:28:30,893 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:30,893 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:30,893 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:30,894 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:30,894 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,895 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1402.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:30,895 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [1, 0, 3, 6, 5, 4, 8, 2, 7, 9], 'cur_cost': 1402.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:30,895 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1402.00)
2025-08-05 10:28:30,895 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:28:30,895 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:30,896 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:30,896 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 1557.0
2025-08-05 10:28:30,901 - ExploitationExpert - INFO - res_population_num: 1
2025-08-05 10:28:30,901 - ExploitationExpert - INFO - res_population_costs: [832.0]
2025-08-05 10:28:30,901 - ExploitationExpert - INFO - res_populations: [array([0, 9, 1, 7, 4, 5, 6, 2, 3, 8], dtype=int64)]
2025-08-05 10:28:30,902 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:30,902 - ExploitationExpert - INFO - populations: [{'tour': [6, 9, 2, 3, 1, 5, 4, 7, 0, 8], 'cur_cost': 1270.0}, {'tour': [6, 8, 7, 4, 3, 1, 2, 5, 0, 9], 'cur_cost': 1596.0}, {'tour': [8, 2, 3, 4, 7, 6, 1, 0, 5, 9], 'cur_cost': 1435.0}, {'tour': [1, 5, 4, 7, 0, 6, 2, 3, 8, 9], 'cur_cost': 939.0}, {'tour': [1, 0, 3, 6, 5, 4, 8, 2, 7, 9], 'cur_cost': 1402.0}, {'tour': array([4, 3, 2, 9, 1, 8, 6, 5, 7, 0], dtype=int64), 'cur_cost': 1557.0}, {'tour': array([9, 5, 4, 6, 2, 1, 0, 8, 7, 3], dtype=int64), 'cur_cost': 1411.0}, {'tour': array([4, 9, 8, 1, 2, 3, 6, 5, 7, 0], dtype=int64), 'cur_cost': 1431.0}, {'tour': array([2, 0, 9, 1, 6, 5, 7, 3, 8, 4], dtype=int64), 'cur_cost': 1462.0}, {'tour': array([8, 4, 9, 2, 1, 7, 6, 5, 3, 0], dtype=int64), 'cur_cost': 1409.0}]
2025-08-05 10:28:30,903 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:30,903 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 27, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 27, 'cache_hits': 0, 'similarity_calculations': 169, 'cache_hit_rate': 0.0, 'cache_size': 169}}
2025-08-05 10:28:30,904 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([4, 3, 2, 9, 1, 8, 6, 5, 7, 0], dtype=int64), 'cur_cost': 1557.0, 'intermediate_solutions': [{'tour': array([0, 4, 3, 9, 1, 5, 6, 2, 8, 7], dtype=int64), 'cur_cost': 1426.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([9, 0, 4, 3, 1, 5, 6, 2, 8, 7], dtype=int64), 'cur_cost': 1508.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 9, 0, 4, 3, 5, 6, 2, 8, 7], dtype=int64), 'cur_cost': 1377.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 9, 0, 4, 1, 5, 6, 2, 8, 7], dtype=int64), 'cur_cost': 1258.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 1, 9, 0, 4, 5, 6, 2, 8, 7], dtype=int64), 'cur_cost': 1469.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:30,904 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1557.00)
2025-08-05 10:28:30,904 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:30,904 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:30,904 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:30,905 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:30,905 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,905 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1171.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:30,905 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [4, 0, 3, 6, 5, 1, 7, 2, 8, 9], 'cur_cost': 1171.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:30,905 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1171.00)
2025-08-05 10:28:30,906 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:30,906 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:30,906 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:30,906 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:30,906 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,907 - ExplorationExpert - INFO - 探索路径生成完成，成本: 892.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:30,907 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [7, 1, 5, 8, 0, 6, 2, 3, 9, 4], 'cur_cost': 892.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:30,907 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 892.00)
2025-08-05 10:28:30,907 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:30,907 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:30,907 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:30,907 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 1369.0
2025-08-05 10:28:30,912 - ExploitationExpert - INFO - res_population_num: 3
2025-08-05 10:28:30,912 - ExploitationExpert - INFO - res_population_costs: [832.0, 832.0, 832]
2025-08-05 10:28:30,912 - ExploitationExpert - INFO - res_populations: [array([0, 9, 1, 7, 4, 5, 6, 2, 3, 8], dtype=int64), array([0, 8, 3, 6, 2, 5, 4, 7, 1, 9], dtype=int64), array([0, 8, 3, 6, 2, 5, 7, 1, 4, 9], dtype=int64)]
2025-08-05 10:28:30,913 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:30,913 - ExploitationExpert - INFO - populations: [{'tour': [6, 9, 2, 3, 1, 5, 4, 7, 0, 8], 'cur_cost': 1270.0}, {'tour': [6, 8, 7, 4, 3, 1, 2, 5, 0, 9], 'cur_cost': 1596.0}, {'tour': [8, 2, 3, 4, 7, 6, 1, 0, 5, 9], 'cur_cost': 1435.0}, {'tour': [1, 5, 4, 7, 0, 6, 2, 3, 8, 9], 'cur_cost': 939.0}, {'tour': [1, 0, 3, 6, 5, 4, 8, 2, 7, 9], 'cur_cost': 1402.0}, {'tour': array([4, 3, 2, 9, 1, 8, 6, 5, 7, 0], dtype=int64), 'cur_cost': 1557.0}, {'tour': [4, 0, 3, 6, 5, 1, 7, 2, 8, 9], 'cur_cost': 1171.0}, {'tour': [7, 1, 5, 8, 0, 6, 2, 3, 9, 4], 'cur_cost': 892.0}, {'tour': array([8, 6, 9, 0, 7, 1, 3, 2, 4, 5], dtype=int64), 'cur_cost': 1369.0}, {'tour': array([8, 4, 9, 2, 1, 7, 6, 5, 3, 0], dtype=int64), 'cur_cost': 1409.0}]
2025-08-05 10:28:30,913 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:30,914 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 28, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 28, 'cache_hits': 0, 'similarity_calculations': 183, 'cache_hit_rate': 0.0, 'cache_size': 183}}
2025-08-05 10:28:30,914 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([8, 6, 9, 0, 7, 1, 3, 2, 4, 5], dtype=int64), 'cur_cost': 1369.0, 'intermediate_solutions': [{'tour': array([9, 0, 2, 1, 6, 5, 7, 3, 8, 4], dtype=int64), 'cur_cost': 1479.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 9, 0, 2, 6, 5, 7, 3, 8, 4], dtype=int64), 'cur_cost': 1154.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 1, 9, 0, 2, 5, 7, 3, 8, 4], dtype=int64), 'cur_cost': 1464.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 1, 9, 0, 6, 5, 7, 3, 8, 4], dtype=int64), 'cur_cost': 1451.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 6, 1, 9, 0, 5, 7, 3, 8, 4], dtype=int64), 'cur_cost': 1371.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:30,915 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1369.00)
2025-08-05 10:28:30,915 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:30,915 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:30,915 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:30,915 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:30,915 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,916 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1248.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:30,916 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [8, 2, 5, 7, 4, 0, 6, 3, 9, 1], 'cur_cost': 1248.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:30,916 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1248.00)
2025-08-05 10:28:30,916 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:30,916 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:30,917 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [6, 9, 2, 3, 1, 5, 4, 7, 0, 8], 'cur_cost': 1270.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [6, 8, 7, 4, 3, 1, 2, 5, 0, 9], 'cur_cost': 1596.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [8, 2, 3, 4, 7, 6, 1, 0, 5, 9], 'cur_cost': 1435.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [1, 5, 4, 7, 0, 6, 2, 3, 8, 9], 'cur_cost': 939.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [1, 0, 3, 6, 5, 4, 8, 2, 7, 9], 'cur_cost': 1402.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 3, 2, 9, 1, 8, 6, 5, 7, 0], dtype=int64), 'cur_cost': 1557.0, 'intermediate_solutions': [{'tour': array([0, 4, 3, 9, 1, 5, 6, 2, 8, 7], dtype=int64), 'cur_cost': 1426.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([9, 0, 4, 3, 1, 5, 6, 2, 8, 7], dtype=int64), 'cur_cost': 1508.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 9, 0, 4, 3, 5, 6, 2, 8, 7], dtype=int64), 'cur_cost': 1377.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 9, 0, 4, 1, 5, 6, 2, 8, 7], dtype=int64), 'cur_cost': 1258.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 1, 9, 0, 4, 5, 6, 2, 8, 7], dtype=int64), 'cur_cost': 1469.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [4, 0, 3, 6, 5, 1, 7, 2, 8, 9], 'cur_cost': 1171.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [7, 1, 5, 8, 0, 6, 2, 3, 9, 4], 'cur_cost': 892.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 6, 9, 0, 7, 1, 3, 2, 4, 5], dtype=int64), 'cur_cost': 1369.0, 'intermediate_solutions': [{'tour': array([9, 0, 2, 1, 6, 5, 7, 3, 8, 4], dtype=int64), 'cur_cost': 1479.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 9, 0, 2, 6, 5, 7, 3, 8, 4], dtype=int64), 'cur_cost': 1154.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 1, 9, 0, 2, 5, 7, 3, 8, 4], dtype=int64), 'cur_cost': 1464.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 1, 9, 0, 6, 5, 7, 3, 8, 4], dtype=int64), 'cur_cost': 1451.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 6, 1, 9, 0, 5, 7, 3, 8, 4], dtype=int64), 'cur_cost': 1371.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [8, 2, 5, 7, 4, 0, 6, 3, 9, 1], 'cur_cost': 1248.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:30,917 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:30,917 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:30,919 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=892.000, 多样性=0.887
2025-08-05 10:28:30,919 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 10:28:30,919 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 10:28:30,919 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:30,919 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.045960249994057234, 'best_improvement': -0.021764032073310423}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.012376237623762328}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.008886951095094126, 'recent_improvements': [-0.013429785229925767, 0.04448640679610708, -0.03120368742011402], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 832.0, 'new_best_cost': 832.0, 'quality_improvement': 0.0, 'old_diversity': 0.6333333333333333, 'new_diversity': 0.6333333333333333, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启']}
2025-08-05 10:28:30,920 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 10:28:30,920 - __main__ - INFO - simple3_10 开始进化第 2 代
2025-08-05 10:28:30,920 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 10:28:30,920 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:30,921 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=892.000, 多样性=0.887
2025-08-05 10:28:30,921 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:30,922 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.887
2025-08-05 10:28:30,922 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:30,923 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.633
2025-08-05 10:28:30,927 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 10:28:30,927 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:30,927 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-05 10:28:30,927 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-05 10:28:30,936 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.308, 适应度梯度: 34.000, 聚类评分: 0.000, 覆盖率: 0.013, 收敛趋势: 0.000, 多样性: 0.670
2025-08-05 10:28:30,936 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 10:28:30,937 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:30,937 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple3_10
2025-08-05 10:28:30,942 - visualization.landscape_visualizer - INFO - 插值约束: 348 个点被约束到最小值 832.00
2025-08-05 10:28:30,943 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=6.3%, 梯度: 34.18 → 32.04
2025-08-05 10:28:31,045 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple3_10\landscape_simple3_10_iter_12_20250805_102830.html
2025-08-05 10:28:31,114 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple3_10\dashboard_simple3_10_iter_12_20250805_102830.html
2025-08-05 10:28:31,114 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 12
2025-08-05 10:28:31,115 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 10:28:31,115 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1883秒
2025-08-05 10:28:31,115 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3076923076923077, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 34.00000000000001, 'local_optima_density': 0.3076923076923077, 'gradient_variance': 88653.04615384617, 'cluster_count': 0}, 'population_state': {'diversity': 0.6696252465483234, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.013, 'fitness_entropy': 0.9329350879565487, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.013)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 34.000)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360910.9361944, 'performance_metrics': {}}}
2025-08-05 10:28:31,115 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:31,115 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:31,115 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:31,115 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:31,116 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:28:31,116 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 10:28:31,116 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:28:31,116 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:31,116 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:31,116 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:28:31,116 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:31,116 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:31,117 - experts.management.collaboration_manager - INFO - 识别精英个体: {3, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:31,117 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:31,117 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:31,117 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:31,117 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:31,117 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,117 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,118 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,118 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,118 - ExplorationExpert - INFO - 探索路径生成完成，成本: 936.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:31,118 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [1, 4, 7, 9, 8, 0, 6, 2, 3, 5], 'cur_cost': 936.0, 'intermediate_solutions': [{'tour': [6, 9, 2, 0, 1, 5, 4, 7, 3, 8], 'cur_cost': 1283.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 9, 2, 3, 8, 0, 7, 4, 5, 1], 'cur_cost': 1183.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 9, 2, 3, 5, 1, 4, 7, 0, 8], 'cur_cost': 1191.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:31,118 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 936.00)
2025-08-05 10:28:31,118 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 10:28:31,118 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:31,118 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:31,118 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 1211.0
2025-08-05 10:28:31,124 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:28:31,124 - ExploitationExpert - INFO - res_population_costs: [832.0, 832.0, 832, 832.0, 832]
2025-08-05 10:28:31,124 - ExploitationExpert - INFO - res_populations: [array([0, 9, 1, 7, 4, 5, 6, 2, 3, 8], dtype=int64), array([0, 8, 3, 6, 2, 5, 4, 7, 1, 9], dtype=int64), array([0, 8, 3, 6, 2, 5, 7, 1, 4, 9], dtype=int64), array([0, 6, 2, 5, 7, 1, 4, 9, 3, 8], dtype=int64), array([0, 3, 6, 2, 5, 7, 1, 4, 9, 8], dtype=int64)]
2025-08-05 10:28:31,125 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:31,126 - ExploitationExpert - INFO - populations: [{'tour': [1, 4, 7, 9, 8, 0, 6, 2, 3, 5], 'cur_cost': 936.0}, {'tour': array([4, 7, 9, 6, 5, 0, 3, 8, 2, 1], dtype=int64), 'cur_cost': 1211.0}, {'tour': [8, 2, 3, 4, 7, 6, 1, 0, 5, 9], 'cur_cost': 1435.0}, {'tour': [1, 5, 4, 7, 0, 6, 2, 3, 8, 9], 'cur_cost': 939.0}, {'tour': [1, 0, 3, 6, 5, 4, 8, 2, 7, 9], 'cur_cost': 1402.0}, {'tour': [4, 3, 2, 9, 1, 8, 6, 5, 7, 0], 'cur_cost': 1557.0}, {'tour': [4, 0, 3, 6, 5, 1, 7, 2, 8, 9], 'cur_cost': 1171.0}, {'tour': [7, 1, 5, 8, 0, 6, 2, 3, 9, 4], 'cur_cost': 892.0}, {'tour': [8, 6, 9, 0, 7, 1, 3, 2, 4, 5], 'cur_cost': 1369.0}, {'tour': [8, 2, 5, 7, 4, 0, 6, 3, 9, 1], 'cur_cost': 1248.0}]
2025-08-05 10:28:31,126 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:31,126 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 29, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 29, 'cache_hits': 0, 'similarity_calculations': 198, 'cache_hit_rate': 0.0, 'cache_size': 198}}
2025-08-05 10:28:31,127 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([4, 7, 9, 6, 5, 0, 3, 8, 2, 1], dtype=int64), 'cur_cost': 1211.0, 'intermediate_solutions': [{'tour': array([7, 8, 6, 4, 3, 1, 2, 5, 0, 9]), 'cur_cost': 1729.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 7, 8, 6, 3, 1, 2, 5, 0, 9]), 'cur_cost': 1455.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 4, 7, 8, 6, 1, 2, 5, 0, 9]), 'cur_cost': 1498.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 4, 7, 8, 3, 1, 2, 5, 0, 9]), 'cur_cost': 1510.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 3, 4, 7, 8, 1, 2, 5, 0, 9]), 'cur_cost': 1547.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:31,127 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1211.00)
2025-08-05 10:28:31,127 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:31,127 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:31,127 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:31,127 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:31,127 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,128 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,128 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,128 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,128 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1133.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:31,128 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [1, 0, 8, 3, 9, 4, 5, 7, 6, 2], 'cur_cost': 1133.0, 'intermediate_solutions': [{'tour': [8, 2, 0, 4, 7, 6, 1, 3, 5, 9], 'cur_cost': 1490.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 2, 3, 6, 7, 4, 1, 0, 5, 9], 'cur_cost': 1162.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 6, 2, 3, 4, 7, 1, 0, 5, 9], 'cur_cost': 1122.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:31,128 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1133.00)
2025-08-05 10:28:31,128 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:31,128 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:31,128 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:31,129 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:31,129 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,129 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,129 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,129 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,129 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1048.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:31,129 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 9, 3, 5, 7, 4, 1, 8, 6, 2], 'cur_cost': 1048.0, 'intermediate_solutions': [{'tour': [1, 5, 4, 0, 7, 6, 2, 3, 8, 9], 'cur_cost': 1130.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 3, 2, 6, 0, 7, 4, 5, 8, 9], 'cur_cost': 1173.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 5, 7, 4, 0, 6, 2, 3, 8, 9], 'cur_cost': 939.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:31,130 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 1048.00)
2025-08-05 10:28:31,130 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:31,130 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:31,130 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:31,130 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:31,130 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,131 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,131 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,131 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,131 - ExplorationExpert - INFO - 探索路径生成完成，成本: 985.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:31,131 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [5, 4, 1, 0, 8, 6, 2, 3, 9, 7], 'cur_cost': 985.0, 'intermediate_solutions': [{'tour': [1, 0, 9, 6, 5, 4, 8, 2, 7, 3], 'cur_cost': 1636.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 0, 3, 6, 2, 8, 4, 5, 7, 9], 'cur_cost': 1197.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 4, 3, 6, 5, 8, 2, 7, 9], 'cur_cost': 1604.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:31,131 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 985.00)
2025-08-05 10:28:31,131 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:28:31,131 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:31,131 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:31,132 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 1495.0
2025-08-05 10:28:31,137 - ExploitationExpert - INFO - res_population_num: 9
2025-08-05 10:28:31,137 - ExploitationExpert - INFO - res_population_costs: [832.0, 832.0, 832, 832.0, 832, 832.0, 832, 832, 832]
2025-08-05 10:28:31,137 - ExploitationExpert - INFO - res_populations: [array([0, 9, 1, 7, 4, 5, 6, 2, 3, 8], dtype=int64), array([0, 8, 3, 6, 2, 5, 4, 7, 1, 9], dtype=int64), array([0, 8, 3, 6, 2, 5, 7, 1, 4, 9], dtype=int64), array([0, 6, 2, 5, 7, 1, 4, 9, 3, 8], dtype=int64), array([0, 3, 6, 2, 5, 7, 1, 4, 9, 8], dtype=int64), array([0, 8, 3, 2, 6, 5, 7, 1, 4, 9], dtype=int64), array([0, 6, 2, 7, 1, 4, 5, 9, 3, 8], dtype=int64), array([0, 3, 6, 2, 7, 1, 4, 5, 9, 8], dtype=int64), array([0, 8, 3, 6, 2, 7, 1, 4, 5, 9], dtype=int64)]
2025-08-05 10:28:31,139 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:31,139 - ExploitationExpert - INFO - populations: [{'tour': [1, 4, 7, 9, 8, 0, 6, 2, 3, 5], 'cur_cost': 936.0}, {'tour': array([4, 7, 9, 6, 5, 0, 3, 8, 2, 1], dtype=int64), 'cur_cost': 1211.0}, {'tour': [1, 0, 8, 3, 9, 4, 5, 7, 6, 2], 'cur_cost': 1133.0}, {'tour': [0, 9, 3, 5, 7, 4, 1, 8, 6, 2], 'cur_cost': 1048.0}, {'tour': [5, 4, 1, 0, 8, 6, 2, 3, 9, 7], 'cur_cost': 985.0}, {'tour': array([0, 3, 5, 8, 9, 2, 7, 4, 6, 1], dtype=int64), 'cur_cost': 1495.0}, {'tour': [4, 0, 3, 6, 5, 1, 7, 2, 8, 9], 'cur_cost': 1171.0}, {'tour': [7, 1, 5, 8, 0, 6, 2, 3, 9, 4], 'cur_cost': 892.0}, {'tour': [8, 6, 9, 0, 7, 1, 3, 2, 4, 5], 'cur_cost': 1369.0}, {'tour': [8, 2, 5, 7, 4, 0, 6, 3, 9, 1], 'cur_cost': 1248.0}]
2025-08-05 10:28:31,140 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:31,140 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 30, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 30, 'cache_hits': 0, 'similarity_calculations': 214, 'cache_hit_rate': 0.0, 'cache_size': 214}}
2025-08-05 10:28:31,140 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([0, 3, 5, 8, 9, 2, 7, 4, 6, 1], dtype=int64), 'cur_cost': 1495.0, 'intermediate_solutions': [{'tour': array([2, 3, 4, 9, 1, 8, 6, 5, 7, 0]), 'cur_cost': 1462.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([9, 2, 3, 4, 1, 8, 6, 5, 7, 0]), 'cur_cost': 1391.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 9, 2, 3, 4, 8, 6, 5, 7, 0]), 'cur_cost': 1558.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 9, 2, 3, 1, 8, 6, 5, 7, 0]), 'cur_cost': 1575.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 1, 9, 2, 3, 8, 6, 5, 7, 0]), 'cur_cost': 1246.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:31,140 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1495.00)
2025-08-05 10:28:31,140 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:31,141 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:31,141 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:31,141 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:31,141 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,141 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,141 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,142 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,142 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1205.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:31,142 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [4, 0, 2, 3, 6, 5, 1, 7, 8, 9], 'cur_cost': 1205.0, 'intermediate_solutions': [{'tour': [4, 0, 3, 6, 5, 1, 2, 7, 8, 9], 'cur_cost': 1405.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 0, 3, 6, 5, 1, 7, 8, 2, 9], 'cur_cost': 1293.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 4, 0, 3, 6, 5, 1, 2, 8, 9], 'cur_cost': 1245.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:31,142 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1205.00)
2025-08-05 10:28:31,142 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:31,142 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:31,142 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:31,143 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:31,143 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,143 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,143 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,143 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,143 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1004.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:31,143 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 6, 2, 3, 5, 7, 4, 1, 8, 9], 'cur_cost': 1004.0, 'intermediate_solutions': [{'tour': [7, 1, 3, 8, 0, 6, 2, 5, 9, 4], 'cur_cost': 1026.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 1, 5, 8, 0, 6, 2, 3, 4, 9], 'cur_cost': 1122.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 9, 1, 5, 8, 0, 6, 2, 3, 4], 'cur_cost': 1137.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:31,144 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 1004.00)
2025-08-05 10:28:31,144 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:31,144 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:31,144 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:31,144 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:31,144 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,144 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,145 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,145 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,145 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1195.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:31,145 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [6, 0, 2, 5, 7, 1, 8, 3, 9, 4], 'cur_cost': 1195.0, 'intermediate_solutions': [{'tour': [4, 6, 9, 0, 7, 1, 3, 2, 8, 5], 'cur_cost': 1378.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 6, 9, 0, 7, 1, 3, 2, 5, 4], 'cur_cost': 1368.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 9, 6, 0, 7, 1, 3, 2, 4, 5], 'cur_cost': 1334.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:31,145 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1195.00)
2025-08-05 10:28:31,145 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:31,145 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:31,145 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:31,146 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:31,146 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,146 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,146 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,146 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,146 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1112.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:31,146 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [6, 0, 5, 1, 8, 3, 9, 4, 7, 2], 'cur_cost': 1112.0, 'intermediate_solutions': [{'tour': [8, 2, 5, 7, 1, 0, 6, 3, 9, 4], 'cur_cost': 1234.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 2, 5, 7, 4, 0, 6, 9, 3, 1], 'cur_cost': 1407.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 2, 5, 7, 4, 3, 0, 6, 9, 1], 'cur_cost': 1340.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:31,147 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1112.00)
2025-08-05 10:28:31,147 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:31,147 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:31,148 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 7, 9, 8, 0, 6, 2, 3, 5], 'cur_cost': 936.0, 'intermediate_solutions': [{'tour': [6, 9, 2, 0, 1, 5, 4, 7, 3, 8], 'cur_cost': 1283.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 9, 2, 3, 8, 0, 7, 4, 5, 1], 'cur_cost': 1183.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 9, 2, 3, 5, 1, 4, 7, 0, 8], 'cur_cost': 1191.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 7, 9, 6, 5, 0, 3, 8, 2, 1], dtype=int64), 'cur_cost': 1211.0, 'intermediate_solutions': [{'tour': array([7, 8, 6, 4, 3, 1, 2, 5, 0, 9]), 'cur_cost': 1729.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 7, 8, 6, 3, 1, 2, 5, 0, 9]), 'cur_cost': 1455.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 4, 7, 8, 6, 1, 2, 5, 0, 9]), 'cur_cost': 1498.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 4, 7, 8, 3, 1, 2, 5, 0, 9]), 'cur_cost': 1510.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 3, 4, 7, 8, 1, 2, 5, 0, 9]), 'cur_cost': 1547.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [1, 0, 8, 3, 9, 4, 5, 7, 6, 2], 'cur_cost': 1133.0, 'intermediate_solutions': [{'tour': [8, 2, 0, 4, 7, 6, 1, 3, 5, 9], 'cur_cost': 1490.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 2, 3, 6, 7, 4, 1, 0, 5, 9], 'cur_cost': 1162.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 6, 2, 3, 4, 7, 1, 0, 5, 9], 'cur_cost': 1122.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 3, 5, 7, 4, 1, 8, 6, 2], 'cur_cost': 1048.0, 'intermediate_solutions': [{'tour': [1, 5, 4, 0, 7, 6, 2, 3, 8, 9], 'cur_cost': 1130.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 3, 2, 6, 0, 7, 4, 5, 8, 9], 'cur_cost': 1173.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 5, 7, 4, 0, 6, 2, 3, 8, 9], 'cur_cost': 939.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [5, 4, 1, 0, 8, 6, 2, 3, 9, 7], 'cur_cost': 985.0, 'intermediate_solutions': [{'tour': [1, 0, 9, 6, 5, 4, 8, 2, 7, 3], 'cur_cost': 1636.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 0, 3, 6, 2, 8, 4, 5, 7, 9], 'cur_cost': 1197.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 4, 3, 6, 5, 8, 2, 7, 9], 'cur_cost': 1604.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 3, 5, 8, 9, 2, 7, 4, 6, 1], dtype=int64), 'cur_cost': 1495.0, 'intermediate_solutions': [{'tour': array([2, 3, 4, 9, 1, 8, 6, 5, 7, 0]), 'cur_cost': 1462.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([9, 2, 3, 4, 1, 8, 6, 5, 7, 0]), 'cur_cost': 1391.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 9, 2, 3, 4, 8, 6, 5, 7, 0]), 'cur_cost': 1558.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 9, 2, 3, 1, 8, 6, 5, 7, 0]), 'cur_cost': 1575.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 1, 9, 2, 3, 8, 6, 5, 7, 0]), 'cur_cost': 1246.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [4, 0, 2, 3, 6, 5, 1, 7, 8, 9], 'cur_cost': 1205.0, 'intermediate_solutions': [{'tour': [4, 0, 3, 6, 5, 1, 2, 7, 8, 9], 'cur_cost': 1405.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 0, 3, 6, 5, 1, 7, 8, 2, 9], 'cur_cost': 1293.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 4, 0, 3, 6, 5, 1, 2, 8, 9], 'cur_cost': 1245.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 2, 3, 5, 7, 4, 1, 8, 9], 'cur_cost': 1004.0, 'intermediate_solutions': [{'tour': [7, 1, 3, 8, 0, 6, 2, 5, 9, 4], 'cur_cost': 1026.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 1, 5, 8, 0, 6, 2, 3, 4, 9], 'cur_cost': 1122.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 9, 1, 5, 8, 0, 6, 2, 3, 4], 'cur_cost': 1137.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [6, 0, 2, 5, 7, 1, 8, 3, 9, 4], 'cur_cost': 1195.0, 'intermediate_solutions': [{'tour': [4, 6, 9, 0, 7, 1, 3, 2, 8, 5], 'cur_cost': 1378.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 6, 9, 0, 7, 1, 3, 2, 5, 4], 'cur_cost': 1368.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 9, 6, 0, 7, 1, 3, 2, 4, 5], 'cur_cost': 1334.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [6, 0, 5, 1, 8, 3, 9, 4, 7, 2], 'cur_cost': 1112.0, 'intermediate_solutions': [{'tour': [8, 2, 5, 7, 1, 0, 6, 3, 9, 4], 'cur_cost': 1234.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 2, 5, 7, 4, 0, 6, 9, 3, 1], 'cur_cost': 1407.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 2, 5, 7, 4, 3, 0, 6, 9, 1], 'cur_cost': 1340.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:31,148 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:31,148 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:31,156 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=936.000, 多样性=0.902
2025-08-05 10:28:31,157 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 10:28:31,157 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 10:28:31,157 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:31,158 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.04489834719503404, 'best_improvement': -0.04932735426008968}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.01754385964912254}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.04522332839508216, 'recent_improvements': [0.04448640679610708, -0.03120368742011402, -0.045960249994057234], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 9, 'new_count': 9, 'count_change': 0, 'old_best_cost': 832.0, 'new_best_cost': 832.0, 'quality_improvement': 0.0, 'old_diversity': 0.7222222222222222, 'new_diversity': 0.7222222222222222, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配']}
2025-08-05 10:28:31,159 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 10:28:31,159 - __main__ - INFO - simple3_10 开始进化第 3 代
2025-08-05 10:28:31,159 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 10:28:31,159 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:31,160 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=936.000, 多样性=0.902
2025-08-05 10:28:31,160 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:31,161 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.902
2025-08-05 10:28:31,161 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:31,163 - EliteExpert - INFO - 精英解分析完成: 精英解数量=9, 多样性=0.722
2025-08-05 10:28:31,166 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 10:28:31,166 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:31,166 - LandscapeExpert - INFO - 添加精英解数据: 9个精英解
2025-08-05 10:28:31,166 - LandscapeExpert - INFO - 数据提取成功: 19个路径, 19个适应度值
2025-08-05 10:28:31,183 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.474, 适应度梯度: -40.884, 聚类评分: 0.000, 覆盖率: 0.015, 收敛趋势: 0.000, 多样性: 0.451
2025-08-05 10:28:31,183 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 10:28:31,183 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:31,183 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple3_10
2025-08-05 10:28:31,189 - visualization.landscape_visualizer - INFO - 插值约束: 150 个点被约束到最小值 832.00
2025-08-05 10:28:31,190 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.9%, 梯度: 19.34 → 17.81
2025-08-05 10:28:31,304 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple3_10\landscape_simple3_10_iter_13_20250805_102831.html
2025-08-05 10:28:31,360 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple3_10\dashboard_simple3_10_iter_13_20250805_102831.html
2025-08-05 10:28:31,361 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 13
2025-08-05 10:28:31,361 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 10:28:31,361 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1947秒
2025-08-05 10:28:31,361 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.47368421052631576, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -40.88421052631579, 'local_optima_density': 0.47368421052631576, 'gradient_variance': 38164.44764542936, 'cluster_count': 0}, 'population_state': {'diversity': 0.4512157586949831, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0145, 'fitness_entropy': 0.8141473388363429, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -40.884)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.015)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360911.1832128, 'performance_metrics': {}}}
2025-08-05 10:28:31,361 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:31,361 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:31,361 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:31,361 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:31,361 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:28:31,362 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 10:28:31,362 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:28:31,362 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:31,362 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:31,362 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:28:31,362 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:31,362 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:31,362 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 4} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:31,363 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:31,363 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:31,363 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:31,363 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:31,363 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,364 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,364 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,364 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,364 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1446.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:31,364 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [5, 7, 9, 6, 8, 2, 3, 1, 0, 4], 'cur_cost': 1446.0, 'intermediate_solutions': [{'tour': [1, 4, 5, 9, 8, 0, 6, 2, 3, 7], 'cur_cost': 907.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 7, 9, 8, 3, 2, 6, 0, 5], 'cur_cost': 896.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 9, 8, 0, 6, 2, 4, 3, 5], 'cur_cost': 1175.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:31,365 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 1446.00)
2025-08-05 10:28:31,365 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 10:28:31,365 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:31,365 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:31,365 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 1327.0
2025-08-05 10:28:31,371 - ExploitationExpert - INFO - res_population_num: 11
2025-08-05 10:28:31,371 - ExploitationExpert - INFO - res_population_costs: [832.0, 832.0, 832, 832.0, 832, 832.0, 832, 832, 832, 832.0, 832]
2025-08-05 10:28:31,371 - ExploitationExpert - INFO - res_populations: [array([0, 9, 1, 7, 4, 5, 6, 2, 3, 8], dtype=int64), array([0, 8, 3, 6, 2, 5, 4, 7, 1, 9], dtype=int64), array([0, 8, 3, 6, 2, 5, 7, 1, 4, 9], dtype=int64), array([0, 6, 2, 5, 7, 1, 4, 9, 3, 8], dtype=int64), array([0, 3, 6, 2, 5, 7, 1, 4, 9, 8], dtype=int64), array([0, 8, 3, 2, 6, 5, 7, 1, 4, 9], dtype=int64), array([0, 6, 2, 7, 1, 4, 5, 9, 3, 8], dtype=int64), array([0, 3, 6, 2, 7, 1, 4, 5, 9, 8], dtype=int64), array([0, 8, 3, 6, 2, 7, 1, 4, 5, 9], dtype=int64), array([0, 8, 9, 1, 7, 4, 5, 6, 2, 3], dtype=int64), array([0, 3, 6, 2, 5, 4, 7, 1, 9, 8], dtype=int64)]
2025-08-05 10:28:31,373 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:31,373 - ExploitationExpert - INFO - populations: [{'tour': [5, 7, 9, 6, 8, 2, 3, 1, 0, 4], 'cur_cost': 1446.0}, {'tour': array([4, 7, 0, 6, 8, 3, 5, 1, 2, 9], dtype=int64), 'cur_cost': 1327.0}, {'tour': [1, 0, 8, 3, 9, 4, 5, 7, 6, 2], 'cur_cost': 1133.0}, {'tour': [0, 9, 3, 5, 7, 4, 1, 8, 6, 2], 'cur_cost': 1048.0}, {'tour': [5, 4, 1, 0, 8, 6, 2, 3, 9, 7], 'cur_cost': 985.0}, {'tour': [0, 3, 5, 8, 9, 2, 7, 4, 6, 1], 'cur_cost': 1495.0}, {'tour': [4, 0, 2, 3, 6, 5, 1, 7, 8, 9], 'cur_cost': 1205.0}, {'tour': [0, 6, 2, 3, 5, 7, 4, 1, 8, 9], 'cur_cost': 1004.0}, {'tour': [6, 0, 2, 5, 7, 1, 8, 3, 9, 4], 'cur_cost': 1195.0}, {'tour': [6, 0, 5, 1, 8, 3, 9, 4, 7, 2], 'cur_cost': 1112.0}]
2025-08-05 10:28:31,374 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:31,374 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 31, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 31, 'cache_hits': 0, 'similarity_calculations': 231, 'cache_hit_rate': 0.0, 'cache_size': 231}}
2025-08-05 10:28:31,374 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([4, 7, 0, 6, 8, 3, 5, 1, 2, 9], dtype=int64), 'cur_cost': 1327.0, 'intermediate_solutions': [{'tour': array([9, 7, 4, 6, 5, 0, 3, 8, 2, 1]), 'cur_cost': 1326.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 9, 7, 4, 5, 0, 3, 8, 2, 1]), 'cur_cost': 1290.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 6, 9, 7, 4, 0, 3, 8, 2, 1]), 'cur_cost': 1291.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 6, 9, 7, 5, 0, 3, 8, 2, 1]), 'cur_cost': 1273.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 5, 6, 9, 7, 0, 3, 8, 2, 1]), 'cur_cost': 1272.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:31,374 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1327.00)
2025-08-05 10:28:31,375 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:31,375 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:31,375 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:31,375 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:31,375 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,375 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,375 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,376 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,376 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1455.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:31,376 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [1, 9, 8, 6, 5, 3, 7, 4, 2, 0], 'cur_cost': 1455.0, 'intermediate_solutions': [{'tour': [2, 0, 8, 3, 9, 4, 5, 7, 6, 1], 'cur_cost': 1224.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 0, 8, 3, 9, 4, 2, 6, 7, 5], 'cur_cost': 1135.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 8, 3, 4, 9, 5, 7, 6, 2], 'cur_cost': 1309.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:31,376 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1455.00)
2025-08-05 10:28:31,376 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:31,376 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:31,376 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:31,377 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:31,377 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,377 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,377 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,377 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,377 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1240.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:31,377 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [4, 0, 3, 2, 5, 1, 6, 8, 9, 7], 'cur_cost': 1240.0, 'intermediate_solutions': [{'tour': [0, 5, 3, 9, 7, 4, 1, 8, 6, 2], 'cur_cost': 1171.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 3, 5, 7, 4, 1, 8, 2, 6], 'cur_cost': 1046.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 3, 7, 4, 1, 8, 6, 2, 5], 'cur_cost': 1185.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:31,377 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1240.00)
2025-08-05 10:28:31,377 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:31,377 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:31,378 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:31,378 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:31,378 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,378 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,378 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,378 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,378 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1087.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:31,379 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [1, 4, 9, 2, 6, 5, 3, 8, 0, 7], 'cur_cost': 1087.0, 'intermediate_solutions': [{'tour': [9, 4, 1, 0, 8, 6, 2, 3, 5, 7], 'cur_cost': 1136.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 5, 1, 0, 8, 6, 2, 3, 9, 7], 'cur_cost': 1004.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 5, 4, 1, 0, 8, 2, 3, 9, 7], 'cur_cost': 1204.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:31,379 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 1087.00)
2025-08-05 10:28:31,379 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:28:31,379 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:31,379 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:31,379 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 1164.0
2025-08-05 10:28:31,385 - ExploitationExpert - INFO - res_population_num: 11
2025-08-05 10:28:31,385 - ExploitationExpert - INFO - res_population_costs: [832.0, 832.0, 832, 832.0, 832, 832.0, 832, 832, 832, 832.0, 832]
2025-08-05 10:28:31,385 - ExploitationExpert - INFO - res_populations: [array([0, 9, 1, 7, 4, 5, 6, 2, 3, 8], dtype=int64), array([0, 8, 3, 6, 2, 5, 4, 7, 1, 9], dtype=int64), array([0, 8, 3, 6, 2, 5, 7, 1, 4, 9], dtype=int64), array([0, 6, 2, 5, 7, 1, 4, 9, 3, 8], dtype=int64), array([0, 3, 6, 2, 5, 7, 1, 4, 9, 8], dtype=int64), array([0, 8, 3, 2, 6, 5, 7, 1, 4, 9], dtype=int64), array([0, 6, 2, 7, 1, 4, 5, 9, 3, 8], dtype=int64), array([0, 3, 6, 2, 7, 1, 4, 5, 9, 8], dtype=int64), array([0, 8, 3, 6, 2, 7, 1, 4, 5, 9], dtype=int64), array([0, 8, 9, 1, 7, 4, 5, 6, 2, 3], dtype=int64), array([0, 3, 6, 2, 5, 4, 7, 1, 9, 8], dtype=int64)]
2025-08-05 10:28:31,387 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:31,387 - ExploitationExpert - INFO - populations: [{'tour': [5, 7, 9, 6, 8, 2, 3, 1, 0, 4], 'cur_cost': 1446.0}, {'tour': array([4, 7, 0, 6, 8, 3, 5, 1, 2, 9], dtype=int64), 'cur_cost': 1327.0}, {'tour': [1, 9, 8, 6, 5, 3, 7, 4, 2, 0], 'cur_cost': 1455.0}, {'tour': [4, 0, 3, 2, 5, 1, 6, 8, 9, 7], 'cur_cost': 1240.0}, {'tour': [1, 4, 9, 2, 6, 5, 3, 8, 0, 7], 'cur_cost': 1087.0}, {'tour': array([0, 6, 5, 1, 7, 9, 4, 2, 8, 3], dtype=int64), 'cur_cost': 1164.0}, {'tour': [4, 0, 2, 3, 6, 5, 1, 7, 8, 9], 'cur_cost': 1205.0}, {'tour': [0, 6, 2, 3, 5, 7, 4, 1, 8, 9], 'cur_cost': 1004.0}, {'tour': [6, 0, 2, 5, 7, 1, 8, 3, 9, 4], 'cur_cost': 1195.0}, {'tour': [6, 0, 5, 1, 8, 3, 9, 4, 7, 2], 'cur_cost': 1112.0}]
2025-08-05 10:28:31,388 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:31,388 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 32, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 32, 'cache_hits': 0, 'similarity_calculations': 249, 'cache_hit_rate': 0.0, 'cache_size': 249}}
2025-08-05 10:28:31,388 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([0, 6, 5, 1, 7, 9, 4, 2, 8, 3], dtype=int64), 'cur_cost': 1164.0, 'intermediate_solutions': [{'tour': array([5, 3, 0, 8, 9, 2, 7, 4, 6, 1]), 'cur_cost': 1301.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 5, 3, 0, 9, 2, 7, 4, 6, 1]), 'cur_cost': 1527.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 8, 5, 3, 0, 2, 7, 4, 6, 1]), 'cur_cost': 1382.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 8, 5, 3, 9, 2, 7, 4, 6, 1]), 'cur_cost': 1489.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 9, 8, 5, 3, 2, 7, 4, 6, 1]), 'cur_cost': 1440.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:31,388 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1164.00)
2025-08-05 10:28:31,388 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:28:31,389 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:31,389 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:31,389 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1627.0
2025-08-05 10:28:31,398 - ExploitationExpert - INFO - res_population_num: 13
2025-08-05 10:28:31,398 - ExploitationExpert - INFO - res_population_costs: [832.0, 832.0, 832, 832.0, 832, 832.0, 832, 832, 832, 832.0, 832, 832, 832]
2025-08-05 10:28:31,399 - ExploitationExpert - INFO - res_populations: [array([0, 9, 1, 7, 4, 5, 6, 2, 3, 8], dtype=int64), array([0, 8, 3, 6, 2, 5, 4, 7, 1, 9], dtype=int64), array([0, 8, 3, 6, 2, 5, 7, 1, 4, 9], dtype=int64), array([0, 6, 2, 5, 7, 1, 4, 9, 3, 8], dtype=int64), array([0, 3, 6, 2, 5, 7, 1, 4, 9, 8], dtype=int64), array([0, 8, 3, 2, 6, 5, 7, 1, 4, 9], dtype=int64), array([0, 6, 2, 7, 1, 4, 5, 9, 3, 8], dtype=int64), array([0, 3, 6, 2, 7, 1, 4, 5, 9, 8], dtype=int64), array([0, 8, 3, 6, 2, 7, 1, 4, 5, 9], dtype=int64), array([0, 8, 9, 1, 7, 4, 5, 6, 2, 3], dtype=int64), array([0, 3, 6, 2, 5, 4, 7, 1, 9, 8], dtype=int64), array([0, 8, 9, 4, 1, 7, 5, 6, 2, 3], dtype=int64), array([0, 6, 2, 5, 4, 7, 1, 9, 3, 8], dtype=int64)]
2025-08-05 10:28:31,401 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:31,401 - ExploitationExpert - INFO - populations: [{'tour': [5, 7, 9, 6, 8, 2, 3, 1, 0, 4], 'cur_cost': 1446.0}, {'tour': array([4, 7, 0, 6, 8, 3, 5, 1, 2, 9], dtype=int64), 'cur_cost': 1327.0}, {'tour': [1, 9, 8, 6, 5, 3, 7, 4, 2, 0], 'cur_cost': 1455.0}, {'tour': [4, 0, 3, 2, 5, 1, 6, 8, 9, 7], 'cur_cost': 1240.0}, {'tour': [1, 4, 9, 2, 6, 5, 3, 8, 0, 7], 'cur_cost': 1087.0}, {'tour': array([0, 6, 5, 1, 7, 9, 4, 2, 8, 3], dtype=int64), 'cur_cost': 1164.0}, {'tour': array([9, 8, 4, 2, 7, 6, 1, 0, 5, 3], dtype=int64), 'cur_cost': 1627.0}, {'tour': [0, 6, 2, 3, 5, 7, 4, 1, 8, 9], 'cur_cost': 1004.0}, {'tour': [6, 0, 2, 5, 7, 1, 8, 3, 9, 4], 'cur_cost': 1195.0}, {'tour': [6, 0, 5, 1, 8, 3, 9, 4, 7, 2], 'cur_cost': 1112.0}]
2025-08-05 10:28:31,402 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:31,402 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 33, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 33, 'cache_hits': 0, 'similarity_calculations': 268, 'cache_hit_rate': 0.0, 'cache_size': 268}}
2025-08-05 10:28:31,402 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([9, 8, 4, 2, 7, 6, 1, 0, 5, 3], dtype=int64), 'cur_cost': 1627.0, 'intermediate_solutions': [{'tour': array([2, 0, 4, 3, 6, 5, 1, 7, 8, 9]), 'cur_cost': 1355.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 2, 0, 4, 6, 5, 1, 7, 8, 9]), 'cur_cost': 1249.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 3, 2, 0, 4, 5, 1, 7, 8, 9]), 'cur_cost': 1152.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 3, 2, 0, 6, 5, 1, 7, 8, 9]), 'cur_cost': 1251.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 6, 3, 2, 0, 5, 1, 7, 8, 9]), 'cur_cost': 1206.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:31,402 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1627.00)
2025-08-05 10:28:31,402 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:31,402 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:31,402 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:31,403 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:31,403 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,403 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,403 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,403 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,404 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1166.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:31,404 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [3, 2, 8, 6, 4, 7, 5, 1, 0, 9], 'cur_cost': 1166.0, 'intermediate_solutions': [{'tour': [0, 2, 6, 3, 5, 7, 4, 1, 8, 9], 'cur_cost': 1013.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 6, 3, 5, 7, 4, 1, 8, 9], 'cur_cost': 1013.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 6, 2, 3, 5, 7, 1, 8, 9], 'cur_cost': 1199.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:31,404 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1166.00)
2025-08-05 10:28:31,404 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:31,404 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:31,404 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:31,405 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:31,405 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,405 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,405 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,405 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,405 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1165.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:31,405 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 8, 1, 9, 3, 7, 4, 5, 6, 2], 'cur_cost': 1165.0, 'intermediate_solutions': [{'tour': [6, 2, 0, 5, 7, 1, 8, 3, 9, 4], 'cur_cost': 1113.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 0, 2, 5, 7, 9, 3, 8, 1, 4], 'cur_cost': 1228.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 2, 5, 7, 1, 8, 3, 9, 4], 'cur_cost': 1103.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:31,406 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1165.00)
2025-08-05 10:28:31,406 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:31,406 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:31,406 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:31,406 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:31,406 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,407 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,407 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,407 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,407 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1107.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:31,407 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [2, 8, 6, 5, 4, 1, 7, 0, 3, 9], 'cur_cost': 1107.0, 'intermediate_solutions': [{'tour': [0, 6, 5, 1, 8, 3, 9, 4, 7, 2], 'cur_cost': 1203.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 0, 8, 1, 5, 3, 9, 4, 7, 2], 'cur_cost': 1152.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 7, 0, 5, 1, 8, 3, 9, 4, 2], 'cur_cost': 1327.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:31,407 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1107.00)
2025-08-05 10:28:31,407 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:31,407 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:31,408 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [5, 7, 9, 6, 8, 2, 3, 1, 0, 4], 'cur_cost': 1446.0, 'intermediate_solutions': [{'tour': [1, 4, 5, 9, 8, 0, 6, 2, 3, 7], 'cur_cost': 907.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 7, 9, 8, 3, 2, 6, 0, 5], 'cur_cost': 896.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 9, 8, 0, 6, 2, 4, 3, 5], 'cur_cost': 1175.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 7, 0, 6, 8, 3, 5, 1, 2, 9], dtype=int64), 'cur_cost': 1327.0, 'intermediate_solutions': [{'tour': array([9, 7, 4, 6, 5, 0, 3, 8, 2, 1]), 'cur_cost': 1326.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 9, 7, 4, 5, 0, 3, 8, 2, 1]), 'cur_cost': 1290.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 6, 9, 7, 4, 0, 3, 8, 2, 1]), 'cur_cost': 1291.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 6, 9, 7, 5, 0, 3, 8, 2, 1]), 'cur_cost': 1273.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 5, 6, 9, 7, 0, 3, 8, 2, 1]), 'cur_cost': 1272.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [1, 9, 8, 6, 5, 3, 7, 4, 2, 0], 'cur_cost': 1455.0, 'intermediate_solutions': [{'tour': [2, 0, 8, 3, 9, 4, 5, 7, 6, 1], 'cur_cost': 1224.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 0, 8, 3, 9, 4, 2, 6, 7, 5], 'cur_cost': 1135.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 8, 3, 4, 9, 5, 7, 6, 2], 'cur_cost': 1309.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [4, 0, 3, 2, 5, 1, 6, 8, 9, 7], 'cur_cost': 1240.0, 'intermediate_solutions': [{'tour': [0, 5, 3, 9, 7, 4, 1, 8, 6, 2], 'cur_cost': 1171.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 3, 5, 7, 4, 1, 8, 2, 6], 'cur_cost': 1046.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 3, 7, 4, 1, 8, 6, 2, 5], 'cur_cost': 1185.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 9, 2, 6, 5, 3, 8, 0, 7], 'cur_cost': 1087.0, 'intermediate_solutions': [{'tour': [9, 4, 1, 0, 8, 6, 2, 3, 5, 7], 'cur_cost': 1136.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 5, 1, 0, 8, 6, 2, 3, 9, 7], 'cur_cost': 1004.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 5, 4, 1, 0, 8, 2, 3, 9, 7], 'cur_cost': 1204.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 6, 5, 1, 7, 9, 4, 2, 8, 3], dtype=int64), 'cur_cost': 1164.0, 'intermediate_solutions': [{'tour': array([5, 3, 0, 8, 9, 2, 7, 4, 6, 1]), 'cur_cost': 1301.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 5, 3, 0, 9, 2, 7, 4, 6, 1]), 'cur_cost': 1527.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 8, 5, 3, 0, 2, 7, 4, 6, 1]), 'cur_cost': 1382.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 8, 5, 3, 9, 2, 7, 4, 6, 1]), 'cur_cost': 1489.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 9, 8, 5, 3, 2, 7, 4, 6, 1]), 'cur_cost': 1440.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([9, 8, 4, 2, 7, 6, 1, 0, 5, 3], dtype=int64), 'cur_cost': 1627.0, 'intermediate_solutions': [{'tour': array([2, 0, 4, 3, 6, 5, 1, 7, 8, 9]), 'cur_cost': 1355.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 2, 0, 4, 6, 5, 1, 7, 8, 9]), 'cur_cost': 1249.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 3, 2, 0, 4, 5, 1, 7, 8, 9]), 'cur_cost': 1152.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 3, 2, 0, 6, 5, 1, 7, 8, 9]), 'cur_cost': 1251.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 6, 3, 2, 0, 5, 1, 7, 8, 9]), 'cur_cost': 1206.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [3, 2, 8, 6, 4, 7, 5, 1, 0, 9], 'cur_cost': 1166.0, 'intermediate_solutions': [{'tour': [0, 2, 6, 3, 5, 7, 4, 1, 8, 9], 'cur_cost': 1013.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 6, 3, 5, 7, 4, 1, 8, 9], 'cur_cost': 1013.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 6, 2, 3, 5, 7, 1, 8, 9], 'cur_cost': 1199.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 1, 9, 3, 7, 4, 5, 6, 2], 'cur_cost': 1165.0, 'intermediate_solutions': [{'tour': [6, 2, 0, 5, 7, 1, 8, 3, 9, 4], 'cur_cost': 1113.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 0, 2, 5, 7, 9, 3, 8, 1, 4], 'cur_cost': 1228.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 2, 5, 7, 1, 8, 3, 9, 4], 'cur_cost': 1103.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [2, 8, 6, 5, 4, 1, 7, 0, 3, 9], 'cur_cost': 1107.0, 'intermediate_solutions': [{'tour': [0, 6, 5, 1, 8, 3, 9, 4, 7, 2], 'cur_cost': 1203.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 0, 8, 1, 5, 3, 9, 4, 7, 2], 'cur_cost': 1152.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 7, 0, 5, 1, 8, 3, 9, 4, 2], 'cur_cost': 1327.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:31,409 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:31,409 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:31,410 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1087.000, 多样性=0.904
2025-08-05 10:28:31,410 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 10:28:31,410 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 10:28:31,410 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:31,411 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.1261829358411454, 'best_improvement': -0.16132478632478633}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.002463054187192134}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.03805101730757403, 'recent_improvements': [-0.03120368742011402, -0.045960249994057234, 0.04489834719503404], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 13, 'new_count': 13, 'count_change': 0, 'old_best_cost': 832.0, 'new_best_cost': 832.0, 'quality_improvement': 0.0, 'old_diversity': 0.7346153846153846, 'new_diversity': 0.7346153846153846, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:31,413 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 10:28:31,413 - __main__ - INFO - simple3_10 开始进化第 4 代
2025-08-05 10:28:31,413 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 10:28:31,413 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:31,414 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1087.000, 多样性=0.904
2025-08-05 10:28:31,414 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:31,414 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.904
2025-08-05 10:28:31,415 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:31,416 - EliteExpert - INFO - 精英解分析完成: 精英解数量=13, 多样性=0.735
2025-08-05 10:28:31,418 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 10:28:31,418 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:31,418 - LandscapeExpert - INFO - 添加精英解数据: 13个精英解
2025-08-05 10:28:31,418 - LandscapeExpert - INFO - 数据提取成功: 23个路径, 23个适应度值
2025-08-05 10:28:31,440 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.565, 适应度梯度: -97.322, 聚类评分: 0.000, 覆盖率: 0.016, 收敛趋势: 0.000, 多样性: 0.361
2025-08-05 10:28:31,441 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 10:28:31,441 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:31,441 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple3_10
2025-08-05 10:28:31,449 - visualization.landscape_visualizer - INFO - 插值约束: 207 个点被约束到最小值 832.00
2025-08-05 10:28:31,451 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=8.8%, 梯度: 34.18 → 31.17
2025-08-05 10:28:31,582 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple3_10\landscape_simple3_10_iter_14_20250805_102831.html
2025-08-05 10:28:31,652 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple3_10\dashboard_simple3_10_iter_14_20250805_102831.html
2025-08-05 10:28:31,652 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 14
2025-08-05 10:28:31,652 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 10:28:31,652 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2349秒
2025-08-05 10:28:31,652 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5652173913043478, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -97.32173913043479, 'local_optima_density': 0.5652173913043478, 'gradient_variance': 55936.24604914935, 'cluster_count': 0}, 'population_state': {'diversity': 0.3607148994672624, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0159, 'fitness_entropy': 0.7307098126673764, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -97.322)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.016)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360911.4418228, 'performance_metrics': {}}}
2025-08-05 10:28:31,652 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:31,652 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:31,652 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:31,652 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:31,653 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:28:31,653 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 10:28:31,653 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:28:31,653 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:31,653 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:31,653 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:28:31,653 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:31,653 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:31,654 - experts.management.collaboration_manager - INFO - 识别精英个体: {9, 4} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:31,654 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-05 10:28:31,654 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:31,654 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:31,654 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 1163.0
2025-08-05 10:28:31,662 - ExploitationExpert - INFO - res_population_num: 13
2025-08-05 10:28:31,662 - ExploitationExpert - INFO - res_population_costs: [832.0, 832.0, 832, 832.0, 832, 832.0, 832, 832, 832, 832.0, 832, 832, 832]
2025-08-05 10:28:31,662 - ExploitationExpert - INFO - res_populations: [array([0, 9, 1, 7, 4, 5, 6, 2, 3, 8], dtype=int64), array([0, 8, 3, 6, 2, 5, 4, 7, 1, 9], dtype=int64), array([0, 8, 3, 6, 2, 5, 7, 1, 4, 9], dtype=int64), array([0, 6, 2, 5, 7, 1, 4, 9, 3, 8], dtype=int64), array([0, 3, 6, 2, 5, 7, 1, 4, 9, 8], dtype=int64), array([0, 8, 3, 2, 6, 5, 7, 1, 4, 9], dtype=int64), array([0, 6, 2, 7, 1, 4, 5, 9, 3, 8], dtype=int64), array([0, 3, 6, 2, 7, 1, 4, 5, 9, 8], dtype=int64), array([0, 8, 3, 6, 2, 7, 1, 4, 5, 9], dtype=int64), array([0, 8, 9, 1, 7, 4, 5, 6, 2, 3], dtype=int64), array([0, 3, 6, 2, 5, 4, 7, 1, 9, 8], dtype=int64), array([0, 8, 9, 4, 1, 7, 5, 6, 2, 3], dtype=int64), array([0, 6, 2, 5, 4, 7, 1, 9, 3, 8], dtype=int64)]
2025-08-05 10:28:31,664 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:31,664 - ExploitationExpert - INFO - populations: [{'tour': array([1, 9, 3, 0, 2, 6, 4, 7, 5, 8], dtype=int64), 'cur_cost': 1163.0}, {'tour': [4, 7, 0, 6, 8, 3, 5, 1, 2, 9], 'cur_cost': 1327.0}, {'tour': [1, 9, 8, 6, 5, 3, 7, 4, 2, 0], 'cur_cost': 1455.0}, {'tour': [4, 0, 3, 2, 5, 1, 6, 8, 9, 7], 'cur_cost': 1240.0}, {'tour': [1, 4, 9, 2, 6, 5, 3, 8, 0, 7], 'cur_cost': 1087.0}, {'tour': [0, 6, 5, 1, 7, 9, 4, 2, 8, 3], 'cur_cost': 1164.0}, {'tour': [9, 8, 4, 2, 7, 6, 1, 0, 5, 3], 'cur_cost': 1627.0}, {'tour': [3, 2, 8, 6, 4, 7, 5, 1, 0, 9], 'cur_cost': 1166.0}, {'tour': [0, 8, 1, 9, 3, 7, 4, 5, 6, 2], 'cur_cost': 1165.0}, {'tour': [2, 8, 6, 5, 4, 1, 7, 0, 3, 9], 'cur_cost': 1107.0}]
2025-08-05 10:28:31,665 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:31,665 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 34, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 34, 'cache_hits': 0, 'similarity_calculations': 288, 'cache_hit_rate': 0.0, 'cache_size': 288}}
2025-08-05 10:28:31,665 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([1, 9, 3, 0, 2, 6, 4, 7, 5, 8], dtype=int64), 'cur_cost': 1163.0, 'intermediate_solutions': [{'tour': array([9, 7, 5, 6, 8, 2, 3, 1, 0, 4]), 'cur_cost': 1499.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 9, 7, 5, 8, 2, 3, 1, 0, 4]), 'cur_cost': 1595.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 6, 9, 7, 5, 2, 3, 1, 0, 4]), 'cur_cost': 1585.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 6, 9, 7, 8, 2, 3, 1, 0, 4]), 'cur_cost': 1595.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 8, 6, 9, 7, 2, 3, 1, 0, 4]), 'cur_cost': 1562.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:31,665 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1163.00)
2025-08-05 10:28:31,665 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:31,665 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:31,666 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:31,666 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:31,666 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,666 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,666 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,666 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:31,666 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1327.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:31,667 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [9, 0, 4, 1, 5, 6, 8, 3, 7, 2], 'cur_cost': 1327.0, 'intermediate_solutions': [{'tour': [7, 4, 0, 6, 8, 3, 5, 1, 2, 9], 'cur_cost': 1345.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 7, 0, 6, 8, 9, 2, 1, 5, 3], 'cur_cost': 1422.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 1, 7, 0, 6, 8, 3, 5, 2, 9], 'cur_cost': 1216.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:31,667 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1327.00)
2025-08-05 10:28:31,667 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-05 10:28:31,667 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:31,667 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:31,667 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 1559.0
