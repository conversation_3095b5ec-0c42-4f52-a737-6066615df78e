2025-08-03 17:05:42,224 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-08-03 17:05:42,225 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-03 17:05:42,228 - StatsExpert - INFO - 开始统计分析
2025-08-03 17:05:42,239 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=9890.000, 多样性=0.966
2025-08-03 17:05:42,244 - PathExpert - INFO - 开始路径结构分析
2025-08-03 17:05:42,255 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.966
2025-08-03 17:05:42,258 - EliteExpert - INFO - 开始精英解分析
2025-08-03 17:05:42,262 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/1)
2025-08-03 17:05:42,262 - LandscapeExpert - INFO - 使用直接传入的种群数据: 20个个体
2025-08-03 17:05:42,263 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-03 17:05:42,263 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-03 17:05:42,527 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.150, 适应度梯度: -2554.090, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 1.000
2025-08-03 17:05:42,528 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-03 17:05:42,528 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-03 17:05:42,597 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记
2025-08-03 17:05:42,907 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_1_20250803_170542.html
2025-08-03 17:05:42,959 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_1_20250803_170542.html
2025-08-03 17:05:42,960 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-03 17:05:42,960 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-03 17:05:42,961 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.6994秒
2025-08-03 17:05:42,961 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-03 17:05:42,962 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.15, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -2554.0899999999983, 'local_optima_density': 0.15, 'gradient_variance': 2040929114.8139005, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.002, 'fitness_entropy': 0.9232196723355077, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -2554.090)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 1, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754211942.5284662, 'performance_metrics': {}}}
2025-08-03 17:05:42,962 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-03 17:05:42,962 - experts.strategy.enhanced_strategy_expert - INFO - 开始增强策略分配分析 (迭代 0)
2025-08-03 17:05:42,963 - experts.analysis.individual_state_analyzer - INFO - 开始分析种群状态 (迭代 0, 种群大小: 20)
2025-08-03 17:05:42,963 - experts.strategy.enhanced_strategy_expert - ERROR - 策略分配过程中发生错误: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 17:05:42,963 - experts.strategy.enhanced_strategy_expert - WARNING - 使用回退策略分配
2025-08-03 17:05:42,963 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 17:05:42,964 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 17:05:42,964 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-03 17:05:42,964 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 17:05:42,964 - __main__ - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 17:05:42,965 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-03 17:05:42,965 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 2, 4, 5} (总数: 4, 保护比例: 0.20)
2025-08-03 17:05:42,965 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-03 17:05:42,965 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-03 17:05:42,966 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:42,970 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 17:05:42,970 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:43,139 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12368.0, 路径长度: 66
2025-08-03 17:05:43,139 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 5, 2, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12368.0}
2025-08-03 17:05:43,140 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 12368.00)
2025-08-03 17:05:43,140 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-03 17:05:43,141 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:05:43,144 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:05:43,146 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 95662.0
2025-08-03 17:05:45,197 - ExploitationExpert - INFO - res_population_num: 1
2025-08-03 17:05:45,198 - ExploitationExpert - INFO - res_population_costs: [9857.0]
2025-08-03 17:05:45,198 - ExploitationExpert - INFO - res_populations: [array([ 0,  1, 11,  9,  3,  7, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14, 63, 52, 65, 54, 57,
       64, 60, 58, 56, 59, 62, 53, 61, 55,  5,  4,  6,  2,  8, 10],
      dtype=int64)]
2025-08-03 17:05:45,199 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:05:45,199 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 2, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12368.0}, {'tour': array([48, 22, 15, 13, 56, 27, 41, 53, 10, 16, 59, 45, 31, 30,  8, 26, 36,
       43, 58, 17, 29, 25, 52, 18, 63, 65, 62,  2, 46, 51,  5, 64,  3, 37,
        4,  6, 33, 21, 61, 44, 40, 32, 39, 28,  7, 11, 55, 50, 14, 34, 57,
       24, 35, 54, 60,  9,  0, 38, 49,  1, 23, 42, 47, 12, 19, 20],
      dtype=int64), 'cur_cost': 95662.0}, {'tour': array([31, 25, 26, 36, 37, 27, 24, 29, 32, 28, 30, 35, 34, 33, 18, 16, 23,
       22, 12, 17, 15, 14, 20, 21, 13, 19,  9, 11,  7,  3,  1,  0, 10,  8,
        2,  6,  4,  5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9963.0}, {'tour': array([13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36,
       35, 28, 30, 34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,
        2,  6, 10,  0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9984.0}, {'tour': array([18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36,
       35, 28, 30, 34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,
        2,  6, 10,  0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9964.0}, {'tour': array([30, 28, 33, 25, 26, 36, 37, 31, 24, 29, 32, 35, 34, 27, 18, 16, 23,
       22, 12, 17, 15, 14, 20, 21, 13, 19,  9, 11,  7,  3,  1,  0, 10,  8,
        2,  6,  4,  5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9890.0}, {'tour': array([ 9, 38, 21,  6,  5, 39, 18, 40,  2, 58, 24, 12, 35,  8, 25, 45, 47,
       13, 53, 23, 46,  7, 37,  3, 16, 56, 52, 29, 33, 30, 11, 17, 54,  0,
       57, 62, 60, 27,  4, 20, 19, 50, 31, 36, 34, 14, 26, 64, 43, 59, 41,
       51,  1, 15, 28, 49, 55, 22, 48, 32, 10, 44, 63, 65, 61, 42],
      dtype=int64), 'cur_cost': 104081.0}, {'tour': array([39, 19,  9, 17, 25,  3, 38, 14, 35, 54, 44, 58, 56, 46, 41, 26, 49,
       10, 24,  7, 45,  2, 16, 55, 34,  1, 36, 13,  4, 52,  5, 51, 65, 23,
       60, 37, 27, 43, 59, 22, 20, 11,  0, 33, 63,  6, 18, 30, 61, 42, 62,
       31, 32, 21, 64, 15, 12, 28, 50, 40, 53, 57, 48, 29, 47,  8],
      dtype=int64), 'cur_cost': 117932.0}, {'tour': array([40, 21,  4,  7, 13, 58, 54, 31, 18, 59, 19, 27, 23, 47, 53, 32, 57,
       46, 63, 36, 55,  0,  9, 15, 48,  2, 61, 44, 50, 65, 38, 41, 29, 49,
        8, 43, 24, 12,  3, 34, 22, 39, 60, 10, 30,  6, 17, 28, 16, 64, 11,
       33,  5, 37,  1, 51, 52, 26, 35, 56, 42, 62, 14, 45, 20, 25],
      dtype=int64), 'cur_cost': 119526.0}, {'tour': array([ 2, 62, 33, 51, 52, 41, 26, 53,  7,  5, 13, 29, 19, 32, 60, 57, 39,
       21, 12, 50, 64,  9, 59, 54, 14,  4,  3, 22, 23, 16, 48, 37,  8, 42,
       10, 61, 44,  6, 38, 11, 63, 18, 15, 40, 47, 35, 24, 34, 56, 30, 58,
       31,  1, 65, 17,  0, 49, 27, 20, 25, 46, 45, 55, 36, 28, 43],
      dtype=int64), 'cur_cost': 107231.0}, {'tour': array([52, 23, 45, 34,  1, 30, 37, 24, 43,  5, 40,  4, 55, 28, 32, 38, 61,
        2, 49, 11, 53, 15, 54, 18,  3, 51,  9,  8, 13, 47, 22, 35, 41, 17,
        7, 26, 62, 42, 44, 14,  0, 39, 10, 60, 48, 46, 58, 12, 20, 64, 63,
       25, 33, 65, 29, 56, 59, 21, 31,  6, 50, 27, 36, 57, 16, 19],
      dtype=int64), 'cur_cost': 112138.0}, {'tour': array([21, 55, 37, 36, 38, 58, 24, 20, 32, 63,  1, 57, 47,  4, 35, 22, 41,
       40,  5, 11, 16, 60,  2, 28, 19, 61, 64, 52, 42, 49, 30, 44, 46, 14,
       25, 29, 10,  8, 15,  7, 17, 51, 50, 45, 13, 26, 53, 59, 65,  6, 43,
       31, 23, 48,  9, 56, 12, 34, 33, 27,  3,  0, 62, 39, 54, 18],
      dtype=int64), 'cur_cost': 90623.0}, {'tour': array([14,  2, 54, 56, 25, 65, 64,  3, 28, 53, 52, 45, 18, 20, 21, 26, 62,
       47, 39,  4, 40, 24, 17, 11, 41,  6,  5, 32, 22, 61, 51, 43, 31, 50,
       60,  7, 49, 30, 46, 36,  0, 15, 48,  9, 13, 29,  8, 19, 27, 55, 44,
       38,  1, 12, 33, 34, 35, 37, 23, 42, 57, 58, 63, 16, 10, 59],
      dtype=int64), 'cur_cost': 101797.0}, {'tour': array([47, 32, 50, 45, 51, 15,  8, 61, 20, 36, 37, 22, 34,  9, 54, 49, 60,
       48, 18, 14, 57, 10, 24,  2, 53, 42, 40, 27,  6, 56, 12,  4,  7, 26,
       44, 11, 62, 16, 63, 59, 52,  0, 13, 46, 28,  3, 58, 39, 29, 55,  1,
        5, 17, 30, 65, 21, 25, 64, 33, 38, 35, 19, 41, 23, 43, 31],
      dtype=int64), 'cur_cost': 103575.0}, {'tour': array([63, 21, 60, 12, 13, 29, 39, 48,  2, 15, 38, 54, 11, 35, 20, 47, 27,
       44, 32,  3, 59,  1, 16, 46, 58, 50, 40, 57,  0, 43, 22, 23, 61,  9,
       64,  7, 34, 65, 36, 41,  6, 30, 28, 31, 19,  5, 25, 42, 10, 33,  4,
       53, 62, 17, 37, 51, 55, 14, 49, 18, 24, 52, 45, 56, 26,  8],
      dtype=int64), 'cur_cost': 112010.0}, {'tour': array([16, 55, 47, 65,  2, 58, 25, 53, 48, 52,  6, 57, 17, 59, 61, 20, 11,
        3, 50, 22, 33, 28, 15, 31, 54,  0, 39, 36,  5, 43, 30, 37, 46, 64,
       60, 27,  8,  1, 45, 51, 14, 26, 24, 38, 19, 32, 49, 13,  4, 21, 44,
       34, 62, 18, 23,  9, 63, 12, 41, 56, 29, 35,  7, 10, 42, 40],
      dtype=int64), 'cur_cost': 105108.0}, {'tour': array([18, 33, 23,  8, 26, 42, 30, 53, 56, 60, 43, 38, 58, 19, 45, 47, 65,
       10, 20, 49, 28, 15,  3, 24, 31, 14, 32,  2, 29, 11,  6,  1, 36,  5,
       55, 17, 16, 13, 41, 27, 12, 35, 57, 48, 25,  0, 59, 44, 51, 46, 50,
       22,  9, 21, 61, 54, 39,  7, 37, 34, 64, 63, 62, 52,  4, 40],
      dtype=int64), 'cur_cost': 91604.0}, {'tour': array([50,  5, 11, 55, 13, 64, 59, 12, 42, 25, 49, 61, 24, 53, 38, 58, 60,
       44, 30, 33, 15, 29,  7,  8,  0, 20, 18, 62, 54, 10, 28, 41, 47, 23,
       32, 19, 26,  6,  9, 14, 31, 65,  4, 37, 48, 51,  3, 63, 39, 16, 34,
       40, 27, 57,  1, 45,  2, 43, 36, 17, 22, 35, 46, 52, 21, 56],
      dtype=int64), 'cur_cost': 106037.0}, {'tour': array([57,  7, 63, 41,  1, 64, 36,  8, 56, 55, 10, 43, 34, 62, 30, 61,  4,
       22, 14, 52, 32, 19, 29, 49, 28, 25, 15, 54, 13, 18,  2, 20,  6, 16,
       48, 33, 17, 27, 37, 44, 31, 35, 60, 24, 38, 65,  9, 53, 47, 21, 45,
       12, 39,  3, 23, 51, 50, 46, 59, 58, 40, 11,  5,  0, 42, 26],
      dtype=int64), 'cur_cost': 105781.0}, {'tour': array([37, 36,  7, 23, 27, 13, 63, 60, 52, 62, 43, 58, 22, 35, 20, 54, 55,
       16, 48, 30, 15,  8, 24, 61, 12, 42, 31, 40,  1, 45,  2, 18, 14,  9,
       19, 53,  0, 21, 32, 11, 28, 41,  3, 51, 46, 34, 47, 25, 26, 49, 57,
        6, 59, 17, 56, 29, 65, 44, 50, 10, 64, 39,  5,  4, 38, 33],
      dtype=int64), 'cur_cost': 107641.0}]
2025-08-03 17:05:45,211 - ExploitationExpert - INFO - 局部搜索耗时: 2.07秒
2025-08-03 17:05:45,211 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-03 17:05:45,212 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([48, 22, 15, 13, 56, 27, 41, 53, 10, 16, 59, 45, 31, 30,  8, 26, 36,
       43, 58, 17, 29, 25, 52, 18, 63, 65, 62,  2, 46, 51,  5, 64,  3, 37,
        4,  6, 33, 21, 61, 44, 40, 32, 39, 28,  7, 11, 55, 50, 14, 34, 57,
       24, 35, 54, 60,  9,  0, 38, 49,  1, 23, 42, 47, 12, 19, 20],
      dtype=int64), 'cur_cost': 95662.0}
2025-08-03 17:05:45,213 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 95662.00)
2025-08-03 17:05:45,214 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-03 17:05:45,215 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-03 17:05:45,217 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:45,235 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 17:05:45,235 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:45,236 - ExplorationExpert - INFO - 探索路径生成完成，成本: 58667.0, 路径长度: 66
2025-08-03 17:05:45,236 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [60, 59, 54, 63, 10, 65, 62, 55, 12, 17, 37, 28, 21, 18, 11, 61, 9, 52, 22, 2, 56, 4, 27, 1, 36, 15, 5, 3, 13, 19, 7, 58, 23, 25, 14, 0, 53, 43, 50, 45, 42, 46, 48, 16, 31, 26, 30, 29, 32, 20, 34, 33, 40, 49, 51, 44, 47, 41, 38, 35, 8, 6, 57, 64, 39, 24], 'cur_cost': 58667.0}
2025-08-03 17:05:45,237 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 58667.00)
2025-08-03 17:05:45,237 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-03 17:05:45,237 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-03 17:05:45,238 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:45,243 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 17:05:45,244 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:45,245 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12411.0, 路径长度: 66
2025-08-03 17:05:45,245 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 21, 16, 12, 22, 23, 13, 20, 14, 15, 17, 18, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12411.0}
2025-08-03 17:05:45,246 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 12411.00)
2025-08-03 17:05:45,246 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-03 17:05:45,247 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:05:45,248 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:05:45,249 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 109966.0
2025-08-03 17:05:47,458 - ExploitationExpert - INFO - res_population_num: 2
2025-08-03 17:05:47,458 - ExploitationExpert - INFO - res_population_costs: [9857.0, 9594.0]
2025-08-03 17:05:47,458 - ExploitationExpert - INFO - res_populations: [array([ 0,  1, 11,  9,  3,  7, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14, 63, 52, 65, 54, 57,
       64, 60, 58, 56, 59, 62, 53, 61, 55,  5,  4,  6,  2,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 17:05:47,460 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:05:47,460 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 2, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12368.0}, {'tour': array([48, 22, 15, 13, 56, 27, 41, 53, 10, 16, 59, 45, 31, 30,  8, 26, 36,
       43, 58, 17, 29, 25, 52, 18, 63, 65, 62,  2, 46, 51,  5, 64,  3, 37,
        4,  6, 33, 21, 61, 44, 40, 32, 39, 28,  7, 11, 55, 50, 14, 34, 57,
       24, 35, 54, 60,  9,  0, 38, 49,  1, 23, 42, 47, 12, 19, 20],
      dtype=int64), 'cur_cost': 95662.0}, {'tour': [60, 59, 54, 63, 10, 65, 62, 55, 12, 17, 37, 28, 21, 18, 11, 61, 9, 52, 22, 2, 56, 4, 27, 1, 36, 15, 5, 3, 13, 19, 7, 58, 23, 25, 14, 0, 53, 43, 50, 45, 42, 46, 48, 16, 31, 26, 30, 29, 32, 20, 34, 33, 40, 49, 51, 44, 47, 41, 38, 35, 8, 6, 57, 64, 39, 24], 'cur_cost': 58667.0}, {'tour': [0, 21, 16, 12, 22, 23, 13, 20, 14, 15, 17, 18, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12411.0}, {'tour': array([ 3,  8,  4, 18,  2, 20, 48, 65, 28, 24, 47, 50, 64, 63, 55, 36, 22,
       62, 49, 30, 21, 43, 34,  5, 44,  1, 11, 60, 16, 39, 31, 52, 42, 14,
       19, 17, 61, 37,  7, 41, 32, 25, 13, 53, 26, 51, 40, 45, 56, 12, 23,
       15, 58, 35, 38,  0, 57, 27,  9, 59, 10, 46,  6, 29, 54, 33],
      dtype=int64), 'cur_cost': 109966.0}, {'tour': array([30, 28, 33, 25, 26, 36, 37, 31, 24, 29, 32, 35, 34, 27, 18, 16, 23,
       22, 12, 17, 15, 14, 20, 21, 13, 19,  9, 11,  7,  3,  1,  0, 10,  8,
        2,  6,  4,  5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9890.0}, {'tour': array([ 9, 38, 21,  6,  5, 39, 18, 40,  2, 58, 24, 12, 35,  8, 25, 45, 47,
       13, 53, 23, 46,  7, 37,  3, 16, 56, 52, 29, 33, 30, 11, 17, 54,  0,
       57, 62, 60, 27,  4, 20, 19, 50, 31, 36, 34, 14, 26, 64, 43, 59, 41,
       51,  1, 15, 28, 49, 55, 22, 48, 32, 10, 44, 63, 65, 61, 42],
      dtype=int64), 'cur_cost': 104081.0}, {'tour': array([39, 19,  9, 17, 25,  3, 38, 14, 35, 54, 44, 58, 56, 46, 41, 26, 49,
       10, 24,  7, 45,  2, 16, 55, 34,  1, 36, 13,  4, 52,  5, 51, 65, 23,
       60, 37, 27, 43, 59, 22, 20, 11,  0, 33, 63,  6, 18, 30, 61, 42, 62,
       31, 32, 21, 64, 15, 12, 28, 50, 40, 53, 57, 48, 29, 47,  8],
      dtype=int64), 'cur_cost': 117932.0}, {'tour': array([40, 21,  4,  7, 13, 58, 54, 31, 18, 59, 19, 27, 23, 47, 53, 32, 57,
       46, 63, 36, 55,  0,  9, 15, 48,  2, 61, 44, 50, 65, 38, 41, 29, 49,
        8, 43, 24, 12,  3, 34, 22, 39, 60, 10, 30,  6, 17, 28, 16, 64, 11,
       33,  5, 37,  1, 51, 52, 26, 35, 56, 42, 62, 14, 45, 20, 25],
      dtype=int64), 'cur_cost': 119526.0}, {'tour': array([ 2, 62, 33, 51, 52, 41, 26, 53,  7,  5, 13, 29, 19, 32, 60, 57, 39,
       21, 12, 50, 64,  9, 59, 54, 14,  4,  3, 22, 23, 16, 48, 37,  8, 42,
       10, 61, 44,  6, 38, 11, 63, 18, 15, 40, 47, 35, 24, 34, 56, 30, 58,
       31,  1, 65, 17,  0, 49, 27, 20, 25, 46, 45, 55, 36, 28, 43],
      dtype=int64), 'cur_cost': 107231.0}, {'tour': array([52, 23, 45, 34,  1, 30, 37, 24, 43,  5, 40,  4, 55, 28, 32, 38, 61,
        2, 49, 11, 53, 15, 54, 18,  3, 51,  9,  8, 13, 47, 22, 35, 41, 17,
        7, 26, 62, 42, 44, 14,  0, 39, 10, 60, 48, 46, 58, 12, 20, 64, 63,
       25, 33, 65, 29, 56, 59, 21, 31,  6, 50, 27, 36, 57, 16, 19],
      dtype=int64), 'cur_cost': 112138.0}, {'tour': array([21, 55, 37, 36, 38, 58, 24, 20, 32, 63,  1, 57, 47,  4, 35, 22, 41,
       40,  5, 11, 16, 60,  2, 28, 19, 61, 64, 52, 42, 49, 30, 44, 46, 14,
       25, 29, 10,  8, 15,  7, 17, 51, 50, 45, 13, 26, 53, 59, 65,  6, 43,
       31, 23, 48,  9, 56, 12, 34, 33, 27,  3,  0, 62, 39, 54, 18],
      dtype=int64), 'cur_cost': 90623.0}, {'tour': array([14,  2, 54, 56, 25, 65, 64,  3, 28, 53, 52, 45, 18, 20, 21, 26, 62,
       47, 39,  4, 40, 24, 17, 11, 41,  6,  5, 32, 22, 61, 51, 43, 31, 50,
       60,  7, 49, 30, 46, 36,  0, 15, 48,  9, 13, 29,  8, 19, 27, 55, 44,
       38,  1, 12, 33, 34, 35, 37, 23, 42, 57, 58, 63, 16, 10, 59],
      dtype=int64), 'cur_cost': 101797.0}, {'tour': array([47, 32, 50, 45, 51, 15,  8, 61, 20, 36, 37, 22, 34,  9, 54, 49, 60,
       48, 18, 14, 57, 10, 24,  2, 53, 42, 40, 27,  6, 56, 12,  4,  7, 26,
       44, 11, 62, 16, 63, 59, 52,  0, 13, 46, 28,  3, 58, 39, 29, 55,  1,
        5, 17, 30, 65, 21, 25, 64, 33, 38, 35, 19, 41, 23, 43, 31],
      dtype=int64), 'cur_cost': 103575.0}, {'tour': array([63, 21, 60, 12, 13, 29, 39, 48,  2, 15, 38, 54, 11, 35, 20, 47, 27,
       44, 32,  3, 59,  1, 16, 46, 58, 50, 40, 57,  0, 43, 22, 23, 61,  9,
       64,  7, 34, 65, 36, 41,  6, 30, 28, 31, 19,  5, 25, 42, 10, 33,  4,
       53, 62, 17, 37, 51, 55, 14, 49, 18, 24, 52, 45, 56, 26,  8],
      dtype=int64), 'cur_cost': 112010.0}, {'tour': array([16, 55, 47, 65,  2, 58, 25, 53, 48, 52,  6, 57, 17, 59, 61, 20, 11,
        3, 50, 22, 33, 28, 15, 31, 54,  0, 39, 36,  5, 43, 30, 37, 46, 64,
       60, 27,  8,  1, 45, 51, 14, 26, 24, 38, 19, 32, 49, 13,  4, 21, 44,
       34, 62, 18, 23,  9, 63, 12, 41, 56, 29, 35,  7, 10, 42, 40],
      dtype=int64), 'cur_cost': 105108.0}, {'tour': array([18, 33, 23,  8, 26, 42, 30, 53, 56, 60, 43, 38, 58, 19, 45, 47, 65,
       10, 20, 49, 28, 15,  3, 24, 31, 14, 32,  2, 29, 11,  6,  1, 36,  5,
       55, 17, 16, 13, 41, 27, 12, 35, 57, 48, 25,  0, 59, 44, 51, 46, 50,
       22,  9, 21, 61, 54, 39,  7, 37, 34, 64, 63, 62, 52,  4, 40],
      dtype=int64), 'cur_cost': 91604.0}, {'tour': array([50,  5, 11, 55, 13, 64, 59, 12, 42, 25, 49, 61, 24, 53, 38, 58, 60,
       44, 30, 33, 15, 29,  7,  8,  0, 20, 18, 62, 54, 10, 28, 41, 47, 23,
       32, 19, 26,  6,  9, 14, 31, 65,  4, 37, 48, 51,  3, 63, 39, 16, 34,
       40, 27, 57,  1, 45,  2, 43, 36, 17, 22, 35, 46, 52, 21, 56],
      dtype=int64), 'cur_cost': 106037.0}, {'tour': array([57,  7, 63, 41,  1, 64, 36,  8, 56, 55, 10, 43, 34, 62, 30, 61,  4,
       22, 14, 52, 32, 19, 29, 49, 28, 25, 15, 54, 13, 18,  2, 20,  6, 16,
       48, 33, 17, 27, 37, 44, 31, 35, 60, 24, 38, 65,  9, 53, 47, 21, 45,
       12, 39,  3, 23, 51, 50, 46, 59, 58, 40, 11,  5,  0, 42, 26],
      dtype=int64), 'cur_cost': 105781.0}, {'tour': array([37, 36,  7, 23, 27, 13, 63, 60, 52, 62, 43, 58, 22, 35, 20, 54, 55,
       16, 48, 30, 15,  8, 24, 61, 12, 42, 31, 40,  1, 45,  2, 18, 14,  9,
       19, 53,  0, 21, 32, 11, 28, 41,  3, 51, 46, 34, 47, 25, 26, 49, 57,
        6, 59, 17, 56, 29, 65, 44, 50, 10, 64, 39,  5,  4, 38, 33],
      dtype=int64), 'cur_cost': 107641.0}]
2025-08-03 17:05:47,470 - ExploitationExpert - INFO - 局部搜索耗时: 2.22秒
2025-08-03 17:05:47,470 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-03 17:05:47,471 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([ 3,  8,  4, 18,  2, 20, 48, 65, 28, 24, 47, 50, 64, 63, 55, 36, 22,
       62, 49, 30, 21, 43, 34,  5, 44,  1, 11, 60, 16, 39, 31, 52, 42, 14,
       19, 17, 61, 37,  7, 41, 32, 25, 13, 53, 26, 51, 40, 45, 56, 12, 23,
       15, 58, 35, 38,  0, 57, 27,  9, 59, 10, 46,  6, 29, 54, 33],
      dtype=int64), 'cur_cost': 109966.0}
2025-08-03 17:05:47,471 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 109966.00)
2025-08-03 17:05:47,471 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-03 17:05:47,472 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-03 17:05:47,472 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:47,494 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 17:05:47,495 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:47,496 - ExplorationExpert - INFO - 探索路径生成完成，成本: 60342.0, 路径长度: 66
2025-08-03 17:05:47,496 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [64, 54, 10, 5, 3, 59, 6, 9, 61, 18, 4, 12, 28, 24, 29, 22, 32, 17, 16, 11, 55, 60, 14, 8, 53, 62, 40, 20, 23, 21, 26, 25, 19, 2, 7, 33, 0, 56, 52, 39, 42, 51, 15, 30, 36, 34, 37, 13, 43, 45, 46, 41, 49, 27, 48, 50, 44, 31, 38, 47, 1, 63, 57, 65, 58, 35], 'cur_cost': 60342.0}
2025-08-03 17:05:47,496 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 60342.00)
2025-08-03 17:05:47,498 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-03 17:05:47,498 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-03 17:05:47,498 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:47,518 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 17:05:47,518 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:47,519 - ExplorationExpert - INFO - 探索路径生成完成，成本: 71173.0, 路径长度: 66
2025-08-03 17:05:47,519 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [54, 17, 13, 37, 16, 36, 15, 5, 60, 23, 9, 53, 57, 61, 8, 14, 11, 62, 56, 22, 12, 7, 24, 1, 2, 59, 3, 4, 64, 21, 32, 10, 19, 31, 0, 63, 44, 49, 48, 38, 40, 34, 27, 18, 30, 47, 50, 42, 26, 20, 39, 51, 35, 28, 43, 41, 29, 25, 6, 58, 55, 65, 52, 46, 45, 33], 'cur_cost': 71173.0}
2025-08-03 17:05:47,520 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 71173.00)
2025-08-03 17:05:47,520 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-03 17:05:47,521 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:05:47,521 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:05:47,521 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 104850.0
2025-08-03 17:05:48,093 - ExploitationExpert - INFO - res_population_num: 3
2025-08-03 17:05:48,093 - ExploitationExpert - INFO - res_population_costs: [9857.0, 9594.0, 9528.0]
2025-08-03 17:05:48,094 - ExploitationExpert - INFO - res_populations: [array([ 0,  1, 11,  9,  3,  7, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14, 63, 52, 65, 54, 57,
       64, 60, 58, 56, 59, 62, 53, 61, 55,  5,  4,  6,  2,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64)]
2025-08-03 17:05:48,096 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:05:48,096 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 2, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12368.0}, {'tour': array([48, 22, 15, 13, 56, 27, 41, 53, 10, 16, 59, 45, 31, 30,  8, 26, 36,
       43, 58, 17, 29, 25, 52, 18, 63, 65, 62,  2, 46, 51,  5, 64,  3, 37,
        4,  6, 33, 21, 61, 44, 40, 32, 39, 28,  7, 11, 55, 50, 14, 34, 57,
       24, 35, 54, 60,  9,  0, 38, 49,  1, 23, 42, 47, 12, 19, 20],
      dtype=int64), 'cur_cost': 95662.0}, {'tour': [60, 59, 54, 63, 10, 65, 62, 55, 12, 17, 37, 28, 21, 18, 11, 61, 9, 52, 22, 2, 56, 4, 27, 1, 36, 15, 5, 3, 13, 19, 7, 58, 23, 25, 14, 0, 53, 43, 50, 45, 42, 46, 48, 16, 31, 26, 30, 29, 32, 20, 34, 33, 40, 49, 51, 44, 47, 41, 38, 35, 8, 6, 57, 64, 39, 24], 'cur_cost': 58667.0}, {'tour': [0, 21, 16, 12, 22, 23, 13, 20, 14, 15, 17, 18, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12411.0}, {'tour': array([ 3,  8,  4, 18,  2, 20, 48, 65, 28, 24, 47, 50, 64, 63, 55, 36, 22,
       62, 49, 30, 21, 43, 34,  5, 44,  1, 11, 60, 16, 39, 31, 52, 42, 14,
       19, 17, 61, 37,  7, 41, 32, 25, 13, 53, 26, 51, 40, 45, 56, 12, 23,
       15, 58, 35, 38,  0, 57, 27,  9, 59, 10, 46,  6, 29, 54, 33],
      dtype=int64), 'cur_cost': 109966.0}, {'tour': [64, 54, 10, 5, 3, 59, 6, 9, 61, 18, 4, 12, 28, 24, 29, 22, 32, 17, 16, 11, 55, 60, 14, 8, 53, 62, 40, 20, 23, 21, 26, 25, 19, 2, 7, 33, 0, 56, 52, 39, 42, 51, 15, 30, 36, 34, 37, 13, 43, 45, 46, 41, 49, 27, 48, 50, 44, 31, 38, 47, 1, 63, 57, 65, 58, 35], 'cur_cost': 60342.0}, {'tour': [54, 17, 13, 37, 16, 36, 15, 5, 60, 23, 9, 53, 57, 61, 8, 14, 11, 62, 56, 22, 12, 7, 24, 1, 2, 59, 3, 4, 64, 21, 32, 10, 19, 31, 0, 63, 44, 49, 48, 38, 40, 34, 27, 18, 30, 47, 50, 42, 26, 20, 39, 51, 35, 28, 43, 41, 29, 25, 6, 58, 55, 65, 52, 46, 45, 33], 'cur_cost': 71173.0}, {'tour': array([15, 56, 11, 29, 30, 62, 51, 32, 43, 14, 57, 18, 21,  3,  1, 24,  9,
       22, 53,  0, 26, 38, 13, 31, 23, 37,  8, 17,  5, 36, 49, 41, 34,  2,
       63,  4, 55, 59, 50, 25, 33, 45, 40, 60, 64, 16, 47,  6, 58, 39, 65,
       12, 61, 46,  7, 52, 20, 27, 28, 10, 48, 35, 44, 42, 19, 54],
      dtype=int64), 'cur_cost': 104850.0}, {'tour': array([40, 21,  4,  7, 13, 58, 54, 31, 18, 59, 19, 27, 23, 47, 53, 32, 57,
       46, 63, 36, 55,  0,  9, 15, 48,  2, 61, 44, 50, 65, 38, 41, 29, 49,
        8, 43, 24, 12,  3, 34, 22, 39, 60, 10, 30,  6, 17, 28, 16, 64, 11,
       33,  5, 37,  1, 51, 52, 26, 35, 56, 42, 62, 14, 45, 20, 25],
      dtype=int64), 'cur_cost': 119526.0}, {'tour': array([ 2, 62, 33, 51, 52, 41, 26, 53,  7,  5, 13, 29, 19, 32, 60, 57, 39,
       21, 12, 50, 64,  9, 59, 54, 14,  4,  3, 22, 23, 16, 48, 37,  8, 42,
       10, 61, 44,  6, 38, 11, 63, 18, 15, 40, 47, 35, 24, 34, 56, 30, 58,
       31,  1, 65, 17,  0, 49, 27, 20, 25, 46, 45, 55, 36, 28, 43],
      dtype=int64), 'cur_cost': 107231.0}, {'tour': array([52, 23, 45, 34,  1, 30, 37, 24, 43,  5, 40,  4, 55, 28, 32, 38, 61,
        2, 49, 11, 53, 15, 54, 18,  3, 51,  9,  8, 13, 47, 22, 35, 41, 17,
        7, 26, 62, 42, 44, 14,  0, 39, 10, 60, 48, 46, 58, 12, 20, 64, 63,
       25, 33, 65, 29, 56, 59, 21, 31,  6, 50, 27, 36, 57, 16, 19],
      dtype=int64), 'cur_cost': 112138.0}, {'tour': array([21, 55, 37, 36, 38, 58, 24, 20, 32, 63,  1, 57, 47,  4, 35, 22, 41,
       40,  5, 11, 16, 60,  2, 28, 19, 61, 64, 52, 42, 49, 30, 44, 46, 14,
       25, 29, 10,  8, 15,  7, 17, 51, 50, 45, 13, 26, 53, 59, 65,  6, 43,
       31, 23, 48,  9, 56, 12, 34, 33, 27,  3,  0, 62, 39, 54, 18],
      dtype=int64), 'cur_cost': 90623.0}, {'tour': array([14,  2, 54, 56, 25, 65, 64,  3, 28, 53, 52, 45, 18, 20, 21, 26, 62,
       47, 39,  4, 40, 24, 17, 11, 41,  6,  5, 32, 22, 61, 51, 43, 31, 50,
       60,  7, 49, 30, 46, 36,  0, 15, 48,  9, 13, 29,  8, 19, 27, 55, 44,
       38,  1, 12, 33, 34, 35, 37, 23, 42, 57, 58, 63, 16, 10, 59],
      dtype=int64), 'cur_cost': 101797.0}, {'tour': array([47, 32, 50, 45, 51, 15,  8, 61, 20, 36, 37, 22, 34,  9, 54, 49, 60,
       48, 18, 14, 57, 10, 24,  2, 53, 42, 40, 27,  6, 56, 12,  4,  7, 26,
       44, 11, 62, 16, 63, 59, 52,  0, 13, 46, 28,  3, 58, 39, 29, 55,  1,
        5, 17, 30, 65, 21, 25, 64, 33, 38, 35, 19, 41, 23, 43, 31],
      dtype=int64), 'cur_cost': 103575.0}, {'tour': array([63, 21, 60, 12, 13, 29, 39, 48,  2, 15, 38, 54, 11, 35, 20, 47, 27,
       44, 32,  3, 59,  1, 16, 46, 58, 50, 40, 57,  0, 43, 22, 23, 61,  9,
       64,  7, 34, 65, 36, 41,  6, 30, 28, 31, 19,  5, 25, 42, 10, 33,  4,
       53, 62, 17, 37, 51, 55, 14, 49, 18, 24, 52, 45, 56, 26,  8],
      dtype=int64), 'cur_cost': 112010.0}, {'tour': array([16, 55, 47, 65,  2, 58, 25, 53, 48, 52,  6, 57, 17, 59, 61, 20, 11,
        3, 50, 22, 33, 28, 15, 31, 54,  0, 39, 36,  5, 43, 30, 37, 46, 64,
       60, 27,  8,  1, 45, 51, 14, 26, 24, 38, 19, 32, 49, 13,  4, 21, 44,
       34, 62, 18, 23,  9, 63, 12, 41, 56, 29, 35,  7, 10, 42, 40],
      dtype=int64), 'cur_cost': 105108.0}, {'tour': array([18, 33, 23,  8, 26, 42, 30, 53, 56, 60, 43, 38, 58, 19, 45, 47, 65,
       10, 20, 49, 28, 15,  3, 24, 31, 14, 32,  2, 29, 11,  6,  1, 36,  5,
       55, 17, 16, 13, 41, 27, 12, 35, 57, 48, 25,  0, 59, 44, 51, 46, 50,
       22,  9, 21, 61, 54, 39,  7, 37, 34, 64, 63, 62, 52,  4, 40],
      dtype=int64), 'cur_cost': 91604.0}, {'tour': array([50,  5, 11, 55, 13, 64, 59, 12, 42, 25, 49, 61, 24, 53, 38, 58, 60,
       44, 30, 33, 15, 29,  7,  8,  0, 20, 18, 62, 54, 10, 28, 41, 47, 23,
       32, 19, 26,  6,  9, 14, 31, 65,  4, 37, 48, 51,  3, 63, 39, 16, 34,
       40, 27, 57,  1, 45,  2, 43, 36, 17, 22, 35, 46, 52, 21, 56],
      dtype=int64), 'cur_cost': 106037.0}, {'tour': array([57,  7, 63, 41,  1, 64, 36,  8, 56, 55, 10, 43, 34, 62, 30, 61,  4,
       22, 14, 52, 32, 19, 29, 49, 28, 25, 15, 54, 13, 18,  2, 20,  6, 16,
       48, 33, 17, 27, 37, 44, 31, 35, 60, 24, 38, 65,  9, 53, 47, 21, 45,
       12, 39,  3, 23, 51, 50, 46, 59, 58, 40, 11,  5,  0, 42, 26],
      dtype=int64), 'cur_cost': 105781.0}, {'tour': array([37, 36,  7, 23, 27, 13, 63, 60, 52, 62, 43, 58, 22, 35, 20, 54, 55,
       16, 48, 30, 15,  8, 24, 61, 12, 42, 31, 40,  1, 45,  2, 18, 14,  9,
       19, 53,  0, 21, 32, 11, 28, 41,  3, 51, 46, 34, 47, 25, 26, 49, 57,
        6, 59, 17, 56, 29, 65, 44, 50, 10, 64, 39,  5,  4, 38, 33],
      dtype=int64), 'cur_cost': 107641.0}]
2025-08-03 17:05:48,105 - ExploitationExpert - INFO - 局部搜索耗时: 0.58秒
2025-08-03 17:05:48,105 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-03 17:05:48,106 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([15, 56, 11, 29, 30, 62, 51, 32, 43, 14, 57, 18, 21,  3,  1, 24,  9,
       22, 53,  0, 26, 38, 13, 31, 23, 37,  8, 17,  5, 36, 49, 41, 34,  2,
       63,  4, 55, 59, 50, 25, 33, 45, 40, 60, 64, 16, 47,  6, 58, 39, 65,
       12, 61, 46,  7, 52, 20, 27, 28, 10, 48, 35, 44, 42, 19, 54],
      dtype=int64), 'cur_cost': 104850.0}
2025-08-03 17:05:48,106 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 104850.00)
2025-08-03 17:05:48,107 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-03 17:05:48,107 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-03 17:05:48,107 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:48,124 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 17:05:48,124 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:48,125 - ExplorationExpert - INFO - 探索路径生成完成，成本: 61947.0, 路径长度: 66
2025-08-03 17:05:48,126 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [60, 63, 17, 30, 11, 55, 23, 35, 18, 8, 10, 61, 62, 57, 9, 59, 3, 14, 34, 15, 19, 21, 22, 5, 1, 54, 4, 36, 37, 24, 40, 46, 20, 27, 32, 2, 0, 16, 33, 49, 42, 50, 38, 44, 45, 47, 51, 25, 43, 13, 6, 64, 53, 12, 48, 41, 28, 7, 52, 56, 58, 65, 39, 26, 31, 29], 'cur_cost': 61947.0}
2025-08-03 17:05:48,127 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 61947.00)
2025-08-03 17:05:48,127 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-03 17:05:48,128 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-03 17:05:48,129 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:48,133 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 17:05:48,134 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:48,134 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12410.0, 路径长度: 66
2025-08-03 17:05:48,134 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 7, 6, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12410.0}
2025-08-03 17:05:48,135 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 12410.00)
2025-08-03 17:05:48,135 - experts.management.collaboration_manager - INFO - 为个体 10 生成利用路径
2025-08-03 17:05:48,135 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:05:48,135 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:05:48,135 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 10 处的路径，新成本: 106924.0
2025-08-03 17:05:48,220 - ExploitationExpert - INFO - res_population_num: 4
2025-08-03 17:05:48,221 - ExploitationExpert - INFO - res_population_costs: [9857.0, 9594.0, 9528.0, 9527]
2025-08-03 17:05:48,221 - ExploitationExpert - INFO - res_populations: [array([ 0,  1, 11,  9,  3,  7, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14, 63, 52, 65, 54, 57,
       64, 60, 58, 56, 59, 62, 53, 61, 55,  5,  4,  6,  2,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64)]
2025-08-03 17:05:48,223 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:05:48,223 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 2, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12368.0}, {'tour': array([48, 22, 15, 13, 56, 27, 41, 53, 10, 16, 59, 45, 31, 30,  8, 26, 36,
       43, 58, 17, 29, 25, 52, 18, 63, 65, 62,  2, 46, 51,  5, 64,  3, 37,
        4,  6, 33, 21, 61, 44, 40, 32, 39, 28,  7, 11, 55, 50, 14, 34, 57,
       24, 35, 54, 60,  9,  0, 38, 49,  1, 23, 42, 47, 12, 19, 20],
      dtype=int64), 'cur_cost': 95662.0}, {'tour': [60, 59, 54, 63, 10, 65, 62, 55, 12, 17, 37, 28, 21, 18, 11, 61, 9, 52, 22, 2, 56, 4, 27, 1, 36, 15, 5, 3, 13, 19, 7, 58, 23, 25, 14, 0, 53, 43, 50, 45, 42, 46, 48, 16, 31, 26, 30, 29, 32, 20, 34, 33, 40, 49, 51, 44, 47, 41, 38, 35, 8, 6, 57, 64, 39, 24], 'cur_cost': 58667.0}, {'tour': [0, 21, 16, 12, 22, 23, 13, 20, 14, 15, 17, 18, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12411.0}, {'tour': array([ 3,  8,  4, 18,  2, 20, 48, 65, 28, 24, 47, 50, 64, 63, 55, 36, 22,
       62, 49, 30, 21, 43, 34,  5, 44,  1, 11, 60, 16, 39, 31, 52, 42, 14,
       19, 17, 61, 37,  7, 41, 32, 25, 13, 53, 26, 51, 40, 45, 56, 12, 23,
       15, 58, 35, 38,  0, 57, 27,  9, 59, 10, 46,  6, 29, 54, 33],
      dtype=int64), 'cur_cost': 109966.0}, {'tour': [64, 54, 10, 5, 3, 59, 6, 9, 61, 18, 4, 12, 28, 24, 29, 22, 32, 17, 16, 11, 55, 60, 14, 8, 53, 62, 40, 20, 23, 21, 26, 25, 19, 2, 7, 33, 0, 56, 52, 39, 42, 51, 15, 30, 36, 34, 37, 13, 43, 45, 46, 41, 49, 27, 48, 50, 44, 31, 38, 47, 1, 63, 57, 65, 58, 35], 'cur_cost': 60342.0}, {'tour': [54, 17, 13, 37, 16, 36, 15, 5, 60, 23, 9, 53, 57, 61, 8, 14, 11, 62, 56, 22, 12, 7, 24, 1, 2, 59, 3, 4, 64, 21, 32, 10, 19, 31, 0, 63, 44, 49, 48, 38, 40, 34, 27, 18, 30, 47, 50, 42, 26, 20, 39, 51, 35, 28, 43, 41, 29, 25, 6, 58, 55, 65, 52, 46, 45, 33], 'cur_cost': 71173.0}, {'tour': array([15, 56, 11, 29, 30, 62, 51, 32, 43, 14, 57, 18, 21,  3,  1, 24,  9,
       22, 53,  0, 26, 38, 13, 31, 23, 37,  8, 17,  5, 36, 49, 41, 34,  2,
       63,  4, 55, 59, 50, 25, 33, 45, 40, 60, 64, 16, 47,  6, 58, 39, 65,
       12, 61, 46,  7, 52, 20, 27, 28, 10, 48, 35, 44, 42, 19, 54],
      dtype=int64), 'cur_cost': 104850.0}, {'tour': [60, 63, 17, 30, 11, 55, 23, 35, 18, 8, 10, 61, 62, 57, 9, 59, 3, 14, 34, 15, 19, 21, 22, 5, 1, 54, 4, 36, 37, 24, 40, 46, 20, 27, 32, 2, 0, 16, 33, 49, 42, 50, 38, 44, 45, 47, 51, 25, 43, 13, 6, 64, 53, 12, 48, 41, 28, 7, 52, 56, 58, 65, 39, 26, 31, 29], 'cur_cost': 61947.0}, {'tour': [0, 7, 6, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12410.0}, {'tour': array([63, 50, 30, 39, 15, 37, 10, 52, 61,  4, 64, 56, 33,  7, 29,  3, 27,
       17, 41, 45, 11,  1, 18, 32, 53, 36, 12, 35, 59, 38, 20, 58,  9, 54,
       24, 34, 16, 42, 31, 43, 47, 25, 19,  5, 48, 44, 60, 49, 22, 62, 14,
        6, 28, 21, 26, 51, 57, 55,  0, 40,  8, 46, 65, 13, 23,  2],
      dtype=int64), 'cur_cost': 106924.0}, {'tour': array([21, 55, 37, 36, 38, 58, 24, 20, 32, 63,  1, 57, 47,  4, 35, 22, 41,
       40,  5, 11, 16, 60,  2, 28, 19, 61, 64, 52, 42, 49, 30, 44, 46, 14,
       25, 29, 10,  8, 15,  7, 17, 51, 50, 45, 13, 26, 53, 59, 65,  6, 43,
       31, 23, 48,  9, 56, 12, 34, 33, 27,  3,  0, 62, 39, 54, 18],
      dtype=int64), 'cur_cost': 90623.0}, {'tour': array([14,  2, 54, 56, 25, 65, 64,  3, 28, 53, 52, 45, 18, 20, 21, 26, 62,
       47, 39,  4, 40, 24, 17, 11, 41,  6,  5, 32, 22, 61, 51, 43, 31, 50,
       60,  7, 49, 30, 46, 36,  0, 15, 48,  9, 13, 29,  8, 19, 27, 55, 44,
       38,  1, 12, 33, 34, 35, 37, 23, 42, 57, 58, 63, 16, 10, 59],
      dtype=int64), 'cur_cost': 101797.0}, {'tour': array([47, 32, 50, 45, 51, 15,  8, 61, 20, 36, 37, 22, 34,  9, 54, 49, 60,
       48, 18, 14, 57, 10, 24,  2, 53, 42, 40, 27,  6, 56, 12,  4,  7, 26,
       44, 11, 62, 16, 63, 59, 52,  0, 13, 46, 28,  3, 58, 39, 29, 55,  1,
        5, 17, 30, 65, 21, 25, 64, 33, 38, 35, 19, 41, 23, 43, 31],
      dtype=int64), 'cur_cost': 103575.0}, {'tour': array([63, 21, 60, 12, 13, 29, 39, 48,  2, 15, 38, 54, 11, 35, 20, 47, 27,
       44, 32,  3, 59,  1, 16, 46, 58, 50, 40, 57,  0, 43, 22, 23, 61,  9,
       64,  7, 34, 65, 36, 41,  6, 30, 28, 31, 19,  5, 25, 42, 10, 33,  4,
       53, 62, 17, 37, 51, 55, 14, 49, 18, 24, 52, 45, 56, 26,  8],
      dtype=int64), 'cur_cost': 112010.0}, {'tour': array([16, 55, 47, 65,  2, 58, 25, 53, 48, 52,  6, 57, 17, 59, 61, 20, 11,
        3, 50, 22, 33, 28, 15, 31, 54,  0, 39, 36,  5, 43, 30, 37, 46, 64,
       60, 27,  8,  1, 45, 51, 14, 26, 24, 38, 19, 32, 49, 13,  4, 21, 44,
       34, 62, 18, 23,  9, 63, 12, 41, 56, 29, 35,  7, 10, 42, 40],
      dtype=int64), 'cur_cost': 105108.0}, {'tour': array([18, 33, 23,  8, 26, 42, 30, 53, 56, 60, 43, 38, 58, 19, 45, 47, 65,
       10, 20, 49, 28, 15,  3, 24, 31, 14, 32,  2, 29, 11,  6,  1, 36,  5,
       55, 17, 16, 13, 41, 27, 12, 35, 57, 48, 25,  0, 59, 44, 51, 46, 50,
       22,  9, 21, 61, 54, 39,  7, 37, 34, 64, 63, 62, 52,  4, 40],
      dtype=int64), 'cur_cost': 91604.0}, {'tour': array([50,  5, 11, 55, 13, 64, 59, 12, 42, 25, 49, 61, 24, 53, 38, 58, 60,
       44, 30, 33, 15, 29,  7,  8,  0, 20, 18, 62, 54, 10, 28, 41, 47, 23,
       32, 19, 26,  6,  9, 14, 31, 65,  4, 37, 48, 51,  3, 63, 39, 16, 34,
       40, 27, 57,  1, 45,  2, 43, 36, 17, 22, 35, 46, 52, 21, 56],
      dtype=int64), 'cur_cost': 106037.0}, {'tour': array([57,  7, 63, 41,  1, 64, 36,  8, 56, 55, 10, 43, 34, 62, 30, 61,  4,
       22, 14, 52, 32, 19, 29, 49, 28, 25, 15, 54, 13, 18,  2, 20,  6, 16,
       48, 33, 17, 27, 37, 44, 31, 35, 60, 24, 38, 65,  9, 53, 47, 21, 45,
       12, 39,  3, 23, 51, 50, 46, 59, 58, 40, 11,  5,  0, 42, 26],
      dtype=int64), 'cur_cost': 105781.0}, {'tour': array([37, 36,  7, 23, 27, 13, 63, 60, 52, 62, 43, 58, 22, 35, 20, 54, 55,
       16, 48, 30, 15,  8, 24, 61, 12, 42, 31, 40,  1, 45,  2, 18, 14,  9,
       19, 53,  0, 21, 32, 11, 28, 41,  3, 51, 46, 34, 47, 25, 26, 49, 57,
        6, 59, 17, 56, 29, 65, 44, 50, 10, 64, 39,  5,  4, 38, 33],
      dtype=int64), 'cur_cost': 107641.0}]
2025-08-03 17:05:48,234 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 17:05:48,234 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-03 17:05:48,235 - experts.management.collaboration_manager - INFO - 个体 10 利用路径生成报告: {'new_tour': array([63, 50, 30, 39, 15, 37, 10, 52, 61,  4, 64, 56, 33,  7, 29,  3, 27,
       17, 41, 45, 11,  1, 18, 32, 53, 36, 12, 35, 59, 38, 20, 58,  9, 54,
       24, 34, 16, 42, 31, 43, 47, 25, 19,  5, 48, 44, 60, 49, 22, 62, 14,
        6, 28, 21, 26, 51, 57, 55,  0, 40,  8, 46, 65, 13, 23,  2],
      dtype=int64), 'cur_cost': 106924.0}
2025-08-03 17:05:48,235 - experts.management.collaboration_manager - INFO - 个体 10 接受新路径 (成本: 106924.00)
2025-08-03 17:05:48,235 - experts.management.collaboration_manager - INFO - 为个体 11 生成探索路径
2025-08-03 17:05:48,236 - ExplorationExpert - INFO - 开始为个体 11 生成探索路径（算法实现）
2025-08-03 17:05:48,236 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:48,240 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 17:05:48,240 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:48,241 - ExplorationExpert - INFO - 探索路径生成完成，成本: 82455.0, 路径长度: 66
2025-08-03 17:05:48,241 - experts.management.collaboration_manager - INFO - 个体 11 探索路径生成报告: {'new_tour': [59, 5, 22, 13, 12, 17, 28, 18, 14, 52, 35, 27, 24, 7, 19, 26, 9, 11, 8, 53, 43, 45, 34, 15, 30, 31, 29, 41, 40, 51, 50, 47, 58, 57, 20, 3, 49, 56, 4, 32, 54, 36, 42, 37, 16, 0, 65, 10, 63, 60, 23, 6, 61, 44, 38, 21, 1, 25, 48, 2, 55, 39, 46, 33, 64, 62], 'cur_cost': 82455.0}
2025-08-03 17:05:48,242 - experts.management.collaboration_manager - INFO - 个体 11 接受新路径 (成本: 82455.00)
2025-08-03 17:05:48,242 - experts.management.collaboration_manager - INFO - 为个体 12 生成探索路径
2025-08-03 17:05:48,243 - ExplorationExpert - INFO - 开始为个体 12 生成探索路径（算法实现）
2025-08-03 17:05:48,243 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:48,260 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 17:05:48,262 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:48,263 - ExplorationExpert - INFO - 探索路径生成完成，成本: 66618.0, 路径长度: 66
2025-08-03 17:05:48,263 - experts.management.collaboration_manager - INFO - 个体 12 探索路径生成报告: {'new_tour': [39, 23, 7, 56, 0, 52, 3, 15, 20, 25, 13, 29, 11, 17, 1, 61, 58, 5, 18, 19, 32, 33, 21, 9, 16, 2, 12, 49, 46, 42, 50, 43, 44, 47, 51, 34, 36, 37, 8, 54, 14, 4, 59, 6, 63, 57, 60, 48, 26, 31, 40, 22, 35, 30, 27, 38, 10, 53, 64, 62, 65, 41, 45, 28, 24, 55], 'cur_cost': 66618.0}
2025-08-03 17:05:48,264 - experts.management.collaboration_manager - INFO - 个体 12 接受新路径 (成本: 66618.00)
2025-08-03 17:05:48,265 - experts.management.collaboration_manager - INFO - 为个体 13 生成利用路径
2025-08-03 17:05:48,265 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:05:48,265 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:05:48,266 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 13 处的路径，新成本: 99010.0
2025-08-03 17:05:48,338 - ExploitationExpert - INFO - res_population_num: 7
2025-08-03 17:05:48,339 - ExploitationExpert - INFO - res_population_costs: [9857.0, 9594.0, 9528.0, 9527, 9527, 9527, 9522]
2025-08-03 17:05:48,339 - ExploitationExpert - INFO - res_populations: [array([ 0,  1, 11,  9,  3,  7, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14, 63, 52, 65, 54, 57,
       64, 60, 58, 56, 59, 62, 53, 61, 55,  5,  4,  6,  2,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-08-03 17:05:48,345 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:05:48,345 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 2, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12368.0}, {'tour': array([48, 22, 15, 13, 56, 27, 41, 53, 10, 16, 59, 45, 31, 30,  8, 26, 36,
       43, 58, 17, 29, 25, 52, 18, 63, 65, 62,  2, 46, 51,  5, 64,  3, 37,
        4,  6, 33, 21, 61, 44, 40, 32, 39, 28,  7, 11, 55, 50, 14, 34, 57,
       24, 35, 54, 60,  9,  0, 38, 49,  1, 23, 42, 47, 12, 19, 20],
      dtype=int64), 'cur_cost': 95662.0}, {'tour': [60, 59, 54, 63, 10, 65, 62, 55, 12, 17, 37, 28, 21, 18, 11, 61, 9, 52, 22, 2, 56, 4, 27, 1, 36, 15, 5, 3, 13, 19, 7, 58, 23, 25, 14, 0, 53, 43, 50, 45, 42, 46, 48, 16, 31, 26, 30, 29, 32, 20, 34, 33, 40, 49, 51, 44, 47, 41, 38, 35, 8, 6, 57, 64, 39, 24], 'cur_cost': 58667.0}, {'tour': [0, 21, 16, 12, 22, 23, 13, 20, 14, 15, 17, 18, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12411.0}, {'tour': array([ 3,  8,  4, 18,  2, 20, 48, 65, 28, 24, 47, 50, 64, 63, 55, 36, 22,
       62, 49, 30, 21, 43, 34,  5, 44,  1, 11, 60, 16, 39, 31, 52, 42, 14,
       19, 17, 61, 37,  7, 41, 32, 25, 13, 53, 26, 51, 40, 45, 56, 12, 23,
       15, 58, 35, 38,  0, 57, 27,  9, 59, 10, 46,  6, 29, 54, 33],
      dtype=int64), 'cur_cost': 109966.0}, {'tour': [64, 54, 10, 5, 3, 59, 6, 9, 61, 18, 4, 12, 28, 24, 29, 22, 32, 17, 16, 11, 55, 60, 14, 8, 53, 62, 40, 20, 23, 21, 26, 25, 19, 2, 7, 33, 0, 56, 52, 39, 42, 51, 15, 30, 36, 34, 37, 13, 43, 45, 46, 41, 49, 27, 48, 50, 44, 31, 38, 47, 1, 63, 57, 65, 58, 35], 'cur_cost': 60342.0}, {'tour': [54, 17, 13, 37, 16, 36, 15, 5, 60, 23, 9, 53, 57, 61, 8, 14, 11, 62, 56, 22, 12, 7, 24, 1, 2, 59, 3, 4, 64, 21, 32, 10, 19, 31, 0, 63, 44, 49, 48, 38, 40, 34, 27, 18, 30, 47, 50, 42, 26, 20, 39, 51, 35, 28, 43, 41, 29, 25, 6, 58, 55, 65, 52, 46, 45, 33], 'cur_cost': 71173.0}, {'tour': array([15, 56, 11, 29, 30, 62, 51, 32, 43, 14, 57, 18, 21,  3,  1, 24,  9,
       22, 53,  0, 26, 38, 13, 31, 23, 37,  8, 17,  5, 36, 49, 41, 34,  2,
       63,  4, 55, 59, 50, 25, 33, 45, 40, 60, 64, 16, 47,  6, 58, 39, 65,
       12, 61, 46,  7, 52, 20, 27, 28, 10, 48, 35, 44, 42, 19, 54],
      dtype=int64), 'cur_cost': 104850.0}, {'tour': [60, 63, 17, 30, 11, 55, 23, 35, 18, 8, 10, 61, 62, 57, 9, 59, 3, 14, 34, 15, 19, 21, 22, 5, 1, 54, 4, 36, 37, 24, 40, 46, 20, 27, 32, 2, 0, 16, 33, 49, 42, 50, 38, 44, 45, 47, 51, 25, 43, 13, 6, 64, 53, 12, 48, 41, 28, 7, 52, 56, 58, 65, 39, 26, 31, 29], 'cur_cost': 61947.0}, {'tour': [0, 7, 6, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12410.0}, {'tour': array([63, 50, 30, 39, 15, 37, 10, 52, 61,  4, 64, 56, 33,  7, 29,  3, 27,
       17, 41, 45, 11,  1, 18, 32, 53, 36, 12, 35, 59, 38, 20, 58,  9, 54,
       24, 34, 16, 42, 31, 43, 47, 25, 19,  5, 48, 44, 60, 49, 22, 62, 14,
        6, 28, 21, 26, 51, 57, 55,  0, 40,  8, 46, 65, 13, 23,  2],
      dtype=int64), 'cur_cost': 106924.0}, {'tour': [59, 5, 22, 13, 12, 17, 28, 18, 14, 52, 35, 27, 24, 7, 19, 26, 9, 11, 8, 53, 43, 45, 34, 15, 30, 31, 29, 41, 40, 51, 50, 47, 58, 57, 20, 3, 49, 56, 4, 32, 54, 36, 42, 37, 16, 0, 65, 10, 63, 60, 23, 6, 61, 44, 38, 21, 1, 25, 48, 2, 55, 39, 46, 33, 64, 62], 'cur_cost': 82455.0}, {'tour': [39, 23, 7, 56, 0, 52, 3, 15, 20, 25, 13, 29, 11, 17, 1, 61, 58, 5, 18, 19, 32, 33, 21, 9, 16, 2, 12, 49, 46, 42, 50, 43, 44, 47, 51, 34, 36, 37, 8, 54, 14, 4, 59, 6, 63, 57, 60, 48, 26, 31, 40, 22, 35, 30, 27, 38, 10, 53, 64, 62, 65, 41, 45, 28, 24, 55], 'cur_cost': 66618.0}, {'tour': array([60, 21, 11,  0, 45, 58, 25, 61,  4, 64, 40, 27,  5, 23, 33, 35, 44,
       14, 12, 34, 39, 65,  7, 56, 43, 15, 42, 41, 19, 16, 37, 52, 24, 18,
       51, 54, 59, 10,  9, 20, 36, 28,  3, 30, 32, 26,  2,  8,  1, 55, 48,
       57, 50,  6, 13, 62, 17, 47, 46, 31, 29, 38, 63, 53, 49, 22],
      dtype=int64), 'cur_cost': 99010.0}, {'tour': array([63, 21, 60, 12, 13, 29, 39, 48,  2, 15, 38, 54, 11, 35, 20, 47, 27,
       44, 32,  3, 59,  1, 16, 46, 58, 50, 40, 57,  0, 43, 22, 23, 61,  9,
       64,  7, 34, 65, 36, 41,  6, 30, 28, 31, 19,  5, 25, 42, 10, 33,  4,
       53, 62, 17, 37, 51, 55, 14, 49, 18, 24, 52, 45, 56, 26,  8],
      dtype=int64), 'cur_cost': 112010.0}, {'tour': array([16, 55, 47, 65,  2, 58, 25, 53, 48, 52,  6, 57, 17, 59, 61, 20, 11,
        3, 50, 22, 33, 28, 15, 31, 54,  0, 39, 36,  5, 43, 30, 37, 46, 64,
       60, 27,  8,  1, 45, 51, 14, 26, 24, 38, 19, 32, 49, 13,  4, 21, 44,
       34, 62, 18, 23,  9, 63, 12, 41, 56, 29, 35,  7, 10, 42, 40],
      dtype=int64), 'cur_cost': 105108.0}, {'tour': array([18, 33, 23,  8, 26, 42, 30, 53, 56, 60, 43, 38, 58, 19, 45, 47, 65,
       10, 20, 49, 28, 15,  3, 24, 31, 14, 32,  2, 29, 11,  6,  1, 36,  5,
       55, 17, 16, 13, 41, 27, 12, 35, 57, 48, 25,  0, 59, 44, 51, 46, 50,
       22,  9, 21, 61, 54, 39,  7, 37, 34, 64, 63, 62, 52,  4, 40],
      dtype=int64), 'cur_cost': 91604.0}, {'tour': array([50,  5, 11, 55, 13, 64, 59, 12, 42, 25, 49, 61, 24, 53, 38, 58, 60,
       44, 30, 33, 15, 29,  7,  8,  0, 20, 18, 62, 54, 10, 28, 41, 47, 23,
       32, 19, 26,  6,  9, 14, 31, 65,  4, 37, 48, 51,  3, 63, 39, 16, 34,
       40, 27, 57,  1, 45,  2, 43, 36, 17, 22, 35, 46, 52, 21, 56],
      dtype=int64), 'cur_cost': 106037.0}, {'tour': array([57,  7, 63, 41,  1, 64, 36,  8, 56, 55, 10, 43, 34, 62, 30, 61,  4,
       22, 14, 52, 32, 19, 29, 49, 28, 25, 15, 54, 13, 18,  2, 20,  6, 16,
       48, 33, 17, 27, 37, 44, 31, 35, 60, 24, 38, 65,  9, 53, 47, 21, 45,
       12, 39,  3, 23, 51, 50, 46, 59, 58, 40, 11,  5,  0, 42, 26],
      dtype=int64), 'cur_cost': 105781.0}, {'tour': array([37, 36,  7, 23, 27, 13, 63, 60, 52, 62, 43, 58, 22, 35, 20, 54, 55,
       16, 48, 30, 15,  8, 24, 61, 12, 42, 31, 40,  1, 45,  2, 18, 14,  9,
       19, 53,  0, 21, 32, 11, 28, 41,  3, 51, 46, 34, 47, 25, 26, 49, 57,
        6, 59, 17, 56, 29, 65, 44, 50, 10, 64, 39,  5,  4, 38, 33],
      dtype=int64), 'cur_cost': 107641.0}]
2025-08-03 17:05:48,357 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-03 17:05:48,357 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-03 17:05:48,358 - experts.management.collaboration_manager - INFO - 个体 13 利用路径生成报告: {'new_tour': array([60, 21, 11,  0, 45, 58, 25, 61,  4, 64, 40, 27,  5, 23, 33, 35, 44,
       14, 12, 34, 39, 65,  7, 56, 43, 15, 42, 41, 19, 16, 37, 52, 24, 18,
       51, 54, 59, 10,  9, 20, 36, 28,  3, 30, 32, 26,  2,  8,  1, 55, 48,
       57, 50,  6, 13, 62, 17, 47, 46, 31, 29, 38, 63, 53, 49, 22],
      dtype=int64), 'cur_cost': 99010.0}
2025-08-03 17:05:48,358 - experts.management.collaboration_manager - INFO - 个体 13 接受新路径 (成本: 99010.00)
2025-08-03 17:05:48,359 - experts.management.collaboration_manager - INFO - 为个体 14 生成探索路径
2025-08-03 17:05:48,359 - ExplorationExpert - INFO - 开始为个体 14 生成探索路径（算法实现）
2025-08-03 17:05:48,359 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:48,365 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 17:05:48,365 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:48,365 - ExplorationExpert - INFO - 探索路径生成完成，成本: 92891.0, 路径长度: 66
2025-08-03 17:05:48,366 - experts.management.collaboration_manager - INFO - 个体 14 探索路径生成报告: {'new_tour': [2, 63, 16, 23, 55, 37, 53, 19, 17, 25, 22, 11, 56, 4, 27, 1, 29, 7, 10, 14, 49, 52, 45, 51, 18, 46, 41, 47, 42, 64, 65, 5, 57, 3, 54, 26, 12, 61, 24, 59, 60, 43, 20, 39, 6, 34, 28, 40, 35, 33, 15, 13, 31, 50, 21, 8, 62, 48, 32, 30, 36, 9, 44, 38, 58, 0], 'cur_cost': 92891.0}
2025-08-03 17:05:48,366 - experts.management.collaboration_manager - INFO - 个体 14 接受新路径 (成本: 92891.00)
2025-08-03 17:05:48,366 - experts.management.collaboration_manager - INFO - 为个体 15 生成探索路径
2025-08-03 17:05:48,367 - ExplorationExpert - INFO - 开始为个体 15 生成探索路径（算法实现）
2025-08-03 17:05:48,367 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:48,371 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 17:05:48,372 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:48,372 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12391.0, 路径长度: 66
2025-08-03 17:05:48,372 - experts.management.collaboration_manager - INFO - 个体 15 探索路径生成报告: {'new_tour': [0, 3, 11, 23, 16, 18, 12, 22, 15, 14, 20, 21, 13, 19, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12391.0}
2025-08-03 17:05:48,373 - experts.management.collaboration_manager - INFO - 个体 15 接受新路径 (成本: 12391.00)
2025-08-03 17:05:48,373 - experts.management.collaboration_manager - INFO - 为个体 16 生成利用路径
2025-08-03 17:05:48,373 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:05:48,373 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:05:48,374 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 16 处的路径，新成本: 104485.0
2025-08-03 17:05:48,455 - ExploitationExpert - INFO - res_population_num: 8
2025-08-03 17:05:48,456 - ExploitationExpert - INFO - res_population_costs: [9857.0, 9594.0, 9528.0, 9527, 9527, 9527, 9522, 9521]
2025-08-03 17:05:48,456 - ExploitationExpert - INFO - res_populations: [array([ 0,  1, 11,  9,  3,  7, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14, 63, 52, 65, 54, 57,
       64, 60, 58, 56, 59, 62, 53, 61, 55,  5,  4,  6,  2,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-03 17:05:48,461 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:05:48,461 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 2, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12368.0}, {'tour': array([48, 22, 15, 13, 56, 27, 41, 53, 10, 16, 59, 45, 31, 30,  8, 26, 36,
       43, 58, 17, 29, 25, 52, 18, 63, 65, 62,  2, 46, 51,  5, 64,  3, 37,
        4,  6, 33, 21, 61, 44, 40, 32, 39, 28,  7, 11, 55, 50, 14, 34, 57,
       24, 35, 54, 60,  9,  0, 38, 49,  1, 23, 42, 47, 12, 19, 20],
      dtype=int64), 'cur_cost': 95662.0}, {'tour': [60, 59, 54, 63, 10, 65, 62, 55, 12, 17, 37, 28, 21, 18, 11, 61, 9, 52, 22, 2, 56, 4, 27, 1, 36, 15, 5, 3, 13, 19, 7, 58, 23, 25, 14, 0, 53, 43, 50, 45, 42, 46, 48, 16, 31, 26, 30, 29, 32, 20, 34, 33, 40, 49, 51, 44, 47, 41, 38, 35, 8, 6, 57, 64, 39, 24], 'cur_cost': 58667.0}, {'tour': [0, 21, 16, 12, 22, 23, 13, 20, 14, 15, 17, 18, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12411.0}, {'tour': array([ 3,  8,  4, 18,  2, 20, 48, 65, 28, 24, 47, 50, 64, 63, 55, 36, 22,
       62, 49, 30, 21, 43, 34,  5, 44,  1, 11, 60, 16, 39, 31, 52, 42, 14,
       19, 17, 61, 37,  7, 41, 32, 25, 13, 53, 26, 51, 40, 45, 56, 12, 23,
       15, 58, 35, 38,  0, 57, 27,  9, 59, 10, 46,  6, 29, 54, 33],
      dtype=int64), 'cur_cost': 109966.0}, {'tour': [64, 54, 10, 5, 3, 59, 6, 9, 61, 18, 4, 12, 28, 24, 29, 22, 32, 17, 16, 11, 55, 60, 14, 8, 53, 62, 40, 20, 23, 21, 26, 25, 19, 2, 7, 33, 0, 56, 52, 39, 42, 51, 15, 30, 36, 34, 37, 13, 43, 45, 46, 41, 49, 27, 48, 50, 44, 31, 38, 47, 1, 63, 57, 65, 58, 35], 'cur_cost': 60342.0}, {'tour': [54, 17, 13, 37, 16, 36, 15, 5, 60, 23, 9, 53, 57, 61, 8, 14, 11, 62, 56, 22, 12, 7, 24, 1, 2, 59, 3, 4, 64, 21, 32, 10, 19, 31, 0, 63, 44, 49, 48, 38, 40, 34, 27, 18, 30, 47, 50, 42, 26, 20, 39, 51, 35, 28, 43, 41, 29, 25, 6, 58, 55, 65, 52, 46, 45, 33], 'cur_cost': 71173.0}, {'tour': array([15, 56, 11, 29, 30, 62, 51, 32, 43, 14, 57, 18, 21,  3,  1, 24,  9,
       22, 53,  0, 26, 38, 13, 31, 23, 37,  8, 17,  5, 36, 49, 41, 34,  2,
       63,  4, 55, 59, 50, 25, 33, 45, 40, 60, 64, 16, 47,  6, 58, 39, 65,
       12, 61, 46,  7, 52, 20, 27, 28, 10, 48, 35, 44, 42, 19, 54],
      dtype=int64), 'cur_cost': 104850.0}, {'tour': [60, 63, 17, 30, 11, 55, 23, 35, 18, 8, 10, 61, 62, 57, 9, 59, 3, 14, 34, 15, 19, 21, 22, 5, 1, 54, 4, 36, 37, 24, 40, 46, 20, 27, 32, 2, 0, 16, 33, 49, 42, 50, 38, 44, 45, 47, 51, 25, 43, 13, 6, 64, 53, 12, 48, 41, 28, 7, 52, 56, 58, 65, 39, 26, 31, 29], 'cur_cost': 61947.0}, {'tour': [0, 7, 6, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12410.0}, {'tour': array([63, 50, 30, 39, 15, 37, 10, 52, 61,  4, 64, 56, 33,  7, 29,  3, 27,
       17, 41, 45, 11,  1, 18, 32, 53, 36, 12, 35, 59, 38, 20, 58,  9, 54,
       24, 34, 16, 42, 31, 43, 47, 25, 19,  5, 48, 44, 60, 49, 22, 62, 14,
        6, 28, 21, 26, 51, 57, 55,  0, 40,  8, 46, 65, 13, 23,  2],
      dtype=int64), 'cur_cost': 106924.0}, {'tour': [59, 5, 22, 13, 12, 17, 28, 18, 14, 52, 35, 27, 24, 7, 19, 26, 9, 11, 8, 53, 43, 45, 34, 15, 30, 31, 29, 41, 40, 51, 50, 47, 58, 57, 20, 3, 49, 56, 4, 32, 54, 36, 42, 37, 16, 0, 65, 10, 63, 60, 23, 6, 61, 44, 38, 21, 1, 25, 48, 2, 55, 39, 46, 33, 64, 62], 'cur_cost': 82455.0}, {'tour': [39, 23, 7, 56, 0, 52, 3, 15, 20, 25, 13, 29, 11, 17, 1, 61, 58, 5, 18, 19, 32, 33, 21, 9, 16, 2, 12, 49, 46, 42, 50, 43, 44, 47, 51, 34, 36, 37, 8, 54, 14, 4, 59, 6, 63, 57, 60, 48, 26, 31, 40, 22, 35, 30, 27, 38, 10, 53, 64, 62, 65, 41, 45, 28, 24, 55], 'cur_cost': 66618.0}, {'tour': array([60, 21, 11,  0, 45, 58, 25, 61,  4, 64, 40, 27,  5, 23, 33, 35, 44,
       14, 12, 34, 39, 65,  7, 56, 43, 15, 42, 41, 19, 16, 37, 52, 24, 18,
       51, 54, 59, 10,  9, 20, 36, 28,  3, 30, 32, 26,  2,  8,  1, 55, 48,
       57, 50,  6, 13, 62, 17, 47, 46, 31, 29, 38, 63, 53, 49, 22],
      dtype=int64), 'cur_cost': 99010.0}, {'tour': [2, 63, 16, 23, 55, 37, 53, 19, 17, 25, 22, 11, 56, 4, 27, 1, 29, 7, 10, 14, 49, 52, 45, 51, 18, 46, 41, 47, 42, 64, 65, 5, 57, 3, 54, 26, 12, 61, 24, 59, 60, 43, 20, 39, 6, 34, 28, 40, 35, 33, 15, 13, 31, 50, 21, 8, 62, 48, 32, 30, 36, 9, 44, 38, 58, 0], 'cur_cost': 92891.0}, {'tour': [0, 3, 11, 23, 16, 18, 12, 22, 15, 14, 20, 21, 13, 19, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12391.0}, {'tour': array([26, 15, 63, 18, 41,  0, 14, 33, 64, 47, 34, 56,  6, 13, 48, 51,  2,
        1, 61, 58, 23, 37, 59, 52, 20, 12, 60, 43, 42,  4, 11, 49, 32, 35,
       50, 29,  5, 46, 36, 16, 17, 62, 57, 55, 38, 31, 30, 22, 45, 65, 40,
       44, 19,  9,  3,  7, 28, 21,  8, 27, 54, 39, 24, 10, 25, 53],
      dtype=int64), 'cur_cost': 104485.0}, {'tour': array([50,  5, 11, 55, 13, 64, 59, 12, 42, 25, 49, 61, 24, 53, 38, 58, 60,
       44, 30, 33, 15, 29,  7,  8,  0, 20, 18, 62, 54, 10, 28, 41, 47, 23,
       32, 19, 26,  6,  9, 14, 31, 65,  4, 37, 48, 51,  3, 63, 39, 16, 34,
       40, 27, 57,  1, 45,  2, 43, 36, 17, 22, 35, 46, 52, 21, 56],
      dtype=int64), 'cur_cost': 106037.0}, {'tour': array([57,  7, 63, 41,  1, 64, 36,  8, 56, 55, 10, 43, 34, 62, 30, 61,  4,
       22, 14, 52, 32, 19, 29, 49, 28, 25, 15, 54, 13, 18,  2, 20,  6, 16,
       48, 33, 17, 27, 37, 44, 31, 35, 60, 24, 38, 65,  9, 53, 47, 21, 45,
       12, 39,  3, 23, 51, 50, 46, 59, 58, 40, 11,  5,  0, 42, 26],
      dtype=int64), 'cur_cost': 105781.0}, {'tour': array([37, 36,  7, 23, 27, 13, 63, 60, 52, 62, 43, 58, 22, 35, 20, 54, 55,
       16, 48, 30, 15,  8, 24, 61, 12, 42, 31, 40,  1, 45,  2, 18, 14,  9,
       19, 53,  0, 21, 32, 11, 28, 41,  3, 51, 46, 34, 47, 25, 26, 49, 57,
        6, 59, 17, 56, 29, 65, 44, 50, 10, 64, 39,  5,  4, 38, 33],
      dtype=int64), 'cur_cost': 107641.0}]
2025-08-03 17:05:48,467 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-03 17:05:48,467 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-03 17:05:48,468 - experts.management.collaboration_manager - INFO - 个体 16 利用路径生成报告: {'new_tour': array([26, 15, 63, 18, 41,  0, 14, 33, 64, 47, 34, 56,  6, 13, 48, 51,  2,
        1, 61, 58, 23, 37, 59, 52, 20, 12, 60, 43, 42,  4, 11, 49, 32, 35,
       50, 29,  5, 46, 36, 16, 17, 62, 57, 55, 38, 31, 30, 22, 45, 65, 40,
       44, 19,  9,  3,  7, 28, 21,  8, 27, 54, 39, 24, 10, 25, 53],
      dtype=int64), 'cur_cost': 104485.0}
2025-08-03 17:05:48,468 - experts.management.collaboration_manager - INFO - 个体 16 接受新路径 (成本: 104485.00)
2025-08-03 17:05:48,469 - experts.management.collaboration_manager - INFO - 为个体 17 生成探索路径
2025-08-03 17:05:48,469 - ExplorationExpert - INFO - 开始为个体 17 生成探索路径（算法实现）
2025-08-03 17:05:48,470 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:48,474 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 17:05:48,474 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:48,475 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14142.0, 路径长度: 66
2025-08-03 17:05:48,475 - experts.management.collaboration_manager - INFO - 个体 17 探索路径生成报告: {'new_tour': [0, 24, 12, 22, 23, 16, 18, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14142.0}
2025-08-03 17:05:48,476 - experts.management.collaboration_manager - INFO - 个体 17 接受新路径 (成本: 14142.00)
2025-08-03 17:05:48,476 - experts.management.collaboration_manager - INFO - 为个体 18 生成探索路径
2025-08-03 17:05:48,476 - ExplorationExpert - INFO - 开始为个体 18 生成探索路径（算法实现）
2025-08-03 17:05:48,477 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:48,484 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 17:05:48,485 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:48,486 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14721.0, 路径长度: 66
2025-08-03 17:05:48,487 - experts.management.collaboration_manager - INFO - 个体 18 探索路径生成报告: {'new_tour': [0, 12, 9, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 16, 23, 22, 15, 14, 20, 21, 13, 19, 17, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14721.0}
2025-08-03 17:05:48,487 - experts.management.collaboration_manager - INFO - 个体 18 接受新路径 (成本: 14721.00)
2025-08-03 17:05:48,488 - experts.management.collaboration_manager - INFO - 为个体 19 生成利用路径
2025-08-03 17:05:48,488 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:05:48,488 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:05:48,489 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 19 处的路径，新成本: 102730.0
2025-08-03 17:05:48,575 - ExploitationExpert - INFO - res_population_num: 8
2025-08-03 17:05:48,575 - ExploitationExpert - INFO - res_population_costs: [9857.0, 9594.0, 9528.0, 9527, 9527, 9527, 9522, 9521]
2025-08-03 17:05:48,576 - ExploitationExpert - INFO - res_populations: [array([ 0,  1, 11,  9,  3,  7, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14, 63, 52, 65, 54, 57,
       64, 60, 58, 56, 59, 62, 53, 61, 55,  5,  4,  6,  2,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-03 17:05:48,583 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:05:48,583 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 2, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12368.0}, {'tour': array([48, 22, 15, 13, 56, 27, 41, 53, 10, 16, 59, 45, 31, 30,  8, 26, 36,
       43, 58, 17, 29, 25, 52, 18, 63, 65, 62,  2, 46, 51,  5, 64,  3, 37,
        4,  6, 33, 21, 61, 44, 40, 32, 39, 28,  7, 11, 55, 50, 14, 34, 57,
       24, 35, 54, 60,  9,  0, 38, 49,  1, 23, 42, 47, 12, 19, 20],
      dtype=int64), 'cur_cost': 95662.0}, {'tour': [60, 59, 54, 63, 10, 65, 62, 55, 12, 17, 37, 28, 21, 18, 11, 61, 9, 52, 22, 2, 56, 4, 27, 1, 36, 15, 5, 3, 13, 19, 7, 58, 23, 25, 14, 0, 53, 43, 50, 45, 42, 46, 48, 16, 31, 26, 30, 29, 32, 20, 34, 33, 40, 49, 51, 44, 47, 41, 38, 35, 8, 6, 57, 64, 39, 24], 'cur_cost': 58667.0}, {'tour': [0, 21, 16, 12, 22, 23, 13, 20, 14, 15, 17, 18, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12411.0}, {'tour': array([ 3,  8,  4, 18,  2, 20, 48, 65, 28, 24, 47, 50, 64, 63, 55, 36, 22,
       62, 49, 30, 21, 43, 34,  5, 44,  1, 11, 60, 16, 39, 31, 52, 42, 14,
       19, 17, 61, 37,  7, 41, 32, 25, 13, 53, 26, 51, 40, 45, 56, 12, 23,
       15, 58, 35, 38,  0, 57, 27,  9, 59, 10, 46,  6, 29, 54, 33],
      dtype=int64), 'cur_cost': 109966.0}, {'tour': [64, 54, 10, 5, 3, 59, 6, 9, 61, 18, 4, 12, 28, 24, 29, 22, 32, 17, 16, 11, 55, 60, 14, 8, 53, 62, 40, 20, 23, 21, 26, 25, 19, 2, 7, 33, 0, 56, 52, 39, 42, 51, 15, 30, 36, 34, 37, 13, 43, 45, 46, 41, 49, 27, 48, 50, 44, 31, 38, 47, 1, 63, 57, 65, 58, 35], 'cur_cost': 60342.0}, {'tour': [54, 17, 13, 37, 16, 36, 15, 5, 60, 23, 9, 53, 57, 61, 8, 14, 11, 62, 56, 22, 12, 7, 24, 1, 2, 59, 3, 4, 64, 21, 32, 10, 19, 31, 0, 63, 44, 49, 48, 38, 40, 34, 27, 18, 30, 47, 50, 42, 26, 20, 39, 51, 35, 28, 43, 41, 29, 25, 6, 58, 55, 65, 52, 46, 45, 33], 'cur_cost': 71173.0}, {'tour': array([15, 56, 11, 29, 30, 62, 51, 32, 43, 14, 57, 18, 21,  3,  1, 24,  9,
       22, 53,  0, 26, 38, 13, 31, 23, 37,  8, 17,  5, 36, 49, 41, 34,  2,
       63,  4, 55, 59, 50, 25, 33, 45, 40, 60, 64, 16, 47,  6, 58, 39, 65,
       12, 61, 46,  7, 52, 20, 27, 28, 10, 48, 35, 44, 42, 19, 54],
      dtype=int64), 'cur_cost': 104850.0}, {'tour': [60, 63, 17, 30, 11, 55, 23, 35, 18, 8, 10, 61, 62, 57, 9, 59, 3, 14, 34, 15, 19, 21, 22, 5, 1, 54, 4, 36, 37, 24, 40, 46, 20, 27, 32, 2, 0, 16, 33, 49, 42, 50, 38, 44, 45, 47, 51, 25, 43, 13, 6, 64, 53, 12, 48, 41, 28, 7, 52, 56, 58, 65, 39, 26, 31, 29], 'cur_cost': 61947.0}, {'tour': [0, 7, 6, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12410.0}, {'tour': array([63, 50, 30, 39, 15, 37, 10, 52, 61,  4, 64, 56, 33,  7, 29,  3, 27,
       17, 41, 45, 11,  1, 18, 32, 53, 36, 12, 35, 59, 38, 20, 58,  9, 54,
       24, 34, 16, 42, 31, 43, 47, 25, 19,  5, 48, 44, 60, 49, 22, 62, 14,
        6, 28, 21, 26, 51, 57, 55,  0, 40,  8, 46, 65, 13, 23,  2],
      dtype=int64), 'cur_cost': 106924.0}, {'tour': [59, 5, 22, 13, 12, 17, 28, 18, 14, 52, 35, 27, 24, 7, 19, 26, 9, 11, 8, 53, 43, 45, 34, 15, 30, 31, 29, 41, 40, 51, 50, 47, 58, 57, 20, 3, 49, 56, 4, 32, 54, 36, 42, 37, 16, 0, 65, 10, 63, 60, 23, 6, 61, 44, 38, 21, 1, 25, 48, 2, 55, 39, 46, 33, 64, 62], 'cur_cost': 82455.0}, {'tour': [39, 23, 7, 56, 0, 52, 3, 15, 20, 25, 13, 29, 11, 17, 1, 61, 58, 5, 18, 19, 32, 33, 21, 9, 16, 2, 12, 49, 46, 42, 50, 43, 44, 47, 51, 34, 36, 37, 8, 54, 14, 4, 59, 6, 63, 57, 60, 48, 26, 31, 40, 22, 35, 30, 27, 38, 10, 53, 64, 62, 65, 41, 45, 28, 24, 55], 'cur_cost': 66618.0}, {'tour': array([60, 21, 11,  0, 45, 58, 25, 61,  4, 64, 40, 27,  5, 23, 33, 35, 44,
       14, 12, 34, 39, 65,  7, 56, 43, 15, 42, 41, 19, 16, 37, 52, 24, 18,
       51, 54, 59, 10,  9, 20, 36, 28,  3, 30, 32, 26,  2,  8,  1, 55, 48,
       57, 50,  6, 13, 62, 17, 47, 46, 31, 29, 38, 63, 53, 49, 22],
      dtype=int64), 'cur_cost': 99010.0}, {'tour': [2, 63, 16, 23, 55, 37, 53, 19, 17, 25, 22, 11, 56, 4, 27, 1, 29, 7, 10, 14, 49, 52, 45, 51, 18, 46, 41, 47, 42, 64, 65, 5, 57, 3, 54, 26, 12, 61, 24, 59, 60, 43, 20, 39, 6, 34, 28, 40, 35, 33, 15, 13, 31, 50, 21, 8, 62, 48, 32, 30, 36, 9, 44, 38, 58, 0], 'cur_cost': 92891.0}, {'tour': [0, 3, 11, 23, 16, 18, 12, 22, 15, 14, 20, 21, 13, 19, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12391.0}, {'tour': array([26, 15, 63, 18, 41,  0, 14, 33, 64, 47, 34, 56,  6, 13, 48, 51,  2,
        1, 61, 58, 23, 37, 59, 52, 20, 12, 60, 43, 42,  4, 11, 49, 32, 35,
       50, 29,  5, 46, 36, 16, 17, 62, 57, 55, 38, 31, 30, 22, 45, 65, 40,
       44, 19,  9,  3,  7, 28, 21,  8, 27, 54, 39, 24, 10, 25, 53],
      dtype=int64), 'cur_cost': 104485.0}, {'tour': [0, 24, 12, 22, 23, 16, 18, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14142.0}, {'tour': [0, 12, 9, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 16, 23, 22, 15, 14, 20, 21, 13, 19, 17, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14721.0}, {'tour': array([43, 64, 65, 46, 39, 32, 54,  1, 63, 49, 53,  2, 61, 57, 59, 12, 37,
       40, 45, 15, 20, 48,  4, 21, 60,  7, 35,  6, 25, 19, 13,  8, 33, 56,
        5, 62, 44, 16, 30, 47, 10, 51, 52, 28, 34,  3, 29, 50, 42,  9, 23,
       36, 18, 26, 58, 31, 22, 55, 11,  0, 38, 41, 27, 17, 24, 14],
      dtype=int64), 'cur_cost': 102730.0}]
2025-08-03 17:05:48,592 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 17:05:48,592 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-03 17:05:48,593 - experts.management.collaboration_manager - INFO - 个体 19 利用路径生成报告: {'new_tour': array([43, 64, 65, 46, 39, 32, 54,  1, 63, 49, 53,  2, 61, 57, 59, 12, 37,
       40, 45, 15, 20, 48,  4, 21, 60,  7, 35,  6, 25, 19, 13,  8, 33, 56,
        5, 62, 44, 16, 30, 47, 10, 51, 52, 28, 34,  3, 29, 50, 42,  9, 23,
       36, 18, 26, 58, 31, 22, 55, 11,  0, 38, 41, 27, 17, 24, 14],
      dtype=int64), 'cur_cost': 102730.0}
2025-08-03 17:05:48,594 - experts.management.collaboration_manager - INFO - 个体 19 接受新路径 (成本: 102730.00)
2025-08-03 17:05:48,594 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 16, 'rejected': 4, 'elite_protected': 4}
2025-08-03 17:05:48,595 - experts.management.collaboration_manager - INFO - 接受率: 16/20 (80.0%)
2025-08-03 17:05:48,596 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 2, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12368.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([48, 22, 15, 13, 56, 27, 41, 53, 10, 16, 59, 45, 31, 30,  8, 26, 36,
       43, 58, 17, 29, 25, 52, 18, 63, 65, 62,  2, 46, 51,  5, 64,  3, 37,
        4,  6, 33, 21, 61, 44, 40, 32, 39, 28,  7, 11, 55, 50, 14, 34, 57,
       24, 35, 54, 60,  9,  0, 38, 49,  1, 23, 42, 47, 12, 19, 20],
      dtype=int64), 'cur_cost': 95662.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [60, 59, 54, 63, 10, 65, 62, 55, 12, 17, 37, 28, 21, 18, 11, 61, 9, 52, 22, 2, 56, 4, 27, 1, 36, 15, 5, 3, 13, 19, 7, 58, 23, 25, 14, 0, 53, 43, 50, 45, 42, 46, 48, 16, 31, 26, 30, 29, 32, 20, 34, 33, 40, 49, 51, 44, 47, 41, 38, 35, 8, 6, 57, 64, 39, 24], 'cur_cost': 58667.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 16, 12, 22, 23, 13, 20, 14, 15, 17, 18, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12411.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 3,  8,  4, 18,  2, 20, 48, 65, 28, 24, 47, 50, 64, 63, 55, 36, 22,
       62, 49, 30, 21, 43, 34,  5, 44,  1, 11, 60, 16, 39, 31, 52, 42, 14,
       19, 17, 61, 37,  7, 41, 32, 25, 13, 53, 26, 51, 40, 45, 56, 12, 23,
       15, 58, 35, 38,  0, 57, 27,  9, 59, 10, 46,  6, 29, 54, 33],
      dtype=int64), 'cur_cost': 109966.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [64, 54, 10, 5, 3, 59, 6, 9, 61, 18, 4, 12, 28, 24, 29, 22, 32, 17, 16, 11, 55, 60, 14, 8, 53, 62, 40, 20, 23, 21, 26, 25, 19, 2, 7, 33, 0, 56, 52, 39, 42, 51, 15, 30, 36, 34, 37, 13, 43, 45, 46, 41, 49, 27, 48, 50, 44, 31, 38, 47, 1, 63, 57, 65, 58, 35], 'cur_cost': 60342.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [54, 17, 13, 37, 16, 36, 15, 5, 60, 23, 9, 53, 57, 61, 8, 14, 11, 62, 56, 22, 12, 7, 24, 1, 2, 59, 3, 4, 64, 21, 32, 10, 19, 31, 0, 63, 44, 49, 48, 38, 40, 34, 27, 18, 30, 47, 50, 42, 26, 20, 39, 51, 35, 28, 43, 41, 29, 25, 6, 58, 55, 65, 52, 46, 45, 33], 'cur_cost': 71173.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([15, 56, 11, 29, 30, 62, 51, 32, 43, 14, 57, 18, 21,  3,  1, 24,  9,
       22, 53,  0, 26, 38, 13, 31, 23, 37,  8, 17,  5, 36, 49, 41, 34,  2,
       63,  4, 55, 59, 50, 25, 33, 45, 40, 60, 64, 16, 47,  6, 58, 39, 65,
       12, 61, 46,  7, 52, 20, 27, 28, 10, 48, 35, 44, 42, 19, 54],
      dtype=int64), 'cur_cost': 104850.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [60, 63, 17, 30, 11, 55, 23, 35, 18, 8, 10, 61, 62, 57, 9, 59, 3, 14, 34, 15, 19, 21, 22, 5, 1, 54, 4, 36, 37, 24, 40, 46, 20, 27, 32, 2, 0, 16, 33, 49, 42, 50, 38, 44, 45, 47, 51, 25, 43, 13, 6, 64, 53, 12, 48, 41, 28, 7, 52, 56, 58, 65, 39, 26, 31, 29], 'cur_cost': 61947.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 6, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12410.0}}, {'individual': 10, 'strategy': 'exploit', 'path_data': {'new_tour': array([63, 50, 30, 39, 15, 37, 10, 52, 61,  4, 64, 56, 33,  7, 29,  3, 27,
       17, 41, 45, 11,  1, 18, 32, 53, 36, 12, 35, 59, 38, 20, 58,  9, 54,
       24, 34, 16, 42, 31, 43, 47, 25, 19,  5, 48, 44, 60, 49, 22, 62, 14,
        6, 28, 21, 26, 51, 57, 55,  0, 40,  8, 46, 65, 13, 23,  2],
      dtype=int64), 'cur_cost': 106924.0}}, {'individual': 11, 'strategy': 'explore', 'path_data': {'new_tour': [59, 5, 22, 13, 12, 17, 28, 18, 14, 52, 35, 27, 24, 7, 19, 26, 9, 11, 8, 53, 43, 45, 34, 15, 30, 31, 29, 41, 40, 51, 50, 47, 58, 57, 20, 3, 49, 56, 4, 32, 54, 36, 42, 37, 16, 0, 65, 10, 63, 60, 23, 6, 61, 44, 38, 21, 1, 25, 48, 2, 55, 39, 46, 33, 64, 62], 'cur_cost': 82455.0}}, {'individual': 12, 'strategy': 'explore', 'path_data': {'new_tour': [39, 23, 7, 56, 0, 52, 3, 15, 20, 25, 13, 29, 11, 17, 1, 61, 58, 5, 18, 19, 32, 33, 21, 9, 16, 2, 12, 49, 46, 42, 50, 43, 44, 47, 51, 34, 36, 37, 8, 54, 14, 4, 59, 6, 63, 57, 60, 48, 26, 31, 40, 22, 35, 30, 27, 38, 10, 53, 64, 62, 65, 41, 45, 28, 24, 55], 'cur_cost': 66618.0}}, {'individual': 13, 'strategy': 'exploit', 'path_data': {'new_tour': array([60, 21, 11,  0, 45, 58, 25, 61,  4, 64, 40, 27,  5, 23, 33, 35, 44,
       14, 12, 34, 39, 65,  7, 56, 43, 15, 42, 41, 19, 16, 37, 52, 24, 18,
       51, 54, 59, 10,  9, 20, 36, 28,  3, 30, 32, 26,  2,  8,  1, 55, 48,
       57, 50,  6, 13, 62, 17, 47, 46, 31, 29, 38, 63, 53, 49, 22],
      dtype=int64), 'cur_cost': 99010.0}}, {'individual': 14, 'strategy': 'explore', 'path_data': {'new_tour': [2, 63, 16, 23, 55, 37, 53, 19, 17, 25, 22, 11, 56, 4, 27, 1, 29, 7, 10, 14, 49, 52, 45, 51, 18, 46, 41, 47, 42, 64, 65, 5, 57, 3, 54, 26, 12, 61, 24, 59, 60, 43, 20, 39, 6, 34, 28, 40, 35, 33, 15, 13, 31, 50, 21, 8, 62, 48, 32, 30, 36, 9, 44, 38, 58, 0], 'cur_cost': 92891.0}}, {'individual': 15, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 11, 23, 16, 18, 12, 22, 15, 14, 20, 21, 13, 19, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12391.0}}, {'individual': 16, 'strategy': 'exploit', 'path_data': {'new_tour': array([26, 15, 63, 18, 41,  0, 14, 33, 64, 47, 34, 56,  6, 13, 48, 51,  2,
        1, 61, 58, 23, 37, 59, 52, 20, 12, 60, 43, 42,  4, 11, 49, 32, 35,
       50, 29,  5, 46, 36, 16, 17, 62, 57, 55, 38, 31, 30, 22, 45, 65, 40,
       44, 19,  9,  3,  7, 28, 21,  8, 27, 54, 39, 24, 10, 25, 53],
      dtype=int64), 'cur_cost': 104485.0}}, {'individual': 17, 'strategy': 'explore', 'path_data': {'new_tour': [0, 24, 12, 22, 23, 16, 18, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14142.0}}, {'individual': 18, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 9, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 16, 23, 22, 15, 14, 20, 21, 13, 19, 17, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14721.0}}, {'individual': 19, 'strategy': 'exploit', 'path_data': {'new_tour': array([43, 64, 65, 46, 39, 32, 54,  1, 63, 49, 53,  2, 61, 57, 59, 12, 37,
       40, 45, 15, 20, 48,  4, 21, 60,  7, 35,  6, 25, 19, 13,  8, 33, 56,
        5, 62, 44, 16, 30, 47, 10, 51, 52, 28, 34,  3, 29, 50, 42,  9, 23,
       36, 18, 26, 58, 31, 22, 55, 11,  0, 38, 41, 27, 17, 24, 14],
      dtype=int64), 'cur_cost': 102730.0}}]
2025-08-03 17:05:48,599 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-03 17:05:48,600 - StatsExpert - INFO - 开始统计分析
2025-08-03 17:05:48,616 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=12368.000, 多样性=0.942
2025-08-03 17:05:48,617 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-03 17:05:48,617 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-03 17:05:48,617 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-03 17:05:48,618 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 1, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.003152320313370672, 'best_improvement': -0.2505561172901921}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.025260029717681917}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 8, 'new_count': 8, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.8647186147186147, 'new_diversity': 0.8647186147186147, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-03 17:05:48,621 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-03 17:05:48,623 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_solution.json
2025-08-03 17:05:48,624 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_20250803_170548.solution
2025-08-03 17:05:48,624 - __main__ - INFO - 实例 composite13_66 处理完成
