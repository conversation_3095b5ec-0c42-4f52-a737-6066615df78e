2025-08-03 17:08:49,067 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-08-03 17:08:49,067 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-03 17:08:49,069 - StatsExpert - INFO - 开始统计分析
2025-08-03 17:08:49,083 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=9859.000, 多样性=0.964
2025-08-03 17:08:49,089 - PathExpert - INFO - 开始路径结构分析
2025-08-03 17:08:49,096 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.964
2025-08-03 17:08:49,140 - EliteExpert - INFO - 开始精英解分析
2025-08-03 17:08:49,144 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/1)
2025-08-03 17:08:49,144 - LandscapeExpert - INFO - 使用直接传入的种群数据: 20个个体
2025-08-03 17:08:49,144 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-03 17:08:49,145 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-03 17:08:49,404 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.150, 适应度梯度: -3158.350, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 1.000
2025-08-03 17:08:49,405 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-03 17:08:49,405 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-03 17:08:49,474 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记
2025-08-03 17:08:49,793 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_1_20250803_170849.html
2025-08-03 17:08:49,853 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_1_20250803_170849.html
2025-08-03 17:08:49,853 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-03 17:08:49,853 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-03 17:08:49,854 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.7101秒
2025-08-03 17:08:49,854 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-03 17:08:49,854 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.15, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -3158.35, 'local_optima_density': 0.15, 'gradient_variance': 1796910799.9235, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0019, 'fitness_entropy': 0.8238654610595805, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -3158.350)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 1, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754212129.4059136, 'performance_metrics': {}}}
2025-08-03 17:08:49,855 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-03 17:08:49,856 - experts.strategy.enhanced_strategy_expert - INFO - 开始增强策略分配分析 (迭代 0)
2025-08-03 17:08:49,856 - experts.analysis.individual_state_analyzer - INFO - 开始分析种群状态 (迭代 0, 种群大小: 20)
2025-08-03 17:08:49,856 - experts.strategy.enhanced_strategy_expert - ERROR - 策略分配过程中发生错误: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 17:08:49,857 - experts.strategy.enhanced_strategy_expert - WARNING - 使用回退策略分配
2025-08-03 17:08:49,857 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 17:08:49,857 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 17:08:49,858 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-03 17:08:49,858 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 17:08:49,858 - __main__ - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 17:08:49,859 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-03 17:08:49,859 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 2, 3, 5} (总数: 4, 保护比例: 0.20)
2025-08-03 17:08:49,860 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-03 17:08:49,860 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-03 17:08:49,860 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:08:49,866 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 17:08:49,866 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:08:50,042 - ExplorationExpert - INFO - 探索路径生成完成，成本: 109041.0, 路径长度: 66
2025-08-03 17:08:50,042 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [1, 62, 21, 12, 34, 41, 58, 18, 35, 61, 2, 33, 60, 51, 49, 57, 48, 8, 55, 7, 59, 53, 50, 0, 5, 32, 43, 56, 4, 29, 52, 16, 19, 38, 40, 44, 20, 3, 23, 25, 14, 15, 11, 36, 39, 27, 24, 45, 37, 22, 46, 10, 64, 31, 6, 13, 54, 28, 9, 17, 65, 26, 47, 42, 30, 63], 'cur_cost': 109041.0}
2025-08-03 17:08:50,043 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 109041.00)
2025-08-03 17:08:50,043 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-03 17:08:50,043 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:08:50,045 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:08:50,046 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 98828.0
2025-08-03 17:08:52,055 - ExploitationExpert - INFO - res_population_num: 1
2025-08-03 17:08:52,056 - ExploitationExpert - INFO - res_population_costs: [9887.0]
2025-08-03 17:08:52,056 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 54, 57, 64, 53, 62,
       59, 56, 58, 60, 55, 61, 65, 52, 63,  2,  8,  5,  4,  6, 10],
      dtype=int64)]
2025-08-03 17:08:52,057 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:08:52,057 - ExploitationExpert - INFO - populations: [{'tour': [1, 62, 21, 12, 34, 41, 58, 18, 35, 61, 2, 33, 60, 51, 49, 57, 48, 8, 55, 7, 59, 53, 50, 0, 5, 32, 43, 56, 4, 29, 52, 16, 19, 38, 40, 44, 20, 3, 23, 25, 14, 15, 11, 36, 39, 27, 24, 45, 37, 22, 46, 10, 64, 31, 6, 13, 54, 28, 9, 17, 65, 26, 47, 42, 30, 63], 'cur_cost': 109041.0}, {'tour': array([36, 25, 28, 37,  4, 22, 51,  6,  0, 40, 45,  1, 19, 55, 58,  8, 34,
       44, 10, 57, 12, 39, 35, 50, 41, 27, 13, 64,  7, 48,  5, 43, 49, 18,
       65, 63, 14, 62, 33, 32, 53, 42, 47, 17, 30, 20,  3, 54, 31, 61, 60,
       11, 56, 24,  2, 59, 21,  9, 52, 46, 26, 38, 15, 29, 23, 16],
      dtype=int64), 'cur_cost': 98828.0}, {'tour': array([14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36,
       35, 28, 30, 34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,
        2,  6, 10,  0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9945.0}, {'tour': array([14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36,
       35, 28, 30, 34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,
        2,  6, 10,  0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9945.0}, {'tour': array([55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63,  2,  8,  5,
        4,  6,  9, 11,  7,  3,  1,  0, 10, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29,
       32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42],
      dtype=int64), 'cur_cost': 10045.0}, {'tour': array([30, 28, 33, 25, 26, 36, 37, 31, 24, 29, 32, 35, 34, 27, 18, 16, 23,
       22, 12, 17, 15, 14, 20, 21, 13, 19,  9, 11,  7,  3,  1,  0, 10,  8,
        2,  6,  4,  5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9890.0}, {'tour': array([41, 53, 49, 58, 43,  9, 60, 21, 65, 46, 14, 23,  8, 28, 26, 22, 13,
       36, 34, 18, 54, 35, 16, 39,  7, 52, 19, 64, 45,  4, 31, 61, 20, 25,
       51, 12,  5, 15, 59, 40, 10, 29, 11, 42, 57, 17,  6, 47, 55, 32,  1,
        3, 50, 56, 63, 44, 27, 48, 37, 24, 30, 33, 38,  2, 62,  0],
      dtype=int64), 'cur_cost': 118526.0}, {'tour': array([53, 24, 12, 41, 46, 11, 64, 26, 29, 10, 15, 48, 59, 58, 27, 44,  8,
       47, 60, 16, 23, 20, 13, 19, 35, 42, 49, 43, 39, 54, 32, 36, 28, 55,
        1,  9, 45, 18, 21, 33,  3, 63,  4, 51, 31,  0, 65, 62, 52, 30, 37,
        2,  7, 14, 50, 40, 34, 38,  5, 57, 17,  6, 56, 22, 25, 61],
      dtype=int64), 'cur_cost': 90927.0}, {'tour': array([27, 24, 37, 63, 44, 59, 43,  8, 40, 10, 41,  2, 46, 20, 30, 52, 28,
       14, 25, 13, 54,  7, 35, 16, 48, 33, 42, 32, 56, 58, 53, 12,  4, 19,
       57,  1, 39, 65, 36, 61, 23, 22,  3, 21, 34, 45, 38, 49,  5, 50,  0,
        6, 60, 47, 15, 11,  9, 26, 64, 17, 18, 31, 55, 62, 51, 29],
      dtype=int64), 'cur_cost': 112604.0}, {'tour': array([50, 38,  7, 54, 32, 27, 17,  8, 34,  5,  4, 59, 31, 30, 45, 49,  9,
       12, 42, 57, 61, 53, 41, 21,  1, 62, 43, 52,  2, 60, 16, 20, 15, 65,
       19, 22, 51,  3, 36, 13, 39, 23, 46, 48, 18, 11, 64, 10, 24, 14,  0,
       25, 29, 63, 33, 58, 35, 56, 40,  6, 28, 47, 26, 44, 55, 37],
      dtype=int64), 'cur_cost': 106452.0}, {'tour': array([30,  9, 28,  8, 32, 64, 53, 50, 48, 52, 37, 10, 14,  0,  2, 63, 13,
       40, 62, 36,  5, 45, 29, 46,  7, 65, 17, 56,  6, 43, 24, 47, 31, 21,
       22, 42, 12, 39,  3, 61, 18, 20, 23, 51, 26, 54, 25, 35, 58, 49,  1,
       27, 19, 11, 34, 44, 57, 59, 15, 38, 55, 60, 16,  4, 33, 41],
      dtype=int64), 'cur_cost': 116456.0}, {'tour': array([39, 48, 57, 56, 51, 11, 16, 61, 41, 47,  0, 29, 20, 52, 64, 34, 36,
       10, 45, 53, 49, 62, 18,  8, 22, 17, 43,  1, 55, 42,  6, 37,  9, 12,
       35, 26, 19, 14, 15, 28, 33, 46, 30, 60, 65, 21, 32, 54, 59, 24, 44,
       13,  3,  5, 40, 50, 25, 31,  4, 38,  2, 63, 58, 23,  7, 27],
      dtype=int64), 'cur_cost': 103219.0}, {'tour': array([11,  6, 18, 29,  8, 31, 54, 44, 59, 40, 39, 58, 48,  4, 57, 32, 10,
       37, 63,  7, 60, 61, 45, 38, 28, 65,  2, 52, 12, 13, 35, 56, 43, 33,
       26, 46, 62,  0, 47, 36, 17, 25, 50,  9, 41, 20, 55, 22, 16, 15, 19,
        1, 53,  3, 30, 42, 21, 34, 23, 24, 51, 64, 14, 49, 27,  5],
      dtype=int64), 'cur_cost': 110642.0}, {'tour': array([ 9, 38, 22, 27,  7,  3, 23, 10, 45, 19, 18, 47, 34, 50, 26, 37, 33,
       12, 14, 41, 15, 30,  5, 51, 32, 57, 28, 25,  8, 35, 62, 31, 52, 54,
       17, 64, 29, 24, 63, 53, 21, 60, 13, 39, 11, 48, 59, 61, 49, 56,  0,
       43,  4, 20, 55, 65, 44,  6, 36, 40, 58, 46,  1, 42, 16,  2],
      dtype=int64), 'cur_cost': 114932.0}, {'tour': array([64, 20, 47, 17, 53, 48, 60, 42, 62, 16, 59, 13, 63, 14, 45, 41, 29,
       51,  7, 11,  9, 56, 22,  0,  3, 50, 25, 27, 40, 30, 28,  6, 44, 26,
       35, 52, 12, 33, 18, 38, 15, 57,  5, 21, 55, 39, 37, 49, 58,  4, 23,
       43, 32, 61, 34, 19,  1,  2, 46, 10, 36, 31, 54,  8, 65, 24],
      dtype=int64), 'cur_cost': 114332.0}, {'tour': array([10, 22, 53,  3, 15, 12, 21, 35, 50,  7, 43, 64, 30, 11, 27, 52, 32,
       62, 31, 58,  1, 20, 37, 38, 49, 44, 33, 17,  6, 47, 51, 42, 26, 16,
        2, 56,  4, 34, 24, 61, 46,  0, 29, 65, 41,  5, 39, 25, 28, 45, 18,
       54, 48, 36,  9, 23, 14, 63,  8, 57, 59, 13, 55, 60, 40, 19],
      dtype=int64), 'cur_cost': 110408.0}, {'tour': array([53, 63, 21,  1, 34, 32, 15,  0, 42, 24,  4, 49, 28, 10, 46, 58, 64,
       26, 33, 52, 41, 55,  2, 19, 43, 35, 37, 57,  9, 20, 44, 56, 16,  6,
       13, 25, 36, 29, 11, 60, 39, 18, 17, 40,  7,  5, 47, 31, 38, 48, 54,
       45, 22, 65, 12,  8, 23,  3, 59, 61, 51, 30, 50, 14, 62, 27],
      dtype=int64), 'cur_cost': 109197.0}, {'tour': array([49, 14, 25, 62, 44, 43, 30, 54, 38, 34, 24, 35, 40, 31, 50, 46, 63,
       19,  3,  1, 17, 29, 47, 11, 58,  0, 39, 42, 12,  2, 36, 13, 59, 27,
        6, 60, 22,  9, 41, 65, 18, 51, 21,  7, 56, 20, 48,  4, 23, 61, 28,
       45, 57,  5, 10, 16, 52, 33, 26, 53, 15,  8, 55, 64, 37, 32],
      dtype=int64), 'cur_cost': 108762.0}, {'tour': array([62, 39, 33, 13,  8, 40, 41,  2, 21, 55, 56, 25, 57, 46, 53, 58,  0,
       12,  5, 43, 59, 16, 15, 47, 35, 27, 42,  6, 60, 14, 30, 22, 26, 37,
       64, 17, 50, 65,  4, 29, 48,  7, 32,  1, 45, 44, 23, 11, 51, 18, 49,
       54,  9, 34, 38, 19, 61, 10, 28, 63, 36, 24, 31,  3, 20, 52],
      dtype=int64), 'cur_cost': 110062.0}, {'tour': array([10, 33, 45, 43, 59, 23, 26, 16, 63, 62, 22,  6, 64, 34, 24,  4, 40,
       50, 37, 46, 17, 32, 11, 65, 47,  5, 15, 42, 51, 25, 31, 49, 48,  3,
       28, 38, 30, 44, 35, 54, 56, 52,  8, 61, 14,  0, 29, 20, 19, 55, 21,
        9,  7, 60, 39, 12, 53,  2, 13, 58, 27, 18, 36,  1, 41, 57],
      dtype=int64), 'cur_cost': 102178.0}]
2025-08-03 17:08:52,068 - ExploitationExpert - INFO - 局部搜索耗时: 2.02秒
2025-08-03 17:08:52,069 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-03 17:08:52,070 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([36, 25, 28, 37,  4, 22, 51,  6,  0, 40, 45,  1, 19, 55, 58,  8, 34,
       44, 10, 57, 12, 39, 35, 50, 41, 27, 13, 64,  7, 48,  5, 43, 49, 18,
       65, 63, 14, 62, 33, 32, 53, 42, 47, 17, 30, 20,  3, 54, 31, 61, 60,
       11, 56, 24,  2, 59, 21,  9, 52, 46, 26, 38, 15, 29, 23, 16],
      dtype=int64), 'cur_cost': 98828.0}
2025-08-03 17:08:52,071 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 98828.00)
2025-08-03 17:08:52,071 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-03 17:08:52,071 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-03 17:08:52,072 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:08:52,090 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 17:08:52,091 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:08:52,092 - ExplorationExpert - INFO - 探索路径生成完成，成本: 65590.0, 路径长度: 66
2025-08-03 17:08:52,092 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [63, 10, 3, 12, 25, 21, 15, 19, 16, 11, 64, 0, 61, 1, 53, 8, 7, 59, 18, 13, 28, 37, 32, 30, 35, 4, 52, 39, 23, 9, 56, 57, 58, 22, 43, 41, 34, 20, 14, 5, 31, 17, 33, 47, 49, 46, 44, 36, 2, 27, 42, 38, 50, 48, 24, 26, 45, 6, 62, 65, 55, 54, 60, 40, 51, 29], 'cur_cost': 65590.0}
2025-08-03 17:08:52,094 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 65590.00)
2025-08-03 17:08:52,094 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-03 17:08:52,095 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-03 17:08:52,096 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:08:52,115 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 17:08:52,117 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:08:52,118 - ExplorationExpert - INFO - 探索路径生成完成，成本: 60480.0, 路径长度: 66
2025-08-03 17:08:52,118 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [31, 13, 16, 18, 24, 23, 30, 22, 3, 8, 7, 64, 62, 11, 57, 2, 1, 15, 26, 35, 27, 19, 43, 44, 45, 37, 0, 5, 52, 56, 17, 4, 58, 10, 53, 60, 6, 61, 12, 36, 40, 50, 49, 20, 48, 38, 41, 39, 47, 14, 25, 33, 21, 28, 32, 42, 51, 9, 63, 55, 65, 59, 46, 34, 29, 54], 'cur_cost': 60480.0}
2025-08-03 17:08:52,119 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 60480.00)
2025-08-03 17:08:52,119 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-03 17:08:52,119 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:08:52,120 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:08:52,120 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 99902.0
2025-08-03 17:08:54,318 - ExploitationExpert - INFO - res_population_num: 2
2025-08-03 17:08:54,318 - ExploitationExpert - INFO - res_population_costs: [9887.0, 9580.0]
2025-08-03 17:08:54,319 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 54, 57, 64, 53, 62,
       59, 56, 58, 60, 55, 61, 65, 52, 63,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59,
       56, 58, 60, 54, 57, 64, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64)]
2025-08-03 17:08:54,320 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:08:54,320 - ExploitationExpert - INFO - populations: [{'tour': [1, 62, 21, 12, 34, 41, 58, 18, 35, 61, 2, 33, 60, 51, 49, 57, 48, 8, 55, 7, 59, 53, 50, 0, 5, 32, 43, 56, 4, 29, 52, 16, 19, 38, 40, 44, 20, 3, 23, 25, 14, 15, 11, 36, 39, 27, 24, 45, 37, 22, 46, 10, 64, 31, 6, 13, 54, 28, 9, 17, 65, 26, 47, 42, 30, 63], 'cur_cost': 109041.0}, {'tour': array([36, 25, 28, 37,  4, 22, 51,  6,  0, 40, 45,  1, 19, 55, 58,  8, 34,
       44, 10, 57, 12, 39, 35, 50, 41, 27, 13, 64,  7, 48,  5, 43, 49, 18,
       65, 63, 14, 62, 33, 32, 53, 42, 47, 17, 30, 20,  3, 54, 31, 61, 60,
       11, 56, 24,  2, 59, 21,  9, 52, 46, 26, 38, 15, 29, 23, 16],
      dtype=int64), 'cur_cost': 98828.0}, {'tour': [63, 10, 3, 12, 25, 21, 15, 19, 16, 11, 64, 0, 61, 1, 53, 8, 7, 59, 18, 13, 28, 37, 32, 30, 35, 4, 52, 39, 23, 9, 56, 57, 58, 22, 43, 41, 34, 20, 14, 5, 31, 17, 33, 47, 49, 46, 44, 36, 2, 27, 42, 38, 50, 48, 24, 26, 45, 6, 62, 65, 55, 54, 60, 40, 51, 29], 'cur_cost': 65590.0}, {'tour': [31, 13, 16, 18, 24, 23, 30, 22, 3, 8, 7, 64, 62, 11, 57, 2, 1, 15, 26, 35, 27, 19, 43, 44, 45, 37, 0, 5, 52, 56, 17, 4, 58, 10, 53, 60, 6, 61, 12, 36, 40, 50, 49, 20, 48, 38, 41, 39, 47, 14, 25, 33, 21, 28, 32, 42, 51, 9, 63, 55, 65, 59, 46, 34, 29, 54], 'cur_cost': 60480.0}, {'tour': array([43, 46, 17, 48, 32, 55, 51,  5, 54,  9, 58, 61, 30, 16, 50, 27, 15,
       63, 23, 59, 47, 40,  1, 36, 13, 19, 57, 62,  6, 38, 49, 26, 33, 24,
       11, 53, 31, 35, 41, 37, 39, 14, 45, 12, 64, 42,  3,  2, 44, 21, 34,
       28, 20, 10,  0, 60, 22,  8, 56, 29,  7, 52, 25, 18,  4, 65],
      dtype=int64), 'cur_cost': 99902.0}, {'tour': array([30, 28, 33, 25, 26, 36, 37, 31, 24, 29, 32, 35, 34, 27, 18, 16, 23,
       22, 12, 17, 15, 14, 20, 21, 13, 19,  9, 11,  7,  3,  1,  0, 10,  8,
        2,  6,  4,  5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9890.0}, {'tour': array([41, 53, 49, 58, 43,  9, 60, 21, 65, 46, 14, 23,  8, 28, 26, 22, 13,
       36, 34, 18, 54, 35, 16, 39,  7, 52, 19, 64, 45,  4, 31, 61, 20, 25,
       51, 12,  5, 15, 59, 40, 10, 29, 11, 42, 57, 17,  6, 47, 55, 32,  1,
        3, 50, 56, 63, 44, 27, 48, 37, 24, 30, 33, 38,  2, 62,  0],
      dtype=int64), 'cur_cost': 118526.0}, {'tour': array([53, 24, 12, 41, 46, 11, 64, 26, 29, 10, 15, 48, 59, 58, 27, 44,  8,
       47, 60, 16, 23, 20, 13, 19, 35, 42, 49, 43, 39, 54, 32, 36, 28, 55,
        1,  9, 45, 18, 21, 33,  3, 63,  4, 51, 31,  0, 65, 62, 52, 30, 37,
        2,  7, 14, 50, 40, 34, 38,  5, 57, 17,  6, 56, 22, 25, 61],
      dtype=int64), 'cur_cost': 90927.0}, {'tour': array([27, 24, 37, 63, 44, 59, 43,  8, 40, 10, 41,  2, 46, 20, 30, 52, 28,
       14, 25, 13, 54,  7, 35, 16, 48, 33, 42, 32, 56, 58, 53, 12,  4, 19,
       57,  1, 39, 65, 36, 61, 23, 22,  3, 21, 34, 45, 38, 49,  5, 50,  0,
        6, 60, 47, 15, 11,  9, 26, 64, 17, 18, 31, 55, 62, 51, 29],
      dtype=int64), 'cur_cost': 112604.0}, {'tour': array([50, 38,  7, 54, 32, 27, 17,  8, 34,  5,  4, 59, 31, 30, 45, 49,  9,
       12, 42, 57, 61, 53, 41, 21,  1, 62, 43, 52,  2, 60, 16, 20, 15, 65,
       19, 22, 51,  3, 36, 13, 39, 23, 46, 48, 18, 11, 64, 10, 24, 14,  0,
       25, 29, 63, 33, 58, 35, 56, 40,  6, 28, 47, 26, 44, 55, 37],
      dtype=int64), 'cur_cost': 106452.0}, {'tour': array([30,  9, 28,  8, 32, 64, 53, 50, 48, 52, 37, 10, 14,  0,  2, 63, 13,
       40, 62, 36,  5, 45, 29, 46,  7, 65, 17, 56,  6, 43, 24, 47, 31, 21,
       22, 42, 12, 39,  3, 61, 18, 20, 23, 51, 26, 54, 25, 35, 58, 49,  1,
       27, 19, 11, 34, 44, 57, 59, 15, 38, 55, 60, 16,  4, 33, 41],
      dtype=int64), 'cur_cost': 116456.0}, {'tour': array([39, 48, 57, 56, 51, 11, 16, 61, 41, 47,  0, 29, 20, 52, 64, 34, 36,
       10, 45, 53, 49, 62, 18,  8, 22, 17, 43,  1, 55, 42,  6, 37,  9, 12,
       35, 26, 19, 14, 15, 28, 33, 46, 30, 60, 65, 21, 32, 54, 59, 24, 44,
       13,  3,  5, 40, 50, 25, 31,  4, 38,  2, 63, 58, 23,  7, 27],
      dtype=int64), 'cur_cost': 103219.0}, {'tour': array([11,  6, 18, 29,  8, 31, 54, 44, 59, 40, 39, 58, 48,  4, 57, 32, 10,
       37, 63,  7, 60, 61, 45, 38, 28, 65,  2, 52, 12, 13, 35, 56, 43, 33,
       26, 46, 62,  0, 47, 36, 17, 25, 50,  9, 41, 20, 55, 22, 16, 15, 19,
        1, 53,  3, 30, 42, 21, 34, 23, 24, 51, 64, 14, 49, 27,  5],
      dtype=int64), 'cur_cost': 110642.0}, {'tour': array([ 9, 38, 22, 27,  7,  3, 23, 10, 45, 19, 18, 47, 34, 50, 26, 37, 33,
       12, 14, 41, 15, 30,  5, 51, 32, 57, 28, 25,  8, 35, 62, 31, 52, 54,
       17, 64, 29, 24, 63, 53, 21, 60, 13, 39, 11, 48, 59, 61, 49, 56,  0,
       43,  4, 20, 55, 65, 44,  6, 36, 40, 58, 46,  1, 42, 16,  2],
      dtype=int64), 'cur_cost': 114932.0}, {'tour': array([64, 20, 47, 17, 53, 48, 60, 42, 62, 16, 59, 13, 63, 14, 45, 41, 29,
       51,  7, 11,  9, 56, 22,  0,  3, 50, 25, 27, 40, 30, 28,  6, 44, 26,
       35, 52, 12, 33, 18, 38, 15, 57,  5, 21, 55, 39, 37, 49, 58,  4, 23,
       43, 32, 61, 34, 19,  1,  2, 46, 10, 36, 31, 54,  8, 65, 24],
      dtype=int64), 'cur_cost': 114332.0}, {'tour': array([10, 22, 53,  3, 15, 12, 21, 35, 50,  7, 43, 64, 30, 11, 27, 52, 32,
       62, 31, 58,  1, 20, 37, 38, 49, 44, 33, 17,  6, 47, 51, 42, 26, 16,
        2, 56,  4, 34, 24, 61, 46,  0, 29, 65, 41,  5, 39, 25, 28, 45, 18,
       54, 48, 36,  9, 23, 14, 63,  8, 57, 59, 13, 55, 60, 40, 19],
      dtype=int64), 'cur_cost': 110408.0}, {'tour': array([53, 63, 21,  1, 34, 32, 15,  0, 42, 24,  4, 49, 28, 10, 46, 58, 64,
       26, 33, 52, 41, 55,  2, 19, 43, 35, 37, 57,  9, 20, 44, 56, 16,  6,
       13, 25, 36, 29, 11, 60, 39, 18, 17, 40,  7,  5, 47, 31, 38, 48, 54,
       45, 22, 65, 12,  8, 23,  3, 59, 61, 51, 30, 50, 14, 62, 27],
      dtype=int64), 'cur_cost': 109197.0}, {'tour': array([49, 14, 25, 62, 44, 43, 30, 54, 38, 34, 24, 35, 40, 31, 50, 46, 63,
       19,  3,  1, 17, 29, 47, 11, 58,  0, 39, 42, 12,  2, 36, 13, 59, 27,
        6, 60, 22,  9, 41, 65, 18, 51, 21,  7, 56, 20, 48,  4, 23, 61, 28,
       45, 57,  5, 10, 16, 52, 33, 26, 53, 15,  8, 55, 64, 37, 32],
      dtype=int64), 'cur_cost': 108762.0}, {'tour': array([62, 39, 33, 13,  8, 40, 41,  2, 21, 55, 56, 25, 57, 46, 53, 58,  0,
       12,  5, 43, 59, 16, 15, 47, 35, 27, 42,  6, 60, 14, 30, 22, 26, 37,
       64, 17, 50, 65,  4, 29, 48,  7, 32,  1, 45, 44, 23, 11, 51, 18, 49,
       54,  9, 34, 38, 19, 61, 10, 28, 63, 36, 24, 31,  3, 20, 52],
      dtype=int64), 'cur_cost': 110062.0}, {'tour': array([10, 33, 45, 43, 59, 23, 26, 16, 63, 62, 22,  6, 64, 34, 24,  4, 40,
       50, 37, 46, 17, 32, 11, 65, 47,  5, 15, 42, 51, 25, 31, 49, 48,  3,
       28, 38, 30, 44, 35, 54, 56, 52,  8, 61, 14,  0, 29, 20, 19, 55, 21,
        9,  7, 60, 39, 12, 53,  2, 13, 58, 27, 18, 36,  1, 41, 57],
      dtype=int64), 'cur_cost': 102178.0}]
2025-08-03 17:08:54,332 - ExploitationExpert - INFO - 局部搜索耗时: 2.21秒
2025-08-03 17:08:54,332 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-03 17:08:54,333 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([43, 46, 17, 48, 32, 55, 51,  5, 54,  9, 58, 61, 30, 16, 50, 27, 15,
       63, 23, 59, 47, 40,  1, 36, 13, 19, 57, 62,  6, 38, 49, 26, 33, 24,
       11, 53, 31, 35, 41, 37, 39, 14, 45, 12, 64, 42,  3,  2, 44, 21, 34,
       28, 20, 10,  0, 60, 22,  8, 56, 29,  7, 52, 25, 18,  4, 65],
      dtype=int64), 'cur_cost': 99902.0}
2025-08-03 17:08:54,333 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 99902.00)
2025-08-03 17:08:54,334 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-03 17:08:54,334 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-03 17:08:54,334 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:08:54,340 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 17:08:54,340 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:08:54,341 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12373.0, 路径长度: 66
2025-08-03 17:08:54,341 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 7, 9, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12373.0}
2025-08-03 17:08:54,342 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 12373.00)
2025-08-03 17:08:54,343 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-03 17:08:54,343 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-03 17:08:54,343 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:08:54,350 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 17:08:54,351 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:08:54,352 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12781.0, 路径长度: 66
2025-08-03 17:08:54,353 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 1, 18, 9, 11, 7, 3, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12781.0}
2025-08-03 17:08:54,354 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 12781.00)
2025-08-03 17:08:54,354 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-03 17:08:54,354 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:08:54,354 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:08:54,355 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 110923.0
2025-08-03 17:08:54,927 - ExploitationExpert - INFO - res_population_num: 3
2025-08-03 17:08:54,928 - ExploitationExpert - INFO - res_population_costs: [9887.0, 9580.0, 9567.0]
2025-08-03 17:08:54,929 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 54, 57, 64, 53, 62,
       59, 56, 58, 60, 55, 61, 65, 52, 63,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59,
       56, 58, 60, 54, 57, 64, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64)]
2025-08-03 17:08:54,932 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:08:54,932 - ExploitationExpert - INFO - populations: [{'tour': [1, 62, 21, 12, 34, 41, 58, 18, 35, 61, 2, 33, 60, 51, 49, 57, 48, 8, 55, 7, 59, 53, 50, 0, 5, 32, 43, 56, 4, 29, 52, 16, 19, 38, 40, 44, 20, 3, 23, 25, 14, 15, 11, 36, 39, 27, 24, 45, 37, 22, 46, 10, 64, 31, 6, 13, 54, 28, 9, 17, 65, 26, 47, 42, 30, 63], 'cur_cost': 109041.0}, {'tour': array([36, 25, 28, 37,  4, 22, 51,  6,  0, 40, 45,  1, 19, 55, 58,  8, 34,
       44, 10, 57, 12, 39, 35, 50, 41, 27, 13, 64,  7, 48,  5, 43, 49, 18,
       65, 63, 14, 62, 33, 32, 53, 42, 47, 17, 30, 20,  3, 54, 31, 61, 60,
       11, 56, 24,  2, 59, 21,  9, 52, 46, 26, 38, 15, 29, 23, 16],
      dtype=int64), 'cur_cost': 98828.0}, {'tour': [63, 10, 3, 12, 25, 21, 15, 19, 16, 11, 64, 0, 61, 1, 53, 8, 7, 59, 18, 13, 28, 37, 32, 30, 35, 4, 52, 39, 23, 9, 56, 57, 58, 22, 43, 41, 34, 20, 14, 5, 31, 17, 33, 47, 49, 46, 44, 36, 2, 27, 42, 38, 50, 48, 24, 26, 45, 6, 62, 65, 55, 54, 60, 40, 51, 29], 'cur_cost': 65590.0}, {'tour': [31, 13, 16, 18, 24, 23, 30, 22, 3, 8, 7, 64, 62, 11, 57, 2, 1, 15, 26, 35, 27, 19, 43, 44, 45, 37, 0, 5, 52, 56, 17, 4, 58, 10, 53, 60, 6, 61, 12, 36, 40, 50, 49, 20, 48, 38, 41, 39, 47, 14, 25, 33, 21, 28, 32, 42, 51, 9, 63, 55, 65, 59, 46, 34, 29, 54], 'cur_cost': 60480.0}, {'tour': array([43, 46, 17, 48, 32, 55, 51,  5, 54,  9, 58, 61, 30, 16, 50, 27, 15,
       63, 23, 59, 47, 40,  1, 36, 13, 19, 57, 62,  6, 38, 49, 26, 33, 24,
       11, 53, 31, 35, 41, 37, 39, 14, 45, 12, 64, 42,  3,  2, 44, 21, 34,
       28, 20, 10,  0, 60, 22,  8, 56, 29,  7, 52, 25, 18,  4, 65],
      dtype=int64), 'cur_cost': 99902.0}, {'tour': [0, 7, 9, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12373.0}, {'tour': [0, 1, 18, 9, 11, 7, 3, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12781.0}, {'tour': array([41, 28,  3, 31, 44, 21, 48, 29, 23, 34, 39, 25, 27, 53, 13,  6, 51,
       60, 17, 58, 30, 40, 56, 18, 57, 43, 55, 64, 37,  7, 26, 38, 65,  5,
        0, 59, 10, 50, 12, 22,  4, 36, 45, 46, 42, 33,  8, 52, 63, 24, 49,
       62, 16, 11, 32, 15, 20, 35, 54,  2,  1, 19,  9, 61, 47, 14],
      dtype=int64), 'cur_cost': 110923.0}, {'tour': array([27, 24, 37, 63, 44, 59, 43,  8, 40, 10, 41,  2, 46, 20, 30, 52, 28,
       14, 25, 13, 54,  7, 35, 16, 48, 33, 42, 32, 56, 58, 53, 12,  4, 19,
       57,  1, 39, 65, 36, 61, 23, 22,  3, 21, 34, 45, 38, 49,  5, 50,  0,
        6, 60, 47, 15, 11,  9, 26, 64, 17, 18, 31, 55, 62, 51, 29],
      dtype=int64), 'cur_cost': 112604.0}, {'tour': array([50, 38,  7, 54, 32, 27, 17,  8, 34,  5,  4, 59, 31, 30, 45, 49,  9,
       12, 42, 57, 61, 53, 41, 21,  1, 62, 43, 52,  2, 60, 16, 20, 15, 65,
       19, 22, 51,  3, 36, 13, 39, 23, 46, 48, 18, 11, 64, 10, 24, 14,  0,
       25, 29, 63, 33, 58, 35, 56, 40,  6, 28, 47, 26, 44, 55, 37],
      dtype=int64), 'cur_cost': 106452.0}, {'tour': array([30,  9, 28,  8, 32, 64, 53, 50, 48, 52, 37, 10, 14,  0,  2, 63, 13,
       40, 62, 36,  5, 45, 29, 46,  7, 65, 17, 56,  6, 43, 24, 47, 31, 21,
       22, 42, 12, 39,  3, 61, 18, 20, 23, 51, 26, 54, 25, 35, 58, 49,  1,
       27, 19, 11, 34, 44, 57, 59, 15, 38, 55, 60, 16,  4, 33, 41],
      dtype=int64), 'cur_cost': 116456.0}, {'tour': array([39, 48, 57, 56, 51, 11, 16, 61, 41, 47,  0, 29, 20, 52, 64, 34, 36,
       10, 45, 53, 49, 62, 18,  8, 22, 17, 43,  1, 55, 42,  6, 37,  9, 12,
       35, 26, 19, 14, 15, 28, 33, 46, 30, 60, 65, 21, 32, 54, 59, 24, 44,
       13,  3,  5, 40, 50, 25, 31,  4, 38,  2, 63, 58, 23,  7, 27],
      dtype=int64), 'cur_cost': 103219.0}, {'tour': array([11,  6, 18, 29,  8, 31, 54, 44, 59, 40, 39, 58, 48,  4, 57, 32, 10,
       37, 63,  7, 60, 61, 45, 38, 28, 65,  2, 52, 12, 13, 35, 56, 43, 33,
       26, 46, 62,  0, 47, 36, 17, 25, 50,  9, 41, 20, 55, 22, 16, 15, 19,
        1, 53,  3, 30, 42, 21, 34, 23, 24, 51, 64, 14, 49, 27,  5],
      dtype=int64), 'cur_cost': 110642.0}, {'tour': array([ 9, 38, 22, 27,  7,  3, 23, 10, 45, 19, 18, 47, 34, 50, 26, 37, 33,
       12, 14, 41, 15, 30,  5, 51, 32, 57, 28, 25,  8, 35, 62, 31, 52, 54,
       17, 64, 29, 24, 63, 53, 21, 60, 13, 39, 11, 48, 59, 61, 49, 56,  0,
       43,  4, 20, 55, 65, 44,  6, 36, 40, 58, 46,  1, 42, 16,  2],
      dtype=int64), 'cur_cost': 114932.0}, {'tour': array([64, 20, 47, 17, 53, 48, 60, 42, 62, 16, 59, 13, 63, 14, 45, 41, 29,
       51,  7, 11,  9, 56, 22,  0,  3, 50, 25, 27, 40, 30, 28,  6, 44, 26,
       35, 52, 12, 33, 18, 38, 15, 57,  5, 21, 55, 39, 37, 49, 58,  4, 23,
       43, 32, 61, 34, 19,  1,  2, 46, 10, 36, 31, 54,  8, 65, 24],
      dtype=int64), 'cur_cost': 114332.0}, {'tour': array([10, 22, 53,  3, 15, 12, 21, 35, 50,  7, 43, 64, 30, 11, 27, 52, 32,
       62, 31, 58,  1, 20, 37, 38, 49, 44, 33, 17,  6, 47, 51, 42, 26, 16,
        2, 56,  4, 34, 24, 61, 46,  0, 29, 65, 41,  5, 39, 25, 28, 45, 18,
       54, 48, 36,  9, 23, 14, 63,  8, 57, 59, 13, 55, 60, 40, 19],
      dtype=int64), 'cur_cost': 110408.0}, {'tour': array([53, 63, 21,  1, 34, 32, 15,  0, 42, 24,  4, 49, 28, 10, 46, 58, 64,
       26, 33, 52, 41, 55,  2, 19, 43, 35, 37, 57,  9, 20, 44, 56, 16,  6,
       13, 25, 36, 29, 11, 60, 39, 18, 17, 40,  7,  5, 47, 31, 38, 48, 54,
       45, 22, 65, 12,  8, 23,  3, 59, 61, 51, 30, 50, 14, 62, 27],
      dtype=int64), 'cur_cost': 109197.0}, {'tour': array([49, 14, 25, 62, 44, 43, 30, 54, 38, 34, 24, 35, 40, 31, 50, 46, 63,
       19,  3,  1, 17, 29, 47, 11, 58,  0, 39, 42, 12,  2, 36, 13, 59, 27,
        6, 60, 22,  9, 41, 65, 18, 51, 21,  7, 56, 20, 48,  4, 23, 61, 28,
       45, 57,  5, 10, 16, 52, 33, 26, 53, 15,  8, 55, 64, 37, 32],
      dtype=int64), 'cur_cost': 108762.0}, {'tour': array([62, 39, 33, 13,  8, 40, 41,  2, 21, 55, 56, 25, 57, 46, 53, 58,  0,
       12,  5, 43, 59, 16, 15, 47, 35, 27, 42,  6, 60, 14, 30, 22, 26, 37,
       64, 17, 50, 65,  4, 29, 48,  7, 32,  1, 45, 44, 23, 11, 51, 18, 49,
       54,  9, 34, 38, 19, 61, 10, 28, 63, 36, 24, 31,  3, 20, 52],
      dtype=int64), 'cur_cost': 110062.0}, {'tour': array([10, 33, 45, 43, 59, 23, 26, 16, 63, 62, 22,  6, 64, 34, 24,  4, 40,
       50, 37, 46, 17, 32, 11, 65, 47,  5, 15, 42, 51, 25, 31, 49, 48,  3,
       28, 38, 30, 44, 35, 54, 56, 52,  8, 61, 14,  0, 29, 20, 19, 55, 21,
        9,  7, 60, 39, 12, 53,  2, 13, 58, 27, 18, 36,  1, 41, 57],
      dtype=int64), 'cur_cost': 102178.0}]
2025-08-03 17:08:54,943 - ExploitationExpert - INFO - 局部搜索耗时: 0.59秒
2025-08-03 17:08:54,944 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-03 17:08:54,944 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([41, 28,  3, 31, 44, 21, 48, 29, 23, 34, 39, 25, 27, 53, 13,  6, 51,
       60, 17, 58, 30, 40, 56, 18, 57, 43, 55, 64, 37,  7, 26, 38, 65,  5,
        0, 59, 10, 50, 12, 22,  4, 36, 45, 46, 42, 33,  8, 52, 63, 24, 49,
       62, 16, 11, 32, 15, 20, 35, 54,  2,  1, 19,  9, 61, 47, 14],
      dtype=int64), 'cur_cost': 110923.0}
2025-08-03 17:08:54,945 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 110923.00)
2025-08-03 17:08:54,945 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-03 17:08:54,946 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-03 17:08:54,946 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:08:54,955 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 17:08:54,955 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:08:54,956 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12498.0, 路径长度: 66
2025-08-03 17:08:54,956 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 2, 19, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12498.0}
2025-08-03 17:08:54,957 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 12498.00)
2025-08-03 17:08:54,957 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-03 17:08:54,957 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-03 17:08:54,958 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:08:54,964 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 17:08:54,964 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:08:54,965 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12467.0, 路径长度: 66
2025-08-03 17:08:54,965 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 18, 21, 17, 12, 22, 23, 16, 19, 13, 20, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12467.0}
2025-08-03 17:08:54,966 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 12467.00)
2025-08-03 17:08:54,966 - experts.management.collaboration_manager - INFO - 为个体 10 生成利用路径
2025-08-03 17:08:54,966 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:08:54,967 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:08:54,967 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 10 处的路径，新成本: 112176.0
2025-08-03 17:08:55,048 - ExploitationExpert - INFO - res_population_num: 9
2025-08-03 17:08:55,050 - ExploitationExpert - INFO - res_population_costs: [9887.0, 9580.0, 9567.0, 9566, 9560, 9552, 9549, 9534, 9532]
2025-08-03 17:08:55,050 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 54, 57, 64, 53, 62,
       59, 56, 58, 60, 55, 61, 65, 52, 63,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59,
       56, 58, 60, 54, 57, 64, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59,
       53, 64, 57, 54, 52, 63, 65, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 17, 12, 22, 23, 13, 20, 21, 19, 16,
       18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 42, 50,
       45, 38, 51, 41, 44, 39, 47, 46, 48, 43, 40, 49, 58, 60, 56, 59, 62,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 43, 40, 49,
       58, 60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 56, 59, 62, 53, 61, 63, 52, 65, 64,
       57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 13, 23, 16, 18, 17, 12, 22, 15, 14, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 17:08:55,056 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:08:55,057 - ExploitationExpert - INFO - populations: [{'tour': [1, 62, 21, 12, 34, 41, 58, 18, 35, 61, 2, 33, 60, 51, 49, 57, 48, 8, 55, 7, 59, 53, 50, 0, 5, 32, 43, 56, 4, 29, 52, 16, 19, 38, 40, 44, 20, 3, 23, 25, 14, 15, 11, 36, 39, 27, 24, 45, 37, 22, 46, 10, 64, 31, 6, 13, 54, 28, 9, 17, 65, 26, 47, 42, 30, 63], 'cur_cost': 109041.0}, {'tour': array([36, 25, 28, 37,  4, 22, 51,  6,  0, 40, 45,  1, 19, 55, 58,  8, 34,
       44, 10, 57, 12, 39, 35, 50, 41, 27, 13, 64,  7, 48,  5, 43, 49, 18,
       65, 63, 14, 62, 33, 32, 53, 42, 47, 17, 30, 20,  3, 54, 31, 61, 60,
       11, 56, 24,  2, 59, 21,  9, 52, 46, 26, 38, 15, 29, 23, 16],
      dtype=int64), 'cur_cost': 98828.0}, {'tour': [63, 10, 3, 12, 25, 21, 15, 19, 16, 11, 64, 0, 61, 1, 53, 8, 7, 59, 18, 13, 28, 37, 32, 30, 35, 4, 52, 39, 23, 9, 56, 57, 58, 22, 43, 41, 34, 20, 14, 5, 31, 17, 33, 47, 49, 46, 44, 36, 2, 27, 42, 38, 50, 48, 24, 26, 45, 6, 62, 65, 55, 54, 60, 40, 51, 29], 'cur_cost': 65590.0}, {'tour': [31, 13, 16, 18, 24, 23, 30, 22, 3, 8, 7, 64, 62, 11, 57, 2, 1, 15, 26, 35, 27, 19, 43, 44, 45, 37, 0, 5, 52, 56, 17, 4, 58, 10, 53, 60, 6, 61, 12, 36, 40, 50, 49, 20, 48, 38, 41, 39, 47, 14, 25, 33, 21, 28, 32, 42, 51, 9, 63, 55, 65, 59, 46, 34, 29, 54], 'cur_cost': 60480.0}, {'tour': array([43, 46, 17, 48, 32, 55, 51,  5, 54,  9, 58, 61, 30, 16, 50, 27, 15,
       63, 23, 59, 47, 40,  1, 36, 13, 19, 57, 62,  6, 38, 49, 26, 33, 24,
       11, 53, 31, 35, 41, 37, 39, 14, 45, 12, 64, 42,  3,  2, 44, 21, 34,
       28, 20, 10,  0, 60, 22,  8, 56, 29,  7, 52, 25, 18,  4, 65],
      dtype=int64), 'cur_cost': 99902.0}, {'tour': [0, 7, 9, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12373.0}, {'tour': [0, 1, 18, 9, 11, 7, 3, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12781.0}, {'tour': array([41, 28,  3, 31, 44, 21, 48, 29, 23, 34, 39, 25, 27, 53, 13,  6, 51,
       60, 17, 58, 30, 40, 56, 18, 57, 43, 55, 64, 37,  7, 26, 38, 65,  5,
        0, 59, 10, 50, 12, 22,  4, 36, 45, 46, 42, 33,  8, 52, 63, 24, 49,
       62, 16, 11, 32, 15, 20, 35, 54,  2,  1, 19,  9, 61, 47, 14],
      dtype=int64), 'cur_cost': 110923.0}, {'tour': [0, 2, 19, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12498.0}, {'tour': [0, 18, 21, 17, 12, 22, 23, 16, 19, 13, 20, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12467.0}, {'tour': array([23, 65, 33, 21,  5, 18,  7, 26, 28, 43, 44,  9, 50, 58, 41, 54, 47,
       39,  8, 34, 10, 52,  6, 64, 60, 11, 29, 35,  3, 42, 30, 62, 31, 46,
       17, 24,  0,  1, 25, 53, 38, 19,  2, 63, 36, 45, 20, 32, 49, 48, 12,
       59, 22, 15, 40,  4, 13, 14, 51, 27, 55, 57, 37, 61, 16, 56],
      dtype=int64), 'cur_cost': 112176.0}, {'tour': array([39, 48, 57, 56, 51, 11, 16, 61, 41, 47,  0, 29, 20, 52, 64, 34, 36,
       10, 45, 53, 49, 62, 18,  8, 22, 17, 43,  1, 55, 42,  6, 37,  9, 12,
       35, 26, 19, 14, 15, 28, 33, 46, 30, 60, 65, 21, 32, 54, 59, 24, 44,
       13,  3,  5, 40, 50, 25, 31,  4, 38,  2, 63, 58, 23,  7, 27],
      dtype=int64), 'cur_cost': 103219.0}, {'tour': array([11,  6, 18, 29,  8, 31, 54, 44, 59, 40, 39, 58, 48,  4, 57, 32, 10,
       37, 63,  7, 60, 61, 45, 38, 28, 65,  2, 52, 12, 13, 35, 56, 43, 33,
       26, 46, 62,  0, 47, 36, 17, 25, 50,  9, 41, 20, 55, 22, 16, 15, 19,
        1, 53,  3, 30, 42, 21, 34, 23, 24, 51, 64, 14, 49, 27,  5],
      dtype=int64), 'cur_cost': 110642.0}, {'tour': array([ 9, 38, 22, 27,  7,  3, 23, 10, 45, 19, 18, 47, 34, 50, 26, 37, 33,
       12, 14, 41, 15, 30,  5, 51, 32, 57, 28, 25,  8, 35, 62, 31, 52, 54,
       17, 64, 29, 24, 63, 53, 21, 60, 13, 39, 11, 48, 59, 61, 49, 56,  0,
       43,  4, 20, 55, 65, 44,  6, 36, 40, 58, 46,  1, 42, 16,  2],
      dtype=int64), 'cur_cost': 114932.0}, {'tour': array([64, 20, 47, 17, 53, 48, 60, 42, 62, 16, 59, 13, 63, 14, 45, 41, 29,
       51,  7, 11,  9, 56, 22,  0,  3, 50, 25, 27, 40, 30, 28,  6, 44, 26,
       35, 52, 12, 33, 18, 38, 15, 57,  5, 21, 55, 39, 37, 49, 58,  4, 23,
       43, 32, 61, 34, 19,  1,  2, 46, 10, 36, 31, 54,  8, 65, 24],
      dtype=int64), 'cur_cost': 114332.0}, {'tour': array([10, 22, 53,  3, 15, 12, 21, 35, 50,  7, 43, 64, 30, 11, 27, 52, 32,
       62, 31, 58,  1, 20, 37, 38, 49, 44, 33, 17,  6, 47, 51, 42, 26, 16,
        2, 56,  4, 34, 24, 61, 46,  0, 29, 65, 41,  5, 39, 25, 28, 45, 18,
       54, 48, 36,  9, 23, 14, 63,  8, 57, 59, 13, 55, 60, 40, 19],
      dtype=int64), 'cur_cost': 110408.0}, {'tour': array([53, 63, 21,  1, 34, 32, 15,  0, 42, 24,  4, 49, 28, 10, 46, 58, 64,
       26, 33, 52, 41, 55,  2, 19, 43, 35, 37, 57,  9, 20, 44, 56, 16,  6,
       13, 25, 36, 29, 11, 60, 39, 18, 17, 40,  7,  5, 47, 31, 38, 48, 54,
       45, 22, 65, 12,  8, 23,  3, 59, 61, 51, 30, 50, 14, 62, 27],
      dtype=int64), 'cur_cost': 109197.0}, {'tour': array([49, 14, 25, 62, 44, 43, 30, 54, 38, 34, 24, 35, 40, 31, 50, 46, 63,
       19,  3,  1, 17, 29, 47, 11, 58,  0, 39, 42, 12,  2, 36, 13, 59, 27,
        6, 60, 22,  9, 41, 65, 18, 51, 21,  7, 56, 20, 48,  4, 23, 61, 28,
       45, 57,  5, 10, 16, 52, 33, 26, 53, 15,  8, 55, 64, 37, 32],
      dtype=int64), 'cur_cost': 108762.0}, {'tour': array([62, 39, 33, 13,  8, 40, 41,  2, 21, 55, 56, 25, 57, 46, 53, 58,  0,
       12,  5, 43, 59, 16, 15, 47, 35, 27, 42,  6, 60, 14, 30, 22, 26, 37,
       64, 17, 50, 65,  4, 29, 48,  7, 32,  1, 45, 44, 23, 11, 51, 18, 49,
       54,  9, 34, 38, 19, 61, 10, 28, 63, 36, 24, 31,  3, 20, 52],
      dtype=int64), 'cur_cost': 110062.0}, {'tour': array([10, 33, 45, 43, 59, 23, 26, 16, 63, 62, 22,  6, 64, 34, 24,  4, 40,
       50, 37, 46, 17, 32, 11, 65, 47,  5, 15, 42, 51, 25, 31, 49, 48,  3,
       28, 38, 30, 44, 35, 54, 56, 52,  8, 61, 14,  0, 29, 20, 19, 55, 21,
        9,  7, 60, 39, 12, 53,  2, 13, 58, 27, 18, 36,  1, 41, 57],
      dtype=int64), 'cur_cost': 102178.0}]
2025-08-03 17:08:55,067 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 17:08:55,067 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-03 17:08:55,067 - experts.management.collaboration_manager - INFO - 个体 10 利用路径生成报告: {'new_tour': array([23, 65, 33, 21,  5, 18,  7, 26, 28, 43, 44,  9, 50, 58, 41, 54, 47,
       39,  8, 34, 10, 52,  6, 64, 60, 11, 29, 35,  3, 42, 30, 62, 31, 46,
       17, 24,  0,  1, 25, 53, 38, 19,  2, 63, 36, 45, 20, 32, 49, 48, 12,
       59, 22, 15, 40,  4, 13, 14, 51, 27, 55, 57, 37, 61, 16, 56],
      dtype=int64), 'cur_cost': 112176.0}
2025-08-03 17:08:55,068 - experts.management.collaboration_manager - INFO - 个体 10 接受新路径 (成本: 112176.00)
2025-08-03 17:08:55,068 - experts.management.collaboration_manager - INFO - 为个体 11 生成探索路径
2025-08-03 17:08:55,068 - ExplorationExpert - INFO - 开始为个体 11 生成探索路径（算法实现）
2025-08-03 17:08:55,069 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:08:55,073 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 17:08:55,073 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:08:55,074 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12806.0, 路径长度: 66
2025-08-03 17:08:55,074 - experts.management.collaboration_manager - INFO - 个体 11 探索路径生成报告: {'new_tour': [0, 11, 21, 6, 2, 8, 5, 4, 9, 3, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12806.0}
2025-08-03 17:08:55,075 - experts.management.collaboration_manager - INFO - 个体 11 接受新路径 (成本: 12806.00)
2025-08-03 17:08:55,075 - experts.management.collaboration_manager - INFO - 为个体 12 生成探索路径
2025-08-03 17:08:55,075 - ExplorationExpert - INFO - 开始为个体 12 生成探索路径（算法实现）
2025-08-03 17:08:55,076 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:08:55,083 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 17:08:55,084 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:08:55,084 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12356.0, 路径长度: 66
2025-08-03 17:08:55,085 - experts.management.collaboration_manager - INFO - 个体 12 探索路径生成报告: {'new_tour': [0, 10, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12356.0}
2025-08-03 17:08:55,085 - experts.management.collaboration_manager - INFO - 个体 12 接受新路径 (成本: 12356.00)
2025-08-03 17:08:55,086 - experts.management.collaboration_manager - INFO - 为个体 13 生成利用路径
2025-08-03 17:08:55,086 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:08:55,086 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:08:55,086 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 13 处的路径，新成本: 103931.0
2025-08-03 17:08:55,166 - ExploitationExpert - INFO - res_population_num: 11
2025-08-03 17:08:55,166 - ExploitationExpert - INFO - res_population_costs: [9887.0, 9580.0, 9567.0, 9566, 9560, 9552, 9549, 9534, 9532, 9527, 9526]
2025-08-03 17:08:55,166 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 54, 57, 64, 53, 62,
       59, 56, 58, 60, 55, 61, 65, 52, 63,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59,
       56, 58, 60, 54, 57, 64, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59,
       53, 64, 57, 54, 52, 63, 65, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 17, 12, 22, 23, 13, 20, 21, 19, 16,
       18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 42, 50,
       45, 38, 51, 41, 44, 39, 47, 46, 48, 43, 40, 49, 58, 60, 56, 59, 62,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 43, 40, 49,
       58, 60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 56, 59, 62, 53, 61, 63, 52, 65, 64,
       57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 13, 23, 16, 18, 17, 12, 22, 15, 14, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 18, 16, 19, 21, 20, 13, 23,
       22, 12, 17, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 18, 16, 19, 21, 20, 13, 23,
       22, 12, 17, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-08-03 17:08:55,173 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:08:55,173 - ExploitationExpert - INFO - populations: [{'tour': [1, 62, 21, 12, 34, 41, 58, 18, 35, 61, 2, 33, 60, 51, 49, 57, 48, 8, 55, 7, 59, 53, 50, 0, 5, 32, 43, 56, 4, 29, 52, 16, 19, 38, 40, 44, 20, 3, 23, 25, 14, 15, 11, 36, 39, 27, 24, 45, 37, 22, 46, 10, 64, 31, 6, 13, 54, 28, 9, 17, 65, 26, 47, 42, 30, 63], 'cur_cost': 109041.0}, {'tour': array([36, 25, 28, 37,  4, 22, 51,  6,  0, 40, 45,  1, 19, 55, 58,  8, 34,
       44, 10, 57, 12, 39, 35, 50, 41, 27, 13, 64,  7, 48,  5, 43, 49, 18,
       65, 63, 14, 62, 33, 32, 53, 42, 47, 17, 30, 20,  3, 54, 31, 61, 60,
       11, 56, 24,  2, 59, 21,  9, 52, 46, 26, 38, 15, 29, 23, 16],
      dtype=int64), 'cur_cost': 98828.0}, {'tour': [63, 10, 3, 12, 25, 21, 15, 19, 16, 11, 64, 0, 61, 1, 53, 8, 7, 59, 18, 13, 28, 37, 32, 30, 35, 4, 52, 39, 23, 9, 56, 57, 58, 22, 43, 41, 34, 20, 14, 5, 31, 17, 33, 47, 49, 46, 44, 36, 2, 27, 42, 38, 50, 48, 24, 26, 45, 6, 62, 65, 55, 54, 60, 40, 51, 29], 'cur_cost': 65590.0}, {'tour': [31, 13, 16, 18, 24, 23, 30, 22, 3, 8, 7, 64, 62, 11, 57, 2, 1, 15, 26, 35, 27, 19, 43, 44, 45, 37, 0, 5, 52, 56, 17, 4, 58, 10, 53, 60, 6, 61, 12, 36, 40, 50, 49, 20, 48, 38, 41, 39, 47, 14, 25, 33, 21, 28, 32, 42, 51, 9, 63, 55, 65, 59, 46, 34, 29, 54], 'cur_cost': 60480.0}, {'tour': array([43, 46, 17, 48, 32, 55, 51,  5, 54,  9, 58, 61, 30, 16, 50, 27, 15,
       63, 23, 59, 47, 40,  1, 36, 13, 19, 57, 62,  6, 38, 49, 26, 33, 24,
       11, 53, 31, 35, 41, 37, 39, 14, 45, 12, 64, 42,  3,  2, 44, 21, 34,
       28, 20, 10,  0, 60, 22,  8, 56, 29,  7, 52, 25, 18,  4, 65],
      dtype=int64), 'cur_cost': 99902.0}, {'tour': [0, 7, 9, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12373.0}, {'tour': [0, 1, 18, 9, 11, 7, 3, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12781.0}, {'tour': array([41, 28,  3, 31, 44, 21, 48, 29, 23, 34, 39, 25, 27, 53, 13,  6, 51,
       60, 17, 58, 30, 40, 56, 18, 57, 43, 55, 64, 37,  7, 26, 38, 65,  5,
        0, 59, 10, 50, 12, 22,  4, 36, 45, 46, 42, 33,  8, 52, 63, 24, 49,
       62, 16, 11, 32, 15, 20, 35, 54,  2,  1, 19,  9, 61, 47, 14],
      dtype=int64), 'cur_cost': 110923.0}, {'tour': [0, 2, 19, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12498.0}, {'tour': [0, 18, 21, 17, 12, 22, 23, 16, 19, 13, 20, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12467.0}, {'tour': array([23, 65, 33, 21,  5, 18,  7, 26, 28, 43, 44,  9, 50, 58, 41, 54, 47,
       39,  8, 34, 10, 52,  6, 64, 60, 11, 29, 35,  3, 42, 30, 62, 31, 46,
       17, 24,  0,  1, 25, 53, 38, 19,  2, 63, 36, 45, 20, 32, 49, 48, 12,
       59, 22, 15, 40,  4, 13, 14, 51, 27, 55, 57, 37, 61, 16, 56],
      dtype=int64), 'cur_cost': 112176.0}, {'tour': [0, 11, 21, 6, 2, 8, 5, 4, 9, 3, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12806.0}, {'tour': [0, 10, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12356.0}, {'tour': array([21, 19, 45, 47, 52, 32, 65, 27, 58, 38, 44, 60, 39, 33, 54, 18, 15,
       23, 53, 63, 30, 11, 26, 24, 22,  8, 43, 28, 50, 41, 42,  6,  5, 16,
        0, 59, 56, 37, 46,  2, 57, 10, 48,  7, 49, 62, 20, 34,  4, 13, 51,
       36, 31, 64, 17, 12, 29, 25, 35, 61,  1, 14, 40, 55,  9,  3],
      dtype=int64), 'cur_cost': 103931.0}, {'tour': array([64, 20, 47, 17, 53, 48, 60, 42, 62, 16, 59, 13, 63, 14, 45, 41, 29,
       51,  7, 11,  9, 56, 22,  0,  3, 50, 25, 27, 40, 30, 28,  6, 44, 26,
       35, 52, 12, 33, 18, 38, 15, 57,  5, 21, 55, 39, 37, 49, 58,  4, 23,
       43, 32, 61, 34, 19,  1,  2, 46, 10, 36, 31, 54,  8, 65, 24],
      dtype=int64), 'cur_cost': 114332.0}, {'tour': array([10, 22, 53,  3, 15, 12, 21, 35, 50,  7, 43, 64, 30, 11, 27, 52, 32,
       62, 31, 58,  1, 20, 37, 38, 49, 44, 33, 17,  6, 47, 51, 42, 26, 16,
        2, 56,  4, 34, 24, 61, 46,  0, 29, 65, 41,  5, 39, 25, 28, 45, 18,
       54, 48, 36,  9, 23, 14, 63,  8, 57, 59, 13, 55, 60, 40, 19],
      dtype=int64), 'cur_cost': 110408.0}, {'tour': array([53, 63, 21,  1, 34, 32, 15,  0, 42, 24,  4, 49, 28, 10, 46, 58, 64,
       26, 33, 52, 41, 55,  2, 19, 43, 35, 37, 57,  9, 20, 44, 56, 16,  6,
       13, 25, 36, 29, 11, 60, 39, 18, 17, 40,  7,  5, 47, 31, 38, 48, 54,
       45, 22, 65, 12,  8, 23,  3, 59, 61, 51, 30, 50, 14, 62, 27],
      dtype=int64), 'cur_cost': 109197.0}, {'tour': array([49, 14, 25, 62, 44, 43, 30, 54, 38, 34, 24, 35, 40, 31, 50, 46, 63,
       19,  3,  1, 17, 29, 47, 11, 58,  0, 39, 42, 12,  2, 36, 13, 59, 27,
        6, 60, 22,  9, 41, 65, 18, 51, 21,  7, 56, 20, 48,  4, 23, 61, 28,
       45, 57,  5, 10, 16, 52, 33, 26, 53, 15,  8, 55, 64, 37, 32],
      dtype=int64), 'cur_cost': 108762.0}, {'tour': array([62, 39, 33, 13,  8, 40, 41,  2, 21, 55, 56, 25, 57, 46, 53, 58,  0,
       12,  5, 43, 59, 16, 15, 47, 35, 27, 42,  6, 60, 14, 30, 22, 26, 37,
       64, 17, 50, 65,  4, 29, 48,  7, 32,  1, 45, 44, 23, 11, 51, 18, 49,
       54,  9, 34, 38, 19, 61, 10, 28, 63, 36, 24, 31,  3, 20, 52],
      dtype=int64), 'cur_cost': 110062.0}, {'tour': array([10, 33, 45, 43, 59, 23, 26, 16, 63, 62, 22,  6, 64, 34, 24,  4, 40,
       50, 37, 46, 17, 32, 11, 65, 47,  5, 15, 42, 51, 25, 31, 49, 48,  3,
       28, 38, 30, 44, 35, 54, 56, 52,  8, 61, 14,  0, 29, 20, 19, 55, 21,
        9,  7, 60, 39, 12, 53,  2, 13, 58, 27, 18, 36,  1, 41, 57],
      dtype=int64), 'cur_cost': 102178.0}]
2025-08-03 17:08:55,186 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 17:08:55,186 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-03 17:08:55,186 - experts.management.collaboration_manager - INFO - 个体 13 利用路径生成报告: {'new_tour': array([21, 19, 45, 47, 52, 32, 65, 27, 58, 38, 44, 60, 39, 33, 54, 18, 15,
       23, 53, 63, 30, 11, 26, 24, 22,  8, 43, 28, 50, 41, 42,  6,  5, 16,
        0, 59, 56, 37, 46,  2, 57, 10, 48,  7, 49, 62, 20, 34,  4, 13, 51,
       36, 31, 64, 17, 12, 29, 25, 35, 61,  1, 14, 40, 55,  9,  3],
      dtype=int64), 'cur_cost': 103931.0}
2025-08-03 17:08:55,187 - experts.management.collaboration_manager - INFO - 个体 13 接受新路径 (成本: 103931.00)
2025-08-03 17:08:55,187 - experts.management.collaboration_manager - INFO - 为个体 14 生成探索路径
2025-08-03 17:08:55,187 - ExplorationExpert - INFO - 开始为个体 14 生成探索路径（算法实现）
2025-08-03 17:08:55,188 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:08:55,193 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 17:08:55,194 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:08:55,195 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12885.0, 路径长度: 66
2025-08-03 17:08:55,195 - experts.management.collaboration_manager - INFO - 个体 14 探索路径生成报告: {'new_tour': [0, 12, 19, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12885.0}
2025-08-03 17:08:55,196 - experts.management.collaboration_manager - INFO - 个体 14 接受新路径 (成本: 12885.00)
2025-08-03 17:08:55,196 - experts.management.collaboration_manager - INFO - 为个体 15 生成探索路径
2025-08-03 17:08:55,196 - ExplorationExpert - INFO - 开始为个体 15 生成探索路径（算法实现）
2025-08-03 17:08:55,197 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:08:55,201 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 17:08:55,201 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:08:55,201 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12381.0, 路径长度: 66
2025-08-03 17:08:55,202 - experts.management.collaboration_manager - INFO - 个体 15 探索路径生成报告: {'new_tour': [0, 22, 17, 15, 12, 23, 16, 18, 19, 13, 20, 21, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12381.0}
2025-08-03 17:08:55,202 - experts.management.collaboration_manager - INFO - 个体 15 接受新路径 (成本: 12381.00)
2025-08-03 17:08:55,202 - experts.management.collaboration_manager - INFO - 为个体 16 生成利用路径
2025-08-03 17:08:55,202 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:08:55,203 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:08:55,203 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 16 处的路径，新成本: 99215.0
2025-08-03 17:08:55,299 - ExploitationExpert - INFO - res_population_num: 16
2025-08-03 17:08:55,299 - ExploitationExpert - INFO - res_population_costs: [9887.0, 9580.0, 9567.0, 9566, 9560, 9552, 9549, 9534, 9532, 9527, 9526, 9521, 9521, 9521, 9521, 9521]
2025-08-03 17:08:55,300 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 54, 57, 64, 53, 62,
       59, 56, 58, 60, 55, 61, 65, 52, 63,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59,
       56, 58, 60, 54, 57, 64, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59,
       53, 64, 57, 54, 52, 63, 65, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 17, 12, 22, 23, 13, 20, 21, 19, 16,
       18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 42, 50,
       45, 38, 51, 41, 44, 39, 47, 46, 48, 43, 40, 49, 58, 60, 56, 59, 62,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 43, 40, 49,
       58, 60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 56, 59, 62, 53, 61, 63, 52, 65, 64,
       57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 13, 23, 16, 18, 17, 12, 22, 15, 14, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 18, 16, 19, 21, 20, 13, 23,
       22, 12, 17, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 18, 16, 19, 21, 20, 13, 23,
       22, 12, 17, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 17:08:55,309 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:08:55,310 - ExploitationExpert - INFO - populations: [{'tour': [1, 62, 21, 12, 34, 41, 58, 18, 35, 61, 2, 33, 60, 51, 49, 57, 48, 8, 55, 7, 59, 53, 50, 0, 5, 32, 43, 56, 4, 29, 52, 16, 19, 38, 40, 44, 20, 3, 23, 25, 14, 15, 11, 36, 39, 27, 24, 45, 37, 22, 46, 10, 64, 31, 6, 13, 54, 28, 9, 17, 65, 26, 47, 42, 30, 63], 'cur_cost': 109041.0}, {'tour': array([36, 25, 28, 37,  4, 22, 51,  6,  0, 40, 45,  1, 19, 55, 58,  8, 34,
       44, 10, 57, 12, 39, 35, 50, 41, 27, 13, 64,  7, 48,  5, 43, 49, 18,
       65, 63, 14, 62, 33, 32, 53, 42, 47, 17, 30, 20,  3, 54, 31, 61, 60,
       11, 56, 24,  2, 59, 21,  9, 52, 46, 26, 38, 15, 29, 23, 16],
      dtype=int64), 'cur_cost': 98828.0}, {'tour': [63, 10, 3, 12, 25, 21, 15, 19, 16, 11, 64, 0, 61, 1, 53, 8, 7, 59, 18, 13, 28, 37, 32, 30, 35, 4, 52, 39, 23, 9, 56, 57, 58, 22, 43, 41, 34, 20, 14, 5, 31, 17, 33, 47, 49, 46, 44, 36, 2, 27, 42, 38, 50, 48, 24, 26, 45, 6, 62, 65, 55, 54, 60, 40, 51, 29], 'cur_cost': 65590.0}, {'tour': [31, 13, 16, 18, 24, 23, 30, 22, 3, 8, 7, 64, 62, 11, 57, 2, 1, 15, 26, 35, 27, 19, 43, 44, 45, 37, 0, 5, 52, 56, 17, 4, 58, 10, 53, 60, 6, 61, 12, 36, 40, 50, 49, 20, 48, 38, 41, 39, 47, 14, 25, 33, 21, 28, 32, 42, 51, 9, 63, 55, 65, 59, 46, 34, 29, 54], 'cur_cost': 60480.0}, {'tour': array([43, 46, 17, 48, 32, 55, 51,  5, 54,  9, 58, 61, 30, 16, 50, 27, 15,
       63, 23, 59, 47, 40,  1, 36, 13, 19, 57, 62,  6, 38, 49, 26, 33, 24,
       11, 53, 31, 35, 41, 37, 39, 14, 45, 12, 64, 42,  3,  2, 44, 21, 34,
       28, 20, 10,  0, 60, 22,  8, 56, 29,  7, 52, 25, 18,  4, 65],
      dtype=int64), 'cur_cost': 99902.0}, {'tour': [0, 7, 9, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12373.0}, {'tour': [0, 1, 18, 9, 11, 7, 3, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12781.0}, {'tour': array([41, 28,  3, 31, 44, 21, 48, 29, 23, 34, 39, 25, 27, 53, 13,  6, 51,
       60, 17, 58, 30, 40, 56, 18, 57, 43, 55, 64, 37,  7, 26, 38, 65,  5,
        0, 59, 10, 50, 12, 22,  4, 36, 45, 46, 42, 33,  8, 52, 63, 24, 49,
       62, 16, 11, 32, 15, 20, 35, 54,  2,  1, 19,  9, 61, 47, 14],
      dtype=int64), 'cur_cost': 110923.0}, {'tour': [0, 2, 19, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12498.0}, {'tour': [0, 18, 21, 17, 12, 22, 23, 16, 19, 13, 20, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12467.0}, {'tour': array([23, 65, 33, 21,  5, 18,  7, 26, 28, 43, 44,  9, 50, 58, 41, 54, 47,
       39,  8, 34, 10, 52,  6, 64, 60, 11, 29, 35,  3, 42, 30, 62, 31, 46,
       17, 24,  0,  1, 25, 53, 38, 19,  2, 63, 36, 45, 20, 32, 49, 48, 12,
       59, 22, 15, 40,  4, 13, 14, 51, 27, 55, 57, 37, 61, 16, 56],
      dtype=int64), 'cur_cost': 112176.0}, {'tour': [0, 11, 21, 6, 2, 8, 5, 4, 9, 3, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12806.0}, {'tour': [0, 10, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12356.0}, {'tour': array([21, 19, 45, 47, 52, 32, 65, 27, 58, 38, 44, 60, 39, 33, 54, 18, 15,
       23, 53, 63, 30, 11, 26, 24, 22,  8, 43, 28, 50, 41, 42,  6,  5, 16,
        0, 59, 56, 37, 46,  2, 57, 10, 48,  7, 49, 62, 20, 34,  4, 13, 51,
       36, 31, 64, 17, 12, 29, 25, 35, 61,  1, 14, 40, 55,  9,  3],
      dtype=int64), 'cur_cost': 103931.0}, {'tour': [0, 12, 19, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12885.0}, {'tour': [0, 22, 17, 15, 12, 23, 16, 18, 19, 13, 20, 21, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12381.0}, {'tour': array([ 7, 53, 59, 15, 38, 10, 26,  5, 31, 29, 22,  2, 61,  1,  0, 37,  6,
       63, 39, 28, 34, 54, 14, 43, 30, 32, 45, 58, 60, 12, 41, 64, 36, 24,
       50, 35, 19, 16,  8, 65, 46, 11,  4, 55, 27, 40, 47, 13,  9, 25, 44,
       48, 49, 17, 20, 57, 21, 56, 42, 52,  3, 18, 33, 23, 51, 62],
      dtype=int64), 'cur_cost': 99215.0}, {'tour': array([49, 14, 25, 62, 44, 43, 30, 54, 38, 34, 24, 35, 40, 31, 50, 46, 63,
       19,  3,  1, 17, 29, 47, 11, 58,  0, 39, 42, 12,  2, 36, 13, 59, 27,
        6, 60, 22,  9, 41, 65, 18, 51, 21,  7, 56, 20, 48,  4, 23, 61, 28,
       45, 57,  5, 10, 16, 52, 33, 26, 53, 15,  8, 55, 64, 37, 32],
      dtype=int64), 'cur_cost': 108762.0}, {'tour': array([62, 39, 33, 13,  8, 40, 41,  2, 21, 55, 56, 25, 57, 46, 53, 58,  0,
       12,  5, 43, 59, 16, 15, 47, 35, 27, 42,  6, 60, 14, 30, 22, 26, 37,
       64, 17, 50, 65,  4, 29, 48,  7, 32,  1, 45, 44, 23, 11, 51, 18, 49,
       54,  9, 34, 38, 19, 61, 10, 28, 63, 36, 24, 31,  3, 20, 52],
      dtype=int64), 'cur_cost': 110062.0}, {'tour': array([10, 33, 45, 43, 59, 23, 26, 16, 63, 62, 22,  6, 64, 34, 24,  4, 40,
       50, 37, 46, 17, 32, 11, 65, 47,  5, 15, 42, 51, 25, 31, 49, 48,  3,
       28, 38, 30, 44, 35, 54, 56, 52,  8, 61, 14,  0, 29, 20, 19, 55, 21,
        9,  7, 60, 39, 12, 53,  2, 13, 58, 27, 18, 36,  1, 41, 57],
      dtype=int64), 'cur_cost': 102178.0}]
2025-08-03 17:08:55,319 - ExploitationExpert - INFO - 局部搜索耗时: 0.12秒
2025-08-03 17:08:55,320 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-03 17:08:55,320 - experts.management.collaboration_manager - INFO - 个体 16 利用路径生成报告: {'new_tour': array([ 7, 53, 59, 15, 38, 10, 26,  5, 31, 29, 22,  2, 61,  1,  0, 37,  6,
       63, 39, 28, 34, 54, 14, 43, 30, 32, 45, 58, 60, 12, 41, 64, 36, 24,
       50, 35, 19, 16,  8, 65, 46, 11,  4, 55, 27, 40, 47, 13,  9, 25, 44,
       48, 49, 17, 20, 57, 21, 56, 42, 52,  3, 18, 33, 23, 51, 62],
      dtype=int64), 'cur_cost': 99215.0}
2025-08-03 17:08:55,321 - experts.management.collaboration_manager - INFO - 个体 16 接受新路径 (成本: 99215.00)
2025-08-03 17:08:55,321 - experts.management.collaboration_manager - INFO - 为个体 17 生成探索路径
2025-08-03 17:08:55,322 - ExplorationExpert - INFO - 开始为个体 17 生成探索路径（算法实现）
2025-08-03 17:08:55,322 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:08:55,337 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 17:08:55,337 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:08:55,338 - ExplorationExpert - INFO - 探索路径生成完成，成本: 59381.0, 路径长度: 66
2025-08-03 17:08:55,338 - experts.management.collaboration_manager - INFO - 个体 17 探索路径生成报告: {'new_tour': [46, 36, 28, 22, 12, 20, 2, 7, 65, 0, 53, 55, 57, 58, 13, 27, 9, 64, 10, 8, 56, 14, 4, 6, 21, 5, 59, 62, 40, 15, 24, 23, 35, 11, 1, 16, 29, 19, 31, 43, 39, 44, 47, 51, 26, 49, 48, 37, 42, 41, 50, 45, 30, 33, 25, 32, 18, 17, 61, 60, 54, 63, 52, 3, 34, 38], 'cur_cost': 59381.0}
2025-08-03 17:08:55,339 - experts.management.collaboration_manager - INFO - 个体 17 接受新路径 (成本: 59381.00)
2025-08-03 17:08:55,339 - experts.management.collaboration_manager - INFO - 为个体 18 生成探索路径
2025-08-03 17:08:55,339 - ExplorationExpert - INFO - 开始为个体 18 生成探索路径（算法实现）
2025-08-03 17:08:55,340 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:08:55,345 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 17:08:55,347 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:08:55,350 - ExplorationExpert - INFO - 探索路径生成完成，成本: 90207.0, 路径长度: 66
2025-08-03 17:08:55,351 - experts.management.collaboration_manager - INFO - 个体 18 探索路径生成报告: {'new_tour': [7, 20, 25, 41, 19, 22, 3, 11, 64, 33, 62, 2, 1, 15, 18, 35, 32, 30, 5, 0, 56, 52, 17, 38, 40, 31, 47, 24, 10, 21, 28, 6, 13, 9, 65, 55, 59, 51, 34, 12, 43, 57, 63, 48, 27, 16, 39, 26, 50, 60, 4, 44, 8, 45, 46, 54, 58, 53, 61, 29, 42, 14, 49, 23, 37, 36], 'cur_cost': 90207.0}
2025-08-03 17:08:55,352 - experts.management.collaboration_manager - INFO - 个体 18 接受新路径 (成本: 90207.00)
2025-08-03 17:08:55,352 - experts.management.collaboration_manager - INFO - 为个体 19 生成利用路径
2025-08-03 17:08:55,352 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:08:55,352 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:08:55,353 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 19 处的路径，新成本: 98342.0
2025-08-03 17:08:55,438 - ExploitationExpert - INFO - res_population_num: 17
2025-08-03 17:08:55,438 - ExploitationExpert - INFO - res_population_costs: [9887.0, 9580.0, 9567.0, 9566, 9560, 9552, 9549, 9534, 9532, 9527, 9526, 9521, 9521, 9521, 9521, 9521, 9521]
2025-08-03 17:08:55,439 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 54, 57, 64, 53, 62,
       59, 56, 58, 60, 55, 61, 65, 52, 63,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59,
       56, 58, 60, 54, 57, 64, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59,
       53, 64, 57, 54, 52, 63, 65, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 17, 12, 22, 23, 13, 20, 21, 19, 16,
       18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 42, 50,
       45, 38, 51, 41, 44, 39, 47, 46, 48, 43, 40, 49, 58, 60, 56, 59, 62,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 43, 40, 49,
       58, 60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 56, 59, 62, 53, 61, 63, 52, 65, 64,
       57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 13, 23, 16, 18, 17, 12, 22, 15, 14, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 18, 16, 19, 21, 20, 13, 23,
       22, 12, 17, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 18, 16, 19, 21, 20, 13, 23,
       22, 12, 17, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 17:08:55,449 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:08:55,450 - ExploitationExpert - INFO - populations: [{'tour': [1, 62, 21, 12, 34, 41, 58, 18, 35, 61, 2, 33, 60, 51, 49, 57, 48, 8, 55, 7, 59, 53, 50, 0, 5, 32, 43, 56, 4, 29, 52, 16, 19, 38, 40, 44, 20, 3, 23, 25, 14, 15, 11, 36, 39, 27, 24, 45, 37, 22, 46, 10, 64, 31, 6, 13, 54, 28, 9, 17, 65, 26, 47, 42, 30, 63], 'cur_cost': 109041.0}, {'tour': array([36, 25, 28, 37,  4, 22, 51,  6,  0, 40, 45,  1, 19, 55, 58,  8, 34,
       44, 10, 57, 12, 39, 35, 50, 41, 27, 13, 64,  7, 48,  5, 43, 49, 18,
       65, 63, 14, 62, 33, 32, 53, 42, 47, 17, 30, 20,  3, 54, 31, 61, 60,
       11, 56, 24,  2, 59, 21,  9, 52, 46, 26, 38, 15, 29, 23, 16],
      dtype=int64), 'cur_cost': 98828.0}, {'tour': [63, 10, 3, 12, 25, 21, 15, 19, 16, 11, 64, 0, 61, 1, 53, 8, 7, 59, 18, 13, 28, 37, 32, 30, 35, 4, 52, 39, 23, 9, 56, 57, 58, 22, 43, 41, 34, 20, 14, 5, 31, 17, 33, 47, 49, 46, 44, 36, 2, 27, 42, 38, 50, 48, 24, 26, 45, 6, 62, 65, 55, 54, 60, 40, 51, 29], 'cur_cost': 65590.0}, {'tour': [31, 13, 16, 18, 24, 23, 30, 22, 3, 8, 7, 64, 62, 11, 57, 2, 1, 15, 26, 35, 27, 19, 43, 44, 45, 37, 0, 5, 52, 56, 17, 4, 58, 10, 53, 60, 6, 61, 12, 36, 40, 50, 49, 20, 48, 38, 41, 39, 47, 14, 25, 33, 21, 28, 32, 42, 51, 9, 63, 55, 65, 59, 46, 34, 29, 54], 'cur_cost': 60480.0}, {'tour': array([43, 46, 17, 48, 32, 55, 51,  5, 54,  9, 58, 61, 30, 16, 50, 27, 15,
       63, 23, 59, 47, 40,  1, 36, 13, 19, 57, 62,  6, 38, 49, 26, 33, 24,
       11, 53, 31, 35, 41, 37, 39, 14, 45, 12, 64, 42,  3,  2, 44, 21, 34,
       28, 20, 10,  0, 60, 22,  8, 56, 29,  7, 52, 25, 18,  4, 65],
      dtype=int64), 'cur_cost': 99902.0}, {'tour': [0, 7, 9, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12373.0}, {'tour': [0, 1, 18, 9, 11, 7, 3, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12781.0}, {'tour': array([41, 28,  3, 31, 44, 21, 48, 29, 23, 34, 39, 25, 27, 53, 13,  6, 51,
       60, 17, 58, 30, 40, 56, 18, 57, 43, 55, 64, 37,  7, 26, 38, 65,  5,
        0, 59, 10, 50, 12, 22,  4, 36, 45, 46, 42, 33,  8, 52, 63, 24, 49,
       62, 16, 11, 32, 15, 20, 35, 54,  2,  1, 19,  9, 61, 47, 14],
      dtype=int64), 'cur_cost': 110923.0}, {'tour': [0, 2, 19, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12498.0}, {'tour': [0, 18, 21, 17, 12, 22, 23, 16, 19, 13, 20, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12467.0}, {'tour': array([23, 65, 33, 21,  5, 18,  7, 26, 28, 43, 44,  9, 50, 58, 41, 54, 47,
       39,  8, 34, 10, 52,  6, 64, 60, 11, 29, 35,  3, 42, 30, 62, 31, 46,
       17, 24,  0,  1, 25, 53, 38, 19,  2, 63, 36, 45, 20, 32, 49, 48, 12,
       59, 22, 15, 40,  4, 13, 14, 51, 27, 55, 57, 37, 61, 16, 56],
      dtype=int64), 'cur_cost': 112176.0}, {'tour': [0, 11, 21, 6, 2, 8, 5, 4, 9, 3, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12806.0}, {'tour': [0, 10, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12356.0}, {'tour': array([21, 19, 45, 47, 52, 32, 65, 27, 58, 38, 44, 60, 39, 33, 54, 18, 15,
       23, 53, 63, 30, 11, 26, 24, 22,  8, 43, 28, 50, 41, 42,  6,  5, 16,
        0, 59, 56, 37, 46,  2, 57, 10, 48,  7, 49, 62, 20, 34,  4, 13, 51,
       36, 31, 64, 17, 12, 29, 25, 35, 61,  1, 14, 40, 55,  9,  3],
      dtype=int64), 'cur_cost': 103931.0}, {'tour': [0, 12, 19, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12885.0}, {'tour': [0, 22, 17, 15, 12, 23, 16, 18, 19, 13, 20, 21, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12381.0}, {'tour': array([ 7, 53, 59, 15, 38, 10, 26,  5, 31, 29, 22,  2, 61,  1,  0, 37,  6,
       63, 39, 28, 34, 54, 14, 43, 30, 32, 45, 58, 60, 12, 41, 64, 36, 24,
       50, 35, 19, 16,  8, 65, 46, 11,  4, 55, 27, 40, 47, 13,  9, 25, 44,
       48, 49, 17, 20, 57, 21, 56, 42, 52,  3, 18, 33, 23, 51, 62],
      dtype=int64), 'cur_cost': 99215.0}, {'tour': [46, 36, 28, 22, 12, 20, 2, 7, 65, 0, 53, 55, 57, 58, 13, 27, 9, 64, 10, 8, 56, 14, 4, 6, 21, 5, 59, 62, 40, 15, 24, 23, 35, 11, 1, 16, 29, 19, 31, 43, 39, 44, 47, 51, 26, 49, 48, 37, 42, 41, 50, 45, 30, 33, 25, 32, 18, 17, 61, 60, 54, 63, 52, 3, 34, 38], 'cur_cost': 59381.0}, {'tour': [7, 20, 25, 41, 19, 22, 3, 11, 64, 33, 62, 2, 1, 15, 18, 35, 32, 30, 5, 0, 56, 52, 17, 38, 40, 31, 47, 24, 10, 21, 28, 6, 13, 9, 65, 55, 59, 51, 34, 12, 43, 57, 63, 48, 27, 16, 39, 26, 50, 60, 4, 44, 8, 45, 46, 54, 58, 53, 61, 29, 42, 14, 49, 23, 37, 36], 'cur_cost': 90207.0}, {'tour': array([48, 29, 33,  9, 51, 40, 10,  0, 43,  8, 30, 11, 22, 46, 44, 59, 62,
        5,  2, 12, 28, 50, 21, 57, 54, 32, 26, 61, 37, 42, 18, 49, 20, 14,
       31, 41, 17, 13, 34, 53, 25, 60, 65, 55, 36, 35,  1, 63,  6,  3, 64,
       15, 24, 19, 39, 16, 56,  7, 23, 45, 47, 52, 38,  4, 58, 27],
      dtype=int64), 'cur_cost': 98342.0}]
2025-08-03 17:08:55,456 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 17:08:55,456 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-03 17:08:55,457 - experts.management.collaboration_manager - INFO - 个体 19 利用路径生成报告: {'new_tour': array([48, 29, 33,  9, 51, 40, 10,  0, 43,  8, 30, 11, 22, 46, 44, 59, 62,
        5,  2, 12, 28, 50, 21, 57, 54, 32, 26, 61, 37, 42, 18, 49, 20, 14,
       31, 41, 17, 13, 34, 53, 25, 60, 65, 55, 36, 35,  1, 63,  6,  3, 64,
       15, 24, 19, 39, 16, 56,  7, 23, 45, 47, 52, 38,  4, 58, 27],
      dtype=int64), 'cur_cost': 98342.0}
2025-08-03 17:08:55,457 - experts.management.collaboration_manager - INFO - 个体 19 接受新路径 (成本: 98342.00)
2025-08-03 17:08:55,458 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 16, 'rejected': 4, 'elite_protected': 4}
2025-08-03 17:08:55,458 - experts.management.collaboration_manager - INFO - 接受率: 16/20 (80.0%)
2025-08-03 17:08:55,459 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [1, 62, 21, 12, 34, 41, 58, 18, 35, 61, 2, 33, 60, 51, 49, 57, 48, 8, 55, 7, 59, 53, 50, 0, 5, 32, 43, 56, 4, 29, 52, 16, 19, 38, 40, 44, 20, 3, 23, 25, 14, 15, 11, 36, 39, 27, 24, 45, 37, 22, 46, 10, 64, 31, 6, 13, 54, 28, 9, 17, 65, 26, 47, 42, 30, 63], 'cur_cost': 109041.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([36, 25, 28, 37,  4, 22, 51,  6,  0, 40, 45,  1, 19, 55, 58,  8, 34,
       44, 10, 57, 12, 39, 35, 50, 41, 27, 13, 64,  7, 48,  5, 43, 49, 18,
       65, 63, 14, 62, 33, 32, 53, 42, 47, 17, 30, 20,  3, 54, 31, 61, 60,
       11, 56, 24,  2, 59, 21,  9, 52, 46, 26, 38, 15, 29, 23, 16],
      dtype=int64), 'cur_cost': 98828.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [63, 10, 3, 12, 25, 21, 15, 19, 16, 11, 64, 0, 61, 1, 53, 8, 7, 59, 18, 13, 28, 37, 32, 30, 35, 4, 52, 39, 23, 9, 56, 57, 58, 22, 43, 41, 34, 20, 14, 5, 31, 17, 33, 47, 49, 46, 44, 36, 2, 27, 42, 38, 50, 48, 24, 26, 45, 6, 62, 65, 55, 54, 60, 40, 51, 29], 'cur_cost': 65590.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [31, 13, 16, 18, 24, 23, 30, 22, 3, 8, 7, 64, 62, 11, 57, 2, 1, 15, 26, 35, 27, 19, 43, 44, 45, 37, 0, 5, 52, 56, 17, 4, 58, 10, 53, 60, 6, 61, 12, 36, 40, 50, 49, 20, 48, 38, 41, 39, 47, 14, 25, 33, 21, 28, 32, 42, 51, 9, 63, 55, 65, 59, 46, 34, 29, 54], 'cur_cost': 60480.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([43, 46, 17, 48, 32, 55, 51,  5, 54,  9, 58, 61, 30, 16, 50, 27, 15,
       63, 23, 59, 47, 40,  1, 36, 13, 19, 57, 62,  6, 38, 49, 26, 33, 24,
       11, 53, 31, 35, 41, 37, 39, 14, 45, 12, 64, 42,  3,  2, 44, 21, 34,
       28, 20, 10,  0, 60, 22,  8, 56, 29,  7, 52, 25, 18,  4, 65],
      dtype=int64), 'cur_cost': 99902.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 9, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12373.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 18, 9, 11, 7, 3, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12781.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([41, 28,  3, 31, 44, 21, 48, 29, 23, 34, 39, 25, 27, 53, 13,  6, 51,
       60, 17, 58, 30, 40, 56, 18, 57, 43, 55, 64, 37,  7, 26, 38, 65,  5,
        0, 59, 10, 50, 12, 22,  4, 36, 45, 46, 42, 33,  8, 52, 63, 24, 49,
       62, 16, 11, 32, 15, 20, 35, 54,  2,  1, 19,  9, 61, 47, 14],
      dtype=int64), 'cur_cost': 110923.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 19, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12498.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 21, 17, 12, 22, 23, 16, 19, 13, 20, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12467.0}}, {'individual': 10, 'strategy': 'exploit', 'path_data': {'new_tour': array([23, 65, 33, 21,  5, 18,  7, 26, 28, 43, 44,  9, 50, 58, 41, 54, 47,
       39,  8, 34, 10, 52,  6, 64, 60, 11, 29, 35,  3, 42, 30, 62, 31, 46,
       17, 24,  0,  1, 25, 53, 38, 19,  2, 63, 36, 45, 20, 32, 49, 48, 12,
       59, 22, 15, 40,  4, 13, 14, 51, 27, 55, 57, 37, 61, 16, 56],
      dtype=int64), 'cur_cost': 112176.0}}, {'individual': 11, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 21, 6, 2, 8, 5, 4, 9, 3, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12806.0}}, {'individual': 12, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12356.0}}, {'individual': 13, 'strategy': 'exploit', 'path_data': {'new_tour': array([21, 19, 45, 47, 52, 32, 65, 27, 58, 38, 44, 60, 39, 33, 54, 18, 15,
       23, 53, 63, 30, 11, 26, 24, 22,  8, 43, 28, 50, 41, 42,  6,  5, 16,
        0, 59, 56, 37, 46,  2, 57, 10, 48,  7, 49, 62, 20, 34,  4, 13, 51,
       36, 31, 64, 17, 12, 29, 25, 35, 61,  1, 14, 40, 55,  9,  3],
      dtype=int64), 'cur_cost': 103931.0}}, {'individual': 14, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 19, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12885.0}}, {'individual': 15, 'strategy': 'explore', 'path_data': {'new_tour': [0, 22, 17, 15, 12, 23, 16, 18, 19, 13, 20, 21, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12381.0}}, {'individual': 16, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 7, 53, 59, 15, 38, 10, 26,  5, 31, 29, 22,  2, 61,  1,  0, 37,  6,
       63, 39, 28, 34, 54, 14, 43, 30, 32, 45, 58, 60, 12, 41, 64, 36, 24,
       50, 35, 19, 16,  8, 65, 46, 11,  4, 55, 27, 40, 47, 13,  9, 25, 44,
       48, 49, 17, 20, 57, 21, 56, 42, 52,  3, 18, 33, 23, 51, 62],
      dtype=int64), 'cur_cost': 99215.0}}, {'individual': 17, 'strategy': 'explore', 'path_data': {'new_tour': [46, 36, 28, 22, 12, 20, 2, 7, 65, 0, 53, 55, 57, 58, 13, 27, 9, 64, 10, 8, 56, 14, 4, 6, 21, 5, 59, 62, 40, 15, 24, 23, 35, 11, 1, 16, 29, 19, 31, 43, 39, 44, 47, 51, 26, 49, 48, 37, 42, 41, 50, 45, 30, 33, 25, 32, 18, 17, 61, 60, 54, 63, 52, 3, 34, 38], 'cur_cost': 59381.0}}, {'individual': 18, 'strategy': 'explore', 'path_data': {'new_tour': [7, 20, 25, 41, 19, 22, 3, 11, 64, 33, 62, 2, 1, 15, 18, 35, 32, 30, 5, 0, 56, 52, 17, 38, 40, 31, 47, 24, 10, 21, 28, 6, 13, 9, 65, 55, 59, 51, 34, 12, 43, 57, 63, 48, 27, 16, 39, 26, 50, 60, 4, 44, 8, 45, 46, 54, 58, 53, 61, 29, 42, 14, 49, 23, 37, 36], 'cur_cost': 90207.0}}, {'individual': 19, 'strategy': 'exploit', 'path_data': {'new_tour': array([48, 29, 33,  9, 51, 40, 10,  0, 43,  8, 30, 11, 22, 46, 44, 59, 62,
        5,  2, 12, 28, 50, 21, 57, 54, 32, 26, 61, 37, 42, 18, 49, 20, 14,
       31, 41, 17, 13, 34, 53, 25, 60, 65, 55, 36, 35,  1, 63,  6,  3, 64,
       15, 24, 19, 39, 16, 56,  7, 23, 45, 47, 52, 38,  4, 58, 27],
      dtype=int64), 'cur_cost': 98342.0}}]
2025-08-03 17:08:55,464 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-03 17:08:55,464 - StatsExpert - INFO - 开始统计分析
2025-08-03 17:08:55,476 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=12356.000, 多样性=0.943
2025-08-03 17:08:55,476 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-03 17:08:55,477 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-03 17:08:55,478 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-03 17:08:55,483 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 1, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.013153931991343695, 'best_improvement': -0.2532711228319302}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.021922567835870217}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 17, 'new_count': 17, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.8600713012477719, 'new_diversity': 0.8600713012477719, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-03 17:08:55,488 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-03 17:08:55,495 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_solution.json
2025-08-03 17:08:55,496 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_20250803_170855.solution
2025-08-03 17:08:55,497 - __main__ - INFO - 实例 composite13_66 处理完成
