enable_llm_reasoning: true
exploitation_strategy:
  convergence_threshold: 1e-6
  default_search_depth: 10
  elite_guidance_weight: 0.7
  local_search_intensity: 0.5
  max_stagnation_tolerance: 5
  step_size: 0.1
exploration_strategy:
  default_intensity: 0.5
  diversification_bias: 0.4
  max_perturbation_strength: 0.8
  mutation_probability: 0.2
  novelty_seeking_factor: 0.6
  region_focus_threshold: 0.6
  search_radius: 0.3
landscape_analysis:
  gradient_estimation_samples: 10
  modality_detection_threshold: 0.1
  neighborhood_size: 20
  ruggedness_calculation_method: autocorrelation
llm_interface:
  fallback_strategy: rule_based
  max_retries: 3
  max_tokens: 2000
  model: deepseek-chat
  provider: deepseek
  temperature: 0.1
  timeout: 30
llm_provider: mock
logging:
  backup_count: 5
  file_rotation: true
  level: INFO
  max_file_size: 10MB
  structured_logging: true
performance_monitoring:
  dashboard_port: 8050
  enable_dashboard: true
  feedback_aggregation_window: 10
  metrics_collection_interval: 1
  performance_history_length: 100
resource_management:
  cache_cleanup_interval: 100
  cpu_utilization_target: 0.85
  memory_threshold: 0.8
  thread_pool_size: 8
strategy_config:
  enable_llm_reasoning: false
  fallback_to_algorithmic: true
  max_llm_retries: 3
  response_parser:
    auto_repair: true
    default_confidence: 0.6
    default_priority: 0.5
    strict_validation: false
  state_analyzer:
    diversity_method: hamming
    history_window: 15
    improvement_threshold: 1e-5
system:
  cache_size: 1000
  deployment_mode: standard
  max_workers: 8
  timeout_seconds: 300
