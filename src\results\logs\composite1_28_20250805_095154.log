2025-08-05 09:51:54,476 - __main__ - INFO - composite1_28 开始进化第 1 代
2025-08-05 09:51:54,476 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 09:51:54,477 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:54,479 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=3209.000, 多样性=0.943
2025-08-05 09:51:54,481 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:54,482 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.943
2025-08-05 09:51:54,483 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:54,485 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 09:51:54,486 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:54,486 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 09:51:54,486 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 09:51:54,496 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.200, 适应度梯度: -1185.720, 聚类评分: 0.000, 覆盖率: 0.075, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:51:54,496 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 09:51:54,497 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 09:51:54,497 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite1_28
2025-08-05 09:51:54,503 - visualization.landscape_visualizer - INFO - 插值约束: 17 个点被约束到最小值 3209.00
2025-08-05 09:51:54,590 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite1_28\landscape_composite1_28_iter_61_20250805_095154.html
2025-08-05 09:51:54,639 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite1_28\dashboard_composite1_28_iter_61_20250805_095154.html
2025-08-05 09:51:54,639 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 61
2025-08-05 09:51:54,640 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 09:51:54,640 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1546秒
2025-08-05 09:51:54,640 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 122, 'max_size': 500, 'hits': 0, 'misses': 122, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 404, 'misses': 212, 'hit_rate': 0.6558441558441559, 'evictions': 112, 'ttl': 7200}}
2025-08-05 09:51:54,640 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -1185.7200000000003, 'local_optima_density': 0.2, 'gradient_variance': 90844584.1936, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0746, 'fitness_entropy': 0.9854752972273343, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -1185.720)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.075)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358714.496884, 'performance_metrics': {}}}
2025-08-05 09:51:54,640 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:54,640 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:54,640 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:54,640 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:54,641 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:51:54,641 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 09:51:54,642 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:51:54,642 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:54,642 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:54,642 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:51:54,642 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:54,642 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:54,642 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:54,644 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:51:54,644 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:51:54,644 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:54,644 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 28
2025-08-05 09:51:54,644 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,645 - ExplorationExpert - INFO - 探索路径生成完成，成本: 5353.0, 路径长度: 28, 收集中间解: 0
2025-08-05 09:51:54,645 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 18, 1, 13, 17, 14, 15, 16, 12, 9, 11, 10, 21, 26, 24, 25, 23, 27, 19, 20, 22, 8, 7, 5, 3, 4, 6, 2], 'cur_cost': 5353.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:54,645 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 5353.00)
2025-08-05 09:51:54,645 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:51:54,645 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:51:54,645 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:54,646 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 28
2025-08-05 09:51:54,646 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,647 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3208.0, 路径长度: 28, 收集中间解: 0
2025-08-05 09:51:54,647 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 5, 19, 24, 25, 26, 22, 21, 20, 27, 23, 10, 14, 17, 16, 15, 9, 18, 12, 13, 11, 8, 7, 6, 1, 2, 3, 4], 'cur_cost': 3208.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:54,647 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 3208.00)
2025-08-05 09:51:54,647 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:51:54,647 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:51:54,648 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:54,649 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 28
2025-08-05 09:51:54,649 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,649 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3725.0, 路径长度: 28, 收集中间解: 0
2025-08-05 09:51:54,649 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 11, 16, 24, 25, 26, 22, 21, 20, 19, 27, 23, 10, 14, 17, 13, 12, 18, 15, 9, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3725.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:54,649 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 3725.00)
2025-08-05 09:51:54,649 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:51:54,649 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:54,650 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:54,650 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 12336.0
2025-08-05 09:51:54,656 - ExploitationExpert - INFO - res_population_num: 3
2025-08-05 09:51:54,657 - ExploitationExpert - INFO - res_population_costs: [3062.0, 3062, 3061]
2025-08-05 09:51:54,657 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 19, 27, 25, 23, 22, 26, 24, 20,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 20, 24, 26, 22, 23,
       25, 27, 19,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 19, 24, 25, 27, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64)]
2025-08-05 09:51:54,658 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:54,659 - ExploitationExpert - INFO - populations: [{'tour': [0, 18, 1, 13, 17, 14, 15, 16, 12, 9, 11, 10, 21, 26, 24, 25, 23, 27, 19, 20, 22, 8, 7, 5, 3, 4, 6, 2], 'cur_cost': 5353.0}, {'tour': [0, 5, 19, 24, 25, 26, 22, 21, 20, 27, 23, 10, 14, 17, 16, 15, 9, 18, 12, 13, 11, 8, 7, 6, 1, 2, 3, 4], 'cur_cost': 3208.0}, {'tour': [0, 11, 16, 24, 25, 26, 22, 21, 20, 19, 27, 23, 10, 14, 17, 13, 12, 18, 15, 9, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3725.0}, {'tour': array([ 8,  5, 22, 16, 26, 25, 15,  4, 11, 18, 13,  7, 12, 20, 19, 27, 21,
       23, 10,  3,  2,  6, 24, 17,  9, 14,  1,  0], dtype=int64), 'cur_cost': 12336.0}, {'tour': array([ 7, 22, 26,  1, 12, 21, 13,  5,  2,  9, 10,  0,  4, 24, 27, 18,  3,
        8, 17,  6, 19, 11, 16, 23, 15, 20, 25, 14], dtype=int64), 'cur_cost': 17548.0}, {'tour': array([ 5, 21, 27, 13,  6, 23,  8, 24,  3, 17, 25, 19, 26, 15, 11, 20,  2,
        0, 18,  1,  7,  4, 10, 16, 14, 22, 12,  9], dtype=int64), 'cur_cost': 16458.0}, {'tour': array([19, 16, 25, 20, 15, 11, 12, 10, 13, 21,  3,  8, 26, 24,  9,  5,  0,
        7, 22, 27, 18,  4, 23,  1, 14,  2, 17,  6], dtype=int64), 'cur_cost': 16601.0}, {'tour': array([21,  2, 20, 17, 13,  7, 18, 22,  5, 26, 11, 25, 16, 19, 23,  0,  1,
        9, 14,  3, 27, 10,  6,  4, 12, 24,  8, 15], dtype=int64), 'cur_cost': 20135.0}, {'tour': array([10, 16, 24,  1,  3, 19, 21,  8, 13, 25,  0, 15, 12,  2, 14, 27,  6,
       17, 26,  7, 11,  9,  5,  4, 18, 20, 23, 22], dtype=int64), 'cur_cost': 18455.0}, {'tour': array([ 9,  1,  4, 23, 12, 27,  6, 20,  2, 10, 13, 25,  5,  3, 24, 15, 18,
       16, 19, 21, 11,  0, 26,  7, 14, 22,  8, 17], dtype=int64), 'cur_cost': 18980.0}]
2025-08-05 09:51:54,661 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:54,661 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 157, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 157, 'cache_hits': 0, 'similarity_calculations': 654, 'cache_hit_rate': 0.0, 'cache_size': 654}}
2025-08-05 09:51:54,662 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 8,  5, 22, 16, 26, 25, 15,  4, 11, 18, 13,  7, 12, 20, 19, 27, 21,
       23, 10,  3,  2,  6, 24, 17,  9, 14,  1,  0], dtype=int64), 'cur_cost': 12336.0, 'intermediate_solutions': [{'tour': array([ 9, 21, 20,  1, 16,  3, 18,  8, 14,  5,  2, 10, 27,  4, 13, 17, 22,
       12,  7, 23, 24, 26,  6, 19, 11, 25,  0, 15], dtype=int64), 'cur_cost': 20480.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1,  9, 21, 20, 16,  3, 18,  8, 14,  5,  2, 10, 27,  4, 13, 17, 22,
       12,  7, 23, 24, 26,  6, 19, 11, 25,  0, 15], dtype=int64), 'cur_cost': 21022.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([16,  1,  9, 21, 20,  3, 18,  8, 14,  5,  2, 10, 27,  4, 13, 17, 22,
       12,  7, 23, 24, 26,  6, 19, 11, 25,  0, 15], dtype=int64), 'cur_cost': 20470.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([20,  1,  9, 21, 16,  3, 18,  8, 14,  5,  2, 10, 27,  4, 13, 17, 22,
       12,  7, 23, 24, 26,  6, 19, 11, 25,  0, 15], dtype=int64), 'cur_cost': 21496.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([20, 16,  1,  9, 21,  3, 18,  8, 14,  5,  2, 10, 27,  4, 13, 17, 22,
       12,  7, 23, 24, 26,  6, 19, 11, 25,  0, 15], dtype=int64), 'cur_cost': 21550.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:54,662 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 12336.00)
2025-08-05 09:51:54,662 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:51:54,662 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:51:54,663 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:54,663 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 28
2025-08-05 09:51:54,664 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,664 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14370.0, 路径长度: 28, 收集中间解: 0
2025-08-05 09:51:54,664 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [11, 19, 16, 23, 21, 17, 13, 9, 15, 22, 5, 1, 4, 2, 26, 3, 0, 25, 20, 6, 10, 24, 27, 8, 12, 18, 14, 7], 'cur_cost': 14370.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:54,664 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 14370.00)
2025-08-05 09:51:54,664 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:51:54,664 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:51:54,664 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:54,665 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 28
2025-08-05 09:51:54,665 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,666 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14826.0, 路径长度: 28, 收集中间解: 0
2025-08-05 09:51:54,666 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [11, 14, 15, 27, 23, 21, 17, 16, 25, 9, 13, 5, 3, 18, 1, 19, 12, 22, 20, 7, 4, 26, 8, 2, 6, 10, 24, 0], 'cur_cost': 14826.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:54,666 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 14826.00)
2025-08-05 09:51:54,666 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:51:54,666 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:51:54,666 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:54,668 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 28
2025-08-05 09:51:54,668 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,668 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9109.0, 路径长度: 28, 收集中间解: 0
2025-08-05 09:51:54,668 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [17, 13, 20, 10, 24, 16, 11, 18, 21, 15, 14, 25, 22, 27, 26, 8, 0, 5, 7, 1, 3, 4, 9, 12, 23, 19, 6, 2], 'cur_cost': 9109.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:54,668 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 9109.00)
2025-08-05 09:51:54,668 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 09:51:54,668 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:54,669 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:54,669 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 17248.0
2025-08-05 09:51:54,678 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 09:51:54,678 - ExploitationExpert - INFO - res_population_costs: [3062.0, 3062, 3061, 3055.0, 3055, 3055]
2025-08-05 09:51:54,678 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 19, 27, 25, 23, 22, 26, 24, 20,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 20, 24, 26, 22, 23,
       25, 27, 19,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 19, 24, 25, 27, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64)]
2025-08-05 09:51:54,680 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:54,680 - ExploitationExpert - INFO - populations: [{'tour': [0, 18, 1, 13, 17, 14, 15, 16, 12, 9, 11, 10, 21, 26, 24, 25, 23, 27, 19, 20, 22, 8, 7, 5, 3, 4, 6, 2], 'cur_cost': 5353.0}, {'tour': [0, 5, 19, 24, 25, 26, 22, 21, 20, 27, 23, 10, 14, 17, 16, 15, 9, 18, 12, 13, 11, 8, 7, 6, 1, 2, 3, 4], 'cur_cost': 3208.0}, {'tour': [0, 11, 16, 24, 25, 26, 22, 21, 20, 19, 27, 23, 10, 14, 17, 13, 12, 18, 15, 9, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3725.0}, {'tour': array([ 8,  5, 22, 16, 26, 25, 15,  4, 11, 18, 13,  7, 12, 20, 19, 27, 21,
       23, 10,  3,  2,  6, 24, 17,  9, 14,  1,  0], dtype=int64), 'cur_cost': 12336.0}, {'tour': [11, 19, 16, 23, 21, 17, 13, 9, 15, 22, 5, 1, 4, 2, 26, 3, 0, 25, 20, 6, 10, 24, 27, 8, 12, 18, 14, 7], 'cur_cost': 14370.0}, {'tour': [11, 14, 15, 27, 23, 21, 17, 16, 25, 9, 13, 5, 3, 18, 1, 19, 12, 22, 20, 7, 4, 26, 8, 2, 6, 10, 24, 0], 'cur_cost': 14826.0}, {'tour': [17, 13, 20, 10, 24, 16, 11, 18, 21, 15, 14, 25, 22, 27, 26, 8, 0, 5, 7, 1, 3, 4, 9, 12, 23, 19, 6, 2], 'cur_cost': 9109.0}, {'tour': array([ 1, 22,  8, 12, 14, 26, 16, 13, 17, 10, 21, 27, 23,  4, 15,  5, 11,
       24, 25, 19,  6,  9,  0, 18,  7,  3, 20,  2], dtype=int64), 'cur_cost': 17248.0}, {'tour': array([10, 16, 24,  1,  3, 19, 21,  8, 13, 25,  0, 15, 12,  2, 14, 27,  6,
       17, 26,  7, 11,  9,  5,  4, 18, 20, 23, 22], dtype=int64), 'cur_cost': 18455.0}, {'tour': array([ 9,  1,  4, 23, 12, 27,  6, 20,  2, 10, 13, 25,  5,  3, 24, 15, 18,
       16, 19, 21, 11,  0, 26,  7, 14, 22,  8, 17], dtype=int64), 'cur_cost': 18980.0}]
2025-08-05 09:51:54,682 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:54,682 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 158, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 158, 'cache_hits': 0, 'similarity_calculations': 655, 'cache_hit_rate': 0.0, 'cache_size': 655}}
2025-08-05 09:51:54,683 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([ 1, 22,  8, 12, 14, 26, 16, 13, 17, 10, 21, 27, 23,  4, 15,  5, 11,
       24, 25, 19,  6,  9,  0, 18,  7,  3, 20,  2], dtype=int64), 'cur_cost': 17248.0, 'intermediate_solutions': [{'tour': array([20,  2, 21, 17, 13,  7, 18, 22,  5, 26, 11, 25, 16, 19, 23,  0,  1,
        9, 14,  3, 27, 10,  6,  4, 12, 24,  8, 15], dtype=int64), 'cur_cost': 20134.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([17, 20,  2, 21, 13,  7, 18, 22,  5, 26, 11, 25, 16, 19, 23,  0,  1,
        9, 14,  3, 27, 10,  6,  4, 12, 24,  8, 15], dtype=int64), 'cur_cost': 20114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([13, 17, 20,  2, 21,  7, 18, 22,  5, 26, 11, 25, 16, 19, 23,  0,  1,
        9, 14,  3, 27, 10,  6,  4, 12, 24,  8, 15], dtype=int64), 'cur_cost': 19612.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([21, 17, 20,  2, 13,  7, 18, 22,  5, 26, 11, 25, 16, 19, 23,  0,  1,
        9, 14,  3, 27, 10,  6,  4, 12, 24,  8, 15], dtype=int64), 'cur_cost': 20667.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([21, 13, 17, 20,  2,  7, 18, 22,  5, 26, 11, 25, 16, 19, 23,  0,  1,
        9, 14,  3, 27, 10,  6,  4, 12, 24,  8, 15], dtype=int64), 'cur_cost': 18539.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:54,683 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 17248.00)
2025-08-05 09:51:54,683 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:51:54,683 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:51:54,683 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:54,684 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 28
2025-08-05 09:51:54,684 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,685 - ExplorationExpert - INFO - 探索路径生成完成，成本: 4331.0, 路径长度: 28, 收集中间解: 0
2025-08-05 09:51:54,685 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 15, 26, 9, 18, 16, 17, 14, 11, 10, 13, 12, 21, 20, 24, 25, 23, 27, 19, 22, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 4331.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:54,685 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 4331.00)
2025-08-05 09:51:54,685 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:51:54,685 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:51:54,685 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:54,687 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 28
2025-08-05 09:51:54,687 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,687 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10040.0, 路径长度: 28, 收集中间解: 0
2025-08-05 09:51:54,687 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 11, 21, 10, 24, 26, 14, 9, 18, 15, 19, 27, 17, 12, 25, 16, 22, 23, 7, 8, 1, 5, 4, 6, 2, 3, 20, 13], 'cur_cost': 10040.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:54,688 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 10040.00)
2025-08-05 09:51:54,688 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:54,688 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:54,689 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 1, 13, 17, 14, 15, 16, 12, 9, 11, 10, 21, 26, 24, 25, 23, 27, 19, 20, 22, 8, 7, 5, 3, 4, 6, 2], 'cur_cost': 5353.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 19, 24, 25, 26, 22, 21, 20, 27, 23, 10, 14, 17, 16, 15, 9, 18, 12, 13, 11, 8, 7, 6, 1, 2, 3, 4], 'cur_cost': 3208.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 16, 24, 25, 26, 22, 21, 20, 19, 27, 23, 10, 14, 17, 13, 12, 18, 15, 9, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3725.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 8,  5, 22, 16, 26, 25, 15,  4, 11, 18, 13,  7, 12, 20, 19, 27, 21,
       23, 10,  3,  2,  6, 24, 17,  9, 14,  1,  0], dtype=int64), 'cur_cost': 12336.0, 'intermediate_solutions': [{'tour': array([ 9, 21, 20,  1, 16,  3, 18,  8, 14,  5,  2, 10, 27,  4, 13, 17, 22,
       12,  7, 23, 24, 26,  6, 19, 11, 25,  0, 15], dtype=int64), 'cur_cost': 20480.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1,  9, 21, 20, 16,  3, 18,  8, 14,  5,  2, 10, 27,  4, 13, 17, 22,
       12,  7, 23, 24, 26,  6, 19, 11, 25,  0, 15], dtype=int64), 'cur_cost': 21022.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([16,  1,  9, 21, 20,  3, 18,  8, 14,  5,  2, 10, 27,  4, 13, 17, 22,
       12,  7, 23, 24, 26,  6, 19, 11, 25,  0, 15], dtype=int64), 'cur_cost': 20470.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([20,  1,  9, 21, 16,  3, 18,  8, 14,  5,  2, 10, 27,  4, 13, 17, 22,
       12,  7, 23, 24, 26,  6, 19, 11, 25,  0, 15], dtype=int64), 'cur_cost': 21496.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([20, 16,  1,  9, 21,  3, 18,  8, 14,  5,  2, 10, 27,  4, 13, 17, 22,
       12,  7, 23, 24, 26,  6, 19, 11, 25,  0, 15], dtype=int64), 'cur_cost': 21550.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [11, 19, 16, 23, 21, 17, 13, 9, 15, 22, 5, 1, 4, 2, 26, 3, 0, 25, 20, 6, 10, 24, 27, 8, 12, 18, 14, 7], 'cur_cost': 14370.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [11, 14, 15, 27, 23, 21, 17, 16, 25, 9, 13, 5, 3, 18, 1, 19, 12, 22, 20, 7, 4, 26, 8, 2, 6, 10, 24, 0], 'cur_cost': 14826.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [17, 13, 20, 10, 24, 16, 11, 18, 21, 15, 14, 25, 22, 27, 26, 8, 0, 5, 7, 1, 3, 4, 9, 12, 23, 19, 6, 2], 'cur_cost': 9109.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 1, 22,  8, 12, 14, 26, 16, 13, 17, 10, 21, 27, 23,  4, 15,  5, 11,
       24, 25, 19,  6,  9,  0, 18,  7,  3, 20,  2], dtype=int64), 'cur_cost': 17248.0, 'intermediate_solutions': [{'tour': array([20,  2, 21, 17, 13,  7, 18, 22,  5, 26, 11, 25, 16, 19, 23,  0,  1,
        9, 14,  3, 27, 10,  6,  4, 12, 24,  8, 15], dtype=int64), 'cur_cost': 20134.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([17, 20,  2, 21, 13,  7, 18, 22,  5, 26, 11, 25, 16, 19, 23,  0,  1,
        9, 14,  3, 27, 10,  6,  4, 12, 24,  8, 15], dtype=int64), 'cur_cost': 20114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([13, 17, 20,  2, 21,  7, 18, 22,  5, 26, 11, 25, 16, 19, 23,  0,  1,
        9, 14,  3, 27, 10,  6,  4, 12, 24,  8, 15], dtype=int64), 'cur_cost': 19612.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([21, 17, 20,  2, 13,  7, 18, 22,  5, 26, 11, 25, 16, 19, 23,  0,  1,
        9, 14,  3, 27, 10,  6,  4, 12, 24,  8, 15], dtype=int64), 'cur_cost': 20667.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([21, 13, 17, 20,  2,  7, 18, 22,  5, 26, 11, 25, 16, 19, 23,  0,  1,
        9, 14,  3, 27, 10,  6,  4, 12, 24,  8, 15], dtype=int64), 'cur_cost': 18539.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 26, 9, 18, 16, 17, 14, 11, 10, 13, 12, 21, 20, 24, 25, 23, 27, 19, 22, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 4331.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 21, 10, 24, 26, 14, 9, 18, 15, 19, 27, 17, 12, 25, 16, 22, 23, 7, 8, 1, 5, 4, 6, 2, 3, 20, 13], 'cur_cost': 10040.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 09:51:54,690 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:54,690 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:54,691 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=3208.000, 多样性=0.936
2025-08-05 09:51:54,691 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 09:51:54,691 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 09:51:54,691 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:54,692 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.1665593913089831, 'best_improvement': 0.0003116235587410408}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.00757575757575755}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.02856062612856182, 'recent_improvements': [-0.03995822001245967, 0.06760276058986807, -0.09707947226958329], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 6, 'new_count': 6, 'count_change': 0, 'old_best_cost': 3055.0, 'new_best_cost': 3055.0, 'quality_improvement': 0.0, 'old_diversity': 0.7738095238095238, 'new_diversity': 0.7738095238095238, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配']}
2025-08-05 09:51:54,692 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 09:51:54,692 - __main__ - INFO - composite1_28 开始进化第 2 代
2025-08-05 09:51:54,692 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 09:51:54,693 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:54,693 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=3208.000, 多样性=0.936
2025-08-05 09:51:54,693 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:54,695 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.936
2025-08-05 09:51:54,695 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:54,696 - EliteExpert - INFO - 精英解分析完成: 精英解数量=6, 多样性=0.774
2025-08-05 09:51:54,698 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 09:51:54,698 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:54,698 - LandscapeExpert - INFO - 添加精英解数据: 6个精英解
2025-08-05 09:51:54,698 - LandscapeExpert - INFO - 数据提取成功: 16个路径, 16个适应度值
2025-08-05 09:51:54,720 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.188, 适应度梯度: -1494.788, 聚类评分: 0.000, 覆盖率: 0.076, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:51:54,720 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 09:51:54,720 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:51:54,720 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite1_28
2025-08-05 09:51:54,726 - visualization.landscape_visualizer - INFO - 插值约束: 17 个点被约束到最小值 3055.00
2025-08-05 09:51:54,846 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite1_28\landscape_composite1_28_iter_62_20250805_095154.html
2025-08-05 09:51:54,897 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite1_28\dashboard_composite1_28_iter_62_20250805_095154.html
2025-08-05 09:51:54,897 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 62
2025-08-05 09:51:54,897 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 09:51:54,897 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1983秒
2025-08-05 09:51:54,897 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1875, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -1494.7875000000001, 'local_optima_density': 0.1875, 'gradient_variance': 19484529.59734375, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0762, 'fitness_entropy': 0.7451559367333088, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -1494.788)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.076)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358714.7209294, 'performance_metrics': {}}}
2025-08-05 09:51:54,897 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:54,897 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:54,897 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:54,897 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:54,898 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:51:54,898 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 09:51:54,898 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:51:54,898 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:54,898 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:54,898 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:51:54,899 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:54,899 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:54,899 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:54,899 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:51:54,899 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:51:54,900 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:54,901 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 28
2025-08-05 09:51:54,901 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,902 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,902 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,902 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,902 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3769.0, 路径长度: 28, 收集中间解: 3
2025-08-05 09:51:54,902 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 25, 22, 12, 16, 15, 14, 17, 13, 10, 11, 9, 18, 21, 26, 24, 19, 27, 23, 20, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3769.0, 'intermediate_solutions': [{'tour': [0, 18, 1, 13, 17, 14, 15, 16, 12, 10, 11, 9, 21, 26, 24, 25, 23, 27, 19, 20, 22, 8, 7, 5, 3, 4, 6, 2], 'cur_cost': 5396.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 1, 13, 17, 14, 15, 16, 12, 9, 11, 10, 21, 26, 20, 19, 27, 23, 25, 24, 22, 8, 7, 5, 3, 4, 6, 2], 'cur_cost': 5355.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 1, 13, 17, 14, 15, 16, 12, 9, 11, 10, 21, 26, 24, 25, 3, 23, 27, 19, 20, 22, 8, 7, 5, 4, 6, 2], 'cur_cost': 7530.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:54,903 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 3769.00)
2025-08-05 09:51:54,903 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:51:54,904 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:51:54,904 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:54,905 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 28
2025-08-05 09:51:54,905 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,906 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,906 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,906 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,906 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13269.0, 路径长度: 28, 收集中间解: 3
2025-08-05 09:51:54,907 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [8, 19, 20, 16, 26, 15, 13, 17, 25, 27, 11, 10, 14, 21, 1, 24, 9, 22, 23, 3, 2, 6, 7, 12, 4, 18, 0, 5], 'cur_cost': 13269.0, 'intermediate_solutions': [{'tour': [0, 5, 19, 24, 25, 26, 22, 21, 20, 27, 23, 10, 14, 17, 16, 15, 9, 18, 12, 13, 11, 1, 7, 6, 8, 2, 3, 4], 'cur_cost': 3273.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 19, 24, 25, 26, 22, 21, 20, 27, 23, 10, 2, 1, 6, 7, 8, 11, 13, 12, 18, 9, 15, 16, 17, 14, 3, 4], 'cur_cost': 5317.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 9, 19, 24, 25, 26, 22, 21, 20, 27, 23, 10, 14, 17, 16, 15, 18, 12, 13, 11, 8, 7, 6, 1, 2, 3, 4], 'cur_cost': 3767.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:54,907 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 13269.00)
2025-08-05 09:51:54,907 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:51:54,907 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:51:54,907 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:54,908 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 28
2025-08-05 09:51:54,908 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,908 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,908 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,908 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,909 - ExplorationExpert - INFO - 探索路径生成完成，成本: 5250.0, 路径长度: 28, 收集中间解: 3
2025-08-05 09:51:54,909 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 9, 6, 20, 24, 25, 26, 22, 21, 19, 27, 23, 10, 14, 17, 16, 15, 18, 12, 13, 11, 8, 7, 5, 3, 4, 2, 1], 'cur_cost': 5250.0, 'intermediate_solutions': [{'tour': [0, 11, 16, 24, 25, 26, 22, 21, 20, 19, 27, 23, 10, 14, 17, 1, 12, 18, 15, 9, 8, 7, 5, 3, 4, 6, 13, 2], 'cur_cost': 8008.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 16, 24, 2, 1, 6, 4, 3, 5, 7, 8, 9, 15, 18, 12, 13, 17, 14, 10, 23, 27, 19, 20, 21, 22, 26, 25], 'cur_cost': 5786.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 16, 25, 24, 26, 22, 21, 20, 19, 27, 23, 10, 14, 17, 13, 12, 18, 15, 9, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3734.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:54,909 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 5250.00)
2025-08-05 09:51:54,909 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:51:54,909 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:51:54,909 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:54,910 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 28
2025-08-05 09:51:54,911 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,911 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,911 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,911 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,911 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9745.0, 路径长度: 28, 收集中间解: 3
2025-08-05 09:51:54,912 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [26, 13, 14, 12, 24, 20, 22, 15, 18, 21, 9, 27, 10, 16, 11, 8, 0, 7, 4, 2, 6, 3, 19, 23, 17, 25, 1, 5], 'cur_cost': 9745.0, 'intermediate_solutions': [{'tour': [8, 5, 16, 22, 26, 25, 15, 4, 11, 18, 13, 7, 12, 20, 19, 27, 21, 23, 10, 3, 2, 6, 24, 17, 9, 14, 1, 0], 'cur_cost': 11783.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 5, 22, 16, 26, 25, 15, 4, 11, 18, 13, 7, 12, 20, 19, 27, 21, 23, 10, 3, 2, 6, 1, 14, 9, 17, 24, 0], 'cur_cost': 12288.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 5, 22, 16, 26, 25, 15, 4, 11, 18, 13, 7, 12, 20, 19, 27, 21, 23, 10, 3, 24, 2, 6, 17, 9, 14, 1, 0], 'cur_cost': 13938.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:54,912 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 9745.00)
2025-08-05 09:51:54,912 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:51:54,912 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:51:54,912 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:54,914 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 28
2025-08-05 09:51:54,914 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,914 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,915 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,915 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,915 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9663.0, 路径长度: 28, 收集中间解: 3
2025-08-05 09:51:54,915 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [26, 25, 17, 9, 24, 27, 23, 20, 12, 21, 16, 15, 19, 13, 0, 4, 3, 6, 5, 1, 11, 10, 14, 22, 18, 8, 7, 2], 'cur_cost': 9663.0, 'intermediate_solutions': [{'tour': [20, 19, 16, 23, 21, 17, 13, 9, 15, 22, 5, 1, 4, 2, 26, 3, 0, 25, 11, 6, 10, 24, 27, 8, 12, 18, 14, 7], 'cur_cost': 14364.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 19, 16, 23, 21, 17, 13, 9, 12, 8, 27, 24, 10, 6, 20, 25, 0, 3, 26, 2, 4, 1, 5, 22, 15, 18, 14, 7], 'cur_cost': 14392.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 19, 16, 23, 21, 13, 9, 15, 22, 5, 1, 4, 17, 2, 26, 3, 0, 25, 20, 6, 10, 24, 27, 8, 12, 18, 14, 7], 'cur_cost': 16453.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:54,915 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 9663.00)
2025-08-05 09:51:54,915 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 09:51:54,916 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:54,916 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:54,916 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 17922.0
2025-08-05 09:51:54,925 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 09:51:54,926 - ExploitationExpert - INFO - res_population_costs: [3055.0, 3055, 3055, 3061, 3062.0, 3062]
2025-08-05 09:51:54,926 - ExploitationExpert - INFO - res_populations: [array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 19, 24, 25, 27, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 19, 27, 25, 23, 22, 26, 24, 20,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 20, 24, 26, 22, 23,
       25, 27, 19,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64)]
2025-08-05 09:51:54,927 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:54,927 - ExploitationExpert - INFO - populations: [{'tour': [0, 25, 22, 12, 16, 15, 14, 17, 13, 10, 11, 9, 18, 21, 26, 24, 19, 27, 23, 20, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3769.0}, {'tour': [8, 19, 20, 16, 26, 15, 13, 17, 25, 27, 11, 10, 14, 21, 1, 24, 9, 22, 23, 3, 2, 6, 7, 12, 4, 18, 0, 5], 'cur_cost': 13269.0}, {'tour': [0, 9, 6, 20, 24, 25, 26, 22, 21, 19, 27, 23, 10, 14, 17, 16, 15, 18, 12, 13, 11, 8, 7, 5, 3, 4, 2, 1], 'cur_cost': 5250.0}, {'tour': [26, 13, 14, 12, 24, 20, 22, 15, 18, 21, 9, 27, 10, 16, 11, 8, 0, 7, 4, 2, 6, 3, 19, 23, 17, 25, 1, 5], 'cur_cost': 9745.0}, {'tour': [26, 25, 17, 9, 24, 27, 23, 20, 12, 21, 16, 15, 19, 13, 0, 4, 3, 6, 5, 1, 11, 10, 14, 22, 18, 8, 7, 2], 'cur_cost': 9663.0}, {'tour': array([11, 18,  0, 14, 15, 16,  1, 25,  7, 12,  4,  6,  9, 20,  2,  8, 24,
       21, 13,  5, 27, 17, 10, 26, 23, 22,  3, 19], dtype=int64), 'cur_cost': 17922.0}, {'tour': [17, 13, 20, 10, 24, 16, 11, 18, 21, 15, 14, 25, 22, 27, 26, 8, 0, 5, 7, 1, 3, 4, 9, 12, 23, 19, 6, 2], 'cur_cost': 9109.0}, {'tour': [1, 22, 8, 12, 14, 26, 16, 13, 17, 10, 21, 27, 23, 4, 15, 5, 11, 24, 25, 19, 6, 9, 0, 18, 7, 3, 20, 2], 'cur_cost': 17248.0}, {'tour': [0, 15, 26, 9, 18, 16, 17, 14, 11, 10, 13, 12, 21, 20, 24, 25, 23, 27, 19, 22, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 4331.0}, {'tour': [0, 11, 21, 10, 24, 26, 14, 9, 18, 15, 19, 27, 17, 12, 25, 16, 22, 23, 7, 8, 1, 5, 4, 6, 2, 3, 20, 13], 'cur_cost': 10040.0}]
2025-08-05 09:51:54,928 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:54,928 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 159, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 159, 'cache_hits': 0, 'similarity_calculations': 657, 'cache_hit_rate': 0.0, 'cache_size': 657}}
2025-08-05 09:51:54,929 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([11, 18,  0, 14, 15, 16,  1, 25,  7, 12,  4,  6,  9, 20,  2,  8, 24,
       21, 13,  5, 27, 17, 10, 26, 23, 22,  3, 19], dtype=int64), 'cur_cost': 17922.0, 'intermediate_solutions': [{'tour': array([15, 14, 11, 27, 23, 21, 17, 16, 25,  9, 13,  5,  3, 18,  1, 19, 12,
       22, 20,  7,  4, 26,  8,  2,  6, 10, 24,  0]), 'cur_cost': 14828.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([27, 15, 14, 11, 23, 21, 17, 16, 25,  9, 13,  5,  3, 18,  1, 19, 12,
       22, 20,  7,  4, 26,  8,  2,  6, 10, 24,  0]), 'cur_cost': 15400.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([23, 27, 15, 14, 11, 21, 17, 16, 25,  9, 13,  5,  3, 18,  1, 19, 12,
       22, 20,  7,  4, 26,  8,  2,  6, 10, 24,  0]), 'cur_cost': 15354.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([11, 27, 15, 14, 23, 21, 17, 16, 25,  9, 13,  5,  3, 18,  1, 19, 12,
       22, 20,  7,  4, 26,  8,  2,  6, 10, 24,  0]), 'cur_cost': 15916.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([11, 23, 27, 15, 14, 21, 17, 16, 25,  9, 13,  5,  3, 18,  1, 19, 12,
       22, 20,  7,  4, 26,  8,  2,  6, 10, 24,  0]), 'cur_cost': 15842.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:54,929 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 17922.00)
2025-08-05 09:51:54,929 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:51:54,929 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:51:54,929 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:54,931 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 28
2025-08-05 09:51:54,931 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,931 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,931 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,931 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,932 - ExplorationExpert - INFO - 探索路径生成完成，成本: 7576.0, 路径长度: 28, 收集中间解: 3
2025-08-05 09:51:54,932 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [2, 0, 4, 7, 6, 21, 19, 10, 17, 18, 9, 14, 16, 13, 11, 26, 24, 25, 15, 22, 23, 27, 8, 1, 3, 5, 20, 12], 'cur_cost': 7576.0, 'intermediate_solutions': [{'tour': [17, 13, 20, 10, 24, 16, 11, 18, 21, 15, 14, 25, 22, 27, 26, 4, 0, 5, 7, 1, 3, 8, 9, 12, 23, 19, 6, 2], 'cur_cost': 9158.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [17, 13, 20, 10, 24, 16, 11, 18, 21, 15, 14, 25, 22, 27, 26, 8, 0, 5, 7, 1, 3, 4, 9, 12, 19, 23, 6, 2], 'cur_cost': 9153.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [17, 13, 20, 10, 24, 16, 11, 18, 21, 15, 14, 25, 22, 27, 26, 8, 0, 5, 7, 1, 3, 4, 12, 23, 9, 19, 6, 2], 'cur_cost': 10233.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:54,932 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 7576.00)
2025-08-05 09:51:54,932 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 09:51:54,932 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:54,932 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:54,933 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 14915.0
2025-08-05 09:51:54,942 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:51:54,942 - ExploitationExpert - INFO - res_population_costs: [3055.0, 3055, 3055, 3061, 3062.0, 3062, 3055.0]
2025-08-05 09:51:54,942 - ExploitationExpert - INFO - res_populations: [array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 19, 24, 25, 27, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 19, 27, 25, 23, 22, 26, 24, 20,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 20, 24, 26, 22, 23,
       25, 27, 19,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64)]
2025-08-05 09:51:54,944 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:54,944 - ExploitationExpert - INFO - populations: [{'tour': [0, 25, 22, 12, 16, 15, 14, 17, 13, 10, 11, 9, 18, 21, 26, 24, 19, 27, 23, 20, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3769.0}, {'tour': [8, 19, 20, 16, 26, 15, 13, 17, 25, 27, 11, 10, 14, 21, 1, 24, 9, 22, 23, 3, 2, 6, 7, 12, 4, 18, 0, 5], 'cur_cost': 13269.0}, {'tour': [0, 9, 6, 20, 24, 25, 26, 22, 21, 19, 27, 23, 10, 14, 17, 16, 15, 18, 12, 13, 11, 8, 7, 5, 3, 4, 2, 1], 'cur_cost': 5250.0}, {'tour': [26, 13, 14, 12, 24, 20, 22, 15, 18, 21, 9, 27, 10, 16, 11, 8, 0, 7, 4, 2, 6, 3, 19, 23, 17, 25, 1, 5], 'cur_cost': 9745.0}, {'tour': [26, 25, 17, 9, 24, 27, 23, 20, 12, 21, 16, 15, 19, 13, 0, 4, 3, 6, 5, 1, 11, 10, 14, 22, 18, 8, 7, 2], 'cur_cost': 9663.0}, {'tour': array([11, 18,  0, 14, 15, 16,  1, 25,  7, 12,  4,  6,  9, 20,  2,  8, 24,
       21, 13,  5, 27, 17, 10, 26, 23, 22,  3, 19], dtype=int64), 'cur_cost': 17922.0}, {'tour': [2, 0, 4, 7, 6, 21, 19, 10, 17, 18, 9, 14, 16, 13, 11, 26, 24, 25, 15, 22, 23, 27, 8, 1, 3, 5, 20, 12], 'cur_cost': 7576.0}, {'tour': array([ 0,  9, 14,  6, 16, 11, 22, 20, 23, 24, 26, 15,  1,  7,  3, 18, 17,
        2,  4, 21,  8, 12, 27, 19, 25,  5, 13, 10], dtype=int64), 'cur_cost': 14915.0}, {'tour': [0, 15, 26, 9, 18, 16, 17, 14, 11, 10, 13, 12, 21, 20, 24, 25, 23, 27, 19, 22, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 4331.0}, {'tour': [0, 11, 21, 10, 24, 26, 14, 9, 18, 15, 19, 27, 17, 12, 25, 16, 22, 23, 7, 8, 1, 5, 4, 6, 2, 3, 20, 13], 'cur_cost': 10040.0}]
2025-08-05 09:51:54,945 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:54,945 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 160, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 160, 'cache_hits': 0, 'similarity_calculations': 660, 'cache_hit_rate': 0.0, 'cache_size': 660}}
2025-08-05 09:51:54,946 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([ 0,  9, 14,  6, 16, 11, 22, 20, 23, 24, 26, 15,  1,  7,  3, 18, 17,
        2,  4, 21,  8, 12, 27, 19, 25,  5, 13, 10], dtype=int64), 'cur_cost': 14915.0, 'intermediate_solutions': [{'tour': array([ 8, 22,  1, 12, 14, 26, 16, 13, 17, 10, 21, 27, 23,  4, 15,  5, 11,
       24, 25, 19,  6,  9,  0, 18,  7,  3, 20,  2]), 'cur_cost': 17294.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([12,  8, 22,  1, 14, 26, 16, 13, 17, 10, 21, 27, 23,  4, 15,  5, 11,
       24, 25, 19,  6,  9,  0, 18,  7,  3, 20,  2]), 'cur_cost': 19368.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14, 12,  8, 22,  1, 26, 16, 13, 17, 10, 21, 27, 23,  4, 15,  5, 11,
       24, 25, 19,  6,  9,  0, 18,  7,  3, 20,  2]), 'cur_cost': 18829.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 1, 12,  8, 22, 14, 26, 16, 13, 17, 10, 21, 27, 23,  4, 15,  5, 11,
       24, 25, 19,  6,  9,  0, 18,  7,  3, 20,  2]), 'cur_cost': 17769.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 1, 14, 12,  8, 22, 26, 16, 13, 17, 10, 21, 27, 23,  4, 15,  5, 11,
       24, 25, 19,  6,  9,  0, 18,  7,  3, 20,  2]), 'cur_cost': 16711.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:54,946 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 14915.00)
2025-08-05 09:51:54,946 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:51:54,946 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:51:54,946 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:54,947 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 28
2025-08-05 09:51:54,947 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,947 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,948 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,948 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,948 - ExplorationExpert - INFO - 探索路径生成完成，成本: 5738.0, 路径长度: 28, 收集中间解: 3
2025-08-05 09:51:54,948 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 14, 22, 6, 1, 2, 5, 3, 4, 7, 8, 20, 24, 25, 26, 21, 19, 27, 23, 10, 17, 16, 15, 9, 18, 12, 13, 11], 'cur_cost': 5738.0, 'intermediate_solutions': [{'tour': [0, 15, 26, 9, 18, 16, 17, 14, 11, 10, 13, 19, 21, 20, 24, 25, 23, 27, 12, 22, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 5435.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 26, 9, 18, 16, 17, 14, 11, 10, 13, 12, 21, 20, 24, 25, 23, 27, 19, 22, 8, 7, 5, 3, 1, 6, 4, 2], 'cur_cost': 4373.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 26, 9, 18, 16, 17, 14, 11, 10, 13, 12, 21, 24, 25, 23, 27, 19, 22, 8, 7, 5, 3, 4, 6, 1, 20, 2], 'cur_cost': 6379.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:54,948 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 5738.00)
2025-08-05 09:51:54,949 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:51:54,949 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:51:54,949 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:54,950 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 28
2025-08-05 09:51:54,950 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,950 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,950 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,950 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:54,950 - ExplorationExpert - INFO - 探索路径生成完成，成本: 5794.0, 路径长度: 28, 收集中间解: 3
2025-08-05 09:51:54,950 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 15, 20, 7, 5, 3, 4, 6, 1, 2, 8, 19, 24, 25, 26, 22, 21, 23, 27, 10, 14, 17, 16, 12, 18, 9, 11, 13], 'cur_cost': 5794.0, 'intermediate_solutions': [{'tour': [0, 11, 21, 10, 24, 2, 14, 9, 18, 15, 19, 27, 17, 12, 25, 16, 22, 23, 7, 8, 1, 5, 4, 6, 26, 3, 20, 13], 'cur_cost': 13753.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 21, 10, 24, 26, 14, 20, 3, 2, 6, 4, 5, 1, 8, 7, 23, 22, 16, 25, 12, 17, 27, 19, 15, 18, 9, 13], 'cur_cost': 10072.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 11, 10, 24, 26, 14, 9, 18, 15, 19, 27, 17, 12, 25, 16, 22, 23, 7, 8, 1, 5, 4, 6, 2, 3, 20, 13], 'cur_cost': 9591.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:54,951 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 5794.00)
2025-08-05 09:51:54,951 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:54,951 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:54,952 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 25, 22, 12, 16, 15, 14, 17, 13, 10, 11, 9, 18, 21, 26, 24, 19, 27, 23, 20, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3769.0, 'intermediate_solutions': [{'tour': [0, 18, 1, 13, 17, 14, 15, 16, 12, 10, 11, 9, 21, 26, 24, 25, 23, 27, 19, 20, 22, 8, 7, 5, 3, 4, 6, 2], 'cur_cost': 5396.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 1, 13, 17, 14, 15, 16, 12, 9, 11, 10, 21, 26, 20, 19, 27, 23, 25, 24, 22, 8, 7, 5, 3, 4, 6, 2], 'cur_cost': 5355.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 1, 13, 17, 14, 15, 16, 12, 9, 11, 10, 21, 26, 24, 25, 3, 23, 27, 19, 20, 22, 8, 7, 5, 4, 6, 2], 'cur_cost': 7530.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [8, 19, 20, 16, 26, 15, 13, 17, 25, 27, 11, 10, 14, 21, 1, 24, 9, 22, 23, 3, 2, 6, 7, 12, 4, 18, 0, 5], 'cur_cost': 13269.0, 'intermediate_solutions': [{'tour': [0, 5, 19, 24, 25, 26, 22, 21, 20, 27, 23, 10, 14, 17, 16, 15, 9, 18, 12, 13, 11, 1, 7, 6, 8, 2, 3, 4], 'cur_cost': 3273.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 19, 24, 25, 26, 22, 21, 20, 27, 23, 10, 2, 1, 6, 7, 8, 11, 13, 12, 18, 9, 15, 16, 17, 14, 3, 4], 'cur_cost': 5317.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 9, 19, 24, 25, 26, 22, 21, 20, 27, 23, 10, 14, 17, 16, 15, 18, 12, 13, 11, 8, 7, 6, 1, 2, 3, 4], 'cur_cost': 3767.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 6, 20, 24, 25, 26, 22, 21, 19, 27, 23, 10, 14, 17, 16, 15, 18, 12, 13, 11, 8, 7, 5, 3, 4, 2, 1], 'cur_cost': 5250.0, 'intermediate_solutions': [{'tour': [0, 11, 16, 24, 25, 26, 22, 21, 20, 19, 27, 23, 10, 14, 17, 1, 12, 18, 15, 9, 8, 7, 5, 3, 4, 6, 13, 2], 'cur_cost': 8008.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 16, 24, 2, 1, 6, 4, 3, 5, 7, 8, 9, 15, 18, 12, 13, 17, 14, 10, 23, 27, 19, 20, 21, 22, 26, 25], 'cur_cost': 5786.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 16, 25, 24, 26, 22, 21, 20, 19, 27, 23, 10, 14, 17, 13, 12, 18, 15, 9, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3734.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [26, 13, 14, 12, 24, 20, 22, 15, 18, 21, 9, 27, 10, 16, 11, 8, 0, 7, 4, 2, 6, 3, 19, 23, 17, 25, 1, 5], 'cur_cost': 9745.0, 'intermediate_solutions': [{'tour': [8, 5, 16, 22, 26, 25, 15, 4, 11, 18, 13, 7, 12, 20, 19, 27, 21, 23, 10, 3, 2, 6, 24, 17, 9, 14, 1, 0], 'cur_cost': 11783.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 5, 22, 16, 26, 25, 15, 4, 11, 18, 13, 7, 12, 20, 19, 27, 21, 23, 10, 3, 2, 6, 1, 14, 9, 17, 24, 0], 'cur_cost': 12288.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 5, 22, 16, 26, 25, 15, 4, 11, 18, 13, 7, 12, 20, 19, 27, 21, 23, 10, 3, 24, 2, 6, 17, 9, 14, 1, 0], 'cur_cost': 13938.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [26, 25, 17, 9, 24, 27, 23, 20, 12, 21, 16, 15, 19, 13, 0, 4, 3, 6, 5, 1, 11, 10, 14, 22, 18, 8, 7, 2], 'cur_cost': 9663.0, 'intermediate_solutions': [{'tour': [20, 19, 16, 23, 21, 17, 13, 9, 15, 22, 5, 1, 4, 2, 26, 3, 0, 25, 11, 6, 10, 24, 27, 8, 12, 18, 14, 7], 'cur_cost': 14364.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 19, 16, 23, 21, 17, 13, 9, 12, 8, 27, 24, 10, 6, 20, 25, 0, 3, 26, 2, 4, 1, 5, 22, 15, 18, 14, 7], 'cur_cost': 14392.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 19, 16, 23, 21, 13, 9, 15, 22, 5, 1, 4, 17, 2, 26, 3, 0, 25, 20, 6, 10, 24, 27, 8, 12, 18, 14, 7], 'cur_cost': 16453.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([11, 18,  0, 14, 15, 16,  1, 25,  7, 12,  4,  6,  9, 20,  2,  8, 24,
       21, 13,  5, 27, 17, 10, 26, 23, 22,  3, 19], dtype=int64), 'cur_cost': 17922.0, 'intermediate_solutions': [{'tour': array([15, 14, 11, 27, 23, 21, 17, 16, 25,  9, 13,  5,  3, 18,  1, 19, 12,
       22, 20,  7,  4, 26,  8,  2,  6, 10, 24,  0]), 'cur_cost': 14828.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([27, 15, 14, 11, 23, 21, 17, 16, 25,  9, 13,  5,  3, 18,  1, 19, 12,
       22, 20,  7,  4, 26,  8,  2,  6, 10, 24,  0]), 'cur_cost': 15400.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([23, 27, 15, 14, 11, 21, 17, 16, 25,  9, 13,  5,  3, 18,  1, 19, 12,
       22, 20,  7,  4, 26,  8,  2,  6, 10, 24,  0]), 'cur_cost': 15354.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([11, 27, 15, 14, 23, 21, 17, 16, 25,  9, 13,  5,  3, 18,  1, 19, 12,
       22, 20,  7,  4, 26,  8,  2,  6, 10, 24,  0]), 'cur_cost': 15916.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([11, 23, 27, 15, 14, 21, 17, 16, 25,  9, 13,  5,  3, 18,  1, 19, 12,
       22, 20,  7,  4, 26,  8,  2,  6, 10, 24,  0]), 'cur_cost': 15842.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [2, 0, 4, 7, 6, 21, 19, 10, 17, 18, 9, 14, 16, 13, 11, 26, 24, 25, 15, 22, 23, 27, 8, 1, 3, 5, 20, 12], 'cur_cost': 7576.0, 'intermediate_solutions': [{'tour': [17, 13, 20, 10, 24, 16, 11, 18, 21, 15, 14, 25, 22, 27, 26, 4, 0, 5, 7, 1, 3, 8, 9, 12, 23, 19, 6, 2], 'cur_cost': 9158.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [17, 13, 20, 10, 24, 16, 11, 18, 21, 15, 14, 25, 22, 27, 26, 8, 0, 5, 7, 1, 3, 4, 9, 12, 19, 23, 6, 2], 'cur_cost': 9153.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [17, 13, 20, 10, 24, 16, 11, 18, 21, 15, 14, 25, 22, 27, 26, 8, 0, 5, 7, 1, 3, 4, 12, 23, 9, 19, 6, 2], 'cur_cost': 10233.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 0,  9, 14,  6, 16, 11, 22, 20, 23, 24, 26, 15,  1,  7,  3, 18, 17,
        2,  4, 21,  8, 12, 27, 19, 25,  5, 13, 10], dtype=int64), 'cur_cost': 14915.0, 'intermediate_solutions': [{'tour': array([ 8, 22,  1, 12, 14, 26, 16, 13, 17, 10, 21, 27, 23,  4, 15,  5, 11,
       24, 25, 19,  6,  9,  0, 18,  7,  3, 20,  2]), 'cur_cost': 17294.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([12,  8, 22,  1, 14, 26, 16, 13, 17, 10, 21, 27, 23,  4, 15,  5, 11,
       24, 25, 19,  6,  9,  0, 18,  7,  3, 20,  2]), 'cur_cost': 19368.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14, 12,  8, 22,  1, 26, 16, 13, 17, 10, 21, 27, 23,  4, 15,  5, 11,
       24, 25, 19,  6,  9,  0, 18,  7,  3, 20,  2]), 'cur_cost': 18829.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 1, 12,  8, 22, 14, 26, 16, 13, 17, 10, 21, 27, 23,  4, 15,  5, 11,
       24, 25, 19,  6,  9,  0, 18,  7,  3, 20,  2]), 'cur_cost': 17769.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 1, 14, 12,  8, 22, 26, 16, 13, 17, 10, 21, 27, 23,  4, 15,  5, 11,
       24, 25, 19,  6,  9,  0, 18,  7,  3, 20,  2]), 'cur_cost': 16711.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 22, 6, 1, 2, 5, 3, 4, 7, 8, 20, 24, 25, 26, 21, 19, 27, 23, 10, 17, 16, 15, 9, 18, 12, 13, 11], 'cur_cost': 5738.0, 'intermediate_solutions': [{'tour': [0, 15, 26, 9, 18, 16, 17, 14, 11, 10, 13, 19, 21, 20, 24, 25, 23, 27, 12, 22, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 5435.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 26, 9, 18, 16, 17, 14, 11, 10, 13, 12, 21, 20, 24, 25, 23, 27, 19, 22, 8, 7, 5, 3, 1, 6, 4, 2], 'cur_cost': 4373.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 26, 9, 18, 16, 17, 14, 11, 10, 13, 12, 21, 24, 25, 23, 27, 19, 22, 8, 7, 5, 3, 4, 6, 1, 20, 2], 'cur_cost': 6379.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 20, 7, 5, 3, 4, 6, 1, 2, 8, 19, 24, 25, 26, 22, 21, 23, 27, 10, 14, 17, 16, 12, 18, 9, 11, 13], 'cur_cost': 5794.0, 'intermediate_solutions': [{'tour': [0, 11, 21, 10, 24, 2, 14, 9, 18, 15, 19, 27, 17, 12, 25, 16, 22, 23, 7, 8, 1, 5, 4, 6, 26, 3, 20, 13], 'cur_cost': 13753.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 21, 10, 24, 26, 14, 20, 3, 2, 6, 4, 5, 1, 8, 7, 23, 22, 16, 25, 12, 17, 27, 19, 15, 18, 9, 13], 'cur_cost': 10072.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 11, 10, 24, 26, 14, 9, 18, 15, 19, 27, 17, 12, 25, 16, 22, 23, 7, 8, 1, 5, 4, 6, 2, 3, 20, 13], 'cur_cost': 9591.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:51:54,952 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:54,952 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:54,954 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=3769.000, 多样性=0.948
2025-08-05 09:51:54,954 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 09:51:54,955 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 09:51:54,955 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:54,955 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.06812674866454806, 'best_improvement': -0.17487531172069826}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.012722646310432646}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.04947831535955751, 'recent_improvements': [0.06760276058986807, -0.09707947226958329, 0.1665593913089831], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 7, 'new_count': 7, 'count_change': 0, 'old_best_cost': 3055.0, 'new_best_cost': 3055.0, 'quality_improvement': 0.0, 'old_diversity': 0.7465986394557823, 'new_diversity': 0.7465986394557823, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 09:51:54,955 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 09:51:54,956 - __main__ - INFO - composite1_28 开始进化第 3 代
2025-08-05 09:51:54,956 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 09:51:54,956 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:54,956 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=3769.000, 多样性=0.948
2025-08-05 09:51:54,957 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:54,958 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.948
2025-08-05 09:51:54,958 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:54,960 - EliteExpert - INFO - 精英解分析完成: 精英解数量=7, 多样性=0.747
2025-08-05 09:51:54,962 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 09:51:54,962 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:54,962 - LandscapeExpert - INFO - 添加精英解数据: 7个精英解
2025-08-05 09:51:54,962 - LandscapeExpert - INFO - 数据提取成功: 17个路径, 17个适应度值
2025-08-05 09:51:54,987 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.235, 适应度梯度: -1734.035, 聚类评分: 0.000, 覆盖率: 0.077, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:51:54,987 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 09:51:54,987 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:51:54,987 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite1_28
2025-08-05 09:51:54,992 - visualization.landscape_visualizer - INFO - 插值约束: 131 个点被约束到最小值 3055.00
2025-08-05 09:51:55,079 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite1_28\landscape_composite1_28_iter_63_20250805_095155.html
2025-08-05 09:51:55,125 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite1_28\dashboard_composite1_28_iter_63_20250805_095155.html
2025-08-05 09:51:55,126 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 63
2025-08-05 09:51:55,126 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 09:51:55,126 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1640秒
2025-08-05 09:51:55,126 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.23529411764705882, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -1734.035294117647, 'local_optima_density': 0.23529411764705882, 'gradient_variance': 21031614.085813142, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0773, 'fitness_entropy': 0.8115652900921527, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -1734.035)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.077)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358714.987171, 'performance_metrics': {}}}
2025-08-05 09:51:55,126 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:55,126 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:55,126 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:55,126 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:55,126 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:51:55,126 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 09:51:55,127 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:51:55,127 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:55,127 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:55,127 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:51:55,127 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:55,127 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:55,127 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:55,127 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:51:55,127 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:51:55,128 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:55,128 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 28
2025-08-05 09:51:55,128 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,128 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,129 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,129 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,129 - ExplorationExpert - INFO - 探索路径生成完成，成本: 5822.0, 路径长度: 28, 收集中间解: 3
2025-08-05 09:51:55,129 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 15, 19, 7, 5, 3, 4, 6, 1, 2, 8, 20, 24, 25, 26, 22, 21, 23, 27, 10, 14, 17, 16, 12, 18, 9, 11, 13], 'cur_cost': 5822.0, 'intermediate_solutions': [{'tour': [0, 25, 22, 12, 16, 15, 14, 17, 13, 10, 11, 6, 18, 21, 26, 24, 19, 27, 23, 20, 8, 7, 5, 3, 4, 9, 1, 2], 'cur_cost': 7874.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [14, 15, 16, 12, 22, 25, 0, 17, 13, 10, 11, 9, 18, 21, 26, 24, 19, 27, 23, 20, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 5828.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 25, 22, 12, 16, 15, 14, 17, 13, 10, 11, 9, 18, 21, 26, 24, 19, 27, 23, 20, 8, 7, 3, 4, 6, 1, 2, 5], 'cur_cost': 3771.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:55,129 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 5822.00)
2025-08-05 09:51:55,129 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 09:51:55,129 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:55,129 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:55,129 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 16584.0
2025-08-05 09:51:55,138 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:51:55,138 - ExploitationExpert - INFO - res_population_costs: [3055.0, 3055, 3055, 3055.0, 3061, 3062.0, 3062]
2025-08-05 09:51:55,138 - ExploitationExpert - INFO - res_populations: [array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 19, 24, 25, 27, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 19, 27, 25, 23, 22, 26, 24, 20,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 20, 24, 26, 22, 23,
       25, 27, 19,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64)]
2025-08-05 09:51:55,140 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:55,140 - ExploitationExpert - INFO - populations: [{'tour': [0, 15, 19, 7, 5, 3, 4, 6, 1, 2, 8, 20, 24, 25, 26, 22, 21, 23, 27, 10, 14, 17, 16, 12, 18, 9, 11, 13], 'cur_cost': 5822.0}, {'tour': array([13, 16, 20, 14, 10, 24,  2, 17, 19,  9, 22,  1,  4,  8, 21, 15, 25,
        7, 12, 27, 26, 11, 18,  0, 23,  5,  6,  3], dtype=int64), 'cur_cost': 16584.0}, {'tour': [0, 9, 6, 20, 24, 25, 26, 22, 21, 19, 27, 23, 10, 14, 17, 16, 15, 18, 12, 13, 11, 8, 7, 5, 3, 4, 2, 1], 'cur_cost': 5250.0}, {'tour': [26, 13, 14, 12, 24, 20, 22, 15, 18, 21, 9, 27, 10, 16, 11, 8, 0, 7, 4, 2, 6, 3, 19, 23, 17, 25, 1, 5], 'cur_cost': 9745.0}, {'tour': [26, 25, 17, 9, 24, 27, 23, 20, 12, 21, 16, 15, 19, 13, 0, 4, 3, 6, 5, 1, 11, 10, 14, 22, 18, 8, 7, 2], 'cur_cost': 9663.0}, {'tour': [11, 18, 0, 14, 15, 16, 1, 25, 7, 12, 4, 6, 9, 20, 2, 8, 24, 21, 13, 5, 27, 17, 10, 26, 23, 22, 3, 19], 'cur_cost': 17922.0}, {'tour': [2, 0, 4, 7, 6, 21, 19, 10, 17, 18, 9, 14, 16, 13, 11, 26, 24, 25, 15, 22, 23, 27, 8, 1, 3, 5, 20, 12], 'cur_cost': 7576.0}, {'tour': [0, 9, 14, 6, 16, 11, 22, 20, 23, 24, 26, 15, 1, 7, 3, 18, 17, 2, 4, 21, 8, 12, 27, 19, 25, 5, 13, 10], 'cur_cost': 14915.0}, {'tour': [0, 14, 22, 6, 1, 2, 5, 3, 4, 7, 8, 20, 24, 25, 26, 21, 19, 27, 23, 10, 17, 16, 15, 9, 18, 12, 13, 11], 'cur_cost': 5738.0}, {'tour': [0, 15, 20, 7, 5, 3, 4, 6, 1, 2, 8, 19, 24, 25, 26, 22, 21, 23, 27, 10, 14, 17, 16, 12, 18, 9, 11, 13], 'cur_cost': 5794.0}]
2025-08-05 09:51:55,141 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:55,141 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 161, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 161, 'cache_hits': 0, 'similarity_calculations': 664, 'cache_hit_rate': 0.0, 'cache_size': 664}}
2025-08-05 09:51:55,141 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([13, 16, 20, 14, 10, 24,  2, 17, 19,  9, 22,  1,  4,  8, 21, 15, 25,
        7, 12, 27, 26, 11, 18,  0, 23,  5,  6,  3], dtype=int64), 'cur_cost': 16584.0, 'intermediate_solutions': [{'tour': array([20, 19,  8, 16, 26, 15, 13, 17, 25, 27, 11, 10, 14, 21,  1, 24,  9,
       22, 23,  3,  2,  6,  7, 12,  4, 18,  0,  5]), 'cur_cost': 14787.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([16, 20, 19,  8, 26, 15, 13, 17, 25, 27, 11, 10, 14, 21,  1, 24,  9,
       22, 23,  3,  2,  6,  7, 12,  4, 18,  0,  5]), 'cur_cost': 14799.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([26, 16, 20, 19,  8, 15, 13, 17, 25, 27, 11, 10, 14, 21,  1, 24,  9,
       22, 23,  3,  2,  6,  7, 12,  4, 18,  0,  5]), 'cur_cost': 14799.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 8, 16, 20, 19, 26, 15, 13, 17, 25, 27, 11, 10, 14, 21,  1, 24,  9,
       22, 23,  3,  2,  6,  7, 12,  4, 18,  0,  5]), 'cur_cost': 12786.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 8, 26, 16, 20, 19, 15, 13, 17, 25, 27, 11, 10, 14, 21,  1, 24,  9,
       22, 23,  3,  2,  6,  7, 12,  4, 18,  0,  5]), 'cur_cost': 13314.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:55,141 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 16584.00)
2025-08-05 09:51:55,142 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:51:55,142 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:51:55,142 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:55,143 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 28
2025-08-05 09:51:55,143 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,143 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,144 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,144 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,144 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3819.0, 路径长度: 28, 收集中间解: 3
2025-08-05 09:51:55,144 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 8, 17, 25, 24, 26, 22, 21, 20, 19, 27, 23, 10, 14, 15, 16, 12, 18, 9, 11, 13, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3819.0, 'intermediate_solutions': [{'tour': [0, 9, 6, 20, 24, 25, 26, 22, 21, 19, 27, 23, 10, 14, 17, 16, 15, 18, 7, 13, 11, 8, 12, 5, 3, 4, 2, 1], 'cur_cost': 9458.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 4, 3, 5, 7, 8, 11, 13, 12, 18, 15, 16, 17, 14, 10, 23, 27, 19, 21, 22, 26, 25, 24, 20, 6, 9, 0, 1], 'cur_cost': 5250.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 6, 20, 25, 26, 22, 21, 19, 27, 23, 10, 14, 24, 17, 16, 15, 18, 12, 13, 11, 8, 7, 5, 3, 4, 2, 1], 'cur_cost': 6333.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:55,144 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 3819.00)
2025-08-05 09:51:55,145 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:51:55,145 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:51:55,145 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:55,146 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 28
2025-08-05 09:51:55,146 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,146 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,146 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,146 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,146 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3826.0, 路径长度: 28, 收集中间解: 3
2025-08-05 09:51:55,146 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 25, 12, 16, 15, 14, 17, 13, 10, 11, 9, 18, 21, 26, 24, 19, 27, 23, 22, 20, 8, 7, 5, 3, 4, 6, 2], 'cur_cost': 3826.0, 'intermediate_solutions': [{'tour': [26, 13, 14, 12, 24, 20, 22, 15, 18, 21, 9, 6, 10, 16, 11, 8, 0, 7, 4, 2, 27, 3, 19, 23, 17, 25, 1, 5], 'cur_cost': 12827.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [26, 13, 14, 12, 24, 20, 22, 15, 18, 21, 9, 27, 10, 16, 11, 8, 0, 7, 4, 23, 19, 3, 6, 2, 17, 25, 1, 5], 'cur_cost': 11325.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [26, 13, 14, 12, 24, 20, 22, 15, 18, 21, 9, 27, 10, 11, 8, 0, 16, 7, 4, 2, 6, 3, 19, 23, 17, 25, 1, 5], 'cur_cost': 11791.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:55,147 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 3826.00)
2025-08-05 09:51:55,147 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:51:55,147 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:51:55,147 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:55,148 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 28
2025-08-05 09:51:55,148 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,148 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,148 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,148 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,148 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3246.0, 路径长度: 28, 收集中间解: 3
2025-08-05 09:51:55,149 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 1, 7, 26, 24, 25, 23, 27, 19, 20, 21, 22, 10, 14, 17, 16, 15, 9, 18, 12, 13, 11, 8, 6, 5, 3, 4, 2], 'cur_cost': 3246.0, 'intermediate_solutions': [{'tour': [26, 15, 17, 9, 24, 27, 23, 20, 12, 21, 16, 25, 19, 13, 0, 4, 3, 6, 5, 1, 11, 10, 14, 22, 18, 8, 7, 2], 'cur_cost': 9681.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [26, 25, 17, 9, 24, 27, 23, 20, 12, 21, 16, 15, 19, 2, 7, 8, 18, 22, 14, 10, 11, 1, 5, 6, 3, 4, 0, 13], 'cur_cost': 9616.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [26, 25, 9, 24, 27, 23, 20, 12, 21, 16, 15, 19, 13, 0, 4, 3, 6, 5, 17, 1, 11, 10, 14, 22, 18, 8, 7, 2], 'cur_cost': 11767.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:55,149 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 3246.00)
2025-08-05 09:51:55,149 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 09:51:55,149 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:55,149 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:55,149 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 14937.0
2025-08-05 09:51:55,160 - ExploitationExpert - INFO - res_population_num: 12
2025-08-05 09:51:55,160 - ExploitationExpert - INFO - res_population_costs: [3055.0, 3055, 3055, 3055.0, 3061, 3062.0, 3062, 3055.0, 3055, 3055, 3055, 3055]
2025-08-05 09:51:55,160 - ExploitationExpert - INFO - res_populations: [array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 19, 24, 25, 27, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 19, 27, 25, 23, 22, 26, 24, 20,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 20, 24, 26, 22, 23,
       25, 27, 19,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64)]
2025-08-05 09:51:55,163 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:55,163 - ExploitationExpert - INFO - populations: [{'tour': [0, 15, 19, 7, 5, 3, 4, 6, 1, 2, 8, 20, 24, 25, 26, 22, 21, 23, 27, 10, 14, 17, 16, 12, 18, 9, 11, 13], 'cur_cost': 5822.0}, {'tour': array([13, 16, 20, 14, 10, 24,  2, 17, 19,  9, 22,  1,  4,  8, 21, 15, 25,
        7, 12, 27, 26, 11, 18,  0, 23,  5,  6,  3], dtype=int64), 'cur_cost': 16584.0}, {'tour': [0, 8, 17, 25, 24, 26, 22, 21, 20, 19, 27, 23, 10, 14, 15, 16, 12, 18, 9, 11, 13, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3819.0}, {'tour': [0, 1, 25, 12, 16, 15, 14, 17, 13, 10, 11, 9, 18, 21, 26, 24, 19, 27, 23, 22, 20, 8, 7, 5, 3, 4, 6, 2], 'cur_cost': 3826.0}, {'tour': [0, 1, 7, 26, 24, 25, 23, 27, 19, 20, 21, 22, 10, 14, 17, 16, 15, 9, 18, 12, 13, 11, 8, 6, 5, 3, 4, 2], 'cur_cost': 3246.0}, {'tour': array([ 1, 26, 16,  5,  9, 24, 19, 22, 13, 15,  8,  2, 14, 21,  3,  0,  7,
       23, 27, 17,  4, 11, 25, 20, 18, 10, 12,  6], dtype=int64), 'cur_cost': 14937.0}, {'tour': [2, 0, 4, 7, 6, 21, 19, 10, 17, 18, 9, 14, 16, 13, 11, 26, 24, 25, 15, 22, 23, 27, 8, 1, 3, 5, 20, 12], 'cur_cost': 7576.0}, {'tour': [0, 9, 14, 6, 16, 11, 22, 20, 23, 24, 26, 15, 1, 7, 3, 18, 17, 2, 4, 21, 8, 12, 27, 19, 25, 5, 13, 10], 'cur_cost': 14915.0}, {'tour': [0, 14, 22, 6, 1, 2, 5, 3, 4, 7, 8, 20, 24, 25, 26, 21, 19, 27, 23, 10, 17, 16, 15, 9, 18, 12, 13, 11], 'cur_cost': 5738.0}, {'tour': [0, 15, 20, 7, 5, 3, 4, 6, 1, 2, 8, 19, 24, 25, 26, 22, 21, 23, 27, 10, 14, 17, 16, 12, 18, 9, 11, 13], 'cur_cost': 5794.0}]
2025-08-05 09:51:55,164 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:51:55,164 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 162, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 162, 'cache_hits': 0, 'similarity_calculations': 669, 'cache_hit_rate': 0.0, 'cache_size': 669}}
2025-08-05 09:51:55,165 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([ 1, 26, 16,  5,  9, 24, 19, 22, 13, 15,  8,  2, 14, 21,  3,  0,  7,
       23, 27, 17,  4, 11, 25, 20, 18, 10, 12,  6], dtype=int64), 'cur_cost': 14937.0, 'intermediate_solutions': [{'tour': array([ 0, 18, 11, 14, 15, 16,  1, 25,  7, 12,  4,  6,  9, 20,  2,  8, 24,
       21, 13,  5, 27, 17, 10, 26, 23, 22,  3, 19]), 'cur_cost': 17380.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([14,  0, 18, 11, 15, 16,  1, 25,  7, 12,  4,  6,  9, 20,  2,  8, 24,
       21, 13,  5, 27, 17, 10, 26, 23, 22,  3, 19]), 'cur_cost': 17939.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([15, 14,  0, 18, 11, 16,  1, 25,  7, 12,  4,  6,  9, 20,  2,  8, 24,
       21, 13,  5, 27, 17, 10, 26, 23, 22,  3, 19]), 'cur_cost': 17967.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([11, 14,  0, 18, 15, 16,  1, 25,  7, 12,  4,  6,  9, 20,  2,  8, 24,
       21, 13,  5, 27, 17, 10, 26, 23, 22,  3, 19]), 'cur_cost': 17897.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([11, 15, 14,  0, 18, 16,  1, 25,  7, 12,  4,  6,  9, 20,  2,  8, 24,
       21, 13,  5, 27, 17, 10, 26, 23, 22,  3, 19]), 'cur_cost': 17911.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:55,165 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 14937.00)
2025-08-05 09:51:55,165 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:51:55,166 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:51:55,166 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:55,167 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 28
2025-08-05 09:51:55,168 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,168 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,168 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,168 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,168 - ExplorationExpert - INFO - 探索路径生成完成，成本: 8084.0, 路径长度: 28, 收集中间解: 3
2025-08-05 09:51:55,169 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [9, 11, 26, 13, 15, 16, 20, 24, 27, 23, 18, 19, 17, 10, 22, 14, 25, 0, 8, 5, 2, 1, 3, 4, 7, 6, 21, 12], 'cur_cost': 8084.0, 'intermediate_solutions': [{'tour': [2, 0, 4, 7, 6, 21, 19, 10, 17, 18, 9, 14, 16, 13, 11, 26, 1, 25, 15, 22, 23, 27, 8, 24, 3, 5, 20, 12], 'cur_cost': 11717.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 0, 4, 7, 6, 21, 19, 10, 17, 18, 9, 14, 16, 13, 11, 26, 24, 25, 15, 22, 23, 27, 8, 1, 20, 5, 3, 12], 'cur_cost': 9117.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [17, 2, 0, 4, 7, 6, 21, 19, 10, 18, 9, 14, 16, 13, 11, 26, 24, 25, 15, 22, 23, 27, 8, 1, 3, 5, 20, 12], 'cur_cost': 7579.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:55,169 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 8084.00)
2025-08-05 09:51:55,169 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 09:51:55,169 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:55,169 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:55,169 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 19066.0
2025-08-05 09:51:55,180 - ExploitationExpert - INFO - res_population_num: 17
2025-08-05 09:51:55,181 - ExploitationExpert - INFO - res_population_costs: [3055.0, 3055, 3055, 3055.0, 3061, 3062.0, 3062, 3055.0, 3055, 3055, 3055, 3055, 3055.0, 3055, 3055, 3055, 3055]
2025-08-05 09:51:55,181 - ExploitationExpert - INFO - res_populations: [array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 19, 24, 25, 27, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 19, 27, 25, 23, 22, 26, 24, 20,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 20, 24, 26, 22, 23,
       25, 27, 19,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64)]
2025-08-05 09:51:55,185 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:55,185 - ExploitationExpert - INFO - populations: [{'tour': [0, 15, 19, 7, 5, 3, 4, 6, 1, 2, 8, 20, 24, 25, 26, 22, 21, 23, 27, 10, 14, 17, 16, 12, 18, 9, 11, 13], 'cur_cost': 5822.0}, {'tour': array([13, 16, 20, 14, 10, 24,  2, 17, 19,  9, 22,  1,  4,  8, 21, 15, 25,
        7, 12, 27, 26, 11, 18,  0, 23,  5,  6,  3], dtype=int64), 'cur_cost': 16584.0}, {'tour': [0, 8, 17, 25, 24, 26, 22, 21, 20, 19, 27, 23, 10, 14, 15, 16, 12, 18, 9, 11, 13, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3819.0}, {'tour': [0, 1, 25, 12, 16, 15, 14, 17, 13, 10, 11, 9, 18, 21, 26, 24, 19, 27, 23, 22, 20, 8, 7, 5, 3, 4, 6, 2], 'cur_cost': 3826.0}, {'tour': [0, 1, 7, 26, 24, 25, 23, 27, 19, 20, 21, 22, 10, 14, 17, 16, 15, 9, 18, 12, 13, 11, 8, 6, 5, 3, 4, 2], 'cur_cost': 3246.0}, {'tour': array([ 1, 26, 16,  5,  9, 24, 19, 22, 13, 15,  8,  2, 14, 21,  3,  0,  7,
       23, 27, 17,  4, 11, 25, 20, 18, 10, 12,  6], dtype=int64), 'cur_cost': 14937.0}, {'tour': [9, 11, 26, 13, 15, 16, 20, 24, 27, 23, 18, 19, 17, 10, 22, 14, 25, 0, 8, 5, 2, 1, 3, 4, 7, 6, 21, 12], 'cur_cost': 8084.0}, {'tour': array([15,  0, 18, 24,  6, 13,  3,  8, 23, 27, 11,  9,  7,  2, 26,  5, 17,
       20, 22,  4, 19, 10, 16, 12, 25, 14,  1, 21], dtype=int64), 'cur_cost': 19066.0}, {'tour': [0, 14, 22, 6, 1, 2, 5, 3, 4, 7, 8, 20, 24, 25, 26, 21, 19, 27, 23, 10, 17, 16, 15, 9, 18, 12, 13, 11], 'cur_cost': 5738.0}, {'tour': [0, 15, 20, 7, 5, 3, 4, 6, 1, 2, 8, 19, 24, 25, 26, 22, 21, 23, 27, 10, 14, 17, 16, 12, 18, 9, 11, 13], 'cur_cost': 5794.0}]
2025-08-05 09:51:55,186 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:51:55,186 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 163, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 163, 'cache_hits': 0, 'similarity_calculations': 675, 'cache_hit_rate': 0.0, 'cache_size': 675}}
2025-08-05 09:51:55,187 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([15,  0, 18, 24,  6, 13,  3,  8, 23, 27, 11,  9,  7,  2, 26,  5, 17,
       20, 22,  4, 19, 10, 16, 12, 25, 14,  1, 21], dtype=int64), 'cur_cost': 19066.0, 'intermediate_solutions': [{'tour': array([14,  9,  0,  6, 16, 11, 22, 20, 23, 24, 26, 15,  1,  7,  3, 18, 17,
        2,  4, 21,  8, 12, 27, 19, 25,  5, 13, 10]), 'cur_cost': 12879.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 6, 14,  9,  0, 16, 11, 22, 20, 23, 24, 26, 15,  1,  7,  3, 18, 17,
        2,  4, 21,  8, 12, 27, 19, 25,  5, 13, 10]), 'cur_cost': 14914.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([16,  6, 14,  9,  0, 11, 22, 20, 23, 24, 26, 15,  1,  7,  3, 18, 17,
        2,  4, 21,  8, 12, 27, 19, 25,  5, 13, 10]), 'cur_cost': 14896.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 0,  6, 14,  9, 16, 11, 22, 20, 23, 24, 26, 15,  1,  7,  3, 18, 17,
        2,  4, 21,  8, 12, 27, 19, 25,  5, 13, 10]), 'cur_cost': 12882.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 0, 16,  6, 14,  9, 11, 22, 20, 23, 24, 26, 15,  1,  7,  3, 18, 17,
        2,  4, 21,  8, 12, 27, 19, 25,  5, 13, 10]), 'cur_cost': 14933.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:55,187 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 19066.00)
2025-08-05 09:51:55,187 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:51:55,187 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:51:55,187 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:55,189 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 28
2025-08-05 09:51:55,189 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,189 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,190 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,190 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,190 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9748.0, 路径长度: 28, 收集中间解: 3
2025-08-05 09:51:55,190 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [2, 5, 20, 22, 25, 10, 14, 18, 26, 24, 11, 21, 16, 19, 17, 12, 23, 15, 13, 9, 8, 6, 0, 4, 7, 1, 3, 27], 'cur_cost': 9748.0, 'intermediate_solutions': [{'tour': [0, 14, 22, 6, 15, 2, 5, 3, 4, 7, 8, 20, 24, 25, 26, 21, 19, 27, 23, 10, 17, 16, 1, 9, 18, 12, 13, 11], 'cur_cost': 9958.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 22, 6, 1, 2, 5, 3, 4, 7, 8, 20, 24, 25, 13, 12, 18, 9, 15, 16, 17, 10, 23, 27, 19, 21, 26, 11], 'cur_cost': 6761.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 22, 6, 2, 5, 3, 4, 7, 8, 20, 24, 25, 26, 21, 19, 27, 23, 10, 1, 17, 16, 15, 9, 18, 12, 13, 11], 'cur_cost': 7842.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:55,190 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 9748.00)
2025-08-05 09:51:55,190 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:51:55,190 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:51:55,190 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:55,191 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 28
2025-08-05 09:51:55,191 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,191 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,192 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,192 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,192 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15464.0, 路径长度: 28, 收集中间解: 3
2025-08-05 09:51:55,192 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [17, 25, 26, 21, 20, 15, 24, 12, 23, 6, 3, 11, 14, 8, 9, 0, 7, 5, 13, 2, 19, 10, 18, 22, 27, 16, 1, 4], 'cur_cost': 15464.0, 'intermediate_solutions': [{'tour': [0, 15, 20, 7, 5, 12, 4, 6, 1, 2, 8, 19, 24, 25, 26, 22, 21, 23, 27, 10, 14, 17, 16, 3, 18, 9, 11, 13], 'cur_cost': 10067.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 20, 7, 5, 3, 4, 6, 1, 2, 8, 19, 24, 25, 26, 22, 21, 23, 27, 10, 14, 17, 16, 12, 18, 9, 13, 11], 'cur_cost': 5776.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 24, 15, 20, 7, 5, 3, 4, 6, 1, 2, 8, 19, 25, 26, 22, 21, 23, 27, 10, 14, 17, 16, 12, 18, 9, 11, 13], 'cur_cost': 6355.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:55,192 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 15464.00)
2025-08-05 09:51:55,192 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:55,192 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:55,195 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 19, 7, 5, 3, 4, 6, 1, 2, 8, 20, 24, 25, 26, 22, 21, 23, 27, 10, 14, 17, 16, 12, 18, 9, 11, 13], 'cur_cost': 5822.0, 'intermediate_solutions': [{'tour': [0, 25, 22, 12, 16, 15, 14, 17, 13, 10, 11, 6, 18, 21, 26, 24, 19, 27, 23, 20, 8, 7, 5, 3, 4, 9, 1, 2], 'cur_cost': 7874.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [14, 15, 16, 12, 22, 25, 0, 17, 13, 10, 11, 9, 18, 21, 26, 24, 19, 27, 23, 20, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 5828.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 25, 22, 12, 16, 15, 14, 17, 13, 10, 11, 9, 18, 21, 26, 24, 19, 27, 23, 20, 8, 7, 3, 4, 6, 1, 2, 5], 'cur_cost': 3771.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([13, 16, 20, 14, 10, 24,  2, 17, 19,  9, 22,  1,  4,  8, 21, 15, 25,
        7, 12, 27, 26, 11, 18,  0, 23,  5,  6,  3], dtype=int64), 'cur_cost': 16584.0, 'intermediate_solutions': [{'tour': array([20, 19,  8, 16, 26, 15, 13, 17, 25, 27, 11, 10, 14, 21,  1, 24,  9,
       22, 23,  3,  2,  6,  7, 12,  4, 18,  0,  5]), 'cur_cost': 14787.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([16, 20, 19,  8, 26, 15, 13, 17, 25, 27, 11, 10, 14, 21,  1, 24,  9,
       22, 23,  3,  2,  6,  7, 12,  4, 18,  0,  5]), 'cur_cost': 14799.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([26, 16, 20, 19,  8, 15, 13, 17, 25, 27, 11, 10, 14, 21,  1, 24,  9,
       22, 23,  3,  2,  6,  7, 12,  4, 18,  0,  5]), 'cur_cost': 14799.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 8, 16, 20, 19, 26, 15, 13, 17, 25, 27, 11, 10, 14, 21,  1, 24,  9,
       22, 23,  3,  2,  6,  7, 12,  4, 18,  0,  5]), 'cur_cost': 12786.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 8, 26, 16, 20, 19, 15, 13, 17, 25, 27, 11, 10, 14, 21,  1, 24,  9,
       22, 23,  3,  2,  6,  7, 12,  4, 18,  0,  5]), 'cur_cost': 13314.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 17, 25, 24, 26, 22, 21, 20, 19, 27, 23, 10, 14, 15, 16, 12, 18, 9, 11, 13, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3819.0, 'intermediate_solutions': [{'tour': [0, 9, 6, 20, 24, 25, 26, 22, 21, 19, 27, 23, 10, 14, 17, 16, 15, 18, 7, 13, 11, 8, 12, 5, 3, 4, 2, 1], 'cur_cost': 9458.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 4, 3, 5, 7, 8, 11, 13, 12, 18, 15, 16, 17, 14, 10, 23, 27, 19, 21, 22, 26, 25, 24, 20, 6, 9, 0, 1], 'cur_cost': 5250.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 6, 20, 25, 26, 22, 21, 19, 27, 23, 10, 14, 24, 17, 16, 15, 18, 12, 13, 11, 8, 7, 5, 3, 4, 2, 1], 'cur_cost': 6333.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 25, 12, 16, 15, 14, 17, 13, 10, 11, 9, 18, 21, 26, 24, 19, 27, 23, 22, 20, 8, 7, 5, 3, 4, 6, 2], 'cur_cost': 3826.0, 'intermediate_solutions': [{'tour': [26, 13, 14, 12, 24, 20, 22, 15, 18, 21, 9, 6, 10, 16, 11, 8, 0, 7, 4, 2, 27, 3, 19, 23, 17, 25, 1, 5], 'cur_cost': 12827.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [26, 13, 14, 12, 24, 20, 22, 15, 18, 21, 9, 27, 10, 16, 11, 8, 0, 7, 4, 23, 19, 3, 6, 2, 17, 25, 1, 5], 'cur_cost': 11325.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [26, 13, 14, 12, 24, 20, 22, 15, 18, 21, 9, 27, 10, 11, 8, 0, 16, 7, 4, 2, 6, 3, 19, 23, 17, 25, 1, 5], 'cur_cost': 11791.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 7, 26, 24, 25, 23, 27, 19, 20, 21, 22, 10, 14, 17, 16, 15, 9, 18, 12, 13, 11, 8, 6, 5, 3, 4, 2], 'cur_cost': 3246.0, 'intermediate_solutions': [{'tour': [26, 15, 17, 9, 24, 27, 23, 20, 12, 21, 16, 25, 19, 13, 0, 4, 3, 6, 5, 1, 11, 10, 14, 22, 18, 8, 7, 2], 'cur_cost': 9681.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [26, 25, 17, 9, 24, 27, 23, 20, 12, 21, 16, 15, 19, 2, 7, 8, 18, 22, 14, 10, 11, 1, 5, 6, 3, 4, 0, 13], 'cur_cost': 9616.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [26, 25, 9, 24, 27, 23, 20, 12, 21, 16, 15, 19, 13, 0, 4, 3, 6, 5, 17, 1, 11, 10, 14, 22, 18, 8, 7, 2], 'cur_cost': 11767.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 1, 26, 16,  5,  9, 24, 19, 22, 13, 15,  8,  2, 14, 21,  3,  0,  7,
       23, 27, 17,  4, 11, 25, 20, 18, 10, 12,  6], dtype=int64), 'cur_cost': 14937.0, 'intermediate_solutions': [{'tour': array([ 0, 18, 11, 14, 15, 16,  1, 25,  7, 12,  4,  6,  9, 20,  2,  8, 24,
       21, 13,  5, 27, 17, 10, 26, 23, 22,  3, 19]), 'cur_cost': 17380.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([14,  0, 18, 11, 15, 16,  1, 25,  7, 12,  4,  6,  9, 20,  2,  8, 24,
       21, 13,  5, 27, 17, 10, 26, 23, 22,  3, 19]), 'cur_cost': 17939.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([15, 14,  0, 18, 11, 16,  1, 25,  7, 12,  4,  6,  9, 20,  2,  8, 24,
       21, 13,  5, 27, 17, 10, 26, 23, 22,  3, 19]), 'cur_cost': 17967.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([11, 14,  0, 18, 15, 16,  1, 25,  7, 12,  4,  6,  9, 20,  2,  8, 24,
       21, 13,  5, 27, 17, 10, 26, 23, 22,  3, 19]), 'cur_cost': 17897.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([11, 15, 14,  0, 18, 16,  1, 25,  7, 12,  4,  6,  9, 20,  2,  8, 24,
       21, 13,  5, 27, 17, 10, 26, 23, 22,  3, 19]), 'cur_cost': 17911.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [9, 11, 26, 13, 15, 16, 20, 24, 27, 23, 18, 19, 17, 10, 22, 14, 25, 0, 8, 5, 2, 1, 3, 4, 7, 6, 21, 12], 'cur_cost': 8084.0, 'intermediate_solutions': [{'tour': [2, 0, 4, 7, 6, 21, 19, 10, 17, 18, 9, 14, 16, 13, 11, 26, 1, 25, 15, 22, 23, 27, 8, 24, 3, 5, 20, 12], 'cur_cost': 11717.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 0, 4, 7, 6, 21, 19, 10, 17, 18, 9, 14, 16, 13, 11, 26, 24, 25, 15, 22, 23, 27, 8, 1, 20, 5, 3, 12], 'cur_cost': 9117.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [17, 2, 0, 4, 7, 6, 21, 19, 10, 18, 9, 14, 16, 13, 11, 26, 24, 25, 15, 22, 23, 27, 8, 1, 3, 5, 20, 12], 'cur_cost': 7579.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([15,  0, 18, 24,  6, 13,  3,  8, 23, 27, 11,  9,  7,  2, 26,  5, 17,
       20, 22,  4, 19, 10, 16, 12, 25, 14,  1, 21], dtype=int64), 'cur_cost': 19066.0, 'intermediate_solutions': [{'tour': array([14,  9,  0,  6, 16, 11, 22, 20, 23, 24, 26, 15,  1,  7,  3, 18, 17,
        2,  4, 21,  8, 12, 27, 19, 25,  5, 13, 10]), 'cur_cost': 12879.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 6, 14,  9,  0, 16, 11, 22, 20, 23, 24, 26, 15,  1,  7,  3, 18, 17,
        2,  4, 21,  8, 12, 27, 19, 25,  5, 13, 10]), 'cur_cost': 14914.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([16,  6, 14,  9,  0, 11, 22, 20, 23, 24, 26, 15,  1,  7,  3, 18, 17,
        2,  4, 21,  8, 12, 27, 19, 25,  5, 13, 10]), 'cur_cost': 14896.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 0,  6, 14,  9, 16, 11, 22, 20, 23, 24, 26, 15,  1,  7,  3, 18, 17,
        2,  4, 21,  8, 12, 27, 19, 25,  5, 13, 10]), 'cur_cost': 12882.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 0, 16,  6, 14,  9, 11, 22, 20, 23, 24, 26, 15,  1,  7,  3, 18, 17,
        2,  4, 21,  8, 12, 27, 19, 25,  5, 13, 10]), 'cur_cost': 14933.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [2, 5, 20, 22, 25, 10, 14, 18, 26, 24, 11, 21, 16, 19, 17, 12, 23, 15, 13, 9, 8, 6, 0, 4, 7, 1, 3, 27], 'cur_cost': 9748.0, 'intermediate_solutions': [{'tour': [0, 14, 22, 6, 15, 2, 5, 3, 4, 7, 8, 20, 24, 25, 26, 21, 19, 27, 23, 10, 17, 16, 1, 9, 18, 12, 13, 11], 'cur_cost': 9958.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 22, 6, 1, 2, 5, 3, 4, 7, 8, 20, 24, 25, 13, 12, 18, 9, 15, 16, 17, 10, 23, 27, 19, 21, 26, 11], 'cur_cost': 6761.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 22, 6, 2, 5, 3, 4, 7, 8, 20, 24, 25, 26, 21, 19, 27, 23, 10, 1, 17, 16, 15, 9, 18, 12, 13, 11], 'cur_cost': 7842.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [17, 25, 26, 21, 20, 15, 24, 12, 23, 6, 3, 11, 14, 8, 9, 0, 7, 5, 13, 2, 19, 10, 18, 22, 27, 16, 1, 4], 'cur_cost': 15464.0, 'intermediate_solutions': [{'tour': [0, 15, 20, 7, 5, 12, 4, 6, 1, 2, 8, 19, 24, 25, 26, 22, 21, 23, 27, 10, 14, 17, 16, 3, 18, 9, 11, 13], 'cur_cost': 10067.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 20, 7, 5, 3, 4, 6, 1, 2, 8, 19, 24, 25, 26, 22, 21, 23, 27, 10, 14, 17, 16, 12, 18, 9, 13, 11], 'cur_cost': 5776.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 24, 15, 20, 7, 5, 3, 4, 6, 1, 2, 8, 19, 25, 26, 22, 21, 23, 27, 10, 14, 17, 16, 12, 18, 9, 11, 13], 'cur_cost': 6355.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:51:55,195 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:55,196 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:55,199 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=3246.000, 多样性=0.954
2025-08-05 09:51:55,199 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 09:51:55,199 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 09:51:55,199 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:55,201 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'slight_improvement', 'improvement_rate': 0.00021947158308814607, 'best_improvement': 0.13876359777129213}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.006700167504187581}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.014476361802517615, 'recent_improvements': [-0.09707947226958329, 0.1665593913089831, -0.06812674866454806], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 17, 'new_count': 17, 'count_change': 0, 'old_best_cost': 3055.0, 'new_best_cost': 3055.0, 'quality_improvement': 0.0, 'old_diversity': 0.7337184873949579, 'new_diversity': 0.7337184873949579, 'diversity_change': 0.0}, 'suggestions': ['改进缓慢，建议调整策略平衡或增加扰动强度', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:51:55,203 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 09:51:55,204 - __main__ - INFO - composite1_28 开始进化第 4 代
2025-08-05 09:51:55,204 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 09:51:55,204 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:55,205 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=3246.000, 多样性=0.954
2025-08-05 09:51:55,205 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:55,206 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.954
2025-08-05 09:51:55,206 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:55,211 - EliteExpert - INFO - 精英解分析完成: 精英解数量=17, 多样性=0.734
2025-08-05 09:51:55,213 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 09:51:55,213 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:55,213 - LandscapeExpert - INFO - 添加精英解数据: 17个精英解
2025-08-05 09:51:55,213 - LandscapeExpert - INFO - 数据提取成功: 27个路径, 27个适应度值
2025-08-05 09:51:55,273 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.519, 适应度梯度: -1644.911, 聚类评分: 0.000, 覆盖率: 0.079, 收敛趋势: 0.000, 多样性: 0.903
2025-08-05 09:51:55,274 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 09:51:55,274 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:51:55,274 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite1_28
2025-08-05 09:51:55,280 - visualization.landscape_visualizer - INFO - 插值约束: 242 个点被约束到最小值 3055.00
2025-08-05 09:51:55,365 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite1_28\landscape_composite1_28_iter_64_20250805_095155.html
2025-08-05 09:51:55,405 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite1_28\dashboard_composite1_28_iter_64_20250805_095155.html
2025-08-05 09:51:55,405 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 64
2025-08-05 09:51:55,405 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 09:51:55,406 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1927秒
2025-08-05 09:51:55,406 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5185185185185185, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -1644.9111111111113, 'local_optima_density': 0.5185185185185185, 'gradient_variance': 9706589.46469136, 'cluster_count': 0}, 'population_state': {'diversity': 0.9027118286377546, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0792, 'fitness_entropy': 0.5269676441801738, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -1644.911)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.079)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.903)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358715.274001, 'performance_metrics': {}}}
2025-08-05 09:51:55,406 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:55,406 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:55,406 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:55,406 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:55,407 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 09:51:55,407 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 09:51:55,407 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 09:51:55,407 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:55,408 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:55,408 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 09:51:55,408 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:55,408 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:55,408 - experts.management.collaboration_manager - INFO - 识别精英个体: {2, 4} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:55,409 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:51:55,409 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:51:55,409 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:55,411 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 28
2025-08-05 09:51:55,411 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,411 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,412 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,412 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,412 - ExplorationExpert - INFO - 探索路径生成完成，成本: 7596.0, 路径长度: 28, 收集中间解: 3
2025-08-05 09:51:55,412 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 3, 20, 10, 13, 16, 12, 21, 24, 27, 15, 18, 25, 19, 14, 11, 22, 9, 26, 23, 17, 7, 6, 5, 2, 1, 8, 4], 'cur_cost': 7596.0, 'intermediate_solutions': [{'tour': [0, 15, 19, 7, 5, 3, 4, 6, 1, 2, 11, 20, 24, 25, 26, 22, 21, 23, 27, 10, 14, 17, 16, 12, 18, 9, 8, 13], 'cur_cost': 8376.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 26, 25, 24, 20, 8, 2, 1, 6, 4, 3, 5, 7, 19, 15, 22, 21, 23, 27, 10, 14, 17, 16, 12, 18, 9, 11, 13], 'cur_cost': 6372.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 19, 7, 5, 3, 21, 4, 6, 1, 2, 8, 20, 24, 25, 26, 22, 23, 27, 10, 14, 17, 16, 12, 18, 9, 11, 13], 'cur_cost': 7899.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:55,412 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 7596.00)
2025-08-05 09:51:55,413 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 09:51:55,413 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:55,413 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:55,413 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 18569.0
2025-08-05 09:51:55,423 - ExploitationExpert - INFO - res_population_num: 20
2025-08-05 09:51:55,424 - ExploitationExpert - INFO - res_population_costs: [3055.0, 3055, 3055, 3055.0, 3055.0, 3055, 3055, 3055, 3055, 3055.0, 3055, 3055, 3055, 3055, 3061, 3062.0, 3062, 3055.0, 3055, 3055]
2025-08-05 09:51:55,424 - ExploitationExpert - INFO - res_populations: [array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 19, 24, 25, 27, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 19, 27, 25, 23, 22, 26, 24, 20,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 20, 24, 26, 22, 23,
       25, 27, 19,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64)]
2025-08-05 09:51:55,428 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:55,428 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 20, 10, 13, 16, 12, 21, 24, 27, 15, 18, 25, 19, 14, 11, 22, 9, 26, 23, 17, 7, 6, 5, 2, 1, 8, 4], 'cur_cost': 7596.0}, {'tour': array([12, 22,  3,  2, 27, 14, 24, 20, 16, 26, 23,  1, 19, 15,  9, 11,  8,
        0, 21,  5, 25,  4, 13,  6, 17,  7, 18, 10], dtype=int64), 'cur_cost': 18569.0}, {'tour': [0, 8, 17, 25, 24, 26, 22, 21, 20, 19, 27, 23, 10, 14, 15, 16, 12, 18, 9, 11, 13, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3819.0}, {'tour': [0, 1, 25, 12, 16, 15, 14, 17, 13, 10, 11, 9, 18, 21, 26, 24, 19, 27, 23, 22, 20, 8, 7, 5, 3, 4, 6, 2], 'cur_cost': 3826.0}, {'tour': [0, 1, 7, 26, 24, 25, 23, 27, 19, 20, 21, 22, 10, 14, 17, 16, 15, 9, 18, 12, 13, 11, 8, 6, 5, 3, 4, 2], 'cur_cost': 3246.0}, {'tour': [1, 26, 16, 5, 9, 24, 19, 22, 13, 15, 8, 2, 14, 21, 3, 0, 7, 23, 27, 17, 4, 11, 25, 20, 18, 10, 12, 6], 'cur_cost': 14937.0}, {'tour': [9, 11, 26, 13, 15, 16, 20, 24, 27, 23, 18, 19, 17, 10, 22, 14, 25, 0, 8, 5, 2, 1, 3, 4, 7, 6, 21, 12], 'cur_cost': 8084.0}, {'tour': [15, 0, 18, 24, 6, 13, 3, 8, 23, 27, 11, 9, 7, 2, 26, 5, 17, 20, 22, 4, 19, 10, 16, 12, 25, 14, 1, 21], 'cur_cost': 19066.0}, {'tour': [2, 5, 20, 22, 25, 10, 14, 18, 26, 24, 11, 21, 16, 19, 17, 12, 23, 15, 13, 9, 8, 6, 0, 4, 7, 1, 3, 27], 'cur_cost': 9748.0}, {'tour': [17, 25, 26, 21, 20, 15, 24, 12, 23, 6, 3, 11, 14, 8, 9, 0, 7, 5, 13, 2, 19, 10, 18, 22, 27, 16, 1, 4], 'cur_cost': 15464.0}]
2025-08-05 09:51:55,429 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:51:55,429 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 164, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 164, 'cache_hits': 0, 'similarity_calculations': 682, 'cache_hit_rate': 0.0, 'cache_size': 682}}
2025-08-05 09:51:55,430 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([12, 22,  3,  2, 27, 14, 24, 20, 16, 26, 23,  1, 19, 15,  9, 11,  8,
        0, 21,  5, 25,  4, 13,  6, 17,  7, 18, 10], dtype=int64), 'cur_cost': 18569.0, 'intermediate_solutions': [{'tour': array([20, 16, 13, 14, 10, 24,  2, 17, 19,  9, 22,  1,  4,  8, 21, 15, 25,
        7, 12, 27, 26, 11, 18,  0, 23,  5,  6,  3]), 'cur_cost': 16044.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([14, 20, 16, 13, 10, 24,  2, 17, 19,  9, 22,  1,  4,  8, 21, 15, 25,
        7, 12, 27, 26, 11, 18,  0, 23,  5,  6,  3]), 'cur_cost': 16563.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([10, 14, 20, 16, 13, 24,  2, 17, 19,  9, 22,  1,  4,  8, 21, 15, 25,
        7, 12, 27, 26, 11, 18,  0, 23,  5,  6,  3]), 'cur_cost': 16571.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([13, 14, 20, 16, 10, 24,  2, 17, 19,  9, 22,  1,  4,  8, 21, 15, 25,
        7, 12, 27, 26, 11, 18,  0, 23,  5,  6,  3]), 'cur_cost': 16602.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([13, 10, 14, 20, 16, 24,  2, 17, 19,  9, 22,  1,  4,  8, 21, 15, 25,
        7, 12, 27, 26, 11, 18,  0, 23,  5,  6,  3]), 'cur_cost': 16617.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:55,430 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 18569.00)
2025-08-05 09:51:55,430 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:51:55,430 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:51:55,430 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:55,431 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 28
2025-08-05 09:51:55,431 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,431 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,432 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,432 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,432 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14807.0, 路径长度: 28, 收集中间解: 3
2025-08-05 09:51:55,432 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [17, 16, 26, 23, 24, 19, 27, 18, 25, 14, 5, 9, 11, 4, 1, 6, 3, 7, 10, 13, 0, 22, 2, 15, 8, 20, 12, 21], 'cur_cost': 14807.0, 'intermediate_solutions': [{'tour': [0, 8, 17, 25, 24, 26, 22, 21, 20, 19, 27, 23, 10, 14, 15, 3, 12, 18, 9, 11, 13, 7, 5, 16, 4, 6, 1, 2], 'cur_cost': 8076.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 17, 25, 24, 26, 22, 23, 27, 19, 20, 21, 10, 14, 15, 16, 12, 18, 9, 11, 13, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3779.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 17, 25, 24, 26, 22, 21, 20, 19, 27, 23, 10, 14, 11, 15, 16, 12, 18, 9, 13, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3829.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:55,432 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 14807.00)
2025-08-05 09:51:55,432 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:51:55,432 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:51:55,432 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:55,433 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 28
2025-08-05 09:51:55,433 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,433 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,433 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,434 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,434 - ExplorationExpert - INFO - 探索路径生成完成，成本: 4220.0, 路径长度: 28, 收集中间解: 3
2025-08-05 09:51:55,434 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 19, 14, 22, 26, 24, 25, 23, 27, 20, 21, 10, 17, 16, 15, 9, 18, 12, 13, 11, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 4220.0, 'intermediate_solutions': [{'tour': [0, 1, 25, 12, 16, 15, 14, 21, 13, 10, 11, 9, 18, 17, 26, 24, 19, 27, 23, 22, 20, 8, 7, 5, 3, 4, 6, 2], 'cur_cost': 4831.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 25, 12, 20, 22, 23, 27, 19, 24, 26, 21, 18, 9, 11, 10, 13, 17, 14, 15, 16, 8, 7, 5, 3, 4, 6, 2], 'cur_cost': 4405.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 25, 12, 16, 15, 14, 17, 13, 10, 11, 9, 18, 21, 26, 24, 19, 27, 23, 22, 20, 7, 5, 3, 4, 6, 8, 2], 'cur_cost': 3864.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:55,434 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 4220.00)
2025-08-05 09:51:55,434 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:51:55,434 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:51:55,434 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:55,435 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 28
2025-08-05 09:51:55,435 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,435 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,435 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,436 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,436 - ExplorationExpert - INFO - 探索路径生成完成，成本: 4199.0, 路径长度: 28, 收集中间解: 3
2025-08-05 09:51:55,436 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 13, 22, 14, 17, 16, 15, 9, 18, 12, 10, 11, 21, 26, 24, 25, 23, 27, 19, 20, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 4199.0, 'intermediate_solutions': [{'tour': [0, 1, 7, 26, 24, 25, 23, 27, 19, 20, 21, 22, 10, 14, 17, 16, 15, 9, 18, 12, 6, 11, 8, 13, 5, 3, 4, 2], 'cur_cost': 7419.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 7, 26, 24, 25, 23, 27, 19, 20, 21, 22, 10, 14, 17, 16, 15, 9, 18, 12, 13, 11, 8, 6, 4, 3, 5, 2], 'cur_cost': 3244.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 7, 26, 24, 25, 23, 27, 19, 20, 21, 22, 10, 14, 17, 16, 15, 9, 18, 13, 11, 12, 8, 6, 5, 3, 4, 2], 'cur_cost': 3346.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:55,436 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 4199.00)
2025-08-05 09:51:55,436 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:51:55,436 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:51:55,436 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:55,437 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 28
2025-08-05 09:51:55,437 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,437 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,437 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,437 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,438 - ExplorationExpert - INFO - 探索路径生成完成，成本: 4254.0, 路径长度: 28, 收集中间解: 3
2025-08-05 09:51:55,438 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 23, 11, 22, 26, 24, 25, 27, 19, 20, 21, 10, 14, 17, 16, 15, 9, 18, 12, 13, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 4254.0, 'intermediate_solutions': [{'tour': [1, 26, 16, 5, 9, 24, 19, 22, 0, 15, 8, 2, 14, 21, 3, 13, 7, 23, 27, 17, 4, 11, 25, 20, 18, 10, 12, 6], 'cur_cost': 18581.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 26, 16, 5, 9, 24, 19, 22, 13, 2, 8, 15, 14, 21, 3, 0, 7, 23, 27, 17, 4, 11, 25, 20, 18, 10, 12, 6], 'cur_cost': 14943.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 1, 26, 16, 5, 9, 24, 19, 22, 13, 15, 8, 2, 14, 21, 0, 7, 23, 27, 17, 4, 11, 25, 20, 18, 10, 12, 6], 'cur_cost': 14913.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:55,438 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 4254.00)
2025-08-05 09:51:55,438 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:51:55,438 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:51:55,438 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:55,439 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 28
2025-08-05 09:51:55,439 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,439 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,439 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,439 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,440 - ExplorationExpert - INFO - 探索路径生成完成，成本: 5772.0, 路径长度: 28, 收集中间解: 3
2025-08-05 09:51:55,440 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 9, 22, 5, 3, 4, 7, 6, 1, 2, 8, 20, 24, 25, 26, 21, 19, 27, 23, 10, 14, 17, 16, 15, 18, 12, 13, 11], 'cur_cost': 5772.0, 'intermediate_solutions': [{'tour': [9, 11, 26, 13, 15, 16, 7, 24, 27, 23, 18, 19, 17, 10, 22, 14, 25, 0, 8, 5, 2, 1, 3, 4, 20, 6, 21, 12], 'cur_cost': 11657.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 16, 15, 13, 26, 11, 20, 24, 27, 23, 18, 19, 17, 10, 22, 14, 25, 0, 8, 5, 2, 1, 3, 4, 7, 6, 21, 12], 'cur_cost': 8065.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 11, 26, 15, 16, 13, 20, 24, 27, 23, 18, 19, 17, 10, 22, 14, 25, 0, 8, 5, 2, 1, 3, 4, 7, 6, 21, 12], 'cur_cost': 8077.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:55,440 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 5772.00)
2025-08-05 09:51:55,440 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 09:51:55,440 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:55,440 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:55,440 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 16421.0
2025-08-05 09:51:55,455 - ExploitationExpert - INFO - res_population_num: 26
2025-08-05 09:51:55,455 - ExploitationExpert - INFO - res_population_costs: [3055.0, 3055, 3055, 3055.0, 3055.0, 3055, 3055, 3055, 3055, 3055.0, 3055, 3055, 3055, 3055, 3061, 3062.0, 3062, 3055.0, 3055, 3055, 3055.0, 3055, 3055, 3055, 3055, 3055]
2025-08-05 09:51:55,455 - ExploitationExpert - INFO - res_populations: [array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 19, 24, 25, 27, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 19, 27, 25, 23, 22, 26, 24, 20,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 20, 24, 26, 22, 23,
       25, 27, 19,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64)]
2025-08-05 09:51:55,460 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:55,460 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 20, 10, 13, 16, 12, 21, 24, 27, 15, 18, 25, 19, 14, 11, 22, 9, 26, 23, 17, 7, 6, 5, 2, 1, 8, 4], 'cur_cost': 7596.0}, {'tour': array([12, 22,  3,  2, 27, 14, 24, 20, 16, 26, 23,  1, 19, 15,  9, 11,  8,
        0, 21,  5, 25,  4, 13,  6, 17,  7, 18, 10], dtype=int64), 'cur_cost': 18569.0}, {'tour': [17, 16, 26, 23, 24, 19, 27, 18, 25, 14, 5, 9, 11, 4, 1, 6, 3, 7, 10, 13, 0, 22, 2, 15, 8, 20, 12, 21], 'cur_cost': 14807.0}, {'tour': [0, 19, 14, 22, 26, 24, 25, 23, 27, 20, 21, 10, 17, 16, 15, 9, 18, 12, 13, 11, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 4220.0}, {'tour': [0, 13, 22, 14, 17, 16, 15, 9, 18, 12, 10, 11, 21, 26, 24, 25, 23, 27, 19, 20, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 4199.0}, {'tour': [0, 23, 11, 22, 26, 24, 25, 27, 19, 20, 21, 10, 14, 17, 16, 15, 9, 18, 12, 13, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 4254.0}, {'tour': [0, 9, 22, 5, 3, 4, 7, 6, 1, 2, 8, 20, 24, 25, 26, 21, 19, 27, 23, 10, 14, 17, 16, 15, 18, 12, 13, 11], 'cur_cost': 5772.0}, {'tour': array([22,  9,  0, 21,  2, 16, 18, 15,  6, 11,  5,  1, 25, 17, 24, 26, 20,
       23, 12,  3,  8, 14, 27, 19, 10, 13,  4,  7], dtype=int64), 'cur_cost': 16421.0}, {'tour': [2, 5, 20, 22, 25, 10, 14, 18, 26, 24, 11, 21, 16, 19, 17, 12, 23, 15, 13, 9, 8, 6, 0, 4, 7, 1, 3, 27], 'cur_cost': 9748.0}, {'tour': [17, 25, 26, 21, 20, 15, 24, 12, 23, 6, 3, 11, 14, 8, 9, 0, 7, 5, 13, 2, 19, 10, 18, 22, 27, 16, 1, 4], 'cur_cost': 15464.0}]
2025-08-05 09:51:55,461 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:51:55,461 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 165, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 165, 'cache_hits': 0, 'similarity_calculations': 690, 'cache_hit_rate': 0.0, 'cache_size': 690}}
2025-08-05 09:51:55,462 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([22,  9,  0, 21,  2, 16, 18, 15,  6, 11,  5,  1, 25, 17, 24, 26, 20,
       23, 12,  3,  8, 14, 27, 19, 10, 13,  4,  7], dtype=int64), 'cur_cost': 16421.0, 'intermediate_solutions': [{'tour': array([18,  0, 15, 24,  6, 13,  3,  8, 23, 27, 11,  9,  7,  2, 26,  5, 17,
       20, 22,  4, 19, 10, 16, 12, 25, 14,  1, 21]), 'cur_cost': 19067.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([24, 18,  0, 15,  6, 13,  3,  8, 23, 27, 11,  9,  7,  2, 26,  5, 17,
       20, 22,  4, 19, 10, 16, 12, 25, 14,  1, 21]), 'cur_cost': 18567.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 6, 24, 18,  0, 15, 13,  3,  8, 23, 27, 11,  9,  7,  2, 26,  5, 17,
       20, 22,  4, 19, 10, 16, 12, 25, 14,  1, 21]), 'cur_cost': 18536.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([15, 24, 18,  0,  6, 13,  3,  8, 23, 27, 11,  9,  7,  2, 26,  5, 17,
       20, 22,  4, 19, 10, 16, 12, 25, 14,  1, 21]), 'cur_cost': 17575.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([15,  6, 24, 18,  0, 13,  3,  8, 23, 27, 11,  9,  7,  2, 26,  5, 17,
       20, 22,  4, 19, 10, 16, 12, 25, 14,  1, 21]), 'cur_cost': 19066.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:55,462 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 16421.00)
2025-08-05 09:51:55,462 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:51:55,463 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:51:55,463 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:55,464 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 28
2025-08-05 09:51:55,464 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,464 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,464 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,464 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,464 - ExplorationExpert - INFO - 探索路径生成完成，成本: 5241.0, 路径长度: 28, 收集中间解: 3
2025-08-05 09:51:55,465 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 18, 16, 5, 3, 4, 7, 6, 1, 2, 8, 20, 24, 25, 26, 22, 21, 19, 27, 23, 10, 14, 17, 13, 12, 15, 9, 11], 'cur_cost': 5241.0, 'intermediate_solutions': [{'tour': [2, 5, 20, 22, 25, 10, 14, 18, 26, 24, 11, 21, 16, 19, 17, 0, 23, 15, 13, 9, 8, 6, 12, 4, 7, 1, 3, 27], 'cur_cost': 13366.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 5, 23, 12, 17, 19, 16, 21, 11, 24, 26, 18, 14, 10, 25, 22, 20, 15, 13, 9, 8, 6, 0, 4, 7, 1, 3, 27], 'cur_cost': 9768.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 5, 20, 6, 22, 25, 10, 14, 18, 26, 24, 11, 21, 16, 19, 17, 12, 23, 15, 13, 9, 8, 0, 4, 7, 1, 3, 27], 'cur_cost': 11791.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:55,465 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 5241.00)
2025-08-05 09:51:55,465 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 09:51:55,465 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:55,465 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:55,465 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 21040.0
2025-08-05 09:51:55,479 - ExploitationExpert - INFO - res_population_num: 29
2025-08-05 09:51:55,480 - ExploitationExpert - INFO - res_population_costs: [3055.0, 3055, 3055, 3055.0, 3055.0, 3055, 3055, 3055, 3055, 3055.0, 3055, 3055, 3055, 3055, 3061, 3062.0, 3062, 3055.0, 3055, 3055, 3055.0, 3055, 3055, 3055, 3055, 3055, 3055.0, 3055, 3055]
2025-08-05 09:51:55,480 - ExploitationExpert - INFO - res_populations: [array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 19, 24, 25, 27, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 19, 27, 25, 23, 22, 26, 24, 20,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 20, 24, 26, 22, 23,
       25, 27, 19,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64)]
2025-08-05 09:51:55,486 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:55,487 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 20, 10, 13, 16, 12, 21, 24, 27, 15, 18, 25, 19, 14, 11, 22, 9, 26, 23, 17, 7, 6, 5, 2, 1, 8, 4], 'cur_cost': 7596.0}, {'tour': array([12, 22,  3,  2, 27, 14, 24, 20, 16, 26, 23,  1, 19, 15,  9, 11,  8,
        0, 21,  5, 25,  4, 13,  6, 17,  7, 18, 10], dtype=int64), 'cur_cost': 18569.0}, {'tour': [17, 16, 26, 23, 24, 19, 27, 18, 25, 14, 5, 9, 11, 4, 1, 6, 3, 7, 10, 13, 0, 22, 2, 15, 8, 20, 12, 21], 'cur_cost': 14807.0}, {'tour': [0, 19, 14, 22, 26, 24, 25, 23, 27, 20, 21, 10, 17, 16, 15, 9, 18, 12, 13, 11, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 4220.0}, {'tour': [0, 13, 22, 14, 17, 16, 15, 9, 18, 12, 10, 11, 21, 26, 24, 25, 23, 27, 19, 20, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 4199.0}, {'tour': [0, 23, 11, 22, 26, 24, 25, 27, 19, 20, 21, 10, 14, 17, 16, 15, 9, 18, 12, 13, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 4254.0}, {'tour': [0, 9, 22, 5, 3, 4, 7, 6, 1, 2, 8, 20, 24, 25, 26, 21, 19, 27, 23, 10, 14, 17, 16, 15, 18, 12, 13, 11], 'cur_cost': 5772.0}, {'tour': array([22,  9,  0, 21,  2, 16, 18, 15,  6, 11,  5,  1, 25, 17, 24, 26, 20,
       23, 12,  3,  8, 14, 27, 19, 10, 13,  4,  7], dtype=int64), 'cur_cost': 16421.0}, {'tour': [0, 18, 16, 5, 3, 4, 7, 6, 1, 2, 8, 20, 24, 25, 26, 22, 21, 19, 27, 23, 10, 14, 17, 13, 12, 15, 9, 11], 'cur_cost': 5241.0}, {'tour': array([ 0, 20, 25, 27,  4, 16,  1, 13, 11, 23,  8, 14, 18, 22,  7,  9, 21,
        2, 19,  5,  3, 12, 24, 10,  6, 15, 26, 17], dtype=int64), 'cur_cost': 21040.0}]
2025-08-05 09:51:55,488 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:51:55,488 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 166, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 166, 'cache_hits': 0, 'similarity_calculations': 699, 'cache_hit_rate': 0.0, 'cache_size': 699}}
2025-08-05 09:51:55,488 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([ 0, 20, 25, 27,  4, 16,  1, 13, 11, 23,  8, 14, 18, 22,  7,  9, 21,
        2, 19,  5,  3, 12, 24, 10,  6, 15, 26, 17], dtype=int64), 'cur_cost': 21040.0, 'intermediate_solutions': [{'tour': array([26, 25, 17, 21, 20, 15, 24, 12, 23,  6,  3, 11, 14,  8,  9,  0,  7,
        5, 13,  2, 19, 10, 18, 22, 27, 16,  1,  4]), 'cur_cost': 15980.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([21, 26, 25, 17, 20, 15, 24, 12, 23,  6,  3, 11, 14,  8,  9,  0,  7,
        5, 13,  2, 19, 10, 18, 22, 27, 16,  1,  4]), 'cur_cost': 15975.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([20, 21, 26, 25, 17, 15, 24, 12, 23,  6,  3, 11, 14,  8,  9,  0,  7,
        5, 13,  2, 19, 10, 18, 22, 27, 16,  1,  4]), 'cur_cost': 14920.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([17, 21, 26, 25, 20, 15, 24, 12, 23,  6,  3, 11, 14,  8,  9,  0,  7,
        5, 13,  2, 19, 10, 18, 22, 27, 16,  1,  4]), 'cur_cost': 15439.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([17, 20, 21, 26, 25, 15, 24, 12, 23,  6,  3, 11, 14,  8,  9,  0,  7,
        5, 13,  2, 19, 10, 18, 22, 27, 16,  1,  4]), 'cur_cost': 15464.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:55,488 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 21040.00)
2025-08-05 09:51:55,489 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:55,489 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:55,490 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 20, 10, 13, 16, 12, 21, 24, 27, 15, 18, 25, 19, 14, 11, 22, 9, 26, 23, 17, 7, 6, 5, 2, 1, 8, 4], 'cur_cost': 7596.0, 'intermediate_solutions': [{'tour': [0, 15, 19, 7, 5, 3, 4, 6, 1, 2, 11, 20, 24, 25, 26, 22, 21, 23, 27, 10, 14, 17, 16, 12, 18, 9, 8, 13], 'cur_cost': 8376.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 26, 25, 24, 20, 8, 2, 1, 6, 4, 3, 5, 7, 19, 15, 22, 21, 23, 27, 10, 14, 17, 16, 12, 18, 9, 11, 13], 'cur_cost': 6372.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 19, 7, 5, 3, 21, 4, 6, 1, 2, 8, 20, 24, 25, 26, 22, 23, 27, 10, 14, 17, 16, 12, 18, 9, 11, 13], 'cur_cost': 7899.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([12, 22,  3,  2, 27, 14, 24, 20, 16, 26, 23,  1, 19, 15,  9, 11,  8,
        0, 21,  5, 25,  4, 13,  6, 17,  7, 18, 10], dtype=int64), 'cur_cost': 18569.0, 'intermediate_solutions': [{'tour': array([20, 16, 13, 14, 10, 24,  2, 17, 19,  9, 22,  1,  4,  8, 21, 15, 25,
        7, 12, 27, 26, 11, 18,  0, 23,  5,  6,  3]), 'cur_cost': 16044.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([14, 20, 16, 13, 10, 24,  2, 17, 19,  9, 22,  1,  4,  8, 21, 15, 25,
        7, 12, 27, 26, 11, 18,  0, 23,  5,  6,  3]), 'cur_cost': 16563.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([10, 14, 20, 16, 13, 24,  2, 17, 19,  9, 22,  1,  4,  8, 21, 15, 25,
        7, 12, 27, 26, 11, 18,  0, 23,  5,  6,  3]), 'cur_cost': 16571.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([13, 14, 20, 16, 10, 24,  2, 17, 19,  9, 22,  1,  4,  8, 21, 15, 25,
        7, 12, 27, 26, 11, 18,  0, 23,  5,  6,  3]), 'cur_cost': 16602.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([13, 10, 14, 20, 16, 24,  2, 17, 19,  9, 22,  1,  4,  8, 21, 15, 25,
        7, 12, 27, 26, 11, 18,  0, 23,  5,  6,  3]), 'cur_cost': 16617.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [17, 16, 26, 23, 24, 19, 27, 18, 25, 14, 5, 9, 11, 4, 1, 6, 3, 7, 10, 13, 0, 22, 2, 15, 8, 20, 12, 21], 'cur_cost': 14807.0, 'intermediate_solutions': [{'tour': [0, 8, 17, 25, 24, 26, 22, 21, 20, 19, 27, 23, 10, 14, 15, 3, 12, 18, 9, 11, 13, 7, 5, 16, 4, 6, 1, 2], 'cur_cost': 8076.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 17, 25, 24, 26, 22, 23, 27, 19, 20, 21, 10, 14, 15, 16, 12, 18, 9, 11, 13, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3779.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 17, 25, 24, 26, 22, 21, 20, 19, 27, 23, 10, 14, 11, 15, 16, 12, 18, 9, 13, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3829.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 14, 22, 26, 24, 25, 23, 27, 20, 21, 10, 17, 16, 15, 9, 18, 12, 13, 11, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 4220.0, 'intermediate_solutions': [{'tour': [0, 1, 25, 12, 16, 15, 14, 21, 13, 10, 11, 9, 18, 17, 26, 24, 19, 27, 23, 22, 20, 8, 7, 5, 3, 4, 6, 2], 'cur_cost': 4831.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 25, 12, 20, 22, 23, 27, 19, 24, 26, 21, 18, 9, 11, 10, 13, 17, 14, 15, 16, 8, 7, 5, 3, 4, 6, 2], 'cur_cost': 4405.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 25, 12, 16, 15, 14, 17, 13, 10, 11, 9, 18, 21, 26, 24, 19, 27, 23, 22, 20, 7, 5, 3, 4, 6, 8, 2], 'cur_cost': 3864.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 22, 14, 17, 16, 15, 9, 18, 12, 10, 11, 21, 26, 24, 25, 23, 27, 19, 20, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 4199.0, 'intermediate_solutions': [{'tour': [0, 1, 7, 26, 24, 25, 23, 27, 19, 20, 21, 22, 10, 14, 17, 16, 15, 9, 18, 12, 6, 11, 8, 13, 5, 3, 4, 2], 'cur_cost': 7419.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 7, 26, 24, 25, 23, 27, 19, 20, 21, 22, 10, 14, 17, 16, 15, 9, 18, 12, 13, 11, 8, 6, 4, 3, 5, 2], 'cur_cost': 3244.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 7, 26, 24, 25, 23, 27, 19, 20, 21, 22, 10, 14, 17, 16, 15, 9, 18, 13, 11, 12, 8, 6, 5, 3, 4, 2], 'cur_cost': 3346.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 23, 11, 22, 26, 24, 25, 27, 19, 20, 21, 10, 14, 17, 16, 15, 9, 18, 12, 13, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 4254.0, 'intermediate_solutions': [{'tour': [1, 26, 16, 5, 9, 24, 19, 22, 0, 15, 8, 2, 14, 21, 3, 13, 7, 23, 27, 17, 4, 11, 25, 20, 18, 10, 12, 6], 'cur_cost': 18581.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 26, 16, 5, 9, 24, 19, 22, 13, 2, 8, 15, 14, 21, 3, 0, 7, 23, 27, 17, 4, 11, 25, 20, 18, 10, 12, 6], 'cur_cost': 14943.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 1, 26, 16, 5, 9, 24, 19, 22, 13, 15, 8, 2, 14, 21, 0, 7, 23, 27, 17, 4, 11, 25, 20, 18, 10, 12, 6], 'cur_cost': 14913.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 22, 5, 3, 4, 7, 6, 1, 2, 8, 20, 24, 25, 26, 21, 19, 27, 23, 10, 14, 17, 16, 15, 18, 12, 13, 11], 'cur_cost': 5772.0, 'intermediate_solutions': [{'tour': [9, 11, 26, 13, 15, 16, 7, 24, 27, 23, 18, 19, 17, 10, 22, 14, 25, 0, 8, 5, 2, 1, 3, 4, 20, 6, 21, 12], 'cur_cost': 11657.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 16, 15, 13, 26, 11, 20, 24, 27, 23, 18, 19, 17, 10, 22, 14, 25, 0, 8, 5, 2, 1, 3, 4, 7, 6, 21, 12], 'cur_cost': 8065.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 11, 26, 15, 16, 13, 20, 24, 27, 23, 18, 19, 17, 10, 22, 14, 25, 0, 8, 5, 2, 1, 3, 4, 7, 6, 21, 12], 'cur_cost': 8077.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([22,  9,  0, 21,  2, 16, 18, 15,  6, 11,  5,  1, 25, 17, 24, 26, 20,
       23, 12,  3,  8, 14, 27, 19, 10, 13,  4,  7], dtype=int64), 'cur_cost': 16421.0, 'intermediate_solutions': [{'tour': array([18,  0, 15, 24,  6, 13,  3,  8, 23, 27, 11,  9,  7,  2, 26,  5, 17,
       20, 22,  4, 19, 10, 16, 12, 25, 14,  1, 21]), 'cur_cost': 19067.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([24, 18,  0, 15,  6, 13,  3,  8, 23, 27, 11,  9,  7,  2, 26,  5, 17,
       20, 22,  4, 19, 10, 16, 12, 25, 14,  1, 21]), 'cur_cost': 18567.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 6, 24, 18,  0, 15, 13,  3,  8, 23, 27, 11,  9,  7,  2, 26,  5, 17,
       20, 22,  4, 19, 10, 16, 12, 25, 14,  1, 21]), 'cur_cost': 18536.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([15, 24, 18,  0,  6, 13,  3,  8, 23, 27, 11,  9,  7,  2, 26,  5, 17,
       20, 22,  4, 19, 10, 16, 12, 25, 14,  1, 21]), 'cur_cost': 17575.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([15,  6, 24, 18,  0, 13,  3,  8, 23, 27, 11,  9,  7,  2, 26,  5, 17,
       20, 22,  4, 19, 10, 16, 12, 25, 14,  1, 21]), 'cur_cost': 19066.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 16, 5, 3, 4, 7, 6, 1, 2, 8, 20, 24, 25, 26, 22, 21, 19, 27, 23, 10, 14, 17, 13, 12, 15, 9, 11], 'cur_cost': 5241.0, 'intermediate_solutions': [{'tour': [2, 5, 20, 22, 25, 10, 14, 18, 26, 24, 11, 21, 16, 19, 17, 0, 23, 15, 13, 9, 8, 6, 12, 4, 7, 1, 3, 27], 'cur_cost': 13366.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 5, 23, 12, 17, 19, 16, 21, 11, 24, 26, 18, 14, 10, 25, 22, 20, 15, 13, 9, 8, 6, 0, 4, 7, 1, 3, 27], 'cur_cost': 9768.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 5, 20, 6, 22, 25, 10, 14, 18, 26, 24, 11, 21, 16, 19, 17, 12, 23, 15, 13, 9, 8, 0, 4, 7, 1, 3, 27], 'cur_cost': 11791.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 0, 20, 25, 27,  4, 16,  1, 13, 11, 23,  8, 14, 18, 22,  7,  9, 21,
        2, 19,  5,  3, 12, 24, 10,  6, 15, 26, 17], dtype=int64), 'cur_cost': 21040.0, 'intermediate_solutions': [{'tour': array([26, 25, 17, 21, 20, 15, 24, 12, 23,  6,  3, 11, 14,  8,  9,  0,  7,
        5, 13,  2, 19, 10, 18, 22, 27, 16,  1,  4]), 'cur_cost': 15980.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([21, 26, 25, 17, 20, 15, 24, 12, 23,  6,  3, 11, 14,  8,  9,  0,  7,
        5, 13,  2, 19, 10, 18, 22, 27, 16,  1,  4]), 'cur_cost': 15975.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([20, 21, 26, 25, 17, 15, 24, 12, 23,  6,  3, 11, 14,  8,  9,  0,  7,
        5, 13,  2, 19, 10, 18, 22, 27, 16,  1,  4]), 'cur_cost': 14920.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([17, 21, 26, 25, 20, 15, 24, 12, 23,  6,  3, 11, 14,  8,  9,  0,  7,
        5, 13,  2, 19, 10, 18, 22, 27, 16,  1,  4]), 'cur_cost': 15439.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([17, 20, 21, 26, 25, 15, 24, 12, 23,  6,  3, 11, 14,  8,  9,  0,  7,
        5, 13,  2, 19, 10, 18, 22, 27, 16,  1,  4]), 'cur_cost': 15464.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 09:51:55,491 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:55,491 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:55,493 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=4199.000, 多样性=0.922
2025-08-05 09:51:55,493 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 09:51:55,493 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 09:51:55,493 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:55,499 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.13742232299950394, 'best_improvement': -0.2935921133703019}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.033277870216306044}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.08316995986294747, 'recent_improvements': [0.1665593913089831, -0.06812674866454806, 0.00021947158308814607], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 29, 'new_count': 29, 'count_change': 0, 'old_best_cost': 3055.0, 'new_best_cost': 3055.0, 'quality_improvement': 0.0, 'old_diversity': 0.700387051372273, 'new_diversity': 0.700387051372273, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:51:55,505 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 09:51:55,506 - __main__ - INFO - composite1_28 开始进化第 5 代
2025-08-05 09:51:55,506 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 09:51:55,506 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:55,507 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=4199.000, 多样性=0.922
2025-08-05 09:51:55,507 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:55,508 - PathExpert - INFO - 路径结构分析完成: 公共边数量=4, 路径相似性=0.922
2025-08-05 09:51:55,508 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:55,522 - EliteExpert - INFO - 精英解分析完成: 精英解数量=29, 多样性=0.700
2025-08-05 09:51:55,525 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 09:51:55,525 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:55,525 - LandscapeExpert - INFO - 添加精英解数据: 29个精英解
2025-08-05 09:51:55,525 - LandscapeExpert - INFO - 数据提取成功: 39个路径, 39个适应度值
2025-08-05 09:51:55,697 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.667, 适应度梯度: -1733.903, 聚类评分: 0.000, 覆盖率: 0.081, 收敛趋势: 0.000, 多样性: 0.574
2025-08-05 09:51:55,697 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 09:51:55,697 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:51:55,697 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite1_28
2025-08-05 09:51:55,703 - visualization.landscape_visualizer - INFO - 插值约束: 247 个点被约束到最小值 3055.00
2025-08-05 09:51:55,782 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite1_28\landscape_composite1_28_iter_65_20250805_095155.html
2025-08-05 09:51:55,855 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite1_28\dashboard_composite1_28_iter_65_20250805_095155.html
2025-08-05 09:51:55,855 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 65
2025-08-05 09:51:55,855 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 09:51:55,855 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.3307秒
2025-08-05 09:51:55,856 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.6666666666666666, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -1733.902564102564, 'local_optima_density': 0.6666666666666666, 'gradient_variance': 19092094.256147265, 'cluster_count': 0}, 'population_state': {'diversity': 0.5736184643067234, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0813, 'fitness_entropy': 0.4030684029744052, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -1733.903)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.081)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358715.6972404, 'performance_metrics': {}}}
2025-08-05 09:51:55,856 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:55,856 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:55,856 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:55,856 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:55,857 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 09:51:55,857 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 09:51:55,857 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 09:51:55,857 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:55,857 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:55,857 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 09:51:55,857 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:55,858 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:55,858 - experts.management.collaboration_manager - INFO - 识别精英个体: {3, 4} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:55,858 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:51:55,858 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:51:55,858 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:55,859 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 28
2025-08-05 09:51:55,859 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,859 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,860 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,860 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,860 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3223.0, 路径长度: 28, 收集中间解: 3
2025-08-05 09:51:55,860 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 7, 6, 5, 3, 4, 2, 8, 20, 24, 25, 26, 22, 21, 19, 27, 23, 10, 14, 17, 16, 15, 9, 18, 12, 13, 11], 'cur_cost': 3223.0, 'intermediate_solutions': [{'tour': [0, 3, 20, 10, 13, 16, 12, 25, 24, 27, 15, 18, 21, 19, 14, 11, 22, 9, 26, 23, 17, 7, 6, 5, 2, 1, 8, 4], 'cur_cost': 7600.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 20, 10, 13, 16, 12, 21, 24, 27, 15, 18, 25, 19, 14, 11, 22, 9, 26, 4, 8, 1, 2, 5, 6, 7, 17, 23], 'cur_cost': 9690.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 20, 13, 16, 12, 21, 24, 27, 15, 18, 25, 19, 10, 14, 11, 22, 9, 26, 23, 17, 7, 6, 5, 2, 1, 8, 4], 'cur_cost': 7584.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:55,860 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 3223.00)
2025-08-05 09:51:55,860 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 09:51:55,860 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:55,861 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:55,861 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 15364.0
2025-08-05 09:51:55,874 - ExploitationExpert - INFO - res_population_num: 31
2025-08-05 09:51:55,874 - ExploitationExpert - INFO - res_population_costs: [3055.0, 3055, 3055, 3055.0, 3055.0, 3055, 3055, 3055, 3055, 3055.0, 3055, 3055, 3055, 3055, 3055.0, 3055, 3055, 3055.0, 3055, 3055, 3055, 3055, 3055, 3055.0, 3055, 3055, 3061, 3062.0, 3062, 3055, 3055]
2025-08-05 09:51:55,874 - ExploitationExpert - INFO - res_populations: [array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 19, 24, 25, 27, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 19, 27, 25, 23, 22, 26, 24, 20,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 20, 24, 26, 22, 23,
       25, 27, 19,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64)]
2025-08-05 09:51:55,881 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:55,881 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 7, 6, 5, 3, 4, 2, 8, 20, 24, 25, 26, 22, 21, 19, 27, 23, 10, 14, 17, 16, 15, 9, 18, 12, 13, 11], 'cur_cost': 3223.0}, {'tour': array([ 7,  0, 14, 13, 16, 22, 18, 25, 10, 15,  6,  9,  5, 27, 11, 17, 24,
       12, 21,  3,  1,  2, 20,  8,  4, 19, 26, 23], dtype=int64), 'cur_cost': 15364.0}, {'tour': [17, 16, 26, 23, 24, 19, 27, 18, 25, 14, 5, 9, 11, 4, 1, 6, 3, 7, 10, 13, 0, 22, 2, 15, 8, 20, 12, 21], 'cur_cost': 14807.0}, {'tour': [0, 19, 14, 22, 26, 24, 25, 23, 27, 20, 21, 10, 17, 16, 15, 9, 18, 12, 13, 11, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 4220.0}, {'tour': [0, 13, 22, 14, 17, 16, 15, 9, 18, 12, 10, 11, 21, 26, 24, 25, 23, 27, 19, 20, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 4199.0}, {'tour': [0, 23, 11, 22, 26, 24, 25, 27, 19, 20, 21, 10, 14, 17, 16, 15, 9, 18, 12, 13, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 4254.0}, {'tour': [0, 9, 22, 5, 3, 4, 7, 6, 1, 2, 8, 20, 24, 25, 26, 21, 19, 27, 23, 10, 14, 17, 16, 15, 18, 12, 13, 11], 'cur_cost': 5772.0}, {'tour': [22, 9, 0, 21, 2, 16, 18, 15, 6, 11, 5, 1, 25, 17, 24, 26, 20, 23, 12, 3, 8, 14, 27, 19, 10, 13, 4, 7], 'cur_cost': 16421.0}, {'tour': [0, 18, 16, 5, 3, 4, 7, 6, 1, 2, 8, 20, 24, 25, 26, 22, 21, 19, 27, 23, 10, 14, 17, 13, 12, 15, 9, 11], 'cur_cost': 5241.0}, {'tour': [0, 20, 25, 27, 4, 16, 1, 13, 11, 23, 8, 14, 18, 22, 7, 9, 21, 2, 19, 5, 3, 12, 24, 10, 6, 15, 26, 17], 'cur_cost': 21040.0}]
2025-08-05 09:51:55,881 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:51:55,882 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 167, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 167, 'cache_hits': 0, 'similarity_calculations': 709, 'cache_hit_rate': 0.0, 'cache_size': 709}}
2025-08-05 09:51:55,882 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([ 7,  0, 14, 13, 16, 22, 18, 25, 10, 15,  6,  9,  5, 27, 11, 17, 24,
       12, 21,  3,  1,  2, 20,  8,  4, 19, 26, 23], dtype=int64), 'cur_cost': 15364.0, 'intermediate_solutions': [{'tour': array([ 3, 22, 12,  2, 27, 14, 24, 20, 16, 26, 23,  1, 19, 15,  9, 11,  8,
        0, 21,  5, 25,  4, 13,  6, 17,  7, 18, 10]), 'cur_cost': 20686.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2,  3, 22, 12, 27, 14, 24, 20, 16, 26, 23,  1, 19, 15,  9, 11,  8,
        0, 21,  5, 25,  4, 13,  6, 17,  7, 18, 10]), 'cur_cost': 19111.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([27,  2,  3, 22, 12, 14, 24, 20, 16, 26, 23,  1, 19, 15,  9, 11,  8,
        0, 21,  5, 25,  4, 13,  6, 17,  7, 18, 10]), 'cur_cost': 18539.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([12,  2,  3, 22, 27, 14, 24, 20, 16, 26, 23,  1, 19, 15,  9, 11,  8,
        0, 21,  5, 25,  4, 13,  6, 17,  7, 18, 10]), 'cur_cost': 18087.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([12, 27,  2,  3, 22, 14, 24, 20, 16, 26, 23,  1, 19, 15,  9, 11,  8,
        0, 21,  5, 25,  4, 13,  6, 17,  7, 18, 10]), 'cur_cost': 18570.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:55,882 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 15364.00)
2025-08-05 09:51:55,883 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:51:55,883 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:51:55,883 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:55,884 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 28
2025-08-05 09:51:55,884 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,884 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,884 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,884 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,884 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11752.0, 路径长度: 28, 收集中间解: 3
2025-08-05 09:51:55,885 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [16, 26, 5, 3, 4, 2, 8, 24, 25, 11, 21, 19, 18, 23, 10, 14, 17, 22, 9, 12, 6, 1, 0, 13, 20, 27, 7, 15], 'cur_cost': 11752.0, 'intermediate_solutions': [{'tour': [22, 16, 26, 23, 24, 19, 27, 18, 25, 14, 5, 9, 11, 4, 1, 6, 3, 7, 10, 13, 0, 17, 2, 15, 8, 20, 12, 21], 'cur_cost': 14816.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [17, 16, 26, 23, 24, 19, 27, 18, 25, 14, 5, 9, 11, 4, 22, 0, 13, 10, 7, 3, 6, 1, 2, 15, 8, 20, 12, 21], 'cur_cost': 14765.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [17, 16, 26, 23, 24, 19, 27, 18, 25, 14, 9, 5, 11, 4, 1, 6, 3, 7, 10, 13, 0, 22, 2, 15, 8, 20, 12, 21], 'cur_cost': 14789.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:55,885 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 11752.00)
2025-08-05 09:51:55,885 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:51:55,885 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:51:55,885 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:55,886 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 28
2025-08-05 09:51:55,886 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,886 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,887 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,887 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,887 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3229.0, 路径长度: 28, 收集中间解: 3
2025-08-05 09:51:55,887 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 3, 7, 5, 6, 1, 2, 8, 4, 11, 14, 17, 16, 15, 9, 18, 12, 13, 10, 21, 26, 24, 25, 23, 27, 19, 20, 22], 'cur_cost': 3229.0, 'intermediate_solutions': [{'tour': [0, 19, 14, 22, 26, 24, 25, 23, 27, 20, 21, 10, 2, 16, 15, 9, 18, 12, 13, 11, 8, 7, 5, 3, 4, 6, 1, 17], 'cur_cost': 8404.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [13, 12, 18, 9, 15, 16, 17, 10, 21, 20, 27, 23, 25, 24, 26, 22, 14, 19, 0, 11, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 6241.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 14, 22, 26, 24, 25, 23, 21, 27, 20, 10, 17, 16, 15, 9, 18, 12, 13, 11, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 4281.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:55,887 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 3229.00)
2025-08-05 09:51:55,887 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:51:55,887 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:51:55,887 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:55,888 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 28
2025-08-05 09:51:55,888 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,888 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,889 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,889 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,889 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16454.0, 路径长度: 28, 收集中间解: 3
2025-08-05 09:51:55,889 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [26, 5, 3, 8, 2, 25, 24, 19, 16, 21, 12, 22, 9, 20, 0, 4, 23, 10, 6, 18, 11, 7, 15, 27, 14, 1, 17, 13], 'cur_cost': 16454.0, 'intermediate_solutions': [{'tour': [0, 13, 26, 14, 17, 16, 15, 9, 18, 12, 10, 11, 21, 22, 24, 25, 23, 27, 19, 20, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 4231.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 22, 14, 3, 5, 7, 8, 20, 19, 27, 23, 25, 24, 26, 21, 11, 10, 12, 18, 9, 15, 16, 17, 4, 6, 1, 2], 'cur_cost': 6295.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 22, 14, 17, 16, 15, 9, 3, 18, 12, 10, 11, 21, 26, 24, 25, 23, 27, 19, 20, 8, 7, 5, 4, 6, 1, 2], 'cur_cost': 6308.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:55,889 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 16454.00)
2025-08-05 09:51:55,889 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:51:55,889 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:51:55,889 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:55,890 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 28
2025-08-05 09:51:55,890 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,890 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,890 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,891 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,891 - ExplorationExpert - INFO - 探索路径生成完成，成本: 5233.0, 路径长度: 28, 收集中间解: 3
2025-08-05 09:51:55,891 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 7, 14, 6, 1, 2, 5, 3, 4, 8, 20, 24, 25, 26, 22, 21, 19, 27, 23, 10, 17, 16, 15, 9, 18, 12, 13, 11], 'cur_cost': 5233.0, 'intermediate_solutions': [{'tour': [0, 23, 11, 22, 26, 24, 25, 27, 19, 20, 21, 10, 14, 17, 16, 1, 9, 18, 12, 13, 8, 7, 5, 3, 4, 6, 15, 2], 'cur_cost': 8474.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 23, 11, 22, 26, 24, 25, 27, 19, 20, 21, 10, 14, 17, 16, 15, 9, 18, 12, 13, 8, 7, 5, 3, 1, 6, 4, 2], 'cur_cost': 4296.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 23, 11, 22, 26, 24, 27, 19, 20, 21, 10, 14, 17, 16, 15, 9, 18, 12, 13, 8, 7, 5, 25, 3, 4, 6, 1, 2], 'cur_cost': 6408.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:55,891 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 5233.00)
2025-08-05 09:51:55,891 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:51:55,891 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:51:55,891 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:55,892 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 28
2025-08-05 09:51:55,892 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,893 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,893 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,893 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,893 - ExplorationExpert - INFO - 探索路径生成完成，成本: 5258.0, 路径长度: 28, 收集中间解: 3
2025-08-05 09:51:55,894 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 12, 16, 6, 1, 2, 5, 3, 4, 7, 8, 20, 24, 25, 26, 22, 21, 19, 27, 23, 10, 14, 17, 13, 15, 9, 18, 11], 'cur_cost': 5258.0, 'intermediate_solutions': [{'tour': [0, 9, 22, 17, 3, 4, 7, 6, 1, 2, 8, 20, 24, 25, 26, 21, 19, 27, 23, 10, 14, 5, 16, 15, 18, 12, 13, 11], 'cur_cost': 8395.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 22, 10, 23, 27, 19, 21, 26, 25, 24, 20, 8, 2, 1, 6, 7, 4, 3, 5, 14, 17, 16, 15, 18, 12, 13, 11], 'cur_cost': 6238.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 13, 22, 5, 3, 4, 7, 6, 1, 2, 8, 20, 24, 25, 26, 21, 19, 27, 23, 10, 14, 17, 16, 15, 18, 12, 11], 'cur_cost': 5774.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:55,894 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 5258.00)
2025-08-05 09:51:55,894 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 09:51:55,894 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:55,894 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:55,895 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 16900.0
2025-08-05 09:51:55,912 - ExploitationExpert - INFO - res_population_num: 34
2025-08-05 09:51:55,912 - ExploitationExpert - INFO - res_population_costs: [3055.0, 3055, 3055, 3055.0, 3055.0, 3055, 3055, 3055, 3055, 3055.0, 3055, 3055, 3055, 3055, 3055.0, 3055, 3055, 3055.0, 3055, 3055, 3055, 3055, 3055, 3055.0, 3055, 3055, 3061, 3062.0, 3062, 3055, 3055, 3055.0, 3055, 3055]
2025-08-05 09:51:55,912 - ExploitationExpert - INFO - res_populations: [array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 19, 24, 25, 27, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 19, 27, 25, 23, 22, 26, 24, 20,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 20, 24, 26, 22, 23,
       25, 27, 19,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64)]
2025-08-05 09:51:55,920 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:55,920 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 7, 6, 5, 3, 4, 2, 8, 20, 24, 25, 26, 22, 21, 19, 27, 23, 10, 14, 17, 16, 15, 9, 18, 12, 13, 11], 'cur_cost': 3223.0}, {'tour': array([ 7,  0, 14, 13, 16, 22, 18, 25, 10, 15,  6,  9,  5, 27, 11, 17, 24,
       12, 21,  3,  1,  2, 20,  8,  4, 19, 26, 23], dtype=int64), 'cur_cost': 15364.0}, {'tour': [16, 26, 5, 3, 4, 2, 8, 24, 25, 11, 21, 19, 18, 23, 10, 14, 17, 22, 9, 12, 6, 1, 0, 13, 20, 27, 7, 15], 'cur_cost': 11752.0}, {'tour': [0, 3, 7, 5, 6, 1, 2, 8, 4, 11, 14, 17, 16, 15, 9, 18, 12, 13, 10, 21, 26, 24, 25, 23, 27, 19, 20, 22], 'cur_cost': 3229.0}, {'tour': [26, 5, 3, 8, 2, 25, 24, 19, 16, 21, 12, 22, 9, 20, 0, 4, 23, 10, 6, 18, 11, 7, 15, 27, 14, 1, 17, 13], 'cur_cost': 16454.0}, {'tour': [0, 7, 14, 6, 1, 2, 5, 3, 4, 8, 20, 24, 25, 26, 22, 21, 19, 27, 23, 10, 17, 16, 15, 9, 18, 12, 13, 11], 'cur_cost': 5233.0}, {'tour': [0, 12, 16, 6, 1, 2, 5, 3, 4, 7, 8, 20, 24, 25, 26, 22, 21, 19, 27, 23, 10, 14, 17, 13, 15, 9, 18, 11], 'cur_cost': 5258.0}, {'tour': array([24,  8, 18, 10,  5, 17,  2, 25, 22, 27,  7,  9, 15, 21,  6, 19, 26,
        1,  0, 23,  3,  4, 20, 13, 12, 14, 11, 16], dtype=int64), 'cur_cost': 16900.0}, {'tour': [0, 18, 16, 5, 3, 4, 7, 6, 1, 2, 8, 20, 24, 25, 26, 22, 21, 19, 27, 23, 10, 14, 17, 13, 12, 15, 9, 11], 'cur_cost': 5241.0}, {'tour': [0, 20, 25, 27, 4, 16, 1, 13, 11, 23, 8, 14, 18, 22, 7, 9, 21, 2, 19, 5, 3, 12, 24, 10, 6, 15, 26, 17], 'cur_cost': 21040.0}]
2025-08-05 09:51:55,921 - ExploitationExpert - INFO - 局部搜索耗时: 0.03秒，最大迭代次数: 10
2025-08-05 09:51:55,921 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 168, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 168, 'cache_hits': 0, 'similarity_calculations': 720, 'cache_hit_rate': 0.0, 'cache_size': 720}}
2025-08-05 09:51:55,922 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([24,  8, 18, 10,  5, 17,  2, 25, 22, 27,  7,  9, 15, 21,  6, 19, 26,
        1,  0, 23,  3,  4, 20, 13, 12, 14, 11, 16], dtype=int64), 'cur_cost': 16900.0, 'intermediate_solutions': [{'tour': array([ 0,  9, 22, 21,  2, 16, 18, 15,  6, 11,  5,  1, 25, 17, 24, 26, 20,
       23, 12,  3,  8, 14, 27, 19, 10, 13,  4,  7]), 'cur_cost': 14351.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([21,  0,  9, 22,  2, 16, 18, 15,  6, 11,  5,  1, 25, 17, 24, 26, 20,
       23, 12,  3,  8, 14, 27, 19, 10, 13,  4,  7]), 'cur_cost': 16421.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 2, 21,  0,  9, 22, 16, 18, 15,  6, 11,  5,  1, 25, 17, 24, 26, 20,
       23, 12,  3,  8, 14, 27, 19, 10, 13,  4,  7]), 'cur_cost': 14839.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([22, 21,  0,  9,  2, 16, 18, 15,  6, 11,  5,  1, 25, 17, 24, 26, 20,
       23, 12,  3,  8, 14, 27, 19, 10, 13,  4,  7]), 'cur_cost': 15882.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([22,  2, 21,  0,  9, 16, 18, 15,  6, 11,  5,  1, 25, 17, 24, 26, 20,
       23, 12,  3,  8, 14, 27, 19, 10, 13,  4,  7]), 'cur_cost': 15896.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:55,922 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 16900.00)
2025-08-05 09:51:55,922 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:51:55,922 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:51:55,923 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:55,924 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 28
2025-08-05 09:51:55,924 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,924 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,924 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,924 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:55,924 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14283.0, 路径长度: 28, 收集中间解: 3
2025-08-05 09:51:55,925 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [1, 5, 4, 24, 8, 20, 25, 26, 21, 12, 23, 9, 14, 11, 0, 10, 15, 18, 3, 19, 17, 6, 2, 16, 22, 27, 13, 7], 'cur_cost': 14283.0, 'intermediate_solutions': [{'tour': [0, 18, 16, 5, 3, 4, 7, 6, 1, 2, 8, 20, 24, 25, 26, 22, 21, 19, 27, 9, 10, 14, 17, 13, 12, 15, 23, 11], 'cur_cost': 6410.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 16, 26, 25, 24, 20, 8, 2, 1, 6, 7, 4, 3, 5, 22, 21, 19, 27, 23, 10, 14, 17, 13, 12, 15, 9, 11], 'cur_cost': 5794.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 8, 16, 5, 3, 4, 7, 6, 1, 2, 20, 24, 25, 26, 22, 21, 19, 27, 23, 10, 14, 17, 13, 12, 15, 9, 11], 'cur_cost': 7333.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:55,925 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 14283.00)
2025-08-05 09:51:55,925 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 09:51:55,925 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:55,925 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:55,926 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 16704.0
2025-08-05 09:51:55,939 - ExploitationExpert - INFO - res_population_num: 35
2025-08-05 09:51:55,940 - ExploitationExpert - INFO - res_population_costs: [3055.0, 3055, 3055, 3055.0, 3055.0, 3055, 3055, 3055, 3055, 3055.0, 3055, 3055, 3055, 3055, 3055.0, 3055, 3055, 3055.0, 3055, 3055, 3055, 3055, 3055, 3055.0, 3055, 3055, 3061, 3062.0, 3062, 3055, 3055, 3055.0, 3055, 3055, 3055]
2025-08-05 09:51:55,940 - ExploitationExpert - INFO - res_populations: [array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 19, 24, 25, 27, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 19, 27, 25, 23, 22, 26, 24, 20,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 20, 24, 26, 22, 23,
       25, 27, 19,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64)]
2025-08-05 09:51:55,948 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:55,948 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 7, 6, 5, 3, 4, 2, 8, 20, 24, 25, 26, 22, 21, 19, 27, 23, 10, 14, 17, 16, 15, 9, 18, 12, 13, 11], 'cur_cost': 3223.0}, {'tour': array([ 7,  0, 14, 13, 16, 22, 18, 25, 10, 15,  6,  9,  5, 27, 11, 17, 24,
       12, 21,  3,  1,  2, 20,  8,  4, 19, 26, 23], dtype=int64), 'cur_cost': 15364.0}, {'tour': [16, 26, 5, 3, 4, 2, 8, 24, 25, 11, 21, 19, 18, 23, 10, 14, 17, 22, 9, 12, 6, 1, 0, 13, 20, 27, 7, 15], 'cur_cost': 11752.0}, {'tour': [0, 3, 7, 5, 6, 1, 2, 8, 4, 11, 14, 17, 16, 15, 9, 18, 12, 13, 10, 21, 26, 24, 25, 23, 27, 19, 20, 22], 'cur_cost': 3229.0}, {'tour': [26, 5, 3, 8, 2, 25, 24, 19, 16, 21, 12, 22, 9, 20, 0, 4, 23, 10, 6, 18, 11, 7, 15, 27, 14, 1, 17, 13], 'cur_cost': 16454.0}, {'tour': [0, 7, 14, 6, 1, 2, 5, 3, 4, 8, 20, 24, 25, 26, 22, 21, 19, 27, 23, 10, 17, 16, 15, 9, 18, 12, 13, 11], 'cur_cost': 5233.0}, {'tour': [0, 12, 16, 6, 1, 2, 5, 3, 4, 7, 8, 20, 24, 25, 26, 22, 21, 19, 27, 23, 10, 14, 17, 13, 15, 9, 18, 11], 'cur_cost': 5258.0}, {'tour': array([24,  8, 18, 10,  5, 17,  2, 25, 22, 27,  7,  9, 15, 21,  6, 19, 26,
        1,  0, 23,  3,  4, 20, 13, 12, 14, 11, 16], dtype=int64), 'cur_cost': 16900.0}, {'tour': [1, 5, 4, 24, 8, 20, 25, 26, 21, 12, 23, 9, 14, 11, 0, 10, 15, 18, 3, 19, 17, 6, 2, 16, 22, 27, 13, 7], 'cur_cost': 14283.0}, {'tour': array([10,  6,  7, 16, 17, 12, 13, 25,  0,  9, 18, 22, 14,  8, 20,  4, 27,
        5,  1, 24, 21, 11, 23, 19,  2,  3, 15, 26], dtype=int64), 'cur_cost': 16704.0}]
2025-08-05 09:51:55,949 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:51:55,949 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 169, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 169, 'cache_hits': 0, 'similarity_calculations': 732, 'cache_hit_rate': 0.0, 'cache_size': 732}}
2025-08-05 09:51:55,949 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([10,  6,  7, 16, 17, 12, 13, 25,  0,  9, 18, 22, 14,  8, 20,  4, 27,
        5,  1, 24, 21, 11, 23, 19,  2,  3, 15, 26], dtype=int64), 'cur_cost': 16704.0, 'intermediate_solutions': [{'tour': array([25, 20,  0, 27,  4, 16,  1, 13, 11, 23,  8, 14, 18, 22,  7,  9, 21,
        2, 19,  5,  3, 12, 24, 10,  6, 15, 26, 17]), 'cur_cost': 21586.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([27, 25, 20,  0,  4, 16,  1, 13, 11, 23,  8, 14, 18, 22,  7,  9, 21,
        2, 19,  5,  3, 12, 24, 10,  6, 15, 26, 17]), 'cur_cost': 19521.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 4, 27, 25, 20,  0, 16,  1, 13, 11, 23,  8, 14, 18, 22,  7,  9, 21,
        2, 19,  5,  3, 12, 24, 10,  6, 15, 26, 17]), 'cur_cost': 21040.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 0, 27, 25, 20,  4, 16,  1, 13, 11, 23,  8, 14, 18, 22,  7,  9, 21,
        2, 19,  5,  3, 12, 24, 10,  6, 15, 26, 17]), 'cur_cost': 21040.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 0,  4, 27, 25, 20, 16,  1, 13, 11, 23,  8, 14, 18, 22,  7,  9, 21,
        2, 19,  5,  3, 12, 24, 10,  6, 15, 26, 17]), 'cur_cost': 19530.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:55,950 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 16704.00)
2025-08-05 09:51:55,950 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:55,950 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:55,952 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 7, 6, 5, 3, 4, 2, 8, 20, 24, 25, 26, 22, 21, 19, 27, 23, 10, 14, 17, 16, 15, 9, 18, 12, 13, 11], 'cur_cost': 3223.0, 'intermediate_solutions': [{'tour': [0, 3, 20, 10, 13, 16, 12, 25, 24, 27, 15, 18, 21, 19, 14, 11, 22, 9, 26, 23, 17, 7, 6, 5, 2, 1, 8, 4], 'cur_cost': 7600.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 20, 10, 13, 16, 12, 21, 24, 27, 15, 18, 25, 19, 14, 11, 22, 9, 26, 4, 8, 1, 2, 5, 6, 7, 17, 23], 'cur_cost': 9690.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 20, 13, 16, 12, 21, 24, 27, 15, 18, 25, 19, 10, 14, 11, 22, 9, 26, 23, 17, 7, 6, 5, 2, 1, 8, 4], 'cur_cost': 7584.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 7,  0, 14, 13, 16, 22, 18, 25, 10, 15,  6,  9,  5, 27, 11, 17, 24,
       12, 21,  3,  1,  2, 20,  8,  4, 19, 26, 23], dtype=int64), 'cur_cost': 15364.0, 'intermediate_solutions': [{'tour': array([ 3, 22, 12,  2, 27, 14, 24, 20, 16, 26, 23,  1, 19, 15,  9, 11,  8,
        0, 21,  5, 25,  4, 13,  6, 17,  7, 18, 10]), 'cur_cost': 20686.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2,  3, 22, 12, 27, 14, 24, 20, 16, 26, 23,  1, 19, 15,  9, 11,  8,
        0, 21,  5, 25,  4, 13,  6, 17,  7, 18, 10]), 'cur_cost': 19111.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([27,  2,  3, 22, 12, 14, 24, 20, 16, 26, 23,  1, 19, 15,  9, 11,  8,
        0, 21,  5, 25,  4, 13,  6, 17,  7, 18, 10]), 'cur_cost': 18539.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([12,  2,  3, 22, 27, 14, 24, 20, 16, 26, 23,  1, 19, 15,  9, 11,  8,
        0, 21,  5, 25,  4, 13,  6, 17,  7, 18, 10]), 'cur_cost': 18087.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([12, 27,  2,  3, 22, 14, 24, 20, 16, 26, 23,  1, 19, 15,  9, 11,  8,
        0, 21,  5, 25,  4, 13,  6, 17,  7, 18, 10]), 'cur_cost': 18570.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [16, 26, 5, 3, 4, 2, 8, 24, 25, 11, 21, 19, 18, 23, 10, 14, 17, 22, 9, 12, 6, 1, 0, 13, 20, 27, 7, 15], 'cur_cost': 11752.0, 'intermediate_solutions': [{'tour': [22, 16, 26, 23, 24, 19, 27, 18, 25, 14, 5, 9, 11, 4, 1, 6, 3, 7, 10, 13, 0, 17, 2, 15, 8, 20, 12, 21], 'cur_cost': 14816.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [17, 16, 26, 23, 24, 19, 27, 18, 25, 14, 5, 9, 11, 4, 22, 0, 13, 10, 7, 3, 6, 1, 2, 15, 8, 20, 12, 21], 'cur_cost': 14765.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [17, 16, 26, 23, 24, 19, 27, 18, 25, 14, 9, 5, 11, 4, 1, 6, 3, 7, 10, 13, 0, 22, 2, 15, 8, 20, 12, 21], 'cur_cost': 14789.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 7, 5, 6, 1, 2, 8, 4, 11, 14, 17, 16, 15, 9, 18, 12, 13, 10, 21, 26, 24, 25, 23, 27, 19, 20, 22], 'cur_cost': 3229.0, 'intermediate_solutions': [{'tour': [0, 19, 14, 22, 26, 24, 25, 23, 27, 20, 21, 10, 2, 16, 15, 9, 18, 12, 13, 11, 8, 7, 5, 3, 4, 6, 1, 17], 'cur_cost': 8404.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [13, 12, 18, 9, 15, 16, 17, 10, 21, 20, 27, 23, 25, 24, 26, 22, 14, 19, 0, 11, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 6241.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 14, 22, 26, 24, 25, 23, 21, 27, 20, 10, 17, 16, 15, 9, 18, 12, 13, 11, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 4281.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [26, 5, 3, 8, 2, 25, 24, 19, 16, 21, 12, 22, 9, 20, 0, 4, 23, 10, 6, 18, 11, 7, 15, 27, 14, 1, 17, 13], 'cur_cost': 16454.0, 'intermediate_solutions': [{'tour': [0, 13, 26, 14, 17, 16, 15, 9, 18, 12, 10, 11, 21, 22, 24, 25, 23, 27, 19, 20, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 4231.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 22, 14, 3, 5, 7, 8, 20, 19, 27, 23, 25, 24, 26, 21, 11, 10, 12, 18, 9, 15, 16, 17, 4, 6, 1, 2], 'cur_cost': 6295.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 22, 14, 17, 16, 15, 9, 3, 18, 12, 10, 11, 21, 26, 24, 25, 23, 27, 19, 20, 8, 7, 5, 4, 6, 1, 2], 'cur_cost': 6308.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 14, 6, 1, 2, 5, 3, 4, 8, 20, 24, 25, 26, 22, 21, 19, 27, 23, 10, 17, 16, 15, 9, 18, 12, 13, 11], 'cur_cost': 5233.0, 'intermediate_solutions': [{'tour': [0, 23, 11, 22, 26, 24, 25, 27, 19, 20, 21, 10, 14, 17, 16, 1, 9, 18, 12, 13, 8, 7, 5, 3, 4, 6, 15, 2], 'cur_cost': 8474.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 23, 11, 22, 26, 24, 25, 27, 19, 20, 21, 10, 14, 17, 16, 15, 9, 18, 12, 13, 8, 7, 5, 3, 1, 6, 4, 2], 'cur_cost': 4296.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 23, 11, 22, 26, 24, 27, 19, 20, 21, 10, 14, 17, 16, 15, 9, 18, 12, 13, 8, 7, 5, 25, 3, 4, 6, 1, 2], 'cur_cost': 6408.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 16, 6, 1, 2, 5, 3, 4, 7, 8, 20, 24, 25, 26, 22, 21, 19, 27, 23, 10, 14, 17, 13, 15, 9, 18, 11], 'cur_cost': 5258.0, 'intermediate_solutions': [{'tour': [0, 9, 22, 17, 3, 4, 7, 6, 1, 2, 8, 20, 24, 25, 26, 21, 19, 27, 23, 10, 14, 5, 16, 15, 18, 12, 13, 11], 'cur_cost': 8395.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 22, 10, 23, 27, 19, 21, 26, 25, 24, 20, 8, 2, 1, 6, 7, 4, 3, 5, 14, 17, 16, 15, 18, 12, 13, 11], 'cur_cost': 6238.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 13, 22, 5, 3, 4, 7, 6, 1, 2, 8, 20, 24, 25, 26, 21, 19, 27, 23, 10, 14, 17, 16, 15, 18, 12, 11], 'cur_cost': 5774.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([24,  8, 18, 10,  5, 17,  2, 25, 22, 27,  7,  9, 15, 21,  6, 19, 26,
        1,  0, 23,  3,  4, 20, 13, 12, 14, 11, 16], dtype=int64), 'cur_cost': 16900.0, 'intermediate_solutions': [{'tour': array([ 0,  9, 22, 21,  2, 16, 18, 15,  6, 11,  5,  1, 25, 17, 24, 26, 20,
       23, 12,  3,  8, 14, 27, 19, 10, 13,  4,  7]), 'cur_cost': 14351.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([21,  0,  9, 22,  2, 16, 18, 15,  6, 11,  5,  1, 25, 17, 24, 26, 20,
       23, 12,  3,  8, 14, 27, 19, 10, 13,  4,  7]), 'cur_cost': 16421.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 2, 21,  0,  9, 22, 16, 18, 15,  6, 11,  5,  1, 25, 17, 24, 26, 20,
       23, 12,  3,  8, 14, 27, 19, 10, 13,  4,  7]), 'cur_cost': 14839.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([22, 21,  0,  9,  2, 16, 18, 15,  6, 11,  5,  1, 25, 17, 24, 26, 20,
       23, 12,  3,  8, 14, 27, 19, 10, 13,  4,  7]), 'cur_cost': 15882.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([22,  2, 21,  0,  9, 16, 18, 15,  6, 11,  5,  1, 25, 17, 24, 26, 20,
       23, 12,  3,  8, 14, 27, 19, 10, 13,  4,  7]), 'cur_cost': 15896.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [1, 5, 4, 24, 8, 20, 25, 26, 21, 12, 23, 9, 14, 11, 0, 10, 15, 18, 3, 19, 17, 6, 2, 16, 22, 27, 13, 7], 'cur_cost': 14283.0, 'intermediate_solutions': [{'tour': [0, 18, 16, 5, 3, 4, 7, 6, 1, 2, 8, 20, 24, 25, 26, 22, 21, 19, 27, 9, 10, 14, 17, 13, 12, 15, 23, 11], 'cur_cost': 6410.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 16, 26, 25, 24, 20, 8, 2, 1, 6, 7, 4, 3, 5, 22, 21, 19, 27, 23, 10, 14, 17, 13, 12, 15, 9, 11], 'cur_cost': 5794.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 8, 16, 5, 3, 4, 7, 6, 1, 2, 20, 24, 25, 26, 22, 21, 19, 27, 23, 10, 14, 17, 13, 12, 15, 9, 11], 'cur_cost': 7333.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([10,  6,  7, 16, 17, 12, 13, 25,  0,  9, 18, 22, 14,  8, 20,  4, 27,
        5,  1, 24, 21, 11, 23, 19,  2,  3, 15, 26], dtype=int64), 'cur_cost': 16704.0, 'intermediate_solutions': [{'tour': array([25, 20,  0, 27,  4, 16,  1, 13, 11, 23,  8, 14, 18, 22,  7,  9, 21,
        2, 19,  5,  3, 12, 24, 10,  6, 15, 26, 17]), 'cur_cost': 21586.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([27, 25, 20,  0,  4, 16,  1, 13, 11, 23,  8, 14, 18, 22,  7,  9, 21,
        2, 19,  5,  3, 12, 24, 10,  6, 15, 26, 17]), 'cur_cost': 19521.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 4, 27, 25, 20,  0, 16,  1, 13, 11, 23,  8, 14, 18, 22,  7,  9, 21,
        2, 19,  5,  3, 12, 24, 10,  6, 15, 26, 17]), 'cur_cost': 21040.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 0, 27, 25, 20,  4, 16,  1, 13, 11, 23,  8, 14, 18, 22,  7,  9, 21,
        2, 19,  5,  3, 12, 24, 10,  6, 15, 26, 17]), 'cur_cost': 21040.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 0,  4, 27, 25, 20, 16,  1, 13, 11, 23,  8, 14, 18, 22,  7,  9, 21,
        2, 19,  5,  3, 12, 24, 10,  6, 15, 26, 17]), 'cur_cost': 19530.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 09:51:55,952 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:55,952 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:55,954 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=3223.000, 多样性=0.951
2025-08-05 09:51:55,954 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 09:51:55,954 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 09:51:55,955 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:55,962 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.12256589386853432, 'best_improvement': 0.232436294355799}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.030981067125645207}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.03464778716747794, 'recent_improvements': [-0.06812674866454806, 0.00021947158308814607, -0.13742232299950394], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 35, 'new_count': 35, 'count_change': 0, 'old_best_cost': 3055.0, 'new_best_cost': 3055.0, 'quality_improvement': 0.0, 'old_diversity': 0.6896758703481393, 'new_diversity': 0.6896758703481393, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:51:55,970 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 09:51:55,979 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite1_28_solution.json
2025-08-05 09:51:55,980 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite1_28_20250805_095155.solution
2025-08-05 09:51:55,980 - __main__ - INFO - 实例执行完成 - 运行时间: 1.51s, 最佳成本: 3055.0
2025-08-05 09:51:55,980 - __main__ - INFO - 实例 composite1_28 处理完成
