2025-08-05 10:28:40,278 - __main__ - INFO - geometry3_10 开始进化第 1 代
2025-08-05 10:28:40,278 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 10:28:40,279 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:40,280 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=82.000, 多样性=0.902
2025-08-05 10:28:40,281 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:40,282 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.902
2025-08-05 10:28:40,283 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:40,286 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 10:28:40,286 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:40,286 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 10:28:40,286 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 10:28:40,294 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.300, 适应度梯度: -1.300, 聚类评分: 0.000, 覆盖率: 0.049, 收敛趋势: 0.000, 多样性: 0.902
2025-08-05 10:28:40,294 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 10:28:40,295 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 10:28:40,295 - visualization.landscape_visualizer - INFO - 设置当前实例名: geometry3_10
2025-08-05 10:28:40,300 - visualization.landscape_visualizer - INFO - 插值约束: 6 个点被约束到最小值 82.00
2025-08-05 10:28:40,302 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=8.7%, 梯度: 2.09 → 1.91
2025-08-05 10:28:40,444 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry3_10\landscape_geometry3_10_iter_41_20250805_102840.html
2025-08-05 10:28:40,530 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry3_10\dashboard_geometry3_10_iter_41_20250805_102840.html
2025-08-05 10:28:40,530 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 41
2025-08-05 10:28:40,530 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 10:28:40,531 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2453秒
2025-08-05 10:28:40,531 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 82, 'max_size': 500, 'hits': 0, 'misses': 82, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 276, 'misses': 136, 'hit_rate': 0.6699029126213593, 'evictions': 36, 'ttl': 7200}}
2025-08-05 10:28:40,531 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -1.2999999999999998, 'local_optima_density': 0.3, 'gradient_variance': 374.066, 'cluster_count': 0}, 'population_state': {'diversity': 0.9022222222222223, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0492, 'fitness_entropy': 0.9464119282150145, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -1.300)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.049)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.902)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360920.2948935, 'performance_metrics': {}}}
2025-08-05 10:28:40,531 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:40,531 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:40,531 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:40,531 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:40,532 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 10:28:40,532 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 10:28:40,532 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 10:28:40,532 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:40,532 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:40,532 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 10:28:40,532 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:40,533 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:40,533 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:40,533 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:40,533 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:40,533 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:40,533 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:40,534 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,534 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:40,534 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [1, 3, 8, 0, 6, 2, 7, 4, 5, 9], 'cur_cost': 98.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:40,534 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 98.00)
2025-08-05 10:28:40,534 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:40,534 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:40,534 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:40,535 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:40,535 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,535 - ExplorationExpert - INFO - 探索路径生成完成，成本: 103.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:40,535 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [7, 2, 5, 4, 9, 3, 8, 1, 0, 6], 'cur_cost': 103.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:40,535 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 103.00)
2025-08-05 10:28:40,535 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:40,535 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:40,536 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:40,536 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:40,536 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,536 - ExplorationExpert - INFO - 探索路径生成完成，成本: 102.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:40,536 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [5, 9, 0, 3, 4, 1, 2, 7, 6, 8], 'cur_cost': 102.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:40,537 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 102.00)
2025-08-05 10:28:40,537 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:40,537 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:40,537 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:40,537 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:40,537 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,538 - ExplorationExpert - INFO - 探索路径生成完成，成本: 100.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:40,538 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [1, 8, 0, 5, 4, 3, 2, 7, 6, 9], 'cur_cost': 100.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:40,538 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 100.00)
2025-08-05 10:28:40,538 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:40,538 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:40,538 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:40,539 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:40,540 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,540 - ExplorationExpert - INFO - 探索路径生成完成，成本: 96.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:40,540 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [2, 4, 5, 0, 1, 7, 3, 8, 9, 6], 'cur_cost': 96.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:40,540 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 96.00)
2025-08-05 10:28:40,540 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:40,541 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:40,541 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:40,542 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:40,542 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,542 - ExplorationExpert - INFO - 探索路径生成完成，成本: 94.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:40,542 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [3, 6, 4, 8, 7, 2, 1, 0, 5, 9], 'cur_cost': 94.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:40,543 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 94.00)
2025-08-05 10:28:40,543 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:40,543 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:40,543 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:40,543 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:40,544 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,544 - ExplorationExpert - INFO - 探索路径生成完成，成本: 100.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:40,544 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 2, 4, 8, 5, 7, 3, 1, 6, 9], 'cur_cost': 100.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:40,544 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 100.00)
2025-08-05 10:28:40,544 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:28:40,544 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:40,544 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:40,544 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 100.0
2025-08-05 10:28:40,553 - ExploitationExpert - INFO - res_population_num: 3
2025-08-05 10:28:40,553 - ExploitationExpert - INFO - res_population_costs: [72.0, 72, 72]
2025-08-05 10:28:40,554 - ExploitationExpert - INFO - res_populations: [array([0, 9, 5, 4, 8, 3, 2, 7, 6, 1], dtype=int64), array([0, 9, 5, 4, 8, 3, 7, 2, 1, 6], dtype=int64), array([0, 5, 9, 8, 4, 3, 7, 2, 1, 6], dtype=int64)]
2025-08-05 10:28:40,555 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:40,555 - ExploitationExpert - INFO - populations: [{'tour': [1, 3, 8, 0, 6, 2, 7, 4, 5, 9], 'cur_cost': 98.0}, {'tour': [7, 2, 5, 4, 9, 3, 8, 1, 0, 6], 'cur_cost': 103.0}, {'tour': [5, 9, 0, 3, 4, 1, 2, 7, 6, 8], 'cur_cost': 102.0}, {'tour': [1, 8, 0, 5, 4, 3, 2, 7, 6, 9], 'cur_cost': 100.0}, {'tour': [2, 4, 5, 0, 1, 7, 3, 8, 9, 6], 'cur_cost': 96.0}, {'tour': [3, 6, 4, 8, 7, 2, 1, 0, 5, 9], 'cur_cost': 94.0}, {'tour': [0, 2, 4, 8, 5, 7, 3, 1, 6, 9], 'cur_cost': 100.0}, {'tour': array([8, 3, 2, 1, 9, 5, 7, 0, 6, 4], dtype=int64), 'cur_cost': 100.0}, {'tour': array([1, 0, 6, 4, 7, 2, 8, 5, 3, 9], dtype=int64), 'cur_cost': 110.0}, {'tour': array([1, 7, 3, 0, 9, 4, 6, 8, 2, 5], dtype=int64), 'cur_cost': 122.0}]
2025-08-05 10:28:40,556 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:40,556 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 105, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 105, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:40,557 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([8, 3, 2, 1, 9, 5, 7, 0, 6, 4], dtype=int64), 'cur_cost': 100.0, 'intermediate_solutions': [{'tour': array([8, 7, 9, 6, 5, 1, 2, 4, 0, 3], dtype=int64), 'cur_cost': 118.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 8, 7, 9, 5, 1, 2, 4, 0, 3], dtype=int64), 'cur_cost': 124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 6, 8, 7, 9, 1, 2, 4, 0, 3], dtype=int64), 'cur_cost': 130.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([9, 6, 8, 7, 5, 1, 2, 4, 0, 3], dtype=int64), 'cur_cost': 127.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([9, 5, 6, 8, 7, 1, 2, 4, 0, 3], dtype=int64), 'cur_cost': 118.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:40,558 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 100.00)
2025-08-05 10:28:40,558 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:40,558 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:40,558 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:40,559 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:40,559 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,559 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:40,559 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [8, 4, 2, 3, 1, 0, 9, 5, 7, 6], 'cur_cost': 101.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:40,559 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 101.00)
2025-08-05 10:28:40,559 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:28:40,559 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:40,559 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:40,559 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 109.0
2025-08-05 10:28:40,565 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:40,565 - ExploitationExpert - INFO - res_population_costs: [72.0, 72, 72, 72]
2025-08-05 10:28:40,566 - ExploitationExpert - INFO - res_populations: [array([0, 9, 5, 4, 8, 3, 2, 7, 6, 1], dtype=int64), array([0, 9, 5, 4, 8, 3, 7, 2, 1, 6], dtype=int64), array([0, 5, 9, 8, 4, 3, 7, 2, 1, 6], dtype=int64), array([0, 1, 6, 7, 2, 3, 4, 8, 9, 5], dtype=int64)]
2025-08-05 10:28:40,566 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:40,566 - ExploitationExpert - INFO - populations: [{'tour': [1, 3, 8, 0, 6, 2, 7, 4, 5, 9], 'cur_cost': 98.0}, {'tour': [7, 2, 5, 4, 9, 3, 8, 1, 0, 6], 'cur_cost': 103.0}, {'tour': [5, 9, 0, 3, 4, 1, 2, 7, 6, 8], 'cur_cost': 102.0}, {'tour': [1, 8, 0, 5, 4, 3, 2, 7, 6, 9], 'cur_cost': 100.0}, {'tour': [2, 4, 5, 0, 1, 7, 3, 8, 9, 6], 'cur_cost': 96.0}, {'tour': [3, 6, 4, 8, 7, 2, 1, 0, 5, 9], 'cur_cost': 94.0}, {'tour': [0, 2, 4, 8, 5, 7, 3, 1, 6, 9], 'cur_cost': 100.0}, {'tour': array([8, 3, 2, 1, 9, 5, 7, 0, 6, 4], dtype=int64), 'cur_cost': 100.0}, {'tour': [8, 4, 2, 3, 1, 0, 9, 5, 7, 6], 'cur_cost': 101.0}, {'tour': array([6, 5, 0, 4, 7, 8, 9, 1, 3, 2], dtype=int64), 'cur_cost': 109.0}]
2025-08-05 10:28:40,567 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:40,567 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 106, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 106, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:40,568 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([6, 5, 0, 4, 7, 8, 9, 1, 3, 2], dtype=int64), 'cur_cost': 109.0, 'intermediate_solutions': [{'tour': array([3, 7, 1, 0, 9, 4, 6, 8, 2, 5], dtype=int64), 'cur_cost': 117.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 3, 7, 1, 9, 4, 6, 8, 2, 5], dtype=int64), 'cur_cost': 122.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 0, 3, 7, 1, 4, 6, 8, 2, 5], dtype=int64), 'cur_cost': 118.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 0, 3, 7, 9, 4, 6, 8, 2, 5], dtype=int64), 'cur_cost': 128.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 9, 0, 3, 7, 4, 6, 8, 2, 5], dtype=int64), 'cur_cost': 124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:40,568 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 109.00)
2025-08-05 10:28:40,568 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:40,568 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:40,569 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [1, 3, 8, 0, 6, 2, 7, 4, 5, 9], 'cur_cost': 98.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [7, 2, 5, 4, 9, 3, 8, 1, 0, 6], 'cur_cost': 103.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [5, 9, 0, 3, 4, 1, 2, 7, 6, 8], 'cur_cost': 102.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [1, 8, 0, 5, 4, 3, 2, 7, 6, 9], 'cur_cost': 100.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [2, 4, 5, 0, 1, 7, 3, 8, 9, 6], 'cur_cost': 96.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [3, 6, 4, 8, 7, 2, 1, 0, 5, 9], 'cur_cost': 94.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 4, 8, 5, 7, 3, 1, 6, 9], 'cur_cost': 100.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 3, 2, 1, 9, 5, 7, 0, 6, 4], dtype=int64), 'cur_cost': 100.0, 'intermediate_solutions': [{'tour': array([8, 7, 9, 6, 5, 1, 2, 4, 0, 3], dtype=int64), 'cur_cost': 118.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 8, 7, 9, 5, 1, 2, 4, 0, 3], dtype=int64), 'cur_cost': 124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 6, 8, 7, 9, 1, 2, 4, 0, 3], dtype=int64), 'cur_cost': 130.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([9, 6, 8, 7, 5, 1, 2, 4, 0, 3], dtype=int64), 'cur_cost': 127.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([9, 5, 6, 8, 7, 1, 2, 4, 0, 3], dtype=int64), 'cur_cost': 118.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [8, 4, 2, 3, 1, 0, 9, 5, 7, 6], 'cur_cost': 101.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 5, 0, 4, 7, 8, 9, 1, 3, 2], dtype=int64), 'cur_cost': 109.0, 'intermediate_solutions': [{'tour': array([3, 7, 1, 0, 9, 4, 6, 8, 2, 5], dtype=int64), 'cur_cost': 117.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 3, 7, 1, 9, 4, 6, 8, 2, 5], dtype=int64), 'cur_cost': 122.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 0, 3, 7, 1, 4, 6, 8, 2, 5], dtype=int64), 'cur_cost': 118.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 0, 3, 7, 9, 4, 6, 8, 2, 5], dtype=int64), 'cur_cost': 128.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 9, 0, 3, 7, 4, 6, 8, 2, 5], dtype=int64), 'cur_cost': 124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:28:40,569 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:40,569 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:40,570 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=94.000, 多样性=0.896
2025-08-05 10:28:40,570 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 10:28:40,570 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 10:28:40,570 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:40,571 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.023928953940823238, 'best_improvement': -0.14634146341463414}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.007389162561576278}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.0051919949000149195, 'recent_improvements': [-0.026270680765820825, 0.05358102526616392, -0.015886690965790984], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 72.0, 'new_best_cost': 72.0, 'quality_improvement': 0.0, 'old_diversity': 0.6666666666666667, 'new_diversity': 0.6666666666666667, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启']}
2025-08-05 10:28:40,571 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 10:28:40,571 - __main__ - INFO - geometry3_10 开始进化第 2 代
2025-08-05 10:28:40,571 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 10:28:40,571 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:40,572 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=94.000, 多样性=0.896
2025-08-05 10:28:40,572 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:40,573 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.896
2025-08-05 10:28:40,573 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:40,575 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.667
2025-08-05 10:28:40,577 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 10:28:40,577 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:40,577 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 10:28:40,577 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 10:28:40,585 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.357, 适应度梯度: 1.257, 聚类评分: 0.000, 覆盖率: 0.050, 收敛趋势: 0.000, 多样性: 0.625
2025-08-05 10:28:40,585 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 10:28:40,585 - LandscapeExpert - INFO - 提取到 4 个精英解
2025-08-05 10:28:40,585 - visualization.landscape_visualizer - INFO - 设置当前实例名: geometry3_10
2025-08-05 10:28:40,589 - visualization.landscape_visualizer - INFO - 插值约束: 53 个点被约束到最小值 72.00
2025-08-05 10:28:40,592 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=6.3%, 梯度: 1.29 → 1.21
2025-08-05 10:28:40,720 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry3_10\landscape_geometry3_10_iter_42_20250805_102840.html
2025-08-05 10:28:40,809 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry3_10\dashboard_geometry3_10_iter_42_20250805_102840.html
2025-08-05 10:28:40,809 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 42
2025-08-05 10:28:40,809 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 10:28:40,810 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2332秒
2025-08-05 10:28:40,810 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.35714285714285715, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 1.2571428571428567, 'local_optima_density': 0.35714285714285715, 'gradient_variance': 118.25959183673469, 'cluster_count': 0}, 'population_state': {'diversity': 0.6248037676609105, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0504, 'fitness_entropy': 0.8757609339601583, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.050)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 1.257)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360920.5852842, 'performance_metrics': {}}}
2025-08-05 10:28:40,810 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:40,810 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:40,810 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:40,811 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:40,811 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:40,811 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 10:28:40,811 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:40,811 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:40,811 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:40,812 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:40,812 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:40,812 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:40,812 - experts.management.collaboration_manager - INFO - 识别精英个体: {4, 5} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:40,812 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:40,812 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:40,812 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:40,813 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:40,813 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,813 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,813 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,813 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,813 - ExplorationExpert - INFO - 探索路径生成完成，成本: 107.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:40,813 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 7, 2, 5, 4, 9, 1, 6, 3, 8], 'cur_cost': 107.0, 'intermediate_solutions': [{'tour': [2, 3, 8, 0, 6, 1, 7, 4, 5, 9], 'cur_cost': 96.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 7, 2, 6, 0, 8, 3, 5, 9], 'cur_cost': 102.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 1, 3, 8, 0, 6, 2, 7, 5, 9], 'cur_cost': 104.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:40,814 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 107.00)
2025-08-05 10:28:40,814 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 10:28:40,814 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:40,814 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:40,814 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 109.0
2025-08-05 10:28:40,826 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:40,826 - ExploitationExpert - INFO - res_population_costs: [72.0, 72, 72, 72]
2025-08-05 10:28:40,827 - ExploitationExpert - INFO - res_populations: [array([0, 9, 5, 4, 8, 3, 2, 7, 6, 1], dtype=int64), array([0, 9, 5, 4, 8, 3, 7, 2, 1, 6], dtype=int64), array([0, 5, 9, 8, 4, 3, 7, 2, 1, 6], dtype=int64), array([0, 1, 6, 7, 2, 3, 4, 8, 9, 5], dtype=int64)]
2025-08-05 10:28:40,828 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:40,828 - ExploitationExpert - INFO - populations: [{'tour': [0, 7, 2, 5, 4, 9, 1, 6, 3, 8], 'cur_cost': 107.0}, {'tour': array([6, 3, 7, 9, 0, 8, 4, 2, 5, 1], dtype=int64), 'cur_cost': 109.0}, {'tour': [5, 9, 0, 3, 4, 1, 2, 7, 6, 8], 'cur_cost': 102.0}, {'tour': [1, 8, 0, 5, 4, 3, 2, 7, 6, 9], 'cur_cost': 100.0}, {'tour': [2, 4, 5, 0, 1, 7, 3, 8, 9, 6], 'cur_cost': 96.0}, {'tour': [3, 6, 4, 8, 7, 2, 1, 0, 5, 9], 'cur_cost': 94.0}, {'tour': [0, 2, 4, 8, 5, 7, 3, 1, 6, 9], 'cur_cost': 100.0}, {'tour': [8, 3, 2, 1, 9, 5, 7, 0, 6, 4], 'cur_cost': 100.0}, {'tour': [8, 4, 2, 3, 1, 0, 9, 5, 7, 6], 'cur_cost': 101.0}, {'tour': [6, 5, 0, 4, 7, 8, 9, 1, 3, 2], 'cur_cost': 109.0}]
2025-08-05 10:28:40,828 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:40,828 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 107, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 107, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:40,829 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([6, 3, 7, 9, 0, 8, 4, 2, 5, 1], dtype=int64), 'cur_cost': 109.0, 'intermediate_solutions': [{'tour': array([5, 2, 7, 4, 9, 3, 8, 1, 0, 6]), 'cur_cost': 107.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 5, 2, 7, 9, 3, 8, 1, 0, 6]), 'cur_cost': 114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 4, 5, 2, 7, 3, 8, 1, 0, 6]), 'cur_cost': 97.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 4, 5, 2, 9, 3, 8, 1, 0, 6]), 'cur_cost': 114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 9, 4, 5, 2, 3, 8, 1, 0, 6]), 'cur_cost': 109.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:40,829 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 109.00)
2025-08-05 10:28:40,829 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:40,829 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:40,829 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:40,829 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:40,829 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,830 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,830 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,830 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,830 - ExplorationExpert - INFO - 探索路径生成完成，成本: 112.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:40,830 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [1, 5, 4, 2, 9, 0, 6, 3, 7, 8], 'cur_cost': 112.0, 'intermediate_solutions': [{'tour': [5, 9, 0, 7, 4, 1, 2, 3, 6, 8], 'cur_cost': 113.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 9, 0, 3, 4, 1, 6, 7, 2, 8], 'cur_cost': 92.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 9, 1, 0, 3, 4, 2, 7, 6, 8], 'cur_cost': 102.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:40,830 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 112.00)
2025-08-05 10:28:40,830 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:40,830 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:40,831 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:40,831 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:40,831 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,831 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,831 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,832 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,832 - ExplorationExpert - INFO - 探索路径生成完成，成本: 102.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:40,832 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [3, 1, 6, 2, 5, 4, 0, 9, 8, 7], 'cur_cost': 102.0, 'intermediate_solutions': [{'tour': [1, 8, 0, 5, 2, 3, 4, 7, 6, 9], 'cur_cost': 111.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 0, 5, 6, 7, 2, 3, 4, 9], 'cur_cost': 103.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 1, 8, 0, 5, 4, 3, 2, 7, 6], 'cur_cost': 100.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:40,832 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 102.00)
2025-08-05 10:28:40,832 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:40,832 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:40,832 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:40,832 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:40,833 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,833 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,833 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,833 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,833 - ExplorationExpert - INFO - 探索路径生成完成，成本: 103.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:40,833 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 9, 1, 2, 7, 4, 5, 3, 8, 6], 'cur_cost': 103.0, 'intermediate_solutions': [{'tour': [2, 4, 6, 0, 1, 7, 3, 8, 9, 5], 'cur_cost': 99.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 4, 5, 8, 3, 7, 1, 0, 9, 6], 'cur_cost': 97.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 5, 0, 1, 7, 9, 3, 8, 6], 'cur_cost': 116.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:40,833 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 103.00)
2025-08-05 10:28:40,833 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:40,833 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:40,833 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:40,834 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:40,834 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,834 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,834 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,834 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,835 - ExplorationExpert - INFO - 探索路径生成完成，成本: 107.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:40,835 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [1, 8, 7, 9, 4, 3, 2, 0, 5, 6], 'cur_cost': 107.0, 'intermediate_solutions': [{'tour': [3, 6, 8, 4, 7, 2, 1, 0, 5, 9], 'cur_cost': 98.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 6, 9, 5, 0, 1, 2, 7, 8, 4], 'cur_cost': 83.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 6, 4, 8, 7, 2, 1, 0, 5, 9], 'cur_cost': 94.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:40,835 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 107.00)
2025-08-05 10:28:40,835 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:40,835 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:40,835 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:40,836 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:40,836 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,836 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,836 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,836 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,837 - ExplorationExpert - INFO - 探索路径生成完成，成本: 89.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:40,837 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [4, 8, 0, 6, 1, 2, 3, 7, 5, 9], 'cur_cost': 89.0, 'intermediate_solutions': [{'tour': [0, 2, 4, 1, 5, 7, 3, 8, 6, 9], 'cur_cost': 117.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 4, 8, 5, 7, 3, 6, 1, 9], 'cur_cost': 103.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 4, 6, 8, 5, 7, 3, 1, 9], 'cur_cost': 124.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:40,837 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 89.00)
2025-08-05 10:28:40,837 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:40,837 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:40,837 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:40,838 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:40,838 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,838 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,838 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,839 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,839 - ExplorationExpert - INFO - 探索路径生成完成，成本: 88.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:40,839 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [3, 9, 6, 7, 2, 1, 0, 5, 4, 8], 'cur_cost': 88.0, 'intermediate_solutions': [{'tour': [3, 8, 2, 1, 9, 5, 7, 0, 6, 4], 'cur_cost': 107.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 3, 2, 1, 9, 5, 7, 4, 6, 0], 'cur_cost': 107.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 3, 2, 1, 9, 5, 4, 7, 0, 6], 'cur_cost': 104.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:40,839 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 88.00)
2025-08-05 10:28:40,839 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:40,839 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:40,839 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:40,840 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:40,840 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,840 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,841 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,841 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,841 - ExplorationExpert - INFO - 探索路径生成完成，成本: 103.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:40,841 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [8, 7, 4, 5, 9, 1, 6, 3, 2, 0], 'cur_cost': 103.0, 'intermediate_solutions': [{'tour': [8, 4, 2, 3, 1, 6, 9, 5, 7, 0], 'cur_cost': 101.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 5, 9, 0, 1, 3, 2, 4, 8], 'cur_cost': 101.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 4, 2, 3, 1, 0, 9, 5, 7, 6], 'cur_cost': 101.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:40,841 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 103.00)
2025-08-05 10:28:40,841 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:28:40,841 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:40,841 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:40,842 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 114.0
2025-08-05 10:28:40,848 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:40,848 - ExploitationExpert - INFO - res_population_costs: [72.0, 72, 72, 72]
2025-08-05 10:28:40,848 - ExploitationExpert - INFO - res_populations: [array([0, 9, 5, 4, 8, 3, 2, 7, 6, 1], dtype=int64), array([0, 9, 5, 4, 8, 3, 7, 2, 1, 6], dtype=int64), array([0, 5, 9, 8, 4, 3, 7, 2, 1, 6], dtype=int64), array([0, 1, 6, 7, 2, 3, 4, 8, 9, 5], dtype=int64)]
2025-08-05 10:28:40,849 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:40,849 - ExploitationExpert - INFO - populations: [{'tour': [0, 7, 2, 5, 4, 9, 1, 6, 3, 8], 'cur_cost': 107.0}, {'tour': array([6, 3, 7, 9, 0, 8, 4, 2, 5, 1], dtype=int64), 'cur_cost': 109.0}, {'tour': [1, 5, 4, 2, 9, 0, 6, 3, 7, 8], 'cur_cost': 112.0}, {'tour': [3, 1, 6, 2, 5, 4, 0, 9, 8, 7], 'cur_cost': 102.0}, {'tour': [0, 9, 1, 2, 7, 4, 5, 3, 8, 6], 'cur_cost': 103.0}, {'tour': [1, 8, 7, 9, 4, 3, 2, 0, 5, 6], 'cur_cost': 107.0}, {'tour': [4, 8, 0, 6, 1, 2, 3, 7, 5, 9], 'cur_cost': 89.0}, {'tour': [3, 9, 6, 7, 2, 1, 0, 5, 4, 8], 'cur_cost': 88.0}, {'tour': [8, 7, 4, 5, 9, 1, 6, 3, 2, 0], 'cur_cost': 103.0}, {'tour': array([1, 5, 0, 3, 7, 4, 9, 6, 8, 2], dtype=int64), 'cur_cost': 114.0}]
2025-08-05 10:28:40,850 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:40,850 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 108, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 108, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:40,851 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([1, 5, 0, 3, 7, 4, 9, 6, 8, 2], dtype=int64), 'cur_cost': 114.0, 'intermediate_solutions': [{'tour': array([0, 5, 6, 4, 7, 8, 9, 1, 3, 2]), 'cur_cost': 113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 0, 5, 6, 7, 8, 9, 1, 3, 2]), 'cur_cost': 108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 4, 0, 5, 6, 8, 9, 1, 3, 2]), 'cur_cost': 110.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 4, 0, 5, 7, 8, 9, 1, 3, 2]), 'cur_cost': 115.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 7, 4, 0, 5, 8, 9, 1, 3, 2]), 'cur_cost': 108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:40,851 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 114.00)
2025-08-05 10:28:40,851 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:40,851 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:40,852 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 2, 5, 4, 9, 1, 6, 3, 8], 'cur_cost': 107.0, 'intermediate_solutions': [{'tour': [2, 3, 8, 0, 6, 1, 7, 4, 5, 9], 'cur_cost': 96.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 7, 2, 6, 0, 8, 3, 5, 9], 'cur_cost': 102.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 1, 3, 8, 0, 6, 2, 7, 5, 9], 'cur_cost': 104.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 3, 7, 9, 0, 8, 4, 2, 5, 1], dtype=int64), 'cur_cost': 109.0, 'intermediate_solutions': [{'tour': array([5, 2, 7, 4, 9, 3, 8, 1, 0, 6]), 'cur_cost': 107.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 5, 2, 7, 9, 3, 8, 1, 0, 6]), 'cur_cost': 114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 4, 5, 2, 7, 3, 8, 1, 0, 6]), 'cur_cost': 97.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 4, 5, 2, 9, 3, 8, 1, 0, 6]), 'cur_cost': 114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 9, 4, 5, 2, 3, 8, 1, 0, 6]), 'cur_cost': 109.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [1, 5, 4, 2, 9, 0, 6, 3, 7, 8], 'cur_cost': 112.0, 'intermediate_solutions': [{'tour': [5, 9, 0, 7, 4, 1, 2, 3, 6, 8], 'cur_cost': 113.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 9, 0, 3, 4, 1, 6, 7, 2, 8], 'cur_cost': 92.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 9, 1, 0, 3, 4, 2, 7, 6, 8], 'cur_cost': 102.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [3, 1, 6, 2, 5, 4, 0, 9, 8, 7], 'cur_cost': 102.0, 'intermediate_solutions': [{'tour': [1, 8, 0, 5, 2, 3, 4, 7, 6, 9], 'cur_cost': 111.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 0, 5, 6, 7, 2, 3, 4, 9], 'cur_cost': 103.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 1, 8, 0, 5, 4, 3, 2, 7, 6], 'cur_cost': 100.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 1, 2, 7, 4, 5, 3, 8, 6], 'cur_cost': 103.0, 'intermediate_solutions': [{'tour': [2, 4, 6, 0, 1, 7, 3, 8, 9, 5], 'cur_cost': 99.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 4, 5, 8, 3, 7, 1, 0, 9, 6], 'cur_cost': 97.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 5, 0, 1, 7, 9, 3, 8, 6], 'cur_cost': 116.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [1, 8, 7, 9, 4, 3, 2, 0, 5, 6], 'cur_cost': 107.0, 'intermediate_solutions': [{'tour': [3, 6, 8, 4, 7, 2, 1, 0, 5, 9], 'cur_cost': 98.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 6, 9, 5, 0, 1, 2, 7, 8, 4], 'cur_cost': 83.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 6, 4, 8, 7, 2, 1, 0, 5, 9], 'cur_cost': 94.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [4, 8, 0, 6, 1, 2, 3, 7, 5, 9], 'cur_cost': 89.0, 'intermediate_solutions': [{'tour': [0, 2, 4, 1, 5, 7, 3, 8, 6, 9], 'cur_cost': 117.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 4, 8, 5, 7, 3, 6, 1, 9], 'cur_cost': 103.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 4, 6, 8, 5, 7, 3, 1, 9], 'cur_cost': 124.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [3, 9, 6, 7, 2, 1, 0, 5, 4, 8], 'cur_cost': 88.0, 'intermediate_solutions': [{'tour': [3, 8, 2, 1, 9, 5, 7, 0, 6, 4], 'cur_cost': 107.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 3, 2, 1, 9, 5, 7, 4, 6, 0], 'cur_cost': 107.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 3, 2, 1, 9, 5, 4, 7, 0, 6], 'cur_cost': 104.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [8, 7, 4, 5, 9, 1, 6, 3, 2, 0], 'cur_cost': 103.0, 'intermediate_solutions': [{'tour': [8, 4, 2, 3, 1, 6, 9, 5, 7, 0], 'cur_cost': 101.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 5, 9, 0, 1, 3, 2, 4, 8], 'cur_cost': 101.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 4, 2, 3, 1, 0, 9, 5, 7, 6], 'cur_cost': 101.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([1, 5, 0, 3, 7, 4, 9, 6, 8, 2], dtype=int64), 'cur_cost': 114.0, 'intermediate_solutions': [{'tour': array([0, 5, 6, 4, 7, 8, 9, 1, 3, 2]), 'cur_cost': 113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 0, 5, 6, 7, 8, 9, 1, 3, 2]), 'cur_cost': 108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 4, 0, 5, 6, 8, 9, 1, 3, 2]), 'cur_cost': 110.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 4, 0, 5, 7, 8, 9, 1, 3, 2]), 'cur_cost': 115.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 7, 4, 0, 5, 8, 9, 1, 3, 2]), 'cur_cost': 108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:28:40,852 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:40,852 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:40,853 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=88.000, 多样性=0.909
2025-08-05 10:28:40,853 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 10:28:40,854 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 10:28:40,854 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:40,854 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.004316350188162851, 'best_improvement': 0.06382978723404255}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.014888337468982474}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.014826035662670342, 'recent_improvements': [0.05358102526616392, -0.015886690965790984, 0.023928953940823238], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 72.0, 'new_best_cost': 72.0, 'quality_improvement': 0.0, 'old_diversity': 0.6666666666666667, 'new_diversity': 0.6666666666666667, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配']}
2025-08-05 10:28:40,854 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 10:28:40,854 - __main__ - INFO - geometry3_10 开始进化第 3 代
2025-08-05 10:28:40,854 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 10:28:40,854 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:40,855 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=88.000, 多样性=0.909
2025-08-05 10:28:40,855 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:40,856 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.909
2025-08-05 10:28:40,856 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:40,856 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.667
2025-08-05 10:28:40,858 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 10:28:40,858 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:40,858 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 10:28:40,858 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 10:28:40,866 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.286, 适应度梯度: -0.329, 聚类评分: 0.000, 覆盖率: 0.051, 收敛趋势: 0.000, 多样性: 0.639
2025-08-05 10:28:40,867 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 10:28:40,867 - LandscapeExpert - INFO - 提取到 4 个精英解
2025-08-05 10:28:40,867 - visualization.landscape_visualizer - INFO - 设置当前实例名: geometry3_10
2025-08-05 10:28:40,871 - visualization.landscape_visualizer - INFO - 插值约束: 104 个点被约束到最小值 72.00
2025-08-05 10:28:40,872 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=8.5%, 梯度: 1.82 → 1.67
2025-08-05 10:28:40,974 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry3_10\landscape_geometry3_10_iter_43_20250805_102840.html
2025-08-05 10:28:41,052 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry3_10\dashboard_geometry3_10_iter_43_20250805_102840.html
2025-08-05 10:28:41,052 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 43
2025-08-05 10:28:41,052 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 10:28:41,053 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1948秒
2025-08-05 10:28:41,053 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2857142857142857, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -0.3285714285714287, 'local_optima_density': 0.2857142857142857, 'gradient_variance': 190.81489795918364, 'cluster_count': 0}, 'population_state': {'diversity': 0.6389324960753532, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0512, 'fitness_entropy': 0.9337851376692216, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -0.329)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.051)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360920.867438, 'performance_metrics': {}}}
2025-08-05 10:28:41,053 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:41,053 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:41,053 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:41,054 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:41,054 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:41,054 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 10:28:41,054 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:41,054 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:41,055 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:41,055 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:41,055 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:41,055 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:41,055 - experts.management.collaboration_manager - INFO - 识别精英个体: {6, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:41,056 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:41,056 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:41,056 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:41,056 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:41,057 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,057 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,057 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,057 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,058 - ExplorationExpert - INFO - 探索路径生成完成，成本: 105.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:41,058 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [6, 3, 2, 7, 0, 9, 1, 5, 4, 8], 'cur_cost': 105.0, 'intermediate_solutions': [{'tour': [0, 7, 2, 8, 4, 9, 1, 6, 3, 5], 'cur_cost': 99.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 7, 0, 5, 4, 9, 1, 6, 3, 8], 'cur_cost': 99.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 2, 5, 3, 4, 9, 1, 6, 8], 'cur_cost': 113.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:41,058 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 105.00)
2025-08-05 10:28:41,058 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 10:28:41,058 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:41,059 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:41,059 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 113.0
2025-08-05 10:28:41,069 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:41,069 - ExploitationExpert - INFO - res_population_costs: [72.0, 72, 72, 72]
2025-08-05 10:28:41,069 - ExploitationExpert - INFO - res_populations: [array([0, 9, 5, 4, 8, 3, 2, 7, 6, 1], dtype=int64), array([0, 9, 5, 4, 8, 3, 7, 2, 1, 6], dtype=int64), array([0, 5, 9, 8, 4, 3, 7, 2, 1, 6], dtype=int64), array([0, 1, 6, 7, 2, 3, 4, 8, 9, 5], dtype=int64)]
2025-08-05 10:28:41,070 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:41,070 - ExploitationExpert - INFO - populations: [{'tour': [6, 3, 2, 7, 0, 9, 1, 5, 4, 8], 'cur_cost': 105.0}, {'tour': array([4, 3, 9, 7, 8, 2, 0, 5, 6, 1], dtype=int64), 'cur_cost': 113.0}, {'tour': [1, 5, 4, 2, 9, 0, 6, 3, 7, 8], 'cur_cost': 112.0}, {'tour': [3, 1, 6, 2, 5, 4, 0, 9, 8, 7], 'cur_cost': 102.0}, {'tour': [0, 9, 1, 2, 7, 4, 5, 3, 8, 6], 'cur_cost': 103.0}, {'tour': [1, 8, 7, 9, 4, 3, 2, 0, 5, 6], 'cur_cost': 107.0}, {'tour': [4, 8, 0, 6, 1, 2, 3, 7, 5, 9], 'cur_cost': 89.0}, {'tour': [3, 9, 6, 7, 2, 1, 0, 5, 4, 8], 'cur_cost': 88.0}, {'tour': [8, 7, 4, 5, 9, 1, 6, 3, 2, 0], 'cur_cost': 103.0}, {'tour': [1, 5, 0, 3, 7, 4, 9, 6, 8, 2], 'cur_cost': 114.0}]
2025-08-05 10:28:41,071 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:41,071 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 109, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 109, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:41,071 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([4, 3, 9, 7, 8, 2, 0, 5, 6, 1], dtype=int64), 'cur_cost': 113.0, 'intermediate_solutions': [{'tour': array([7, 3, 6, 9, 0, 8, 4, 2, 5, 1]), 'cur_cost': 108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([9, 7, 3, 6, 0, 8, 4, 2, 5, 1]), 'cur_cost': 116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 9, 7, 3, 6, 8, 4, 2, 5, 1]), 'cur_cost': 115.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 9, 7, 3, 0, 8, 4, 2, 5, 1]), 'cur_cost': 111.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 0, 9, 7, 3, 8, 4, 2, 5, 1]), 'cur_cost': 97.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:41,072 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 113.00)
2025-08-05 10:28:41,072 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-05 10:28:41,072 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:41,072 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:41,073 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 118.0
2025-08-05 10:28:41,082 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:41,082 - ExploitationExpert - INFO - res_population_costs: [72.0, 72, 72, 72]
2025-08-05 10:28:41,082 - ExploitationExpert - INFO - res_populations: [array([0, 9, 5, 4, 8, 3, 2, 7, 6, 1], dtype=int64), array([0, 9, 5, 4, 8, 3, 7, 2, 1, 6], dtype=int64), array([0, 5, 9, 8, 4, 3, 7, 2, 1, 6], dtype=int64), array([0, 1, 6, 7, 2, 3, 4, 8, 9, 5], dtype=int64)]
2025-08-05 10:28:41,084 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:41,084 - ExploitationExpert - INFO - populations: [{'tour': [6, 3, 2, 7, 0, 9, 1, 5, 4, 8], 'cur_cost': 105.0}, {'tour': array([4, 3, 9, 7, 8, 2, 0, 5, 6, 1], dtype=int64), 'cur_cost': 113.0}, {'tour': array([3, 8, 5, 2, 9, 6, 4, 7, 1, 0], dtype=int64), 'cur_cost': 118.0}, {'tour': [3, 1, 6, 2, 5, 4, 0, 9, 8, 7], 'cur_cost': 102.0}, {'tour': [0, 9, 1, 2, 7, 4, 5, 3, 8, 6], 'cur_cost': 103.0}, {'tour': [1, 8, 7, 9, 4, 3, 2, 0, 5, 6], 'cur_cost': 107.0}, {'tour': [4, 8, 0, 6, 1, 2, 3, 7, 5, 9], 'cur_cost': 89.0}, {'tour': [3, 9, 6, 7, 2, 1, 0, 5, 4, 8], 'cur_cost': 88.0}, {'tour': [8, 7, 4, 5, 9, 1, 6, 3, 2, 0], 'cur_cost': 103.0}, {'tour': [1, 5, 0, 3, 7, 4, 9, 6, 8, 2], 'cur_cost': 114.0}]
2025-08-05 10:28:41,085 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:41,085 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 110, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 110, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:41,086 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([3, 8, 5, 2, 9, 6, 4, 7, 1, 0], dtype=int64), 'cur_cost': 118.0, 'intermediate_solutions': [{'tour': array([4, 5, 1, 2, 9, 0, 6, 3, 7, 8]), 'cur_cost': 100.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 4, 5, 1, 9, 0, 6, 3, 7, 8]), 'cur_cost': 106.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 2, 4, 5, 1, 0, 6, 3, 7, 8]), 'cur_cost': 107.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 2, 4, 5, 9, 0, 6, 3, 7, 8]), 'cur_cost': 100.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 9, 2, 4, 5, 0, 6, 3, 7, 8]), 'cur_cost': 112.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:41,086 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 118.00)
2025-08-05 10:28:41,086 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:41,087 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:41,087 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:41,087 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:41,087 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,088 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,088 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,088 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,088 - ExplorationExpert - INFO - 探索路径生成完成，成本: 111.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:41,088 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [5, 7, 8, 9, 2, 4, 3, 1, 0, 6], 'cur_cost': 111.0, 'intermediate_solutions': [{'tour': [2, 1, 6, 3, 5, 4, 0, 9, 8, 7], 'cur_cost': 98.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 9, 0, 4, 5, 2, 6, 1, 7], 'cur_cost': 98.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 1, 6, 7, 2, 5, 4, 0, 9, 8], 'cur_cost': 96.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:41,089 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 111.00)
2025-08-05 10:28:41,089 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:41,089 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:41,089 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:41,089 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:41,090 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,090 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,090 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,090 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,090 - ExplorationExpert - INFO - 探索路径生成完成，成本: 109.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:41,090 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [3, 5, 2, 6, 1, 4, 8, 0, 9, 7], 'cur_cost': 109.0, 'intermediate_solutions': [{'tour': [0, 9, 1, 7, 2, 4, 5, 3, 8, 6], 'cur_cost': 104.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 1, 2, 7, 4, 3, 5, 8, 6], 'cur_cost': 104.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 1, 2, 7, 4, 5, 3, 8, 6], 'cur_cost': 103.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:41,091 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 109.00)
2025-08-05 10:28:41,091 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:41,091 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:41,091 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:41,092 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:41,092 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,092 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,092 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,093 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,093 - ExplorationExpert - INFO - 探索路径生成完成，成本: 96.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:41,093 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 2, 4, 3, 7, 8, 1, 6, 5, 9], 'cur_cost': 96.0, 'intermediate_solutions': [{'tour': [8, 1, 7, 9, 4, 3, 2, 0, 5, 6], 'cur_cost': 120.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 7, 4, 9, 3, 2, 0, 5, 6], 'cur_cost': 108.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 8, 5, 7, 9, 4, 3, 2, 0, 6], 'cur_cost': 111.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:41,093 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 96.00)
2025-08-05 10:28:41,093 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:41,093 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:41,094 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:41,094 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:41,094 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,094 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,095 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,095 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,095 - ExplorationExpert - INFO - 探索路径生成完成，成本: 121.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:41,095 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [5, 2, 6, 0, 8, 1, 3, 7, 9, 4], 'cur_cost': 121.0, 'intermediate_solutions': [{'tour': [4, 8, 0, 6, 5, 2, 3, 7, 1, 9], 'cur_cost': 103.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 0, 6, 1, 2, 3, 7, 9, 5], 'cur_cost': 90.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 8, 0, 6, 5, 1, 2, 3, 7, 9], 'cur_cost': 105.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:41,095 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 121.00)
2025-08-05 10:28:41,096 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:41,096 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:41,096 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:41,096 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:41,097 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,097 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,097 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,097 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,097 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:41,098 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [3, 1, 6, 0, 2, 7, 5, 4, 8, 9], 'cur_cost': 98.0, 'intermediate_solutions': [{'tour': [3, 9, 6, 7, 2, 5, 0, 1, 4, 8], 'cur_cost': 96.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 9, 6, 7, 2, 1, 4, 5, 0, 8], 'cur_cost': 103.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 9, 6, 7, 1, 2, 0, 5, 4, 8], 'cur_cost': 99.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:41,098 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 98.00)
2025-08-05 10:28:41,098 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:41,098 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:41,098 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:41,099 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:41,099 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,100 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,100 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,100 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,100 - ExplorationExpert - INFO - 探索路径生成完成，成本: 103.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:41,100 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [4, 0, 1, 5, 6, 3, 2, 7, 8, 9], 'cur_cost': 103.0, 'intermediate_solutions': [{'tour': [9, 7, 4, 5, 8, 1, 6, 3, 2, 0], 'cur_cost': 113.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 9, 5, 4, 7, 8, 6, 3, 2, 0], 'cur_cost': 109.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 7, 5, 9, 1, 6, 4, 3, 2, 0], 'cur_cost': 105.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:41,100 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 103.00)
2025-08-05 10:28:41,101 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:28:41,101 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:41,101 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:41,101 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 110.0
2025-08-05 10:28:41,111 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:41,111 - ExploitationExpert - INFO - res_population_costs: [72.0, 72, 72, 72]
2025-08-05 10:28:41,111 - ExploitationExpert - INFO - res_populations: [array([0, 9, 5, 4, 8, 3, 2, 7, 6, 1], dtype=int64), array([0, 9, 5, 4, 8, 3, 7, 2, 1, 6], dtype=int64), array([0, 5, 9, 8, 4, 3, 7, 2, 1, 6], dtype=int64), array([0, 1, 6, 7, 2, 3, 4, 8, 9, 5], dtype=int64)]
2025-08-05 10:28:41,113 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:41,113 - ExploitationExpert - INFO - populations: [{'tour': [6, 3, 2, 7, 0, 9, 1, 5, 4, 8], 'cur_cost': 105.0}, {'tour': array([4, 3, 9, 7, 8, 2, 0, 5, 6, 1], dtype=int64), 'cur_cost': 113.0}, {'tour': array([3, 8, 5, 2, 9, 6, 4, 7, 1, 0], dtype=int64), 'cur_cost': 118.0}, {'tour': [5, 7, 8, 9, 2, 4, 3, 1, 0, 6], 'cur_cost': 111.0}, {'tour': [3, 5, 2, 6, 1, 4, 8, 0, 9, 7], 'cur_cost': 109.0}, {'tour': [0, 2, 4, 3, 7, 8, 1, 6, 5, 9], 'cur_cost': 96.0}, {'tour': [5, 2, 6, 0, 8, 1, 3, 7, 9, 4], 'cur_cost': 121.0}, {'tour': [3, 1, 6, 0, 2, 7, 5, 4, 8, 9], 'cur_cost': 98.0}, {'tour': [4, 0, 1, 5, 6, 3, 2, 7, 8, 9], 'cur_cost': 103.0}, {'tour': array([6, 0, 9, 1, 7, 5, 4, 2, 8, 3], dtype=int64), 'cur_cost': 110.0}]
2025-08-05 10:28:41,114 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:41,115 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 111, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 111, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:41,116 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([6, 0, 9, 1, 7, 5, 4, 2, 8, 3], dtype=int64), 'cur_cost': 110.0, 'intermediate_solutions': [{'tour': array([0, 5, 1, 3, 7, 4, 9, 6, 8, 2]), 'cur_cost': 116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 0, 5, 1, 7, 4, 9, 6, 8, 2]), 'cur_cost': 115.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 3, 0, 5, 1, 4, 9, 6, 8, 2]), 'cur_cost': 111.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 3, 0, 5, 7, 4, 9, 6, 8, 2]), 'cur_cost': 122.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 7, 3, 0, 5, 4, 9, 6, 8, 2]), 'cur_cost': 111.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:41,116 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 110.00)
2025-08-05 10:28:41,116 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:41,116 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:41,118 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [6, 3, 2, 7, 0, 9, 1, 5, 4, 8], 'cur_cost': 105.0, 'intermediate_solutions': [{'tour': [0, 7, 2, 8, 4, 9, 1, 6, 3, 5], 'cur_cost': 99.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 7, 0, 5, 4, 9, 1, 6, 3, 8], 'cur_cost': 99.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 2, 5, 3, 4, 9, 1, 6, 8], 'cur_cost': 113.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 3, 9, 7, 8, 2, 0, 5, 6, 1], dtype=int64), 'cur_cost': 113.0, 'intermediate_solutions': [{'tour': array([7, 3, 6, 9, 0, 8, 4, 2, 5, 1]), 'cur_cost': 108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([9, 7, 3, 6, 0, 8, 4, 2, 5, 1]), 'cur_cost': 116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 9, 7, 3, 6, 8, 4, 2, 5, 1]), 'cur_cost': 115.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 9, 7, 3, 0, 8, 4, 2, 5, 1]), 'cur_cost': 111.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 0, 9, 7, 3, 8, 4, 2, 5, 1]), 'cur_cost': 97.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 8, 5, 2, 9, 6, 4, 7, 1, 0], dtype=int64), 'cur_cost': 118.0, 'intermediate_solutions': [{'tour': array([4, 5, 1, 2, 9, 0, 6, 3, 7, 8]), 'cur_cost': 100.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 4, 5, 1, 9, 0, 6, 3, 7, 8]), 'cur_cost': 106.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 2, 4, 5, 1, 0, 6, 3, 7, 8]), 'cur_cost': 107.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 2, 4, 5, 9, 0, 6, 3, 7, 8]), 'cur_cost': 100.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 9, 2, 4, 5, 0, 6, 3, 7, 8]), 'cur_cost': 112.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [5, 7, 8, 9, 2, 4, 3, 1, 0, 6], 'cur_cost': 111.0, 'intermediate_solutions': [{'tour': [2, 1, 6, 3, 5, 4, 0, 9, 8, 7], 'cur_cost': 98.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 9, 0, 4, 5, 2, 6, 1, 7], 'cur_cost': 98.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 1, 6, 7, 2, 5, 4, 0, 9, 8], 'cur_cost': 96.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [3, 5, 2, 6, 1, 4, 8, 0, 9, 7], 'cur_cost': 109.0, 'intermediate_solutions': [{'tour': [0, 9, 1, 7, 2, 4, 5, 3, 8, 6], 'cur_cost': 104.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 1, 2, 7, 4, 3, 5, 8, 6], 'cur_cost': 104.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 1, 2, 7, 4, 5, 3, 8, 6], 'cur_cost': 103.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 4, 3, 7, 8, 1, 6, 5, 9], 'cur_cost': 96.0, 'intermediate_solutions': [{'tour': [8, 1, 7, 9, 4, 3, 2, 0, 5, 6], 'cur_cost': 120.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 7, 4, 9, 3, 2, 0, 5, 6], 'cur_cost': 108.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 8, 5, 7, 9, 4, 3, 2, 0, 6], 'cur_cost': 111.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [5, 2, 6, 0, 8, 1, 3, 7, 9, 4], 'cur_cost': 121.0, 'intermediate_solutions': [{'tour': [4, 8, 0, 6, 5, 2, 3, 7, 1, 9], 'cur_cost': 103.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 0, 6, 1, 2, 3, 7, 9, 5], 'cur_cost': 90.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 8, 0, 6, 5, 1, 2, 3, 7, 9], 'cur_cost': 105.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [3, 1, 6, 0, 2, 7, 5, 4, 8, 9], 'cur_cost': 98.0, 'intermediate_solutions': [{'tour': [3, 9, 6, 7, 2, 5, 0, 1, 4, 8], 'cur_cost': 96.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 9, 6, 7, 2, 1, 4, 5, 0, 8], 'cur_cost': 103.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 9, 6, 7, 1, 2, 0, 5, 4, 8], 'cur_cost': 99.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [4, 0, 1, 5, 6, 3, 2, 7, 8, 9], 'cur_cost': 103.0, 'intermediate_solutions': [{'tour': [9, 7, 4, 5, 8, 1, 6, 3, 2, 0], 'cur_cost': 113.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 9, 5, 4, 7, 8, 6, 3, 2, 0], 'cur_cost': 109.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 7, 5, 9, 1, 6, 4, 3, 2, 0], 'cur_cost': 105.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 0, 9, 1, 7, 5, 4, 2, 8, 3], dtype=int64), 'cur_cost': 110.0, 'intermediate_solutions': [{'tour': array([0, 5, 1, 3, 7, 4, 9, 6, 8, 2]), 'cur_cost': 116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 0, 5, 1, 7, 4, 9, 6, 8, 2]), 'cur_cost': 115.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 3, 0, 5, 1, 4, 9, 6, 8, 2]), 'cur_cost': 111.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 3, 0, 5, 7, 4, 9, 6, 8, 2]), 'cur_cost': 122.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 7, 3, 0, 5, 4, 9, 6, 8, 2]), 'cur_cost': 111.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:28:41,119 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:41,119 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:41,121 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=96.000, 多样性=0.929
2025-08-05 10:28:41,121 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 10:28:41,121 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 10:28:41,122 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:41,122 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.06688949970024997, 'best_improvement': -0.09090909090909091}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.02200488997555014}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.005785170388814067, 'recent_improvements': [-0.015886690965790984, 0.023928953940823238, -0.004316350188162851], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 72.0, 'new_best_cost': 72.0, 'quality_improvement': 0.0, 'old_diversity': 0.6666666666666667, 'new_diversity': 0.6666666666666667, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:41,122 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 10:28:41,122 - __main__ - INFO - geometry3_10 开始进化第 4 代
2025-08-05 10:28:41,123 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 10:28:41,123 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:41,124 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=96.000, 多样性=0.929
2025-08-05 10:28:41,124 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:41,125 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.929
2025-08-05 10:28:41,125 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:41,126 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.667
2025-08-05 10:28:41,130 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 10:28:41,130 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:41,130 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 10:28:41,131 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 10:28:41,141 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.357, 适应度梯度: 0.657, 聚类评分: 0.000, 覆盖率: 0.052, 收敛趋势: 0.000, 多样性: 0.642
2025-08-05 10:28:41,141 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 10:28:41,141 - LandscapeExpert - INFO - 提取到 4 个精英解
2025-08-05 10:28:41,141 - visualization.landscape_visualizer - INFO - 设置当前实例名: geometry3_10
2025-08-05 10:28:41,147 - visualization.landscape_visualizer - INFO - 插值约束: 228 个点被约束到最小值 72.00
2025-08-05 10:28:41,150 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=5.2%, 梯度: 1.91 → 1.81
2025-08-05 10:28:41,267 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry3_10\landscape_geometry3_10_iter_44_20250805_102841.html
2025-08-05 10:28:41,326 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry3_10\dashboard_geometry3_10_iter_44_20250805_102841.html
2025-08-05 10:28:41,326 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 44
2025-08-05 10:28:41,326 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 10:28:41,326 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1965秒
2025-08-05 10:28:41,327 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.35714285714285715, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 0.6571428571428576, 'local_optima_density': 0.35714285714285715, 'gradient_variance': 276.9224489795919, 'cluster_count': 0}, 'population_state': {'diversity': 0.6420722135007849, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0522, 'fitness_entropy': 0.929906376575379, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.052)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360921.141107, 'performance_metrics': {}}}
2025-08-05 10:28:41,327 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:41,327 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:41,327 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:41,327 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:41,328 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:28:41,328 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 10:28:41,328 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:28:41,328 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:41,328 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:41,328 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:28:41,328 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:41,329 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:41,329 - experts.management.collaboration_manager - INFO - 识别精英个体: {5, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:41,329 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:41,329 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:41,329 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:41,330 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:41,330 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,330 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,330 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,331 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,331 - ExplorationExpert - INFO - 探索路径生成完成，成本: 107.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:41,331 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [6, 7, 0, 5, 4, 3, 2, 1, 9, 8], 'cur_cost': 107.0, 'intermediate_solutions': [{'tour': [6, 3, 2, 5, 0, 9, 1, 7, 4, 8], 'cur_cost': 109.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 3, 8, 4, 5, 1, 9, 0, 7, 2], 'cur_cost': 99.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 3, 7, 0, 9, 1, 5, 2, 4, 8], 'cur_cost': 116.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:41,331 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 107.00)
2025-08-05 10:28:41,331 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 10:28:41,332 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:41,332 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:41,332 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 112.0
2025-08-05 10:28:41,338 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:41,338 - ExploitationExpert - INFO - res_population_costs: [72.0, 72, 72, 72]
2025-08-05 10:28:41,338 - ExploitationExpert - INFO - res_populations: [array([0, 9, 5, 4, 8, 3, 2, 7, 6, 1], dtype=int64), array([0, 9, 5, 4, 8, 3, 7, 2, 1, 6], dtype=int64), array([0, 5, 9, 8, 4, 3, 7, 2, 1, 6], dtype=int64), array([0, 1, 6, 7, 2, 3, 4, 8, 9, 5], dtype=int64)]
2025-08-05 10:28:41,339 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:41,339 - ExploitationExpert - INFO - populations: [{'tour': [6, 7, 0, 5, 4, 3, 2, 1, 9, 8], 'cur_cost': 107.0}, {'tour': array([9, 7, 3, 8, 2, 4, 6, 0, 5, 1], dtype=int64), 'cur_cost': 112.0}, {'tour': [3, 8, 5, 2, 9, 6, 4, 7, 1, 0], 'cur_cost': 118.0}, {'tour': [5, 7, 8, 9, 2, 4, 3, 1, 0, 6], 'cur_cost': 111.0}, {'tour': [3, 5, 2, 6, 1, 4, 8, 0, 9, 7], 'cur_cost': 109.0}, {'tour': [0, 2, 4, 3, 7, 8, 1, 6, 5, 9], 'cur_cost': 96.0}, {'tour': [5, 2, 6, 0, 8, 1, 3, 7, 9, 4], 'cur_cost': 121.0}, {'tour': [3, 1, 6, 0, 2, 7, 5, 4, 8, 9], 'cur_cost': 98.0}, {'tour': [4, 0, 1, 5, 6, 3, 2, 7, 8, 9], 'cur_cost': 103.0}, {'tour': [6, 0, 9, 1, 7, 5, 4, 2, 8, 3], 'cur_cost': 110.0}]
2025-08-05 10:28:41,339 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:41,340 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 112, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 112, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:41,340 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([9, 7, 3, 8, 2, 4, 6, 0, 5, 1], dtype=int64), 'cur_cost': 112.0, 'intermediate_solutions': [{'tour': array([9, 3, 4, 7, 8, 2, 0, 5, 6, 1]), 'cur_cost': 106.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 9, 3, 4, 8, 2, 0, 5, 6, 1]), 'cur_cost': 105.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 7, 9, 3, 4, 2, 0, 5, 6, 1]), 'cur_cost': 114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 7, 9, 3, 8, 2, 0, 5, 6, 1]), 'cur_cost': 115.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 8, 7, 9, 3, 2, 0, 5, 6, 1]), 'cur_cost': 106.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:41,340 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 112.00)
2025-08-05 10:28:41,340 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-05 10:28:41,340 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:41,341 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:41,341 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 108.0
2025-08-05 10:28:41,346 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:41,346 - ExploitationExpert - INFO - res_population_costs: [72.0, 72, 72, 72]
2025-08-05 10:28:41,347 - ExploitationExpert - INFO - res_populations: [array([0, 9, 5, 4, 8, 3, 2, 7, 6, 1], dtype=int64), array([0, 9, 5, 4, 8, 3, 7, 2, 1, 6], dtype=int64), array([0, 5, 9, 8, 4, 3, 7, 2, 1, 6], dtype=int64), array([0, 1, 6, 7, 2, 3, 4, 8, 9, 5], dtype=int64)]
2025-08-05 10:28:41,347 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:41,348 - ExploitationExpert - INFO - populations: [{'tour': [6, 7, 0, 5, 4, 3, 2, 1, 9, 8], 'cur_cost': 107.0}, {'tour': array([9, 7, 3, 8, 2, 4, 6, 0, 5, 1], dtype=int64), 'cur_cost': 112.0}, {'tour': array([6, 5, 0, 4, 1, 3, 8, 9, 2, 7], dtype=int64), 'cur_cost': 108.0}, {'tour': [5, 7, 8, 9, 2, 4, 3, 1, 0, 6], 'cur_cost': 111.0}, {'tour': [3, 5, 2, 6, 1, 4, 8, 0, 9, 7], 'cur_cost': 109.0}, {'tour': [0, 2, 4, 3, 7, 8, 1, 6, 5, 9], 'cur_cost': 96.0}, {'tour': [5, 2, 6, 0, 8, 1, 3, 7, 9, 4], 'cur_cost': 121.0}, {'tour': [3, 1, 6, 0, 2, 7, 5, 4, 8, 9], 'cur_cost': 98.0}, {'tour': [4, 0, 1, 5, 6, 3, 2, 7, 8, 9], 'cur_cost': 103.0}, {'tour': [6, 0, 9, 1, 7, 5, 4, 2, 8, 3], 'cur_cost': 110.0}]
2025-08-05 10:28:41,348 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:41,348 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 113, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 113, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:41,349 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([6, 5, 0, 4, 1, 3, 8, 9, 2, 7], dtype=int64), 'cur_cost': 108.0, 'intermediate_solutions': [{'tour': array([5, 8, 3, 2, 9, 6, 4, 7, 1, 0]), 'cur_cost': 106.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 5, 8, 3, 9, 6, 4, 7, 1, 0]), 'cur_cost': 116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 2, 5, 8, 3, 6, 4, 7, 1, 0]), 'cur_cost': 116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 2, 5, 8, 9, 6, 4, 7, 1, 0]), 'cur_cost': 113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 9, 2, 5, 8, 6, 4, 7, 1, 0]), 'cur_cost': 131.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:41,349 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 108.00)
2025-08-05 10:28:41,349 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:41,349 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:41,349 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:41,350 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:41,350 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,350 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,350 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,350 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,351 - ExplorationExpert - INFO - 探索路径生成完成，成本: 125.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:41,351 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 8, 6, 7, 5, 3, 4, 9, 2], 'cur_cost': 125.0, 'intermediate_solutions': [{'tour': [5, 7, 8, 2, 9, 4, 3, 1, 0, 6], 'cur_cost': 112.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 3, 4, 2, 9, 8, 7, 5, 0, 6], 'cur_cost': 104.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 2, 7, 8, 9, 4, 3, 1, 0, 6], 'cur_cost': 99.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:41,351 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 125.00)
2025-08-05 10:28:41,351 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:41,351 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:41,351 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:41,351 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:41,351 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,352 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,352 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,352 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,352 - ExplorationExpert - INFO - 探索路径生成完成，成本: 86.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:41,352 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [7, 2, 1, 6, 3, 0, 9, 5, 4, 8], 'cur_cost': 86.0, 'intermediate_solutions': [{'tour': [3, 5, 8, 6, 1, 4, 2, 0, 9, 7], 'cur_cost': 118.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 2, 6, 9, 0, 8, 4, 1, 7], 'cur_cost': 108.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 5, 2, 8, 6, 1, 4, 0, 9, 7], 'cur_cost': 121.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:41,352 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 86.00)
2025-08-05 10:28:41,352 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:41,352 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:41,352 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:41,353 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:41,353 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,353 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,353 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,353 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,353 - ExplorationExpert - INFO - 探索路径生成完成，成本: 113.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:41,353 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [6, 9, 3, 5, 4, 7, 2, 1, 0, 8], 'cur_cost': 113.0, 'intermediate_solutions': [{'tour': [1, 2, 4, 3, 7, 8, 0, 6, 5, 9], 'cur_cost': 99.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 4, 3, 7, 8, 1, 5, 6, 9], 'cur_cost': 108.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 0, 2, 4, 3, 8, 1, 6, 5, 9], 'cur_cost': 109.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:41,354 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 113.00)
2025-08-05 10:28:41,354 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:28:41,354 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:41,354 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:41,354 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 97.0
2025-08-05 10:28:41,360 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:41,360 - ExploitationExpert - INFO - res_population_costs: [72.0, 72, 72, 72]
2025-08-05 10:28:41,360 - ExploitationExpert - INFO - res_populations: [array([0, 9, 5, 4, 8, 3, 2, 7, 6, 1], dtype=int64), array([0, 9, 5, 4, 8, 3, 7, 2, 1, 6], dtype=int64), array([0, 5, 9, 8, 4, 3, 7, 2, 1, 6], dtype=int64), array([0, 1, 6, 7, 2, 3, 4, 8, 9, 5], dtype=int64)]
2025-08-05 10:28:41,361 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:41,361 - ExploitationExpert - INFO - populations: [{'tour': [6, 7, 0, 5, 4, 3, 2, 1, 9, 8], 'cur_cost': 107.0}, {'tour': array([9, 7, 3, 8, 2, 4, 6, 0, 5, 1], dtype=int64), 'cur_cost': 112.0}, {'tour': array([6, 5, 0, 4, 1, 3, 8, 9, 2, 7], dtype=int64), 'cur_cost': 108.0}, {'tour': [0, 1, 8, 6, 7, 5, 3, 4, 9, 2], 'cur_cost': 125.0}, {'tour': [7, 2, 1, 6, 3, 0, 9, 5, 4, 8], 'cur_cost': 86.0}, {'tour': [6, 9, 3, 5, 4, 7, 2, 1, 0, 8], 'cur_cost': 113.0}, {'tour': array([4, 8, 7, 2, 0, 1, 3, 5, 9, 6], dtype=int64), 'cur_cost': 97.0}, {'tour': [3, 1, 6, 0, 2, 7, 5, 4, 8, 9], 'cur_cost': 98.0}, {'tour': [4, 0, 1, 5, 6, 3, 2, 7, 8, 9], 'cur_cost': 103.0}, {'tour': [6, 0, 9, 1, 7, 5, 4, 2, 8, 3], 'cur_cost': 110.0}]
2025-08-05 10:28:41,361 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:41,362 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 114, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 114, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:41,362 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([4, 8, 7, 2, 0, 1, 3, 5, 9, 6], dtype=int64), 'cur_cost': 97.0, 'intermediate_solutions': [{'tour': array([6, 2, 5, 0, 8, 1, 3, 7, 9, 4]), 'cur_cost': 126.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 6, 2, 5, 8, 1, 3, 7, 9, 4]), 'cur_cost': 121.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 0, 6, 2, 5, 1, 3, 7, 9, 4]), 'cur_cost': 113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 0, 6, 2, 8, 1, 3, 7, 9, 4]), 'cur_cost': 113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 8, 0, 6, 2, 1, 3, 7, 9, 4]), 'cur_cost': 113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:41,362 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 97.00)
2025-08-05 10:28:41,362 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:41,362 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:41,362 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:41,363 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:41,363 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,363 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,363 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,363 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,363 - ExplorationExpert - INFO - 探索路径生成完成，成本: 95.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:41,364 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [4, 3, 5, 1, 2, 7, 0, 6, 9, 8], 'cur_cost': 95.0, 'intermediate_solutions': [{'tour': [3, 1, 6, 5, 2, 7, 0, 4, 8, 9], 'cur_cost': 105.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 1, 6, 0, 2, 7, 5, 9, 8, 4], 'cur_cost': 87.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 1, 6, 0, 2, 7, 8, 5, 4, 9], 'cur_cost': 100.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:41,364 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 95.00)
2025-08-05 10:28:41,364 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:41,364 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:41,364 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:41,364 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:41,364 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,365 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,365 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,365 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,365 - ExplorationExpert - INFO - 探索路径生成完成，成本: 117.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:41,365 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [8, 3, 0, 7, 5, 9, 6, 4, 1, 2], 'cur_cost': 117.0, 'intermediate_solutions': [{'tour': [4, 0, 1, 5, 6, 3, 9, 7, 8, 2], 'cur_cost': 124.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 0, 1, 5, 6, 3, 2, 7, 9, 8], 'cur_cost': 104.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 1, 5, 6, 3, 2, 8, 7, 9], 'cur_cost': 117.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:41,365 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 117.00)
2025-08-05 10:28:41,365 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:41,365 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:41,365 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:41,366 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:41,366 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,366 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,366 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,366 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,366 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:41,366 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [1, 4, 2, 3, 5, 9, 8, 7, 6, 0], 'cur_cost': 98.0, 'intermediate_solutions': [{'tour': [6, 2, 9, 1, 7, 5, 4, 0, 8, 3], 'cur_cost': 123.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 0, 9, 3, 8, 2, 4, 5, 7, 1], 'cur_cost': 103.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 9, 1, 7, 5, 4, 2, 8, 3], 'cur_cost': 110.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:41,367 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 98.00)
2025-08-05 10:28:41,367 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:41,367 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:41,368 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [6, 7, 0, 5, 4, 3, 2, 1, 9, 8], 'cur_cost': 107.0, 'intermediate_solutions': [{'tour': [6, 3, 2, 5, 0, 9, 1, 7, 4, 8], 'cur_cost': 109.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 3, 8, 4, 5, 1, 9, 0, 7, 2], 'cur_cost': 99.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 3, 7, 0, 9, 1, 5, 2, 4, 8], 'cur_cost': 116.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([9, 7, 3, 8, 2, 4, 6, 0, 5, 1], dtype=int64), 'cur_cost': 112.0, 'intermediate_solutions': [{'tour': array([9, 3, 4, 7, 8, 2, 0, 5, 6, 1]), 'cur_cost': 106.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 9, 3, 4, 8, 2, 0, 5, 6, 1]), 'cur_cost': 105.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 7, 9, 3, 4, 2, 0, 5, 6, 1]), 'cur_cost': 114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 7, 9, 3, 8, 2, 0, 5, 6, 1]), 'cur_cost': 115.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 8, 7, 9, 3, 2, 0, 5, 6, 1]), 'cur_cost': 106.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 5, 0, 4, 1, 3, 8, 9, 2, 7], dtype=int64), 'cur_cost': 108.0, 'intermediate_solutions': [{'tour': array([5, 8, 3, 2, 9, 6, 4, 7, 1, 0]), 'cur_cost': 106.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 5, 8, 3, 9, 6, 4, 7, 1, 0]), 'cur_cost': 116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 2, 5, 8, 3, 6, 4, 7, 1, 0]), 'cur_cost': 116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 2, 5, 8, 9, 6, 4, 7, 1, 0]), 'cur_cost': 113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 9, 2, 5, 8, 6, 4, 7, 1, 0]), 'cur_cost': 131.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 8, 6, 7, 5, 3, 4, 9, 2], 'cur_cost': 125.0, 'intermediate_solutions': [{'tour': [5, 7, 8, 2, 9, 4, 3, 1, 0, 6], 'cur_cost': 112.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 3, 4, 2, 9, 8, 7, 5, 0, 6], 'cur_cost': 104.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 2, 7, 8, 9, 4, 3, 1, 0, 6], 'cur_cost': 99.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [7, 2, 1, 6, 3, 0, 9, 5, 4, 8], 'cur_cost': 86.0, 'intermediate_solutions': [{'tour': [3, 5, 8, 6, 1, 4, 2, 0, 9, 7], 'cur_cost': 118.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 2, 6, 9, 0, 8, 4, 1, 7], 'cur_cost': 108.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 5, 2, 8, 6, 1, 4, 0, 9, 7], 'cur_cost': 121.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [6, 9, 3, 5, 4, 7, 2, 1, 0, 8], 'cur_cost': 113.0, 'intermediate_solutions': [{'tour': [1, 2, 4, 3, 7, 8, 0, 6, 5, 9], 'cur_cost': 99.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 4, 3, 7, 8, 1, 5, 6, 9], 'cur_cost': 108.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 0, 2, 4, 3, 8, 1, 6, 5, 9], 'cur_cost': 109.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 8, 7, 2, 0, 1, 3, 5, 9, 6], dtype=int64), 'cur_cost': 97.0, 'intermediate_solutions': [{'tour': array([6, 2, 5, 0, 8, 1, 3, 7, 9, 4]), 'cur_cost': 126.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 6, 2, 5, 8, 1, 3, 7, 9, 4]), 'cur_cost': 121.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 0, 6, 2, 5, 1, 3, 7, 9, 4]), 'cur_cost': 113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 0, 6, 2, 8, 1, 3, 7, 9, 4]), 'cur_cost': 113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 8, 0, 6, 2, 1, 3, 7, 9, 4]), 'cur_cost': 113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [4, 3, 5, 1, 2, 7, 0, 6, 9, 8], 'cur_cost': 95.0, 'intermediate_solutions': [{'tour': [3, 1, 6, 5, 2, 7, 0, 4, 8, 9], 'cur_cost': 105.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 1, 6, 0, 2, 7, 5, 9, 8, 4], 'cur_cost': 87.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 1, 6, 0, 2, 7, 8, 5, 4, 9], 'cur_cost': 100.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [8, 3, 0, 7, 5, 9, 6, 4, 1, 2], 'cur_cost': 117.0, 'intermediate_solutions': [{'tour': [4, 0, 1, 5, 6, 3, 9, 7, 8, 2], 'cur_cost': 124.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 0, 1, 5, 6, 3, 2, 7, 9, 8], 'cur_cost': 104.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 1, 5, 6, 3, 2, 8, 7, 9], 'cur_cost': 117.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 2, 3, 5, 9, 8, 7, 6, 0], 'cur_cost': 98.0, 'intermediate_solutions': [{'tour': [6, 2, 9, 1, 7, 5, 4, 0, 8, 3], 'cur_cost': 123.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 0, 9, 3, 8, 2, 4, 5, 7, 1], 'cur_cost': 103.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 9, 1, 7, 5, 4, 2, 8, 3], 'cur_cost': 110.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:41,368 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:41,368 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:41,369 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=86.000, 多样性=0.916
2025-08-05 10:28:41,369 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 10:28:41,369 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 10:28:41,369 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:41,370 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.03169801842646528, 'best_improvement': 0.10416666666666667}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.014354066985646024}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.04540922682053661, 'recent_improvements': [0.023928953940823238, -0.004316350188162851, -0.06688949970024997], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 72.0, 'new_best_cost': 72.0, 'quality_improvement': 0.0, 'old_diversity': 0.6666666666666667, 'new_diversity': 0.6666666666666667, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:41,370 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 10:28:41,370 - __main__ - INFO - geometry3_10 开始进化第 5 代
2025-08-05 10:28:41,370 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 10:28:41,370 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:41,371 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=86.000, 多样性=0.916
2025-08-05 10:28:41,371 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:41,371 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.916
2025-08-05 10:28:41,371 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:41,372 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.667
2025-08-05 10:28:41,373 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 10:28:41,373 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:41,374 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 10:28:41,374 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 10:28:41,382 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.357, 适应度梯度: 0.900, 聚类评分: 0.000, 覆盖率: 0.053, 收敛趋势: 0.000, 多样性: 0.638
2025-08-05 10:28:41,382 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 10:28:41,382 - LandscapeExpert - INFO - 提取到 4 个精英解
2025-08-05 10:28:41,382 - visualization.landscape_visualizer - INFO - 设置当前实例名: geometry3_10
2025-08-05 10:28:41,386 - visualization.landscape_visualizer - INFO - 插值约束: 92 个点被约束到最小值 72.00
2025-08-05 10:28:41,388 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=8.7%, 梯度: 1.94 → 1.78
2025-08-05 10:28:41,480 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry3_10\landscape_geometry3_10_iter_45_20250805_102841.html
2025-08-05 10:28:41,528 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry3_10\dashboard_geometry3_10_iter_45_20250805_102841.html
2025-08-05 10:28:41,528 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 45
2025-08-05 10:28:41,528 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 10:28:41,528 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1550秒
2025-08-05 10:28:41,528 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.35714285714285715, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 0.8999999999999994, 'local_optima_density': 0.35714285714285715, 'gradient_variance': 288.92428571428565, 'cluster_count': 0}, 'population_state': {'diversity': 0.6381475667189953, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0532, 'fitness_entropy': 0.929906376575379, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.053)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 0.900)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360921.3825984, 'performance_metrics': {}}}
2025-08-05 10:28:41,528 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:41,529 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:41,529 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:41,529 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:41,529 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 10:28:41,529 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 10:28:41,529 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 10:28:41,530 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:41,530 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:41,530 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 10:28:41,530 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:41,530 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:41,531 - experts.management.collaboration_manager - INFO - 识别精英个体: {4, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:41,531 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:41,531 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:41,531 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:41,532 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:41,532 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,532 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,532 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,532 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,532 - ExplorationExpert - INFO - 探索路径生成完成，成本: 91.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:41,533 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [2, 0, 1, 6, 3, 4, 8, 5, 9, 7], 'cur_cost': 91.0, 'intermediate_solutions': [{'tour': [0, 7, 6, 5, 4, 3, 2, 1, 9, 8], 'cur_cost': 108.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 0, 5, 9, 1, 2, 3, 4, 8], 'cur_cost': 97.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 7, 0, 3, 5, 4, 2, 1, 9, 8], 'cur_cost': 122.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:41,533 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 91.00)
2025-08-05 10:28:41,533 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:41,533 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:41,533 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:41,533 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:41,534 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,534 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,534 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,534 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,534 - ExplorationExpert - INFO - 探索路径生成完成，成本: 85.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:41,534 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [1, 7, 4, 8, 3, 2, 0, 5, 9, 6], 'cur_cost': 85.0, 'intermediate_solutions': [{'tour': [4, 7, 3, 8, 2, 9, 6, 0, 5, 1], 'cur_cost': 107.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 7, 3, 8, 5, 0, 6, 4, 2, 1], 'cur_cost': 109.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 9, 3, 8, 2, 4, 6, 0, 5, 1], 'cur_cost': 117.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:41,534 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 85.00)
2025-08-05 10:28:41,535 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:41,535 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:41,535 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:41,535 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:41,536 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,536 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,536 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,536 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,536 - ExplorationExpert - INFO - 探索路径生成完成，成本: 107.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:41,536 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [8, 2, 7, 3, 0, 1, 5, 9, 4, 6], 'cur_cost': 107.0, 'intermediate_solutions': [{'tour': [6, 5, 0, 3, 1, 4, 8, 9, 2, 7], 'cur_cost': 106.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 5, 4, 0, 1, 3, 8, 9, 2, 7], 'cur_cost': 104.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 4, 5, 1, 3, 8, 9, 2, 7], 'cur_cost': 104.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:41,537 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 107.00)
2025-08-05 10:28:41,537 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:28:41,537 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:41,537 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:41,537 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 111.0
2025-08-05 10:28:41,543 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:41,543 - ExploitationExpert - INFO - res_population_costs: [72.0, 72, 72, 72]
2025-08-05 10:28:41,543 - ExploitationExpert - INFO - res_populations: [array([0, 9, 5, 4, 8, 3, 2, 7, 6, 1], dtype=int64), array([0, 9, 5, 4, 8, 3, 7, 2, 1, 6], dtype=int64), array([0, 5, 9, 8, 4, 3, 7, 2, 1, 6], dtype=int64), array([0, 1, 6, 7, 2, 3, 4, 8, 9, 5], dtype=int64)]
2025-08-05 10:28:41,544 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:41,544 - ExploitationExpert - INFO - populations: [{'tour': [2, 0, 1, 6, 3, 4, 8, 5, 9, 7], 'cur_cost': 91.0}, {'tour': [1, 7, 4, 8, 3, 2, 0, 5, 9, 6], 'cur_cost': 85.0}, {'tour': [8, 2, 7, 3, 0, 1, 5, 9, 4, 6], 'cur_cost': 107.0}, {'tour': array([2, 0, 9, 3, 5, 8, 7, 4, 1, 6], dtype=int64), 'cur_cost': 111.0}, {'tour': [7, 2, 1, 6, 3, 0, 9, 5, 4, 8], 'cur_cost': 86.0}, {'tour': [6, 9, 3, 5, 4, 7, 2, 1, 0, 8], 'cur_cost': 113.0}, {'tour': [4, 8, 7, 2, 0, 1, 3, 5, 9, 6], 'cur_cost': 97.0}, {'tour': [4, 3, 5, 1, 2, 7, 0, 6, 9, 8], 'cur_cost': 95.0}, {'tour': [8, 3, 0, 7, 5, 9, 6, 4, 1, 2], 'cur_cost': 117.0}, {'tour': [1, 4, 2, 3, 5, 9, 8, 7, 6, 0], 'cur_cost': 98.0}]
2025-08-05 10:28:41,545 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:41,545 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 115, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 115, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:41,545 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([2, 0, 9, 3, 5, 8, 7, 4, 1, 6], dtype=int64), 'cur_cost': 111.0, 'intermediate_solutions': [{'tour': array([8, 1, 0, 6, 7, 5, 3, 4, 9, 2]), 'cur_cost': 115.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 8, 1, 0, 7, 5, 3, 4, 9, 2]), 'cur_cost': 127.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 6, 8, 1, 0, 5, 3, 4, 9, 2]), 'cur_cost': 110.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 6, 8, 1, 7, 5, 3, 4, 9, 2]), 'cur_cost': 126.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 7, 6, 8, 1, 5, 3, 4, 9, 2]), 'cur_cost': 128.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:41,545 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 111.00)
2025-08-05 10:28:41,546 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:41,546 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:41,546 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:41,546 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:41,546 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,546 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,547 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,547 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,547 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:41,547 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [2, 6, 7, 1, 3, 4, 5, 0, 9, 8], 'cur_cost': 101.0, 'intermediate_solutions': [{'tour': [7, 4, 1, 6, 3, 0, 9, 5, 2, 8], 'cur_cost': 108.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 2, 1, 6, 5, 9, 0, 3, 4, 8], 'cur_cost': 82.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 6, 2, 1, 3, 0, 9, 5, 4, 8], 'cur_cost': 96.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:41,547 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 101.00)
2025-08-05 10:28:41,547 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:28:41,547 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:41,547 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:41,548 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 110.0
2025-08-05 10:28:41,553 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:41,553 - ExploitationExpert - INFO - res_population_costs: [72.0, 72, 72, 72]
2025-08-05 10:28:41,553 - ExploitationExpert - INFO - res_populations: [array([0, 9, 5, 4, 8, 3, 2, 7, 6, 1], dtype=int64), array([0, 9, 5, 4, 8, 3, 7, 2, 1, 6], dtype=int64), array([0, 5, 9, 8, 4, 3, 7, 2, 1, 6], dtype=int64), array([0, 1, 6, 7, 2, 3, 4, 8, 9, 5], dtype=int64)]
2025-08-05 10:28:41,554 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:41,554 - ExploitationExpert - INFO - populations: [{'tour': [2, 0, 1, 6, 3, 4, 8, 5, 9, 7], 'cur_cost': 91.0}, {'tour': [1, 7, 4, 8, 3, 2, 0, 5, 9, 6], 'cur_cost': 85.0}, {'tour': [8, 2, 7, 3, 0, 1, 5, 9, 4, 6], 'cur_cost': 107.0}, {'tour': array([2, 0, 9, 3, 5, 8, 7, 4, 1, 6], dtype=int64), 'cur_cost': 111.0}, {'tour': [2, 6, 7, 1, 3, 4, 5, 0, 9, 8], 'cur_cost': 101.0}, {'tour': array([5, 2, 8, 7, 4, 1, 6, 0, 9, 3], dtype=int64), 'cur_cost': 110.0}, {'tour': [4, 8, 7, 2, 0, 1, 3, 5, 9, 6], 'cur_cost': 97.0}, {'tour': [4, 3, 5, 1, 2, 7, 0, 6, 9, 8], 'cur_cost': 95.0}, {'tour': [8, 3, 0, 7, 5, 9, 6, 4, 1, 2], 'cur_cost': 117.0}, {'tour': [1, 4, 2, 3, 5, 9, 8, 7, 6, 0], 'cur_cost': 98.0}]
2025-08-05 10:28:41,555 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:41,555 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 116, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 116, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:41,556 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([5, 2, 8, 7, 4, 1, 6, 0, 9, 3], dtype=int64), 'cur_cost': 110.0, 'intermediate_solutions': [{'tour': array([3, 9, 6, 5, 4, 7, 2, 1, 0, 8]), 'cur_cost': 103.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 3, 9, 6, 4, 7, 2, 1, 0, 8]), 'cur_cost': 112.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 5, 3, 9, 6, 7, 2, 1, 0, 8]), 'cur_cost': 99.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 5, 3, 9, 4, 7, 2, 1, 0, 8]), 'cur_cost': 116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 4, 5, 3, 9, 7, 2, 1, 0, 8]), 'cur_cost': 123.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:41,556 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 110.00)
2025-08-05 10:28:41,556 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:41,556 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:41,556 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:41,556 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:41,556 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,556 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,556 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,557 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,557 - ExplorationExpert - INFO - 探索路径生成完成，成本: 108.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:41,557 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [6, 8, 1, 9, 5, 0, 2, 3, 4, 7], 'cur_cost': 108.0, 'intermediate_solutions': [{'tour': [4, 9, 7, 2, 0, 1, 3, 5, 8, 6], 'cur_cost': 123.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 7, 2, 0, 1, 6, 9, 5, 3], 'cur_cost': 82.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 8, 7, 2, 0, 3, 1, 5, 9, 6], 'cur_cost': 102.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:41,557 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 108.00)
2025-08-05 10:28:41,558 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:41,558 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:41,558 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:41,558 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:41,558 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,558 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,558 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,559 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,559 - ExplorationExpert - INFO - 探索路径生成完成，成本: 72.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:41,559 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [2, 1, 6, 0, 9, 5, 4, 8, 3, 7], 'cur_cost': 72.0, 'intermediate_solutions': [{'tour': [4, 3, 5, 1, 2, 7, 8, 6, 9, 0], 'cur_cost': 106.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 3, 5, 6, 0, 7, 2, 1, 9, 8], 'cur_cost': 97.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 3, 5, 1, 7, 0, 6, 2, 9, 8], 'cur_cost': 107.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:41,559 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 72.00)
2025-08-05 10:28:41,559 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:41,559 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:41,559 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:41,560 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 119.0
2025-08-05 10:28:41,565 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:41,565 - ExploitationExpert - INFO - res_population_costs: [72.0, 72, 72, 72]
2025-08-05 10:28:41,566 - ExploitationExpert - INFO - res_populations: [array([0, 9, 5, 4, 8, 3, 2, 7, 6, 1], dtype=int64), array([0, 9, 5, 4, 8, 3, 7, 2, 1, 6], dtype=int64), array([0, 5, 9, 8, 4, 3, 7, 2, 1, 6], dtype=int64), array([0, 1, 6, 7, 2, 3, 4, 8, 9, 5], dtype=int64)]
2025-08-05 10:28:41,567 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:41,567 - ExploitationExpert - INFO - populations: [{'tour': [2, 0, 1, 6, 3, 4, 8, 5, 9, 7], 'cur_cost': 91.0}, {'tour': [1, 7, 4, 8, 3, 2, 0, 5, 9, 6], 'cur_cost': 85.0}, {'tour': [8, 2, 7, 3, 0, 1, 5, 9, 4, 6], 'cur_cost': 107.0}, {'tour': array([2, 0, 9, 3, 5, 8, 7, 4, 1, 6], dtype=int64), 'cur_cost': 111.0}, {'tour': [2, 6, 7, 1, 3, 4, 5, 0, 9, 8], 'cur_cost': 101.0}, {'tour': array([5, 2, 8, 7, 4, 1, 6, 0, 9, 3], dtype=int64), 'cur_cost': 110.0}, {'tour': [6, 8, 1, 9, 5, 0, 2, 3, 4, 7], 'cur_cost': 108.0}, {'tour': [2, 1, 6, 0, 9, 5, 4, 8, 3, 7], 'cur_cost': 72.0}, {'tour': array([1, 5, 7, 9, 3, 2, 0, 4, 8, 6], dtype=int64), 'cur_cost': 119.0}, {'tour': [1, 4, 2, 3, 5, 9, 8, 7, 6, 0], 'cur_cost': 98.0}]
2025-08-05 10:28:41,567 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:41,568 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 117, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 117, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:41,568 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([1, 5, 7, 9, 3, 2, 0, 4, 8, 6], dtype=int64), 'cur_cost': 119.0, 'intermediate_solutions': [{'tour': array([0, 3, 8, 7, 5, 9, 6, 4, 1, 2]), 'cur_cost': 114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 0, 3, 8, 5, 9, 6, 4, 1, 2]), 'cur_cost': 106.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 7, 0, 3, 8, 9, 6, 4, 1, 2]), 'cur_cost': 124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 7, 0, 3, 5, 9, 6, 4, 1, 2]), 'cur_cost': 117.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 5, 7, 0, 3, 9, 6, 4, 1, 2]), 'cur_cost': 129.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:41,568 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 119.00)
2025-08-05 10:28:41,568 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:41,569 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:41,569 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:41,570 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:41,570 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,570 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,570 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,571 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,571 - ExplorationExpert - INFO - 探索路径生成完成，成本: 96.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:41,571 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [9, 0, 1, 5, 4, 3, 2, 7, 6, 8], 'cur_cost': 96.0, 'intermediate_solutions': [{'tour': [4, 1, 2, 3, 5, 9, 8, 7, 6, 0], 'cur_cost': 101.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 7, 8, 9, 5, 3, 2, 4, 1], 'cur_cost': 98.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 2, 3, 5, 7, 9, 8, 6, 0], 'cur_cost': 122.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:41,572 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 96.00)
2025-08-05 10:28:41,572 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:41,572 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:41,573 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [2, 0, 1, 6, 3, 4, 8, 5, 9, 7], 'cur_cost': 91.0, 'intermediate_solutions': [{'tour': [0, 7, 6, 5, 4, 3, 2, 1, 9, 8], 'cur_cost': 108.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 0, 5, 9, 1, 2, 3, 4, 8], 'cur_cost': 97.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 7, 0, 3, 5, 4, 2, 1, 9, 8], 'cur_cost': 122.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 4, 8, 3, 2, 0, 5, 9, 6], 'cur_cost': 85.0, 'intermediate_solutions': [{'tour': [4, 7, 3, 8, 2, 9, 6, 0, 5, 1], 'cur_cost': 107.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 7, 3, 8, 5, 0, 6, 4, 2, 1], 'cur_cost': 109.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 9, 3, 8, 2, 4, 6, 0, 5, 1], 'cur_cost': 117.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [8, 2, 7, 3, 0, 1, 5, 9, 4, 6], 'cur_cost': 107.0, 'intermediate_solutions': [{'tour': [6, 5, 0, 3, 1, 4, 8, 9, 2, 7], 'cur_cost': 106.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 5, 4, 0, 1, 3, 8, 9, 2, 7], 'cur_cost': 104.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 4, 5, 1, 3, 8, 9, 2, 7], 'cur_cost': 104.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 0, 9, 3, 5, 8, 7, 4, 1, 6], dtype=int64), 'cur_cost': 111.0, 'intermediate_solutions': [{'tour': array([8, 1, 0, 6, 7, 5, 3, 4, 9, 2]), 'cur_cost': 115.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 8, 1, 0, 7, 5, 3, 4, 9, 2]), 'cur_cost': 127.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 6, 8, 1, 0, 5, 3, 4, 9, 2]), 'cur_cost': 110.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 6, 8, 1, 7, 5, 3, 4, 9, 2]), 'cur_cost': 126.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 7, 6, 8, 1, 5, 3, 4, 9, 2]), 'cur_cost': 128.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [2, 6, 7, 1, 3, 4, 5, 0, 9, 8], 'cur_cost': 101.0, 'intermediate_solutions': [{'tour': [7, 4, 1, 6, 3, 0, 9, 5, 2, 8], 'cur_cost': 108.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 2, 1, 6, 5, 9, 0, 3, 4, 8], 'cur_cost': 82.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 6, 2, 1, 3, 0, 9, 5, 4, 8], 'cur_cost': 96.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([5, 2, 8, 7, 4, 1, 6, 0, 9, 3], dtype=int64), 'cur_cost': 110.0, 'intermediate_solutions': [{'tour': array([3, 9, 6, 5, 4, 7, 2, 1, 0, 8]), 'cur_cost': 103.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 3, 9, 6, 4, 7, 2, 1, 0, 8]), 'cur_cost': 112.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 5, 3, 9, 6, 7, 2, 1, 0, 8]), 'cur_cost': 99.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 5, 3, 9, 4, 7, 2, 1, 0, 8]), 'cur_cost': 116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 4, 5, 3, 9, 7, 2, 1, 0, 8]), 'cur_cost': 123.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [6, 8, 1, 9, 5, 0, 2, 3, 4, 7], 'cur_cost': 108.0, 'intermediate_solutions': [{'tour': [4, 9, 7, 2, 0, 1, 3, 5, 8, 6], 'cur_cost': 123.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 7, 2, 0, 1, 6, 9, 5, 3], 'cur_cost': 82.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 8, 7, 2, 0, 3, 1, 5, 9, 6], 'cur_cost': 102.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [2, 1, 6, 0, 9, 5, 4, 8, 3, 7], 'cur_cost': 72.0, 'intermediate_solutions': [{'tour': [4, 3, 5, 1, 2, 7, 8, 6, 9, 0], 'cur_cost': 106.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 3, 5, 6, 0, 7, 2, 1, 9, 8], 'cur_cost': 97.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 3, 5, 1, 7, 0, 6, 2, 9, 8], 'cur_cost': 107.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([1, 5, 7, 9, 3, 2, 0, 4, 8, 6], dtype=int64), 'cur_cost': 119.0, 'intermediate_solutions': [{'tour': array([0, 3, 8, 7, 5, 9, 6, 4, 1, 2]), 'cur_cost': 114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 0, 3, 8, 5, 9, 6, 4, 1, 2]), 'cur_cost': 106.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 7, 0, 3, 8, 9, 6, 4, 1, 2]), 'cur_cost': 124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 7, 0, 3, 5, 9, 6, 4, 1, 2]), 'cur_cost': 117.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 5, 7, 0, 3, 9, 6, 4, 1, 2]), 'cur_cost': 129.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [9, 0, 1, 5, 4, 3, 2, 7, 6, 8], 'cur_cost': 96.0, 'intermediate_solutions': [{'tour': [4, 1, 2, 3, 5, 9, 8, 7, 6, 0], 'cur_cost': 101.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 7, 8, 9, 5, 3, 2, 4, 1], 'cur_cost': 98.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 2, 3, 5, 7, 9, 8, 6, 0], 'cur_cost': 122.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:41,573 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:41,574 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:41,575 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=72.000, 多样性=0.882
2025-08-05 10:28:41,576 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 10:28:41,576 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 10:28:41,576 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:41,576 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.0885370378511452, 'best_improvement': 0.16279069767441862}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.03640776699029125}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.018007184307314066, 'recent_improvements': [-0.004316350188162851, -0.06688949970024997, 0.03169801842646528], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 72.0, 'new_best_cost': 72.0, 'quality_improvement': 0.0, 'old_diversity': 0.6666666666666667, 'new_diversity': 0.6666666666666667, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:41,576 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 10:28:41,578 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\geometry3_10_solution.json
2025-08-05 10:28:41,578 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\geometry3_10_20250805_102841.solution
2025-08-05 10:28:41,578 - __main__ - INFO - 实例执行完成 - 运行时间: 1.30s, 最佳成本: 72.0
2025-08-05 10:28:41,578 - __main__ - INFO - 实例 geometry3_10 处理完成
