"""
Structured logging system for the intelligent strategy selection system.

This module provides structured logging capabilities with support for
JSON formatting, contextual information, and performance tracking.
"""

import logging
import json
import time
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
from pathlib import Path


@dataclass
class LogEntry:
    """Structured log entry."""
    timestamp: str
    level: str
    module: str
    function: str
    message: str
    context: Dict[str, Any]
    execution_time: Optional[float] = None
    memory_usage: Optional[float] = None


class StructuredLogger:
    """
    Structured logger for the intelligent strategy system.
    
    Provides structured logging with JSON formatting, contextual information,
    and performance tracking capabilities.
    """
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the structured logger.
        
        Args:
            name: Logger name
            config: Optional configuration dictionary
        """
        self.name = name
        self.config = config or {}
        self.logger = logging.getLogger(name)
        
        # Configure logger
        self._configure_logger()
        
        # Context stack for nested operations
        self.context_stack = []
        
    def _configure_logger(self):
        """Configure the underlying logger."""
        # Set log level
        log_level = self.config.get('log_level', 'INFO')
        self.logger.setLevel(getattr(logging, log_level))
        
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Console handler
        if self.config.get('log_to_console', True):
            console_handler = logging.StreamHandler()
            console_formatter = self._create_formatter('console')
            console_handler.setFormatter(console_formatter)
            self.logger.addHandler(console_handler)
        
        # File handler
        if self.config.get('log_to_file', False):
            log_file = self.config.get('log_file_path', 'logs/intelligent_strategy.log')
            Path(log_file).parent.mkdir(parents=True, exist_ok=True)
            
            file_handler = logging.FileHandler(log_file)
            file_formatter = self._create_formatter('file')
            file_handler.setFormatter(file_formatter)
            self.logger.addHandler(file_handler)
    
    def _create_formatter(self, handler_type: str) -> logging.Formatter:
        """Create a formatter for the specified handler type."""
        if self.config.get('log_format') == 'json':
            return JsonFormatter()
        else:
            format_str = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            return logging.Formatter(format_str)
    
    def _create_log_entry(self, level: str, message: str, 
                         context: Optional[Dict[str, Any]] = None,
                         execution_time: Optional[float] = None) -> LogEntry:
        """Create a structured log entry."""
        import inspect
        
        # Get caller information
        frame = inspect.currentframe().f_back.f_back
        module = frame.f_globals.get('__name__', 'unknown')
        function = frame.f_code.co_name
        
        # Merge context with stack
        merged_context = {}
        for ctx in self.context_stack:
            merged_context.update(ctx)
        if context:
            merged_context.update(context)
        
        return LogEntry(
            timestamp=datetime.now().isoformat(),
            level=level,
            module=module,
            function=function,
            message=message,
            context=merged_context,
            execution_time=execution_time
        )
    
    def debug(self, message: str, context: Optional[Dict[str, Any]] = None):
        """Log debug message."""
        entry = self._create_log_entry('DEBUG', message, context)
        self.logger.debug(self._format_message(entry))
    
    def info(self, message: str, context: Optional[Dict[str, Any]] = None):
        """Log info message."""
        entry = self._create_log_entry('INFO', message, context)
        self.logger.info(self._format_message(entry))
    
    def warning(self, message: str, context: Optional[Dict[str, Any]] = None):
        """Log warning message."""
        entry = self._create_log_entry('WARNING', message, context)
        self.logger.warning(self._format_message(entry))
    
    def error(self, message: str, context: Optional[Dict[str, Any]] = None):
        """Log error message."""
        entry = self._create_log_entry('ERROR', message, context)
        self.logger.error(self._format_message(entry))
    
    def critical(self, message: str, context: Optional[Dict[str, Any]] = None):
        """Log critical message."""
        entry = self._create_log_entry('CRITICAL', message, context)
        self.logger.critical(self._format_message(entry))
    
    def _format_message(self, entry: LogEntry) -> str:
        """Format log entry for output."""
        if self.config.get('log_format') == 'json':
            return json.dumps(asdict(entry), default=str)
        else:
            context_str = f" | Context: {entry.context}" if entry.context else ""
            exec_time_str = f" | Time: {entry.execution_time:.3f}s" if entry.execution_time else ""
            return f"{entry.message}{context_str}{exec_time_str}"
    
    def push_context(self, context: Dict[str, Any]):
        """Push context onto the context stack."""
        self.context_stack.append(context)
    
    def pop_context(self):
        """Pop context from the context stack."""
        if self.context_stack:
            return self.context_stack.pop()
        return None
    
    def log_execution_time(self, operation: str, execution_time: float, 
                          context: Optional[Dict[str, Any]] = None):
        """Log execution time for an operation."""
        ctx = context or {}
        ctx['operation'] = operation
        entry = self._create_log_entry('INFO', f"Operation completed: {operation}", 
                                     ctx, execution_time)
        self.logger.info(self._format_message(entry))
    
    def log_strategy_selection(self, individual_id: int, strategy_type: str, 
                             confidence: float, reasoning: str):
        """Log strategy selection decision."""
        context = {
            'individual_id': individual_id,
            'strategy_type': strategy_type,
            'confidence': confidence,
            'reasoning': reasoning
        }
        self.info("Strategy selected", context)
    
    def log_strategy_execution(self, individual_id: int, strategy_type: str, 
                             success: bool, execution_time: float, 
                             improvement: Optional[float] = None):
        """Log strategy execution result."""
        context = {
            'individual_id': individual_id,
            'strategy_type': strategy_type,
            'success': success,
            'improvement': improvement
        }
        message = f"Strategy execution {'succeeded' if success else 'failed'}"
        entry = self._create_log_entry('INFO', message, context, execution_time)
        self.logger.info(self._format_message(entry))
    
    def log_llm_interaction(self, provider: str, model: str, tokens_used: int, 
                           response_time: float, success: bool):
        """Log LLM interaction."""
        context = {
            'provider': provider,
            'model': model,
            'tokens_used': tokens_used,
            'success': success
        }
        message = f"LLM interaction {'succeeded' if success else 'failed'}"
        entry = self._create_log_entry('INFO', message, context, response_time)
        self.logger.info(self._format_message(entry))


class JsonFormatter(logging.Formatter):
    """JSON formatter for structured logging."""
    
    def format(self, record):
        """Format log record as JSON."""
        log_data = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'message': record.getMessage()
        }
        
        # Add extra fields if present
        if hasattr(record, 'context'):
            log_data['context'] = record.context
        if hasattr(record, 'execution_time'):
            log_data['execution_time'] = record.execution_time
        
        return json.dumps(log_data, default=str)


class TimedOperation:
    """Context manager for timing operations."""
    
    def __init__(self, logger: StructuredLogger, operation: str, 
                 context: Optional[Dict[str, Any]] = None):
        """
        Initialize timed operation.
        
        Args:
            logger: Structured logger instance
            operation: Operation name
            context: Optional context dictionary
        """
        self.logger = logger
        self.operation = operation
        self.context = context or {}
        self.start_time = None
    
    def __enter__(self):
        """Start timing."""
        self.start_time = time.time()
        self.logger.push_context({'operation': self.operation})
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """End timing and log result."""
        execution_time = time.time() - self.start_time
        self.logger.pop_context()
        
        if exc_type is None:
            self.logger.log_execution_time(self.operation, execution_time, self.context)
        else:
            error_context = self.context.copy()
            error_context.update({
                'error_type': exc_type.__name__,
                'error_message': str(exc_val)
            })
            self.logger.error(f"Operation failed: {self.operation}", error_context)


# Global logger instance
_global_logger = None

def get_logger(name: str = 'intelligent_strategy', 
               config: Optional[Dict[str, Any]] = None) -> StructuredLogger:
    """Get or create a structured logger instance."""
    global _global_logger
    if _global_logger is None:
        _global_logger = StructuredLogger(name, config)
    return _global_logger
