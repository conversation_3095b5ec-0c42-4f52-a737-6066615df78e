# -*- coding: utf-8 -*-
import numpy as np
import os
import pickle as pkl
import logging

# 获取模块的日志记录器
logger = logging.getLogger(__name__)

class LoadInstace:
    def __init__(self, func_name, input_path, opt_costs=None):
        tsp_cords = []
        # 确保文件路径正确，并且目录存在
        path_tsp = os.path.join(input_path, func_name) + ".tsp"
        
        # 检查文件是否存在
        if not os.path.exists(path_tsp):
            raise FileNotFoundError(f"TSP文件不存在: {path_tsp}")
            
        with open(path_tsp, 'r', encoding="utf-8") as f:
            tsp_cord = f.readlines()
        for line in tsp_cord:
            tsp_cords.append(line.split())

        self.tsp_cords = tsp_cords
        self.func_name = func_name
        self.input_path = input_path
        
        # 设置最优代价
        self.given_optimal_cost = None
        if opt_costs and func_name in opt_costs:
            self.given_optimal_cost = opt_costs[func_name]
     

    @staticmethod
    def calculate_distance_matrix(cities):
        """
        计算TSP的距离矩阵

        参数:
        cities (list of tuples): 每个元组包含城市的坐标 (x, y)

        返回:
        numpy.ndarray: 距离矩阵
        """
        num_cities = len(cities)
        distance_matrix = np.zeros((num_cities, num_cities))

        for i in range(num_cities):
            for j in range(i, num_cities):
                if i == j:
                    distance_matrix[i, j] = 0  # 城市到自身的距离为0
                else:
                    x1, y1 = cities[i]
                    x2, y2 = cities[j]
                    distance = np.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2) + 0.5  # 加0.5补误差
                    distance = int(distance)  # 距离矩阵只取整数
                    distance_matrix[i, j] = distance
                    distance_matrix[j, i] = distance  # 距离矩阵对称

        return distance_matrix

    @staticmethod
    def calculate_path_length(distance_matrix, path):
        """
        计算TSP问题路径长度

        参数:
        distance_matrix (numpy.ndarray): 距离矩阵
        path (list): 路径列表，表示访问城市的顺序

        返回:
        float: 路径长度
        """
        path = [int(city) for city in path]
        num_cities = len(path)
        path_length = 0

        for i in range(num_cities - 1):
            city_from = path[i]
            city_to = path[i + 1]
            path_length += distance_matrix[city_from, city_to]

        # 回到起点
        path_length += distance_matrix[path[-1], path[0]]

        return path_length

    def get_distance_matrix(self):
        tsps_int = [[int(elem) for elem in sublist] for sublist in self.tsp_cords]
        return self.calculate_distance_matrix(tsps_int)
    
    def get_given_optimal_cost(self):
        """获取实例的最优代价
        
        返回:
        int: 实例的最优代价，如果没有则返回None
        """
        return self.given_optimal_cost

    
    
    def get_tsp_cords(self):
        tsps_int = [[int(elem) for elem in sublist] for sublist in self.tsp_cords]
        return tsps_int


def load_all_instances(func_name, input_path, func_begin, func_end, output_path):
    """
    加载所有指定的TSP实例并返回instances_selected格式

    参数:
    func_name (list): 实例名称列表
    input_path (str): 输入路径
    func_begin (int): 起始索引
    func_end (int): 结束索引
    output_path (str): 输出路径

    返回:
    list: instances_selected格式的实例列表，每个元素包含func_name, coordinate, distance_matrix
    """
    import pickle as pkl

    opt_costs={"simple1_9":680, "simple2_10":1265, "simple3_10":832, "simple4_11":803, "simple5_12":754, "simple6_12":845,
               "geometry1_10":130, "geometry2_12":1344, "geometry3_10":72, "geometry4_10":72, "geometry5_10":78, "geometry6_15":130,
               "composite1_28":3055, "composite2_34":3575, "composite3_22":9455, "composite4_33":8761, "composite5_35":9061, "composite6_39":23763, "composite7_42":14408,
                "composite8_45":10973, "composite9_48":6767, "composite10_55":10442, "composite11_59":24451,"composite12_60":9614, "composite13_66":9521,
                "eil51":426, "berlin52":7542, "st70":675, "pr76":108159,  "kroA100":21282, "lin105":14379}

    # 确保输出目录存在
    os.makedirs(output_path, exist_ok=True)

    # 打印当前路径信息，帮助调试
    logger.info(f"当前工作目录: {os.getcwd()}")
    logger.info(f"输入路径: {input_path}")
    logger.info(f"输出路径: {output_path}")

    instances_selected = []

    try:
        # 检查输入目录是否存在
        if not os.path.exists(input_path):
            logger.warning(f"警告: 输入目录不存在: {input_path}")
            return instances_selected  # 返回空列表

        # 加载实例数据
        name = []
        coordinates = []
        distance_matrixs = []
        opt_cost = []

        for i in range(func_begin, func_end + 1):
            if i >= len(func_name) or func_name[i] == "":
                continue

            try:
                logger.info(f"尝试加载实例: {func_name[i]}, 路径: {input_path}")
                instance = LoadInstace(func_name[i], input_path, opt_costs)
                name.append(func_name[i])
                cities = instance.get_tsp_cords()
                coordinates.append(cities)
                distance_matrix = instance.calculate_distance_matrix(cities)
                distance_matrixs.append(distance_matrix)

                # 获取最优代价
                if instance.get_given_optimal_cost() is not None:
                    opt_cost.append(instance.get_given_optimal_cost())
                else:
                    # 如果没有最优代价，添加一个默认值或None
                    opt_cost.append(None)

            except Exception as e:
                logger.warning(f"警告: 加载实例 {func_name[i]} 时出错: {e}")
                continue

        # 保存原始格式的实例文件（保持向后兼容）
        instances = {
            "func_name": name,
            "coordinate": coordinates,
            "distance_matrix": distance_matrixs,
            "optimal_cost": opt_cost
        }

        output_file = os.path.join(output_path, "mmtsp_instances.pkl")
        with open(output_file, "wb") as f:
            pkl.dump(instances, f)

        # 转换为instances_selected格式
        for i in range(len(name)):
            instance_selected = {
                "func_name": name[i],
                "coordinate": coordinates[i],
                "distance_matrix": distance_matrixs[i],
            }
            instances_selected.append(instance_selected)

        if len(instances_selected) == 0:
            logger.warning("警告: 没有成功加载任何实例")
        else:
            logger.info(f"成功加载 {len(instances_selected)} 个实例")

    except Exception as e:
        logger.error(f"错误: 加载或处理实例时出错: {str(e)}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return []  # 返回空列表

    return instances_selected


# 预定义的TSP实例名称列表
DEFAULT_INSTANCES = [
    "simple1_9", "simple2_10", "simple3_10", "simple4_11", "simple5_12", "simple6_12",
    "geometry1_10", "geometry2_12", "geometry3_10", "geometry4_10", "geometry5_10", "geometry6_15",
    "composite1_28", "composite2_34", "composite3_22", "composite4_33", "composite5_35", "", "composite7_42", "composite8_45",
    "composite6_39", "composite8_45", "composite9_48", "composite10_55", "composite11_59", "composite13_66", "", "", "", "",  # 20 - 25
    "att48", "eil51", "berlin52", "st70", "eil76", "pr76", "eil101", "kroA100", "lin105", "kroA150", "lin318"
]