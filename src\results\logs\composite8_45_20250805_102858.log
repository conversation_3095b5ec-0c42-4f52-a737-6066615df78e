2025-08-05 10:28:58,149 - __main__ - INFO - composite8_45 开始进化第 1 代
2025-08-05 10:28:58,150 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 10:28:58,152 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:58,156 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=11318.000, 多样性=0.969
2025-08-05 10:28:58,161 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:58,168 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.969
2025-08-05 10:28:58,171 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:58,176 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 10:28:58,177 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:58,177 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 10:28:58,177 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 10:28:58,205 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.200, 适应度梯度: -8103.700, 聚类评分: 0.000, 覆盖率: 0.114, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:58,206 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 10:28:58,206 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 10:28:58,206 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite8_45
2025-08-05 10:28:58,212 - visualization.landscape_visualizer - INFO - 插值约束: 247 个点被约束到最小值 11318.00
2025-08-05 10:28:58,214 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=5.2%, 梯度: 3365.59 → 3191.67
2025-08-05 10:28:58,328 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite8_45\landscape_composite8_45_iter_96_20250805_102858.html
2025-08-05 10:28:58,394 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite8_45\dashboard_composite8_45_iter_96_20250805_102858.html
2025-08-05 10:28:58,394 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 96
2025-08-05 10:28:58,394 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 10:28:58,394 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2179秒
2025-08-05 10:28:58,395 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 192, 'max_size': 500, 'hits': 0, 'misses': 192, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 639, 'misses': 334, 'hit_rate': 0.656731757451182, 'evictions': 234, 'ttl': 7200}}
2025-08-05 10:28:58,395 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -8103.699999999997, 'local_optima_density': 0.2, 'gradient_variance': 1870835155.978, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1136, 'fitness_entropy': 0.9372305632161296, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -8103.700)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.114)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360938.20628, 'performance_metrics': {}}}
2025-08-05 10:28:58,395 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:58,395 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:58,395 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:58,395 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:58,397 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:28:58,397 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 10:28:58,397 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:28:58,397 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:58,397 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:58,397 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:28:58,397 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:58,397 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:58,398 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:58,398 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:58,398 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:58,398 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:58,400 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 45
2025-08-05 10:28:58,400 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,400 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15699.0, 路径长度: 45, 收集中间解: 0
2025-08-05 10:28:58,400 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 21, 9, 16, 18, 20, 14, 11, 13, 17, 19, 12, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 3, 5, 6, 4, 1, 2], 'cur_cost': 15699.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:58,400 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 15699.00)
2025-08-05 10:28:58,401 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:58,401 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:58,401 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:58,402 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 45
2025-08-05 10:28:58,403 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,403 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15540.0, 路径长度: 45, 收集中间解: 0
2025-08-05 10:28:58,403 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 14, 15, 3, 7, 9, 5, 6, 4, 1, 8, 2, 10, 17, 19, 18, 20, 21, 12, 16, 11, 13, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15540.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:58,403 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 15540.00)
2025-08-05 10:28:58,404 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:58,404 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:58,404 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:58,406 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 45
2025-08-05 10:28:58,406 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,406 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15378.0, 路径长度: 45, 收集中间解: 0
2025-08-05 10:28:58,406 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 17, 3, 1, 4, 6, 5, 9, 7, 8, 2, 10, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15378.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:58,406 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 15378.00)
2025-08-05 10:28:58,406 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:28:58,406 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:58,406 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:58,406 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 92096.0
2025-08-05 10:28:58,418 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 10:28:58,418 - ExploitationExpert - INFO - res_population_costs: [10979.0, 10979]
2025-08-05 10:28:58,418 - ExploitationExpert - INFO - res_populations: [array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 43, 33, 38, 39, 44, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 44, 39,
       38, 33, 43, 36, 35, 32, 30, 28, 24, 25, 22, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64)]
2025-08-05 10:28:58,419 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:58,419 - ExploitationExpert - INFO - populations: [{'tour': [0, 21, 9, 16, 18, 20, 14, 11, 13, 17, 19, 12, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 3, 5, 6, 4, 1, 2], 'cur_cost': 15699.0}, {'tour': [0, 14, 15, 3, 7, 9, 5, 6, 4, 1, 8, 2, 10, 17, 19, 18, 20, 21, 12, 16, 11, 13, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15540.0}, {'tour': [0, 17, 3, 1, 4, 6, 5, 9, 7, 8, 2, 10, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15378.0}, {'tour': array([14,  9, 37,  2,  4, 21, 28, 23, 32, 22, 17, 19,  8, 27,  1, 39, 11,
       13,  5, 38, 18, 25, 29, 44,  7, 12, 34, 31, 16,  0, 33, 26, 10, 40,
       41, 15, 35, 43, 24,  3, 30, 42, 20, 36,  6], dtype=int64), 'cur_cost': 92096.0}, {'tour': array([20,  2,  9,  1, 43,  6, 24,  4, 12, 29, 40, 23, 30, 42, 15, 39, 33,
       35, 19, 10,  8, 27, 22, 14, 44, 17, 11, 21, 13, 18,  0,  5, 41, 36,
        7, 16, 25, 28, 34, 32, 37, 38, 31,  3, 26], dtype=int64), 'cur_cost': 78863.0}, {'tour': array([28, 41, 16, 12, 14, 25,  1, 30, 35, 10, 34,  6, 21, 38, 17, 44,  5,
       15,  7, 32, 31, 23, 19,  9,  8, 18, 33,  2, 37, 39, 24, 22,  4, 13,
        0, 11, 40,  3, 36, 20, 27, 26, 43, 29, 42], dtype=int64), 'cur_cost': 93642.0}, {'tour': array([16, 32, 26,  8, 39, 12, 13, 33, 37, 25, 36,  3, 30,  6,  5, 24, 44,
       20,  2, 10, 34, 17,  1, 11,  9, 15, 40,  0, 19, 18, 41, 31, 43,  4,
       42, 38, 14, 27, 23, 22, 35, 28,  7, 29, 21], dtype=int64), 'cur_cost': 91400.0}, {'tour': array([33,  7, 35, 41, 32, 12, 31, 11, 44, 20,  3,  1, 37, 26, 14, 17, 29,
        0, 24, 27, 34,  6,  4, 43, 21, 36, 39, 10, 42, 23,  8, 38, 40,  5,
       16,  2, 30, 19,  9, 13, 28, 22, 18, 15, 25], dtype=int64), 'cur_cost': 93090.0}, {'tour': array([10,  1, 37, 26, 25, 12, 39,  7, 19, 38, 18, 43, 28, 30, 34, 20,  0,
       27, 31, 41,  2,  8, 40, 42, 16, 23, 17, 21, 44, 35, 36, 32, 13,  9,
       29,  4,  5, 11, 14, 22,  6,  3, 24, 15, 33], dtype=int64), 'cur_cost': 81799.0}, {'tour': array([25,  2, 20, 10, 31, 35, 34, 21,  3,  8, 29, 43, 16,  1, 23, 19, 39,
       24, 32,  0,  5, 30, 42, 22, 11, 41, 36, 37, 26,  7, 13, 15, 18,  4,
        9, 28, 17, 14, 38, 40,  6, 12, 27, 33, 44], dtype=int64), 'cur_cost': 87968.0}]
2025-08-05 10:28:58,423 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:58,423 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 248, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 248, 'cache_hits': 0, 'similarity_calculations': 1200, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:58,425 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([14,  9, 37,  2,  4, 21, 28, 23, 32, 22, 17, 19,  8, 27,  1, 39, 11,
       13,  5, 38, 18, 25, 29, 44,  7, 12, 34, 31, 16,  0, 33, 26, 10, 40,
       41, 15, 35, 43, 24,  3, 30, 42, 20, 36,  6], dtype=int64), 'cur_cost': 92096.0, 'intermediate_solutions': [{'tour': array([10, 42, 12,  4,  2, 18, 15, 27, 41, 30, 13, 17, 35, 32,  0, 40, 11,
       44, 29,  7, 20, 22, 16, 37, 25,  6, 23, 28, 24,  1, 39,  8, 26, 33,
       38,  5, 43, 19, 36, 31,  9,  3, 21, 34, 14], dtype=int64), 'cur_cost': 97462.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 4, 10, 42, 12,  2, 18, 15, 27, 41, 30, 13, 17, 35, 32,  0, 40, 11,
       44, 29,  7, 20, 22, 16, 37, 25,  6, 23, 28, 24,  1, 39,  8, 26, 33,
       38,  5, 43, 19, 36, 31,  9,  3, 21, 34, 14], dtype=int64), 'cur_cost': 97508.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 2,  4, 10, 42, 12, 18, 15, 27, 41, 30, 13, 17, 35, 32,  0, 40, 11,
       44, 29,  7, 20, 22, 16, 37, 25,  6, 23, 28, 24,  1, 39,  8, 26, 33,
       38,  5, 43, 19, 36, 31,  9,  3, 21, 34, 14], dtype=int64), 'cur_cost': 93390.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([12,  4, 10, 42,  2, 18, 15, 27, 41, 30, 13, 17, 35, 32,  0, 40, 11,
       44, 29,  7, 20, 22, 16, 37, 25,  6, 23, 28, 24,  1, 39,  8, 26, 33,
       38,  5, 43, 19, 36, 31,  9,  3, 21, 34, 14], dtype=int64), 'cur_cost': 97087.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([12,  2,  4, 10, 42, 18, 15, 27, 41, 30, 13, 17, 35, 32,  0, 40, 11,
       44, 29,  7, 20, 22, 16, 37, 25,  6, 23, 28, 24,  1, 39,  8, 26, 33,
       38,  5, 43, 19, 36, 31,  9,  3, 21, 34, 14], dtype=int64), 'cur_cost': 93418.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:58,426 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 92096.00)
2025-08-05 10:28:58,426 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:58,426 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:58,426 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:58,429 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 45
2025-08-05 10:28:58,429 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,429 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15591.0, 路径长度: 45, 收集中间解: 0
2025-08-05 10:28:58,429 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 8, 12, 3, 7, 9, 5, 6, 4, 1, 10, 2, 17, 19, 18, 20, 14, 11, 13, 15, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15591.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:58,430 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 15591.00)
2025-08-05 10:28:58,430 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:28:58,430 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:58,430 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:58,430 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 99317.0
2025-08-05 10:28:58,441 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 10:28:58,441 - ExploitationExpert - INFO - res_population_costs: [10979.0, 10979]
2025-08-05 10:28:58,441 - ExploitationExpert - INFO - res_populations: [array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 43, 33, 38, 39, 44, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 44, 39,
       38, 33, 43, 36, 35, 32, 30, 28, 24, 25, 22, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64)]
2025-08-05 10:28:58,442 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:58,442 - ExploitationExpert - INFO - populations: [{'tour': [0, 21, 9, 16, 18, 20, 14, 11, 13, 17, 19, 12, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 3, 5, 6, 4, 1, 2], 'cur_cost': 15699.0}, {'tour': [0, 14, 15, 3, 7, 9, 5, 6, 4, 1, 8, 2, 10, 17, 19, 18, 20, 21, 12, 16, 11, 13, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15540.0}, {'tour': [0, 17, 3, 1, 4, 6, 5, 9, 7, 8, 2, 10, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15378.0}, {'tour': array([14,  9, 37,  2,  4, 21, 28, 23, 32, 22, 17, 19,  8, 27,  1, 39, 11,
       13,  5, 38, 18, 25, 29, 44,  7, 12, 34, 31, 16,  0, 33, 26, 10, 40,
       41, 15, 35, 43, 24,  3, 30, 42, 20, 36,  6], dtype=int64), 'cur_cost': 92096.0}, {'tour': [0, 8, 12, 3, 7, 9, 5, 6, 4, 1, 10, 2, 17, 19, 18, 20, 14, 11, 13, 15, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15591.0}, {'tour': array([23,  9, 15, 14, 27, 10, 44, 29, 38, 12, 37, 39, 11, 40, 26,  4,  8,
        7, 21, 20, 31, 33, 34, 19, 25,  2, 32, 24,  3, 35, 18, 42, 28,  6,
       13, 36,  5, 30, 16,  0, 22, 17, 43,  1, 41], dtype=int64), 'cur_cost': 99317.0}, {'tour': array([16, 32, 26,  8, 39, 12, 13, 33, 37, 25, 36,  3, 30,  6,  5, 24, 44,
       20,  2, 10, 34, 17,  1, 11,  9, 15, 40,  0, 19, 18, 41, 31, 43,  4,
       42, 38, 14, 27, 23, 22, 35, 28,  7, 29, 21], dtype=int64), 'cur_cost': 91400.0}, {'tour': array([33,  7, 35, 41, 32, 12, 31, 11, 44, 20,  3,  1, 37, 26, 14, 17, 29,
        0, 24, 27, 34,  6,  4, 43, 21, 36, 39, 10, 42, 23,  8, 38, 40,  5,
       16,  2, 30, 19,  9, 13, 28, 22, 18, 15, 25], dtype=int64), 'cur_cost': 93090.0}, {'tour': array([10,  1, 37, 26, 25, 12, 39,  7, 19, 38, 18, 43, 28, 30, 34, 20,  0,
       27, 31, 41,  2,  8, 40, 42, 16, 23, 17, 21, 44, 35, 36, 32, 13,  9,
       29,  4,  5, 11, 14, 22,  6,  3, 24, 15, 33], dtype=int64), 'cur_cost': 81799.0}, {'tour': array([25,  2, 20, 10, 31, 35, 34, 21,  3,  8, 29, 43, 16,  1, 23, 19, 39,
       24, 32,  0,  5, 30, 42, 22, 11, 41, 36, 37, 26,  7, 13, 15, 18,  4,
        9, 28, 17, 14, 38, 40,  6, 12, 27, 33, 44], dtype=int64), 'cur_cost': 87968.0}]
2025-08-05 10:28:58,444 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:58,444 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 249, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 249, 'cache_hits': 0, 'similarity_calculations': 1201, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:58,445 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([23,  9, 15, 14, 27, 10, 44, 29, 38, 12, 37, 39, 11, 40, 26,  4,  8,
        7, 21, 20, 31, 33, 34, 19, 25,  2, 32, 24,  3, 35, 18, 42, 28,  6,
       13, 36,  5, 30, 16,  0, 22, 17, 43,  1, 41], dtype=int64), 'cur_cost': 99317.0, 'intermediate_solutions': [{'tour': array([16, 41, 28, 12, 14, 25,  1, 30, 35, 10, 34,  6, 21, 38, 17, 44,  5,
       15,  7, 32, 31, 23, 19,  9,  8, 18, 33,  2, 37, 39, 24, 22,  4, 13,
        0, 11, 40,  3, 36, 20, 27, 26, 43, 29, 42], dtype=int64), 'cur_cost': 94111.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([12, 16, 41, 28, 14, 25,  1, 30, 35, 10, 34,  6, 21, 38, 17, 44,  5,
       15,  7, 32, 31, 23, 19,  9,  8, 18, 33,  2, 37, 39, 24, 22,  4, 13,
        0, 11, 40,  3, 36, 20, 27, 26, 43, 29, 42], dtype=int64), 'cur_cost': 94114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14, 12, 16, 41, 28, 25,  1, 30, 35, 10, 34,  6, 21, 38, 17, 44,  5,
       15,  7, 32, 31, 23, 19,  9,  8, 18, 33,  2, 37, 39, 24, 22,  4, 13,
        0, 11, 40,  3, 36, 20, 27, 26, 43, 29, 42], dtype=int64), 'cur_cost': 89938.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([28, 12, 16, 41, 14, 25,  1, 30, 35, 10, 34,  6, 21, 38, 17, 44,  5,
       15,  7, 32, 31, 23, 19,  9,  8, 18, 33,  2, 37, 39, 24, 22,  4, 13,
        0, 11, 40,  3, 36, 20, 27, 26, 43, 29, 42], dtype=int64), 'cur_cost': 94170.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([28, 14, 12, 16, 41, 25,  1, 30, 35, 10, 34,  6, 21, 38, 17, 44,  5,
       15,  7, 32, 31, 23, 19,  9,  8, 18, 33,  2, 37, 39, 24, 22,  4, 13,
        0, 11, 40,  3, 36, 20, 27, 26, 43, 29, 42], dtype=int64), 'cur_cost': 93654.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:58,445 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 99317.00)
2025-08-05 10:28:58,445 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:58,445 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:58,445 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:58,447 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 45
2025-08-05 10:28:58,447 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,447 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15576.0, 路径长度: 45, 收集中间解: 0
2025-08-05 10:28:58,447 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 18, 12, 5, 9, 7, 3, 2, 8, 1, 4, 6, 10, 17, 19, 11, 14, 20, 21, 16, 13, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15576.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:58,448 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 15576.00)
2025-08-05 10:28:58,448 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:58,448 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:58,448 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:58,449 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 45
2025-08-05 10:28:58,449 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,450 - ExplorationExpert - INFO - 探索路径生成完成，成本: 59088.0, 路径长度: 45, 收集中间解: 0
2025-08-05 10:28:58,450 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [21, 3, 16, 20, 7, 2, 12, 15, 18, 42, 44, 37, 33, 43, 36, 35, 38, 40, 30, 24, 25, 31, 8, 1, 11, 29, 19, 39, 13, 22, 34, 10, 6, 32, 27, 14, 26, 17, 4, 9, 23, 28, 41, 0, 5], 'cur_cost': 59088.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:58,450 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 59088.00)
2025-08-05 10:28:58,450 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:58,450 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:58,450 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:58,453 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 45
2025-08-05 10:28:58,454 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,454 - ExplorationExpert - INFO - 探索路径生成完成，成本: 42341.0, 路径长度: 45, 收集中间解: 0
2025-08-05 10:28:58,454 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [16, 11, 44, 40, 34, 20, 15, 33, 30, 32, 13, 21, 39, 38, 35, 19, 18, 14, 43, 28, 23, 31, 36, 12, 42, 29, 26, 37, 41, 2, 7, 9, 6, 4, 5, 10, 0, 17, 3, 1, 8, 27, 25, 22, 24], 'cur_cost': 42341.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:58,454 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 42341.00)
2025-08-05 10:28:58,455 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:58,455 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:58,455 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:58,458 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 45
2025-08-05 10:28:58,458 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,458 - ExplorationExpert - INFO - 探索路径生成完成，成本: 52901.0, 路径长度: 45, 收集中间解: 0
2025-08-05 10:28:58,458 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [14, 44, 20, 13, 12, 38, 18, 15, 40, 36, 31, 24, 26, 27, 19, 33, 23, 35, 42, 10, 1, 2, 8, 4, 9, 5, 21, 41, 37, 43, 17, 16, 3, 34, 32, 22, 28, 29, 11, 39, 30, 0, 6, 7, 25], 'cur_cost': 52901.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:58,459 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 52901.00)
2025-08-05 10:28:58,459 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:58,459 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:58,460 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 9, 16, 18, 20, 14, 11, 13, 17, 19, 12, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 3, 5, 6, 4, 1, 2], 'cur_cost': 15699.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 15, 3, 7, 9, 5, 6, 4, 1, 8, 2, 10, 17, 19, 18, 20, 21, 12, 16, 11, 13, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15540.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 3, 1, 4, 6, 5, 9, 7, 8, 2, 10, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15378.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([14,  9, 37,  2,  4, 21, 28, 23, 32, 22, 17, 19,  8, 27,  1, 39, 11,
       13,  5, 38, 18, 25, 29, 44,  7, 12, 34, 31, 16,  0, 33, 26, 10, 40,
       41, 15, 35, 43, 24,  3, 30, 42, 20, 36,  6], dtype=int64), 'cur_cost': 92096.0, 'intermediate_solutions': [{'tour': array([10, 42, 12,  4,  2, 18, 15, 27, 41, 30, 13, 17, 35, 32,  0, 40, 11,
       44, 29,  7, 20, 22, 16, 37, 25,  6, 23, 28, 24,  1, 39,  8, 26, 33,
       38,  5, 43, 19, 36, 31,  9,  3, 21, 34, 14], dtype=int64), 'cur_cost': 97462.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 4, 10, 42, 12,  2, 18, 15, 27, 41, 30, 13, 17, 35, 32,  0, 40, 11,
       44, 29,  7, 20, 22, 16, 37, 25,  6, 23, 28, 24,  1, 39,  8, 26, 33,
       38,  5, 43, 19, 36, 31,  9,  3, 21, 34, 14], dtype=int64), 'cur_cost': 97508.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 2,  4, 10, 42, 12, 18, 15, 27, 41, 30, 13, 17, 35, 32,  0, 40, 11,
       44, 29,  7, 20, 22, 16, 37, 25,  6, 23, 28, 24,  1, 39,  8, 26, 33,
       38,  5, 43, 19, 36, 31,  9,  3, 21, 34, 14], dtype=int64), 'cur_cost': 93390.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([12,  4, 10, 42,  2, 18, 15, 27, 41, 30, 13, 17, 35, 32,  0, 40, 11,
       44, 29,  7, 20, 22, 16, 37, 25,  6, 23, 28, 24,  1, 39,  8, 26, 33,
       38,  5, 43, 19, 36, 31,  9,  3, 21, 34, 14], dtype=int64), 'cur_cost': 97087.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([12,  2,  4, 10, 42, 18, 15, 27, 41, 30, 13, 17, 35, 32,  0, 40, 11,
       44, 29,  7, 20, 22, 16, 37, 25,  6, 23, 28, 24,  1, 39,  8, 26, 33,
       38,  5, 43, 19, 36, 31,  9,  3, 21, 34, 14], dtype=int64), 'cur_cost': 93418.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 12, 3, 7, 9, 5, 6, 4, 1, 10, 2, 17, 19, 18, 20, 14, 11, 13, 15, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15591.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([23,  9, 15, 14, 27, 10, 44, 29, 38, 12, 37, 39, 11, 40, 26,  4,  8,
        7, 21, 20, 31, 33, 34, 19, 25,  2, 32, 24,  3, 35, 18, 42, 28,  6,
       13, 36,  5, 30, 16,  0, 22, 17, 43,  1, 41], dtype=int64), 'cur_cost': 99317.0, 'intermediate_solutions': [{'tour': array([16, 41, 28, 12, 14, 25,  1, 30, 35, 10, 34,  6, 21, 38, 17, 44,  5,
       15,  7, 32, 31, 23, 19,  9,  8, 18, 33,  2, 37, 39, 24, 22,  4, 13,
        0, 11, 40,  3, 36, 20, 27, 26, 43, 29, 42], dtype=int64), 'cur_cost': 94111.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([12, 16, 41, 28, 14, 25,  1, 30, 35, 10, 34,  6, 21, 38, 17, 44,  5,
       15,  7, 32, 31, 23, 19,  9,  8, 18, 33,  2, 37, 39, 24, 22,  4, 13,
        0, 11, 40,  3, 36, 20, 27, 26, 43, 29, 42], dtype=int64), 'cur_cost': 94114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14, 12, 16, 41, 28, 25,  1, 30, 35, 10, 34,  6, 21, 38, 17, 44,  5,
       15,  7, 32, 31, 23, 19,  9,  8, 18, 33,  2, 37, 39, 24, 22,  4, 13,
        0, 11, 40,  3, 36, 20, 27, 26, 43, 29, 42], dtype=int64), 'cur_cost': 89938.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([28, 12, 16, 41, 14, 25,  1, 30, 35, 10, 34,  6, 21, 38, 17, 44,  5,
       15,  7, 32, 31, 23, 19,  9,  8, 18, 33,  2, 37, 39, 24, 22,  4, 13,
        0, 11, 40,  3, 36, 20, 27, 26, 43, 29, 42], dtype=int64), 'cur_cost': 94170.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([28, 14, 12, 16, 41, 25,  1, 30, 35, 10, 34,  6, 21, 38, 17, 44,  5,
       15,  7, 32, 31, 23, 19,  9,  8, 18, 33,  2, 37, 39, 24, 22,  4, 13,
        0, 11, 40,  3, 36, 20, 27, 26, 43, 29, 42], dtype=int64), 'cur_cost': 93654.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 12, 5, 9, 7, 3, 2, 8, 1, 4, 6, 10, 17, 19, 11, 14, 20, 21, 16, 13, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15576.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [21, 3, 16, 20, 7, 2, 12, 15, 18, 42, 44, 37, 33, 43, 36, 35, 38, 40, 30, 24, 25, 31, 8, 1, 11, 29, 19, 39, 13, 22, 34, 10, 6, 32, 27, 14, 26, 17, 4, 9, 23, 28, 41, 0, 5], 'cur_cost': 59088.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [16, 11, 44, 40, 34, 20, 15, 33, 30, 32, 13, 21, 39, 38, 35, 19, 18, 14, 43, 28, 23, 31, 36, 12, 42, 29, 26, 37, 41, 2, 7, 9, 6, 4, 5, 10, 0, 17, 3, 1, 8, 27, 25, 22, 24], 'cur_cost': 42341.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [14, 44, 20, 13, 12, 38, 18, 15, 40, 36, 31, 24, 26, 27, 19, 33, 23, 35, 42, 10, 1, 2, 8, 4, 9, 5, 21, 41, 37, 43, 17, 16, 3, 34, 32, 22, 28, 29, 11, 39, 30, 0, 6, 7, 25], 'cur_cost': 52901.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:58,460 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:58,460 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:58,463 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=15378.000, 多样性=0.900
2025-08-05 10:28:58,463 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 10:28:58,463 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 10:28:58,464 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:58,464 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.022183804404113733, 'best_improvement': -0.3587206220180244}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.07131940906775351}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.12372728720648075, 'recent_improvements': [0.11488603201553711, -0.001585696186415295, -0.13256854239742438], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 10979.0, 'new_best_cost': 10979.0, 'quality_improvement': 0.0, 'old_diversity': 0.9777777777777777, 'new_diversity': 0.9777777777777777, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配']}
2025-08-05 10:28:58,464 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 10:28:58,464 - __main__ - INFO - composite8_45 开始进化第 2 代
2025-08-05 10:28:58,464 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 10:28:58,465 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:58,465 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=15378.000, 多样性=0.900
2025-08-05 10:28:58,465 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:58,467 - PathExpert - INFO - 路径结构分析完成: 公共边数量=13, 路径相似性=0.900
2025-08-05 10:28:58,467 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:58,468 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.978
2025-08-05 10:28:58,470 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 10:28:58,470 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:58,470 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-05 10:28:58,470 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-05 10:28:58,488 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.250, 适应度梯度: -4588.167, 聚类评分: 0.000, 覆盖率: 0.115, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:58,489 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 10:28:58,489 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:58,489 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite8_45
2025-08-05 10:28:58,494 - visualization.landscape_visualizer - INFO - 插值约束: 193 个点被约束到最小值 10979.00
2025-08-05 10:28:58,497 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=3.7%, 梯度: 3054.96 → 2940.41
2025-08-05 10:28:58,651 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite8_45\landscape_composite8_45_iter_97_20250805_102858.html
2025-08-05 10:28:58,728 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite8_45\dashboard_composite8_45_iter_97_20250805_102858.html
2025-08-05 10:28:58,728 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 97
2025-08-05 10:28:58,728 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 10:28:58,728 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2576秒
2025-08-05 10:28:58,729 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.25, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -4588.166666666667, 'local_optima_density': 0.25, 'gradient_variance': 1101169327.238889, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1148, 'fitness_entropy': 0.7668937437992748, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -4588.167)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.115)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360938.48968, 'performance_metrics': {}}}
2025-08-05 10:28:58,729 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:58,729 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:58,729 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:58,729 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:58,729 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:28:58,729 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 10:28:58,730 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:28:58,730 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:58,730 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:58,730 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:28:58,730 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:58,730 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:58,730 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:58,730 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:58,731 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:58,731 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:58,732 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 45
2025-08-05 10:28:58,732 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,733 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,733 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,733 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,733 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11321.0, 路径长度: 45, 收集中间解: 3
2025-08-05 10:28:58,733 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 6, 8, 2, 7, 9, 5, 3, 1, 4, 10, 17, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 11321.0, 'intermediate_solutions': [{'tour': [0, 21, 9, 16, 18, 20, 14, 11, 13, 17, 19, 12, 44, 41, 42, 34, 15, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 3, 5, 6, 4, 1, 2], 'cur_cost': 18501.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 38, 35, 36, 43, 33, 39, 37, 44, 34, 42, 41, 15, 12, 19, 17, 13, 11, 14, 20, 18, 16, 9, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 3, 5, 6, 4, 1, 2], 'cur_cost': 17970.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [21, 9, 16, 0, 18, 20, 14, 11, 13, 17, 19, 12, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 3, 5, 6, 4, 1, 2], 'cur_cost': 19750.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:58,733 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 11321.00)
2025-08-05 10:28:58,734 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:58,734 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:58,734 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:58,735 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 45
2025-08-05 10:28:58,735 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,735 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,735 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,735 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,736 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15666.0, 路径长度: 45, 收集中间解: 3
2025-08-05 10:28:58,736 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 3, 18, 8, 7, 9, 5, 6, 4, 1, 10, 2, 17, 19, 11, 14, 20, 21, 12, 16, 13, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15666.0, 'intermediate_solutions': [{'tour': [0, 14, 15, 3, 7, 9, 5, 6, 4, 1, 8, 2, 10, 17, 28, 18, 20, 21, 12, 16, 11, 13, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 19, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 24128.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 15, 3, 23, 40, 38, 35, 36, 43, 33, 39, 37, 44, 34, 41, 42, 13, 11, 16, 12, 21, 20, 18, 19, 17, 10, 2, 8, 1, 4, 6, 5, 9, 7, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 23041.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [14, 15, 3, 7, 9, 5, 6, 4, 1, 8, 2, 10, 17, 19, 18, 20, 21, 12, 16, 11, 0, 13, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15923.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:58,736 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 15666.00)
2025-08-05 10:28:58,736 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:58,736 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:58,736 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:58,739 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 45
2025-08-05 10:28:58,739 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,739 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,739 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,740 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,740 - ExplorationExpert - INFO - 探索路径生成完成，成本: 50861.0, 路径长度: 45, 收集中间解: 3
2025-08-05 10:28:58,740 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [10, 9, 13, 35, 40, 38, 19, 12, 18, 8, 17, 37, 43, 32, 23, 30, 14, 16, 1, 20, 39, 44, 21, 33, 31, 26, 29, 22, 41, 28, 11, 2, 4, 3, 7, 0, 5, 15, 34, 27, 25, 24, 36, 42, 6], 'cur_cost': 50861.0, 'intermediate_solutions': [{'tour': [0, 17, 3, 1, 4, 6, 5, 9, 7, 8, 2, 10, 22, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 19], 'cur_cost': 19632.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 3, 1, 4, 6, 5, 9, 7, 8, 2, 10, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 22, 29], 'cur_cost': 15376.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 3, 1, 4, 6, 5, 9, 7, 8, 2, 10, 19, 18, 20, 21, 14, 11, 13, 15, 12, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15419.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:58,740 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 50861.00)
2025-08-05 10:28:58,740 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:28:58,740 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:58,740 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:58,741 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 93937.0
2025-08-05 10:28:58,752 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:58,752 - ExploitationExpert - INFO - res_population_costs: [10979.0, 10979, 10973.0, 10973]
2025-08-05 10:28:58,752 - ExploitationExpert - INFO - res_populations: [array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 43, 33, 38, 39, 44, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 44, 39,
       38, 33, 43, 36, 35, 32, 30, 28, 24, 25, 22, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64)]
2025-08-05 10:28:58,753 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:58,753 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 8, 2, 7, 9, 5, 3, 1, 4, 10, 17, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 11321.0}, {'tour': [0, 3, 18, 8, 7, 9, 5, 6, 4, 1, 10, 2, 17, 19, 11, 14, 20, 21, 12, 16, 13, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15666.0}, {'tour': [10, 9, 13, 35, 40, 38, 19, 12, 18, 8, 17, 37, 43, 32, 23, 30, 14, 16, 1, 20, 39, 44, 21, 33, 31, 26, 29, 22, 41, 28, 11, 2, 4, 3, 7, 0, 5, 15, 34, 27, 25, 24, 36, 42, 6], 'cur_cost': 50861.0}, {'tour': array([12, 38,  8, 23, 14,  6, 10,  0, 43,  9, 37, 29,  7, 39, 30, 18, 42,
        5,  2, 44, 20,  1, 28, 16, 35, 41, 11, 32, 19, 26, 21, 40, 24, 17,
        3, 22, 15, 13, 27,  4, 25, 31, 34, 36, 33], dtype=int64), 'cur_cost': 93937.0}, {'tour': [0, 8, 12, 3, 7, 9, 5, 6, 4, 1, 10, 2, 17, 19, 18, 20, 14, 11, 13, 15, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15591.0}, {'tour': [23, 9, 15, 14, 27, 10, 44, 29, 38, 12, 37, 39, 11, 40, 26, 4, 8, 7, 21, 20, 31, 33, 34, 19, 25, 2, 32, 24, 3, 35, 18, 42, 28, 6, 13, 36, 5, 30, 16, 0, 22, 17, 43, 1, 41], 'cur_cost': 99317.0}, {'tour': [0, 18, 12, 5, 9, 7, 3, 2, 8, 1, 4, 6, 10, 17, 19, 11, 14, 20, 21, 16, 13, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15576.0}, {'tour': [21, 3, 16, 20, 7, 2, 12, 15, 18, 42, 44, 37, 33, 43, 36, 35, 38, 40, 30, 24, 25, 31, 8, 1, 11, 29, 19, 39, 13, 22, 34, 10, 6, 32, 27, 14, 26, 17, 4, 9, 23, 28, 41, 0, 5], 'cur_cost': 59088.0}, {'tour': [16, 11, 44, 40, 34, 20, 15, 33, 30, 32, 13, 21, 39, 38, 35, 19, 18, 14, 43, 28, 23, 31, 36, 12, 42, 29, 26, 37, 41, 2, 7, 9, 6, 4, 5, 10, 0, 17, 3, 1, 8, 27, 25, 22, 24], 'cur_cost': 42341.0}, {'tour': [14, 44, 20, 13, 12, 38, 18, 15, 40, 36, 31, 24, 26, 27, 19, 33, 23, 35, 42, 10, 1, 2, 8, 4, 9, 5, 21, 41, 37, 43, 17, 16, 3, 34, 32, 22, 28, 29, 11, 39, 30, 0, 6, 7, 25], 'cur_cost': 52901.0}]
2025-08-05 10:28:58,754 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:58,754 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 250, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 250, 'cache_hits': 0, 'similarity_calculations': 1203, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:58,755 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([12, 38,  8, 23, 14,  6, 10,  0, 43,  9, 37, 29,  7, 39, 30, 18, 42,
        5,  2, 44, 20,  1, 28, 16, 35, 41, 11, 32, 19, 26, 21, 40, 24, 17,
        3, 22, 15, 13, 27,  4, 25, 31, 34, 36, 33], dtype=int64), 'cur_cost': 93937.0, 'intermediate_solutions': [{'tour': array([37,  9, 14,  2,  4, 21, 28, 23, 32, 22, 17, 19,  8, 27,  1, 39, 11,
       13,  5, 38, 18, 25, 29, 44,  7, 12, 34, 31, 16,  0, 33, 26, 10, 40,
       41, 15, 35, 43, 24,  3, 30, 42, 20, 36,  6]), 'cur_cost': 92119.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2, 37,  9, 14,  4, 21, 28, 23, 32, 22, 17, 19,  8, 27,  1, 39, 11,
       13,  5, 38, 18, 25, 29, 44,  7, 12, 34, 31, 16,  0, 33, 26, 10, 40,
       41, 15, 35, 43, 24,  3, 30, 42, 20, 36,  6]), 'cur_cost': 92067.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 4,  2, 37,  9, 14, 21, 28, 23, 32, 22, 17, 19,  8, 27,  1, 39, 11,
       13,  5, 38, 18, 25, 29, 44,  7, 12, 34, 31, 16,  0, 33, 26, 10, 40,
       41, 15, 35, 43, 24,  3, 30, 42, 20, 36,  6]), 'cur_cost': 87902.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([14,  2, 37,  9,  4, 21, 28, 23, 32, 22, 17, 19,  8, 27,  1, 39, 11,
       13,  5, 38, 18, 25, 29, 44,  7, 12, 34, 31, 16,  0, 33, 26, 10, 40,
       41, 15, 35, 43, 24,  3, 30, 42, 20, 36,  6]), 'cur_cost': 92047.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([14,  4,  2, 37,  9, 21, 28, 23, 32, 22, 17, 19,  8, 27,  1, 39, 11,
       13,  5, 38, 18, 25, 29, 44,  7, 12, 34, 31, 16,  0, 33, 26, 10, 40,
       41, 15, 35, 43, 24,  3, 30, 42, 20, 36,  6]), 'cur_cost': 92095.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:58,755 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 93937.00)
2025-08-05 10:28:58,755 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:58,755 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:58,756 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:58,757 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 45
2025-08-05 10:28:58,757 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,757 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,757 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,757 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,758 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11376.0, 路径长度: 45, 收集中间解: 3
2025-08-05 10:28:58,758 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 11, 14, 12, 21, 18, 20, 19, 17, 13, 15, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 11376.0, 'intermediate_solutions': [{'tour': [0, 8, 12, 3, 7, 9, 5, 6, 4, 1, 10, 2, 17, 19, 18, 20, 14, 11, 13, 15, 21, 16, 42, 41, 38, 44, 37, 39, 33, 43, 36, 35, 34, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15622.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 12, 3, 7, 9, 5, 6, 4, 1, 10, 2, 17, 19, 18, 20, 14, 11, 13, 15, 21, 16, 42, 39, 37, 44, 34, 41, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15699.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 12, 3, 7, 9, 5, 6, 4, 1, 10, 2, 17, 19, 18, 20, 14, 11, 13, 15, 21, 16, 42, 41, 34, 44, 37, 39, 33, 27, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 26, 31, 29, 22], 'cur_cost': 21528.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:58,758 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 11376.00)
2025-08-05 10:28:58,758 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:28:58,758 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:58,758 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:58,758 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 93449.0
2025-08-05 10:28:58,771 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 10:28:58,771 - ExploitationExpert - INFO - res_population_costs: [10979.0, 10979, 10973.0, 10973, 10973, 10973]
2025-08-05 10:28:58,771 - ExploitationExpert - INFO - res_populations: [array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 43, 33, 38, 39, 44, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 44, 39,
       38, 33, 43, 36, 35, 32, 30, 28, 24, 25, 22, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64)]
2025-08-05 10:28:58,773 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:58,773 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 8, 2, 7, 9, 5, 3, 1, 4, 10, 17, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 11321.0}, {'tour': [0, 3, 18, 8, 7, 9, 5, 6, 4, 1, 10, 2, 17, 19, 11, 14, 20, 21, 12, 16, 13, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15666.0}, {'tour': [10, 9, 13, 35, 40, 38, 19, 12, 18, 8, 17, 37, 43, 32, 23, 30, 14, 16, 1, 20, 39, 44, 21, 33, 31, 26, 29, 22, 41, 28, 11, 2, 4, 3, 7, 0, 5, 15, 34, 27, 25, 24, 36, 42, 6], 'cur_cost': 50861.0}, {'tour': array([12, 38,  8, 23, 14,  6, 10,  0, 43,  9, 37, 29,  7, 39, 30, 18, 42,
        5,  2, 44, 20,  1, 28, 16, 35, 41, 11, 32, 19, 26, 21, 40, 24, 17,
        3, 22, 15, 13, 27,  4, 25, 31, 34, 36, 33], dtype=int64), 'cur_cost': 93937.0}, {'tour': [0, 11, 14, 12, 21, 18, 20, 19, 17, 13, 15, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 11376.0}, {'tour': array([32, 10, 40, 20,  6, 26,  4, 34, 33, 42, 18, 19, 17, 36, 22, 14, 16,
       27, 12, 21, 15,  1, 37,  2, 30, 28, 35, 23,  0, 43, 25,  7, 13, 38,
       41,  8, 44, 39,  9,  5, 24, 11, 29,  3, 31], dtype=int64), 'cur_cost': 93449.0}, {'tour': [0, 18, 12, 5, 9, 7, 3, 2, 8, 1, 4, 6, 10, 17, 19, 11, 14, 20, 21, 16, 13, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15576.0}, {'tour': [21, 3, 16, 20, 7, 2, 12, 15, 18, 42, 44, 37, 33, 43, 36, 35, 38, 40, 30, 24, 25, 31, 8, 1, 11, 29, 19, 39, 13, 22, 34, 10, 6, 32, 27, 14, 26, 17, 4, 9, 23, 28, 41, 0, 5], 'cur_cost': 59088.0}, {'tour': [16, 11, 44, 40, 34, 20, 15, 33, 30, 32, 13, 21, 39, 38, 35, 19, 18, 14, 43, 28, 23, 31, 36, 12, 42, 29, 26, 37, 41, 2, 7, 9, 6, 4, 5, 10, 0, 17, 3, 1, 8, 27, 25, 22, 24], 'cur_cost': 42341.0}, {'tour': [14, 44, 20, 13, 12, 38, 18, 15, 40, 36, 31, 24, 26, 27, 19, 33, 23, 35, 42, 10, 1, 2, 8, 4, 9, 5, 21, 41, 37, 43, 17, 16, 3, 34, 32, 22, 28, 29, 11, 39, 30, 0, 6, 7, 25], 'cur_cost': 52901.0}]
2025-08-05 10:28:58,774 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:58,774 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 251, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 251, 'cache_hits': 0, 'similarity_calculations': 1206, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:58,775 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([32, 10, 40, 20,  6, 26,  4, 34, 33, 42, 18, 19, 17, 36, 22, 14, 16,
       27, 12, 21, 15,  1, 37,  2, 30, 28, 35, 23,  0, 43, 25,  7, 13, 38,
       41,  8, 44, 39,  9,  5, 24, 11, 29,  3, 31], dtype=int64), 'cur_cost': 93449.0, 'intermediate_solutions': [{'tour': array([15,  9, 23, 14, 27, 10, 44, 29, 38, 12, 37, 39, 11, 40, 26,  4,  8,
        7, 21, 20, 31, 33, 34, 19, 25,  2, 32, 24,  3, 35, 18, 42, 28,  6,
       13, 36,  5, 30, 16,  0, 22, 17, 43,  1, 41]), 'cur_cost': 99842.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([14, 15,  9, 23, 27, 10, 44, 29, 38, 12, 37, 39, 11, 40, 26,  4,  8,
        7, 21, 20, 31, 33, 34, 19, 25,  2, 32, 24,  3, 35, 18, 42, 28,  6,
       13, 36,  5, 30, 16,  0, 22, 17, 43,  1, 41]), 'cur_cost': 95728.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([27, 14, 15,  9, 23, 10, 44, 29, 38, 12, 37, 39, 11, 40, 26,  4,  8,
        7, 21, 20, 31, 33, 34, 19, 25,  2, 32, 24,  3, 35, 18, 42, 28,  6,
       13, 36,  5, 30, 16,  0, 22, 17, 43,  1, 41]), 'cur_cost': 99335.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([23, 14, 15,  9, 27, 10, 44, 29, 38, 12, 37, 39, 11, 40, 26,  4,  8,
        7, 21, 20, 31, 33, 34, 19, 25,  2, 32, 24,  3, 35, 18, 42, 28,  6,
       13, 36,  5, 30, 16,  0, 22, 17, 43,  1, 41]), 'cur_cost': 99303.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([23, 27, 14, 15,  9, 10, 44, 29, 38, 12, 37, 39, 11, 40, 26,  4,  8,
        7, 21, 20, 31, 33, 34, 19, 25,  2, 32, 24,  3, 35, 18, 42, 28,  6,
       13, 36,  5, 30, 16,  0, 22, 17, 43,  1, 41]), 'cur_cost': 91975.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:58,775 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 93449.00)
2025-08-05 10:28:58,775 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:58,776 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:58,776 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:58,777 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 45
2025-08-05 10:28:58,777 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,777 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,778 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,778 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,778 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11538.0, 路径长度: 45, 收集中间解: 3
2025-08-05 10:28:58,778 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 9, 7, 20, 14, 11, 13, 17, 19, 18, 21, 12, 16, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 1, 4, 6, 5, 3, 2], 'cur_cost': 11538.0, 'intermediate_solutions': [{'tour': [0, 18, 12, 5, 9, 7, 3, 2, 8, 1, 4, 6, 10, 17, 31, 11, 14, 20, 21, 16, 13, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 19, 29, 22], 'cur_cost': 23939.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 12, 5, 9, 41, 15, 13, 16, 21, 20, 14, 11, 19, 17, 10, 6, 4, 1, 8, 2, 3, 7, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 21599.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 12, 5, 28, 9, 7, 3, 2, 8, 1, 4, 6, 10, 17, 19, 11, 14, 20, 21, 16, 13, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 23234.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:58,779 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 11538.00)
2025-08-05 10:28:58,779 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:58,779 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:58,779 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:58,781 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 45
2025-08-05 10:28:58,781 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,781 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,781 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,781 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,781 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11524.0, 路径长度: 45, 收集中间解: 3
2025-08-05 10:28:58,782 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 7, 3, 16, 21, 12, 20, 14, 11, 13, 17, 19, 18, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 1, 4, 6, 5, 9, 2], 'cur_cost': 11524.0, 'intermediate_solutions': [{'tour': [21, 3, 16, 20, 7, 2, 12, 15, 36, 42, 44, 37, 33, 43, 18, 35, 38, 40, 30, 24, 25, 31, 8, 1, 11, 29, 19, 39, 13, 22, 34, 10, 6, 32, 27, 14, 26, 17, 4, 9, 23, 28, 41, 0, 5], 'cur_cost': 61917.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [21, 3, 16, 20, 7, 2, 12, 15, 0, 41, 28, 23, 9, 4, 17, 26, 14, 27, 32, 6, 10, 34, 22, 13, 39, 19, 29, 11, 1, 8, 31, 25, 24, 30, 40, 38, 35, 36, 43, 33, 37, 44, 42, 18, 5], 'cur_cost': 63158.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [21, 3, 16, 20, 7, 2, 12, 15, 18, 42, 44, 37, 33, 43, 36, 35, 38, 40, 30, 24, 25, 31, 8, 1, 11, 14, 29, 19, 39, 13, 22, 34, 10, 6, 32, 27, 26, 17, 4, 9, 23, 28, 41, 0, 5], 'cur_cost': 54910.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:58,782 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 11524.00)
2025-08-05 10:28:58,782 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:58,782 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:58,782 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:58,785 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 45
2025-08-05 10:28:58,786 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,786 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,786 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,787 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,787 - ExplorationExpert - INFO - 探索路径生成完成，成本: 45261.0, 路径长度: 45, 收集中间解: 3
2025-08-05 10:28:58,787 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [35, 12, 21, 14, 36, 43, 38, 17, 19, 34, 18, 23, 26, 31, 27, 42, 16, 40, 39, 33, 20, 37, 29, 11, 10, 0, 7, 1, 8, 15, 44, 32, 24, 25, 28, 13, 2, 5, 6, 3, 4, 9, 41, 30, 22], 'cur_cost': 45261.0, 'intermediate_solutions': [{'tour': [16, 11, 44, 5, 34, 20, 15, 33, 30, 32, 13, 21, 39, 38, 35, 19, 18, 14, 43, 28, 23, 31, 36, 12, 42, 29, 26, 37, 41, 2, 7, 9, 6, 4, 40, 10, 0, 17, 3, 1, 8, 27, 25, 22, 24], 'cur_cost': 54357.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [16, 11, 44, 40, 34, 20, 15, 33, 30, 32, 13, 21, 39, 38, 35, 19, 18, 14, 43, 28, 23, 31, 36, 12, 42, 22, 25, 27, 8, 1, 3, 17, 0, 10, 5, 4, 6, 9, 7, 2, 41, 37, 26, 29, 24], 'cur_cost': 42425.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [16, 11, 44, 40, 34, 20, 15, 33, 30, 32, 13, 21, 39, 38, 35, 19, 7, 18, 14, 43, 28, 23, 31, 36, 12, 42, 29, 26, 37, 41, 2, 9, 6, 4, 5, 10, 0, 17, 3, 1, 8, 27, 25, 22, 24], 'cur_cost': 46490.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:58,787 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 45261.00)
2025-08-05 10:28:58,788 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:58,788 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:58,788 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:58,790 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 45
2025-08-05 10:28:58,790 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,790 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,791 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,791 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,791 - ExplorationExpert - INFO - 探索路径生成完成，成本: 55262.0, 路径长度: 45, 收集中间解: 3
2025-08-05 10:28:58,791 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [10, 3, 18, 2, 40, 19, 6, 4, 1, 17, 43, 11, 30, 14, 21, 15, 12, 13, 16, 42, 41, 32, 29, 22, 24, 25, 27, 31, 28, 0, 5, 8, 34, 36, 33, 9, 23, 20, 44, 37, 7, 26, 35, 39, 38], 'cur_cost': 55262.0, 'intermediate_solutions': [{'tour': [14, 44, 20, 13, 12, 38, 2, 15, 40, 36, 31, 24, 26, 27, 19, 33, 23, 35, 42, 10, 1, 18, 8, 4, 9, 5, 21, 41, 37, 43, 17, 16, 3, 34, 32, 22, 28, 29, 11, 39, 30, 0, 6, 7, 25], 'cur_cost': 60673.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 9, 4, 8, 2, 1, 10, 42, 35, 23, 33, 19, 27, 26, 24, 31, 36, 40, 15, 18, 38, 12, 13, 20, 44, 14, 21, 41, 37, 43, 17, 16, 3, 34, 32, 22, 28, 29, 11, 39, 30, 0, 6, 7, 25], 'cur_cost': 52493.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [14, 44, 20, 13, 12, 38, 18, 15, 40, 36, 31, 24, 26, 27, 11, 19, 33, 23, 35, 42, 10, 1, 2, 8, 4, 9, 5, 21, 41, 37, 43, 17, 16, 3, 34, 32, 22, 28, 29, 39, 30, 0, 6, 7, 25], 'cur_cost': 52330.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:58,791 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 55262.00)
2025-08-05 10:28:58,791 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:58,792 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:58,793 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 8, 2, 7, 9, 5, 3, 1, 4, 10, 17, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 11321.0, 'intermediate_solutions': [{'tour': [0, 21, 9, 16, 18, 20, 14, 11, 13, 17, 19, 12, 44, 41, 42, 34, 15, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 3, 5, 6, 4, 1, 2], 'cur_cost': 18501.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 38, 35, 36, 43, 33, 39, 37, 44, 34, 42, 41, 15, 12, 19, 17, 13, 11, 14, 20, 18, 16, 9, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 3, 5, 6, 4, 1, 2], 'cur_cost': 17970.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [21, 9, 16, 0, 18, 20, 14, 11, 13, 17, 19, 12, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 3, 5, 6, 4, 1, 2], 'cur_cost': 19750.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 18, 8, 7, 9, 5, 6, 4, 1, 10, 2, 17, 19, 11, 14, 20, 21, 12, 16, 13, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15666.0, 'intermediate_solutions': [{'tour': [0, 14, 15, 3, 7, 9, 5, 6, 4, 1, 8, 2, 10, 17, 28, 18, 20, 21, 12, 16, 11, 13, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 19, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 24128.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 15, 3, 23, 40, 38, 35, 36, 43, 33, 39, 37, 44, 34, 41, 42, 13, 11, 16, 12, 21, 20, 18, 19, 17, 10, 2, 8, 1, 4, 6, 5, 9, 7, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 23041.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [14, 15, 3, 7, 9, 5, 6, 4, 1, 8, 2, 10, 17, 19, 18, 20, 21, 12, 16, 11, 0, 13, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15923.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [10, 9, 13, 35, 40, 38, 19, 12, 18, 8, 17, 37, 43, 32, 23, 30, 14, 16, 1, 20, 39, 44, 21, 33, 31, 26, 29, 22, 41, 28, 11, 2, 4, 3, 7, 0, 5, 15, 34, 27, 25, 24, 36, 42, 6], 'cur_cost': 50861.0, 'intermediate_solutions': [{'tour': [0, 17, 3, 1, 4, 6, 5, 9, 7, 8, 2, 10, 22, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 19], 'cur_cost': 19632.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 3, 1, 4, 6, 5, 9, 7, 8, 2, 10, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 22, 29], 'cur_cost': 15376.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 3, 1, 4, 6, 5, 9, 7, 8, 2, 10, 19, 18, 20, 21, 14, 11, 13, 15, 12, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15419.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([12, 38,  8, 23, 14,  6, 10,  0, 43,  9, 37, 29,  7, 39, 30, 18, 42,
        5,  2, 44, 20,  1, 28, 16, 35, 41, 11, 32, 19, 26, 21, 40, 24, 17,
        3, 22, 15, 13, 27,  4, 25, 31, 34, 36, 33], dtype=int64), 'cur_cost': 93937.0, 'intermediate_solutions': [{'tour': array([37,  9, 14,  2,  4, 21, 28, 23, 32, 22, 17, 19,  8, 27,  1, 39, 11,
       13,  5, 38, 18, 25, 29, 44,  7, 12, 34, 31, 16,  0, 33, 26, 10, 40,
       41, 15, 35, 43, 24,  3, 30, 42, 20, 36,  6]), 'cur_cost': 92119.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2, 37,  9, 14,  4, 21, 28, 23, 32, 22, 17, 19,  8, 27,  1, 39, 11,
       13,  5, 38, 18, 25, 29, 44,  7, 12, 34, 31, 16,  0, 33, 26, 10, 40,
       41, 15, 35, 43, 24,  3, 30, 42, 20, 36,  6]), 'cur_cost': 92067.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 4,  2, 37,  9, 14, 21, 28, 23, 32, 22, 17, 19,  8, 27,  1, 39, 11,
       13,  5, 38, 18, 25, 29, 44,  7, 12, 34, 31, 16,  0, 33, 26, 10, 40,
       41, 15, 35, 43, 24,  3, 30, 42, 20, 36,  6]), 'cur_cost': 87902.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([14,  2, 37,  9,  4, 21, 28, 23, 32, 22, 17, 19,  8, 27,  1, 39, 11,
       13,  5, 38, 18, 25, 29, 44,  7, 12, 34, 31, 16,  0, 33, 26, 10, 40,
       41, 15, 35, 43, 24,  3, 30, 42, 20, 36,  6]), 'cur_cost': 92047.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([14,  4,  2, 37,  9, 21, 28, 23, 32, 22, 17, 19,  8, 27,  1, 39, 11,
       13,  5, 38, 18, 25, 29, 44,  7, 12, 34, 31, 16,  0, 33, 26, 10, 40,
       41, 15, 35, 43, 24,  3, 30, 42, 20, 36,  6]), 'cur_cost': 92095.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 14, 12, 21, 18, 20, 19, 17, 13, 15, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 11376.0, 'intermediate_solutions': [{'tour': [0, 8, 12, 3, 7, 9, 5, 6, 4, 1, 10, 2, 17, 19, 18, 20, 14, 11, 13, 15, 21, 16, 42, 41, 38, 44, 37, 39, 33, 43, 36, 35, 34, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15622.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 12, 3, 7, 9, 5, 6, 4, 1, 10, 2, 17, 19, 18, 20, 14, 11, 13, 15, 21, 16, 42, 39, 37, 44, 34, 41, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15699.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 12, 3, 7, 9, 5, 6, 4, 1, 10, 2, 17, 19, 18, 20, 14, 11, 13, 15, 21, 16, 42, 41, 34, 44, 37, 39, 33, 27, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 26, 31, 29, 22], 'cur_cost': 21528.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([32, 10, 40, 20,  6, 26,  4, 34, 33, 42, 18, 19, 17, 36, 22, 14, 16,
       27, 12, 21, 15,  1, 37,  2, 30, 28, 35, 23,  0, 43, 25,  7, 13, 38,
       41,  8, 44, 39,  9,  5, 24, 11, 29,  3, 31], dtype=int64), 'cur_cost': 93449.0, 'intermediate_solutions': [{'tour': array([15,  9, 23, 14, 27, 10, 44, 29, 38, 12, 37, 39, 11, 40, 26,  4,  8,
        7, 21, 20, 31, 33, 34, 19, 25,  2, 32, 24,  3, 35, 18, 42, 28,  6,
       13, 36,  5, 30, 16,  0, 22, 17, 43,  1, 41]), 'cur_cost': 99842.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([14, 15,  9, 23, 27, 10, 44, 29, 38, 12, 37, 39, 11, 40, 26,  4,  8,
        7, 21, 20, 31, 33, 34, 19, 25,  2, 32, 24,  3, 35, 18, 42, 28,  6,
       13, 36,  5, 30, 16,  0, 22, 17, 43,  1, 41]), 'cur_cost': 95728.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([27, 14, 15,  9, 23, 10, 44, 29, 38, 12, 37, 39, 11, 40, 26,  4,  8,
        7, 21, 20, 31, 33, 34, 19, 25,  2, 32, 24,  3, 35, 18, 42, 28,  6,
       13, 36,  5, 30, 16,  0, 22, 17, 43,  1, 41]), 'cur_cost': 99335.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([23, 14, 15,  9, 27, 10, 44, 29, 38, 12, 37, 39, 11, 40, 26,  4,  8,
        7, 21, 20, 31, 33, 34, 19, 25,  2, 32, 24,  3, 35, 18, 42, 28,  6,
       13, 36,  5, 30, 16,  0, 22, 17, 43,  1, 41]), 'cur_cost': 99303.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([23, 27, 14, 15,  9, 10, 44, 29, 38, 12, 37, 39, 11, 40, 26,  4,  8,
        7, 21, 20, 31, 33, 34, 19, 25,  2, 32, 24,  3, 35, 18, 42, 28,  6,
       13, 36,  5, 30, 16,  0, 22, 17, 43,  1, 41]), 'cur_cost': 91975.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 7, 20, 14, 11, 13, 17, 19, 18, 21, 12, 16, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 1, 4, 6, 5, 3, 2], 'cur_cost': 11538.0, 'intermediate_solutions': [{'tour': [0, 18, 12, 5, 9, 7, 3, 2, 8, 1, 4, 6, 10, 17, 31, 11, 14, 20, 21, 16, 13, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 19, 29, 22], 'cur_cost': 23939.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 12, 5, 9, 41, 15, 13, 16, 21, 20, 14, 11, 19, 17, 10, 6, 4, 1, 8, 2, 3, 7, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 21599.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 12, 5, 28, 9, 7, 3, 2, 8, 1, 4, 6, 10, 17, 19, 11, 14, 20, 21, 16, 13, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 23234.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 3, 16, 21, 12, 20, 14, 11, 13, 17, 19, 18, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 1, 4, 6, 5, 9, 2], 'cur_cost': 11524.0, 'intermediate_solutions': [{'tour': [21, 3, 16, 20, 7, 2, 12, 15, 36, 42, 44, 37, 33, 43, 18, 35, 38, 40, 30, 24, 25, 31, 8, 1, 11, 29, 19, 39, 13, 22, 34, 10, 6, 32, 27, 14, 26, 17, 4, 9, 23, 28, 41, 0, 5], 'cur_cost': 61917.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [21, 3, 16, 20, 7, 2, 12, 15, 0, 41, 28, 23, 9, 4, 17, 26, 14, 27, 32, 6, 10, 34, 22, 13, 39, 19, 29, 11, 1, 8, 31, 25, 24, 30, 40, 38, 35, 36, 43, 33, 37, 44, 42, 18, 5], 'cur_cost': 63158.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [21, 3, 16, 20, 7, 2, 12, 15, 18, 42, 44, 37, 33, 43, 36, 35, 38, 40, 30, 24, 25, 31, 8, 1, 11, 14, 29, 19, 39, 13, 22, 34, 10, 6, 32, 27, 26, 17, 4, 9, 23, 28, 41, 0, 5], 'cur_cost': 54910.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [35, 12, 21, 14, 36, 43, 38, 17, 19, 34, 18, 23, 26, 31, 27, 42, 16, 40, 39, 33, 20, 37, 29, 11, 10, 0, 7, 1, 8, 15, 44, 32, 24, 25, 28, 13, 2, 5, 6, 3, 4, 9, 41, 30, 22], 'cur_cost': 45261.0, 'intermediate_solutions': [{'tour': [16, 11, 44, 5, 34, 20, 15, 33, 30, 32, 13, 21, 39, 38, 35, 19, 18, 14, 43, 28, 23, 31, 36, 12, 42, 29, 26, 37, 41, 2, 7, 9, 6, 4, 40, 10, 0, 17, 3, 1, 8, 27, 25, 22, 24], 'cur_cost': 54357.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [16, 11, 44, 40, 34, 20, 15, 33, 30, 32, 13, 21, 39, 38, 35, 19, 18, 14, 43, 28, 23, 31, 36, 12, 42, 22, 25, 27, 8, 1, 3, 17, 0, 10, 5, 4, 6, 9, 7, 2, 41, 37, 26, 29, 24], 'cur_cost': 42425.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [16, 11, 44, 40, 34, 20, 15, 33, 30, 32, 13, 21, 39, 38, 35, 19, 7, 18, 14, 43, 28, 23, 31, 36, 12, 42, 29, 26, 37, 41, 2, 9, 6, 4, 5, 10, 0, 17, 3, 1, 8, 27, 25, 22, 24], 'cur_cost': 46490.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [10, 3, 18, 2, 40, 19, 6, 4, 1, 17, 43, 11, 30, 14, 21, 15, 12, 13, 16, 42, 41, 32, 29, 22, 24, 25, 27, 31, 28, 0, 5, 8, 34, 36, 33, 9, 23, 20, 44, 37, 7, 26, 35, 39, 38], 'cur_cost': 55262.0, 'intermediate_solutions': [{'tour': [14, 44, 20, 13, 12, 38, 2, 15, 40, 36, 31, 24, 26, 27, 19, 33, 23, 35, 42, 10, 1, 18, 8, 4, 9, 5, 21, 41, 37, 43, 17, 16, 3, 34, 32, 22, 28, 29, 11, 39, 30, 0, 6, 7, 25], 'cur_cost': 60673.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 9, 4, 8, 2, 1, 10, 42, 35, 23, 33, 19, 27, 26, 24, 31, 36, 40, 15, 18, 38, 12, 13, 20, 44, 14, 21, 41, 37, 43, 17, 16, 3, 34, 32, 22, 28, 29, 11, 39, 30, 0, 6, 7, 25], 'cur_cost': 52493.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [14, 44, 20, 13, 12, 38, 18, 15, 40, 36, 31, 24, 26, 27, 11, 19, 33, 23, 35, 42, 10, 1, 2, 8, 4, 9, 5, 21, 41, 37, 43, 17, 16, 3, 34, 32, 22, 28, 29, 39, 30, 0, 6, 7, 25], 'cur_cost': 52330.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:58,794 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:58,794 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:58,797 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=11321.000, 多样性=0.944
2025-08-05 10:28:58,797 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 10:28:58,797 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 10:28:58,797 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:58,798 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.12435939368335559, 'best_improvement': 0.2638184419300299}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.048272078990674754}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.0102990541088492, 'recent_improvements': [-0.001585696186415295, -0.13256854239742438, -0.022183804404113733], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 6, 'new_count': 6, 'count_change': 0, 'old_best_cost': 10973.0, 'new_best_cost': 10973.0, 'quality_improvement': 0.0, 'old_diversity': 0.8074074074074075, 'new_diversity': 0.8074074074074075, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配']}
2025-08-05 10:28:58,798 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 10:28:58,798 - __main__ - INFO - composite8_45 开始进化第 3 代
2025-08-05 10:28:58,798 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 10:28:58,798 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:58,799 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=11321.000, 多样性=0.944
2025-08-05 10:28:58,799 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:58,801 - PathExpert - INFO - 路径结构分析完成: 公共边数量=14, 路径相似性=0.944
2025-08-05 10:28:58,801 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:58,804 - EliteExpert - INFO - 精英解分析完成: 精英解数量=6, 多样性=0.807
2025-08-05 10:28:58,806 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 10:28:58,807 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:58,807 - LandscapeExpert - INFO - 添加精英解数据: 6个精英解
2025-08-05 10:28:58,807 - LandscapeExpert - INFO - 数据提取成功: 16个路径, 16个适应度值
2025-08-05 10:28:58,842 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.312, 适应度梯度: 605.575, 聚类评分: 0.000, 覆盖率: 0.116, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:58,842 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 10:28:58,842 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:58,842 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite8_45
2025-08-05 10:28:58,847 - visualization.landscape_visualizer - INFO - 插值约束: 182 个点被约束到最小值 10973.00
2025-08-05 10:28:58,848 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=9.9%, 梯度: 2328.02 → 2098.48
2025-08-05 10:28:59,004 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite8_45\landscape_composite8_45_iter_98_20250805_102858.html
2025-08-05 10:28:59,063 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite8_45\dashboard_composite8_45_iter_98_20250805_102858.html
2025-08-05 10:28:59,063 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 98
2025-08-05 10:28:59,063 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 10:28:59,063 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2568秒
2025-08-05 10:28:59,064 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3125, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 605.5750000000007, 'local_optima_density': 0.3125, 'gradient_variance': 917048175.4343749, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1159, 'fitness_entropy': 0.6858203810934291, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.116)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 605.575)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360938.8422108, 'performance_metrics': {}}}
2025-08-05 10:28:59,064 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:59,064 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:59,064 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:59,064 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:59,065 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:59,065 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 10:28:59,065 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:59,065 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:59,065 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:59,066 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:59,066 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:59,066 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:59,066 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 4} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:59,066 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:59,066 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:59,066 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:59,068 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 45
2025-08-05 10:28:59,068 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,068 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,068 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,068 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,068 - ExplorationExpert - INFO - 探索路径生成完成，成本: 71910.0, 路径长度: 45, 收集中间解: 3
2025-08-05 10:28:59,069 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [12, 38, 13, 35, 6, 18, 19, 0, 43, 1, 37, 17, 7, 39, 11, 30, 20, 21, 15, 16, 36, 44, 42, 41, 31, 26, 29, 22, 24, 25, 27, 2, 4, 3, 28, 10, 5, 33, 34, 9, 14, 40, 23, 8, 32], 'cur_cost': 71910.0, 'intermediate_solutions': [{'tour': [0, 6, 8, 2, 7, 9, 5, 3, 1, 4, 10, 17, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 22, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 35], 'cur_cost': 19384.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 8, 2, 7, 9, 5, 3, 1, 4, 10, 17, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 32, 23, 40, 38, 35, 36, 43, 33, 39, 37, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 17180.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 8, 2, 9, 5, 3, 1, 4, 10, 17, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 7, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 17359.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:59,069 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 71910.00)
2025-08-05 10:28:59,069 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:59,069 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:59,069 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:59,070 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 45
2025-08-05 10:28:59,070 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,070 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,071 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,071 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,071 - ExplorationExpert - INFO - 探索路径生成完成，成本: 76479.0, 路径长度: 45, 收集中间解: 3
2025-08-05 10:28:59,071 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [32, 11, 8, 12, 7, 9, 5, 19, 4, 18, 15, 2, 42, 41, 23, 44, 14, 16, 1, 20, 13, 35, 28, 33, 34, 0, 37, 39, 24, 26, 36, 40, 38, 17, 3, 22, 30, 10, 27, 25, 29, 31, 21, 6, 43], 'cur_cost': 76479.0, 'intermediate_solutions': [{'tour': [0, 3, 18, 5, 7, 9, 8, 6, 4, 1, 10, 2, 17, 19, 11, 14, 20, 21, 12, 16, 13, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15752.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 18, 8, 7, 9, 5, 6, 4, 1, 10, 2, 17, 19, 11, 14, 20, 21, 12, 16, 13, 15, 41, 42, 34, 23, 40, 38, 35, 36, 43, 33, 39, 37, 44, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 21459.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 18, 8, 7, 9, 5, 6, 4, 10, 2, 17, 19, 11, 14, 20, 21, 12, 16, 13, 15, 41, 42, 34, 44, 1, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 21787.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:59,071 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 76479.00)
2025-08-05 10:28:59,071 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:59,072 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:59,072 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:59,073 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 45
2025-08-05 10:28:59,073 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,073 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,073 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,074 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,074 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15425.0, 路径长度: 45, 收集中间解: 3
2025-08-05 10:28:59,074 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 6, 17, 1, 4, 9, 7, 3, 5, 8, 2, 10, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15425.0, 'intermediate_solutions': [{'tour': [10, 9, 13, 35, 40, 38, 19, 12, 18, 8, 17, 37, 43, 32, 23, 30, 14, 28, 1, 20, 39, 44, 21, 33, 31, 26, 29, 22, 41, 16, 11, 2, 4, 3, 7, 0, 5, 15, 34, 27, 25, 24, 36, 42, 6], 'cur_cost': 50935.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 9, 13, 35, 40, 38, 19, 12, 18, 8, 17, 37, 43, 32, 23, 30, 14, 16, 1, 20, 22, 29, 26, 31, 33, 21, 44, 39, 41, 28, 11, 2, 4, 3, 7, 0, 5, 15, 34, 27, 25, 24, 36, 42, 6], 'cur_cost': 48671.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 9, 13, 35, 40, 38, 19, 12, 18, 8, 17, 37, 43, 32, 23, 30, 14, 16, 1, 20, 39, 44, 21, 33, 31, 26, 29, 22, 28, 11, 2, 4, 3, 41, 7, 0, 5, 15, 34, 27, 25, 24, 36, 42, 6], 'cur_cost': 50995.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:59,074 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 15425.00)
2025-08-05 10:28:59,074 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:28:59,074 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:59,074 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:59,075 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 79619.0
2025-08-05 10:28:59,087 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 10:28:59,088 - ExploitationExpert - INFO - res_population_costs: [10973.0, 10973, 10973, 10973, 10979.0, 10979]
2025-08-05 10:28:59,088 - ExploitationExpert - INFO - res_populations: [array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 43, 33, 38, 39, 44, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 44, 39,
       38, 33, 43, 36, 35, 32, 30, 28, 24, 25, 22, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64)]
2025-08-05 10:28:59,090 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:59,090 - ExploitationExpert - INFO - populations: [{'tour': [12, 38, 13, 35, 6, 18, 19, 0, 43, 1, 37, 17, 7, 39, 11, 30, 20, 21, 15, 16, 36, 44, 42, 41, 31, 26, 29, 22, 24, 25, 27, 2, 4, 3, 28, 10, 5, 33, 34, 9, 14, 40, 23, 8, 32], 'cur_cost': 71910.0}, {'tour': [32, 11, 8, 12, 7, 9, 5, 19, 4, 18, 15, 2, 42, 41, 23, 44, 14, 16, 1, 20, 13, 35, 28, 33, 34, 0, 37, 39, 24, 26, 36, 40, 38, 17, 3, 22, 30, 10, 27, 25, 29, 31, 21, 6, 43], 'cur_cost': 76479.0}, {'tour': [0, 6, 17, 1, 4, 9, 7, 3, 5, 8, 2, 10, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15425.0}, {'tour': array([ 4,  6, 33, 13,  9, 34, 43, 19, 22, 32, 28, 25, 10, 21, 26, 40, 12,
       11,  7, 41,  8, 42, 44, 39, 36, 30, 27,  1, 37,  2, 29, 23, 20, 24,
       18, 14, 15, 35, 17, 38,  0, 16,  3, 31,  5], dtype=int64), 'cur_cost': 79619.0}, {'tour': [0, 11, 14, 12, 21, 18, 20, 19, 17, 13, 15, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 11376.0}, {'tour': [32, 10, 40, 20, 6, 26, 4, 34, 33, 42, 18, 19, 17, 36, 22, 14, 16, 27, 12, 21, 15, 1, 37, 2, 30, 28, 35, 23, 0, 43, 25, 7, 13, 38, 41, 8, 44, 39, 9, 5, 24, 11, 29, 3, 31], 'cur_cost': 93449.0}, {'tour': [0, 9, 7, 20, 14, 11, 13, 17, 19, 18, 21, 12, 16, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 1, 4, 6, 5, 3, 2], 'cur_cost': 11538.0}, {'tour': [0, 7, 3, 16, 21, 12, 20, 14, 11, 13, 17, 19, 18, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 1, 4, 6, 5, 9, 2], 'cur_cost': 11524.0}, {'tour': [35, 12, 21, 14, 36, 43, 38, 17, 19, 34, 18, 23, 26, 31, 27, 42, 16, 40, 39, 33, 20, 37, 29, 11, 10, 0, 7, 1, 8, 15, 44, 32, 24, 25, 28, 13, 2, 5, 6, 3, 4, 9, 41, 30, 22], 'cur_cost': 45261.0}, {'tour': [10, 3, 18, 2, 40, 19, 6, 4, 1, 17, 43, 11, 30, 14, 21, 15, 12, 13, 16, 42, 41, 32, 29, 22, 24, 25, 27, 31, 28, 0, 5, 8, 34, 36, 33, 9, 23, 20, 44, 37, 7, 26, 35, 39, 38], 'cur_cost': 55262.0}]
2025-08-05 10:28:59,091 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:59,091 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 252, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 252, 'cache_hits': 0, 'similarity_calculations': 1210, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:59,092 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 4,  6, 33, 13,  9, 34, 43, 19, 22, 32, 28, 25, 10, 21, 26, 40, 12,
       11,  7, 41,  8, 42, 44, 39, 36, 30, 27,  1, 37,  2, 29, 23, 20, 24,
       18, 14, 15, 35, 17, 38,  0, 16,  3, 31,  5], dtype=int64), 'cur_cost': 79619.0, 'intermediate_solutions': [{'tour': array([ 8, 38, 12, 23, 14,  6, 10,  0, 43,  9, 37, 29,  7, 39, 30, 18, 42,
        5,  2, 44, 20,  1, 28, 16, 35, 41, 11, 32, 19, 26, 21, 40, 24, 17,
        3, 22, 15, 13, 27,  4, 25, 31, 34, 36, 33]), 'cur_cost': 93961.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([23,  8, 38, 12, 14,  6, 10,  0, 43,  9, 37, 29,  7, 39, 30, 18, 42,
        5,  2, 44, 20,  1, 28, 16, 35, 41, 11, 32, 19, 26, 21, 40, 24, 17,
        3, 22, 15, 13, 27,  4, 25, 31, 34, 36, 33]), 'cur_cost': 93436.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14, 23,  8, 38, 12,  6, 10,  0, 43,  9, 37, 29,  7, 39, 30, 18, 42,
        5,  2, 44, 20,  1, 28, 16, 35, 41, 11, 32, 19, 26, 21, 40, 24, 17,
        3, 22, 15, 13, 27,  4, 25, 31, 34, 36, 33]), 'cur_cost': 93981.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([12, 23,  8, 38, 14,  6, 10,  0, 43,  9, 37, 29,  7, 39, 30, 18, 42,
        5,  2, 44, 20,  1, 28, 16, 35, 41, 11, 32, 19, 26, 21, 40, 24, 17,
        3, 22, 15, 13, 27,  4, 25, 31, 34, 36, 33]), 'cur_cost': 93985.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([12, 14, 23,  8, 38,  6, 10,  0, 43,  9, 37, 29,  7, 39, 30, 18, 42,
        5,  2, 44, 20,  1, 28, 16, 35, 41, 11, 32, 19, 26, 21, 40, 24, 17,
        3, 22, 15, 13, 27,  4, 25, 31, 34, 36, 33]), 'cur_cost': 93557.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:59,092 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 79619.00)
2025-08-05 10:28:59,092 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:59,092 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:59,092 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:59,094 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 45
2025-08-05 10:28:59,094 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,094 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,094 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,095 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,095 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11448.0, 路径长度: 45, 收集中间解: 3
2025-08-05 10:28:59,095 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 4, 21, 17, 19, 18, 20, 14, 11, 13, 15, 12, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 1, 6], 'cur_cost': 11448.0, 'intermediate_solutions': [{'tour': [0, 11, 14, 12, 21, 18, 20, 19, 17, 13, 15, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 29, 31, 22, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 11394.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 14, 12, 21, 18, 20, 19, 17, 13, 15, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 5, 9, 7, 8, 10, 22, 29, 31, 3, 2, 1, 4, 6], 'cur_cost': 18896.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 14, 12, 21, 18, 20, 19, 25, 17, 13, 15, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 15691.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:59,095 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 11448.00)
2025-08-05 10:28:59,095 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:28:59,095 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:59,095 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:59,096 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 82961.0
2025-08-05 10:28:59,108 - ExploitationExpert - INFO - res_population_num: 8
2025-08-05 10:28:59,108 - ExploitationExpert - INFO - res_population_costs: [10973.0, 10973, 10973, 10973, 10979.0, 10979, 10973.0, 10973]
2025-08-05 10:28:59,108 - ExploitationExpert - INFO - res_populations: [array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 43, 33, 38, 39, 44, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 44, 39,
       38, 33, 43, 36, 35, 32, 30, 28, 24, 25, 22, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64), array([ 0,  1,  4,  6,  5,  3,  2,  7,  9,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 39, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 25, 22, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64)]
2025-08-05 10:28:59,111 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:59,111 - ExploitationExpert - INFO - populations: [{'tour': [12, 38, 13, 35, 6, 18, 19, 0, 43, 1, 37, 17, 7, 39, 11, 30, 20, 21, 15, 16, 36, 44, 42, 41, 31, 26, 29, 22, 24, 25, 27, 2, 4, 3, 28, 10, 5, 33, 34, 9, 14, 40, 23, 8, 32], 'cur_cost': 71910.0}, {'tour': [32, 11, 8, 12, 7, 9, 5, 19, 4, 18, 15, 2, 42, 41, 23, 44, 14, 16, 1, 20, 13, 35, 28, 33, 34, 0, 37, 39, 24, 26, 36, 40, 38, 17, 3, 22, 30, 10, 27, 25, 29, 31, 21, 6, 43], 'cur_cost': 76479.0}, {'tour': [0, 6, 17, 1, 4, 9, 7, 3, 5, 8, 2, 10, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15425.0}, {'tour': array([ 4,  6, 33, 13,  9, 34, 43, 19, 22, 32, 28, 25, 10, 21, 26, 40, 12,
       11,  7, 41,  8, 42, 44, 39, 36, 30, 27,  1, 37,  2, 29, 23, 20, 24,
       18, 14, 15, 35, 17, 38,  0, 16,  3, 31,  5], dtype=int64), 'cur_cost': 79619.0}, {'tour': [0, 4, 21, 17, 19, 18, 20, 14, 11, 13, 15, 12, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 1, 6], 'cur_cost': 11448.0}, {'tour': array([10, 29, 22, 27, 15, 40, 43, 35, 16, 23, 34, 19,  2,  0, 30,  6,  3,
       12, 28,  5, 20, 41,  9, 31,  4, 18, 11, 37, 42, 32, 17,  7, 44,  1,
       14, 25, 24,  8, 13, 36, 39, 33, 38, 21, 26], dtype=int64), 'cur_cost': 82961.0}, {'tour': [0, 9, 7, 20, 14, 11, 13, 17, 19, 18, 21, 12, 16, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 1, 4, 6, 5, 3, 2], 'cur_cost': 11538.0}, {'tour': [0, 7, 3, 16, 21, 12, 20, 14, 11, 13, 17, 19, 18, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 1, 4, 6, 5, 9, 2], 'cur_cost': 11524.0}, {'tour': [35, 12, 21, 14, 36, 43, 38, 17, 19, 34, 18, 23, 26, 31, 27, 42, 16, 40, 39, 33, 20, 37, 29, 11, 10, 0, 7, 1, 8, 15, 44, 32, 24, 25, 28, 13, 2, 5, 6, 3, 4, 9, 41, 30, 22], 'cur_cost': 45261.0}, {'tour': [10, 3, 18, 2, 40, 19, 6, 4, 1, 17, 43, 11, 30, 14, 21, 15, 12, 13, 16, 42, 41, 32, 29, 22, 24, 25, 27, 31, 28, 0, 5, 8, 34, 36, 33, 9, 23, 20, 44, 37, 7, 26, 35, 39, 38], 'cur_cost': 55262.0}]
2025-08-05 10:28:59,112 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:59,112 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 253, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 253, 'cache_hits': 0, 'similarity_calculations': 1215, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:59,113 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([10, 29, 22, 27, 15, 40, 43, 35, 16, 23, 34, 19,  2,  0, 30,  6,  3,
       12, 28,  5, 20, 41,  9, 31,  4, 18, 11, 37, 42, 32, 17,  7, 44,  1,
       14, 25, 24,  8, 13, 36, 39, 33, 38, 21, 26], dtype=int64), 'cur_cost': 82961.0, 'intermediate_solutions': [{'tour': array([40, 10, 32, 20,  6, 26,  4, 34, 33, 42, 18, 19, 17, 36, 22, 14, 16,
       27, 12, 21, 15,  1, 37,  2, 30, 28, 35, 23,  0, 43, 25,  7, 13, 38,
       41,  8, 44, 39,  9,  5, 24, 11, 29,  3, 31]), 'cur_cost': 97061.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([20, 40, 10, 32,  6, 26,  4, 34, 33, 42, 18, 19, 17, 36, 22, 14, 16,
       27, 12, 21, 15,  1, 37,  2, 30, 28, 35, 23,  0, 43, 25,  7, 13, 38,
       41,  8, 44, 39,  9,  5, 24, 11, 29,  3, 31]), 'cur_cost': 97129.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 6, 20, 40, 10, 32, 26,  4, 34, 33, 42, 18, 19, 17, 36, 22, 14, 16,
       27, 12, 21, 15,  1, 37,  2, 30, 28, 35, 23,  0, 43, 25,  7, 13, 38,
       41,  8, 44, 39,  9,  5, 24, 11, 29,  3, 31]), 'cur_cost': 93431.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([32, 20, 40, 10,  6, 26,  4, 34, 33, 42, 18, 19, 17, 36, 22, 14, 16,
       27, 12, 21, 15,  1, 37,  2, 30, 28, 35, 23,  0, 43, 25,  7, 13, 38,
       41,  8, 44, 39,  9,  5, 24, 11, 29,  3, 31]), 'cur_cost': 89784.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([32,  6, 20, 40, 10, 26,  4, 34, 33, 42, 18, 19, 17, 36, 22, 14, 16,
       27, 12, 21, 15,  1, 37,  2, 30, 28, 35, 23,  0, 43, 25,  7, 13, 38,
       41,  8, 44, 39,  9,  5, 24, 11, 29,  3, 31]), 'cur_cost': 93450.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:59,113 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 82961.00)
2025-08-05 10:28:59,113 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:59,113 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:59,113 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:59,114 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 45
2025-08-05 10:28:59,115 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,115 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,115 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,115 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,115 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15122.0, 路径长度: 45, 收集中间解: 3
2025-08-05 10:28:59,115 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 1, 22, 17, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 10, 8, 7, 9, 5, 3, 2, 6, 4], 'cur_cost': 15122.0, 'intermediate_solutions': [{'tour': [0, 9, 7, 20, 14, 11, 13, 17, 19, 18, 21, 12, 16, 15, 41, 42, 34, 3, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 1, 4, 6, 5, 44, 2], 'cur_cost': 23730.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 7, 20, 14, 11, 13, 17, 19, 18, 21, 12, 16, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 1, 8, 10, 22, 29, 31, 26, 27, 25, 24, 28, 30, 32, 23, 40, 38, 4, 6, 5, 3, 2], 'cur_cost': 17650.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 7, 20, 14, 11, 13, 17, 19, 18, 21, 12, 16, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 31, 29, 22, 10, 8, 1, 4, 6, 5, 3, 2, 26], 'cur_cost': 18940.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:59,116 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 15122.00)
2025-08-05 10:28:59,116 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:59,116 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:59,116 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:59,117 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 45
2025-08-05 10:28:59,117 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,117 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,117 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,118 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,118 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11360.0, 路径长度: 45, 收集中间解: 3
2025-08-05 10:28:59,118 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 20, 15, 14, 11, 13, 17, 19, 18, 21, 12, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 11360.0, 'intermediate_solutions': [{'tour': [0, 7, 3, 16, 21, 12, 20, 14, 11, 13, 17, 19, 18, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 22, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 40, 10, 8, 1, 4, 6, 5, 9, 2], 'cur_cost': 13789.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 3, 16, 21, 12, 20, 14, 11, 13, 17, 19, 18, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 22, 29, 31, 10, 8, 1, 4, 6, 5, 9, 2], 'cur_cost': 11488.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 3, 16, 21, 12, 14, 11, 13, 17, 19, 18, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 1, 4, 6, 5, 20, 9, 2], 'cur_cost': 15759.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:59,118 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 11360.00)
2025-08-05 10:28:59,118 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:59,118 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:59,118 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:59,119 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 45
2025-08-05 10:28:59,119 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,120 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,120 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,120 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,120 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15363.0, 路径长度: 45, 收集中间解: 3
2025-08-05 10:28:59,120 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 17, 1, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 6, 4], 'cur_cost': 15363.0, 'intermediate_solutions': [{'tour': [35, 12, 21, 14, 36, 43, 38, 17, 19, 34, 18, 23, 26, 31, 27, 42, 16, 40, 39, 33, 20, 37, 29, 11, 10, 32, 7, 1, 8, 15, 44, 0, 24, 25, 28, 13, 2, 5, 6, 3, 4, 9, 41, 30, 22], 'cur_cost': 56478.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [35, 12, 21, 14, 36, 43, 38, 17, 19, 34, 18, 23, 26, 40, 16, 42, 27, 31, 39, 33, 20, 37, 29, 11, 10, 0, 7, 1, 8, 15, 44, 32, 24, 25, 28, 13, 2, 5, 6, 3, 4, 9, 41, 30, 22], 'cur_cost': 51191.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [35, 12, 33, 21, 14, 36, 43, 38, 17, 19, 34, 18, 23, 26, 31, 27, 42, 16, 40, 39, 20, 37, 29, 11, 10, 0, 7, 1, 8, 15, 44, 32, 24, 25, 28, 13, 2, 5, 6, 3, 4, 9, 41, 30, 22], 'cur_cost': 48026.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:59,120 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 15363.00)
2025-08-05 10:28:59,121 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:28:59,121 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:59,121 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:59,121 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 88122.0
2025-08-05 10:28:59,132 - ExploitationExpert - INFO - res_population_num: 8
2025-08-05 10:28:59,133 - ExploitationExpert - INFO - res_population_costs: [10973.0, 10973, 10973, 10973, 10979.0, 10979, 10973.0, 10973]
2025-08-05 10:28:59,133 - ExploitationExpert - INFO - res_populations: [array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 43, 33, 38, 39, 44, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 44, 39,
       38, 33, 43, 36, 35, 32, 30, 28, 24, 25, 22, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64), array([ 0,  1,  4,  6,  5,  3,  2,  7,  9,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 39, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 25, 22, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64)]
2025-08-05 10:28:59,135 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:59,135 - ExploitationExpert - INFO - populations: [{'tour': [12, 38, 13, 35, 6, 18, 19, 0, 43, 1, 37, 17, 7, 39, 11, 30, 20, 21, 15, 16, 36, 44, 42, 41, 31, 26, 29, 22, 24, 25, 27, 2, 4, 3, 28, 10, 5, 33, 34, 9, 14, 40, 23, 8, 32], 'cur_cost': 71910.0}, {'tour': [32, 11, 8, 12, 7, 9, 5, 19, 4, 18, 15, 2, 42, 41, 23, 44, 14, 16, 1, 20, 13, 35, 28, 33, 34, 0, 37, 39, 24, 26, 36, 40, 38, 17, 3, 22, 30, 10, 27, 25, 29, 31, 21, 6, 43], 'cur_cost': 76479.0}, {'tour': [0, 6, 17, 1, 4, 9, 7, 3, 5, 8, 2, 10, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15425.0}, {'tour': array([ 4,  6, 33, 13,  9, 34, 43, 19, 22, 32, 28, 25, 10, 21, 26, 40, 12,
       11,  7, 41,  8, 42, 44, 39, 36, 30, 27,  1, 37,  2, 29, 23, 20, 24,
       18, 14, 15, 35, 17, 38,  0, 16,  3, 31,  5], dtype=int64), 'cur_cost': 79619.0}, {'tour': [0, 4, 21, 17, 19, 18, 20, 14, 11, 13, 15, 12, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 1, 6], 'cur_cost': 11448.0}, {'tour': array([10, 29, 22, 27, 15, 40, 43, 35, 16, 23, 34, 19,  2,  0, 30,  6,  3,
       12, 28,  5, 20, 41,  9, 31,  4, 18, 11, 37, 42, 32, 17,  7, 44,  1,
       14, 25, 24,  8, 13, 36, 39, 33, 38, 21, 26], dtype=int64), 'cur_cost': 82961.0}, {'tour': [0, 1, 22, 17, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 10, 8, 7, 9, 5, 3, 2, 6, 4], 'cur_cost': 15122.0}, {'tour': [0, 20, 15, 14, 11, 13, 17, 19, 18, 21, 12, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 11360.0}, {'tour': [0, 17, 1, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 6, 4], 'cur_cost': 15363.0}, {'tour': array([39, 25, 12, 38, 27,  5, 18, 21,  2, 23,  1, 20, 14, 26, 43, 10,  0,
       22, 41, 29,  4,  7,  6, 40,  9, 44, 24, 34, 13, 35, 37, 19, 16, 30,
       28, 17, 11, 15,  8, 31, 32, 42, 33,  3, 36], dtype=int64), 'cur_cost': 88122.0}]
2025-08-05 10:28:59,136 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:59,137 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 254, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 254, 'cache_hits': 0, 'similarity_calculations': 1221, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:59,137 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([39, 25, 12, 38, 27,  5, 18, 21,  2, 23,  1, 20, 14, 26, 43, 10,  0,
       22, 41, 29,  4,  7,  6, 40,  9, 44, 24, 34, 13, 35, 37, 19, 16, 30,
       28, 17, 11, 15,  8, 31, 32, 42, 33,  3, 36], dtype=int64), 'cur_cost': 88122.0, 'intermediate_solutions': [{'tour': array([18,  3, 10,  2, 40, 19,  6,  4,  1, 17, 43, 11, 30, 14, 21, 15, 12,
       13, 16, 42, 41, 32, 29, 22, 24, 25, 27, 31, 28,  0,  5,  8, 34, 36,
       33,  9, 23, 20, 44, 37,  7, 26, 35, 39, 38]), 'cur_cost': 51617.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2, 18,  3, 10, 40, 19,  6,  4,  1, 17, 43, 11, 30, 14, 21, 15, 12,
       13, 16, 42, 41, 32, 29, 22, 24, 25, 27, 31, 28,  0,  5,  8, 34, 36,
       33,  9, 23, 20, 44, 37,  7, 26, 35, 39, 38]), 'cur_cost': 55264.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([40,  2, 18,  3, 10, 19,  6,  4,  1, 17, 43, 11, 30, 14, 21, 15, 12,
       13, 16, 42, 41, 32, 29, 22, 24, 25, 27, 31, 28,  0,  5,  8, 34, 36,
       33,  9, 23, 20, 44, 37,  7, 26, 35, 39, 38]), 'cur_cost': 52860.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([10,  2, 18,  3, 40, 19,  6,  4,  1, 17, 43, 11, 30, 14, 21, 15, 12,
       13, 16, 42, 41, 32, 29, 22, 24, 25, 27, 31, 28,  0,  5,  8, 34, 36,
       33,  9, 23, 20, 44, 37,  7, 26, 35, 39, 38]), 'cur_cost': 55281.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([10, 40,  2, 18,  3, 19,  6,  4,  1, 17, 43, 11, 30, 14, 21, 15, 12,
       13, 16, 42, 41, 32, 29, 22, 24, 25, 27, 31, 28,  0,  5,  8, 34, 36,
       33,  9, 23, 20, 44, 37,  7, 26, 35, 39, 38]), 'cur_cost': 58841.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:59,138 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 88122.00)
2025-08-05 10:28:59,138 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:59,138 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:59,140 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [12, 38, 13, 35, 6, 18, 19, 0, 43, 1, 37, 17, 7, 39, 11, 30, 20, 21, 15, 16, 36, 44, 42, 41, 31, 26, 29, 22, 24, 25, 27, 2, 4, 3, 28, 10, 5, 33, 34, 9, 14, 40, 23, 8, 32], 'cur_cost': 71910.0, 'intermediate_solutions': [{'tour': [0, 6, 8, 2, 7, 9, 5, 3, 1, 4, 10, 17, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 22, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 35], 'cur_cost': 19384.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 8, 2, 7, 9, 5, 3, 1, 4, 10, 17, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 32, 23, 40, 38, 35, 36, 43, 33, 39, 37, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 17180.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 8, 2, 9, 5, 3, 1, 4, 10, 17, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 7, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 17359.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [32, 11, 8, 12, 7, 9, 5, 19, 4, 18, 15, 2, 42, 41, 23, 44, 14, 16, 1, 20, 13, 35, 28, 33, 34, 0, 37, 39, 24, 26, 36, 40, 38, 17, 3, 22, 30, 10, 27, 25, 29, 31, 21, 6, 43], 'cur_cost': 76479.0, 'intermediate_solutions': [{'tour': [0, 3, 18, 5, 7, 9, 8, 6, 4, 1, 10, 2, 17, 19, 11, 14, 20, 21, 12, 16, 13, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15752.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 18, 8, 7, 9, 5, 6, 4, 1, 10, 2, 17, 19, 11, 14, 20, 21, 12, 16, 13, 15, 41, 42, 34, 23, 40, 38, 35, 36, 43, 33, 39, 37, 44, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 21459.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 18, 8, 7, 9, 5, 6, 4, 10, 2, 17, 19, 11, 14, 20, 21, 12, 16, 13, 15, 41, 42, 34, 44, 1, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 21787.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 17, 1, 4, 9, 7, 3, 5, 8, 2, 10, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15425.0, 'intermediate_solutions': [{'tour': [10, 9, 13, 35, 40, 38, 19, 12, 18, 8, 17, 37, 43, 32, 23, 30, 14, 28, 1, 20, 39, 44, 21, 33, 31, 26, 29, 22, 41, 16, 11, 2, 4, 3, 7, 0, 5, 15, 34, 27, 25, 24, 36, 42, 6], 'cur_cost': 50935.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 9, 13, 35, 40, 38, 19, 12, 18, 8, 17, 37, 43, 32, 23, 30, 14, 16, 1, 20, 22, 29, 26, 31, 33, 21, 44, 39, 41, 28, 11, 2, 4, 3, 7, 0, 5, 15, 34, 27, 25, 24, 36, 42, 6], 'cur_cost': 48671.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 9, 13, 35, 40, 38, 19, 12, 18, 8, 17, 37, 43, 32, 23, 30, 14, 16, 1, 20, 39, 44, 21, 33, 31, 26, 29, 22, 28, 11, 2, 4, 3, 41, 7, 0, 5, 15, 34, 27, 25, 24, 36, 42, 6], 'cur_cost': 50995.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 4,  6, 33, 13,  9, 34, 43, 19, 22, 32, 28, 25, 10, 21, 26, 40, 12,
       11,  7, 41,  8, 42, 44, 39, 36, 30, 27,  1, 37,  2, 29, 23, 20, 24,
       18, 14, 15, 35, 17, 38,  0, 16,  3, 31,  5], dtype=int64), 'cur_cost': 79619.0, 'intermediate_solutions': [{'tour': array([ 8, 38, 12, 23, 14,  6, 10,  0, 43,  9, 37, 29,  7, 39, 30, 18, 42,
        5,  2, 44, 20,  1, 28, 16, 35, 41, 11, 32, 19, 26, 21, 40, 24, 17,
        3, 22, 15, 13, 27,  4, 25, 31, 34, 36, 33]), 'cur_cost': 93961.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([23,  8, 38, 12, 14,  6, 10,  0, 43,  9, 37, 29,  7, 39, 30, 18, 42,
        5,  2, 44, 20,  1, 28, 16, 35, 41, 11, 32, 19, 26, 21, 40, 24, 17,
        3, 22, 15, 13, 27,  4, 25, 31, 34, 36, 33]), 'cur_cost': 93436.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14, 23,  8, 38, 12,  6, 10,  0, 43,  9, 37, 29,  7, 39, 30, 18, 42,
        5,  2, 44, 20,  1, 28, 16, 35, 41, 11, 32, 19, 26, 21, 40, 24, 17,
        3, 22, 15, 13, 27,  4, 25, 31, 34, 36, 33]), 'cur_cost': 93981.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([12, 23,  8, 38, 14,  6, 10,  0, 43,  9, 37, 29,  7, 39, 30, 18, 42,
        5,  2, 44, 20,  1, 28, 16, 35, 41, 11, 32, 19, 26, 21, 40, 24, 17,
        3, 22, 15, 13, 27,  4, 25, 31, 34, 36, 33]), 'cur_cost': 93985.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([12, 14, 23,  8, 38,  6, 10,  0, 43,  9, 37, 29,  7, 39, 30, 18, 42,
        5,  2, 44, 20,  1, 28, 16, 35, 41, 11, 32, 19, 26, 21, 40, 24, 17,
        3, 22, 15, 13, 27,  4, 25, 31, 34, 36, 33]), 'cur_cost': 93557.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 21, 17, 19, 18, 20, 14, 11, 13, 15, 12, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 1, 6], 'cur_cost': 11448.0, 'intermediate_solutions': [{'tour': [0, 11, 14, 12, 21, 18, 20, 19, 17, 13, 15, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 29, 31, 22, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 11394.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 14, 12, 21, 18, 20, 19, 17, 13, 15, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 5, 9, 7, 8, 10, 22, 29, 31, 3, 2, 1, 4, 6], 'cur_cost': 18896.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 14, 12, 21, 18, 20, 19, 25, 17, 13, 15, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 15691.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([10, 29, 22, 27, 15, 40, 43, 35, 16, 23, 34, 19,  2,  0, 30,  6,  3,
       12, 28,  5, 20, 41,  9, 31,  4, 18, 11, 37, 42, 32, 17,  7, 44,  1,
       14, 25, 24,  8, 13, 36, 39, 33, 38, 21, 26], dtype=int64), 'cur_cost': 82961.0, 'intermediate_solutions': [{'tour': array([40, 10, 32, 20,  6, 26,  4, 34, 33, 42, 18, 19, 17, 36, 22, 14, 16,
       27, 12, 21, 15,  1, 37,  2, 30, 28, 35, 23,  0, 43, 25,  7, 13, 38,
       41,  8, 44, 39,  9,  5, 24, 11, 29,  3, 31]), 'cur_cost': 97061.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([20, 40, 10, 32,  6, 26,  4, 34, 33, 42, 18, 19, 17, 36, 22, 14, 16,
       27, 12, 21, 15,  1, 37,  2, 30, 28, 35, 23,  0, 43, 25,  7, 13, 38,
       41,  8, 44, 39,  9,  5, 24, 11, 29,  3, 31]), 'cur_cost': 97129.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 6, 20, 40, 10, 32, 26,  4, 34, 33, 42, 18, 19, 17, 36, 22, 14, 16,
       27, 12, 21, 15,  1, 37,  2, 30, 28, 35, 23,  0, 43, 25,  7, 13, 38,
       41,  8, 44, 39,  9,  5, 24, 11, 29,  3, 31]), 'cur_cost': 93431.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([32, 20, 40, 10,  6, 26,  4, 34, 33, 42, 18, 19, 17, 36, 22, 14, 16,
       27, 12, 21, 15,  1, 37,  2, 30, 28, 35, 23,  0, 43, 25,  7, 13, 38,
       41,  8, 44, 39,  9,  5, 24, 11, 29,  3, 31]), 'cur_cost': 89784.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([32,  6, 20, 40, 10, 26,  4, 34, 33, 42, 18, 19, 17, 36, 22, 14, 16,
       27, 12, 21, 15,  1, 37,  2, 30, 28, 35, 23,  0, 43, 25,  7, 13, 38,
       41,  8, 44, 39,  9,  5, 24, 11, 29,  3, 31]), 'cur_cost': 93450.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 22, 17, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 10, 8, 7, 9, 5, 3, 2, 6, 4], 'cur_cost': 15122.0, 'intermediate_solutions': [{'tour': [0, 9, 7, 20, 14, 11, 13, 17, 19, 18, 21, 12, 16, 15, 41, 42, 34, 3, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 1, 4, 6, 5, 44, 2], 'cur_cost': 23730.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 7, 20, 14, 11, 13, 17, 19, 18, 21, 12, 16, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 1, 8, 10, 22, 29, 31, 26, 27, 25, 24, 28, 30, 32, 23, 40, 38, 4, 6, 5, 3, 2], 'cur_cost': 17650.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 7, 20, 14, 11, 13, 17, 19, 18, 21, 12, 16, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 31, 29, 22, 10, 8, 1, 4, 6, 5, 3, 2, 26], 'cur_cost': 18940.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 15, 14, 11, 13, 17, 19, 18, 21, 12, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 11360.0, 'intermediate_solutions': [{'tour': [0, 7, 3, 16, 21, 12, 20, 14, 11, 13, 17, 19, 18, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 22, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 40, 10, 8, 1, 4, 6, 5, 9, 2], 'cur_cost': 13789.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 3, 16, 21, 12, 20, 14, 11, 13, 17, 19, 18, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 22, 29, 31, 10, 8, 1, 4, 6, 5, 9, 2], 'cur_cost': 11488.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 3, 16, 21, 12, 14, 11, 13, 17, 19, 18, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 1, 4, 6, 5, 20, 9, 2], 'cur_cost': 15759.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 1, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 6, 4], 'cur_cost': 15363.0, 'intermediate_solutions': [{'tour': [35, 12, 21, 14, 36, 43, 38, 17, 19, 34, 18, 23, 26, 31, 27, 42, 16, 40, 39, 33, 20, 37, 29, 11, 10, 32, 7, 1, 8, 15, 44, 0, 24, 25, 28, 13, 2, 5, 6, 3, 4, 9, 41, 30, 22], 'cur_cost': 56478.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [35, 12, 21, 14, 36, 43, 38, 17, 19, 34, 18, 23, 26, 40, 16, 42, 27, 31, 39, 33, 20, 37, 29, 11, 10, 0, 7, 1, 8, 15, 44, 32, 24, 25, 28, 13, 2, 5, 6, 3, 4, 9, 41, 30, 22], 'cur_cost': 51191.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [35, 12, 33, 21, 14, 36, 43, 38, 17, 19, 34, 18, 23, 26, 31, 27, 42, 16, 40, 39, 20, 37, 29, 11, 10, 0, 7, 1, 8, 15, 44, 32, 24, 25, 28, 13, 2, 5, 6, 3, 4, 9, 41, 30, 22], 'cur_cost': 48026.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([39, 25, 12, 38, 27,  5, 18, 21,  2, 23,  1, 20, 14, 26, 43, 10,  0,
       22, 41, 29,  4,  7,  6, 40,  9, 44, 24, 34, 13, 35, 37, 19, 16, 30,
       28, 17, 11, 15,  8, 31, 32, 42, 33,  3, 36], dtype=int64), 'cur_cost': 88122.0, 'intermediate_solutions': [{'tour': array([18,  3, 10,  2, 40, 19,  6,  4,  1, 17, 43, 11, 30, 14, 21, 15, 12,
       13, 16, 42, 41, 32, 29, 22, 24, 25, 27, 31, 28,  0,  5,  8, 34, 36,
       33,  9, 23, 20, 44, 37,  7, 26, 35, 39, 38]), 'cur_cost': 51617.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2, 18,  3, 10, 40, 19,  6,  4,  1, 17, 43, 11, 30, 14, 21, 15, 12,
       13, 16, 42, 41, 32, 29, 22, 24, 25, 27, 31, 28,  0,  5,  8, 34, 36,
       33,  9, 23, 20, 44, 37,  7, 26, 35, 39, 38]), 'cur_cost': 55264.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([40,  2, 18,  3, 10, 19,  6,  4,  1, 17, 43, 11, 30, 14, 21, 15, 12,
       13, 16, 42, 41, 32, 29, 22, 24, 25, 27, 31, 28,  0,  5,  8, 34, 36,
       33,  9, 23, 20, 44, 37,  7, 26, 35, 39, 38]), 'cur_cost': 52860.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([10,  2, 18,  3, 40, 19,  6,  4,  1, 17, 43, 11, 30, 14, 21, 15, 12,
       13, 16, 42, 41, 32, 29, 22, 24, 25, 27, 31, 28,  0,  5,  8, 34, 36,
       33,  9, 23, 20, 44, 37,  7, 26, 35, 39, 38]), 'cur_cost': 55281.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([10, 40,  2, 18,  3, 19,  6,  4,  1, 17, 43, 11, 30, 14, 21, 15, 12,
       13, 16, 42, 41, 32, 29, 22, 24, 25, 27, 31, 28,  0,  5,  8, 34, 36,
       33,  9, 23, 20, 44, 37,  7, 26, 35, 39, 38]), 'cur_cost': 58841.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:28:59,140 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:59,141 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:59,144 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=11360.000, 多样性=0.944
2025-08-05 10:28:59,144 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 10:28:59,145 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 10:28:59,145 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:59,146 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.036831456837985665, 'best_improvement': -0.0034449253599505344}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.0}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.12846396804038998, 'recent_improvements': [-0.13256854239742438, -0.022183804404113733, 0.12435939368335559], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 8, 'new_count': 8, 'count_change': 0, 'old_best_cost': 10973.0, 'new_best_cost': 10973.0, 'quality_improvement': 0.0, 'old_diversity': 0.7626984126984127, 'new_diversity': 0.7626984126984127, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:59,146 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 10:28:59,147 - __main__ - INFO - composite8_45 开始进化第 4 代
2025-08-05 10:28:59,147 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 10:28:59,147 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:59,149 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=11360.000, 多样性=0.944
2025-08-05 10:28:59,150 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:59,152 - PathExpert - INFO - 路径结构分析完成: 公共边数量=13, 路径相似性=0.944
2025-08-05 10:28:59,152 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:59,156 - EliteExpert - INFO - 精英解分析完成: 精英解数量=8, 多样性=0.763
2025-08-05 10:28:59,159 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 10:28:59,160 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:59,160 - LandscapeExpert - INFO - 添加精英解数据: 8个精英解
2025-08-05 10:28:59,160 - LandscapeExpert - INFO - 数据提取成功: 18个路径, 18个适应度值
2025-08-05 10:28:59,202 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.389, 适应度梯度: -9379.933, 聚类评分: 0.000, 覆盖率: 0.117, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:59,203 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 10:28:59,203 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:59,203 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite8_45
2025-08-05 10:28:59,210 - visualization.landscape_visualizer - INFO - 插值约束: 212 个点被约束到最小值 10973.00
2025-08-05 10:28:59,211 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=5.2%, 梯度: 4237.23 → 4016.70
2025-08-05 10:28:59,379 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite8_45\landscape_composite8_45_iter_99_20250805_102859.html
2025-08-05 10:28:59,441 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite8_45\dashboard_composite8_45_iter_99_20250805_102859.html
2025-08-05 10:28:59,442 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 99
2025-08-05 10:28:59,442 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 10:28:59,442 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2830秒
2025-08-05 10:28:59,443 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3888888888888889, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -9379.933333333334, 'local_optima_density': 0.3888888888888889, 'gradient_variance': 993509531.6977779, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.117, 'fitness_entropy': 0.6375814913924284, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -9379.933)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.117)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360939.203681, 'performance_metrics': {}}}
2025-08-05 10:28:59,443 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:59,443 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:59,443 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:59,443 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:59,444 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:59,444 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 10:28:59,444 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:59,444 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:59,444 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:59,444 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:59,444 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:59,444 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:59,444 - experts.management.collaboration_manager - INFO - 识别精英个体: {4, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:59,445 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:59,445 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:59,445 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:59,446 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 45
2025-08-05 10:28:59,446 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,447 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,447 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,447 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,447 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11364.0, 路径长度: 45, 收集中间解: 3
2025-08-05 10:28:59,447 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 15, 18, 20, 14, 11, 13, 17, 19, 16, 21, 12, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 6, 4], 'cur_cost': 11364.0, 'intermediate_solutions': [{'tour': [12, 38, 13, 35, 6, 18, 19, 0, 43, 1, 37, 17, 7, 39, 11, 30, 20, 21, 15, 16, 36, 44, 42, 41, 31, 26, 29, 22, 24, 25, 27, 2, 4, 3, 28, 10, 5, 33, 34, 9, 14, 40, 32, 8, 23], 'cur_cost': 71898.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [12, 38, 13, 35, 6, 18, 19, 0, 43, 1, 37, 17, 7, 39, 11, 30, 20, 21, 33, 5, 10, 28, 3, 4, 2, 27, 25, 24, 22, 29, 26, 31, 41, 42, 44, 36, 16, 15, 34, 9, 14, 40, 23, 8, 32], 'cur_cost': 74612.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [12, 38, 13, 35, 6, 18, 19, 0, 43, 1, 37, 17, 7, 39, 11, 30, 20, 21, 15, 16, 36, 44, 42, 41, 31, 26, 29, 22, 24, 25, 27, 2, 4, 3, 28, 10, 5, 33, 34, 9, 14, 40, 32, 23, 8], 'cur_cost': 68209.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:59,448 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 11364.00)
2025-08-05 10:28:59,448 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:59,448 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:59,448 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:59,449 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 45
2025-08-05 10:28:59,449 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,449 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,450 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,450 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,450 - ExplorationExpert - INFO - 探索路径生成完成，成本: 71254.0, 路径长度: 45, 收集中间解: 3
2025-08-05 10:28:59,450 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [32, 11, 17, 18, 7, 34, 5, 3, 4, 8, 2, 25, 19, 0, 20, 14, 12, 13, 15, 41, 21, 16, 42, 33, 36, 30, 27, 1, 37, 26, 29, 40, 44, 24, 23, 10, 22, 28, 9, 38, 39, 31, 6, 35, 43], 'cur_cost': 71254.0, 'intermediate_solutions': [{'tour': [32, 11, 8, 12, 7, 9, 5, 19, 4, 18, 15, 2, 42, 41, 23, 44, 14, 16, 1, 20, 13, 35, 28, 33, 34, 0, 17, 39, 24, 26, 36, 40, 38, 37, 3, 22, 30, 10, 27, 25, 29, 31, 21, 6, 43], 'cur_cost': 76472.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [32, 11, 42, 2, 15, 18, 4, 19, 5, 9, 7, 12, 8, 41, 23, 44, 14, 16, 1, 20, 13, 35, 28, 33, 34, 0, 37, 39, 24, 26, 36, 40, 38, 17, 3, 22, 30, 10, 27, 25, 29, 31, 21, 6, 43], 'cur_cost': 78774.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [32, 11, 8, 12, 7, 9, 5, 19, 4, 18, 15, 2, 42, 41, 0, 23, 44, 14, 16, 1, 20, 13, 35, 28, 33, 34, 37, 39, 24, 26, 36, 40, 38, 17, 3, 22, 30, 10, 27, 25, 29, 31, 21, 6, 43], 'cur_cost': 74220.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:59,451 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 71254.00)
2025-08-05 10:28:59,451 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:59,451 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:59,451 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:59,454 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 45
2025-08-05 10:28:59,454 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,454 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,454 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,454 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,455 - ExplorationExpert - INFO - 探索路径生成完成，成本: 40611.0, 路径长度: 45, 收集中间解: 3
2025-08-05 10:28:59,455 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [32, 30, 26, 29, 21, 34, 35, 20, 11, 44, 17, 12, 16, 43, 23, 28, 22, 42, 40, 37, 36, 39, 13, 0, 10, 4, 6, 1, 18, 14, 33, 27, 15, 31, 41, 38, 19, 2, 8, 9, 7, 3, 5, 25, 24], 'cur_cost': 40611.0, 'intermediate_solutions': [{'tour': [0, 6, 17, 1, 4, 9, 7, 3, 5, 8, 2, 10, 41, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 19, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 20456.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 36, 43, 33, 39, 37, 44, 34, 41, 42, 16, 21, 12, 15, 13, 11, 14, 20, 18, 19, 10, 2, 8, 5, 3, 7, 9, 4, 1, 17, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 17833.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 17, 1, 4, 9, 7, 3, 5, 8, 2, 10, 19, 18, 20, 24, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 25, 27, 26, 31, 29, 22], 'cur_cost': 19711.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:59,455 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 40611.00)
2025-08-05 10:28:59,455 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:28:59,455 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:59,455 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:59,456 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 85296.0
2025-08-05 10:28:59,472 - ExploitationExpert - INFO - res_population_num: 9
2025-08-05 10:28:59,472 - ExploitationExpert - INFO - res_population_costs: [10973.0, 10973, 10973, 10973, 10973.0, 10973, 10979.0, 10979, 10973]
2025-08-05 10:28:59,472 - ExploitationExpert - INFO - res_populations: [array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  3,  2,  7,  9,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 39, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 25, 22, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 43, 33, 38, 39, 44, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 44, 39,
       38, 33, 43, 36, 35, 32, 30, 28, 24, 25, 22, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64), array([ 0,  1,  4,  6,  5,  3,  2,  7,  9,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 39, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64)]
2025-08-05 10:28:59,476 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:59,476 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 15, 18, 20, 14, 11, 13, 17, 19, 16, 21, 12, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 6, 4], 'cur_cost': 11364.0}, {'tour': [32, 11, 17, 18, 7, 34, 5, 3, 4, 8, 2, 25, 19, 0, 20, 14, 12, 13, 15, 41, 21, 16, 42, 33, 36, 30, 27, 1, 37, 26, 29, 40, 44, 24, 23, 10, 22, 28, 9, 38, 39, 31, 6, 35, 43], 'cur_cost': 71254.0}, {'tour': [32, 30, 26, 29, 21, 34, 35, 20, 11, 44, 17, 12, 16, 43, 23, 28, 22, 42, 40, 37, 36, 39, 13, 0, 10, 4, 6, 1, 18, 14, 33, 27, 15, 31, 41, 38, 19, 2, 8, 9, 7, 3, 5, 25, 24], 'cur_cost': 40611.0}, {'tour': array([15, 26,  8, 43,  3, 28,  7, 44, 33, 39, 23, 18, 29, 21,  1, 38, 34,
       36, 27, 25, 19, 17, 35, 30, 11, 24, 32, 41, 12,  2,  9, 14,  4, 31,
       10,  0,  6, 16, 42, 20, 22, 13, 40, 37,  5], dtype=int64), 'cur_cost': 85296.0}, {'tour': [0, 4, 21, 17, 19, 18, 20, 14, 11, 13, 15, 12, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 1, 6], 'cur_cost': 11448.0}, {'tour': [10, 29, 22, 27, 15, 40, 43, 35, 16, 23, 34, 19, 2, 0, 30, 6, 3, 12, 28, 5, 20, 41, 9, 31, 4, 18, 11, 37, 42, 32, 17, 7, 44, 1, 14, 25, 24, 8, 13, 36, 39, 33, 38, 21, 26], 'cur_cost': 82961.0}, {'tour': [0, 1, 22, 17, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 10, 8, 7, 9, 5, 3, 2, 6, 4], 'cur_cost': 15122.0}, {'tour': [0, 20, 15, 14, 11, 13, 17, 19, 18, 21, 12, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 11360.0}, {'tour': [0, 17, 1, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 6, 4], 'cur_cost': 15363.0}, {'tour': [39, 25, 12, 38, 27, 5, 18, 21, 2, 23, 1, 20, 14, 26, 43, 10, 0, 22, 41, 29, 4, 7, 6, 40, 9, 44, 24, 34, 13, 35, 37, 19, 16, 30, 28, 17, 11, 15, 8, 31, 32, 42, 33, 3, 36], 'cur_cost': 88122.0}]
2025-08-05 10:28:59,477 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:59,477 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 255, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 255, 'cache_hits': 0, 'similarity_calculations': 1228, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:59,477 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([15, 26,  8, 43,  3, 28,  7, 44, 33, 39, 23, 18, 29, 21,  1, 38, 34,
       36, 27, 25, 19, 17, 35, 30, 11, 24, 32, 41, 12,  2,  9, 14,  4, 31,
       10,  0,  6, 16, 42, 20, 22, 13, 40, 37,  5], dtype=int64), 'cur_cost': 85296.0, 'intermediate_solutions': [{'tour': array([33,  6,  4, 13,  9, 34, 43, 19, 22, 32, 28, 25, 10, 21, 26, 40, 12,
       11,  7, 41,  8, 42, 44, 39, 36, 30, 27,  1, 37,  2, 29, 23, 20, 24,
       18, 14, 15, 35, 17, 38,  0, 16,  3, 31,  5]), 'cur_cost': 83295.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([13, 33,  6,  4,  9, 34, 43, 19, 22, 32, 28, 25, 10, 21, 26, 40, 12,
       11,  7, 41,  8, 42, 44, 39, 36, 30, 27,  1, 37,  2, 29, 23, 20, 24,
       18, 14, 15, 35, 17, 38,  0, 16,  3, 31,  5]), 'cur_cost': 79630.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9, 13, 33,  6,  4, 34, 43, 19, 22, 32, 28, 25, 10, 21, 26, 40, 12,
       11,  7, 41,  8, 42, 44, 39, 36, 30, 27,  1, 37,  2, 29, 23, 20, 24,
       18, 14, 15, 35, 17, 38,  0, 16,  3, 31,  5]), 'cur_cost': 79596.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4, 13, 33,  6,  9, 34, 43, 19, 22, 32, 28, 25, 10, 21, 26, 40, 12,
       11,  7, 41,  8, 42, 44, 39, 36, 30, 27,  1, 37,  2, 29, 23, 20, 24,
       18, 14, 15, 35, 17, 38,  0, 16,  3, 31,  5]), 'cur_cost': 79609.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4,  9, 13, 33,  6, 34, 43, 19, 22, 32, 28, 25, 10, 21, 26, 40, 12,
       11,  7, 41,  8, 42, 44, 39, 36, 30, 27,  1, 37,  2, 29, 23, 20, 24,
       18, 14, 15, 35, 17, 38,  0, 16,  3, 31,  5]), 'cur_cost': 79650.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:59,478 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 85296.00)
2025-08-05 10:28:59,478 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:59,478 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:59,478 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:59,479 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 45
2025-08-05 10:28:59,479 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,479 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,480 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,480 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,480 - ExplorationExpert - INFO - 探索路径生成完成，成本: 78524.0, 路径长度: 45, 收集中间解: 3
2025-08-05 10:28:59,480 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [30, 26, 29, 20, 14, 5, 13, 17, 8, 16, 25, 12, 43, 23, 28, 3, 40, 41, 21, 39, 42, 33, 10, 4, 27, 37, 18, 7, 31, 24, 22, 38, 19, 0, 1, 35, 32, 6, 44, 11, 34, 15, 36, 9, 2], 'cur_cost': 78524.0, 'intermediate_solutions': [{'tour': [0, 4, 21, 17, 19, 18, 20, 14, 11, 13, 15, 12, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 24, 23, 32, 30, 28, 40, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 1, 6], 'cur_cost': 17512.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 34, 41, 42, 16, 12, 15, 13, 11, 14, 20, 18, 19, 17, 21, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 1, 6], 'cur_cost': 13744.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 21, 17, 19, 18, 20, 14, 11, 13, 15, 12, 16, 42, 41, 34, 44, 37, 39, 33, 36, 43, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 1, 6], 'cur_cost': 11471.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:59,480 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 78524.00)
2025-08-05 10:28:59,481 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:28:59,481 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:59,481 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:59,481 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 79959.0
2025-08-05 10:28:59,493 - ExploitationExpert - INFO - res_population_num: 9
2025-08-05 10:28:59,493 - ExploitationExpert - INFO - res_population_costs: [10973.0, 10973, 10973, 10973, 10973.0, 10973, 10979.0, 10979, 10973]
2025-08-05 10:28:59,494 - ExploitationExpert - INFO - res_populations: [array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  3,  2,  7,  9,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 39, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 25, 22, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 43, 33, 38, 39, 44, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 44, 39,
       38, 33, 43, 36, 35, 32, 30, 28, 24, 25, 22, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64), array([ 0,  1,  4,  6,  5,  3,  2,  7,  9,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 39, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64)]
2025-08-05 10:28:59,496 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:59,496 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 15, 18, 20, 14, 11, 13, 17, 19, 16, 21, 12, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 6, 4], 'cur_cost': 11364.0}, {'tour': [32, 11, 17, 18, 7, 34, 5, 3, 4, 8, 2, 25, 19, 0, 20, 14, 12, 13, 15, 41, 21, 16, 42, 33, 36, 30, 27, 1, 37, 26, 29, 40, 44, 24, 23, 10, 22, 28, 9, 38, 39, 31, 6, 35, 43], 'cur_cost': 71254.0}, {'tour': [32, 30, 26, 29, 21, 34, 35, 20, 11, 44, 17, 12, 16, 43, 23, 28, 22, 42, 40, 37, 36, 39, 13, 0, 10, 4, 6, 1, 18, 14, 33, 27, 15, 31, 41, 38, 19, 2, 8, 9, 7, 3, 5, 25, 24], 'cur_cost': 40611.0}, {'tour': array([15, 26,  8, 43,  3, 28,  7, 44, 33, 39, 23, 18, 29, 21,  1, 38, 34,
       36, 27, 25, 19, 17, 35, 30, 11, 24, 32, 41, 12,  2,  9, 14,  4, 31,
       10,  0,  6, 16, 42, 20, 22, 13, 40, 37,  5], dtype=int64), 'cur_cost': 85296.0}, {'tour': [30, 26, 29, 20, 14, 5, 13, 17, 8, 16, 25, 12, 43, 23, 28, 3, 40, 41, 21, 39, 42, 33, 10, 4, 27, 37, 18, 7, 31, 24, 22, 38, 19, 0, 1, 35, 32, 6, 44, 11, 34, 15, 36, 9, 2], 'cur_cost': 78524.0}, {'tour': array([16, 34,  5,  1,  0, 29,  7, 12, 17, 31, 40,  8, 42, 28,  4, 41,  3,
        2, 11, 25,  6, 22, 27, 24, 37, 33, 32, 23, 30, 38, 26, 43,  9, 20,
       19, 44, 36, 21, 18, 15, 13, 14, 10, 35, 39], dtype=int64), 'cur_cost': 79959.0}, {'tour': [0, 1, 22, 17, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 10, 8, 7, 9, 5, 3, 2, 6, 4], 'cur_cost': 15122.0}, {'tour': [0, 20, 15, 14, 11, 13, 17, 19, 18, 21, 12, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 11360.0}, {'tour': [0, 17, 1, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 6, 4], 'cur_cost': 15363.0}, {'tour': [39, 25, 12, 38, 27, 5, 18, 21, 2, 23, 1, 20, 14, 26, 43, 10, 0, 22, 41, 29, 4, 7, 6, 40, 9, 44, 24, 34, 13, 35, 37, 19, 16, 30, 28, 17, 11, 15, 8, 31, 32, 42, 33, 3, 36], 'cur_cost': 88122.0}]
2025-08-05 10:28:59,497 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:59,497 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 256, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 256, 'cache_hits': 0, 'similarity_calculations': 1236, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:59,498 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([16, 34,  5,  1,  0, 29,  7, 12, 17, 31, 40,  8, 42, 28,  4, 41,  3,
        2, 11, 25,  6, 22, 27, 24, 37, 33, 32, 23, 30, 38, 26, 43,  9, 20,
       19, 44, 36, 21, 18, 15, 13, 14, 10, 35, 39], dtype=int64), 'cur_cost': 79959.0, 'intermediate_solutions': [{'tour': array([22, 29, 10, 27, 15, 40, 43, 35, 16, 23, 34, 19,  2,  0, 30,  6,  3,
       12, 28,  5, 20, 41,  9, 31,  4, 18, 11, 37, 42, 32, 17,  7, 44,  1,
       14, 25, 24,  8, 13, 36, 39, 33, 38, 21, 26]), 'cur_cost': 82993.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([27, 22, 29, 10, 15, 40, 43, 35, 16, 23, 34, 19,  2,  0, 30,  6,  3,
       12, 28,  5, 20, 41,  9, 31,  4, 18, 11, 37, 42, 32, 17,  7, 44,  1,
       14, 25, 24,  8, 13, 36, 39, 33, 38, 21, 26]), 'cur_cost': 79248.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([15, 27, 22, 29, 10, 40, 43, 35, 16, 23, 34, 19,  2,  0, 30,  6,  3,
       12, 28,  5, 20, 41,  9, 31,  4, 18, 11, 37, 42, 32, 17,  7, 44,  1,
       14, 25, 24,  8, 13, 36, 39, 33, 38, 21, 26]), 'cur_cost': 82867.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([10, 27, 22, 29, 15, 40, 43, 35, 16, 23, 34, 19,  2,  0, 30,  6,  3,
       12, 28,  5, 20, 41,  9, 31,  4, 18, 11, 37, 42, 32, 17,  7, 44,  1,
       14, 25, 24,  8, 13, 36, 39, 33, 38, 21, 26]), 'cur_cost': 82954.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([10, 15, 27, 22, 29, 40, 43, 35, 16, 23, 34, 19,  2,  0, 30,  6,  3,
       12, 28,  5, 20, 41,  9, 31,  4, 18, 11, 37, 42, 32, 17,  7, 44,  1,
       14, 25, 24,  8, 13, 36, 39, 33, 38, 21, 26]), 'cur_cost': 82830.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:59,498 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 79959.00)
2025-08-05 10:28:59,498 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:59,498 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:59,498 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:59,499 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 45
2025-08-05 10:28:59,499 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,500 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,500 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,500 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,500 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11959.0, 路径长度: 45, 收集中间解: 3
2025-08-05 10:28:59,500 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 5, 11, 24, 28, 25, 27, 26, 31, 29, 23, 32, 30, 22, 15, 13, 17, 19, 18, 20, 14, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 10, 8, 7, 9, 3, 2, 1, 4, 6], 'cur_cost': 11959.0, 'intermediate_solutions': [{'tour': [0, 1, 22, 17, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 6, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 10, 8, 7, 9, 5, 3, 2, 34, 4], 'cur_cost': 27211.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 22, 17, 19, 18, 20, 14, 11, 24, 28, 30, 32, 23, 40, 38, 35, 36, 43, 33, 39, 37, 44, 34, 41, 42, 16, 21, 12, 15, 13, 25, 27, 26, 31, 29, 10, 8, 7, 9, 5, 3, 2, 6, 4], 'cur_cost': 19361.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 22, 17, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 2, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 10, 8, 7, 9, 5, 3, 6, 4], 'cur_cost': 18837.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:59,501 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 11959.00)
2025-08-05 10:28:59,501 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:59,501 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:59,501 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:59,502 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 45
2025-08-05 10:28:59,502 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,502 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,503 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,503 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,503 - ExplorationExpert - INFO - 探索路径生成完成，成本: 18866.0, 路径长度: 45, 收集中间解: 3
2025-08-05 10:28:59,503 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 9, 22, 8, 7, 3, 5, 6, 4, 1, 10, 2, 17, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29], 'cur_cost': 18866.0, 'intermediate_solutions': [{'tour': [0, 20, 15, 14, 11, 13, 17, 19, 18, 21, 8, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 12, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 19615.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 15, 14, 11, 13, 17, 19, 18, 21, 12, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 27, 25, 24, 28, 30, 32, 23, 40, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 17256.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [20, 15, 14, 11, 13, 17, 19, 18, 21, 12, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 0, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 17475.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:59,503 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 18866.00)
2025-08-05 10:28:59,503 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:59,503 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:59,504 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:59,505 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 45
2025-08-05 10:28:59,505 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,505 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,505 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,506 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,506 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11535.0, 路径长度: 45, 收集中间解: 3
2025-08-05 10:28:59,506 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 5, 16, 20, 14, 11, 13, 17, 19, 18, 21, 12, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 3, 2, 1, 4, 6], 'cur_cost': 11535.0, 'intermediate_solutions': [{'tour': [0, 17, 1, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 22, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 42, 10, 8, 7, 9, 5, 3, 2, 6, 4], 'cur_cost': 21328.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 1, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 2, 3, 5, 9, 7, 8, 10, 22, 29, 31, 26, 27, 25, 24, 28, 30, 32, 23, 40, 38, 35, 36, 43, 33, 39, 6, 4], 'cur_cost': 21453.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 36, 17, 1, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 6, 4], 'cur_cost': 17833.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:59,506 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 11535.00)
2025-08-05 10:28:59,506 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:28:59,506 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:59,507 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:59,507 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 88460.0
2025-08-05 10:28:59,522 - ExploitationExpert - INFO - res_population_num: 9
2025-08-05 10:28:59,522 - ExploitationExpert - INFO - res_population_costs: [10973.0, 10973, 10973, 10973, 10973.0, 10973, 10979.0, 10979, 10973]
2025-08-05 10:28:59,522 - ExploitationExpert - INFO - res_populations: [array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  3,  2,  7,  9,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 39, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 25, 22, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 43, 33, 38, 39, 44, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 44, 39,
       38, 33, 43, 36, 35, 32, 30, 28, 24, 25, 22, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64), array([ 0,  1,  4,  6,  5,  3,  2,  7,  9,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 39, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64)]
2025-08-05 10:28:59,527 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:59,527 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 15, 18, 20, 14, 11, 13, 17, 19, 16, 21, 12, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 6, 4], 'cur_cost': 11364.0}, {'tour': [32, 11, 17, 18, 7, 34, 5, 3, 4, 8, 2, 25, 19, 0, 20, 14, 12, 13, 15, 41, 21, 16, 42, 33, 36, 30, 27, 1, 37, 26, 29, 40, 44, 24, 23, 10, 22, 28, 9, 38, 39, 31, 6, 35, 43], 'cur_cost': 71254.0}, {'tour': [32, 30, 26, 29, 21, 34, 35, 20, 11, 44, 17, 12, 16, 43, 23, 28, 22, 42, 40, 37, 36, 39, 13, 0, 10, 4, 6, 1, 18, 14, 33, 27, 15, 31, 41, 38, 19, 2, 8, 9, 7, 3, 5, 25, 24], 'cur_cost': 40611.0}, {'tour': array([15, 26,  8, 43,  3, 28,  7, 44, 33, 39, 23, 18, 29, 21,  1, 38, 34,
       36, 27, 25, 19, 17, 35, 30, 11, 24, 32, 41, 12,  2,  9, 14,  4, 31,
       10,  0,  6, 16, 42, 20, 22, 13, 40, 37,  5], dtype=int64), 'cur_cost': 85296.0}, {'tour': [30, 26, 29, 20, 14, 5, 13, 17, 8, 16, 25, 12, 43, 23, 28, 3, 40, 41, 21, 39, 42, 33, 10, 4, 27, 37, 18, 7, 31, 24, 22, 38, 19, 0, 1, 35, 32, 6, 44, 11, 34, 15, 36, 9, 2], 'cur_cost': 78524.0}, {'tour': array([16, 34,  5,  1,  0, 29,  7, 12, 17, 31, 40,  8, 42, 28,  4, 41,  3,
        2, 11, 25,  6, 22, 27, 24, 37, 33, 32, 23, 30, 38, 26, 43,  9, 20,
       19, 44, 36, 21, 18, 15, 13, 14, 10, 35, 39], dtype=int64), 'cur_cost': 79959.0}, {'tour': [0, 5, 11, 24, 28, 25, 27, 26, 31, 29, 23, 32, 30, 22, 15, 13, 17, 19, 18, 20, 14, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 10, 8, 7, 9, 3, 2, 1, 4, 6], 'cur_cost': 11959.0}, {'tour': [0, 9, 22, 8, 7, 3, 5, 6, 4, 1, 10, 2, 17, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29], 'cur_cost': 18866.0}, {'tour': [0, 5, 16, 20, 14, 11, 13, 17, 19, 18, 21, 12, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 3, 2, 1, 4, 6], 'cur_cost': 11535.0}, {'tour': array([43, 33, 37, 35,  1, 20, 14, 12, 13, 15, 21, 39, 40, 36, 44,  7, 29,
       18,  4, 30, 19, 26, 16, 17, 34,  0, 41,  6, 28, 42, 22,  3,  9, 27,
        2, 25, 10, 31, 23, 38, 32, 11,  8,  5, 24], dtype=int64), 'cur_cost': 88460.0}]
2025-08-05 10:28:59,528 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:59,528 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 257, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 257, 'cache_hits': 0, 'similarity_calculations': 1245, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:59,529 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([43, 33, 37, 35,  1, 20, 14, 12, 13, 15, 21, 39, 40, 36, 44,  7, 29,
       18,  4, 30, 19, 26, 16, 17, 34,  0, 41,  6, 28, 42, 22,  3,  9, 27,
        2, 25, 10, 31, 23, 38, 32, 11,  8,  5, 24], dtype=int64), 'cur_cost': 88460.0, 'intermediate_solutions': [{'tour': array([12, 25, 39, 38, 27,  5, 18, 21,  2, 23,  1, 20, 14, 26, 43, 10,  0,
       22, 41, 29,  4,  7,  6, 40,  9, 44, 24, 34, 13, 35, 37, 19, 16, 30,
       28, 17, 11, 15,  8, 31, 32, 42, 33,  3, 36]), 'cur_cost': 88070.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([38, 12, 25, 39, 27,  5, 18, 21,  2, 23,  1, 20, 14, 26, 43, 10,  0,
       22, 41, 29,  4,  7,  6, 40,  9, 44, 24, 34, 13, 35, 37, 19, 16, 30,
       28, 17, 11, 15,  8, 31, 32, 42, 33,  3, 36]), 'cur_cost': 88123.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([27, 38, 12, 25, 39,  5, 18, 21,  2, 23,  1, 20, 14, 26, 43, 10,  0,
       22, 41, 29,  4,  7,  6, 40,  9, 44, 24, 34, 13, 35, 37, 19, 16, 30,
       28, 17, 11, 15,  8, 31, 32, 42, 33,  3, 36]), 'cur_cost': 90328.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([39, 38, 12, 25, 27,  5, 18, 21,  2, 23,  1, 20, 14, 26, 43, 10,  0,
       22, 41, 29,  4,  7,  6, 40,  9, 44, 24, 34, 13, 35, 37, 19, 16, 30,
       28, 17, 11, 15,  8, 31, 32, 42, 33,  3, 36]), 'cur_cost': 82182.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([39, 27, 38, 12, 25,  5, 18, 21,  2, 23,  1, 20, 14, 26, 43, 10,  0,
       22, 41, 29,  4,  7,  6, 40,  9, 44, 24, 34, 13, 35, 37, 19, 16, 30,
       28, 17, 11, 15,  8, 31, 32, 42, 33,  3, 36]), 'cur_cost': 88136.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:59,529 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 88460.00)
2025-08-05 10:28:59,530 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:59,530 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:59,532 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 15, 18, 20, 14, 11, 13, 17, 19, 16, 21, 12, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 6, 4], 'cur_cost': 11364.0, 'intermediate_solutions': [{'tour': [12, 38, 13, 35, 6, 18, 19, 0, 43, 1, 37, 17, 7, 39, 11, 30, 20, 21, 15, 16, 36, 44, 42, 41, 31, 26, 29, 22, 24, 25, 27, 2, 4, 3, 28, 10, 5, 33, 34, 9, 14, 40, 32, 8, 23], 'cur_cost': 71898.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [12, 38, 13, 35, 6, 18, 19, 0, 43, 1, 37, 17, 7, 39, 11, 30, 20, 21, 33, 5, 10, 28, 3, 4, 2, 27, 25, 24, 22, 29, 26, 31, 41, 42, 44, 36, 16, 15, 34, 9, 14, 40, 23, 8, 32], 'cur_cost': 74612.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [12, 38, 13, 35, 6, 18, 19, 0, 43, 1, 37, 17, 7, 39, 11, 30, 20, 21, 15, 16, 36, 44, 42, 41, 31, 26, 29, 22, 24, 25, 27, 2, 4, 3, 28, 10, 5, 33, 34, 9, 14, 40, 32, 23, 8], 'cur_cost': 68209.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [32, 11, 17, 18, 7, 34, 5, 3, 4, 8, 2, 25, 19, 0, 20, 14, 12, 13, 15, 41, 21, 16, 42, 33, 36, 30, 27, 1, 37, 26, 29, 40, 44, 24, 23, 10, 22, 28, 9, 38, 39, 31, 6, 35, 43], 'cur_cost': 71254.0, 'intermediate_solutions': [{'tour': [32, 11, 8, 12, 7, 9, 5, 19, 4, 18, 15, 2, 42, 41, 23, 44, 14, 16, 1, 20, 13, 35, 28, 33, 34, 0, 17, 39, 24, 26, 36, 40, 38, 37, 3, 22, 30, 10, 27, 25, 29, 31, 21, 6, 43], 'cur_cost': 76472.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [32, 11, 42, 2, 15, 18, 4, 19, 5, 9, 7, 12, 8, 41, 23, 44, 14, 16, 1, 20, 13, 35, 28, 33, 34, 0, 37, 39, 24, 26, 36, 40, 38, 17, 3, 22, 30, 10, 27, 25, 29, 31, 21, 6, 43], 'cur_cost': 78774.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [32, 11, 8, 12, 7, 9, 5, 19, 4, 18, 15, 2, 42, 41, 0, 23, 44, 14, 16, 1, 20, 13, 35, 28, 33, 34, 37, 39, 24, 26, 36, 40, 38, 17, 3, 22, 30, 10, 27, 25, 29, 31, 21, 6, 43], 'cur_cost': 74220.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [32, 30, 26, 29, 21, 34, 35, 20, 11, 44, 17, 12, 16, 43, 23, 28, 22, 42, 40, 37, 36, 39, 13, 0, 10, 4, 6, 1, 18, 14, 33, 27, 15, 31, 41, 38, 19, 2, 8, 9, 7, 3, 5, 25, 24], 'cur_cost': 40611.0, 'intermediate_solutions': [{'tour': [0, 6, 17, 1, 4, 9, 7, 3, 5, 8, 2, 10, 41, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 19, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 20456.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 36, 43, 33, 39, 37, 44, 34, 41, 42, 16, 21, 12, 15, 13, 11, 14, 20, 18, 19, 10, 2, 8, 5, 3, 7, 9, 4, 1, 17, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 17833.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 17, 1, 4, 9, 7, 3, 5, 8, 2, 10, 19, 18, 20, 24, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 25, 27, 26, 31, 29, 22], 'cur_cost': 19711.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([15, 26,  8, 43,  3, 28,  7, 44, 33, 39, 23, 18, 29, 21,  1, 38, 34,
       36, 27, 25, 19, 17, 35, 30, 11, 24, 32, 41, 12,  2,  9, 14,  4, 31,
       10,  0,  6, 16, 42, 20, 22, 13, 40, 37,  5], dtype=int64), 'cur_cost': 85296.0, 'intermediate_solutions': [{'tour': array([33,  6,  4, 13,  9, 34, 43, 19, 22, 32, 28, 25, 10, 21, 26, 40, 12,
       11,  7, 41,  8, 42, 44, 39, 36, 30, 27,  1, 37,  2, 29, 23, 20, 24,
       18, 14, 15, 35, 17, 38,  0, 16,  3, 31,  5]), 'cur_cost': 83295.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([13, 33,  6,  4,  9, 34, 43, 19, 22, 32, 28, 25, 10, 21, 26, 40, 12,
       11,  7, 41,  8, 42, 44, 39, 36, 30, 27,  1, 37,  2, 29, 23, 20, 24,
       18, 14, 15, 35, 17, 38,  0, 16,  3, 31,  5]), 'cur_cost': 79630.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9, 13, 33,  6,  4, 34, 43, 19, 22, 32, 28, 25, 10, 21, 26, 40, 12,
       11,  7, 41,  8, 42, 44, 39, 36, 30, 27,  1, 37,  2, 29, 23, 20, 24,
       18, 14, 15, 35, 17, 38,  0, 16,  3, 31,  5]), 'cur_cost': 79596.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4, 13, 33,  6,  9, 34, 43, 19, 22, 32, 28, 25, 10, 21, 26, 40, 12,
       11,  7, 41,  8, 42, 44, 39, 36, 30, 27,  1, 37,  2, 29, 23, 20, 24,
       18, 14, 15, 35, 17, 38,  0, 16,  3, 31,  5]), 'cur_cost': 79609.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4,  9, 13, 33,  6, 34, 43, 19, 22, 32, 28, 25, 10, 21, 26, 40, 12,
       11,  7, 41,  8, 42, 44, 39, 36, 30, 27,  1, 37,  2, 29, 23, 20, 24,
       18, 14, 15, 35, 17, 38,  0, 16,  3, 31,  5]), 'cur_cost': 79650.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [30, 26, 29, 20, 14, 5, 13, 17, 8, 16, 25, 12, 43, 23, 28, 3, 40, 41, 21, 39, 42, 33, 10, 4, 27, 37, 18, 7, 31, 24, 22, 38, 19, 0, 1, 35, 32, 6, 44, 11, 34, 15, 36, 9, 2], 'cur_cost': 78524.0, 'intermediate_solutions': [{'tour': [0, 4, 21, 17, 19, 18, 20, 14, 11, 13, 15, 12, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 24, 23, 32, 30, 28, 40, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 1, 6], 'cur_cost': 17512.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 34, 41, 42, 16, 12, 15, 13, 11, 14, 20, 18, 19, 17, 21, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 1, 6], 'cur_cost': 13744.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 21, 17, 19, 18, 20, 14, 11, 13, 15, 12, 16, 42, 41, 34, 44, 37, 39, 33, 36, 43, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 1, 6], 'cur_cost': 11471.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([16, 34,  5,  1,  0, 29,  7, 12, 17, 31, 40,  8, 42, 28,  4, 41,  3,
        2, 11, 25,  6, 22, 27, 24, 37, 33, 32, 23, 30, 38, 26, 43,  9, 20,
       19, 44, 36, 21, 18, 15, 13, 14, 10, 35, 39], dtype=int64), 'cur_cost': 79959.0, 'intermediate_solutions': [{'tour': array([22, 29, 10, 27, 15, 40, 43, 35, 16, 23, 34, 19,  2,  0, 30,  6,  3,
       12, 28,  5, 20, 41,  9, 31,  4, 18, 11, 37, 42, 32, 17,  7, 44,  1,
       14, 25, 24,  8, 13, 36, 39, 33, 38, 21, 26]), 'cur_cost': 82993.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([27, 22, 29, 10, 15, 40, 43, 35, 16, 23, 34, 19,  2,  0, 30,  6,  3,
       12, 28,  5, 20, 41,  9, 31,  4, 18, 11, 37, 42, 32, 17,  7, 44,  1,
       14, 25, 24,  8, 13, 36, 39, 33, 38, 21, 26]), 'cur_cost': 79248.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([15, 27, 22, 29, 10, 40, 43, 35, 16, 23, 34, 19,  2,  0, 30,  6,  3,
       12, 28,  5, 20, 41,  9, 31,  4, 18, 11, 37, 42, 32, 17,  7, 44,  1,
       14, 25, 24,  8, 13, 36, 39, 33, 38, 21, 26]), 'cur_cost': 82867.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([10, 27, 22, 29, 15, 40, 43, 35, 16, 23, 34, 19,  2,  0, 30,  6,  3,
       12, 28,  5, 20, 41,  9, 31,  4, 18, 11, 37, 42, 32, 17,  7, 44,  1,
       14, 25, 24,  8, 13, 36, 39, 33, 38, 21, 26]), 'cur_cost': 82954.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([10, 15, 27, 22, 29, 40, 43, 35, 16, 23, 34, 19,  2,  0, 30,  6,  3,
       12, 28,  5, 20, 41,  9, 31,  4, 18, 11, 37, 42, 32, 17,  7, 44,  1,
       14, 25, 24,  8, 13, 36, 39, 33, 38, 21, 26]), 'cur_cost': 82830.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 11, 24, 28, 25, 27, 26, 31, 29, 23, 32, 30, 22, 15, 13, 17, 19, 18, 20, 14, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 10, 8, 7, 9, 3, 2, 1, 4, 6], 'cur_cost': 11959.0, 'intermediate_solutions': [{'tour': [0, 1, 22, 17, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 6, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 10, 8, 7, 9, 5, 3, 2, 34, 4], 'cur_cost': 27211.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 22, 17, 19, 18, 20, 14, 11, 24, 28, 30, 32, 23, 40, 38, 35, 36, 43, 33, 39, 37, 44, 34, 41, 42, 16, 21, 12, 15, 13, 25, 27, 26, 31, 29, 10, 8, 7, 9, 5, 3, 2, 6, 4], 'cur_cost': 19361.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 22, 17, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 2, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 10, 8, 7, 9, 5, 3, 6, 4], 'cur_cost': 18837.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 22, 8, 7, 3, 5, 6, 4, 1, 10, 2, 17, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29], 'cur_cost': 18866.0, 'intermediate_solutions': [{'tour': [0, 20, 15, 14, 11, 13, 17, 19, 18, 21, 8, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 12, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 19615.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 15, 14, 11, 13, 17, 19, 18, 21, 12, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 27, 25, 24, 28, 30, 32, 23, 40, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 17256.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [20, 15, 14, 11, 13, 17, 19, 18, 21, 12, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 0, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 17475.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 16, 20, 14, 11, 13, 17, 19, 18, 21, 12, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 3, 2, 1, 4, 6], 'cur_cost': 11535.0, 'intermediate_solutions': [{'tour': [0, 17, 1, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 22, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 42, 10, 8, 7, 9, 5, 3, 2, 6, 4], 'cur_cost': 21328.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 1, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 2, 3, 5, 9, 7, 8, 10, 22, 29, 31, 26, 27, 25, 24, 28, 30, 32, 23, 40, 38, 35, 36, 43, 33, 39, 6, 4], 'cur_cost': 21453.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 36, 17, 1, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 6, 4], 'cur_cost': 17833.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([43, 33, 37, 35,  1, 20, 14, 12, 13, 15, 21, 39, 40, 36, 44,  7, 29,
       18,  4, 30, 19, 26, 16, 17, 34,  0, 41,  6, 28, 42, 22,  3,  9, 27,
        2, 25, 10, 31, 23, 38, 32, 11,  8,  5, 24], dtype=int64), 'cur_cost': 88460.0, 'intermediate_solutions': [{'tour': array([12, 25, 39, 38, 27,  5, 18, 21,  2, 23,  1, 20, 14, 26, 43, 10,  0,
       22, 41, 29,  4,  7,  6, 40,  9, 44, 24, 34, 13, 35, 37, 19, 16, 30,
       28, 17, 11, 15,  8, 31, 32, 42, 33,  3, 36]), 'cur_cost': 88070.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([38, 12, 25, 39, 27,  5, 18, 21,  2, 23,  1, 20, 14, 26, 43, 10,  0,
       22, 41, 29,  4,  7,  6, 40,  9, 44, 24, 34, 13, 35, 37, 19, 16, 30,
       28, 17, 11, 15,  8, 31, 32, 42, 33,  3, 36]), 'cur_cost': 88123.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([27, 38, 12, 25, 39,  5, 18, 21,  2, 23,  1, 20, 14, 26, 43, 10,  0,
       22, 41, 29,  4,  7,  6, 40,  9, 44, 24, 34, 13, 35, 37, 19, 16, 30,
       28, 17, 11, 15,  8, 31, 32, 42, 33,  3, 36]), 'cur_cost': 90328.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([39, 38, 12, 25, 27,  5, 18, 21,  2, 23,  1, 20, 14, 26, 43, 10,  0,
       22, 41, 29,  4,  7,  6, 40,  9, 44, 24, 34, 13, 35, 37, 19, 16, 30,
       28, 17, 11, 15,  8, 31, 32, 42, 33,  3, 36]), 'cur_cost': 82182.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([39, 27, 38, 12, 25,  5, 18, 21,  2, 23,  1, 20, 14, 26, 43, 10,  0,
       22, 41, 29,  4,  7,  6, 40,  9, 44, 24, 34, 13, 35, 37, 19, 16, 30,
       28, 17, 11, 15,  8, 31, 32, 42, 33,  3, 36]), 'cur_cost': 88136.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:28:59,532 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:59,532 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:59,535 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=11364.000, 多样性=0.949
2025-08-05 10:28:59,535 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 10:28:59,535 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 10:28:59,535 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:59,536 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.02278568513637215, 'best_improvement': -0.00035211267605633805}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.005232862375719448}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.007323826216935965, 'recent_improvements': [-0.022183804404113733, 0.12435939368335559, -0.036831456837985665], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 9, 'new_count': 9, 'count_change': 0, 'old_best_cost': 10973.0, 'new_best_cost': 10973.0, 'quality_improvement': 0.0, 'old_diversity': 0.7617283950617284, 'new_diversity': 0.7617283950617284, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:59,537 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 10:28:59,537 - __main__ - INFO - composite8_45 开始进化第 5 代
2025-08-05 10:28:59,537 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 10:28:59,538 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:59,538 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=11364.000, 多样性=0.949
2025-08-05 10:28:59,539 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:59,540 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.949
2025-08-05 10:28:59,541 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:59,544 - EliteExpert - INFO - 精英解分析完成: 精英解数量=9, 多样性=0.762
2025-08-05 10:28:59,545 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 10:28:59,545 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:59,545 - LandscapeExpert - INFO - 添加精英解数据: 9个精英解
2025-08-05 10:28:59,545 - LandscapeExpert - INFO - 数据提取成功: 19个路径, 19个适应度值
2025-08-05 10:28:59,583 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.368, 适应度梯度: -7248.842, 聚类评分: 0.000, 覆盖率: 0.118, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:59,583 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 10:28:59,583 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:59,583 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite8_45
2025-08-05 10:28:59,589 - visualization.landscape_visualizer - INFO - 插值约束: 111 个点被约束到最小值 10973.00
2025-08-05 10:28:59,591 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=5.4%, 梯度: 2675.00 → 2531.59
2025-08-05 10:28:59,730 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite8_45\landscape_composite8_45_iter_100_20250805_102859.html
2025-08-05 10:28:59,798 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite8_45\dashboard_composite8_45_iter_100_20250805_102859.html
2025-08-05 10:28:59,798 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 100
2025-08-05 10:28:59,798 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 10:28:59,798 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2529秒
2025-08-05 10:28:59,799 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3684210526315789, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -7248.842105263159, 'local_optima_density': 0.3684210526315789, 'gradient_variance': 872872055.7119113, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1179, 'fitness_entropy': 0.685972951096462, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -7248.842)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.118)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360939.5833042, 'performance_metrics': {}}}
2025-08-05 10:28:59,799 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:59,799 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:59,799 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:59,799 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:59,799 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:59,799 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 10:28:59,800 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:59,800 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:59,800 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:59,800 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:59,800 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:59,801 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:59,801 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 8} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:59,801 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:59,801 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:59,801 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:59,803 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 45
2025-08-05 10:28:59,803 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,803 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,803 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,803 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,804 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19215.0, 路径长度: 45, 收集中间解: 3
2025-08-05 10:28:59,804 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 22, 20, 3, 7, 9, 5, 6, 4, 1, 8, 2, 10, 17, 19, 18, 21, 12, 14, 11, 13, 15, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29], 'cur_cost': 19215.0, 'intermediate_solutions': [{'tour': [0, 1, 15, 18, 20, 14, 11, 13, 17, 19, 16, 21, 12, 41, 42, 34, 44, 37, 39, 33, 10, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 43, 8, 7, 9, 5, 3, 2, 6, 4], 'cur_cost': 19694.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 15, 18, 20, 14, 11, 13, 17, 19, 16, 21, 12, 41, 42, 27, 25, 24, 28, 30, 32, 23, 40, 38, 35, 36, 43, 33, 39, 37, 44, 34, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 6, 4], 'cur_cost': 17255.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [31, 0, 1, 15, 18, 20, 14, 11, 13, 17, 19, 16, 21, 12, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 29, 22, 10, 8, 7, 9, 5, 3, 2, 6, 4], 'cur_cost': 18738.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:59,804 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 19215.00)
2025-08-05 10:28:59,804 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:59,804 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:59,805 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:59,806 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 45
2025-08-05 10:28:59,806 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,806 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,807 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,807 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,807 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19279.0, 路径长度: 45, 收集中间解: 3
2025-08-05 10:28:59,807 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 14, 25, 8, 7, 9, 5, 3, 2, 1, 4, 6, 10, 17, 19, 18, 20, 21, 12, 16, 11, 13, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 22, 27, 26, 31, 29], 'cur_cost': 19279.0, 'intermediate_solutions': [{'tour': [32, 11, 17, 18, 7, 34, 5, 3, 4, 8, 2, 25, 9, 0, 20, 14, 12, 13, 15, 41, 21, 16, 42, 33, 36, 30, 27, 1, 37, 26, 29, 40, 44, 24, 23, 10, 22, 28, 19, 38, 39, 31, 6, 35, 43], 'cur_cost': 67584.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [32, 11, 17, 18, 7, 13, 12, 14, 20, 0, 19, 25, 2, 8, 4, 3, 5, 34, 15, 41, 21, 16, 42, 33, 36, 30, 27, 1, 37, 26, 29, 40, 44, 24, 23, 10, 22, 28, 9, 38, 39, 31, 6, 35, 43], 'cur_cost': 71689.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [32, 11, 17, 18, 7, 34, 5, 3, 4, 8, 2, 25, 19, 0, 20, 14, 12, 13, 15, 41, 21, 16, 42, 36, 30, 27, 1, 37, 26, 29, 40, 44, 24, 23, 33, 10, 22, 28, 9, 38, 39, 31, 6, 35, 43], 'cur_cost': 73520.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:59,807 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 19279.00)
2025-08-05 10:28:59,808 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:59,808 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:59,808 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:59,810 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 45
2025-08-05 10:28:59,810 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,811 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,811 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,811 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,811 - ExplorationExpert - INFO - 探索路径生成完成，成本: 40440.0, 路径长度: 45, 收集中间解: 3
2025-08-05 10:28:59,811 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [14, 33, 42, 38, 36, 15, 18, 44, 34, 19, 17, 16, 13, 35, 37, 41, 12, 20, 31, 25, 28, 30, 43, 29, 32, 21, 23, 40, 2, 8, 4, 5, 1, 6, 3, 7, 10, 0, 39, 27, 22, 26, 24, 11, 9], 'cur_cost': 40440.0, 'intermediate_solutions': [{'tour': [32, 30, 26, 29, 21, 34, 35, 39, 11, 44, 17, 12, 16, 43, 23, 28, 22, 42, 40, 37, 36, 20, 13, 0, 10, 4, 6, 1, 18, 14, 33, 27, 15, 31, 41, 38, 19, 2, 8, 9, 7, 3, 5, 25, 24], 'cur_cost': 40661.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [32, 30, 21, 29, 26, 34, 35, 20, 11, 44, 17, 12, 16, 43, 23, 28, 22, 42, 40, 37, 36, 39, 13, 0, 10, 4, 6, 1, 18, 14, 33, 27, 15, 31, 41, 38, 19, 2, 8, 9, 7, 3, 5, 25, 24], 'cur_cost': 44298.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [32, 30, 26, 29, 21, 34, 35, 20, 11, 44, 12, 16, 43, 23, 28, 22, 42, 40, 37, 36, 17, 39, 13, 0, 10, 4, 6, 1, 18, 14, 33, 27, 15, 31, 41, 38, 19, 2, 8, 9, 7, 3, 5, 25, 24], 'cur_cost': 43334.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:59,812 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 40440.00)
2025-08-05 10:28:59,812 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:28:59,812 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:59,812 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:59,812 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 85474.0
2025-08-05 10:28:59,824 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 10:28:59,824 - ExploitationExpert - INFO - res_population_costs: [10973.0, 10973, 10973, 10973, 10973.0, 10973, 10973, 10979.0, 10979, 10973]
2025-08-05 10:28:59,824 - ExploitationExpert - INFO - res_populations: [array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  3,  2,  7,  9,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 39, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 25, 22, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  3,  2,  7,  9,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 39, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 43, 33, 38, 39, 44, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 44, 39,
       38, 33, 43, 36, 35, 32, 30, 28, 24, 25, 22, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64)]
2025-08-05 10:28:59,828 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:59,828 - ExploitationExpert - INFO - populations: [{'tour': [0, 22, 20, 3, 7, 9, 5, 6, 4, 1, 8, 2, 10, 17, 19, 18, 21, 12, 14, 11, 13, 15, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29], 'cur_cost': 19215.0}, {'tour': [0, 14, 25, 8, 7, 9, 5, 3, 2, 1, 4, 6, 10, 17, 19, 18, 20, 21, 12, 16, 11, 13, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 22, 27, 26, 31, 29], 'cur_cost': 19279.0}, {'tour': [14, 33, 42, 38, 36, 15, 18, 44, 34, 19, 17, 16, 13, 35, 37, 41, 12, 20, 31, 25, 28, 30, 43, 29, 32, 21, 23, 40, 2, 8, 4, 5, 1, 6, 3, 7, 10, 0, 39, 27, 22, 26, 24, 11, 9], 'cur_cost': 40440.0}, {'tour': array([12, 31,  4, 40, 19, 14, 43,  3, 30, 29, 23, 36, 22, 11, 44, 13, 17,
       26, 21, 38, 24, 33,  7, 10,  8, 20,  6,  2, 25,  0, 35, 34, 39, 27,
       18, 28,  9,  1, 15, 16,  5, 42, 32, 37, 41], dtype=int64), 'cur_cost': 85474.0}, {'tour': [30, 26, 29, 20, 14, 5, 13, 17, 8, 16, 25, 12, 43, 23, 28, 3, 40, 41, 21, 39, 42, 33, 10, 4, 27, 37, 18, 7, 31, 24, 22, 38, 19, 0, 1, 35, 32, 6, 44, 11, 34, 15, 36, 9, 2], 'cur_cost': 78524.0}, {'tour': [16, 34, 5, 1, 0, 29, 7, 12, 17, 31, 40, 8, 42, 28, 4, 41, 3, 2, 11, 25, 6, 22, 27, 24, 37, 33, 32, 23, 30, 38, 26, 43, 9, 20, 19, 44, 36, 21, 18, 15, 13, 14, 10, 35, 39], 'cur_cost': 79959.0}, {'tour': [0, 5, 11, 24, 28, 25, 27, 26, 31, 29, 23, 32, 30, 22, 15, 13, 17, 19, 18, 20, 14, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 10, 8, 7, 9, 3, 2, 1, 4, 6], 'cur_cost': 11959.0}, {'tour': [0, 9, 22, 8, 7, 3, 5, 6, 4, 1, 10, 2, 17, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29], 'cur_cost': 18866.0}, {'tour': [0, 5, 16, 20, 14, 11, 13, 17, 19, 18, 21, 12, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 3, 2, 1, 4, 6], 'cur_cost': 11535.0}, {'tour': [43, 33, 37, 35, 1, 20, 14, 12, 13, 15, 21, 39, 40, 36, 44, 7, 29, 18, 4, 30, 19, 26, 16, 17, 34, 0, 41, 6, 28, 42, 22, 3, 9, 27, 2, 25, 10, 31, 23, 38, 32, 11, 8, 5, 24], 'cur_cost': 88460.0}]
2025-08-05 10:28:59,828 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:59,829 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 258, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 258, 'cache_hits': 0, 'similarity_calculations': 1255, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:59,829 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([12, 31,  4, 40, 19, 14, 43,  3, 30, 29, 23, 36, 22, 11, 44, 13, 17,
       26, 21, 38, 24, 33,  7, 10,  8, 20,  6,  2, 25,  0, 35, 34, 39, 27,
       18, 28,  9,  1, 15, 16,  5, 42, 32, 37, 41], dtype=int64), 'cur_cost': 85474.0, 'intermediate_solutions': [{'tour': array([ 8, 26, 15, 43,  3, 28,  7, 44, 33, 39, 23, 18, 29, 21,  1, 38, 34,
       36, 27, 25, 19, 17, 35, 30, 11, 24, 32, 41, 12,  2,  9, 14,  4, 31,
       10,  0,  6, 16, 42, 20, 22, 13, 40, 37,  5]), 'cur_cost': 81596.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([43,  8, 26, 15,  3, 28,  7, 44, 33, 39, 23, 18, 29, 21,  1, 38, 34,
       36, 27, 25, 19, 17, 35, 30, 11, 24, 32, 41, 12,  2,  9, 14,  4, 31,
       10,  0,  6, 16, 42, 20, 22, 13, 40, 37,  5]), 'cur_cost': 85305.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3, 43,  8, 26, 15, 28,  7, 44, 33, 39, 23, 18, 29, 21,  1, 38, 34,
       36, 27, 25, 19, 17, 35, 30, 11, 24, 32, 41, 12,  2,  9, 14,  4, 31,
       10,  0,  6, 16, 42, 20, 22, 13, 40, 37,  5]), 'cur_cost': 81434.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([15, 43,  8, 26,  3, 28,  7, 44, 33, 39, 23, 18, 29, 21,  1, 38, 34,
       36, 27, 25, 19, 17, 35, 30, 11, 24, 32, 41, 12,  2,  9, 14,  4, 31,
       10,  0,  6, 16, 42, 20, 22, 13, 40, 37,  5]), 'cur_cost': 85382.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([15,  3, 43,  8, 26, 28,  7, 44, 33, 39, 23, 18, 29, 21,  1, 38, 34,
       36, 27, 25, 19, 17, 35, 30, 11, 24, 32, 41, 12,  2,  9, 14,  4, 31,
       10,  0,  6, 16, 42, 20, 22, 13, 40, 37,  5]), 'cur_cost': 81578.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:59,830 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 85474.00)
2025-08-05 10:28:59,830 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:59,830 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:59,830 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:59,832 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 45
2025-08-05 10:28:59,832 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,832 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,832 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,833 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,833 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11389.0, 路径长度: 45, 收集中间解: 3
2025-08-05 10:28:59,833 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 16, 21, 17, 19, 18, 20, 14, 11, 13, 15, 12, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 11389.0, 'intermediate_solutions': [{'tour': [30, 23, 29, 20, 14, 5, 13, 17, 8, 16, 25, 12, 43, 26, 28, 3, 40, 41, 21, 39, 42, 33, 10, 4, 27, 37, 18, 7, 31, 24, 22, 38, 19, 0, 1, 35, 32, 6, 44, 11, 34, 15, 36, 9, 2], 'cur_cost': 78560.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [30, 26, 29, 20, 14, 5, 13, 17, 8, 16, 25, 12, 43, 23, 28, 3, 40, 41, 21, 39, 42, 33, 10, 4, 27, 37, 18, 7, 31, 24, 22, 38, 19, 0, 1, 35, 32, 6, 44, 11, 34, 15, 36, 2, 9], 'cur_cost': 78492.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [30, 26, 29, 20, 14, 5, 13, 17, 8, 16, 25, 12, 43, 23, 28, 3, 40, 41, 39, 42, 33, 10, 4, 27, 37, 18, 7, 31, 24, 22, 38, 21, 19, 0, 1, 35, 32, 6, 44, 11, 34, 15, 36, 9, 2], 'cur_cost': 75858.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:59,833 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 11389.00)
2025-08-05 10:28:59,833 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:28:59,833 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:59,834 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:59,834 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 82722.0
2025-08-05 10:28:59,849 - ExploitationExpert - INFO - res_population_num: 11
2025-08-05 10:28:59,850 - ExploitationExpert - INFO - res_population_costs: [10973.0, 10973, 10973, 10973, 10973.0, 10973, 10973, 10979.0, 10979, 10973, 10973]
2025-08-05 10:28:59,850 - ExploitationExpert - INFO - res_populations: [array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  3,  2,  7,  9,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 39, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 25, 22, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  3,  2,  7,  9,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 39, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 43, 33, 38, 39, 44, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 44, 39,
       38, 33, 43, 36, 35, 32, 30, 28, 24, 25, 22, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 39, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64)]
2025-08-05 10:28:59,856 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:59,857 - ExploitationExpert - INFO - populations: [{'tour': [0, 22, 20, 3, 7, 9, 5, 6, 4, 1, 8, 2, 10, 17, 19, 18, 21, 12, 14, 11, 13, 15, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29], 'cur_cost': 19215.0}, {'tour': [0, 14, 25, 8, 7, 9, 5, 3, 2, 1, 4, 6, 10, 17, 19, 18, 20, 21, 12, 16, 11, 13, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 22, 27, 26, 31, 29], 'cur_cost': 19279.0}, {'tour': [14, 33, 42, 38, 36, 15, 18, 44, 34, 19, 17, 16, 13, 35, 37, 41, 12, 20, 31, 25, 28, 30, 43, 29, 32, 21, 23, 40, 2, 8, 4, 5, 1, 6, 3, 7, 10, 0, 39, 27, 22, 26, 24, 11, 9], 'cur_cost': 40440.0}, {'tour': array([12, 31,  4, 40, 19, 14, 43,  3, 30, 29, 23, 36, 22, 11, 44, 13, 17,
       26, 21, 38, 24, 33,  7, 10,  8, 20,  6,  2, 25,  0, 35, 34, 39, 27,
       18, 28,  9,  1, 15, 16,  5, 42, 32, 37, 41], dtype=int64), 'cur_cost': 85474.0}, {'tour': [0, 16, 21, 17, 19, 18, 20, 14, 11, 13, 15, 12, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 11389.0}, {'tour': array([15,  6, 39, 37, 42, 17,  1, 13,  0, 34, 16, 35, 33,  5, 38,  7,  9,
       11,  8, 14, 44, 31, 36,  2, 10, 41, 12, 23,  4, 18, 43, 26, 28, 20,
       40, 22, 32, 19, 29,  3, 21, 30, 27, 25, 24], dtype=int64), 'cur_cost': 82722.0}, {'tour': [0, 5, 11, 24, 28, 25, 27, 26, 31, 29, 23, 32, 30, 22, 15, 13, 17, 19, 18, 20, 14, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 10, 8, 7, 9, 3, 2, 1, 4, 6], 'cur_cost': 11959.0}, {'tour': [0, 9, 22, 8, 7, 3, 5, 6, 4, 1, 10, 2, 17, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29], 'cur_cost': 18866.0}, {'tour': [0, 5, 16, 20, 14, 11, 13, 17, 19, 18, 21, 12, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 3, 2, 1, 4, 6], 'cur_cost': 11535.0}, {'tour': [43, 33, 37, 35, 1, 20, 14, 12, 13, 15, 21, 39, 40, 36, 44, 7, 29, 18, 4, 30, 19, 26, 16, 17, 34, 0, 41, 6, 28, 42, 22, 3, 9, 27, 2, 25, 10, 31, 23, 38, 32, 11, 8, 5, 24], 'cur_cost': 88460.0}]
2025-08-05 10:28:59,858 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:59,858 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 259, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 259, 'cache_hits': 0, 'similarity_calculations': 1266, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:59,859 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([15,  6, 39, 37, 42, 17,  1, 13,  0, 34, 16, 35, 33,  5, 38,  7,  9,
       11,  8, 14, 44, 31, 36,  2, 10, 41, 12, 23,  4, 18, 43, 26, 28, 20,
       40, 22, 32, 19, 29,  3, 21, 30, 27, 25, 24], dtype=int64), 'cur_cost': 82722.0, 'intermediate_solutions': [{'tour': array([ 5, 34, 16,  1,  0, 29,  7, 12, 17, 31, 40,  8, 42, 28,  4, 41,  3,
        2, 11, 25,  6, 22, 27, 24, 37, 33, 32, 23, 30, 38, 26, 43,  9, 20,
       19, 44, 36, 21, 18, 15, 13, 14, 10, 35, 39]), 'cur_cost': 83719.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1,  5, 34, 16,  0, 29,  7, 12, 17, 31, 40,  8, 42, 28,  4, 41,  3,
        2, 11, 25,  6, 22, 27, 24, 37, 33, 32, 23, 30, 38, 26, 43,  9, 20,
       19, 44, 36, 21, 18, 15, 13, 14, 10, 35, 39]), 'cur_cost': 83693.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 0,  1,  5, 34, 16, 29,  7, 12, 17, 31, 40,  8, 42, 28,  4, 41,  3,
        2, 11, 25,  6, 22, 27, 24, 37, 33, 32, 23, 30, 38, 26, 43,  9, 20,
       19, 44, 36, 21, 18, 15, 13, 14, 10, 35, 39]), 'cur_cost': 80070.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([16,  1,  5, 34,  0, 29,  7, 12, 17, 31, 40,  8, 42, 28,  4, 41,  3,
        2, 11, 25,  6, 22, 27, 24, 37, 33, 32, 23, 30, 38, 26, 43,  9, 20,
       19, 44, 36, 21, 18, 15, 13, 14, 10, 35, 39]), 'cur_cost': 83706.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([16,  0,  1,  5, 34, 29,  7, 12, 17, 31, 40,  8, 42, 28,  4, 41,  3,
        2, 11, 25,  6, 22, 27, 24, 37, 33, 32, 23, 30, 38, 26, 43,  9, 20,
       19, 44, 36, 21, 18, 15, 13, 14, 10, 35, 39]), 'cur_cost': 79916.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:59,860 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 82722.00)
2025-08-05 10:28:59,860 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:59,860 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:59,860 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:59,862 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 45
2025-08-05 10:28:59,862 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,863 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,863 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,863 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,864 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15612.0, 路径长度: 45, 收集中间解: 3
2025-08-05 10:28:59,864 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 14, 4, 18, 20, 21, 12, 16, 19, 17, 13, 11, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 1, 6], 'cur_cost': 15612.0, 'intermediate_solutions': [{'tour': [0, 5, 11, 24, 28, 25, 27, 26, 31, 2, 23, 32, 30, 22, 15, 13, 17, 19, 18, 20, 14, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 10, 8, 7, 9, 3, 29, 1, 4, 6], 'cur_cost': 26865.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 11, 24, 28, 25, 27, 26, 31, 29, 23, 32, 30, 22, 15, 13, 17, 19, 18, 20, 14, 12, 21, 16, 42, 41, 34, 10, 40, 38, 35, 36, 43, 33, 39, 37, 44, 8, 7, 9, 3, 2, 1, 4, 6], 'cur_cost': 17929.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 11, 24, 28, 25, 27, 31, 29, 23, 32, 30, 22, 15, 13, 17, 19, 18, 20, 14, 12, 21, 16, 42, 41, 26, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 10, 8, 7, 9, 3, 2, 1, 4, 6], 'cur_cost': 17858.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:59,864 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 15612.00)
2025-08-05 10:28:59,864 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:59,865 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:59,865 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:59,870 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 45
2025-08-05 10:28:59,870 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,870 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,870 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,870 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,871 - ExplorationExpert - INFO - 探索路径生成完成，成本: 48019.0, 路径长度: 45, 收集中间解: 3
2025-08-05 10:28:59,871 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [3, 9, 19, 40, 13, 38, 16, 34, 39, 17, 10, 20, 21, 12, 11, 15, 31, 30, 18, 23, 36, 41, 37, 43, 42, 14, 26, 24, 32, 22, 29, 25, 35, 28, 0, 7, 8, 5, 1, 4, 6, 44, 33, 27, 2], 'cur_cost': 48019.0, 'intermediate_solutions': [{'tour': [0, 9, 22, 8, 7, 3, 5, 6, 4, 1, 10, 2, 17, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 40, 35, 38, 36, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29], 'cur_cost': 18861.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 22, 8, 7, 3, 5, 6, 4, 1, 10, 2, 17, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 31, 26, 27, 25, 24, 29], 'cur_cost': 18960.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 22, 8, 7, 3, 5, 6, 4, 1, 10, 2, 17, 19, 18, 20, 14, 11, 13, 15, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 12, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29], 'cur_cost': 21598.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:59,871 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 48019.00)
2025-08-05 10:28:59,871 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:59,871 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:59,871 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:59,873 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 45
2025-08-05 10:28:59,873 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,873 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,873 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,874 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:59,874 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15477.0, 路径长度: 45, 收集中间解: 3
2025-08-05 10:28:59,874 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 18, 13, 1, 4, 6, 5, 9, 7, 3, 2, 8, 10, 17, 19, 11, 14, 20, 21, 12, 16, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15477.0, 'intermediate_solutions': [{'tour': [0, 5, 16, 20, 14, 11, 13, 17, 15, 18, 21, 12, 19, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 3, 2, 1, 4, 6], 'cur_cost': 11564.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 16, 20, 14, 11, 13, 17, 19, 18, 21, 12, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 3, 1, 2, 4, 6], 'cur_cost': 11615.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 16, 20, 14, 11, 13, 17, 19, 21, 12, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 3, 2, 1, 4, 6, 18], 'cur_cost': 15677.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:59,874 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 15477.00)
2025-08-05 10:28:59,874 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:28:59,875 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:59,875 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:59,875 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 90080.0
2025-08-05 10:28:59,891 - ExploitationExpert - INFO - res_population_num: 11
2025-08-05 10:28:59,891 - ExploitationExpert - INFO - res_population_costs: [10973.0, 10973, 10973, 10973, 10973.0, 10973, 10973, 10979.0, 10979, 10973, 10973]
2025-08-05 10:28:59,891 - ExploitationExpert - INFO - res_populations: [array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  3,  2,  7,  9,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 39, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 25, 22, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  3,  2,  7,  9,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 39, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 43, 33, 38, 39, 44, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 44, 39,
       38, 33, 43, 36, 35, 32, 30, 28, 24, 25, 22, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 39, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64)]
2025-08-05 10:28:59,894 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:59,895 - ExploitationExpert - INFO - populations: [{'tour': [0, 22, 20, 3, 7, 9, 5, 6, 4, 1, 8, 2, 10, 17, 19, 18, 21, 12, 14, 11, 13, 15, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29], 'cur_cost': 19215.0}, {'tour': [0, 14, 25, 8, 7, 9, 5, 3, 2, 1, 4, 6, 10, 17, 19, 18, 20, 21, 12, 16, 11, 13, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 22, 27, 26, 31, 29], 'cur_cost': 19279.0}, {'tour': [14, 33, 42, 38, 36, 15, 18, 44, 34, 19, 17, 16, 13, 35, 37, 41, 12, 20, 31, 25, 28, 30, 43, 29, 32, 21, 23, 40, 2, 8, 4, 5, 1, 6, 3, 7, 10, 0, 39, 27, 22, 26, 24, 11, 9], 'cur_cost': 40440.0}, {'tour': array([12, 31,  4, 40, 19, 14, 43,  3, 30, 29, 23, 36, 22, 11, 44, 13, 17,
       26, 21, 38, 24, 33,  7, 10,  8, 20,  6,  2, 25,  0, 35, 34, 39, 27,
       18, 28,  9,  1, 15, 16,  5, 42, 32, 37, 41], dtype=int64), 'cur_cost': 85474.0}, {'tour': [0, 16, 21, 17, 19, 18, 20, 14, 11, 13, 15, 12, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 11389.0}, {'tour': array([15,  6, 39, 37, 42, 17,  1, 13,  0, 34, 16, 35, 33,  5, 38,  7,  9,
       11,  8, 14, 44, 31, 36,  2, 10, 41, 12, 23,  4, 18, 43, 26, 28, 20,
       40, 22, 32, 19, 29,  3, 21, 30, 27, 25, 24], dtype=int64), 'cur_cost': 82722.0}, {'tour': [0, 14, 4, 18, 20, 21, 12, 16, 19, 17, 13, 11, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 1, 6], 'cur_cost': 15612.0}, {'tour': [3, 9, 19, 40, 13, 38, 16, 34, 39, 17, 10, 20, 21, 12, 11, 15, 31, 30, 18, 23, 36, 41, 37, 43, 42, 14, 26, 24, 32, 22, 29, 25, 35, 28, 0, 7, 8, 5, 1, 4, 6, 44, 33, 27, 2], 'cur_cost': 48019.0}, {'tour': [0, 18, 13, 1, 4, 6, 5, 9, 7, 3, 2, 8, 10, 17, 19, 11, 14, 20, 21, 12, 16, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15477.0}, {'tour': array([36, 42,  2, 37, 30, 40, 24, 34, 43, 35,  8, 26, 21, 32, 28, 44, 14,
       16, 12, 41,  4,  9, 25,  6, 15, 20,  1, 23, 31, 27, 19,  5, 22, 10,
       29, 13, 11,  0, 39,  3, 18, 33, 38,  7, 17], dtype=int64), 'cur_cost': 90080.0}]
2025-08-05 10:28:59,896 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:59,896 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 260, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 260, 'cache_hits': 0, 'similarity_calculations': 1278, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:59,897 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([36, 42,  2, 37, 30, 40, 24, 34, 43, 35,  8, 26, 21, 32, 28, 44, 14,
       16, 12, 41,  4,  9, 25,  6, 15, 20,  1, 23, 31, 27, 19,  5, 22, 10,
       29, 13, 11,  0, 39,  3, 18, 33, 38,  7, 17], dtype=int64), 'cur_cost': 90080.0, 'intermediate_solutions': [{'tour': array([37, 33, 43, 35,  1, 20, 14, 12, 13, 15, 21, 39, 40, 36, 44,  7, 29,
       18,  4, 30, 19, 26, 16, 17, 34,  0, 41,  6, 28, 42, 22,  3,  9, 27,
        2, 25, 10, 31, 23, 38, 32, 11,  8,  5, 24]), 'cur_cost': 88461.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([35, 37, 33, 43,  1, 20, 14, 12, 13, 15, 21, 39, 40, 36, 44,  7, 29,
       18,  4, 30, 19, 26, 16, 17, 34,  0, 41,  6, 28, 42, 22,  3,  9, 27,
        2, 25, 10, 31, 23, 38, 32, 11,  8,  5, 24]), 'cur_cost': 88441.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1, 35, 37, 33, 43, 20, 14, 12, 13, 15, 21, 39, 40, 36, 44,  7, 29,
       18,  4, 30, 19, 26, 16, 17, 34,  0, 41,  6, 28, 42, 22,  3,  9, 27,
        2, 25, 10, 31, 23, 38, 32, 11,  8,  5, 24]), 'cur_cost': 88580.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([43, 35, 37, 33,  1, 20, 14, 12, 13, 15, 21, 39, 40, 36, 44,  7, 29,
       18,  4, 30, 19, 26, 16, 17, 34,  0, 41,  6, 28, 42, 22,  3,  9, 27,
        2, 25, 10, 31, 23, 38, 32, 11,  8,  5, 24]), 'cur_cost': 88514.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([43,  1, 35, 37, 33, 20, 14, 12, 13, 15, 21, 39, 40, 36, 44,  7, 29,
       18,  4, 30, 19, 26, 16, 17, 34,  0, 41,  6, 28, 42, 22,  3,  9, 27,
        2, 25, 10, 31, 23, 38, 32, 11,  8,  5, 24]), 'cur_cost': 90848.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:59,897 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 90080.00)
2025-08-05 10:28:59,897 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:59,897 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:59,900 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 22, 20, 3, 7, 9, 5, 6, 4, 1, 8, 2, 10, 17, 19, 18, 21, 12, 14, 11, 13, 15, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29], 'cur_cost': 19215.0, 'intermediate_solutions': [{'tour': [0, 1, 15, 18, 20, 14, 11, 13, 17, 19, 16, 21, 12, 41, 42, 34, 44, 37, 39, 33, 10, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 43, 8, 7, 9, 5, 3, 2, 6, 4], 'cur_cost': 19694.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 15, 18, 20, 14, 11, 13, 17, 19, 16, 21, 12, 41, 42, 27, 25, 24, 28, 30, 32, 23, 40, 38, 35, 36, 43, 33, 39, 37, 44, 34, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 6, 4], 'cur_cost': 17255.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [31, 0, 1, 15, 18, 20, 14, 11, 13, 17, 19, 16, 21, 12, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 29, 22, 10, 8, 7, 9, 5, 3, 2, 6, 4], 'cur_cost': 18738.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 25, 8, 7, 9, 5, 3, 2, 1, 4, 6, 10, 17, 19, 18, 20, 21, 12, 16, 11, 13, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 22, 27, 26, 31, 29], 'cur_cost': 19279.0, 'intermediate_solutions': [{'tour': [32, 11, 17, 18, 7, 34, 5, 3, 4, 8, 2, 25, 9, 0, 20, 14, 12, 13, 15, 41, 21, 16, 42, 33, 36, 30, 27, 1, 37, 26, 29, 40, 44, 24, 23, 10, 22, 28, 19, 38, 39, 31, 6, 35, 43], 'cur_cost': 67584.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [32, 11, 17, 18, 7, 13, 12, 14, 20, 0, 19, 25, 2, 8, 4, 3, 5, 34, 15, 41, 21, 16, 42, 33, 36, 30, 27, 1, 37, 26, 29, 40, 44, 24, 23, 10, 22, 28, 9, 38, 39, 31, 6, 35, 43], 'cur_cost': 71689.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [32, 11, 17, 18, 7, 34, 5, 3, 4, 8, 2, 25, 19, 0, 20, 14, 12, 13, 15, 41, 21, 16, 42, 36, 30, 27, 1, 37, 26, 29, 40, 44, 24, 23, 33, 10, 22, 28, 9, 38, 39, 31, 6, 35, 43], 'cur_cost': 73520.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [14, 33, 42, 38, 36, 15, 18, 44, 34, 19, 17, 16, 13, 35, 37, 41, 12, 20, 31, 25, 28, 30, 43, 29, 32, 21, 23, 40, 2, 8, 4, 5, 1, 6, 3, 7, 10, 0, 39, 27, 22, 26, 24, 11, 9], 'cur_cost': 40440.0, 'intermediate_solutions': [{'tour': [32, 30, 26, 29, 21, 34, 35, 39, 11, 44, 17, 12, 16, 43, 23, 28, 22, 42, 40, 37, 36, 20, 13, 0, 10, 4, 6, 1, 18, 14, 33, 27, 15, 31, 41, 38, 19, 2, 8, 9, 7, 3, 5, 25, 24], 'cur_cost': 40661.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [32, 30, 21, 29, 26, 34, 35, 20, 11, 44, 17, 12, 16, 43, 23, 28, 22, 42, 40, 37, 36, 39, 13, 0, 10, 4, 6, 1, 18, 14, 33, 27, 15, 31, 41, 38, 19, 2, 8, 9, 7, 3, 5, 25, 24], 'cur_cost': 44298.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [32, 30, 26, 29, 21, 34, 35, 20, 11, 44, 12, 16, 43, 23, 28, 22, 42, 40, 37, 36, 17, 39, 13, 0, 10, 4, 6, 1, 18, 14, 33, 27, 15, 31, 41, 38, 19, 2, 8, 9, 7, 3, 5, 25, 24], 'cur_cost': 43334.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([12, 31,  4, 40, 19, 14, 43,  3, 30, 29, 23, 36, 22, 11, 44, 13, 17,
       26, 21, 38, 24, 33,  7, 10,  8, 20,  6,  2, 25,  0, 35, 34, 39, 27,
       18, 28,  9,  1, 15, 16,  5, 42, 32, 37, 41], dtype=int64), 'cur_cost': 85474.0, 'intermediate_solutions': [{'tour': array([ 8, 26, 15, 43,  3, 28,  7, 44, 33, 39, 23, 18, 29, 21,  1, 38, 34,
       36, 27, 25, 19, 17, 35, 30, 11, 24, 32, 41, 12,  2,  9, 14,  4, 31,
       10,  0,  6, 16, 42, 20, 22, 13, 40, 37,  5]), 'cur_cost': 81596.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([43,  8, 26, 15,  3, 28,  7, 44, 33, 39, 23, 18, 29, 21,  1, 38, 34,
       36, 27, 25, 19, 17, 35, 30, 11, 24, 32, 41, 12,  2,  9, 14,  4, 31,
       10,  0,  6, 16, 42, 20, 22, 13, 40, 37,  5]), 'cur_cost': 85305.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3, 43,  8, 26, 15, 28,  7, 44, 33, 39, 23, 18, 29, 21,  1, 38, 34,
       36, 27, 25, 19, 17, 35, 30, 11, 24, 32, 41, 12,  2,  9, 14,  4, 31,
       10,  0,  6, 16, 42, 20, 22, 13, 40, 37,  5]), 'cur_cost': 81434.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([15, 43,  8, 26,  3, 28,  7, 44, 33, 39, 23, 18, 29, 21,  1, 38, 34,
       36, 27, 25, 19, 17, 35, 30, 11, 24, 32, 41, 12,  2,  9, 14,  4, 31,
       10,  0,  6, 16, 42, 20, 22, 13, 40, 37,  5]), 'cur_cost': 85382.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([15,  3, 43,  8, 26, 28,  7, 44, 33, 39, 23, 18, 29, 21,  1, 38, 34,
       36, 27, 25, 19, 17, 35, 30, 11, 24, 32, 41, 12,  2,  9, 14,  4, 31,
       10,  0,  6, 16, 42, 20, 22, 13, 40, 37,  5]), 'cur_cost': 81578.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 21, 17, 19, 18, 20, 14, 11, 13, 15, 12, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 11389.0, 'intermediate_solutions': [{'tour': [30, 23, 29, 20, 14, 5, 13, 17, 8, 16, 25, 12, 43, 26, 28, 3, 40, 41, 21, 39, 42, 33, 10, 4, 27, 37, 18, 7, 31, 24, 22, 38, 19, 0, 1, 35, 32, 6, 44, 11, 34, 15, 36, 9, 2], 'cur_cost': 78560.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [30, 26, 29, 20, 14, 5, 13, 17, 8, 16, 25, 12, 43, 23, 28, 3, 40, 41, 21, 39, 42, 33, 10, 4, 27, 37, 18, 7, 31, 24, 22, 38, 19, 0, 1, 35, 32, 6, 44, 11, 34, 15, 36, 2, 9], 'cur_cost': 78492.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [30, 26, 29, 20, 14, 5, 13, 17, 8, 16, 25, 12, 43, 23, 28, 3, 40, 41, 39, 42, 33, 10, 4, 27, 37, 18, 7, 31, 24, 22, 38, 21, 19, 0, 1, 35, 32, 6, 44, 11, 34, 15, 36, 9, 2], 'cur_cost': 75858.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([15,  6, 39, 37, 42, 17,  1, 13,  0, 34, 16, 35, 33,  5, 38,  7,  9,
       11,  8, 14, 44, 31, 36,  2, 10, 41, 12, 23,  4, 18, 43, 26, 28, 20,
       40, 22, 32, 19, 29,  3, 21, 30, 27, 25, 24], dtype=int64), 'cur_cost': 82722.0, 'intermediate_solutions': [{'tour': array([ 5, 34, 16,  1,  0, 29,  7, 12, 17, 31, 40,  8, 42, 28,  4, 41,  3,
        2, 11, 25,  6, 22, 27, 24, 37, 33, 32, 23, 30, 38, 26, 43,  9, 20,
       19, 44, 36, 21, 18, 15, 13, 14, 10, 35, 39]), 'cur_cost': 83719.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1,  5, 34, 16,  0, 29,  7, 12, 17, 31, 40,  8, 42, 28,  4, 41,  3,
        2, 11, 25,  6, 22, 27, 24, 37, 33, 32, 23, 30, 38, 26, 43,  9, 20,
       19, 44, 36, 21, 18, 15, 13, 14, 10, 35, 39]), 'cur_cost': 83693.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 0,  1,  5, 34, 16, 29,  7, 12, 17, 31, 40,  8, 42, 28,  4, 41,  3,
        2, 11, 25,  6, 22, 27, 24, 37, 33, 32, 23, 30, 38, 26, 43,  9, 20,
       19, 44, 36, 21, 18, 15, 13, 14, 10, 35, 39]), 'cur_cost': 80070.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([16,  1,  5, 34,  0, 29,  7, 12, 17, 31, 40,  8, 42, 28,  4, 41,  3,
        2, 11, 25,  6, 22, 27, 24, 37, 33, 32, 23, 30, 38, 26, 43,  9, 20,
       19, 44, 36, 21, 18, 15, 13, 14, 10, 35, 39]), 'cur_cost': 83706.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([16,  0,  1,  5, 34, 29,  7, 12, 17, 31, 40,  8, 42, 28,  4, 41,  3,
        2, 11, 25,  6, 22, 27, 24, 37, 33, 32, 23, 30, 38, 26, 43,  9, 20,
       19, 44, 36, 21, 18, 15, 13, 14, 10, 35, 39]), 'cur_cost': 79916.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 4, 18, 20, 21, 12, 16, 19, 17, 13, 11, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 5, 3, 2, 1, 6], 'cur_cost': 15612.0, 'intermediate_solutions': [{'tour': [0, 5, 11, 24, 28, 25, 27, 26, 31, 2, 23, 32, 30, 22, 15, 13, 17, 19, 18, 20, 14, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 10, 8, 7, 9, 3, 29, 1, 4, 6], 'cur_cost': 26865.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 11, 24, 28, 25, 27, 26, 31, 29, 23, 32, 30, 22, 15, 13, 17, 19, 18, 20, 14, 12, 21, 16, 42, 41, 34, 10, 40, 38, 35, 36, 43, 33, 39, 37, 44, 8, 7, 9, 3, 2, 1, 4, 6], 'cur_cost': 17929.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 11, 24, 28, 25, 27, 31, 29, 23, 32, 30, 22, 15, 13, 17, 19, 18, 20, 14, 12, 21, 16, 42, 41, 26, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 10, 8, 7, 9, 3, 2, 1, 4, 6], 'cur_cost': 17858.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [3, 9, 19, 40, 13, 38, 16, 34, 39, 17, 10, 20, 21, 12, 11, 15, 31, 30, 18, 23, 36, 41, 37, 43, 42, 14, 26, 24, 32, 22, 29, 25, 35, 28, 0, 7, 8, 5, 1, 4, 6, 44, 33, 27, 2], 'cur_cost': 48019.0, 'intermediate_solutions': [{'tour': [0, 9, 22, 8, 7, 3, 5, 6, 4, 1, 10, 2, 17, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 40, 35, 38, 36, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29], 'cur_cost': 18861.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 22, 8, 7, 3, 5, 6, 4, 1, 10, 2, 17, 19, 18, 20, 14, 11, 13, 15, 12, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 31, 26, 27, 25, 24, 29], 'cur_cost': 18960.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 22, 8, 7, 3, 5, 6, 4, 1, 10, 2, 17, 19, 18, 20, 14, 11, 13, 15, 21, 16, 42, 41, 34, 44, 37, 39, 33, 43, 12, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29], 'cur_cost': 21598.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 13, 1, 4, 6, 5, 9, 7, 3, 2, 8, 10, 17, 19, 11, 14, 20, 21, 12, 16, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22], 'cur_cost': 15477.0, 'intermediate_solutions': [{'tour': [0, 5, 16, 20, 14, 11, 13, 17, 15, 18, 21, 12, 19, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 3, 2, 1, 4, 6], 'cur_cost': 11564.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 16, 20, 14, 11, 13, 17, 19, 18, 21, 12, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 3, 1, 2, 4, 6], 'cur_cost': 11615.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 16, 20, 14, 11, 13, 17, 19, 21, 12, 15, 41, 42, 34, 44, 37, 39, 33, 43, 36, 35, 38, 40, 23, 32, 30, 28, 24, 25, 27, 26, 31, 29, 22, 10, 8, 7, 9, 3, 2, 1, 4, 6, 18], 'cur_cost': 15677.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([36, 42,  2, 37, 30, 40, 24, 34, 43, 35,  8, 26, 21, 32, 28, 44, 14,
       16, 12, 41,  4,  9, 25,  6, 15, 20,  1, 23, 31, 27, 19,  5, 22, 10,
       29, 13, 11,  0, 39,  3, 18, 33, 38,  7, 17], dtype=int64), 'cur_cost': 90080.0, 'intermediate_solutions': [{'tour': array([37, 33, 43, 35,  1, 20, 14, 12, 13, 15, 21, 39, 40, 36, 44,  7, 29,
       18,  4, 30, 19, 26, 16, 17, 34,  0, 41,  6, 28, 42, 22,  3,  9, 27,
        2, 25, 10, 31, 23, 38, 32, 11,  8,  5, 24]), 'cur_cost': 88461.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([35, 37, 33, 43,  1, 20, 14, 12, 13, 15, 21, 39, 40, 36, 44,  7, 29,
       18,  4, 30, 19, 26, 16, 17, 34,  0, 41,  6, 28, 42, 22,  3,  9, 27,
        2, 25, 10, 31, 23, 38, 32, 11,  8,  5, 24]), 'cur_cost': 88441.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1, 35, 37, 33, 43, 20, 14, 12, 13, 15, 21, 39, 40, 36, 44,  7, 29,
       18,  4, 30, 19, 26, 16, 17, 34,  0, 41,  6, 28, 42, 22,  3,  9, 27,
        2, 25, 10, 31, 23, 38, 32, 11,  8,  5, 24]), 'cur_cost': 88580.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([43, 35, 37, 33,  1, 20, 14, 12, 13, 15, 21, 39, 40, 36, 44,  7, 29,
       18,  4, 30, 19, 26, 16, 17, 34,  0, 41,  6, 28, 42, 22,  3,  9, 27,
        2, 25, 10, 31, 23, 38, 32, 11,  8,  5, 24]), 'cur_cost': 88514.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([43,  1, 35, 37, 33, 20, 14, 12, 13, 15, 21, 39, 40, 36, 44,  7, 29,
       18,  4, 30, 19, 26, 16, 17, 34,  0, 41,  6, 28, 42, 22,  3,  9, 27,
        2, 25, 10, 31, 23, 38, 32, 11,  8,  5, 24]), 'cur_cost': 90848.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:28:59,900 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:59,900 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:59,903 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=11389.000, 多样性=0.957
2025-08-05 10:28:59,903 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 10:28:59,903 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 10:28:59,903 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:59,905 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.04011352587802456, 'best_improvement': -0.002199929602252728}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.008328995314940165}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.07357253940986387, 'recent_improvements': [0.12435939368335559, -0.036831456837985665, -0.02278568513637215], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 11, 'new_count': 11, 'count_change': 0, 'old_best_cost': 10973.0, 'new_best_cost': 10973.0, 'quality_improvement': 0.0, 'old_diversity': 0.7604040404040404, 'new_diversity': 0.7604040404040404, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:59,906 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 10:28:59,912 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite8_45_solution.json
2025-08-05 10:28:59,912 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite8_45_20250805_102859.solution
2025-08-05 10:28:59,913 - __main__ - INFO - 实例执行完成 - 运行时间: 1.77s, 最佳成本: 10973.0
2025-08-05 10:28:59,913 - __main__ - INFO - 实例 composite8_45 处理完成
