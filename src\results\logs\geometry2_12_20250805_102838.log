2025-08-05 10:28:38,643 - __main__ - INFO - geometry2_12 开始进化第 1 代
2025-08-05 10:28:38,643 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 10:28:38,643 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:38,645 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1344.000, 多样性=0.924
2025-08-05 10:28:38,646 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:38,648 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.924
2025-08-05 10:28:38,671 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:38,674 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 10:28:38,674 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:38,675 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 10:28:38,675 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 10:28:38,685 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -57.760, 聚类评分: 0.000, 覆盖率: 0.041, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:38,685 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 10:28:38,685 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 10:28:38,686 - visualization.landscape_visualizer - INFO - 设置当前实例名: geometry2_12
2025-08-05 10:28:38,693 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=5.1%, 梯度: 20.64 → 19.59
2025-08-05 10:28:38,834 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry2_12\landscape_geometry2_12_iter_36_20250805_102838.html
2025-08-05 10:28:38,900 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry2_12\dashboard_geometry2_12_iter_36_20250805_102838.html
2025-08-05 10:28:38,900 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 36
2025-08-05 10:28:38,900 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 10:28:38,900 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2259秒
2025-08-05 10:28:38,901 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 72, 'max_size': 500, 'hits': 0, 'misses': 72, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 245, 'misses': 116, 'hit_rate': 0.6786703601108033, 'evictions': 16, 'ttl': 7200}}
2025-08-05 10:28:38,901 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -57.760000000000005, 'local_optima_density': 0.1, 'gradient_variance': 144329.5424, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0411, 'fitness_entropy': 0.9349775297671233, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -57.760)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.041)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360918.6851478, 'performance_metrics': {}}}
2025-08-05 10:28:38,901 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:38,901 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:38,901 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:38,901 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:38,902 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:28:38,902 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 10:28:38,902 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:28:38,902 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:38,902 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:38,902 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:28:38,902 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:38,903 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:38,903 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:38,903 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:38,903 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:38,903 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:38,904 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:38,904 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,904 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1620.0, 路径长度: 12, 收集中间解: 0
2025-08-05 10:28:38,904 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [7, 8, 1, 2, 5, 4, 0, 11, 10, 9, 3, 6], 'cur_cost': 1620.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:38,904 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 1620.00)
2025-08-05 10:28:38,904 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:38,904 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:38,905 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:38,905 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:38,905 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,905 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1797.0, 路径长度: 12, 收集中间解: 0
2025-08-05 10:28:38,905 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [1, 4, 9, 3, 11, 0, 5, 10, 2, 7, 6, 8], 'cur_cost': 1797.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:38,906 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 1797.00)
2025-08-05 10:28:38,906 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:38,906 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:38,906 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:38,907 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:38,907 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,907 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1604.0, 路径长度: 12, 收集中间解: 0
2025-08-05 10:28:38,908 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [1, 11, 4, 0, 2, 3, 5, 10, 9, 8, 7, 6], 'cur_cost': 1604.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:38,908 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1604.00)
2025-08-05 10:28:38,908 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:38,908 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:38,908 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:38,909 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:38,909 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,909 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1542.0, 路径长度: 12, 收集中间解: 0
2025-08-05 10:28:38,909 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [2, 3, 1, 5, 0, 6, 7, 8, 9, 4, 10, 11], 'cur_cost': 1542.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:38,909 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1542.00)
2025-08-05 10:28:38,909 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:38,909 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:38,910 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:38,910 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 12
2025-08-05 10:28:38,910 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,910 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1691.0, 路径长度: 12, 收集中间解: 0
2025-08-05 10:28:38,910 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [3, 9, 0, 5, 11, 10, 6, 7, 2, 8, 1, 4], 'cur_cost': 1691.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:38,911 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1691.00)
2025-08-05 10:28:38,911 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:38,911 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:38,911 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:38,911 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:38,911 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,911 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2084.0, 路径长度: 12, 收集中间解: 0
2025-08-05 10:28:38,912 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [3, 7, 0, 9, 1, 11, 5, 4, 2, 8, 6, 10], 'cur_cost': 2084.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:38,912 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 2084.00)
2025-08-05 10:28:38,912 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:28:38,912 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:38,912 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:38,912 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1783.0
2025-08-05 10:28:38,918 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:38,918 - ExploitationExpert - INFO - res_population_costs: [1344.0, 1344, 1344, 1344]
2025-08-05 10:28:38,919 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0,  5, 10,  4,  3,  9,  8,  2,  1,  7,  6, 11], dtype=int64)]
2025-08-05 10:28:38,920 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:38,920 - ExploitationExpert - INFO - populations: [{'tour': [7, 8, 1, 2, 5, 4, 0, 11, 10, 9, 3, 6], 'cur_cost': 1620.0}, {'tour': [1, 4, 9, 3, 11, 0, 5, 10, 2, 7, 6, 8], 'cur_cost': 1797.0}, {'tour': [1, 11, 4, 0, 2, 3, 5, 10, 9, 8, 7, 6], 'cur_cost': 1604.0}, {'tour': [2, 3, 1, 5, 0, 6, 7, 8, 9, 4, 10, 11], 'cur_cost': 1542.0}, {'tour': [3, 9, 0, 5, 11, 10, 6, 7, 2, 8, 1, 4], 'cur_cost': 1691.0}, {'tour': [3, 7, 0, 9, 1, 11, 5, 4, 2, 8, 6, 10], 'cur_cost': 2084.0}, {'tour': array([ 4,  0,  8,  6,  7,  2,  1, 11, 10,  9,  5,  3], dtype=int64), 'cur_cost': 1783.0}, {'tour': array([ 8, 11,  0,  1,  3,  4,  5,  2,  9, 10,  7,  6], dtype=int64), 'cur_cost': 1955.0}, {'tour': array([10,  0,  8,  2, 11,  3,  1,  9,  5,  4,  7,  6], dtype=int64), 'cur_cost': 2161.0}, {'tour': array([ 1,  8, 11, 10,  2,  6,  7,  4,  9,  5,  3,  0], dtype=int64), 'cur_cost': 2012.0}]
2025-08-05 10:28:38,921 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:38,921 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 92, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 92, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:38,922 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([ 4,  0,  8,  6,  7,  2,  1, 11, 10,  9,  5,  3], dtype=int64), 'cur_cost': 1783.0, 'intermediate_solutions': [{'tour': array([ 1,  2,  7,  9, 11,  5,  0,  8,  4,  6,  3, 10], dtype=int64), 'cur_cost': 2127.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9,  1,  2,  7, 11,  5,  0,  8,  4,  6,  3, 10], dtype=int64), 'cur_cost': 2009.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([11,  9,  1,  2,  7,  5,  0,  8,  4,  6,  3, 10], dtype=int64), 'cur_cost': 2115.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 7,  9,  1,  2, 11,  5,  0,  8,  4,  6,  3, 10], dtype=int64), 'cur_cost': 2276.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 7, 11,  9,  1,  2,  5,  0,  8,  4,  6,  3, 10], dtype=int64), 'cur_cost': 2351.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:38,922 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1783.00)
2025-08-05 10:28:38,922 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:38,922 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:38,923 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:38,923 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:38,924 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,924 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1727.0, 路径长度: 12, 收集中间解: 0
2025-08-05 10:28:38,924 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 3, 2, 4, 1, 10, 5, 11, 6, 7, 8, 9], 'cur_cost': 1727.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:38,924 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1727.00)
2025-08-05 10:28:38,924 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:38,924 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:38,924 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:38,925 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 2058.0
2025-08-05 10:28:38,932 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 10:28:38,933 - ExploitationExpert - INFO - res_population_costs: [1344.0, 1344, 1344, 1344, 1344.0, 1344]
2025-08-05 10:28:38,933 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0,  5, 10,  4,  3,  9,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  7,  8,  9, 10,  5,  4,  3,  2,  1], dtype=int64), array([ 0,  5, 11,  6,  7,  8,  9, 10,  4,  3,  2,  1], dtype=int64)]
2025-08-05 10:28:38,934 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:38,934 - ExploitationExpert - INFO - populations: [{'tour': [7, 8, 1, 2, 5, 4, 0, 11, 10, 9, 3, 6], 'cur_cost': 1620.0}, {'tour': [1, 4, 9, 3, 11, 0, 5, 10, 2, 7, 6, 8], 'cur_cost': 1797.0}, {'tour': [1, 11, 4, 0, 2, 3, 5, 10, 9, 8, 7, 6], 'cur_cost': 1604.0}, {'tour': [2, 3, 1, 5, 0, 6, 7, 8, 9, 4, 10, 11], 'cur_cost': 1542.0}, {'tour': [3, 9, 0, 5, 11, 10, 6, 7, 2, 8, 1, 4], 'cur_cost': 1691.0}, {'tour': [3, 7, 0, 9, 1, 11, 5, 4, 2, 8, 6, 10], 'cur_cost': 2084.0}, {'tour': array([ 4,  0,  8,  6,  7,  2,  1, 11, 10,  9,  5,  3], dtype=int64), 'cur_cost': 1783.0}, {'tour': [0, 3, 2, 4, 1, 10, 5, 11, 6, 7, 8, 9], 'cur_cost': 1727.0}, {'tour': array([11, 10,  2,  9,  0,  1,  3,  7,  8,  5,  6,  4], dtype=int64), 'cur_cost': 2058.0}, {'tour': array([ 1,  8, 11, 10,  2,  6,  7,  4,  9,  5,  3,  0], dtype=int64), 'cur_cost': 2012.0}]
2025-08-05 10:28:38,935 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:38,935 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 93, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 93, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:38,935 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([11, 10,  2,  9,  0,  1,  3,  7,  8,  5,  6,  4], dtype=int64), 'cur_cost': 2058.0, 'intermediate_solutions': [{'tour': array([ 8,  0, 10,  2, 11,  3,  1,  9,  5,  4,  7,  6], dtype=int64), 'cur_cost': 2267.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2,  8,  0, 10, 11,  3,  1,  9,  5,  4,  7,  6], dtype=int64), 'cur_cost': 1999.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([11,  2,  8,  0, 10,  3,  1,  9,  5,  4,  7,  6], dtype=int64), 'cur_cost': 1999.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([10,  2,  8,  0, 11,  3,  1,  9,  5,  4,  7,  6], dtype=int64), 'cur_cost': 2100.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([10, 11,  2,  8,  0,  3,  1,  9,  5,  4,  7,  6], dtype=int64), 'cur_cost': 2057.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:38,936 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 2058.00)
2025-08-05 10:28:38,936 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:38,936 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:38,936 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:38,936 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 12
2025-08-05 10:28:38,936 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:38,937 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2062.0, 路径长度: 12, 收集中间解: 0
2025-08-05 10:28:38,937 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [7, 4, 9, 3, 5, 0, 6, 8, 10, 1, 2, 11], 'cur_cost': 2062.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:38,937 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 2062.00)
2025-08-05 10:28:38,937 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:38,937 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:38,938 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [7, 8, 1, 2, 5, 4, 0, 11, 10, 9, 3, 6], 'cur_cost': 1620.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 9, 3, 11, 0, 5, 10, 2, 7, 6, 8], 'cur_cost': 1797.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [1, 11, 4, 0, 2, 3, 5, 10, 9, 8, 7, 6], 'cur_cost': 1604.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [2, 3, 1, 5, 0, 6, 7, 8, 9, 4, 10, 11], 'cur_cost': 1542.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [3, 9, 0, 5, 11, 10, 6, 7, 2, 8, 1, 4], 'cur_cost': 1691.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [3, 7, 0, 9, 1, 11, 5, 4, 2, 8, 6, 10], 'cur_cost': 2084.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 4,  0,  8,  6,  7,  2,  1, 11, 10,  9,  5,  3], dtype=int64), 'cur_cost': 1783.0, 'intermediate_solutions': [{'tour': array([ 1,  2,  7,  9, 11,  5,  0,  8,  4,  6,  3, 10], dtype=int64), 'cur_cost': 2127.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9,  1,  2,  7, 11,  5,  0,  8,  4,  6,  3, 10], dtype=int64), 'cur_cost': 2009.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([11,  9,  1,  2,  7,  5,  0,  8,  4,  6,  3, 10], dtype=int64), 'cur_cost': 2115.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 7,  9,  1,  2, 11,  5,  0,  8,  4,  6,  3, 10], dtype=int64), 'cur_cost': 2276.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 7, 11,  9,  1,  2,  5,  0,  8,  4,  6,  3, 10], dtype=int64), 'cur_cost': 2351.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 2, 4, 1, 10, 5, 11, 6, 7, 8, 9], 'cur_cost': 1727.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([11, 10,  2,  9,  0,  1,  3,  7,  8,  5,  6,  4], dtype=int64), 'cur_cost': 2058.0, 'intermediate_solutions': [{'tour': array([ 8,  0, 10,  2, 11,  3,  1,  9,  5,  4,  7,  6], dtype=int64), 'cur_cost': 2267.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2,  8,  0, 10, 11,  3,  1,  9,  5,  4,  7,  6], dtype=int64), 'cur_cost': 1999.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([11,  2,  8,  0, 10,  3,  1,  9,  5,  4,  7,  6], dtype=int64), 'cur_cost': 1999.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([10,  2,  8,  0, 11,  3,  1,  9,  5,  4,  7,  6], dtype=int64), 'cur_cost': 2100.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([10, 11,  2,  8,  0,  3,  1,  9,  5,  4,  7,  6], dtype=int64), 'cur_cost': 2057.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [7, 4, 9, 3, 5, 0, 6, 8, 10, 1, 2, 11], 'cur_cost': 2062.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:38,938 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:38,938 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:38,939 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1542.000, 多样性=0.909
2025-08-05 10:28:38,940 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 10:28:38,940 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 10:28:38,940 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:38,940 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.029331509253255394, 'best_improvement': -0.14732142857142858}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.016032064128256772}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.015476443673479038, 'recent_improvements': [0.029904714714085973, -0.054797732988600766, -0.0010481726328720983], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 6, 'new_count': 6, 'count_change': 0, 'old_best_cost': 1344.0, 'new_best_cost': 1344.0, 'quality_improvement': 0.0, 'old_diversity': 0.7222222222222222, 'new_diversity': 0.7222222222222222, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配']}
2025-08-05 10:28:38,940 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 10:28:38,940 - __main__ - INFO - geometry2_12 开始进化第 2 代
2025-08-05 10:28:38,940 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 10:28:38,941 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:38,941 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1542.000, 多样性=0.909
2025-08-05 10:28:38,941 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:38,942 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.909
2025-08-05 10:28:38,942 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:38,943 - EliteExpert - INFO - 精英解分析完成: 精英解数量=6, 多样性=0.722
2025-08-05 10:28:38,944 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 10:28:38,944 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:38,944 - LandscapeExpert - INFO - 添加精英解数据: 6个精英解
2025-08-05 10:28:38,944 - LandscapeExpert - INFO - 数据提取成功: 16个路径, 16个适应度值
2025-08-05 10:28:38,955 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.438, 适应度梯度: 22.087, 聚类评分: 0.000, 覆盖率: 0.043, 收敛趋势: 0.000, 多样性: 0.671
2025-08-05 10:28:38,955 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 10:28:38,956 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:38,956 - visualization.landscape_visualizer - INFO - 设置当前实例名: geometry2_12
2025-08-05 10:28:38,959 - visualization.landscape_visualizer - INFO - 插值约束: 63 个点被约束到最小值 1344.00
2025-08-05 10:28:38,960 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=10.0%, 梯度: 39.84 → 35.86
2025-08-05 10:28:39,087 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry2_12\landscape_geometry2_12_iter_37_20250805_102839.html
2025-08-05 10:28:39,169 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry2_12\dashboard_geometry2_12_iter_37_20250805_102839.html
2025-08-05 10:28:39,169 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 37
2025-08-05 10:28:39,170 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 10:28:39,170 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2255秒
2025-08-05 10:28:39,170 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.4375, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 22.0875, 'local_optima_density': 0.4375, 'gradient_variance': 50578.959843749995, 'cluster_count': 0}, 'population_state': {'diversity': 0.6713541666666667, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0427, 'fitness_entropy': 0.8846257998344362, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.043)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 22.087)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360918.9556146, 'performance_metrics': {}}}
2025-08-05 10:28:39,170 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:39,170 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:39,171 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:39,171 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:39,171 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:39,171 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 10:28:39,171 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:39,171 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:39,172 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:39,172 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:39,172 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:39,172 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:39,172 - experts.management.collaboration_manager - INFO - 识别精英个体: {2, 3} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:39,172 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:39,172 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:39,172 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:39,173 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:39,173 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,173 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,173 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,173 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,173 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1735.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:39,174 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [2, 9, 1, 7, 6, 0, 5, 4, 3, 8, 10, 11], 'cur_cost': 1735.0, 'intermediate_solutions': [{'tour': [7, 8, 11, 2, 5, 4, 0, 1, 10, 9, 3, 6], 'cur_cost': 1933.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 8, 1, 2, 11, 0, 4, 5, 10, 9, 3, 6], 'cur_cost': 1663.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 8, 1, 2, 5, 4, 11, 10, 9, 3, 6], 'cur_cost': 1697.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:39,174 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1735.00)
2025-08-05 10:28:39,174 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:39,174 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:39,174 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:39,174 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:39,175 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,175 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,175 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,175 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,175 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1614.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:39,175 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 5, 10, 4, 3, 2, 7, 6, 11, 9, 8], 'cur_cost': 1614.0, 'intermediate_solutions': [{'tour': [1, 4, 10, 3, 11, 0, 5, 9, 2, 7, 6, 8], 'cur_cost': 1876.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 9, 3, 11, 0, 5, 10, 6, 7, 2, 8], 'cur_cost': 1691.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 9, 3, 11, 0, 8, 5, 10, 2, 7, 6], 'cur_cost': 1830.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:39,175 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1614.00)
2025-08-05 10:28:39,176 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:39,176 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:39,176 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:39,176 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:39,176 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,176 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,176 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,176 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,177 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1826.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:39,177 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [2, 10, 4, 7, 1, 6, 0, 5, 3, 8, 9, 11], 'cur_cost': 1826.0, 'intermediate_solutions': [{'tour': [1, 11, 4, 5, 2, 3, 0, 10, 9, 8, 7, 6], 'cur_cost': 1653.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 11, 5, 3, 2, 0, 4, 10, 9, 8, 7, 6], 'cur_cost': 1543.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 11, 5, 4, 0, 2, 3, 10, 9, 8, 7, 6], 'cur_cost': 1559.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:39,177 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 1826.00)
2025-08-05 10:28:39,177 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:39,177 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:39,177 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:39,177 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:39,178 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,178 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,178 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,178 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,178 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2053.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:39,178 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [3, 11, 1, 8, 5, 6, 0, 2, 4, 9, 10, 7], 'cur_cost': 2053.0, 'intermediate_solutions': [{'tour': [2, 3, 6, 5, 0, 1, 7, 8, 9, 4, 10, 11], 'cur_cost': 1617.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 3, 1, 9, 8, 7, 6, 0, 5, 4, 10, 11], 'cur_cost': 1602.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 3, 1, 5, 0, 6, 8, 9, 7, 4, 10, 11], 'cur_cost': 1884.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:39,178 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 2053.00)
2025-08-05 10:28:39,179 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:39,179 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:39,179 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:39,179 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 12
2025-08-05 10:28:39,179 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,179 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,179 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,179 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,180 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2234.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:39,180 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [11, 7, 4, 8, 6, 3, 2, 5, 10, 1, 9, 0], 'cur_cost': 2234.0, 'intermediate_solutions': [{'tour': [3, 9, 0, 2, 11, 10, 6, 7, 5, 8, 1, 4], 'cur_cost': 2055.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 9, 0, 5, 11, 4, 1, 8, 2, 7, 6, 10], 'cur_cost': 1814.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 9, 0, 4, 5, 11, 10, 6, 7, 2, 8, 1], 'cur_cost': 1721.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:39,180 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 2234.00)
2025-08-05 10:28:39,180 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:28:39,180 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:39,180 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:39,180 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 2100.0
2025-08-05 10:28:39,186 - ExploitationExpert - INFO - res_population_num: 11
2025-08-05 10:28:39,187 - ExploitationExpert - INFO - res_population_costs: [1344.0, 1344, 1344, 1344, 1344.0, 1344, 1344.0, 1344, 1344, 1344, 1344]
2025-08-05 10:28:39,187 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0,  5, 10,  4,  3,  9,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  7,  8,  9, 10,  5,  4,  3,  2,  1], dtype=int64), array([ 0,  5, 11,  6,  7,  8,  9, 10,  4,  3,  2,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  3,  2,  8,  7,  6,  1], dtype=int64), array([ 0, 11, 10,  9,  8,  7,  6,  1,  2,  3,  4,  5], dtype=int64)]
2025-08-05 10:28:39,189 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:39,189 - ExploitationExpert - INFO - populations: [{'tour': [2, 9, 1, 7, 6, 0, 5, 4, 3, 8, 10, 11], 'cur_cost': 1735.0}, {'tour': [0, 1, 5, 10, 4, 3, 2, 7, 6, 11, 9, 8], 'cur_cost': 1614.0}, {'tour': [2, 10, 4, 7, 1, 6, 0, 5, 3, 8, 9, 11], 'cur_cost': 1826.0}, {'tour': [3, 11, 1, 8, 5, 6, 0, 2, 4, 9, 10, 7], 'cur_cost': 2053.0}, {'tour': [11, 7, 4, 8, 6, 3, 2, 5, 10, 1, 9, 0], 'cur_cost': 2234.0}, {'tour': array([11,  8,  2,  6,  4,  1,  0, 10,  3,  7,  9,  5], dtype=int64), 'cur_cost': 2100.0}, {'tour': [4, 0, 8, 6, 7, 2, 1, 11, 10, 9, 5, 3], 'cur_cost': 1783.0}, {'tour': [0, 3, 2, 4, 1, 10, 5, 11, 6, 7, 8, 9], 'cur_cost': 1727.0}, {'tour': [11, 10, 2, 9, 0, 1, 3, 7, 8, 5, 6, 4], 'cur_cost': 2058.0}, {'tour': [7, 4, 9, 3, 5, 0, 6, 8, 10, 1, 2, 11], 'cur_cost': 2062.0}]
2025-08-05 10:28:39,189 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:39,189 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 94, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 94, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:39,190 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([11,  8,  2,  6,  4,  1,  0, 10,  3,  7,  9,  5], dtype=int64), 'cur_cost': 2100.0, 'intermediate_solutions': [{'tour': array([ 0,  7,  3,  9,  1, 11,  5,  4,  2,  8,  6, 10]), 'cur_cost': 1977.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9,  0,  7,  3,  1, 11,  5,  4,  2,  8,  6, 10]), 'cur_cost': 1962.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1,  9,  0,  7,  3, 11,  5,  4,  2,  8,  6, 10]), 'cur_cost': 2173.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 3,  9,  0,  7,  1, 11,  5,  4,  2,  8,  6, 10]), 'cur_cost': 1917.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 3,  1,  9,  0,  7, 11,  5,  4,  2,  8,  6, 10]), 'cur_cost': 2126.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:39,190 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 2100.00)
2025-08-05 10:28:39,190 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:39,190 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:39,190 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:39,190 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:39,191 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,191 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,191 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,191 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,191 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1781.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:39,191 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [1, 8, 3, 11, 6, 0, 5, 4, 2, 7, 9, 10], 'cur_cost': 1781.0, 'intermediate_solutions': [{'tour': [4, 8, 0, 6, 7, 2, 1, 11, 10, 9, 5, 3], 'cur_cost': 1680.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 0, 8, 6, 7, 2, 1, 11, 9, 10, 5, 3], 'cur_cost': 1839.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 8, 6, 7, 2, 1, 11, 10, 9, 3, 5], 'cur_cost': 1721.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:39,191 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1781.00)
2025-08-05 10:28:39,191 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:39,191 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:39,192 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:39,192 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:39,192 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,192 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,192 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,192 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,193 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1621.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:39,193 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [3, 6, 2, 5, 0, 11, 10, 4, 9, 8, 7, 1], 'cur_cost': 1621.0, 'intermediate_solutions': [{'tour': [0, 3, 2, 4, 1, 10, 5, 11, 6, 7, 9, 8], 'cur_cost': 1845.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 2, 4, 1, 10, 5, 11, 8, 7, 6, 9], 'cur_cost': 2050.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 0, 2, 4, 1, 10, 5, 11, 6, 7, 8, 9], 'cur_cost': 1667.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:39,193 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1621.00)
2025-08-05 10:28:39,193 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:39,193 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:39,193 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:39,193 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:39,193 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,194 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,194 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,194 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,194 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1498.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:39,194 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 4, 3, 9, 8, 2, 1, 5, 10, 11, 6, 7], 'cur_cost': 1498.0, 'intermediate_solutions': [{'tour': [11, 10, 2, 9, 0, 6, 3, 7, 8, 5, 1, 4], 'cur_cost': 2059.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 10, 2, 9, 0, 1, 3, 8, 7, 5, 6, 4], 'cur_cost': 1997.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 2, 9, 0, 1, 3, 7, 8, 5, 6, 11, 4], 'cur_cost': 1952.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:39,194 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1498.00)
2025-08-05 10:28:39,194 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:28:39,195 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:39,195 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:39,195 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 2067.0
2025-08-05 10:28:39,208 - ExploitationExpert - INFO - res_population_num: 14
2025-08-05 10:28:39,208 - ExploitationExpert - INFO - res_population_costs: [1344.0, 1344, 1344, 1344, 1344.0, 1344, 1344.0, 1344, 1344, 1344, 1344, 1344.0, 1344, 1344]
2025-08-05 10:28:39,208 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0,  5, 10,  4,  3,  9,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  7,  8,  9, 10,  5,  4,  3,  2,  1], dtype=int64), array([ 0,  5, 11,  6,  7,  8,  9, 10,  4,  3,  2,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  3,  2,  8,  7,  6,  1], dtype=int64), array([ 0, 11, 10,  9,  8,  7,  6,  1,  2,  3,  4,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9, 10,  4,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  5,  4, 10, 11,  6,  7,  8,  9,  3,  2,  1], dtype=int64)]
2025-08-05 10:28:39,212 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:39,213 - ExploitationExpert - INFO - populations: [{'tour': [2, 9, 1, 7, 6, 0, 5, 4, 3, 8, 10, 11], 'cur_cost': 1735.0}, {'tour': [0, 1, 5, 10, 4, 3, 2, 7, 6, 11, 9, 8], 'cur_cost': 1614.0}, {'tour': [2, 10, 4, 7, 1, 6, 0, 5, 3, 8, 9, 11], 'cur_cost': 1826.0}, {'tour': [3, 11, 1, 8, 5, 6, 0, 2, 4, 9, 10, 7], 'cur_cost': 2053.0}, {'tour': [11, 7, 4, 8, 6, 3, 2, 5, 10, 1, 9, 0], 'cur_cost': 2234.0}, {'tour': array([11,  8,  2,  6,  4,  1,  0, 10,  3,  7,  9,  5], dtype=int64), 'cur_cost': 2100.0}, {'tour': [1, 8, 3, 11, 6, 0, 5, 4, 2, 7, 9, 10], 'cur_cost': 1781.0}, {'tour': [3, 6, 2, 5, 0, 11, 10, 4, 9, 8, 7, 1], 'cur_cost': 1621.0}, {'tour': [0, 4, 3, 9, 8, 2, 1, 5, 10, 11, 6, 7], 'cur_cost': 1498.0}, {'tour': array([ 8,  1,  5,  3,  7,  0,  6,  9,  2,  4, 11, 10], dtype=int64), 'cur_cost': 2067.0}]
2025-08-05 10:28:39,214 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:39,214 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 95, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 95, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:39,215 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([ 8,  1,  5,  3,  7,  0,  6,  9,  2,  4, 11, 10], dtype=int64), 'cur_cost': 2067.0, 'intermediate_solutions': [{'tour': array([ 9,  4,  7,  3,  5,  0,  6,  8, 10,  1,  2, 11]), 'cur_cost': 2123.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 3,  9,  4,  7,  5,  0,  6,  8, 10,  1,  2, 11]), 'cur_cost': 2110.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 5,  3,  9,  4,  7,  0,  6,  8, 10,  1,  2, 11]), 'cur_cost': 2006.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 7,  3,  9,  4,  5,  0,  6,  8, 10,  1,  2, 11]), 'cur_cost': 1971.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 7,  5,  3,  9,  4,  0,  6,  8, 10,  1,  2, 11]), 'cur_cost': 2108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:39,215 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 2067.00)
2025-08-05 10:28:39,215 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:39,215 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:39,217 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [2, 9, 1, 7, 6, 0, 5, 4, 3, 8, 10, 11], 'cur_cost': 1735.0, 'intermediate_solutions': [{'tour': [7, 8, 11, 2, 5, 4, 0, 1, 10, 9, 3, 6], 'cur_cost': 1933.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 8, 1, 2, 11, 0, 4, 5, 10, 9, 3, 6], 'cur_cost': 1663.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 8, 1, 2, 5, 4, 11, 10, 9, 3, 6], 'cur_cost': 1697.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 5, 10, 4, 3, 2, 7, 6, 11, 9, 8], 'cur_cost': 1614.0, 'intermediate_solutions': [{'tour': [1, 4, 10, 3, 11, 0, 5, 9, 2, 7, 6, 8], 'cur_cost': 1876.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 9, 3, 11, 0, 5, 10, 6, 7, 2, 8], 'cur_cost': 1691.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 9, 3, 11, 0, 8, 5, 10, 2, 7, 6], 'cur_cost': 1830.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [2, 10, 4, 7, 1, 6, 0, 5, 3, 8, 9, 11], 'cur_cost': 1826.0, 'intermediate_solutions': [{'tour': [1, 11, 4, 5, 2, 3, 0, 10, 9, 8, 7, 6], 'cur_cost': 1653.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 11, 5, 3, 2, 0, 4, 10, 9, 8, 7, 6], 'cur_cost': 1543.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 11, 5, 4, 0, 2, 3, 10, 9, 8, 7, 6], 'cur_cost': 1559.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [3, 11, 1, 8, 5, 6, 0, 2, 4, 9, 10, 7], 'cur_cost': 2053.0, 'intermediate_solutions': [{'tour': [2, 3, 6, 5, 0, 1, 7, 8, 9, 4, 10, 11], 'cur_cost': 1617.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 3, 1, 9, 8, 7, 6, 0, 5, 4, 10, 11], 'cur_cost': 1602.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 3, 1, 5, 0, 6, 8, 9, 7, 4, 10, 11], 'cur_cost': 1884.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [11, 7, 4, 8, 6, 3, 2, 5, 10, 1, 9, 0], 'cur_cost': 2234.0, 'intermediate_solutions': [{'tour': [3, 9, 0, 2, 11, 10, 6, 7, 5, 8, 1, 4], 'cur_cost': 2055.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 9, 0, 5, 11, 4, 1, 8, 2, 7, 6, 10], 'cur_cost': 1814.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 9, 0, 4, 5, 11, 10, 6, 7, 2, 8, 1], 'cur_cost': 1721.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([11,  8,  2,  6,  4,  1,  0, 10,  3,  7,  9,  5], dtype=int64), 'cur_cost': 2100.0, 'intermediate_solutions': [{'tour': array([ 0,  7,  3,  9,  1, 11,  5,  4,  2,  8,  6, 10]), 'cur_cost': 1977.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9,  0,  7,  3,  1, 11,  5,  4,  2,  8,  6, 10]), 'cur_cost': 1962.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1,  9,  0,  7,  3, 11,  5,  4,  2,  8,  6, 10]), 'cur_cost': 2173.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 3,  9,  0,  7,  1, 11,  5,  4,  2,  8,  6, 10]), 'cur_cost': 1917.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 3,  1,  9,  0,  7, 11,  5,  4,  2,  8,  6, 10]), 'cur_cost': 2126.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [1, 8, 3, 11, 6, 0, 5, 4, 2, 7, 9, 10], 'cur_cost': 1781.0, 'intermediate_solutions': [{'tour': [4, 8, 0, 6, 7, 2, 1, 11, 10, 9, 5, 3], 'cur_cost': 1680.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 0, 8, 6, 7, 2, 1, 11, 9, 10, 5, 3], 'cur_cost': 1839.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 8, 6, 7, 2, 1, 11, 10, 9, 3, 5], 'cur_cost': 1721.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [3, 6, 2, 5, 0, 11, 10, 4, 9, 8, 7, 1], 'cur_cost': 1621.0, 'intermediate_solutions': [{'tour': [0, 3, 2, 4, 1, 10, 5, 11, 6, 7, 9, 8], 'cur_cost': 1845.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 2, 4, 1, 10, 5, 11, 8, 7, 6, 9], 'cur_cost': 2050.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 0, 2, 4, 1, 10, 5, 11, 6, 7, 8, 9], 'cur_cost': 1667.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 3, 9, 8, 2, 1, 5, 10, 11, 6, 7], 'cur_cost': 1498.0, 'intermediate_solutions': [{'tour': [11, 10, 2, 9, 0, 6, 3, 7, 8, 5, 1, 4], 'cur_cost': 2059.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 10, 2, 9, 0, 1, 3, 8, 7, 5, 6, 4], 'cur_cost': 1997.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 2, 9, 0, 1, 3, 7, 8, 5, 6, 11, 4], 'cur_cost': 1952.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 8,  1,  5,  3,  7,  0,  6,  9,  2,  4, 11, 10], dtype=int64), 'cur_cost': 2067.0, 'intermediate_solutions': [{'tour': array([ 9,  4,  7,  3,  5,  0,  6,  8, 10,  1,  2, 11]), 'cur_cost': 2123.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 3,  9,  4,  7,  5,  0,  6,  8, 10,  1,  2, 11]), 'cur_cost': 2110.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 5,  3,  9,  4,  7,  0,  6,  8, 10,  1,  2, 11]), 'cur_cost': 2006.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 7,  3,  9,  4,  5,  0,  6,  8, 10,  1,  2, 11]), 'cur_cost': 1971.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 7,  5,  3,  9,  4,  0,  6,  8, 10,  1,  2, 11]), 'cur_cost': 2108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:28:39,217 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:39,217 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:39,219 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1498.000, 多样性=0.894
2025-08-05 10:28:39,219 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 10:28:39,219 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 10:28:39,219 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:39,220 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.024888256391725854, 'best_improvement': 0.028534370946822308}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.01629327902240316}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.012733111867672689, 'recent_improvements': [-0.054797732988600766, -0.0010481726328720983, -0.029331509253255394], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 14, 'new_count': 14, 'count_change': 0, 'old_best_cost': 1344.0, 'new_best_cost': 1344.0, 'quality_improvement': 0.0, 'old_diversity': 0.7738095238095238, 'new_diversity': 0.7738095238095238, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 10:28:39,221 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 10:28:39,221 - __main__ - INFO - geometry2_12 开始进化第 3 代
2025-08-05 10:28:39,221 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 10:28:39,221 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:39,222 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1498.000, 多样性=0.894
2025-08-05 10:28:39,222 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:39,222 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.894
2025-08-05 10:28:39,223 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:39,225 - EliteExpert - INFO - 精英解分析完成: 精英解数量=14, 多样性=0.774
2025-08-05 10:28:39,226 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 10:28:39,226 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:39,227 - LandscapeExpert - INFO - 添加精英解数据: 14个精英解
2025-08-05 10:28:39,227 - LandscapeExpert - INFO - 数据提取成功: 24个路径, 24个适应度值
2025-08-05 10:28:39,249 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.583, 适应度梯度: -88.475, 聚类评分: 0.000, 覆盖率: 0.044, 收敛趋势: 0.000, 多样性: 0.434
2025-08-05 10:28:39,249 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 10:28:39,249 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:39,249 - visualization.landscape_visualizer - INFO - 设置当前实例名: geometry2_12
2025-08-05 10:28:39,256 - visualization.landscape_visualizer - INFO - 插值约束: 365 个点被约束到最小值 1344.00
2025-08-05 10:28:39,257 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=8.9%, 梯度: 45.46 → 41.43
2025-08-05 10:28:39,350 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry2_12\landscape_geometry2_12_iter_38_20250805_102839.html
2025-08-05 10:28:39,421 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry2_12\dashboard_geometry2_12_iter_38_20250805_102839.html
2025-08-05 10:28:39,421 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 38
2025-08-05 10:28:39,422 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 10:28:39,422 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1960秒
2025-08-05 10:28:39,423 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5833333333333334, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -88.47500000000001, 'local_optima_density': 0.5833333333333334, 'gradient_variance': 37752.856041666666, 'cluster_count': 0}, 'population_state': {'diversity': 0.43372584541062803, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0445, 'fitness_entropy': 0.7046684876180734, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -88.475)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.044)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360919.2496955, 'performance_metrics': {}}}
2025-08-05 10:28:39,423 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:39,423 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:39,423 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:39,423 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:39,424 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:39,424 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 10:28:39,424 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:39,425 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:39,425 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:39,425 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:39,425 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:39,425 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:39,425 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:39,425 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:39,425 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:39,426 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:39,426 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:39,426 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,426 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,426 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,427 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,427 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2116.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:39,427 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [2, 10, 0, 9, 5, 7, 1, 3, 4, 8, 6, 11], 'cur_cost': 2116.0, 'intermediate_solutions': [{'tour': [11, 9, 1, 7, 6, 0, 5, 4, 3, 8, 10, 2], 'cur_cost': 1898.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 9, 1, 8, 3, 4, 5, 0, 6, 7, 10, 11], 'cur_cost': 1839.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 9, 1, 7, 6, 0, 5, 4, 3, 8, 11, 10], 'cur_cost': 1779.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:39,427 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 2116.00)
2025-08-05 10:28:39,427 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:39,427 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:39,427 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:39,428 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:39,428 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,428 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,429 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,429 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,429 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1736.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:39,429 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [5, 11, 4, 7, 1, 0, 2, 3, 8, 9, 10, 6], 'cur_cost': 1736.0, 'intermediate_solutions': [{'tour': [0, 1, 5, 2, 4, 3, 10, 7, 6, 11, 9, 8], 'cur_cost': 1946.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 5, 10, 4, 3, 2, 6, 7, 11, 9, 8], 'cur_cost': 1794.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 10, 4, 3, 2, 5, 7, 6, 11, 9, 8], 'cur_cost': 1843.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:39,429 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 1736.00)
2025-08-05 10:28:39,430 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:39,430 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:39,430 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:39,430 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:39,430 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,430 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,431 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,431 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,431 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1405.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:39,431 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [1, 6, 7, 2, 3, 4, 5, 0, 11, 10, 9, 8], 'cur_cost': 1405.0, 'intermediate_solutions': [{'tour': [2, 10, 4, 7, 1, 3, 0, 5, 6, 8, 9, 11], 'cur_cost': 2068.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 10, 11, 9, 8, 3, 5, 0, 6, 1, 7, 4], 'cur_cost': 1766.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 2, 10, 7, 1, 6, 0, 5, 3, 8, 9, 11], 'cur_cost': 1882.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:39,432 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1405.00)
2025-08-05 10:28:39,432 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:39,432 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:39,432 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:39,433 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:39,433 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,433 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,433 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,433 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,433 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1628.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:39,433 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 6, 5, 10, 9, 4, 3, 2, 7, 1, 8, 11], 'cur_cost': 1628.0, 'intermediate_solutions': [{'tour': [3, 10, 1, 8, 5, 6, 0, 2, 4, 9, 11, 7], 'cur_cost': 2129.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 8, 1, 11, 6, 0, 2, 4, 9, 10, 7], 'cur_cost': 1932.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 11, 1, 5, 6, 0, 2, 4, 9, 10, 8, 7], 'cur_cost': 1889.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:39,434 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1628.00)
2025-08-05 10:28:39,434 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:28:39,434 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:39,434 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:39,435 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 2071.0
2025-08-05 10:28:39,448 - ExploitationExpert - INFO - res_population_num: 18
2025-08-05 10:28:39,448 - ExploitationExpert - INFO - res_population_costs: [1344.0, 1344, 1344, 1344, 1344.0, 1344, 1344.0, 1344, 1344, 1344, 1344, 1344.0, 1344, 1344, 1344.0, 1344, 1344, 1344]
2025-08-05 10:28:39,448 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0,  5, 10,  4,  3,  9,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  7,  8,  9, 10,  5,  4,  3,  2,  1], dtype=int64), array([ 0,  5, 11,  6,  7,  8,  9, 10,  4,  3,  2,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  3,  2,  8,  7,  6,  1], dtype=int64), array([ 0, 11, 10,  9,  8,  7,  6,  1,  2,  3,  4,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9, 10,  4,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  5,  4, 10, 11,  6,  7,  8,  9,  3,  2,  1], dtype=int64), array([ 0,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  2,  8,  7,  6, 11, 10,  9,  3,  4,  5], dtype=int64), array([ 0,  5,  4,  9, 10, 11,  6,  7,  8,  3,  2,  1], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9,  4, 10,  5], dtype=int64)]
2025-08-05 10:28:39,453 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:39,453 - ExploitationExpert - INFO - populations: [{'tour': [2, 10, 0, 9, 5, 7, 1, 3, 4, 8, 6, 11], 'cur_cost': 2116.0}, {'tour': [5, 11, 4, 7, 1, 0, 2, 3, 8, 9, 10, 6], 'cur_cost': 1736.0}, {'tour': [1, 6, 7, 2, 3, 4, 5, 0, 11, 10, 9, 8], 'cur_cost': 1405.0}, {'tour': [0, 6, 5, 10, 9, 4, 3, 2, 7, 1, 8, 11], 'cur_cost': 1628.0}, {'tour': array([ 2,  5, 11, 10,  7,  0,  9,  8,  1,  4,  6,  3], dtype=int64), 'cur_cost': 2071.0}, {'tour': [11, 8, 2, 6, 4, 1, 0, 10, 3, 7, 9, 5], 'cur_cost': 2100.0}, {'tour': [1, 8, 3, 11, 6, 0, 5, 4, 2, 7, 9, 10], 'cur_cost': 1781.0}, {'tour': [3, 6, 2, 5, 0, 11, 10, 4, 9, 8, 7, 1], 'cur_cost': 1621.0}, {'tour': [0, 4, 3, 9, 8, 2, 1, 5, 10, 11, 6, 7], 'cur_cost': 1498.0}, {'tour': [8, 1, 5, 3, 7, 0, 6, 9, 2, 4, 11, 10], 'cur_cost': 2067.0}]
2025-08-05 10:28:39,454 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:39,454 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 96, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 96, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:39,455 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([ 2,  5, 11, 10,  7,  0,  9,  8,  1,  4,  6,  3], dtype=int64), 'cur_cost': 2071.0, 'intermediate_solutions': [{'tour': array([ 4,  7, 11,  8,  6,  3,  2,  5, 10,  1,  9,  0]), 'cur_cost': 2381.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 8,  4,  7, 11,  6,  3,  2,  5, 10,  1,  9,  0]), 'cur_cost': 2222.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 6,  8,  4,  7, 11,  3,  2,  5, 10,  1,  9,  0]), 'cur_cost': 2234.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([11,  8,  4,  7,  6,  3,  2,  5, 10,  1,  9,  0]), 'cur_cost': 2160.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([11,  6,  8,  4,  7,  3,  2,  5, 10,  1,  9,  0]), 'cur_cost': 2071.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:39,455 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 2071.00)
2025-08-05 10:28:39,455 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:28:39,455 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:39,455 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:39,456 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 2498.0
2025-08-05 10:28:39,472 - ExploitationExpert - INFO - res_population_num: 23
2025-08-05 10:28:39,472 - ExploitationExpert - INFO - res_population_costs: [1344.0, 1344, 1344, 1344, 1344.0, 1344, 1344.0, 1344, 1344, 1344, 1344, 1344.0, 1344, 1344, 1344.0, 1344, 1344, 1344, 1344.0, 1344, 1344, 1344, 1344]
2025-08-05 10:28:39,472 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0,  5, 10,  4,  3,  9,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  7,  8,  9, 10,  5,  4,  3,  2,  1], dtype=int64), array([ 0,  5, 11,  6,  7,  8,  9, 10,  4,  3,  2,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  3,  2,  8,  7,  6,  1], dtype=int64), array([ 0, 11, 10,  9,  8,  7,  6,  1,  2,  3,  4,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9, 10,  4,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  5,  4, 10, 11,  6,  7,  8,  9,  3,  2,  1], dtype=int64), array([ 0,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  2,  8,  7,  6, 11, 10,  9,  3,  4,  5], dtype=int64), array([ 0,  5,  4,  9, 10, 11,  6,  7,  8,  3,  2,  1], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  8,  3,  2,  1,  7,  6], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64)]
2025-08-05 10:28:39,479 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:39,479 - ExploitationExpert - INFO - populations: [{'tour': [2, 10, 0, 9, 5, 7, 1, 3, 4, 8, 6, 11], 'cur_cost': 2116.0}, {'tour': [5, 11, 4, 7, 1, 0, 2, 3, 8, 9, 10, 6], 'cur_cost': 1736.0}, {'tour': [1, 6, 7, 2, 3, 4, 5, 0, 11, 10, 9, 8], 'cur_cost': 1405.0}, {'tour': [0, 6, 5, 10, 9, 4, 3, 2, 7, 1, 8, 11], 'cur_cost': 1628.0}, {'tour': array([ 2,  5, 11, 10,  7,  0,  9,  8,  1,  4,  6,  3], dtype=int64), 'cur_cost': 2071.0}, {'tour': array([ 6,  5,  8, 11,  7, 10,  3,  0,  2,  4,  1,  9], dtype=int64), 'cur_cost': 2498.0}, {'tour': [1, 8, 3, 11, 6, 0, 5, 4, 2, 7, 9, 10], 'cur_cost': 1781.0}, {'tour': [3, 6, 2, 5, 0, 11, 10, 4, 9, 8, 7, 1], 'cur_cost': 1621.0}, {'tour': [0, 4, 3, 9, 8, 2, 1, 5, 10, 11, 6, 7], 'cur_cost': 1498.0}, {'tour': [8, 1, 5, 3, 7, 0, 6, 9, 2, 4, 11, 10], 'cur_cost': 2067.0}]
2025-08-05 10:28:39,480 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:39,480 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 97, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 97, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:39,480 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([ 6,  5,  8, 11,  7, 10,  3,  0,  2,  4,  1,  9], dtype=int64), 'cur_cost': 2498.0, 'intermediate_solutions': [{'tour': array([ 2,  8, 11,  6,  4,  1,  0, 10,  3,  7,  9,  5]), 'cur_cost': 2101.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 6,  2,  8, 11,  4,  1,  0, 10,  3,  7,  9,  5]), 'cur_cost': 2116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 4,  6,  2,  8, 11,  1,  0, 10,  3,  7,  9,  5]), 'cur_cost': 2099.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([11,  6,  2,  8,  4,  1,  0, 10,  3,  7,  9,  5]), 'cur_cost': 1893.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([11,  4,  6,  2,  8,  1,  0, 10,  3,  7,  9,  5]), 'cur_cost': 1998.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:39,480 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 2498.00)
2025-08-05 10:28:39,481 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:39,481 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:39,481 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:39,481 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:39,481 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,482 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,482 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,482 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,482 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1785.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:39,482 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [6, 2, 0, 11, 4, 3, 1, 5, 9, 10, 8, 7], 'cur_cost': 1785.0, 'intermediate_solutions': [{'tour': [1, 8, 3, 0, 6, 11, 5, 4, 2, 7, 9, 10], 'cur_cost': 1738.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 3, 11, 9, 7, 2, 4, 5, 0, 6, 10], 'cur_cost': 2017.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 8, 3, 11, 6, 0, 4, 5, 2, 7, 9, 10], 'cur_cost': 1844.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:39,482 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1785.00)
2025-08-05 10:28:39,482 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:39,482 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:39,483 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:39,483 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:39,483 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,483 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,484 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,484 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,484 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1756.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:39,484 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 2, 4, 10, 3, 8, 7, 6, 11, 5, 1, 9], 'cur_cost': 1756.0, 'intermediate_solutions': [{'tour': [3, 6, 2, 5, 0, 11, 10, 1, 9, 8, 7, 4], 'cur_cost': 1893.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 11, 0, 5, 2, 6, 10, 4, 9, 8, 7, 1], 'cur_cost': 1739.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 3, 6, 2, 0, 11, 10, 4, 9, 8, 7, 1], 'cur_cost': 1650.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:39,484 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1756.00)
2025-08-05 10:28:39,484 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:39,484 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:39,485 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:39,485 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 12
2025-08-05 10:28:39,485 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,485 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,485 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,485 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,486 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1993.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:39,486 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [1, 10, 7, 2, 5, 3, 4, 11, 6, 0, 8, 9], 'cur_cost': 1993.0, 'intermediate_solutions': [{'tour': [0, 8, 3, 9, 4, 2, 1, 5, 10, 11, 6, 7], 'cur_cost': 1604.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 2, 8, 9, 3, 4, 5, 10, 11, 6, 7], 'cur_cost': 1406.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 9, 8, 2, 1, 5, 10, 4, 11, 6, 7], 'cur_cost': 1576.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:39,486 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 1993.00)
2025-08-05 10:28:39,486 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:28:39,486 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:39,486 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:39,486 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 1962.0
2025-08-05 10:28:39,496 - ExploitationExpert - INFO - res_population_num: 26
2025-08-05 10:28:39,496 - ExploitationExpert - INFO - res_population_costs: [1344.0, 1344, 1344, 1344, 1344.0, 1344, 1344.0, 1344, 1344, 1344, 1344, 1344.0, 1344, 1344, 1344.0, 1344, 1344, 1344, 1344.0, 1344, 1344, 1344, 1344, 1344, 1344, 1344]
2025-08-05 10:28:39,496 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0,  5, 10,  4,  3,  9,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  7,  8,  9, 10,  5,  4,  3,  2,  1], dtype=int64), array([ 0,  5, 11,  6,  7,  8,  9, 10,  4,  3,  2,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  3,  2,  8,  7,  6,  1], dtype=int64), array([ 0, 11, 10,  9,  8,  7,  6,  1,  2,  3,  4,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9, 10,  4,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  5,  4, 10, 11,  6,  7,  8,  9,  3,  2,  1], dtype=int64), array([ 0,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  2,  8,  7,  6, 11, 10,  9,  3,  4,  5], dtype=int64), array([ 0,  5,  4,  9, 10, 11,  6,  7,  8,  3,  2,  1], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  8,  3,  2,  1,  7,  6], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64)]
2025-08-05 10:28:39,501 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:39,501 - ExploitationExpert - INFO - populations: [{'tour': [2, 10, 0, 9, 5, 7, 1, 3, 4, 8, 6, 11], 'cur_cost': 2116.0}, {'tour': [5, 11, 4, 7, 1, 0, 2, 3, 8, 9, 10, 6], 'cur_cost': 1736.0}, {'tour': [1, 6, 7, 2, 3, 4, 5, 0, 11, 10, 9, 8], 'cur_cost': 1405.0}, {'tour': [0, 6, 5, 10, 9, 4, 3, 2, 7, 1, 8, 11], 'cur_cost': 1628.0}, {'tour': array([ 2,  5, 11, 10,  7,  0,  9,  8,  1,  4,  6,  3], dtype=int64), 'cur_cost': 2071.0}, {'tour': array([ 6,  5,  8, 11,  7, 10,  3,  0,  2,  4,  1,  9], dtype=int64), 'cur_cost': 2498.0}, {'tour': [6, 2, 0, 11, 4, 3, 1, 5, 9, 10, 8, 7], 'cur_cost': 1785.0}, {'tour': [0, 2, 4, 10, 3, 8, 7, 6, 11, 5, 1, 9], 'cur_cost': 1756.0}, {'tour': [1, 10, 7, 2, 5, 3, 4, 11, 6, 0, 8, 9], 'cur_cost': 1993.0}, {'tour': array([ 0,  8,  3,  2,  6, 10,  4,  9,  1,  5, 11,  7], dtype=int64), 'cur_cost': 1962.0}]
2025-08-05 10:28:39,501 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:39,502 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 98, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 98, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:39,502 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([ 0,  8,  3,  2,  6, 10,  4,  9,  1,  5, 11,  7], dtype=int64), 'cur_cost': 1962.0, 'intermediate_solutions': [{'tour': array([ 5,  1,  8,  3,  7,  0,  6,  9,  2,  4, 11, 10]), 'cur_cost': 1903.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 3,  5,  1,  8,  7,  0,  6,  9,  2,  4, 11, 10]), 'cur_cost': 1950.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 7,  3,  5,  1,  8,  0,  6,  9,  2,  4, 11, 10]), 'cur_cost': 2154.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 8,  3,  5,  1,  7,  0,  6,  9,  2,  4, 11, 10]), 'cur_cost': 1945.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 8,  7,  3,  5,  1,  0,  6,  9,  2,  4, 11, 10]), 'cur_cost': 1944.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:39,502 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1962.00)
2025-08-05 10:28:39,502 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:39,503 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:39,504 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [2, 10, 0, 9, 5, 7, 1, 3, 4, 8, 6, 11], 'cur_cost': 2116.0, 'intermediate_solutions': [{'tour': [11, 9, 1, 7, 6, 0, 5, 4, 3, 8, 10, 2], 'cur_cost': 1898.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 9, 1, 8, 3, 4, 5, 0, 6, 7, 10, 11], 'cur_cost': 1839.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 9, 1, 7, 6, 0, 5, 4, 3, 8, 11, 10], 'cur_cost': 1779.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [5, 11, 4, 7, 1, 0, 2, 3, 8, 9, 10, 6], 'cur_cost': 1736.0, 'intermediate_solutions': [{'tour': [0, 1, 5, 2, 4, 3, 10, 7, 6, 11, 9, 8], 'cur_cost': 1946.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 5, 10, 4, 3, 2, 6, 7, 11, 9, 8], 'cur_cost': 1794.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 10, 4, 3, 2, 5, 7, 6, 11, 9, 8], 'cur_cost': 1843.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [1, 6, 7, 2, 3, 4, 5, 0, 11, 10, 9, 8], 'cur_cost': 1405.0, 'intermediate_solutions': [{'tour': [2, 10, 4, 7, 1, 3, 0, 5, 6, 8, 9, 11], 'cur_cost': 2068.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 10, 11, 9, 8, 3, 5, 0, 6, 1, 7, 4], 'cur_cost': 1766.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 2, 10, 7, 1, 6, 0, 5, 3, 8, 9, 11], 'cur_cost': 1882.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 5, 10, 9, 4, 3, 2, 7, 1, 8, 11], 'cur_cost': 1628.0, 'intermediate_solutions': [{'tour': [3, 10, 1, 8, 5, 6, 0, 2, 4, 9, 11, 7], 'cur_cost': 2129.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 8, 1, 11, 6, 0, 2, 4, 9, 10, 7], 'cur_cost': 1932.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 11, 1, 5, 6, 0, 2, 4, 9, 10, 8, 7], 'cur_cost': 1889.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 2,  5, 11, 10,  7,  0,  9,  8,  1,  4,  6,  3], dtype=int64), 'cur_cost': 2071.0, 'intermediate_solutions': [{'tour': array([ 4,  7, 11,  8,  6,  3,  2,  5, 10,  1,  9,  0]), 'cur_cost': 2381.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 8,  4,  7, 11,  6,  3,  2,  5, 10,  1,  9,  0]), 'cur_cost': 2222.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 6,  8,  4,  7, 11,  3,  2,  5, 10,  1,  9,  0]), 'cur_cost': 2234.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([11,  8,  4,  7,  6,  3,  2,  5, 10,  1,  9,  0]), 'cur_cost': 2160.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([11,  6,  8,  4,  7,  3,  2,  5, 10,  1,  9,  0]), 'cur_cost': 2071.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 6,  5,  8, 11,  7, 10,  3,  0,  2,  4,  1,  9], dtype=int64), 'cur_cost': 2498.0, 'intermediate_solutions': [{'tour': array([ 2,  8, 11,  6,  4,  1,  0, 10,  3,  7,  9,  5]), 'cur_cost': 2101.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 6,  2,  8, 11,  4,  1,  0, 10,  3,  7,  9,  5]), 'cur_cost': 2116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 4,  6,  2,  8, 11,  1,  0, 10,  3,  7,  9,  5]), 'cur_cost': 2099.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([11,  6,  2,  8,  4,  1,  0, 10,  3,  7,  9,  5]), 'cur_cost': 1893.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([11,  4,  6,  2,  8,  1,  0, 10,  3,  7,  9,  5]), 'cur_cost': 1998.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [6, 2, 0, 11, 4, 3, 1, 5, 9, 10, 8, 7], 'cur_cost': 1785.0, 'intermediate_solutions': [{'tour': [1, 8, 3, 0, 6, 11, 5, 4, 2, 7, 9, 10], 'cur_cost': 1738.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 3, 11, 9, 7, 2, 4, 5, 0, 6, 10], 'cur_cost': 2017.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 8, 3, 11, 6, 0, 4, 5, 2, 7, 9, 10], 'cur_cost': 1844.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 4, 10, 3, 8, 7, 6, 11, 5, 1, 9], 'cur_cost': 1756.0, 'intermediate_solutions': [{'tour': [3, 6, 2, 5, 0, 11, 10, 1, 9, 8, 7, 4], 'cur_cost': 1893.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 11, 0, 5, 2, 6, 10, 4, 9, 8, 7, 1], 'cur_cost': 1739.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 3, 6, 2, 0, 11, 10, 4, 9, 8, 7, 1], 'cur_cost': 1650.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [1, 10, 7, 2, 5, 3, 4, 11, 6, 0, 8, 9], 'cur_cost': 1993.0, 'intermediate_solutions': [{'tour': [0, 8, 3, 9, 4, 2, 1, 5, 10, 11, 6, 7], 'cur_cost': 1604.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 2, 8, 9, 3, 4, 5, 10, 11, 6, 7], 'cur_cost': 1406.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 9, 8, 2, 1, 5, 10, 4, 11, 6, 7], 'cur_cost': 1576.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 0,  8,  3,  2,  6, 10,  4,  9,  1,  5, 11,  7], dtype=int64), 'cur_cost': 1962.0, 'intermediate_solutions': [{'tour': array([ 5,  1,  8,  3,  7,  0,  6,  9,  2,  4, 11, 10]), 'cur_cost': 1903.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 3,  5,  1,  8,  7,  0,  6,  9,  2,  4, 11, 10]), 'cur_cost': 1950.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 7,  3,  5,  1,  8,  0,  6,  9,  2,  4, 11, 10]), 'cur_cost': 2154.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 8,  3,  5,  1,  7,  0,  6,  9,  2,  4, 11, 10]), 'cur_cost': 1945.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 8,  7,  3,  5,  1,  0,  6,  9,  2,  4, 11, 10]), 'cur_cost': 1944.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:28:39,504 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:39,504 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:39,505 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1405.000, 多样性=0.913
2025-08-05 10:28:39,506 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 10:28:39,506 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 10:28:39,506 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:39,508 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.026270680765820825, 'best_improvement': 0.062082777036048066}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.020703933747411852}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.01192004187942688, 'recent_improvements': [-0.0010481726328720983, -0.029331509253255394, -0.024888256391725854], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 26, 'new_count': 26, 'count_change': 0, 'old_best_cost': 1344.0, 'new_best_cost': 1344.0, 'quality_improvement': 0.0, 'old_diversity': 0.752051282051282, 'new_diversity': 0.752051282051282, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:39,511 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 10:28:39,511 - __main__ - INFO - geometry2_12 开始进化第 4 代
2025-08-05 10:28:39,511 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 10:28:39,511 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:39,512 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1405.000, 多样性=0.913
2025-08-05 10:28:39,512 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:39,512 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.913
2025-08-05 10:28:39,513 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:39,518 - EliteExpert - INFO - 精英解分析完成: 精英解数量=26, 多样性=0.752
2025-08-05 10:28:39,519 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 10:28:39,519 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:39,519 - LandscapeExpert - INFO - 添加精英解数据: 26个精英解
2025-08-05 10:28:39,519 - LandscapeExpert - INFO - 数据提取成功: 36个路径, 36个适应度值
2025-08-05 10:28:39,568 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.722, 适应度梯度: -98.578, 聚类评分: 0.000, 覆盖率: 0.047, 收敛趋势: 0.000, 多样性: 0.275
2025-08-05 10:28:39,569 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 10:28:39,569 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:39,569 - visualization.landscape_visualizer - INFO - 设置当前实例名: geometry2_12
2025-08-05 10:28:39,575 - visualization.landscape_visualizer - INFO - 插值约束: 484 个点被约束到最小值 1344.00
2025-08-05 10:28:39,576 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=6.9%, 梯度: 60.74 → 56.55
2025-08-05 10:28:39,681 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry2_12\landscape_geometry2_12_iter_39_20250805_102839.html
2025-08-05 10:28:39,747 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry2_12\dashboard_geometry2_12_iter_39_20250805_102839.html
2025-08-05 10:28:39,747 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 39
2025-08-05 10:28:39,747 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 10:28:39,748 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2284秒
2025-08-05 10:28:39,748 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.7222222222222222, 'modality': 'multi-modal', 'deceptiveness': 'high', 'gradient_strength': -98.57777777777778, 'local_optima_density': 0.7222222222222222, 'gradient_variance': 60126.56839506175, 'cluster_count': 0}, 'population_state': {'diversity': 0.2746031746031746, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0467, 'fitness_entropy': 0.5263395716323146, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'high_local_optima', 'severity': 'high', 'description': '高局部最优密度区域 (密度: 0.722)', 'recommendation': '增加扰动强度'}, {'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -98.578)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.047)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing', 'diversification'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100, 'diversification_strength': 0.6}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360919.5699115, 'performance_metrics': {}}}
2025-08-05 10:28:39,748 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:39,748 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:39,748 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:39,748 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:39,749 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:28:39,749 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 10:28:39,749 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:28:39,749 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:39,749 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:39,749 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:28:39,749 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:39,750 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:39,750 - experts.management.collaboration_manager - INFO - 识别精英个体: {2, 3} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:39,750 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-05 10:28:39,750 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:39,750 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:39,750 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 2187.0
2025-08-05 10:28:39,766 - ExploitationExpert - INFO - res_population_num: 27
2025-08-05 10:28:39,766 - ExploitationExpert - INFO - res_population_costs: [1344.0, 1344, 1344, 1344, 1344.0, 1344, 1344.0, 1344, 1344, 1344, 1344, 1344.0, 1344, 1344, 1344.0, 1344, 1344, 1344, 1344.0, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344]
2025-08-05 10:28:39,766 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0,  5, 10,  4,  3,  9,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  7,  8,  9, 10,  5,  4,  3,  2,  1], dtype=int64), array([ 0,  5, 11,  6,  7,  8,  9, 10,  4,  3,  2,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  3,  2,  8,  7,  6,  1], dtype=int64), array([ 0, 11, 10,  9,  8,  7,  6,  1,  2,  3,  4,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9, 10,  4,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  5,  4, 10, 11,  6,  7,  8,  9,  3,  2,  1], dtype=int64), array([ 0,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  2,  8,  7,  6, 11, 10,  9,  3,  4,  5], dtype=int64), array([ 0,  5,  4,  9, 10, 11,  6,  7,  8,  3,  2,  1], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  8,  3,  2,  1,  7,  6], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  7,  8,  9, 10, 11,  6], dtype=int64)]
2025-08-05 10:28:39,774 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:39,774 - ExploitationExpert - INFO - populations: [{'tour': array([ 7,  5,  8,  1,  4,  2,  0,  6, 10,  3,  9, 11], dtype=int64), 'cur_cost': 2187.0}, {'tour': [5, 11, 4, 7, 1, 0, 2, 3, 8, 9, 10, 6], 'cur_cost': 1736.0}, {'tour': [1, 6, 7, 2, 3, 4, 5, 0, 11, 10, 9, 8], 'cur_cost': 1405.0}, {'tour': [0, 6, 5, 10, 9, 4, 3, 2, 7, 1, 8, 11], 'cur_cost': 1628.0}, {'tour': [2, 5, 11, 10, 7, 0, 9, 8, 1, 4, 6, 3], 'cur_cost': 2071.0}, {'tour': [6, 5, 8, 11, 7, 10, 3, 0, 2, 4, 1, 9], 'cur_cost': 2498.0}, {'tour': [6, 2, 0, 11, 4, 3, 1, 5, 9, 10, 8, 7], 'cur_cost': 1785.0}, {'tour': [0, 2, 4, 10, 3, 8, 7, 6, 11, 5, 1, 9], 'cur_cost': 1756.0}, {'tour': [1, 10, 7, 2, 5, 3, 4, 11, 6, 0, 8, 9], 'cur_cost': 1993.0}, {'tour': [0, 8, 3, 2, 6, 10, 4, 9, 1, 5, 11, 7], 'cur_cost': 1962.0}]
2025-08-05 10:28:39,774 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:39,775 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 99, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 99, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:39,775 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([ 7,  5,  8,  1,  4,  2,  0,  6, 10,  3,  9, 11], dtype=int64), 'cur_cost': 2187.0, 'intermediate_solutions': [{'tour': array([ 0, 10,  2,  9,  5,  7,  1,  3,  4,  8,  6, 11]), 'cur_cost': 1965.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9,  0, 10,  2,  5,  7,  1,  3,  4,  8,  6, 11]), 'cur_cost': 2129.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 5,  9,  0, 10,  2,  7,  1,  3,  4,  8,  6, 11]), 'cur_cost': 1904.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2,  9,  0, 10,  5,  7,  1,  3,  4,  8,  6, 11]), 'cur_cost': 2009.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2,  5,  9,  0, 10,  7,  1,  3,  4,  8,  6, 11]), 'cur_cost': 2128.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:39,775 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 2187.00)
2025-08-05 10:28:39,775 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:39,775 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:39,775 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:39,776 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:39,776 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,776 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,776 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,777 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,777 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1687.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:39,777 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [4, 5, 0, 1, 3, 9, 2, 8, 6, 7, 11, 10], 'cur_cost': 1687.0, 'intermediate_solutions': [{'tour': [5, 11, 4, 7, 9, 0, 2, 3, 8, 1, 10, 6], 'cur_cost': 2127.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 11, 5, 7, 1, 0, 2, 3, 8, 9, 10, 6], 'cur_cost': 1781.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 4, 7, 1, 0, 2, 11, 3, 8, 9, 10, 6], 'cur_cost': 1887.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:39,777 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1687.00)
2025-08-05 10:28:39,777 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:39,778 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:39,778 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:39,778 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 12
2025-08-05 10:28:39,778 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,778 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,779 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,779 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,779 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2063.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:39,779 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [10, 5, 9, 11, 6, 8, 4, 2, 0, 1, 3, 7], 'cur_cost': 2063.0, 'intermediate_solutions': [{'tour': [1, 6, 7, 2, 3, 11, 5, 0, 4, 10, 9, 8], 'cur_cost': 1557.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 7, 2, 3, 4, 0, 5, 11, 10, 9, 8], 'cur_cost': 1451.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 10, 7, 2, 3, 4, 5, 0, 11, 9, 8], 'cur_cost': 1802.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:39,780 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 2063.00)
2025-08-05 10:28:39,780 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:39,780 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:39,780 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:39,781 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 12
2025-08-05 10:28:39,781 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,781 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,781 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,782 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,782 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2112.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:39,782 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [4, 6, 5, 9, 11, 8, 7, 3, 1, 0, 2, 10], 'cur_cost': 2112.0, 'intermediate_solutions': [{'tour': [3, 6, 5, 10, 9, 4, 0, 2, 7, 1, 8, 11], 'cur_cost': 1932.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 8, 1, 7, 2, 3, 4, 9, 10, 5, 6, 0], 'cur_cost': 1628.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 5, 1, 10, 9, 4, 3, 2, 7, 8, 11], 'cur_cost': 1719.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:39,782 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 2112.00)
2025-08-05 10:28:39,782 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:28:39,782 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:39,782 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:39,783 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 1927.0
2025-08-05 10:28:39,796 - ExploitationExpert - INFO - res_population_num: 30
2025-08-05 10:28:39,796 - ExploitationExpert - INFO - res_population_costs: [1344.0, 1344, 1344, 1344, 1344.0, 1344, 1344.0, 1344, 1344, 1344, 1344, 1344.0, 1344, 1344, 1344.0, 1344, 1344, 1344, 1344.0, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344.0, 1344, 1344]
2025-08-05 10:28:39,797 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0,  5, 10,  4,  3,  9,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  7,  8,  9, 10,  5,  4,  3,  2,  1], dtype=int64), array([ 0,  5, 11,  6,  7,  8,  9, 10,  4,  3,  2,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  3,  2,  8,  7,  6,  1], dtype=int64), array([ 0, 11, 10,  9,  8,  7,  6,  1,  2,  3,  4,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9, 10,  4,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  5,  4, 10, 11,  6,  7,  8,  9,  3,  2,  1], dtype=int64), array([ 0,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  2,  8,  7,  6, 11, 10,  9,  3,  4,  5], dtype=int64), array([ 0,  5,  4,  9, 10, 11,  6,  7,  8,  3,  2,  1], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  8,  3,  2,  1,  7,  6], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  7,  8,  9, 10, 11,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  2,  8,  7,  1,  6, 11], dtype=int64)]
2025-08-05 10:28:39,806 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:39,806 - ExploitationExpert - INFO - populations: [{'tour': array([ 7,  5,  8,  1,  4,  2,  0,  6, 10,  3,  9, 11], dtype=int64), 'cur_cost': 2187.0}, {'tour': [4, 5, 0, 1, 3, 9, 2, 8, 6, 7, 11, 10], 'cur_cost': 1687.0}, {'tour': [10, 5, 9, 11, 6, 8, 4, 2, 0, 1, 3, 7], 'cur_cost': 2063.0}, {'tour': [4, 6, 5, 9, 11, 8, 7, 3, 1, 0, 2, 10], 'cur_cost': 2112.0}, {'tour': array([10,  4, 11,  9,  2,  1,  7,  3,  5,  0,  6,  8], dtype=int64), 'cur_cost': 1927.0}, {'tour': [6, 5, 8, 11, 7, 10, 3, 0, 2, 4, 1, 9], 'cur_cost': 2498.0}, {'tour': [6, 2, 0, 11, 4, 3, 1, 5, 9, 10, 8, 7], 'cur_cost': 1785.0}, {'tour': [0, 2, 4, 10, 3, 8, 7, 6, 11, 5, 1, 9], 'cur_cost': 1756.0}, {'tour': [1, 10, 7, 2, 5, 3, 4, 11, 6, 0, 8, 9], 'cur_cost': 1993.0}, {'tour': [0, 8, 3, 2, 6, 10, 4, 9, 1, 5, 11, 7], 'cur_cost': 1962.0}]
2025-08-05 10:28:39,807 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:39,807 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 100, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 100, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:39,808 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([10,  4, 11,  9,  2,  1,  7,  3,  5,  0,  6,  8], dtype=int64), 'cur_cost': 1927.0, 'intermediate_solutions': [{'tour': array([11,  5,  2, 10,  7,  0,  9,  8,  1,  4,  6,  3]), 'cur_cost': 2283.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([10, 11,  5,  2,  7,  0,  9,  8,  1,  4,  6,  3]), 'cur_cost': 1972.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 7, 10, 11,  5,  2,  0,  9,  8,  1,  4,  6,  3]), 'cur_cost': 2116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2, 10, 11,  5,  7,  0,  9,  8,  1,  4,  6,  3]), 'cur_cost': 2059.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2,  7, 10, 11,  5,  0,  9,  8,  1,  4,  6,  3]), 'cur_cost': 1946.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:39,808 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1927.00)
2025-08-05 10:28:39,808 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:28:39,809 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:39,809 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:39,809 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 2221.0
2025-08-05 10:28:39,822 - ExploitationExpert - INFO - res_population_num: 33
2025-08-05 10:28:39,822 - ExploitationExpert - INFO - res_population_costs: [1344.0, 1344, 1344, 1344, 1344.0, 1344, 1344.0, 1344, 1344, 1344, 1344, 1344.0, 1344, 1344, 1344.0, 1344, 1344, 1344, 1344.0, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344.0, 1344, 1344, 1344.0, 1344, 1344]
2025-08-05 10:28:39,823 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0,  5, 10,  4,  3,  9,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  7,  8,  9, 10,  5,  4,  3,  2,  1], dtype=int64), array([ 0,  5, 11,  6,  7,  8,  9, 10,  4,  3,  2,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  3,  2,  8,  7,  6,  1], dtype=int64), array([ 0, 11, 10,  9,  8,  7,  6,  1,  2,  3,  4,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9, 10,  4,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  5,  4, 10, 11,  6,  7,  8,  9,  3,  2,  1], dtype=int64), array([ 0,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  2,  8,  7,  6, 11, 10,  9,  3,  4,  5], dtype=int64), array([ 0,  5,  4,  9, 10, 11,  6,  7,  8,  3,  2,  1], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  8,  3,  2,  1,  7,  6], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  7,  8,  9, 10, 11,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  2,  8,  7,  1,  6, 11], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  3,  9,  8,  7,  2,  1], dtype=int64), array([ 0,  5,  4,  3,  2,  8,  9, 10, 11,  6,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64)]
2025-08-05 10:28:39,832 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:39,832 - ExploitationExpert - INFO - populations: [{'tour': array([ 7,  5,  8,  1,  4,  2,  0,  6, 10,  3,  9, 11], dtype=int64), 'cur_cost': 2187.0}, {'tour': [4, 5, 0, 1, 3, 9, 2, 8, 6, 7, 11, 10], 'cur_cost': 1687.0}, {'tour': [10, 5, 9, 11, 6, 8, 4, 2, 0, 1, 3, 7], 'cur_cost': 2063.0}, {'tour': [4, 6, 5, 9, 11, 8, 7, 3, 1, 0, 2, 10], 'cur_cost': 2112.0}, {'tour': array([10,  4, 11,  9,  2,  1,  7,  3,  5,  0,  6,  8], dtype=int64), 'cur_cost': 1927.0}, {'tour': array([ 0,  4,  6, 11,  2,  9,  1,  8,  5, 10,  7,  3], dtype=int64), 'cur_cost': 2221.0}, {'tour': [6, 2, 0, 11, 4, 3, 1, 5, 9, 10, 8, 7], 'cur_cost': 1785.0}, {'tour': [0, 2, 4, 10, 3, 8, 7, 6, 11, 5, 1, 9], 'cur_cost': 1756.0}, {'tour': [1, 10, 7, 2, 5, 3, 4, 11, 6, 0, 8, 9], 'cur_cost': 1993.0}, {'tour': [0, 8, 3, 2, 6, 10, 4, 9, 1, 5, 11, 7], 'cur_cost': 1962.0}]
2025-08-05 10:28:39,833 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:39,833 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 101, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 101, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:39,834 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([ 0,  4,  6, 11,  2,  9,  1,  8,  5, 10,  7,  3], dtype=int64), 'cur_cost': 2221.0, 'intermediate_solutions': [{'tour': array([ 8,  5,  6, 11,  7, 10,  3,  0,  2,  4,  1,  9]), 'cur_cost': 2175.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([11,  8,  5,  6,  7, 10,  3,  0,  2,  4,  1,  9]), 'cur_cost': 2337.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 7, 11,  8,  5,  6, 10,  3,  0,  2,  4,  1,  9]), 'cur_cost': 2412.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6, 11,  8,  5,  7, 10,  3,  0,  2,  4,  1,  9]), 'cur_cost': 2425.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6,  7, 11,  8,  5, 10,  3,  0,  2,  4,  1,  9]), 'cur_cost': 2276.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:39,834 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 2221.00)
2025-08-05 10:28:39,834 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:39,834 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:39,835 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:39,835 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:39,836 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,836 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,836 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,836 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,836 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1451.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:39,836 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [1, 0, 5, 3, 2, 8, 4, 9, 10, 11, 6, 7], 'cur_cost': 1451.0, 'intermediate_solutions': [{'tour': [6, 2, 0, 11, 9, 3, 1, 5, 4, 10, 8, 7], 'cur_cost': 1780.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 2, 0, 11, 4, 7, 8, 10, 9, 5, 1, 3], 'cur_cost': 1997.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 2, 0, 11, 4, 3, 1, 5, 9, 8, 7, 10], 'cur_cost': 1946.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:39,836 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1451.00)
2025-08-05 10:28:39,837 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:39,837 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:39,837 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:39,837 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:39,837 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,837 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,838 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,838 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,838 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1344.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:39,838 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [4, 5, 0, 1, 2, 3, 8, 7, 6, 11, 10, 9], 'cur_cost': 1344.0, 'intermediate_solutions': [{'tour': [6, 2, 4, 10, 3, 8, 7, 0, 11, 5, 1, 9], 'cur_cost': 1889.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 4, 10, 3, 6, 7, 8, 11, 5, 1, 9], 'cur_cost': 2024.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 4, 10, 3, 7, 6, 11, 5, 1, 9, 8], 'cur_cost': 1817.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:39,838 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1344.00)
2025-08-05 10:28:39,838 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:39,839 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:39,839 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:39,839 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 12
2025-08-05 10:28:39,839 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,839 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,840 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,840 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,840 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2176.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:39,840 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [6, 9, 3, 11, 1, 8, 5, 10, 0, 7, 4, 2], 'cur_cost': 2176.0, 'intermediate_solutions': [{'tour': [1, 10, 7, 2, 5, 3, 4, 6, 11, 0, 8, 9], 'cur_cost': 2038.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 10, 7, 2, 4, 3, 5, 11, 6, 0, 8, 9], 'cur_cost': 1915.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 10, 7, 2, 5, 3, 4, 11, 6, 8, 9, 0], 'cur_cost': 2005.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:39,840 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 2176.00)
2025-08-05 10:28:39,840 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:39,840 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:39,841 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:39,841 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:39,841 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,842 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,842 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,842 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:39,843 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1660.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:39,843 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [11, 0, 4, 10, 9, 5, 3, 2, 1, 6, 7, 8], 'cur_cost': 1660.0, 'intermediate_solutions': [{'tour': [0, 8, 3, 2, 6, 7, 4, 9, 1, 5, 11, 10], 'cur_cost': 1831.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 3, 2, 6, 10, 4, 9, 1, 5, 7, 11], 'cur_cost': 2006.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 3, 6, 10, 4, 9, 1, 5, 11, 2, 7], 'cur_cost': 1994.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:39,843 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1660.00)
2025-08-05 10:28:39,843 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:39,844 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:39,845 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 7,  5,  8,  1,  4,  2,  0,  6, 10,  3,  9, 11], dtype=int64), 'cur_cost': 2187.0, 'intermediate_solutions': [{'tour': array([ 0, 10,  2,  9,  5,  7,  1,  3,  4,  8,  6, 11]), 'cur_cost': 1965.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9,  0, 10,  2,  5,  7,  1,  3,  4,  8,  6, 11]), 'cur_cost': 2129.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 5,  9,  0, 10,  2,  7,  1,  3,  4,  8,  6, 11]), 'cur_cost': 1904.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2,  9,  0, 10,  5,  7,  1,  3,  4,  8,  6, 11]), 'cur_cost': 2009.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2,  5,  9,  0, 10,  7,  1,  3,  4,  8,  6, 11]), 'cur_cost': 2128.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [4, 5, 0, 1, 3, 9, 2, 8, 6, 7, 11, 10], 'cur_cost': 1687.0, 'intermediate_solutions': [{'tour': [5, 11, 4, 7, 9, 0, 2, 3, 8, 1, 10, 6], 'cur_cost': 2127.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 11, 5, 7, 1, 0, 2, 3, 8, 9, 10, 6], 'cur_cost': 1781.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 4, 7, 1, 0, 2, 11, 3, 8, 9, 10, 6], 'cur_cost': 1887.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [10, 5, 9, 11, 6, 8, 4, 2, 0, 1, 3, 7], 'cur_cost': 2063.0, 'intermediate_solutions': [{'tour': [1, 6, 7, 2, 3, 11, 5, 0, 4, 10, 9, 8], 'cur_cost': 1557.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 7, 2, 3, 4, 0, 5, 11, 10, 9, 8], 'cur_cost': 1451.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 10, 7, 2, 3, 4, 5, 0, 11, 9, 8], 'cur_cost': 1802.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [4, 6, 5, 9, 11, 8, 7, 3, 1, 0, 2, 10], 'cur_cost': 2112.0, 'intermediate_solutions': [{'tour': [3, 6, 5, 10, 9, 4, 0, 2, 7, 1, 8, 11], 'cur_cost': 1932.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 8, 1, 7, 2, 3, 4, 9, 10, 5, 6, 0], 'cur_cost': 1628.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 5, 1, 10, 9, 4, 3, 2, 7, 8, 11], 'cur_cost': 1719.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([10,  4, 11,  9,  2,  1,  7,  3,  5,  0,  6,  8], dtype=int64), 'cur_cost': 1927.0, 'intermediate_solutions': [{'tour': array([11,  5,  2, 10,  7,  0,  9,  8,  1,  4,  6,  3]), 'cur_cost': 2283.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([10, 11,  5,  2,  7,  0,  9,  8,  1,  4,  6,  3]), 'cur_cost': 1972.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 7, 10, 11,  5,  2,  0,  9,  8,  1,  4,  6,  3]), 'cur_cost': 2116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2, 10, 11,  5,  7,  0,  9,  8,  1,  4,  6,  3]), 'cur_cost': 2059.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2,  7, 10, 11,  5,  0,  9,  8,  1,  4,  6,  3]), 'cur_cost': 1946.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 0,  4,  6, 11,  2,  9,  1,  8,  5, 10,  7,  3], dtype=int64), 'cur_cost': 2221.0, 'intermediate_solutions': [{'tour': array([ 8,  5,  6, 11,  7, 10,  3,  0,  2,  4,  1,  9]), 'cur_cost': 2175.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([11,  8,  5,  6,  7, 10,  3,  0,  2,  4,  1,  9]), 'cur_cost': 2337.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 7, 11,  8,  5,  6, 10,  3,  0,  2,  4,  1,  9]), 'cur_cost': 2412.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6, 11,  8,  5,  7, 10,  3,  0,  2,  4,  1,  9]), 'cur_cost': 2425.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6,  7, 11,  8,  5, 10,  3,  0,  2,  4,  1,  9]), 'cur_cost': 2276.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [1, 0, 5, 3, 2, 8, 4, 9, 10, 11, 6, 7], 'cur_cost': 1451.0, 'intermediate_solutions': [{'tour': [6, 2, 0, 11, 9, 3, 1, 5, 4, 10, 8, 7], 'cur_cost': 1780.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 2, 0, 11, 4, 7, 8, 10, 9, 5, 1, 3], 'cur_cost': 1997.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 2, 0, 11, 4, 3, 1, 5, 9, 8, 7, 10], 'cur_cost': 1946.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [4, 5, 0, 1, 2, 3, 8, 7, 6, 11, 10, 9], 'cur_cost': 1344.0, 'intermediate_solutions': [{'tour': [6, 2, 4, 10, 3, 8, 7, 0, 11, 5, 1, 9], 'cur_cost': 1889.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 4, 10, 3, 6, 7, 8, 11, 5, 1, 9], 'cur_cost': 2024.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 4, 10, 3, 7, 6, 11, 5, 1, 9, 8], 'cur_cost': 1817.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [6, 9, 3, 11, 1, 8, 5, 10, 0, 7, 4, 2], 'cur_cost': 2176.0, 'intermediate_solutions': [{'tour': [1, 10, 7, 2, 5, 3, 4, 6, 11, 0, 8, 9], 'cur_cost': 2038.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 10, 7, 2, 4, 3, 5, 11, 6, 0, 8, 9], 'cur_cost': 1915.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 10, 7, 2, 5, 3, 4, 11, 6, 8, 9, 0], 'cur_cost': 2005.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [11, 0, 4, 10, 9, 5, 3, 2, 1, 6, 7, 8], 'cur_cost': 1660.0, 'intermediate_solutions': [{'tour': [0, 8, 3, 2, 6, 7, 4, 9, 1, 5, 11, 10], 'cur_cost': 1831.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 3, 2, 6, 10, 4, 9, 1, 5, 7, 11], 'cur_cost': 2006.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 3, 6, 10, 4, 9, 1, 5, 11, 2, 7], 'cur_cost': 1994.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:39,846 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:39,846 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:39,847 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1344.000, 多样性=0.904
2025-08-05 10:28:39,847 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 10:28:39,847 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 10:28:39,847 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:39,851 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.05358102526616392, 'best_improvement': 0.04341637010676157}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.01014198782961453}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.0015304142437172902, 'recent_improvements': [-0.029331509253255394, -0.024888256391725854, -0.026270680765820825], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 33, 'new_count': 33, 'count_change': 0, 'old_best_cost': 1344.0, 'new_best_cost': 1344.0, 'quality_improvement': 0.0, 'old_diversity': 0.75552398989899, 'new_diversity': 0.75552398989899, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:39,855 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 10:28:39,856 - __main__ - INFO - geometry2_12 开始进化第 5 代
2025-08-05 10:28:39,856 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 10:28:39,856 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:39,857 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1344.000, 多样性=0.904
2025-08-05 10:28:39,858 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:39,859 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.904
2025-08-05 10:28:39,859 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:39,875 - EliteExpert - INFO - 精英解分析完成: 精英解数量=33, 多样性=0.756
2025-08-05 10:28:39,877 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 10:28:39,877 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:39,877 - LandscapeExpert - INFO - 添加精英解数据: 33个精英解
2025-08-05 10:28:39,877 - LandscapeExpert - INFO - 数据提取成功: 43个路径, 43个适应度值
2025-08-05 10:28:39,952 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.791, 适应度梯度: -96.740, 聚类评分: 0.000, 覆盖率: 0.048, 收敛趋势: 0.000, 多样性: 0.228
2025-08-05 10:28:39,953 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 10:28:39,953 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:39,953 - visualization.landscape_visualizer - INFO - 设置当前实例名: geometry2_12
2025-08-05 10:28:39,965 - visualization.landscape_visualizer - INFO - 插值约束: 260 个点被约束到最小值 1344.00
2025-08-05 10:28:39,966 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.1%, 梯度: 41.90 → 38.92
2025-08-05 10:28:40,094 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry2_12\landscape_geometry2_12_iter_40_20250805_102840.html
2025-08-05 10:28:40,154 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry2_12\dashboard_geometry2_12_iter_40_20250805_102840.html
2025-08-05 10:28:40,155 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 40
2025-08-05 10:28:40,155 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 10:28:40,155 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2791秒
2025-08-05 10:28:40,155 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.7906976744186046, 'modality': 'multi-modal', 'deceptiveness': 'high', 'gradient_strength': -96.73953488372094, 'local_optima_density': 0.7906976744186046, 'gradient_variance': 60007.35169280691, 'cluster_count': 0}, 'population_state': {'diversity': 0.22761338175075332, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0482, 'fitness_entropy': 0.46423020920635066, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'high_local_optima', 'severity': 'high', 'description': '高局部最优密度区域 (密度: 0.791)', 'recommendation': '增加扰动强度'}, {'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -96.740)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.048)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization', 'diversification'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50, 'diversification_strength': 0.6}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360919.9530926, 'performance_metrics': {}}}
2025-08-05 10:28:40,156 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:40,156 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:40,156 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:40,156 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:40,157 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 10:28:40,157 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 10:28:40,157 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 10:28:40,157 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:40,157 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:40,157 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 10:28:40,158 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:40,158 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:40,158 - experts.management.collaboration_manager - INFO - 识别精英个体: {6, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:40,158 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-05 10:28:40,158 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:40,158 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:40,159 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 2092.0
2025-08-05 10:28:40,169 - ExploitationExpert - INFO - res_population_num: 37
2025-08-05 10:28:40,169 - ExploitationExpert - INFO - res_population_costs: [1344.0, 1344, 1344, 1344, 1344.0, 1344, 1344.0, 1344, 1344, 1344, 1344, 1344.0, 1344, 1344, 1344.0, 1344, 1344, 1344, 1344.0, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344.0, 1344, 1344, 1344.0, 1344, 1344, 1344.0, 1344, 1344, 1344]
2025-08-05 10:28:40,169 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0,  5, 10,  4,  3,  9,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  7,  8,  9, 10,  5,  4,  3,  2,  1], dtype=int64), array([ 0,  5, 11,  6,  7,  8,  9, 10,  4,  3,  2,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  3,  2,  8,  7,  6,  1], dtype=int64), array([ 0, 11, 10,  9,  8,  7,  6,  1,  2,  3,  4,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9, 10,  4,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  5,  4, 10, 11,  6,  7,  8,  9,  3,  2,  1], dtype=int64), array([ 0,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  2,  8,  7,  6, 11, 10,  9,  3,  4,  5], dtype=int64), array([ 0,  5,  4,  9, 10, 11,  6,  7,  8,  3,  2,  1], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  8,  3,  2,  1,  7,  6], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  7,  8,  9, 10, 11,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  2,  8,  7,  1,  6, 11], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  3,  9,  8,  7,  2,  1], dtype=int64), array([ 0,  5,  4,  3,  2,  8,  9, 10, 11,  6,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0,  1,  2,  7,  8,  9,  3,  4, 10,  5, 11,  6], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11,  6], dtype=int64)]
2025-08-05 10:28:40,177 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:40,177 - ExploitationExpert - INFO - populations: [{'tour': array([ 9,  8, 10,  2,  6, 11,  4,  1,  3,  0,  7,  5], dtype=int64), 'cur_cost': 2092.0}, {'tour': [4, 5, 0, 1, 3, 9, 2, 8, 6, 7, 11, 10], 'cur_cost': 1687.0}, {'tour': [10, 5, 9, 11, 6, 8, 4, 2, 0, 1, 3, 7], 'cur_cost': 2063.0}, {'tour': [4, 6, 5, 9, 11, 8, 7, 3, 1, 0, 2, 10], 'cur_cost': 2112.0}, {'tour': [10, 4, 11, 9, 2, 1, 7, 3, 5, 0, 6, 8], 'cur_cost': 1927.0}, {'tour': [0, 4, 6, 11, 2, 9, 1, 8, 5, 10, 7, 3], 'cur_cost': 2221.0}, {'tour': [1, 0, 5, 3, 2, 8, 4, 9, 10, 11, 6, 7], 'cur_cost': 1451.0}, {'tour': [4, 5, 0, 1, 2, 3, 8, 7, 6, 11, 10, 9], 'cur_cost': 1344.0}, {'tour': [6, 9, 3, 11, 1, 8, 5, 10, 0, 7, 4, 2], 'cur_cost': 2176.0}, {'tour': [11, 0, 4, 10, 9, 5, 3, 2, 1, 6, 7, 8], 'cur_cost': 1660.0}]
2025-08-05 10:28:40,178 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:40,178 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 102, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 102, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:40,179 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([ 9,  8, 10,  2,  6, 11,  4,  1,  3,  0,  7,  5], dtype=int64), 'cur_cost': 2092.0, 'intermediate_solutions': [{'tour': array([ 8,  5,  7,  1,  4,  2,  0,  6, 10,  3,  9, 11]), 'cur_cost': 2170.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1,  8,  5,  7,  4,  2,  0,  6, 10,  3,  9, 11]), 'cur_cost': 2174.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 4,  1,  8,  5,  7,  2,  0,  6, 10,  3,  9, 11]), 'cur_cost': 2084.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 7,  1,  8,  5,  4,  2,  0,  6, 10,  3,  9, 11]), 'cur_cost': 2019.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 7,  4,  1,  8,  5,  2,  0,  6, 10,  3,  9, 11]), 'cur_cost': 2204.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:40,179 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 2092.00)
2025-08-05 10:28:40,179 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:40,179 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:40,179 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:40,180 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:40,180 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,180 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,180 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,181 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,181 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1715.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:40,181 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 4, 3, 5, 10, 11, 7, 8, 9, 6], 'cur_cost': 1715.0, 'intermediate_solutions': [{'tour': [4, 5, 0, 1, 2, 9, 3, 8, 6, 7, 11, 10], 'cur_cost': 1641.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 5, 0, 1, 3, 9, 2, 10, 11, 7, 6, 8], 'cur_cost': 1854.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 5, 0, 3, 9, 2, 8, 6, 7, 11, 10, 1], 'cur_cost': 1872.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:40,181 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1715.00)
2025-08-05 10:28:40,181 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:40,181 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:40,181 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:40,182 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:40,182 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,182 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,182 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,182 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,182 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1723.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:40,182 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [3, 11, 1, 6, 0, 9, 4, 5, 10, 2, 7, 8], 'cur_cost': 1723.0, 'intermediate_solutions': [{'tour': [3, 5, 9, 11, 6, 8, 4, 2, 0, 1, 10, 7], 'cur_cost': 2169.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 6, 11, 9, 5, 10, 4, 2, 0, 1, 3, 7], 'cur_cost': 1841.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 10, 5, 9, 11, 6, 8, 4, 0, 1, 3, 7], 'cur_cost': 1962.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:40,182 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1723.00)
2025-08-05 10:28:40,182 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:40,183 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:40,183 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:40,183 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:40,183 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,183 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,183 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,184 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,184 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1558.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:40,184 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 6, 2, 7, 1, 3, 4, 5, 11, 10, 9, 8], 'cur_cost': 1558.0, 'intermediate_solutions': [{'tour': [4, 6, 5, 9, 11, 8, 2, 3, 1, 0, 7, 10], 'cur_cost': 2122.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 5, 6, 4, 11, 8, 7, 3, 1, 0, 2, 10], 'cur_cost': 2055.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 6, 5, 0, 9, 11, 8, 7, 3, 1, 2, 10], 'cur_cost': 2110.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:40,184 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1558.00)
2025-08-05 10:28:40,184 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:40,184 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:40,184 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:40,184 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:40,185 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,185 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,185 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,185 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,185 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1838.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:40,185 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 9, 4, 6, 10, 5, 1, 2, 3, 8, 7, 11], 'cur_cost': 1838.0, 'intermediate_solutions': [{'tour': [10, 4, 8, 9, 2, 1, 7, 3, 5, 0, 6, 11], 'cur_cost': 1573.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 2, 9, 11, 4, 10, 7, 3, 5, 0, 6, 8], 'cur_cost': 2031.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 4, 8, 11, 9, 2, 1, 7, 3, 5, 0, 6], 'cur_cost': 1971.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:40,185 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1838.00)
2025-08-05 10:28:40,185 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:28:40,185 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:40,186 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:40,186 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 2007.0
2025-08-05 10:28:40,195 - ExploitationExpert - INFO - res_population_num: 37
2025-08-05 10:28:40,195 - ExploitationExpert - INFO - res_population_costs: [1344.0, 1344, 1344, 1344, 1344.0, 1344, 1344.0, 1344, 1344, 1344, 1344, 1344.0, 1344, 1344, 1344.0, 1344, 1344, 1344, 1344.0, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344.0, 1344, 1344, 1344.0, 1344, 1344, 1344.0, 1344, 1344, 1344]
2025-08-05 10:28:40,195 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0,  5, 10,  4,  3,  9,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  7,  8,  9, 10,  5,  4,  3,  2,  1], dtype=int64), array([ 0,  5, 11,  6,  7,  8,  9, 10,  4,  3,  2,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  3,  2,  8,  7,  6,  1], dtype=int64), array([ 0, 11, 10,  9,  8,  7,  6,  1,  2,  3,  4,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9, 10,  4,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  5,  4, 10, 11,  6,  7,  8,  9,  3,  2,  1], dtype=int64), array([ 0,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  2,  8,  7,  6, 11, 10,  9,  3,  4,  5], dtype=int64), array([ 0,  5,  4,  9, 10, 11,  6,  7,  8,  3,  2,  1], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  8,  3,  2,  1,  7,  6], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  7,  8,  9, 10, 11,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  2,  8,  7,  1,  6, 11], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  3,  9,  8,  7,  2,  1], dtype=int64), array([ 0,  5,  4,  3,  2,  8,  9, 10, 11,  6,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0,  1,  2,  7,  8,  9,  3,  4, 10,  5, 11,  6], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11,  6], dtype=int64)]
2025-08-05 10:28:40,201 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:40,201 - ExploitationExpert - INFO - populations: [{'tour': array([ 9,  8, 10,  2,  6, 11,  4,  1,  3,  0,  7,  5], dtype=int64), 'cur_cost': 2092.0}, {'tour': [0, 1, 2, 4, 3, 5, 10, 11, 7, 8, 9, 6], 'cur_cost': 1715.0}, {'tour': [3, 11, 1, 6, 0, 9, 4, 5, 10, 2, 7, 8], 'cur_cost': 1723.0}, {'tour': [0, 6, 2, 7, 1, 3, 4, 5, 11, 10, 9, 8], 'cur_cost': 1558.0}, {'tour': [0, 9, 4, 6, 10, 5, 1, 2, 3, 8, 7, 11], 'cur_cost': 1838.0}, {'tour': array([ 4,  5,  3,  0,  8,  7,  9, 11, 10,  2,  1,  6], dtype=int64), 'cur_cost': 2007.0}, {'tour': [1, 0, 5, 3, 2, 8, 4, 9, 10, 11, 6, 7], 'cur_cost': 1451.0}, {'tour': [4, 5, 0, 1, 2, 3, 8, 7, 6, 11, 10, 9], 'cur_cost': 1344.0}, {'tour': [6, 9, 3, 11, 1, 8, 5, 10, 0, 7, 4, 2], 'cur_cost': 2176.0}, {'tour': [11, 0, 4, 10, 9, 5, 3, 2, 1, 6, 7, 8], 'cur_cost': 1660.0}]
2025-08-05 10:28:40,202 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:40,202 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 103, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 103, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:40,203 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([ 4,  5,  3,  0,  8,  7,  9, 11, 10,  2,  1,  6], dtype=int64), 'cur_cost': 2007.0, 'intermediate_solutions': [{'tour': array([ 6,  4,  0, 11,  2,  9,  1,  8,  5, 10,  7,  3]), 'cur_cost': 2264.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([11,  6,  4,  0,  2,  9,  1,  8,  5, 10,  7,  3]), 'cur_cost': 2204.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 2, 11,  6,  4,  0,  9,  1,  8,  5, 10,  7,  3]), 'cur_cost': 2203.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 0, 11,  6,  4,  2,  9,  1,  8,  5, 10,  7,  3]), 'cur_cost': 2115.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 0,  2, 11,  6,  4,  9,  1,  8,  5, 10,  7,  3]), 'cur_cost': 2160.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:40,203 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 2007.00)
2025-08-05 10:28:40,204 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:40,204 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:40,204 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:40,204 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:40,204 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,205 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,205 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,205 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,205 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1781.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:40,206 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [1, 8, 0, 2, 11, 5, 4, 3, 9, 10, 6, 7], 'cur_cost': 1781.0, 'intermediate_solutions': [{'tour': [1, 0, 8, 3, 2, 5, 4, 9, 10, 11, 6, 7], 'cur_cost': 1513.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 0, 4, 8, 2, 3, 5, 9, 10, 11, 6, 7], 'cur_cost': 1559.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 5, 2, 3, 8, 4, 9, 10, 11, 6, 7], 'cur_cost': 1468.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:40,206 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 1781.00)
2025-08-05 10:28:40,207 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:40,207 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:40,207 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:40,208 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:40,208 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,208 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,208 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,209 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,209 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1559.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:40,209 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 4, 1, 5, 6, 7, 2, 3, 8, 9, 10, 11], 'cur_cost': 1559.0, 'intermediate_solutions': [{'tour': [4, 5, 8, 1, 2, 3, 0, 7, 6, 11, 10, 9], 'cur_cost': 1636.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 2, 1, 0, 5, 3, 8, 7, 6, 11, 10, 9], 'cur_cost': 1436.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 5, 0, 2, 1, 3, 8, 7, 6, 11, 10, 9], 'cur_cost': 1436.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:40,209 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 1559.00)
2025-08-05 10:28:40,209 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:40,210 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:40,210 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:40,210 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 1861.0
2025-08-05 10:28:40,224 - ExploitationExpert - INFO - res_population_num: 38
2025-08-05 10:28:40,224 - ExploitationExpert - INFO - res_population_costs: [1344.0, 1344, 1344, 1344, 1344.0, 1344, 1344.0, 1344, 1344, 1344, 1344, 1344.0, 1344, 1344, 1344.0, 1344, 1344, 1344, 1344.0, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344.0, 1344, 1344, 1344.0, 1344, 1344, 1344.0, 1344, 1344, 1344, 1344.0]
2025-08-05 10:28:40,225 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0,  5, 10,  4,  3,  9,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  7,  8,  9, 10,  5,  4,  3,  2,  1], dtype=int64), array([ 0,  5, 11,  6,  7,  8,  9, 10,  4,  3,  2,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  3,  2,  8,  7,  6,  1], dtype=int64), array([ 0, 11, 10,  9,  8,  7,  6,  1,  2,  3,  4,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9, 10,  4,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  5,  4, 10, 11,  6,  7,  8,  9,  3,  2,  1], dtype=int64), array([ 0,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  2,  8,  7,  6, 11, 10,  9,  3,  4,  5], dtype=int64), array([ 0,  5,  4,  9, 10, 11,  6,  7,  8,  3,  2,  1], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  8,  3,  2,  1,  7,  6], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  7,  8,  9, 10, 11,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  2,  8,  7,  1,  6, 11], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  3,  9,  8,  7,  2,  1], dtype=int64), array([ 0,  5,  4,  3,  2,  8,  9, 10, 11,  6,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0,  1,  2,  7,  8,  9,  3,  4, 10,  5, 11,  6], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  5, 10, 11,  6,  7,  8,  9,  4,  3,  2,  1], dtype=int64)]
2025-08-05 10:28:40,236 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:40,236 - ExploitationExpert - INFO - populations: [{'tour': array([ 9,  8, 10,  2,  6, 11,  4,  1,  3,  0,  7,  5], dtype=int64), 'cur_cost': 2092.0}, {'tour': [0, 1, 2, 4, 3, 5, 10, 11, 7, 8, 9, 6], 'cur_cost': 1715.0}, {'tour': [3, 11, 1, 6, 0, 9, 4, 5, 10, 2, 7, 8], 'cur_cost': 1723.0}, {'tour': [0, 6, 2, 7, 1, 3, 4, 5, 11, 10, 9, 8], 'cur_cost': 1558.0}, {'tour': [0, 9, 4, 6, 10, 5, 1, 2, 3, 8, 7, 11], 'cur_cost': 1838.0}, {'tour': array([ 4,  5,  3,  0,  8,  7,  9, 11, 10,  2,  1,  6], dtype=int64), 'cur_cost': 2007.0}, {'tour': [1, 8, 0, 2, 11, 5, 4, 3, 9, 10, 6, 7], 'cur_cost': 1781.0}, {'tour': [0, 4, 1, 5, 6, 7, 2, 3, 8, 9, 10, 11], 'cur_cost': 1559.0}, {'tour': array([10,  0,  7,  6,  8,  4,  2,  3,  9,  5,  1, 11], dtype=int64), 'cur_cost': 1861.0}, {'tour': [11, 0, 4, 10, 9, 5, 3, 2, 1, 6, 7, 8], 'cur_cost': 1660.0}]
2025-08-05 10:28:40,237 - ExploitationExpert - INFO - 局部搜索耗时: 0.03秒，最大迭代次数: 10
2025-08-05 10:28:40,237 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 104, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 104, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:40,238 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([10,  0,  7,  6,  8,  4,  2,  3,  9,  5,  1, 11], dtype=int64), 'cur_cost': 1861.0, 'intermediate_solutions': [{'tour': array([ 3,  9,  6, 11,  1,  8,  5, 10,  0,  7,  4,  2]), 'cur_cost': 2008.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([11,  3,  9,  6,  1,  8,  5, 10,  0,  7,  4,  2]), 'cur_cost': 2159.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1, 11,  3,  9,  6,  8,  5, 10,  0,  7,  4,  2]), 'cur_cost': 2171.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6, 11,  3,  9,  1,  8,  5, 10,  0,  7,  4,  2]), 'cur_cost': 2060.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6,  1, 11,  3,  9,  8,  5, 10,  0,  7,  4,  2]), 'cur_cost': 1954.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:40,238 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1861.00)
2025-08-05 10:28:40,239 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:40,239 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:40,239 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:40,240 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:40,240 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,241 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,241 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,241 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:40,241 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1687.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:40,242 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [6, 7, 2, 1, 3, 4, 5, 0, 10, 11, 9, 8], 'cur_cost': 1687.0, 'intermediate_solutions': [{'tour': [11, 0, 2, 10, 9, 5, 3, 4, 1, 6, 7, 8], 'cur_cost': 1828.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 0, 4, 10, 9, 8, 7, 6, 1, 2, 3, 5], 'cur_cost': 1436.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 0, 4, 9, 5, 3, 10, 2, 1, 6, 7, 8], 'cur_cost': 1828.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:40,242 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1687.00)
2025-08-05 10:28:40,242 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:40,242 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:40,244 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 9,  8, 10,  2,  6, 11,  4,  1,  3,  0,  7,  5], dtype=int64), 'cur_cost': 2092.0, 'intermediate_solutions': [{'tour': array([ 8,  5,  7,  1,  4,  2,  0,  6, 10,  3,  9, 11]), 'cur_cost': 2170.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1,  8,  5,  7,  4,  2,  0,  6, 10,  3,  9, 11]), 'cur_cost': 2174.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 4,  1,  8,  5,  7,  2,  0,  6, 10,  3,  9, 11]), 'cur_cost': 2084.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 7,  1,  8,  5,  4,  2,  0,  6, 10,  3,  9, 11]), 'cur_cost': 2019.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 7,  4,  1,  8,  5,  2,  0,  6, 10,  3,  9, 11]), 'cur_cost': 2204.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 4, 3, 5, 10, 11, 7, 8, 9, 6], 'cur_cost': 1715.0, 'intermediate_solutions': [{'tour': [4, 5, 0, 1, 2, 9, 3, 8, 6, 7, 11, 10], 'cur_cost': 1641.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 5, 0, 1, 3, 9, 2, 10, 11, 7, 6, 8], 'cur_cost': 1854.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 5, 0, 3, 9, 2, 8, 6, 7, 11, 10, 1], 'cur_cost': 1872.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [3, 11, 1, 6, 0, 9, 4, 5, 10, 2, 7, 8], 'cur_cost': 1723.0, 'intermediate_solutions': [{'tour': [3, 5, 9, 11, 6, 8, 4, 2, 0, 1, 10, 7], 'cur_cost': 2169.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 6, 11, 9, 5, 10, 4, 2, 0, 1, 3, 7], 'cur_cost': 1841.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 10, 5, 9, 11, 6, 8, 4, 0, 1, 3, 7], 'cur_cost': 1962.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 2, 7, 1, 3, 4, 5, 11, 10, 9, 8], 'cur_cost': 1558.0, 'intermediate_solutions': [{'tour': [4, 6, 5, 9, 11, 8, 2, 3, 1, 0, 7, 10], 'cur_cost': 2122.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 5, 6, 4, 11, 8, 7, 3, 1, 0, 2, 10], 'cur_cost': 2055.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 6, 5, 0, 9, 11, 8, 7, 3, 1, 2, 10], 'cur_cost': 2110.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 4, 6, 10, 5, 1, 2, 3, 8, 7, 11], 'cur_cost': 1838.0, 'intermediate_solutions': [{'tour': [10, 4, 8, 9, 2, 1, 7, 3, 5, 0, 6, 11], 'cur_cost': 1573.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 2, 9, 11, 4, 10, 7, 3, 5, 0, 6, 8], 'cur_cost': 2031.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 4, 8, 11, 9, 2, 1, 7, 3, 5, 0, 6], 'cur_cost': 1971.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 4,  5,  3,  0,  8,  7,  9, 11, 10,  2,  1,  6], dtype=int64), 'cur_cost': 2007.0, 'intermediate_solutions': [{'tour': array([ 6,  4,  0, 11,  2,  9,  1,  8,  5, 10,  7,  3]), 'cur_cost': 2264.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([11,  6,  4,  0,  2,  9,  1,  8,  5, 10,  7,  3]), 'cur_cost': 2204.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 2, 11,  6,  4,  0,  9,  1,  8,  5, 10,  7,  3]), 'cur_cost': 2203.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 0, 11,  6,  4,  2,  9,  1,  8,  5, 10,  7,  3]), 'cur_cost': 2115.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 0,  2, 11,  6,  4,  9,  1,  8,  5, 10,  7,  3]), 'cur_cost': 2160.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [1, 8, 0, 2, 11, 5, 4, 3, 9, 10, 6, 7], 'cur_cost': 1781.0, 'intermediate_solutions': [{'tour': [1, 0, 8, 3, 2, 5, 4, 9, 10, 11, 6, 7], 'cur_cost': 1513.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 0, 4, 8, 2, 3, 5, 9, 10, 11, 6, 7], 'cur_cost': 1559.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 5, 2, 3, 8, 4, 9, 10, 11, 6, 7], 'cur_cost': 1468.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 1, 5, 6, 7, 2, 3, 8, 9, 10, 11], 'cur_cost': 1559.0, 'intermediate_solutions': [{'tour': [4, 5, 8, 1, 2, 3, 0, 7, 6, 11, 10, 9], 'cur_cost': 1636.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 2, 1, 0, 5, 3, 8, 7, 6, 11, 10, 9], 'cur_cost': 1436.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 5, 0, 2, 1, 3, 8, 7, 6, 11, 10, 9], 'cur_cost': 1436.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([10,  0,  7,  6,  8,  4,  2,  3,  9,  5,  1, 11], dtype=int64), 'cur_cost': 1861.0, 'intermediate_solutions': [{'tour': array([ 3,  9,  6, 11,  1,  8,  5, 10,  0,  7,  4,  2]), 'cur_cost': 2008.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([11,  3,  9,  6,  1,  8,  5, 10,  0,  7,  4,  2]), 'cur_cost': 2159.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1, 11,  3,  9,  6,  8,  5, 10,  0,  7,  4,  2]), 'cur_cost': 2171.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6, 11,  3,  9,  1,  8,  5, 10,  0,  7,  4,  2]), 'cur_cost': 2060.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6,  1, 11,  3,  9,  8,  5, 10,  0,  7,  4,  2]), 'cur_cost': 1954.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [6, 7, 2, 1, 3, 4, 5, 0, 10, 11, 9, 8], 'cur_cost': 1687.0, 'intermediate_solutions': [{'tour': [11, 0, 2, 10, 9, 5, 3, 4, 1, 6, 7, 8], 'cur_cost': 1828.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 0, 4, 10, 9, 8, 7, 6, 1, 2, 3, 5], 'cur_cost': 1436.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 0, 4, 9, 5, 3, 10, 2, 1, 6, 7, 8], 'cur_cost': 1828.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:40,245 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:40,245 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:40,247 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1558.000, 多样性=0.894
2025-08-05 10:28:40,247 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 10:28:40,247 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 10:28:40,247 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:40,255 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.015886690965790984, 'best_improvement': -0.15922619047619047}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.010245901639344062}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.039234640828944886, 'recent_improvements': [-0.024888256391725854, -0.026270680765820825, 0.05358102526616392], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 38, 'new_count': 38, 'count_change': 0, 'old_best_cost': 1344.0, 'new_best_cost': 1344.0, 'quality_improvement': 0.0, 'old_diversity': 0.7537932669511617, 'new_diversity': 0.7537932669511617, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:40,264 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 10:28:40,274 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\geometry2_12_solution.json
2025-08-05 10:28:40,275 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\geometry2_12_20250805_102840.solution
2025-08-05 10:28:40,275 - __main__ - INFO - 实例执行完成 - 运行时间: 1.63s, 最佳成本: 1344.0
2025-08-05 10:28:40,275 - __main__ - INFO - 实例 geometry2_12 处理完成
