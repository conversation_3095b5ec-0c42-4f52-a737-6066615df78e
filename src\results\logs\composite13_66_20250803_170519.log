2025-08-03 17:05:19,266 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-08-03 17:05:19,266 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-03 17:05:19,269 - StatsExpert - INFO - 开始统计分析
2025-08-03 17:05:19,282 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=9936.000, 多样性=0.972
2025-08-03 17:05:19,287 - PathExpert - INFO - 开始路径结构分析
2025-08-03 17:05:19,293 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.972
2025-08-03 17:05:19,318 - EliteExpert - INFO - 开始精英解分析
2025-08-03 17:05:19,320 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/1)
2025-08-03 17:05:19,320 - LandscapeExpert - INFO - 使用直接传入的种群数据: 20个个体
2025-08-03 17:05:19,320 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-03 17:05:19,320 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-03 17:05:19,561 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: 1732.610, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 1.000
2025-08-03 17:05:19,562 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-03 17:05:19,562 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-03 17:05:19,641 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记
2025-08-03 17:05:19,967 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_1_20250803_170519.html
2025-08-03 17:05:20,038 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_1_20250803_170519.html
2025-08-03 17:05:20,038 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-03 17:05:20,038 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-03 17:05:20,038 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.7184秒
2025-08-03 17:05:20,038 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-03 17:05:20,038 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 1732.6100000000006, 'local_optima_density': 0.1, 'gradient_variance': 2018935490.7299, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.002, 'fitness_entropy': 0.8888583270495175, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 1732.610)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 1, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754211919.5623085, 'performance_metrics': {}}}
2025-08-03 17:05:20,038 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-03 17:05:20,038 - experts.strategy.enhanced_strategy_expert - INFO - 开始增强策略分配分析 (迭代 0)
2025-08-03 17:05:20,039 - experts.analysis.individual_state_analyzer - INFO - 开始分析种群状态 (迭代 0, 种群大小: 20)
2025-08-03 17:05:20,039 - experts.strategy.enhanced_strategy_expert - ERROR - 策略分配过程中发生错误: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 17:05:20,039 - experts.strategy.enhanced_strategy_expert - WARNING - 使用回退策略分配
2025-08-03 17:05:20,039 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 17:05:20,039 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 17:05:20,039 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-03 17:05:20,039 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 17:05:20,039 - __main__ - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 17:05:20,040 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-03 17:05:20,040 - experts.management.collaboration_manager - INFO - 识别精英个体: {2, 3, 4, 5} (总数: 4, 保护比例: 0.20)
2025-08-03 17:05:20,040 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-03 17:05:20,040 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-03 17:05:20,040 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:20,055 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 17:05:20,055 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:20,190 - ExplorationExpert - INFO - 探索路径生成完成，成本: 68630.0, 路径长度: 66
2025-08-03 17:05:20,190 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [20, 6, 58, 7, 64, 3, 60, 52, 21, 32, 9, 10, 5, 19, 15, 11, 27, 31, 29, 12, 35, 36, 0, 22, 28, 16, 34, 8, 57, 1, 54, 23, 4, 61, 39, 40, 49, 51, 14, 48, 13, 17, 30, 46, 44, 26, 2, 55, 65, 18, 45, 37, 42, 50, 43, 38, 47, 41, 24, 56, 62, 59, 63, 53, 25, 33], 'cur_cost': 68630.0}
2025-08-03 17:05:20,190 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 68630.00)
2025-08-03 17:05:20,190 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-03 17:05:20,190 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:05:20,193 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:05:20,193 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 99532.0
2025-08-03 17:05:21,830 - ExploitationExpert - INFO - res_population_num: 1
2025-08-03 17:05:21,830 - ExploitationExpert - INFO - res_population_costs: [9869.0]
2025-08-03 17:05:21,830 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 64, 53, 62, 59, 56,
       58, 60, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64)]
2025-08-03 17:05:21,831 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:05:21,831 - ExploitationExpert - INFO - populations: [{'tour': [20, 6, 58, 7, 64, 3, 60, 52, 21, 32, 9, 10, 5, 19, 15, 11, 27, 31, 29, 12, 35, 36, 0, 22, 28, 16, 34, 8, 57, 1, 54, 23, 4, 61, 39, 40, 49, 51, 14, 48, 13, 17, 30, 46, 44, 26, 2, 55, 65, 18, 45, 37, 42, 50, 43, 38, 47, 41, 24, 56, 62, 59, 63, 53, 25, 33], 'cur_cost': 68630.0}, {'tour': array([19, 51, 20, 37, 23, 56, 45, 47, 59, 63, 46,  8, 16, 54, 33, 60, 39,
       29, 42, 14, 34, 27, 30, 13, 41, 44, 21, 32, 55, 38,  0, 57,  3,  7,
        9, 43, 28, 12, 17, 22,  5, 26, 48,  2, 35, 18, 31, 40, 58, 10, 50,
       15, 24, 49, 65, 52, 53, 62, 11, 64,  4, 36,  1, 61,  6, 25],
      dtype=int64), 'cur_cost': 99532.0}, {'tour': array([38, 51, 50, 45, 44, 41, 39, 47, 49, 40, 43, 48, 46, 42, 21, 20, 13,
       23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30,
       34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,  2,  6, 10,
        0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63],
      dtype=int64), 'cur_cost': 9956.0}, {'tour': array([20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36,
       35, 28, 30, 34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,
        2,  6, 10,  0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9958.0}, {'tour': array([36, 26, 25, 31, 33, 28, 30, 35, 34, 37, 27, 24, 29, 32, 18, 16, 23,
       22, 12, 17, 15, 14, 20, 21, 13, 19,  9, 11,  7,  3,  1,  0, 10,  8,
        2,  6,  4,  5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9955.0}, {'tour': array([37, 25, 26, 36, 27, 31, 33, 28, 30, 35, 34, 32, 29, 24, 18, 16, 23,
       22, 12, 17, 15, 14, 20, 21, 13, 19,  9, 11,  7,  3,  1,  0, 10,  8,
        2,  6,  4,  5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9936.0}, {'tour': array([63, 57, 47, 24, 55,  1, 28, 10, 19, 58,  9, 30, 45, 14, 65, 44, 62,
       38, 36, 46, 59, 23, 15, 25, 18, 53, 13, 37, 56,  5, 49, 29, 21, 40,
        7,  4, 12,  0, 51, 20,  6, 17, 50, 32,  2, 34, 33, 42, 54, 61, 60,
       41, 48, 31, 11, 52, 27, 35, 16, 39, 64,  3, 43,  8, 22, 26],
      dtype=int64), 'cur_cost': 113059.0}, {'tour': array([20, 19,  3,  1, 56,  8, 42, 12, 49, 32, 55, 17, 33, 63, 22, 53, 11,
       51, 37, 27,  4, 60, 39, 43, 58, 24, 15, 26, 65, 40, 35, 52, 10, 29,
       28, 23, 30, 13,  6, 21, 31,  9, 18, 36, 41, 38, 50, 59, 62,  7, 57,
       54, 45,  0, 16, 47,  2, 14, 34, 61, 25, 46, 64, 44, 48,  5],
      dtype=int64), 'cur_cost': 106754.0}, {'tour': array([19, 17, 34, 64, 44, 60, 58, 13, 32,  3, 12, 25, 16, 57, 40, 59, 33,
       21, 56, 15, 27, 31, 54, 63, 41, 62,  7,  8, 38, 35, 65,  6, 50, 20,
        4,  2, 23, 10, 48, 39, 45,  1, 52, 47, 36, 49,  5, 28, 24, 11, 14,
       37, 43, 53, 29, 55, 46, 22,  9,  0, 51, 18, 61, 42, 30, 26],
      dtype=int64), 'cur_cost': 112181.0}, {'tour': array([32, 20, 36, 38, 43, 51, 14, 17, 27, 46, 47, 42, 34, 55,  8, 53, 35,
       24, 59, 60, 39,  3,  5, 33, 45,  4, 26, 19, 18,  7,  6, 65, 12, 30,
       22, 49,  1, 31, 41, 13, 57, 50, 44, 54, 48, 11, 61, 23, 28, 37,  2,
       21, 40, 10, 64, 63, 62, 25, 15, 29, 56, 16, 58, 52,  0,  9],
      dtype=int64), 'cur_cost': 94763.0}, {'tour': array([33,  1, 19, 16,  3,  7,  0, 20, 27, 25, 22, 53, 31,  5, 24, 26, 45,
       10, 18, 63, 39, 48, 42, 15, 32, 60, 46, 41, 52, 14, 35, 38, 58, 29,
       59, 47, 62,  8, 44, 49, 28, 57, 65, 36, 64, 12,  4, 56, 55, 11, 17,
       43, 23,  9,  6, 51, 34, 13, 21, 54, 30, 37, 50, 40, 61,  2],
      dtype=int64), 'cur_cost': 103189.0}, {'tour': array([11, 48, 14, 39, 52, 17, 29, 50, 65, 44,  5, 27, 21,  2, 10, 33, 45,
       37,  9, 25, 49, 42,  0, 20, 41, 58, 59, 51,  8, 64, 61, 47,  1, 63,
       53, 54, 28, 46, 36, 34, 23, 31, 40, 35, 15, 18,  4, 60, 12, 43, 30,
       56,  7, 13,  6, 55, 38, 16, 62, 32, 22, 24, 19, 57,  3, 26],
      dtype=int64), 'cur_cost': 109582.0}, {'tour': array([ 3, 35, 10, 50, 60,  6, 46, 48, 53, 28, 49, 58, 23, 40, 52,  7, 51,
       43, 31, 30, 41, 39, 38, 33, 55, 57, 64, 18, 56, 42, 54,  0, 44, 29,
       59, 22, 24, 21, 61, 14, 26, 11, 32, 20, 34,  2, 17, 36,  4,  9,  1,
       45, 13,  5, 47, 37, 63, 65, 19,  8, 25, 12, 27, 62, 15, 16],
      dtype=int64), 'cur_cost': 110185.0}, {'tour': array([21,  8, 11, 22, 31, 53, 47,  4, 59, 45, 51, 16, 39,  1, 46, 34, 32,
       38, 12, 60, 58, 62, 33, 18, 41, 19, 20, 54,  7, 14, 25, 40, 56,  5,
       30, 26, 50, 15, 48, 57, 61,  3, 65,  9, 28, 43, 17, 44, 36, 49, 42,
       24,  6, 13, 55,  0, 27, 23, 29, 52, 35, 64, 37, 10, 63,  2],
      dtype=int64), 'cur_cost': 104089.0}, {'tour': array([14, 53, 17, 65,  0, 18,  8, 31,  7, 40, 22,  1, 51, 10, 61, 41, 30,
       54, 60, 23, 63, 35,  9, 26, 47, 12, 19,  5, 15, 36, 33, 64, 49, 59,
       32, 62, 37,  2, 48, 38, 24, 50, 27, 52, 46, 20,  4, 55, 25, 45, 21,
       34, 11,  6, 39, 43, 56, 16, 58, 13,  3, 42, 44, 29, 57, 28],
      dtype=int64), 'cur_cost': 122553.0}, {'tour': array([34, 65, 27, 56, 35,  6, 18, 13, 63, 47, 60,  1, 45, 19, 28, 53,  8,
        3, 39, 52, 16, 17, 10, 43, 14, 57, 58, 36, 25, 31, 42, 21, 62, 50,
       29, 26, 20, 24, 46,  7, 30, 55,  4, 59, 41,  0, 40, 49, 22, 33, 44,
       23, 51,  9, 15,  2, 64, 11, 48, 54, 38, 12, 32,  5, 61, 37],
      dtype=int64), 'cur_cost': 114249.0}, {'tour': array([37, 27, 15, 60, 39, 53, 36, 35, 48, 30, 33, 31, 34, 22, 12, 17, 44,
       10,  5, 19, 41, 14, 62, 38, 18, 21, 52, 61, 54,  6, 56,  1, 59, 43,
       28, 11, 23,  0, 25, 26, 46, 49, 51,  2,  8, 29,  9, 47, 42, 13, 16,
        3, 20, 24, 45, 40,  7, 64, 55, 63, 32, 58, 57, 50, 65,  4],
      dtype=int64), 'cur_cost': 86322.0}, {'tour': array([11,  5, 62, 54, 24, 23, 27, 17, 14, 64, 33,  9, 65, 20, 30,  0, 61,
       15, 35,  8, 51,  4, 53, 55,  1,  6, 22, 43, 50,  3, 16, 29, 37, 38,
       26, 44, 49,  2, 25, 10, 32, 13, 42, 46, 63, 34, 52, 19, 56, 59, 21,
       45, 57, 36, 47, 28, 12, 60, 48,  7, 41, 40, 58, 31, 18, 39],
      dtype=int64), 'cur_cost': 107612.0}, {'tour': array([34, 51, 38, 16, 48,  0, 56, 39,  4, 58, 55, 40, 24, 31, 54,  2, 59,
       23, 35, 22, 65, 25, 46, 27, 64, 57, 63,  7, 28, 33, 49, 29, 36, 52,
       41, 15, 18, 10, 62, 14, 13, 30, 42, 21, 50, 47,  6, 60,  5, 61,  9,
       53, 12,  8, 11,  1, 45, 17, 19, 26, 37, 43, 20,  3, 44, 32],
      dtype=int64), 'cur_cost': 91777.0}, {'tour': array([13, 64, 22, 42, 10, 65,  5, 41,  9, 60,  8, 46, 49, 27, 48,  0, 50,
       56, 32, 12,  4, 57, 55, 35, 28, 36, 24, 29, 53,  2, 30, 44, 52, 26,
       38, 23, 62,  6, 11, 31, 20, 19, 58, 45, 34, 54, 40, 15, 43, 39, 37,
       63, 21,  1, 14, 16, 61, 33, 17, 25, 47,  7, 59, 18, 51,  3],
      dtype=int64), 'cur_cost': 110355.0}]
2025-08-03 17:05:21,837 - ExploitationExpert - INFO - 局部搜索耗时: 1.64秒
2025-08-03 17:05:21,837 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-03 17:05:21,838 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([19, 51, 20, 37, 23, 56, 45, 47, 59, 63, 46,  8, 16, 54, 33, 60, 39,
       29, 42, 14, 34, 27, 30, 13, 41, 44, 21, 32, 55, 38,  0, 57,  3,  7,
        9, 43, 28, 12, 17, 22,  5, 26, 48,  2, 35, 18, 31, 40, 58, 10, 50,
       15, 24, 49, 65, 52, 53, 62, 11, 64,  4, 36,  1, 61,  6, 25],
      dtype=int64), 'cur_cost': 99532.0}
2025-08-03 17:05:21,838 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 99532.00)
2025-08-03 17:05:21,838 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-03 17:05:21,838 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-03 17:05:21,838 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:21,843 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 17:05:21,843 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:21,843 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12417.0, 路径长度: 66
2025-08-03 17:05:21,843 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 11, 4, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 3, 7, 1, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12417.0}
2025-08-03 17:05:21,843 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 12417.00)
2025-08-03 17:05:21,843 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-03 17:05:21,843 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-03 17:05:21,844 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:21,850 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 17:05:21,850 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:21,850 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12369.0, 路径长度: 66
2025-08-03 17:05:21,851 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 3, 19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12369.0}
2025-08-03 17:05:21,851 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 12369.00)
2025-08-03 17:05:21,851 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-03 17:05:21,851 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:05:21,851 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:05:21,851 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 107783.0
2025-08-03 17:05:23,632 - ExploitationExpert - INFO - res_population_num: 2
2025-08-03 17:05:23,632 - ExploitationExpert - INFO - res_population_costs: [9869.0, 9618.0]
2025-08-03 17:05:23,632 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 64, 53, 62, 59, 56,
       58, 60, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26, 36, 40, 43,
       48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59,
       56, 58, 60, 54, 57, 64, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64)]
2025-08-03 17:05:23,633 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:05:23,633 - ExploitationExpert - INFO - populations: [{'tour': [20, 6, 58, 7, 64, 3, 60, 52, 21, 32, 9, 10, 5, 19, 15, 11, 27, 31, 29, 12, 35, 36, 0, 22, 28, 16, 34, 8, 57, 1, 54, 23, 4, 61, 39, 40, 49, 51, 14, 48, 13, 17, 30, 46, 44, 26, 2, 55, 65, 18, 45, 37, 42, 50, 43, 38, 47, 41, 24, 56, 62, 59, 63, 53, 25, 33], 'cur_cost': 68630.0}, {'tour': array([19, 51, 20, 37, 23, 56, 45, 47, 59, 63, 46,  8, 16, 54, 33, 60, 39,
       29, 42, 14, 34, 27, 30, 13, 41, 44, 21, 32, 55, 38,  0, 57,  3,  7,
        9, 43, 28, 12, 17, 22,  5, 26, 48,  2, 35, 18, 31, 40, 58, 10, 50,
       15, 24, 49, 65, 52, 53, 62, 11, 64,  4, 36,  1, 61,  6, 25],
      dtype=int64), 'cur_cost': 99532.0}, {'tour': [0, 11, 4, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 3, 7, 1, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12417.0}, {'tour': [0, 3, 19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12369.0}, {'tour': array([ 9, 58, 30, 47,  4, 53, 42, 60, 46, 32, 17, 26, 31, 25, 65, 19, 41,
       57, 61, 38, 21, 13, 35, 62, 44, 55, 50, 64, 28, 34, 59, 54,  1,  6,
       45,  3, 29, 51, 48, 39, 56,  5, 37, 10, 33, 14, 18, 27, 43,  2,  8,
       20, 49, 12, 40, 15, 36,  7, 23, 63, 24, 22, 52,  0, 16, 11],
      dtype=int64), 'cur_cost': 107783.0}, {'tour': array([37, 25, 26, 36, 27, 31, 33, 28, 30, 35, 34, 32, 29, 24, 18, 16, 23,
       22, 12, 17, 15, 14, 20, 21, 13, 19,  9, 11,  7,  3,  1,  0, 10,  8,
        2,  6,  4,  5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9936.0}, {'tour': array([63, 57, 47, 24, 55,  1, 28, 10, 19, 58,  9, 30, 45, 14, 65, 44, 62,
       38, 36, 46, 59, 23, 15, 25, 18, 53, 13, 37, 56,  5, 49, 29, 21, 40,
        7,  4, 12,  0, 51, 20,  6, 17, 50, 32,  2, 34, 33, 42, 54, 61, 60,
       41, 48, 31, 11, 52, 27, 35, 16, 39, 64,  3, 43,  8, 22, 26],
      dtype=int64), 'cur_cost': 113059.0}, {'tour': array([20, 19,  3,  1, 56,  8, 42, 12, 49, 32, 55, 17, 33, 63, 22, 53, 11,
       51, 37, 27,  4, 60, 39, 43, 58, 24, 15, 26, 65, 40, 35, 52, 10, 29,
       28, 23, 30, 13,  6, 21, 31,  9, 18, 36, 41, 38, 50, 59, 62,  7, 57,
       54, 45,  0, 16, 47,  2, 14, 34, 61, 25, 46, 64, 44, 48,  5],
      dtype=int64), 'cur_cost': 106754.0}, {'tour': array([19, 17, 34, 64, 44, 60, 58, 13, 32,  3, 12, 25, 16, 57, 40, 59, 33,
       21, 56, 15, 27, 31, 54, 63, 41, 62,  7,  8, 38, 35, 65,  6, 50, 20,
        4,  2, 23, 10, 48, 39, 45,  1, 52, 47, 36, 49,  5, 28, 24, 11, 14,
       37, 43, 53, 29, 55, 46, 22,  9,  0, 51, 18, 61, 42, 30, 26],
      dtype=int64), 'cur_cost': 112181.0}, {'tour': array([32, 20, 36, 38, 43, 51, 14, 17, 27, 46, 47, 42, 34, 55,  8, 53, 35,
       24, 59, 60, 39,  3,  5, 33, 45,  4, 26, 19, 18,  7,  6, 65, 12, 30,
       22, 49,  1, 31, 41, 13, 57, 50, 44, 54, 48, 11, 61, 23, 28, 37,  2,
       21, 40, 10, 64, 63, 62, 25, 15, 29, 56, 16, 58, 52,  0,  9],
      dtype=int64), 'cur_cost': 94763.0}, {'tour': array([33,  1, 19, 16,  3,  7,  0, 20, 27, 25, 22, 53, 31,  5, 24, 26, 45,
       10, 18, 63, 39, 48, 42, 15, 32, 60, 46, 41, 52, 14, 35, 38, 58, 29,
       59, 47, 62,  8, 44, 49, 28, 57, 65, 36, 64, 12,  4, 56, 55, 11, 17,
       43, 23,  9,  6, 51, 34, 13, 21, 54, 30, 37, 50, 40, 61,  2],
      dtype=int64), 'cur_cost': 103189.0}, {'tour': array([11, 48, 14, 39, 52, 17, 29, 50, 65, 44,  5, 27, 21,  2, 10, 33, 45,
       37,  9, 25, 49, 42,  0, 20, 41, 58, 59, 51,  8, 64, 61, 47,  1, 63,
       53, 54, 28, 46, 36, 34, 23, 31, 40, 35, 15, 18,  4, 60, 12, 43, 30,
       56,  7, 13,  6, 55, 38, 16, 62, 32, 22, 24, 19, 57,  3, 26],
      dtype=int64), 'cur_cost': 109582.0}, {'tour': array([ 3, 35, 10, 50, 60,  6, 46, 48, 53, 28, 49, 58, 23, 40, 52,  7, 51,
       43, 31, 30, 41, 39, 38, 33, 55, 57, 64, 18, 56, 42, 54,  0, 44, 29,
       59, 22, 24, 21, 61, 14, 26, 11, 32, 20, 34,  2, 17, 36,  4,  9,  1,
       45, 13,  5, 47, 37, 63, 65, 19,  8, 25, 12, 27, 62, 15, 16],
      dtype=int64), 'cur_cost': 110185.0}, {'tour': array([21,  8, 11, 22, 31, 53, 47,  4, 59, 45, 51, 16, 39,  1, 46, 34, 32,
       38, 12, 60, 58, 62, 33, 18, 41, 19, 20, 54,  7, 14, 25, 40, 56,  5,
       30, 26, 50, 15, 48, 57, 61,  3, 65,  9, 28, 43, 17, 44, 36, 49, 42,
       24,  6, 13, 55,  0, 27, 23, 29, 52, 35, 64, 37, 10, 63,  2],
      dtype=int64), 'cur_cost': 104089.0}, {'tour': array([14, 53, 17, 65,  0, 18,  8, 31,  7, 40, 22,  1, 51, 10, 61, 41, 30,
       54, 60, 23, 63, 35,  9, 26, 47, 12, 19,  5, 15, 36, 33, 64, 49, 59,
       32, 62, 37,  2, 48, 38, 24, 50, 27, 52, 46, 20,  4, 55, 25, 45, 21,
       34, 11,  6, 39, 43, 56, 16, 58, 13,  3, 42, 44, 29, 57, 28],
      dtype=int64), 'cur_cost': 122553.0}, {'tour': array([34, 65, 27, 56, 35,  6, 18, 13, 63, 47, 60,  1, 45, 19, 28, 53,  8,
        3, 39, 52, 16, 17, 10, 43, 14, 57, 58, 36, 25, 31, 42, 21, 62, 50,
       29, 26, 20, 24, 46,  7, 30, 55,  4, 59, 41,  0, 40, 49, 22, 33, 44,
       23, 51,  9, 15,  2, 64, 11, 48, 54, 38, 12, 32,  5, 61, 37],
      dtype=int64), 'cur_cost': 114249.0}, {'tour': array([37, 27, 15, 60, 39, 53, 36, 35, 48, 30, 33, 31, 34, 22, 12, 17, 44,
       10,  5, 19, 41, 14, 62, 38, 18, 21, 52, 61, 54,  6, 56,  1, 59, 43,
       28, 11, 23,  0, 25, 26, 46, 49, 51,  2,  8, 29,  9, 47, 42, 13, 16,
        3, 20, 24, 45, 40,  7, 64, 55, 63, 32, 58, 57, 50, 65,  4],
      dtype=int64), 'cur_cost': 86322.0}, {'tour': array([11,  5, 62, 54, 24, 23, 27, 17, 14, 64, 33,  9, 65, 20, 30,  0, 61,
       15, 35,  8, 51,  4, 53, 55,  1,  6, 22, 43, 50,  3, 16, 29, 37, 38,
       26, 44, 49,  2, 25, 10, 32, 13, 42, 46, 63, 34, 52, 19, 56, 59, 21,
       45, 57, 36, 47, 28, 12, 60, 48,  7, 41, 40, 58, 31, 18, 39],
      dtype=int64), 'cur_cost': 107612.0}, {'tour': array([34, 51, 38, 16, 48,  0, 56, 39,  4, 58, 55, 40, 24, 31, 54,  2, 59,
       23, 35, 22, 65, 25, 46, 27, 64, 57, 63,  7, 28, 33, 49, 29, 36, 52,
       41, 15, 18, 10, 62, 14, 13, 30, 42, 21, 50, 47,  6, 60,  5, 61,  9,
       53, 12,  8, 11,  1, 45, 17, 19, 26, 37, 43, 20,  3, 44, 32],
      dtype=int64), 'cur_cost': 91777.0}, {'tour': array([13, 64, 22, 42, 10, 65,  5, 41,  9, 60,  8, 46, 49, 27, 48,  0, 50,
       56, 32, 12,  4, 57, 55, 35, 28, 36, 24, 29, 53,  2, 30, 44, 52, 26,
       38, 23, 62,  6, 11, 31, 20, 19, 58, 45, 34, 54, 40, 15, 43, 39, 37,
       63, 21,  1, 14, 16, 61, 33, 17, 25, 47,  7, 59, 18, 51,  3],
      dtype=int64), 'cur_cost': 110355.0}]
2025-08-03 17:05:23,639 - ExploitationExpert - INFO - 局部搜索耗时: 1.79秒
2025-08-03 17:05:23,639 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-03 17:05:23,639 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([ 9, 58, 30, 47,  4, 53, 42, 60, 46, 32, 17, 26, 31, 25, 65, 19, 41,
       57, 61, 38, 21, 13, 35, 62, 44, 55, 50, 64, 28, 34, 59, 54,  1,  6,
       45,  3, 29, 51, 48, 39, 56,  5, 37, 10, 33, 14, 18, 27, 43,  2,  8,
       20, 49, 12, 40, 15, 36,  7, 23, 63, 24, 22, 52,  0, 16, 11],
      dtype=int64), 'cur_cost': 107783.0}
2025-08-03 17:05:23,639 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 107783.00)
2025-08-03 17:05:23,640 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-03 17:05:23,640 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-03 17:05:23,640 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:23,655 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 17:05:23,656 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:23,656 - ExplorationExpert - INFO - 探索路径生成完成，成本: 56563.0, 路径长度: 66
2025-08-03 17:05:23,656 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [50, 35, 24, 17, 21, 28, 36, 13, 8, 5, 0, 61, 52, 14, 16, 30, 23, 9, 63, 65, 58, 12, 10, 15, 2, 55, 19, 7, 4, 3, 31, 43, 39, 48, 41, 22, 1, 62, 11, 6, 18, 49, 40, 38, 47, 44, 37, 29, 32, 34, 33, 20, 51, 46, 27, 26, 45, 60, 53, 64, 54, 59, 57, 56, 42, 25], 'cur_cost': 56563.0}
2025-08-03 17:05:23,656 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 56563.00)
2025-08-03 17:05:23,656 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-03 17:05:23,656 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-03 17:05:23,656 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:23,661 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 17:05:23,661 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:23,661 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98232.0, 路径长度: 66
2025-08-03 17:05:23,661 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [7, 21, 28, 23, 5, 27, 32, 31, 3, 4, 48, 39, 44, 2, 65, 18, 42, 43, 58, 59, 33, 25, 26, 41, 14, 53, 36, 47, 1, 40, 37, 50, 29, 38, 17, 49, 9, 11, 45, 20, 57, 6, 19, 13, 60, 55, 56, 51, 62, 15, 52, 54, 46, 8, 22, 30, 12, 10, 61, 63, 0, 35, 64, 24, 16, 34], 'cur_cost': 98232.0}
2025-08-03 17:05:23,661 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 98232.00)
2025-08-03 17:05:23,661 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-03 17:05:23,661 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:05:23,662 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:05:23,662 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 94203.0
2025-08-03 17:05:24,100 - ExploitationExpert - INFO - res_population_num: 9
2025-08-03 17:05:24,100 - ExploitationExpert - INFO - res_population_costs: [9869.0, 9618.0, 9561.0, 9557, 9556, 9556, 9555, 9555, 9527]
2025-08-03 17:05:24,100 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 64, 53, 62, 59, 56,
       58, 60, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26, 36, 40, 43,
       48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59,
       56, 58, 60, 54, 57, 64, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62, 59, 56,
       60, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34,
       35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13,
       23, 16, 18, 17, 12, 22, 15, 14,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4, 14, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34,
       42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 43, 40, 49, 58, 60, 54,
       57, 64, 65, 52, 63, 61, 53, 62, 59, 56, 55,  6,  2,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26,
       36, 37, 27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43,
       48, 49, 47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64,
       65, 52, 63, 61, 53, 62, 59, 56, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26,
       36, 37, 27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43,
       48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64,
       65, 52, 63, 61, 53, 62, 59, 56, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 56, 59, 62, 53, 61, 63, 52, 65, 64,
       57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36, 37, 25,
       26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 56, 59, 62, 53, 61, 63, 52, 65, 64,
       57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49, 48, 43,
       40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36, 37, 25,
       26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 17:05:24,103 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:05:24,103 - ExploitationExpert - INFO - populations: [{'tour': [20, 6, 58, 7, 64, 3, 60, 52, 21, 32, 9, 10, 5, 19, 15, 11, 27, 31, 29, 12, 35, 36, 0, 22, 28, 16, 34, 8, 57, 1, 54, 23, 4, 61, 39, 40, 49, 51, 14, 48, 13, 17, 30, 46, 44, 26, 2, 55, 65, 18, 45, 37, 42, 50, 43, 38, 47, 41, 24, 56, 62, 59, 63, 53, 25, 33], 'cur_cost': 68630.0}, {'tour': array([19, 51, 20, 37, 23, 56, 45, 47, 59, 63, 46,  8, 16, 54, 33, 60, 39,
       29, 42, 14, 34, 27, 30, 13, 41, 44, 21, 32, 55, 38,  0, 57,  3,  7,
        9, 43, 28, 12, 17, 22,  5, 26, 48,  2, 35, 18, 31, 40, 58, 10, 50,
       15, 24, 49, 65, 52, 53, 62, 11, 64,  4, 36,  1, 61,  6, 25],
      dtype=int64), 'cur_cost': 99532.0}, {'tour': [0, 11, 4, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 3, 7, 1, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12417.0}, {'tour': [0, 3, 19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12369.0}, {'tour': array([ 9, 58, 30, 47,  4, 53, 42, 60, 46, 32, 17, 26, 31, 25, 65, 19, 41,
       57, 61, 38, 21, 13, 35, 62, 44, 55, 50, 64, 28, 34, 59, 54,  1,  6,
       45,  3, 29, 51, 48, 39, 56,  5, 37, 10, 33, 14, 18, 27, 43,  2,  8,
       20, 49, 12, 40, 15, 36,  7, 23, 63, 24, 22, 52,  0, 16, 11],
      dtype=int64), 'cur_cost': 107783.0}, {'tour': [50, 35, 24, 17, 21, 28, 36, 13, 8, 5, 0, 61, 52, 14, 16, 30, 23, 9, 63, 65, 58, 12, 10, 15, 2, 55, 19, 7, 4, 3, 31, 43, 39, 48, 41, 22, 1, 62, 11, 6, 18, 49, 40, 38, 47, 44, 37, 29, 32, 34, 33, 20, 51, 46, 27, 26, 45, 60, 53, 64, 54, 59, 57, 56, 42, 25], 'cur_cost': 56563.0}, {'tour': [7, 21, 28, 23, 5, 27, 32, 31, 3, 4, 48, 39, 44, 2, 65, 18, 42, 43, 58, 59, 33, 25, 26, 41, 14, 53, 36, 47, 1, 40, 37, 50, 29, 38, 17, 49, 9, 11, 45, 20, 57, 6, 19, 13, 60, 55, 56, 51, 62, 15, 52, 54, 46, 8, 22, 30, 12, 10, 61, 63, 0, 35, 64, 24, 16, 34], 'cur_cost': 98232.0}, {'tour': array([37, 63,  9,  7, 17, 30,  6, 31, 13, 42, 16, 52, 55,  5, 61, 39, 60,
       57,  1, 12, 10, 18, 46, 27, 36,  3, 14, 34, 32, 65, 56, 19, 29, 54,
       51, 40, 48, 64,  0, 25, 58, 44, 23, 22, 45, 49, 38, 50, 28, 21, 15,
       24,  4, 53, 20,  8, 47, 41,  2, 43, 26, 11, 35, 33, 59, 62],
      dtype=int64), 'cur_cost': 94203.0}, {'tour': array([19, 17, 34, 64, 44, 60, 58, 13, 32,  3, 12, 25, 16, 57, 40, 59, 33,
       21, 56, 15, 27, 31, 54, 63, 41, 62,  7,  8, 38, 35, 65,  6, 50, 20,
        4,  2, 23, 10, 48, 39, 45,  1, 52, 47, 36, 49,  5, 28, 24, 11, 14,
       37, 43, 53, 29, 55, 46, 22,  9,  0, 51, 18, 61, 42, 30, 26],
      dtype=int64), 'cur_cost': 112181.0}, {'tour': array([32, 20, 36, 38, 43, 51, 14, 17, 27, 46, 47, 42, 34, 55,  8, 53, 35,
       24, 59, 60, 39,  3,  5, 33, 45,  4, 26, 19, 18,  7,  6, 65, 12, 30,
       22, 49,  1, 31, 41, 13, 57, 50, 44, 54, 48, 11, 61, 23, 28, 37,  2,
       21, 40, 10, 64, 63, 62, 25, 15, 29, 56, 16, 58, 52,  0,  9],
      dtype=int64), 'cur_cost': 94763.0}, {'tour': array([33,  1, 19, 16,  3,  7,  0, 20, 27, 25, 22, 53, 31,  5, 24, 26, 45,
       10, 18, 63, 39, 48, 42, 15, 32, 60, 46, 41, 52, 14, 35, 38, 58, 29,
       59, 47, 62,  8, 44, 49, 28, 57, 65, 36, 64, 12,  4, 56, 55, 11, 17,
       43, 23,  9,  6, 51, 34, 13, 21, 54, 30, 37, 50, 40, 61,  2],
      dtype=int64), 'cur_cost': 103189.0}, {'tour': array([11, 48, 14, 39, 52, 17, 29, 50, 65, 44,  5, 27, 21,  2, 10, 33, 45,
       37,  9, 25, 49, 42,  0, 20, 41, 58, 59, 51,  8, 64, 61, 47,  1, 63,
       53, 54, 28, 46, 36, 34, 23, 31, 40, 35, 15, 18,  4, 60, 12, 43, 30,
       56,  7, 13,  6, 55, 38, 16, 62, 32, 22, 24, 19, 57,  3, 26],
      dtype=int64), 'cur_cost': 109582.0}, {'tour': array([ 3, 35, 10, 50, 60,  6, 46, 48, 53, 28, 49, 58, 23, 40, 52,  7, 51,
       43, 31, 30, 41, 39, 38, 33, 55, 57, 64, 18, 56, 42, 54,  0, 44, 29,
       59, 22, 24, 21, 61, 14, 26, 11, 32, 20, 34,  2, 17, 36,  4,  9,  1,
       45, 13,  5, 47, 37, 63, 65, 19,  8, 25, 12, 27, 62, 15, 16],
      dtype=int64), 'cur_cost': 110185.0}, {'tour': array([21,  8, 11, 22, 31, 53, 47,  4, 59, 45, 51, 16, 39,  1, 46, 34, 32,
       38, 12, 60, 58, 62, 33, 18, 41, 19, 20, 54,  7, 14, 25, 40, 56,  5,
       30, 26, 50, 15, 48, 57, 61,  3, 65,  9, 28, 43, 17, 44, 36, 49, 42,
       24,  6, 13, 55,  0, 27, 23, 29, 52, 35, 64, 37, 10, 63,  2],
      dtype=int64), 'cur_cost': 104089.0}, {'tour': array([14, 53, 17, 65,  0, 18,  8, 31,  7, 40, 22,  1, 51, 10, 61, 41, 30,
       54, 60, 23, 63, 35,  9, 26, 47, 12, 19,  5, 15, 36, 33, 64, 49, 59,
       32, 62, 37,  2, 48, 38, 24, 50, 27, 52, 46, 20,  4, 55, 25, 45, 21,
       34, 11,  6, 39, 43, 56, 16, 58, 13,  3, 42, 44, 29, 57, 28],
      dtype=int64), 'cur_cost': 122553.0}, {'tour': array([34, 65, 27, 56, 35,  6, 18, 13, 63, 47, 60,  1, 45, 19, 28, 53,  8,
        3, 39, 52, 16, 17, 10, 43, 14, 57, 58, 36, 25, 31, 42, 21, 62, 50,
       29, 26, 20, 24, 46,  7, 30, 55,  4, 59, 41,  0, 40, 49, 22, 33, 44,
       23, 51,  9, 15,  2, 64, 11, 48, 54, 38, 12, 32,  5, 61, 37],
      dtype=int64), 'cur_cost': 114249.0}, {'tour': array([37, 27, 15, 60, 39, 53, 36, 35, 48, 30, 33, 31, 34, 22, 12, 17, 44,
       10,  5, 19, 41, 14, 62, 38, 18, 21, 52, 61, 54,  6, 56,  1, 59, 43,
       28, 11, 23,  0, 25, 26, 46, 49, 51,  2,  8, 29,  9, 47, 42, 13, 16,
        3, 20, 24, 45, 40,  7, 64, 55, 63, 32, 58, 57, 50, 65,  4],
      dtype=int64), 'cur_cost': 86322.0}, {'tour': array([11,  5, 62, 54, 24, 23, 27, 17, 14, 64, 33,  9, 65, 20, 30,  0, 61,
       15, 35,  8, 51,  4, 53, 55,  1,  6, 22, 43, 50,  3, 16, 29, 37, 38,
       26, 44, 49,  2, 25, 10, 32, 13, 42, 46, 63, 34, 52, 19, 56, 59, 21,
       45, 57, 36, 47, 28, 12, 60, 48,  7, 41, 40, 58, 31, 18, 39],
      dtype=int64), 'cur_cost': 107612.0}, {'tour': array([34, 51, 38, 16, 48,  0, 56, 39,  4, 58, 55, 40, 24, 31, 54,  2, 59,
       23, 35, 22, 65, 25, 46, 27, 64, 57, 63,  7, 28, 33, 49, 29, 36, 52,
       41, 15, 18, 10, 62, 14, 13, 30, 42, 21, 50, 47,  6, 60,  5, 61,  9,
       53, 12,  8, 11,  1, 45, 17, 19, 26, 37, 43, 20,  3, 44, 32],
      dtype=int64), 'cur_cost': 91777.0}, {'tour': array([13, 64, 22, 42, 10, 65,  5, 41,  9, 60,  8, 46, 49, 27, 48,  0, 50,
       56, 32, 12,  4, 57, 55, 35, 28, 36, 24, 29, 53,  2, 30, 44, 52, 26,
       38, 23, 62,  6, 11, 31, 20, 19, 58, 45, 34, 54, 40, 15, 43, 39, 37,
       63, 21,  1, 14, 16, 61, 33, 17, 25, 47,  7, 59, 18, 51,  3],
      dtype=int64), 'cur_cost': 110355.0}]
2025-08-03 17:05:24,109 - ExploitationExpert - INFO - 局部搜索耗时: 0.45秒
2025-08-03 17:05:24,109 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-03 17:05:24,109 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([37, 63,  9,  7, 17, 30,  6, 31, 13, 42, 16, 52, 55,  5, 61, 39, 60,
       57,  1, 12, 10, 18, 46, 27, 36,  3, 14, 34, 32, 65, 56, 19, 29, 54,
       51, 40, 48, 64,  0, 25, 58, 44, 23, 22, 45, 49, 38, 50, 28, 21, 15,
       24,  4, 53, 20,  8, 47, 41,  2, 43, 26, 11, 35, 33, 59, 62],
      dtype=int64), 'cur_cost': 94203.0}
2025-08-03 17:05:24,109 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 94203.00)
2025-08-03 17:05:24,109 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-03 17:05:24,109 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-03 17:05:24,110 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:24,115 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 17:05:24,116 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:24,117 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12411.0, 路径长度: 66
2025-08-03 17:05:24,117 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 7, 6, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12411.0}
2025-08-03 17:05:24,117 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 12411.00)
2025-08-03 17:05:24,117 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-03 17:05:24,117 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-03 17:05:24,118 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:24,122 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 17:05:24,122 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:24,122 - ExplorationExpert - INFO - 探索路径生成完成，成本: 111650.0, 路径长度: 66
2025-08-03 17:05:24,122 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [7, 21, 24, 18, 25, 60, 23, 3, 14, 37, 27, 31, 65, 36, 22, 53, 13, 19, 9, 39, 4, 62, 20, 6, 47, 55, 2, 29, 40, 46, 41, 59, 44, 34, 30, 64, 61, 48, 26, 16, 54, 11, 33, 58, 17, 1, 12, 38, 52, 51, 45, 49, 63, 28, 42, 15, 0, 8, 43, 56, 10, 35, 5, 50, 32, 57], 'cur_cost': 111650.0}
2025-08-03 17:05:24,122 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 111650.00)
2025-08-03 17:05:24,122 - experts.management.collaboration_manager - INFO - 为个体 10 生成利用路径
2025-08-03 17:05:24,122 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:05:24,122 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:05:24,123 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 10 处的路径，新成本: 102888.0
2025-08-03 17:05:24,194 - ExploitationExpert - INFO - res_population_num: 9
2025-08-03 17:05:24,194 - ExploitationExpert - INFO - res_population_costs: [9869.0, 9618.0, 9561.0, 9557, 9556, 9556, 9555, 9555, 9527]
2025-08-03 17:05:24,194 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 64, 53, 62, 59, 56,
       58, 60, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26, 36, 40, 43,
       48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59,
       56, 58, 60, 54, 57, 64, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62, 59, 56,
       60, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34,
       35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13,
       23, 16, 18, 17, 12, 22, 15, 14,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4, 14, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34,
       42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 43, 40, 49, 58, 60, 54,
       57, 64, 65, 52, 63, 61, 53, 62, 59, 56, 55,  6,  2,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26,
       36, 37, 27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43,
       48, 49, 47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64,
       65, 52, 63, 61, 53, 62, 59, 56, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26,
       36, 37, 27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43,
       48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64,
       65, 52, 63, 61, 53, 62, 59, 56, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 56, 59, 62, 53, 61, 63, 52, 65, 64,
       57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36, 37, 25,
       26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 56, 59, 62, 53, 61, 63, 52, 65, 64,
       57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49, 48, 43,
       40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36, 37, 25,
       26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 17:05:24,197 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:05:24,197 - ExploitationExpert - INFO - populations: [{'tour': [20, 6, 58, 7, 64, 3, 60, 52, 21, 32, 9, 10, 5, 19, 15, 11, 27, 31, 29, 12, 35, 36, 0, 22, 28, 16, 34, 8, 57, 1, 54, 23, 4, 61, 39, 40, 49, 51, 14, 48, 13, 17, 30, 46, 44, 26, 2, 55, 65, 18, 45, 37, 42, 50, 43, 38, 47, 41, 24, 56, 62, 59, 63, 53, 25, 33], 'cur_cost': 68630.0}, {'tour': array([19, 51, 20, 37, 23, 56, 45, 47, 59, 63, 46,  8, 16, 54, 33, 60, 39,
       29, 42, 14, 34, 27, 30, 13, 41, 44, 21, 32, 55, 38,  0, 57,  3,  7,
        9, 43, 28, 12, 17, 22,  5, 26, 48,  2, 35, 18, 31, 40, 58, 10, 50,
       15, 24, 49, 65, 52, 53, 62, 11, 64,  4, 36,  1, 61,  6, 25],
      dtype=int64), 'cur_cost': 99532.0}, {'tour': [0, 11, 4, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 3, 7, 1, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12417.0}, {'tour': [0, 3, 19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12369.0}, {'tour': array([ 9, 58, 30, 47,  4, 53, 42, 60, 46, 32, 17, 26, 31, 25, 65, 19, 41,
       57, 61, 38, 21, 13, 35, 62, 44, 55, 50, 64, 28, 34, 59, 54,  1,  6,
       45,  3, 29, 51, 48, 39, 56,  5, 37, 10, 33, 14, 18, 27, 43,  2,  8,
       20, 49, 12, 40, 15, 36,  7, 23, 63, 24, 22, 52,  0, 16, 11],
      dtype=int64), 'cur_cost': 107783.0}, {'tour': [50, 35, 24, 17, 21, 28, 36, 13, 8, 5, 0, 61, 52, 14, 16, 30, 23, 9, 63, 65, 58, 12, 10, 15, 2, 55, 19, 7, 4, 3, 31, 43, 39, 48, 41, 22, 1, 62, 11, 6, 18, 49, 40, 38, 47, 44, 37, 29, 32, 34, 33, 20, 51, 46, 27, 26, 45, 60, 53, 64, 54, 59, 57, 56, 42, 25], 'cur_cost': 56563.0}, {'tour': [7, 21, 28, 23, 5, 27, 32, 31, 3, 4, 48, 39, 44, 2, 65, 18, 42, 43, 58, 59, 33, 25, 26, 41, 14, 53, 36, 47, 1, 40, 37, 50, 29, 38, 17, 49, 9, 11, 45, 20, 57, 6, 19, 13, 60, 55, 56, 51, 62, 15, 52, 54, 46, 8, 22, 30, 12, 10, 61, 63, 0, 35, 64, 24, 16, 34], 'cur_cost': 98232.0}, {'tour': array([37, 63,  9,  7, 17, 30,  6, 31, 13, 42, 16, 52, 55,  5, 61, 39, 60,
       57,  1, 12, 10, 18, 46, 27, 36,  3, 14, 34, 32, 65, 56, 19, 29, 54,
       51, 40, 48, 64,  0, 25, 58, 44, 23, 22, 45, 49, 38, 50, 28, 21, 15,
       24,  4, 53, 20,  8, 47, 41,  2, 43, 26, 11, 35, 33, 59, 62],
      dtype=int64), 'cur_cost': 94203.0}, {'tour': [0, 7, 6, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12411.0}, {'tour': [7, 21, 24, 18, 25, 60, 23, 3, 14, 37, 27, 31, 65, 36, 22, 53, 13, 19, 9, 39, 4, 62, 20, 6, 47, 55, 2, 29, 40, 46, 41, 59, 44, 34, 30, 64, 61, 48, 26, 16, 54, 11, 33, 58, 17, 1, 12, 38, 52, 51, 45, 49, 63, 28, 42, 15, 0, 8, 43, 56, 10, 35, 5, 50, 32, 57], 'cur_cost': 111650.0}, {'tour': array([38, 55, 52,  1, 50,  4, 18, 60, 36, 25,  0, 49, 48, 28, 33, 45,  9,
       43, 13, 64, 41, 39, 19, 47, 46, 26, 31, 10, 27, 22, 35, 16,  8, 62,
        2, 42, 29,  6, 53, 17,  3,  5, 11, 23, 56, 14, 21, 34, 54, 57, 32,
       30, 51, 37, 59, 63, 24, 44, 12, 61, 15, 65,  7, 20, 40, 58],
      dtype=int64), 'cur_cost': 102888.0}, {'tour': array([11, 48, 14, 39, 52, 17, 29, 50, 65, 44,  5, 27, 21,  2, 10, 33, 45,
       37,  9, 25, 49, 42,  0, 20, 41, 58, 59, 51,  8, 64, 61, 47,  1, 63,
       53, 54, 28, 46, 36, 34, 23, 31, 40, 35, 15, 18,  4, 60, 12, 43, 30,
       56,  7, 13,  6, 55, 38, 16, 62, 32, 22, 24, 19, 57,  3, 26],
      dtype=int64), 'cur_cost': 109582.0}, {'tour': array([ 3, 35, 10, 50, 60,  6, 46, 48, 53, 28, 49, 58, 23, 40, 52,  7, 51,
       43, 31, 30, 41, 39, 38, 33, 55, 57, 64, 18, 56, 42, 54,  0, 44, 29,
       59, 22, 24, 21, 61, 14, 26, 11, 32, 20, 34,  2, 17, 36,  4,  9,  1,
       45, 13,  5, 47, 37, 63, 65, 19,  8, 25, 12, 27, 62, 15, 16],
      dtype=int64), 'cur_cost': 110185.0}, {'tour': array([21,  8, 11, 22, 31, 53, 47,  4, 59, 45, 51, 16, 39,  1, 46, 34, 32,
       38, 12, 60, 58, 62, 33, 18, 41, 19, 20, 54,  7, 14, 25, 40, 56,  5,
       30, 26, 50, 15, 48, 57, 61,  3, 65,  9, 28, 43, 17, 44, 36, 49, 42,
       24,  6, 13, 55,  0, 27, 23, 29, 52, 35, 64, 37, 10, 63,  2],
      dtype=int64), 'cur_cost': 104089.0}, {'tour': array([14, 53, 17, 65,  0, 18,  8, 31,  7, 40, 22,  1, 51, 10, 61, 41, 30,
       54, 60, 23, 63, 35,  9, 26, 47, 12, 19,  5, 15, 36, 33, 64, 49, 59,
       32, 62, 37,  2, 48, 38, 24, 50, 27, 52, 46, 20,  4, 55, 25, 45, 21,
       34, 11,  6, 39, 43, 56, 16, 58, 13,  3, 42, 44, 29, 57, 28],
      dtype=int64), 'cur_cost': 122553.0}, {'tour': array([34, 65, 27, 56, 35,  6, 18, 13, 63, 47, 60,  1, 45, 19, 28, 53,  8,
        3, 39, 52, 16, 17, 10, 43, 14, 57, 58, 36, 25, 31, 42, 21, 62, 50,
       29, 26, 20, 24, 46,  7, 30, 55,  4, 59, 41,  0, 40, 49, 22, 33, 44,
       23, 51,  9, 15,  2, 64, 11, 48, 54, 38, 12, 32,  5, 61, 37],
      dtype=int64), 'cur_cost': 114249.0}, {'tour': array([37, 27, 15, 60, 39, 53, 36, 35, 48, 30, 33, 31, 34, 22, 12, 17, 44,
       10,  5, 19, 41, 14, 62, 38, 18, 21, 52, 61, 54,  6, 56,  1, 59, 43,
       28, 11, 23,  0, 25, 26, 46, 49, 51,  2,  8, 29,  9, 47, 42, 13, 16,
        3, 20, 24, 45, 40,  7, 64, 55, 63, 32, 58, 57, 50, 65,  4],
      dtype=int64), 'cur_cost': 86322.0}, {'tour': array([11,  5, 62, 54, 24, 23, 27, 17, 14, 64, 33,  9, 65, 20, 30,  0, 61,
       15, 35,  8, 51,  4, 53, 55,  1,  6, 22, 43, 50,  3, 16, 29, 37, 38,
       26, 44, 49,  2, 25, 10, 32, 13, 42, 46, 63, 34, 52, 19, 56, 59, 21,
       45, 57, 36, 47, 28, 12, 60, 48,  7, 41, 40, 58, 31, 18, 39],
      dtype=int64), 'cur_cost': 107612.0}, {'tour': array([34, 51, 38, 16, 48,  0, 56, 39,  4, 58, 55, 40, 24, 31, 54,  2, 59,
       23, 35, 22, 65, 25, 46, 27, 64, 57, 63,  7, 28, 33, 49, 29, 36, 52,
       41, 15, 18, 10, 62, 14, 13, 30, 42, 21, 50, 47,  6, 60,  5, 61,  9,
       53, 12,  8, 11,  1, 45, 17, 19, 26, 37, 43, 20,  3, 44, 32],
      dtype=int64), 'cur_cost': 91777.0}, {'tour': array([13, 64, 22, 42, 10, 65,  5, 41,  9, 60,  8, 46, 49, 27, 48,  0, 50,
       56, 32, 12,  4, 57, 55, 35, 28, 36, 24, 29, 53,  2, 30, 44, 52, 26,
       38, 23, 62,  6, 11, 31, 20, 19, 58, 45, 34, 54, 40, 15, 43, 39, 37,
       63, 21,  1, 14, 16, 61, 33, 17, 25, 47,  7, 59, 18, 51,  3],
      dtype=int64), 'cur_cost': 110355.0}]
2025-08-03 17:05:24,201 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-08-03 17:05:24,201 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-03 17:05:24,202 - experts.management.collaboration_manager - INFO - 个体 10 利用路径生成报告: {'new_tour': array([38, 55, 52,  1, 50,  4, 18, 60, 36, 25,  0, 49, 48, 28, 33, 45,  9,
       43, 13, 64, 41, 39, 19, 47, 46, 26, 31, 10, 27, 22, 35, 16,  8, 62,
        2, 42, 29,  6, 53, 17,  3,  5, 11, 23, 56, 14, 21, 34, 54, 57, 32,
       30, 51, 37, 59, 63, 24, 44, 12, 61, 15, 65,  7, 20, 40, 58],
      dtype=int64), 'cur_cost': 102888.0}
2025-08-03 17:05:24,202 - experts.management.collaboration_manager - INFO - 个体 10 接受新路径 (成本: 102888.00)
2025-08-03 17:05:24,202 - experts.management.collaboration_manager - INFO - 为个体 11 生成探索路径
2025-08-03 17:05:24,202 - ExplorationExpert - INFO - 开始为个体 11 生成探索路径（算法实现）
2025-08-03 17:05:24,202 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:24,206 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 17:05:24,206 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:24,206 - ExplorationExpert - INFO - 探索路径生成完成，成本: 102799.0, 路径长度: 66
2025-08-03 17:05:24,206 - experts.management.collaboration_manager - INFO - 个体 11 探索路径生成报告: {'new_tour': [50, 3, 21, 28, 16, 27, 48, 35, 15, 9, 26, 53, 36, 40, 23, 4, 39, 64, 2, 6, 33, 37, 29, 65, 54, 42, 43, 0, 24, 14, 34, 55, 18, 11, 56, 5, 20, 12, 60, 13, 10, 32, 52, 30, 49, 61, 62, 57, 8, 51, 58, 38, 44, 17, 59, 1, 46, 41, 45, 47, 31, 25, 63, 19, 7, 22], 'cur_cost': 102799.0}
2025-08-03 17:05:24,206 - experts.management.collaboration_manager - INFO - 个体 11 接受新路径 (成本: 102799.00)
2025-08-03 17:05:24,206 - experts.management.collaboration_manager - INFO - 为个体 12 生成探索路径
2025-08-03 17:05:24,206 - ExplorationExpert - INFO - 开始为个体 12 生成探索路径（算法实现）
2025-08-03 17:05:24,207 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:24,211 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 17:05:24,211 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:24,211 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12860.0, 路径长度: 66
2025-08-03 17:05:24,211 - experts.management.collaboration_manager - INFO - 个体 12 探索路径生成报告: {'new_tour': [0, 11, 16, 10, 8, 2, 6, 4, 5, 9, 3, 7, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12860.0}
2025-08-03 17:05:24,211 - experts.management.collaboration_manager - INFO - 个体 12 接受新路径 (成本: 12860.00)
2025-08-03 17:05:24,211 - experts.management.collaboration_manager - INFO - 为个体 13 生成利用路径
2025-08-03 17:05:24,211 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:05:24,212 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:05:24,212 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 13 处的路径，新成本: 102979.0
2025-08-03 17:05:24,280 - ExploitationExpert - INFO - res_population_num: 10
2025-08-03 17:05:24,281 - ExploitationExpert - INFO - res_population_costs: [9869.0, 9618.0, 9561.0, 9557, 9556, 9556, 9555, 9555, 9527, 9527]
2025-08-03 17:05:24,282 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 64, 53, 62, 59, 56,
       58, 60, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26, 36, 40, 43,
       48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59,
       56, 58, 60, 54, 57, 64, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62, 59, 56,
       60, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34,
       35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13,
       23, 16, 18, 17, 12, 22, 15, 14,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4, 14, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34,
       42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 43, 40, 49, 58, 60, 54,
       57, 64, 65, 52, 63, 61, 53, 62, 59, 56, 55,  6,  2,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26,
       36, 37, 27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43,
       48, 49, 47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64,
       65, 52, 63, 61, 53, 62, 59, 56, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26,
       36, 37, 27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43,
       48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64,
       65, 52, 63, 61, 53, 62, 59, 56, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 56, 59, 62, 53, 61, 63, 52, 65, 64,
       57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36, 37, 25,
       26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 56, 59, 62, 53, 61, 63, 52, 65, 64,
       57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49, 48, 43,
       40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36, 37, 25,
       26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-03 17:05:24,286 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:05:24,286 - ExploitationExpert - INFO - populations: [{'tour': [20, 6, 58, 7, 64, 3, 60, 52, 21, 32, 9, 10, 5, 19, 15, 11, 27, 31, 29, 12, 35, 36, 0, 22, 28, 16, 34, 8, 57, 1, 54, 23, 4, 61, 39, 40, 49, 51, 14, 48, 13, 17, 30, 46, 44, 26, 2, 55, 65, 18, 45, 37, 42, 50, 43, 38, 47, 41, 24, 56, 62, 59, 63, 53, 25, 33], 'cur_cost': 68630.0}, {'tour': array([19, 51, 20, 37, 23, 56, 45, 47, 59, 63, 46,  8, 16, 54, 33, 60, 39,
       29, 42, 14, 34, 27, 30, 13, 41, 44, 21, 32, 55, 38,  0, 57,  3,  7,
        9, 43, 28, 12, 17, 22,  5, 26, 48,  2, 35, 18, 31, 40, 58, 10, 50,
       15, 24, 49, 65, 52, 53, 62, 11, 64,  4, 36,  1, 61,  6, 25],
      dtype=int64), 'cur_cost': 99532.0}, {'tour': [0, 11, 4, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 3, 7, 1, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12417.0}, {'tour': [0, 3, 19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12369.0}, {'tour': array([ 9, 58, 30, 47,  4, 53, 42, 60, 46, 32, 17, 26, 31, 25, 65, 19, 41,
       57, 61, 38, 21, 13, 35, 62, 44, 55, 50, 64, 28, 34, 59, 54,  1,  6,
       45,  3, 29, 51, 48, 39, 56,  5, 37, 10, 33, 14, 18, 27, 43,  2,  8,
       20, 49, 12, 40, 15, 36,  7, 23, 63, 24, 22, 52,  0, 16, 11],
      dtype=int64), 'cur_cost': 107783.0}, {'tour': [50, 35, 24, 17, 21, 28, 36, 13, 8, 5, 0, 61, 52, 14, 16, 30, 23, 9, 63, 65, 58, 12, 10, 15, 2, 55, 19, 7, 4, 3, 31, 43, 39, 48, 41, 22, 1, 62, 11, 6, 18, 49, 40, 38, 47, 44, 37, 29, 32, 34, 33, 20, 51, 46, 27, 26, 45, 60, 53, 64, 54, 59, 57, 56, 42, 25], 'cur_cost': 56563.0}, {'tour': [7, 21, 28, 23, 5, 27, 32, 31, 3, 4, 48, 39, 44, 2, 65, 18, 42, 43, 58, 59, 33, 25, 26, 41, 14, 53, 36, 47, 1, 40, 37, 50, 29, 38, 17, 49, 9, 11, 45, 20, 57, 6, 19, 13, 60, 55, 56, 51, 62, 15, 52, 54, 46, 8, 22, 30, 12, 10, 61, 63, 0, 35, 64, 24, 16, 34], 'cur_cost': 98232.0}, {'tour': array([37, 63,  9,  7, 17, 30,  6, 31, 13, 42, 16, 52, 55,  5, 61, 39, 60,
       57,  1, 12, 10, 18, 46, 27, 36,  3, 14, 34, 32, 65, 56, 19, 29, 54,
       51, 40, 48, 64,  0, 25, 58, 44, 23, 22, 45, 49, 38, 50, 28, 21, 15,
       24,  4, 53, 20,  8, 47, 41,  2, 43, 26, 11, 35, 33, 59, 62],
      dtype=int64), 'cur_cost': 94203.0}, {'tour': [0, 7, 6, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12411.0}, {'tour': [7, 21, 24, 18, 25, 60, 23, 3, 14, 37, 27, 31, 65, 36, 22, 53, 13, 19, 9, 39, 4, 62, 20, 6, 47, 55, 2, 29, 40, 46, 41, 59, 44, 34, 30, 64, 61, 48, 26, 16, 54, 11, 33, 58, 17, 1, 12, 38, 52, 51, 45, 49, 63, 28, 42, 15, 0, 8, 43, 56, 10, 35, 5, 50, 32, 57], 'cur_cost': 111650.0}, {'tour': array([38, 55, 52,  1, 50,  4, 18, 60, 36, 25,  0, 49, 48, 28, 33, 45,  9,
       43, 13, 64, 41, 39, 19, 47, 46, 26, 31, 10, 27, 22, 35, 16,  8, 62,
        2, 42, 29,  6, 53, 17,  3,  5, 11, 23, 56, 14, 21, 34, 54, 57, 32,
       30, 51, 37, 59, 63, 24, 44, 12, 61, 15, 65,  7, 20, 40, 58],
      dtype=int64), 'cur_cost': 102888.0}, {'tour': [50, 3, 21, 28, 16, 27, 48, 35, 15, 9, 26, 53, 36, 40, 23, 4, 39, 64, 2, 6, 33, 37, 29, 65, 54, 42, 43, 0, 24, 14, 34, 55, 18, 11, 56, 5, 20, 12, 60, 13, 10, 32, 52, 30, 49, 61, 62, 57, 8, 51, 58, 38, 44, 17, 59, 1, 46, 41, 45, 47, 31, 25, 63, 19, 7, 22], 'cur_cost': 102799.0}, {'tour': [0, 11, 16, 10, 8, 2, 6, 4, 5, 9, 3, 7, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12860.0}, {'tour': array([26, 24, 63, 60, 61, 10, 42, 54,  4, 16, 15, 23, 56, 58,  5, 57,  9,
       49, 36, 59, 22,  3, 29, 17, 18, 48, 53, 34, 45, 65, 27, 39, 43, 14,
       55, 31, 37, 11,  6, 46, 12, 33, 25, 13,  2,  0, 20, 41, 35, 30, 47,
        8, 62, 32, 38,  7, 28, 50, 40, 52, 51, 64, 21,  1, 19, 44],
      dtype=int64), 'cur_cost': 102979.0}, {'tour': array([14, 53, 17, 65,  0, 18,  8, 31,  7, 40, 22,  1, 51, 10, 61, 41, 30,
       54, 60, 23, 63, 35,  9, 26, 47, 12, 19,  5, 15, 36, 33, 64, 49, 59,
       32, 62, 37,  2, 48, 38, 24, 50, 27, 52, 46, 20,  4, 55, 25, 45, 21,
       34, 11,  6, 39, 43, 56, 16, 58, 13,  3, 42, 44, 29, 57, 28],
      dtype=int64), 'cur_cost': 122553.0}, {'tour': array([34, 65, 27, 56, 35,  6, 18, 13, 63, 47, 60,  1, 45, 19, 28, 53,  8,
        3, 39, 52, 16, 17, 10, 43, 14, 57, 58, 36, 25, 31, 42, 21, 62, 50,
       29, 26, 20, 24, 46,  7, 30, 55,  4, 59, 41,  0, 40, 49, 22, 33, 44,
       23, 51,  9, 15,  2, 64, 11, 48, 54, 38, 12, 32,  5, 61, 37],
      dtype=int64), 'cur_cost': 114249.0}, {'tour': array([37, 27, 15, 60, 39, 53, 36, 35, 48, 30, 33, 31, 34, 22, 12, 17, 44,
       10,  5, 19, 41, 14, 62, 38, 18, 21, 52, 61, 54,  6, 56,  1, 59, 43,
       28, 11, 23,  0, 25, 26, 46, 49, 51,  2,  8, 29,  9, 47, 42, 13, 16,
        3, 20, 24, 45, 40,  7, 64, 55, 63, 32, 58, 57, 50, 65,  4],
      dtype=int64), 'cur_cost': 86322.0}, {'tour': array([11,  5, 62, 54, 24, 23, 27, 17, 14, 64, 33,  9, 65, 20, 30,  0, 61,
       15, 35,  8, 51,  4, 53, 55,  1,  6, 22, 43, 50,  3, 16, 29, 37, 38,
       26, 44, 49,  2, 25, 10, 32, 13, 42, 46, 63, 34, 52, 19, 56, 59, 21,
       45, 57, 36, 47, 28, 12, 60, 48,  7, 41, 40, 58, 31, 18, 39],
      dtype=int64), 'cur_cost': 107612.0}, {'tour': array([34, 51, 38, 16, 48,  0, 56, 39,  4, 58, 55, 40, 24, 31, 54,  2, 59,
       23, 35, 22, 65, 25, 46, 27, 64, 57, 63,  7, 28, 33, 49, 29, 36, 52,
       41, 15, 18, 10, 62, 14, 13, 30, 42, 21, 50, 47,  6, 60,  5, 61,  9,
       53, 12,  8, 11,  1, 45, 17, 19, 26, 37, 43, 20,  3, 44, 32],
      dtype=int64), 'cur_cost': 91777.0}, {'tour': array([13, 64, 22, 42, 10, 65,  5, 41,  9, 60,  8, 46, 49, 27, 48,  0, 50,
       56, 32, 12,  4, 57, 55, 35, 28, 36, 24, 29, 53,  2, 30, 44, 52, 26,
       38, 23, 62,  6, 11, 31, 20, 19, 58, 45, 34, 54, 40, 15, 43, 39, 37,
       63, 21,  1, 14, 16, 61, 33, 17, 25, 47,  7, 59, 18, 51,  3],
      dtype=int64), 'cur_cost': 110355.0}]
2025-08-03 17:05:24,290 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-08-03 17:05:24,290 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-03 17:05:24,290 - experts.management.collaboration_manager - INFO - 个体 13 利用路径生成报告: {'new_tour': array([26, 24, 63, 60, 61, 10, 42, 54,  4, 16, 15, 23, 56, 58,  5, 57,  9,
       49, 36, 59, 22,  3, 29, 17, 18, 48, 53, 34, 45, 65, 27, 39, 43, 14,
       55, 31, 37, 11,  6, 46, 12, 33, 25, 13,  2,  0, 20, 41, 35, 30, 47,
        8, 62, 32, 38,  7, 28, 50, 40, 52, 51, 64, 21,  1, 19, 44],
      dtype=int64), 'cur_cost': 102979.0}
2025-08-03 17:05:24,290 - experts.management.collaboration_manager - INFO - 个体 13 接受新路径 (成本: 102979.00)
2025-08-03 17:05:24,290 - experts.management.collaboration_manager - INFO - 为个体 14 生成探索路径
2025-08-03 17:05:24,290 - ExplorationExpert - INFO - 开始为个体 14 生成探索路径（算法实现）
2025-08-03 17:05:24,291 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:24,302 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 17:05:24,303 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:24,303 - ExplorationExpert - INFO - 探索路径生成完成，成本: 55687.0, 路径长度: 66
2025-08-03 17:05:24,303 - experts.management.collaboration_manager - INFO - 个体 14 探索路径生成报告: {'new_tour': [25, 32, 11, 52, 3, 65, 10, 18, 23, 4, 17, 34, 30, 24, 31, 22, 36, 2, 6, 59, 15, 5, 61, 0, 8, 56, 7, 1, 12, 47, 42, 14, 21, 33, 43, 44, 46, 49, 41, 13, 16, 28, 19, 40, 38, 50, 51, 48, 37, 29, 27, 20, 58, 60, 64, 54, 62, 57, 53, 55, 39, 26, 35, 9, 63, 45], 'cur_cost': 55687.0}
2025-08-03 17:05:24,303 - experts.management.collaboration_manager - INFO - 个体 14 接受新路径 (成本: 55687.00)
2025-08-03 17:05:24,303 - experts.management.collaboration_manager - INFO - 为个体 15 生成探索路径
2025-08-03 17:05:24,303 - ExplorationExpert - INFO - 开始为个体 15 生成探索路径（算法实现）
2025-08-03 17:05:24,303 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:24,307 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 17:05:24,307 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:24,307 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12915.0, 路径长度: 66
2025-08-03 17:05:24,307 - experts.management.collaboration_manager - INFO - 个体 15 探索路径生成报告: {'new_tour': [0, 21, 2, 6, 4, 5, 8, 10, 1, 7, 3, 9, 11, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12915.0}
2025-08-03 17:05:24,307 - experts.management.collaboration_manager - INFO - 个体 15 接受新路径 (成本: 12915.00)
2025-08-03 17:05:24,307 - experts.management.collaboration_manager - INFO - 为个体 16 生成利用路径
2025-08-03 17:05:24,307 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:05:24,308 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:05:24,308 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 16 处的路径，新成本: 103838.0
2025-08-03 17:05:24,377 - ExploitationExpert - INFO - res_population_num: 10
2025-08-03 17:05:24,377 - ExploitationExpert - INFO - res_population_costs: [9869.0, 9618.0, 9561.0, 9557, 9556, 9556, 9555, 9555, 9527, 9527]
2025-08-03 17:05:24,377 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 64, 53, 62, 59, 56,
       58, 60, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26, 36, 40, 43,
       48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59,
       56, 58, 60, 54, 57, 64, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62, 59, 56,
       60, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34,
       35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13,
       23, 16, 18, 17, 12, 22, 15, 14,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4, 14, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34,
       42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 43, 40, 49, 58, 60, 54,
       57, 64, 65, 52, 63, 61, 53, 62, 59, 56, 55,  6,  2,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26,
       36, 37, 27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43,
       48, 49, 47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64,
       65, 52, 63, 61, 53, 62, 59, 56, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26,
       36, 37, 27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43,
       48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64,
       65, 52, 63, 61, 53, 62, 59, 56, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 56, 59, 62, 53, 61, 63, 52, 65, 64,
       57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36, 37, 25,
       26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 56, 59, 62, 53, 61, 63, 52, 65, 64,
       57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49, 48, 43,
       40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36, 37, 25,
       26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-03 17:05:24,381 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:05:24,382 - ExploitationExpert - INFO - populations: [{'tour': [20, 6, 58, 7, 64, 3, 60, 52, 21, 32, 9, 10, 5, 19, 15, 11, 27, 31, 29, 12, 35, 36, 0, 22, 28, 16, 34, 8, 57, 1, 54, 23, 4, 61, 39, 40, 49, 51, 14, 48, 13, 17, 30, 46, 44, 26, 2, 55, 65, 18, 45, 37, 42, 50, 43, 38, 47, 41, 24, 56, 62, 59, 63, 53, 25, 33], 'cur_cost': 68630.0}, {'tour': array([19, 51, 20, 37, 23, 56, 45, 47, 59, 63, 46,  8, 16, 54, 33, 60, 39,
       29, 42, 14, 34, 27, 30, 13, 41, 44, 21, 32, 55, 38,  0, 57,  3,  7,
        9, 43, 28, 12, 17, 22,  5, 26, 48,  2, 35, 18, 31, 40, 58, 10, 50,
       15, 24, 49, 65, 52, 53, 62, 11, 64,  4, 36,  1, 61,  6, 25],
      dtype=int64), 'cur_cost': 99532.0}, {'tour': [0, 11, 4, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 3, 7, 1, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12417.0}, {'tour': [0, 3, 19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12369.0}, {'tour': array([ 9, 58, 30, 47,  4, 53, 42, 60, 46, 32, 17, 26, 31, 25, 65, 19, 41,
       57, 61, 38, 21, 13, 35, 62, 44, 55, 50, 64, 28, 34, 59, 54,  1,  6,
       45,  3, 29, 51, 48, 39, 56,  5, 37, 10, 33, 14, 18, 27, 43,  2,  8,
       20, 49, 12, 40, 15, 36,  7, 23, 63, 24, 22, 52,  0, 16, 11],
      dtype=int64), 'cur_cost': 107783.0}, {'tour': [50, 35, 24, 17, 21, 28, 36, 13, 8, 5, 0, 61, 52, 14, 16, 30, 23, 9, 63, 65, 58, 12, 10, 15, 2, 55, 19, 7, 4, 3, 31, 43, 39, 48, 41, 22, 1, 62, 11, 6, 18, 49, 40, 38, 47, 44, 37, 29, 32, 34, 33, 20, 51, 46, 27, 26, 45, 60, 53, 64, 54, 59, 57, 56, 42, 25], 'cur_cost': 56563.0}, {'tour': [7, 21, 28, 23, 5, 27, 32, 31, 3, 4, 48, 39, 44, 2, 65, 18, 42, 43, 58, 59, 33, 25, 26, 41, 14, 53, 36, 47, 1, 40, 37, 50, 29, 38, 17, 49, 9, 11, 45, 20, 57, 6, 19, 13, 60, 55, 56, 51, 62, 15, 52, 54, 46, 8, 22, 30, 12, 10, 61, 63, 0, 35, 64, 24, 16, 34], 'cur_cost': 98232.0}, {'tour': array([37, 63,  9,  7, 17, 30,  6, 31, 13, 42, 16, 52, 55,  5, 61, 39, 60,
       57,  1, 12, 10, 18, 46, 27, 36,  3, 14, 34, 32, 65, 56, 19, 29, 54,
       51, 40, 48, 64,  0, 25, 58, 44, 23, 22, 45, 49, 38, 50, 28, 21, 15,
       24,  4, 53, 20,  8, 47, 41,  2, 43, 26, 11, 35, 33, 59, 62],
      dtype=int64), 'cur_cost': 94203.0}, {'tour': [0, 7, 6, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12411.0}, {'tour': [7, 21, 24, 18, 25, 60, 23, 3, 14, 37, 27, 31, 65, 36, 22, 53, 13, 19, 9, 39, 4, 62, 20, 6, 47, 55, 2, 29, 40, 46, 41, 59, 44, 34, 30, 64, 61, 48, 26, 16, 54, 11, 33, 58, 17, 1, 12, 38, 52, 51, 45, 49, 63, 28, 42, 15, 0, 8, 43, 56, 10, 35, 5, 50, 32, 57], 'cur_cost': 111650.0}, {'tour': array([38, 55, 52,  1, 50,  4, 18, 60, 36, 25,  0, 49, 48, 28, 33, 45,  9,
       43, 13, 64, 41, 39, 19, 47, 46, 26, 31, 10, 27, 22, 35, 16,  8, 62,
        2, 42, 29,  6, 53, 17,  3,  5, 11, 23, 56, 14, 21, 34, 54, 57, 32,
       30, 51, 37, 59, 63, 24, 44, 12, 61, 15, 65,  7, 20, 40, 58],
      dtype=int64), 'cur_cost': 102888.0}, {'tour': [50, 3, 21, 28, 16, 27, 48, 35, 15, 9, 26, 53, 36, 40, 23, 4, 39, 64, 2, 6, 33, 37, 29, 65, 54, 42, 43, 0, 24, 14, 34, 55, 18, 11, 56, 5, 20, 12, 60, 13, 10, 32, 52, 30, 49, 61, 62, 57, 8, 51, 58, 38, 44, 17, 59, 1, 46, 41, 45, 47, 31, 25, 63, 19, 7, 22], 'cur_cost': 102799.0}, {'tour': [0, 11, 16, 10, 8, 2, 6, 4, 5, 9, 3, 7, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12860.0}, {'tour': array([26, 24, 63, 60, 61, 10, 42, 54,  4, 16, 15, 23, 56, 58,  5, 57,  9,
       49, 36, 59, 22,  3, 29, 17, 18, 48, 53, 34, 45, 65, 27, 39, 43, 14,
       55, 31, 37, 11,  6, 46, 12, 33, 25, 13,  2,  0, 20, 41, 35, 30, 47,
        8, 62, 32, 38,  7, 28, 50, 40, 52, 51, 64, 21,  1, 19, 44],
      dtype=int64), 'cur_cost': 102979.0}, {'tour': [25, 32, 11, 52, 3, 65, 10, 18, 23, 4, 17, 34, 30, 24, 31, 22, 36, 2, 6, 59, 15, 5, 61, 0, 8, 56, 7, 1, 12, 47, 42, 14, 21, 33, 43, 44, 46, 49, 41, 13, 16, 28, 19, 40, 38, 50, 51, 48, 37, 29, 27, 20, 58, 60, 64, 54, 62, 57, 53, 55, 39, 26, 35, 9, 63, 45], 'cur_cost': 55687.0}, {'tour': [0, 21, 2, 6, 4, 5, 8, 10, 1, 7, 3, 9, 11, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12915.0}, {'tour': array([29, 32,  7, 44, 53, 16, 43,  4, 47, 62, 30, 40, 31, 17, 42, 38, 39,
       41, 11, 37, 13, 23, 56, 24,  6, 27, 22, 60, 58, 45,  0, 54, 49,  2,
       28, 50, 36, 20,  1, 10,  3, 34, 61, 57, 19,  9,  8, 18, 26, 48, 14,
       52, 12, 46, 64, 63, 15, 55,  5, 65, 21, 59, 51, 33, 25, 35],
      dtype=int64), 'cur_cost': 103838.0}, {'tour': array([11,  5, 62, 54, 24, 23, 27, 17, 14, 64, 33,  9, 65, 20, 30,  0, 61,
       15, 35,  8, 51,  4, 53, 55,  1,  6, 22, 43, 50,  3, 16, 29, 37, 38,
       26, 44, 49,  2, 25, 10, 32, 13, 42, 46, 63, 34, 52, 19, 56, 59, 21,
       45, 57, 36, 47, 28, 12, 60, 48,  7, 41, 40, 58, 31, 18, 39],
      dtype=int64), 'cur_cost': 107612.0}, {'tour': array([34, 51, 38, 16, 48,  0, 56, 39,  4, 58, 55, 40, 24, 31, 54,  2, 59,
       23, 35, 22, 65, 25, 46, 27, 64, 57, 63,  7, 28, 33, 49, 29, 36, 52,
       41, 15, 18, 10, 62, 14, 13, 30, 42, 21, 50, 47,  6, 60,  5, 61,  9,
       53, 12,  8, 11,  1, 45, 17, 19, 26, 37, 43, 20,  3, 44, 32],
      dtype=int64), 'cur_cost': 91777.0}, {'tour': array([13, 64, 22, 42, 10, 65,  5, 41,  9, 60,  8, 46, 49, 27, 48,  0, 50,
       56, 32, 12,  4, 57, 55, 35, 28, 36, 24, 29, 53,  2, 30, 44, 52, 26,
       38, 23, 62,  6, 11, 31, 20, 19, 58, 45, 34, 54, 40, 15, 43, 39, 37,
       63, 21,  1, 14, 16, 61, 33, 17, 25, 47,  7, 59, 18, 51,  3],
      dtype=int64), 'cur_cost': 110355.0}]
2025-08-03 17:05:24,386 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-08-03 17:05:24,386 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-03 17:05:24,386 - experts.management.collaboration_manager - INFO - 个体 16 利用路径生成报告: {'new_tour': array([29, 32,  7, 44, 53, 16, 43,  4, 47, 62, 30, 40, 31, 17, 42, 38, 39,
       41, 11, 37, 13, 23, 56, 24,  6, 27, 22, 60, 58, 45,  0, 54, 49,  2,
       28, 50, 36, 20,  1, 10,  3, 34, 61, 57, 19,  9,  8, 18, 26, 48, 14,
       52, 12, 46, 64, 63, 15, 55,  5, 65, 21, 59, 51, 33, 25, 35],
      dtype=int64), 'cur_cost': 103838.0}
2025-08-03 17:05:24,386 - experts.management.collaboration_manager - INFO - 个体 16 接受新路径 (成本: 103838.00)
2025-08-03 17:05:24,386 - experts.management.collaboration_manager - INFO - 为个体 17 生成探索路径
2025-08-03 17:05:24,386 - ExplorationExpert - INFO - 开始为个体 17 生成探索路径（算法实现）
2025-08-03 17:05:24,387 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:24,398 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 17:05:24,398 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:24,398 - ExplorationExpert - INFO - 探索路径生成完成，成本: 63789.0, 路径长度: 66
2025-08-03 17:05:24,398 - experts.management.collaboration_manager - INFO - 个体 17 探索路径生成报告: {'new_tour': [19, 37, 31, 18, 4, 2, 53, 15, 28, 35, 13, 22, 14, 24, 10, 0, 60, 56, 23, 8, 63, 64, 52, 9, 1, 7, 58, 40, 48, 42, 12, 5, 17, 27, 6, 16, 26, 29, 20, 36, 49, 39, 25, 30, 47, 50, 51, 21, 3, 55, 65, 62, 11, 33, 46, 43, 45, 34, 41, 32, 38, 44, 59, 61, 57, 54], 'cur_cost': 63789.0}
2025-08-03 17:05:24,398 - experts.management.collaboration_manager - INFO - 个体 17 接受新路径 (成本: 63789.00)
2025-08-03 17:05:24,398 - experts.management.collaboration_manager - INFO - 为个体 18 生成探索路径
2025-08-03 17:05:24,398 - ExplorationExpert - INFO - 开始为个体 18 生成探索路径（算法实现）
2025-08-03 17:05:24,398 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:24,402 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 17:05:24,402 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:24,402 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12338.0, 路径长度: 66
2025-08-03 17:05:24,402 - experts.management.collaboration_manager - INFO - 个体 18 探索路径生成报告: {'new_tour': [0, 3, 1, 12, 22, 23, 16, 18, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12338.0}
2025-08-03 17:05:24,402 - experts.management.collaboration_manager - INFO - 个体 18 接受新路径 (成本: 12338.00)
2025-08-03 17:05:24,402 - experts.management.collaboration_manager - INFO - 为个体 19 生成利用路径
2025-08-03 17:05:24,402 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:05:24,403 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:05:24,403 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 19 处的路径，新成本: 114542.0
2025-08-03 17:05:24,466 - ExploitationExpert - INFO - res_population_num: 10
2025-08-03 17:05:24,466 - ExploitationExpert - INFO - res_population_costs: [9869.0, 9618.0, 9561.0, 9557, 9556, 9556, 9555, 9555, 9527, 9527]
2025-08-03 17:05:24,466 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 64, 53, 62, 59, 56,
       58, 60, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26, 36, 40, 43,
       48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59,
       56, 58, 60, 54, 57, 64, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62, 59, 56,
       60, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34,
       35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13,
       23, 16, 18, 17, 12, 22, 15, 14,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4, 14, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34,
       42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 43, 40, 49, 58, 60, 54,
       57, 64, 65, 52, 63, 61, 53, 62, 59, 56, 55,  6,  2,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26,
       36, 37, 27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43,
       48, 49, 47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64,
       65, 52, 63, 61, 53, 62, 59, 56, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26,
       36, 37, 27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43,
       48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64,
       65, 52, 63, 61, 53, 62, 59, 56, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 56, 59, 62, 53, 61, 63, 52, 65, 64,
       57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36, 37, 25,
       26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 56, 59, 62, 53, 61, 63, 52, 65, 64,
       57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49, 48, 43,
       40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36, 37, 25,
       26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-03 17:05:24,469 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:05:24,469 - ExploitationExpert - INFO - populations: [{'tour': [20, 6, 58, 7, 64, 3, 60, 52, 21, 32, 9, 10, 5, 19, 15, 11, 27, 31, 29, 12, 35, 36, 0, 22, 28, 16, 34, 8, 57, 1, 54, 23, 4, 61, 39, 40, 49, 51, 14, 48, 13, 17, 30, 46, 44, 26, 2, 55, 65, 18, 45, 37, 42, 50, 43, 38, 47, 41, 24, 56, 62, 59, 63, 53, 25, 33], 'cur_cost': 68630.0}, {'tour': array([19, 51, 20, 37, 23, 56, 45, 47, 59, 63, 46,  8, 16, 54, 33, 60, 39,
       29, 42, 14, 34, 27, 30, 13, 41, 44, 21, 32, 55, 38,  0, 57,  3,  7,
        9, 43, 28, 12, 17, 22,  5, 26, 48,  2, 35, 18, 31, 40, 58, 10, 50,
       15, 24, 49, 65, 52, 53, 62, 11, 64,  4, 36,  1, 61,  6, 25],
      dtype=int64), 'cur_cost': 99532.0}, {'tour': [0, 11, 4, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 3, 7, 1, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12417.0}, {'tour': [0, 3, 19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12369.0}, {'tour': array([ 9, 58, 30, 47,  4, 53, 42, 60, 46, 32, 17, 26, 31, 25, 65, 19, 41,
       57, 61, 38, 21, 13, 35, 62, 44, 55, 50, 64, 28, 34, 59, 54,  1,  6,
       45,  3, 29, 51, 48, 39, 56,  5, 37, 10, 33, 14, 18, 27, 43,  2,  8,
       20, 49, 12, 40, 15, 36,  7, 23, 63, 24, 22, 52,  0, 16, 11],
      dtype=int64), 'cur_cost': 107783.0}, {'tour': [50, 35, 24, 17, 21, 28, 36, 13, 8, 5, 0, 61, 52, 14, 16, 30, 23, 9, 63, 65, 58, 12, 10, 15, 2, 55, 19, 7, 4, 3, 31, 43, 39, 48, 41, 22, 1, 62, 11, 6, 18, 49, 40, 38, 47, 44, 37, 29, 32, 34, 33, 20, 51, 46, 27, 26, 45, 60, 53, 64, 54, 59, 57, 56, 42, 25], 'cur_cost': 56563.0}, {'tour': [7, 21, 28, 23, 5, 27, 32, 31, 3, 4, 48, 39, 44, 2, 65, 18, 42, 43, 58, 59, 33, 25, 26, 41, 14, 53, 36, 47, 1, 40, 37, 50, 29, 38, 17, 49, 9, 11, 45, 20, 57, 6, 19, 13, 60, 55, 56, 51, 62, 15, 52, 54, 46, 8, 22, 30, 12, 10, 61, 63, 0, 35, 64, 24, 16, 34], 'cur_cost': 98232.0}, {'tour': array([37, 63,  9,  7, 17, 30,  6, 31, 13, 42, 16, 52, 55,  5, 61, 39, 60,
       57,  1, 12, 10, 18, 46, 27, 36,  3, 14, 34, 32, 65, 56, 19, 29, 54,
       51, 40, 48, 64,  0, 25, 58, 44, 23, 22, 45, 49, 38, 50, 28, 21, 15,
       24,  4, 53, 20,  8, 47, 41,  2, 43, 26, 11, 35, 33, 59, 62],
      dtype=int64), 'cur_cost': 94203.0}, {'tour': [0, 7, 6, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12411.0}, {'tour': [7, 21, 24, 18, 25, 60, 23, 3, 14, 37, 27, 31, 65, 36, 22, 53, 13, 19, 9, 39, 4, 62, 20, 6, 47, 55, 2, 29, 40, 46, 41, 59, 44, 34, 30, 64, 61, 48, 26, 16, 54, 11, 33, 58, 17, 1, 12, 38, 52, 51, 45, 49, 63, 28, 42, 15, 0, 8, 43, 56, 10, 35, 5, 50, 32, 57], 'cur_cost': 111650.0}, {'tour': array([38, 55, 52,  1, 50,  4, 18, 60, 36, 25,  0, 49, 48, 28, 33, 45,  9,
       43, 13, 64, 41, 39, 19, 47, 46, 26, 31, 10, 27, 22, 35, 16,  8, 62,
        2, 42, 29,  6, 53, 17,  3,  5, 11, 23, 56, 14, 21, 34, 54, 57, 32,
       30, 51, 37, 59, 63, 24, 44, 12, 61, 15, 65,  7, 20, 40, 58],
      dtype=int64), 'cur_cost': 102888.0}, {'tour': [50, 3, 21, 28, 16, 27, 48, 35, 15, 9, 26, 53, 36, 40, 23, 4, 39, 64, 2, 6, 33, 37, 29, 65, 54, 42, 43, 0, 24, 14, 34, 55, 18, 11, 56, 5, 20, 12, 60, 13, 10, 32, 52, 30, 49, 61, 62, 57, 8, 51, 58, 38, 44, 17, 59, 1, 46, 41, 45, 47, 31, 25, 63, 19, 7, 22], 'cur_cost': 102799.0}, {'tour': [0, 11, 16, 10, 8, 2, 6, 4, 5, 9, 3, 7, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12860.0}, {'tour': array([26, 24, 63, 60, 61, 10, 42, 54,  4, 16, 15, 23, 56, 58,  5, 57,  9,
       49, 36, 59, 22,  3, 29, 17, 18, 48, 53, 34, 45, 65, 27, 39, 43, 14,
       55, 31, 37, 11,  6, 46, 12, 33, 25, 13,  2,  0, 20, 41, 35, 30, 47,
        8, 62, 32, 38,  7, 28, 50, 40, 52, 51, 64, 21,  1, 19, 44],
      dtype=int64), 'cur_cost': 102979.0}, {'tour': [25, 32, 11, 52, 3, 65, 10, 18, 23, 4, 17, 34, 30, 24, 31, 22, 36, 2, 6, 59, 15, 5, 61, 0, 8, 56, 7, 1, 12, 47, 42, 14, 21, 33, 43, 44, 46, 49, 41, 13, 16, 28, 19, 40, 38, 50, 51, 48, 37, 29, 27, 20, 58, 60, 64, 54, 62, 57, 53, 55, 39, 26, 35, 9, 63, 45], 'cur_cost': 55687.0}, {'tour': [0, 21, 2, 6, 4, 5, 8, 10, 1, 7, 3, 9, 11, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12915.0}, {'tour': array([29, 32,  7, 44, 53, 16, 43,  4, 47, 62, 30, 40, 31, 17, 42, 38, 39,
       41, 11, 37, 13, 23, 56, 24,  6, 27, 22, 60, 58, 45,  0, 54, 49,  2,
       28, 50, 36, 20,  1, 10,  3, 34, 61, 57, 19,  9,  8, 18, 26, 48, 14,
       52, 12, 46, 64, 63, 15, 55,  5, 65, 21, 59, 51, 33, 25, 35],
      dtype=int64), 'cur_cost': 103838.0}, {'tour': [19, 37, 31, 18, 4, 2, 53, 15, 28, 35, 13, 22, 14, 24, 10, 0, 60, 56, 23, 8, 63, 64, 52, 9, 1, 7, 58, 40, 48, 42, 12, 5, 17, 27, 6, 16, 26, 29, 20, 36, 49, 39, 25, 30, 47, 50, 51, 21, 3, 55, 65, 62, 11, 33, 46, 43, 45, 34, 41, 32, 38, 44, 59, 61, 57, 54], 'cur_cost': 63789.0}, {'tour': [0, 3, 1, 12, 22, 23, 16, 18, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12338.0}, {'tour': array([29, 36,  2,  3, 33, 18, 16, 40, 26, 22, 20, 15, 61, 11, 42, 58, 65,
       34, 55, 21, 48, 14, 50, 24, 30,  7, 17, 57, 12, 38,  9,  8,  6, 47,
       59, 28, 35, 31, 52, 49, 62, 41,  5, 39, 60, 43, 37, 32,  4, 45, 13,
       53, 10, 25, 63, 19, 44, 54,  1, 64, 46,  0, 51, 27, 23, 56],
      dtype=int64), 'cur_cost': 114542.0}]
2025-08-03 17:05:24,472 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-08-03 17:05:24,472 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-03 17:05:24,472 - experts.management.collaboration_manager - INFO - 个体 19 利用路径生成报告: {'new_tour': array([29, 36,  2,  3, 33, 18, 16, 40, 26, 22, 20, 15, 61, 11, 42, 58, 65,
       34, 55, 21, 48, 14, 50, 24, 30,  7, 17, 57, 12, 38,  9,  8,  6, 47,
       59, 28, 35, 31, 52, 49, 62, 41,  5, 39, 60, 43, 37, 32,  4, 45, 13,
       53, 10, 25, 63, 19, 44, 54,  1, 64, 46,  0, 51, 27, 23, 56],
      dtype=int64), 'cur_cost': 114542.0}
2025-08-03 17:05:24,472 - experts.management.collaboration_manager - INFO - 个体 19 接受新路径 (成本: 114542.00)
2025-08-03 17:05:24,472 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 16, 'rejected': 4, 'elite_protected': 4}
2025-08-03 17:05:24,472 - experts.management.collaboration_manager - INFO - 接受率: 16/20 (80.0%)
2025-08-03 17:05:24,473 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [20, 6, 58, 7, 64, 3, 60, 52, 21, 32, 9, 10, 5, 19, 15, 11, 27, 31, 29, 12, 35, 36, 0, 22, 28, 16, 34, 8, 57, 1, 54, 23, 4, 61, 39, 40, 49, 51, 14, 48, 13, 17, 30, 46, 44, 26, 2, 55, 65, 18, 45, 37, 42, 50, 43, 38, 47, 41, 24, 56, 62, 59, 63, 53, 25, 33], 'cur_cost': 68630.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([19, 51, 20, 37, 23, 56, 45, 47, 59, 63, 46,  8, 16, 54, 33, 60, 39,
       29, 42, 14, 34, 27, 30, 13, 41, 44, 21, 32, 55, 38,  0, 57,  3,  7,
        9, 43, 28, 12, 17, 22,  5, 26, 48,  2, 35, 18, 31, 40, 58, 10, 50,
       15, 24, 49, 65, 52, 53, 62, 11, 64,  4, 36,  1, 61,  6, 25],
      dtype=int64), 'cur_cost': 99532.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 4, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 3, 7, 1, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12417.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12369.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 9, 58, 30, 47,  4, 53, 42, 60, 46, 32, 17, 26, 31, 25, 65, 19, 41,
       57, 61, 38, 21, 13, 35, 62, 44, 55, 50, 64, 28, 34, 59, 54,  1,  6,
       45,  3, 29, 51, 48, 39, 56,  5, 37, 10, 33, 14, 18, 27, 43,  2,  8,
       20, 49, 12, 40, 15, 36,  7, 23, 63, 24, 22, 52,  0, 16, 11],
      dtype=int64), 'cur_cost': 107783.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [50, 35, 24, 17, 21, 28, 36, 13, 8, 5, 0, 61, 52, 14, 16, 30, 23, 9, 63, 65, 58, 12, 10, 15, 2, 55, 19, 7, 4, 3, 31, 43, 39, 48, 41, 22, 1, 62, 11, 6, 18, 49, 40, 38, 47, 44, 37, 29, 32, 34, 33, 20, 51, 46, 27, 26, 45, 60, 53, 64, 54, 59, 57, 56, 42, 25], 'cur_cost': 56563.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [7, 21, 28, 23, 5, 27, 32, 31, 3, 4, 48, 39, 44, 2, 65, 18, 42, 43, 58, 59, 33, 25, 26, 41, 14, 53, 36, 47, 1, 40, 37, 50, 29, 38, 17, 49, 9, 11, 45, 20, 57, 6, 19, 13, 60, 55, 56, 51, 62, 15, 52, 54, 46, 8, 22, 30, 12, 10, 61, 63, 0, 35, 64, 24, 16, 34], 'cur_cost': 98232.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([37, 63,  9,  7, 17, 30,  6, 31, 13, 42, 16, 52, 55,  5, 61, 39, 60,
       57,  1, 12, 10, 18, 46, 27, 36,  3, 14, 34, 32, 65, 56, 19, 29, 54,
       51, 40, 48, 64,  0, 25, 58, 44, 23, 22, 45, 49, 38, 50, 28, 21, 15,
       24,  4, 53, 20,  8, 47, 41,  2, 43, 26, 11, 35, 33, 59, 62],
      dtype=int64), 'cur_cost': 94203.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 6, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12411.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [7, 21, 24, 18, 25, 60, 23, 3, 14, 37, 27, 31, 65, 36, 22, 53, 13, 19, 9, 39, 4, 62, 20, 6, 47, 55, 2, 29, 40, 46, 41, 59, 44, 34, 30, 64, 61, 48, 26, 16, 54, 11, 33, 58, 17, 1, 12, 38, 52, 51, 45, 49, 63, 28, 42, 15, 0, 8, 43, 56, 10, 35, 5, 50, 32, 57], 'cur_cost': 111650.0}}, {'individual': 10, 'strategy': 'exploit', 'path_data': {'new_tour': array([38, 55, 52,  1, 50,  4, 18, 60, 36, 25,  0, 49, 48, 28, 33, 45,  9,
       43, 13, 64, 41, 39, 19, 47, 46, 26, 31, 10, 27, 22, 35, 16,  8, 62,
        2, 42, 29,  6, 53, 17,  3,  5, 11, 23, 56, 14, 21, 34, 54, 57, 32,
       30, 51, 37, 59, 63, 24, 44, 12, 61, 15, 65,  7, 20, 40, 58],
      dtype=int64), 'cur_cost': 102888.0}}, {'individual': 11, 'strategy': 'explore', 'path_data': {'new_tour': [50, 3, 21, 28, 16, 27, 48, 35, 15, 9, 26, 53, 36, 40, 23, 4, 39, 64, 2, 6, 33, 37, 29, 65, 54, 42, 43, 0, 24, 14, 34, 55, 18, 11, 56, 5, 20, 12, 60, 13, 10, 32, 52, 30, 49, 61, 62, 57, 8, 51, 58, 38, 44, 17, 59, 1, 46, 41, 45, 47, 31, 25, 63, 19, 7, 22], 'cur_cost': 102799.0}}, {'individual': 12, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 16, 10, 8, 2, 6, 4, 5, 9, 3, 7, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12860.0}}, {'individual': 13, 'strategy': 'exploit', 'path_data': {'new_tour': array([26, 24, 63, 60, 61, 10, 42, 54,  4, 16, 15, 23, 56, 58,  5, 57,  9,
       49, 36, 59, 22,  3, 29, 17, 18, 48, 53, 34, 45, 65, 27, 39, 43, 14,
       55, 31, 37, 11,  6, 46, 12, 33, 25, 13,  2,  0, 20, 41, 35, 30, 47,
        8, 62, 32, 38,  7, 28, 50, 40, 52, 51, 64, 21,  1, 19, 44],
      dtype=int64), 'cur_cost': 102979.0}}, {'individual': 14, 'strategy': 'explore', 'path_data': {'new_tour': [25, 32, 11, 52, 3, 65, 10, 18, 23, 4, 17, 34, 30, 24, 31, 22, 36, 2, 6, 59, 15, 5, 61, 0, 8, 56, 7, 1, 12, 47, 42, 14, 21, 33, 43, 44, 46, 49, 41, 13, 16, 28, 19, 40, 38, 50, 51, 48, 37, 29, 27, 20, 58, 60, 64, 54, 62, 57, 53, 55, 39, 26, 35, 9, 63, 45], 'cur_cost': 55687.0}}, {'individual': 15, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 2, 6, 4, 5, 8, 10, 1, 7, 3, 9, 11, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12915.0}}, {'individual': 16, 'strategy': 'exploit', 'path_data': {'new_tour': array([29, 32,  7, 44, 53, 16, 43,  4, 47, 62, 30, 40, 31, 17, 42, 38, 39,
       41, 11, 37, 13, 23, 56, 24,  6, 27, 22, 60, 58, 45,  0, 54, 49,  2,
       28, 50, 36, 20,  1, 10,  3, 34, 61, 57, 19,  9,  8, 18, 26, 48, 14,
       52, 12, 46, 64, 63, 15, 55,  5, 65, 21, 59, 51, 33, 25, 35],
      dtype=int64), 'cur_cost': 103838.0}}, {'individual': 17, 'strategy': 'explore', 'path_data': {'new_tour': [19, 37, 31, 18, 4, 2, 53, 15, 28, 35, 13, 22, 14, 24, 10, 0, 60, 56, 23, 8, 63, 64, 52, 9, 1, 7, 58, 40, 48, 42, 12, 5, 17, 27, 6, 16, 26, 29, 20, 36, 49, 39, 25, 30, 47, 50, 51, 21, 3, 55, 65, 62, 11, 33, 46, 43, 45, 34, 41, 32, 38, 44, 59, 61, 57, 54], 'cur_cost': 63789.0}}, {'individual': 18, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 1, 12, 22, 23, 16, 18, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12338.0}}, {'individual': 19, 'strategy': 'exploit', 'path_data': {'new_tour': array([29, 36,  2,  3, 33, 18, 16, 40, 26, 22, 20, 15, 61, 11, 42, 58, 65,
       34, 55, 21, 48, 14, 50, 24, 30,  7, 17, 57, 12, 38,  9,  8,  6, 47,
       59, 28, 35, 31, 52, 49, 62, 41,  5, 39, 60, 43, 37, 32,  4, 45, 13,
       53, 10, 25, 63, 19, 44, 54,  1, 64, 46,  0, 51, 27, 23, 56],
      dtype=int64), 'cur_cost': 114542.0}}]
2025-08-03 17:05:24,474 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-03 17:05:24,474 - StatsExpert - INFO - 开始统计分析
2025-08-03 17:05:24,486 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=12338.000, 多样性=0.963
2025-08-03 17:05:24,486 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-03 17:05:24,486 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-03 17:05:24,487 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-03 17:05:24,488 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 1, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.018234583649722344, 'best_improvement': -0.24174718196457326}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.009922099220992128}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 10, 'new_count': 10, 'count_change': 0, 'old_best_cost': 9527, 'new_best_cost': 9527, 'quality_improvement': 0.0, 'old_diversity': 0.8878787878787879, 'new_diversity': 0.8878787878787879, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-03 17:05:24,489 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-03 17:05:24,493 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_solution.json
2025-08-03 17:05:24,494 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_20250803_170524.solution
2025-08-03 17:05:24,494 - __main__ - INFO - 实例 composite13_66 处理完成
