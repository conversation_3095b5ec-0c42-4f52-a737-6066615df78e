"""
EoH-TSP-Solver integration adapter for the intelligent strategy selection system.

This module provides the integration layer between the intelligent strategy
selection system and the existing EoH-TSP-Solver framework.
"""

import time
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import logging

from ..core.individual_state import IndividualState, IndividualContext, StagnationLevel
from ..core.data_structures import StrategyType, StrategyAssignment, ExecutionResult, ExecutionStatus
from ..core.landscape_analysis import LandscapeAnalyzer, LandscapeFeatures
from ..strategies import get_strategy_factory, create_execution_engine
from ..llm_interface import LLMStrategyInterface
from ..monitoring import IndividualStateMonitor, PerformanceMonitor
from ..coordination import CollaborativeEscapeCoordinator, StrategyCoordinator


@dataclass
class EoHIndividual:
    """Wrapper for EoH-TSP-Solver individual representation."""
    tour: np.ndarray
    cur_cost: float
    individual_id: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format expected by EoH framework."""
        return {
            "tour": self.tour,
            "cur_cost": self.cur_cost
        }


class EoHStrategyAdapter:
    """
    Adapter for integrating intelligent strategy selection with EoH-TSP-Solver.
    
    This class provides the bridge between the intelligent strategy system
    and the existing EoH framework, handling data format conversions and
    execution coordination.
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize the EoH strategy adapter."""
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.landscape_analyzer = LandscapeAnalyzer(self.config.get('landscape_analysis', {}))
        self.state_monitor = IndividualStateMonitor(self.config.get('state_monitoring', {}))
        self.performance_monitor = PerformanceMonitor(self.config.get('performance_monitoring', {}))
        
        # Strategy components
        self.strategy_factory = get_strategy_factory(self.config)
        self.execution_engine = create_execution_engine(self.config)
        self.llm_interface = LLMStrategyInterface(self.config.get('llm_interface', {}))
        
        # Coordination components
        self.escape_coordinator = CollaborativeEscapeCoordinator(self.config.get('collaborative_escape', {}))
        self.strategy_coordinator = StrategyCoordinator(self.config.get('strategy_coordination', {}))
        
        # Integration state
        self.individual_states: Dict[int, IndividualState] = {}
        self.execution_history: List[ExecutionResult] = []
        self.current_iteration = 0
        
        # Performance tracking
        self.integration_stats = {
            'total_strategy_selections': 0,
            'total_strategy_executions': 0,
            'successful_executions': 0,
            'total_integration_time': 0.0
        }
    
    def integrate_with_evolution_phase(self, 
                                     populations: List[Dict],
                                     strategies: Dict[int, str],
                                     landscape_report: Dict[str, Any],
                                     distance_matrix: np.ndarray,
                                     iteration: int,
                                     res_populations: List[Dict]) -> List[Dict]:
        """
        Integrate with the EoH evolution phase.
        
        Args:
            populations: Current population from EoH framework
            strategies: Strategy assignments from strategy expert
            landscape_report: Landscape analysis report
            distance_matrix: Distance matrix for TSP instance
            iteration: Current iteration number
            res_populations: Elite solutions repository
            
        Returns:
            Updated population after intelligent strategy execution
        """
        start_time = time.time()
        self.current_iteration = iteration
        
        try:
            # Convert EoH population to internal format
            eoh_individuals = self._convert_population_to_internal(populations)
            
            # Update individual states
            self._update_individual_states(eoh_individuals, distance_matrix, iteration)
            
            # Analyze fitness landscape
            landscape_features = self._analyze_landscape(eoh_individuals, distance_matrix, landscape_report)
            
            # Select intelligent strategies
            intelligent_assignments = self._select_intelligent_strategies(
                eoh_individuals, landscape_features, strategies
            )
            
            # Execute strategies
            execution_results = self._execute_strategies(
                intelligent_assignments, eoh_individuals, distance_matrix
            )
            
            # Update population with results
            updated_population = self._update_population_with_results(
                populations, execution_results
            )
            
            # Update performance monitoring
            self._update_performance_monitoring(execution_results)
            
            # Update integration statistics
            integration_time = time.time() - start_time
            self.integration_stats['total_integration_time'] += integration_time
            self.integration_stats['total_strategy_selections'] += len(intelligent_assignments)
            self.integration_stats['total_strategy_executions'] += len(execution_results)
            self.integration_stats['successful_executions'] += sum(1 for r in execution_results if r.success)
            
            self.logger.info(
                f"Intelligent strategy integration completed for iteration {iteration}: "
                f"{len(execution_results)} strategies executed, "
                f"{sum(1 for r in execution_results if r.success)} successful, "
                f"integration time: {integration_time:.3f}s"
            )
            
            return updated_population
            
        except Exception as e:
            self.logger.error(f"Error in intelligent strategy integration: {e}")
            # Return original population on error
            return populations
    
    def _convert_population_to_internal(self, populations: List[Dict]) -> List[EoHIndividual]:
        """Convert EoH population format to internal format."""
        eoh_individuals = []
        
        for i, individual in enumerate(populations):
            eoh_individual = EoHIndividual(
                tour=individual["tour"],
                cur_cost=individual["cur_cost"],
                individual_id=i
            )
            eoh_individuals.append(eoh_individual)
        
        return eoh_individuals
    
    def _update_individual_states(self, 
                                individuals: List[EoHIndividual],
                                distance_matrix: np.ndarray,
                                iteration: int):
        """Update individual states based on current population."""
        # Sort individuals by fitness for ranking
        sorted_individuals = sorted(individuals, key=lambda x: x.cur_cost)
        
        for rank, individual in enumerate(sorted_individuals):
            individual_id = individual.individual_id
            
            # Get or create individual state
            if individual_id not in self.individual_states:
                self.individual_states[individual_id] = IndividualState(
                    individual_id=individual_id,
                    fitness_value=individual.cur_cost,
                    fitness_rank=rank,
                    fitness_percentile=rank / len(individuals),
                    current_solution=individual.tour.tolist()
                )
            else:
                # Update existing state
                state = self.individual_states[individual_id]
                old_fitness = state.fitness_value
                
                # Update basic information
                state.fitness_value = individual.cur_cost
                state.fitness_rank = rank
                state.fitness_percentile = rank / len(individuals)
                state.current_solution = individual.tour.tolist()
                
                # Update stagnation information
                if individual.cur_cost >= old_fitness:
                    state.stagnation_duration += 1
                else:
                    state.stagnation_duration = 0
                    state.recent_improvements.append(old_fitness - individual.cur_cost)
                    if len(state.recent_improvements) > 10:
                        state.recent_improvements.pop(0)
                
                # Update stagnation level
                state.update_stagnation_level()
                
                # Calculate performance trend
                state.calculate_performance_trend()
            
            # Update state monitor
            self.state_monitor.update_individual_state(individual_id, self.individual_states[individual_id])
    
    def _analyze_landscape(self, 
                         individuals: List[EoHIndividual],
                         distance_matrix: np.ndarray,
                         landscape_report: Dict[str, Any]) -> LandscapeFeatures:
        """Analyze fitness landscape using current population."""
        # Extract fitness values and solutions
        fitness_values = [ind.cur_cost for ind in individuals]
        solutions = [ind.tour for ind in individuals]
        
        # Use landscape analyzer
        landscape_features = self.landscape_analyzer.analyze_landscape(
            solutions=solutions,
            fitness_values=fitness_values,
            distance_matrix=distance_matrix
        )
        
        # Enhance with EoH landscape report information
        if landscape_report:
            # Extract relevant information from EoH landscape report
            if 'ruggedness' in landscape_report:
                landscape_features.global_ruggedness = landscape_report['ruggedness'].get('autocorrelation', 0.5)
            
            if 'modality' in landscape_report:
                landscape_features.modality = landscape_report['modality'].get('estimated_peaks', 1)
            
            if 'diversity' in landscape_report:
                landscape_features.population_diversity = landscape_report['diversity'].get('diversity_index', 0.5)
        
        return landscape_features
    
    def _select_intelligent_strategies(self, 
                                     individuals: List[EoHIndividual],
                                     landscape_features: LandscapeFeatures,
                                     eoh_strategies: Dict[int, str]) -> Dict[int, StrategyAssignment]:
        """Select intelligent strategies using LLM interface."""
        # Prepare individual states for LLM
        individual_states = [self.individual_states[ind.individual_id] for ind in individuals]
        
        # Prepare population context
        population_context = {
            'population_size': len(individuals),
            'current_iteration': self.current_iteration,
            'best_fitness': min(ind.cur_cost for ind in individuals),
            'worst_fitness': max(ind.cur_cost for ind in individuals),
            'average_fitness': np.mean([ind.cur_cost for ind in individuals]),
            'eoh_strategies': eoh_strategies
        }
        
        # Use LLM interface for strategy selection
        assignments = self.llm_interface.select_strategies(
            landscape_analysis=landscape_features,
            individual_states=individual_states,
            population_context=population_context
        )
        
        # Enhance assignments with collaborative escape information
        for individual_id, assignment in assignments.items():
            if individual_id < len(individuals):
                individual = individuals[individual_id]
                individual_state = self.individual_states[individual_id]
                
                # Check for collaborative escape opportunities
                if individual_state.stagnation_level in [StagnationLevel.HIGH, StagnationLevel.CRITICAL]:
                    collaboration_info = self.escape_coordinator.request_collaborative_escape(
                        individual_id=str(individual_id),
                        individual_state=individual_state,
                        current_fitness=individual.cur_cost,
                        solution_features=self._extract_solution_features(individual.tour)
                    )
                    
                    if collaboration_info:
                        assignment.metadata['collaboration'] = collaboration_info
        
        return assignments
    
    def _execute_strategies(self, 
                          assignments: Dict[int, StrategyAssignment],
                          individuals: List[EoHIndividual],
                          distance_matrix: np.ndarray) -> List[ExecutionResult]:
        """Execute assigned strategies on individuals."""
        execution_results = []
        
        for individual_id, assignment in assignments.items():
            if individual_id >= len(individuals):
                continue
            
            individual = individuals[individual_id]
            individual_state = self.individual_states[individual_id]
            
            # Create execution context
            context = IndividualContext(
                individual_state=individual_state,
                current_solution=individual.tour.tolist(),
                current_fitness=individual.cur_cost,
                available_time_budget=assignment.time_budget,
                population_size=len(individuals),
                current_iteration=self.current_iteration,
                total_iterations=100,  # Default, should be configurable
                local_ruggedness=individual_state.local_ruggedness,
                gradient_strength=np.linalg.norm(individual_state.local_gradient) if len(individual_state.local_gradient) > 0 else 0.0
            )
            
            # Create fitness function
            def fitness_function(solution):
                return self._calculate_tour_cost(solution, distance_matrix)
            
            # Execute strategy
            try:
                result = self.execution_engine.execute_strategy(assignment, context, fitness_function)
                execution_results.append(result)
                
                # Report escape result if collaborative
                if 'collaboration' in assignment.metadata:
                    self.escape_coordinator.report_escape_result(
                        individual_id=str(individual_id),
                        escape_successful=result.success,
                        new_fitness=result.new_fitness,
                        strategy_used=assignment.strategy_type,
                        execution_result=result
                    )
                
            except Exception as e:
                self.logger.error(f"Error executing strategy for individual {individual_id}: {e}")
                # Create failed result
                result = ExecutionResult(
                    individual_id=individual_id,
                    strategy_type=assignment.strategy_type,
                    status=ExecutionStatus.FAILED,
                    execution_time=0.0,
                    success=False,
                    old_fitness=individual.cur_cost,
                    new_fitness=individual.cur_cost,
                    error_message=str(e)
                )
                execution_results.append(result)
        
        return execution_results
    
    def _update_population_with_results(self, 
                                      original_population: List[Dict],
                                      execution_results: List[ExecutionResult]) -> List[Dict]:
        """Update population with strategy execution results."""
        updated_population = []
        
        for i, individual in enumerate(original_population):
            # Find corresponding execution result
            result = None
            for r in execution_results:
                if r.individual_id == i:
                    result = r
                    break
            
            if result and result.success and result.new_solution:
                # Update with improved solution
                updated_individual = {
                    "tour": np.array(result.new_solution, dtype=np.int64),
                    "cur_cost": result.new_fitness
                }
            else:
                # Keep original individual
                updated_individual = individual.copy()
            
            updated_population.append(updated_individual)
        
        return updated_population
    
    def _update_performance_monitoring(self, execution_results: List[ExecutionResult]):
        """Update performance monitoring with execution results."""
        for result in execution_results:
            # Update individual state with strategy result
            if result.individual_id in self.individual_states:
                state = self.individual_states[result.individual_id]
                state.last_strategy_type = result.strategy_type.value
                state.last_strategy_success = result.success
                
                # Update strategy success rate
                if result.strategy_type.value not in state.preferred_strategies:
                    state.preferred_strategies[result.strategy_type.value] = 0.5
                
                # Update with exponential moving average
                alpha = 0.1
                current_rate = state.preferred_strategies[result.strategy_type.value]
                new_rate = alpha * (1.0 if result.success else 0.0) + (1 - alpha) * current_rate
                state.preferred_strategies[result.strategy_type.value] = new_rate
        
        # Store execution results
        self.execution_history.extend(execution_results)
        
        # Keep only recent history
        if len(self.execution_history) > 1000:
            self.execution_history = self.execution_history[-1000:]
    
    def _extract_solution_features(self, tour: np.ndarray) -> Dict[str, float]:
        """Extract features from a solution for collaboration."""
        n = len(tour)
        
        # Basic features
        features = {
            'tour_length': float(n),
            'first_city': float(tour[0]) / n,
            'last_city': float(tour[-1]) / n
        }
        
        # Edge pattern features
        edge_lengths = []
        for i in range(n):
            edge_lengths.append(abs(tour[(i + 1) % n] - tour[i]))
        
        features.update({
            'avg_edge_length': np.mean(edge_lengths),
            'max_edge_length': np.max(edge_lengths),
            'edge_variance': np.var(edge_lengths)
        })
        
        return features
    
    def _calculate_tour_cost(self, tour: List[int], distance_matrix: np.ndarray) -> float:
        """Calculate the cost of a tour."""
        if len(tour) != len(distance_matrix):
            return float('inf')
        
        total_cost = 0.0
        n = len(tour)
        
        for i in range(n):
            from_city = tour[i]
            to_city = tour[(i + 1) % n]
            total_cost += distance_matrix[from_city][to_city]
        
        return total_cost
    
    def get_integration_statistics(self) -> Dict[str, Any]:
        """Get integration statistics."""
        return {
            'integration_stats': self.integration_stats.copy(),
            'individual_states_count': len(self.individual_states),
            'execution_history_length': len(self.execution_history),
            'landscape_analyzer_stats': self.landscape_analyzer.get_analysis_statistics(),
            'strategy_factory_stats': self.strategy_factory.get_strategy_statistics(),
            'execution_engine_stats': self.execution_engine.get_execution_statistics(),
            'escape_coordinator_stats': self.escape_coordinator.get_coordination_statistics(),
            'strategy_coordinator_stats': self.strategy_coordinator.get_coordination_status()
        }
    
    def cleanup(self):
        """Cleanup resources and perform maintenance."""
        # Cleanup coordination components
        self.escape_coordinator.cleanup_inactive_groups()
        
        # Reset old individual states
        current_time = time.time()
        old_states = []
        for individual_id, state in self.individual_states.items():
            # Remove states that haven't been updated recently
            if hasattr(state, 'last_update_time'):
                if current_time - state.last_update_time > 300:  # 5 minutes
                    old_states.append(individual_id)
        
        for individual_id in old_states:
            del self.individual_states[individual_id]
        
        self.logger.info(f"Cleanup completed: removed {len(old_states)} old individual states")


class EoHSystemIntegrator:
    """
    High-level integrator for the EoH-TSP-Solver system.
    
    This class provides the main integration point for incorporating
    the intelligent strategy selection system into the EoH framework.
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize the system integrator."""
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Initialize adapter
        self.adapter = EoHStrategyAdapter(self.config)
        
        # Integration flags
        self.is_enabled = self.config.get('enabled', True)
        self.integration_mode = self.config.get('mode', 'full')  # 'full', 'analysis_only', 'strategies_only'
        
        self.logger.info(f"EoH System Integrator initialized: enabled={self.is_enabled}, mode={self.integration_mode}")
    
    def integrate_evolution_phase(self, **kwargs) -> List[Dict]:
        """
        Main integration point for the evolution phase.
        
        This method should be called from the EoH collaboration manager
        to integrate intelligent strategy selection.
        """
        if not self.is_enabled:
            # Return original population if integration is disabled
            return kwargs.get('populations', [])
        
        try:
            return self.adapter.integrate_with_evolution_phase(**kwargs)
        except Exception as e:
            self.logger.error(f"Integration failed, falling back to original behavior: {e}")
            return kwargs.get('populations', [])
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status."""
        return {
            'enabled': self.is_enabled,
            'integration_mode': self.integration_mode,
            'adapter_statistics': self.adapter.get_integration_statistics()
        }
    
    def enable_integration(self):
        """Enable intelligent strategy integration."""
        self.is_enabled = True
        self.logger.info("Intelligent strategy integration enabled")
    
    def disable_integration(self):
        """Disable intelligent strategy integration."""
        self.is_enabled = False
        self.logger.info("Intelligent strategy integration disabled")
    
    def cleanup(self):
        """Cleanup integration resources."""
        self.adapter.cleanup()


# Global integrator instance
_global_integrator = None


def get_eoh_integrator(config: Optional[Dict] = None) -> EoHSystemIntegrator:
    """Get the global EoH system integrator instance."""
    global _global_integrator
    if _global_integrator is None:
        _global_integrator = EoHSystemIntegrator(config)
    return _global_integrator


def integrate_with_eoh_evolution(populations: List[Dict],
                                strategies: Dict[int, str],
                                landscape_report: Dict[str, Any],
                                distance_matrix: np.ndarray,
                                iteration: int,
                                res_populations: List[Dict],
                                config: Optional[Dict] = None) -> List[Dict]:
    """
    Convenience function for integrating with EoH evolution phase.
    
    This function can be called directly from the EoH collaboration manager.
    """
    integrator = get_eoh_integrator(config)
    
    return integrator.integrate_evolution_phase(
        populations=populations,
        strategies=strategies,
        landscape_report=landscape_report,
        distance_matrix=distance_matrix,
        iteration=iteration,
        res_populations=res_populations
    )
