"""
Core data structures for the intelligent strategy selection system.

This module defines the fundamental data structures used throughout
the strategy selection and execution process.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Union
from enum import Enum
import numpy as np


class StrategyType(Enum):
    """Enumeration for strategy types."""
    STRONG_EXPLORATION = "strong_exploration"
    BALANCED_EXPLORATION = "balanced_exploration"
    INTELLIGENT_EXPLORATION = "intelligent_exploration"
    CAUTIOUS_EXPLOITATION = "cautious_exploitation"
    MODERATE_EXPLOITATION = "moderate_exploitation"
    AGGRESSIVE_EXPLOITATION = "aggressive_exploitation"
    INTENSIVE_EXPLOITATION = "intensive_exploitation"
    HYBRID = "hybrid"


class ExecutionStatus(Enum):
    """Enumeration for execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"


@dataclass
class ExplorationParameters:
    """
    Parameters for exploration strategies.
    
    All parameters are normalized to [0,1] range for consistency.
    """
    exploration_intensity: float = 0.5      # [0,1] Overall exploration strength
    perturbation_strength: float = 0.5      # [0,1] Magnitude of perturbations
    diversification_bias: float = 0.5       # [0,1] Bias towards diversity
    search_radius: float = 0.3              # [0,1] Search neighborhood size
    mutation_probability: float = 0.2       # [0,1] Probability of mutations
    region_focus_weight: float = 0.4        # [0,1] Focus on specific regions
    novelty_seeking_factor: float = 0.6     # [0,1] Preference for novel solutions
    
    def __post_init__(self):
        """Validate parameter ranges."""
        for field_name, value in self.__dict__.items():
            if not 0.0 <= value <= 1.0:
                raise ValueError(f"{field_name} must be in range [0,1], got {value}")
    
    def to_dict(self) -> Dict[str, float]:
        """Convert to dictionary representation."""
        return {
            'exploration_intensity': self.exploration_intensity,
            'perturbation_strength': self.perturbation_strength,
            'diversification_bias': self.diversification_bias,
            'search_radius': self.search_radius,
            'mutation_probability': self.mutation_probability,
            'region_focus_weight': self.region_focus_weight,
            'novelty_seeking_factor': self.novelty_seeking_factor
        }


@dataclass
class ExploitationParameters:
    """
    Parameters for exploitation strategies.
    
    All parameters are normalized to [0,1] range for consistency.
    """
    local_search_intensity: float = 0.5     # [0,1] Intensity of local search
    convergence_threshold: float = 1e-6     # Threshold for convergence detection
    elite_guidance_weight: float = 0.7      # [0,1] Weight for elite solution guidance
    step_size: float = 0.1                  # [0,1] Step size for local moves
    search_depth: int = 10                  # Maximum search depth
    patience: int = 5                       # Stagnation tolerance
    gradient_following_strength: float = 0.6 # [0,1] Strength of gradient following
    
    def __post_init__(self):
        """Validate parameter ranges."""
        # Validate normalized parameters
        normalized_params = ['local_search_intensity', 'elite_guidance_weight', 
                           'step_size', 'gradient_following_strength']
        for param in normalized_params:
            value = getattr(self, param)
            if not 0.0 <= value <= 1.0:
                raise ValueError(f"{param} must be in range [0,1], got {value}")
        
        # Validate positive integers
        if self.search_depth <= 0:
            raise ValueError(f"search_depth must be positive, got {self.search_depth}")
        if self.patience < 0:
            raise ValueError(f"patience must be non-negative, got {self.patience}")
    
    def to_dict(self) -> Dict[str, Union[float, int]]:
        """Convert to dictionary representation."""
        return {
            'local_search_intensity': self.local_search_intensity,
            'convergence_threshold': self.convergence_threshold,
            'elite_guidance_weight': self.elite_guidance_weight,
            'step_size': self.step_size,
            'search_depth': self.search_depth,
            'patience': self.patience,
            'gradient_following_strength': self.gradient_following_strength
        }


@dataclass
class StrategyAssignment:
    """
    Complete strategy assignment for an individual.
    """
    individual_id: int
    strategy_type: StrategyType
    exploration_params: Optional[ExplorationParameters] = None
    exploitation_params: Optional[ExploitationParameters] = None
    confidence: float = 0.5
    reasoning: str = ""
    priority: float = 0.5
    time_budget: float = 1.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Validate strategy assignment."""
        if not 0.0 <= self.confidence <= 1.0:
            raise ValueError(f"confidence must be in [0,1], got {self.confidence}")
        if not 0.0 <= self.priority <= 1.0:
            raise ValueError(f"priority must be in [0,1], got {self.priority}")
        if self.time_budget <= 0:
            raise ValueError(f"time_budget must be positive, got {self.time_budget}")
    
    def is_exploration_strategy(self) -> bool:
        """Check if this is an exploration strategy."""
        exploration_types = {
            StrategyType.STRONG_EXPLORATION,
            StrategyType.BALANCED_EXPLORATION,
            StrategyType.INTELLIGENT_EXPLORATION
        }
        return self.strategy_type in exploration_types
    
    def is_exploitation_strategy(self) -> bool:
        """Check if this is an exploitation strategy."""
        exploitation_types = {
            StrategyType.CAUTIOUS_EXPLOITATION,
            StrategyType.MODERATE_EXPLOITATION,
            StrategyType.AGGRESSIVE_EXPLOITATION,
            StrategyType.INTENSIVE_EXPLOITATION
        }
        return self.strategy_type in exploitation_types
    
    def get_parameters(self) -> Dict[str, Any]:
        """Get the appropriate parameters for this strategy."""
        if self.is_exploration_strategy() and self.exploration_params:
            return self.exploration_params.to_dict()
        elif self.is_exploitation_strategy() and self.exploitation_params:
            return self.exploitation_params.to_dict()
        else:
            return {}


@dataclass
class ExecutionResult:
    """
    Result of strategy execution for an individual.
    """
    individual_id: int
    strategy_type: StrategyType
    status: ExecutionStatus
    execution_time: float
    success: bool = False
    improvement: float = 0.0
    old_fitness: float = 0.0
    new_fitness: float = 0.0
    iterations_performed: int = 0
    operations_count: int = 0
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def fitness_improvement(self) -> float:
        """Calculate fitness improvement (positive means better)."""
        # For minimization problems, improvement is old - new
        return self.old_fitness - self.new_fitness
    
    @property
    def relative_improvement(self) -> float:
        """Calculate relative improvement percentage."""
        if self.old_fitness == 0:
            return 0.0
        return (self.fitness_improvement / abs(self.old_fitness)) * 100
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            'individual_id': self.individual_id,
            'strategy_type': self.strategy_type.value,
            'status': self.status.value,
            'execution_time': self.execution_time,
            'success': self.success,
            'improvement': self.improvement,
            'old_fitness': self.old_fitness,
            'new_fitness': self.new_fitness,
            'fitness_improvement': self.fitness_improvement,
            'relative_improvement': self.relative_improvement,
            'iterations_performed': self.iterations_performed,
            'operations_count': self.operations_count,
            'error_message': self.error_message,
            'metadata': self.metadata
        }


@dataclass
class FeedbackReport:
    """
    Aggregated feedback report from strategy execution.
    """
    iteration: int
    total_individuals: int
    execution_results: List[ExecutionResult]
    
    # Aggregated metrics
    overall_success_rate: float = 0.0
    average_improvement: float = 0.0
    total_execution_time: float = 0.0
    strategy_performance: Dict[str, Dict[str, float]] = field(default_factory=dict)
    
    # Resource utilization
    cpu_utilization: float = 0.0
    memory_usage: float = 0.0
    
    def __post_init__(self):
        """Calculate aggregated metrics."""
        if not self.execution_results:
            return
        
        # Calculate overall metrics
        successful_results = [r for r in self.execution_results if r.success]
        self.overall_success_rate = len(successful_results) / len(self.execution_results)
        
        if successful_results:
            self.average_improvement = np.mean([r.fitness_improvement for r in successful_results])
        
        self.total_execution_time = sum(r.execution_time for r in self.execution_results)
        
        # Calculate strategy-specific performance
        strategy_stats = {}
        for result in self.execution_results:
            strategy_name = result.strategy_type.value
            if strategy_name not in strategy_stats:
                strategy_stats[strategy_name] = {
                    'count': 0,
                    'successes': 0,
                    'total_improvement': 0.0,
                    'total_time': 0.0
                }
            
            stats = strategy_stats[strategy_name]
            stats['count'] += 1
            if result.success:
                stats['successes'] += 1
                stats['total_improvement'] += result.fitness_improvement
            stats['total_time'] += result.execution_time
        
        # Convert to performance metrics
        for strategy_name, stats in strategy_stats.items():
            self.strategy_performance[strategy_name] = {
                'success_rate': stats['successes'] / stats['count'] if stats['count'] > 0 else 0.0,
                'average_improvement': stats['total_improvement'] / stats['successes'] if stats['successes'] > 0 else 0.0,
                'average_time': stats['total_time'] / stats['count'] if stats['count'] > 0 else 0.0,
                'efficiency': (stats['total_improvement'] / stats['total_time']) if stats['total_time'] > 0 else 0.0
            }
