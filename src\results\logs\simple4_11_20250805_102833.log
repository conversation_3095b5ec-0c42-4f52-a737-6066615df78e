2025-08-05 10:28:33,027 - __main__ - INFO - simple4_11 开始进化第 1 代
2025-08-05 10:28:33,027 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 10:28:33,027 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:33,028 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=822.000, 多样性=0.861
2025-08-05 10:28:33,030 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:33,030 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.861
2025-08-05 10:28:33,078 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:33,085 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 10:28:33,085 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:33,085 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 10:28:33,086 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 10:28:33,097 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -28.940, 聚类评分: 0.000, 覆盖率: 0.018, 收敛趋势: 0.000, 多样性: 0.947
2025-08-05 10:28:33,097 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 10:28:33,097 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 10:28:33,097 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple4_11
2025-08-05 10:28:33,106 - visualization.landscape_visualizer - INFO - 插值约束: 2 个点被约束到最小值 822.00
2025-08-05 10:28:33,108 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=8.7%, 梯度: 24.20 → 22.10
2025-08-05 10:28:33,240 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple4_11\landscape_simple4_11_iter_16_20250805_102833.html
2025-08-05 10:28:33,300 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple4_11\dashboard_simple4_11_iter_16_20250805_102833.html
2025-08-05 10:28:33,301 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 16
2025-08-05 10:28:33,301 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 10:28:33,301 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2165秒
2025-08-05 10:28:33,301 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 32, 'max_size': 500, 'hits': 0, 'misses': 32, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 50, 'max_size': 100, 'hits': 107, 'misses': 50, 'hit_rate': 0.6815286624203821, 'evictions': 0, 'ttl': 7200}}
2025-08-05 10:28:33,301 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -28.940000000000005, 'local_optima_density': 0.1, 'gradient_variance': 73221.08840000001, 'cluster_count': 0}, 'population_state': {'diversity': 0.9466666666666667, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0178, 'fitness_entropy': 0.8982444017039272, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -28.940)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.018)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.947)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360913.097756, 'performance_metrics': {}}}
2025-08-05 10:28:33,301 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:33,302 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:33,302 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:33,302 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:33,302 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 10:28:33,302 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 10:28:33,302 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 10:28:33,302 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:33,302 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:33,303 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 10:28:33,303 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:33,303 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:33,303 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:33,303 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:33,303 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:33,303 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:33,304 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 11
2025-08-05 10:28:33,304 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,304 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1348.0, 路径长度: 11, 收集中间解: 0
2025-08-05 10:28:33,305 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [8, 0, 5, 4, 2, 6, 10, 7, 1, 3, 9], 'cur_cost': 1348.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:33,305 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 1348.00)
2025-08-05 10:28:33,305 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:33,305 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:33,305 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:33,306 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 11
2025-08-05 10:28:33,306 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,306 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1073.0, 路径长度: 11, 收集中间解: 0
2025-08-05 10:28:33,306 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [8, 6, 5, 9, 3, 1, 2, 7, 4, 10, 0], 'cur_cost': 1073.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:33,306 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1073.00)
2025-08-05 10:28:33,306 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:33,306 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:33,306 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:33,307 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 10:28:33,307 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,307 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1244.0, 路径长度: 11, 收集中间解: 0
2025-08-05 10:28:33,307 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [4, 5, 1, 6, 0, 3, 2, 7, 9, 10, 8], 'cur_cost': 1244.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:33,307 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 1244.00)
2025-08-05 10:28:33,307 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:28:33,307 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:33,308 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:33,308 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1535.0
2025-08-05 10:28:33,311 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 10:28:33,311 - ExploitationExpert - INFO - res_population_costs: [803.0, 803]
2025-08-05 10:28:33,311 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  8, 10,  5,  3,  9,  2,  4,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  4,  9,  3,  5, 10,  8,  6], dtype=int64)]
2025-08-05 10:28:33,312 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:33,312 - ExploitationExpert - INFO - populations: [{'tour': [8, 0, 5, 4, 2, 6, 10, 7, 1, 3, 9], 'cur_cost': 1348.0}, {'tour': [8, 6, 5, 9, 3, 1, 2, 7, 4, 10, 0], 'cur_cost': 1073.0}, {'tour': [4, 5, 1, 6, 0, 3, 2, 7, 9, 10, 8], 'cur_cost': 1244.0}, {'tour': array([ 2,  4, 10,  3,  5,  1,  8,  9,  6,  7,  0], dtype=int64), 'cur_cost': 1535.0}, {'tour': array([ 4,  9,  0,  6,  3,  7,  1,  8,  2,  5, 10], dtype=int64), 'cur_cost': 1415.0}, {'tour': array([ 8,  6,  4, 10,  3,  7,  2,  1,  0,  9,  5], dtype=int64), 'cur_cost': 1322.0}, {'tour': array([ 5,  8,  7,  3, 10,  1,  9,  2,  4,  0,  6], dtype=int64), 'cur_cost': 1323.0}, {'tour': array([ 0,  6, 10,  4,  2,  3,  8,  1,  5,  9,  7], dtype=int64), 'cur_cost': 1339.0}, {'tour': array([ 4,  6,  5,  0,  3,  1,  7, 10,  2,  8,  9], dtype=int64), 'cur_cost': 1481.0}, {'tour': array([ 4,  0,  2,  8,  9,  1,  3,  6, 10,  5,  7], dtype=int64), 'cur_cost': 1358.0}]
2025-08-05 10:28:33,314 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:33,314 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 40, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 40, 'cache_hits': 0, 'similarity_calculations': 288, 'cache_hit_rate': 0.0, 'cache_size': 288}}
2025-08-05 10:28:33,314 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 2,  4, 10,  3,  5,  1,  8,  9,  6,  7,  0], dtype=int64), 'cur_cost': 1535.0, 'intermediate_solutions': [{'tour': array([ 8,  3,  4,  1,  9,  2,  0, 10,  7,  5,  6], dtype=int64), 'cur_cost': 1477.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1,  8,  3,  4,  9,  2,  0, 10,  7,  5,  6], dtype=int64), 'cur_cost': 1511.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9,  1,  8,  3,  4,  2,  0, 10,  7,  5,  6], dtype=int64), 'cur_cost': 1546.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4,  1,  8,  3,  9,  2,  0, 10,  7,  5,  6], dtype=int64), 'cur_cost': 1566.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4,  9,  1,  8,  3,  2,  0, 10,  7,  5,  6], dtype=int64), 'cur_cost': 1599.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:33,314 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1535.00)
2025-08-05 10:28:33,314 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:33,315 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:33,315 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:33,315 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 11
2025-08-05 10:28:33,315 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,316 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1160.0, 路径长度: 11, 收集中间解: 0
2025-08-05 10:28:33,316 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [6, 3, 10, 8, 1, 5, 0, 2, 7, 4, 9], 'cur_cost': 1160.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:33,316 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1160.00)
2025-08-05 10:28:33,316 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:33,316 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:33,316 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:33,317 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 11
2025-08-05 10:28:33,317 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,317 - ExplorationExpert - INFO - 探索路径生成完成，成本: 961.0, 路径长度: 11, 收集中间解: 0
2025-08-05 10:28:33,317 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [6, 8, 10, 3, 5, 1, 7, 4, 2, 9, 0], 'cur_cost': 961.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:33,317 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 961.00)
2025-08-05 10:28:33,318 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:33,318 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:33,318 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:33,318 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 10:28:33,318 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,318 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1140.0, 路径长度: 11, 收集中间解: 0
2025-08-05 10:28:33,318 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [3, 4, 1, 10, 5, 8, 6, 0, 2, 7, 9], 'cur_cost': 1140.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:33,318 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1140.00)
2025-08-05 10:28:33,319 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:33,319 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:33,319 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:33,319 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 10:28:33,319 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,320 - ExplorationExpert - INFO - 探索路径生成完成，成本: 993.0, 路径长度: 11, 收集中间解: 0
2025-08-05 10:28:33,320 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 1, 10, 3, 4, 7, 2, 9, 5, 8, 6], 'cur_cost': 993.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:33,320 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 993.00)
2025-08-05 10:28:33,320 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:33,320 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:33,320 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:33,320 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 1253.0
2025-08-05 10:28:33,325 - ExploitationExpert - INFO - res_population_num: 3
2025-08-05 10:28:33,325 - ExploitationExpert - INFO - res_population_costs: [803.0, 803, 803.0]
2025-08-05 10:28:33,325 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  8, 10,  5,  3,  9,  2,  4,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  4,  9,  3,  5, 10,  8,  6], dtype=int64), array([ 0,  6,  3,  5,  8, 10,  9,  2,  4,  7,  1], dtype=int64)]
2025-08-05 10:28:33,326 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:33,326 - ExploitationExpert - INFO - populations: [{'tour': [8, 0, 5, 4, 2, 6, 10, 7, 1, 3, 9], 'cur_cost': 1348.0}, {'tour': [8, 6, 5, 9, 3, 1, 2, 7, 4, 10, 0], 'cur_cost': 1073.0}, {'tour': [4, 5, 1, 6, 0, 3, 2, 7, 9, 10, 8], 'cur_cost': 1244.0}, {'tour': array([ 2,  4, 10,  3,  5,  1,  8,  9,  6,  7,  0], dtype=int64), 'cur_cost': 1535.0}, {'tour': [6, 3, 10, 8, 1, 5, 0, 2, 7, 4, 9], 'cur_cost': 1160.0}, {'tour': [6, 8, 10, 3, 5, 1, 7, 4, 2, 9, 0], 'cur_cost': 961.0}, {'tour': [3, 4, 1, 10, 5, 8, 6, 0, 2, 7, 9], 'cur_cost': 1140.0}, {'tour': [0, 1, 10, 3, 4, 7, 2, 9, 5, 8, 6], 'cur_cost': 993.0}, {'tour': array([ 2,  6,  9,  4,  7,  3,  5,  1,  0, 10,  8], dtype=int64), 'cur_cost': 1253.0}, {'tour': array([ 4,  0,  2,  8,  9,  1,  3,  6, 10,  5,  7], dtype=int64), 'cur_cost': 1358.0}]
2025-08-05 10:28:33,327 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:33,327 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 41, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 41, 'cache_hits': 0, 'similarity_calculations': 289, 'cache_hit_rate': 0.0, 'cache_size': 289}}
2025-08-05 10:28:33,328 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 2,  6,  9,  4,  7,  3,  5,  1,  0, 10,  8], dtype=int64), 'cur_cost': 1253.0, 'intermediate_solutions': [{'tour': array([ 5,  6,  4,  0,  3,  1,  7, 10,  2,  8,  9], dtype=int64), 'cur_cost': 1553.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 0,  5,  6,  4,  3,  1,  7, 10,  2,  8,  9], dtype=int64), 'cur_cost': 1578.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3,  0,  5,  6,  4,  1,  7, 10,  2,  8,  9], dtype=int64), 'cur_cost': 1504.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4,  0,  5,  6,  3,  1,  7, 10,  2,  8,  9], dtype=int64), 'cur_cost': 1460.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4,  3,  0,  5,  6,  1,  7, 10,  2,  8,  9], dtype=int64), 'cur_cost': 1449.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:33,328 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1253.00)
2025-08-05 10:28:33,328 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:33,328 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:33,328 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:33,329 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 11
2025-08-05 10:28:33,329 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,329 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1381.0, 路径长度: 11, 收集中间解: 0
2025-08-05 10:28:33,329 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [3, 5, 6, 2, 8, 10, 4, 1, 0, 7, 9], 'cur_cost': 1381.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:33,329 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1381.00)
2025-08-05 10:28:33,329 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:33,330 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:33,330 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [8, 0, 5, 4, 2, 6, 10, 7, 1, 3, 9], 'cur_cost': 1348.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [8, 6, 5, 9, 3, 1, 2, 7, 4, 10, 0], 'cur_cost': 1073.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [4, 5, 1, 6, 0, 3, 2, 7, 9, 10, 8], 'cur_cost': 1244.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 2,  4, 10,  3,  5,  1,  8,  9,  6,  7,  0], dtype=int64), 'cur_cost': 1535.0, 'intermediate_solutions': [{'tour': array([ 8,  3,  4,  1,  9,  2,  0, 10,  7,  5,  6], dtype=int64), 'cur_cost': 1477.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1,  8,  3,  4,  9,  2,  0, 10,  7,  5,  6], dtype=int64), 'cur_cost': 1511.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9,  1,  8,  3,  4,  2,  0, 10,  7,  5,  6], dtype=int64), 'cur_cost': 1546.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4,  1,  8,  3,  9,  2,  0, 10,  7,  5,  6], dtype=int64), 'cur_cost': 1566.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4,  9,  1,  8,  3,  2,  0, 10,  7,  5,  6], dtype=int64), 'cur_cost': 1599.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [6, 3, 10, 8, 1, 5, 0, 2, 7, 4, 9], 'cur_cost': 1160.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [6, 8, 10, 3, 5, 1, 7, 4, 2, 9, 0], 'cur_cost': 961.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [3, 4, 1, 10, 5, 8, 6, 0, 2, 7, 9], 'cur_cost': 1140.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 10, 3, 4, 7, 2, 9, 5, 8, 6], 'cur_cost': 993.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 2,  6,  9,  4,  7,  3,  5,  1,  0, 10,  8], dtype=int64), 'cur_cost': 1253.0, 'intermediate_solutions': [{'tour': array([ 5,  6,  4,  0,  3,  1,  7, 10,  2,  8,  9], dtype=int64), 'cur_cost': 1553.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 0,  5,  6,  4,  3,  1,  7, 10,  2,  8,  9], dtype=int64), 'cur_cost': 1578.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3,  0,  5,  6,  4,  1,  7, 10,  2,  8,  9], dtype=int64), 'cur_cost': 1504.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4,  0,  5,  6,  3,  1,  7, 10,  2,  8,  9], dtype=int64), 'cur_cost': 1460.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4,  3,  0,  5,  6,  1,  7, 10,  2,  8,  9], dtype=int64), 'cur_cost': 1449.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [3, 5, 6, 2, 8, 10, 4, 1, 0, 7, 9], 'cur_cost': 1381.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:33,331 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:33,331 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:33,332 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=961.000, 多样性=0.895
2025-08-05 10:28:33,332 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 10:28:33,332 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 10:28:33,332 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:33,332 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.026004099754075378, 'best_improvement': -0.16909975669099755}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.03990610328638498}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.09834925011848228, 'recent_improvements': [-0.1261829358411454, -0.028903783396767527, 0.07051556439581914], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 803.0, 'new_best_cost': 803.0, 'quality_improvement': 0.0, 'old_diversity': 0.7272727272727273, 'new_diversity': 0.7272727272727273, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 10:28:33,332 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 10:28:33,332 - __main__ - INFO - simple4_11 开始进化第 2 代
2025-08-05 10:28:33,333 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 10:28:33,333 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:33,333 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=961.000, 多样性=0.895
2025-08-05 10:28:33,333 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:33,334 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.895
2025-08-05 10:28:33,334 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:33,335 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.727
2025-08-05 10:28:33,336 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 10:28:33,337 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:33,337 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-05 10:28:33,337 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-05 10:28:33,345 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.308, 适应度梯度: 37.508, 聚类评分: 0.000, 覆盖率: 0.019, 收敛趋势: 0.000, 多样性: 0.757
2025-08-05 10:28:33,345 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 10:28:33,345 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:33,345 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple4_11
2025-08-05 10:28:33,349 - visualization.landscape_visualizer - INFO - 插值约束: 174 个点被约束到最小值 803.00
2025-08-05 10:28:33,351 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.8%, 梯度: 32.43 → 29.88
2025-08-05 10:28:33,471 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple4_11\landscape_simple4_11_iter_17_20250805_102833.html
2025-08-05 10:28:33,529 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple4_11\dashboard_simple4_11_iter_17_20250805_102833.html
2025-08-05 10:28:33,529 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 17
2025-08-05 10:28:33,529 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 10:28:33,529 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1927秒
2025-08-05 10:28:33,530 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3076923076923077, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 37.507692307692295, 'local_optima_density': 0.3076923076923077, 'gradient_variance': 61190.89609467455, 'cluster_count': 0}, 'population_state': {'diversity': 0.7573964497041421, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0191, 'fitness_entropy': 0.9686322539060781, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.019)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.757)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 37.508)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360913.345869, 'performance_metrics': {}}}
2025-08-05 10:28:33,530 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:33,530 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:33,530 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:33,530 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:33,530 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:33,531 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 10:28:33,531 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:33,531 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:33,531 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:33,531 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:33,531 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:33,532 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:33,532 - experts.management.collaboration_manager - INFO - 识别精英个体: {5, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:33,532 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:33,532 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:33,532 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:33,533 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 11
2025-08-05 10:28:33,533 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,533 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,533 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,533 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,533 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1126.0, 路径长度: 11, 收集中间解: 3
2025-08-05 10:28:33,533 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [7, 1, 3, 10, 6, 5, 8, 0, 2, 4, 9], 'cur_cost': 1126.0, 'intermediate_solutions': [{'tour': [8, 5, 0, 4, 2, 6, 10, 7, 1, 3, 9], 'cur_cost': 1317.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 0, 5, 4, 2, 6, 10, 7, 3, 1, 9], 'cur_cost': 1427.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 4, 0, 5, 2, 6, 10, 7, 1, 3, 9], 'cur_cost': 1558.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:33,534 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1126.00)
2025-08-05 10:28:33,534 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:33,534 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:33,534 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:33,534 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 11
2025-08-05 10:28:33,534 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,534 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,534 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,535 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,535 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1550.0, 路径长度: 11, 收集中间解: 3
2025-08-05 10:28:33,535 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [8, 3, 5, 9, 6, 7, 0, 2, 4, 10, 1], 'cur_cost': 1550.0, 'intermediate_solutions': [{'tour': [8, 3, 5, 9, 6, 1, 2, 7, 4, 10, 0], 'cur_cost': 1175.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 6, 5, 9, 3, 1, 2, 0, 10, 4, 7], 'cur_cost': 1286.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 8, 5, 9, 3, 1, 2, 7, 4, 10, 0], 'cur_cost': 998.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:33,535 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1550.00)
2025-08-05 10:28:33,535 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:33,536 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:33,536 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:33,536 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 11
2025-08-05 10:28:33,537 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,537 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,537 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,537 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,537 - ExplorationExpert - INFO - 探索路径生成完成，成本: 984.0, 路径长度: 11, 收集中间解: 3
2025-08-05 10:28:33,537 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [7, 2, 3, 10, 8, 5, 1, 6, 0, 9, 4], 'cur_cost': 984.0, 'intermediate_solutions': [{'tour': [4, 5, 1, 6, 0, 3, 10, 7, 9, 2, 8], 'cur_cost': 1544.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 5, 1, 6, 0, 2, 3, 7, 9, 10, 8], 'cur_cost': 1366.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 5, 1, 6, 8, 0, 3, 2, 7, 9, 10], 'cur_cost': 1316.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:33,538 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 984.00)
2025-08-05 10:28:33,538 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:28:33,538 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:33,538 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:33,539 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1442.0
2025-08-05 10:28:33,547 - ExploitationExpert - INFO - res_population_num: 3
2025-08-05 10:28:33,548 - ExploitationExpert - INFO - res_population_costs: [803.0, 803, 803.0]
2025-08-05 10:28:33,548 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  8, 10,  5,  3,  9,  2,  4,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  4,  9,  3,  5, 10,  8,  6], dtype=int64), array([ 0,  6,  3,  5,  8, 10,  9,  2,  4,  7,  1], dtype=int64)]
2025-08-05 10:28:33,549 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:33,549 - ExploitationExpert - INFO - populations: [{'tour': [7, 1, 3, 10, 6, 5, 8, 0, 2, 4, 9], 'cur_cost': 1126.0}, {'tour': [8, 3, 5, 9, 6, 7, 0, 2, 4, 10, 1], 'cur_cost': 1550.0}, {'tour': [7, 2, 3, 10, 8, 5, 1, 6, 0, 9, 4], 'cur_cost': 984.0}, {'tour': array([ 2,  1,  9,  4,  5,  7,  0,  8,  6,  3, 10], dtype=int64), 'cur_cost': 1442.0}, {'tour': [6, 3, 10, 8, 1, 5, 0, 2, 7, 4, 9], 'cur_cost': 1160.0}, {'tour': [6, 8, 10, 3, 5, 1, 7, 4, 2, 9, 0], 'cur_cost': 961.0}, {'tour': [3, 4, 1, 10, 5, 8, 6, 0, 2, 7, 9], 'cur_cost': 1140.0}, {'tour': [0, 1, 10, 3, 4, 7, 2, 9, 5, 8, 6], 'cur_cost': 993.0}, {'tour': [2, 6, 9, 4, 7, 3, 5, 1, 0, 10, 8], 'cur_cost': 1253.0}, {'tour': [3, 5, 6, 2, 8, 10, 4, 1, 0, 7, 9], 'cur_cost': 1381.0}]
2025-08-05 10:28:33,549 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:33,549 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 42, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 42, 'cache_hits': 0, 'similarity_calculations': 291, 'cache_hit_rate': 0.0, 'cache_size': 291}}
2025-08-05 10:28:33,550 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 2,  1,  9,  4,  5,  7,  0,  8,  6,  3, 10], dtype=int64), 'cur_cost': 1442.0, 'intermediate_solutions': [{'tour': array([10,  4,  2,  3,  5,  1,  8,  9,  6,  7,  0]), 'cur_cost': 1504.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 3, 10,  4,  2,  5,  1,  8,  9,  6,  7,  0]), 'cur_cost': 1552.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 5,  3, 10,  4,  2,  1,  8,  9,  6,  7,  0]), 'cur_cost': 1445.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2,  3, 10,  4,  5,  1,  8,  9,  6,  7,  0]), 'cur_cost': 1679.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2,  5,  3, 10,  4,  1,  8,  9,  6,  7,  0]), 'cur_cost': 1607.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:33,550 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1442.00)
2025-08-05 10:28:33,550 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:33,550 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:33,551 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:33,551 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 11
2025-08-05 10:28:33,552 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,552 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,552 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,552 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,552 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1220.0, 路径长度: 11, 收集中间解: 3
2025-08-05 10:28:33,553 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [9, 8, 3, 5, 1, 7, 4, 6, 0, 10, 2], 'cur_cost': 1220.0, 'intermediate_solutions': [{'tour': [6, 7, 10, 8, 1, 5, 0, 2, 3, 4, 9], 'cur_cost': 1506.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 3, 10, 8, 1, 5, 0, 9, 4, 7, 2], 'cur_cost': 1182.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 7, 3, 10, 8, 1, 5, 0, 2, 4, 9], 'cur_cost': 1364.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:33,553 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1220.00)
2025-08-05 10:28:33,553 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:33,553 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:33,553 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:33,553 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 10:28:33,553 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,554 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,554 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,554 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,554 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1244.0, 路径长度: 11, 收集中间解: 3
2025-08-05 10:28:33,554 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [6, 10, 1, 8, 4, 7, 2, 3, 5, 9, 0], 'cur_cost': 1244.0, 'intermediate_solutions': [{'tour': [6, 8, 10, 3, 5, 0, 7, 4, 2, 9, 1], 'cur_cost': 1044.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 8, 10, 3, 9, 2, 4, 7, 1, 5, 0], 'cur_cost': 917.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 8, 10, 9, 3, 5, 1, 7, 4, 2, 0], 'cur_cost': 922.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:33,554 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 1244.00)
2025-08-05 10:28:33,554 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:33,554 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:33,555 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:33,555 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 10:28:33,555 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,555 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,555 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,555 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,555 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1465.0, 路径长度: 11, 收集中间解: 3
2025-08-05 10:28:33,556 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [4, 8, 7, 5, 10, 3, 1, 2, 9, 6, 0], 'cur_cost': 1465.0, 'intermediate_solutions': [{'tour': [2, 4, 1, 10, 5, 8, 6, 0, 3, 7, 9], 'cur_cost': 1085.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 4, 1, 10, 5, 8, 6, 0, 9, 7, 2], 'cur_cost': 1157.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 4, 1, 10, 5, 8, 6, 0, 2, 7, 9], 'cur_cost': 1140.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:33,556 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1465.00)
2025-08-05 10:28:33,556 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:33,556 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:33,556 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:33,556 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 11
2025-08-05 10:28:33,556 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,556 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,557 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,557 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,557 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1443.0, 路径长度: 11, 收集中间解: 3
2025-08-05 10:28:33,557 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [9, 3, 5, 8, 1, 10, 0, 2, 4, 6, 7], 'cur_cost': 1443.0, 'intermediate_solutions': [{'tour': [0, 1, 4, 3, 10, 7, 2, 9, 5, 8, 6], 'cur_cost': 1137.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 3, 10, 1, 7, 2, 9, 5, 8, 6], 'cur_cost': 1167.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 3, 10, 4, 7, 2, 9, 5, 8, 6], 'cur_cost': 979.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:33,557 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 1443.00)
2025-08-05 10:28:33,557 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:33,557 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:33,557 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:33,558 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 11
2025-08-05 10:28:33,558 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,558 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,558 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,558 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,558 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1282.0, 路径长度: 11, 收集中间解: 3
2025-08-05 10:28:33,558 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [8, 10, 1, 9, 3, 2, 0, 4, 7, 5, 6], 'cur_cost': 1282.0, 'intermediate_solutions': [{'tour': [2, 6, 9, 4, 7, 1, 5, 3, 0, 10, 8], 'cur_cost': 1229.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 6, 9, 4, 7, 3, 5, 1, 10, 0, 8], 'cur_cost': 1372.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 6, 9, 1, 4, 7, 3, 5, 0, 10, 8], 'cur_cost': 1288.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:33,558 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1282.00)
2025-08-05 10:28:33,559 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:28:33,559 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:33,559 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:33,559 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 1313.0
2025-08-05 10:28:33,564 - ExploitationExpert - INFO - res_population_num: 3
2025-08-05 10:28:33,564 - ExploitationExpert - INFO - res_population_costs: [803.0, 803, 803.0]
2025-08-05 10:28:33,564 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  8, 10,  5,  3,  9,  2,  4,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  4,  9,  3,  5, 10,  8,  6], dtype=int64), array([ 0,  6,  3,  5,  8, 10,  9,  2,  4,  7,  1], dtype=int64)]
2025-08-05 10:28:33,565 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:33,565 - ExploitationExpert - INFO - populations: [{'tour': [7, 1, 3, 10, 6, 5, 8, 0, 2, 4, 9], 'cur_cost': 1126.0}, {'tour': [8, 3, 5, 9, 6, 7, 0, 2, 4, 10, 1], 'cur_cost': 1550.0}, {'tour': [7, 2, 3, 10, 8, 5, 1, 6, 0, 9, 4], 'cur_cost': 984.0}, {'tour': array([ 2,  1,  9,  4,  5,  7,  0,  8,  6,  3, 10], dtype=int64), 'cur_cost': 1442.0}, {'tour': [9, 8, 3, 5, 1, 7, 4, 6, 0, 10, 2], 'cur_cost': 1220.0}, {'tour': [6, 10, 1, 8, 4, 7, 2, 3, 5, 9, 0], 'cur_cost': 1244.0}, {'tour': [4, 8, 7, 5, 10, 3, 1, 2, 9, 6, 0], 'cur_cost': 1465.0}, {'tour': [9, 3, 5, 8, 1, 10, 0, 2, 4, 6, 7], 'cur_cost': 1443.0}, {'tour': [8, 10, 1, 9, 3, 2, 0, 4, 7, 5, 6], 'cur_cost': 1282.0}, {'tour': array([ 8,  1,  0,  5,  2,  7,  9,  3,  4,  6, 10], dtype=int64), 'cur_cost': 1313.0}]
2025-08-05 10:28:33,566 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:33,566 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 43, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 43, 'cache_hits': 0, 'similarity_calculations': 294, 'cache_hit_rate': 0.0, 'cache_size': 294}}
2025-08-05 10:28:33,566 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([ 8,  1,  0,  5,  2,  7,  9,  3,  4,  6, 10], dtype=int64), 'cur_cost': 1313.0, 'intermediate_solutions': [{'tour': array([ 6,  5,  3,  2,  8, 10,  4,  1,  0,  7,  9]), 'cur_cost': 1376.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2,  6,  5,  3,  8, 10,  4,  1,  0,  7,  9]), 'cur_cost': 1314.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 8,  2,  6,  5,  3, 10,  4,  1,  0,  7,  9]), 'cur_cost': 1464.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 3,  2,  6,  5,  8, 10,  4,  1,  0,  7,  9]), 'cur_cost': 1284.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 3,  8,  2,  6,  5, 10,  4,  1,  0,  7,  9]), 'cur_cost': 1428.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:33,566 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1313.00)
2025-08-05 10:28:33,567 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:33,567 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:33,567 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [7, 1, 3, 10, 6, 5, 8, 0, 2, 4, 9], 'cur_cost': 1126.0, 'intermediate_solutions': [{'tour': [8, 5, 0, 4, 2, 6, 10, 7, 1, 3, 9], 'cur_cost': 1317.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 0, 5, 4, 2, 6, 10, 7, 3, 1, 9], 'cur_cost': 1427.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 4, 0, 5, 2, 6, 10, 7, 1, 3, 9], 'cur_cost': 1558.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [8, 3, 5, 9, 6, 7, 0, 2, 4, 10, 1], 'cur_cost': 1550.0, 'intermediate_solutions': [{'tour': [8, 3, 5, 9, 6, 1, 2, 7, 4, 10, 0], 'cur_cost': 1175.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 6, 5, 9, 3, 1, 2, 0, 10, 4, 7], 'cur_cost': 1286.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 8, 5, 9, 3, 1, 2, 7, 4, 10, 0], 'cur_cost': 998.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [7, 2, 3, 10, 8, 5, 1, 6, 0, 9, 4], 'cur_cost': 984.0, 'intermediate_solutions': [{'tour': [4, 5, 1, 6, 0, 3, 10, 7, 9, 2, 8], 'cur_cost': 1544.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 5, 1, 6, 0, 2, 3, 7, 9, 10, 8], 'cur_cost': 1366.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 5, 1, 6, 8, 0, 3, 2, 7, 9, 10], 'cur_cost': 1316.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 2,  1,  9,  4,  5,  7,  0,  8,  6,  3, 10], dtype=int64), 'cur_cost': 1442.0, 'intermediate_solutions': [{'tour': array([10,  4,  2,  3,  5,  1,  8,  9,  6,  7,  0]), 'cur_cost': 1504.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 3, 10,  4,  2,  5,  1,  8,  9,  6,  7,  0]), 'cur_cost': 1552.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 5,  3, 10,  4,  2,  1,  8,  9,  6,  7,  0]), 'cur_cost': 1445.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2,  3, 10,  4,  5,  1,  8,  9,  6,  7,  0]), 'cur_cost': 1679.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2,  5,  3, 10,  4,  1,  8,  9,  6,  7,  0]), 'cur_cost': 1607.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [9, 8, 3, 5, 1, 7, 4, 6, 0, 10, 2], 'cur_cost': 1220.0, 'intermediate_solutions': [{'tour': [6, 7, 10, 8, 1, 5, 0, 2, 3, 4, 9], 'cur_cost': 1506.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 3, 10, 8, 1, 5, 0, 9, 4, 7, 2], 'cur_cost': 1182.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 7, 3, 10, 8, 1, 5, 0, 2, 4, 9], 'cur_cost': 1364.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [6, 10, 1, 8, 4, 7, 2, 3, 5, 9, 0], 'cur_cost': 1244.0, 'intermediate_solutions': [{'tour': [6, 8, 10, 3, 5, 0, 7, 4, 2, 9, 1], 'cur_cost': 1044.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 8, 10, 3, 9, 2, 4, 7, 1, 5, 0], 'cur_cost': 917.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 8, 10, 9, 3, 5, 1, 7, 4, 2, 0], 'cur_cost': 922.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [4, 8, 7, 5, 10, 3, 1, 2, 9, 6, 0], 'cur_cost': 1465.0, 'intermediate_solutions': [{'tour': [2, 4, 1, 10, 5, 8, 6, 0, 3, 7, 9], 'cur_cost': 1085.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 4, 1, 10, 5, 8, 6, 0, 9, 7, 2], 'cur_cost': 1157.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 4, 1, 10, 5, 8, 6, 0, 2, 7, 9], 'cur_cost': 1140.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [9, 3, 5, 8, 1, 10, 0, 2, 4, 6, 7], 'cur_cost': 1443.0, 'intermediate_solutions': [{'tour': [0, 1, 4, 3, 10, 7, 2, 9, 5, 8, 6], 'cur_cost': 1137.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 3, 10, 1, 7, 2, 9, 5, 8, 6], 'cur_cost': 1167.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 3, 10, 4, 7, 2, 9, 5, 8, 6], 'cur_cost': 979.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [8, 10, 1, 9, 3, 2, 0, 4, 7, 5, 6], 'cur_cost': 1282.0, 'intermediate_solutions': [{'tour': [2, 6, 9, 4, 7, 1, 5, 3, 0, 10, 8], 'cur_cost': 1229.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 6, 9, 4, 7, 3, 5, 1, 10, 0, 8], 'cur_cost': 1372.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 6, 9, 1, 4, 7, 3, 5, 0, 10, 8], 'cur_cost': 1288.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 8,  1,  0,  5,  2,  7,  9,  3,  4,  6, 10], dtype=int64), 'cur_cost': 1313.0, 'intermediate_solutions': [{'tour': array([ 6,  5,  3,  2,  8, 10,  4,  1,  0,  7,  9]), 'cur_cost': 1376.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2,  6,  5,  3,  8, 10,  4,  1,  0,  7,  9]), 'cur_cost': 1314.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 8,  2,  6,  5,  3, 10,  4,  1,  0,  7,  9]), 'cur_cost': 1464.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 3,  2,  6,  5,  8, 10,  4,  1,  0,  7,  9]), 'cur_cost': 1284.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 3,  8,  2,  6,  5, 10,  4,  1,  0,  7,  9]), 'cur_cost': 1428.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:28:33,568 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:33,568 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:33,569 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=984.000, 多样性=0.883
2025-08-05 10:28:33,569 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 10:28:33,569 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 10:28:33,569 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:33,569 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.03828675133481956, 'best_improvement': -0.023933402705515087}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.013544018058690956}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.0014498418213460748, 'recent_improvements': [-0.028903783396767527, 0.07051556439581914, -0.026004099754075378], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 803.0, 'new_best_cost': 803.0, 'quality_improvement': 0.0, 'old_diversity': 0.7272727272727273, 'new_diversity': 0.7272727272727273, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启']}
2025-08-05 10:28:33,569 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 10:28:33,570 - __main__ - INFO - simple4_11 开始进化第 3 代
2025-08-05 10:28:33,570 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 10:28:33,570 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:33,570 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=984.000, 多样性=0.883
2025-08-05 10:28:33,570 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:33,571 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.883
2025-08-05 10:28:33,571 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:33,571 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.727
2025-08-05 10:28:33,573 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 10:28:33,573 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:33,574 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-05 10:28:33,574 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-05 10:28:33,581 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.231, 适应度梯度: 71.662, 聚类评分: 0.000, 覆盖率: 0.020, 收敛趋势: 0.000, 多样性: 0.748
2025-08-05 10:28:33,581 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 10:28:33,581 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:33,581 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple4_11
2025-08-05 10:28:33,584 - visualization.landscape_visualizer - INFO - 插值约束: 21 个点被约束到最小值 803.00
2025-08-05 10:28:33,585 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=8.5%, 梯度: 23.87 → 21.84
2025-08-05 10:28:33,701 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple4_11\landscape_simple4_11_iter_18_20250805_102833.html
2025-08-05 10:28:33,787 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple4_11\dashboard_simple4_11_iter_18_20250805_102833.html
2025-08-05 10:28:33,787 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 18
2025-08-05 10:28:33,787 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 10:28:33,787 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2139秒
2025-08-05 10:28:33,787 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.23076923076923078, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 71.66153846153846, 'local_optima_density': 0.23076923076923078, 'gradient_variance': 81074.2546745562, 'cluster_count': 0}, 'population_state': {'diversity': 0.7475345167652859, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0201, 'fitness_entropy': 0.9479479190038742, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.020)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.748)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 71.662)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360913.5811112, 'performance_metrics': {}}}
2025-08-05 10:28:33,787 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:33,787 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:33,788 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:33,788 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:33,788 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore']
2025-08-05 10:28:33,788 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 10:28:33,788 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore']
2025-08-05 10:28:33,789 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:33,789 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:33,789 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore']
2025-08-05 10:28:33,789 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:33,789 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:33,789 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:33,789 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:33,790 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:33,790 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:33,790 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 10:28:33,790 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,790 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,791 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,791 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,791 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1002.0, 路径长度: 11, 收集中间解: 3
2025-08-05 10:28:33,791 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [2, 5, 0, 3, 6, 8, 10, 9, 4, 7, 1], 'cur_cost': 1002.0, 'intermediate_solutions': [{'tour': [7, 1, 3, 2, 6, 5, 8, 0, 10, 4, 9], 'cur_cost': 1282.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 1, 3, 4, 2, 0, 8, 5, 6, 10, 9], 'cur_cost': 1135.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 1, 3, 10, 6, 5, 8, 2, 4, 9, 0], 'cur_cost': 1258.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:33,791 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 1002.00)
2025-08-05 10:28:33,791 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 10:28:33,791 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:33,791 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:33,792 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 1498.0
2025-08-05 10:28:33,796 - ExploitationExpert - INFO - res_population_num: 3
2025-08-05 10:28:33,797 - ExploitationExpert - INFO - res_population_costs: [803.0, 803, 803.0]
2025-08-05 10:28:33,797 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  8, 10,  5,  3,  9,  2,  4,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  4,  9,  3,  5, 10,  8,  6], dtype=int64), array([ 0,  6,  3,  5,  8, 10,  9,  2,  4,  7,  1], dtype=int64)]
2025-08-05 10:28:33,798 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:33,798 - ExploitationExpert - INFO - populations: [{'tour': [2, 5, 0, 3, 6, 8, 10, 9, 4, 7, 1], 'cur_cost': 1002.0}, {'tour': array([ 6, 10,  9,  0,  5,  7,  1,  2,  8,  4,  3], dtype=int64), 'cur_cost': 1498.0}, {'tour': [7, 2, 3, 10, 8, 5, 1, 6, 0, 9, 4], 'cur_cost': 984.0}, {'tour': [2, 1, 9, 4, 5, 7, 0, 8, 6, 3, 10], 'cur_cost': 1442.0}, {'tour': [9, 8, 3, 5, 1, 7, 4, 6, 0, 10, 2], 'cur_cost': 1220.0}, {'tour': [6, 10, 1, 8, 4, 7, 2, 3, 5, 9, 0], 'cur_cost': 1244.0}, {'tour': [4, 8, 7, 5, 10, 3, 1, 2, 9, 6, 0], 'cur_cost': 1465.0}, {'tour': [9, 3, 5, 8, 1, 10, 0, 2, 4, 6, 7], 'cur_cost': 1443.0}, {'tour': [8, 10, 1, 9, 3, 2, 0, 4, 7, 5, 6], 'cur_cost': 1282.0}, {'tour': [8, 1, 0, 5, 2, 7, 9, 3, 4, 6, 10], 'cur_cost': 1313.0}]
2025-08-05 10:28:33,798 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:33,798 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 44, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 44, 'cache_hits': 0, 'similarity_calculations': 298, 'cache_hit_rate': 0.0, 'cache_size': 298}}
2025-08-05 10:28:33,799 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([ 6, 10,  9,  0,  5,  7,  1,  2,  8,  4,  3], dtype=int64), 'cur_cost': 1498.0, 'intermediate_solutions': [{'tour': array([ 5,  3,  8,  9,  6,  7,  0,  2,  4, 10,  1]), 'cur_cost': 1544.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9,  5,  3,  8,  6,  7,  0,  2,  4, 10,  1]), 'cur_cost': 1466.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 6,  9,  5,  3,  8,  7,  0,  2,  4, 10,  1]), 'cur_cost': 1538.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 8,  9,  5,  3,  6,  7,  0,  2,  4, 10,  1]), 'cur_cost': 1500.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 8,  6,  9,  5,  3,  7,  0,  2,  4, 10,  1]), 'cur_cost': 1472.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:33,799 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1498.00)
2025-08-05 10:28:33,799 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:33,799 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:33,799 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:33,799 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 10:28:33,799 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,799 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,800 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,800 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,800 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1163.0, 路径长度: 11, 收集中间解: 3
2025-08-05 10:28:33,800 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [1, 6, 4, 3, 7, 2, 9, 10, 5, 8, 0], 'cur_cost': 1163.0, 'intermediate_solutions': [{'tour': [1, 2, 3, 10, 8, 5, 7, 6, 0, 9, 4], 'cur_cost': 1234.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 2, 3, 10, 8, 5, 6, 1, 0, 9, 4], 'cur_cost': 987.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 2, 3, 8, 5, 1, 10, 6, 0, 9, 4], 'cur_cost': 1103.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:33,800 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 1163.00)
2025-08-05 10:28:33,800 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:33,800 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:33,800 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:33,801 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 11
2025-08-05 10:28:33,801 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,801 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,801 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,801 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,801 - ExplorationExpert - INFO - 探索路径生成完成，成本: 959.0, 路径长度: 11, 收集中间解: 3
2025-08-05 10:28:33,801 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 6, 5, 10, 8, 1, 3, 2, 7, 4, 9], 'cur_cost': 959.0, 'intermediate_solutions': [{'tour': [2, 1, 9, 4, 10, 7, 0, 8, 6, 3, 5], 'cur_cost': 1432.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 1, 9, 5, 4, 7, 0, 8, 6, 3, 10], 'cur_cost': 1269.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 1, 9, 3, 4, 5, 7, 0, 8, 6, 10], 'cur_cost': 1481.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:33,802 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 959.00)
2025-08-05 10:28:33,802 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:33,802 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:33,802 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:33,802 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 10:28:33,802 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,802 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,802 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,803 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,803 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1345.0, 路径长度: 11, 收集中间解: 3
2025-08-05 10:28:33,803 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [6, 9, 0, 7, 8, 10, 5, 3, 1, 2, 4], 'cur_cost': 1345.0, 'intermediate_solutions': [{'tour': [9, 7, 3, 5, 1, 8, 4, 6, 0, 10, 2], 'cur_cost': 1515.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 0, 6, 4, 7, 1, 5, 3, 8, 10, 2], 'cur_cost': 1185.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 8, 3, 5, 1, 4, 7, 6, 0, 10, 2], 'cur_cost': 1226.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:33,803 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1345.00)
2025-08-05 10:28:33,803 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:33,803 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:33,803 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:33,804 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 10:28:33,804 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,804 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,804 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,804 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,804 - ExplorationExpert - INFO - 探索路径生成完成，成本: 952.0, 路径长度: 11, 收集中间解: 3
2025-08-05 10:28:33,804 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [1, 6, 0, 8, 3, 10, 5, 9, 2, 7, 4], 'cur_cost': 952.0, 'intermediate_solutions': [{'tour': [6, 10, 1, 8, 4, 3, 2, 7, 5, 9, 0], 'cur_cost': 1469.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 10, 1, 9, 5, 3, 2, 7, 4, 8, 0], 'cur_cost': 1154.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 1, 8, 10, 4, 7, 2, 3, 5, 9, 0], 'cur_cost': 1112.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:33,804 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 952.00)
2025-08-05 10:28:33,804 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:28:33,805 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:33,805 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:33,805 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1531.0
2025-08-05 10:28:33,810 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:33,810 - ExploitationExpert - INFO - res_population_costs: [803.0, 803, 803.0, 803]
2025-08-05 10:28:33,810 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  8, 10,  5,  3,  9,  2,  4,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  4,  9,  3,  5, 10,  8,  6], dtype=int64), array([ 0,  6,  3,  5,  8, 10,  9,  2,  4,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  4,  9, 10,  8,  5,  3,  6], dtype=int64)]
2025-08-05 10:28:33,811 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:33,811 - ExploitationExpert - INFO - populations: [{'tour': [2, 5, 0, 3, 6, 8, 10, 9, 4, 7, 1], 'cur_cost': 1002.0}, {'tour': array([ 6, 10,  9,  0,  5,  7,  1,  2,  8,  4,  3], dtype=int64), 'cur_cost': 1498.0}, {'tour': [1, 6, 4, 3, 7, 2, 9, 10, 5, 8, 0], 'cur_cost': 1163.0}, {'tour': [0, 6, 5, 10, 8, 1, 3, 2, 7, 4, 9], 'cur_cost': 959.0}, {'tour': [6, 9, 0, 7, 8, 10, 5, 3, 1, 2, 4], 'cur_cost': 1345.0}, {'tour': [1, 6, 0, 8, 3, 10, 5, 9, 2, 7, 4], 'cur_cost': 952.0}, {'tour': array([ 9,  1,  4,  0,  8,  2, 10,  5,  7,  6,  3], dtype=int64), 'cur_cost': 1531.0}, {'tour': [9, 3, 5, 8, 1, 10, 0, 2, 4, 6, 7], 'cur_cost': 1443.0}, {'tour': [8, 10, 1, 9, 3, 2, 0, 4, 7, 5, 6], 'cur_cost': 1282.0}, {'tour': [8, 1, 0, 5, 2, 7, 9, 3, 4, 6, 10], 'cur_cost': 1313.0}]
2025-08-05 10:28:33,812 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:33,812 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 45, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 45, 'cache_hits': 0, 'similarity_calculations': 303, 'cache_hit_rate': 0.0, 'cache_size': 303}}
2025-08-05 10:28:33,812 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([ 9,  1,  4,  0,  8,  2, 10,  5,  7,  6,  3], dtype=int64), 'cur_cost': 1531.0, 'intermediate_solutions': [{'tour': array([ 7,  8,  4,  5, 10,  3,  1,  2,  9,  6,  0]), 'cur_cost': 1445.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 5,  7,  8,  4, 10,  3,  1,  2,  9,  6,  0]), 'cur_cost': 1526.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([10,  5,  7,  8,  4,  3,  1,  2,  9,  6,  0]), 'cur_cost': 1441.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4,  5,  7,  8, 10,  3,  1,  2,  9,  6,  0]), 'cur_cost': 1417.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4, 10,  5,  7,  8,  3,  1,  2,  9,  6,  0]), 'cur_cost': 1452.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:33,812 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1531.00)
2025-08-05 10:28:33,812 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:28:33,813 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:33,813 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:33,813 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 1531.0
2025-08-05 10:28:33,821 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:33,821 - ExploitationExpert - INFO - res_population_costs: [803.0, 803, 803.0, 803]
2025-08-05 10:28:33,821 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  8, 10,  5,  3,  9,  2,  4,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  4,  9,  3,  5, 10,  8,  6], dtype=int64), array([ 0,  6,  3,  5,  8, 10,  9,  2,  4,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  4,  9, 10,  8,  5,  3,  6], dtype=int64)]
2025-08-05 10:28:33,823 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:33,823 - ExploitationExpert - INFO - populations: [{'tour': [2, 5, 0, 3, 6, 8, 10, 9, 4, 7, 1], 'cur_cost': 1002.0}, {'tour': array([ 6, 10,  9,  0,  5,  7,  1,  2,  8,  4,  3], dtype=int64), 'cur_cost': 1498.0}, {'tour': [1, 6, 4, 3, 7, 2, 9, 10, 5, 8, 0], 'cur_cost': 1163.0}, {'tour': [0, 6, 5, 10, 8, 1, 3, 2, 7, 4, 9], 'cur_cost': 959.0}, {'tour': [6, 9, 0, 7, 8, 10, 5, 3, 1, 2, 4], 'cur_cost': 1345.0}, {'tour': [1, 6, 0, 8, 3, 10, 5, 9, 2, 7, 4], 'cur_cost': 952.0}, {'tour': array([ 9,  1,  4,  0,  8,  2, 10,  5,  7,  6,  3], dtype=int64), 'cur_cost': 1531.0}, {'tour': array([ 8,  7,  0,  3,  5,  1,  6, 10,  4,  9,  2], dtype=int64), 'cur_cost': 1531.0}, {'tour': [8, 10, 1, 9, 3, 2, 0, 4, 7, 5, 6], 'cur_cost': 1282.0}, {'tour': [8, 1, 0, 5, 2, 7, 9, 3, 4, 6, 10], 'cur_cost': 1313.0}]
2025-08-05 10:28:33,824 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:33,824 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 46, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 46, 'cache_hits': 0, 'similarity_calculations': 309, 'cache_hit_rate': 0.0, 'cache_size': 309}}
2025-08-05 10:28:33,825 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([ 8,  7,  0,  3,  5,  1,  6, 10,  4,  9,  2], dtype=int64), 'cur_cost': 1531.0, 'intermediate_solutions': [{'tour': array([ 5,  3,  9,  8,  1, 10,  0,  2,  4,  6,  7]), 'cur_cost': 1550.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 8,  5,  3,  9,  1, 10,  0,  2,  4,  6,  7]), 'cur_cost': 1510.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1,  8,  5,  3,  9, 10,  0,  2,  4,  6,  7]), 'cur_cost': 1331.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 9,  8,  5,  3,  1, 10,  0,  2,  4,  6,  7]), 'cur_cost': 1393.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 9,  1,  8,  5,  3, 10,  0,  2,  4,  6,  7]), 'cur_cost': 1432.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:33,825 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1531.00)
2025-08-05 10:28:33,825 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:33,825 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:33,825 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:33,826 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 10:28:33,826 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,826 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,826 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,827 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,827 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1145.0, 路径长度: 11, 收集中间解: 3
2025-08-05 10:28:33,827 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [5, 9, 2, 6, 10, 8, 3, 1, 7, 4, 0], 'cur_cost': 1145.0, 'intermediate_solutions': [{'tour': [3, 10, 1, 9, 8, 2, 0, 4, 7, 5, 6], 'cur_cost': 1456.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 10, 1, 9, 3, 2, 0, 7, 4, 5, 6], 'cur_cost': 1262.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 8, 10, 1, 3, 2, 0, 4, 7, 5, 6], 'cur_cost': 1316.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:33,827 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1145.00)
2025-08-05 10:28:33,828 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:33,828 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:33,828 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:33,829 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 11
2025-08-05 10:28:33,829 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,831 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,831 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,831 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:33,831 - ExplorationExpert - INFO - 探索路径生成完成，成本: 873.0, 路径长度: 11, 收集中间解: 3
2025-08-05 10:28:33,832 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [2, 1, 3, 10, 8, 6, 0, 5, 9, 4, 7], 'cur_cost': 873.0, 'intermediate_solutions': [{'tour': [8, 7, 0, 5, 2, 1, 9, 3, 4, 6, 10], 'cur_cost': 1480.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 5, 0, 1, 8, 7, 9, 3, 4, 6, 10], 'cur_cost': 1588.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 1, 0, 5, 7, 2, 9, 3, 4, 6, 10], 'cur_cost': 1316.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:33,832 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 873.00)
2025-08-05 10:28:33,832 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:33,832 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:33,834 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [2, 5, 0, 3, 6, 8, 10, 9, 4, 7, 1], 'cur_cost': 1002.0, 'intermediate_solutions': [{'tour': [7, 1, 3, 2, 6, 5, 8, 0, 10, 4, 9], 'cur_cost': 1282.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 1, 3, 4, 2, 0, 8, 5, 6, 10, 9], 'cur_cost': 1135.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 1, 3, 10, 6, 5, 8, 2, 4, 9, 0], 'cur_cost': 1258.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 6, 10,  9,  0,  5,  7,  1,  2,  8,  4,  3], dtype=int64), 'cur_cost': 1498.0, 'intermediate_solutions': [{'tour': array([ 5,  3,  8,  9,  6,  7,  0,  2,  4, 10,  1]), 'cur_cost': 1544.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9,  5,  3,  8,  6,  7,  0,  2,  4, 10,  1]), 'cur_cost': 1466.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 6,  9,  5,  3,  8,  7,  0,  2,  4, 10,  1]), 'cur_cost': 1538.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 8,  9,  5,  3,  6,  7,  0,  2,  4, 10,  1]), 'cur_cost': 1500.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 8,  6,  9,  5,  3,  7,  0,  2,  4, 10,  1]), 'cur_cost': 1472.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [1, 6, 4, 3, 7, 2, 9, 10, 5, 8, 0], 'cur_cost': 1163.0, 'intermediate_solutions': [{'tour': [1, 2, 3, 10, 8, 5, 7, 6, 0, 9, 4], 'cur_cost': 1234.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 2, 3, 10, 8, 5, 6, 1, 0, 9, 4], 'cur_cost': 987.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 2, 3, 8, 5, 1, 10, 6, 0, 9, 4], 'cur_cost': 1103.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 5, 10, 8, 1, 3, 2, 7, 4, 9], 'cur_cost': 959.0, 'intermediate_solutions': [{'tour': [2, 1, 9, 4, 10, 7, 0, 8, 6, 3, 5], 'cur_cost': 1432.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 1, 9, 5, 4, 7, 0, 8, 6, 3, 10], 'cur_cost': 1269.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 1, 9, 3, 4, 5, 7, 0, 8, 6, 10], 'cur_cost': 1481.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [6, 9, 0, 7, 8, 10, 5, 3, 1, 2, 4], 'cur_cost': 1345.0, 'intermediate_solutions': [{'tour': [9, 7, 3, 5, 1, 8, 4, 6, 0, 10, 2], 'cur_cost': 1515.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 0, 6, 4, 7, 1, 5, 3, 8, 10, 2], 'cur_cost': 1185.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 8, 3, 5, 1, 4, 7, 6, 0, 10, 2], 'cur_cost': 1226.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [1, 6, 0, 8, 3, 10, 5, 9, 2, 7, 4], 'cur_cost': 952.0, 'intermediate_solutions': [{'tour': [6, 10, 1, 8, 4, 3, 2, 7, 5, 9, 0], 'cur_cost': 1469.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 10, 1, 9, 5, 3, 2, 7, 4, 8, 0], 'cur_cost': 1154.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 1, 8, 10, 4, 7, 2, 3, 5, 9, 0], 'cur_cost': 1112.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 9,  1,  4,  0,  8,  2, 10,  5,  7,  6,  3], dtype=int64), 'cur_cost': 1531.0, 'intermediate_solutions': [{'tour': array([ 7,  8,  4,  5, 10,  3,  1,  2,  9,  6,  0]), 'cur_cost': 1445.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 5,  7,  8,  4, 10,  3,  1,  2,  9,  6,  0]), 'cur_cost': 1526.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([10,  5,  7,  8,  4,  3,  1,  2,  9,  6,  0]), 'cur_cost': 1441.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4,  5,  7,  8, 10,  3,  1,  2,  9,  6,  0]), 'cur_cost': 1417.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4, 10,  5,  7,  8,  3,  1,  2,  9,  6,  0]), 'cur_cost': 1452.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 8,  7,  0,  3,  5,  1,  6, 10,  4,  9,  2], dtype=int64), 'cur_cost': 1531.0, 'intermediate_solutions': [{'tour': array([ 5,  3,  9,  8,  1, 10,  0,  2,  4,  6,  7]), 'cur_cost': 1550.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 8,  5,  3,  9,  1, 10,  0,  2,  4,  6,  7]), 'cur_cost': 1510.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1,  8,  5,  3,  9, 10,  0,  2,  4,  6,  7]), 'cur_cost': 1331.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 9,  8,  5,  3,  1, 10,  0,  2,  4,  6,  7]), 'cur_cost': 1393.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 9,  1,  8,  5,  3, 10,  0,  2,  4,  6,  7]), 'cur_cost': 1432.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [5, 9, 2, 6, 10, 8, 3, 1, 7, 4, 0], 'cur_cost': 1145.0, 'intermediate_solutions': [{'tour': [3, 10, 1, 9, 8, 2, 0, 4, 7, 5, 6], 'cur_cost': 1456.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 10, 1, 9, 3, 2, 0, 7, 4, 5, 6], 'cur_cost': 1262.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 8, 10, 1, 3, 2, 0, 4, 7, 5, 6], 'cur_cost': 1316.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [2, 1, 3, 10, 8, 6, 0, 5, 9, 4, 7], 'cur_cost': 873.0, 'intermediate_solutions': [{'tour': [8, 7, 0, 5, 2, 1, 9, 3, 4, 6, 10], 'cur_cost': 1480.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 5, 0, 1, 8, 7, 9, 3, 4, 6, 10], 'cur_cost': 1588.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 1, 0, 5, 7, 2, 9, 3, 4, 6, 10], 'cur_cost': 1316.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:33,835 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:33,835 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:33,836 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=873.000, 多样性=0.895
2025-08-05 10:28:33,837 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 10:28:33,837 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 10:28:33,837 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:33,837 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.06897869248809659, 'best_improvement': 0.11280487804878049}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.013729977116705023}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.05440115786531935, 'recent_improvements': [0.07051556439581914, -0.026004099754075378, -0.03828675133481956], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 803.0, 'new_best_cost': 803.0, 'quality_improvement': 0.0, 'old_diversity': 0.7272727272727273, 'new_diversity': 0.7272727272727273, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:33,838 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 10:28:33,838 - __main__ - INFO - simple4_11 开始进化第 4 代
2025-08-05 10:28:33,838 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 10:28:33,838 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:33,839 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=873.000, 多样性=0.895
2025-08-05 10:28:33,840 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:33,841 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.895
2025-08-05 10:28:33,841 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:33,843 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.727
2025-08-05 10:28:33,845 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 10:28:33,846 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:33,846 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 10:28:33,846 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 10:28:33,863 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.286, 适应度梯度: -17.786, 聚类评分: 0.000, 覆盖率: 0.021, 收敛趋势: 0.000, 多样性: 0.689
2025-08-05 10:28:33,864 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 10:28:33,864 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:33,864 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple4_11
2025-08-05 10:28:33,871 - visualization.landscape_visualizer - INFO - 插值约束: 72 个点被约束到最小值 803.00
2025-08-05 10:28:33,874 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=9.8%, 梯度: 26.23 → 23.66
2025-08-05 10:28:34,037 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple4_11\landscape_simple4_11_iter_19_20250805_102833.html
2025-08-05 10:28:34,118 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple4_11\dashboard_simple4_11_iter_19_20250805_102833.html
2025-08-05 10:28:34,118 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 19
2025-08-05 10:28:34,118 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 10:28:34,118 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2730秒
2025-08-05 10:28:34,119 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2857142857142857, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -17.785714285714278, 'local_optima_density': 0.2857142857142857, 'gradient_variance': 62597.80265306123, 'cluster_count': 0}, 'population_state': {'diversity': 0.6891679748822606, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0212, 'fitness_entropy': 0.9285249215482252, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -17.786)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.021)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360913.8643596, 'performance_metrics': {}}}
2025-08-05 10:28:34,119 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:34,119 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:34,119 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:34,119 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:34,120 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore']
2025-08-05 10:28:34,120 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 10:28:34,120 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore']
2025-08-05 10:28:34,120 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:34,120 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:34,120 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore']
2025-08-05 10:28:34,120 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:34,120 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:34,121 - experts.management.collaboration_manager - INFO - 识别精英个体: {9, 5} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:34,121 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:34,121 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:34,121 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:34,121 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 10:28:34,121 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,121 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,122 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,122 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,122 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1187.0, 路径长度: 11, 收集中间解: 3
2025-08-05 10:28:34,122 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [1, 8, 0, 2, 4, 7, 3, 5, 10, 6, 9], 'cur_cost': 1187.0, 'intermediate_solutions': [{'tour': [2, 5, 0, 3, 6, 8, 10, 7, 4, 9, 1], 'cur_cost': 1166.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 5, 0, 3, 6, 8, 10, 9, 4, 1, 7], 'cur_cost': 1075.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 5, 0, 3, 8, 10, 6, 9, 4, 7, 1], 'cur_cost': 1114.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:34,122 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1187.00)
2025-08-05 10:28:34,122 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 10:28:34,122 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:34,122 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:34,123 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 1168.0
2025-08-05 10:28:34,128 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:34,128 - ExploitationExpert - INFO - res_population_costs: [803.0, 803, 803.0, 803]
2025-08-05 10:28:34,128 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  8, 10,  5,  3,  9,  2,  4,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  4,  9,  3,  5, 10,  8,  6], dtype=int64), array([ 0,  6,  3,  5,  8, 10,  9,  2,  4,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  4,  9, 10,  8,  5,  3,  6], dtype=int64)]
2025-08-05 10:28:34,129 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:34,129 - ExploitationExpert - INFO - populations: [{'tour': [1, 8, 0, 2, 4, 7, 3, 5, 10, 6, 9], 'cur_cost': 1187.0}, {'tour': array([ 3,  8,  9,  2,  0, 10,  5,  6,  1,  4,  7], dtype=int64), 'cur_cost': 1168.0}, {'tour': [1, 6, 4, 3, 7, 2, 9, 10, 5, 8, 0], 'cur_cost': 1163.0}, {'tour': [0, 6, 5, 10, 8, 1, 3, 2, 7, 4, 9], 'cur_cost': 959.0}, {'tour': [6, 9, 0, 7, 8, 10, 5, 3, 1, 2, 4], 'cur_cost': 1345.0}, {'tour': [1, 6, 0, 8, 3, 10, 5, 9, 2, 7, 4], 'cur_cost': 952.0}, {'tour': [9, 1, 4, 0, 8, 2, 10, 5, 7, 6, 3], 'cur_cost': 1531.0}, {'tour': [8, 7, 0, 3, 5, 1, 6, 10, 4, 9, 2], 'cur_cost': 1531.0}, {'tour': [5, 9, 2, 6, 10, 8, 3, 1, 7, 4, 0], 'cur_cost': 1145.0}, {'tour': [2, 1, 3, 10, 8, 6, 0, 5, 9, 4, 7], 'cur_cost': 873.0}]
2025-08-05 10:28:34,130 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:34,130 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 47, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 47, 'cache_hits': 0, 'similarity_calculations': 316, 'cache_hit_rate': 0.0, 'cache_size': 316}}
2025-08-05 10:28:34,130 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([ 3,  8,  9,  2,  0, 10,  5,  6,  1,  4,  7], dtype=int64), 'cur_cost': 1168.0, 'intermediate_solutions': [{'tour': array([ 9, 10,  6,  0,  5,  7,  1,  2,  8,  4,  3]), 'cur_cost': 1376.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 0,  9, 10,  6,  5,  7,  1,  2,  8,  4,  3]), 'cur_cost': 1487.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 5,  0,  9, 10,  6,  7,  1,  2,  8,  4,  3]), 'cur_cost': 1483.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6,  0,  9, 10,  5,  7,  1,  2,  8,  4,  3]), 'cur_cost': 1384.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6,  5,  0,  9, 10,  7,  1,  2,  8,  4,  3]), 'cur_cost': 1495.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:34,130 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1168.00)
2025-08-05 10:28:34,130 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:34,130 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:34,131 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:34,131 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 11
2025-08-05 10:28:34,131 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,131 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,131 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,132 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,132 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1208.0, 路径长度: 11, 收集中间解: 3
2025-08-05 10:28:34,132 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [4, 9, 10, 3, 6, 8, 0, 1, 2, 7, 5], 'cur_cost': 1208.0, 'intermediate_solutions': [{'tour': [1, 6, 4, 3, 7, 2, 9, 10, 8, 5, 0], 'cur_cost': 1160.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 10, 9, 2, 7, 3, 4, 5, 8, 0], 'cur_cost': 1201.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 4, 3, 7, 2, 9, 5, 10, 8, 0], 'cur_cost': 1161.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:34,132 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1208.00)
2025-08-05 10:28:34,132 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:34,132 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:34,132 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:34,133 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 11
2025-08-05 10:28:34,133 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,133 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,133 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,133 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,133 - ExplorationExpert - INFO - 探索路径生成完成，成本: 975.0, 路径长度: 11, 收集中间解: 3
2025-08-05 10:28:34,133 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [8, 5, 6, 3, 10, 9, 4, 2, 7, 1, 0], 'cur_cost': 975.0, 'intermediate_solutions': [{'tour': [0, 6, 5, 10, 1, 8, 3, 2, 7, 4, 9], 'cur_cost': 1101.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 10, 5, 6, 1, 3, 2, 7, 4, 9], 'cur_cost': 966.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 5, 10, 8, 1, 9, 3, 2, 7, 4], 'cur_cost': 1022.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:34,133 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 975.00)
2025-08-05 10:28:34,134 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:34,134 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:34,134 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:34,134 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 10:28:34,134 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,134 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,134 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,134 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,135 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1428.0, 路径长度: 11, 收集中间解: 3
2025-08-05 10:28:34,135 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [4, 10, 6, 7, 1, 5, 3, 2, 9, 8, 0], 'cur_cost': 1428.0, 'intermediate_solutions': [{'tour': [9, 6, 0, 7, 8, 10, 5, 3, 1, 2, 4], 'cur_cost': 1129.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 9, 0, 7, 8, 10, 2, 1, 3, 5, 4], 'cur_cost': 1575.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 9, 7, 0, 8, 10, 5, 3, 1, 2, 4], 'cur_cost': 1188.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:34,135 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1428.00)
2025-08-05 10:28:34,135 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:34,135 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:34,135 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:34,135 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 10:28:34,135 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,136 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,136 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,136 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,136 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1282.0, 路径长度: 11, 收集中间解: 3
2025-08-05 10:28:34,136 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [4, 8, 0, 9, 1, 10, 5, 3, 6, 2, 7], 'cur_cost': 1282.0, 'intermediate_solutions': [{'tour': [1, 6, 5, 8, 3, 10, 0, 9, 2, 7, 4], 'cur_cost': 1102.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 0, 8, 2, 9, 5, 10, 3, 7, 4], 'cur_cost': 1092.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 0, 8, 10, 3, 5, 9, 2, 7, 4], 'cur_cost': 905.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:34,136 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 1282.00)
2025-08-05 10:28:34,136 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:28:34,136 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:34,137 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:34,137 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1359.0
2025-08-05 10:28:34,143 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:34,143 - ExploitationExpert - INFO - res_population_costs: [803.0, 803, 803.0, 803]
2025-08-05 10:28:34,143 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  8, 10,  5,  3,  9,  2,  4,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  4,  9,  3,  5, 10,  8,  6], dtype=int64), array([ 0,  6,  3,  5,  8, 10,  9,  2,  4,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  4,  9, 10,  8,  5,  3,  6], dtype=int64)]
2025-08-05 10:28:34,144 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:34,144 - ExploitationExpert - INFO - populations: [{'tour': [1, 8, 0, 2, 4, 7, 3, 5, 10, 6, 9], 'cur_cost': 1187.0}, {'tour': array([ 3,  8,  9,  2,  0, 10,  5,  6,  1,  4,  7], dtype=int64), 'cur_cost': 1168.0}, {'tour': [4, 9, 10, 3, 6, 8, 0, 1, 2, 7, 5], 'cur_cost': 1208.0}, {'tour': [8, 5, 6, 3, 10, 9, 4, 2, 7, 1, 0], 'cur_cost': 975.0}, {'tour': [4, 10, 6, 7, 1, 5, 3, 2, 9, 8, 0], 'cur_cost': 1428.0}, {'tour': [4, 8, 0, 9, 1, 10, 5, 3, 6, 2, 7], 'cur_cost': 1282.0}, {'tour': array([ 8,  1,  6, 10,  5,  0,  9,  4,  2,  3,  7], dtype=int64), 'cur_cost': 1359.0}, {'tour': [8, 7, 0, 3, 5, 1, 6, 10, 4, 9, 2], 'cur_cost': 1531.0}, {'tour': [5, 9, 2, 6, 10, 8, 3, 1, 7, 4, 0], 'cur_cost': 1145.0}, {'tour': [2, 1, 3, 10, 8, 6, 0, 5, 9, 4, 7], 'cur_cost': 873.0}]
2025-08-05 10:28:34,144 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:34,144 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 48, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 48, 'cache_hits': 0, 'similarity_calculations': 324, 'cache_hit_rate': 0.0, 'cache_size': 324}}
2025-08-05 10:28:34,145 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([ 8,  1,  6, 10,  5,  0,  9,  4,  2,  3,  7], dtype=int64), 'cur_cost': 1359.0, 'intermediate_solutions': [{'tour': array([ 4,  1,  9,  0,  8,  2, 10,  5,  7,  6,  3]), 'cur_cost': 1555.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 0,  4,  1,  9,  8,  2, 10,  5,  7,  6,  3]), 'cur_cost': 1561.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 8,  0,  4,  1,  9,  2, 10,  5,  7,  6,  3]), 'cur_cost': 1464.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 9,  0,  4,  1,  8,  2, 10,  5,  7,  6,  3]), 'cur_cost': 1621.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 9,  8,  0,  4,  1,  2, 10,  5,  7,  6,  3]), 'cur_cost': 1418.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:34,145 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1359.00)
2025-08-05 10:28:34,145 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:28:34,145 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:34,145 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:34,145 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 1541.0
2025-08-05 10:28:34,151 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:34,151 - ExploitationExpert - INFO - res_population_costs: [803.0, 803, 803.0, 803]
2025-08-05 10:28:34,152 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  8, 10,  5,  3,  9,  2,  4,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  4,  9,  3,  5, 10,  8,  6], dtype=int64), array([ 0,  6,  3,  5,  8, 10,  9,  2,  4,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  4,  9, 10,  8,  5,  3,  6], dtype=int64)]
2025-08-05 10:28:34,152 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:34,152 - ExploitationExpert - INFO - populations: [{'tour': [1, 8, 0, 2, 4, 7, 3, 5, 10, 6, 9], 'cur_cost': 1187.0}, {'tour': array([ 3,  8,  9,  2,  0, 10,  5,  6,  1,  4,  7], dtype=int64), 'cur_cost': 1168.0}, {'tour': [4, 9, 10, 3, 6, 8, 0, 1, 2, 7, 5], 'cur_cost': 1208.0}, {'tour': [8, 5, 6, 3, 10, 9, 4, 2, 7, 1, 0], 'cur_cost': 975.0}, {'tour': [4, 10, 6, 7, 1, 5, 3, 2, 9, 8, 0], 'cur_cost': 1428.0}, {'tour': [4, 8, 0, 9, 1, 10, 5, 3, 6, 2, 7], 'cur_cost': 1282.0}, {'tour': array([ 8,  1,  6, 10,  5,  0,  9,  4,  2,  3,  7], dtype=int64), 'cur_cost': 1359.0}, {'tour': array([ 7,  6,  4,  5,  0,  8,  1,  3, 10,  2,  9], dtype=int64), 'cur_cost': 1541.0}, {'tour': [5, 9, 2, 6, 10, 8, 3, 1, 7, 4, 0], 'cur_cost': 1145.0}, {'tour': [2, 1, 3, 10, 8, 6, 0, 5, 9, 4, 7], 'cur_cost': 873.0}]
2025-08-05 10:28:34,153 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:34,153 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 49, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 49, 'cache_hits': 0, 'similarity_calculations': 333, 'cache_hit_rate': 0.0, 'cache_size': 333}}
2025-08-05 10:28:34,154 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([ 7,  6,  4,  5,  0,  8,  1,  3, 10,  2,  9], dtype=int64), 'cur_cost': 1541.0, 'intermediate_solutions': [{'tour': array([ 0,  7,  8,  3,  5,  1,  6, 10,  4,  9,  2]), 'cur_cost': 1513.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 3,  0,  7,  8,  5,  1,  6, 10,  4,  9,  2]), 'cur_cost': 1434.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 5,  3,  0,  7,  8,  1,  6, 10,  4,  9,  2]), 'cur_cost': 1523.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 8,  3,  0,  7,  5,  1,  6, 10,  4,  9,  2]), 'cur_cost': 1531.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 8,  5,  3,  0,  7,  1,  6, 10,  4,  9,  2]), 'cur_cost': 1344.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:34,154 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1541.00)
2025-08-05 10:28:34,154 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:34,154 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:34,154 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:34,155 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 10:28:34,155 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,155 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,155 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,155 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,155 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1302.0, 路径长度: 11, 收集中间解: 3
2025-08-05 10:28:34,155 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [4, 8, 0, 7, 1, 3, 5, 10, 6, 2, 9], 'cur_cost': 1302.0, 'intermediate_solutions': [{'tour': [5, 9, 2, 6, 10, 3, 8, 1, 7, 4, 0], 'cur_cost': 1278.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 10, 6, 2, 9, 8, 3, 1, 7, 4, 0], 'cur_cost': 1179.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 9, 2, 6, 10, 3, 1, 7, 4, 0, 8], 'cur_cost': 1146.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:34,156 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1302.00)
2025-08-05 10:28:34,156 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:34,156 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:34,156 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:34,156 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 10:28:34,156 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,156 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,156 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,157 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,157 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1142.0, 路径长度: 11, 收集中间解: 3
2025-08-05 10:28:34,157 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [2, 10, 5, 0, 4, 7, 1, 3, 6, 8, 9], 'cur_cost': 1142.0, 'intermediate_solutions': [{'tour': [2, 6, 3, 10, 8, 1, 0, 5, 9, 4, 7], 'cur_cost': 1073.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 1, 3, 10, 4, 9, 5, 0, 6, 8, 7], 'cur_cost': 1226.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 1, 3, 4, 10, 8, 6, 0, 5, 9, 7], 'cur_cost': 1113.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:34,157 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 1142.00)
2025-08-05 10:28:34,157 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:34,157 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:34,158 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [1, 8, 0, 2, 4, 7, 3, 5, 10, 6, 9], 'cur_cost': 1187.0, 'intermediate_solutions': [{'tour': [2, 5, 0, 3, 6, 8, 10, 7, 4, 9, 1], 'cur_cost': 1166.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 5, 0, 3, 6, 8, 10, 9, 4, 1, 7], 'cur_cost': 1075.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 5, 0, 3, 8, 10, 6, 9, 4, 7, 1], 'cur_cost': 1114.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 3,  8,  9,  2,  0, 10,  5,  6,  1,  4,  7], dtype=int64), 'cur_cost': 1168.0, 'intermediate_solutions': [{'tour': array([ 9, 10,  6,  0,  5,  7,  1,  2,  8,  4,  3]), 'cur_cost': 1376.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 0,  9, 10,  6,  5,  7,  1,  2,  8,  4,  3]), 'cur_cost': 1487.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 5,  0,  9, 10,  6,  7,  1,  2,  8,  4,  3]), 'cur_cost': 1483.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6,  0,  9, 10,  5,  7,  1,  2,  8,  4,  3]), 'cur_cost': 1384.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6,  5,  0,  9, 10,  7,  1,  2,  8,  4,  3]), 'cur_cost': 1495.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [4, 9, 10, 3, 6, 8, 0, 1, 2, 7, 5], 'cur_cost': 1208.0, 'intermediate_solutions': [{'tour': [1, 6, 4, 3, 7, 2, 9, 10, 8, 5, 0], 'cur_cost': 1160.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 10, 9, 2, 7, 3, 4, 5, 8, 0], 'cur_cost': 1201.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 4, 3, 7, 2, 9, 5, 10, 8, 0], 'cur_cost': 1161.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [8, 5, 6, 3, 10, 9, 4, 2, 7, 1, 0], 'cur_cost': 975.0, 'intermediate_solutions': [{'tour': [0, 6, 5, 10, 1, 8, 3, 2, 7, 4, 9], 'cur_cost': 1101.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 10, 5, 6, 1, 3, 2, 7, 4, 9], 'cur_cost': 966.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 5, 10, 8, 1, 9, 3, 2, 7, 4], 'cur_cost': 1022.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [4, 10, 6, 7, 1, 5, 3, 2, 9, 8, 0], 'cur_cost': 1428.0, 'intermediate_solutions': [{'tour': [9, 6, 0, 7, 8, 10, 5, 3, 1, 2, 4], 'cur_cost': 1129.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 9, 0, 7, 8, 10, 2, 1, 3, 5, 4], 'cur_cost': 1575.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 9, 7, 0, 8, 10, 5, 3, 1, 2, 4], 'cur_cost': 1188.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [4, 8, 0, 9, 1, 10, 5, 3, 6, 2, 7], 'cur_cost': 1282.0, 'intermediate_solutions': [{'tour': [1, 6, 5, 8, 3, 10, 0, 9, 2, 7, 4], 'cur_cost': 1102.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 0, 8, 2, 9, 5, 10, 3, 7, 4], 'cur_cost': 1092.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 0, 8, 10, 3, 5, 9, 2, 7, 4], 'cur_cost': 905.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 8,  1,  6, 10,  5,  0,  9,  4,  2,  3,  7], dtype=int64), 'cur_cost': 1359.0, 'intermediate_solutions': [{'tour': array([ 4,  1,  9,  0,  8,  2, 10,  5,  7,  6,  3]), 'cur_cost': 1555.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 0,  4,  1,  9,  8,  2, 10,  5,  7,  6,  3]), 'cur_cost': 1561.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 8,  0,  4,  1,  9,  2, 10,  5,  7,  6,  3]), 'cur_cost': 1464.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 9,  0,  4,  1,  8,  2, 10,  5,  7,  6,  3]), 'cur_cost': 1621.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 9,  8,  0,  4,  1,  2, 10,  5,  7,  6,  3]), 'cur_cost': 1418.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 7,  6,  4,  5,  0,  8,  1,  3, 10,  2,  9], dtype=int64), 'cur_cost': 1541.0, 'intermediate_solutions': [{'tour': array([ 0,  7,  8,  3,  5,  1,  6, 10,  4,  9,  2]), 'cur_cost': 1513.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 3,  0,  7,  8,  5,  1,  6, 10,  4,  9,  2]), 'cur_cost': 1434.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 5,  3,  0,  7,  8,  1,  6, 10,  4,  9,  2]), 'cur_cost': 1523.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 8,  3,  0,  7,  5,  1,  6, 10,  4,  9,  2]), 'cur_cost': 1531.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 8,  5,  3,  0,  7,  1,  6, 10,  4,  9,  2]), 'cur_cost': 1344.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [4, 8, 0, 7, 1, 3, 5, 10, 6, 2, 9], 'cur_cost': 1302.0, 'intermediate_solutions': [{'tour': [5, 9, 2, 6, 10, 3, 8, 1, 7, 4, 0], 'cur_cost': 1278.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 10, 6, 2, 9, 8, 3, 1, 7, 4, 0], 'cur_cost': 1179.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 9, 2, 6, 10, 3, 1, 7, 4, 0, 8], 'cur_cost': 1146.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [2, 10, 5, 0, 4, 7, 1, 3, 6, 8, 9], 'cur_cost': 1142.0, 'intermediate_solutions': [{'tour': [2, 6, 3, 10, 8, 1, 0, 5, 9, 4, 7], 'cur_cost': 1073.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 1, 3, 10, 4, 9, 5, 0, 6, 8, 7], 'cur_cost': 1226.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 1, 3, 4, 10, 8, 6, 0, 5, 9, 7], 'cur_cost': 1113.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:34,159 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:34,159 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:34,160 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=975.000, 多样性=0.881
2025-08-05 10:28:34,160 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 10:28:34,160 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 10:28:34,160 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:34,160 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.057596983893113844, 'best_improvement': -0.11683848797250859}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.015801354401805724}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.04749139612108597, 'recent_improvements': [-0.026004099754075378, -0.03828675133481956, 0.06897869248809659], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 803.0, 'new_best_cost': 803.0, 'quality_improvement': 0.0, 'old_diversity': 0.7272727272727273, 'new_diversity': 0.7272727272727273, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:34,161 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 10:28:34,161 - __main__ - INFO - simple4_11 开始进化第 5 代
2025-08-05 10:28:34,161 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 10:28:34,161 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:34,162 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=975.000, 多样性=0.881
2025-08-05 10:28:34,162 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:34,162 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.881
2025-08-05 10:28:34,163 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:34,163 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.727
2025-08-05 10:28:34,165 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 10:28:34,165 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:34,166 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 10:28:34,166 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 10:28:34,177 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.429, 适应度梯度: 63.400, 聚类评分: 0.000, 覆盖率: 0.022, 收敛趋势: 0.000, 多样性: 0.697
2025-08-05 10:28:34,177 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 10:28:34,177 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:34,177 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple4_11
2025-08-05 10:28:34,181 - visualization.landscape_visualizer - INFO - 插值约束: 38 个点被约束到最小值 803.00
2025-08-05 10:28:34,182 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=9.4%, 梯度: 27.68 → 25.08
2025-08-05 10:28:34,315 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple4_11\landscape_simple4_11_iter_20_20250805_102834.html
2025-08-05 10:28:34,374 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple4_11\dashboard_simple4_11_iter_20_20250805_102834.html
2025-08-05 10:28:34,374 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 20
2025-08-05 10:28:34,375 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 10:28:34,375 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2099秒
2025-08-05 10:28:34,375 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.42857142857142855, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 63.39999999999999, 'local_optima_density': 0.42857142857142855, 'gradient_variance': 64378.93142857143, 'cluster_count': 0}, 'population_state': {'diversity': 0.6970172684458399, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0222, 'fitness_entropy': 0.9357849740192012, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.022)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 63.400)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360914.1776948, 'performance_metrics': {}}}
2025-08-05 10:28:34,375 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:34,375 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:34,375 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:34,375 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:34,375 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 10:28:34,376 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 10:28:34,376 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 10:28:34,376 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:34,376 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:34,376 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 10:28:34,376 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:34,376 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:34,376 - experts.management.collaboration_manager - INFO - 识别精英个体: {9, 3} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:34,377 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:34,377 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:34,377 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:34,377 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 11
2025-08-05 10:28:34,377 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,378 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,378 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,378 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,379 - ExplorationExpert - INFO - 探索路径生成完成，成本: 973.0, 路径长度: 11, 收集中间解: 3
2025-08-05 10:28:34,379 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [7, 4, 3, 6, 0, 8, 10, 5, 1, 2, 9], 'cur_cost': 973.0, 'intermediate_solutions': [{'tour': [1, 8, 0, 2, 3, 7, 4, 5, 10, 6, 9], 'cur_cost': 1331.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 0, 10, 5, 3, 7, 4, 2, 6, 9], 'cur_cost': 1211.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 8, 0, 2, 4, 7, 3, 5, 10, 6, 9], 'cur_cost': 1187.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:34,380 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 973.00)
2025-08-05 10:28:34,380 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:34,380 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:34,380 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:34,380 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 10:28:34,381 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,381 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,381 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,381 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,381 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1166.0, 路径长度: 11, 收集中间解: 3
2025-08-05 10:28:34,381 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 7, 3, 8, 10, 5, 6, 1, 2, 4, 9], 'cur_cost': 1166.0, 'intermediate_solutions': [{'tour': [9, 8, 3, 2, 0, 10, 5, 6, 1, 4, 7], 'cur_cost': 1168.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 9, 7, 4, 1, 6, 5, 10, 0, 2], 'cur_cost': 1168.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 8, 9, 2, 0, 10, 5, 6, 4, 7, 1], 'cur_cost': 1185.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:34,381 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1166.00)
2025-08-05 10:28:34,381 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:34,381 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:34,382 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:34,382 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 10:28:34,382 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,382 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,382 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,382 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,383 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1353.0, 路径长度: 11, 收集中间解: 3
2025-08-05 10:28:34,383 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [1, 9, 5, 7, 0, 2, 4, 3, 6, 8, 10], 'cur_cost': 1353.0, 'intermediate_solutions': [{'tour': [10, 9, 4, 3, 6, 8, 0, 1, 2, 7, 5], 'cur_cost': 1115.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 9, 10, 3, 6, 8, 1, 0, 2, 7, 5], 'cur_cost': 1335.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 9, 10, 3, 6, 7, 8, 0, 1, 2, 5], 'cur_cost': 1425.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:34,383 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1353.00)
2025-08-05 10:28:34,383 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:34,383 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:34,383 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:34,383 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 11
2025-08-05 10:28:34,384 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,384 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,384 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,384 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,384 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1067.0, 路径长度: 11, 收集中间解: 3
2025-08-05 10:28:34,384 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [6, 3, 2, 4, 7, 5, 10, 8, 0, 1, 9], 'cur_cost': 1067.0, 'intermediate_solutions': [{'tour': [8, 5, 6, 3, 1, 9, 4, 2, 7, 10, 0], 'cur_cost': 1156.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 5, 6, 3, 10, 9, 4, 2, 7, 0, 1], 'cur_cost': 1104.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 5, 3, 10, 9, 4, 2, 7, 1, 0, 6], 'cur_cost': 906.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:34,384 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 1067.00)
2025-08-05 10:28:34,384 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:28:34,384 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:34,385 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:34,385 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 1316.0
2025-08-05 10:28:34,391 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:34,391 - ExploitationExpert - INFO - res_population_costs: [803.0, 803, 803.0, 803]
2025-08-05 10:28:34,391 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  8, 10,  5,  3,  9,  2,  4,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  4,  9,  3,  5, 10,  8,  6], dtype=int64), array([ 0,  6,  3,  5,  8, 10,  9,  2,  4,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  4,  9, 10,  8,  5,  3,  6], dtype=int64)]
2025-08-05 10:28:34,392 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:34,392 - ExploitationExpert - INFO - populations: [{'tour': [7, 4, 3, 6, 0, 8, 10, 5, 1, 2, 9], 'cur_cost': 973.0}, {'tour': [0, 7, 3, 8, 10, 5, 6, 1, 2, 4, 9], 'cur_cost': 1166.0}, {'tour': [1, 9, 5, 7, 0, 2, 4, 3, 6, 8, 10], 'cur_cost': 1353.0}, {'tour': [6, 3, 2, 4, 7, 5, 10, 8, 0, 1, 9], 'cur_cost': 1067.0}, {'tour': array([ 2, 10,  4,  8,  5,  0,  6,  3,  9,  1,  7], dtype=int64), 'cur_cost': 1316.0}, {'tour': [4, 8, 0, 9, 1, 10, 5, 3, 6, 2, 7], 'cur_cost': 1282.0}, {'tour': [8, 1, 6, 10, 5, 0, 9, 4, 2, 3, 7], 'cur_cost': 1359.0}, {'tour': [7, 6, 4, 5, 0, 8, 1, 3, 10, 2, 9], 'cur_cost': 1541.0}, {'tour': [4, 8, 0, 7, 1, 3, 5, 10, 6, 2, 9], 'cur_cost': 1302.0}, {'tour': [2, 10, 5, 0, 4, 7, 1, 3, 6, 8, 9], 'cur_cost': 1142.0}]
2025-08-05 10:28:34,392 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:34,392 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 50, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 50, 'cache_hits': 0, 'similarity_calculations': 343, 'cache_hit_rate': 0.0, 'cache_size': 343}}
2025-08-05 10:28:34,393 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([ 2, 10,  4,  8,  5,  0,  6,  3,  9,  1,  7], dtype=int64), 'cur_cost': 1316.0, 'intermediate_solutions': [{'tour': array([ 6, 10,  4,  7,  1,  5,  3,  2,  9,  8,  0]), 'cur_cost': 1120.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 7,  6, 10,  4,  1,  5,  3,  2,  9,  8,  0]), 'cur_cost': 1427.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1,  7,  6, 10,  4,  5,  3,  2,  9,  8,  0]), 'cur_cost': 1387.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4,  7,  6, 10,  1,  5,  3,  2,  9,  8,  0]), 'cur_cost': 1309.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4,  1,  7,  6, 10,  5,  3,  2,  9,  8,  0]), 'cur_cost': 1274.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:34,393 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1316.00)
2025-08-05 10:28:34,393 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:34,393 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:34,393 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:34,394 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 10:28:34,394 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,394 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,394 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,394 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,394 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1253.0, 路径长度: 11, 收集中间解: 3
2025-08-05 10:28:34,394 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 3, 10, 4, 6, 5, 8, 9, 2, 7, 1], 'cur_cost': 1253.0, 'intermediate_solutions': [{'tour': [4, 8, 3, 9, 1, 10, 5, 0, 6, 2, 7], 'cur_cost': 1204.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 2, 6, 3, 5, 10, 1, 9, 0, 8, 7], 'cur_cost': 1279.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 8, 0, 9, 1, 10, 5, 6, 2, 3, 7], 'cur_cost': 1366.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:34,395 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1253.00)
2025-08-05 10:28:34,395 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:34,395 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:34,395 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:34,395 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 11
2025-08-05 10:28:34,395 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,395 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,396 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,396 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,396 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1083.0, 路径长度: 11, 收集中间解: 3
2025-08-05 10:28:34,396 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [1, 6, 8, 3, 10, 9, 5, 0, 2, 7, 4], 'cur_cost': 1083.0, 'intermediate_solutions': [{'tour': [8, 9, 6, 10, 5, 0, 1, 4, 2, 3, 7], 'cur_cost': 1294.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 1, 6, 10, 5, 0, 9, 3, 2, 4, 7], 'cur_cost': 1234.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 1, 6, 10, 5, 0, 9, 4, 2, 3, 7], 'cur_cost': 1359.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:34,396 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1083.00)
2025-08-05 10:28:34,396 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:28:34,396 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:34,396 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:34,397 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 1502.0
2025-08-05 10:28:34,402 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:34,402 - ExploitationExpert - INFO - res_population_costs: [803.0, 803, 803.0, 803]
2025-08-05 10:28:34,402 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  8, 10,  5,  3,  9,  2,  4,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  4,  9,  3,  5, 10,  8,  6], dtype=int64), array([ 0,  6,  3,  5,  8, 10,  9,  2,  4,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  4,  9, 10,  8,  5,  3,  6], dtype=int64)]
2025-08-05 10:28:34,403 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:34,403 - ExploitationExpert - INFO - populations: [{'tour': [7, 4, 3, 6, 0, 8, 10, 5, 1, 2, 9], 'cur_cost': 973.0}, {'tour': [0, 7, 3, 8, 10, 5, 6, 1, 2, 4, 9], 'cur_cost': 1166.0}, {'tour': [1, 9, 5, 7, 0, 2, 4, 3, 6, 8, 10], 'cur_cost': 1353.0}, {'tour': [6, 3, 2, 4, 7, 5, 10, 8, 0, 1, 9], 'cur_cost': 1067.0}, {'tour': array([ 2, 10,  4,  8,  5,  0,  6,  3,  9,  1,  7], dtype=int64), 'cur_cost': 1316.0}, {'tour': [0, 3, 10, 4, 6, 5, 8, 9, 2, 7, 1], 'cur_cost': 1253.0}, {'tour': [1, 6, 8, 3, 10, 9, 5, 0, 2, 7, 4], 'cur_cost': 1083.0}, {'tour': array([ 7,  5, 10,  4,  3,  0,  1,  9,  2,  6,  8], dtype=int64), 'cur_cost': 1502.0}, {'tour': [4, 8, 0, 7, 1, 3, 5, 10, 6, 2, 9], 'cur_cost': 1302.0}, {'tour': [2, 10, 5, 0, 4, 7, 1, 3, 6, 8, 9], 'cur_cost': 1142.0}]
2025-08-05 10:28:34,404 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:34,404 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 51, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 51, 'cache_hits': 0, 'similarity_calculations': 354, 'cache_hit_rate': 0.0, 'cache_size': 354}}
2025-08-05 10:28:34,404 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([ 7,  5, 10,  4,  3,  0,  1,  9,  2,  6,  8], dtype=int64), 'cur_cost': 1502.0, 'intermediate_solutions': [{'tour': array([ 4,  6,  7,  5,  0,  8,  1,  3, 10,  2,  9]), 'cur_cost': 1514.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 5,  4,  6,  7,  0,  8,  1,  3, 10,  2,  9]), 'cur_cost': 1566.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 0,  5,  4,  6,  7,  8,  1,  3, 10,  2,  9]), 'cur_cost': 1698.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 7,  5,  4,  6,  0,  8,  1,  3, 10,  2,  9]), 'cur_cost': 1478.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 7,  0,  5,  4,  6,  8,  1,  3, 10,  2,  9]), 'cur_cost': 1511.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:34,404 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1502.00)
2025-08-05 10:28:34,405 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:34,405 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:34,405 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:34,405 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 1410.0
2025-08-05 10:28:34,411 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:34,411 - ExploitationExpert - INFO - res_population_costs: [803.0, 803, 803.0, 803]
2025-08-05 10:28:34,411 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  8, 10,  5,  3,  9,  2,  4,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  4,  9,  3,  5, 10,  8,  6], dtype=int64), array([ 0,  6,  3,  5,  8, 10,  9,  2,  4,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  4,  9, 10,  8,  5,  3,  6], dtype=int64)]
2025-08-05 10:28:34,412 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:34,412 - ExploitationExpert - INFO - populations: [{'tour': [7, 4, 3, 6, 0, 8, 10, 5, 1, 2, 9], 'cur_cost': 973.0}, {'tour': [0, 7, 3, 8, 10, 5, 6, 1, 2, 4, 9], 'cur_cost': 1166.0}, {'tour': [1, 9, 5, 7, 0, 2, 4, 3, 6, 8, 10], 'cur_cost': 1353.0}, {'tour': [6, 3, 2, 4, 7, 5, 10, 8, 0, 1, 9], 'cur_cost': 1067.0}, {'tour': array([ 2, 10,  4,  8,  5,  0,  6,  3,  9,  1,  7], dtype=int64), 'cur_cost': 1316.0}, {'tour': [0, 3, 10, 4, 6, 5, 8, 9, 2, 7, 1], 'cur_cost': 1253.0}, {'tour': [1, 6, 8, 3, 10, 9, 5, 0, 2, 7, 4], 'cur_cost': 1083.0}, {'tour': array([ 7,  5, 10,  4,  3,  0,  1,  9,  2,  6,  8], dtype=int64), 'cur_cost': 1502.0}, {'tour': array([ 3,  0,  8,  2,  5, 10,  7,  4,  6,  9,  1], dtype=int64), 'cur_cost': 1410.0}, {'tour': [2, 10, 5, 0, 4, 7, 1, 3, 6, 8, 9], 'cur_cost': 1142.0}]
2025-08-05 10:28:34,413 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:34,413 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 52, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 52, 'cache_hits': 0, 'similarity_calculations': 366, 'cache_hit_rate': 0.0, 'cache_size': 366}}
2025-08-05 10:28:34,414 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 3,  0,  8,  2,  5, 10,  7,  4,  6,  9,  1], dtype=int64), 'cur_cost': 1410.0, 'intermediate_solutions': [{'tour': array([ 0,  8,  4,  7,  1,  3,  5, 10,  6,  2,  9]), 'cur_cost': 1217.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 7,  0,  8,  4,  1,  3,  5, 10,  6,  2,  9]), 'cur_cost': 1348.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1,  7,  0,  8,  4,  3,  5, 10,  6,  2,  9]), 'cur_cost': 1389.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4,  7,  0,  8,  1,  3,  5, 10,  6,  2,  9]), 'cur_cost': 1161.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4,  1,  7,  0,  8,  3,  5, 10,  6,  2,  9]), 'cur_cost': 1228.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:34,414 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1410.00)
2025-08-05 10:28:34,414 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:34,414 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:34,414 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:34,414 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 10:28:34,415 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,415 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,415 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,415 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,415 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1369.0, 路径长度: 11, 收集中间解: 3
2025-08-05 10:28:34,415 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [2, 6, 0, 4, 10, 5, 3, 1, 7, 9, 8], 'cur_cost': 1369.0, 'intermediate_solutions': [{'tour': [2, 10, 5, 0, 8, 7, 1, 3, 6, 4, 9], 'cur_cost': 1351.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 10, 5, 0, 4, 6, 3, 1, 7, 8, 9], 'cur_cost': 1454.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 5, 0, 4, 7, 1, 3, 6, 8, 2, 9], 'cur_cost': 1121.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:34,415 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 1369.00)
2025-08-05 10:28:34,415 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:34,416 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:34,417 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [7, 4, 3, 6, 0, 8, 10, 5, 1, 2, 9], 'cur_cost': 973.0, 'intermediate_solutions': [{'tour': [1, 8, 0, 2, 3, 7, 4, 5, 10, 6, 9], 'cur_cost': 1331.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 0, 10, 5, 3, 7, 4, 2, 6, 9], 'cur_cost': 1211.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 8, 0, 2, 4, 7, 3, 5, 10, 6, 9], 'cur_cost': 1187.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 3, 8, 10, 5, 6, 1, 2, 4, 9], 'cur_cost': 1166.0, 'intermediate_solutions': [{'tour': [9, 8, 3, 2, 0, 10, 5, 6, 1, 4, 7], 'cur_cost': 1168.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 9, 7, 4, 1, 6, 5, 10, 0, 2], 'cur_cost': 1168.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 8, 9, 2, 0, 10, 5, 6, 4, 7, 1], 'cur_cost': 1185.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [1, 9, 5, 7, 0, 2, 4, 3, 6, 8, 10], 'cur_cost': 1353.0, 'intermediate_solutions': [{'tour': [10, 9, 4, 3, 6, 8, 0, 1, 2, 7, 5], 'cur_cost': 1115.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 9, 10, 3, 6, 8, 1, 0, 2, 7, 5], 'cur_cost': 1335.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 9, 10, 3, 6, 7, 8, 0, 1, 2, 5], 'cur_cost': 1425.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [6, 3, 2, 4, 7, 5, 10, 8, 0, 1, 9], 'cur_cost': 1067.0, 'intermediate_solutions': [{'tour': [8, 5, 6, 3, 1, 9, 4, 2, 7, 10, 0], 'cur_cost': 1156.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 5, 6, 3, 10, 9, 4, 2, 7, 0, 1], 'cur_cost': 1104.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 5, 3, 10, 9, 4, 2, 7, 1, 0, 6], 'cur_cost': 906.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 2, 10,  4,  8,  5,  0,  6,  3,  9,  1,  7], dtype=int64), 'cur_cost': 1316.0, 'intermediate_solutions': [{'tour': array([ 6, 10,  4,  7,  1,  5,  3,  2,  9,  8,  0]), 'cur_cost': 1120.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 7,  6, 10,  4,  1,  5,  3,  2,  9,  8,  0]), 'cur_cost': 1427.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1,  7,  6, 10,  4,  5,  3,  2,  9,  8,  0]), 'cur_cost': 1387.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4,  7,  6, 10,  1,  5,  3,  2,  9,  8,  0]), 'cur_cost': 1309.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4,  1,  7,  6, 10,  5,  3,  2,  9,  8,  0]), 'cur_cost': 1274.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 10, 4, 6, 5, 8, 9, 2, 7, 1], 'cur_cost': 1253.0, 'intermediate_solutions': [{'tour': [4, 8, 3, 9, 1, 10, 5, 0, 6, 2, 7], 'cur_cost': 1204.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 2, 6, 3, 5, 10, 1, 9, 0, 8, 7], 'cur_cost': 1279.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 8, 0, 9, 1, 10, 5, 6, 2, 3, 7], 'cur_cost': 1366.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [1, 6, 8, 3, 10, 9, 5, 0, 2, 7, 4], 'cur_cost': 1083.0, 'intermediate_solutions': [{'tour': [8, 9, 6, 10, 5, 0, 1, 4, 2, 3, 7], 'cur_cost': 1294.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 1, 6, 10, 5, 0, 9, 3, 2, 4, 7], 'cur_cost': 1234.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 1, 6, 10, 5, 0, 9, 4, 2, 3, 7], 'cur_cost': 1359.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 7,  5, 10,  4,  3,  0,  1,  9,  2,  6,  8], dtype=int64), 'cur_cost': 1502.0, 'intermediate_solutions': [{'tour': array([ 4,  6,  7,  5,  0,  8,  1,  3, 10,  2,  9]), 'cur_cost': 1514.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 5,  4,  6,  7,  0,  8,  1,  3, 10,  2,  9]), 'cur_cost': 1566.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 0,  5,  4,  6,  7,  8,  1,  3, 10,  2,  9]), 'cur_cost': 1698.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 7,  5,  4,  6,  0,  8,  1,  3, 10,  2,  9]), 'cur_cost': 1478.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 7,  0,  5,  4,  6,  8,  1,  3, 10,  2,  9]), 'cur_cost': 1511.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 3,  0,  8,  2,  5, 10,  7,  4,  6,  9,  1], dtype=int64), 'cur_cost': 1410.0, 'intermediate_solutions': [{'tour': array([ 0,  8,  4,  7,  1,  3,  5, 10,  6,  2,  9]), 'cur_cost': 1217.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 7,  0,  8,  4,  1,  3,  5, 10,  6,  2,  9]), 'cur_cost': 1348.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1,  7,  0,  8,  4,  3,  5, 10,  6,  2,  9]), 'cur_cost': 1389.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4,  7,  0,  8,  1,  3,  5, 10,  6,  2,  9]), 'cur_cost': 1161.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4,  1,  7,  0,  8,  3,  5, 10,  6,  2,  9]), 'cur_cost': 1228.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [2, 6, 0, 4, 10, 5, 3, 1, 7, 9, 8], 'cur_cost': 1369.0, 'intermediate_solutions': [{'tour': [2, 10, 5, 0, 8, 7, 1, 3, 6, 4, 9], 'cur_cost': 1351.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 10, 5, 0, 4, 6, 3, 1, 7, 8, 9], 'cur_cost': 1454.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 5, 0, 4, 7, 1, 3, 6, 8, 2, 9], 'cur_cost': 1121.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:34,417 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:34,417 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:34,418 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=973.000, 多样性=0.903
2025-08-05 10:28:34,418 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 10:28:34,418 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 10:28:34,418 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:34,419 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.011767024547855491, 'best_improvement': 0.0020512820512820513}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.025229357798164792}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.009655116279147141, 'recent_improvements': [-0.03828675133481956, 0.06897869248809659, -0.057596983893113844], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 803.0, 'new_best_cost': 803.0, 'quality_improvement': 0.0, 'old_diversity': 0.7272727272727273, 'new_diversity': 0.7272727272727273, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:34,419 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 10:28:34,421 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple4_11_solution.json
2025-08-05 10:28:34,421 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple4_11_20250805_102834.solution
2025-08-05 10:28:34,421 - __main__ - INFO - 实例执行完成 - 运行时间: 1.40s, 最佳成本: 803.0
2025-08-05 10:28:34,421 - __main__ - INFO - 实例 simple4_11 处理完成
