2025-08-04 17:36:40,779 - __main__ - INFO - simple1_9 开始进化第 1 代
2025-08-04 17:36:40,780 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-04 17:36:40,785 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:36:40,789 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=681.000, 多样性=0.857
2025-08-04 17:36:40,792 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:36:40,794 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.857
2025-08-04 17:36:40,795 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:36:40,817 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-04 17:36:40,817 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:36:40,817 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-04 17:36:40,818 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-04 17:36:41,121 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -44.960, 聚类评分: 0.000, 覆盖率: 0.001, 收敛趋势: 0.000, 多样性: 0.771
2025-08-04 17:36:41,121 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-04 17:36:41,122 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-04 17:36:41,122 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:36:41,556 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记，坐标系统已统一
2025-08-04 17:36:45,655 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_1_20250804_173645.html
2025-08-04 17:36:45,694 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_1_20250804_173645.html
2025-08-04 17:36:45,695 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-04 17:36:45,695 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-04 17:36:45,696 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 4.8789秒
2025-08-04 17:36:45,696 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-04 17:36:45,697 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -44.959999999999994, 'local_optima_density': 0.1, 'gradient_variance': 35058.9584, 'cluster_count': 0}, 'population_state': {'diversity': 0.7711111111111111, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.001, 'fitness_entropy': 0.9739760316291207, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -44.960)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.001)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.771)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754300201.12185, 'performance_metrics': {}}}
2025-08-04 17:36:45,698 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:36:45,698 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:36:45,698 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:36:45,699 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:36:45,699 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-04 17:36:45,699 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-04 17:36:45,700 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-04 17:36:45,700 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:36:45,700 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:36:45,700 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-04 17:36:45,701 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:36:45,701 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:36:45,701 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-04 17:36:45,702 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:36:45,702 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:36:45,702 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:45,708 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:36:45,708 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:45,855 - ExplorationExpert - INFO - 探索路径生成完成，成本: 932.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:36:45,855 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [1, 7, 0, 8, 6, 5, 3, 4, 2], 'cur_cost': 932.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:36:45,855 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 932.00)
2025-08-04 17:36:45,855 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:36:45,855 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:36:45,855 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:45,856 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:36:45,856 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:45,857 - ExplorationExpert - INFO - 探索路径生成完成，成本: 757.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:36:45,857 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [1, 0, 7, 6, 3, 5, 8, 4, 2], 'cur_cost': 757.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:36:45,857 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 757.00)
2025-08-04 17:36:45,857 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:36:45,858 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:36:45,858 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:45,858 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:36:45,859 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:45,859 - ExplorationExpert - INFO - 探索路径生成完成，成本: 945.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:36:45,859 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [5, 7, 1, 6, 2, 4, 0, 3, 8], 'cur_cost': 945.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:36:45,859 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 945.00)
2025-08-04 17:36:45,859 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-04 17:36:45,860 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:36:45,902 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:36:45,904 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 882.0
2025-08-04 17:36:47,564 - ExploitationExpert - INFO - res_population_num: 1
2025-08-04 17:36:47,564 - ExploitationExpert - INFO - res_population_costs: [778.0]
2025-08-04 17:36:47,564 - ExploitationExpert - INFO - res_populations: [array([0, 1, 2, 4, 7, 5, 3, 8, 6], dtype=int64)]
2025-08-04 17:36:47,565 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:36:47,565 - ExploitationExpert - INFO - populations: [{'tour': [1, 7, 0, 8, 6, 5, 3, 4, 2], 'cur_cost': 932.0}, {'tour': [1, 0, 7, 6, 3, 5, 8, 4, 2], 'cur_cost': 757.0}, {'tour': [5, 7, 1, 6, 2, 4, 0, 3, 8], 'cur_cost': 945.0}, {'tour': array([2, 8, 3, 5, 0, 1, 4, 7, 6], dtype=int64), 'cur_cost': 882.0}, {'tour': array([4, 5, 0, 2, 6, 7, 3, 8, 1], dtype=int64), 'cur_cost': 1088.0}, {'tour': array([6, 3, 4, 8, 2, 5, 1, 0, 7], dtype=int64), 'cur_cost': 963.0}, {'tour': array([0, 1, 3, 6, 4, 5, 2, 8, 7], dtype=int64), 'cur_cost': 1042.0}, {'tour': array([3, 1, 8, 5, 0, 2, 6, 7, 4], dtype=int64), 'cur_cost': 1180.0}, {'tour': array([4, 5, 7, 8, 0, 1, 2, 3, 6], dtype=int64), 'cur_cost': 1015.0}, {'tour': array([1, 7, 4, 0, 6, 5, 3, 2, 8], dtype=int64), 'cur_cost': 958.0}]
2025-08-04 17:36:47,566 - ExploitationExpert - INFO - 局部搜索耗时: 1.66秒，最大迭代次数: 10
2025-08-04 17:36:47,566 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-04 17:36:47,567 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([2, 8, 3, 5, 0, 1, 4, 7, 6], dtype=int64), 'cur_cost': 882.0, 'intermediate_solutions': [{'tour': array([8, 1, 4, 6, 0, 3, 5, 2, 7], dtype=int64), 'cur_cost': 1104.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 8, 1, 4, 0, 3, 5, 2, 7], dtype=int64), 'cur_cost': 1078.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 6, 8, 1, 4, 3, 5, 2, 7], dtype=int64), 'cur_cost': 1090.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 6, 8, 1, 0, 3, 5, 2, 7], dtype=int64), 'cur_cost': 1091.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 0, 6, 8, 1, 3, 5, 2, 7], dtype=int64), 'cur_cost': 1122.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:36:47,568 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 882.00)
2025-08-04 17:36:47,568 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:36:47,568 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:36:47,568 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:47,569 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:36:47,569 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:47,569 - ExplorationExpert - INFO - 探索路径生成完成，成本: 971.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:36:47,569 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [3, 8, 5, 0, 4, 7, 6, 1, 2], 'cur_cost': 971.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:36:47,570 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 971.00)
2025-08-04 17:36:47,570 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:36:47,570 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:36:47,570 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:47,570 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:36:47,571 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:47,571 - ExplorationExpert - INFO - 探索路径生成完成，成本: 957.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:36:47,571 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 2, 6, 8, 3, 7, 5, 4, 1], 'cur_cost': 957.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:36:47,571 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 957.00)
2025-08-04 17:36:47,571 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:36:47,572 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:36:47,572 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:47,572 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:36:47,572 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:47,573 - ExplorationExpert - INFO - 探索路径生成完成，成本: 828.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:36:47,573 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [4, 7, 3, 6, 5, 0, 1, 2, 8], 'cur_cost': 828.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:36:47,573 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 828.00)
2025-08-04 17:36:47,573 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-04 17:36:47,573 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:36:47,574 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:36:47,574 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 924.0
2025-08-04 17:36:49,517 - ExploitationExpert - INFO - res_population_num: 2
2025-08-04 17:36:49,517 - ExploitationExpert - INFO - res_population_costs: [778.0, 680.0]
2025-08-04 17:36:49,517 - ExploitationExpert - INFO - res_populations: [array([0, 1, 2, 4, 7, 5, 3, 8, 6], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-04 17:36:49,518 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:36:49,518 - ExploitationExpert - INFO - populations: [{'tour': [1, 7, 0, 8, 6, 5, 3, 4, 2], 'cur_cost': 932.0}, {'tour': [1, 0, 7, 6, 3, 5, 8, 4, 2], 'cur_cost': 757.0}, {'tour': [5, 7, 1, 6, 2, 4, 0, 3, 8], 'cur_cost': 945.0}, {'tour': array([2, 8, 3, 5, 0, 1, 4, 7, 6], dtype=int64), 'cur_cost': 882.0}, {'tour': [3, 8, 5, 0, 4, 7, 6, 1, 2], 'cur_cost': 971.0}, {'tour': [0, 2, 6, 8, 3, 7, 5, 4, 1], 'cur_cost': 957.0}, {'tour': [4, 7, 3, 6, 5, 0, 1, 2, 8], 'cur_cost': 828.0}, {'tour': array([2, 0, 1, 3, 6, 7, 5, 4, 8], dtype=int64), 'cur_cost': 924.0}, {'tour': array([4, 5, 7, 8, 0, 1, 2, 3, 6], dtype=int64), 'cur_cost': 1015.0}, {'tour': array([1, 7, 4, 0, 6, 5, 3, 2, 8], dtype=int64), 'cur_cost': 958.0}]
2025-08-04 17:36:49,519 - ExploitationExpert - INFO - 局部搜索耗时: 1.94秒，最大迭代次数: 10
2025-08-04 17:36:49,520 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-04 17:36:49,520 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([2, 0, 1, 3, 6, 7, 5, 4, 8], dtype=int64), 'cur_cost': 924.0, 'intermediate_solutions': [{'tour': array([8, 1, 3, 5, 0, 2, 6, 7, 4], dtype=int64), 'cur_cost': 1117.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 8, 1, 3, 0, 2, 6, 7, 4], dtype=int64), 'cur_cost': 1197.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 5, 8, 1, 3, 2, 6, 7, 4], dtype=int64), 'cur_cost': 1166.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 5, 8, 1, 0, 2, 6, 7, 4], dtype=int64), 'cur_cost': 992.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 0, 5, 8, 1, 2, 6, 7, 4], dtype=int64), 'cur_cost': 1150.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:36:49,521 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 924.00)
2025-08-04 17:36:49,521 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:36:49,521 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:36:49,521 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:49,522 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:36:49,522 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:49,522 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1092.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:36:49,522 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [1, 3, 2, 5, 7, 6, 0, 4, 8], 'cur_cost': 1092.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:36:49,523 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1092.00)
2025-08-04 17:36:49,523 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:36:49,523 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:36:49,523 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:49,523 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:36:49,524 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:49,524 - ExplorationExpert - INFO - 探索路径生成完成，成本: 818.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:36:49,524 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 6, 5, 8, 3, 4, 2, 7, 1], 'cur_cost': 818.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:36:49,524 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 818.00)
2025-08-04 17:36:49,524 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:36:49,524 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:36:49,525 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 0, 8, 6, 5, 3, 4, 2], 'cur_cost': 932.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [1, 0, 7, 6, 3, 5, 8, 4, 2], 'cur_cost': 757.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [5, 7, 1, 6, 2, 4, 0, 3, 8], 'cur_cost': 945.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 8, 3, 5, 0, 1, 4, 7, 6], dtype=int64), 'cur_cost': 882.0, 'intermediate_solutions': [{'tour': array([8, 1, 4, 6, 0, 3, 5, 2, 7], dtype=int64), 'cur_cost': 1104.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 8, 1, 4, 0, 3, 5, 2, 7], dtype=int64), 'cur_cost': 1078.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 6, 8, 1, 4, 3, 5, 2, 7], dtype=int64), 'cur_cost': 1090.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 6, 8, 1, 0, 3, 5, 2, 7], dtype=int64), 'cur_cost': 1091.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 0, 6, 8, 1, 3, 5, 2, 7], dtype=int64), 'cur_cost': 1122.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [3, 8, 5, 0, 4, 7, 6, 1, 2], 'cur_cost': 971.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 6, 8, 3, 7, 5, 4, 1], 'cur_cost': 957.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [4, 7, 3, 6, 5, 0, 1, 2, 8], 'cur_cost': 828.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 0, 1, 3, 6, 7, 5, 4, 8], dtype=int64), 'cur_cost': 924.0, 'intermediate_solutions': [{'tour': array([8, 1, 3, 5, 0, 2, 6, 7, 4], dtype=int64), 'cur_cost': 1117.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 8, 1, 3, 0, 2, 6, 7, 4], dtype=int64), 'cur_cost': 1197.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 5, 8, 1, 3, 2, 6, 7, 4], dtype=int64), 'cur_cost': 1166.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 5, 8, 1, 0, 2, 6, 7, 4], dtype=int64), 'cur_cost': 992.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 0, 5, 8, 1, 2, 6, 7, 4], dtype=int64), 'cur_cost': 1150.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [1, 3, 2, 5, 7, 6, 0, 4, 8], 'cur_cost': 1092.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 5, 8, 3, 4, 2, 7, 1], 'cur_cost': 818.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-04 17:36:49,527 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:36:49,527 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:36:49,528 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=757.000, 多样性=0.872
2025-08-04 17:36:49,528 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-04 17:36:49,528 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-04 17:36:49,529 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:36:49,529 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 80, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'slight_improvement', 'improvement_rate': 0.006351764685484693, 'best_improvement': -0.11160058737151249}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.017291066282420903}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.5555555555555556, 'new_diversity': 0.5555555555555556, 'diversity_change': 0.0}, 'suggestions': ['改进缓慢，建议调整策略平衡或增加扰动强度', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-04 17:36:49,540 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-04 17:36:49,540 - __main__ - INFO - simple1_9 开始进化第 2 代
2025-08-04 17:36:49,540 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-04 17:36:49,540 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:36:49,541 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=757.000, 多样性=0.872
2025-08-04 17:36:49,541 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:36:49,542 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.872
2025-08-04 17:36:49,542 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:36:49,542 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.556
2025-08-04 17:36:49,544 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-04 17:36:49,544 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:36:49,544 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-04 17:36:49,544 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-04 17:36:49,551 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.167, 适应度梯度: 17.700, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 0.660
2025-08-04 17:36:49,551 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-04 17:36:49,552 - LandscapeExpert - INFO - 提取到 4 个精英解
2025-08-04 17:36:49,552 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:36:49,555 - visualization.landscape_visualizer - INFO - 插值约束: 25 个点被约束到最小值 680.00
2025-08-04 17:36:49,559 - visualization.landscape_visualizer - INFO - 已添加 4 个精英解标记，坐标系统已统一
2025-08-04 17:36:49,634 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_2_20250804_173649.html
2025-08-04 17:36:49,667 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_2_20250804_173649.html
2025-08-04 17:36:49,667 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 2
2025-08-04 17:36:49,667 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-04 17:36:49,668 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1247秒
2025-08-04 17:36:49,668 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.16666666666666666, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 17.7, 'local_optima_density': 0.16666666666666666, 'gradient_variance': 15767.543333333335, 'cluster_count': 0}, 'population_state': {'diversity': 0.6603535353535354, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0022, 'fitness_entropy': 0.9533522741071693, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 17.700)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754300209.551246, 'performance_metrics': {}}}
2025-08-04 17:36:49,669 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:36:49,669 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:36:49,669 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:36:49,669 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:36:49,670 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-04 17:36:49,670 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-04 17:36:49,670 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-04 17:36:49,671 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:36:49,671 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:36:49,671 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-04 17:36:49,672 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:36:49,672 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:36:49,672 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 9} (总数: 2, 保护比例: 0.20)
2025-08-04 17:36:49,672 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:36:49,673 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:36:49,673 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:49,673 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:36:49,673 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:49,674 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:49,674 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:49,674 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:49,675 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1018.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:36:49,675 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [1, 8, 0, 2, 7, 3, 5, 6, 4], 'cur_cost': 1018.0, 'intermediate_solutions': [{'tour': [1, 7, 3, 8, 6, 5, 0, 4, 2], 'cur_cost': 879.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 7, 0, 8, 6, 5, 4, 3, 2], 'cur_cost': 1106.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 1, 7, 0, 6, 5, 3, 4, 2], 'cur_cost': 890.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:36:49,675 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1018.00)
2025-08-04 17:36:49,676 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:36:49,676 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:36:49,676 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:49,676 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:36:49,676 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:49,677 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:49,677 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:49,677 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:49,677 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1120.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:36:49,678 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [5, 2, 0, 3, 4, 1, 8, 6, 7], 'cur_cost': 1120.0, 'intermediate_solutions': [{'tour': [1, 6, 7, 0, 3, 5, 8, 4, 2], 'cur_cost': 857.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 0, 2, 4, 8, 5, 3, 6, 7], 'cur_cost': 787.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 3, 0, 7, 6, 5, 8, 4, 2], 'cur_cost': 911.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:36:49,678 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 1120.00)
2025-08-04 17:36:49,679 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:36:49,679 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:36:49,679 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:49,680 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:36:49,680 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:49,681 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:49,681 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:49,681 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:49,682 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1056.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:36:49,682 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [1, 5, 7, 8, 4, 6, 3, 0, 2], 'cur_cost': 1056.0, 'intermediate_solutions': [{'tour': [5, 7, 0, 6, 2, 4, 1, 3, 8], 'cur_cost': 945.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 7, 1, 2, 6, 4, 0, 3, 8], 'cur_cost': 1058.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 1, 6, 4, 0, 3, 2, 8], 'cur_cost': 1021.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:36:49,682 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1056.00)
2025-08-04 17:36:49,682 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:36:49,683 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:36:49,683 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:49,683 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:36:49,683 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:49,684 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:49,684 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:49,684 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:49,684 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1095.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:36:49,684 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [5, 7, 6, 2, 0, 3, 8, 1, 4], 'cur_cost': 1095.0, 'intermediate_solutions': [{'tour': [2, 8, 5, 3, 0, 1, 4, 7, 6], 'cur_cost': 899.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 3, 5, 0, 7, 4, 1, 6], 'cur_cost': 961.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 2, 8, 3, 5, 0, 1, 4, 6], 'cur_cost': 878.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:36:49,685 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1095.00)
2025-08-04 17:36:49,685 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-04 17:36:49,685 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:36:49,685 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:36:49,685 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 1053.0
2025-08-04 17:36:50,271 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:36:50,271 - ExploitationExpert - INFO - res_population_costs: [680.0, 778.0, 680.0]
2025-08-04 17:36:50,271 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 2, 4, 7, 5, 3, 8, 6], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64)]
2025-08-04 17:36:50,272 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:36:50,272 - ExploitationExpert - INFO - populations: [{'tour': [1, 8, 0, 2, 7, 3, 5, 6, 4], 'cur_cost': 1018.0}, {'tour': [5, 2, 0, 3, 4, 1, 8, 6, 7], 'cur_cost': 1120.0}, {'tour': [1, 5, 7, 8, 4, 6, 3, 0, 2], 'cur_cost': 1056.0}, {'tour': [5, 7, 6, 2, 0, 3, 8, 1, 4], 'cur_cost': 1095.0}, {'tour': array([3, 4, 8, 1, 0, 5, 7, 6, 2], dtype=int64), 'cur_cost': 1053.0}, {'tour': [0, 2, 6, 8, 3, 7, 5, 4, 1], 'cur_cost': 957.0}, {'tour': [4, 7, 3, 6, 5, 0, 1, 2, 8], 'cur_cost': 828.0}, {'tour': [2, 0, 1, 3, 6, 7, 5, 4, 8], 'cur_cost': 924.0}, {'tour': [1, 3, 2, 5, 7, 6, 0, 4, 8], 'cur_cost': 1092.0}, {'tour': [0, 6, 5, 8, 3, 4, 2, 7, 1], 'cur_cost': 818.0}]
2025-08-04 17:36:50,273 - ExploitationExpert - INFO - 局部搜索耗时: 0.59秒，最大迭代次数: 10
2025-08-04 17:36:50,273 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-04 17:36:50,273 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([3, 4, 8, 1, 0, 5, 7, 6, 2], dtype=int64), 'cur_cost': 1053.0, 'intermediate_solutions': [{'tour': array([5, 8, 3, 0, 4, 7, 6, 1, 2]), 'cur_cost': 991.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 5, 8, 3, 4, 7, 6, 1, 2]), 'cur_cost': 985.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 0, 5, 8, 3, 7, 6, 1, 2]), 'cur_cost': 843.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 0, 5, 8, 4, 7, 6, 1, 2]), 'cur_cost': 1027.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 4, 0, 5, 8, 7, 6, 1, 2]), 'cur_cost': 1006.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:36:50,274 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1053.00)
2025-08-04 17:36:50,274 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:36:50,274 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:36:50,274 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:50,275 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:36:50,275 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,275 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,276 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,276 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,276 - ExplorationExpert - INFO - 探索路径生成完成，成本: 779.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:36:50,276 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 4, 7, 5, 6, 3, 8, 2, 1], 'cur_cost': 779.0, 'intermediate_solutions': [{'tour': [0, 1, 6, 8, 3, 7, 5, 4, 2], 'cur_cost': 835.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 6, 2, 3, 7, 5, 4, 1], 'cur_cost': 1038.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 6, 8, 0, 3, 7, 5, 4, 1], 'cur_cost': 1108.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:36:50,276 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 779.00)
2025-08-04 17:36:50,277 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:36:50,277 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:36:50,277 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:50,277 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:36:50,277 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,278 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,278 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,278 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,279 - ExplorationExpert - INFO - 探索路径生成完成，成本: 890.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:36:50,279 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [8, 6, 5, 0, 2, 4, 1, 7, 3], 'cur_cost': 890.0, 'intermediate_solutions': [{'tour': [4, 7, 8, 6, 5, 0, 1, 2, 3], 'cur_cost': 969.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 7, 3, 6, 5, 0, 1, 8, 2], 'cur_cost': 826.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 7, 2, 3, 6, 5, 0, 1, 8], 'cur_cost': 985.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:36:50,279 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 890.00)
2025-08-04 17:36:50,280 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:36:50,280 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:36:50,280 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:50,280 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:36:50,281 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,281 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,281 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,281 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,281 - ExplorationExpert - INFO - 探索路径生成完成，成本: 831.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:36:50,281 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [6, 0, 4, 2, 8, 3, 7, 5, 1], 'cur_cost': 831.0, 'intermediate_solutions': [{'tour': [2, 6, 1, 3, 0, 7, 5, 4, 8], 'cur_cost': 1089.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 0, 1, 3, 6, 7, 5, 8, 4], 'cur_cost': 836.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 0, 1, 3, 6, 7, 5, 8, 4], 'cur_cost': 836.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:36:50,282 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 831.00)
2025-08-04 17:36:50,282 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-04 17:36:50,282 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:36:50,283 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:36:50,283 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 979.0
2025-08-04 17:36:50,291 - ExploitationExpert - INFO - res_population_num: 4
2025-08-04 17:36:50,291 - ExploitationExpert - INFO - res_population_costs: [680.0, 778.0, 680.0, 680]
2025-08-04 17:36:50,291 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 2, 4, 7, 5, 3, 8, 6], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:36:50,292 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:36:50,292 - ExploitationExpert - INFO - populations: [{'tour': [1, 8, 0, 2, 7, 3, 5, 6, 4], 'cur_cost': 1018.0}, {'tour': [5, 2, 0, 3, 4, 1, 8, 6, 7], 'cur_cost': 1120.0}, {'tour': [1, 5, 7, 8, 4, 6, 3, 0, 2], 'cur_cost': 1056.0}, {'tour': [5, 7, 6, 2, 0, 3, 8, 1, 4], 'cur_cost': 1095.0}, {'tour': array([3, 4, 8, 1, 0, 5, 7, 6, 2], dtype=int64), 'cur_cost': 1053.0}, {'tour': [0, 4, 7, 5, 6, 3, 8, 2, 1], 'cur_cost': 779.0}, {'tour': [8, 6, 5, 0, 2, 4, 1, 7, 3], 'cur_cost': 890.0}, {'tour': [6, 0, 4, 2, 8, 3, 7, 5, 1], 'cur_cost': 831.0}, {'tour': array([6, 3, 2, 4, 1, 8, 5, 0, 7], dtype=int64), 'cur_cost': 979.0}, {'tour': [0, 6, 5, 8, 3, 4, 2, 7, 1], 'cur_cost': 818.0}]
2025-08-04 17:36:50,293 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:36:50,293 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-04 17:36:50,294 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([6, 3, 2, 4, 1, 8, 5, 0, 7], dtype=int64), 'cur_cost': 979.0, 'intermediate_solutions': [{'tour': array([2, 3, 1, 5, 7, 6, 0, 4, 8]), 'cur_cost': 1009.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 2, 3, 1, 7, 6, 0, 4, 8]), 'cur_cost': 1069.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 5, 2, 3, 1, 6, 0, 4, 8]), 'cur_cost': 1040.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 5, 2, 3, 7, 6, 0, 4, 8]), 'cur_cost': 1082.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 7, 5, 2, 3, 6, 0, 4, 8]), 'cur_cost': 1071.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:36:50,294 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 979.00)
2025-08-04 17:36:50,295 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:36:50,295 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:36:50,295 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:50,295 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:36:50,296 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,296 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,297 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,297 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,297 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1040.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:36:50,298 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [1, 3, 2, 5, 7, 8, 4, 0, 6], 'cur_cost': 1040.0, 'intermediate_solutions': [{'tour': [0, 6, 4, 8, 3, 5, 2, 7, 1], 'cur_cost': 952.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 2, 4, 3, 8, 5, 7, 1], 'cur_cost': 878.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 6, 5, 8, 4, 2, 7, 1], 'cur_cost': 841.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:36:50,298 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 1040.00)
2025-08-04 17:36:50,299 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:36:50,299 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:36:50,300 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [1, 8, 0, 2, 7, 3, 5, 6, 4], 'cur_cost': 1018.0, 'intermediate_solutions': [{'tour': [1, 7, 3, 8, 6, 5, 0, 4, 2], 'cur_cost': 879.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 7, 0, 8, 6, 5, 4, 3, 2], 'cur_cost': 1106.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 1, 7, 0, 6, 5, 3, 4, 2], 'cur_cost': 890.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [5, 2, 0, 3, 4, 1, 8, 6, 7], 'cur_cost': 1120.0, 'intermediate_solutions': [{'tour': [1, 6, 7, 0, 3, 5, 8, 4, 2], 'cur_cost': 857.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 0, 2, 4, 8, 5, 3, 6, 7], 'cur_cost': 787.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 3, 0, 7, 6, 5, 8, 4, 2], 'cur_cost': 911.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [1, 5, 7, 8, 4, 6, 3, 0, 2], 'cur_cost': 1056.0, 'intermediate_solutions': [{'tour': [5, 7, 0, 6, 2, 4, 1, 3, 8], 'cur_cost': 945.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 7, 1, 2, 6, 4, 0, 3, 8], 'cur_cost': 1058.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 1, 6, 4, 0, 3, 2, 8], 'cur_cost': 1021.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [5, 7, 6, 2, 0, 3, 8, 1, 4], 'cur_cost': 1095.0, 'intermediate_solutions': [{'tour': [2, 8, 5, 3, 0, 1, 4, 7, 6], 'cur_cost': 899.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 3, 5, 0, 7, 4, 1, 6], 'cur_cost': 961.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 2, 8, 3, 5, 0, 1, 4, 6], 'cur_cost': 878.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 4, 8, 1, 0, 5, 7, 6, 2], dtype=int64), 'cur_cost': 1053.0, 'intermediate_solutions': [{'tour': array([5, 8, 3, 0, 4, 7, 6, 1, 2]), 'cur_cost': 991.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 5, 8, 3, 4, 7, 6, 1, 2]), 'cur_cost': 985.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 0, 5, 8, 3, 7, 6, 1, 2]), 'cur_cost': 843.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 0, 5, 8, 4, 7, 6, 1, 2]), 'cur_cost': 1027.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 4, 0, 5, 8, 7, 6, 1, 2]), 'cur_cost': 1006.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 7, 5, 6, 3, 8, 2, 1], 'cur_cost': 779.0, 'intermediate_solutions': [{'tour': [0, 1, 6, 8, 3, 7, 5, 4, 2], 'cur_cost': 835.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 6, 2, 3, 7, 5, 4, 1], 'cur_cost': 1038.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 6, 8, 0, 3, 7, 5, 4, 1], 'cur_cost': 1108.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [8, 6, 5, 0, 2, 4, 1, 7, 3], 'cur_cost': 890.0, 'intermediate_solutions': [{'tour': [4, 7, 8, 6, 5, 0, 1, 2, 3], 'cur_cost': 969.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 7, 3, 6, 5, 0, 1, 8, 2], 'cur_cost': 826.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 7, 2, 3, 6, 5, 0, 1, 8], 'cur_cost': 985.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [6, 0, 4, 2, 8, 3, 7, 5, 1], 'cur_cost': 831.0, 'intermediate_solutions': [{'tour': [2, 6, 1, 3, 0, 7, 5, 4, 8], 'cur_cost': 1089.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 0, 1, 3, 6, 7, 5, 8, 4], 'cur_cost': 836.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 0, 1, 3, 6, 7, 5, 8, 4], 'cur_cost': 836.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 3, 2, 4, 1, 8, 5, 0, 7], dtype=int64), 'cur_cost': 979.0, 'intermediate_solutions': [{'tour': array([2, 3, 1, 5, 7, 6, 0, 4, 8]), 'cur_cost': 1009.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 2, 3, 1, 7, 6, 0, 4, 8]), 'cur_cost': 1069.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 5, 2, 3, 1, 6, 0, 4, 8]), 'cur_cost': 1040.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 5, 2, 3, 7, 6, 0, 4, 8]), 'cur_cost': 1082.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 7, 5, 2, 3, 6, 0, 4, 8]), 'cur_cost': 1071.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [1, 3, 2, 5, 7, 8, 4, 0, 6], 'cur_cost': 1040.0, 'intermediate_solutions': [{'tour': [0, 6, 4, 8, 3, 5, 2, 7, 1], 'cur_cost': 952.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 2, 4, 3, 8, 5, 7, 1], 'cur_cost': 878.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 6, 5, 8, 4, 2, 7, 1], 'cur_cost': 841.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:36:50,302 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:36:50,303 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:36:50,304 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=779.000, 多样性=0.904
2025-08-04 17:36:50,304 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-04 17:36:50,304 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-04 17:36:50,304 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:36:50,304 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.0458718261002005, 'best_improvement': -0.02906208718626156}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.036827195467421844}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.7222222222222222, 'new_diversity': 0.7222222222222222, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-04 17:36:50,305 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-04 17:36:50,305 - __main__ - INFO - simple1_9 开始进化第 3 代
2025-08-04 17:36:50,305 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-04 17:36:50,305 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:36:50,306 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=779.000, 多样性=0.904
2025-08-04 17:36:50,306 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:36:50,307 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.904
2025-08-04 17:36:50,307 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:36:50,308 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.722
2025-08-04 17:36:50,309 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-04 17:36:50,309 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:36:50,309 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-04 17:36:50,310 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-04 17:36:50,317 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.286, 适应度梯度: 32.071, 聚类评分: 0.000, 覆盖率: 0.003, 收敛趋势: 0.000, 多样性: 0.571
2025-08-04 17:36:50,317 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-04 17:36:50,318 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:36:50,318 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:36:50,359 - visualization.landscape_visualizer - INFO - 插值约束: 21 个点被约束到最小值 680.00
2025-08-04 17:36:50,363 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:36:50,438 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_3_20250804_173650.html
2025-08-04 17:36:50,473 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_3_20250804_173650.html
2025-08-04 17:36:50,474 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 3
2025-08-04 17:36:50,474 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-04 17:36:50,474 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1648秒
2025-08-04 17:36:50,475 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2857142857142857, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 32.07142857142857, 'local_optima_density': 0.2857142857142857, 'gradient_variance': 15689.323469387757, 'cluster_count': 0}, 'population_state': {'diversity': 0.5714285714285714, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0034, 'fitness_entropy': 0.9474569977257028, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.003)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 32.071)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754300210.3177042, 'performance_metrics': {}}}
2025-08-04 17:36:50,475 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:36:50,475 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:36:50,476 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:36:50,476 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:36:50,476 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-04 17:36:50,476 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-04 17:36:50,477 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-04 17:36:50,477 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:36:50,477 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:36:50,477 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-04 17:36:50,478 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:36:50,478 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:36:50,478 - experts.management.collaboration_manager - INFO - 识别精英个体: {5, 7} (总数: 2, 保护比例: 0.20)
2025-08-04 17:36:50,479 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:36:50,479 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:36:50,479 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:50,480 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:36:50,480 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,481 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,481 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,481 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,482 - ExplorationExpert - INFO - 探索路径生成完成，成本: 826.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:36:50,482 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [7, 5, 6, 8, 4, 2, 0, 1, 3], 'cur_cost': 826.0, 'intermediate_solutions': [{'tour': [1, 8, 0, 2, 7, 3, 5, 4, 6], 'cur_cost': 1102.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 0, 2, 7, 3, 5, 4, 6], 'cur_cost': 1102.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 8, 0, 2, 7, 3, 6, 4, 5], 'cur_cost': 1187.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:36:50,482 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 826.00)
2025-08-04 17:36:50,483 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-04 17:36:50,483 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:36:50,483 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:36:50,483 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 993.0
2025-08-04 17:36:50,490 - ExploitationExpert - INFO - res_population_num: 4
2025-08-04 17:36:50,490 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680, 778.0]
2025-08-04 17:36:50,490 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 2, 4, 7, 5, 3, 8, 6], dtype=int64)]
2025-08-04 17:36:50,491 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:36:50,491 - ExploitationExpert - INFO - populations: [{'tour': [7, 5, 6, 8, 4, 2, 0, 1, 3], 'cur_cost': 826.0}, {'tour': array([5, 3, 1, 6, 0, 8, 7, 2, 4], dtype=int64), 'cur_cost': 993.0}, {'tour': [1, 5, 7, 8, 4, 6, 3, 0, 2], 'cur_cost': 1056.0}, {'tour': [5, 7, 6, 2, 0, 3, 8, 1, 4], 'cur_cost': 1095.0}, {'tour': [3, 4, 8, 1, 0, 5, 7, 6, 2], 'cur_cost': 1053.0}, {'tour': [0, 4, 7, 5, 6, 3, 8, 2, 1], 'cur_cost': 779.0}, {'tour': [8, 6, 5, 0, 2, 4, 1, 7, 3], 'cur_cost': 890.0}, {'tour': [6, 0, 4, 2, 8, 3, 7, 5, 1], 'cur_cost': 831.0}, {'tour': [6, 3, 2, 4, 1, 8, 5, 0, 7], 'cur_cost': 979.0}, {'tour': [1, 3, 2, 5, 7, 8, 4, 0, 6], 'cur_cost': 1040.0}]
2025-08-04 17:36:50,492 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:36:50,492 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-04 17:36:50,493 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([5, 3, 1, 6, 0, 8, 7, 2, 4], dtype=int64), 'cur_cost': 993.0, 'intermediate_solutions': [{'tour': array([0, 2, 5, 3, 4, 1, 8, 6, 7]), 'cur_cost': 1071.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 0, 2, 5, 4, 1, 8, 6, 7]), 'cur_cost': 1130.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 3, 0, 2, 5, 1, 8, 6, 7]), 'cur_cost': 1202.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 3, 0, 2, 4, 1, 8, 6, 7]), 'cur_cost': 943.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 4, 3, 0, 2, 1, 8, 6, 7]), 'cur_cost': 1120.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:36:50,494 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 993.00)
2025-08-04 17:36:50,494 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-04 17:36:50,494 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:36:50,494 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:36:50,495 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 822.0
2025-08-04 17:36:50,501 - ExploitationExpert - INFO - res_population_num: 4
2025-08-04 17:36:50,502 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680, 778.0]
2025-08-04 17:36:50,502 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 2, 4, 7, 5, 3, 8, 6], dtype=int64)]
2025-08-04 17:36:50,503 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:36:50,503 - ExploitationExpert - INFO - populations: [{'tour': [7, 5, 6, 8, 4, 2, 0, 1, 3], 'cur_cost': 826.0}, {'tour': array([5, 3, 1, 6, 0, 8, 7, 2, 4], dtype=int64), 'cur_cost': 993.0}, {'tour': array([4, 8, 2, 1, 0, 7, 5, 6, 3], dtype=int64), 'cur_cost': 822.0}, {'tour': [5, 7, 6, 2, 0, 3, 8, 1, 4], 'cur_cost': 1095.0}, {'tour': [3, 4, 8, 1, 0, 5, 7, 6, 2], 'cur_cost': 1053.0}, {'tour': [0, 4, 7, 5, 6, 3, 8, 2, 1], 'cur_cost': 779.0}, {'tour': [8, 6, 5, 0, 2, 4, 1, 7, 3], 'cur_cost': 890.0}, {'tour': [6, 0, 4, 2, 8, 3, 7, 5, 1], 'cur_cost': 831.0}, {'tour': [6, 3, 2, 4, 1, 8, 5, 0, 7], 'cur_cost': 979.0}, {'tour': [1, 3, 2, 5, 7, 8, 4, 0, 6], 'cur_cost': 1040.0}]
2025-08-04 17:36:50,504 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:36:50,504 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-04 17:36:50,505 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([4, 8, 2, 1, 0, 7, 5, 6, 3], dtype=int64), 'cur_cost': 822.0, 'intermediate_solutions': [{'tour': array([7, 5, 1, 8, 4, 6, 3, 0, 2]), 'cur_cost': 1138.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 7, 5, 1, 4, 6, 3, 0, 2]), 'cur_cost': 1030.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 8, 7, 5, 1, 6, 3, 0, 2]), 'cur_cost': 943.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 8, 7, 5, 4, 6, 3, 0, 2]), 'cur_cost': 1113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 4, 8, 7, 5, 6, 3, 0, 2]), 'cur_cost': 918.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:36:50,505 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 822.00)
2025-08-04 17:36:50,505 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-04 17:36:50,505 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:36:50,506 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:36:50,506 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1061.0
2025-08-04 17:36:50,513 - ExploitationExpert - INFO - res_population_num: 4
2025-08-04 17:36:50,513 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680, 778.0]
2025-08-04 17:36:50,513 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 2, 4, 7, 5, 3, 8, 6], dtype=int64)]
2025-08-04 17:36:50,515 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:36:50,515 - ExploitationExpert - INFO - populations: [{'tour': [7, 5, 6, 8, 4, 2, 0, 1, 3], 'cur_cost': 826.0}, {'tour': array([5, 3, 1, 6, 0, 8, 7, 2, 4], dtype=int64), 'cur_cost': 993.0}, {'tour': array([4, 8, 2, 1, 0, 7, 5, 6, 3], dtype=int64), 'cur_cost': 822.0}, {'tour': array([0, 7, 6, 2, 1, 3, 8, 5, 4], dtype=int64), 'cur_cost': 1061.0}, {'tour': [3, 4, 8, 1, 0, 5, 7, 6, 2], 'cur_cost': 1053.0}, {'tour': [0, 4, 7, 5, 6, 3, 8, 2, 1], 'cur_cost': 779.0}, {'tour': [8, 6, 5, 0, 2, 4, 1, 7, 3], 'cur_cost': 890.0}, {'tour': [6, 0, 4, 2, 8, 3, 7, 5, 1], 'cur_cost': 831.0}, {'tour': [6, 3, 2, 4, 1, 8, 5, 0, 7], 'cur_cost': 979.0}, {'tour': [1, 3, 2, 5, 7, 8, 4, 0, 6], 'cur_cost': 1040.0}]
2025-08-04 17:36:50,516 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:36:50,516 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-04 17:36:50,517 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([0, 7, 6, 2, 1, 3, 8, 5, 4], dtype=int64), 'cur_cost': 1061.0, 'intermediate_solutions': [{'tour': array([6, 7, 5, 2, 0, 3, 8, 1, 4]), 'cur_cost': 1086.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 6, 7, 5, 0, 3, 8, 1, 4]), 'cur_cost': 1009.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 2, 6, 7, 5, 3, 8, 1, 4]), 'cur_cost': 976.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 2, 6, 7, 0, 3, 8, 1, 4]), 'cur_cost': 1154.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 0, 2, 6, 7, 3, 8, 1, 4]), 'cur_cost': 1088.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:36:50,517 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1061.00)
2025-08-04 17:36:50,517 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:36:50,518 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:36:50,518 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:50,518 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:36:50,518 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,519 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,519 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,519 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,519 - ExplorationExpert - INFO - 探索路径生成完成，成本: 782.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:36:50,519 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 4, 8, 3, 5, 6, 7, 2, 1], 'cur_cost': 782.0, 'intermediate_solutions': [{'tour': [3, 6, 8, 1, 0, 5, 7, 4, 2], 'cur_cost': 956.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 4, 8, 1, 0, 5, 7, 2, 6], 'cur_cost': 1053.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 4, 8, 0, 1, 5, 7, 6, 2], 'cur_cost': 1052.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:36:50,520 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 782.00)
2025-08-04 17:36:50,520 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:36:50,520 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:36:50,520 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:50,521 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:36:50,521 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,521 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,521 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,521 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,522 - ExplorationExpert - INFO - 探索路径生成完成，成本: 855.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:36:50,522 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [4, 8, 5, 7, 3, 0, 1, 6, 2], 'cur_cost': 855.0, 'intermediate_solutions': [{'tour': [0, 4, 2, 5, 6, 3, 8, 7, 1], 'cur_cost': 815.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 7, 5, 6, 2, 8, 3, 1], 'cur_cost': 895.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 5, 6, 4, 3, 8, 2, 1], 'cur_cost': 854.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:36:50,522 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 855.00)
2025-08-04 17:36:50,523 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:36:50,523 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:36:50,523 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:50,523 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:36:50,523 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,524 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,524 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,524 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,524 - ExplorationExpert - INFO - 探索路径生成完成，成本: 805.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:36:50,524 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [4, 0, 6, 5, 3, 7, 8, 2, 1], 'cur_cost': 805.0, 'intermediate_solutions': [{'tour': [4, 6, 5, 0, 2, 8, 1, 7, 3], 'cur_cost': 1056.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 6, 5, 0, 2, 7, 1, 4, 3], 'cur_cost': 1026.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 5, 0, 2, 4, 1, 7, 6, 3], 'cur_cost': 900.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:36:50,525 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 805.00)
2025-08-04 17:36:50,525 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:36:50,525 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:36:50,525 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:50,526 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:36:50,526 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,526 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,526 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,527 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,527 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1083.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:36:50,527 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [1, 8, 4, 6, 0, 3, 7, 5, 2], 'cur_cost': 1083.0, 'intermediate_solutions': [{'tour': [6, 0, 4, 2, 3, 8, 7, 5, 1], 'cur_cost': 906.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 8, 2, 4, 0, 3, 7, 5, 1], 'cur_cost': 920.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 4, 2, 5, 8, 3, 7, 1], 'cur_cost': 891.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:36:50,527 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 1083.00)
2025-08-04 17:36:50,527 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:36:50,528 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:36:50,528 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:50,528 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:36:50,529 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,529 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,529 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,530 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,530 - ExplorationExpert - INFO - 探索路径生成完成，成本: 850.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:36:50,530 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [6, 7, 5, 8, 3, 0, 4, 2, 1], 'cur_cost': 850.0, 'intermediate_solutions': [{'tour': [6, 5, 2, 4, 1, 8, 3, 0, 7], 'cur_cost': 948.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 4, 2, 3, 1, 8, 5, 0, 7], 'cur_cost': 1086.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 3, 2, 4, 1, 8, 5, 7], 'cur_cost': 938.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:36:50,531 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 850.00)
2025-08-04 17:36:50,531 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:36:50,531 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:36:50,531 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:50,532 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:36:50,532 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,532 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,532 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,533 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,533 - ExplorationExpert - INFO - 探索路径生成完成，成本: 942.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:36:50,533 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [4, 0, 3, 5, 6, 8, 7, 2, 1], 'cur_cost': 942.0, 'intermediate_solutions': [{'tour': [0, 3, 2, 5, 7, 8, 4, 1, 6], 'cur_cost': 1021.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 3, 2, 5, 7, 8, 4, 6, 0], 'cur_cost': 1028.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 3, 2, 5, 7, 8, 0, 6, 4], 'cur_cost': 1134.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:36:50,533 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 942.00)
2025-08-04 17:36:50,534 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:36:50,534 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:36:50,535 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [7, 5, 6, 8, 4, 2, 0, 1, 3], 'cur_cost': 826.0, 'intermediate_solutions': [{'tour': [1, 8, 0, 2, 7, 3, 5, 4, 6], 'cur_cost': 1102.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 0, 2, 7, 3, 5, 4, 6], 'cur_cost': 1102.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 8, 0, 2, 7, 3, 6, 4, 5], 'cur_cost': 1187.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([5, 3, 1, 6, 0, 8, 7, 2, 4], dtype=int64), 'cur_cost': 993.0, 'intermediate_solutions': [{'tour': array([0, 2, 5, 3, 4, 1, 8, 6, 7]), 'cur_cost': 1071.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 0, 2, 5, 4, 1, 8, 6, 7]), 'cur_cost': 1130.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 3, 0, 2, 5, 1, 8, 6, 7]), 'cur_cost': 1202.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 3, 0, 2, 4, 1, 8, 6, 7]), 'cur_cost': 943.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 4, 3, 0, 2, 1, 8, 6, 7]), 'cur_cost': 1120.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 8, 2, 1, 0, 7, 5, 6, 3], dtype=int64), 'cur_cost': 822.0, 'intermediate_solutions': [{'tour': array([7, 5, 1, 8, 4, 6, 3, 0, 2]), 'cur_cost': 1138.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 7, 5, 1, 4, 6, 3, 0, 2]), 'cur_cost': 1030.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 8, 7, 5, 1, 6, 3, 0, 2]), 'cur_cost': 943.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 8, 7, 5, 4, 6, 3, 0, 2]), 'cur_cost': 1113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 4, 8, 7, 5, 6, 3, 0, 2]), 'cur_cost': 918.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 7, 6, 2, 1, 3, 8, 5, 4], dtype=int64), 'cur_cost': 1061.0, 'intermediate_solutions': [{'tour': array([6, 7, 5, 2, 0, 3, 8, 1, 4]), 'cur_cost': 1086.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 6, 7, 5, 0, 3, 8, 1, 4]), 'cur_cost': 1009.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 2, 6, 7, 5, 3, 8, 1, 4]), 'cur_cost': 976.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 2, 6, 7, 0, 3, 8, 1, 4]), 'cur_cost': 1154.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 0, 2, 6, 7, 3, 8, 1, 4]), 'cur_cost': 1088.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 8, 3, 5, 6, 7, 2, 1], 'cur_cost': 782.0, 'intermediate_solutions': [{'tour': [3, 6, 8, 1, 0, 5, 7, 4, 2], 'cur_cost': 956.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 4, 8, 1, 0, 5, 7, 2, 6], 'cur_cost': 1053.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 4, 8, 0, 1, 5, 7, 6, 2], 'cur_cost': 1052.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [4, 8, 5, 7, 3, 0, 1, 6, 2], 'cur_cost': 855.0, 'intermediate_solutions': [{'tour': [0, 4, 2, 5, 6, 3, 8, 7, 1], 'cur_cost': 815.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 7, 5, 6, 2, 8, 3, 1], 'cur_cost': 895.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 5, 6, 4, 3, 8, 2, 1], 'cur_cost': 854.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [4, 0, 6, 5, 3, 7, 8, 2, 1], 'cur_cost': 805.0, 'intermediate_solutions': [{'tour': [4, 6, 5, 0, 2, 8, 1, 7, 3], 'cur_cost': 1056.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 6, 5, 0, 2, 7, 1, 4, 3], 'cur_cost': 1026.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 5, 0, 2, 4, 1, 7, 6, 3], 'cur_cost': 900.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [1, 8, 4, 6, 0, 3, 7, 5, 2], 'cur_cost': 1083.0, 'intermediate_solutions': [{'tour': [6, 0, 4, 2, 3, 8, 7, 5, 1], 'cur_cost': 906.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 8, 2, 4, 0, 3, 7, 5, 1], 'cur_cost': 920.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 4, 2, 5, 8, 3, 7, 1], 'cur_cost': 891.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [6, 7, 5, 8, 3, 0, 4, 2, 1], 'cur_cost': 850.0, 'intermediate_solutions': [{'tour': [6, 5, 2, 4, 1, 8, 3, 0, 7], 'cur_cost': 948.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 4, 2, 3, 1, 8, 5, 0, 7], 'cur_cost': 1086.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 3, 2, 4, 1, 8, 5, 7], 'cur_cost': 938.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [4, 0, 3, 5, 6, 8, 7, 2, 1], 'cur_cost': 942.0, 'intermediate_solutions': [{'tour': [0, 3, 2, 5, 7, 8, 4, 1, 6], 'cur_cost': 1021.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 3, 2, 5, 7, 8, 4, 6, 0], 'cur_cost': 1028.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 3, 2, 5, 7, 8, 0, 6, 4], 'cur_cost': 1134.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:36:50,537 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:36:50,537 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:36:50,538 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=782.000, 多样性=0.859
2025-08-04 17:36:50,538 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-04 17:36:50,538 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-04 17:36:50,539 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:36:50,539 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.03819050024728159, 'best_improvement': -0.0038510911424903724}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.049180327868852534}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.7222222222222222, 'new_diversity': 0.7222222222222222, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:36:50,539 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-04 17:36:50,540 - __main__ - INFO - simple1_9 开始进化第 4 代
2025-08-04 17:36:50,540 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-04 17:36:50,540 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:36:50,540 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=782.000, 多样性=0.859
2025-08-04 17:36:50,541 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:36:50,541 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.859
2025-08-04 17:36:50,542 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:36:50,542 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.722
2025-08-04 17:36:50,544 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-04 17:36:50,544 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:36:50,544 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-04 17:36:50,544 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-04 17:36:50,552 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.214, 适应度梯度: 2.414, 聚类评分: 0.000, 覆盖率: 0.004, 收敛趋势: 0.000, 多样性: 0.547
2025-08-04 17:36:50,553 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-04 17:36:50,553 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:36:50,553 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:36:50,556 - visualization.landscape_visualizer - INFO - 插值约束: 285 个点被约束到最小值 680.00
2025-08-04 17:36:50,560 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:36:50,637 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_4_20250804_173650.html
2025-08-04 17:36:50,673 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_4_20250804_173650.html
2025-08-04 17:36:50,674 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 4
2025-08-04 17:36:50,674 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-04 17:36:50,674 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1305秒
2025-08-04 17:36:50,674 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.21428571428571427, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 2.4142857142857252, 'local_optima_density': 0.21428571428571427, 'gradient_variance': 19257.77408163265, 'cluster_count': 0}, 'population_state': {'diversity': 0.5470957613814756, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0044, 'fitness_entropy': 0.9615862351816215, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.004)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 2.414)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754300210.5534701, 'performance_metrics': {}}}
2025-08-04 17:36:50,675 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:36:50,675 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:36:50,675 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:36:50,676 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:36:50,676 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-04 17:36:50,676 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-04 17:36:50,676 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-04 17:36:50,677 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:36:50,677 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:36:50,677 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-04 17:36:50,677 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:36:50,678 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:36:50,678 - experts.management.collaboration_manager - INFO - 识别精英个体: {4, 6} (总数: 2, 保护比例: 0.20)
2025-08-04 17:36:50,679 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:36:50,679 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:36:50,679 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:50,680 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:36:50,680 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,680 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,681 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,681 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,681 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1020.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:36:50,681 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [3, 6, 7, 0, 4, 8, 5, 2, 1], 'cur_cost': 1020.0, 'intermediate_solutions': [{'tour': [4, 5, 6, 8, 7, 2, 0, 1, 3], 'cur_cost': 1024.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 0, 2, 4, 8, 6, 1, 3], 'cur_cost': 960.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 5, 8, 4, 2, 0, 1, 6, 3], 'cur_cost': 769.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:36:50,682 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1020.00)
2025-08-04 17:36:50,682 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-04 17:36:50,682 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:36:50,683 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:36:50,683 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 1105.0
2025-08-04 17:36:50,689 - ExploitationExpert - INFO - res_population_num: 4
2025-08-04 17:36:50,690 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680, 778.0]
2025-08-04 17:36:50,690 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 2, 4, 7, 5, 3, 8, 6], dtype=int64)]
2025-08-04 17:36:50,691 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:36:50,691 - ExploitationExpert - INFO - populations: [{'tour': [3, 6, 7, 0, 4, 8, 5, 2, 1], 'cur_cost': 1020.0}, {'tour': array([5, 4, 0, 3, 2, 7, 6, 1, 8], dtype=int64), 'cur_cost': 1105.0}, {'tour': [4, 8, 2, 1, 0, 7, 5, 6, 3], 'cur_cost': 822.0}, {'tour': [0, 7, 6, 2, 1, 3, 8, 5, 4], 'cur_cost': 1061.0}, {'tour': [0, 4, 8, 3, 5, 6, 7, 2, 1], 'cur_cost': 782.0}, {'tour': [4, 8, 5, 7, 3, 0, 1, 6, 2], 'cur_cost': 855.0}, {'tour': [4, 0, 6, 5, 3, 7, 8, 2, 1], 'cur_cost': 805.0}, {'tour': [1, 8, 4, 6, 0, 3, 7, 5, 2], 'cur_cost': 1083.0}, {'tour': [6, 7, 5, 8, 3, 0, 4, 2, 1], 'cur_cost': 850.0}, {'tour': [4, 0, 3, 5, 6, 8, 7, 2, 1], 'cur_cost': 942.0}]
2025-08-04 17:36:50,691 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:36:50,692 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-08-04 17:36:50,692 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([5, 4, 0, 3, 2, 7, 6, 1, 8], dtype=int64), 'cur_cost': 1105.0, 'intermediate_solutions': [{'tour': array([1, 3, 5, 6, 0, 8, 7, 2, 4]), 'cur_cost': 909.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 1, 3, 5, 0, 8, 7, 2, 4]), 'cur_cost': 1031.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 6, 1, 3, 5, 8, 7, 2, 4]), 'cur_cost': 898.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 6, 1, 3, 0, 8, 7, 2, 4]), 'cur_cost': 1028.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 0, 6, 1, 3, 8, 7, 2, 4]), 'cur_cost': 1000.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:36:50,693 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1105.00)
2025-08-04 17:36:50,693 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:36:50,693 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:36:50,693 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:50,693 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:36:50,693 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,694 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,694 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,695 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,695 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1031.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:36:50,695 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [3, 6, 1, 4, 8, 5, 2, 7, 0], 'cur_cost': 1031.0, 'intermediate_solutions': [{'tour': [4, 8, 0, 1, 2, 7, 5, 6, 3], 'cur_cost': 905.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 2, 1, 6, 5, 7, 0, 3], 'cur_cost': 922.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 2, 1, 0, 7, 4, 5, 6, 3], 'cur_cost': 849.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:36:50,695 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1031.00)
2025-08-04 17:36:50,696 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-04 17:36:50,696 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:36:50,696 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:36:50,696 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 891.0
2025-08-04 17:36:50,703 - ExploitationExpert - INFO - res_population_num: 4
2025-08-04 17:36:50,703 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680, 778.0]
2025-08-04 17:36:50,703 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 2, 4, 7, 5, 3, 8, 6], dtype=int64)]
2025-08-04 17:36:50,704 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:36:50,704 - ExploitationExpert - INFO - populations: [{'tour': [3, 6, 7, 0, 4, 8, 5, 2, 1], 'cur_cost': 1020.0}, {'tour': array([5, 4, 0, 3, 2, 7, 6, 1, 8], dtype=int64), 'cur_cost': 1105.0}, {'tour': [3, 6, 1, 4, 8, 5, 2, 7, 0], 'cur_cost': 1031.0}, {'tour': array([6, 4, 0, 1, 3, 8, 2, 7, 5], dtype=int64), 'cur_cost': 891.0}, {'tour': [0, 4, 8, 3, 5, 6, 7, 2, 1], 'cur_cost': 782.0}, {'tour': [4, 8, 5, 7, 3, 0, 1, 6, 2], 'cur_cost': 855.0}, {'tour': [4, 0, 6, 5, 3, 7, 8, 2, 1], 'cur_cost': 805.0}, {'tour': [1, 8, 4, 6, 0, 3, 7, 5, 2], 'cur_cost': 1083.0}, {'tour': [6, 7, 5, 8, 3, 0, 4, 2, 1], 'cur_cost': 850.0}, {'tour': [4, 0, 3, 5, 6, 8, 7, 2, 1], 'cur_cost': 942.0}]
2025-08-04 17:36:50,705 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:36:50,705 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-08-04 17:36:50,706 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([6, 4, 0, 1, 3, 8, 2, 7, 5], dtype=int64), 'cur_cost': 891.0, 'intermediate_solutions': [{'tour': array([6, 7, 0, 2, 1, 3, 8, 5, 4]), 'cur_cost': 1063.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 6, 7, 0, 1, 3, 8, 5, 4]), 'cur_cost': 936.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 2, 6, 7, 0, 3, 8, 5, 4]), 'cur_cost': 1042.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 2, 6, 7, 1, 3, 8, 5, 4]), 'cur_cost': 1091.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 1, 2, 6, 7, 3, 8, 5, 4]), 'cur_cost': 915.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:36:50,706 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 891.00)
2025-08-04 17:36:50,707 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:36:50,707 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:36:50,707 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:50,707 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:36:50,708 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,708 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,708 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,708 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,708 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1005.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:36:50,708 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [2, 6, 4, 5, 3, 7, 0, 1, 8], 'cur_cost': 1005.0, 'intermediate_solutions': [{'tour': [0, 4, 7, 3, 5, 6, 8, 2, 1], 'cur_cost': 792.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 8, 4, 5, 6, 7, 2, 1], 'cur_cost': 901.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 3, 8, 5, 6, 7, 2, 1], 'cur_cost': 845.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:36:50,709 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 1005.00)
2025-08-04 17:36:50,709 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:36:50,709 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:36:50,709 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:50,710 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:36:50,710 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,710 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,710 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,711 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,711 - ExplorationExpert - INFO - 探索路径生成完成，成本: 960.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:36:50,711 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [6, 8, 0, 2, 4, 7, 3, 5, 1], 'cur_cost': 960.0, 'intermediate_solutions': [{'tour': [4, 1, 5, 7, 3, 0, 8, 6, 2], 'cur_cost': 1049.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 7, 5, 8, 3, 0, 1, 6, 2], 'cur_cost': 872.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 8, 5, 7, 0, 1, 3, 6, 2], 'cur_cost': 901.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:36:50,711 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 960.00)
2025-08-04 17:36:50,712 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:36:50,712 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:36:50,712 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:50,713 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:36:50,713 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,713 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,713 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,714 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,714 - ExplorationExpert - INFO - 探索路径生成完成，成本: 932.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:36:50,714 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [7, 1, 0, 3, 4, 8, 2, 5, 6], 'cur_cost': 932.0, 'intermediate_solutions': [{'tour': [7, 0, 6, 5, 3, 4, 8, 2, 1], 'cur_cost': 892.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 0, 8, 7, 3, 5, 6, 2, 1], 'cur_cost': 934.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 6, 5, 8, 3, 7, 2, 1], 'cur_cost': 864.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:36:50,715 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 932.00)
2025-08-04 17:36:50,715 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-04 17:36:50,715 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:36:50,715 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:36:50,716 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 1101.0
2025-08-04 17:36:50,722 - ExploitationExpert - INFO - res_population_num: 4
2025-08-04 17:36:50,722 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680, 778.0]
2025-08-04 17:36:50,723 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 2, 4, 7, 5, 3, 8, 6], dtype=int64)]
2025-08-04 17:36:50,723 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:36:50,724 - ExploitationExpert - INFO - populations: [{'tour': [3, 6, 7, 0, 4, 8, 5, 2, 1], 'cur_cost': 1020.0}, {'tour': array([5, 4, 0, 3, 2, 7, 6, 1, 8], dtype=int64), 'cur_cost': 1105.0}, {'tour': [3, 6, 1, 4, 8, 5, 2, 7, 0], 'cur_cost': 1031.0}, {'tour': array([6, 4, 0, 1, 3, 8, 2, 7, 5], dtype=int64), 'cur_cost': 891.0}, {'tour': [2, 6, 4, 5, 3, 7, 0, 1, 8], 'cur_cost': 1005.0}, {'tour': [6, 8, 0, 2, 4, 7, 3, 5, 1], 'cur_cost': 960.0}, {'tour': [7, 1, 0, 3, 4, 8, 2, 5, 6], 'cur_cost': 932.0}, {'tour': array([7, 0, 8, 4, 3, 6, 2, 1, 5], dtype=int64), 'cur_cost': 1101.0}, {'tour': [6, 7, 5, 8, 3, 0, 4, 2, 1], 'cur_cost': 850.0}, {'tour': [4, 0, 3, 5, 6, 8, 7, 2, 1], 'cur_cost': 942.0}]
2025-08-04 17:36:50,725 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:36:50,725 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-08-04 17:36:50,725 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([7, 0, 8, 4, 3, 6, 2, 1, 5], dtype=int64), 'cur_cost': 1101.0, 'intermediate_solutions': [{'tour': array([4, 8, 1, 6, 0, 3, 7, 5, 2]), 'cur_cost': 970.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 4, 8, 1, 0, 3, 7, 5, 2]), 'cur_cost': 1080.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 6, 4, 8, 1, 3, 7, 5, 2]), 'cur_cost': 1113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 6, 4, 8, 0, 3, 7, 5, 2]), 'cur_cost': 1066.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 0, 6, 4, 8, 3, 7, 5, 2]), 'cur_cost': 904.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:36:50,726 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1101.00)
2025-08-04 17:36:50,726 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:36:50,726 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:36:50,726 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:50,727 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:36:50,727 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,727 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,727 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,727 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,728 - ExplorationExpert - INFO - 探索路径生成完成，成本: 872.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:36:50,728 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [2, 3, 0, 1, 7, 5, 6, 8, 4], 'cur_cost': 872.0, 'intermediate_solutions': [{'tour': [6, 7, 5, 8, 0, 3, 4, 2, 1], 'cur_cost': 945.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 6, 5, 8, 3, 0, 4, 2, 1], 'cur_cost': 855.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 7, 5, 8, 3, 2, 0, 4, 1], 'cur_cost': 916.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:36:50,728 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 872.00)
2025-08-04 17:36:50,728 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:36:50,729 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:36:50,729 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:50,730 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:36:50,730 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,730 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,730 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,731 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,731 - ExplorationExpert - INFO - 探索路径生成完成，成本: 930.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:36:50,731 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [4, 8, 3, 6, 5, 0, 7, 2, 1], 'cur_cost': 930.0, 'intermediate_solutions': [{'tour': [4, 5, 3, 0, 6, 8, 7, 2, 1], 'cur_cost': 1026.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 0, 3, 5, 6, 8, 7, 1, 2], 'cur_cost': 885.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 3, 6, 8, 7, 2, 5, 1], 'cur_cost': 1111.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:36:50,732 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 930.00)
2025-08-04 17:36:50,732 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:36:50,732 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:36:50,734 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [3, 6, 7, 0, 4, 8, 5, 2, 1], 'cur_cost': 1020.0, 'intermediate_solutions': [{'tour': [4, 5, 6, 8, 7, 2, 0, 1, 3], 'cur_cost': 1024.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 0, 2, 4, 8, 6, 1, 3], 'cur_cost': 960.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 5, 8, 4, 2, 0, 1, 6, 3], 'cur_cost': 769.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([5, 4, 0, 3, 2, 7, 6, 1, 8], dtype=int64), 'cur_cost': 1105.0, 'intermediate_solutions': [{'tour': array([1, 3, 5, 6, 0, 8, 7, 2, 4]), 'cur_cost': 909.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 1, 3, 5, 0, 8, 7, 2, 4]), 'cur_cost': 1031.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 6, 1, 3, 5, 8, 7, 2, 4]), 'cur_cost': 898.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 6, 1, 3, 0, 8, 7, 2, 4]), 'cur_cost': 1028.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 0, 6, 1, 3, 8, 7, 2, 4]), 'cur_cost': 1000.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [3, 6, 1, 4, 8, 5, 2, 7, 0], 'cur_cost': 1031.0, 'intermediate_solutions': [{'tour': [4, 8, 0, 1, 2, 7, 5, 6, 3], 'cur_cost': 905.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 2, 1, 6, 5, 7, 0, 3], 'cur_cost': 922.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 2, 1, 0, 7, 4, 5, 6, 3], 'cur_cost': 849.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 4, 0, 1, 3, 8, 2, 7, 5], dtype=int64), 'cur_cost': 891.0, 'intermediate_solutions': [{'tour': array([6, 7, 0, 2, 1, 3, 8, 5, 4]), 'cur_cost': 1063.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 6, 7, 0, 1, 3, 8, 5, 4]), 'cur_cost': 936.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 2, 6, 7, 0, 3, 8, 5, 4]), 'cur_cost': 1042.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 2, 6, 7, 1, 3, 8, 5, 4]), 'cur_cost': 1091.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 1, 2, 6, 7, 3, 8, 5, 4]), 'cur_cost': 915.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [2, 6, 4, 5, 3, 7, 0, 1, 8], 'cur_cost': 1005.0, 'intermediate_solutions': [{'tour': [0, 4, 7, 3, 5, 6, 8, 2, 1], 'cur_cost': 792.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 8, 4, 5, 6, 7, 2, 1], 'cur_cost': 901.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 3, 8, 5, 6, 7, 2, 1], 'cur_cost': 845.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [6, 8, 0, 2, 4, 7, 3, 5, 1], 'cur_cost': 960.0, 'intermediate_solutions': [{'tour': [4, 1, 5, 7, 3, 0, 8, 6, 2], 'cur_cost': 1049.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 7, 5, 8, 3, 0, 1, 6, 2], 'cur_cost': 872.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 8, 5, 7, 0, 1, 3, 6, 2], 'cur_cost': 901.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [7, 1, 0, 3, 4, 8, 2, 5, 6], 'cur_cost': 932.0, 'intermediate_solutions': [{'tour': [7, 0, 6, 5, 3, 4, 8, 2, 1], 'cur_cost': 892.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 0, 8, 7, 3, 5, 6, 2, 1], 'cur_cost': 934.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 6, 5, 8, 3, 7, 2, 1], 'cur_cost': 864.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 0, 8, 4, 3, 6, 2, 1, 5], dtype=int64), 'cur_cost': 1101.0, 'intermediate_solutions': [{'tour': array([4, 8, 1, 6, 0, 3, 7, 5, 2]), 'cur_cost': 970.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 4, 8, 1, 0, 3, 7, 5, 2]), 'cur_cost': 1080.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 6, 4, 8, 1, 3, 7, 5, 2]), 'cur_cost': 1113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 6, 4, 8, 0, 3, 7, 5, 2]), 'cur_cost': 1066.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 0, 6, 4, 8, 3, 7, 5, 2]), 'cur_cost': 904.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [2, 3, 0, 1, 7, 5, 6, 8, 4], 'cur_cost': 872.0, 'intermediate_solutions': [{'tour': [6, 7, 5, 8, 0, 3, 4, 2, 1], 'cur_cost': 945.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 6, 5, 8, 3, 0, 4, 2, 1], 'cur_cost': 855.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 7, 5, 8, 3, 2, 0, 4, 1], 'cur_cost': 916.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [4, 8, 3, 6, 5, 0, 7, 2, 1], 'cur_cost': 930.0, 'intermediate_solutions': [{'tour': [4, 5, 3, 0, 6, 8, 7, 2, 1], 'cur_cost': 1026.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 0, 3, 5, 6, 8, 7, 1, 2], 'cur_cost': 885.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 3, 6, 8, 7, 2, 5, 1], 'cur_cost': 1111.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:36:50,736 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:36:50,736 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:36:50,737 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=872.000, 多样性=0.869
2025-08-04 17:36:50,737 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-04 17:36:50,737 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-04 17:36:50,737 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:36:50,738 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.07573654791893238, 'best_improvement': -0.11508951406649616}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.011494252873563194}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.01591936778089845, 'recent_improvements': [0.006351764685484693, -0.0458718261002005, 0.03819050024728159], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.7222222222222222, 'new_diversity': 0.7222222222222222, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:36:50,738 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-04 17:36:50,738 - __main__ - INFO - simple1_9 开始进化第 5 代
2025-08-04 17:36:50,738 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-04 17:36:50,739 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:36:50,739 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=872.000, 多样性=0.869
2025-08-04 17:36:50,739 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:36:50,740 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.869
2025-08-04 17:36:50,740 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:36:50,741 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.722
2025-08-04 17:36:50,742 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-04 17:36:50,742 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:36:50,742 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-04 17:36:50,743 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-04 17:36:50,751 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.214, 适应度梯度: 1.100, 聚类评分: 0.000, 覆盖率: 0.005, 收敛趋势: 0.000, 多样性: 0.554
2025-08-04 17:36:50,751 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-04 17:36:50,751 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:36:50,751 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:36:50,754 - visualization.landscape_visualizer - INFO - 插值约束: 15 个点被约束到最小值 680.00
2025-08-04 17:36:50,758 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:36:50,840 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_5_20250804_173650.html
2025-08-04 17:36:50,942 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_5_20250804_173650.html
2025-08-04 17:36:50,942 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 5
2025-08-04 17:36:50,942 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-04 17:36:50,942 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2001秒
2025-08-04 17:36:50,943 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.21428571428571427, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 1.1000000000000025, 'local_optima_density': 0.21428571428571427, 'gradient_variance': 17222.432857142856, 'cluster_count': 0}, 'population_state': {'diversity': 0.554160125588697, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0054, 'fitness_entropy': 0.9654305096819759, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.005)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 1.100)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754300210.7514691, 'performance_metrics': {}}}
2025-08-04 17:36:50,943 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:36:50,943 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:36:50,944 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:36:50,944 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:36:50,944 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-04 17:36:50,944 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-04 17:36:50,944 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-04 17:36:50,945 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:36:50,945 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:36:50,946 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-04 17:36:50,946 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:36:50,946 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:36:50,947 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 3} (总数: 2, 保护比例: 0.20)
2025-08-04 17:36:50,947 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:36:50,947 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:36:50,947 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:50,948 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:36:50,948 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,948 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,948 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,949 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,949 - ExplorationExpert - INFO - 探索路径生成完成，成本: 935.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:36:50,949 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [7, 5, 8, 3, 4, 0, 1, 6, 2], 'cur_cost': 935.0, 'intermediate_solutions': [{'tour': [3, 0, 7, 6, 4, 8, 5, 2, 1], 'cur_cost': 1108.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 6, 7, 0, 4, 8, 2, 5, 1], 'cur_cost': 1049.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 6, 7, 4, 8, 0, 5, 2, 1], 'cur_cost': 1111.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:36:50,949 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 935.00)
2025-08-04 17:36:50,950 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-04 17:36:50,950 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:36:50,950 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:36:50,950 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 945.0
2025-08-04 17:36:50,957 - ExploitationExpert - INFO - res_population_num: 4
2025-08-04 17:36:50,957 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680, 778.0]
2025-08-04 17:36:50,957 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 2, 4, 7, 5, 3, 8, 6], dtype=int64)]
2025-08-04 17:36:50,958 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:36:50,958 - ExploitationExpert - INFO - populations: [{'tour': [7, 5, 8, 3, 4, 0, 1, 6, 2], 'cur_cost': 935.0}, {'tour': array([7, 8, 4, 5, 2, 0, 1, 6, 3], dtype=int64), 'cur_cost': 945.0}, {'tour': [3, 6, 1, 4, 8, 5, 2, 7, 0], 'cur_cost': 1031.0}, {'tour': [6, 4, 0, 1, 3, 8, 2, 7, 5], 'cur_cost': 891.0}, {'tour': [2, 6, 4, 5, 3, 7, 0, 1, 8], 'cur_cost': 1005.0}, {'tour': [6, 8, 0, 2, 4, 7, 3, 5, 1], 'cur_cost': 960.0}, {'tour': [7, 1, 0, 3, 4, 8, 2, 5, 6], 'cur_cost': 932.0}, {'tour': [7, 0, 8, 4, 3, 6, 2, 1, 5], 'cur_cost': 1101.0}, {'tour': [2, 3, 0, 1, 7, 5, 6, 8, 4], 'cur_cost': 872.0}, {'tour': [4, 8, 3, 6, 5, 0, 7, 2, 1], 'cur_cost': 930.0}]
2025-08-04 17:36:50,959 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:36:50,959 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-08-04 17:36:50,959 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([7, 8, 4, 5, 2, 0, 1, 6, 3], dtype=int64), 'cur_cost': 945.0, 'intermediate_solutions': [{'tour': array([0, 4, 5, 3, 2, 7, 6, 1, 8]), 'cur_cost': 1081.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 0, 4, 5, 2, 7, 6, 1, 8]), 'cur_cost': 1108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 3, 0, 4, 5, 7, 6, 1, 8]), 'cur_cost': 1047.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 3, 0, 4, 2, 7, 6, 1, 8]), 'cur_cost': 931.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 2, 3, 0, 4, 7, 6, 1, 8]), 'cur_cost': 1100.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:36:50,960 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 945.00)
2025-08-04 17:36:50,960 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-04 17:36:50,960 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:36:50,960 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:36:50,961 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 1087.0
2025-08-04 17:36:50,968 - ExploitationExpert - INFO - res_population_num: 4
2025-08-04 17:36:50,968 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680, 778.0]
2025-08-04 17:36:50,968 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 2, 4, 7, 5, 3, 8, 6], dtype=int64)]
2025-08-04 17:36:50,969 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:36:50,969 - ExploitationExpert - INFO - populations: [{'tour': [7, 5, 8, 3, 4, 0, 1, 6, 2], 'cur_cost': 935.0}, {'tour': array([7, 8, 4, 5, 2, 0, 1, 6, 3], dtype=int64), 'cur_cost': 945.0}, {'tour': array([8, 2, 3, 1, 4, 6, 7, 5, 0], dtype=int64), 'cur_cost': 1087.0}, {'tour': [6, 4, 0, 1, 3, 8, 2, 7, 5], 'cur_cost': 891.0}, {'tour': [2, 6, 4, 5, 3, 7, 0, 1, 8], 'cur_cost': 1005.0}, {'tour': [6, 8, 0, 2, 4, 7, 3, 5, 1], 'cur_cost': 960.0}, {'tour': [7, 1, 0, 3, 4, 8, 2, 5, 6], 'cur_cost': 932.0}, {'tour': [7, 0, 8, 4, 3, 6, 2, 1, 5], 'cur_cost': 1101.0}, {'tour': [2, 3, 0, 1, 7, 5, 6, 8, 4], 'cur_cost': 872.0}, {'tour': [4, 8, 3, 6, 5, 0, 7, 2, 1], 'cur_cost': 930.0}]
2025-08-04 17:36:50,970 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:36:50,970 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-08-04 17:36:50,971 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([8, 2, 3, 1, 4, 6, 7, 5, 0], dtype=int64), 'cur_cost': 1087.0, 'intermediate_solutions': [{'tour': array([1, 6, 3, 4, 8, 5, 2, 7, 0]), 'cur_cost': 964.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 1, 6, 3, 8, 5, 2, 7, 0]), 'cur_cost': 975.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 4, 1, 6, 3, 5, 2, 7, 0]), 'cur_cost': 1007.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 4, 1, 6, 8, 5, 2, 7, 0]), 'cur_cost': 1097.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 8, 4, 1, 6, 5, 2, 7, 0]), 'cur_cost': 980.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:36:50,972 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1087.00)
2025-08-04 17:36:50,972 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:36:50,972 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:36:50,972 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:50,973 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:36:50,973 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,973 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,973 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,973 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,974 - ExplorationExpert - INFO - 探索路径生成完成，成本: 932.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:36:50,974 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [6, 7, 5, 0, 1, 2, 3, 8, 4], 'cur_cost': 932.0, 'intermediate_solutions': [{'tour': [6, 4, 0, 3, 1, 8, 2, 7, 5], 'cur_cost': 1070.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 4, 0, 1, 8, 3, 2, 7, 5], 'cur_cost': 951.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 1, 3, 8, 2, 7, 5, 4], 'cur_cost': 975.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:36:50,974 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 932.00)
2025-08-04 17:36:50,974 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:36:50,975 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:36:50,975 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:50,975 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:36:50,975 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,975 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,976 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,976 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,976 - ExplorationExpert - INFO - 探索路径生成完成，成本: 993.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:36:50,976 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [5, 0, 1, 3, 4, 8, 2, 6, 7], 'cur_cost': 993.0, 'intermediate_solutions': [{'tour': [2, 6, 4, 0, 3, 7, 5, 1, 8], 'cur_cost': 1120.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 6, 4, 5, 3, 7, 0, 8, 1], 'cur_cost': 1137.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 6, 5, 3, 7, 0, 1, 8, 4], 'cur_cost': 834.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:36:50,976 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 993.00)
2025-08-04 17:36:50,977 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:36:50,977 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:36:50,977 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:50,977 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:36:50,977 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,978 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,978 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,978 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,978 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1018.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:36:50,979 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [1, 7, 6, 3, 0, 4, 2, 5, 8], 'cur_cost': 1018.0, 'intermediate_solutions': [{'tour': [6, 8, 7, 2, 4, 0, 3, 5, 1], 'cur_cost': 967.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 8, 0, 2, 7, 4, 3, 5, 1], 'cur_cost': 1096.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 8, 2, 4, 7, 3, 5, 1, 0], 'cur_cost': 817.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:36:50,979 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1018.00)
2025-08-04 17:36:50,979 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:36:50,980 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:36:50,980 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:50,981 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:36:50,981 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,982 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,982 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,982 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,982 - ExplorationExpert - INFO - 探索路径生成完成，成本: 736.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:36:50,983 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [7, 6, 5, 3, 8, 4, 2, 0, 1], 'cur_cost': 736.0, 'intermediate_solutions': [{'tour': [7, 0, 1, 3, 4, 8, 2, 5, 6], 'cur_cost': 932.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 1, 3, 0, 4, 8, 2, 5, 6], 'cur_cost': 1018.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 1, 0, 3, 4, 8, 2, 5, 6], 'cur_cost': 932.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:36:50,983 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 736.00)
2025-08-04 17:36:50,983 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-04 17:36:50,983 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:36:50,984 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:36:50,984 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 929.0
2025-08-04 17:36:50,990 - ExploitationExpert - INFO - res_population_num: 4
2025-08-04 17:36:50,990 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680, 778.0]
2025-08-04 17:36:50,990 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 2, 4, 7, 5, 3, 8, 6], dtype=int64)]
2025-08-04 17:36:50,991 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:36:50,992 - ExploitationExpert - INFO - populations: [{'tour': [7, 5, 8, 3, 4, 0, 1, 6, 2], 'cur_cost': 935.0}, {'tour': array([7, 8, 4, 5, 2, 0, 1, 6, 3], dtype=int64), 'cur_cost': 945.0}, {'tour': array([8, 2, 3, 1, 4, 6, 7, 5, 0], dtype=int64), 'cur_cost': 1087.0}, {'tour': [6, 7, 5, 0, 1, 2, 3, 8, 4], 'cur_cost': 932.0}, {'tour': [5, 0, 1, 3, 4, 8, 2, 6, 7], 'cur_cost': 993.0}, {'tour': [1, 7, 6, 3, 0, 4, 2, 5, 8], 'cur_cost': 1018.0}, {'tour': [7, 6, 5, 3, 8, 4, 2, 0, 1], 'cur_cost': 736.0}, {'tour': array([6, 5, 3, 4, 1, 2, 0, 8, 7], dtype=int64), 'cur_cost': 929.0}, {'tour': [2, 3, 0, 1, 7, 5, 6, 8, 4], 'cur_cost': 872.0}, {'tour': [4, 8, 3, 6, 5, 0, 7, 2, 1], 'cur_cost': 930.0}]
2025-08-04 17:36:50,993 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:36:50,993 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-08-04 17:36:50,993 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([6, 5, 3, 4, 1, 2, 0, 8, 7], dtype=int64), 'cur_cost': 929.0, 'intermediate_solutions': [{'tour': array([8, 0, 7, 4, 3, 6, 2, 1, 5]), 'cur_cost': 1128.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 8, 0, 7, 3, 6, 2, 1, 5]), 'cur_cost': 1111.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 4, 8, 0, 7, 6, 2, 1, 5]), 'cur_cost': 1073.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 4, 8, 0, 3, 6, 2, 1, 5]), 'cur_cost': 1114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 3, 4, 8, 0, 6, 2, 1, 5]), 'cur_cost': 1074.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:36:50,994 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 929.00)
2025-08-04 17:36:50,994 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:36:50,994 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:36:50,994 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:50,995 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:36:50,995 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,995 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,996 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,996 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,996 - ExplorationExpert - INFO - 探索路径生成完成，成本: 819.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:36:50,997 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [1, 4, 0, 6, 7, 5, 3, 8, 2], 'cur_cost': 819.0, 'intermediate_solutions': [{'tour': [2, 3, 0, 8, 7, 5, 6, 1, 4], 'cur_cost': 918.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 2, 0, 1, 7, 5, 6, 8, 4], 'cur_cost': 941.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 3, 4, 0, 1, 7, 5, 6, 8], 'cur_cost': 904.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:36:50,997 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 819.00)
2025-08-04 17:36:50,997 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:36:50,998 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:36:50,998 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:36:50,998 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:36:50,999 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,999 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,999 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:50,999 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:36:51,000 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1016.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:36:51,000 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 3, 1, 5, 4, 2, 8, 7, 6], 'cur_cost': 1016.0, 'intermediate_solutions': [{'tour': [0, 8, 3, 6, 5, 4, 7, 2, 1], 'cur_cost': 932.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 2, 7, 0, 5, 6, 3, 1], 'cur_cost': 979.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 3, 6, 4, 5, 0, 7, 2, 1], 'cur_cost': 1125.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:36:51,000 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1016.00)
2025-08-04 17:36:51,000 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:36:51,001 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:36:51,002 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [7, 5, 8, 3, 4, 0, 1, 6, 2], 'cur_cost': 935.0, 'intermediate_solutions': [{'tour': [3, 0, 7, 6, 4, 8, 5, 2, 1], 'cur_cost': 1108.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 6, 7, 0, 4, 8, 2, 5, 1], 'cur_cost': 1049.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 6, 7, 4, 8, 0, 5, 2, 1], 'cur_cost': 1111.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 8, 4, 5, 2, 0, 1, 6, 3], dtype=int64), 'cur_cost': 945.0, 'intermediate_solutions': [{'tour': array([0, 4, 5, 3, 2, 7, 6, 1, 8]), 'cur_cost': 1081.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 0, 4, 5, 2, 7, 6, 1, 8]), 'cur_cost': 1108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 3, 0, 4, 5, 7, 6, 1, 8]), 'cur_cost': 1047.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 3, 0, 4, 2, 7, 6, 1, 8]), 'cur_cost': 931.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 2, 3, 0, 4, 7, 6, 1, 8]), 'cur_cost': 1100.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 2, 3, 1, 4, 6, 7, 5, 0], dtype=int64), 'cur_cost': 1087.0, 'intermediate_solutions': [{'tour': array([1, 6, 3, 4, 8, 5, 2, 7, 0]), 'cur_cost': 964.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 1, 6, 3, 8, 5, 2, 7, 0]), 'cur_cost': 975.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 4, 1, 6, 3, 5, 2, 7, 0]), 'cur_cost': 1007.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 4, 1, 6, 8, 5, 2, 7, 0]), 'cur_cost': 1097.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 8, 4, 1, 6, 5, 2, 7, 0]), 'cur_cost': 980.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [6, 7, 5, 0, 1, 2, 3, 8, 4], 'cur_cost': 932.0, 'intermediate_solutions': [{'tour': [6, 4, 0, 3, 1, 8, 2, 7, 5], 'cur_cost': 1070.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 4, 0, 1, 8, 3, 2, 7, 5], 'cur_cost': 951.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 1, 3, 8, 2, 7, 5, 4], 'cur_cost': 975.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [5, 0, 1, 3, 4, 8, 2, 6, 7], 'cur_cost': 993.0, 'intermediate_solutions': [{'tour': [2, 6, 4, 0, 3, 7, 5, 1, 8], 'cur_cost': 1120.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 6, 4, 5, 3, 7, 0, 8, 1], 'cur_cost': 1137.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 6, 5, 3, 7, 0, 1, 8, 4], 'cur_cost': 834.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 6, 3, 0, 4, 2, 5, 8], 'cur_cost': 1018.0, 'intermediate_solutions': [{'tour': [6, 8, 7, 2, 4, 0, 3, 5, 1], 'cur_cost': 967.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 8, 0, 2, 7, 4, 3, 5, 1], 'cur_cost': 1096.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 8, 2, 4, 7, 3, 5, 1, 0], 'cur_cost': 817.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [7, 6, 5, 3, 8, 4, 2, 0, 1], 'cur_cost': 736.0, 'intermediate_solutions': [{'tour': [7, 0, 1, 3, 4, 8, 2, 5, 6], 'cur_cost': 932.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 1, 3, 0, 4, 8, 2, 5, 6], 'cur_cost': 1018.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 1, 0, 3, 4, 8, 2, 5, 6], 'cur_cost': 932.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 5, 3, 4, 1, 2, 0, 8, 7], dtype=int64), 'cur_cost': 929.0, 'intermediate_solutions': [{'tour': array([8, 0, 7, 4, 3, 6, 2, 1, 5]), 'cur_cost': 1128.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 8, 0, 7, 3, 6, 2, 1, 5]), 'cur_cost': 1111.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 4, 8, 0, 7, 6, 2, 1, 5]), 'cur_cost': 1073.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 4, 8, 0, 3, 6, 2, 1, 5]), 'cur_cost': 1114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 3, 4, 8, 0, 6, 2, 1, 5]), 'cur_cost': 1074.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 0, 6, 7, 5, 3, 8, 2], 'cur_cost': 819.0, 'intermediate_solutions': [{'tour': [2, 3, 0, 8, 7, 5, 6, 1, 4], 'cur_cost': 918.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 2, 0, 1, 7, 5, 6, 8, 4], 'cur_cost': 941.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 3, 4, 0, 1, 7, 5, 6, 8], 'cur_cost': 904.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 1, 5, 4, 2, 8, 7, 6], 'cur_cost': 1016.0, 'intermediate_solutions': [{'tour': [0, 8, 3, 6, 5, 4, 7, 2, 1], 'cur_cost': 932.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 2, 7, 0, 5, 6, 3, 1], 'cur_cost': 979.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 3, 6, 4, 5, 0, 7, 2, 1], 'cur_cost': 1125.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:36:51,004 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:36:51,004 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:36:51,005 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=736.000, 多样性=0.894
2025-08-04 17:36:51,005 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-04 17:36:51,005 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-04 17:36:51,005 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:36:51,006 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.07221063139742519, 'best_improvement': 0.1559633027522936}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.02840909090909104}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.01493236090936593, 'recent_improvements': [-0.0458718261002005, 0.03819050024728159, -0.07573654791893238], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.7222222222222222, 'new_diversity': 0.7222222222222222, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:36:51,006 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-04 17:36:51,008 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_solution.json
2025-08-04 17:36:51,008 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_20250804_173651.solution
2025-08-04 17:36:51,009 - __main__ - INFO - 实例执行完成 - 运行时间: 10.40s, 最佳成本: 680.0
2025-08-04 17:36:51,009 - __main__ - INFO - 实例 simple1_9 处理完成
