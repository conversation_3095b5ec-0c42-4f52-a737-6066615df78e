2025-08-04 16:43:05,486 - __main__ - INFO - simple1_9 开始进化第 1 代
2025-08-04 16:43:05,489 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-04 16:43:05,493 - StatsExpert - INFO - 开始统计分析
2025-08-04 16:43:05,500 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=706.000, 多样性=0.889
2025-08-04 16:43:05,503 - PathExpert - INFO - 开始路径结构分析
2025-08-04 16:43:05,504 - PathExpert - INFO - 路径结构分析完成: 公共边数量=4, 路径相似性=0.889
2025-08-04 16:43:05,507 - EliteExpert - INFO - 开始精英解分析
2025-08-04 16:43:05,541 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-04 16:43:05,542 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 16:43:05,542 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-04 16:43:05,543 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-04 16:43:05,912 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.300, 适应度梯度: -20.320, 聚类评分: 0.000, 覆盖率: 0.001, 收敛趋势: 0.000, 多样性: 0.800
2025-08-04 16:43:05,913 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-04 16:43:05,913 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-04 16:43:05,914 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 16:43:06,319 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记，坐标系统已统一
2025-08-04 16:43:10,156 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_1_20250804_164310.html
2025-08-04 16:43:10,203 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_1_20250804_164310.html
2025-08-04 16:43:10,204 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-04 16:43:10,205 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-04 16:43:10,205 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 4.6646秒
2025-08-04 16:43:10,206 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-04 16:43:10,207 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -20.320000000000004, 'local_optima_density': 0.3, 'gradient_variance': 39864.91360000001, 'cluster_count': 0}, 'population_state': {'diversity': 0.8, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.001, 'fitness_entropy': 0.9172088932791937, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -20.320)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.001)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.800)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754296985.913058, 'performance_metrics': {}}}
2025-08-04 16:43:10,208 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 16:43:10,209 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 16:43:10,209 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 16:43:10,209 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 16:43:10,209 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 16:43:10,210 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-04 16:43:10,210 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 16:43:10,210 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 16:43:10,210 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 16:43:10,211 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 16:43:10,211 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 16:43:10,211 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 16:43:10,212 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 7} (总数: 2, 保护比例: 0.20)
2025-08-04 16:43:10,212 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 16:43:10,212 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 16:43:10,212 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:10,223 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 16:43:10,224 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:10,376 - ExplorationExpert - INFO - 探索路径生成完成，成本: 731.0, 路径长度: 9, 收集中间解: 0
2025-08-04 16:43:10,376 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [4, 2, 8, 5, 3, 6, 7, 0, 1], 'cur_cost': 731.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 16:43:10,376 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 731.00)
2025-08-04 16:43:10,377 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 16:43:10,377 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 16:43:10,377 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:10,378 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 16:43:10,378 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:10,378 - ExplorationExpert - INFO - 探索路径生成完成，成本: 839.0, 路径长度: 9, 收集中间解: 0
2025-08-04 16:43:10,378 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [3, 5, 7, 6, 8, 4, 2, 0, 1], 'cur_cost': 839.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 16:43:10,379 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 839.00)
2025-08-04 16:43:10,379 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 16:43:10,379 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 16:43:10,379 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:10,380 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 16:43:10,380 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:10,381 - ExplorationExpert - INFO - 探索路径生成完成，成本: 775.0, 路径长度: 9, 收集中间解: 0
2025-08-04 16:43:10,381 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [5, 6, 7, 3, 8, 4, 2, 0, 1], 'cur_cost': 775.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 16:43:10,381 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 775.00)
2025-08-04 16:43:10,381 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-04 16:43:10,382 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 16:43:10,385 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 16:43:10,386 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1049.0
2025-08-04 16:43:11,619 - ExploitationExpert - INFO - res_population_num: 1
2025-08-04 16:43:11,619 - ExploitationExpert - INFO - res_population_costs: [680.0]
2025-08-04 16:43:11,619 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 16:43:11,620 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 16:43:11,620 - ExploitationExpert - INFO - populations: [{'tour': [4, 2, 8, 5, 3, 6, 7, 0, 1], 'cur_cost': 731.0}, {'tour': [3, 5, 7, 6, 8, 4, 2, 0, 1], 'cur_cost': 839.0}, {'tour': [5, 6, 7, 3, 8, 4, 2, 0, 1], 'cur_cost': 775.0}, {'tour': array([1, 4, 2, 6, 8, 0, 3, 7, 5], dtype=int64), 'cur_cost': 1049.0}, {'tour': array([8, 6, 5, 7, 1, 2, 4, 0, 3], dtype=int64), 'cur_cost': 886.0}, {'tour': array([0, 7, 2, 4, 3, 5, 8, 6, 1], dtype=int64), 'cur_cost': 853.0}, {'tour': array([7, 1, 3, 5, 0, 4, 6, 2, 8], dtype=int64), 'cur_cost': 1089.0}, {'tour': array([4, 2, 1, 0, 6, 5, 3, 7, 8], dtype=int64), 'cur_cost': 706.0}, {'tour': array([4, 2, 3, 7, 6, 8, 5, 1, 0], dtype=int64), 'cur_cost': 878.0}, {'tour': array([3, 2, 0, 1, 6, 8, 7, 4, 5], dtype=int64), 'cur_cost': 962.0}]
2025-08-04 16:43:11,622 - ExploitationExpert - INFO - 局部搜索耗时: 1.24秒，最大迭代次数: 10
2025-08-04 16:43:11,622 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-04 16:43:11,623 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([1, 4, 2, 6, 8, 0, 3, 7, 5], dtype=int64), 'cur_cost': 1049.0, 'intermediate_solutions': [{'tour': array([5, 0, 3, 8, 1, 7, 4, 6, 2], dtype=int64), 'cur_cost': 1236.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 5, 0, 3, 1, 7, 4, 6, 2], dtype=int64), 'cur_cost': 1173.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 8, 5, 0, 3, 7, 4, 6, 2], dtype=int64), 'cur_cost': 1157.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 8, 5, 0, 1, 7, 4, 6, 2], dtype=int64), 'cur_cost': 1054.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 1, 8, 5, 0, 7, 4, 6, 2], dtype=int64), 'cur_cost': 1233.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 16:43:11,624 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1049.00)
2025-08-04 16:43:11,624 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 16:43:11,624 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 16:43:11,624 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:11,625 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 16:43:11,625 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:11,625 - ExplorationExpert - INFO - 探索路径生成完成，成本: 799.0, 路径长度: 9, 收集中间解: 0
2025-08-04 16:43:11,625 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [4, 2, 0, 7, 3, 8, 5, 6, 1], 'cur_cost': 799.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 16:43:11,626 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 799.00)
2025-08-04 16:43:11,626 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 16:43:11,626 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 16:43:11,626 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:11,626 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 16:43:11,627 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:11,627 - ExplorationExpert - INFO - 探索路径生成完成，成本: 877.0, 路径长度: 9, 收集中间解: 0
2025-08-04 16:43:11,627 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 6, 4, 5, 3, 7, 8, 2, 1], 'cur_cost': 877.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 16:43:11,628 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 877.00)
2025-08-04 16:43:11,628 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-04 16:43:11,628 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 16:43:11,628 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 16:43:11,629 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1051.0
2025-08-04 16:43:13,400 - ExploitationExpert - INFO - res_population_num: 2
2025-08-04 16:43:13,401 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0]
2025-08-04 16:43:13,401 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 16:43:13,401 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 16:43:13,402 - ExploitationExpert - INFO - populations: [{'tour': [4, 2, 8, 5, 3, 6, 7, 0, 1], 'cur_cost': 731.0}, {'tour': [3, 5, 7, 6, 8, 4, 2, 0, 1], 'cur_cost': 839.0}, {'tour': [5, 6, 7, 3, 8, 4, 2, 0, 1], 'cur_cost': 775.0}, {'tour': array([1, 4, 2, 6, 8, 0, 3, 7, 5], dtype=int64), 'cur_cost': 1049.0}, {'tour': [4, 2, 0, 7, 3, 8, 5, 6, 1], 'cur_cost': 799.0}, {'tour': [0, 6, 4, 5, 3, 7, 8, 2, 1], 'cur_cost': 877.0}, {'tour': array([2, 5, 6, 3, 0, 4, 1, 8, 7], dtype=int64), 'cur_cost': 1051.0}, {'tour': array([4, 2, 1, 0, 6, 5, 3, 7, 8], dtype=int64), 'cur_cost': 706.0}, {'tour': array([4, 2, 3, 7, 6, 8, 5, 1, 0], dtype=int64), 'cur_cost': 878.0}, {'tour': array([3, 2, 0, 1, 6, 8, 7, 4, 5], dtype=int64), 'cur_cost': 962.0}]
2025-08-04 16:43:13,403 - ExploitationExpert - INFO - 局部搜索耗时: 1.77秒，最大迭代次数: 10
2025-08-04 16:43:13,403 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-04 16:43:13,404 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([2, 5, 6, 3, 0, 4, 1, 8, 7], dtype=int64), 'cur_cost': 1051.0, 'intermediate_solutions': [{'tour': array([3, 1, 7, 5, 0, 4, 6, 2, 8], dtype=int64), 'cur_cost': 1090.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 3, 1, 7, 0, 4, 6, 2, 8], dtype=int64), 'cur_cost': 1058.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 5, 3, 1, 7, 4, 6, 2, 8], dtype=int64), 'cur_cost': 1149.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 5, 3, 1, 0, 4, 6, 2, 8], dtype=int64), 'cur_cost': 950.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 0, 5, 3, 1, 4, 6, 2, 8], dtype=int64), 'cur_cost': 1070.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 16:43:13,405 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1051.00)
2025-08-04 16:43:13,405 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 16:43:13,405 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 16:43:13,405 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:13,406 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 16:43:13,406 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,406 - ExplorationExpert - INFO - 探索路径生成完成，成本: 972.0, 路径长度: 9, 收集中间解: 0
2025-08-04 16:43:13,406 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [2, 7, 6, 0, 8, 3, 5, 4, 1], 'cur_cost': 972.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 16:43:13,407 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 972.00)
2025-08-04 16:43:13,407 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 16:43:13,407 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 16:43:13,407 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:13,407 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 16:43:13,408 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,408 - ExplorationExpert - INFO - 探索路径生成完成，成本: 891.0, 路径长度: 9, 收集中间解: 0
2025-08-04 16:43:13,408 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [7, 5, 8, 4, 0, 1, 6, 3, 2], 'cur_cost': 891.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 16:43:13,408 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 891.00)
2025-08-04 16:43:13,408 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 16:43:13,408 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 16:43:13,409 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:13,409 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 16:43:13,409 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,409 - ExplorationExpert - INFO - 探索路径生成完成，成本: 940.0, 路径长度: 9, 收集中间解: 0
2025-08-04 16:43:13,410 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [3, 4, 1, 0, 5, 7, 6, 8, 2], 'cur_cost': 940.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 16:43:13,410 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 940.00)
2025-08-04 16:43:13,410 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 16:43:13,410 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 16:43:13,411 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [4, 2, 8, 5, 3, 6, 7, 0, 1], 'cur_cost': 731.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [3, 5, 7, 6, 8, 4, 2, 0, 1], 'cur_cost': 839.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [5, 6, 7, 3, 8, 4, 2, 0, 1], 'cur_cost': 775.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([1, 4, 2, 6, 8, 0, 3, 7, 5], dtype=int64), 'cur_cost': 1049.0, 'intermediate_solutions': [{'tour': array([5, 0, 3, 8, 1, 7, 4, 6, 2], dtype=int64), 'cur_cost': 1236.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 5, 0, 3, 1, 7, 4, 6, 2], dtype=int64), 'cur_cost': 1173.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 8, 5, 0, 3, 7, 4, 6, 2], dtype=int64), 'cur_cost': 1157.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 8, 5, 0, 1, 7, 4, 6, 2], dtype=int64), 'cur_cost': 1054.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 1, 8, 5, 0, 7, 4, 6, 2], dtype=int64), 'cur_cost': 1233.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [4, 2, 0, 7, 3, 8, 5, 6, 1], 'cur_cost': 799.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 4, 5, 3, 7, 8, 2, 1], 'cur_cost': 877.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 5, 6, 3, 0, 4, 1, 8, 7], dtype=int64), 'cur_cost': 1051.0, 'intermediate_solutions': [{'tour': array([3, 1, 7, 5, 0, 4, 6, 2, 8], dtype=int64), 'cur_cost': 1090.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 3, 1, 7, 0, 4, 6, 2, 8], dtype=int64), 'cur_cost': 1058.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 5, 3, 1, 7, 4, 6, 2, 8], dtype=int64), 'cur_cost': 1149.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 5, 3, 1, 0, 4, 6, 2, 8], dtype=int64), 'cur_cost': 950.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 0, 5, 3, 1, 4, 6, 2, 8], dtype=int64), 'cur_cost': 1070.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [2, 7, 6, 0, 8, 3, 5, 4, 1], 'cur_cost': 972.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [7, 5, 8, 4, 0, 1, 6, 3, 2], 'cur_cost': 891.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [3, 4, 1, 0, 5, 7, 6, 8, 2], 'cur_cost': 940.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-04 16:43:13,412 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 16:43:13,413 - StatsExpert - INFO - 开始统计分析
2025-08-04 16:43:13,414 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=731.000, 多样性=0.869
2025-08-04 16:43:13,414 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-04 16:43:13,414 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-04 16:43:13,414 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 16:43:13,414 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.039477592260575706, 'best_improvement': -0.03541076487252125}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.022222222222222296}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.7777777777777778, 'new_diversity': 0.7777777777777778, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-04 16:43:13,421 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-04 16:43:13,421 - __main__ - INFO - simple1_9 开始进化第 2 代
2025-08-04 16:43:13,421 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-04 16:43:13,422 - StatsExpert - INFO - 开始统计分析
2025-08-04 16:43:13,422 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=731.000, 多样性=0.869
2025-08-04 16:43:13,422 - PathExpert - INFO - 开始路径结构分析
2025-08-04 16:43:13,423 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.869
2025-08-04 16:43:13,423 - EliteExpert - INFO - 开始精英解分析
2025-08-04 16:43:13,424 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.778
2025-08-04 16:43:13,426 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-04 16:43:13,426 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 16:43:13,426 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-04 16:43:13,426 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-04 16:43:13,432 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.167, 适应度梯度: -8.717, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 0.652
2025-08-04 16:43:13,432 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-04 16:43:13,432 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 16:43:13,432 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 16:43:13,435 - visualization.landscape_visualizer - INFO - 插值约束: 46 个点被约束到最小值 680.00
2025-08-04 16:43:13,439 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 16:43:13,523 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_2_20250804_164313.html
2025-08-04 16:43:13,557 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_2_20250804_164313.html
2025-08-04 16:43:13,558 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 2
2025-08-04 16:43:13,558 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-04 16:43:13,558 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1316秒
2025-08-04 16:43:13,559 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.16666666666666666, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -8.716666666666663, 'local_optima_density': 0.16666666666666666, 'gradient_variance': 15159.749722222223, 'cluster_count': 0}, 'population_state': {'diversity': 0.6515151515151515, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0022, 'fitness_entropy': 0.972765278018163, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -8.717)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754296993.4328785, 'performance_metrics': {}}}
2025-08-04 16:43:13,559 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 16:43:13,559 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 16:43:13,560 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 16:43:13,560 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 16:43:13,560 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 16:43:13,560 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-04 16:43:13,561 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 16:43:13,561 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 16:43:13,561 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 16:43:13,561 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 16:43:13,562 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 16:43:13,562 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 16:43:13,562 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 2} (总数: 2, 保护比例: 0.20)
2025-08-04 16:43:13,563 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 16:43:13,563 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 16:43:13,563 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:13,563 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 16:43:13,564 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,564 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,564 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,564 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,564 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1218.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:43:13,565 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [4, 6, 2, 5, 1, 3, 7, 0, 8], 'cur_cost': 1218.0, 'intermediate_solutions': [{'tour': [4, 2, 8, 7, 3, 6, 5, 0, 1], 'cur_cost': 749.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 2, 8, 5, 3, 6, 0, 7, 1], 'cur_cost': 829.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 2, 3, 8, 5, 6, 7, 0, 1], 'cur_cost': 766.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:43:13,565 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 1218.00)
2025-08-04 16:43:13,565 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 16:43:13,565 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 16:43:13,566 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:13,566 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 16:43:13,566 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,566 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,566 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,567 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,567 - ExplorationExpert - INFO - 探索路径生成完成，成本: 953.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:43:13,567 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [1, 2, 0, 7, 5, 8, 3, 6, 4], 'cur_cost': 953.0, 'intermediate_solutions': [{'tour': [8, 5, 7, 6, 3, 4, 2, 0, 1], 'cur_cost': 873.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 2, 4, 8, 6, 7, 0, 1], 'cur_cost': 898.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 5, 7, 6, 8, 4, 2, 0, 1], 'cur_cost': 839.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:43:13,567 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 953.00)
2025-08-04 16:43:13,567 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 16:43:13,568 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 16:43:13,568 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:13,568 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 16:43:13,568 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,569 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,569 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,569 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,569 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1087.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:43:13,569 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 7, 4, 6, 1, 3, 2, 8, 5], 'cur_cost': 1087.0, 'intermediate_solutions': [{'tour': [5, 6, 7, 3, 8, 4, 1, 0, 2], 'cur_cost': 834.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 6, 7, 3, 8, 4, 1, 0, 2], 'cur_cost': 834.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 5, 6, 7, 3, 8, 2, 0, 1], 'cur_cost': 808.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:43:13,570 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 1087.00)
2025-08-04 16:43:13,570 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-04 16:43:13,571 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 16:43:13,571 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 16:43:13,572 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1046.0
2025-08-04 16:43:13,641 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 16:43:13,641 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0]
2025-08-04 16:43:13,642 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-04 16:43:13,642 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 16:43:13,643 - ExploitationExpert - INFO - populations: [{'tour': [4, 6, 2, 5, 1, 3, 7, 0, 8], 'cur_cost': 1218.0}, {'tour': [1, 2, 0, 7, 5, 8, 3, 6, 4], 'cur_cost': 953.0}, {'tour': [0, 7, 4, 6, 1, 3, 2, 8, 5], 'cur_cost': 1087.0}, {'tour': array([1, 6, 2, 4, 5, 0, 3, 8, 7], dtype=int64), 'cur_cost': 1046.0}, {'tour': [4, 2, 0, 7, 3, 8, 5, 6, 1], 'cur_cost': 799.0}, {'tour': [0, 6, 4, 5, 3, 7, 8, 2, 1], 'cur_cost': 877.0}, {'tour': [2, 5, 6, 3, 0, 4, 1, 8, 7], 'cur_cost': 1051.0}, {'tour': [2, 7, 6, 0, 8, 3, 5, 4, 1], 'cur_cost': 972.0}, {'tour': [7, 5, 8, 4, 0, 1, 6, 3, 2], 'cur_cost': 891.0}, {'tour': [3, 4, 1, 0, 5, 7, 6, 8, 2], 'cur_cost': 940.0}]
2025-08-04 16:43:13,643 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 16:43:13,644 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-04 16:43:13,644 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([1, 6, 2, 4, 5, 0, 3, 8, 7], dtype=int64), 'cur_cost': 1046.0, 'intermediate_solutions': [{'tour': array([2, 4, 1, 6, 8, 0, 3, 7, 5]), 'cur_cost': 986.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 2, 4, 1, 8, 0, 3, 7, 5]), 'cur_cost': 989.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 6, 2, 4, 1, 0, 3, 7, 5]), 'cur_cost': 888.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 6, 2, 4, 8, 0, 3, 7, 5]), 'cur_cost': 1016.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 8, 6, 2, 4, 0, 3, 7, 5]), 'cur_cost': 1066.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 16:43:13,645 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1046.00)
2025-08-04 16:43:13,645 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 16:43:13,645 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 16:43:13,645 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:13,646 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 16:43:13,646 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,646 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,646 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,647 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,647 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1155.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:43:13,647 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [3, 4, 8, 6, 1, 7, 2, 5, 0], 'cur_cost': 1155.0, 'intermediate_solutions': [{'tour': [4, 2, 0, 7, 8, 3, 5, 6, 1], 'cur_cost': 788.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 2, 0, 7, 8, 3, 5, 6, 1], 'cur_cost': 788.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 2, 7, 3, 8, 5, 6, 1], 'cur_cost': 875.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:43:13,647 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1155.00)
2025-08-04 16:43:13,648 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 16:43:13,648 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 16:43:13,648 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:13,648 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 16:43:13,648 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,649 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,649 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,649 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,649 - ExplorationExpert - INFO - 探索路径生成完成，成本: 861.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:43:13,649 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [5, 3, 8, 4, 7, 6, 0, 1, 2], 'cur_cost': 861.0, 'intermediate_solutions': [{'tour': [7, 6, 4, 5, 3, 0, 8, 2, 1], 'cur_cost': 1058.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 4, 5, 8, 7, 3, 2, 1], 'cur_cost': 963.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 5, 3, 7, 4, 8, 2, 1], 'cur_cost': 759.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:43:13,650 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 861.00)
2025-08-04 16:43:13,650 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-04 16:43:13,650 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 16:43:13,650 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 16:43:13,651 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1042.0
2025-08-04 16:43:13,718 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 16:43:13,719 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0]
2025-08-04 16:43:13,719 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-04 16:43:13,720 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 16:43:13,720 - ExploitationExpert - INFO - populations: [{'tour': [4, 6, 2, 5, 1, 3, 7, 0, 8], 'cur_cost': 1218.0}, {'tour': [1, 2, 0, 7, 5, 8, 3, 6, 4], 'cur_cost': 953.0}, {'tour': [0, 7, 4, 6, 1, 3, 2, 8, 5], 'cur_cost': 1087.0}, {'tour': array([1, 6, 2, 4, 5, 0, 3, 8, 7], dtype=int64), 'cur_cost': 1046.0}, {'tour': [3, 4, 8, 6, 1, 7, 2, 5, 0], 'cur_cost': 1155.0}, {'tour': [5, 3, 8, 4, 7, 6, 0, 1, 2], 'cur_cost': 861.0}, {'tour': array([0, 3, 6, 1, 8, 2, 5, 7, 4], dtype=int64), 'cur_cost': 1042.0}, {'tour': [2, 7, 6, 0, 8, 3, 5, 4, 1], 'cur_cost': 972.0}, {'tour': [7, 5, 8, 4, 0, 1, 6, 3, 2], 'cur_cost': 891.0}, {'tour': [3, 4, 1, 0, 5, 7, 6, 8, 2], 'cur_cost': 940.0}]
2025-08-04 16:43:13,722 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 16:43:13,722 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-04 16:43:13,723 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([0, 3, 6, 1, 8, 2, 5, 7, 4], dtype=int64), 'cur_cost': 1042.0, 'intermediate_solutions': [{'tour': array([6, 5, 2, 3, 0, 4, 1, 8, 7]), 'cur_cost': 1051.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 6, 5, 2, 0, 4, 1, 8, 7]), 'cur_cost': 984.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 3, 6, 5, 2, 4, 1, 8, 7]), 'cur_cost': 975.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 3, 6, 5, 0, 4, 1, 8, 7]), 'cur_cost': 1031.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 0, 3, 6, 5, 4, 1, 8, 7]), 'cur_cost': 1062.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 16:43:13,724 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1042.00)
2025-08-04 16:43:13,724 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 16:43:13,724 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 16:43:13,724 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:13,725 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 16:43:13,725 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,725 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,725 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,726 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,726 - ExplorationExpert - INFO - 探索路径生成完成，成本: 769.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:43:13,726 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 7, 6, 5, 8, 3, 4, 2, 1], 'cur_cost': 769.0, 'intermediate_solutions': [{'tour': [2, 7, 6, 0, 8, 3, 4, 5, 1], 'cur_cost': 1090.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 7, 6, 5, 3, 8, 0, 4, 1], 'cur_cost': 888.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 6, 0, 8, 3, 5, 4, 1, 2], 'cur_cost': 972.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:43:13,727 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 769.00)
2025-08-04 16:43:13,727 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 16:43:13,727 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 16:43:13,727 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:13,727 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 16:43:13,728 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,728 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,728 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,728 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,729 - ExplorationExpert - INFO - 探索路径生成完成，成本: 853.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:43:13,729 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [1, 7, 5, 6, 0, 3, 8, 4, 2], 'cur_cost': 853.0, 'intermediate_solutions': [{'tour': [7, 5, 8, 4, 6, 1, 0, 3, 2], 'cur_cost': 979.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 8, 4, 2, 3, 6, 1, 0], 'cur_cost': 815.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 6, 5, 8, 4, 0, 1, 3, 2], 'cur_cost': 917.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:43:13,729 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 853.00)
2025-08-04 16:43:13,729 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 16:43:13,729 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 16:43:13,730 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:13,730 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 16:43:13,730 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,730 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,731 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,731 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,731 - ExplorationExpert - INFO - 探索路径生成完成，成本: 887.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:43:13,731 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [4, 2, 0, 7, 6, 1, 3, 5, 8], 'cur_cost': 887.0, 'intermediate_solutions': [{'tour': [3, 4, 1, 0, 5, 7, 6, 2, 8], 'cur_cost': 918.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 4, 1, 6, 7, 5, 0, 8, 2], 'cur_cost': 1013.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 4, 0, 5, 7, 6, 8, 2, 1], 'cur_cost': 1029.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:43:13,732 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 887.00)
2025-08-04 16:43:13,732 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 16:43:13,732 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 16:43:13,733 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [4, 6, 2, 5, 1, 3, 7, 0, 8], 'cur_cost': 1218.0, 'intermediate_solutions': [{'tour': [4, 2, 8, 7, 3, 6, 5, 0, 1], 'cur_cost': 749.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 2, 8, 5, 3, 6, 0, 7, 1], 'cur_cost': 829.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 2, 3, 8, 5, 6, 7, 0, 1], 'cur_cost': 766.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [1, 2, 0, 7, 5, 8, 3, 6, 4], 'cur_cost': 953.0, 'intermediate_solutions': [{'tour': [8, 5, 7, 6, 3, 4, 2, 0, 1], 'cur_cost': 873.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 2, 4, 8, 6, 7, 0, 1], 'cur_cost': 898.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 5, 7, 6, 8, 4, 2, 0, 1], 'cur_cost': 839.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 4, 6, 1, 3, 2, 8, 5], 'cur_cost': 1087.0, 'intermediate_solutions': [{'tour': [5, 6, 7, 3, 8, 4, 1, 0, 2], 'cur_cost': 834.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 6, 7, 3, 8, 4, 1, 0, 2], 'cur_cost': 834.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 5, 6, 7, 3, 8, 2, 0, 1], 'cur_cost': 808.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([1, 6, 2, 4, 5, 0, 3, 8, 7], dtype=int64), 'cur_cost': 1046.0, 'intermediate_solutions': [{'tour': array([2, 4, 1, 6, 8, 0, 3, 7, 5]), 'cur_cost': 986.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 2, 4, 1, 8, 0, 3, 7, 5]), 'cur_cost': 989.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 6, 2, 4, 1, 0, 3, 7, 5]), 'cur_cost': 888.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 6, 2, 4, 8, 0, 3, 7, 5]), 'cur_cost': 1016.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 8, 6, 2, 4, 0, 3, 7, 5]), 'cur_cost': 1066.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [3, 4, 8, 6, 1, 7, 2, 5, 0], 'cur_cost': 1155.0, 'intermediate_solutions': [{'tour': [4, 2, 0, 7, 8, 3, 5, 6, 1], 'cur_cost': 788.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 2, 0, 7, 8, 3, 5, 6, 1], 'cur_cost': 788.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 2, 7, 3, 8, 5, 6, 1], 'cur_cost': 875.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [5, 3, 8, 4, 7, 6, 0, 1, 2], 'cur_cost': 861.0, 'intermediate_solutions': [{'tour': [7, 6, 4, 5, 3, 0, 8, 2, 1], 'cur_cost': 1058.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 4, 5, 8, 7, 3, 2, 1], 'cur_cost': 963.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 5, 3, 7, 4, 8, 2, 1], 'cur_cost': 759.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 3, 6, 1, 8, 2, 5, 7, 4], dtype=int64), 'cur_cost': 1042.0, 'intermediate_solutions': [{'tour': array([6, 5, 2, 3, 0, 4, 1, 8, 7]), 'cur_cost': 1051.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 6, 5, 2, 0, 4, 1, 8, 7]), 'cur_cost': 984.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 3, 6, 5, 2, 4, 1, 8, 7]), 'cur_cost': 975.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 3, 6, 5, 0, 4, 1, 8, 7]), 'cur_cost': 1031.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 0, 3, 6, 5, 4, 1, 8, 7]), 'cur_cost': 1062.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 6, 5, 8, 3, 4, 2, 1], 'cur_cost': 769.0, 'intermediate_solutions': [{'tour': [2, 7, 6, 0, 8, 3, 4, 5, 1], 'cur_cost': 1090.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 7, 6, 5, 3, 8, 0, 4, 1], 'cur_cost': 888.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 6, 0, 8, 3, 5, 4, 1, 2], 'cur_cost': 972.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 5, 6, 0, 3, 8, 4, 2], 'cur_cost': 853.0, 'intermediate_solutions': [{'tour': [7, 5, 8, 4, 6, 1, 0, 3, 2], 'cur_cost': 979.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 8, 4, 2, 3, 6, 1, 0], 'cur_cost': 815.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 6, 5, 8, 4, 0, 1, 3, 2], 'cur_cost': 917.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [4, 2, 0, 7, 6, 1, 3, 5, 8], 'cur_cost': 887.0, 'intermediate_solutions': [{'tour': [3, 4, 1, 0, 5, 7, 6, 2, 8], 'cur_cost': 918.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 4, 1, 6, 7, 5, 0, 8, 2], 'cur_cost': 1013.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 4, 0, 5, 7, 6, 8, 2, 1], 'cur_cost': 1029.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 16:43:13,735 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 16:43:13,735 - StatsExpert - INFO - 开始统计分析
2025-08-04 16:43:13,736 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=769.000, 多样性=0.894
2025-08-04 16:43:13,737 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-04 16:43:13,737 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-04 16:43:13,738 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 16:43:13,738 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.10566606865531536, 'best_improvement': -0.05198358413132695}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.02840909090909104}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-04 16:43:13,739 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-04 16:43:13,739 - __main__ - INFO - simple1_9 开始进化第 3 代
2025-08-04 16:43:13,740 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-04 16:43:13,740 - StatsExpert - INFO - 开始统计分析
2025-08-04 16:43:13,740 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=769.000, 多样性=0.894
2025-08-04 16:43:13,741 - PathExpert - INFO - 开始路径结构分析
2025-08-04 16:43:13,741 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.894
2025-08-04 16:43:13,741 - EliteExpert - INFO - 开始精英解分析
2025-08-04 16:43:13,742 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.630
2025-08-04 16:43:13,743 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-04 16:43:13,744 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 16:43:13,744 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-04 16:43:13,744 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-04 16:43:13,750 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.231, 适应度梯度: 22.185, 聚类评分: 0.000, 覆盖率: 0.003, 收敛趋势: 0.000, 多样性: 0.604
2025-08-04 16:43:13,751 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-04 16:43:13,751 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 16:43:13,751 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 16:43:13,756 - visualization.landscape_visualizer - INFO - 插值约束: 48 个点被约束到最小值 680.00
2025-08-04 16:43:13,760 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 16:43:13,841 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_3_20250804_164313.html
2025-08-04 16:43:13,880 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_3_20250804_164313.html
2025-08-04 16:43:13,880 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 3
2025-08-04 16:43:13,881 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-04 16:43:13,881 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1381秒
2025-08-04 16:43:13,881 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.23076923076923078, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 22.18461538461539, 'local_optima_density': 0.23076923076923078, 'gradient_variance': 34383.25207100592, 'cluster_count': 0}, 'population_state': {'diversity': 0.6035502958579881, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0033, 'fitness_entropy': 0.9383574700386479, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.003)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 22.185)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754296993.7514975, 'performance_metrics': {}}}
2025-08-04 16:43:13,882 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 16:43:13,882 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 16:43:13,882 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 16:43:13,883 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 16:43:13,883 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-04 16:43:13,883 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-04 16:43:13,883 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-04 16:43:13,884 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 16:43:13,884 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 16:43:13,884 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-04 16:43:13,884 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 16:43:13,885 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 16:43:13,885 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 7} (总数: 2, 保护比例: 0.20)
2025-08-04 16:43:13,885 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-04 16:43:13,885 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 16:43:13,886 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 16:43:13,886 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 942.0
2025-08-04 16:43:13,959 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 16:43:13,959 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0]
2025-08-04 16:43:13,960 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-04 16:43:13,960 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 16:43:13,961 - ExploitationExpert - INFO - populations: [{'tour': array([2, 7, 4, 8, 5, 6, 0, 1, 3], dtype=int64), 'cur_cost': 942.0}, {'tour': [1, 2, 0, 7, 5, 8, 3, 6, 4], 'cur_cost': 953.0}, {'tour': [0, 7, 4, 6, 1, 3, 2, 8, 5], 'cur_cost': 1087.0}, {'tour': [1, 6, 2, 4, 5, 0, 3, 8, 7], 'cur_cost': 1046.0}, {'tour': [3, 4, 8, 6, 1, 7, 2, 5, 0], 'cur_cost': 1155.0}, {'tour': [5, 3, 8, 4, 7, 6, 0, 1, 2], 'cur_cost': 861.0}, {'tour': [0, 3, 6, 1, 8, 2, 5, 7, 4], 'cur_cost': 1042.0}, {'tour': [0, 7, 6, 5, 8, 3, 4, 2, 1], 'cur_cost': 769.0}, {'tour': [1, 7, 5, 6, 0, 3, 8, 4, 2], 'cur_cost': 853.0}, {'tour': [4, 2, 0, 7, 6, 1, 3, 5, 8], 'cur_cost': 887.0}]
2025-08-04 16:43:13,961 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒，最大迭代次数: 10
2025-08-04 16:43:13,962 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-04 16:43:13,962 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([2, 7, 4, 8, 5, 6, 0, 1, 3], dtype=int64), 'cur_cost': 942.0, 'intermediate_solutions': [{'tour': array([2, 6, 4, 5, 1, 3, 7, 0, 8]), 'cur_cost': 1192.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 2, 6, 4, 1, 3, 7, 0, 8]), 'cur_cost': 1163.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 5, 2, 6, 4, 3, 7, 0, 8]), 'cur_cost': 1255.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 5, 2, 6, 1, 3, 7, 0, 8]), 'cur_cost': 1164.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 1, 5, 2, 6, 3, 7, 0, 8]), 'cur_cost': 1111.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 16:43:13,963 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 942.00)
2025-08-04 16:43:13,963 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 16:43:13,963 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 16:43:13,963 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:13,964 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 16:43:13,964 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,964 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,964 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,964 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:13,965 - ExplorationExpert - INFO - 探索路径生成完成，成本: 704.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:43:13,966 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [4, 0, 1, 6, 7, 3, 5, 8, 2], 'cur_cost': 704.0, 'intermediate_solutions': [{'tour': [1, 2, 6, 7, 5, 8, 3, 0, 4], 'cur_cost': 972.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 2, 0, 7, 5, 8, 3, 4, 6], 'cur_cost': 986.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 0, 7, 5, 8, 3, 1, 6, 4], 'cur_cost': 947.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:43:13,966 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 704.00)
2025-08-04 16:43:13,966 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-04 16:43:13,966 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 16:43:13,966 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 16:43:13,967 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 1069.0
2025-08-04 16:43:14,041 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 16:43:14,041 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0]
2025-08-04 16:43:14,042 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-04 16:43:14,043 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 16:43:14,043 - ExploitationExpert - INFO - populations: [{'tour': array([2, 7, 4, 8, 5, 6, 0, 1, 3], dtype=int64), 'cur_cost': 942.0}, {'tour': [4, 0, 1, 6, 7, 3, 5, 8, 2], 'cur_cost': 704.0}, {'tour': array([1, 6, 2, 5, 8, 4, 0, 3, 7], dtype=int64), 'cur_cost': 1069.0}, {'tour': [1, 6, 2, 4, 5, 0, 3, 8, 7], 'cur_cost': 1046.0}, {'tour': [3, 4, 8, 6, 1, 7, 2, 5, 0], 'cur_cost': 1155.0}, {'tour': [5, 3, 8, 4, 7, 6, 0, 1, 2], 'cur_cost': 861.0}, {'tour': [0, 3, 6, 1, 8, 2, 5, 7, 4], 'cur_cost': 1042.0}, {'tour': [0, 7, 6, 5, 8, 3, 4, 2, 1], 'cur_cost': 769.0}, {'tour': [1, 7, 5, 6, 0, 3, 8, 4, 2], 'cur_cost': 853.0}, {'tour': [4, 2, 0, 7, 6, 1, 3, 5, 8], 'cur_cost': 887.0}]
2025-08-04 16:43:14,044 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒，最大迭代次数: 10
2025-08-04 16:43:14,044 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-04 16:43:14,045 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([1, 6, 2, 5, 8, 4, 0, 3, 7], dtype=int64), 'cur_cost': 1069.0, 'intermediate_solutions': [{'tour': array([4, 7, 0, 6, 1, 3, 2, 8, 5]), 'cur_cost': 1049.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 4, 7, 0, 1, 3, 2, 8, 5]), 'cur_cost': 953.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 6, 4, 7, 0, 3, 2, 8, 5]), 'cur_cost': 1084.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 6, 4, 7, 1, 3, 2, 8, 5]), 'cur_cost': 1106.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 1, 6, 4, 7, 3, 2, 8, 5]), 'cur_cost': 941.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 16:43:14,046 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1069.00)
2025-08-04 16:43:14,046 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 16:43:14,046 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 16:43:14,047 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:14,047 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 16:43:14,047 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,048 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,048 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,048 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,048 - ExplorationExpert - INFO - 探索路径生成完成，成本: 883.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:43:14,048 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [1, 5, 6, 8, 4, 2, 7, 3, 0], 'cur_cost': 883.0, 'intermediate_solutions': [{'tour': [1, 6, 2, 4, 5, 3, 0, 8, 7], 'cur_cost': 1039.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 2, 4, 5, 0, 8, 3, 7], 'cur_cost': 1033.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 4, 5, 0, 3, 8, 2, 7], 'cur_cost': 1095.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:43:14,049 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 883.00)
2025-08-04 16:43:14,049 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-04 16:43:14,049 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 16:43:14,049 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 16:43:14,050 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 872.0
2025-08-04 16:43:14,116 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 16:43:14,116 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0]
2025-08-04 16:43:14,116 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-04 16:43:14,117 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 16:43:14,117 - ExploitationExpert - INFO - populations: [{'tour': array([2, 7, 4, 8, 5, 6, 0, 1, 3], dtype=int64), 'cur_cost': 942.0}, {'tour': [4, 0, 1, 6, 7, 3, 5, 8, 2], 'cur_cost': 704.0}, {'tour': array([1, 6, 2, 5, 8, 4, 0, 3, 7], dtype=int64), 'cur_cost': 1069.0}, {'tour': [1, 5, 6, 8, 4, 2, 7, 3, 0], 'cur_cost': 883.0}, {'tour': array([7, 6, 3, 2, 4, 8, 5, 1, 0], dtype=int64), 'cur_cost': 872.0}, {'tour': [5, 3, 8, 4, 7, 6, 0, 1, 2], 'cur_cost': 861.0}, {'tour': [0, 3, 6, 1, 8, 2, 5, 7, 4], 'cur_cost': 1042.0}, {'tour': [0, 7, 6, 5, 8, 3, 4, 2, 1], 'cur_cost': 769.0}, {'tour': [1, 7, 5, 6, 0, 3, 8, 4, 2], 'cur_cost': 853.0}, {'tour': [4, 2, 0, 7, 6, 1, 3, 5, 8], 'cur_cost': 887.0}]
2025-08-04 16:43:14,118 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 16:43:14,118 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-04 16:43:14,119 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([7, 6, 3, 2, 4, 8, 5, 1, 0], dtype=int64), 'cur_cost': 872.0, 'intermediate_solutions': [{'tour': array([8, 4, 3, 6, 1, 7, 2, 5, 0]), 'cur_cost': 1128.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 8, 4, 3, 1, 7, 2, 5, 0]), 'cur_cost': 1174.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 6, 8, 4, 3, 7, 2, 5, 0]), 'cur_cost': 1009.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 6, 8, 4, 1, 7, 2, 5, 0]), 'cur_cost': 1122.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 1, 6, 8, 4, 7, 2, 5, 0]), 'cur_cost': 1168.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 16:43:14,120 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 872.00)
2025-08-04 16:43:14,120 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 16:43:14,121 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 16:43:14,121 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:14,122 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 16:43:14,122 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,122 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,123 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,123 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,123 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1003.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:43:14,124 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 3, 6, 4, 1, 2, 8, 7, 5], 'cur_cost': 1003.0, 'intermediate_solutions': [{'tour': [6, 3, 8, 4, 7, 5, 0, 1, 2], 'cur_cost': 936.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 7, 4, 8, 3, 5, 1, 2], 'cur_cost': 938.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 5, 3, 8, 4, 7, 0, 1, 2], 'cur_cost': 853.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:43:14,124 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1003.00)
2025-08-04 16:43:14,124 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 16:43:14,124 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 16:43:14,125 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:14,125 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 16:43:14,125 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,125 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,126 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,126 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,126 - ExplorationExpert - INFO - 探索路径生成完成，成本: 829.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:43:14,126 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [5, 3, 7, 6, 8, 4, 2, 0, 1], 'cur_cost': 829.0, 'intermediate_solutions': [{'tour': [0, 3, 2, 1, 8, 6, 5, 7, 4], 'cur_cost': 1047.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 2, 8, 1, 6, 5, 7, 4], 'cur_cost': 988.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 6, 8, 1, 2, 5, 7, 4], 'cur_cost': 1101.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:43:14,127 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 829.00)
2025-08-04 16:43:14,127 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 16:43:14,127 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 16:43:14,127 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:14,128 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 16:43:14,128 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,128 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,128 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,128 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,128 - ExplorationExpert - INFO - 探索路径生成完成，成本: 983.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:43:14,129 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [1, 4, 7, 0, 3, 8, 5, 6, 2], 'cur_cost': 983.0, 'intermediate_solutions': [{'tour': [5, 7, 6, 0, 8, 3, 4, 2, 1], 'cur_cost': 944.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 6, 5, 8, 3, 4, 1, 2], 'cur_cost': 905.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 0, 7, 6, 5, 8, 4, 2, 1], 'cur_cost': 911.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:43:14,129 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 983.00)
2025-08-04 16:43:14,129 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 16:43:14,129 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 16:43:14,130 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:14,130 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 16:43:14,130 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,130 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,131 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,131 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,131 - ExplorationExpert - INFO - 探索路径生成完成，成本: 789.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:43:14,131 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 1, 2, 8, 5, 6, 3, 7, 4], 'cur_cost': 789.0, 'intermediate_solutions': [{'tour': [1, 7, 5, 2, 0, 3, 8, 4, 6], 'cur_cost': 1061.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 7, 5, 6, 0, 3, 8, 2, 4], 'cur_cost': 827.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 5, 6, 0, 1, 3, 8, 4, 2], 'cur_cost': 804.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:43:14,132 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 789.00)
2025-08-04 16:43:14,132 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 16:43:14,132 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 16:43:14,132 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:14,132 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 16:43:14,133 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,133 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,133 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,133 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,133 - ExplorationExpert - INFO - 探索路径生成完成，成本: 921.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:43:14,134 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [3, 6, 5, 0, 7, 8, 4, 2, 1], 'cur_cost': 921.0, 'intermediate_solutions': [{'tour': [4, 2, 0, 7, 6, 1, 8, 5, 3], 'cur_cost': 924.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 2, 0, 6, 7, 1, 3, 5, 8], 'cur_cost': 906.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 7, 6, 1, 3, 2, 5, 8], 'cur_cost': 1050.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:43:14,134 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 921.00)
2025-08-04 16:43:14,134 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 16:43:14,134 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 16:43:14,136 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 7, 4, 8, 5, 6, 0, 1, 3], dtype=int64), 'cur_cost': 942.0, 'intermediate_solutions': [{'tour': array([2, 6, 4, 5, 1, 3, 7, 0, 8]), 'cur_cost': 1192.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 2, 6, 4, 1, 3, 7, 0, 8]), 'cur_cost': 1163.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 5, 2, 6, 4, 3, 7, 0, 8]), 'cur_cost': 1255.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 5, 2, 6, 1, 3, 7, 0, 8]), 'cur_cost': 1164.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 1, 5, 2, 6, 3, 7, 0, 8]), 'cur_cost': 1111.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [4, 0, 1, 6, 7, 3, 5, 8, 2], 'cur_cost': 704.0, 'intermediate_solutions': [{'tour': [1, 2, 6, 7, 5, 8, 3, 0, 4], 'cur_cost': 972.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 2, 0, 7, 5, 8, 3, 4, 6], 'cur_cost': 986.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 0, 7, 5, 8, 3, 1, 6, 4], 'cur_cost': 947.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([1, 6, 2, 5, 8, 4, 0, 3, 7], dtype=int64), 'cur_cost': 1069.0, 'intermediate_solutions': [{'tour': array([4, 7, 0, 6, 1, 3, 2, 8, 5]), 'cur_cost': 1049.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 4, 7, 0, 1, 3, 2, 8, 5]), 'cur_cost': 953.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 6, 4, 7, 0, 3, 2, 8, 5]), 'cur_cost': 1084.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 6, 4, 7, 1, 3, 2, 8, 5]), 'cur_cost': 1106.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 1, 6, 4, 7, 3, 2, 8, 5]), 'cur_cost': 941.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [1, 5, 6, 8, 4, 2, 7, 3, 0], 'cur_cost': 883.0, 'intermediate_solutions': [{'tour': [1, 6, 2, 4, 5, 3, 0, 8, 7], 'cur_cost': 1039.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 2, 4, 5, 0, 8, 3, 7], 'cur_cost': 1033.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 4, 5, 0, 3, 8, 2, 7], 'cur_cost': 1095.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 6, 3, 2, 4, 8, 5, 1, 0], dtype=int64), 'cur_cost': 872.0, 'intermediate_solutions': [{'tour': array([8, 4, 3, 6, 1, 7, 2, 5, 0]), 'cur_cost': 1128.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 8, 4, 3, 1, 7, 2, 5, 0]), 'cur_cost': 1174.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 6, 8, 4, 3, 7, 2, 5, 0]), 'cur_cost': 1009.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 6, 8, 4, 1, 7, 2, 5, 0]), 'cur_cost': 1122.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 1, 6, 8, 4, 7, 2, 5, 0]), 'cur_cost': 1168.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 6, 4, 1, 2, 8, 7, 5], 'cur_cost': 1003.0, 'intermediate_solutions': [{'tour': [6, 3, 8, 4, 7, 5, 0, 1, 2], 'cur_cost': 936.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 7, 4, 8, 3, 5, 1, 2], 'cur_cost': 938.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 5, 3, 8, 4, 7, 0, 1, 2], 'cur_cost': 853.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [5, 3, 7, 6, 8, 4, 2, 0, 1], 'cur_cost': 829.0, 'intermediate_solutions': [{'tour': [0, 3, 2, 1, 8, 6, 5, 7, 4], 'cur_cost': 1047.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 2, 8, 1, 6, 5, 7, 4], 'cur_cost': 988.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 6, 8, 1, 2, 5, 7, 4], 'cur_cost': 1101.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 7, 0, 3, 8, 5, 6, 2], 'cur_cost': 983.0, 'intermediate_solutions': [{'tour': [5, 7, 6, 0, 8, 3, 4, 2, 1], 'cur_cost': 944.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 6, 5, 8, 3, 4, 1, 2], 'cur_cost': 905.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 0, 7, 6, 5, 8, 4, 2, 1], 'cur_cost': 911.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 8, 5, 6, 3, 7, 4], 'cur_cost': 789.0, 'intermediate_solutions': [{'tour': [1, 7, 5, 2, 0, 3, 8, 4, 6], 'cur_cost': 1061.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 7, 5, 6, 0, 3, 8, 2, 4], 'cur_cost': 827.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 5, 6, 0, 1, 3, 8, 4, 2], 'cur_cost': 804.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [3, 6, 5, 0, 7, 8, 4, 2, 1], 'cur_cost': 921.0, 'intermediate_solutions': [{'tour': [4, 2, 0, 7, 6, 1, 8, 5, 3], 'cur_cost': 924.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 2, 0, 6, 7, 1, 3, 5, 8], 'cur_cost': 906.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 7, 6, 1, 3, 2, 5, 8], 'cur_cost': 1050.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 16:43:14,139 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 16:43:14,139 - StatsExpert - INFO - 开始统计分析
2025-08-04 16:43:14,141 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=704.000, 多样性=0.911
2025-08-04 16:43:14,141 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-04 16:43:14,141 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-04 16:43:14,141 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 16:43:14,141 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.0985339523093313, 'best_improvement': 0.08452535760728218}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.0193370165745855}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 16:43:14,142 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-04 16:43:14,142 - __main__ - INFO - simple1_9 开始进化第 4 代
2025-08-04 16:43:14,142 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-04 16:43:14,143 - StatsExpert - INFO - 开始统计分析
2025-08-04 16:43:14,143 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=704.000, 多样性=0.911
2025-08-04 16:43:14,143 - PathExpert - INFO - 开始路径结构分析
2025-08-04 16:43:14,144 - PathExpert - INFO - 路径结构分析完成: 公共边数量=3, 路径相似性=0.911
2025-08-04 16:43:14,144 - EliteExpert - INFO - 开始精英解分析
2025-08-04 16:43:14,145 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.630
2025-08-04 16:43:14,146 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-04 16:43:14,146 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 16:43:14,147 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-04 16:43:14,147 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-04 16:43:14,155 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.231, 适应度梯度: 12.385, 聚类评分: 0.000, 覆盖率: 0.004, 收敛趋势: 0.000, 多样性: 0.616
2025-08-04 16:43:14,155 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-04 16:43:14,155 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 16:43:14,155 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 16:43:14,182 - visualization.landscape_visualizer - INFO - 插值约束: 161 个点被约束到最小值 680.00
2025-08-04 16:43:14,188 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 16:43:14,383 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_4_20250804_164314.html
2025-08-04 16:43:14,418 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_4_20250804_164314.html
2025-08-04 16:43:14,418 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 4
2025-08-04 16:43:14,419 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-04 16:43:14,419 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2730秒
2025-08-04 16:43:14,420 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.23076923076923078, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 12.384615384615378, 'local_optima_density': 0.23076923076923078, 'gradient_variance': 19631.43053254438, 'cluster_count': 0}, 'population_state': {'diversity': 0.616370808678501, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0043, 'fitness_entropy': 0.9246934699012206, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.004)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 12.385)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754296994.155912, 'performance_metrics': {}}}
2025-08-04 16:43:14,421 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 16:43:14,421 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 16:43:14,422 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 16:43:14,422 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 16:43:14,423 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-04 16:43:14,423 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-04 16:43:14,424 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-04 16:43:14,424 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 16:43:14,425 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 16:43:14,425 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-04 16:43:14,425 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 16:43:14,426 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 16:43:14,426 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 1} (总数: 2, 保护比例: 0.20)
2025-08-04 16:43:14,426 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 16:43:14,426 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 16:43:14,427 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:14,427 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 16:43:14,427 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,428 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,428 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,428 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,428 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1015.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:43:14,428 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [2, 5, 0, 4, 7, 3, 8, 6, 1], 'cur_cost': 1015.0, 'intermediate_solutions': [{'tour': [2, 7, 4, 8, 5, 6, 1, 0, 3], 'cur_cost': 923.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 7, 4, 8, 5, 6, 1, 0, 3], 'cur_cost': 923.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 7, 4, 8, 5, 6, 0, 1, 3], 'cur_cost': 942.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:43:14,429 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1015.00)
2025-08-04 16:43:14,429 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 16:43:14,429 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 16:43:14,429 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:14,430 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 16:43:14,430 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,430 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,430 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,431 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,431 - ExplorationExpert - INFO - 探索路径生成完成，成本: 947.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:43:14,431 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [2, 7, 3, 4, 6, 5, 8, 0, 1], 'cur_cost': 947.0, 'intermediate_solutions': [{'tour': [4, 0, 6, 1, 7, 3, 5, 8, 2], 'cur_cost': 802.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 5, 3, 7, 6, 1, 0, 4, 2], 'cur_cost': 704.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 6, 1, 7, 3, 5, 8, 2], 'cur_cost': 802.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:43:14,431 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 947.00)
2025-08-04 16:43:14,432 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-04 16:43:14,432 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 16:43:14,432 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 16:43:14,432 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 1003.0
2025-08-04 16:43:14,512 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 16:43:14,513 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0]
2025-08-04 16:43:14,513 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-04 16:43:14,514 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 16:43:14,514 - ExploitationExpert - INFO - populations: [{'tour': [2, 5, 0, 4, 7, 3, 8, 6, 1], 'cur_cost': 1015.0}, {'tour': [2, 7, 3, 4, 6, 5, 8, 0, 1], 'cur_cost': 947.0}, {'tour': array([6, 1, 5, 0, 3, 2, 4, 8, 7], dtype=int64), 'cur_cost': 1003.0}, {'tour': [1, 5, 6, 8, 4, 2, 7, 3, 0], 'cur_cost': 883.0}, {'tour': [7, 6, 3, 2, 4, 8, 5, 1, 0], 'cur_cost': 872.0}, {'tour': [0, 3, 6, 4, 1, 2, 8, 7, 5], 'cur_cost': 1003.0}, {'tour': [5, 3, 7, 6, 8, 4, 2, 0, 1], 'cur_cost': 829.0}, {'tour': [1, 4, 7, 0, 3, 8, 5, 6, 2], 'cur_cost': 983.0}, {'tour': [0, 1, 2, 8, 5, 6, 3, 7, 4], 'cur_cost': 789.0}, {'tour': [3, 6, 5, 0, 7, 8, 4, 2, 1], 'cur_cost': 921.0}]
2025-08-04 16:43:14,515 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒，最大迭代次数: 10
2025-08-04 16:43:14,515 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-08-04 16:43:14,515 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([6, 1, 5, 0, 3, 2, 4, 8, 7], dtype=int64), 'cur_cost': 1003.0, 'intermediate_solutions': [{'tour': array([2, 6, 1, 5, 8, 4, 0, 3, 7]), 'cur_cost': 1067.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 2, 6, 1, 8, 4, 0, 3, 7]), 'cur_cost': 1092.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 5, 2, 6, 1, 4, 0, 3, 7]), 'cur_cost': 1042.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 5, 2, 6, 8, 4, 0, 3, 7]), 'cur_cost': 1157.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 8, 5, 2, 6, 4, 0, 3, 7]), 'cur_cost': 1180.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 16:43:14,516 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1003.00)
2025-08-04 16:43:14,516 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 16:43:14,516 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 16:43:14,517 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:14,517 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 16:43:14,517 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,518 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,518 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,518 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,518 - ExplorationExpert - INFO - 探索路径生成完成，成本: 796.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:43:14,519 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [1, 7, 8, 3, 5, 6, 0, 4, 2], 'cur_cost': 796.0, 'intermediate_solutions': [{'tour': [1, 5, 4, 8, 6, 2, 7, 3, 0], 'cur_cost': 1089.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 5, 6, 8, 4, 3, 7, 2, 0], 'cur_cost': 952.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 5, 6, 4, 8, 2, 7, 3, 0], 'cur_cost': 937.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:43:14,519 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 796.00)
2025-08-04 16:43:14,519 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 16:43:14,520 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 16:43:14,520 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:14,521 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 16:43:14,521 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,522 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,522 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,522 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,522 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1158.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:43:14,523 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [5, 0, 3, 4, 8, 1, 7, 2, 6], 'cur_cost': 1158.0, 'intermediate_solutions': [{'tour': [7, 6, 1, 2, 4, 8, 5, 3, 0], 'cur_cost': 857.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 6, 3, 2, 4, 8, 5, 0, 1], 'cur_cost': 875.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 4, 6, 3, 2, 8, 5, 1, 0], 'cur_cost': 984.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:43:14,523 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1158.00)
2025-08-04 16:43:14,523 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-04 16:43:14,524 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 16:43:14,524 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 16:43:14,524 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 1157.0
2025-08-04 16:43:14,608 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 16:43:14,608 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0]
2025-08-04 16:43:14,608 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-04 16:43:14,609 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 16:43:14,610 - ExploitationExpert - INFO - populations: [{'tour': [2, 5, 0, 4, 7, 3, 8, 6, 1], 'cur_cost': 1015.0}, {'tour': [2, 7, 3, 4, 6, 5, 8, 0, 1], 'cur_cost': 947.0}, {'tour': array([6, 1, 5, 0, 3, 2, 4, 8, 7], dtype=int64), 'cur_cost': 1003.0}, {'tour': [1, 7, 8, 3, 5, 6, 0, 4, 2], 'cur_cost': 796.0}, {'tour': [5, 0, 3, 4, 8, 1, 7, 2, 6], 'cur_cost': 1158.0}, {'tour': array([4, 3, 8, 0, 7, 2, 5, 1, 6], dtype=int64), 'cur_cost': 1157.0}, {'tour': [5, 3, 7, 6, 8, 4, 2, 0, 1], 'cur_cost': 829.0}, {'tour': [1, 4, 7, 0, 3, 8, 5, 6, 2], 'cur_cost': 983.0}, {'tour': [0, 1, 2, 8, 5, 6, 3, 7, 4], 'cur_cost': 789.0}, {'tour': [3, 6, 5, 0, 7, 8, 4, 2, 1], 'cur_cost': 921.0}]
2025-08-04 16:43:14,611 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒，最大迭代次数: 10
2025-08-04 16:43:14,611 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-08-04 16:43:14,611 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([4, 3, 8, 0, 7, 2, 5, 1, 6], dtype=int64), 'cur_cost': 1157.0, 'intermediate_solutions': [{'tour': array([6, 3, 0, 4, 1, 2, 8, 7, 5]), 'cur_cost': 881.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 6, 3, 0, 1, 2, 8, 7, 5]), 'cur_cost': 953.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 4, 6, 3, 0, 2, 8, 7, 5]), 'cur_cost': 1030.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 4, 6, 3, 1, 2, 8, 7, 5]), 'cur_cost': 1022.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 1, 4, 6, 3, 2, 8, 7, 5]), 'cur_cost': 933.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 16:43:14,612 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1157.00)
2025-08-04 16:43:14,613 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 16:43:14,613 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 16:43:14,613 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:14,614 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 16:43:14,614 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,614 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,615 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,615 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,616 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1010.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:43:14,616 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [2, 8, 0, 6, 4, 5, 3, 7, 1], 'cur_cost': 1010.0, 'intermediate_solutions': [{'tour': [5, 3, 8, 6, 7, 4, 2, 0, 1], 'cur_cost': 846.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 3, 7, 6, 8, 1, 0, 2, 4], 'cur_cost': 886.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 3, 7, 6, 8, 4, 2, 0, 1], 'cur_cost': 829.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:43:14,616 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1010.00)
2025-08-04 16:43:14,617 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-04 16:43:14,617 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 16:43:14,617 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 16:43:14,617 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 1085.0
2025-08-04 16:43:14,718 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 16:43:14,718 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0]
2025-08-04 16:43:14,719 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-04 16:43:14,720 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 16:43:14,720 - ExploitationExpert - INFO - populations: [{'tour': [2, 5, 0, 4, 7, 3, 8, 6, 1], 'cur_cost': 1015.0}, {'tour': [2, 7, 3, 4, 6, 5, 8, 0, 1], 'cur_cost': 947.0}, {'tour': array([6, 1, 5, 0, 3, 2, 4, 8, 7], dtype=int64), 'cur_cost': 1003.0}, {'tour': [1, 7, 8, 3, 5, 6, 0, 4, 2], 'cur_cost': 796.0}, {'tour': [5, 0, 3, 4, 8, 1, 7, 2, 6], 'cur_cost': 1158.0}, {'tour': array([4, 3, 8, 0, 7, 2, 5, 1, 6], dtype=int64), 'cur_cost': 1157.0}, {'tour': [2, 8, 0, 6, 4, 5, 3, 7, 1], 'cur_cost': 1010.0}, {'tour': array([1, 5, 8, 0, 3, 6, 4, 2, 7], dtype=int64), 'cur_cost': 1085.0}, {'tour': [0, 1, 2, 8, 5, 6, 3, 7, 4], 'cur_cost': 789.0}, {'tour': [3, 6, 5, 0, 7, 8, 4, 2, 1], 'cur_cost': 921.0}]
2025-08-04 16:43:14,722 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒，最大迭代次数: 10
2025-08-04 16:43:14,722 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-08-04 16:43:14,723 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([1, 5, 8, 0, 3, 6, 4, 2, 7], dtype=int64), 'cur_cost': 1085.0, 'intermediate_solutions': [{'tour': array([7, 4, 1, 0, 3, 8, 5, 6, 2]), 'cur_cost': 934.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 7, 4, 1, 3, 8, 5, 6, 2]), 'cur_cost': 1013.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 0, 7, 4, 1, 8, 5, 6, 2]), 'cur_cost': 1092.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 0, 7, 4, 3, 8, 5, 6, 2]), 'cur_cost': 916.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 3, 0, 7, 4, 8, 5, 6, 2]), 'cur_cost': 1058.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 16:43:14,723 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1085.00)
2025-08-04 16:43:14,724 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 16:43:14,724 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 16:43:14,724 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:14,725 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 16:43:14,725 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,725 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,725 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,725 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,726 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1055.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:43:14,726 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [6, 8, 0, 2, 5, 7, 3, 4, 1], 'cur_cost': 1055.0, 'intermediate_solutions': [{'tour': [0, 7, 2, 8, 5, 6, 3, 1, 4], 'cur_cost': 940.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 7, 3, 6, 5, 8, 2, 1], 'cur_cost': 789.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 2, 8, 4, 5, 6, 3, 7], 'cur_cost': 832.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:43:14,726 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 1055.00)
2025-08-04 16:43:14,727 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 16:43:14,727 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 16:43:14,727 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:14,727 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 16:43:14,728 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,728 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,728 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,728 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,729 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1124.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:43:14,729 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [3, 6, 8, 7, 0, 2, 5, 4, 1], 'cur_cost': 1124.0, 'intermediate_solutions': [{'tour': [3, 6, 2, 0, 7, 8, 4, 5, 1], 'cur_cost': 1154.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 6, 0, 7, 8, 4, 2, 1], 'cur_cost': 852.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 6, 5, 0, 7, 8, 2, 4, 1], 'cur_cost': 895.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:43:14,729 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1124.00)
2025-08-04 16:43:14,729 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 16:43:14,730 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 16:43:14,731 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [2, 5, 0, 4, 7, 3, 8, 6, 1], 'cur_cost': 1015.0, 'intermediate_solutions': [{'tour': [2, 7, 4, 8, 5, 6, 1, 0, 3], 'cur_cost': 923.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 7, 4, 8, 5, 6, 1, 0, 3], 'cur_cost': 923.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 7, 4, 8, 5, 6, 0, 1, 3], 'cur_cost': 942.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [2, 7, 3, 4, 6, 5, 8, 0, 1], 'cur_cost': 947.0, 'intermediate_solutions': [{'tour': [4, 0, 6, 1, 7, 3, 5, 8, 2], 'cur_cost': 802.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 5, 3, 7, 6, 1, 0, 4, 2], 'cur_cost': 704.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 6, 1, 7, 3, 5, 8, 2], 'cur_cost': 802.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 1, 5, 0, 3, 2, 4, 8, 7], dtype=int64), 'cur_cost': 1003.0, 'intermediate_solutions': [{'tour': array([2, 6, 1, 5, 8, 4, 0, 3, 7]), 'cur_cost': 1067.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 2, 6, 1, 8, 4, 0, 3, 7]), 'cur_cost': 1092.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 5, 2, 6, 1, 4, 0, 3, 7]), 'cur_cost': 1042.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 5, 2, 6, 8, 4, 0, 3, 7]), 'cur_cost': 1157.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 8, 5, 2, 6, 4, 0, 3, 7]), 'cur_cost': 1180.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 8, 3, 5, 6, 0, 4, 2], 'cur_cost': 796.0, 'intermediate_solutions': [{'tour': [1, 5, 4, 8, 6, 2, 7, 3, 0], 'cur_cost': 1089.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 5, 6, 8, 4, 3, 7, 2, 0], 'cur_cost': 952.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 5, 6, 4, 8, 2, 7, 3, 0], 'cur_cost': 937.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [5, 0, 3, 4, 8, 1, 7, 2, 6], 'cur_cost': 1158.0, 'intermediate_solutions': [{'tour': [7, 6, 1, 2, 4, 8, 5, 3, 0], 'cur_cost': 857.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 6, 3, 2, 4, 8, 5, 0, 1], 'cur_cost': 875.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 4, 6, 3, 2, 8, 5, 1, 0], 'cur_cost': 984.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 3, 8, 0, 7, 2, 5, 1, 6], dtype=int64), 'cur_cost': 1157.0, 'intermediate_solutions': [{'tour': array([6, 3, 0, 4, 1, 2, 8, 7, 5]), 'cur_cost': 881.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 6, 3, 0, 1, 2, 8, 7, 5]), 'cur_cost': 953.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 4, 6, 3, 0, 2, 8, 7, 5]), 'cur_cost': 1030.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 4, 6, 3, 1, 2, 8, 7, 5]), 'cur_cost': 1022.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 1, 4, 6, 3, 2, 8, 7, 5]), 'cur_cost': 933.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [2, 8, 0, 6, 4, 5, 3, 7, 1], 'cur_cost': 1010.0, 'intermediate_solutions': [{'tour': [5, 3, 8, 6, 7, 4, 2, 0, 1], 'cur_cost': 846.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 3, 7, 6, 8, 1, 0, 2, 4], 'cur_cost': 886.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 3, 7, 6, 8, 4, 2, 0, 1], 'cur_cost': 829.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([1, 5, 8, 0, 3, 6, 4, 2, 7], dtype=int64), 'cur_cost': 1085.0, 'intermediate_solutions': [{'tour': array([7, 4, 1, 0, 3, 8, 5, 6, 2]), 'cur_cost': 934.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 7, 4, 1, 3, 8, 5, 6, 2]), 'cur_cost': 1013.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 0, 7, 4, 1, 8, 5, 6, 2]), 'cur_cost': 1092.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 0, 7, 4, 3, 8, 5, 6, 2]), 'cur_cost': 916.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 3, 0, 7, 4, 8, 5, 6, 2]), 'cur_cost': 1058.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [6, 8, 0, 2, 5, 7, 3, 4, 1], 'cur_cost': 1055.0, 'intermediate_solutions': [{'tour': [0, 7, 2, 8, 5, 6, 3, 1, 4], 'cur_cost': 940.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 7, 3, 6, 5, 8, 2, 1], 'cur_cost': 789.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 2, 8, 4, 5, 6, 3, 7], 'cur_cost': 832.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [3, 6, 8, 7, 0, 2, 5, 4, 1], 'cur_cost': 1124.0, 'intermediate_solutions': [{'tour': [3, 6, 2, 0, 7, 8, 4, 5, 1], 'cur_cost': 1154.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 6, 0, 7, 8, 4, 2, 1], 'cur_cost': 852.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 6, 5, 0, 7, 8, 2, 4, 1], 'cur_cost': 895.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 16:43:14,733 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 16:43:14,734 - StatsExpert - INFO - 开始统计分析
2025-08-04 16:43:14,735 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=796.000, 多样性=0.872
2025-08-04 16:43:14,735 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-04 16:43:14,735 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-04 16:43:14,736 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 16:43:14,736 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.12152548035500756, 'best_improvement': -0.13068181818181818}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.043360433604335946}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.029528180024377793, 'recent_improvements': [0.039477592260575706, -0.10566606865531536, 0.0985339523093313], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 16:43:14,738 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-04 16:43:14,738 - __main__ - INFO - simple1_9 开始进化第 5 代
2025-08-04 16:43:14,738 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-04 16:43:14,739 - StatsExpert - INFO - 开始统计分析
2025-08-04 16:43:14,739 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=796.000, 多样性=0.872
2025-08-04 16:43:14,740 - PathExpert - INFO - 开始路径结构分析
2025-08-04 16:43:14,740 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.872
2025-08-04 16:43:14,740 - EliteExpert - INFO - 开始精英解分析
2025-08-04 16:43:14,741 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.630
2025-08-04 16:43:14,742 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-04 16:43:14,743 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 16:43:14,743 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-04 16:43:14,743 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-04 16:43:14,750 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.308, 适应度梯度: 7.815, 聚类评分: 0.000, 覆盖率: 0.005, 收敛趋势: 0.000, 多样性: 0.607
2025-08-04 16:43:14,750 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-04 16:43:14,750 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 16:43:14,751 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 16:43:14,755 - visualization.landscape_visualizer - INFO - 插值约束: 102 个点被约束到最小值 680.00
2025-08-04 16:43:14,760 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 16:43:14,843 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_5_20250804_164314.html
2025-08-04 16:43:14,884 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_5_20250804_164314.html
2025-08-04 16:43:14,885 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 5
2025-08-04 16:43:14,885 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-04 16:43:14,885 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1429秒
2025-08-04 16:43:14,886 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3076923076923077, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 7.815384615384606, 'local_optima_density': 0.3076923076923077, 'gradient_variance': 27311.756686390534, 'cluster_count': 0}, 'population_state': {'diversity': 0.606508875739645, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0053, 'fitness_entropy': 0.9479479190038742, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.005)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 7.815)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754296994.750687, 'performance_metrics': {}}}
2025-08-04 16:43:14,887 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 16:43:14,888 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 16:43:14,888 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 16:43:14,888 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 16:43:14,889 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-04 16:43:14,889 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-04 16:43:14,889 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-04 16:43:14,890 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 16:43:14,890 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 16:43:14,891 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-04 16:43:14,891 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 16:43:14,892 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 16:43:14,892 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 3} (总数: 2, 保护比例: 0.20)
2025-08-04 16:43:14,892 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 16:43:14,892 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 16:43:14,892 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:14,893 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 16:43:14,893 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,893 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,893 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,894 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,894 - ExplorationExpert - INFO - 探索路径生成完成，成本: 829.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:43:14,894 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [1, 0, 4, 8, 3, 7, 5, 6, 2], 'cur_cost': 829.0, 'intermediate_solutions': [{'tour': [5, 2, 0, 4, 7, 3, 8, 6, 1], 'cur_cost': 1042.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 8, 3, 7, 4, 0, 5, 2], 'cur_cost': 1015.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 5, 0, 1, 4, 7, 3, 8, 6], 'cur_cost': 1012.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:43:14,894 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 829.00)
2025-08-04 16:43:14,894 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 16:43:14,895 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 16:43:14,895 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:14,895 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 16:43:14,896 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,896 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,896 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,896 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,896 - ExplorationExpert - INFO - 探索路径生成完成，成本: 923.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:43:14,897 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [2, 0, 7, 5, 3, 8, 4, 6, 1], 'cur_cost': 923.0, 'intermediate_solutions': [{'tour': [2, 7, 3, 4, 6, 5, 0, 8, 1], 'cur_cost': 1109.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 7, 3, 4, 6, 1, 0, 8, 5], 'cur_cost': 1031.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 7, 3, 4, 1, 6, 5, 8, 0], 'cur_cost': 970.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:43:14,897 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 923.00)
2025-08-04 16:43:14,897 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 16:43:14,897 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 16:43:14,897 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:14,898 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 16:43:14,898 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,898 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,898 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,899 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,899 - ExplorationExpert - INFO - 探索路径生成完成，成本: 694.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:43:14,899 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [3, 5, 7, 6, 0, 1, 4, 2, 8], 'cur_cost': 694.0, 'intermediate_solutions': [{'tour': [6, 1, 5, 0, 7, 2, 4, 8, 3], 'cur_cost': 955.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 1, 8, 4, 2, 3, 0, 5, 7], 'cur_cost': 998.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 1, 5, 0, 3, 2, 4, 8, 7], 'cur_cost': 1003.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:43:14,899 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 694.00)
2025-08-04 16:43:14,900 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 16:43:14,900 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 16:43:14,900 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:14,900 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 16:43:14,900 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,901 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,901 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,901 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:14,901 - ExplorationExpert - INFO - 探索路径生成完成，成本: 972.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:43:14,901 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [1, 8, 3, 4, 7, 5, 6, 0, 2], 'cur_cost': 972.0, 'intermediate_solutions': [{'tour': [1, 7, 8, 3, 0, 6, 5, 4, 2], 'cur_cost': 915.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 0, 6, 5, 3, 8, 7, 2], 'cur_cost': 853.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 8, 3, 5, 6, 0, 4, 2], 'cur_cost': 796.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:43:14,902 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 972.00)
2025-08-04 16:43:14,902 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-04 16:43:14,902 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 16:43:14,902 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 16:43:14,903 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 1108.0
2025-08-04 16:43:14,980 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 16:43:14,980 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0]
2025-08-04 16:43:14,980 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-04 16:43:14,981 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 16:43:14,981 - ExploitationExpert - INFO - populations: [{'tour': [1, 0, 4, 8, 3, 7, 5, 6, 2], 'cur_cost': 829.0}, {'tour': [2, 0, 7, 5, 3, 8, 4, 6, 1], 'cur_cost': 923.0}, {'tour': [3, 5, 7, 6, 0, 1, 4, 2, 8], 'cur_cost': 694.0}, {'tour': [1, 8, 3, 4, 7, 5, 6, 0, 2], 'cur_cost': 972.0}, {'tour': array([7, 2, 6, 5, 3, 1, 8, 0, 4], dtype=int64), 'cur_cost': 1108.0}, {'tour': [4, 3, 8, 0, 7, 2, 5, 1, 6], 'cur_cost': 1157.0}, {'tour': [2, 8, 0, 6, 4, 5, 3, 7, 1], 'cur_cost': 1010.0}, {'tour': [1, 5, 8, 0, 3, 6, 4, 2, 7], 'cur_cost': 1085.0}, {'tour': [6, 8, 0, 2, 5, 7, 3, 4, 1], 'cur_cost': 1055.0}, {'tour': [3, 6, 8, 7, 0, 2, 5, 4, 1], 'cur_cost': 1124.0}]
2025-08-04 16:43:14,982 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒，最大迭代次数: 10
2025-08-04 16:43:14,982 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-08-04 16:43:14,983 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([7, 2, 6, 5, 3, 1, 8, 0, 4], dtype=int64), 'cur_cost': 1108.0, 'intermediate_solutions': [{'tour': array([3, 0, 5, 4, 8, 1, 7, 2, 6]), 'cur_cost': 1209.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 3, 0, 5, 8, 1, 7, 2, 6]), 'cur_cost': 1241.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 4, 3, 0, 5, 1, 7, 2, 6]), 'cur_cost': 1218.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 4, 3, 0, 8, 1, 7, 2, 6]), 'cur_cost': 1214.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 8, 4, 3, 0, 1, 7, 2, 6]), 'cur_cost': 996.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 16:43:14,983 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1108.00)
2025-08-04 16:43:14,984 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-04 16:43:14,984 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 16:43:14,984 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 16:43:14,984 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 1085.0
2025-08-04 16:43:15,058 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 16:43:15,058 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0]
2025-08-04 16:43:15,059 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-04 16:43:15,060 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 16:43:15,060 - ExploitationExpert - INFO - populations: [{'tour': [1, 0, 4, 8, 3, 7, 5, 6, 2], 'cur_cost': 829.0}, {'tour': [2, 0, 7, 5, 3, 8, 4, 6, 1], 'cur_cost': 923.0}, {'tour': [3, 5, 7, 6, 0, 1, 4, 2, 8], 'cur_cost': 694.0}, {'tour': [1, 8, 3, 4, 7, 5, 6, 0, 2], 'cur_cost': 972.0}, {'tour': array([7, 2, 6, 5, 3, 1, 8, 0, 4], dtype=int64), 'cur_cost': 1108.0}, {'tour': array([8, 4, 5, 0, 2, 3, 7, 1, 6], dtype=int64), 'cur_cost': 1085.0}, {'tour': [2, 8, 0, 6, 4, 5, 3, 7, 1], 'cur_cost': 1010.0}, {'tour': [1, 5, 8, 0, 3, 6, 4, 2, 7], 'cur_cost': 1085.0}, {'tour': [6, 8, 0, 2, 5, 7, 3, 4, 1], 'cur_cost': 1055.0}, {'tour': [3, 6, 8, 7, 0, 2, 5, 4, 1], 'cur_cost': 1124.0}]
2025-08-04 16:43:15,061 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒，最大迭代次数: 10
2025-08-04 16:43:15,061 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-08-04 16:43:15,061 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([8, 4, 5, 0, 2, 3, 7, 1, 6], dtype=int64), 'cur_cost': 1085.0, 'intermediate_solutions': [{'tour': array([8, 3, 4, 0, 7, 2, 5, 1, 6]), 'cur_cost': 1096.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 8, 3, 4, 7, 2, 5, 1, 6]), 'cur_cost': 1115.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 0, 8, 3, 4, 2, 5, 1, 6]), 'cur_cost': 1014.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 0, 8, 3, 7, 2, 5, 1, 6]), 'cur_cost': 1097.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 7, 0, 8, 3, 2, 5, 1, 6]), 'cur_cost': 1149.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 16:43:15,062 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1085.00)
2025-08-04 16:43:15,062 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 16:43:15,062 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 16:43:15,062 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:15,063 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 16:43:15,063 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:15,063 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:15,063 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:15,064 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:15,064 - ExplorationExpert - INFO - 探索路径生成完成，成本: 799.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:43:15,064 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [6, 5, 8, 3, 4, 2, 7, 0, 1], 'cur_cost': 799.0, 'intermediate_solutions': [{'tour': [4, 8, 0, 6, 2, 5, 3, 7, 1], 'cur_cost': 1045.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 0, 1, 7, 3, 5, 4, 6], 'cur_cost': 1007.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 8, 0, 6, 4, 5, 3, 1, 7], 'cur_cost': 1107.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:43:15,064 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 799.00)
2025-08-04 16:43:15,065 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 16:43:15,065 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 16:43:15,065 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:15,065 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 16:43:15,065 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:15,066 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:15,066 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:15,066 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:15,066 - ExplorationExpert - INFO - 探索路径生成完成，成本: 847.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:43:15,066 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 3, 5, 7, 8, 4, 2, 1, 6], 'cur_cost': 847.0, 'intermediate_solutions': [{'tour': [1, 4, 8, 0, 3, 6, 5, 2, 7], 'cur_cost': 1061.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 5, 8, 4, 6, 3, 0, 2, 7], 'cur_cost': 1115.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 5, 8, 2, 0, 3, 6, 4, 7], 'cur_cost': 1084.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:43:15,067 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 847.00)
2025-08-04 16:43:15,067 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 16:43:15,067 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 16:43:15,067 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:43:15,068 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 16:43:15,068 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:15,068 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:15,068 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:15,069 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:43:15,069 - ExplorationExpert - INFO - 探索路径生成完成，成本: 704.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:43:15,069 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [2, 4, 0, 1, 6, 7, 3, 5, 8], 'cur_cost': 704.0, 'intermediate_solutions': [{'tour': [3, 8, 0, 2, 5, 7, 6, 4, 1], 'cur_cost': 1088.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 7, 5, 2, 0, 8, 6, 4, 1], 'cur_cost': 1129.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 8, 0, 5, 7, 3, 2, 4, 1], 'cur_cost': 966.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:43:15,069 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 704.00)
2025-08-04 16:43:15,070 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-04 16:43:15,070 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 16:43:15,070 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 16:43:15,071 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 1076.0
2025-08-04 16:43:15,144 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 16:43:15,144 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0]
2025-08-04 16:43:15,144 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-04 16:43:15,145 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 16:43:15,145 - ExploitationExpert - INFO - populations: [{'tour': [1, 0, 4, 8, 3, 7, 5, 6, 2], 'cur_cost': 829.0}, {'tour': [2, 0, 7, 5, 3, 8, 4, 6, 1], 'cur_cost': 923.0}, {'tour': [3, 5, 7, 6, 0, 1, 4, 2, 8], 'cur_cost': 694.0}, {'tour': [1, 8, 3, 4, 7, 5, 6, 0, 2], 'cur_cost': 972.0}, {'tour': array([7, 2, 6, 5, 3, 1, 8, 0, 4], dtype=int64), 'cur_cost': 1108.0}, {'tour': array([8, 4, 5, 0, 2, 3, 7, 1, 6], dtype=int64), 'cur_cost': 1085.0}, {'tour': [6, 5, 8, 3, 4, 2, 7, 0, 1], 'cur_cost': 799.0}, {'tour': [0, 3, 5, 7, 8, 4, 2, 1, 6], 'cur_cost': 847.0}, {'tour': [2, 4, 0, 1, 6, 7, 3, 5, 8], 'cur_cost': 704.0}, {'tour': array([6, 4, 7, 8, 1, 0, 5, 3, 2], dtype=int64), 'cur_cost': 1076.0}]
2025-08-04 16:43:15,146 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒，最大迭代次数: 10
2025-08-04 16:43:15,146 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-08-04 16:43:15,147 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([6, 4, 7, 8, 1, 0, 5, 3, 2], dtype=int64), 'cur_cost': 1076.0, 'intermediate_solutions': [{'tour': array([8, 6, 3, 7, 0, 2, 5, 4, 1]), 'cur_cost': 1109.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 8, 6, 3, 0, 2, 5, 4, 1]), 'cur_cost': 1124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 7, 8, 6, 3, 2, 5, 4, 1]), 'cur_cost': 1024.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 7, 8, 6, 0, 2, 5, 4, 1]), 'cur_cost': 1097.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 0, 7, 8, 6, 2, 5, 4, 1]), 'cur_cost': 1210.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 16:43:15,148 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1076.00)
2025-08-04 16:43:15,148 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 16:43:15,148 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 16:43:15,149 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [1, 0, 4, 8, 3, 7, 5, 6, 2], 'cur_cost': 829.0, 'intermediate_solutions': [{'tour': [5, 2, 0, 4, 7, 3, 8, 6, 1], 'cur_cost': 1042.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 8, 3, 7, 4, 0, 5, 2], 'cur_cost': 1015.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 5, 0, 1, 4, 7, 3, 8, 6], 'cur_cost': 1012.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [2, 0, 7, 5, 3, 8, 4, 6, 1], 'cur_cost': 923.0, 'intermediate_solutions': [{'tour': [2, 7, 3, 4, 6, 5, 0, 8, 1], 'cur_cost': 1109.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 7, 3, 4, 6, 1, 0, 8, 5], 'cur_cost': 1031.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 7, 3, 4, 1, 6, 5, 8, 0], 'cur_cost': 970.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [3, 5, 7, 6, 0, 1, 4, 2, 8], 'cur_cost': 694.0, 'intermediate_solutions': [{'tour': [6, 1, 5, 0, 7, 2, 4, 8, 3], 'cur_cost': 955.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 1, 8, 4, 2, 3, 0, 5, 7], 'cur_cost': 998.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 1, 5, 0, 3, 2, 4, 8, 7], 'cur_cost': 1003.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [1, 8, 3, 4, 7, 5, 6, 0, 2], 'cur_cost': 972.0, 'intermediate_solutions': [{'tour': [1, 7, 8, 3, 0, 6, 5, 4, 2], 'cur_cost': 915.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 0, 6, 5, 3, 8, 7, 2], 'cur_cost': 853.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 8, 3, 5, 6, 0, 4, 2], 'cur_cost': 796.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 2, 6, 5, 3, 1, 8, 0, 4], dtype=int64), 'cur_cost': 1108.0, 'intermediate_solutions': [{'tour': array([3, 0, 5, 4, 8, 1, 7, 2, 6]), 'cur_cost': 1209.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 3, 0, 5, 8, 1, 7, 2, 6]), 'cur_cost': 1241.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 4, 3, 0, 5, 1, 7, 2, 6]), 'cur_cost': 1218.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 4, 3, 0, 8, 1, 7, 2, 6]), 'cur_cost': 1214.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 8, 4, 3, 0, 1, 7, 2, 6]), 'cur_cost': 996.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 4, 5, 0, 2, 3, 7, 1, 6], dtype=int64), 'cur_cost': 1085.0, 'intermediate_solutions': [{'tour': array([8, 3, 4, 0, 7, 2, 5, 1, 6]), 'cur_cost': 1096.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 8, 3, 4, 7, 2, 5, 1, 6]), 'cur_cost': 1115.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 0, 8, 3, 4, 2, 5, 1, 6]), 'cur_cost': 1014.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 0, 8, 3, 7, 2, 5, 1, 6]), 'cur_cost': 1097.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 7, 0, 8, 3, 2, 5, 1, 6]), 'cur_cost': 1149.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [6, 5, 8, 3, 4, 2, 7, 0, 1], 'cur_cost': 799.0, 'intermediate_solutions': [{'tour': [4, 8, 0, 6, 2, 5, 3, 7, 1], 'cur_cost': 1045.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 0, 1, 7, 3, 5, 4, 6], 'cur_cost': 1007.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 8, 0, 6, 4, 5, 3, 1, 7], 'cur_cost': 1107.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 5, 7, 8, 4, 2, 1, 6], 'cur_cost': 847.0, 'intermediate_solutions': [{'tour': [1, 4, 8, 0, 3, 6, 5, 2, 7], 'cur_cost': 1061.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 5, 8, 4, 6, 3, 0, 2, 7], 'cur_cost': 1115.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 5, 8, 2, 0, 3, 6, 4, 7], 'cur_cost': 1084.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [2, 4, 0, 1, 6, 7, 3, 5, 8], 'cur_cost': 704.0, 'intermediate_solutions': [{'tour': [3, 8, 0, 2, 5, 7, 6, 4, 1], 'cur_cost': 1088.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 7, 5, 2, 0, 8, 6, 4, 1], 'cur_cost': 1129.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 8, 0, 5, 7, 3, 2, 4, 1], 'cur_cost': 966.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 4, 7, 8, 1, 0, 5, 3, 2], dtype=int64), 'cur_cost': 1076.0, 'intermediate_solutions': [{'tour': array([8, 6, 3, 7, 0, 2, 5, 4, 1]), 'cur_cost': 1109.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 8, 6, 3, 0, 2, 5, 4, 1]), 'cur_cost': 1124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 7, 8, 6, 3, 2, 5, 4, 1]), 'cur_cost': 1024.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 7, 8, 6, 0, 2, 5, 4, 1]), 'cur_cost': 1097.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 0, 7, 8, 6, 2, 5, 4, 1]), 'cur_cost': 1210.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-04 16:43:15,151 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 16:43:15,152 - StatsExpert - INFO - 开始统计分析
2025-08-04 16:43:15,153 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=694.000, 多样性=0.919
2025-08-04 16:43:15,153 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-04 16:43:15,153 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-04 16:43:15,154 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 16:43:15,154 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.09939283327268532, 'best_improvement': 0.12814070351758794}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.053824362606232204}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.00792970584984611, 'recent_improvements': [-0.10566606865531536, 0.0985339523093313, -0.12152548035500756], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 16:43:15,155 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-04 16:43:15,180 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_solution.json
2025-08-04 16:43:15,181 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_20250804_164315.solution
2025-08-04 16:43:15,196 - __main__ - INFO - 评估统计 - 总次数: 240295.33333312775, 运行时间: 9.88s, 最佳成本: 680.0
2025-08-04 16:43:15,196 - __main__ - INFO - 实例 simple1_9 处理完成
