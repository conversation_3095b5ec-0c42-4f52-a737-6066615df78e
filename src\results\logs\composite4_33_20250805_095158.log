2025-08-05 09:51:58,832 - __main__ - INFO - composite4_33 开始进化第 1 代
2025-08-05 09:51:58,833 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 09:51:58,834 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:58,836 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=8881.000, 多样性=0.968
2025-08-05 09:51:58,838 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:58,840 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.968
2025-08-05 09:51:58,841 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:58,843 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 09:51:58,844 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:58,844 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 09:51:58,844 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 09:51:58,855 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.200, 适应度梯度: -2277.520, 聚类评分: 0.000, 覆盖率: 0.095, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:51:58,855 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 09:51:58,855 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 09:51:58,855 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite4_33
2025-08-05 09:51:58,860 - visualization.landscape_visualizer - INFO - 插值约束: 222 个点被约束到最小值 8881.00
2025-08-05 09:51:58,952 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite4_33\landscape_composite4_33_iter_76_20250805_095158.html
2025-08-05 09:51:59,007 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite4_33\dashboard_composite4_33_iter_76_20250805_095158.html
2025-08-05 09:51:59,007 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 76
2025-08-05 09:51:59,007 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 09:51:59,008 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1653秒
2025-08-05 09:51:59,008 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 152, 'max_size': 500, 'hits': 0, 'misses': 152, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 499, 'misses': 270, 'hit_rate': 0.6488946684005201, 'evictions': 170, 'ttl': 7200}}
2025-08-05 09:51:59,008 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -2277.5200000000004, 'local_optima_density': 0.2, 'gradient_variance': 518240416.0896001, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0947, 'fitness_entropy': 0.8427376486136672, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -2277.520)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.095)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358718.8553967, 'performance_metrics': {}}}
2025-08-05 09:51:59,008 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:59,009 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:59,009 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:59,009 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:59,010 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 09:51:59,010 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 09:51:59,010 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 09:51:59,010 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:59,011 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:59,011 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 09:51:59,011 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:59,011 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:59,011 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:59,011 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:51:59,011 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:51:59,011 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:59,013 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 33
2025-08-05 09:51:59,013 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,013 - ExplorationExpert - INFO - 探索路径生成完成，成本: 50090.0, 路径长度: 33, 收集中间解: 0
2025-08-05 09:51:59,013 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [28, 24, 18, 32, 21, 13, 16, 10, 4, 27, 19, 17, 6, 20, 30, 22, 2, 7, 12, 11, 8, 25, 26, 31, 14, 9, 29, 15, 23, 5, 0, 1, 3], 'cur_cost': 50090.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:59,013 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 50090.00)
2025-08-05 09:51:59,013 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:51:59,014 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:51:59,014 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:59,015 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 09:51:59,015 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,015 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11664.0, 路径长度: 33, 收集中间解: 0
2025-08-05 09:51:59,015 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 14, 18, 7, 2, 3, 4, 5, 9, 1, 6, 8, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 19, 15, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 11664.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:59,015 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 11664.00)
2025-08-05 09:51:59,015 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:51:59,016 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:51:59,016 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:59,018 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 33
2025-08-05 09:51:59,019 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,019 - ExplorationExpert - INFO - 探索路径生成完成，成本: 23127.0, 路径长度: 33, 收集中间解: 0
2025-08-05 09:51:59,019 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [8, 2, 0, 3, 28, 13, 32, 27, 26, 31, 29, 7, 11, 9, 16, 19, 17, 4, 5, 18, 10, 14, 1, 30, 12, 22, 23, 21, 20, 25, 24, 6, 15], 'cur_cost': 23127.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:59,019 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 23127.00)
2025-08-05 09:51:59,019 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:51:59,019 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:51:59,019 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:59,022 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 33
2025-08-05 09:51:59,022 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,022 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19568.0, 路径长度: 33, 收集中间解: 0
2025-08-05 09:51:59,023 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [16, 10, 15, 32, 28, 1, 30, 7, 31, 8, 3, 27, 29, 13, 12, 5, 6, 26, 11, 4, 14, 18, 19, 2, 9, 0, 22, 21, 25, 24, 20, 23, 17], 'cur_cost': 19568.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:59,023 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 19568.00)
2025-08-05 09:51:59,023 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:51:59,023 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:51:59,023 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:59,024 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 09:51:59,024 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,025 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16976.0, 路径长度: 33, 收集中间解: 0
2025-08-05 09:51:59,025 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 20, 4, 3, 2, 1, 6, 5, 9, 8, 7, 28, 26, 27, 32, 31, 30, 29, 13, 17, 16, 11, 10, 19, 15, 18, 14, 22, 21, 25, 24, 23], 'cur_cost': 16976.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:59,025 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 16976.00)
2025-08-05 09:51:59,025 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:51:59,025 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:51:59,025 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:59,026 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 09:51:59,027 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,027 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9731.0, 路径长度: 33, 收集中间解: 0
2025-08-05 09:51:59,027 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 8, 9, 20, 21, 22, 23, 24, 25, 30, 26, 27, 28, 29, 31, 32, 4, 3, 2, 1, 6, 5, 7, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13], 'cur_cost': 9731.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:59,027 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 9731.00)
2025-08-05 09:51:59,027 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:51:59,027 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:59,028 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:59,028 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 53492.0
2025-08-05 09:51:59,036 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:59,036 - ExploitationExpert - INFO - res_population_costs: [8786.0, 8786, 8773, 8761]
2025-08-05 09:51:59,037 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 13, 14, 18,
       19, 15, 10, 11, 16, 17, 12, 32, 31, 30, 29, 26, 27, 28,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 13, 14, 18,
       19, 15, 10, 11, 16, 17, 12, 32, 31, 30, 29, 26, 27, 28,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 26, 28,
       29, 30, 31, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64)]
2025-08-05 09:51:59,038 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:59,038 - ExploitationExpert - INFO - populations: [{'tour': [28, 24, 18, 32, 21, 13, 16, 10, 4, 27, 19, 17, 6, 20, 30, 22, 2, 7, 12, 11, 8, 25, 26, 31, 14, 9, 29, 15, 23, 5, 0, 1, 3], 'cur_cost': 50090.0}, {'tour': [0, 14, 18, 7, 2, 3, 4, 5, 9, 1, 6, 8, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 19, 15, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 11664.0}, {'tour': [8, 2, 0, 3, 28, 13, 32, 27, 26, 31, 29, 7, 11, 9, 16, 19, 17, 4, 5, 18, 10, 14, 1, 30, 12, 22, 23, 21, 20, 25, 24, 6, 15], 'cur_cost': 23127.0}, {'tour': [16, 10, 15, 32, 28, 1, 30, 7, 31, 8, 3, 27, 29, 13, 12, 5, 6, 26, 11, 4, 14, 18, 19, 2, 9, 0, 22, 21, 25, 24, 20, 23, 17], 'cur_cost': 19568.0}, {'tour': [0, 12, 20, 4, 3, 2, 1, 6, 5, 9, 8, 7, 28, 26, 27, 32, 31, 30, 29, 13, 17, 16, 11, 10, 19, 15, 18, 14, 22, 21, 25, 24, 23], 'cur_cost': 16976.0}, {'tour': [0, 8, 9, 20, 21, 22, 23, 24, 25, 30, 26, 27, 28, 29, 31, 32, 4, 3, 2, 1, 6, 5, 7, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13], 'cur_cost': 9731.0}, {'tour': array([15, 29,  6, 20, 19, 10, 23, 13,  1, 28, 31,  3, 16, 21, 18, 17,  2,
       24, 30, 27, 32,  5, 25, 11,  4, 22, 12, 14,  9, 26,  8,  7,  0],
      dtype=int64), 'cur_cost': 53492.0}, {'tour': array([25,  8, 13, 31, 21, 10, 20, 17,  6, 29, 27, 24, 22, 30, 19, 15,  9,
        2,  4,  5, 11, 16, 23,  3, 26, 28, 18,  1,  0, 14, 32,  7, 12],
      dtype=int64), 'cur_cost': 48892.0}, {'tour': array([ 1, 23, 16, 12, 15,  4, 13,  8, 31, 19, 26, 28, 21, 10,  5, 22, 24,
        7, 29, 25,  9, 20, 11, 17,  2,  0, 30, 18, 27,  3, 32,  6, 14],
      dtype=int64), 'cur_cost': 49351.0}, {'tour': array([24, 12,  3, 17, 20, 27, 13,  0, 18, 14,  5, 29, 30, 28, 23, 22,  2,
       31, 15, 19, 10, 11,  8, 32, 26, 16,  4, 25,  1,  7,  6, 21,  9],
      dtype=int64), 'cur_cost': 46087.0}]
2025-08-05 09:51:59,040 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:59,040 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 196, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 196, 'cache_hits': 0, 'similarity_calculations': 888, 'cache_hit_rate': 0.0, 'cache_size': 888}}
2025-08-05 09:51:59,040 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([15, 29,  6, 20, 19, 10, 23, 13,  1, 28, 31,  3, 16, 21, 18, 17,  2,
       24, 30, 27, 32,  5, 25, 11,  4, 22, 12, 14,  9, 26,  8,  7,  0],
      dtype=int64), 'cur_cost': 53492.0, 'intermediate_solutions': [{'tour': array([12,  8,  3,  2,  4, 10, 27,  9, 22, 11, 25, 24, 17, 20, 19, 21, 28,
        0,  5, 23,  1, 14, 26,  6, 30, 32, 15,  7, 31, 13, 18, 16, 29],
      dtype=int64), 'cur_cost': 49176.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2, 12,  8,  3,  4, 10, 27,  9, 22, 11, 25, 24, 17, 20, 19, 21, 28,
        0,  5, 23,  1, 14, 26,  6, 30, 32, 15,  7, 31, 13, 18, 16, 29],
      dtype=int64), 'cur_cost': 49828.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 4,  2, 12,  8,  3, 10, 27,  9, 22, 11, 25, 24, 17, 20, 19, 21, 28,
        0,  5, 23,  1, 14, 26,  6, 30, 32, 15,  7, 31, 13, 18, 16, 29],
      dtype=int64), 'cur_cost': 49829.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 3,  2, 12,  8,  4, 10, 27,  9, 22, 11, 25, 24, 17, 20, 19, 21, 28,
        0,  5, 23,  1, 14, 26,  6, 30, 32, 15,  7, 31, 13, 18, 16, 29],
      dtype=int64), 'cur_cost': 49821.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 3,  4,  2, 12,  8, 10, 27,  9, 22, 11, 25, 24, 17, 20, 19, 21, 28,
        0,  5, 23,  1, 14, 26,  6, 30, 32, 15,  7, 31, 13, 18, 16, 29],
      dtype=int64), 'cur_cost': 49823.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:59,041 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 53492.00)
2025-08-05 09:51:59,041 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:51:59,041 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:51:59,041 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:59,043 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 09:51:59,043 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,044 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9610.0, 路径长度: 33, 收集中间解: 0
2025-08-05 09:51:59,044 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 6, 11, 12, 17, 16, 10, 19, 15, 18, 14, 13, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 9610.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:59,044 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 9610.00)
2025-08-05 09:51:59,044 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:51:59,044 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:59,044 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:59,045 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 56040.0
2025-08-05 09:51:59,056 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 09:51:59,056 - ExploitationExpert - INFO - res_population_costs: [8786.0, 8786, 8773, 8761, 8761]
2025-08-05 09:51:59,056 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 13, 14, 18,
       19, 15, 10, 11, 16, 17, 12, 32, 31, 30, 29, 26, 27, 28,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 13, 14, 18,
       19, 15, 10, 11, 16, 17, 12, 32, 31, 30, 29, 26, 27, 28,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 26, 28,
       29, 30, 31, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64)]
2025-08-05 09:51:59,060 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:59,060 - ExploitationExpert - INFO - populations: [{'tour': [28, 24, 18, 32, 21, 13, 16, 10, 4, 27, 19, 17, 6, 20, 30, 22, 2, 7, 12, 11, 8, 25, 26, 31, 14, 9, 29, 15, 23, 5, 0, 1, 3], 'cur_cost': 50090.0}, {'tour': [0, 14, 18, 7, 2, 3, 4, 5, 9, 1, 6, 8, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 19, 15, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 11664.0}, {'tour': [8, 2, 0, 3, 28, 13, 32, 27, 26, 31, 29, 7, 11, 9, 16, 19, 17, 4, 5, 18, 10, 14, 1, 30, 12, 22, 23, 21, 20, 25, 24, 6, 15], 'cur_cost': 23127.0}, {'tour': [16, 10, 15, 32, 28, 1, 30, 7, 31, 8, 3, 27, 29, 13, 12, 5, 6, 26, 11, 4, 14, 18, 19, 2, 9, 0, 22, 21, 25, 24, 20, 23, 17], 'cur_cost': 19568.0}, {'tour': [0, 12, 20, 4, 3, 2, 1, 6, 5, 9, 8, 7, 28, 26, 27, 32, 31, 30, 29, 13, 17, 16, 11, 10, 19, 15, 18, 14, 22, 21, 25, 24, 23], 'cur_cost': 16976.0}, {'tour': [0, 8, 9, 20, 21, 22, 23, 24, 25, 30, 26, 27, 28, 29, 31, 32, 4, 3, 2, 1, 6, 5, 7, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13], 'cur_cost': 9731.0}, {'tour': array([15, 29,  6, 20, 19, 10, 23, 13,  1, 28, 31,  3, 16, 21, 18, 17,  2,
       24, 30, 27, 32,  5, 25, 11,  4, 22, 12, 14,  9, 26,  8,  7,  0],
      dtype=int64), 'cur_cost': 53492.0}, {'tour': [0, 6, 11, 12, 17, 16, 10, 19, 15, 18, 14, 13, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 9610.0}, {'tour': array([ 1, 17,  2, 14, 31,  8, 27, 19, 12,  9, 20, 28, 11,  3, 15, 25, 32,
        5, 22, 16,  7,  0, 24, 30,  6,  4, 10, 13, 21, 18, 26, 23, 29],
      dtype=int64), 'cur_cost': 56040.0}, {'tour': array([24, 12,  3, 17, 20, 27, 13,  0, 18, 14,  5, 29, 30, 28, 23, 22,  2,
       31, 15, 19, 10, 11,  8, 32, 26, 16,  4, 25,  1,  7,  6, 21,  9],
      dtype=int64), 'cur_cost': 46087.0}]
2025-08-05 09:51:59,062 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:51:59,062 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 197, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 197, 'cache_hits': 0, 'similarity_calculations': 889, 'cache_hit_rate': 0.0, 'cache_size': 889}}
2025-08-05 09:51:59,063 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 1, 17,  2, 14, 31,  8, 27, 19, 12,  9, 20, 28, 11,  3, 15, 25, 32,
        5, 22, 16,  7,  0, 24, 30,  6,  4, 10, 13, 21, 18, 26, 23, 29],
      dtype=int64), 'cur_cost': 56040.0, 'intermediate_solutions': [{'tour': array([16, 23,  1, 12, 15,  4, 13,  8, 31, 19, 26, 28, 21, 10,  5, 22, 24,
        7, 29, 25,  9, 20, 11, 17,  2,  0, 30, 18, 27,  3, 32,  6, 14],
      dtype=int64), 'cur_cost': 49343.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([12, 16, 23,  1, 15,  4, 13,  8, 31, 19, 26, 28, 21, 10,  5, 22, 24,
        7, 29, 25,  9, 20, 11, 17,  2,  0, 30, 18, 27,  3, 32,  6, 14],
      dtype=int64), 'cur_cost': 49354.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([15, 12, 16, 23,  1,  4, 13,  8, 31, 19, 26, 28, 21, 10,  5, 22, 24,
        7, 29, 25,  9, 20, 11, 17,  2,  0, 30, 18, 27,  3, 32,  6, 14],
      dtype=int64), 'cur_cost': 46601.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 1, 12, 16, 23, 15,  4, 13,  8, 31, 19, 26, 28, 21, 10,  5, 22, 24,
        7, 29, 25,  9, 20, 11, 17,  2,  0, 30, 18, 27,  3, 32,  6, 14],
      dtype=int64), 'cur_cost': 51359.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 1, 15, 12, 16, 23,  4, 13,  8, 31, 19, 26, 28, 21, 10,  5, 22, 24,
        7, 29, 25,  9, 20, 11, 17,  2,  0, 30, 18, 27,  3, 32,  6, 14],
      dtype=int64), 'cur_cost': 49345.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:59,063 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 56040.00)
2025-08-05 09:51:59,063 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:51:59,063 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:51:59,063 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:59,064 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 09:51:59,064 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,065 - ExplorationExpert - INFO - 探索路径生成完成，成本: 8915.0, 路径长度: 33, 收集中间解: 0
2025-08-05 09:51:59,065 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 9, 6, 5, 4, 3, 2, 1, 7, 8, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 8915.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:59,065 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 8915.00)
2025-08-05 09:51:59,065 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:59,065 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:59,066 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [28, 24, 18, 32, 21, 13, 16, 10, 4, 27, 19, 17, 6, 20, 30, 22, 2, 7, 12, 11, 8, 25, 26, 31, 14, 9, 29, 15, 23, 5, 0, 1, 3], 'cur_cost': 50090.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 18, 7, 2, 3, 4, 5, 9, 1, 6, 8, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 19, 15, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 11664.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [8, 2, 0, 3, 28, 13, 32, 27, 26, 31, 29, 7, 11, 9, 16, 19, 17, 4, 5, 18, 10, 14, 1, 30, 12, 22, 23, 21, 20, 25, 24, 6, 15], 'cur_cost': 23127.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [16, 10, 15, 32, 28, 1, 30, 7, 31, 8, 3, 27, 29, 13, 12, 5, 6, 26, 11, 4, 14, 18, 19, 2, 9, 0, 22, 21, 25, 24, 20, 23, 17], 'cur_cost': 19568.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 20, 4, 3, 2, 1, 6, 5, 9, 8, 7, 28, 26, 27, 32, 31, 30, 29, 13, 17, 16, 11, 10, 19, 15, 18, 14, 22, 21, 25, 24, 23], 'cur_cost': 16976.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 9, 20, 21, 22, 23, 24, 25, 30, 26, 27, 28, 29, 31, 32, 4, 3, 2, 1, 6, 5, 7, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13], 'cur_cost': 9731.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([15, 29,  6, 20, 19, 10, 23, 13,  1, 28, 31,  3, 16, 21, 18, 17,  2,
       24, 30, 27, 32,  5, 25, 11,  4, 22, 12, 14,  9, 26,  8,  7,  0],
      dtype=int64), 'cur_cost': 53492.0, 'intermediate_solutions': [{'tour': array([12,  8,  3,  2,  4, 10, 27,  9, 22, 11, 25, 24, 17, 20, 19, 21, 28,
        0,  5, 23,  1, 14, 26,  6, 30, 32, 15,  7, 31, 13, 18, 16, 29],
      dtype=int64), 'cur_cost': 49176.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2, 12,  8,  3,  4, 10, 27,  9, 22, 11, 25, 24, 17, 20, 19, 21, 28,
        0,  5, 23,  1, 14, 26,  6, 30, 32, 15,  7, 31, 13, 18, 16, 29],
      dtype=int64), 'cur_cost': 49828.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 4,  2, 12,  8,  3, 10, 27,  9, 22, 11, 25, 24, 17, 20, 19, 21, 28,
        0,  5, 23,  1, 14, 26,  6, 30, 32, 15,  7, 31, 13, 18, 16, 29],
      dtype=int64), 'cur_cost': 49829.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 3,  2, 12,  8,  4, 10, 27,  9, 22, 11, 25, 24, 17, 20, 19, 21, 28,
        0,  5, 23,  1, 14, 26,  6, 30, 32, 15,  7, 31, 13, 18, 16, 29],
      dtype=int64), 'cur_cost': 49821.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 3,  4,  2, 12,  8, 10, 27,  9, 22, 11, 25, 24, 17, 20, 19, 21, 28,
        0,  5, 23,  1, 14, 26,  6, 30, 32, 15,  7, 31, 13, 18, 16, 29],
      dtype=int64), 'cur_cost': 49823.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 11, 12, 17, 16, 10, 19, 15, 18, 14, 13, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 9610.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 1, 17,  2, 14, 31,  8, 27, 19, 12,  9, 20, 28, 11,  3, 15, 25, 32,
        5, 22, 16,  7,  0, 24, 30,  6,  4, 10, 13, 21, 18, 26, 23, 29],
      dtype=int64), 'cur_cost': 56040.0, 'intermediate_solutions': [{'tour': array([16, 23,  1, 12, 15,  4, 13,  8, 31, 19, 26, 28, 21, 10,  5, 22, 24,
        7, 29, 25,  9, 20, 11, 17,  2,  0, 30, 18, 27,  3, 32,  6, 14],
      dtype=int64), 'cur_cost': 49343.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([12, 16, 23,  1, 15,  4, 13,  8, 31, 19, 26, 28, 21, 10,  5, 22, 24,
        7, 29, 25,  9, 20, 11, 17,  2,  0, 30, 18, 27,  3, 32,  6, 14],
      dtype=int64), 'cur_cost': 49354.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([15, 12, 16, 23,  1,  4, 13,  8, 31, 19, 26, 28, 21, 10,  5, 22, 24,
        7, 29, 25,  9, 20, 11, 17,  2,  0, 30, 18, 27,  3, 32,  6, 14],
      dtype=int64), 'cur_cost': 46601.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 1, 12, 16, 23, 15,  4, 13,  8, 31, 19, 26, 28, 21, 10,  5, 22, 24,
        7, 29, 25,  9, 20, 11, 17,  2,  0, 30, 18, 27,  3, 32,  6, 14],
      dtype=int64), 'cur_cost': 51359.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 1, 15, 12, 16, 23,  4, 13,  8, 31, 19, 26, 28, 21, 10,  5, 22, 24,
        7, 29, 25,  9, 20, 11, 17,  2,  0, 30, 18, 27,  3, 32,  6, 14],
      dtype=int64), 'cur_cost': 49345.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 6, 5, 4, 3, 2, 1, 7, 8, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 8915.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 09:51:59,067 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:59,067 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:59,070 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=8915.000, 多样性=0.937
2025-08-05 09:51:59,070 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 09:51:59,070 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 09:51:59,070 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:59,071 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.04467159918127275, 'best_improvement': -0.0038283977029613782}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.03268428372739934}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.006509921669508247, 'recent_improvements': [0.003424167143579016, -0.038358799787394766, -0.009595676195437474], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 5, 'new_count': 5, 'count_change': 0, 'old_best_cost': 8761, 'new_best_cost': 8761, 'quality_improvement': 0.0, 'old_diversity': 0.7424242424242424, 'new_diversity': 0.7424242424242424, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启']}
2025-08-05 09:51:59,071 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 09:51:59,071 - __main__ - INFO - composite4_33 开始进化第 2 代
2025-08-05 09:51:59,071 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 09:51:59,072 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:59,072 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=8915.000, 多样性=0.937
2025-08-05 09:51:59,072 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:59,074 - PathExpert - INFO - 路径结构分析完成: 公共边数量=5, 路径相似性=0.937
2025-08-05 09:51:59,075 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:59,076 - EliteExpert - INFO - 精英解分析完成: 精英解数量=5, 多样性=0.742
2025-08-05 09:51:59,078 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 09:51:59,078 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:59,078 - LandscapeExpert - INFO - 添加精英解数据: 5个精英解
2025-08-05 09:51:59,078 - LandscapeExpert - INFO - 数据提取成功: 15个路径, 15个适应度值
2025-08-05 09:51:59,103 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.133, 适应度梯度: -4360.507, 聚类评分: 0.000, 覆盖率: 0.096, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:51:59,103 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 09:51:59,103 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:51:59,104 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite4_33
2025-08-05 09:51:59,109 - visualization.landscape_visualizer - INFO - 插值约束: 206 个点被约束到最小值 8761.00
2025-08-05 09:51:59,219 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite4_33\landscape_composite4_33_iter_77_20250805_095159.html
2025-08-05 09:51:59,263 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite4_33\dashboard_composite4_33_iter_77_20250805_095159.html
2025-08-05 09:51:59,263 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 77
2025-08-05 09:51:59,263 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 09:51:59,263 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1851秒
2025-08-05 09:51:59,263 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.13333333333333333, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -4360.506666666667, 'local_optima_density': 0.13333333333333333, 'gradient_variance': 294047841.07128894, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0961, 'fitness_entropy': 0.7240345888528967, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -4360.507)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.096)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358719.1032338, 'performance_metrics': {}}}
2025-08-05 09:51:59,263 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:59,263 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:59,264 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:59,264 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:59,264 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 09:51:59,264 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 09:51:59,265 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 09:51:59,265 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:59,265 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:59,265 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 09:51:59,265 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:59,265 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:59,266 - experts.management.collaboration_manager - INFO - 识别精英个体: {9, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:59,266 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:51:59,266 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:51:59,266 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:59,268 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 33
2025-08-05 09:51:59,268 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,268 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,268 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,268 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,269 - ExplorationExpert - INFO - 探索路径生成完成，成本: 29291.0, 路径长度: 33, 收集中间解: 3
2025-08-05 09:51:59,269 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [6, 2, 29, 3, 4, 7, 13, 12, 31, 11, 28, 16, 17, 14, 9, 30, 8, 26, 5, 10, 19, 27, 15, 22, 23, 20, 24, 21, 32, 0, 1, 18, 25], 'cur_cost': 29291.0, 'intermediate_solutions': [{'tour': [28, 24, 18, 32, 21, 13, 16, 10, 4, 27, 23, 17, 6, 20, 30, 22, 2, 7, 12, 11, 8, 25, 26, 31, 14, 9, 29, 15, 19, 5, 0, 1, 3], 'cur_cost': 50124.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [28, 24, 4, 10, 16, 13, 21, 32, 18, 27, 19, 17, 6, 20, 30, 22, 2, 7, 12, 11, 8, 25, 26, 31, 14, 9, 29, 15, 23, 5, 0, 1, 3], 'cur_cost': 50066.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [28, 24, 18, 32, 21, 26, 13, 16, 10, 4, 27, 19, 17, 6, 20, 30, 22, 2, 7, 12, 11, 8, 25, 31, 14, 9, 29, 15, 23, 5, 0, 1, 3], 'cur_cost': 50249.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:59,269 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 29291.00)
2025-08-05 09:51:59,269 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:51:59,269 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:51:59,269 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:59,270 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 09:51:59,270 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,271 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,271 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,271 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,271 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14911.0, 路径长度: 33, 收集中间解: 3
2025-08-05 09:51:59,271 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 5, 22, 6, 1, 2, 3, 4, 8, 7, 9, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13, 23, 20, 21, 25, 24], 'cur_cost': 14911.0, 'intermediate_solutions': [{'tour': [0, 14, 18, 7, 2, 3, 4, 5, 9, 20, 6, 8, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 19, 15, 13, 22, 1, 21, 25, 24, 23], 'cur_cost': 23669.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 29, 30, 31, 32, 27, 26, 28, 8, 6, 1, 9, 5, 4, 3, 2, 7, 18, 14, 12, 17, 16, 11, 10, 19, 15, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 9589.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 18, 7, 2, 3, 4, 31, 5, 9, 1, 6, 8, 28, 26, 27, 32, 30, 29, 12, 17, 16, 11, 10, 19, 15, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 12692.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:59,272 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 14911.00)
2025-08-05 09:51:59,272 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:51:59,272 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:51:59,272 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:59,274 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 33
2025-08-05 09:51:59,274 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,274 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,274 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,274 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,274 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27035.0, 路径长度: 33, 收集中间解: 3
2025-08-05 09:51:59,275 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [1, 2, 4, 29, 5, 17, 15, 28, 9, 8, 31, 3, 27, 30, 11, 13, 19, 32, 12, 18, 14, 6, 10, 22, 24, 25, 20, 23, 26, 0, 7, 16, 21], 'cur_cost': 27035.0, 'intermediate_solutions': [{'tour': [8, 2, 0, 3, 28, 13, 32, 27, 26, 31, 29, 7, 11, 9, 16, 19, 17, 4, 5, 18, 10, 14, 15, 30, 12, 22, 23, 21, 20, 25, 24, 6, 1], 'cur_cost': 19687.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 2, 0, 3, 28, 13, 32, 27, 26, 31, 29, 7, 11, 9, 16, 19, 17, 4, 5, 18, 10, 14, 1, 15, 6, 24, 25, 20, 21, 23, 22, 12, 30], 'cur_cost': 23114.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 2, 0, 3, 28, 13, 32, 27, 26, 31, 29, 7, 11, 9, 16, 19, 17, 4, 5, 18, 10, 14, 1, 30, 12, 22, 23, 15, 21, 20, 25, 24, 6], 'cur_cost': 27696.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:59,275 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 27035.00)
2025-08-05 09:51:59,275 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:51:59,275 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:51:59,275 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:59,276 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 09:51:59,276 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,276 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,276 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,277 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,277 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16957.0, 路径长度: 33, 收集中间解: 3
2025-08-05 09:51:59,277 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 10, 22, 2, 3, 4, 5, 9, 1, 6, 7, 8, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 19, 15, 18, 14, 13, 23, 20, 21, 25, 24], 'cur_cost': 16957.0, 'intermediate_solutions': [{'tour': [13, 10, 15, 32, 28, 1, 30, 7, 31, 8, 3, 27, 29, 16, 12, 5, 6, 26, 11, 4, 14, 18, 19, 2, 9, 0, 22, 21, 25, 24, 20, 23, 17], 'cur_cost': 19589.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [16, 10, 15, 32, 28, 1, 30, 7, 31, 8, 3, 29, 27, 13, 12, 5, 6, 26, 11, 4, 14, 18, 19, 2, 9, 0, 22, 21, 25, 24, 20, 23, 17], 'cur_cost': 19478.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [16, 10, 15, 32, 28, 1, 30, 7, 31, 8, 3, 22, 27, 29, 13, 12, 5, 6, 26, 11, 4, 14, 18, 19, 2, 9, 0, 21, 25, 24, 20, 23, 17], 'cur_cost': 24845.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:59,277 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 16957.00)
2025-08-05 09:51:59,277 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:51:59,277 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:51:59,277 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:59,279 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 33
2025-08-05 09:51:59,279 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,279 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,279 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,280 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,280 - ExplorationExpert - INFO - 探索路径生成完成，成本: 18872.0, 路径长度: 33, 收集中间解: 3
2025-08-05 09:51:59,280 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [8, 27, 2, 3, 6, 0, 31, 1, 4, 17, 32, 26, 30, 16, 13, 19, 14, 15, 10, 28, 7, 29, 11, 22, 23, 24, 21, 20, 25, 9, 5, 12, 18], 'cur_cost': 18872.0, 'intermediate_solutions': [{'tour': [0, 12, 20, 14, 3, 2, 1, 6, 5, 9, 8, 7, 28, 26, 27, 32, 31, 30, 29, 13, 17, 16, 11, 10, 19, 15, 18, 4, 22, 21, 25, 24, 23], 'cur_cost': 19728.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 16, 17, 13, 29, 30, 31, 32, 27, 26, 28, 7, 8, 9, 5, 6, 1, 2, 3, 4, 20, 12, 11, 10, 19, 15, 18, 14, 22, 21, 25, 24, 23], 'cur_cost': 16990.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 11, 20, 4, 3, 2, 1, 6, 5, 9, 8, 7, 28, 26, 27, 32, 31, 30, 29, 13, 17, 16, 10, 19, 15, 18, 14, 22, 21, 25, 24, 23], 'cur_cost': 16989.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:59,280 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 18872.00)
2025-08-05 09:51:59,280 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:51:59,280 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:51:59,280 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:59,281 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 09:51:59,281 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,281 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,281 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,282 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,282 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9607.0, 路径长度: 33, 收集中间解: 3
2025-08-05 09:51:59,282 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 13, 14, 18, 19, 15, 10, 16, 11, 17, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 9607.0, 'intermediate_solutions': [{'tour': [0, 8, 9, 20, 21, 22, 23, 24, 26, 30, 25, 27, 28, 29, 31, 32, 4, 3, 2, 1, 6, 5, 7, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13], 'cur_cost': 15328.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 9, 20, 21, 22, 23, 24, 25, 30, 26, 27, 28, 29, 31, 32, 4, 3, 2, 1, 6, 5, 7, 12, 17, 16, 11, 10, 19, 15, 18, 13, 14], 'cur_cost': 9745.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 9, 20, 21, 22, 23, 24, 25, 30, 26, 27, 28, 29, 31, 32, 4, 3, 2, 1, 6, 5, 0, 7, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13], 'cur_cost': 9721.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:59,282 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 9607.00)
2025-08-05 09:51:59,282 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:51:59,282 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:59,282 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:59,282 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 42052.0
2025-08-05 09:51:59,292 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 09:51:59,292 - ExploitationExpert - INFO - res_population_costs: [8761, 8761, 8773, 8786.0, 8786, 8761]
2025-08-05 09:51:59,292 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 26, 28,
       29, 30, 31, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 13, 14, 18,
       19, 15, 10, 11, 16, 17, 12, 32, 31, 30, 29, 26, 27, 28,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 13, 14, 18,
       19, 15, 10, 11, 16, 17, 12, 32, 31, 30, 29, 26, 27, 28,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 24, 20, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64)]
2025-08-05 09:51:59,294 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:59,294 - ExploitationExpert - INFO - populations: [{'tour': [6, 2, 29, 3, 4, 7, 13, 12, 31, 11, 28, 16, 17, 14, 9, 30, 8, 26, 5, 10, 19, 27, 15, 22, 23, 20, 24, 21, 32, 0, 1, 18, 25], 'cur_cost': 29291.0}, {'tour': [0, 5, 22, 6, 1, 2, 3, 4, 8, 7, 9, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13, 23, 20, 21, 25, 24], 'cur_cost': 14911.0}, {'tour': [1, 2, 4, 29, 5, 17, 15, 28, 9, 8, 31, 3, 27, 30, 11, 13, 19, 32, 12, 18, 14, 6, 10, 22, 24, 25, 20, 23, 26, 0, 7, 16, 21], 'cur_cost': 27035.0}, {'tour': [0, 10, 22, 2, 3, 4, 5, 9, 1, 6, 7, 8, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 19, 15, 18, 14, 13, 23, 20, 21, 25, 24], 'cur_cost': 16957.0}, {'tour': [8, 27, 2, 3, 6, 0, 31, 1, 4, 17, 32, 26, 30, 16, 13, 19, 14, 15, 10, 28, 7, 29, 11, 22, 23, 24, 21, 20, 25, 9, 5, 12, 18], 'cur_cost': 18872.0}, {'tour': [0, 12, 13, 14, 18, 19, 15, 10, 16, 11, 17, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 9607.0}, {'tour': array([ 3, 13, 15, 27, 25,  2,  9,  7, 18,  5,  6, 22, 16,  0, 32, 29,  4,
        1, 24, 21, 31, 20, 30, 28, 11, 10, 17, 19, 23, 14, 12, 26,  8],
      dtype=int64), 'cur_cost': 42052.0}, {'tour': [0, 6, 11, 12, 17, 16, 10, 19, 15, 18, 14, 13, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 9610.0}, {'tour': [1, 17, 2, 14, 31, 8, 27, 19, 12, 9, 20, 28, 11, 3, 15, 25, 32, 5, 22, 16, 7, 0, 24, 30, 6, 4, 10, 13, 21, 18, 26, 23, 29], 'cur_cost': 56040.0}, {'tour': [0, 9, 6, 5, 4, 3, 2, 1, 7, 8, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 8915.0}]
2025-08-05 09:51:59,295 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:59,295 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 198, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 198, 'cache_hits': 0, 'similarity_calculations': 891, 'cache_hit_rate': 0.0, 'cache_size': 891}}
2025-08-05 09:51:59,296 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([ 3, 13, 15, 27, 25,  2,  9,  7, 18,  5,  6, 22, 16,  0, 32, 29,  4,
        1, 24, 21, 31, 20, 30, 28, 11, 10, 17, 19, 23, 14, 12, 26,  8],
      dtype=int64), 'cur_cost': 42052.0, 'intermediate_solutions': [{'tour': array([ 6, 29, 15, 20, 19, 10, 23, 13,  1, 28, 31,  3, 16, 21, 18, 17,  2,
       24, 30, 27, 32,  5, 25, 11,  4, 22, 12, 14,  9, 26,  8,  7,  0]), 'cur_cost': 52783.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([20,  6, 29, 15, 19, 10, 23, 13,  1, 28, 31,  3, 16, 21, 18, 17,  2,
       24, 30, 27, 32,  5, 25, 11,  4, 22, 12, 14,  9, 26,  8,  7,  0]), 'cur_cost': 51441.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([19, 20,  6, 29, 15, 10, 23, 13,  1, 28, 31,  3, 16, 21, 18, 17,  2,
       24, 30, 27, 32,  5, 25, 11,  4, 22, 12, 14,  9, 26,  8,  7,  0]), 'cur_cost': 53489.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([15, 20,  6, 29, 19, 10, 23, 13,  1, 28, 31,  3, 16, 21, 18, 17,  2,
       24, 30, 27, 32,  5, 25, 11,  4, 22, 12, 14,  9, 26,  8,  7,  0]), 'cur_cost': 53490.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([15, 19, 20,  6, 29, 10, 23, 13,  1, 28, 31,  3, 16, 21, 18, 17,  2,
       24, 30, 27, 32,  5, 25, 11,  4, 22, 12, 14,  9, 26,  8,  7,  0]), 'cur_cost': 53488.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:59,296 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 42052.00)
2025-08-05 09:51:59,296 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:51:59,296 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:51:59,296 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:59,298 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 33
2025-08-05 09:51:59,298 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,298 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,299 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,299 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,299 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15800.0, 路径长度: 33, 收集中间解: 3
2025-08-05 09:51:59,299 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [22, 23, 31, 29, 26, 9, 30, 4, 0, 5, 32, 27, 17, 28, 13, 14, 11, 8, 16, 18, 10, 15, 19, 12, 3, 1, 6, 2, 7, 21, 20, 25, 24], 'cur_cost': 15800.0, 'intermediate_solutions': [{'tour': [0, 6, 11, 12, 17, 16, 10, 19, 15, 18, 4, 13, 32, 26, 27, 28, 29, 30, 31, 8, 14, 3, 2, 1, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 15098.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 11, 12, 17, 16, 10, 19, 15, 18, 32, 13, 14, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 11826.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 11, 12, 17, 16, 10, 19, 15, 18, 14, 13, 2, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 1, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 10403.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:59,299 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 15800.00)
2025-08-05 09:51:59,299 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:51:59,299 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:59,299 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:59,300 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 41183.0
2025-08-05 09:51:59,311 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 09:51:59,311 - ExploitationExpert - INFO - res_population_costs: [8761, 8761, 8773, 8786.0, 8786, 8761]
2025-08-05 09:51:59,312 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 26, 28,
       29, 30, 31, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 13, 14, 18,
       19, 15, 10, 11, 16, 17, 12, 32, 31, 30, 29, 26, 27, 28,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 13, 14, 18,
       19, 15, 10, 11, 16, 17, 12, 32, 31, 30, 29, 26, 27, 28,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 24, 20, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64)]
2025-08-05 09:51:59,313 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:59,313 - ExploitationExpert - INFO - populations: [{'tour': [6, 2, 29, 3, 4, 7, 13, 12, 31, 11, 28, 16, 17, 14, 9, 30, 8, 26, 5, 10, 19, 27, 15, 22, 23, 20, 24, 21, 32, 0, 1, 18, 25], 'cur_cost': 29291.0}, {'tour': [0, 5, 22, 6, 1, 2, 3, 4, 8, 7, 9, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13, 23, 20, 21, 25, 24], 'cur_cost': 14911.0}, {'tour': [1, 2, 4, 29, 5, 17, 15, 28, 9, 8, 31, 3, 27, 30, 11, 13, 19, 32, 12, 18, 14, 6, 10, 22, 24, 25, 20, 23, 26, 0, 7, 16, 21], 'cur_cost': 27035.0}, {'tour': [0, 10, 22, 2, 3, 4, 5, 9, 1, 6, 7, 8, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 19, 15, 18, 14, 13, 23, 20, 21, 25, 24], 'cur_cost': 16957.0}, {'tour': [8, 27, 2, 3, 6, 0, 31, 1, 4, 17, 32, 26, 30, 16, 13, 19, 14, 15, 10, 28, 7, 29, 11, 22, 23, 24, 21, 20, 25, 9, 5, 12, 18], 'cur_cost': 18872.0}, {'tour': [0, 12, 13, 14, 18, 19, 15, 10, 16, 11, 17, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 9607.0}, {'tour': array([ 3, 13, 15, 27, 25,  2,  9,  7, 18,  5,  6, 22, 16,  0, 32, 29,  4,
        1, 24, 21, 31, 20, 30, 28, 11, 10, 17, 19, 23, 14, 12, 26,  8],
      dtype=int64), 'cur_cost': 42052.0}, {'tour': [22, 23, 31, 29, 26, 9, 30, 4, 0, 5, 32, 27, 17, 28, 13, 14, 11, 8, 16, 18, 10, 15, 19, 12, 3, 1, 6, 2, 7, 21, 20, 25, 24], 'cur_cost': 15800.0}, {'tour': array([10, 31, 11, 23,  2, 14,  6, 25, 22,  4,  1,  7, 26,  0, 30, 27, 19,
       32,  9, 28, 15, 13, 17, 18,  3, 21, 24,  8, 20, 12, 16, 29,  5],
      dtype=int64), 'cur_cost': 41183.0}, {'tour': [0, 9, 6, 5, 4, 3, 2, 1, 7, 8, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 8915.0}]
2025-08-05 09:51:59,314 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:59,314 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 199, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 199, 'cache_hits': 0, 'similarity_calculations': 894, 'cache_hit_rate': 0.0, 'cache_size': 894}}
2025-08-05 09:51:59,315 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([10, 31, 11, 23,  2, 14,  6, 25, 22,  4,  1,  7, 26,  0, 30, 27, 19,
       32,  9, 28, 15, 13, 17, 18,  3, 21, 24,  8, 20, 12, 16, 29,  5],
      dtype=int64), 'cur_cost': 41183.0, 'intermediate_solutions': [{'tour': array([ 2, 17,  1, 14, 31,  8, 27, 19, 12,  9, 20, 28, 11,  3, 15, 25, 32,
        5, 22, 16,  7,  0, 24, 30,  6,  4, 10, 13, 21, 18, 26, 23, 29]), 'cur_cost': 56034.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([14,  2, 17,  1, 31,  8, 27, 19, 12,  9, 20, 28, 11,  3, 15, 25, 32,
        5, 22, 16,  7,  0, 24, 30,  6,  4, 10, 13, 21, 18, 26, 23, 29]), 'cur_cost': 56144.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([31, 14,  2, 17,  1,  8, 27, 19, 12,  9, 20, 28, 11,  3, 15, 25, 32,
        5, 22, 16,  7,  0, 24, 30,  6,  4, 10, 13, 21, 18, 26, 23, 29]), 'cur_cost': 55145.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 1, 14,  2, 17, 31,  8, 27, 19, 12,  9, 20, 28, 11,  3, 15, 25, 32,
        5, 22, 16,  7,  0, 24, 30,  6,  4, 10, 13, 21, 18, 26, 23, 29]), 'cur_cost': 56044.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 1, 31, 14,  2, 17,  8, 27, 19, 12,  9, 20, 28, 11,  3, 15, 25, 32,
        5, 22, 16,  7,  0, 24, 30,  6,  4, 10, 13, 21, 18, 26, 23, 29]), 'cur_cost': 56051.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:59,315 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 41183.00)
2025-08-05 09:51:59,315 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:51:59,315 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:51:59,316 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:59,317 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 33
2025-08-05 09:51:59,317 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,318 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,318 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,318 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,318 - ExplorationExpert - INFO - 探索路径生成完成，成本: 24821.0, 路径长度: 33, 收集中间解: 3
2025-08-05 09:51:59,319 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [5, 31, 2, 1, 30, 27, 4, 28, 9, 8, 6, 11, 10, 26, 29, 7, 0, 16, 19, 18, 13, 12, 3, 14, 22, 21, 25, 20, 23, 32, 17, 15, 24], 'cur_cost': 24821.0, 'intermediate_solutions': [{'tour': [0, 9, 29, 5, 4, 3, 2, 1, 7, 8, 28, 26, 27, 32, 31, 30, 6, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 10460.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 6, 5, 4, 3, 2, 1, 7, 8, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 19, 15, 18, 14, 25, 21, 20, 22, 13, 24, 23], 'cur_cost': 16318.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 6, 5, 4, 3, 2, 1, 7, 8, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23, 11], 'cur_cost': 10938.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:59,319 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 24821.00)
2025-08-05 09:51:59,319 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:59,319 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:59,320 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [6, 2, 29, 3, 4, 7, 13, 12, 31, 11, 28, 16, 17, 14, 9, 30, 8, 26, 5, 10, 19, 27, 15, 22, 23, 20, 24, 21, 32, 0, 1, 18, 25], 'cur_cost': 29291.0, 'intermediate_solutions': [{'tour': [28, 24, 18, 32, 21, 13, 16, 10, 4, 27, 23, 17, 6, 20, 30, 22, 2, 7, 12, 11, 8, 25, 26, 31, 14, 9, 29, 15, 19, 5, 0, 1, 3], 'cur_cost': 50124.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [28, 24, 4, 10, 16, 13, 21, 32, 18, 27, 19, 17, 6, 20, 30, 22, 2, 7, 12, 11, 8, 25, 26, 31, 14, 9, 29, 15, 23, 5, 0, 1, 3], 'cur_cost': 50066.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [28, 24, 18, 32, 21, 26, 13, 16, 10, 4, 27, 19, 17, 6, 20, 30, 22, 2, 7, 12, 11, 8, 25, 31, 14, 9, 29, 15, 23, 5, 0, 1, 3], 'cur_cost': 50249.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 22, 6, 1, 2, 3, 4, 8, 7, 9, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13, 23, 20, 21, 25, 24], 'cur_cost': 14911.0, 'intermediate_solutions': [{'tour': [0, 14, 18, 7, 2, 3, 4, 5, 9, 20, 6, 8, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 19, 15, 13, 22, 1, 21, 25, 24, 23], 'cur_cost': 23669.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 29, 30, 31, 32, 27, 26, 28, 8, 6, 1, 9, 5, 4, 3, 2, 7, 18, 14, 12, 17, 16, 11, 10, 19, 15, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 9589.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 18, 7, 2, 3, 4, 31, 5, 9, 1, 6, 8, 28, 26, 27, 32, 30, 29, 12, 17, 16, 11, 10, 19, 15, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 12692.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [1, 2, 4, 29, 5, 17, 15, 28, 9, 8, 31, 3, 27, 30, 11, 13, 19, 32, 12, 18, 14, 6, 10, 22, 24, 25, 20, 23, 26, 0, 7, 16, 21], 'cur_cost': 27035.0, 'intermediate_solutions': [{'tour': [8, 2, 0, 3, 28, 13, 32, 27, 26, 31, 29, 7, 11, 9, 16, 19, 17, 4, 5, 18, 10, 14, 15, 30, 12, 22, 23, 21, 20, 25, 24, 6, 1], 'cur_cost': 19687.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 2, 0, 3, 28, 13, 32, 27, 26, 31, 29, 7, 11, 9, 16, 19, 17, 4, 5, 18, 10, 14, 1, 15, 6, 24, 25, 20, 21, 23, 22, 12, 30], 'cur_cost': 23114.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 2, 0, 3, 28, 13, 32, 27, 26, 31, 29, 7, 11, 9, 16, 19, 17, 4, 5, 18, 10, 14, 1, 30, 12, 22, 23, 15, 21, 20, 25, 24, 6], 'cur_cost': 27696.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 22, 2, 3, 4, 5, 9, 1, 6, 7, 8, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 19, 15, 18, 14, 13, 23, 20, 21, 25, 24], 'cur_cost': 16957.0, 'intermediate_solutions': [{'tour': [13, 10, 15, 32, 28, 1, 30, 7, 31, 8, 3, 27, 29, 16, 12, 5, 6, 26, 11, 4, 14, 18, 19, 2, 9, 0, 22, 21, 25, 24, 20, 23, 17], 'cur_cost': 19589.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [16, 10, 15, 32, 28, 1, 30, 7, 31, 8, 3, 29, 27, 13, 12, 5, 6, 26, 11, 4, 14, 18, 19, 2, 9, 0, 22, 21, 25, 24, 20, 23, 17], 'cur_cost': 19478.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [16, 10, 15, 32, 28, 1, 30, 7, 31, 8, 3, 22, 27, 29, 13, 12, 5, 6, 26, 11, 4, 14, 18, 19, 2, 9, 0, 21, 25, 24, 20, 23, 17], 'cur_cost': 24845.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [8, 27, 2, 3, 6, 0, 31, 1, 4, 17, 32, 26, 30, 16, 13, 19, 14, 15, 10, 28, 7, 29, 11, 22, 23, 24, 21, 20, 25, 9, 5, 12, 18], 'cur_cost': 18872.0, 'intermediate_solutions': [{'tour': [0, 12, 20, 14, 3, 2, 1, 6, 5, 9, 8, 7, 28, 26, 27, 32, 31, 30, 29, 13, 17, 16, 11, 10, 19, 15, 18, 4, 22, 21, 25, 24, 23], 'cur_cost': 19728.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 16, 17, 13, 29, 30, 31, 32, 27, 26, 28, 7, 8, 9, 5, 6, 1, 2, 3, 4, 20, 12, 11, 10, 19, 15, 18, 14, 22, 21, 25, 24, 23], 'cur_cost': 16990.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 11, 20, 4, 3, 2, 1, 6, 5, 9, 8, 7, 28, 26, 27, 32, 31, 30, 29, 13, 17, 16, 10, 19, 15, 18, 14, 22, 21, 25, 24, 23], 'cur_cost': 16989.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 13, 14, 18, 19, 15, 10, 16, 11, 17, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 9607.0, 'intermediate_solutions': [{'tour': [0, 8, 9, 20, 21, 22, 23, 24, 26, 30, 25, 27, 28, 29, 31, 32, 4, 3, 2, 1, 6, 5, 7, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13], 'cur_cost': 15328.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 9, 20, 21, 22, 23, 24, 25, 30, 26, 27, 28, 29, 31, 32, 4, 3, 2, 1, 6, 5, 7, 12, 17, 16, 11, 10, 19, 15, 18, 13, 14], 'cur_cost': 9745.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 9, 20, 21, 22, 23, 24, 25, 30, 26, 27, 28, 29, 31, 32, 4, 3, 2, 1, 6, 5, 0, 7, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13], 'cur_cost': 9721.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 3, 13, 15, 27, 25,  2,  9,  7, 18,  5,  6, 22, 16,  0, 32, 29,  4,
        1, 24, 21, 31, 20, 30, 28, 11, 10, 17, 19, 23, 14, 12, 26,  8],
      dtype=int64), 'cur_cost': 42052.0, 'intermediate_solutions': [{'tour': array([ 6, 29, 15, 20, 19, 10, 23, 13,  1, 28, 31,  3, 16, 21, 18, 17,  2,
       24, 30, 27, 32,  5, 25, 11,  4, 22, 12, 14,  9, 26,  8,  7,  0]), 'cur_cost': 52783.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([20,  6, 29, 15, 19, 10, 23, 13,  1, 28, 31,  3, 16, 21, 18, 17,  2,
       24, 30, 27, 32,  5, 25, 11,  4, 22, 12, 14,  9, 26,  8,  7,  0]), 'cur_cost': 51441.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([19, 20,  6, 29, 15, 10, 23, 13,  1, 28, 31,  3, 16, 21, 18, 17,  2,
       24, 30, 27, 32,  5, 25, 11,  4, 22, 12, 14,  9, 26,  8,  7,  0]), 'cur_cost': 53489.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([15, 20,  6, 29, 19, 10, 23, 13,  1, 28, 31,  3, 16, 21, 18, 17,  2,
       24, 30, 27, 32,  5, 25, 11,  4, 22, 12, 14,  9, 26,  8,  7,  0]), 'cur_cost': 53490.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([15, 19, 20,  6, 29, 10, 23, 13,  1, 28, 31,  3, 16, 21, 18, 17,  2,
       24, 30, 27, 32,  5, 25, 11,  4, 22, 12, 14,  9, 26,  8,  7,  0]), 'cur_cost': 53488.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [22, 23, 31, 29, 26, 9, 30, 4, 0, 5, 32, 27, 17, 28, 13, 14, 11, 8, 16, 18, 10, 15, 19, 12, 3, 1, 6, 2, 7, 21, 20, 25, 24], 'cur_cost': 15800.0, 'intermediate_solutions': [{'tour': [0, 6, 11, 12, 17, 16, 10, 19, 15, 18, 4, 13, 32, 26, 27, 28, 29, 30, 31, 8, 14, 3, 2, 1, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 15098.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 11, 12, 17, 16, 10, 19, 15, 18, 32, 13, 14, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 11826.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 11, 12, 17, 16, 10, 19, 15, 18, 14, 13, 2, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 1, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 10403.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([10, 31, 11, 23,  2, 14,  6, 25, 22,  4,  1,  7, 26,  0, 30, 27, 19,
       32,  9, 28, 15, 13, 17, 18,  3, 21, 24,  8, 20, 12, 16, 29,  5],
      dtype=int64), 'cur_cost': 41183.0, 'intermediate_solutions': [{'tour': array([ 2, 17,  1, 14, 31,  8, 27, 19, 12,  9, 20, 28, 11,  3, 15, 25, 32,
        5, 22, 16,  7,  0, 24, 30,  6,  4, 10, 13, 21, 18, 26, 23, 29]), 'cur_cost': 56034.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([14,  2, 17,  1, 31,  8, 27, 19, 12,  9, 20, 28, 11,  3, 15, 25, 32,
        5, 22, 16,  7,  0, 24, 30,  6,  4, 10, 13, 21, 18, 26, 23, 29]), 'cur_cost': 56144.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([31, 14,  2, 17,  1,  8, 27, 19, 12,  9, 20, 28, 11,  3, 15, 25, 32,
        5, 22, 16,  7,  0, 24, 30,  6,  4, 10, 13, 21, 18, 26, 23, 29]), 'cur_cost': 55145.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 1, 14,  2, 17, 31,  8, 27, 19, 12,  9, 20, 28, 11,  3, 15, 25, 32,
        5, 22, 16,  7,  0, 24, 30,  6,  4, 10, 13, 21, 18, 26, 23, 29]), 'cur_cost': 56044.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 1, 31, 14,  2, 17,  8, 27, 19, 12,  9, 20, 28, 11,  3, 15, 25, 32,
        5, 22, 16,  7,  0, 24, 30,  6,  4, 10, 13, 21, 18, 26, 23, 29]), 'cur_cost': 56051.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [5, 31, 2, 1, 30, 27, 4, 28, 9, 8, 6, 11, 10, 26, 29, 7, 0, 16, 19, 18, 13, 12, 3, 14, 22, 21, 25, 20, 23, 32, 17, 15, 24], 'cur_cost': 24821.0, 'intermediate_solutions': [{'tour': [0, 9, 29, 5, 4, 3, 2, 1, 7, 8, 28, 26, 27, 32, 31, 30, 6, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 10460.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 6, 5, 4, 3, 2, 1, 7, 8, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 19, 15, 18, 14, 25, 21, 20, 22, 13, 24, 23], 'cur_cost': 16318.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 6, 5, 4, 3, 2, 1, 7, 8, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23, 11], 'cur_cost': 10938.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:51:59,321 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:59,321 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:59,323 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9607.000, 多样性=0.952
2025-08-05 09:51:59,323 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 09:51:59,323 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 09:51:59,323 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:59,323 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.08135505202508904, 'best_improvement': -0.0776219854178351}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.01581595974119353}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.04151519948433376, 'recent_improvements': [-0.038358799787394766, -0.009595676195437474, 0.04467159918127275], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 6, 'new_count': 6, 'count_change': 0, 'old_best_cost': 8761, 'new_best_cost': 8761, 'quality_improvement': 0.0, 'old_diversity': 0.7535353535353535, 'new_diversity': 0.7535353535353535, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 09:51:59,324 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 09:51:59,324 - __main__ - INFO - composite4_33 开始进化第 3 代
2025-08-05 09:51:59,324 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 09:51:59,324 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:59,325 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9607.000, 多样性=0.952
2025-08-05 09:51:59,325 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:59,326 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.952
2025-08-05 09:51:59,327 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:59,328 - EliteExpert - INFO - 精英解分析完成: 精英解数量=6, 多样性=0.754
2025-08-05 09:51:59,330 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 09:51:59,330 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:59,330 - LandscapeExpert - INFO - 添加精英解数据: 6个精英解
2025-08-05 09:51:59,330 - LandscapeExpert - INFO - 数据提取成功: 16个路径, 16个适应度值
2025-08-05 09:51:59,356 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.188, 适应度梯度: -1412.512, 聚类评分: 0.000, 覆盖率: 0.097, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:51:59,357 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 09:51:59,357 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:51:59,357 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite4_33
2025-08-05 09:51:59,363 - visualization.landscape_visualizer - INFO - 插值约束: 126 个点被约束到最小值 8761.00
2025-08-05 09:51:59,461 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite4_33\landscape_composite4_33_iter_78_20250805_095159.html
2025-08-05 09:51:59,515 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite4_33\dashboard_composite4_33_iter_78_20250805_095159.html
2025-08-05 09:51:59,515 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 78
2025-08-05 09:51:59,515 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 09:51:59,516 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1860秒
2025-08-05 09:51:59,516 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1875, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -1412.5124999999998, 'local_optima_density': 0.1875, 'gradient_variance': 116198367.93984374, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0971, 'fitness_entropy': 0.8405940738665993, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -1412.512)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.097)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358719.3576279, 'performance_metrics': {}}}
2025-08-05 09:51:59,516 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:59,516 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:59,516 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:59,516 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:59,517 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 09:51:59,517 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 09:51:59,517 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 09:51:59,517 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:59,517 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:59,517 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 09:51:59,518 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:59,518 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:59,518 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 5} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:59,518 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-05 09:51:59,518 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:59,518 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:59,519 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 42018.0
2025-08-05 09:51:59,529 - ExploitationExpert - INFO - res_population_num: 8
2025-08-05 09:51:59,529 - ExploitationExpert - INFO - res_population_costs: [8761, 8761, 8761, 8773, 8786.0, 8786, 8761.0, 8761]
2025-08-05 09:51:59,529 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 24, 20, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 26, 28,
       29, 30, 31, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 13, 14, 18,
       19, 15, 10, 11, 16, 17, 12, 32, 31, 30, 29, 26, 27, 28,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 13, 14, 18,
       19, 15, 10, 11, 16, 17, 12, 32, 31, 30, 29, 26, 27, 28,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64)]
2025-08-05 09:51:59,531 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:59,531 - ExploitationExpert - INFO - populations: [{'tour': array([22, 24,  0,  7, 13, 32, 15, 30, 17,  2,  8,  3, 25, 23, 12, 27, 29,
       31, 19, 18, 14,  9,  6,  1, 21, 10, 20, 16,  5,  4, 11, 28, 26],
      dtype=int64), 'cur_cost': 42018.0}, {'tour': [0, 5, 22, 6, 1, 2, 3, 4, 8, 7, 9, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13, 23, 20, 21, 25, 24], 'cur_cost': 14911.0}, {'tour': [1, 2, 4, 29, 5, 17, 15, 28, 9, 8, 31, 3, 27, 30, 11, 13, 19, 32, 12, 18, 14, 6, 10, 22, 24, 25, 20, 23, 26, 0, 7, 16, 21], 'cur_cost': 27035.0}, {'tour': [0, 10, 22, 2, 3, 4, 5, 9, 1, 6, 7, 8, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 19, 15, 18, 14, 13, 23, 20, 21, 25, 24], 'cur_cost': 16957.0}, {'tour': [8, 27, 2, 3, 6, 0, 31, 1, 4, 17, 32, 26, 30, 16, 13, 19, 14, 15, 10, 28, 7, 29, 11, 22, 23, 24, 21, 20, 25, 9, 5, 12, 18], 'cur_cost': 18872.0}, {'tour': [0, 12, 13, 14, 18, 19, 15, 10, 16, 11, 17, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 9607.0}, {'tour': [3, 13, 15, 27, 25, 2, 9, 7, 18, 5, 6, 22, 16, 0, 32, 29, 4, 1, 24, 21, 31, 20, 30, 28, 11, 10, 17, 19, 23, 14, 12, 26, 8], 'cur_cost': 42052.0}, {'tour': [22, 23, 31, 29, 26, 9, 30, 4, 0, 5, 32, 27, 17, 28, 13, 14, 11, 8, 16, 18, 10, 15, 19, 12, 3, 1, 6, 2, 7, 21, 20, 25, 24], 'cur_cost': 15800.0}, {'tour': [10, 31, 11, 23, 2, 14, 6, 25, 22, 4, 1, 7, 26, 0, 30, 27, 19, 32, 9, 28, 15, 13, 17, 18, 3, 21, 24, 8, 20, 12, 16, 29, 5], 'cur_cost': 41183.0}, {'tour': [5, 31, 2, 1, 30, 27, 4, 28, 9, 8, 6, 11, 10, 26, 29, 7, 0, 16, 19, 18, 13, 12, 3, 14, 22, 21, 25, 20, 23, 32, 17, 15, 24], 'cur_cost': 24821.0}]
2025-08-05 09:51:59,532 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:59,532 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 200, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 200, 'cache_hits': 0, 'similarity_calculations': 898, 'cache_hit_rate': 0.0, 'cache_size': 898}}
2025-08-05 09:51:59,532 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([22, 24,  0,  7, 13, 32, 15, 30, 17,  2,  8,  3, 25, 23, 12, 27, 29,
       31, 19, 18, 14,  9,  6,  1, 21, 10, 20, 16,  5,  4, 11, 28, 26],
      dtype=int64), 'cur_cost': 42018.0, 'intermediate_solutions': [{'tour': array([29,  2,  6,  3,  4,  7, 13, 12, 31, 11, 28, 16, 17, 14,  9, 30,  8,
       26,  5, 10, 19, 27, 15, 22, 23, 20, 24, 21, 32,  0,  1, 18, 25]), 'cur_cost': 28584.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 3, 29,  2,  6,  4,  7, 13, 12, 31, 11, 28, 16, 17, 14,  9, 30,  8,
       26,  5, 10, 19, 27, 15, 22, 23, 20, 24, 21, 32,  0,  1, 18, 25]), 'cur_cost': 29288.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 4,  3, 29,  2,  6,  7, 13, 12, 31, 11, 28, 16, 17, 14,  9, 30,  8,
       26,  5, 10, 19, 27, 15, 22, 23, 20, 24, 21, 32,  0,  1, 18, 25]), 'cur_cost': 29280.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6,  3, 29,  2,  4,  7, 13, 12, 31, 11, 28, 16, 17, 14,  9, 30,  8,
       26,  5, 10, 19, 27, 15, 22, 23, 20, 24, 21, 32,  0,  1, 18, 25]), 'cur_cost': 29298.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6,  4,  3, 29,  2,  7, 13, 12, 31, 11, 28, 16, 17, 14,  9, 30,  8,
       26,  5, 10, 19, 27, 15, 22, 23, 20, 24, 21, 32,  0,  1, 18, 25]), 'cur_cost': 29288.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:59,533 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 42018.00)
2025-08-05 09:51:59,533 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:51:59,533 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:51:59,533 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:59,534 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 09:51:59,534 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,534 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,534 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,534 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,534 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14949.0, 路径长度: 33, 收集中间解: 3
2025-08-05 09:51:59,534 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 22, 17, 10, 16, 11, 12, 13, 18, 14, 19, 15, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 21, 20, 23, 24, 25], 'cur_cost': 14949.0, 'intermediate_solutions': [{'tour': [0, 5, 22, 6, 1, 2, 3, 4, 8, 32, 9, 28, 26, 27, 7, 31, 30, 29, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13, 23, 20, 21, 25, 24], 'cur_cost': 16829.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 22, 6, 1, 2, 3, 4, 8, 7, 9, 28, 25, 21, 20, 23, 13, 14, 18, 15, 19, 10, 11, 16, 17, 12, 29, 30, 31, 32, 27, 26, 24], 'cur_cost': 20495.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 22, 6, 1, 2, 3, 4, 8, 7, 9, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 5, 10, 19, 15, 18, 14, 13, 23, 20, 21, 25, 24], 'cur_cost': 17648.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:59,535 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 14949.00)
2025-08-05 09:51:59,535 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:51:59,535 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:51:59,535 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:59,536 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 09:51:59,536 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,536 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,536 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,536 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,536 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9646.0, 路径长度: 33, 收集中间解: 3
2025-08-05 09:51:59,536 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 5, 13, 10, 16, 11, 17, 12, 18, 14, 19, 15, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 7, 9, 22, 20, 21, 25, 24, 23], 'cur_cost': 9646.0, 'intermediate_solutions': [{'tour': [1, 2, 4, 29, 5, 17, 15, 28, 9, 8, 31, 3, 27, 14, 11, 13, 19, 32, 12, 18, 30, 6, 10, 22, 24, 25, 20, 23, 26, 0, 7, 16, 21], 'cur_cost': 27213.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 2, 4, 29, 5, 17, 15, 28, 9, 8, 31, 3, 27, 22, 10, 6, 14, 18, 12, 32, 19, 13, 11, 30, 24, 25, 20, 23, 26, 0, 7, 16, 21], 'cur_cost': 32446.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 2, 4, 29, 5, 17, 15, 28, 9, 3, 8, 31, 27, 30, 11, 13, 19, 32, 12, 18, 14, 6, 10, 22, 24, 25, 20, 23, 26, 0, 7, 16, 21], 'cur_cost': 26109.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:59,537 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 9646.00)
2025-08-05 09:51:59,537 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:51:59,537 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:51:59,537 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:59,538 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 09:51:59,538 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,538 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,538 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,538 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,538 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15806.0, 路径长度: 33, 收集中间解: 3
2025-08-05 09:51:59,538 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 25, 3, 10, 16, 11, 17, 12, 13, 18, 14, 19, 15, 32, 26, 27, 28, 29, 30, 31, 8, 4, 5, 9, 1, 2, 7, 6, 22, 20, 21, 23, 24], 'cur_cost': 15806.0, 'intermediate_solutions': [{'tour': [0, 10, 22, 2, 3, 4, 5, 9, 1, 6, 7, 8, 28, 26, 27, 32, 14, 30, 29, 12, 17, 16, 11, 19, 15, 18, 31, 13, 23, 20, 21, 25, 24], 'cur_cost': 21444.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 22, 5, 4, 3, 2, 9, 1, 6, 7, 8, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 19, 15, 18, 14, 13, 23, 20, 21, 25, 24], 'cur_cost': 16970.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 22, 2, 3, 4, 5, 9, 1, 6, 7, 8, 28, 26, 32, 31, 30, 29, 12, 27, 17, 16, 11, 19, 15, 18, 14, 13, 23, 20, 21, 25, 24], 'cur_cost': 19128.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:59,539 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 15806.00)
2025-08-05 09:51:59,539 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:51:59,539 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:51:59,539 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:59,540 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 33
2025-08-05 09:51:59,541 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,541 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,541 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,541 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,541 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20610.0, 路径长度: 33, 收集中间解: 3
2025-08-05 09:51:59,541 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [1, 4, 3, 8, 6, 26, 5, 29, 28, 12, 27, 7, 18, 14, 32, 30, 31, 11, 16, 13, 0, 2, 15, 19, 9, 23, 24, 21, 22, 25, 20, 17, 10], 'cur_cost': 20610.0, 'intermediate_solutions': [{'tour': [8, 27, 2, 3, 6, 0, 31, 7, 4, 17, 32, 26, 30, 16, 13, 19, 14, 15, 10, 28, 1, 29, 11, 22, 23, 24, 21, 20, 25, 9, 5, 12, 18], 'cur_cost': 18873.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 27, 2, 3, 6, 0, 31, 1, 4, 17, 10, 15, 14, 19, 13, 16, 30, 26, 32, 28, 7, 29, 11, 22, 23, 24, 21, 20, 25, 9, 5, 12, 18], 'cur_cost': 16689.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 27, 2, 3, 6, 0, 31, 1, 4, 17, 32, 26, 30, 16, 13, 19, 14, 15, 10, 28, 7, 29, 11, 22, 23, 24, 20, 25, 9, 5, 21, 12, 18], 'cur_cost': 24155.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:59,542 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 20610.00)
2025-08-05 09:51:59,542 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:51:59,542 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:51:59,542 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:59,542 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 09:51:59,543 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,543 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,543 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,543 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,543 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14930.0, 路径长度: 33, 收集中间解: 3
2025-08-05 09:51:59,543 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 8, 22, 19, 15, 18, 14, 13, 17, 12, 16, 11, 10, 32, 26, 27, 28, 29, 30, 31, 9, 5, 4, 3, 2, 1, 6, 7, 21, 20, 23, 24, 25], 'cur_cost': 14930.0, 'intermediate_solutions': [{'tour': [0, 12, 13, 14, 18, 19, 15, 10, 16, 11, 17, 32, 26, 27, 28, 29, 9, 31, 8, 4, 3, 2, 1, 6, 5, 30, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 11473.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 16, 10, 15, 19, 18, 14, 13, 12, 0, 17, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 11627.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 13, 14, 18, 19, 15, 10, 16, 11, 17, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 24, 5, 9, 7, 22, 20, 21, 25, 23], 'cur_cost': 15724.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:59,544 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 14930.00)
2025-08-05 09:51:59,544 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:51:59,544 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:59,544 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:59,544 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 51654.0
2025-08-05 09:51:59,555 - ExploitationExpert - INFO - res_population_num: 12
2025-08-05 09:51:59,556 - ExploitationExpert - INFO - res_population_costs: [8761, 8761, 8761, 8773, 8786.0, 8786, 8761.0, 8761, 8761.0, 8761, 8761, 8761]
2025-08-05 09:51:59,556 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 24, 20, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 26, 28,
       29, 30, 31, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 13, 14, 18,
       19, 15, 10, 11, 16, 17, 12, 32, 31, 30, 29, 26, 27, 28,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 13, 14, 18,
       19, 15, 10, 11, 16, 17, 12, 32, 31, 30, 29, 26, 27, 28,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64)]
2025-08-05 09:51:59,559 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:59,559 - ExploitationExpert - INFO - populations: [{'tour': array([22, 24,  0,  7, 13, 32, 15, 30, 17,  2,  8,  3, 25, 23, 12, 27, 29,
       31, 19, 18, 14,  9,  6,  1, 21, 10, 20, 16,  5,  4, 11, 28, 26],
      dtype=int64), 'cur_cost': 42018.0}, {'tour': [0, 22, 17, 10, 16, 11, 12, 13, 18, 14, 19, 15, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 21, 20, 23, 24, 25], 'cur_cost': 14949.0}, {'tour': [0, 5, 13, 10, 16, 11, 17, 12, 18, 14, 19, 15, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 7, 9, 22, 20, 21, 25, 24, 23], 'cur_cost': 9646.0}, {'tour': [0, 25, 3, 10, 16, 11, 17, 12, 13, 18, 14, 19, 15, 32, 26, 27, 28, 29, 30, 31, 8, 4, 5, 9, 1, 2, 7, 6, 22, 20, 21, 23, 24], 'cur_cost': 15806.0}, {'tour': [1, 4, 3, 8, 6, 26, 5, 29, 28, 12, 27, 7, 18, 14, 32, 30, 31, 11, 16, 13, 0, 2, 15, 19, 9, 23, 24, 21, 22, 25, 20, 17, 10], 'cur_cost': 20610.0}, {'tour': [0, 8, 22, 19, 15, 18, 14, 13, 17, 12, 16, 11, 10, 32, 26, 27, 28, 29, 30, 31, 9, 5, 4, 3, 2, 1, 6, 7, 21, 20, 23, 24, 25], 'cur_cost': 14930.0}, {'tour': array([13, 16, 32,  8,  5, 24, 12,  3,  2, 17, 31, 28,  9, 22,  4, 23, 14,
       18, 10,  6, 20, 26, 25,  7, 27, 11,  1, 29, 21,  0, 19, 15, 30],
      dtype=int64), 'cur_cost': 51654.0}, {'tour': [22, 23, 31, 29, 26, 9, 30, 4, 0, 5, 32, 27, 17, 28, 13, 14, 11, 8, 16, 18, 10, 15, 19, 12, 3, 1, 6, 2, 7, 21, 20, 25, 24], 'cur_cost': 15800.0}, {'tour': [10, 31, 11, 23, 2, 14, 6, 25, 22, 4, 1, 7, 26, 0, 30, 27, 19, 32, 9, 28, 15, 13, 17, 18, 3, 21, 24, 8, 20, 12, 16, 29, 5], 'cur_cost': 41183.0}, {'tour': [5, 31, 2, 1, 30, 27, 4, 28, 9, 8, 6, 11, 10, 26, 29, 7, 0, 16, 19, 18, 13, 12, 3, 14, 22, 21, 25, 20, 23, 32, 17, 15, 24], 'cur_cost': 24821.0}]
2025-08-05 09:51:59,560 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:51:59,560 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 201, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 201, 'cache_hits': 0, 'similarity_calculations': 903, 'cache_hit_rate': 0.0, 'cache_size': 903}}
2025-08-05 09:51:59,561 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([13, 16, 32,  8,  5, 24, 12,  3,  2, 17, 31, 28,  9, 22,  4, 23, 14,
       18, 10,  6, 20, 26, 25,  7, 27, 11,  1, 29, 21,  0, 19, 15, 30],
      dtype=int64), 'cur_cost': 51654.0, 'intermediate_solutions': [{'tour': array([15, 13,  3, 27, 25,  2,  9,  7, 18,  5,  6, 22, 16,  0, 32, 29,  4,
        1, 24, 21, 31, 20, 30, 28, 11, 10, 17, 19, 23, 14, 12, 26,  8]), 'cur_cost': 42784.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([27, 15, 13,  3, 25,  2,  9,  7, 18,  5,  6, 22, 16,  0, 32, 29,  4,
        1, 24, 21, 31, 20, 30, 28, 11, 10, 17, 19, 23, 14, 12, 26,  8]), 'cur_cost': 42710.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([25, 27, 15, 13,  3,  2,  9,  7, 18,  5,  6, 22, 16,  0, 32, 29,  4,
        1, 24, 21, 31, 20, 30, 28, 11, 10, 17, 19, 23, 14, 12, 26,  8]), 'cur_cost': 42039.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 3, 27, 15, 13, 25,  2,  9,  7, 18,  5,  6, 22, 16,  0, 32, 29,  4,
        1, 24, 21, 31, 20, 30, 28, 11, 10, 17, 19, 23, 14, 12, 26,  8]), 'cur_cost': 42026.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 3, 25, 27, 15, 13,  2,  9,  7, 18,  5,  6, 22, 16,  0, 32, 29,  4,
        1, 24, 21, 31, 20, 30, 28, 11, 10, 17, 19, 23, 14, 12, 26,  8]), 'cur_cost': 42051.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:59,561 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 51654.00)
2025-08-05 09:51:59,561 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:51:59,561 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:51:59,561 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:59,562 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 09:51:59,563 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,563 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,563 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,563 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,563 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15675.0, 路径长度: 33, 收集中间解: 3
2025-08-05 09:51:59,563 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 21, 3, 16, 11, 17, 12, 13, 18, 14, 19, 15, 10, 32, 26, 27, 28, 29, 30, 31, 8, 4, 5, 9, 1, 2, 7, 6, 22, 20, 23, 24, 25], 'cur_cost': 15675.0, 'intermediate_solutions': [{'tour': [22, 23, 31, 29, 26, 9, 30, 4, 0, 5, 32, 27, 21, 28, 13, 14, 11, 8, 16, 18, 10, 15, 19, 12, 3, 1, 6, 2, 7, 17, 20, 25, 24], 'cur_cost': 21203.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 23, 31, 29, 26, 9, 30, 4, 0, 5, 32, 27, 17, 28, 13, 14, 11, 8, 16, 18, 10, 15, 19, 12, 3, 1, 6, 20, 21, 7, 2, 25, 24], 'cur_cost': 21857.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 23, 12, 31, 29, 26, 9, 30, 4, 0, 5, 32, 27, 17, 28, 13, 14, 11, 8, 16, 18, 10, 15, 19, 3, 1, 6, 2, 7, 21, 20, 25, 24], 'cur_cost': 17869.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:59,564 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 15675.00)
2025-08-05 09:51:59,564 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:51:59,564 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:59,564 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:59,564 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 47456.0
2025-08-05 09:51:59,577 - ExploitationExpert - INFO - res_population_num: 17
2025-08-05 09:51:59,577 - ExploitationExpert - INFO - res_population_costs: [8761, 8761, 8761, 8773, 8786.0, 8786, 8761.0, 8761, 8761.0, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761]
2025-08-05 09:51:59,577 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 24, 20, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 26, 28,
       29, 30, 31, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 13, 14, 18,
       19, 15, 10, 11, 16, 17, 12, 32, 31, 30, 29, 26, 27, 28,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 13, 14, 18,
       19, 15, 10, 11, 16, 17, 12, 32, 31, 30, 29, 26, 27, 28,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 20, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64)]
2025-08-05 09:51:59,582 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:59,582 - ExploitationExpert - INFO - populations: [{'tour': array([22, 24,  0,  7, 13, 32, 15, 30, 17,  2,  8,  3, 25, 23, 12, 27, 29,
       31, 19, 18, 14,  9,  6,  1, 21, 10, 20, 16,  5,  4, 11, 28, 26],
      dtype=int64), 'cur_cost': 42018.0}, {'tour': [0, 22, 17, 10, 16, 11, 12, 13, 18, 14, 19, 15, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 21, 20, 23, 24, 25], 'cur_cost': 14949.0}, {'tour': [0, 5, 13, 10, 16, 11, 17, 12, 18, 14, 19, 15, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 7, 9, 22, 20, 21, 25, 24, 23], 'cur_cost': 9646.0}, {'tour': [0, 25, 3, 10, 16, 11, 17, 12, 13, 18, 14, 19, 15, 32, 26, 27, 28, 29, 30, 31, 8, 4, 5, 9, 1, 2, 7, 6, 22, 20, 21, 23, 24], 'cur_cost': 15806.0}, {'tour': [1, 4, 3, 8, 6, 26, 5, 29, 28, 12, 27, 7, 18, 14, 32, 30, 31, 11, 16, 13, 0, 2, 15, 19, 9, 23, 24, 21, 22, 25, 20, 17, 10], 'cur_cost': 20610.0}, {'tour': [0, 8, 22, 19, 15, 18, 14, 13, 17, 12, 16, 11, 10, 32, 26, 27, 28, 29, 30, 31, 9, 5, 4, 3, 2, 1, 6, 7, 21, 20, 23, 24, 25], 'cur_cost': 14930.0}, {'tour': array([13, 16, 32,  8,  5, 24, 12,  3,  2, 17, 31, 28,  9, 22,  4, 23, 14,
       18, 10,  6, 20, 26, 25,  7, 27, 11,  1, 29, 21,  0, 19, 15, 30],
      dtype=int64), 'cur_cost': 51654.0}, {'tour': [0, 21, 3, 16, 11, 17, 12, 13, 18, 14, 19, 15, 10, 32, 26, 27, 28, 29, 30, 31, 8, 4, 5, 9, 1, 2, 7, 6, 22, 20, 23, 24, 25], 'cur_cost': 15675.0}, {'tour': array([31, 24,  0, 25, 23, 11,  9, 10, 22,  8, 19,  3,  2, 26, 30, 21, 12,
       27,  7, 29, 13, 15,  6, 16, 18, 20, 14, 17, 32,  5,  1,  4, 28],
      dtype=int64), 'cur_cost': 47456.0}, {'tour': [5, 31, 2, 1, 30, 27, 4, 28, 9, 8, 6, 11, 10, 26, 29, 7, 0, 16, 19, 18, 13, 12, 3, 14, 22, 21, 25, 20, 23, 32, 17, 15, 24], 'cur_cost': 24821.0}]
2025-08-05 09:51:59,583 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:51:59,583 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 202, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 202, 'cache_hits': 0, 'similarity_calculations': 909, 'cache_hit_rate': 0.0, 'cache_size': 909}}
2025-08-05 09:51:59,584 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([31, 24,  0, 25, 23, 11,  9, 10, 22,  8, 19,  3,  2, 26, 30, 21, 12,
       27,  7, 29, 13, 15,  6, 16, 18, 20, 14, 17, 32,  5,  1,  4, 28],
      dtype=int64), 'cur_cost': 47456.0, 'intermediate_solutions': [{'tour': array([11, 31, 10, 23,  2, 14,  6, 25, 22,  4,  1,  7, 26,  0, 30, 27, 19,
       32,  9, 28, 15, 13, 17, 18,  3, 21, 24,  8, 20, 12, 16, 29,  5]), 'cur_cost': 41178.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([23, 11, 31, 10,  2, 14,  6, 25, 22,  4,  1,  7, 26,  0, 30, 27, 19,
       32,  9, 28, 15, 13, 17, 18,  3, 21, 24,  8, 20, 12, 16, 29,  5]), 'cur_cost': 41195.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 2, 23, 11, 31, 10, 14,  6, 25, 22,  4,  1,  7, 26,  0, 30, 27, 19,
       32,  9, 28, 15, 13, 17, 18,  3, 21, 24,  8, 20, 12, 16, 29,  5]), 'cur_cost': 38444.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([10, 23, 11, 31,  2, 14,  6, 25, 22,  4,  1,  7, 26,  0, 30, 27, 19,
       32,  9, 28, 15, 13, 17, 18,  3, 21, 24,  8, 20, 12, 16, 29,  5]), 'cur_cost': 41227.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([10,  2, 23, 11, 31, 14,  6, 25, 22,  4,  1,  7, 26,  0, 30, 27, 19,
       32,  9, 28, 15, 13, 17, 18,  3, 21, 24,  8, 20, 12, 16, 29,  5]), 'cur_cost': 41177.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:59,584 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 47456.00)
2025-08-05 09:51:59,584 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:51:59,584 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:51:59,584 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:59,586 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 33
2025-08-05 09:51:59,586 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,586 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,587 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,587 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:59,587 - ExplorationExpert - INFO - 探索路径生成完成，成本: 23528.0, 路径长度: 33, 收集中间解: 3
2025-08-05 09:51:59,587 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [29, 30, 3, 2, 26, 5, 27, 32, 4, 9, 17, 10, 19, 12, 16, 11, 14, 8, 6, 28, 1, 31, 13, 22, 24, 21, 25, 7, 18, 15, 0, 23, 20], 'cur_cost': 23528.0, 'intermediate_solutions': [{'tour': [5, 31, 2, 1, 30, 27, 4, 28, 9, 8, 6, 11, 10, 26, 29, 7, 18, 16, 19, 0, 13, 12, 3, 14, 22, 21, 25, 20, 23, 32, 17, 15, 24], 'cur_cost': 27567.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 31, 2, 1, 30, 27, 17, 32, 23, 20, 25, 21, 22, 14, 3, 12, 13, 18, 19, 16, 0, 7, 29, 26, 10, 11, 6, 8, 9, 28, 4, 15, 24], 'cur_cost': 26828.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 31, 2, 1, 30, 27, 4, 28, 9, 8, 0, 6, 11, 10, 26, 29, 7, 16, 19, 18, 13, 12, 3, 14, 22, 21, 25, 20, 23, 32, 17, 15, 24], 'cur_cost': 24822.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:59,588 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 23528.00)
2025-08-05 09:51:59,588 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:59,588 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:59,590 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([22, 24,  0,  7, 13, 32, 15, 30, 17,  2,  8,  3, 25, 23, 12, 27, 29,
       31, 19, 18, 14,  9,  6,  1, 21, 10, 20, 16,  5,  4, 11, 28, 26],
      dtype=int64), 'cur_cost': 42018.0, 'intermediate_solutions': [{'tour': array([29,  2,  6,  3,  4,  7, 13, 12, 31, 11, 28, 16, 17, 14,  9, 30,  8,
       26,  5, 10, 19, 27, 15, 22, 23, 20, 24, 21, 32,  0,  1, 18, 25]), 'cur_cost': 28584.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 3, 29,  2,  6,  4,  7, 13, 12, 31, 11, 28, 16, 17, 14,  9, 30,  8,
       26,  5, 10, 19, 27, 15, 22, 23, 20, 24, 21, 32,  0,  1, 18, 25]), 'cur_cost': 29288.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 4,  3, 29,  2,  6,  7, 13, 12, 31, 11, 28, 16, 17, 14,  9, 30,  8,
       26,  5, 10, 19, 27, 15, 22, 23, 20, 24, 21, 32,  0,  1, 18, 25]), 'cur_cost': 29280.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6,  3, 29,  2,  4,  7, 13, 12, 31, 11, 28, 16, 17, 14,  9, 30,  8,
       26,  5, 10, 19, 27, 15, 22, 23, 20, 24, 21, 32,  0,  1, 18, 25]), 'cur_cost': 29298.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6,  4,  3, 29,  2,  7, 13, 12, 31, 11, 28, 16, 17, 14,  9, 30,  8,
       26,  5, 10, 19, 27, 15, 22, 23, 20, 24, 21, 32,  0,  1, 18, 25]), 'cur_cost': 29288.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 22, 17, 10, 16, 11, 12, 13, 18, 14, 19, 15, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 21, 20, 23, 24, 25], 'cur_cost': 14949.0, 'intermediate_solutions': [{'tour': [0, 5, 22, 6, 1, 2, 3, 4, 8, 32, 9, 28, 26, 27, 7, 31, 30, 29, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13, 23, 20, 21, 25, 24], 'cur_cost': 16829.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 22, 6, 1, 2, 3, 4, 8, 7, 9, 28, 25, 21, 20, 23, 13, 14, 18, 15, 19, 10, 11, 16, 17, 12, 29, 30, 31, 32, 27, 26, 24], 'cur_cost': 20495.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 22, 6, 1, 2, 3, 4, 8, 7, 9, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 5, 10, 19, 15, 18, 14, 13, 23, 20, 21, 25, 24], 'cur_cost': 17648.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 13, 10, 16, 11, 17, 12, 18, 14, 19, 15, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 7, 9, 22, 20, 21, 25, 24, 23], 'cur_cost': 9646.0, 'intermediate_solutions': [{'tour': [1, 2, 4, 29, 5, 17, 15, 28, 9, 8, 31, 3, 27, 14, 11, 13, 19, 32, 12, 18, 30, 6, 10, 22, 24, 25, 20, 23, 26, 0, 7, 16, 21], 'cur_cost': 27213.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 2, 4, 29, 5, 17, 15, 28, 9, 8, 31, 3, 27, 22, 10, 6, 14, 18, 12, 32, 19, 13, 11, 30, 24, 25, 20, 23, 26, 0, 7, 16, 21], 'cur_cost': 32446.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 2, 4, 29, 5, 17, 15, 28, 9, 3, 8, 31, 27, 30, 11, 13, 19, 32, 12, 18, 14, 6, 10, 22, 24, 25, 20, 23, 26, 0, 7, 16, 21], 'cur_cost': 26109.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 25, 3, 10, 16, 11, 17, 12, 13, 18, 14, 19, 15, 32, 26, 27, 28, 29, 30, 31, 8, 4, 5, 9, 1, 2, 7, 6, 22, 20, 21, 23, 24], 'cur_cost': 15806.0, 'intermediate_solutions': [{'tour': [0, 10, 22, 2, 3, 4, 5, 9, 1, 6, 7, 8, 28, 26, 27, 32, 14, 30, 29, 12, 17, 16, 11, 19, 15, 18, 31, 13, 23, 20, 21, 25, 24], 'cur_cost': 21444.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 22, 5, 4, 3, 2, 9, 1, 6, 7, 8, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 19, 15, 18, 14, 13, 23, 20, 21, 25, 24], 'cur_cost': 16970.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 22, 2, 3, 4, 5, 9, 1, 6, 7, 8, 28, 26, 32, 31, 30, 29, 12, 27, 17, 16, 11, 19, 15, 18, 14, 13, 23, 20, 21, 25, 24], 'cur_cost': 19128.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 3, 8, 6, 26, 5, 29, 28, 12, 27, 7, 18, 14, 32, 30, 31, 11, 16, 13, 0, 2, 15, 19, 9, 23, 24, 21, 22, 25, 20, 17, 10], 'cur_cost': 20610.0, 'intermediate_solutions': [{'tour': [8, 27, 2, 3, 6, 0, 31, 7, 4, 17, 32, 26, 30, 16, 13, 19, 14, 15, 10, 28, 1, 29, 11, 22, 23, 24, 21, 20, 25, 9, 5, 12, 18], 'cur_cost': 18873.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 27, 2, 3, 6, 0, 31, 1, 4, 17, 10, 15, 14, 19, 13, 16, 30, 26, 32, 28, 7, 29, 11, 22, 23, 24, 21, 20, 25, 9, 5, 12, 18], 'cur_cost': 16689.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 27, 2, 3, 6, 0, 31, 1, 4, 17, 32, 26, 30, 16, 13, 19, 14, 15, 10, 28, 7, 29, 11, 22, 23, 24, 20, 25, 9, 5, 21, 12, 18], 'cur_cost': 24155.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 22, 19, 15, 18, 14, 13, 17, 12, 16, 11, 10, 32, 26, 27, 28, 29, 30, 31, 9, 5, 4, 3, 2, 1, 6, 7, 21, 20, 23, 24, 25], 'cur_cost': 14930.0, 'intermediate_solutions': [{'tour': [0, 12, 13, 14, 18, 19, 15, 10, 16, 11, 17, 32, 26, 27, 28, 29, 9, 31, 8, 4, 3, 2, 1, 6, 5, 30, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 11473.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 16, 10, 15, 19, 18, 14, 13, 12, 0, 17, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 11627.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 13, 14, 18, 19, 15, 10, 16, 11, 17, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 24, 5, 9, 7, 22, 20, 21, 25, 23], 'cur_cost': 15724.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([13, 16, 32,  8,  5, 24, 12,  3,  2, 17, 31, 28,  9, 22,  4, 23, 14,
       18, 10,  6, 20, 26, 25,  7, 27, 11,  1, 29, 21,  0, 19, 15, 30],
      dtype=int64), 'cur_cost': 51654.0, 'intermediate_solutions': [{'tour': array([15, 13,  3, 27, 25,  2,  9,  7, 18,  5,  6, 22, 16,  0, 32, 29,  4,
        1, 24, 21, 31, 20, 30, 28, 11, 10, 17, 19, 23, 14, 12, 26,  8]), 'cur_cost': 42784.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([27, 15, 13,  3, 25,  2,  9,  7, 18,  5,  6, 22, 16,  0, 32, 29,  4,
        1, 24, 21, 31, 20, 30, 28, 11, 10, 17, 19, 23, 14, 12, 26,  8]), 'cur_cost': 42710.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([25, 27, 15, 13,  3,  2,  9,  7, 18,  5,  6, 22, 16,  0, 32, 29,  4,
        1, 24, 21, 31, 20, 30, 28, 11, 10, 17, 19, 23, 14, 12, 26,  8]), 'cur_cost': 42039.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 3, 27, 15, 13, 25,  2,  9,  7, 18,  5,  6, 22, 16,  0, 32, 29,  4,
        1, 24, 21, 31, 20, 30, 28, 11, 10, 17, 19, 23, 14, 12, 26,  8]), 'cur_cost': 42026.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 3, 25, 27, 15, 13,  2,  9,  7, 18,  5,  6, 22, 16,  0, 32, 29,  4,
        1, 24, 21, 31, 20, 30, 28, 11, 10, 17, 19, 23, 14, 12, 26,  8]), 'cur_cost': 42051.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 3, 16, 11, 17, 12, 13, 18, 14, 19, 15, 10, 32, 26, 27, 28, 29, 30, 31, 8, 4, 5, 9, 1, 2, 7, 6, 22, 20, 23, 24, 25], 'cur_cost': 15675.0, 'intermediate_solutions': [{'tour': [22, 23, 31, 29, 26, 9, 30, 4, 0, 5, 32, 27, 21, 28, 13, 14, 11, 8, 16, 18, 10, 15, 19, 12, 3, 1, 6, 2, 7, 17, 20, 25, 24], 'cur_cost': 21203.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 23, 31, 29, 26, 9, 30, 4, 0, 5, 32, 27, 17, 28, 13, 14, 11, 8, 16, 18, 10, 15, 19, 12, 3, 1, 6, 20, 21, 7, 2, 25, 24], 'cur_cost': 21857.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 23, 12, 31, 29, 26, 9, 30, 4, 0, 5, 32, 27, 17, 28, 13, 14, 11, 8, 16, 18, 10, 15, 19, 3, 1, 6, 2, 7, 21, 20, 25, 24], 'cur_cost': 17869.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([31, 24,  0, 25, 23, 11,  9, 10, 22,  8, 19,  3,  2, 26, 30, 21, 12,
       27,  7, 29, 13, 15,  6, 16, 18, 20, 14, 17, 32,  5,  1,  4, 28],
      dtype=int64), 'cur_cost': 47456.0, 'intermediate_solutions': [{'tour': array([11, 31, 10, 23,  2, 14,  6, 25, 22,  4,  1,  7, 26,  0, 30, 27, 19,
       32,  9, 28, 15, 13, 17, 18,  3, 21, 24,  8, 20, 12, 16, 29,  5]), 'cur_cost': 41178.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([23, 11, 31, 10,  2, 14,  6, 25, 22,  4,  1,  7, 26,  0, 30, 27, 19,
       32,  9, 28, 15, 13, 17, 18,  3, 21, 24,  8, 20, 12, 16, 29,  5]), 'cur_cost': 41195.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 2, 23, 11, 31, 10, 14,  6, 25, 22,  4,  1,  7, 26,  0, 30, 27, 19,
       32,  9, 28, 15, 13, 17, 18,  3, 21, 24,  8, 20, 12, 16, 29,  5]), 'cur_cost': 38444.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([10, 23, 11, 31,  2, 14,  6, 25, 22,  4,  1,  7, 26,  0, 30, 27, 19,
       32,  9, 28, 15, 13, 17, 18,  3, 21, 24,  8, 20, 12, 16, 29,  5]), 'cur_cost': 41227.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([10,  2, 23, 11, 31, 14,  6, 25, 22,  4,  1,  7, 26,  0, 30, 27, 19,
       32,  9, 28, 15, 13, 17, 18,  3, 21, 24,  8, 20, 12, 16, 29,  5]), 'cur_cost': 41177.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [29, 30, 3, 2, 26, 5, 27, 32, 4, 9, 17, 10, 19, 12, 16, 11, 14, 8, 6, 28, 1, 31, 13, 22, 24, 21, 25, 7, 18, 15, 0, 23, 20], 'cur_cost': 23528.0, 'intermediate_solutions': [{'tour': [5, 31, 2, 1, 30, 27, 4, 28, 9, 8, 6, 11, 10, 26, 29, 7, 18, 16, 19, 0, 13, 12, 3, 14, 22, 21, 25, 20, 23, 32, 17, 15, 24], 'cur_cost': 27567.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 31, 2, 1, 30, 27, 17, 32, 23, 20, 25, 21, 22, 14, 3, 12, 13, 18, 19, 16, 0, 7, 29, 26, 10, 11, 6, 8, 9, 28, 4, 15, 24], 'cur_cost': 26828.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 31, 2, 1, 30, 27, 4, 28, 9, 8, 0, 6, 11, 10, 26, 29, 7, 16, 19, 18, 13, 12, 3, 14, 22, 21, 25, 20, 23, 32, 17, 15, 24], 'cur_cost': 24822.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:51:59,590 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:59,590 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:59,593 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9646.000, 多样性=0.906
2025-08-05 09:51:59,593 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 09:51:59,593 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 09:51:59,593 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:59,595 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.09928248418884407, 'best_improvement': -0.0040595399188092015}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.048124557678697846}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.04547536411026324, 'recent_improvements': [-0.009595676195437474, 0.04467159918127275, 0.08135505202508904], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 17, 'new_count': 17, 'count_change': 0, 'old_best_cost': 8761, 'new_best_cost': 8761, 'quality_improvement': 0.0, 'old_diversity': 0.7154634581105169, 'new_diversity': 0.7154634581105169, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:51:59,597 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 09:51:59,597 - __main__ - INFO - composite4_33 开始进化第 4 代
2025-08-05 09:51:59,597 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 09:51:59,597 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:59,598 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9646.000, 多样性=0.906
2025-08-05 09:51:59,598 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:59,600 - PathExpert - INFO - 路径结构分析完成: 公共边数量=8, 路径相似性=0.906
2025-08-05 09:51:59,600 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:59,606 - EliteExpert - INFO - 精英解分析完成: 精英解数量=17, 多样性=0.715
2025-08-05 09:51:59,608 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 09:51:59,608 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:59,608 - LandscapeExpert - INFO - 添加精英解数据: 17个精英解
2025-08-05 09:51:59,608 - LandscapeExpert - INFO - 数据提取成功: 27个路径, 27个适应度值
