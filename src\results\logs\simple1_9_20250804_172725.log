2025-08-04 17:27:25,624 - __main__ - INFO - simple1_9 开始进化第 1 代
2025-08-04 17:27:25,626 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-04 17:27:25,627 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:27:25,629 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=681.000, 多样性=0.894
2025-08-04 17:27:25,630 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:27:25,632 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.894
2025-08-04 17:27:25,633 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:27:25,654 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-04 17:27:25,655 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:27:25,655 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-04 17:27:25,655 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-04 17:27:25,949 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -26.300, 聚类评分: 0.000, 覆盖率: 0.001, 收敛趋势: 0.000, 多样性: 0.804
2025-08-04 17:27:25,950 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-04 17:27:25,950 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-04 17:27:25,950 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:27:25,987 - visualization.landscape_visualizer - INFO - 插值约束: 67 个点被约束到最小值 681.00
2025-08-04 17:27:26,349 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记，坐标系统已统一
2025-08-04 17:27:30,226 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_1_20250804_172730.html
2025-08-04 17:27:30,260 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_1_20250804_172730.html
2025-08-04 17:27:30,260 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-04 17:27:30,260 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-04 17:27:30,261 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 4.6067秒
2025-08-04 17:27:30,261 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-04 17:27:30,261 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -26.299999999999994, 'local_optima_density': 0.1, 'gradient_variance': 37017.882000000005, 'cluster_count': 0}, 'population_state': {'diversity': 0.8044444444444444, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.001, 'fitness_entropy': 0.8983289127685206, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -26.300)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.001)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.804)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754299645.9506023, 'performance_metrics': {}}}
2025-08-04 17:27:30,263 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:27:30,263 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:27:30,264 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:27:30,264 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:27:30,265 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-04 17:27:30,265 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-04 17:27:30,265 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-04 17:27:30,266 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:27:30,266 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:27:30,267 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-04 17:27:30,267 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:27:30,267 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:27:30,267 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-04 17:27:30,268 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:27:30,268 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:27:30,268 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:30,279 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:27:30,279 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:30,426 - ExplorationExpert - INFO - 探索路径生成完成，成本: 887.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:27:30,426 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [8, 5, 7, 3, 4, 0, 1, 6, 2], 'cur_cost': 887.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:27:30,427 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 887.00)
2025-08-04 17:27:30,427 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:27:30,427 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:27:30,427 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:30,428 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:27:30,428 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:30,428 - ExplorationExpert - INFO - 探索路径生成完成，成本: 788.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:27:30,429 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [4, 8, 7, 6, 3, 5, 0, 1, 2], 'cur_cost': 788.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:27:30,429 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 788.00)
2025-08-04 17:27:30,429 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:27:30,429 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:27:30,429 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:30,430 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:27:30,431 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:30,431 - ExplorationExpert - INFO - 探索路径生成完成，成本: 958.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:27:30,431 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [1, 5, 4, 0, 2, 8, 3, 7, 6], 'cur_cost': 958.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:27:30,431 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 958.00)
2025-08-04 17:27:30,432 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:27:30,432 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:27:30,432 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:30,433 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:27:30,433 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:30,433 - ExplorationExpert - INFO - 探索路径生成完成，成本: 835.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:27:30,433 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 6, 3, 8, 4, 2, 7, 5, 1], 'cur_cost': 835.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:27:30,433 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 835.00)
2025-08-04 17:27:30,433 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:27:30,433 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:27:30,433 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:30,434 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:27:30,434 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:30,434 - ExplorationExpert - INFO - 探索路径生成完成，成本: 835.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:27:30,434 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [8, 3, 6, 0, 1, 5, 7, 2, 4], 'cur_cost': 835.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:27:30,435 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 835.00)
2025-08-04 17:27:30,435 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-04 17:27:30,435 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:27:30,461 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:27:30,462 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 1177.0
2025-08-04 17:27:31,602 - ExploitationExpert - INFO - res_population_num: 1
2025-08-04 17:27:31,603 - ExploitationExpert - INFO - res_population_costs: [680.0]
2025-08-04 17:27:31,603 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 17:27:31,603 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:27:31,603 - ExploitationExpert - INFO - populations: [{'tour': [8, 5, 7, 3, 4, 0, 1, 6, 2], 'cur_cost': 887.0}, {'tour': [4, 8, 7, 6, 3, 5, 0, 1, 2], 'cur_cost': 788.0}, {'tour': [1, 5, 4, 0, 2, 8, 3, 7, 6], 'cur_cost': 958.0}, {'tour': [0, 6, 3, 8, 4, 2, 7, 5, 1], 'cur_cost': 835.0}, {'tour': [8, 3, 6, 0, 1, 5, 7, 2, 4], 'cur_cost': 835.0}, {'tour': array([0, 8, 6, 2, 5, 1, 4, 3, 7], dtype=int64), 'cur_cost': 1177.0}, {'tour': array([0, 3, 4, 5, 8, 6, 2, 7, 1], dtype=int64), 'cur_cost': 1113.0}, {'tour': array([4, 0, 1, 8, 2, 3, 5, 6, 7], dtype=int64), 'cur_cost': 860.0}, {'tour': array([5, 7, 4, 0, 2, 8, 6, 3, 1], dtype=int64), 'cur_cost': 1048.0}, {'tour': array([7, 3, 1, 2, 5, 4, 0, 8, 6], dtype=int64), 'cur_cost': 1121.0}]
2025-08-04 17:27:31,605 - ExploitationExpert - INFO - 局部搜索耗时: 1.14秒，最大迭代次数: 10
2025-08-04 17:27:31,605 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-04 17:27:31,605 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([0, 8, 6, 2, 5, 1, 4, 3, 7], dtype=int64), 'cur_cost': 1177.0, 'intermediate_solutions': [{'tour': array([5, 6, 4, 2, 1, 7, 8, 0, 3], dtype=int64), 'cur_cost': 946.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 5, 6, 4, 1, 7, 8, 0, 3], dtype=int64), 'cur_cost': 1120.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 2, 5, 6, 4, 7, 8, 0, 3], dtype=int64), 'cur_cost': 1136.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 2, 5, 6, 1, 7, 8, 0, 3], dtype=int64), 'cur_cost': 1010.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 1, 2, 5, 6, 7, 8, 0, 3], dtype=int64), 'cur_cost': 1037.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:27:31,606 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1177.00)
2025-08-04 17:27:31,606 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:27:31,606 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:27:31,607 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:31,607 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:27:31,608 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:31,608 - ExplorationExpert - INFO - 探索路径生成完成，成本: 764.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:27:31,608 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [1, 7, 5, 6, 3, 8, 4, 2, 0], 'cur_cost': 764.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:27:31,608 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 764.00)
2025-08-04 17:27:31,608 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:27:31,608 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:27:31,609 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:31,609 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:27:31,609 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:31,609 - ExplorationExpert - INFO - 探索路径生成完成，成本: 806.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:27:31,610 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 1, 7, 3, 8, 5, 6, 4, 2], 'cur_cost': 806.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:27:31,610 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 806.00)
2025-08-04 17:27:31,610 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:27:31,610 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:27:31,610 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:31,611 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:27:31,611 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:31,611 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1028.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:27:31,611 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [3, 5, 0, 2, 4, 7, 6, 8, 1], 'cur_cost': 1028.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:27:31,612 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1028.00)
2025-08-04 17:27:31,612 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-04 17:27:31,612 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:27:31,612 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:27:31,612 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 911.0
2025-08-04 17:27:33,321 - ExploitationExpert - INFO - res_population_num: 2
2025-08-04 17:27:33,321 - ExploitationExpert - INFO - res_population_costs: [680.0, 680]
2025-08-04 17:27:33,322 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-04 17:27:33,322 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:27:33,323 - ExploitationExpert - INFO - populations: [{'tour': [8, 5, 7, 3, 4, 0, 1, 6, 2], 'cur_cost': 887.0}, {'tour': [4, 8, 7, 6, 3, 5, 0, 1, 2], 'cur_cost': 788.0}, {'tour': [1, 5, 4, 0, 2, 8, 3, 7, 6], 'cur_cost': 958.0}, {'tour': [0, 6, 3, 8, 4, 2, 7, 5, 1], 'cur_cost': 835.0}, {'tour': [8, 3, 6, 0, 1, 5, 7, 2, 4], 'cur_cost': 835.0}, {'tour': array([0, 8, 6, 2, 5, 1, 4, 3, 7], dtype=int64), 'cur_cost': 1177.0}, {'tour': [1, 7, 5, 6, 3, 8, 4, 2, 0], 'cur_cost': 764.0}, {'tour': [0, 1, 7, 3, 8, 5, 6, 4, 2], 'cur_cost': 806.0}, {'tour': [3, 5, 0, 2, 4, 7, 6, 8, 1], 'cur_cost': 1028.0}, {'tour': array([7, 1, 6, 8, 4, 2, 0, 5, 3], dtype=int64), 'cur_cost': 911.0}]
2025-08-04 17:27:33,324 - ExploitationExpert - INFO - 局部搜索耗时: 1.71秒，最大迭代次数: 10
2025-08-04 17:27:33,324 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-04 17:27:33,324 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([7, 1, 6, 8, 4, 2, 0, 5, 3], dtype=int64), 'cur_cost': 911.0, 'intermediate_solutions': [{'tour': array([1, 3, 7, 2, 5, 4, 0, 8, 6], dtype=int64), 'cur_cost': 1151.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 1, 3, 7, 5, 4, 0, 8, 6], dtype=int64), 'cur_cost': 1127.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 2, 1, 3, 7, 4, 0, 8, 6], dtype=int64), 'cur_cost': 1062.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 2, 1, 3, 5, 4, 0, 8, 6], dtype=int64), 'cur_cost': 1080.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 5, 2, 1, 3, 4, 0, 8, 6], dtype=int64), 'cur_cost': 1111.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:27:33,325 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 911.00)
2025-08-04 17:27:33,325 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:27:33,325 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:27:33,326 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [8, 5, 7, 3, 4, 0, 1, 6, 2], 'cur_cost': 887.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [4, 8, 7, 6, 3, 5, 0, 1, 2], 'cur_cost': 788.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [1, 5, 4, 0, 2, 8, 3, 7, 6], 'cur_cost': 958.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 3, 8, 4, 2, 7, 5, 1], 'cur_cost': 835.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [8, 3, 6, 0, 1, 5, 7, 2, 4], 'cur_cost': 835.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 8, 6, 2, 5, 1, 4, 3, 7], dtype=int64), 'cur_cost': 1177.0, 'intermediate_solutions': [{'tour': array([5, 6, 4, 2, 1, 7, 8, 0, 3], dtype=int64), 'cur_cost': 946.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 5, 6, 4, 1, 7, 8, 0, 3], dtype=int64), 'cur_cost': 1120.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 2, 5, 6, 4, 7, 8, 0, 3], dtype=int64), 'cur_cost': 1136.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 2, 5, 6, 1, 7, 8, 0, 3], dtype=int64), 'cur_cost': 1010.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 1, 2, 5, 6, 7, 8, 0, 3], dtype=int64), 'cur_cost': 1037.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 5, 6, 3, 8, 4, 2, 0], 'cur_cost': 764.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 7, 3, 8, 5, 6, 4, 2], 'cur_cost': 806.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [3, 5, 0, 2, 4, 7, 6, 8, 1], 'cur_cost': 1028.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 1, 6, 8, 4, 2, 0, 5, 3], dtype=int64), 'cur_cost': 911.0, 'intermediate_solutions': [{'tour': array([1, 3, 7, 2, 5, 4, 0, 8, 6], dtype=int64), 'cur_cost': 1151.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 1, 3, 7, 5, 4, 0, 8, 6], dtype=int64), 'cur_cost': 1127.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 2, 1, 3, 7, 4, 0, 8, 6], dtype=int64), 'cur_cost': 1062.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 2, 1, 3, 5, 4, 0, 8, 6], dtype=int64), 'cur_cost': 1080.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 5, 2, 1, 3, 4, 0, 8, 6], dtype=int64), 'cur_cost': 1111.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-04 17:27:33,328 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:27:33,328 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:27:33,329 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=764.000, 多样性=0.894
2025-08-04 17:27:33,329 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-04 17:27:33,329 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-04 17:27:33,330 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:27:33,330 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.03511496228075444, 'best_improvement': -0.12187958883994127}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 1.2421003452298022e-16}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.4444444444444444, 'new_diversity': 0.4444444444444444, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-04 17:27:33,335 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-04 17:27:33,335 - __main__ - INFO - simple1_9 开始进化第 2 代
2025-08-04 17:27:33,335 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-04 17:27:33,336 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:27:33,336 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=764.000, 多样性=0.894
2025-08-04 17:27:33,336 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:27:33,337 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.894
2025-08-04 17:27:33,337 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:27:33,338 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.444
2025-08-04 17:27:33,339 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-04 17:27:33,339 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:27:33,339 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-04 17:27:33,339 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-04 17:27:33,345 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.250, 适应度梯度: 2.967, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 0.658
2025-08-04 17:27:33,345 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-04 17:27:33,345 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:27:33,346 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:27:33,349 - visualization.landscape_visualizer - INFO - 插值约束: 243 个点被约束到最小值 680.00
2025-08-04 17:27:33,353 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:27:33,428 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_2_20250804_172733.html
2025-08-04 17:27:33,467 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_2_20250804_172733.html
2025-08-04 17:27:33,467 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 2
2025-08-04 17:27:33,467 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-04 17:27:33,467 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1276秒
2025-08-04 17:27:33,468 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.25, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 2.966666666666671, 'local_optima_density': 0.25, 'gradient_variance': 29490.985555555555, 'cluster_count': 0}, 'population_state': {'diversity': 0.6578282828282828, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0022, 'fitness_entropy': 0.972765278018163, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 2.967)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754299653.3453927, 'performance_metrics': {}}}
2025-08-04 17:27:33,468 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:27:33,469 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:27:33,469 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:27:33,469 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:27:33,469 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-04 17:27:33,470 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-04 17:27:33,470 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-04 17:27:33,470 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:27:33,470 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:27:33,471 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-04 17:27:33,471 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:27:33,471 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:27:33,471 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 6} (总数: 2, 保护比例: 0.20)
2025-08-04 17:27:33,472 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:27:33,472 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:27:33,472 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:33,472 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:27:33,473 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,473 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,473 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,473 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,474 - ExplorationExpert - INFO - 探索路径生成完成，成本: 829.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:27:33,474 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [7, 0, 4, 2, 8, 5, 3, 6, 1], 'cur_cost': 829.0, 'intermediate_solutions': [{'tour': [8, 5, 7, 4, 3, 0, 1, 6, 2], 'cur_cost': 960.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 5, 7, 3, 4, 0, 2, 6, 1], 'cur_cost': 1047.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 2, 5, 7, 3, 4, 0, 1, 6], 'cur_cost': 912.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:27:33,474 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 829.00)
2025-08-04 17:27:33,474 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:27:33,475 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:27:33,475 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:33,475 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:27:33,475 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,475 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,476 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,476 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,476 - ExplorationExpert - INFO - 探索路径生成完成，成本: 842.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:27:33,476 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [3, 5, 6, 8, 7, 4, 2, 0, 1], 'cur_cost': 842.0, 'intermediate_solutions': [{'tour': [0, 8, 7, 6, 3, 5, 4, 1, 2], 'cur_cost': 980.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 5, 3, 6, 7, 8, 0, 1, 2], 'cur_cost': 844.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 8, 7, 6, 3, 5, 2, 0, 1], 'cur_cost': 874.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:27:33,477 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 842.00)
2025-08-04 17:27:33,477 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:27:33,477 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:27:33,477 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:33,477 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:27:33,478 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,478 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,478 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,478 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,478 - ExplorationExpert - INFO - 探索路径生成完成，成本: 812.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:27:33,479 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [4, 7, 5, 6, 0, 1, 2, 8, 3], 'cur_cost': 812.0, 'intermediate_solutions': [{'tour': [1, 3, 4, 0, 2, 8, 5, 7, 6], 'cur_cost': 968.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 5, 4, 0, 2, 3, 8, 7, 6], 'cur_cost': 1033.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 0, 2, 8, 3, 7, 6, 5], 'cur_cost': 874.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:27:33,479 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 812.00)
2025-08-04 17:27:33,479 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:27:33,479 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:27:33,480 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:33,480 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:27:33,480 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,481 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,481 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,481 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,481 - ExplorationExpert - INFO - 探索路径生成完成，成本: 897.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:27:33,482 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [1, 7, 3, 6, 0, 5, 8, 4, 2], 'cur_cost': 897.0, 'intermediate_solutions': [{'tour': [0, 4, 3, 8, 6, 2, 7, 5, 1], 'cur_cost': 1023.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 3, 8, 2, 4, 7, 5, 1], 'cur_cost': 804.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 0, 6, 3, 8, 4, 2, 5, 1], 'cur_cost': 973.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:27:33,482 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 897.00)
2025-08-04 17:27:33,483 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:27:33,483 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:27:33,483 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:33,483 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:27:33,484 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,484 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,484 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,484 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,484 - ExplorationExpert - INFO - 探索路径生成完成，成本: 822.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:27:33,485 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [7, 5, 6, 3, 4, 2, 8, 0, 1], 'cur_cost': 822.0, 'intermediate_solutions': [{'tour': [8, 3, 5, 0, 1, 6, 7, 2, 4], 'cur_cost': 791.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 3, 5, 1, 0, 6, 7, 2, 4], 'cur_cost': 807.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 3, 6, 0, 1, 5, 2, 4, 7], 'cur_cost': 892.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:27:33,485 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 822.00)
2025-08-04 17:27:33,485 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-04 17:27:33,485 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:27:33,486 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:27:33,486 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 1001.0
2025-08-04 17:27:33,558 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:27:33,558 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680]
2025-08-04 17:27:33,558 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:27:33,559 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:27:33,559 - ExploitationExpert - INFO - populations: [{'tour': [7, 0, 4, 2, 8, 5, 3, 6, 1], 'cur_cost': 829.0}, {'tour': [3, 5, 6, 8, 7, 4, 2, 0, 1], 'cur_cost': 842.0}, {'tour': [4, 7, 5, 6, 0, 1, 2, 8, 3], 'cur_cost': 812.0}, {'tour': [1, 7, 3, 6, 0, 5, 8, 4, 2], 'cur_cost': 897.0}, {'tour': [7, 5, 6, 3, 4, 2, 8, 0, 1], 'cur_cost': 822.0}, {'tour': array([3, 7, 4, 5, 6, 8, 1, 0, 2], dtype=int64), 'cur_cost': 1001.0}, {'tour': [1, 7, 5, 6, 3, 8, 4, 2, 0], 'cur_cost': 764.0}, {'tour': [0, 1, 7, 3, 8, 5, 6, 4, 2], 'cur_cost': 806.0}, {'tour': [3, 5, 0, 2, 4, 7, 6, 8, 1], 'cur_cost': 1028.0}, {'tour': [7, 1, 6, 8, 4, 2, 0, 5, 3], 'cur_cost': 911.0}]
2025-08-04 17:27:33,561 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒，最大迭代次数: 10
2025-08-04 17:27:33,561 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-04 17:27:33,562 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([3, 7, 4, 5, 6, 8, 1, 0, 2], dtype=int64), 'cur_cost': 1001.0, 'intermediate_solutions': [{'tour': array([6, 8, 0, 2, 5, 1, 4, 3, 7]), 'cur_cost': 1112.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 6, 8, 0, 5, 1, 4, 3, 7]), 'cur_cost': 1178.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 2, 6, 8, 0, 1, 4, 3, 7]), 'cur_cost': 1041.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 2, 6, 8, 5, 1, 4, 3, 7]), 'cur_cost': 1093.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 5, 2, 6, 8, 1, 4, 3, 7]), 'cur_cost': 1178.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:27:33,562 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1001.00)
2025-08-04 17:27:33,562 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:27:33,563 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:27:33,563 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:33,564 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:27:33,564 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,564 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,565 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,565 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,565 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1102.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:27:33,566 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [4, 0, 3, 7, 1, 2, 6, 8, 5], 'cur_cost': 1102.0, 'intermediate_solutions': [{'tour': [1, 7, 5, 2, 3, 8, 4, 6, 0], 'cur_cost': 980.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 4, 8, 3, 6, 5, 7, 1, 0], 'cur_cost': 764.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 5, 6, 3, 8, 4, 2, 0], 'cur_cost': 764.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:27:33,566 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 1102.00)
2025-08-04 17:27:33,567 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:27:33,567 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:27:33,567 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:33,567 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:27:33,568 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,568 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,568 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,568 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,569 - ExplorationExpert - INFO - 探索路径生成完成，成本: 943.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:27:33,569 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [1, 0, 5, 7, 4, 3, 8, 2, 6], 'cur_cost': 943.0, 'intermediate_solutions': [{'tour': [0, 1, 3, 7, 8, 5, 6, 4, 2], 'cur_cost': 854.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 7, 3, 4, 6, 5, 8, 2], 'cur_cost': 894.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 7, 3, 8, 5, 6, 4, 2], 'cur_cost': 776.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:27:33,569 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 943.00)
2025-08-04 17:27:33,569 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-04 17:27:33,570 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:27:33,570 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:27:33,570 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 987.0
2025-08-04 17:27:33,641 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:27:33,641 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680]
2025-08-04 17:27:33,642 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:27:33,643 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:27:33,643 - ExploitationExpert - INFO - populations: [{'tour': [7, 0, 4, 2, 8, 5, 3, 6, 1], 'cur_cost': 829.0}, {'tour': [3, 5, 6, 8, 7, 4, 2, 0, 1], 'cur_cost': 842.0}, {'tour': [4, 7, 5, 6, 0, 1, 2, 8, 3], 'cur_cost': 812.0}, {'tour': [1, 7, 3, 6, 0, 5, 8, 4, 2], 'cur_cost': 897.0}, {'tour': [7, 5, 6, 3, 4, 2, 8, 0, 1], 'cur_cost': 822.0}, {'tour': array([3, 7, 4, 5, 6, 8, 1, 0, 2], dtype=int64), 'cur_cost': 1001.0}, {'tour': [4, 0, 3, 7, 1, 2, 6, 8, 5], 'cur_cost': 1102.0}, {'tour': [1, 0, 5, 7, 4, 3, 8, 2, 6], 'cur_cost': 943.0}, {'tour': array([6, 0, 4, 2, 7, 5, 1, 3, 8], dtype=int64), 'cur_cost': 987.0}, {'tour': [7, 1, 6, 8, 4, 2, 0, 5, 3], 'cur_cost': 911.0}]
2025-08-04 17:27:33,644 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:27:33,644 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-04 17:27:33,645 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([6, 0, 4, 2, 7, 5, 1, 3, 8], dtype=int64), 'cur_cost': 987.0, 'intermediate_solutions': [{'tour': array([0, 5, 3, 2, 4, 7, 6, 8, 1]), 'cur_cost': 928.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 0, 5, 3, 4, 7, 6, 8, 1]), 'cur_cost': 1067.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 2, 0, 5, 3, 7, 6, 8, 1]), 'cur_cost': 936.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 2, 0, 5, 4, 7, 6, 8, 1]), 'cur_cost': 1202.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 4, 2, 0, 5, 7, 6, 8, 1]), 'cur_cost': 1064.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:27:33,645 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 987.00)
2025-08-04 17:27:33,646 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:27:33,646 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:27:33,646 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:33,647 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:27:33,647 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,648 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,648 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,649 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,649 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1022.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:27:33,649 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [1, 5, 2, 3, 8, 7, 6, 0, 4], 'cur_cost': 1022.0, 'intermediate_solutions': [{'tour': [7, 1, 3, 8, 4, 2, 0, 5, 6], 'cur_cost': 924.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 1, 6, 8, 4, 2, 3, 5, 0], 'cur_cost': 957.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 1, 3, 6, 8, 4, 2, 0, 5], 'cur_cost': 1006.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:27:33,650 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1022.00)
2025-08-04 17:27:33,650 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:27:33,650 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:27:33,651 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [7, 0, 4, 2, 8, 5, 3, 6, 1], 'cur_cost': 829.0, 'intermediate_solutions': [{'tour': [8, 5, 7, 4, 3, 0, 1, 6, 2], 'cur_cost': 960.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 5, 7, 3, 4, 0, 2, 6, 1], 'cur_cost': 1047.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 2, 5, 7, 3, 4, 0, 1, 6], 'cur_cost': 912.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [3, 5, 6, 8, 7, 4, 2, 0, 1], 'cur_cost': 842.0, 'intermediate_solutions': [{'tour': [0, 8, 7, 6, 3, 5, 4, 1, 2], 'cur_cost': 980.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 5, 3, 6, 7, 8, 0, 1, 2], 'cur_cost': 844.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 8, 7, 6, 3, 5, 2, 0, 1], 'cur_cost': 874.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [4, 7, 5, 6, 0, 1, 2, 8, 3], 'cur_cost': 812.0, 'intermediate_solutions': [{'tour': [1, 3, 4, 0, 2, 8, 5, 7, 6], 'cur_cost': 968.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 5, 4, 0, 2, 3, 8, 7, 6], 'cur_cost': 1033.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 0, 2, 8, 3, 7, 6, 5], 'cur_cost': 874.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 3, 6, 0, 5, 8, 4, 2], 'cur_cost': 897.0, 'intermediate_solutions': [{'tour': [0, 4, 3, 8, 6, 2, 7, 5, 1], 'cur_cost': 1023.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 3, 8, 2, 4, 7, 5, 1], 'cur_cost': 804.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 0, 6, 3, 8, 4, 2, 5, 1], 'cur_cost': 973.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [7, 5, 6, 3, 4, 2, 8, 0, 1], 'cur_cost': 822.0, 'intermediate_solutions': [{'tour': [8, 3, 5, 0, 1, 6, 7, 2, 4], 'cur_cost': 791.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 3, 5, 1, 0, 6, 7, 2, 4], 'cur_cost': 807.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 3, 6, 0, 1, 5, 2, 4, 7], 'cur_cost': 892.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 7, 4, 5, 6, 8, 1, 0, 2], dtype=int64), 'cur_cost': 1001.0, 'intermediate_solutions': [{'tour': array([6, 8, 0, 2, 5, 1, 4, 3, 7]), 'cur_cost': 1112.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 6, 8, 0, 5, 1, 4, 3, 7]), 'cur_cost': 1178.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 2, 6, 8, 0, 1, 4, 3, 7]), 'cur_cost': 1041.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 2, 6, 8, 5, 1, 4, 3, 7]), 'cur_cost': 1093.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 5, 2, 6, 8, 1, 4, 3, 7]), 'cur_cost': 1178.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [4, 0, 3, 7, 1, 2, 6, 8, 5], 'cur_cost': 1102.0, 'intermediate_solutions': [{'tour': [1, 7, 5, 2, 3, 8, 4, 6, 0], 'cur_cost': 980.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 4, 8, 3, 6, 5, 7, 1, 0], 'cur_cost': 764.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 5, 6, 3, 8, 4, 2, 0], 'cur_cost': 764.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [1, 0, 5, 7, 4, 3, 8, 2, 6], 'cur_cost': 943.0, 'intermediate_solutions': [{'tour': [0, 1, 3, 7, 8, 5, 6, 4, 2], 'cur_cost': 854.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 7, 3, 4, 6, 5, 8, 2], 'cur_cost': 894.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 7, 3, 8, 5, 6, 4, 2], 'cur_cost': 776.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 0, 4, 2, 7, 5, 1, 3, 8], dtype=int64), 'cur_cost': 987.0, 'intermediate_solutions': [{'tour': array([0, 5, 3, 2, 4, 7, 6, 8, 1]), 'cur_cost': 928.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 0, 5, 3, 4, 7, 6, 8, 1]), 'cur_cost': 1067.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 2, 0, 5, 3, 7, 6, 8, 1]), 'cur_cost': 936.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 2, 0, 5, 4, 7, 6, 8, 1]), 'cur_cost': 1202.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 4, 2, 0, 5, 7, 6, 8, 1]), 'cur_cost': 1064.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [1, 5, 2, 3, 8, 7, 6, 0, 4], 'cur_cost': 1022.0, 'intermediate_solutions': [{'tour': [7, 1, 3, 8, 4, 2, 0, 5, 6], 'cur_cost': 924.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 1, 6, 8, 4, 2, 3, 5, 0], 'cur_cost': 957.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 1, 3, 6, 8, 4, 2, 0, 5], 'cur_cost': 1006.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:27:33,653 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:27:33,654 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:27:33,655 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=812.000, 多样性=0.869
2025-08-04 17:27:33,655 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-04 17:27:33,655 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-04 17:27:33,655 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:27:33,656 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.009640039034705197, 'best_improvement': -0.06282722513089005}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.02762430939226544}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-04 17:27:33,657 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-04 17:27:33,657 - __main__ - INFO - simple1_9 开始进化第 3 代
2025-08-04 17:27:33,657 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-04 17:27:33,658 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:27:33,658 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=812.000, 多样性=0.869
2025-08-04 17:27:33,658 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:27:33,659 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.869
2025-08-04 17:27:33,659 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:27:33,660 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.630
2025-08-04 17:27:33,661 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-04 17:27:33,661 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:27:33,662 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-04 17:27:33,662 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-04 17:27:33,669 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.308, 适应度梯度: -16.508, 聚类评分: 0.000, 覆盖率: 0.003, 收敛趋势: 0.000, 多样性: 0.603
2025-08-04 17:27:33,669 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-04 17:27:33,670 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:27:33,670 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:27:33,710 - visualization.landscape_visualizer - INFO - 插值约束: 63 个点被约束到最小值 680.00
2025-08-04 17:27:33,714 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:27:33,793 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_3_20250804_172733.html
2025-08-04 17:27:33,837 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_3_20250804_172733.html
2025-08-04 17:27:33,838 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 3
2025-08-04 17:27:33,838 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-04 17:27:33,838 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1773秒
2025-08-04 17:27:33,838 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3076923076923077, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -16.5076923076923, 'local_optima_density': 0.3076923076923077, 'gradient_variance': 20879.48071005917, 'cluster_count': 0}, 'population_state': {'diversity': 0.6025641025641025, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0033, 'fitness_entropy': 0.9138311481971516, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -16.508)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.003)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754299653.6690366, 'performance_metrics': {}}}
2025-08-04 17:27:33,839 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:27:33,839 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:27:33,840 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:27:33,840 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:27:33,840 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'exploit']
2025-08-04 17:27:33,840 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-04 17:27:33,841 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'exploit']
2025-08-04 17:27:33,841 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:27:33,841 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:27:33,842 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'exploit']
2025-08-04 17:27:33,842 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:27:33,842 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:27:33,843 - experts.management.collaboration_manager - INFO - 识别精英个体: {2, 4} (总数: 2, 保护比例: 0.20)
2025-08-04 17:27:33,843 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:27:33,843 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:27:33,843 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:33,843 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:27:33,844 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,844 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,844 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,844 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,845 - ExplorationExpert - INFO - 探索路径生成完成，成本: 921.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:27:33,845 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 7, 1, 4, 2, 5, 3, 8, 6], 'cur_cost': 921.0, 'intermediate_solutions': [{'tour': [7, 1, 4, 2, 8, 5, 3, 6, 0], 'cur_cost': 829.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 3, 5, 8, 2, 4, 0, 7], 'cur_cost': 829.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 0, 5, 4, 2, 8, 3, 6, 1], 'cur_cost': 931.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:27:33,845 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 921.00)
2025-08-04 17:27:33,845 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:27:33,845 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:27:33,846 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:33,846 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:27:33,846 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,846 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,847 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,847 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,847 - ExplorationExpert - INFO - 探索路径生成完成，成本: 825.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:27:33,848 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [6, 0, 1, 7, 3, 5, 8, 4, 2], 'cur_cost': 825.0, 'intermediate_solutions': [{'tour': [3, 5, 6, 7, 8, 4, 2, 0, 1], 'cur_cost': 784.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 6, 7, 8, 4, 2, 0, 1], 'cur_cost': 784.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 6, 8, 7, 4, 5, 2, 0, 1], 'cur_cost': 1070.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:27:33,848 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 825.00)
2025-08-04 17:27:33,848 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:27:33,849 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:27:33,849 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:33,849 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:27:33,850 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,850 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,850 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,850 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,850 - ExplorationExpert - INFO - 探索路径生成完成，成本: 875.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:27:33,851 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [6, 0, 4, 8, 3, 5, 7, 2, 1], 'cur_cost': 875.0, 'intermediate_solutions': [{'tour': [4, 7, 6, 5, 0, 1, 2, 8, 3], 'cur_cost': 853.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 7, 5, 6, 0, 1, 2, 3, 8], 'cur_cost': 835.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 7, 5, 6, 0, 8, 1, 2, 3], 'cur_cost': 1053.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:27:33,851 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 875.00)
2025-08-04 17:27:33,851 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:27:33,851 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:27:33,852 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:33,852 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:27:33,852 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,852 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,852 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,853 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,853 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1009.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:27:33,853 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [4, 6, 0, 8, 7, 3, 5, 2, 1], 'cur_cost': 1009.0, 'intermediate_solutions': [{'tour': [5, 7, 3, 6, 0, 1, 8, 4, 2], 'cur_cost': 870.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 7, 3, 6, 0, 5, 8, 2, 4], 'cur_cost': 871.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 1, 7, 3, 6, 0, 5, 8, 4], 'cur_cost': 897.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:27:33,853 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1009.00)
2025-08-04 17:27:33,854 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:27:33,854 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:27:33,854 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:33,854 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:27:33,855 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,855 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,855 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,855 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:33,855 - ExplorationExpert - INFO - 探索路径生成完成，成本: 978.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:27:33,855 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 6, 3, 4, 2, 8, 7, 1, 5], 'cur_cost': 978.0, 'intermediate_solutions': [{'tour': [7, 5, 6, 8, 4, 2, 3, 0, 1], 'cur_cost': 872.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 6, 3, 4, 2, 1, 0, 8], 'cur_cost': 821.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 5, 6, 3, 4, 8, 0, 1, 2], 'cur_cost': 905.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:27:33,856 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 978.00)
2025-08-04 17:27:33,856 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-04 17:27:33,856 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:27:33,856 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:27:33,857 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 952.0
2025-08-04 17:27:33,927 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:27:33,927 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680]
2025-08-04 17:27:33,927 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:27:33,928 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:27:33,928 - ExploitationExpert - INFO - populations: [{'tour': [0, 7, 1, 4, 2, 5, 3, 8, 6], 'cur_cost': 921.0}, {'tour': [6, 0, 1, 7, 3, 5, 8, 4, 2], 'cur_cost': 825.0}, {'tour': [6, 0, 4, 8, 3, 5, 7, 2, 1], 'cur_cost': 875.0}, {'tour': [4, 6, 0, 8, 7, 3, 5, 2, 1], 'cur_cost': 1009.0}, {'tour': [0, 6, 3, 4, 2, 8, 7, 1, 5], 'cur_cost': 978.0}, {'tour': array([0, 2, 4, 7, 1, 5, 6, 3, 8], dtype=int64), 'cur_cost': 952.0}, {'tour': [4, 0, 3, 7, 1, 2, 6, 8, 5], 'cur_cost': 1102.0}, {'tour': [1, 0, 5, 7, 4, 3, 8, 2, 6], 'cur_cost': 943.0}, {'tour': [6, 0, 4, 2, 7, 5, 1, 3, 8], 'cur_cost': 987.0}, {'tour': [1, 5, 2, 3, 8, 7, 6, 0, 4], 'cur_cost': 1022.0}]
2025-08-04 17:27:33,929 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:27:33,929 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-04 17:27:33,930 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([0, 2, 4, 7, 1, 5, 6, 3, 8], dtype=int64), 'cur_cost': 952.0, 'intermediate_solutions': [{'tour': array([4, 7, 3, 5, 6, 8, 1, 0, 2]), 'cur_cost': 827.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 4, 7, 3, 6, 8, 1, 0, 2]), 'cur_cost': 1055.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 5, 4, 7, 3, 8, 1, 0, 2]), 'cur_cost': 979.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 5, 4, 7, 6, 8, 1, 0, 2]), 'cur_cost': 1014.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 6, 5, 4, 7, 8, 1, 0, 2]), 'cur_cost': 987.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:27:33,931 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 952.00)
2025-08-04 17:27:33,931 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-04 17:27:33,931 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:27:33,932 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:27:33,932 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1074.0
2025-08-04 17:27:34,007 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:27:34,007 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680]
2025-08-04 17:27:34,007 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:27:34,008 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:27:34,008 - ExploitationExpert - INFO - populations: [{'tour': [0, 7, 1, 4, 2, 5, 3, 8, 6], 'cur_cost': 921.0}, {'tour': [6, 0, 1, 7, 3, 5, 8, 4, 2], 'cur_cost': 825.0}, {'tour': [6, 0, 4, 8, 3, 5, 7, 2, 1], 'cur_cost': 875.0}, {'tour': [4, 6, 0, 8, 7, 3, 5, 2, 1], 'cur_cost': 1009.0}, {'tour': [0, 6, 3, 4, 2, 8, 7, 1, 5], 'cur_cost': 978.0}, {'tour': array([0, 2, 4, 7, 1, 5, 6, 3, 8], dtype=int64), 'cur_cost': 952.0}, {'tour': array([4, 3, 5, 0, 7, 6, 2, 1, 8], dtype=int64), 'cur_cost': 1074.0}, {'tour': [1, 0, 5, 7, 4, 3, 8, 2, 6], 'cur_cost': 943.0}, {'tour': [6, 0, 4, 2, 7, 5, 1, 3, 8], 'cur_cost': 987.0}, {'tour': [1, 5, 2, 3, 8, 7, 6, 0, 4], 'cur_cost': 1022.0}]
2025-08-04 17:27:34,009 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒，最大迭代次数: 10
2025-08-04 17:27:34,009 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-04 17:27:34,011 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([4, 3, 5, 0, 7, 6, 2, 1, 8], dtype=int64), 'cur_cost': 1074.0, 'intermediate_solutions': [{'tour': array([3, 0, 4, 7, 1, 2, 6, 8, 5]), 'cur_cost': 1056.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 3, 0, 4, 1, 2, 6, 8, 5]), 'cur_cost': 1013.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 7, 3, 0, 4, 2, 6, 8, 5]), 'cur_cost': 1043.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 7, 3, 0, 1, 2, 6, 8, 5]), 'cur_cost': 1029.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 1, 7, 3, 0, 2, 6, 8, 5]), 'cur_cost': 1113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:27:34,011 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1074.00)
2025-08-04 17:27:34,011 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:27:34,012 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:27:34,012 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:34,012 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:27:34,012 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,013 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,013 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,013 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,014 - ExplorationExpert - INFO - 探索路径生成完成，成本: 880.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:27:34,014 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [5, 8, 7, 0, 1, 6, 3, 4, 2], 'cur_cost': 880.0, 'intermediate_solutions': [{'tour': [1, 0, 5, 2, 4, 3, 8, 7, 6], 'cur_cost': 884.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 0, 5, 7, 4, 6, 2, 8, 3], 'cur_cost': 1017.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 5, 7, 6, 4, 3, 8, 2], 'cur_cost': 909.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:27:34,014 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 880.00)
2025-08-04 17:27:34,015 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:27:34,015 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:27:34,015 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:34,016 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:27:34,016 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,016 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,016 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,017 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,017 - ExplorationExpert - INFO - 探索路径生成完成，成本: 926.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:27:34,017 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 3, 4, 6, 7, 5, 8, 2, 1], 'cur_cost': 926.0, 'intermediate_solutions': [{'tour': [2, 0, 4, 6, 7, 5, 1, 3, 8], 'cur_cost': 1022.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 0, 4, 5, 7, 2, 1, 3, 8], 'cur_cost': 1046.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 4, 7, 2, 5, 1, 3, 8], 'cur_cost': 1128.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:27:34,017 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 926.00)
2025-08-04 17:27:34,018 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-04 17:27:34,018 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:27:34,018 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:27:34,018 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 1076.0
2025-08-04 17:27:34,092 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:27:34,092 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680]
2025-08-04 17:27:34,092 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:27:34,093 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:27:34,093 - ExploitationExpert - INFO - populations: [{'tour': [0, 7, 1, 4, 2, 5, 3, 8, 6], 'cur_cost': 921.0}, {'tour': [6, 0, 1, 7, 3, 5, 8, 4, 2], 'cur_cost': 825.0}, {'tour': [6, 0, 4, 8, 3, 5, 7, 2, 1], 'cur_cost': 875.0}, {'tour': [4, 6, 0, 8, 7, 3, 5, 2, 1], 'cur_cost': 1009.0}, {'tour': [0, 6, 3, 4, 2, 8, 7, 1, 5], 'cur_cost': 978.0}, {'tour': array([0, 2, 4, 7, 1, 5, 6, 3, 8], dtype=int64), 'cur_cost': 952.0}, {'tour': array([4, 3, 5, 0, 7, 6, 2, 1, 8], dtype=int64), 'cur_cost': 1074.0}, {'tour': [5, 8, 7, 0, 1, 6, 3, 4, 2], 'cur_cost': 880.0}, {'tour': [0, 3, 4, 6, 7, 5, 8, 2, 1], 'cur_cost': 926.0}, {'tour': array([7, 3, 1, 5, 0, 8, 4, 2, 6], dtype=int64), 'cur_cost': 1076.0}]
2025-08-04 17:27:34,094 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒，最大迭代次数: 10
2025-08-04 17:27:34,094 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-04 17:27:34,094 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([7, 3, 1, 5, 0, 8, 4, 2, 6], dtype=int64), 'cur_cost': 1076.0, 'intermediate_solutions': [{'tour': array([2, 5, 1, 3, 8, 7, 6, 0, 4]), 'cur_cost': 986.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 2, 5, 1, 8, 7, 6, 0, 4]), 'cur_cost': 1134.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 3, 2, 5, 1, 7, 6, 0, 4]), 'cur_cost': 1049.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 3, 2, 5, 8, 7, 6, 0, 4]), 'cur_cost': 1042.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 8, 3, 2, 5, 7, 6, 0, 4]), 'cur_cost': 1017.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:27:34,095 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1076.00)
2025-08-04 17:27:34,095 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:27:34,095 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:27:34,097 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 1, 4, 2, 5, 3, 8, 6], 'cur_cost': 921.0, 'intermediate_solutions': [{'tour': [7, 1, 4, 2, 8, 5, 3, 6, 0], 'cur_cost': 829.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 3, 5, 8, 2, 4, 0, 7], 'cur_cost': 829.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 0, 5, 4, 2, 8, 3, 6, 1], 'cur_cost': 931.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [6, 0, 1, 7, 3, 5, 8, 4, 2], 'cur_cost': 825.0, 'intermediate_solutions': [{'tour': [3, 5, 6, 7, 8, 4, 2, 0, 1], 'cur_cost': 784.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 6, 7, 8, 4, 2, 0, 1], 'cur_cost': 784.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 6, 8, 7, 4, 5, 2, 0, 1], 'cur_cost': 1070.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [6, 0, 4, 8, 3, 5, 7, 2, 1], 'cur_cost': 875.0, 'intermediate_solutions': [{'tour': [4, 7, 6, 5, 0, 1, 2, 8, 3], 'cur_cost': 853.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 7, 5, 6, 0, 1, 2, 3, 8], 'cur_cost': 835.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 7, 5, 6, 0, 8, 1, 2, 3], 'cur_cost': 1053.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [4, 6, 0, 8, 7, 3, 5, 2, 1], 'cur_cost': 1009.0, 'intermediate_solutions': [{'tour': [5, 7, 3, 6, 0, 1, 8, 4, 2], 'cur_cost': 870.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 7, 3, 6, 0, 5, 8, 2, 4], 'cur_cost': 871.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 1, 7, 3, 6, 0, 5, 8, 4], 'cur_cost': 897.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 3, 4, 2, 8, 7, 1, 5], 'cur_cost': 978.0, 'intermediate_solutions': [{'tour': [7, 5, 6, 8, 4, 2, 3, 0, 1], 'cur_cost': 872.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 6, 3, 4, 2, 1, 0, 8], 'cur_cost': 821.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 5, 6, 3, 4, 8, 0, 1, 2], 'cur_cost': 905.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 2, 4, 7, 1, 5, 6, 3, 8], dtype=int64), 'cur_cost': 952.0, 'intermediate_solutions': [{'tour': array([4, 7, 3, 5, 6, 8, 1, 0, 2]), 'cur_cost': 827.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 4, 7, 3, 6, 8, 1, 0, 2]), 'cur_cost': 1055.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 5, 4, 7, 3, 8, 1, 0, 2]), 'cur_cost': 979.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 5, 4, 7, 6, 8, 1, 0, 2]), 'cur_cost': 1014.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 6, 5, 4, 7, 8, 1, 0, 2]), 'cur_cost': 987.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 3, 5, 0, 7, 6, 2, 1, 8], dtype=int64), 'cur_cost': 1074.0, 'intermediate_solutions': [{'tour': array([3, 0, 4, 7, 1, 2, 6, 8, 5]), 'cur_cost': 1056.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 3, 0, 4, 1, 2, 6, 8, 5]), 'cur_cost': 1013.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 7, 3, 0, 4, 2, 6, 8, 5]), 'cur_cost': 1043.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 7, 3, 0, 1, 2, 6, 8, 5]), 'cur_cost': 1029.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 1, 7, 3, 0, 2, 6, 8, 5]), 'cur_cost': 1113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [5, 8, 7, 0, 1, 6, 3, 4, 2], 'cur_cost': 880.0, 'intermediate_solutions': [{'tour': [1, 0, 5, 2, 4, 3, 8, 7, 6], 'cur_cost': 884.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 0, 5, 7, 4, 6, 2, 8, 3], 'cur_cost': 1017.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 5, 7, 6, 4, 3, 8, 2], 'cur_cost': 909.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 4, 6, 7, 5, 8, 2, 1], 'cur_cost': 926.0, 'intermediate_solutions': [{'tour': [2, 0, 4, 6, 7, 5, 1, 3, 8], 'cur_cost': 1022.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 0, 4, 5, 7, 2, 1, 3, 8], 'cur_cost': 1046.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 4, 7, 2, 5, 1, 3, 8], 'cur_cost': 1128.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 3, 1, 5, 0, 8, 4, 2, 6], dtype=int64), 'cur_cost': 1076.0, 'intermediate_solutions': [{'tour': array([2, 5, 1, 3, 8, 7, 6, 0, 4]), 'cur_cost': 986.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 2, 5, 1, 8, 7, 6, 0, 4]), 'cur_cost': 1134.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 3, 2, 5, 1, 7, 6, 0, 4]), 'cur_cost': 1049.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 3, 2, 5, 8, 7, 6, 0, 4]), 'cur_cost': 1042.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 8, 3, 2, 5, 7, 6, 0, 4]), 'cur_cost': 1017.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-04 17:27:34,100 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:27:34,100 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:27:34,101 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=825.000, 多样性=0.857
2025-08-04 17:27:34,102 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-04 17:27:34,102 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-04 17:27:34,102 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:27:34,102 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.006798404208604594, 'best_improvement': -0.01600985221674877}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.01420454545454552}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:27:34,103 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-04 17:27:34,103 - __main__ - INFO - simple1_9 开始进化第 4 代
2025-08-04 17:27:34,103 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-04 17:27:34,103 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:27:34,104 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=825.000, 多样性=0.857
2025-08-04 17:27:34,104 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:27:34,105 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.857
2025-08-04 17:27:34,105 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:27:34,105 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.630
2025-08-04 17:27:34,107 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-04 17:27:34,107 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:27:34,107 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-04 17:27:34,107 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-04 17:27:34,114 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.308, 适应度梯度: -3.585, 聚类评分: 0.000, 覆盖率: 0.004, 收敛趋势: 0.000, 多样性: 0.593
2025-08-04 17:27:34,115 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-04 17:27:34,115 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:27:34,115 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:27:34,119 - visualization.landscape_visualizer - INFO - 插值约束: 81 个点被约束到最小值 680.00
2025-08-04 17:27:34,122 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:27:34,198 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_4_20250804_172734.html
2025-08-04 17:27:34,235 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_4_20250804_172734.html
2025-08-04 17:27:34,235 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 4
2025-08-04 17:27:34,235 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-04 17:27:34,235 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1277秒
2025-08-04 17:27:34,236 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3076923076923077, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -3.5846153846153888, 'local_optima_density': 0.3076923076923077, 'gradient_variance': 14791.399763313608, 'cluster_count': 0}, 'population_state': {'diversity': 0.5927021696252466, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0043, 'fitness_entropy': 0.9383574700386479, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -3.585)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.004)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754299654.1153314, 'performance_metrics': {}}}
2025-08-04 17:27:34,236 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:27:34,236 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:27:34,236 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:27:34,236 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:27:34,236 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-04 17:27:34,236 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-04 17:27:34,237 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-04 17:27:34,237 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:27:34,237 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:27:34,237 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-04 17:27:34,237 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:27:34,238 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:27:34,238 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2} (总数: 2, 保护比例: 0.20)
2025-08-04 17:27:34,238 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:27:34,238 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:27:34,239 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:34,239 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:27:34,239 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,239 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,240 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,240 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,240 - ExplorationExpert - INFO - 探索路径生成完成，成本: 829.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:27:34,240 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [2, 4, 0, 6, 5, 3, 7, 8, 1], 'cur_cost': 829.0, 'intermediate_solutions': [{'tour': [0, 7, 1, 4, 2, 3, 5, 8, 6], 'cur_cost': 918.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 3, 5, 2, 4, 1, 7, 6], 'cur_cost': 915.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 1, 4, 2, 0, 5, 3, 8, 6], 'cur_cost': 903.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:27:34,241 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 829.00)
2025-08-04 17:27:34,241 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:27:34,241 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:27:34,241 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:34,241 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:27:34,241 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,242 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,242 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,242 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,242 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1068.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:27:34,242 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [2, 3, 6, 8, 0, 7, 5, 4, 1], 'cur_cost': 1068.0, 'intermediate_solutions': [{'tour': [6, 0, 1, 7, 4, 5, 8, 3, 2], 'cur_cost': 1016.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 8, 5, 3, 7, 1, 0, 4, 2], 'cur_cost': 858.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 1, 8, 7, 3, 5, 4, 2], 'cur_cost': 910.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:27:34,243 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 1068.00)
2025-08-04 17:27:34,243 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:27:34,243 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:27:34,243 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:34,244 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:27:34,244 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,244 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,244 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,244 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,245 - ExplorationExpert - INFO - 探索路径生成完成，成本: 886.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:27:34,245 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [5, 8, 3, 6, 7, 0, 4, 2, 1], 'cur_cost': 886.0, 'intermediate_solutions': [{'tour': [6, 0, 4, 3, 8, 5, 7, 2, 1], 'cur_cost': 938.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 3, 8, 4, 0, 6, 7, 2, 1], 'cur_cost': 932.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 4, 8, 2, 3, 5, 7, 1], 'cur_cost': 903.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:27:34,245 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 886.00)
2025-08-04 17:27:34,245 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-04 17:27:34,246 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:27:34,246 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:27:34,246 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1057.0
2025-08-04 17:27:34,317 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:27:34,317 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680]
2025-08-04 17:27:34,317 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:27:34,318 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:27:34,318 - ExploitationExpert - INFO - populations: [{'tour': [2, 4, 0, 6, 5, 3, 7, 8, 1], 'cur_cost': 829.0}, {'tour': [2, 3, 6, 8, 0, 7, 5, 4, 1], 'cur_cost': 1068.0}, {'tour': [5, 8, 3, 6, 7, 0, 4, 2, 1], 'cur_cost': 886.0}, {'tour': array([2, 8, 0, 5, 3, 7, 1, 4, 6], dtype=int64), 'cur_cost': 1057.0}, {'tour': [0, 6, 3, 4, 2, 8, 7, 1, 5], 'cur_cost': 978.0}, {'tour': [0, 2, 4, 7, 1, 5, 6, 3, 8], 'cur_cost': 952.0}, {'tour': [4, 3, 5, 0, 7, 6, 2, 1, 8], 'cur_cost': 1074.0}, {'tour': [5, 8, 7, 0, 1, 6, 3, 4, 2], 'cur_cost': 880.0}, {'tour': [0, 3, 4, 6, 7, 5, 8, 2, 1], 'cur_cost': 926.0}, {'tour': [7, 3, 1, 5, 0, 8, 4, 2, 6], 'cur_cost': 1076.0}]
2025-08-04 17:27:34,319 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:27:34,319 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-08-04 17:27:34,321 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([2, 8, 0, 5, 3, 7, 1, 4, 6], dtype=int64), 'cur_cost': 1057.0, 'intermediate_solutions': [{'tour': array([0, 6, 4, 8, 7, 3, 5, 2, 1]), 'cur_cost': 903.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 0, 6, 4, 7, 3, 5, 2, 1]), 'cur_cost': 1086.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 8, 0, 6, 4, 3, 5, 2, 1]), 'cur_cost': 1088.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 8, 0, 6, 7, 3, 5, 2, 1]), 'cur_cost': 950.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 7, 8, 0, 6, 3, 5, 2, 1]), 'cur_cost': 994.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:27:34,321 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1057.00)
2025-08-04 17:27:34,321 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:27:34,321 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:27:34,321 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:34,321 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:27:34,321 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,321 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,322 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,322 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,322 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1123.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:27:34,322 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [4, 6, 5, 8, 0, 2, 7, 1, 3], 'cur_cost': 1123.0, 'intermediate_solutions': [{'tour': [0, 4, 3, 6, 2, 8, 7, 1, 5], 'cur_cost': 1100.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 3, 4, 2, 7, 8, 1, 5], 'cur_cost': 1059.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 0, 6, 3, 4, 2, 8, 7, 1], 'cur_cost': 978.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:27:34,323 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1123.00)
2025-08-04 17:27:34,323 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:27:34,323 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:27:34,323 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:34,323 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:27:34,324 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,324 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,324 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,324 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,324 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1026.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:27:34,325 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [4, 5, 0, 6, 2, 8, 3, 7, 1], 'cur_cost': 1026.0, 'intermediate_solutions': [{'tour': [0, 8, 4, 7, 1, 5, 6, 3, 2], 'cur_cost': 1063.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 6, 5, 1, 7, 4, 3, 8], 'cur_cost': 1107.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 7, 1, 5, 4, 6, 3, 8], 'cur_cost': 1154.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:27:34,325 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1026.00)
2025-08-04 17:27:34,325 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-04 17:27:34,325 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:27:34,325 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:27:34,326 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1110.0
2025-08-04 17:27:34,397 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:27:34,398 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680]
2025-08-04 17:27:34,398 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:27:34,399 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:27:34,399 - ExploitationExpert - INFO - populations: [{'tour': [2, 4, 0, 6, 5, 3, 7, 8, 1], 'cur_cost': 829.0}, {'tour': [2, 3, 6, 8, 0, 7, 5, 4, 1], 'cur_cost': 1068.0}, {'tour': [5, 8, 3, 6, 7, 0, 4, 2, 1], 'cur_cost': 886.0}, {'tour': array([2, 8, 0, 5, 3, 7, 1, 4, 6], dtype=int64), 'cur_cost': 1057.0}, {'tour': [4, 6, 5, 8, 0, 2, 7, 1, 3], 'cur_cost': 1123.0}, {'tour': [4, 5, 0, 6, 2, 8, 3, 7, 1], 'cur_cost': 1026.0}, {'tour': array([2, 7, 6, 0, 8, 5, 4, 3, 1], dtype=int64), 'cur_cost': 1110.0}, {'tour': [5, 8, 7, 0, 1, 6, 3, 4, 2], 'cur_cost': 880.0}, {'tour': [0, 3, 4, 6, 7, 5, 8, 2, 1], 'cur_cost': 926.0}, {'tour': [7, 3, 1, 5, 0, 8, 4, 2, 6], 'cur_cost': 1076.0}]
2025-08-04 17:27:34,400 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:27:34,400 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-08-04 17:27:34,401 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([2, 7, 6, 0, 8, 5, 4, 3, 1], dtype=int64), 'cur_cost': 1110.0, 'intermediate_solutions': [{'tour': array([5, 3, 4, 0, 7, 6, 2, 1, 8]), 'cur_cost': 1035.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 5, 3, 4, 7, 6, 2, 1, 8]), 'cur_cost': 1126.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 0, 5, 3, 4, 6, 2, 1, 8]), 'cur_cost': 1133.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 0, 5, 3, 7, 6, 2, 1, 8]), 'cur_cost': 1014.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 7, 0, 5, 3, 6, 2, 1, 8]), 'cur_cost': 1066.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:27:34,401 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1110.00)
2025-08-04 17:27:34,402 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:27:34,402 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:27:34,402 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:34,402 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:27:34,403 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,403 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,403 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,403 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,403 - ExplorationExpert - INFO - 探索路径生成完成，成本: 867.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:27:34,404 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [6, 3, 8, 7, 4, 0, 1, 2, 5], 'cur_cost': 867.0, 'intermediate_solutions': [{'tour': [5, 8, 7, 0, 2, 6, 3, 4, 1], 'cur_cost': 1079.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 4, 3, 6, 1, 0, 7, 8, 2], 'cur_cost': 968.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 5, 7, 0, 1, 6, 3, 4, 2], 'cur_cost': 792.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:27:34,404 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 867.00)
2025-08-04 17:27:34,404 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:27:34,404 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:27:34,404 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:34,405 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:27:34,405 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,405 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,406 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,406 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,406 - ExplorationExpert - INFO - 探索路径生成完成，成本: 789.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:27:34,406 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [3, 7, 5, 6, 8, 2, 4, 0, 1], 'cur_cost': 789.0, 'intermediate_solutions': [{'tour': [0, 3, 4, 6, 5, 7, 8, 2, 1], 'cur_cost': 902.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 4, 6, 1, 2, 8, 5, 7], 'cur_cost': 1005.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 6, 7, 5, 8, 2, 4, 1], 'cur_cost': 780.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:27:34,407 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 789.00)
2025-08-04 17:27:34,407 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-04 17:27:34,407 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:27:34,407 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:27:34,407 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 921.0
2025-08-04 17:27:34,480 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:27:34,481 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680]
2025-08-04 17:27:34,481 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:27:34,482 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:27:34,482 - ExploitationExpert - INFO - populations: [{'tour': [2, 4, 0, 6, 5, 3, 7, 8, 1], 'cur_cost': 829.0}, {'tour': [2, 3, 6, 8, 0, 7, 5, 4, 1], 'cur_cost': 1068.0}, {'tour': [5, 8, 3, 6, 7, 0, 4, 2, 1], 'cur_cost': 886.0}, {'tour': array([2, 8, 0, 5, 3, 7, 1, 4, 6], dtype=int64), 'cur_cost': 1057.0}, {'tour': [4, 6, 5, 8, 0, 2, 7, 1, 3], 'cur_cost': 1123.0}, {'tour': [4, 5, 0, 6, 2, 8, 3, 7, 1], 'cur_cost': 1026.0}, {'tour': array([2, 7, 6, 0, 8, 5, 4, 3, 1], dtype=int64), 'cur_cost': 1110.0}, {'tour': [6, 3, 8, 7, 4, 0, 1, 2, 5], 'cur_cost': 867.0}, {'tour': [3, 7, 5, 6, 8, 2, 4, 0, 1], 'cur_cost': 789.0}, {'tour': array([4, 0, 7, 8, 6, 5, 3, 2, 1], dtype=int64), 'cur_cost': 921.0}]
2025-08-04 17:27:34,483 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒，最大迭代次数: 10
2025-08-04 17:27:34,484 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-08-04 17:27:34,485 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([4, 0, 7, 8, 6, 5, 3, 2, 1], dtype=int64), 'cur_cost': 921.0, 'intermediate_solutions': [{'tour': array([1, 3, 7, 5, 0, 8, 4, 2, 6]), 'cur_cost': 1019.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 1, 3, 7, 0, 8, 4, 2, 6]), 'cur_cost': 1021.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 5, 1, 3, 7, 8, 4, 2, 6]), 'cur_cost': 1041.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 5, 1, 3, 0, 8, 4, 2, 6]), 'cur_cost': 1083.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 0, 5, 1, 3, 8, 4, 2, 6]), 'cur_cost': 1041.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:27:34,485 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 921.00)
2025-08-04 17:27:34,485 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:27:34,486 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:27:34,487 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [2, 4, 0, 6, 5, 3, 7, 8, 1], 'cur_cost': 829.0, 'intermediate_solutions': [{'tour': [0, 7, 1, 4, 2, 3, 5, 8, 6], 'cur_cost': 918.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 3, 5, 2, 4, 1, 7, 6], 'cur_cost': 915.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 1, 4, 2, 0, 5, 3, 8, 6], 'cur_cost': 903.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [2, 3, 6, 8, 0, 7, 5, 4, 1], 'cur_cost': 1068.0, 'intermediate_solutions': [{'tour': [6, 0, 1, 7, 4, 5, 8, 3, 2], 'cur_cost': 1016.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 8, 5, 3, 7, 1, 0, 4, 2], 'cur_cost': 858.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 1, 8, 7, 3, 5, 4, 2], 'cur_cost': 910.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [5, 8, 3, 6, 7, 0, 4, 2, 1], 'cur_cost': 886.0, 'intermediate_solutions': [{'tour': [6, 0, 4, 3, 8, 5, 7, 2, 1], 'cur_cost': 938.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 3, 8, 4, 0, 6, 7, 2, 1], 'cur_cost': 932.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 4, 8, 2, 3, 5, 7, 1], 'cur_cost': 903.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 8, 0, 5, 3, 7, 1, 4, 6], dtype=int64), 'cur_cost': 1057.0, 'intermediate_solutions': [{'tour': array([0, 6, 4, 8, 7, 3, 5, 2, 1]), 'cur_cost': 903.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 0, 6, 4, 7, 3, 5, 2, 1]), 'cur_cost': 1086.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 8, 0, 6, 4, 3, 5, 2, 1]), 'cur_cost': 1088.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 8, 0, 6, 7, 3, 5, 2, 1]), 'cur_cost': 950.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 7, 8, 0, 6, 3, 5, 2, 1]), 'cur_cost': 994.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [4, 6, 5, 8, 0, 2, 7, 1, 3], 'cur_cost': 1123.0, 'intermediate_solutions': [{'tour': [0, 4, 3, 6, 2, 8, 7, 1, 5], 'cur_cost': 1100.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 3, 4, 2, 7, 8, 1, 5], 'cur_cost': 1059.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 0, 6, 3, 4, 2, 8, 7, 1], 'cur_cost': 978.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [4, 5, 0, 6, 2, 8, 3, 7, 1], 'cur_cost': 1026.0, 'intermediate_solutions': [{'tour': [0, 8, 4, 7, 1, 5, 6, 3, 2], 'cur_cost': 1063.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 6, 5, 1, 7, 4, 3, 8], 'cur_cost': 1107.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 7, 1, 5, 4, 6, 3, 8], 'cur_cost': 1154.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 7, 6, 0, 8, 5, 4, 3, 1], dtype=int64), 'cur_cost': 1110.0, 'intermediate_solutions': [{'tour': array([5, 3, 4, 0, 7, 6, 2, 1, 8]), 'cur_cost': 1035.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 5, 3, 4, 7, 6, 2, 1, 8]), 'cur_cost': 1126.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 0, 5, 3, 4, 6, 2, 1, 8]), 'cur_cost': 1133.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 0, 5, 3, 7, 6, 2, 1, 8]), 'cur_cost': 1014.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 7, 0, 5, 3, 6, 2, 1, 8]), 'cur_cost': 1066.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [6, 3, 8, 7, 4, 0, 1, 2, 5], 'cur_cost': 867.0, 'intermediate_solutions': [{'tour': [5, 8, 7, 0, 2, 6, 3, 4, 1], 'cur_cost': 1079.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 4, 3, 6, 1, 0, 7, 8, 2], 'cur_cost': 968.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 5, 7, 0, 1, 6, 3, 4, 2], 'cur_cost': 792.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [3, 7, 5, 6, 8, 2, 4, 0, 1], 'cur_cost': 789.0, 'intermediate_solutions': [{'tour': [0, 3, 4, 6, 5, 7, 8, 2, 1], 'cur_cost': 902.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 4, 6, 1, 2, 8, 5, 7], 'cur_cost': 1005.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 6, 7, 5, 8, 2, 4, 1], 'cur_cost': 780.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 0, 7, 8, 6, 5, 3, 2, 1], dtype=int64), 'cur_cost': 921.0, 'intermediate_solutions': [{'tour': array([1, 3, 7, 5, 0, 8, 4, 2, 6]), 'cur_cost': 1019.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 1, 3, 7, 0, 8, 4, 2, 6]), 'cur_cost': 1021.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 5, 1, 3, 7, 8, 4, 2, 6]), 'cur_cost': 1041.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 5, 1, 3, 0, 8, 4, 2, 6]), 'cur_cost': 1083.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 0, 5, 1, 3, 8, 4, 2, 6]), 'cur_cost': 1041.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-04 17:27:34,490 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:27:34,490 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:27:34,491 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=789.000, 多样性=0.844
2025-08-04 17:27:34,491 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-04 17:27:34,491 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-04 17:27:34,492 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:27:34,492 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.005619240355665488, 'best_improvement': 0.04363636363636364}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.014409221902017098}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.014158279036074924, 'recent_improvements': [-0.03511496228075444, -0.009640039034705197, -0.006798404208604594], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:27:34,492 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-04 17:27:34,493 - __main__ - INFO - simple1_9 开始进化第 5 代
2025-08-04 17:27:34,493 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-04 17:27:34,493 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:27:34,494 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=789.000, 多样性=0.844
2025-08-04 17:27:34,494 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:27:34,495 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.844
2025-08-04 17:27:34,495 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:27:34,495 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.630
2025-08-04 17:27:34,497 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-04 17:27:34,498 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:27:34,498 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-04 17:27:34,498 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-04 17:27:34,505 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.385, 适应度梯度: 31.354, 聚类评分: 0.000, 覆盖率: 0.005, 收敛趋势: 0.000, 多样性: 0.601
2025-08-04 17:27:34,506 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-04 17:27:34,506 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:27:34,506 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:27:34,509 - visualization.landscape_visualizer - INFO - 插值约束: 147 个点被约束到最小值 680.00
2025-08-04 17:27:34,514 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:27:34,741 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_5_20250804_172734.html
2025-08-04 17:27:34,781 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_5_20250804_172734.html
2025-08-04 17:27:34,781 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 5
2025-08-04 17:27:34,782 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-04 17:27:34,782 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2847秒
2025-08-04 17:27:34,783 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.38461538461538464, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 31.353846153846153, 'local_optima_density': 0.38461538461538464, 'gradient_variance': 28369.170177514796, 'cluster_count': 0}, 'population_state': {'diversity': 0.6005917159763313, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0052, 'fitness_entropy': 0.957713559837711, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.005)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 31.354)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754299654.5065043, 'performance_metrics': {}}}
2025-08-04 17:27:34,783 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:27:34,784 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:27:34,784 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:27:34,784 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:27:34,784 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:27:34,785 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-04 17:27:34,785 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:27:34,785 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:27:34,786 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:27:34,786 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:27:34,786 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:27:34,787 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:27:34,787 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 0} (总数: 2, 保护比例: 0.20)
2025-08-04 17:27:34,787 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:27:34,787 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:27:34,787 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:34,788 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:27:34,788 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,788 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,788 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,788 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,789 - ExplorationExpert - INFO - 探索路径生成完成，成本: 975.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:27:34,789 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [5, 6, 3, 0, 7, 8, 1, 4, 2], 'cur_cost': 975.0, 'intermediate_solutions': [{'tour': [2, 4, 0, 5, 6, 3, 7, 8, 1], 'cur_cost': 898.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 7, 3, 5, 6, 0, 4, 1], 'cur_cost': 805.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 0, 5, 3, 7, 6, 8, 1], 'cur_cost': 925.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:27:34,789 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 975.00)
2025-08-04 17:27:34,789 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-04 17:27:34,790 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:27:34,790 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:27:34,790 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 783.0
2025-08-04 17:27:34,867 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:27:34,867 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680]
2025-08-04 17:27:34,868 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:27:34,869 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:27:34,869 - ExploitationExpert - INFO - populations: [{'tour': [5, 6, 3, 0, 7, 8, 1, 4, 2], 'cur_cost': 975.0}, {'tour': array([0, 6, 8, 3, 5, 7, 2, 4, 1], dtype=int64), 'cur_cost': 783.0}, {'tour': [5, 8, 3, 6, 7, 0, 4, 2, 1], 'cur_cost': 886.0}, {'tour': [2, 8, 0, 5, 3, 7, 1, 4, 6], 'cur_cost': 1057.0}, {'tour': [4, 6, 5, 8, 0, 2, 7, 1, 3], 'cur_cost': 1123.0}, {'tour': [4, 5, 0, 6, 2, 8, 3, 7, 1], 'cur_cost': 1026.0}, {'tour': [2, 7, 6, 0, 8, 5, 4, 3, 1], 'cur_cost': 1110.0}, {'tour': [6, 3, 8, 7, 4, 0, 1, 2, 5], 'cur_cost': 867.0}, {'tour': [3, 7, 5, 6, 8, 2, 4, 0, 1], 'cur_cost': 789.0}, {'tour': [4, 0, 7, 8, 6, 5, 3, 2, 1], 'cur_cost': 921.0}]
2025-08-04 17:27:34,870 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒，最大迭代次数: 10
2025-08-04 17:27:34,870 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-08-04 17:27:34,871 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([0, 6, 8, 3, 5, 7, 2, 4, 1], dtype=int64), 'cur_cost': 783.0, 'intermediate_solutions': [{'tour': array([6, 3, 2, 8, 0, 7, 5, 4, 1]), 'cur_cost': 1009.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 6, 3, 2, 0, 7, 5, 4, 1]), 'cur_cost': 1096.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 8, 6, 3, 2, 7, 5, 4, 1]), 'cur_cost': 1019.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 8, 6, 3, 0, 7, 5, 4, 1]), 'cur_cost': 1006.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 0, 8, 6, 3, 7, 5, 4, 1]), 'cur_cost': 1022.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:27:34,871 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 783.00)
2025-08-04 17:27:34,872 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:27:34,872 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:27:34,872 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:34,872 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:27:34,872 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,873 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,873 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,873 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,873 - ExplorationExpert - INFO - 探索路径生成完成，成本: 948.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:27:34,873 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [5, 6, 2, 8, 7, 1, 4, 0, 3], 'cur_cost': 948.0, 'intermediate_solutions': [{'tour': [7, 8, 3, 6, 5, 0, 4, 2, 1], 'cur_cost': 865.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 1, 2, 4, 0, 7, 6, 3, 8], 'cur_cost': 886.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 8, 3, 6, 7, 0, 4, 2, 1], 'cur_cost': 886.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:27:34,874 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 948.00)
2025-08-04 17:27:34,874 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:27:34,874 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:27:34,874 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:34,875 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:27:34,875 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,875 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,875 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,875 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,876 - ExplorationExpert - INFO - 探索路径生成完成，成本: 861.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:27:34,876 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 3, 5, 7, 6, 4, 2, 8, 1], 'cur_cost': 861.0, 'intermediate_solutions': [{'tour': [2, 6, 0, 5, 3, 7, 1, 4, 8], 'cur_cost': 963.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 1, 7, 3, 5, 0, 8, 4, 6], 'cur_cost': 1083.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 8, 5, 3, 0, 7, 1, 4, 6], 'cur_cost': 1039.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:27:34,876 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 861.00)
2025-08-04 17:27:34,876 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-04 17:27:34,877 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:27:34,877 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:27:34,877 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 1031.0
2025-08-04 17:27:34,950 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:27:34,950 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680]
2025-08-04 17:27:34,950 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:27:34,951 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:27:34,951 - ExploitationExpert - INFO - populations: [{'tour': [5, 6, 3, 0, 7, 8, 1, 4, 2], 'cur_cost': 975.0}, {'tour': array([0, 6, 8, 3, 5, 7, 2, 4, 1], dtype=int64), 'cur_cost': 783.0}, {'tour': [5, 6, 2, 8, 7, 1, 4, 0, 3], 'cur_cost': 948.0}, {'tour': [0, 3, 5, 7, 6, 4, 2, 8, 1], 'cur_cost': 861.0}, {'tour': array([4, 1, 7, 5, 2, 8, 0, 6, 3], dtype=int64), 'cur_cost': 1031.0}, {'tour': [4, 5, 0, 6, 2, 8, 3, 7, 1], 'cur_cost': 1026.0}, {'tour': [2, 7, 6, 0, 8, 5, 4, 3, 1], 'cur_cost': 1110.0}, {'tour': [6, 3, 8, 7, 4, 0, 1, 2, 5], 'cur_cost': 867.0}, {'tour': [3, 7, 5, 6, 8, 2, 4, 0, 1], 'cur_cost': 789.0}, {'tour': [4, 0, 7, 8, 6, 5, 3, 2, 1], 'cur_cost': 921.0}]
2025-08-04 17:27:34,952 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒，最大迭代次数: 10
2025-08-04 17:27:34,953 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-08-04 17:27:34,953 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([4, 1, 7, 5, 2, 8, 0, 6, 3], dtype=int64), 'cur_cost': 1031.0, 'intermediate_solutions': [{'tour': array([5, 6, 4, 8, 0, 2, 7, 1, 3]), 'cur_cost': 1060.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 5, 6, 4, 0, 2, 7, 1, 3]), 'cur_cost': 1028.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 8, 5, 6, 4, 2, 7, 1, 3]), 'cur_cost': 1054.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 8, 5, 6, 0, 2, 7, 1, 3]), 'cur_cost': 1029.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 0, 8, 5, 6, 2, 7, 1, 3]), 'cur_cost': 1121.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:27:34,954 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1031.00)
2025-08-04 17:27:34,954 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:27:34,954 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:27:34,954 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:34,955 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:27:34,955 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,955 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,955 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,956 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:34,956 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1013.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:27:34,956 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [6, 3, 4, 8, 2, 1, 5, 0, 7], 'cur_cost': 1013.0, 'intermediate_solutions': [{'tour': [3, 5, 0, 6, 2, 8, 4, 7, 1], 'cur_cost': 1055.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 5, 0, 8, 2, 6, 3, 7, 1], 'cur_cost': 1088.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 5, 0, 6, 2, 3, 8, 7, 1], 'cur_cost': 1101.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:27:34,956 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1013.00)
2025-08-04 17:27:34,956 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-04 17:27:34,956 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:27:34,957 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:27:34,957 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 978.0
2025-08-04 17:27:35,029 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:27:35,030 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680]
2025-08-04 17:27:35,030 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:27:35,031 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:27:35,032 - ExploitationExpert - INFO - populations: [{'tour': [5, 6, 3, 0, 7, 8, 1, 4, 2], 'cur_cost': 975.0}, {'tour': array([0, 6, 8, 3, 5, 7, 2, 4, 1], dtype=int64), 'cur_cost': 783.0}, {'tour': [5, 6, 2, 8, 7, 1, 4, 0, 3], 'cur_cost': 948.0}, {'tour': [0, 3, 5, 7, 6, 4, 2, 8, 1], 'cur_cost': 861.0}, {'tour': array([4, 1, 7, 5, 2, 8, 0, 6, 3], dtype=int64), 'cur_cost': 1031.0}, {'tour': [6, 3, 4, 8, 2, 1, 5, 0, 7], 'cur_cost': 1013.0}, {'tour': array([1, 8, 5, 7, 3, 0, 4, 2, 6], dtype=int64), 'cur_cost': 978.0}, {'tour': [6, 3, 8, 7, 4, 0, 1, 2, 5], 'cur_cost': 867.0}, {'tour': [3, 7, 5, 6, 8, 2, 4, 0, 1], 'cur_cost': 789.0}, {'tour': [4, 0, 7, 8, 6, 5, 3, 2, 1], 'cur_cost': 921.0}]
2025-08-04 17:27:35,033 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒，最大迭代次数: 10
2025-08-04 17:27:35,033 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-08-04 17:27:35,034 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([1, 8, 5, 7, 3, 0, 4, 2, 6], dtype=int64), 'cur_cost': 978.0, 'intermediate_solutions': [{'tour': array([6, 7, 2, 0, 8, 5, 4, 3, 1]), 'cur_cost': 1121.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 6, 7, 2, 8, 5, 4, 3, 1]), 'cur_cost': 978.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 0, 6, 7, 2, 5, 4, 3, 1]), 'cur_cost': 1222.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 0, 6, 7, 8, 5, 4, 3, 1]), 'cur_cost': 1056.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 8, 0, 6, 7, 5, 4, 3, 1]), 'cur_cost': 1052.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:27:35,034 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 978.00)
2025-08-04 17:27:35,034 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:27:35,034 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:27:35,035 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:35,035 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:27:35,035 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:35,035 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:35,036 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:35,036 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:35,036 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1003.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:27:35,036 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [4, 7, 5, 8, 0, 2, 3, 6, 1], 'cur_cost': 1003.0, 'intermediate_solutions': [{'tour': [6, 3, 8, 2, 4, 0, 1, 7, 5], 'cur_cost': 727.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 3, 8, 7, 4, 0, 1, 5, 2], 'cur_cost': 1014.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 3, 8, 7, 4, 1, 0, 2, 5], 'cur_cost': 878.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:27:35,037 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1003.00)
2025-08-04 17:27:35,037 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:27:35,037 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:27:35,037 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:35,038 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:27:35,038 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:35,038 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:35,038 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:35,039 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:35,039 - ExplorationExpert - INFO - 探索路径生成完成，成本: 763.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:27:35,039 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [3, 6, 7, 5, 0, 1, 4, 2, 8], 'cur_cost': 763.0, 'intermediate_solutions': [{'tour': [3, 7, 6, 5, 8, 2, 4, 0, 1], 'cur_cost': 758.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 7, 5, 6, 0, 4, 2, 8, 1], 'cur_cost': 879.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 7, 8, 5, 6, 2, 4, 0, 1], 'cur_cost': 852.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:27:35,039 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 763.00)
2025-08-04 17:27:35,040 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:27:35,040 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:27:35,040 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:27:35,040 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:27:35,040 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:35,041 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:35,041 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:35,041 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:27:35,041 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1076.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:27:35,041 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [3, 0, 4, 8, 2, 6, 7, 5, 1], 'cur_cost': 1076.0, 'intermediate_solutions': [{'tour': [4, 0, 7, 3, 6, 5, 8, 2, 1], 'cur_cost': 843.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 0, 7, 8, 1, 2, 3, 5, 6], 'cur_cost': 999.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 8, 6, 4, 5, 3, 2, 1], 'cur_cost': 993.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:27:35,042 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1076.00)
2025-08-04 17:27:35,042 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:27:35,042 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:27:35,044 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [5, 6, 3, 0, 7, 8, 1, 4, 2], 'cur_cost': 975.0, 'intermediate_solutions': [{'tour': [2, 4, 0, 5, 6, 3, 7, 8, 1], 'cur_cost': 898.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 7, 3, 5, 6, 0, 4, 1], 'cur_cost': 805.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 0, 5, 3, 7, 6, 8, 1], 'cur_cost': 925.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 6, 8, 3, 5, 7, 2, 4, 1], dtype=int64), 'cur_cost': 783.0, 'intermediate_solutions': [{'tour': array([6, 3, 2, 8, 0, 7, 5, 4, 1]), 'cur_cost': 1009.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 6, 3, 2, 0, 7, 5, 4, 1]), 'cur_cost': 1096.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 8, 6, 3, 2, 7, 5, 4, 1]), 'cur_cost': 1019.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 8, 6, 3, 0, 7, 5, 4, 1]), 'cur_cost': 1006.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 0, 8, 6, 3, 7, 5, 4, 1]), 'cur_cost': 1022.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [5, 6, 2, 8, 7, 1, 4, 0, 3], 'cur_cost': 948.0, 'intermediate_solutions': [{'tour': [7, 8, 3, 6, 5, 0, 4, 2, 1], 'cur_cost': 865.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 1, 2, 4, 0, 7, 6, 3, 8], 'cur_cost': 886.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 8, 3, 6, 7, 0, 4, 2, 1], 'cur_cost': 886.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 5, 7, 6, 4, 2, 8, 1], 'cur_cost': 861.0, 'intermediate_solutions': [{'tour': [2, 6, 0, 5, 3, 7, 1, 4, 8], 'cur_cost': 963.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 1, 7, 3, 5, 0, 8, 4, 6], 'cur_cost': 1083.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 8, 5, 3, 0, 7, 1, 4, 6], 'cur_cost': 1039.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 1, 7, 5, 2, 8, 0, 6, 3], dtype=int64), 'cur_cost': 1031.0, 'intermediate_solutions': [{'tour': array([5, 6, 4, 8, 0, 2, 7, 1, 3]), 'cur_cost': 1060.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 5, 6, 4, 0, 2, 7, 1, 3]), 'cur_cost': 1028.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 8, 5, 6, 4, 2, 7, 1, 3]), 'cur_cost': 1054.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 8, 5, 6, 0, 2, 7, 1, 3]), 'cur_cost': 1029.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 0, 8, 5, 6, 2, 7, 1, 3]), 'cur_cost': 1121.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [6, 3, 4, 8, 2, 1, 5, 0, 7], 'cur_cost': 1013.0, 'intermediate_solutions': [{'tour': [3, 5, 0, 6, 2, 8, 4, 7, 1], 'cur_cost': 1055.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 5, 0, 8, 2, 6, 3, 7, 1], 'cur_cost': 1088.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 5, 0, 6, 2, 3, 8, 7, 1], 'cur_cost': 1101.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([1, 8, 5, 7, 3, 0, 4, 2, 6], dtype=int64), 'cur_cost': 978.0, 'intermediate_solutions': [{'tour': array([6, 7, 2, 0, 8, 5, 4, 3, 1]), 'cur_cost': 1121.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 6, 7, 2, 8, 5, 4, 3, 1]), 'cur_cost': 978.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 0, 6, 7, 2, 5, 4, 3, 1]), 'cur_cost': 1222.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 0, 6, 7, 8, 5, 4, 3, 1]), 'cur_cost': 1056.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 8, 0, 6, 7, 5, 4, 3, 1]), 'cur_cost': 1052.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [4, 7, 5, 8, 0, 2, 3, 6, 1], 'cur_cost': 1003.0, 'intermediate_solutions': [{'tour': [6, 3, 8, 2, 4, 0, 1, 7, 5], 'cur_cost': 727.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 3, 8, 7, 4, 0, 1, 5, 2], 'cur_cost': 1014.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 3, 8, 7, 4, 1, 0, 2, 5], 'cur_cost': 878.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [3, 6, 7, 5, 0, 1, 4, 2, 8], 'cur_cost': 763.0, 'intermediate_solutions': [{'tour': [3, 7, 6, 5, 8, 2, 4, 0, 1], 'cur_cost': 758.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 7, 5, 6, 0, 4, 2, 8, 1], 'cur_cost': 879.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 7, 8, 5, 6, 2, 4, 0, 1], 'cur_cost': 852.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [3, 0, 4, 8, 2, 6, 7, 5, 1], 'cur_cost': 1076.0, 'intermediate_solutions': [{'tour': [4, 0, 7, 3, 6, 5, 8, 2, 1], 'cur_cost': 843.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 0, 7, 8, 1, 2, 3, 5, 6], 'cur_cost': 999.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 8, 6, 4, 5, 3, 2, 1], 'cur_cost': 993.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:27:35,046 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:27:35,046 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:27:35,047 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=763.000, 多样性=0.881
2025-08-04 17:27:35,048 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-04 17:27:35,048 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-04 17:27:35,048 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:27:35,048 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.03337522239172544, 'best_improvement': 0.032953105196451206}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.04385964912280682}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.002010399339519854, 'recent_improvements': [-0.009640039034705197, -0.006798404208604594, -0.005619240355665488], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:27:35,049 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-04 17:27:35,051 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_solution.json
2025-08-04 17:27:35,051 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_20250804_172735.solution
2025-08-04 17:27:35,061 - __main__ - INFO - 评估统计 - 总次数: 249294.3333331191, 运行时间: 9.61s, 最佳成本: 680.0
2025-08-04 17:27:35,061 - __main__ - INFO - 实例 simple1_9 处理完成
