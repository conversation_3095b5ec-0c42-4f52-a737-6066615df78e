2025-08-05 09:52:12,699 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-08-05 09:52:12,699 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 09:52:12,700 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:12,704 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9890.000, 多样性=0.976
2025-08-05 09:52:12,707 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:12,713 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.976
2025-08-05 09:52:12,716 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:12,718 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 09:52:12,718 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:12,718 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 09:52:12,719 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 09:52:12,740 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -12796.100, 聚类评分: 0.000, 覆盖率: 0.142, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:12,740 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 09:52:12,740 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 09:52:12,740 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite13_66
2025-08-05 09:52:12,855 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite13_66\landscape_composite13_66_iter_121_20250805_095212.html
2025-08-05 09:52:12,902 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite13_66\dashboard_composite13_66_iter_121_20250805_095212.html
2025-08-05 09:52:12,902 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 121
2025-08-05 09:52:12,902 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 09:52:12,903 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1853秒
2025-08-05 09:52:12,903 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 242, 'max_size': 500, 'hits': 0, 'misses': 242, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 792, 'misses': 436, 'hit_rate': 0.6449511400651465, 'evictions': 336, 'ttl': 7200}}
2025-08-05 09:52:12,903 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -12796.1, 'local_optima_density': 0.1, 'gradient_variance': 2083051221.154, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1419, 'fitness_entropy': 0.9911594714322186, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -12796.100)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.142)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358732.7404945, 'performance_metrics': {}}}
2025-08-05 09:52:12,903 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:12,903 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:12,904 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:12,904 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:12,905 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:52:12,906 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 09:52:12,906 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:52:12,906 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:12,906 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:12,906 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:52:12,906 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:12,907 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:12,907 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:12,907 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:12,907 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:12,907 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:12,920 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-05 09:52:12,921 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,921 - ExplorationExpert - INFO - 探索路径生成完成，成本: 64268.0, 路径长度: 66, 收集中间解: 0
2025-08-05 09:52:12,922 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [10, 54, 6, 63, 8, 20, 24, 26, 31, 23, 5, 60, 61, 57, 22, 30, 11, 1, 65, 64, 56, 19, 7, 17, 34, 36, 28, 15, 12, 32, 14, 35, 3, 52, 16, 27, 37, 4, 2, 13, 48, 40, 42, 45, 49, 21, 9, 62, 18, 46, 41, 38, 44, 47, 43, 39, 25, 0, 53, 58, 50, 33, 51, 29, 55, 59], 'cur_cost': 64268.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:12,922 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 64268.00)
2025-08-05 09:52:12,922 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:12,922 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:12,922 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:12,927 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-05 09:52:12,927 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,928 - ExplorationExpert - INFO - 探索路径生成完成，成本: 80219.0, 路径长度: 66, 收集中间解: 0
2025-08-05 09:52:12,928 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [6, 20, 24, 60, 1, 56, 34, 36, 28, 35, 37, 48, 40, 45, 21, 62, 46, 38, 44, 47, 43, 39, 0, 31, 4, 15, 33, 63, 57, 26, 54, 9, 17, 50, 51, 14, 49, 19, 65, 8, 22, 30, 29, 42, 27, 18, 32, 10, 11, 25, 16, 3, 12, 53, 55, 61, 41, 23, 59, 5, 7, 2, 64, 13, 58, 52], 'cur_cost': 80219.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:12,928 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 80219.00)
2025-08-05 09:52:12,928 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:12,928 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:12,929 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:12,931 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-05 09:52:12,932 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,932 - ExplorationExpert - INFO - 探索路径生成完成，成本: 88190.0, 路径长度: 66, 收集中间解: 0
2025-08-05 09:52:12,933 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [10, 54, 60, 1, 34, 26, 28, 23, 37, 48, 61, 57, 21, 46, 44, 19, 7, 17, 4, 15, 33, 14, 52, 49, 2, 8, 40, 42, 45, 27, 18, 9, 59, 41, 38, 58, 53, 43, 39, 0, 65, 51, 29, 63, 30, 24, 3, 25, 36, 12, 16, 50, 47, 11, 56, 5, 22, 31, 62, 55, 6, 13, 20, 64, 32, 35], 'cur_cost': 88190.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:12,933 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 88190.00)
2025-08-05 09:52:12,933 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:52:12,933 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:12,934 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:12,934 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 109064.0
2025-08-05 09:52:12,944 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 09:52:12,944 - ExploitationExpert - INFO - res_population_costs: [9534.0, 9521]
2025-08-05 09:52:12,945 - ExploitationExpert - INFO - res_populations: [array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-05 09:52:12,946 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:12,946 - ExploitationExpert - INFO - populations: [{'tour': [10, 54, 6, 63, 8, 20, 24, 26, 31, 23, 5, 60, 61, 57, 22, 30, 11, 1, 65, 64, 56, 19, 7, 17, 34, 36, 28, 15, 12, 32, 14, 35, 3, 52, 16, 27, 37, 4, 2, 13, 48, 40, 42, 45, 49, 21, 9, 62, 18, 46, 41, 38, 44, 47, 43, 39, 25, 0, 53, 58, 50, 33, 51, 29, 55, 59], 'cur_cost': 64268.0}, {'tour': [6, 20, 24, 60, 1, 56, 34, 36, 28, 35, 37, 48, 40, 45, 21, 62, 46, 38, 44, 47, 43, 39, 0, 31, 4, 15, 33, 63, 57, 26, 54, 9, 17, 50, 51, 14, 49, 19, 65, 8, 22, 30, 29, 42, 27, 18, 32, 10, 11, 25, 16, 3, 12, 53, 55, 61, 41, 23, 59, 5, 7, 2, 64, 13, 58, 52], 'cur_cost': 80219.0}, {'tour': [10, 54, 60, 1, 34, 26, 28, 23, 37, 48, 61, 57, 21, 46, 44, 19, 7, 17, 4, 15, 33, 14, 52, 49, 2, 8, 40, 42, 45, 27, 18, 9, 59, 41, 38, 58, 53, 43, 39, 0, 65, 51, 29, 63, 30, 24, 3, 25, 36, 12, 16, 50, 47, 11, 56, 5, 22, 31, 62, 55, 6, 13, 20, 64, 32, 35], 'cur_cost': 88190.0}, {'tour': array([52, 49,  4, 12, 58, 14, 19, 48, 37, 25, 21, 15, 44,  9, 40, 22, 36,
       24, 35, 30, 10, 45, 17, 46, 59,  8, 63, 61, 20, 41, 56, 38, 13, 57,
        6,  3, 33, 47, 31, 50, 32, 54, 29, 34, 53, 11, 18,  0,  2, 16, 64,
       51, 55,  1, 65, 43,  5, 23, 39, 26,  7, 62, 42, 60, 28, 27],
      dtype=int64), 'cur_cost': 109064.0}, {'tour': array([35,  8, 33, 30, 45, 18, 20, 38, 46, 65, 28,  0, 41,  5, 37,  2, 57,
       52,  3,  6, 22, 53, 62, 21, 14, 59, 12, 17, 43, 32, 13, 40, 48, 27,
       47, 26, 42, 60, 51, 10,  4, 58, 49, 24, 56, 64, 61, 50, 16, 15, 63,
       55,  7, 54, 34, 44, 36, 29, 39,  1, 19, 31, 11, 23, 25,  9],
      dtype=int64), 'cur_cost': 100252.0}, {'tour': array([35, 29, 60, 54, 24, 50, 20, 13, 63, 34, 26, 59, 49,  5, 15, 31, 62,
       16, 19, 64, 21, 40, 55, 10, 39, 58,  3, 45, 53, 14, 17, 30, 37, 28,
        8,  0, 48, 47, 61, 42, 11, 22, 33,  6,  7, 43, 51, 36, 25, 52, 65,
        1,  9, 44, 12, 23, 46, 41, 38, 56, 32, 57, 18, 27,  4,  2],
      dtype=int64), 'cur_cost': 101699.0}, {'tour': array([41, 46, 42, 39, 34, 28, 56, 51, 58,  1, 32,  4, 54,  8, 37,  6, 60,
       26, 21, 10, 50, 61, 63, 29, 64, 18, 55, 16, 30, 11, 23,  9, 49, 17,
       33, 36, 24, 57, 19, 13, 31, 59, 20,  7, 12, 25, 65, 27, 48, 35, 15,
       44, 38,  5, 52, 14, 47, 43, 45, 40, 22,  3,  2,  0, 53, 62],
      dtype=int64), 'cur_cost': 97789.0}, {'tour': array([22, 34, 53,  2, 35, 13, 26, 14, 41, 36,  6, 44, 15, 48, 47, 33,  1,
       58, 55, 23,  8, 62, 25, 46, 16, 57, 61,  0, 20, 32, 31, 18, 59, 50,
       51, 40, 12, 56, 37, 29,  4, 49,  5, 63, 30, 54, 28, 65, 60, 24, 10,
       42, 21, 39, 52,  9, 19,  3, 17, 64, 27, 11, 43, 45,  7, 38],
      dtype=int64), 'cur_cost': 109261.0}, {'tour': array([51, 19,  5, 45, 36, 31, 11, 14, 39, 27, 48, 59, 23, 56, 17,  9,  4,
        8, 63, 65, 64, 20, 37, 53, 32, 29, 35, 38, 42, 61, 49, 52, 43, 54,
       44, 13, 50, 15, 62, 41, 33, 28, 25, 55, 26, 46, 24, 47, 21, 40,  7,
       22, 16,  0, 57, 58, 10, 30,  1, 60, 18, 12, 34,  3,  2,  6],
      dtype=int64), 'cur_cost': 101441.0}, {'tour': array([27, 20, 38, 64, 65, 61,  0, 21, 48, 16, 35, 40, 47, 54, 52, 11, 22,
       58, 31, 25, 28, 53, 62,  2, 19,  8, 12,  5, 50, 39, 33, 49, 56, 24,
       15, 17, 63, 59, 41, 36, 51,  6, 30,  9, 14, 34,  1, 43, 44, 32, 13,
       45,  7, 29,  4, 46, 57, 37, 42, 60, 55, 26,  3, 18, 10, 23],
      dtype=int64), 'cur_cost': 103638.0}]
2025-08-05 09:52:12,950 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:12,950 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 313, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 313, 'cache_hits': 0, 'similarity_calculations': 1590, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:12,951 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([52, 49,  4, 12, 58, 14, 19, 48, 37, 25, 21, 15, 44,  9, 40, 22, 36,
       24, 35, 30, 10, 45, 17, 46, 59,  8, 63, 61, 20, 41, 56, 38, 13, 57,
        6,  3, 33, 47, 31, 50, 32, 54, 29, 34, 53, 11, 18,  0,  2, 16, 64,
       51, 55,  1, 65, 43,  5, 23, 39, 26,  7, 62, 42, 60, 28, 27],
      dtype=int64), 'cur_cost': 109064.0, 'intermediate_solutions': [{'tour': array([33, 20, 56,  1, 16, 29, 60, 28, 52, 36, 65, 39, 35, 22,  5, 30, 38,
       57, 49, 47, 62, 18, 23,  0, 61, 44, 51, 32, 19, 54, 25, 11, 42, 14,
       40, 41, 64,  4, 55, 34, 37, 58,  2, 63, 45, 48, 31, 21,  6, 12, 53,
       43,  7,  3, 24, 26, 59, 15, 50, 17, 46, 27,  8,  9, 13, 10],
      dtype=int64), 'cur_cost': 113014.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1, 33, 20, 56, 16, 29, 60, 28, 52, 36, 65, 39, 35, 22,  5, 30, 38,
       57, 49, 47, 62, 18, 23,  0, 61, 44, 51, 32, 19, 54, 25, 11, 42, 14,
       40, 41, 64,  4, 55, 34, 37, 58,  2, 63, 45, 48, 31, 21,  6, 12, 53,
       43,  7,  3, 24, 26, 59, 15, 50, 17, 46, 27,  8,  9, 13, 10],
      dtype=int64), 'cur_cost': 112637.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([16,  1, 33, 20, 56, 29, 60, 28, 52, 36, 65, 39, 35, 22,  5, 30, 38,
       57, 49, 47, 62, 18, 23,  0, 61, 44, 51, 32, 19, 54, 25, 11, 42, 14,
       40, 41, 64,  4, 55, 34, 37, 58,  2, 63, 45, 48, 31, 21,  6, 12, 53,
       43,  7,  3, 24, 26, 59, 15, 50, 17, 46, 27,  8,  9, 13, 10],
      dtype=int64), 'cur_cost': 114932.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([56,  1, 33, 20, 16, 29, 60, 28, 52, 36, 65, 39, 35, 22,  5, 30, 38,
       57, 49, 47, 62, 18, 23,  0, 61, 44, 51, 32, 19, 54, 25, 11, 42, 14,
       40, 41, 64,  4, 55, 34, 37, 58,  2, 63, 45, 48, 31, 21,  6, 12, 53,
       43,  7,  3, 24, 26, 59, 15, 50, 17, 46, 27,  8,  9, 13, 10],
      dtype=int64), 'cur_cost': 110802.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([56, 16,  1, 33, 20, 29, 60, 28, 52, 36, 65, 39, 35, 22,  5, 30, 38,
       57, 49, 47, 62, 18, 23,  0, 61, 44, 51, 32, 19, 54, 25, 11, 42, 14,
       40, 41, 64,  4, 55, 34, 37, 58,  2, 63, 45, 48, 31, 21,  6, 12, 53,
       43,  7,  3, 24, 26, 59, 15, 50, 17, 46, 27,  8,  9, 13, 10],
      dtype=int64), 'cur_cost': 112947.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:12,951 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 109064.00)
2025-08-05 09:52:12,951 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:12,951 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:12,952 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:12,961 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-05 09:52:12,962 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,962 - ExplorationExpert - INFO - 探索路径生成完成，成本: 50209.0, 路径长度: 66, 收集中间解: 0
2025-08-05 09:52:12,963 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [65, 59, 11, 61, 63, 55, 52, 18, 20, 16, 14, 21, 19, 36, 25, 13, 33, 3, 62, 64, 57, 4, 22, 17, 32, 30, 27, 7, 8, 0, 5, 10, 23, 31, 9, 58, 56, 53, 49, 50, 45, 38, 48, 51, 34, 35, 1, 26, 6, 29, 15, 2, 60, 47, 44, 39, 40, 41, 12, 37, 24, 28, 43, 46, 42, 54], 'cur_cost': 50209.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:12,963 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 50209.00)
2025-08-05 09:52:12,963 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:12,963 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:12,963 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:12,971 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-05 09:52:12,971 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,971 - ExplorationExpert - INFO - 探索路径生成完成，成本: 56257.0, 路径长度: 66, 收集中间解: 0
2025-08-05 09:52:12,972 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [62, 1, 16, 30, 33, 0, 54, 57, 52, 15, 6, 63, 10, 64, 21, 32, 25, 31, 12, 22, 17, 4, 65, 5, 58, 18, 23, 27, 24, 19, 20, 35, 8, 3, 26, 34, 48, 47, 42, 44, 38, 43, 49, 50, 37, 36, 46, 29, 13, 9, 2, 55, 53, 11, 60, 56, 14, 7, 28, 45, 39, 41, 51, 40, 59, 61], 'cur_cost': 56257.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:12,972 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 56257.00)
2025-08-05 09:52:12,972 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:12,972 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:12,972 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:12,982 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-05 09:52:12,983 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,983 - ExplorationExpert - INFO - 探索路径生成完成，成本: 65946.0, 路径长度: 66, 收集中间解: 0
2025-08-05 09:52:12,983 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [44, 14, 6, 12, 18, 31, 17, 22, 15, 7, 61, 10, 1, 11, 19, 26, 5, 55, 4, 54, 59, 62, 0, 20, 33, 21, 35, 32, 25, 8, 63, 52, 23, 13, 37, 43, 34, 16, 47, 51, 36, 40, 39, 27, 29, 46, 30, 28, 3, 65, 57, 58, 2, 60, 48, 45, 50, 38, 49, 24, 41, 9, 56, 53, 64, 42], 'cur_cost': 65946.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:12,984 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 65946.00)
2025-08-05 09:52:12,984 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 09:52:12,984 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:12,984 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:12,984 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 107849.0
2025-08-05 09:52:12,998 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 09:52:12,998 - ExploitationExpert - INFO - res_population_costs: [9534.0, 9521]
2025-08-05 09:52:12,998 - ExploitationExpert - INFO - res_populations: [array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-05 09:52:12,999 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:12,999 - ExploitationExpert - INFO - populations: [{'tour': [10, 54, 6, 63, 8, 20, 24, 26, 31, 23, 5, 60, 61, 57, 22, 30, 11, 1, 65, 64, 56, 19, 7, 17, 34, 36, 28, 15, 12, 32, 14, 35, 3, 52, 16, 27, 37, 4, 2, 13, 48, 40, 42, 45, 49, 21, 9, 62, 18, 46, 41, 38, 44, 47, 43, 39, 25, 0, 53, 58, 50, 33, 51, 29, 55, 59], 'cur_cost': 64268.0}, {'tour': [6, 20, 24, 60, 1, 56, 34, 36, 28, 35, 37, 48, 40, 45, 21, 62, 46, 38, 44, 47, 43, 39, 0, 31, 4, 15, 33, 63, 57, 26, 54, 9, 17, 50, 51, 14, 49, 19, 65, 8, 22, 30, 29, 42, 27, 18, 32, 10, 11, 25, 16, 3, 12, 53, 55, 61, 41, 23, 59, 5, 7, 2, 64, 13, 58, 52], 'cur_cost': 80219.0}, {'tour': [10, 54, 60, 1, 34, 26, 28, 23, 37, 48, 61, 57, 21, 46, 44, 19, 7, 17, 4, 15, 33, 14, 52, 49, 2, 8, 40, 42, 45, 27, 18, 9, 59, 41, 38, 58, 53, 43, 39, 0, 65, 51, 29, 63, 30, 24, 3, 25, 36, 12, 16, 50, 47, 11, 56, 5, 22, 31, 62, 55, 6, 13, 20, 64, 32, 35], 'cur_cost': 88190.0}, {'tour': array([52, 49,  4, 12, 58, 14, 19, 48, 37, 25, 21, 15, 44,  9, 40, 22, 36,
       24, 35, 30, 10, 45, 17, 46, 59,  8, 63, 61, 20, 41, 56, 38, 13, 57,
        6,  3, 33, 47, 31, 50, 32, 54, 29, 34, 53, 11, 18,  0,  2, 16, 64,
       51, 55,  1, 65, 43,  5, 23, 39, 26,  7, 62, 42, 60, 28, 27],
      dtype=int64), 'cur_cost': 109064.0}, {'tour': [65, 59, 11, 61, 63, 55, 52, 18, 20, 16, 14, 21, 19, 36, 25, 13, 33, 3, 62, 64, 57, 4, 22, 17, 32, 30, 27, 7, 8, 0, 5, 10, 23, 31, 9, 58, 56, 53, 49, 50, 45, 38, 48, 51, 34, 35, 1, 26, 6, 29, 15, 2, 60, 47, 44, 39, 40, 41, 12, 37, 24, 28, 43, 46, 42, 54], 'cur_cost': 50209.0}, {'tour': [62, 1, 16, 30, 33, 0, 54, 57, 52, 15, 6, 63, 10, 64, 21, 32, 25, 31, 12, 22, 17, 4, 65, 5, 58, 18, 23, 27, 24, 19, 20, 35, 8, 3, 26, 34, 48, 47, 42, 44, 38, 43, 49, 50, 37, 36, 46, 29, 13, 9, 2, 55, 53, 11, 60, 56, 14, 7, 28, 45, 39, 41, 51, 40, 59, 61], 'cur_cost': 56257.0}, {'tour': [44, 14, 6, 12, 18, 31, 17, 22, 15, 7, 61, 10, 1, 11, 19, 26, 5, 55, 4, 54, 59, 62, 0, 20, 33, 21, 35, 32, 25, 8, 63, 52, 23, 13, 37, 43, 34, 16, 47, 51, 36, 40, 39, 27, 29, 46, 30, 28, 3, 65, 57, 58, 2, 60, 48, 45, 50, 38, 49, 24, 41, 9, 56, 53, 64, 42], 'cur_cost': 65946.0}, {'tour': array([25, 46, 11, 54, 38, 56, 58, 61, 17, 32, 40, 44, 16, 42, 49, 57, 18,
        6, 37,  0, 63, 12, 35, 45, 34,  8, 19, 55, 64,  4,  2, 31,  5, 22,
       24, 65, 43, 59, 39, 51, 26, 13, 33, 10, 27, 48,  9, 50, 28, 14, 36,
       20, 21, 62, 52,  1, 29,  3,  7, 47, 53, 41, 23, 60, 30, 15],
      dtype=int64), 'cur_cost': 107849.0}, {'tour': array([51, 19,  5, 45, 36, 31, 11, 14, 39, 27, 48, 59, 23, 56, 17,  9,  4,
        8, 63, 65, 64, 20, 37, 53, 32, 29, 35, 38, 42, 61, 49, 52, 43, 54,
       44, 13, 50, 15, 62, 41, 33, 28, 25, 55, 26, 46, 24, 47, 21, 40,  7,
       22, 16,  0, 57, 58, 10, 30,  1, 60, 18, 12, 34,  3,  2,  6],
      dtype=int64), 'cur_cost': 101441.0}, {'tour': array([27, 20, 38, 64, 65, 61,  0, 21, 48, 16, 35, 40, 47, 54, 52, 11, 22,
       58, 31, 25, 28, 53, 62,  2, 19,  8, 12,  5, 50, 39, 33, 49, 56, 24,
       15, 17, 63, 59, 41, 36, 51,  6, 30,  9, 14, 34,  1, 43, 44, 32, 13,
       45,  7, 29,  4, 46, 57, 37, 42, 60, 55, 26,  3, 18, 10, 23],
      dtype=int64), 'cur_cost': 103638.0}]
2025-08-05 09:52:13,002 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:13,002 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 314, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 314, 'cache_hits': 0, 'similarity_calculations': 1591, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:13,003 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([25, 46, 11, 54, 38, 56, 58, 61, 17, 32, 40, 44, 16, 42, 49, 57, 18,
        6, 37,  0, 63, 12, 35, 45, 34,  8, 19, 55, 64,  4,  2, 31,  5, 22,
       24, 65, 43, 59, 39, 51, 26, 13, 33, 10, 27, 48,  9, 50, 28, 14, 36,
       20, 21, 62, 52,  1, 29,  3,  7, 47, 53, 41, 23, 60, 30, 15],
      dtype=int64), 'cur_cost': 107849.0, 'intermediate_solutions': [{'tour': array([53, 34, 22,  2, 35, 13, 26, 14, 41, 36,  6, 44, 15, 48, 47, 33,  1,
       58, 55, 23,  8, 62, 25, 46, 16, 57, 61,  0, 20, 32, 31, 18, 59, 50,
       51, 40, 12, 56, 37, 29,  4, 49,  5, 63, 30, 54, 28, 65, 60, 24, 10,
       42, 21, 39, 52,  9, 19,  3, 17, 64, 27, 11, 43, 45,  7, 38],
      dtype=int64), 'cur_cost': 110889.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2, 53, 34, 22, 35, 13, 26, 14, 41, 36,  6, 44, 15, 48, 47, 33,  1,
       58, 55, 23,  8, 62, 25, 46, 16, 57, 61,  0, 20, 32, 31, 18, 59, 50,
       51, 40, 12, 56, 37, 29,  4, 49,  5, 63, 30, 54, 28, 65, 60, 24, 10,
       42, 21, 39, 52,  9, 19,  3, 17, 64, 27, 11, 43, 45,  7, 38],
      dtype=int64), 'cur_cost': 109511.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([35,  2, 53, 34, 22, 13, 26, 14, 41, 36,  6, 44, 15, 48, 47, 33,  1,
       58, 55, 23,  8, 62, 25, 46, 16, 57, 61,  0, 20, 32, 31, 18, 59, 50,
       51, 40, 12, 56, 37, 29,  4, 49,  5, 63, 30, 54, 28, 65, 60, 24, 10,
       42, 21, 39, 52,  9, 19,  3, 17, 64, 27, 11, 43, 45,  7, 38],
      dtype=int64), 'cur_cost': 108793.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([22,  2, 53, 34, 35, 13, 26, 14, 41, 36,  6, 44, 15, 48, 47, 33,  1,
       58, 55, 23,  8, 62, 25, 46, 16, 57, 61,  0, 20, 32, 31, 18, 59, 50,
       51, 40, 12, 56, 37, 29,  4, 49,  5, 63, 30, 54, 28, 65, 60, 24, 10,
       42, 21, 39, 52,  9, 19,  3, 17, 64, 27, 11, 43, 45,  7, 38],
      dtype=int64), 'cur_cost': 107283.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([22, 35,  2, 53, 34, 13, 26, 14, 41, 36,  6, 44, 15, 48, 47, 33,  1,
       58, 55, 23,  8, 62, 25, 46, 16, 57, 61,  0, 20, 32, 31, 18, 59, 50,
       51, 40, 12, 56, 37, 29,  4, 49,  5, 63, 30, 54, 28, 65, 60, 24, 10,
       42, 21, 39, 52,  9, 19,  3, 17, 64, 27, 11, 43, 45,  7, 38],
      dtype=int64), 'cur_cost': 109260.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:13,003 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 107849.00)
2025-08-05 09:52:13,003 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:52:13,004 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:52:13,004 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:13,012 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-05 09:52:13,013 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,014 - ExplorationExpert - INFO - 探索路径生成完成，成本: 53487.0, 路径长度: 66, 收集中间解: 0
2025-08-05 09:52:13,014 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [35, 19, 17, 20, 33, 0, 14, 6, 7, 60, 9, 12, 34, 37, 23, 5, 63, 1, 22, 21, 11, 62, 39, 38, 41, 51, 47, 48, 16, 26, 32, 24, 31, 3, 57, 54, 65, 4, 59, 15, 10, 18, 30, 25, 36, 2, 53, 58, 55, 56, 52, 64, 43, 42, 50, 46, 44, 40, 49, 27, 28, 29, 13, 8, 61, 45], 'cur_cost': 53487.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:13,014 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 53487.00)
2025-08-05 09:52:13,015 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:13,015 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:13,015 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:13,019 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 09:52:13,020 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,020 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12807.0, 路径长度: 66, 收集中间解: 0
2025-08-05 09:52:13,020 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 12, 16, 8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 13, 20, 21, 19, 18, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12807.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:13,021 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 12807.00)
2025-08-05 09:52:13,021 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:13,021 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:13,023 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [10, 54, 6, 63, 8, 20, 24, 26, 31, 23, 5, 60, 61, 57, 22, 30, 11, 1, 65, 64, 56, 19, 7, 17, 34, 36, 28, 15, 12, 32, 14, 35, 3, 52, 16, 27, 37, 4, 2, 13, 48, 40, 42, 45, 49, 21, 9, 62, 18, 46, 41, 38, 44, 47, 43, 39, 25, 0, 53, 58, 50, 33, 51, 29, 55, 59], 'cur_cost': 64268.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [6, 20, 24, 60, 1, 56, 34, 36, 28, 35, 37, 48, 40, 45, 21, 62, 46, 38, 44, 47, 43, 39, 0, 31, 4, 15, 33, 63, 57, 26, 54, 9, 17, 50, 51, 14, 49, 19, 65, 8, 22, 30, 29, 42, 27, 18, 32, 10, 11, 25, 16, 3, 12, 53, 55, 61, 41, 23, 59, 5, 7, 2, 64, 13, 58, 52], 'cur_cost': 80219.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [10, 54, 60, 1, 34, 26, 28, 23, 37, 48, 61, 57, 21, 46, 44, 19, 7, 17, 4, 15, 33, 14, 52, 49, 2, 8, 40, 42, 45, 27, 18, 9, 59, 41, 38, 58, 53, 43, 39, 0, 65, 51, 29, 63, 30, 24, 3, 25, 36, 12, 16, 50, 47, 11, 56, 5, 22, 31, 62, 55, 6, 13, 20, 64, 32, 35], 'cur_cost': 88190.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([52, 49,  4, 12, 58, 14, 19, 48, 37, 25, 21, 15, 44,  9, 40, 22, 36,
       24, 35, 30, 10, 45, 17, 46, 59,  8, 63, 61, 20, 41, 56, 38, 13, 57,
        6,  3, 33, 47, 31, 50, 32, 54, 29, 34, 53, 11, 18,  0,  2, 16, 64,
       51, 55,  1, 65, 43,  5, 23, 39, 26,  7, 62, 42, 60, 28, 27],
      dtype=int64), 'cur_cost': 109064.0, 'intermediate_solutions': [{'tour': array([33, 20, 56,  1, 16, 29, 60, 28, 52, 36, 65, 39, 35, 22,  5, 30, 38,
       57, 49, 47, 62, 18, 23,  0, 61, 44, 51, 32, 19, 54, 25, 11, 42, 14,
       40, 41, 64,  4, 55, 34, 37, 58,  2, 63, 45, 48, 31, 21,  6, 12, 53,
       43,  7,  3, 24, 26, 59, 15, 50, 17, 46, 27,  8,  9, 13, 10],
      dtype=int64), 'cur_cost': 113014.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1, 33, 20, 56, 16, 29, 60, 28, 52, 36, 65, 39, 35, 22,  5, 30, 38,
       57, 49, 47, 62, 18, 23,  0, 61, 44, 51, 32, 19, 54, 25, 11, 42, 14,
       40, 41, 64,  4, 55, 34, 37, 58,  2, 63, 45, 48, 31, 21,  6, 12, 53,
       43,  7,  3, 24, 26, 59, 15, 50, 17, 46, 27,  8,  9, 13, 10],
      dtype=int64), 'cur_cost': 112637.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([16,  1, 33, 20, 56, 29, 60, 28, 52, 36, 65, 39, 35, 22,  5, 30, 38,
       57, 49, 47, 62, 18, 23,  0, 61, 44, 51, 32, 19, 54, 25, 11, 42, 14,
       40, 41, 64,  4, 55, 34, 37, 58,  2, 63, 45, 48, 31, 21,  6, 12, 53,
       43,  7,  3, 24, 26, 59, 15, 50, 17, 46, 27,  8,  9, 13, 10],
      dtype=int64), 'cur_cost': 114932.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([56,  1, 33, 20, 16, 29, 60, 28, 52, 36, 65, 39, 35, 22,  5, 30, 38,
       57, 49, 47, 62, 18, 23,  0, 61, 44, 51, 32, 19, 54, 25, 11, 42, 14,
       40, 41, 64,  4, 55, 34, 37, 58,  2, 63, 45, 48, 31, 21,  6, 12, 53,
       43,  7,  3, 24, 26, 59, 15, 50, 17, 46, 27,  8,  9, 13, 10],
      dtype=int64), 'cur_cost': 110802.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([56, 16,  1, 33, 20, 29, 60, 28, 52, 36, 65, 39, 35, 22,  5, 30, 38,
       57, 49, 47, 62, 18, 23,  0, 61, 44, 51, 32, 19, 54, 25, 11, 42, 14,
       40, 41, 64,  4, 55, 34, 37, 58,  2, 63, 45, 48, 31, 21,  6, 12, 53,
       43,  7,  3, 24, 26, 59, 15, 50, 17, 46, 27,  8,  9, 13, 10],
      dtype=int64), 'cur_cost': 112947.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [65, 59, 11, 61, 63, 55, 52, 18, 20, 16, 14, 21, 19, 36, 25, 13, 33, 3, 62, 64, 57, 4, 22, 17, 32, 30, 27, 7, 8, 0, 5, 10, 23, 31, 9, 58, 56, 53, 49, 50, 45, 38, 48, 51, 34, 35, 1, 26, 6, 29, 15, 2, 60, 47, 44, 39, 40, 41, 12, 37, 24, 28, 43, 46, 42, 54], 'cur_cost': 50209.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [62, 1, 16, 30, 33, 0, 54, 57, 52, 15, 6, 63, 10, 64, 21, 32, 25, 31, 12, 22, 17, 4, 65, 5, 58, 18, 23, 27, 24, 19, 20, 35, 8, 3, 26, 34, 48, 47, 42, 44, 38, 43, 49, 50, 37, 36, 46, 29, 13, 9, 2, 55, 53, 11, 60, 56, 14, 7, 28, 45, 39, 41, 51, 40, 59, 61], 'cur_cost': 56257.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [44, 14, 6, 12, 18, 31, 17, 22, 15, 7, 61, 10, 1, 11, 19, 26, 5, 55, 4, 54, 59, 62, 0, 20, 33, 21, 35, 32, 25, 8, 63, 52, 23, 13, 37, 43, 34, 16, 47, 51, 36, 40, 39, 27, 29, 46, 30, 28, 3, 65, 57, 58, 2, 60, 48, 45, 50, 38, 49, 24, 41, 9, 56, 53, 64, 42], 'cur_cost': 65946.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([25, 46, 11, 54, 38, 56, 58, 61, 17, 32, 40, 44, 16, 42, 49, 57, 18,
        6, 37,  0, 63, 12, 35, 45, 34,  8, 19, 55, 64,  4,  2, 31,  5, 22,
       24, 65, 43, 59, 39, 51, 26, 13, 33, 10, 27, 48,  9, 50, 28, 14, 36,
       20, 21, 62, 52,  1, 29,  3,  7, 47, 53, 41, 23, 60, 30, 15],
      dtype=int64), 'cur_cost': 107849.0, 'intermediate_solutions': [{'tour': array([53, 34, 22,  2, 35, 13, 26, 14, 41, 36,  6, 44, 15, 48, 47, 33,  1,
       58, 55, 23,  8, 62, 25, 46, 16, 57, 61,  0, 20, 32, 31, 18, 59, 50,
       51, 40, 12, 56, 37, 29,  4, 49,  5, 63, 30, 54, 28, 65, 60, 24, 10,
       42, 21, 39, 52,  9, 19,  3, 17, 64, 27, 11, 43, 45,  7, 38],
      dtype=int64), 'cur_cost': 110889.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2, 53, 34, 22, 35, 13, 26, 14, 41, 36,  6, 44, 15, 48, 47, 33,  1,
       58, 55, 23,  8, 62, 25, 46, 16, 57, 61,  0, 20, 32, 31, 18, 59, 50,
       51, 40, 12, 56, 37, 29,  4, 49,  5, 63, 30, 54, 28, 65, 60, 24, 10,
       42, 21, 39, 52,  9, 19,  3, 17, 64, 27, 11, 43, 45,  7, 38],
      dtype=int64), 'cur_cost': 109511.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([35,  2, 53, 34, 22, 13, 26, 14, 41, 36,  6, 44, 15, 48, 47, 33,  1,
       58, 55, 23,  8, 62, 25, 46, 16, 57, 61,  0, 20, 32, 31, 18, 59, 50,
       51, 40, 12, 56, 37, 29,  4, 49,  5, 63, 30, 54, 28, 65, 60, 24, 10,
       42, 21, 39, 52,  9, 19,  3, 17, 64, 27, 11, 43, 45,  7, 38],
      dtype=int64), 'cur_cost': 108793.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([22,  2, 53, 34, 35, 13, 26, 14, 41, 36,  6, 44, 15, 48, 47, 33,  1,
       58, 55, 23,  8, 62, 25, 46, 16, 57, 61,  0, 20, 32, 31, 18, 59, 50,
       51, 40, 12, 56, 37, 29,  4, 49,  5, 63, 30, 54, 28, 65, 60, 24, 10,
       42, 21, 39, 52,  9, 19,  3, 17, 64, 27, 11, 43, 45,  7, 38],
      dtype=int64), 'cur_cost': 107283.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([22, 35,  2, 53, 34, 13, 26, 14, 41, 36,  6, 44, 15, 48, 47, 33,  1,
       58, 55, 23,  8, 62, 25, 46, 16, 57, 61,  0, 20, 32, 31, 18, 59, 50,
       51, 40, 12, 56, 37, 29,  4, 49,  5, 63, 30, 54, 28, 65, 60, 24, 10,
       42, 21, 39, 52,  9, 19,  3, 17, 64, 27, 11, 43, 45,  7, 38],
      dtype=int64), 'cur_cost': 109260.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [35, 19, 17, 20, 33, 0, 14, 6, 7, 60, 9, 12, 34, 37, 23, 5, 63, 1, 22, 21, 11, 62, 39, 38, 41, 51, 47, 48, 16, 26, 32, 24, 31, 3, 57, 54, 65, 4, 59, 15, 10, 18, 30, 25, 36, 2, 53, 58, 55, 56, 52, 64, 43, 42, 50, 46, 44, 40, 49, 27, 28, 29, 13, 8, 61, 45], 'cur_cost': 53487.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 16, 8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 13, 20, 21, 19, 18, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12807.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:13,023 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:13,023 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:13,027 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12807.000, 多样性=0.980
2025-08-05 09:52:13,027 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 09:52:13,027 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 09:52:13,027 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:13,028 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.05666996560308821, 'best_improvement': -0.2949443882709808}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.004484304932735417}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.01611256726605891, 'recent_improvements': [0.04562600420120888, -0.03125151422123978, 0.0778511387333267], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.9696969696969697, 'new_diversity': 0.9696969696969697, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 09:52:13,028 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 09:52:13,028 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-08-05 09:52:13,028 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 09:52:13,028 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:13,029 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12807.000, 多样性=0.980
2025-08-05 09:52:13,030 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:13,032 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.980
2025-08-05 09:52:13,033 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:13,034 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.970
2025-08-05 09:52:13,036 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 09:52:13,036 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:13,036 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-05 09:52:13,036 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-05 09:52:13,064 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.083, 适应度梯度: 1145.333, 聚类评分: 0.000, 覆盖率: 0.143, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:13,065 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 09:52:13,065 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:13,065 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite13_66
2025-08-05 09:52:13,087 - visualization.landscape_visualizer - INFO - 插值约束: 300 个点被约束到最小值 9521.00
2025-08-05 09:52:13,185 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite13_66\landscape_composite13_66_iter_122_20250805_095213.html
2025-08-05 09:52:13,234 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite13_66\dashboard_composite13_66_iter_122_20250805_095213.html
2025-08-05 09:52:13,235 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 122
2025-08-05 09:52:13,235 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 09:52:13,235 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1986秒
2025-08-05 09:52:13,235 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.08333333333333333, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 1145.3333333333328, 'local_optima_density': 0.08333333333333333, 'gradient_variance': 739841675.3488889, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1429, 'fitness_entropy': 0.9873179343530822, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.143)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 1145.333)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358733.065805, 'performance_metrics': {}}}
2025-08-05 09:52:13,235 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:13,236 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:13,236 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:13,236 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:13,236 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:52:13,236 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 09:52:13,236 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:52:13,237 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:13,237 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:13,237 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:52:13,237 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:13,237 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:13,237 - experts.management.collaboration_manager - INFO - 识别精英个体: {9, 4} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:13,237 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:13,237 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:13,238 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:13,239 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 09:52:13,239 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,240 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,240 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,240 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,240 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12448.0, 路径长度: 66, 收集中间解: 3
2025-08-05 09:52:13,240 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 4, 20, 17, 12, 22, 23, 16, 18, 19, 13, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12448.0, 'intermediate_solutions': [{'tour': [10, 54, 6, 63, 8, 20, 24, 26, 31, 23, 5, 60, 61, 57, 22, 30, 11, 1, 65, 64, 56, 19, 7, 17, 34, 36, 28, 15, 12, 32, 14, 35, 3, 52, 16, 27, 37, 4, 2, 13, 48, 40, 42, 45, 49, 21, 29, 62, 18, 46, 41, 38, 44, 47, 43, 39, 25, 0, 53, 58, 50, 33, 51, 9, 55, 59], 'cur_cost': 64582.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 54, 6, 63, 8, 20, 24, 26, 31, 23, 5, 60, 61, 57, 22, 30, 11, 1, 65, 64, 56, 19, 7, 17, 34, 36, 28, 15, 12, 32, 14, 35, 3, 4, 37, 27, 16, 52, 2, 13, 48, 40, 42, 45, 49, 21, 9, 62, 18, 46, 41, 38, 44, 47, 43, 39, 25, 0, 53, 58, 50, 33, 51, 29, 55, 59], 'cur_cost': 64214.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 54, 6, 63, 8, 20, 24, 26, 31, 23, 5, 60, 61, 57, 22, 30, 11, 1, 65, 64, 56, 19, 7, 17, 34, 36, 28, 15, 12, 32, 14, 35, 3, 52, 16, 27, 37, 2, 13, 48, 40, 42, 45, 49, 21, 9, 62, 18, 46, 41, 38, 4, 44, 47, 43, 39, 25, 0, 53, 58, 50, 33, 51, 29, 55, 59], 'cur_cost': 69226.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:13,241 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 12448.00)
2025-08-05 09:52:13,241 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:13,241 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:13,241 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:13,244 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 09:52:13,245 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,245 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,245 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,245 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,245 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12785.0, 路径长度: 66, 收集中间解: 3
2025-08-05 09:52:13,245 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 6, 1, 7, 3, 9, 11, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12785.0, 'intermediate_solutions': [{'tour': [6, 20, 24, 60, 1, 56, 34, 36, 28, 35, 37, 48, 40, 45, 21, 62, 46, 38, 55, 47, 43, 39, 0, 31, 4, 15, 33, 63, 57, 26, 54, 9, 17, 50, 51, 14, 49, 19, 65, 8, 22, 30, 29, 42, 27, 18, 32, 10, 11, 25, 16, 3, 12, 53, 44, 61, 41, 23, 59, 5, 7, 2, 64, 13, 58, 52], 'cur_cost': 91297.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 20, 24, 60, 1, 56, 34, 36, 28, 35, 37, 48, 40, 45, 21, 62, 46, 38, 44, 47, 43, 39, 0, 31, 4, 15, 51, 50, 17, 9, 54, 26, 57, 63, 33, 14, 49, 19, 65, 8, 22, 30, 29, 42, 27, 18, 32, 10, 11, 25, 16, 3, 12, 53, 55, 61, 41, 23, 59, 5, 7, 2, 64, 13, 58, 52], 'cur_cost': 80268.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 20, 24, 60, 1, 56, 34, 36, 28, 19, 35, 37, 48, 40, 45, 21, 62, 46, 38, 44, 47, 43, 39, 0, 31, 4, 15, 33, 63, 57, 26, 54, 9, 17, 50, 51, 14, 49, 65, 8, 22, 30, 29, 42, 27, 18, 32, 10, 11, 25, 16, 3, 12, 53, 55, 61, 41, 23, 59, 5, 7, 2, 64, 13, 58, 52], 'cur_cost': 82046.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:13,245 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 12785.00)
2025-08-05 09:52:13,245 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:13,245 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:13,245 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:13,251 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-05 09:52:13,251 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,251 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,252 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,252 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,252 - ExplorationExpert - INFO - 探索路径生成完成，成本: 55451.0, 路径长度: 66, 收集中间解: 3
2025-08-05 09:52:13,252 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [34, 17, 6, 63, 54, 55, 11, 61, 60, 59, 12, 27, 13, 28, 5, 57, 20, 18, 25, 33, 15, 30, 22, 8, 52, 0, 2, 62, 19, 4, 58, 39, 47, 43, 51, 50, 44, 46, 49, 38, 42, 23, 26, 16, 29, 35, 14, 37, 3, 10, 1, 7, 9, 65, 64, 56, 40, 41, 48, 45, 21, 36, 31, 24, 32, 53], 'cur_cost': 55451.0, 'intermediate_solutions': [{'tour': [10, 54, 60, 1, 34, 26, 28, 23, 37, 48, 61, 57, 21, 46, 44, 19, 7, 17, 4, 15, 33, 14, 52, 49, 2, 8, 40, 42, 45, 27, 18, 9, 59, 41, 38, 53, 58, 43, 39, 0, 65, 51, 29, 63, 30, 24, 3, 25, 36, 12, 16, 50, 47, 11, 56, 5, 22, 31, 62, 55, 6, 13, 20, 64, 32, 35], 'cur_cost': 88191.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 54, 60, 1, 34, 26, 28, 23, 37, 48, 61, 57, 21, 46, 44, 19, 7, 17, 4, 15, 33, 14, 52, 49, 2, 8, 40, 42, 45, 27, 18, 9, 59, 41, 38, 58, 53, 43, 39, 0, 65, 51, 29, 63, 30, 24, 3, 25, 36, 12, 16, 50, 47, 6, 55, 62, 31, 22, 5, 56, 11, 13, 20, 64, 32, 35], 'cur_cost': 88166.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [20, 10, 54, 60, 1, 34, 26, 28, 23, 37, 48, 61, 57, 21, 46, 44, 19, 7, 17, 4, 15, 33, 14, 52, 49, 2, 8, 40, 42, 45, 27, 18, 9, 59, 41, 38, 58, 53, 43, 39, 0, 65, 51, 29, 63, 30, 24, 3, 25, 36, 12, 16, 50, 47, 11, 56, 5, 22, 31, 62, 55, 6, 13, 64, 32, 35], 'cur_cost': 88674.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:13,253 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 55451.00)
2025-08-05 09:52:13,253 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:52:13,253 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:13,253 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:13,253 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 106768.0
2025-08-05 09:52:13,265 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 09:52:13,265 - ExploitationExpert - INFO - res_population_costs: [9521, 9534.0]
2025-08-05 09:52:13,265 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64)]
2025-08-05 09:52:13,266 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:13,266 - ExploitationExpert - INFO - populations: [{'tour': [0, 4, 20, 17, 12, 22, 23, 16, 18, 19, 13, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12448.0}, {'tour': [0, 12, 6, 1, 7, 3, 9, 11, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12785.0}, {'tour': [34, 17, 6, 63, 54, 55, 11, 61, 60, 59, 12, 27, 13, 28, 5, 57, 20, 18, 25, 33, 15, 30, 22, 8, 52, 0, 2, 62, 19, 4, 58, 39, 47, 43, 51, 50, 44, 46, 49, 38, 42, 23, 26, 16, 29, 35, 14, 37, 3, 10, 1, 7, 9, 65, 64, 56, 40, 41, 48, 45, 21, 36, 31, 24, 32, 53], 'cur_cost': 55451.0}, {'tour': array([ 1, 60, 33, 52, 12, 35, 43, 55, 42, 58, 40, 29, 50, 41, 45,  3, 28,
       39,  5, 26, 34, 16, 62, 56, 61, 18, 10, 19, 30, 53,  4, 59, 31, 49,
       17, 46, 11,  0, 36, 64, 51, 13, 27, 21,  9,  8, 54, 37,  2, 15, 44,
       47, 32, 57, 20, 22, 25, 24, 48,  7, 14, 65, 63, 38, 23,  6],
      dtype=int64), 'cur_cost': 106768.0}, {'tour': [65, 59, 11, 61, 63, 55, 52, 18, 20, 16, 14, 21, 19, 36, 25, 13, 33, 3, 62, 64, 57, 4, 22, 17, 32, 30, 27, 7, 8, 0, 5, 10, 23, 31, 9, 58, 56, 53, 49, 50, 45, 38, 48, 51, 34, 35, 1, 26, 6, 29, 15, 2, 60, 47, 44, 39, 40, 41, 12, 37, 24, 28, 43, 46, 42, 54], 'cur_cost': 50209.0}, {'tour': [62, 1, 16, 30, 33, 0, 54, 57, 52, 15, 6, 63, 10, 64, 21, 32, 25, 31, 12, 22, 17, 4, 65, 5, 58, 18, 23, 27, 24, 19, 20, 35, 8, 3, 26, 34, 48, 47, 42, 44, 38, 43, 49, 50, 37, 36, 46, 29, 13, 9, 2, 55, 53, 11, 60, 56, 14, 7, 28, 45, 39, 41, 51, 40, 59, 61], 'cur_cost': 56257.0}, {'tour': [44, 14, 6, 12, 18, 31, 17, 22, 15, 7, 61, 10, 1, 11, 19, 26, 5, 55, 4, 54, 59, 62, 0, 20, 33, 21, 35, 32, 25, 8, 63, 52, 23, 13, 37, 43, 34, 16, 47, 51, 36, 40, 39, 27, 29, 46, 30, 28, 3, 65, 57, 58, 2, 60, 48, 45, 50, 38, 49, 24, 41, 9, 56, 53, 64, 42], 'cur_cost': 65946.0}, {'tour': [25, 46, 11, 54, 38, 56, 58, 61, 17, 32, 40, 44, 16, 42, 49, 57, 18, 6, 37, 0, 63, 12, 35, 45, 34, 8, 19, 55, 64, 4, 2, 31, 5, 22, 24, 65, 43, 59, 39, 51, 26, 13, 33, 10, 27, 48, 9, 50, 28, 14, 36, 20, 21, 62, 52, 1, 29, 3, 7, 47, 53, 41, 23, 60, 30, 15], 'cur_cost': 107849.0}, {'tour': [35, 19, 17, 20, 33, 0, 14, 6, 7, 60, 9, 12, 34, 37, 23, 5, 63, 1, 22, 21, 11, 62, 39, 38, 41, 51, 47, 48, 16, 26, 32, 24, 31, 3, 57, 54, 65, 4, 59, 15, 10, 18, 30, 25, 36, 2, 53, 58, 55, 56, 52, 64, 43, 42, 50, 46, 44, 40, 49, 27, 28, 29, 13, 8, 61, 45], 'cur_cost': 53487.0}, {'tour': [0, 12, 16, 8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 13, 20, 21, 19, 18, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12807.0}]
2025-08-05 09:52:13,267 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:13,267 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 315, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 315, 'cache_hits': 0, 'similarity_calculations': 1593, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:13,268 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 1, 60, 33, 52, 12, 35, 43, 55, 42, 58, 40, 29, 50, 41, 45,  3, 28,
       39,  5, 26, 34, 16, 62, 56, 61, 18, 10, 19, 30, 53,  4, 59, 31, 49,
       17, 46, 11,  0, 36, 64, 51, 13, 27, 21,  9,  8, 54, 37,  2, 15, 44,
       47, 32, 57, 20, 22, 25, 24, 48,  7, 14, 65, 63, 38, 23,  6],
      dtype=int64), 'cur_cost': 106768.0, 'intermediate_solutions': [{'tour': array([ 4, 49, 52, 12, 58, 14, 19, 48, 37, 25, 21, 15, 44,  9, 40, 22, 36,
       24, 35, 30, 10, 45, 17, 46, 59,  8, 63, 61, 20, 41, 56, 38, 13, 57,
        6,  3, 33, 47, 31, 50, 32, 54, 29, 34, 53, 11, 18,  0,  2, 16, 64,
       51, 55,  1, 65, 43,  5, 23, 39, 26,  7, 62, 42, 60, 28, 27]), 'cur_cost': 108847.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([12,  4, 49, 52, 58, 14, 19, 48, 37, 25, 21, 15, 44,  9, 40, 22, 36,
       24, 35, 30, 10, 45, 17, 46, 59,  8, 63, 61, 20, 41, 56, 38, 13, 57,
        6,  3, 33, 47, 31, 50, 32, 54, 29, 34, 53, 11, 18,  0,  2, 16, 64,
       51, 55,  1, 65, 43,  5, 23, 39, 26,  7, 62, 42, 60, 28, 27]), 'cur_cost': 105484.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([58, 12,  4, 49, 52, 14, 19, 48, 37, 25, 21, 15, 44,  9, 40, 22, 36,
       24, 35, 30, 10, 45, 17, 46, 59,  8, 63, 61, 20, 41, 56, 38, 13, 57,
        6,  3, 33, 47, 31, 50, 32, 54, 29, 34, 53, 11, 18,  0,  2, 16, 64,
       51, 55,  1, 65, 43,  5, 23, 39, 26,  7, 62, 42, 60, 28, 27]), 'cur_cost': 109085.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([52, 12,  4, 49, 58, 14, 19, 48, 37, 25, 21, 15, 44,  9, 40, 22, 36,
       24, 35, 30, 10, 45, 17, 46, 59,  8, 63, 61, 20, 41, 56, 38, 13, 57,
        6,  3, 33, 47, 31, 50, 32, 54, 29, 34, 53, 11, 18,  0,  2, 16, 64,
       51, 55,  1, 65, 43,  5, 23, 39, 26,  7, 62, 42, 60, 28, 27]), 'cur_cost': 109040.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([52, 58, 12,  4, 49, 14, 19, 48, 37, 25, 21, 15, 44,  9, 40, 22, 36,
       24, 35, 30, 10, 45, 17, 46, 59,  8, 63, 61, 20, 41, 56, 38, 13, 57,
        6,  3, 33, 47, 31, 50, 32, 54, 29, 34, 53, 11, 18,  0,  2, 16, 64,
       51, 55,  1, 65, 43,  5, 23, 39, 26,  7, 62, 42, 60, 28, 27]), 'cur_cost': 105808.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:13,269 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 106768.00)
2025-08-05 09:52:13,269 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:13,269 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:13,269 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:13,272 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 09:52:13,272 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,272 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,272 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,273 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,273 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12428.0, 路径长度: 66, 收集中间解: 3
2025-08-05 09:52:13,273 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 5, 7, 19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12428.0, 'intermediate_solutions': [{'tour': [65, 59, 11, 61, 63, 55, 52, 18, 20, 16, 14, 21, 19, 36, 25, 13, 33, 3, 51, 64, 57, 4, 22, 17, 32, 30, 27, 7, 8, 0, 5, 10, 23, 31, 9, 58, 56, 53, 49, 50, 45, 38, 48, 62, 34, 35, 1, 26, 6, 29, 15, 2, 60, 47, 44, 39, 40, 41, 12, 37, 24, 28, 43, 46, 42, 54], 'cur_cost': 58041.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [65, 59, 11, 61, 63, 55, 52, 18, 20, 16, 14, 21, 19, 36, 25, 13, 33, 3, 62, 64, 57, 4, 22, 60, 2, 15, 29, 6, 26, 1, 35, 34, 51, 48, 38, 45, 50, 49, 53, 56, 58, 9, 31, 23, 10, 5, 0, 8, 7, 27, 30, 32, 17, 47, 44, 39, 40, 41, 12, 37, 24, 28, 43, 46, 42, 54], 'cur_cost': 50741.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [65, 59, 11, 61, 63, 55, 52, 18, 20, 16, 14, 21, 19, 60, 36, 25, 13, 33, 3, 62, 64, 57, 4, 22, 17, 32, 30, 27, 7, 8, 0, 5, 10, 23, 31, 9, 58, 56, 53, 49, 50, 45, 38, 48, 51, 34, 35, 1, 26, 6, 29, 15, 2, 47, 44, 39, 40, 41, 12, 37, 24, 28, 43, 46, 42, 54], 'cur_cost': 52657.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:13,273 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 12428.00)
2025-08-05 09:52:13,274 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:13,274 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:13,274 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:13,276 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 09:52:13,277 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,277 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,277 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,277 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,277 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12920.0, 路径长度: 66, 收集中间解: 3
2025-08-05 09:52:13,278 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 10, 19, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12920.0, 'intermediate_solutions': [{'tour': [62, 1, 16, 30, 33, 0, 54, 57, 52, 15, 6, 63, 10, 64, 21, 32, 25, 31, 12, 22, 17, 23, 65, 5, 58, 18, 4, 27, 24, 19, 20, 35, 8, 3, 26, 34, 48, 47, 42, 44, 38, 43, 49, 50, 37, 36, 46, 29, 13, 9, 2, 55, 53, 11, 60, 56, 14, 7, 28, 45, 39, 41, 51, 40, 59, 61], 'cur_cost': 58033.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [62, 1, 16, 30, 33, 0, 54, 57, 52, 15, 6, 63, 10, 64, 21, 32, 25, 31, 12, 22, 17, 4, 65, 5, 58, 18, 23, 27, 24, 19, 20, 35, 8, 3, 26, 34, 48, 47, 42, 44, 38, 46, 36, 37, 50, 49, 43, 29, 13, 9, 2, 55, 53, 11, 60, 56, 14, 7, 28, 45, 39, 41, 51, 40, 59, 61], 'cur_cost': 56201.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [62, 1, 16, 30, 33, 0, 54, 57, 52, 15, 6, 63, 10, 64, 21, 32, 25, 31, 12, 22, 17, 4, 58, 65, 5, 18, 23, 27, 24, 19, 20, 35, 8, 3, 26, 34, 48, 47, 42, 44, 38, 43, 49, 50, 37, 36, 46, 29, 13, 9, 2, 55, 53, 11, 60, 56, 14, 7, 28, 45, 39, 41, 51, 40, 59, 61], 'cur_cost': 54604.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:13,278 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 12920.00)
2025-08-05 09:52:13,278 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:13,278 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:13,278 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:13,284 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-05 09:52:13,284 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,284 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,285 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,285 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,285 - ExplorationExpert - INFO - 探索路径生成完成，成本: 53927.0, 路径长度: 66, 收集中间解: 3
2025-08-05 09:52:13,286 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [40, 18, 36, 27, 7, 10, 23, 15, 34, 11, 4, 56, 0, 1, 8, 63, 60, 55, 9, 20, 25, 28, 13, 16, 12, 33, 32, 24, 49, 43, 45, 51, 37, 47, 39, 22, 29, 19, 35, 46, 48, 31, 30, 44, 41, 50, 14, 5, 2, 52, 53, 64, 65, 57, 58, 61, 6, 54, 59, 3, 17, 21, 26, 42, 38, 62], 'cur_cost': 53927.0, 'intermediate_solutions': [{'tour': [44, 14, 52, 12, 18, 31, 17, 22, 15, 7, 61, 10, 1, 11, 19, 26, 5, 55, 4, 54, 59, 62, 0, 20, 33, 21, 35, 32, 25, 8, 63, 6, 23, 13, 37, 43, 34, 16, 47, 51, 36, 40, 39, 27, 29, 46, 30, 28, 3, 65, 57, 58, 2, 60, 48, 45, 50, 38, 49, 24, 41, 9, 56, 53, 64, 42], 'cur_cost': 67659.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [44, 14, 6, 12, 18, 31, 17, 22, 15, 7, 61, 10, 1, 11, 19, 26, 5, 55, 54, 4, 59, 62, 0, 20, 33, 21, 35, 32, 25, 8, 63, 52, 23, 13, 37, 43, 34, 16, 47, 51, 36, 40, 39, 27, 29, 46, 30, 28, 3, 65, 57, 58, 2, 60, 48, 45, 50, 38, 49, 24, 41, 9, 56, 53, 64, 42], 'cur_cost': 66007.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [14, 6, 12, 18, 31, 17, 22, 15, 7, 61, 10, 1, 11, 19, 26, 5, 55, 4, 54, 59, 62, 44, 0, 20, 33, 21, 35, 32, 25, 8, 63, 52, 23, 13, 37, 43, 34, 16, 47, 51, 36, 40, 39, 27, 29, 46, 30, 28, 3, 65, 57, 58, 2, 60, 48, 45, 50, 38, 49, 24, 41, 9, 56, 53, 64, 42], 'cur_cost': 70180.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:13,286 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 53927.00)
2025-08-05 09:52:13,286 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 09:52:13,286 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:13,286 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:13,287 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 89784.0
2025-08-05 09:52:13,293 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 09:52:13,294 - ExploitationExpert - INFO - res_population_costs: [9521, 9534.0]
2025-08-05 09:52:13,294 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64)]
2025-08-05 09:52:13,295 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:13,295 - ExploitationExpert - INFO - populations: [{'tour': [0, 4, 20, 17, 12, 22, 23, 16, 18, 19, 13, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12448.0}, {'tour': [0, 12, 6, 1, 7, 3, 9, 11, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12785.0}, {'tour': [34, 17, 6, 63, 54, 55, 11, 61, 60, 59, 12, 27, 13, 28, 5, 57, 20, 18, 25, 33, 15, 30, 22, 8, 52, 0, 2, 62, 19, 4, 58, 39, 47, 43, 51, 50, 44, 46, 49, 38, 42, 23, 26, 16, 29, 35, 14, 37, 3, 10, 1, 7, 9, 65, 64, 56, 40, 41, 48, 45, 21, 36, 31, 24, 32, 53], 'cur_cost': 55451.0}, {'tour': array([ 1, 60, 33, 52, 12, 35, 43, 55, 42, 58, 40, 29, 50, 41, 45,  3, 28,
       39,  5, 26, 34, 16, 62, 56, 61, 18, 10, 19, 30, 53,  4, 59, 31, 49,
       17, 46, 11,  0, 36, 64, 51, 13, 27, 21,  9,  8, 54, 37,  2, 15, 44,
       47, 32, 57, 20, 22, 25, 24, 48,  7, 14, 65, 63, 38, 23,  6],
      dtype=int64), 'cur_cost': 106768.0}, {'tour': [0, 5, 7, 19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12428.0}, {'tour': [0, 10, 19, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12920.0}, {'tour': [40, 18, 36, 27, 7, 10, 23, 15, 34, 11, 4, 56, 0, 1, 8, 63, 60, 55, 9, 20, 25, 28, 13, 16, 12, 33, 32, 24, 49, 43, 45, 51, 37, 47, 39, 22, 29, 19, 35, 46, 48, 31, 30, 44, 41, 50, 14, 5, 2, 52, 53, 64, 65, 57, 58, 61, 6, 54, 59, 3, 17, 21, 26, 42, 38, 62], 'cur_cost': 53927.0}, {'tour': array([58, 13,  2, 28, 41, 43, 14, 53, 49, 62, 23, 17, 38, 54, 52, 65,  0,
        1, 64, 61, 44, 47, 35, 32, 24, 33, 36, 22, 30, 27, 59, 15, 11, 20,
       39, 29, 25, 56, 50, 37, 40, 18,  3, 60, 10, 57,  8,  7, 34,  9, 16,
       19, 63, 31, 55, 26, 42, 45, 21, 51,  4, 12, 48, 46,  6,  5],
      dtype=int64), 'cur_cost': 89784.0}, {'tour': [35, 19, 17, 20, 33, 0, 14, 6, 7, 60, 9, 12, 34, 37, 23, 5, 63, 1, 22, 21, 11, 62, 39, 38, 41, 51, 47, 48, 16, 26, 32, 24, 31, 3, 57, 54, 65, 4, 59, 15, 10, 18, 30, 25, 36, 2, 53, 58, 55, 56, 52, 64, 43, 42, 50, 46, 44, 40, 49, 27, 28, 29, 13, 8, 61, 45], 'cur_cost': 53487.0}, {'tour': [0, 12, 16, 8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 13, 20, 21, 19, 18, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12807.0}]
2025-08-05 09:52:13,296 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:13,296 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 316, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 316, 'cache_hits': 0, 'similarity_calculations': 1596, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:13,297 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([58, 13,  2, 28, 41, 43, 14, 53, 49, 62, 23, 17, 38, 54, 52, 65,  0,
        1, 64, 61, 44, 47, 35, 32, 24, 33, 36, 22, 30, 27, 59, 15, 11, 20,
       39, 29, 25, 56, 50, 37, 40, 18,  3, 60, 10, 57,  8,  7, 34,  9, 16,
       19, 63, 31, 55, 26, 42, 45, 21, 51,  4, 12, 48, 46,  6,  5],
      dtype=int64), 'cur_cost': 89784.0, 'intermediate_solutions': [{'tour': array([11, 46, 25, 54, 38, 56, 58, 61, 17, 32, 40, 44, 16, 42, 49, 57, 18,
        6, 37,  0, 63, 12, 35, 45, 34,  8, 19, 55, 64,  4,  2, 31,  5, 22,
       24, 65, 43, 59, 39, 51, 26, 13, 33, 10, 27, 48,  9, 50, 28, 14, 36,
       20, 21, 62, 52,  1, 29,  3,  7, 47, 53, 41, 23, 60, 30, 15]), 'cur_cost': 109705.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([54, 11, 46, 25, 38, 56, 58, 61, 17, 32, 40, 44, 16, 42, 49, 57, 18,
        6, 37,  0, 63, 12, 35, 45, 34,  8, 19, 55, 64,  4,  2, 31,  5, 22,
       24, 65, 43, 59, 39, 51, 26, 13, 33, 10, 27, 48,  9, 50, 28, 14, 36,
       20, 21, 62, 52,  1, 29,  3,  7, 47, 53, 41, 23, 60, 30, 15]), 'cur_cost': 107886.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([38, 54, 11, 46, 25, 56, 58, 61, 17, 32, 40, 44, 16, 42, 49, 57, 18,
        6, 37,  0, 63, 12, 35, 45, 34,  8, 19, 55, 64,  4,  2, 31,  5, 22,
       24, 65, 43, 59, 39, 51, 26, 13, 33, 10, 27, 48,  9, 50, 28, 14, 36,
       20, 21, 62, 52,  1, 29,  3,  7, 47, 53, 41, 23, 60, 30, 15]), 'cur_cost': 108199.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([25, 54, 11, 46, 38, 56, 58, 61, 17, 32, 40, 44, 16, 42, 49, 57, 18,
        6, 37,  0, 63, 12, 35, 45, 34,  8, 19, 55, 64,  4,  2, 31,  5, 22,
       24, 65, 43, 59, 39, 51, 26, 13, 33, 10, 27, 48,  9, 50, 28, 14, 36,
       20, 21, 62, 52,  1, 29,  3,  7, 47, 53, 41, 23, 60, 30, 15]), 'cur_cost': 105934.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([25, 38, 54, 11, 46, 56, 58, 61, 17, 32, 40, 44, 16, 42, 49, 57, 18,
        6, 37,  0, 63, 12, 35, 45, 34,  8, 19, 55, 64,  4,  2, 31,  5, 22,
       24, 65, 43, 59, 39, 51, 26, 13, 33, 10, 27, 48,  9, 50, 28, 14, 36,
       20, 21, 62, 52,  1, 29,  3,  7, 47, 53, 41, 23, 60, 30, 15]), 'cur_cost': 107885.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:13,297 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 89784.00)
2025-08-05 09:52:13,297 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:52:13,297 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:52:13,298 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:13,299 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-05 09:52:13,300 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,300 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,300 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,300 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,301 - ExplorationExpert - INFO - 探索路径生成完成，成本: 88424.0, 路径长度: 66, 收集中间解: 3
2025-08-05 09:52:13,301 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [34, 4, 63, 54, 22, 11, 16, 18, 59, 13, 2, 28, 5, 37, 20, 25, 35, 15, 8, 31, 24, 29, 32, 3, 7, 23, 17, 9, 43, 51, 6, 49, 38, 42, 26, 41, 53, 58, 55, 10, 1, 65, 44, 64, 56, 40, 48, 14, 36, 60, 52, 61, 30, 46, 62, 19, 39, 12, 57, 21, 27, 33, 47, 0, 45, 50], 'cur_cost': 88424.0, 'intermediate_solutions': [{'tour': [35, 19, 17, 20, 33, 0, 14, 6, 7, 60, 9, 12, 5, 37, 23, 34, 63, 1, 22, 21, 11, 62, 39, 38, 41, 51, 47, 48, 16, 26, 32, 24, 31, 3, 57, 54, 65, 4, 59, 15, 10, 18, 30, 25, 36, 2, 53, 58, 55, 56, 52, 64, 43, 42, 50, 46, 44, 40, 49, 27, 28, 29, 13, 8, 61, 45], 'cur_cost': 57310.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [35, 19, 17, 20, 33, 0, 14, 6, 7, 60, 9, 12, 34, 37, 23, 5, 63, 1, 22, 21, 11, 62, 39, 59, 4, 65, 54, 57, 3, 31, 24, 32, 26, 16, 48, 47, 51, 41, 38, 15, 10, 18, 30, 25, 36, 2, 53, 58, 55, 56, 52, 64, 43, 42, 50, 46, 44, 40, 49, 27, 28, 29, 13, 8, 61, 45], 'cur_cost': 55782.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [19, 17, 20, 33, 0, 14, 6, 7, 60, 9, 12, 34, 37, 23, 5, 63, 1, 22, 21, 11, 62, 39, 38, 41, 51, 47, 48, 16, 26, 32, 24, 31, 3, 57, 54, 65, 4, 59, 35, 15, 10, 18, 30, 25, 36, 2, 53, 58, 55, 56, 52, 64, 43, 42, 50, 46, 44, 40, 49, 27, 28, 29, 13, 8, 61, 45], 'cur_cost': 53851.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:13,301 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 88424.00)
2025-08-05 09:52:13,301 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:13,301 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:13,301 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:13,307 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-05 09:52:13,307 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,308 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,308 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,308 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,309 - ExplorationExpert - INFO - 探索路径生成完成，成本: 67913.0, 路径长度: 66, 收集中间解: 3
2025-08-05 09:52:13,309 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [64, 17, 26, 33, 14, 28, 30, 35, 31, 9, 52, 22, 34, 5, 61, 11, 63, 12, 27, 2, 4, 13, 20, 32, 40, 23, 43, 44, 46, 19, 15, 37, 7, 65, 0, 56, 3, 55, 16, 21, 49, 42, 25, 47, 41, 36, 8, 10, 58, 59, 54, 62, 6, 1, 24, 48, 39, 45, 18, 50, 29, 51, 38, 60, 53, 57], 'cur_cost': 67913.0, 'intermediate_solutions': [{'tour': [0, 12, 16, 8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 26, 63, 14, 15, 22, 23, 13, 20, 21, 19, 18, 17, 27, 37, 25, 52, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 24529.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 12, 16, 8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 13, 23, 22, 15, 14, 63, 52, 65, 54, 57, 20, 21, 19, 18, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 16722.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 16, 8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 17, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 13, 20, 21, 19, 18, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 16546.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:13,309 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 67913.00)
2025-08-05 09:52:13,309 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:13,309 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:13,311 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 20, 17, 12, 22, 23, 16, 18, 19, 13, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12448.0, 'intermediate_solutions': [{'tour': [10, 54, 6, 63, 8, 20, 24, 26, 31, 23, 5, 60, 61, 57, 22, 30, 11, 1, 65, 64, 56, 19, 7, 17, 34, 36, 28, 15, 12, 32, 14, 35, 3, 52, 16, 27, 37, 4, 2, 13, 48, 40, 42, 45, 49, 21, 29, 62, 18, 46, 41, 38, 44, 47, 43, 39, 25, 0, 53, 58, 50, 33, 51, 9, 55, 59], 'cur_cost': 64582.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 54, 6, 63, 8, 20, 24, 26, 31, 23, 5, 60, 61, 57, 22, 30, 11, 1, 65, 64, 56, 19, 7, 17, 34, 36, 28, 15, 12, 32, 14, 35, 3, 4, 37, 27, 16, 52, 2, 13, 48, 40, 42, 45, 49, 21, 9, 62, 18, 46, 41, 38, 44, 47, 43, 39, 25, 0, 53, 58, 50, 33, 51, 29, 55, 59], 'cur_cost': 64214.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 54, 6, 63, 8, 20, 24, 26, 31, 23, 5, 60, 61, 57, 22, 30, 11, 1, 65, 64, 56, 19, 7, 17, 34, 36, 28, 15, 12, 32, 14, 35, 3, 52, 16, 27, 37, 2, 13, 48, 40, 42, 45, 49, 21, 9, 62, 18, 46, 41, 38, 4, 44, 47, 43, 39, 25, 0, 53, 58, 50, 33, 51, 29, 55, 59], 'cur_cost': 69226.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 6, 1, 7, 3, 9, 11, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12785.0, 'intermediate_solutions': [{'tour': [6, 20, 24, 60, 1, 56, 34, 36, 28, 35, 37, 48, 40, 45, 21, 62, 46, 38, 55, 47, 43, 39, 0, 31, 4, 15, 33, 63, 57, 26, 54, 9, 17, 50, 51, 14, 49, 19, 65, 8, 22, 30, 29, 42, 27, 18, 32, 10, 11, 25, 16, 3, 12, 53, 44, 61, 41, 23, 59, 5, 7, 2, 64, 13, 58, 52], 'cur_cost': 91297.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 20, 24, 60, 1, 56, 34, 36, 28, 35, 37, 48, 40, 45, 21, 62, 46, 38, 44, 47, 43, 39, 0, 31, 4, 15, 51, 50, 17, 9, 54, 26, 57, 63, 33, 14, 49, 19, 65, 8, 22, 30, 29, 42, 27, 18, 32, 10, 11, 25, 16, 3, 12, 53, 55, 61, 41, 23, 59, 5, 7, 2, 64, 13, 58, 52], 'cur_cost': 80268.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 20, 24, 60, 1, 56, 34, 36, 28, 19, 35, 37, 48, 40, 45, 21, 62, 46, 38, 44, 47, 43, 39, 0, 31, 4, 15, 33, 63, 57, 26, 54, 9, 17, 50, 51, 14, 49, 65, 8, 22, 30, 29, 42, 27, 18, 32, 10, 11, 25, 16, 3, 12, 53, 55, 61, 41, 23, 59, 5, 7, 2, 64, 13, 58, 52], 'cur_cost': 82046.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [34, 17, 6, 63, 54, 55, 11, 61, 60, 59, 12, 27, 13, 28, 5, 57, 20, 18, 25, 33, 15, 30, 22, 8, 52, 0, 2, 62, 19, 4, 58, 39, 47, 43, 51, 50, 44, 46, 49, 38, 42, 23, 26, 16, 29, 35, 14, 37, 3, 10, 1, 7, 9, 65, 64, 56, 40, 41, 48, 45, 21, 36, 31, 24, 32, 53], 'cur_cost': 55451.0, 'intermediate_solutions': [{'tour': [10, 54, 60, 1, 34, 26, 28, 23, 37, 48, 61, 57, 21, 46, 44, 19, 7, 17, 4, 15, 33, 14, 52, 49, 2, 8, 40, 42, 45, 27, 18, 9, 59, 41, 38, 53, 58, 43, 39, 0, 65, 51, 29, 63, 30, 24, 3, 25, 36, 12, 16, 50, 47, 11, 56, 5, 22, 31, 62, 55, 6, 13, 20, 64, 32, 35], 'cur_cost': 88191.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 54, 60, 1, 34, 26, 28, 23, 37, 48, 61, 57, 21, 46, 44, 19, 7, 17, 4, 15, 33, 14, 52, 49, 2, 8, 40, 42, 45, 27, 18, 9, 59, 41, 38, 58, 53, 43, 39, 0, 65, 51, 29, 63, 30, 24, 3, 25, 36, 12, 16, 50, 47, 6, 55, 62, 31, 22, 5, 56, 11, 13, 20, 64, 32, 35], 'cur_cost': 88166.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [20, 10, 54, 60, 1, 34, 26, 28, 23, 37, 48, 61, 57, 21, 46, 44, 19, 7, 17, 4, 15, 33, 14, 52, 49, 2, 8, 40, 42, 45, 27, 18, 9, 59, 41, 38, 58, 53, 43, 39, 0, 65, 51, 29, 63, 30, 24, 3, 25, 36, 12, 16, 50, 47, 11, 56, 5, 22, 31, 62, 55, 6, 13, 64, 32, 35], 'cur_cost': 88674.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 1, 60, 33, 52, 12, 35, 43, 55, 42, 58, 40, 29, 50, 41, 45,  3, 28,
       39,  5, 26, 34, 16, 62, 56, 61, 18, 10, 19, 30, 53,  4, 59, 31, 49,
       17, 46, 11,  0, 36, 64, 51, 13, 27, 21,  9,  8, 54, 37,  2, 15, 44,
       47, 32, 57, 20, 22, 25, 24, 48,  7, 14, 65, 63, 38, 23,  6],
      dtype=int64), 'cur_cost': 106768.0, 'intermediate_solutions': [{'tour': array([ 4, 49, 52, 12, 58, 14, 19, 48, 37, 25, 21, 15, 44,  9, 40, 22, 36,
       24, 35, 30, 10, 45, 17, 46, 59,  8, 63, 61, 20, 41, 56, 38, 13, 57,
        6,  3, 33, 47, 31, 50, 32, 54, 29, 34, 53, 11, 18,  0,  2, 16, 64,
       51, 55,  1, 65, 43,  5, 23, 39, 26,  7, 62, 42, 60, 28, 27]), 'cur_cost': 108847.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([12,  4, 49, 52, 58, 14, 19, 48, 37, 25, 21, 15, 44,  9, 40, 22, 36,
       24, 35, 30, 10, 45, 17, 46, 59,  8, 63, 61, 20, 41, 56, 38, 13, 57,
        6,  3, 33, 47, 31, 50, 32, 54, 29, 34, 53, 11, 18,  0,  2, 16, 64,
       51, 55,  1, 65, 43,  5, 23, 39, 26,  7, 62, 42, 60, 28, 27]), 'cur_cost': 105484.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([58, 12,  4, 49, 52, 14, 19, 48, 37, 25, 21, 15, 44,  9, 40, 22, 36,
       24, 35, 30, 10, 45, 17, 46, 59,  8, 63, 61, 20, 41, 56, 38, 13, 57,
        6,  3, 33, 47, 31, 50, 32, 54, 29, 34, 53, 11, 18,  0,  2, 16, 64,
       51, 55,  1, 65, 43,  5, 23, 39, 26,  7, 62, 42, 60, 28, 27]), 'cur_cost': 109085.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([52, 12,  4, 49, 58, 14, 19, 48, 37, 25, 21, 15, 44,  9, 40, 22, 36,
       24, 35, 30, 10, 45, 17, 46, 59,  8, 63, 61, 20, 41, 56, 38, 13, 57,
        6,  3, 33, 47, 31, 50, 32, 54, 29, 34, 53, 11, 18,  0,  2, 16, 64,
       51, 55,  1, 65, 43,  5, 23, 39, 26,  7, 62, 42, 60, 28, 27]), 'cur_cost': 109040.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([52, 58, 12,  4, 49, 14, 19, 48, 37, 25, 21, 15, 44,  9, 40, 22, 36,
       24, 35, 30, 10, 45, 17, 46, 59,  8, 63, 61, 20, 41, 56, 38, 13, 57,
        6,  3, 33, 47, 31, 50, 32, 54, 29, 34, 53, 11, 18,  0,  2, 16, 64,
       51, 55,  1, 65, 43,  5, 23, 39, 26,  7, 62, 42, 60, 28, 27]), 'cur_cost': 105808.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 7, 19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12428.0, 'intermediate_solutions': [{'tour': [65, 59, 11, 61, 63, 55, 52, 18, 20, 16, 14, 21, 19, 36, 25, 13, 33, 3, 51, 64, 57, 4, 22, 17, 32, 30, 27, 7, 8, 0, 5, 10, 23, 31, 9, 58, 56, 53, 49, 50, 45, 38, 48, 62, 34, 35, 1, 26, 6, 29, 15, 2, 60, 47, 44, 39, 40, 41, 12, 37, 24, 28, 43, 46, 42, 54], 'cur_cost': 58041.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [65, 59, 11, 61, 63, 55, 52, 18, 20, 16, 14, 21, 19, 36, 25, 13, 33, 3, 62, 64, 57, 4, 22, 60, 2, 15, 29, 6, 26, 1, 35, 34, 51, 48, 38, 45, 50, 49, 53, 56, 58, 9, 31, 23, 10, 5, 0, 8, 7, 27, 30, 32, 17, 47, 44, 39, 40, 41, 12, 37, 24, 28, 43, 46, 42, 54], 'cur_cost': 50741.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [65, 59, 11, 61, 63, 55, 52, 18, 20, 16, 14, 21, 19, 60, 36, 25, 13, 33, 3, 62, 64, 57, 4, 22, 17, 32, 30, 27, 7, 8, 0, 5, 10, 23, 31, 9, 58, 56, 53, 49, 50, 45, 38, 48, 51, 34, 35, 1, 26, 6, 29, 15, 2, 47, 44, 39, 40, 41, 12, 37, 24, 28, 43, 46, 42, 54], 'cur_cost': 52657.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 19, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12920.0, 'intermediate_solutions': [{'tour': [62, 1, 16, 30, 33, 0, 54, 57, 52, 15, 6, 63, 10, 64, 21, 32, 25, 31, 12, 22, 17, 23, 65, 5, 58, 18, 4, 27, 24, 19, 20, 35, 8, 3, 26, 34, 48, 47, 42, 44, 38, 43, 49, 50, 37, 36, 46, 29, 13, 9, 2, 55, 53, 11, 60, 56, 14, 7, 28, 45, 39, 41, 51, 40, 59, 61], 'cur_cost': 58033.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [62, 1, 16, 30, 33, 0, 54, 57, 52, 15, 6, 63, 10, 64, 21, 32, 25, 31, 12, 22, 17, 4, 65, 5, 58, 18, 23, 27, 24, 19, 20, 35, 8, 3, 26, 34, 48, 47, 42, 44, 38, 46, 36, 37, 50, 49, 43, 29, 13, 9, 2, 55, 53, 11, 60, 56, 14, 7, 28, 45, 39, 41, 51, 40, 59, 61], 'cur_cost': 56201.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [62, 1, 16, 30, 33, 0, 54, 57, 52, 15, 6, 63, 10, 64, 21, 32, 25, 31, 12, 22, 17, 4, 58, 65, 5, 18, 23, 27, 24, 19, 20, 35, 8, 3, 26, 34, 48, 47, 42, 44, 38, 43, 49, 50, 37, 36, 46, 29, 13, 9, 2, 55, 53, 11, 60, 56, 14, 7, 28, 45, 39, 41, 51, 40, 59, 61], 'cur_cost': 54604.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [40, 18, 36, 27, 7, 10, 23, 15, 34, 11, 4, 56, 0, 1, 8, 63, 60, 55, 9, 20, 25, 28, 13, 16, 12, 33, 32, 24, 49, 43, 45, 51, 37, 47, 39, 22, 29, 19, 35, 46, 48, 31, 30, 44, 41, 50, 14, 5, 2, 52, 53, 64, 65, 57, 58, 61, 6, 54, 59, 3, 17, 21, 26, 42, 38, 62], 'cur_cost': 53927.0, 'intermediate_solutions': [{'tour': [44, 14, 52, 12, 18, 31, 17, 22, 15, 7, 61, 10, 1, 11, 19, 26, 5, 55, 4, 54, 59, 62, 0, 20, 33, 21, 35, 32, 25, 8, 63, 6, 23, 13, 37, 43, 34, 16, 47, 51, 36, 40, 39, 27, 29, 46, 30, 28, 3, 65, 57, 58, 2, 60, 48, 45, 50, 38, 49, 24, 41, 9, 56, 53, 64, 42], 'cur_cost': 67659.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [44, 14, 6, 12, 18, 31, 17, 22, 15, 7, 61, 10, 1, 11, 19, 26, 5, 55, 54, 4, 59, 62, 0, 20, 33, 21, 35, 32, 25, 8, 63, 52, 23, 13, 37, 43, 34, 16, 47, 51, 36, 40, 39, 27, 29, 46, 30, 28, 3, 65, 57, 58, 2, 60, 48, 45, 50, 38, 49, 24, 41, 9, 56, 53, 64, 42], 'cur_cost': 66007.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [14, 6, 12, 18, 31, 17, 22, 15, 7, 61, 10, 1, 11, 19, 26, 5, 55, 4, 54, 59, 62, 44, 0, 20, 33, 21, 35, 32, 25, 8, 63, 52, 23, 13, 37, 43, 34, 16, 47, 51, 36, 40, 39, 27, 29, 46, 30, 28, 3, 65, 57, 58, 2, 60, 48, 45, 50, 38, 49, 24, 41, 9, 56, 53, 64, 42], 'cur_cost': 70180.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([58, 13,  2, 28, 41, 43, 14, 53, 49, 62, 23, 17, 38, 54, 52, 65,  0,
        1, 64, 61, 44, 47, 35, 32, 24, 33, 36, 22, 30, 27, 59, 15, 11, 20,
       39, 29, 25, 56, 50, 37, 40, 18,  3, 60, 10, 57,  8,  7, 34,  9, 16,
       19, 63, 31, 55, 26, 42, 45, 21, 51,  4, 12, 48, 46,  6,  5],
      dtype=int64), 'cur_cost': 89784.0, 'intermediate_solutions': [{'tour': array([11, 46, 25, 54, 38, 56, 58, 61, 17, 32, 40, 44, 16, 42, 49, 57, 18,
        6, 37,  0, 63, 12, 35, 45, 34,  8, 19, 55, 64,  4,  2, 31,  5, 22,
       24, 65, 43, 59, 39, 51, 26, 13, 33, 10, 27, 48,  9, 50, 28, 14, 36,
       20, 21, 62, 52,  1, 29,  3,  7, 47, 53, 41, 23, 60, 30, 15]), 'cur_cost': 109705.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([54, 11, 46, 25, 38, 56, 58, 61, 17, 32, 40, 44, 16, 42, 49, 57, 18,
        6, 37,  0, 63, 12, 35, 45, 34,  8, 19, 55, 64,  4,  2, 31,  5, 22,
       24, 65, 43, 59, 39, 51, 26, 13, 33, 10, 27, 48,  9, 50, 28, 14, 36,
       20, 21, 62, 52,  1, 29,  3,  7, 47, 53, 41, 23, 60, 30, 15]), 'cur_cost': 107886.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([38, 54, 11, 46, 25, 56, 58, 61, 17, 32, 40, 44, 16, 42, 49, 57, 18,
        6, 37,  0, 63, 12, 35, 45, 34,  8, 19, 55, 64,  4,  2, 31,  5, 22,
       24, 65, 43, 59, 39, 51, 26, 13, 33, 10, 27, 48,  9, 50, 28, 14, 36,
       20, 21, 62, 52,  1, 29,  3,  7, 47, 53, 41, 23, 60, 30, 15]), 'cur_cost': 108199.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([25, 54, 11, 46, 38, 56, 58, 61, 17, 32, 40, 44, 16, 42, 49, 57, 18,
        6, 37,  0, 63, 12, 35, 45, 34,  8, 19, 55, 64,  4,  2, 31,  5, 22,
       24, 65, 43, 59, 39, 51, 26, 13, 33, 10, 27, 48,  9, 50, 28, 14, 36,
       20, 21, 62, 52,  1, 29,  3,  7, 47, 53, 41, 23, 60, 30, 15]), 'cur_cost': 105934.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([25, 38, 54, 11, 46, 56, 58, 61, 17, 32, 40, 44, 16, 42, 49, 57, 18,
        6, 37,  0, 63, 12, 35, 45, 34,  8, 19, 55, 64,  4,  2, 31,  5, 22,
       24, 65, 43, 59, 39, 51, 26, 13, 33, 10, 27, 48,  9, 50, 28, 14, 36,
       20, 21, 62, 52,  1, 29,  3,  7, 47, 53, 41, 23, 60, 30, 15]), 'cur_cost': 107885.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [34, 4, 63, 54, 22, 11, 16, 18, 59, 13, 2, 28, 5, 37, 20, 25, 35, 15, 8, 31, 24, 29, 32, 3, 7, 23, 17, 9, 43, 51, 6, 49, 38, 42, 26, 41, 53, 58, 55, 10, 1, 65, 44, 64, 56, 40, 48, 14, 36, 60, 52, 61, 30, 46, 62, 19, 39, 12, 57, 21, 27, 33, 47, 0, 45, 50], 'cur_cost': 88424.0, 'intermediate_solutions': [{'tour': [35, 19, 17, 20, 33, 0, 14, 6, 7, 60, 9, 12, 5, 37, 23, 34, 63, 1, 22, 21, 11, 62, 39, 38, 41, 51, 47, 48, 16, 26, 32, 24, 31, 3, 57, 54, 65, 4, 59, 15, 10, 18, 30, 25, 36, 2, 53, 58, 55, 56, 52, 64, 43, 42, 50, 46, 44, 40, 49, 27, 28, 29, 13, 8, 61, 45], 'cur_cost': 57310.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [35, 19, 17, 20, 33, 0, 14, 6, 7, 60, 9, 12, 34, 37, 23, 5, 63, 1, 22, 21, 11, 62, 39, 59, 4, 65, 54, 57, 3, 31, 24, 32, 26, 16, 48, 47, 51, 41, 38, 15, 10, 18, 30, 25, 36, 2, 53, 58, 55, 56, 52, 64, 43, 42, 50, 46, 44, 40, 49, 27, 28, 29, 13, 8, 61, 45], 'cur_cost': 55782.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [19, 17, 20, 33, 0, 14, 6, 7, 60, 9, 12, 34, 37, 23, 5, 63, 1, 22, 21, 11, 62, 39, 38, 41, 51, 47, 48, 16, 26, 32, 24, 31, 3, 57, 54, 65, 4, 59, 35, 15, 10, 18, 30, 25, 36, 2, 53, 58, 55, 56, 52, 64, 43, 42, 50, 46, 44, 40, 49, 27, 28, 29, 13, 8, 61, 45], 'cur_cost': 53851.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [64, 17, 26, 33, 14, 28, 30, 35, 31, 9, 52, 22, 34, 5, 61, 11, 63, 12, 27, 2, 4, 13, 20, 32, 40, 23, 43, 44, 46, 19, 15, 37, 7, 65, 0, 56, 3, 55, 16, 21, 49, 42, 25, 47, 41, 36, 8, 10, 58, 59, 54, 62, 6, 1, 24, 48, 39, 45, 18, 50, 29, 51, 38, 60, 53, 57], 'cur_cost': 67913.0, 'intermediate_solutions': [{'tour': [0, 12, 16, 8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 26, 63, 14, 15, 22, 23, 13, 20, 21, 19, 18, 17, 27, 37, 25, 52, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 24529.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 12, 16, 8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 13, 23, 22, 15, 14, 63, 52, 65, 54, 57, 20, 21, 19, 18, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 16722.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 16, 8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 17, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 13, 20, 21, 19, 18, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 16546.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:13,311 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:13,312 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:13,315 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12428.000, 多样性=0.959
2025-08-05 09:52:13,315 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 09:52:13,315 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 09:52:13,315 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:13,315 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.10184900382217661, 'best_improvement': 0.02959319122354962}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.022321428571428416}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.012709225690924216, 'recent_improvements': [-0.03125151422123978, 0.0778511387333267, -0.05666996560308821], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.9696969696969697, 'new_diversity': 0.9696969696969697, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配']}
2025-08-05 09:52:13,316 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 09:52:13,316 - __main__ - INFO - composite13_66 开始进化第 3 代
2025-08-05 09:52:13,316 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 09:52:13,316 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:13,317 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12428.000, 多样性=0.959
2025-08-05 09:52:13,317 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:13,320 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.959
2025-08-05 09:52:13,320 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:13,321 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.970
2025-08-05 09:52:13,323 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 09:52:13,323 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:13,323 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-05 09:52:13,323 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-05 09:52:13,350 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.167, 适应度梯度: -7822.850, 聚类评分: 0.000, 覆盖率: 0.144, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:13,351 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 09:52:13,351 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:13,351 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite13_66
2025-08-05 09:52:13,356 - visualization.landscape_visualizer - INFO - 插值约束: 156 个点被约束到最小值 9521.00
2025-08-05 09:52:13,461 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite13_66\landscape_composite13_66_iter_123_20250805_095213.html
2025-08-05 09:52:13,501 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite13_66\dashboard_composite13_66_iter_123_20250805_095213.html
2025-08-05 09:52:13,501 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 123
2025-08-05 09:52:13,502 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 09:52:13,502 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1787秒
2025-08-05 09:52:13,502 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.16666666666666666, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -7822.849999999998, 'local_optima_density': 0.16666666666666666, 'gradient_variance': 973765019.3275, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1438, 'fitness_entropy': 0.8437590816619511, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -7822.850)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.144)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358733.3519354, 'performance_metrics': {}}}
2025-08-05 09:52:13,502 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:13,502 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:13,502 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:13,502 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:13,503 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 09:52:13,503 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 09:52:13,503 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 09:52:13,503 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:13,503 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:13,503 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 09:52:13,503 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:13,504 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:13,504 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 4} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:13,504 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:13,504 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:13,504 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:13,506 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 09:52:13,506 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,506 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,506 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,507 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,507 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12352.0, 路径长度: 66, 收集中间解: 3
2025-08-05 09:52:13,507 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 10, 5, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12352.0, 'intermediate_solutions': [{'tour': [0, 4, 20, 28, 12, 22, 23, 16, 18, 19, 13, 21, 14, 15, 27, 37, 25, 26, 36, 35, 17, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 17176.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 20, 17, 12, 22, 23, 16, 18, 19, 13, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 43, 40, 49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 48, 42], 'cur_cost': 18044.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 20, 17, 12, 22, 8, 23, 16, 18, 19, 13, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14916.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:13,507 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 12352.00)
2025-08-05 09:52:13,507 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:13,508 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:13,508 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:13,510 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 09:52:13,510 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,510 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,510 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,511 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,511 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12335.0, 路径长度: 66, 收集中间解: 3
2025-08-05 09:52:13,511 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 17, 19, 18, 16, 23, 22, 12, 15, 14, 20, 21, 13, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12335.0, 'intermediate_solutions': [{'tour': [0, 12, 6, 1, 7, 3, 45, 11, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 9, 38, 51, 50, 41, 42], 'cur_cost': 22753.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [26, 25, 37, 27, 19, 21, 20, 13, 17, 18, 16, 23, 22, 15, 14, 63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10, 2, 8, 4, 5, 11, 9, 3, 7, 1, 6, 12, 0, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 14259.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 6, 1, 7, 3, 9, 11, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 60, 64, 57, 54, 65, 52, 63, 14, 58, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 16490.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:13,511 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 12335.00)
2025-08-05 09:52:13,511 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:13,511 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:13,511 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:13,513 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 09:52:13,514 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,514 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,514 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,514 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,514 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12707.0, 路径长度: 66, 收集中间解: 3
2025-08-05 09:52:13,515 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 3, 22, 8, 2, 6, 4, 5, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12707.0, 'intermediate_solutions': [{'tour': [34, 17, 6, 63, 54, 55, 11, 61, 60, 59, 12, 27, 13, 28, 5, 57, 20, 14, 25, 33, 15, 30, 22, 8, 52, 0, 2, 62, 19, 4, 58, 39, 47, 43, 51, 50, 44, 46, 49, 38, 42, 23, 26, 16, 29, 35, 18, 37, 3, 10, 1, 7, 9, 65, 64, 56, 40, 41, 48, 45, 21, 36, 31, 24, 32, 53], 'cur_cost': 55357.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [34, 17, 6, 63, 54, 55, 11, 61, 60, 59, 12, 27, 13, 28, 5, 57, 20, 18, 25, 33, 15, 30, 22, 8, 29, 16, 26, 23, 42, 38, 49, 46, 44, 50, 51, 43, 47, 39, 58, 4, 19, 62, 2, 0, 52, 35, 14, 37, 3, 10, 1, 7, 9, 65, 64, 56, 40, 41, 48, 45, 21, 36, 31, 24, 32, 53], 'cur_cost': 59368.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [34, 17, 6, 63, 54, 55, 11, 61, 60, 59, 12, 27, 14, 13, 28, 5, 57, 20, 18, 25, 33, 15, 30, 22, 8, 52, 0, 2, 62, 19, 4, 58, 39, 47, 43, 51, 50, 44, 46, 49, 38, 42, 23, 26, 16, 29, 35, 37, 3, 10, 1, 7, 9, 65, 64, 56, 40, 41, 48, 45, 21, 36, 31, 24, 32, 53], 'cur_cost': 53115.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:13,515 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 12707.00)
2025-08-05 09:52:13,515 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:52:13,515 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:13,515 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:13,515 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 102985.0
2025-08-05 09:52:13,525 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 09:52:13,525 - ExploitationExpert - INFO - res_population_costs: [9521, 9534.0]
2025-08-05 09:52:13,525 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64)]
2025-08-05 09:52:13,526 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:13,526 - ExploitationExpert - INFO - populations: [{'tour': [0, 10, 5, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12352.0}, {'tour': [0, 17, 19, 18, 16, 23, 22, 12, 15, 14, 20, 21, 13, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12335.0}, {'tour': [0, 3, 22, 8, 2, 6, 4, 5, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12707.0}, {'tour': array([27, 16, 28, 38, 30, 53, 32, 36, 41, 43,  9, 20,  8, 26, 51, 50, 57,
        7, 23, 13, 12, 37,  5, 29, 63, 55, 65, 39, 11,  1,  3,  0, 15, 40,
       33, 47, 56, 14, 58, 22, 10, 54, 49, 24,  6, 48, 46, 52, 21,  2, 19,
       34, 64, 62, 61, 25, 44, 35, 45, 60,  4, 18, 17, 59, 42, 31],
      dtype=int64), 'cur_cost': 102985.0}, {'tour': [0, 5, 7, 19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12428.0}, {'tour': [0, 10, 19, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12920.0}, {'tour': [40, 18, 36, 27, 7, 10, 23, 15, 34, 11, 4, 56, 0, 1, 8, 63, 60, 55, 9, 20, 25, 28, 13, 16, 12, 33, 32, 24, 49, 43, 45, 51, 37, 47, 39, 22, 29, 19, 35, 46, 48, 31, 30, 44, 41, 50, 14, 5, 2, 52, 53, 64, 65, 57, 58, 61, 6, 54, 59, 3, 17, 21, 26, 42, 38, 62], 'cur_cost': 53927.0}, {'tour': [58, 13, 2, 28, 41, 43, 14, 53, 49, 62, 23, 17, 38, 54, 52, 65, 0, 1, 64, 61, 44, 47, 35, 32, 24, 33, 36, 22, 30, 27, 59, 15, 11, 20, 39, 29, 25, 56, 50, 37, 40, 18, 3, 60, 10, 57, 8, 7, 34, 9, 16, 19, 63, 31, 55, 26, 42, 45, 21, 51, 4, 12, 48, 46, 6, 5], 'cur_cost': 89784.0}, {'tour': [34, 4, 63, 54, 22, 11, 16, 18, 59, 13, 2, 28, 5, 37, 20, 25, 35, 15, 8, 31, 24, 29, 32, 3, 7, 23, 17, 9, 43, 51, 6, 49, 38, 42, 26, 41, 53, 58, 55, 10, 1, 65, 44, 64, 56, 40, 48, 14, 36, 60, 52, 61, 30, 46, 62, 19, 39, 12, 57, 21, 27, 33, 47, 0, 45, 50], 'cur_cost': 88424.0}, {'tour': [64, 17, 26, 33, 14, 28, 30, 35, 31, 9, 52, 22, 34, 5, 61, 11, 63, 12, 27, 2, 4, 13, 20, 32, 40, 23, 43, 44, 46, 19, 15, 37, 7, 65, 0, 56, 3, 55, 16, 21, 49, 42, 25, 47, 41, 36, 8, 10, 58, 59, 54, 62, 6, 1, 24, 48, 39, 45, 18, 50, 29, 51, 38, 60, 53, 57], 'cur_cost': 67913.0}]
2025-08-05 09:52:13,527 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:13,527 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 317, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 317, 'cache_hits': 0, 'similarity_calculations': 1600, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:13,528 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([27, 16, 28, 38, 30, 53, 32, 36, 41, 43,  9, 20,  8, 26, 51, 50, 57,
        7, 23, 13, 12, 37,  5, 29, 63, 55, 65, 39, 11,  1,  3,  0, 15, 40,
       33, 47, 56, 14, 58, 22, 10, 54, 49, 24,  6, 48, 46, 52, 21,  2, 19,
       34, 64, 62, 61, 25, 44, 35, 45, 60,  4, 18, 17, 59, 42, 31],
      dtype=int64), 'cur_cost': 102985.0, 'intermediate_solutions': [{'tour': array([33, 60,  1, 52, 12, 35, 43, 55, 42, 58, 40, 29, 50, 41, 45,  3, 28,
       39,  5, 26, 34, 16, 62, 56, 61, 18, 10, 19, 30, 53,  4, 59, 31, 49,
       17, 46, 11,  0, 36, 64, 51, 13, 27, 21,  9,  8, 54, 37,  2, 15, 44,
       47, 32, 57, 20, 22, 25, 24, 48,  7, 14, 65, 63, 38, 23,  6]), 'cur_cost': 106790.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([52, 33, 60,  1, 12, 35, 43, 55, 42, 58, 40, 29, 50, 41, 45,  3, 28,
       39,  5, 26, 34, 16, 62, 56, 61, 18, 10, 19, 30, 53,  4, 59, 31, 49,
       17, 46, 11,  0, 36, 64, 51, 13, 27, 21,  9,  8, 54, 37,  2, 15, 44,
       47, 32, 57, 20, 22, 25, 24, 48,  7, 14, 65, 63, 38, 23,  6]), 'cur_cost': 106957.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([12, 52, 33, 60,  1, 35, 43, 55, 42, 58, 40, 29, 50, 41, 45,  3, 28,
       39,  5, 26, 34, 16, 62, 56, 61, 18, 10, 19, 30, 53,  4, 59, 31, 49,
       17, 46, 11,  0, 36, 64, 51, 13, 27, 21,  9,  8, 54, 37,  2, 15, 44,
       47, 32, 57, 20, 22, 25, 24, 48,  7, 14, 65, 63, 38, 23,  6]), 'cur_cost': 108685.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 1, 52, 33, 60, 12, 35, 43, 55, 42, 58, 40, 29, 50, 41, 45,  3, 28,
       39,  5, 26, 34, 16, 62, 56, 61, 18, 10, 19, 30, 53,  4, 59, 31, 49,
       17, 46, 11,  0, 36, 64, 51, 13, 27, 21,  9,  8, 54, 37,  2, 15, 44,
       47, 32, 57, 20, 22, 25, 24, 48,  7, 14, 65, 63, 38, 23,  6]), 'cur_cost': 106724.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 1, 12, 52, 33, 60, 35, 43, 55, 42, 58, 40, 29, 50, 41, 45,  3, 28,
       39,  5, 26, 34, 16, 62, 56, 61, 18, 10, 19, 30, 53,  4, 59, 31, 49,
       17, 46, 11,  0, 36, 64, 51, 13, 27, 21,  9,  8, 54, 37,  2, 15, 44,
       47, 32, 57, 20, 22, 25, 24, 48,  7, 14, 65, 63, 38, 23,  6]), 'cur_cost': 108686.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:13,529 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 102985.00)
2025-08-05 09:52:13,529 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:13,529 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:13,529 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:13,531 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-05 09:52:13,531 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,531 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,531 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,532 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,532 - ExplorationExpert - INFO - 探索路径生成完成，成本: 90100.0, 路径长度: 66, 收集中间解: 3
2025-08-05 09:52:13,532 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [18, 13, 4, 12, 14, 22, 1, 17, 19, 26, 35, 20, 25, 34, 33, 31, 29, 3, 49, 43, 9, 5, 23, 2, 6, 21, 46, 44, 56, 64, 36, 60, 53, 61, 40, 55, 38, 42, 45, 59, 51, 32, 15, 57, 39, 37, 11, 16, 62, 52, 50, 24, 8, 10, 7, 41, 48, 63, 27, 0, 65, 54, 30, 47, 58, 28], 'cur_cost': 90100.0, 'intermediate_solutions': [{'tour': [0, 5, 7, 19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 63, 33, 31, 24, 29, 32, 3, 9, 11, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 34, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 20506.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 7, 19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 15, 17, 27, 37, 25, 26, 36, 35, 28, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10, 4, 6, 2, 8, 1, 11, 9, 3, 32, 29, 24, 31, 33, 34, 30, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 16698.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 7, 19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 15, 17, 34, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24, 29, 32, 3, 9, 11, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12509.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:13,532 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 90100.00)
2025-08-05 09:52:13,533 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:13,533 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:13,533 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:13,538 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-05 09:52:13,538 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,538 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,539 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,539 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,539 - ExplorationExpert - INFO - 探索路径生成完成，成本: 61299.0, 路径长度: 66, 收集中间解: 3
2025-08-05 09:52:13,539 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [44, 18, 29, 14, 19, 9, 10, 62, 55, 7, 64, 0, 3, 1, 61, 53, 63, 56, 57, 39, 17, 20, 25, 22, 34, 12, 15, 21, 33, 5, 23, 35, 4, 11, 16, 43, 27, 40, 51, 47, 50, 46, 48, 30, 31, 24, 37, 2, 6, 13, 49, 45, 36, 38, 32, 26, 42, 28, 41, 8, 59, 58, 54, 60, 65, 52], 'cur_cost': 61299.0, 'intermediate_solutions': [{'tour': [0, 10, 19, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 54, 64, 57, 60, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12995.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 19, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 44, 39, 43, 48, 46, 47, 49, 40, 32, 29, 24, 31, 45, 38, 51, 50, 41, 42], 'cur_cost': 17196.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 19, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 38, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 51, 50, 41, 42], 'cur_cost': 17200.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:13,540 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 61299.00)
2025-08-05 09:52:13,540 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:13,540 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:13,540 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:13,541 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 09:52:13,541 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,542 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,542 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,542 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,543 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12338.0, 路径长度: 66, 收集中间解: 3
2025-08-05 09:52:13,543 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 6, 22, 20, 21, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12338.0, 'intermediate_solutions': [{'tour': [40, 18, 36, 27, 7, 10, 23, 15, 34, 11, 4, 56, 0, 1, 8, 63, 60, 19, 9, 20, 25, 28, 13, 16, 12, 33, 32, 24, 49, 43, 45, 51, 37, 47, 39, 22, 29, 55, 35, 46, 48, 31, 30, 44, 41, 50, 14, 5, 2, 52, 53, 64, 65, 57, 58, 61, 6, 54, 59, 3, 17, 21, 26, 42, 38, 62], 'cur_cost': 59547.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [40, 18, 36, 27, 7, 10, 23, 15, 34, 11, 4, 56, 0, 1, 8, 63, 60, 55, 9, 20, 25, 28, 13, 16, 12, 33, 32, 24, 49, 43, 45, 51, 37, 47, 39, 50, 41, 44, 30, 31, 48, 46, 35, 19, 29, 22, 14, 5, 2, 52, 53, 64, 65, 57, 58, 61, 6, 54, 59, 3, 17, 21, 26, 42, 38, 62], 'cur_cost': 51184.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [40, 18, 36, 27, 7, 10, 23, 15, 34, 11, 4, 56, 0, 1, 8, 63, 60, 55, 9, 20, 25, 28, 13, 16, 12, 33, 32, 24, 49, 43, 45, 51, 37, 47, 39, 22, 29, 19, 35, 46, 48, 31, 30, 44, 41, 50, 14, 5, 2, 52, 54, 53, 64, 65, 57, 58, 61, 6, 59, 3, 17, 21, 26, 42, 38, 62], 'cur_cost': 53878.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:13,543 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 12338.00)
2025-08-05 09:52:13,543 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 09:52:13,543 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:13,544 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:13,544 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 103174.0
2025-08-05 09:52:13,552 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 09:52:13,552 - ExploitationExpert - INFO - res_population_costs: [9521, 9534.0]
2025-08-05 09:52:13,552 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64)]
2025-08-05 09:52:13,553 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:13,553 - ExploitationExpert - INFO - populations: [{'tour': [0, 10, 5, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12352.0}, {'tour': [0, 17, 19, 18, 16, 23, 22, 12, 15, 14, 20, 21, 13, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12335.0}, {'tour': [0, 3, 22, 8, 2, 6, 4, 5, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12707.0}, {'tour': array([27, 16, 28, 38, 30, 53, 32, 36, 41, 43,  9, 20,  8, 26, 51, 50, 57,
        7, 23, 13, 12, 37,  5, 29, 63, 55, 65, 39, 11,  1,  3,  0, 15, 40,
       33, 47, 56, 14, 58, 22, 10, 54, 49, 24,  6, 48, 46, 52, 21,  2, 19,
       34, 64, 62, 61, 25, 44, 35, 45, 60,  4, 18, 17, 59, 42, 31],
      dtype=int64), 'cur_cost': 102985.0}, {'tour': [18, 13, 4, 12, 14, 22, 1, 17, 19, 26, 35, 20, 25, 34, 33, 31, 29, 3, 49, 43, 9, 5, 23, 2, 6, 21, 46, 44, 56, 64, 36, 60, 53, 61, 40, 55, 38, 42, 45, 59, 51, 32, 15, 57, 39, 37, 11, 16, 62, 52, 50, 24, 8, 10, 7, 41, 48, 63, 27, 0, 65, 54, 30, 47, 58, 28], 'cur_cost': 90100.0}, {'tour': [44, 18, 29, 14, 19, 9, 10, 62, 55, 7, 64, 0, 3, 1, 61, 53, 63, 56, 57, 39, 17, 20, 25, 22, 34, 12, 15, 21, 33, 5, 23, 35, 4, 11, 16, 43, 27, 40, 51, 47, 50, 46, 48, 30, 31, 24, 37, 2, 6, 13, 49, 45, 36, 38, 32, 26, 42, 28, 41, 8, 59, 58, 54, 60, 65, 52], 'cur_cost': 61299.0}, {'tour': [0, 6, 22, 20, 21, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12338.0}, {'tour': array([53, 51, 11, 10, 50, 18, 17, 19,  8,  3, 42, 28, 63, 32, 49, 35,  4,
       61, 31, 26, 21, 64, 30, 38, 29, 20, 48,  1, 40, 34, 15, 52, 65, 59,
       27, 60, 45, 23, 25, 24, 22, 43, 41, 58,  0, 62,  5, 57,  9, 37, 16,
       33,  6, 14,  7, 12, 13, 56, 44, 46, 36, 55,  2, 39, 47, 54],
      dtype=int64), 'cur_cost': 103174.0}, {'tour': [34, 4, 63, 54, 22, 11, 16, 18, 59, 13, 2, 28, 5, 37, 20, 25, 35, 15, 8, 31, 24, 29, 32, 3, 7, 23, 17, 9, 43, 51, 6, 49, 38, 42, 26, 41, 53, 58, 55, 10, 1, 65, 44, 64, 56, 40, 48, 14, 36, 60, 52, 61, 30, 46, 62, 19, 39, 12, 57, 21, 27, 33, 47, 0, 45, 50], 'cur_cost': 88424.0}, {'tour': [64, 17, 26, 33, 14, 28, 30, 35, 31, 9, 52, 22, 34, 5, 61, 11, 63, 12, 27, 2, 4, 13, 20, 32, 40, 23, 43, 44, 46, 19, 15, 37, 7, 65, 0, 56, 3, 55, 16, 21, 49, 42, 25, 47, 41, 36, 8, 10, 58, 59, 54, 62, 6, 1, 24, 48, 39, 45, 18, 50, 29, 51, 38, 60, 53, 57], 'cur_cost': 67913.0}]
2025-08-05 09:52:13,554 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:13,555 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 318, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 318, 'cache_hits': 0, 'similarity_calculations': 1605, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:13,556 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([53, 51, 11, 10, 50, 18, 17, 19,  8,  3, 42, 28, 63, 32, 49, 35,  4,
       61, 31, 26, 21, 64, 30, 38, 29, 20, 48,  1, 40, 34, 15, 52, 65, 59,
       27, 60, 45, 23, 25, 24, 22, 43, 41, 58,  0, 62,  5, 57,  9, 37, 16,
       33,  6, 14,  7, 12, 13, 56, 44, 46, 36, 55,  2, 39, 47, 54],
      dtype=int64), 'cur_cost': 103174.0, 'intermediate_solutions': [{'tour': array([ 2, 13, 58, 28, 41, 43, 14, 53, 49, 62, 23, 17, 38, 54, 52, 65,  0,
        1, 64, 61, 44, 47, 35, 32, 24, 33, 36, 22, 30, 27, 59, 15, 11, 20,
       39, 29, 25, 56, 50, 37, 40, 18,  3, 60, 10, 57,  8,  7, 34,  9, 16,
       19, 63, 31, 55, 26, 42, 45, 21, 51,  4, 12, 48, 46,  6,  5]), 'cur_cost': 89695.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([28,  2, 13, 58, 41, 43, 14, 53, 49, 62, 23, 17, 38, 54, 52, 65,  0,
        1, 64, 61, 44, 47, 35, 32, 24, 33, 36, 22, 30, 27, 59, 15, 11, 20,
       39, 29, 25, 56, 50, 37, 40, 18,  3, 60, 10, 57,  8,  7, 34,  9, 16,
       19, 63, 31, 55, 26, 42, 45, 21, 51,  4, 12, 48, 46,  6,  5]), 'cur_cost': 91373.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([41, 28,  2, 13, 58, 43, 14, 53, 49, 62, 23, 17, 38, 54, 52, 65,  0,
        1, 64, 61, 44, 47, 35, 32, 24, 33, 36, 22, 30, 27, 59, 15, 11, 20,
       39, 29, 25, 56, 50, 37, 40, 18,  3, 60, 10, 57,  8,  7, 34,  9, 16,
       19, 63, 31, 55, 26, 42, 45, 21, 51,  4, 12, 48, 46,  6,  5]), 'cur_cost': 93941.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([58, 28,  2, 13, 41, 43, 14, 53, 49, 62, 23, 17, 38, 54, 52, 65,  0,
        1, 64, 61, 44, 47, 35, 32, 24, 33, 36, 22, 30, 27, 59, 15, 11, 20,
       39, 29, 25, 56, 50, 37, 40, 18,  3, 60, 10, 57,  8,  7, 34,  9, 16,
       19, 63, 31, 55, 26, 42, 45, 21, 51,  4, 12, 48, 46,  6,  5]), 'cur_cost': 90035.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([58, 41, 28,  2, 13, 43, 14, 53, 49, 62, 23, 17, 38, 54, 52, 65,  0,
        1, 64, 61, 44, 47, 35, 32, 24, 33, 36, 22, 30, 27, 59, 15, 11, 20,
       39, 29, 25, 56, 50, 37, 40, 18,  3, 60, 10, 57,  8,  7, 34,  9, 16,
       19, 63, 31, 55, 26, 42, 45, 21, 51,  4, 12, 48, 46,  6,  5]), 'cur_cost': 91878.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:13,556 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 103174.00)
2025-08-05 09:52:13,556 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:52:13,556 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:13,556 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:13,556 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 108562.0
2025-08-05 09:52:13,565 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 09:52:13,566 - ExploitationExpert - INFO - res_population_costs: [9521, 9534.0]
2025-08-05 09:52:13,566 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64)]
2025-08-05 09:52:13,567 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:13,567 - ExploitationExpert - INFO - populations: [{'tour': [0, 10, 5, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12352.0}, {'tour': [0, 17, 19, 18, 16, 23, 22, 12, 15, 14, 20, 21, 13, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12335.0}, {'tour': [0, 3, 22, 8, 2, 6, 4, 5, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12707.0}, {'tour': array([27, 16, 28, 38, 30, 53, 32, 36, 41, 43,  9, 20,  8, 26, 51, 50, 57,
        7, 23, 13, 12, 37,  5, 29, 63, 55, 65, 39, 11,  1,  3,  0, 15, 40,
       33, 47, 56, 14, 58, 22, 10, 54, 49, 24,  6, 48, 46, 52, 21,  2, 19,
       34, 64, 62, 61, 25, 44, 35, 45, 60,  4, 18, 17, 59, 42, 31],
      dtype=int64), 'cur_cost': 102985.0}, {'tour': [18, 13, 4, 12, 14, 22, 1, 17, 19, 26, 35, 20, 25, 34, 33, 31, 29, 3, 49, 43, 9, 5, 23, 2, 6, 21, 46, 44, 56, 64, 36, 60, 53, 61, 40, 55, 38, 42, 45, 59, 51, 32, 15, 57, 39, 37, 11, 16, 62, 52, 50, 24, 8, 10, 7, 41, 48, 63, 27, 0, 65, 54, 30, 47, 58, 28], 'cur_cost': 90100.0}, {'tour': [44, 18, 29, 14, 19, 9, 10, 62, 55, 7, 64, 0, 3, 1, 61, 53, 63, 56, 57, 39, 17, 20, 25, 22, 34, 12, 15, 21, 33, 5, 23, 35, 4, 11, 16, 43, 27, 40, 51, 47, 50, 46, 48, 30, 31, 24, 37, 2, 6, 13, 49, 45, 36, 38, 32, 26, 42, 28, 41, 8, 59, 58, 54, 60, 65, 52], 'cur_cost': 61299.0}, {'tour': [0, 6, 22, 20, 21, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12338.0}, {'tour': array([53, 51, 11, 10, 50, 18, 17, 19,  8,  3, 42, 28, 63, 32, 49, 35,  4,
       61, 31, 26, 21, 64, 30, 38, 29, 20, 48,  1, 40, 34, 15, 52, 65, 59,
       27, 60, 45, 23, 25, 24, 22, 43, 41, 58,  0, 62,  5, 57,  9, 37, 16,
       33,  6, 14,  7, 12, 13, 56, 44, 46, 36, 55,  2, 39, 47, 54],
      dtype=int64), 'cur_cost': 103174.0}, {'tour': array([ 7, 29,  1, 28, 13, 37, 46, 27, 43, 52, 11, 34, 40, 51,  9, 42, 45,
       60, 58, 15, 62, 39, 20,  2, 41, 25, 10,  5, 56, 50,  8, 65, 19, 18,
        6, 36, 47, 35, 32, 61, 48, 12, 33, 64, 57, 30, 22, 14, 53,  4, 54,
        3, 63,  0, 38, 21, 24, 44, 16, 49, 26, 59, 23, 17, 31, 55],
      dtype=int64), 'cur_cost': 108562.0}, {'tour': [64, 17, 26, 33, 14, 28, 30, 35, 31, 9, 52, 22, 34, 5, 61, 11, 63, 12, 27, 2, 4, 13, 20, 32, 40, 23, 43, 44, 46, 19, 15, 37, 7, 65, 0, 56, 3, 55, 16, 21, 49, 42, 25, 47, 41, 36, 8, 10, 58, 59, 54, 62, 6, 1, 24, 48, 39, 45, 18, 50, 29, 51, 38, 60, 53, 57], 'cur_cost': 67913.0}]
2025-08-05 09:52:13,569 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:13,569 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 319, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 319, 'cache_hits': 0, 'similarity_calculations': 1611, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:13,570 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 7, 29,  1, 28, 13, 37, 46, 27, 43, 52, 11, 34, 40, 51,  9, 42, 45,
       60, 58, 15, 62, 39, 20,  2, 41, 25, 10,  5, 56, 50,  8, 65, 19, 18,
        6, 36, 47, 35, 32, 61, 48, 12, 33, 64, 57, 30, 22, 14, 53,  4, 54,
        3, 63,  0, 38, 21, 24, 44, 16, 49, 26, 59, 23, 17, 31, 55],
      dtype=int64), 'cur_cost': 108562.0, 'intermediate_solutions': [{'tour': array([63,  4, 34, 54, 22, 11, 16, 18, 59, 13,  2, 28,  5, 37, 20, 25, 35,
       15,  8, 31, 24, 29, 32,  3,  7, 23, 17,  9, 43, 51,  6, 49, 38, 42,
       26, 41, 53, 58, 55, 10,  1, 65, 44, 64, 56, 40, 48, 14, 36, 60, 52,
       61, 30, 46, 62, 19, 39, 12, 57, 21, 27, 33, 47,  0, 45, 50]), 'cur_cost': 92115.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([54, 63,  4, 34, 22, 11, 16, 18, 59, 13,  2, 28,  5, 37, 20, 25, 35,
       15,  8, 31, 24, 29, 32,  3,  7, 23, 17,  9, 43, 51,  6, 49, 38, 42,
       26, 41, 53, 58, 55, 10,  1, 65, 44, 64, 56, 40, 48, 14, 36, 60, 52,
       61, 30, 46, 62, 19, 39, 12, 57, 21, 27, 33, 47,  0, 45, 50]), 'cur_cost': 88400.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([22, 54, 63,  4, 34, 11, 16, 18, 59, 13,  2, 28,  5, 37, 20, 25, 35,
       15,  8, 31, 24, 29, 32,  3,  7, 23, 17,  9, 43, 51,  6, 49, 38, 42,
       26, 41, 53, 58, 55, 10,  1, 65, 44, 64, 56, 40, 48, 14, 36, 60, 52,
       61, 30, 46, 62, 19, 39, 12, 57, 21, 27, 33, 47,  0, 45, 50]), 'cur_cost': 88526.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([34, 54, 63,  4, 22, 11, 16, 18, 59, 13,  2, 28,  5, 37, 20, 25, 35,
       15,  8, 31, 24, 29, 32,  3,  7, 23, 17,  9, 43, 51,  6, 49, 38, 42,
       26, 41, 53, 58, 55, 10,  1, 65, 44, 64, 56, 40, 48, 14, 36, 60, 52,
       61, 30, 46, 62, 19, 39, 12, 57, 21, 27, 33, 47,  0, 45, 50]), 'cur_cost': 88659.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([34, 22, 54, 63,  4, 11, 16, 18, 59, 13,  2, 28,  5, 37, 20, 25, 35,
       15,  8, 31, 24, 29, 32,  3,  7, 23, 17,  9, 43, 51,  6, 49, 38, 42,
       26, 41, 53, 58, 55, 10,  1, 65, 44, 64, 56, 40, 48, 14, 36, 60, 52,
       61, 30, 46, 62, 19, 39, 12, 57, 21, 27, 33, 47,  0, 45, 50]), 'cur_cost': 86444.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:13,570 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 108562.00)
2025-08-05 09:52:13,570 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:13,570 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:13,570 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:13,578 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-05 09:52:13,578 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,578 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,578 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,579 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,579 - ExplorationExpert - INFO - 探索路径生成完成，成本: 69941.0, 路径长度: 66, 收集中间解: 3
2025-08-05 09:52:13,579 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [12, 24, 23, 34, 13, 27, 0, 14, 11, 53, 61, 65, 5, 52, 2, 16, 6, 21, 18, 4, 20, 32, 9, 62, 47, 49, 40, 44, 39, 15, 28, 26, 7, 8, 54, 58, 57, 56, 22, 35, 37, 10, 60, 48, 36, 33, 29, 43, 41, 45, 19, 1, 55, 46, 42, 30, 31, 3, 17, 25, 51, 38, 59, 64, 63, 50], 'cur_cost': 69941.0, 'intermediate_solutions': [{'tour': [64, 17, 26, 33, 14, 28, 30, 35, 31, 9, 52, 22, 34, 5, 61, 11, 63, 12, 27, 2, 4, 13, 20, 32, 40, 23, 43, 44, 46, 19, 15, 37, 7, 65, 0, 56, 3, 55, 29, 21, 49, 42, 25, 47, 41, 36, 8, 10, 58, 59, 54, 62, 6, 1, 24, 48, 39, 45, 18, 50, 16, 51, 38, 60, 53, 57], 'cur_cost': 68582.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [64, 17, 26, 33, 14, 28, 30, 35, 31, 9, 52, 22, 34, 5, 61, 11, 63, 12, 27, 2, 4, 13, 20, 32, 40, 23, 43, 44, 46, 19, 15, 37, 7, 65, 0, 56, 3, 55, 16, 21, 49, 42, 25, 47, 41, 36, 8, 10, 58, 59, 54, 62, 6, 51, 29, 50, 18, 45, 39, 48, 24, 1, 38, 60, 53, 57], 'cur_cost': 72864.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [64, 17, 33, 14, 28, 30, 35, 31, 9, 52, 22, 34, 5, 61, 11, 63, 12, 27, 2, 4, 13, 20, 32, 40, 23, 43, 44, 46, 19, 26, 15, 37, 7, 65, 0, 56, 3, 55, 16, 21, 49, 42, 25, 47, 41, 36, 8, 10, 58, 59, 54, 62, 6, 1, 24, 48, 39, 45, 18, 50, 29, 51, 38, 60, 53, 57], 'cur_cost': 70192.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:13,579 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 69941.00)
2025-08-05 09:52:13,579 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:13,580 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:13,582 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 5, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12352.0, 'intermediate_solutions': [{'tour': [0, 4, 20, 28, 12, 22, 23, 16, 18, 19, 13, 21, 14, 15, 27, 37, 25, 26, 36, 35, 17, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 17176.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 20, 17, 12, 22, 23, 16, 18, 19, 13, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 43, 40, 49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 48, 42], 'cur_cost': 18044.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 20, 17, 12, 22, 8, 23, 16, 18, 19, 13, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14916.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 19, 18, 16, 23, 22, 12, 15, 14, 20, 21, 13, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12335.0, 'intermediate_solutions': [{'tour': [0, 12, 6, 1, 7, 3, 45, 11, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 9, 38, 51, 50, 41, 42], 'cur_cost': 22753.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [26, 25, 37, 27, 19, 21, 20, 13, 17, 18, 16, 23, 22, 15, 14, 63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10, 2, 8, 4, 5, 11, 9, 3, 7, 1, 6, 12, 0, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 14259.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 6, 1, 7, 3, 9, 11, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 60, 64, 57, 54, 65, 52, 63, 14, 58, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 16490.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 22, 8, 2, 6, 4, 5, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12707.0, 'intermediate_solutions': [{'tour': [34, 17, 6, 63, 54, 55, 11, 61, 60, 59, 12, 27, 13, 28, 5, 57, 20, 14, 25, 33, 15, 30, 22, 8, 52, 0, 2, 62, 19, 4, 58, 39, 47, 43, 51, 50, 44, 46, 49, 38, 42, 23, 26, 16, 29, 35, 18, 37, 3, 10, 1, 7, 9, 65, 64, 56, 40, 41, 48, 45, 21, 36, 31, 24, 32, 53], 'cur_cost': 55357.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [34, 17, 6, 63, 54, 55, 11, 61, 60, 59, 12, 27, 13, 28, 5, 57, 20, 18, 25, 33, 15, 30, 22, 8, 29, 16, 26, 23, 42, 38, 49, 46, 44, 50, 51, 43, 47, 39, 58, 4, 19, 62, 2, 0, 52, 35, 14, 37, 3, 10, 1, 7, 9, 65, 64, 56, 40, 41, 48, 45, 21, 36, 31, 24, 32, 53], 'cur_cost': 59368.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [34, 17, 6, 63, 54, 55, 11, 61, 60, 59, 12, 27, 14, 13, 28, 5, 57, 20, 18, 25, 33, 15, 30, 22, 8, 52, 0, 2, 62, 19, 4, 58, 39, 47, 43, 51, 50, 44, 46, 49, 38, 42, 23, 26, 16, 29, 35, 37, 3, 10, 1, 7, 9, 65, 64, 56, 40, 41, 48, 45, 21, 36, 31, 24, 32, 53], 'cur_cost': 53115.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([27, 16, 28, 38, 30, 53, 32, 36, 41, 43,  9, 20,  8, 26, 51, 50, 57,
        7, 23, 13, 12, 37,  5, 29, 63, 55, 65, 39, 11,  1,  3,  0, 15, 40,
       33, 47, 56, 14, 58, 22, 10, 54, 49, 24,  6, 48, 46, 52, 21,  2, 19,
       34, 64, 62, 61, 25, 44, 35, 45, 60,  4, 18, 17, 59, 42, 31],
      dtype=int64), 'cur_cost': 102985.0, 'intermediate_solutions': [{'tour': array([33, 60,  1, 52, 12, 35, 43, 55, 42, 58, 40, 29, 50, 41, 45,  3, 28,
       39,  5, 26, 34, 16, 62, 56, 61, 18, 10, 19, 30, 53,  4, 59, 31, 49,
       17, 46, 11,  0, 36, 64, 51, 13, 27, 21,  9,  8, 54, 37,  2, 15, 44,
       47, 32, 57, 20, 22, 25, 24, 48,  7, 14, 65, 63, 38, 23,  6]), 'cur_cost': 106790.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([52, 33, 60,  1, 12, 35, 43, 55, 42, 58, 40, 29, 50, 41, 45,  3, 28,
       39,  5, 26, 34, 16, 62, 56, 61, 18, 10, 19, 30, 53,  4, 59, 31, 49,
       17, 46, 11,  0, 36, 64, 51, 13, 27, 21,  9,  8, 54, 37,  2, 15, 44,
       47, 32, 57, 20, 22, 25, 24, 48,  7, 14, 65, 63, 38, 23,  6]), 'cur_cost': 106957.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([12, 52, 33, 60,  1, 35, 43, 55, 42, 58, 40, 29, 50, 41, 45,  3, 28,
       39,  5, 26, 34, 16, 62, 56, 61, 18, 10, 19, 30, 53,  4, 59, 31, 49,
       17, 46, 11,  0, 36, 64, 51, 13, 27, 21,  9,  8, 54, 37,  2, 15, 44,
       47, 32, 57, 20, 22, 25, 24, 48,  7, 14, 65, 63, 38, 23,  6]), 'cur_cost': 108685.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 1, 52, 33, 60, 12, 35, 43, 55, 42, 58, 40, 29, 50, 41, 45,  3, 28,
       39,  5, 26, 34, 16, 62, 56, 61, 18, 10, 19, 30, 53,  4, 59, 31, 49,
       17, 46, 11,  0, 36, 64, 51, 13, 27, 21,  9,  8, 54, 37,  2, 15, 44,
       47, 32, 57, 20, 22, 25, 24, 48,  7, 14, 65, 63, 38, 23,  6]), 'cur_cost': 106724.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 1, 12, 52, 33, 60, 35, 43, 55, 42, 58, 40, 29, 50, 41, 45,  3, 28,
       39,  5, 26, 34, 16, 62, 56, 61, 18, 10, 19, 30, 53,  4, 59, 31, 49,
       17, 46, 11,  0, 36, 64, 51, 13, 27, 21,  9,  8, 54, 37,  2, 15, 44,
       47, 32, 57, 20, 22, 25, 24, 48,  7, 14, 65, 63, 38, 23,  6]), 'cur_cost': 108686.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [18, 13, 4, 12, 14, 22, 1, 17, 19, 26, 35, 20, 25, 34, 33, 31, 29, 3, 49, 43, 9, 5, 23, 2, 6, 21, 46, 44, 56, 64, 36, 60, 53, 61, 40, 55, 38, 42, 45, 59, 51, 32, 15, 57, 39, 37, 11, 16, 62, 52, 50, 24, 8, 10, 7, 41, 48, 63, 27, 0, 65, 54, 30, 47, 58, 28], 'cur_cost': 90100.0, 'intermediate_solutions': [{'tour': [0, 5, 7, 19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 63, 33, 31, 24, 29, 32, 3, 9, 11, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 34, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 20506.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 7, 19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 15, 17, 27, 37, 25, 26, 36, 35, 28, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10, 4, 6, 2, 8, 1, 11, 9, 3, 32, 29, 24, 31, 33, 34, 30, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 16698.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 7, 19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 15, 17, 34, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24, 29, 32, 3, 9, 11, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12509.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [44, 18, 29, 14, 19, 9, 10, 62, 55, 7, 64, 0, 3, 1, 61, 53, 63, 56, 57, 39, 17, 20, 25, 22, 34, 12, 15, 21, 33, 5, 23, 35, 4, 11, 16, 43, 27, 40, 51, 47, 50, 46, 48, 30, 31, 24, 37, 2, 6, 13, 49, 45, 36, 38, 32, 26, 42, 28, 41, 8, 59, 58, 54, 60, 65, 52], 'cur_cost': 61299.0, 'intermediate_solutions': [{'tour': [0, 10, 19, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 54, 64, 57, 60, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12995.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 19, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 44, 39, 43, 48, 46, 47, 49, 40, 32, 29, 24, 31, 45, 38, 51, 50, 41, 42], 'cur_cost': 17196.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 19, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 38, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 51, 50, 41, 42], 'cur_cost': 17200.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 22, 20, 21, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12338.0, 'intermediate_solutions': [{'tour': [40, 18, 36, 27, 7, 10, 23, 15, 34, 11, 4, 56, 0, 1, 8, 63, 60, 19, 9, 20, 25, 28, 13, 16, 12, 33, 32, 24, 49, 43, 45, 51, 37, 47, 39, 22, 29, 55, 35, 46, 48, 31, 30, 44, 41, 50, 14, 5, 2, 52, 53, 64, 65, 57, 58, 61, 6, 54, 59, 3, 17, 21, 26, 42, 38, 62], 'cur_cost': 59547.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [40, 18, 36, 27, 7, 10, 23, 15, 34, 11, 4, 56, 0, 1, 8, 63, 60, 55, 9, 20, 25, 28, 13, 16, 12, 33, 32, 24, 49, 43, 45, 51, 37, 47, 39, 50, 41, 44, 30, 31, 48, 46, 35, 19, 29, 22, 14, 5, 2, 52, 53, 64, 65, 57, 58, 61, 6, 54, 59, 3, 17, 21, 26, 42, 38, 62], 'cur_cost': 51184.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [40, 18, 36, 27, 7, 10, 23, 15, 34, 11, 4, 56, 0, 1, 8, 63, 60, 55, 9, 20, 25, 28, 13, 16, 12, 33, 32, 24, 49, 43, 45, 51, 37, 47, 39, 22, 29, 19, 35, 46, 48, 31, 30, 44, 41, 50, 14, 5, 2, 52, 54, 53, 64, 65, 57, 58, 61, 6, 59, 3, 17, 21, 26, 42, 38, 62], 'cur_cost': 53878.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([53, 51, 11, 10, 50, 18, 17, 19,  8,  3, 42, 28, 63, 32, 49, 35,  4,
       61, 31, 26, 21, 64, 30, 38, 29, 20, 48,  1, 40, 34, 15, 52, 65, 59,
       27, 60, 45, 23, 25, 24, 22, 43, 41, 58,  0, 62,  5, 57,  9, 37, 16,
       33,  6, 14,  7, 12, 13, 56, 44, 46, 36, 55,  2, 39, 47, 54],
      dtype=int64), 'cur_cost': 103174.0, 'intermediate_solutions': [{'tour': array([ 2, 13, 58, 28, 41, 43, 14, 53, 49, 62, 23, 17, 38, 54, 52, 65,  0,
        1, 64, 61, 44, 47, 35, 32, 24, 33, 36, 22, 30, 27, 59, 15, 11, 20,
       39, 29, 25, 56, 50, 37, 40, 18,  3, 60, 10, 57,  8,  7, 34,  9, 16,
       19, 63, 31, 55, 26, 42, 45, 21, 51,  4, 12, 48, 46,  6,  5]), 'cur_cost': 89695.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([28,  2, 13, 58, 41, 43, 14, 53, 49, 62, 23, 17, 38, 54, 52, 65,  0,
        1, 64, 61, 44, 47, 35, 32, 24, 33, 36, 22, 30, 27, 59, 15, 11, 20,
       39, 29, 25, 56, 50, 37, 40, 18,  3, 60, 10, 57,  8,  7, 34,  9, 16,
       19, 63, 31, 55, 26, 42, 45, 21, 51,  4, 12, 48, 46,  6,  5]), 'cur_cost': 91373.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([41, 28,  2, 13, 58, 43, 14, 53, 49, 62, 23, 17, 38, 54, 52, 65,  0,
        1, 64, 61, 44, 47, 35, 32, 24, 33, 36, 22, 30, 27, 59, 15, 11, 20,
       39, 29, 25, 56, 50, 37, 40, 18,  3, 60, 10, 57,  8,  7, 34,  9, 16,
       19, 63, 31, 55, 26, 42, 45, 21, 51,  4, 12, 48, 46,  6,  5]), 'cur_cost': 93941.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([58, 28,  2, 13, 41, 43, 14, 53, 49, 62, 23, 17, 38, 54, 52, 65,  0,
        1, 64, 61, 44, 47, 35, 32, 24, 33, 36, 22, 30, 27, 59, 15, 11, 20,
       39, 29, 25, 56, 50, 37, 40, 18,  3, 60, 10, 57,  8,  7, 34,  9, 16,
       19, 63, 31, 55, 26, 42, 45, 21, 51,  4, 12, 48, 46,  6,  5]), 'cur_cost': 90035.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([58, 41, 28,  2, 13, 43, 14, 53, 49, 62, 23, 17, 38, 54, 52, 65,  0,
        1, 64, 61, 44, 47, 35, 32, 24, 33, 36, 22, 30, 27, 59, 15, 11, 20,
       39, 29, 25, 56, 50, 37, 40, 18,  3, 60, 10, 57,  8,  7, 34,  9, 16,
       19, 63, 31, 55, 26, 42, 45, 21, 51,  4, 12, 48, 46,  6,  5]), 'cur_cost': 91878.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 7, 29,  1, 28, 13, 37, 46, 27, 43, 52, 11, 34, 40, 51,  9, 42, 45,
       60, 58, 15, 62, 39, 20,  2, 41, 25, 10,  5, 56, 50,  8, 65, 19, 18,
        6, 36, 47, 35, 32, 61, 48, 12, 33, 64, 57, 30, 22, 14, 53,  4, 54,
        3, 63,  0, 38, 21, 24, 44, 16, 49, 26, 59, 23, 17, 31, 55],
      dtype=int64), 'cur_cost': 108562.0, 'intermediate_solutions': [{'tour': array([63,  4, 34, 54, 22, 11, 16, 18, 59, 13,  2, 28,  5, 37, 20, 25, 35,
       15,  8, 31, 24, 29, 32,  3,  7, 23, 17,  9, 43, 51,  6, 49, 38, 42,
       26, 41, 53, 58, 55, 10,  1, 65, 44, 64, 56, 40, 48, 14, 36, 60, 52,
       61, 30, 46, 62, 19, 39, 12, 57, 21, 27, 33, 47,  0, 45, 50]), 'cur_cost': 92115.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([54, 63,  4, 34, 22, 11, 16, 18, 59, 13,  2, 28,  5, 37, 20, 25, 35,
       15,  8, 31, 24, 29, 32,  3,  7, 23, 17,  9, 43, 51,  6, 49, 38, 42,
       26, 41, 53, 58, 55, 10,  1, 65, 44, 64, 56, 40, 48, 14, 36, 60, 52,
       61, 30, 46, 62, 19, 39, 12, 57, 21, 27, 33, 47,  0, 45, 50]), 'cur_cost': 88400.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([22, 54, 63,  4, 34, 11, 16, 18, 59, 13,  2, 28,  5, 37, 20, 25, 35,
       15,  8, 31, 24, 29, 32,  3,  7, 23, 17,  9, 43, 51,  6, 49, 38, 42,
       26, 41, 53, 58, 55, 10,  1, 65, 44, 64, 56, 40, 48, 14, 36, 60, 52,
       61, 30, 46, 62, 19, 39, 12, 57, 21, 27, 33, 47,  0, 45, 50]), 'cur_cost': 88526.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([34, 54, 63,  4, 22, 11, 16, 18, 59, 13,  2, 28,  5, 37, 20, 25, 35,
       15,  8, 31, 24, 29, 32,  3,  7, 23, 17,  9, 43, 51,  6, 49, 38, 42,
       26, 41, 53, 58, 55, 10,  1, 65, 44, 64, 56, 40, 48, 14, 36, 60, 52,
       61, 30, 46, 62, 19, 39, 12, 57, 21, 27, 33, 47,  0, 45, 50]), 'cur_cost': 88659.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([34, 22, 54, 63,  4, 11, 16, 18, 59, 13,  2, 28,  5, 37, 20, 25, 35,
       15,  8, 31, 24, 29, 32,  3,  7, 23, 17,  9, 43, 51,  6, 49, 38, 42,
       26, 41, 53, 58, 55, 10,  1, 65, 44, 64, 56, 40, 48, 14, 36, 60, 52,
       61, 30, 46, 62, 19, 39, 12, 57, 21, 27, 33, 47,  0, 45, 50]), 'cur_cost': 86444.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [12, 24, 23, 34, 13, 27, 0, 14, 11, 53, 61, 65, 5, 52, 2, 16, 6, 21, 18, 4, 20, 32, 9, 62, 47, 49, 40, 44, 39, 15, 28, 26, 7, 8, 54, 58, 57, 56, 22, 35, 37, 10, 60, 48, 36, 33, 29, 43, 41, 45, 19, 1, 55, 46, 42, 30, 31, 3, 17, 25, 51, 38, 59, 64, 63, 50], 'cur_cost': 69941.0, 'intermediate_solutions': [{'tour': [64, 17, 26, 33, 14, 28, 30, 35, 31, 9, 52, 22, 34, 5, 61, 11, 63, 12, 27, 2, 4, 13, 20, 32, 40, 23, 43, 44, 46, 19, 15, 37, 7, 65, 0, 56, 3, 55, 29, 21, 49, 42, 25, 47, 41, 36, 8, 10, 58, 59, 54, 62, 6, 1, 24, 48, 39, 45, 18, 50, 16, 51, 38, 60, 53, 57], 'cur_cost': 68582.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [64, 17, 26, 33, 14, 28, 30, 35, 31, 9, 52, 22, 34, 5, 61, 11, 63, 12, 27, 2, 4, 13, 20, 32, 40, 23, 43, 44, 46, 19, 15, 37, 7, 65, 0, 56, 3, 55, 16, 21, 49, 42, 25, 47, 41, 36, 8, 10, 58, 59, 54, 62, 6, 51, 29, 50, 18, 45, 39, 48, 24, 1, 38, 60, 53, 57], 'cur_cost': 72864.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [64, 17, 33, 14, 28, 30, 35, 31, 9, 52, 22, 34, 5, 61, 11, 63, 12, 27, 2, 4, 13, 20, 32, 40, 23, 43, 44, 46, 19, 26, 15, 37, 7, 65, 0, 56, 3, 55, 16, 21, 49, 42, 25, 47, 41, 36, 8, 10, 58, 59, 54, 62, 6, 1, 24, 48, 39, 45, 18, 50, 29, 51, 38, 60, 53, 57], 'cur_cost': 70192.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:13,582 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:13,582 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:13,586 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12335.000, 多样性=0.955
2025-08-05 09:52:13,586 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 09:52:13,586 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 09:52:13,586 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:13,586 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.05051827029262141, 'best_improvement': 0.00748310267138719}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.0038637161924836108}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.011998932544424959, 'recent_improvements': [0.0778511387333267, -0.05666996560308821, 0.10184900382217661], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.9696969696969697, 'new_diversity': 0.9696969696969697, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:52:13,587 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 09:52:13,587 - __main__ - INFO - composite13_66 开始进化第 4 代
2025-08-05 09:52:13,587 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 09:52:13,587 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:13,587 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12335.000, 多样性=0.955
2025-08-05 09:52:13,588 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:13,590 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.955
2025-08-05 09:52:13,590 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:13,591 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.970
2025-08-05 09:52:13,593 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 09:52:13,593 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:13,593 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-05 09:52:13,593 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-05 09:52:13,619 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.083, 适应度梯度: -18048.783, 聚类评分: 0.000, 覆盖率: 0.145, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:13,620 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 09:52:13,620 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:13,620 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite13_66
2025-08-05 09:52:13,662 - visualization.landscape_visualizer - INFO - 插值约束: 112 个点被约束到最小值 9521.00
2025-08-05 09:52:13,763 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite13_66\landscape_composite13_66_iter_124_20250805_095213.html
2025-08-05 09:52:13,806 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite13_66\dashboard_composite13_66_iter_124_20250805_095213.html
2025-08-05 09:52:13,806 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 124
2025-08-05 09:52:13,806 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 09:52:13,807 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2141秒
2025-08-05 09:52:13,807 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.08333333333333333, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -18048.783333333333, 'local_optima_density': 0.08333333333333333, 'gradient_variance': 1588741754.809722, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1446, 'fitness_entropy': 0.8166663857315859, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -18048.783)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.145)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358733.6204512, 'performance_metrics': {}}}
2025-08-05 09:52:13,807 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:13,807 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:13,807 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:13,807 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:13,808 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 09:52:13,808 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 09:52:13,808 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 09:52:13,808 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:13,809 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:13,809 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 09:52:13,809 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:13,809 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:13,809 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 6} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:13,810 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:13,810 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:13,810 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:13,815 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-05 09:52:13,815 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,815 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,815 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,816 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,816 - ExplorationExpert - INFO - 探索路径生成完成，成本: 63542.0, 路径长度: 66, 收集中间解: 3
2025-08-05 09:52:13,816 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [41, 20, 28, 29, 27, 22, 26, 11, 65, 54, 56, 63, 9, 52, 4, 8, 53, 7, 61, 0, 1, 62, 21, 37, 6, 23, 5, 57, 49, 39, 44, 50, 46, 14, 3, 55, 40, 12, 30, 13, 24, 25, 2, 17, 32, 34, 31, 10, 60, 47, 51, 38, 43, 18, 19, 33, 16, 15, 36, 35, 48, 42, 58, 59, 64, 45], 'cur_cost': 63542.0, 'intermediate_solutions': [{'tour': [0, 10, 5, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 36, 37, 25, 26, 27, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12421.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 5, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 42, 48, 43], 'cur_cost': 12351.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 5, 20, 21, 13, 23, 16, 50, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 15187.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:13,816 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 63542.00)
2025-08-05 09:52:13,817 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:13,817 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:13,817 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:13,819 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 09:52:13,819 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,819 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,820 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,820 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,820 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14691.0, 路径长度: 66, 收集中间解: 3
2025-08-05 09:52:13,820 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 18, 6, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14691.0, 'intermediate_solutions': [{'tour': [0, 17, 19, 18, 16, 23, 22, 12, 15, 14, 20, 21, 13, 10, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 27, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 18255.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 19, 18, 16, 23, 22, 12, 15, 14, 35, 36, 26, 25, 37, 27, 13, 21, 20, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14762.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 19, 18, 16, 23, 22, 12, 15, 14, 20, 21, 13, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 9, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 16179.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:13,821 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 14691.00)
2025-08-05 09:52:13,821 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:13,821 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:13,821 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:13,823 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 09:52:13,823 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,823 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,824 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,824 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,824 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12726.0, 路径长度: 66, 收集中间解: 3
2025-08-05 09:52:13,824 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 17, 15, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12726.0, 'intermediate_solutions': [{'tour': [0, 3, 22, 8, 2, 6, 4, 14, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 5, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 15319.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 22, 8, 2, 6, 4, 5, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 37, 27, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12763.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 22, 8, 2, 6, 4, 5, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 41, 45, 38, 51, 50, 42], 'cur_cost': 12673.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:13,825 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 12726.00)
2025-08-05 09:52:13,825 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:52:13,825 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:13,825 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:13,825 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 99260.0
2025-08-05 09:52:13,836 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 09:52:13,836 - ExploitationExpert - INFO - res_population_costs: [9521, 9534.0]
2025-08-05 09:52:13,836 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64)]
2025-08-05 09:52:13,837 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:13,837 - ExploitationExpert - INFO - populations: [{'tour': [41, 20, 28, 29, 27, 22, 26, 11, 65, 54, 56, 63, 9, 52, 4, 8, 53, 7, 61, 0, 1, 62, 21, 37, 6, 23, 5, 57, 49, 39, 44, 50, 46, 14, 3, 55, 40, 12, 30, 13, 24, 25, 2, 17, 32, 34, 31, 10, 60, 47, 51, 38, 43, 18, 19, 33, 16, 15, 36, 35, 48, 42, 58, 59, 64, 45], 'cur_cost': 63542.0}, {'tour': [0, 18, 6, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14691.0}, {'tour': [0, 17, 15, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12726.0}, {'tour': array([43,  8, 42, 63,  1, 24, 31, 38, 15, 37, 57, 22, 49, 27, 65, 53, 17,
       25, 64, 60,  3,  5,  4, 48, 18,  6, 54, 30, 33, 16, 36, 10, 44, 56,
        9, 45, 21, 29, 19, 34, 26, 14, 50, 47,  7, 35, 13, 52, 12, 58, 55,
       59, 41, 40,  2,  0, 51, 28, 11, 23, 39, 32, 20, 61, 62, 46],
      dtype=int64), 'cur_cost': 99260.0}, {'tour': [18, 13, 4, 12, 14, 22, 1, 17, 19, 26, 35, 20, 25, 34, 33, 31, 29, 3, 49, 43, 9, 5, 23, 2, 6, 21, 46, 44, 56, 64, 36, 60, 53, 61, 40, 55, 38, 42, 45, 59, 51, 32, 15, 57, 39, 37, 11, 16, 62, 52, 50, 24, 8, 10, 7, 41, 48, 63, 27, 0, 65, 54, 30, 47, 58, 28], 'cur_cost': 90100.0}, {'tour': [44, 18, 29, 14, 19, 9, 10, 62, 55, 7, 64, 0, 3, 1, 61, 53, 63, 56, 57, 39, 17, 20, 25, 22, 34, 12, 15, 21, 33, 5, 23, 35, 4, 11, 16, 43, 27, 40, 51, 47, 50, 46, 48, 30, 31, 24, 37, 2, 6, 13, 49, 45, 36, 38, 32, 26, 42, 28, 41, 8, 59, 58, 54, 60, 65, 52], 'cur_cost': 61299.0}, {'tour': [0, 6, 22, 20, 21, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12338.0}, {'tour': [53, 51, 11, 10, 50, 18, 17, 19, 8, 3, 42, 28, 63, 32, 49, 35, 4, 61, 31, 26, 21, 64, 30, 38, 29, 20, 48, 1, 40, 34, 15, 52, 65, 59, 27, 60, 45, 23, 25, 24, 22, 43, 41, 58, 0, 62, 5, 57, 9, 37, 16, 33, 6, 14, 7, 12, 13, 56, 44, 46, 36, 55, 2, 39, 47, 54], 'cur_cost': 103174.0}, {'tour': [7, 29, 1, 28, 13, 37, 46, 27, 43, 52, 11, 34, 40, 51, 9, 42, 45, 60, 58, 15, 62, 39, 20, 2, 41, 25, 10, 5, 56, 50, 8, 65, 19, 18, 6, 36, 47, 35, 32, 61, 48, 12, 33, 64, 57, 30, 22, 14, 53, 4, 54, 3, 63, 0, 38, 21, 24, 44, 16, 49, 26, 59, 23, 17, 31, 55], 'cur_cost': 108562.0}, {'tour': [12, 24, 23, 34, 13, 27, 0, 14, 11, 53, 61, 65, 5, 52, 2, 16, 6, 21, 18, 4, 20, 32, 9, 62, 47, 49, 40, 44, 39, 15, 28, 26, 7, 8, 54, 58, 57, 56, 22, 35, 37, 10, 60, 48, 36, 33, 29, 43, 41, 45, 19, 1, 55, 46, 42, 30, 31, 3, 17, 25, 51, 38, 59, 64, 63, 50], 'cur_cost': 69941.0}]
2025-08-05 09:52:13,838 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:13,838 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 320, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 320, 'cache_hits': 0, 'similarity_calculations': 1618, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:13,839 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([43,  8, 42, 63,  1, 24, 31, 38, 15, 37, 57, 22, 49, 27, 65, 53, 17,
       25, 64, 60,  3,  5,  4, 48, 18,  6, 54, 30, 33, 16, 36, 10, 44, 56,
        9, 45, 21, 29, 19, 34, 26, 14, 50, 47,  7, 35, 13, 52, 12, 58, 55,
       59, 41, 40,  2,  0, 51, 28, 11, 23, 39, 32, 20, 61, 62, 46],
      dtype=int64), 'cur_cost': 99260.0, 'intermediate_solutions': [{'tour': array([28, 16, 27, 38, 30, 53, 32, 36, 41, 43,  9, 20,  8, 26, 51, 50, 57,
        7, 23, 13, 12, 37,  5, 29, 63, 55, 65, 39, 11,  1,  3,  0, 15, 40,
       33, 47, 56, 14, 58, 22, 10, 54, 49, 24,  6, 48, 46, 52, 21,  2, 19,
       34, 64, 62, 61, 25, 44, 35, 45, 60,  4, 18, 17, 59, 42, 31]), 'cur_cost': 102940.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([38, 28, 16, 27, 30, 53, 32, 36, 41, 43,  9, 20,  8, 26, 51, 50, 57,
        7, 23, 13, 12, 37,  5, 29, 63, 55, 65, 39, 11,  1,  3,  0, 15, 40,
       33, 47, 56, 14, 58, 22, 10, 54, 49, 24,  6, 48, 46, 52, 21,  2, 19,
       34, 64, 62, 61, 25, 44, 35, 45, 60,  4, 18, 17, 59, 42, 31]), 'cur_cost': 103043.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([30, 38, 28, 16, 27, 53, 32, 36, 41, 43,  9, 20,  8, 26, 51, 50, 57,
        7, 23, 13, 12, 37,  5, 29, 63, 55, 65, 39, 11,  1,  3,  0, 15, 40,
       33, 47, 56, 14, 58, 22, 10, 54, 49, 24,  6, 48, 46, 52, 21,  2, 19,
       34, 64, 62, 61, 25, 44, 35, 45, 60,  4, 18, 17, 59, 42, 31]), 'cur_cost': 102903.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([27, 38, 28, 16, 30, 53, 32, 36, 41, 43,  9, 20,  8, 26, 51, 50, 57,
        7, 23, 13, 12, 37,  5, 29, 63, 55, 65, 39, 11,  1,  3,  0, 15, 40,
       33, 47, 56, 14, 58, 22, 10, 54, 49, 24,  6, 48, 46, 52, 21,  2, 19,
       34, 64, 62, 61, 25, 44, 35, 45, 60,  4, 18, 17, 59, 42, 31]), 'cur_cost': 103036.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([27, 30, 38, 28, 16, 53, 32, 36, 41, 43,  9, 20,  8, 26, 51, 50, 57,
        7, 23, 13, 12, 37,  5, 29, 63, 55, 65, 39, 11,  1,  3,  0, 15, 40,
       33, 47, 56, 14, 58, 22, 10, 54, 49, 24,  6, 48, 46, 52, 21,  2, 19,
       34, 64, 62, 61, 25, 44, 35, 45, 60,  4, 18, 17, 59, 42, 31]), 'cur_cost': 100930.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:13,839 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 99260.00)
2025-08-05 09:52:13,839 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:13,839 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:13,839 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:13,841 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 09:52:13,842 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,842 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,842 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,842 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,843 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12842.0, 路径长度: 66, 收集中间解: 3
2025-08-05 09:52:13,843 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 20, 6, 9, 11, 7, 3, 1, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12842.0, 'intermediate_solutions': [{'tour': [18, 13, 4, 12, 14, 22, 1, 17, 19, 26, 35, 20, 25, 34, 33, 31, 29, 3, 49, 43, 9, 5, 23, 6, 2, 21, 46, 44, 56, 64, 36, 60, 53, 61, 40, 55, 38, 42, 45, 59, 51, 32, 15, 57, 39, 37, 11, 16, 62, 52, 50, 24, 8, 10, 7, 41, 48, 63, 27, 0, 65, 54, 30, 47, 58, 28], 'cur_cost': 90100.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [18, 13, 4, 12, 61, 53, 60, 36, 64, 56, 44, 46, 21, 6, 2, 23, 5, 9, 43, 49, 3, 29, 31, 33, 34, 25, 20, 35, 26, 19, 17, 1, 22, 14, 40, 55, 38, 42, 45, 59, 51, 32, 15, 57, 39, 37, 11, 16, 62, 52, 50, 24, 8, 10, 7, 41, 48, 63, 27, 0, 65, 54, 30, 47, 58, 28], 'cur_cost': 90533.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [18, 13, 4, 12, 14, 22, 1, 17, 19, 26, 35, 20, 25, 34, 33, 31, 29, 3, 49, 43, 9, 5, 23, 2, 6, 21, 46, 44, 56, 64, 36, 60, 53, 61, 40, 55, 38, 59, 42, 45, 51, 32, 15, 57, 39, 37, 11, 16, 62, 52, 50, 24, 8, 10, 7, 41, 48, 63, 27, 0, 65, 54, 30, 47, 58, 28], 'cur_cost': 90090.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:13,843 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 12842.00)
2025-08-05 09:52:13,843 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:13,843 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:13,843 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:13,848 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-05 09:52:13,848 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,849 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,849 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,849 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,849 - ExplorationExpert - INFO - 探索路径生成完成，成本: 53784.0, 路径长度: 66, 收集中间解: 3
2025-08-05 09:52:13,849 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [60, 62, 23, 37, 26, 22, 3, 1, 65, 61, 17, 12, 29, 18, 15, 11, 54, 8, 63, 53, 59, 55, 7, 10, 14, 28, 0, 24, 27, 6, 64, 52, 58, 9, 13, 19, 35, 4, 34, 31, 16, 21, 2, 33, 49, 40, 43, 50, 41, 45, 51, 39, 46, 20, 32, 30, 36, 48, 42, 38, 25, 47, 44, 5, 56, 57], 'cur_cost': 53784.0, 'intermediate_solutions': [{'tour': [44, 18, 29, 14, 19, 9, 10, 62, 48, 7, 64, 0, 3, 1, 61, 53, 63, 56, 57, 39, 17, 20, 25, 22, 34, 12, 15, 21, 33, 5, 23, 35, 4, 11, 16, 43, 27, 40, 51, 47, 50, 46, 55, 30, 31, 24, 37, 2, 6, 13, 49, 45, 36, 38, 32, 26, 42, 28, 41, 8, 59, 58, 54, 60, 65, 52], 'cur_cost': 69151.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [44, 18, 29, 14, 19, 9, 10, 62, 55, 7, 64, 0, 3, 1, 61, 53, 63, 56, 57, 39, 17, 20, 25, 22, 34, 12, 15, 21, 33, 5, 23, 35, 4, 11, 16, 43, 27, 40, 51, 47, 50, 46, 58, 59, 8, 41, 28, 42, 26, 32, 38, 36, 45, 49, 13, 6, 2, 37, 24, 31, 30, 48, 54, 60, 65, 52], 'cur_cost': 66817.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [44, 18, 29, 19, 9, 10, 62, 55, 7, 64, 0, 3, 14, 1, 61, 53, 63, 56, 57, 39, 17, 20, 25, 22, 34, 12, 15, 21, 33, 5, 23, 35, 4, 11, 16, 43, 27, 40, 51, 47, 50, 46, 48, 30, 31, 24, 37, 2, 6, 13, 49, 45, 36, 38, 32, 26, 42, 28, 41, 8, 59, 58, 54, 60, 65, 52], 'cur_cost': 63541.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:13,850 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 53784.00)
2025-08-05 09:52:13,850 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:13,850 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:13,850 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:13,851 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-05 09:52:13,852 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,852 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,852 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,853 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,853 - ExplorationExpert - INFO - 探索路径生成完成，成本: 93020.0, 路径长度: 66, 收集中间解: 3
2025-08-05 09:52:13,853 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [17, 15, 6, 27, 26, 4, 9, 54, 20, 3, 13, 55, 8, 62, 7, 61, 0, 58, 60, 21, 37, 14, 23, 5, 63, 49, 39, 50, 46, 19, 40, 12, 30, 16, 25, 41, 32, 31, 10, 53, 1, 51, 38, 48, 18, 33, 57, 44, 64, 42, 59, 29, 22, 52, 34, 56, 2, 28, 45, 43, 65, 11, 24, 47, 35, 36], 'cur_cost': 93020.0, 'intermediate_solutions': [{'tour': [0, 6, 22, 20, 21, 13, 25, 16, 18, 12, 17, 15, 14, 19, 27, 37, 23, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 17002.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 22, 20, 21, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 32, 29, 24, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12345.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 22, 20, 21, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 9, 40, 43, 48, 42], 'cur_cost': 17228.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:13,853 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 93020.00)
2025-08-05 09:52:13,853 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 09:52:13,853 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:13,854 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:13,854 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 109100.0
2025-08-05 09:52:13,862 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 09:52:13,862 - ExploitationExpert - INFO - res_population_costs: [9521, 9534.0]
2025-08-05 09:52:13,863 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64)]
2025-08-05 09:52:13,864 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:13,864 - ExploitationExpert - INFO - populations: [{'tour': [41, 20, 28, 29, 27, 22, 26, 11, 65, 54, 56, 63, 9, 52, 4, 8, 53, 7, 61, 0, 1, 62, 21, 37, 6, 23, 5, 57, 49, 39, 44, 50, 46, 14, 3, 55, 40, 12, 30, 13, 24, 25, 2, 17, 32, 34, 31, 10, 60, 47, 51, 38, 43, 18, 19, 33, 16, 15, 36, 35, 48, 42, 58, 59, 64, 45], 'cur_cost': 63542.0}, {'tour': [0, 18, 6, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14691.0}, {'tour': [0, 17, 15, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12726.0}, {'tour': array([43,  8, 42, 63,  1, 24, 31, 38, 15, 37, 57, 22, 49, 27, 65, 53, 17,
       25, 64, 60,  3,  5,  4, 48, 18,  6, 54, 30, 33, 16, 36, 10, 44, 56,
        9, 45, 21, 29, 19, 34, 26, 14, 50, 47,  7, 35, 13, 52, 12, 58, 55,
       59, 41, 40,  2,  0, 51, 28, 11, 23, 39, 32, 20, 61, 62, 46],
      dtype=int64), 'cur_cost': 99260.0}, {'tour': [0, 20, 6, 9, 11, 7, 3, 1, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12842.0}, {'tour': [60, 62, 23, 37, 26, 22, 3, 1, 65, 61, 17, 12, 29, 18, 15, 11, 54, 8, 63, 53, 59, 55, 7, 10, 14, 28, 0, 24, 27, 6, 64, 52, 58, 9, 13, 19, 35, 4, 34, 31, 16, 21, 2, 33, 49, 40, 43, 50, 41, 45, 51, 39, 46, 20, 32, 30, 36, 48, 42, 38, 25, 47, 44, 5, 56, 57], 'cur_cost': 53784.0}, {'tour': [17, 15, 6, 27, 26, 4, 9, 54, 20, 3, 13, 55, 8, 62, 7, 61, 0, 58, 60, 21, 37, 14, 23, 5, 63, 49, 39, 50, 46, 19, 40, 12, 30, 16, 25, 41, 32, 31, 10, 53, 1, 51, 38, 48, 18, 33, 57, 44, 64, 42, 59, 29, 22, 52, 34, 56, 2, 28, 45, 43, 65, 11, 24, 47, 35, 36], 'cur_cost': 93020.0}, {'tour': array([49, 31, 48, 54, 60, 13, 17, 51, 55, 32, 14, 65, 21, 56, 52, 46, 16,
       35, 64,  2,  3, 29, 61, 27,  9, 58, 42,  6, 15, 10, 19, 12, 38, 45,
       40, 34, 63, 44, 50, 53, 41, 25,  0, 23,  5, 30, 18,  1, 37, 39,  4,
       20, 28, 47, 43,  7, 24, 22, 33,  8, 62, 59, 11, 26, 36, 57],
      dtype=int64), 'cur_cost': 109100.0}, {'tour': [7, 29, 1, 28, 13, 37, 46, 27, 43, 52, 11, 34, 40, 51, 9, 42, 45, 60, 58, 15, 62, 39, 20, 2, 41, 25, 10, 5, 56, 50, 8, 65, 19, 18, 6, 36, 47, 35, 32, 61, 48, 12, 33, 64, 57, 30, 22, 14, 53, 4, 54, 3, 63, 0, 38, 21, 24, 44, 16, 49, 26, 59, 23, 17, 31, 55], 'cur_cost': 108562.0}, {'tour': [12, 24, 23, 34, 13, 27, 0, 14, 11, 53, 61, 65, 5, 52, 2, 16, 6, 21, 18, 4, 20, 32, 9, 62, 47, 49, 40, 44, 39, 15, 28, 26, 7, 8, 54, 58, 57, 56, 22, 35, 37, 10, 60, 48, 36, 33, 29, 43, 41, 45, 19, 1, 55, 46, 42, 30, 31, 3, 17, 25, 51, 38, 59, 64, 63, 50], 'cur_cost': 69941.0}]
2025-08-05 09:52:13,865 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:13,865 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 321, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 321, 'cache_hits': 0, 'similarity_calculations': 1626, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:13,866 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([49, 31, 48, 54, 60, 13, 17, 51, 55, 32, 14, 65, 21, 56, 52, 46, 16,
       35, 64,  2,  3, 29, 61, 27,  9, 58, 42,  6, 15, 10, 19, 12, 38, 45,
       40, 34, 63, 44, 50, 53, 41, 25,  0, 23,  5, 30, 18,  1, 37, 39,  4,
       20, 28, 47, 43,  7, 24, 22, 33,  8, 62, 59, 11, 26, 36, 57],
      dtype=int64), 'cur_cost': 109100.0, 'intermediate_solutions': [{'tour': array([11, 51, 53, 10, 50, 18, 17, 19,  8,  3, 42, 28, 63, 32, 49, 35,  4,
       61, 31, 26, 21, 64, 30, 38, 29, 20, 48,  1, 40, 34, 15, 52, 65, 59,
       27, 60, 45, 23, 25, 24, 22, 43, 41, 58,  0, 62,  5, 57,  9, 37, 16,
       33,  6, 14,  7, 12, 13, 56, 44, 46, 36, 55,  2, 39, 47, 54]), 'cur_cost': 105168.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([10, 11, 51, 53, 50, 18, 17, 19,  8,  3, 42, 28, 63, 32, 49, 35,  4,
       61, 31, 26, 21, 64, 30, 38, 29, 20, 48,  1, 40, 34, 15, 52, 65, 59,
       27, 60, 45, 23, 25, 24, 22, 43, 41, 58,  0, 62,  5, 57,  9, 37, 16,
       33,  6, 14,  7, 12, 13, 56, 44, 46, 36, 55,  2, 39, 47, 54]), 'cur_cost': 104446.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([50, 10, 11, 51, 53, 18, 17, 19,  8,  3, 42, 28, 63, 32, 49, 35,  4,
       61, 31, 26, 21, 64, 30, 38, 29, 20, 48,  1, 40, 34, 15, 52, 65, 59,
       27, 60, 45, 23, 25, 24, 22, 43, 41, 58,  0, 62,  5, 57,  9, 37, 16,
       33,  6, 14,  7, 12, 13, 56, 44, 46, 36, 55,  2, 39, 47, 54]), 'cur_cost': 106505.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([53, 10, 11, 51, 50, 18, 17, 19,  8,  3, 42, 28, 63, 32, 49, 35,  4,
       61, 31, 26, 21, 64, 30, 38, 29, 20, 48,  1, 40, 34, 15, 52, 65, 59,
       27, 60, 45, 23, 25, 24, 22, 43, 41, 58,  0, 62,  5, 57,  9, 37, 16,
       33,  6, 14,  7, 12, 13, 56, 44, 46, 36, 55,  2, 39, 47, 54]), 'cur_cost': 98774.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([53, 50, 10, 11, 51, 18, 17, 19,  8,  3, 42, 28, 63, 32, 49, 35,  4,
       61, 31, 26, 21, 64, 30, 38, 29, 20, 48,  1, 40, 34, 15, 52, 65, 59,
       27, 60, 45, 23, 25, 24, 22, 43, 41, 58,  0, 62,  5, 57,  9, 37, 16,
       33,  6, 14,  7, 12, 13, 56, 44, 46, 36, 55,  2, 39, 47, 54]), 'cur_cost': 103184.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:13,866 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 109100.00)
2025-08-05 09:52:13,866 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:52:13,866 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:13,866 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:13,867 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 100936.0
2025-08-05 09:52:13,875 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 09:52:13,875 - ExploitationExpert - INFO - res_population_costs: [9521, 9534.0]
2025-08-05 09:52:13,875 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64)]
2025-08-05 09:52:13,876 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:13,877 - ExploitationExpert - INFO - populations: [{'tour': [41, 20, 28, 29, 27, 22, 26, 11, 65, 54, 56, 63, 9, 52, 4, 8, 53, 7, 61, 0, 1, 62, 21, 37, 6, 23, 5, 57, 49, 39, 44, 50, 46, 14, 3, 55, 40, 12, 30, 13, 24, 25, 2, 17, 32, 34, 31, 10, 60, 47, 51, 38, 43, 18, 19, 33, 16, 15, 36, 35, 48, 42, 58, 59, 64, 45], 'cur_cost': 63542.0}, {'tour': [0, 18, 6, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14691.0}, {'tour': [0, 17, 15, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12726.0}, {'tour': array([43,  8, 42, 63,  1, 24, 31, 38, 15, 37, 57, 22, 49, 27, 65, 53, 17,
       25, 64, 60,  3,  5,  4, 48, 18,  6, 54, 30, 33, 16, 36, 10, 44, 56,
        9, 45, 21, 29, 19, 34, 26, 14, 50, 47,  7, 35, 13, 52, 12, 58, 55,
       59, 41, 40,  2,  0, 51, 28, 11, 23, 39, 32, 20, 61, 62, 46],
      dtype=int64), 'cur_cost': 99260.0}, {'tour': [0, 20, 6, 9, 11, 7, 3, 1, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12842.0}, {'tour': [60, 62, 23, 37, 26, 22, 3, 1, 65, 61, 17, 12, 29, 18, 15, 11, 54, 8, 63, 53, 59, 55, 7, 10, 14, 28, 0, 24, 27, 6, 64, 52, 58, 9, 13, 19, 35, 4, 34, 31, 16, 21, 2, 33, 49, 40, 43, 50, 41, 45, 51, 39, 46, 20, 32, 30, 36, 48, 42, 38, 25, 47, 44, 5, 56, 57], 'cur_cost': 53784.0}, {'tour': [17, 15, 6, 27, 26, 4, 9, 54, 20, 3, 13, 55, 8, 62, 7, 61, 0, 58, 60, 21, 37, 14, 23, 5, 63, 49, 39, 50, 46, 19, 40, 12, 30, 16, 25, 41, 32, 31, 10, 53, 1, 51, 38, 48, 18, 33, 57, 44, 64, 42, 59, 29, 22, 52, 34, 56, 2, 28, 45, 43, 65, 11, 24, 47, 35, 36], 'cur_cost': 93020.0}, {'tour': array([49, 31, 48, 54, 60, 13, 17, 51, 55, 32, 14, 65, 21, 56, 52, 46, 16,
       35, 64,  2,  3, 29, 61, 27,  9, 58, 42,  6, 15, 10, 19, 12, 38, 45,
       40, 34, 63, 44, 50, 53, 41, 25,  0, 23,  5, 30, 18,  1, 37, 39,  4,
       20, 28, 47, 43,  7, 24, 22, 33,  8, 62, 59, 11, 26, 36, 57],
      dtype=int64), 'cur_cost': 109100.0}, {'tour': array([39, 44, 48, 29, 19, 17, 34, 55, 57, 42, 45,  7,  4,  1, 64, 30,  8,
       49, 23, 50, 31, 63, 40, 11, 26, 12, 21, 53, 32, 27,  2, 25, 16, 35,
       15, 18, 24, 60, 58, 20, 33, 14, 36, 54, 47,  5, 10, 65, 13, 38, 37,
       52, 28, 59, 61,  6, 56, 51, 41, 62,  3,  9, 43, 46, 22,  0],
      dtype=int64), 'cur_cost': 100936.0}, {'tour': [12, 24, 23, 34, 13, 27, 0, 14, 11, 53, 61, 65, 5, 52, 2, 16, 6, 21, 18, 4, 20, 32, 9, 62, 47, 49, 40, 44, 39, 15, 28, 26, 7, 8, 54, 58, 57, 56, 22, 35, 37, 10, 60, 48, 36, 33, 29, 43, 41, 45, 19, 1, 55, 46, 42, 30, 31, 3, 17, 25, 51, 38, 59, 64, 63, 50], 'cur_cost': 69941.0}]
2025-08-05 09:52:13,879 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:13,879 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 322, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 322, 'cache_hits': 0, 'similarity_calculations': 1635, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:13,880 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([39, 44, 48, 29, 19, 17, 34, 55, 57, 42, 45,  7,  4,  1, 64, 30,  8,
       49, 23, 50, 31, 63, 40, 11, 26, 12, 21, 53, 32, 27,  2, 25, 16, 35,
       15, 18, 24, 60, 58, 20, 33, 14, 36, 54, 47,  5, 10, 65, 13, 38, 37,
       52, 28, 59, 61,  6, 56, 51, 41, 62,  3,  9, 43, 46, 22,  0],
      dtype=int64), 'cur_cost': 100936.0, 'intermediate_solutions': [{'tour': array([ 1, 29,  7, 28, 13, 37, 46, 27, 43, 52, 11, 34, 40, 51,  9, 42, 45,
       60, 58, 15, 62, 39, 20,  2, 41, 25, 10,  5, 56, 50,  8, 65, 19, 18,
        6, 36, 47, 35, 32, 61, 48, 12, 33, 64, 57, 30, 22, 14, 53,  4, 54,
        3, 63,  0, 38, 21, 24, 44, 16, 49, 26, 59, 23, 17, 31, 55]), 'cur_cost': 108532.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([28,  1, 29,  7, 13, 37, 46, 27, 43, 52, 11, 34, 40, 51,  9, 42, 45,
       60, 58, 15, 62, 39, 20,  2, 41, 25, 10,  5, 56, 50,  8, 65, 19, 18,
        6, 36, 47, 35, 32, 61, 48, 12, 33, 64, 57, 30, 22, 14, 53,  4, 54,
        3, 63,  0, 38, 21, 24, 44, 16, 49, 26, 59, 23, 17, 31, 55]), 'cur_cost': 110491.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([13, 28,  1, 29,  7, 37, 46, 27, 43, 52, 11, 34, 40, 51,  9, 42, 45,
       60, 58, 15, 62, 39, 20,  2, 41, 25, 10,  5, 56, 50,  8, 65, 19, 18,
        6, 36, 47, 35, 32, 61, 48, 12, 33, 64, 57, 30, 22, 14, 53,  4, 54,
        3, 63,  0, 38, 21, 24, 44, 16, 49, 26, 59, 23, 17, 31, 55]), 'cur_cost': 110197.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 7, 28,  1, 29, 13, 37, 46, 27, 43, 52, 11, 34, 40, 51,  9, 42, 45,
       60, 58, 15, 62, 39, 20,  2, 41, 25, 10,  5, 56, 50,  8, 65, 19, 18,
        6, 36, 47, 35, 32, 61, 48, 12, 33, 64, 57, 30, 22, 14, 53,  4, 54,
        3, 63,  0, 38, 21, 24, 44, 16, 49, 26, 59, 23, 17, 31, 55]), 'cur_cost': 108591.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 7, 13, 28,  1, 29, 37, 46, 27, 43, 52, 11, 34, 40, 51,  9, 42, 45,
       60, 58, 15, 62, 39, 20,  2, 41, 25, 10,  5, 56, 50,  8, 65, 19, 18,
        6, 36, 47, 35, 32, 61, 48, 12, 33, 64, 57, 30, 22, 14, 53,  4, 54,
        3, 63,  0, 38, 21, 24, 44, 16, 49, 26, 59, 23, 17, 31, 55]), 'cur_cost': 106725.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:13,880 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 100936.00)
2025-08-05 09:52:13,880 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:13,880 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:13,881 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:13,883 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 09:52:13,884 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,884 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,884 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,884 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:13,885 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12875.0, 路径长度: 66, 收集中间解: 3
2025-08-05 09:52:13,885 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 8, 13, 7, 3, 9, 11, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12875.0, 'intermediate_solutions': [{'tour': [12, 24, 23, 34, 13, 27, 0, 14, 11, 53, 61, 65, 5, 52, 2, 46, 6, 21, 18, 4, 20, 32, 9, 62, 47, 49, 40, 44, 39, 15, 28, 26, 7, 8, 54, 58, 57, 56, 22, 35, 37, 10, 60, 48, 36, 33, 29, 43, 41, 45, 19, 1, 55, 16, 42, 30, 31, 3, 17, 25, 51, 38, 59, 64, 63, 50], 'cur_cost': 72887.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [30, 42, 46, 55, 1, 19, 45, 41, 43, 29, 33, 36, 48, 60, 10, 37, 35, 22, 56, 57, 58, 54, 8, 7, 26, 28, 15, 39, 44, 40, 49, 47, 62, 9, 32, 20, 4, 18, 21, 6, 16, 2, 52, 5, 65, 61, 53, 11, 14, 0, 27, 13, 34, 23, 24, 12, 31, 3, 17, 25, 51, 38, 59, 64, 63, 50], 'cur_cost': 71769.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [12, 24, 23, 34, 13, 27, 0, 14, 53, 61, 65, 5, 52, 2, 16, 6, 21, 18, 4, 20, 32, 9, 62, 47, 49, 40, 44, 39, 15, 28, 26, 7, 8, 54, 58, 57, 56, 22, 35, 37, 10, 60, 11, 48, 36, 33, 29, 43, 41, 45, 19, 1, 55, 46, 42, 30, 31, 3, 17, 25, 51, 38, 59, 64, 63, 50], 'cur_cost': 70346.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:13,885 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 12875.00)
2025-08-05 09:52:13,885 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:13,885 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:13,888 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [41, 20, 28, 29, 27, 22, 26, 11, 65, 54, 56, 63, 9, 52, 4, 8, 53, 7, 61, 0, 1, 62, 21, 37, 6, 23, 5, 57, 49, 39, 44, 50, 46, 14, 3, 55, 40, 12, 30, 13, 24, 25, 2, 17, 32, 34, 31, 10, 60, 47, 51, 38, 43, 18, 19, 33, 16, 15, 36, 35, 48, 42, 58, 59, 64, 45], 'cur_cost': 63542.0, 'intermediate_solutions': [{'tour': [0, 10, 5, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 36, 37, 25, 26, 27, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12421.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 5, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 42, 48, 43], 'cur_cost': 12351.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 5, 20, 21, 13, 23, 16, 50, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 15187.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 6, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14691.0, 'intermediate_solutions': [{'tour': [0, 17, 19, 18, 16, 23, 22, 12, 15, 14, 20, 21, 13, 10, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 27, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 18255.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 19, 18, 16, 23, 22, 12, 15, 14, 35, 36, 26, 25, 37, 27, 13, 21, 20, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14762.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 19, 18, 16, 23, 22, 12, 15, 14, 20, 21, 13, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 9, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 16179.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 15, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12726.0, 'intermediate_solutions': [{'tour': [0, 3, 22, 8, 2, 6, 4, 14, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 5, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 15319.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 22, 8, 2, 6, 4, 5, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 37, 27, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12763.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 22, 8, 2, 6, 4, 5, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 41, 45, 38, 51, 50, 42], 'cur_cost': 12673.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([43,  8, 42, 63,  1, 24, 31, 38, 15, 37, 57, 22, 49, 27, 65, 53, 17,
       25, 64, 60,  3,  5,  4, 48, 18,  6, 54, 30, 33, 16, 36, 10, 44, 56,
        9, 45, 21, 29, 19, 34, 26, 14, 50, 47,  7, 35, 13, 52, 12, 58, 55,
       59, 41, 40,  2,  0, 51, 28, 11, 23, 39, 32, 20, 61, 62, 46],
      dtype=int64), 'cur_cost': 99260.0, 'intermediate_solutions': [{'tour': array([28, 16, 27, 38, 30, 53, 32, 36, 41, 43,  9, 20,  8, 26, 51, 50, 57,
        7, 23, 13, 12, 37,  5, 29, 63, 55, 65, 39, 11,  1,  3,  0, 15, 40,
       33, 47, 56, 14, 58, 22, 10, 54, 49, 24,  6, 48, 46, 52, 21,  2, 19,
       34, 64, 62, 61, 25, 44, 35, 45, 60,  4, 18, 17, 59, 42, 31]), 'cur_cost': 102940.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([38, 28, 16, 27, 30, 53, 32, 36, 41, 43,  9, 20,  8, 26, 51, 50, 57,
        7, 23, 13, 12, 37,  5, 29, 63, 55, 65, 39, 11,  1,  3,  0, 15, 40,
       33, 47, 56, 14, 58, 22, 10, 54, 49, 24,  6, 48, 46, 52, 21,  2, 19,
       34, 64, 62, 61, 25, 44, 35, 45, 60,  4, 18, 17, 59, 42, 31]), 'cur_cost': 103043.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([30, 38, 28, 16, 27, 53, 32, 36, 41, 43,  9, 20,  8, 26, 51, 50, 57,
        7, 23, 13, 12, 37,  5, 29, 63, 55, 65, 39, 11,  1,  3,  0, 15, 40,
       33, 47, 56, 14, 58, 22, 10, 54, 49, 24,  6, 48, 46, 52, 21,  2, 19,
       34, 64, 62, 61, 25, 44, 35, 45, 60,  4, 18, 17, 59, 42, 31]), 'cur_cost': 102903.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([27, 38, 28, 16, 30, 53, 32, 36, 41, 43,  9, 20,  8, 26, 51, 50, 57,
        7, 23, 13, 12, 37,  5, 29, 63, 55, 65, 39, 11,  1,  3,  0, 15, 40,
       33, 47, 56, 14, 58, 22, 10, 54, 49, 24,  6, 48, 46, 52, 21,  2, 19,
       34, 64, 62, 61, 25, 44, 35, 45, 60,  4, 18, 17, 59, 42, 31]), 'cur_cost': 103036.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([27, 30, 38, 28, 16, 53, 32, 36, 41, 43,  9, 20,  8, 26, 51, 50, 57,
        7, 23, 13, 12, 37,  5, 29, 63, 55, 65, 39, 11,  1,  3,  0, 15, 40,
       33, 47, 56, 14, 58, 22, 10, 54, 49, 24,  6, 48, 46, 52, 21,  2, 19,
       34, 64, 62, 61, 25, 44, 35, 45, 60,  4, 18, 17, 59, 42, 31]), 'cur_cost': 100930.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 6, 9, 11, 7, 3, 1, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12842.0, 'intermediate_solutions': [{'tour': [18, 13, 4, 12, 14, 22, 1, 17, 19, 26, 35, 20, 25, 34, 33, 31, 29, 3, 49, 43, 9, 5, 23, 6, 2, 21, 46, 44, 56, 64, 36, 60, 53, 61, 40, 55, 38, 42, 45, 59, 51, 32, 15, 57, 39, 37, 11, 16, 62, 52, 50, 24, 8, 10, 7, 41, 48, 63, 27, 0, 65, 54, 30, 47, 58, 28], 'cur_cost': 90100.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [18, 13, 4, 12, 61, 53, 60, 36, 64, 56, 44, 46, 21, 6, 2, 23, 5, 9, 43, 49, 3, 29, 31, 33, 34, 25, 20, 35, 26, 19, 17, 1, 22, 14, 40, 55, 38, 42, 45, 59, 51, 32, 15, 57, 39, 37, 11, 16, 62, 52, 50, 24, 8, 10, 7, 41, 48, 63, 27, 0, 65, 54, 30, 47, 58, 28], 'cur_cost': 90533.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [18, 13, 4, 12, 14, 22, 1, 17, 19, 26, 35, 20, 25, 34, 33, 31, 29, 3, 49, 43, 9, 5, 23, 2, 6, 21, 46, 44, 56, 64, 36, 60, 53, 61, 40, 55, 38, 59, 42, 45, 51, 32, 15, 57, 39, 37, 11, 16, 62, 52, 50, 24, 8, 10, 7, 41, 48, 63, 27, 0, 65, 54, 30, 47, 58, 28], 'cur_cost': 90090.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [60, 62, 23, 37, 26, 22, 3, 1, 65, 61, 17, 12, 29, 18, 15, 11, 54, 8, 63, 53, 59, 55, 7, 10, 14, 28, 0, 24, 27, 6, 64, 52, 58, 9, 13, 19, 35, 4, 34, 31, 16, 21, 2, 33, 49, 40, 43, 50, 41, 45, 51, 39, 46, 20, 32, 30, 36, 48, 42, 38, 25, 47, 44, 5, 56, 57], 'cur_cost': 53784.0, 'intermediate_solutions': [{'tour': [44, 18, 29, 14, 19, 9, 10, 62, 48, 7, 64, 0, 3, 1, 61, 53, 63, 56, 57, 39, 17, 20, 25, 22, 34, 12, 15, 21, 33, 5, 23, 35, 4, 11, 16, 43, 27, 40, 51, 47, 50, 46, 55, 30, 31, 24, 37, 2, 6, 13, 49, 45, 36, 38, 32, 26, 42, 28, 41, 8, 59, 58, 54, 60, 65, 52], 'cur_cost': 69151.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [44, 18, 29, 14, 19, 9, 10, 62, 55, 7, 64, 0, 3, 1, 61, 53, 63, 56, 57, 39, 17, 20, 25, 22, 34, 12, 15, 21, 33, 5, 23, 35, 4, 11, 16, 43, 27, 40, 51, 47, 50, 46, 58, 59, 8, 41, 28, 42, 26, 32, 38, 36, 45, 49, 13, 6, 2, 37, 24, 31, 30, 48, 54, 60, 65, 52], 'cur_cost': 66817.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [44, 18, 29, 19, 9, 10, 62, 55, 7, 64, 0, 3, 14, 1, 61, 53, 63, 56, 57, 39, 17, 20, 25, 22, 34, 12, 15, 21, 33, 5, 23, 35, 4, 11, 16, 43, 27, 40, 51, 47, 50, 46, 48, 30, 31, 24, 37, 2, 6, 13, 49, 45, 36, 38, 32, 26, 42, 28, 41, 8, 59, 58, 54, 60, 65, 52], 'cur_cost': 63541.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [17, 15, 6, 27, 26, 4, 9, 54, 20, 3, 13, 55, 8, 62, 7, 61, 0, 58, 60, 21, 37, 14, 23, 5, 63, 49, 39, 50, 46, 19, 40, 12, 30, 16, 25, 41, 32, 31, 10, 53, 1, 51, 38, 48, 18, 33, 57, 44, 64, 42, 59, 29, 22, 52, 34, 56, 2, 28, 45, 43, 65, 11, 24, 47, 35, 36], 'cur_cost': 93020.0, 'intermediate_solutions': [{'tour': [0, 6, 22, 20, 21, 13, 25, 16, 18, 12, 17, 15, 14, 19, 27, 37, 23, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 17002.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 22, 20, 21, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 32, 29, 24, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12345.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 22, 20, 21, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 9, 40, 43, 48, 42], 'cur_cost': 17228.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([49, 31, 48, 54, 60, 13, 17, 51, 55, 32, 14, 65, 21, 56, 52, 46, 16,
       35, 64,  2,  3, 29, 61, 27,  9, 58, 42,  6, 15, 10, 19, 12, 38, 45,
       40, 34, 63, 44, 50, 53, 41, 25,  0, 23,  5, 30, 18,  1, 37, 39,  4,
       20, 28, 47, 43,  7, 24, 22, 33,  8, 62, 59, 11, 26, 36, 57],
      dtype=int64), 'cur_cost': 109100.0, 'intermediate_solutions': [{'tour': array([11, 51, 53, 10, 50, 18, 17, 19,  8,  3, 42, 28, 63, 32, 49, 35,  4,
       61, 31, 26, 21, 64, 30, 38, 29, 20, 48,  1, 40, 34, 15, 52, 65, 59,
       27, 60, 45, 23, 25, 24, 22, 43, 41, 58,  0, 62,  5, 57,  9, 37, 16,
       33,  6, 14,  7, 12, 13, 56, 44, 46, 36, 55,  2, 39, 47, 54]), 'cur_cost': 105168.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([10, 11, 51, 53, 50, 18, 17, 19,  8,  3, 42, 28, 63, 32, 49, 35,  4,
       61, 31, 26, 21, 64, 30, 38, 29, 20, 48,  1, 40, 34, 15, 52, 65, 59,
       27, 60, 45, 23, 25, 24, 22, 43, 41, 58,  0, 62,  5, 57,  9, 37, 16,
       33,  6, 14,  7, 12, 13, 56, 44, 46, 36, 55,  2, 39, 47, 54]), 'cur_cost': 104446.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([50, 10, 11, 51, 53, 18, 17, 19,  8,  3, 42, 28, 63, 32, 49, 35,  4,
       61, 31, 26, 21, 64, 30, 38, 29, 20, 48,  1, 40, 34, 15, 52, 65, 59,
       27, 60, 45, 23, 25, 24, 22, 43, 41, 58,  0, 62,  5, 57,  9, 37, 16,
       33,  6, 14,  7, 12, 13, 56, 44, 46, 36, 55,  2, 39, 47, 54]), 'cur_cost': 106505.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([53, 10, 11, 51, 50, 18, 17, 19,  8,  3, 42, 28, 63, 32, 49, 35,  4,
       61, 31, 26, 21, 64, 30, 38, 29, 20, 48,  1, 40, 34, 15, 52, 65, 59,
       27, 60, 45, 23, 25, 24, 22, 43, 41, 58,  0, 62,  5, 57,  9, 37, 16,
       33,  6, 14,  7, 12, 13, 56, 44, 46, 36, 55,  2, 39, 47, 54]), 'cur_cost': 98774.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([53, 50, 10, 11, 51, 18, 17, 19,  8,  3, 42, 28, 63, 32, 49, 35,  4,
       61, 31, 26, 21, 64, 30, 38, 29, 20, 48,  1, 40, 34, 15, 52, 65, 59,
       27, 60, 45, 23, 25, 24, 22, 43, 41, 58,  0, 62,  5, 57,  9, 37, 16,
       33,  6, 14,  7, 12, 13, 56, 44, 46, 36, 55,  2, 39, 47, 54]), 'cur_cost': 103184.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([39, 44, 48, 29, 19, 17, 34, 55, 57, 42, 45,  7,  4,  1, 64, 30,  8,
       49, 23, 50, 31, 63, 40, 11, 26, 12, 21, 53, 32, 27,  2, 25, 16, 35,
       15, 18, 24, 60, 58, 20, 33, 14, 36, 54, 47,  5, 10, 65, 13, 38, 37,
       52, 28, 59, 61,  6, 56, 51, 41, 62,  3,  9, 43, 46, 22,  0],
      dtype=int64), 'cur_cost': 100936.0, 'intermediate_solutions': [{'tour': array([ 1, 29,  7, 28, 13, 37, 46, 27, 43, 52, 11, 34, 40, 51,  9, 42, 45,
       60, 58, 15, 62, 39, 20,  2, 41, 25, 10,  5, 56, 50,  8, 65, 19, 18,
        6, 36, 47, 35, 32, 61, 48, 12, 33, 64, 57, 30, 22, 14, 53,  4, 54,
        3, 63,  0, 38, 21, 24, 44, 16, 49, 26, 59, 23, 17, 31, 55]), 'cur_cost': 108532.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([28,  1, 29,  7, 13, 37, 46, 27, 43, 52, 11, 34, 40, 51,  9, 42, 45,
       60, 58, 15, 62, 39, 20,  2, 41, 25, 10,  5, 56, 50,  8, 65, 19, 18,
        6, 36, 47, 35, 32, 61, 48, 12, 33, 64, 57, 30, 22, 14, 53,  4, 54,
        3, 63,  0, 38, 21, 24, 44, 16, 49, 26, 59, 23, 17, 31, 55]), 'cur_cost': 110491.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([13, 28,  1, 29,  7, 37, 46, 27, 43, 52, 11, 34, 40, 51,  9, 42, 45,
       60, 58, 15, 62, 39, 20,  2, 41, 25, 10,  5, 56, 50,  8, 65, 19, 18,
        6, 36, 47, 35, 32, 61, 48, 12, 33, 64, 57, 30, 22, 14, 53,  4, 54,
        3, 63,  0, 38, 21, 24, 44, 16, 49, 26, 59, 23, 17, 31, 55]), 'cur_cost': 110197.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 7, 28,  1, 29, 13, 37, 46, 27, 43, 52, 11, 34, 40, 51,  9, 42, 45,
       60, 58, 15, 62, 39, 20,  2, 41, 25, 10,  5, 56, 50,  8, 65, 19, 18,
        6, 36, 47, 35, 32, 61, 48, 12, 33, 64, 57, 30, 22, 14, 53,  4, 54,
        3, 63,  0, 38, 21, 24, 44, 16, 49, 26, 59, 23, 17, 31, 55]), 'cur_cost': 108591.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 7, 13, 28,  1, 29, 37, 46, 27, 43, 52, 11, 34, 40, 51,  9, 42, 45,
       60, 58, 15, 62, 39, 20,  2, 41, 25, 10,  5, 56, 50,  8, 65, 19, 18,
        6, 36, 47, 35, 32, 61, 48, 12, 33, 64, 57, 30, 22, 14, 53,  4, 54,
        3, 63,  0, 38, 21, 24, 44, 16, 49, 26, 59, 23, 17, 31, 55]), 'cur_cost': 106725.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 13, 7, 3, 9, 11, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12875.0, 'intermediate_solutions': [{'tour': [12, 24, 23, 34, 13, 27, 0, 14, 11, 53, 61, 65, 5, 52, 2, 46, 6, 21, 18, 4, 20, 32, 9, 62, 47, 49, 40, 44, 39, 15, 28, 26, 7, 8, 54, 58, 57, 56, 22, 35, 37, 10, 60, 48, 36, 33, 29, 43, 41, 45, 19, 1, 55, 16, 42, 30, 31, 3, 17, 25, 51, 38, 59, 64, 63, 50], 'cur_cost': 72887.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [30, 42, 46, 55, 1, 19, 45, 41, 43, 29, 33, 36, 48, 60, 10, 37, 35, 22, 56, 57, 58, 54, 8, 7, 26, 28, 15, 39, 44, 40, 49, 47, 62, 9, 32, 20, 4, 18, 21, 6, 16, 2, 52, 5, 65, 61, 53, 11, 14, 0, 27, 13, 34, 23, 24, 12, 31, 3, 17, 25, 51, 38, 59, 64, 63, 50], 'cur_cost': 71769.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [12, 24, 23, 34, 13, 27, 0, 14, 53, 61, 65, 5, 52, 2, 16, 6, 21, 18, 4, 20, 32, 9, 62, 47, 49, 40, 44, 39, 15, 28, 26, 7, 8, 54, 58, 57, 56, 22, 35, 37, 10, 60, 11, 48, 36, 33, 29, 43, 41, 45, 19, 1, 55, 46, 42, 30, 31, 3, 17, 25, 51, 38, 59, 64, 63, 50], 'cur_cost': 70346.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:13,889 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:13,889 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:13,897 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12726.000, 多样性=0.942
2025-08-05 09:52:13,898 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 09:52:13,898 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 09:52:13,899 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:13,899 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.004810984206318699, 'best_improvement': -0.03169841913254966}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.013751763046544288}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.0030758476552333984, 'recent_improvements': [-0.05666996560308821, 0.10184900382217661, -0.05051827029262141], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.9696969696969697, 'new_diversity': 0.9696969696969697, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:52:13,899 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 09:52:13,900 - __main__ - INFO - composite13_66 开始进化第 5 代
2025-08-05 09:52:13,900 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 09:52:13,900 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:13,902 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12726.000, 多样性=0.942
2025-08-05 09:52:13,902 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:13,906 - PathExpert - INFO - 路径结构分析完成: 公共边数量=4, 路径相似性=0.942
2025-08-05 09:52:13,907 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:13,909 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.970
2025-08-05 09:52:13,913 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 09:52:13,913 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:13,914 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-05 09:52:13,914 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-05 09:52:13,960 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.083, 适应度梯度: -9895.133, 聚类评分: 0.000, 覆盖率: 0.145, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:13,960 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 09:52:13,960 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:13,960 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite13_66
2025-08-05 09:52:13,969 - visualization.landscape_visualizer - INFO - 插值约束: 155 个点被约束到最小值 9521.00
2025-08-05 09:52:14,103 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite13_66\landscape_composite13_66_iter_125_20250805_095214.html
2025-08-05 09:52:14,171 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite13_66\dashboard_composite13_66_iter_125_20250805_095214.html
2025-08-05 09:52:14,171 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 125
2025-08-05 09:52:14,172 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 09:52:14,172 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2589秒
2025-08-05 09:52:14,172 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.08333333333333333, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -9895.13333333333, 'local_optima_density': 0.08333333333333333, 'gradient_variance': 1601951796.3622224, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1454, 'fitness_entropy': 0.8166663857315859, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -9895.133)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.145)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358733.960438, 'performance_metrics': {}}}
2025-08-05 09:52:14,172 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:14,172 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:14,173 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:14,173 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:14,173 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 09:52:14,173 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 09:52:14,174 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 09:52:14,174 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:14,174 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:14,174 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 09:52:14,174 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:14,174 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:14,175 - experts.management.collaboration_manager - INFO - 识别精英个体: {2, 4} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:14,175 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:14,175 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:14,175 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:14,181 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-05 09:52:14,181 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,182 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,182 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,182 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,182 - ExplorationExpert - INFO - 探索路径生成完成，成本: 50982.0, 路径长度: 66, 收集中间解: 3
2025-08-05 09:52:14,182 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [57, 53, 54, 64, 11, 7, 2, 8, 9, 22, 6, 23, 34, 26, 30, 24, 37, 16, 18, 20, 33, 40, 21, 19, 29, 36, 25, 28, 0, 14, 39, 51, 47, 48, 49, 38, 42, 17, 1, 52, 58, 55, 3, 56, 59, 4, 63, 10, 12, 27, 46, 13, 31, 45, 43, 15, 41, 32, 35, 5, 65, 61, 60, 62, 44, 50], 'cur_cost': 50982.0, 'intermediate_solutions': [{'tour': [41, 20, 28, 29, 49, 22, 26, 11, 65, 54, 56, 63, 9, 52, 4, 8, 53, 7, 61, 0, 1, 62, 21, 37, 6, 23, 5, 57, 27, 39, 44, 50, 46, 14, 3, 55, 40, 12, 30, 13, 24, 25, 2, 17, 32, 34, 31, 10, 60, 47, 51, 38, 43, 18, 19, 33, 16, 15, 36, 35, 48, 42, 58, 59, 64, 45], 'cur_cost': 67976.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [41, 20, 28, 29, 27, 22, 26, 11, 65, 54, 56, 63, 9, 52, 4, 8, 53, 7, 61, 0, 1, 62, 21, 37, 6, 23, 39, 49, 57, 5, 44, 50, 46, 14, 3, 55, 40, 12, 30, 13, 24, 25, 2, 17, 32, 34, 31, 10, 60, 47, 51, 38, 43, 18, 19, 33, 16, 15, 36, 35, 48, 42, 58, 59, 64, 45], 'cur_cost': 66159.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [41, 20, 28, 29, 27, 22, 26, 11, 65, 54, 56, 63, 9, 52, 4, 8, 53, 7, 61, 0, 1, 62, 21, 37, 6, 23, 5, 57, 49, 39, 44, 50, 46, 14, 3, 55, 12, 30, 13, 24, 25, 2, 17, 32, 34, 31, 10, 60, 47, 51, 38, 43, 18, 19, 33, 16, 15, 36, 35, 48, 42, 58, 59, 64, 40, 45], 'cur_cost': 61326.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:14,183 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 50982.00)
2025-08-05 09:52:14,183 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:14,183 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:14,183 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:14,185 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 09:52:14,185 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,186 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,186 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,186 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,186 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12769.0, 路径长度: 66, 收集中间解: 3
2025-08-05 09:52:14,186 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12769.0, 'intermediate_solutions': [{'tour': [0, 18, 36, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 6, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 18472.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 6, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 48, 43, 40, 42], 'cur_cost': 14709.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 6, 16, 23, 22, 12, 17, 15, 14, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 20, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 18655.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:14,187 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 12769.00)
2025-08-05 09:52:14,187 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:14,187 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:14,187 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:14,189 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 09:52:14,189 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,189 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,190 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,190 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,190 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10393.0, 路径长度: 66, 收集中间解: 3
2025-08-05 09:52:14,190 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 8, 4, 7, 3, 9, 11, 1, 5, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10393.0, 'intermediate_solutions': [{'tour': [0, 17, 15, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 44, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 10, 45, 38, 51, 50, 41, 42], 'cur_cost': 22062.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 15, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37, 27, 21, 20, 13, 19, 18, 16, 23, 12, 22, 14, 63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 18471.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 15, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 23, 16, 18, 19, 13, 30, 20, 21, 27, 37, 25, 26, 36, 35, 28, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 15180.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:14,190 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 10393.00)
2025-08-05 09:52:14,191 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:52:14,191 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:14,191 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:14,191 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 92166.0
2025-08-05 09:52:14,202 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 09:52:14,202 - ExploitationExpert - INFO - res_population_costs: [9521, 9534.0]
2025-08-05 09:52:14,202 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64)]
2025-08-05 09:52:14,203 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:14,203 - ExploitationExpert - INFO - populations: [{'tour': [57, 53, 54, 64, 11, 7, 2, 8, 9, 22, 6, 23, 34, 26, 30, 24, 37, 16, 18, 20, 33, 40, 21, 19, 29, 36, 25, 28, 0, 14, 39, 51, 47, 48, 49, 38, 42, 17, 1, 52, 58, 55, 3, 56, 59, 4, 63, 10, 12, 27, 46, 13, 31, 45, 43, 15, 41, 32, 35, 5, 65, 61, 60, 62, 44, 50], 'cur_cost': 50982.0}, {'tour': [0, 12, 8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12769.0}, {'tour': [0, 8, 4, 7, 3, 9, 11, 1, 5, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10393.0}, {'tour': array([34, 30, 41, 56, 17, 44, 21, 63, 60, 54, 50, 18, 35, 65, 25, 48, 39,
       45, 37, 46, 29, 16, 36, 19, 24,  7,  9, 14, 10, 61, 20, 11,  6, 64,
       53, 57, 22, 28,  8,  5,  0,  1, 42,  2, 15, 47, 49, 59, 52, 62, 27,
       26, 13,  4, 33, 38, 58, 43, 32, 51, 23, 12, 55, 40,  3, 31],
      dtype=int64), 'cur_cost': 92166.0}, {'tour': [0, 20, 6, 9, 11, 7, 3, 1, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12842.0}, {'tour': [60, 62, 23, 37, 26, 22, 3, 1, 65, 61, 17, 12, 29, 18, 15, 11, 54, 8, 63, 53, 59, 55, 7, 10, 14, 28, 0, 24, 27, 6, 64, 52, 58, 9, 13, 19, 35, 4, 34, 31, 16, 21, 2, 33, 49, 40, 43, 50, 41, 45, 51, 39, 46, 20, 32, 30, 36, 48, 42, 38, 25, 47, 44, 5, 56, 57], 'cur_cost': 53784.0}, {'tour': [17, 15, 6, 27, 26, 4, 9, 54, 20, 3, 13, 55, 8, 62, 7, 61, 0, 58, 60, 21, 37, 14, 23, 5, 63, 49, 39, 50, 46, 19, 40, 12, 30, 16, 25, 41, 32, 31, 10, 53, 1, 51, 38, 48, 18, 33, 57, 44, 64, 42, 59, 29, 22, 52, 34, 56, 2, 28, 45, 43, 65, 11, 24, 47, 35, 36], 'cur_cost': 93020.0}, {'tour': [49, 31, 48, 54, 60, 13, 17, 51, 55, 32, 14, 65, 21, 56, 52, 46, 16, 35, 64, 2, 3, 29, 61, 27, 9, 58, 42, 6, 15, 10, 19, 12, 38, 45, 40, 34, 63, 44, 50, 53, 41, 25, 0, 23, 5, 30, 18, 1, 37, 39, 4, 20, 28, 47, 43, 7, 24, 22, 33, 8, 62, 59, 11, 26, 36, 57], 'cur_cost': 109100.0}, {'tour': [39, 44, 48, 29, 19, 17, 34, 55, 57, 42, 45, 7, 4, 1, 64, 30, 8, 49, 23, 50, 31, 63, 40, 11, 26, 12, 21, 53, 32, 27, 2, 25, 16, 35, 15, 18, 24, 60, 58, 20, 33, 14, 36, 54, 47, 5, 10, 65, 13, 38, 37, 52, 28, 59, 61, 6, 56, 51, 41, 62, 3, 9, 43, 46, 22, 0], 'cur_cost': 100936.0}, {'tour': [0, 8, 13, 7, 3, 9, 11, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12875.0}]
2025-08-05 09:52:14,204 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:14,204 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 323, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 323, 'cache_hits': 0, 'similarity_calculations': 1645, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:14,205 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([34, 30, 41, 56, 17, 44, 21, 63, 60, 54, 50, 18, 35, 65, 25, 48, 39,
       45, 37, 46, 29, 16, 36, 19, 24,  7,  9, 14, 10, 61, 20, 11,  6, 64,
       53, 57, 22, 28,  8,  5,  0,  1, 42,  2, 15, 47, 49, 59, 52, 62, 27,
       26, 13,  4, 33, 38, 58, 43, 32, 51, 23, 12, 55, 40,  3, 31],
      dtype=int64), 'cur_cost': 92166.0, 'intermediate_solutions': [{'tour': array([42,  8, 43, 63,  1, 24, 31, 38, 15, 37, 57, 22, 49, 27, 65, 53, 17,
       25, 64, 60,  3,  5,  4, 48, 18,  6, 54, 30, 33, 16, 36, 10, 44, 56,
        9, 45, 21, 29, 19, 34, 26, 14, 50, 47,  7, 35, 13, 52, 12, 58, 55,
       59, 41, 40,  2,  0, 51, 28, 11, 23, 39, 32, 20, 61, 62, 46]), 'cur_cost': 99227.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([63, 42,  8, 43,  1, 24, 31, 38, 15, 37, 57, 22, 49, 27, 65, 53, 17,
       25, 64, 60,  3,  5,  4, 48, 18,  6, 54, 30, 33, 16, 36, 10, 44, 56,
        9, 45, 21, 29, 19, 34, 26, 14, 50, 47,  7, 35, 13, 52, 12, 58, 55,
       59, 41, 40,  2,  0, 51, 28, 11, 23, 39, 32, 20, 61, 62, 46]), 'cur_cost': 103532.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1, 63, 42,  8, 43, 24, 31, 38, 15, 37, 57, 22, 49, 27, 65, 53, 17,
       25, 64, 60,  3,  5,  4, 48, 18,  6, 54, 30, 33, 16, 36, 10, 44, 56,
        9, 45, 21, 29, 19, 34, 26, 14, 50, 47,  7, 35, 13, 52, 12, 58, 55,
       59, 41, 40,  2,  0, 51, 28, 11, 23, 39, 32, 20, 61, 62, 46]), 'cur_cost': 101904.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([43, 63, 42,  8,  1, 24, 31, 38, 15, 37, 57, 22, 49, 27, 65, 53, 17,
       25, 64, 60,  3,  5,  4, 48, 18,  6, 54, 30, 33, 16, 36, 10, 44, 56,
        9, 45, 21, 29, 19, 34, 26, 14, 50, 47,  7, 35, 13, 52, 12, 58, 55,
       59, 41, 40,  2,  0, 51, 28, 11, 23, 39, 32, 20, 61, 62, 46]), 'cur_cost': 98632.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([43,  1, 63, 42,  8, 24, 31, 38, 15, 37, 57, 22, 49, 27, 65, 53, 17,
       25, 64, 60,  3,  5,  4, 48, 18,  6, 54, 30, 33, 16, 36, 10, 44, 56,
        9, 45, 21, 29, 19, 34, 26, 14, 50, 47,  7, 35, 13, 52, 12, 58, 55,
       59, 41, 40,  2,  0, 51, 28, 11, 23, 39, 32, 20, 61, 62, 46]), 'cur_cost': 99312.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:14,205 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 92166.00)
2025-08-05 09:52:14,205 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:14,205 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:14,205 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:14,212 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-05 09:52:14,212 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,212 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,213 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,213 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,213 - ExplorationExpert - INFO - 探索路径生成完成，成本: 68778.0, 路径长度: 66, 收集中间解: 3
2025-08-05 09:52:14,213 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [22, 35, 0, 14, 7, 53, 20, 28, 6, 61, 58, 63, 55, 9, 56, 4, 16, 18, 33, 11, 13, 5, 27, 34, 31, 12, 1, 15, 10, 19, 26, 3, 23, 30, 2, 17, 43, 40, 44, 39, 21, 24, 49, 38, 46, 42, 48, 47, 51, 29, 36, 41, 32, 8, 64, 54, 60, 57, 52, 59, 62, 45, 37, 25, 50, 65], 'cur_cost': 68778.0, 'intermediate_solutions': [{'tour': [0, 20, 6, 9, 11, 7, 3, 1, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 32, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 58, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 22282.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 6, 9, 11, 7, 3, 1, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 51, 38, 45, 44, 50, 41, 42], 'cur_cost': 12884.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 6, 9, 11, 7, 3, 1, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 35, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 15180.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:14,214 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 68778.00)
2025-08-05 09:52:14,214 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:14,214 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:14,214 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:14,216 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-05 09:52:14,216 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,216 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,217 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,217 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,217 - ExplorationExpert - INFO - 探索路径生成完成，成本: 81665.0, 路径长度: 66, 收集中间解: 3
2025-08-05 09:52:14,217 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [57, 53, 4, 2, 6, 9, 7, 3, 1, 34, 26, 30, 24, 59, 16, 58, 20, 33, 40, 54, 10, 52, 36, 25, 28, 0, 14, 39, 51, 47, 17, 49, 38, 42, 31, 41, 55, 62, 56, 64, 63, 50, 12, 27, 46, 13, 32, 45, 61, 23, 48, 22, 35, 43, 65, 11, 37, 19, 18, 29, 60, 44, 21, 5, 15, 8], 'cur_cost': 81665.0, 'intermediate_solutions': [{'tour': [60, 62, 23, 37, 26, 22, 3, 19, 65, 61, 17, 12, 29, 18, 15, 11, 54, 8, 63, 53, 59, 55, 7, 10, 14, 28, 0, 24, 27, 6, 64, 52, 58, 9, 13, 1, 35, 4, 34, 31, 16, 21, 2, 33, 49, 40, 43, 50, 41, 45, 51, 39, 46, 20, 32, 30, 36, 48, 42, 38, 25, 47, 44, 5, 56, 57], 'cur_cost': 58031.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [60, 62, 23, 37, 26, 22, 3, 1, 65, 61, 17, 12, 29, 24, 0, 28, 14, 10, 7, 55, 59, 53, 63, 8, 54, 11, 15, 18, 27, 6, 64, 52, 58, 9, 13, 19, 35, 4, 34, 31, 16, 21, 2, 33, 49, 40, 43, 50, 41, 45, 51, 39, 46, 20, 32, 30, 36, 48, 42, 38, 25, 47, 44, 5, 56, 57], 'cur_cost': 53683.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [60, 62, 23, 37, 26, 22, 3, 1, 65, 61, 17, 12, 18, 15, 11, 54, 8, 63, 53, 59, 55, 7, 10, 14, 28, 0, 24, 27, 6, 64, 52, 58, 9, 13, 19, 35, 4, 34, 31, 16, 21, 2, 33, 49, 40, 43, 50, 41, 45, 51, 39, 46, 29, 20, 32, 30, 36, 48, 42, 38, 25, 47, 44, 5, 56, 57], 'cur_cost': 53449.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:14,217 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 81665.00)
2025-08-05 09:52:14,218 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:14,218 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:14,218 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:14,220 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 09:52:14,220 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,220 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,220 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,220 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,221 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12909.0, 路径长度: 66, 收集中间解: 3
2025-08-05 09:52:14,221 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 13, 21, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12909.0, 'intermediate_solutions': [{'tour': [17, 15, 6, 27, 26, 4, 9, 54, 20, 3, 13, 55, 8, 62, 7, 61, 0, 58, 60, 21, 37, 14, 23, 5, 63, 49, 39, 50, 46, 19, 40, 12, 30, 16, 25, 41, 32, 31, 10, 53, 1, 51, 18, 48, 38, 33, 57, 44, 64, 42, 59, 29, 22, 52, 34, 56, 2, 28, 45, 43, 65, 11, 24, 47, 35, 36], 'cur_cost': 95449.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [17, 15, 6, 27, 26, 4, 9, 54, 20, 3, 13, 55, 8, 62, 7, 61, 0, 58, 60, 21, 37, 14, 23, 5, 63, 49, 39, 50, 46, 19, 40, 12, 30, 16, 25, 41, 32, 31, 10, 53, 1, 51, 38, 48, 18, 64, 44, 57, 33, 42, 59, 29, 22, 52, 34, 56, 2, 28, 45, 43, 65, 11, 24, 47, 35, 36], 'cur_cost': 93059.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [17, 15, 6, 27, 26, 4, 9, 54, 20, 3, 13, 55, 8, 62, 7, 61, 0, 58, 60, 21, 37, 14, 23, 5, 63, 49, 39, 50, 46, 19, 40, 12, 30, 16, 25, 41, 32, 31, 10, 53, 1, 51, 38, 48, 24, 18, 33, 57, 44, 64, 42, 59, 29, 22, 52, 34, 56, 2, 28, 45, 43, 65, 11, 47, 35, 36], 'cur_cost': 93330.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:14,221 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 12909.00)
2025-08-05 09:52:14,221 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 09:52:14,221 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:14,221 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:14,222 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 93385.0
2025-08-05 09:52:14,231 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 09:52:14,231 - ExploitationExpert - INFO - res_population_costs: [9521, 9534.0]
2025-08-05 09:52:14,231 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64)]
2025-08-05 09:52:14,232 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:14,232 - ExploitationExpert - INFO - populations: [{'tour': [57, 53, 54, 64, 11, 7, 2, 8, 9, 22, 6, 23, 34, 26, 30, 24, 37, 16, 18, 20, 33, 40, 21, 19, 29, 36, 25, 28, 0, 14, 39, 51, 47, 48, 49, 38, 42, 17, 1, 52, 58, 55, 3, 56, 59, 4, 63, 10, 12, 27, 46, 13, 31, 45, 43, 15, 41, 32, 35, 5, 65, 61, 60, 62, 44, 50], 'cur_cost': 50982.0}, {'tour': [0, 12, 8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12769.0}, {'tour': [0, 8, 4, 7, 3, 9, 11, 1, 5, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10393.0}, {'tour': array([34, 30, 41, 56, 17, 44, 21, 63, 60, 54, 50, 18, 35, 65, 25, 48, 39,
       45, 37, 46, 29, 16, 36, 19, 24,  7,  9, 14, 10, 61, 20, 11,  6, 64,
       53, 57, 22, 28,  8,  5,  0,  1, 42,  2, 15, 47, 49, 59, 52, 62, 27,
       26, 13,  4, 33, 38, 58, 43, 32, 51, 23, 12, 55, 40,  3, 31],
      dtype=int64), 'cur_cost': 92166.0}, {'tour': [22, 35, 0, 14, 7, 53, 20, 28, 6, 61, 58, 63, 55, 9, 56, 4, 16, 18, 33, 11, 13, 5, 27, 34, 31, 12, 1, 15, 10, 19, 26, 3, 23, 30, 2, 17, 43, 40, 44, 39, 21, 24, 49, 38, 46, 42, 48, 47, 51, 29, 36, 41, 32, 8, 64, 54, 60, 57, 52, 59, 62, 45, 37, 25, 50, 65], 'cur_cost': 68778.0}, {'tour': [57, 53, 4, 2, 6, 9, 7, 3, 1, 34, 26, 30, 24, 59, 16, 58, 20, 33, 40, 54, 10, 52, 36, 25, 28, 0, 14, 39, 51, 47, 17, 49, 38, 42, 31, 41, 55, 62, 56, 64, 63, 50, 12, 27, 46, 13, 32, 45, 61, 23, 48, 22, 35, 43, 65, 11, 37, 19, 18, 29, 60, 44, 21, 5, 15, 8], 'cur_cost': 81665.0}, {'tour': [0, 13, 21, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12909.0}, {'tour': array([24, 21, 12, 63, 29, 65, 22, 15, 48, 20, 54, 14, 11,  7, 19,  8, 62,
       18, 17, 50, 10, 30, 25, 44, 53, 60,  9, 41,  4,  0,  1, 56, 43, 45,
       34,  6, 47, 33,  5, 13, 46, 26, 40, 39, 23, 55, 64, 32, 27, 37, 42,
       59,  2, 58, 52, 57, 16, 61, 35, 31, 28, 38,  3, 51, 49, 36],
      dtype=int64), 'cur_cost': 93385.0}, {'tour': [39, 44, 48, 29, 19, 17, 34, 55, 57, 42, 45, 7, 4, 1, 64, 30, 8, 49, 23, 50, 31, 63, 40, 11, 26, 12, 21, 53, 32, 27, 2, 25, 16, 35, 15, 18, 24, 60, 58, 20, 33, 14, 36, 54, 47, 5, 10, 65, 13, 38, 37, 52, 28, 59, 61, 6, 56, 51, 41, 62, 3, 9, 43, 46, 22, 0], 'cur_cost': 100936.0}, {'tour': [0, 8, 13, 7, 3, 9, 11, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12875.0}]
2025-08-05 09:52:14,233 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:14,233 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 324, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 324, 'cache_hits': 0, 'similarity_calculations': 1656, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:14,234 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([24, 21, 12, 63, 29, 65, 22, 15, 48, 20, 54, 14, 11,  7, 19,  8, 62,
       18, 17, 50, 10, 30, 25, 44, 53, 60,  9, 41,  4,  0,  1, 56, 43, 45,
       34,  6, 47, 33,  5, 13, 46, 26, 40, 39, 23, 55, 64, 32, 27, 37, 42,
       59,  2, 58, 52, 57, 16, 61, 35, 31, 28, 38,  3, 51, 49, 36],
      dtype=int64), 'cur_cost': 93385.0, 'intermediate_solutions': [{'tour': array([48, 31, 49, 54, 60, 13, 17, 51, 55, 32, 14, 65, 21, 56, 52, 46, 16,
       35, 64,  2,  3, 29, 61, 27,  9, 58, 42,  6, 15, 10, 19, 12, 38, 45,
       40, 34, 63, 44, 50, 53, 41, 25,  0, 23,  5, 30, 18,  1, 37, 39,  4,
       20, 28, 47, 43,  7, 24, 22, 33,  8, 62, 59, 11, 26, 36, 57]), 'cur_cost': 109100.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([54, 48, 31, 49, 60, 13, 17, 51, 55, 32, 14, 65, 21, 56, 52, 46, 16,
       35, 64,  2,  3, 29, 61, 27,  9, 58, 42,  6, 15, 10, 19, 12, 38, 45,
       40, 34, 63, 44, 50, 53, 41, 25,  0, 23,  5, 30, 18,  1, 37, 39,  4,
       20, 28, 47, 43,  7, 24, 22, 33,  8, 62, 59, 11, 26, 36, 57]), 'cur_cost': 109027.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([60, 54, 48, 31, 49, 13, 17, 51, 55, 32, 14, 65, 21, 56, 52, 46, 16,
       35, 64,  2,  3, 29, 61, 27,  9, 58, 42,  6, 15, 10, 19, 12, 38, 45,
       40, 34, 63, 44, 50, 53, 41, 25,  0, 23,  5, 30, 18,  1, 37, 39,  4,
       20, 28, 47, 43,  7, 24, 22, 33,  8, 62, 59, 11, 26, 36, 57]), 'cur_cost': 105703.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([49, 54, 48, 31, 60, 13, 17, 51, 55, 32, 14, 65, 21, 56, 52, 46, 16,
       35, 64,  2,  3, 29, 61, 27,  9, 58, 42,  6, 15, 10, 19, 12, 38, 45,
       40, 34, 63, 44, 50, 53, 41, 25,  0, 23,  5, 30, 18,  1, 37, 39,  4,
       20, 28, 47, 43,  7, 24, 22, 33,  8, 62, 59, 11, 26, 36, 57]), 'cur_cost': 112716.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([49, 60, 54, 48, 31, 13, 17, 51, 55, 32, 14, 65, 21, 56, 52, 46, 16,
       35, 64,  2,  3, 29, 61, 27,  9, 58, 42,  6, 15, 10, 19, 12, 38, 45,
       40, 34, 63, 44, 50, 53, 41, 25,  0, 23,  5, 30, 18,  1, 37, 39,  4,
       20, 28, 47, 43,  7, 24, 22, 33,  8, 62, 59, 11, 26, 36, 57]), 'cur_cost': 109036.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:14,234 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 93385.00)
2025-08-05 09:52:14,234 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:52:14,234 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:14,235 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:14,235 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 108330.0
2025-08-05 09:52:14,247 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 09:52:14,248 - ExploitationExpert - INFO - res_population_costs: [9521, 9534.0]
2025-08-05 09:52:14,248 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64)]
2025-08-05 09:52:14,249 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:14,249 - ExploitationExpert - INFO - populations: [{'tour': [57, 53, 54, 64, 11, 7, 2, 8, 9, 22, 6, 23, 34, 26, 30, 24, 37, 16, 18, 20, 33, 40, 21, 19, 29, 36, 25, 28, 0, 14, 39, 51, 47, 48, 49, 38, 42, 17, 1, 52, 58, 55, 3, 56, 59, 4, 63, 10, 12, 27, 46, 13, 31, 45, 43, 15, 41, 32, 35, 5, 65, 61, 60, 62, 44, 50], 'cur_cost': 50982.0}, {'tour': [0, 12, 8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12769.0}, {'tour': [0, 8, 4, 7, 3, 9, 11, 1, 5, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10393.0}, {'tour': array([34, 30, 41, 56, 17, 44, 21, 63, 60, 54, 50, 18, 35, 65, 25, 48, 39,
       45, 37, 46, 29, 16, 36, 19, 24,  7,  9, 14, 10, 61, 20, 11,  6, 64,
       53, 57, 22, 28,  8,  5,  0,  1, 42,  2, 15, 47, 49, 59, 52, 62, 27,
       26, 13,  4, 33, 38, 58, 43, 32, 51, 23, 12, 55, 40,  3, 31],
      dtype=int64), 'cur_cost': 92166.0}, {'tour': [22, 35, 0, 14, 7, 53, 20, 28, 6, 61, 58, 63, 55, 9, 56, 4, 16, 18, 33, 11, 13, 5, 27, 34, 31, 12, 1, 15, 10, 19, 26, 3, 23, 30, 2, 17, 43, 40, 44, 39, 21, 24, 49, 38, 46, 42, 48, 47, 51, 29, 36, 41, 32, 8, 64, 54, 60, 57, 52, 59, 62, 45, 37, 25, 50, 65], 'cur_cost': 68778.0}, {'tour': [57, 53, 4, 2, 6, 9, 7, 3, 1, 34, 26, 30, 24, 59, 16, 58, 20, 33, 40, 54, 10, 52, 36, 25, 28, 0, 14, 39, 51, 47, 17, 49, 38, 42, 31, 41, 55, 62, 56, 64, 63, 50, 12, 27, 46, 13, 32, 45, 61, 23, 48, 22, 35, 43, 65, 11, 37, 19, 18, 29, 60, 44, 21, 5, 15, 8], 'cur_cost': 81665.0}, {'tour': [0, 13, 21, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12909.0}, {'tour': array([24, 21, 12, 63, 29, 65, 22, 15, 48, 20, 54, 14, 11,  7, 19,  8, 62,
       18, 17, 50, 10, 30, 25, 44, 53, 60,  9, 41,  4,  0,  1, 56, 43, 45,
       34,  6, 47, 33,  5, 13, 46, 26, 40, 39, 23, 55, 64, 32, 27, 37, 42,
       59,  2, 58, 52, 57, 16, 61, 35, 31, 28, 38,  3, 51, 49, 36],
      dtype=int64), 'cur_cost': 93385.0}, {'tour': array([64, 11, 33, 31, 47, 58, 18, 12,  0, 51, 38, 21, 28, 22, 20, 39, 25,
       17, 37, 52, 43, 41,  6, 46, 32, 42, 36,  9, 63, 23, 40, 29, 45, 24,
       50,  5, 15, 60, 35, 61, 53, 27, 55,  2, 44,  3, 57,  8, 19, 34, 62,
        7, 65, 13, 49, 30,  1, 48,  4, 59, 10, 26, 16, 14, 56, 54],
      dtype=int64), 'cur_cost': 108330.0}, {'tour': [0, 8, 13, 7, 3, 9, 11, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12875.0}]
2025-08-05 09:52:14,251 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:14,251 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 325, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 325, 'cache_hits': 0, 'similarity_calculations': 1668, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:14,252 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([64, 11, 33, 31, 47, 58, 18, 12,  0, 51, 38, 21, 28, 22, 20, 39, 25,
       17, 37, 52, 43, 41,  6, 46, 32, 42, 36,  9, 63, 23, 40, 29, 45, 24,
       50,  5, 15, 60, 35, 61, 53, 27, 55,  2, 44,  3, 57,  8, 19, 34, 62,
        7, 65, 13, 49, 30,  1, 48,  4, 59, 10, 26, 16, 14, 56, 54],
      dtype=int64), 'cur_cost': 108330.0, 'intermediate_solutions': [{'tour': array([48, 44, 39, 29, 19, 17, 34, 55, 57, 42, 45,  7,  4,  1, 64, 30,  8,
       49, 23, 50, 31, 63, 40, 11, 26, 12, 21, 53, 32, 27,  2, 25, 16, 35,
       15, 18, 24, 60, 58, 20, 33, 14, 36, 54, 47,  5, 10, 65, 13, 38, 37,
       52, 28, 59, 61,  6, 56, 51, 41, 62,  3,  9, 43, 46, 22,  0]), 'cur_cost': 100969.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([29, 48, 44, 39, 19, 17, 34, 55, 57, 42, 45,  7,  4,  1, 64, 30,  8,
       49, 23, 50, 31, 63, 40, 11, 26, 12, 21, 53, 32, 27,  2, 25, 16, 35,
       15, 18, 24, 60, 58, 20, 33, 14, 36, 54, 47,  5, 10, 65, 13, 38, 37,
       52, 28, 59, 61,  6, 56, 51, 41, 62,  3,  9, 43, 46, 22,  0]), 'cur_cost': 100571.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([19, 29, 48, 44, 39, 17, 34, 55, 57, 42, 45,  7,  4,  1, 64, 30,  8,
       49, 23, 50, 31, 63, 40, 11, 26, 12, 21, 53, 32, 27,  2, 25, 16, 35,
       15, 18, 24, 60, 58, 20, 33, 14, 36, 54, 47,  5, 10, 65, 13, 38, 37,
       52, 28, 59, 61,  6, 56, 51, 41, 62,  3,  9, 43, 46, 22,  0]), 'cur_cost': 101083.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([39, 29, 48, 44, 19, 17, 34, 55, 57, 42, 45,  7,  4,  1, 64, 30,  8,
       49, 23, 50, 31, 63, 40, 11, 26, 12, 21, 53, 32, 27,  2, 25, 16, 35,
       15, 18, 24, 60, 58, 20, 33, 14, 36, 54, 47,  5, 10, 65, 13, 38, 37,
       52, 28, 59, 61,  6, 56, 51, 41, 62,  3,  9, 43, 46, 22,  0]), 'cur_cost': 103252.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([39, 19, 29, 48, 44, 17, 34, 55, 57, 42, 45,  7,  4,  1, 64, 30,  8,
       49, 23, 50, 31, 63, 40, 11, 26, 12, 21, 53, 32, 27,  2, 25, 16, 35,
       15, 18, 24, 60, 58, 20, 33, 14, 36, 54, 47,  5, 10, 65, 13, 38, 37,
       52, 28, 59, 61,  6, 56, 51, 41, 62,  3,  9, 43, 46, 22,  0]), 'cur_cost': 103649.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:14,252 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 108330.00)
2025-08-05 09:52:14,252 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:14,252 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:14,252 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:14,255 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 09:52:14,255 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,255 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,256 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,256 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,256 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14862.0, 路径长度: 66, 收集中间解: 3
2025-08-05 09:52:14,256 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 13, 3, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14862.0, 'intermediate_solutions': [{'tour': [0, 8, 13, 7, 3, 9, 11, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 47, 29, 32, 40, 49, 24, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 21281.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 28, 35, 36, 26, 25, 37, 27, 20, 21, 19, 23, 16, 18, 17, 12, 22, 15, 14, 63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10, 2, 6, 4, 5, 1, 11, 9, 3, 7, 13, 8, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 16869.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 13, 7, 3, 9, 11, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 51, 49, 47, 46, 48, 43, 39, 44, 45, 38, 50, 41, 42], 'cur_cost': 13019.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:14,257 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 14862.00)
2025-08-05 09:52:14,257 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:14,257 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:14,259 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [57, 53, 54, 64, 11, 7, 2, 8, 9, 22, 6, 23, 34, 26, 30, 24, 37, 16, 18, 20, 33, 40, 21, 19, 29, 36, 25, 28, 0, 14, 39, 51, 47, 48, 49, 38, 42, 17, 1, 52, 58, 55, 3, 56, 59, 4, 63, 10, 12, 27, 46, 13, 31, 45, 43, 15, 41, 32, 35, 5, 65, 61, 60, 62, 44, 50], 'cur_cost': 50982.0, 'intermediate_solutions': [{'tour': [41, 20, 28, 29, 49, 22, 26, 11, 65, 54, 56, 63, 9, 52, 4, 8, 53, 7, 61, 0, 1, 62, 21, 37, 6, 23, 5, 57, 27, 39, 44, 50, 46, 14, 3, 55, 40, 12, 30, 13, 24, 25, 2, 17, 32, 34, 31, 10, 60, 47, 51, 38, 43, 18, 19, 33, 16, 15, 36, 35, 48, 42, 58, 59, 64, 45], 'cur_cost': 67976.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [41, 20, 28, 29, 27, 22, 26, 11, 65, 54, 56, 63, 9, 52, 4, 8, 53, 7, 61, 0, 1, 62, 21, 37, 6, 23, 39, 49, 57, 5, 44, 50, 46, 14, 3, 55, 40, 12, 30, 13, 24, 25, 2, 17, 32, 34, 31, 10, 60, 47, 51, 38, 43, 18, 19, 33, 16, 15, 36, 35, 48, 42, 58, 59, 64, 45], 'cur_cost': 66159.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [41, 20, 28, 29, 27, 22, 26, 11, 65, 54, 56, 63, 9, 52, 4, 8, 53, 7, 61, 0, 1, 62, 21, 37, 6, 23, 5, 57, 49, 39, 44, 50, 46, 14, 3, 55, 12, 30, 13, 24, 25, 2, 17, 32, 34, 31, 10, 60, 47, 51, 38, 43, 18, 19, 33, 16, 15, 36, 35, 48, 42, 58, 59, 64, 40, 45], 'cur_cost': 61326.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12769.0, 'intermediate_solutions': [{'tour': [0, 18, 36, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 6, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 18472.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 6, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 48, 43, 40, 42], 'cur_cost': 14709.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 6, 16, 23, 22, 12, 17, 15, 14, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 20, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 18655.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 4, 7, 3, 9, 11, 1, 5, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10393.0, 'intermediate_solutions': [{'tour': [0, 17, 15, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 44, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 10, 45, 38, 51, 50, 41, 42], 'cur_cost': 22062.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 15, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37, 27, 21, 20, 13, 19, 18, 16, 23, 12, 22, 14, 63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 18471.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 15, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 23, 16, 18, 19, 13, 30, 20, 21, 27, 37, 25, 26, 36, 35, 28, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 15180.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([34, 30, 41, 56, 17, 44, 21, 63, 60, 54, 50, 18, 35, 65, 25, 48, 39,
       45, 37, 46, 29, 16, 36, 19, 24,  7,  9, 14, 10, 61, 20, 11,  6, 64,
       53, 57, 22, 28,  8,  5,  0,  1, 42,  2, 15, 47, 49, 59, 52, 62, 27,
       26, 13,  4, 33, 38, 58, 43, 32, 51, 23, 12, 55, 40,  3, 31],
      dtype=int64), 'cur_cost': 92166.0, 'intermediate_solutions': [{'tour': array([42,  8, 43, 63,  1, 24, 31, 38, 15, 37, 57, 22, 49, 27, 65, 53, 17,
       25, 64, 60,  3,  5,  4, 48, 18,  6, 54, 30, 33, 16, 36, 10, 44, 56,
        9, 45, 21, 29, 19, 34, 26, 14, 50, 47,  7, 35, 13, 52, 12, 58, 55,
       59, 41, 40,  2,  0, 51, 28, 11, 23, 39, 32, 20, 61, 62, 46]), 'cur_cost': 99227.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([63, 42,  8, 43,  1, 24, 31, 38, 15, 37, 57, 22, 49, 27, 65, 53, 17,
       25, 64, 60,  3,  5,  4, 48, 18,  6, 54, 30, 33, 16, 36, 10, 44, 56,
        9, 45, 21, 29, 19, 34, 26, 14, 50, 47,  7, 35, 13, 52, 12, 58, 55,
       59, 41, 40,  2,  0, 51, 28, 11, 23, 39, 32, 20, 61, 62, 46]), 'cur_cost': 103532.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1, 63, 42,  8, 43, 24, 31, 38, 15, 37, 57, 22, 49, 27, 65, 53, 17,
       25, 64, 60,  3,  5,  4, 48, 18,  6, 54, 30, 33, 16, 36, 10, 44, 56,
        9, 45, 21, 29, 19, 34, 26, 14, 50, 47,  7, 35, 13, 52, 12, 58, 55,
       59, 41, 40,  2,  0, 51, 28, 11, 23, 39, 32, 20, 61, 62, 46]), 'cur_cost': 101904.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([43, 63, 42,  8,  1, 24, 31, 38, 15, 37, 57, 22, 49, 27, 65, 53, 17,
       25, 64, 60,  3,  5,  4, 48, 18,  6, 54, 30, 33, 16, 36, 10, 44, 56,
        9, 45, 21, 29, 19, 34, 26, 14, 50, 47,  7, 35, 13, 52, 12, 58, 55,
       59, 41, 40,  2,  0, 51, 28, 11, 23, 39, 32, 20, 61, 62, 46]), 'cur_cost': 98632.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([43,  1, 63, 42,  8, 24, 31, 38, 15, 37, 57, 22, 49, 27, 65, 53, 17,
       25, 64, 60,  3,  5,  4, 48, 18,  6, 54, 30, 33, 16, 36, 10, 44, 56,
        9, 45, 21, 29, 19, 34, 26, 14, 50, 47,  7, 35, 13, 52, 12, 58, 55,
       59, 41, 40,  2,  0, 51, 28, 11, 23, 39, 32, 20, 61, 62, 46]), 'cur_cost': 99312.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [22, 35, 0, 14, 7, 53, 20, 28, 6, 61, 58, 63, 55, 9, 56, 4, 16, 18, 33, 11, 13, 5, 27, 34, 31, 12, 1, 15, 10, 19, 26, 3, 23, 30, 2, 17, 43, 40, 44, 39, 21, 24, 49, 38, 46, 42, 48, 47, 51, 29, 36, 41, 32, 8, 64, 54, 60, 57, 52, 59, 62, 45, 37, 25, 50, 65], 'cur_cost': 68778.0, 'intermediate_solutions': [{'tour': [0, 20, 6, 9, 11, 7, 3, 1, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 32, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 58, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 22282.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 6, 9, 11, 7, 3, 1, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 51, 38, 45, 44, 50, 41, 42], 'cur_cost': 12884.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 6, 9, 11, 7, 3, 1, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 35, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 15180.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [57, 53, 4, 2, 6, 9, 7, 3, 1, 34, 26, 30, 24, 59, 16, 58, 20, 33, 40, 54, 10, 52, 36, 25, 28, 0, 14, 39, 51, 47, 17, 49, 38, 42, 31, 41, 55, 62, 56, 64, 63, 50, 12, 27, 46, 13, 32, 45, 61, 23, 48, 22, 35, 43, 65, 11, 37, 19, 18, 29, 60, 44, 21, 5, 15, 8], 'cur_cost': 81665.0, 'intermediate_solutions': [{'tour': [60, 62, 23, 37, 26, 22, 3, 19, 65, 61, 17, 12, 29, 18, 15, 11, 54, 8, 63, 53, 59, 55, 7, 10, 14, 28, 0, 24, 27, 6, 64, 52, 58, 9, 13, 1, 35, 4, 34, 31, 16, 21, 2, 33, 49, 40, 43, 50, 41, 45, 51, 39, 46, 20, 32, 30, 36, 48, 42, 38, 25, 47, 44, 5, 56, 57], 'cur_cost': 58031.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [60, 62, 23, 37, 26, 22, 3, 1, 65, 61, 17, 12, 29, 24, 0, 28, 14, 10, 7, 55, 59, 53, 63, 8, 54, 11, 15, 18, 27, 6, 64, 52, 58, 9, 13, 19, 35, 4, 34, 31, 16, 21, 2, 33, 49, 40, 43, 50, 41, 45, 51, 39, 46, 20, 32, 30, 36, 48, 42, 38, 25, 47, 44, 5, 56, 57], 'cur_cost': 53683.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [60, 62, 23, 37, 26, 22, 3, 1, 65, 61, 17, 12, 18, 15, 11, 54, 8, 63, 53, 59, 55, 7, 10, 14, 28, 0, 24, 27, 6, 64, 52, 58, 9, 13, 19, 35, 4, 34, 31, 16, 21, 2, 33, 49, 40, 43, 50, 41, 45, 51, 39, 46, 29, 20, 32, 30, 36, 48, 42, 38, 25, 47, 44, 5, 56, 57], 'cur_cost': 53449.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 21, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12909.0, 'intermediate_solutions': [{'tour': [17, 15, 6, 27, 26, 4, 9, 54, 20, 3, 13, 55, 8, 62, 7, 61, 0, 58, 60, 21, 37, 14, 23, 5, 63, 49, 39, 50, 46, 19, 40, 12, 30, 16, 25, 41, 32, 31, 10, 53, 1, 51, 18, 48, 38, 33, 57, 44, 64, 42, 59, 29, 22, 52, 34, 56, 2, 28, 45, 43, 65, 11, 24, 47, 35, 36], 'cur_cost': 95449.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [17, 15, 6, 27, 26, 4, 9, 54, 20, 3, 13, 55, 8, 62, 7, 61, 0, 58, 60, 21, 37, 14, 23, 5, 63, 49, 39, 50, 46, 19, 40, 12, 30, 16, 25, 41, 32, 31, 10, 53, 1, 51, 38, 48, 18, 64, 44, 57, 33, 42, 59, 29, 22, 52, 34, 56, 2, 28, 45, 43, 65, 11, 24, 47, 35, 36], 'cur_cost': 93059.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [17, 15, 6, 27, 26, 4, 9, 54, 20, 3, 13, 55, 8, 62, 7, 61, 0, 58, 60, 21, 37, 14, 23, 5, 63, 49, 39, 50, 46, 19, 40, 12, 30, 16, 25, 41, 32, 31, 10, 53, 1, 51, 38, 48, 24, 18, 33, 57, 44, 64, 42, 59, 29, 22, 52, 34, 56, 2, 28, 45, 43, 65, 11, 47, 35, 36], 'cur_cost': 93330.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([24, 21, 12, 63, 29, 65, 22, 15, 48, 20, 54, 14, 11,  7, 19,  8, 62,
       18, 17, 50, 10, 30, 25, 44, 53, 60,  9, 41,  4,  0,  1, 56, 43, 45,
       34,  6, 47, 33,  5, 13, 46, 26, 40, 39, 23, 55, 64, 32, 27, 37, 42,
       59,  2, 58, 52, 57, 16, 61, 35, 31, 28, 38,  3, 51, 49, 36],
      dtype=int64), 'cur_cost': 93385.0, 'intermediate_solutions': [{'tour': array([48, 31, 49, 54, 60, 13, 17, 51, 55, 32, 14, 65, 21, 56, 52, 46, 16,
       35, 64,  2,  3, 29, 61, 27,  9, 58, 42,  6, 15, 10, 19, 12, 38, 45,
       40, 34, 63, 44, 50, 53, 41, 25,  0, 23,  5, 30, 18,  1, 37, 39,  4,
       20, 28, 47, 43,  7, 24, 22, 33,  8, 62, 59, 11, 26, 36, 57]), 'cur_cost': 109100.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([54, 48, 31, 49, 60, 13, 17, 51, 55, 32, 14, 65, 21, 56, 52, 46, 16,
       35, 64,  2,  3, 29, 61, 27,  9, 58, 42,  6, 15, 10, 19, 12, 38, 45,
       40, 34, 63, 44, 50, 53, 41, 25,  0, 23,  5, 30, 18,  1, 37, 39,  4,
       20, 28, 47, 43,  7, 24, 22, 33,  8, 62, 59, 11, 26, 36, 57]), 'cur_cost': 109027.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([60, 54, 48, 31, 49, 13, 17, 51, 55, 32, 14, 65, 21, 56, 52, 46, 16,
       35, 64,  2,  3, 29, 61, 27,  9, 58, 42,  6, 15, 10, 19, 12, 38, 45,
       40, 34, 63, 44, 50, 53, 41, 25,  0, 23,  5, 30, 18,  1, 37, 39,  4,
       20, 28, 47, 43,  7, 24, 22, 33,  8, 62, 59, 11, 26, 36, 57]), 'cur_cost': 105703.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([49, 54, 48, 31, 60, 13, 17, 51, 55, 32, 14, 65, 21, 56, 52, 46, 16,
       35, 64,  2,  3, 29, 61, 27,  9, 58, 42,  6, 15, 10, 19, 12, 38, 45,
       40, 34, 63, 44, 50, 53, 41, 25,  0, 23,  5, 30, 18,  1, 37, 39,  4,
       20, 28, 47, 43,  7, 24, 22, 33,  8, 62, 59, 11, 26, 36, 57]), 'cur_cost': 112716.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([49, 60, 54, 48, 31, 13, 17, 51, 55, 32, 14, 65, 21, 56, 52, 46, 16,
       35, 64,  2,  3, 29, 61, 27,  9, 58, 42,  6, 15, 10, 19, 12, 38, 45,
       40, 34, 63, 44, 50, 53, 41, 25,  0, 23,  5, 30, 18,  1, 37, 39,  4,
       20, 28, 47, 43,  7, 24, 22, 33,  8, 62, 59, 11, 26, 36, 57]), 'cur_cost': 109036.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([64, 11, 33, 31, 47, 58, 18, 12,  0, 51, 38, 21, 28, 22, 20, 39, 25,
       17, 37, 52, 43, 41,  6, 46, 32, 42, 36,  9, 63, 23, 40, 29, 45, 24,
       50,  5, 15, 60, 35, 61, 53, 27, 55,  2, 44,  3, 57,  8, 19, 34, 62,
        7, 65, 13, 49, 30,  1, 48,  4, 59, 10, 26, 16, 14, 56, 54],
      dtype=int64), 'cur_cost': 108330.0, 'intermediate_solutions': [{'tour': array([48, 44, 39, 29, 19, 17, 34, 55, 57, 42, 45,  7,  4,  1, 64, 30,  8,
       49, 23, 50, 31, 63, 40, 11, 26, 12, 21, 53, 32, 27,  2, 25, 16, 35,
       15, 18, 24, 60, 58, 20, 33, 14, 36, 54, 47,  5, 10, 65, 13, 38, 37,
       52, 28, 59, 61,  6, 56, 51, 41, 62,  3,  9, 43, 46, 22,  0]), 'cur_cost': 100969.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([29, 48, 44, 39, 19, 17, 34, 55, 57, 42, 45,  7,  4,  1, 64, 30,  8,
       49, 23, 50, 31, 63, 40, 11, 26, 12, 21, 53, 32, 27,  2, 25, 16, 35,
       15, 18, 24, 60, 58, 20, 33, 14, 36, 54, 47,  5, 10, 65, 13, 38, 37,
       52, 28, 59, 61,  6, 56, 51, 41, 62,  3,  9, 43, 46, 22,  0]), 'cur_cost': 100571.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([19, 29, 48, 44, 39, 17, 34, 55, 57, 42, 45,  7,  4,  1, 64, 30,  8,
       49, 23, 50, 31, 63, 40, 11, 26, 12, 21, 53, 32, 27,  2, 25, 16, 35,
       15, 18, 24, 60, 58, 20, 33, 14, 36, 54, 47,  5, 10, 65, 13, 38, 37,
       52, 28, 59, 61,  6, 56, 51, 41, 62,  3,  9, 43, 46, 22,  0]), 'cur_cost': 101083.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([39, 29, 48, 44, 19, 17, 34, 55, 57, 42, 45,  7,  4,  1, 64, 30,  8,
       49, 23, 50, 31, 63, 40, 11, 26, 12, 21, 53, 32, 27,  2, 25, 16, 35,
       15, 18, 24, 60, 58, 20, 33, 14, 36, 54, 47,  5, 10, 65, 13, 38, 37,
       52, 28, 59, 61,  6, 56, 51, 41, 62,  3,  9, 43, 46, 22,  0]), 'cur_cost': 103252.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([39, 19, 29, 48, 44, 17, 34, 55, 57, 42, 45,  7,  4,  1, 64, 30,  8,
       49, 23, 50, 31, 63, 40, 11, 26, 12, 21, 53, 32, 27,  2, 25, 16, 35,
       15, 18, 24, 60, 58, 20, 33, 14, 36, 54, 47,  5, 10, 65, 13, 38, 37,
       52, 28, 59, 61,  6, 56, 51, 41, 62,  3,  9, 43, 46, 22,  0]), 'cur_cost': 103649.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 3, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14862.0, 'intermediate_solutions': [{'tour': [0, 8, 13, 7, 3, 9, 11, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 47, 29, 32, 40, 49, 24, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 21281.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 28, 35, 36, 26, 25, 37, 27, 20, 21, 19, 23, 16, 18, 17, 12, 22, 15, 14, 63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10, 2, 6, 4, 5, 1, 11, 9, 3, 7, 13, 8, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 16869.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 13, 7, 3, 9, 11, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 51, 49, 47, 46, 48, 43, 39, 44, 45, 38, 50, 41, 42], 'cur_cost': 13019.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:14,260 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:14,260 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:14,263 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10393.000, 多样性=0.952
2025-08-05 09:52:14,263 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 09:52:14,263 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 09:52:14,263 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:14,264 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.07890457442242578, 'best_improvement': 0.18332547540468333}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.011083303539506647}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.05332999401424766, 'recent_improvements': [0.10184900382217661, -0.05051827029262141, -0.004810984206318699], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.9696969696969697, 'new_diversity': 0.9696969696969697, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:52:14,264 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 09:52:14,267 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_solution.json
2025-08-05 09:52:14,268 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_20250805_095214.solution
2025-08-05 09:52:14,268 - __main__ - INFO - 实例执行完成 - 运行时间: 1.59s, 最佳成本: 9521
2025-08-05 09:52:14,268 - __main__ - INFO - 实例 composite13_66 处理完成
