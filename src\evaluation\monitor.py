"""
评估监控和报告

提供实时监控、警报和报告生成功能。
"""

import time
import threading
from typing import Dict, List, Optional, Any, Callable

from .types import EvaluationType, AlgorithmPhase, EvaluationStatistics
from .counter import EvaluationCounter
from .config import EvaluationConfig


class EvaluationMonitor:
    """评估监控器"""
    
    def __init__(self, 
                 counter: EvaluationCounter,
                 config: Optional[EvaluationConfig] = None):
        """
        初始化评估监控器
        
        Args:
            counter: 评估计数器
            config: 监控配置
        """
        self.counter = counter
        self.config = config or EvaluationConfig()
        self.monitoring_active = False
        self.alert_callbacks: List[Callable[[str], None]] = []
        self.monitoring_thread: Optional[threading.Thread] = None
        self._last_check_time = time.time()
        self._last_total_count = 0
    
    def add_alert_callback(self, callback: Callable[[str], None]) -> None:
        """添加警报回调函数"""
        self.alert_callbacks.append(callback)
    
    def remove_alert_callback(self, callback: Callable[[str], None]) -> bool:
        """移除警报回调函数"""
        try:
            self.alert_callbacks.remove(callback)
            return True
        except ValueError:
            return False
    
    def start_monitoring(self) -> bool:
        """开始监控"""
        if self.monitoring_active or not self.config.enable_monitoring:
            return False
        
        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop, 
            daemon=True,
            name="EvaluationMonitor"
        )
        self.monitoring_thread.start()
        return True
    
    def stop_monitoring(self) -> bool:
        """停止监控"""
        if not self.monitoring_active:
            return False
        
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=2.0)
        return True
    
    def _monitoring_loop(self) -> None:
        """监控循环"""
        while self.monitoring_active:
            try:
                alerts = self.check_alerts()
                for alert in alerts:
                    self._trigger_alert(alert)
                
                time.sleep(self.config.monitoring_interval)
            except Exception as e:
                print(f"监控循环错误: {e}")
                time.sleep(1.0)  # 错误时等待1秒
    
    def _trigger_alert(self, alert_message: str) -> None:
        """触发警报"""
        for callback in self.alert_callbacks:
            try:
                callback(alert_message)
            except Exception as e:
                print(f"警报回调错误: {e}")
    
    def get_real_time_metrics(self) -> Dict[str, Any]:
        """获取实时指标"""
        stats = self.counter.get_statistics()
        current_time = time.time()
        
        elapsed_time = stats.get_elapsed_time()
        evaluations_per_second = stats.get_evaluations_per_second()
        
        # 计算瞬时速率
        time_since_last_check = current_time - self._last_check_time
        count_since_last_check = stats.total_count - self._last_total_count
        
        instantaneous_rate = 0.0
        if time_since_last_check > 0:
            instantaneous_rate = count_since_last_check / time_since_last_check
        
        # 更新检查点
        self._last_check_time = current_time
        self._last_total_count = stats.total_count
        
        # 计算效率指标
        efficiency_metrics = self._calculate_efficiency_metrics(stats)
        
        return {
            'timestamp': current_time,
            'total_evaluations': stats.total_count,
            'elapsed_time': elapsed_time,
            'average_evaluations_per_second': evaluations_per_second,
            'instantaneous_evaluations_per_second': instantaneous_rate,
            'memory_usage_mb': self.counter.get_memory_usage_mb(),
            'by_type': {t.value: count for t, count in stats.by_type.items() if count > 0},
            'by_phase': {p.value: count for p, count in stats.by_phase.items() if count > 0},
            'efficiency': efficiency_metrics,
            'cost_statistics': {
                'average_cost': stats.get_average_cost(),
                'min_cost': stats.min_cost,
                'max_cost': stats.max_cost,
                'best_cost': stats.best_cost
            },
            'improvement_statistics': {
                'average_improvement': stats.get_average_improvement(),
                'total_improvement': stats.total_improvement,
                'improvement_count': stats.improvement_count
            }
        }
    
    def _calculate_efficiency_metrics(self, stats: EvaluationStatistics) -> Dict[str, float]:
        """计算效率指标"""
        total = stats.total_count
        if total == 0:
            return {}
        
        return {
            'full_evaluation_ratio': stats.by_type.get(EvaluationType.FULL_EVALUATION, 0) / total,
            'delta_evaluation_ratio': stats.by_type.get(EvaluationType.DELTA_EVALUATION, 0) / total,
            'local_search_ratio': stats.by_phase.get(AlgorithmPhase.LOCAL_SEARCH, 0) / total,
            'initialization_ratio': stats.by_phase.get(AlgorithmPhase.INITIALIZATION, 0) / total,
            'evolution_ratio': stats.by_phase.get(AlgorithmPhase.EVOLUTION, 0) / total
        }
    
    def check_alerts(self) -> List[str]:
        """检查警报条件"""
        alerts = []
        metrics = self.get_real_time_metrics()
        thresholds = self.config.alert_thresholds
        
        # 检查评估频率
        avg_rate = metrics['average_evaluations_per_second']
        if avg_rate > thresholds['max_evaluations_per_second']:
            alerts.append(
                f">> 高评估频率警报: {avg_rate:.1f} 次/秒 "
                f"(阈值: {thresholds['max_evaluations_per_second']})"
            )
        
        # 检查总评估次数
        total_evals = metrics['total_evaluations']
        if total_evals > thresholds['max_evaluations']:
            alerts.append(
                f">> 评估次数超限: {total_evals:,} 次 "
                f"(阈值: {thresholds['max_evaluations']:,})"
            )
        
        # 检查内存使用
        memory_mb = metrics['memory_usage_mb']
        if memory_mb > thresholds['memory_usage_mb']:
            alerts.append(
                f">> 内存使用警报: {memory_mb:.1f} MB "
                f"(阈值: {thresholds['memory_usage_mb']} MB)"
            )
        
        # 检查效率指标
        efficiency = metrics.get('efficiency', {})
        full_eval_ratio = efficiency.get('full_evaluation_ratio', 0)
        if full_eval_ratio > 0.8:  # 80%以上是完整评估
            alerts.append(
                f">> 完整评估比例过高: {full_eval_ratio:.1%} "
                f"(建议增加增量评估使用)"
            )
        
        # 检查运行时间
        elapsed_time = metrics['elapsed_time']
        if elapsed_time > thresholds['phase_duration_seconds']:
            alerts.append(
                f">> 运行时间过长: {elapsed_time:.1f} 秒 "
                f"(阈值: {thresholds['phase_duration_seconds']} 秒)"
            )
        
        return alerts
    
    def generate_monitoring_report(self) -> Dict[str, Any]:
        """生成监控报告"""
        metrics = self.get_real_time_metrics()
        alerts = self.check_alerts()
        
        return {
            'report_time': time.time(),
            'monitoring_status': 'active' if self.monitoring_active else 'inactive',
            'current_metrics': metrics,
            'active_alerts': alerts,
            'alert_count': len(alerts),
            'thresholds': self.config.alert_thresholds,
            'config_summary': {
                'monitoring_interval': self.config.monitoring_interval,
                'enable_monitoring': self.config.enable_monitoring,
                'detailed_logging': self.config.detailed_logging
            }
        }


class EvaluationReporter:
    """评估报告生成器"""
    
    def __init__(self, counter: EvaluationCounter):
        """
        初始化报告生成器
        
        Args:
            counter: 评估计数器
        """
        self.counter = counter
    
    def generate_summary_report(self) -> str:
        """生成摘要报告"""
        stats = self.counter.get_statistics()
        
        report_lines = [
            "=" * 60,
            "EoH-TSP-Solver 评估统计摘要报告",
            "=" * 60,
            f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            ">> 基础统计:",
            f"  总评估次数: {stats.total_count:,}",
            f"  运行时间: {stats.get_elapsed_time():.2f} 秒",
            f"  平均评估速率: {stats.get_evaluations_per_second():.1f} 次/秒",
            f"  内存使用: {self.counter.get_memory_usage_mb():.2f} MB",
            ""
        ]
        
        # 按类型统计
        if any(count > 0 for count in stats.by_type.values()):
            report_lines.extend([
                ">> 按评估类型统计:",
                *[f"  {eval_type.value}: {count:,} ({count/stats.total_count*100:.1f}%)"
                  for eval_type, count in stats.by_type.items() if count > 0],
                ""
            ])
        
        # 按阶段统计
        if any(count > 0 for count in stats.by_phase.values()):
            report_lines.extend([
                ">> 按算法阶段统计:",
                *[f"  {phase.value}: {count:,} ({count/stats.total_count*100:.1f}%)"
                  for phase, count in stats.by_phase.items() if count > 0],
                ""
            ])
        
        # 成本统计
        if stats.best_cost is not None:
            report_lines.extend([
                ">> 成本统计:",
                f"  最佳成本: {stats.best_cost:.2f}",
                f"  最小成本: {stats.min_cost:.2f}",
                f"  最大成本: {stats.max_cost:.2f}",
                f"  平均成本: {stats.get_average_cost():.2f}" if stats.get_average_cost() else "  平均成本: N/A",
                ""
            ])
        
        # 改进统计
        if stats.improvement_count > 0:
            report_lines.extend([
                ">> 改进统计:",
                f"  总改进量: {stats.total_improvement:.2f}",
                f"  改进次数: {stats.improvement_count:,}",
                f"  平均改进: {stats.get_average_improvement():.4f}",
                ""
            ])
        
        report_lines.append("=" * 60)
        
        return "\n".join(report_lines)
    
    def generate_detailed_report(self) -> str:
        """生成详细报告"""
        stats = self.counter.get_statistics()
        records = self.counter.get_detailed_records(limit=100)  # 最近100条记录
        
        report_lines = [
            "=" * 80,
            "EoH-TSP-Solver 详细评估统计报告",
            "=" * 80,
            f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            self.generate_summary_report(),
            ""
        ]
        
        if records:
            report_lines.extend([
                ">> 最近评估记录 (最多显示100条):",
                "-" * 80,
                f"{'时间':<12} {'类型':<20} {'阶段':<15} {'次数':<6} {'成本':<10} {'改进':<10}",
                "-" * 80
            ])
            
            for record in records[-20:]:  # 显示最近20条
                timestamp_str = time.strftime('%H:%M:%S', time.localtime(record.timestamp))
                cost_str = f"{record.cost_value:.2f}" if record.cost_value is not None else "N/A"
                improvement_str = f"{record.improvement:.4f}" if record.improvement is not None else "N/A"
                
                report_lines.append(
                    f"{timestamp_str:<12} {record.evaluation_type.value:<20} "
                    f"{record.algorithm_phase.value:<15} {record.count:<6} "
                    f"{cost_str:<10} {improvement_str:<10}"
                )
            
            report_lines.extend(["", "-" * 80])
        
        report_lines.append("=" * 80)
        
        return "\n".join(report_lines)
    
    def export_report(self, 
                     filepath: str, 
                     format: str = "txt",
                     detailed: bool = False) -> bool:
        """
        导出报告到文件
        
        Args:
            filepath: 文件路径
            format: 导出格式 (txt, json)
            detailed: 是否生成详细报告
        
        Returns:
            bool: 是否成功导出
        """
        try:
            if format.lower() == "txt":
                content = self.generate_detailed_report() if detailed else self.generate_summary_report()
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(content)
            
            elif format.lower() == "json":
                import json
                stats = self.counter.get_statistics()
                
                data = {
                    'timestamp': time.time(),
                    'total_count': stats.total_count,
                    'elapsed_time': stats.get_elapsed_time(),
                    'evaluations_per_second': stats.get_evaluations_per_second(),
                    'memory_usage_mb': self.counter.get_memory_usage_mb(),
                    'by_type': {t.value: count for t, count in stats.by_type.items()},
                    'by_phase': {p.value: count for p, count in stats.by_phase.items()},
                    'cost_statistics': {
                        'best_cost': stats.best_cost,
                        'min_cost': stats.min_cost,
                        'max_cost': stats.max_cost,
                        'average_cost': stats.get_average_cost()
                    },
                    'improvement_statistics': {
                        'total_improvement': stats.total_improvement,
                        'improvement_count': stats.improvement_count,
                        'average_improvement': stats.get_average_improvement()
                    }
                }
                
                if detailed:
                    records = self.counter.get_detailed_records()
                    data['detailed_records'] = [
                        {
                            'timestamp': record.timestamp,
                            'evaluation_type': record.evaluation_type.value,
                            'algorithm_phase': record.algorithm_phase.value,
                            'count': record.count,
                            'cost_value': record.cost_value,
                            'improvement': record.improvement,
                            'metadata': record.metadata
                        }
                        for record in records
                    ]
                
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
            
            else:
                raise ValueError(f"不支持的导出格式: {format}")
            
            return True
            
        except Exception as e:
            print(f"导出报告失败: {e}")
            return False
