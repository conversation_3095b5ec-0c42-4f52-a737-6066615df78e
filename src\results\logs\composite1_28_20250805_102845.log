2025-08-05 10:28:45,975 - __main__ - INFO - composite1_28 开始进化第 1 代
2025-08-05 10:28:45,975 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 10:28:45,976 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:45,978 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=3159.000, 多样性=0.960
2025-08-05 10:28:45,979 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:45,981 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.960
2025-08-05 10:28:45,983 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:45,985 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 10:28:45,986 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:45,986 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 10:28:45,986 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 10:28:45,997 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.200, 适应度梯度: 729.760, 聚类评分: 0.000, 覆盖率: 0.073, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:45,997 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 10:28:45,997 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 10:28:45,997 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite1_28
2025-08-05 10:28:46,007 - visualization.landscape_visualizer - INFO - 插值约束: 15 个点被约束到最小值 3159.00
2025-08-05 10:28:46,008 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=8.1%, 梯度: 620.70 → 570.24
2025-08-05 10:28:46,162 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite1_28\landscape_composite1_28_iter_61_20250805_102846.html
2025-08-05 10:28:46,219 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite1_28\dashboard_composite1_28_iter_61_20250805_102846.html
2025-08-05 10:28:46,219 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 61
2025-08-05 10:28:46,219 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 10:28:46,219 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2342秒
2025-08-05 10:28:46,219 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 122, 'max_size': 500, 'hits': 0, 'misses': 122, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 414, 'misses': 202, 'hit_rate': 0.672077922077922, 'evictions': 102, 'ttl': 7200}}
2025-08-05 10:28:46,220 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 729.76, 'local_optima_density': 0.2, 'gradient_variance': 84897219.3664, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0726, 'fitness_entropy': 0.9464119282150146, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.073)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 729.760)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360925.997749, 'performance_metrics': {}}}
2025-08-05 10:28:46,220 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:46,220 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:46,220 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:46,220 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:46,221 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:46,221 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 10:28:46,221 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:46,222 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:46,222 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:46,222 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:46,222 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:46,222 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:46,222 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:46,222 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:46,222 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:46,222 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:46,225 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 28
2025-08-05 10:28:46,225 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,225 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10633.0, 路径长度: 28, 收集中间解: 0
2025-08-05 10:28:46,226 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [1, 11, 20, 13, 21, 22, 15, 14, 17, 25, 10, 23, 9, 27, 26, 8, 2, 5, 4, 6, 3, 0, 7, 24, 16, 18, 12, 19], 'cur_cost': 10633.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:46,226 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 10633.00)
2025-08-05 10:28:46,226 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:46,226 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:46,226 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:46,227 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 28
2025-08-05 10:28:46,228 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,229 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3804.0, 路径长度: 28, 收集中间解: 0
2025-08-05 10:28:46,229 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 2, 24, 17, 14, 15, 16, 12, 18, 9, 11, 10, 13, 21, 26, 25, 23, 27, 19, 20, 22, 8, 7, 5, 3, 4, 6, 1], 'cur_cost': 3804.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:46,229 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 3804.00)
2025-08-05 10:28:46,229 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:46,229 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:46,229 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:46,230 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 28
2025-08-05 10:28:46,230 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,230 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16454.0, 路径长度: 28, 收集中间解: 0
2025-08-05 10:28:46,230 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 11, 20, 17, 21, 22, 16, 18, 23, 9, 27, 8, 5, 6, 3, 25, 12, 14, 19, 4, 15, 10, 1, 24, 13, 2, 26, 7], 'cur_cost': 16454.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:46,231 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 16454.00)
2025-08-05 10:28:46,231 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:28:46,231 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:46,231 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:46,231 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 17051.0
2025-08-05 10:28:46,237 - ExploitationExpert - INFO - res_population_num: 3
2025-08-05 10:28:46,237 - ExploitationExpert - INFO - res_population_costs: [3055.0, 3055, 3055]
2025-08-05 10:28:46,238 - ExploitationExpert - INFO - res_populations: [array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64)]
2025-08-05 10:28:46,239 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:46,239 - ExploitationExpert - INFO - populations: [{'tour': [1, 11, 20, 13, 21, 22, 15, 14, 17, 25, 10, 23, 9, 27, 26, 8, 2, 5, 4, 6, 3, 0, 7, 24, 16, 18, 12, 19], 'cur_cost': 10633.0}, {'tour': [0, 2, 24, 17, 14, 15, 16, 12, 18, 9, 11, 10, 13, 21, 26, 25, 23, 27, 19, 20, 22, 8, 7, 5, 3, 4, 6, 1], 'cur_cost': 3804.0}, {'tour': [0, 11, 20, 17, 21, 22, 16, 18, 23, 9, 27, 8, 5, 6, 3, 25, 12, 14, 19, 4, 15, 10, 1, 24, 13, 2, 26, 7], 'cur_cost': 16454.0}, {'tour': array([ 3, 14, 25, 10, 19, 13, 26, 16, 22, 24,  9,  6, 23, 18, 21, 27,  0,
       20, 12,  2, 11, 15,  7,  8,  4,  1,  5, 17], dtype=int64), 'cur_cost': 17051.0}, {'tour': array([19, 14,  7,  0, 27,  2, 18, 10,  6, 25, 21,  5, 26, 11, 16,  9,  8,
       12,  4, 17, 22, 24, 15,  3,  1, 13, 23, 20], dtype=int64), 'cur_cost': 18063.0}, {'tour': array([ 6, 14, 12, 11, 17, 26,  1, 20, 27,  9,  4,  5, 21, 19, 22,  2,  8,
       16,  0, 18, 25, 24, 23, 10, 13, 15,  3,  7], dtype=int64), 'cur_cost': 13373.0}, {'tour': array([ 2, 14, 24, 15,  7, 10, 21,  5,  6, 13,  9, 25, 17, 11, 23,  3,  0,
       22, 27, 18, 12, 20,  1, 16,  4,  8, 19, 26], dtype=int64), 'cur_cost': 17579.0}, {'tour': array([11,  6, 26, 13,  0, 21, 24, 12, 10,  5, 14,  2, 20, 23,  3, 19,  1,
       16, 22,  8, 27,  9, 25,  7, 17,  4, 15, 18], dtype=int64), 'cur_cost': 22138.0}, {'tour': array([19, 26,  6, 10,  7,  3, 27,  1, 20,  5,  2, 25,  0, 12, 22, 23, 18,
        9, 24, 17,  4, 15,  8, 14, 16, 21, 13, 11], dtype=int64), 'cur_cost': 18915.0}, {'tour': array([ 5, 18,  7,  4, 10, 15,  6, 13, 12,  3, 11, 27, 21, 25, 17, 14, 16,
        8, 19,  9,  2, 23, 20, 22, 26, 24,  1,  0], dtype=int64), 'cur_cost': 14947.0}]
2025-08-05 10:28:46,241 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:46,241 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 157, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 157, 'cache_hits': 0, 'similarity_calculations': 654, 'cache_hit_rate': 0.0, 'cache_size': 654}}
2025-08-05 10:28:46,241 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 3, 14, 25, 10, 19, 13, 26, 16, 22, 24,  9,  6, 23, 18, 21, 27,  0,
       20, 12,  2, 11, 15,  7,  8,  4,  1,  5, 17], dtype=int64), 'cur_cost': 17051.0, 'intermediate_solutions': [{'tour': array([12, 14, 17,  0, 25, 16, 21, 20, 15,  2,  9, 19, 22, 18,  1, 10,  6,
       27, 23,  4, 24, 13,  3,  5, 26,  7, 11,  8], dtype=int64), 'cur_cost': 20575.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 0, 12, 14, 17, 25, 16, 21, 20, 15,  2,  9, 19, 22, 18,  1, 10,  6,
       27, 23,  4, 24, 13,  3,  5, 26,  7, 11,  8], dtype=int64), 'cur_cost': 19054.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([25,  0, 12, 14, 17, 16, 21, 20, 15,  2,  9, 19, 22, 18,  1, 10,  6,
       27, 23,  4, 24, 13,  3,  5, 26,  7, 11,  8], dtype=int64), 'cur_cost': 20016.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([17,  0, 12, 14, 25, 16, 21, 20, 15,  2,  9, 19, 22, 18,  1, 10,  6,
       27, 23,  4, 24, 13,  3,  5, 26,  7, 11,  8], dtype=int64), 'cur_cost': 21107.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([17, 25,  0, 12, 14, 16, 21, 20, 15,  2,  9, 19, 22, 18,  1, 10,  6,
       27, 23,  4, 24, 13,  3,  5, 26,  7, 11,  8], dtype=int64), 'cur_cost': 20567.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:46,242 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 17051.00)
2025-08-05 10:28:46,242 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:46,242 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:46,242 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:46,243 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 28
2025-08-05 10:28:46,243 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,243 - ExplorationExpert - INFO - 探索路径生成完成，成本: 4309.0, 路径长度: 28, 收集中间解: 0
2025-08-05 10:28:46,243 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 19, 9, 20, 24, 25, 26, 22, 21, 23, 27, 10, 14, 17, 16, 15, 18, 12, 13, 11, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 4309.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:46,243 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 4309.00)
2025-08-05 10:28:46,244 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:46,244 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:46,244 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:46,244 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 28
2025-08-05 10:28:46,245 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,245 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16034.0, 路径长度: 28, 收集中间解: 0
2025-08-05 10:28:46,245 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [19, 9, 20, 24, 15, 18, 17, 25, 10, 23, 5, 27, 8, 2, 14, 4, 6, 22, 0, 1, 11, 12, 7, 3, 13, 21, 26, 16], 'cur_cost': 16034.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:46,245 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 16034.00)
2025-08-05 10:28:46,245 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:46,245 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:46,245 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:46,246 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 28
2025-08-05 10:28:46,246 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,246 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3677.0, 路径长度: 28, 收集中间解: 0
2025-08-05 10:28:46,247 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 8, 21, 10, 14, 17, 16, 15, 9, 18, 12, 13, 11, 20, 24, 25, 26, 22, 23, 27, 19, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3677.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:46,247 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 3677.00)
2025-08-05 10:28:46,247 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:28:46,247 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:46,247 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:46,247 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 17045.0
2025-08-05 10:28:46,257 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 10:28:46,258 - ExploitationExpert - INFO - res_population_costs: [3055.0, 3055, 3055, 3055.0, 3055, 3055, 3055]
2025-08-05 10:28:46,258 - ExploitationExpert - INFO - res_populations: [array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64)]
2025-08-05 10:28:46,261 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:46,261 - ExploitationExpert - INFO - populations: [{'tour': [1, 11, 20, 13, 21, 22, 15, 14, 17, 25, 10, 23, 9, 27, 26, 8, 2, 5, 4, 6, 3, 0, 7, 24, 16, 18, 12, 19], 'cur_cost': 10633.0}, {'tour': [0, 2, 24, 17, 14, 15, 16, 12, 18, 9, 11, 10, 13, 21, 26, 25, 23, 27, 19, 20, 22, 8, 7, 5, 3, 4, 6, 1], 'cur_cost': 3804.0}, {'tour': [0, 11, 20, 17, 21, 22, 16, 18, 23, 9, 27, 8, 5, 6, 3, 25, 12, 14, 19, 4, 15, 10, 1, 24, 13, 2, 26, 7], 'cur_cost': 16454.0}, {'tour': array([ 3, 14, 25, 10, 19, 13, 26, 16, 22, 24,  9,  6, 23, 18, 21, 27,  0,
       20, 12,  2, 11, 15,  7,  8,  4,  1,  5, 17], dtype=int64), 'cur_cost': 17051.0}, {'tour': [0, 19, 9, 20, 24, 25, 26, 22, 21, 23, 27, 10, 14, 17, 16, 15, 18, 12, 13, 11, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 4309.0}, {'tour': [19, 9, 20, 24, 15, 18, 17, 25, 10, 23, 5, 27, 8, 2, 14, 4, 6, 22, 0, 1, 11, 12, 7, 3, 13, 21, 26, 16], 'cur_cost': 16034.0}, {'tour': [0, 8, 21, 10, 14, 17, 16, 15, 9, 18, 12, 13, 11, 20, 24, 25, 26, 22, 23, 27, 19, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3677.0}, {'tour': array([17,  3, 15,  8, 16, 24,  6,  1,  7, 21, 19, 20, 13,  0,  5, 12, 10,
        2,  9, 14, 27, 22,  4, 11, 25, 18, 26, 23], dtype=int64), 'cur_cost': 17045.0}, {'tour': array([19, 26,  6, 10,  7,  3, 27,  1, 20,  5,  2, 25,  0, 12, 22, 23, 18,
        9, 24, 17,  4, 15,  8, 14, 16, 21, 13, 11], dtype=int64), 'cur_cost': 18915.0}, {'tour': array([ 5, 18,  7,  4, 10, 15,  6, 13, 12,  3, 11, 27, 21, 25, 17, 14, 16,
        8, 19,  9,  2, 23, 20, 22, 26, 24,  1,  0], dtype=int64), 'cur_cost': 14947.0}]
2025-08-05 10:28:46,263 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:46,264 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 158, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 158, 'cache_hits': 0, 'similarity_calculations': 655, 'cache_hit_rate': 0.0, 'cache_size': 655}}
2025-08-05 10:28:46,265 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([17,  3, 15,  8, 16, 24,  6,  1,  7, 21, 19, 20, 13,  0,  5, 12, 10,
        2,  9, 14, 27, 22,  4, 11, 25, 18, 26, 23], dtype=int64), 'cur_cost': 17045.0, 'intermediate_solutions': [{'tour': array([26,  6, 11, 13,  0, 21, 24, 12, 10,  5, 14,  2, 20, 23,  3, 19,  1,
       16, 22,  8, 27,  9, 25,  7, 17,  4, 15, 18], dtype=int64), 'cur_cost': 22179.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([13, 26,  6, 11,  0, 21, 24, 12, 10,  5, 14,  2, 20, 23,  3, 19,  1,
       16, 22,  8, 27,  9, 25,  7, 17,  4, 15, 18], dtype=int64), 'cur_cost': 22094.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 0, 13, 26,  6, 11, 21, 24, 12, 10,  5, 14,  2, 20, 23,  3, 19,  1,
       16, 22,  8, 27,  9, 25,  7, 17,  4, 15, 18], dtype=int64), 'cur_cost': 22615.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([11, 13, 26,  6,  0, 21, 24, 12, 10,  5, 14,  2, 20, 23,  3, 19,  1,
       16, 22,  8, 27,  9, 25,  7, 17,  4, 15, 18], dtype=int64), 'cur_cost': 20121.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([11,  0, 13, 26,  6, 21, 24, 12, 10,  5, 14,  2, 20, 23,  3, 19,  1,
       16, 22,  8, 27,  9, 25,  7, 17,  4, 15, 18], dtype=int64), 'cur_cost': 22126.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:46,265 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 17045.00)
2025-08-05 10:28:46,265 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:46,265 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:46,265 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:46,268 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 28
2025-08-05 10:28:46,268 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,269 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9647.0, 路径长度: 28, 收集中间解: 0
2025-08-05 10:28:46,269 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [19, 14, 15, 16, 9, 24, 21, 13, 23, 22, 17, 27, 11, 26, 0, 8, 2, 5, 7, 1, 20, 25, 12, 10, 18, 4, 3, 6], 'cur_cost': 9647.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:46,269 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 9647.00)
2025-08-05 10:28:46,269 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:46,269 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:46,269 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:46,270 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 28
2025-08-05 10:28:46,270 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,270 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3710.0, 路径长度: 28, 收集中间解: 0
2025-08-05 10:28:46,271 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 13, 21, 20, 24, 25, 26, 22, 23, 27, 19, 10, 14, 17, 16, 15, 9, 18, 12, 11, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3710.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:46,271 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 3710.00)
2025-08-05 10:28:46,271 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:46,271 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:46,272 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [1, 11, 20, 13, 21, 22, 15, 14, 17, 25, 10, 23, 9, 27, 26, 8, 2, 5, 4, 6, 3, 0, 7, 24, 16, 18, 12, 19], 'cur_cost': 10633.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 24, 17, 14, 15, 16, 12, 18, 9, 11, 10, 13, 21, 26, 25, 23, 27, 19, 20, 22, 8, 7, 5, 3, 4, 6, 1], 'cur_cost': 3804.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 20, 17, 21, 22, 16, 18, 23, 9, 27, 8, 5, 6, 3, 25, 12, 14, 19, 4, 15, 10, 1, 24, 13, 2, 26, 7], 'cur_cost': 16454.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 3, 14, 25, 10, 19, 13, 26, 16, 22, 24,  9,  6, 23, 18, 21, 27,  0,
       20, 12,  2, 11, 15,  7,  8,  4,  1,  5, 17], dtype=int64), 'cur_cost': 17051.0, 'intermediate_solutions': [{'tour': array([12, 14, 17,  0, 25, 16, 21, 20, 15,  2,  9, 19, 22, 18,  1, 10,  6,
       27, 23,  4, 24, 13,  3,  5, 26,  7, 11,  8], dtype=int64), 'cur_cost': 20575.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 0, 12, 14, 17, 25, 16, 21, 20, 15,  2,  9, 19, 22, 18,  1, 10,  6,
       27, 23,  4, 24, 13,  3,  5, 26,  7, 11,  8], dtype=int64), 'cur_cost': 19054.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([25,  0, 12, 14, 17, 16, 21, 20, 15,  2,  9, 19, 22, 18,  1, 10,  6,
       27, 23,  4, 24, 13,  3,  5, 26,  7, 11,  8], dtype=int64), 'cur_cost': 20016.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([17,  0, 12, 14, 25, 16, 21, 20, 15,  2,  9, 19, 22, 18,  1, 10,  6,
       27, 23,  4, 24, 13,  3,  5, 26,  7, 11,  8], dtype=int64), 'cur_cost': 21107.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([17, 25,  0, 12, 14, 16, 21, 20, 15,  2,  9, 19, 22, 18,  1, 10,  6,
       27, 23,  4, 24, 13,  3,  5, 26,  7, 11,  8], dtype=int64), 'cur_cost': 20567.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 9, 20, 24, 25, 26, 22, 21, 23, 27, 10, 14, 17, 16, 15, 18, 12, 13, 11, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 4309.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [19, 9, 20, 24, 15, 18, 17, 25, 10, 23, 5, 27, 8, 2, 14, 4, 6, 22, 0, 1, 11, 12, 7, 3, 13, 21, 26, 16], 'cur_cost': 16034.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 21, 10, 14, 17, 16, 15, 9, 18, 12, 13, 11, 20, 24, 25, 26, 22, 23, 27, 19, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3677.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([17,  3, 15,  8, 16, 24,  6,  1,  7, 21, 19, 20, 13,  0,  5, 12, 10,
        2,  9, 14, 27, 22,  4, 11, 25, 18, 26, 23], dtype=int64), 'cur_cost': 17045.0, 'intermediate_solutions': [{'tour': array([26,  6, 11, 13,  0, 21, 24, 12, 10,  5, 14,  2, 20, 23,  3, 19,  1,
       16, 22,  8, 27,  9, 25,  7, 17,  4, 15, 18], dtype=int64), 'cur_cost': 22179.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([13, 26,  6, 11,  0, 21, 24, 12, 10,  5, 14,  2, 20, 23,  3, 19,  1,
       16, 22,  8, 27,  9, 25,  7, 17,  4, 15, 18], dtype=int64), 'cur_cost': 22094.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 0, 13, 26,  6, 11, 21, 24, 12, 10,  5, 14,  2, 20, 23,  3, 19,  1,
       16, 22,  8, 27,  9, 25,  7, 17,  4, 15, 18], dtype=int64), 'cur_cost': 22615.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([11, 13, 26,  6,  0, 21, 24, 12, 10,  5, 14,  2, 20, 23,  3, 19,  1,
       16, 22,  8, 27,  9, 25,  7, 17,  4, 15, 18], dtype=int64), 'cur_cost': 20121.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([11,  0, 13, 26,  6, 21, 24, 12, 10,  5, 14,  2, 20, 23,  3, 19,  1,
       16, 22,  8, 27,  9, 25,  7, 17,  4, 15, 18], dtype=int64), 'cur_cost': 22126.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [19, 14, 15, 16, 9, 24, 21, 13, 23, 22, 17, 27, 11, 26, 0, 8, 2, 5, 7, 1, 20, 25, 12, 10, 18, 4, 3, 6], 'cur_cost': 9647.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 21, 20, 24, 25, 26, 22, 23, 27, 19, 10, 14, 17, 16, 15, 9, 18, 12, 11, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3710.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:46,272 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:46,274 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:46,275 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=3677.000, 多样性=0.917
2025-08-05 10:28:46,275 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 10:28:46,275 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 10:28:46,275 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:46,275 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.10271766530838698, 'best_improvement': -0.16397594175371955}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.044665012406948076}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.02527550472229385, 'recent_improvements': [-0.02523317227325704, -0.0437099136266997, 0.025317837171330654], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 7, 'new_count': 7, 'count_change': 0, 'old_best_cost': 3055.0, 'new_best_cost': 3055.0, 'quality_improvement': 0.0, 'old_diversity': 0.6207482993197279, 'new_diversity': 0.6207482993197279, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 10:28:46,276 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 10:28:46,276 - __main__ - INFO - composite1_28 开始进化第 2 代
2025-08-05 10:28:46,276 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 10:28:46,277 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:46,277 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=3677.000, 多样性=0.917
2025-08-05 10:28:46,277 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:46,279 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.917
2025-08-05 10:28:46,279 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:46,281 - EliteExpert - INFO - 精英解分析完成: 精英解数量=7, 多样性=0.621
2025-08-05 10:28:46,282 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 10:28:46,282 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:46,283 - LandscapeExpert - INFO - 添加精英解数据: 7个精英解
2025-08-05 10:28:46,283 - LandscapeExpert - INFO - 数据提取成功: 17个路径, 17个适应度值
2025-08-05 10:28:46,306 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.412, 适应度梯度: -629.176, 聚类评分: 0.000, 覆盖率: 0.074, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:46,306 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 10:28:46,306 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:46,306 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite1_28
2025-08-05 10:28:46,311 - visualization.landscape_visualizer - INFO - 插值约束: 139 个点被约束到最小值 3055.00
2025-08-05 10:28:46,312 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=5.0%, 梯度: 726.01 → 689.39
2025-08-05 10:28:46,451 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite1_28\landscape_composite1_28_iter_62_20250805_102846.html
2025-08-05 10:28:46,550 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite1_28\dashboard_composite1_28_iter_62_20250805_102846.html
2025-08-05 10:28:46,551 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 62
2025-08-05 10:28:46,551 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 10:28:46,551 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2687秒
2025-08-05 10:28:46,551 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.4117647058823529, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -629.1764705882351, 'local_optima_density': 0.4117647058823529, 'gradient_variance': 22742939.665328715, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.074, 'fitness_entropy': 0.6892094263601618, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -629.176)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.074)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360926.3065474, 'performance_metrics': {}}}
2025-08-05 10:28:46,552 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:46,552 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:46,552 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:46,552 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:46,553 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:46,553 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 10:28:46,553 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:46,553 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:46,553 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:46,554 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:46,554 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:46,554 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:46,554 - experts.management.collaboration_manager - INFO - 识别精英个体: {9, 6} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:46,554 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:46,554 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:46,555 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:46,556 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 28
2025-08-05 10:28:46,556 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,557 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,557 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,557 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,557 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14878.0, 路径长度: 28, 收集中间解: 3
2025-08-05 10:28:46,557 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [1, 19, 24, 13, 15, 18, 17, 14, 21, 25, 10, 23, 9, 27, 3, 4, 12, 20, 0, 6, 22, 8, 5, 11, 16, 2, 26, 7], 'cur_cost': 14878.0, 'intermediate_solutions': [{'tour': [1, 11, 20, 13, 21, 22, 15, 14, 17, 25, 10, 23, 9, 27, 26, 8, 2, 5, 4, 6, 24, 0, 7, 3, 16, 18, 12, 19], 'cur_cost': 12162.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 11, 20, 13, 4, 5, 2, 8, 26, 27, 9, 23, 10, 25, 17, 14, 15, 22, 21, 6, 3, 0, 7, 24, 16, 18, 12, 19], 'cur_cost': 12211.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 11, 20, 13, 21, 22, 15, 14, 17, 25, 23, 9, 27, 26, 8, 2, 5, 4, 6, 3, 0, 7, 24, 10, 16, 18, 12, 19], 'cur_cost': 9570.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:46,558 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 14878.00)
2025-08-05 10:28:46,558 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:46,558 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:46,558 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:46,559 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 28
2025-08-05 10:28:46,559 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,559 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,559 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,559 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,560 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3317.0, 路径长度: 28, 收集中间解: 3
2025-08-05 10:28:46,560 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 16, 11, 12, 18, 15, 14, 17, 13, 10, 9, 21, 26, 24, 25, 23, 27, 19, 20, 22, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3317.0, 'intermediate_solutions': [{'tour': [0, 2, 24, 17, 14, 15, 16, 12, 18, 9, 11, 10, 13, 21, 26, 25, 23, 27, 19, 20, 22, 5, 7, 8, 3, 4, 6, 1], 'cur_cost': 3873.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 24, 17, 14, 15, 16, 12, 18, 9, 11, 10, 13, 21, 26, 25, 23, 27, 19, 20, 22, 8, 7, 4, 3, 5, 6, 1], 'cur_cost': 3792.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 24, 17, 14, 16, 12, 18, 9, 11, 10, 13, 15, 21, 26, 25, 23, 27, 19, 20, 22, 8, 7, 5, 3, 4, 6, 1], 'cur_cost': 3855.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:46,560 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 3317.00)
2025-08-05 10:28:46,560 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:46,560 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:46,560 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:46,561 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 28
2025-08-05 10:28:46,561 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,561 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,562 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,562 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,562 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11689.0, 路径长度: 28, 收集中间解: 3
2025-08-05 10:28:46,562 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [1, 16, 11, 13, 18, 15, 14, 17, 22, 25, 10, 21, 9, 27, 24, 23, 0, 19, 20, 6, 8, 12, 4, 3, 26, 2, 5, 7], 'cur_cost': 11689.0, 'intermediate_solutions': [{'tour': [0, 11, 20, 17, 21, 22, 16, 18, 23, 9, 27, 8, 5, 6, 3, 25, 14, 12, 19, 4, 15, 10, 1, 24, 13, 2, 26, 7], 'cur_cost': 16455.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 20, 17, 21, 22, 16, 18, 23, 9, 27, 8, 5, 6, 3, 25, 12, 14, 19, 4, 2, 13, 24, 1, 10, 15, 26, 7], 'cur_cost': 14915.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 17, 21, 22, 16, 18, 23, 9, 27, 8, 5, 6, 3, 25, 12, 14, 19, 4, 11, 15, 10, 1, 24, 13, 2, 26, 7], 'cur_cost': 15940.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:46,562 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 11689.00)
2025-08-05 10:28:46,562 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:28:46,562 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:46,563 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:46,563 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 17851.0
2025-08-05 10:28:46,578 - ExploitationExpert - INFO - res_population_num: 12
2025-08-05 10:28:46,579 - ExploitationExpert - INFO - res_population_costs: [3055.0, 3055, 3055, 3055.0, 3055, 3055, 3055, 3055.0, 3055, 3055, 3055, 3055]
2025-08-05 10:28:46,579 - ExploitationExpert - INFO - res_populations: [array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64)]
2025-08-05 10:28:46,582 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:46,582 - ExploitationExpert - INFO - populations: [{'tour': [1, 19, 24, 13, 15, 18, 17, 14, 21, 25, 10, 23, 9, 27, 3, 4, 12, 20, 0, 6, 22, 8, 5, 11, 16, 2, 26, 7], 'cur_cost': 14878.0}, {'tour': [0, 16, 11, 12, 18, 15, 14, 17, 13, 10, 9, 21, 26, 24, 25, 23, 27, 19, 20, 22, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3317.0}, {'tour': [1, 16, 11, 13, 18, 15, 14, 17, 22, 25, 10, 21, 9, 27, 24, 23, 0, 19, 20, 6, 8, 12, 4, 3, 26, 2, 5, 7], 'cur_cost': 11689.0}, {'tour': array([ 4, 26,  3,  1,  2, 10, 17, 16,  6, 18,  5,  7,  9, 24,  8, 12, 19,
        0, 27, 13, 21, 14, 20, 11, 22, 23, 25, 15], dtype=int64), 'cur_cost': 17851.0}, {'tour': [0, 19, 9, 20, 24, 25, 26, 22, 21, 23, 27, 10, 14, 17, 16, 15, 18, 12, 13, 11, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 4309.0}, {'tour': [19, 9, 20, 24, 15, 18, 17, 25, 10, 23, 5, 27, 8, 2, 14, 4, 6, 22, 0, 1, 11, 12, 7, 3, 13, 21, 26, 16], 'cur_cost': 16034.0}, {'tour': [0, 8, 21, 10, 14, 17, 16, 15, 9, 18, 12, 13, 11, 20, 24, 25, 26, 22, 23, 27, 19, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3677.0}, {'tour': [17, 3, 15, 8, 16, 24, 6, 1, 7, 21, 19, 20, 13, 0, 5, 12, 10, 2, 9, 14, 27, 22, 4, 11, 25, 18, 26, 23], 'cur_cost': 17045.0}, {'tour': [19, 14, 15, 16, 9, 24, 21, 13, 23, 22, 17, 27, 11, 26, 0, 8, 2, 5, 7, 1, 20, 25, 12, 10, 18, 4, 3, 6], 'cur_cost': 9647.0}, {'tour': [0, 13, 21, 20, 24, 25, 26, 22, 23, 27, 19, 10, 14, 17, 16, 15, 9, 18, 12, 11, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3710.0}]
2025-08-05 10:28:46,583 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:46,583 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 159, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 159, 'cache_hits': 0, 'similarity_calculations': 657, 'cache_hit_rate': 0.0, 'cache_size': 657}}
2025-08-05 10:28:46,583 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 4, 26,  3,  1,  2, 10, 17, 16,  6, 18,  5,  7,  9, 24,  8, 12, 19,
        0, 27, 13, 21, 14, 20, 11, 22, 23, 25, 15], dtype=int64), 'cur_cost': 17851.0, 'intermediate_solutions': [{'tour': array([25, 14,  3, 10, 19, 13, 26, 16, 22, 24,  9,  6, 23, 18, 21, 27,  0,
       20, 12,  2, 11, 15,  7,  8,  4,  1,  5, 17]), 'cur_cost': 17059.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([10, 25, 14,  3, 19, 13, 26, 16, 22, 24,  9,  6, 23, 18, 21, 27,  0,
       20, 12,  2, 11, 15,  7,  8,  4,  1,  5, 17]), 'cur_cost': 16514.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([19, 10, 25, 14,  3, 13, 26, 16, 22, 24,  9,  6, 23, 18, 21, 27,  0,
       20, 12,  2, 11, 15,  7,  8,  4,  1,  5, 17]), 'cur_cost': 17072.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 3, 10, 25, 14, 19, 13, 26, 16, 22, 24,  9,  6, 23, 18, 21, 27,  0,
       20, 12,  2, 11, 15,  7,  8,  4,  1,  5, 17]), 'cur_cost': 17071.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 3, 19, 10, 25, 14, 13, 26, 16, 22, 24,  9,  6, 23, 18, 21, 27,  0,
       20, 12,  2, 11, 15,  7,  8,  4,  1,  5, 17]), 'cur_cost': 16523.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:46,583 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 17851.00)
2025-08-05 10:28:46,584 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:46,584 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:46,584 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:46,584 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 28
2025-08-05 10:28:46,585 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,585 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,585 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,585 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,585 - ExplorationExpert - INFO - 探索路径生成完成，成本: 5318.0, 路径长度: 28, 收集中间解: 3
2025-08-05 10:28:46,585 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 18, 4, 15, 16, 17, 14, 11, 10, 13, 12, 9, 21, 26, 24, 25, 23, 27, 19, 20, 22, 8, 7, 5, 3, 2, 6, 1], 'cur_cost': 5318.0, 'intermediate_solutions': [{'tour': [0, 19, 9, 20, 10, 25, 26, 22, 21, 23, 27, 24, 14, 17, 16, 15, 18, 12, 13, 11, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 5323.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 9, 20, 24, 25, 26, 22, 21, 23, 27, 10, 14, 17, 16, 15, 18, 12, 13, 11, 8, 7, 6, 4, 3, 5, 1, 2], 'cur_cost': 4327.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 20, 24, 25, 26, 22, 21, 23, 27, 10, 14, 17, 9, 16, 15, 18, 12, 13, 11, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3247.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:46,586 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 5318.00)
2025-08-05 10:28:46,586 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:46,586 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:46,586 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:46,587 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 28
2025-08-05 10:28:46,587 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,588 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,588 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,588 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,588 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9186.0, 路径长度: 28, 收集中间解: 3
2025-08-05 10:28:46,588 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [15, 11, 18, 24, 21, 26, 27, 16, 12, 19, 14, 13, 25, 22, 8, 7, 0, 3, 4, 6, 20, 10, 23, 9, 5, 2, 1, 17], 'cur_cost': 9186.0, 'intermediate_solutions': [{'tour': [19, 9, 25, 24, 15, 18, 17, 20, 10, 23, 5, 27, 8, 2, 14, 4, 6, 22, 0, 1, 11, 12, 7, 3, 13, 21, 26, 16], 'cur_cost': 16007.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [19, 9, 20, 24, 15, 18, 17, 25, 10, 23, 5, 27, 8, 2, 14, 4, 22, 6, 0, 1, 11, 12, 7, 3, 13, 21, 26, 16], 'cur_cost': 16055.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [19, 23, 9, 20, 24, 15, 18, 17, 25, 10, 5, 27, 8, 2, 14, 4, 6, 22, 0, 1, 11, 12, 7, 3, 13, 21, 26, 16], 'cur_cost': 15504.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:46,588 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 9186.00)
2025-08-05 10:28:46,588 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:46,589 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:46,589 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:46,589 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 28
2025-08-05 10:28:46,589 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,590 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,590 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,590 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,590 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13933.0, 路径长度: 28, 收集中间解: 3
2025-08-05 10:28:46,590 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [15, 24, 12, 21, 18, 27, 11, 13, 10, 9, 23, 25, 22, 3, 4, 20, 0, 14, 8, 7, 5, 1, 16, 6, 2, 17, 19, 26], 'cur_cost': 13933.0, 'intermediate_solutions': [{'tour': [0, 8, 25, 10, 14, 17, 16, 15, 9, 18, 12, 13, 11, 20, 24, 21, 26, 22, 23, 27, 19, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3742.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 21, 10, 14, 17, 16, 15, 9, 18, 12, 13, 11, 20, 24, 25, 26, 22, 23, 27, 19, 7, 5, 3, 4, 6, 2, 1], 'cur_cost': 3679.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 21, 10, 14, 17, 16, 15, 9, 18, 12, 13, 11, 20, 24, 25, 26, 22, 23, 7, 27, 19, 5, 3, 4, 6, 1, 2], 'cur_cost': 5790.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:46,590 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 13933.00)
2025-08-05 10:28:46,590 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:28:46,591 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:46,591 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:46,591 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 18525.0
2025-08-05 10:28:46,602 - ExploitationExpert - INFO - res_population_num: 13
2025-08-05 10:28:46,602 - ExploitationExpert - INFO - res_population_costs: [3055.0, 3055, 3055, 3055.0, 3055, 3055, 3055, 3055.0, 3055, 3055, 3055, 3055, 3055]
2025-08-05 10:28:46,603 - ExploitationExpert - INFO - res_populations: [array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64)]
2025-08-05 10:28:46,608 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:46,608 - ExploitationExpert - INFO - populations: [{'tour': [1, 19, 24, 13, 15, 18, 17, 14, 21, 25, 10, 23, 9, 27, 3, 4, 12, 20, 0, 6, 22, 8, 5, 11, 16, 2, 26, 7], 'cur_cost': 14878.0}, {'tour': [0, 16, 11, 12, 18, 15, 14, 17, 13, 10, 9, 21, 26, 24, 25, 23, 27, 19, 20, 22, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3317.0}, {'tour': [1, 16, 11, 13, 18, 15, 14, 17, 22, 25, 10, 21, 9, 27, 24, 23, 0, 19, 20, 6, 8, 12, 4, 3, 26, 2, 5, 7], 'cur_cost': 11689.0}, {'tour': array([ 4, 26,  3,  1,  2, 10, 17, 16,  6, 18,  5,  7,  9, 24,  8, 12, 19,
        0, 27, 13, 21, 14, 20, 11, 22, 23, 25, 15], dtype=int64), 'cur_cost': 17851.0}, {'tour': [0, 18, 4, 15, 16, 17, 14, 11, 10, 13, 12, 9, 21, 26, 24, 25, 23, 27, 19, 20, 22, 8, 7, 5, 3, 2, 6, 1], 'cur_cost': 5318.0}, {'tour': [15, 11, 18, 24, 21, 26, 27, 16, 12, 19, 14, 13, 25, 22, 8, 7, 0, 3, 4, 6, 20, 10, 23, 9, 5, 2, 1, 17], 'cur_cost': 9186.0}, {'tour': [15, 24, 12, 21, 18, 27, 11, 13, 10, 9, 23, 25, 22, 3, 4, 20, 0, 14, 8, 7, 5, 1, 16, 6, 2, 17, 19, 26], 'cur_cost': 13933.0}, {'tour': array([22,  7,  2, 20,  5, 11, 14, 15, 26, 25,  9, 18,  3, 17,  0, 10,  6,
       12, 27, 23, 16, 19, 13,  8,  4, 21,  1, 24], dtype=int64), 'cur_cost': 18525.0}, {'tour': [19, 14, 15, 16, 9, 24, 21, 13, 23, 22, 17, 27, 11, 26, 0, 8, 2, 5, 7, 1, 20, 25, 12, 10, 18, 4, 3, 6], 'cur_cost': 9647.0}, {'tour': [0, 13, 21, 20, 24, 25, 26, 22, 23, 27, 19, 10, 14, 17, 16, 15, 9, 18, 12, 11, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3710.0}]
2025-08-05 10:28:46,609 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:46,610 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 160, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 160, 'cache_hits': 0, 'similarity_calculations': 660, 'cache_hit_rate': 0.0, 'cache_size': 660}}
2025-08-05 10:28:46,611 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([22,  7,  2, 20,  5, 11, 14, 15, 26, 25,  9, 18,  3, 17,  0, 10,  6,
       12, 27, 23, 16, 19, 13,  8,  4, 21,  1, 24], dtype=int64), 'cur_cost': 18525.0, 'intermediate_solutions': [{'tour': array([15,  3, 17,  8, 16, 24,  6,  1,  7, 21, 19, 20, 13,  0,  5, 12, 10,
        2,  9, 14, 27, 22,  4, 11, 25, 18, 26, 23]), 'cur_cost': 17069.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 8, 15,  3, 17, 16, 24,  6,  1,  7, 21, 19, 20, 13,  0,  5, 12, 10,
        2,  9, 14, 27, 22,  4, 11, 25, 18, 26, 23]), 'cur_cost': 16506.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([16,  8, 15,  3, 17, 24,  6,  1,  7, 21, 19, 20, 13,  0,  5, 12, 10,
        2,  9, 14, 27, 22,  4, 11, 25, 18, 26, 23]), 'cur_cost': 17045.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([17,  8, 15,  3, 16, 24,  6,  1,  7, 21, 19, 20, 13,  0,  5, 12, 10,
        2,  9, 14, 27, 22,  4, 11, 25, 18, 26, 23]), 'cur_cost': 17045.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([17, 16,  8, 15,  3, 24,  6,  1,  7, 21, 19, 20, 13,  0,  5, 12, 10,
        2,  9, 14, 27, 22,  4, 11, 25, 18, 26, 23]), 'cur_cost': 16495.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:46,611 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 18525.00)
2025-08-05 10:28:46,611 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:46,611 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:46,611 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:46,612 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 28
2025-08-05 10:28:46,613 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,613 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,613 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,613 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,614 - ExplorationExpert - INFO - 探索路径生成完成，成本: 5247.0, 路径长度: 28, 收集中间解: 3
2025-08-05 10:28:46,614 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 11, 5, 15, 16, 17, 14, 10, 13, 12, 18, 9, 21, 26, 24, 25, 23, 27, 19, 20, 22, 8, 7, 6, 1, 2, 3, 4], 'cur_cost': 5247.0, 'intermediate_solutions': [{'tour': [19, 16, 15, 14, 9, 24, 21, 13, 23, 22, 17, 27, 11, 26, 0, 8, 2, 5, 7, 1, 20, 25, 12, 10, 18, 4, 3, 6], 'cur_cost': 9661.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [19, 14, 15, 16, 9, 24, 21, 11, 27, 17, 22, 23, 13, 26, 0, 8, 2, 5, 7, 1, 20, 25, 12, 10, 18, 4, 3, 6], 'cur_cost': 9646.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [19, 14, 15, 16, 9, 24, 13, 23, 22, 17, 27, 11, 26, 0, 8, 2, 21, 5, 7, 1, 20, 25, 12, 10, 18, 4, 3, 6], 'cur_cost': 11767.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:46,614 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 5247.00)
2025-08-05 10:28:46,614 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:46,614 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:46,614 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:46,616 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 28
2025-08-05 10:28:46,616 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,617 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,617 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,617 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:46,617 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9231.0, 路径长度: 28, 收集中间解: 3
2025-08-05 10:28:46,617 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [7, 5, 6, 3, 9, 12, 22, 19, 10, 11, 16, 17, 15, 24, 21, 25, 14, 20, 18, 26, 13, 0, 1, 8, 2, 4, 27, 23], 'cur_cost': 9231.0, 'intermediate_solutions': [{'tour': [0, 13, 21, 20, 24, 25, 26, 22, 11, 27, 19, 10, 14, 17, 16, 15, 9, 18, 12, 23, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 5331.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 21, 20, 24, 25, 26, 22, 23, 27, 19, 10, 14, 17, 16, 15, 9, 18, 12, 11, 8, 7, 5, 3, 4, 6, 2, 1], 'cur_cost': 3712.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 21, 20, 24, 25, 26, 22, 23, 27, 19, 10, 14, 16, 17, 15, 9, 18, 12, 11, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3726.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:46,617 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 9231.00)
2025-08-05 10:28:46,617 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:46,618 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:46,619 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [1, 19, 24, 13, 15, 18, 17, 14, 21, 25, 10, 23, 9, 27, 3, 4, 12, 20, 0, 6, 22, 8, 5, 11, 16, 2, 26, 7], 'cur_cost': 14878.0, 'intermediate_solutions': [{'tour': [1, 11, 20, 13, 21, 22, 15, 14, 17, 25, 10, 23, 9, 27, 26, 8, 2, 5, 4, 6, 24, 0, 7, 3, 16, 18, 12, 19], 'cur_cost': 12162.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 11, 20, 13, 4, 5, 2, 8, 26, 27, 9, 23, 10, 25, 17, 14, 15, 22, 21, 6, 3, 0, 7, 24, 16, 18, 12, 19], 'cur_cost': 12211.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 11, 20, 13, 21, 22, 15, 14, 17, 25, 23, 9, 27, 26, 8, 2, 5, 4, 6, 3, 0, 7, 24, 10, 16, 18, 12, 19], 'cur_cost': 9570.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 11, 12, 18, 15, 14, 17, 13, 10, 9, 21, 26, 24, 25, 23, 27, 19, 20, 22, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3317.0, 'intermediate_solutions': [{'tour': [0, 2, 24, 17, 14, 15, 16, 12, 18, 9, 11, 10, 13, 21, 26, 25, 23, 27, 19, 20, 22, 5, 7, 8, 3, 4, 6, 1], 'cur_cost': 3873.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 24, 17, 14, 15, 16, 12, 18, 9, 11, 10, 13, 21, 26, 25, 23, 27, 19, 20, 22, 8, 7, 4, 3, 5, 6, 1], 'cur_cost': 3792.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 24, 17, 14, 16, 12, 18, 9, 11, 10, 13, 15, 21, 26, 25, 23, 27, 19, 20, 22, 8, 7, 5, 3, 4, 6, 1], 'cur_cost': 3855.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [1, 16, 11, 13, 18, 15, 14, 17, 22, 25, 10, 21, 9, 27, 24, 23, 0, 19, 20, 6, 8, 12, 4, 3, 26, 2, 5, 7], 'cur_cost': 11689.0, 'intermediate_solutions': [{'tour': [0, 11, 20, 17, 21, 22, 16, 18, 23, 9, 27, 8, 5, 6, 3, 25, 14, 12, 19, 4, 15, 10, 1, 24, 13, 2, 26, 7], 'cur_cost': 16455.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 20, 17, 21, 22, 16, 18, 23, 9, 27, 8, 5, 6, 3, 25, 12, 14, 19, 4, 2, 13, 24, 1, 10, 15, 26, 7], 'cur_cost': 14915.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 17, 21, 22, 16, 18, 23, 9, 27, 8, 5, 6, 3, 25, 12, 14, 19, 4, 11, 15, 10, 1, 24, 13, 2, 26, 7], 'cur_cost': 15940.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 4, 26,  3,  1,  2, 10, 17, 16,  6, 18,  5,  7,  9, 24,  8, 12, 19,
        0, 27, 13, 21, 14, 20, 11, 22, 23, 25, 15], dtype=int64), 'cur_cost': 17851.0, 'intermediate_solutions': [{'tour': array([25, 14,  3, 10, 19, 13, 26, 16, 22, 24,  9,  6, 23, 18, 21, 27,  0,
       20, 12,  2, 11, 15,  7,  8,  4,  1,  5, 17]), 'cur_cost': 17059.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([10, 25, 14,  3, 19, 13, 26, 16, 22, 24,  9,  6, 23, 18, 21, 27,  0,
       20, 12,  2, 11, 15,  7,  8,  4,  1,  5, 17]), 'cur_cost': 16514.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([19, 10, 25, 14,  3, 13, 26, 16, 22, 24,  9,  6, 23, 18, 21, 27,  0,
       20, 12,  2, 11, 15,  7,  8,  4,  1,  5, 17]), 'cur_cost': 17072.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 3, 10, 25, 14, 19, 13, 26, 16, 22, 24,  9,  6, 23, 18, 21, 27,  0,
       20, 12,  2, 11, 15,  7,  8,  4,  1,  5, 17]), 'cur_cost': 17071.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 3, 19, 10, 25, 14, 13, 26, 16, 22, 24,  9,  6, 23, 18, 21, 27,  0,
       20, 12,  2, 11, 15,  7,  8,  4,  1,  5, 17]), 'cur_cost': 16523.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 4, 15, 16, 17, 14, 11, 10, 13, 12, 9, 21, 26, 24, 25, 23, 27, 19, 20, 22, 8, 7, 5, 3, 2, 6, 1], 'cur_cost': 5318.0, 'intermediate_solutions': [{'tour': [0, 19, 9, 20, 10, 25, 26, 22, 21, 23, 27, 24, 14, 17, 16, 15, 18, 12, 13, 11, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 5323.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 9, 20, 24, 25, 26, 22, 21, 23, 27, 10, 14, 17, 16, 15, 18, 12, 13, 11, 8, 7, 6, 4, 3, 5, 1, 2], 'cur_cost': 4327.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 20, 24, 25, 26, 22, 21, 23, 27, 10, 14, 17, 9, 16, 15, 18, 12, 13, 11, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3247.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [15, 11, 18, 24, 21, 26, 27, 16, 12, 19, 14, 13, 25, 22, 8, 7, 0, 3, 4, 6, 20, 10, 23, 9, 5, 2, 1, 17], 'cur_cost': 9186.0, 'intermediate_solutions': [{'tour': [19, 9, 25, 24, 15, 18, 17, 20, 10, 23, 5, 27, 8, 2, 14, 4, 6, 22, 0, 1, 11, 12, 7, 3, 13, 21, 26, 16], 'cur_cost': 16007.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [19, 9, 20, 24, 15, 18, 17, 25, 10, 23, 5, 27, 8, 2, 14, 4, 22, 6, 0, 1, 11, 12, 7, 3, 13, 21, 26, 16], 'cur_cost': 16055.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [19, 23, 9, 20, 24, 15, 18, 17, 25, 10, 5, 27, 8, 2, 14, 4, 6, 22, 0, 1, 11, 12, 7, 3, 13, 21, 26, 16], 'cur_cost': 15504.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [15, 24, 12, 21, 18, 27, 11, 13, 10, 9, 23, 25, 22, 3, 4, 20, 0, 14, 8, 7, 5, 1, 16, 6, 2, 17, 19, 26], 'cur_cost': 13933.0, 'intermediate_solutions': [{'tour': [0, 8, 25, 10, 14, 17, 16, 15, 9, 18, 12, 13, 11, 20, 24, 21, 26, 22, 23, 27, 19, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3742.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 21, 10, 14, 17, 16, 15, 9, 18, 12, 13, 11, 20, 24, 25, 26, 22, 23, 27, 19, 7, 5, 3, 4, 6, 2, 1], 'cur_cost': 3679.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 21, 10, 14, 17, 16, 15, 9, 18, 12, 13, 11, 20, 24, 25, 26, 22, 23, 7, 27, 19, 5, 3, 4, 6, 1, 2], 'cur_cost': 5790.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([22,  7,  2, 20,  5, 11, 14, 15, 26, 25,  9, 18,  3, 17,  0, 10,  6,
       12, 27, 23, 16, 19, 13,  8,  4, 21,  1, 24], dtype=int64), 'cur_cost': 18525.0, 'intermediate_solutions': [{'tour': array([15,  3, 17,  8, 16, 24,  6,  1,  7, 21, 19, 20, 13,  0,  5, 12, 10,
        2,  9, 14, 27, 22,  4, 11, 25, 18, 26, 23]), 'cur_cost': 17069.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 8, 15,  3, 17, 16, 24,  6,  1,  7, 21, 19, 20, 13,  0,  5, 12, 10,
        2,  9, 14, 27, 22,  4, 11, 25, 18, 26, 23]), 'cur_cost': 16506.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([16,  8, 15,  3, 17, 24,  6,  1,  7, 21, 19, 20, 13,  0,  5, 12, 10,
        2,  9, 14, 27, 22,  4, 11, 25, 18, 26, 23]), 'cur_cost': 17045.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([17,  8, 15,  3, 16, 24,  6,  1,  7, 21, 19, 20, 13,  0,  5, 12, 10,
        2,  9, 14, 27, 22,  4, 11, 25, 18, 26, 23]), 'cur_cost': 17045.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([17, 16,  8, 15,  3, 24,  6,  1,  7, 21, 19, 20, 13,  0,  5, 12, 10,
        2,  9, 14, 27, 22,  4, 11, 25, 18, 26, 23]), 'cur_cost': 16495.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 5, 15, 16, 17, 14, 10, 13, 12, 18, 9, 21, 26, 24, 25, 23, 27, 19, 20, 22, 8, 7, 6, 1, 2, 3, 4], 'cur_cost': 5247.0, 'intermediate_solutions': [{'tour': [19, 16, 15, 14, 9, 24, 21, 13, 23, 22, 17, 27, 11, 26, 0, 8, 2, 5, 7, 1, 20, 25, 12, 10, 18, 4, 3, 6], 'cur_cost': 9661.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [19, 14, 15, 16, 9, 24, 21, 11, 27, 17, 22, 23, 13, 26, 0, 8, 2, 5, 7, 1, 20, 25, 12, 10, 18, 4, 3, 6], 'cur_cost': 9646.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [19, 14, 15, 16, 9, 24, 13, 23, 22, 17, 27, 11, 26, 0, 8, 2, 21, 5, 7, 1, 20, 25, 12, 10, 18, 4, 3, 6], 'cur_cost': 11767.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [7, 5, 6, 3, 9, 12, 22, 19, 10, 11, 16, 17, 15, 24, 21, 25, 14, 20, 18, 26, 13, 0, 1, 8, 2, 4, 27, 23], 'cur_cost': 9231.0, 'intermediate_solutions': [{'tour': [0, 13, 21, 20, 24, 25, 26, 22, 11, 27, 19, 10, 14, 17, 16, 15, 9, 18, 12, 23, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 5331.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 21, 20, 24, 25, 26, 22, 23, 27, 19, 10, 14, 17, 16, 15, 9, 18, 12, 11, 8, 7, 5, 3, 4, 6, 2, 1], 'cur_cost': 3712.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 21, 20, 24, 25, 26, 22, 23, 27, 19, 10, 14, 16, 17, 15, 9, 18, 12, 11, 8, 7, 5, 3, 4, 6, 1, 2], 'cur_cost': 3726.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:46,619 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:46,619 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:46,621 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=3317.000, 多样性=0.921
2025-08-05 10:28:46,621 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 10:28:46,621 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 10:28:46,621 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:46,623 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.018359234995362125, 'best_improvement': 0.09790590155017677}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.004329004329004314}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.07321378946754334, 'recent_improvements': [-0.0437099136266997, 0.025317837171330654, 0.10271766530838698], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 13, 'new_count': 13, 'count_change': 0, 'old_best_cost': 3055.0, 'new_best_cost': 3055.0, 'quality_improvement': 0.0, 'old_diversity': 0.6547619047619048, 'new_diversity': 0.6547619047619048, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 10:28:46,625 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 10:28:46,625 - __main__ - INFO - composite1_28 开始进化第 3 代
2025-08-05 10:28:46,625 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 10:28:46,625 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:46,626 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=3317.000, 多样性=0.921
2025-08-05 10:28:46,626 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:46,627 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.921
2025-08-05 10:28:46,627 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:46,631 - EliteExpert - INFO - 精英解分析完成: 精英解数量=13, 多样性=0.655
2025-08-05 10:28:46,632 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 10:28:46,632 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:46,632 - LandscapeExpert - INFO - 添加精英解数据: 13个精英解
2025-08-05 10:28:46,633 - LandscapeExpert - INFO - 数据提取成功: 23个路径, 23个适应度值
2025-08-05 10:28:46,673 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.565, 适应度梯度: -2271.435, 聚类评分: 0.000, 覆盖率: 0.076, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:46,674 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 10:28:46,674 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:46,674 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite1_28
2025-08-05 10:28:46,681 - visualization.landscape_visualizer - INFO - 插值约束: 55 个点被约束到最小值 3055.00
2025-08-05 10:28:46,683 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=5.9%, 梯度: 670.69 → 630.89
