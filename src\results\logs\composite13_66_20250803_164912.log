2025-08-03 16:49:12,063 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-08-03 16:49:12,064 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-03 16:49:12,066 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:49:12,078 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=9859.000, 多样性=0.975
2025-08-03 16:49:12,085 - PathExpert - INFO - 开始路径结构分析
2025-08-03 16:49:12,092 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.975
2025-08-03 16:49:12,151 - EliteExpert - INFO - 开始精英解分析
2025-08-03 16:49:12,154 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/1)
2025-08-03 16:49:12,155 - LandscapeExpert - INFO - 使用直接传入的种群数据: 20个个体
2025-08-03 16:49:12,155 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-03 16:49:12,156 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-03 16:49:12,419 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.150, 适应度梯度: -2157.480, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 1.000
2025-08-03 16:49:12,419 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-03 16:49:12,420 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-03 16:49:12,488 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记
2025-08-03 16:49:12,841 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_1_20250803_164912.html
2025-08-03 16:49:12,894 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_1_20250803_164912.html
2025-08-03 16:49:12,895 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-03 16:49:12,896 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-03 16:49:12,896 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.7414秒
2025-08-03 16:49:12,897 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-03 16:49:12,897 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.15, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -2157.4799999999996, 'local_optima_density': 0.15, 'gradient_variance': 2501006016.9575996, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0019, 'fitness_entropy': 0.887092015535129, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -2157.480)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 1, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754210952.4197714, 'performance_metrics': {}}}
2025-08-03 16:49:12,898 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-03 16:49:12,899 - experts.strategy.enhanced_strategy_expert - INFO - 开始增强策略分配分析 (迭代 0)
2025-08-03 16:49:12,899 - experts.analysis.individual_state_analyzer - INFO - 开始分析种群状态 (迭代 0, 种群大小: 20)
2025-08-03 16:49:12,899 - experts.strategy.enhanced_strategy_expert - ERROR - 策略分配过程中发生错误: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:49:12,899 - experts.strategy.enhanced_strategy_expert - WARNING - 使用回退策略分配
2025-08-03 16:49:12,900 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 16:49:12,900 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:49:12,900 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-03 16:49:12,900 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 16:49:12,901 - __main__ - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:49:12,901 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-03 16:49:12,901 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 2, 4, 5} (总数: 4, 保护比例: 0.20)
2025-08-03 16:49:12,902 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-03 16:49:12,902 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-03 16:49:12,902 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:49:12,921 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:49:12,922 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:49:13,123 - ExplorationExpert - INFO - 探索路径生成完成，成本: 60086.0, 路径长度: 66
2025-08-03 16:49:13,124 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [21, 17, 5, 53, 3, 12, 6, 23, 1, 11, 64, 10, 18, 28, 14, 24, 26, 29, 7, 13, 16, 15, 35, 20, 33, 37, 49, 42, 51, 50, 36, 30, 27, 43, 22, 46, 31, 45, 25, 47, 44, 19, 40, 9, 56, 60, 52, 58, 54, 59, 61, 8, 4, 55, 65, 62, 0, 57, 63, 2, 32, 48, 39, 41, 38, 34], 'cur_cost': 60086.0}
2025-08-03 16:49:13,124 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 60086.00)
2025-08-03 16:49:13,125 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-03 16:49:13,125 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:49:13,127 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:49:13,129 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 98459.0
2025-08-03 16:49:15,092 - ExploitationExpert - INFO - res_population_num: 1
2025-08-03 16:49:15,093 - ExploitationExpert - INFO - res_population_costs: [9857.0]
2025-08-03 16:49:15,093 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24, 29, 32, 34, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 63, 52, 65, 54, 57,
       64, 60, 58, 56, 59, 62, 53, 61, 55,  5,  4,  6,  2,  8, 10],
      dtype=int64)]
2025-08-03 16:49:15,094 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:49:15,095 - ExploitationExpert - INFO - populations: [{'tour': [21, 17, 5, 53, 3, 12, 6, 23, 1, 11, 64, 10, 18, 28, 14, 24, 26, 29, 7, 13, 16, 15, 35, 20, 33, 37, 49, 42, 51, 50, 36, 30, 27, 43, 22, 46, 31, 45, 25, 47, 44, 19, 40, 9, 56, 60, 52, 58, 54, 59, 61, 8, 4, 55, 65, 62, 0, 57, 63, 2, 32, 48, 39, 41, 38, 34], 'cur_cost': 60086.0}, {'tour': array([17, 12, 31, 27, 60,  7, 41,  8,  6, 10, 23,  2, 56, 54, 15, 30,  9,
       50, 39, 58, 45, 53,  4, 20, 24,  1, 42, 37, 21, 65, 46, 63, 11, 48,
       25, 29, 36, 26, 55, 16, 19, 13, 51, 49, 32,  5, 38, 14, 34, 35, 44,
       47, 61, 22, 33, 64, 57,  0, 62, 18, 52, 43, 40,  3, 59, 28],
      dtype=int64), 'cur_cost': 98459.0}, {'tour': array([43, 40, 49, 47, 46, 48, 39, 44, 45, 38, 51, 50, 41, 42, 21, 20, 13,
       23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30,
       34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,  2,  6, 10,
        0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63],
      dtype=int64), 'cur_cost': 9971.0}, {'tour': array([55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63,  2,  8,  5,
        4,  6,  9, 11,  7,  3,  1,  0, 10, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29,
       32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42],
      dtype=int64), 'cur_cost': 10045.0}, {'tour': array([34, 35, 28, 30, 32, 33, 25, 26, 36, 37, 31, 24, 29, 27, 18, 16, 23,
       22, 12, 17, 15, 14, 20, 21, 13, 19,  9, 11,  7,  3,  1,  0, 10,  8,
        2,  6,  4,  5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9863.0}, {'tour': array([35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 36, 27, 18, 16, 23,
       22, 12, 17, 15, 14, 20, 21, 13, 19,  9, 11,  7,  3,  1,  0, 10,  8,
        2,  6,  4,  5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9859.0}, {'tour': array([44, 21, 58, 40, 39, 11, 25, 53, 26, 46, 60,  0,  9, 14, 13, 63, 59,
       64, 45, 41, 32, 24, 51, 30, 12, 49, 19, 17,  4, 48, 28,  3,  5, 29,
       65, 34, 61, 57, 16,  8, 35, 52, 27, 62,  7, 18, 10, 22,  6, 33, 20,
       47, 54, 50, 43, 38, 15, 42, 36, 31, 56,  2, 37, 55, 23,  1],
      dtype=int64), 'cur_cost': 108092.0}, {'tour': array([33,  0, 59, 55, 20, 12, 65,  5, 32, 40, 24, 46, 19, 52, 50, 31, 39,
       26,  8, 21, 16, 41, 62, 44, 18, 64, 17,  6, 38, 22,  2, 57, 27, 63,
        7, 54, 60, 25, 53, 35, 15, 61, 23,  3, 10,  9,  1, 37, 49, 11, 13,
        4, 45, 56, 51, 34, 30, 29, 43, 48, 36, 14, 28, 42, 47, 58],
      dtype=int64), 'cur_cost': 109572.0}, {'tour': array([39, 49, 45, 17, 61, 24,  2,  1, 35, 27, 65, 38, 60, 16, 29, 15, 22,
        7, 48,  5, 31, 46, 57,  6, 21, 53, 43, 28, 47,  8, 20, 23, 11, 34,
        3, 19, 42, 18, 10, 51, 55, 63, 32, 52, 36, 64, 30, 56, 41, 25, 58,
       50, 44, 54,  4, 62, 12, 13, 33, 37, 26,  9, 59,  0, 14, 40],
      dtype=int64), 'cur_cost': 111235.0}, {'tour': array([44, 38, 16, 10, 54,  2, 65, 55, 12, 20, 15, 17, 41, 45, 39, 28,  0,
       52,  1, 64, 56, 48, 37, 49, 25, 26, 62, 47, 14, 33, 30, 35, 31, 27,
       59,  4, 53, 13, 36,  8, 18,  9, 19, 60, 61, 24, 42,  5, 46, 63, 23,
        3, 43, 58, 34, 40, 32, 29, 50,  6, 51, 11, 21, 57, 22,  7],
      dtype=int64), 'cur_cost': 97529.0}, {'tour': array([11, 32, 23, 24, 13, 16, 45, 61, 43, 34, 51,  7, 55, 54, 64,  3, 46,
       63, 33, 59,  8, 52, 60, 26, 19, 14,  6, 31, 28, 21, 48, 56, 42,  5,
       65, 27, 39, 30, 40, 12, 25, 57, 44, 20,  0, 47, 41, 50, 22, 10, 37,
        2, 35, 58, 38, 18,  1,  9, 17, 15,  4, 29, 53, 49, 62, 36],
      dtype=int64), 'cur_cost': 112191.0}, {'tour': array([46, 20, 45, 58, 34,  3, 30,  9, 15, 17, 55,  5, 42, 40,  0, 26,  7,
       38, 14, 47, 64, 22, 21, 51, 41, 43, 16, 48, 19, 11,  1, 49, 29, 63,
       59, 57, 52, 13, 27, 33, 60, 61, 12, 10, 37, 62, 56, 24, 35, 18, 36,
       54,  4, 44, 39, 32, 50, 65, 53, 28,  8, 31, 25,  2,  6, 23],
      dtype=int64), 'cur_cost': 97553.0}, {'tour': array([47, 57, 33, 64, 16, 61, 38, 32, 58, 56, 60,  5, 11,  9, 22, 43, 42,
        7, 18,  6, 34, 45, 52, 46, 10, 29,  1, 37, 30, 21, 17,  0, 63, 27,
       41, 31, 28,  2, 50, 13, 20,  3, 40, 65, 62, 25,  8,  4, 59, 23, 48,
       39, 19, 44, 35, 12, 15, 36, 24, 26, 14, 54, 51, 55, 53, 49],
      dtype=int64), 'cur_cost': 99941.0}, {'tour': array([15, 48, 43, 64, 62, 26, 46, 21,  2,  1, 38,  7, 16,  6, 12, 65, 22,
       52, 51, 55, 11, 17, 19, 57, 23, 14, 28, 61, 37, 53, 60, 49, 58, 54,
       50, 30, 47, 29, 45,  0, 56, 10, 40, 24, 27, 44, 33, 18,  4,  3, 32,
       31, 35, 41, 36,  9, 25,  8, 34, 42, 59, 20, 63, 13,  5, 39],
      dtype=int64), 'cur_cost': 112063.0}, {'tour': array([32,  4, 13, 17, 35, 64, 15, 25, 41, 48,  9, 46,  0, 34, 57, 24, 26,
       40,  7, 59, 45, 39, 43, 47, 37, 38, 14, 28, 10, 51, 58,  8, 54, 49,
        6, 63, 16, 30, 60, 18, 55, 36, 31, 62, 23, 56, 20,  5,  3, 29, 50,
       33,  1, 44, 12, 65, 52, 21, 19, 22, 27, 42, 61, 53, 11,  2],
      dtype=int64), 'cur_cost': 108338.0}, {'tour': array([64,  5, 41, 13, 23, 32, 40, 36,  0, 42, 39, 24, 47, 61, 35, 26, 16,
        7, 63, 33, 27, 10, 20,  2,  4, 21, 58, 56, 38,  8,  3,  6, 55, 34,
       17, 54, 15, 52,  1, 57, 45, 29, 18, 50, 43, 11, 37, 22, 51, 62, 65,
       44, 60, 25, 49,  9, 31, 12, 59, 46, 48, 53, 30, 14, 19, 28],
      dtype=int64), 'cur_cost': 108083.0}, {'tour': array([33, 42, 56, 16,  6, 46, 63, 24, 28, 29,  1, 34, 40,  7, 32, 64, 21,
        8, 20, 51, 15, 13, 12, 61, 30, 14,  4, 49, 31, 26,  5, 45,  3, 57,
       58, 48, 65, 47, 39, 59, 25, 22, 60, 37, 55, 44, 19, 54,  2, 10,  9,
       17, 11, 27, 23, 35, 18, 41, 36,  0, 38, 53, 62, 52, 43, 50],
      dtype=int64), 'cur_cost': 112101.0}, {'tour': array([62, 59, 10, 25, 14, 23, 53, 11, 22,  0, 32, 64, 20,  4, 51, 38, 37,
       41, 39, 65, 34, 36, 57, 55, 28, 46,  6, 54, 56, 26, 49, 52, 27, 19,
       24, 44,  5, 31, 17, 33, 40, 35, 18, 61,  2, 47, 45, 13, 60, 43, 15,
        3,  1,  8, 21, 42, 30, 16,  7, 29, 58, 50,  9, 48, 12, 63],
      dtype=int64), 'cur_cost': 107440.0}, {'tour': array([21, 63, 33, 31, 16, 54,  6, 12, 30,  9, 22,  1, 49,  5, 14, 20, 32,
       36, 47, 38, 24, 48,  4, 58, 15, 18, 17, 40, 23, 13, 46, 26, 65, 28,
        0, 43, 41, 34, 64, 44,  7, 10, 60, 27,  8,  3, 29, 35, 39, 52, 57,
       25, 55, 37, 62, 56, 59, 42,  2, 11, 19, 61, 50, 53, 45, 51],
      dtype=int64), 'cur_cost': 106193.0}, {'tour': array([17, 19,  4, 35, 60, 41, 30, 32, 18, 27, 28,  6, 15, 25, 40, 53, 64,
       42, 11, 47, 21, 45, 49, 10, 52, 39,  3, 37, 44, 31, 61, 46, 63, 48,
       29, 50, 51, 24, 59,  7,  5, 38,  2, 54, 12, 20,  9, 36, 43,  8, 62,
       16, 65, 57, 58, 33, 22, 56,  1, 14, 55, 13, 34, 26, 23,  0],
      dtype=int64), 'cur_cost': 108782.0}]
2025-08-03 16:49:15,105 - ExploitationExpert - INFO - 局部搜索耗时: 1.98秒
2025-08-03 16:49:15,106 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-03 16:49:15,106 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([17, 12, 31, 27, 60,  7, 41,  8,  6, 10, 23,  2, 56, 54, 15, 30,  9,
       50, 39, 58, 45, 53,  4, 20, 24,  1, 42, 37, 21, 65, 46, 63, 11, 48,
       25, 29, 36, 26, 55, 16, 19, 13, 51, 49, 32,  5, 38, 14, 34, 35, 44,
       47, 61, 22, 33, 64, 57,  0, 62, 18, 52, 43, 40,  3, 59, 28],
      dtype=int64), 'cur_cost': 98459.0}
2025-08-03 16:49:15,107 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 98459.00)
2025-08-03 16:49:15,107 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-03 16:49:15,107 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-03 16:49:15,108 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:49:15,115 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:49:15,117 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:49:15,118 - ExplorationExpert - INFO - 探索路径生成完成，成本: 105174.0, 路径长度: 66
2025-08-03 16:49:15,119 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [53, 29, 13, 33, 43, 22, 47, 55, 27, 2, 32, 1, 17, 19, 18, 45, 0, 52, 11, 15, 36, 65, 23, 63, 58, 21, 24, 4, 34, 42, 16, 48, 10, 56, 39, 51, 28, 31, 12, 44, 60, 30, 59, 3, 9, 38, 41, 20, 37, 40, 49, 54, 26, 35, 50, 64, 6, 7, 46, 8, 62, 5, 61, 57, 14, 25], 'cur_cost': 105174.0}
2025-08-03 16:49:15,120 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 105174.00)
2025-08-03 16:49:15,120 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-03 16:49:15,120 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-03 16:49:15,121 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:49:15,127 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:49:15,127 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:49:15,128 - ExplorationExpert - INFO - 探索路径生成完成，成本: 102337.0, 路径长度: 66
2025-08-03 16:49:15,129 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [53, 47, 27, 37, 34, 50, 56, 45, 44, 9, 38, 61, 8, 55, 0, 63, 64, 17, 14, 3, 40, 29, 13, 10, 42, 51, 22, 2, 62, 21, 26, 52, 20, 12, 46, 57, 60, 4, 30, 41, 54, 58, 32, 35, 24, 23, 39, 1, 16, 59, 19, 36, 7, 33, 18, 11, 28, 48, 31, 6, 15, 43, 49, 65, 5, 25], 'cur_cost': 102337.0}
2025-08-03 16:49:15,130 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 102337.00)
2025-08-03 16:49:15,130 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-03 16:49:15,130 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:49:15,131 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:49:15,131 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 102389.0
2025-08-03 16:49:17,390 - ExploitationExpert - INFO - res_population_num: 2
2025-08-03 16:49:17,390 - ExploitationExpert - INFO - res_population_costs: [9857.0, 9610.0]
2025-08-03 16:49:17,390 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24, 29, 32, 34, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 63, 52, 65, 54, 57,
       64, 60, 58, 56, 59, 62, 53, 61, 55,  5,  4,  6,  2,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28, 30, 35, 34, 40, 43,
       48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59,
       56, 58, 60, 54, 57, 64, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64)]
2025-08-03 16:49:17,391 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:49:17,391 - ExploitationExpert - INFO - populations: [{'tour': [21, 17, 5, 53, 3, 12, 6, 23, 1, 11, 64, 10, 18, 28, 14, 24, 26, 29, 7, 13, 16, 15, 35, 20, 33, 37, 49, 42, 51, 50, 36, 30, 27, 43, 22, 46, 31, 45, 25, 47, 44, 19, 40, 9, 56, 60, 52, 58, 54, 59, 61, 8, 4, 55, 65, 62, 0, 57, 63, 2, 32, 48, 39, 41, 38, 34], 'cur_cost': 60086.0}, {'tour': array([17, 12, 31, 27, 60,  7, 41,  8,  6, 10, 23,  2, 56, 54, 15, 30,  9,
       50, 39, 58, 45, 53,  4, 20, 24,  1, 42, 37, 21, 65, 46, 63, 11, 48,
       25, 29, 36, 26, 55, 16, 19, 13, 51, 49, 32,  5, 38, 14, 34, 35, 44,
       47, 61, 22, 33, 64, 57,  0, 62, 18, 52, 43, 40,  3, 59, 28],
      dtype=int64), 'cur_cost': 98459.0}, {'tour': [53, 29, 13, 33, 43, 22, 47, 55, 27, 2, 32, 1, 17, 19, 18, 45, 0, 52, 11, 15, 36, 65, 23, 63, 58, 21, 24, 4, 34, 42, 16, 48, 10, 56, 39, 51, 28, 31, 12, 44, 60, 30, 59, 3, 9, 38, 41, 20, 37, 40, 49, 54, 26, 35, 50, 64, 6, 7, 46, 8, 62, 5, 61, 57, 14, 25], 'cur_cost': 105174.0}, {'tour': [53, 47, 27, 37, 34, 50, 56, 45, 44, 9, 38, 61, 8, 55, 0, 63, 64, 17, 14, 3, 40, 29, 13, 10, 42, 51, 22, 2, 62, 21, 26, 52, 20, 12, 46, 57, 60, 4, 30, 41, 54, 58, 32, 35, 24, 23, 39, 1, 16, 59, 19, 36, 7, 33, 18, 11, 28, 48, 31, 6, 15, 43, 49, 65, 5, 25], 'cur_cost': 102337.0}, {'tour': array([43, 61, 26, 20, 37, 12, 53, 33,  6, 65,  9, 27, 59, 24, 28, 57, 11,
        4, 41, 40, 55, 16, 42, 39,  1, 60, 52, 63, 10, 47, 58,  0, 38, 50,
       21, 22,  8, 13,  3,  2, 18, 23, 19, 51, 25, 32, 35, 46, 62, 44, 36,
       31, 48, 30,  5, 34, 56, 45,  7, 64, 15, 29, 14, 54, 49, 17],
      dtype=int64), 'cur_cost': 102389.0}, {'tour': array([35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 36, 27, 18, 16, 23,
       22, 12, 17, 15, 14, 20, 21, 13, 19,  9, 11,  7,  3,  1,  0, 10,  8,
        2,  6,  4,  5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9859.0}, {'tour': array([44, 21, 58, 40, 39, 11, 25, 53, 26, 46, 60,  0,  9, 14, 13, 63, 59,
       64, 45, 41, 32, 24, 51, 30, 12, 49, 19, 17,  4, 48, 28,  3,  5, 29,
       65, 34, 61, 57, 16,  8, 35, 52, 27, 62,  7, 18, 10, 22,  6, 33, 20,
       47, 54, 50, 43, 38, 15, 42, 36, 31, 56,  2, 37, 55, 23,  1],
      dtype=int64), 'cur_cost': 108092.0}, {'tour': array([33,  0, 59, 55, 20, 12, 65,  5, 32, 40, 24, 46, 19, 52, 50, 31, 39,
       26,  8, 21, 16, 41, 62, 44, 18, 64, 17,  6, 38, 22,  2, 57, 27, 63,
        7, 54, 60, 25, 53, 35, 15, 61, 23,  3, 10,  9,  1, 37, 49, 11, 13,
        4, 45, 56, 51, 34, 30, 29, 43, 48, 36, 14, 28, 42, 47, 58],
      dtype=int64), 'cur_cost': 109572.0}, {'tour': array([39, 49, 45, 17, 61, 24,  2,  1, 35, 27, 65, 38, 60, 16, 29, 15, 22,
        7, 48,  5, 31, 46, 57,  6, 21, 53, 43, 28, 47,  8, 20, 23, 11, 34,
        3, 19, 42, 18, 10, 51, 55, 63, 32, 52, 36, 64, 30, 56, 41, 25, 58,
       50, 44, 54,  4, 62, 12, 13, 33, 37, 26,  9, 59,  0, 14, 40],
      dtype=int64), 'cur_cost': 111235.0}, {'tour': array([44, 38, 16, 10, 54,  2, 65, 55, 12, 20, 15, 17, 41, 45, 39, 28,  0,
       52,  1, 64, 56, 48, 37, 49, 25, 26, 62, 47, 14, 33, 30, 35, 31, 27,
       59,  4, 53, 13, 36,  8, 18,  9, 19, 60, 61, 24, 42,  5, 46, 63, 23,
        3, 43, 58, 34, 40, 32, 29, 50,  6, 51, 11, 21, 57, 22,  7],
      dtype=int64), 'cur_cost': 97529.0}, {'tour': array([11, 32, 23, 24, 13, 16, 45, 61, 43, 34, 51,  7, 55, 54, 64,  3, 46,
       63, 33, 59,  8, 52, 60, 26, 19, 14,  6, 31, 28, 21, 48, 56, 42,  5,
       65, 27, 39, 30, 40, 12, 25, 57, 44, 20,  0, 47, 41, 50, 22, 10, 37,
        2, 35, 58, 38, 18,  1,  9, 17, 15,  4, 29, 53, 49, 62, 36],
      dtype=int64), 'cur_cost': 112191.0}, {'tour': array([46, 20, 45, 58, 34,  3, 30,  9, 15, 17, 55,  5, 42, 40,  0, 26,  7,
       38, 14, 47, 64, 22, 21, 51, 41, 43, 16, 48, 19, 11,  1, 49, 29, 63,
       59, 57, 52, 13, 27, 33, 60, 61, 12, 10, 37, 62, 56, 24, 35, 18, 36,
       54,  4, 44, 39, 32, 50, 65, 53, 28,  8, 31, 25,  2,  6, 23],
      dtype=int64), 'cur_cost': 97553.0}, {'tour': array([47, 57, 33, 64, 16, 61, 38, 32, 58, 56, 60,  5, 11,  9, 22, 43, 42,
        7, 18,  6, 34, 45, 52, 46, 10, 29,  1, 37, 30, 21, 17,  0, 63, 27,
       41, 31, 28,  2, 50, 13, 20,  3, 40, 65, 62, 25,  8,  4, 59, 23, 48,
       39, 19, 44, 35, 12, 15, 36, 24, 26, 14, 54, 51, 55, 53, 49],
      dtype=int64), 'cur_cost': 99941.0}, {'tour': array([15, 48, 43, 64, 62, 26, 46, 21,  2,  1, 38,  7, 16,  6, 12, 65, 22,
       52, 51, 55, 11, 17, 19, 57, 23, 14, 28, 61, 37, 53, 60, 49, 58, 54,
       50, 30, 47, 29, 45,  0, 56, 10, 40, 24, 27, 44, 33, 18,  4,  3, 32,
       31, 35, 41, 36,  9, 25,  8, 34, 42, 59, 20, 63, 13,  5, 39],
      dtype=int64), 'cur_cost': 112063.0}, {'tour': array([32,  4, 13, 17, 35, 64, 15, 25, 41, 48,  9, 46,  0, 34, 57, 24, 26,
       40,  7, 59, 45, 39, 43, 47, 37, 38, 14, 28, 10, 51, 58,  8, 54, 49,
        6, 63, 16, 30, 60, 18, 55, 36, 31, 62, 23, 56, 20,  5,  3, 29, 50,
       33,  1, 44, 12, 65, 52, 21, 19, 22, 27, 42, 61, 53, 11,  2],
      dtype=int64), 'cur_cost': 108338.0}, {'tour': array([64,  5, 41, 13, 23, 32, 40, 36,  0, 42, 39, 24, 47, 61, 35, 26, 16,
        7, 63, 33, 27, 10, 20,  2,  4, 21, 58, 56, 38,  8,  3,  6, 55, 34,
       17, 54, 15, 52,  1, 57, 45, 29, 18, 50, 43, 11, 37, 22, 51, 62, 65,
       44, 60, 25, 49,  9, 31, 12, 59, 46, 48, 53, 30, 14, 19, 28],
      dtype=int64), 'cur_cost': 108083.0}, {'tour': array([33, 42, 56, 16,  6, 46, 63, 24, 28, 29,  1, 34, 40,  7, 32, 64, 21,
        8, 20, 51, 15, 13, 12, 61, 30, 14,  4, 49, 31, 26,  5, 45,  3, 57,
       58, 48, 65, 47, 39, 59, 25, 22, 60, 37, 55, 44, 19, 54,  2, 10,  9,
       17, 11, 27, 23, 35, 18, 41, 36,  0, 38, 53, 62, 52, 43, 50],
      dtype=int64), 'cur_cost': 112101.0}, {'tour': array([62, 59, 10, 25, 14, 23, 53, 11, 22,  0, 32, 64, 20,  4, 51, 38, 37,
       41, 39, 65, 34, 36, 57, 55, 28, 46,  6, 54, 56, 26, 49, 52, 27, 19,
       24, 44,  5, 31, 17, 33, 40, 35, 18, 61,  2, 47, 45, 13, 60, 43, 15,
        3,  1,  8, 21, 42, 30, 16,  7, 29, 58, 50,  9, 48, 12, 63],
      dtype=int64), 'cur_cost': 107440.0}, {'tour': array([21, 63, 33, 31, 16, 54,  6, 12, 30,  9, 22,  1, 49,  5, 14, 20, 32,
       36, 47, 38, 24, 48,  4, 58, 15, 18, 17, 40, 23, 13, 46, 26, 65, 28,
        0, 43, 41, 34, 64, 44,  7, 10, 60, 27,  8,  3, 29, 35, 39, 52, 57,
       25, 55, 37, 62, 56, 59, 42,  2, 11, 19, 61, 50, 53, 45, 51],
      dtype=int64), 'cur_cost': 106193.0}, {'tour': array([17, 19,  4, 35, 60, 41, 30, 32, 18, 27, 28,  6, 15, 25, 40, 53, 64,
       42, 11, 47, 21, 45, 49, 10, 52, 39,  3, 37, 44, 31, 61, 46, 63, 48,
       29, 50, 51, 24, 59,  7,  5, 38,  2, 54, 12, 20,  9, 36, 43,  8, 62,
       16, 65, 57, 58, 33, 22, 56,  1, 14, 55, 13, 34, 26, 23,  0],
      dtype=int64), 'cur_cost': 108782.0}]
2025-08-03 16:49:17,403 - ExploitationExpert - INFO - 局部搜索耗时: 2.27秒
2025-08-03 16:49:17,403 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-03 16:49:17,404 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([43, 61, 26, 20, 37, 12, 53, 33,  6, 65,  9, 27, 59, 24, 28, 57, 11,
        4, 41, 40, 55, 16, 42, 39,  1, 60, 52, 63, 10, 47, 58,  0, 38, 50,
       21, 22,  8, 13,  3,  2, 18, 23, 19, 51, 25, 32, 35, 46, 62, 44, 36,
       31, 48, 30,  5, 34, 56, 45,  7, 64, 15, 29, 14, 54, 49, 17],
      dtype=int64), 'cur_cost': 102389.0}
2025-08-03 16:49:17,405 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 102389.00)
2025-08-03 16:49:17,405 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-03 16:49:17,406 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-03 16:49:17,406 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:49:17,424 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:49:17,424 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:49:17,425 - ExplorationExpert - INFO - 探索路径生成完成，成本: 60417.0, 路径长度: 66
2025-08-03 16:49:17,425 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [24, 4, 14, 36, 21, 19, 25, 32, 8, 22, 28, 2, 15, 5, 23, 27, 12, 9, 53, 63, 18, 43, 37, 20, 35, 0, 61, 55, 54, 3, 57, 39, 42, 38, 13, 17, 40, 46, 50, 31, 34, 7, 64, 56, 65, 59, 1, 52, 60, 62, 11, 16, 26, 6, 10, 58, 47, 44, 51, 48, 49, 45, 41, 30, 33, 29], 'cur_cost': 60417.0}
2025-08-03 16:49:17,426 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 60417.00)
2025-08-03 16:49:17,426 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-03 16:49:17,427 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-03 16:49:17,427 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:49:17,434 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:49:17,434 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:49:17,434 - ExplorationExpert - INFO - 探索路径生成完成，成本: 96999.0, 路径长度: 66
2025-08-03 16:49:17,435 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [47, 14, 36, 23, 10, 17, 28, 18, 45, 29, 35, 22, 62, 50, 30, 43, 12, 54, 59, 65, 38, 52, 40, 61, 7, 55, 11, 0, 48, 31, 3, 15, 60, 39, 6, 26, 37, 21, 56, 53, 25, 13, 34, 32, 41, 16, 5, 49, 4, 63, 57, 58, 42, 1, 2, 8, 24, 9, 20, 19, 64, 51, 46, 44, 27, 33], 'cur_cost': 96999.0}
2025-08-03 16:49:17,435 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 96999.00)
2025-08-03 16:49:17,436 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-03 16:49:17,436 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:49:17,436 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:49:17,436 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 109664.0
2025-08-03 16:49:17,991 - ExploitationExpert - INFO - res_population_num: 4
2025-08-03 16:49:17,991 - ExploitationExpert - INFO - res_population_costs: [9857.0, 9610.0, 9548.0, 9548]
2025-08-03 16:49:17,992 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24, 29, 32, 34, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 63, 52, 65, 54, 57,
       64, 60, 58, 56, 59, 62, 53, 61, 55,  5,  4,  6,  2,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28, 30, 35, 34, 40, 43,
       48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59,
       56, 58, 60, 54, 57, 64, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 41, 38, 51, 50, 45, 44, 39, 47, 49, 40, 43, 48, 46,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 42, 46,
       48, 43, 40, 49, 47, 39, 44, 45, 50, 51, 38, 41, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64)]
2025-08-03 16:49:17,994 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:49:17,995 - ExploitationExpert - INFO - populations: [{'tour': [21, 17, 5, 53, 3, 12, 6, 23, 1, 11, 64, 10, 18, 28, 14, 24, 26, 29, 7, 13, 16, 15, 35, 20, 33, 37, 49, 42, 51, 50, 36, 30, 27, 43, 22, 46, 31, 45, 25, 47, 44, 19, 40, 9, 56, 60, 52, 58, 54, 59, 61, 8, 4, 55, 65, 62, 0, 57, 63, 2, 32, 48, 39, 41, 38, 34], 'cur_cost': 60086.0}, {'tour': array([17, 12, 31, 27, 60,  7, 41,  8,  6, 10, 23,  2, 56, 54, 15, 30,  9,
       50, 39, 58, 45, 53,  4, 20, 24,  1, 42, 37, 21, 65, 46, 63, 11, 48,
       25, 29, 36, 26, 55, 16, 19, 13, 51, 49, 32,  5, 38, 14, 34, 35, 44,
       47, 61, 22, 33, 64, 57,  0, 62, 18, 52, 43, 40,  3, 59, 28],
      dtype=int64), 'cur_cost': 98459.0}, {'tour': [53, 29, 13, 33, 43, 22, 47, 55, 27, 2, 32, 1, 17, 19, 18, 45, 0, 52, 11, 15, 36, 65, 23, 63, 58, 21, 24, 4, 34, 42, 16, 48, 10, 56, 39, 51, 28, 31, 12, 44, 60, 30, 59, 3, 9, 38, 41, 20, 37, 40, 49, 54, 26, 35, 50, 64, 6, 7, 46, 8, 62, 5, 61, 57, 14, 25], 'cur_cost': 105174.0}, {'tour': [53, 47, 27, 37, 34, 50, 56, 45, 44, 9, 38, 61, 8, 55, 0, 63, 64, 17, 14, 3, 40, 29, 13, 10, 42, 51, 22, 2, 62, 21, 26, 52, 20, 12, 46, 57, 60, 4, 30, 41, 54, 58, 32, 35, 24, 23, 39, 1, 16, 59, 19, 36, 7, 33, 18, 11, 28, 48, 31, 6, 15, 43, 49, 65, 5, 25], 'cur_cost': 102337.0}, {'tour': array([43, 61, 26, 20, 37, 12, 53, 33,  6, 65,  9, 27, 59, 24, 28, 57, 11,
        4, 41, 40, 55, 16, 42, 39,  1, 60, 52, 63, 10, 47, 58,  0, 38, 50,
       21, 22,  8, 13,  3,  2, 18, 23, 19, 51, 25, 32, 35, 46, 62, 44, 36,
       31, 48, 30,  5, 34, 56, 45,  7, 64, 15, 29, 14, 54, 49, 17],
      dtype=int64), 'cur_cost': 102389.0}, {'tour': [24, 4, 14, 36, 21, 19, 25, 32, 8, 22, 28, 2, 15, 5, 23, 27, 12, 9, 53, 63, 18, 43, 37, 20, 35, 0, 61, 55, 54, 3, 57, 39, 42, 38, 13, 17, 40, 46, 50, 31, 34, 7, 64, 56, 65, 59, 1, 52, 60, 62, 11, 16, 26, 6, 10, 58, 47, 44, 51, 48, 49, 45, 41, 30, 33, 29], 'cur_cost': 60417.0}, {'tour': [47, 14, 36, 23, 10, 17, 28, 18, 45, 29, 35, 22, 62, 50, 30, 43, 12, 54, 59, 65, 38, 52, 40, 61, 7, 55, 11, 0, 48, 31, 3, 15, 60, 39, 6, 26, 37, 21, 56, 53, 25, 13, 34, 32, 41, 16, 5, 49, 4, 63, 57, 58, 42, 1, 2, 8, 24, 9, 20, 19, 64, 51, 46, 44, 27, 33], 'cur_cost': 96999.0}, {'tour': array([60, 23, 55, 25, 21,  9, 11,  3, 13, 44, 64, 28, 16, 50, 41, 43, 48,
       18, 42, 65,  0, 31, 61, 30, 40, 22, 58, 56,  8,  1, 37, 63, 32, 12,
       47, 39,  4, 29,  6, 59, 49,  2, 14, 51, 53,  5, 45, 27, 57, 36, 52,
       15, 17, 20,  7, 19, 33, 10, 54, 24, 34, 26, 38, 35, 62, 46],
      dtype=int64), 'cur_cost': 109664.0}, {'tour': array([39, 49, 45, 17, 61, 24,  2,  1, 35, 27, 65, 38, 60, 16, 29, 15, 22,
        7, 48,  5, 31, 46, 57,  6, 21, 53, 43, 28, 47,  8, 20, 23, 11, 34,
        3, 19, 42, 18, 10, 51, 55, 63, 32, 52, 36, 64, 30, 56, 41, 25, 58,
       50, 44, 54,  4, 62, 12, 13, 33, 37, 26,  9, 59,  0, 14, 40],
      dtype=int64), 'cur_cost': 111235.0}, {'tour': array([44, 38, 16, 10, 54,  2, 65, 55, 12, 20, 15, 17, 41, 45, 39, 28,  0,
       52,  1, 64, 56, 48, 37, 49, 25, 26, 62, 47, 14, 33, 30, 35, 31, 27,
       59,  4, 53, 13, 36,  8, 18,  9, 19, 60, 61, 24, 42,  5, 46, 63, 23,
        3, 43, 58, 34, 40, 32, 29, 50,  6, 51, 11, 21, 57, 22,  7],
      dtype=int64), 'cur_cost': 97529.0}, {'tour': array([11, 32, 23, 24, 13, 16, 45, 61, 43, 34, 51,  7, 55, 54, 64,  3, 46,
       63, 33, 59,  8, 52, 60, 26, 19, 14,  6, 31, 28, 21, 48, 56, 42,  5,
       65, 27, 39, 30, 40, 12, 25, 57, 44, 20,  0, 47, 41, 50, 22, 10, 37,
        2, 35, 58, 38, 18,  1,  9, 17, 15,  4, 29, 53, 49, 62, 36],
      dtype=int64), 'cur_cost': 112191.0}, {'tour': array([46, 20, 45, 58, 34,  3, 30,  9, 15, 17, 55,  5, 42, 40,  0, 26,  7,
       38, 14, 47, 64, 22, 21, 51, 41, 43, 16, 48, 19, 11,  1, 49, 29, 63,
       59, 57, 52, 13, 27, 33, 60, 61, 12, 10, 37, 62, 56, 24, 35, 18, 36,
       54,  4, 44, 39, 32, 50, 65, 53, 28,  8, 31, 25,  2,  6, 23],
      dtype=int64), 'cur_cost': 97553.0}, {'tour': array([47, 57, 33, 64, 16, 61, 38, 32, 58, 56, 60,  5, 11,  9, 22, 43, 42,
        7, 18,  6, 34, 45, 52, 46, 10, 29,  1, 37, 30, 21, 17,  0, 63, 27,
       41, 31, 28,  2, 50, 13, 20,  3, 40, 65, 62, 25,  8,  4, 59, 23, 48,
       39, 19, 44, 35, 12, 15, 36, 24, 26, 14, 54, 51, 55, 53, 49],
      dtype=int64), 'cur_cost': 99941.0}, {'tour': array([15, 48, 43, 64, 62, 26, 46, 21,  2,  1, 38,  7, 16,  6, 12, 65, 22,
       52, 51, 55, 11, 17, 19, 57, 23, 14, 28, 61, 37, 53, 60, 49, 58, 54,
       50, 30, 47, 29, 45,  0, 56, 10, 40, 24, 27, 44, 33, 18,  4,  3, 32,
       31, 35, 41, 36,  9, 25,  8, 34, 42, 59, 20, 63, 13,  5, 39],
      dtype=int64), 'cur_cost': 112063.0}, {'tour': array([32,  4, 13, 17, 35, 64, 15, 25, 41, 48,  9, 46,  0, 34, 57, 24, 26,
       40,  7, 59, 45, 39, 43, 47, 37, 38, 14, 28, 10, 51, 58,  8, 54, 49,
        6, 63, 16, 30, 60, 18, 55, 36, 31, 62, 23, 56, 20,  5,  3, 29, 50,
       33,  1, 44, 12, 65, 52, 21, 19, 22, 27, 42, 61, 53, 11,  2],
      dtype=int64), 'cur_cost': 108338.0}, {'tour': array([64,  5, 41, 13, 23, 32, 40, 36,  0, 42, 39, 24, 47, 61, 35, 26, 16,
        7, 63, 33, 27, 10, 20,  2,  4, 21, 58, 56, 38,  8,  3,  6, 55, 34,
       17, 54, 15, 52,  1, 57, 45, 29, 18, 50, 43, 11, 37, 22, 51, 62, 65,
       44, 60, 25, 49,  9, 31, 12, 59, 46, 48, 53, 30, 14, 19, 28],
      dtype=int64), 'cur_cost': 108083.0}, {'tour': array([33, 42, 56, 16,  6, 46, 63, 24, 28, 29,  1, 34, 40,  7, 32, 64, 21,
        8, 20, 51, 15, 13, 12, 61, 30, 14,  4, 49, 31, 26,  5, 45,  3, 57,
       58, 48, 65, 47, 39, 59, 25, 22, 60, 37, 55, 44, 19, 54,  2, 10,  9,
       17, 11, 27, 23, 35, 18, 41, 36,  0, 38, 53, 62, 52, 43, 50],
      dtype=int64), 'cur_cost': 112101.0}, {'tour': array([62, 59, 10, 25, 14, 23, 53, 11, 22,  0, 32, 64, 20,  4, 51, 38, 37,
       41, 39, 65, 34, 36, 57, 55, 28, 46,  6, 54, 56, 26, 49, 52, 27, 19,
       24, 44,  5, 31, 17, 33, 40, 35, 18, 61,  2, 47, 45, 13, 60, 43, 15,
        3,  1,  8, 21, 42, 30, 16,  7, 29, 58, 50,  9, 48, 12, 63],
      dtype=int64), 'cur_cost': 107440.0}, {'tour': array([21, 63, 33, 31, 16, 54,  6, 12, 30,  9, 22,  1, 49,  5, 14, 20, 32,
       36, 47, 38, 24, 48,  4, 58, 15, 18, 17, 40, 23, 13, 46, 26, 65, 28,
        0, 43, 41, 34, 64, 44,  7, 10, 60, 27,  8,  3, 29, 35, 39, 52, 57,
       25, 55, 37, 62, 56, 59, 42,  2, 11, 19, 61, 50, 53, 45, 51],
      dtype=int64), 'cur_cost': 106193.0}, {'tour': array([17, 19,  4, 35, 60, 41, 30, 32, 18, 27, 28,  6, 15, 25, 40, 53, 64,
       42, 11, 47, 21, 45, 49, 10, 52, 39,  3, 37, 44, 31, 61, 46, 63, 48,
       29, 50, 51, 24, 59,  7,  5, 38,  2, 54, 12, 20,  9, 36, 43,  8, 62,
       16, 65, 57, 58, 33, 22, 56,  1, 14, 55, 13, 34, 26, 23,  0],
      dtype=int64), 'cur_cost': 108782.0}]
2025-08-03 16:49:18,005 - ExploitationExpert - INFO - 局部搜索耗时: 0.57秒
2025-08-03 16:49:18,005 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-03 16:49:18,006 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([60, 23, 55, 25, 21,  9, 11,  3, 13, 44, 64, 28, 16, 50, 41, 43, 48,
       18, 42, 65,  0, 31, 61, 30, 40, 22, 58, 56,  8,  1, 37, 63, 32, 12,
       47, 39,  4, 29,  6, 59, 49,  2, 14, 51, 53,  5, 45, 27, 57, 36, 52,
       15, 17, 20,  7, 19, 33, 10, 54, 24, 34, 26, 38, 35, 62, 46],
      dtype=int64), 'cur_cost': 109664.0}
2025-08-03 16:49:18,006 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 109664.00)
2025-08-03 16:49:18,006 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-03 16:49:18,007 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-03 16:49:18,007 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:49:18,015 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:49:18,019 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:49:18,021 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12746.0, 路径长度: 66
2025-08-03 16:49:18,021 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 16, 18, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12746.0}
2025-08-03 16:49:18,022 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 12746.00)
2025-08-03 16:49:18,022 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-03 16:49:18,022 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-03 16:49:18,023 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:49:18,041 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:49:18,042 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:49:18,042 - ExplorationExpert - INFO - 探索路径生成完成，成本: 57721.0, 路径长度: 66
2025-08-03 16:49:18,042 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [55, 1, 2, 5, 52, 54, 3, 65, 13, 28, 17, 24, 36, 16, 32, 12, 25, 26, 6, 61, 57, 11, 56, 19, 33, 18, 35, 22, 30, 27, 20, 15, 21, 40, 43, 50, 39, 47, 51, 23, 37, 29, 4, 8, 64, 58, 0, 63, 9, 59, 14, 46, 49, 45, 42, 34, 10, 53, 7, 31, 48, 44, 41, 38, 60, 62], 'cur_cost': 57721.0}
2025-08-03 16:49:18,043 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 57721.00)
2025-08-03 16:49:18,043 - experts.management.collaboration_manager - INFO - 为个体 10 生成利用路径
2025-08-03 16:49:18,043 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:49:18,044 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:49:18,045 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 10 处的路径，新成本: 108847.0
2025-08-03 16:49:18,121 - ExploitationExpert - INFO - res_population_num: 8
2025-08-03 16:49:18,121 - ExploitationExpert - INFO - res_population_costs: [9857.0, 9610.0, 9548.0, 9548, 9546, 9540, 9539, 9536]
2025-08-03 16:49:18,122 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24, 29, 32, 34, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 63, 52, 65, 54, 57,
       64, 60, 58, 56, 59, 62, 53, 61, 55,  5,  4,  6,  2,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28, 30, 35, 34, 40, 43,
       48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59,
       56, 58, 60, 54, 57, 64, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 41, 38, 51, 50, 45, 44, 39, 47, 49, 40, 43, 48, 46,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 42, 46,
       48, 43, 40, 49, 47, 39, 44, 45, 50, 51, 38, 41, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34,
       35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 13, 21, 20,
       14, 15, 22, 23, 16, 18, 12, 17,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4, 17, 12, 18, 16, 23, 22, 15, 14, 20,
       21, 13, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34,
       40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59,
       62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55,  6,  2,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:49:18,126 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:49:18,126 - ExploitationExpert - INFO - populations: [{'tour': [21, 17, 5, 53, 3, 12, 6, 23, 1, 11, 64, 10, 18, 28, 14, 24, 26, 29, 7, 13, 16, 15, 35, 20, 33, 37, 49, 42, 51, 50, 36, 30, 27, 43, 22, 46, 31, 45, 25, 47, 44, 19, 40, 9, 56, 60, 52, 58, 54, 59, 61, 8, 4, 55, 65, 62, 0, 57, 63, 2, 32, 48, 39, 41, 38, 34], 'cur_cost': 60086.0}, {'tour': array([17, 12, 31, 27, 60,  7, 41,  8,  6, 10, 23,  2, 56, 54, 15, 30,  9,
       50, 39, 58, 45, 53,  4, 20, 24,  1, 42, 37, 21, 65, 46, 63, 11, 48,
       25, 29, 36, 26, 55, 16, 19, 13, 51, 49, 32,  5, 38, 14, 34, 35, 44,
       47, 61, 22, 33, 64, 57,  0, 62, 18, 52, 43, 40,  3, 59, 28],
      dtype=int64), 'cur_cost': 98459.0}, {'tour': [53, 29, 13, 33, 43, 22, 47, 55, 27, 2, 32, 1, 17, 19, 18, 45, 0, 52, 11, 15, 36, 65, 23, 63, 58, 21, 24, 4, 34, 42, 16, 48, 10, 56, 39, 51, 28, 31, 12, 44, 60, 30, 59, 3, 9, 38, 41, 20, 37, 40, 49, 54, 26, 35, 50, 64, 6, 7, 46, 8, 62, 5, 61, 57, 14, 25], 'cur_cost': 105174.0}, {'tour': [53, 47, 27, 37, 34, 50, 56, 45, 44, 9, 38, 61, 8, 55, 0, 63, 64, 17, 14, 3, 40, 29, 13, 10, 42, 51, 22, 2, 62, 21, 26, 52, 20, 12, 46, 57, 60, 4, 30, 41, 54, 58, 32, 35, 24, 23, 39, 1, 16, 59, 19, 36, 7, 33, 18, 11, 28, 48, 31, 6, 15, 43, 49, 65, 5, 25], 'cur_cost': 102337.0}, {'tour': array([43, 61, 26, 20, 37, 12, 53, 33,  6, 65,  9, 27, 59, 24, 28, 57, 11,
        4, 41, 40, 55, 16, 42, 39,  1, 60, 52, 63, 10, 47, 58,  0, 38, 50,
       21, 22,  8, 13,  3,  2, 18, 23, 19, 51, 25, 32, 35, 46, 62, 44, 36,
       31, 48, 30,  5, 34, 56, 45,  7, 64, 15, 29, 14, 54, 49, 17],
      dtype=int64), 'cur_cost': 102389.0}, {'tour': [24, 4, 14, 36, 21, 19, 25, 32, 8, 22, 28, 2, 15, 5, 23, 27, 12, 9, 53, 63, 18, 43, 37, 20, 35, 0, 61, 55, 54, 3, 57, 39, 42, 38, 13, 17, 40, 46, 50, 31, 34, 7, 64, 56, 65, 59, 1, 52, 60, 62, 11, 16, 26, 6, 10, 58, 47, 44, 51, 48, 49, 45, 41, 30, 33, 29], 'cur_cost': 60417.0}, {'tour': [47, 14, 36, 23, 10, 17, 28, 18, 45, 29, 35, 22, 62, 50, 30, 43, 12, 54, 59, 65, 38, 52, 40, 61, 7, 55, 11, 0, 48, 31, 3, 15, 60, 39, 6, 26, 37, 21, 56, 53, 25, 13, 34, 32, 41, 16, 5, 49, 4, 63, 57, 58, 42, 1, 2, 8, 24, 9, 20, 19, 64, 51, 46, 44, 27, 33], 'cur_cost': 96999.0}, {'tour': array([60, 23, 55, 25, 21,  9, 11,  3, 13, 44, 64, 28, 16, 50, 41, 43, 48,
       18, 42, 65,  0, 31, 61, 30, 40, 22, 58, 56,  8,  1, 37, 63, 32, 12,
       47, 39,  4, 29,  6, 59, 49,  2, 14, 51, 53,  5, 45, 27, 57, 36, 52,
       15, 17, 20,  7, 19, 33, 10, 54, 24, 34, 26, 38, 35, 62, 46],
      dtype=int64), 'cur_cost': 109664.0}, {'tour': [0, 16, 18, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12746.0}, {'tour': [55, 1, 2, 5, 52, 54, 3, 65, 13, 28, 17, 24, 36, 16, 32, 12, 25, 26, 6, 61, 57, 11, 56, 19, 33, 18, 35, 22, 30, 27, 20, 15, 21, 40, 43, 50, 39, 47, 51, 23, 37, 29, 4, 8, 64, 58, 0, 63, 9, 59, 14, 46, 49, 45, 42, 34, 10, 53, 7, 31, 48, 44, 41, 38, 60, 62], 'cur_cost': 57721.0}, {'tour': array([55, 38, 29,  8, 20, 36, 64, 19, 27,  4, 22,  6, 44, 57, 65,  5, 51,
       61, 17, 21, 48, 49, 16, 59, 14, 25, 58, 52, 63, 37, 40, 41, 50, 60,
       34, 39, 18,  3, 56, 13, 23, 31,  1, 30, 46,  0, 24, 32,  7, 42, 26,
       11, 12,  2, 45, 47, 54, 33, 62, 10, 43,  9, 15, 28, 35, 53],
      dtype=int64), 'cur_cost': 108847.0}, {'tour': array([46, 20, 45, 58, 34,  3, 30,  9, 15, 17, 55,  5, 42, 40,  0, 26,  7,
       38, 14, 47, 64, 22, 21, 51, 41, 43, 16, 48, 19, 11,  1, 49, 29, 63,
       59, 57, 52, 13, 27, 33, 60, 61, 12, 10, 37, 62, 56, 24, 35, 18, 36,
       54,  4, 44, 39, 32, 50, 65, 53, 28,  8, 31, 25,  2,  6, 23],
      dtype=int64), 'cur_cost': 97553.0}, {'tour': array([47, 57, 33, 64, 16, 61, 38, 32, 58, 56, 60,  5, 11,  9, 22, 43, 42,
        7, 18,  6, 34, 45, 52, 46, 10, 29,  1, 37, 30, 21, 17,  0, 63, 27,
       41, 31, 28,  2, 50, 13, 20,  3, 40, 65, 62, 25,  8,  4, 59, 23, 48,
       39, 19, 44, 35, 12, 15, 36, 24, 26, 14, 54, 51, 55, 53, 49],
      dtype=int64), 'cur_cost': 99941.0}, {'tour': array([15, 48, 43, 64, 62, 26, 46, 21,  2,  1, 38,  7, 16,  6, 12, 65, 22,
       52, 51, 55, 11, 17, 19, 57, 23, 14, 28, 61, 37, 53, 60, 49, 58, 54,
       50, 30, 47, 29, 45,  0, 56, 10, 40, 24, 27, 44, 33, 18,  4,  3, 32,
       31, 35, 41, 36,  9, 25,  8, 34, 42, 59, 20, 63, 13,  5, 39],
      dtype=int64), 'cur_cost': 112063.0}, {'tour': array([32,  4, 13, 17, 35, 64, 15, 25, 41, 48,  9, 46,  0, 34, 57, 24, 26,
       40,  7, 59, 45, 39, 43, 47, 37, 38, 14, 28, 10, 51, 58,  8, 54, 49,
        6, 63, 16, 30, 60, 18, 55, 36, 31, 62, 23, 56, 20,  5,  3, 29, 50,
       33,  1, 44, 12, 65, 52, 21, 19, 22, 27, 42, 61, 53, 11,  2],
      dtype=int64), 'cur_cost': 108338.0}, {'tour': array([64,  5, 41, 13, 23, 32, 40, 36,  0, 42, 39, 24, 47, 61, 35, 26, 16,
        7, 63, 33, 27, 10, 20,  2,  4, 21, 58, 56, 38,  8,  3,  6, 55, 34,
       17, 54, 15, 52,  1, 57, 45, 29, 18, 50, 43, 11, 37, 22, 51, 62, 65,
       44, 60, 25, 49,  9, 31, 12, 59, 46, 48, 53, 30, 14, 19, 28],
      dtype=int64), 'cur_cost': 108083.0}, {'tour': array([33, 42, 56, 16,  6, 46, 63, 24, 28, 29,  1, 34, 40,  7, 32, 64, 21,
        8, 20, 51, 15, 13, 12, 61, 30, 14,  4, 49, 31, 26,  5, 45,  3, 57,
       58, 48, 65, 47, 39, 59, 25, 22, 60, 37, 55, 44, 19, 54,  2, 10,  9,
       17, 11, 27, 23, 35, 18, 41, 36,  0, 38, 53, 62, 52, 43, 50],
      dtype=int64), 'cur_cost': 112101.0}, {'tour': array([62, 59, 10, 25, 14, 23, 53, 11, 22,  0, 32, 64, 20,  4, 51, 38, 37,
       41, 39, 65, 34, 36, 57, 55, 28, 46,  6, 54, 56, 26, 49, 52, 27, 19,
       24, 44,  5, 31, 17, 33, 40, 35, 18, 61,  2, 47, 45, 13, 60, 43, 15,
        3,  1,  8, 21, 42, 30, 16,  7, 29, 58, 50,  9, 48, 12, 63],
      dtype=int64), 'cur_cost': 107440.0}, {'tour': array([21, 63, 33, 31, 16, 54,  6, 12, 30,  9, 22,  1, 49,  5, 14, 20, 32,
       36, 47, 38, 24, 48,  4, 58, 15, 18, 17, 40, 23, 13, 46, 26, 65, 28,
        0, 43, 41, 34, 64, 44,  7, 10, 60, 27,  8,  3, 29, 35, 39, 52, 57,
       25, 55, 37, 62, 56, 59, 42,  2, 11, 19, 61, 50, 53, 45, 51],
      dtype=int64), 'cur_cost': 106193.0}, {'tour': array([17, 19,  4, 35, 60, 41, 30, 32, 18, 27, 28,  6, 15, 25, 40, 53, 64,
       42, 11, 47, 21, 45, 49, 10, 52, 39,  3, 37, 44, 31, 61, 46, 63, 48,
       29, 50, 51, 24, 59,  7,  5, 38,  2, 54, 12, 20,  9, 36, 43,  8, 62,
       16, 65, 57, 58, 33, 22, 56,  1, 14, 55, 13, 34, 26, 23,  0],
      dtype=int64), 'cur_cost': 108782.0}]
2025-08-03 16:49:18,136 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-03 16:49:18,136 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-03 16:49:18,137 - experts.management.collaboration_manager - INFO - 个体 10 利用路径生成报告: {'new_tour': array([55, 38, 29,  8, 20, 36, 64, 19, 27,  4, 22,  6, 44, 57, 65,  5, 51,
       61, 17, 21, 48, 49, 16, 59, 14, 25, 58, 52, 63, 37, 40, 41, 50, 60,
       34, 39, 18,  3, 56, 13, 23, 31,  1, 30, 46,  0, 24, 32,  7, 42, 26,
       11, 12,  2, 45, 47, 54, 33, 62, 10, 43,  9, 15, 28, 35, 53],
      dtype=int64), 'cur_cost': 108847.0}
2025-08-03 16:49:18,137 - experts.management.collaboration_manager - INFO - 个体 10 接受新路径 (成本: 108847.00)
2025-08-03 16:49:18,138 - experts.management.collaboration_manager - INFO - 为个体 11 生成探索路径
2025-08-03 16:49:18,138 - ExplorationExpert - INFO - 开始为个体 11 生成探索路径（算法实现）
2025-08-03 16:49:18,139 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:49:18,155 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:49:18,156 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:49:18,157 - ExplorationExpert - INFO - 探索路径生成完成，成本: 61086.0, 路径长度: 66
2025-08-03 16:49:18,157 - experts.management.collaboration_manager - INFO - 个体 11 探索路径生成报告: {'new_tour': [59, 9, 11, 60, 18, 30, 32, 5, 22, 17, 23, 19, 36, 35, 33, 25, 4, 3, 16, 8, 12, 37, 48, 40, 47, 28, 43, 42, 49, 38, 21, 15, 20, 27, 44, 39, 7, 55, 53, 65, 64, 57, 0, 63, 54, 14, 10, 61, 46, 41, 13, 2, 56, 58, 52, 6, 62, 45, 26, 31, 34, 29, 1, 24, 50, 51], 'cur_cost': 61086.0}
2025-08-03 16:49:18,157 - experts.management.collaboration_manager - INFO - 个体 11 接受新路径 (成本: 61086.00)
2025-08-03 16:49:18,158 - experts.management.collaboration_manager - INFO - 为个体 12 生成探索路径
2025-08-03 16:49:18,158 - ExplorationExpert - INFO - 开始为个体 12 生成探索路径（算法实现）
2025-08-03 16:49:18,158 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:49:18,164 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:49:18,165 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:49:18,165 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12864.0, 路径长度: 66
2025-08-03 16:49:18,166 - experts.management.collaboration_manager - INFO - 个体 12 探索路径生成报告: {'new_tour': [0, 16, 2, 1, 7, 3, 9, 11, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12864.0}
2025-08-03 16:49:18,166 - experts.management.collaboration_manager - INFO - 个体 12 接受新路径 (成本: 12864.00)
2025-08-03 16:49:18,166 - experts.management.collaboration_manager - INFO - 为个体 13 生成利用路径
2025-08-03 16:49:18,167 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:49:18,167 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:49:18,167 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 13 处的路径，新成本: 111518.0
2025-08-03 16:49:18,261 - ExploitationExpert - INFO - res_population_num: 11
2025-08-03 16:49:18,262 - ExploitationExpert - INFO - res_population_costs: [9857.0, 9610.0, 9548.0, 9548, 9546, 9540, 9539, 9536, 9530, 9530, 9521]
2025-08-03 16:49:18,262 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24, 29, 32, 34, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 63, 52, 65, 54, 57,
       64, 60, 58, 56, 59, 62, 53, 61, 55,  5,  4,  6,  2,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28, 30, 35, 34, 40, 43,
       48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59,
       56, 58, 60, 54, 57, 64, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 41, 38, 51, 50, 45, 44, 39, 47, 49, 40, 43, 48, 46,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 42, 46,
       48, 43, 40, 49, 47, 39, 44, 45, 50, 51, 38, 41, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34,
       35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 13, 21, 20,
       14, 15, 22, 23, 16, 18, 12, 17,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4, 17, 12, 18, 16, 23, 22, 15, 14, 20,
       21, 13, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34,
       40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59,
       62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55,  6,  2,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:49:18,269 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:49:18,269 - ExploitationExpert - INFO - populations: [{'tour': [21, 17, 5, 53, 3, 12, 6, 23, 1, 11, 64, 10, 18, 28, 14, 24, 26, 29, 7, 13, 16, 15, 35, 20, 33, 37, 49, 42, 51, 50, 36, 30, 27, 43, 22, 46, 31, 45, 25, 47, 44, 19, 40, 9, 56, 60, 52, 58, 54, 59, 61, 8, 4, 55, 65, 62, 0, 57, 63, 2, 32, 48, 39, 41, 38, 34], 'cur_cost': 60086.0}, {'tour': array([17, 12, 31, 27, 60,  7, 41,  8,  6, 10, 23,  2, 56, 54, 15, 30,  9,
       50, 39, 58, 45, 53,  4, 20, 24,  1, 42, 37, 21, 65, 46, 63, 11, 48,
       25, 29, 36, 26, 55, 16, 19, 13, 51, 49, 32,  5, 38, 14, 34, 35, 44,
       47, 61, 22, 33, 64, 57,  0, 62, 18, 52, 43, 40,  3, 59, 28],
      dtype=int64), 'cur_cost': 98459.0}, {'tour': [53, 29, 13, 33, 43, 22, 47, 55, 27, 2, 32, 1, 17, 19, 18, 45, 0, 52, 11, 15, 36, 65, 23, 63, 58, 21, 24, 4, 34, 42, 16, 48, 10, 56, 39, 51, 28, 31, 12, 44, 60, 30, 59, 3, 9, 38, 41, 20, 37, 40, 49, 54, 26, 35, 50, 64, 6, 7, 46, 8, 62, 5, 61, 57, 14, 25], 'cur_cost': 105174.0}, {'tour': [53, 47, 27, 37, 34, 50, 56, 45, 44, 9, 38, 61, 8, 55, 0, 63, 64, 17, 14, 3, 40, 29, 13, 10, 42, 51, 22, 2, 62, 21, 26, 52, 20, 12, 46, 57, 60, 4, 30, 41, 54, 58, 32, 35, 24, 23, 39, 1, 16, 59, 19, 36, 7, 33, 18, 11, 28, 48, 31, 6, 15, 43, 49, 65, 5, 25], 'cur_cost': 102337.0}, {'tour': array([43, 61, 26, 20, 37, 12, 53, 33,  6, 65,  9, 27, 59, 24, 28, 57, 11,
        4, 41, 40, 55, 16, 42, 39,  1, 60, 52, 63, 10, 47, 58,  0, 38, 50,
       21, 22,  8, 13,  3,  2, 18, 23, 19, 51, 25, 32, 35, 46, 62, 44, 36,
       31, 48, 30,  5, 34, 56, 45,  7, 64, 15, 29, 14, 54, 49, 17],
      dtype=int64), 'cur_cost': 102389.0}, {'tour': [24, 4, 14, 36, 21, 19, 25, 32, 8, 22, 28, 2, 15, 5, 23, 27, 12, 9, 53, 63, 18, 43, 37, 20, 35, 0, 61, 55, 54, 3, 57, 39, 42, 38, 13, 17, 40, 46, 50, 31, 34, 7, 64, 56, 65, 59, 1, 52, 60, 62, 11, 16, 26, 6, 10, 58, 47, 44, 51, 48, 49, 45, 41, 30, 33, 29], 'cur_cost': 60417.0}, {'tour': [47, 14, 36, 23, 10, 17, 28, 18, 45, 29, 35, 22, 62, 50, 30, 43, 12, 54, 59, 65, 38, 52, 40, 61, 7, 55, 11, 0, 48, 31, 3, 15, 60, 39, 6, 26, 37, 21, 56, 53, 25, 13, 34, 32, 41, 16, 5, 49, 4, 63, 57, 58, 42, 1, 2, 8, 24, 9, 20, 19, 64, 51, 46, 44, 27, 33], 'cur_cost': 96999.0}, {'tour': array([60, 23, 55, 25, 21,  9, 11,  3, 13, 44, 64, 28, 16, 50, 41, 43, 48,
       18, 42, 65,  0, 31, 61, 30, 40, 22, 58, 56,  8,  1, 37, 63, 32, 12,
       47, 39,  4, 29,  6, 59, 49,  2, 14, 51, 53,  5, 45, 27, 57, 36, 52,
       15, 17, 20,  7, 19, 33, 10, 54, 24, 34, 26, 38, 35, 62, 46],
      dtype=int64), 'cur_cost': 109664.0}, {'tour': [0, 16, 18, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12746.0}, {'tour': [55, 1, 2, 5, 52, 54, 3, 65, 13, 28, 17, 24, 36, 16, 32, 12, 25, 26, 6, 61, 57, 11, 56, 19, 33, 18, 35, 22, 30, 27, 20, 15, 21, 40, 43, 50, 39, 47, 51, 23, 37, 29, 4, 8, 64, 58, 0, 63, 9, 59, 14, 46, 49, 45, 42, 34, 10, 53, 7, 31, 48, 44, 41, 38, 60, 62], 'cur_cost': 57721.0}, {'tour': array([55, 38, 29,  8, 20, 36, 64, 19, 27,  4, 22,  6, 44, 57, 65,  5, 51,
       61, 17, 21, 48, 49, 16, 59, 14, 25, 58, 52, 63, 37, 40, 41, 50, 60,
       34, 39, 18,  3, 56, 13, 23, 31,  1, 30, 46,  0, 24, 32,  7, 42, 26,
       11, 12,  2, 45, 47, 54, 33, 62, 10, 43,  9, 15, 28, 35, 53],
      dtype=int64), 'cur_cost': 108847.0}, {'tour': [59, 9, 11, 60, 18, 30, 32, 5, 22, 17, 23, 19, 36, 35, 33, 25, 4, 3, 16, 8, 12, 37, 48, 40, 47, 28, 43, 42, 49, 38, 21, 15, 20, 27, 44, 39, 7, 55, 53, 65, 64, 57, 0, 63, 54, 14, 10, 61, 46, 41, 13, 2, 56, 58, 52, 6, 62, 45, 26, 31, 34, 29, 1, 24, 50, 51], 'cur_cost': 61086.0}, {'tour': [0, 16, 2, 1, 7, 3, 9, 11, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12864.0}, {'tour': array([18, 13, 38, 14, 10, 25, 52, 53, 37, 43,  1, 42, 34, 27, 24, 60, 33,
       23, 62, 20, 56, 32, 45, 29, 63,  8, 31,  3,  7, 12, 41,  2,  4,  0,
       11, 35, 21, 26, 15, 55, 47, 44,  9, 16, 54,  5, 28, 65, 57, 39, 22,
       46,  6, 40, 48, 17, 36, 58, 49, 19, 61, 51, 30, 59, 64, 50],
      dtype=int64), 'cur_cost': 111518.0}, {'tour': array([32,  4, 13, 17, 35, 64, 15, 25, 41, 48,  9, 46,  0, 34, 57, 24, 26,
       40,  7, 59, 45, 39, 43, 47, 37, 38, 14, 28, 10, 51, 58,  8, 54, 49,
        6, 63, 16, 30, 60, 18, 55, 36, 31, 62, 23, 56, 20,  5,  3, 29, 50,
       33,  1, 44, 12, 65, 52, 21, 19, 22, 27, 42, 61, 53, 11,  2],
      dtype=int64), 'cur_cost': 108338.0}, {'tour': array([64,  5, 41, 13, 23, 32, 40, 36,  0, 42, 39, 24, 47, 61, 35, 26, 16,
        7, 63, 33, 27, 10, 20,  2,  4, 21, 58, 56, 38,  8,  3,  6, 55, 34,
       17, 54, 15, 52,  1, 57, 45, 29, 18, 50, 43, 11, 37, 22, 51, 62, 65,
       44, 60, 25, 49,  9, 31, 12, 59, 46, 48, 53, 30, 14, 19, 28],
      dtype=int64), 'cur_cost': 108083.0}, {'tour': array([33, 42, 56, 16,  6, 46, 63, 24, 28, 29,  1, 34, 40,  7, 32, 64, 21,
        8, 20, 51, 15, 13, 12, 61, 30, 14,  4, 49, 31, 26,  5, 45,  3, 57,
       58, 48, 65, 47, 39, 59, 25, 22, 60, 37, 55, 44, 19, 54,  2, 10,  9,
       17, 11, 27, 23, 35, 18, 41, 36,  0, 38, 53, 62, 52, 43, 50],
      dtype=int64), 'cur_cost': 112101.0}, {'tour': array([62, 59, 10, 25, 14, 23, 53, 11, 22,  0, 32, 64, 20,  4, 51, 38, 37,
       41, 39, 65, 34, 36, 57, 55, 28, 46,  6, 54, 56, 26, 49, 52, 27, 19,
       24, 44,  5, 31, 17, 33, 40, 35, 18, 61,  2, 47, 45, 13, 60, 43, 15,
        3,  1,  8, 21, 42, 30, 16,  7, 29, 58, 50,  9, 48, 12, 63],
      dtype=int64), 'cur_cost': 107440.0}, {'tour': array([21, 63, 33, 31, 16, 54,  6, 12, 30,  9, 22,  1, 49,  5, 14, 20, 32,
       36, 47, 38, 24, 48,  4, 58, 15, 18, 17, 40, 23, 13, 46, 26, 65, 28,
        0, 43, 41, 34, 64, 44,  7, 10, 60, 27,  8,  3, 29, 35, 39, 52, 57,
       25, 55, 37, 62, 56, 59, 42,  2, 11, 19, 61, 50, 53, 45, 51],
      dtype=int64), 'cur_cost': 106193.0}, {'tour': array([17, 19,  4, 35, 60, 41, 30, 32, 18, 27, 28,  6, 15, 25, 40, 53, 64,
       42, 11, 47, 21, 45, 49, 10, 52, 39,  3, 37, 44, 31, 61, 46, 63, 48,
       29, 50, 51, 24, 59,  7,  5, 38,  2, 54, 12, 20,  9, 36, 43,  8, 62,
       16, 65, 57, 58, 33, 22, 56,  1, 14, 55, 13, 34, 26, 23,  0],
      dtype=int64), 'cur_cost': 108782.0}]
2025-08-03 16:49:18,277 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒
2025-08-03 16:49:18,278 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-03 16:49:18,279 - experts.management.collaboration_manager - INFO - 个体 13 利用路径生成报告: {'new_tour': array([18, 13, 38, 14, 10, 25, 52, 53, 37, 43,  1, 42, 34, 27, 24, 60, 33,
       23, 62, 20, 56, 32, 45, 29, 63,  8, 31,  3,  7, 12, 41,  2,  4,  0,
       11, 35, 21, 26, 15, 55, 47, 44,  9, 16, 54,  5, 28, 65, 57, 39, 22,
       46,  6, 40, 48, 17, 36, 58, 49, 19, 61, 51, 30, 59, 64, 50],
      dtype=int64), 'cur_cost': 111518.0}
2025-08-03 16:49:18,281 - experts.management.collaboration_manager - INFO - 个体 13 接受新路径 (成本: 111518.00)
2025-08-03 16:49:18,283 - experts.management.collaboration_manager - INFO - 为个体 14 生成探索路径
2025-08-03 16:49:18,285 - ExplorationExpert - INFO - 开始为个体 14 生成探索路径（算法实现）
2025-08-03 16:49:18,286 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:49:18,291 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:49:18,291 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:49:18,292 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98178.0, 路径长度: 66
2025-08-03 16:49:18,292 - experts.management.collaboration_manager - INFO - 个体 14 探索路径生成报告: {'new_tour': [47, 27, 23, 52, 19, 18, 45, 11, 38, 61, 62, 28, 30, 63, 53, 65, 64, 22, 0, 54, 3, 36, 17, 10, 56, 31, 4, 12, 60, 58, 9, 7, 33, 8, 48, 46, 2, 20, 6, 25, 37, 41, 55, 39, 26, 24, 21, 40, 34, 49, 57, 44, 1, 32, 59, 42, 13, 14, 15, 35, 5, 16, 29, 51, 50, 43], 'cur_cost': 98178.0}
2025-08-03 16:49:18,293 - experts.management.collaboration_manager - INFO - 个体 14 接受新路径 (成本: 98178.00)
2025-08-03 16:49:18,293 - experts.management.collaboration_manager - INFO - 为个体 15 生成探索路径
2025-08-03 16:49:18,293 - ExplorationExpert - INFO - 开始为个体 15 生成探索路径（算法实现）
2025-08-03 16:49:18,294 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:49:18,309 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:49:18,310 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:49:18,311 - ExplorationExpert - INFO - 探索路径生成完成，成本: 55630.0, 路径长度: 66
2025-08-03 16:49:18,312 - experts.management.collaboration_manager - INFO - 个体 15 探索路径生成报告: {'new_tour': [8, 11, 53, 15, 12, 20, 4, 58, 0, 57, 18, 9, 19, 33, 24, 17, 16, 2, 37, 34, 25, 28, 29, 14, 10, 1, 55, 22, 5, 3, 6, 36, 31, 49, 51, 50, 23, 46, 40, 42, 48, 43, 26, 47, 38, 35, 21, 39, 45, 44, 56, 62, 60, 64, 59, 63, 7, 52, 65, 13, 32, 30, 27, 41, 61, 54], 'cur_cost': 55630.0}
2025-08-03 16:49:18,313 - experts.management.collaboration_manager - INFO - 个体 15 接受新路径 (成本: 55630.00)
2025-08-03 16:49:18,314 - experts.management.collaboration_manager - INFO - 为个体 16 生成利用路径
2025-08-03 16:49:18,314 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:49:18,315 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:49:18,317 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 16 处的路径，新成本: 108234.0
2025-08-03 16:49:18,396 - ExploitationExpert - INFO - res_population_num: 11
2025-08-03 16:49:18,397 - ExploitationExpert - INFO - res_population_costs: [9857.0, 9610.0, 9548.0, 9548, 9546, 9540, 9539, 9536, 9530, 9530, 9521]
2025-08-03 16:49:18,398 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24, 29, 32, 34, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 63, 52, 65, 54, 57,
       64, 60, 58, 56, 59, 62, 53, 61, 55,  5,  4,  6,  2,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28, 30, 35, 34, 40, 43,
       48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59,
       56, 58, 60, 54, 57, 64, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 41, 38, 51, 50, 45, 44, 39, 47, 49, 40, 43, 48, 46,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 42, 46,
       48, 43, 40, 49, 47, 39, 44, 45, 50, 51, 38, 41, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34,
       35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 13, 21, 20,
       14, 15, 22, 23, 16, 18, 12, 17,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4, 17, 12, 18, 16, 23, 22, 15, 14, 20,
       21, 13, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34,
       40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59,
       62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55,  6,  2,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:49:18,405 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:49:18,405 - ExploitationExpert - INFO - populations: [{'tour': [21, 17, 5, 53, 3, 12, 6, 23, 1, 11, 64, 10, 18, 28, 14, 24, 26, 29, 7, 13, 16, 15, 35, 20, 33, 37, 49, 42, 51, 50, 36, 30, 27, 43, 22, 46, 31, 45, 25, 47, 44, 19, 40, 9, 56, 60, 52, 58, 54, 59, 61, 8, 4, 55, 65, 62, 0, 57, 63, 2, 32, 48, 39, 41, 38, 34], 'cur_cost': 60086.0}, {'tour': array([17, 12, 31, 27, 60,  7, 41,  8,  6, 10, 23,  2, 56, 54, 15, 30,  9,
       50, 39, 58, 45, 53,  4, 20, 24,  1, 42, 37, 21, 65, 46, 63, 11, 48,
       25, 29, 36, 26, 55, 16, 19, 13, 51, 49, 32,  5, 38, 14, 34, 35, 44,
       47, 61, 22, 33, 64, 57,  0, 62, 18, 52, 43, 40,  3, 59, 28],
      dtype=int64), 'cur_cost': 98459.0}, {'tour': [53, 29, 13, 33, 43, 22, 47, 55, 27, 2, 32, 1, 17, 19, 18, 45, 0, 52, 11, 15, 36, 65, 23, 63, 58, 21, 24, 4, 34, 42, 16, 48, 10, 56, 39, 51, 28, 31, 12, 44, 60, 30, 59, 3, 9, 38, 41, 20, 37, 40, 49, 54, 26, 35, 50, 64, 6, 7, 46, 8, 62, 5, 61, 57, 14, 25], 'cur_cost': 105174.0}, {'tour': [53, 47, 27, 37, 34, 50, 56, 45, 44, 9, 38, 61, 8, 55, 0, 63, 64, 17, 14, 3, 40, 29, 13, 10, 42, 51, 22, 2, 62, 21, 26, 52, 20, 12, 46, 57, 60, 4, 30, 41, 54, 58, 32, 35, 24, 23, 39, 1, 16, 59, 19, 36, 7, 33, 18, 11, 28, 48, 31, 6, 15, 43, 49, 65, 5, 25], 'cur_cost': 102337.0}, {'tour': array([43, 61, 26, 20, 37, 12, 53, 33,  6, 65,  9, 27, 59, 24, 28, 57, 11,
        4, 41, 40, 55, 16, 42, 39,  1, 60, 52, 63, 10, 47, 58,  0, 38, 50,
       21, 22,  8, 13,  3,  2, 18, 23, 19, 51, 25, 32, 35, 46, 62, 44, 36,
       31, 48, 30,  5, 34, 56, 45,  7, 64, 15, 29, 14, 54, 49, 17],
      dtype=int64), 'cur_cost': 102389.0}, {'tour': [24, 4, 14, 36, 21, 19, 25, 32, 8, 22, 28, 2, 15, 5, 23, 27, 12, 9, 53, 63, 18, 43, 37, 20, 35, 0, 61, 55, 54, 3, 57, 39, 42, 38, 13, 17, 40, 46, 50, 31, 34, 7, 64, 56, 65, 59, 1, 52, 60, 62, 11, 16, 26, 6, 10, 58, 47, 44, 51, 48, 49, 45, 41, 30, 33, 29], 'cur_cost': 60417.0}, {'tour': [47, 14, 36, 23, 10, 17, 28, 18, 45, 29, 35, 22, 62, 50, 30, 43, 12, 54, 59, 65, 38, 52, 40, 61, 7, 55, 11, 0, 48, 31, 3, 15, 60, 39, 6, 26, 37, 21, 56, 53, 25, 13, 34, 32, 41, 16, 5, 49, 4, 63, 57, 58, 42, 1, 2, 8, 24, 9, 20, 19, 64, 51, 46, 44, 27, 33], 'cur_cost': 96999.0}, {'tour': array([60, 23, 55, 25, 21,  9, 11,  3, 13, 44, 64, 28, 16, 50, 41, 43, 48,
       18, 42, 65,  0, 31, 61, 30, 40, 22, 58, 56,  8,  1, 37, 63, 32, 12,
       47, 39,  4, 29,  6, 59, 49,  2, 14, 51, 53,  5, 45, 27, 57, 36, 52,
       15, 17, 20,  7, 19, 33, 10, 54, 24, 34, 26, 38, 35, 62, 46],
      dtype=int64), 'cur_cost': 109664.0}, {'tour': [0, 16, 18, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12746.0}, {'tour': [55, 1, 2, 5, 52, 54, 3, 65, 13, 28, 17, 24, 36, 16, 32, 12, 25, 26, 6, 61, 57, 11, 56, 19, 33, 18, 35, 22, 30, 27, 20, 15, 21, 40, 43, 50, 39, 47, 51, 23, 37, 29, 4, 8, 64, 58, 0, 63, 9, 59, 14, 46, 49, 45, 42, 34, 10, 53, 7, 31, 48, 44, 41, 38, 60, 62], 'cur_cost': 57721.0}, {'tour': array([55, 38, 29,  8, 20, 36, 64, 19, 27,  4, 22,  6, 44, 57, 65,  5, 51,
       61, 17, 21, 48, 49, 16, 59, 14, 25, 58, 52, 63, 37, 40, 41, 50, 60,
       34, 39, 18,  3, 56, 13, 23, 31,  1, 30, 46,  0, 24, 32,  7, 42, 26,
       11, 12,  2, 45, 47, 54, 33, 62, 10, 43,  9, 15, 28, 35, 53],
      dtype=int64), 'cur_cost': 108847.0}, {'tour': [59, 9, 11, 60, 18, 30, 32, 5, 22, 17, 23, 19, 36, 35, 33, 25, 4, 3, 16, 8, 12, 37, 48, 40, 47, 28, 43, 42, 49, 38, 21, 15, 20, 27, 44, 39, 7, 55, 53, 65, 64, 57, 0, 63, 54, 14, 10, 61, 46, 41, 13, 2, 56, 58, 52, 6, 62, 45, 26, 31, 34, 29, 1, 24, 50, 51], 'cur_cost': 61086.0}, {'tour': [0, 16, 2, 1, 7, 3, 9, 11, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12864.0}, {'tour': array([18, 13, 38, 14, 10, 25, 52, 53, 37, 43,  1, 42, 34, 27, 24, 60, 33,
       23, 62, 20, 56, 32, 45, 29, 63,  8, 31,  3,  7, 12, 41,  2,  4,  0,
       11, 35, 21, 26, 15, 55, 47, 44,  9, 16, 54,  5, 28, 65, 57, 39, 22,
       46,  6, 40, 48, 17, 36, 58, 49, 19, 61, 51, 30, 59, 64, 50],
      dtype=int64), 'cur_cost': 111518.0}, {'tour': [47, 27, 23, 52, 19, 18, 45, 11, 38, 61, 62, 28, 30, 63, 53, 65, 64, 22, 0, 54, 3, 36, 17, 10, 56, 31, 4, 12, 60, 58, 9, 7, 33, 8, 48, 46, 2, 20, 6, 25, 37, 41, 55, 39, 26, 24, 21, 40, 34, 49, 57, 44, 1, 32, 59, 42, 13, 14, 15, 35, 5, 16, 29, 51, 50, 43], 'cur_cost': 98178.0}, {'tour': [8, 11, 53, 15, 12, 20, 4, 58, 0, 57, 18, 9, 19, 33, 24, 17, 16, 2, 37, 34, 25, 28, 29, 14, 10, 1, 55, 22, 5, 3, 6, 36, 31, 49, 51, 50, 23, 46, 40, 42, 48, 43, 26, 47, 38, 35, 21, 39, 45, 44, 56, 62, 60, 64, 59, 63, 7, 52, 65, 13, 32, 30, 27, 41, 61, 54], 'cur_cost': 55630.0}, {'tour': array([53, 42, 17, 20, 16, 32, 62, 44, 26, 37, 41, 10,  4, 43, 19, 47, 57,
       14, 65, 56, 40, 50, 35, 39, 33, 51,  9, 11, 38, 30,  7, 18, 13, 48,
       46, 55, 49, 54, 34,  0, 15, 36, 23,  6, 24, 61, 29, 21,  8, 25, 31,
       52,  3, 27,  1, 12, 28, 58, 60, 59, 63, 22,  2, 64,  5, 45],
      dtype=int64), 'cur_cost': 108234.0}, {'tour': array([62, 59, 10, 25, 14, 23, 53, 11, 22,  0, 32, 64, 20,  4, 51, 38, 37,
       41, 39, 65, 34, 36, 57, 55, 28, 46,  6, 54, 56, 26, 49, 52, 27, 19,
       24, 44,  5, 31, 17, 33, 40, 35, 18, 61,  2, 47, 45, 13, 60, 43, 15,
        3,  1,  8, 21, 42, 30, 16,  7, 29, 58, 50,  9, 48, 12, 63],
      dtype=int64), 'cur_cost': 107440.0}, {'tour': array([21, 63, 33, 31, 16, 54,  6, 12, 30,  9, 22,  1, 49,  5, 14, 20, 32,
       36, 47, 38, 24, 48,  4, 58, 15, 18, 17, 40, 23, 13, 46, 26, 65, 28,
        0, 43, 41, 34, 64, 44,  7, 10, 60, 27,  8,  3, 29, 35, 39, 52, 57,
       25, 55, 37, 62, 56, 59, 42,  2, 11, 19, 61, 50, 53, 45, 51],
      dtype=int64), 'cur_cost': 106193.0}, {'tour': array([17, 19,  4, 35, 60, 41, 30, 32, 18, 27, 28,  6, 15, 25, 40, 53, 64,
       42, 11, 47, 21, 45, 49, 10, 52, 39,  3, 37, 44, 31, 61, 46, 63, 48,
       29, 50, 51, 24, 59,  7,  5, 38,  2, 54, 12, 20,  9, 36, 43,  8, 62,
       16, 65, 57, 58, 33, 22, 56,  1, 14, 55, 13, 34, 26, 23,  0],
      dtype=int64), 'cur_cost': 108782.0}]
2025-08-03 16:49:18,412 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 16:49:18,413 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-03 16:49:18,415 - experts.management.collaboration_manager - INFO - 个体 16 利用路径生成报告: {'new_tour': array([53, 42, 17, 20, 16, 32, 62, 44, 26, 37, 41, 10,  4, 43, 19, 47, 57,
       14, 65, 56, 40, 50, 35, 39, 33, 51,  9, 11, 38, 30,  7, 18, 13, 48,
       46, 55, 49, 54, 34,  0, 15, 36, 23,  6, 24, 61, 29, 21,  8, 25, 31,
       52,  3, 27,  1, 12, 28, 58, 60, 59, 63, 22,  2, 64,  5, 45],
      dtype=int64), 'cur_cost': 108234.0}
2025-08-03 16:49:18,418 - experts.management.collaboration_manager - INFO - 个体 16 接受新路径 (成本: 108234.00)
2025-08-03 16:49:18,418 - experts.management.collaboration_manager - INFO - 为个体 17 生成探索路径
2025-08-03 16:49:18,419 - ExplorationExpert - INFO - 开始为个体 17 生成探索路径（算法实现）
2025-08-03 16:49:18,419 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:49:18,425 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:49:18,426 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:49:18,426 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12810.0, 路径长度: 66
2025-08-03 16:49:18,426 - experts.management.collaboration_manager - INFO - 个体 17 探索路径生成报告: {'new_tour': [0, 12, 5, 7, 3, 9, 11, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12810.0}
2025-08-03 16:49:18,427 - experts.management.collaboration_manager - INFO - 个体 17 接受新路径 (成本: 12810.00)
2025-08-03 16:49:18,428 - experts.management.collaboration_manager - INFO - 为个体 18 生成探索路径
2025-08-03 16:49:18,428 - ExplorationExpert - INFO - 开始为个体 18 生成探索路径（算法实现）
2025-08-03 16:49:18,428 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:49:18,442 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:49:18,442 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:49:18,442 - ExplorationExpert - INFO - 探索路径生成完成，成本: 59430.0, 路径长度: 66
2025-08-03 16:49:18,442 - experts.management.collaboration_manager - INFO - 个体 18 探索路径生成报告: {'new_tour': [22, 33, 14, 17, 19, 12, 8, 61, 57, 56, 60, 23, 26, 1, 7, 62, 2, 16, 4, 6, 37, 20, 5, 18, 24, 28, 35, 10, 63, 11, 55, 65, 15, 27, 13, 36, 34, 40, 50, 45, 46, 25, 30, 21, 49, 42, 41, 32, 9, 64, 52, 54, 0, 58, 3, 29, 43, 48, 47, 38, 39, 51, 44, 31, 59, 53], 'cur_cost': 59430.0}
2025-08-03 16:49:18,443 - experts.management.collaboration_manager - INFO - 个体 18 接受新路径 (成本: 59430.00)
2025-08-03 16:49:18,443 - experts.management.collaboration_manager - INFO - 为个体 19 生成利用路径
2025-08-03 16:49:18,443 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:49:18,444 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:49:18,445 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 19 处的路径，新成本: 96790.0
2025-08-03 16:49:18,535 - ExploitationExpert - INFO - res_population_num: 13
2025-08-03 16:49:18,535 - ExploitationExpert - INFO - res_population_costs: [9857.0, 9610.0, 9548.0, 9548, 9546, 9540, 9539, 9536, 9530, 9530, 9521, 9521, 9521]
2025-08-03 16:49:18,535 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24, 29, 32, 34, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 63, 52, 65, 54, 57,
       64, 60, 58, 56, 59, 62, 53, 61, 55,  5,  4,  6,  2,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28, 30, 35, 34, 40, 43,
       48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59,
       56, 58, 60, 54, 57, 64, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 41, 38, 51, 50, 45, 44, 39, 47, 49, 40, 43, 48, 46,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 42, 46,
       48, 43, 40, 49, 47, 39, 44, 45, 50, 51, 38, 41, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34,
       35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 13, 21, 20,
       14, 15, 22, 23, 16, 18, 12, 17,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4, 17, 12, 18, 16, 23, 22, 15, 14, 20,
       21, 13, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34,
       40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59,
       62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55,  6,  2,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:49:18,542 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:49:18,542 - ExploitationExpert - INFO - populations: [{'tour': [21, 17, 5, 53, 3, 12, 6, 23, 1, 11, 64, 10, 18, 28, 14, 24, 26, 29, 7, 13, 16, 15, 35, 20, 33, 37, 49, 42, 51, 50, 36, 30, 27, 43, 22, 46, 31, 45, 25, 47, 44, 19, 40, 9, 56, 60, 52, 58, 54, 59, 61, 8, 4, 55, 65, 62, 0, 57, 63, 2, 32, 48, 39, 41, 38, 34], 'cur_cost': 60086.0}, {'tour': array([17, 12, 31, 27, 60,  7, 41,  8,  6, 10, 23,  2, 56, 54, 15, 30,  9,
       50, 39, 58, 45, 53,  4, 20, 24,  1, 42, 37, 21, 65, 46, 63, 11, 48,
       25, 29, 36, 26, 55, 16, 19, 13, 51, 49, 32,  5, 38, 14, 34, 35, 44,
       47, 61, 22, 33, 64, 57,  0, 62, 18, 52, 43, 40,  3, 59, 28],
      dtype=int64), 'cur_cost': 98459.0}, {'tour': [53, 29, 13, 33, 43, 22, 47, 55, 27, 2, 32, 1, 17, 19, 18, 45, 0, 52, 11, 15, 36, 65, 23, 63, 58, 21, 24, 4, 34, 42, 16, 48, 10, 56, 39, 51, 28, 31, 12, 44, 60, 30, 59, 3, 9, 38, 41, 20, 37, 40, 49, 54, 26, 35, 50, 64, 6, 7, 46, 8, 62, 5, 61, 57, 14, 25], 'cur_cost': 105174.0}, {'tour': [53, 47, 27, 37, 34, 50, 56, 45, 44, 9, 38, 61, 8, 55, 0, 63, 64, 17, 14, 3, 40, 29, 13, 10, 42, 51, 22, 2, 62, 21, 26, 52, 20, 12, 46, 57, 60, 4, 30, 41, 54, 58, 32, 35, 24, 23, 39, 1, 16, 59, 19, 36, 7, 33, 18, 11, 28, 48, 31, 6, 15, 43, 49, 65, 5, 25], 'cur_cost': 102337.0}, {'tour': array([43, 61, 26, 20, 37, 12, 53, 33,  6, 65,  9, 27, 59, 24, 28, 57, 11,
        4, 41, 40, 55, 16, 42, 39,  1, 60, 52, 63, 10, 47, 58,  0, 38, 50,
       21, 22,  8, 13,  3,  2, 18, 23, 19, 51, 25, 32, 35, 46, 62, 44, 36,
       31, 48, 30,  5, 34, 56, 45,  7, 64, 15, 29, 14, 54, 49, 17],
      dtype=int64), 'cur_cost': 102389.0}, {'tour': [24, 4, 14, 36, 21, 19, 25, 32, 8, 22, 28, 2, 15, 5, 23, 27, 12, 9, 53, 63, 18, 43, 37, 20, 35, 0, 61, 55, 54, 3, 57, 39, 42, 38, 13, 17, 40, 46, 50, 31, 34, 7, 64, 56, 65, 59, 1, 52, 60, 62, 11, 16, 26, 6, 10, 58, 47, 44, 51, 48, 49, 45, 41, 30, 33, 29], 'cur_cost': 60417.0}, {'tour': [47, 14, 36, 23, 10, 17, 28, 18, 45, 29, 35, 22, 62, 50, 30, 43, 12, 54, 59, 65, 38, 52, 40, 61, 7, 55, 11, 0, 48, 31, 3, 15, 60, 39, 6, 26, 37, 21, 56, 53, 25, 13, 34, 32, 41, 16, 5, 49, 4, 63, 57, 58, 42, 1, 2, 8, 24, 9, 20, 19, 64, 51, 46, 44, 27, 33], 'cur_cost': 96999.0}, {'tour': array([60, 23, 55, 25, 21,  9, 11,  3, 13, 44, 64, 28, 16, 50, 41, 43, 48,
       18, 42, 65,  0, 31, 61, 30, 40, 22, 58, 56,  8,  1, 37, 63, 32, 12,
       47, 39,  4, 29,  6, 59, 49,  2, 14, 51, 53,  5, 45, 27, 57, 36, 52,
       15, 17, 20,  7, 19, 33, 10, 54, 24, 34, 26, 38, 35, 62, 46],
      dtype=int64), 'cur_cost': 109664.0}, {'tour': [0, 16, 18, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12746.0}, {'tour': [55, 1, 2, 5, 52, 54, 3, 65, 13, 28, 17, 24, 36, 16, 32, 12, 25, 26, 6, 61, 57, 11, 56, 19, 33, 18, 35, 22, 30, 27, 20, 15, 21, 40, 43, 50, 39, 47, 51, 23, 37, 29, 4, 8, 64, 58, 0, 63, 9, 59, 14, 46, 49, 45, 42, 34, 10, 53, 7, 31, 48, 44, 41, 38, 60, 62], 'cur_cost': 57721.0}, {'tour': array([55, 38, 29,  8, 20, 36, 64, 19, 27,  4, 22,  6, 44, 57, 65,  5, 51,
       61, 17, 21, 48, 49, 16, 59, 14, 25, 58, 52, 63, 37, 40, 41, 50, 60,
       34, 39, 18,  3, 56, 13, 23, 31,  1, 30, 46,  0, 24, 32,  7, 42, 26,
       11, 12,  2, 45, 47, 54, 33, 62, 10, 43,  9, 15, 28, 35, 53],
      dtype=int64), 'cur_cost': 108847.0}, {'tour': [59, 9, 11, 60, 18, 30, 32, 5, 22, 17, 23, 19, 36, 35, 33, 25, 4, 3, 16, 8, 12, 37, 48, 40, 47, 28, 43, 42, 49, 38, 21, 15, 20, 27, 44, 39, 7, 55, 53, 65, 64, 57, 0, 63, 54, 14, 10, 61, 46, 41, 13, 2, 56, 58, 52, 6, 62, 45, 26, 31, 34, 29, 1, 24, 50, 51], 'cur_cost': 61086.0}, {'tour': [0, 16, 2, 1, 7, 3, 9, 11, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12864.0}, {'tour': array([18, 13, 38, 14, 10, 25, 52, 53, 37, 43,  1, 42, 34, 27, 24, 60, 33,
       23, 62, 20, 56, 32, 45, 29, 63,  8, 31,  3,  7, 12, 41,  2,  4,  0,
       11, 35, 21, 26, 15, 55, 47, 44,  9, 16, 54,  5, 28, 65, 57, 39, 22,
       46,  6, 40, 48, 17, 36, 58, 49, 19, 61, 51, 30, 59, 64, 50],
      dtype=int64), 'cur_cost': 111518.0}, {'tour': [47, 27, 23, 52, 19, 18, 45, 11, 38, 61, 62, 28, 30, 63, 53, 65, 64, 22, 0, 54, 3, 36, 17, 10, 56, 31, 4, 12, 60, 58, 9, 7, 33, 8, 48, 46, 2, 20, 6, 25, 37, 41, 55, 39, 26, 24, 21, 40, 34, 49, 57, 44, 1, 32, 59, 42, 13, 14, 15, 35, 5, 16, 29, 51, 50, 43], 'cur_cost': 98178.0}, {'tour': [8, 11, 53, 15, 12, 20, 4, 58, 0, 57, 18, 9, 19, 33, 24, 17, 16, 2, 37, 34, 25, 28, 29, 14, 10, 1, 55, 22, 5, 3, 6, 36, 31, 49, 51, 50, 23, 46, 40, 42, 48, 43, 26, 47, 38, 35, 21, 39, 45, 44, 56, 62, 60, 64, 59, 63, 7, 52, 65, 13, 32, 30, 27, 41, 61, 54], 'cur_cost': 55630.0}, {'tour': array([53, 42, 17, 20, 16, 32, 62, 44, 26, 37, 41, 10,  4, 43, 19, 47, 57,
       14, 65, 56, 40, 50, 35, 39, 33, 51,  9, 11, 38, 30,  7, 18, 13, 48,
       46, 55, 49, 54, 34,  0, 15, 36, 23,  6, 24, 61, 29, 21,  8, 25, 31,
       52,  3, 27,  1, 12, 28, 58, 60, 59, 63, 22,  2, 64,  5, 45],
      dtype=int64), 'cur_cost': 108234.0}, {'tour': [0, 12, 5, 7, 3, 9, 11, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12810.0}, {'tour': [22, 33, 14, 17, 19, 12, 8, 61, 57, 56, 60, 23, 26, 1, 7, 62, 2, 16, 4, 6, 37, 20, 5, 18, 24, 28, 35, 10, 63, 11, 55, 65, 15, 27, 13, 36, 34, 40, 50, 45, 46, 25, 30, 21, 49, 42, 41, 32, 9, 64, 52, 54, 0, 58, 3, 29, 43, 48, 47, 38, 39, 51, 44, 31, 59, 53], 'cur_cost': 59430.0}, {'tour': array([ 0, 33, 65, 57,  7,  3, 46, 30,  1, 22, 13,  9, 16, 32, 14, 50,  4,
       58, 54, 34, 10, 20, 26, 37, 59, 61, 21, 25, 51, 64, 18, 29, 44, 28,
       35,  6, 23, 15, 24, 45, 36,  5, 42, 47, 60,  2, 55, 62, 27, 63, 31,
       41, 43, 49,  8, 12, 53, 11, 56, 17, 52, 38, 39, 48, 40, 19],
      dtype=int64), 'cur_cost': 96790.0}]
2025-08-03 16:49:18,552 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒
2025-08-03 16:49:18,553 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-03 16:49:18,554 - experts.management.collaboration_manager - INFO - 个体 19 利用路径生成报告: {'new_tour': array([ 0, 33, 65, 57,  7,  3, 46, 30,  1, 22, 13,  9, 16, 32, 14, 50,  4,
       58, 54, 34, 10, 20, 26, 37, 59, 61, 21, 25, 51, 64, 18, 29, 44, 28,
       35,  6, 23, 15, 24, 45, 36,  5, 42, 47, 60,  2, 55, 62, 27, 63, 31,
       41, 43, 49,  8, 12, 53, 11, 56, 17, 52, 38, 39, 48, 40, 19],
      dtype=int64), 'cur_cost': 96790.0}
2025-08-03 16:49:18,554 - experts.management.collaboration_manager - INFO - 个体 19 接受新路径 (成本: 96790.00)
2025-08-03 16:49:18,555 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 16, 'rejected': 4, 'elite_protected': 4}
2025-08-03 16:49:18,555 - experts.management.collaboration_manager - INFO - 接受率: 16/20 (80.0%)
2025-08-03 16:49:18,556 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [21, 17, 5, 53, 3, 12, 6, 23, 1, 11, 64, 10, 18, 28, 14, 24, 26, 29, 7, 13, 16, 15, 35, 20, 33, 37, 49, 42, 51, 50, 36, 30, 27, 43, 22, 46, 31, 45, 25, 47, 44, 19, 40, 9, 56, 60, 52, 58, 54, 59, 61, 8, 4, 55, 65, 62, 0, 57, 63, 2, 32, 48, 39, 41, 38, 34], 'cur_cost': 60086.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([17, 12, 31, 27, 60,  7, 41,  8,  6, 10, 23,  2, 56, 54, 15, 30,  9,
       50, 39, 58, 45, 53,  4, 20, 24,  1, 42, 37, 21, 65, 46, 63, 11, 48,
       25, 29, 36, 26, 55, 16, 19, 13, 51, 49, 32,  5, 38, 14, 34, 35, 44,
       47, 61, 22, 33, 64, 57,  0, 62, 18, 52, 43, 40,  3, 59, 28],
      dtype=int64), 'cur_cost': 98459.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [53, 29, 13, 33, 43, 22, 47, 55, 27, 2, 32, 1, 17, 19, 18, 45, 0, 52, 11, 15, 36, 65, 23, 63, 58, 21, 24, 4, 34, 42, 16, 48, 10, 56, 39, 51, 28, 31, 12, 44, 60, 30, 59, 3, 9, 38, 41, 20, 37, 40, 49, 54, 26, 35, 50, 64, 6, 7, 46, 8, 62, 5, 61, 57, 14, 25], 'cur_cost': 105174.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [53, 47, 27, 37, 34, 50, 56, 45, 44, 9, 38, 61, 8, 55, 0, 63, 64, 17, 14, 3, 40, 29, 13, 10, 42, 51, 22, 2, 62, 21, 26, 52, 20, 12, 46, 57, 60, 4, 30, 41, 54, 58, 32, 35, 24, 23, 39, 1, 16, 59, 19, 36, 7, 33, 18, 11, 28, 48, 31, 6, 15, 43, 49, 65, 5, 25], 'cur_cost': 102337.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([43, 61, 26, 20, 37, 12, 53, 33,  6, 65,  9, 27, 59, 24, 28, 57, 11,
        4, 41, 40, 55, 16, 42, 39,  1, 60, 52, 63, 10, 47, 58,  0, 38, 50,
       21, 22,  8, 13,  3,  2, 18, 23, 19, 51, 25, 32, 35, 46, 62, 44, 36,
       31, 48, 30,  5, 34, 56, 45,  7, 64, 15, 29, 14, 54, 49, 17],
      dtype=int64), 'cur_cost': 102389.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [24, 4, 14, 36, 21, 19, 25, 32, 8, 22, 28, 2, 15, 5, 23, 27, 12, 9, 53, 63, 18, 43, 37, 20, 35, 0, 61, 55, 54, 3, 57, 39, 42, 38, 13, 17, 40, 46, 50, 31, 34, 7, 64, 56, 65, 59, 1, 52, 60, 62, 11, 16, 26, 6, 10, 58, 47, 44, 51, 48, 49, 45, 41, 30, 33, 29], 'cur_cost': 60417.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [47, 14, 36, 23, 10, 17, 28, 18, 45, 29, 35, 22, 62, 50, 30, 43, 12, 54, 59, 65, 38, 52, 40, 61, 7, 55, 11, 0, 48, 31, 3, 15, 60, 39, 6, 26, 37, 21, 56, 53, 25, 13, 34, 32, 41, 16, 5, 49, 4, 63, 57, 58, 42, 1, 2, 8, 24, 9, 20, 19, 64, 51, 46, 44, 27, 33], 'cur_cost': 96999.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([60, 23, 55, 25, 21,  9, 11,  3, 13, 44, 64, 28, 16, 50, 41, 43, 48,
       18, 42, 65,  0, 31, 61, 30, 40, 22, 58, 56,  8,  1, 37, 63, 32, 12,
       47, 39,  4, 29,  6, 59, 49,  2, 14, 51, 53,  5, 45, 27, 57, 36, 52,
       15, 17, 20,  7, 19, 33, 10, 54, 24, 34, 26, 38, 35, 62, 46],
      dtype=int64), 'cur_cost': 109664.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 18, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12746.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [55, 1, 2, 5, 52, 54, 3, 65, 13, 28, 17, 24, 36, 16, 32, 12, 25, 26, 6, 61, 57, 11, 56, 19, 33, 18, 35, 22, 30, 27, 20, 15, 21, 40, 43, 50, 39, 47, 51, 23, 37, 29, 4, 8, 64, 58, 0, 63, 9, 59, 14, 46, 49, 45, 42, 34, 10, 53, 7, 31, 48, 44, 41, 38, 60, 62], 'cur_cost': 57721.0}}, {'individual': 10, 'strategy': 'exploit', 'path_data': {'new_tour': array([55, 38, 29,  8, 20, 36, 64, 19, 27,  4, 22,  6, 44, 57, 65,  5, 51,
       61, 17, 21, 48, 49, 16, 59, 14, 25, 58, 52, 63, 37, 40, 41, 50, 60,
       34, 39, 18,  3, 56, 13, 23, 31,  1, 30, 46,  0, 24, 32,  7, 42, 26,
       11, 12,  2, 45, 47, 54, 33, 62, 10, 43,  9, 15, 28, 35, 53],
      dtype=int64), 'cur_cost': 108847.0}}, {'individual': 11, 'strategy': 'explore', 'path_data': {'new_tour': [59, 9, 11, 60, 18, 30, 32, 5, 22, 17, 23, 19, 36, 35, 33, 25, 4, 3, 16, 8, 12, 37, 48, 40, 47, 28, 43, 42, 49, 38, 21, 15, 20, 27, 44, 39, 7, 55, 53, 65, 64, 57, 0, 63, 54, 14, 10, 61, 46, 41, 13, 2, 56, 58, 52, 6, 62, 45, 26, 31, 34, 29, 1, 24, 50, 51], 'cur_cost': 61086.0}}, {'individual': 12, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 2, 1, 7, 3, 9, 11, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12864.0}}, {'individual': 13, 'strategy': 'exploit', 'path_data': {'new_tour': array([18, 13, 38, 14, 10, 25, 52, 53, 37, 43,  1, 42, 34, 27, 24, 60, 33,
       23, 62, 20, 56, 32, 45, 29, 63,  8, 31,  3,  7, 12, 41,  2,  4,  0,
       11, 35, 21, 26, 15, 55, 47, 44,  9, 16, 54,  5, 28, 65, 57, 39, 22,
       46,  6, 40, 48, 17, 36, 58, 49, 19, 61, 51, 30, 59, 64, 50],
      dtype=int64), 'cur_cost': 111518.0}}, {'individual': 14, 'strategy': 'explore', 'path_data': {'new_tour': [47, 27, 23, 52, 19, 18, 45, 11, 38, 61, 62, 28, 30, 63, 53, 65, 64, 22, 0, 54, 3, 36, 17, 10, 56, 31, 4, 12, 60, 58, 9, 7, 33, 8, 48, 46, 2, 20, 6, 25, 37, 41, 55, 39, 26, 24, 21, 40, 34, 49, 57, 44, 1, 32, 59, 42, 13, 14, 15, 35, 5, 16, 29, 51, 50, 43], 'cur_cost': 98178.0}}, {'individual': 15, 'strategy': 'explore', 'path_data': {'new_tour': [8, 11, 53, 15, 12, 20, 4, 58, 0, 57, 18, 9, 19, 33, 24, 17, 16, 2, 37, 34, 25, 28, 29, 14, 10, 1, 55, 22, 5, 3, 6, 36, 31, 49, 51, 50, 23, 46, 40, 42, 48, 43, 26, 47, 38, 35, 21, 39, 45, 44, 56, 62, 60, 64, 59, 63, 7, 52, 65, 13, 32, 30, 27, 41, 61, 54], 'cur_cost': 55630.0}}, {'individual': 16, 'strategy': 'exploit', 'path_data': {'new_tour': array([53, 42, 17, 20, 16, 32, 62, 44, 26, 37, 41, 10,  4, 43, 19, 47, 57,
       14, 65, 56, 40, 50, 35, 39, 33, 51,  9, 11, 38, 30,  7, 18, 13, 48,
       46, 55, 49, 54, 34,  0, 15, 36, 23,  6, 24, 61, 29, 21,  8, 25, 31,
       52,  3, 27,  1, 12, 28, 58, 60, 59, 63, 22,  2, 64,  5, 45],
      dtype=int64), 'cur_cost': 108234.0}}, {'individual': 17, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 7, 3, 9, 11, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12810.0}}, {'individual': 18, 'strategy': 'explore', 'path_data': {'new_tour': [22, 33, 14, 17, 19, 12, 8, 61, 57, 56, 60, 23, 26, 1, 7, 62, 2, 16, 4, 6, 37, 20, 5, 18, 24, 28, 35, 10, 63, 11, 55, 65, 15, 27, 13, 36, 34, 40, 50, 45, 46, 25, 30, 21, 49, 42, 41, 32, 9, 64, 52, 54, 0, 58, 3, 29, 43, 48, 47, 38, 39, 51, 44, 31, 59, 53], 'cur_cost': 59430.0}}, {'individual': 19, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 0, 33, 65, 57,  7,  3, 46, 30,  1, 22, 13,  9, 16, 32, 14, 50,  4,
       58, 54, 34, 10, 20, 26, 37, 59, 61, 21, 25, 51, 64, 18, 29, 44, 28,
       35,  6, 23, 15, 24, 45, 36,  5, 42, 47, 60,  2, 55, 62, 27, 63, 31,
       41, 43, 49,  8, 12, 53, 11, 56, 17, 52, 38, 39, 48, 40, 19],
      dtype=int64), 'cur_cost': 96790.0}}]
2025-08-03 16:49:18,559 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-03 16:49:18,560 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:49:18,572 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=12746.000, 多样性=0.975
2025-08-03 16:49:18,572 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-03 16:49:18,572 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-03 16:49:18,573 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-03 16:49:18,575 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 1, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.08968965307078468, 'best_improvement': -0.2928288873110863}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -8.176614881425657e-05}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 13, 'new_count': 13, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.8302253302253302, 'new_diversity': 0.8302253302253302, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-03 16:49:18,579 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-03 16:49:18,589 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_solution.json
2025-08-03 16:49:18,590 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_20250803_164918.solution
2025-08-03 16:49:18,590 - __main__ - INFO - 实例 composite13_66 处理完成
