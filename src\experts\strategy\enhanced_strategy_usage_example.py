# -*- coding: utf-8 -*-
"""
增强策略专家使用示例

演示如何使用增强策略专家进行个体级策略选择，包括配置、初始化、分析和结果处理。
"""

import logging
import json
import sys
import os
from typing import Dict, List, Any

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from experts.strategy.enhanced_strategy_expert import EnhancedStrategyExpert
# from experts.management.collaboration_manager import ExpertCollaborationManager


def create_sample_populations(size: int = 10) -> List[Dict]:
    """创建示例种群数据"""
    import random
    
    populations = []
    for i in range(size):
        # 模拟不同适应度水平的个体
        base_fitness = 100
        if i < size * 0.1:  # 前10%为优秀个体
            fitness = base_fitness + random.uniform(-5, 0)
        elif i < size * 0.3:  # 前30%为良好个体
            fitness = base_fitness + random.uniform(0, 10)
        else:  # 其余为普通个体
            fitness = base_fitness + random.uniform(10, 30)
        
        # 生成随机路径
        tour = list(range(20))  # 假设20个城市
        random.shuffle(tour)
        
        population = {
            'cur_cost': fitness,
            'tour': tour,
            'individual_id': i
        }
        populations.append(population)
    
    return populations


def create_sample_landscape_report() -> Dict[str, Any]:
    """创建示例景观报告"""
    return {
        'global_ruggedness': 0.68,
        'modality': 'multi_modal',
        'deceptiveness': 'high',
        'gradient_strength': 0.45,
        'population_diversity': 0.32,
        'convergence_trend': 0.65,
        'evolution_phase': 'balance',
        'difficult_regions': [
            {'center': [5, 8], 'radius': 0.12, 'difficulty': 0.8},
            {'center': [12, 15], 'radius': 0.08, 'difficulty': 0.9}
        ],
        'opportunity_regions': [
            {'center': [2, 3], 'radius': 0.15, 'potential': 0.7},
            {'center': [18, 19], 'radius': 0.10, 'potential': 0.6}
        ],
        'local_optima_count': 12,
        'search_space_coverage': 0.28,
        'fitness_variance': 156.7,
        'convergence_rate': 0.15
    }


class MockLLMInterface:
    """模拟LLM接口"""
    
    def __init__(self, response_type: str = 'success'):
        self.response_type = response_type
        self.call_count = 0
    
    def get_response(self, prompt: str) -> str:
        """模拟LLM响应"""
        self.call_count += 1
        
        if self.response_type == 'success':
            return self._generate_success_response()
        elif self.response_type == 'partial_error':
            return self._generate_partial_error_response()
        elif self.response_type == 'failure':
            raise Exception("LLM调用失败")
        else:
            return "无效响应"
    
    def _generate_success_response(self) -> str:
        """生成成功响应"""
        return """
        {
          "strategy_assignments": [
            {
              "individual_id": 0,
              "strategy_type": "aggressive_exploitation",
              "confidence": 0.92,
              "reasoning": "顶级个体(百分位0.0)且无停滞，景观梯度中等(0.45)，适合激进开发策略深度优化",
              "priority": 0.95,
              "expected_improvement": 0.18,
              "parameters": {
                "local_search_depth": 4,
                "elite_influence": 0.9,
                "perturbation_strength": 0.15
              }
            },
            {
              "individual_id": 1,
              "strategy_type": "moderate_exploitation",
              "confidence": 0.85,
              "reasoning": "优秀个体(百分位0.1)表现稳定，适合中等开发策略持续优化",
              "priority": 0.8,
              "expected_improvement": 0.12,
              "parameters": {
                "local_search_depth": 3,
                "elite_influence": 0.7,
                "perturbation_strength": 0.2
              }
            },
            {
              "individual_id": 2,
              "strategy_type": "balanced_exploration",
              "confidence": 0.78,
              "reasoning": "中等个体(百分位0.4)存在轻微停滞，多样性贡献中等，需要平衡探索",
              "priority": 0.6,
              "expected_improvement": 0.08,
              "parameters": {
                "exploration_radius": 0.25,
                "diversification_strength": 0.6,
                "random_component": 0.3
              }
            },
            {
              "individual_id": 3,
              "strategy_type": "intelligent_exploration",
              "confidence": 0.72,
              "reasoning": "中等个体(百分位0.5)在机会区域附近，可以尝试智能探索",
              "priority": 0.65,
              "expected_improvement": 0.10,
              "parameters": {
                "gradient_following": 0.4,
                "opportunity_bias": 0.7,
                "adaptive_step": 0.3
              }
            },
            {
              "individual_id": 4,
              "strategy_type": "strong_exploration",
              "confidence": 0.88,
              "reasoning": "普通个体(百分位0.7)存在中等停滞(3代)，多样性贡献低，需要强探索突破",
              "priority": 0.7,
              "expected_improvement": 0.15,
              "parameters": {
                "exploration_radius": 0.4,
                "diversification_strength": 0.8,
                "random_component": 0.5
              }
            }
          ],
          "global_analysis": {
            "exploration_ratio": 0.6,
            "exploitation_ratio": 0.4,
            "key_insights": "种群多样性较低(0.32)且存在多个停滞个体，需要增强探索策略。景观粗糙度较高(0.68)且欺骗性强，建议采用智能探索避免陷阱。",
            "risk_assessment": "中高风险：景观复杂且种群收敛趋势明显(0.65)，需要平衡探索开发以避免早熟收敛。",
            "coordination_strategy": "采用分层策略：前20%个体专注开发，中间30%平衡策略，后50%强化探索。重点关注机会区域的开发。",
            "resource_allocation": "高优先级个体分配60%计算资源，确保精英解持续优化。探索个体分配40%资源用于多样性维护。"
          }
        }
        """
    
    def _generate_partial_error_response(self) -> str:
        """生成部分错误响应"""
        return """
        {
          "strategy_assignments": [
            {
              "individual_id": 0,
              "strategy_type": "invalid_strategy",
              "confidence": 1.5,
              "reasoning": "测试无效策略类型"
            }
          ],
          "global_analysis": {
            "exploration_ratio": 0.6,
            "key_insights": "部分错误的响应"
          }
        }
        """


def demonstrate_basic_usage():
    """演示基本使用方法"""
    print("=== 增强策略专家基本使用演示 ===\n")
    
    # 1. 配置增强策略专家
    config = {
        'enable_llm_reasoning': True,
        'fallback_to_algorithmic': True,
        'max_llm_retries': 3,
        'state_analyzer': {
            'history_window': 15,
            'improvement_threshold': 1e-5,
            'diversity_method': 'hamming'
        },
        'response_parser': {
            'strict_validation': False,
            'auto_repair': True,
            'default_confidence': 0.6,
            'default_priority': 0.5
        }
    }
    
    # 2. 创建增强策略专家实例
    enhanced_expert = EnhancedStrategyExpert(config)
    
    # 3. 设置模拟LLM接口
    mock_llm = MockLLMInterface('success')
    enhanced_expert.interface_llm = mock_llm
    
    # 4. 准备测试数据
    populations = create_sample_populations(5)
    landscape_report = create_sample_landscape_report()
    
    print("种群信息:")
    for i, pop in enumerate(populations):
        print(f"  个体 {i}: 适应度 {pop['cur_cost']:.2f}")
    
    print(f"\n景观特征:")
    print(f"  全局粗糙度: {landscape_report['global_ruggedness']:.3f}")
    print(f"  种群多样性: {landscape_report['population_diversity']:.3f}")
    print(f"  收敛趋势: {landscape_report['convergence_trend']:.3f}")
    print(f"  进化阶段: {landscape_report['evolution_phase']}")
    
    # 5. 执行策略分析
    print("\n执行策略分析...")
    strategy_list, detailed_report = enhanced_expert.analyze(
        populations=populations,
        landscape_report=landscape_report,
        iteration=15,
        strategy_feedback={'summary': '上次策略效果良好，继续优化'}
    )
    
    # 6. 显示结果
    print(f"\n策略分配结果:")
    for i, strategy in enumerate(strategy_list):
        print(f"  个体 {i}: {strategy}")
    
    print(f"\n详细报告:")
    print(detailed_report)
    
    print(f"\nLLM调用次数: {mock_llm.call_count}")


def demonstrate_error_handling():
    """演示错误处理机制"""
    print("\n=== 错误处理机制演示 ===\n")
    
    config = {
        'enable_llm_reasoning': True,
        'fallback_to_algorithmic': True,
        'max_llm_retries': 2
    }
    
    enhanced_expert = EnhancedStrategyExpert(config)
    
    # 测试LLM失败回退
    print("1. 测试LLM调用失败回退到算法方法:")
    mock_llm_fail = MockLLMInterface('failure')
    enhanced_expert.interface_llm = mock_llm_fail
    
    populations = create_sample_populations(3)
    landscape_report = create_sample_landscape_report()
    
    strategy_list, report = enhanced_expert.analyze(
        populations=populations,
        landscape_report=landscape_report,
        iteration=5
    )
    
    print(f"  回退成功，获得 {len(strategy_list)} 个策略分配")
    print(f"  策略: {strategy_list}")
    
    # 测试部分错误响应修复
    print("\n2. 测试部分错误响应自动修复:")
    mock_llm_partial = MockLLMInterface('partial_error')
    enhanced_expert.interface_llm = mock_llm_partial
    
    strategy_list, report = enhanced_expert.analyze(
        populations=populations,
        landscape_report=landscape_report,
        iteration=5
    )
    
    print(f"  修复成功，获得 {len(strategy_list)} 个策略分配")
    print(f"  策略: {strategy_list}")


def demonstrate_integration_with_collaboration_manager():
    """演示与协作管理器的集成"""
    print("\n=== 与协作管理器集成演示 ===\n")
    
    # 注意：这里只是演示集成概念，实际使用需要完整的协作管理器
    print("集成步骤:")
    print("1. 在协作管理器中注册增强策略专家")
    print("2. 在策略阶段调用增强策略专家")
    print("3. 将策略分配结果传递给进化阶段")
    
    # 模拟集成代码
    integration_code = '''
    # 在协作管理器中的集成示例
    class ExpertCollaborationManager:
        def __init__(self):
            self.enhanced_strategy_expert = EnhancedStrategyExpert(config)
            
        def run_strategy_phase(self, populations, landscape_report, iteration):
            # 使用增强策略专家进行策略选择
            strategy_assignments, report = self.enhanced_strategy_expert.analyze(
                populations=populations,
                landscape_report=landscape_report,
                iteration=iteration
            )
            
            # 将结果传递给进化阶段
            return {
                'strategy_assignments': strategy_assignments,
                'detailed_report': report,
                'phase': 'strategy_completed'
            }
    '''
    
    print("集成代码示例:")
    print(integration_code)


def main():
    """主函数"""
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    print("增强策略专家使用示例")
    print("=" * 50)
    
    # 演示基本使用
    demonstrate_basic_usage()
    
    # 演示错误处理
    demonstrate_error_handling()
    
    # 演示集成
    demonstrate_integration_with_collaboration_manager()
    
    print("\n演示完成！")


if __name__ == '__main__':
    main()
