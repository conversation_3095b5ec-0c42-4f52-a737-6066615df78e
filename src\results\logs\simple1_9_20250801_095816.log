2025-08-01 09:58:16,868 - __main__ - INFO - simple1_9 开始进化第 1 代
2025-08-01 09:58:16,869 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-01 09:58:16,869 - StatsExpert - INFO - 开始统计分析
2025-08-01 09:58:16,870 - StatsExpert - INFO - 统计分析完成: 种群大小=8, 最优成本=830.0, 多样性=0.734
2025-08-01 09:58:16,872 - PathExpert - INFO - 开始路径结构分析
2025-08-01 09:58:16,875 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.071
2025-08-01 09:58:16,876 - EliteExpert - INFO - 开始精英解分析
2025-08-01 09:58:16,878 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/3)
2025-08-01 09:58:16,878 - LandscapeExpert - INFO - 从统计报告生成模拟数据: 8个路径, 8个适应度值
2025-08-01 09:58:16,879 - LandscapeExpert - INFO - 数据提取成功: 8个路径, 8个适应度值
2025-08-01 09:58:17,087 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-01 09:58:17,088 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-01 09:58:17,163 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记
2025-08-01 09:58:17,500 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_1_20250801_095817.html
2025-08-01 09:58:17,578 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_1_20250801_095817.html
2025-08-01 09:58:17,579 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-01 09:58:17,579 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-01 09:58:17,580 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.7022秒
2025-08-01 09:58:17,580 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 3, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754013497.0875843, 'performance_metrics': {}}}
2025-08-01 09:58:17,581 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-01 09:58:17,581 - StrategyExpert - INFO - 开始策略分配分析
2025-08-01 09:58:17,581 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 8 individuals
  • diversity: 0.5
  • best_cost: 830.0
  • mean_cost: 964.0
- Search-space: ruggedness 0.5, modality uni-modal, deceptiveness low
- Evolution phase: exploration
- Landscape focus suggestion: exploration
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 1): [{"type": "unexplored_space", "potential": "high", "description": "未充分探索的搜索空间", "recommendation": "增加探索性操作"}]
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-08-01 09:58:17,584 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-08-01 09:58:17,584 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-01 09:58:19,054 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit"
  },
  "rationale": "High opportunity in unexplored space and low diversity. Prioritize exploration, assigning most individuals to explore to search broadly."
}
```
2025-08-01 09:58:19,055 - utils.utils - INFO - 成功从JSON中提取数据
2025-08-01 09:58:19,056 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-08-01 09:58:19,056 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-08-01 09:58:19,056 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit"
  },
  "rationale": "High opportunity in unexplored space and low diversity. Prioritize exploration, assigning most individuals to explore to search broadly."
}
```
2025-08-01 09:58:19,057 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-01 09:58:19,057 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-08-01 09:58:19,057 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit"
  },
  "rationale": "High opportunity in unexplored space and low diversity. Prioritize exploration, assigning most individuals to explore to search broadly."
}
```
2025-08-01 09:58:19,058 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-01 09:58:19,058 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-01 09:58:19,058 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-01 09:58:19,059 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:58:19,059 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-01 09:58:19,059 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:58:19,244 - ExplorationExpert - INFO - 探索路径生成完成，成本: 977.0, 路径长度: 9
2025-08-01 09:58:19,244 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [4, 8, 7, 2, 3, 1, 0, 6, 5], 'cur_cost': 977.0}
2025-08-01 09:58:19,245 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-01 09:58:19,245 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-01 09:58:19,245 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:58:19,246 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-01 09:58:19,246 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:58:19,246 - ExplorationExpert - INFO - 探索路径生成完成，成本: 916.0, 路径长度: 9
2025-08-01 09:58:19,247 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 2, 5, 7, 3, 6, 8, 4, 1], 'cur_cost': 916.0}
2025-08-01 09:58:19,247 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-01 09:58:19,247 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-01 09:58:19,248 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:58:19,248 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-01 09:58:19,248 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:58:19,248 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1090.0, 路径长度: 9
2025-08-01 09:58:19,249 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [3, 4, 0, 2, 6, 5, 7, 8, 1], 'cur_cost': 1090.0}
2025-08-01 09:58:19,249 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-01 09:58:19,249 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:58:19,252 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:58:19,253 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1125.0
2025-08-01 09:58:21,277 - ExploitationExpert - INFO - res_population_num: 1
2025-08-01 09:58:21,278 - ExploitationExpert - INFO - res_population_costs: [732.0]
2025-08-01 09:58:21,278 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 5, 7, 3, 6], dtype=int64)]
2025-08-01 09:58:21,278 - ExploitationExpert - INFO - populations_num: 8
2025-08-01 09:58:21,278 - ExploitationExpert - INFO - populations: [{'tour': [4, 8, 7, 2, 3, 1, 0, 6, 5], 'cur_cost': 977.0}, {'tour': [0, 2, 5, 7, 3, 6, 8, 4, 1], 'cur_cost': 916.0}, {'tour': [3, 4, 0, 2, 6, 5, 7, 8, 1], 'cur_cost': 1090.0}, {'tour': array([8, 6, 7, 0, 5, 2, 1, 3, 4], dtype=int64), 'cur_cost': 1125.0}, {'tour': array([0, 1, 7, 4, 6, 8, 3, 2, 5], dtype=int64), 'cur_cost': 1079.0}, {'tour': array([4, 6, 8, 7, 3, 1, 0, 5, 2], dtype=int64), 'cur_cost': 999.0}, {'tour': array([8, 5, 6, 1, 4, 3, 0, 7, 2], dtype=int64), 'cur_cost': 954.0}, {'tour': array([5, 4, 1, 2, 8, 7, 6, 0, 3], dtype=int64), 'cur_cost': 937.0}]
2025-08-01 09:58:21,280 - ExploitationExpert - INFO - 局部搜索耗时: 2.03秒
2025-08-01 09:58:21,280 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-01 09:58:21,281 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([8, 6, 7, 0, 5, 2, 1, 3, 4], dtype=int64), 'cur_cost': 1125.0}
2025-08-01 09:58:21,281 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-01 09:58:21,281 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-01 09:58:21,281 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:58:21,282 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-01 09:58:21,282 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:58:21,282 - ExplorationExpert - INFO - 探索路径生成完成，成本: 969.0, 路径长度: 9
2025-08-01 09:58:21,283 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [3, 6, 1, 8, 7, 5, 0, 4, 2], 'cur_cost': 969.0}
2025-08-01 09:58:21,283 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-01 09:58:21,283 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:58:21,283 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:58:21,284 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 976.0
2025-08-01 09:58:23,589 - ExploitationExpert - INFO - res_population_num: 2
2025-08-01 09:58:23,590 - ExploitationExpert - INFO - res_population_costs: [732.0, 680.0]
2025-08-01 09:58:23,590 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 5, 7, 3, 6], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-01 09:58:23,591 - ExploitationExpert - INFO - populations_num: 8
2025-08-01 09:58:23,591 - ExploitationExpert - INFO - populations: [{'tour': [4, 8, 7, 2, 3, 1, 0, 6, 5], 'cur_cost': 977.0}, {'tour': [0, 2, 5, 7, 3, 6, 8, 4, 1], 'cur_cost': 916.0}, {'tour': [3, 4, 0, 2, 6, 5, 7, 8, 1], 'cur_cost': 1090.0}, {'tour': array([8, 6, 7, 0, 5, 2, 1, 3, 4], dtype=int64), 'cur_cost': 1125.0}, {'tour': [3, 6, 1, 8, 7, 5, 0, 4, 2], 'cur_cost': 969.0}, {'tour': array([4, 2, 0, 8, 7, 1, 3, 5, 6], dtype=int64), 'cur_cost': 976.0}, {'tour': array([8, 5, 6, 1, 4, 3, 0, 7, 2], dtype=int64), 'cur_cost': 954.0}, {'tour': array([5, 4, 1, 2, 8, 7, 6, 0, 3], dtype=int64), 'cur_cost': 937.0}]
2025-08-01 09:58:23,592 - ExploitationExpert - INFO - 局部搜索耗时: 2.31秒
2025-08-01 09:58:23,592 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-01 09:58:23,593 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([4, 2, 0, 8, 7, 1, 3, 5, 6], dtype=int64), 'cur_cost': 976.0}
2025-08-01 09:58:23,593 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-01 09:58:23,593 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-01 09:58:23,593 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:58:23,594 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-01 09:58:23,594 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:58:23,594 - ExplorationExpert - INFO - 探索路径生成完成，成本: 763.0, 路径长度: 9
2025-08-01 09:58:23,594 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [3, 6, 7, 5, 0, 1, 4, 2, 8], 'cur_cost': 763.0}
2025-08-01 09:58:23,595 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-01 09:58:23,595 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:58:23,595 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:58:23,595 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 960.0
2025-08-01 09:58:24,299 - ExploitationExpert - INFO - res_population_num: 3
2025-08-01 09:58:24,300 - ExploitationExpert - INFO - res_population_costs: [732.0, 680.0, 680.0]
2025-08-01 09:58:24,300 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 5, 7, 3, 6], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64)]
2025-08-01 09:58:24,301 - ExploitationExpert - INFO - populations_num: 8
2025-08-01 09:58:24,301 - ExploitationExpert - INFO - populations: [{'tour': [4, 8, 7, 2, 3, 1, 0, 6, 5], 'cur_cost': 977.0}, {'tour': [0, 2, 5, 7, 3, 6, 8, 4, 1], 'cur_cost': 916.0}, {'tour': [3, 4, 0, 2, 6, 5, 7, 8, 1], 'cur_cost': 1090.0}, {'tour': array([8, 6, 7, 0, 5, 2, 1, 3, 4], dtype=int64), 'cur_cost': 1125.0}, {'tour': [3, 6, 1, 8, 7, 5, 0, 4, 2], 'cur_cost': 969.0}, {'tour': array([4, 2, 0, 8, 7, 1, 3, 5, 6], dtype=int64), 'cur_cost': 976.0}, {'tour': [3, 6, 7, 5, 0, 1, 4, 2, 8], 'cur_cost': 763.0}, {'tour': array([2, 0, 4, 5, 3, 7, 1, 6, 8], dtype=int64), 'cur_cost': 960.0}]
2025-08-01 09:58:24,302 - ExploitationExpert - INFO - 局部搜索耗时: 0.71秒
2025-08-01 09:58:24,302 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-01 09:58:24,303 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([2, 0, 4, 5, 3, 7, 1, 6, 8], dtype=int64), 'cur_cost': 960.0}
2025-08-01 09:58:24,305 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [4, 8, 7, 2, 3, 1, 0, 6, 5], 'cur_cost': 977.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 5, 7, 3, 6, 8, 4, 1], 'cur_cost': 916.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [3, 4, 0, 2, 6, 5, 7, 8, 1], 'cur_cost': 1090.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 6, 7, 0, 5, 2, 1, 3, 4], dtype=int64), 'cur_cost': 1125.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [3, 6, 1, 8, 7, 5, 0, 4, 2], 'cur_cost': 969.0}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 2, 0, 8, 7, 1, 3, 5, 6], dtype=int64), 'cur_cost': 976.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [3, 6, 7, 5, 0, 1, 4, 2, 8], 'cur_cost': 763.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 0, 4, 5, 3, 7, 1, 6, 8], dtype=int64), 'cur_cost': 960.0}}]
2025-08-01 09:58:24,307 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-01 09:58:24,308 - StatsExpert - INFO - 开始统计分析
2025-08-01 09:58:24,309 - StatsExpert - INFO - 统计分析完成: 种群大小=8, 最优成本=763.0, 多样性=0.762
2025-08-01 09:58:24,309 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-01 09:58:24,309 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-01 09:58:24,309 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-01 09:58:24,310 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 80, 'iteration': 0, 'total_iterations': 3, 'cost_improvement': {'status': 'slight_improvement', 'improvement_rate': 0.00993068988369693, 'best_improvement': 0.08072289156626505}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.0378378378378377}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.7037037037037037, 'new_diversity': 0.7037037037037037, 'diversity_change': 0.0}, 'suggestions': ['改进缓慢，建议调整策略平衡或增加扰动强度', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-01 09:58:24,310 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-01 09:58:24,310 - __main__ - INFO - simple1_9 开始进化第 2 代
2025-08-01 09:58:24,311 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-01 09:58:24,311 - StatsExpert - INFO - 开始统计分析
2025-08-01 09:58:24,312 - StatsExpert - INFO - 统计分析完成: 种群大小=8, 最优成本=763.0, 多样性=0.762
2025-08-01 09:58:24,312 - PathExpert - INFO - 开始路径结构分析
2025-08-01 09:58:24,312 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.115
2025-08-01 09:58:24,312 - EliteExpert - INFO - 开始精英解分析
2025-08-01 09:58:24,313 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.704
2025-08-01 09:58:24,314 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/3)
2025-08-01 09:58:24,314 - LandscapeExpert - INFO - 从统计报告生成模拟数据: 8个路径, 8个适应度值
2025-08-01 09:58:24,315 - LandscapeExpert - INFO - 数据提取成功: 8个路径, 8个适应度值
2025-08-01 09:58:24,318 - core.algorithms.fitness_landscape_analyzer - ERROR - 景观分析失败: '>' not supported between instances of 'str' and 'int'
2025-08-01 09:58:24,319 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-01 09:58:24,319 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-01 09:58:24,329 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记
2025-08-01 09:58:24,429 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_2_20250801_095824.html
2025-08-01 09:58:24,512 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_2_20250801_095824.html
2025-08-01 09:58:24,512 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 2
2025-08-01 09:58:24,512 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-01 09:58:24,513 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1986秒
2025-08-01 09:58:24,513 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 1, 'total': 3, 'progress': 0.3333333333333333}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754013504.3199499, 'performance_metrics': {}}}
2025-08-01 09:58:24,513 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-01 09:58:24,514 - StrategyExpert - INFO - 开始策略分配分析
2025-08-01 09:58:24,514 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 8 individuals
  • diversity: 0.5
  • best_cost: 763.0
  • mean_cost: 972.0
- Search-space: ruggedness 0.5, modality uni-modal, deceptiveness low
- Evolution phase: exploitation
- Landscape focus suggestion: exploitation
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 1): [{"type": "unexplored_space", "potential": "high", "description": "未充分探索的搜索空间", "recommendation": "增加探索性操作"}]
- Last-iteration feedback: {'overall_score': 80, 'iteration': 0, 'total_iterations': 3, 'cost_improvement': {'status': 'slight_improvement', 'impro...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-08-01 09:58:24,515 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-08-01 09:58:24,516 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-01 09:58:26,148 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.40,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "exploit",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore"
  },
  "rationale": "Exploitation phase with opportunity for high potential discovery. Exploit best individuals while exploring unexplored search space."
}
```
2025-08-01 09:58:26,148 - utils.utils - INFO - 成功从JSON中提取数据
2025-08-01 09:58:26,148 - StrategyExpert - INFO - 策略分配完成: ['exploit', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-01 09:58:26,149 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-01 09:58:26,149 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.40,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "exploit",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore"
  },
  "rationale": "Exploitation phase with opportunity for high potential discovery. Exploit best individuals while exploring unexplored search space."
}
```
2025-08-01 09:58:26,150 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-01 09:58:26,151 - __main__ - INFO - 策略分配: ['exploit', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-01 09:58:26,151 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.40,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "exploit",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore"
  },
  "rationale": "Exploitation phase with opportunity for high potential discovery. Exploit best individuals while exploring unexplored search space."
}
```
2025-08-01 09:58:26,152 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-01 09:58:26,152 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-01 09:58:26,153 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:58:26,153 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:58:26,153 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 1114.0
2025-08-01 09:58:26,215 - ExploitationExpert - INFO - res_population_num: 4
2025-08-01 09:58:26,215 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 732.0, 680]
2025-08-01 09:58:26,216 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 1, 4, 2, 8, 5, 7, 3, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-01 09:58:26,217 - ExploitationExpert - INFO - populations_num: 8
2025-08-01 09:58:26,218 - ExploitationExpert - INFO - populations: [{'tour': array([1, 6, 4, 8, 0, 7, 2, 5, 3], dtype=int64), 'cur_cost': 1114.0}, {'tour': [0, 2, 5, 7, 3, 6, 8, 4, 1], 'cur_cost': 916.0}, {'tour': [3, 4, 0, 2, 6, 5, 7, 8, 1], 'cur_cost': 1090.0}, {'tour': [8, 6, 7, 0, 5, 2, 1, 3, 4], 'cur_cost': 1125.0}, {'tour': [3, 6, 1, 8, 7, 5, 0, 4, 2], 'cur_cost': 969.0}, {'tour': [4, 2, 0, 8, 7, 1, 3, 5, 6], 'cur_cost': 976.0}, {'tour': [3, 6, 7, 5, 0, 1, 4, 2, 8], 'cur_cost': 763.0}, {'tour': [2, 0, 4, 5, 3, 7, 1, 6, 8], 'cur_cost': 960.0}]
2025-08-01 09:58:26,219 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-08-01 09:58:26,219 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-01 09:58:26,220 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([1, 6, 4, 8, 0, 7, 2, 5, 3], dtype=int64), 'cur_cost': 1114.0}
2025-08-01 09:58:26,220 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-01 09:58:26,221 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:58:26,221 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:58:26,221 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 1100.0
2025-08-01 09:58:26,279 - ExploitationExpert - INFO - res_population_num: 4
2025-08-01 09:58:26,280 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 732.0, 680]
2025-08-01 09:58:26,280 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 1, 4, 2, 8, 5, 7, 3, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-01 09:58:26,281 - ExploitationExpert - INFO - populations_num: 8
2025-08-01 09:58:26,282 - ExploitationExpert - INFO - populations: [{'tour': array([1, 6, 4, 8, 0, 7, 2, 5, 3], dtype=int64), 'cur_cost': 1114.0}, {'tour': array([7, 3, 5, 4, 0, 8, 1, 6, 2], dtype=int64), 'cur_cost': 1100.0}, {'tour': [3, 4, 0, 2, 6, 5, 7, 8, 1], 'cur_cost': 1090.0}, {'tour': [8, 6, 7, 0, 5, 2, 1, 3, 4], 'cur_cost': 1125.0}, {'tour': [3, 6, 1, 8, 7, 5, 0, 4, 2], 'cur_cost': 969.0}, {'tour': [4, 2, 0, 8, 7, 1, 3, 5, 6], 'cur_cost': 976.0}, {'tour': [3, 6, 7, 5, 0, 1, 4, 2, 8], 'cur_cost': 763.0}, {'tour': [2, 0, 4, 5, 3, 7, 1, 6, 8], 'cur_cost': 960.0}]
2025-08-01 09:58:26,283 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-08-01 09:58:26,283 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-01 09:58:26,283 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([7, 3, 5, 4, 0, 8, 1, 6, 2], dtype=int64), 'cur_cost': 1100.0}
2025-08-01 09:58:26,284 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-01 09:58:26,284 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:58:26,284 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:58:26,285 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 1043.0
2025-08-01 09:58:26,344 - ExploitationExpert - INFO - res_population_num: 4
2025-08-01 09:58:26,344 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 732.0, 680]
2025-08-01 09:58:26,345 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 1, 4, 2, 8, 5, 7, 3, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-01 09:58:26,346 - ExploitationExpert - INFO - populations_num: 8
2025-08-01 09:58:26,346 - ExploitationExpert - INFO - populations: [{'tour': array([1, 6, 4, 8, 0, 7, 2, 5, 3], dtype=int64), 'cur_cost': 1114.0}, {'tour': array([7, 3, 5, 4, 0, 8, 1, 6, 2], dtype=int64), 'cur_cost': 1100.0}, {'tour': array([8, 2, 3, 1, 0, 5, 4, 6, 7], dtype=int64), 'cur_cost': 1043.0}, {'tour': [8, 6, 7, 0, 5, 2, 1, 3, 4], 'cur_cost': 1125.0}, {'tour': [3, 6, 1, 8, 7, 5, 0, 4, 2], 'cur_cost': 969.0}, {'tour': [4, 2, 0, 8, 7, 1, 3, 5, 6], 'cur_cost': 976.0}, {'tour': [3, 6, 7, 5, 0, 1, 4, 2, 8], 'cur_cost': 763.0}, {'tour': [2, 0, 4, 5, 3, 7, 1, 6, 8], 'cur_cost': 960.0}]
2025-08-01 09:58:26,347 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-08-01 09:58:26,347 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-01 09:58:26,347 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([8, 2, 3, 1, 0, 5, 4, 6, 7], dtype=int64), 'cur_cost': 1043.0}
2025-08-01 09:58:26,348 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-01 09:58:26,348 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:58:26,348 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:58:26,349 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 924.0
2025-08-01 09:58:26,407 - ExploitationExpert - INFO - res_population_num: 4
2025-08-01 09:58:26,408 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 732.0, 680]
2025-08-01 09:58:26,409 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 1, 4, 2, 8, 5, 7, 3, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-01 09:58:26,410 - ExploitationExpert - INFO - populations_num: 8
2025-08-01 09:58:26,410 - ExploitationExpert - INFO - populations: [{'tour': array([1, 6, 4, 8, 0, 7, 2, 5, 3], dtype=int64), 'cur_cost': 1114.0}, {'tour': array([7, 3, 5, 4, 0, 8, 1, 6, 2], dtype=int64), 'cur_cost': 1100.0}, {'tour': array([8, 2, 3, 1, 0, 5, 4, 6, 7], dtype=int64), 'cur_cost': 1043.0}, {'tour': array([3, 7, 8, 2, 1, 6, 5, 4, 0], dtype=int64), 'cur_cost': 924.0}, {'tour': [3, 6, 1, 8, 7, 5, 0, 4, 2], 'cur_cost': 969.0}, {'tour': [4, 2, 0, 8, 7, 1, 3, 5, 6], 'cur_cost': 976.0}, {'tour': [3, 6, 7, 5, 0, 1, 4, 2, 8], 'cur_cost': 763.0}, {'tour': [2, 0, 4, 5, 3, 7, 1, 6, 8], 'cur_cost': 960.0}]
2025-08-01 09:58:26,412 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-08-01 09:58:26,412 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-01 09:58:26,412 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([3, 7, 8, 2, 1, 6, 5, 4, 0], dtype=int64), 'cur_cost': 924.0}
2025-08-01 09:58:26,412 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-01 09:58:26,413 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-01 09:58:26,413 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:58:26,413 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-01 09:58:26,413 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:58:26,414 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1119.0, 路径长度: 9
2025-08-01 09:58:26,414 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [4, 3, 1, 5, 8, 7, 6, 2, 0], 'cur_cost': 1119.0}
2025-08-01 09:58:26,414 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-01 09:58:26,414 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-01 09:58:26,415 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:58:26,415 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-01 09:58:26,415 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:58:26,415 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1113.0, 路径长度: 9
2025-08-01 09:58:26,416 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [1, 5, 0, 3, 2, 4, 7, 6, 8], 'cur_cost': 1113.0}
2025-08-01 09:58:26,416 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-01 09:58:26,417 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-01 09:58:26,417 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:58:26,418 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-01 09:58:26,418 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:58:26,418 - ExplorationExpert - INFO - 探索路径生成完成，成本: 799.0, 路径长度: 9
2025-08-01 09:58:26,419 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [5, 7, 0, 6, 1, 2, 4, 8, 3], 'cur_cost': 799.0}
2025-08-01 09:58:26,419 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-01 09:58:26,419 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-01 09:58:26,420 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:58:26,420 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-01 09:58:26,420 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:58:26,421 - ExplorationExpert - INFO - 探索路径生成完成，成本: 999.0, 路径长度: 9
2025-08-01 09:58:26,421 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [2, 4, 3, 1, 5, 0, 6, 7, 8], 'cur_cost': 999.0}
2025-08-01 09:58:26,422 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([1, 6, 4, 8, 0, 7, 2, 5, 3], dtype=int64), 'cur_cost': 1114.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 3, 5, 4, 0, 8, 1, 6, 2], dtype=int64), 'cur_cost': 1100.0}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 2, 3, 1, 0, 5, 4, 6, 7], dtype=int64), 'cur_cost': 1043.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 7, 8, 2, 1, 6, 5, 4, 0], dtype=int64), 'cur_cost': 924.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [4, 3, 1, 5, 8, 7, 6, 2, 0], 'cur_cost': 1119.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [1, 5, 0, 3, 2, 4, 7, 6, 8], 'cur_cost': 1113.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [5, 7, 0, 6, 1, 2, 4, 8, 3], 'cur_cost': 799.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [2, 4, 3, 1, 5, 0, 6, 7, 8], 'cur_cost': 999.0}}]
2025-08-01 09:58:26,422 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-01 09:58:26,422 - StatsExpert - INFO - 开始统计分析
2025-08-01 09:58:26,423 - StatsExpert - INFO - 统计分析完成: 种群大小=8, 最优成本=799.0, 多样性=0.770
2025-08-01 09:58:26,423 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-01 09:58:26,423 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-01 09:58:26,423 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-01 09:58:26,424 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 1, 'total_iterations': 3, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.032596733437966874, 'best_improvement': -0.047182175622542594}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.01041666666666663}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6851851851851852, 'new_diversity': 0.6851851851851852, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-01 09:58:26,424 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-01 09:58:26,424 - __main__ - INFO - simple1_9 开始进化第 3 代
2025-08-01 09:58:26,425 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-01 09:58:26,425 - StatsExpert - INFO - 开始统计分析
2025-08-01 09:58:26,425 - StatsExpert - INFO - 统计分析完成: 种群大小=8, 最优成本=799.0, 多样性=0.770
2025-08-01 09:58:26,426 - PathExpert - INFO - 开始路径结构分析
2025-08-01 09:58:26,426 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.075
2025-08-01 09:58:26,426 - EliteExpert - INFO - 开始精英解分析
2025-08-01 09:58:26,427 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.685
2025-08-01 09:58:26,429 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/3)
2025-08-01 09:58:26,430 - LandscapeExpert - INFO - 从统计报告生成模拟数据: 8个路径, 8个适应度值
2025-08-01 09:58:26,430 - LandscapeExpert - INFO - 数据提取成功: 8个路径, 8个适应度值
2025-08-01 09:58:26,434 - core.algorithms.fitness_landscape_analyzer - ERROR - 景观分析失败: '>' not supported between instances of 'str' and 'int'
2025-08-01 09:58:26,434 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-01 09:58:26,435 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-01 09:58:26,450 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记
2025-08-01 09:58:26,551 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_3_20250801_095826.html
2025-08-01 09:58:26,598 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_3_20250801_095826.html
2025-08-01 09:58:26,598 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 3
2025-08-01 09:58:26,598 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-01 09:58:26,598 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1693秒
2025-08-01 09:58:26,599 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 3, 'progress': 0.6666666666666666}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754013506.4345312, 'performance_metrics': {}}}
2025-08-01 09:58:26,600 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-01 09:58:26,600 - StrategyExpert - INFO - 开始策略分配分析
2025-08-01 09:58:26,600 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 8 individuals
  • diversity: 0.5
  • best_cost: 799.0
  • mean_cost: 1026.38
- Search-space: ruggedness 0.5, modality uni-modal, deceptiveness low
- Evolution phase: exploitation
- Landscape focus suggestion: exploitation
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 1): [{"type": "unexplored_space", "potential": "high", "description": "未充分探索的搜索空间", "recommendation": "增加探索性操作"}]
- Last-iteration feedback: {'overall_score': 40, 'iteration': 1, 'total_iterations': 3, 'cost_improvement': {'status': 'deterioration', 'improvemen...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-08-01 09:58:26,602 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-08-01 09:58:26,602 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-01 09:58:28,088 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.3,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit"
  },
  "rationale": "Exploitation phase and opportunity in unexplored space. Mostly exploit, but explore for individuals in the opportunity region."
}
```
2025-08-01 09:58:28,090 - utils.utils - INFO - 成功从JSON中提取数据
2025-08-01 09:58:28,090 - StrategyExpert - INFO - 策略分配完成: ['exploit', 'exploit', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-08-01 09:58:28,090 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'exploit', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-08-01 09:58:28,091 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.3,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit"
  },
  "rationale": "Exploitation phase and opportunity in unexplored space. Mostly exploit, but explore for individuals in the opportunity region."
}
```
2025-08-01 09:58:28,091 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-01 09:58:28,092 - __main__ - INFO - 策略分配: ['exploit', 'exploit', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-08-01 09:58:28,092 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.3,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit"
  },
  "rationale": "Exploitation phase and opportunity in unexplored space. Mostly exploit, but explore for individuals in the opportunity region."
}
```
2025-08-01 09:58:28,092 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-01 09:58:28,093 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-01 09:58:28,093 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:58:28,093 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:58:28,093 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 1055.0
2025-08-01 09:58:28,147 - ExploitationExpert - INFO - res_population_num: 4
2025-08-01 09:58:28,147 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680, 732.0]
2025-08-01 09:58:28,148 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 5, 7, 3, 6], dtype=int64)]
2025-08-01 09:58:28,149 - ExploitationExpert - INFO - populations_num: 8
2025-08-01 09:58:28,149 - ExploitationExpert - INFO - populations: [{'tour': array([8, 3, 4, 1, 6, 2, 7, 0, 5], dtype=int64), 'cur_cost': 1055.0}, {'tour': [7, 3, 5, 4, 0, 8, 1, 6, 2], 'cur_cost': 1100.0}, {'tour': [8, 2, 3, 1, 0, 5, 4, 6, 7], 'cur_cost': 1043.0}, {'tour': [3, 7, 8, 2, 1, 6, 5, 4, 0], 'cur_cost': 924.0}, {'tour': [4, 3, 1, 5, 8, 7, 6, 2, 0], 'cur_cost': 1119.0}, {'tour': [1, 5, 0, 3, 2, 4, 7, 6, 8], 'cur_cost': 1113.0}, {'tour': [5, 7, 0, 6, 1, 2, 4, 8, 3], 'cur_cost': 799.0}, {'tour': [2, 4, 3, 1, 5, 0, 6, 7, 8], 'cur_cost': 999.0}]
2025-08-01 09:58:28,150 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-08-01 09:58:28,150 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-08-01 09:58:28,151 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([8, 3, 4, 1, 6, 2, 7, 0, 5], dtype=int64), 'cur_cost': 1055.0}
2025-08-01 09:58:28,151 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-01 09:58:28,151 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:58:28,152 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:58:28,152 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 948.0
2025-08-01 09:58:28,211 - ExploitationExpert - INFO - res_population_num: 4
2025-08-01 09:58:28,211 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680, 732.0]
2025-08-01 09:58:28,211 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 5, 7, 3, 6], dtype=int64)]
2025-08-01 09:58:28,213 - ExploitationExpert - INFO - populations_num: 8
2025-08-01 09:58:28,213 - ExploitationExpert - INFO - populations: [{'tour': array([8, 3, 4, 1, 6, 2, 7, 0, 5], dtype=int64), 'cur_cost': 1055.0}, {'tour': array([6, 5, 8, 3, 0, 2, 7, 4, 1], dtype=int64), 'cur_cost': 948.0}, {'tour': [8, 2, 3, 1, 0, 5, 4, 6, 7], 'cur_cost': 1043.0}, {'tour': [3, 7, 8, 2, 1, 6, 5, 4, 0], 'cur_cost': 924.0}, {'tour': [4, 3, 1, 5, 8, 7, 6, 2, 0], 'cur_cost': 1119.0}, {'tour': [1, 5, 0, 3, 2, 4, 7, 6, 8], 'cur_cost': 1113.0}, {'tour': [5, 7, 0, 6, 1, 2, 4, 8, 3], 'cur_cost': 799.0}, {'tour': [2, 4, 3, 1, 5, 0, 6, 7, 8], 'cur_cost': 999.0}]
2025-08-01 09:58:28,214 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-08-01 09:58:28,214 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-08-01 09:58:28,214 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([6, 5, 8, 3, 0, 2, 7, 4, 1], dtype=int64), 'cur_cost': 948.0}
2025-08-01 09:58:28,215 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-01 09:58:28,215 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:58:28,215 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:58:28,215 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 1068.0
2025-08-01 09:58:28,267 - ExploitationExpert - INFO - res_population_num: 4
2025-08-01 09:58:28,267 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680, 732.0]
2025-08-01 09:58:28,268 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 5, 7, 3, 6], dtype=int64)]
2025-08-01 09:58:28,270 - ExploitationExpert - INFO - populations_num: 8
2025-08-01 09:58:28,270 - ExploitationExpert - INFO - populations: [{'tour': array([8, 3, 4, 1, 6, 2, 7, 0, 5], dtype=int64), 'cur_cost': 1055.0}, {'tour': array([6, 5, 8, 3, 0, 2, 7, 4, 1], dtype=int64), 'cur_cost': 948.0}, {'tour': array([6, 7, 0, 5, 1, 2, 3, 8, 4], dtype=int64), 'cur_cost': 1068.0}, {'tour': [3, 7, 8, 2, 1, 6, 5, 4, 0], 'cur_cost': 924.0}, {'tour': [4, 3, 1, 5, 8, 7, 6, 2, 0], 'cur_cost': 1119.0}, {'tour': [1, 5, 0, 3, 2, 4, 7, 6, 8], 'cur_cost': 1113.0}, {'tour': [5, 7, 0, 6, 1, 2, 4, 8, 3], 'cur_cost': 799.0}, {'tour': [2, 4, 3, 1, 5, 0, 6, 7, 8], 'cur_cost': 999.0}]
2025-08-01 09:58:28,276 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-08-01 09:58:28,278 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-08-01 09:58:28,279 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([6, 7, 0, 5, 1, 2, 3, 8, 4], dtype=int64), 'cur_cost': 1068.0}
2025-08-01 09:58:28,280 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-01 09:58:28,280 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-01 09:58:28,280 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:58:28,281 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-01 09:58:28,281 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:58:28,281 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1000.0, 路径长度: 9
2025-08-01 09:58:28,282 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [1, 7, 6, 8, 0, 4, 2, 3, 5], 'cur_cost': 1000.0}
2025-08-01 09:58:28,282 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-01 09:58:28,282 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-01 09:58:28,283 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:58:28,283 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-01 09:58:28,283 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:58:28,284 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1102.0, 路径长度: 9
2025-08-01 09:58:28,284 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [4, 6, 2, 7, 5, 8, 1, 0, 3], 'cur_cost': 1102.0}
2025-08-01 09:58:28,285 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-01 09:58:28,285 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:58:28,286 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:58:28,286 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 935.0
2025-08-01 09:58:28,346 - ExploitationExpert - INFO - res_population_num: 4
2025-08-01 09:58:28,347 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680, 732.0]
2025-08-01 09:58:28,347 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 5, 7, 3, 6], dtype=int64)]
2025-08-01 09:58:28,348 - ExploitationExpert - INFO - populations_num: 8
2025-08-01 09:58:28,348 - ExploitationExpert - INFO - populations: [{'tour': array([8, 3, 4, 1, 6, 2, 7, 0, 5], dtype=int64), 'cur_cost': 1055.0}, {'tour': array([6, 5, 8, 3, 0, 2, 7, 4, 1], dtype=int64), 'cur_cost': 948.0}, {'tour': array([6, 7, 0, 5, 1, 2, 3, 8, 4], dtype=int64), 'cur_cost': 1068.0}, {'tour': [1, 7, 6, 8, 0, 4, 2, 3, 5], 'cur_cost': 1000.0}, {'tour': [4, 6, 2, 7, 5, 8, 1, 0, 3], 'cur_cost': 1102.0}, {'tour': array([5, 7, 0, 1, 6, 4, 3, 2, 8], dtype=int64), 'cur_cost': 935.0}, {'tour': [5, 7, 0, 6, 1, 2, 4, 8, 3], 'cur_cost': 799.0}, {'tour': [2, 4, 3, 1, 5, 0, 6, 7, 8], 'cur_cost': 999.0}]
2025-08-01 09:58:28,349 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-08-01 09:58:28,350 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-08-01 09:58:28,350 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([5, 7, 0, 1, 6, 4, 3, 2, 8], dtype=int64), 'cur_cost': 935.0}
2025-08-01 09:58:28,351 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-01 09:58:28,351 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:58:28,351 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:58:28,352 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 919.0
2025-08-01 09:58:28,417 - ExploitationExpert - INFO - res_population_num: 4
2025-08-01 09:58:28,418 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680, 732.0]
2025-08-01 09:58:28,419 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 5, 7, 3, 6], dtype=int64)]
2025-08-01 09:58:28,420 - ExploitationExpert - INFO - populations_num: 8
2025-08-01 09:58:28,421 - ExploitationExpert - INFO - populations: [{'tour': array([8, 3, 4, 1, 6, 2, 7, 0, 5], dtype=int64), 'cur_cost': 1055.0}, {'tour': array([6, 5, 8, 3, 0, 2, 7, 4, 1], dtype=int64), 'cur_cost': 948.0}, {'tour': array([6, 7, 0, 5, 1, 2, 3, 8, 4], dtype=int64), 'cur_cost': 1068.0}, {'tour': [1, 7, 6, 8, 0, 4, 2, 3, 5], 'cur_cost': 1000.0}, {'tour': [4, 6, 2, 7, 5, 8, 1, 0, 3], 'cur_cost': 1102.0}, {'tour': array([5, 7, 0, 1, 6, 4, 3, 2, 8], dtype=int64), 'cur_cost': 935.0}, {'tour': array([0, 7, 2, 8, 5, 3, 4, 1, 6], dtype=int64), 'cur_cost': 919.0}, {'tour': [2, 4, 3, 1, 5, 0, 6, 7, 8], 'cur_cost': 999.0}]
2025-08-01 09:58:28,423 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-08-01 09:58:28,423 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-08-01 09:58:28,424 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([0, 7, 2, 8, 5, 3, 4, 1, 6], dtype=int64), 'cur_cost': 919.0}
2025-08-01 09:58:28,424 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-01 09:58:28,424 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:58:28,425 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:58:28,425 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 827.0
2025-08-01 09:58:28,509 - ExploitationExpert - INFO - res_population_num: 4
2025-08-01 09:58:28,510 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680, 732.0]
2025-08-01 09:58:28,511 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 5, 7, 3, 6], dtype=int64)]
2025-08-01 09:58:28,515 - ExploitationExpert - INFO - populations_num: 8
2025-08-01 09:58:28,516 - ExploitationExpert - INFO - populations: [{'tour': array([8, 3, 4, 1, 6, 2, 7, 0, 5], dtype=int64), 'cur_cost': 1055.0}, {'tour': array([6, 5, 8, 3, 0, 2, 7, 4, 1], dtype=int64), 'cur_cost': 948.0}, {'tour': array([6, 7, 0, 5, 1, 2, 3, 8, 4], dtype=int64), 'cur_cost': 1068.0}, {'tour': [1, 7, 6, 8, 0, 4, 2, 3, 5], 'cur_cost': 1000.0}, {'tour': [4, 6, 2, 7, 5, 8, 1, 0, 3], 'cur_cost': 1102.0}, {'tour': array([5, 7, 0, 1, 6, 4, 3, 2, 8], dtype=int64), 'cur_cost': 935.0}, {'tour': array([0, 7, 2, 8, 5, 3, 4, 1, 6], dtype=int64), 'cur_cost': 919.0}, {'tour': array([0, 2, 4, 7, 3, 5, 6, 8, 1], dtype=int64), 'cur_cost': 827.0}]
2025-08-01 09:58:28,520 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-01 09:58:28,520 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-08-01 09:58:28,521 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([0, 2, 4, 7, 3, 5, 6, 8, 1], dtype=int64), 'cur_cost': 827.0}
2025-08-01 09:58:28,522 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 3, 4, 1, 6, 2, 7, 0, 5], dtype=int64), 'cur_cost': 1055.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 5, 8, 3, 0, 2, 7, 4, 1], dtype=int64), 'cur_cost': 948.0}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 7, 0, 5, 1, 2, 3, 8, 4], dtype=int64), 'cur_cost': 1068.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 6, 8, 0, 4, 2, 3, 5], 'cur_cost': 1000.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [4, 6, 2, 7, 5, 8, 1, 0, 3], 'cur_cost': 1102.0}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([5, 7, 0, 1, 6, 4, 3, 2, 8], dtype=int64), 'cur_cost': 935.0}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 7, 2, 8, 5, 3, 4, 1, 6], dtype=int64), 'cur_cost': 919.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 2, 4, 7, 3, 5, 6, 8, 1], dtype=int64), 'cur_cost': 827.0}}]
2025-08-01 09:58:28,523 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-01 09:58:28,524 - StatsExpert - INFO - 开始统计分析
2025-08-01 09:58:28,525 - StatsExpert - INFO - 统计分析完成: 种群大小=8, 最优成本=827.0, 多样性=0.742
2025-08-01 09:58:28,525 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-01 09:58:28,526 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-01 09:58:28,526 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-01 09:58:28,527 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 80, 'iteration': 2, 'total_iterations': 3, 'cost_improvement': {'status': 'slight_improvement', 'improvement_rate': 0.007875530649729253, 'best_improvement': -0.03504380475594493}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.036082474226803996}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6851851851851852, 'new_diversity': 0.6851851851851852, 'diversity_change': 0.0}, 'suggestions': ['改进缓慢，建议调整策略平衡或增加扰动强度', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-01 09:58:28,527 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-01 09:58:28,529 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_solution.json
2025-08-01 09:58:28,530 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_20250801_095828.solution
2025-08-01 09:58:28,530 - __main__ - INFO - 实例 simple1_9 处理完成
