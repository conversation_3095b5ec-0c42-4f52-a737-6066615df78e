2025-08-05 10:28:47,783 - __main__ - INFO - composite2_34 开始进化第 1 代
2025-08-05 10:28:47,783 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 10:28:47,784 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:47,786 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=3836.000, 多样性=0.961
2025-08-05 10:28:47,787 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:47,790 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.961
2025-08-05 10:28:47,791 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:47,793 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 10:28:47,793 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:47,793 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 10:28:47,793 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 10:28:47,804 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.200, 适应度梯度: -1001.500, 聚类评分: 0.000, 覆盖率: 0.079, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:47,804 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 10:28:47,804 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 10:28:47,804 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite2_34
2025-08-05 10:28:47,809 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.3%, 梯度: 506.33 → 469.19
2025-08-05 10:28:47,910 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite2_34\landscape_composite2_34_iter_66_20250805_102847.html
2025-08-05 10:28:47,993 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite2_34\dashboard_composite2_34_iter_66_20250805_102847.html
2025-08-05 10:28:47,993 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 66
2025-08-05 10:28:47,994 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 10:28:47,994 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2010秒
2025-08-05 10:28:47,994 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 132, 'max_size': 500, 'hits': 0, 'misses': 132, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 445, 'misses': 222, 'hit_rate': 0.6671664167916042, 'evictions': 122, 'ttl': 7200}}
2025-08-05 10:28:47,994 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -1001.5, 'local_optima_density': 0.2, 'gradient_variance': 113651182.898, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0794, 'fitness_entropy': 0.9911594714322186, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -1001.500)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.079)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360927.8044038, 'performance_metrics': {}}}
2025-08-05 10:28:47,994 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:47,995 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:47,995 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:47,995 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:47,996 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:47,996 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 10:28:47,997 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:47,997 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:47,997 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:47,997 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:47,997 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:47,997 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:47,998 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:47,998 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:47,998 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:47,998 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:48,000 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 34
2025-08-05 10:28:48,000 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,000 - ExplorationExpert - INFO - 探索路径生成完成，成本: 22262.0, 路径长度: 34, 收集中间解: 0
2025-08-05 10:28:48,001 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [10, 18, 29, 24, 16, 30, 26, 0, 3, 12, 19, 6, 2, 22, 23, 1, 33, 4, 15, 21, 7, 13, 11, 28, 17, 8, 5, 9, 31, 27, 25, 20, 14, 32], 'cur_cost': 22262.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:48,001 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 22262.00)
2025-08-05 10:28:48,001 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:48,001 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:48,002 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:48,003 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 34
2025-08-05 10:28:48,004 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,004 - ExplorationExpert - INFO - 探索路径生成完成，成本: 4935.0, 路径长度: 34, 收集中间解: 0
2025-08-05 10:28:48,004 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 2, 1, 10, 14, 13, 15, 16, 11, 12, 9, 6, 5, 4, 3, 8, 7, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 19, 18], 'cur_cost': 4935.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:48,004 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 4935.00)
2025-08-05 10:28:48,005 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:48,005 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:48,005 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:48,006 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 34
2025-08-05 10:28:48,006 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,006 - ExplorationExpert - INFO - 探索路径生成完成，成本: 5306.0, 路径长度: 34, 收集中间解: 0
2025-08-05 10:28:48,007 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 6, 16, 21, 20, 18, 23, 22, 24, 17, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 2, 7, 9, 1, 15, 13, 14, 10, 12, 11], 'cur_cost': 5306.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:48,007 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 5306.00)
2025-08-05 10:28:48,007 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:48,007 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:48,007 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:48,008 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 34
2025-08-05 10:28:48,008 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,009 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3772.0, 路径长度: 34, 收集中间解: 0
2025-08-05 10:28:48,009 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 8, 3, 1, 4, 5, 6, 7, 9, 2, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 3772.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:48,009 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 3772.00)
2025-08-05 10:28:48,009 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:28:48,009 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:48,010 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:48,010 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 22745.0
2025-08-05 10:28:48,017 - ExploitationExpert - INFO - res_population_num: 1
2025-08-05 10:28:48,017 - ExploitationExpert - INFO - res_population_costs: [3576.0]
2025-08-05 10:28:48,017 - ExploitationExpert - INFO - res_populations: [array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 14, 13, 11, 12, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64)]
2025-08-05 10:28:48,018 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:48,018 - ExploitationExpert - INFO - populations: [{'tour': [10, 18, 29, 24, 16, 30, 26, 0, 3, 12, 19, 6, 2, 22, 23, 1, 33, 4, 15, 21, 7, 13, 11, 28, 17, 8, 5, 9, 31, 27, 25, 20, 14, 32], 'cur_cost': 22262.0}, {'tour': [0, 2, 1, 10, 14, 13, 15, 16, 11, 12, 9, 6, 5, 4, 3, 8, 7, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 19, 18], 'cur_cost': 4935.0}, {'tour': [0, 6, 16, 21, 20, 18, 23, 22, 24, 17, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 2, 7, 9, 1, 15, 13, 14, 10, 12, 11], 'cur_cost': 5306.0}, {'tour': [0, 8, 3, 1, 4, 5, 6, 7, 9, 2, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 3772.0}, {'tour': array([16,  6, 10, 33,  2, 21, 17, 19, 20, 13, 14, 28, 22, 15,  8, 25, 31,
        9, 30, 18,  5, 26,  7, 29, 27, 32, 11,  0, 24,  4,  1, 23,  3, 12],
      dtype=int64), 'cur_cost': 22745.0}, {'tour': array([23, 10, 12,  3, 30, 22, 17, 26, 21, 32,  9, 18,  8,  1, 16, 19, 20,
       29, 11,  4,  5,  2, 24, 15, 27,  0, 31, 25, 33,  7, 14, 13,  6, 28],
      dtype=int64), 'cur_cost': 20887.0}, {'tour': array([ 7,  3, 33, 21, 10, 28, 22, 30, 17, 25, 12, 26, 31, 18, 19, 14, 20,
       32,  8, 24,  2, 16, 13,  1, 11, 23, 29,  5,  9,  6,  0, 15,  4, 27],
      dtype=int64), 'cur_cost': 22123.0}, {'tour': array([18, 28,  4, 12, 14, 19, 32, 13,  6, 29,  8, 11, 31,  3,  7, 20, 25,
       24, 10, 30, 27, 17, 16,  1,  5, 26, 15,  9, 23,  0, 22, 21, 33,  2],
      dtype=int64), 'cur_cost': 24961.0}, {'tour': array([ 4,  3, 31,  8, 20, 19, 13, 33, 25, 15, 26, 24, 29,  9, 27, 16, 21,
       22,  2,  0,  5, 12, 30, 28, 23,  7, 14,  1,  6, 17, 11, 18, 10, 32],
      dtype=int64), 'cur_cost': 23583.0}, {'tour': array([ 8, 27,  5, 32, 19, 11, 10, 12, 31, 30, 16, 21, 24, 13,  6,  9, 33,
        1,  2, 29, 28, 18, 25, 15, 20, 14,  0,  7, 17,  3, 26, 22,  4, 23],
      dtype=int64), 'cur_cost': 23094.0}]
2025-08-05 10:28:48,020 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:48,020 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 170, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 170, 'cache_hits': 0, 'similarity_calculations': 732, 'cache_hit_rate': 0.0, 'cache_size': 732}}
2025-08-05 10:28:48,021 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([16,  6, 10, 33,  2, 21, 17, 19, 20, 13, 14, 28, 22, 15,  8, 25, 31,
        9, 30, 18,  5, 26,  7, 29, 27, 32, 11,  0, 24,  4,  1, 23,  3, 12],
      dtype=int64), 'cur_cost': 22745.0, 'intermediate_solutions': [{'tour': array([11, 18, 29, 33, 21, 19, 30,  1, 15,  5, 10, 25, 24, 12, 23, 31,  6,
       16,  8, 17,  0,  4,  2, 22,  9, 28, 13, 14, 32,  7, 20,  3, 26, 27],
      dtype=int64), 'cur_cost': 24401.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([33, 11, 18, 29, 21, 19, 30,  1, 15,  5, 10, 25, 24, 12, 23, 31,  6,
       16,  8, 17,  0,  4,  2, 22,  9, 28, 13, 14, 32,  7, 20,  3, 26, 27],
      dtype=int64), 'cur_cost': 24375.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([21, 33, 11, 18, 29, 19, 30,  1, 15,  5, 10, 25, 24, 12, 23, 31,  6,
       16,  8, 17,  0,  4,  2, 22,  9, 28, 13, 14, 32,  7, 20,  3, 26, 27],
      dtype=int64), 'cur_cost': 25659.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([29, 33, 11, 18, 21, 19, 30,  1, 15,  5, 10, 25, 24, 12, 23, 31,  6,
       16,  8, 17,  0,  4,  2, 22,  9, 28, 13, 14, 32,  7, 20,  3, 26, 27],
      dtype=int64), 'cur_cost': 23038.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([29, 21, 33, 11, 18, 19, 30,  1, 15,  5, 10, 25, 24, 12, 23, 31,  6,
       16,  8, 17,  0,  4,  2, 22,  9, 28, 13, 14, 32,  7, 20,  3, 26, 27],
      dtype=int64), 'cur_cost': 24333.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:48,021 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 22745.00)
2025-08-05 10:28:48,021 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:48,022 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:48,022 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:48,024 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 34
2025-08-05 10:28:48,025 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,025 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16888.0, 路径长度: 34, 收集中间解: 0
2025-08-05 10:28:48,025 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [24, 28, 21, 25, 29, 26, 0, 7, 1, 6, 15, 2, 10, 11, 8, 14, 5, 13, 18, 20, 30, 22, 17, 32, 19, 27, 23, 16, 9, 3, 4, 12, 33, 31], 'cur_cost': 16888.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:48,025 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 16888.00)
2025-08-05 10:28:48,025 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:48,026 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:48,026 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:48,028 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 34
2025-08-05 10:28:48,028 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,029 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15999.0, 路径长度: 34, 收集中间解: 0
2025-08-05 10:28:48,029 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [30, 33, 26, 0, 5, 10, 6, 11, 4, 8, 9, 29, 23, 22, 21, 25, 20, 19, 31, 24, 16, 7, 2, 13, 15, 12, 18, 32, 17, 27, 28, 3, 1, 14], 'cur_cost': 15999.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:48,029 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 15999.00)
2025-08-05 10:28:48,029 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:28:48,029 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:48,030 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:48,030 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 25650.0
2025-08-05 10:28:48,041 - ExploitationExpert - INFO - res_population_num: 3
2025-08-05 10:28:48,042 - ExploitationExpert - INFO - res_population_costs: [3576.0, 3575, 3575]
2025-08-05 10:28:48,042 - ExploitationExpert - INFO - res_populations: [array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 14, 13, 11, 12, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 13, 12, 11, 15, 16,  9,  7,  6,  2,  5,  4,  1,  3,  8],
      dtype=int64)]
2025-08-05 10:28:48,043 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:48,043 - ExploitationExpert - INFO - populations: [{'tour': [10, 18, 29, 24, 16, 30, 26, 0, 3, 12, 19, 6, 2, 22, 23, 1, 33, 4, 15, 21, 7, 13, 11, 28, 17, 8, 5, 9, 31, 27, 25, 20, 14, 32], 'cur_cost': 22262.0}, {'tour': [0, 2, 1, 10, 14, 13, 15, 16, 11, 12, 9, 6, 5, 4, 3, 8, 7, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 19, 18], 'cur_cost': 4935.0}, {'tour': [0, 6, 16, 21, 20, 18, 23, 22, 24, 17, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 2, 7, 9, 1, 15, 13, 14, 10, 12, 11], 'cur_cost': 5306.0}, {'tour': [0, 8, 3, 1, 4, 5, 6, 7, 9, 2, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 3772.0}, {'tour': array([16,  6, 10, 33,  2, 21, 17, 19, 20, 13, 14, 28, 22, 15,  8, 25, 31,
        9, 30, 18,  5, 26,  7, 29, 27, 32, 11,  0, 24,  4,  1, 23,  3, 12],
      dtype=int64), 'cur_cost': 22745.0}, {'tour': [24, 28, 21, 25, 29, 26, 0, 7, 1, 6, 15, 2, 10, 11, 8, 14, 5, 13, 18, 20, 30, 22, 17, 32, 19, 27, 23, 16, 9, 3, 4, 12, 33, 31], 'cur_cost': 16888.0}, {'tour': [30, 33, 26, 0, 5, 10, 6, 11, 4, 8, 9, 29, 23, 22, 21, 25, 20, 19, 31, 24, 16, 7, 2, 13, 15, 12, 18, 32, 17, 27, 28, 3, 1, 14], 'cur_cost': 15999.0}, {'tour': array([29, 15, 31, 11,  2, 13, 20,  6,  8, 30, 18,  7, 21,  0, 22, 24,  5,
       27, 12, 23, 10,  3, 17, 26, 33, 28, 32,  9, 14, 19, 25,  1, 16,  4],
      dtype=int64), 'cur_cost': 25650.0}, {'tour': array([ 4,  3, 31,  8, 20, 19, 13, 33, 25, 15, 26, 24, 29,  9, 27, 16, 21,
       22,  2,  0,  5, 12, 30, 28, 23,  7, 14,  1,  6, 17, 11, 18, 10, 32],
      dtype=int64), 'cur_cost': 23583.0}, {'tour': array([ 8, 27,  5, 32, 19, 11, 10, 12, 31, 30, 16, 21, 24, 13,  6,  9, 33,
        1,  2, 29, 28, 18, 25, 15, 20, 14,  0,  7, 17,  3, 26, 22,  4, 23],
      dtype=int64), 'cur_cost': 23094.0}]
2025-08-05 10:28:48,044 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:48,044 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 171, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 171, 'cache_hits': 0, 'similarity_calculations': 733, 'cache_hit_rate': 0.0, 'cache_size': 733}}
2025-08-05 10:28:48,045 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([29, 15, 31, 11,  2, 13, 20,  6,  8, 30, 18,  7, 21,  0, 22, 24,  5,
       27, 12, 23, 10,  3, 17, 26, 33, 28, 32,  9, 14, 19, 25,  1, 16,  4],
      dtype=int64), 'cur_cost': 25650.0, 'intermediate_solutions': [{'tour': array([ 4, 28, 18, 12, 14, 19, 32, 13,  6, 29,  8, 11, 31,  3,  7, 20, 25,
       24, 10, 30, 27, 17, 16,  1,  5, 26, 15,  9, 23,  0, 22, 21, 33,  2],
      dtype=int64), 'cur_cost': 23901.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([12,  4, 28, 18, 14, 19, 32, 13,  6, 29,  8, 11, 31,  3,  7, 20, 25,
       24, 10, 30, 27, 17, 16,  1,  5, 26, 15,  9, 23,  0, 22, 21, 33,  2],
      dtype=int64), 'cur_cost': 25309.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14, 12,  4, 28, 18, 19, 32, 13,  6, 29,  8, 11, 31,  3,  7, 20, 25,
       24, 10, 30, 27, 17, 16,  1,  5, 26, 15,  9, 23,  0, 22, 21, 33,  2],
      dtype=int64), 'cur_cost': 23804.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([18, 12,  4, 28, 14, 19, 32, 13,  6, 29,  8, 11, 31,  3,  7, 20, 25,
       24, 10, 30, 27, 17, 16,  1,  5, 26, 15,  9, 23,  0, 22, 21, 33,  2],
      dtype=int64), 'cur_cost': 26074.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([18, 14, 12,  4, 28, 19, 32, 13,  6, 29,  8, 11, 31,  3,  7, 20, 25,
       24, 10, 30, 27, 17, 16,  1,  5, 26, 15,  9, 23,  0, 22, 21, 33,  2],
      dtype=int64), 'cur_cost': 24873.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:48,045 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 25650.00)
2025-08-05 10:28:48,045 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:48,046 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:48,046 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:48,048 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 34
2025-08-05 10:28:48,048 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,048 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13437.0, 路径长度: 34, 收集中间解: 0
2025-08-05 10:28:48,048 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [8, 4, 10, 3, 12, 22, 29, 30, 24, 31, 20, 23, 18, 27, 7, 5, 1, 0, 6, 16, 14, 9, 13, 11, 19, 21, 32, 28, 25, 33, 26, 17, 15, 2], 'cur_cost': 13437.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:48,048 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 13437.00)
2025-08-05 10:28:48,049 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:48,049 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:48,049 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:48,050 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 34
2025-08-05 10:28:48,050 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,050 - ExplorationExpert - INFO - 探索路径生成完成，成本: 4885.0, 路径长度: 34, 收集中间解: 0
2025-08-05 10:28:48,050 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 1, 8, 15, 13, 14, 10, 12, 11, 16, 9, 6, 5, 4, 3, 7, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 19, 18], 'cur_cost': 4885.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:48,050 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 4885.00)
2025-08-05 10:28:48,050 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:48,051 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:48,052 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [10, 18, 29, 24, 16, 30, 26, 0, 3, 12, 19, 6, 2, 22, 23, 1, 33, 4, 15, 21, 7, 13, 11, 28, 17, 8, 5, 9, 31, 27, 25, 20, 14, 32], 'cur_cost': 22262.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 1, 10, 14, 13, 15, 16, 11, 12, 9, 6, 5, 4, 3, 8, 7, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 19, 18], 'cur_cost': 4935.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 16, 21, 20, 18, 23, 22, 24, 17, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 2, 7, 9, 1, 15, 13, 14, 10, 12, 11], 'cur_cost': 5306.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 3, 1, 4, 5, 6, 7, 9, 2, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 3772.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([16,  6, 10, 33,  2, 21, 17, 19, 20, 13, 14, 28, 22, 15,  8, 25, 31,
        9, 30, 18,  5, 26,  7, 29, 27, 32, 11,  0, 24,  4,  1, 23,  3, 12],
      dtype=int64), 'cur_cost': 22745.0, 'intermediate_solutions': [{'tour': array([11, 18, 29, 33, 21, 19, 30,  1, 15,  5, 10, 25, 24, 12, 23, 31,  6,
       16,  8, 17,  0,  4,  2, 22,  9, 28, 13, 14, 32,  7, 20,  3, 26, 27],
      dtype=int64), 'cur_cost': 24401.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([33, 11, 18, 29, 21, 19, 30,  1, 15,  5, 10, 25, 24, 12, 23, 31,  6,
       16,  8, 17,  0,  4,  2, 22,  9, 28, 13, 14, 32,  7, 20,  3, 26, 27],
      dtype=int64), 'cur_cost': 24375.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([21, 33, 11, 18, 29, 19, 30,  1, 15,  5, 10, 25, 24, 12, 23, 31,  6,
       16,  8, 17,  0,  4,  2, 22,  9, 28, 13, 14, 32,  7, 20,  3, 26, 27],
      dtype=int64), 'cur_cost': 25659.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([29, 33, 11, 18, 21, 19, 30,  1, 15,  5, 10, 25, 24, 12, 23, 31,  6,
       16,  8, 17,  0,  4,  2, 22,  9, 28, 13, 14, 32,  7, 20,  3, 26, 27],
      dtype=int64), 'cur_cost': 23038.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([29, 21, 33, 11, 18, 19, 30,  1, 15,  5, 10, 25, 24, 12, 23, 31,  6,
       16,  8, 17,  0,  4,  2, 22,  9, 28, 13, 14, 32,  7, 20,  3, 26, 27],
      dtype=int64), 'cur_cost': 24333.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [24, 28, 21, 25, 29, 26, 0, 7, 1, 6, 15, 2, 10, 11, 8, 14, 5, 13, 18, 20, 30, 22, 17, 32, 19, 27, 23, 16, 9, 3, 4, 12, 33, 31], 'cur_cost': 16888.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [30, 33, 26, 0, 5, 10, 6, 11, 4, 8, 9, 29, 23, 22, 21, 25, 20, 19, 31, 24, 16, 7, 2, 13, 15, 12, 18, 32, 17, 27, 28, 3, 1, 14], 'cur_cost': 15999.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([29, 15, 31, 11,  2, 13, 20,  6,  8, 30, 18,  7, 21,  0, 22, 24,  5,
       27, 12, 23, 10,  3, 17, 26, 33, 28, 32,  9, 14, 19, 25,  1, 16,  4],
      dtype=int64), 'cur_cost': 25650.0, 'intermediate_solutions': [{'tour': array([ 4, 28, 18, 12, 14, 19, 32, 13,  6, 29,  8, 11, 31,  3,  7, 20, 25,
       24, 10, 30, 27, 17, 16,  1,  5, 26, 15,  9, 23,  0, 22, 21, 33,  2],
      dtype=int64), 'cur_cost': 23901.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([12,  4, 28, 18, 14, 19, 32, 13,  6, 29,  8, 11, 31,  3,  7, 20, 25,
       24, 10, 30, 27, 17, 16,  1,  5, 26, 15,  9, 23,  0, 22, 21, 33,  2],
      dtype=int64), 'cur_cost': 25309.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14, 12,  4, 28, 18, 19, 32, 13,  6, 29,  8, 11, 31,  3,  7, 20, 25,
       24, 10, 30, 27, 17, 16,  1,  5, 26, 15,  9, 23,  0, 22, 21, 33,  2],
      dtype=int64), 'cur_cost': 23804.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([18, 12,  4, 28, 14, 19, 32, 13,  6, 29,  8, 11, 31,  3,  7, 20, 25,
       24, 10, 30, 27, 17, 16,  1,  5, 26, 15,  9, 23,  0, 22, 21, 33,  2],
      dtype=int64), 'cur_cost': 26074.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([18, 14, 12,  4, 28, 19, 32, 13,  6, 29,  8, 11, 31,  3,  7, 20, 25,
       24, 10, 30, 27, 17, 16,  1,  5, 26, 15,  9, 23,  0, 22, 21, 33,  2],
      dtype=int64), 'cur_cost': 24873.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [8, 4, 10, 3, 12, 22, 29, 30, 24, 31, 20, 23, 18, 27, 7, 5, 1, 0, 6, 16, 14, 9, 13, 11, 19, 21, 32, 28, 25, 33, 26, 17, 15, 2], 'cur_cost': 13437.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 8, 15, 13, 14, 10, 12, 11, 16, 9, 6, 5, 4, 3, 7, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 19, 18], 'cur_cost': 4885.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:48,052 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:48,052 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:48,055 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=3772.000, 多样性=0.961
2025-08-05 10:28:48,055 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 10:28:48,056 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 10:28:48,056 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:48,056 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.06539881945741151, 'best_improvement': 0.016684045881126174}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.000680272108843535}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.011106955366045826, 'recent_improvements': [-0.009109674455190056, 0.001540624646917067, -0.031323585187281705], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 3575, 'new_best_cost': 3575, 'quality_improvement': 0.0, 'old_diversity': 0.7058823529411765, 'new_diversity': 0.7058823529411765, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配']}
2025-08-05 10:28:48,057 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 10:28:48,057 - __main__ - INFO - composite2_34 开始进化第 2 代
2025-08-05 10:28:48,057 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 10:28:48,058 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:48,059 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=3772.000, 多样性=0.961
2025-08-05 10:28:48,060 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:48,068 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.961
2025-08-05 10:28:48,069 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:48,071 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.706
2025-08-05 10:28:48,077 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 10:28:48,077 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:48,078 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-05 10:28:48,078 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-05 10:28:48,109 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.154, 适应度梯度: 60.200, 聚类评分: 0.000, 覆盖率: 0.081, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:48,110 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 10:28:48,110 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:48,110 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite2_34
2025-08-05 10:28:48,116 - visualization.landscape_visualizer - INFO - 插值约束: 26 个点被约束到最小值 3575.00
2025-08-05 10:28:48,117 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.1%, 梯度: 735.30 → 682.76
2025-08-05 10:28:48,243 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite2_34\landscape_composite2_34_iter_67_20250805_102848.html
2025-08-05 10:28:48,294 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite2_34\dashboard_composite2_34_iter_67_20250805_102848.html
2025-08-05 10:28:48,294 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 67
2025-08-05 10:28:48,294 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 10:28:48,294 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2171秒
2025-08-05 10:28:48,294 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.15384615384615385, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 60.200000000000074, 'local_optima_density': 0.15384615384615385, 'gradient_variance': 48613748.16000001, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0806, 'fitness_entropy': 0.7872226051203909, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.081)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 60.200)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360928.110506, 'performance_metrics': {}}}
2025-08-05 10:28:48,295 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:48,295 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:48,295 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:48,295 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:48,295 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:48,295 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 10:28:48,295 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:48,295 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:48,295 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:48,296 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:48,296 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:48,296 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:48,296 - experts.management.collaboration_manager - INFO - 识别精英个体: {9, 3} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:48,296 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:48,296 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:48,296 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:48,297 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 34
2025-08-05 10:28:48,298 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,298 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,298 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,299 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,299 - ExplorationExpert - INFO - 探索路径生成完成，成本: 6676.0, 路径长度: 34, 收集中间解: 3
2025-08-05 10:28:48,299 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 24, 9, 17, 23, 22, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 7, 2, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 6676.0, 'intermediate_solutions': [{'tour': [33, 18, 29, 24, 16, 30, 26, 0, 3, 12, 19, 6, 2, 22, 23, 1, 10, 4, 15, 21, 7, 13, 11, 28, 17, 8, 5, 9, 31, 27, 25, 20, 14, 32], 'cur_cost': 20955.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 18, 29, 24, 16, 30, 26, 0, 3, 12, 19, 6, 2, 22, 23, 1, 33, 4, 15, 21, 7, 13, 11, 28, 17, 8, 5, 27, 31, 9, 25, 20, 14, 32], 'cur_cost': 23949.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 18, 29, 26, 24, 16, 30, 0, 3, 12, 19, 6, 2, 22, 23, 1, 33, 4, 15, 21, 7, 13, 11, 28, 17, 8, 5, 9, 31, 27, 25, 20, 14, 32], 'cur_cost': 22204.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:48,299 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 6676.00)
2025-08-05 10:28:48,299 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:48,299 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:48,299 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:48,301 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 34
2025-08-05 10:28:48,301 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,301 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,301 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,302 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,302 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15567.0, 路径长度: 34, 收集中间解: 3
2025-08-05 10:28:48,302 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [26, 21, 25, 19, 32, 18, 12, 14, 13, 6, 1, 3, 5, 7, 33, 27, 28, 24, 30, 20, 31, 23, 10, 9, 16, 11, 2, 4, 8, 15, 0, 29, 22, 17], 'cur_cost': 15567.0, 'intermediate_solutions': [{'tour': [0, 2, 1, 10, 14, 13, 15, 16, 11, 12, 9, 6, 5, 4, 3, 8, 7, 33, 30, 29, 28, 32, 31, 20, 26, 27, 24, 22, 23, 17, 25, 21, 19, 18], 'cur_cost': 7393.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 1, 10, 14, 13, 15, 16, 11, 12, 9, 6, 5, 4, 3, 8, 7, 33, 28, 29, 30, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 19, 18], 'cur_cost': 4959.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 2, 1, 10, 14, 13, 15, 16, 11, 12, 9, 6, 5, 4, 3, 8, 7, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 21, 19, 18], 'cur_cost': 6973.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:48,302 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 15567.00)
2025-08-05 10:28:48,302 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:48,302 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:48,302 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:48,303 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 34
2025-08-05 10:28:48,303 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,303 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,303 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,304 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,304 - ExplorationExpert - INFO - 探索路径生成完成，成本: 6972.0, 路径长度: 34, 收集中间解: 3
2025-08-05 10:28:48,304 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 21, 5, 10, 14, 13, 15, 16, 11, 12, 9, 6, 7, 8, 3, 4, 1, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 18, 19], 'cur_cost': 6972.0, 'intermediate_solutions': [{'tour': [0, 6, 16, 21, 20, 18, 23, 22, 24, 17, 19, 25, 31, 30, 11, 28, 32, 26, 27, 33, 8, 3, 4, 5, 2, 7, 9, 1, 15, 13, 14, 10, 12, 29], 'cur_cost': 8767.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 16, 21, 20, 18, 23, 22, 24, 17, 19, 25, 31, 30, 29, 32, 28, 26, 27, 33, 8, 3, 4, 5, 2, 7, 9, 1, 15, 13, 14, 10, 12, 11], 'cur_cost': 5318.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 21, 20, 18, 23, 22, 24, 17, 19, 25, 31, 30, 29, 28, 16, 32, 26, 27, 33, 8, 3, 4, 5, 2, 7, 9, 1, 15, 13, 14, 10, 12, 11], 'cur_cost': 7092.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:48,304 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 6972.00)
2025-08-05 10:28:48,304 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:48,304 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:48,304 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:48,305 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 34
2025-08-05 10:28:48,306 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,306 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,306 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,306 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,307 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16482.0, 路径长度: 34, 收集中间解: 3
2025-08-05 10:28:48,307 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [16, 24, 9, 19, 23, 18, 15, 20, 13, 12, 25, 31, 30, 29, 28, 32, 26, 2, 6, 8, 3, 4, 5, 11, 7, 21, 1, 0, 22, 27, 14, 10, 33, 17], 'cur_cost': 16482.0, 'intermediate_solutions': [{'tour': [0, 8, 3, 1, 4, 5, 6, 7, 9, 2, 16, 15, 13, 14, 10, 12, 33, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 11], 'cur_cost': 5680.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 3, 1, 4, 5, 6, 7, 9, 2, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 33, 27], 'cur_cost': 3840.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 2, 3, 1, 4, 5, 6, 7, 9, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 3763.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:48,307 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 16482.00)
2025-08-05 10:28:48,307 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:28:48,307 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:48,307 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:48,307 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 24348.0
2025-08-05 10:28:48,316 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:28:48,316 - ExploitationExpert - INFO - res_population_costs: [3575, 3575, 3576.0, 3575, 3575]
2025-08-05 10:28:48,316 - ExploitationExpert - INFO - res_populations: [array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 13, 12, 11, 15, 16,  9,  7,  6,  2,  5,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 14, 13, 11, 12, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64)]
2025-08-05 10:28:48,318 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:48,318 - ExploitationExpert - INFO - populations: [{'tour': [0, 24, 9, 17, 23, 22, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 7, 2, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 6676.0}, {'tour': [26, 21, 25, 19, 32, 18, 12, 14, 13, 6, 1, 3, 5, 7, 33, 27, 28, 24, 30, 20, 31, 23, 10, 9, 16, 11, 2, 4, 8, 15, 0, 29, 22, 17], 'cur_cost': 15567.0}, {'tour': [0, 21, 5, 10, 14, 13, 15, 16, 11, 12, 9, 6, 7, 8, 3, 4, 1, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 18, 19], 'cur_cost': 6972.0}, {'tour': [16, 24, 9, 19, 23, 18, 15, 20, 13, 12, 25, 31, 30, 29, 28, 32, 26, 2, 6, 8, 3, 4, 5, 11, 7, 21, 1, 0, 22, 27, 14, 10, 33, 17], 'cur_cost': 16482.0}, {'tour': array([16,  5, 31, 17, 20, 13,  4, 28, 12, 23, 11,  3,  9, 26,  2, 27, 32,
        6, 33,  0, 30, 22, 21,  1, 10,  8, 18, 29, 24, 25, 14, 15,  7, 19],
      dtype=int64), 'cur_cost': 24348.0}, {'tour': [24, 28, 21, 25, 29, 26, 0, 7, 1, 6, 15, 2, 10, 11, 8, 14, 5, 13, 18, 20, 30, 22, 17, 32, 19, 27, 23, 16, 9, 3, 4, 12, 33, 31], 'cur_cost': 16888.0}, {'tour': [30, 33, 26, 0, 5, 10, 6, 11, 4, 8, 9, 29, 23, 22, 21, 25, 20, 19, 31, 24, 16, 7, 2, 13, 15, 12, 18, 32, 17, 27, 28, 3, 1, 14], 'cur_cost': 15999.0}, {'tour': [29, 15, 31, 11, 2, 13, 20, 6, 8, 30, 18, 7, 21, 0, 22, 24, 5, 27, 12, 23, 10, 3, 17, 26, 33, 28, 32, 9, 14, 19, 25, 1, 16, 4], 'cur_cost': 25650.0}, {'tour': [8, 4, 10, 3, 12, 22, 29, 30, 24, 31, 20, 23, 18, 27, 7, 5, 1, 0, 6, 16, 14, 9, 13, 11, 19, 21, 32, 28, 25, 33, 26, 17, 15, 2], 'cur_cost': 13437.0}, {'tour': [0, 1, 8, 15, 13, 14, 10, 12, 11, 16, 9, 6, 5, 4, 3, 7, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 19, 18], 'cur_cost': 4885.0}]
2025-08-05 10:28:48,319 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:48,319 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 172, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 172, 'cache_hits': 0, 'similarity_calculations': 735, 'cache_hit_rate': 0.0, 'cache_size': 735}}
2025-08-05 10:28:48,320 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([16,  5, 31, 17, 20, 13,  4, 28, 12, 23, 11,  3,  9, 26,  2, 27, 32,
        6, 33,  0, 30, 22, 21,  1, 10,  8, 18, 29, 24, 25, 14, 15,  7, 19],
      dtype=int64), 'cur_cost': 24348.0, 'intermediate_solutions': [{'tour': array([10,  6, 16, 33,  2, 21, 17, 19, 20, 13, 14, 28, 22, 15,  8, 25, 31,
        9, 30, 18,  5, 26,  7, 29, 27, 32, 11,  0, 24,  4,  1, 23,  3, 12]), 'cur_cost': 22725.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([33, 10,  6, 16,  2, 21, 17, 19, 20, 13, 14, 28, 22, 15,  8, 25, 31,
        9, 30, 18,  5, 26,  7, 29, 27, 32, 11,  0, 24,  4,  1, 23,  3, 12]), 'cur_cost': 23514.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 2, 33, 10,  6, 16, 21, 17, 19, 20, 13, 14, 28, 22, 15,  8, 25, 31,
        9, 30, 18,  5, 26,  7, 29, 27, 32, 11,  0, 24,  4,  1, 23,  3, 12]), 'cur_cost': 23090.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([16, 33, 10,  6,  2, 21, 17, 19, 20, 13, 14, 28, 22, 15,  8, 25, 31,
        9, 30, 18,  5, 26,  7, 29, 27, 32, 11,  0, 24,  4,  1, 23,  3, 12]), 'cur_cost': 22283.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([16,  2, 33, 10,  6, 21, 17, 19, 20, 13, 14, 28, 22, 15,  8, 25, 31,
        9, 30, 18,  5, 26,  7, 29, 27, 32, 11,  0, 24,  4,  1, 23,  3, 12]), 'cur_cost': 22731.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:48,320 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 24348.00)
2025-08-05 10:28:48,320 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:48,320 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:48,320 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:48,321 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 34
2025-08-05 10:28:48,321 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,322 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,322 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,322 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,322 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20824.0, 路径长度: 34, 收集中间解: 3
2025-08-05 10:28:48,322 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [24, 28, 5, 25, 32, 26, 12, 16, 1, 2, 7, 22, 33, 4, 20, 18, 30, 31, 3, 10, 9, 15, 27, 0, 17, 13, 14, 29, 19, 21, 23, 8, 11, 6], 'cur_cost': 20824.0, 'intermediate_solutions': [{'tour': [24, 28, 21, 25, 29, 26, 0, 7, 1, 6, 15, 2, 10, 11, 8, 14, 5, 13, 18, 20, 30, 22, 3, 32, 19, 27, 23, 16, 9, 17, 4, 12, 33, 31], 'cur_cost': 20068.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [24, 28, 21, 25, 29, 26, 0, 4, 3, 9, 16, 23, 27, 19, 32, 17, 22, 30, 20, 18, 13, 5, 14, 8, 11, 10, 2, 15, 6, 1, 7, 12, 33, 31], 'cur_cost': 16870.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [24, 28, 21, 25, 29, 26, 0, 1, 6, 15, 2, 10, 7, 11, 8, 14, 5, 13, 18, 20, 30, 22, 17, 32, 19, 27, 23, 16, 9, 3, 4, 12, 33, 31], 'cur_cost': 18125.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:48,323 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 20824.00)
2025-08-05 10:28:48,323 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:48,323 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:48,323 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:48,324 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 34
2025-08-05 10:28:48,324 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,324 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,324 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,324 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,325 - ExplorationExpert - INFO - 探索路径生成完成，成本: 5128.0, 路径长度: 34, 收集中间解: 3
2025-08-05 10:28:48,325 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 8, 11, 3, 4, 5, 6, 7, 9, 2, 1, 16, 15, 13, 14, 10, 12, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 5128.0, 'intermediate_solutions': [{'tour': [30, 33, 26, 0, 5, 10, 6, 11, 4, 8, 24, 29, 23, 22, 21, 25, 20, 19, 31, 9, 16, 7, 2, 13, 15, 12, 18, 32, 17, 27, 28, 3, 1, 14], 'cur_cost': 16827.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [30, 33, 26, 0, 5, 10, 6, 11, 4, 8, 9, 29, 23, 22, 21, 25, 20, 19, 31, 24, 16, 7, 2, 13, 15, 12, 18, 32, 17, 27, 28, 3, 14, 1], 'cur_cost': 16494.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [30, 33, 26, 0, 5, 10, 6, 11, 4, 8, 9, 29, 23, 22, 21, 25, 20, 19, 31, 24, 16, 2, 7, 13, 15, 12, 18, 32, 17, 27, 28, 3, 1, 14], 'cur_cost': 16000.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:48,325 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 5128.00)
2025-08-05 10:28:48,325 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:28:48,325 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:48,325 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:48,325 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 24249.0
2025-08-05 10:28:48,335 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 10:28:48,335 - ExploitationExpert - INFO - res_population_costs: [3575, 3575, 3576.0, 3575, 3575, 3575]
2025-08-05 10:28:48,335 - ExploitationExpert - INFO - res_populations: [array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 13, 12, 11, 15, 16,  9,  7,  6,  2,  5,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 14, 13, 11, 12, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0, 33, 30, 29, 28, 27, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64)]
2025-08-05 10:28:48,337 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:48,337 - ExploitationExpert - INFO - populations: [{'tour': [0, 24, 9, 17, 23, 22, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 7, 2, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 6676.0}, {'tour': [26, 21, 25, 19, 32, 18, 12, 14, 13, 6, 1, 3, 5, 7, 33, 27, 28, 24, 30, 20, 31, 23, 10, 9, 16, 11, 2, 4, 8, 15, 0, 29, 22, 17], 'cur_cost': 15567.0}, {'tour': [0, 21, 5, 10, 14, 13, 15, 16, 11, 12, 9, 6, 7, 8, 3, 4, 1, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 18, 19], 'cur_cost': 6972.0}, {'tour': [16, 24, 9, 19, 23, 18, 15, 20, 13, 12, 25, 31, 30, 29, 28, 32, 26, 2, 6, 8, 3, 4, 5, 11, 7, 21, 1, 0, 22, 27, 14, 10, 33, 17], 'cur_cost': 16482.0}, {'tour': array([16,  5, 31, 17, 20, 13,  4, 28, 12, 23, 11,  3,  9, 26,  2, 27, 32,
        6, 33,  0, 30, 22, 21,  1, 10,  8, 18, 29, 24, 25, 14, 15,  7, 19],
      dtype=int64), 'cur_cost': 24348.0}, {'tour': [24, 28, 5, 25, 32, 26, 12, 16, 1, 2, 7, 22, 33, 4, 20, 18, 30, 31, 3, 10, 9, 15, 27, 0, 17, 13, 14, 29, 19, 21, 23, 8, 11, 6], 'cur_cost': 20824.0}, {'tour': [0, 8, 11, 3, 4, 5, 6, 7, 9, 2, 1, 16, 15, 13, 14, 10, 12, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 5128.0}, {'tour': array([20, 28, 32, 21, 17,  1, 15, 22, 12,  3,  9, 10,  5, 31,  4, 13, 30,
        6, 33, 16, 24, 25,  8,  0, 23, 26, 11, 19,  7, 27, 29, 14,  2, 18],
      dtype=int64), 'cur_cost': 24249.0}, {'tour': [8, 4, 10, 3, 12, 22, 29, 30, 24, 31, 20, 23, 18, 27, 7, 5, 1, 0, 6, 16, 14, 9, 13, 11, 19, 21, 32, 28, 25, 33, 26, 17, 15, 2], 'cur_cost': 13437.0}, {'tour': [0, 1, 8, 15, 13, 14, 10, 12, 11, 16, 9, 6, 5, 4, 3, 7, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 19, 18], 'cur_cost': 4885.0}]
2025-08-05 10:28:48,337 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:48,338 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 173, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 173, 'cache_hits': 0, 'similarity_calculations': 738, 'cache_hit_rate': 0.0, 'cache_size': 738}}
2025-08-05 10:28:48,338 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([20, 28, 32, 21, 17,  1, 15, 22, 12,  3,  9, 10,  5, 31,  4, 13, 30,
        6, 33, 16, 24, 25,  8,  0, 23, 26, 11, 19,  7, 27, 29, 14,  2, 18],
      dtype=int64), 'cur_cost': 24249.0, 'intermediate_solutions': [{'tour': array([31, 15, 29, 11,  2, 13, 20,  6,  8, 30, 18,  7, 21,  0, 22, 24,  5,
       27, 12, 23, 10,  3, 17, 26, 33, 28, 32,  9, 14, 19, 25,  1, 16,  4]), 'cur_cost': 25663.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([11, 31, 15, 29,  2, 13, 20,  6,  8, 30, 18,  7, 21,  0, 22, 24,  5,
       27, 12, 23, 10,  3, 17, 26, 33, 28, 32,  9, 14, 19, 25,  1, 16,  4]), 'cur_cost': 25698.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 2, 11, 31, 15, 29, 13, 20,  6,  8, 30, 18,  7, 21,  0, 22, 24,  5,
       27, 12, 23, 10,  3, 17, 26, 33, 28, 32,  9, 14, 19, 25,  1, 16,  4]), 'cur_cost': 25183.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([29, 11, 31, 15,  2, 13, 20,  6,  8, 30, 18,  7, 21,  0, 22, 24,  5,
       27, 12, 23, 10,  3, 17, 26, 33, 28, 32,  9, 14, 19, 25,  1, 16,  4]), 'cur_cost': 25681.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([29,  2, 11, 31, 15, 13, 20,  6,  8, 30, 18,  7, 21,  0, 22, 24,  5,
       27, 12, 23, 10,  3, 17, 26, 33, 28, 32,  9, 14, 19, 25,  1, 16,  4]), 'cur_cost': 24800.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:48,338 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 24249.00)
2025-08-05 10:28:48,339 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:48,339 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:48,339 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:48,341 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 34
2025-08-05 10:28:48,341 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,341 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,341 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,341 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,342 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13118.0, 路径长度: 34, 收集中间解: 3
2025-08-05 10:28:48,342 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [3, 4, 0, 7, 2, 11, 12, 23, 24, 20, 22, 18, 32, 21, 31, 30, 26, 29, 19, 27, 1, 15, 9, 14, 6, 8, 16, 13, 10, 5, 33, 28, 25, 17], 'cur_cost': 13118.0, 'intermediate_solutions': [{'tour': [8, 17, 10, 3, 12, 22, 29, 30, 24, 31, 20, 23, 18, 27, 7, 5, 1, 0, 6, 16, 14, 9, 13, 11, 19, 21, 32, 28, 25, 33, 26, 4, 15, 2], 'cur_cost': 14622.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 4, 10, 3, 12, 22, 29, 23, 20, 31, 24, 30, 18, 27, 7, 5, 1, 0, 6, 16, 14, 9, 13, 11, 19, 21, 32, 28, 25, 33, 26, 17, 15, 2], 'cur_cost': 14752.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 30, 4, 10, 3, 12, 22, 29, 24, 31, 20, 23, 18, 27, 7, 5, 1, 0, 6, 16, 14, 9, 13, 11, 19, 21, 32, 28, 25, 33, 26, 17, 15, 2], 'cur_cost': 15171.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:48,342 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 13118.00)
2025-08-05 10:28:48,342 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:48,342 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:48,342 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:48,344 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 34
2025-08-05 10:28:48,344 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,344 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,344 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,345 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,345 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15592.0, 路径长度: 34, 收集中间解: 3
2025-08-05 10:28:48,345 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [12, 23, 22, 20, 17, 31, 18, 29, 33, 26, 30, 7, 4, 3, 1, 15, 0, 9, 16, 6, 8, 11, 10, 24, 25, 19, 21, 28, 5, 14, 2, 13, 32, 27], 'cur_cost': 15592.0, 'intermediate_solutions': [{'tour': [0, 1, 8, 15, 13, 14, 10, 12, 11, 16, 9, 6, 26, 4, 3, 7, 2, 33, 30, 29, 28, 32, 31, 25, 5, 27, 24, 22, 23, 17, 20, 21, 19, 18], 'cur_cost': 8468.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 8, 15, 13, 14, 10, 12, 11, 16, 9, 6, 5, 4, 3, 7, 2, 33, 20, 17, 23, 22, 24, 27, 26, 25, 31, 32, 28, 29, 30, 21, 19, 18], 'cur_cost': 6175.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 8, 15, 14, 10, 12, 11, 16, 9, 6, 5, 4, 3, 7, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 13, 19, 18], 'cur_cost': 6458.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:48,345 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 15592.00)
2025-08-05 10:28:48,345 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:48,345 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:48,347 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 24, 9, 17, 23, 22, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 7, 2, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 6676.0, 'intermediate_solutions': [{'tour': [33, 18, 29, 24, 16, 30, 26, 0, 3, 12, 19, 6, 2, 22, 23, 1, 10, 4, 15, 21, 7, 13, 11, 28, 17, 8, 5, 9, 31, 27, 25, 20, 14, 32], 'cur_cost': 20955.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 18, 29, 24, 16, 30, 26, 0, 3, 12, 19, 6, 2, 22, 23, 1, 33, 4, 15, 21, 7, 13, 11, 28, 17, 8, 5, 27, 31, 9, 25, 20, 14, 32], 'cur_cost': 23949.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 18, 29, 26, 24, 16, 30, 0, 3, 12, 19, 6, 2, 22, 23, 1, 33, 4, 15, 21, 7, 13, 11, 28, 17, 8, 5, 9, 31, 27, 25, 20, 14, 32], 'cur_cost': 22204.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [26, 21, 25, 19, 32, 18, 12, 14, 13, 6, 1, 3, 5, 7, 33, 27, 28, 24, 30, 20, 31, 23, 10, 9, 16, 11, 2, 4, 8, 15, 0, 29, 22, 17], 'cur_cost': 15567.0, 'intermediate_solutions': [{'tour': [0, 2, 1, 10, 14, 13, 15, 16, 11, 12, 9, 6, 5, 4, 3, 8, 7, 33, 30, 29, 28, 32, 31, 20, 26, 27, 24, 22, 23, 17, 25, 21, 19, 18], 'cur_cost': 7393.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 1, 10, 14, 13, 15, 16, 11, 12, 9, 6, 5, 4, 3, 8, 7, 33, 28, 29, 30, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 19, 18], 'cur_cost': 4959.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 2, 1, 10, 14, 13, 15, 16, 11, 12, 9, 6, 5, 4, 3, 8, 7, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 21, 19, 18], 'cur_cost': 6973.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 5, 10, 14, 13, 15, 16, 11, 12, 9, 6, 7, 8, 3, 4, 1, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 18, 19], 'cur_cost': 6972.0, 'intermediate_solutions': [{'tour': [0, 6, 16, 21, 20, 18, 23, 22, 24, 17, 19, 25, 31, 30, 11, 28, 32, 26, 27, 33, 8, 3, 4, 5, 2, 7, 9, 1, 15, 13, 14, 10, 12, 29], 'cur_cost': 8767.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 16, 21, 20, 18, 23, 22, 24, 17, 19, 25, 31, 30, 29, 32, 28, 26, 27, 33, 8, 3, 4, 5, 2, 7, 9, 1, 15, 13, 14, 10, 12, 11], 'cur_cost': 5318.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 21, 20, 18, 23, 22, 24, 17, 19, 25, 31, 30, 29, 28, 16, 32, 26, 27, 33, 8, 3, 4, 5, 2, 7, 9, 1, 15, 13, 14, 10, 12, 11], 'cur_cost': 7092.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [16, 24, 9, 19, 23, 18, 15, 20, 13, 12, 25, 31, 30, 29, 28, 32, 26, 2, 6, 8, 3, 4, 5, 11, 7, 21, 1, 0, 22, 27, 14, 10, 33, 17], 'cur_cost': 16482.0, 'intermediate_solutions': [{'tour': [0, 8, 3, 1, 4, 5, 6, 7, 9, 2, 16, 15, 13, 14, 10, 12, 33, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 11], 'cur_cost': 5680.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 3, 1, 4, 5, 6, 7, 9, 2, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 33, 27], 'cur_cost': 3840.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 2, 3, 1, 4, 5, 6, 7, 9, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 3763.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([16,  5, 31, 17, 20, 13,  4, 28, 12, 23, 11,  3,  9, 26,  2, 27, 32,
        6, 33,  0, 30, 22, 21,  1, 10,  8, 18, 29, 24, 25, 14, 15,  7, 19],
      dtype=int64), 'cur_cost': 24348.0, 'intermediate_solutions': [{'tour': array([10,  6, 16, 33,  2, 21, 17, 19, 20, 13, 14, 28, 22, 15,  8, 25, 31,
        9, 30, 18,  5, 26,  7, 29, 27, 32, 11,  0, 24,  4,  1, 23,  3, 12]), 'cur_cost': 22725.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([33, 10,  6, 16,  2, 21, 17, 19, 20, 13, 14, 28, 22, 15,  8, 25, 31,
        9, 30, 18,  5, 26,  7, 29, 27, 32, 11,  0, 24,  4,  1, 23,  3, 12]), 'cur_cost': 23514.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 2, 33, 10,  6, 16, 21, 17, 19, 20, 13, 14, 28, 22, 15,  8, 25, 31,
        9, 30, 18,  5, 26,  7, 29, 27, 32, 11,  0, 24,  4,  1, 23,  3, 12]), 'cur_cost': 23090.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([16, 33, 10,  6,  2, 21, 17, 19, 20, 13, 14, 28, 22, 15,  8, 25, 31,
        9, 30, 18,  5, 26,  7, 29, 27, 32, 11,  0, 24,  4,  1, 23,  3, 12]), 'cur_cost': 22283.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([16,  2, 33, 10,  6, 21, 17, 19, 20, 13, 14, 28, 22, 15,  8, 25, 31,
        9, 30, 18,  5, 26,  7, 29, 27, 32, 11,  0, 24,  4,  1, 23,  3, 12]), 'cur_cost': 22731.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [24, 28, 5, 25, 32, 26, 12, 16, 1, 2, 7, 22, 33, 4, 20, 18, 30, 31, 3, 10, 9, 15, 27, 0, 17, 13, 14, 29, 19, 21, 23, 8, 11, 6], 'cur_cost': 20824.0, 'intermediate_solutions': [{'tour': [24, 28, 21, 25, 29, 26, 0, 7, 1, 6, 15, 2, 10, 11, 8, 14, 5, 13, 18, 20, 30, 22, 3, 32, 19, 27, 23, 16, 9, 17, 4, 12, 33, 31], 'cur_cost': 20068.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [24, 28, 21, 25, 29, 26, 0, 4, 3, 9, 16, 23, 27, 19, 32, 17, 22, 30, 20, 18, 13, 5, 14, 8, 11, 10, 2, 15, 6, 1, 7, 12, 33, 31], 'cur_cost': 16870.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [24, 28, 21, 25, 29, 26, 0, 1, 6, 15, 2, 10, 7, 11, 8, 14, 5, 13, 18, 20, 30, 22, 17, 32, 19, 27, 23, 16, 9, 3, 4, 12, 33, 31], 'cur_cost': 18125.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 11, 3, 4, 5, 6, 7, 9, 2, 1, 16, 15, 13, 14, 10, 12, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 5128.0, 'intermediate_solutions': [{'tour': [30, 33, 26, 0, 5, 10, 6, 11, 4, 8, 24, 29, 23, 22, 21, 25, 20, 19, 31, 9, 16, 7, 2, 13, 15, 12, 18, 32, 17, 27, 28, 3, 1, 14], 'cur_cost': 16827.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [30, 33, 26, 0, 5, 10, 6, 11, 4, 8, 9, 29, 23, 22, 21, 25, 20, 19, 31, 24, 16, 7, 2, 13, 15, 12, 18, 32, 17, 27, 28, 3, 14, 1], 'cur_cost': 16494.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [30, 33, 26, 0, 5, 10, 6, 11, 4, 8, 9, 29, 23, 22, 21, 25, 20, 19, 31, 24, 16, 2, 7, 13, 15, 12, 18, 32, 17, 27, 28, 3, 1, 14], 'cur_cost': 16000.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([20, 28, 32, 21, 17,  1, 15, 22, 12,  3,  9, 10,  5, 31,  4, 13, 30,
        6, 33, 16, 24, 25,  8,  0, 23, 26, 11, 19,  7, 27, 29, 14,  2, 18],
      dtype=int64), 'cur_cost': 24249.0, 'intermediate_solutions': [{'tour': array([31, 15, 29, 11,  2, 13, 20,  6,  8, 30, 18,  7, 21,  0, 22, 24,  5,
       27, 12, 23, 10,  3, 17, 26, 33, 28, 32,  9, 14, 19, 25,  1, 16,  4]), 'cur_cost': 25663.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([11, 31, 15, 29,  2, 13, 20,  6,  8, 30, 18,  7, 21,  0, 22, 24,  5,
       27, 12, 23, 10,  3, 17, 26, 33, 28, 32,  9, 14, 19, 25,  1, 16,  4]), 'cur_cost': 25698.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 2, 11, 31, 15, 29, 13, 20,  6,  8, 30, 18,  7, 21,  0, 22, 24,  5,
       27, 12, 23, 10,  3, 17, 26, 33, 28, 32,  9, 14, 19, 25,  1, 16,  4]), 'cur_cost': 25183.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([29, 11, 31, 15,  2, 13, 20,  6,  8, 30, 18,  7, 21,  0, 22, 24,  5,
       27, 12, 23, 10,  3, 17, 26, 33, 28, 32,  9, 14, 19, 25,  1, 16,  4]), 'cur_cost': 25681.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([29,  2, 11, 31, 15, 13, 20,  6,  8, 30, 18,  7, 21,  0, 22, 24,  5,
       27, 12, 23, 10,  3, 17, 26, 33, 28, 32,  9, 14, 19, 25,  1, 16,  4]), 'cur_cost': 24800.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [3, 4, 0, 7, 2, 11, 12, 23, 24, 20, 22, 18, 32, 21, 31, 30, 26, 29, 19, 27, 1, 15, 9, 14, 6, 8, 16, 13, 10, 5, 33, 28, 25, 17], 'cur_cost': 13118.0, 'intermediate_solutions': [{'tour': [8, 17, 10, 3, 12, 22, 29, 30, 24, 31, 20, 23, 18, 27, 7, 5, 1, 0, 6, 16, 14, 9, 13, 11, 19, 21, 32, 28, 25, 33, 26, 4, 15, 2], 'cur_cost': 14622.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 4, 10, 3, 12, 22, 29, 23, 20, 31, 24, 30, 18, 27, 7, 5, 1, 0, 6, 16, 14, 9, 13, 11, 19, 21, 32, 28, 25, 33, 26, 17, 15, 2], 'cur_cost': 14752.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 30, 4, 10, 3, 12, 22, 29, 24, 31, 20, 23, 18, 27, 7, 5, 1, 0, 6, 16, 14, 9, 13, 11, 19, 21, 32, 28, 25, 33, 26, 17, 15, 2], 'cur_cost': 15171.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [12, 23, 22, 20, 17, 31, 18, 29, 33, 26, 30, 7, 4, 3, 1, 15, 0, 9, 16, 6, 8, 11, 10, 24, 25, 19, 21, 28, 5, 14, 2, 13, 32, 27], 'cur_cost': 15592.0, 'intermediate_solutions': [{'tour': [0, 1, 8, 15, 13, 14, 10, 12, 11, 16, 9, 6, 26, 4, 3, 7, 2, 33, 30, 29, 28, 32, 31, 25, 5, 27, 24, 22, 23, 17, 20, 21, 19, 18], 'cur_cost': 8468.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 8, 15, 13, 14, 10, 12, 11, 16, 9, 6, 5, 4, 3, 7, 2, 33, 20, 17, 23, 22, 24, 27, 26, 25, 31, 32, 28, 29, 30, 21, 19, 18], 'cur_cost': 6175.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 8, 15, 14, 10, 12, 11, 16, 9, 6, 5, 4, 3, 7, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 13, 19, 18], 'cur_cost': 6458.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:48,347 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:48,347 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:48,349 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=5128.000, 多样性=0.951
2025-08-05 10:28:48,349 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 10:28:48,349 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 10:28:48,349 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:48,350 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.13499026312930193, 'best_improvement': -0.35949098621421}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.01087695445275319}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.03192909740524722, 'recent_improvements': [0.001540624646917067, -0.031323585187281705, 0.06539881945741151], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 6, 'new_count': 6, 'count_change': 0, 'old_best_cost': 3575, 'new_best_cost': 3575, 'quality_improvement': 0.0, 'old_diversity': 0.6352941176470589, 'new_diversity': 0.6352941176470589, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 10:28:48,350 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 10:28:48,350 - __main__ - INFO - composite2_34 开始进化第 3 代
2025-08-05 10:28:48,350 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 10:28:48,351 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:48,351 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=5128.000, 多样性=0.951
2025-08-05 10:28:48,351 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:48,353 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.951
2025-08-05 10:28:48,353 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:48,355 - EliteExpert - INFO - 精英解分析完成: 精英解数量=6, 多样性=0.635
2025-08-05 10:28:48,358 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 10:28:48,358 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:48,358 - LandscapeExpert - INFO - 添加精英解数据: 6个精英解
2025-08-05 10:28:48,358 - LandscapeExpert - INFO - 数据提取成功: 16个路径, 16个适应度值
2025-08-05 10:28:48,391 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.312, 适应度梯度: -176.675, 聚类评分: 0.000, 覆盖率: 0.082, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:48,392 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 10:28:48,392 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:48,392 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite2_34
2025-08-05 10:28:48,401 - visualization.landscape_visualizer - INFO - 插值约束: 154 个点被约束到最小值 3575.00
2025-08-05 10:28:48,404 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=6.0%, 梯度: 874.06 → 821.73
2025-08-05 10:28:48,521 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite2_34\landscape_composite2_34_iter_68_20250805_102848.html
2025-08-05 10:28:48,575 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite2_34\dashboard_composite2_34_iter_68_20250805_102848.html
2025-08-05 10:28:48,575 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 68
2025-08-05 10:28:48,576 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 10:28:48,576 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2176秒
2025-08-05 10:28:48,576 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3125, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -176.67500000000018, 'local_optima_density': 0.3125, 'gradient_variance': 37747428.639375, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0818, 'fitness_entropy': 0.8537510532665803, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -176.675)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.082)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360928.392471, 'performance_metrics': {}}}
2025-08-05 10:28:48,576 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:48,576 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:48,576 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:48,577 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:48,577 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:48,577 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 10:28:48,577 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:48,577 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:48,577 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:48,577 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:48,577 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:48,578 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:48,578 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 6} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:48,578 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:48,578 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:48,578 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:48,580 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 34
2025-08-05 10:28:48,580 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,580 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,580 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,581 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,581 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14781.0, 路径长度: 34, 收集中间解: 3
2025-08-05 10:28:48,581 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [29, 24, 25, 18, 31, 0, 2, 14, 4, 10, 8, 13, 7, 9, 12, 22, 17, 21, 19, 33, 28, 27, 26, 32, 1, 5, 6, 15, 16, 3, 11, 23, 20, 30], 'cur_cost': 14781.0, 'intermediate_solutions': [{'tour': [0, 24, 9, 17, 23, 22, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 7, 2, 1, 16, 15, 12, 14, 10, 13, 11], 'cur_cost': 6710.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 24, 9, 17, 23, 22, 21, 29, 30, 31, 25, 19, 18, 20, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 7, 2, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 8011.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 24, 9, 17, 23, 22, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 7, 26, 27, 33, 8, 3, 4, 5, 6, 2, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 8433.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:48,581 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 14781.00)
2025-08-05 10:28:48,581 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:48,581 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:48,581 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:48,582 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 34
2025-08-05 10:28:48,582 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,582 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,582 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,582 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,583 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3806.0, 路径长度: 34, 收集中间解: 3
2025-08-05 10:28:48,583 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 6, 9, 7, 8, 3, 4, 5, 2, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 3806.0, 'intermediate_solutions': [{'tour': [26, 21, 25, 19, 32, 18, 12, 14, 13, 1, 6, 3, 5, 7, 33, 27, 28, 24, 30, 20, 31, 23, 10, 9, 16, 11, 2, 4, 8, 15, 0, 29, 22, 17], 'cur_cost': 15639.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [26, 21, 25, 19, 32, 18, 12, 14, 4, 2, 11, 16, 9, 10, 23, 31, 20, 30, 24, 28, 27, 33, 7, 5, 3, 1, 6, 13, 8, 15, 0, 29, 22, 17], 'cur_cost': 16936.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [26, 21, 25, 19, 18, 12, 14, 13, 6, 1, 3, 5, 7, 33, 27, 28, 24, 30, 20, 31, 23, 10, 9, 16, 11, 2, 4, 32, 8, 15, 0, 29, 22, 17], 'cur_cost': 16036.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:48,583 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 3806.00)
2025-08-05 10:28:48,583 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:48,583 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:48,583 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:48,584 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 34
2025-08-05 10:28:48,584 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,584 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,584 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,585 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,585 - ExplorationExpert - INFO - 探索路径生成完成，成本: 22302.0, 路径长度: 34, 收集中间解: 3
2025-08-05 10:28:48,585 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [29, 1, 6, 9, 14, 0, 3, 4, 5, 23, 8, 13, 30, 26, 12, 32, 17, 21, 16, 33, 28, 27, 10, 31, 7, 19, 18, 15, 24, 25, 11, 20, 2, 22], 'cur_cost': 22302.0, 'intermediate_solutions': [{'tour': [12, 21, 5, 10, 14, 13, 15, 16, 11, 0, 9, 6, 7, 8, 3, 4, 1, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 18, 19], 'cur_cost': 6492.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 5, 10, 14, 13, 15, 16, 11, 12, 9, 6, 7, 8, 3, 4, 1, 2, 20, 17, 23, 22, 24, 27, 26, 25, 31, 32, 28, 29, 30, 33, 18, 19], 'cur_cost': 7852.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 10, 14, 13, 15, 16, 11, 21, 12, 9, 6, 7, 8, 3, 4, 1, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 18, 19], 'cur_cost': 6477.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:48,585 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 22302.00)
2025-08-05 10:28:48,585 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:48,585 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:48,585 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:48,586 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 34
2025-08-05 10:28:48,586 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,586 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,587 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,587 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,587 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21957.0, 路径长度: 34, 收集中间解: 3
2025-08-05 10:28:48,587 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [20, 5, 25, 18, 31, 13, 2, 14, 1, 10, 11, 15, 9, 29, 28, 22, 12, 21, 3, 0, 30, 4, 26, 32, 17, 19, 6, 8, 16, 27, 23, 7, 33, 24], 'cur_cost': 21957.0, 'intermediate_solutions': [{'tour': [16, 24, 9, 19, 23, 18, 15, 14, 13, 12, 25, 31, 30, 29, 28, 32, 26, 2, 6, 8, 3, 4, 5, 11, 7, 21, 1, 0, 22, 27, 20, 10, 33, 17], 'cur_cost': 15264.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [16, 24, 9, 19, 23, 18, 15, 20, 13, 12, 25, 31, 30, 29, 28, 32, 26, 2, 6, 5, 4, 3, 8, 11, 7, 21, 1, 0, 22, 27, 14, 10, 33, 17], 'cur_cost': 16468.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [16, 24, 9, 19, 23, 18, 3, 15, 20, 13, 12, 25, 31, 30, 29, 28, 32, 26, 2, 6, 8, 4, 5, 11, 7, 21, 1, 0, 22, 27, 14, 10, 33, 17], 'cur_cost': 17492.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:48,587 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 21957.00)
2025-08-05 10:28:48,587 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:28:48,587 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:48,588 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:48,588 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 23414.0
2025-08-05 10:28:48,600 - ExploitationExpert - INFO - res_population_num: 9
2025-08-05 10:28:48,600 - ExploitationExpert - INFO - res_population_costs: [3575, 3575, 3575, 3575, 3575, 3576.0, 3575, 3575, 3575]
2025-08-05 10:28:48,600 - ExploitationExpert - INFO - res_populations: [array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 13, 12, 11, 15, 16,  9,  7,  6,  2,  5,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0, 33, 30, 29, 28, 27, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 14, 13, 11, 12, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 21, 19, 18, 20, 23,
       17, 10, 14, 13, 12, 11, 15, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64)]
2025-08-05 10:28:48,603 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:48,603 - ExploitationExpert - INFO - populations: [{'tour': [29, 24, 25, 18, 31, 0, 2, 14, 4, 10, 8, 13, 7, 9, 12, 22, 17, 21, 19, 33, 28, 27, 26, 32, 1, 5, 6, 15, 16, 3, 11, 23, 20, 30], 'cur_cost': 14781.0}, {'tour': [0, 1, 6, 9, 7, 8, 3, 4, 5, 2, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 3806.0}, {'tour': [29, 1, 6, 9, 14, 0, 3, 4, 5, 23, 8, 13, 30, 26, 12, 32, 17, 21, 16, 33, 28, 27, 10, 31, 7, 19, 18, 15, 24, 25, 11, 20, 2, 22], 'cur_cost': 22302.0}, {'tour': [20, 5, 25, 18, 31, 13, 2, 14, 1, 10, 11, 15, 9, 29, 28, 22, 12, 21, 3, 0, 30, 4, 26, 32, 17, 19, 6, 8, 16, 27, 23, 7, 33, 24], 'cur_cost': 21957.0}, {'tour': array([ 6, 11, 10, 21, 16,  1, 27, 29,  8, 20, 31, 33, 30,  7, 28, 25,  9,
       14,  5, 18,  0, 24,  3, 12, 22,  2, 32,  4, 13, 19, 23, 17, 26, 15],
      dtype=int64), 'cur_cost': 23414.0}, {'tour': [24, 28, 5, 25, 32, 26, 12, 16, 1, 2, 7, 22, 33, 4, 20, 18, 30, 31, 3, 10, 9, 15, 27, 0, 17, 13, 14, 29, 19, 21, 23, 8, 11, 6], 'cur_cost': 20824.0}, {'tour': [0, 8, 11, 3, 4, 5, 6, 7, 9, 2, 1, 16, 15, 13, 14, 10, 12, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 5128.0}, {'tour': [20, 28, 32, 21, 17, 1, 15, 22, 12, 3, 9, 10, 5, 31, 4, 13, 30, 6, 33, 16, 24, 25, 8, 0, 23, 26, 11, 19, 7, 27, 29, 14, 2, 18], 'cur_cost': 24249.0}, {'tour': [3, 4, 0, 7, 2, 11, 12, 23, 24, 20, 22, 18, 32, 21, 31, 30, 26, 29, 19, 27, 1, 15, 9, 14, 6, 8, 16, 13, 10, 5, 33, 28, 25, 17], 'cur_cost': 13118.0}, {'tour': [12, 23, 22, 20, 17, 31, 18, 29, 33, 26, 30, 7, 4, 3, 1, 15, 0, 9, 16, 6, 8, 11, 10, 24, 25, 19, 21, 28, 5, 14, 2, 13, 32, 27], 'cur_cost': 15592.0}]
2025-08-05 10:28:48,603 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:48,604 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 174, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 174, 'cache_hits': 0, 'similarity_calculations': 742, 'cache_hit_rate': 0.0, 'cache_size': 742}}
2025-08-05 10:28:48,604 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([ 6, 11, 10, 21, 16,  1, 27, 29,  8, 20, 31, 33, 30,  7, 28, 25,  9,
       14,  5, 18,  0, 24,  3, 12, 22,  2, 32,  4, 13, 19, 23, 17, 26, 15],
      dtype=int64), 'cur_cost': 23414.0, 'intermediate_solutions': [{'tour': array([31,  5, 16, 17, 20, 13,  4, 28, 12, 23, 11,  3,  9, 26,  2, 27, 32,
        6, 33,  0, 30, 22, 21,  1, 10,  8, 18, 29, 24, 25, 14, 15,  7, 19]), 'cur_cost': 24253.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([17, 31,  5, 16, 20, 13,  4, 28, 12, 23, 11,  3,  9, 26,  2, 27, 32,
        6, 33,  0, 30, 22, 21,  1, 10,  8, 18, 29, 24, 25, 14, 15,  7, 19]), 'cur_cost': 24345.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([20, 17, 31,  5, 16, 13,  4, 28, 12, 23, 11,  3,  9, 26,  2, 27, 32,
        6, 33,  0, 30, 22, 21,  1, 10,  8, 18, 29, 24, 25, 14, 15,  7, 19]), 'cur_cost': 22850.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([16, 17, 31,  5, 20, 13,  4, 28, 12, 23, 11,  3,  9, 26,  2, 27, 32,
        6, 33,  0, 30, 22, 21,  1, 10,  8, 18, 29, 24, 25, 14, 15,  7, 19]), 'cur_cost': 25455.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([16, 20, 17, 31,  5, 13,  4, 28, 12, 23, 11,  3,  9, 26,  2, 27, 32,
        6, 33,  0, 30, 22, 21,  1, 10,  8, 18, 29, 24, 25, 14, 15,  7, 19]), 'cur_cost': 24393.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:48,604 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 23414.00)
2025-08-05 10:28:48,605 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:28:48,605 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:48,605 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:48,605 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 15177.0
2025-08-05 10:28:48,619 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 10:28:48,620 - ExploitationExpert - INFO - res_population_costs: [3575, 3575, 3575, 3575, 3575, 3576.0, 3575, 3575, 3575, 3575]
2025-08-05 10:28:48,620 - ExploitationExpert - INFO - res_populations: [array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 13, 12, 11, 15, 16,  9,  7,  6,  2,  5,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0, 33, 30, 29, 28, 27, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 14, 13, 11, 12, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 21, 19, 18, 20, 23,
       17, 10, 14, 13, 12, 11, 15, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64)]
2025-08-05 10:28:48,623 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:48,623 - ExploitationExpert - INFO - populations: [{'tour': [29, 24, 25, 18, 31, 0, 2, 14, 4, 10, 8, 13, 7, 9, 12, 22, 17, 21, 19, 33, 28, 27, 26, 32, 1, 5, 6, 15, 16, 3, 11, 23, 20, 30], 'cur_cost': 14781.0}, {'tour': [0, 1, 6, 9, 7, 8, 3, 4, 5, 2, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 3806.0}, {'tour': [29, 1, 6, 9, 14, 0, 3, 4, 5, 23, 8, 13, 30, 26, 12, 32, 17, 21, 16, 33, 28, 27, 10, 31, 7, 19, 18, 15, 24, 25, 11, 20, 2, 22], 'cur_cost': 22302.0}, {'tour': [20, 5, 25, 18, 31, 13, 2, 14, 1, 10, 11, 15, 9, 29, 28, 22, 12, 21, 3, 0, 30, 4, 26, 32, 17, 19, 6, 8, 16, 27, 23, 7, 33, 24], 'cur_cost': 21957.0}, {'tour': array([ 6, 11, 10, 21, 16,  1, 27, 29,  8, 20, 31, 33, 30,  7, 28, 25,  9,
       14,  5, 18,  0, 24,  3, 12, 22,  2, 32,  4, 13, 19, 23, 17, 26, 15],
      dtype=int64), 'cur_cost': 23414.0}, {'tour': array([26, 31, 20, 19, 27,  5, 29, 25, 28, 18, 17, 13,  0,  2,  4,  7,  9,
        3, 10, 32, 30, 22, 11, 16, 15, 24, 14, 12,  1,  6, 33, 23, 21,  8],
      dtype=int64), 'cur_cost': 15177.0}, {'tour': [0, 8, 11, 3, 4, 5, 6, 7, 9, 2, 1, 16, 15, 13, 14, 10, 12, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 5128.0}, {'tour': [20, 28, 32, 21, 17, 1, 15, 22, 12, 3, 9, 10, 5, 31, 4, 13, 30, 6, 33, 16, 24, 25, 8, 0, 23, 26, 11, 19, 7, 27, 29, 14, 2, 18], 'cur_cost': 24249.0}, {'tour': [3, 4, 0, 7, 2, 11, 12, 23, 24, 20, 22, 18, 32, 21, 31, 30, 26, 29, 19, 27, 1, 15, 9, 14, 6, 8, 16, 13, 10, 5, 33, 28, 25, 17], 'cur_cost': 13118.0}, {'tour': [12, 23, 22, 20, 17, 31, 18, 29, 33, 26, 30, 7, 4, 3, 1, 15, 0, 9, 16, 6, 8, 11, 10, 24, 25, 19, 21, 28, 5, 14, 2, 13, 32, 27], 'cur_cost': 15592.0}]
2025-08-05 10:28:48,624 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:48,625 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 175, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 175, 'cache_hits': 0, 'similarity_calculations': 747, 'cache_hit_rate': 0.0, 'cache_size': 747}}
2025-08-05 10:28:48,625 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([26, 31, 20, 19, 27,  5, 29, 25, 28, 18, 17, 13,  0,  2,  4,  7,  9,
        3, 10, 32, 30, 22, 11, 16, 15, 24, 14, 12,  1,  6, 33, 23, 21,  8],
      dtype=int64), 'cur_cost': 15177.0, 'intermediate_solutions': [{'tour': array([ 5, 28, 24, 25, 32, 26, 12, 16,  1,  2,  7, 22, 33,  4, 20, 18, 30,
       31,  3, 10,  9, 15, 27,  0, 17, 13, 14, 29, 19, 21, 23,  8, 11,  6]), 'cur_cost': 19481.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([25,  5, 28, 24, 32, 26, 12, 16,  1,  2,  7, 22, 33,  4, 20, 18, 30,
       31,  3, 10,  9, 15, 27,  0, 17, 13, 14, 29, 19, 21, 23,  8, 11,  6]), 'cur_cost': 21290.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([32, 25,  5, 28, 24, 26, 12, 16,  1,  2,  7, 22, 33,  4, 20, 18, 30,
       31,  3, 10,  9, 15, 27,  0, 17, 13, 14, 29, 19, 21, 23,  8, 11,  6]), 'cur_cost': 21314.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([24, 25,  5, 28, 32, 26, 12, 16,  1,  2,  7, 22, 33,  4, 20, 18, 30,
       31,  3, 10,  9, 15, 27,  0, 17, 13, 14, 29, 19, 21, 23,  8, 11,  6]), 'cur_cost': 20747.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([24, 32, 25,  5, 28, 26, 12, 16,  1,  2,  7, 22, 33,  4, 20, 18, 30,
       31,  3, 10,  9, 15, 27,  0, 17, 13, 14, 29, 19, 21, 23,  8, 11,  6]), 'cur_cost': 20817.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:48,625 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 15177.00)
2025-08-05 10:28:48,625 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:48,626 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:48,626 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:48,627 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 34
2025-08-05 10:28:48,627 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,627 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,628 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,628 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,628 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20344.0, 路径长度: 34, 收集中间解: 3
2025-08-05 10:28:48,628 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [5, 14, 13, 1, 23, 11, 7, 9, 31, 12, 30, 16, 0, 8, 26, 32, 17, 18, 21, 28, 22, 20, 27, 33, 24, 15, 25, 4, 10, 6, 3, 2, 19, 29], 'cur_cost': 20344.0, 'intermediate_solutions': [{'tour': [0, 8, 11, 3, 4, 5, 6, 7, 9, 2, 1, 16, 15, 13, 14, 10, 12, 25, 23, 22, 24, 21, 20, 18, 19, 17, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 6186.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 11, 3, 4, 5, 6, 7, 9, 2, 1, 16, 15, 13, 14, 10, 12, 17, 23, 31, 25, 19, 18, 20, 21, 24, 22, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 6391.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 11, 3, 4, 5, 28, 6, 7, 9, 2, 1, 16, 15, 13, 14, 10, 12, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 32, 26, 27, 33], 'cur_cost': 6944.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:48,628 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 20344.00)
2025-08-05 10:28:48,628 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:28:48,628 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:48,629 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:48,629 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 21579.0
2025-08-05 10:28:48,639 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 10:28:48,640 - ExploitationExpert - INFO - res_population_costs: [3575, 3575, 3575, 3575, 3575, 3576.0, 3575, 3575, 3575, 3575]
2025-08-05 10:28:48,640 - ExploitationExpert - INFO - res_populations: [array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 13, 12, 11, 15, 16,  9,  7,  6,  2,  5,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0, 33, 30, 29, 28, 27, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 14, 13, 11, 12, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 21, 19, 18, 20, 23,
       17, 10, 14, 13, 12, 11, 15, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64)]
2025-08-05 10:28:48,643 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:48,643 - ExploitationExpert - INFO - populations: [{'tour': [29, 24, 25, 18, 31, 0, 2, 14, 4, 10, 8, 13, 7, 9, 12, 22, 17, 21, 19, 33, 28, 27, 26, 32, 1, 5, 6, 15, 16, 3, 11, 23, 20, 30], 'cur_cost': 14781.0}, {'tour': [0, 1, 6, 9, 7, 8, 3, 4, 5, 2, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 3806.0}, {'tour': [29, 1, 6, 9, 14, 0, 3, 4, 5, 23, 8, 13, 30, 26, 12, 32, 17, 21, 16, 33, 28, 27, 10, 31, 7, 19, 18, 15, 24, 25, 11, 20, 2, 22], 'cur_cost': 22302.0}, {'tour': [20, 5, 25, 18, 31, 13, 2, 14, 1, 10, 11, 15, 9, 29, 28, 22, 12, 21, 3, 0, 30, 4, 26, 32, 17, 19, 6, 8, 16, 27, 23, 7, 33, 24], 'cur_cost': 21957.0}, {'tour': array([ 6, 11, 10, 21, 16,  1, 27, 29,  8, 20, 31, 33, 30,  7, 28, 25,  9,
       14,  5, 18,  0, 24,  3, 12, 22,  2, 32,  4, 13, 19, 23, 17, 26, 15],
      dtype=int64), 'cur_cost': 23414.0}, {'tour': array([26, 31, 20, 19, 27,  5, 29, 25, 28, 18, 17, 13,  0,  2,  4,  7,  9,
        3, 10, 32, 30, 22, 11, 16, 15, 24, 14, 12,  1,  6, 33, 23, 21,  8],
      dtype=int64), 'cur_cost': 15177.0}, {'tour': [5, 14, 13, 1, 23, 11, 7, 9, 31, 12, 30, 16, 0, 8, 26, 32, 17, 18, 21, 28, 22, 20, 27, 33, 24, 15, 25, 4, 10, 6, 3, 2, 19, 29], 'cur_cost': 20344.0}, {'tour': array([ 7, 23, 11, 17,  2, 16, 30, 21, 28, 32, 26, 19, 10,  0,  5, 24, 14,
        1, 12, 15,  9, 27, 33, 25, 20, 22, 31,  3, 18,  6, 29,  4,  8, 13],
      dtype=int64), 'cur_cost': 21579.0}, {'tour': [3, 4, 0, 7, 2, 11, 12, 23, 24, 20, 22, 18, 32, 21, 31, 30, 26, 29, 19, 27, 1, 15, 9, 14, 6, 8, 16, 13, 10, 5, 33, 28, 25, 17], 'cur_cost': 13118.0}, {'tour': [12, 23, 22, 20, 17, 31, 18, 29, 33, 26, 30, 7, 4, 3, 1, 15, 0, 9, 16, 6, 8, 11, 10, 24, 25, 19, 21, 28, 5, 14, 2, 13, 32, 27], 'cur_cost': 15592.0}]
2025-08-05 10:28:48,644 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:48,644 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 176, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 176, 'cache_hits': 0, 'similarity_calculations': 753, 'cache_hit_rate': 0.0, 'cache_size': 753}}
2025-08-05 10:28:48,645 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([ 7, 23, 11, 17,  2, 16, 30, 21, 28, 32, 26, 19, 10,  0,  5, 24, 14,
        1, 12, 15,  9, 27, 33, 25, 20, 22, 31,  3, 18,  6, 29,  4,  8, 13],
      dtype=int64), 'cur_cost': 21579.0, 'intermediate_solutions': [{'tour': array([32, 28, 20, 21, 17,  1, 15, 22, 12,  3,  9, 10,  5, 31,  4, 13, 30,
        6, 33, 16, 24, 25,  8,  0, 23, 26, 11, 19,  7, 27, 29, 14,  2, 18]), 'cur_cost': 24288.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([21, 32, 28, 20, 17,  1, 15, 22, 12,  3,  9, 10,  5, 31,  4, 13, 30,
        6, 33, 16, 24, 25,  8,  0, 23, 26, 11, 19,  7, 27, 29, 14,  2, 18]), 'cur_cost': 24256.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([17, 21, 32, 28, 20,  1, 15, 22, 12,  3,  9, 10,  5, 31,  4, 13, 30,
        6, 33, 16, 24, 25,  8,  0, 23, 26, 11, 19,  7, 27, 29, 14,  2, 18]), 'cur_cost': 24330.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([20, 21, 32, 28, 17,  1, 15, 22, 12,  3,  9, 10,  5, 31,  4, 13, 30,
        6, 33, 16, 24, 25,  8,  0, 23, 26, 11, 19,  7, 27, 29, 14,  2, 18]), 'cur_cost': 24200.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([20, 17, 21, 32, 28,  1, 15, 22, 12,  3,  9, 10,  5, 31,  4, 13, 30,
        6, 33, 16, 24, 25,  8,  0, 23, 26, 11, 19,  7, 27, 29, 14,  2, 18]), 'cur_cost': 23462.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:48,645 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 21579.00)
2025-08-05 10:28:48,645 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:48,645 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:48,646 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:48,646 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 34
2025-08-05 10:28:48,647 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,647 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,647 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,647 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,647 - ExplorationExpert - INFO - 探索路径生成完成，成本: 24565.0, 路径长度: 34, 收集中间解: 3
2025-08-05 10:28:48,647 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [20, 24, 13, 7, 8, 1, 23, 11, 9, 26, 12, 17, 3, 0, 22, 21, 27, 18, 5, 30, 16, 28, 2, 10, 31, 6, 15, 32, 29, 33, 14, 19, 25, 4], 'cur_cost': 24565.0, 'intermediate_solutions': [{'tour': [3, 4, 0, 7, 20, 11, 12, 23, 24, 2, 22, 18, 32, 21, 31, 30, 26, 29, 19, 27, 1, 15, 9, 14, 6, 8, 16, 13, 10, 5, 33, 28, 25, 17], 'cur_cost': 16264.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 4, 0, 7, 2, 11, 12, 6, 14, 9, 15, 1, 27, 19, 29, 26, 30, 31, 21, 32, 18, 22, 20, 24, 23, 8, 16, 13, 10, 5, 33, 28, 25, 17], 'cur_cost': 14070.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 4, 0, 7, 2, 11, 12, 23, 27, 24, 20, 22, 18, 32, 21, 31, 30, 26, 29, 19, 1, 15, 9, 14, 6, 8, 16, 13, 10, 5, 33, 28, 25, 17], 'cur_cost': 13957.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:48,647 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 24565.00)
2025-08-05 10:28:48,648 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:48,648 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:48,648 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:48,649 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 34
2025-08-05 10:28:48,649 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,649 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,649 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,649 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,649 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19195.0, 路径长度: 34, 收集中间解: 3
2025-08-05 10:28:48,649 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [7, 8, 9, 4, 23, 13, 14, 26, 12, 17, 3, 22, 24, 21, 20, 18, 1, 5, 30, 16, 28, 2, 0, 10, 15, 6, 29, 19, 25, 11, 33, 31, 32, 27], 'cur_cost': 19195.0, 'intermediate_solutions': [{'tour': [12, 23, 22, 20, 17, 31, 18, 29, 33, 26, 15, 7, 4, 3, 1, 30, 0, 9, 16, 6, 8, 11, 10, 24, 25, 19, 21, 28, 5, 14, 2, 13, 32, 27], 'cur_cost': 16777.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [12, 23, 22, 20, 17, 31, 18, 29, 33, 19, 25, 24, 10, 11, 8, 6, 16, 9, 0, 15, 1, 3, 4, 7, 30, 26, 21, 28, 5, 14, 2, 13, 32, 27], 'cur_cost': 16816.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [12, 23, 22, 20, 17, 31, 18, 29, 33, 30, 7, 4, 3, 1, 15, 0, 9, 16, 6, 8, 11, 10, 24, 25, 19, 26, 21, 28, 5, 14, 2, 13, 32, 27], 'cur_cost': 16801.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:48,650 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 19195.00)
2025-08-05 10:28:48,650 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:48,650 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:48,652 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [29, 24, 25, 18, 31, 0, 2, 14, 4, 10, 8, 13, 7, 9, 12, 22, 17, 21, 19, 33, 28, 27, 26, 32, 1, 5, 6, 15, 16, 3, 11, 23, 20, 30], 'cur_cost': 14781.0, 'intermediate_solutions': [{'tour': [0, 24, 9, 17, 23, 22, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 7, 2, 1, 16, 15, 12, 14, 10, 13, 11], 'cur_cost': 6710.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 24, 9, 17, 23, 22, 21, 29, 30, 31, 25, 19, 18, 20, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 7, 2, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 8011.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 24, 9, 17, 23, 22, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 7, 26, 27, 33, 8, 3, 4, 5, 6, 2, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 8433.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 6, 9, 7, 8, 3, 4, 5, 2, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 3806.0, 'intermediate_solutions': [{'tour': [26, 21, 25, 19, 32, 18, 12, 14, 13, 1, 6, 3, 5, 7, 33, 27, 28, 24, 30, 20, 31, 23, 10, 9, 16, 11, 2, 4, 8, 15, 0, 29, 22, 17], 'cur_cost': 15639.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [26, 21, 25, 19, 32, 18, 12, 14, 4, 2, 11, 16, 9, 10, 23, 31, 20, 30, 24, 28, 27, 33, 7, 5, 3, 1, 6, 13, 8, 15, 0, 29, 22, 17], 'cur_cost': 16936.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [26, 21, 25, 19, 18, 12, 14, 13, 6, 1, 3, 5, 7, 33, 27, 28, 24, 30, 20, 31, 23, 10, 9, 16, 11, 2, 4, 32, 8, 15, 0, 29, 22, 17], 'cur_cost': 16036.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [29, 1, 6, 9, 14, 0, 3, 4, 5, 23, 8, 13, 30, 26, 12, 32, 17, 21, 16, 33, 28, 27, 10, 31, 7, 19, 18, 15, 24, 25, 11, 20, 2, 22], 'cur_cost': 22302.0, 'intermediate_solutions': [{'tour': [12, 21, 5, 10, 14, 13, 15, 16, 11, 0, 9, 6, 7, 8, 3, 4, 1, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 18, 19], 'cur_cost': 6492.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 5, 10, 14, 13, 15, 16, 11, 12, 9, 6, 7, 8, 3, 4, 1, 2, 20, 17, 23, 22, 24, 27, 26, 25, 31, 32, 28, 29, 30, 33, 18, 19], 'cur_cost': 7852.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 10, 14, 13, 15, 16, 11, 21, 12, 9, 6, 7, 8, 3, 4, 1, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 18, 19], 'cur_cost': 6477.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [20, 5, 25, 18, 31, 13, 2, 14, 1, 10, 11, 15, 9, 29, 28, 22, 12, 21, 3, 0, 30, 4, 26, 32, 17, 19, 6, 8, 16, 27, 23, 7, 33, 24], 'cur_cost': 21957.0, 'intermediate_solutions': [{'tour': [16, 24, 9, 19, 23, 18, 15, 14, 13, 12, 25, 31, 30, 29, 28, 32, 26, 2, 6, 8, 3, 4, 5, 11, 7, 21, 1, 0, 22, 27, 20, 10, 33, 17], 'cur_cost': 15264.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [16, 24, 9, 19, 23, 18, 15, 20, 13, 12, 25, 31, 30, 29, 28, 32, 26, 2, 6, 5, 4, 3, 8, 11, 7, 21, 1, 0, 22, 27, 14, 10, 33, 17], 'cur_cost': 16468.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [16, 24, 9, 19, 23, 18, 3, 15, 20, 13, 12, 25, 31, 30, 29, 28, 32, 26, 2, 6, 8, 4, 5, 11, 7, 21, 1, 0, 22, 27, 14, 10, 33, 17], 'cur_cost': 17492.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 6, 11, 10, 21, 16,  1, 27, 29,  8, 20, 31, 33, 30,  7, 28, 25,  9,
       14,  5, 18,  0, 24,  3, 12, 22,  2, 32,  4, 13, 19, 23, 17, 26, 15],
      dtype=int64), 'cur_cost': 23414.0, 'intermediate_solutions': [{'tour': array([31,  5, 16, 17, 20, 13,  4, 28, 12, 23, 11,  3,  9, 26,  2, 27, 32,
        6, 33,  0, 30, 22, 21,  1, 10,  8, 18, 29, 24, 25, 14, 15,  7, 19]), 'cur_cost': 24253.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([17, 31,  5, 16, 20, 13,  4, 28, 12, 23, 11,  3,  9, 26,  2, 27, 32,
        6, 33,  0, 30, 22, 21,  1, 10,  8, 18, 29, 24, 25, 14, 15,  7, 19]), 'cur_cost': 24345.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([20, 17, 31,  5, 16, 13,  4, 28, 12, 23, 11,  3,  9, 26,  2, 27, 32,
        6, 33,  0, 30, 22, 21,  1, 10,  8, 18, 29, 24, 25, 14, 15,  7, 19]), 'cur_cost': 22850.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([16, 17, 31,  5, 20, 13,  4, 28, 12, 23, 11,  3,  9, 26,  2, 27, 32,
        6, 33,  0, 30, 22, 21,  1, 10,  8, 18, 29, 24, 25, 14, 15,  7, 19]), 'cur_cost': 25455.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([16, 20, 17, 31,  5, 13,  4, 28, 12, 23, 11,  3,  9, 26,  2, 27, 32,
        6, 33,  0, 30, 22, 21,  1, 10,  8, 18, 29, 24, 25, 14, 15,  7, 19]), 'cur_cost': 24393.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([26, 31, 20, 19, 27,  5, 29, 25, 28, 18, 17, 13,  0,  2,  4,  7,  9,
        3, 10, 32, 30, 22, 11, 16, 15, 24, 14, 12,  1,  6, 33, 23, 21,  8],
      dtype=int64), 'cur_cost': 15177.0, 'intermediate_solutions': [{'tour': array([ 5, 28, 24, 25, 32, 26, 12, 16,  1,  2,  7, 22, 33,  4, 20, 18, 30,
       31,  3, 10,  9, 15, 27,  0, 17, 13, 14, 29, 19, 21, 23,  8, 11,  6]), 'cur_cost': 19481.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([25,  5, 28, 24, 32, 26, 12, 16,  1,  2,  7, 22, 33,  4, 20, 18, 30,
       31,  3, 10,  9, 15, 27,  0, 17, 13, 14, 29, 19, 21, 23,  8, 11,  6]), 'cur_cost': 21290.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([32, 25,  5, 28, 24, 26, 12, 16,  1,  2,  7, 22, 33,  4, 20, 18, 30,
       31,  3, 10,  9, 15, 27,  0, 17, 13, 14, 29, 19, 21, 23,  8, 11,  6]), 'cur_cost': 21314.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([24, 25,  5, 28, 32, 26, 12, 16,  1,  2,  7, 22, 33,  4, 20, 18, 30,
       31,  3, 10,  9, 15, 27,  0, 17, 13, 14, 29, 19, 21, 23,  8, 11,  6]), 'cur_cost': 20747.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([24, 32, 25,  5, 28, 26, 12, 16,  1,  2,  7, 22, 33,  4, 20, 18, 30,
       31,  3, 10,  9, 15, 27,  0, 17, 13, 14, 29, 19, 21, 23,  8, 11,  6]), 'cur_cost': 20817.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [5, 14, 13, 1, 23, 11, 7, 9, 31, 12, 30, 16, 0, 8, 26, 32, 17, 18, 21, 28, 22, 20, 27, 33, 24, 15, 25, 4, 10, 6, 3, 2, 19, 29], 'cur_cost': 20344.0, 'intermediate_solutions': [{'tour': [0, 8, 11, 3, 4, 5, 6, 7, 9, 2, 1, 16, 15, 13, 14, 10, 12, 25, 23, 22, 24, 21, 20, 18, 19, 17, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 6186.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 11, 3, 4, 5, 6, 7, 9, 2, 1, 16, 15, 13, 14, 10, 12, 17, 23, 31, 25, 19, 18, 20, 21, 24, 22, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 6391.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 11, 3, 4, 5, 28, 6, 7, 9, 2, 1, 16, 15, 13, 14, 10, 12, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 32, 26, 27, 33], 'cur_cost': 6944.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 7, 23, 11, 17,  2, 16, 30, 21, 28, 32, 26, 19, 10,  0,  5, 24, 14,
        1, 12, 15,  9, 27, 33, 25, 20, 22, 31,  3, 18,  6, 29,  4,  8, 13],
      dtype=int64), 'cur_cost': 21579.0, 'intermediate_solutions': [{'tour': array([32, 28, 20, 21, 17,  1, 15, 22, 12,  3,  9, 10,  5, 31,  4, 13, 30,
        6, 33, 16, 24, 25,  8,  0, 23, 26, 11, 19,  7, 27, 29, 14,  2, 18]), 'cur_cost': 24288.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([21, 32, 28, 20, 17,  1, 15, 22, 12,  3,  9, 10,  5, 31,  4, 13, 30,
        6, 33, 16, 24, 25,  8,  0, 23, 26, 11, 19,  7, 27, 29, 14,  2, 18]), 'cur_cost': 24256.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([17, 21, 32, 28, 20,  1, 15, 22, 12,  3,  9, 10,  5, 31,  4, 13, 30,
        6, 33, 16, 24, 25,  8,  0, 23, 26, 11, 19,  7, 27, 29, 14,  2, 18]), 'cur_cost': 24330.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([20, 21, 32, 28, 17,  1, 15, 22, 12,  3,  9, 10,  5, 31,  4, 13, 30,
        6, 33, 16, 24, 25,  8,  0, 23, 26, 11, 19,  7, 27, 29, 14,  2, 18]), 'cur_cost': 24200.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([20, 17, 21, 32, 28,  1, 15, 22, 12,  3,  9, 10,  5, 31,  4, 13, 30,
        6, 33, 16, 24, 25,  8,  0, 23, 26, 11, 19,  7, 27, 29, 14,  2, 18]), 'cur_cost': 23462.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [20, 24, 13, 7, 8, 1, 23, 11, 9, 26, 12, 17, 3, 0, 22, 21, 27, 18, 5, 30, 16, 28, 2, 10, 31, 6, 15, 32, 29, 33, 14, 19, 25, 4], 'cur_cost': 24565.0, 'intermediate_solutions': [{'tour': [3, 4, 0, 7, 20, 11, 12, 23, 24, 2, 22, 18, 32, 21, 31, 30, 26, 29, 19, 27, 1, 15, 9, 14, 6, 8, 16, 13, 10, 5, 33, 28, 25, 17], 'cur_cost': 16264.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 4, 0, 7, 2, 11, 12, 6, 14, 9, 15, 1, 27, 19, 29, 26, 30, 31, 21, 32, 18, 22, 20, 24, 23, 8, 16, 13, 10, 5, 33, 28, 25, 17], 'cur_cost': 14070.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 4, 0, 7, 2, 11, 12, 23, 27, 24, 20, 22, 18, 32, 21, 31, 30, 26, 29, 19, 1, 15, 9, 14, 6, 8, 16, 13, 10, 5, 33, 28, 25, 17], 'cur_cost': 13957.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [7, 8, 9, 4, 23, 13, 14, 26, 12, 17, 3, 22, 24, 21, 20, 18, 1, 5, 30, 16, 28, 2, 0, 10, 15, 6, 29, 19, 25, 11, 33, 31, 32, 27], 'cur_cost': 19195.0, 'intermediate_solutions': [{'tour': [12, 23, 22, 20, 17, 31, 18, 29, 33, 26, 15, 7, 4, 3, 1, 30, 0, 9, 16, 6, 8, 11, 10, 24, 25, 19, 21, 28, 5, 14, 2, 13, 32, 27], 'cur_cost': 16777.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [12, 23, 22, 20, 17, 31, 18, 29, 33, 19, 25, 24, 10, 11, 8, 6, 16, 9, 0, 15, 1, 3, 4, 7, 30, 26, 21, 28, 5, 14, 2, 13, 32, 27], 'cur_cost': 16816.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [12, 23, 22, 20, 17, 31, 18, 29, 33, 30, 7, 4, 3, 1, 15, 0, 9, 16, 6, 8, 11, 10, 24, 25, 19, 26, 21, 28, 5, 14, 2, 13, 32, 27], 'cur_cost': 16801.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:48,652 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:48,652 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:48,654 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=3806.000, 多样性=0.954
2025-08-05 10:28:48,654 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 10:28:48,654 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 10:28:48,654 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:48,655 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.0024406706036040355, 'best_improvement': 0.2578003120124805}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.0034364261168383587}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.05183333897101012, 'recent_improvements': [-0.031323585187281705, 0.06539881945741151, -0.13499026312930193], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 10, 'new_count': 10, 'count_change': 0, 'old_best_cost': 3575, 'new_best_cost': 3575, 'quality_improvement': 0.0, 'old_diversity': 0.5908496732026144, 'new_diversity': 0.5908496732026144, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:48,656 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 10:28:48,656 - __main__ - INFO - composite2_34 开始进化第 4 代
2025-08-05 10:28:48,656 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 10:28:48,656 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:48,657 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=3806.000, 多样性=0.954
2025-08-05 10:28:48,657 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:48,658 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.954
2025-08-05 10:28:48,658 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:48,661 - EliteExpert - INFO - 精英解分析完成: 精英解数量=10, 多样性=0.591
2025-08-05 10:28:48,663 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 10:28:48,663 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:48,664 - LandscapeExpert - INFO - 添加精英解数据: 10个精英解
2025-08-05 10:28:48,664 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-05 10:28:48,714 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.450, 适应度梯度: -2932.410, 聚类评分: 0.000, 覆盖率: 0.083, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:48,714 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 10:28:48,714 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:48,714 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite2_34
2025-08-05 10:28:48,722 - visualization.landscape_visualizer - INFO - 插值约束: 1 个点被约束到最小值 3575.00
2025-08-05 10:28:48,725 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=8.9%, 梯度: 691.91 → 630.48
2025-08-05 10:28:48,862 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite2_34\landscape_composite2_34_iter_69_20250805_102848.html
2025-08-05 10:28:48,904 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite2_34\dashboard_composite2_34_iter_69_20250805_102848.html
2025-08-05 10:28:48,904 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 69
2025-08-05 10:28:48,904 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 10:28:48,904 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2411秒
2025-08-05 10:28:48,905 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.45, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -2932.4100000000008, 'local_optima_density': 0.45, 'gradient_variance': 31055055.509899985, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0832, 'fitness_entropy': 0.8103163632291326, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -2932.410)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.083)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360928.7147188, 'performance_metrics': {}}}
2025-08-05 10:28:48,905 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:48,905 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:48,905 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:48,905 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:48,906 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 10:28:48,906 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 10:28:48,906 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 10:28:48,906 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:48,906 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:48,907 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 10:28:48,907 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:48,907 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:48,907 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:48,907 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:48,907 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:48,908 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:48,908 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 34
2025-08-05 10:28:48,909 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,909 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,909 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,909 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,909 - ExplorationExpert - INFO - 探索路径生成完成，成本: 5910.0, 路径长度: 34, 收集中间解: 3
2025-08-05 10:28:48,909 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 17, 11, 14, 13, 15, 16, 10, 12, 9, 6, 5, 4, 3, 8, 7, 2, 1, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 20, 21, 19, 18], 'cur_cost': 5910.0, 'intermediate_solutions': [{'tour': [29, 24, 25, 18, 31, 33, 2, 14, 4, 10, 8, 13, 7, 9, 12, 22, 17, 21, 19, 0, 28, 27, 26, 32, 1, 5, 6, 15, 16, 3, 11, 23, 20, 30], 'cur_cost': 15988.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [29, 24, 25, 18, 31, 0, 2, 14, 4, 10, 8, 13, 7, 9, 12, 22, 17, 21, 19, 33, 28, 6, 5, 1, 32, 26, 27, 15, 16, 3, 11, 23, 20, 30], 'cur_cost': 16110.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [29, 24, 25, 18, 31, 0, 2, 14, 4, 10, 8, 30, 13, 7, 9, 12, 22, 17, 21, 19, 33, 28, 27, 26, 32, 1, 5, 6, 15, 16, 3, 11, 23, 20], 'cur_cost': 16042.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:48,910 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 5910.00)
2025-08-05 10:28:48,910 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:48,910 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:48,910 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:48,911 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 34
2025-08-05 10:28:48,911 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,911 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,911 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,912 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,912 - ExplorationExpert - INFO - 探索路径生成完成，成本: 26729.0, 路径长度: 34, 收集中间解: 3
2025-08-05 10:28:48,912 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [6, 17, 25, 18, 7, 15, 2, 14, 8, 9, 11, 33, 13, 3, 12, 22, 27, 5, 23, 0, 29, 21, 32, 16, 19, 26, 31, 24, 1, 28, 20, 4, 10, 30], 'cur_cost': 26729.0, 'intermediate_solutions': [{'tour': [0, 1, 6, 9, 7, 8, 3, 2, 5, 4, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 3855.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 6, 7, 9, 8, 3, 4, 5, 2, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 3825.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 6, 9, 7, 8, 3, 4, 5, 2, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 21, 20, 18, 33, 19, 25, 31, 30, 29, 28, 32, 26, 27], 'cur_cost': 5094.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:48,912 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 26729.00)
2025-08-05 10:28:48,912 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-05 10:28:48,912 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:48,912 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:48,913 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 25574.0
2025-08-05 10:28:48,924 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 10:28:48,924 - ExploitationExpert - INFO - res_population_costs: [3575, 3575, 3575, 3575, 3575, 3575, 3575, 3575, 3575, 3576.0]
2025-08-05 10:28:48,924 - ExploitationExpert - INFO - res_populations: [array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 13, 12, 11, 15, 16,  9,  7,  6,  2,  5,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0, 33, 30, 29, 28, 27, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 21, 19, 18, 20, 23,
       17, 10, 14, 13, 12, 11, 15, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 14, 13, 11, 12, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64)]
2025-08-05 10:28:48,927 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:48,928 - ExploitationExpert - INFO - populations: [{'tour': [0, 17, 11, 14, 13, 15, 16, 10, 12, 9, 6, 5, 4, 3, 8, 7, 2, 1, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 20, 21, 19, 18], 'cur_cost': 5910.0}, {'tour': [6, 17, 25, 18, 7, 15, 2, 14, 8, 9, 11, 33, 13, 3, 12, 22, 27, 5, 23, 0, 29, 21, 32, 16, 19, 26, 31, 24, 1, 28, 20, 4, 10, 30], 'cur_cost': 26729.0}, {'tour': array([18, 12,  9, 23,  4, 22, 21,  7, 30, 32, 20,  0, 14, 17,  1, 13,  5,
       19, 29, 15,  3, 10, 11, 25,  6, 33, 26,  2, 24, 31,  8, 16, 27, 28],
      dtype=int64), 'cur_cost': 25574.0}, {'tour': [20, 5, 25, 18, 31, 13, 2, 14, 1, 10, 11, 15, 9, 29, 28, 22, 12, 21, 3, 0, 30, 4, 26, 32, 17, 19, 6, 8, 16, 27, 23, 7, 33, 24], 'cur_cost': 21957.0}, {'tour': [6, 11, 10, 21, 16, 1, 27, 29, 8, 20, 31, 33, 30, 7, 28, 25, 9, 14, 5, 18, 0, 24, 3, 12, 22, 2, 32, 4, 13, 19, 23, 17, 26, 15], 'cur_cost': 23414.0}, {'tour': [26, 31, 20, 19, 27, 5, 29, 25, 28, 18, 17, 13, 0, 2, 4, 7, 9, 3, 10, 32, 30, 22, 11, 16, 15, 24, 14, 12, 1, 6, 33, 23, 21, 8], 'cur_cost': 15177.0}, {'tour': [5, 14, 13, 1, 23, 11, 7, 9, 31, 12, 30, 16, 0, 8, 26, 32, 17, 18, 21, 28, 22, 20, 27, 33, 24, 15, 25, 4, 10, 6, 3, 2, 19, 29], 'cur_cost': 20344.0}, {'tour': [7, 23, 11, 17, 2, 16, 30, 21, 28, 32, 26, 19, 10, 0, 5, 24, 14, 1, 12, 15, 9, 27, 33, 25, 20, 22, 31, 3, 18, 6, 29, 4, 8, 13], 'cur_cost': 21579.0}, {'tour': [20, 24, 13, 7, 8, 1, 23, 11, 9, 26, 12, 17, 3, 0, 22, 21, 27, 18, 5, 30, 16, 28, 2, 10, 31, 6, 15, 32, 29, 33, 14, 19, 25, 4], 'cur_cost': 24565.0}, {'tour': [7, 8, 9, 4, 23, 13, 14, 26, 12, 17, 3, 22, 24, 21, 20, 18, 1, 5, 30, 16, 28, 2, 0, 10, 15, 6, 29, 19, 25, 11, 33, 31, 32, 27], 'cur_cost': 19195.0}]
2025-08-05 10:28:48,928 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:48,928 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 177, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 177, 'cache_hits': 0, 'similarity_calculations': 760, 'cache_hit_rate': 0.0, 'cache_size': 760}}
2025-08-05 10:28:48,929 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([18, 12,  9, 23,  4, 22, 21,  7, 30, 32, 20,  0, 14, 17,  1, 13,  5,
       19, 29, 15,  3, 10, 11, 25,  6, 33, 26,  2, 24, 31,  8, 16, 27, 28],
      dtype=int64), 'cur_cost': 25574.0, 'intermediate_solutions': [{'tour': array([ 6,  1, 29,  9, 14,  0,  3,  4,  5, 23,  8, 13, 30, 26, 12, 32, 17,
       21, 16, 33, 28, 27, 10, 31,  7, 19, 18, 15, 24, 25, 11, 20,  2, 22]), 'cur_cost': 23551.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9,  6,  1, 29, 14,  0,  3,  4,  5, 23,  8, 13, 30, 26, 12, 32, 17,
       21, 16, 33, 28, 27, 10, 31,  7, 19, 18, 15, 24, 25, 11, 20,  2, 22]), 'cur_cost': 23098.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14,  9,  6,  1, 29,  0,  3,  4,  5, 23,  8, 13, 30, 26, 12, 32, 17,
       21, 16, 33, 28, 27, 10, 31,  7, 19, 18, 15, 24, 25, 11, 20,  2, 22]), 'cur_cost': 22565.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([29,  9,  6,  1, 14,  0,  3,  4,  5, 23,  8, 13, 30, 26, 12, 32, 17,
       21, 16, 33, 28, 27, 10, 31,  7, 19, 18, 15, 24, 25, 11, 20,  2, 22]), 'cur_cost': 22386.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([29, 14,  9,  6,  1,  0,  3,  4,  5, 23,  8, 13, 30, 26, 12, 32, 17,
       21, 16, 33, 28, 27, 10, 31,  7, 19, 18, 15, 24, 25, 11, 20,  2, 22]), 'cur_cost': 21864.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:48,929 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 25574.00)
2025-08-05 10:28:48,929 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:48,930 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:48,930 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:48,932 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 34
2025-08-05 10:28:48,933 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,933 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,933 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,933 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,933 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11798.0, 路径长度: 34, 收集中间解: 3
2025-08-05 10:28:48,934 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [17, 18, 28, 26, 31, 30, 20, 14, 10, 6, 2, 4, 1, 9, 3, 12, 5, 8, 0, 33, 22, 19, 23, 24, 21, 29, 7, 11, 15, 16, 25, 32, 27, 13], 'cur_cost': 11798.0, 'intermediate_solutions': [{'tour': [20, 16, 25, 18, 31, 13, 2, 14, 1, 10, 11, 15, 9, 29, 28, 22, 12, 21, 3, 0, 30, 4, 26, 32, 17, 19, 6, 8, 5, 27, 23, 7, 33, 24], 'cur_cost': 20983.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [17, 32, 26, 4, 30, 0, 3, 21, 12, 22, 28, 29, 9, 15, 11, 10, 1, 14, 2, 13, 31, 18, 25, 5, 20, 19, 6, 8, 16, 27, 23, 7, 33, 24], 'cur_cost': 21915.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [20, 5, 25, 18, 31, 13, 2, 14, 1, 10, 11, 9, 29, 28, 22, 12, 21, 3, 0, 30, 4, 26, 32, 17, 19, 6, 15, 8, 16, 27, 23, 7, 33, 24], 'cur_cost': 23247.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:48,934 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 11798.00)
2025-08-05 10:28:48,934 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:28:48,934 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:48,934 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:48,934 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 21923.0
2025-08-05 10:28:48,949 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 10:28:48,949 - ExploitationExpert - INFO - res_population_costs: [3575, 3575, 3575, 3575, 3575, 3575, 3575, 3575, 3575, 3576.0]
2025-08-05 10:28:48,949 - ExploitationExpert - INFO - res_populations: [array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 13, 12, 11, 15, 16,  9,  7,  6,  2,  5,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0, 33, 30, 29, 28, 27, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 21, 19, 18, 20, 23,
       17, 10, 14, 13, 12, 11, 15, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 14, 13, 11, 12, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64)]
2025-08-05 10:28:48,952 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:48,952 - ExploitationExpert - INFO - populations: [{'tour': [0, 17, 11, 14, 13, 15, 16, 10, 12, 9, 6, 5, 4, 3, 8, 7, 2, 1, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 20, 21, 19, 18], 'cur_cost': 5910.0}, {'tour': [6, 17, 25, 18, 7, 15, 2, 14, 8, 9, 11, 33, 13, 3, 12, 22, 27, 5, 23, 0, 29, 21, 32, 16, 19, 26, 31, 24, 1, 28, 20, 4, 10, 30], 'cur_cost': 26729.0}, {'tour': array([18, 12,  9, 23,  4, 22, 21,  7, 30, 32, 20,  0, 14, 17,  1, 13,  5,
       19, 29, 15,  3, 10, 11, 25,  6, 33, 26,  2, 24, 31,  8, 16, 27, 28],
      dtype=int64), 'cur_cost': 25574.0}, {'tour': [17, 18, 28, 26, 31, 30, 20, 14, 10, 6, 2, 4, 1, 9, 3, 12, 5, 8, 0, 33, 22, 19, 23, 24, 21, 29, 7, 11, 15, 16, 25, 32, 27, 13], 'cur_cost': 11798.0}, {'tour': array([33,  3, 10, 17,  8, 19, 12, 32,  7, 16,  4,  1, 15, 26, 11,  2, 22,
       30, 23, 18, 14, 29, 24, 13, 25, 20, 21,  6,  0,  5,  9, 31, 27, 28],
      dtype=int64), 'cur_cost': 21923.0}, {'tour': [26, 31, 20, 19, 27, 5, 29, 25, 28, 18, 17, 13, 0, 2, 4, 7, 9, 3, 10, 32, 30, 22, 11, 16, 15, 24, 14, 12, 1, 6, 33, 23, 21, 8], 'cur_cost': 15177.0}, {'tour': [5, 14, 13, 1, 23, 11, 7, 9, 31, 12, 30, 16, 0, 8, 26, 32, 17, 18, 21, 28, 22, 20, 27, 33, 24, 15, 25, 4, 10, 6, 3, 2, 19, 29], 'cur_cost': 20344.0}, {'tour': [7, 23, 11, 17, 2, 16, 30, 21, 28, 32, 26, 19, 10, 0, 5, 24, 14, 1, 12, 15, 9, 27, 33, 25, 20, 22, 31, 3, 18, 6, 29, 4, 8, 13], 'cur_cost': 21579.0}, {'tour': [20, 24, 13, 7, 8, 1, 23, 11, 9, 26, 12, 17, 3, 0, 22, 21, 27, 18, 5, 30, 16, 28, 2, 10, 31, 6, 15, 32, 29, 33, 14, 19, 25, 4], 'cur_cost': 24565.0}, {'tour': [7, 8, 9, 4, 23, 13, 14, 26, 12, 17, 3, 22, 24, 21, 20, 18, 1, 5, 30, 16, 28, 2, 0, 10, 15, 6, 29, 19, 25, 11, 33, 31, 32, 27], 'cur_cost': 19195.0}]
2025-08-05 10:28:48,953 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:48,953 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 178, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 178, 'cache_hits': 0, 'similarity_calculations': 768, 'cache_hit_rate': 0.0, 'cache_size': 768}}
2025-08-05 10:28:48,954 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([33,  3, 10, 17,  8, 19, 12, 32,  7, 16,  4,  1, 15, 26, 11,  2, 22,
       30, 23, 18, 14, 29, 24, 13, 25, 20, 21,  6,  0,  5,  9, 31, 27, 28],
      dtype=int64), 'cur_cost': 21923.0, 'intermediate_solutions': [{'tour': array([10, 11,  6, 21, 16,  1, 27, 29,  8, 20, 31, 33, 30,  7, 28, 25,  9,
       14,  5, 18,  0, 24,  3, 12, 22,  2, 32,  4, 13, 19, 23, 17, 26, 15]), 'cur_cost': 23108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([21, 10, 11,  6, 16,  1, 27, 29,  8, 20, 31, 33, 30,  7, 28, 25,  9,
       14,  5, 18,  0, 24,  3, 12, 22,  2, 32,  4, 13, 19, 23, 17, 26, 15]), 'cur_cost': 23383.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([16, 21, 10, 11,  6,  1, 27, 29,  8, 20, 31, 33, 30,  7, 28, 25,  9,
       14,  5, 18,  0, 24,  3, 12, 22,  2, 32,  4, 13, 19, 23, 17, 26, 15]), 'cur_cost': 22139.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6, 21, 10, 11, 16,  1, 27, 29,  8, 20, 31, 33, 30,  7, 28, 25,  9,
       14,  5, 18,  0, 24,  3, 12, 22,  2, 32,  4, 13, 19, 23, 17, 26, 15]), 'cur_cost': 23073.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6, 16, 21, 10, 11,  1, 27, 29,  8, 20, 31, 33, 30,  7, 28, 25,  9,
       14,  5, 18,  0, 24,  3, 12, 22,  2, 32,  4, 13, 19, 23, 17, 26, 15]), 'cur_cost': 23414.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:48,955 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 21923.00)
2025-08-05 10:28:48,955 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:48,955 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:48,955 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:48,956 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 34
2025-08-05 10:28:48,956 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,956 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,957 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,957 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,957 - ExplorationExpert - INFO - 探索路径生成完成，成本: 4822.0, 路径长度: 34, 收集中间解: 3
2025-08-05 10:28:48,957 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 22, 21, 24, 23, 17, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 7, 9, 2, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 4822.0, 'intermediate_solutions': [{'tour': [26, 31, 33, 19, 27, 5, 29, 25, 28, 18, 17, 13, 0, 2, 4, 7, 9, 3, 10, 32, 30, 22, 11, 16, 15, 24, 14, 12, 1, 6, 20, 23, 21, 8], 'cur_cost': 14728.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [26, 31, 20, 19, 27, 5, 29, 25, 28, 18, 17, 13, 0, 2, 4, 7, 9, 3, 10, 8, 21, 23, 33, 6, 1, 12, 14, 24, 15, 16, 11, 22, 30, 32], 'cur_cost': 13918.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [26, 31, 20, 19, 27, 5, 29, 25, 28, 18, 17, 13, 0, 2, 4, 7, 9, 3, 10, 30, 22, 11, 16, 15, 24, 14, 12, 1, 6, 33, 23, 21, 8, 32], 'cur_cost': 15145.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:48,957 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 4822.00)
2025-08-05 10:28:48,957 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:48,957 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:48,958 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:48,958 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 34
2025-08-05 10:28:48,958 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,959 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,959 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,959 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,959 - ExplorationExpert - INFO - 探索路径生成完成，成本: 6135.0, 路径长度: 34, 收集中间解: 3
2025-08-05 10:28:48,959 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 3, 19, 11, 13, 14, 15, 16, 10, 12, 9, 6, 5, 4, 1, 8, 7, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 18], 'cur_cost': 6135.0, 'intermediate_solutions': [{'tour': [5, 15, 13, 1, 23, 11, 7, 9, 31, 12, 30, 16, 0, 8, 26, 32, 17, 18, 21, 28, 22, 20, 27, 33, 24, 14, 25, 4, 10, 6, 3, 2, 19, 29], 'cur_cost': 20321.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 14, 13, 1, 23, 11, 7, 9, 31, 12, 30, 16, 0, 8, 26, 32, 17, 18, 21, 28, 22, 33, 27, 20, 24, 15, 25, 4, 10, 6, 3, 2, 19, 29], 'cur_cost': 20390.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 14, 13, 1, 23, 11, 7, 9, 31, 12, 30, 16, 0, 8, 26, 32, 17, 18, 21, 28, 22, 20, 27, 33, 24, 15, 25, 4, 10, 6, 3, 19, 2, 29], 'cur_cost': 21644.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:48,959 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 6135.00)
2025-08-05 10:28:48,960 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:48,960 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:48,960 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:48,961 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 34
2025-08-05 10:28:48,961 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,961 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,961 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,961 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,961 - ExplorationExpert - INFO - 探索路径生成完成，成本: 6037.0, 路径长度: 34, 收集中间解: 3
2025-08-05 10:28:48,961 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 7, 19, 14, 13, 15, 16, 10, 12, 11, 9, 6, 5, 4, 3, 8, 1, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 18], 'cur_cost': 6037.0, 'intermediate_solutions': [{'tour': [7, 23, 11, 17, 2, 16, 30, 15, 28, 32, 26, 19, 10, 0, 5, 24, 14, 1, 12, 21, 9, 27, 33, 25, 20, 22, 31, 3, 18, 6, 29, 4, 8, 13], 'cur_cost': 23566.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 23, 11, 17, 2, 16, 30, 21, 28, 32, 26, 19, 10, 25, 33, 27, 9, 15, 12, 1, 14, 24, 5, 0, 20, 22, 31, 3, 18, 6, 29, 4, 8, 13], 'cur_cost': 22313.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 23, 11, 17, 2, 16, 30, 21, 28, 32, 26, 19, 10, 0, 5, 24, 1, 12, 15, 9, 27, 33, 14, 25, 20, 22, 31, 3, 18, 6, 29, 4, 8, 13], 'cur_cost': 23191.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:48,962 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 6037.00)
2025-08-05 10:28:48,962 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:48,962 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:48,962 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:48,962 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 23222.0
2025-08-05 10:28:48,971 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 10:28:48,972 - ExploitationExpert - INFO - res_population_costs: [3575, 3575, 3575, 3575, 3575, 3575, 3575, 3575, 3575, 3576.0]
2025-08-05 10:28:48,972 - ExploitationExpert - INFO - res_populations: [array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 13, 12, 11, 15, 16,  9,  7,  6,  2,  5,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0, 33, 30, 29, 28, 27, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 21, 19, 18, 20, 23,
       17, 10, 14, 13, 12, 11, 15, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 14, 13, 11, 12, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64)]
2025-08-05 10:28:48,975 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:48,975 - ExploitationExpert - INFO - populations: [{'tour': [0, 17, 11, 14, 13, 15, 16, 10, 12, 9, 6, 5, 4, 3, 8, 7, 2, 1, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 20, 21, 19, 18], 'cur_cost': 5910.0}, {'tour': [6, 17, 25, 18, 7, 15, 2, 14, 8, 9, 11, 33, 13, 3, 12, 22, 27, 5, 23, 0, 29, 21, 32, 16, 19, 26, 31, 24, 1, 28, 20, 4, 10, 30], 'cur_cost': 26729.0}, {'tour': array([18, 12,  9, 23,  4, 22, 21,  7, 30, 32, 20,  0, 14, 17,  1, 13,  5,
       19, 29, 15,  3, 10, 11, 25,  6, 33, 26,  2, 24, 31,  8, 16, 27, 28],
      dtype=int64), 'cur_cost': 25574.0}, {'tour': [17, 18, 28, 26, 31, 30, 20, 14, 10, 6, 2, 4, 1, 9, 3, 12, 5, 8, 0, 33, 22, 19, 23, 24, 21, 29, 7, 11, 15, 16, 25, 32, 27, 13], 'cur_cost': 11798.0}, {'tour': array([33,  3, 10, 17,  8, 19, 12, 32,  7, 16,  4,  1, 15, 26, 11,  2, 22,
       30, 23, 18, 14, 29, 24, 13, 25, 20, 21,  6,  0,  5,  9, 31, 27, 28],
      dtype=int64), 'cur_cost': 21923.0}, {'tour': [0, 22, 21, 24, 23, 17, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 7, 9, 2, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 4822.0}, {'tour': [0, 3, 19, 11, 13, 14, 15, 16, 10, 12, 9, 6, 5, 4, 1, 8, 7, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 18], 'cur_cost': 6135.0}, {'tour': [0, 7, 19, 14, 13, 15, 16, 10, 12, 11, 9, 6, 5, 4, 3, 8, 1, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 18], 'cur_cost': 6037.0}, {'tour': array([ 2, 16, 18, 10, 12, 27, 22,  0, 17, 28, 33, 31, 32, 24,  5, 29, 26,
        7,  3, 21,  1, 23, 25, 20,  6, 19, 15,  4, 13, 11,  8, 14,  9, 30],
      dtype=int64), 'cur_cost': 23222.0}, {'tour': [7, 8, 9, 4, 23, 13, 14, 26, 12, 17, 3, 22, 24, 21, 20, 18, 1, 5, 30, 16, 28, 2, 0, 10, 15, 6, 29, 19, 25, 11, 33, 31, 32, 27], 'cur_cost': 19195.0}]
2025-08-05 10:28:48,976 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:48,976 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 179, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 179, 'cache_hits': 0, 'similarity_calculations': 777, 'cache_hit_rate': 0.0, 'cache_size': 777}}
2025-08-05 10:28:48,977 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 2, 16, 18, 10, 12, 27, 22,  0, 17, 28, 33, 31, 32, 24,  5, 29, 26,
        7,  3, 21,  1, 23, 25, 20,  6, 19, 15,  4, 13, 11,  8, 14,  9, 30],
      dtype=int64), 'cur_cost': 23222.0, 'intermediate_solutions': [{'tour': array([13, 24, 20,  7,  8,  1, 23, 11,  9, 26, 12, 17,  3,  0, 22, 21, 27,
       18,  5, 30, 16, 28,  2, 10, 31,  6, 15, 32, 29, 33, 14, 19, 25,  4]), 'cur_cost': 24563.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 7, 13, 24, 20,  8,  1, 23, 11,  9, 26, 12, 17,  3,  0, 22, 21, 27,
       18,  5, 30, 16, 28,  2, 10, 31,  6, 15, 32, 29, 33, 14, 19, 25,  4]), 'cur_cost': 24550.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 8,  7, 13, 24, 20,  1, 23, 11,  9, 26, 12, 17,  3,  0, 22, 21, 27,
       18,  5, 30, 16, 28,  2, 10, 31,  6, 15, 32, 29, 33, 14, 19, 25,  4]), 'cur_cost': 24560.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([20,  7, 13, 24,  8,  1, 23, 11,  9, 26, 12, 17,  3,  0, 22, 21, 27,
       18,  5, 30, 16, 28,  2, 10, 31,  6, 15, 32, 29, 33, 14, 19, 25,  4]), 'cur_cost': 26543.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([20,  8,  7, 13, 24,  1, 23, 11,  9, 26, 12, 17,  3,  0, 22, 21, 27,
       18,  5, 30, 16, 28,  2, 10, 31,  6, 15, 32, 29, 33, 14, 19, 25,  4]), 'cur_cost': 26562.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:48,977 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 23222.00)
2025-08-05 10:28:48,978 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:48,978 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:48,978 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:48,980 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 34
2025-08-05 10:28:48,980 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,981 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,981 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,981 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:48,981 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14847.0, 路径长度: 34, 收集中间解: 3
2025-08-05 10:28:48,981 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [9, 6, 0, 7, 2, 3, 11, 12, 23, 30, 19, 24, 33, 26, 20, 32, 21, 25, 18, 17, 27, 28, 4, 15, 8, 5, 10, 16, 14, 1, 29, 31, 22, 13], 'cur_cost': 14847.0, 'intermediate_solutions': [{'tour': [7, 8, 9, 4, 22, 13, 14, 26, 12, 17, 3, 23, 24, 21, 20, 18, 1, 5, 30, 16, 28, 2, 0, 10, 15, 6, 29, 19, 25, 11, 33, 31, 32, 27], 'cur_cost': 19230.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 8, 9, 4, 1, 18, 20, 21, 24, 22, 3, 17, 12, 26, 14, 13, 23, 5, 30, 16, 28, 2, 0, 10, 15, 6, 29, 19, 25, 11, 33, 31, 32, 27], 'cur_cost': 19171.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 8, 9, 4, 23, 13, 14, 26, 12, 6, 17, 3, 22, 24, 21, 20, 18, 1, 5, 30, 16, 28, 2, 0, 10, 15, 29, 19, 25, 11, 33, 31, 32, 27], 'cur_cost': 19721.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:48,981 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 14847.00)
2025-08-05 10:28:48,982 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:48,982 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:48,983 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 11, 14, 13, 15, 16, 10, 12, 9, 6, 5, 4, 3, 8, 7, 2, 1, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 20, 21, 19, 18], 'cur_cost': 5910.0, 'intermediate_solutions': [{'tour': [29, 24, 25, 18, 31, 33, 2, 14, 4, 10, 8, 13, 7, 9, 12, 22, 17, 21, 19, 0, 28, 27, 26, 32, 1, 5, 6, 15, 16, 3, 11, 23, 20, 30], 'cur_cost': 15988.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [29, 24, 25, 18, 31, 0, 2, 14, 4, 10, 8, 13, 7, 9, 12, 22, 17, 21, 19, 33, 28, 6, 5, 1, 32, 26, 27, 15, 16, 3, 11, 23, 20, 30], 'cur_cost': 16110.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [29, 24, 25, 18, 31, 0, 2, 14, 4, 10, 8, 30, 13, 7, 9, 12, 22, 17, 21, 19, 33, 28, 27, 26, 32, 1, 5, 6, 15, 16, 3, 11, 23, 20], 'cur_cost': 16042.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [6, 17, 25, 18, 7, 15, 2, 14, 8, 9, 11, 33, 13, 3, 12, 22, 27, 5, 23, 0, 29, 21, 32, 16, 19, 26, 31, 24, 1, 28, 20, 4, 10, 30], 'cur_cost': 26729.0, 'intermediate_solutions': [{'tour': [0, 1, 6, 9, 7, 8, 3, 2, 5, 4, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 3855.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 6, 7, 9, 8, 3, 4, 5, 2, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 3825.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 6, 9, 7, 8, 3, 4, 5, 2, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 21, 20, 18, 33, 19, 25, 31, 30, 29, 28, 32, 26, 27], 'cur_cost': 5094.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([18, 12,  9, 23,  4, 22, 21,  7, 30, 32, 20,  0, 14, 17,  1, 13,  5,
       19, 29, 15,  3, 10, 11, 25,  6, 33, 26,  2, 24, 31,  8, 16, 27, 28],
      dtype=int64), 'cur_cost': 25574.0, 'intermediate_solutions': [{'tour': array([ 6,  1, 29,  9, 14,  0,  3,  4,  5, 23,  8, 13, 30, 26, 12, 32, 17,
       21, 16, 33, 28, 27, 10, 31,  7, 19, 18, 15, 24, 25, 11, 20,  2, 22]), 'cur_cost': 23551.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9,  6,  1, 29, 14,  0,  3,  4,  5, 23,  8, 13, 30, 26, 12, 32, 17,
       21, 16, 33, 28, 27, 10, 31,  7, 19, 18, 15, 24, 25, 11, 20,  2, 22]), 'cur_cost': 23098.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14,  9,  6,  1, 29,  0,  3,  4,  5, 23,  8, 13, 30, 26, 12, 32, 17,
       21, 16, 33, 28, 27, 10, 31,  7, 19, 18, 15, 24, 25, 11, 20,  2, 22]), 'cur_cost': 22565.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([29,  9,  6,  1, 14,  0,  3,  4,  5, 23,  8, 13, 30, 26, 12, 32, 17,
       21, 16, 33, 28, 27, 10, 31,  7, 19, 18, 15, 24, 25, 11, 20,  2, 22]), 'cur_cost': 22386.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([29, 14,  9,  6,  1,  0,  3,  4,  5, 23,  8, 13, 30, 26, 12, 32, 17,
       21, 16, 33, 28, 27, 10, 31,  7, 19, 18, 15, 24, 25, 11, 20,  2, 22]), 'cur_cost': 21864.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [17, 18, 28, 26, 31, 30, 20, 14, 10, 6, 2, 4, 1, 9, 3, 12, 5, 8, 0, 33, 22, 19, 23, 24, 21, 29, 7, 11, 15, 16, 25, 32, 27, 13], 'cur_cost': 11798.0, 'intermediate_solutions': [{'tour': [20, 16, 25, 18, 31, 13, 2, 14, 1, 10, 11, 15, 9, 29, 28, 22, 12, 21, 3, 0, 30, 4, 26, 32, 17, 19, 6, 8, 5, 27, 23, 7, 33, 24], 'cur_cost': 20983.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [17, 32, 26, 4, 30, 0, 3, 21, 12, 22, 28, 29, 9, 15, 11, 10, 1, 14, 2, 13, 31, 18, 25, 5, 20, 19, 6, 8, 16, 27, 23, 7, 33, 24], 'cur_cost': 21915.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [20, 5, 25, 18, 31, 13, 2, 14, 1, 10, 11, 9, 29, 28, 22, 12, 21, 3, 0, 30, 4, 26, 32, 17, 19, 6, 15, 8, 16, 27, 23, 7, 33, 24], 'cur_cost': 23247.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([33,  3, 10, 17,  8, 19, 12, 32,  7, 16,  4,  1, 15, 26, 11,  2, 22,
       30, 23, 18, 14, 29, 24, 13, 25, 20, 21,  6,  0,  5,  9, 31, 27, 28],
      dtype=int64), 'cur_cost': 21923.0, 'intermediate_solutions': [{'tour': array([10, 11,  6, 21, 16,  1, 27, 29,  8, 20, 31, 33, 30,  7, 28, 25,  9,
       14,  5, 18,  0, 24,  3, 12, 22,  2, 32,  4, 13, 19, 23, 17, 26, 15]), 'cur_cost': 23108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([21, 10, 11,  6, 16,  1, 27, 29,  8, 20, 31, 33, 30,  7, 28, 25,  9,
       14,  5, 18,  0, 24,  3, 12, 22,  2, 32,  4, 13, 19, 23, 17, 26, 15]), 'cur_cost': 23383.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([16, 21, 10, 11,  6,  1, 27, 29,  8, 20, 31, 33, 30,  7, 28, 25,  9,
       14,  5, 18,  0, 24,  3, 12, 22,  2, 32,  4, 13, 19, 23, 17, 26, 15]), 'cur_cost': 22139.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6, 21, 10, 11, 16,  1, 27, 29,  8, 20, 31, 33, 30,  7, 28, 25,  9,
       14,  5, 18,  0, 24,  3, 12, 22,  2, 32,  4, 13, 19, 23, 17, 26, 15]), 'cur_cost': 23073.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6, 16, 21, 10, 11,  1, 27, 29,  8, 20, 31, 33, 30,  7, 28, 25,  9,
       14,  5, 18,  0, 24,  3, 12, 22,  2, 32,  4, 13, 19, 23, 17, 26, 15]), 'cur_cost': 23414.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 22, 21, 24, 23, 17, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 7, 9, 2, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 4822.0, 'intermediate_solutions': [{'tour': [26, 31, 33, 19, 27, 5, 29, 25, 28, 18, 17, 13, 0, 2, 4, 7, 9, 3, 10, 32, 30, 22, 11, 16, 15, 24, 14, 12, 1, 6, 20, 23, 21, 8], 'cur_cost': 14728.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [26, 31, 20, 19, 27, 5, 29, 25, 28, 18, 17, 13, 0, 2, 4, 7, 9, 3, 10, 8, 21, 23, 33, 6, 1, 12, 14, 24, 15, 16, 11, 22, 30, 32], 'cur_cost': 13918.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [26, 31, 20, 19, 27, 5, 29, 25, 28, 18, 17, 13, 0, 2, 4, 7, 9, 3, 10, 30, 22, 11, 16, 15, 24, 14, 12, 1, 6, 33, 23, 21, 8, 32], 'cur_cost': 15145.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 19, 11, 13, 14, 15, 16, 10, 12, 9, 6, 5, 4, 1, 8, 7, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 18], 'cur_cost': 6135.0, 'intermediate_solutions': [{'tour': [5, 15, 13, 1, 23, 11, 7, 9, 31, 12, 30, 16, 0, 8, 26, 32, 17, 18, 21, 28, 22, 20, 27, 33, 24, 14, 25, 4, 10, 6, 3, 2, 19, 29], 'cur_cost': 20321.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 14, 13, 1, 23, 11, 7, 9, 31, 12, 30, 16, 0, 8, 26, 32, 17, 18, 21, 28, 22, 33, 27, 20, 24, 15, 25, 4, 10, 6, 3, 2, 19, 29], 'cur_cost': 20390.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 14, 13, 1, 23, 11, 7, 9, 31, 12, 30, 16, 0, 8, 26, 32, 17, 18, 21, 28, 22, 20, 27, 33, 24, 15, 25, 4, 10, 6, 3, 19, 2, 29], 'cur_cost': 21644.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 19, 14, 13, 15, 16, 10, 12, 11, 9, 6, 5, 4, 3, 8, 1, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 18], 'cur_cost': 6037.0, 'intermediate_solutions': [{'tour': [7, 23, 11, 17, 2, 16, 30, 15, 28, 32, 26, 19, 10, 0, 5, 24, 14, 1, 12, 21, 9, 27, 33, 25, 20, 22, 31, 3, 18, 6, 29, 4, 8, 13], 'cur_cost': 23566.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 23, 11, 17, 2, 16, 30, 21, 28, 32, 26, 19, 10, 25, 33, 27, 9, 15, 12, 1, 14, 24, 5, 0, 20, 22, 31, 3, 18, 6, 29, 4, 8, 13], 'cur_cost': 22313.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 23, 11, 17, 2, 16, 30, 21, 28, 32, 26, 19, 10, 0, 5, 24, 1, 12, 15, 9, 27, 33, 14, 25, 20, 22, 31, 3, 18, 6, 29, 4, 8, 13], 'cur_cost': 23191.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 2, 16, 18, 10, 12, 27, 22,  0, 17, 28, 33, 31, 32, 24,  5, 29, 26,
        7,  3, 21,  1, 23, 25, 20,  6, 19, 15,  4, 13, 11,  8, 14,  9, 30],
      dtype=int64), 'cur_cost': 23222.0, 'intermediate_solutions': [{'tour': array([13, 24, 20,  7,  8,  1, 23, 11,  9, 26, 12, 17,  3,  0, 22, 21, 27,
       18,  5, 30, 16, 28,  2, 10, 31,  6, 15, 32, 29, 33, 14, 19, 25,  4]), 'cur_cost': 24563.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 7, 13, 24, 20,  8,  1, 23, 11,  9, 26, 12, 17,  3,  0, 22, 21, 27,
       18,  5, 30, 16, 28,  2, 10, 31,  6, 15, 32, 29, 33, 14, 19, 25,  4]), 'cur_cost': 24550.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 8,  7, 13, 24, 20,  1, 23, 11,  9, 26, 12, 17,  3,  0, 22, 21, 27,
       18,  5, 30, 16, 28,  2, 10, 31,  6, 15, 32, 29, 33, 14, 19, 25,  4]), 'cur_cost': 24560.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([20,  7, 13, 24,  8,  1, 23, 11,  9, 26, 12, 17,  3,  0, 22, 21, 27,
       18,  5, 30, 16, 28,  2, 10, 31,  6, 15, 32, 29, 33, 14, 19, 25,  4]), 'cur_cost': 26543.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([20,  8,  7, 13, 24,  1, 23, 11,  9, 26, 12, 17,  3,  0, 22, 21, 27,
       18,  5, 30, 16, 28,  2, 10, 31,  6, 15, 32, 29, 33, 14, 19, 25,  4]), 'cur_cost': 26562.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [9, 6, 0, 7, 2, 3, 11, 12, 23, 30, 19, 24, 33, 26, 20, 32, 21, 25, 18, 17, 27, 28, 4, 15, 8, 5, 10, 16, 14, 1, 29, 31, 22, 13], 'cur_cost': 14847.0, 'intermediate_solutions': [{'tour': [7, 8, 9, 4, 22, 13, 14, 26, 12, 17, 3, 23, 24, 21, 20, 18, 1, 5, 30, 16, 28, 2, 0, 10, 15, 6, 29, 19, 25, 11, 33, 31, 32, 27], 'cur_cost': 19230.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 8, 9, 4, 1, 18, 20, 21, 24, 22, 3, 17, 12, 26, 14, 13, 23, 5, 30, 16, 28, 2, 0, 10, 15, 6, 29, 19, 25, 11, 33, 31, 32, 27], 'cur_cost': 19171.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 8, 9, 4, 23, 13, 14, 26, 12, 6, 17, 3, 22, 24, 21, 20, 18, 1, 5, 30, 16, 28, 2, 0, 10, 15, 29, 19, 25, 11, 33, 31, 32, 27], 'cur_cost': 19721.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:48,984 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:48,984 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:48,986 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=4822.000, 多样性=0.929
2025-08-05 10:28:48,986 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 10:28:48,986 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 10:28:48,986 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:48,987 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.04687194726164887, 'best_improvement': -0.26694692590646346}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.026027397260273765}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.033919745030507764, 'recent_improvements': [0.06539881945741151, -0.13499026312930193, -0.0024406706036040355], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 10, 'new_count': 10, 'count_change': 0, 'old_best_cost': 3575, 'new_best_cost': 3575, 'quality_improvement': 0.0, 'old_diversity': 0.5908496732026144, 'new_diversity': 0.5908496732026144, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:48,988 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 10:28:48,988 - __main__ - INFO - composite2_34 开始进化第 5 代
2025-08-05 10:28:48,988 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 10:28:48,989 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:48,989 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=4822.000, 多样性=0.929
2025-08-05 10:28:48,989 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:48,991 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.929
2025-08-05 10:28:48,991 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:48,993 - EliteExpert - INFO - 精英解分析完成: 精英解数量=10, 多样性=0.591
2025-08-05 10:28:48,994 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 10:28:48,994 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:48,994 - LandscapeExpert - INFO - 添加精英解数据: 10个精英解
2025-08-05 10:28:48,995 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-05 10:28:49,041 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.450, 适应度梯度: -2669.560, 聚类评分: 0.000, 覆盖率: 0.084, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:49,041 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 10:28:49,041 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:49,041 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite2_34
2025-08-05 10:28:49,048 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.5%, 梯度: 766.19 → 708.42
2025-08-05 10:28:49,203 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite2_34\landscape_composite2_34_iter_70_20250805_102849.html
2025-08-05 10:28:49,266 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite2_34\dashboard_composite2_34_iter_70_20250805_102849.html
2025-08-05 10:28:49,267 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 70
2025-08-05 10:28:49,267 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 10:28:49,267 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2725秒
2025-08-05 10:28:49,267 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.45, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -2669.5600000000004, 'local_optima_density': 0.45, 'gradient_variance': 56749630.9744, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0839, 'fitness_entropy': 0.7414439525170821, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -2669.560)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.084)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360929.0412078, 'performance_metrics': {}}}
2025-08-05 10:28:49,267 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:49,267 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:49,267 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:49,268 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:49,268 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 10:28:49,268 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 10:28:49,268 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 10:28:49,268 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:49,268 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:49,268 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 10:28:49,269 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:49,269 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:49,269 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 5} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:49,269 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:49,269 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:49,269 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:49,270 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 34
2025-08-05 10:28:49,270 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,270 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,270 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,271 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,271 - ExplorationExpert - INFO - 探索路径生成完成，成本: 22077.0, 路径长度: 34, 收集中间解: 3
2025-08-05 10:28:49,271 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [6, 12, 11, 18, 7, 22, 2, 32, 30, 16, 4, 5, 14, 9, 8, 13, 1, 19, 29, 0, 3, 21, 23, 25, 17, 33, 31, 24, 15, 28, 20, 10, 27, 26], 'cur_cost': 22077.0, 'intermediate_solutions': [{'tour': [0, 14, 11, 17, 13, 15, 16, 10, 12, 9, 6, 5, 4, 3, 8, 7, 2, 1, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 20, 21, 19, 18], 'cur_cost': 6336.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 11, 14, 13, 15, 16, 10, 12, 9, 6, 5, 4, 3, 8, 7, 2, 1, 18, 19, 21, 20, 23, 22, 24, 27, 26, 25, 31, 32, 28, 29, 30, 33], 'cur_cost': 5926.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 11, 14, 13, 15, 16, 10, 12, 6, 5, 4, 3, 9, 8, 7, 2, 1, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 20, 21, 19, 18], 'cur_cost': 5997.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:49,271 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 22077.00)
2025-08-05 10:28:49,271 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 10:28:49,271 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:49,272 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:49,272 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 23328.0
2025-08-05 10:28:49,284 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 10:28:49,284 - ExploitationExpert - INFO - res_population_costs: [3575, 3575, 3575, 3575, 3575, 3575, 3575, 3575, 3575, 3576.0]
2025-08-05 10:28:49,284 - ExploitationExpert - INFO - res_populations: [array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 13, 12, 11, 15, 16,  9,  7,  6,  2,  5,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0, 33, 30, 29, 28, 27, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 21, 19, 18, 20, 23,
       17, 10, 14, 13, 12, 11, 15, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 14, 13, 11, 12, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64)]
2025-08-05 10:28:49,288 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:49,288 - ExploitationExpert - INFO - populations: [{'tour': [6, 12, 11, 18, 7, 22, 2, 32, 30, 16, 4, 5, 14, 9, 8, 13, 1, 19, 29, 0, 3, 21, 23, 25, 17, 33, 31, 24, 15, 28, 20, 10, 27, 26], 'cur_cost': 22077.0}, {'tour': array([ 5,  6, 15, 31, 23, 11, 13, 22, 32, 18,  4,  8,  1, 17,  3, 29, 16,
       12, 26,  7, 20, 27,  2, 25, 14, 30, 10, 24, 21, 19,  9, 28, 33,  0],
      dtype=int64), 'cur_cost': 23328.0}, {'tour': [18, 12, 9, 23, 4, 22, 21, 7, 30, 32, 20, 0, 14, 17, 1, 13, 5, 19, 29, 15, 3, 10, 11, 25, 6, 33, 26, 2, 24, 31, 8, 16, 27, 28], 'cur_cost': 25574.0}, {'tour': [17, 18, 28, 26, 31, 30, 20, 14, 10, 6, 2, 4, 1, 9, 3, 12, 5, 8, 0, 33, 22, 19, 23, 24, 21, 29, 7, 11, 15, 16, 25, 32, 27, 13], 'cur_cost': 11798.0}, {'tour': [33, 3, 10, 17, 8, 19, 12, 32, 7, 16, 4, 1, 15, 26, 11, 2, 22, 30, 23, 18, 14, 29, 24, 13, 25, 20, 21, 6, 0, 5, 9, 31, 27, 28], 'cur_cost': 21923.0}, {'tour': [0, 22, 21, 24, 23, 17, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 7, 9, 2, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 4822.0}, {'tour': [0, 3, 19, 11, 13, 14, 15, 16, 10, 12, 9, 6, 5, 4, 1, 8, 7, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 18], 'cur_cost': 6135.0}, {'tour': [0, 7, 19, 14, 13, 15, 16, 10, 12, 11, 9, 6, 5, 4, 3, 8, 1, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 18], 'cur_cost': 6037.0}, {'tour': [2, 16, 18, 10, 12, 27, 22, 0, 17, 28, 33, 31, 32, 24, 5, 29, 26, 7, 3, 21, 1, 23, 25, 20, 6, 19, 15, 4, 13, 11, 8, 14, 9, 30], 'cur_cost': 23222.0}, {'tour': [9, 6, 0, 7, 2, 3, 11, 12, 23, 30, 19, 24, 33, 26, 20, 32, 21, 25, 18, 17, 27, 28, 4, 15, 8, 5, 10, 16, 14, 1, 29, 31, 22, 13], 'cur_cost': 14847.0}]
2025-08-05 10:28:49,289 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:49,289 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 180, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 180, 'cache_hits': 0, 'similarity_calculations': 787, 'cache_hit_rate': 0.0, 'cache_size': 787}}
2025-08-05 10:28:49,290 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([ 5,  6, 15, 31, 23, 11, 13, 22, 32, 18,  4,  8,  1, 17,  3, 29, 16,
       12, 26,  7, 20, 27,  2, 25, 14, 30, 10, 24, 21, 19,  9, 28, 33,  0],
      dtype=int64), 'cur_cost': 23328.0, 'intermediate_solutions': [{'tour': array([25, 17,  6, 18,  7, 15,  2, 14,  8,  9, 11, 33, 13,  3, 12, 22, 27,
        5, 23,  0, 29, 21, 32, 16, 19, 26, 31, 24,  1, 28, 20,  4, 10, 30]), 'cur_cost': 26282.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([18, 25, 17,  6,  7, 15,  2, 14,  8,  9, 11, 33, 13,  3, 12, 22, 27,
        5, 23,  0, 29, 21, 32, 16, 19, 26, 31, 24,  1, 28, 20,  4, 10, 30]), 'cur_cost': 25493.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 7, 18, 25, 17,  6, 15,  2, 14,  8,  9, 11, 33, 13,  3, 12, 22, 27,
        5, 23,  0, 29, 21, 32, 16, 19, 26, 31, 24,  1, 28, 20,  4, 10, 30]), 'cur_cost': 26710.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6, 18, 25, 17,  7, 15,  2, 14,  8,  9, 11, 33, 13,  3, 12, 22, 27,
        5, 23,  0, 29, 21, 32, 16, 19, 26, 31, 24,  1, 28, 20,  4, 10, 30]), 'cur_cost': 26729.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6,  7, 18, 25, 17, 15,  2, 14,  8,  9, 11, 33, 13,  3, 12, 22, 27,
        5, 23,  0, 29, 21, 32, 16, 19, 26, 31, 24,  1, 28, 20,  4, 10, 30]), 'cur_cost': 25793.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:49,290 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 23328.00)
2025-08-05 10:28:49,291 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-05 10:28:49,291 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:49,291 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:49,291 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 19515.0
2025-08-05 10:28:49,302 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 10:28:49,302 - ExploitationExpert - INFO - res_population_costs: [3575, 3575, 3575, 3575, 3575, 3575, 3575, 3575, 3575, 3576.0]
2025-08-05 10:28:49,302 - ExploitationExpert - INFO - res_populations: [array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 13, 12, 11, 15, 16,  9,  7,  6,  2,  5,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0, 33, 30, 29, 28, 27, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 21, 19, 18, 20, 23,
       17, 10, 14, 13, 12, 11, 15, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 14, 13, 11, 12, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64)]
2025-08-05 10:28:49,305 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:49,305 - ExploitationExpert - INFO - populations: [{'tour': [6, 12, 11, 18, 7, 22, 2, 32, 30, 16, 4, 5, 14, 9, 8, 13, 1, 19, 29, 0, 3, 21, 23, 25, 17, 33, 31, 24, 15, 28, 20, 10, 27, 26], 'cur_cost': 22077.0}, {'tour': array([ 5,  6, 15, 31, 23, 11, 13, 22, 32, 18,  4,  8,  1, 17,  3, 29, 16,
       12, 26,  7, 20, 27,  2, 25, 14, 30, 10, 24, 21, 19,  9, 28, 33,  0],
      dtype=int64), 'cur_cost': 23328.0}, {'tour': array([17, 16, 24,  0,  6, 12, 10,  9, 20, 23,  7, 27, 30,  5, 21, 32, 18,
       33, 25, 14, 26, 28, 22, 31, 19, 11,  2,  8,  4,  1,  3, 13, 15, 29],
      dtype=int64), 'cur_cost': 19515.0}, {'tour': [17, 18, 28, 26, 31, 30, 20, 14, 10, 6, 2, 4, 1, 9, 3, 12, 5, 8, 0, 33, 22, 19, 23, 24, 21, 29, 7, 11, 15, 16, 25, 32, 27, 13], 'cur_cost': 11798.0}, {'tour': [33, 3, 10, 17, 8, 19, 12, 32, 7, 16, 4, 1, 15, 26, 11, 2, 22, 30, 23, 18, 14, 29, 24, 13, 25, 20, 21, 6, 0, 5, 9, 31, 27, 28], 'cur_cost': 21923.0}, {'tour': [0, 22, 21, 24, 23, 17, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 7, 9, 2, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 4822.0}, {'tour': [0, 3, 19, 11, 13, 14, 15, 16, 10, 12, 9, 6, 5, 4, 1, 8, 7, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 18], 'cur_cost': 6135.0}, {'tour': [0, 7, 19, 14, 13, 15, 16, 10, 12, 11, 9, 6, 5, 4, 3, 8, 1, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 18], 'cur_cost': 6037.0}, {'tour': [2, 16, 18, 10, 12, 27, 22, 0, 17, 28, 33, 31, 32, 24, 5, 29, 26, 7, 3, 21, 1, 23, 25, 20, 6, 19, 15, 4, 13, 11, 8, 14, 9, 30], 'cur_cost': 23222.0}, {'tour': [9, 6, 0, 7, 2, 3, 11, 12, 23, 30, 19, 24, 33, 26, 20, 32, 21, 25, 18, 17, 27, 28, 4, 15, 8, 5, 10, 16, 14, 1, 29, 31, 22, 13], 'cur_cost': 14847.0}]
2025-08-05 10:28:49,307 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:49,307 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 181, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 181, 'cache_hits': 0, 'similarity_calculations': 798, 'cache_hit_rate': 0.0, 'cache_size': 798}}
2025-08-05 10:28:49,308 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([17, 16, 24,  0,  6, 12, 10,  9, 20, 23,  7, 27, 30,  5, 21, 32, 18,
       33, 25, 14, 26, 28, 22, 31, 19, 11,  2,  8,  4,  1,  3, 13, 15, 29],
      dtype=int64), 'cur_cost': 19515.0, 'intermediate_solutions': [{'tour': array([ 9, 12, 18, 23,  4, 22, 21,  7, 30, 32, 20,  0, 14, 17,  1, 13,  5,
       19, 29, 15,  3, 10, 11, 25,  6, 33, 26,  2, 24, 31,  8, 16, 27, 28]), 'cur_cost': 24804.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([23,  9, 12, 18,  4, 22, 21,  7, 30, 32, 20,  0, 14, 17,  1, 13,  5,
       19, 29, 15,  3, 10, 11, 25,  6, 33, 26,  2, 24, 31,  8, 16, 27, 28]), 'cur_cost': 25574.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 4, 23,  9, 12, 18, 22, 21,  7, 30, 32, 20,  0, 14, 17,  1, 13,  5,
       19, 29, 15,  3, 10, 11, 25,  6, 33, 26,  2, 24, 31,  8, 16, 27, 28]), 'cur_cost': 24777.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([18, 23,  9, 12,  4, 22, 21,  7, 30, 32, 20,  0, 14, 17,  1, 13,  5,
       19, 29, 15,  3, 10, 11, 25,  6, 33, 26,  2, 24, 31,  8, 16, 27, 28]), 'cur_cost': 24552.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([18,  4, 23,  9, 12, 22, 21,  7, 30, 32, 20,  0, 14, 17,  1, 13,  5,
       19, 29, 15,  3, 10, 11, 25,  6, 33, 26,  2, 24, 31,  8, 16, 27, 28]), 'cur_cost': 25617.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:49,308 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 19515.00)
2025-08-05 10:28:49,309 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:49,309 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:49,309 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:49,310 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 34
2025-08-05 10:28:49,310 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,310 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,310 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,310 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,311 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21149.0, 路径长度: 34, 收集中间解: 3
2025-08-05 10:28:49,311 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [18, 26, 7, 2, 31, 14, 13, 19, 29, 0, 3, 24, 6, 33, 1, 4, 28, 30, 20, 10, 12, 25, 17, 23, 16, 21, 8, 32, 9, 5, 15, 11, 27, 22], 'cur_cost': 21149.0, 'intermediate_solutions': [{'tour': [17, 18, 28, 26, 31, 30, 20, 14, 10, 6, 2, 4, 1, 9, 3, 12, 5, 8, 0, 33, 22, 19, 23, 24, 32, 29, 7, 11, 15, 16, 25, 21, 27, 13], 'cur_cost': 12961.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [17, 18, 28, 26, 31, 30, 20, 14, 10, 6, 2, 4, 1, 9, 3, 12, 5, 8, 0, 33, 22, 15, 11, 7, 29, 21, 24, 23, 19, 16, 25, 32, 27, 13], 'cur_cost': 13302.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [17, 18, 28, 26, 31, 30, 20, 10, 6, 2, 4, 1, 9, 14, 3, 12, 5, 8, 0, 33, 22, 19, 23, 24, 21, 29, 7, 11, 15, 16, 25, 32, 27, 13], 'cur_cost': 13038.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:49,311 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 21149.00)
2025-08-05 10:28:49,311 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:49,311 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:49,311 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:49,312 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 34
2025-08-05 10:28:49,313 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,313 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,313 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,313 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,313 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27518.0, 路径长度: 34, 收集中间解: 3
2025-08-05 10:28:49,313 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [6, 12, 7, 22, 2, 19, 3, 13, 25, 29, 23, 9, 33, 4, 28, 11, 24, 30, 5, 26, 17, 32, 31, 8, 27, 15, 16, 18, 1, 14, 20, 0, 21, 10], 'cur_cost': 27518.0, 'intermediate_solutions': [{'tour': [33, 3, 10, 17, 8, 19, 12, 32, 7, 30, 4, 1, 15, 26, 11, 2, 22, 16, 23, 18, 14, 29, 24, 13, 25, 20, 21, 6, 0, 5, 9, 31, 27, 28], 'cur_cost': 22610.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [33, 3, 10, 17, 8, 19, 12, 32, 7, 16, 4, 1, 15, 26, 11, 2, 22, 30, 23, 18, 14, 29, 24, 13, 25, 20, 21, 6, 0, 5, 27, 31, 9, 28], 'cur_cost': 23666.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [33, 3, 10, 17, 8, 19, 12, 32, 7, 16, 4, 1, 15, 26, 11, 2, 22, 23, 18, 14, 29, 24, 13, 25, 20, 21, 30, 6, 0, 5, 9, 31, 27, 28], 'cur_cost': 21138.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:49,314 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 27518.00)
2025-08-05 10:28:49,314 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:49,314 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:49,314 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:49,315 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 34
2025-08-05 10:28:49,315 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,315 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,315 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,315 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,315 - ExplorationExpert - INFO - 探索路径生成完成，成本: 5927.0, 路径长度: 34, 收集中间解: 3
2025-08-05 10:28:49,316 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 21, 23, 8, 3, 4, 5, 6, 7, 9, 2, 1, 16, 15, 13, 14, 10, 12, 11, 17, 22, 24, 19, 20, 18, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 5927.0, 'intermediate_solutions': [{'tour': [0, 22, 21, 11, 23, 17, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 7, 9, 2, 1, 16, 15, 13, 14, 10, 12, 24], 'cur_cost': 7376.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 22, 21, 24, 23, 17, 20, 18, 19, 25, 31, 30, 29, 28, 32, 9, 7, 6, 5, 4, 3, 8, 33, 27, 26, 2, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 6620.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 22, 21, 24, 23, 17, 20, 18, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 19, 3, 4, 5, 6, 7, 9, 2, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 6949.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:49,316 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 5927.00)
2025-08-05 10:28:49,316 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:49,316 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:49,316 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:49,317 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 34
2025-08-05 10:28:49,317 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,317 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,317 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,317 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,318 - ExplorationExpert - INFO - 探索路径生成完成，成本: 4803.0, 路径长度: 34, 收集中间解: 3
2025-08-05 10:28:49,318 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 2, 5, 15, 13, 14, 10, 12, 11, 16, 9, 6, 7, 8, 3, 4, 1, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 19, 18], 'cur_cost': 4803.0, 'intermediate_solutions': [{'tour': [0, 3, 19, 11, 13, 14, 15, 16, 10, 12, 9, 6, 5, 4, 1, 8, 7, 2, 21, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 33, 18], 'cur_cost': 8289.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 19, 11, 13, 14, 15, 16, 10, 12, 9, 6, 5, 4, 1, 8, 7, 2, 33, 30, 29, 28, 32, 31, 25, 24, 27, 26, 22, 23, 17, 20, 21, 18], 'cur_cost': 7288.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 19, 11, 13, 14, 15, 16, 10, 12, 9, 6, 5, 4, 1, 8, 7, 17, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 20, 21, 18], 'cur_cost': 8093.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:49,318 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 4803.00)
2025-08-05 10:28:49,318 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:49,318 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:49,318 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:49,319 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 34
2025-08-05 10:28:49,319 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,319 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,319 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,319 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,320 - ExplorationExpert - INFO - 探索路径生成完成，成本: 22132.0, 路径长度: 34, 收集中间解: 3
2025-08-05 10:28:49,320 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [21, 18, 22, 2, 32, 30, 0, 3, 14, 8, 13, 19, 25, 4, 6, 33, 24, 11, 9, 28, 29, 10, 1, 12, 16, 26, 20, 15, 23, 7, 17, 31, 27, 5], 'cur_cost': 22132.0, 'intermediate_solutions': [{'tour': [0, 9, 19, 14, 13, 15, 16, 10, 12, 11, 7, 6, 5, 4, 3, 8, 1, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 18], 'cur_cost': 6070.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 19, 14, 13, 15, 16, 10, 12, 11, 9, 6, 5, 4, 3, 8, 1, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 21, 20, 17, 23, 18], 'cur_cost': 6052.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 19, 14, 13, 15, 16, 10, 12, 11, 9, 6, 5, 4, 3, 8, 2, 1, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 18], 'cur_cost': 6011.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:49,320 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 22132.00)
2025-08-05 10:28:49,320 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:49,320 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:49,320 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:49,320 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 22905.0
2025-08-05 10:28:49,331 - ExploitationExpert - INFO - res_population_num: 11
2025-08-05 10:28:49,331 - ExploitationExpert - INFO - res_population_costs: [3575, 3575, 3575, 3575, 3575, 3575, 3575, 3575, 3575, 3576.0, 3575]
2025-08-05 10:28:49,331 - ExploitationExpert - INFO - res_populations: [array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 13, 12, 11, 15, 16,  9,  7,  6,  2,  5,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0, 33, 30, 29, 28, 27, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 21, 19, 18, 20, 23,
       17, 10, 14, 13, 12, 11, 15, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 14, 13, 11, 12, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64)]
2025-08-05 10:28:49,334 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:49,334 - ExploitationExpert - INFO - populations: [{'tour': [6, 12, 11, 18, 7, 22, 2, 32, 30, 16, 4, 5, 14, 9, 8, 13, 1, 19, 29, 0, 3, 21, 23, 25, 17, 33, 31, 24, 15, 28, 20, 10, 27, 26], 'cur_cost': 22077.0}, {'tour': array([ 5,  6, 15, 31, 23, 11, 13, 22, 32, 18,  4,  8,  1, 17,  3, 29, 16,
       12, 26,  7, 20, 27,  2, 25, 14, 30, 10, 24, 21, 19,  9, 28, 33,  0],
      dtype=int64), 'cur_cost': 23328.0}, {'tour': array([17, 16, 24,  0,  6, 12, 10,  9, 20, 23,  7, 27, 30,  5, 21, 32, 18,
       33, 25, 14, 26, 28, 22, 31, 19, 11,  2,  8,  4,  1,  3, 13, 15, 29],
      dtype=int64), 'cur_cost': 19515.0}, {'tour': [18, 26, 7, 2, 31, 14, 13, 19, 29, 0, 3, 24, 6, 33, 1, 4, 28, 30, 20, 10, 12, 25, 17, 23, 16, 21, 8, 32, 9, 5, 15, 11, 27, 22], 'cur_cost': 21149.0}, {'tour': [6, 12, 7, 22, 2, 19, 3, 13, 25, 29, 23, 9, 33, 4, 28, 11, 24, 30, 5, 26, 17, 32, 31, 8, 27, 15, 16, 18, 1, 14, 20, 0, 21, 10], 'cur_cost': 27518.0}, {'tour': [0, 21, 23, 8, 3, 4, 5, 6, 7, 9, 2, 1, 16, 15, 13, 14, 10, 12, 11, 17, 22, 24, 19, 20, 18, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 5927.0}, {'tour': [0, 2, 5, 15, 13, 14, 10, 12, 11, 16, 9, 6, 7, 8, 3, 4, 1, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 19, 18], 'cur_cost': 4803.0}, {'tour': [21, 18, 22, 2, 32, 30, 0, 3, 14, 8, 13, 19, 25, 4, 6, 33, 24, 11, 9, 28, 29, 10, 1, 12, 16, 26, 20, 15, 23, 7, 17, 31, 27, 5], 'cur_cost': 22132.0}, {'tour': array([ 5, 29,  6, 31,  7, 26,  2, 19, 32, 27, 23, 33, 21, 15,  1, 18, 25,
       10, 14, 24, 22, 20, 12, 11, 30,  8,  3, 28,  0, 17, 16,  9, 13,  4],
      dtype=int64), 'cur_cost': 22905.0}, {'tour': [9, 6, 0, 7, 2, 3, 11, 12, 23, 30, 19, 24, 33, 26, 20, 32, 21, 25, 18, 17, 27, 28, 4, 15, 8, 5, 10, 16, 14, 1, 29, 31, 22, 13], 'cur_cost': 14847.0}]
2025-08-05 10:28:49,335 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:49,335 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 182, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 182, 'cache_hits': 0, 'similarity_calculations': 810, 'cache_hit_rate': 0.0, 'cache_size': 810}}
2025-08-05 10:28:49,336 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 5, 29,  6, 31,  7, 26,  2, 19, 32, 27, 23, 33, 21, 15,  1, 18, 25,
       10, 14, 24, 22, 20, 12, 11, 30,  8,  3, 28,  0, 17, 16,  9, 13,  4],
      dtype=int64), 'cur_cost': 22905.0, 'intermediate_solutions': [{'tour': array([18, 16,  2, 10, 12, 27, 22,  0, 17, 28, 33, 31, 32, 24,  5, 29, 26,
        7,  3, 21,  1, 23, 25, 20,  6, 19, 15,  4, 13, 11,  8, 14,  9, 30]), 'cur_cost': 22973.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([10, 18, 16,  2, 12, 27, 22,  0, 17, 28, 33, 31, 32, 24,  5, 29, 26,
        7,  3, 21,  1, 23, 25, 20,  6, 19, 15,  4, 13, 11,  8, 14,  9, 30]), 'cur_cost': 24031.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([12, 10, 18, 16,  2, 27, 22,  0, 17, 28, 33, 31, 32, 24,  5, 29, 26,
        7,  3, 21,  1, 23, 25, 20,  6, 19, 15,  4, 13, 11,  8, 14,  9, 30]), 'cur_cost': 23197.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2, 10, 18, 16, 12, 27, 22,  0, 17, 28, 33, 31, 32, 24,  5, 29, 26,
        7,  3, 21,  1, 23, 25, 20,  6, 19, 15,  4, 13, 11,  8, 14,  9, 30]), 'cur_cost': 23295.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2, 12, 10, 18, 16, 27, 22,  0, 17, 28, 33, 31, 32, 24,  5, 29, 26,
        7,  3, 21,  1, 23, 25, 20,  6, 19, 15,  4, 13, 11,  8, 14,  9, 30]), 'cur_cost': 23251.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:49,336 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 22905.00)
2025-08-05 10:28:49,336 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:49,336 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:49,336 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:49,337 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 34
2025-08-05 10:28:49,338 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,338 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,338 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,338 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,338 - ExplorationExpert - INFO - 探索路径生成完成，成本: 26418.0, 路径长度: 34, 收集中间解: 3
2025-08-05 10:28:49,339 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [18, 22, 3, 32, 30, 0, 14, 9, 13, 28, 11, 29, 10, 33, 24, 26, 5, 6, 1, 25, 12, 20, 27, 16, 23, 8, 15, 4, 19, 2, 31, 17, 21, 7], 'cur_cost': 26418.0, 'intermediate_solutions': [{'tour': [9, 6, 26, 7, 2, 3, 11, 12, 23, 30, 19, 24, 33, 0, 20, 32, 21, 25, 18, 17, 27, 28, 4, 15, 8, 5, 10, 16, 14, 1, 29, 31, 22, 13], 'cur_cost': 17709.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 6, 0, 7, 2, 3, 11, 12, 23, 30, 19, 24, 33, 26, 20, 32, 21, 25, 18, 17, 27, 28, 4, 15, 8, 5, 10, 16, 31, 29, 1, 14, 22, 13], 'cur_cost': 15996.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 6, 0, 7, 2, 3, 11, 12, 23, 19, 30, 24, 33, 26, 20, 32, 21, 25, 18, 17, 27, 28, 4, 15, 8, 5, 10, 16, 14, 1, 29, 31, 22, 13], 'cur_cost': 14808.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:49,339 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 26418.00)
2025-08-05 10:28:49,339 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:49,339 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:49,341 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [6, 12, 11, 18, 7, 22, 2, 32, 30, 16, 4, 5, 14, 9, 8, 13, 1, 19, 29, 0, 3, 21, 23, 25, 17, 33, 31, 24, 15, 28, 20, 10, 27, 26], 'cur_cost': 22077.0, 'intermediate_solutions': [{'tour': [0, 14, 11, 17, 13, 15, 16, 10, 12, 9, 6, 5, 4, 3, 8, 7, 2, 1, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 20, 21, 19, 18], 'cur_cost': 6336.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 11, 14, 13, 15, 16, 10, 12, 9, 6, 5, 4, 3, 8, 7, 2, 1, 18, 19, 21, 20, 23, 22, 24, 27, 26, 25, 31, 32, 28, 29, 30, 33], 'cur_cost': 5926.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 11, 14, 13, 15, 16, 10, 12, 6, 5, 4, 3, 9, 8, 7, 2, 1, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 20, 21, 19, 18], 'cur_cost': 5997.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 5,  6, 15, 31, 23, 11, 13, 22, 32, 18,  4,  8,  1, 17,  3, 29, 16,
       12, 26,  7, 20, 27,  2, 25, 14, 30, 10, 24, 21, 19,  9, 28, 33,  0],
      dtype=int64), 'cur_cost': 23328.0, 'intermediate_solutions': [{'tour': array([25, 17,  6, 18,  7, 15,  2, 14,  8,  9, 11, 33, 13,  3, 12, 22, 27,
        5, 23,  0, 29, 21, 32, 16, 19, 26, 31, 24,  1, 28, 20,  4, 10, 30]), 'cur_cost': 26282.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([18, 25, 17,  6,  7, 15,  2, 14,  8,  9, 11, 33, 13,  3, 12, 22, 27,
        5, 23,  0, 29, 21, 32, 16, 19, 26, 31, 24,  1, 28, 20,  4, 10, 30]), 'cur_cost': 25493.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 7, 18, 25, 17,  6, 15,  2, 14,  8,  9, 11, 33, 13,  3, 12, 22, 27,
        5, 23,  0, 29, 21, 32, 16, 19, 26, 31, 24,  1, 28, 20,  4, 10, 30]), 'cur_cost': 26710.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6, 18, 25, 17,  7, 15,  2, 14,  8,  9, 11, 33, 13,  3, 12, 22, 27,
        5, 23,  0, 29, 21, 32, 16, 19, 26, 31, 24,  1, 28, 20,  4, 10, 30]), 'cur_cost': 26729.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6,  7, 18, 25, 17, 15,  2, 14,  8,  9, 11, 33, 13,  3, 12, 22, 27,
        5, 23,  0, 29, 21, 32, 16, 19, 26, 31, 24,  1, 28, 20,  4, 10, 30]), 'cur_cost': 25793.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([17, 16, 24,  0,  6, 12, 10,  9, 20, 23,  7, 27, 30,  5, 21, 32, 18,
       33, 25, 14, 26, 28, 22, 31, 19, 11,  2,  8,  4,  1,  3, 13, 15, 29],
      dtype=int64), 'cur_cost': 19515.0, 'intermediate_solutions': [{'tour': array([ 9, 12, 18, 23,  4, 22, 21,  7, 30, 32, 20,  0, 14, 17,  1, 13,  5,
       19, 29, 15,  3, 10, 11, 25,  6, 33, 26,  2, 24, 31,  8, 16, 27, 28]), 'cur_cost': 24804.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([23,  9, 12, 18,  4, 22, 21,  7, 30, 32, 20,  0, 14, 17,  1, 13,  5,
       19, 29, 15,  3, 10, 11, 25,  6, 33, 26,  2, 24, 31,  8, 16, 27, 28]), 'cur_cost': 25574.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 4, 23,  9, 12, 18, 22, 21,  7, 30, 32, 20,  0, 14, 17,  1, 13,  5,
       19, 29, 15,  3, 10, 11, 25,  6, 33, 26,  2, 24, 31,  8, 16, 27, 28]), 'cur_cost': 24777.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([18, 23,  9, 12,  4, 22, 21,  7, 30, 32, 20,  0, 14, 17,  1, 13,  5,
       19, 29, 15,  3, 10, 11, 25,  6, 33, 26,  2, 24, 31,  8, 16, 27, 28]), 'cur_cost': 24552.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([18,  4, 23,  9, 12, 22, 21,  7, 30, 32, 20,  0, 14, 17,  1, 13,  5,
       19, 29, 15,  3, 10, 11, 25,  6, 33, 26,  2, 24, 31,  8, 16, 27, 28]), 'cur_cost': 25617.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [18, 26, 7, 2, 31, 14, 13, 19, 29, 0, 3, 24, 6, 33, 1, 4, 28, 30, 20, 10, 12, 25, 17, 23, 16, 21, 8, 32, 9, 5, 15, 11, 27, 22], 'cur_cost': 21149.0, 'intermediate_solutions': [{'tour': [17, 18, 28, 26, 31, 30, 20, 14, 10, 6, 2, 4, 1, 9, 3, 12, 5, 8, 0, 33, 22, 19, 23, 24, 32, 29, 7, 11, 15, 16, 25, 21, 27, 13], 'cur_cost': 12961.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [17, 18, 28, 26, 31, 30, 20, 14, 10, 6, 2, 4, 1, 9, 3, 12, 5, 8, 0, 33, 22, 15, 11, 7, 29, 21, 24, 23, 19, 16, 25, 32, 27, 13], 'cur_cost': 13302.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [17, 18, 28, 26, 31, 30, 20, 10, 6, 2, 4, 1, 9, 14, 3, 12, 5, 8, 0, 33, 22, 19, 23, 24, 21, 29, 7, 11, 15, 16, 25, 32, 27, 13], 'cur_cost': 13038.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [6, 12, 7, 22, 2, 19, 3, 13, 25, 29, 23, 9, 33, 4, 28, 11, 24, 30, 5, 26, 17, 32, 31, 8, 27, 15, 16, 18, 1, 14, 20, 0, 21, 10], 'cur_cost': 27518.0, 'intermediate_solutions': [{'tour': [33, 3, 10, 17, 8, 19, 12, 32, 7, 30, 4, 1, 15, 26, 11, 2, 22, 16, 23, 18, 14, 29, 24, 13, 25, 20, 21, 6, 0, 5, 9, 31, 27, 28], 'cur_cost': 22610.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [33, 3, 10, 17, 8, 19, 12, 32, 7, 16, 4, 1, 15, 26, 11, 2, 22, 30, 23, 18, 14, 29, 24, 13, 25, 20, 21, 6, 0, 5, 27, 31, 9, 28], 'cur_cost': 23666.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [33, 3, 10, 17, 8, 19, 12, 32, 7, 16, 4, 1, 15, 26, 11, 2, 22, 23, 18, 14, 29, 24, 13, 25, 20, 21, 30, 6, 0, 5, 9, 31, 27, 28], 'cur_cost': 21138.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 23, 8, 3, 4, 5, 6, 7, 9, 2, 1, 16, 15, 13, 14, 10, 12, 11, 17, 22, 24, 19, 20, 18, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 5927.0, 'intermediate_solutions': [{'tour': [0, 22, 21, 11, 23, 17, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 7, 9, 2, 1, 16, 15, 13, 14, 10, 12, 24], 'cur_cost': 7376.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 22, 21, 24, 23, 17, 20, 18, 19, 25, 31, 30, 29, 28, 32, 9, 7, 6, 5, 4, 3, 8, 33, 27, 26, 2, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 6620.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 22, 21, 24, 23, 17, 20, 18, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 19, 3, 4, 5, 6, 7, 9, 2, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 6949.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 5, 15, 13, 14, 10, 12, 11, 16, 9, 6, 7, 8, 3, 4, 1, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 19, 18], 'cur_cost': 4803.0, 'intermediate_solutions': [{'tour': [0, 3, 19, 11, 13, 14, 15, 16, 10, 12, 9, 6, 5, 4, 1, 8, 7, 2, 21, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 33, 18], 'cur_cost': 8289.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 19, 11, 13, 14, 15, 16, 10, 12, 9, 6, 5, 4, 1, 8, 7, 2, 33, 30, 29, 28, 32, 31, 25, 24, 27, 26, 22, 23, 17, 20, 21, 18], 'cur_cost': 7288.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 19, 11, 13, 14, 15, 16, 10, 12, 9, 6, 5, 4, 1, 8, 7, 17, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 20, 21, 18], 'cur_cost': 8093.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [21, 18, 22, 2, 32, 30, 0, 3, 14, 8, 13, 19, 25, 4, 6, 33, 24, 11, 9, 28, 29, 10, 1, 12, 16, 26, 20, 15, 23, 7, 17, 31, 27, 5], 'cur_cost': 22132.0, 'intermediate_solutions': [{'tour': [0, 9, 19, 14, 13, 15, 16, 10, 12, 11, 7, 6, 5, 4, 3, 8, 1, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 18], 'cur_cost': 6070.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 19, 14, 13, 15, 16, 10, 12, 11, 9, 6, 5, 4, 3, 8, 1, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 21, 20, 17, 23, 18], 'cur_cost': 6052.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 19, 14, 13, 15, 16, 10, 12, 11, 9, 6, 5, 4, 3, 8, 2, 1, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 18], 'cur_cost': 6011.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 5, 29,  6, 31,  7, 26,  2, 19, 32, 27, 23, 33, 21, 15,  1, 18, 25,
       10, 14, 24, 22, 20, 12, 11, 30,  8,  3, 28,  0, 17, 16,  9, 13,  4],
      dtype=int64), 'cur_cost': 22905.0, 'intermediate_solutions': [{'tour': array([18, 16,  2, 10, 12, 27, 22,  0, 17, 28, 33, 31, 32, 24,  5, 29, 26,
        7,  3, 21,  1, 23, 25, 20,  6, 19, 15,  4, 13, 11,  8, 14,  9, 30]), 'cur_cost': 22973.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([10, 18, 16,  2, 12, 27, 22,  0, 17, 28, 33, 31, 32, 24,  5, 29, 26,
        7,  3, 21,  1, 23, 25, 20,  6, 19, 15,  4, 13, 11,  8, 14,  9, 30]), 'cur_cost': 24031.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([12, 10, 18, 16,  2, 27, 22,  0, 17, 28, 33, 31, 32, 24,  5, 29, 26,
        7,  3, 21,  1, 23, 25, 20,  6, 19, 15,  4, 13, 11,  8, 14,  9, 30]), 'cur_cost': 23197.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2, 10, 18, 16, 12, 27, 22,  0, 17, 28, 33, 31, 32, 24,  5, 29, 26,
        7,  3, 21,  1, 23, 25, 20,  6, 19, 15,  4, 13, 11,  8, 14,  9, 30]), 'cur_cost': 23295.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2, 12, 10, 18, 16, 27, 22,  0, 17, 28, 33, 31, 32, 24,  5, 29, 26,
        7,  3, 21,  1, 23, 25, 20,  6, 19, 15,  4, 13, 11,  8, 14,  9, 30]), 'cur_cost': 23251.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [18, 22, 3, 32, 30, 0, 14, 9, 13, 28, 11, 29, 10, 33, 24, 26, 5, 6, 1, 25, 12, 20, 27, 16, 23, 8, 15, 4, 19, 2, 31, 17, 21, 7], 'cur_cost': 26418.0, 'intermediate_solutions': [{'tour': [9, 6, 26, 7, 2, 3, 11, 12, 23, 30, 19, 24, 33, 0, 20, 32, 21, 25, 18, 17, 27, 28, 4, 15, 8, 5, 10, 16, 14, 1, 29, 31, 22, 13], 'cur_cost': 17709.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 6, 0, 7, 2, 3, 11, 12, 23, 30, 19, 24, 33, 26, 20, 32, 21, 25, 18, 17, 27, 28, 4, 15, 8, 5, 10, 16, 31, 29, 1, 14, 22, 13], 'cur_cost': 15996.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 6, 0, 7, 2, 3, 11, 12, 23, 19, 30, 24, 33, 26, 20, 32, 21, 25, 18, 17, 27, 28, 4, 15, 8, 5, 10, 16, 14, 1, 29, 31, 22, 13], 'cur_cost': 14808.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:49,342 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:49,342 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:49,344 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=4803.000, 多样性=0.964
2025-08-05 10:28:49,344 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 10:28:49,344 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 10:28:49,344 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:49,345 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.11912923982349037, 'best_improvement': 0.003940273745333886}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.03727144866385359}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.044059157933826525, 'recent_improvements': [-0.13499026312930193, -0.0024406706036040355, -0.04687194726164887], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 11, 'new_count': 11, 'count_change': 0, 'old_best_cost': 3575, 'new_best_cost': 3575, 'quality_improvement': 0.0, 'old_diversity': 0.5796791443850268, 'new_diversity': 0.5796791443850268, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:49,346 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 10:28:49,352 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite2_34_solution.json
2025-08-05 10:28:49,352 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite2_34_20250805_102849.solution
2025-08-05 10:28:49,352 - __main__ - INFO - 实例执行完成 - 运行时间: 1.57s, 最佳成本: 3575
2025-08-05 10:28:49,352 - __main__ - INFO - 实例 composite2_34 处理完成
