2025-08-01 17:06:30,626 - __main__ - INFO - simple1_9 开始进化第 1 代
2025-08-01 17:06:30,627 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-01 17:06:30,628 - StatsExpert - INFO - 开始统计分析
2025-08-01 17:06:30,629 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=681.000, 多样性=0.874
2025-08-01 17:06:30,630 - PathExpert - INFO - 开始路径结构分析
2025-08-01 17:06:30,631 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.874
2025-08-01 17:06:30,632 - EliteExpert - INFO - 开始精英解分析
2025-08-01 17:06:30,634 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/3)
2025-08-01 17:06:30,635 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-01 17:06:30,635 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-01 17:06:30,635 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-01 17:06:30,851 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-01 17:06:30,851 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-01 17:06:30,929 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记
2025-08-01 17:06:31,257 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_1_20250801_170631.html
2025-08-01 17:06:31,312 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_1_20250801_170631.html
2025-08-01 17:06:31,312 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-01 17:06:31,313 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-01 17:06:31,313 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.6793秒
2025-08-01 17:06:31,313 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-01 17:06:31,314 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 3, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754039190.8514888, 'performance_metrics': {}}}
2025-08-01 17:06:31,314 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-01 17:06:31,314 - StrategyExpert - INFO - 开始策略分配分析
2025-08-01 17:06:31,315 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 681.0
  • mean_cost: 983.8
- Search-space: ruggedness 0.5, modality uni-modal, deceptiveness low
- Evolution phase: exploration
- Landscape focus suggestion: exploration
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 1): [{"type": "unexplored_space", "potential": "high", "description": "未充分探索的搜索空间", "recommendation": "增加探索性操作"}]
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-08-01 17:06:31,317 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-08-01 17:06:31,317 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-01 17:06:32,945 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.80,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High unexplored space potential and exploration phase.  Bias heavily towards exploration to discover new regions. Increased density towards the best individuals."
}
```
2025-08-01 17:06:32,946 - utils.utils - INFO - 成功从JSON中提取数据
2025-08-01 17:06:32,946 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-08-01 17:06:32,947 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-08-01 17:06:32,947 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.80,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High unexplored space potential and exploration phase.  Bias heavily towards exploration to discover new regions. Increased density towards the best individuals."
}
```
2025-08-01 17:06:32,947 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-01 17:06:32,948 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-08-01 17:06:32,948 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.80,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High unexplored space potential and exploration phase.  Bias heavily towards exploration to discover new regions. Increased density towards the best individuals."
}
```
2025-08-01 17:06:32,949 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-01 17:06:32,950 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-01 17:06:32,950 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-01 17:06:32,950 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:06:32,951 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-01 17:06:32,951 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:06:33,143 - ExplorationExpert - INFO - 探索路径生成完成，成本: 804.0, 路径长度: 9
2025-08-01 17:06:33,143 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [3, 7, 5, 0, 1, 4, 2, 8, 6], 'cur_cost': 804.0}
2025-08-01 17:06:33,144 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-01 17:06:33,144 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-01 17:06:33,144 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:06:33,145 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-01 17:06:33,145 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:06:33,145 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1034.0, 路径长度: 9
2025-08-01 17:06:33,145 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [5, 1, 6, 3, 4, 0, 7, 8, 2], 'cur_cost': 1034.0}
2025-08-01 17:06:33,146 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-01 17:06:33,146 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-01 17:06:33,146 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:06:33,146 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-01 17:06:33,147 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:06:33,147 - ExplorationExpert - INFO - 探索路径生成完成，成本: 832.0, 路径长度: 9
2025-08-01 17:06:33,147 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 2, 4, 5, 6, 7, 3, 8, 1], 'cur_cost': 832.0}
2025-08-01 17:06:33,147 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-01 17:06:33,147 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-01 17:06:33,147 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:06:33,148 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-01 17:06:33,148 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:06:33,148 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1079.0, 路径长度: 9
2025-08-01 17:06:33,149 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 2, 5, 3, 1, 7, 8, 4, 6], 'cur_cost': 1079.0}
2025-08-01 17:06:33,149 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-01 17:06:33,149 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-01 17:06:33,149 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:06:33,150 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-01 17:06:33,151 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:06:33,151 - ExplorationExpert - INFO - 探索路径生成完成，成本: 852.0, 路径长度: 9
2025-08-01 17:06:33,151 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [1, 2, 6, 7, 3, 5, 8, 4, 0], 'cur_cost': 852.0}
2025-08-01 17:06:33,151 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-01 17:06:33,152 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-01 17:06:33,152 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:06:33,152 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-01 17:06:33,152 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:06:33,153 - ExplorationExpert - INFO - 探索路径生成完成，成本: 914.0, 路径长度: 9
2025-08-01 17:06:33,153 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [2, 7, 6, 1, 8, 3, 5, 0, 4], 'cur_cost': 914.0}
2025-08-01 17:06:33,153 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-01 17:06:33,153 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-01 17:06:33,154 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:06:33,154 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-01 17:06:33,154 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:06:33,155 - ExplorationExpert - INFO - 探索路径生成完成，成本: 815.0, 路径长度: 9
2025-08-01 17:06:33,155 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [5, 3, 6, 7, 8, 4, 2, 0, 1], 'cur_cost': 815.0}
2025-08-01 17:06:33,155 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-01 17:06:33,155 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:06:33,157 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:06:33,157 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 1139.0
2025-08-01 17:06:35,191 - ExploitationExpert - INFO - res_population_num: 1
2025-08-01 17:06:35,191 - ExploitationExpert - INFO - res_population_costs: [818.0]
2025-08-01 17:06:35,191 - ExploitationExpert - INFO - res_populations: [array([0, 7, 5, 2, 4, 8, 3, 6, 1], dtype=int64)]
2025-08-01 17:06:35,192 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:06:35,192 - ExploitationExpert - INFO - populations: [{'tour': [3, 7, 5, 0, 1, 4, 2, 8, 6], 'cur_cost': 804.0}, {'tour': [5, 1, 6, 3, 4, 0, 7, 8, 2], 'cur_cost': 1034.0}, {'tour': [0, 2, 4, 5, 6, 7, 3, 8, 1], 'cur_cost': 832.0}, {'tour': [0, 2, 5, 3, 1, 7, 8, 4, 6], 'cur_cost': 1079.0}, {'tour': [1, 2, 6, 7, 3, 5, 8, 4, 0], 'cur_cost': 852.0}, {'tour': [2, 7, 6, 1, 8, 3, 5, 0, 4], 'cur_cost': 914.0}, {'tour': [5, 3, 6, 7, 8, 4, 2, 0, 1], 'cur_cost': 815.0}, {'tour': array([7, 5, 4, 3, 0, 6, 8, 1, 2], dtype=int64), 'cur_cost': 1139.0}, {'tour': array([2, 3, 8, 6, 1, 4, 7, 0, 5], dtype=int64), 'cur_cost': 1072.0}, {'tour': array([5, 3, 1, 4, 2, 8, 6, 7, 0], dtype=int64), 'cur_cost': 922.0}]
2025-08-01 17:06:35,193 - ExploitationExpert - INFO - 局部搜索耗时: 2.04秒
2025-08-01 17:06:35,193 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-01 17:06:35,194 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([7, 5, 4, 3, 0, 6, 8, 1, 2], dtype=int64), 'cur_cost': 1139.0}
2025-08-01 17:06:35,194 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-01 17:06:35,194 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:06:35,194 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:06:35,195 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 998.0
2025-08-01 17:06:37,527 - ExploitationExpert - INFO - res_population_num: 2
2025-08-01 17:06:37,528 - ExploitationExpert - INFO - res_population_costs: [818.0, 680.0]
2025-08-01 17:06:37,528 - ExploitationExpert - INFO - res_populations: [array([0, 7, 5, 2, 4, 8, 3, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-01 17:06:37,529 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:06:37,529 - ExploitationExpert - INFO - populations: [{'tour': [3, 7, 5, 0, 1, 4, 2, 8, 6], 'cur_cost': 804.0}, {'tour': [5, 1, 6, 3, 4, 0, 7, 8, 2], 'cur_cost': 1034.0}, {'tour': [0, 2, 4, 5, 6, 7, 3, 8, 1], 'cur_cost': 832.0}, {'tour': [0, 2, 5, 3, 1, 7, 8, 4, 6], 'cur_cost': 1079.0}, {'tour': [1, 2, 6, 7, 3, 5, 8, 4, 0], 'cur_cost': 852.0}, {'tour': [2, 7, 6, 1, 8, 3, 5, 0, 4], 'cur_cost': 914.0}, {'tour': [5, 3, 6, 7, 8, 4, 2, 0, 1], 'cur_cost': 815.0}, {'tour': array([7, 5, 4, 3, 0, 6, 8, 1, 2], dtype=int64), 'cur_cost': 1139.0}, {'tour': array([8, 4, 0, 5, 6, 7, 1, 3, 2], dtype=int64), 'cur_cost': 998.0}, {'tour': array([5, 3, 1, 4, 2, 8, 6, 7, 0], dtype=int64), 'cur_cost': 922.0}]
2025-08-01 17:06:37,530 - ExploitationExpert - INFO - 局部搜索耗时: 2.34秒
2025-08-01 17:06:37,530 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-01 17:06:37,530 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([8, 4, 0, 5, 6, 7, 1, 3, 2], dtype=int64), 'cur_cost': 998.0}
2025-08-01 17:06:37,531 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-01 17:06:37,531 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:06:37,531 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:06:37,532 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 1028.0
2025-08-01 17:06:38,119 - ExploitationExpert - INFO - res_population_num: 2
2025-08-01 17:06:38,119 - ExploitationExpert - INFO - res_population_costs: [818.0, 680.0]
2025-08-01 17:06:38,120 - ExploitationExpert - INFO - res_populations: [array([0, 7, 5, 2, 4, 8, 3, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-01 17:06:38,120 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:06:38,121 - ExploitationExpert - INFO - populations: [{'tour': [3, 7, 5, 0, 1, 4, 2, 8, 6], 'cur_cost': 804.0}, {'tour': [5, 1, 6, 3, 4, 0, 7, 8, 2], 'cur_cost': 1034.0}, {'tour': [0, 2, 4, 5, 6, 7, 3, 8, 1], 'cur_cost': 832.0}, {'tour': [0, 2, 5, 3, 1, 7, 8, 4, 6], 'cur_cost': 1079.0}, {'tour': [1, 2, 6, 7, 3, 5, 8, 4, 0], 'cur_cost': 852.0}, {'tour': [2, 7, 6, 1, 8, 3, 5, 0, 4], 'cur_cost': 914.0}, {'tour': [5, 3, 6, 7, 8, 4, 2, 0, 1], 'cur_cost': 815.0}, {'tour': array([7, 5, 4, 3, 0, 6, 8, 1, 2], dtype=int64), 'cur_cost': 1139.0}, {'tour': array([8, 4, 0, 5, 6, 7, 1, 3, 2], dtype=int64), 'cur_cost': 998.0}, {'tour': array([6, 4, 2, 7, 1, 8, 0, 3, 5], dtype=int64), 'cur_cost': 1028.0}]
2025-08-01 17:06:38,122 - ExploitationExpert - INFO - 局部搜索耗时: 0.59秒
2025-08-01 17:06:38,122 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-01 17:06:38,123 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([6, 4, 2, 7, 1, 8, 0, 3, 5], dtype=int64), 'cur_cost': 1028.0}
2025-08-01 17:06:38,123 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [3, 7, 5, 0, 1, 4, 2, 8, 6], 'cur_cost': 804.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [5, 1, 6, 3, 4, 0, 7, 8, 2], 'cur_cost': 1034.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 4, 5, 6, 7, 3, 8, 1], 'cur_cost': 832.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 5, 3, 1, 7, 8, 4, 6], 'cur_cost': 1079.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [1, 2, 6, 7, 3, 5, 8, 4, 0], 'cur_cost': 852.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [2, 7, 6, 1, 8, 3, 5, 0, 4], 'cur_cost': 914.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [5, 3, 6, 7, 8, 4, 2, 0, 1], 'cur_cost': 815.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 5, 4, 3, 0, 6, 8, 1, 2], dtype=int64), 'cur_cost': 1139.0}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 4, 0, 5, 6, 7, 1, 3, 2], dtype=int64), 'cur_cost': 998.0}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 4, 2, 7, 1, 8, 0, 3, 5], dtype=int64), 'cur_cost': 1028.0}}]
2025-08-01 17:06:38,124 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-01 17:06:38,124 - StatsExpert - INFO - 开始统计分析
2025-08-01 17:06:38,125 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=804.000, 多样性=0.886
2025-08-01 17:06:38,125 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-01 17:06:38,125 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-01 17:06:38,125 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-01 17:06:38,126 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 3, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.017773883760976005, 'best_improvement': -0.18061674008810572}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.014124293785310799}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6666666666666666, 'new_diversity': 0.6666666666666666, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-01 17:06:38,126 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-01 17:06:38,126 - __main__ - INFO - simple1_9 开始进化第 2 代
2025-08-01 17:06:38,127 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-01 17:06:38,127 - StatsExpert - INFO - 开始统计分析
2025-08-01 17:06:38,127 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=804.000, 多样性=0.886
2025-08-01 17:06:38,128 - PathExpert - INFO - 开始路径结构分析
2025-08-01 17:06:38,128 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.886
2025-08-01 17:06:38,129 - EliteExpert - INFO - 开始精英解分析
2025-08-01 17:06:38,129 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.667
2025-08-01 17:06:38,131 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/3)
2025-08-01 17:06:38,131 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-01 17:06:38,132 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-01 17:06:38,132 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-01 17:06:38,143 - core.algorithms.fitness_landscape_analyzer - ERROR - 景观分析失败: '>' not supported between instances of 'str' and 'int'
2025-08-01 17:06:38,144 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-01 17:06:38,145 - LandscapeExpert - INFO - 提取到 2 个精英解
2025-08-01 17:06:38,179 - visualization.landscape_visualizer - INFO - 已添加 2 个精英解标记
2025-08-01 17:06:38,264 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_2_20250801_170638.html
2025-08-01 17:06:38,320 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_2_20250801_170638.html
2025-08-01 17:06:38,320 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 2
2025-08-01 17:06:38,321 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-01 17:06:38,321 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1897秒
2025-08-01 17:06:38,321 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 1, 'total': 3, 'progress': 0.3333333333333333}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754039198.1444416, 'performance_metrics': {}}}
2025-08-01 17:06:38,322 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-01 17:06:38,322 - StrategyExpert - INFO - 开始策略分配分析
2025-08-01 17:06:38,322 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 804.0
  • mean_cost: 949.5
- Search-space: ruggedness 0.5, modality uni-modal, deceptiveness low
- Evolution phase: exploitation
- Landscape focus suggestion: exploitation
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 1): [{"type": "unexplored_space", "potential": "high", "description": "未充分探索的搜索空间", "recommendation": "增加探索性操作"}]
- Last-iteration feedback: {'overall_score': 40, 'iteration': 0, 'total_iterations': 3, 'cost_improvement': {'status': 'deterioration', 'improvemen...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-08-01 17:06:38,324 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-08-01 17:06:38,324 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-01 17:06:39,948 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "exploit",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Exploitation phase with some deterioration. High potential unexplored space needs exploration. Balance explore and exploit."
}
```
2025-08-01 17:06:39,948 - utils.utils - INFO - 成功从JSON中提取数据
2025-08-01 17:06:39,949 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-08-01 17:06:39,949 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-08-01 17:06:39,950 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "exploit",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Exploitation phase with some deterioration. High potential unexplored space needs exploration. Balance explore and exploit."
}
```
2025-08-01 17:06:39,951 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-01 17:06:39,951 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-08-01 17:06:39,951 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "exploit",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Exploitation phase with some deterioration. High potential unexplored space needs exploration. Balance explore and exploit."
}
```
2025-08-01 17:06:39,952 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-01 17:06:39,952 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-01 17:06:39,952 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-01 17:06:39,952 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:06:39,953 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-01 17:06:39,953 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:06:39,953 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1043.0, 路径长度: 9
2025-08-01 17:06:39,954 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [3, 1, 0, 5, 4, 6, 7, 8, 2], 'cur_cost': 1043.0}
2025-08-01 17:06:39,954 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-01 17:06:39,954 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-01 17:06:39,954 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:06:39,955 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-01 17:06:39,955 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:06:39,955 - ExplorationExpert - INFO - 探索路径生成完成，成本: 820.0, 路径长度: 9
2025-08-01 17:06:39,955 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [3, 7, 5, 1, 0, 4, 2, 8, 6], 'cur_cost': 820.0}
2025-08-01 17:06:39,955 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-01 17:06:39,956 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-01 17:06:39,956 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:06:39,956 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-01 17:06:39,956 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:06:39,957 - ExplorationExpert - INFO - 探索路径生成完成，成本: 950.0, 路径长度: 9
2025-08-01 17:06:39,957 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [5, 7, 0, 6, 8, 3, 4, 2, 1], 'cur_cost': 950.0}
2025-08-01 17:06:39,957 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-01 17:06:39,957 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:06:39,957 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:06:39,958 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 885.0
2025-08-01 17:06:40,008 - ExploitationExpert - INFO - res_population_num: 4
2025-08-01 17:06:40,008 - ExploitationExpert - INFO - res_population_costs: [680.0, 818.0, 680, 680]
2025-08-01 17:06:40,009 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 5, 2, 4, 8, 3, 6, 1], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64)]
2025-08-01 17:06:40,010 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:06:40,010 - ExploitationExpert - INFO - populations: [{'tour': [3, 1, 0, 5, 4, 6, 7, 8, 2], 'cur_cost': 1043.0}, {'tour': [3, 7, 5, 1, 0, 4, 2, 8, 6], 'cur_cost': 820.0}, {'tour': [5, 7, 0, 6, 8, 3, 4, 2, 1], 'cur_cost': 950.0}, {'tour': array([3, 8, 5, 2, 4, 7, 6, 0, 1], dtype=int64), 'cur_cost': 885.0}, {'tour': [1, 2, 6, 7, 3, 5, 8, 4, 0], 'cur_cost': 852.0}, {'tour': [2, 7, 6, 1, 8, 3, 5, 0, 4], 'cur_cost': 914.0}, {'tour': [5, 3, 6, 7, 8, 4, 2, 0, 1], 'cur_cost': 815.0}, {'tour': [7, 5, 4, 3, 0, 6, 8, 1, 2], 'cur_cost': 1139.0}, {'tour': [8, 4, 0, 5, 6, 7, 1, 3, 2], 'cur_cost': 998.0}, {'tour': [6, 4, 2, 7, 1, 8, 0, 3, 5], 'cur_cost': 1028.0}]
2025-08-01 17:06:40,011 - ExploitationExpert - INFO - 局部搜索耗时: 0.05秒
2025-08-01 17:06:40,013 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-01 17:06:40,013 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([3, 8, 5, 2, 4, 7, 6, 0, 1], dtype=int64), 'cur_cost': 885.0}
2025-08-01 17:06:40,013 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-01 17:06:40,013 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:06:40,013 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:06:40,013 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 778.0
2025-08-01 17:06:40,068 - ExploitationExpert - INFO - res_population_num: 4
2025-08-01 17:06:40,068 - ExploitationExpert - INFO - res_population_costs: [680.0, 818.0, 680, 680]
2025-08-01 17:06:40,068 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 5, 2, 4, 8, 3, 6, 1], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64)]
2025-08-01 17:06:40,070 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:06:40,070 - ExploitationExpert - INFO - populations: [{'tour': [3, 1, 0, 5, 4, 6, 7, 8, 2], 'cur_cost': 1043.0}, {'tour': [3, 7, 5, 1, 0, 4, 2, 8, 6], 'cur_cost': 820.0}, {'tour': [5, 7, 0, 6, 8, 3, 4, 2, 1], 'cur_cost': 950.0}, {'tour': array([3, 8, 5, 2, 4, 7, 6, 0, 1], dtype=int64), 'cur_cost': 885.0}, {'tour': array([4, 0, 7, 1, 6, 5, 3, 8, 2], dtype=int64), 'cur_cost': 778.0}, {'tour': [2, 7, 6, 1, 8, 3, 5, 0, 4], 'cur_cost': 914.0}, {'tour': [5, 3, 6, 7, 8, 4, 2, 0, 1], 'cur_cost': 815.0}, {'tour': [7, 5, 4, 3, 0, 6, 8, 1, 2], 'cur_cost': 1139.0}, {'tour': [8, 4, 0, 5, 6, 7, 1, 3, 2], 'cur_cost': 998.0}, {'tour': [6, 4, 2, 7, 1, 8, 0, 3, 5], 'cur_cost': 1028.0}]
2025-08-01 17:06:40,075 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-08-01 17:06:40,076 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-01 17:06:40,076 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([4, 0, 7, 1, 6, 5, 3, 8, 2], dtype=int64), 'cur_cost': 778.0}
2025-08-01 17:06:40,077 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-01 17:06:40,077 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:06:40,077 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:06:40,078 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 1085.0
2025-08-01 17:06:40,131 - ExploitationExpert - INFO - res_population_num: 4
2025-08-01 17:06:40,131 - ExploitationExpert - INFO - res_population_costs: [680.0, 818.0, 680, 680]
2025-08-01 17:06:40,131 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 5, 2, 4, 8, 3, 6, 1], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64)]
2025-08-01 17:06:40,133 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:06:40,133 - ExploitationExpert - INFO - populations: [{'tour': [3, 1, 0, 5, 4, 6, 7, 8, 2], 'cur_cost': 1043.0}, {'tour': [3, 7, 5, 1, 0, 4, 2, 8, 6], 'cur_cost': 820.0}, {'tour': [5, 7, 0, 6, 8, 3, 4, 2, 1], 'cur_cost': 950.0}, {'tour': array([3, 8, 5, 2, 4, 7, 6, 0, 1], dtype=int64), 'cur_cost': 885.0}, {'tour': array([4, 0, 7, 1, 6, 5, 3, 8, 2], dtype=int64), 'cur_cost': 778.0}, {'tour': array([4, 7, 8, 2, 1, 5, 0, 3, 6], dtype=int64), 'cur_cost': 1085.0}, {'tour': [5, 3, 6, 7, 8, 4, 2, 0, 1], 'cur_cost': 815.0}, {'tour': [7, 5, 4, 3, 0, 6, 8, 1, 2], 'cur_cost': 1139.0}, {'tour': [8, 4, 0, 5, 6, 7, 1, 3, 2], 'cur_cost': 998.0}, {'tour': [6, 4, 2, 7, 1, 8, 0, 3, 5], 'cur_cost': 1028.0}]
2025-08-01 17:06:40,136 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-08-01 17:06:40,136 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-01 17:06:40,137 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([4, 7, 8, 2, 1, 5, 0, 3, 6], dtype=int64), 'cur_cost': 1085.0}
2025-08-01 17:06:40,140 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-01 17:06:40,141 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-01 17:06:40,142 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:06:40,142 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-01 17:06:40,143 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:06:40,143 - ExplorationExpert - INFO - 探索路径生成完成，成本: 851.0, 路径长度: 9
2025-08-01 17:06:40,144 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [6, 0, 1, 4, 7, 3, 5, 8, 2], 'cur_cost': 851.0}
2025-08-01 17:06:40,144 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-01 17:06:40,144 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:06:40,144 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:06:40,145 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 1159.0
2025-08-01 17:06:40,198 - ExploitationExpert - INFO - res_population_num: 4
2025-08-01 17:06:40,198 - ExploitationExpert - INFO - res_population_costs: [680.0, 818.0, 680, 680]
2025-08-01 17:06:40,199 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 5, 2, 4, 8, 3, 6, 1], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64)]
2025-08-01 17:06:40,201 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:06:40,202 - ExploitationExpert - INFO - populations: [{'tour': [3, 1, 0, 5, 4, 6, 7, 8, 2], 'cur_cost': 1043.0}, {'tour': [3, 7, 5, 1, 0, 4, 2, 8, 6], 'cur_cost': 820.0}, {'tour': [5, 7, 0, 6, 8, 3, 4, 2, 1], 'cur_cost': 950.0}, {'tour': array([3, 8, 5, 2, 4, 7, 6, 0, 1], dtype=int64), 'cur_cost': 885.0}, {'tour': array([4, 0, 7, 1, 6, 5, 3, 8, 2], dtype=int64), 'cur_cost': 778.0}, {'tour': array([4, 7, 8, 2, 1, 5, 0, 3, 6], dtype=int64), 'cur_cost': 1085.0}, {'tour': [6, 0, 1, 4, 7, 3, 5, 8, 2], 'cur_cost': 851.0}, {'tour': array([8, 7, 1, 2, 6, 4, 3, 0, 5], dtype=int64), 'cur_cost': 1159.0}, {'tour': [8, 4, 0, 5, 6, 7, 1, 3, 2], 'cur_cost': 998.0}, {'tour': [6, 4, 2, 7, 1, 8, 0, 3, 5], 'cur_cost': 1028.0}]
2025-08-01 17:06:40,209 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-08-01 17:06:40,209 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-01 17:06:40,209 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([8, 7, 1, 2, 6, 4, 3, 0, 5], dtype=int64), 'cur_cost': 1159.0}
2025-08-01 17:06:40,210 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-01 17:06:40,210 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-01 17:06:40,211 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:06:40,211 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-01 17:06:40,212 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:06:40,212 - ExplorationExpert - INFO - 探索路径生成完成，成本: 727.0, 路径长度: 9
2025-08-01 17:06:40,212 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [4, 0, 1, 7, 5, 6, 3, 8, 2], 'cur_cost': 727.0}
2025-08-01 17:06:40,213 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-01 17:06:40,213 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:06:40,213 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:06:40,214 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 1038.0
2025-08-01 17:06:40,267 - ExploitationExpert - INFO - res_population_num: 4
2025-08-01 17:06:40,267 - ExploitationExpert - INFO - res_population_costs: [680.0, 818.0, 680, 680]
2025-08-01 17:06:40,269 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 5, 2, 4, 8, 3, 6, 1], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64)]
2025-08-01 17:06:40,271 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:06:40,272 - ExploitationExpert - INFO - populations: [{'tour': [3, 1, 0, 5, 4, 6, 7, 8, 2], 'cur_cost': 1043.0}, {'tour': [3, 7, 5, 1, 0, 4, 2, 8, 6], 'cur_cost': 820.0}, {'tour': [5, 7, 0, 6, 8, 3, 4, 2, 1], 'cur_cost': 950.0}, {'tour': array([3, 8, 5, 2, 4, 7, 6, 0, 1], dtype=int64), 'cur_cost': 885.0}, {'tour': array([4, 0, 7, 1, 6, 5, 3, 8, 2], dtype=int64), 'cur_cost': 778.0}, {'tour': array([4, 7, 8, 2, 1, 5, 0, 3, 6], dtype=int64), 'cur_cost': 1085.0}, {'tour': [6, 0, 1, 4, 7, 3, 5, 8, 2], 'cur_cost': 851.0}, {'tour': array([8, 7, 1, 2, 6, 4, 3, 0, 5], dtype=int64), 'cur_cost': 1159.0}, {'tour': [4, 0, 1, 7, 5, 6, 3, 8, 2], 'cur_cost': 727.0}, {'tour': array([5, 7, 0, 3, 4, 2, 6, 1, 8], dtype=int64), 'cur_cost': 1038.0}]
2025-08-01 17:06:40,278 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-08-01 17:06:40,278 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-08-01 17:06:40,279 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([5, 7, 0, 3, 4, 2, 6, 1, 8], dtype=int64), 'cur_cost': 1038.0}
2025-08-01 17:06:40,280 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [3, 1, 0, 5, 4, 6, 7, 8, 2], 'cur_cost': 1043.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [3, 7, 5, 1, 0, 4, 2, 8, 6], 'cur_cost': 820.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [5, 7, 0, 6, 8, 3, 4, 2, 1], 'cur_cost': 950.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 8, 5, 2, 4, 7, 6, 0, 1], dtype=int64), 'cur_cost': 885.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 0, 7, 1, 6, 5, 3, 8, 2], dtype=int64), 'cur_cost': 778.0}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 7, 8, 2, 1, 5, 0, 3, 6], dtype=int64), 'cur_cost': 1085.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [6, 0, 1, 4, 7, 3, 5, 8, 2], 'cur_cost': 851.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 7, 1, 2, 6, 4, 3, 0, 5], dtype=int64), 'cur_cost': 1159.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [4, 0, 1, 7, 5, 6, 3, 8, 2], 'cur_cost': 727.0}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([5, 7, 0, 3, 4, 2, 6, 1, 8], dtype=int64), 'cur_cost': 1038.0}}]
2025-08-01 17:06:40,281 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-01 17:06:40,281 - StatsExpert - INFO - 开始统计分析
2025-08-01 17:06:40,283 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=727.000, 多样性=0.847
2025-08-01 17:06:40,284 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-01 17:06:40,285 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-01 17:06:40,285 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-01 17:06:40,286 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 1, 'total_iterations': 3, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.03165251245861575, 'best_improvement': 0.09577114427860696}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.044568245125348085}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.7037037037037037, 'new_diversity': 0.7037037037037037, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-01 17:06:40,287 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-01 17:06:40,287 - __main__ - INFO - simple1_9 开始进化第 3 代
2025-08-01 17:06:40,288 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-01 17:06:40,288 - StatsExpert - INFO - 开始统计分析
2025-08-01 17:06:40,288 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=727.000, 多样性=0.847
2025-08-01 17:06:40,289 - PathExpert - INFO - 开始路径结构分析
2025-08-01 17:06:40,289 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.847
2025-08-01 17:06:40,290 - EliteExpert - INFO - 开始精英解分析
2025-08-01 17:06:40,291 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.704
2025-08-01 17:06:40,292 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/3)
2025-08-01 17:06:40,293 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-01 17:06:40,293 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-01 17:06:40,293 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-01 17:06:40,306 - core.algorithms.fitness_landscape_analyzer - ERROR - 景观分析失败: '>' not supported between instances of 'str' and 'int'
2025-08-01 17:06:40,308 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-01 17:06:40,309 - LandscapeExpert - INFO - 提取到 4 个精英解
2025-08-01 17:06:40,323 - visualization.landscape_visualizer - INFO - 已添加 4 个精英解标记
2025-08-01 17:06:40,414 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_3_20250801_170640.html
2025-08-01 17:06:40,462 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_3_20250801_170640.html
2025-08-01 17:06:40,463 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 3
2025-08-01 17:06:40,463 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-01 17:06:40,463 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1710秒
2025-08-01 17:06:40,464 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 3, 'progress': 0.6666666666666666}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754039200.3081036, 'performance_metrics': {}}}
2025-08-01 17:06:40,464 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-01 17:06:40,464 - StrategyExpert - INFO - 开始策略分配分析
2025-08-01 17:06:40,464 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 727.0
  • mean_cost: 933.6
- Search-space: ruggedness 0.5, modality uni-modal, deceptiveness low
- Evolution phase: exploitation
- Landscape focus suggestion: exploitation
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 1): [{"type": "unexplored_space", "potential": "high", "description": "未充分探索的搜索空间", "recommendation": "增加探索性操作"}]
- Last-iteration feedback: {'overall_score': 100, 'iteration': 1, 'total_iterations': 3, 'cost_improvement': {'status': 'significant_improvement', ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-08-01 17:06:40,467 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-08-01 17:06:40,467 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-01 17:06:41,985 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.3,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "exploit",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Exploitation is favored as the landscape is exploitable, even though some unexplored space remains."
}
```
2025-08-01 17:06:41,987 - utils.utils - INFO - 成功从JSON中提取数据
2025-08-01 17:06:41,987 - StrategyExpert - INFO - 策略分配完成: ['exploit', 'exploit', 'exploit', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit', 'exploit']
2025-08-01 17:06:41,987 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'exploit', 'exploit', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit', 'exploit']
2025-08-01 17:06:41,988 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.3,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "exploit",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Exploitation is favored as the landscape is exploitable, even though some unexplored space remains."
}
```
2025-08-01 17:06:41,988 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-01 17:06:41,989 - __main__ - INFO - 策略分配: ['exploit', 'exploit', 'exploit', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit', 'exploit']
2025-08-01 17:06:41,989 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.3,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "exploit",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Exploitation is favored as the landscape is exploitable, even though some unexplored space remains."
}
```
2025-08-01 17:06:41,990 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-01 17:06:41,990 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-01 17:06:41,990 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:06:41,990 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:06:41,990 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 973.0
2025-08-01 17:06:42,044 - ExploitationExpert - INFO - res_population_num: 4
2025-08-01 17:06:42,044 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680, 818.0]
2025-08-01 17:06:42,044 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 7, 5, 2, 4, 8, 3, 6, 1], dtype=int64)]
2025-08-01 17:06:42,046 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:06:42,046 - ExploitationExpert - INFO - populations: [{'tour': array([8, 7, 5, 1, 6, 2, 4, 0, 3], dtype=int64), 'cur_cost': 973.0}, {'tour': [3, 7, 5, 1, 0, 4, 2, 8, 6], 'cur_cost': 820.0}, {'tour': [5, 7, 0, 6, 8, 3, 4, 2, 1], 'cur_cost': 950.0}, {'tour': [3, 8, 5, 2, 4, 7, 6, 0, 1], 'cur_cost': 885.0}, {'tour': [4, 0, 7, 1, 6, 5, 3, 8, 2], 'cur_cost': 778.0}, {'tour': [4, 7, 8, 2, 1, 5, 0, 3, 6], 'cur_cost': 1085.0}, {'tour': [6, 0, 1, 4, 7, 3, 5, 8, 2], 'cur_cost': 851.0}, {'tour': [8, 7, 1, 2, 6, 4, 3, 0, 5], 'cur_cost': 1159.0}, {'tour': [4, 0, 1, 7, 5, 6, 3, 8, 2], 'cur_cost': 727.0}, {'tour': [5, 7, 0, 3, 4, 2, 6, 1, 8], 'cur_cost': 1038.0}]
2025-08-01 17:06:42,047 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-08-01 17:06:42,047 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-08-01 17:06:42,047 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([8, 7, 5, 1, 6, 2, 4, 0, 3], dtype=int64), 'cur_cost': 973.0}
2025-08-01 17:06:42,047 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-01 17:06:42,048 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:06:42,048 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:06:42,048 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 1141.0
2025-08-01 17:06:42,103 - ExploitationExpert - INFO - res_population_num: 4
2025-08-01 17:06:42,104 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680, 818.0]
2025-08-01 17:06:42,105 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 7, 5, 2, 4, 8, 3, 6, 1], dtype=int64)]
2025-08-01 17:06:42,109 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:06:42,109 - ExploitationExpert - INFO - populations: [{'tour': array([8, 7, 5, 1, 6, 2, 4, 0, 3], dtype=int64), 'cur_cost': 973.0}, {'tour': array([7, 0, 6, 4, 3, 1, 5, 2, 8], dtype=int64), 'cur_cost': 1141.0}, {'tour': [5, 7, 0, 6, 8, 3, 4, 2, 1], 'cur_cost': 950.0}, {'tour': [3, 8, 5, 2, 4, 7, 6, 0, 1], 'cur_cost': 885.0}, {'tour': [4, 0, 7, 1, 6, 5, 3, 8, 2], 'cur_cost': 778.0}, {'tour': [4, 7, 8, 2, 1, 5, 0, 3, 6], 'cur_cost': 1085.0}, {'tour': [6, 0, 1, 4, 7, 3, 5, 8, 2], 'cur_cost': 851.0}, {'tour': [8, 7, 1, 2, 6, 4, 3, 0, 5], 'cur_cost': 1159.0}, {'tour': [4, 0, 1, 7, 5, 6, 3, 8, 2], 'cur_cost': 727.0}, {'tour': [5, 7, 0, 3, 4, 2, 6, 1, 8], 'cur_cost': 1038.0}]
2025-08-01 17:06:42,111 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-08-01 17:06:42,111 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-08-01 17:06:42,111 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([7, 0, 6, 4, 3, 1, 5, 2, 8], dtype=int64), 'cur_cost': 1141.0}
2025-08-01 17:06:42,112 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-01 17:06:42,112 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:06:42,112 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:06:42,113 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 912.0
2025-08-01 17:06:42,168 - ExploitationExpert - INFO - res_population_num: 4
2025-08-01 17:06:42,169 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680, 818.0]
2025-08-01 17:06:42,169 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 7, 5, 2, 4, 8, 3, 6, 1], dtype=int64)]
2025-08-01 17:06:42,171 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:06:42,173 - ExploitationExpert - INFO - populations: [{'tour': array([8, 7, 5, 1, 6, 2, 4, 0, 3], dtype=int64), 'cur_cost': 973.0}, {'tour': array([7, 0, 6, 4, 3, 1, 5, 2, 8], dtype=int64), 'cur_cost': 1141.0}, {'tour': array([8, 5, 1, 0, 3, 7, 6, 2, 4], dtype=int64), 'cur_cost': 912.0}, {'tour': [3, 8, 5, 2, 4, 7, 6, 0, 1], 'cur_cost': 885.0}, {'tour': [4, 0, 7, 1, 6, 5, 3, 8, 2], 'cur_cost': 778.0}, {'tour': [4, 7, 8, 2, 1, 5, 0, 3, 6], 'cur_cost': 1085.0}, {'tour': [6, 0, 1, 4, 7, 3, 5, 8, 2], 'cur_cost': 851.0}, {'tour': [8, 7, 1, 2, 6, 4, 3, 0, 5], 'cur_cost': 1159.0}, {'tour': [4, 0, 1, 7, 5, 6, 3, 8, 2], 'cur_cost': 727.0}, {'tour': [5, 7, 0, 3, 4, 2, 6, 1, 8], 'cur_cost': 1038.0}]
2025-08-01 17:06:42,178 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-08-01 17:06:42,178 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-08-01 17:06:42,178 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([8, 5, 1, 0, 3, 7, 6, 2, 4], dtype=int64), 'cur_cost': 912.0}
2025-08-01 17:06:42,179 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-01 17:06:42,179 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-01 17:06:42,179 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:06:42,180 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-01 17:06:42,180 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:06:42,180 - ExplorationExpert - INFO - 探索路径生成完成，成本: 860.0, 路径长度: 9
2025-08-01 17:06:42,180 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [1, 6, 3, 8, 7, 5, 0, 4, 2], 'cur_cost': 860.0}
2025-08-01 17:06:42,181 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-01 17:06:42,181 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:06:42,181 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:06:42,181 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 931.0
2025-08-01 17:06:42,247 - ExploitationExpert - INFO - res_population_num: 4
2025-08-01 17:06:42,247 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680, 818.0]
2025-08-01 17:06:42,247 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 7, 5, 2, 4, 8, 3, 6, 1], dtype=int64)]
2025-08-01 17:06:42,248 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:06:42,248 - ExploitationExpert - INFO - populations: [{'tour': array([8, 7, 5, 1, 6, 2, 4, 0, 3], dtype=int64), 'cur_cost': 973.0}, {'tour': array([7, 0, 6, 4, 3, 1, 5, 2, 8], dtype=int64), 'cur_cost': 1141.0}, {'tour': array([8, 5, 1, 0, 3, 7, 6, 2, 4], dtype=int64), 'cur_cost': 912.0}, {'tour': [1, 6, 3, 8, 7, 5, 0, 4, 2], 'cur_cost': 860.0}, {'tour': array([6, 4, 0, 1, 3, 7, 8, 2, 5], dtype=int64), 'cur_cost': 931.0}, {'tour': [4, 7, 8, 2, 1, 5, 0, 3, 6], 'cur_cost': 1085.0}, {'tour': [6, 0, 1, 4, 7, 3, 5, 8, 2], 'cur_cost': 851.0}, {'tour': [8, 7, 1, 2, 6, 4, 3, 0, 5], 'cur_cost': 1159.0}, {'tour': [4, 0, 1, 7, 5, 6, 3, 8, 2], 'cur_cost': 727.0}, {'tour': [5, 7, 0, 3, 4, 2, 6, 1, 8], 'cur_cost': 1038.0}]
2025-08-01 17:06:42,250 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-08-01 17:06:42,250 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-08-01 17:06:42,251 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([6, 4, 0, 1, 3, 7, 8, 2, 5], dtype=int64), 'cur_cost': 931.0}
2025-08-01 17:06:42,251 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-01 17:06:42,251 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:06:42,252 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:06:42,253 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 846.0
2025-08-01 17:06:42,308 - ExploitationExpert - INFO - res_population_num: 4
2025-08-01 17:06:42,308 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680, 818.0]
2025-08-01 17:06:42,309 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 7, 5, 2, 4, 8, 3, 6, 1], dtype=int64)]
2025-08-01 17:06:42,310 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:06:42,310 - ExploitationExpert - INFO - populations: [{'tour': array([8, 7, 5, 1, 6, 2, 4, 0, 3], dtype=int64), 'cur_cost': 973.0}, {'tour': array([7, 0, 6, 4, 3, 1, 5, 2, 8], dtype=int64), 'cur_cost': 1141.0}, {'tour': array([8, 5, 1, 0, 3, 7, 6, 2, 4], dtype=int64), 'cur_cost': 912.0}, {'tour': [1, 6, 3, 8, 7, 5, 0, 4, 2], 'cur_cost': 860.0}, {'tour': array([6, 4, 0, 1, 3, 7, 8, 2, 5], dtype=int64), 'cur_cost': 931.0}, {'tour': array([2, 0, 1, 5, 3, 8, 6, 7, 4], dtype=int64), 'cur_cost': 846.0}, {'tour': [6, 0, 1, 4, 7, 3, 5, 8, 2], 'cur_cost': 851.0}, {'tour': [8, 7, 1, 2, 6, 4, 3, 0, 5], 'cur_cost': 1159.0}, {'tour': [4, 0, 1, 7, 5, 6, 3, 8, 2], 'cur_cost': 727.0}, {'tour': [5, 7, 0, 3, 4, 2, 6, 1, 8], 'cur_cost': 1038.0}]
2025-08-01 17:06:42,312 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-08-01 17:06:42,313 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-08-01 17:06:42,313 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([2, 0, 1, 5, 3, 8, 6, 7, 4], dtype=int64), 'cur_cost': 846.0}
2025-08-01 17:06:42,313 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-01 17:06:42,313 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:06:42,314 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:06:42,314 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 895.0
2025-08-01 17:06:42,368 - ExploitationExpert - INFO - res_population_num: 4
2025-08-01 17:06:42,368 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680, 818.0]
2025-08-01 17:06:42,369 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 7, 5, 2, 4, 8, 3, 6, 1], dtype=int64)]
2025-08-01 17:06:42,371 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:06:42,374 - ExploitationExpert - INFO - populations: [{'tour': array([8, 7, 5, 1, 6, 2, 4, 0, 3], dtype=int64), 'cur_cost': 973.0}, {'tour': array([7, 0, 6, 4, 3, 1, 5, 2, 8], dtype=int64), 'cur_cost': 1141.0}, {'tour': array([8, 5, 1, 0, 3, 7, 6, 2, 4], dtype=int64), 'cur_cost': 912.0}, {'tour': [1, 6, 3, 8, 7, 5, 0, 4, 2], 'cur_cost': 860.0}, {'tour': array([6, 4, 0, 1, 3, 7, 8, 2, 5], dtype=int64), 'cur_cost': 931.0}, {'tour': array([2, 0, 1, 5, 3, 8, 6, 7, 4], dtype=int64), 'cur_cost': 846.0}, {'tour': array([8, 5, 3, 0, 1, 4, 6, 7, 2], dtype=int64), 'cur_cost': 895.0}, {'tour': [8, 7, 1, 2, 6, 4, 3, 0, 5], 'cur_cost': 1159.0}, {'tour': [4, 0, 1, 7, 5, 6, 3, 8, 2], 'cur_cost': 727.0}, {'tour': [5, 7, 0, 3, 4, 2, 6, 1, 8], 'cur_cost': 1038.0}]
2025-08-01 17:06:42,376 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-08-01 17:06:42,377 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 14, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 14, 'cache_hits': 0, 'similarity_calculations': 91, 'cache_hit_rate': 0.0, 'cache_size': 91}}
2025-08-01 17:06:42,377 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([8, 5, 3, 0, 1, 4, 6, 7, 2], dtype=int64), 'cur_cost': 895.0}
2025-08-01 17:06:42,377 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-01 17:06:42,378 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-01 17:06:42,378 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:06:42,378 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-01 17:06:42,378 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:06:42,379 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1058.0, 路径长度: 9
2025-08-01 17:06:42,379 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [6, 8, 2, 5, 7, 0, 4, 3, 1], 'cur_cost': 1058.0}
2025-08-01 17:06:42,379 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-01 17:06:42,379 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:06:42,379 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:06:42,380 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 799.0
2025-08-01 17:06:42,444 - ExploitationExpert - INFO - res_population_num: 4
2025-08-01 17:06:42,444 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680, 818.0]
2025-08-01 17:06:42,445 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 7, 5, 2, 4, 8, 3, 6, 1], dtype=int64)]
2025-08-01 17:06:42,446 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:06:42,446 - ExploitationExpert - INFO - populations: [{'tour': array([8, 7, 5, 1, 6, 2, 4, 0, 3], dtype=int64), 'cur_cost': 973.0}, {'tour': array([7, 0, 6, 4, 3, 1, 5, 2, 8], dtype=int64), 'cur_cost': 1141.0}, {'tour': array([8, 5, 1, 0, 3, 7, 6, 2, 4], dtype=int64), 'cur_cost': 912.0}, {'tour': [1, 6, 3, 8, 7, 5, 0, 4, 2], 'cur_cost': 860.0}, {'tour': array([6, 4, 0, 1, 3, 7, 8, 2, 5], dtype=int64), 'cur_cost': 931.0}, {'tour': array([2, 0, 1, 5, 3, 8, 6, 7, 4], dtype=int64), 'cur_cost': 846.0}, {'tour': array([8, 5, 3, 0, 1, 4, 6, 7, 2], dtype=int64), 'cur_cost': 895.0}, {'tour': [6, 8, 2, 5, 7, 0, 4, 3, 1], 'cur_cost': 1058.0}, {'tour': array([3, 4, 2, 1, 0, 7, 8, 6, 5], dtype=int64), 'cur_cost': 799.0}, {'tour': [5, 7, 0, 3, 4, 2, 6, 1, 8], 'cur_cost': 1038.0}]
2025-08-01 17:06:42,448 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-08-01 17:06:42,448 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 15, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 15, 'cache_hits': 0, 'similarity_calculations': 105, 'cache_hit_rate': 0.0, 'cache_size': 105}}
2025-08-01 17:06:42,449 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([3, 4, 2, 1, 0, 7, 8, 6, 5], dtype=int64), 'cur_cost': 799.0}
2025-08-01 17:06:42,449 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-01 17:06:42,449 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:06:42,449 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:06:42,450 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 1052.0
2025-08-01 17:06:42,499 - ExploitationExpert - INFO - res_population_num: 4
2025-08-01 17:06:42,499 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680, 818.0]
2025-08-01 17:06:42,500 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 7, 5, 2, 4, 8, 3, 6, 1], dtype=int64)]
2025-08-01 17:06:42,501 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:06:42,502 - ExploitationExpert - INFO - populations: [{'tour': array([8, 7, 5, 1, 6, 2, 4, 0, 3], dtype=int64), 'cur_cost': 973.0}, {'tour': array([7, 0, 6, 4, 3, 1, 5, 2, 8], dtype=int64), 'cur_cost': 1141.0}, {'tour': array([8, 5, 1, 0, 3, 7, 6, 2, 4], dtype=int64), 'cur_cost': 912.0}, {'tour': [1, 6, 3, 8, 7, 5, 0, 4, 2], 'cur_cost': 860.0}, {'tour': array([6, 4, 0, 1, 3, 7, 8, 2, 5], dtype=int64), 'cur_cost': 931.0}, {'tour': array([2, 0, 1, 5, 3, 8, 6, 7, 4], dtype=int64), 'cur_cost': 846.0}, {'tour': array([8, 5, 3, 0, 1, 4, 6, 7, 2], dtype=int64), 'cur_cost': 895.0}, {'tour': [6, 8, 2, 5, 7, 0, 4, 3, 1], 'cur_cost': 1058.0}, {'tour': array([3, 4, 2, 1, 0, 7, 8, 6, 5], dtype=int64), 'cur_cost': 799.0}, {'tour': array([5, 7, 4, 8, 3, 0, 6, 2, 1], dtype=int64), 'cur_cost': 1052.0}]
2025-08-01 17:06:42,505 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-08-01 17:06:42,506 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 16, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 16, 'cache_hits': 0, 'similarity_calculations': 120, 'cache_hit_rate': 0.0, 'cache_size': 120}}
2025-08-01 17:06:42,509 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([5, 7, 4, 8, 3, 0, 6, 2, 1], dtype=int64), 'cur_cost': 1052.0}
2025-08-01 17:06:42,510 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 7, 5, 1, 6, 2, 4, 0, 3], dtype=int64), 'cur_cost': 973.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 0, 6, 4, 3, 1, 5, 2, 8], dtype=int64), 'cur_cost': 1141.0}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 5, 1, 0, 3, 7, 6, 2, 4], dtype=int64), 'cur_cost': 912.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [1, 6, 3, 8, 7, 5, 0, 4, 2], 'cur_cost': 860.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 4, 0, 1, 3, 7, 8, 2, 5], dtype=int64), 'cur_cost': 931.0}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 0, 1, 5, 3, 8, 6, 7, 4], dtype=int64), 'cur_cost': 846.0}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 5, 3, 0, 1, 4, 6, 7, 2], dtype=int64), 'cur_cost': 895.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [6, 8, 2, 5, 7, 0, 4, 3, 1], 'cur_cost': 1058.0}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 4, 2, 1, 0, 7, 8, 6, 5], dtype=int64), 'cur_cost': 799.0}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([5, 7, 4, 8, 3, 0, 6, 2, 1], dtype=int64), 'cur_cost': 1052.0}}]
2025-08-01 17:06:42,511 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-01 17:06:42,511 - StatsExpert - INFO - 开始统计分析
2025-08-01 17:06:42,513 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=799.000, 多样性=0.874
2025-08-01 17:06:42,513 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-01 17:06:42,514 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-01 17:06:42,514 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-01 17:06:42,514 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 2, 'total_iterations': 3, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.032512738100284606, 'best_improvement': -0.09903713892709766}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.032069970845480876}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.7037037037037037, 'new_diversity': 0.7037037037037037, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-01 17:06:42,515 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-01 17:06:42,520 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_solution.json
2025-08-01 17:06:42,521 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_20250801_170642.solution
2025-08-01 17:06:42,521 - __main__ - INFO - 实例 simple1_9 处理完成
