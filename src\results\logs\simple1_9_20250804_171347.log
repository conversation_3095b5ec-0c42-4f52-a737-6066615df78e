2025-08-04 17:13:47,670 - __main__ - INFO - simple1_9 开始进化第 1 代
2025-08-04 17:13:47,670 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-04 17:13:47,671 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:13:47,672 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=747.000, 多样性=0.901
2025-08-04 17:13:47,673 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:13:47,674 - PathExpert - INFO - 路径结构分析完成: 公共边数量=3, 路径相似性=0.901
2025-08-04 17:13:47,675 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:13:47,677 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-04 17:13:47,678 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:13:47,678 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-04 17:13:47,678 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-04 17:13:47,853 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.200, 适应度梯度: -16.580, 聚类评分: 0.000, 覆盖率: 0.001, 收敛趋势: 0.000, 多样性: 0.811
2025-08-04 17:13:47,854 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-04 17:13:47,854 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-04 17:13:47,855 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:13:47,860 - visualization.landscape_visualizer - INFO - 插值约束: 43 个点被约束到最小值 747.00
2025-08-04 17:13:47,915 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记，坐标系统已统一
2025-08-04 17:13:48,485 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_1_20250804_171348.html
2025-08-04 17:13:48,529 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_1_20250804_171348.html
2025-08-04 17:13:48,529 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-04 17:13:48,530 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-04 17:13:48,530 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.8535秒
2025-08-04 17:13:48,530 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-04 17:13:48,530 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -16.580000000000002, 'local_optima_density': 0.2, 'gradient_variance': 31406.0516, 'cluster_count': 0}, 'population_state': {'diversity': 0.8111111111111111, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.001, 'fitness_entropy': 0.969570350190125, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -16.580)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.001)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.811)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754298827.8545344, 'performance_metrics': {}}}
2025-08-04 17:13:48,532 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:13:48,532 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:13:48,534 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:13:48,535 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:13:48,535 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-04 17:13:48,536 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-04 17:13:48,536 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-04 17:13:48,536 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:13:48,536 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:13:48,536 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-04 17:13:48,536 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:13:48,537 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:13:48,537 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 2} (总数: 2, 保护比例: 0.20)
2025-08-04 17:13:48,537 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:13:48,537 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:13:48,537 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:48,538 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:13:48,538 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:48,692 - ExplorationExpert - INFO - 探索路径生成完成，成本: 896.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:13:48,693 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [2, 5, 3, 4, 8, 7, 6, 0, 1], 'cur_cost': 896.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:13:48,693 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 896.00)
2025-08-04 17:13:48,693 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:13:48,693 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:13:48,694 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:48,694 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:13:48,695 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:48,695 - ExplorationExpert - INFO - 探索路径生成完成，成本: 938.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:13:48,695 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [1, 7, 3, 8, 5, 0, 4, 2, 6], 'cur_cost': 938.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:13:48,696 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 938.00)
2025-08-04 17:13:48,696 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:13:48,696 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:13:48,696 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:48,697 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:13:48,697 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:48,697 - ExplorationExpert - INFO - 探索路径生成完成，成本: 913.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:13:48,697 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [2, 5, 7, 3, 8, 4, 0, 1, 6], 'cur_cost': 913.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:13:48,699 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 913.00)
2025-08-04 17:13:48,699 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:13:48,699 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:13:48,700 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:48,700 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:13:48,701 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:48,701 - ExplorationExpert - INFO - 探索路径生成完成，成本: 721.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:13:48,701 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [1, 4, 2, 8, 7, 5, 3, 6, 0], 'cur_cost': 721.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:13:48,702 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 721.00)
2025-08-04 17:13:48,702 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:13:48,702 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:13:48,702 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:48,703 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:13:48,703 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:48,703 - ExplorationExpert - INFO - 探索路径生成完成，成本: 774.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:13:48,703 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [5, 3, 8, 7, 0, 1, 4, 2, 6], 'cur_cost': 774.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:13:48,704 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 774.00)
2025-08-04 17:13:48,704 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:13:48,704 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:13:48,704 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:48,705 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:13:48,705 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:48,705 - ExplorationExpert - INFO - 探索路径生成完成，成本: 988.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:13:48,705 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [5, 7, 8, 4, 0, 6, 2, 1, 3], 'cur_cost': 988.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:13:48,706 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 988.00)
2025-08-04 17:13:48,706 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:13:48,706 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:13:48,706 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:48,706 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:13:48,707 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:48,707 - ExplorationExpert - INFO - 探索路径生成完成，成本: 939.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:13:48,707 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 2, 1, 8, 3, 6, 5, 7, 4], 'cur_cost': 939.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:13:48,707 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 939.00)
2025-08-04 17:13:48,707 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:13:48,708 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:13:48,708 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:48,708 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:13:48,708 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:48,709 - ExplorationExpert - INFO - 探索路径生成完成，成本: 680.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:13:48,709 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [7, 3, 5, 6, 0, 1, 4, 2, 8], 'cur_cost': 680.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:13:48,709 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 680.00)
2025-08-04 17:13:48,709 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-04 17:13:48,710 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:13:48,711 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:13:48,712 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 1012.0
2025-08-04 17:13:49,855 - ExploitationExpert - INFO - res_population_num: 1
2025-08-04 17:13:49,855 - ExploitationExpert - INFO - res_population_costs: [680.0]
2025-08-04 17:13:49,856 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 17:13:49,856 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:13:49,856 - ExploitationExpert - INFO - populations: [{'tour': [2, 5, 3, 4, 8, 7, 6, 0, 1], 'cur_cost': 896.0}, {'tour': [1, 7, 3, 8, 5, 0, 4, 2, 6], 'cur_cost': 938.0}, {'tour': [2, 5, 7, 3, 8, 4, 0, 1, 6], 'cur_cost': 913.0}, {'tour': [1, 4, 2, 8, 7, 5, 3, 6, 0], 'cur_cost': 721.0}, {'tour': [5, 3, 8, 7, 0, 1, 4, 2, 6], 'cur_cost': 774.0}, {'tour': [5, 7, 8, 4, 0, 6, 2, 1, 3], 'cur_cost': 988.0}, {'tour': [0, 2, 1, 8, 3, 6, 5, 7, 4], 'cur_cost': 939.0}, {'tour': [7, 3, 5, 6, 0, 1, 4, 2, 8], 'cur_cost': 680.0}, {'tour': array([3, 2, 8, 5, 4, 6, 7, 1, 0], dtype=int64), 'cur_cost': 1012.0}, {'tour': array([8, 6, 2, 1, 3, 0, 7, 4, 5], dtype=int64), 'cur_cost': 1175.0}]
2025-08-04 17:13:49,857 - ExploitationExpert - INFO - 局部搜索耗时: 1.15秒，最大迭代次数: 10
2025-08-04 17:13:49,858 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-04 17:13:49,858 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([3, 2, 8, 5, 4, 6, 7, 1, 0], dtype=int64), 'cur_cost': 1012.0, 'intermediate_solutions': [{'tour': array([7, 2, 4, 0, 5, 3, 1, 8, 6], dtype=int64), 'cur_cost': 1022.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 7, 2, 4, 5, 3, 1, 8, 6], dtype=int64), 'cur_cost': 1051.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 0, 7, 2, 4, 3, 1, 8, 6], dtype=int64), 'cur_cost': 1069.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 0, 7, 2, 5, 3, 1, 8, 6], dtype=int64), 'cur_cost': 1164.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 5, 0, 7, 2, 3, 1, 8, 6], dtype=int64), 'cur_cost': 1263.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:13:49,859 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1012.00)
2025-08-04 17:13:49,859 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-04 17:13:49,860 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:13:49,860 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:13:49,860 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 1146.0
2025-08-04 17:13:51,496 - ExploitationExpert - INFO - res_population_num: 2
2025-08-04 17:13:51,497 - ExploitationExpert - INFO - res_population_costs: [680.0, 680]
2025-08-04 17:13:51,497 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64)]
2025-08-04 17:13:51,497 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:13:51,497 - ExploitationExpert - INFO - populations: [{'tour': [2, 5, 3, 4, 8, 7, 6, 0, 1], 'cur_cost': 896.0}, {'tour': [1, 7, 3, 8, 5, 0, 4, 2, 6], 'cur_cost': 938.0}, {'tour': [2, 5, 7, 3, 8, 4, 0, 1, 6], 'cur_cost': 913.0}, {'tour': [1, 4, 2, 8, 7, 5, 3, 6, 0], 'cur_cost': 721.0}, {'tour': [5, 3, 8, 7, 0, 1, 4, 2, 6], 'cur_cost': 774.0}, {'tour': [5, 7, 8, 4, 0, 6, 2, 1, 3], 'cur_cost': 988.0}, {'tour': [0, 2, 1, 8, 3, 6, 5, 7, 4], 'cur_cost': 939.0}, {'tour': [7, 3, 5, 6, 0, 1, 4, 2, 8], 'cur_cost': 680.0}, {'tour': array([3, 2, 8, 5, 4, 6, 7, 1, 0], dtype=int64), 'cur_cost': 1012.0}, {'tour': array([0, 7, 2, 3, 1, 8, 4, 6, 5], dtype=int64), 'cur_cost': 1146.0}]
2025-08-04 17:13:51,499 - ExploitationExpert - INFO - 局部搜索耗时: 1.64秒，最大迭代次数: 10
2025-08-04 17:13:51,499 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-04 17:13:51,500 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([0, 7, 2, 3, 1, 8, 4, 6, 5], dtype=int64), 'cur_cost': 1146.0, 'intermediate_solutions': [{'tour': array([2, 6, 8, 1, 3, 0, 7, 4, 5], dtype=int64), 'cur_cost': 1287.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 2, 6, 8, 3, 0, 7, 4, 5], dtype=int64), 'cur_cost': 1155.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 1, 2, 6, 8, 0, 7, 4, 5], dtype=int64), 'cur_cost': 1151.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 1, 2, 6, 3, 0, 7, 4, 5], dtype=int64), 'cur_cost': 1146.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 3, 1, 2, 6, 0, 7, 4, 5], dtype=int64), 'cur_cost': 1086.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:13:51,501 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1146.00)
2025-08-04 17:13:51,501 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:13:51,501 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:13:51,502 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [2, 5, 3, 4, 8, 7, 6, 0, 1], 'cur_cost': 896.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 3, 8, 5, 0, 4, 2, 6], 'cur_cost': 938.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [2, 5, 7, 3, 8, 4, 0, 1, 6], 'cur_cost': 913.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 2, 8, 7, 5, 3, 6, 0], 'cur_cost': 721.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [5, 3, 8, 7, 0, 1, 4, 2, 6], 'cur_cost': 774.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [5, 7, 8, 4, 0, 6, 2, 1, 3], 'cur_cost': 988.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 1, 8, 3, 6, 5, 7, 4], 'cur_cost': 939.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [7, 3, 5, 6, 0, 1, 4, 2, 8], 'cur_cost': 680.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 2, 8, 5, 4, 6, 7, 1, 0], dtype=int64), 'cur_cost': 1012.0, 'intermediate_solutions': [{'tour': array([7, 2, 4, 0, 5, 3, 1, 8, 6], dtype=int64), 'cur_cost': 1022.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 7, 2, 4, 5, 3, 1, 8, 6], dtype=int64), 'cur_cost': 1051.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 0, 7, 2, 4, 3, 1, 8, 6], dtype=int64), 'cur_cost': 1069.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 0, 7, 2, 5, 3, 1, 8, 6], dtype=int64), 'cur_cost': 1164.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 5, 0, 7, 2, 3, 1, 8, 6], dtype=int64), 'cur_cost': 1263.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 7, 2, 3, 1, 8, 4, 6, 5], dtype=int64), 'cur_cost': 1146.0, 'intermediate_solutions': [{'tour': array([2, 6, 8, 1, 3, 0, 7, 4, 5], dtype=int64), 'cur_cost': 1287.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 2, 6, 8, 3, 0, 7, 4, 5], dtype=int64), 'cur_cost': 1155.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 1, 2, 6, 8, 0, 7, 4, 5], dtype=int64), 'cur_cost': 1151.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 1, 2, 6, 3, 0, 7, 4, 5], dtype=int64), 'cur_cost': 1146.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 3, 1, 2, 6, 0, 7, 4, 5], dtype=int64), 'cur_cost': 1086.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-04 17:13:51,504 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:13:51,504 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:13:51,505 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=680.000, 多样性=0.889
2025-08-04 17:13:51,505 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-04 17:13:51,505 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-04 17:13:51,506 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:13:51,506 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.05786528271522476, 'best_improvement': 0.08969210174029452}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.013698630136986484}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.8888888888888888, 'new_diversity': 0.8888888888888888, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-04 17:13:51,506 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-04 17:13:51,506 - __main__ - INFO - simple1_9 开始进化第 2 代
2025-08-04 17:13:51,507 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-04 17:13:51,507 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:13:51,507 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=680.000, 多样性=0.889
2025-08-04 17:13:51,508 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:13:51,508 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.889
2025-08-04 17:13:51,509 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:13:51,509 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.889
2025-08-04 17:13:51,511 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-04 17:13:51,511 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:13:51,511 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-04 17:13:51,511 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-04 17:13:51,518 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.250, 适应度梯度: 22.983, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 0.672
2025-08-04 17:13:51,519 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-04 17:13:51,519 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:13:51,519 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:13:51,522 - visualization.landscape_visualizer - INFO - 插值约束: 73 个点被约束到最小值 680.00
2025-08-04 17:13:51,525 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:13:51,606 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_2_20250804_171351.html
2025-08-04 17:13:51,646 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_2_20250804_171351.html
2025-08-04 17:13:51,646 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 2
2025-08-04 17:13:51,646 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-04 17:13:51,646 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1350秒
2025-08-04 17:13:51,647 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.25, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 22.98333333333333, 'local_optima_density': 0.25, 'gradient_variance': 34025.66972222222, 'cluster_count': 0}, 'population_state': {'diversity': 0.6717171717171717, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0022, 'fitness_entropy': 0.8983741916842143, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 22.983)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754298831.5194416, 'performance_metrics': {}}}
2025-08-04 17:13:51,648 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:13:51,648 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:13:51,648 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:13:51,649 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:13:51,649 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-04 17:13:51,650 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-04 17:13:51,650 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-04 17:13:51,651 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:13:51,651 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:13:51,651 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-04 17:13:51,652 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:13:51,652 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:13:51,653 - experts.management.collaboration_manager - INFO - 识别精英个体: {3, 7} (总数: 2, 保护比例: 0.20)
2025-08-04 17:13:51,653 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:13:51,653 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:13:51,653 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:51,654 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:13:51,654 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,654 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,655 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,655 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,655 - ExplorationExpert - INFO - 探索路径生成完成，成本: 968.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:13:51,655 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [8, 4, 0, 7, 5, 6, 3, 2, 1], 'cur_cost': 968.0, 'intermediate_solutions': [{'tour': [2, 5, 3, 8, 4, 7, 6, 0, 1], 'cur_cost': 861.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 5, 3, 4, 8, 1, 0, 6, 7], 'cur_cost': 978.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 5, 3, 7, 4, 8, 6, 0, 1], 'cur_cost': 902.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:13:51,656 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 968.00)
2025-08-04 17:13:51,656 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:13:51,656 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:13:51,656 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:51,656 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:13:51,657 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,657 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,657 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,657 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,657 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1036.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:13:51,658 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [8, 5, 0, 4, 7, 1, 2, 6, 3], 'cur_cost': 1036.0, 'intermediate_solutions': [{'tour': [1, 7, 3, 8, 5, 6, 4, 2, 0], 'cur_cost': 806.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 2, 4, 0, 5, 8, 3, 7, 1], 'cur_cost': 938.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 3, 8, 0, 4, 2, 5, 6], 'cur_cost': 902.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:13:51,658 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1036.00)
2025-08-04 17:13:51,658 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:13:51,658 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:13:51,659 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:51,659 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:13:51,659 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,659 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,659 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,660 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,660 - ExplorationExpert - INFO - 探索路径生成完成，成本: 893.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:13:51,660 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [2, 4, 7, 6, 5, 8, 0, 1, 3], 'cur_cost': 893.0, 'intermediate_solutions': [{'tour': [2, 5, 7, 8, 3, 4, 0, 1, 6], 'cur_cost': 965.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 5, 7, 8, 3, 4, 0, 1, 6], 'cur_cost': 965.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 2, 5, 7, 3, 8, 0, 1, 6], 'cur_cost': 885.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:13:51,660 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 893.00)
2025-08-04 17:13:51,660 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:13:51,661 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:13:51,661 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:51,661 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:13:51,661 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,662 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,662 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,662 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,662 - ExplorationExpert - INFO - 探索路径生成完成，成本: 882.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:13:51,662 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [1, 6, 2, 7, 3, 5, 8, 4, 0], 'cur_cost': 882.0, 'intermediate_solutions': [{'tour': [1, 4, 2, 8, 6, 5, 3, 7, 0], 'cur_cost': 721.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 2, 8, 7, 5, 3, 0, 6], 'cur_cost': 821.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 2, 3, 8, 7, 5, 6, 0], 'cur_cost': 756.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:13:51,663 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 882.00)
2025-08-04 17:13:51,663 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:13:51,663 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:13:51,663 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:51,663 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:13:51,664 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,664 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,664 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,664 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,665 - ExplorationExpert - INFO - 探索路径生成完成，成本: 904.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:13:51,665 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [1, 6, 7, 8, 4, 5, 3, 2, 0], 'cur_cost': 904.0, 'intermediate_solutions': [{'tour': [2, 3, 8, 7, 0, 1, 4, 5, 6], 'cur_cost': 948.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 3, 8, 7, 0, 1, 6, 2, 4], 'cur_cost': 858.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 4, 3, 8, 7, 0, 1, 2, 6], 'cur_cost': 951.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:13:51,666 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 904.00)
2025-08-04 17:13:51,666 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:13:51,666 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:13:51,667 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:51,667 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:13:51,667 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,668 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,668 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,668 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,669 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1137.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:13:51,669 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [5, 3, 1, 7, 4, 8, 0, 6, 2], 'cur_cost': 1137.0, 'intermediate_solutions': [{'tour': [5, 7, 3, 4, 0, 6, 2, 1, 8], 'cur_cost': 1036.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 7, 5, 4, 0, 6, 2, 1, 3], 'cur_cost': 1051.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 0, 8, 4, 6, 2, 1, 3], 'cur_cost': 1090.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:13:51,671 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1137.00)
2025-08-04 17:13:51,671 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:13:51,671 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:13:51,671 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:51,671 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:13:51,671 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,671 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,671 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,672 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,672 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1060.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:13:51,672 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [2, 5, 3, 4, 0, 7, 6, 8, 1], 'cur_cost': 1060.0, 'intermediate_solutions': [{'tour': [0, 8, 1, 2, 3, 6, 5, 7, 4], 'cur_cost': 1020.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 3, 8, 1, 2, 5, 7, 4], 'cur_cost': 1012.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 1, 3, 6, 5, 7, 4, 8], 'cur_cost': 997.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:13:51,673 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1060.00)
2025-08-04 17:13:51,673 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:13:51,673 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:13:51,673 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:51,673 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:13:51,673 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,674 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,674 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,674 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,674 - ExplorationExpert - INFO - 探索路径生成完成，成本: 912.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:13:51,674 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [5, 7, 3, 4, 1, 0, 6, 8, 2], 'cur_cost': 912.0, 'intermediate_solutions': [{'tour': [7, 3, 5, 6, 0, 8, 4, 2, 1], 'cur_cost': 839.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 5, 6, 0, 1, 4, 8, 2], 'cur_cost': 764.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 3, 6, 0, 1, 4, 2, 5, 8], 'cur_cost': 820.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:13:51,675 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 912.00)
2025-08-04 17:13:51,675 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-04 17:13:51,675 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:13:51,675 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:13:51,676 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 805.0
2025-08-04 17:13:51,739 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:13:51,739 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 17:13:51,740 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:13:51,741 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:13:51,741 - ExploitationExpert - INFO - populations: [{'tour': [8, 4, 0, 7, 5, 6, 3, 2, 1], 'cur_cost': 968.0}, {'tour': [8, 5, 0, 4, 7, 1, 2, 6, 3], 'cur_cost': 1036.0}, {'tour': [2, 4, 7, 6, 5, 8, 0, 1, 3], 'cur_cost': 893.0}, {'tour': [1, 6, 2, 7, 3, 5, 8, 4, 0], 'cur_cost': 882.0}, {'tour': [1, 6, 7, 8, 4, 5, 3, 2, 0], 'cur_cost': 904.0}, {'tour': [5, 3, 1, 7, 4, 8, 0, 6, 2], 'cur_cost': 1137.0}, {'tour': [2, 5, 3, 4, 0, 7, 6, 8, 1], 'cur_cost': 1060.0}, {'tour': [5, 7, 3, 4, 1, 0, 6, 8, 2], 'cur_cost': 912.0}, {'tour': array([4, 2, 8, 6, 1, 0, 7, 3, 5], dtype=int64), 'cur_cost': 805.0}, {'tour': [0, 7, 2, 3, 1, 8, 4, 6, 5], 'cur_cost': 1146.0}]
2025-08-04 17:13:51,742 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:13:51,742 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-04 17:13:51,742 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([4, 2, 8, 6, 1, 0, 7, 3, 5], dtype=int64), 'cur_cost': 805.0, 'intermediate_solutions': [{'tour': array([8, 2, 3, 5, 4, 6, 7, 1, 0]), 'cur_cost': 988.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 8, 2, 3, 4, 6, 7, 1, 0]), 'cur_cost': 995.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 5, 8, 2, 3, 6, 7, 1, 0]), 'cur_cost': 924.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 5, 8, 2, 4, 6, 7, 1, 0]), 'cur_cost': 838.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 4, 5, 8, 2, 6, 7, 1, 0]), 'cur_cost': 1024.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:13:51,743 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 805.00)
2025-08-04 17:13:51,743 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-04 17:13:51,743 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:13:51,744 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:13:51,744 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 867.0
2025-08-04 17:13:51,809 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:13:51,809 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 17:13:51,809 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:13:51,810 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:13:51,810 - ExploitationExpert - INFO - populations: [{'tour': [8, 4, 0, 7, 5, 6, 3, 2, 1], 'cur_cost': 968.0}, {'tour': [8, 5, 0, 4, 7, 1, 2, 6, 3], 'cur_cost': 1036.0}, {'tour': [2, 4, 7, 6, 5, 8, 0, 1, 3], 'cur_cost': 893.0}, {'tour': [1, 6, 2, 7, 3, 5, 8, 4, 0], 'cur_cost': 882.0}, {'tour': [1, 6, 7, 8, 4, 5, 3, 2, 0], 'cur_cost': 904.0}, {'tour': [5, 3, 1, 7, 4, 8, 0, 6, 2], 'cur_cost': 1137.0}, {'tour': [2, 5, 3, 4, 0, 7, 6, 8, 1], 'cur_cost': 1060.0}, {'tour': [5, 7, 3, 4, 1, 0, 6, 8, 2], 'cur_cost': 912.0}, {'tour': array([4, 2, 8, 6, 1, 0, 7, 3, 5], dtype=int64), 'cur_cost': 805.0}, {'tour': array([1, 6, 4, 2, 7, 8, 5, 3, 0], dtype=int64), 'cur_cost': 867.0}]
2025-08-04 17:13:51,811 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:13:51,811 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-04 17:13:51,812 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([1, 6, 4, 2, 7, 8, 5, 3, 0], dtype=int64), 'cur_cost': 867.0, 'intermediate_solutions': [{'tour': array([2, 7, 0, 3, 1, 8, 4, 6, 5]), 'cur_cost': 1166.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 2, 7, 0, 1, 8, 4, 6, 5]), 'cur_cost': 958.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 3, 2, 7, 0, 8, 4, 6, 5]), 'cur_cost': 1145.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 3, 2, 7, 1, 8, 4, 6, 5]), 'cur_cost': 1146.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 1, 3, 2, 7, 8, 4, 6, 5]), 'cur_cost': 1015.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:13:51,812 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 867.00)
2025-08-04 17:13:51,812 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:13:51,813 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:13:51,813 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [8, 4, 0, 7, 5, 6, 3, 2, 1], 'cur_cost': 968.0, 'intermediate_solutions': [{'tour': [2, 5, 3, 8, 4, 7, 6, 0, 1], 'cur_cost': 861.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 5, 3, 4, 8, 1, 0, 6, 7], 'cur_cost': 978.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 5, 3, 7, 4, 8, 6, 0, 1], 'cur_cost': 902.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [8, 5, 0, 4, 7, 1, 2, 6, 3], 'cur_cost': 1036.0, 'intermediate_solutions': [{'tour': [1, 7, 3, 8, 5, 6, 4, 2, 0], 'cur_cost': 806.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 2, 4, 0, 5, 8, 3, 7, 1], 'cur_cost': 938.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 3, 8, 0, 4, 2, 5, 6], 'cur_cost': 902.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [2, 4, 7, 6, 5, 8, 0, 1, 3], 'cur_cost': 893.0, 'intermediate_solutions': [{'tour': [2, 5, 7, 8, 3, 4, 0, 1, 6], 'cur_cost': 965.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 5, 7, 8, 3, 4, 0, 1, 6], 'cur_cost': 965.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 2, 5, 7, 3, 8, 0, 1, 6], 'cur_cost': 885.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [1, 6, 2, 7, 3, 5, 8, 4, 0], 'cur_cost': 882.0, 'intermediate_solutions': [{'tour': [1, 4, 2, 8, 6, 5, 3, 7, 0], 'cur_cost': 721.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 2, 8, 7, 5, 3, 0, 6], 'cur_cost': 821.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 2, 3, 8, 7, 5, 6, 0], 'cur_cost': 756.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [1, 6, 7, 8, 4, 5, 3, 2, 0], 'cur_cost': 904.0, 'intermediate_solutions': [{'tour': [2, 3, 8, 7, 0, 1, 4, 5, 6], 'cur_cost': 948.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 3, 8, 7, 0, 1, 6, 2, 4], 'cur_cost': 858.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 4, 3, 8, 7, 0, 1, 2, 6], 'cur_cost': 951.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [5, 3, 1, 7, 4, 8, 0, 6, 2], 'cur_cost': 1137.0, 'intermediate_solutions': [{'tour': [5, 7, 3, 4, 0, 6, 2, 1, 8], 'cur_cost': 1036.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 7, 5, 4, 0, 6, 2, 1, 3], 'cur_cost': 1051.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 0, 8, 4, 6, 2, 1, 3], 'cur_cost': 1090.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [2, 5, 3, 4, 0, 7, 6, 8, 1], 'cur_cost': 1060.0, 'intermediate_solutions': [{'tour': [0, 8, 1, 2, 3, 6, 5, 7, 4], 'cur_cost': 1020.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 3, 8, 1, 2, 5, 7, 4], 'cur_cost': 1012.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 1, 3, 6, 5, 7, 4, 8], 'cur_cost': 997.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [5, 7, 3, 4, 1, 0, 6, 8, 2], 'cur_cost': 912.0, 'intermediate_solutions': [{'tour': [7, 3, 5, 6, 0, 8, 4, 2, 1], 'cur_cost': 839.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 5, 6, 0, 1, 4, 8, 2], 'cur_cost': 764.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 3, 6, 0, 1, 4, 2, 5, 8], 'cur_cost': 820.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 2, 8, 6, 1, 0, 7, 3, 5], dtype=int64), 'cur_cost': 805.0, 'intermediate_solutions': [{'tour': array([8, 2, 3, 5, 4, 6, 7, 1, 0]), 'cur_cost': 988.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 8, 2, 3, 4, 6, 7, 1, 0]), 'cur_cost': 995.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 5, 8, 2, 3, 6, 7, 1, 0]), 'cur_cost': 924.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 5, 8, 2, 4, 6, 7, 1, 0]), 'cur_cost': 838.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 4, 5, 8, 2, 6, 7, 1, 0]), 'cur_cost': 1024.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([1, 6, 4, 2, 7, 8, 5, 3, 0], dtype=int64), 'cur_cost': 867.0, 'intermediate_solutions': [{'tour': array([2, 7, 0, 3, 1, 8, 4, 6, 5]), 'cur_cost': 1166.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 2, 7, 0, 1, 8, 4, 6, 5]), 'cur_cost': 958.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 3, 2, 7, 0, 8, 4, 6, 5]), 'cur_cost': 1145.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 3, 2, 7, 1, 8, 4, 6, 5]), 'cur_cost': 1146.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 1, 3, 2, 7, 8, 4, 6, 5]), 'cur_cost': 1015.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-04 17:13:51,816 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:13:51,817 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:13:51,818 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=805.000, 多样性=0.894
2025-08-04 17:13:51,818 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-04 17:13:51,819 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-04 17:13:51,819 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:13:51,819 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.07556948030485634, 'best_improvement': -0.18382352941176472}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.005555555555555731}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.8518518518518519, 'new_diversity': 0.8518518518518519, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-04 17:13:51,820 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-04 17:13:51,820 - __main__ - INFO - simple1_9 开始进化第 3 代
2025-08-04 17:13:51,820 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-04 17:13:51,821 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:13:51,821 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=805.000, 多样性=0.894
2025-08-04 17:13:51,821 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:13:51,822 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.894
2025-08-04 17:13:51,822 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:13:51,823 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.852
2025-08-04 17:13:51,824 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-04 17:13:51,824 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:13:51,824 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-04 17:13:51,825 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-04 17:13:51,831 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.385, 适应度梯度: 40.077, 聚类评分: 0.000, 覆盖率: 0.003, 收敛趋势: 0.000, 多样性: 0.624
2025-08-04 17:13:51,831 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-04 17:13:51,832 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:13:51,832 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:13:51,838 - visualization.landscape_visualizer - INFO - 插值约束: 262 个点被约束到最小值 680.00
2025-08-04 17:13:51,842 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:13:51,927 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_3_20250804_171351.html
2025-08-04 17:13:51,968 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_3_20250804_171351.html
2025-08-04 17:13:51,969 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 3
2025-08-04 17:13:51,969 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-04 17:13:51,969 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1448秒
2025-08-04 17:13:51,969 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.38461538461538464, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 40.07692307692308, 'local_optima_density': 0.38461538461538464, 'gradient_variance': 19300.280236686387, 'cluster_count': 0}, 'population_state': {'diversity': 0.6242603550295858, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0033, 'fitness_entropy': 0.9064315085556598, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.003)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 40.077)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754298831.8319237, 'performance_metrics': {}}}
2025-08-04 17:13:51,970 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:13:51,970 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:13:51,970 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:13:51,971 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:13:51,971 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:13:51,971 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-04 17:13:51,971 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:13:51,972 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:13:51,972 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:13:51,972 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:13:51,972 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:13:51,973 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:13:51,973 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 9} (总数: 2, 保护比例: 0.20)
2025-08-04 17:13:51,973 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:13:51,973 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:13:51,973 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:51,974 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:13:51,974 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,974 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,975 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,975 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:51,975 - ExplorationExpert - INFO - 探索路径生成完成，成本: 939.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:13:51,975 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [4, 6, 1, 5, 0, 7, 3, 8, 2], 'cur_cost': 939.0, 'intermediate_solutions': [{'tour': [8, 4, 3, 7, 5, 6, 0, 2, 1], 'cur_cost': 955.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 4, 0, 7, 5, 1, 2, 3, 6], 'cur_cost': 1028.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 8, 4, 0, 7, 5, 6, 3, 1], 'cur_cost': 908.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:13:51,976 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 939.00)
2025-08-04 17:13:51,976 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-04 17:13:51,976 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:13:51,976 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:13:51,977 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 822.0
2025-08-04 17:13:52,041 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:13:52,041 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 17:13:52,041 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:13:52,042 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:13:52,042 - ExploitationExpert - INFO - populations: [{'tour': [4, 6, 1, 5, 0, 7, 3, 8, 2], 'cur_cost': 939.0}, {'tour': array([7, 3, 8, 2, 1, 0, 6, 5, 4], dtype=int64), 'cur_cost': 822.0}, {'tour': [2, 4, 7, 6, 5, 8, 0, 1, 3], 'cur_cost': 893.0}, {'tour': [1, 6, 2, 7, 3, 5, 8, 4, 0], 'cur_cost': 882.0}, {'tour': [1, 6, 7, 8, 4, 5, 3, 2, 0], 'cur_cost': 904.0}, {'tour': [5, 3, 1, 7, 4, 8, 0, 6, 2], 'cur_cost': 1137.0}, {'tour': [2, 5, 3, 4, 0, 7, 6, 8, 1], 'cur_cost': 1060.0}, {'tour': [5, 7, 3, 4, 1, 0, 6, 8, 2], 'cur_cost': 912.0}, {'tour': [4, 2, 8, 6, 1, 0, 7, 3, 5], 'cur_cost': 805.0}, {'tour': [1, 6, 4, 2, 7, 8, 5, 3, 0], 'cur_cost': 867.0}]
2025-08-04 17:13:52,043 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:13:52,043 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-04 17:13:52,044 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([7, 3, 8, 2, 1, 0, 6, 5, 4], dtype=int64), 'cur_cost': 822.0, 'intermediate_solutions': [{'tour': array([0, 5, 8, 4, 7, 1, 2, 6, 3]), 'cur_cost': 1092.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 0, 5, 8, 7, 1, 2, 6, 3]), 'cur_cost': 1071.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 4, 0, 5, 8, 1, 2, 6, 3]), 'cur_cost': 1069.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 4, 0, 5, 7, 1, 2, 6, 3]), 'cur_cost': 1009.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 7, 4, 0, 5, 1, 2, 6, 3]), 'cur_cost': 1064.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:13:52,044 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 822.00)
2025-08-04 17:13:52,044 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:13:52,045 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:13:52,045 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:52,046 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:13:52,046 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,046 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,046 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,047 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,047 - ExplorationExpert - INFO - 探索路径生成完成，成本: 913.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:13:52,047 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [4, 2, 5, 3, 8, 1, 6, 7, 0], 'cur_cost': 913.0, 'intermediate_solutions': [{'tour': [4, 2, 7, 6, 5, 8, 0, 1, 3], 'cur_cost': 901.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 4, 7, 6, 5, 8, 0, 3, 1], 'cur_cost': 963.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 7, 6, 5, 8, 0, 1, 3], 'cur_cost': 893.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:13:52,047 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 913.00)
2025-08-04 17:13:52,048 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:13:52,048 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:13:52,048 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:52,049 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:13:52,049 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,049 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,049 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,050 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,050 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1005.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:13:52,050 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [5, 2, 3, 8, 1, 0, 4, 6, 7], 'cur_cost': 1005.0, 'intermediate_solutions': [{'tour': [6, 1, 2, 7, 3, 5, 8, 4, 0], 'cur_cost': 885.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 3, 7, 2, 6, 5, 8, 4, 0], 'cur_cost': 936.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 2, 7, 3, 5, 4, 8, 0], 'cur_cost': 977.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:13:52,051 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1005.00)
2025-08-04 17:13:52,051 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:13:52,051 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:13:52,051 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:52,052 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:13:52,052 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,052 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,053 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,053 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,053 - ExplorationExpert - INFO - 探索路径生成完成，成本: 810.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:13:52,053 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [2, 7, 5, 6, 3, 8, 4, 0, 1], 'cur_cost': 810.0, 'intermediate_solutions': [{'tour': [1, 6, 7, 8, 4, 3, 5, 2, 0], 'cur_cost': 907.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 7, 6, 4, 5, 3, 2, 0], 'cur_cost': 1015.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 8, 4, 5, 3, 2, 6, 0], 'cur_cost': 988.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:13:52,054 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 810.00)
2025-08-04 17:13:52,054 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-04 17:13:52,054 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:13:52,054 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:13:52,054 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 1001.0
2025-08-04 17:13:52,121 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:13:52,122 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 17:13:52,122 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:13:52,123 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:13:52,123 - ExploitationExpert - INFO - populations: [{'tour': [4, 6, 1, 5, 0, 7, 3, 8, 2], 'cur_cost': 939.0}, {'tour': array([7, 3, 8, 2, 1, 0, 6, 5, 4], dtype=int64), 'cur_cost': 822.0}, {'tour': [4, 2, 5, 3, 8, 1, 6, 7, 0], 'cur_cost': 913.0}, {'tour': [5, 2, 3, 8, 1, 0, 4, 6, 7], 'cur_cost': 1005.0}, {'tour': [2, 7, 5, 6, 3, 8, 4, 0, 1], 'cur_cost': 810.0}, {'tour': array([8, 6, 4, 2, 1, 7, 5, 3, 0], dtype=int64), 'cur_cost': 1001.0}, {'tour': [2, 5, 3, 4, 0, 7, 6, 8, 1], 'cur_cost': 1060.0}, {'tour': [5, 7, 3, 4, 1, 0, 6, 8, 2], 'cur_cost': 912.0}, {'tour': [4, 2, 8, 6, 1, 0, 7, 3, 5], 'cur_cost': 805.0}, {'tour': [1, 6, 4, 2, 7, 8, 5, 3, 0], 'cur_cost': 867.0}]
2025-08-04 17:13:52,124 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:13:52,125 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-04 17:13:52,125 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([8, 6, 4, 2, 1, 7, 5, 3, 0], dtype=int64), 'cur_cost': 1001.0, 'intermediate_solutions': [{'tour': array([1, 3, 5, 7, 4, 8, 0, 6, 2]), 'cur_cost': 1048.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 1, 3, 5, 4, 8, 0, 6, 2]), 'cur_cost': 1142.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 7, 1, 3, 5, 8, 0, 6, 2]), 'cur_cost': 1023.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 7, 1, 3, 4, 8, 0, 6, 2]), 'cur_cost': 1173.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 4, 7, 1, 3, 8, 0, 6, 2]), 'cur_cost': 1200.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:13:52,126 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1001.00)
2025-08-04 17:13:52,126 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-04 17:13:52,126 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:13:52,126 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:13:52,126 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1109.0
2025-08-04 17:13:52,191 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:13:52,191 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 17:13:52,192 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:13:52,192 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:13:52,192 - ExploitationExpert - INFO - populations: [{'tour': [4, 6, 1, 5, 0, 7, 3, 8, 2], 'cur_cost': 939.0}, {'tour': array([7, 3, 8, 2, 1, 0, 6, 5, 4], dtype=int64), 'cur_cost': 822.0}, {'tour': [4, 2, 5, 3, 8, 1, 6, 7, 0], 'cur_cost': 913.0}, {'tour': [5, 2, 3, 8, 1, 0, 4, 6, 7], 'cur_cost': 1005.0}, {'tour': [2, 7, 5, 6, 3, 8, 4, 0, 1], 'cur_cost': 810.0}, {'tour': array([8, 6, 4, 2, 1, 7, 5, 3, 0], dtype=int64), 'cur_cost': 1001.0}, {'tour': array([3, 2, 5, 8, 7, 0, 4, 6, 1], dtype=int64), 'cur_cost': 1109.0}, {'tour': [5, 7, 3, 4, 1, 0, 6, 8, 2], 'cur_cost': 912.0}, {'tour': [4, 2, 8, 6, 1, 0, 7, 3, 5], 'cur_cost': 805.0}, {'tour': [1, 6, 4, 2, 7, 8, 5, 3, 0], 'cur_cost': 867.0}]
2025-08-04 17:13:52,193 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:13:52,194 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-04 17:13:52,194 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([3, 2, 5, 8, 7, 0, 4, 6, 1], dtype=int64), 'cur_cost': 1109.0, 'intermediate_solutions': [{'tour': array([3, 5, 2, 4, 0, 7, 6, 8, 1]), 'cur_cost': 1021.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 3, 5, 2, 0, 7, 6, 8, 1]), 'cur_cost': 1071.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 4, 3, 5, 2, 7, 6, 8, 1]), 'cur_cost': 1011.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 4, 3, 5, 0, 7, 6, 8, 1]), 'cur_cost': 985.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 0, 4, 3, 5, 7, 6, 8, 1]), 'cur_cost': 1001.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:13:52,195 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1109.00)
2025-08-04 17:13:52,195 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:13:52,195 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:13:52,195 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:52,196 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:13:52,196 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,197 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,197 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,197 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,197 - ExplorationExpert - INFO - 探索路径生成完成，成本: 802.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:13:52,198 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [5, 6, 3, 7, 8, 4, 2, 0, 1], 'cur_cost': 802.0, 'intermediate_solutions': [{'tour': [5, 7, 3, 4, 1, 8, 6, 0, 2], 'cur_cost': 1072.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 7, 8, 6, 0, 1, 4, 3, 2], 'cur_cost': 987.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 6, 3, 4, 1, 0, 8, 2], 'cur_cost': 933.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:13:52,198 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 802.00)
2025-08-04 17:13:52,198 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:13:52,199 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:13:52,199 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:52,200 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:13:52,200 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,200 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,201 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,201 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,201 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1086.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:13:52,202 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [2, 1, 5, 0, 4, 7, 8, 6, 3], 'cur_cost': 1086.0, 'intermediate_solutions': [{'tour': [4, 2, 8, 6, 5, 0, 7, 3, 1], 'cur_cost': 909.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 2, 8, 6, 5, 3, 7, 0, 1], 'cur_cost': 721.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 2, 8, 6, 1, 0, 7, 3, 5], 'cur_cost': 805.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:13:52,202 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 1086.00)
2025-08-04 17:13:52,203 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:13:52,203 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:13:52,203 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:52,203 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:13:52,204 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,204 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,204 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,204 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,205 - ExplorationExpert - INFO - 探索路径生成完成，成本: 967.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:13:52,205 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 6, 4, 5, 2, 8, 3, 7, 1], 'cur_cost': 967.0, 'intermediate_solutions': [{'tour': [1, 6, 7, 2, 4, 8, 5, 3, 0], 'cur_cost': 808.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 4, 2, 7, 8, 3, 5, 0], 'cur_cost': 850.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 6, 4, 2, 8, 5, 3, 0], 'cur_cost': 838.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:13:52,205 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 967.00)
2025-08-04 17:13:52,206 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:13:52,206 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:13:52,207 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [4, 6, 1, 5, 0, 7, 3, 8, 2], 'cur_cost': 939.0, 'intermediate_solutions': [{'tour': [8, 4, 3, 7, 5, 6, 0, 2, 1], 'cur_cost': 955.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 4, 0, 7, 5, 1, 2, 3, 6], 'cur_cost': 1028.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 8, 4, 0, 7, 5, 6, 3, 1], 'cur_cost': 908.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 3, 8, 2, 1, 0, 6, 5, 4], dtype=int64), 'cur_cost': 822.0, 'intermediate_solutions': [{'tour': array([0, 5, 8, 4, 7, 1, 2, 6, 3]), 'cur_cost': 1092.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 0, 5, 8, 7, 1, 2, 6, 3]), 'cur_cost': 1071.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 4, 0, 5, 8, 1, 2, 6, 3]), 'cur_cost': 1069.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 4, 0, 5, 7, 1, 2, 6, 3]), 'cur_cost': 1009.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 7, 4, 0, 5, 1, 2, 6, 3]), 'cur_cost': 1064.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [4, 2, 5, 3, 8, 1, 6, 7, 0], 'cur_cost': 913.0, 'intermediate_solutions': [{'tour': [4, 2, 7, 6, 5, 8, 0, 1, 3], 'cur_cost': 901.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 4, 7, 6, 5, 8, 0, 3, 1], 'cur_cost': 963.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 7, 6, 5, 8, 0, 1, 3], 'cur_cost': 893.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [5, 2, 3, 8, 1, 0, 4, 6, 7], 'cur_cost': 1005.0, 'intermediate_solutions': [{'tour': [6, 1, 2, 7, 3, 5, 8, 4, 0], 'cur_cost': 885.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 3, 7, 2, 6, 5, 8, 4, 0], 'cur_cost': 936.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 2, 7, 3, 5, 4, 8, 0], 'cur_cost': 977.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [2, 7, 5, 6, 3, 8, 4, 0, 1], 'cur_cost': 810.0, 'intermediate_solutions': [{'tour': [1, 6, 7, 8, 4, 3, 5, 2, 0], 'cur_cost': 907.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 7, 6, 4, 5, 3, 2, 0], 'cur_cost': 1015.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 8, 4, 5, 3, 2, 6, 0], 'cur_cost': 988.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 6, 4, 2, 1, 7, 5, 3, 0], dtype=int64), 'cur_cost': 1001.0, 'intermediate_solutions': [{'tour': array([1, 3, 5, 7, 4, 8, 0, 6, 2]), 'cur_cost': 1048.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 1, 3, 5, 4, 8, 0, 6, 2]), 'cur_cost': 1142.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 7, 1, 3, 5, 8, 0, 6, 2]), 'cur_cost': 1023.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 7, 1, 3, 4, 8, 0, 6, 2]), 'cur_cost': 1173.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 4, 7, 1, 3, 8, 0, 6, 2]), 'cur_cost': 1200.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 2, 5, 8, 7, 0, 4, 6, 1], dtype=int64), 'cur_cost': 1109.0, 'intermediate_solutions': [{'tour': array([3, 5, 2, 4, 0, 7, 6, 8, 1]), 'cur_cost': 1021.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 3, 5, 2, 0, 7, 6, 8, 1]), 'cur_cost': 1071.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 4, 3, 5, 2, 7, 6, 8, 1]), 'cur_cost': 1011.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 4, 3, 5, 0, 7, 6, 8, 1]), 'cur_cost': 985.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 0, 4, 3, 5, 7, 6, 8, 1]), 'cur_cost': 1001.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [5, 6, 3, 7, 8, 4, 2, 0, 1], 'cur_cost': 802.0, 'intermediate_solutions': [{'tour': [5, 7, 3, 4, 1, 8, 6, 0, 2], 'cur_cost': 1072.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 7, 8, 6, 0, 1, 4, 3, 2], 'cur_cost': 987.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 6, 3, 4, 1, 0, 8, 2], 'cur_cost': 933.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [2, 1, 5, 0, 4, 7, 8, 6, 3], 'cur_cost': 1086.0, 'intermediate_solutions': [{'tour': [4, 2, 8, 6, 5, 0, 7, 3, 1], 'cur_cost': 909.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 2, 8, 6, 5, 3, 7, 0, 1], 'cur_cost': 721.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 2, 8, 6, 1, 0, 7, 3, 5], 'cur_cost': 805.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 4, 5, 2, 8, 3, 7, 1], 'cur_cost': 967.0, 'intermediate_solutions': [{'tour': [1, 6, 7, 2, 4, 8, 5, 3, 0], 'cur_cost': 808.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 4, 2, 7, 8, 3, 5, 0], 'cur_cost': 850.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 6, 4, 2, 8, 5, 3, 0], 'cur_cost': 838.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:13:52,209 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:13:52,210 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:13:52,211 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=802.000, 多样性=0.874
2025-08-04 17:13:52,211 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-04 17:13:52,211 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-04 17:13:52,211 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:13:52,212 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 80, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'slight_improvement', 'improvement_rate': 0.009803184356444564, 'best_improvement': 0.0037267080745341614}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.022099447513812227}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.8518518518518519, 'new_diversity': 0.8518518518518519, 'diversity_change': 0.0}, 'suggestions': ['改进缓慢，建议调整策略平衡或增加扰动强度', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:13:52,212 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-04 17:13:52,213 - __main__ - INFO - simple1_9 开始进化第 4 代
2025-08-04 17:13:52,213 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-04 17:13:52,213 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:13:52,213 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=802.000, 多样性=0.874
2025-08-04 17:13:52,214 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:13:52,214 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.874
2025-08-04 17:13:52,215 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:13:52,215 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.852
2025-08-04 17:13:52,217 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-04 17:13:52,217 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:13:52,218 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-04 17:13:52,218 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-04 17:13:52,226 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.231, 适应度梯度: 9.631, 聚类评分: 0.000, 覆盖率: 0.004, 收敛趋势: 0.000, 多样性: 0.598
2025-08-04 17:13:52,226 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-04 17:13:52,226 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:13:52,227 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:13:52,232 - visualization.landscape_visualizer - INFO - 插值约束: 29 个点被约束到最小值 680.00
2025-08-04 17:13:52,236 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:13:52,316 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_4_20250804_171352.html
2025-08-04 17:13:52,359 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_4_20250804_171352.html
2025-08-04 17:13:52,360 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 4
2025-08-04 17:13:52,360 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-04 17:13:52,360 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1430秒
2025-08-04 17:13:52,360 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.23076923076923078, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 9.63076923076923, 'local_optima_density': 0.23076923076923078, 'gradient_variance': 28520.82982248521, 'cluster_count': 0}, 'population_state': {'diversity': 0.5976331360946745, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0043, 'fitness_entropy': 0.9686322539060781, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.004)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 9.631)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754298832.2262082, 'performance_metrics': {}}}
2025-08-04 17:13:52,361 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:13:52,361 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:13:52,362 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:13:52,362 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:13:52,362 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-04 17:13:52,363 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-04 17:13:52,363 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-04 17:13:52,363 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:13:52,364 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:13:52,364 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-04 17:13:52,364 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:13:52,365 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:13:52,365 - experts.management.collaboration_manager - INFO - 识别精英个体: {4, 7} (总数: 2, 保护比例: 0.20)
2025-08-04 17:13:52,365 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:13:52,366 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:13:52,366 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:52,367 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:13:52,367 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,368 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,368 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,368 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,368 - ExplorationExpert - INFO - 探索路径生成完成，成本: 825.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:13:52,368 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [6, 0, 1, 7, 3, 5, 8, 4, 2], 'cur_cost': 825.0, 'intermediate_solutions': [{'tour': [4, 6, 3, 5, 0, 7, 1, 8, 2], 'cur_cost': 979.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 6, 1, 5, 0, 7, 2, 8, 3], 'cur_cost': 1075.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 4, 1, 5, 0, 7, 3, 8, 2], 'cur_cost': 1061.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:13:52,369 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 825.00)
2025-08-04 17:13:52,369 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:13:52,369 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:13:52,370 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:52,370 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:13:52,370 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,371 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,371 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,371 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,371 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1143.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:13:52,371 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [7, 0, 1, 3, 4, 5, 2, 6, 8], 'cur_cost': 1143.0, 'intermediate_solutions': [{'tour': [8, 3, 7, 2, 1, 0, 6, 5, 4], 'cur_cost': 853.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 8, 0, 1, 2, 6, 5, 4], 'cur_cost': 951.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 7, 3, 8, 2, 1, 0, 5, 4], 'cur_cost': 919.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:13:52,372 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1143.00)
2025-08-04 17:13:52,372 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:13:52,372 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:13:52,372 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:52,373 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:13:52,373 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,373 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,373 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,373 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,374 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1019.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:13:52,374 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [2, 7, 0, 8, 3, 4, 5, 6, 1], 'cur_cost': 1019.0, 'intermediate_solutions': [{'tour': [4, 6, 5, 3, 8, 1, 2, 7, 0], 'cur_cost': 972.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 2, 5, 3, 1, 8, 6, 7, 0], 'cur_cost': 1021.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 5, 3, 8, 1, 6, 7, 0], 'cur_cost': 924.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:13:52,374 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1019.00)
2025-08-04 17:13:52,374 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-04 17:13:52,374 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:13:52,375 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:13:52,375 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 907.0
2025-08-04 17:13:52,445 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:13:52,445 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 17:13:52,445 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:13:52,446 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:13:52,447 - ExploitationExpert - INFO - populations: [{'tour': [6, 0, 1, 7, 3, 5, 8, 4, 2], 'cur_cost': 825.0}, {'tour': [7, 0, 1, 3, 4, 5, 2, 6, 8], 'cur_cost': 1143.0}, {'tour': [2, 7, 0, 8, 3, 4, 5, 6, 1], 'cur_cost': 1019.0}, {'tour': array([7, 3, 4, 0, 5, 6, 1, 2, 8], dtype=int64), 'cur_cost': 907.0}, {'tour': [2, 7, 5, 6, 3, 8, 4, 0, 1], 'cur_cost': 810.0}, {'tour': [8, 6, 4, 2, 1, 7, 5, 3, 0], 'cur_cost': 1001.0}, {'tour': [3, 2, 5, 8, 7, 0, 4, 6, 1], 'cur_cost': 1109.0}, {'tour': [5, 6, 3, 7, 8, 4, 2, 0, 1], 'cur_cost': 802.0}, {'tour': [2, 1, 5, 0, 4, 7, 8, 6, 3], 'cur_cost': 1086.0}, {'tour': [0, 6, 4, 5, 2, 8, 3, 7, 1], 'cur_cost': 967.0}]
2025-08-04 17:13:52,447 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:13:52,447 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-08-04 17:13:52,448 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([7, 3, 4, 0, 5, 6, 1, 2, 8], dtype=int64), 'cur_cost': 907.0, 'intermediate_solutions': [{'tour': array([3, 2, 5, 8, 1, 0, 4, 6, 7]), 'cur_cost': 1015.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 3, 2, 5, 1, 0, 4, 6, 7]), 'cur_cost': 1010.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 8, 3, 2, 5, 0, 4, 6, 7]), 'cur_cost': 1144.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 8, 3, 2, 1, 0, 4, 6, 7]), 'cur_cost': 893.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 1, 8, 3, 2, 0, 4, 6, 7]), 'cur_cost': 1082.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:13:52,449 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 907.00)
2025-08-04 17:13:52,449 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:13:52,450 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:13:52,450 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:52,451 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:13:52,451 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,451 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,451 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,452 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,452 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1188.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:13:52,452 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [8, 0, 6, 4, 7, 1, 3, 2, 5], 'cur_cost': 1188.0, 'intermediate_solutions': [{'tour': [2, 7, 5, 6, 8, 3, 4, 0, 1], 'cur_cost': 876.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 4, 8, 3, 6, 5, 7, 0, 1], 'cur_cost': 734.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 7, 5, 3, 8, 4, 0, 6, 1], 'cur_cost': 875.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:13:52,453 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 1188.00)
2025-08-04 17:13:52,453 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:13:52,453 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:13:52,454 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:52,454 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:13:52,454 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,455 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,455 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,455 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,456 - ExplorationExpert - INFO - 探索路径生成完成，成本: 905.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:13:52,456 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [2, 8, 5, 3, 7, 0, 4, 1, 6], 'cur_cost': 905.0, 'intermediate_solutions': [{'tour': [8, 6, 4, 2, 1, 7, 0, 3, 5], 'cur_cost': 976.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 2, 4, 6, 1, 7, 5, 3, 0], 'cur_cost': 942.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 6, 2, 1, 7, 4, 5, 3, 0], 'cur_cost': 1151.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:13:52,456 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 905.00)
2025-08-04 17:13:52,457 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-04 17:13:52,457 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:13:52,457 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:13:52,457 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1179.0
2025-08-04 17:13:52,539 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:13:52,539 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 17:13:52,539 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:13:52,540 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:13:52,540 - ExploitationExpert - INFO - populations: [{'tour': [6, 0, 1, 7, 3, 5, 8, 4, 2], 'cur_cost': 825.0}, {'tour': [7, 0, 1, 3, 4, 5, 2, 6, 8], 'cur_cost': 1143.0}, {'tour': [2, 7, 0, 8, 3, 4, 5, 6, 1], 'cur_cost': 1019.0}, {'tour': array([7, 3, 4, 0, 5, 6, 1, 2, 8], dtype=int64), 'cur_cost': 907.0}, {'tour': [8, 0, 6, 4, 7, 1, 3, 2, 5], 'cur_cost': 1188.0}, {'tour': [2, 8, 5, 3, 7, 0, 4, 1, 6], 'cur_cost': 905.0}, {'tour': array([5, 0, 3, 4, 7, 6, 2, 8, 1], dtype=int64), 'cur_cost': 1179.0}, {'tour': [5, 6, 3, 7, 8, 4, 2, 0, 1], 'cur_cost': 802.0}, {'tour': [2, 1, 5, 0, 4, 7, 8, 6, 3], 'cur_cost': 1086.0}, {'tour': [0, 6, 4, 5, 2, 8, 3, 7, 1], 'cur_cost': 967.0}]
2025-08-04 17:13:52,541 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒，最大迭代次数: 10
2025-08-04 17:13:52,541 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-08-04 17:13:52,542 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([5, 0, 3, 4, 7, 6, 2, 8, 1], dtype=int64), 'cur_cost': 1179.0, 'intermediate_solutions': [{'tour': array([5, 2, 3, 8, 7, 0, 4, 6, 1]), 'cur_cost': 1089.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 5, 2, 3, 7, 0, 4, 6, 1]), 'cur_cost': 1094.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 8, 5, 2, 3, 0, 4, 6, 1]), 'cur_cost': 1109.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 8, 5, 2, 7, 0, 4, 6, 1]), 'cur_cost': 1082.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 7, 8, 5, 2, 0, 4, 6, 1]), 'cur_cost': 1063.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:13:52,543 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1179.00)
2025-08-04 17:13:52,543 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:13:52,543 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:13:52,543 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:52,544 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:13:52,544 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,544 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,544 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,545 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,545 - ExplorationExpert - INFO - 探索路径生成完成，成本: 928.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:13:52,545 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [1, 4, 5, 7, 0, 2, 8, 3, 6], 'cur_cost': 928.0, 'intermediate_solutions': [{'tour': [5, 4, 3, 7, 8, 6, 2, 0, 1], 'cur_cost': 1074.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 4, 8, 7, 3, 6, 2, 0, 1], 'cur_cost': 1008.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 5, 6, 3, 8, 4, 2, 0, 1], 'cur_cost': 764.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:13:52,546 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 928.00)
2025-08-04 17:13:52,546 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-04 17:13:52,546 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:13:52,546 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:13:52,546 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 997.0
2025-08-04 17:13:52,627 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:13:52,627 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 17:13:52,627 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:13:52,628 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:13:52,628 - ExploitationExpert - INFO - populations: [{'tour': [6, 0, 1, 7, 3, 5, 8, 4, 2], 'cur_cost': 825.0}, {'tour': [7, 0, 1, 3, 4, 5, 2, 6, 8], 'cur_cost': 1143.0}, {'tour': [2, 7, 0, 8, 3, 4, 5, 6, 1], 'cur_cost': 1019.0}, {'tour': array([7, 3, 4, 0, 5, 6, 1, 2, 8], dtype=int64), 'cur_cost': 907.0}, {'tour': [8, 0, 6, 4, 7, 1, 3, 2, 5], 'cur_cost': 1188.0}, {'tour': [2, 8, 5, 3, 7, 0, 4, 1, 6], 'cur_cost': 905.0}, {'tour': array([5, 0, 3, 4, 7, 6, 2, 8, 1], dtype=int64), 'cur_cost': 1179.0}, {'tour': [1, 4, 5, 7, 0, 2, 8, 3, 6], 'cur_cost': 928.0}, {'tour': array([8, 2, 0, 1, 3, 7, 5, 4, 6], dtype=int64), 'cur_cost': 997.0}, {'tour': [0, 6, 4, 5, 2, 8, 3, 7, 1], 'cur_cost': 967.0}]
2025-08-04 17:13:52,629 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒，最大迭代次数: 10
2025-08-04 17:13:52,630 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-08-04 17:13:52,630 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([8, 2, 0, 1, 3, 7, 5, 4, 6], dtype=int64), 'cur_cost': 997.0, 'intermediate_solutions': [{'tour': array([5, 1, 2, 0, 4, 7, 8, 6, 3]), 'cur_cost': 998.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 5, 1, 2, 4, 7, 8, 6, 3]), 'cur_cost': 1031.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 0, 5, 1, 2, 7, 8, 6, 3]), 'cur_cost': 1094.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 0, 5, 1, 4, 7, 8, 6, 3]), 'cur_cost': 1097.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 4, 0, 5, 1, 7, 8, 6, 3]), 'cur_cost': 1034.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:13:52,631 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 997.00)
2025-08-04 17:13:52,631 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:13:52,631 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:13:52,631 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:52,632 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:13:52,632 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,632 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,633 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,633 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,633 - ExplorationExpert - INFO - 探索路径生成完成，成本: 898.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:13:52,633 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [4, 8, 6, 7, 1, 0, 3, 5, 2], 'cur_cost': 898.0, 'intermediate_solutions': [{'tour': [0, 6, 4, 5, 1, 8, 3, 7, 2], 'cur_cost': 1125.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 4, 5, 2, 8, 3, 1, 7], 'cur_cost': 1113.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 4, 5, 3, 2, 8, 7, 1], 'cur_cost': 953.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:13:52,634 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 898.00)
2025-08-04 17:13:52,634 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:13:52,634 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:13:52,637 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [6, 0, 1, 7, 3, 5, 8, 4, 2], 'cur_cost': 825.0, 'intermediate_solutions': [{'tour': [4, 6, 3, 5, 0, 7, 1, 8, 2], 'cur_cost': 979.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 6, 1, 5, 0, 7, 2, 8, 3], 'cur_cost': 1075.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 4, 1, 5, 0, 7, 3, 8, 2], 'cur_cost': 1061.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [7, 0, 1, 3, 4, 5, 2, 6, 8], 'cur_cost': 1143.0, 'intermediate_solutions': [{'tour': [8, 3, 7, 2, 1, 0, 6, 5, 4], 'cur_cost': 853.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 8, 0, 1, 2, 6, 5, 4], 'cur_cost': 951.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 7, 3, 8, 2, 1, 0, 5, 4], 'cur_cost': 919.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [2, 7, 0, 8, 3, 4, 5, 6, 1], 'cur_cost': 1019.0, 'intermediate_solutions': [{'tour': [4, 6, 5, 3, 8, 1, 2, 7, 0], 'cur_cost': 972.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 2, 5, 3, 1, 8, 6, 7, 0], 'cur_cost': 1021.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 5, 3, 8, 1, 6, 7, 0], 'cur_cost': 924.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 3, 4, 0, 5, 6, 1, 2, 8], dtype=int64), 'cur_cost': 907.0, 'intermediate_solutions': [{'tour': array([3, 2, 5, 8, 1, 0, 4, 6, 7]), 'cur_cost': 1015.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 3, 2, 5, 1, 0, 4, 6, 7]), 'cur_cost': 1010.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 8, 3, 2, 5, 0, 4, 6, 7]), 'cur_cost': 1144.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 8, 3, 2, 1, 0, 4, 6, 7]), 'cur_cost': 893.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 1, 8, 3, 2, 0, 4, 6, 7]), 'cur_cost': 1082.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [8, 0, 6, 4, 7, 1, 3, 2, 5], 'cur_cost': 1188.0, 'intermediate_solutions': [{'tour': [2, 7, 5, 6, 8, 3, 4, 0, 1], 'cur_cost': 876.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 4, 8, 3, 6, 5, 7, 0, 1], 'cur_cost': 734.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 7, 5, 3, 8, 4, 0, 6, 1], 'cur_cost': 875.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [2, 8, 5, 3, 7, 0, 4, 1, 6], 'cur_cost': 905.0, 'intermediate_solutions': [{'tour': [8, 6, 4, 2, 1, 7, 0, 3, 5], 'cur_cost': 976.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 2, 4, 6, 1, 7, 5, 3, 0], 'cur_cost': 942.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 6, 2, 1, 7, 4, 5, 3, 0], 'cur_cost': 1151.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([5, 0, 3, 4, 7, 6, 2, 8, 1], dtype=int64), 'cur_cost': 1179.0, 'intermediate_solutions': [{'tour': array([5, 2, 3, 8, 7, 0, 4, 6, 1]), 'cur_cost': 1089.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 5, 2, 3, 7, 0, 4, 6, 1]), 'cur_cost': 1094.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 8, 5, 2, 3, 0, 4, 6, 1]), 'cur_cost': 1109.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 8, 5, 2, 7, 0, 4, 6, 1]), 'cur_cost': 1082.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 7, 8, 5, 2, 0, 4, 6, 1]), 'cur_cost': 1063.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 5, 7, 0, 2, 8, 3, 6], 'cur_cost': 928.0, 'intermediate_solutions': [{'tour': [5, 4, 3, 7, 8, 6, 2, 0, 1], 'cur_cost': 1074.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 4, 8, 7, 3, 6, 2, 0, 1], 'cur_cost': 1008.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 5, 6, 3, 8, 4, 2, 0, 1], 'cur_cost': 764.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 2, 0, 1, 3, 7, 5, 4, 6], dtype=int64), 'cur_cost': 997.0, 'intermediate_solutions': [{'tour': array([5, 1, 2, 0, 4, 7, 8, 6, 3]), 'cur_cost': 998.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 5, 1, 2, 4, 7, 8, 6, 3]), 'cur_cost': 1031.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 0, 5, 1, 2, 7, 8, 6, 3]), 'cur_cost': 1094.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 0, 5, 1, 4, 7, 8, 6, 3]), 'cur_cost': 1097.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 4, 0, 5, 1, 7, 8, 6, 3]), 'cur_cost': 1034.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [4, 8, 6, 7, 1, 0, 3, 5, 2], 'cur_cost': 898.0, 'intermediate_solutions': [{'tour': [0, 6, 4, 5, 1, 8, 3, 7, 2], 'cur_cost': 1125.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 4, 5, 2, 8, 3, 1, 7], 'cur_cost': 1113.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 4, 5, 3, 2, 8, 7, 1], 'cur_cost': 953.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:13:52,640 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:13:52,640 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:13:52,641 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=825.000, 多样性=0.899
2025-08-04 17:13:52,642 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-04 17:13:52,642 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-04 17:13:52,642 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:13:52,642 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.052167818218961666, 'best_improvement': -0.028678304239401497}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.02824858757062147}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.0240310491793901, 'recent_improvements': [0.05786528271522476, -0.07556948030485634, 0.009803184356444564], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.8518518518518519, 'new_diversity': 0.8518518518518519, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:13:52,643 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-04 17:13:52,643 - __main__ - INFO - simple1_9 开始进化第 5 代
2025-08-04 17:13:52,643 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-04 17:13:52,644 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:13:52,644 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=825.000, 多样性=0.899
2025-08-04 17:13:52,645 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:13:52,645 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.899
2025-08-04 17:13:52,646 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:13:52,646 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.852
2025-08-04 17:13:52,648 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-04 17:13:52,649 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:13:52,649 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-04 17:13:52,649 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-04 17:13:52,658 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.231, 适应度梯度: 11.585, 聚类评分: 0.000, 覆盖率: 0.005, 收敛趋势: 0.000, 多样性: 0.620
2025-08-04 17:13:52,659 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-04 17:13:52,659 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:13:52,659 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:13:52,691 - visualization.landscape_visualizer - INFO - 插值约束: 96 个点被约束到最小值 680.00
2025-08-04 17:13:52,696 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:13:52,928 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_5_20250804_171352.html
2025-08-04 17:13:52,971 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_5_20250804_171352.html
2025-08-04 17:13:52,971 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 5
2025-08-04 17:13:52,972 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-04 17:13:52,972 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.3234秒
2025-08-04 17:13:52,972 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.23076923076923078, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 11.584615384615383, 'local_optima_density': 0.23076923076923078, 'gradient_variance': 33854.63668639053, 'cluster_count': 0}, 'population_state': {'diversity': 0.6203155818540433, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0053, 'fitness_entropy': 0.9473539682709406, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.005)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 11.585)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754298832.6599727, 'performance_metrics': {}}}
2025-08-04 17:13:52,973 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:13:52,973 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:13:52,973 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:13:52,973 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:13:52,974 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:13:52,974 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-04 17:13:52,974 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:13:52,975 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:13:52,975 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:13:52,975 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:13:52,976 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:13:52,976 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:13:52,977 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 9} (总数: 2, 保护比例: 0.20)
2025-08-04 17:13:52,977 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:13:52,977 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:13:52,977 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:52,977 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:13:52,978 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,978 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,978 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,978 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:52,979 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1043.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:13:52,979 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [6, 7, 4, 8, 5, 1, 2, 3, 0], 'cur_cost': 1043.0, 'intermediate_solutions': [{'tour': [4, 0, 1, 7, 3, 5, 8, 6, 2], 'cur_cost': 858.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 0, 1, 4, 8, 5, 3, 7, 2], 'cur_cost': 882.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 1, 7, 5, 8, 4, 3, 2], 'cur_cost': 989.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:13:52,979 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 1043.00)
2025-08-04 17:13:52,979 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-04 17:13:52,979 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:13:52,980 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:13:52,980 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 1002.0
2025-08-04 17:13:53,045 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:13:53,046 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 17:13:53,046 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:13:53,046 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:13:53,047 - ExploitationExpert - INFO - populations: [{'tour': [6, 7, 4, 8, 5, 1, 2, 3, 0], 'cur_cost': 1043.0}, {'tour': array([6, 3, 7, 4, 5, 8, 0, 1, 2], dtype=int64), 'cur_cost': 1002.0}, {'tour': [2, 7, 0, 8, 3, 4, 5, 6, 1], 'cur_cost': 1019.0}, {'tour': [7, 3, 4, 0, 5, 6, 1, 2, 8], 'cur_cost': 907.0}, {'tour': [8, 0, 6, 4, 7, 1, 3, 2, 5], 'cur_cost': 1188.0}, {'tour': [2, 8, 5, 3, 7, 0, 4, 1, 6], 'cur_cost': 905.0}, {'tour': [5, 0, 3, 4, 7, 6, 2, 8, 1], 'cur_cost': 1179.0}, {'tour': [1, 4, 5, 7, 0, 2, 8, 3, 6], 'cur_cost': 928.0}, {'tour': [8, 2, 0, 1, 3, 7, 5, 4, 6], 'cur_cost': 997.0}, {'tour': [4, 8, 6, 7, 1, 0, 3, 5, 2], 'cur_cost': 898.0}]
2025-08-04 17:13:53,047 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:13:53,047 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-08-04 17:13:53,048 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([6, 3, 7, 4, 5, 8, 0, 1, 2], dtype=int64), 'cur_cost': 1002.0, 'intermediate_solutions': [{'tour': array([1, 0, 7, 3, 4, 5, 2, 6, 8]), 'cur_cost': 1128.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 1, 0, 7, 4, 5, 2, 6, 8]), 'cur_cost': 1108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 3, 1, 0, 7, 5, 2, 6, 8]), 'cur_cost': 1081.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 3, 1, 0, 4, 5, 2, 6, 8]), 'cur_cost': 1083.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 4, 3, 1, 0, 5, 2, 6, 8]), 'cur_cost': 1139.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:13:53,049 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1002.00)
2025-08-04 17:13:53,049 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:13:53,049 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:13:53,050 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:53,050 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:13:53,050 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:53,051 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:53,051 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:53,051 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:53,051 - ExplorationExpert - INFO - 探索路径生成完成，成本: 941.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:13:53,051 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [7, 0, 6, 3, 5, 4, 2, 8, 1], 'cur_cost': 941.0, 'intermediate_solutions': [{'tour': [2, 7, 0, 8, 3, 6, 5, 4, 1], 'cur_cost': 986.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 0, 7, 8, 3, 4, 5, 6, 1], 'cur_cost': 965.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 7, 0, 8, 4, 5, 6, 1, 3], 'cur_cost': 1091.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:13:53,052 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 941.00)
2025-08-04 17:13:53,052 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:13:53,052 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:13:53,052 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:53,053 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:13:53,053 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:53,053 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:53,053 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:53,053 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:53,054 - ExplorationExpert - INFO - 探索路径生成完成，成本: 756.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:13:53,054 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [7, 8, 3, 2, 4, 0, 1, 6, 5], 'cur_cost': 756.0, 'intermediate_solutions': [{'tour': [7, 3, 4, 8, 5, 6, 1, 2, 0], 'cur_cost': 913.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 4, 0, 5, 8, 2, 1, 6], 'cur_cost': 931.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 4, 0, 5, 6, 7, 1, 2, 8], 'cur_cost': 926.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:13:53,054 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 756.00)
2025-08-04 17:13:53,054 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-04 17:13:53,055 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:13:53,055 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:13:53,055 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 1065.0
2025-08-04 17:13:53,121 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:13:53,121 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 17:13:53,121 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:13:53,122 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:13:53,122 - ExploitationExpert - INFO - populations: [{'tour': [6, 7, 4, 8, 5, 1, 2, 3, 0], 'cur_cost': 1043.0}, {'tour': array([6, 3, 7, 4, 5, 8, 0, 1, 2], dtype=int64), 'cur_cost': 1002.0}, {'tour': [7, 0, 6, 3, 5, 4, 2, 8, 1], 'cur_cost': 941.0}, {'tour': [7, 8, 3, 2, 4, 0, 1, 6, 5], 'cur_cost': 756.0}, {'tour': array([7, 1, 2, 0, 8, 6, 3, 5, 4], dtype=int64), 'cur_cost': 1065.0}, {'tour': [2, 8, 5, 3, 7, 0, 4, 1, 6], 'cur_cost': 905.0}, {'tour': [5, 0, 3, 4, 7, 6, 2, 8, 1], 'cur_cost': 1179.0}, {'tour': [1, 4, 5, 7, 0, 2, 8, 3, 6], 'cur_cost': 928.0}, {'tour': [8, 2, 0, 1, 3, 7, 5, 4, 6], 'cur_cost': 997.0}, {'tour': [4, 8, 6, 7, 1, 0, 3, 5, 2], 'cur_cost': 898.0}]
2025-08-04 17:13:53,123 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:13:53,123 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-08-04 17:13:53,124 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([7, 1, 2, 0, 8, 6, 3, 5, 4], dtype=int64), 'cur_cost': 1065.0, 'intermediate_solutions': [{'tour': array([6, 0, 8, 4, 7, 1, 3, 2, 5]), 'cur_cost': 1105.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 6, 0, 8, 7, 1, 3, 2, 5]), 'cur_cost': 1223.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 4, 6, 0, 8, 1, 3, 2, 5]), 'cur_cost': 1211.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 4, 6, 0, 7, 1, 3, 2, 5]), 'cur_cost': 1136.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 7, 4, 6, 0, 1, 3, 2, 5]), 'cur_cost': 1055.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:13:53,125 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1065.00)
2025-08-04 17:13:53,125 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:13:53,125 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:13:53,125 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:53,126 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:13:53,126 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:53,126 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:53,127 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:53,127 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:53,127 - ExplorationExpert - INFO - 探索路径生成完成，成本: 681.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:13:53,127 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [5, 6, 0, 1, 4, 2, 8, 3, 7], 'cur_cost': 681.0, 'intermediate_solutions': [{'tour': [2, 8, 7, 3, 5, 0, 4, 1, 6], 'cur_cost': 936.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 1, 4, 0, 7, 3, 5, 6], 'cur_cost': 933.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 8, 5, 3, 1, 7, 0, 4, 6], 'cur_cost': 1058.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:13:53,128 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 681.00)
2025-08-04 17:13:53,128 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-04 17:13:53,128 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:13:53,128 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:13:53,128 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1167.0
2025-08-04 17:13:53,194 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:13:53,194 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 17:13:53,194 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:13:53,195 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:13:53,195 - ExploitationExpert - INFO - populations: [{'tour': [6, 7, 4, 8, 5, 1, 2, 3, 0], 'cur_cost': 1043.0}, {'tour': array([6, 3, 7, 4, 5, 8, 0, 1, 2], dtype=int64), 'cur_cost': 1002.0}, {'tour': [7, 0, 6, 3, 5, 4, 2, 8, 1], 'cur_cost': 941.0}, {'tour': [7, 8, 3, 2, 4, 0, 1, 6, 5], 'cur_cost': 756.0}, {'tour': array([7, 1, 2, 0, 8, 6, 3, 5, 4], dtype=int64), 'cur_cost': 1065.0}, {'tour': [5, 6, 0, 1, 4, 2, 8, 3, 7], 'cur_cost': 681.0}, {'tour': array([5, 2, 3, 0, 7, 4, 6, 1, 8], dtype=int64), 'cur_cost': 1167.0}, {'tour': [1, 4, 5, 7, 0, 2, 8, 3, 6], 'cur_cost': 928.0}, {'tour': [8, 2, 0, 1, 3, 7, 5, 4, 6], 'cur_cost': 997.0}, {'tour': [4, 8, 6, 7, 1, 0, 3, 5, 2], 'cur_cost': 898.0}]
2025-08-04 17:13:53,196 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:13:53,197 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-08-04 17:13:53,197 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([5, 2, 3, 0, 7, 4, 6, 1, 8], dtype=int64), 'cur_cost': 1167.0, 'intermediate_solutions': [{'tour': array([3, 0, 5, 4, 7, 6, 2, 8, 1]), 'cur_cost': 1199.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 3, 0, 5, 7, 6, 2, 8, 1]), 'cur_cost': 1097.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 4, 3, 0, 5, 6, 2, 8, 1]), 'cur_cost': 1127.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 4, 3, 0, 7, 6, 2, 8, 1]), 'cur_cost': 1183.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 7, 4, 3, 0, 6, 2, 8, 1]), 'cur_cost': 1138.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:13:53,198 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1167.00)
2025-08-04 17:13:53,198 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:13:53,198 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:13:53,198 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:53,199 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:13:53,199 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:53,200 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:53,200 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:53,200 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:53,201 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1196.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:13:53,207 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [1, 7, 8, 0, 5, 4, 3, 2, 6], 'cur_cost': 1196.0, 'intermediate_solutions': [{'tour': [1, 4, 5, 0, 7, 2, 8, 3, 6], 'cur_cost': 988.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 2, 0, 7, 5, 8, 3, 6], 'cur_cost': 840.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 5, 7, 0, 2, 8, 3, 6], 'cur_cost': 928.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:13:53,254 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1196.00)
2025-08-04 17:13:53,254 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:13:53,254 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:13:53,254 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:53,255 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:13:53,256 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:53,256 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:53,256 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:53,256 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:53,257 - ExplorationExpert - INFO - 探索路径生成完成，成本: 885.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:13:53,257 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [8, 3, 5, 0, 4, 7, 6, 1, 2], 'cur_cost': 885.0, 'intermediate_solutions': [{'tour': [8, 2, 0, 6, 3, 7, 5, 4, 1], 'cur_cost': 980.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 2, 0, 1, 3, 7, 6, 4, 5], 'cur_cost': 966.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 0, 1, 3, 7, 2, 5, 4, 6], 'cur_cost': 1139.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:13:53,257 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 885.00)
2025-08-04 17:13:53,258 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:13:53,258 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:13:53,258 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:13:53,258 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:13:53,259 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:53,259 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:53,259 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:53,260 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:13:53,260 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1169.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:13:53,260 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [8, 5, 2, 3, 1, 7, 0, 4, 6], 'cur_cost': 1169.0, 'intermediate_solutions': [{'tour': [4, 8, 7, 6, 1, 0, 3, 5, 2], 'cur_cost': 838.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 6, 7, 5, 3, 0, 1, 2], 'cur_cost': 809.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 8, 6, 7, 0, 3, 1, 5, 2], 'cur_cost': 1083.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:13:53,261 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 1169.00)
2025-08-04 17:13:53,261 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:13:53,261 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:13:53,262 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [6, 7, 4, 8, 5, 1, 2, 3, 0], 'cur_cost': 1043.0, 'intermediate_solutions': [{'tour': [4, 0, 1, 7, 3, 5, 8, 6, 2], 'cur_cost': 858.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 0, 1, 4, 8, 5, 3, 7, 2], 'cur_cost': 882.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 1, 7, 5, 8, 4, 3, 2], 'cur_cost': 989.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 3, 7, 4, 5, 8, 0, 1, 2], dtype=int64), 'cur_cost': 1002.0, 'intermediate_solutions': [{'tour': array([1, 0, 7, 3, 4, 5, 2, 6, 8]), 'cur_cost': 1128.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 1, 0, 7, 4, 5, 2, 6, 8]), 'cur_cost': 1108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 3, 1, 0, 7, 5, 2, 6, 8]), 'cur_cost': 1081.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 3, 1, 0, 4, 5, 2, 6, 8]), 'cur_cost': 1083.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 4, 3, 1, 0, 5, 2, 6, 8]), 'cur_cost': 1139.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [7, 0, 6, 3, 5, 4, 2, 8, 1], 'cur_cost': 941.0, 'intermediate_solutions': [{'tour': [2, 7, 0, 8, 3, 6, 5, 4, 1], 'cur_cost': 986.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 0, 7, 8, 3, 4, 5, 6, 1], 'cur_cost': 965.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 7, 0, 8, 4, 5, 6, 1, 3], 'cur_cost': 1091.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [7, 8, 3, 2, 4, 0, 1, 6, 5], 'cur_cost': 756.0, 'intermediate_solutions': [{'tour': [7, 3, 4, 8, 5, 6, 1, 2, 0], 'cur_cost': 913.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 4, 0, 5, 8, 2, 1, 6], 'cur_cost': 931.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 4, 0, 5, 6, 7, 1, 2, 8], 'cur_cost': 926.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 1, 2, 0, 8, 6, 3, 5, 4], dtype=int64), 'cur_cost': 1065.0, 'intermediate_solutions': [{'tour': array([6, 0, 8, 4, 7, 1, 3, 2, 5]), 'cur_cost': 1105.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 6, 0, 8, 7, 1, 3, 2, 5]), 'cur_cost': 1223.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 4, 6, 0, 8, 1, 3, 2, 5]), 'cur_cost': 1211.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 4, 6, 0, 7, 1, 3, 2, 5]), 'cur_cost': 1136.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 7, 4, 6, 0, 1, 3, 2, 5]), 'cur_cost': 1055.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [5, 6, 0, 1, 4, 2, 8, 3, 7], 'cur_cost': 681.0, 'intermediate_solutions': [{'tour': [2, 8, 7, 3, 5, 0, 4, 1, 6], 'cur_cost': 936.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 1, 4, 0, 7, 3, 5, 6], 'cur_cost': 933.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 8, 5, 3, 1, 7, 0, 4, 6], 'cur_cost': 1058.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([5, 2, 3, 0, 7, 4, 6, 1, 8], dtype=int64), 'cur_cost': 1167.0, 'intermediate_solutions': [{'tour': array([3, 0, 5, 4, 7, 6, 2, 8, 1]), 'cur_cost': 1199.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 3, 0, 5, 7, 6, 2, 8, 1]), 'cur_cost': 1097.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 4, 3, 0, 5, 6, 2, 8, 1]), 'cur_cost': 1127.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 4, 3, 0, 7, 6, 2, 8, 1]), 'cur_cost': 1183.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 7, 4, 3, 0, 6, 2, 8, 1]), 'cur_cost': 1138.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 8, 0, 5, 4, 3, 2, 6], 'cur_cost': 1196.0, 'intermediate_solutions': [{'tour': [1, 4, 5, 0, 7, 2, 8, 3, 6], 'cur_cost': 988.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 2, 0, 7, 5, 8, 3, 6], 'cur_cost': 840.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 5, 7, 0, 2, 8, 3, 6], 'cur_cost': 928.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [8, 3, 5, 0, 4, 7, 6, 1, 2], 'cur_cost': 885.0, 'intermediate_solutions': [{'tour': [8, 2, 0, 6, 3, 7, 5, 4, 1], 'cur_cost': 980.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 2, 0, 1, 3, 7, 6, 4, 5], 'cur_cost': 966.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 0, 1, 3, 7, 2, 5, 4, 6], 'cur_cost': 1139.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [8, 5, 2, 3, 1, 7, 0, 4, 6], 'cur_cost': 1169.0, 'intermediate_solutions': [{'tour': [4, 8, 7, 6, 1, 0, 3, 5, 2], 'cur_cost': 838.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 6, 7, 5, 3, 0, 1, 2], 'cur_cost': 809.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 8, 6, 7, 0, 3, 1, 5, 2], 'cur_cost': 1083.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:13:53,265 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:13:53,265 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:13:53,268 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=681.000, 多样性=0.901
2025-08-04 17:13:53,268 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-04 17:13:53,268 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-04 17:13:53,269 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:13:53,269 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.05874023266221351, 'best_improvement': 0.17454545454545456}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.00274725274725271}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.011700831042947335, 'recent_improvements': [-0.07556948030485634, 0.009803184356444564, -0.052167818218961666], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.8518518518518519, 'new_diversity': 0.8518518518518519, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:13:53,270 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-04 17:13:53,271 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_solution.json
2025-08-04 17:13:53,272 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_20250804_171353.solution
2025-08-04 17:13:53,282 - __main__ - INFO - 评估统计 - 总次数: 237559.333333129, 运行时间: 5.73s, 最佳成本: 680.0
2025-08-04 17:13:53,282 - __main__ - INFO - 实例 simple1_9 处理完成
