"""
EoH-TSP-Solver 适应度评估次数统计系统

该模块提供了完整的适应度评估次数统计功能，包括：
- 多类型评估计数
- 算法阶段追踪
- 线程安全统计
- 性能优化实现
- 监控和报告功能

主要组件：
- EvaluationCounter: 核心计数器类
- NumbaCounterWrapper: Numba兼容的高性能计数器
- EvaluationType: 评估类型枚举
- AlgorithmPhase: 算法阶段枚举
- EvaluationContext: 阶段上下文管理器
"""

from .types import (
    EvaluationType,
    AlgorithmPhase,
    EvaluationComplexity,
    EvaluationRecord,
    EvaluationStatistics
)

from .counter import (
    EvaluationCounter,
    NumbaCounterWrapper
)

from .context import (
    EvaluationContext,
    NestedEvaluationContext
)

from .decorators import (
    evaluation_tracker,
    count_evaluations
)

from .config import (
    EvaluationConfig,
    EvaluationConfigManager
)

from .monitor import (
    EvaluationMonitor
)

from .integration import (
    EvaluationIntegrator
)

__version__ = "1.0.0"
__author__ = "Augment Agent"

# 全局计数器实例（可选使用）
_global_counter = None

def get_global_counter():
    """获取全局计数器实例"""
    global _global_counter
    # 检查配置是否启用评估统计
    try:
        from .config import get_config
        config = get_config()
        if not config.enabled:
            return None
    except Exception:
        # 如果配置加载失败，返回None以确保安全
        return None

    # 如果配置启用但全局计数器为None，不自动创建
    # 只有通过 setup_evaluation_system 正确初始化才会有计数器
    return _global_counter

def reset_global_counter():
    """重置全局计数器"""
    global _global_counter
    if _global_counter is not None:
        _global_counter.reset()

def set_global_counter(counter):
    """设置全局计数器"""
    global _global_counter
    _global_counter = counter

def clear_global_counter():
    """清除全局计数器（用于禁用评估统计）"""
    global _global_counter
    _global_counter = None

__all__ = [
    # 类型定义
    'EvaluationType',
    'AlgorithmPhase', 
    'EvaluationComplexity',
    'EvaluationRecord',
    'EvaluationStatistics',
    
    # 核心计数器
    'EvaluationCounter',
    'NumbaCounterWrapper',
    
    # 上下文管理
    'EvaluationContext',
    'NestedEvaluationContext',
    
    # 装饰器
    'evaluation_tracker',
    'count_evaluations',
    
    # 配置管理
    'EvaluationConfig',
    'EvaluationConfigManager',
    
    # 监控
    'EvaluationMonitor',
    
    # 集成工具
    'EvaluationIntegrator',
    
    # 全局函数
    'get_global_counter',
    'reset_global_counter',
    'set_global_counter'
]
