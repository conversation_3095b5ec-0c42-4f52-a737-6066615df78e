{"enabled": false, "detailed_logging": true, "max_records": 5000, "batch_size": 100, "enable_monitoring": false, "monitoring_interval": 1.0, "alert_thresholds": {"max_evaluations": 1200000, "max_evaluations_per_second": 10000, "efficiency_threshold": 0.1, "memory_usage_mb": 50.0, "phase_duration_seconds": 300}, "export_format": "json", "auto_export": false, "export_interval": 3600, "use_numba_counter": false, "thread_safe": true, "auto_reset_interval": 0, "max_memory_mb": 100.0}