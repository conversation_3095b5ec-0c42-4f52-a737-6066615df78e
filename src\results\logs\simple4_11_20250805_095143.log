2025-08-05 09:51:43,186 - __main__ - INFO - simple4_11 开始进化第 1 代
2025-08-05 09:51:43,186 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 09:51:43,187 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:43,188 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=803.000, 多样性=0.887
2025-08-05 09:51:43,189 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:43,190 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.887
2025-08-05 09:51:43,191 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:43,193 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 09:51:43,193 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:43,193 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 09:51:43,193 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 09:51:43,199 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -41.780, 聚类评分: 0.000, 覆盖率: 0.018, 收敛趋势: 0.000, 多样性: 0.976
2025-08-05 09:51:43,199 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 09:51:43,199 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 09:51:43,199 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple4_11
2025-08-05 09:51:43,203 - visualization.landscape_visualizer - INFO - 插值约束: 197 个点被约束到最小值 803.00
2025-08-05 09:51:43,279 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple4_11\landscape_simple4_11_iter_16_20250805_095143.html
2025-08-05 09:51:43,323 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple4_11\dashboard_simple4_11_iter_16_20250805_095143.html
2025-08-05 09:51:43,323 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 16
2025-08-05 09:51:43,323 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 09:51:43,323 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1306秒
2025-08-05 09:51:43,323 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 32, 'max_size': 500, 'hits': 0, 'misses': 32, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 54, 'max_size': 100, 'hits': 103, 'misses': 54, 'hit_rate': 0.6560509554140127, 'evictions': 0, 'ttl': 7200}}
2025-08-05 09:51:43,323 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -41.780000000000015, 'local_optima_density': 0.1, 'gradient_variance': 77983.86760000001, 'cluster_count': 0}, 'population_state': {'diversity': 0.9755555555555556, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0176, 'fitness_entropy': 0.94268071481726, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -41.780)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.018)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.976)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358703.1992378, 'performance_metrics': {}}}
2025-08-05 09:51:43,324 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:43,324 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:43,324 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:43,324 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:43,325 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 09:51:43,325 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 09:51:43,325 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 09:51:43,325 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:43,325 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:43,326 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 09:51:43,326 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:43,326 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:43,326 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:43,326 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:51:43,326 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:51:43,327 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:43,327 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 11
2025-08-05 09:51:43,327 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,327 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1132.0, 路径长度: 11, 收集中间解: 0
2025-08-05 09:51:43,327 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [7, 2, 3, 6, 5, 8, 0, 1, 4, 9, 10], 'cur_cost': 1132.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:43,328 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1132.00)
2025-08-05 09:51:43,328 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:51:43,328 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:51:43,328 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:43,328 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 11
2025-08-05 09:51:43,328 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,329 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1264.0, 路径长度: 11, 收集中间解: 0
2025-08-05 09:51:43,329 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [6, 5, 8, 0, 1, 3, 9, 4, 10, 2, 7], 'cur_cost': 1264.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:43,329 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 1264.00)
2025-08-05 09:51:43,329 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:51:43,329 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:51:43,329 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:43,330 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 11
2025-08-05 09:51:43,330 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,330 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1394.0, 路径长度: 11, 收集中间解: 0
2025-08-05 09:51:43,330 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [6, 8, 0, 4, 3, 1, 2, 10, 7, 9, 5], 'cur_cost': 1394.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:43,331 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 1394.00)
2025-08-05 09:51:43,331 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:51:43,331 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:51:43,331 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:43,332 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 11
2025-08-05 09:51:43,332 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,332 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1085.0, 路径长度: 11, 收集中间解: 0
2025-08-05 09:51:43,332 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [2, 1, 3, 6, 5, 9, 10, 0, 8, 7, 4], 'cur_cost': 1085.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:43,332 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1085.00)
2025-08-05 09:51:43,332 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 09:51:43,333 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:43,333 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:43,333 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 1300.0
2025-08-05 09:51:43,339 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 09:51:43,339 - ExploitationExpert - INFO - res_population_costs: [803.0, 803]
2025-08-05 09:51:43,339 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  2,  7,  4,  9, 10,  8,  5,  3,  6], dtype=int64), array([ 0,  1,  7,  4,  2,  9, 10,  8,  5,  3,  6], dtype=int64)]
2025-08-05 09:51:43,341 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:43,341 - ExploitationExpert - INFO - populations: [{'tour': [7, 2, 3, 6, 5, 8, 0, 1, 4, 9, 10], 'cur_cost': 1132.0}, {'tour': [6, 5, 8, 0, 1, 3, 9, 4, 10, 2, 7], 'cur_cost': 1264.0}, {'tour': [6, 8, 0, 4, 3, 1, 2, 10, 7, 9, 5], 'cur_cost': 1394.0}, {'tour': [2, 1, 3, 6, 5, 9, 10, 0, 8, 7, 4], 'cur_cost': 1085.0}, {'tour': array([ 1,  6,  9,  4,  2, 10,  5,  7,  3,  8,  0], dtype=int64), 'cur_cost': 1300.0}, {'tour': array([ 8,  2,  3, 10,  1,  0,  6,  9,  5,  4,  7], dtype=int64), 'cur_cost': 1379.0}, {'tour': array([10,  8,  5,  3,  9,  4,  1,  0,  6,  2,  7], dtype=int64), 'cur_cost': 1097.0}, {'tour': array([ 7,  5,  3,  1,  6,  0,  8,  2,  9, 10,  4], dtype=int64), 'cur_cost': 1207.0}, {'tour': array([ 5,  8,  9,  0, 10,  3,  7,  1,  6,  2,  4], dtype=int64), 'cur_cost': 1362.0}, {'tour': array([ 3,  1,  9,  5, 10,  2,  6,  8,  0,  4,  7], dtype=int64), 'cur_cost': 1253.0}]
2025-08-05 09:51:43,342 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:43,343 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 40, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 40, 'cache_hits': 0, 'similarity_calculations': 288, 'cache_hit_rate': 0.0, 'cache_size': 288}}
2025-08-05 09:51:43,344 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([ 1,  6,  9,  4,  2, 10,  5,  7,  3,  8,  0], dtype=int64), 'cur_cost': 1300.0, 'intermediate_solutions': [{'tour': array([ 6, 10,  4,  3,  9,  0,  5,  2,  7,  1,  8], dtype=int64), 'cur_cost': 1396.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 3,  6, 10,  4,  9,  0,  5,  2,  7,  1,  8], dtype=int64), 'cur_cost': 1389.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9,  3,  6, 10,  4,  0,  5,  2,  7,  1,  8], dtype=int64), 'cur_cost': 1404.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4,  3,  6, 10,  9,  0,  5,  2,  7,  1,  8], dtype=int64), 'cur_cost': 1411.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4,  9,  3,  6, 10,  0,  5,  2,  7,  1,  8], dtype=int64), 'cur_cost': 1354.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:43,344 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1300.00)
2025-08-05 09:51:43,344 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 09:51:43,344 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:43,344 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:43,345 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 1681.0
2025-08-05 09:51:43,351 - ExploitationExpert - INFO - res_population_num: 3
2025-08-05 09:51:43,351 - ExploitationExpert - INFO - res_population_costs: [803.0, 803, 803]
2025-08-05 09:51:43,351 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  2,  7,  4,  9, 10,  8,  5,  3,  6], dtype=int64), array([ 0,  1,  7,  4,  2,  9, 10,  8,  5,  3,  6], dtype=int64), array([ 0,  6,  8, 10,  5,  3,  9,  2,  4,  7,  1], dtype=int64)]
2025-08-05 09:51:43,353 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:43,353 - ExploitationExpert - INFO - populations: [{'tour': [7, 2, 3, 6, 5, 8, 0, 1, 4, 9, 10], 'cur_cost': 1132.0}, {'tour': [6, 5, 8, 0, 1, 3, 9, 4, 10, 2, 7], 'cur_cost': 1264.0}, {'tour': [6, 8, 0, 4, 3, 1, 2, 10, 7, 9, 5], 'cur_cost': 1394.0}, {'tour': [2, 1, 3, 6, 5, 9, 10, 0, 8, 7, 4], 'cur_cost': 1085.0}, {'tour': array([ 1,  6,  9,  4,  2, 10,  5,  7,  3,  8,  0], dtype=int64), 'cur_cost': 1300.0}, {'tour': array([ 0, 10,  7,  5,  3,  1,  8,  2,  9,  6,  4], dtype=int64), 'cur_cost': 1681.0}, {'tour': array([10,  8,  5,  3,  9,  4,  1,  0,  6,  2,  7], dtype=int64), 'cur_cost': 1097.0}, {'tour': array([ 7,  5,  3,  1,  6,  0,  8,  2,  9, 10,  4], dtype=int64), 'cur_cost': 1207.0}, {'tour': array([ 5,  8,  9,  0, 10,  3,  7,  1,  6,  2,  4], dtype=int64), 'cur_cost': 1362.0}, {'tour': array([ 3,  1,  9,  5, 10,  2,  6,  8,  0,  4,  7], dtype=int64), 'cur_cost': 1253.0}]
2025-08-05 09:51:43,356 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:43,356 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 41, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 41, 'cache_hits': 0, 'similarity_calculations': 289, 'cache_hit_rate': 0.0, 'cache_size': 289}}
2025-08-05 09:51:43,357 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([ 0, 10,  7,  5,  3,  1,  8,  2,  9,  6,  4], dtype=int64), 'cur_cost': 1681.0, 'intermediate_solutions': [{'tour': array([ 3,  2,  8, 10,  1,  0,  6,  9,  5,  4,  7], dtype=int64), 'cur_cost': 1235.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([10,  3,  2,  8,  1,  0,  6,  9,  5,  4,  7], dtype=int64), 'cur_cost': 1364.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1, 10,  3,  2,  8,  0,  6,  9,  5,  4,  7], dtype=int64), 'cur_cost': 1264.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 8, 10,  3,  2,  1,  0,  6,  9,  5,  4,  7], dtype=int64), 'cur_cost': 1172.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 8,  1, 10,  3,  2,  0,  6,  9,  5,  4,  7], dtype=int64), 'cur_cost': 1418.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:43,357 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1681.00)
2025-08-05 09:51:43,358 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:51:43,358 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:51:43,358 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:43,359 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 09:51:43,359 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,359 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1170.0, 路径长度: 11, 收集中间解: 0
2025-08-05 09:51:43,359 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [3, 8, 6, 10, 0, 9, 5, 1, 2, 7, 4], 'cur_cost': 1170.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:43,360 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1170.00)
2025-08-05 09:51:43,360 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:51:43,360 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:51:43,360 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:43,361 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 11
2025-08-05 09:51:43,361 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,362 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1294.0, 路径长度: 11, 收集中间解: 0
2025-08-05 09:51:43,362 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [2, 8, 10, 0, 3, 9, 4, 7, 5, 1, 6], 'cur_cost': 1294.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:43,362 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1294.00)
2025-08-05 09:51:43,363 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:51:43,363 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:51:43,363 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:43,364 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 11
2025-08-05 09:51:43,364 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,364 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1276.0, 路径长度: 11, 收集中间解: 0
2025-08-05 09:51:43,364 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [7, 5, 8, 10, 1, 0, 4, 9, 3, 6, 2], 'cur_cost': 1276.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:43,365 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1276.00)
2025-08-05 09:51:43,365 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:51:43,365 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:51:43,366 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:43,367 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 11
2025-08-05 09:51:43,367 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,367 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1010.0, 路径长度: 11, 收集中间解: 0
2025-08-05 09:51:43,367 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [3, 2, 9, 5, 6, 0, 8, 10, 1, 7, 4], 'cur_cost': 1010.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:43,368 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1010.00)
2025-08-05 09:51:43,368 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:43,368 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:43,370 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [7, 2, 3, 6, 5, 8, 0, 1, 4, 9, 10], 'cur_cost': 1132.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [6, 5, 8, 0, 1, 3, 9, 4, 10, 2, 7], 'cur_cost': 1264.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [6, 8, 0, 4, 3, 1, 2, 10, 7, 9, 5], 'cur_cost': 1394.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [2, 1, 3, 6, 5, 9, 10, 0, 8, 7, 4], 'cur_cost': 1085.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 1,  6,  9,  4,  2, 10,  5,  7,  3,  8,  0], dtype=int64), 'cur_cost': 1300.0, 'intermediate_solutions': [{'tour': array([ 6, 10,  4,  3,  9,  0,  5,  2,  7,  1,  8], dtype=int64), 'cur_cost': 1396.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 3,  6, 10,  4,  9,  0,  5,  2,  7,  1,  8], dtype=int64), 'cur_cost': 1389.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9,  3,  6, 10,  4,  0,  5,  2,  7,  1,  8], dtype=int64), 'cur_cost': 1404.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4,  3,  6, 10,  9,  0,  5,  2,  7,  1,  8], dtype=int64), 'cur_cost': 1411.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4,  9,  3,  6, 10,  0,  5,  2,  7,  1,  8], dtype=int64), 'cur_cost': 1354.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 0, 10,  7,  5,  3,  1,  8,  2,  9,  6,  4], dtype=int64), 'cur_cost': 1681.0, 'intermediate_solutions': [{'tour': array([ 3,  2,  8, 10,  1,  0,  6,  9,  5,  4,  7], dtype=int64), 'cur_cost': 1235.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([10,  3,  2,  8,  1,  0,  6,  9,  5,  4,  7], dtype=int64), 'cur_cost': 1364.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1, 10,  3,  2,  8,  0,  6,  9,  5,  4,  7], dtype=int64), 'cur_cost': 1264.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 8, 10,  3,  2,  1,  0,  6,  9,  5,  4,  7], dtype=int64), 'cur_cost': 1172.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 8,  1, 10,  3,  2,  0,  6,  9,  5,  4,  7], dtype=int64), 'cur_cost': 1418.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [3, 8, 6, 10, 0, 9, 5, 1, 2, 7, 4], 'cur_cost': 1170.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [2, 8, 10, 0, 3, 9, 4, 7, 5, 1, 6], 'cur_cost': 1294.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [7, 5, 8, 10, 1, 0, 4, 9, 3, 6, 2], 'cur_cost': 1276.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [3, 2, 9, 5, 6, 0, 8, 10, 1, 7, 4], 'cur_cost': 1010.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 09:51:43,370 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:43,370 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:43,372 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1010.000, 多样性=0.909
2025-08-05 09:51:43,373 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 09:51:43,373 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 09:51:43,373 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:43,373 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.15410528919845812, 'best_improvement': -0.2577833125778331}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.025056947608200618}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.035626967760774765, 'recent_improvements': [-0.059878797053610856, 0.04601153765404451, 0.011375138467938662], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 803.0, 'new_best_cost': 803.0, 'quality_improvement': 0.0, 'old_diversity': 0.696969696969697, 'new_diversity': 0.696969696969697, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 09:51:43,374 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 09:51:43,374 - __main__ - INFO - simple4_11 开始进化第 2 代
2025-08-05 09:51:43,374 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 09:51:43,375 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:43,376 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1010.000, 多样性=0.909
2025-08-05 09:51:43,376 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:43,377 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.909
2025-08-05 09:51:43,378 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:43,380 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.697
2025-08-05 09:51:43,384 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 09:51:43,385 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:43,385 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-05 09:51:43,385 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-05 09:51:43,401 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.308, 适应度梯度: 18.554, 聚类评分: 0.000, 覆盖率: 0.019, 收敛趋势: 0.000, 多样性: 0.757
2025-08-05 09:51:43,401 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 09:51:43,402 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:51:43,402 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple4_11
2025-08-05 09:51:43,408 - visualization.landscape_visualizer - INFO - 插值约束: 3 个点被约束到最小值 803.00
2025-08-05 09:51:43,523 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple4_11\landscape_simple4_11_iter_17_20250805_095143.html
2025-08-05 09:51:43,567 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple4_11\dashboard_simple4_11_iter_17_20250805_095143.html
2025-08-05 09:51:43,568 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 17
2025-08-05 09:51:43,568 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 09:51:43,568 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1838秒
2025-08-05 09:51:43,568 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3076923076923077, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 18.55384615384615, 'local_optima_density': 0.3076923076923077, 'gradient_variance': 90226.29633136094, 'cluster_count': 0}, 'population_state': {'diversity': 0.7573964497041421, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0188, 'fitness_entropy': 0.9138311481971516, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.019)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.757)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 18.554)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358703.401366, 'performance_metrics': {}}}
2025-08-05 09:51:43,568 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:43,568 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:43,569 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:43,569 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:43,569 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 09:51:43,569 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 09:51:43,569 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 09:51:43,570 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:43,570 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:43,570 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 09:51:43,570 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:43,570 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:43,570 - experts.management.collaboration_manager - INFO - 识别精英个体: {9, 3} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:43,570 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:51:43,571 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:51:43,571 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:43,571 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 09:51:43,571 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,571 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,572 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,572 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,572 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1341.0, 路径长度: 11, 收集中间解: 3
2025-08-05 09:51:43,572 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [4, 10, 1, 7, 8, 5, 3, 6, 0, 2, 9], 'cur_cost': 1341.0, 'intermediate_solutions': [{'tour': [7, 2, 3, 6, 5, 8, 0, 4, 1, 9, 10], 'cur_cost': 1260.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 2, 3, 4, 1, 0, 8, 5, 6, 9, 10], 'cur_cost': 1221.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 2, 3, 6, 5, 8, 4, 0, 1, 9, 10], 'cur_cost': 1357.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:43,572 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1341.00)
2025-08-05 09:51:43,572 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:51:43,572 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:51:43,572 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:43,573 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 09:51:43,573 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,573 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,573 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,573 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,573 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1323.0, 路径长度: 11, 收集中间解: 3
2025-08-05 09:51:43,573 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [6, 10, 3, 7, 8, 9, 5, 1, 2, 4, 0], 'cur_cost': 1323.0, 'intermediate_solutions': [{'tour': [6, 5, 8, 0, 1, 7, 9, 4, 10, 2, 3], 'cur_cost': 1260.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 5, 8, 0, 1, 3, 9, 4, 10, 7, 2], 'cur_cost': 1278.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 8, 0, 1, 3, 9, 4, 6, 10, 2, 7], 'cur_cost': 1286.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:43,574 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1323.00)
2025-08-05 09:51:43,574 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-05 09:51:43,574 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:43,574 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:43,574 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 1461.0
2025-08-05 09:51:43,580 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:43,580 - ExploitationExpert - INFO - res_population_costs: [803.0, 803, 803, 803]
2025-08-05 09:51:43,580 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  2,  7,  4,  9, 10,  8,  5,  3,  6], dtype=int64), array([ 0,  1,  7,  4,  2,  9, 10,  8,  5,  3,  6], dtype=int64), array([ 0,  6,  8, 10,  5,  3,  9,  2,  4,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  4,  9,  3,  5, 10,  8,  6], dtype=int64)]
2025-08-05 09:51:43,581 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:43,581 - ExploitationExpert - INFO - populations: [{'tour': [4, 10, 1, 7, 8, 5, 3, 6, 0, 2, 9], 'cur_cost': 1341.0}, {'tour': [6, 10, 3, 7, 8, 9, 5, 1, 2, 4, 0], 'cur_cost': 1323.0}, {'tour': array([ 4,  1,  2,  8,  5,  3, 10,  9,  0,  7,  6], dtype=int64), 'cur_cost': 1461.0}, {'tour': [2, 1, 3, 6, 5, 9, 10, 0, 8, 7, 4], 'cur_cost': 1085.0}, {'tour': [1, 6, 9, 4, 2, 10, 5, 7, 3, 8, 0], 'cur_cost': 1300.0}, {'tour': [0, 10, 7, 5, 3, 1, 8, 2, 9, 6, 4], 'cur_cost': 1681.0}, {'tour': [3, 8, 6, 10, 0, 9, 5, 1, 2, 7, 4], 'cur_cost': 1170.0}, {'tour': [2, 8, 10, 0, 3, 9, 4, 7, 5, 1, 6], 'cur_cost': 1294.0}, {'tour': [7, 5, 8, 10, 1, 0, 4, 9, 3, 6, 2], 'cur_cost': 1276.0}, {'tour': [3, 2, 9, 5, 6, 0, 8, 10, 1, 7, 4], 'cur_cost': 1010.0}]
2025-08-05 09:51:43,582 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:43,582 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 42, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 42, 'cache_hits': 0, 'similarity_calculations': 291, 'cache_hit_rate': 0.0, 'cache_size': 291}}
2025-08-05 09:51:43,582 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([ 4,  1,  2,  8,  5,  3, 10,  9,  0,  7,  6], dtype=int64), 'cur_cost': 1461.0, 'intermediate_solutions': [{'tour': array([ 0,  8,  6,  4,  3,  1,  2, 10,  7,  9,  5]), 'cur_cost': 1426.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 4,  0,  8,  6,  3,  1,  2, 10,  7,  9,  5]), 'cur_cost': 1448.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3,  4,  0,  8,  6,  1,  2, 10,  7,  9,  5]), 'cur_cost': 1414.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6,  4,  0,  8,  3,  1,  2, 10,  7,  9,  5]), 'cur_cost': 1481.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6,  3,  4,  0,  8,  1,  2, 10,  7,  9,  5]), 'cur_cost': 1476.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:43,582 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1461.00)
2025-08-05 09:51:43,582 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:51:43,582 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:51:43,582 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:43,583 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 11
2025-08-05 09:51:43,583 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,583 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,583 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,583 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,583 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1150.0, 路径长度: 11, 收集中间解: 3
2025-08-05 09:51:43,584 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [6, 5, 1, 2, 0, 9, 4, 7, 3, 10, 8], 'cur_cost': 1150.0, 'intermediate_solutions': [{'tour': [2, 1, 3, 4, 5, 9, 10, 0, 8, 7, 6], 'cur_cost': 1491.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 1, 3, 6, 5, 9, 10, 8, 0, 7, 4], 'cur_cost': 949.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 3, 6, 1, 5, 9, 10, 0, 8, 7, 4], 'cur_cost': 1168.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:43,584 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 1150.00)
2025-08-05 09:51:43,584 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:51:43,584 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:51:43,584 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:43,584 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 11
2025-08-05 09:51:43,584 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,585 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,585 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,585 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,585 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1064.0, 路径长度: 11, 收集中间解: 3
2025-08-05 09:51:43,585 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [1, 7, 9, 5, 3, 10, 8, 0, 6, 2, 4], 'cur_cost': 1064.0, 'intermediate_solutions': [{'tour': [1, 0, 9, 4, 2, 10, 5, 7, 3, 8, 6], 'cur_cost': 1306.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 9, 0, 8, 3, 7, 5, 10, 2, 4], 'cur_cost': 1388.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 9, 4, 2, 10, 5, 7, 3, 8, 0], 'cur_cost': 1300.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:43,585 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1064.00)
2025-08-05 09:51:43,585 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 09:51:43,585 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:43,586 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:43,586 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 1283.0
2025-08-05 09:51:43,591 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:43,591 - ExploitationExpert - INFO - res_population_costs: [803.0, 803, 803, 803]
2025-08-05 09:51:43,591 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  2,  7,  4,  9, 10,  8,  5,  3,  6], dtype=int64), array([ 0,  1,  7,  4,  2,  9, 10,  8,  5,  3,  6], dtype=int64), array([ 0,  6,  8, 10,  5,  3,  9,  2,  4,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  4,  9,  3,  5, 10,  8,  6], dtype=int64)]
2025-08-05 09:51:43,592 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:43,592 - ExploitationExpert - INFO - populations: [{'tour': [4, 10, 1, 7, 8, 5, 3, 6, 0, 2, 9], 'cur_cost': 1341.0}, {'tour': [6, 10, 3, 7, 8, 9, 5, 1, 2, 4, 0], 'cur_cost': 1323.0}, {'tour': array([ 4,  1,  2,  8,  5,  3, 10,  9,  0,  7,  6], dtype=int64), 'cur_cost': 1461.0}, {'tour': [6, 5, 1, 2, 0, 9, 4, 7, 3, 10, 8], 'cur_cost': 1150.0}, {'tour': [1, 7, 9, 5, 3, 10, 8, 0, 6, 2, 4], 'cur_cost': 1064.0}, {'tour': array([ 2, 10,  9,  6,  3,  1,  4,  7,  8,  5,  0], dtype=int64), 'cur_cost': 1283.0}, {'tour': [3, 8, 6, 10, 0, 9, 5, 1, 2, 7, 4], 'cur_cost': 1170.0}, {'tour': [2, 8, 10, 0, 3, 9, 4, 7, 5, 1, 6], 'cur_cost': 1294.0}, {'tour': [7, 5, 8, 10, 1, 0, 4, 9, 3, 6, 2], 'cur_cost': 1276.0}, {'tour': [3, 2, 9, 5, 6, 0, 8, 10, 1, 7, 4], 'cur_cost': 1010.0}]
2025-08-05 09:51:43,593 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:43,593 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 43, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 43, 'cache_hits': 0, 'similarity_calculations': 294, 'cache_hit_rate': 0.0, 'cache_size': 294}}
2025-08-05 09:51:43,594 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([ 2, 10,  9,  6,  3,  1,  4,  7,  8,  5,  0], dtype=int64), 'cur_cost': 1283.0, 'intermediate_solutions': [{'tour': array([ 7, 10,  0,  5,  3,  1,  8,  2,  9,  6,  4]), 'cur_cost': 1436.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 5,  7, 10,  0,  3,  1,  8,  2,  9,  6,  4]), 'cur_cost': 1704.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3,  5,  7, 10,  0,  1,  8,  2,  9,  6,  4]), 'cur_cost': 1640.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 0,  5,  7, 10,  3,  1,  8,  2,  9,  6,  4]), 'cur_cost': 1685.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 0,  3,  5,  7, 10,  1,  8,  2,  9,  6,  4]), 'cur_cost': 1730.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:43,594 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1283.00)
2025-08-05 09:51:43,594 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:51:43,594 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:51:43,594 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:43,594 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 09:51:43,594 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,595 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,595 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,595 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,595 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1392.0, 路径长度: 11, 收集中间解: 3
2025-08-05 09:51:43,595 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [1, 8, 4, 6, 9, 10, 5, 3, 2, 7, 0], 'cur_cost': 1392.0, 'intermediate_solutions': [{'tour': [3, 8, 6, 10, 0, 9, 5, 7, 2, 1, 4], 'cur_cost': 1322.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 6, 10, 1, 5, 9, 0, 2, 7, 4], 'cur_cost': 1266.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 8, 6, 10, 9, 5, 1, 2, 7, 4, 0], 'cur_cost': 1106.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:43,595 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1392.00)
2025-08-05 09:51:43,595 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:51:43,595 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:51:43,596 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:43,596 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 09:51:43,596 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,596 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,596 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,596 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,596 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1417.0, 路径长度: 11, 收集中间解: 3
2025-08-05 09:51:43,597 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [3, 4, 6, 8, 7, 9, 10, 5, 1, 2, 0], 'cur_cost': 1417.0, 'intermediate_solutions': [{'tour': [2, 8, 5, 0, 3, 9, 4, 7, 10, 1, 6], 'cur_cost': 1339.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 10, 0, 3, 9, 4, 7, 5, 6, 1], 'cur_cost': 1179.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 8, 0, 3, 9, 4, 7, 5, 1, 6, 10], 'cur_cost': 1332.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:43,597 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1417.00)
2025-08-05 09:51:43,597 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:51:43,597 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:51:43,597 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:43,597 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 09:51:43,597 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,598 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,598 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,598 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,598 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1039.0, 路径长度: 11, 收集中间解: 3
2025-08-05 09:51:43,598 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [1, 9, 3, 6, 10, 5, 8, 0, 2, 7, 4], 'cur_cost': 1039.0, 'intermediate_solutions': [{'tour': [7, 5, 8, 10, 1, 0, 3, 9, 4, 6, 2], 'cur_cost': 1297.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 6, 3, 9, 4, 0, 1, 10, 8, 2], 'cur_cost': 1321.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 5, 8, 10, 1, 0, 4, 9, 3, 6, 2], 'cur_cost': 1276.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:43,598 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1039.00)
2025-08-05 09:51:43,598 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:51:43,598 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:51:43,599 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:43,599 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 09:51:43,599 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,599 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,599 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,599 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,599 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1147.0, 路径长度: 11, 收集中间解: 3
2025-08-05 09:51:43,600 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [4, 5, 0, 10, 8, 6, 3, 1, 2, 7, 9], 'cur_cost': 1147.0, 'intermediate_solutions': [{'tour': [3, 2, 9, 5, 6, 1, 8, 10, 0, 7, 4], 'cur_cost': 1177.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 2, 9, 5, 6, 1, 10, 8, 0, 7, 4], 'cur_cost': 1146.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 9, 5, 6, 0, 8, 10, 1, 2, 7, 4], 'cur_cost': 962.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:43,600 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 1147.00)
2025-08-05 09:51:43,600 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:43,600 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:43,601 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [4, 10, 1, 7, 8, 5, 3, 6, 0, 2, 9], 'cur_cost': 1341.0, 'intermediate_solutions': [{'tour': [7, 2, 3, 6, 5, 8, 0, 4, 1, 9, 10], 'cur_cost': 1260.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 2, 3, 4, 1, 0, 8, 5, 6, 9, 10], 'cur_cost': 1221.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 2, 3, 6, 5, 8, 4, 0, 1, 9, 10], 'cur_cost': 1357.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [6, 10, 3, 7, 8, 9, 5, 1, 2, 4, 0], 'cur_cost': 1323.0, 'intermediate_solutions': [{'tour': [6, 5, 8, 0, 1, 7, 9, 4, 10, 2, 3], 'cur_cost': 1260.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 5, 8, 0, 1, 3, 9, 4, 10, 7, 2], 'cur_cost': 1278.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 8, 0, 1, 3, 9, 4, 6, 10, 2, 7], 'cur_cost': 1286.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 4,  1,  2,  8,  5,  3, 10,  9,  0,  7,  6], dtype=int64), 'cur_cost': 1461.0, 'intermediate_solutions': [{'tour': array([ 0,  8,  6,  4,  3,  1,  2, 10,  7,  9,  5]), 'cur_cost': 1426.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 4,  0,  8,  6,  3,  1,  2, 10,  7,  9,  5]), 'cur_cost': 1448.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3,  4,  0,  8,  6,  1,  2, 10,  7,  9,  5]), 'cur_cost': 1414.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6,  4,  0,  8,  3,  1,  2, 10,  7,  9,  5]), 'cur_cost': 1481.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6,  3,  4,  0,  8,  1,  2, 10,  7,  9,  5]), 'cur_cost': 1476.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [6, 5, 1, 2, 0, 9, 4, 7, 3, 10, 8], 'cur_cost': 1150.0, 'intermediate_solutions': [{'tour': [2, 1, 3, 4, 5, 9, 10, 0, 8, 7, 6], 'cur_cost': 1491.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 1, 3, 6, 5, 9, 10, 8, 0, 7, 4], 'cur_cost': 949.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 3, 6, 1, 5, 9, 10, 0, 8, 7, 4], 'cur_cost': 1168.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 9, 5, 3, 10, 8, 0, 6, 2, 4], 'cur_cost': 1064.0, 'intermediate_solutions': [{'tour': [1, 0, 9, 4, 2, 10, 5, 7, 3, 8, 6], 'cur_cost': 1306.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 9, 0, 8, 3, 7, 5, 10, 2, 4], 'cur_cost': 1388.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 9, 4, 2, 10, 5, 7, 3, 8, 0], 'cur_cost': 1300.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 2, 10,  9,  6,  3,  1,  4,  7,  8,  5,  0], dtype=int64), 'cur_cost': 1283.0, 'intermediate_solutions': [{'tour': array([ 7, 10,  0,  5,  3,  1,  8,  2,  9,  6,  4]), 'cur_cost': 1436.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 5,  7, 10,  0,  3,  1,  8,  2,  9,  6,  4]), 'cur_cost': 1704.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3,  5,  7, 10,  0,  1,  8,  2,  9,  6,  4]), 'cur_cost': 1640.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 0,  5,  7, 10,  3,  1,  8,  2,  9,  6,  4]), 'cur_cost': 1685.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 0,  3,  5,  7, 10,  1,  8,  2,  9,  6,  4]), 'cur_cost': 1730.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [1, 8, 4, 6, 9, 10, 5, 3, 2, 7, 0], 'cur_cost': 1392.0, 'intermediate_solutions': [{'tour': [3, 8, 6, 10, 0, 9, 5, 7, 2, 1, 4], 'cur_cost': 1322.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 6, 10, 1, 5, 9, 0, 2, 7, 4], 'cur_cost': 1266.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 8, 6, 10, 9, 5, 1, 2, 7, 4, 0], 'cur_cost': 1106.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [3, 4, 6, 8, 7, 9, 10, 5, 1, 2, 0], 'cur_cost': 1417.0, 'intermediate_solutions': [{'tour': [2, 8, 5, 0, 3, 9, 4, 7, 10, 1, 6], 'cur_cost': 1339.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 10, 0, 3, 9, 4, 7, 5, 6, 1], 'cur_cost': 1179.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 8, 0, 3, 9, 4, 7, 5, 1, 6, 10], 'cur_cost': 1332.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [1, 9, 3, 6, 10, 5, 8, 0, 2, 7, 4], 'cur_cost': 1039.0, 'intermediate_solutions': [{'tour': [7, 5, 8, 10, 1, 0, 3, 9, 4, 6, 2], 'cur_cost': 1297.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 6, 3, 9, 4, 0, 1, 10, 8, 2], 'cur_cost': 1321.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 5, 8, 10, 1, 0, 4, 9, 3, 6, 2], 'cur_cost': 1276.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [4, 5, 0, 10, 8, 6, 3, 1, 2, 7, 9], 'cur_cost': 1147.0, 'intermediate_solutions': [{'tour': [3, 2, 9, 5, 6, 1, 8, 10, 0, 7, 4], 'cur_cost': 1177.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 2, 9, 5, 6, 1, 10, 8, 0, 7, 4], 'cur_cost': 1146.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 9, 5, 6, 0, 8, 10, 1, 2, 7, 4], 'cur_cost': 962.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:51:43,601 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:43,601 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:43,602 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1039.000, 多样性=0.879
2025-08-05 09:51:43,602 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 09:51:43,602 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 09:51:43,603 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:43,603 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.03376300261344437, 'best_improvement': -0.028712871287128714}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.03333333333333342}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.10005841342625132, 'recent_improvements': [0.04601153765404451, 0.011375138467938662, -0.15410528919845812], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 803.0, 'new_best_cost': 803.0, 'quality_improvement': 0.0, 'old_diversity': 0.6666666666666666, 'new_diversity': 0.6666666666666666, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配']}
2025-08-05 09:51:43,603 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 09:51:43,603 - __main__ - INFO - simple4_11 开始进化第 3 代
2025-08-05 09:51:43,603 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 09:51:43,603 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:43,604 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1039.000, 多样性=0.879
2025-08-05 09:51:43,604 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:43,604 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.879
2025-08-05 09:51:43,605 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:43,605 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.667
2025-08-05 09:51:43,607 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 09:51:43,607 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:43,607 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 09:51:43,607 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 09:51:43,616 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.357, 适应度梯度: 55.957, 聚类评分: 0.000, 覆盖率: 0.020, 收敛趋势: 0.000, 多样性: 0.694
2025-08-05 09:51:43,616 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 09:51:43,616 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:51:43,617 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple4_11
2025-08-05 09:51:43,620 - visualization.landscape_visualizer - INFO - 插值约束: 220 个点被约束到最小值 803.00
2025-08-05 09:51:43,710 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple4_11\landscape_simple4_11_iter_18_20250805_095143.html
2025-08-05 09:51:43,757 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple4_11\dashboard_simple4_11_iter_18_20250805_095143.html
2025-08-05 09:51:43,757 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 18
2025-08-05 09:51:43,757 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 09:51:43,757 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1493秒
2025-08-05 09:51:43,758 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.35714285714285715, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 55.95714285714285, 'local_optima_density': 0.35714285714285715, 'gradient_variance': 81419.703877551, 'cluster_count': 0}, 'population_state': {'diversity': 0.6938775510204082, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0199, 'fitness_entropy': 0.9755037590061086, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.020)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 55.957)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358703.6167343, 'performance_metrics': {}}}
2025-08-05 09:51:43,758 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:43,758 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:43,758 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:43,758 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:43,759 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore']
2025-08-05 09:51:43,759 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 09:51:43,759 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore']
2025-08-05 09:51:43,759 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:43,759 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:43,760 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore']
2025-08-05 09:51:43,760 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:43,760 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:43,760 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 4} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:43,760 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:51:43,760 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:51:43,761 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:43,761 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 11
2025-08-05 09:51:43,761 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,761 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,762 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,762 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,762 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1196.0, 路径长度: 11, 收集中间解: 3
2025-08-05 09:51:43,762 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [9, 10, 5, 1, 3, 2, 7, 6, 0, 8, 4], 'cur_cost': 1196.0, 'intermediate_solutions': [{'tour': [4, 10, 1, 7, 8, 5, 3, 2, 0, 6, 9], 'cur_cost': 1399.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 10, 1, 7, 8, 5, 3, 6, 0, 9, 2], 'cur_cost': 1310.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 1, 7, 8, 5, 3, 10, 6, 0, 2, 9], 'cur_cost': 1221.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:43,762 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1196.00)
2025-08-05 09:51:43,762 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:51:43,762 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:51:43,762 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:43,763 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 11
2025-08-05 09:51:43,763 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,763 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,763 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,763 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,763 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1372.0, 路径长度: 11, 收集中间解: 3
2025-08-05 09:51:43,764 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [8, 5, 1, 3, 6, 2, 7, 9, 10, 0, 4], 'cur_cost': 1372.0, 'intermediate_solutions': [{'tour': [6, 2, 3, 7, 8, 9, 5, 1, 10, 4, 0], 'cur_cost': 1575.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 10, 3, 7, 8, 9, 5, 1, 2, 0, 4], 'cur_cost': 1570.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 10, 7, 8, 9, 5, 3, 1, 2, 4, 0], 'cur_cost': 1321.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:43,764 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1372.00)
2025-08-05 09:51:43,764 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-05 09:51:43,764 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:43,764 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:43,764 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 1520.0
2025-08-05 09:51:43,770 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:43,770 - ExploitationExpert - INFO - res_population_costs: [803.0, 803, 803, 803]
2025-08-05 09:51:43,770 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  2,  7,  4,  9, 10,  8,  5,  3,  6], dtype=int64), array([ 0,  1,  7,  4,  2,  9, 10,  8,  5,  3,  6], dtype=int64), array([ 0,  6,  8, 10,  5,  3,  9,  2,  4,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  4,  9,  3,  5, 10,  8,  6], dtype=int64)]
2025-08-05 09:51:43,771 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:43,771 - ExploitationExpert - INFO - populations: [{'tour': [9, 10, 5, 1, 3, 2, 7, 6, 0, 8, 4], 'cur_cost': 1196.0}, {'tour': [8, 5, 1, 3, 6, 2, 7, 9, 10, 0, 4], 'cur_cost': 1372.0}, {'tour': array([ 8,  0,  4,  5,  3,  1,  7,  6,  9, 10,  2], dtype=int64), 'cur_cost': 1520.0}, {'tour': [6, 5, 1, 2, 0, 9, 4, 7, 3, 10, 8], 'cur_cost': 1150.0}, {'tour': [1, 7, 9, 5, 3, 10, 8, 0, 6, 2, 4], 'cur_cost': 1064.0}, {'tour': [2, 10, 9, 6, 3, 1, 4, 7, 8, 5, 0], 'cur_cost': 1283.0}, {'tour': [1, 8, 4, 6, 9, 10, 5, 3, 2, 7, 0], 'cur_cost': 1392.0}, {'tour': [3, 4, 6, 8, 7, 9, 10, 5, 1, 2, 0], 'cur_cost': 1417.0}, {'tour': [1, 9, 3, 6, 10, 5, 8, 0, 2, 7, 4], 'cur_cost': 1039.0}, {'tour': [4, 5, 0, 10, 8, 6, 3, 1, 2, 7, 9], 'cur_cost': 1147.0}]
2025-08-05 09:51:43,772 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:43,772 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 44, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 44, 'cache_hits': 0, 'similarity_calculations': 298, 'cache_hit_rate': 0.0, 'cache_size': 298}}
2025-08-05 09:51:43,772 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([ 8,  0,  4,  5,  3,  1,  7,  6,  9, 10,  2], dtype=int64), 'cur_cost': 1520.0, 'intermediate_solutions': [{'tour': array([ 2,  1,  4,  8,  5,  3, 10,  9,  0,  7,  6]), 'cur_cost': 1460.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 8,  2,  1,  4,  5,  3, 10,  9,  0,  7,  6]), 'cur_cost': 1469.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 5,  8,  2,  1,  4,  3, 10,  9,  0,  7,  6]), 'cur_cost': 1409.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4,  8,  2,  1,  5,  3, 10,  9,  0,  7,  6]), 'cur_cost': 1630.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4,  5,  8,  2,  1,  3, 10,  9,  0,  7,  6]), 'cur_cost': 1534.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:43,773 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1520.00)
2025-08-05 09:51:43,773 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:51:43,773 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:51:43,773 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:43,773 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 11
2025-08-05 09:51:43,773 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,773 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,774 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,774 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,774 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1329.0, 路径长度: 11, 收集中间解: 3
2025-08-05 09:51:43,774 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [6, 7, 3, 1, 0, 5, 10, 9, 8, 2, 4], 'cur_cost': 1329.0, 'intermediate_solutions': [{'tour': [6, 5, 4, 2, 0, 9, 1, 7, 3, 10, 8], 'cur_cost': 1289.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 5, 1, 2, 0, 7, 4, 9, 3, 10, 8], 'cur_cost': 1110.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 5, 1, 2, 0, 9, 10, 4, 7, 3, 8], 'cur_cost': 1287.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:43,774 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1329.00)
2025-08-05 09:51:43,774 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:51:43,774 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:51:43,774 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:43,775 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 09:51:43,775 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,775 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,775 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,775 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,775 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1512.0, 路径长度: 11, 收集中间解: 3
2025-08-05 09:51:43,775 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [7, 10, 2, 9, 0, 6, 5, 3, 1, 4, 8], 'cur_cost': 1512.0, 'intermediate_solutions': [{'tour': [1, 7, 9, 5, 6, 10, 8, 0, 3, 2, 4], 'cur_cost': 1076.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 7, 9, 5, 3, 10, 2, 6, 0, 8, 4], 'cur_cost': 1342.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 9, 5, 3, 10, 8, 0, 6, 2, 1, 4], 'cur_cost': 1012.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:43,776 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 1512.00)
2025-08-05 09:51:43,776 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:51:43,776 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:51:43,776 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:43,777 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 11
2025-08-05 09:51:43,777 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,777 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,777 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,777 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,777 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1054.0, 路径长度: 11, 收集中间解: 3
2025-08-05 09:51:43,778 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [1, 2, 9, 5, 10, 3, 6, 8, 0, 7, 4], 'cur_cost': 1054.0, 'intermediate_solutions': [{'tour': [2, 4, 9, 6, 3, 1, 10, 7, 8, 5, 0], 'cur_cost': 1431.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 10, 9, 6, 3, 1, 4, 7, 5, 8, 0], 'cur_cost': 1239.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 10, 9, 6, 3, 1, 4, 7, 8, 5, 0], 'cur_cost': 1283.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:43,778 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1054.00)
2025-08-05 09:51:43,778 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:51:43,778 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:43,778 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:43,778 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1415.0
2025-08-05 09:51:43,786 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:43,786 - ExploitationExpert - INFO - res_population_costs: [803.0, 803, 803, 803]
2025-08-05 09:51:43,786 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  2,  7,  4,  9, 10,  8,  5,  3,  6], dtype=int64), array([ 0,  1,  7,  4,  2,  9, 10,  8,  5,  3,  6], dtype=int64), array([ 0,  6,  8, 10,  5,  3,  9,  2,  4,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  4,  9,  3,  5, 10,  8,  6], dtype=int64)]
2025-08-05 09:51:43,787 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:43,787 - ExploitationExpert - INFO - populations: [{'tour': [9, 10, 5, 1, 3, 2, 7, 6, 0, 8, 4], 'cur_cost': 1196.0}, {'tour': [8, 5, 1, 3, 6, 2, 7, 9, 10, 0, 4], 'cur_cost': 1372.0}, {'tour': array([ 8,  0,  4,  5,  3,  1,  7,  6,  9, 10,  2], dtype=int64), 'cur_cost': 1520.0}, {'tour': [6, 7, 3, 1, 0, 5, 10, 9, 8, 2, 4], 'cur_cost': 1329.0}, {'tour': [7, 10, 2, 9, 0, 6, 5, 3, 1, 4, 8], 'cur_cost': 1512.0}, {'tour': [1, 2, 9, 5, 10, 3, 6, 8, 0, 7, 4], 'cur_cost': 1054.0}, {'tour': array([ 4,  9,  6,  0,  8,  7,  1,  3,  5,  2, 10], dtype=int64), 'cur_cost': 1415.0}, {'tour': [3, 4, 6, 8, 7, 9, 10, 5, 1, 2, 0], 'cur_cost': 1417.0}, {'tour': [1, 9, 3, 6, 10, 5, 8, 0, 2, 7, 4], 'cur_cost': 1039.0}, {'tour': [4, 5, 0, 10, 8, 6, 3, 1, 2, 7, 9], 'cur_cost': 1147.0}]
2025-08-05 09:51:43,788 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:43,788 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 45, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 45, 'cache_hits': 0, 'similarity_calculations': 303, 'cache_hit_rate': 0.0, 'cache_size': 303}}
2025-08-05 09:51:43,788 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([ 4,  9,  6,  0,  8,  7,  1,  3,  5,  2, 10], dtype=int64), 'cur_cost': 1415.0, 'intermediate_solutions': [{'tour': array([ 4,  8,  1,  6,  9, 10,  5,  3,  2,  7,  0]), 'cur_cost': 1401.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 6,  4,  8,  1,  9, 10,  5,  3,  2,  7,  0]), 'cur_cost': 1333.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9,  6,  4,  8,  1, 10,  5,  3,  2,  7,  0]), 'cur_cost': 1546.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 1,  6,  4,  8,  9, 10,  5,  3,  2,  7,  0]), 'cur_cost': 1315.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 1,  9,  6,  4,  8, 10,  5,  3,  2,  7,  0]), 'cur_cost': 1337.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:43,789 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1415.00)
2025-08-05 09:51:43,789 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 09:51:43,789 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:43,789 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:43,789 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 1322.0
2025-08-05 09:51:43,795 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:43,795 - ExploitationExpert - INFO - res_population_costs: [803.0, 803, 803, 803]
2025-08-05 09:51:43,795 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  2,  7,  4,  9, 10,  8,  5,  3,  6], dtype=int64), array([ 0,  1,  7,  4,  2,  9, 10,  8,  5,  3,  6], dtype=int64), array([ 0,  6,  8, 10,  5,  3,  9,  2,  4,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  4,  9,  3,  5, 10,  8,  6], dtype=int64)]
2025-08-05 09:51:43,796 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:43,796 - ExploitationExpert - INFO - populations: [{'tour': [9, 10, 5, 1, 3, 2, 7, 6, 0, 8, 4], 'cur_cost': 1196.0}, {'tour': [8, 5, 1, 3, 6, 2, 7, 9, 10, 0, 4], 'cur_cost': 1372.0}, {'tour': array([ 8,  0,  4,  5,  3,  1,  7,  6,  9, 10,  2], dtype=int64), 'cur_cost': 1520.0}, {'tour': [6, 7, 3, 1, 0, 5, 10, 9, 8, 2, 4], 'cur_cost': 1329.0}, {'tour': [7, 10, 2, 9, 0, 6, 5, 3, 1, 4, 8], 'cur_cost': 1512.0}, {'tour': [1, 2, 9, 5, 10, 3, 6, 8, 0, 7, 4], 'cur_cost': 1054.0}, {'tour': array([ 4,  9,  6,  0,  8,  7,  1,  3,  5,  2, 10], dtype=int64), 'cur_cost': 1415.0}, {'tour': array([ 5,  9,  7,  3,  6, 10,  8,  2,  1,  4,  0], dtype=int64), 'cur_cost': 1322.0}, {'tour': [1, 9, 3, 6, 10, 5, 8, 0, 2, 7, 4], 'cur_cost': 1039.0}, {'tour': [4, 5, 0, 10, 8, 6, 3, 1, 2, 7, 9], 'cur_cost': 1147.0}]
2025-08-05 09:51:43,797 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:43,797 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 46, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 46, 'cache_hits': 0, 'similarity_calculations': 309, 'cache_hit_rate': 0.0, 'cache_size': 309}}
2025-08-05 09:51:43,797 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([ 5,  9,  7,  3,  6, 10,  8,  2,  1,  4,  0], dtype=int64), 'cur_cost': 1322.0, 'intermediate_solutions': [{'tour': array([ 6,  4,  3,  8,  7,  9, 10,  5,  1,  2,  0]), 'cur_cost': 1385.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 8,  6,  4,  3,  7,  9, 10,  5,  1,  2,  0]), 'cur_cost': 1326.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 7,  8,  6,  4,  3,  9, 10,  5,  1,  2,  0]), 'cur_cost': 1443.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 3,  8,  6,  4,  7,  9, 10,  5,  1,  2,  0]), 'cur_cost': 1192.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 3,  7,  8,  6,  4,  9, 10,  5,  1,  2,  0]), 'cur_cost': 1386.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:43,797 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1322.00)
2025-08-05 09:51:43,798 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:51:43,798 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:51:43,798 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:43,798 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 11
2025-08-05 09:51:43,798 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,799 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,799 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,799 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,799 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1058.0, 路径长度: 11, 收集中间解: 3
2025-08-05 09:51:43,799 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [1, 3, 10, 6, 0, 2, 4, 7, 9, 5, 8], 'cur_cost': 1058.0, 'intermediate_solutions': [{'tour': [1, 9, 3, 6, 5, 10, 8, 0, 2, 7, 4], 'cur_cost': 997.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 9, 3, 6, 10, 4, 7, 2, 0, 8, 5], 'cur_cost': 1193.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 9, 3, 2, 6, 10, 5, 8, 0, 7, 4], 'cur_cost': 1152.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:43,799 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 1058.00)
2025-08-05 09:51:43,799 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:51:43,799 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:51:43,799 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:43,800 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 09:51:43,800 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,800 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,800 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,800 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,800 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1099.0, 路径长度: 11, 收集中间解: 3
2025-08-05 09:51:43,801 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [5, 7, 2, 3, 8, 10, 6, 0, 1, 4, 9], 'cur_cost': 1099.0, 'intermediate_solutions': [{'tour': [4, 5, 0, 10, 8, 6, 3, 9, 2, 7, 1], 'cur_cost': 1145.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 5, 0, 10, 8, 6, 7, 2, 1, 3, 9], 'cur_cost': 1201.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 4, 5, 0, 10, 8, 3, 1, 2, 7, 9], 'cur_cost': 1323.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:43,801 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1099.00)
2025-08-05 09:51:43,801 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:43,801 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:43,802 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [9, 10, 5, 1, 3, 2, 7, 6, 0, 8, 4], 'cur_cost': 1196.0, 'intermediate_solutions': [{'tour': [4, 10, 1, 7, 8, 5, 3, 2, 0, 6, 9], 'cur_cost': 1399.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 10, 1, 7, 8, 5, 3, 6, 0, 9, 2], 'cur_cost': 1310.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 1, 7, 8, 5, 3, 10, 6, 0, 2, 9], 'cur_cost': 1221.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [8, 5, 1, 3, 6, 2, 7, 9, 10, 0, 4], 'cur_cost': 1372.0, 'intermediate_solutions': [{'tour': [6, 2, 3, 7, 8, 9, 5, 1, 10, 4, 0], 'cur_cost': 1575.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 10, 3, 7, 8, 9, 5, 1, 2, 0, 4], 'cur_cost': 1570.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 10, 7, 8, 9, 5, 3, 1, 2, 4, 0], 'cur_cost': 1321.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 8,  0,  4,  5,  3,  1,  7,  6,  9, 10,  2], dtype=int64), 'cur_cost': 1520.0, 'intermediate_solutions': [{'tour': array([ 2,  1,  4,  8,  5,  3, 10,  9,  0,  7,  6]), 'cur_cost': 1460.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 8,  2,  1,  4,  5,  3, 10,  9,  0,  7,  6]), 'cur_cost': 1469.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 5,  8,  2,  1,  4,  3, 10,  9,  0,  7,  6]), 'cur_cost': 1409.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4,  8,  2,  1,  5,  3, 10,  9,  0,  7,  6]), 'cur_cost': 1630.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4,  5,  8,  2,  1,  3, 10,  9,  0,  7,  6]), 'cur_cost': 1534.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [6, 7, 3, 1, 0, 5, 10, 9, 8, 2, 4], 'cur_cost': 1329.0, 'intermediate_solutions': [{'tour': [6, 5, 4, 2, 0, 9, 1, 7, 3, 10, 8], 'cur_cost': 1289.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 5, 1, 2, 0, 7, 4, 9, 3, 10, 8], 'cur_cost': 1110.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 5, 1, 2, 0, 9, 10, 4, 7, 3, 8], 'cur_cost': 1287.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [7, 10, 2, 9, 0, 6, 5, 3, 1, 4, 8], 'cur_cost': 1512.0, 'intermediate_solutions': [{'tour': [1, 7, 9, 5, 6, 10, 8, 0, 3, 2, 4], 'cur_cost': 1076.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 7, 9, 5, 3, 10, 2, 6, 0, 8, 4], 'cur_cost': 1342.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 9, 5, 3, 10, 8, 0, 6, 2, 1, 4], 'cur_cost': 1012.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [1, 2, 9, 5, 10, 3, 6, 8, 0, 7, 4], 'cur_cost': 1054.0, 'intermediate_solutions': [{'tour': [2, 4, 9, 6, 3, 1, 10, 7, 8, 5, 0], 'cur_cost': 1431.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 10, 9, 6, 3, 1, 4, 7, 5, 8, 0], 'cur_cost': 1239.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 10, 9, 6, 3, 1, 4, 7, 8, 5, 0], 'cur_cost': 1283.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 4,  9,  6,  0,  8,  7,  1,  3,  5,  2, 10], dtype=int64), 'cur_cost': 1415.0, 'intermediate_solutions': [{'tour': array([ 4,  8,  1,  6,  9, 10,  5,  3,  2,  7,  0]), 'cur_cost': 1401.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 6,  4,  8,  1,  9, 10,  5,  3,  2,  7,  0]), 'cur_cost': 1333.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9,  6,  4,  8,  1, 10,  5,  3,  2,  7,  0]), 'cur_cost': 1546.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 1,  6,  4,  8,  9, 10,  5,  3,  2,  7,  0]), 'cur_cost': 1315.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 1,  9,  6,  4,  8, 10,  5,  3,  2,  7,  0]), 'cur_cost': 1337.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 5,  9,  7,  3,  6, 10,  8,  2,  1,  4,  0], dtype=int64), 'cur_cost': 1322.0, 'intermediate_solutions': [{'tour': array([ 6,  4,  3,  8,  7,  9, 10,  5,  1,  2,  0]), 'cur_cost': 1385.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 8,  6,  4,  3,  7,  9, 10,  5,  1,  2,  0]), 'cur_cost': 1326.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 7,  8,  6,  4,  3,  9, 10,  5,  1,  2,  0]), 'cur_cost': 1443.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 3,  8,  6,  4,  7,  9, 10,  5,  1,  2,  0]), 'cur_cost': 1192.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 3,  7,  8,  6,  4,  9, 10,  5,  1,  2,  0]), 'cur_cost': 1386.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [1, 3, 10, 6, 0, 2, 4, 7, 9, 5, 8], 'cur_cost': 1058.0, 'intermediate_solutions': [{'tour': [1, 9, 3, 6, 5, 10, 8, 0, 2, 7, 4], 'cur_cost': 997.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 9, 3, 6, 10, 4, 7, 2, 0, 8, 5], 'cur_cost': 1193.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 9, 3, 2, 6, 10, 5, 8, 0, 7, 4], 'cur_cost': 1152.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [5, 7, 2, 3, 8, 10, 6, 0, 1, 4, 9], 'cur_cost': 1099.0, 'intermediate_solutions': [{'tour': [4, 5, 0, 10, 8, 6, 3, 9, 2, 7, 1], 'cur_cost': 1145.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 5, 0, 10, 8, 6, 7, 2, 1, 3, 9], 'cur_cost': 1201.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 4, 5, 0, 10, 8, 3, 1, 2, 7, 9], 'cur_cost': 1323.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:51:43,802 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:43,803 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:43,804 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1054.000, 多样性=0.909
2025-08-05 09:51:43,804 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 09:51:43,804 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 09:51:43,804 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:43,804 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.025142458368520467, 'best_improvement': -0.014436958614051972}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.03448275862068963}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.011193932072752858, 'recent_improvements': [0.011375138467938662, -0.15410528919845812, 0.03376300261344437], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 803.0, 'new_best_cost': 803.0, 'quality_improvement': 0.0, 'old_diversity': 0.6666666666666666, 'new_diversity': 0.6666666666666666, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:51:43,805 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 09:51:43,805 - __main__ - INFO - simple4_11 开始进化第 4 代
2025-08-05 09:51:43,805 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 09:51:43,805 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:43,807 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1054.000, 多样性=0.909
2025-08-05 09:51:43,807 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:43,809 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.909
2025-08-05 09:51:43,809 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:43,810 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.667
2025-08-05 09:51:43,814 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 09:51:43,814 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:43,814 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 09:51:43,815 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 09:51:43,831 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.357, 适应度梯度: 67.214, 聚类评分: 0.000, 覆盖率: 0.021, 收敛趋势: 0.000, 多样性: 0.721
2025-08-05 09:51:43,831 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 09:51:43,831 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:51:43,832 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple4_11
2025-08-05 09:51:43,838 - visualization.landscape_visualizer - INFO - 插值约束: 103 个点被约束到最小值 803.00
2025-08-05 09:51:43,938 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple4_11\landscape_simple4_11_iter_19_20250805_095143.html
2025-08-05 09:51:43,976 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple4_11\dashboard_simple4_11_iter_19_20250805_095143.html
2025-08-05 09:51:43,976 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 19
2025-08-05 09:51:43,977 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 09:51:43,977 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1636秒
2025-08-05 09:51:43,977 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.35714285714285715, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 67.21428571428571, 'local_optima_density': 0.35714285714285715, 'gradient_variance': 50628.80836734694, 'cluster_count': 0}, 'population_state': {'diversity': 0.7205651491365777, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0209, 'fitness_entropy': 0.929906376575379, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.021)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.721)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 67.214)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358703.8310254, 'performance_metrics': {}}}
2025-08-05 09:51:43,977 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:43,977 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:43,977 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:43,978 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:43,978 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:51:43,978 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 09:51:43,978 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:51:43,978 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:43,978 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:43,978 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:51:43,979 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:43,979 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:43,979 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 5} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:43,979 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:51:43,979 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:51:43,979 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:43,980 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 11
2025-08-05 09:51:43,980 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,980 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,980 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,981 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,981 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1139.0, 路径长度: 11, 收集中间解: 3
2025-08-05 09:51:43,981 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [7, 1, 6, 5, 8, 10, 3, 2, 4, 9, 0], 'cur_cost': 1139.0, 'intermediate_solutions': [{'tour': [9, 10, 5, 4, 3, 2, 7, 6, 0, 8, 1], 'cur_cost': 1275.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 10, 9, 1, 3, 2, 7, 6, 0, 8, 4], 'cur_cost': 1283.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 10, 5, 1, 3, 2, 7, 6, 0, 8, 4], 'cur_cost': 1196.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:43,981 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1139.00)
2025-08-05 09:51:43,981 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:51:43,981 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:51:43,981 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:43,982 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 11
2025-08-05 09:51:43,982 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,982 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,982 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,982 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,983 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1387.0, 路径长度: 11, 收集中间解: 3
2025-08-05 09:51:43,983 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [6, 3, 2, 9, 10, 1, 7, 5, 8, 0, 4], 'cur_cost': 1387.0, 'intermediate_solutions': [{'tour': [8, 5, 1, 3, 6, 2, 9, 7, 10, 0, 4], 'cur_cost': 1530.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 5, 1, 2, 6, 3, 7, 9, 10, 0, 4], 'cur_cost': 1447.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 5, 2, 1, 3, 6, 7, 9, 10, 0, 4], 'cur_cost': 1439.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:43,983 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1387.00)
2025-08-05 09:51:43,983 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-05 09:51:43,983 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:43,983 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:43,983 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 1272.0
2025-08-05 09:51:43,989 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:43,989 - ExploitationExpert - INFO - res_population_costs: [803.0, 803, 803, 803]
2025-08-05 09:51:43,989 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  2,  7,  4,  9, 10,  8,  5,  3,  6], dtype=int64), array([ 0,  1,  7,  4,  2,  9, 10,  8,  5,  3,  6], dtype=int64), array([ 0,  6,  8, 10,  5,  3,  9,  2,  4,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  4,  9,  3,  5, 10,  8,  6], dtype=int64)]
2025-08-05 09:51:43,990 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:43,991 - ExploitationExpert - INFO - populations: [{'tour': [7, 1, 6, 5, 8, 10, 3, 2, 4, 9, 0], 'cur_cost': 1139.0}, {'tour': [6, 3, 2, 9, 10, 1, 7, 5, 8, 0, 4], 'cur_cost': 1387.0}, {'tour': array([10,  6,  0,  5,  7,  9,  3,  4,  1,  2,  8], dtype=int64), 'cur_cost': 1272.0}, {'tour': [6, 7, 3, 1, 0, 5, 10, 9, 8, 2, 4], 'cur_cost': 1329.0}, {'tour': [7, 10, 2, 9, 0, 6, 5, 3, 1, 4, 8], 'cur_cost': 1512.0}, {'tour': [1, 2, 9, 5, 10, 3, 6, 8, 0, 7, 4], 'cur_cost': 1054.0}, {'tour': [4, 9, 6, 0, 8, 7, 1, 3, 5, 2, 10], 'cur_cost': 1415.0}, {'tour': [5, 9, 7, 3, 6, 10, 8, 2, 1, 4, 0], 'cur_cost': 1322.0}, {'tour': [1, 3, 10, 6, 0, 2, 4, 7, 9, 5, 8], 'cur_cost': 1058.0}, {'tour': [5, 7, 2, 3, 8, 10, 6, 0, 1, 4, 9], 'cur_cost': 1099.0}]
2025-08-05 09:51:43,991 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:43,991 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 47, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 47, 'cache_hits': 0, 'similarity_calculations': 316, 'cache_hit_rate': 0.0, 'cache_size': 316}}
2025-08-05 09:51:43,992 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([10,  6,  0,  5,  7,  9,  3,  4,  1,  2,  8], dtype=int64), 'cur_cost': 1272.0, 'intermediate_solutions': [{'tour': array([ 4,  0,  8,  5,  3,  1,  7,  6,  9, 10,  2]), 'cur_cost': 1279.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 5,  4,  0,  8,  3,  1,  7,  6,  9, 10,  2]), 'cur_cost': 1519.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3,  5,  4,  0,  8,  1,  7,  6,  9, 10,  2]), 'cur_cost': 1511.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 8,  5,  4,  0,  3,  1,  7,  6,  9, 10,  2]), 'cur_cost': 1512.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 8,  3,  5,  4,  0,  1,  7,  6,  9, 10,  2]), 'cur_cost': 1543.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:43,992 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1272.00)
2025-08-05 09:51:43,992 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:51:43,992 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:51:43,992 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:43,992 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 09:51:43,992 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,992 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,993 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,993 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,993 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1345.0, 路径长度: 11, 收集中间解: 3
2025-08-05 09:51:43,993 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [4, 8, 1, 5, 6, 0, 3, 2, 7, 9, 10], 'cur_cost': 1345.0, 'intermediate_solutions': [{'tour': [6, 7, 2, 1, 0, 5, 10, 9, 8, 3, 4], 'cur_cost': 1270.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 3, 1, 0, 5, 8, 9, 10, 2, 4], 'cur_cost': 1318.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 7, 3, 1, 4, 0, 5, 10, 9, 8, 2], 'cur_cost': 1441.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:43,993 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1345.00)
2025-08-05 09:51:43,993 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 09:51:43,993 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:43,993 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:43,994 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 1330.0
2025-08-05 09:51:43,999 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:43,999 - ExploitationExpert - INFO - res_population_costs: [803.0, 803, 803, 803]
2025-08-05 09:51:43,999 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  2,  7,  4,  9, 10,  8,  5,  3,  6], dtype=int64), array([ 0,  1,  7,  4,  2,  9, 10,  8,  5,  3,  6], dtype=int64), array([ 0,  6,  8, 10,  5,  3,  9,  2,  4,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  4,  9,  3,  5, 10,  8,  6], dtype=int64)]
2025-08-05 09:51:44,000 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:44,000 - ExploitationExpert - INFO - populations: [{'tour': [7, 1, 6, 5, 8, 10, 3, 2, 4, 9, 0], 'cur_cost': 1139.0}, {'tour': [6, 3, 2, 9, 10, 1, 7, 5, 8, 0, 4], 'cur_cost': 1387.0}, {'tour': array([10,  6,  0,  5,  7,  9,  3,  4,  1,  2,  8], dtype=int64), 'cur_cost': 1272.0}, {'tour': [4, 8, 1, 5, 6, 0, 3, 2, 7, 9, 10], 'cur_cost': 1345.0}, {'tour': array([10,  9,  3,  0,  4,  6,  8,  1,  2,  7,  5], dtype=int64), 'cur_cost': 1330.0}, {'tour': [1, 2, 9, 5, 10, 3, 6, 8, 0, 7, 4], 'cur_cost': 1054.0}, {'tour': [4, 9, 6, 0, 8, 7, 1, 3, 5, 2, 10], 'cur_cost': 1415.0}, {'tour': [5, 9, 7, 3, 6, 10, 8, 2, 1, 4, 0], 'cur_cost': 1322.0}, {'tour': [1, 3, 10, 6, 0, 2, 4, 7, 9, 5, 8], 'cur_cost': 1058.0}, {'tour': [5, 7, 2, 3, 8, 10, 6, 0, 1, 4, 9], 'cur_cost': 1099.0}]
2025-08-05 09:51:44,001 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:44,002 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 48, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 48, 'cache_hits': 0, 'similarity_calculations': 324, 'cache_hit_rate': 0.0, 'cache_size': 324}}
2025-08-05 09:51:44,002 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([10,  9,  3,  0,  4,  6,  8,  1,  2,  7,  5], dtype=int64), 'cur_cost': 1330.0, 'intermediate_solutions': [{'tour': array([ 2, 10,  7,  9,  0,  6,  5,  3,  1,  4,  8]), 'cur_cost': 1510.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9,  2, 10,  7,  0,  6,  5,  3,  1,  4,  8]), 'cur_cost': 1411.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 0,  9,  2, 10,  7,  6,  5,  3,  1,  4,  8]), 'cur_cost': 1531.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 7,  9,  2, 10,  0,  6,  5,  3,  1,  4,  8]), 'cur_cost': 1401.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 7,  0,  9,  2, 10,  6,  5,  3,  1,  4,  8]), 'cur_cost': 1539.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:44,002 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1330.00)
2025-08-05 09:51:44,002 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:51:44,002 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:51:44,002 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:44,003 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 11
2025-08-05 09:51:44,003 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,003 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,003 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,004 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,004 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1228.0, 路径长度: 11, 收集中间解: 3
2025-08-05 09:51:44,004 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [7, 1, 9, 5, 10, 3, 4, 2, 0, 6, 8], 'cur_cost': 1228.0, 'intermediate_solutions': [{'tour': [1, 10, 9, 5, 2, 3, 6, 8, 0, 7, 4], 'cur_cost': 1191.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 2, 9, 5, 10, 3, 6, 8, 0, 4, 7], 'cur_cost': 1055.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 9, 2, 5, 10, 3, 6, 8, 0, 7, 4], 'cur_cost': 1153.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:44,004 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 1228.00)
2025-08-05 09:51:44,004 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:51:44,004 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:44,004 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:44,004 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1310.0
2025-08-05 09:51:44,010 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:44,010 - ExploitationExpert - INFO - res_population_costs: [803.0, 803, 803, 803]
2025-08-05 09:51:44,010 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  2,  7,  4,  9, 10,  8,  5,  3,  6], dtype=int64), array([ 0,  1,  7,  4,  2,  9, 10,  8,  5,  3,  6], dtype=int64), array([ 0,  6,  8, 10,  5,  3,  9,  2,  4,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  4,  9,  3,  5, 10,  8,  6], dtype=int64)]
2025-08-05 09:51:44,011 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:44,011 - ExploitationExpert - INFO - populations: [{'tour': [7, 1, 6, 5, 8, 10, 3, 2, 4, 9, 0], 'cur_cost': 1139.0}, {'tour': [6, 3, 2, 9, 10, 1, 7, 5, 8, 0, 4], 'cur_cost': 1387.0}, {'tour': array([10,  6,  0,  5,  7,  9,  3,  4,  1,  2,  8], dtype=int64), 'cur_cost': 1272.0}, {'tour': [4, 8, 1, 5, 6, 0, 3, 2, 7, 9, 10], 'cur_cost': 1345.0}, {'tour': array([10,  9,  3,  0,  4,  6,  8,  1,  2,  7,  5], dtype=int64), 'cur_cost': 1330.0}, {'tour': [7, 1, 9, 5, 10, 3, 4, 2, 0, 6, 8], 'cur_cost': 1228.0}, {'tour': array([ 4,  7, 10,  6,  9,  8,  5,  2,  0,  1,  3], dtype=int64), 'cur_cost': 1310.0}, {'tour': [5, 9, 7, 3, 6, 10, 8, 2, 1, 4, 0], 'cur_cost': 1322.0}, {'tour': [1, 3, 10, 6, 0, 2, 4, 7, 9, 5, 8], 'cur_cost': 1058.0}, {'tour': [5, 7, 2, 3, 8, 10, 6, 0, 1, 4, 9], 'cur_cost': 1099.0}]
2025-08-05 09:51:44,012 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:44,012 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 49, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 49, 'cache_hits': 0, 'similarity_calculations': 333, 'cache_hit_rate': 0.0, 'cache_size': 333}}
2025-08-05 09:51:44,012 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([ 4,  7, 10,  6,  9,  8,  5,  2,  0,  1,  3], dtype=int64), 'cur_cost': 1310.0, 'intermediate_solutions': [{'tour': array([ 6,  9,  4,  0,  8,  7,  1,  3,  5,  2, 10]), 'cur_cost': 1468.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 0,  6,  9,  4,  8,  7,  1,  3,  5,  2, 10]), 'cur_cost': 1468.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 8,  0,  6,  9,  4,  7,  1,  3,  5,  2, 10]), 'cur_cost': 1062.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4,  0,  6,  9,  8,  7,  1,  3,  5,  2, 10]), 'cur_cost': 1518.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4,  8,  0,  6,  9,  7,  1,  3,  5,  2, 10]), 'cur_cost': 1443.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:44,012 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1310.00)
2025-08-05 09:51:44,013 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:51:44,013 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:51:44,013 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:44,013 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 11
2025-08-05 09:51:44,013 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,013 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,014 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,014 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,014 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1199.0, 路径长度: 11, 收集中间解: 3
2025-08-05 09:51:44,014 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [8, 3, 1, 7, 2, 5, 6, 10, 9, 4, 0], 'cur_cost': 1199.0, 'intermediate_solutions': [{'tour': [5, 9, 6, 3, 7, 10, 8, 2, 1, 4, 0], 'cur_cost': 1435.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 9, 7, 3, 6, 10, 2, 8, 1, 4, 0], 'cur_cost': 1511.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 9, 7, 6, 10, 8, 3, 2, 1, 4, 0], 'cur_cost': 1309.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:44,014 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1199.00)
2025-08-05 09:51:44,014 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:51:44,014 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:51:44,015 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:44,015 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 11
2025-08-05 09:51:44,015 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,015 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,015 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,016 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,016 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1103.0, 路径长度: 11, 收集中间解: 3
2025-08-05 09:51:44,016 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [8, 6, 3, 1, 7, 2, 4, 5, 10, 9, 0], 'cur_cost': 1103.0, 'intermediate_solutions': [{'tour': [1, 3, 10, 6, 0, 2, 4, 5, 9, 7, 8], 'cur_cost': 1378.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 3, 10, 6, 0, 2, 5, 9, 7, 4, 8], 'cur_cost': 1299.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 3, 10, 7, 6, 0, 2, 4, 9, 5, 8], 'cur_cost': 1292.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:44,016 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 1103.00)
2025-08-05 09:51:44,016 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:51:44,016 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:51:44,016 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:44,017 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 11
2025-08-05 09:51:44,017 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,017 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,017 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,017 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,017 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1475.0, 路径长度: 11, 收集中间解: 3
2025-08-05 09:51:44,017 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [5, 8, 6, 9, 1, 7, 10, 0, 2, 3, 4], 'cur_cost': 1475.0, 'intermediate_solutions': [{'tour': [5, 6, 2, 3, 8, 10, 7, 0, 1, 4, 9], 'cur_cost': 1314.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 7, 2, 3, 8, 10, 6, 0, 1, 9, 4], 'cur_cost': 1207.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 2, 1, 3, 8, 10, 6, 0, 4, 9], 'cur_cost': 1150.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:44,018 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1475.00)
2025-08-05 09:51:44,018 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:44,018 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:44,019 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [7, 1, 6, 5, 8, 10, 3, 2, 4, 9, 0], 'cur_cost': 1139.0, 'intermediate_solutions': [{'tour': [9, 10, 5, 4, 3, 2, 7, 6, 0, 8, 1], 'cur_cost': 1275.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 10, 9, 1, 3, 2, 7, 6, 0, 8, 4], 'cur_cost': 1283.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 10, 5, 1, 3, 2, 7, 6, 0, 8, 4], 'cur_cost': 1196.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [6, 3, 2, 9, 10, 1, 7, 5, 8, 0, 4], 'cur_cost': 1387.0, 'intermediate_solutions': [{'tour': [8, 5, 1, 3, 6, 2, 9, 7, 10, 0, 4], 'cur_cost': 1530.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 5, 1, 2, 6, 3, 7, 9, 10, 0, 4], 'cur_cost': 1447.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 5, 2, 1, 3, 6, 7, 9, 10, 0, 4], 'cur_cost': 1439.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([10,  6,  0,  5,  7,  9,  3,  4,  1,  2,  8], dtype=int64), 'cur_cost': 1272.0, 'intermediate_solutions': [{'tour': array([ 4,  0,  8,  5,  3,  1,  7,  6,  9, 10,  2]), 'cur_cost': 1279.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 5,  4,  0,  8,  3,  1,  7,  6,  9, 10,  2]), 'cur_cost': 1519.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3,  5,  4,  0,  8,  1,  7,  6,  9, 10,  2]), 'cur_cost': 1511.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 8,  5,  4,  0,  3,  1,  7,  6,  9, 10,  2]), 'cur_cost': 1512.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 8,  3,  5,  4,  0,  1,  7,  6,  9, 10,  2]), 'cur_cost': 1543.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [4, 8, 1, 5, 6, 0, 3, 2, 7, 9, 10], 'cur_cost': 1345.0, 'intermediate_solutions': [{'tour': [6, 7, 2, 1, 0, 5, 10, 9, 8, 3, 4], 'cur_cost': 1270.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 3, 1, 0, 5, 8, 9, 10, 2, 4], 'cur_cost': 1318.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 7, 3, 1, 4, 0, 5, 10, 9, 8, 2], 'cur_cost': 1441.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([10,  9,  3,  0,  4,  6,  8,  1,  2,  7,  5], dtype=int64), 'cur_cost': 1330.0, 'intermediate_solutions': [{'tour': array([ 2, 10,  7,  9,  0,  6,  5,  3,  1,  4,  8]), 'cur_cost': 1510.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9,  2, 10,  7,  0,  6,  5,  3,  1,  4,  8]), 'cur_cost': 1411.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 0,  9,  2, 10,  7,  6,  5,  3,  1,  4,  8]), 'cur_cost': 1531.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 7,  9,  2, 10,  0,  6,  5,  3,  1,  4,  8]), 'cur_cost': 1401.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 7,  0,  9,  2, 10,  6,  5,  3,  1,  4,  8]), 'cur_cost': 1539.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [7, 1, 9, 5, 10, 3, 4, 2, 0, 6, 8], 'cur_cost': 1228.0, 'intermediate_solutions': [{'tour': [1, 10, 9, 5, 2, 3, 6, 8, 0, 7, 4], 'cur_cost': 1191.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 2, 9, 5, 10, 3, 6, 8, 0, 4, 7], 'cur_cost': 1055.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 9, 2, 5, 10, 3, 6, 8, 0, 7, 4], 'cur_cost': 1153.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 4,  7, 10,  6,  9,  8,  5,  2,  0,  1,  3], dtype=int64), 'cur_cost': 1310.0, 'intermediate_solutions': [{'tour': array([ 6,  9,  4,  0,  8,  7,  1,  3,  5,  2, 10]), 'cur_cost': 1468.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 0,  6,  9,  4,  8,  7,  1,  3,  5,  2, 10]), 'cur_cost': 1468.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 8,  0,  6,  9,  4,  7,  1,  3,  5,  2, 10]), 'cur_cost': 1062.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4,  0,  6,  9,  8,  7,  1,  3,  5,  2, 10]), 'cur_cost': 1518.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4,  8,  0,  6,  9,  7,  1,  3,  5,  2, 10]), 'cur_cost': 1443.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [8, 3, 1, 7, 2, 5, 6, 10, 9, 4, 0], 'cur_cost': 1199.0, 'intermediate_solutions': [{'tour': [5, 9, 6, 3, 7, 10, 8, 2, 1, 4, 0], 'cur_cost': 1435.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 9, 7, 3, 6, 10, 2, 8, 1, 4, 0], 'cur_cost': 1511.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 9, 7, 6, 10, 8, 3, 2, 1, 4, 0], 'cur_cost': 1309.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [8, 6, 3, 1, 7, 2, 4, 5, 10, 9, 0], 'cur_cost': 1103.0, 'intermediate_solutions': [{'tour': [1, 3, 10, 6, 0, 2, 4, 5, 9, 7, 8], 'cur_cost': 1378.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 3, 10, 6, 0, 2, 5, 9, 7, 4, 8], 'cur_cost': 1299.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 3, 10, 7, 6, 0, 2, 4, 9, 5, 8], 'cur_cost': 1292.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [5, 8, 6, 9, 1, 7, 10, 0, 2, 3, 4], 'cur_cost': 1475.0, 'intermediate_solutions': [{'tour': [5, 6, 2, 3, 8, 10, 7, 0, 1, 4, 9], 'cur_cost': 1314.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 7, 2, 3, 8, 10, 6, 0, 1, 9, 4], 'cur_cost': 1207.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 2, 1, 3, 8, 10, 6, 0, 4, 9], 'cur_cost': 1150.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:51:44,019 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:44,019 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:44,020 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1103.000, 多样性=0.917
2025-08-05 09:51:44,020 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 09:51:44,020 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 09:51:44,021 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:44,021 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.0033242508962417366, 'best_improvement': -0.046489563567362426}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.008888888888888946}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.06448141541496882, 'recent_improvements': [-0.15410528919845812, 0.03376300261344437, -0.025142458368520467], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 803.0, 'new_best_cost': 803.0, 'quality_improvement': 0.0, 'old_diversity': 0.6666666666666666, 'new_diversity': 0.6666666666666666, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:51:44,021 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 09:51:44,021 - __main__ - INFO - simple4_11 开始进化第 5 代
2025-08-05 09:51:44,021 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 09:51:44,021 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:44,022 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1103.000, 多样性=0.917
2025-08-05 09:51:44,022 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:44,023 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.917
2025-08-05 09:51:44,023 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:44,023 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.667
2025-08-05 09:51:44,025 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 09:51:44,025 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:44,025 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 09:51:44,025 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 09:51:44,034 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.286, 适应度梯度: 6.043, 聚类评分: 0.000, 覆盖率: 0.022, 收敛趋势: 0.000, 多样性: 0.709
2025-08-05 09:51:44,034 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 09:51:44,034 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:51:44,035 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple4_11
2025-08-05 09:51:44,038 - visualization.landscape_visualizer - INFO - 插值约束: 96 个点被约束到最小值 803.00
2025-08-05 09:51:44,114 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple4_11\landscape_simple4_11_iter_20_20250805_095144.html
2025-08-05 09:51:44,152 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple4_11\dashboard_simple4_11_iter_20_20250805_095144.html
2025-08-05 09:51:44,152 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 20
2025-08-05 09:51:44,152 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 09:51:44,152 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1274秒
2025-08-05 09:51:44,152 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2857142857142857, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 6.0428571428571285, 'local_optima_density': 0.2857142857142857, 'gradient_variance': 56356.64673469388, 'cluster_count': 0}, 'population_state': {'diversity': 0.7087912087912088, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0218, 'fitness_entropy': 0.9491132589845683, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.022)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.709)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 6.043)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358704.0342164, 'performance_metrics': {}}}
2025-08-05 09:51:44,153 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:44,153 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:44,153 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:44,153 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:44,153 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:51:44,153 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 09:51:44,154 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:51:44,154 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:44,154 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:44,154 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:51:44,154 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:44,154 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:44,155 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 0} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:44,155 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:51:44,155 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:51:44,155 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:44,156 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 09:51:44,156 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,156 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,157 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,157 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,157 - ExplorationExpert - INFO - 探索路径生成完成，成本: 909.0, 路径长度: 11, 收集中间解: 3
2025-08-05 09:51:44,157 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [3, 5, 9, 7, 4, 2, 1, 6, 0, 8, 10], 'cur_cost': 909.0, 'intermediate_solutions': [{'tour': [7, 1, 6, 5, 8, 10, 3, 2, 0, 9, 4], 'cur_cost': 1085.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 1, 6, 5, 8, 9, 4, 2, 3, 10, 0], 'cur_cost': 1174.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 1, 6, 5, 8, 10, 3, 2, 4, 9, 0], 'cur_cost': 1139.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:44,158 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 909.00)
2025-08-05 09:51:44,158 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 09:51:44,158 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:44,158 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:44,159 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 1218.0
2025-08-05 09:51:44,167 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:44,167 - ExploitationExpert - INFO - res_population_costs: [803.0, 803, 803, 803]
2025-08-05 09:51:44,167 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  2,  7,  4,  9, 10,  8,  5,  3,  6], dtype=int64), array([ 0,  1,  7,  4,  2,  9, 10,  8,  5,  3,  6], dtype=int64), array([ 0,  6,  8, 10,  5,  3,  9,  2,  4,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  4,  9,  3,  5, 10,  8,  6], dtype=int64)]
2025-08-05 09:51:44,168 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:44,168 - ExploitationExpert - INFO - populations: [{'tour': [3, 5, 9, 7, 4, 2, 1, 6, 0, 8, 10], 'cur_cost': 909.0}, {'tour': array([ 4,  9,  7,  0,  3,  6,  1, 10,  8,  5,  2], dtype=int64), 'cur_cost': 1218.0}, {'tour': [10, 6, 0, 5, 7, 9, 3, 4, 1, 2, 8], 'cur_cost': 1272.0}, {'tour': [4, 8, 1, 5, 6, 0, 3, 2, 7, 9, 10], 'cur_cost': 1345.0}, {'tour': [10, 9, 3, 0, 4, 6, 8, 1, 2, 7, 5], 'cur_cost': 1330.0}, {'tour': [7, 1, 9, 5, 10, 3, 4, 2, 0, 6, 8], 'cur_cost': 1228.0}, {'tour': [4, 7, 10, 6, 9, 8, 5, 2, 0, 1, 3], 'cur_cost': 1310.0}, {'tour': [8, 3, 1, 7, 2, 5, 6, 10, 9, 4, 0], 'cur_cost': 1199.0}, {'tour': [8, 6, 3, 1, 7, 2, 4, 5, 10, 9, 0], 'cur_cost': 1103.0}, {'tour': [5, 8, 6, 9, 1, 7, 10, 0, 2, 3, 4], 'cur_cost': 1475.0}]
2025-08-05 09:51:44,169 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:44,169 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 50, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 50, 'cache_hits': 0, 'similarity_calculations': 343, 'cache_hit_rate': 0.0, 'cache_size': 343}}
2025-08-05 09:51:44,170 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([ 4,  9,  7,  0,  3,  6,  1, 10,  8,  5,  2], dtype=int64), 'cur_cost': 1218.0, 'intermediate_solutions': [{'tour': array([ 2,  3,  6,  9, 10,  1,  7,  5,  8,  0,  4]), 'cur_cost': 1303.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9,  2,  3,  6, 10,  1,  7,  5,  8,  0,  4]), 'cur_cost': 1323.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([10,  9,  2,  3,  6,  1,  7,  5,  8,  0,  4]), 'cur_cost': 1341.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6,  9,  2,  3, 10,  1,  7,  5,  8,  0,  4]), 'cur_cost': 1467.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6, 10,  9,  2,  3,  1,  7,  5,  8,  0,  4]), 'cur_cost': 1328.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:44,170 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1218.00)
2025-08-05 09:51:44,170 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:51:44,170 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:51:44,171 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:44,171 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 09:51:44,171 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,171 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,172 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,172 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,172 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1249.0, 路径长度: 11, 收集中间解: 3
2025-08-05 09:51:44,173 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 5, 9, 2, 8, 10, 3, 1, 6, 7, 4], 'cur_cost': 1249.0, 'intermediate_solutions': [{'tour': [10, 3, 0, 5, 7, 9, 6, 4, 1, 2, 8], 'cur_cost': 1441.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 6, 0, 5, 7, 9, 4, 3, 1, 2, 8], 'cur_cost': 1249.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 0, 5, 7, 9, 6, 3, 4, 1, 2, 8], 'cur_cost': 1396.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:44,173 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1249.00)
2025-08-05 09:51:44,173 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:51:44,173 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:44,173 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:44,173 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1539.0
2025-08-05 09:51:44,181 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:44,181 - ExploitationExpert - INFO - res_population_costs: [803.0, 803, 803, 803]
2025-08-05 09:51:44,181 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  2,  7,  4,  9, 10,  8,  5,  3,  6], dtype=int64), array([ 0,  1,  7,  4,  2,  9, 10,  8,  5,  3,  6], dtype=int64), array([ 0,  6,  8, 10,  5,  3,  9,  2,  4,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  4,  9,  3,  5, 10,  8,  6], dtype=int64)]
2025-08-05 09:51:44,182 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:44,182 - ExploitationExpert - INFO - populations: [{'tour': [3, 5, 9, 7, 4, 2, 1, 6, 0, 8, 10], 'cur_cost': 909.0}, {'tour': array([ 4,  9,  7,  0,  3,  6,  1, 10,  8,  5,  2], dtype=int64), 'cur_cost': 1218.0}, {'tour': [0, 5, 9, 2, 8, 10, 3, 1, 6, 7, 4], 'cur_cost': 1249.0}, {'tour': array([ 9,  4,  0,  5,  2,  8,  3,  7,  6,  1, 10], dtype=int64), 'cur_cost': 1539.0}, {'tour': [10, 9, 3, 0, 4, 6, 8, 1, 2, 7, 5], 'cur_cost': 1330.0}, {'tour': [7, 1, 9, 5, 10, 3, 4, 2, 0, 6, 8], 'cur_cost': 1228.0}, {'tour': [4, 7, 10, 6, 9, 8, 5, 2, 0, 1, 3], 'cur_cost': 1310.0}, {'tour': [8, 3, 1, 7, 2, 5, 6, 10, 9, 4, 0], 'cur_cost': 1199.0}, {'tour': [8, 6, 3, 1, 7, 2, 4, 5, 10, 9, 0], 'cur_cost': 1103.0}, {'tour': [5, 8, 6, 9, 1, 7, 10, 0, 2, 3, 4], 'cur_cost': 1475.0}]
2025-08-05 09:51:44,183 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:44,183 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 51, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 51, 'cache_hits': 0, 'similarity_calculations': 354, 'cache_hit_rate': 0.0, 'cache_size': 354}}
2025-08-05 09:51:44,184 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 9,  4,  0,  5,  2,  8,  3,  7,  6,  1, 10], dtype=int64), 'cur_cost': 1539.0, 'intermediate_solutions': [{'tour': array([ 1,  8,  4,  5,  6,  0,  3,  2,  7,  9, 10]), 'cur_cost': 1359.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 5,  1,  8,  4,  6,  0,  3,  2,  7,  9, 10]), 'cur_cost': 1316.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 6,  5,  1,  8,  4,  0,  3,  2,  7,  9, 10]), 'cur_cost': 1398.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4,  5,  1,  8,  6,  0,  3,  2,  7,  9, 10]), 'cur_cost': 1309.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4,  6,  5,  1,  8,  0,  3,  2,  7,  9, 10]), 'cur_cost': 1376.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:44,184 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1539.00)
2025-08-05 09:51:44,184 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:51:44,184 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:51:44,184 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:44,185 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 11
2025-08-05 09:51:44,185 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,185 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,185 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,185 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,186 - ExplorationExpert - INFO - 探索路径生成完成，成本: 877.0, 路径长度: 11, 收集中间解: 3
2025-08-05 09:51:44,186 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [7, 4, 2, 3, 5, 6, 0, 8, 10, 9, 1], 'cur_cost': 877.0, 'intermediate_solutions': [{'tour': [10, 7, 3, 0, 4, 6, 8, 1, 2, 9, 5], 'cur_cost': 1440.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 9, 3, 0, 4, 6, 5, 7, 2, 1, 8], 'cur_cost': 1318.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 9, 3, 4, 0, 6, 8, 1, 2, 7, 5], 'cur_cost': 1211.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:44,186 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 877.00)
2025-08-05 09:51:44,186 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:51:44,186 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:51:44,186 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:44,187 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 09:51:44,187 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,187 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,187 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,187 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,187 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1265.0, 路径长度: 11, 收集中间解: 3
2025-08-05 09:51:44,187 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [5, 7, 1, 2, 6, 0, 3, 10, 8, 9, 4], 'cur_cost': 1265.0, 'intermediate_solutions': [{'tour': [6, 1, 9, 5, 10, 3, 4, 2, 0, 7, 8], 'cur_cost': 1364.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 2, 4, 3, 10, 5, 9, 1, 0, 6, 8], 'cur_cost': 1130.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 1, 8, 9, 5, 10, 3, 4, 2, 0, 6], 'cur_cost': 1247.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:44,187 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1265.00)
2025-08-05 09:51:44,187 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:51:44,188 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:51:44,188 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:44,188 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 09:51:44,188 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,188 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,188 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,188 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,189 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1138.0, 路径长度: 11, 收集中间解: 3
2025-08-05 09:51:44,189 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 10, 5, 1, 8, 6, 3, 2, 7, 4, 9], 'cur_cost': 1138.0, 'intermediate_solutions': [{'tour': [4, 7, 6, 10, 9, 8, 5, 2, 0, 1, 3], 'cur_cost': 1221.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 7, 10, 6, 9, 2, 5, 8, 0, 1, 3], 'cur_cost': 1231.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 7, 10, 9, 8, 5, 2, 0, 6, 1, 3], 'cur_cost': 1196.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:44,189 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1138.00)
2025-08-05 09:51:44,189 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:51:44,189 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:51:44,189 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:44,189 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 09:51:44,189 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,190 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,190 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,190 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,190 - ExplorationExpert - INFO - 探索路径生成完成，成本: 971.0, 路径长度: 11, 收集中间解: 3
2025-08-05 09:51:44,190 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [6, 10, 3, 9, 2, 7, 4, 1, 5, 8, 0], 'cur_cost': 971.0, 'intermediate_solutions': [{'tour': [8, 3, 1, 7, 2, 5, 6, 10, 4, 9, 0], 'cur_cost': 1298.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 0, 4, 9, 10, 6, 5, 2, 7, 1, 3], 'cur_cost': 1199.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 3, 5, 1, 7, 2, 6, 10, 9, 4, 0], 'cur_cost': 1250.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:44,190 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 971.00)
2025-08-05 09:51:44,190 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:51:44,190 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:51:44,190 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:44,191 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 11
2025-08-05 09:51:44,191 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,191 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,191 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,191 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:44,191 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1224.0, 路径长度: 11, 收集中间解: 3
2025-08-05 09:51:44,191 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [7, 8, 0, 1, 3, 5, 10, 6, 2, 4, 9], 'cur_cost': 1224.0, 'intermediate_solutions': [{'tour': [8, 6, 3, 1, 7, 2, 5, 4, 10, 9, 0], 'cur_cost': 1329.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 6, 3, 1, 5, 4, 2, 7, 10, 9, 0], 'cur_cost': 1282.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 7, 6, 3, 1, 2, 4, 5, 10, 9, 0], 'cur_cost': 1348.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:44,191 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 1224.00)
2025-08-05 09:51:44,192 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 09:51:44,192 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:44,192 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:44,192 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 1262.0
2025-08-05 09:51:44,198 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:44,198 - ExploitationExpert - INFO - res_population_costs: [803.0, 803, 803, 803]
2025-08-05 09:51:44,198 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  2,  7,  4,  9, 10,  8,  5,  3,  6], dtype=int64), array([ 0,  1,  7,  4,  2,  9, 10,  8,  5,  3,  6], dtype=int64), array([ 0,  6,  8, 10,  5,  3,  9,  2,  4,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  4,  9,  3,  5, 10,  8,  6], dtype=int64)]
2025-08-05 09:51:44,199 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:44,199 - ExploitationExpert - INFO - populations: [{'tour': [3, 5, 9, 7, 4, 2, 1, 6, 0, 8, 10], 'cur_cost': 909.0}, {'tour': array([ 4,  9,  7,  0,  3,  6,  1, 10,  8,  5,  2], dtype=int64), 'cur_cost': 1218.0}, {'tour': [0, 5, 9, 2, 8, 10, 3, 1, 6, 7, 4], 'cur_cost': 1249.0}, {'tour': array([ 9,  4,  0,  5,  2,  8,  3,  7,  6,  1, 10], dtype=int64), 'cur_cost': 1539.0}, {'tour': [7, 4, 2, 3, 5, 6, 0, 8, 10, 9, 1], 'cur_cost': 877.0}, {'tour': [5, 7, 1, 2, 6, 0, 3, 10, 8, 9, 4], 'cur_cost': 1265.0}, {'tour': [0, 10, 5, 1, 8, 6, 3, 2, 7, 4, 9], 'cur_cost': 1138.0}, {'tour': [6, 10, 3, 9, 2, 7, 4, 1, 5, 8, 0], 'cur_cost': 971.0}, {'tour': [7, 8, 0, 1, 3, 5, 10, 6, 2, 4, 9], 'cur_cost': 1224.0}, {'tour': array([ 0,  5, 10,  6,  3,  1,  2,  7,  8,  9,  4], dtype=int64), 'cur_cost': 1262.0}]
2025-08-05 09:51:44,200 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:44,200 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 52, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 52, 'cache_hits': 0, 'similarity_calculations': 366, 'cache_hit_rate': 0.0, 'cache_size': 366}}
2025-08-05 09:51:44,200 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([ 0,  5, 10,  6,  3,  1,  2,  7,  8,  9,  4], dtype=int64), 'cur_cost': 1262.0, 'intermediate_solutions': [{'tour': array([ 6,  8,  5,  9,  1,  7, 10,  0,  2,  3,  4]), 'cur_cost': 1436.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9,  6,  8,  5,  1,  7, 10,  0,  2,  3,  4]), 'cur_cost': 1388.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1,  9,  6,  8,  5,  7, 10,  0,  2,  3,  4]), 'cur_cost': 1494.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 5,  9,  6,  8,  1,  7, 10,  0,  2,  3,  4]), 'cur_cost': 1528.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 5,  1,  9,  6,  8,  7, 10,  0,  2,  3,  4]), 'cur_cost': 1662.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:44,200 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1262.00)
2025-08-05 09:51:44,201 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:44,201 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:44,202 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [3, 5, 9, 7, 4, 2, 1, 6, 0, 8, 10], 'cur_cost': 909.0, 'intermediate_solutions': [{'tour': [7, 1, 6, 5, 8, 10, 3, 2, 0, 9, 4], 'cur_cost': 1085.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 1, 6, 5, 8, 9, 4, 2, 3, 10, 0], 'cur_cost': 1174.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 1, 6, 5, 8, 10, 3, 2, 4, 9, 0], 'cur_cost': 1139.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 4,  9,  7,  0,  3,  6,  1, 10,  8,  5,  2], dtype=int64), 'cur_cost': 1218.0, 'intermediate_solutions': [{'tour': array([ 2,  3,  6,  9, 10,  1,  7,  5,  8,  0,  4]), 'cur_cost': 1303.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9,  2,  3,  6, 10,  1,  7,  5,  8,  0,  4]), 'cur_cost': 1323.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([10,  9,  2,  3,  6,  1,  7,  5,  8,  0,  4]), 'cur_cost': 1341.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6,  9,  2,  3, 10,  1,  7,  5,  8,  0,  4]), 'cur_cost': 1467.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6, 10,  9,  2,  3,  1,  7,  5,  8,  0,  4]), 'cur_cost': 1328.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 9, 2, 8, 10, 3, 1, 6, 7, 4], 'cur_cost': 1249.0, 'intermediate_solutions': [{'tour': [10, 3, 0, 5, 7, 9, 6, 4, 1, 2, 8], 'cur_cost': 1441.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 6, 0, 5, 7, 9, 4, 3, 1, 2, 8], 'cur_cost': 1249.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 0, 5, 7, 9, 6, 3, 4, 1, 2, 8], 'cur_cost': 1396.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 9,  4,  0,  5,  2,  8,  3,  7,  6,  1, 10], dtype=int64), 'cur_cost': 1539.0, 'intermediate_solutions': [{'tour': array([ 1,  8,  4,  5,  6,  0,  3,  2,  7,  9, 10]), 'cur_cost': 1359.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 5,  1,  8,  4,  6,  0,  3,  2,  7,  9, 10]), 'cur_cost': 1316.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 6,  5,  1,  8,  4,  0,  3,  2,  7,  9, 10]), 'cur_cost': 1398.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4,  5,  1,  8,  6,  0,  3,  2,  7,  9, 10]), 'cur_cost': 1309.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4,  6,  5,  1,  8,  0,  3,  2,  7,  9, 10]), 'cur_cost': 1376.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [7, 4, 2, 3, 5, 6, 0, 8, 10, 9, 1], 'cur_cost': 877.0, 'intermediate_solutions': [{'tour': [10, 7, 3, 0, 4, 6, 8, 1, 2, 9, 5], 'cur_cost': 1440.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 9, 3, 0, 4, 6, 5, 7, 2, 1, 8], 'cur_cost': 1318.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 9, 3, 4, 0, 6, 8, 1, 2, 7, 5], 'cur_cost': 1211.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [5, 7, 1, 2, 6, 0, 3, 10, 8, 9, 4], 'cur_cost': 1265.0, 'intermediate_solutions': [{'tour': [6, 1, 9, 5, 10, 3, 4, 2, 0, 7, 8], 'cur_cost': 1364.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 2, 4, 3, 10, 5, 9, 1, 0, 6, 8], 'cur_cost': 1130.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 1, 8, 9, 5, 10, 3, 4, 2, 0, 6], 'cur_cost': 1247.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 5, 1, 8, 6, 3, 2, 7, 4, 9], 'cur_cost': 1138.0, 'intermediate_solutions': [{'tour': [4, 7, 6, 10, 9, 8, 5, 2, 0, 1, 3], 'cur_cost': 1221.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 7, 10, 6, 9, 2, 5, 8, 0, 1, 3], 'cur_cost': 1231.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 7, 10, 9, 8, 5, 2, 0, 6, 1, 3], 'cur_cost': 1196.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [6, 10, 3, 9, 2, 7, 4, 1, 5, 8, 0], 'cur_cost': 971.0, 'intermediate_solutions': [{'tour': [8, 3, 1, 7, 2, 5, 6, 10, 4, 9, 0], 'cur_cost': 1298.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 0, 4, 9, 10, 6, 5, 2, 7, 1, 3], 'cur_cost': 1199.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 3, 5, 1, 7, 2, 6, 10, 9, 4, 0], 'cur_cost': 1250.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [7, 8, 0, 1, 3, 5, 10, 6, 2, 4, 9], 'cur_cost': 1224.0, 'intermediate_solutions': [{'tour': [8, 6, 3, 1, 7, 2, 5, 4, 10, 9, 0], 'cur_cost': 1329.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 6, 3, 1, 5, 4, 2, 7, 10, 9, 0], 'cur_cost': 1282.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 7, 6, 3, 1, 2, 4, 5, 10, 9, 0], 'cur_cost': 1348.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 0,  5, 10,  6,  3,  1,  2,  7,  8,  9,  4], dtype=int64), 'cur_cost': 1262.0, 'intermediate_solutions': [{'tour': array([ 6,  8,  5,  9,  1,  7, 10,  0,  2,  3,  4]), 'cur_cost': 1436.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9,  6,  8,  5,  1,  7, 10,  0,  2,  3,  4]), 'cur_cost': 1388.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1,  9,  6,  8,  5,  7, 10,  0,  2,  3,  4]), 'cur_cost': 1494.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 5,  9,  6,  8,  1,  7, 10,  0,  2,  3,  4]), 'cur_cost': 1528.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 5,  1,  9,  6,  8,  7, 10,  0,  2,  3,  4]), 'cur_cost': 1662.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 09:51:44,202 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:44,202 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:44,203 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=877.000, 多样性=0.907
2025-08-05 09:51:44,203 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 09:51:44,203 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 09:51:44,204 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:44,204 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.08344639652885903, 'best_improvement': 0.20489573889392565}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.011013215859031028}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.018543626754843053, 'recent_improvements': [0.03376300261344437, -0.025142458368520467, -0.0033242508962417366], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 803.0, 'new_best_cost': 803.0, 'quality_improvement': 0.0, 'old_diversity': 0.6666666666666666, 'new_diversity': 0.6666666666666666, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:51:44,204 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 09:51:44,208 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple4_11_solution.json
2025-08-05 09:51:44,208 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple4_11_20250805_095144.solution
2025-08-05 09:51:44,209 - __main__ - INFO - 实例执行完成 - 运行时间: 1.02s, 最佳成本: 803.0
2025-08-05 09:51:44,209 - __main__ - INFO - 实例 simple4_11 处理完成
