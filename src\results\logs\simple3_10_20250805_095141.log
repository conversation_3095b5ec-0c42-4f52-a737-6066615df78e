2025-08-05 09:51:41,174 - __main__ - INFO - simple3_10 开始进化第 1 代
2025-08-05 09:51:41,175 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 09:51:41,175 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:41,176 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=832.000, 多样性=0.918
2025-08-05 09:51:41,177 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:41,178 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.918
2025-08-05 09:51:41,179 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:41,181 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 09:51:41,181 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:41,181 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 09:51:41,181 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 09:51:41,187 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -0.800, 聚类评分: 0.000, 覆盖率: 0.012, 收敛趋势: 0.000, 多样性: 0.918
2025-08-05 09:51:41,187 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 09:51:41,187 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 09:51:41,188 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple3_10
2025-08-05 09:51:41,191 - visualization.landscape_visualizer - INFO - 插值约束: 127 个点被约束到最小值 832.00
2025-08-05 09:51:41,272 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple3_10\landscape_simple3_10_iter_11_20250805_095141.html
2025-08-05 09:51:41,312 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple3_10\dashboard_simple3_10_iter_11_20250805_095141.html
2025-08-05 09:51:41,313 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 11
2025-08-05 09:51:41,313 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 09:51:41,313 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1318秒
2025-08-05 09:51:41,313 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 22, 'max_size': 500, 'hits': 0, 'misses': 22, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 34, 'max_size': 100, 'hits': 72, 'misses': 34, 'hit_rate': 0.6792452830188679, 'evictions': 0, 'ttl': 7200}}
2025-08-05 09:51:41,313 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -0.8000000000000071, 'local_optima_density': 0.1, 'gradient_variance': 93733.128, 'cluster_count': 0}, 'population_state': {'diversity': 0.9177777777777777, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0116, 'fitness_entropy': 0.8982444017039273, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -0.800)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.012)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.918)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358701.1872938, 'performance_metrics': {}}}
2025-08-05 09:51:41,313 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:41,313 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:41,314 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:41,314 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:41,314 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 09:51:41,315 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 09:51:41,315 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 09:51:41,315 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:41,315 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:41,315 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 09:51:41,315 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:41,316 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:41,316 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:41,316 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:51:41,316 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:51:41,316 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,317 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:41,317 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,317 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1035.0, 路径长度: 10, 收集中间解: 0
2025-08-05 09:51:41,317 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [7, 9, 4, 0, 8, 3, 6, 2, 5, 1], 'cur_cost': 1035.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,317 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 1035.00)
2025-08-05 09:51:41,318 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:51:41,318 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:51:41,318 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,318 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 09:51:41,318 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,319 - ExplorationExpert - INFO - 探索路径生成完成，成本: 920.0, 路径长度: 10, 收集中间解: 0
2025-08-05 09:51:41,319 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [2, 0, 8, 3, 9, 4, 5, 7, 1, 6], 'cur_cost': 920.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,319 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 920.00)
2025-08-05 09:51:41,319 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:51:41,319 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:51:41,319 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,320 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 09:51:41,320 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,320 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1411.0, 路径长度: 10, 收集中间解: 0
2025-08-05 09:51:41,320 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [2, 8, 3, 6, 1, 0, 5, 7, 9, 4], 'cur_cost': 1411.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,320 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1411.00)
2025-08-05 09:51:41,320 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:51:41,320 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:51:41,320 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,321 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:41,321 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,321 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1077.0, 路径长度: 10, 收集中间解: 0
2025-08-05 09:51:41,321 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 6, 8, 3, 5, 4, 7, 1, 9, 2], 'cur_cost': 1077.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,321 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1077.00)
2025-08-05 09:51:41,321 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 09:51:41,321 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:41,322 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:41,322 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 1323.0
2025-08-05 09:51:41,326 - ExploitationExpert - INFO - res_population_num: 3
2025-08-05 09:51:41,326 - ExploitationExpert - INFO - res_population_costs: [832.0, 832, 832]
2025-08-05 09:51:41,326 - ExploitationExpert - INFO - res_populations: [array([0, 9, 4, 1, 7, 5, 2, 6, 3, 8], dtype=int64), array([0, 8, 3, 6, 2, 7, 1, 4, 5, 9], dtype=int64), array([0, 9, 4, 1, 7, 5, 6, 2, 3, 8], dtype=int64)]
2025-08-05 09:51:41,327 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:41,327 - ExploitationExpert - INFO - populations: [{'tour': [7, 9, 4, 0, 8, 3, 6, 2, 5, 1], 'cur_cost': 1035.0}, {'tour': [2, 0, 8, 3, 9, 4, 5, 7, 1, 6], 'cur_cost': 920.0}, {'tour': [2, 8, 3, 6, 1, 0, 5, 7, 9, 4], 'cur_cost': 1411.0}, {'tour': [0, 6, 8, 3, 5, 4, 7, 1, 9, 2], 'cur_cost': 1077.0}, {'tour': array([8, 0, 5, 6, 7, 4, 2, 3, 9, 1], dtype=int64), 'cur_cost': 1323.0}, {'tour': array([8, 2, 3, 4, 6, 5, 1, 0, 7, 9], dtype=int64), 'cur_cost': 1509.0}, {'tour': array([7, 0, 9, 1, 8, 3, 2, 5, 6, 4], dtype=int64), 'cur_cost': 1349.0}, {'tour': array([1, 5, 2, 0, 7, 4, 3, 6, 9, 8], dtype=int64), 'cur_cost': 1375.0}, {'tour': array([1, 0, 5, 7, 4, 8, 2, 9, 3, 6], dtype=int64), 'cur_cost': 1371.0}, {'tour': array([7, 8, 6, 2, 9, 5, 4, 1, 3, 0], dtype=int64), 'cur_cost': 1289.0}]
2025-08-05 09:51:41,328 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:41,328 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 27, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 27, 'cache_hits': 0, 'similarity_calculations': 169, 'cache_hit_rate': 0.0, 'cache_size': 169}}
2025-08-05 09:51:41,329 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([8, 0, 5, 6, 7, 4, 2, 3, 9, 1], dtype=int64), 'cur_cost': 1323.0, 'intermediate_solutions': [{'tour': array([1, 3, 7, 0, 6, 5, 2, 9, 4, 8], dtype=int64), 'cur_cost': 1765.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 1, 3, 7, 6, 5, 2, 9, 4, 8], dtype=int64), 'cur_cost': 1703.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 0, 1, 3, 7, 5, 2, 9, 4, 8], dtype=int64), 'cur_cost': 1620.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 0, 1, 3, 6, 5, 2, 9, 4, 8], dtype=int64), 'cur_cost': 1725.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 6, 0, 1, 3, 5, 2, 9, 4, 8], dtype=int64), 'cur_cost': 1753.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:41,329 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1323.00)
2025-08-05 09:51:41,329 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 09:51:41,329 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:41,329 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:41,329 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 1700.0
2025-08-05 09:51:41,336 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 09:51:41,336 - ExploitationExpert - INFO - res_population_costs: [832.0, 832, 832, 832.0, 832, 832]
2025-08-05 09:51:41,336 - ExploitationExpert - INFO - res_populations: [array([0, 9, 4, 1, 7, 5, 2, 6, 3, 8], dtype=int64), array([0, 8, 3, 6, 2, 7, 1, 4, 5, 9], dtype=int64), array([0, 9, 4, 1, 7, 5, 6, 2, 3, 8], dtype=int64), array([0, 3, 6, 2, 5, 4, 7, 1, 9, 8], dtype=int64), array([0, 9, 1, 7, 4, 5, 2, 6, 3, 8], dtype=int64), array([0, 8, 3, 9, 4, 1, 7, 5, 2, 6], dtype=int64)]
2025-08-05 09:51:41,337 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:41,337 - ExploitationExpert - INFO - populations: [{'tour': [7, 9, 4, 0, 8, 3, 6, 2, 5, 1], 'cur_cost': 1035.0}, {'tour': [2, 0, 8, 3, 9, 4, 5, 7, 1, 6], 'cur_cost': 920.0}, {'tour': [2, 8, 3, 6, 1, 0, 5, 7, 9, 4], 'cur_cost': 1411.0}, {'tour': [0, 6, 8, 3, 5, 4, 7, 1, 9, 2], 'cur_cost': 1077.0}, {'tour': array([8, 0, 5, 6, 7, 4, 2, 3, 9, 1], dtype=int64), 'cur_cost': 1323.0}, {'tour': array([0, 5, 3, 1, 2, 8, 7, 6, 4, 9], dtype=int64), 'cur_cost': 1700.0}, {'tour': array([7, 0, 9, 1, 8, 3, 2, 5, 6, 4], dtype=int64), 'cur_cost': 1349.0}, {'tour': array([1, 5, 2, 0, 7, 4, 3, 6, 9, 8], dtype=int64), 'cur_cost': 1375.0}, {'tour': array([1, 0, 5, 7, 4, 8, 2, 9, 3, 6], dtype=int64), 'cur_cost': 1371.0}, {'tour': array([7, 8, 6, 2, 9, 5, 4, 1, 3, 0], dtype=int64), 'cur_cost': 1289.0}]
2025-08-05 09:51:41,339 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:41,339 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 28, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 28, 'cache_hits': 0, 'similarity_calculations': 183, 'cache_hit_rate': 0.0, 'cache_size': 183}}
2025-08-05 09:51:41,339 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([0, 5, 3, 1, 2, 8, 7, 6, 4, 9], dtype=int64), 'cur_cost': 1700.0, 'intermediate_solutions': [{'tour': array([3, 2, 8, 4, 6, 5, 1, 0, 7, 9], dtype=int64), 'cur_cost': 1496.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 3, 2, 8, 6, 5, 1, 0, 7, 9], dtype=int64), 'cur_cost': 1500.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 4, 3, 2, 8, 5, 1, 0, 7, 9], dtype=int64), 'cur_cost': 1596.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 4, 3, 2, 6, 5, 1, 0, 7, 9], dtype=int64), 'cur_cost': 1417.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 6, 4, 3, 2, 5, 1, 0, 7, 9], dtype=int64), 'cur_cost': 1502.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:41,339 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1700.00)
2025-08-05 09:51:41,339 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:51:41,340 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:51:41,340 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,340 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:41,340 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,341 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1151.0, 路径长度: 10, 收集中间解: 0
2025-08-05 09:51:41,341 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 4, 3, 8, 9, 5, 7, 1, 6, 2], 'cur_cost': 1151.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,341 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1151.00)
2025-08-05 09:51:41,341 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:51:41,341 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:51:41,341 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,342 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 09:51:41,342 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,342 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1381.0, 路径长度: 10, 收集中间解: 0
2025-08-05 09:51:41,342 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [9, 4, 5, 3, 6, 2, 0, 7, 8, 1], 'cur_cost': 1381.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,342 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1381.00)
2025-08-05 09:51:41,343 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:51:41,343 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:51:41,343 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,343 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 09:51:41,343 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,343 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1156.0, 路径长度: 10, 收集中间解: 0
2025-08-05 09:51:41,344 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [9, 5, 0, 8, 3, 2, 4, 7, 1, 6], 'cur_cost': 1156.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,344 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1156.00)
2025-08-05 09:51:41,344 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:51:41,344 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:51:41,344 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,345 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 09:51:41,345 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,345 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1219.0, 路径长度: 10, 收集中间解: 0
2025-08-05 09:51:41,345 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [1, 0, 2, 3, 6, 5, 4, 7, 8, 9], 'cur_cost': 1219.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,345 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1219.00)
2025-08-05 09:51:41,345 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:41,345 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:41,347 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [7, 9, 4, 0, 8, 3, 6, 2, 5, 1], 'cur_cost': 1035.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [2, 0, 8, 3, 9, 4, 5, 7, 1, 6], 'cur_cost': 920.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [2, 8, 3, 6, 1, 0, 5, 7, 9, 4], 'cur_cost': 1411.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 8, 3, 5, 4, 7, 1, 9, 2], 'cur_cost': 1077.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 0, 5, 6, 7, 4, 2, 3, 9, 1], dtype=int64), 'cur_cost': 1323.0, 'intermediate_solutions': [{'tour': array([1, 3, 7, 0, 6, 5, 2, 9, 4, 8], dtype=int64), 'cur_cost': 1765.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 1, 3, 7, 6, 5, 2, 9, 4, 8], dtype=int64), 'cur_cost': 1703.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 0, 1, 3, 7, 5, 2, 9, 4, 8], dtype=int64), 'cur_cost': 1620.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 0, 1, 3, 6, 5, 2, 9, 4, 8], dtype=int64), 'cur_cost': 1725.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 6, 0, 1, 3, 5, 2, 9, 4, 8], dtype=int64), 'cur_cost': 1753.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 5, 3, 1, 2, 8, 7, 6, 4, 9], dtype=int64), 'cur_cost': 1700.0, 'intermediate_solutions': [{'tour': array([3, 2, 8, 4, 6, 5, 1, 0, 7, 9], dtype=int64), 'cur_cost': 1496.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 3, 2, 8, 6, 5, 1, 0, 7, 9], dtype=int64), 'cur_cost': 1500.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 4, 3, 2, 8, 5, 1, 0, 7, 9], dtype=int64), 'cur_cost': 1596.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 4, 3, 2, 6, 5, 1, 0, 7, 9], dtype=int64), 'cur_cost': 1417.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 6, 4, 3, 2, 5, 1, 0, 7, 9], dtype=int64), 'cur_cost': 1502.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 3, 8, 9, 5, 7, 1, 6, 2], 'cur_cost': 1151.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [9, 4, 5, 3, 6, 2, 0, 7, 8, 1], 'cur_cost': 1381.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [9, 5, 0, 8, 3, 2, 4, 7, 1, 6], 'cur_cost': 1156.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [1, 0, 2, 3, 6, 5, 4, 7, 8, 9], 'cur_cost': 1219.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 09:51:41,347 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:41,347 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:41,348 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=920.000, 多样性=0.873
2025-08-05 09:51:41,348 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 09:51:41,348 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 09:51:41,348 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:41,349 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.013723187108399043, 'best_improvement': -0.10576923076923077}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.04842615012106544}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.03146420097193987, 'recent_improvements': [-0.009788175682420592, 0.06124608260638922, -0.07271657762630035], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 6, 'new_count': 6, 'count_change': 0, 'old_best_cost': 832.0, 'new_best_cost': 832.0, 'quality_improvement': 0.0, 'old_diversity': 0.74, 'new_diversity': 0.74, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配']}
2025-08-05 09:51:41,349 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 09:51:41,349 - __main__ - INFO - simple3_10 开始进化第 2 代
2025-08-05 09:51:41,349 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 09:51:41,349 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:41,350 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=920.000, 多样性=0.873
2025-08-05 09:51:41,350 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:41,351 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.873
2025-08-05 09:51:41,351 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:41,352 - EliteExpert - INFO - 精英解分析完成: 精英解数量=6, 多样性=0.740
2025-08-05 09:51:41,353 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 09:51:41,354 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:41,354 - LandscapeExpert - INFO - 添加精英解数据: 6个精英解
2025-08-05 09:51:41,354 - LandscapeExpert - INFO - 数据提取成功: 16个路径, 16个适应度值
2025-08-05 09:51:41,364 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.438, 适应度梯度: 54.000, 聚类评分: 0.000, 覆盖率: 0.013, 收敛趋势: 0.000, 多样性: 0.539
2025-08-05 09:51:41,364 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 09:51:41,364 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:51:41,364 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple3_10
2025-08-05 09:51:41,368 - visualization.landscape_visualizer - INFO - 插值约束: 120 个点被约束到最小值 832.00
2025-08-05 09:51:41,443 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple3_10\landscape_simple3_10_iter_12_20250805_095141.html
2025-08-05 09:51:41,489 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple3_10\dashboard_simple3_10_iter_12_20250805_095141.html
2025-08-05 09:51:41,489 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 12
2025-08-05 09:51:41,489 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 09:51:41,489 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1366秒
2025-08-05 09:51:41,489 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.4375, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 53.999999999999986, 'local_optima_density': 0.4375, 'gradient_variance': 102810.02, 'cluster_count': 0}, 'population_state': {'diversity': 0.5385416666666667, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0131, 'fitness_entropy': 0.8852130207431887, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.013)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 54.000)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358701.3646247, 'performance_metrics': {}}}
2025-08-05 09:51:41,490 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:41,490 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:41,490 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:41,490 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:41,490 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 09:51:41,490 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 09:51:41,491 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 09:51:41,491 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:41,491 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:41,491 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 09:51:41,491 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:41,491 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:41,492 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:41,492 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:51:41,492 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:51:41,492 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,492 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 09:51:41,493 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,493 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,493 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,493 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,493 - ExplorationExpert - INFO - 探索路径生成完成，成本: 936.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:41,494 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [7, 6, 3, 2, 0, 8, 9, 5, 4, 1], 'cur_cost': 936.0, 'intermediate_solutions': [{'tour': [7, 9, 4, 0, 8, 3, 6, 5, 2, 1], 'cur_cost': 1262.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 9, 5, 2, 6, 3, 8, 0, 4, 1], 'cur_cost': 980.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 9, 4, 0, 8, 3, 6, 2, 5, 1], 'cur_cost': 1035.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,494 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 936.00)
2025-08-05 09:51:41,494 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:51:41,494 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:51:41,494 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,494 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:41,495 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,495 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,495 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,495 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,495 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1523.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:41,495 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [1, 3, 7, 2, 5, 4, 0, 8, 6, 9], 'cur_cost': 1523.0, 'intermediate_solutions': [{'tour': [2, 0, 8, 3, 9, 1, 5, 7, 4, 6], 'cur_cost': 937.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 0, 8, 3, 9, 4, 5, 1, 7, 6], 'cur_cost': 903.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 0, 8, 3, 4, 5, 7, 9, 1, 6], 'cur_cost': 1165.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,495 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 1523.00)
2025-08-05 09:51:41,495 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-05 09:51:41,496 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:41,496 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:41,496 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 1263.0
2025-08-05 09:51:41,503 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:51:41,503 - ExploitationExpert - INFO - res_population_costs: [832.0, 832, 832, 832.0, 832, 832, 832]
2025-08-05 09:51:41,504 - ExploitationExpert - INFO - res_populations: [array([0, 9, 4, 1, 7, 5, 2, 6, 3, 8], dtype=int64), array([0, 8, 3, 6, 2, 7, 1, 4, 5, 9], dtype=int64), array([0, 9, 4, 1, 7, 5, 6, 2, 3, 8], dtype=int64), array([0, 3, 6, 2, 5, 4, 7, 1, 9, 8], dtype=int64), array([0, 9, 1, 7, 4, 5, 2, 6, 3, 8], dtype=int64), array([0, 8, 3, 9, 4, 1, 7, 5, 2, 6], dtype=int64), array([0, 8, 3, 9, 1, 7, 4, 5, 2, 6], dtype=int64)]
2025-08-05 09:51:41,505 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:41,505 - ExploitationExpert - INFO - populations: [{'tour': [7, 6, 3, 2, 0, 8, 9, 5, 4, 1], 'cur_cost': 936.0}, {'tour': [1, 3, 7, 2, 5, 4, 0, 8, 6, 9], 'cur_cost': 1523.0}, {'tour': array([3, 8, 4, 1, 5, 9, 2, 7, 0, 6], dtype=int64), 'cur_cost': 1263.0}, {'tour': [0, 6, 8, 3, 5, 4, 7, 1, 9, 2], 'cur_cost': 1077.0}, {'tour': [8, 0, 5, 6, 7, 4, 2, 3, 9, 1], 'cur_cost': 1323.0}, {'tour': [0, 5, 3, 1, 2, 8, 7, 6, 4, 9], 'cur_cost': 1700.0}, {'tour': [0, 4, 3, 8, 9, 5, 7, 1, 6, 2], 'cur_cost': 1151.0}, {'tour': [9, 4, 5, 3, 6, 2, 0, 7, 8, 1], 'cur_cost': 1381.0}, {'tour': [9, 5, 0, 8, 3, 2, 4, 7, 1, 6], 'cur_cost': 1156.0}, {'tour': [1, 0, 2, 3, 6, 5, 4, 7, 8, 9], 'cur_cost': 1219.0}]
2025-08-05 09:51:41,505 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:41,506 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 29, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 29, 'cache_hits': 0, 'similarity_calculations': 198, 'cache_hit_rate': 0.0, 'cache_size': 198}}
2025-08-05 09:51:41,506 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([3, 8, 4, 1, 5, 9, 2, 7, 0, 6], dtype=int64), 'cur_cost': 1263.0, 'intermediate_solutions': [{'tour': array([3, 8, 2, 6, 1, 0, 5, 7, 9, 4]), 'cur_cost': 1376.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 3, 8, 2, 1, 0, 5, 7, 9, 4]), 'cur_cost': 1411.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 6, 3, 8, 2, 0, 5, 7, 9, 4]), 'cur_cost': 1194.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 6, 3, 8, 1, 0, 5, 7, 9, 4]), 'cur_cost': 1319.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 1, 6, 3, 8, 0, 5, 7, 9, 4]), 'cur_cost': 1355.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:41,506 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1263.00)
2025-08-05 09:51:41,506 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:51:41,506 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:51:41,506 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,507 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:41,507 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,507 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,507 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,507 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,507 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1185.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:41,507 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 4, 8, 5, 7, 6, 2, 3, 9], 'cur_cost': 1185.0, 'intermediate_solutions': [{'tour': [7, 6, 8, 3, 5, 4, 0, 1, 9, 2], 'cur_cost': 1476.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 9, 1, 7, 4, 5, 3, 8, 2], 'cur_cost': 1075.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 3, 5, 4, 7, 1, 9, 6, 2], 'cur_cost': 939.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,508 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1185.00)
2025-08-05 09:51:41,508 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:51:41,508 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:51:41,508 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,508 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:41,509 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,509 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,509 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,509 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,509 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1259.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:41,509 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 3, 4, 6, 8, 2, 5, 7, 1, 9], 'cur_cost': 1259.0, 'intermediate_solutions': [{'tour': [9, 0, 5, 6, 7, 4, 2, 3, 8, 1], 'cur_cost': 1329.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 0, 5, 6, 7, 4, 9, 3, 2, 1], 'cur_cost': 1340.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 9, 0, 5, 6, 7, 4, 2, 3, 1], 'cur_cost': 1442.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,510 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1259.00)
2025-08-05 09:51:41,510 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 09:51:41,510 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:41,510 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:41,510 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 1497.0
2025-08-05 09:51:41,517 - ExploitationExpert - INFO - res_population_num: 8
2025-08-05 09:51:41,517 - ExploitationExpert - INFO - res_population_costs: [832.0, 832, 832, 832.0, 832, 832, 832, 832]
2025-08-05 09:51:41,517 - ExploitationExpert - INFO - res_populations: [array([0, 9, 4, 1, 7, 5, 2, 6, 3, 8], dtype=int64), array([0, 8, 3, 6, 2, 7, 1, 4, 5, 9], dtype=int64), array([0, 9, 4, 1, 7, 5, 6, 2, 3, 8], dtype=int64), array([0, 3, 6, 2, 5, 4, 7, 1, 9, 8], dtype=int64), array([0, 9, 1, 7, 4, 5, 2, 6, 3, 8], dtype=int64), array([0, 8, 3, 9, 4, 1, 7, 5, 2, 6], dtype=int64), array([0, 8, 3, 9, 1, 7, 4, 5, 2, 6], dtype=int64), array([0, 8, 3, 9, 5, 4, 1, 7, 2, 6], dtype=int64)]
2025-08-05 09:51:41,519 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:41,519 - ExploitationExpert - INFO - populations: [{'tour': [7, 6, 3, 2, 0, 8, 9, 5, 4, 1], 'cur_cost': 936.0}, {'tour': [1, 3, 7, 2, 5, 4, 0, 8, 6, 9], 'cur_cost': 1523.0}, {'tour': array([3, 8, 4, 1, 5, 9, 2, 7, 0, 6], dtype=int64), 'cur_cost': 1263.0}, {'tour': [0, 1, 4, 8, 5, 7, 6, 2, 3, 9], 'cur_cost': 1185.0}, {'tour': [0, 3, 4, 6, 8, 2, 5, 7, 1, 9], 'cur_cost': 1259.0}, {'tour': array([3, 6, 5, 0, 9, 2, 7, 1, 8, 4], dtype=int64), 'cur_cost': 1497.0}, {'tour': [0, 4, 3, 8, 9, 5, 7, 1, 6, 2], 'cur_cost': 1151.0}, {'tour': [9, 4, 5, 3, 6, 2, 0, 7, 8, 1], 'cur_cost': 1381.0}, {'tour': [9, 5, 0, 8, 3, 2, 4, 7, 1, 6], 'cur_cost': 1156.0}, {'tour': [1, 0, 2, 3, 6, 5, 4, 7, 8, 9], 'cur_cost': 1219.0}]
2025-08-05 09:51:41,520 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:41,520 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 30, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 30, 'cache_hits': 0, 'similarity_calculations': 214, 'cache_hit_rate': 0.0, 'cache_size': 214}}
2025-08-05 09:51:41,521 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([3, 6, 5, 0, 9, 2, 7, 1, 8, 4], dtype=int64), 'cur_cost': 1497.0, 'intermediate_solutions': [{'tour': array([3, 5, 0, 1, 2, 8, 7, 6, 4, 9]), 'cur_cost': 1654.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 3, 5, 0, 2, 8, 7, 6, 4, 9]), 'cur_cost': 1650.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 1, 3, 5, 0, 8, 7, 6, 4, 9]), 'cur_cost': 1707.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 1, 3, 5, 2, 8, 7, 6, 4, 9]), 'cur_cost': 1702.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 2, 1, 3, 5, 8, 7, 6, 4, 9]), 'cur_cost': 1702.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:41,521 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1497.00)
2025-08-05 09:51:41,521 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:51:41,521 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:51:41,521 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,521 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:41,522 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,522 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,522 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,522 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,522 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1271.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:41,522 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [2, 8, 3, 7, 4, 9, 0, 6, 5, 1], 'cur_cost': 1271.0, 'intermediate_solutions': [{'tour': [0, 4, 3, 8, 2, 5, 7, 1, 6, 9], 'cur_cost': 1336.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 6, 1, 7, 5, 9, 8, 3, 4], 'cur_cost': 1151.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 3, 8, 9, 5, 7, 1, 6, 2], 'cur_cost': 1089.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,523 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1271.00)
2025-08-05 09:51:41,523 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:51:41,523 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:51:41,523 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,523 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:41,523 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,524 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,524 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,524 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,524 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1195.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:41,524 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [7, 9, 3, 1, 2, 6, 0, 8, 5, 4], 'cur_cost': 1195.0, 'intermediate_solutions': [{'tour': [9, 4, 5, 3, 7, 2, 0, 6, 8, 1], 'cur_cost': 1481.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 4, 5, 1, 8, 7, 0, 2, 6, 3], 'cur_cost': 1249.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 9, 4, 5, 3, 6, 2, 0, 7, 1], 'cur_cost': 1164.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,524 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1195.00)
2025-08-05 09:51:41,524 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:51:41,524 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:51:41,525 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,525 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 09:51:41,525 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,525 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,525 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,525 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,525 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1048.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:41,526 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [3, 0, 5, 7, 4, 1, 8, 6, 2, 9], 'cur_cost': 1048.0, 'intermediate_solutions': [{'tour': [9, 5, 0, 1, 3, 2, 4, 7, 8, 6], 'cur_cost': 1533.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 5, 0, 8, 3, 6, 1, 7, 4, 2], 'cur_cost': 1165.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 0, 8, 3, 2, 4, 7, 1, 6, 9], 'cur_cost': 1156.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,526 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1048.00)
2025-08-05 09:51:41,526 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:51:41,526 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:51:41,526 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,527 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 09:51:41,527 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,527 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,527 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,527 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,527 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1003.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:41,527 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [1, 7, 4, 0, 3, 8, 6, 2, 5, 9], 'cur_cost': 1003.0, 'intermediate_solutions': [{'tour': [1, 0, 2, 3, 6, 5, 4, 8, 7, 9], 'cur_cost': 1436.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 0, 2, 3, 6, 5, 9, 8, 7, 4], 'cur_cost': 1183.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 2, 3, 6, 4, 7, 8, 5, 9], 'cur_cost': 1385.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,527 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1003.00)
2025-08-05 09:51:41,528 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:41,528 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:41,529 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [7, 6, 3, 2, 0, 8, 9, 5, 4, 1], 'cur_cost': 936.0, 'intermediate_solutions': [{'tour': [7, 9, 4, 0, 8, 3, 6, 5, 2, 1], 'cur_cost': 1262.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 9, 5, 2, 6, 3, 8, 0, 4, 1], 'cur_cost': 980.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 9, 4, 0, 8, 3, 6, 2, 5, 1], 'cur_cost': 1035.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [1, 3, 7, 2, 5, 4, 0, 8, 6, 9], 'cur_cost': 1523.0, 'intermediate_solutions': [{'tour': [2, 0, 8, 3, 9, 1, 5, 7, 4, 6], 'cur_cost': 937.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 0, 8, 3, 9, 4, 5, 1, 7, 6], 'cur_cost': 903.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 0, 8, 3, 4, 5, 7, 9, 1, 6], 'cur_cost': 1165.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 8, 4, 1, 5, 9, 2, 7, 0, 6], dtype=int64), 'cur_cost': 1263.0, 'intermediate_solutions': [{'tour': array([3, 8, 2, 6, 1, 0, 5, 7, 9, 4]), 'cur_cost': 1376.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 3, 8, 2, 1, 0, 5, 7, 9, 4]), 'cur_cost': 1411.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 6, 3, 8, 2, 0, 5, 7, 9, 4]), 'cur_cost': 1194.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 6, 3, 8, 1, 0, 5, 7, 9, 4]), 'cur_cost': 1319.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 1, 6, 3, 8, 0, 5, 7, 9, 4]), 'cur_cost': 1355.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 4, 8, 5, 7, 6, 2, 3, 9], 'cur_cost': 1185.0, 'intermediate_solutions': [{'tour': [7, 6, 8, 3, 5, 4, 0, 1, 9, 2], 'cur_cost': 1476.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 9, 1, 7, 4, 5, 3, 8, 2], 'cur_cost': 1075.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 3, 5, 4, 7, 1, 9, 6, 2], 'cur_cost': 939.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 4, 6, 8, 2, 5, 7, 1, 9], 'cur_cost': 1259.0, 'intermediate_solutions': [{'tour': [9, 0, 5, 6, 7, 4, 2, 3, 8, 1], 'cur_cost': 1329.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 0, 5, 6, 7, 4, 9, 3, 2, 1], 'cur_cost': 1340.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 9, 0, 5, 6, 7, 4, 2, 3, 1], 'cur_cost': 1442.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 6, 5, 0, 9, 2, 7, 1, 8, 4], dtype=int64), 'cur_cost': 1497.0, 'intermediate_solutions': [{'tour': array([3, 5, 0, 1, 2, 8, 7, 6, 4, 9]), 'cur_cost': 1654.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 3, 5, 0, 2, 8, 7, 6, 4, 9]), 'cur_cost': 1650.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 1, 3, 5, 0, 8, 7, 6, 4, 9]), 'cur_cost': 1707.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 1, 3, 5, 2, 8, 7, 6, 4, 9]), 'cur_cost': 1702.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 2, 1, 3, 5, 8, 7, 6, 4, 9]), 'cur_cost': 1702.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [2, 8, 3, 7, 4, 9, 0, 6, 5, 1], 'cur_cost': 1271.0, 'intermediate_solutions': [{'tour': [0, 4, 3, 8, 2, 5, 7, 1, 6, 9], 'cur_cost': 1336.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 6, 1, 7, 5, 9, 8, 3, 4], 'cur_cost': 1151.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 3, 8, 9, 5, 7, 1, 6, 2], 'cur_cost': 1089.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [7, 9, 3, 1, 2, 6, 0, 8, 5, 4], 'cur_cost': 1195.0, 'intermediate_solutions': [{'tour': [9, 4, 5, 3, 7, 2, 0, 6, 8, 1], 'cur_cost': 1481.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 4, 5, 1, 8, 7, 0, 2, 6, 3], 'cur_cost': 1249.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 9, 4, 5, 3, 6, 2, 0, 7, 1], 'cur_cost': 1164.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [3, 0, 5, 7, 4, 1, 8, 6, 2, 9], 'cur_cost': 1048.0, 'intermediate_solutions': [{'tour': [9, 5, 0, 1, 3, 2, 4, 7, 8, 6], 'cur_cost': 1533.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 5, 0, 8, 3, 6, 1, 7, 4, 2], 'cur_cost': 1165.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 0, 8, 3, 2, 4, 7, 1, 6, 9], 'cur_cost': 1156.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 4, 0, 3, 8, 6, 2, 5, 9], 'cur_cost': 1003.0, 'intermediate_solutions': [{'tour': [1, 0, 2, 3, 6, 5, 4, 8, 7, 9], 'cur_cost': 1436.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 0, 2, 3, 6, 5, 9, 8, 7, 4], 'cur_cost': 1183.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 2, 3, 6, 4, 7, 8, 5, 9], 'cur_cost': 1385.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:51:41,529 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:41,529 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:41,530 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=936.000, 多样性=0.882
2025-08-05 09:51:41,530 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 09:51:41,530 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 09:51:41,531 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:41,531 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.03410827442450418, 'best_improvement': -0.017391304347826087}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.010178117048346374}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.03748463485739414, 'recent_improvements': [0.06124608260638922, -0.07271657762630035, -0.013723187108399043], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 8, 'new_count': 8, 'count_change': 0, 'old_best_cost': 832.0, 'new_best_cost': 832.0, 'quality_improvement': 0.0, 'old_diversity': 0.7285714285714285, 'new_diversity': 0.7285714285714285, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配']}
2025-08-05 09:51:41,531 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 09:51:41,531 - __main__ - INFO - simple3_10 开始进化第 3 代
2025-08-05 09:51:41,531 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 09:51:41,532 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:41,532 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=936.000, 多样性=0.882
2025-08-05 09:51:41,532 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:41,533 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.882
2025-08-05 09:51:41,533 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:41,534 - EliteExpert - INFO - 精英解分析完成: 精英解数量=8, 多样性=0.729
2025-08-05 09:51:41,535 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 09:51:41,536 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:41,536 - LandscapeExpert - INFO - 添加精英解数据: 8个精英解
2025-08-05 09:51:41,536 - LandscapeExpert - INFO - 数据提取成功: 18个路径, 18个适应度值
2025-08-05 09:51:41,548 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.444, 适应度梯度: -31.722, 聚类评分: 0.000, 覆盖率: 0.014, 收敛趋势: 0.000, 多样性: 0.469
2025-08-05 09:51:41,548 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 09:51:41,548 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:51:41,549 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple3_10
2025-08-05 09:51:41,553 - visualization.landscape_visualizer - INFO - 插值约束: 62 个点被约束到最小值 832.00
2025-08-05 09:51:41,629 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple3_10\landscape_simple3_10_iter_13_20250805_095141.html
2025-08-05 09:51:41,673 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple3_10\dashboard_simple3_10_iter_13_20250805_095141.html
2025-08-05 09:51:41,673 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 13
2025-08-05 09:51:41,673 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 09:51:41,673 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1376秒
2025-08-05 09:51:41,673 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.4444444444444444, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -31.722222222222214, 'local_optima_density': 0.4444444444444444, 'gradient_variance': 57853.200617283954, 'cluster_count': 0}, 'population_state': {'diversity': 0.4694989106753813, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0143, 'fitness_entropy': 0.8371613473715502, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -31.722)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.014)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358701.5486224, 'performance_metrics': {}}}
2025-08-05 09:51:41,673 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:41,673 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:41,674 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:41,674 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:41,674 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:51:41,674 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 09:51:41,674 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:51:41,675 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:41,675 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:41,675 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:51:41,675 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:41,675 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:41,675 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 9} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:41,676 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:51:41,676 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:51:41,676 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,676 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:41,676 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,677 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,677 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,677 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,677 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1132.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:41,677 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [6, 9, 0, 1, 4, 5, 7, 2, 8, 3], 'cur_cost': 1132.0, 'intermediate_solutions': [{'tour': [7, 0, 3, 2, 6, 8, 9, 5, 4, 1], 'cur_cost': 904.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 6, 2, 3, 0, 8, 9, 5, 4, 1], 'cur_cost': 839.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 6, 3, 2, 0, 8, 9, 5, 4, 1], 'cur_cost': 936.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,677 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 1132.00)
2025-08-05 09:51:41,678 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 09:51:41,678 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:41,678 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:41,678 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 1261.0
2025-08-05 09:51:41,684 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 09:51:41,684 - ExploitationExpert - INFO - res_population_costs: [832.0, 832, 832, 832.0, 832, 832, 832, 832, 832, 832]
2025-08-05 09:51:41,685 - ExploitationExpert - INFO - res_populations: [array([0, 9, 4, 1, 7, 5, 2, 6, 3, 8], dtype=int64), array([0, 8, 3, 6, 2, 7, 1, 4, 5, 9], dtype=int64), array([0, 9, 4, 1, 7, 5, 6, 2, 3, 8], dtype=int64), array([0, 3, 6, 2, 5, 4, 7, 1, 9, 8], dtype=int64), array([0, 9, 1, 7, 4, 5, 2, 6, 3, 8], dtype=int64), array([0, 8, 3, 9, 4, 1, 7, 5, 2, 6], dtype=int64), array([0, 8, 3, 9, 1, 7, 4, 5, 2, 6], dtype=int64), array([0, 8, 3, 9, 5, 4, 1, 7, 2, 6], dtype=int64), array([0, 8, 9, 4, 1, 7, 5, 2, 6, 3], dtype=int64), array([0, 3, 2, 6, 5, 7, 1, 4, 9, 8], dtype=int64)]
2025-08-05 09:51:41,686 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:41,687 - ExploitationExpert - INFO - populations: [{'tour': [6, 9, 0, 1, 4, 5, 7, 2, 8, 3], 'cur_cost': 1132.0}, {'tour': array([4, 1, 6, 5, 7, 9, 0, 3, 2, 8], dtype=int64), 'cur_cost': 1261.0}, {'tour': [3, 8, 4, 1, 5, 9, 2, 7, 0, 6], 'cur_cost': 1263.0}, {'tour': [0, 1, 4, 8, 5, 7, 6, 2, 3, 9], 'cur_cost': 1185.0}, {'tour': [0, 3, 4, 6, 8, 2, 5, 7, 1, 9], 'cur_cost': 1259.0}, {'tour': [3, 6, 5, 0, 9, 2, 7, 1, 8, 4], 'cur_cost': 1497.0}, {'tour': [2, 8, 3, 7, 4, 9, 0, 6, 5, 1], 'cur_cost': 1271.0}, {'tour': [7, 9, 3, 1, 2, 6, 0, 8, 5, 4], 'cur_cost': 1195.0}, {'tour': [3, 0, 5, 7, 4, 1, 8, 6, 2, 9], 'cur_cost': 1048.0}, {'tour': [1, 7, 4, 0, 3, 8, 6, 2, 5, 9], 'cur_cost': 1003.0}]
2025-08-05 09:51:41,687 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:41,687 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 31, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 31, 'cache_hits': 0, 'similarity_calculations': 231, 'cache_hit_rate': 0.0, 'cache_size': 231}}
2025-08-05 09:51:41,688 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([4, 1, 6, 5, 7, 9, 0, 3, 2, 8], dtype=int64), 'cur_cost': 1261.0, 'intermediate_solutions': [{'tour': array([7, 3, 1, 2, 5, 4, 0, 8, 6, 9]), 'cur_cost': 1582.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 7, 3, 1, 5, 4, 0, 8, 6, 9]), 'cur_cost': 1498.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 2, 7, 3, 1, 4, 0, 8, 6, 9]), 'cur_cost': 1487.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 2, 7, 3, 5, 4, 0, 8, 6, 9]), 'cur_cost': 1521.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 5, 2, 7, 3, 4, 0, 8, 6, 9]), 'cur_cost': 1524.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:41,688 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1261.00)
2025-08-05 09:51:41,688 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:51:41,688 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:51:41,689 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,689 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 09:51:41,690 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,690 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,690 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,690 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,690 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1022.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:41,690 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [8, 6, 5, 1, 4, 7, 2, 0, 3, 9], 'cur_cost': 1022.0, 'intermediate_solutions': [{'tour': [3, 8, 4, 1, 5, 9, 2, 7, 6, 0], 'cur_cost': 1239.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 4, 1, 9, 5, 2, 7, 0, 6], 'cur_cost': 1288.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 3, 8, 4, 1, 5, 9, 7, 0, 6], 'cur_cost': 1120.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,690 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1022.00)
2025-08-05 09:51:41,691 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:51:41,691 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:51:41,691 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,691 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:41,691 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,692 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,692 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,692 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,692 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1174.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:41,692 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 7, 6, 3, 8, 2, 5, 4, 1, 9], 'cur_cost': 1174.0, 'intermediate_solutions': [{'tour': [0, 1, 4, 6, 5, 7, 8, 2, 3, 9], 'cur_cost': 1296.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 4, 8, 5, 7, 6, 2, 9, 3], 'cur_cost': 1240.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 4, 8, 5, 7, 2, 3, 6, 9], 'cur_cost': 1312.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,692 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1174.00)
2025-08-05 09:51:41,692 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:51:41,692 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:51:41,692 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,693 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 09:51:41,693 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,693 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,693 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,693 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,694 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1443.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:41,694 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [6, 0, 8, 2, 4, 9, 1, 3, 5, 7], 'cur_cost': 1443.0, 'intermediate_solutions': [{'tour': [0, 5, 4, 6, 8, 2, 3, 7, 1, 9], 'cur_cost': 1263.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 4, 6, 7, 5, 2, 8, 1, 9], 'cur_cost': 1480.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 4, 6, 8, 2, 7, 5, 1, 9], 'cur_cost': 1329.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,694 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1443.00)
2025-08-05 09:51:41,694 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 09:51:41,694 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:41,694 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:41,694 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 1216.0
2025-08-05 09:51:41,701 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 09:51:41,701 - ExploitationExpert - INFO - res_population_costs: [832.0, 832, 832, 832.0, 832, 832, 832, 832, 832, 832]
2025-08-05 09:51:41,702 - ExploitationExpert - INFO - res_populations: [array([0, 9, 4, 1, 7, 5, 2, 6, 3, 8], dtype=int64), array([0, 8, 3, 6, 2, 7, 1, 4, 5, 9], dtype=int64), array([0, 9, 4, 1, 7, 5, 6, 2, 3, 8], dtype=int64), array([0, 3, 6, 2, 5, 4, 7, 1, 9, 8], dtype=int64), array([0, 9, 1, 7, 4, 5, 2, 6, 3, 8], dtype=int64), array([0, 8, 3, 9, 4, 1, 7, 5, 2, 6], dtype=int64), array([0, 8, 3, 9, 1, 7, 4, 5, 2, 6], dtype=int64), array([0, 8, 3, 9, 5, 4, 1, 7, 2, 6], dtype=int64), array([0, 8, 9, 4, 1, 7, 5, 2, 6, 3], dtype=int64), array([0, 3, 2, 6, 5, 7, 1, 4, 9, 8], dtype=int64)]
2025-08-05 09:51:41,704 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:41,704 - ExploitationExpert - INFO - populations: [{'tour': [6, 9, 0, 1, 4, 5, 7, 2, 8, 3], 'cur_cost': 1132.0}, {'tour': array([4, 1, 6, 5, 7, 9, 0, 3, 2, 8], dtype=int64), 'cur_cost': 1261.0}, {'tour': [8, 6, 5, 1, 4, 7, 2, 0, 3, 9], 'cur_cost': 1022.0}, {'tour': [0, 7, 6, 3, 8, 2, 5, 4, 1, 9], 'cur_cost': 1174.0}, {'tour': [6, 0, 8, 2, 4, 9, 1, 3, 5, 7], 'cur_cost': 1443.0}, {'tour': array([3, 6, 2, 0, 4, 7, 9, 8, 1, 5], dtype=int64), 'cur_cost': 1216.0}, {'tour': [2, 8, 3, 7, 4, 9, 0, 6, 5, 1], 'cur_cost': 1271.0}, {'tour': [7, 9, 3, 1, 2, 6, 0, 8, 5, 4], 'cur_cost': 1195.0}, {'tour': [3, 0, 5, 7, 4, 1, 8, 6, 2, 9], 'cur_cost': 1048.0}, {'tour': [1, 7, 4, 0, 3, 8, 6, 2, 5, 9], 'cur_cost': 1003.0}]
2025-08-05 09:51:41,704 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:41,704 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 32, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 32, 'cache_hits': 0, 'similarity_calculations': 249, 'cache_hit_rate': 0.0, 'cache_size': 249}}
2025-08-05 09:51:41,705 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([3, 6, 2, 0, 4, 7, 9, 8, 1, 5], dtype=int64), 'cur_cost': 1216.0, 'intermediate_solutions': [{'tour': array([5, 6, 3, 0, 9, 2, 7, 1, 8, 4]), 'cur_cost': 1297.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 5, 6, 3, 9, 2, 7, 1, 8, 4]), 'cur_cost': 1451.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 0, 5, 6, 3, 2, 7, 1, 8, 4]), 'cur_cost': 1347.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 0, 5, 6, 9, 2, 7, 1, 8, 4]), 'cur_cost': 1543.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 9, 0, 5, 6, 2, 7, 1, 8, 4]), 'cur_cost': 1354.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:41,705 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1216.00)
2025-08-05 09:51:41,705 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:51:41,705 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:41,705 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:41,706 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1342.0
2025-08-05 09:51:41,714 - ExploitationExpert - INFO - res_population_num: 11
2025-08-05 09:51:41,714 - ExploitationExpert - INFO - res_population_costs: [832.0, 832, 832, 832.0, 832, 832, 832, 832, 832, 832, 832]
2025-08-05 09:51:41,714 - ExploitationExpert - INFO - res_populations: [array([0, 9, 4, 1, 7, 5, 2, 6, 3, 8], dtype=int64), array([0, 8, 3, 6, 2, 7, 1, 4, 5, 9], dtype=int64), array([0, 9, 4, 1, 7, 5, 6, 2, 3, 8], dtype=int64), array([0, 3, 6, 2, 5, 4, 7, 1, 9, 8], dtype=int64), array([0, 9, 1, 7, 4, 5, 2, 6, 3, 8], dtype=int64), array([0, 8, 3, 9, 4, 1, 7, 5, 2, 6], dtype=int64), array([0, 8, 3, 9, 1, 7, 4, 5, 2, 6], dtype=int64), array([0, 8, 3, 9, 5, 4, 1, 7, 2, 6], dtype=int64), array([0, 8, 9, 4, 1, 7, 5, 2, 6, 3], dtype=int64), array([0, 3, 2, 6, 5, 7, 1, 4, 9, 8], dtype=int64), array([0, 8, 9, 1, 7, 4, 5, 6, 2, 3], dtype=int64)]
2025-08-05 09:51:41,716 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:41,716 - ExploitationExpert - INFO - populations: [{'tour': [6, 9, 0, 1, 4, 5, 7, 2, 8, 3], 'cur_cost': 1132.0}, {'tour': array([4, 1, 6, 5, 7, 9, 0, 3, 2, 8], dtype=int64), 'cur_cost': 1261.0}, {'tour': [8, 6, 5, 1, 4, 7, 2, 0, 3, 9], 'cur_cost': 1022.0}, {'tour': [0, 7, 6, 3, 8, 2, 5, 4, 1, 9], 'cur_cost': 1174.0}, {'tour': [6, 0, 8, 2, 4, 9, 1, 3, 5, 7], 'cur_cost': 1443.0}, {'tour': array([3, 6, 2, 0, 4, 7, 9, 8, 1, 5], dtype=int64), 'cur_cost': 1216.0}, {'tour': array([0, 5, 8, 3, 1, 4, 9, 6, 2, 7], dtype=int64), 'cur_cost': 1342.0}, {'tour': [7, 9, 3, 1, 2, 6, 0, 8, 5, 4], 'cur_cost': 1195.0}, {'tour': [3, 0, 5, 7, 4, 1, 8, 6, 2, 9], 'cur_cost': 1048.0}, {'tour': [1, 7, 4, 0, 3, 8, 6, 2, 5, 9], 'cur_cost': 1003.0}]
2025-08-05 09:51:41,717 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:41,717 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 33, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 33, 'cache_hits': 0, 'similarity_calculations': 268, 'cache_hit_rate': 0.0, 'cache_size': 268}}
2025-08-05 09:51:41,718 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([0, 5, 8, 3, 1, 4, 9, 6, 2, 7], dtype=int64), 'cur_cost': 1342.0, 'intermediate_solutions': [{'tour': array([3, 8, 2, 7, 4, 9, 0, 6, 5, 1]), 'cur_cost': 1250.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 3, 8, 2, 4, 9, 0, 6, 5, 1]), 'cur_cost': 1239.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 7, 3, 8, 2, 9, 0, 6, 5, 1]), 'cur_cost': 1150.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 7, 3, 8, 4, 9, 0, 6, 5, 1]), 'cur_cost': 1446.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 4, 7, 3, 8, 9, 0, 6, 5, 1]), 'cur_cost': 1271.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:41,718 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1342.00)
2025-08-05 09:51:41,718 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:51:41,718 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:51:41,718 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,719 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 09:51:41,719 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,720 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,720 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,720 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,720 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1573.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:41,720 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [8, 0, 2, 1, 6, 9, 4, 3, 7, 5], 'cur_cost': 1573.0, 'intermediate_solutions': [{'tour': [7, 9, 3, 1, 2, 6, 0, 4, 5, 8], 'cur_cost': 1452.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 9, 4, 5, 8, 0, 6, 2, 1, 3], 'cur_cost': 1405.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 9, 1, 2, 6, 0, 8, 3, 5, 4], 'cur_cost': 1093.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,721 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1573.00)
2025-08-05 09:51:41,721 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:51:41,721 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:51:41,721 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,721 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:41,721 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,721 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,722 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,722 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,722 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1128.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:41,722 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [1, 7, 8, 4, 5, 0, 6, 2, 3, 9], 'cur_cost': 1128.0, 'intermediate_solutions': [{'tour': [9, 0, 5, 7, 4, 1, 8, 6, 2, 3], 'cur_cost': 993.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 0, 5, 7, 4, 1, 6, 8, 2, 9], 'cur_cost': 1140.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 0, 5, 4, 1, 8, 6, 7, 2, 9], 'cur_cost': 1296.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,722 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1128.00)
2025-08-05 09:51:41,722 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:51:41,722 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:51:41,722 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,723 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 09:51:41,723 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,723 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,723 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,723 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,723 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1558.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:41,723 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 2, 4, 6, 9, 5, 1, 8, 3, 7], 'cur_cost': 1558.0, 'intermediate_solutions': [{'tour': [1, 7, 2, 0, 3, 8, 6, 4, 5, 9], 'cur_cost': 1131.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 7, 4, 0, 3, 2, 6, 8, 5, 9], 'cur_cost': 1044.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 7, 4, 0, 3, 8, 2, 5, 9], 'cur_cost': 1316.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,723 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 1558.00)
2025-08-05 09:51:41,724 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:41,724 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:41,725 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [6, 9, 0, 1, 4, 5, 7, 2, 8, 3], 'cur_cost': 1132.0, 'intermediate_solutions': [{'tour': [7, 0, 3, 2, 6, 8, 9, 5, 4, 1], 'cur_cost': 904.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 6, 2, 3, 0, 8, 9, 5, 4, 1], 'cur_cost': 839.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 6, 3, 2, 0, 8, 9, 5, 4, 1], 'cur_cost': 936.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 1, 6, 5, 7, 9, 0, 3, 2, 8], dtype=int64), 'cur_cost': 1261.0, 'intermediate_solutions': [{'tour': array([7, 3, 1, 2, 5, 4, 0, 8, 6, 9]), 'cur_cost': 1582.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 7, 3, 1, 5, 4, 0, 8, 6, 9]), 'cur_cost': 1498.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 2, 7, 3, 1, 4, 0, 8, 6, 9]), 'cur_cost': 1487.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 2, 7, 3, 5, 4, 0, 8, 6, 9]), 'cur_cost': 1521.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 5, 2, 7, 3, 4, 0, 8, 6, 9]), 'cur_cost': 1524.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [8, 6, 5, 1, 4, 7, 2, 0, 3, 9], 'cur_cost': 1022.0, 'intermediate_solutions': [{'tour': [3, 8, 4, 1, 5, 9, 2, 7, 6, 0], 'cur_cost': 1239.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 4, 1, 9, 5, 2, 7, 0, 6], 'cur_cost': 1288.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 3, 8, 4, 1, 5, 9, 7, 0, 6], 'cur_cost': 1120.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 6, 3, 8, 2, 5, 4, 1, 9], 'cur_cost': 1174.0, 'intermediate_solutions': [{'tour': [0, 1, 4, 6, 5, 7, 8, 2, 3, 9], 'cur_cost': 1296.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 4, 8, 5, 7, 6, 2, 9, 3], 'cur_cost': 1240.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 4, 8, 5, 7, 2, 3, 6, 9], 'cur_cost': 1312.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [6, 0, 8, 2, 4, 9, 1, 3, 5, 7], 'cur_cost': 1443.0, 'intermediate_solutions': [{'tour': [0, 5, 4, 6, 8, 2, 3, 7, 1, 9], 'cur_cost': 1263.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 4, 6, 7, 5, 2, 8, 1, 9], 'cur_cost': 1480.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 4, 6, 8, 2, 7, 5, 1, 9], 'cur_cost': 1329.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 6, 2, 0, 4, 7, 9, 8, 1, 5], dtype=int64), 'cur_cost': 1216.0, 'intermediate_solutions': [{'tour': array([5, 6, 3, 0, 9, 2, 7, 1, 8, 4]), 'cur_cost': 1297.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 5, 6, 3, 9, 2, 7, 1, 8, 4]), 'cur_cost': 1451.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 0, 5, 6, 3, 2, 7, 1, 8, 4]), 'cur_cost': 1347.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 0, 5, 6, 9, 2, 7, 1, 8, 4]), 'cur_cost': 1543.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 9, 0, 5, 6, 2, 7, 1, 8, 4]), 'cur_cost': 1354.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 5, 8, 3, 1, 4, 9, 6, 2, 7], dtype=int64), 'cur_cost': 1342.0, 'intermediate_solutions': [{'tour': array([3, 8, 2, 7, 4, 9, 0, 6, 5, 1]), 'cur_cost': 1250.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 3, 8, 2, 4, 9, 0, 6, 5, 1]), 'cur_cost': 1239.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 7, 3, 8, 2, 9, 0, 6, 5, 1]), 'cur_cost': 1150.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 7, 3, 8, 4, 9, 0, 6, 5, 1]), 'cur_cost': 1446.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 4, 7, 3, 8, 9, 0, 6, 5, 1]), 'cur_cost': 1271.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [8, 0, 2, 1, 6, 9, 4, 3, 7, 5], 'cur_cost': 1573.0, 'intermediate_solutions': [{'tour': [7, 9, 3, 1, 2, 6, 0, 4, 5, 8], 'cur_cost': 1452.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 9, 4, 5, 8, 0, 6, 2, 1, 3], 'cur_cost': 1405.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 9, 1, 2, 6, 0, 8, 3, 5, 4], 'cur_cost': 1093.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 8, 4, 5, 0, 6, 2, 3, 9], 'cur_cost': 1128.0, 'intermediate_solutions': [{'tour': [9, 0, 5, 7, 4, 1, 8, 6, 2, 3], 'cur_cost': 993.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 0, 5, 7, 4, 1, 6, 8, 2, 9], 'cur_cost': 1140.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 0, 5, 4, 1, 8, 6, 7, 2, 9], 'cur_cost': 1296.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 4, 6, 9, 5, 1, 8, 3, 7], 'cur_cost': 1558.0, 'intermediate_solutions': [{'tour': [1, 7, 2, 0, 3, 8, 6, 4, 5, 9], 'cur_cost': 1131.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 7, 4, 0, 3, 2, 6, 8, 5, 9], 'cur_cost': 1044.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 7, 4, 0, 3, 8, 2, 5, 9], 'cur_cost': 1316.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:51:41,725 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:41,725 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:41,726 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1022.000, 多样性=0.896
2025-08-05 09:51:41,726 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 09:51:41,726 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 09:51:41,726 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:41,727 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.059878797053610856, 'best_improvement': -0.09188034188034189}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.015113350125944298}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.05341242602540227, 'recent_improvements': [-0.07271657762630035, -0.013723187108399043, 0.03410827442450418], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 11, 'new_count': 11, 'count_change': 0, 'old_best_cost': 832.0, 'new_best_cost': 832.0, 'quality_improvement': 0.0, 'old_diversity': 0.7363636363636363, 'new_diversity': 0.7363636363636363, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:51:41,728 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 09:51:41,728 - __main__ - INFO - simple3_10 开始进化第 4 代
2025-08-05 09:51:41,728 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 09:51:41,728 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:41,729 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1022.000, 多样性=0.896
2025-08-05 09:51:41,729 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:41,729 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.896
2025-08-05 09:51:41,730 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:41,731 - EliteExpert - INFO - 精英解分析完成: 精英解数量=11, 多样性=0.736
2025-08-05 09:51:41,732 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 09:51:41,732 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:41,733 - LandscapeExpert - INFO - 添加精英解数据: 11个精英解
2025-08-05 09:51:41,733 - LandscapeExpert - INFO - 数据提取成功: 21个路径, 21个适应度值
2025-08-05 09:51:41,755 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.524, 适应度梯度: -75.029, 聚类评分: 0.000, 覆盖率: 0.015, 收敛趋势: 0.000, 多样性: 0.401
2025-08-05 09:51:41,756 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 09:51:41,756 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:51:41,756 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple3_10
2025-08-05 09:51:41,762 - visualization.landscape_visualizer - INFO - 插值约束: 189 个点被约束到最小值 832.00
2025-08-05 09:51:41,863 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple3_10\landscape_simple3_10_iter_14_20250805_095141.html
2025-08-05 09:51:41,901 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple3_10\dashboard_simple3_10_iter_14_20250805_095141.html
2025-08-05 09:51:41,902 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 14
2025-08-05 09:51:41,902 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 09:51:41,902 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1702秒
2025-08-05 09:51:41,902 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5238095238095238, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -75.02857142857142, 'local_optima_density': 0.5238095238095238, 'gradient_variance': 57289.288707482985, 'cluster_count': 0}, 'population_state': {'diversity': 0.40113378684807255, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0155, 'fitness_entropy': 0.7648399694181539, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -75.029)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.015)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358701.7567406, 'performance_metrics': {}}}
2025-08-05 09:51:41,902 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:41,902 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:41,902 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:41,902 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:41,903 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 09:51:41,903 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 09:51:41,903 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 09:51:41,903 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:41,903 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:41,904 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 09:51:41,904 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:41,904 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:41,904 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:41,904 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:51:41,904 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:51:41,904 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,905 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:41,905 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,905 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,906 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,906 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,906 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1433.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:41,906 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [3, 4, 5, 9, 7, 8, 0, 6, 2, 1], 'cur_cost': 1433.0, 'intermediate_solutions': [{'tour': [9, 6, 0, 1, 4, 5, 7, 2, 8, 3], 'cur_cost': 1132.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 9, 0, 1, 4, 5, 7, 3, 8, 2], 'cur_cost': 1119.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 6, 0, 1, 4, 5, 7, 2, 8, 3], 'cur_cost': 1132.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,907 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1433.00)
2025-08-05 09:51:41,907 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:51:41,907 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:51:41,907 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,908 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 09:51:41,908 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,908 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,908 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,908 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,908 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1408.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:41,908 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [6, 1, 4, 9, 8, 2, 5, 0, 7, 3], 'cur_cost': 1408.0, 'intermediate_solutions': [{'tour': [4, 1, 6, 7, 5, 9, 0, 3, 2, 8], 'cur_cost': 1250.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 1, 8, 2, 3, 0, 9, 7, 5, 6], 'cur_cost': 1261.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 1, 6, 9, 5, 7, 0, 3, 2, 8], 'cur_cost': 1320.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,908 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1408.00)
2025-08-05 09:51:41,908 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:51:41,908 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:51:41,908 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,909 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:41,909 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,909 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,909 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,909 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,909 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1453.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:41,909 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [5, 6, 4, 8, 1, 7, 0, 3, 2, 9], 'cur_cost': 1453.0, 'intermediate_solutions': [{'tour': [8, 6, 5, 1, 9, 7, 2, 0, 3, 4], 'cur_cost': 1451.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 6, 5, 1, 4, 7, 3, 0, 2, 9], 'cur_cost': 1152.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 6, 5, 1, 4, 7, 2, 9, 0, 3], 'cur_cost': 1068.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,909 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 1453.00)
2025-08-05 09:51:41,910 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:51:41,910 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:51:41,910 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,910 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 09:51:41,910 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,910 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,910 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,911 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,911 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1355.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:41,911 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [8, 4, 5, 0, 7, 2, 6, 3, 1, 9], 'cur_cost': 1355.0, 'intermediate_solutions': [{'tour': [0, 7, 6, 3, 8, 2, 5, 1, 4, 9], 'cur_cost': 1193.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 6, 3, 8, 2, 5, 4, 9, 1], 'cur_cost': 1358.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 6, 8, 2, 3, 5, 4, 1, 9], 'cur_cost': 1261.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,911 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1355.00)
2025-08-05 09:51:41,911 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 09:51:41,911 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:41,911 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:41,912 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 1201.0
2025-08-05 09:51:41,918 - ExploitationExpert - INFO - res_population_num: 13
2025-08-05 09:51:41,918 - ExploitationExpert - INFO - res_population_costs: [832.0, 832, 832, 832.0, 832, 832, 832, 832, 832, 832, 832, 832.0, 832]
2025-08-05 09:51:41,918 - ExploitationExpert - INFO - res_populations: [array([0, 9, 4, 1, 7, 5, 2, 6, 3, 8], dtype=int64), array([0, 8, 3, 6, 2, 7, 1, 4, 5, 9], dtype=int64), array([0, 9, 4, 1, 7, 5, 6, 2, 3, 8], dtype=int64), array([0, 3, 6, 2, 5, 4, 7, 1, 9, 8], dtype=int64), array([0, 9, 1, 7, 4, 5, 2, 6, 3, 8], dtype=int64), array([0, 8, 3, 9, 4, 1, 7, 5, 2, 6], dtype=int64), array([0, 8, 3, 9, 1, 7, 4, 5, 2, 6], dtype=int64), array([0, 8, 3, 9, 5, 4, 1, 7, 2, 6], dtype=int64), array([0, 8, 9, 4, 1, 7, 5, 2, 6, 3], dtype=int64), array([0, 3, 2, 6, 5, 7, 1, 4, 9, 8], dtype=int64), array([0, 8, 9, 1, 7, 4, 5, 6, 2, 3], dtype=int64), array([0, 9, 1, 7, 4, 5, 6, 2, 3, 8], dtype=int64), array([0, 3, 6, 2, 7, 1, 4, 5, 9, 8], dtype=int64)]
2025-08-05 09:51:41,921 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:41,921 - ExploitationExpert - INFO - populations: [{'tour': [3, 4, 5, 9, 7, 8, 0, 6, 2, 1], 'cur_cost': 1433.0}, {'tour': [6, 1, 4, 9, 8, 2, 5, 0, 7, 3], 'cur_cost': 1408.0}, {'tour': [5, 6, 4, 8, 1, 7, 0, 3, 2, 9], 'cur_cost': 1453.0}, {'tour': [8, 4, 5, 0, 7, 2, 6, 3, 1, 9], 'cur_cost': 1355.0}, {'tour': array([2, 8, 7, 1, 9, 5, 4, 0, 6, 3], dtype=int64), 'cur_cost': 1201.0}, {'tour': [3, 6, 2, 0, 4, 7, 9, 8, 1, 5], 'cur_cost': 1216.0}, {'tour': [0, 5, 8, 3, 1, 4, 9, 6, 2, 7], 'cur_cost': 1342.0}, {'tour': [8, 0, 2, 1, 6, 9, 4, 3, 7, 5], 'cur_cost': 1573.0}, {'tour': [1, 7, 8, 4, 5, 0, 6, 2, 3, 9], 'cur_cost': 1128.0}, {'tour': [0, 2, 4, 6, 9, 5, 1, 8, 3, 7], 'cur_cost': 1558.0}]
2025-08-05 09:51:41,921 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:41,921 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 34, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 34, 'cache_hits': 0, 'similarity_calculations': 288, 'cache_hit_rate': 0.0, 'cache_size': 288}}
2025-08-05 09:51:41,922 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([2, 8, 7, 1, 9, 5, 4, 0, 6, 3], dtype=int64), 'cur_cost': 1201.0, 'intermediate_solutions': [{'tour': array([8, 0, 6, 2, 4, 9, 1, 3, 5, 7]), 'cur_cost': 1373.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 8, 0, 6, 4, 9, 1, 3, 5, 7]), 'cur_cost': 1438.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 2, 8, 0, 6, 9, 1, 3, 5, 7]), 'cur_cost': 1347.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 2, 8, 0, 4, 9, 1, 3, 5, 7]), 'cur_cost': 1362.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 4, 2, 8, 0, 9, 1, 3, 5, 7]), 'cur_cost': 1487.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:41,922 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1201.00)
2025-08-05 09:51:41,922 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:51:41,922 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:51:41,922 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,923 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 09:51:41,923 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,923 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,923 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,923 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,923 - ExplorationExpert - INFO - 探索路径生成完成，成本: 862.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:41,923 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [8, 3, 0, 2, 6, 5, 7, 4, 1, 9], 'cur_cost': 862.0, 'intermediate_solutions': [{'tour': [3, 1, 2, 0, 4, 7, 9, 8, 6, 5], 'cur_cost': 1492.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 6, 3, 0, 4, 7, 9, 8, 1, 5], 'cur_cost': 1155.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 6, 1, 2, 0, 4, 7, 9, 8, 5], 'cur_cost': 1446.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,923 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 862.00)
2025-08-05 09:51:41,924 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:51:41,924 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:51:41,924 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,924 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 09:51:41,924 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,924 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,924 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,924 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,925 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1421.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:41,925 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [3, 6, 0, 8, 4, 5, 2, 1, 9, 7], 'cur_cost': 1421.0, 'intermediate_solutions': [{'tour': [0, 8, 5, 3, 1, 4, 9, 6, 2, 7], 'cur_cost': 1382.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 8, 3, 1, 4, 7, 2, 6, 9], 'cur_cost': 1176.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 8, 3, 1, 9, 6, 2, 7, 4], 'cur_cost': 1342.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,925 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1421.00)
2025-08-05 09:51:41,925 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 09:51:41,925 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:41,925 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:41,925 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 1227.0
2025-08-05 09:51:42,959 - ExploitationExpert - INFO - res_population_num: 13
2025-08-05 09:51:42,963 - ExploitationExpert - INFO - res_population_costs: [832.0, 832, 832, 832.0, 832, 832, 832, 832, 832, 832, 832, 832.0, 832]
2025-08-05 09:51:42,963 - ExploitationExpert - INFO - res_populations: [array([0, 9, 4, 1, 7, 5, 2, 6, 3, 8], dtype=int64), array([0, 8, 3, 6, 2, 7, 1, 4, 5, 9], dtype=int64), array([0, 9, 4, 1, 7, 5, 6, 2, 3, 8], dtype=int64), array([0, 3, 6, 2, 5, 4, 7, 1, 9, 8], dtype=int64), array([0, 9, 1, 7, 4, 5, 2, 6, 3, 8], dtype=int64), array([0, 8, 3, 9, 4, 1, 7, 5, 2, 6], dtype=int64), array([0, 8, 3, 9, 1, 7, 4, 5, 2, 6], dtype=int64), array([0, 8, 3, 9, 5, 4, 1, 7, 2, 6], dtype=int64), array([0, 8, 9, 4, 1, 7, 5, 2, 6, 3], dtype=int64), array([0, 3, 2, 6, 5, 7, 1, 4, 9, 8], dtype=int64), array([0, 8, 9, 1, 7, 4, 5, 6, 2, 3], dtype=int64), array([0, 9, 1, 7, 4, 5, 6, 2, 3, 8], dtype=int64), array([0, 3, 6, 2, 7, 1, 4, 5, 9, 8], dtype=int64)]
2025-08-05 09:51:42,965 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:42,965 - ExploitationExpert - INFO - populations: [{'tour': [3, 4, 5, 9, 7, 8, 0, 6, 2, 1], 'cur_cost': 1433.0}, {'tour': [6, 1, 4, 9, 8, 2, 5, 0, 7, 3], 'cur_cost': 1408.0}, {'tour': [5, 6, 4, 8, 1, 7, 0, 3, 2, 9], 'cur_cost': 1453.0}, {'tour': [8, 4, 5, 0, 7, 2, 6, 3, 1, 9], 'cur_cost': 1355.0}, {'tour': array([2, 8, 7, 1, 9, 5, 4, 0, 6, 3], dtype=int64), 'cur_cost': 1201.0}, {'tour': [8, 3, 0, 2, 6, 5, 7, 4, 1, 9], 'cur_cost': 862.0}, {'tour': [3, 6, 0, 8, 4, 5, 2, 1, 9, 7], 'cur_cost': 1421.0}, {'tour': array([7, 0, 5, 8, 3, 2, 6, 4, 1, 9], dtype=int64), 'cur_cost': 1227.0}, {'tour': [1, 7, 8, 4, 5, 0, 6, 2, 3, 9], 'cur_cost': 1128.0}, {'tour': [0, 2, 4, 6, 9, 5, 1, 8, 3, 7], 'cur_cost': 1558.0}]
2025-08-05 09:51:42,966 - ExploitationExpert - INFO - 局部搜索耗时: 1.04秒，最大迭代次数: 10
2025-08-05 09:51:42,966 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 35, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 35, 'cache_hits': 0, 'similarity_calculations': 288, 'cache_hit_rate': 0.0, 'cache_size': 288}}
2025-08-05 09:51:42,966 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([7, 0, 5, 8, 3, 2, 6, 4, 1, 9], dtype=int64), 'cur_cost': 1227.0, 'intermediate_solutions': [{'tour': array([2, 0, 8, 1, 6, 9, 4, 3, 7, 5]), 'cur_cost': 1572.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 2, 0, 8, 6, 9, 4, 3, 7, 5]), 'cur_cost': 1426.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 1, 2, 0, 8, 9, 4, 3, 7, 5]), 'cur_cost': 1486.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 1, 2, 0, 6, 9, 4, 3, 7, 5]), 'cur_cost': 1617.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 6, 1, 2, 0, 9, 4, 3, 7, 5]), 'cur_cost': 1568.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:42,966 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1227.00)
2025-08-05 09:51:42,966 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:51:42,966 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:51:42,966 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:42,967 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:42,967 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:42,967 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:42,967 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:42,967 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:42,967 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1264.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:42,968 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 4, 7, 6, 9, 8, 3, 2, 5, 1], 'cur_cost': 1264.0, 'intermediate_solutions': [{'tour': [1, 7, 8, 4, 5, 0, 9, 2, 3, 6], 'cur_cost': 1332.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 7, 8, 4, 5, 3, 2, 6, 0, 9], 'cur_cost': 1174.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 8, 4, 5, 0, 6, 3, 2, 9], 'cur_cost': 1271.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:42,968 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 1264.00)
2025-08-05 09:51:42,968 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 09:51:42,968 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:42,968 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:42,969 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 1561.0
2025-08-05 09:51:42,976 - ExploitationExpert - INFO - res_population_num: 13
2025-08-05 09:51:42,976 - ExploitationExpert - INFO - res_population_costs: [832.0, 832, 832, 832.0, 832, 832, 832, 832, 832, 832, 832, 832.0, 832]
2025-08-05 09:51:42,977 - ExploitationExpert - INFO - res_populations: [array([0, 9, 4, 1, 7, 5, 2, 6, 3, 8], dtype=int64), array([0, 8, 3, 6, 2, 7, 1, 4, 5, 9], dtype=int64), array([0, 9, 4, 1, 7, 5, 6, 2, 3, 8], dtype=int64), array([0, 3, 6, 2, 5, 4, 7, 1, 9, 8], dtype=int64), array([0, 9, 1, 7, 4, 5, 2, 6, 3, 8], dtype=int64), array([0, 8, 3, 9, 4, 1, 7, 5, 2, 6], dtype=int64), array([0, 8, 3, 9, 1, 7, 4, 5, 2, 6], dtype=int64), array([0, 8, 3, 9, 5, 4, 1, 7, 2, 6], dtype=int64), array([0, 8, 9, 4, 1, 7, 5, 2, 6, 3], dtype=int64), array([0, 3, 2, 6, 5, 7, 1, 4, 9, 8], dtype=int64), array([0, 8, 9, 1, 7, 4, 5, 6, 2, 3], dtype=int64), array([0, 9, 1, 7, 4, 5, 6, 2, 3, 8], dtype=int64), array([0, 3, 6, 2, 7, 1, 4, 5, 9, 8], dtype=int64)]
2025-08-05 09:51:42,980 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:42,980 - ExploitationExpert - INFO - populations: [{'tour': [3, 4, 5, 9, 7, 8, 0, 6, 2, 1], 'cur_cost': 1433.0}, {'tour': [6, 1, 4, 9, 8, 2, 5, 0, 7, 3], 'cur_cost': 1408.0}, {'tour': [5, 6, 4, 8, 1, 7, 0, 3, 2, 9], 'cur_cost': 1453.0}, {'tour': [8, 4, 5, 0, 7, 2, 6, 3, 1, 9], 'cur_cost': 1355.0}, {'tour': array([2, 8, 7, 1, 9, 5, 4, 0, 6, 3], dtype=int64), 'cur_cost': 1201.0}, {'tour': [8, 3, 0, 2, 6, 5, 7, 4, 1, 9], 'cur_cost': 862.0}, {'tour': [3, 6, 0, 8, 4, 5, 2, 1, 9, 7], 'cur_cost': 1421.0}, {'tour': array([7, 0, 5, 8, 3, 2, 6, 4, 1, 9], dtype=int64), 'cur_cost': 1227.0}, {'tour': [0, 4, 7, 6, 9, 8, 3, 2, 5, 1], 'cur_cost': 1264.0}, {'tour': array([2, 5, 7, 0, 8, 1, 6, 9, 3, 4], dtype=int64), 'cur_cost': 1561.0}]
2025-08-05 09:51:42,981 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:42,981 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 36, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 36, 'cache_hits': 0, 'similarity_calculations': 288, 'cache_hit_rate': 0.0, 'cache_size': 288}}
2025-08-05 09:51:42,982 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([2, 5, 7, 0, 8, 1, 6, 9, 3, 4], dtype=int64), 'cur_cost': 1561.0, 'intermediate_solutions': [{'tour': array([4, 2, 0, 6, 9, 5, 1, 8, 3, 7]), 'cur_cost': 1348.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 4, 2, 0, 9, 5, 1, 8, 3, 7]), 'cur_cost': 1488.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 6, 4, 2, 0, 5, 1, 8, 3, 7]), 'cur_cost': 1551.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 6, 4, 2, 9, 5, 1, 8, 3, 7]), 'cur_cost': 1558.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 9, 6, 4, 2, 5, 1, 8, 3, 7]), 'cur_cost': 1567.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:42,982 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1561.00)
2025-08-05 09:51:42,982 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:42,982 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:42,983 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [3, 4, 5, 9, 7, 8, 0, 6, 2, 1], 'cur_cost': 1433.0, 'intermediate_solutions': [{'tour': [9, 6, 0, 1, 4, 5, 7, 2, 8, 3], 'cur_cost': 1132.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 9, 0, 1, 4, 5, 7, 3, 8, 2], 'cur_cost': 1119.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 6, 0, 1, 4, 5, 7, 2, 8, 3], 'cur_cost': 1132.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [6, 1, 4, 9, 8, 2, 5, 0, 7, 3], 'cur_cost': 1408.0, 'intermediate_solutions': [{'tour': [4, 1, 6, 7, 5, 9, 0, 3, 2, 8], 'cur_cost': 1250.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 1, 8, 2, 3, 0, 9, 7, 5, 6], 'cur_cost': 1261.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 1, 6, 9, 5, 7, 0, 3, 2, 8], 'cur_cost': 1320.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [5, 6, 4, 8, 1, 7, 0, 3, 2, 9], 'cur_cost': 1453.0, 'intermediate_solutions': [{'tour': [8, 6, 5, 1, 9, 7, 2, 0, 3, 4], 'cur_cost': 1451.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 6, 5, 1, 4, 7, 3, 0, 2, 9], 'cur_cost': 1152.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 6, 5, 1, 4, 7, 2, 9, 0, 3], 'cur_cost': 1068.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [8, 4, 5, 0, 7, 2, 6, 3, 1, 9], 'cur_cost': 1355.0, 'intermediate_solutions': [{'tour': [0, 7, 6, 3, 8, 2, 5, 1, 4, 9], 'cur_cost': 1193.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 6, 3, 8, 2, 5, 4, 9, 1], 'cur_cost': 1358.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 6, 8, 2, 3, 5, 4, 1, 9], 'cur_cost': 1261.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 8, 7, 1, 9, 5, 4, 0, 6, 3], dtype=int64), 'cur_cost': 1201.0, 'intermediate_solutions': [{'tour': array([8, 0, 6, 2, 4, 9, 1, 3, 5, 7]), 'cur_cost': 1373.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 8, 0, 6, 4, 9, 1, 3, 5, 7]), 'cur_cost': 1438.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 2, 8, 0, 6, 9, 1, 3, 5, 7]), 'cur_cost': 1347.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 2, 8, 0, 4, 9, 1, 3, 5, 7]), 'cur_cost': 1362.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 4, 2, 8, 0, 9, 1, 3, 5, 7]), 'cur_cost': 1487.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [8, 3, 0, 2, 6, 5, 7, 4, 1, 9], 'cur_cost': 862.0, 'intermediate_solutions': [{'tour': [3, 1, 2, 0, 4, 7, 9, 8, 6, 5], 'cur_cost': 1492.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 6, 3, 0, 4, 7, 9, 8, 1, 5], 'cur_cost': 1155.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 6, 1, 2, 0, 4, 7, 9, 8, 5], 'cur_cost': 1446.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [3, 6, 0, 8, 4, 5, 2, 1, 9, 7], 'cur_cost': 1421.0, 'intermediate_solutions': [{'tour': [0, 8, 5, 3, 1, 4, 9, 6, 2, 7], 'cur_cost': 1382.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 8, 3, 1, 4, 7, 2, 6, 9], 'cur_cost': 1176.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 8, 3, 1, 9, 6, 2, 7, 4], 'cur_cost': 1342.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 0, 5, 8, 3, 2, 6, 4, 1, 9], dtype=int64), 'cur_cost': 1227.0, 'intermediate_solutions': [{'tour': array([2, 0, 8, 1, 6, 9, 4, 3, 7, 5]), 'cur_cost': 1572.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 2, 0, 8, 6, 9, 4, 3, 7, 5]), 'cur_cost': 1426.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 1, 2, 0, 8, 9, 4, 3, 7, 5]), 'cur_cost': 1486.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 1, 2, 0, 6, 9, 4, 3, 7, 5]), 'cur_cost': 1617.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 6, 1, 2, 0, 9, 4, 3, 7, 5]), 'cur_cost': 1568.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 7, 6, 9, 8, 3, 2, 5, 1], 'cur_cost': 1264.0, 'intermediate_solutions': [{'tour': [1, 7, 8, 4, 5, 0, 9, 2, 3, 6], 'cur_cost': 1332.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 7, 8, 4, 5, 3, 2, 6, 0, 9], 'cur_cost': 1174.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 8, 4, 5, 0, 6, 3, 2, 9], 'cur_cost': 1271.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 5, 7, 0, 8, 1, 6, 9, 3, 4], dtype=int64), 'cur_cost': 1561.0, 'intermediate_solutions': [{'tour': array([4, 2, 0, 6, 9, 5, 1, 8, 3, 7]), 'cur_cost': 1348.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 4, 2, 0, 9, 5, 1, 8, 3, 7]), 'cur_cost': 1488.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 6, 4, 2, 0, 5, 1, 8, 3, 7]), 'cur_cost': 1551.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 6, 4, 2, 9, 5, 1, 8, 3, 7]), 'cur_cost': 1558.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 9, 6, 4, 2, 5, 1, 8, 3, 7]), 'cur_cost': 1567.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 09:51:42,983 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:42,983 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:42,984 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=862.000, 多样性=0.891
2025-08-05 09:51:42,985 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 09:51:42,985 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 09:51:42,985 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:42,985 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.04601153765404451, 'best_improvement': 0.15655577299412915}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.004962779156327451}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.023077804972605906, 'recent_improvements': [-0.013723187108399043, 0.03410827442450418, -0.059878797053610856], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 13, 'new_count': 13, 'count_change': 0, 'old_best_cost': 832.0, 'new_best_cost': 832.0, 'quality_improvement': 0.0, 'old_diversity': 0.7333333333333333, 'new_diversity': 0.7333333333333333, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:51:42,986 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 09:51:42,986 - __main__ - INFO - simple3_10 开始进化第 5 代
2025-08-05 09:51:42,986 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 09:51:42,986 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:42,987 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=862.000, 多样性=0.891
2025-08-05 09:51:42,987 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:42,988 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.891
2025-08-05 09:51:42,988 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:42,990 - EliteExpert - INFO - 精英解分析完成: 精英解数量=13, 多样性=0.733
2025-08-05 09:51:42,991 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 09:51:42,992 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:42,992 - LandscapeExpert - INFO - 添加精英解数据: 13个精英解
2025-08-05 09:51:42,992 - LandscapeExpert - INFO - 数据提取成功: 23个路径, 23个适应度值
2025-08-05 09:51:43,010 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.565, 适应度梯度: -81.017, 聚类评分: 0.000, 覆盖率: 0.017, 收敛趋势: 0.000, 多样性: 0.370
2025-08-05 09:51:43,010 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 09:51:43,010 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:51:43,010 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple3_10
2025-08-05 09:51:43,015 - visualization.landscape_visualizer - INFO - 插值约束: 227 个点被约束到最小值 832.00
2025-08-05 09:51:43,090 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple3_10\landscape_simple3_10_iter_15_20250805_095143.html
2025-08-05 09:51:43,127 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple3_10\dashboard_simple3_10_iter_15_20250805_095143.html
2025-08-05 09:51:43,127 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 15
2025-08-05 09:51:43,127 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 09:51:43,127 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1363秒
2025-08-05 09:51:43,127 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5652173913043478, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -81.01739130434783, 'local_optima_density': 0.5652173913043478, 'gradient_variance': 37801.043175803396, 'cluster_count': 0}, 'population_state': {'diversity': 0.3699948444749957, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0167, 'fitness_entropy': 0.7345690731272254, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -81.017)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.017)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358703.0103002, 'performance_metrics': {}}}
2025-08-05 09:51:43,127 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:43,128 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:43,128 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:43,128 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:43,128 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:51:43,128 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 09:51:43,128 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:51:43,129 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:43,129 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:43,129 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:51:43,129 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:43,129 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:43,130 - experts.management.collaboration_manager - INFO - 识别精英个体: {4, 5} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:43,130 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-05 09:51:43,130 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:43,130 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:43,130 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 1300.0
2025-08-05 09:51:43,137 - ExploitationExpert - INFO - res_population_num: 13
2025-08-05 09:51:43,137 - ExploitationExpert - INFO - res_population_costs: [832.0, 832, 832, 832.0, 832, 832, 832, 832, 832, 832, 832, 832.0, 832]
2025-08-05 09:51:43,137 - ExploitationExpert - INFO - res_populations: [array([0, 9, 4, 1, 7, 5, 2, 6, 3, 8], dtype=int64), array([0, 8, 3, 6, 2, 7, 1, 4, 5, 9], dtype=int64), array([0, 9, 4, 1, 7, 5, 6, 2, 3, 8], dtype=int64), array([0, 3, 6, 2, 5, 4, 7, 1, 9, 8], dtype=int64), array([0, 9, 1, 7, 4, 5, 2, 6, 3, 8], dtype=int64), array([0, 8, 3, 9, 4, 1, 7, 5, 2, 6], dtype=int64), array([0, 8, 3, 9, 1, 7, 4, 5, 2, 6], dtype=int64), array([0, 8, 3, 9, 5, 4, 1, 7, 2, 6], dtype=int64), array([0, 8, 9, 4, 1, 7, 5, 2, 6, 3], dtype=int64), array([0, 3, 2, 6, 5, 7, 1, 4, 9, 8], dtype=int64), array([0, 8, 9, 1, 7, 4, 5, 6, 2, 3], dtype=int64), array([0, 9, 1, 7, 4, 5, 6, 2, 3, 8], dtype=int64), array([0, 3, 6, 2, 7, 1, 4, 5, 9, 8], dtype=int64)]
2025-08-05 09:51:43,140 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:43,140 - ExploitationExpert - INFO - populations: [{'tour': array([1, 7, 8, 0, 2, 9, 4, 3, 6, 5], dtype=int64), 'cur_cost': 1300.0}, {'tour': [6, 1, 4, 9, 8, 2, 5, 0, 7, 3], 'cur_cost': 1408.0}, {'tour': [5, 6, 4, 8, 1, 7, 0, 3, 2, 9], 'cur_cost': 1453.0}, {'tour': [8, 4, 5, 0, 7, 2, 6, 3, 1, 9], 'cur_cost': 1355.0}, {'tour': [2, 8, 7, 1, 9, 5, 4, 0, 6, 3], 'cur_cost': 1201.0}, {'tour': [8, 3, 0, 2, 6, 5, 7, 4, 1, 9], 'cur_cost': 862.0}, {'tour': [3, 6, 0, 8, 4, 5, 2, 1, 9, 7], 'cur_cost': 1421.0}, {'tour': [7, 0, 5, 8, 3, 2, 6, 4, 1, 9], 'cur_cost': 1227.0}, {'tour': [0, 4, 7, 6, 9, 8, 3, 2, 5, 1], 'cur_cost': 1264.0}, {'tour': [2, 5, 7, 0, 8, 1, 6, 9, 3, 4], 'cur_cost': 1561.0}]
2025-08-05 09:51:43,140 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:43,140 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 37, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 37, 'cache_hits': 0, 'similarity_calculations': 288, 'cache_hit_rate': 0.0, 'cache_size': 288}}
2025-08-05 09:51:43,141 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([1, 7, 8, 0, 2, 9, 4, 3, 6, 5], dtype=int64), 'cur_cost': 1300.0, 'intermediate_solutions': [{'tour': array([5, 4, 3, 9, 7, 8, 0, 6, 2, 1]), 'cur_cost': 1258.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([9, 5, 4, 3, 7, 8, 0, 6, 2, 1]), 'cur_cost': 1395.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 9, 5, 4, 3, 8, 0, 6, 2, 1]), 'cur_cost': 1103.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 9, 5, 4, 7, 8, 0, 6, 2, 1]), 'cur_cost': 1203.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 7, 9, 5, 4, 8, 0, 6, 2, 1]), 'cur_cost': 1428.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:43,141 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1300.00)
2025-08-05 09:51:43,141 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:51:43,141 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:51:43,142 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:43,142 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 09:51:43,142 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,142 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,143 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,143 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,143 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1391.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:43,143 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [6, 1, 9, 7, 5, 0, 3, 4, 8, 2], 'cur_cost': 1391.0, 'intermediate_solutions': [{'tour': [6, 5, 4, 9, 8, 2, 1, 0, 7, 3], 'cur_cost': 1485.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 1, 4, 9, 8, 0, 5, 2, 7, 3], 'cur_cost': 1329.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 1, 4, 2, 9, 8, 5, 0, 7, 3], 'cur_cost': 1504.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:43,143 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1391.00)
2025-08-05 09:51:43,143 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-05 09:51:43,143 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:43,144 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:43,144 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 1528.0
2025-08-05 09:51:43,150 - ExploitationExpert - INFO - res_population_num: 13
2025-08-05 09:51:43,150 - ExploitationExpert - INFO - res_population_costs: [832.0, 832, 832, 832.0, 832, 832, 832, 832, 832, 832, 832, 832.0, 832]
2025-08-05 09:51:43,151 - ExploitationExpert - INFO - res_populations: [array([0, 9, 4, 1, 7, 5, 2, 6, 3, 8], dtype=int64), array([0, 8, 3, 6, 2, 7, 1, 4, 5, 9], dtype=int64), array([0, 9, 4, 1, 7, 5, 6, 2, 3, 8], dtype=int64), array([0, 3, 6, 2, 5, 4, 7, 1, 9, 8], dtype=int64), array([0, 9, 1, 7, 4, 5, 2, 6, 3, 8], dtype=int64), array([0, 8, 3, 9, 4, 1, 7, 5, 2, 6], dtype=int64), array([0, 8, 3, 9, 1, 7, 4, 5, 2, 6], dtype=int64), array([0, 8, 3, 9, 5, 4, 1, 7, 2, 6], dtype=int64), array([0, 8, 9, 4, 1, 7, 5, 2, 6, 3], dtype=int64), array([0, 3, 2, 6, 5, 7, 1, 4, 9, 8], dtype=int64), array([0, 8, 9, 1, 7, 4, 5, 6, 2, 3], dtype=int64), array([0, 9, 1, 7, 4, 5, 6, 2, 3, 8], dtype=int64), array([0, 3, 6, 2, 7, 1, 4, 5, 9, 8], dtype=int64)]
2025-08-05 09:51:43,153 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:43,153 - ExploitationExpert - INFO - populations: [{'tour': array([1, 7, 8, 0, 2, 9, 4, 3, 6, 5], dtype=int64), 'cur_cost': 1300.0}, {'tour': [6, 1, 9, 7, 5, 0, 3, 4, 8, 2], 'cur_cost': 1391.0}, {'tour': array([2, 5, 0, 3, 7, 9, 1, 8, 4, 6], dtype=int64), 'cur_cost': 1528.0}, {'tour': [8, 4, 5, 0, 7, 2, 6, 3, 1, 9], 'cur_cost': 1355.0}, {'tour': [2, 8, 7, 1, 9, 5, 4, 0, 6, 3], 'cur_cost': 1201.0}, {'tour': [8, 3, 0, 2, 6, 5, 7, 4, 1, 9], 'cur_cost': 862.0}, {'tour': [3, 6, 0, 8, 4, 5, 2, 1, 9, 7], 'cur_cost': 1421.0}, {'tour': [7, 0, 5, 8, 3, 2, 6, 4, 1, 9], 'cur_cost': 1227.0}, {'tour': [0, 4, 7, 6, 9, 8, 3, 2, 5, 1], 'cur_cost': 1264.0}, {'tour': [2, 5, 7, 0, 8, 1, 6, 9, 3, 4], 'cur_cost': 1561.0}]
2025-08-05 09:51:43,154 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:43,154 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 38, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 38, 'cache_hits': 0, 'similarity_calculations': 288, 'cache_hit_rate': 0.0, 'cache_size': 288}}
2025-08-05 09:51:43,154 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([2, 5, 0, 3, 7, 9, 1, 8, 4, 6], dtype=int64), 'cur_cost': 1528.0, 'intermediate_solutions': [{'tour': array([4, 6, 5, 8, 1, 7, 0, 3, 2, 9]), 'cur_cost': 1430.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 4, 6, 5, 1, 7, 0, 3, 2, 9]), 'cur_cost': 1292.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 8, 4, 6, 5, 7, 0, 3, 2, 9]), 'cur_cost': 1504.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 8, 4, 6, 1, 7, 0, 3, 2, 9]), 'cur_cost': 1456.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 1, 8, 4, 6, 7, 0, 3, 2, 9]), 'cur_cost': 1530.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:43,155 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1528.00)
2025-08-05 09:51:43,155 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:51:43,155 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:51:43,155 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:43,155 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:43,156 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,156 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,156 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,156 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,156 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1388.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:43,156 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [3, 5, 0, 1, 8, 6, 2, 4, 7, 9], 'cur_cost': 1388.0, 'intermediate_solutions': [{'tour': [8, 4, 5, 0, 2, 7, 6, 3, 1, 9], 'cur_cost': 1428.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 4, 5, 0, 7, 2, 6, 1, 3, 9], 'cur_cost': 1416.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 4, 0, 7, 2, 6, 3, 1, 5, 9], 'cur_cost': 1399.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:43,157 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1388.00)
2025-08-05 09:51:43,157 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:51:43,157 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:51:43,157 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:43,157 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:43,157 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,157 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,158 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,158 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,158 - ExplorationExpert - INFO - 探索路径生成完成，成本: 912.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:43,158 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [2, 9, 4, 7, 1, 5, 0, 8, 3, 6], 'cur_cost': 912.0, 'intermediate_solutions': [{'tour': [2, 8, 6, 1, 9, 5, 4, 0, 7, 3], 'cur_cost': 1486.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 7, 1, 9, 5, 3, 6, 0, 4], 'cur_cost': 1394.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 6, 8, 7, 1, 9, 5, 4, 0, 3], 'cur_cost': 1106.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:43,158 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 912.00)
2025-08-05 09:51:43,158 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:51:43,158 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:51:43,158 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:43,159 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 09:51:43,159 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,159 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,159 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,159 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,159 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1393.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:43,159 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [9, 0, 5, 7, 8, 2, 3, 4, 1, 6], 'cur_cost': 1393.0, 'intermediate_solutions': [{'tour': [8, 2, 0, 3, 6, 5, 7, 4, 1, 9], 'cur_cost': 992.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 3, 0, 2, 6, 5, 7, 9, 1, 4], 'cur_cost': 1079.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 0, 2, 6, 5, 7, 4, 1, 9, 3], 'cur_cost': 856.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:43,159 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 1393.00)
2025-08-05 09:51:43,160 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:51:43,160 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:51:43,160 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:43,160 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 09:51:43,160 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,160 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,161 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,161 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,161 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1076.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:43,161 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [5, 4, 1, 6, 8, 0, 2, 3, 9, 7], 'cur_cost': 1076.0, 'intermediate_solutions': [{'tour': [3, 6, 0, 8, 4, 9, 2, 1, 5, 7], 'cur_cost': 1378.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 0, 6, 3, 4, 5, 2, 1, 9, 7], 'cur_cost': 1426.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 6, 0, 4, 5, 2, 1, 9, 8, 7], 'cur_cost': 1461.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:43,161 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1076.00)
2025-08-05 09:51:43,161 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:51:43,161 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:51:43,161 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:43,162 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 09:51:43,162 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,162 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,162 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,162 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,162 - ExplorationExpert - INFO - 探索路径生成完成，成本: 946.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:43,162 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [2, 0, 3, 9, 5, 7, 1, 4, 8, 6], 'cur_cost': 946.0, 'intermediate_solutions': [{'tour': [1, 0, 5, 8, 3, 2, 6, 4, 7, 9], 'cur_cost': 1245.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 0, 5, 8, 3, 9, 1, 4, 6, 2], 'cur_cost': 1227.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 6, 0, 5, 8, 3, 2, 4, 1, 9], 'cur_cost': 1289.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:43,163 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 946.00)
2025-08-05 09:51:43,163 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:51:43,163 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:51:43,163 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:43,163 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 09:51:43,163 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,163 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,164 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,164 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:43,164 - ExplorationExpert - INFO - 探索路径生成完成，成本: 964.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:43,164 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [9, 0, 8, 3, 5, 4, 1, 7, 6, 2], 'cur_cost': 964.0, 'intermediate_solutions': [{'tour': [0, 1, 7, 6, 9, 8, 3, 2, 5, 4], 'cur_cost': 1230.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 7, 6, 9, 8, 3, 2, 1, 5], 'cur_cost': 1262.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 7, 6, 2, 9, 8, 3, 5, 1], 'cur_cost': 1237.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:43,164 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 964.00)
2025-08-05 09:51:43,164 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 09:51:43,164 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:43,164 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:43,165 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 1351.0
2025-08-05 09:51:43,171 - ExploitationExpert - INFO - res_population_num: 13
2025-08-05 09:51:43,172 - ExploitationExpert - INFO - res_population_costs: [832.0, 832, 832, 832.0, 832, 832, 832, 832, 832, 832, 832, 832.0, 832]
2025-08-05 09:51:43,172 - ExploitationExpert - INFO - res_populations: [array([0, 9, 4, 1, 7, 5, 2, 6, 3, 8], dtype=int64), array([0, 8, 3, 6, 2, 7, 1, 4, 5, 9], dtype=int64), array([0, 9, 4, 1, 7, 5, 6, 2, 3, 8], dtype=int64), array([0, 3, 6, 2, 5, 4, 7, 1, 9, 8], dtype=int64), array([0, 9, 1, 7, 4, 5, 2, 6, 3, 8], dtype=int64), array([0, 8, 3, 9, 4, 1, 7, 5, 2, 6], dtype=int64), array([0, 8, 3, 9, 1, 7, 4, 5, 2, 6], dtype=int64), array([0, 8, 3, 9, 5, 4, 1, 7, 2, 6], dtype=int64), array([0, 8, 9, 4, 1, 7, 5, 2, 6, 3], dtype=int64), array([0, 3, 2, 6, 5, 7, 1, 4, 9, 8], dtype=int64), array([0, 8, 9, 1, 7, 4, 5, 6, 2, 3], dtype=int64), array([0, 9, 1, 7, 4, 5, 6, 2, 3, 8], dtype=int64), array([0, 3, 6, 2, 7, 1, 4, 5, 9, 8], dtype=int64)]
2025-08-05 09:51:43,174 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:43,174 - ExploitationExpert - INFO - populations: [{'tour': array([1, 7, 8, 0, 2, 9, 4, 3, 6, 5], dtype=int64), 'cur_cost': 1300.0}, {'tour': [6, 1, 9, 7, 5, 0, 3, 4, 8, 2], 'cur_cost': 1391.0}, {'tour': array([2, 5, 0, 3, 7, 9, 1, 8, 4, 6], dtype=int64), 'cur_cost': 1528.0}, {'tour': [3, 5, 0, 1, 8, 6, 2, 4, 7, 9], 'cur_cost': 1388.0}, {'tour': [2, 9, 4, 7, 1, 5, 0, 8, 3, 6], 'cur_cost': 912.0}, {'tour': [9, 0, 5, 7, 8, 2, 3, 4, 1, 6], 'cur_cost': 1393.0}, {'tour': [5, 4, 1, 6, 8, 0, 2, 3, 9, 7], 'cur_cost': 1076.0}, {'tour': [2, 0, 3, 9, 5, 7, 1, 4, 8, 6], 'cur_cost': 946.0}, {'tour': [9, 0, 8, 3, 5, 4, 1, 7, 6, 2], 'cur_cost': 964.0}, {'tour': array([7, 9, 2, 0, 8, 5, 1, 3, 6, 4], dtype=int64), 'cur_cost': 1351.0}]
2025-08-05 09:51:43,175 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:43,175 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 39, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 39, 'cache_hits': 0, 'similarity_calculations': 288, 'cache_hit_rate': 0.0, 'cache_size': 288}}
2025-08-05 09:51:43,176 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([7, 9, 2, 0, 8, 5, 1, 3, 6, 4], dtype=int64), 'cur_cost': 1351.0, 'intermediate_solutions': [{'tour': array([7, 5, 2, 0, 8, 1, 6, 9, 3, 4]), 'cur_cost': 1362.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 7, 5, 2, 8, 1, 6, 9, 3, 4]), 'cur_cost': 1616.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 0, 7, 5, 2, 1, 6, 9, 3, 4]), 'cur_cost': 1561.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 0, 7, 5, 8, 1, 6, 9, 3, 4]), 'cur_cost': 1617.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 8, 0, 7, 5, 1, 6, 9, 3, 4]), 'cur_cost': 1424.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:43,176 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1351.00)
2025-08-05 09:51:43,176 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:43,176 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:43,177 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([1, 7, 8, 0, 2, 9, 4, 3, 6, 5], dtype=int64), 'cur_cost': 1300.0, 'intermediate_solutions': [{'tour': array([5, 4, 3, 9, 7, 8, 0, 6, 2, 1]), 'cur_cost': 1258.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([9, 5, 4, 3, 7, 8, 0, 6, 2, 1]), 'cur_cost': 1395.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 9, 5, 4, 3, 8, 0, 6, 2, 1]), 'cur_cost': 1103.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 9, 5, 4, 7, 8, 0, 6, 2, 1]), 'cur_cost': 1203.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 7, 9, 5, 4, 8, 0, 6, 2, 1]), 'cur_cost': 1428.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [6, 1, 9, 7, 5, 0, 3, 4, 8, 2], 'cur_cost': 1391.0, 'intermediate_solutions': [{'tour': [6, 5, 4, 9, 8, 2, 1, 0, 7, 3], 'cur_cost': 1485.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 1, 4, 9, 8, 0, 5, 2, 7, 3], 'cur_cost': 1329.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 1, 4, 2, 9, 8, 5, 0, 7, 3], 'cur_cost': 1504.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 5, 0, 3, 7, 9, 1, 8, 4, 6], dtype=int64), 'cur_cost': 1528.0, 'intermediate_solutions': [{'tour': array([4, 6, 5, 8, 1, 7, 0, 3, 2, 9]), 'cur_cost': 1430.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 4, 6, 5, 1, 7, 0, 3, 2, 9]), 'cur_cost': 1292.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 8, 4, 6, 5, 7, 0, 3, 2, 9]), 'cur_cost': 1504.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 8, 4, 6, 1, 7, 0, 3, 2, 9]), 'cur_cost': 1456.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 1, 8, 4, 6, 7, 0, 3, 2, 9]), 'cur_cost': 1530.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [3, 5, 0, 1, 8, 6, 2, 4, 7, 9], 'cur_cost': 1388.0, 'intermediate_solutions': [{'tour': [8, 4, 5, 0, 2, 7, 6, 3, 1, 9], 'cur_cost': 1428.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 4, 5, 0, 7, 2, 6, 1, 3, 9], 'cur_cost': 1416.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 4, 0, 7, 2, 6, 3, 1, 5, 9], 'cur_cost': 1399.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [2, 9, 4, 7, 1, 5, 0, 8, 3, 6], 'cur_cost': 912.0, 'intermediate_solutions': [{'tour': [2, 8, 6, 1, 9, 5, 4, 0, 7, 3], 'cur_cost': 1486.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 7, 1, 9, 5, 3, 6, 0, 4], 'cur_cost': 1394.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 6, 8, 7, 1, 9, 5, 4, 0, 3], 'cur_cost': 1106.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [9, 0, 5, 7, 8, 2, 3, 4, 1, 6], 'cur_cost': 1393.0, 'intermediate_solutions': [{'tour': [8, 2, 0, 3, 6, 5, 7, 4, 1, 9], 'cur_cost': 992.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 3, 0, 2, 6, 5, 7, 9, 1, 4], 'cur_cost': 1079.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 0, 2, 6, 5, 7, 4, 1, 9, 3], 'cur_cost': 856.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [5, 4, 1, 6, 8, 0, 2, 3, 9, 7], 'cur_cost': 1076.0, 'intermediate_solutions': [{'tour': [3, 6, 0, 8, 4, 9, 2, 1, 5, 7], 'cur_cost': 1378.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 0, 6, 3, 4, 5, 2, 1, 9, 7], 'cur_cost': 1426.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 6, 0, 4, 5, 2, 1, 9, 8, 7], 'cur_cost': 1461.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [2, 0, 3, 9, 5, 7, 1, 4, 8, 6], 'cur_cost': 946.0, 'intermediate_solutions': [{'tour': [1, 0, 5, 8, 3, 2, 6, 4, 7, 9], 'cur_cost': 1245.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 0, 5, 8, 3, 9, 1, 4, 6, 2], 'cur_cost': 1227.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 6, 0, 5, 8, 3, 2, 4, 1, 9], 'cur_cost': 1289.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [9, 0, 8, 3, 5, 4, 1, 7, 6, 2], 'cur_cost': 964.0, 'intermediate_solutions': [{'tour': [0, 1, 7, 6, 9, 8, 3, 2, 5, 4], 'cur_cost': 1230.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 7, 6, 9, 8, 3, 2, 1, 5], 'cur_cost': 1262.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 7, 6, 2, 9, 8, 3, 5, 1], 'cur_cost': 1237.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 9, 2, 0, 8, 5, 1, 3, 6, 4], dtype=int64), 'cur_cost': 1351.0, 'intermediate_solutions': [{'tour': array([7, 5, 2, 0, 8, 1, 6, 9, 3, 4]), 'cur_cost': 1362.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 7, 5, 2, 8, 1, 6, 9, 3, 4]), 'cur_cost': 1616.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 0, 7, 5, 2, 1, 6, 9, 3, 4]), 'cur_cost': 1561.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 0, 7, 5, 8, 1, 6, 9, 3, 4]), 'cur_cost': 1617.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 8, 0, 7, 5, 1, 6, 9, 3, 4]), 'cur_cost': 1424.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 09:51:43,178 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:43,178 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:43,179 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=912.000, 多样性=0.873
2025-08-05 09:51:43,179 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 09:51:43,179 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 09:51:43,179 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:43,179 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.011375138467938662, 'best_improvement': -0.058004640371229696}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.01995012468827955}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.005951631614770165, 'recent_improvements': [0.03410827442450418, -0.059878797053610856, 0.04601153765404451], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 13, 'new_count': 13, 'count_change': 0, 'old_best_cost': 832.0, 'new_best_cost': 832.0, 'quality_improvement': 0.0, 'old_diversity': 0.7333333333333333, 'new_diversity': 0.7333333333333333, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:51:43,180 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 09:51:43,184 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple3_10_solution.json
2025-08-05 09:51:43,184 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple3_10_20250805_095143.solution
2025-08-05 09:51:43,184 - __main__ - INFO - 实例执行完成 - 运行时间: 2.01s, 最佳成本: 832.0
2025-08-05 09:51:43,184 - __main__ - INFO - 实例 simple3_10 处理完成
