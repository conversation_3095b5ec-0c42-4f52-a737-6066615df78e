2025-08-05 09:52:21,155 - __main__ - INFO - kroA100 开始进化第 1 代
2025-08-05 09:52:21,155 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 09:52:21,157 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:21,164 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=27015.000, 多样性=0.990
2025-08-05 09:52:21,167 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:21,171 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.990
2025-08-05 09:52:21,173 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:21,175 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 09:52:21,175 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:21,175 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 09:52:21,175 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 09:52:21,204 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -13936.220, 聚类评分: 0.000, 覆盖率: 0.166, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:21,205 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 09:52:21,205 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 09:52:21,205 - visualization.landscape_visualizer - INFO - 设置当前实例名: kroA100
2025-08-05 09:52:21,214 - visualization.landscape_visualizer - INFO - 插值约束: 133 个点被约束到最小值 27015.00
2025-08-05 09:52:21,336 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_kroA100\landscape_kroA100_iter_146_20250805_095221.html
2025-08-05 09:52:21,403 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_kroA100\dashboard_kroA100_iter_146_20250805_095221.html
2025-08-05 09:52:21,403 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 146
2025-08-05 09:52:21,403 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 09:52:21,404 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2275秒
2025-08-05 09:52:21,404 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 292, 'max_size': 500, 'hits': 0, 'misses': 292, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 971, 'misses': 512, 'hit_rate': 0.6547538772757923, 'evictions': 412, 'ttl': 7200}}
2025-08-05 09:52:21,404 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -13936.220000000001, 'local_optima_density': 0.1, 'gradient_variance': 6717979871.003599, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1655, 'fitness_entropy': 0.8173454221465103, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -13936.220)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.166)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358741.2057338, 'performance_metrics': {}}}
2025-08-05 09:52:21,404 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:21,404 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:21,405 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:21,405 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:21,407 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:52:21,408 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 09:52:21,408 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:52:21,408 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:21,408 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:21,408 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:52:21,408 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:21,408 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:21,408 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:21,408 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:21,408 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:21,409 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:21,413 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 09:52:21,414 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:21,414 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30896.0, 路径长度: 100, 收集中间解: 0
2025-08-05 09:52:21,414 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 8, 11, 3, 64, 65, 25, 69, 21, 15, 87, 93, 17, 23, 37, 35, 98, 83, 9, 71, 20, 73, 58, 16, 14, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 78, 52, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 27, 92, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 6, 56, 86, 50, 19, 85, 26, 34, 61, 59, 76, 66, 42], 'cur_cost': 30896.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:21,414 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 30896.00)
2025-08-05 09:52:21,415 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:21,415 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:21,415 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:21,420 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 09:52:21,420 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:21,420 - ExplorationExpert - INFO - 探索路径生成完成，成本: 29358.0, 路径长度: 100, 收集中间解: 0
2025-08-05 09:52:21,420 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 14, 20, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 42, 61, 59, 76, 22, 97, 90, 44, 31, 10, 16, 73, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57], 'cur_cost': 29358.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:21,421 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 29358.00)
2025-08-05 09:52:21,421 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:21,421 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:21,421 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:21,427 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 09:52:21,427 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:21,428 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30580.0, 路径长度: 100, 收集中间解: 0
2025-08-05 09:52:21,428 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 18, 17, 10, 14, 16, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 30580.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:21,428 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 30580.00)
2025-08-05 09:52:21,428 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:52:21,429 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:21,429 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:21,429 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 177001.0
2025-08-05 09:52:21,458 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:21,458 - ExploitationExpert - INFO - res_population_costs: [22427.0, 22356, 22308, 22282, 21776, 21411, 21389]
2025-08-05 09:52:21,458 - ExploitationExpert - INFO - res_populations: [array([ 0, 92, 27, 57, 66, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75, 32, 36,
        4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28, 33, 82,
       54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 72, 68, 80, 24, 60, 50,
       86, 76, 61, 59, 22, 97, 44, 90, 46, 31, 10, 14, 16, 58, 73, 20, 71,
        9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3, 18,
       52, 78, 89, 74, 96, 55, 79, 30, 88, 41,  7, 91, 48,  5, 62],
      dtype=int64), array([ 0, 62,  5, 48, 91,  7, 41, 88, 30, 79, 55, 96, 74, 89, 78, 52, 18,
        3, 64, 25, 65, 69, 21, 15, 87, 93, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 59, 61, 76, 86, 50, 60,
       24, 80, 68, 72, 67, 84,  8,  6, 56, 19, 85, 34, 26, 11, 54, 82, 33,
       28, 45, 42,  2, 13, 70, 40, 99, 47, 29, 38, 95, 77, 51,  4, 36, 32,
       75, 12, 94, 81, 49, 43,  1, 53, 39, 63, 66, 57, 27, 92, 46],
      dtype=int64), array([ 0, 62, 46, 92, 27, 57, 66, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75,
       32, 36,  4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28,
       33, 82, 54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 72, 68, 80, 24,
       60, 50, 86, 76, 61, 59, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20,
       71,  9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3,
       18, 52, 78, 89, 48,  5, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 46, 92, 27, 66, 57, 68, 63, 39, 53,  1, 43, 72, 49, 81, 94, 12,
       75, 32, 36,  4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45,
       28, 33, 82, 54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 80, 24, 60,
       50, 86, 76, 61, 59, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20, 71,
        9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3, 18,
       52, 78, 89, 74, 96, 55, 79, 30, 88, 41,  7, 91, 48,  5, 62],
      dtype=int64), array([ 0, 91, 74, 96, 55, 79, 30, 88, 41,  7, 66, 27, 92, 57, 60, 24, 80,
       84, 67, 72, 68, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75, 32, 36,  4,
       51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28, 33, 82, 54,
       11, 26, 34, 85, 19, 56,  6,  8, 86, 50, 76, 61, 59, 22, 97, 90, 44,
       31, 10, 14, 16, 58, 73, 20, 71,  9, 83, 35, 98, 37, 23, 17, 78, 52,
       87, 15, 93, 21, 69, 65, 25, 64,  3, 18, 89, 48,  5, 62, 46],
      dtype=int64), array([ 0, 62,  5, 48, 89, 18, 74, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 59, 61, 76, 50, 86,  8,
        6, 56, 19, 85, 34, 26, 11, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40,
       99, 47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81, 49, 43,  1,
       53, 39, 63, 68, 72, 67, 84, 80, 24, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 62, 89, 48,  5, 91,  7, 41, 88, 30, 79, 55, 96, 74, 18,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26,
       11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99, 47,
       51, 77, 95,  4, 36, 32, 75, 12, 94, 81, 38, 29, 84, 67, 72, 49, 43,
        1, 53, 39, 63, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92, 46],
      dtype=int64)]
2025-08-05 09:52:21,462 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:21,463 - ExploitationExpert - INFO - populations: [{'tour': [0, 8, 11, 3, 64, 65, 25, 69, 21, 15, 87, 93, 17, 23, 37, 35, 98, 83, 9, 71, 20, 73, 58, 16, 14, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 78, 52, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 27, 92, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 6, 56, 86, 50, 19, 85, 26, 34, 61, 59, 76, 66, 42], 'cur_cost': 30896.0}, {'tour': [0, 14, 20, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 42, 61, 59, 76, 22, 97, 90, 44, 31, 10, 16, 73, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57], 'cur_cost': 29358.0}, {'tour': [0, 18, 17, 10, 14, 16, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 30580.0}, {'tour': array([ 4, 63,  8, 10, 27, 76, 42, 17, 94, 34,  7, 81, 15, 53, 73, 20, 83,
       96, 95, 12, 93,  2, 52, 61, 48, 35, 77,  9, 66, 11, 51, 70, 60, 90,
       57, 32, 45, 89, 21, 44, 58, 86, 50, 38, 79, 37,  1, 56, 14, 88, 28,
       71, 19, 43, 39, 30, 47, 78, 92, 54, 99, 69, 36, 46, 23,  3, 59, 31,
       13, 64, 74, 67,  0, 97, 75, 84, 29, 87, 25, 49, 82, 85, 26, 80, 91,
        6, 98,  5, 22, 18, 72, 65, 24, 68, 55, 41, 62, 40, 33, 16],
      dtype=int64), 'cur_cost': 177001.0}, {'tour': array([73, 88, 94, 70, 90, 89, 44, 41, 85, 15,  2, 99, 56, 36, 62, 13, 25,
       45,  8, 92, 10, 35, 61, 60,  9, 33, 50, 12,  4, 76, 52, 65, 81, 69,
       91, 48, 31, 40, 26, 53,  5, 27, 30, 75, 19, 17, 71,  7, 57, 42, 58,
       38, 18, 97, 59, 64, 96, 66, 49, 77, 72, 39, 29,  0, 34, 23, 21,  3,
       79, 93, 84,  1, 78, 14, 87, 16, 54, 63, 28, 55, 82, 43, 46, 11, 67,
       83, 86, 68, 74, 22,  6, 95, 51, 32, 24, 80, 20, 37, 47, 98],
      dtype=int64), 'cur_cost': 160908.0}, {'tour': array([40, 83,  1, 78, 99, 98, 52, 93, 35, 33,  4, 51, 80, 70, 37, 73, 38,
       77, 24, 16, 13, 49, 27, 85, 29, 44, 59, 95, 46, 84, 75, 72, 74, 48,
        5, 67, 68, 15, 36, 65, 21, 32, 96, 47, 60, 14, 20, 97, 34, 86, 56,
        2, 82, 10, 55, 54, 63, 90, 94, 43, 50, 45, 41, 76, 39,  6, 57, 17,
       88, 92, 11,  3, 18, 61, 79, 89, 22, 12, 64, 87,  8,  0, 26,  9, 31,
        7, 81, 58, 62, 53, 23, 71, 91, 42, 28, 30, 19, 25, 66, 69],
      dtype=int64), 'cur_cost': 169559.0}, {'tour': array([20, 18, 35, 91, 16, 17, 61, 90, 28, 34,  3,  4, 82, 65, 36, 98, 63,
       96, 38, 30, 44, 58, 67, 23, 84,  8, 46, 27, 37, 10, 49, 77,  9, 74,
       99, 43, 33,  7, 78, 57,  0, 89, 85, 56, 47, 80, 92, 93, 50, 15, 42,
       59, 48, 60, 72, 54, 45, 73, 66, 39, 40,  5, 87, 14, 13, 32, 24, 86,
       41, 12, 69, 52, 68, 29, 25, 94, 53, 55, 76, 21, 75, 95, 51, 83, 22,
       70, 26, 97, 11,  6, 88, 62, 31, 81,  1, 64, 19, 79, 71,  2],
      dtype=int64), 'cur_cost': 174868.0}, {'tour': array([46, 73, 86,  5, 40, 17, 76, 77, 43, 95, 47, 80,  1, 96, 53, 14, 39,
       48, 32, 13, 21, 29, 38, 42, 54, 83, 52, 12, 61, 98, 55, 59, 33, 62,
       15, 24, 78, 64, 22, 74, 30, 87, 90, 45, 81, 69, 58,  3, 18, 92, 25,
        6, 91, 79, 60, 68, 44, 97, 34, 93, 23, 70, 66, 89, 88, 11, 67, 19,
       56,  8, 72, 16, 63, 36, 31, 82, 57, 26,  4, 37, 27, 10, 65, 84, 20,
        2, 71, 50,  7, 49, 28, 75, 51, 35, 41,  9, 94, 85,  0, 99],
      dtype=int64), 'cur_cost': 172048.0}, {'tour': array([60,  9, 87, 33, 74, 48, 68, 22, 57, 13, 56, 29,  2, 77, 76, 53, 52,
       43, 64, 12,  7, 83, 99, 98, 79,  0, 23, 28, 19, 80, 78, 51, 90, 41,
       40, 73, 86, 16, 59, 11, 72, 89, 21, 62, 47, 24, 84, 55, 34, 92, 15,
       61, 20, 36, 10, 95, 65, 71,  1, 30, 27, 26, 42, 54, 63, 17,  4, 58,
       69, 97, 32, 96, 14, 67, 75, 35, 37,  3, 46, 49, 85, 93, 44,  8,  5,
       94, 82,  6, 38, 39, 45, 91, 81, 88, 18, 31, 50, 25, 66, 70],
      dtype=int64), 'cur_cost': 182619.0}, {'tour': array([28, 34, 32, 92, 66, 26, 21, 83, 14, 36,  9, 61, 30, 13, 97, 75, 98,
       71, 45, 62, 35, 82, 54, 39, 60, 23, 78, 24, 11, 56, 96, 90, 49,  3,
       76, 68, 27, 87,  7, 38, 79, 55, 42, 69, 16, 40, 22, 25,  0,  2, 33,
       58, 31, 91, 70,  1, 80, 86, 46, 81, 59, 15, 12, 99, 63, 84, 94, 37,
       93, 88, 18, 77, 41, 19, 85, 43,  6, 48, 65, 72, 20, 57, 52, 10, 95,
       51,  5, 47, 64, 74, 89,  4, 73, 29, 44,  8, 50, 67, 17, 53],
      dtype=int64), 'cur_cost': 178962.0}]
2025-08-05 09:52:21,467 - ExploitationExpert - INFO - 局部搜索耗时: 0.04秒，最大迭代次数: 10
2025-08-05 09:52:21,467 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 378, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 378, 'cache_hits': 0, 'similarity_calculations': 1980, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:21,468 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 4, 63,  8, 10, 27, 76, 42, 17, 94, 34,  7, 81, 15, 53, 73, 20, 83,
       96, 95, 12, 93,  2, 52, 61, 48, 35, 77,  9, 66, 11, 51, 70, 60, 90,
       57, 32, 45, 89, 21, 44, 58, 86, 50, 38, 79, 37,  1, 56, 14, 88, 28,
       71, 19, 43, 39, 30, 47, 78, 92, 54, 99, 69, 36, 46, 23,  3, 59, 31,
       13, 64, 74, 67,  0, 97, 75, 84, 29, 87, 25, 49, 82, 85, 26, 80, 91,
        6, 98,  5, 22, 18, 72, 65, 24, 68, 55, 41, 62, 40, 33, 16],
      dtype=int64), 'cur_cost': 177001.0, 'intermediate_solutions': [{'tour': array([59, 57, 14, 20, 33, 44, 84, 31,  5, 88, 64, 22, 67, 15, 17, 68, 40,
       21,  3, 34, 85, 98, 93, 11, 81, 76, 49,  6,  8, 29, 92, 39, 61, 62,
       66, 16,  1, 86, 77, 10, 72, 50,  9,  2, 36, 80, 71,  0, 60, 79, 54,
       41, 47, 97, 89, 45, 82, 30, 58, 13, 96, 42, 73, 12, 26, 83, 27, 18,
       90, 65, 63, 23, 52, 78, 37, 70, 55, 91,  7, 46, 43, 99, 53, 56, 28,
       35,  4, 69, 75, 87, 19, 32, 48, 95, 25, 51, 38, 94, 74, 24],
      dtype=int64), 'cur_cost': 182615.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([20, 59, 57, 14, 33, 44, 84, 31,  5, 88, 64, 22, 67, 15, 17, 68, 40,
       21,  3, 34, 85, 98, 93, 11, 81, 76, 49,  6,  8, 29, 92, 39, 61, 62,
       66, 16,  1, 86, 77, 10, 72, 50,  9,  2, 36, 80, 71,  0, 60, 79, 54,
       41, 47, 97, 89, 45, 82, 30, 58, 13, 96, 42, 73, 12, 26, 83, 27, 18,
       90, 65, 63, 23, 52, 78, 37, 70, 55, 91,  7, 46, 43, 99, 53, 56, 28,
       35,  4, 69, 75, 87, 19, 32, 48, 95, 25, 51, 38, 94, 74, 24],
      dtype=int64), 'cur_cost': 183996.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([33, 20, 59, 57, 14, 44, 84, 31,  5, 88, 64, 22, 67, 15, 17, 68, 40,
       21,  3, 34, 85, 98, 93, 11, 81, 76, 49,  6,  8, 29, 92, 39, 61, 62,
       66, 16,  1, 86, 77, 10, 72, 50,  9,  2, 36, 80, 71,  0, 60, 79, 54,
       41, 47, 97, 89, 45, 82, 30, 58, 13, 96, 42, 73, 12, 26, 83, 27, 18,
       90, 65, 63, 23, 52, 78, 37, 70, 55, 91,  7, 46, 43, 99, 53, 56, 28,
       35,  4, 69, 75, 87, 19, 32, 48, 95, 25, 51, 38, 94, 74, 24],
      dtype=int64), 'cur_cost': 182135.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([14, 20, 59, 57, 33, 44, 84, 31,  5, 88, 64, 22, 67, 15, 17, 68, 40,
       21,  3, 34, 85, 98, 93, 11, 81, 76, 49,  6,  8, 29, 92, 39, 61, 62,
       66, 16,  1, 86, 77, 10, 72, 50,  9,  2, 36, 80, 71,  0, 60, 79, 54,
       41, 47, 97, 89, 45, 82, 30, 58, 13, 96, 42, 73, 12, 26, 83, 27, 18,
       90, 65, 63, 23, 52, 78, 37, 70, 55, 91,  7, 46, 43, 99, 53, 56, 28,
       35,  4, 69, 75, 87, 19, 32, 48, 95, 25, 51, 38, 94, 74, 24],
      dtype=int64), 'cur_cost': 182208.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([14, 33, 20, 59, 57, 44, 84, 31,  5, 88, 64, 22, 67, 15, 17, 68, 40,
       21,  3, 34, 85, 98, 93, 11, 81, 76, 49,  6,  8, 29, 92, 39, 61, 62,
       66, 16,  1, 86, 77, 10, 72, 50,  9,  2, 36, 80, 71,  0, 60, 79, 54,
       41, 47, 97, 89, 45, 82, 30, 58, 13, 96, 42, 73, 12, 26, 83, 27, 18,
       90, 65, 63, 23, 52, 78, 37, 70, 55, 91,  7, 46, 43, 99, 53, 56, 28,
       35,  4, 69, 75, 87, 19, 32, 48, 95, 25, 51, 38, 94, 74, 24],
      dtype=int64), 'cur_cost': 184204.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:21,468 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 177001.00)
2025-08-05 09:52:21,468 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:21,469 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:21,469 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:21,486 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 100
2025-08-05 09:52:21,486 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:21,486 - ExplorationExpert - INFO - 探索路径生成完成，成本: 106102.0, 路径长度: 100, 收集中间解: 0
2025-08-05 09:52:21,486 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [65, 79, 30, 62, 14, 76, 28, 43, 19, 44, 60, 49, 53, 39, 6, 95, 61, 86, 47, 36, 34, 13, 66, 59, 90, 20, 5, 52, 31, 98, 7, 17, 10, 87, 57, 88, 24, 68, 92, 96, 22, 83, 16, 35, 93, 97, 89, 3, 78, 21, 64, 71, 55, 18, 15, 0, 11, 8, 33, 40, 26, 67, 56, 80, 1, 4, 12, 2, 51, 99, 38, 75, 81, 72, 45, 63, 84, 70, 27, 58, 41, 25, 23, 91, 73, 37, 74, 50, 54, 77, 82, 46, 85, 42, 32, 29, 94, 48, 9, 69], 'cur_cost': 106102.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:21,487 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 106102.00)
2025-08-05 09:52:21,487 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:21,487 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:21,487 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:21,503 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 100
2025-08-05 09:52:21,503 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:21,504 - ExplorationExpert - INFO - 探索路径生成完成，成本: 102727.0, 路径长度: 100, 收集中间解: 0
2025-08-05 09:52:21,504 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [87, 91, 62, 27, 72, 54, 38, 94, 80, 49, 8, 82, 76, 34, 29, 26, 28, 4, 36, 84, 85, 56, 66, 90, 92, 46, 17, 79, 41, 68, 77, 40, 1, 45, 24, 70, 0, 83, 44, 11, 20, 59, 42, 51, 67, 32, 53, 75, 19, 71, 31, 23, 9, 52, 93, 55, 30, 7, 39, 60, 2, 97, 73, 22, 74, 25, 21, 65, 5, 96, 64, 48, 69, 89, 98, 78, 3, 14, 58, 16, 61, 63, 43, 81, 33, 86, 50, 95, 6, 47, 13, 12, 10, 35, 37, 18, 15, 88, 57, 99], 'cur_cost': 102727.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:21,504 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 102727.00)
2025-08-05 09:52:21,504 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:21,504 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:21,504 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:21,520 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 100
2025-08-05 09:52:21,521 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:21,521 - ExplorationExpert - INFO - 探索路径生成完成，成本: 106782.0, 路径长度: 100, 收集中间解: 0
2025-08-05 09:52:21,521 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [40, 43, 26, 95, 82, 29, 51, 60, 97, 19, 70, 6, 34, 84, 45, 75, 88, 57, 66, 18, 21, 15, 5, 69, 87, 17, 58, 52, 92, 50, 39, 68, 81, 77, 85, 59, 33, 49, 11, 27, 86, 38, 53, 30, 1, 36, 12, 63, 90, 71, 20, 91, 3, 41, 55, 62, 22, 10, 16, 98, 35, 9, 78, 14, 46, 79, 74, 76, 67, 2, 80, 24, 7, 44, 0, 73, 31, 96, 61, 48, 37, 56, 47, 99, 42, 72, 94, 28, 13, 4, 89, 65, 93, 83, 23, 64, 8, 54, 32, 25], 'cur_cost': 106782.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:21,521 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 106782.00)
2025-08-05 09:52:21,522 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:21,522 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:21,522 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:21,538 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 100
2025-08-05 09:52:21,539 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:21,539 - ExplorationExpert - INFO - 探索路径生成完成，成本: 104479.0, 路径长度: 100, 收集中间解: 0
2025-08-05 09:52:21,539 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [58, 71, 44, 59, 11, 95, 40, 67, 28, 72, 1, 86, 43, 2, 45, 24, 97, 57, 7, 5, 88, 50, 46, 48, 64, 31, 17, 37, 18, 65, 55, 23, 73, 41, 9, 90, 82, 54, 36, 68, 34, 92, 63, 80, 38, 76, 62, 0, 20, 91, 10, 52, 16, 87, 21, 30, 96, 89, 3, 93, 74, 15, 83, 6, 29, 81, 99, 42, 77, 47, 33, 22, 8, 19, 39, 84, 53, 79, 49, 13, 51, 94, 26, 56, 61, 27, 85, 66, 60, 70, 12, 75, 4, 14, 35, 78, 69, 25, 98, 32], 'cur_cost': 104479.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:21,540 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 104479.00)
2025-08-05 09:52:21,540 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:52:21,540 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:21,540 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:21,541 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 170125.0
2025-08-05 09:52:21,564 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:21,564 - ExploitationExpert - INFO - res_population_costs: [22427.0, 22356, 22308, 22282, 21776, 21411, 21389]
2025-08-05 09:52:21,564 - ExploitationExpert - INFO - res_populations: [array([ 0, 92, 27, 57, 66, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75, 32, 36,
        4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28, 33, 82,
       54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 72, 68, 80, 24, 60, 50,
       86, 76, 61, 59, 22, 97, 44, 90, 46, 31, 10, 14, 16, 58, 73, 20, 71,
        9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3, 18,
       52, 78, 89, 74, 96, 55, 79, 30, 88, 41,  7, 91, 48,  5, 62],
      dtype=int64), array([ 0, 62,  5, 48, 91,  7, 41, 88, 30, 79, 55, 96, 74, 89, 78, 52, 18,
        3, 64, 25, 65, 69, 21, 15, 87, 93, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 59, 61, 76, 86, 50, 60,
       24, 80, 68, 72, 67, 84,  8,  6, 56, 19, 85, 34, 26, 11, 54, 82, 33,
       28, 45, 42,  2, 13, 70, 40, 99, 47, 29, 38, 95, 77, 51,  4, 36, 32,
       75, 12, 94, 81, 49, 43,  1, 53, 39, 63, 66, 57, 27, 92, 46],
      dtype=int64), array([ 0, 62, 46, 92, 27, 57, 66, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75,
       32, 36,  4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28,
       33, 82, 54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 72, 68, 80, 24,
       60, 50, 86, 76, 61, 59, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20,
       71,  9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3,
       18, 52, 78, 89, 48,  5, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 46, 92, 27, 66, 57, 68, 63, 39, 53,  1, 43, 72, 49, 81, 94, 12,
       75, 32, 36,  4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45,
       28, 33, 82, 54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 80, 24, 60,
       50, 86, 76, 61, 59, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20, 71,
        9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3, 18,
       52, 78, 89, 74, 96, 55, 79, 30, 88, 41,  7, 91, 48,  5, 62],
      dtype=int64), array([ 0, 91, 74, 96, 55, 79, 30, 88, 41,  7, 66, 27, 92, 57, 60, 24, 80,
       84, 67, 72, 68, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75, 32, 36,  4,
       51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28, 33, 82, 54,
       11, 26, 34, 85, 19, 56,  6,  8, 86, 50, 76, 61, 59, 22, 97, 90, 44,
       31, 10, 14, 16, 58, 73, 20, 71,  9, 83, 35, 98, 37, 23, 17, 78, 52,
       87, 15, 93, 21, 69, 65, 25, 64,  3, 18, 89, 48,  5, 62, 46],
      dtype=int64), array([ 0, 62,  5, 48, 89, 18, 74, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 59, 61, 76, 50, 86,  8,
        6, 56, 19, 85, 34, 26, 11, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40,
       99, 47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81, 49, 43,  1,
       53, 39, 63, 68, 72, 67, 84, 80, 24, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 62, 89, 48,  5, 91,  7, 41, 88, 30, 79, 55, 96, 74, 18,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26,
       11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99, 47,
       51, 77, 95,  4, 36, 32, 75, 12, 94, 81, 38, 29, 84, 67, 72, 49, 43,
        1, 53, 39, 63, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92, 46],
      dtype=int64)]
2025-08-05 09:52:21,568 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:21,568 - ExploitationExpert - INFO - populations: [{'tour': [0, 8, 11, 3, 64, 65, 25, 69, 21, 15, 87, 93, 17, 23, 37, 35, 98, 83, 9, 71, 20, 73, 58, 16, 14, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 78, 52, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 27, 92, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 6, 56, 86, 50, 19, 85, 26, 34, 61, 59, 76, 66, 42], 'cur_cost': 30896.0}, {'tour': [0, 14, 20, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 42, 61, 59, 76, 22, 97, 90, 44, 31, 10, 16, 73, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57], 'cur_cost': 29358.0}, {'tour': [0, 18, 17, 10, 14, 16, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 30580.0}, {'tour': array([ 4, 63,  8, 10, 27, 76, 42, 17, 94, 34,  7, 81, 15, 53, 73, 20, 83,
       96, 95, 12, 93,  2, 52, 61, 48, 35, 77,  9, 66, 11, 51, 70, 60, 90,
       57, 32, 45, 89, 21, 44, 58, 86, 50, 38, 79, 37,  1, 56, 14, 88, 28,
       71, 19, 43, 39, 30, 47, 78, 92, 54, 99, 69, 36, 46, 23,  3, 59, 31,
       13, 64, 74, 67,  0, 97, 75, 84, 29, 87, 25, 49, 82, 85, 26, 80, 91,
        6, 98,  5, 22, 18, 72, 65, 24, 68, 55, 41, 62, 40, 33, 16],
      dtype=int64), 'cur_cost': 177001.0}, {'tour': [65, 79, 30, 62, 14, 76, 28, 43, 19, 44, 60, 49, 53, 39, 6, 95, 61, 86, 47, 36, 34, 13, 66, 59, 90, 20, 5, 52, 31, 98, 7, 17, 10, 87, 57, 88, 24, 68, 92, 96, 22, 83, 16, 35, 93, 97, 89, 3, 78, 21, 64, 71, 55, 18, 15, 0, 11, 8, 33, 40, 26, 67, 56, 80, 1, 4, 12, 2, 51, 99, 38, 75, 81, 72, 45, 63, 84, 70, 27, 58, 41, 25, 23, 91, 73, 37, 74, 50, 54, 77, 82, 46, 85, 42, 32, 29, 94, 48, 9, 69], 'cur_cost': 106102.0}, {'tour': [87, 91, 62, 27, 72, 54, 38, 94, 80, 49, 8, 82, 76, 34, 29, 26, 28, 4, 36, 84, 85, 56, 66, 90, 92, 46, 17, 79, 41, 68, 77, 40, 1, 45, 24, 70, 0, 83, 44, 11, 20, 59, 42, 51, 67, 32, 53, 75, 19, 71, 31, 23, 9, 52, 93, 55, 30, 7, 39, 60, 2, 97, 73, 22, 74, 25, 21, 65, 5, 96, 64, 48, 69, 89, 98, 78, 3, 14, 58, 16, 61, 63, 43, 81, 33, 86, 50, 95, 6, 47, 13, 12, 10, 35, 37, 18, 15, 88, 57, 99], 'cur_cost': 102727.0}, {'tour': [40, 43, 26, 95, 82, 29, 51, 60, 97, 19, 70, 6, 34, 84, 45, 75, 88, 57, 66, 18, 21, 15, 5, 69, 87, 17, 58, 52, 92, 50, 39, 68, 81, 77, 85, 59, 33, 49, 11, 27, 86, 38, 53, 30, 1, 36, 12, 63, 90, 71, 20, 91, 3, 41, 55, 62, 22, 10, 16, 98, 35, 9, 78, 14, 46, 79, 74, 76, 67, 2, 80, 24, 7, 44, 0, 73, 31, 96, 61, 48, 37, 56, 47, 99, 42, 72, 94, 28, 13, 4, 89, 65, 93, 83, 23, 64, 8, 54, 32, 25], 'cur_cost': 106782.0}, {'tour': [58, 71, 44, 59, 11, 95, 40, 67, 28, 72, 1, 86, 43, 2, 45, 24, 97, 57, 7, 5, 88, 50, 46, 48, 64, 31, 17, 37, 18, 65, 55, 23, 73, 41, 9, 90, 82, 54, 36, 68, 34, 92, 63, 80, 38, 76, 62, 0, 20, 91, 10, 52, 16, 87, 21, 30, 96, 89, 3, 93, 74, 15, 83, 6, 29, 81, 99, 42, 77, 47, 33, 22, 8, 19, 39, 84, 53, 79, 49, 13, 51, 94, 26, 56, 61, 27, 85, 66, 60, 70, 12, 75, 4, 14, 35, 78, 69, 25, 98, 32], 'cur_cost': 104479.0}, {'tour': array([20,  9,  5, 34, 31, 89, 84, 32, 33, 79, 63, 65, 43, 85, 45, 92, 62,
       55, 73, 68, 71,  8, 10, 30,  3, 76, 39, 86, 52, 22, 12, 38,  6, 18,
       66,  2,  0, 29, 36, 83, 75, 53, 93, 47, 60, 15, 82, 81, 11, 44, 46,
       26, 48, 27, 95, 98, 97, 72, 56, 50, 94, 25, 42, 67, 70, 35, 96, 61,
       49, 80, 57, 28, 90, 14, 19, 59, 88, 21, 41,  7, 91, 78, 13,  4, 51,
       99, 74, 23, 40, 37, 69, 17, 54, 24, 64, 58,  1, 16, 77, 87],
      dtype=int64), 'cur_cost': 170125.0}, {'tour': array([28, 34, 32, 92, 66, 26, 21, 83, 14, 36,  9, 61, 30, 13, 97, 75, 98,
       71, 45, 62, 35, 82, 54, 39, 60, 23, 78, 24, 11, 56, 96, 90, 49,  3,
       76, 68, 27, 87,  7, 38, 79, 55, 42, 69, 16, 40, 22, 25,  0,  2, 33,
       58, 31, 91, 70,  1, 80, 86, 46, 81, 59, 15, 12, 99, 63, 84, 94, 37,
       93, 88, 18, 77, 41, 19, 85, 43,  6, 48, 65, 72, 20, 57, 52, 10, 95,
       51,  5, 47, 64, 74, 89,  4, 73, 29, 44,  8, 50, 67, 17, 53],
      dtype=int64), 'cur_cost': 178962.0}]
2025-08-05 09:52:21,570 - ExploitationExpert - INFO - 局部搜索耗时: 0.03秒，最大迭代次数: 10
2025-08-05 09:52:21,570 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 379, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 379, 'cache_hits': 0, 'similarity_calculations': 1981, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:21,572 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([20,  9,  5, 34, 31, 89, 84, 32, 33, 79, 63, 65, 43, 85, 45, 92, 62,
       55, 73, 68, 71,  8, 10, 30,  3, 76, 39, 86, 52, 22, 12, 38,  6, 18,
       66,  2,  0, 29, 36, 83, 75, 53, 93, 47, 60, 15, 82, 81, 11, 44, 46,
       26, 48, 27, 95, 98, 97, 72, 56, 50, 94, 25, 42, 67, 70, 35, 96, 61,
       49, 80, 57, 28, 90, 14, 19, 59, 88, 21, 41,  7, 91, 78, 13,  4, 51,
       99, 74, 23, 40, 37, 69, 17, 54, 24, 64, 58,  1, 16, 77, 87],
      dtype=int64), 'cur_cost': 170125.0, 'intermediate_solutions': [{'tour': array([87,  9, 60, 33, 74, 48, 68, 22, 57, 13, 56, 29,  2, 77, 76, 53, 52,
       43, 64, 12,  7, 83, 99, 98, 79,  0, 23, 28, 19, 80, 78, 51, 90, 41,
       40, 73, 86, 16, 59, 11, 72, 89, 21, 62, 47, 24, 84, 55, 34, 92, 15,
       61, 20, 36, 10, 95, 65, 71,  1, 30, 27, 26, 42, 54, 63, 17,  4, 58,
       69, 97, 32, 96, 14, 67, 75, 35, 37,  3, 46, 49, 85, 93, 44,  8,  5,
       94, 82,  6, 38, 39, 45, 91, 81, 88, 18, 31, 50, 25, 66, 70],
      dtype=int64), 'cur_cost': 182670.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([33, 87,  9, 60, 74, 48, 68, 22, 57, 13, 56, 29,  2, 77, 76, 53, 52,
       43, 64, 12,  7, 83, 99, 98, 79,  0, 23, 28, 19, 80, 78, 51, 90, 41,
       40, 73, 86, 16, 59, 11, 72, 89, 21, 62, 47, 24, 84, 55, 34, 92, 15,
       61, 20, 36, 10, 95, 65, 71,  1, 30, 27, 26, 42, 54, 63, 17,  4, 58,
       69, 97, 32, 96, 14, 67, 75, 35, 37,  3, 46, 49, 85, 93, 44,  8,  5,
       94, 82,  6, 38, 39, 45, 91, 81, 88, 18, 31, 50, 25, 66, 70],
      dtype=int64), 'cur_cost': 181032.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([74, 33, 87,  9, 60, 48, 68, 22, 57, 13, 56, 29,  2, 77, 76, 53, 52,
       43, 64, 12,  7, 83, 99, 98, 79,  0, 23, 28, 19, 80, 78, 51, 90, 41,
       40, 73, 86, 16, 59, 11, 72, 89, 21, 62, 47, 24, 84, 55, 34, 92, 15,
       61, 20, 36, 10, 95, 65, 71,  1, 30, 27, 26, 42, 54, 63, 17,  4, 58,
       69, 97, 32, 96, 14, 67, 75, 35, 37,  3, 46, 49, 85, 93, 44,  8,  5,
       94, 82,  6, 38, 39, 45, 91, 81, 88, 18, 31, 50, 25, 66, 70],
      dtype=int64), 'cur_cost': 185369.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([60, 33, 87,  9, 74, 48, 68, 22, 57, 13, 56, 29,  2, 77, 76, 53, 52,
       43, 64, 12,  7, 83, 99, 98, 79,  0, 23, 28, 19, 80, 78, 51, 90, 41,
       40, 73, 86, 16, 59, 11, 72, 89, 21, 62, 47, 24, 84, 55, 34, 92, 15,
       61, 20, 36, 10, 95, 65, 71,  1, 30, 27, 26, 42, 54, 63, 17,  4, 58,
       69, 97, 32, 96, 14, 67, 75, 35, 37,  3, 46, 49, 85, 93, 44,  8,  5,
       94, 82,  6, 38, 39, 45, 91, 81, 88, 18, 31, 50, 25, 66, 70],
      dtype=int64), 'cur_cost': 180039.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([60, 74, 33, 87,  9, 48, 68, 22, 57, 13, 56, 29,  2, 77, 76, 53, 52,
       43, 64, 12,  7, 83, 99, 98, 79,  0, 23, 28, 19, 80, 78, 51, 90, 41,
       40, 73, 86, 16, 59, 11, 72, 89, 21, 62, 47, 24, 84, 55, 34, 92, 15,
       61, 20, 36, 10, 95, 65, 71,  1, 30, 27, 26, 42, 54, 63, 17,  4, 58,
       69, 97, 32, 96, 14, 67, 75, 35, 37,  3, 46, 49, 85, 93, 44,  8,  5,
       94, 82,  6, 38, 39, 45, 91, 81, 88, 18, 31, 50, 25, 66, 70],
      dtype=int64), 'cur_cost': 182607.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:21,572 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 170125.00)
2025-08-05 09:52:21,572 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:21,572 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:21,572 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:21,576 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 09:52:21,576 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:21,577 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32630.0, 路径长度: 100, 收集中间解: 0
2025-08-05 09:52:21,577 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 14, 4, 3, 64, 65, 25, 69, 21, 15, 87, 93, 17, 23, 37, 35, 98, 83, 9, 71, 20, 73, 58, 16, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 78, 52, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 27, 92, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42, 66], 'cur_cost': 32630.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:21,578 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 32630.00)
2025-08-05 09:52:21,578 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:21,578 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:21,581 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 11, 3, 64, 65, 25, 69, 21, 15, 87, 93, 17, 23, 37, 35, 98, 83, 9, 71, 20, 73, 58, 16, 14, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 78, 52, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 27, 92, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 6, 56, 86, 50, 19, 85, 26, 34, 61, 59, 76, 66, 42], 'cur_cost': 30896.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 20, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 42, 61, 59, 76, 22, 97, 90, 44, 31, 10, 16, 73, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57], 'cur_cost': 29358.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 17, 10, 14, 16, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 30580.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 4, 63,  8, 10, 27, 76, 42, 17, 94, 34,  7, 81, 15, 53, 73, 20, 83,
       96, 95, 12, 93,  2, 52, 61, 48, 35, 77,  9, 66, 11, 51, 70, 60, 90,
       57, 32, 45, 89, 21, 44, 58, 86, 50, 38, 79, 37,  1, 56, 14, 88, 28,
       71, 19, 43, 39, 30, 47, 78, 92, 54, 99, 69, 36, 46, 23,  3, 59, 31,
       13, 64, 74, 67,  0, 97, 75, 84, 29, 87, 25, 49, 82, 85, 26, 80, 91,
        6, 98,  5, 22, 18, 72, 65, 24, 68, 55, 41, 62, 40, 33, 16],
      dtype=int64), 'cur_cost': 177001.0, 'intermediate_solutions': [{'tour': array([59, 57, 14, 20, 33, 44, 84, 31,  5, 88, 64, 22, 67, 15, 17, 68, 40,
       21,  3, 34, 85, 98, 93, 11, 81, 76, 49,  6,  8, 29, 92, 39, 61, 62,
       66, 16,  1, 86, 77, 10, 72, 50,  9,  2, 36, 80, 71,  0, 60, 79, 54,
       41, 47, 97, 89, 45, 82, 30, 58, 13, 96, 42, 73, 12, 26, 83, 27, 18,
       90, 65, 63, 23, 52, 78, 37, 70, 55, 91,  7, 46, 43, 99, 53, 56, 28,
       35,  4, 69, 75, 87, 19, 32, 48, 95, 25, 51, 38, 94, 74, 24],
      dtype=int64), 'cur_cost': 182615.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([20, 59, 57, 14, 33, 44, 84, 31,  5, 88, 64, 22, 67, 15, 17, 68, 40,
       21,  3, 34, 85, 98, 93, 11, 81, 76, 49,  6,  8, 29, 92, 39, 61, 62,
       66, 16,  1, 86, 77, 10, 72, 50,  9,  2, 36, 80, 71,  0, 60, 79, 54,
       41, 47, 97, 89, 45, 82, 30, 58, 13, 96, 42, 73, 12, 26, 83, 27, 18,
       90, 65, 63, 23, 52, 78, 37, 70, 55, 91,  7, 46, 43, 99, 53, 56, 28,
       35,  4, 69, 75, 87, 19, 32, 48, 95, 25, 51, 38, 94, 74, 24],
      dtype=int64), 'cur_cost': 183996.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([33, 20, 59, 57, 14, 44, 84, 31,  5, 88, 64, 22, 67, 15, 17, 68, 40,
       21,  3, 34, 85, 98, 93, 11, 81, 76, 49,  6,  8, 29, 92, 39, 61, 62,
       66, 16,  1, 86, 77, 10, 72, 50,  9,  2, 36, 80, 71,  0, 60, 79, 54,
       41, 47, 97, 89, 45, 82, 30, 58, 13, 96, 42, 73, 12, 26, 83, 27, 18,
       90, 65, 63, 23, 52, 78, 37, 70, 55, 91,  7, 46, 43, 99, 53, 56, 28,
       35,  4, 69, 75, 87, 19, 32, 48, 95, 25, 51, 38, 94, 74, 24],
      dtype=int64), 'cur_cost': 182135.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([14, 20, 59, 57, 33, 44, 84, 31,  5, 88, 64, 22, 67, 15, 17, 68, 40,
       21,  3, 34, 85, 98, 93, 11, 81, 76, 49,  6,  8, 29, 92, 39, 61, 62,
       66, 16,  1, 86, 77, 10, 72, 50,  9,  2, 36, 80, 71,  0, 60, 79, 54,
       41, 47, 97, 89, 45, 82, 30, 58, 13, 96, 42, 73, 12, 26, 83, 27, 18,
       90, 65, 63, 23, 52, 78, 37, 70, 55, 91,  7, 46, 43, 99, 53, 56, 28,
       35,  4, 69, 75, 87, 19, 32, 48, 95, 25, 51, 38, 94, 74, 24],
      dtype=int64), 'cur_cost': 182208.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([14, 33, 20, 59, 57, 44, 84, 31,  5, 88, 64, 22, 67, 15, 17, 68, 40,
       21,  3, 34, 85, 98, 93, 11, 81, 76, 49,  6,  8, 29, 92, 39, 61, 62,
       66, 16,  1, 86, 77, 10, 72, 50,  9,  2, 36, 80, 71,  0, 60, 79, 54,
       41, 47, 97, 89, 45, 82, 30, 58, 13, 96, 42, 73, 12, 26, 83, 27, 18,
       90, 65, 63, 23, 52, 78, 37, 70, 55, 91,  7, 46, 43, 99, 53, 56, 28,
       35,  4, 69, 75, 87, 19, 32, 48, 95, 25, 51, 38, 94, 74, 24],
      dtype=int64), 'cur_cost': 184204.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [65, 79, 30, 62, 14, 76, 28, 43, 19, 44, 60, 49, 53, 39, 6, 95, 61, 86, 47, 36, 34, 13, 66, 59, 90, 20, 5, 52, 31, 98, 7, 17, 10, 87, 57, 88, 24, 68, 92, 96, 22, 83, 16, 35, 93, 97, 89, 3, 78, 21, 64, 71, 55, 18, 15, 0, 11, 8, 33, 40, 26, 67, 56, 80, 1, 4, 12, 2, 51, 99, 38, 75, 81, 72, 45, 63, 84, 70, 27, 58, 41, 25, 23, 91, 73, 37, 74, 50, 54, 77, 82, 46, 85, 42, 32, 29, 94, 48, 9, 69], 'cur_cost': 106102.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [87, 91, 62, 27, 72, 54, 38, 94, 80, 49, 8, 82, 76, 34, 29, 26, 28, 4, 36, 84, 85, 56, 66, 90, 92, 46, 17, 79, 41, 68, 77, 40, 1, 45, 24, 70, 0, 83, 44, 11, 20, 59, 42, 51, 67, 32, 53, 75, 19, 71, 31, 23, 9, 52, 93, 55, 30, 7, 39, 60, 2, 97, 73, 22, 74, 25, 21, 65, 5, 96, 64, 48, 69, 89, 98, 78, 3, 14, 58, 16, 61, 63, 43, 81, 33, 86, 50, 95, 6, 47, 13, 12, 10, 35, 37, 18, 15, 88, 57, 99], 'cur_cost': 102727.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [40, 43, 26, 95, 82, 29, 51, 60, 97, 19, 70, 6, 34, 84, 45, 75, 88, 57, 66, 18, 21, 15, 5, 69, 87, 17, 58, 52, 92, 50, 39, 68, 81, 77, 85, 59, 33, 49, 11, 27, 86, 38, 53, 30, 1, 36, 12, 63, 90, 71, 20, 91, 3, 41, 55, 62, 22, 10, 16, 98, 35, 9, 78, 14, 46, 79, 74, 76, 67, 2, 80, 24, 7, 44, 0, 73, 31, 96, 61, 48, 37, 56, 47, 99, 42, 72, 94, 28, 13, 4, 89, 65, 93, 83, 23, 64, 8, 54, 32, 25], 'cur_cost': 106782.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [58, 71, 44, 59, 11, 95, 40, 67, 28, 72, 1, 86, 43, 2, 45, 24, 97, 57, 7, 5, 88, 50, 46, 48, 64, 31, 17, 37, 18, 65, 55, 23, 73, 41, 9, 90, 82, 54, 36, 68, 34, 92, 63, 80, 38, 76, 62, 0, 20, 91, 10, 52, 16, 87, 21, 30, 96, 89, 3, 93, 74, 15, 83, 6, 29, 81, 99, 42, 77, 47, 33, 22, 8, 19, 39, 84, 53, 79, 49, 13, 51, 94, 26, 56, 61, 27, 85, 66, 60, 70, 12, 75, 4, 14, 35, 78, 69, 25, 98, 32], 'cur_cost': 104479.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([20,  9,  5, 34, 31, 89, 84, 32, 33, 79, 63, 65, 43, 85, 45, 92, 62,
       55, 73, 68, 71,  8, 10, 30,  3, 76, 39, 86, 52, 22, 12, 38,  6, 18,
       66,  2,  0, 29, 36, 83, 75, 53, 93, 47, 60, 15, 82, 81, 11, 44, 46,
       26, 48, 27, 95, 98, 97, 72, 56, 50, 94, 25, 42, 67, 70, 35, 96, 61,
       49, 80, 57, 28, 90, 14, 19, 59, 88, 21, 41,  7, 91, 78, 13,  4, 51,
       99, 74, 23, 40, 37, 69, 17, 54, 24, 64, 58,  1, 16, 77, 87],
      dtype=int64), 'cur_cost': 170125.0, 'intermediate_solutions': [{'tour': array([87,  9, 60, 33, 74, 48, 68, 22, 57, 13, 56, 29,  2, 77, 76, 53, 52,
       43, 64, 12,  7, 83, 99, 98, 79,  0, 23, 28, 19, 80, 78, 51, 90, 41,
       40, 73, 86, 16, 59, 11, 72, 89, 21, 62, 47, 24, 84, 55, 34, 92, 15,
       61, 20, 36, 10, 95, 65, 71,  1, 30, 27, 26, 42, 54, 63, 17,  4, 58,
       69, 97, 32, 96, 14, 67, 75, 35, 37,  3, 46, 49, 85, 93, 44,  8,  5,
       94, 82,  6, 38, 39, 45, 91, 81, 88, 18, 31, 50, 25, 66, 70],
      dtype=int64), 'cur_cost': 182670.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([33, 87,  9, 60, 74, 48, 68, 22, 57, 13, 56, 29,  2, 77, 76, 53, 52,
       43, 64, 12,  7, 83, 99, 98, 79,  0, 23, 28, 19, 80, 78, 51, 90, 41,
       40, 73, 86, 16, 59, 11, 72, 89, 21, 62, 47, 24, 84, 55, 34, 92, 15,
       61, 20, 36, 10, 95, 65, 71,  1, 30, 27, 26, 42, 54, 63, 17,  4, 58,
       69, 97, 32, 96, 14, 67, 75, 35, 37,  3, 46, 49, 85, 93, 44,  8,  5,
       94, 82,  6, 38, 39, 45, 91, 81, 88, 18, 31, 50, 25, 66, 70],
      dtype=int64), 'cur_cost': 181032.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([74, 33, 87,  9, 60, 48, 68, 22, 57, 13, 56, 29,  2, 77, 76, 53, 52,
       43, 64, 12,  7, 83, 99, 98, 79,  0, 23, 28, 19, 80, 78, 51, 90, 41,
       40, 73, 86, 16, 59, 11, 72, 89, 21, 62, 47, 24, 84, 55, 34, 92, 15,
       61, 20, 36, 10, 95, 65, 71,  1, 30, 27, 26, 42, 54, 63, 17,  4, 58,
       69, 97, 32, 96, 14, 67, 75, 35, 37,  3, 46, 49, 85, 93, 44,  8,  5,
       94, 82,  6, 38, 39, 45, 91, 81, 88, 18, 31, 50, 25, 66, 70],
      dtype=int64), 'cur_cost': 185369.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([60, 33, 87,  9, 74, 48, 68, 22, 57, 13, 56, 29,  2, 77, 76, 53, 52,
       43, 64, 12,  7, 83, 99, 98, 79,  0, 23, 28, 19, 80, 78, 51, 90, 41,
       40, 73, 86, 16, 59, 11, 72, 89, 21, 62, 47, 24, 84, 55, 34, 92, 15,
       61, 20, 36, 10, 95, 65, 71,  1, 30, 27, 26, 42, 54, 63, 17,  4, 58,
       69, 97, 32, 96, 14, 67, 75, 35, 37,  3, 46, 49, 85, 93, 44,  8,  5,
       94, 82,  6, 38, 39, 45, 91, 81, 88, 18, 31, 50, 25, 66, 70],
      dtype=int64), 'cur_cost': 180039.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([60, 74, 33, 87,  9, 48, 68, 22, 57, 13, 56, 29,  2, 77, 76, 53, 52,
       43, 64, 12,  7, 83, 99, 98, 79,  0, 23, 28, 19, 80, 78, 51, 90, 41,
       40, 73, 86, 16, 59, 11, 72, 89, 21, 62, 47, 24, 84, 55, 34, 92, 15,
       61, 20, 36, 10, 95, 65, 71,  1, 30, 27, 26, 42, 54, 63, 17,  4, 58,
       69, 97, 32, 96, 14, 67, 75, 35, 37,  3, 46, 49, 85, 93, 44,  8,  5,
       94, 82,  6, 38, 39, 45, 91, 81, 88, 18, 31, 50, 25, 66, 70],
      dtype=int64), 'cur_cost': 182607.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 4, 3, 64, 65, 25, 69, 21, 15, 87, 93, 17, 23, 37, 35, 98, 83, 9, 71, 20, 73, 58, 16, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 78, 52, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 27, 92, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42, 66], 'cur_cost': 32630.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:21,581 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:21,581 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:21,587 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=29358.000, 多样性=0.979
2025-08-05 09:52:21,587 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 09:52:21,587 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 09:52:21,587 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:21,588 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.09002312728986465, 'best_improvement': -0.08672959466962799}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.01144524236983839}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.015840811243175063, 'recent_improvements': [0.0007981043247442405, -0.058065781505154235, -0.030883518161605894], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 7, 'new_count': 7, 'count_change': 0, 'old_best_cost': 21389, 'new_best_cost': 21389, 'quality_improvement': 0.0, 'old_diversity': 0.919047619047619, 'new_diversity': 0.919047619047619, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配']}
2025-08-05 09:52:21,589 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 09:52:21,589 - __main__ - INFO - kroA100 开始进化第 2 代
2025-08-05 09:52:21,589 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 09:52:21,590 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:21,591 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=29358.000, 多样性=0.979
2025-08-05 09:52:21,591 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:21,596 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.979
2025-08-05 09:52:21,596 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:21,600 - EliteExpert - INFO - 精英解分析完成: 精英解数量=7, 多样性=0.919
2025-08-05 09:52:21,603 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 09:52:21,603 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:21,603 - LandscapeExpert - INFO - 添加精英解数据: 7个精英解
2025-08-05 09:52:21,603 - LandscapeExpert - INFO - 数据提取成功: 17个路径, 17个适应度值
2025-08-05 09:52:21,679 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.118, 适应度梯度: -14458.918, 聚类评分: 0.000, 覆盖率: 0.167, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:21,680 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 09:52:21,680 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:21,680 - visualization.landscape_visualizer - INFO - 设置当前实例名: kroA100
2025-08-05 09:52:21,721 - visualization.landscape_visualizer - INFO - 插值约束: 333 个点被约束到最小值 21389.00
2025-08-05 09:52:21,851 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_kroA100\landscape_kroA100_iter_147_20250805_095221.html
2025-08-05 09:52:21,937 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_kroA100\dashboard_kroA100_iter_147_20250805_095221.html
2025-08-05 09:52:21,937 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 147
2025-08-05 09:52:21,937 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 09:52:21,937 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.3337秒
2025-08-05 09:52:21,937 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.11764705882352941, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -14458.917647058825, 'local_optima_density': 0.11764705882352941, 'gradient_variance': 2585657425.750865, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1669, 'fitness_entropy': 0.7954584372331487, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -14458.918)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.167)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358741.6808276, 'performance_metrics': {}}}
2025-08-05 09:52:21,938 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:21,938 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:21,938 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:21,938 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:21,939 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:52:21,939 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 09:52:21,939 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:52:21,939 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:21,939 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:21,939 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:52:21,940 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:21,940 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:21,940 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:21,940 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:21,940 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:21,940 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:21,952 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 100
2025-08-05 09:52:21,952 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:21,952 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:21,953 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:21,953 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:21,953 - ExplorationExpert - INFO - 探索路径生成完成，成本: 104938.0, 路径长度: 100, 收集中间解: 3
2025-08-05 09:52:21,953 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [84, 81, 39, 80, 38, 11, 46, 66, 0, 92, 18, 71, 31, 78, 9, 30, 43, 19, 29, 75, 54, 57, 50, 13, 61, 82, 85, 16, 48, 7, 79, 24, 95, 70, 47, 99, 59, 33, 77, 32, 60, 63, 26, 62, 88, 87, 14, 97, 35, 83, 93, 65, 69, 89, 64, 41, 68, 49, 76, 28, 36, 27, 58, 44, 10, 22, 74, 23, 21, 3, 53, 45, 51, 2, 4, 40, 34, 73, 52, 25, 96, 17, 15, 91, 98, 86, 8, 6, 90, 37, 5, 56, 1, 67, 72, 12, 94, 42, 20, 55], 'cur_cost': 104938.0, 'intermediate_solutions': [{'tour': [0, 8, 11, 3, 64, 65, 25, 69, 21, 15, 87, 93, 17, 62, 37, 35, 98, 83, 9, 71, 20, 73, 58, 16, 14, 10, 31, 90, 97, 22, 44, 46, 23, 5, 48, 89, 78, 52, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 27, 92, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 6, 56, 86, 50, 19, 85, 26, 34, 61, 59, 76, 66, 42], 'cur_cost': 33825.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 11, 3, 64, 65, 25, 69, 21, 15, 87, 93, 17, 23, 37, 35, 98, 83, 9, 71, 20, 73, 58, 16, 14, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 78, 52, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 27, 92, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 76, 59, 61, 34, 26, 85, 19, 50, 86, 56, 6, 54, 82, 33, 66, 42], 'cur_cost': 32278.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 11, 3, 64, 65, 25, 69, 21, 15, 87, 93, 17, 23, 37, 35, 98, 83, 9, 71, 20, 73, 58, 16, 14, 10, 31, 90, 97, 22, 44, 80, 46, 62, 5, 48, 89, 78, 52, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 27, 92, 57, 60, 24, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 6, 56, 86, 50, 19, 85, 26, 34, 61, 59, 76, 66, 42], 'cur_cost': 33016.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:21,954 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 104938.00)
2025-08-05 09:52:21,954 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:21,954 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:21,955 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:21,958 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 09:52:21,958 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:21,959 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:21,959 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:21,959 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:21,960 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32193.0, 路径长度: 100, 收集中间解: 3
2025-08-05 09:52:21,960 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 19, 16, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 9, 83, 71, 20, 73, 58, 10, 14, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 61, 59, 76, 56, 6, 8, 86, 50, 42, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 25, 98], 'cur_cost': 32193.0, 'intermediate_solutions': [{'tour': [0, 14, 20, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 27, 45, 28, 33, 82, 54, 42, 61, 59, 76, 22, 97, 90, 44, 31, 10, 16, 73, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 2, 66, 57], 'cur_cost': 36059.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 20, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 42, 61, 59, 76, 22, 97, 90, 44, 31, 10, 16, 73, 58, 71, 37, 35, 83, 9, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57], 'cur_cost': 30042.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 20, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 42, 61, 59, 76, 22, 97, 90, 44, 31, 10, 16, 73, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57], 'cur_cost': 29358.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:21,960 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 32193.00)
2025-08-05 09:52:21,960 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:21,961 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:21,961 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:21,965 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 09:52:21,965 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:21,966 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:21,966 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:21,966 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:21,967 - ExplorationExpert - INFO - 探索路径生成完成，成本: 29422.0, 路径长度: 100, 收集中间解: 3
2025-08-05 09:52:21,967 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 5, 18, 20, 73, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 48, 62, 89, 74, 25, 98, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 29422.0, 'intermediate_solutions': [{'tour': [62, 18, 17, 10, 14, 16, 31, 90, 97, 22, 44, 46, 0, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 30852.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 17, 10, 14, 16, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 42, 61], 'cur_cost': 30746.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 17, 10, 14, 16, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 25, 98, 92, 27, 66, 57, 12, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 33722.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:21,967 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 29422.00)
2025-08-05 09:52:21,968 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:52:21,968 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:21,968 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:21,968 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 180861.0
2025-08-05 09:52:21,994 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:21,994 - ExploitationExpert - INFO - res_population_costs: [21389, 21411, 21776, 22282, 22308, 22356, 22427.0]
2025-08-05 09:52:21,995 - ExploitationExpert - INFO - res_populations: [array([ 0, 62, 89, 48,  5, 91,  7, 41, 88, 30, 79, 55, 96, 74, 18,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26,
       11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99, 47,
       51, 77, 95,  4, 36, 32, 75, 12, 94, 81, 38, 29, 84, 67, 72, 49, 43,
        1, 53, 39, 63, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 62,  5, 48, 89, 18, 74, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 59, 61, 76, 50, 86,  8,
        6, 56, 19, 85, 34, 26, 11, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40,
       99, 47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81, 49, 43,  1,
       53, 39, 63, 68, 72, 67, 84, 80, 24, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 91, 74, 96, 55, 79, 30, 88, 41,  7, 66, 27, 92, 57, 60, 24, 80,
       84, 67, 72, 68, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75, 32, 36,  4,
       51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28, 33, 82, 54,
       11, 26, 34, 85, 19, 56,  6,  8, 86, 50, 76, 61, 59, 22, 97, 90, 44,
       31, 10, 14, 16, 58, 73, 20, 71,  9, 83, 35, 98, 37, 23, 17, 78, 52,
       87, 15, 93, 21, 69, 65, 25, 64,  3, 18, 89, 48,  5, 62, 46],
      dtype=int64), array([ 0, 46, 92, 27, 66, 57, 68, 63, 39, 53,  1, 43, 72, 49, 81, 94, 12,
       75, 32, 36,  4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45,
       28, 33, 82, 54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 80, 24, 60,
       50, 86, 76, 61, 59, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20, 71,
        9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3, 18,
       52, 78, 89, 74, 96, 55, 79, 30, 88, 41,  7, 91, 48,  5, 62],
      dtype=int64), array([ 0, 62, 46, 92, 27, 57, 66, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75,
       32, 36,  4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28,
       33, 82, 54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 72, 68, 80, 24,
       60, 50, 86, 76, 61, 59, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20,
       71,  9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3,
       18, 52, 78, 89, 48,  5, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 62,  5, 48, 91,  7, 41, 88, 30, 79, 55, 96, 74, 89, 78, 52, 18,
        3, 64, 25, 65, 69, 21, 15, 87, 93, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 59, 61, 76, 86, 50, 60,
       24, 80, 68, 72, 67, 84,  8,  6, 56, 19, 85, 34, 26, 11, 54, 82, 33,
       28, 45, 42,  2, 13, 70, 40, 99, 47, 29, 38, 95, 77, 51,  4, 36, 32,
       75, 12, 94, 81, 49, 43,  1, 53, 39, 63, 66, 57, 27, 92, 46],
      dtype=int64), array([ 0, 92, 27, 57, 66, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75, 32, 36,
        4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28, 33, 82,
       54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 72, 68, 80, 24, 60, 50,
       86, 76, 61, 59, 22, 97, 44, 90, 46, 31, 10, 14, 16, 58, 73, 20, 71,
        9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3, 18,
       52, 78, 89, 74, 96, 55, 79, 30, 88, 41,  7, 91, 48,  5, 62],
      dtype=int64)]
2025-08-05 09:52:21,998 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:21,998 - ExploitationExpert - INFO - populations: [{'tour': [84, 81, 39, 80, 38, 11, 46, 66, 0, 92, 18, 71, 31, 78, 9, 30, 43, 19, 29, 75, 54, 57, 50, 13, 61, 82, 85, 16, 48, 7, 79, 24, 95, 70, 47, 99, 59, 33, 77, 32, 60, 63, 26, 62, 88, 87, 14, 97, 35, 83, 93, 65, 69, 89, 64, 41, 68, 49, 76, 28, 36, 27, 58, 44, 10, 22, 74, 23, 21, 3, 53, 45, 51, 2, 4, 40, 34, 73, 52, 25, 96, 17, 15, 91, 98, 86, 8, 6, 90, 37, 5, 56, 1, 67, 72, 12, 94, 42, 20, 55], 'cur_cost': 104938.0}, {'tour': [0, 19, 16, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 9, 83, 71, 20, 73, 58, 10, 14, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 61, 59, 76, 56, 6, 8, 86, 50, 42, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 25, 98], 'cur_cost': 32193.0}, {'tour': [0, 5, 18, 20, 73, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 48, 62, 89, 74, 25, 98, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 29422.0}, {'tour': array([32,  3, 56, 42, 88, 45,  0, 17, 14, 67, 23, 37, 63,  5, 59, 11, 53,
       81, 52, 54, 35, 70, 55, 92, 75, 30, 69, 72, 33,  8, 76,  7, 43, 15,
       50, 65, 89, 28, 36, 58, 44, 21, 83, 74,  4, 98, 27, 60, 49, 51, 99,
       41, 12, 20, 34, 94,  9, 68, 57, 90, 16, 91, 86, 95, 82, 66, 40, 87,
       73, 71, 46, 84, 78, 80, 38, 47, 10, 29, 22,  2, 26, 13, 62, 97, 96,
       85, 19, 31, 24, 79, 48,  6, 64, 18, 39, 77, 93,  1, 61, 25],
      dtype=int64), 'cur_cost': 180861.0}, {'tour': [65, 79, 30, 62, 14, 76, 28, 43, 19, 44, 60, 49, 53, 39, 6, 95, 61, 86, 47, 36, 34, 13, 66, 59, 90, 20, 5, 52, 31, 98, 7, 17, 10, 87, 57, 88, 24, 68, 92, 96, 22, 83, 16, 35, 93, 97, 89, 3, 78, 21, 64, 71, 55, 18, 15, 0, 11, 8, 33, 40, 26, 67, 56, 80, 1, 4, 12, 2, 51, 99, 38, 75, 81, 72, 45, 63, 84, 70, 27, 58, 41, 25, 23, 91, 73, 37, 74, 50, 54, 77, 82, 46, 85, 42, 32, 29, 94, 48, 9, 69], 'cur_cost': 106102.0}, {'tour': [87, 91, 62, 27, 72, 54, 38, 94, 80, 49, 8, 82, 76, 34, 29, 26, 28, 4, 36, 84, 85, 56, 66, 90, 92, 46, 17, 79, 41, 68, 77, 40, 1, 45, 24, 70, 0, 83, 44, 11, 20, 59, 42, 51, 67, 32, 53, 75, 19, 71, 31, 23, 9, 52, 93, 55, 30, 7, 39, 60, 2, 97, 73, 22, 74, 25, 21, 65, 5, 96, 64, 48, 69, 89, 98, 78, 3, 14, 58, 16, 61, 63, 43, 81, 33, 86, 50, 95, 6, 47, 13, 12, 10, 35, 37, 18, 15, 88, 57, 99], 'cur_cost': 102727.0}, {'tour': [40, 43, 26, 95, 82, 29, 51, 60, 97, 19, 70, 6, 34, 84, 45, 75, 88, 57, 66, 18, 21, 15, 5, 69, 87, 17, 58, 52, 92, 50, 39, 68, 81, 77, 85, 59, 33, 49, 11, 27, 86, 38, 53, 30, 1, 36, 12, 63, 90, 71, 20, 91, 3, 41, 55, 62, 22, 10, 16, 98, 35, 9, 78, 14, 46, 79, 74, 76, 67, 2, 80, 24, 7, 44, 0, 73, 31, 96, 61, 48, 37, 56, 47, 99, 42, 72, 94, 28, 13, 4, 89, 65, 93, 83, 23, 64, 8, 54, 32, 25], 'cur_cost': 106782.0}, {'tour': [58, 71, 44, 59, 11, 95, 40, 67, 28, 72, 1, 86, 43, 2, 45, 24, 97, 57, 7, 5, 88, 50, 46, 48, 64, 31, 17, 37, 18, 65, 55, 23, 73, 41, 9, 90, 82, 54, 36, 68, 34, 92, 63, 80, 38, 76, 62, 0, 20, 91, 10, 52, 16, 87, 21, 30, 96, 89, 3, 93, 74, 15, 83, 6, 29, 81, 99, 42, 77, 47, 33, 22, 8, 19, 39, 84, 53, 79, 49, 13, 51, 94, 26, 56, 61, 27, 85, 66, 60, 70, 12, 75, 4, 14, 35, 78, 69, 25, 98, 32], 'cur_cost': 104479.0}, {'tour': [20, 9, 5, 34, 31, 89, 84, 32, 33, 79, 63, 65, 43, 85, 45, 92, 62, 55, 73, 68, 71, 8, 10, 30, 3, 76, 39, 86, 52, 22, 12, 38, 6, 18, 66, 2, 0, 29, 36, 83, 75, 53, 93, 47, 60, 15, 82, 81, 11, 44, 46, 26, 48, 27, 95, 98, 97, 72, 56, 50, 94, 25, 42, 67, 70, 35, 96, 61, 49, 80, 57, 28, 90, 14, 19, 59, 88, 21, 41, 7, 91, 78, 13, 4, 51, 99, 74, 23, 40, 37, 69, 17, 54, 24, 64, 58, 1, 16, 77, 87], 'cur_cost': 170125.0}, {'tour': [0, 14, 4, 3, 64, 65, 25, 69, 21, 15, 87, 93, 17, 23, 37, 35, 98, 83, 9, 71, 20, 73, 58, 16, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 78, 52, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 27, 92, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42, 66], 'cur_cost': 32630.0}]
2025-08-05 09:52:21,999 - ExploitationExpert - INFO - 局部搜索耗时: 0.03秒，最大迭代次数: 10
2025-08-05 09:52:22,000 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 380, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 380, 'cache_hits': 0, 'similarity_calculations': 1983, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:22,000 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([32,  3, 56, 42, 88, 45,  0, 17, 14, 67, 23, 37, 63,  5, 59, 11, 53,
       81, 52, 54, 35, 70, 55, 92, 75, 30, 69, 72, 33,  8, 76,  7, 43, 15,
       50, 65, 89, 28, 36, 58, 44, 21, 83, 74,  4, 98, 27, 60, 49, 51, 99,
       41, 12, 20, 34, 94,  9, 68, 57, 90, 16, 91, 86, 95, 82, 66, 40, 87,
       73, 71, 46, 84, 78, 80, 38, 47, 10, 29, 22,  2, 26, 13, 62, 97, 96,
       85, 19, 31, 24, 79, 48,  6, 64, 18, 39, 77, 93,  1, 61, 25],
      dtype=int64), 'cur_cost': 180861.0, 'intermediate_solutions': [{'tour': array([ 8, 63,  4, 10, 27, 76, 42, 17, 94, 34,  7, 81, 15, 53, 73, 20, 83,
       96, 95, 12, 93,  2, 52, 61, 48, 35, 77,  9, 66, 11, 51, 70, 60, 90,
       57, 32, 45, 89, 21, 44, 58, 86, 50, 38, 79, 37,  1, 56, 14, 88, 28,
       71, 19, 43, 39, 30, 47, 78, 92, 54, 99, 69, 36, 46, 23,  3, 59, 31,
       13, 64, 74, 67,  0, 97, 75, 84, 29, 87, 25, 49, 82, 85, 26, 80, 91,
        6, 98,  5, 22, 18, 72, 65, 24, 68, 55, 41, 62, 40, 33, 16]), 'cur_cost': 176987.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([10,  8, 63,  4, 27, 76, 42, 17, 94, 34,  7, 81, 15, 53, 73, 20, 83,
       96, 95, 12, 93,  2, 52, 61, 48, 35, 77,  9, 66, 11, 51, 70, 60, 90,
       57, 32, 45, 89, 21, 44, 58, 86, 50, 38, 79, 37,  1, 56, 14, 88, 28,
       71, 19, 43, 39, 30, 47, 78, 92, 54, 99, 69, 36, 46, 23,  3, 59, 31,
       13, 64, 74, 67,  0, 97, 75, 84, 29, 87, 25, 49, 82, 85, 26, 80, 91,
        6, 98,  5, 22, 18, 72, 65, 24, 68, 55, 41, 62, 40, 33, 16]), 'cur_cost': 175257.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([27, 10,  8, 63,  4, 76, 42, 17, 94, 34,  7, 81, 15, 53, 73, 20, 83,
       96, 95, 12, 93,  2, 52, 61, 48, 35, 77,  9, 66, 11, 51, 70, 60, 90,
       57, 32, 45, 89, 21, 44, 58, 86, 50, 38, 79, 37,  1, 56, 14, 88, 28,
       71, 19, 43, 39, 30, 47, 78, 92, 54, 99, 69, 36, 46, 23,  3, 59, 31,
       13, 64, 74, 67,  0, 97, 75, 84, 29, 87, 25, 49, 82, 85, 26, 80, 91,
        6, 98,  5, 22, 18, 72, 65, 24, 68, 55, 41, 62, 40, 33, 16]), 'cur_cost': 176385.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4, 10,  8, 63, 27, 76, 42, 17, 94, 34,  7, 81, 15, 53, 73, 20, 83,
       96, 95, 12, 93,  2, 52, 61, 48, 35, 77,  9, 66, 11, 51, 70, 60, 90,
       57, 32, 45, 89, 21, 44, 58, 86, 50, 38, 79, 37,  1, 56, 14, 88, 28,
       71, 19, 43, 39, 30, 47, 78, 92, 54, 99, 69, 36, 46, 23,  3, 59, 31,
       13, 64, 74, 67,  0, 97, 75, 84, 29, 87, 25, 49, 82, 85, 26, 80, 91,
        6, 98,  5, 22, 18, 72, 65, 24, 68, 55, 41, 62, 40, 33, 16]), 'cur_cost': 178540.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4, 27, 10,  8, 63, 76, 42, 17, 94, 34,  7, 81, 15, 53, 73, 20, 83,
       96, 95, 12, 93,  2, 52, 61, 48, 35, 77,  9, 66, 11, 51, 70, 60, 90,
       57, 32, 45, 89, 21, 44, 58, 86, 50, 38, 79, 37,  1, 56, 14, 88, 28,
       71, 19, 43, 39, 30, 47, 78, 92, 54, 99, 69, 36, 46, 23,  3, 59, 31,
       13, 64, 74, 67,  0, 97, 75, 84, 29, 87, 25, 49, 82, 85, 26, 80, 91,
        6, 98,  5, 22, 18, 72, 65, 24, 68, 55, 41, 62, 40, 33, 16]), 'cur_cost': 178384.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:22,002 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 180861.00)
2025-08-05 09:52:22,002 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:22,002 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:22,002 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:22,015 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 100
2025-08-05 09:52:22,015 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,015 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,015 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,016 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,016 - ExplorationExpert - INFO - 探索路径生成完成，成本: 96301.0, 路径长度: 100, 收集中间解: 3
2025-08-05 09:52:22,017 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [1, 53, 12, 6, 44, 62, 64, 21, 52, 41, 74, 98, 15, 30, 72, 51, 33, 19, 11, 54, 2, 84, 99, 94, 32, 82, 60, 59, 86, 13, 28, 75, 76, 91, 23, 22, 61, 50, 10, 73, 78, 3, 31, 27, 66, 48, 89, 18, 83, 69, 71, 90, 16, 35, 87, 7, 65, 57, 67, 29, 40, 45, 42, 70, 8, 56, 34, 85, 80, 24, 4, 68, 0, 9, 58, 96, 46, 79, 20, 93, 39, 43, 95, 77, 49, 26, 36, 63, 38, 97, 92, 5, 55, 88, 25, 37, 17, 14, 47, 81], 'cur_cost': 96301.0, 'intermediate_solutions': [{'tour': [65, 79, 30, 62, 14, 76, 28, 43, 19, 44, 60, 49, 53, 39, 63, 95, 61, 86, 47, 36, 34, 13, 66, 59, 90, 20, 5, 52, 31, 98, 7, 17, 10, 87, 57, 88, 24, 68, 92, 96, 22, 83, 16, 35, 93, 97, 89, 3, 78, 21, 64, 71, 55, 18, 15, 0, 11, 8, 33, 40, 26, 67, 56, 80, 1, 4, 12, 2, 51, 99, 38, 75, 81, 72, 45, 6, 84, 70, 27, 58, 41, 25, 23, 91, 73, 37, 74, 50, 54, 77, 82, 46, 85, 42, 32, 29, 94, 48, 9, 69], 'cur_cost': 104076.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [65, 79, 30, 62, 14, 76, 28, 43, 19, 44, 60, 49, 53, 39, 6, 95, 61, 86, 47, 36, 34, 13, 66, 59, 90, 20, 5, 52, 31, 98, 7, 17, 10, 87, 57, 88, 24, 68, 92, 96, 22, 83, 16, 35, 93, 97, 89, 3, 78, 21, 64, 71, 55, 18, 15, 0, 11, 29, 32, 42, 85, 46, 82, 77, 54, 50, 74, 37, 73, 91, 23, 25, 41, 58, 27, 70, 84, 63, 45, 72, 81, 75, 38, 99, 51, 2, 12, 4, 1, 80, 56, 67, 26, 40, 33, 8, 94, 48, 9, 69], 'cur_cost': 107466.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [65, 79, 30, 62, 14, 76, 28, 43, 19, 44, 60, 49, 53, 39, 6, 95, 42, 61, 86, 47, 36, 34, 13, 66, 59, 90, 20, 5, 52, 31, 98, 7, 17, 10, 87, 57, 88, 24, 68, 92, 96, 22, 83, 16, 35, 93, 97, 89, 3, 78, 21, 64, 71, 55, 18, 15, 0, 11, 8, 33, 40, 26, 67, 56, 80, 1, 4, 12, 2, 51, 99, 38, 75, 81, 72, 45, 63, 84, 70, 27, 58, 41, 25, 23, 91, 73, 37, 74, 50, 54, 77, 82, 46, 85, 32, 29, 94, 48, 9, 69], 'cur_cost': 106069.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:22,017 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 96301.00)
2025-08-05 09:52:22,017 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:22,017 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:22,017 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:22,030 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 100
2025-08-05 09:52:22,031 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,031 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,031 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,031 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,032 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101357.0, 路径长度: 100, 收集中间解: 3
2025-08-05 09:52:22,032 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [83, 23, 91, 22, 11, 61, 58, 52, 73, 31, 44, 26, 72, 56, 34, 85, 27, 74, 71, 69, 3, 35, 48, 41, 88, 68, 33, 13, 45, 40, 47, 94, 8, 77, 19, 38, 39, 4, 81, 32, 1, 67, 36, 42, 29, 70, 6, 28, 63, 84, 24, 62, 10, 92, 80, 86, 60, 0, 78, 55, 46, 59, 5, 90, 17, 25, 37, 96, 9, 15, 16, 20, 64, 87, 66, 57, 49, 51, 82, 99, 50, 76, 14, 21, 93, 30, 18, 89, 53, 79, 97, 7, 54, 43, 95, 12, 75, 2, 98, 65], 'cur_cost': 101357.0, 'intermediate_solutions': [{'tour': [87, 91, 62, 27, 72, 54, 38, 94, 80, 49, 8, 82, 76, 34, 29, 26, 28, 4, 36, 84, 85, 56, 66, 90, 92, 46, 17, 79, 41, 68, 77, 40, 1, 45, 24, 70, 0, 7, 44, 11, 20, 59, 42, 51, 67, 32, 53, 75, 19, 71, 31, 23, 9, 52, 93, 55, 30, 83, 39, 60, 2, 97, 73, 22, 74, 25, 21, 65, 5, 96, 64, 48, 69, 89, 98, 78, 3, 14, 58, 16, 61, 63, 43, 81, 33, 86, 50, 95, 6, 47, 13, 12, 10, 35, 37, 18, 15, 88, 57, 99], 'cur_cost': 104471.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [87, 91, 62, 27, 72, 54, 38, 94, 80, 49, 8, 82, 76, 34, 29, 26, 28, 4, 36, 84, 85, 56, 66, 90, 92, 46, 17, 79, 41, 68, 77, 40, 1, 45, 24, 70, 0, 83, 44, 11, 20, 59, 42, 51, 67, 32, 53, 75, 19, 71, 31, 23, 9, 52, 93, 55, 30, 7, 39, 60, 2, 97, 73, 22, 74, 25, 21, 65, 5, 96, 64, 48, 69, 89, 98, 78, 3, 14, 58, 16, 61, 63, 43, 81, 33, 86, 50, 95, 6, 47, 13, 12, 15, 18, 37, 35, 10, 88, 57, 99], 'cur_cost': 103639.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [87, 91, 62, 27, 72, 54, 38, 94, 80, 49, 8, 82, 76, 34, 29, 26, 28, 4, 36, 84, 85, 56, 66, 90, 92, 46, 17, 79, 41, 68, 77, 40, 1, 45, 24, 70, 0, 83, 44, 11, 20, 59, 42, 51, 67, 32, 53, 75, 19, 71, 31, 23, 9, 52, 93, 55, 30, 7, 39, 60, 2, 97, 73, 22, 74, 25, 21, 65, 5, 96, 64, 48, 69, 89, 98, 78, 3, 14, 58, 16, 61, 63, 43, 81, 33, 86, 95, 6, 47, 13, 12, 10, 35, 50, 37, 18, 15, 88, 57, 99], 'cur_cost': 106568.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:22,032 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 101357.00)
2025-08-05 09:52:22,032 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:22,032 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:22,032 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:22,038 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 09:52:22,038 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,038 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,039 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,039 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,039 - ExplorationExpert - INFO - 探索路径生成完成，成本: 33761.0, 路径长度: 100, 收集中间解: 3
2025-08-05 09:52:22,039 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 6, 18, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 33761.0, 'intermediate_solutions': [{'tour': [40, 43, 26, 95, 82, 29, 51, 60, 97, 19, 70, 6, 34, 84, 45, 75, 88, 57, 66, 18, 21, 15, 5, 69, 87, 17, 58, 52, 92, 50, 39, 68, 81, 77, 85, 59, 33, 49, 11, 27, 86, 38, 53, 30, 1, 36, 12, 63, 90, 71, 20, 91, 3, 93, 55, 62, 22, 10, 16, 98, 35, 9, 78, 14, 46, 79, 74, 76, 67, 2, 80, 24, 7, 44, 0, 73, 31, 96, 61, 48, 37, 56, 47, 99, 42, 72, 94, 28, 13, 4, 89, 65, 41, 83, 23, 64, 8, 54, 32, 25], 'cur_cost': 108511.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [40, 43, 26, 95, 82, 29, 51, 60, 97, 19, 70, 6, 34, 84, 45, 75, 88, 57, 66, 18, 21, 15, 5, 69, 87, 17, 58, 52, 92, 50, 39, 68, 81, 77, 85, 59, 33, 49, 11, 27, 86, 38, 53, 30, 1, 36, 12, 63, 90, 71, 20, 91, 3, 41, 55, 62, 22, 10, 16, 98, 35, 9, 78, 14, 46, 79, 74, 76, 67, 2, 80, 24, 7, 44, 0, 73, 31, 96, 61, 48, 37, 56, 89, 4, 13, 28, 94, 72, 42, 99, 47, 65, 93, 83, 23, 64, 8, 54, 32, 25], 'cur_cost': 110380.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [40, 43, 26, 95, 82, 29, 51, 60, 97, 19, 70, 6, 34, 84, 45, 75, 88, 57, 66, 18, 21, 15, 5, 69, 87, 17, 58, 52, 92, 50, 39, 68, 81, 77, 85, 59, 33, 49, 11, 27, 86, 38, 53, 30, 1, 36, 12, 63, 90, 71, 20, 91, 3, 41, 55, 62, 22, 10, 16, 98, 35, 78, 14, 46, 79, 74, 76, 67, 2, 80, 24, 7, 44, 0, 73, 31, 96, 61, 48, 37, 56, 47, 99, 9, 42, 72, 94, 28, 13, 4, 89, 65, 93, 83, 23, 64, 8, 54, 32, 25], 'cur_cost': 111919.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:22,040 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 33761.00)
2025-08-05 09:52:22,040 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:22,040 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:22,040 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:22,043 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 09:52:22,043 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,044 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,044 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,044 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,045 - ExplorationExpert - INFO - 探索路径生成完成，成本: 33683.0, 路径长度: 100, 收集中间解: 3
2025-08-05 09:52:22,045 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 13, 10, 19, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 2, 45, 28, 33, 82, 54, 6, 8, 56, 86, 50, 42], 'cur_cost': 33683.0, 'intermediate_solutions': [{'tour': [58, 71, 44, 59, 11, 95, 40, 67, 28, 72, 1, 86, 43, 2, 45, 24, 97, 57, 7, 5, 88, 50, 46, 48, 64, 31, 17, 37, 18, 65, 55, 23, 73, 41, 9, 90, 82, 54, 74, 68, 34, 92, 63, 80, 38, 76, 62, 0, 20, 91, 10, 52, 16, 87, 21, 30, 96, 89, 3, 93, 36, 15, 83, 6, 29, 81, 99, 42, 77, 47, 33, 22, 8, 19, 39, 84, 53, 79, 49, 13, 51, 94, 26, 56, 61, 27, 85, 66, 60, 70, 12, 75, 4, 14, 35, 78, 69, 25, 98, 32], 'cur_cost': 111842.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [58, 71, 44, 59, 11, 95, 40, 67, 28, 72, 1, 86, 43, 2, 45, 24, 97, 57, 7, 5, 88, 50, 46, 48, 64, 31, 17, 37, 18, 65, 55, 23, 73, 41, 9, 90, 82, 54, 36, 68, 34, 92, 63, 80, 38, 76, 62, 0, 20, 91, 10, 52, 16, 87, 21, 30, 96, 89, 3, 93, 74, 15, 83, 6, 29, 81, 99, 42, 77, 47, 33, 22, 8, 19, 39, 84, 53, 79, 49, 13, 51, 94, 26, 56, 61, 27, 85, 66, 60, 70, 12, 75, 4, 32, 98, 25, 69, 78, 35, 14], 'cur_cost': 98944.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [58, 71, 44, 59, 11, 95, 40, 67, 28, 72, 1, 86, 43, 2, 45, 24, 97, 57, 7, 5, 88, 50, 46, 48, 64, 31, 17, 37, 18, 65, 55, 23, 73, 41, 9, 90, 82, 54, 36, 68, 34, 92, 63, 80, 38, 76, 62, 0, 20, 91, 10, 52, 16, 87, 21, 30, 96, 89, 3, 93, 74, 15, 83, 6, 29, 81, 99, 42, 77, 47, 33, 22, 8, 19, 39, 84, 53, 79, 49, 66, 13, 51, 94, 26, 56, 61, 27, 85, 60, 70, 12, 75, 4, 14, 35, 78, 69, 25, 98, 32], 'cur_cost': 105229.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:22,045 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 33683.00)
2025-08-05 09:52:22,045 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:52:22,045 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:22,046 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:22,046 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 172385.0
2025-08-05 09:52:22,069 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:22,069 - ExploitationExpert - INFO - res_population_costs: [21389, 21411, 21776, 22282, 22308, 22356, 22427.0]
2025-08-05 09:52:22,070 - ExploitationExpert - INFO - res_populations: [array([ 0, 62, 89, 48,  5, 91,  7, 41, 88, 30, 79, 55, 96, 74, 18,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26,
       11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99, 47,
       51, 77, 95,  4, 36, 32, 75, 12, 94, 81, 38, 29, 84, 67, 72, 49, 43,
        1, 53, 39, 63, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 62,  5, 48, 89, 18, 74, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 59, 61, 76, 50, 86,  8,
        6, 56, 19, 85, 34, 26, 11, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40,
       99, 47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81, 49, 43,  1,
       53, 39, 63, 68, 72, 67, 84, 80, 24, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 91, 74, 96, 55, 79, 30, 88, 41,  7, 66, 27, 92, 57, 60, 24, 80,
       84, 67, 72, 68, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75, 32, 36,  4,
       51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28, 33, 82, 54,
       11, 26, 34, 85, 19, 56,  6,  8, 86, 50, 76, 61, 59, 22, 97, 90, 44,
       31, 10, 14, 16, 58, 73, 20, 71,  9, 83, 35, 98, 37, 23, 17, 78, 52,
       87, 15, 93, 21, 69, 65, 25, 64,  3, 18, 89, 48,  5, 62, 46],
      dtype=int64), array([ 0, 46, 92, 27, 66, 57, 68, 63, 39, 53,  1, 43, 72, 49, 81, 94, 12,
       75, 32, 36,  4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45,
       28, 33, 82, 54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 80, 24, 60,
       50, 86, 76, 61, 59, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20, 71,
        9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3, 18,
       52, 78, 89, 74, 96, 55, 79, 30, 88, 41,  7, 91, 48,  5, 62],
      dtype=int64), array([ 0, 62, 46, 92, 27, 57, 66, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75,
       32, 36,  4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28,
       33, 82, 54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 72, 68, 80, 24,
       60, 50, 86, 76, 61, 59, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20,
       71,  9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3,
       18, 52, 78, 89, 48,  5, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 62,  5, 48, 91,  7, 41, 88, 30, 79, 55, 96, 74, 89, 78, 52, 18,
        3, 64, 25, 65, 69, 21, 15, 87, 93, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 59, 61, 76, 86, 50, 60,
       24, 80, 68, 72, 67, 84,  8,  6, 56, 19, 85, 34, 26, 11, 54, 82, 33,
       28, 45, 42,  2, 13, 70, 40, 99, 47, 29, 38, 95, 77, 51,  4, 36, 32,
       75, 12, 94, 81, 49, 43,  1, 53, 39, 63, 66, 57, 27, 92, 46],
      dtype=int64), array([ 0, 92, 27, 57, 66, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75, 32, 36,
        4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28, 33, 82,
       54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 72, 68, 80, 24, 60, 50,
       86, 76, 61, 59, 22, 97, 44, 90, 46, 31, 10, 14, 16, 58, 73, 20, 71,
        9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3, 18,
       52, 78, 89, 74, 96, 55, 79, 30, 88, 41,  7, 91, 48,  5, 62],
      dtype=int64)]
2025-08-05 09:52:22,073 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:22,073 - ExploitationExpert - INFO - populations: [{'tour': [84, 81, 39, 80, 38, 11, 46, 66, 0, 92, 18, 71, 31, 78, 9, 30, 43, 19, 29, 75, 54, 57, 50, 13, 61, 82, 85, 16, 48, 7, 79, 24, 95, 70, 47, 99, 59, 33, 77, 32, 60, 63, 26, 62, 88, 87, 14, 97, 35, 83, 93, 65, 69, 89, 64, 41, 68, 49, 76, 28, 36, 27, 58, 44, 10, 22, 74, 23, 21, 3, 53, 45, 51, 2, 4, 40, 34, 73, 52, 25, 96, 17, 15, 91, 98, 86, 8, 6, 90, 37, 5, 56, 1, 67, 72, 12, 94, 42, 20, 55], 'cur_cost': 104938.0}, {'tour': [0, 19, 16, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 9, 83, 71, 20, 73, 58, 10, 14, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 61, 59, 76, 56, 6, 8, 86, 50, 42, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 25, 98], 'cur_cost': 32193.0}, {'tour': [0, 5, 18, 20, 73, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 48, 62, 89, 74, 25, 98, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 29422.0}, {'tour': array([32,  3, 56, 42, 88, 45,  0, 17, 14, 67, 23, 37, 63,  5, 59, 11, 53,
       81, 52, 54, 35, 70, 55, 92, 75, 30, 69, 72, 33,  8, 76,  7, 43, 15,
       50, 65, 89, 28, 36, 58, 44, 21, 83, 74,  4, 98, 27, 60, 49, 51, 99,
       41, 12, 20, 34, 94,  9, 68, 57, 90, 16, 91, 86, 95, 82, 66, 40, 87,
       73, 71, 46, 84, 78, 80, 38, 47, 10, 29, 22,  2, 26, 13, 62, 97, 96,
       85, 19, 31, 24, 79, 48,  6, 64, 18, 39, 77, 93,  1, 61, 25],
      dtype=int64), 'cur_cost': 180861.0}, {'tour': [1, 53, 12, 6, 44, 62, 64, 21, 52, 41, 74, 98, 15, 30, 72, 51, 33, 19, 11, 54, 2, 84, 99, 94, 32, 82, 60, 59, 86, 13, 28, 75, 76, 91, 23, 22, 61, 50, 10, 73, 78, 3, 31, 27, 66, 48, 89, 18, 83, 69, 71, 90, 16, 35, 87, 7, 65, 57, 67, 29, 40, 45, 42, 70, 8, 56, 34, 85, 80, 24, 4, 68, 0, 9, 58, 96, 46, 79, 20, 93, 39, 43, 95, 77, 49, 26, 36, 63, 38, 97, 92, 5, 55, 88, 25, 37, 17, 14, 47, 81], 'cur_cost': 96301.0}, {'tour': [83, 23, 91, 22, 11, 61, 58, 52, 73, 31, 44, 26, 72, 56, 34, 85, 27, 74, 71, 69, 3, 35, 48, 41, 88, 68, 33, 13, 45, 40, 47, 94, 8, 77, 19, 38, 39, 4, 81, 32, 1, 67, 36, 42, 29, 70, 6, 28, 63, 84, 24, 62, 10, 92, 80, 86, 60, 0, 78, 55, 46, 59, 5, 90, 17, 25, 37, 96, 9, 15, 16, 20, 64, 87, 66, 57, 49, 51, 82, 99, 50, 76, 14, 21, 93, 30, 18, 89, 53, 79, 97, 7, 54, 43, 95, 12, 75, 2, 98, 65], 'cur_cost': 101357.0}, {'tour': [0, 6, 18, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 33761.0}, {'tour': [0, 13, 10, 19, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 2, 45, 28, 33, 82, 54, 6, 8, 56, 86, 50, 42], 'cur_cost': 33683.0}, {'tour': array([64,  0, 80, 40, 70, 79, 41, 34, 89, 67, 29, 66, 91, 32, 11, 77, 14,
       73, 71, 27, 22,  7, 38, 78, 20,  8, 46, 90, 58, 87, 47, 13,  9, 61,
       97, 26, 30, 99,  2, 98, 52, 56,  4, 31, 39, 57, 85, 23, 68, 45,  3,
       60, 82, 65, 33, 49,  5, 21, 37,  1, 76, 25, 53, 63, 42, 59, 55, 19,
       93, 16, 72, 51, 92, 54, 74, 81, 17, 94, 44, 18, 83, 24, 84, 50, 36,
        6, 96, 69, 88, 28, 75, 12, 62, 15, 35, 86, 10, 95, 48, 43],
      dtype=int64), 'cur_cost': 172385.0}, {'tour': [0, 14, 4, 3, 64, 65, 25, 69, 21, 15, 87, 93, 17, 23, 37, 35, 98, 83, 9, 71, 20, 73, 58, 16, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 78, 52, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 27, 92, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42, 66], 'cur_cost': 32630.0}]
2025-08-05 09:52:22,075 - ExploitationExpert - INFO - 局部搜索耗时: 0.03秒，最大迭代次数: 10
2025-08-05 09:52:22,075 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 381, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 381, 'cache_hits': 0, 'similarity_calculations': 1986, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:22,076 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([64,  0, 80, 40, 70, 79, 41, 34, 89, 67, 29, 66, 91, 32, 11, 77, 14,
       73, 71, 27, 22,  7, 38, 78, 20,  8, 46, 90, 58, 87, 47, 13,  9, 61,
       97, 26, 30, 99,  2, 98, 52, 56,  4, 31, 39, 57, 85, 23, 68, 45,  3,
       60, 82, 65, 33, 49,  5, 21, 37,  1, 76, 25, 53, 63, 42, 59, 55, 19,
       93, 16, 72, 51, 92, 54, 74, 81, 17, 94, 44, 18, 83, 24, 84, 50, 36,
        6, 96, 69, 88, 28, 75, 12, 62, 15, 35, 86, 10, 95, 48, 43],
      dtype=int64), 'cur_cost': 172385.0, 'intermediate_solutions': [{'tour': array([ 5,  9, 20, 34, 31, 89, 84, 32, 33, 79, 63, 65, 43, 85, 45, 92, 62,
       55, 73, 68, 71,  8, 10, 30,  3, 76, 39, 86, 52, 22, 12, 38,  6, 18,
       66,  2,  0, 29, 36, 83, 75, 53, 93, 47, 60, 15, 82, 81, 11, 44, 46,
       26, 48, 27, 95, 98, 97, 72, 56, 50, 94, 25, 42, 67, 70, 35, 96, 61,
       49, 80, 57, 28, 90, 14, 19, 59, 88, 21, 41,  7, 91, 78, 13,  4, 51,
       99, 74, 23, 40, 37, 69, 17, 54, 24, 64, 58,  1, 16, 77, 87]), 'cur_cost': 169633.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([34,  5,  9, 20, 31, 89, 84, 32, 33, 79, 63, 65, 43, 85, 45, 92, 62,
       55, 73, 68, 71,  8, 10, 30,  3, 76, 39, 86, 52, 22, 12, 38,  6, 18,
       66,  2,  0, 29, 36, 83, 75, 53, 93, 47, 60, 15, 82, 81, 11, 44, 46,
       26, 48, 27, 95, 98, 97, 72, 56, 50, 94, 25, 42, 67, 70, 35, 96, 61,
       49, 80, 57, 28, 90, 14, 19, 59, 88, 21, 41,  7, 91, 78, 13,  4, 51,
       99, 74, 23, 40, 37, 69, 17, 54, 24, 64, 58,  1, 16, 77, 87]), 'cur_cost': 170980.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([31, 34,  5,  9, 20, 89, 84, 32, 33, 79, 63, 65, 43, 85, 45, 92, 62,
       55, 73, 68, 71,  8, 10, 30,  3, 76, 39, 86, 52, 22, 12, 38,  6, 18,
       66,  2,  0, 29, 36, 83, 75, 53, 93, 47, 60, 15, 82, 81, 11, 44, 46,
       26, 48, 27, 95, 98, 97, 72, 56, 50, 94, 25, 42, 67, 70, 35, 96, 61,
       49, 80, 57, 28, 90, 14, 19, 59, 88, 21, 41,  7, 91, 78, 13,  4, 51,
       99, 74, 23, 40, 37, 69, 17, 54, 24, 64, 58,  1, 16, 77, 87]), 'cur_cost': 170249.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([20, 34,  5,  9, 31, 89, 84, 32, 33, 79, 63, 65, 43, 85, 45, 92, 62,
       55, 73, 68, 71,  8, 10, 30,  3, 76, 39, 86, 52, 22, 12, 38,  6, 18,
       66,  2,  0, 29, 36, 83, 75, 53, 93, 47, 60, 15, 82, 81, 11, 44, 46,
       26, 48, 27, 95, 98, 97, 72, 56, 50, 94, 25, 42, 67, 70, 35, 96, 61,
       49, 80, 57, 28, 90, 14, 19, 59, 88, 21, 41,  7, 91, 78, 13,  4, 51,
       99, 74, 23, 40, 37, 69, 17, 54, 24, 64, 58,  1, 16, 77, 87]), 'cur_cost': 170994.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([20, 31, 34,  5,  9, 89, 84, 32, 33, 79, 63, 65, 43, 85, 45, 92, 62,
       55, 73, 68, 71,  8, 10, 30,  3, 76, 39, 86, 52, 22, 12, 38,  6, 18,
       66,  2,  0, 29, 36, 83, 75, 53, 93, 47, 60, 15, 82, 81, 11, 44, 46,
       26, 48, 27, 95, 98, 97, 72, 56, 50, 94, 25, 42, 67, 70, 35, 96, 61,
       49, 80, 57, 28, 90, 14, 19, 59, 88, 21, 41,  7, 91, 78, 13,  4, 51,
       99, 74, 23, 40, 37, 69, 17, 54, 24, 64, 58,  1, 16, 77, 87]), 'cur_cost': 169523.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:22,076 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 172385.00)
2025-08-05 09:52:22,076 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:22,077 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:22,077 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:22,098 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 100
2025-08-05 09:52:22,098 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,099 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,099 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,099 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,100 - ExplorationExpert - INFO - 探索路径生成完成，成本: 104148.0, 路径长度: 100, 收集中间解: 3
2025-08-05 09:52:22,100 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [38, 72, 29, 76, 24, 28, 19, 2, 4, 39, 79, 74, 9, 64, 62, 23, 7, 63, 6, 40, 60, 66, 11, 49, 13, 94, 99, 34, 46, 91, 37, 92, 89, 31, 8, 68, 80, 44, 18, 15, 55, 65, 96, 69, 5, 97, 54, 1, 51, 36, 70, 22, 59, 27, 85, 33, 26, 14, 10, 16, 71, 3, 30, 53, 81, 77, 32, 42, 67, 90, 20, 61, 0, 48, 41, 21, 78, 58, 52, 73, 83, 17, 86, 56, 43, 95, 75, 12, 57, 84, 50, 82, 35, 93, 98, 25, 87, 88, 45, 47], 'cur_cost': 104148.0, 'intermediate_solutions': [{'tour': [0, 14, 4, 3, 64, 65, 25, 69, 21, 15, 87, 93, 17, 23, 63, 35, 98, 83, 9, 71, 20, 73, 58, 16, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 78, 52, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 27, 92, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 37, 67, 84, 38, 29, 95, 77, 51, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42, 66], 'cur_cost': 42059.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 4, 3, 64, 65, 25, 69, 21, 15, 87, 93, 17, 23, 37, 35, 98, 83, 9, 71, 20, 73, 58, 16, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 78, 52, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 27, 92, 76, 50, 86, 8, 6, 56, 19, 34, 85, 26, 11, 54, 82, 33, 28, 45, 2, 13, 40, 70, 99, 47, 81, 94, 12, 75, 32, 36, 51, 77, 95, 29, 38, 84, 67, 63, 39, 53, 1, 43, 49, 72, 68, 80, 24, 60, 57, 59, 61, 42, 66], 'cur_cost': 33679.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 4, 3, 64, 65, 25, 69, 21, 15, 87, 93, 17, 23, 37, 35, 98, 83, 9, 71, 20, 73, 58, 16, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 78, 52, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 27, 92, 57, 60, 24, 80, 68, 72, 49, 43, 39, 1, 53, 63, 67, 84, 38, 29, 95, 77, 51, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42, 66], 'cur_cost': 33114.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:22,100 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 104148.00)
2025-08-05 09:52:22,100 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:22,100 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:22,103 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [84, 81, 39, 80, 38, 11, 46, 66, 0, 92, 18, 71, 31, 78, 9, 30, 43, 19, 29, 75, 54, 57, 50, 13, 61, 82, 85, 16, 48, 7, 79, 24, 95, 70, 47, 99, 59, 33, 77, 32, 60, 63, 26, 62, 88, 87, 14, 97, 35, 83, 93, 65, 69, 89, 64, 41, 68, 49, 76, 28, 36, 27, 58, 44, 10, 22, 74, 23, 21, 3, 53, 45, 51, 2, 4, 40, 34, 73, 52, 25, 96, 17, 15, 91, 98, 86, 8, 6, 90, 37, 5, 56, 1, 67, 72, 12, 94, 42, 20, 55], 'cur_cost': 104938.0, 'intermediate_solutions': [{'tour': [0, 8, 11, 3, 64, 65, 25, 69, 21, 15, 87, 93, 17, 62, 37, 35, 98, 83, 9, 71, 20, 73, 58, 16, 14, 10, 31, 90, 97, 22, 44, 46, 23, 5, 48, 89, 78, 52, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 27, 92, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 6, 56, 86, 50, 19, 85, 26, 34, 61, 59, 76, 66, 42], 'cur_cost': 33825.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 11, 3, 64, 65, 25, 69, 21, 15, 87, 93, 17, 23, 37, 35, 98, 83, 9, 71, 20, 73, 58, 16, 14, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 78, 52, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 27, 92, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 76, 59, 61, 34, 26, 85, 19, 50, 86, 56, 6, 54, 82, 33, 66, 42], 'cur_cost': 32278.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 11, 3, 64, 65, 25, 69, 21, 15, 87, 93, 17, 23, 37, 35, 98, 83, 9, 71, 20, 73, 58, 16, 14, 10, 31, 90, 97, 22, 44, 80, 46, 62, 5, 48, 89, 78, 52, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 27, 92, 57, 60, 24, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 6, 56, 86, 50, 19, 85, 26, 34, 61, 59, 76, 66, 42], 'cur_cost': 33016.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 16, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 9, 83, 71, 20, 73, 58, 10, 14, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 61, 59, 76, 56, 6, 8, 86, 50, 42, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 25, 98], 'cur_cost': 32193.0, 'intermediate_solutions': [{'tour': [0, 14, 20, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 27, 45, 28, 33, 82, 54, 42, 61, 59, 76, 22, 97, 90, 44, 31, 10, 16, 73, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 2, 66, 57], 'cur_cost': 36059.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 20, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 42, 61, 59, 76, 22, 97, 90, 44, 31, 10, 16, 73, 58, 71, 37, 35, 83, 9, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57], 'cur_cost': 30042.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 20, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 42, 61, 59, 76, 22, 97, 90, 44, 31, 10, 16, 73, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57], 'cur_cost': 29358.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 18, 20, 73, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 48, 62, 89, 74, 25, 98, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 29422.0, 'intermediate_solutions': [{'tour': [62, 18, 17, 10, 14, 16, 31, 90, 97, 22, 44, 46, 0, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 30852.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 17, 10, 14, 16, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 42, 61], 'cur_cost': 30746.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 17, 10, 14, 16, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 25, 98, 92, 27, 66, 57, 12, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 33722.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([32,  3, 56, 42, 88, 45,  0, 17, 14, 67, 23, 37, 63,  5, 59, 11, 53,
       81, 52, 54, 35, 70, 55, 92, 75, 30, 69, 72, 33,  8, 76,  7, 43, 15,
       50, 65, 89, 28, 36, 58, 44, 21, 83, 74,  4, 98, 27, 60, 49, 51, 99,
       41, 12, 20, 34, 94,  9, 68, 57, 90, 16, 91, 86, 95, 82, 66, 40, 87,
       73, 71, 46, 84, 78, 80, 38, 47, 10, 29, 22,  2, 26, 13, 62, 97, 96,
       85, 19, 31, 24, 79, 48,  6, 64, 18, 39, 77, 93,  1, 61, 25],
      dtype=int64), 'cur_cost': 180861.0, 'intermediate_solutions': [{'tour': array([ 8, 63,  4, 10, 27, 76, 42, 17, 94, 34,  7, 81, 15, 53, 73, 20, 83,
       96, 95, 12, 93,  2, 52, 61, 48, 35, 77,  9, 66, 11, 51, 70, 60, 90,
       57, 32, 45, 89, 21, 44, 58, 86, 50, 38, 79, 37,  1, 56, 14, 88, 28,
       71, 19, 43, 39, 30, 47, 78, 92, 54, 99, 69, 36, 46, 23,  3, 59, 31,
       13, 64, 74, 67,  0, 97, 75, 84, 29, 87, 25, 49, 82, 85, 26, 80, 91,
        6, 98,  5, 22, 18, 72, 65, 24, 68, 55, 41, 62, 40, 33, 16]), 'cur_cost': 176987.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([10,  8, 63,  4, 27, 76, 42, 17, 94, 34,  7, 81, 15, 53, 73, 20, 83,
       96, 95, 12, 93,  2, 52, 61, 48, 35, 77,  9, 66, 11, 51, 70, 60, 90,
       57, 32, 45, 89, 21, 44, 58, 86, 50, 38, 79, 37,  1, 56, 14, 88, 28,
       71, 19, 43, 39, 30, 47, 78, 92, 54, 99, 69, 36, 46, 23,  3, 59, 31,
       13, 64, 74, 67,  0, 97, 75, 84, 29, 87, 25, 49, 82, 85, 26, 80, 91,
        6, 98,  5, 22, 18, 72, 65, 24, 68, 55, 41, 62, 40, 33, 16]), 'cur_cost': 175257.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([27, 10,  8, 63,  4, 76, 42, 17, 94, 34,  7, 81, 15, 53, 73, 20, 83,
       96, 95, 12, 93,  2, 52, 61, 48, 35, 77,  9, 66, 11, 51, 70, 60, 90,
       57, 32, 45, 89, 21, 44, 58, 86, 50, 38, 79, 37,  1, 56, 14, 88, 28,
       71, 19, 43, 39, 30, 47, 78, 92, 54, 99, 69, 36, 46, 23,  3, 59, 31,
       13, 64, 74, 67,  0, 97, 75, 84, 29, 87, 25, 49, 82, 85, 26, 80, 91,
        6, 98,  5, 22, 18, 72, 65, 24, 68, 55, 41, 62, 40, 33, 16]), 'cur_cost': 176385.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4, 10,  8, 63, 27, 76, 42, 17, 94, 34,  7, 81, 15, 53, 73, 20, 83,
       96, 95, 12, 93,  2, 52, 61, 48, 35, 77,  9, 66, 11, 51, 70, 60, 90,
       57, 32, 45, 89, 21, 44, 58, 86, 50, 38, 79, 37,  1, 56, 14, 88, 28,
       71, 19, 43, 39, 30, 47, 78, 92, 54, 99, 69, 36, 46, 23,  3, 59, 31,
       13, 64, 74, 67,  0, 97, 75, 84, 29, 87, 25, 49, 82, 85, 26, 80, 91,
        6, 98,  5, 22, 18, 72, 65, 24, 68, 55, 41, 62, 40, 33, 16]), 'cur_cost': 178540.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4, 27, 10,  8, 63, 76, 42, 17, 94, 34,  7, 81, 15, 53, 73, 20, 83,
       96, 95, 12, 93,  2, 52, 61, 48, 35, 77,  9, 66, 11, 51, 70, 60, 90,
       57, 32, 45, 89, 21, 44, 58, 86, 50, 38, 79, 37,  1, 56, 14, 88, 28,
       71, 19, 43, 39, 30, 47, 78, 92, 54, 99, 69, 36, 46, 23,  3, 59, 31,
       13, 64, 74, 67,  0, 97, 75, 84, 29, 87, 25, 49, 82, 85, 26, 80, 91,
        6, 98,  5, 22, 18, 72, 65, 24, 68, 55, 41, 62, 40, 33, 16]), 'cur_cost': 178384.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [1, 53, 12, 6, 44, 62, 64, 21, 52, 41, 74, 98, 15, 30, 72, 51, 33, 19, 11, 54, 2, 84, 99, 94, 32, 82, 60, 59, 86, 13, 28, 75, 76, 91, 23, 22, 61, 50, 10, 73, 78, 3, 31, 27, 66, 48, 89, 18, 83, 69, 71, 90, 16, 35, 87, 7, 65, 57, 67, 29, 40, 45, 42, 70, 8, 56, 34, 85, 80, 24, 4, 68, 0, 9, 58, 96, 46, 79, 20, 93, 39, 43, 95, 77, 49, 26, 36, 63, 38, 97, 92, 5, 55, 88, 25, 37, 17, 14, 47, 81], 'cur_cost': 96301.0, 'intermediate_solutions': [{'tour': [65, 79, 30, 62, 14, 76, 28, 43, 19, 44, 60, 49, 53, 39, 63, 95, 61, 86, 47, 36, 34, 13, 66, 59, 90, 20, 5, 52, 31, 98, 7, 17, 10, 87, 57, 88, 24, 68, 92, 96, 22, 83, 16, 35, 93, 97, 89, 3, 78, 21, 64, 71, 55, 18, 15, 0, 11, 8, 33, 40, 26, 67, 56, 80, 1, 4, 12, 2, 51, 99, 38, 75, 81, 72, 45, 6, 84, 70, 27, 58, 41, 25, 23, 91, 73, 37, 74, 50, 54, 77, 82, 46, 85, 42, 32, 29, 94, 48, 9, 69], 'cur_cost': 104076.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [65, 79, 30, 62, 14, 76, 28, 43, 19, 44, 60, 49, 53, 39, 6, 95, 61, 86, 47, 36, 34, 13, 66, 59, 90, 20, 5, 52, 31, 98, 7, 17, 10, 87, 57, 88, 24, 68, 92, 96, 22, 83, 16, 35, 93, 97, 89, 3, 78, 21, 64, 71, 55, 18, 15, 0, 11, 29, 32, 42, 85, 46, 82, 77, 54, 50, 74, 37, 73, 91, 23, 25, 41, 58, 27, 70, 84, 63, 45, 72, 81, 75, 38, 99, 51, 2, 12, 4, 1, 80, 56, 67, 26, 40, 33, 8, 94, 48, 9, 69], 'cur_cost': 107466.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [65, 79, 30, 62, 14, 76, 28, 43, 19, 44, 60, 49, 53, 39, 6, 95, 42, 61, 86, 47, 36, 34, 13, 66, 59, 90, 20, 5, 52, 31, 98, 7, 17, 10, 87, 57, 88, 24, 68, 92, 96, 22, 83, 16, 35, 93, 97, 89, 3, 78, 21, 64, 71, 55, 18, 15, 0, 11, 8, 33, 40, 26, 67, 56, 80, 1, 4, 12, 2, 51, 99, 38, 75, 81, 72, 45, 63, 84, 70, 27, 58, 41, 25, 23, 91, 73, 37, 74, 50, 54, 77, 82, 46, 85, 32, 29, 94, 48, 9, 69], 'cur_cost': 106069.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [83, 23, 91, 22, 11, 61, 58, 52, 73, 31, 44, 26, 72, 56, 34, 85, 27, 74, 71, 69, 3, 35, 48, 41, 88, 68, 33, 13, 45, 40, 47, 94, 8, 77, 19, 38, 39, 4, 81, 32, 1, 67, 36, 42, 29, 70, 6, 28, 63, 84, 24, 62, 10, 92, 80, 86, 60, 0, 78, 55, 46, 59, 5, 90, 17, 25, 37, 96, 9, 15, 16, 20, 64, 87, 66, 57, 49, 51, 82, 99, 50, 76, 14, 21, 93, 30, 18, 89, 53, 79, 97, 7, 54, 43, 95, 12, 75, 2, 98, 65], 'cur_cost': 101357.0, 'intermediate_solutions': [{'tour': [87, 91, 62, 27, 72, 54, 38, 94, 80, 49, 8, 82, 76, 34, 29, 26, 28, 4, 36, 84, 85, 56, 66, 90, 92, 46, 17, 79, 41, 68, 77, 40, 1, 45, 24, 70, 0, 7, 44, 11, 20, 59, 42, 51, 67, 32, 53, 75, 19, 71, 31, 23, 9, 52, 93, 55, 30, 83, 39, 60, 2, 97, 73, 22, 74, 25, 21, 65, 5, 96, 64, 48, 69, 89, 98, 78, 3, 14, 58, 16, 61, 63, 43, 81, 33, 86, 50, 95, 6, 47, 13, 12, 10, 35, 37, 18, 15, 88, 57, 99], 'cur_cost': 104471.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [87, 91, 62, 27, 72, 54, 38, 94, 80, 49, 8, 82, 76, 34, 29, 26, 28, 4, 36, 84, 85, 56, 66, 90, 92, 46, 17, 79, 41, 68, 77, 40, 1, 45, 24, 70, 0, 83, 44, 11, 20, 59, 42, 51, 67, 32, 53, 75, 19, 71, 31, 23, 9, 52, 93, 55, 30, 7, 39, 60, 2, 97, 73, 22, 74, 25, 21, 65, 5, 96, 64, 48, 69, 89, 98, 78, 3, 14, 58, 16, 61, 63, 43, 81, 33, 86, 50, 95, 6, 47, 13, 12, 15, 18, 37, 35, 10, 88, 57, 99], 'cur_cost': 103639.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [87, 91, 62, 27, 72, 54, 38, 94, 80, 49, 8, 82, 76, 34, 29, 26, 28, 4, 36, 84, 85, 56, 66, 90, 92, 46, 17, 79, 41, 68, 77, 40, 1, 45, 24, 70, 0, 83, 44, 11, 20, 59, 42, 51, 67, 32, 53, 75, 19, 71, 31, 23, 9, 52, 93, 55, 30, 7, 39, 60, 2, 97, 73, 22, 74, 25, 21, 65, 5, 96, 64, 48, 69, 89, 98, 78, 3, 14, 58, 16, 61, 63, 43, 81, 33, 86, 95, 6, 47, 13, 12, 10, 35, 50, 37, 18, 15, 88, 57, 99], 'cur_cost': 106568.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 18, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 33761.0, 'intermediate_solutions': [{'tour': [40, 43, 26, 95, 82, 29, 51, 60, 97, 19, 70, 6, 34, 84, 45, 75, 88, 57, 66, 18, 21, 15, 5, 69, 87, 17, 58, 52, 92, 50, 39, 68, 81, 77, 85, 59, 33, 49, 11, 27, 86, 38, 53, 30, 1, 36, 12, 63, 90, 71, 20, 91, 3, 93, 55, 62, 22, 10, 16, 98, 35, 9, 78, 14, 46, 79, 74, 76, 67, 2, 80, 24, 7, 44, 0, 73, 31, 96, 61, 48, 37, 56, 47, 99, 42, 72, 94, 28, 13, 4, 89, 65, 41, 83, 23, 64, 8, 54, 32, 25], 'cur_cost': 108511.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [40, 43, 26, 95, 82, 29, 51, 60, 97, 19, 70, 6, 34, 84, 45, 75, 88, 57, 66, 18, 21, 15, 5, 69, 87, 17, 58, 52, 92, 50, 39, 68, 81, 77, 85, 59, 33, 49, 11, 27, 86, 38, 53, 30, 1, 36, 12, 63, 90, 71, 20, 91, 3, 41, 55, 62, 22, 10, 16, 98, 35, 9, 78, 14, 46, 79, 74, 76, 67, 2, 80, 24, 7, 44, 0, 73, 31, 96, 61, 48, 37, 56, 89, 4, 13, 28, 94, 72, 42, 99, 47, 65, 93, 83, 23, 64, 8, 54, 32, 25], 'cur_cost': 110380.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [40, 43, 26, 95, 82, 29, 51, 60, 97, 19, 70, 6, 34, 84, 45, 75, 88, 57, 66, 18, 21, 15, 5, 69, 87, 17, 58, 52, 92, 50, 39, 68, 81, 77, 85, 59, 33, 49, 11, 27, 86, 38, 53, 30, 1, 36, 12, 63, 90, 71, 20, 91, 3, 41, 55, 62, 22, 10, 16, 98, 35, 78, 14, 46, 79, 74, 76, 67, 2, 80, 24, 7, 44, 0, 73, 31, 96, 61, 48, 37, 56, 47, 99, 9, 42, 72, 94, 28, 13, 4, 89, 65, 93, 83, 23, 64, 8, 54, 32, 25], 'cur_cost': 111919.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 10, 19, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 2, 45, 28, 33, 82, 54, 6, 8, 56, 86, 50, 42], 'cur_cost': 33683.0, 'intermediate_solutions': [{'tour': [58, 71, 44, 59, 11, 95, 40, 67, 28, 72, 1, 86, 43, 2, 45, 24, 97, 57, 7, 5, 88, 50, 46, 48, 64, 31, 17, 37, 18, 65, 55, 23, 73, 41, 9, 90, 82, 54, 74, 68, 34, 92, 63, 80, 38, 76, 62, 0, 20, 91, 10, 52, 16, 87, 21, 30, 96, 89, 3, 93, 36, 15, 83, 6, 29, 81, 99, 42, 77, 47, 33, 22, 8, 19, 39, 84, 53, 79, 49, 13, 51, 94, 26, 56, 61, 27, 85, 66, 60, 70, 12, 75, 4, 14, 35, 78, 69, 25, 98, 32], 'cur_cost': 111842.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [58, 71, 44, 59, 11, 95, 40, 67, 28, 72, 1, 86, 43, 2, 45, 24, 97, 57, 7, 5, 88, 50, 46, 48, 64, 31, 17, 37, 18, 65, 55, 23, 73, 41, 9, 90, 82, 54, 36, 68, 34, 92, 63, 80, 38, 76, 62, 0, 20, 91, 10, 52, 16, 87, 21, 30, 96, 89, 3, 93, 74, 15, 83, 6, 29, 81, 99, 42, 77, 47, 33, 22, 8, 19, 39, 84, 53, 79, 49, 13, 51, 94, 26, 56, 61, 27, 85, 66, 60, 70, 12, 75, 4, 32, 98, 25, 69, 78, 35, 14], 'cur_cost': 98944.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [58, 71, 44, 59, 11, 95, 40, 67, 28, 72, 1, 86, 43, 2, 45, 24, 97, 57, 7, 5, 88, 50, 46, 48, 64, 31, 17, 37, 18, 65, 55, 23, 73, 41, 9, 90, 82, 54, 36, 68, 34, 92, 63, 80, 38, 76, 62, 0, 20, 91, 10, 52, 16, 87, 21, 30, 96, 89, 3, 93, 74, 15, 83, 6, 29, 81, 99, 42, 77, 47, 33, 22, 8, 19, 39, 84, 53, 79, 49, 66, 13, 51, 94, 26, 56, 61, 27, 85, 60, 70, 12, 75, 4, 14, 35, 78, 69, 25, 98, 32], 'cur_cost': 105229.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([64,  0, 80, 40, 70, 79, 41, 34, 89, 67, 29, 66, 91, 32, 11, 77, 14,
       73, 71, 27, 22,  7, 38, 78, 20,  8, 46, 90, 58, 87, 47, 13,  9, 61,
       97, 26, 30, 99,  2, 98, 52, 56,  4, 31, 39, 57, 85, 23, 68, 45,  3,
       60, 82, 65, 33, 49,  5, 21, 37,  1, 76, 25, 53, 63, 42, 59, 55, 19,
       93, 16, 72, 51, 92, 54, 74, 81, 17, 94, 44, 18, 83, 24, 84, 50, 36,
        6, 96, 69, 88, 28, 75, 12, 62, 15, 35, 86, 10, 95, 48, 43],
      dtype=int64), 'cur_cost': 172385.0, 'intermediate_solutions': [{'tour': array([ 5,  9, 20, 34, 31, 89, 84, 32, 33, 79, 63, 65, 43, 85, 45, 92, 62,
       55, 73, 68, 71,  8, 10, 30,  3, 76, 39, 86, 52, 22, 12, 38,  6, 18,
       66,  2,  0, 29, 36, 83, 75, 53, 93, 47, 60, 15, 82, 81, 11, 44, 46,
       26, 48, 27, 95, 98, 97, 72, 56, 50, 94, 25, 42, 67, 70, 35, 96, 61,
       49, 80, 57, 28, 90, 14, 19, 59, 88, 21, 41,  7, 91, 78, 13,  4, 51,
       99, 74, 23, 40, 37, 69, 17, 54, 24, 64, 58,  1, 16, 77, 87]), 'cur_cost': 169633.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([34,  5,  9, 20, 31, 89, 84, 32, 33, 79, 63, 65, 43, 85, 45, 92, 62,
       55, 73, 68, 71,  8, 10, 30,  3, 76, 39, 86, 52, 22, 12, 38,  6, 18,
       66,  2,  0, 29, 36, 83, 75, 53, 93, 47, 60, 15, 82, 81, 11, 44, 46,
       26, 48, 27, 95, 98, 97, 72, 56, 50, 94, 25, 42, 67, 70, 35, 96, 61,
       49, 80, 57, 28, 90, 14, 19, 59, 88, 21, 41,  7, 91, 78, 13,  4, 51,
       99, 74, 23, 40, 37, 69, 17, 54, 24, 64, 58,  1, 16, 77, 87]), 'cur_cost': 170980.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([31, 34,  5,  9, 20, 89, 84, 32, 33, 79, 63, 65, 43, 85, 45, 92, 62,
       55, 73, 68, 71,  8, 10, 30,  3, 76, 39, 86, 52, 22, 12, 38,  6, 18,
       66,  2,  0, 29, 36, 83, 75, 53, 93, 47, 60, 15, 82, 81, 11, 44, 46,
       26, 48, 27, 95, 98, 97, 72, 56, 50, 94, 25, 42, 67, 70, 35, 96, 61,
       49, 80, 57, 28, 90, 14, 19, 59, 88, 21, 41,  7, 91, 78, 13,  4, 51,
       99, 74, 23, 40, 37, 69, 17, 54, 24, 64, 58,  1, 16, 77, 87]), 'cur_cost': 170249.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([20, 34,  5,  9, 31, 89, 84, 32, 33, 79, 63, 65, 43, 85, 45, 92, 62,
       55, 73, 68, 71,  8, 10, 30,  3, 76, 39, 86, 52, 22, 12, 38,  6, 18,
       66,  2,  0, 29, 36, 83, 75, 53, 93, 47, 60, 15, 82, 81, 11, 44, 46,
       26, 48, 27, 95, 98, 97, 72, 56, 50, 94, 25, 42, 67, 70, 35, 96, 61,
       49, 80, 57, 28, 90, 14, 19, 59, 88, 21, 41,  7, 91, 78, 13,  4, 51,
       99, 74, 23, 40, 37, 69, 17, 54, 24, 64, 58,  1, 16, 77, 87]), 'cur_cost': 170994.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([20, 31, 34,  5,  9, 89, 84, 32, 33, 79, 63, 65, 43, 85, 45, 92, 62,
       55, 73, 68, 71,  8, 10, 30,  3, 76, 39, 86, 52, 22, 12, 38,  6, 18,
       66,  2,  0, 29, 36, 83, 75, 53, 93, 47, 60, 15, 82, 81, 11, 44, 46,
       26, 48, 27, 95, 98, 97, 72, 56, 50, 94, 25, 42, 67, 70, 35, 96, 61,
       49, 80, 57, 28, 90, 14, 19, 59, 88, 21, 41,  7, 91, 78, 13,  4, 51,
       99, 74, 23, 40, 37, 69, 17, 54, 24, 64, 58,  1, 16, 77, 87]), 'cur_cost': 169523.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [38, 72, 29, 76, 24, 28, 19, 2, 4, 39, 79, 74, 9, 64, 62, 23, 7, 63, 6, 40, 60, 66, 11, 49, 13, 94, 99, 34, 46, 91, 37, 92, 89, 31, 8, 68, 80, 44, 18, 15, 55, 65, 96, 69, 5, 97, 54, 1, 51, 36, 70, 22, 59, 27, 85, 33, 26, 14, 10, 16, 71, 3, 30, 53, 81, 77, 32, 42, 67, 90, 20, 61, 0, 48, 41, 21, 78, 58, 52, 73, 83, 17, 86, 56, 43, 95, 75, 12, 57, 84, 50, 82, 35, 93, 98, 25, 87, 88, 45, 47], 'cur_cost': 104148.0, 'intermediate_solutions': [{'tour': [0, 14, 4, 3, 64, 65, 25, 69, 21, 15, 87, 93, 17, 23, 63, 35, 98, 83, 9, 71, 20, 73, 58, 16, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 78, 52, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 27, 92, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 37, 67, 84, 38, 29, 95, 77, 51, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42, 66], 'cur_cost': 42059.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 4, 3, 64, 65, 25, 69, 21, 15, 87, 93, 17, 23, 37, 35, 98, 83, 9, 71, 20, 73, 58, 16, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 78, 52, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 27, 92, 76, 50, 86, 8, 6, 56, 19, 34, 85, 26, 11, 54, 82, 33, 28, 45, 2, 13, 40, 70, 99, 47, 81, 94, 12, 75, 32, 36, 51, 77, 95, 29, 38, 84, 67, 63, 39, 53, 1, 43, 49, 72, 68, 80, 24, 60, 57, 59, 61, 42, 66], 'cur_cost': 33679.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 4, 3, 64, 65, 25, 69, 21, 15, 87, 93, 17, 23, 37, 35, 98, 83, 9, 71, 20, 73, 58, 16, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 78, 52, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 27, 92, 57, 60, 24, 80, 68, 72, 49, 43, 39, 1, 53, 63, 67, 84, 38, 29, 95, 77, 51, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42, 66], 'cur_cost': 33114.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:22,103 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:22,103 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:22,109 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=29422.000, 多样性=0.990
2025-08-05 09:52:22,109 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 09:52:22,109 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 09:52:22,109 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:22,110 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.007385528749130407, 'best_improvement': -0.002179985012603038}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.011123723041997447}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.07404445439750945, 'recent_improvements': [-0.058065781505154235, -0.030883518161605894, 0.09002312728986465], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 7, 'new_count': 7, 'count_change': 0, 'old_best_cost': 21389, 'new_best_cost': 21389, 'quality_improvement': 0.0, 'old_diversity': 0.919047619047619, 'new_diversity': 0.919047619047619, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 09:52:22,111 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 09:52:22,111 - __main__ - INFO - kroA100 开始进化第 3 代
2025-08-05 09:52:22,111 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 09:52:22,112 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:22,113 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=29422.000, 多样性=0.990
2025-08-05 09:52:22,113 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:22,118 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.990
2025-08-05 09:52:22,118 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:22,121 - EliteExpert - INFO - 精英解分析完成: 精英解数量=7, 多样性=0.919
2025-08-05 09:52:22,124 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 09:52:22,124 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:22,124 - LandscapeExpert - INFO - 添加精英解数据: 7个精英解
2025-08-05 09:52:22,124 - LandscapeExpert - INFO - 数据提取成功: 17个路径, 17个适应度值
2025-08-05 09:52:22,202 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.118, 适应度梯度: -9273.635, 聚类评分: 0.000, 覆盖率: 0.168, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:22,202 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 09:52:22,202 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:22,202 - visualization.landscape_visualizer - INFO - 设置当前实例名: kroA100
2025-08-05 09:52:22,208 - visualization.landscape_visualizer - INFO - 插值约束: 208 个点被约束到最小值 21389.00
2025-08-05 09:52:22,322 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_kroA100\landscape_kroA100_iter_148_20250805_095222.html
2025-08-05 09:52:22,381 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_kroA100\dashboard_kroA100_iter_148_20250805_095222.html
2025-08-05 09:52:22,381 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 148
2025-08-05 09:52:22,381 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 09:52:22,381 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2570秒
2025-08-05 09:52:22,381 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.11764705882352941, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -9273.635294117645, 'local_optima_density': 0.11764705882352941, 'gradient_variance': 2531290407.831696, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1676, 'fitness_entropy': 0.7258303821788832, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -9273.635)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.168)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358742.2026532, 'performance_metrics': {}}}
2025-08-05 09:52:22,381 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:22,381 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:22,382 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:22,382 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:22,382 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:52:22,383 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 09:52:22,383 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:52:22,383 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:22,383 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:22,383 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:52:22,383 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:22,383 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:22,383 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:22,383 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-05 09:52:22,383 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:22,383 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:22,385 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 186346.0
2025-08-05 09:52:22,404 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:22,404 - ExploitationExpert - INFO - res_population_costs: [21389, 21411, 21776, 22282, 22308, 22356, 22427.0]
2025-08-05 09:52:22,405 - ExploitationExpert - INFO - res_populations: [array([ 0, 62, 89, 48,  5, 91,  7, 41, 88, 30, 79, 55, 96, 74, 18,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26,
       11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99, 47,
       51, 77, 95,  4, 36, 32, 75, 12, 94, 81, 38, 29, 84, 67, 72, 49, 43,
        1, 53, 39, 63, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 62,  5, 48, 89, 18, 74, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 59, 61, 76, 50, 86,  8,
        6, 56, 19, 85, 34, 26, 11, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40,
       99, 47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81, 49, 43,  1,
       53, 39, 63, 68, 72, 67, 84, 80, 24, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 91, 74, 96, 55, 79, 30, 88, 41,  7, 66, 27, 92, 57, 60, 24, 80,
       84, 67, 72, 68, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75, 32, 36,  4,
       51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28, 33, 82, 54,
       11, 26, 34, 85, 19, 56,  6,  8, 86, 50, 76, 61, 59, 22, 97, 90, 44,
       31, 10, 14, 16, 58, 73, 20, 71,  9, 83, 35, 98, 37, 23, 17, 78, 52,
       87, 15, 93, 21, 69, 65, 25, 64,  3, 18, 89, 48,  5, 62, 46],
      dtype=int64), array([ 0, 46, 92, 27, 66, 57, 68, 63, 39, 53,  1, 43, 72, 49, 81, 94, 12,
       75, 32, 36,  4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45,
       28, 33, 82, 54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 80, 24, 60,
       50, 86, 76, 61, 59, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20, 71,
        9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3, 18,
       52, 78, 89, 74, 96, 55, 79, 30, 88, 41,  7, 91, 48,  5, 62],
      dtype=int64), array([ 0, 62, 46, 92, 27, 57, 66, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75,
       32, 36,  4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28,
       33, 82, 54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 72, 68, 80, 24,
       60, 50, 86, 76, 61, 59, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20,
       71,  9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3,
       18, 52, 78, 89, 48,  5, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 62,  5, 48, 91,  7, 41, 88, 30, 79, 55, 96, 74, 89, 78, 52, 18,
        3, 64, 25, 65, 69, 21, 15, 87, 93, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 59, 61, 76, 86, 50, 60,
       24, 80, 68, 72, 67, 84,  8,  6, 56, 19, 85, 34, 26, 11, 54, 82, 33,
       28, 45, 42,  2, 13, 70, 40, 99, 47, 29, 38, 95, 77, 51,  4, 36, 32,
       75, 12, 94, 81, 49, 43,  1, 53, 39, 63, 66, 57, 27, 92, 46],
      dtype=int64), array([ 0, 92, 27, 57, 66, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75, 32, 36,
        4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28, 33, 82,
       54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 72, 68, 80, 24, 60, 50,
       86, 76, 61, 59, 22, 97, 44, 90, 46, 31, 10, 14, 16, 58, 73, 20, 71,
        9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3, 18,
       52, 78, 89, 74, 96, 55, 79, 30, 88, 41,  7, 91, 48,  5, 62],
      dtype=int64)]
2025-08-05 09:52:22,410 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:22,410 - ExploitationExpert - INFO - populations: [{'tour': array([33, 87, 26,  4, 92, 64, 39, 29, 25, 78, 36, 41, 94, 35, 38, 90, 72,
       66, 30, 85, 32,  2, 73, 76, 11, 88, 96, 97, 83,  9, 51, 68,  7, 91,
        3, 13,  1, 67, 77, 52, 84, 54, 61, 95, 86, 37, 59, 12, 44, 31, 71,
       56,  0, 17, 28, 49, 19, 74, 75, 89, 21, 55, 15, 43, 16, 46, 80, 98,
       48, 53, 60,  8, 40, 23, 82, 10,  6, 20, 70, 62, 34, 24, 47, 57, 14,
       18, 42, 69, 79, 45, 58, 63, 65, 27, 50, 93, 99,  5, 22, 81],
      dtype=int64), 'cur_cost': 186346.0}, {'tour': [0, 19, 16, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 9, 83, 71, 20, 73, 58, 10, 14, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 61, 59, 76, 56, 6, 8, 86, 50, 42, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 25, 98], 'cur_cost': 32193.0}, {'tour': [0, 5, 18, 20, 73, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 48, 62, 89, 74, 25, 98, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 29422.0}, {'tour': [32, 3, 56, 42, 88, 45, 0, 17, 14, 67, 23, 37, 63, 5, 59, 11, 53, 81, 52, 54, 35, 70, 55, 92, 75, 30, 69, 72, 33, 8, 76, 7, 43, 15, 50, 65, 89, 28, 36, 58, 44, 21, 83, 74, 4, 98, 27, 60, 49, 51, 99, 41, 12, 20, 34, 94, 9, 68, 57, 90, 16, 91, 86, 95, 82, 66, 40, 87, 73, 71, 46, 84, 78, 80, 38, 47, 10, 29, 22, 2, 26, 13, 62, 97, 96, 85, 19, 31, 24, 79, 48, 6, 64, 18, 39, 77, 93, 1, 61, 25], 'cur_cost': 180861.0}, {'tour': [1, 53, 12, 6, 44, 62, 64, 21, 52, 41, 74, 98, 15, 30, 72, 51, 33, 19, 11, 54, 2, 84, 99, 94, 32, 82, 60, 59, 86, 13, 28, 75, 76, 91, 23, 22, 61, 50, 10, 73, 78, 3, 31, 27, 66, 48, 89, 18, 83, 69, 71, 90, 16, 35, 87, 7, 65, 57, 67, 29, 40, 45, 42, 70, 8, 56, 34, 85, 80, 24, 4, 68, 0, 9, 58, 96, 46, 79, 20, 93, 39, 43, 95, 77, 49, 26, 36, 63, 38, 97, 92, 5, 55, 88, 25, 37, 17, 14, 47, 81], 'cur_cost': 96301.0}, {'tour': [83, 23, 91, 22, 11, 61, 58, 52, 73, 31, 44, 26, 72, 56, 34, 85, 27, 74, 71, 69, 3, 35, 48, 41, 88, 68, 33, 13, 45, 40, 47, 94, 8, 77, 19, 38, 39, 4, 81, 32, 1, 67, 36, 42, 29, 70, 6, 28, 63, 84, 24, 62, 10, 92, 80, 86, 60, 0, 78, 55, 46, 59, 5, 90, 17, 25, 37, 96, 9, 15, 16, 20, 64, 87, 66, 57, 49, 51, 82, 99, 50, 76, 14, 21, 93, 30, 18, 89, 53, 79, 97, 7, 54, 43, 95, 12, 75, 2, 98, 65], 'cur_cost': 101357.0}, {'tour': [0, 6, 18, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 33761.0}, {'tour': [0, 13, 10, 19, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 2, 45, 28, 33, 82, 54, 6, 8, 56, 86, 50, 42], 'cur_cost': 33683.0}, {'tour': [64, 0, 80, 40, 70, 79, 41, 34, 89, 67, 29, 66, 91, 32, 11, 77, 14, 73, 71, 27, 22, 7, 38, 78, 20, 8, 46, 90, 58, 87, 47, 13, 9, 61, 97, 26, 30, 99, 2, 98, 52, 56, 4, 31, 39, 57, 85, 23, 68, 45, 3, 60, 82, 65, 33, 49, 5, 21, 37, 1, 76, 25, 53, 63, 42, 59, 55, 19, 93, 16, 72, 51, 92, 54, 74, 81, 17, 94, 44, 18, 83, 24, 84, 50, 36, 6, 96, 69, 88, 28, 75, 12, 62, 15, 35, 86, 10, 95, 48, 43], 'cur_cost': 172385.0}, {'tour': [38, 72, 29, 76, 24, 28, 19, 2, 4, 39, 79, 74, 9, 64, 62, 23, 7, 63, 6, 40, 60, 66, 11, 49, 13, 94, 99, 34, 46, 91, 37, 92, 89, 31, 8, 68, 80, 44, 18, 15, 55, 65, 96, 69, 5, 97, 54, 1, 51, 36, 70, 22, 59, 27, 85, 33, 26, 14, 10, 16, 71, 3, 30, 53, 81, 77, 32, 42, 67, 90, 20, 61, 0, 48, 41, 21, 78, 58, 52, 73, 83, 17, 86, 56, 43, 95, 75, 12, 57, 84, 50, 82, 35, 93, 98, 25, 87, 88, 45, 47], 'cur_cost': 104148.0}]
2025-08-05 09:52:22,413 - ExploitationExpert - INFO - 局部搜索耗时: 0.03秒，最大迭代次数: 10
2025-08-05 09:52:22,413 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 382, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 382, 'cache_hits': 0, 'similarity_calculations': 1990, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:22,415 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([33, 87, 26,  4, 92, 64, 39, 29, 25, 78, 36, 41, 94, 35, 38, 90, 72,
       66, 30, 85, 32,  2, 73, 76, 11, 88, 96, 97, 83,  9, 51, 68,  7, 91,
        3, 13,  1, 67, 77, 52, 84, 54, 61, 95, 86, 37, 59, 12, 44, 31, 71,
       56,  0, 17, 28, 49, 19, 74, 75, 89, 21, 55, 15, 43, 16, 46, 80, 98,
       48, 53, 60,  8, 40, 23, 82, 10,  6, 20, 70, 62, 34, 24, 47, 57, 14,
       18, 42, 69, 79, 45, 58, 63, 65, 27, 50, 93, 99,  5, 22, 81],
      dtype=int64), 'cur_cost': 186346.0, 'intermediate_solutions': [{'tour': array([39, 81, 84, 80, 38, 11, 46, 66,  0, 92, 18, 71, 31, 78,  9, 30, 43,
       19, 29, 75, 54, 57, 50, 13, 61, 82, 85, 16, 48,  7, 79, 24, 95, 70,
       47, 99, 59, 33, 77, 32, 60, 63, 26, 62, 88, 87, 14, 97, 35, 83, 93,
       65, 69, 89, 64, 41, 68, 49, 76, 28, 36, 27, 58, 44, 10, 22, 74, 23,
       21,  3, 53, 45, 51,  2,  4, 40, 34, 73, 52, 25, 96, 17, 15, 91, 98,
       86,  8,  6, 90, 37,  5, 56,  1, 67, 72, 12, 94, 42, 20, 55]), 'cur_cost': 104128.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([80, 39, 81, 84, 38, 11, 46, 66,  0, 92, 18, 71, 31, 78,  9, 30, 43,
       19, 29, 75, 54, 57, 50, 13, 61, 82, 85, 16, 48,  7, 79, 24, 95, 70,
       47, 99, 59, 33, 77, 32, 60, 63, 26, 62, 88, 87, 14, 97, 35, 83, 93,
       65, 69, 89, 64, 41, 68, 49, 76, 28, 36, 27, 58, 44, 10, 22, 74, 23,
       21,  3, 53, 45, 51,  2,  4, 40, 34, 73, 52, 25, 96, 17, 15, 91, 98,
       86,  8,  6, 90, 37,  5, 56,  1, 67, 72, 12, 94, 42, 20, 55]), 'cur_cost': 103944.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([38, 80, 39, 81, 84, 11, 46, 66,  0, 92, 18, 71, 31, 78,  9, 30, 43,
       19, 29, 75, 54, 57, 50, 13, 61, 82, 85, 16, 48,  7, 79, 24, 95, 70,
       47, 99, 59, 33, 77, 32, 60, 63, 26, 62, 88, 87, 14, 97, 35, 83, 93,
       65, 69, 89, 64, 41, 68, 49, 76, 28, 36, 27, 58, 44, 10, 22, 74, 23,
       21,  3, 53, 45, 51,  2,  4, 40, 34, 73, 52, 25, 96, 17, 15, 91, 98,
       86,  8,  6, 90, 37,  5, 56,  1, 67, 72, 12, 94, 42, 20, 55]), 'cur_cost': 105023.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([84, 80, 39, 81, 38, 11, 46, 66,  0, 92, 18, 71, 31, 78,  9, 30, 43,
       19, 29, 75, 54, 57, 50, 13, 61, 82, 85, 16, 48,  7, 79, 24, 95, 70,
       47, 99, 59, 33, 77, 32, 60, 63, 26, 62, 88, 87, 14, 97, 35, 83, 93,
       65, 69, 89, 64, 41, 68, 49, 76, 28, 36, 27, 58, 44, 10, 22, 74, 23,
       21,  3, 53, 45, 51,  2,  4, 40, 34, 73, 52, 25, 96, 17, 15, 91, 98,
       86,  8,  6, 90, 37,  5, 56,  1, 67, 72, 12, 94, 42, 20, 55]), 'cur_cost': 104448.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([84, 38, 80, 39, 81, 11, 46, 66,  0, 92, 18, 71, 31, 78,  9, 30, 43,
       19, 29, 75, 54, 57, 50, 13, 61, 82, 85, 16, 48,  7, 79, 24, 95, 70,
       47, 99, 59, 33, 77, 32, 60, 63, 26, 62, 88, 87, 14, 97, 35, 83, 93,
       65, 69, 89, 64, 41, 68, 49, 76, 28, 36, 27, 58, 44, 10, 22, 74, 23,
       21,  3, 53, 45, 51,  2,  4, 40, 34, 73, 52, 25, 96, 17, 15, 91, 98,
       86,  8,  6, 90, 37,  5, 56,  1, 67, 72, 12, 94, 42, 20, 55]), 'cur_cost': 105054.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:22,415 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 186346.00)
2025-08-05 09:52:22,415 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:22,416 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:22,416 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:22,431 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 100
2025-08-05 09:52:22,431 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,431 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,431 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,431 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,432 - ExplorationExpert - INFO - 探索路径生成完成，成本: 102636.0, 路径长度: 100, 收集中间解: 3
2025-08-05 09:52:22,432 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [85, 22, 66, 50, 77, 38, 6, 46, 37, 30, 17, 90, 48, 14, 23, 10, 97, 68, 76, 60, 0, 7, 79, 83, 58, 27, 86, 80, 34, 42, 28, 13, 2, 63, 75, 54, 53, 94, 51, 82, 43, 41, 3, 93, 65, 15, 88, 25, 87, 57, 74, 21, 71, 55, 1, 49, 92, 89, 96, 5, 18, 64, 39, 32, 84, 95, 36, 99, 11, 67, 12, 19, 26, 40, 70, 59, 24, 33, 29, 8, 47, 72, 61, 44, 31, 91, 78, 62, 35, 98, 9, 20, 73, 16, 52, 56, 45, 4, 81, 69], 'cur_cost': 102636.0, 'intermediate_solutions': [{'tour': [0, 19, 16, 18, 74, 96, 55, 79, 30, 88, 41, 7, 8, 5, 48, 62, 89, 9, 83, 71, 20, 73, 58, 10, 14, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 61, 59, 76, 56, 6, 91, 86, 50, 42, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 25, 98], 'cur_cost': 38133.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 16, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 9, 83, 71, 20, 73, 58, 10, 14, 17, 23, 37, 35, 42, 50, 86, 8, 6, 56, 76, 59, 61, 34, 85, 26, 11, 54, 82, 33, 28, 45, 2, 13, 40, 70, 99, 47, 81, 94, 12, 75, 32, 36, 4, 51, 77, 95, 29, 38, 84, 67, 63, 39, 53, 1, 43, 49, 72, 68, 80, 24, 60, 57, 66, 27, 92, 46, 44, 22, 97, 90, 31, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 25, 98], 'cur_cost': 34150.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 16, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 9, 83, 71, 20, 73, 58, 10, 14, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 61, 59, 76, 56, 6, 8, 86, 50, 42, 35, 37, 23, 17, 78, 67, 52, 87, 15, 21, 93, 69, 65, 64, 3, 25, 98], 'cur_cost': 37209.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:22,432 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 102636.00)
2025-08-05 09:52:22,433 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:22,433 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:22,433 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:22,448 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 100
2025-08-05 09:52:22,448 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,449 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,449 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,451 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,451 - ExplorationExpert - INFO - 探索路径生成完成，成本: 96883.0, 路径长度: 100, 收集中间解: 3
2025-08-05 09:52:22,451 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [66, 68, 85, 92, 55, 30, 91, 97, 34, 80, 2, 38, 50, 27, 26, 8, 53, 41, 89, 22, 7, 24, 77, 61, 86, 67, 39, 94, 57, 19, 72, 60, 42, 47, 6, 63, 45, 33, 56, 76, 83, 14, 9, 71, 17, 96, 23, 20, 5, 74, 3, 35, 16, 54, 59, 29, 99, 75, 90, 0, 62, 64, 46, 98, 73, 10, 48, 44, 79, 87, 58, 21, 15, 78, 37, 69, 93, 65, 49, 1, 51, 70, 40, 11, 82, 43, 36, 32, 12, 4, 81, 95, 84, 88, 25, 52, 18, 31, 28, 13], 'cur_cost': 96883.0, 'intermediate_solutions': [{'tour': [0, 5, 18, 23, 73, 58, 71, 9, 83, 35, 37, 20, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 48, 62, 89, 74, 25, 98, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 31039.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 18, 20, 73, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 48, 62, 89, 74, 25, 98, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 13, 40, 70, 99, 47, 81, 94, 12, 75, 32, 36, 4, 51, 77, 95, 29, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 30273.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 18, 20, 73, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 48, 62, 89, 74, 25, 98, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 11, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 32488.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:22,451 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 96883.00)
2025-08-05 09:52:22,451 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:52:22,451 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:22,451 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:22,451 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 165064.0
2025-08-05 09:52:22,468 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:22,469 - ExploitationExpert - INFO - res_population_costs: [21389, 21411, 21776, 22282, 22308, 22356, 22427.0]
2025-08-05 09:52:22,469 - ExploitationExpert - INFO - res_populations: [array([ 0, 62, 89, 48,  5, 91,  7, 41, 88, 30, 79, 55, 96, 74, 18,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26,
       11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99, 47,
       51, 77, 95,  4, 36, 32, 75, 12, 94, 81, 38, 29, 84, 67, 72, 49, 43,
        1, 53, 39, 63, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 62,  5, 48, 89, 18, 74, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 59, 61, 76, 50, 86,  8,
        6, 56, 19, 85, 34, 26, 11, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40,
       99, 47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81, 49, 43,  1,
       53, 39, 63, 68, 72, 67, 84, 80, 24, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 91, 74, 96, 55, 79, 30, 88, 41,  7, 66, 27, 92, 57, 60, 24, 80,
       84, 67, 72, 68, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75, 32, 36,  4,
       51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28, 33, 82, 54,
       11, 26, 34, 85, 19, 56,  6,  8, 86, 50, 76, 61, 59, 22, 97, 90, 44,
       31, 10, 14, 16, 58, 73, 20, 71,  9, 83, 35, 98, 37, 23, 17, 78, 52,
       87, 15, 93, 21, 69, 65, 25, 64,  3, 18, 89, 48,  5, 62, 46],
      dtype=int64), array([ 0, 46, 92, 27, 66, 57, 68, 63, 39, 53,  1, 43, 72, 49, 81, 94, 12,
       75, 32, 36,  4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45,
       28, 33, 82, 54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 80, 24, 60,
       50, 86, 76, 61, 59, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20, 71,
        9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3, 18,
       52, 78, 89, 74, 96, 55, 79, 30, 88, 41,  7, 91, 48,  5, 62],
      dtype=int64), array([ 0, 62, 46, 92, 27, 57, 66, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75,
       32, 36,  4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28,
       33, 82, 54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 72, 68, 80, 24,
       60, 50, 86, 76, 61, 59, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20,
       71,  9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3,
       18, 52, 78, 89, 48,  5, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 62,  5, 48, 91,  7, 41, 88, 30, 79, 55, 96, 74, 89, 78, 52, 18,
        3, 64, 25, 65, 69, 21, 15, 87, 93, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 59, 61, 76, 86, 50, 60,
       24, 80, 68, 72, 67, 84,  8,  6, 56, 19, 85, 34, 26, 11, 54, 82, 33,
       28, 45, 42,  2, 13, 70, 40, 99, 47, 29, 38, 95, 77, 51,  4, 36, 32,
       75, 12, 94, 81, 49, 43,  1, 53, 39, 63, 66, 57, 27, 92, 46],
      dtype=int64), array([ 0, 92, 27, 57, 66, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75, 32, 36,
        4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28, 33, 82,
       54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 72, 68, 80, 24, 60, 50,
       86, 76, 61, 59, 22, 97, 44, 90, 46, 31, 10, 14, 16, 58, 73, 20, 71,
        9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3, 18,
       52, 78, 89, 74, 96, 55, 79, 30, 88, 41,  7, 91, 48,  5, 62],
      dtype=int64)]
2025-08-05 09:52:22,472 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:22,473 - ExploitationExpert - INFO - populations: [{'tour': array([33, 87, 26,  4, 92, 64, 39, 29, 25, 78, 36, 41, 94, 35, 38, 90, 72,
       66, 30, 85, 32,  2, 73, 76, 11, 88, 96, 97, 83,  9, 51, 68,  7, 91,
        3, 13,  1, 67, 77, 52, 84, 54, 61, 95, 86, 37, 59, 12, 44, 31, 71,
       56,  0, 17, 28, 49, 19, 74, 75, 89, 21, 55, 15, 43, 16, 46, 80, 98,
       48, 53, 60,  8, 40, 23, 82, 10,  6, 20, 70, 62, 34, 24, 47, 57, 14,
       18, 42, 69, 79, 45, 58, 63, 65, 27, 50, 93, 99,  5, 22, 81],
      dtype=int64), 'cur_cost': 186346.0}, {'tour': [85, 22, 66, 50, 77, 38, 6, 46, 37, 30, 17, 90, 48, 14, 23, 10, 97, 68, 76, 60, 0, 7, 79, 83, 58, 27, 86, 80, 34, 42, 28, 13, 2, 63, 75, 54, 53, 94, 51, 82, 43, 41, 3, 93, 65, 15, 88, 25, 87, 57, 74, 21, 71, 55, 1, 49, 92, 89, 96, 5, 18, 64, 39, 32, 84, 95, 36, 99, 11, 67, 12, 19, 26, 40, 70, 59, 24, 33, 29, 8, 47, 72, 61, 44, 31, 91, 78, 62, 35, 98, 9, 20, 73, 16, 52, 56, 45, 4, 81, 69], 'cur_cost': 102636.0}, {'tour': [66, 68, 85, 92, 55, 30, 91, 97, 34, 80, 2, 38, 50, 27, 26, 8, 53, 41, 89, 22, 7, 24, 77, 61, 86, 67, 39, 94, 57, 19, 72, 60, 42, 47, 6, 63, 45, 33, 56, 76, 83, 14, 9, 71, 17, 96, 23, 20, 5, 74, 3, 35, 16, 54, 59, 29, 99, 75, 90, 0, 62, 64, 46, 98, 73, 10, 48, 44, 79, 87, 58, 21, 15, 78, 37, 69, 93, 65, 49, 1, 51, 70, 40, 11, 82, 43, 36, 32, 12, 4, 81, 95, 84, 88, 25, 52, 18, 31, 28, 13], 'cur_cost': 96883.0}, {'tour': array([59, 40,  6,  8, 91, 75, 42, 72, 88, 92, 18, 39, 52, 46, 45, 25, 13,
       84, 41, 56,  0, 12, 11, 74, 93, 81, 49, 27, 65, 69, 66, 26,  3, 24,
       50,  7, 76, 94, 87, 28, 82, 98, 83, 80, 34, 71, 16, 22, 55, 62, 47,
       31, 35, 58, 90, 73, 97, 61, 36, 78, 96, 51, 79, 57, 15, 21, 17, 53,
       67, 95, 19, 77, 23,  9, 30, 99, 32, 63, 89, 48, 70, 14,  5, 29,  4,
        1, 43, 44, 38, 64, 85, 20, 68, 33, 86, 37,  2, 54, 10, 60],
      dtype=int64), 'cur_cost': 165064.0}, {'tour': [1, 53, 12, 6, 44, 62, 64, 21, 52, 41, 74, 98, 15, 30, 72, 51, 33, 19, 11, 54, 2, 84, 99, 94, 32, 82, 60, 59, 86, 13, 28, 75, 76, 91, 23, 22, 61, 50, 10, 73, 78, 3, 31, 27, 66, 48, 89, 18, 83, 69, 71, 90, 16, 35, 87, 7, 65, 57, 67, 29, 40, 45, 42, 70, 8, 56, 34, 85, 80, 24, 4, 68, 0, 9, 58, 96, 46, 79, 20, 93, 39, 43, 95, 77, 49, 26, 36, 63, 38, 97, 92, 5, 55, 88, 25, 37, 17, 14, 47, 81], 'cur_cost': 96301.0}, {'tour': [83, 23, 91, 22, 11, 61, 58, 52, 73, 31, 44, 26, 72, 56, 34, 85, 27, 74, 71, 69, 3, 35, 48, 41, 88, 68, 33, 13, 45, 40, 47, 94, 8, 77, 19, 38, 39, 4, 81, 32, 1, 67, 36, 42, 29, 70, 6, 28, 63, 84, 24, 62, 10, 92, 80, 86, 60, 0, 78, 55, 46, 59, 5, 90, 17, 25, 37, 96, 9, 15, 16, 20, 64, 87, 66, 57, 49, 51, 82, 99, 50, 76, 14, 21, 93, 30, 18, 89, 53, 79, 97, 7, 54, 43, 95, 12, 75, 2, 98, 65], 'cur_cost': 101357.0}, {'tour': [0, 6, 18, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 33761.0}, {'tour': [0, 13, 10, 19, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 2, 45, 28, 33, 82, 54, 6, 8, 56, 86, 50, 42], 'cur_cost': 33683.0}, {'tour': [64, 0, 80, 40, 70, 79, 41, 34, 89, 67, 29, 66, 91, 32, 11, 77, 14, 73, 71, 27, 22, 7, 38, 78, 20, 8, 46, 90, 58, 87, 47, 13, 9, 61, 97, 26, 30, 99, 2, 98, 52, 56, 4, 31, 39, 57, 85, 23, 68, 45, 3, 60, 82, 65, 33, 49, 5, 21, 37, 1, 76, 25, 53, 63, 42, 59, 55, 19, 93, 16, 72, 51, 92, 54, 74, 81, 17, 94, 44, 18, 83, 24, 84, 50, 36, 6, 96, 69, 88, 28, 75, 12, 62, 15, 35, 86, 10, 95, 48, 43], 'cur_cost': 172385.0}, {'tour': [38, 72, 29, 76, 24, 28, 19, 2, 4, 39, 79, 74, 9, 64, 62, 23, 7, 63, 6, 40, 60, 66, 11, 49, 13, 94, 99, 34, 46, 91, 37, 92, 89, 31, 8, 68, 80, 44, 18, 15, 55, 65, 96, 69, 5, 97, 54, 1, 51, 36, 70, 22, 59, 27, 85, 33, 26, 14, 10, 16, 71, 3, 30, 53, 81, 77, 32, 42, 67, 90, 20, 61, 0, 48, 41, 21, 78, 58, 52, 73, 83, 17, 86, 56, 43, 95, 75, 12, 57, 84, 50, 82, 35, 93, 98, 25, 87, 88, 45, 47], 'cur_cost': 104148.0}]
2025-08-05 09:52:22,474 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:22,474 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 383, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 383, 'cache_hits': 0, 'similarity_calculations': 1995, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:22,476 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([59, 40,  6,  8, 91, 75, 42, 72, 88, 92, 18, 39, 52, 46, 45, 25, 13,
       84, 41, 56,  0, 12, 11, 74, 93, 81, 49, 27, 65, 69, 66, 26,  3, 24,
       50,  7, 76, 94, 87, 28, 82, 98, 83, 80, 34, 71, 16, 22, 55, 62, 47,
       31, 35, 58, 90, 73, 97, 61, 36, 78, 96, 51, 79, 57, 15, 21, 17, 53,
       67, 95, 19, 77, 23,  9, 30, 99, 32, 63, 89, 48, 70, 14,  5, 29,  4,
        1, 43, 44, 38, 64, 85, 20, 68, 33, 86, 37,  2, 54, 10, 60],
      dtype=int64), 'cur_cost': 165064.0, 'intermediate_solutions': [{'tour': array([56,  3, 32, 42, 88, 45,  0, 17, 14, 67, 23, 37, 63,  5, 59, 11, 53,
       81, 52, 54, 35, 70, 55, 92, 75, 30, 69, 72, 33,  8, 76,  7, 43, 15,
       50, 65, 89, 28, 36, 58, 44, 21, 83, 74,  4, 98, 27, 60, 49, 51, 99,
       41, 12, 20, 34, 94,  9, 68, 57, 90, 16, 91, 86, 95, 82, 66, 40, 87,
       73, 71, 46, 84, 78, 80, 38, 47, 10, 29, 22,  2, 26, 13, 62, 97, 96,
       85, 19, 31, 24, 79, 48,  6, 64, 18, 39, 77, 93,  1, 61, 25]), 'cur_cost': 180783.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([42, 56,  3, 32, 88, 45,  0, 17, 14, 67, 23, 37, 63,  5, 59, 11, 53,
       81, 52, 54, 35, 70, 55, 92, 75, 30, 69, 72, 33,  8, 76,  7, 43, 15,
       50, 65, 89, 28, 36, 58, 44, 21, 83, 74,  4, 98, 27, 60, 49, 51, 99,
       41, 12, 20, 34, 94,  9, 68, 57, 90, 16, 91, 86, 95, 82, 66, 40, 87,
       73, 71, 46, 84, 78, 80, 38, 47, 10, 29, 22,  2, 26, 13, 62, 97, 96,
       85, 19, 31, 24, 79, 48,  6, 64, 18, 39, 77, 93,  1, 61, 25]), 'cur_cost': 180706.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([88, 42, 56,  3, 32, 45,  0, 17, 14, 67, 23, 37, 63,  5, 59, 11, 53,
       81, 52, 54, 35, 70, 55, 92, 75, 30, 69, 72, 33,  8, 76,  7, 43, 15,
       50, 65, 89, 28, 36, 58, 44, 21, 83, 74,  4, 98, 27, 60, 49, 51, 99,
       41, 12, 20, 34, 94,  9, 68, 57, 90, 16, 91, 86, 95, 82, 66, 40, 87,
       73, 71, 46, 84, 78, 80, 38, 47, 10, 29, 22,  2, 26, 13, 62, 97, 96,
       85, 19, 31, 24, 79, 48,  6, 64, 18, 39, 77, 93,  1, 61, 25]), 'cur_cost': 177466.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([32, 42, 56,  3, 88, 45,  0, 17, 14, 67, 23, 37, 63,  5, 59, 11, 53,
       81, 52, 54, 35, 70, 55, 92, 75, 30, 69, 72, 33,  8, 76,  7, 43, 15,
       50, 65, 89, 28, 36, 58, 44, 21, 83, 74,  4, 98, 27, 60, 49, 51, 99,
       41, 12, 20, 34, 94,  9, 68, 57, 90, 16, 91, 86, 95, 82, 66, 40, 87,
       73, 71, 46, 84, 78, 80, 38, 47, 10, 29, 22,  2, 26, 13, 62, 97, 96,
       85, 19, 31, 24, 79, 48,  6, 64, 18, 39, 77, 93,  1, 61, 25]), 'cur_cost': 177461.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([32, 88, 42, 56,  3, 45,  0, 17, 14, 67, 23, 37, 63,  5, 59, 11, 53,
       81, 52, 54, 35, 70, 55, 92, 75, 30, 69, 72, 33,  8, 76,  7, 43, 15,
       50, 65, 89, 28, 36, 58, 44, 21, 83, 74,  4, 98, 27, 60, 49, 51, 99,
       41, 12, 20, 34, 94,  9, 68, 57, 90, 16, 91, 86, 95, 82, 66, 40, 87,
       73, 71, 46, 84, 78, 80, 38, 47, 10, 29, 22,  2, 26, 13, 62, 97, 96,
       85, 19, 31, 24, 79, 48,  6, 64, 18, 39, 77, 93,  1, 61, 25]), 'cur_cost': 180622.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:22,476 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 165064.00)
2025-08-05 09:52:22,476 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:22,476 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:22,476 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:22,490 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 100
2025-08-05 09:52:22,490 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,491 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,491 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,491 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,492 - ExplorationExpert - INFO - 探索路径生成完成，成本: 105498.0, 路径长度: 100, 收集中间解: 3
2025-08-05 09:52:22,492 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [54, 70, 63, 92, 20, 83, 93, 97, 24, 1, 33, 57, 5, 88, 72, 8, 28, 95, 85, 40, 56, 60, 7, 69, 18, 17, 22, 9, 74, 14, 11, 50, 29, 53, 27, 90, 37, 62, 46, 89, 25, 41, 48, 15, 96, 78, 58, 64, 3, 0, 71, 31, 23, 30, 79, 87, 21, 68, 49, 82, 6, 47, 4, 84, 36, 51, 38, 86, 26, 59, 45, 19, 10, 80, 2, 43, 42, 61, 13, 99, 67, 32, 66, 16, 76, 77, 34, 73, 55, 91, 35, 44, 52, 65, 98, 39, 94, 12, 75, 81], 'cur_cost': 105498.0, 'intermediate_solutions': [{'tour': [1, 53, 12, 6, 44, 62, 64, 21, 52, 41, 74, 98, 15, 30, 72, 51, 33, 19, 11, 54, 2, 84, 99, 94, 32, 82, 60, 59, 86, 13, 28, 75, 76, 91, 23, 22, 97, 50, 10, 73, 78, 3, 31, 27, 66, 48, 89, 18, 83, 69, 71, 90, 16, 35, 87, 7, 65, 57, 67, 29, 40, 45, 42, 70, 8, 56, 34, 85, 80, 24, 4, 68, 0, 9, 58, 96, 46, 79, 20, 93, 39, 43, 95, 77, 49, 26, 36, 63, 38, 61, 92, 5, 55, 88, 25, 37, 17, 14, 47, 81], 'cur_cost': 96065.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 53, 12, 6, 44, 62, 64, 21, 52, 41, 74, 98, 15, 30, 72, 51, 33, 19, 11, 54, 2, 84, 99, 94, 32, 82, 60, 59, 86, 13, 28, 75, 76, 91, 23, 22, 61, 50, 10, 73, 78, 3, 31, 27, 66, 48, 89, 18, 83, 69, 71, 90, 16, 35, 87, 7, 0, 68, 4, 24, 80, 85, 34, 56, 8, 70, 42, 45, 40, 29, 67, 57, 65, 9, 58, 96, 46, 79, 20, 93, 39, 43, 95, 77, 49, 26, 36, 63, 38, 97, 92, 5, 55, 88, 25, 37, 17, 14, 47, 81], 'cur_cost': 96018.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 53, 12, 6, 44, 62, 64, 21, 52, 41, 74, 98, 15, 30, 72, 51, 33, 19, 11, 8, 54, 2, 84, 99, 94, 32, 82, 60, 59, 86, 13, 28, 75, 76, 91, 23, 22, 61, 50, 10, 73, 78, 3, 31, 27, 66, 48, 89, 18, 83, 69, 71, 90, 16, 35, 87, 7, 65, 57, 67, 29, 40, 45, 42, 70, 56, 34, 85, 80, 24, 4, 68, 0, 9, 58, 96, 46, 79, 20, 93, 39, 43, 95, 77, 49, 26, 36, 63, 38, 97, 92, 5, 55, 88, 25, 37, 17, 14, 47, 81], 'cur_cost': 96614.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:22,492 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 105498.00)
2025-08-05 09:52:22,492 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:22,492 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:22,492 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:22,497 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 09:52:22,497 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,498 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,498 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,499 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,499 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30570.0, 路径长度: 100, 收集中间解: 3
2025-08-05 09:52:22,499 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 16, 7, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 42, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57], 'cur_cost': 30570.0, 'intermediate_solutions': [{'tour': [83, 23, 91, 22, 11, 61, 58, 52, 73, 31, 44, 26, 72, 56, 34, 85, 27, 4, 71, 69, 3, 35, 48, 41, 88, 68, 33, 13, 45, 40, 47, 94, 8, 77, 19, 38, 39, 74, 81, 32, 1, 67, 36, 42, 29, 70, 6, 28, 63, 84, 24, 62, 10, 92, 80, 86, 60, 0, 78, 55, 46, 59, 5, 90, 17, 25, 37, 96, 9, 15, 16, 20, 64, 87, 66, 57, 49, 51, 82, 99, 50, 76, 14, 21, 93, 30, 18, 89, 53, 79, 97, 7, 54, 43, 95, 12, 75, 2, 98, 65], 'cur_cost': 106911.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [83, 23, 60, 86, 80, 92, 10, 62, 24, 84, 63, 28, 6, 70, 29, 42, 36, 67, 1, 32, 81, 4, 39, 38, 19, 77, 8, 94, 47, 40, 45, 13, 33, 68, 88, 41, 48, 35, 3, 69, 71, 74, 27, 85, 34, 56, 72, 26, 44, 31, 73, 52, 58, 61, 11, 22, 91, 0, 78, 55, 46, 59, 5, 90, 17, 25, 37, 96, 9, 15, 16, 20, 64, 87, 66, 57, 49, 51, 82, 99, 50, 76, 14, 21, 93, 30, 18, 89, 53, 79, 97, 7, 54, 43, 95, 12, 75, 2, 98, 65], 'cur_cost': 101644.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [83, 23, 91, 22, 11, 61, 58, 52, 73, 31, 26, 72, 56, 34, 85, 27, 74, 71, 69, 3, 35, 48, 41, 88, 68, 33, 13, 45, 40, 47, 94, 8, 77, 19, 38, 39, 4, 81, 32, 1, 67, 36, 42, 29, 70, 6, 28, 63, 84, 24, 62, 10, 92, 80, 86, 60, 0, 78, 55, 46, 59, 5, 90, 17, 25, 37, 96, 9, 15, 16, 20, 64, 87, 66, 57, 49, 51, 82, 99, 50, 76, 14, 21, 93, 30, 18, 89, 53, 79, 44, 97, 7, 54, 43, 95, 12, 75, 2, 98, 65], 'cur_cost': 101700.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:22,500 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 30570.00)
2025-08-05 09:52:22,500 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:22,500 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:22,500 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:22,504 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 09:52:22,504 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,504 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,505 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,505 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,505 - ExplorationExpert - INFO - 探索路径生成完成，成本: 35485.0, 路径长度: 100, 收集中间解: 3
2025-08-05 09:52:22,505 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 13, 17, 1, 43, 49, 72, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 63, 39, 53, 68, 80, 24, 60, 50, 86, 8, 6, 56, 19, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 54, 82, 33, 28, 45, 2, 42, 70, 40, 99, 47], 'cur_cost': 35485.0, 'intermediate_solutions': [{'tour': [0, 6, 18, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 90, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 61, 59, 76, 22, 97, 67, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 39272.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 18, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 62, 48, 89, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 33705.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 18, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 23, 16, 73, 20, 58, 71, 9, 83, 35, 37, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 35949.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:22,506 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 35485.00)
2025-08-05 09:52:22,506 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:22,506 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:22,506 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:22,510 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 09:52:22,510 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,510 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,510 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,511 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,511 - ExplorationExpert - INFO - 探索路径生成完成，成本: 31265.0, 路径长度: 100, 收集中间解: 3
2025-08-05 09:52:22,511 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 7, 21, 14, 16, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 91, 74, 18, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 31265.0, 'intermediate_solutions': [{'tour': [0, 13, 10, 19, 11, 26, 85, 34, 46, 59, 76, 22, 97, 90, 44, 31, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 61, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 2, 45, 28, 33, 82, 54, 6, 8, 56, 86, 50, 42], 'cur_cost': 36627.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 10, 19, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 6, 54, 82, 33, 28, 45, 2, 40, 70, 99, 47, 81, 94, 12, 75, 32, 36, 4, 51, 77, 95, 29, 38, 84, 67, 63, 39, 53, 1, 43, 49, 72, 68, 80, 24, 60, 57, 66, 27, 92, 46, 98, 25, 74, 18, 89, 62, 48, 5, 91, 7, 41, 8, 56, 86, 50, 42], 'cur_cost': 37128.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 10, 19, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 31, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 2, 45, 28, 33, 82, 54, 6, 8, 56, 86, 50, 42], 'cur_cost': 35813.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:22,511 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 31265.00)
2025-08-05 09:52:22,512 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:52:22,512 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:22,512 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:22,512 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 178575.0
2025-08-05 09:52:22,529 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:22,530 - ExploitationExpert - INFO - res_population_costs: [21389, 21411, 21776, 22282, 22308, 22356, 22427.0]
2025-08-05 09:52:22,530 - ExploitationExpert - INFO - res_populations: [array([ 0, 62, 89, 48,  5, 91,  7, 41, 88, 30, 79, 55, 96, 74, 18,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26,
       11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99, 47,
       51, 77, 95,  4, 36, 32, 75, 12, 94, 81, 38, 29, 84, 67, 72, 49, 43,
        1, 53, 39, 63, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 62,  5, 48, 89, 18, 74, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 59, 61, 76, 50, 86,  8,
        6, 56, 19, 85, 34, 26, 11, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40,
       99, 47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81, 49, 43,  1,
       53, 39, 63, 68, 72, 67, 84, 80, 24, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 91, 74, 96, 55, 79, 30, 88, 41,  7, 66, 27, 92, 57, 60, 24, 80,
       84, 67, 72, 68, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75, 32, 36,  4,
       51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28, 33, 82, 54,
       11, 26, 34, 85, 19, 56,  6,  8, 86, 50, 76, 61, 59, 22, 97, 90, 44,
       31, 10, 14, 16, 58, 73, 20, 71,  9, 83, 35, 98, 37, 23, 17, 78, 52,
       87, 15, 93, 21, 69, 65, 25, 64,  3, 18, 89, 48,  5, 62, 46],
      dtype=int64), array([ 0, 46, 92, 27, 66, 57, 68, 63, 39, 53,  1, 43, 72, 49, 81, 94, 12,
       75, 32, 36,  4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45,
       28, 33, 82, 54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 80, 24, 60,
       50, 86, 76, 61, 59, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20, 71,
        9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3, 18,
       52, 78, 89, 74, 96, 55, 79, 30, 88, 41,  7, 91, 48,  5, 62],
      dtype=int64), array([ 0, 62, 46, 92, 27, 57, 66, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75,
       32, 36,  4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28,
       33, 82, 54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 72, 68, 80, 24,
       60, 50, 86, 76, 61, 59, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20,
       71,  9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3,
       18, 52, 78, 89, 48,  5, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 62,  5, 48, 91,  7, 41, 88, 30, 79, 55, 96, 74, 89, 78, 52, 18,
        3, 64, 25, 65, 69, 21, 15, 87, 93, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 59, 61, 76, 86, 50, 60,
       24, 80, 68, 72, 67, 84,  8,  6, 56, 19, 85, 34, 26, 11, 54, 82, 33,
       28, 45, 42,  2, 13, 70, 40, 99, 47, 29, 38, 95, 77, 51,  4, 36, 32,
       75, 12, 94, 81, 49, 43,  1, 53, 39, 63, 66, 57, 27, 92, 46],
      dtype=int64), array([ 0, 92, 27, 57, 66, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75, 32, 36,
        4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28, 33, 82,
       54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 72, 68, 80, 24, 60, 50,
       86, 76, 61, 59, 22, 97, 44, 90, 46, 31, 10, 14, 16, 58, 73, 20, 71,
        9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3, 18,
       52, 78, 89, 74, 96, 55, 79, 30, 88, 41,  7, 91, 48,  5, 62],
      dtype=int64)]
2025-08-05 09:52:22,534 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:22,534 - ExploitationExpert - INFO - populations: [{'tour': array([33, 87, 26,  4, 92, 64, 39, 29, 25, 78, 36, 41, 94, 35, 38, 90, 72,
       66, 30, 85, 32,  2, 73, 76, 11, 88, 96, 97, 83,  9, 51, 68,  7, 91,
        3, 13,  1, 67, 77, 52, 84, 54, 61, 95, 86, 37, 59, 12, 44, 31, 71,
       56,  0, 17, 28, 49, 19, 74, 75, 89, 21, 55, 15, 43, 16, 46, 80, 98,
       48, 53, 60,  8, 40, 23, 82, 10,  6, 20, 70, 62, 34, 24, 47, 57, 14,
       18, 42, 69, 79, 45, 58, 63, 65, 27, 50, 93, 99,  5, 22, 81],
      dtype=int64), 'cur_cost': 186346.0}, {'tour': [85, 22, 66, 50, 77, 38, 6, 46, 37, 30, 17, 90, 48, 14, 23, 10, 97, 68, 76, 60, 0, 7, 79, 83, 58, 27, 86, 80, 34, 42, 28, 13, 2, 63, 75, 54, 53, 94, 51, 82, 43, 41, 3, 93, 65, 15, 88, 25, 87, 57, 74, 21, 71, 55, 1, 49, 92, 89, 96, 5, 18, 64, 39, 32, 84, 95, 36, 99, 11, 67, 12, 19, 26, 40, 70, 59, 24, 33, 29, 8, 47, 72, 61, 44, 31, 91, 78, 62, 35, 98, 9, 20, 73, 16, 52, 56, 45, 4, 81, 69], 'cur_cost': 102636.0}, {'tour': [66, 68, 85, 92, 55, 30, 91, 97, 34, 80, 2, 38, 50, 27, 26, 8, 53, 41, 89, 22, 7, 24, 77, 61, 86, 67, 39, 94, 57, 19, 72, 60, 42, 47, 6, 63, 45, 33, 56, 76, 83, 14, 9, 71, 17, 96, 23, 20, 5, 74, 3, 35, 16, 54, 59, 29, 99, 75, 90, 0, 62, 64, 46, 98, 73, 10, 48, 44, 79, 87, 58, 21, 15, 78, 37, 69, 93, 65, 49, 1, 51, 70, 40, 11, 82, 43, 36, 32, 12, 4, 81, 95, 84, 88, 25, 52, 18, 31, 28, 13], 'cur_cost': 96883.0}, {'tour': array([59, 40,  6,  8, 91, 75, 42, 72, 88, 92, 18, 39, 52, 46, 45, 25, 13,
       84, 41, 56,  0, 12, 11, 74, 93, 81, 49, 27, 65, 69, 66, 26,  3, 24,
       50,  7, 76, 94, 87, 28, 82, 98, 83, 80, 34, 71, 16, 22, 55, 62, 47,
       31, 35, 58, 90, 73, 97, 61, 36, 78, 96, 51, 79, 57, 15, 21, 17, 53,
       67, 95, 19, 77, 23,  9, 30, 99, 32, 63, 89, 48, 70, 14,  5, 29,  4,
        1, 43, 44, 38, 64, 85, 20, 68, 33, 86, 37,  2, 54, 10, 60],
      dtype=int64), 'cur_cost': 165064.0}, {'tour': [54, 70, 63, 92, 20, 83, 93, 97, 24, 1, 33, 57, 5, 88, 72, 8, 28, 95, 85, 40, 56, 60, 7, 69, 18, 17, 22, 9, 74, 14, 11, 50, 29, 53, 27, 90, 37, 62, 46, 89, 25, 41, 48, 15, 96, 78, 58, 64, 3, 0, 71, 31, 23, 30, 79, 87, 21, 68, 49, 82, 6, 47, 4, 84, 36, 51, 38, 86, 26, 59, 45, 19, 10, 80, 2, 43, 42, 61, 13, 99, 67, 32, 66, 16, 76, 77, 34, 73, 55, 91, 35, 44, 52, 65, 98, 39, 94, 12, 75, 81], 'cur_cost': 105498.0}, {'tour': [0, 16, 7, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 42, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57], 'cur_cost': 30570.0}, {'tour': [0, 13, 17, 1, 43, 49, 72, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 63, 39, 53, 68, 80, 24, 60, 50, 86, 8, 6, 56, 19, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 54, 82, 33, 28, 45, 2, 42, 70, 40, 99, 47], 'cur_cost': 35485.0}, {'tour': [0, 7, 21, 14, 16, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 91, 74, 18, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 31265.0}, {'tour': array([31,  6, 88, 97, 14, 85, 39, 43, 73, 58, 64, 84, 41, 22, 99, 37, 98,
       95,  7, 23, 28, 81, 35, 29, 46, 59, 68, 69, 19, 71, 94, 42, 10, 56,
       51, 96, 79,  3, 47, 90, 89, 67, 49,  2, 38, 18, 26,  4, 16, 60, 65,
       50, 53, 70, 36, 12, 24, 77, 25, 11, 83, 87, 40,  5, 45,  0, 55, 61,
       74, 82, 44, 66, 30, 78, 93, 48, 76, 17, 62, 21, 72, 34, 52,  8, 32,
       86, 33, 54, 75, 91, 20, 27, 80,  9, 13, 15, 57,  1, 63, 92],
      dtype=int64), 'cur_cost': 178575.0}, {'tour': [38, 72, 29, 76, 24, 28, 19, 2, 4, 39, 79, 74, 9, 64, 62, 23, 7, 63, 6, 40, 60, 66, 11, 49, 13, 94, 99, 34, 46, 91, 37, 92, 89, 31, 8, 68, 80, 44, 18, 15, 55, 65, 96, 69, 5, 97, 54, 1, 51, 36, 70, 22, 59, 27, 85, 33, 26, 14, 10, 16, 71, 3, 30, 53, 81, 77, 32, 42, 67, 90, 20, 61, 0, 48, 41, 21, 78, 58, 52, 73, 83, 17, 86, 56, 43, 95, 75, 12, 57, 84, 50, 82, 35, 93, 98, 25, 87, 88, 45, 47], 'cur_cost': 104148.0}]
2025-08-05 09:52:22,536 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:22,536 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 384, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 384, 'cache_hits': 0, 'similarity_calculations': 2001, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:22,538 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([31,  6, 88, 97, 14, 85, 39, 43, 73, 58, 64, 84, 41, 22, 99, 37, 98,
       95,  7, 23, 28, 81, 35, 29, 46, 59, 68, 69, 19, 71, 94, 42, 10, 56,
       51, 96, 79,  3, 47, 90, 89, 67, 49,  2, 38, 18, 26,  4, 16, 60, 65,
       50, 53, 70, 36, 12, 24, 77, 25, 11, 83, 87, 40,  5, 45,  0, 55, 61,
       74, 82, 44, 66, 30, 78, 93, 48, 76, 17, 62, 21, 72, 34, 52,  8, 32,
       86, 33, 54, 75, 91, 20, 27, 80,  9, 13, 15, 57,  1, 63, 92],
      dtype=int64), 'cur_cost': 178575.0, 'intermediate_solutions': [{'tour': array([80,  0, 64, 40, 70, 79, 41, 34, 89, 67, 29, 66, 91, 32, 11, 77, 14,
       73, 71, 27, 22,  7, 38, 78, 20,  8, 46, 90, 58, 87, 47, 13,  9, 61,
       97, 26, 30, 99,  2, 98, 52, 56,  4, 31, 39, 57, 85, 23, 68, 45,  3,
       60, 82, 65, 33, 49,  5, 21, 37,  1, 76, 25, 53, 63, 42, 59, 55, 19,
       93, 16, 72, 51, 92, 54, 74, 81, 17, 94, 44, 18, 83, 24, 84, 50, 36,
        6, 96, 69, 88, 28, 75, 12, 62, 15, 35, 86, 10, 95, 48, 43]), 'cur_cost': 172754.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([40, 80,  0, 64, 70, 79, 41, 34, 89, 67, 29, 66, 91, 32, 11, 77, 14,
       73, 71, 27, 22,  7, 38, 78, 20,  8, 46, 90, 58, 87, 47, 13,  9, 61,
       97, 26, 30, 99,  2, 98, 52, 56,  4, 31, 39, 57, 85, 23, 68, 45,  3,
       60, 82, 65, 33, 49,  5, 21, 37,  1, 76, 25, 53, 63, 42, 59, 55, 19,
       93, 16, 72, 51, 92, 54, 74, 81, 17, 94, 44, 18, 83, 24, 84, 50, 36,
        6, 96, 69, 88, 28, 75, 12, 62, 15, 35, 86, 10, 95, 48, 43]), 'cur_cost': 175279.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([70, 40, 80,  0, 64, 79, 41, 34, 89, 67, 29, 66, 91, 32, 11, 77, 14,
       73, 71, 27, 22,  7, 38, 78, 20,  8, 46, 90, 58, 87, 47, 13,  9, 61,
       97, 26, 30, 99,  2, 98, 52, 56,  4, 31, 39, 57, 85, 23, 68, 45,  3,
       60, 82, 65, 33, 49,  5, 21, 37,  1, 76, 25, 53, 63, 42, 59, 55, 19,
       93, 16, 72, 51, 92, 54, 74, 81, 17, 94, 44, 18, 83, 24, 84, 50, 36,
        6, 96, 69, 88, 28, 75, 12, 62, 15, 35, 86, 10, 95, 48, 43]), 'cur_cost': 169143.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([64, 40, 80,  0, 70, 79, 41, 34, 89, 67, 29, 66, 91, 32, 11, 77, 14,
       73, 71, 27, 22,  7, 38, 78, 20,  8, 46, 90, 58, 87, 47, 13,  9, 61,
       97, 26, 30, 99,  2, 98, 52, 56,  4, 31, 39, 57, 85, 23, 68, 45,  3,
       60, 82, 65, 33, 49,  5, 21, 37,  1, 76, 25, 53, 63, 42, 59, 55, 19,
       93, 16, 72, 51, 92, 54, 74, 81, 17, 94, 44, 18, 83, 24, 84, 50, 36,
        6, 96, 69, 88, 28, 75, 12, 62, 15, 35, 86, 10, 95, 48, 43]), 'cur_cost': 177605.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([64, 70, 40, 80,  0, 79, 41, 34, 89, 67, 29, 66, 91, 32, 11, 77, 14,
       73, 71, 27, 22,  7, 38, 78, 20,  8, 46, 90, 58, 87, 47, 13,  9, 61,
       97, 26, 30, 99,  2, 98, 52, 56,  4, 31, 39, 57, 85, 23, 68, 45,  3,
       60, 82, 65, 33, 49,  5, 21, 37,  1, 76, 25, 53, 63, 42, 59, 55, 19,
       93, 16, 72, 51, 92, 54, 74, 81, 17, 94, 44, 18, 83, 24, 84, 50, 36,
        6, 96, 69, 88, 28, 75, 12, 62, 15, 35, 86, 10, 95, 48, 43]), 'cur_cost': 172696.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:22,538 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 178575.00)
2025-08-05 09:52:22,538 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:22,538 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:22,538 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:22,542 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 09:52:22,543 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,543 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,543 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,544 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,544 - ExplorationExpert - INFO - 探索路径生成完成，成本: 35239.0, 路径长度: 100, 收集中间解: 3
2025-08-05 09:52:22,544 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 17, 12, 5, 48, 62, 91, 7, 41, 88, 30, 79, 55, 96, 74, 18, 52, 78, 87, 15, 21, 93, 69, 65, 64, 3, 25, 89, 9, 83, 71, 20, 73, 58, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42, 35, 37, 23, 98], 'cur_cost': 35239.0, 'intermediate_solutions': [{'tour': [38, 72, 29, 76, 24, 28, 19, 2, 4, 39, 79, 74, 9, 64, 62, 27, 7, 63, 6, 40, 60, 66, 11, 49, 13, 94, 99, 34, 46, 91, 37, 92, 89, 31, 8, 68, 80, 44, 18, 15, 55, 65, 96, 69, 5, 97, 54, 1, 51, 36, 70, 22, 59, 23, 85, 33, 26, 14, 10, 16, 71, 3, 30, 53, 81, 77, 32, 42, 67, 90, 20, 61, 0, 48, 41, 21, 78, 58, 52, 73, 83, 17, 86, 56, 43, 95, 75, 12, 57, 84, 50, 82, 35, 93, 98, 25, 87, 88, 45, 47], 'cur_cost': 105407.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [38, 78, 21, 41, 48, 0, 61, 20, 90, 67, 42, 32, 77, 81, 53, 30, 3, 71, 16, 10, 14, 26, 33, 85, 27, 59, 22, 70, 36, 51, 1, 54, 97, 5, 69, 96, 65, 55, 15, 18, 44, 80, 68, 8, 31, 89, 92, 37, 91, 46, 34, 99, 94, 13, 49, 11, 66, 60, 40, 6, 63, 7, 23, 62, 64, 9, 74, 79, 39, 4, 2, 19, 28, 24, 76, 29, 72, 58, 52, 73, 83, 17, 86, 56, 43, 95, 75, 12, 57, 84, 50, 82, 35, 93, 98, 25, 87, 88, 45, 47], 'cur_cost': 107995.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [38, 72, 29, 76, 24, 28, 19, 2, 4, 39, 79, 74, 9, 64, 62, 23, 7, 63, 40, 60, 66, 11, 49, 13, 94, 99, 34, 46, 91, 37, 92, 89, 31, 8, 68, 80, 44, 18, 15, 55, 65, 96, 69, 5, 6, 97, 54, 1, 51, 36, 70, 22, 59, 27, 85, 33, 26, 14, 10, 16, 71, 3, 30, 53, 81, 77, 32, 42, 67, 90, 20, 61, 0, 48, 41, 21, 78, 58, 52, 73, 83, 17, 86, 56, 43, 95, 75, 12, 57, 84, 50, 82, 35, 93, 98, 25, 87, 88, 45, 47], 'cur_cost': 105505.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:22,544 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 35239.00)
2025-08-05 09:52:22,545 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:22,545 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:22,548 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([33, 87, 26,  4, 92, 64, 39, 29, 25, 78, 36, 41, 94, 35, 38, 90, 72,
       66, 30, 85, 32,  2, 73, 76, 11, 88, 96, 97, 83,  9, 51, 68,  7, 91,
        3, 13,  1, 67, 77, 52, 84, 54, 61, 95, 86, 37, 59, 12, 44, 31, 71,
       56,  0, 17, 28, 49, 19, 74, 75, 89, 21, 55, 15, 43, 16, 46, 80, 98,
       48, 53, 60,  8, 40, 23, 82, 10,  6, 20, 70, 62, 34, 24, 47, 57, 14,
       18, 42, 69, 79, 45, 58, 63, 65, 27, 50, 93, 99,  5, 22, 81],
      dtype=int64), 'cur_cost': 186346.0, 'intermediate_solutions': [{'tour': array([39, 81, 84, 80, 38, 11, 46, 66,  0, 92, 18, 71, 31, 78,  9, 30, 43,
       19, 29, 75, 54, 57, 50, 13, 61, 82, 85, 16, 48,  7, 79, 24, 95, 70,
       47, 99, 59, 33, 77, 32, 60, 63, 26, 62, 88, 87, 14, 97, 35, 83, 93,
       65, 69, 89, 64, 41, 68, 49, 76, 28, 36, 27, 58, 44, 10, 22, 74, 23,
       21,  3, 53, 45, 51,  2,  4, 40, 34, 73, 52, 25, 96, 17, 15, 91, 98,
       86,  8,  6, 90, 37,  5, 56,  1, 67, 72, 12, 94, 42, 20, 55]), 'cur_cost': 104128.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([80, 39, 81, 84, 38, 11, 46, 66,  0, 92, 18, 71, 31, 78,  9, 30, 43,
       19, 29, 75, 54, 57, 50, 13, 61, 82, 85, 16, 48,  7, 79, 24, 95, 70,
       47, 99, 59, 33, 77, 32, 60, 63, 26, 62, 88, 87, 14, 97, 35, 83, 93,
       65, 69, 89, 64, 41, 68, 49, 76, 28, 36, 27, 58, 44, 10, 22, 74, 23,
       21,  3, 53, 45, 51,  2,  4, 40, 34, 73, 52, 25, 96, 17, 15, 91, 98,
       86,  8,  6, 90, 37,  5, 56,  1, 67, 72, 12, 94, 42, 20, 55]), 'cur_cost': 103944.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([38, 80, 39, 81, 84, 11, 46, 66,  0, 92, 18, 71, 31, 78,  9, 30, 43,
       19, 29, 75, 54, 57, 50, 13, 61, 82, 85, 16, 48,  7, 79, 24, 95, 70,
       47, 99, 59, 33, 77, 32, 60, 63, 26, 62, 88, 87, 14, 97, 35, 83, 93,
       65, 69, 89, 64, 41, 68, 49, 76, 28, 36, 27, 58, 44, 10, 22, 74, 23,
       21,  3, 53, 45, 51,  2,  4, 40, 34, 73, 52, 25, 96, 17, 15, 91, 98,
       86,  8,  6, 90, 37,  5, 56,  1, 67, 72, 12, 94, 42, 20, 55]), 'cur_cost': 105023.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([84, 80, 39, 81, 38, 11, 46, 66,  0, 92, 18, 71, 31, 78,  9, 30, 43,
       19, 29, 75, 54, 57, 50, 13, 61, 82, 85, 16, 48,  7, 79, 24, 95, 70,
       47, 99, 59, 33, 77, 32, 60, 63, 26, 62, 88, 87, 14, 97, 35, 83, 93,
       65, 69, 89, 64, 41, 68, 49, 76, 28, 36, 27, 58, 44, 10, 22, 74, 23,
       21,  3, 53, 45, 51,  2,  4, 40, 34, 73, 52, 25, 96, 17, 15, 91, 98,
       86,  8,  6, 90, 37,  5, 56,  1, 67, 72, 12, 94, 42, 20, 55]), 'cur_cost': 104448.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([84, 38, 80, 39, 81, 11, 46, 66,  0, 92, 18, 71, 31, 78,  9, 30, 43,
       19, 29, 75, 54, 57, 50, 13, 61, 82, 85, 16, 48,  7, 79, 24, 95, 70,
       47, 99, 59, 33, 77, 32, 60, 63, 26, 62, 88, 87, 14, 97, 35, 83, 93,
       65, 69, 89, 64, 41, 68, 49, 76, 28, 36, 27, 58, 44, 10, 22, 74, 23,
       21,  3, 53, 45, 51,  2,  4, 40, 34, 73, 52, 25, 96, 17, 15, 91, 98,
       86,  8,  6, 90, 37,  5, 56,  1, 67, 72, 12, 94, 42, 20, 55]), 'cur_cost': 105054.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [85, 22, 66, 50, 77, 38, 6, 46, 37, 30, 17, 90, 48, 14, 23, 10, 97, 68, 76, 60, 0, 7, 79, 83, 58, 27, 86, 80, 34, 42, 28, 13, 2, 63, 75, 54, 53, 94, 51, 82, 43, 41, 3, 93, 65, 15, 88, 25, 87, 57, 74, 21, 71, 55, 1, 49, 92, 89, 96, 5, 18, 64, 39, 32, 84, 95, 36, 99, 11, 67, 12, 19, 26, 40, 70, 59, 24, 33, 29, 8, 47, 72, 61, 44, 31, 91, 78, 62, 35, 98, 9, 20, 73, 16, 52, 56, 45, 4, 81, 69], 'cur_cost': 102636.0, 'intermediate_solutions': [{'tour': [0, 19, 16, 18, 74, 96, 55, 79, 30, 88, 41, 7, 8, 5, 48, 62, 89, 9, 83, 71, 20, 73, 58, 10, 14, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 61, 59, 76, 56, 6, 91, 86, 50, 42, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 25, 98], 'cur_cost': 38133.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 16, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 9, 83, 71, 20, 73, 58, 10, 14, 17, 23, 37, 35, 42, 50, 86, 8, 6, 56, 76, 59, 61, 34, 85, 26, 11, 54, 82, 33, 28, 45, 2, 13, 40, 70, 99, 47, 81, 94, 12, 75, 32, 36, 4, 51, 77, 95, 29, 38, 84, 67, 63, 39, 53, 1, 43, 49, 72, 68, 80, 24, 60, 57, 66, 27, 92, 46, 44, 22, 97, 90, 31, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 25, 98], 'cur_cost': 34150.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 16, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 9, 83, 71, 20, 73, 58, 10, 14, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 61, 59, 76, 56, 6, 8, 86, 50, 42, 35, 37, 23, 17, 78, 67, 52, 87, 15, 21, 93, 69, 65, 64, 3, 25, 98], 'cur_cost': 37209.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [66, 68, 85, 92, 55, 30, 91, 97, 34, 80, 2, 38, 50, 27, 26, 8, 53, 41, 89, 22, 7, 24, 77, 61, 86, 67, 39, 94, 57, 19, 72, 60, 42, 47, 6, 63, 45, 33, 56, 76, 83, 14, 9, 71, 17, 96, 23, 20, 5, 74, 3, 35, 16, 54, 59, 29, 99, 75, 90, 0, 62, 64, 46, 98, 73, 10, 48, 44, 79, 87, 58, 21, 15, 78, 37, 69, 93, 65, 49, 1, 51, 70, 40, 11, 82, 43, 36, 32, 12, 4, 81, 95, 84, 88, 25, 52, 18, 31, 28, 13], 'cur_cost': 96883.0, 'intermediate_solutions': [{'tour': [0, 5, 18, 23, 73, 58, 71, 9, 83, 35, 37, 20, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 48, 62, 89, 74, 25, 98, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 31039.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 18, 20, 73, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 48, 62, 89, 74, 25, 98, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 13, 40, 70, 99, 47, 81, 94, 12, 75, 32, 36, 4, 51, 77, 95, 29, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 30273.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 18, 20, 73, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 48, 62, 89, 74, 25, 98, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 11, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 32488.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([59, 40,  6,  8, 91, 75, 42, 72, 88, 92, 18, 39, 52, 46, 45, 25, 13,
       84, 41, 56,  0, 12, 11, 74, 93, 81, 49, 27, 65, 69, 66, 26,  3, 24,
       50,  7, 76, 94, 87, 28, 82, 98, 83, 80, 34, 71, 16, 22, 55, 62, 47,
       31, 35, 58, 90, 73, 97, 61, 36, 78, 96, 51, 79, 57, 15, 21, 17, 53,
       67, 95, 19, 77, 23,  9, 30, 99, 32, 63, 89, 48, 70, 14,  5, 29,  4,
        1, 43, 44, 38, 64, 85, 20, 68, 33, 86, 37,  2, 54, 10, 60],
      dtype=int64), 'cur_cost': 165064.0, 'intermediate_solutions': [{'tour': array([56,  3, 32, 42, 88, 45,  0, 17, 14, 67, 23, 37, 63,  5, 59, 11, 53,
       81, 52, 54, 35, 70, 55, 92, 75, 30, 69, 72, 33,  8, 76,  7, 43, 15,
       50, 65, 89, 28, 36, 58, 44, 21, 83, 74,  4, 98, 27, 60, 49, 51, 99,
       41, 12, 20, 34, 94,  9, 68, 57, 90, 16, 91, 86, 95, 82, 66, 40, 87,
       73, 71, 46, 84, 78, 80, 38, 47, 10, 29, 22,  2, 26, 13, 62, 97, 96,
       85, 19, 31, 24, 79, 48,  6, 64, 18, 39, 77, 93,  1, 61, 25]), 'cur_cost': 180783.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([42, 56,  3, 32, 88, 45,  0, 17, 14, 67, 23, 37, 63,  5, 59, 11, 53,
       81, 52, 54, 35, 70, 55, 92, 75, 30, 69, 72, 33,  8, 76,  7, 43, 15,
       50, 65, 89, 28, 36, 58, 44, 21, 83, 74,  4, 98, 27, 60, 49, 51, 99,
       41, 12, 20, 34, 94,  9, 68, 57, 90, 16, 91, 86, 95, 82, 66, 40, 87,
       73, 71, 46, 84, 78, 80, 38, 47, 10, 29, 22,  2, 26, 13, 62, 97, 96,
       85, 19, 31, 24, 79, 48,  6, 64, 18, 39, 77, 93,  1, 61, 25]), 'cur_cost': 180706.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([88, 42, 56,  3, 32, 45,  0, 17, 14, 67, 23, 37, 63,  5, 59, 11, 53,
       81, 52, 54, 35, 70, 55, 92, 75, 30, 69, 72, 33,  8, 76,  7, 43, 15,
       50, 65, 89, 28, 36, 58, 44, 21, 83, 74,  4, 98, 27, 60, 49, 51, 99,
       41, 12, 20, 34, 94,  9, 68, 57, 90, 16, 91, 86, 95, 82, 66, 40, 87,
       73, 71, 46, 84, 78, 80, 38, 47, 10, 29, 22,  2, 26, 13, 62, 97, 96,
       85, 19, 31, 24, 79, 48,  6, 64, 18, 39, 77, 93,  1, 61, 25]), 'cur_cost': 177466.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([32, 42, 56,  3, 88, 45,  0, 17, 14, 67, 23, 37, 63,  5, 59, 11, 53,
       81, 52, 54, 35, 70, 55, 92, 75, 30, 69, 72, 33,  8, 76,  7, 43, 15,
       50, 65, 89, 28, 36, 58, 44, 21, 83, 74,  4, 98, 27, 60, 49, 51, 99,
       41, 12, 20, 34, 94,  9, 68, 57, 90, 16, 91, 86, 95, 82, 66, 40, 87,
       73, 71, 46, 84, 78, 80, 38, 47, 10, 29, 22,  2, 26, 13, 62, 97, 96,
       85, 19, 31, 24, 79, 48,  6, 64, 18, 39, 77, 93,  1, 61, 25]), 'cur_cost': 177461.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([32, 88, 42, 56,  3, 45,  0, 17, 14, 67, 23, 37, 63,  5, 59, 11, 53,
       81, 52, 54, 35, 70, 55, 92, 75, 30, 69, 72, 33,  8, 76,  7, 43, 15,
       50, 65, 89, 28, 36, 58, 44, 21, 83, 74,  4, 98, 27, 60, 49, 51, 99,
       41, 12, 20, 34, 94,  9, 68, 57, 90, 16, 91, 86, 95, 82, 66, 40, 87,
       73, 71, 46, 84, 78, 80, 38, 47, 10, 29, 22,  2, 26, 13, 62, 97, 96,
       85, 19, 31, 24, 79, 48,  6, 64, 18, 39, 77, 93,  1, 61, 25]), 'cur_cost': 180622.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [54, 70, 63, 92, 20, 83, 93, 97, 24, 1, 33, 57, 5, 88, 72, 8, 28, 95, 85, 40, 56, 60, 7, 69, 18, 17, 22, 9, 74, 14, 11, 50, 29, 53, 27, 90, 37, 62, 46, 89, 25, 41, 48, 15, 96, 78, 58, 64, 3, 0, 71, 31, 23, 30, 79, 87, 21, 68, 49, 82, 6, 47, 4, 84, 36, 51, 38, 86, 26, 59, 45, 19, 10, 80, 2, 43, 42, 61, 13, 99, 67, 32, 66, 16, 76, 77, 34, 73, 55, 91, 35, 44, 52, 65, 98, 39, 94, 12, 75, 81], 'cur_cost': 105498.0, 'intermediate_solutions': [{'tour': [1, 53, 12, 6, 44, 62, 64, 21, 52, 41, 74, 98, 15, 30, 72, 51, 33, 19, 11, 54, 2, 84, 99, 94, 32, 82, 60, 59, 86, 13, 28, 75, 76, 91, 23, 22, 97, 50, 10, 73, 78, 3, 31, 27, 66, 48, 89, 18, 83, 69, 71, 90, 16, 35, 87, 7, 65, 57, 67, 29, 40, 45, 42, 70, 8, 56, 34, 85, 80, 24, 4, 68, 0, 9, 58, 96, 46, 79, 20, 93, 39, 43, 95, 77, 49, 26, 36, 63, 38, 61, 92, 5, 55, 88, 25, 37, 17, 14, 47, 81], 'cur_cost': 96065.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 53, 12, 6, 44, 62, 64, 21, 52, 41, 74, 98, 15, 30, 72, 51, 33, 19, 11, 54, 2, 84, 99, 94, 32, 82, 60, 59, 86, 13, 28, 75, 76, 91, 23, 22, 61, 50, 10, 73, 78, 3, 31, 27, 66, 48, 89, 18, 83, 69, 71, 90, 16, 35, 87, 7, 0, 68, 4, 24, 80, 85, 34, 56, 8, 70, 42, 45, 40, 29, 67, 57, 65, 9, 58, 96, 46, 79, 20, 93, 39, 43, 95, 77, 49, 26, 36, 63, 38, 97, 92, 5, 55, 88, 25, 37, 17, 14, 47, 81], 'cur_cost': 96018.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 53, 12, 6, 44, 62, 64, 21, 52, 41, 74, 98, 15, 30, 72, 51, 33, 19, 11, 8, 54, 2, 84, 99, 94, 32, 82, 60, 59, 86, 13, 28, 75, 76, 91, 23, 22, 61, 50, 10, 73, 78, 3, 31, 27, 66, 48, 89, 18, 83, 69, 71, 90, 16, 35, 87, 7, 65, 57, 67, 29, 40, 45, 42, 70, 56, 34, 85, 80, 24, 4, 68, 0, 9, 58, 96, 46, 79, 20, 93, 39, 43, 95, 77, 49, 26, 36, 63, 38, 97, 92, 5, 55, 88, 25, 37, 17, 14, 47, 81], 'cur_cost': 96614.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 7, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 42, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57], 'cur_cost': 30570.0, 'intermediate_solutions': [{'tour': [83, 23, 91, 22, 11, 61, 58, 52, 73, 31, 44, 26, 72, 56, 34, 85, 27, 4, 71, 69, 3, 35, 48, 41, 88, 68, 33, 13, 45, 40, 47, 94, 8, 77, 19, 38, 39, 74, 81, 32, 1, 67, 36, 42, 29, 70, 6, 28, 63, 84, 24, 62, 10, 92, 80, 86, 60, 0, 78, 55, 46, 59, 5, 90, 17, 25, 37, 96, 9, 15, 16, 20, 64, 87, 66, 57, 49, 51, 82, 99, 50, 76, 14, 21, 93, 30, 18, 89, 53, 79, 97, 7, 54, 43, 95, 12, 75, 2, 98, 65], 'cur_cost': 106911.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [83, 23, 60, 86, 80, 92, 10, 62, 24, 84, 63, 28, 6, 70, 29, 42, 36, 67, 1, 32, 81, 4, 39, 38, 19, 77, 8, 94, 47, 40, 45, 13, 33, 68, 88, 41, 48, 35, 3, 69, 71, 74, 27, 85, 34, 56, 72, 26, 44, 31, 73, 52, 58, 61, 11, 22, 91, 0, 78, 55, 46, 59, 5, 90, 17, 25, 37, 96, 9, 15, 16, 20, 64, 87, 66, 57, 49, 51, 82, 99, 50, 76, 14, 21, 93, 30, 18, 89, 53, 79, 97, 7, 54, 43, 95, 12, 75, 2, 98, 65], 'cur_cost': 101644.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [83, 23, 91, 22, 11, 61, 58, 52, 73, 31, 26, 72, 56, 34, 85, 27, 74, 71, 69, 3, 35, 48, 41, 88, 68, 33, 13, 45, 40, 47, 94, 8, 77, 19, 38, 39, 4, 81, 32, 1, 67, 36, 42, 29, 70, 6, 28, 63, 84, 24, 62, 10, 92, 80, 86, 60, 0, 78, 55, 46, 59, 5, 90, 17, 25, 37, 96, 9, 15, 16, 20, 64, 87, 66, 57, 49, 51, 82, 99, 50, 76, 14, 21, 93, 30, 18, 89, 53, 79, 44, 97, 7, 54, 43, 95, 12, 75, 2, 98, 65], 'cur_cost': 101700.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 17, 1, 43, 49, 72, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 63, 39, 53, 68, 80, 24, 60, 50, 86, 8, 6, 56, 19, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 54, 82, 33, 28, 45, 2, 42, 70, 40, 99, 47], 'cur_cost': 35485.0, 'intermediate_solutions': [{'tour': [0, 6, 18, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 90, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 61, 59, 76, 22, 97, 67, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 39272.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 18, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 62, 48, 89, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 33705.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 18, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 23, 16, 73, 20, 58, 71, 9, 83, 35, 37, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 35949.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 21, 14, 16, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 91, 74, 18, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 31265.0, 'intermediate_solutions': [{'tour': [0, 13, 10, 19, 11, 26, 85, 34, 46, 59, 76, 22, 97, 90, 44, 31, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 61, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 2, 45, 28, 33, 82, 54, 6, 8, 56, 86, 50, 42], 'cur_cost': 36627.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 10, 19, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 6, 54, 82, 33, 28, 45, 2, 40, 70, 99, 47, 81, 94, 12, 75, 32, 36, 4, 51, 77, 95, 29, 38, 84, 67, 63, 39, 53, 1, 43, 49, 72, 68, 80, 24, 60, 57, 66, 27, 92, 46, 98, 25, 74, 18, 89, 62, 48, 5, 91, 7, 41, 8, 56, 86, 50, 42], 'cur_cost': 37128.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 10, 19, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 31, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 2, 45, 28, 33, 82, 54, 6, 8, 56, 86, 50, 42], 'cur_cost': 35813.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([31,  6, 88, 97, 14, 85, 39, 43, 73, 58, 64, 84, 41, 22, 99, 37, 98,
       95,  7, 23, 28, 81, 35, 29, 46, 59, 68, 69, 19, 71, 94, 42, 10, 56,
       51, 96, 79,  3, 47, 90, 89, 67, 49,  2, 38, 18, 26,  4, 16, 60, 65,
       50, 53, 70, 36, 12, 24, 77, 25, 11, 83, 87, 40,  5, 45,  0, 55, 61,
       74, 82, 44, 66, 30, 78, 93, 48, 76, 17, 62, 21, 72, 34, 52,  8, 32,
       86, 33, 54, 75, 91, 20, 27, 80,  9, 13, 15, 57,  1, 63, 92],
      dtype=int64), 'cur_cost': 178575.0, 'intermediate_solutions': [{'tour': array([80,  0, 64, 40, 70, 79, 41, 34, 89, 67, 29, 66, 91, 32, 11, 77, 14,
       73, 71, 27, 22,  7, 38, 78, 20,  8, 46, 90, 58, 87, 47, 13,  9, 61,
       97, 26, 30, 99,  2, 98, 52, 56,  4, 31, 39, 57, 85, 23, 68, 45,  3,
       60, 82, 65, 33, 49,  5, 21, 37,  1, 76, 25, 53, 63, 42, 59, 55, 19,
       93, 16, 72, 51, 92, 54, 74, 81, 17, 94, 44, 18, 83, 24, 84, 50, 36,
        6, 96, 69, 88, 28, 75, 12, 62, 15, 35, 86, 10, 95, 48, 43]), 'cur_cost': 172754.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([40, 80,  0, 64, 70, 79, 41, 34, 89, 67, 29, 66, 91, 32, 11, 77, 14,
       73, 71, 27, 22,  7, 38, 78, 20,  8, 46, 90, 58, 87, 47, 13,  9, 61,
       97, 26, 30, 99,  2, 98, 52, 56,  4, 31, 39, 57, 85, 23, 68, 45,  3,
       60, 82, 65, 33, 49,  5, 21, 37,  1, 76, 25, 53, 63, 42, 59, 55, 19,
       93, 16, 72, 51, 92, 54, 74, 81, 17, 94, 44, 18, 83, 24, 84, 50, 36,
        6, 96, 69, 88, 28, 75, 12, 62, 15, 35, 86, 10, 95, 48, 43]), 'cur_cost': 175279.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([70, 40, 80,  0, 64, 79, 41, 34, 89, 67, 29, 66, 91, 32, 11, 77, 14,
       73, 71, 27, 22,  7, 38, 78, 20,  8, 46, 90, 58, 87, 47, 13,  9, 61,
       97, 26, 30, 99,  2, 98, 52, 56,  4, 31, 39, 57, 85, 23, 68, 45,  3,
       60, 82, 65, 33, 49,  5, 21, 37,  1, 76, 25, 53, 63, 42, 59, 55, 19,
       93, 16, 72, 51, 92, 54, 74, 81, 17, 94, 44, 18, 83, 24, 84, 50, 36,
        6, 96, 69, 88, 28, 75, 12, 62, 15, 35, 86, 10, 95, 48, 43]), 'cur_cost': 169143.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([64, 40, 80,  0, 70, 79, 41, 34, 89, 67, 29, 66, 91, 32, 11, 77, 14,
       73, 71, 27, 22,  7, 38, 78, 20,  8, 46, 90, 58, 87, 47, 13,  9, 61,
       97, 26, 30, 99,  2, 98, 52, 56,  4, 31, 39, 57, 85, 23, 68, 45,  3,
       60, 82, 65, 33, 49,  5, 21, 37,  1, 76, 25, 53, 63, 42, 59, 55, 19,
       93, 16, 72, 51, 92, 54, 74, 81, 17, 94, 44, 18, 83, 24, 84, 50, 36,
        6, 96, 69, 88, 28, 75, 12, 62, 15, 35, 86, 10, 95, 48, 43]), 'cur_cost': 177605.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([64, 70, 40, 80,  0, 79, 41, 34, 89, 67, 29, 66, 91, 32, 11, 77, 14,
       73, 71, 27, 22,  7, 38, 78, 20,  8, 46, 90, 58, 87, 47, 13,  9, 61,
       97, 26, 30, 99,  2, 98, 52, 56,  4, 31, 39, 57, 85, 23, 68, 45,  3,
       60, 82, 65, 33, 49,  5, 21, 37,  1, 76, 25, 53, 63, 42, 59, 55, 19,
       93, 16, 72, 51, 92, 54, 74, 81, 17, 94, 44, 18, 83, 24, 84, 50, 36,
        6, 96, 69, 88, 28, 75, 12, 62, 15, 35, 86, 10, 95, 48, 43]), 'cur_cost': 172696.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 12, 5, 48, 62, 91, 7, 41, 88, 30, 79, 55, 96, 74, 18, 52, 78, 87, 15, 21, 93, 69, 65, 64, 3, 25, 89, 9, 83, 71, 20, 73, 58, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42, 35, 37, 23, 98], 'cur_cost': 35239.0, 'intermediate_solutions': [{'tour': [38, 72, 29, 76, 24, 28, 19, 2, 4, 39, 79, 74, 9, 64, 62, 27, 7, 63, 6, 40, 60, 66, 11, 49, 13, 94, 99, 34, 46, 91, 37, 92, 89, 31, 8, 68, 80, 44, 18, 15, 55, 65, 96, 69, 5, 97, 54, 1, 51, 36, 70, 22, 59, 23, 85, 33, 26, 14, 10, 16, 71, 3, 30, 53, 81, 77, 32, 42, 67, 90, 20, 61, 0, 48, 41, 21, 78, 58, 52, 73, 83, 17, 86, 56, 43, 95, 75, 12, 57, 84, 50, 82, 35, 93, 98, 25, 87, 88, 45, 47], 'cur_cost': 105407.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [38, 78, 21, 41, 48, 0, 61, 20, 90, 67, 42, 32, 77, 81, 53, 30, 3, 71, 16, 10, 14, 26, 33, 85, 27, 59, 22, 70, 36, 51, 1, 54, 97, 5, 69, 96, 65, 55, 15, 18, 44, 80, 68, 8, 31, 89, 92, 37, 91, 46, 34, 99, 94, 13, 49, 11, 66, 60, 40, 6, 63, 7, 23, 62, 64, 9, 74, 79, 39, 4, 2, 19, 28, 24, 76, 29, 72, 58, 52, 73, 83, 17, 86, 56, 43, 95, 75, 12, 57, 84, 50, 82, 35, 93, 98, 25, 87, 88, 45, 47], 'cur_cost': 107995.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [38, 72, 29, 76, 24, 28, 19, 2, 4, 39, 79, 74, 9, 64, 62, 23, 7, 63, 40, 60, 66, 11, 49, 13, 94, 99, 34, 46, 91, 37, 92, 89, 31, 8, 68, 80, 44, 18, 15, 55, 65, 96, 69, 5, 6, 97, 54, 1, 51, 36, 70, 22, 59, 27, 85, 33, 26, 14, 10, 16, 71, 3, 30, 53, 81, 77, 32, 42, 67, 90, 20, 61, 0, 48, 41, 21, 78, 58, 52, 73, 83, 17, 86, 56, 43, 95, 75, 12, 57, 84, 50, 82, 35, 93, 98, 25, 87, 88, 45, 47], 'cur_cost': 105505.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:22,549 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:22,549 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:22,554 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=30570.000, 多样性=0.989
2025-08-05 09:52:22,554 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 09:52:22,554 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 09:52:22,554 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:22,556 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.05255189068448968, 'best_improvement': -0.03901842158928693}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.0006735518634933027}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.011748994706237742, 'recent_improvements': [-0.030883518161605894, 0.09002312728986465, -0.007385528749130407], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 7, 'new_count': 7, 'count_change': 0, 'old_best_cost': 21389, 'new_best_cost': 21389, 'quality_improvement': 0.0, 'old_diversity': 0.919047619047619, 'new_diversity': 0.919047619047619, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:52:22,556 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 09:52:22,557 - __main__ - INFO - kroA100 开始进化第 4 代
2025-08-05 09:52:22,557 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 09:52:22,557 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:22,558 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=30570.000, 多样性=0.989
2025-08-05 09:52:22,559 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:22,563 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.989
2025-08-05 09:52:22,564 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:22,566 - EliteExpert - INFO - 精英解分析完成: 精英解数量=7, 多样性=0.919
2025-08-05 09:52:22,569 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 09:52:22,569 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:22,569 - LandscapeExpert - INFO - 添加精英解数据: 7个精英解
2025-08-05 09:52:22,569 - LandscapeExpert - INFO - 数据提取成功: 17个路径, 17个适应度值
2025-08-05 09:52:22,649 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.118, 适应度梯度: -14038.012, 聚类评分: 0.000, 覆盖率: 0.168, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:22,649 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 09:52:22,649 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:22,650 - visualization.landscape_visualizer - INFO - 设置当前实例名: kroA100
2025-08-05 09:52:22,657 - visualization.landscape_visualizer - INFO - 插值约束: 213 个点被约束到最小值 21389.00
2025-08-05 09:52:22,779 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_kroA100\landscape_kroA100_iter_149_20250805_095222.html
2025-08-05 09:52:22,848 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_kroA100\dashboard_kroA100_iter_149_20250805_095222.html
2025-08-05 09:52:22,849 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 149
2025-08-05 09:52:22,849 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 09:52:22,849 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2818秒
2025-08-05 09:52:22,849 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.11764705882352941, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -14038.011764705876, 'local_optima_density': 0.11764705882352941, 'gradient_variance': 4223914926.919862, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1684, 'fitness_entropy': 0.6949883477962157, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -14038.012)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.168)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358742.6496704, 'performance_metrics': {}}}
2025-08-05 09:52:22,849 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:22,850 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:22,850 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:22,850 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:22,850 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:52:22,850 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 09:52:22,851 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:52:22,851 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:22,851 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:22,851 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:52:22,851 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:22,851 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:22,851 - experts.management.collaboration_manager - INFO - 识别精英个体: {5, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:22,852 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-05 09:52:22,852 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:22,852 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:22,852 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 180941.0
2025-08-05 09:52:22,879 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:22,879 - ExploitationExpert - INFO - res_population_costs: [21389, 21411, 21776, 22282, 22308, 22356, 22427.0]
2025-08-05 09:52:22,879 - ExploitationExpert - INFO - res_populations: [array([ 0, 62, 89, 48,  5, 91,  7, 41, 88, 30, 79, 55, 96, 74, 18,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26,
       11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99, 47,
       51, 77, 95,  4, 36, 32, 75, 12, 94, 81, 38, 29, 84, 67, 72, 49, 43,
        1, 53, 39, 63, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 62,  5, 48, 89, 18, 74, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 59, 61, 76, 50, 86,  8,
        6, 56, 19, 85, 34, 26, 11, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40,
       99, 47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81, 49, 43,  1,
       53, 39, 63, 68, 72, 67, 84, 80, 24, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 91, 74, 96, 55, 79, 30, 88, 41,  7, 66, 27, 92, 57, 60, 24, 80,
       84, 67, 72, 68, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75, 32, 36,  4,
       51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28, 33, 82, 54,
       11, 26, 34, 85, 19, 56,  6,  8, 86, 50, 76, 61, 59, 22, 97, 90, 44,
       31, 10, 14, 16, 58, 73, 20, 71,  9, 83, 35, 98, 37, 23, 17, 78, 52,
       87, 15, 93, 21, 69, 65, 25, 64,  3, 18, 89, 48,  5, 62, 46],
      dtype=int64), array([ 0, 46, 92, 27, 66, 57, 68, 63, 39, 53,  1, 43, 72, 49, 81, 94, 12,
       75, 32, 36,  4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45,
       28, 33, 82, 54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 80, 24, 60,
       50, 86, 76, 61, 59, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20, 71,
        9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3, 18,
       52, 78, 89, 74, 96, 55, 79, 30, 88, 41,  7, 91, 48,  5, 62],
      dtype=int64), array([ 0, 62, 46, 92, 27, 57, 66, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75,
       32, 36,  4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28,
       33, 82, 54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 72, 68, 80, 24,
       60, 50, 86, 76, 61, 59, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20,
       71,  9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3,
       18, 52, 78, 89, 48,  5, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 62,  5, 48, 91,  7, 41, 88, 30, 79, 55, 96, 74, 89, 78, 52, 18,
        3, 64, 25, 65, 69, 21, 15, 87, 93, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 59, 61, 76, 86, 50, 60,
       24, 80, 68, 72, 67, 84,  8,  6, 56, 19, 85, 34, 26, 11, 54, 82, 33,
       28, 45, 42,  2, 13, 70, 40, 99, 47, 29, 38, 95, 77, 51,  4, 36, 32,
       75, 12, 94, 81, 49, 43,  1, 53, 39, 63, 66, 57, 27, 92, 46],
      dtype=int64), array([ 0, 92, 27, 57, 66, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75, 32, 36,
        4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28, 33, 82,
       54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 72, 68, 80, 24, 60, 50,
       86, 76, 61, 59, 22, 97, 44, 90, 46, 31, 10, 14, 16, 58, 73, 20, 71,
        9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3, 18,
       52, 78, 89, 74, 96, 55, 79, 30, 88, 41,  7, 91, 48,  5, 62],
      dtype=int64)]
2025-08-05 09:52:22,883 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:22,883 - ExploitationExpert - INFO - populations: [{'tour': array([ 4, 97, 58,  8, 22, 68, 47, 50, 64, 89, 77, 46, 59, 48, 16, 27, 12,
       86, 83, 90,  3, 45, 26, 57, 43, 92, 70,  9, 55, 93, 56, 33, 95, 67,
       61, 19, 72, 98, 87, 30, 36, 82, 78, 28,  0, 75, 21,  2, 62,  7, 79,
       91, 52, 39, 23, 69, 40, 10, 42, 74, 13, 71, 15, 54, 60,  1,  5, 84,
        6, 24, 76, 96, 32, 63, 66, 44, 25, 99, 65, 80, 81, 31, 38, 88, 17,
       51, 85, 14, 29, 41, 53, 49, 18, 11, 73, 37, 35, 20, 94, 34],
      dtype=int64), 'cur_cost': 180941.0}, {'tour': [85, 22, 66, 50, 77, 38, 6, 46, 37, 30, 17, 90, 48, 14, 23, 10, 97, 68, 76, 60, 0, 7, 79, 83, 58, 27, 86, 80, 34, 42, 28, 13, 2, 63, 75, 54, 53, 94, 51, 82, 43, 41, 3, 93, 65, 15, 88, 25, 87, 57, 74, 21, 71, 55, 1, 49, 92, 89, 96, 5, 18, 64, 39, 32, 84, 95, 36, 99, 11, 67, 12, 19, 26, 40, 70, 59, 24, 33, 29, 8, 47, 72, 61, 44, 31, 91, 78, 62, 35, 98, 9, 20, 73, 16, 52, 56, 45, 4, 81, 69], 'cur_cost': 102636.0}, {'tour': [66, 68, 85, 92, 55, 30, 91, 97, 34, 80, 2, 38, 50, 27, 26, 8, 53, 41, 89, 22, 7, 24, 77, 61, 86, 67, 39, 94, 57, 19, 72, 60, 42, 47, 6, 63, 45, 33, 56, 76, 83, 14, 9, 71, 17, 96, 23, 20, 5, 74, 3, 35, 16, 54, 59, 29, 99, 75, 90, 0, 62, 64, 46, 98, 73, 10, 48, 44, 79, 87, 58, 21, 15, 78, 37, 69, 93, 65, 49, 1, 51, 70, 40, 11, 82, 43, 36, 32, 12, 4, 81, 95, 84, 88, 25, 52, 18, 31, 28, 13], 'cur_cost': 96883.0}, {'tour': [59, 40, 6, 8, 91, 75, 42, 72, 88, 92, 18, 39, 52, 46, 45, 25, 13, 84, 41, 56, 0, 12, 11, 74, 93, 81, 49, 27, 65, 69, 66, 26, 3, 24, 50, 7, 76, 94, 87, 28, 82, 98, 83, 80, 34, 71, 16, 22, 55, 62, 47, 31, 35, 58, 90, 73, 97, 61, 36, 78, 96, 51, 79, 57, 15, 21, 17, 53, 67, 95, 19, 77, 23, 9, 30, 99, 32, 63, 89, 48, 70, 14, 5, 29, 4, 1, 43, 44, 38, 64, 85, 20, 68, 33, 86, 37, 2, 54, 10, 60], 'cur_cost': 165064.0}, {'tour': [54, 70, 63, 92, 20, 83, 93, 97, 24, 1, 33, 57, 5, 88, 72, 8, 28, 95, 85, 40, 56, 60, 7, 69, 18, 17, 22, 9, 74, 14, 11, 50, 29, 53, 27, 90, 37, 62, 46, 89, 25, 41, 48, 15, 96, 78, 58, 64, 3, 0, 71, 31, 23, 30, 79, 87, 21, 68, 49, 82, 6, 47, 4, 84, 36, 51, 38, 86, 26, 59, 45, 19, 10, 80, 2, 43, 42, 61, 13, 99, 67, 32, 66, 16, 76, 77, 34, 73, 55, 91, 35, 44, 52, 65, 98, 39, 94, 12, 75, 81], 'cur_cost': 105498.0}, {'tour': [0, 16, 7, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 42, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57], 'cur_cost': 30570.0}, {'tour': [0, 13, 17, 1, 43, 49, 72, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 63, 39, 53, 68, 80, 24, 60, 50, 86, 8, 6, 56, 19, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 54, 82, 33, 28, 45, 2, 42, 70, 40, 99, 47], 'cur_cost': 35485.0}, {'tour': [0, 7, 21, 14, 16, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 91, 74, 18, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 31265.0}, {'tour': [31, 6, 88, 97, 14, 85, 39, 43, 73, 58, 64, 84, 41, 22, 99, 37, 98, 95, 7, 23, 28, 81, 35, 29, 46, 59, 68, 69, 19, 71, 94, 42, 10, 56, 51, 96, 79, 3, 47, 90, 89, 67, 49, 2, 38, 18, 26, 4, 16, 60, 65, 50, 53, 70, 36, 12, 24, 77, 25, 11, 83, 87, 40, 5, 45, 0, 55, 61, 74, 82, 44, 66, 30, 78, 93, 48, 76, 17, 62, 21, 72, 34, 52, 8, 32, 86, 33, 54, 75, 91, 20, 27, 80, 9, 13, 15, 57, 1, 63, 92], 'cur_cost': 178575.0}, {'tour': [0, 17, 12, 5, 48, 62, 91, 7, 41, 88, 30, 79, 55, 96, 74, 18, 52, 78, 87, 15, 21, 93, 69, 65, 64, 3, 25, 89, 9, 83, 71, 20, 73, 58, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42, 35, 37, 23, 98], 'cur_cost': 35239.0}]
2025-08-05 09:52:22,884 - ExploitationExpert - INFO - 局部搜索耗时: 0.03秒，最大迭代次数: 10
2025-08-05 09:52:22,885 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 385, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 385, 'cache_hits': 0, 'similarity_calculations': 2008, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:22,886 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([ 4, 97, 58,  8, 22, 68, 47, 50, 64, 89, 77, 46, 59, 48, 16, 27, 12,
       86, 83, 90,  3, 45, 26, 57, 43, 92, 70,  9, 55, 93, 56, 33, 95, 67,
       61, 19, 72, 98, 87, 30, 36, 82, 78, 28,  0, 75, 21,  2, 62,  7, 79,
       91, 52, 39, 23, 69, 40, 10, 42, 74, 13, 71, 15, 54, 60,  1,  5, 84,
        6, 24, 76, 96, 32, 63, 66, 44, 25, 99, 65, 80, 81, 31, 38, 88, 17,
       51, 85, 14, 29, 41, 53, 49, 18, 11, 73, 37, 35, 20, 94, 34],
      dtype=int64), 'cur_cost': 180941.0, 'intermediate_solutions': [{'tour': array([26, 87, 33,  4, 92, 64, 39, 29, 25, 78, 36, 41, 94, 35, 38, 90, 72,
       66, 30, 85, 32,  2, 73, 76, 11, 88, 96, 97, 83,  9, 51, 68,  7, 91,
        3, 13,  1, 67, 77, 52, 84, 54, 61, 95, 86, 37, 59, 12, 44, 31, 71,
       56,  0, 17, 28, 49, 19, 74, 75, 89, 21, 55, 15, 43, 16, 46, 80, 98,
       48, 53, 60,  8, 40, 23, 82, 10,  6, 20, 70, 62, 34, 24, 47, 57, 14,
       18, 42, 69, 79, 45, 58, 63, 65, 27, 50, 93, 99,  5, 22, 81]), 'cur_cost': 186261.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 4, 26, 87, 33, 92, 64, 39, 29, 25, 78, 36, 41, 94, 35, 38, 90, 72,
       66, 30, 85, 32,  2, 73, 76, 11, 88, 96, 97, 83,  9, 51, 68,  7, 91,
        3, 13,  1, 67, 77, 52, 84, 54, 61, 95, 86, 37, 59, 12, 44, 31, 71,
       56,  0, 17, 28, 49, 19, 74, 75, 89, 21, 55, 15, 43, 16, 46, 80, 98,
       48, 53, 60,  8, 40, 23, 82, 10,  6, 20, 70, 62, 34, 24, 47, 57, 14,
       18, 42, 69, 79, 45, 58, 63, 65, 27, 50, 93, 99,  5, 22, 81]), 'cur_cost': 184895.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([92,  4, 26, 87, 33, 64, 39, 29, 25, 78, 36, 41, 94, 35, 38, 90, 72,
       66, 30, 85, 32,  2, 73, 76, 11, 88, 96, 97, 83,  9, 51, 68,  7, 91,
        3, 13,  1, 67, 77, 52, 84, 54, 61, 95, 86, 37, 59, 12, 44, 31, 71,
       56,  0, 17, 28, 49, 19, 74, 75, 89, 21, 55, 15, 43, 16, 46, 80, 98,
       48, 53, 60,  8, 40, 23, 82, 10,  6, 20, 70, 62, 34, 24, 47, 57, 14,
       18, 42, 69, 79, 45, 58, 63, 65, 27, 50, 93, 99,  5, 22, 81]), 'cur_cost': 188161.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([33,  4, 26, 87, 92, 64, 39, 29, 25, 78, 36, 41, 94, 35, 38, 90, 72,
       66, 30, 85, 32,  2, 73, 76, 11, 88, 96, 97, 83,  9, 51, 68,  7, 91,
        3, 13,  1, 67, 77, 52, 84, 54, 61, 95, 86, 37, 59, 12, 44, 31, 71,
       56,  0, 17, 28, 49, 19, 74, 75, 89, 21, 55, 15, 43, 16, 46, 80, 98,
       48, 53, 60,  8, 40, 23, 82, 10,  6, 20, 70, 62, 34, 24, 47, 57, 14,
       18, 42, 69, 79, 45, 58, 63, 65, 27, 50, 93, 99,  5, 22, 81]), 'cur_cost': 184035.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([33, 92,  4, 26, 87, 64, 39, 29, 25, 78, 36, 41, 94, 35, 38, 90, 72,
       66, 30, 85, 32,  2, 73, 76, 11, 88, 96, 97, 83,  9, 51, 68,  7, 91,
        3, 13,  1, 67, 77, 52, 84, 54, 61, 95, 86, 37, 59, 12, 44, 31, 71,
       56,  0, 17, 28, 49, 19, 74, 75, 89, 21, 55, 15, 43, 16, 46, 80, 98,
       48, 53, 60,  8, 40, 23, 82, 10,  6, 20, 70, 62, 34, 24, 47, 57, 14,
       18, 42, 69, 79, 45, 58, 63, 65, 27, 50, 93, 99,  5, 22, 81]), 'cur_cost': 183778.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:22,886 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 180941.00)
2025-08-05 09:52:22,886 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:22,887 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:22,887 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:22,892 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 09:52:22,892 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,892 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,892 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,893 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,893 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32119.0, 路径长度: 100, 收集中间解: 3
2025-08-05 09:52:22,893 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 19, 16, 1, 43, 49, 72, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 63, 39, 53, 68, 80, 24, 60, 50, 86, 8, 6, 56, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 54, 82, 33, 28, 45, 2, 42, 13, 70, 40, 99, 47], 'cur_cost': 32119.0, 'intermediate_solutions': [{'tour': [85, 22, 66, 50, 77, 38, 6, 46, 48, 30, 17, 90, 37, 14, 23, 10, 97, 68, 76, 60, 0, 7, 79, 83, 58, 27, 86, 80, 34, 42, 28, 13, 2, 63, 75, 54, 53, 94, 51, 82, 43, 41, 3, 93, 65, 15, 88, 25, 87, 57, 74, 21, 71, 55, 1, 49, 92, 89, 96, 5, 18, 64, 39, 32, 84, 95, 36, 99, 11, 67, 12, 19, 26, 40, 70, 59, 24, 33, 29, 8, 47, 72, 61, 44, 31, 91, 78, 62, 35, 98, 9, 20, 73, 16, 52, 56, 45, 4, 81, 69], 'cur_cost': 101716.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [85, 22, 66, 50, 77, 38, 6, 46, 37, 30, 17, 90, 48, 14, 23, 10, 97, 68, 76, 60, 0, 7, 79, 83, 58, 27, 86, 80, 34, 42, 28, 13, 2, 63, 75, 54, 53, 94, 51, 82, 43, 41, 3, 93, 65, 15, 88, 25, 87, 57, 74, 21, 71, 55, 1, 49, 92, 89, 96, 5, 18, 64, 39, 32, 84, 95, 44, 61, 72, 47, 8, 29, 33, 24, 59, 70, 40, 26, 19, 12, 67, 11, 99, 36, 31, 91, 78, 62, 35, 98, 9, 20, 73, 16, 52, 56, 45, 4, 81, 69], 'cur_cost': 107141.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [85, 22, 66, 50, 77, 38, 6, 46, 37, 30, 17, 90, 48, 14, 23, 10, 97, 68, 76, 60, 0, 7, 79, 83, 58, 27, 86, 80, 34, 42, 28, 13, 2, 63, 75, 54, 53, 94, 51, 82, 43, 3, 93, 65, 15, 88, 25, 87, 57, 74, 21, 71, 55, 41, 1, 49, 92, 89, 96, 5, 18, 64, 39, 32, 84, 95, 36, 99, 11, 67, 12, 19, 26, 40, 70, 59, 24, 33, 29, 8, 47, 72, 61, 44, 31, 91, 78, 62, 35, 98, 9, 20, 73, 16, 52, 56, 45, 4, 81, 69], 'cur_cost': 102636.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:22,893 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 32119.00)
2025-08-05 09:52:22,894 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:22,894 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:22,894 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:22,906 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 100
2025-08-05 09:52:22,906 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,907 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,907 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,907 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,908 - ExplorationExpert - INFO - 探索路径生成完成，成本: 105252.0, 路径长度: 100, 收集中间解: 3
2025-08-05 09:52:22,908 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [73, 19, 13, 70, 12, 42, 4, 39, 84, 2, 92, 56, 27, 86, 29, 6, 43, 47, 94, 26, 45, 68, 51, 95, 11, 66, 61, 97, 59, 91, 64, 96, 0, 9, 31, 90, 41, 17, 21, 69, 87, 23, 25, 18, 37, 55, 30, 65, 71, 16, 58, 78, 5, 22, 24, 85, 34, 57, 44, 80, 67, 36, 53, 63, 38, 99, 46, 50, 10, 52, 79, 89, 98, 88, 49, 54, 76, 14, 62, 7, 60, 82, 33, 28, 81, 32, 40, 1, 8, 72, 75, 74, 48, 20, 93, 15, 83, 35, 3, 77], 'cur_cost': 105252.0, 'intermediate_solutions': [{'tour': [66, 68, 85, 92, 55, 30, 91, 97, 34, 80, 2, 38, 50, 27, 26, 8, 53, 41, 89, 22, 7, 24, 77, 61, 86, 67, 39, 94, 57, 19, 72, 60, 42, 47, 6, 63, 45, 33, 56, 76, 83, 14, 9, 71, 17, 96, 23, 20, 5, 74, 3, 35, 16, 54, 59, 29, 99, 75, 90, 0, 62, 64, 46, 98, 73, 10, 48, 44, 79, 87, 58, 21, 15, 78, 37, 69, 93, 65, 49, 1, 51, 70, 40, 11, 52, 43, 36, 32, 12, 4, 81, 95, 84, 88, 25, 82, 18, 31, 28, 13], 'cur_cost': 105103.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [66, 68, 85, 92, 55, 30, 91, 97, 34, 80, 2, 38, 50, 27, 26, 8, 53, 41, 89, 22, 7, 24, 77, 61, 86, 67, 39, 94, 57, 19, 72, 60, 42, 47, 6, 63, 45, 33, 56, 76, 83, 14, 9, 71, 17, 96, 23, 20, 51, 1, 49, 65, 93, 69, 37, 78, 15, 21, 58, 87, 79, 44, 48, 10, 73, 98, 46, 64, 62, 0, 90, 75, 99, 29, 59, 54, 16, 35, 3, 74, 5, 70, 40, 11, 82, 43, 36, 32, 12, 4, 81, 95, 84, 88, 25, 52, 18, 31, 28, 13], 'cur_cost': 101432.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [66, 68, 85, 92, 55, 30, 91, 97, 34, 80, 2, 38, 50, 27, 26, 8, 53, 41, 89, 22, 7, 24, 77, 61, 86, 67, 39, 94, 57, 19, 72, 60, 42, 47, 6, 63, 45, 33, 56, 76, 83, 14, 9, 71, 17, 96, 23, 20, 5, 74, 3, 35, 54, 59, 29, 99, 75, 90, 0, 62, 64, 46, 98, 73, 10, 48, 44, 79, 87, 58, 21, 15, 78, 16, 37, 69, 93, 65, 49, 1, 51, 70, 40, 11, 82, 43, 36, 32, 12, 4, 81, 95, 84, 88, 25, 52, 18, 31, 28, 13], 'cur_cost': 98605.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:22,908 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 105252.00)
2025-08-05 09:52:22,908 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:52:22,909 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:22,909 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:22,909 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 169986.0
2025-08-05 09:52:22,937 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:22,937 - ExploitationExpert - INFO - res_population_costs: [21389, 21411, 21776, 22282, 22308, 22356, 22427.0]
2025-08-05 09:52:22,938 - ExploitationExpert - INFO - res_populations: [array([ 0, 62, 89, 48,  5, 91,  7, 41, 88, 30, 79, 55, 96, 74, 18,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26,
       11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99, 47,
       51, 77, 95,  4, 36, 32, 75, 12, 94, 81, 38, 29, 84, 67, 72, 49, 43,
        1, 53, 39, 63, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 62,  5, 48, 89, 18, 74, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 59, 61, 76, 50, 86,  8,
        6, 56, 19, 85, 34, 26, 11, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40,
       99, 47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81, 49, 43,  1,
       53, 39, 63, 68, 72, 67, 84, 80, 24, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 91, 74, 96, 55, 79, 30, 88, 41,  7, 66, 27, 92, 57, 60, 24, 80,
       84, 67, 72, 68, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75, 32, 36,  4,
       51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28, 33, 82, 54,
       11, 26, 34, 85, 19, 56,  6,  8, 86, 50, 76, 61, 59, 22, 97, 90, 44,
       31, 10, 14, 16, 58, 73, 20, 71,  9, 83, 35, 98, 37, 23, 17, 78, 52,
       87, 15, 93, 21, 69, 65, 25, 64,  3, 18, 89, 48,  5, 62, 46],
      dtype=int64), array([ 0, 46, 92, 27, 66, 57, 68, 63, 39, 53,  1, 43, 72, 49, 81, 94, 12,
       75, 32, 36,  4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45,
       28, 33, 82, 54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 80, 24, 60,
       50, 86, 76, 61, 59, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20, 71,
        9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3, 18,
       52, 78, 89, 74, 96, 55, 79, 30, 88, 41,  7, 91, 48,  5, 62],
      dtype=int64), array([ 0, 62, 46, 92, 27, 57, 66, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75,
       32, 36,  4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28,
       33, 82, 54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 72, 68, 80, 24,
       60, 50, 86, 76, 61, 59, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20,
       71,  9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3,
       18, 52, 78, 89, 48,  5, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 62,  5, 48, 91,  7, 41, 88, 30, 79, 55, 96, 74, 89, 78, 52, 18,
        3, 64, 25, 65, 69, 21, 15, 87, 93, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 59, 61, 76, 86, 50, 60,
       24, 80, 68, 72, 67, 84,  8,  6, 56, 19, 85, 34, 26, 11, 54, 82, 33,
       28, 45, 42,  2, 13, 70, 40, 99, 47, 29, 38, 95, 77, 51,  4, 36, 32,
       75, 12, 94, 81, 49, 43,  1, 53, 39, 63, 66, 57, 27, 92, 46],
      dtype=int64), array([ 0, 92, 27, 57, 66, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75, 32, 36,
        4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28, 33, 82,
       54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 72, 68, 80, 24, 60, 50,
       86, 76, 61, 59, 22, 97, 44, 90, 46, 31, 10, 14, 16, 58, 73, 20, 71,
        9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3, 18,
       52, 78, 89, 74, 96, 55, 79, 30, 88, 41,  7, 91, 48,  5, 62],
      dtype=int64)]
2025-08-05 09:52:22,942 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:22,942 - ExploitationExpert - INFO - populations: [{'tour': array([ 4, 97, 58,  8, 22, 68, 47, 50, 64, 89, 77, 46, 59, 48, 16, 27, 12,
       86, 83, 90,  3, 45, 26, 57, 43, 92, 70,  9, 55, 93, 56, 33, 95, 67,
       61, 19, 72, 98, 87, 30, 36, 82, 78, 28,  0, 75, 21,  2, 62,  7, 79,
       91, 52, 39, 23, 69, 40, 10, 42, 74, 13, 71, 15, 54, 60,  1,  5, 84,
        6, 24, 76, 96, 32, 63, 66, 44, 25, 99, 65, 80, 81, 31, 38, 88, 17,
       51, 85, 14, 29, 41, 53, 49, 18, 11, 73, 37, 35, 20, 94, 34],
      dtype=int64), 'cur_cost': 180941.0}, {'tour': [0, 19, 16, 1, 43, 49, 72, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 63, 39, 53, 68, 80, 24, 60, 50, 86, 8, 6, 56, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 54, 82, 33, 28, 45, 2, 42, 13, 70, 40, 99, 47], 'cur_cost': 32119.0}, {'tour': [73, 19, 13, 70, 12, 42, 4, 39, 84, 2, 92, 56, 27, 86, 29, 6, 43, 47, 94, 26, 45, 68, 51, 95, 11, 66, 61, 97, 59, 91, 64, 96, 0, 9, 31, 90, 41, 17, 21, 69, 87, 23, 25, 18, 37, 55, 30, 65, 71, 16, 58, 78, 5, 22, 24, 85, 34, 57, 44, 80, 67, 36, 53, 63, 38, 99, 46, 50, 10, 52, 79, 89, 98, 88, 49, 54, 76, 14, 62, 7, 60, 82, 33, 28, 81, 32, 40, 1, 8, 72, 75, 74, 48, 20, 93, 15, 83, 35, 3, 77], 'cur_cost': 105252.0}, {'tour': array([99, 18, 88, 41, 50, 43, 77, 59, 58, 37, 20, 16, 13, 95, 33, 29, 44,
       87, 52, 12, 78, 55, 71, 24, 70, 66, 45, 98, 81, 17, 96, 54, 26, 15,
       46, 28, 93,  5, 22, 40,  4, 25, 34, 75, 97, 83, 36, 94, 80, 73,  0,
       90, 49, 57, 84, 74, 10, 60, 14,  3, 38, 89,  6, 85, 35, 30, 82, 56,
       86, 32, 76, 62, 63, 65, 21,  2, 51,  8, 91, 53, 72, 61, 48, 64, 42,
        1, 23, 47, 67, 19, 92,  9, 68, 69, 79, 39, 27, 11,  7, 31],
      dtype=int64), 'cur_cost': 169986.0}, {'tour': [54, 70, 63, 92, 20, 83, 93, 97, 24, 1, 33, 57, 5, 88, 72, 8, 28, 95, 85, 40, 56, 60, 7, 69, 18, 17, 22, 9, 74, 14, 11, 50, 29, 53, 27, 90, 37, 62, 46, 89, 25, 41, 48, 15, 96, 78, 58, 64, 3, 0, 71, 31, 23, 30, 79, 87, 21, 68, 49, 82, 6, 47, 4, 84, 36, 51, 38, 86, 26, 59, 45, 19, 10, 80, 2, 43, 42, 61, 13, 99, 67, 32, 66, 16, 76, 77, 34, 73, 55, 91, 35, 44, 52, 65, 98, 39, 94, 12, 75, 81], 'cur_cost': 105498.0}, {'tour': [0, 16, 7, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 42, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57], 'cur_cost': 30570.0}, {'tour': [0, 13, 17, 1, 43, 49, 72, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 63, 39, 53, 68, 80, 24, 60, 50, 86, 8, 6, 56, 19, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 54, 82, 33, 28, 45, 2, 42, 70, 40, 99, 47], 'cur_cost': 35485.0}, {'tour': [0, 7, 21, 14, 16, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 91, 74, 18, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 31265.0}, {'tour': [31, 6, 88, 97, 14, 85, 39, 43, 73, 58, 64, 84, 41, 22, 99, 37, 98, 95, 7, 23, 28, 81, 35, 29, 46, 59, 68, 69, 19, 71, 94, 42, 10, 56, 51, 96, 79, 3, 47, 90, 89, 67, 49, 2, 38, 18, 26, 4, 16, 60, 65, 50, 53, 70, 36, 12, 24, 77, 25, 11, 83, 87, 40, 5, 45, 0, 55, 61, 74, 82, 44, 66, 30, 78, 93, 48, 76, 17, 62, 21, 72, 34, 52, 8, 32, 86, 33, 54, 75, 91, 20, 27, 80, 9, 13, 15, 57, 1, 63, 92], 'cur_cost': 178575.0}, {'tour': [0, 17, 12, 5, 48, 62, 91, 7, 41, 88, 30, 79, 55, 96, 74, 18, 52, 78, 87, 15, 21, 93, 69, 65, 64, 3, 25, 89, 9, 83, 71, 20, 73, 58, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42, 35, 37, 23, 98], 'cur_cost': 35239.0}]
2025-08-05 09:52:22,943 - ExploitationExpert - INFO - 局部搜索耗时: 0.03秒，最大迭代次数: 10
2025-08-05 09:52:22,943 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 386, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 386, 'cache_hits': 0, 'similarity_calculations': 2016, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:22,944 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([99, 18, 88, 41, 50, 43, 77, 59, 58, 37, 20, 16, 13, 95, 33, 29, 44,
       87, 52, 12, 78, 55, 71, 24, 70, 66, 45, 98, 81, 17, 96, 54, 26, 15,
       46, 28, 93,  5, 22, 40,  4, 25, 34, 75, 97, 83, 36, 94, 80, 73,  0,
       90, 49, 57, 84, 74, 10, 60, 14,  3, 38, 89,  6, 85, 35, 30, 82, 56,
       86, 32, 76, 62, 63, 65, 21,  2, 51,  8, 91, 53, 72, 61, 48, 64, 42,
        1, 23, 47, 67, 19, 92,  9, 68, 69, 79, 39, 27, 11,  7, 31],
      dtype=int64), 'cur_cost': 169986.0, 'intermediate_solutions': [{'tour': array([ 6, 40, 59,  8, 91, 75, 42, 72, 88, 92, 18, 39, 52, 46, 45, 25, 13,
       84, 41, 56,  0, 12, 11, 74, 93, 81, 49, 27, 65, 69, 66, 26,  3, 24,
       50,  7, 76, 94, 87, 28, 82, 98, 83, 80, 34, 71, 16, 22, 55, 62, 47,
       31, 35, 58, 90, 73, 97, 61, 36, 78, 96, 51, 79, 57, 15, 21, 17, 53,
       67, 95, 19, 77, 23,  9, 30, 99, 32, 63, 89, 48, 70, 14,  5, 29,  4,
        1, 43, 44, 38, 64, 85, 20, 68, 33, 86, 37,  2, 54, 10, 60]), 'cur_cost': 165417.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 8,  6, 40, 59, 91, 75, 42, 72, 88, 92, 18, 39, 52, 46, 45, 25, 13,
       84, 41, 56,  0, 12, 11, 74, 93, 81, 49, 27, 65, 69, 66, 26,  3, 24,
       50,  7, 76, 94, 87, 28, 82, 98, 83, 80, 34, 71, 16, 22, 55, 62, 47,
       31, 35, 58, 90, 73, 97, 61, 36, 78, 96, 51, 79, 57, 15, 21, 17, 53,
       67, 95, 19, 77, 23,  9, 30, 99, 32, 63, 89, 48, 70, 14,  5, 29,  4,
        1, 43, 44, 38, 64, 85, 20, 68, 33, 86, 37,  2, 54, 10, 60]), 'cur_cost': 164496.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([91,  8,  6, 40, 59, 75, 42, 72, 88, 92, 18, 39, 52, 46, 45, 25, 13,
       84, 41, 56,  0, 12, 11, 74, 93, 81, 49, 27, 65, 69, 66, 26,  3, 24,
       50,  7, 76, 94, 87, 28, 82, 98, 83, 80, 34, 71, 16, 22, 55, 62, 47,
       31, 35, 58, 90, 73, 97, 61, 36, 78, 96, 51, 79, 57, 15, 21, 17, 53,
       67, 95, 19, 77, 23,  9, 30, 99, 32, 63, 89, 48, 70, 14,  5, 29,  4,
        1, 43, 44, 38, 64, 85, 20, 68, 33, 86, 37,  2, 54, 10, 60]), 'cur_cost': 165159.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([59,  8,  6, 40, 91, 75, 42, 72, 88, 92, 18, 39, 52, 46, 45, 25, 13,
       84, 41, 56,  0, 12, 11, 74, 93, 81, 49, 27, 65, 69, 66, 26,  3, 24,
       50,  7, 76, 94, 87, 28, 82, 98, 83, 80, 34, 71, 16, 22, 55, 62, 47,
       31, 35, 58, 90, 73, 97, 61, 36, 78, 96, 51, 79, 57, 15, 21, 17, 53,
       67, 95, 19, 77, 23,  9, 30, 99, 32, 63, 89, 48, 70, 14,  5, 29,  4,
        1, 43, 44, 38, 64, 85, 20, 68, 33, 86, 37,  2, 54, 10, 60]), 'cur_cost': 165198.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([59, 91,  8,  6, 40, 75, 42, 72, 88, 92, 18, 39, 52, 46, 45, 25, 13,
       84, 41, 56,  0, 12, 11, 74, 93, 81, 49, 27, 65, 69, 66, 26,  3, 24,
       50,  7, 76, 94, 87, 28, 82, 98, 83, 80, 34, 71, 16, 22, 55, 62, 47,
       31, 35, 58, 90, 73, 97, 61, 36, 78, 96, 51, 79, 57, 15, 21, 17, 53,
       67, 95, 19, 77, 23,  9, 30, 99, 32, 63, 89, 48, 70, 14,  5, 29,  4,
        1, 43, 44, 38, 64, 85, 20, 68, 33, 86, 37,  2, 54, 10, 60]), 'cur_cost': 163571.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:22,945 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 169986.00)
2025-08-05 09:52:22,945 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:22,945 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:22,945 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:22,949 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 100
2025-08-05 09:52:22,949 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,950 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,950 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,950 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,950 - ExplorationExpert - INFO - 探索路径生成完成，成本: 160363.0, 路径长度: 100, 收集中间解: 3
2025-08-05 09:52:22,950 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [73, 70, 39, 92, 43, 47, 26, 51, 11, 84, 29, 74, 91, 9, 85, 34, 17, 23, 44, 10, 65, 20, 5, 35, 37, 80, 63, 38, 46, 96, 32, 88, 41, 94, 28, 18, 98, 8, 76, 86, 19, 82, 52, 12, 93, 50, 75, 4, 72, 62, 22, 30, 67, 0, 81, 97, 6, 68, 13, 95, 21, 16, 59, 27, 45, 15, 83, 61, 7, 78, 64, 57, 69, 36, 25, 33, 58, 55, 87, 31, 56, 66, 1, 2, 14, 89, 3, 60, 42, 77, 71, 49, 40, 48, 53, 54, 99, 24, 79, 90], 'cur_cost': 160363.0, 'intermediate_solutions': [{'tour': [54, 70, 63, 92, 20, 83, 93, 97, 24, 1, 33, 57, 5, 88, 72, 8, 28, 95, 85, 40, 56, 60, 7, 69, 11, 17, 22, 9, 74, 14, 18, 50, 29, 53, 27, 90, 37, 62, 46, 89, 25, 41, 48, 15, 96, 78, 58, 64, 3, 0, 71, 31, 23, 30, 79, 87, 21, 68, 49, 82, 6, 47, 4, 84, 36, 51, 38, 86, 26, 59, 45, 19, 10, 80, 2, 43, 42, 61, 13, 99, 67, 32, 66, 16, 76, 77, 34, 73, 55, 91, 35, 44, 52, 65, 98, 39, 94, 12, 75, 81], 'cur_cost': 110938.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [54, 70, 63, 92, 20, 83, 93, 97, 24, 1, 33, 57, 5, 88, 72, 8, 28, 95, 85, 40, 56, 60, 7, 69, 18, 17, 22, 9, 74, 14, 11, 50, 29, 53, 27, 90, 37, 62, 46, 89, 25, 41, 48, 15, 96, 78, 58, 64, 3, 0, 71, 31, 23, 30, 79, 87, 21, 68, 49, 82, 6, 47, 4, 84, 16, 66, 32, 67, 99, 13, 61, 42, 43, 2, 80, 10, 19, 45, 59, 26, 86, 38, 51, 36, 76, 77, 34, 73, 55, 91, 35, 44, 52, 65, 98, 39, 94, 12, 75, 81], 'cur_cost': 107823.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [54, 70, 63, 92, 83, 93, 97, 24, 1, 33, 57, 5, 88, 72, 8, 28, 95, 85, 40, 56, 60, 7, 69, 18, 17, 22, 9, 74, 14, 11, 50, 29, 53, 27, 90, 37, 62, 46, 89, 25, 41, 48, 15, 96, 78, 58, 64, 3, 0, 71, 31, 23, 30, 79, 87, 21, 68, 49, 82, 6, 47, 4, 84, 36, 51, 38, 86, 26, 59, 45, 19, 10, 80, 2, 43, 42, 61, 13, 99, 67, 32, 66, 16, 76, 20, 77, 34, 73, 55, 91, 35, 44, 52, 65, 98, 39, 94, 12, 75, 81], 'cur_cost': 107643.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:22,951 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 160363.00)
2025-08-05 09:52:22,951 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:22,951 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:22,951 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:22,957 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 09:52:22,957 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,958 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,958 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,958 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,959 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32920.0, 路径长度: 100, 收集中间解: 3
2025-08-05 09:52:22,959 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 13, 14, 10, 16, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 32920.0, 'intermediate_solutions': [{'tour': [0, 16, 7, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 52, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 42, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 67, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57], 'cur_cost': 39799.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 16, 7, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 42, 61, 59, 76, 46, 98, 25, 74, 18, 89, 62, 48, 5, 91, 41, 88, 30, 79, 55, 96, 3, 64, 65, 69, 93, 21, 15, 87, 52, 78, 17, 23, 37, 35, 83, 9, 71, 58, 20, 73, 14, 10, 31, 44, 90, 97, 22, 92, 27, 66, 57], 'cur_cost': 31181.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 16, 7, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 40, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 13, 2, 45, 28, 33, 82, 54, 42, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57], 'cur_cost': 34383.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:22,959 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 32920.00)
2025-08-05 09:52:22,959 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:22,959 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:22,960 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:22,964 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 09:52:22,964 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,964 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,964 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,965 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,965 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30093.0, 路径长度: 100, 收集中间解: 3
2025-08-05 09:52:22,965 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 3, 20, 16, 14, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 25, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 30093.0, 'intermediate_solutions': [{'tour': [0, 13, 17, 1, 43, 49, 72, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 63, 39, 53, 68, 80, 24, 60, 50, 86, 8, 6, 56, 19, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 25, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 23, 98, 46, 92, 27, 66, 57, 54, 82, 33, 28, 45, 2, 42, 70, 40, 99, 47], 'cur_cost': 36240.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 17, 1, 43, 49, 72, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 63, 39, 53, 68, 80, 24, 60, 50, 86, 8, 6, 56, 19, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 82, 54, 57, 66, 27, 92, 46, 98, 25, 74, 18, 89, 62, 33, 28, 45, 2, 42, 70, 40, 99, 47], 'cur_cost': 39289.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 17, 1, 43, 49, 72, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 63, 39, 53, 68, 80, 24, 60, 50, 86, 8, 6, 56, 19, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 99, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 54, 82, 33, 28, 45, 2, 42, 70, 40, 47], 'cur_cost': 41023.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:22,965 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 30093.00)
2025-08-05 09:52:22,965 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:22,965 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:22,966 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:22,979 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 100
2025-08-05 09:52:22,979 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,980 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,980 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,980 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:22,981 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101375.0, 路径长度: 100, 收集中间解: 3
2025-08-05 09:52:22,981 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [74, 16, 7, 78, 65, 71, 57, 49, 75, 66, 67, 63, 82, 70, 61, 20, 5, 52, 91, 35, 64, 37, 18, 93, 48, 44, 17, 21, 69, 10, 31, 58, 26, 68, 76, 90, 56, 45, 47, 19, 50, 43, 27, 39, 60, 34, 38, 6, 42, 12, 72, 51, 28, 77, 13, 2, 86, 99, 4, 29, 32, 88, 30, 25, 87, 41, 89, 62, 73, 3, 22, 24, 11, 85, 8, 54, 36, 1, 92, 59, 14, 15, 46, 0, 23, 97, 98, 83, 9, 80, 33, 40, 81, 84, 53, 94, 95, 79, 55, 96], 'cur_cost': 101375.0, 'intermediate_solutions': [{'tour': [0, 7, 21, 14, 16, 10, 31, 90, 97, 22, 44, 46, 40, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 91, 74, 18, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 62, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 41397.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 21, 14, 16, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 93, 69, 65, 64, 3, 96, 55, 79, 39, 53, 1, 43, 49, 72, 68, 80, 24, 60, 57, 66, 27, 92, 98, 25, 18, 74, 91, 41, 88, 30, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 33823.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 21, 14, 16, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 91, 74, 18, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 51, 86, 50, 76, 59, 61, 42], 'cur_cost': 33579.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:22,981 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 101375.00)
2025-08-05 09:52:22,981 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:52:22,982 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:22,982 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:22,982 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 165081.0
2025-08-05 09:52:23,003 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:23,003 - ExploitationExpert - INFO - res_population_costs: [21389, 21411, 21776, 22282, 22308, 22356, 22427.0]
2025-08-05 09:52:23,003 - ExploitationExpert - INFO - res_populations: [array([ 0, 62, 89, 48,  5, 91,  7, 41, 88, 30, 79, 55, 96, 74, 18,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26,
       11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99, 47,
       51, 77, 95,  4, 36, 32, 75, 12, 94, 81, 38, 29, 84, 67, 72, 49, 43,
        1, 53, 39, 63, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 62,  5, 48, 89, 18, 74, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 59, 61, 76, 50, 86,  8,
        6, 56, 19, 85, 34, 26, 11, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40,
       99, 47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81, 49, 43,  1,
       53, 39, 63, 68, 72, 67, 84, 80, 24, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 91, 74, 96, 55, 79, 30, 88, 41,  7, 66, 27, 92, 57, 60, 24, 80,
       84, 67, 72, 68, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75, 32, 36,  4,
       51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28, 33, 82, 54,
       11, 26, 34, 85, 19, 56,  6,  8, 86, 50, 76, 61, 59, 22, 97, 90, 44,
       31, 10, 14, 16, 58, 73, 20, 71,  9, 83, 35, 98, 37, 23, 17, 78, 52,
       87, 15, 93, 21, 69, 65, 25, 64,  3, 18, 89, 48,  5, 62, 46],
      dtype=int64), array([ 0, 46, 92, 27, 66, 57, 68, 63, 39, 53,  1, 43, 72, 49, 81, 94, 12,
       75, 32, 36,  4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45,
       28, 33, 82, 54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 80, 24, 60,
       50, 86, 76, 61, 59, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20, 71,
        9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3, 18,
       52, 78, 89, 74, 96, 55, 79, 30, 88, 41,  7, 91, 48,  5, 62],
      dtype=int64), array([ 0, 62, 46, 92, 27, 57, 66, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75,
       32, 36,  4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28,
       33, 82, 54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 72, 68, 80, 24,
       60, 50, 86, 76, 61, 59, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20,
       71,  9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3,
       18, 52, 78, 89, 48,  5, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 62,  5, 48, 91,  7, 41, 88, 30, 79, 55, 96, 74, 89, 78, 52, 18,
        3, 64, 25, 65, 69, 21, 15, 87, 93, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 59, 61, 76, 86, 50, 60,
       24, 80, 68, 72, 67, 84,  8,  6, 56, 19, 85, 34, 26, 11, 54, 82, 33,
       28, 45, 42,  2, 13, 70, 40, 99, 47, 29, 38, 95, 77, 51,  4, 36, 32,
       75, 12, 94, 81, 49, 43,  1, 53, 39, 63, 66, 57, 27, 92, 46],
      dtype=int64), array([ 0, 92, 27, 57, 66, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75, 32, 36,
        4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28, 33, 82,
       54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 72, 68, 80, 24, 60, 50,
       86, 76, 61, 59, 22, 97, 44, 90, 46, 31, 10, 14, 16, 58, 73, 20, 71,
        9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3, 18,
       52, 78, 89, 74, 96, 55, 79, 30, 88, 41,  7, 91, 48,  5, 62],
      dtype=int64)]
2025-08-05 09:52:23,007 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:23,007 - ExploitationExpert - INFO - populations: [{'tour': array([ 4, 97, 58,  8, 22, 68, 47, 50, 64, 89, 77, 46, 59, 48, 16, 27, 12,
       86, 83, 90,  3, 45, 26, 57, 43, 92, 70,  9, 55, 93, 56, 33, 95, 67,
       61, 19, 72, 98, 87, 30, 36, 82, 78, 28,  0, 75, 21,  2, 62,  7, 79,
       91, 52, 39, 23, 69, 40, 10, 42, 74, 13, 71, 15, 54, 60,  1,  5, 84,
        6, 24, 76, 96, 32, 63, 66, 44, 25, 99, 65, 80, 81, 31, 38, 88, 17,
       51, 85, 14, 29, 41, 53, 49, 18, 11, 73, 37, 35, 20, 94, 34],
      dtype=int64), 'cur_cost': 180941.0}, {'tour': [0, 19, 16, 1, 43, 49, 72, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 63, 39, 53, 68, 80, 24, 60, 50, 86, 8, 6, 56, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 54, 82, 33, 28, 45, 2, 42, 13, 70, 40, 99, 47], 'cur_cost': 32119.0}, {'tour': [73, 19, 13, 70, 12, 42, 4, 39, 84, 2, 92, 56, 27, 86, 29, 6, 43, 47, 94, 26, 45, 68, 51, 95, 11, 66, 61, 97, 59, 91, 64, 96, 0, 9, 31, 90, 41, 17, 21, 69, 87, 23, 25, 18, 37, 55, 30, 65, 71, 16, 58, 78, 5, 22, 24, 85, 34, 57, 44, 80, 67, 36, 53, 63, 38, 99, 46, 50, 10, 52, 79, 89, 98, 88, 49, 54, 76, 14, 62, 7, 60, 82, 33, 28, 81, 32, 40, 1, 8, 72, 75, 74, 48, 20, 93, 15, 83, 35, 3, 77], 'cur_cost': 105252.0}, {'tour': array([99, 18, 88, 41, 50, 43, 77, 59, 58, 37, 20, 16, 13, 95, 33, 29, 44,
       87, 52, 12, 78, 55, 71, 24, 70, 66, 45, 98, 81, 17, 96, 54, 26, 15,
       46, 28, 93,  5, 22, 40,  4, 25, 34, 75, 97, 83, 36, 94, 80, 73,  0,
       90, 49, 57, 84, 74, 10, 60, 14,  3, 38, 89,  6, 85, 35, 30, 82, 56,
       86, 32, 76, 62, 63, 65, 21,  2, 51,  8, 91, 53, 72, 61, 48, 64, 42,
        1, 23, 47, 67, 19, 92,  9, 68, 69, 79, 39, 27, 11,  7, 31],
      dtype=int64), 'cur_cost': 169986.0}, {'tour': [73, 70, 39, 92, 43, 47, 26, 51, 11, 84, 29, 74, 91, 9, 85, 34, 17, 23, 44, 10, 65, 20, 5, 35, 37, 80, 63, 38, 46, 96, 32, 88, 41, 94, 28, 18, 98, 8, 76, 86, 19, 82, 52, 12, 93, 50, 75, 4, 72, 62, 22, 30, 67, 0, 81, 97, 6, 68, 13, 95, 21, 16, 59, 27, 45, 15, 83, 61, 7, 78, 64, 57, 69, 36, 25, 33, 58, 55, 87, 31, 56, 66, 1, 2, 14, 89, 3, 60, 42, 77, 71, 49, 40, 48, 53, 54, 99, 24, 79, 90], 'cur_cost': 160363.0}, {'tour': [0, 13, 14, 10, 16, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 32920.0}, {'tour': [0, 3, 20, 16, 14, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 25, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 30093.0}, {'tour': [74, 16, 7, 78, 65, 71, 57, 49, 75, 66, 67, 63, 82, 70, 61, 20, 5, 52, 91, 35, 64, 37, 18, 93, 48, 44, 17, 21, 69, 10, 31, 58, 26, 68, 76, 90, 56, 45, 47, 19, 50, 43, 27, 39, 60, 34, 38, 6, 42, 12, 72, 51, 28, 77, 13, 2, 86, 99, 4, 29, 32, 88, 30, 25, 87, 41, 89, 62, 73, 3, 22, 24, 11, 85, 8, 54, 36, 1, 92, 59, 14, 15, 46, 0, 23, 97, 98, 83, 9, 80, 33, 40, 81, 84, 53, 94, 95, 79, 55, 96], 'cur_cost': 101375.0}, {'tour': array([85, 21, 16, 69, 31, 70, 68,  6,  5, 54, 64, 26, 34, 33, 45,  7, 61,
       50, 96, 63,  8, 24, 59, 78, 56, 20, 12, 89, 23, 28, 11, 22, 71, 74,
       55, 57, 25, 88, 27, 97, 58,  1, 84, 36, 44, 99, 82, 41, 86, 62, 77,
       19, 80, 92,  0,  2, 93, 14, 35, 10, 15, 98, 30, 53, 75,  9, 48, 83,
       73, 60, 67,  3, 32, 29, 72, 87, 17, 43, 66, 18, 40, 90, 46, 79, 39,
       13, 65, 51, 42, 38, 76, 95, 47, 91, 81, 49, 37,  4, 94, 52],
      dtype=int64), 'cur_cost': 165081.0}, {'tour': [0, 17, 12, 5, 48, 62, 91, 7, 41, 88, 30, 79, 55, 96, 74, 18, 52, 78, 87, 15, 21, 93, 69, 65, 64, 3, 25, 89, 9, 83, 71, 20, 73, 58, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42, 35, 37, 23, 98], 'cur_cost': 35239.0}]
2025-08-05 09:52:23,009 - ExploitationExpert - INFO - 局部搜索耗时: 0.03秒，最大迭代次数: 10
2025-08-05 09:52:23,009 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 387, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 387, 'cache_hits': 0, 'similarity_calculations': 2025, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:23,010 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([85, 21, 16, 69, 31, 70, 68,  6,  5, 54, 64, 26, 34, 33, 45,  7, 61,
       50, 96, 63,  8, 24, 59, 78, 56, 20, 12, 89, 23, 28, 11, 22, 71, 74,
       55, 57, 25, 88, 27, 97, 58,  1, 84, 36, 44, 99, 82, 41, 86, 62, 77,
       19, 80, 92,  0,  2, 93, 14, 35, 10, 15, 98, 30, 53, 75,  9, 48, 83,
       73, 60, 67,  3, 32, 29, 72, 87, 17, 43, 66, 18, 40, 90, 46, 79, 39,
       13, 65, 51, 42, 38, 76, 95, 47, 91, 81, 49, 37,  4, 94, 52],
      dtype=int64), 'cur_cost': 165081.0, 'intermediate_solutions': [{'tour': array([88,  6, 31, 97, 14, 85, 39, 43, 73, 58, 64, 84, 41, 22, 99, 37, 98,
       95,  7, 23, 28, 81, 35, 29, 46, 59, 68, 69, 19, 71, 94, 42, 10, 56,
       51, 96, 79,  3, 47, 90, 89, 67, 49,  2, 38, 18, 26,  4, 16, 60, 65,
       50, 53, 70, 36, 12, 24, 77, 25, 11, 83, 87, 40,  5, 45,  0, 55, 61,
       74, 82, 44, 66, 30, 78, 93, 48, 76, 17, 62, 21, 72, 34, 52,  8, 32,
       86, 33, 54, 75, 91, 20, 27, 80,  9, 13, 15, 57,  1, 63, 92]), 'cur_cost': 177490.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([97, 88,  6, 31, 14, 85, 39, 43, 73, 58, 64, 84, 41, 22, 99, 37, 98,
       95,  7, 23, 28, 81, 35, 29, 46, 59, 68, 69, 19, 71, 94, 42, 10, 56,
       51, 96, 79,  3, 47, 90, 89, 67, 49,  2, 38, 18, 26,  4, 16, 60, 65,
       50, 53, 70, 36, 12, 24, 77, 25, 11, 83, 87, 40,  5, 45,  0, 55, 61,
       74, 82, 44, 66, 30, 78, 93, 48, 76, 17, 62, 21, 72, 34, 52,  8, 32,
       86, 33, 54, 75, 91, 20, 27, 80,  9, 13, 15, 57,  1, 63, 92]), 'cur_cost': 178121.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14, 97, 88,  6, 31, 85, 39, 43, 73, 58, 64, 84, 41, 22, 99, 37, 98,
       95,  7, 23, 28, 81, 35, 29, 46, 59, 68, 69, 19, 71, 94, 42, 10, 56,
       51, 96, 79,  3, 47, 90, 89, 67, 49,  2, 38, 18, 26,  4, 16, 60, 65,
       50, 53, 70, 36, 12, 24, 77, 25, 11, 83, 87, 40,  5, 45,  0, 55, 61,
       74, 82, 44, 66, 30, 78, 93, 48, 76, 17, 62, 21, 72, 34, 52,  8, 32,
       86, 33, 54, 75, 91, 20, 27, 80,  9, 13, 15, 57,  1, 63, 92]), 'cur_cost': 178673.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([31, 97, 88,  6, 14, 85, 39, 43, 73, 58, 64, 84, 41, 22, 99, 37, 98,
       95,  7, 23, 28, 81, 35, 29, 46, 59, 68, 69, 19, 71, 94, 42, 10, 56,
       51, 96, 79,  3, 47, 90, 89, 67, 49,  2, 38, 18, 26,  4, 16, 60, 65,
       50, 53, 70, 36, 12, 24, 77, 25, 11, 83, 87, 40,  5, 45,  0, 55, 61,
       74, 82, 44, 66, 30, 78, 93, 48, 76, 17, 62, 21, 72, 34, 52,  8, 32,
       86, 33, 54, 75, 91, 20, 27, 80,  9, 13, 15, 57,  1, 63, 92]), 'cur_cost': 178547.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([31, 14, 97, 88,  6, 85, 39, 43, 73, 58, 64, 84, 41, 22, 99, 37, 98,
       95,  7, 23, 28, 81, 35, 29, 46, 59, 68, 69, 19, 71, 94, 42, 10, 56,
       51, 96, 79,  3, 47, 90, 89, 67, 49,  2, 38, 18, 26,  4, 16, 60, 65,
       50, 53, 70, 36, 12, 24, 77, 25, 11, 83, 87, 40,  5, 45,  0, 55, 61,
       74, 82, 44, 66, 30, 78, 93, 48, 76, 17, 62, 21, 72, 34, 52,  8, 32,
       86, 33, 54, 75, 91, 20, 27, 80,  9, 13, 15, 57,  1, 63, 92]), 'cur_cost': 176549.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:23,010 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 165081.00)
2025-08-05 09:52:23,010 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:23,010 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:23,011 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:23,016 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 09:52:23,016 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,017 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,017 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,017 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,018 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32340.0, 路径长度: 100, 收集中间解: 3
2025-08-05 09:52:23,018 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 19, 12, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 94, 81, 49, 43, 72, 67, 84, 38, 29, 33, 82, 54, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 60, 24, 80, 68, 63, 39, 53, 1, 50, 86, 8, 6, 56, 42], 'cur_cost': 32340.0, 'intermediate_solutions': [{'tour': [0, 17, 12, 5, 48, 62, 91, 7, 41, 88, 30, 79, 55, 96, 74, 18, 52, 78, 87, 15, 21, 53, 69, 65, 64, 3, 25, 89, 9, 83, 71, 20, 73, 58, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 93, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42, 35, 37, 23, 98], 'cur_cost': 45077.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 12, 5, 48, 62, 91, 7, 41, 88, 30, 79, 55, 96, 74, 18, 52, 78, 87, 15, 21, 93, 69, 65, 64, 3, 25, 89, 9, 83, 71, 20, 73, 58, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 34, 85, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42, 35, 37, 23, 98], 'cur_cost': 35198.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 12, 5, 48, 62, 91, 7, 41, 88, 79, 55, 96, 74, 18, 52, 78, 87, 15, 21, 93, 69, 65, 64, 3, 25, 89, 9, 83, 71, 20, 73, 58, 30, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42, 35, 37, 23, 98], 'cur_cost': 38566.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:23,019 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 32340.00)
2025-08-05 09:52:23,019 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:23,019 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:23,023 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 4, 97, 58,  8, 22, 68, 47, 50, 64, 89, 77, 46, 59, 48, 16, 27, 12,
       86, 83, 90,  3, 45, 26, 57, 43, 92, 70,  9, 55, 93, 56, 33, 95, 67,
       61, 19, 72, 98, 87, 30, 36, 82, 78, 28,  0, 75, 21,  2, 62,  7, 79,
       91, 52, 39, 23, 69, 40, 10, 42, 74, 13, 71, 15, 54, 60,  1,  5, 84,
        6, 24, 76, 96, 32, 63, 66, 44, 25, 99, 65, 80, 81, 31, 38, 88, 17,
       51, 85, 14, 29, 41, 53, 49, 18, 11, 73, 37, 35, 20, 94, 34],
      dtype=int64), 'cur_cost': 180941.0, 'intermediate_solutions': [{'tour': array([26, 87, 33,  4, 92, 64, 39, 29, 25, 78, 36, 41, 94, 35, 38, 90, 72,
       66, 30, 85, 32,  2, 73, 76, 11, 88, 96, 97, 83,  9, 51, 68,  7, 91,
        3, 13,  1, 67, 77, 52, 84, 54, 61, 95, 86, 37, 59, 12, 44, 31, 71,
       56,  0, 17, 28, 49, 19, 74, 75, 89, 21, 55, 15, 43, 16, 46, 80, 98,
       48, 53, 60,  8, 40, 23, 82, 10,  6, 20, 70, 62, 34, 24, 47, 57, 14,
       18, 42, 69, 79, 45, 58, 63, 65, 27, 50, 93, 99,  5, 22, 81]), 'cur_cost': 186261.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 4, 26, 87, 33, 92, 64, 39, 29, 25, 78, 36, 41, 94, 35, 38, 90, 72,
       66, 30, 85, 32,  2, 73, 76, 11, 88, 96, 97, 83,  9, 51, 68,  7, 91,
        3, 13,  1, 67, 77, 52, 84, 54, 61, 95, 86, 37, 59, 12, 44, 31, 71,
       56,  0, 17, 28, 49, 19, 74, 75, 89, 21, 55, 15, 43, 16, 46, 80, 98,
       48, 53, 60,  8, 40, 23, 82, 10,  6, 20, 70, 62, 34, 24, 47, 57, 14,
       18, 42, 69, 79, 45, 58, 63, 65, 27, 50, 93, 99,  5, 22, 81]), 'cur_cost': 184895.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([92,  4, 26, 87, 33, 64, 39, 29, 25, 78, 36, 41, 94, 35, 38, 90, 72,
       66, 30, 85, 32,  2, 73, 76, 11, 88, 96, 97, 83,  9, 51, 68,  7, 91,
        3, 13,  1, 67, 77, 52, 84, 54, 61, 95, 86, 37, 59, 12, 44, 31, 71,
       56,  0, 17, 28, 49, 19, 74, 75, 89, 21, 55, 15, 43, 16, 46, 80, 98,
       48, 53, 60,  8, 40, 23, 82, 10,  6, 20, 70, 62, 34, 24, 47, 57, 14,
       18, 42, 69, 79, 45, 58, 63, 65, 27, 50, 93, 99,  5, 22, 81]), 'cur_cost': 188161.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([33,  4, 26, 87, 92, 64, 39, 29, 25, 78, 36, 41, 94, 35, 38, 90, 72,
       66, 30, 85, 32,  2, 73, 76, 11, 88, 96, 97, 83,  9, 51, 68,  7, 91,
        3, 13,  1, 67, 77, 52, 84, 54, 61, 95, 86, 37, 59, 12, 44, 31, 71,
       56,  0, 17, 28, 49, 19, 74, 75, 89, 21, 55, 15, 43, 16, 46, 80, 98,
       48, 53, 60,  8, 40, 23, 82, 10,  6, 20, 70, 62, 34, 24, 47, 57, 14,
       18, 42, 69, 79, 45, 58, 63, 65, 27, 50, 93, 99,  5, 22, 81]), 'cur_cost': 184035.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([33, 92,  4, 26, 87, 64, 39, 29, 25, 78, 36, 41, 94, 35, 38, 90, 72,
       66, 30, 85, 32,  2, 73, 76, 11, 88, 96, 97, 83,  9, 51, 68,  7, 91,
        3, 13,  1, 67, 77, 52, 84, 54, 61, 95, 86, 37, 59, 12, 44, 31, 71,
       56,  0, 17, 28, 49, 19, 74, 75, 89, 21, 55, 15, 43, 16, 46, 80, 98,
       48, 53, 60,  8, 40, 23, 82, 10,  6, 20, 70, 62, 34, 24, 47, 57, 14,
       18, 42, 69, 79, 45, 58, 63, 65, 27, 50, 93, 99,  5, 22, 81]), 'cur_cost': 183778.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 16, 1, 43, 49, 72, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 63, 39, 53, 68, 80, 24, 60, 50, 86, 8, 6, 56, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 54, 82, 33, 28, 45, 2, 42, 13, 70, 40, 99, 47], 'cur_cost': 32119.0, 'intermediate_solutions': [{'tour': [85, 22, 66, 50, 77, 38, 6, 46, 48, 30, 17, 90, 37, 14, 23, 10, 97, 68, 76, 60, 0, 7, 79, 83, 58, 27, 86, 80, 34, 42, 28, 13, 2, 63, 75, 54, 53, 94, 51, 82, 43, 41, 3, 93, 65, 15, 88, 25, 87, 57, 74, 21, 71, 55, 1, 49, 92, 89, 96, 5, 18, 64, 39, 32, 84, 95, 36, 99, 11, 67, 12, 19, 26, 40, 70, 59, 24, 33, 29, 8, 47, 72, 61, 44, 31, 91, 78, 62, 35, 98, 9, 20, 73, 16, 52, 56, 45, 4, 81, 69], 'cur_cost': 101716.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [85, 22, 66, 50, 77, 38, 6, 46, 37, 30, 17, 90, 48, 14, 23, 10, 97, 68, 76, 60, 0, 7, 79, 83, 58, 27, 86, 80, 34, 42, 28, 13, 2, 63, 75, 54, 53, 94, 51, 82, 43, 41, 3, 93, 65, 15, 88, 25, 87, 57, 74, 21, 71, 55, 1, 49, 92, 89, 96, 5, 18, 64, 39, 32, 84, 95, 44, 61, 72, 47, 8, 29, 33, 24, 59, 70, 40, 26, 19, 12, 67, 11, 99, 36, 31, 91, 78, 62, 35, 98, 9, 20, 73, 16, 52, 56, 45, 4, 81, 69], 'cur_cost': 107141.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [85, 22, 66, 50, 77, 38, 6, 46, 37, 30, 17, 90, 48, 14, 23, 10, 97, 68, 76, 60, 0, 7, 79, 83, 58, 27, 86, 80, 34, 42, 28, 13, 2, 63, 75, 54, 53, 94, 51, 82, 43, 3, 93, 65, 15, 88, 25, 87, 57, 74, 21, 71, 55, 41, 1, 49, 92, 89, 96, 5, 18, 64, 39, 32, 84, 95, 36, 99, 11, 67, 12, 19, 26, 40, 70, 59, 24, 33, 29, 8, 47, 72, 61, 44, 31, 91, 78, 62, 35, 98, 9, 20, 73, 16, 52, 56, 45, 4, 81, 69], 'cur_cost': 102636.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [73, 19, 13, 70, 12, 42, 4, 39, 84, 2, 92, 56, 27, 86, 29, 6, 43, 47, 94, 26, 45, 68, 51, 95, 11, 66, 61, 97, 59, 91, 64, 96, 0, 9, 31, 90, 41, 17, 21, 69, 87, 23, 25, 18, 37, 55, 30, 65, 71, 16, 58, 78, 5, 22, 24, 85, 34, 57, 44, 80, 67, 36, 53, 63, 38, 99, 46, 50, 10, 52, 79, 89, 98, 88, 49, 54, 76, 14, 62, 7, 60, 82, 33, 28, 81, 32, 40, 1, 8, 72, 75, 74, 48, 20, 93, 15, 83, 35, 3, 77], 'cur_cost': 105252.0, 'intermediate_solutions': [{'tour': [66, 68, 85, 92, 55, 30, 91, 97, 34, 80, 2, 38, 50, 27, 26, 8, 53, 41, 89, 22, 7, 24, 77, 61, 86, 67, 39, 94, 57, 19, 72, 60, 42, 47, 6, 63, 45, 33, 56, 76, 83, 14, 9, 71, 17, 96, 23, 20, 5, 74, 3, 35, 16, 54, 59, 29, 99, 75, 90, 0, 62, 64, 46, 98, 73, 10, 48, 44, 79, 87, 58, 21, 15, 78, 37, 69, 93, 65, 49, 1, 51, 70, 40, 11, 52, 43, 36, 32, 12, 4, 81, 95, 84, 88, 25, 82, 18, 31, 28, 13], 'cur_cost': 105103.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [66, 68, 85, 92, 55, 30, 91, 97, 34, 80, 2, 38, 50, 27, 26, 8, 53, 41, 89, 22, 7, 24, 77, 61, 86, 67, 39, 94, 57, 19, 72, 60, 42, 47, 6, 63, 45, 33, 56, 76, 83, 14, 9, 71, 17, 96, 23, 20, 51, 1, 49, 65, 93, 69, 37, 78, 15, 21, 58, 87, 79, 44, 48, 10, 73, 98, 46, 64, 62, 0, 90, 75, 99, 29, 59, 54, 16, 35, 3, 74, 5, 70, 40, 11, 82, 43, 36, 32, 12, 4, 81, 95, 84, 88, 25, 52, 18, 31, 28, 13], 'cur_cost': 101432.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [66, 68, 85, 92, 55, 30, 91, 97, 34, 80, 2, 38, 50, 27, 26, 8, 53, 41, 89, 22, 7, 24, 77, 61, 86, 67, 39, 94, 57, 19, 72, 60, 42, 47, 6, 63, 45, 33, 56, 76, 83, 14, 9, 71, 17, 96, 23, 20, 5, 74, 3, 35, 54, 59, 29, 99, 75, 90, 0, 62, 64, 46, 98, 73, 10, 48, 44, 79, 87, 58, 21, 15, 78, 16, 37, 69, 93, 65, 49, 1, 51, 70, 40, 11, 82, 43, 36, 32, 12, 4, 81, 95, 84, 88, 25, 52, 18, 31, 28, 13], 'cur_cost': 98605.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([99, 18, 88, 41, 50, 43, 77, 59, 58, 37, 20, 16, 13, 95, 33, 29, 44,
       87, 52, 12, 78, 55, 71, 24, 70, 66, 45, 98, 81, 17, 96, 54, 26, 15,
       46, 28, 93,  5, 22, 40,  4, 25, 34, 75, 97, 83, 36, 94, 80, 73,  0,
       90, 49, 57, 84, 74, 10, 60, 14,  3, 38, 89,  6, 85, 35, 30, 82, 56,
       86, 32, 76, 62, 63, 65, 21,  2, 51,  8, 91, 53, 72, 61, 48, 64, 42,
        1, 23, 47, 67, 19, 92,  9, 68, 69, 79, 39, 27, 11,  7, 31],
      dtype=int64), 'cur_cost': 169986.0, 'intermediate_solutions': [{'tour': array([ 6, 40, 59,  8, 91, 75, 42, 72, 88, 92, 18, 39, 52, 46, 45, 25, 13,
       84, 41, 56,  0, 12, 11, 74, 93, 81, 49, 27, 65, 69, 66, 26,  3, 24,
       50,  7, 76, 94, 87, 28, 82, 98, 83, 80, 34, 71, 16, 22, 55, 62, 47,
       31, 35, 58, 90, 73, 97, 61, 36, 78, 96, 51, 79, 57, 15, 21, 17, 53,
       67, 95, 19, 77, 23,  9, 30, 99, 32, 63, 89, 48, 70, 14,  5, 29,  4,
        1, 43, 44, 38, 64, 85, 20, 68, 33, 86, 37,  2, 54, 10, 60]), 'cur_cost': 165417.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 8,  6, 40, 59, 91, 75, 42, 72, 88, 92, 18, 39, 52, 46, 45, 25, 13,
       84, 41, 56,  0, 12, 11, 74, 93, 81, 49, 27, 65, 69, 66, 26,  3, 24,
       50,  7, 76, 94, 87, 28, 82, 98, 83, 80, 34, 71, 16, 22, 55, 62, 47,
       31, 35, 58, 90, 73, 97, 61, 36, 78, 96, 51, 79, 57, 15, 21, 17, 53,
       67, 95, 19, 77, 23,  9, 30, 99, 32, 63, 89, 48, 70, 14,  5, 29,  4,
        1, 43, 44, 38, 64, 85, 20, 68, 33, 86, 37,  2, 54, 10, 60]), 'cur_cost': 164496.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([91,  8,  6, 40, 59, 75, 42, 72, 88, 92, 18, 39, 52, 46, 45, 25, 13,
       84, 41, 56,  0, 12, 11, 74, 93, 81, 49, 27, 65, 69, 66, 26,  3, 24,
       50,  7, 76, 94, 87, 28, 82, 98, 83, 80, 34, 71, 16, 22, 55, 62, 47,
       31, 35, 58, 90, 73, 97, 61, 36, 78, 96, 51, 79, 57, 15, 21, 17, 53,
       67, 95, 19, 77, 23,  9, 30, 99, 32, 63, 89, 48, 70, 14,  5, 29,  4,
        1, 43, 44, 38, 64, 85, 20, 68, 33, 86, 37,  2, 54, 10, 60]), 'cur_cost': 165159.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([59,  8,  6, 40, 91, 75, 42, 72, 88, 92, 18, 39, 52, 46, 45, 25, 13,
       84, 41, 56,  0, 12, 11, 74, 93, 81, 49, 27, 65, 69, 66, 26,  3, 24,
       50,  7, 76, 94, 87, 28, 82, 98, 83, 80, 34, 71, 16, 22, 55, 62, 47,
       31, 35, 58, 90, 73, 97, 61, 36, 78, 96, 51, 79, 57, 15, 21, 17, 53,
       67, 95, 19, 77, 23,  9, 30, 99, 32, 63, 89, 48, 70, 14,  5, 29,  4,
        1, 43, 44, 38, 64, 85, 20, 68, 33, 86, 37,  2, 54, 10, 60]), 'cur_cost': 165198.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([59, 91,  8,  6, 40, 75, 42, 72, 88, 92, 18, 39, 52, 46, 45, 25, 13,
       84, 41, 56,  0, 12, 11, 74, 93, 81, 49, 27, 65, 69, 66, 26,  3, 24,
       50,  7, 76, 94, 87, 28, 82, 98, 83, 80, 34, 71, 16, 22, 55, 62, 47,
       31, 35, 58, 90, 73, 97, 61, 36, 78, 96, 51, 79, 57, 15, 21, 17, 53,
       67, 95, 19, 77, 23,  9, 30, 99, 32, 63, 89, 48, 70, 14,  5, 29,  4,
        1, 43, 44, 38, 64, 85, 20, 68, 33, 86, 37,  2, 54, 10, 60]), 'cur_cost': 163571.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [73, 70, 39, 92, 43, 47, 26, 51, 11, 84, 29, 74, 91, 9, 85, 34, 17, 23, 44, 10, 65, 20, 5, 35, 37, 80, 63, 38, 46, 96, 32, 88, 41, 94, 28, 18, 98, 8, 76, 86, 19, 82, 52, 12, 93, 50, 75, 4, 72, 62, 22, 30, 67, 0, 81, 97, 6, 68, 13, 95, 21, 16, 59, 27, 45, 15, 83, 61, 7, 78, 64, 57, 69, 36, 25, 33, 58, 55, 87, 31, 56, 66, 1, 2, 14, 89, 3, 60, 42, 77, 71, 49, 40, 48, 53, 54, 99, 24, 79, 90], 'cur_cost': 160363.0, 'intermediate_solutions': [{'tour': [54, 70, 63, 92, 20, 83, 93, 97, 24, 1, 33, 57, 5, 88, 72, 8, 28, 95, 85, 40, 56, 60, 7, 69, 11, 17, 22, 9, 74, 14, 18, 50, 29, 53, 27, 90, 37, 62, 46, 89, 25, 41, 48, 15, 96, 78, 58, 64, 3, 0, 71, 31, 23, 30, 79, 87, 21, 68, 49, 82, 6, 47, 4, 84, 36, 51, 38, 86, 26, 59, 45, 19, 10, 80, 2, 43, 42, 61, 13, 99, 67, 32, 66, 16, 76, 77, 34, 73, 55, 91, 35, 44, 52, 65, 98, 39, 94, 12, 75, 81], 'cur_cost': 110938.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [54, 70, 63, 92, 20, 83, 93, 97, 24, 1, 33, 57, 5, 88, 72, 8, 28, 95, 85, 40, 56, 60, 7, 69, 18, 17, 22, 9, 74, 14, 11, 50, 29, 53, 27, 90, 37, 62, 46, 89, 25, 41, 48, 15, 96, 78, 58, 64, 3, 0, 71, 31, 23, 30, 79, 87, 21, 68, 49, 82, 6, 47, 4, 84, 16, 66, 32, 67, 99, 13, 61, 42, 43, 2, 80, 10, 19, 45, 59, 26, 86, 38, 51, 36, 76, 77, 34, 73, 55, 91, 35, 44, 52, 65, 98, 39, 94, 12, 75, 81], 'cur_cost': 107823.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [54, 70, 63, 92, 83, 93, 97, 24, 1, 33, 57, 5, 88, 72, 8, 28, 95, 85, 40, 56, 60, 7, 69, 18, 17, 22, 9, 74, 14, 11, 50, 29, 53, 27, 90, 37, 62, 46, 89, 25, 41, 48, 15, 96, 78, 58, 64, 3, 0, 71, 31, 23, 30, 79, 87, 21, 68, 49, 82, 6, 47, 4, 84, 36, 51, 38, 86, 26, 59, 45, 19, 10, 80, 2, 43, 42, 61, 13, 99, 67, 32, 66, 16, 76, 20, 77, 34, 73, 55, 91, 35, 44, 52, 65, 98, 39, 94, 12, 75, 81], 'cur_cost': 107643.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 14, 10, 16, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 32920.0, 'intermediate_solutions': [{'tour': [0, 16, 7, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 52, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 42, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 67, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57], 'cur_cost': 39799.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 16, 7, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 42, 61, 59, 76, 46, 98, 25, 74, 18, 89, 62, 48, 5, 91, 41, 88, 30, 79, 55, 96, 3, 64, 65, 69, 93, 21, 15, 87, 52, 78, 17, 23, 37, 35, 83, 9, 71, 58, 20, 73, 14, 10, 31, 44, 90, 97, 22, 92, 27, 66, 57], 'cur_cost': 31181.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 16, 7, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 40, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 13, 2, 45, 28, 33, 82, 54, 42, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57], 'cur_cost': 34383.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 20, 16, 14, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 25, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 30093.0, 'intermediate_solutions': [{'tour': [0, 13, 17, 1, 43, 49, 72, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 63, 39, 53, 68, 80, 24, 60, 50, 86, 8, 6, 56, 19, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 25, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 23, 98, 46, 92, 27, 66, 57, 54, 82, 33, 28, 45, 2, 42, 70, 40, 99, 47], 'cur_cost': 36240.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 17, 1, 43, 49, 72, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 63, 39, 53, 68, 80, 24, 60, 50, 86, 8, 6, 56, 19, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 82, 54, 57, 66, 27, 92, 46, 98, 25, 74, 18, 89, 62, 33, 28, 45, 2, 42, 70, 40, 99, 47], 'cur_cost': 39289.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 17, 1, 43, 49, 72, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 63, 39, 53, 68, 80, 24, 60, 50, 86, 8, 6, 56, 19, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 99, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 54, 82, 33, 28, 45, 2, 42, 70, 40, 47], 'cur_cost': 41023.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [74, 16, 7, 78, 65, 71, 57, 49, 75, 66, 67, 63, 82, 70, 61, 20, 5, 52, 91, 35, 64, 37, 18, 93, 48, 44, 17, 21, 69, 10, 31, 58, 26, 68, 76, 90, 56, 45, 47, 19, 50, 43, 27, 39, 60, 34, 38, 6, 42, 12, 72, 51, 28, 77, 13, 2, 86, 99, 4, 29, 32, 88, 30, 25, 87, 41, 89, 62, 73, 3, 22, 24, 11, 85, 8, 54, 36, 1, 92, 59, 14, 15, 46, 0, 23, 97, 98, 83, 9, 80, 33, 40, 81, 84, 53, 94, 95, 79, 55, 96], 'cur_cost': 101375.0, 'intermediate_solutions': [{'tour': [0, 7, 21, 14, 16, 10, 31, 90, 97, 22, 44, 46, 40, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 91, 74, 18, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 62, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 41397.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 21, 14, 16, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 93, 69, 65, 64, 3, 96, 55, 79, 39, 53, 1, 43, 49, 72, 68, 80, 24, 60, 57, 66, 27, 92, 98, 25, 18, 74, 91, 41, 88, 30, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 33823.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 21, 14, 16, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 91, 74, 18, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 51, 86, 50, 76, 59, 61, 42], 'cur_cost': 33579.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([85, 21, 16, 69, 31, 70, 68,  6,  5, 54, 64, 26, 34, 33, 45,  7, 61,
       50, 96, 63,  8, 24, 59, 78, 56, 20, 12, 89, 23, 28, 11, 22, 71, 74,
       55, 57, 25, 88, 27, 97, 58,  1, 84, 36, 44, 99, 82, 41, 86, 62, 77,
       19, 80, 92,  0,  2, 93, 14, 35, 10, 15, 98, 30, 53, 75,  9, 48, 83,
       73, 60, 67,  3, 32, 29, 72, 87, 17, 43, 66, 18, 40, 90, 46, 79, 39,
       13, 65, 51, 42, 38, 76, 95, 47, 91, 81, 49, 37,  4, 94, 52],
      dtype=int64), 'cur_cost': 165081.0, 'intermediate_solutions': [{'tour': array([88,  6, 31, 97, 14, 85, 39, 43, 73, 58, 64, 84, 41, 22, 99, 37, 98,
       95,  7, 23, 28, 81, 35, 29, 46, 59, 68, 69, 19, 71, 94, 42, 10, 56,
       51, 96, 79,  3, 47, 90, 89, 67, 49,  2, 38, 18, 26,  4, 16, 60, 65,
       50, 53, 70, 36, 12, 24, 77, 25, 11, 83, 87, 40,  5, 45,  0, 55, 61,
       74, 82, 44, 66, 30, 78, 93, 48, 76, 17, 62, 21, 72, 34, 52,  8, 32,
       86, 33, 54, 75, 91, 20, 27, 80,  9, 13, 15, 57,  1, 63, 92]), 'cur_cost': 177490.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([97, 88,  6, 31, 14, 85, 39, 43, 73, 58, 64, 84, 41, 22, 99, 37, 98,
       95,  7, 23, 28, 81, 35, 29, 46, 59, 68, 69, 19, 71, 94, 42, 10, 56,
       51, 96, 79,  3, 47, 90, 89, 67, 49,  2, 38, 18, 26,  4, 16, 60, 65,
       50, 53, 70, 36, 12, 24, 77, 25, 11, 83, 87, 40,  5, 45,  0, 55, 61,
       74, 82, 44, 66, 30, 78, 93, 48, 76, 17, 62, 21, 72, 34, 52,  8, 32,
       86, 33, 54, 75, 91, 20, 27, 80,  9, 13, 15, 57,  1, 63, 92]), 'cur_cost': 178121.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14, 97, 88,  6, 31, 85, 39, 43, 73, 58, 64, 84, 41, 22, 99, 37, 98,
       95,  7, 23, 28, 81, 35, 29, 46, 59, 68, 69, 19, 71, 94, 42, 10, 56,
       51, 96, 79,  3, 47, 90, 89, 67, 49,  2, 38, 18, 26,  4, 16, 60, 65,
       50, 53, 70, 36, 12, 24, 77, 25, 11, 83, 87, 40,  5, 45,  0, 55, 61,
       74, 82, 44, 66, 30, 78, 93, 48, 76, 17, 62, 21, 72, 34, 52,  8, 32,
       86, 33, 54, 75, 91, 20, 27, 80,  9, 13, 15, 57,  1, 63, 92]), 'cur_cost': 178673.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([31, 97, 88,  6, 14, 85, 39, 43, 73, 58, 64, 84, 41, 22, 99, 37, 98,
       95,  7, 23, 28, 81, 35, 29, 46, 59, 68, 69, 19, 71, 94, 42, 10, 56,
       51, 96, 79,  3, 47, 90, 89, 67, 49,  2, 38, 18, 26,  4, 16, 60, 65,
       50, 53, 70, 36, 12, 24, 77, 25, 11, 83, 87, 40,  5, 45,  0, 55, 61,
       74, 82, 44, 66, 30, 78, 93, 48, 76, 17, 62, 21, 72, 34, 52,  8, 32,
       86, 33, 54, 75, 91, 20, 27, 80,  9, 13, 15, 57,  1, 63, 92]), 'cur_cost': 178547.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([31, 14, 97, 88,  6, 85, 39, 43, 73, 58, 64, 84, 41, 22, 99, 37, 98,
       95,  7, 23, 28, 81, 35, 29, 46, 59, 68, 69, 19, 71, 94, 42, 10, 56,
       51, 96, 79,  3, 47, 90, 89, 67, 49,  2, 38, 18, 26,  4, 16, 60, 65,
       50, 53, 70, 36, 12, 24, 77, 25, 11, 83, 87, 40,  5, 45,  0, 55, 61,
       74, 82, 44, 66, 30, 78, 93, 48, 76, 17, 62, 21, 72, 34, 52,  8, 32,
       86, 33, 54, 75, 91, 20, 27, 80,  9, 13, 15, 57,  1, 63, 92]), 'cur_cost': 176549.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 12, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 94, 81, 49, 43, 72, 67, 84, 38, 29, 33, 82, 54, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 60, 24, 80, 68, 63, 39, 53, 1, 50, 86, 8, 6, 56, 42], 'cur_cost': 32340.0, 'intermediate_solutions': [{'tour': [0, 17, 12, 5, 48, 62, 91, 7, 41, 88, 30, 79, 55, 96, 74, 18, 52, 78, 87, 15, 21, 53, 69, 65, 64, 3, 25, 89, 9, 83, 71, 20, 73, 58, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 93, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42, 35, 37, 23, 98], 'cur_cost': 45077.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 12, 5, 48, 62, 91, 7, 41, 88, 30, 79, 55, 96, 74, 18, 52, 78, 87, 15, 21, 93, 69, 65, 64, 3, 25, 89, 9, 83, 71, 20, 73, 58, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 34, 85, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42, 35, 37, 23, 98], 'cur_cost': 35198.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 12, 5, 48, 62, 91, 7, 41, 88, 79, 55, 96, 74, 18, 52, 78, 87, 15, 21, 93, 69, 65, 64, 3, 25, 89, 9, 83, 71, 20, 73, 58, 30, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42, 35, 37, 23, 98], 'cur_cost': 38566.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:23,024 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:23,024 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:23,030 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=30093.000, 多样性=0.979
2025-08-05 09:52:23,030 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 09:52:23,030 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 09:52:23,030 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:23,031 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 70, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'slight_improvement', 'improvement_rate': 8.704175307173757e-05, 'best_improvement': 0.015603532875368007}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.01055942484834883}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.07128750898717717, 'recent_improvements': [0.09002312728986465, -0.007385528749130407, -0.05255189068448968], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 7, 'new_count': 7, 'count_change': 0, 'old_best_cost': 21389, 'new_best_cost': 21389, 'quality_improvement': 0.0, 'old_diversity': 0.919047619047619, 'new_diversity': 0.919047619047619, 'diversity_change': 0.0}, 'suggestions': ['改进缓慢，建议调整策略平衡或增加扰动强度', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:52:23,032 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 09:52:23,032 - __main__ - INFO - kroA100 开始进化第 5 代
2025-08-05 09:52:23,032 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 09:52:23,032 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:23,033 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=30093.000, 多样性=0.979
2025-08-05 09:52:23,034 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:23,038 - PathExpert - INFO - 路径结构分析完成: 公共边数量=3, 路径相似性=0.979
2025-08-05 09:52:23,038 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:23,040 - EliteExpert - INFO - 精英解分析完成: 精英解数量=7, 多样性=0.919
2025-08-05 09:52:23,042 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 09:52:23,043 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:23,043 - LandscapeExpert - INFO - 添加精英解数据: 7个精英解
2025-08-05 09:52:23,043 - LandscapeExpert - INFO - 数据提取成功: 17个路径, 17个适应度值
2025-08-05 09:52:23,112 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.118, 适应度梯度: -11066.071, 聚类评分: 0.000, 覆盖率: 0.169, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:23,112 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 09:52:23,112 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:23,112 - visualization.landscape_visualizer - INFO - 设置当前实例名: kroA100
2025-08-05 09:52:23,119 - visualization.landscape_visualizer - INFO - 插值约束: 375 个点被约束到最小值 21389.00
2025-08-05 09:52:23,249 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_kroA100\landscape_kroA100_iter_150_20250805_095223.html
2025-08-05 09:52:23,316 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_kroA100\dashboard_kroA100_iter_150_20250805_095223.html
2025-08-05 09:52:23,317 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 150
2025-08-05 09:52:23,317 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 09:52:23,317 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2759秒
2025-08-05 09:52:23,318 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.11764705882352941, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -11066.070588235292, 'local_optima_density': 0.11764705882352941, 'gradient_variance': 1707629346.122076, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1693, 'fitness_entropy': 0.7258303821788832, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -11066.071)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.169)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358743.112663, 'performance_metrics': {}}}
2025-08-05 09:52:23,318 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:23,318 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:23,318 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:23,318 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:23,319 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:52:23,319 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 09:52:23,319 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:52:23,319 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:23,320 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:23,320 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:52:23,320 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:23,320 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:23,321 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 6} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:23,321 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-05 09:52:23,321 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:23,321 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:23,321 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 182553.0
2025-08-05 09:52:23,340 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:23,340 - ExploitationExpert - INFO - res_population_costs: [21389, 21411, 21776, 22282, 22308, 22356, 22427.0]
2025-08-05 09:52:23,340 - ExploitationExpert - INFO - res_populations: [array([ 0, 62, 89, 48,  5, 91,  7, 41, 88, 30, 79, 55, 96, 74, 18,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26,
       11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99, 47,
       51, 77, 95,  4, 36, 32, 75, 12, 94, 81, 38, 29, 84, 67, 72, 49, 43,
        1, 53, 39, 63, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 62,  5, 48, 89, 18, 74, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 59, 61, 76, 50, 86,  8,
        6, 56, 19, 85, 34, 26, 11, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40,
       99, 47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81, 49, 43,  1,
       53, 39, 63, 68, 72, 67, 84, 80, 24, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 91, 74, 96, 55, 79, 30, 88, 41,  7, 66, 27, 92, 57, 60, 24, 80,
       84, 67, 72, 68, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75, 32, 36,  4,
       51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28, 33, 82, 54,
       11, 26, 34, 85, 19, 56,  6,  8, 86, 50, 76, 61, 59, 22, 97, 90, 44,
       31, 10, 14, 16, 58, 73, 20, 71,  9, 83, 35, 98, 37, 23, 17, 78, 52,
       87, 15, 93, 21, 69, 65, 25, 64,  3, 18, 89, 48,  5, 62, 46],
      dtype=int64), array([ 0, 46, 92, 27, 66, 57, 68, 63, 39, 53,  1, 43, 72, 49, 81, 94, 12,
       75, 32, 36,  4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45,
       28, 33, 82, 54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 80, 24, 60,
       50, 86, 76, 61, 59, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20, 71,
        9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3, 18,
       52, 78, 89, 74, 96, 55, 79, 30, 88, 41,  7, 91, 48,  5, 62],
      dtype=int64), array([ 0, 62, 46, 92, 27, 57, 66, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75,
       32, 36,  4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28,
       33, 82, 54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 72, 68, 80, 24,
       60, 50, 86, 76, 61, 59, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20,
       71,  9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3,
       18, 52, 78, 89, 48,  5, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 62,  5, 48, 91,  7, 41, 88, 30, 79, 55, 96, 74, 89, 78, 52, 18,
        3, 64, 25, 65, 69, 21, 15, 87, 93, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 59, 61, 76, 86, 50, 60,
       24, 80, 68, 72, 67, 84,  8,  6, 56, 19, 85, 34, 26, 11, 54, 82, 33,
       28, 45, 42,  2, 13, 70, 40, 99, 47, 29, 38, 95, 77, 51,  4, 36, 32,
       75, 12, 94, 81, 49, 43,  1, 53, 39, 63, 66, 57, 27, 92, 46],
      dtype=int64), array([ 0, 92, 27, 57, 66, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75, 32, 36,
        4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28, 33, 82,
       54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 72, 68, 80, 24, 60, 50,
       86, 76, 61, 59, 22, 97, 44, 90, 46, 31, 10, 14, 16, 58, 73, 20, 71,
        9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3, 18,
       52, 78, 89, 74, 96, 55, 79, 30, 88, 41,  7, 91, 48,  5, 62],
      dtype=int64)]
2025-08-05 09:52:23,345 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:23,345 - ExploitationExpert - INFO - populations: [{'tour': array([31, 63, 89, 72, 52, 60, 38, 16, 87,  8,  2, 88, 49, 11, 12, 84, 26,
       23, 54, 27, 13, 55, 37, 47,  3, 94, 65, 95, 48, 64, 24,  1, 96, 29,
       28, 41, 10, 99, 33,  7, 93, 20, 76, 77, 70, 78,  4, 46, 36, 61, 92,
       39,  6, 69, 35, 51, 68,  5, 42, 86, 75, 81, 18, 56, 14, 74,  9, 15,
       71, 59, 45, 53, 97, 85, 98, 82, 21, 80, 22, 83, 17, 57, 90, 91, 50,
       44, 40, 62, 25, 30, 43, 34, 79, 73, 58,  0, 67, 19, 66, 32],
      dtype=int64), 'cur_cost': 182553.0}, {'tour': [0, 19, 16, 1, 43, 49, 72, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 63, 39, 53, 68, 80, 24, 60, 50, 86, 8, 6, 56, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 54, 82, 33, 28, 45, 2, 42, 13, 70, 40, 99, 47], 'cur_cost': 32119.0}, {'tour': [73, 19, 13, 70, 12, 42, 4, 39, 84, 2, 92, 56, 27, 86, 29, 6, 43, 47, 94, 26, 45, 68, 51, 95, 11, 66, 61, 97, 59, 91, 64, 96, 0, 9, 31, 90, 41, 17, 21, 69, 87, 23, 25, 18, 37, 55, 30, 65, 71, 16, 58, 78, 5, 22, 24, 85, 34, 57, 44, 80, 67, 36, 53, 63, 38, 99, 46, 50, 10, 52, 79, 89, 98, 88, 49, 54, 76, 14, 62, 7, 60, 82, 33, 28, 81, 32, 40, 1, 8, 72, 75, 74, 48, 20, 93, 15, 83, 35, 3, 77], 'cur_cost': 105252.0}, {'tour': [99, 18, 88, 41, 50, 43, 77, 59, 58, 37, 20, 16, 13, 95, 33, 29, 44, 87, 52, 12, 78, 55, 71, 24, 70, 66, 45, 98, 81, 17, 96, 54, 26, 15, 46, 28, 93, 5, 22, 40, 4, 25, 34, 75, 97, 83, 36, 94, 80, 73, 0, 90, 49, 57, 84, 74, 10, 60, 14, 3, 38, 89, 6, 85, 35, 30, 82, 56, 86, 32, 76, 62, 63, 65, 21, 2, 51, 8, 91, 53, 72, 61, 48, 64, 42, 1, 23, 47, 67, 19, 92, 9, 68, 69, 79, 39, 27, 11, 7, 31], 'cur_cost': 169986.0}, {'tour': [73, 70, 39, 92, 43, 47, 26, 51, 11, 84, 29, 74, 91, 9, 85, 34, 17, 23, 44, 10, 65, 20, 5, 35, 37, 80, 63, 38, 46, 96, 32, 88, 41, 94, 28, 18, 98, 8, 76, 86, 19, 82, 52, 12, 93, 50, 75, 4, 72, 62, 22, 30, 67, 0, 81, 97, 6, 68, 13, 95, 21, 16, 59, 27, 45, 15, 83, 61, 7, 78, 64, 57, 69, 36, 25, 33, 58, 55, 87, 31, 56, 66, 1, 2, 14, 89, 3, 60, 42, 77, 71, 49, 40, 48, 53, 54, 99, 24, 79, 90], 'cur_cost': 160363.0}, {'tour': [0, 13, 14, 10, 16, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 32920.0}, {'tour': [0, 3, 20, 16, 14, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 25, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 30093.0}, {'tour': [74, 16, 7, 78, 65, 71, 57, 49, 75, 66, 67, 63, 82, 70, 61, 20, 5, 52, 91, 35, 64, 37, 18, 93, 48, 44, 17, 21, 69, 10, 31, 58, 26, 68, 76, 90, 56, 45, 47, 19, 50, 43, 27, 39, 60, 34, 38, 6, 42, 12, 72, 51, 28, 77, 13, 2, 86, 99, 4, 29, 32, 88, 30, 25, 87, 41, 89, 62, 73, 3, 22, 24, 11, 85, 8, 54, 36, 1, 92, 59, 14, 15, 46, 0, 23, 97, 98, 83, 9, 80, 33, 40, 81, 84, 53, 94, 95, 79, 55, 96], 'cur_cost': 101375.0}, {'tour': [85, 21, 16, 69, 31, 70, 68, 6, 5, 54, 64, 26, 34, 33, 45, 7, 61, 50, 96, 63, 8, 24, 59, 78, 56, 20, 12, 89, 23, 28, 11, 22, 71, 74, 55, 57, 25, 88, 27, 97, 58, 1, 84, 36, 44, 99, 82, 41, 86, 62, 77, 19, 80, 92, 0, 2, 93, 14, 35, 10, 15, 98, 30, 53, 75, 9, 48, 83, 73, 60, 67, 3, 32, 29, 72, 87, 17, 43, 66, 18, 40, 90, 46, 79, 39, 13, 65, 51, 42, 38, 76, 95, 47, 91, 81, 49, 37, 4, 94, 52], 'cur_cost': 165081.0}, {'tour': [0, 19, 12, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 94, 81, 49, 43, 72, 67, 84, 38, 29, 33, 82, 54, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 60, 24, 80, 68, 63, 39, 53, 1, 50, 86, 8, 6, 56, 42], 'cur_cost': 32340.0}]
2025-08-05 09:52:23,346 - ExploitationExpert - INFO - 局部搜索耗时: 0.03秒，最大迭代次数: 10
2025-08-05 09:52:23,347 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 388, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 388, 'cache_hits': 0, 'similarity_calculations': 2035, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:23,348 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([31, 63, 89, 72, 52, 60, 38, 16, 87,  8,  2, 88, 49, 11, 12, 84, 26,
       23, 54, 27, 13, 55, 37, 47,  3, 94, 65, 95, 48, 64, 24,  1, 96, 29,
       28, 41, 10, 99, 33,  7, 93, 20, 76, 77, 70, 78,  4, 46, 36, 61, 92,
       39,  6, 69, 35, 51, 68,  5, 42, 86, 75, 81, 18, 56, 14, 74,  9, 15,
       71, 59, 45, 53, 97, 85, 98, 82, 21, 80, 22, 83, 17, 57, 90, 91, 50,
       44, 40, 62, 25, 30, 43, 34, 79, 73, 58,  0, 67, 19, 66, 32],
      dtype=int64), 'cur_cost': 182553.0, 'intermediate_solutions': [{'tour': array([58, 97,  4,  8, 22, 68, 47, 50, 64, 89, 77, 46, 59, 48, 16, 27, 12,
       86, 83, 90,  3, 45, 26, 57, 43, 92, 70,  9, 55, 93, 56, 33, 95, 67,
       61, 19, 72, 98, 87, 30, 36, 82, 78, 28,  0, 75, 21,  2, 62,  7, 79,
       91, 52, 39, 23, 69, 40, 10, 42, 74, 13, 71, 15, 54, 60,  1,  5, 84,
        6, 24, 76, 96, 32, 63, 66, 44, 25, 99, 65, 80, 81, 31, 38, 88, 17,
       51, 85, 14, 29, 41, 53, 49, 18, 11, 73, 37, 35, 20, 94, 34]), 'cur_cost': 180305.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 8, 58, 97,  4, 22, 68, 47, 50, 64, 89, 77, 46, 59, 48, 16, 27, 12,
       86, 83, 90,  3, 45, 26, 57, 43, 92, 70,  9, 55, 93, 56, 33, 95, 67,
       61, 19, 72, 98, 87, 30, 36, 82, 78, 28,  0, 75, 21,  2, 62,  7, 79,
       91, 52, 39, 23, 69, 40, 10, 42, 74, 13, 71, 15, 54, 60,  1,  5, 84,
        6, 24, 76, 96, 32, 63, 66, 44, 25, 99, 65, 80, 81, 31, 38, 88, 17,
       51, 85, 14, 29, 41, 53, 49, 18, 11, 73, 37, 35, 20, 94, 34]), 'cur_cost': 181024.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([22,  8, 58, 97,  4, 68, 47, 50, 64, 89, 77, 46, 59, 48, 16, 27, 12,
       86, 83, 90,  3, 45, 26, 57, 43, 92, 70,  9, 55, 93, 56, 33, 95, 67,
       61, 19, 72, 98, 87, 30, 36, 82, 78, 28,  0, 75, 21,  2, 62,  7, 79,
       91, 52, 39, 23, 69, 40, 10, 42, 74, 13, 71, 15, 54, 60,  1,  5, 84,
        6, 24, 76, 96, 32, 63, 66, 44, 25, 99, 65, 80, 81, 31, 38, 88, 17,
       51, 85, 14, 29, 41, 53, 49, 18, 11, 73, 37, 35, 20, 94, 34]), 'cur_cost': 179865.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4,  8, 58, 97, 22, 68, 47, 50, 64, 89, 77, 46, 59, 48, 16, 27, 12,
       86, 83, 90,  3, 45, 26, 57, 43, 92, 70,  9, 55, 93, 56, 33, 95, 67,
       61, 19, 72, 98, 87, 30, 36, 82, 78, 28,  0, 75, 21,  2, 62,  7, 79,
       91, 52, 39, 23, 69, 40, 10, 42, 74, 13, 71, 15, 54, 60,  1,  5, 84,
        6, 24, 76, 96, 32, 63, 66, 44, 25, 99, 65, 80, 81, 31, 38, 88, 17,
       51, 85, 14, 29, 41, 53, 49, 18, 11, 73, 37, 35, 20, 94, 34]), 'cur_cost': 179124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4, 22,  8, 58, 97, 68, 47, 50, 64, 89, 77, 46, 59, 48, 16, 27, 12,
       86, 83, 90,  3, 45, 26, 57, 43, 92, 70,  9, 55, 93, 56, 33, 95, 67,
       61, 19, 72, 98, 87, 30, 36, 82, 78, 28,  0, 75, 21,  2, 62,  7, 79,
       91, 52, 39, 23, 69, 40, 10, 42, 74, 13, 71, 15, 54, 60,  1,  5, 84,
        6, 24, 76, 96, 32, 63, 66, 44, 25, 99, 65, 80, 81, 31, 38, 88, 17,
       51, 85, 14, 29, 41, 53, 49, 18, 11, 73, 37, 35, 20, 94, 34]), 'cur_cost': 180891.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:23,348 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 182553.00)
2025-08-05 09:52:23,348 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:23,348 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:23,348 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:23,365 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 100
2025-08-05 09:52:23,365 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,366 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,366 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,366 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,367 - ExplorationExpert - INFO - 探索路径生成完成，成本: 90809.0, 路径长度: 100, 收集中间解: 3
2025-08-05 09:52:23,367 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [43, 38, 47, 95, 40, 32, 60, 24, 66, 97, 46, 73, 22, 33, 82, 8, 90, 20, 93, 88, 27, 67, 76, 45, 59, 63, 84, 19, 34, 85, 50, 53, 68, 51, 6, 16, 87, 25, 55, 41, 48, 17, 71, 10, 14, 89, 96, 31, 58, 18, 0, 35, 9, 21, 52, 91, 65, 64, 74, 83, 5, 56, 28, 72, 75, 94, 81, 57, 80, 86, 49, 2, 99, 13, 70, 26, 92, 39, 1, 7, 78, 23, 69, 44, 98, 61, 4, 12, 77, 54, 62, 37, 15, 30, 3, 79, 11, 42, 29, 36], 'cur_cost': 90809.0, 'intermediate_solutions': [{'tour': [0, 19, 16, 41, 43, 49, 72, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 63, 39, 53, 68, 80, 24, 60, 50, 86, 8, 6, 56, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 1, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 54, 82, 33, 28, 45, 2, 42, 13, 70, 40, 99, 47], 'cur_cost': 35301.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 16, 1, 43, 49, 72, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 63, 39, 53, 68, 80, 24, 60, 50, 86, 8, 5, 91, 7, 41, 88, 30, 79, 55, 96, 3, 64, 65, 69, 93, 21, 15, 87, 52, 78, 17, 23, 37, 35, 83, 9, 71, 58, 20, 73, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26, 11, 56, 6, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 54, 82, 33, 28, 45, 2, 42, 13, 70, 40, 99, 47], 'cur_cost': 35675.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 16, 1, 43, 49, 72, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 63, 39, 53, 68, 80, 24, 60, 50, 86, 8, 6, 56, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 64, 18, 74, 25, 98, 46, 92, 27, 66, 57, 54, 82, 33, 28, 45, 2, 42, 13, 70, 40, 99, 47], 'cur_cost': 33060.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:23,368 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 90809.00)
2025-08-05 09:52:23,368 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:23,368 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:23,368 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:23,373 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 09:52:23,373 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,373 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,374 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,374 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,374 - ExplorationExpert - INFO - 探索路径生成完成，成本: 29602.0, 路径长度: 100, 收集中间解: 3
2025-08-05 09:52:23,375 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 10, 16, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 42, 61, 59, 76, 22, 97, 90, 44, 31, 14, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57], 'cur_cost': 29602.0, 'intermediate_solutions': [{'tour': [73, 19, 13, 70, 12, 42, 4, 39, 84, 2, 92, 56, 27, 86, 29, 6, 43, 47, 94, 26, 45, 68, 51, 95, 11, 66, 61, 97, 59, 91, 64, 96, 0, 9, 31, 90, 77, 17, 21, 69, 87, 23, 25, 18, 37, 55, 30, 65, 71, 16, 58, 78, 5, 22, 24, 85, 34, 57, 44, 80, 67, 36, 53, 63, 38, 99, 46, 50, 10, 52, 79, 89, 98, 88, 49, 54, 76, 14, 62, 7, 60, 82, 33, 28, 81, 32, 40, 1, 8, 72, 75, 74, 48, 20, 93, 15, 83, 35, 3, 41], 'cur_cost': 104291.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [73, 19, 13, 70, 12, 42, 4, 39, 84, 2, 92, 56, 27, 86, 29, 6, 43, 47, 94, 26, 45, 68, 51, 95, 11, 66, 61, 97, 59, 91, 64, 96, 0, 9, 31, 90, 41, 17, 21, 69, 87, 23, 25, 18, 37, 55, 30, 65, 22, 5, 78, 58, 16, 71, 24, 85, 34, 57, 44, 80, 67, 36, 53, 63, 38, 99, 46, 50, 10, 52, 79, 89, 98, 88, 49, 54, 76, 14, 62, 7, 60, 82, 33, 28, 81, 32, 40, 1, 8, 72, 75, 74, 48, 20, 93, 15, 83, 35, 3, 77], 'cur_cost': 106687.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [73, 19, 13, 70, 12, 42, 4, 39, 84, 2, 92, 56, 27, 86, 29, 6, 43, 47, 94, 26, 45, 68, 51, 95, 11, 66, 61, 97, 59, 91, 64, 96, 0, 9, 31, 90, 41, 17, 21, 69, 87, 23, 25, 18, 37, 55, 30, 65, 71, 16, 58, 78, 5, 22, 24, 85, 34, 57, 44, 80, 67, 36, 53, 38, 99, 46, 50, 10, 63, 52, 79, 89, 98, 88, 49, 54, 76, 14, 62, 7, 60, 82, 33, 28, 81, 32, 40, 1, 8, 72, 75, 74, 48, 20, 93, 15, 83, 35, 3, 77], 'cur_cost': 108191.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:23,375 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 29602.00)
2025-08-05 09:52:23,375 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:52:23,375 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:23,375 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:23,376 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 171323.0
2025-08-05 09:52:23,400 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:23,401 - ExploitationExpert - INFO - res_population_costs: [21389, 21411, 21776, 22282, 22308, 22356, 22427.0]
2025-08-05 09:52:23,401 - ExploitationExpert - INFO - res_populations: [array([ 0, 62, 89, 48,  5, 91,  7, 41, 88, 30, 79, 55, 96, 74, 18,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26,
       11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99, 47,
       51, 77, 95,  4, 36, 32, 75, 12, 94, 81, 38, 29, 84, 67, 72, 49, 43,
        1, 53, 39, 63, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 62,  5, 48, 89, 18, 74, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 59, 61, 76, 50, 86,  8,
        6, 56, 19, 85, 34, 26, 11, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40,
       99, 47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81, 49, 43,  1,
       53, 39, 63, 68, 72, 67, 84, 80, 24, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 91, 74, 96, 55, 79, 30, 88, 41,  7, 66, 27, 92, 57, 60, 24, 80,
       84, 67, 72, 68, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75, 32, 36,  4,
       51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28, 33, 82, 54,
       11, 26, 34, 85, 19, 56,  6,  8, 86, 50, 76, 61, 59, 22, 97, 90, 44,
       31, 10, 14, 16, 58, 73, 20, 71,  9, 83, 35, 98, 37, 23, 17, 78, 52,
       87, 15, 93, 21, 69, 65, 25, 64,  3, 18, 89, 48,  5, 62, 46],
      dtype=int64), array([ 0, 46, 92, 27, 66, 57, 68, 63, 39, 53,  1, 43, 72, 49, 81, 94, 12,
       75, 32, 36,  4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45,
       28, 33, 82, 54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 80, 24, 60,
       50, 86, 76, 61, 59, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20, 71,
        9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3, 18,
       52, 78, 89, 74, 96, 55, 79, 30, 88, 41,  7, 91, 48,  5, 62],
      dtype=int64), array([ 0, 62, 46, 92, 27, 57, 66, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75,
       32, 36,  4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28,
       33, 82, 54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 72, 68, 80, 24,
       60, 50, 86, 76, 61, 59, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20,
       71,  9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3,
       18, 52, 78, 89, 48,  5, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 62,  5, 48, 91,  7, 41, 88, 30, 79, 55, 96, 74, 89, 78, 52, 18,
        3, 64, 25, 65, 69, 21, 15, 87, 93, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 59, 61, 76, 86, 50, 60,
       24, 80, 68, 72, 67, 84,  8,  6, 56, 19, 85, 34, 26, 11, 54, 82, 33,
       28, 45, 42,  2, 13, 70, 40, 99, 47, 29, 38, 95, 77, 51,  4, 36, 32,
       75, 12, 94, 81, 49, 43,  1, 53, 39, 63, 66, 57, 27, 92, 46],
      dtype=int64), array([ 0, 92, 27, 57, 66, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75, 32, 36,
        4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28, 33, 82,
       54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 72, 68, 80, 24, 60, 50,
       86, 76, 61, 59, 22, 97, 44, 90, 46, 31, 10, 14, 16, 58, 73, 20, 71,
        9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3, 18,
       52, 78, 89, 74, 96, 55, 79, 30, 88, 41,  7, 91, 48,  5, 62],
      dtype=int64)]
2025-08-05 09:52:23,405 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:23,405 - ExploitationExpert - INFO - populations: [{'tour': array([31, 63, 89, 72, 52, 60, 38, 16, 87,  8,  2, 88, 49, 11, 12, 84, 26,
       23, 54, 27, 13, 55, 37, 47,  3, 94, 65, 95, 48, 64, 24,  1, 96, 29,
       28, 41, 10, 99, 33,  7, 93, 20, 76, 77, 70, 78,  4, 46, 36, 61, 92,
       39,  6, 69, 35, 51, 68,  5, 42, 86, 75, 81, 18, 56, 14, 74,  9, 15,
       71, 59, 45, 53, 97, 85, 98, 82, 21, 80, 22, 83, 17, 57, 90, 91, 50,
       44, 40, 62, 25, 30, 43, 34, 79, 73, 58,  0, 67, 19, 66, 32],
      dtype=int64), 'cur_cost': 182553.0}, {'tour': [43, 38, 47, 95, 40, 32, 60, 24, 66, 97, 46, 73, 22, 33, 82, 8, 90, 20, 93, 88, 27, 67, 76, 45, 59, 63, 84, 19, 34, 85, 50, 53, 68, 51, 6, 16, 87, 25, 55, 41, 48, 17, 71, 10, 14, 89, 96, 31, 58, 18, 0, 35, 9, 21, 52, 91, 65, 64, 74, 83, 5, 56, 28, 72, 75, 94, 81, 57, 80, 86, 49, 2, 99, 13, 70, 26, 92, 39, 1, 7, 78, 23, 69, 44, 98, 61, 4, 12, 77, 54, 62, 37, 15, 30, 3, 79, 11, 42, 29, 36], 'cur_cost': 90809.0}, {'tour': [0, 10, 16, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 42, 61, 59, 76, 22, 97, 90, 44, 31, 14, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57], 'cur_cost': 29602.0}, {'tour': array([ 3,  7, 64, 44,  4,  8, 94, 74, 96, 15, 55, 22, 84, 81, 18, 70, 73,
       29, 34, 89, 24, 48, 93, 14, 82, 79, 88, 71, 66, 12, 25, 59, 13, 49,
       87, 98, 57, 36, 69, 42, 28, 85, 91, 26, 21,  5, 86, 33, 77, 43, 58,
       46, 11, 37, 76, 31, 80, 35, 30, 65,  6, 10, 92, 19, 99, 78,  1,  0,
       16, 97, 50, 72, 61, 54,  9, 67, 38, 27, 17, 62, 41, 75, 90, 53, 32,
       83, 95, 51, 20, 56, 52, 68, 45, 47, 23, 60,  2, 40, 39, 63],
      dtype=int64), 'cur_cost': 171323.0}, {'tour': [73, 70, 39, 92, 43, 47, 26, 51, 11, 84, 29, 74, 91, 9, 85, 34, 17, 23, 44, 10, 65, 20, 5, 35, 37, 80, 63, 38, 46, 96, 32, 88, 41, 94, 28, 18, 98, 8, 76, 86, 19, 82, 52, 12, 93, 50, 75, 4, 72, 62, 22, 30, 67, 0, 81, 97, 6, 68, 13, 95, 21, 16, 59, 27, 45, 15, 83, 61, 7, 78, 64, 57, 69, 36, 25, 33, 58, 55, 87, 31, 56, 66, 1, 2, 14, 89, 3, 60, 42, 77, 71, 49, 40, 48, 53, 54, 99, 24, 79, 90], 'cur_cost': 160363.0}, {'tour': [0, 13, 14, 10, 16, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 32920.0}, {'tour': [0, 3, 20, 16, 14, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 25, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 30093.0}, {'tour': [74, 16, 7, 78, 65, 71, 57, 49, 75, 66, 67, 63, 82, 70, 61, 20, 5, 52, 91, 35, 64, 37, 18, 93, 48, 44, 17, 21, 69, 10, 31, 58, 26, 68, 76, 90, 56, 45, 47, 19, 50, 43, 27, 39, 60, 34, 38, 6, 42, 12, 72, 51, 28, 77, 13, 2, 86, 99, 4, 29, 32, 88, 30, 25, 87, 41, 89, 62, 73, 3, 22, 24, 11, 85, 8, 54, 36, 1, 92, 59, 14, 15, 46, 0, 23, 97, 98, 83, 9, 80, 33, 40, 81, 84, 53, 94, 95, 79, 55, 96], 'cur_cost': 101375.0}, {'tour': [85, 21, 16, 69, 31, 70, 68, 6, 5, 54, 64, 26, 34, 33, 45, 7, 61, 50, 96, 63, 8, 24, 59, 78, 56, 20, 12, 89, 23, 28, 11, 22, 71, 74, 55, 57, 25, 88, 27, 97, 58, 1, 84, 36, 44, 99, 82, 41, 86, 62, 77, 19, 80, 92, 0, 2, 93, 14, 35, 10, 15, 98, 30, 53, 75, 9, 48, 83, 73, 60, 67, 3, 32, 29, 72, 87, 17, 43, 66, 18, 40, 90, 46, 79, 39, 13, 65, 51, 42, 38, 76, 95, 47, 91, 81, 49, 37, 4, 94, 52], 'cur_cost': 165081.0}, {'tour': [0, 19, 12, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 94, 81, 49, 43, 72, 67, 84, 38, 29, 33, 82, 54, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 60, 24, 80, 68, 63, 39, 53, 1, 50, 86, 8, 6, 56, 42], 'cur_cost': 32340.0}]
2025-08-05 09:52:23,406 - ExploitationExpert - INFO - 局部搜索耗时: 0.03秒，最大迭代次数: 10
2025-08-05 09:52:23,407 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 389, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 389, 'cache_hits': 0, 'similarity_calculations': 2046, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:23,408 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 3,  7, 64, 44,  4,  8, 94, 74, 96, 15, 55, 22, 84, 81, 18, 70, 73,
       29, 34, 89, 24, 48, 93, 14, 82, 79, 88, 71, 66, 12, 25, 59, 13, 49,
       87, 98, 57, 36, 69, 42, 28, 85, 91, 26, 21,  5, 86, 33, 77, 43, 58,
       46, 11, 37, 76, 31, 80, 35, 30, 65,  6, 10, 92, 19, 99, 78,  1,  0,
       16, 97, 50, 72, 61, 54,  9, 67, 38, 27, 17, 62, 41, 75, 90, 53, 32,
       83, 95, 51, 20, 56, 52, 68, 45, 47, 23, 60,  2, 40, 39, 63],
      dtype=int64), 'cur_cost': 171323.0, 'intermediate_solutions': [{'tour': array([88, 18, 99, 41, 50, 43, 77, 59, 58, 37, 20, 16, 13, 95, 33, 29, 44,
       87, 52, 12, 78, 55, 71, 24, 70, 66, 45, 98, 81, 17, 96, 54, 26, 15,
       46, 28, 93,  5, 22, 40,  4, 25, 34, 75, 97, 83, 36, 94, 80, 73,  0,
       90, 49, 57, 84, 74, 10, 60, 14,  3, 38, 89,  6, 85, 35, 30, 82, 56,
       86, 32, 76, 62, 63, 65, 21,  2, 51,  8, 91, 53, 72, 61, 48, 64, 42,
        1, 23, 47, 67, 19, 92,  9, 68, 69, 79, 39, 27, 11,  7, 31]), 'cur_cost': 171789.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([41, 88, 18, 99, 50, 43, 77, 59, 58, 37, 20, 16, 13, 95, 33, 29, 44,
       87, 52, 12, 78, 55, 71, 24, 70, 66, 45, 98, 81, 17, 96, 54, 26, 15,
       46, 28, 93,  5, 22, 40,  4, 25, 34, 75, 97, 83, 36, 94, 80, 73,  0,
       90, 49, 57, 84, 74, 10, 60, 14,  3, 38, 89,  6, 85, 35, 30, 82, 56,
       86, 32, 76, 62, 63, 65, 21,  2, 51,  8, 91, 53, 72, 61, 48, 64, 42,
        1, 23, 47, 67, 19, 92,  9, 68, 69, 79, 39, 27, 11,  7, 31]), 'cur_cost': 168952.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([50, 41, 88, 18, 99, 43, 77, 59, 58, 37, 20, 16, 13, 95, 33, 29, 44,
       87, 52, 12, 78, 55, 71, 24, 70, 66, 45, 98, 81, 17, 96, 54, 26, 15,
       46, 28, 93,  5, 22, 40,  4, 25, 34, 75, 97, 83, 36, 94, 80, 73,  0,
       90, 49, 57, 84, 74, 10, 60, 14,  3, 38, 89,  6, 85, 35, 30, 82, 56,
       86, 32, 76, 62, 63, 65, 21,  2, 51,  8, 91, 53, 72, 61, 48, 64, 42,
        1, 23, 47, 67, 19, 92,  9, 68, 69, 79, 39, 27, 11,  7, 31]), 'cur_cost': 169271.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([99, 41, 88, 18, 50, 43, 77, 59, 58, 37, 20, 16, 13, 95, 33, 29, 44,
       87, 52, 12, 78, 55, 71, 24, 70, 66, 45, 98, 81, 17, 96, 54, 26, 15,
       46, 28, 93,  5, 22, 40,  4, 25, 34, 75, 97, 83, 36, 94, 80, 73,  0,
       90, 49, 57, 84, 74, 10, 60, 14,  3, 38, 89,  6, 85, 35, 30, 82, 56,
       86, 32, 76, 62, 63, 65, 21,  2, 51,  8, 91, 53, 72, 61, 48, 64, 42,
        1, 23, 47, 67, 19, 92,  9, 68, 69, 79, 39, 27, 11,  7, 31]), 'cur_cost': 169923.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([99, 50, 41, 88, 18, 43, 77, 59, 58, 37, 20, 16, 13, 95, 33, 29, 44,
       87, 52, 12, 78, 55, 71, 24, 70, 66, 45, 98, 81, 17, 96, 54, 26, 15,
       46, 28, 93,  5, 22, 40,  4, 25, 34, 75, 97, 83, 36, 94, 80, 73,  0,
       90, 49, 57, 84, 74, 10, 60, 14,  3, 38, 89,  6, 85, 35, 30, 82, 56,
       86, 32, 76, 62, 63, 65, 21,  2, 51,  8, 91, 53, 72, 61, 48, 64, 42,
        1, 23, 47, 67, 19, 92,  9, 68, 69, 79, 39, 27, 11,  7, 31]), 'cur_cost': 169436.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:23,408 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 171323.00)
2025-08-05 09:52:23,408 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:23,408 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:23,409 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:23,413 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 09:52:23,413 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,414 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,414 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,414 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,415 - ExplorationExpert - INFO - 探索路径生成完成，成本: 31217.0, 路径长度: 100, 收集中间解: 3
2025-08-05 09:52:23,415 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 18, 11, 6, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 26, 85, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 31217.0, 'intermediate_solutions': [{'tour': [73, 70, 39, 92, 43, 47, 26, 51, 11, 84, 29, 74, 91, 9, 85, 34, 17, 23, 44, 10, 65, 20, 5, 35, 37, 80, 63, 89, 46, 96, 32, 88, 41, 94, 28, 18, 98, 8, 76, 86, 19, 82, 52, 12, 93, 50, 75, 4, 72, 62, 22, 30, 67, 0, 81, 97, 6, 68, 13, 95, 21, 16, 59, 27, 45, 15, 83, 61, 7, 78, 64, 57, 69, 36, 25, 33, 58, 55, 87, 31, 56, 66, 1, 2, 14, 38, 3, 60, 42, 77, 71, 49, 40, 48, 53, 54, 99, 24, 79, 90], 'cur_cost': 163658.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [73, 70, 39, 92, 43, 47, 26, 51, 11, 84, 29, 74, 91, 9, 85, 34, 17, 23, 44, 10, 65, 20, 5, 35, 37, 80, 63, 38, 46, 96, 32, 88, 41, 94, 28, 18, 98, 36, 69, 57, 64, 78, 7, 61, 83, 15, 45, 27, 59, 16, 21, 95, 13, 68, 6, 97, 81, 0, 67, 30, 22, 62, 72, 4, 75, 50, 93, 12, 52, 82, 19, 86, 76, 8, 25, 33, 58, 55, 87, 31, 56, 66, 1, 2, 14, 89, 3, 60, 42, 77, 71, 49, 40, 48, 53, 54, 99, 24, 79, 90], 'cur_cost': 160850.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [73, 70, 39, 92, 43, 47, 26, 51, 84, 29, 74, 91, 9, 85, 34, 17, 23, 44, 10, 65, 20, 5, 35, 37, 80, 63, 38, 46, 96, 32, 88, 41, 94, 28, 18, 98, 8, 76, 86, 19, 82, 52, 12, 93, 50, 75, 4, 72, 62, 22, 30, 67, 0, 81, 97, 6, 68, 13, 95, 21, 16, 59, 27, 45, 15, 83, 61, 7, 78, 64, 57, 69, 36, 25, 33, 58, 55, 87, 31, 56, 66, 1, 11, 2, 14, 89, 3, 60, 42, 77, 71, 49, 40, 48, 53, 54, 99, 24, 79, 90], 'cur_cost': 159518.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:23,415 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 31217.00)
2025-08-05 09:52:23,415 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:23,416 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:23,416 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:23,420 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 09:52:23,420 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,420 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,420 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,421 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,421 - ExplorationExpert - INFO - 探索路径生成完成，成本: 33486.0, 路径长度: 100, 收集中间解: 3
2025-08-05 09:52:23,421 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 14, 11, 12, 32, 75, 94, 81, 49, 43, 72, 67, 84, 38, 29, 95, 77, 51, 4, 36, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 6, 8, 56, 86, 50, 60, 24, 80, 68, 63, 39, 53, 1, 66, 27, 92, 57, 76, 59, 61, 85, 26, 34, 19, 22, 97, 90, 44, 31, 10, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 42], 'cur_cost': 33486.0, 'intermediate_solutions': [{'tour': [0, 13, 14, 10, 16, 31, 90, 97, 22, 44, 46, 62, 57, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 25, 98, 92, 27, 66, 5, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 36602.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 14, 10, 16, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 67, 63, 39, 53, 1, 43, 49, 72, 68, 80, 24, 60, 57, 66, 27, 92, 98, 25, 18, 74, 91, 7, 41, 88, 30, 79, 55, 96, 3, 64, 65, 69, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 38530.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 14, 10, 16, 31, 90, 97, 22, 44, 46, 79, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 30, 88, 41, 7, 91, 74, 18, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 34738.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:23,421 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 33486.00)
2025-08-05 09:52:23,421 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:23,422 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:23,422 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:23,425 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 09:52:23,425 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,426 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,426 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,426 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,427 - ExplorationExpert - INFO - 探索路径生成完成，成本: 34467.0, 路径长度: 100, 收集中间解: 3
2025-08-05 09:52:23,427 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 12, 17, 5, 48, 62, 91, 7, 41, 88, 30, 79, 55, 96, 74, 18, 52, 78, 87, 15, 21, 93, 69, 65, 64, 3, 25, 89, 9, 83, 71, 20, 73, 58, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42, 35, 37, 23, 98], 'cur_cost': 34467.0, 'intermediate_solutions': [{'tour': [0, 3, 20, 16, 14, 10, 31, 90, 97, 92, 44, 46, 62, 5, 48, 89, 9, 83, 71, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 25, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 98, 22, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 31772.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 20, 16, 14, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 25, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 40, 70, 99, 47, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 30464.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 20, 16, 14, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 25, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 98, 92, 54, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 32679.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:23,427 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 34467.00)
2025-08-05 09:52:23,427 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:23,427 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:23,427 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:23,431 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 09:52:23,431 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,431 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,431 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,432 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,432 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30591.0, 路径长度: 100, 收集中间解: 3
2025-08-05 09:52:23,432 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 7, 19, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 12, 94, 81, 49, 43, 72, 67, 84, 38, 29, 33, 82, 54, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 60, 24, 80, 68, 63, 39, 53, 1, 50, 86, 8, 6, 56, 42], 'cur_cost': 30591.0, 'intermediate_solutions': [{'tour': [74, 16, 7, 78, 65, 71, 57, 49, 75, 66, 67, 63, 82, 70, 61, 20, 5, 52, 91, 35, 64, 37, 18, 93, 48, 44, 17, 21, 69, 10, 31, 58, 26, 68, 76, 90, 56, 45, 47, 19, 50, 43, 27, 39, 60, 34, 59, 6, 42, 12, 72, 51, 28, 77, 13, 2, 86, 99, 4, 29, 32, 88, 30, 25, 87, 41, 89, 62, 73, 3, 22, 24, 11, 85, 8, 54, 36, 1, 92, 38, 14, 15, 46, 0, 23, 97, 98, 83, 9, 80, 33, 40, 81, 84, 53, 94, 95, 79, 55, 96], 'cur_cost': 102464.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [74, 16, 7, 78, 65, 71, 57, 49, 85, 11, 24, 22, 3, 73, 62, 89, 41, 87, 25, 30, 88, 32, 29, 4, 99, 86, 2, 13, 77, 28, 51, 72, 12, 42, 6, 38, 34, 60, 39, 27, 43, 50, 19, 47, 45, 56, 90, 76, 68, 26, 58, 31, 10, 69, 21, 17, 44, 48, 93, 18, 37, 64, 35, 91, 52, 5, 20, 61, 70, 82, 63, 67, 66, 75, 8, 54, 36, 1, 92, 59, 14, 15, 46, 0, 23, 97, 98, 83, 9, 80, 33, 40, 81, 84, 53, 94, 95, 79, 55, 96], 'cur_cost': 103210.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [74, 16, 7, 78, 65, 71, 57, 49, 75, 66, 67, 63, 82, 70, 61, 20, 5, 52, 91, 35, 64, 37, 18, 93, 48, 44, 17, 21, 69, 10, 31, 58, 26, 68, 76, 90, 56, 45, 47, 19, 50, 43, 96, 27, 39, 60, 34, 38, 6, 42, 12, 72, 51, 28, 77, 13, 2, 86, 99, 4, 29, 32, 88, 30, 25, 87, 41, 89, 62, 73, 3, 22, 24, 11, 85, 8, 54, 36, 1, 92, 59, 14, 15, 46, 0, 23, 97, 98, 83, 9, 80, 33, 40, 81, 84, 53, 94, 95, 79, 55], 'cur_cost': 103320.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:23,432 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 30591.00)
2025-08-05 09:52:23,432 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:52:23,433 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:23,433 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:23,433 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 163126.0
2025-08-05 09:52:23,458 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:23,458 - ExploitationExpert - INFO - res_population_costs: [21389, 21411, 21776, 22282, 22308, 22356, 22427.0]
2025-08-05 09:52:23,458 - ExploitationExpert - INFO - res_populations: [array([ 0, 62, 89, 48,  5, 91,  7, 41, 88, 30, 79, 55, 96, 74, 18,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26,
       11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99, 47,
       51, 77, 95,  4, 36, 32, 75, 12, 94, 81, 38, 29, 84, 67, 72, 49, 43,
        1, 53, 39, 63, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 62,  5, 48, 89, 18, 74, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 59, 61, 76, 50, 86,  8,
        6, 56, 19, 85, 34, 26, 11, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40,
       99, 47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81, 49, 43,  1,
       53, 39, 63, 68, 72, 67, 84, 80, 24, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 91, 74, 96, 55, 79, 30, 88, 41,  7, 66, 27, 92, 57, 60, 24, 80,
       84, 67, 72, 68, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75, 32, 36,  4,
       51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28, 33, 82, 54,
       11, 26, 34, 85, 19, 56,  6,  8, 86, 50, 76, 61, 59, 22, 97, 90, 44,
       31, 10, 14, 16, 58, 73, 20, 71,  9, 83, 35, 98, 37, 23, 17, 78, 52,
       87, 15, 93, 21, 69, 65, 25, 64,  3, 18, 89, 48,  5, 62, 46],
      dtype=int64), array([ 0, 46, 92, 27, 66, 57, 68, 63, 39, 53,  1, 43, 72, 49, 81, 94, 12,
       75, 32, 36,  4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45,
       28, 33, 82, 54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 80, 24, 60,
       50, 86, 76, 61, 59, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20, 71,
        9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3, 18,
       52, 78, 89, 74, 96, 55, 79, 30, 88, 41,  7, 91, 48,  5, 62],
      dtype=int64), array([ 0, 62, 46, 92, 27, 57, 66, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75,
       32, 36,  4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28,
       33, 82, 54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 72, 68, 80, 24,
       60, 50, 86, 76, 61, 59, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20,
       71,  9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3,
       18, 52, 78, 89, 48,  5, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 62,  5, 48, 91,  7, 41, 88, 30, 79, 55, 96, 74, 89, 78, 52, 18,
        3, 64, 25, 65, 69, 21, 15, 87, 93, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 59, 61, 76, 86, 50, 60,
       24, 80, 68, 72, 67, 84,  8,  6, 56, 19, 85, 34, 26, 11, 54, 82, 33,
       28, 45, 42,  2, 13, 70, 40, 99, 47, 29, 38, 95, 77, 51,  4, 36, 32,
       75, 12, 94, 81, 49, 43,  1, 53, 39, 63, 66, 57, 27, 92, 46],
      dtype=int64), array([ 0, 92, 27, 57, 66, 63, 39, 53,  1, 43, 49, 81, 94, 12, 75, 32, 36,
        4, 51, 77, 95, 38, 29, 47, 99, 40, 70, 13,  2, 42, 45, 28, 33, 82,
       54, 11, 26, 34, 85, 19, 56,  6,  8, 84, 67, 72, 68, 80, 24, 60, 50,
       86, 76, 61, 59, 22, 97, 44, 90, 46, 31, 10, 14, 16, 58, 73, 20, 71,
        9, 83, 35, 98, 37, 23, 17, 93, 87, 15, 21, 69, 65, 25, 64,  3, 18,
       52, 78, 89, 74, 96, 55, 79, 30, 88, 41,  7, 91, 48,  5, 62],
      dtype=int64)]
2025-08-05 09:52:23,465 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:23,465 - ExploitationExpert - INFO - populations: [{'tour': array([31, 63, 89, 72, 52, 60, 38, 16, 87,  8,  2, 88, 49, 11, 12, 84, 26,
       23, 54, 27, 13, 55, 37, 47,  3, 94, 65, 95, 48, 64, 24,  1, 96, 29,
       28, 41, 10, 99, 33,  7, 93, 20, 76, 77, 70, 78,  4, 46, 36, 61, 92,
       39,  6, 69, 35, 51, 68,  5, 42, 86, 75, 81, 18, 56, 14, 74,  9, 15,
       71, 59, 45, 53, 97, 85, 98, 82, 21, 80, 22, 83, 17, 57, 90, 91, 50,
       44, 40, 62, 25, 30, 43, 34, 79, 73, 58,  0, 67, 19, 66, 32],
      dtype=int64), 'cur_cost': 182553.0}, {'tour': [43, 38, 47, 95, 40, 32, 60, 24, 66, 97, 46, 73, 22, 33, 82, 8, 90, 20, 93, 88, 27, 67, 76, 45, 59, 63, 84, 19, 34, 85, 50, 53, 68, 51, 6, 16, 87, 25, 55, 41, 48, 17, 71, 10, 14, 89, 96, 31, 58, 18, 0, 35, 9, 21, 52, 91, 65, 64, 74, 83, 5, 56, 28, 72, 75, 94, 81, 57, 80, 86, 49, 2, 99, 13, 70, 26, 92, 39, 1, 7, 78, 23, 69, 44, 98, 61, 4, 12, 77, 54, 62, 37, 15, 30, 3, 79, 11, 42, 29, 36], 'cur_cost': 90809.0}, {'tour': [0, 10, 16, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 42, 61, 59, 76, 22, 97, 90, 44, 31, 14, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57], 'cur_cost': 29602.0}, {'tour': array([ 3,  7, 64, 44,  4,  8, 94, 74, 96, 15, 55, 22, 84, 81, 18, 70, 73,
       29, 34, 89, 24, 48, 93, 14, 82, 79, 88, 71, 66, 12, 25, 59, 13, 49,
       87, 98, 57, 36, 69, 42, 28, 85, 91, 26, 21,  5, 86, 33, 77, 43, 58,
       46, 11, 37, 76, 31, 80, 35, 30, 65,  6, 10, 92, 19, 99, 78,  1,  0,
       16, 97, 50, 72, 61, 54,  9, 67, 38, 27, 17, 62, 41, 75, 90, 53, 32,
       83, 95, 51, 20, 56, 52, 68, 45, 47, 23, 60,  2, 40, 39, 63],
      dtype=int64), 'cur_cost': 171323.0}, {'tour': [0, 18, 11, 6, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 26, 85, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 31217.0}, {'tour': [0, 14, 11, 12, 32, 75, 94, 81, 49, 43, 72, 67, 84, 38, 29, 95, 77, 51, 4, 36, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 6, 8, 56, 86, 50, 60, 24, 80, 68, 63, 39, 53, 1, 66, 27, 92, 57, 76, 59, 61, 85, 26, 34, 19, 22, 97, 90, 44, 31, 10, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 42], 'cur_cost': 33486.0}, {'tour': [0, 12, 17, 5, 48, 62, 91, 7, 41, 88, 30, 79, 55, 96, 74, 18, 52, 78, 87, 15, 21, 93, 69, 65, 64, 3, 25, 89, 9, 83, 71, 20, 73, 58, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42, 35, 37, 23, 98], 'cur_cost': 34467.0}, {'tour': [0, 7, 19, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 12, 94, 81, 49, 43, 72, 67, 84, 38, 29, 33, 82, 54, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 60, 24, 80, 68, 63, 39, 53, 1, 50, 86, 8, 6, 56, 42], 'cur_cost': 30591.0}, {'tour': array([23, 45, 83, 31, 72, 42, 87, 80, 56, 66, 10, 41,  9, 44, 21, 98, 69,
        5, 64, 46, 14, 28, 84, 11, 52, 58, 38, 12, 76, 16, 34, 54, 19, 95,
       13, 29, 30, 60, 53,  8, 27, 43, 92, 59, 18, 86, 89, 62, 73,  3, 63,
        1, 22, 24, 70, 17,  6, 25, 65, 68, 50, 51, 57, 77, 37, 36, 48, 74,
       40, 99, 67, 79, 96, 39, 32, 82,  4,  0, 71, 15, 26, 49, 90, 91, 61,
       93, 85, 47,  2, 33, 20, 81, 35, 75, 78, 55, 97,  7, 94, 88],
      dtype=int64), 'cur_cost': 163126.0}, {'tour': [0, 19, 12, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 94, 81, 49, 43, 72, 67, 84, 38, 29, 33, 82, 54, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 60, 24, 80, 68, 63, 39, 53, 1, 50, 86, 8, 6, 56, 42], 'cur_cost': 32340.0}]
2025-08-05 09:52:23,469 - ExploitationExpert - INFO - 局部搜索耗时: 0.04秒，最大迭代次数: 10
2025-08-05 09:52:23,470 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 390, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 390, 'cache_hits': 0, 'similarity_calculations': 2058, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:23,471 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([23, 45, 83, 31, 72, 42, 87, 80, 56, 66, 10, 41,  9, 44, 21, 98, 69,
        5, 64, 46, 14, 28, 84, 11, 52, 58, 38, 12, 76, 16, 34, 54, 19, 95,
       13, 29, 30, 60, 53,  8, 27, 43, 92, 59, 18, 86, 89, 62, 73,  3, 63,
        1, 22, 24, 70, 17,  6, 25, 65, 68, 50, 51, 57, 77, 37, 36, 48, 74,
       40, 99, 67, 79, 96, 39, 32, 82,  4,  0, 71, 15, 26, 49, 90, 91, 61,
       93, 85, 47,  2, 33, 20, 81, 35, 75, 78, 55, 97,  7, 94, 88],
      dtype=int64), 'cur_cost': 163126.0, 'intermediate_solutions': [{'tour': array([16, 21, 85, 69, 31, 70, 68,  6,  5, 54, 64, 26, 34, 33, 45,  7, 61,
       50, 96, 63,  8, 24, 59, 78, 56, 20, 12, 89, 23, 28, 11, 22, 71, 74,
       55, 57, 25, 88, 27, 97, 58,  1, 84, 36, 44, 99, 82, 41, 86, 62, 77,
       19, 80, 92,  0,  2, 93, 14, 35, 10, 15, 98, 30, 53, 75,  9, 48, 83,
       73, 60, 67,  3, 32, 29, 72, 87, 17, 43, 66, 18, 40, 90, 46, 79, 39,
       13, 65, 51, 42, 38, 76, 95, 47, 91, 81, 49, 37,  4, 94, 52]), 'cur_cost': 165121.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([69, 16, 21, 85, 31, 70, 68,  6,  5, 54, 64, 26, 34, 33, 45,  7, 61,
       50, 96, 63,  8, 24, 59, 78, 56, 20, 12, 89, 23, 28, 11, 22, 71, 74,
       55, 57, 25, 88, 27, 97, 58,  1, 84, 36, 44, 99, 82, 41, 86, 62, 77,
       19, 80, 92,  0,  2, 93, 14, 35, 10, 15, 98, 30, 53, 75,  9, 48, 83,
       73, 60, 67,  3, 32, 29, 72, 87, 17, 43, 66, 18, 40, 90, 46, 79, 39,
       13, 65, 51, 42, 38, 76, 95, 47, 91, 81, 49, 37,  4, 94, 52]), 'cur_cost': 162452.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([31, 69, 16, 21, 85, 70, 68,  6,  5, 54, 64, 26, 34, 33, 45,  7, 61,
       50, 96, 63,  8, 24, 59, 78, 56, 20, 12, 89, 23, 28, 11, 22, 71, 74,
       55, 57, 25, 88, 27, 97, 58,  1, 84, 36, 44, 99, 82, 41, 86, 62, 77,
       19, 80, 92,  0,  2, 93, 14, 35, 10, 15, 98, 30, 53, 75,  9, 48, 83,
       73, 60, 67,  3, 32, 29, 72, 87, 17, 43, 66, 18, 40, 90, 46, 79, 39,
       13, 65, 51, 42, 38, 76, 95, 47, 91, 81, 49, 37,  4, 94, 52]), 'cur_cost': 162861.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([85, 69, 16, 21, 31, 70, 68,  6,  5, 54, 64, 26, 34, 33, 45,  7, 61,
       50, 96, 63,  8, 24, 59, 78, 56, 20, 12, 89, 23, 28, 11, 22, 71, 74,
       55, 57, 25, 88, 27, 97, 58,  1, 84, 36, 44, 99, 82, 41, 86, 62, 77,
       19, 80, 92,  0,  2, 93, 14, 35, 10, 15, 98, 30, 53, 75,  9, 48, 83,
       73, 60, 67,  3, 32, 29, 72, 87, 17, 43, 66, 18, 40, 90, 46, 79, 39,
       13, 65, 51, 42, 38, 76, 95, 47, 91, 81, 49, 37,  4, 94, 52]), 'cur_cost': 165050.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([85, 31, 69, 16, 21, 70, 68,  6,  5, 54, 64, 26, 34, 33, 45,  7, 61,
       50, 96, 63,  8, 24, 59, 78, 56, 20, 12, 89, 23, 28, 11, 22, 71, 74,
       55, 57, 25, 88, 27, 97, 58,  1, 84, 36, 44, 99, 82, 41, 86, 62, 77,
       19, 80, 92,  0,  2, 93, 14, 35, 10, 15, 98, 30, 53, 75,  9, 48, 83,
       73, 60, 67,  3, 32, 29, 72, 87, 17, 43, 66, 18, 40, 90, 46, 79, 39,
       13, 65, 51, 42, 38, 76, 95, 47, 91, 81, 49, 37,  4, 94, 52]), 'cur_cost': 164989.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:23,472 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 163126.00)
2025-08-05 09:52:23,472 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:23,472 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:23,472 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:23,479 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 09:52:23,480 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,480 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,481 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,481 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,481 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30484.0, 路径长度: 100, 收集中间解: 3
2025-08-05 09:52:23,481 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 19, 2, 10, 14, 16, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 28, 45, 42, 33, 82, 54, 11, 26, 85, 34, 61, 59, 76, 56, 6, 8, 86, 50], 'cur_cost': 30484.0, 'intermediate_solutions': [{'tour': [0, 19, 12, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 94, 81, 49, 43, 72, 67, 84, 38, 29, 33, 82, 54, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 7, 65, 64, 3, 96, 55, 79, 30, 88, 41, 69, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 60, 24, 80, 68, 63, 39, 53, 1, 50, 86, 8, 6, 56, 42], 'cur_cost': 36110.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 12, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 94, 81, 49, 43, 72, 67, 84, 38, 29, 33, 82, 54, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 96, 3, 64, 65, 69, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 60, 24, 80, 68, 63, 39, 53, 1, 50, 86, 8, 6, 56, 42], 'cur_cost': 33789.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 12, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 94, 81, 43, 72, 67, 84, 38, 29, 33, 82, 54, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 49, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 60, 24, 80, 68, 63, 39, 53, 1, 50, 86, 8, 6, 56, 42], 'cur_cost': 35798.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:23,482 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 30484.00)
2025-08-05 09:52:23,482 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:23,482 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:23,489 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([31, 63, 89, 72, 52, 60, 38, 16, 87,  8,  2, 88, 49, 11, 12, 84, 26,
       23, 54, 27, 13, 55, 37, 47,  3, 94, 65, 95, 48, 64, 24,  1, 96, 29,
       28, 41, 10, 99, 33,  7, 93, 20, 76, 77, 70, 78,  4, 46, 36, 61, 92,
       39,  6, 69, 35, 51, 68,  5, 42, 86, 75, 81, 18, 56, 14, 74,  9, 15,
       71, 59, 45, 53, 97, 85, 98, 82, 21, 80, 22, 83, 17, 57, 90, 91, 50,
       44, 40, 62, 25, 30, 43, 34, 79, 73, 58,  0, 67, 19, 66, 32],
      dtype=int64), 'cur_cost': 182553.0, 'intermediate_solutions': [{'tour': array([58, 97,  4,  8, 22, 68, 47, 50, 64, 89, 77, 46, 59, 48, 16, 27, 12,
       86, 83, 90,  3, 45, 26, 57, 43, 92, 70,  9, 55, 93, 56, 33, 95, 67,
       61, 19, 72, 98, 87, 30, 36, 82, 78, 28,  0, 75, 21,  2, 62,  7, 79,
       91, 52, 39, 23, 69, 40, 10, 42, 74, 13, 71, 15, 54, 60,  1,  5, 84,
        6, 24, 76, 96, 32, 63, 66, 44, 25, 99, 65, 80, 81, 31, 38, 88, 17,
       51, 85, 14, 29, 41, 53, 49, 18, 11, 73, 37, 35, 20, 94, 34]), 'cur_cost': 180305.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 8, 58, 97,  4, 22, 68, 47, 50, 64, 89, 77, 46, 59, 48, 16, 27, 12,
       86, 83, 90,  3, 45, 26, 57, 43, 92, 70,  9, 55, 93, 56, 33, 95, 67,
       61, 19, 72, 98, 87, 30, 36, 82, 78, 28,  0, 75, 21,  2, 62,  7, 79,
       91, 52, 39, 23, 69, 40, 10, 42, 74, 13, 71, 15, 54, 60,  1,  5, 84,
        6, 24, 76, 96, 32, 63, 66, 44, 25, 99, 65, 80, 81, 31, 38, 88, 17,
       51, 85, 14, 29, 41, 53, 49, 18, 11, 73, 37, 35, 20, 94, 34]), 'cur_cost': 181024.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([22,  8, 58, 97,  4, 68, 47, 50, 64, 89, 77, 46, 59, 48, 16, 27, 12,
       86, 83, 90,  3, 45, 26, 57, 43, 92, 70,  9, 55, 93, 56, 33, 95, 67,
       61, 19, 72, 98, 87, 30, 36, 82, 78, 28,  0, 75, 21,  2, 62,  7, 79,
       91, 52, 39, 23, 69, 40, 10, 42, 74, 13, 71, 15, 54, 60,  1,  5, 84,
        6, 24, 76, 96, 32, 63, 66, 44, 25, 99, 65, 80, 81, 31, 38, 88, 17,
       51, 85, 14, 29, 41, 53, 49, 18, 11, 73, 37, 35, 20, 94, 34]), 'cur_cost': 179865.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4,  8, 58, 97, 22, 68, 47, 50, 64, 89, 77, 46, 59, 48, 16, 27, 12,
       86, 83, 90,  3, 45, 26, 57, 43, 92, 70,  9, 55, 93, 56, 33, 95, 67,
       61, 19, 72, 98, 87, 30, 36, 82, 78, 28,  0, 75, 21,  2, 62,  7, 79,
       91, 52, 39, 23, 69, 40, 10, 42, 74, 13, 71, 15, 54, 60,  1,  5, 84,
        6, 24, 76, 96, 32, 63, 66, 44, 25, 99, 65, 80, 81, 31, 38, 88, 17,
       51, 85, 14, 29, 41, 53, 49, 18, 11, 73, 37, 35, 20, 94, 34]), 'cur_cost': 179124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4, 22,  8, 58, 97, 68, 47, 50, 64, 89, 77, 46, 59, 48, 16, 27, 12,
       86, 83, 90,  3, 45, 26, 57, 43, 92, 70,  9, 55, 93, 56, 33, 95, 67,
       61, 19, 72, 98, 87, 30, 36, 82, 78, 28,  0, 75, 21,  2, 62,  7, 79,
       91, 52, 39, 23, 69, 40, 10, 42, 74, 13, 71, 15, 54, 60,  1,  5, 84,
        6, 24, 76, 96, 32, 63, 66, 44, 25, 99, 65, 80, 81, 31, 38, 88, 17,
       51, 85, 14, 29, 41, 53, 49, 18, 11, 73, 37, 35, 20, 94, 34]), 'cur_cost': 180891.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [43, 38, 47, 95, 40, 32, 60, 24, 66, 97, 46, 73, 22, 33, 82, 8, 90, 20, 93, 88, 27, 67, 76, 45, 59, 63, 84, 19, 34, 85, 50, 53, 68, 51, 6, 16, 87, 25, 55, 41, 48, 17, 71, 10, 14, 89, 96, 31, 58, 18, 0, 35, 9, 21, 52, 91, 65, 64, 74, 83, 5, 56, 28, 72, 75, 94, 81, 57, 80, 86, 49, 2, 99, 13, 70, 26, 92, 39, 1, 7, 78, 23, 69, 44, 98, 61, 4, 12, 77, 54, 62, 37, 15, 30, 3, 79, 11, 42, 29, 36], 'cur_cost': 90809.0, 'intermediate_solutions': [{'tour': [0, 19, 16, 41, 43, 49, 72, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 63, 39, 53, 68, 80, 24, 60, 50, 86, 8, 6, 56, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 1, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 54, 82, 33, 28, 45, 2, 42, 13, 70, 40, 99, 47], 'cur_cost': 35301.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 16, 1, 43, 49, 72, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 63, 39, 53, 68, 80, 24, 60, 50, 86, 8, 5, 91, 7, 41, 88, 30, 79, 55, 96, 3, 64, 65, 69, 93, 21, 15, 87, 52, 78, 17, 23, 37, 35, 83, 9, 71, 58, 20, 73, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26, 11, 56, 6, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 54, 82, 33, 28, 45, 2, 42, 13, 70, 40, 99, 47], 'cur_cost': 35675.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 16, 1, 43, 49, 72, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 63, 39, 53, 68, 80, 24, 60, 50, 86, 8, 6, 56, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 64, 18, 74, 25, 98, 46, 92, 27, 66, 57, 54, 82, 33, 28, 45, 2, 42, 13, 70, 40, 99, 47], 'cur_cost': 33060.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 16, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 42, 61, 59, 76, 22, 97, 90, 44, 31, 14, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57], 'cur_cost': 29602.0, 'intermediate_solutions': [{'tour': [73, 19, 13, 70, 12, 42, 4, 39, 84, 2, 92, 56, 27, 86, 29, 6, 43, 47, 94, 26, 45, 68, 51, 95, 11, 66, 61, 97, 59, 91, 64, 96, 0, 9, 31, 90, 77, 17, 21, 69, 87, 23, 25, 18, 37, 55, 30, 65, 71, 16, 58, 78, 5, 22, 24, 85, 34, 57, 44, 80, 67, 36, 53, 63, 38, 99, 46, 50, 10, 52, 79, 89, 98, 88, 49, 54, 76, 14, 62, 7, 60, 82, 33, 28, 81, 32, 40, 1, 8, 72, 75, 74, 48, 20, 93, 15, 83, 35, 3, 41], 'cur_cost': 104291.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [73, 19, 13, 70, 12, 42, 4, 39, 84, 2, 92, 56, 27, 86, 29, 6, 43, 47, 94, 26, 45, 68, 51, 95, 11, 66, 61, 97, 59, 91, 64, 96, 0, 9, 31, 90, 41, 17, 21, 69, 87, 23, 25, 18, 37, 55, 30, 65, 22, 5, 78, 58, 16, 71, 24, 85, 34, 57, 44, 80, 67, 36, 53, 63, 38, 99, 46, 50, 10, 52, 79, 89, 98, 88, 49, 54, 76, 14, 62, 7, 60, 82, 33, 28, 81, 32, 40, 1, 8, 72, 75, 74, 48, 20, 93, 15, 83, 35, 3, 77], 'cur_cost': 106687.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [73, 19, 13, 70, 12, 42, 4, 39, 84, 2, 92, 56, 27, 86, 29, 6, 43, 47, 94, 26, 45, 68, 51, 95, 11, 66, 61, 97, 59, 91, 64, 96, 0, 9, 31, 90, 41, 17, 21, 69, 87, 23, 25, 18, 37, 55, 30, 65, 71, 16, 58, 78, 5, 22, 24, 85, 34, 57, 44, 80, 67, 36, 53, 38, 99, 46, 50, 10, 63, 52, 79, 89, 98, 88, 49, 54, 76, 14, 62, 7, 60, 82, 33, 28, 81, 32, 40, 1, 8, 72, 75, 74, 48, 20, 93, 15, 83, 35, 3, 77], 'cur_cost': 108191.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 3,  7, 64, 44,  4,  8, 94, 74, 96, 15, 55, 22, 84, 81, 18, 70, 73,
       29, 34, 89, 24, 48, 93, 14, 82, 79, 88, 71, 66, 12, 25, 59, 13, 49,
       87, 98, 57, 36, 69, 42, 28, 85, 91, 26, 21,  5, 86, 33, 77, 43, 58,
       46, 11, 37, 76, 31, 80, 35, 30, 65,  6, 10, 92, 19, 99, 78,  1,  0,
       16, 97, 50, 72, 61, 54,  9, 67, 38, 27, 17, 62, 41, 75, 90, 53, 32,
       83, 95, 51, 20, 56, 52, 68, 45, 47, 23, 60,  2, 40, 39, 63],
      dtype=int64), 'cur_cost': 171323.0, 'intermediate_solutions': [{'tour': array([88, 18, 99, 41, 50, 43, 77, 59, 58, 37, 20, 16, 13, 95, 33, 29, 44,
       87, 52, 12, 78, 55, 71, 24, 70, 66, 45, 98, 81, 17, 96, 54, 26, 15,
       46, 28, 93,  5, 22, 40,  4, 25, 34, 75, 97, 83, 36, 94, 80, 73,  0,
       90, 49, 57, 84, 74, 10, 60, 14,  3, 38, 89,  6, 85, 35, 30, 82, 56,
       86, 32, 76, 62, 63, 65, 21,  2, 51,  8, 91, 53, 72, 61, 48, 64, 42,
        1, 23, 47, 67, 19, 92,  9, 68, 69, 79, 39, 27, 11,  7, 31]), 'cur_cost': 171789.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([41, 88, 18, 99, 50, 43, 77, 59, 58, 37, 20, 16, 13, 95, 33, 29, 44,
       87, 52, 12, 78, 55, 71, 24, 70, 66, 45, 98, 81, 17, 96, 54, 26, 15,
       46, 28, 93,  5, 22, 40,  4, 25, 34, 75, 97, 83, 36, 94, 80, 73,  0,
       90, 49, 57, 84, 74, 10, 60, 14,  3, 38, 89,  6, 85, 35, 30, 82, 56,
       86, 32, 76, 62, 63, 65, 21,  2, 51,  8, 91, 53, 72, 61, 48, 64, 42,
        1, 23, 47, 67, 19, 92,  9, 68, 69, 79, 39, 27, 11,  7, 31]), 'cur_cost': 168952.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([50, 41, 88, 18, 99, 43, 77, 59, 58, 37, 20, 16, 13, 95, 33, 29, 44,
       87, 52, 12, 78, 55, 71, 24, 70, 66, 45, 98, 81, 17, 96, 54, 26, 15,
       46, 28, 93,  5, 22, 40,  4, 25, 34, 75, 97, 83, 36, 94, 80, 73,  0,
       90, 49, 57, 84, 74, 10, 60, 14,  3, 38, 89,  6, 85, 35, 30, 82, 56,
       86, 32, 76, 62, 63, 65, 21,  2, 51,  8, 91, 53, 72, 61, 48, 64, 42,
        1, 23, 47, 67, 19, 92,  9, 68, 69, 79, 39, 27, 11,  7, 31]), 'cur_cost': 169271.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([99, 41, 88, 18, 50, 43, 77, 59, 58, 37, 20, 16, 13, 95, 33, 29, 44,
       87, 52, 12, 78, 55, 71, 24, 70, 66, 45, 98, 81, 17, 96, 54, 26, 15,
       46, 28, 93,  5, 22, 40,  4, 25, 34, 75, 97, 83, 36, 94, 80, 73,  0,
       90, 49, 57, 84, 74, 10, 60, 14,  3, 38, 89,  6, 85, 35, 30, 82, 56,
       86, 32, 76, 62, 63, 65, 21,  2, 51,  8, 91, 53, 72, 61, 48, 64, 42,
        1, 23, 47, 67, 19, 92,  9, 68, 69, 79, 39, 27, 11,  7, 31]), 'cur_cost': 169923.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([99, 50, 41, 88, 18, 43, 77, 59, 58, 37, 20, 16, 13, 95, 33, 29, 44,
       87, 52, 12, 78, 55, 71, 24, 70, 66, 45, 98, 81, 17, 96, 54, 26, 15,
       46, 28, 93,  5, 22, 40,  4, 25, 34, 75, 97, 83, 36, 94, 80, 73,  0,
       90, 49, 57, 84, 74, 10, 60, 14,  3, 38, 89,  6, 85, 35, 30, 82, 56,
       86, 32, 76, 62, 63, 65, 21,  2, 51,  8, 91, 53, 72, 61, 48, 64, 42,
        1, 23, 47, 67, 19, 92,  9, 68, 69, 79, 39, 27, 11,  7, 31]), 'cur_cost': 169436.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 11, 6, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 26, 85, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 31217.0, 'intermediate_solutions': [{'tour': [73, 70, 39, 92, 43, 47, 26, 51, 11, 84, 29, 74, 91, 9, 85, 34, 17, 23, 44, 10, 65, 20, 5, 35, 37, 80, 63, 89, 46, 96, 32, 88, 41, 94, 28, 18, 98, 8, 76, 86, 19, 82, 52, 12, 93, 50, 75, 4, 72, 62, 22, 30, 67, 0, 81, 97, 6, 68, 13, 95, 21, 16, 59, 27, 45, 15, 83, 61, 7, 78, 64, 57, 69, 36, 25, 33, 58, 55, 87, 31, 56, 66, 1, 2, 14, 38, 3, 60, 42, 77, 71, 49, 40, 48, 53, 54, 99, 24, 79, 90], 'cur_cost': 163658.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [73, 70, 39, 92, 43, 47, 26, 51, 11, 84, 29, 74, 91, 9, 85, 34, 17, 23, 44, 10, 65, 20, 5, 35, 37, 80, 63, 38, 46, 96, 32, 88, 41, 94, 28, 18, 98, 36, 69, 57, 64, 78, 7, 61, 83, 15, 45, 27, 59, 16, 21, 95, 13, 68, 6, 97, 81, 0, 67, 30, 22, 62, 72, 4, 75, 50, 93, 12, 52, 82, 19, 86, 76, 8, 25, 33, 58, 55, 87, 31, 56, 66, 1, 2, 14, 89, 3, 60, 42, 77, 71, 49, 40, 48, 53, 54, 99, 24, 79, 90], 'cur_cost': 160850.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [73, 70, 39, 92, 43, 47, 26, 51, 84, 29, 74, 91, 9, 85, 34, 17, 23, 44, 10, 65, 20, 5, 35, 37, 80, 63, 38, 46, 96, 32, 88, 41, 94, 28, 18, 98, 8, 76, 86, 19, 82, 52, 12, 93, 50, 75, 4, 72, 62, 22, 30, 67, 0, 81, 97, 6, 68, 13, 95, 21, 16, 59, 27, 45, 15, 83, 61, 7, 78, 64, 57, 69, 36, 25, 33, 58, 55, 87, 31, 56, 66, 1, 11, 2, 14, 89, 3, 60, 42, 77, 71, 49, 40, 48, 53, 54, 99, 24, 79, 90], 'cur_cost': 159518.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 11, 12, 32, 75, 94, 81, 49, 43, 72, 67, 84, 38, 29, 95, 77, 51, 4, 36, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 6, 8, 56, 86, 50, 60, 24, 80, 68, 63, 39, 53, 1, 66, 27, 92, 57, 76, 59, 61, 85, 26, 34, 19, 22, 97, 90, 44, 31, 10, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 42], 'cur_cost': 33486.0, 'intermediate_solutions': [{'tour': [0, 13, 14, 10, 16, 31, 90, 97, 22, 44, 46, 62, 57, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 25, 98, 92, 27, 66, 5, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 36602.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 14, 10, 16, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 67, 63, 39, 53, 1, 43, 49, 72, 68, 80, 24, 60, 57, 66, 27, 92, 98, 25, 18, 74, 91, 7, 41, 88, 30, 79, 55, 96, 3, 64, 65, 69, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 38530.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 14, 10, 16, 31, 90, 97, 22, 44, 46, 79, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 30, 88, 41, 7, 91, 74, 18, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 34738.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 17, 5, 48, 62, 91, 7, 41, 88, 30, 79, 55, 96, 74, 18, 52, 78, 87, 15, 21, 93, 69, 65, 64, 3, 25, 89, 9, 83, 71, 20, 73, 58, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42, 35, 37, 23, 98], 'cur_cost': 34467.0, 'intermediate_solutions': [{'tour': [0, 3, 20, 16, 14, 10, 31, 90, 97, 92, 44, 46, 62, 5, 48, 89, 9, 83, 71, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 25, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 98, 22, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 31772.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 20, 16, 14, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 25, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 40, 70, 99, 47, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 30464.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 20, 16, 14, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 25, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 98, 92, 54, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61, 42], 'cur_cost': 32679.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 19, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 12, 94, 81, 49, 43, 72, 67, 84, 38, 29, 33, 82, 54, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 60, 24, 80, 68, 63, 39, 53, 1, 50, 86, 8, 6, 56, 42], 'cur_cost': 30591.0, 'intermediate_solutions': [{'tour': [74, 16, 7, 78, 65, 71, 57, 49, 75, 66, 67, 63, 82, 70, 61, 20, 5, 52, 91, 35, 64, 37, 18, 93, 48, 44, 17, 21, 69, 10, 31, 58, 26, 68, 76, 90, 56, 45, 47, 19, 50, 43, 27, 39, 60, 34, 59, 6, 42, 12, 72, 51, 28, 77, 13, 2, 86, 99, 4, 29, 32, 88, 30, 25, 87, 41, 89, 62, 73, 3, 22, 24, 11, 85, 8, 54, 36, 1, 92, 38, 14, 15, 46, 0, 23, 97, 98, 83, 9, 80, 33, 40, 81, 84, 53, 94, 95, 79, 55, 96], 'cur_cost': 102464.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [74, 16, 7, 78, 65, 71, 57, 49, 85, 11, 24, 22, 3, 73, 62, 89, 41, 87, 25, 30, 88, 32, 29, 4, 99, 86, 2, 13, 77, 28, 51, 72, 12, 42, 6, 38, 34, 60, 39, 27, 43, 50, 19, 47, 45, 56, 90, 76, 68, 26, 58, 31, 10, 69, 21, 17, 44, 48, 93, 18, 37, 64, 35, 91, 52, 5, 20, 61, 70, 82, 63, 67, 66, 75, 8, 54, 36, 1, 92, 59, 14, 15, 46, 0, 23, 97, 98, 83, 9, 80, 33, 40, 81, 84, 53, 94, 95, 79, 55, 96], 'cur_cost': 103210.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [74, 16, 7, 78, 65, 71, 57, 49, 75, 66, 67, 63, 82, 70, 61, 20, 5, 52, 91, 35, 64, 37, 18, 93, 48, 44, 17, 21, 69, 10, 31, 58, 26, 68, 76, 90, 56, 45, 47, 19, 50, 43, 96, 27, 39, 60, 34, 38, 6, 42, 12, 72, 51, 28, 77, 13, 2, 86, 99, 4, 29, 32, 88, 30, 25, 87, 41, 89, 62, 73, 3, 22, 24, 11, 85, 8, 54, 36, 1, 92, 59, 14, 15, 46, 0, 23, 97, 98, 83, 9, 80, 33, 40, 81, 84, 53, 94, 95, 79, 55], 'cur_cost': 103320.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([23, 45, 83, 31, 72, 42, 87, 80, 56, 66, 10, 41,  9, 44, 21, 98, 69,
        5, 64, 46, 14, 28, 84, 11, 52, 58, 38, 12, 76, 16, 34, 54, 19, 95,
       13, 29, 30, 60, 53,  8, 27, 43, 92, 59, 18, 86, 89, 62, 73,  3, 63,
        1, 22, 24, 70, 17,  6, 25, 65, 68, 50, 51, 57, 77, 37, 36, 48, 74,
       40, 99, 67, 79, 96, 39, 32, 82,  4,  0, 71, 15, 26, 49, 90, 91, 61,
       93, 85, 47,  2, 33, 20, 81, 35, 75, 78, 55, 97,  7, 94, 88],
      dtype=int64), 'cur_cost': 163126.0, 'intermediate_solutions': [{'tour': array([16, 21, 85, 69, 31, 70, 68,  6,  5, 54, 64, 26, 34, 33, 45,  7, 61,
       50, 96, 63,  8, 24, 59, 78, 56, 20, 12, 89, 23, 28, 11, 22, 71, 74,
       55, 57, 25, 88, 27, 97, 58,  1, 84, 36, 44, 99, 82, 41, 86, 62, 77,
       19, 80, 92,  0,  2, 93, 14, 35, 10, 15, 98, 30, 53, 75,  9, 48, 83,
       73, 60, 67,  3, 32, 29, 72, 87, 17, 43, 66, 18, 40, 90, 46, 79, 39,
       13, 65, 51, 42, 38, 76, 95, 47, 91, 81, 49, 37,  4, 94, 52]), 'cur_cost': 165121.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([69, 16, 21, 85, 31, 70, 68,  6,  5, 54, 64, 26, 34, 33, 45,  7, 61,
       50, 96, 63,  8, 24, 59, 78, 56, 20, 12, 89, 23, 28, 11, 22, 71, 74,
       55, 57, 25, 88, 27, 97, 58,  1, 84, 36, 44, 99, 82, 41, 86, 62, 77,
       19, 80, 92,  0,  2, 93, 14, 35, 10, 15, 98, 30, 53, 75,  9, 48, 83,
       73, 60, 67,  3, 32, 29, 72, 87, 17, 43, 66, 18, 40, 90, 46, 79, 39,
       13, 65, 51, 42, 38, 76, 95, 47, 91, 81, 49, 37,  4, 94, 52]), 'cur_cost': 162452.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([31, 69, 16, 21, 85, 70, 68,  6,  5, 54, 64, 26, 34, 33, 45,  7, 61,
       50, 96, 63,  8, 24, 59, 78, 56, 20, 12, 89, 23, 28, 11, 22, 71, 74,
       55, 57, 25, 88, 27, 97, 58,  1, 84, 36, 44, 99, 82, 41, 86, 62, 77,
       19, 80, 92,  0,  2, 93, 14, 35, 10, 15, 98, 30, 53, 75,  9, 48, 83,
       73, 60, 67,  3, 32, 29, 72, 87, 17, 43, 66, 18, 40, 90, 46, 79, 39,
       13, 65, 51, 42, 38, 76, 95, 47, 91, 81, 49, 37,  4, 94, 52]), 'cur_cost': 162861.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([85, 69, 16, 21, 31, 70, 68,  6,  5, 54, 64, 26, 34, 33, 45,  7, 61,
       50, 96, 63,  8, 24, 59, 78, 56, 20, 12, 89, 23, 28, 11, 22, 71, 74,
       55, 57, 25, 88, 27, 97, 58,  1, 84, 36, 44, 99, 82, 41, 86, 62, 77,
       19, 80, 92,  0,  2, 93, 14, 35, 10, 15, 98, 30, 53, 75,  9, 48, 83,
       73, 60, 67,  3, 32, 29, 72, 87, 17, 43, 66, 18, 40, 90, 46, 79, 39,
       13, 65, 51, 42, 38, 76, 95, 47, 91, 81, 49, 37,  4, 94, 52]), 'cur_cost': 165050.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([85, 31, 69, 16, 21, 70, 68,  6,  5, 54, 64, 26, 34, 33, 45,  7, 61,
       50, 96, 63,  8, 24, 59, 78, 56, 20, 12, 89, 23, 28, 11, 22, 71, 74,
       55, 57, 25, 88, 27, 97, 58,  1, 84, 36, 44, 99, 82, 41, 86, 62, 77,
       19, 80, 92,  0,  2, 93, 14, 35, 10, 15, 98, 30, 53, 75,  9, 48, 83,
       73, 60, 67,  3, 32, 29, 72, 87, 17, 43, 66, 18, 40, 90, 46, 79, 39,
       13, 65, 51, 42, 38, 76, 95, 47, 91, 81, 49, 37,  4, 94, 52]), 'cur_cost': 164989.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 2, 10, 14, 16, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 28, 45, 42, 33, 82, 54, 11, 26, 85, 34, 61, 59, 76, 56, 6, 8, 86, 50], 'cur_cost': 30484.0, 'intermediate_solutions': [{'tour': [0, 19, 12, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 94, 81, 49, 43, 72, 67, 84, 38, 29, 33, 82, 54, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 7, 65, 64, 3, 96, 55, 79, 30, 88, 41, 69, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 60, 24, 80, 68, 63, 39, 53, 1, 50, 86, 8, 6, 56, 42], 'cur_cost': 36110.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 12, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 94, 81, 49, 43, 72, 67, 84, 38, 29, 33, 82, 54, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 96, 3, 64, 65, 69, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 60, 24, 80, 68, 63, 39, 53, 1, 50, 86, 8, 6, 56, 42], 'cur_cost': 33789.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 12, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 94, 81, 43, 72, 67, 84, 38, 29, 33, 82, 54, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 49, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 60, 24, 80, 68, 63, 39, 53, 1, 50, 86, 8, 6, 56, 42], 'cur_cost': 35798.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:23,490 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:23,490 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:23,499 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=29602.000, 多样性=0.982
2025-08-05 09:52:23,499 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 09:52:23,500 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 09:52:23,500 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:23,502 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.0726713502332704, 'best_improvement': 0.016316086797594123}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.002951861943687383}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.0037362852511010672, 'recent_improvements': [-0.007385528749130407, -0.05255189068448968, 8.704175307173757e-05], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 7, 'new_count': 7, 'count_change': 0, 'old_best_cost': 21389, 'new_best_cost': 21389, 'quality_improvement': 0.0, 'old_diversity': 0.919047619047619, 'new_diversity': 0.919047619047619, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:52:23,503 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 09:52:23,510 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\kroA100_solution.json
2025-08-05 09:52:23,510 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\kroA100_20250805_095223.solution
2025-08-05 09:52:23,510 - __main__ - INFO - 实例执行完成 - 运行时间: 2.37s, 最佳成本: 21389
2025-08-05 09:52:23,510 - __main__ - INFO - 实例 kroA100 处理完成
