# -*- coding: utf-8 -*-
"""
策略分配解析和验证模块

负责解析LLM响应的策略分配结果，验证分配的有效性，并提供错误处理和修复机制。
"""

import json
import re
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum


class StrategyType(Enum):
    """策略类型枚举"""
    STRONG_EXPLORATION = "strong_exploration"
    BALANCED_EXPLORATION = "balanced_exploration"
    INTELLIGENT_EXPLORATION = "intelligent_exploration"
    CAUTIOUS_EXPLOITATION = "cautious_exploitation"
    MODERATE_EXPLOITATION = "moderate_exploitation"
    AGGRESSIVE_EXPLOITATION = "aggressive_exploitation"
    INTENSIVE_EXPLOITATION = "intensive_exploitation"
    ADAPTIVE_HYBRID = "adaptive_hybrid"
    COLLABORATIVE_ESCAPE = "collaborative_escape"


class ValidationError(Exception):
    """验证错误异常"""
    pass


@dataclass
class ParsedStrategyAssignment:
    """解析后的策略分配"""
    individual_id: int
    strategy_type: StrategyType
    confidence: float
    reasoning: str
    priority: float = 0.5
    expected_improvement: float = 0.0
    parameters: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.parameters is None:
            self.parameters = {}


@dataclass
class ParsedGlobalAnalysis:
    """解析后的全局分析"""
    exploration_ratio: float
    exploitation_ratio: float
    key_insights: str
    risk_assessment: str
    coordination_strategy: str = ""
    resource_allocation: str = ""


@dataclass
class ParsedStrategyResponse:
    """解析后的完整策略响应"""
    strategy_assignments: List[ParsedStrategyAssignment]
    global_analysis: ParsedGlobalAnalysis
    parsing_errors: List[str]
    validation_warnings: List[str]


class StrategyResponseParser:
    """
    策略响应解析器
    
    负责解析LLM返回的策略分配响应，验证其有效性，并提供错误修复机制。
    """
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 配置参数
        self.strict_validation = self.config.get('strict_validation', False)
        self.auto_repair = self.config.get('auto_repair', True)
        self.default_confidence = self.config.get('default_confidence', 0.5)
        self.default_priority = self.config.get('default_priority', 0.5)
        
        # 策略映射（处理可能的变体）
        self.strategy_mapping = {
            'strong_exploration': StrategyType.STRONG_EXPLORATION,
            'strongexploration': StrategyType.STRONG_EXPLORATION,
            'strong_explore': StrategyType.STRONG_EXPLORATION,
            'balanced_exploration': StrategyType.BALANCED_EXPLORATION,
            'balancedexploration': StrategyType.BALANCED_EXPLORATION,
            'balanced_explore': StrategyType.BALANCED_EXPLORATION,
            'intelligent_exploration': StrategyType.INTELLIGENT_EXPLORATION,
            'intelligentexploration': StrategyType.INTELLIGENT_EXPLORATION,
            'intelligent_explore': StrategyType.INTELLIGENT_EXPLORATION,
            'cautious_exploitation': StrategyType.CAUTIOUS_EXPLOITATION,
            'cautiousexploitation': StrategyType.CAUTIOUS_EXPLOITATION,
            'cautious_exploit': StrategyType.CAUTIOUS_EXPLOITATION,
            'moderate_exploitation': StrategyType.MODERATE_EXPLOITATION,
            'moderateexploitation': StrategyType.MODERATE_EXPLOITATION,
            'moderate_exploit': StrategyType.MODERATE_EXPLOITATION,
            'aggressive_exploitation': StrategyType.AGGRESSIVE_EXPLOITATION,
            'aggressiveexploitation': StrategyType.AGGRESSIVE_EXPLOITATION,
            'aggressive_exploit': StrategyType.AGGRESSIVE_EXPLOITATION,
            'intensive_exploitation': StrategyType.INTENSIVE_EXPLOITATION,
            'intensiveexploitation': StrategyType.INTENSIVE_EXPLOITATION,
            'intensive_exploit': StrategyType.INTENSIVE_EXPLOITATION,
            'adaptive_hybrid': StrategyType.ADAPTIVE_HYBRID,
            'adaptivehybrid': StrategyType.ADAPTIVE_HYBRID,
            'hybrid': StrategyType.ADAPTIVE_HYBRID,
            'collaborative_escape': StrategyType.COLLABORATIVE_ESCAPE,
            'collaborativeescape': StrategyType.COLLABORATIVE_ESCAPE,
            'escape': StrategyType.COLLABORATIVE_ESCAPE
        }
    
    def parse_strategy_response(self, response: str, population_size: int) -> ParsedStrategyResponse:
        """
        解析策略响应
        
        Args:
            response: LLM响应文本
            population_size: 种群大小
            
        Returns:
            解析后的策略响应对象
        """
        self.logger.info(f"开始解析策略响应，种群大小: {population_size}")
        
        parsing_errors = []
        validation_warnings = []
        
        try:
            # 1. 提取JSON内容
            json_data = self._extract_json_from_response(response)
            
            # 2. 解析策略分配
            strategy_assignments, assignment_errors = self._parse_strategy_assignments(
                json_data, population_size
            )
            parsing_errors.extend(assignment_errors)
            
            # 3. 解析全局分析
            global_analysis, analysis_errors = self._parse_global_analysis(json_data)
            parsing_errors.extend(analysis_errors)
            
            # 4. 验证分配结果
            validation_warnings.extend(self._validate_strategy_assignments(strategy_assignments, population_size))
            
            # 5. 自动修复（如果启用）
            if self.auto_repair and parsing_errors:
                strategy_assignments = self._repair_strategy_assignments(
                    strategy_assignments, population_size
                )
                validation_warnings.append("已自动修复部分策略分配错误")
            
            parsed_response = ParsedStrategyResponse(
                strategy_assignments=strategy_assignments,
                global_analysis=global_analysis,
                parsing_errors=parsing_errors,
                validation_warnings=validation_warnings
            )
            
            self.logger.info(f"策略响应解析完成，成功解析 {len(strategy_assignments)} 个策略分配")
            return parsed_response
            
        except Exception as e:
            self.logger.error(f"策略响应解析失败: {str(e)}")
            # 返回默认响应
            return self._create_fallback_response(population_size, str(e))
    
    def _extract_json_from_response(self, response: str) -> Dict[str, Any]:
        """从响应中提取JSON数据"""
        try:
            # 尝试多种JSON提取方法
            
            # 方法1: 直接JSON解析
            try:
                return json.loads(response.strip())
            except json.JSONDecodeError:
                pass
            
            # 方法2: 正则表达式提取
            json_patterns = [
                r'\{.*\}',  # 基本花括号匹配
                r'```json\s*(\{.*?\})\s*```',  # Markdown代码块
                r'```\s*(\{.*?\})\s*```',  # 通用代码块
            ]
            
            for pattern in json_patterns:
                matches = re.findall(pattern, response, re.DOTALL | re.IGNORECASE)
                for match in matches:
                    try:
                        if isinstance(match, tuple):
                            match = match[0] if match else ""
                        return json.loads(match.strip())
                    except json.JSONDecodeError:
                        continue
            
            # 方法3: 查找最大的有效JSON片段
            start_idx = response.find('{')
            if start_idx != -1:
                brace_count = 0
                for i, char in enumerate(response[start_idx:], start_idx):
                    if char == '{':
                        brace_count += 1
                    elif char == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            json_str = response[start_idx:i+1]
                            try:
                                return json.loads(json_str)
                            except json.JSONDecodeError:
                                break
            
            raise ValueError("无法从响应中提取有效的JSON数据")
            
        except Exception as e:
            raise ValueError(f"JSON提取失败: {str(e)}")
    
    def _parse_strategy_assignments(self, json_data: Dict[str, Any], 
                                  population_size: int) -> Tuple[List[ParsedStrategyAssignment], List[str]]:
        """解析策略分配"""
        errors = []
        assignments = []
        
        try:
            assignments_data = json_data.get('strategy_assignments', [])
            
            if not isinstance(assignments_data, list):
                errors.append("strategy_assignments字段不是列表类型")
                return [], errors
            
            for i, assignment_data in enumerate(assignments_data):
                try:
                    assignment = self._parse_single_assignment(assignment_data, i)
                    assignments.append(assignment)
                except Exception as e:
                    errors.append(f"解析第{i}个策略分配失败: {str(e)}")
            
            return assignments, errors
            
        except Exception as e:
            errors.append(f"解析策略分配列表失败: {str(e)}")
            return [], errors
    
    def _parse_single_assignment(self, assignment_data: Dict[str, Any], index: int) -> ParsedStrategyAssignment:
        """解析单个策略分配"""
        
        # 提取个体ID
        individual_id = assignment_data.get('individual_id')
        if individual_id is None:
            individual_id = index  # 使用索引作为默认ID
        
        # 提取策略类型
        strategy_type_str = assignment_data.get('strategy_type', '').lower().replace(' ', '_')
        strategy_type = self.strategy_mapping.get(strategy_type_str)
        
        if strategy_type is None:
            # 尝试模糊匹配
            for key, value in self.strategy_mapping.items():
                if key in strategy_type_str or strategy_type_str in key:
                    strategy_type = value
                    break
            
            if strategy_type is None:
                strategy_type = StrategyType.BALANCED_EXPLORATION  # 默认策略
        
        # 提取其他字段
        confidence = self._safe_float_extract(assignment_data.get('confidence'), self.default_confidence, 0.1, 1.0)
        priority = self._safe_float_extract(assignment_data.get('priority'), self.default_priority, 0.1, 1.0)
        expected_improvement = self._safe_float_extract(assignment_data.get('expected_improvement'), 0.0, 0.0, 1.0)
        
        reasoning = assignment_data.get('reasoning', '未提供推理')
        parameters = assignment_data.get('parameters', {})
        
        if not isinstance(parameters, dict):
            parameters = {}
        
        return ParsedStrategyAssignment(
            individual_id=individual_id,
            strategy_type=strategy_type,
            confidence=confidence,
            reasoning=reasoning,
            priority=priority,
            expected_improvement=expected_improvement,
            parameters=parameters
        )
    
    def _parse_global_analysis(self, json_data: Dict[str, Any]) -> Tuple[ParsedGlobalAnalysis, List[str]]:
        """解析全局分析"""
        errors = []
        
        try:
            analysis_data = json_data.get('global_analysis', {})
            
            if not isinstance(analysis_data, dict):
                errors.append("global_analysis字段不是字典类型")
                analysis_data = {}
            
            exploration_ratio = self._safe_float_extract(analysis_data.get('exploration_ratio'), 0.5, 0.0, 1.0)
            exploitation_ratio = self._safe_float_extract(analysis_data.get('exploitation_ratio'), 0.5, 0.0, 1.0)
            
            # 确保比例和为1
            total_ratio = exploration_ratio + exploitation_ratio
            if total_ratio > 0:
                exploration_ratio /= total_ratio
                exploitation_ratio /= total_ratio
            
            global_analysis = ParsedGlobalAnalysis(
                exploration_ratio=exploration_ratio,
                exploitation_ratio=exploitation_ratio,
                key_insights=analysis_data.get('key_insights', '无关键洞察'),
                risk_assessment=analysis_data.get('risk_assessment', '无风险评估'),
                coordination_strategy=analysis_data.get('coordination_strategy', ''),
                resource_allocation=analysis_data.get('resource_allocation', '')
            )
            
            return global_analysis, errors
            
        except Exception as e:
            errors.append(f"解析全局分析失败: {str(e)}")
            return ParsedGlobalAnalysis(
                exploration_ratio=0.5,
                exploitation_ratio=0.5,
                key_insights='解析失败',
                risk_assessment='无法评估'
            ), errors
    
    def _safe_float_extract(self, value: Any, default: float, min_val: float, max_val: float) -> float:
        """安全提取浮点数值"""
        try:
            if value is None:
                return default
            
            float_val = float(value)
            return max(min_val, min(max_val, float_val))
            
        except (ValueError, TypeError):
            return default

    def _validate_strategy_assignments(self, assignments: List[ParsedStrategyAssignment],
                                     population_size: int) -> List[str]:
        """验证策略分配"""
        warnings = []

        # 检查分配数量
        if len(assignments) != population_size:
            warnings.append(f"策略分配数量({len(assignments)})与种群大小({population_size})不匹配")

        # 检查个体ID连续性
        assigned_ids = {assignment.individual_id for assignment in assignments}
        expected_ids = set(range(population_size))

        missing_ids = expected_ids - assigned_ids
        if missing_ids:
            warnings.append(f"缺少个体ID的策略分配: {sorted(missing_ids)}")

        duplicate_ids = []
        seen_ids = set()
        for assignment in assignments:
            if assignment.individual_id in seen_ids:
                duplicate_ids.append(assignment.individual_id)
            seen_ids.add(assignment.individual_id)

        if duplicate_ids:
            warnings.append(f"重复的个体ID: {sorted(set(duplicate_ids))}")

        # 检查策略分布合理性
        strategy_counts = {}
        for assignment in assignments:
            strategy_type = assignment.strategy_type.value
            strategy_counts[strategy_type] = strategy_counts.get(strategy_type, 0) + 1

        # 检查是否所有个体都分配了相同策略
        if len(strategy_counts) == 1:
            warnings.append("所有个体都分配了相同策略，可能缺乏多样性")

        # 检查探索/开发平衡
        exploration_strategies = ['strong_exploration', 'balanced_exploration', 'intelligent_exploration']
        exploitation_strategies = ['cautious_exploitation', 'moderate_exploitation', 'aggressive_exploitation', 'intensive_exploitation']

        exploration_count = sum(strategy_counts.get(s, 0) for s in exploration_strategies)
        exploitation_count = sum(strategy_counts.get(s, 0) for s in exploitation_strategies)

        if exploration_count == 0:
            warnings.append("没有分配探索策略，可能导致搜索停滞")
        elif exploitation_count == 0:
            warnings.append("没有分配开发策略，可能错失优化机会")

        return warnings

    def _repair_strategy_assignments(self, assignments: List[ParsedStrategyAssignment],
                                   population_size: int) -> List[ParsedStrategyAssignment]:
        """修复策略分配"""
        self.logger.info("开始自动修复策略分配")

        # 创建ID到分配的映射
        id_to_assignment = {assignment.individual_id: assignment for assignment in assignments}

        # 修复缺失的个体ID
        for individual_id in range(population_size):
            if individual_id not in id_to_assignment:
                # 创建默认分配
                default_assignment = ParsedStrategyAssignment(
                    individual_id=individual_id,
                    strategy_type=StrategyType.BALANCED_EXPLORATION,
                    confidence=self.default_confidence,
                    reasoning="自动修复：使用默认策略",
                    priority=self.default_priority
                )
                id_to_assignment[individual_id] = default_assignment

        # 移除超出范围的个体ID
        valid_assignments = []
        for assignment in id_to_assignment.values():
            if 0 <= assignment.individual_id < population_size:
                valid_assignments.append(assignment)

        # 按个体ID排序
        valid_assignments.sort(key=lambda x: x.individual_id)

        # 确保策略分布合理
        valid_assignments = self._ensure_strategy_balance(valid_assignments)

        self.logger.info(f"策略分配修复完成，修复后数量: {len(valid_assignments)}")
        return valid_assignments

    def _ensure_strategy_balance(self, assignments: List[ParsedStrategyAssignment]) -> List[ParsedStrategyAssignment]:
        """确保策略分布平衡"""
        if len(assignments) < 2:
            return assignments

        # 统计当前策略分布
        strategy_counts = {}
        for assignment in assignments:
            strategy_type = assignment.strategy_type.value
            strategy_counts[strategy_type] = strategy_counts.get(strategy_type, 0) + 1

        # 如果所有个体都是同一策略，进行调整
        if len(strategy_counts) == 1:
            strategy_types = list(StrategyType)
            for i, assignment in enumerate(assignments):
                if i % 3 == 0:
                    assignment.strategy_type = StrategyType.BALANCED_EXPLORATION
                elif i % 3 == 1:
                    assignment.strategy_type = StrategyType.MODERATE_EXPLOITATION
                else:
                    assignment.strategy_type = StrategyType.INTELLIGENT_EXPLORATION

                assignment.reasoning += " (已调整以确保策略多样性)"

        return assignments

    def _create_fallback_response(self, population_size: int, error_message: str) -> ParsedStrategyResponse:
        """创建回退响应"""
        self.logger.warning(f"创建回退策略响应: {error_message}")

        # 创建简单的策略分配
        assignments = []
        for i in range(population_size):
            if i % 3 == 0:
                strategy_type = StrategyType.BALANCED_EXPLORATION
            elif i % 3 == 1:
                strategy_type = StrategyType.MODERATE_EXPLOITATION
            else:
                strategy_type = StrategyType.INTELLIGENT_EXPLORATION

            assignment = ParsedStrategyAssignment(
                individual_id=i,
                strategy_type=strategy_type,
                confidence=self.default_confidence,
                reasoning=f"回退策略分配 (原因: {error_message})",
                priority=self.default_priority
            )
            assignments.append(assignment)

        global_analysis = ParsedGlobalAnalysis(
            exploration_ratio=0.6,
            exploitation_ratio=0.4,
            key_insights=f"使用回退策略分配，原因: {error_message}",
            risk_assessment="中等风险：使用默认策略分配"
        )

        return ParsedStrategyResponse(
            strategy_assignments=assignments,
            global_analysis=global_analysis,
            parsing_errors=[error_message],
            validation_warnings=["使用回退策略分配"]
        )

    def convert_to_legacy_format(self, parsed_response: ParsedStrategyResponse) -> Tuple[List[str], str]:
        """转换为旧版格式以保持兼容性"""

        # 转换策略分配为字符串列表
        strategy_list = []
        for assignment in parsed_response.strategy_assignments:
            # 将策略类型转换为简化格式
            strategy_type = assignment.strategy_type.value
            if 'exploration' in strategy_type:
                legacy_strategy = 'explore'
            elif 'exploitation' in strategy_type:
                legacy_strategy = 'exploit'
            else:
                legacy_strategy = 'explore'  # 默认

            strategy_list.append(legacy_strategy)

        # 生成详细报告
        report = f"""
=== 策略分配报告 ===

【解析状态】
- 解析错误: {len(parsed_response.parsing_errors)}
- 验证警告: {len(parsed_response.validation_warnings)}

【策略分布】"""

        strategy_counts = {}
        for assignment in parsed_response.strategy_assignments:
            strategy_type = assignment.strategy_type.value
            strategy_counts[strategy_type] = strategy_counts.get(strategy_type, 0) + 1

        for strategy_type, count in sorted(strategy_counts.items()):
            percentage = (count / len(parsed_response.strategy_assignments)) * 100
            report += f"\n- {strategy_type}: {count} ({percentage:.1f}%)"

        report += f"""

【全局分析】
- 探索比例: {parsed_response.global_analysis.exploration_ratio:.2f}
- 开发比例: {parsed_response.global_analysis.exploitation_ratio:.2f}
- 关键洞察: {parsed_response.global_analysis.key_insights}
- 风险评估: {parsed_response.global_analysis.risk_assessment}
"""

        if parsed_response.parsing_errors:
            report += f"\n【解析错误】\n" + "\n".join(f"- {error}" for error in parsed_response.parsing_errors)

        if parsed_response.validation_warnings:
            report += f"\n【验证警告】\n" + "\n".join(f"- {warning}" for warning in parsed_response.validation_warnings)

        return strategy_list, report
