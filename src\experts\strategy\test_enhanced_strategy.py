# -*- coding: utf-8 -*-
"""
增强策略专家测试模块

测试增强策略专家的各个组件，包括个体状态分析、策略选择、LLM交互等。
"""

import unittest
import logging
import json
import sys
import os
from typing import Dict, List, Any
from unittest.mock import Mock, patch

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from experts.strategy.enhanced_strategy_expert import EnhancedStrategyExpert, StrategyType, StagnationLevel
from experts.analysis.individual_state_analyzer import IndividualStateAnalyzer
from experts.strategy.strategy_parser import StrategyResponseParser
from experts.prompts.enhanced_strategy_prompts import generate_individual_strategy_prompt


class TestEnhancedStrategyExpert(unittest.TestCase):
    """增强策略专家测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.config = {
            'enable_llm_reasoning': True,
            'fallback_to_algorithmic': True,
            'max_llm_retries': 2,
            'state_analyzer': {
                'history_window': 10,
                'improvement_threshold': 1e-6
            },
            'response_parser': {
                'strict_validation': False,
                'auto_repair': True
            }
        }
        
        # 创建模拟的LLM接口
        self.mock_llm = Mock()
        
        # 创建增强策略专家实例
        self.expert = EnhancedStrategyExpert(self.config)
        self.expert.interface_llm = self.mock_llm
        
        # 创建测试数据
        self.test_populations = self._create_test_populations()
        self.test_landscape_report = self._create_test_landscape_report()
    
    def _create_test_populations(self) -> List[Dict]:
        """创建测试种群数据"""
        populations = []
        
        # 创建5个个体的测试种群
        fitness_values = [100.5, 120.3, 95.8, 110.2, 105.7]
        tours = [
            [0, 1, 2, 3, 4],
            [0, 2, 1, 4, 3],
            [0, 3, 4, 1, 2],
            [0, 4, 3, 2, 1],
            [0, 1, 4, 2, 3]
        ]
        
        for i, (fitness, tour) in enumerate(zip(fitness_values, tours)):
            population = {
                'cur_cost': fitness,
                'tour': tour,
                'individual_id': i
            }
            populations.append(population)
        
        return populations
    
    def _create_test_landscape_report(self) -> Dict[str, Any]:
        """创建测试景观报告"""
        return {
            'global_ruggedness': 0.65,
            'modality': 'multi_modal',
            'deceptiveness': 'medium',
            'gradient_strength': 0.72,
            'population_diversity': 0.45,
            'convergence_trend': 0.38,
            'evolution_phase': 'exploration',
            'difficult_regions': [{'center': [1, 2], 'radius': 0.1}],
            'opportunity_regions': [{'center': [3, 4], 'radius': 0.15}],
            'local_optima_count': 8,
            'search_space_coverage': 0.25
        }
    
    def test_individual_state_analysis(self):
        """测试个体状态分析"""
        # 测试状态分析器
        analyzer = IndividualStateAnalyzer(self.config.get('state_analyzer', {}))
        
        # 分析种群状态
        metrics = analyzer.analyze_population_states(self.test_populations, iteration=10)
        
        # 验证结果
        self.assertEqual(len(metrics), len(self.test_populations))
        
        for i, metric in enumerate(metrics):
            self.assertEqual(metric.individual_id, i)
            self.assertIsInstance(metric.fitness_value, float)
            self.assertIsInstance(metric.fitness_rank, int)
            self.assertIsInstance(metric.fitness_percentile, float)
            self.assertIn(metric.stagnation_level, list(StagnationLevel))
    
    def test_strategy_prompt_generation(self):
        """测试策略提示生成"""
        # 创建个体特征数据
        individual_features = [
            {
                'individual_id': 0,
                'fitness_value': 95.8,
                'fitness_rank': 0,
                'fitness_percentile': 0.0,
                'stagnation_duration': 0,
                'stagnation_level': 'none',
                'diversity_contribution': 0.8,
                'distance_to_best': 0.0,
                'recent_improvements': 2,
                'preferred_strategies': ['moderate_exploitation']
            },
            {
                'individual_id': 1,
                'fitness_value': 120.3,
                'fitness_rank': 4,
                'fitness_percentile': 0.8,
                'stagnation_duration': 5,
                'stagnation_level': 'moderate',
                'diversity_contribution': 0.3,
                'distance_to_best': 24.5,
                'recent_improvements': 0,
                'preferred_strategies': []
            }
        ]
        
        landscape_context = {
            'global_ruggedness': 0.65,
            'modality': 'multi_modal',
            'deceptiveness': 'medium',
            'gradient_strength': 0.72,
            'population_diversity': 0.45,
            'convergence_trend': 0.38,
            'evolution_phase': 'exploration',
            'difficult_regions': [],
            'opportunity_regions': []
        }
        
        # 生成提示
        prompt = generate_individual_strategy_prompt(
            individual_features, landscape_context, iteration=10
        )
        
        # 验证提示内容
        self.assertIsInstance(prompt, str)
        self.assertIn('TSP优化策略选择专家', prompt)
        self.assertIn('个体 0', prompt)
        self.assertIn('个体 1', prompt)
        self.assertIn('适应度: 95.8', prompt)
        self.assertIn('停滞状态: 5代', prompt)
    
    def test_strategy_response_parsing(self):
        """测试策略响应解析"""
        parser = StrategyResponseParser(self.config.get('response_parser', {}))
        
        # 创建模拟LLM响应
        mock_response = """
        {
          "strategy_assignments": [
            {
              "individual_id": 0,
              "strategy_type": "moderate_exploitation",
              "confidence": 0.85,
              "reasoning": "顶级个体，适合开发策略",
              "priority": 0.9,
              "expected_improvement": 0.15,
              "parameters": {
                "local_search_depth": 3,
                "elite_influence": 0.8
              }
            },
            {
              "individual_id": 1,
              "strategy_type": "balanced_exploration",
              "confidence": 0.72,
              "reasoning": "中等个体存在停滞，需要探索",
              "priority": 0.6,
              "expected_improvement": 0.08,
              "parameters": {
                "exploration_radius": 0.2,
                "diversification_strength": 0.6
              }
            }
          ],
          "global_analysis": {
            "exploration_ratio": 0.5,
            "exploitation_ratio": 0.5,
            "key_insights": "种群状态平衡，采用混合策略",
            "risk_assessment": "低风险，策略分配合理"
          }
        }
        """
        
        # 解析响应
        parsed_response = parser.parse_strategy_response(mock_response, population_size=2)
        
        # 验证解析结果
        self.assertEqual(len(parsed_response.strategy_assignments), 2)
        self.assertEqual(len(parsed_response.parsing_errors), 0)
        
        # 验证第一个分配
        assignment1 = parsed_response.strategy_assignments[0]
        self.assertEqual(assignment1.individual_id, 0)
        self.assertEqual(assignment1.strategy_type.value, 'moderate_exploitation')
        self.assertEqual(assignment1.confidence, 0.85)
        
        # 验证全局分析
        global_analysis = parsed_response.global_analysis
        self.assertEqual(global_analysis.exploration_ratio, 0.5)
        self.assertEqual(global_analysis.exploitation_ratio, 0.5)
    
    def test_algorithmic_strategy_selection(self):
        """测试算法策略选择"""
        # 分析个体状态
        metrics = self.expert.state_analyzer.analyze_population_states(
            self.test_populations, iteration=5
        )
        
        # 转换为特征格式
        individual_features = self.expert._convert_metrics_to_features(metrics)
        
        # 构建景观上下文
        landscape_context = self.expert._build_landscape_context(self.test_landscape_report)
        
        # 执行算法策略选择
        strategy_assignments = self.expert._algorithmic_strategy_selection(
            individual_features, landscape_context, iteration=5
        )
        
        # 验证结果
        self.assertEqual(len(strategy_assignments), len(self.test_populations))
        
        for assignment in strategy_assignments:
            self.assertIsInstance(assignment.strategy_type, StrategyType)
            self.assertGreaterEqual(assignment.confidence, 0.1)
            self.assertLessEqual(assignment.confidence, 1.0)
            self.assertIsInstance(assignment.reasoning, str)
            self.assertGreater(len(assignment.reasoning), 0)
    
    def test_llm_strategy_selection_success(self):
        """测试LLM策略选择成功场景"""
        # 设置模拟LLM响应
        mock_llm_response = """
        {
          "strategy_assignments": [
            {
              "individual_id": 0,
              "strategy_type": "aggressive_exploitation",
              "confidence": 0.9,
              "reasoning": "最优个体，深度开发",
              "priority": 1.0,
              "expected_improvement": 0.2
            },
            {
              "individual_id": 1,
              "strategy_type": "strong_exploration",
              "confidence": 0.8,
              "reasoning": "停滞个体，强探索",
              "priority": 0.7,
              "expected_improvement": 0.1
            }
          ],
          "global_analysis": {
            "exploration_ratio": 0.4,
            "exploitation_ratio": 0.6,
            "key_insights": "偏向开发的策略分配",
            "risk_assessment": "中等风险"
          }
        }
        """
        
        self.mock_llm.get_response.return_value = mock_llm_response
        
        # 执行分析
        strategy_list, detailed_report = self.expert.analyze(
            populations=self.test_populations,
            landscape_report=self.test_landscape_report,
            iteration=8
        )
        
        # 验证结果
        self.assertIsInstance(strategy_list, list)
        self.assertEqual(len(strategy_list), len(self.test_populations))
        self.assertIsInstance(detailed_report, str)
        self.assertIn('策略分配报告', detailed_report)
    
    def test_llm_strategy_selection_fallback(self):
        """测试LLM策略选择失败回退场景"""
        # 设置LLM调用失败
        self.mock_llm.get_response.side_effect = Exception("LLM调用失败")
        
        # 执行分析
        strategy_list, detailed_report = self.expert.analyze(
            populations=self.test_populations,
            landscape_report=self.test_landscape_report,
            iteration=8
        )
        
        # 验证回退到算法方法
        self.assertIsInstance(strategy_list, list)
        self.assertEqual(len(strategy_list), len(self.test_populations))
        self.assertIsInstance(detailed_report, str)
        self.assertIn('算法策略选择', detailed_report)
    
    def test_invalid_llm_response_parsing(self):
        """测试无效LLM响应解析"""
        parser = StrategyResponseParser()
        
        # 测试无效JSON
        invalid_response = "这不是有效的JSON响应"
        parsed_response = parser.parse_strategy_response(invalid_response, population_size=2)
        
        # 验证回退响应
        self.assertEqual(len(parsed_response.strategy_assignments), 2)
        self.assertGreater(len(parsed_response.parsing_errors), 0)
        self.assertIn('回退策略分配', parsed_response.strategy_assignments[0].reasoning)
    
    def test_strategy_distribution_balance(self):
        """测试策略分布平衡"""
        # 创建大种群测试
        large_populations = []
        for i in range(20):
            population = {
                'cur_cost': 100 + i * 2,
                'tour': list(range(5)),
                'individual_id': i
            }
            large_populations.append(population)
        
        # 执行算法策略选择
        metrics = self.expert.state_analyzer.analyze_population_states(
            large_populations, iteration=10
        )
        individual_features = self.expert._convert_metrics_to_features(metrics)
        landscape_context = self.expert._build_landscape_context(self.test_landscape_report)
        
        strategy_assignments = self.expert._algorithmic_strategy_selection(
            individual_features, landscape_context, iteration=10
        )
        
        # 统计策略分布
        strategy_counts = {}
        for assignment in strategy_assignments:
            strategy_type = assignment.strategy_type.value
            strategy_counts[strategy_type] = strategy_counts.get(strategy_type, 0) + 1
        
        # 验证策略多样性
        self.assertGreater(len(strategy_counts), 1, "策略分配应该具有多样性")
        
        # 验证探索/开发平衡
        exploration_strategies = ['strong_exploration', 'balanced_exploration', 'intelligent_exploration']
        exploitation_strategies = ['cautious_exploitation', 'moderate_exploitation', 'aggressive_exploitation', 'intensive_exploitation']
        
        exploration_count = sum(strategy_counts.get(s, 0) for s in exploration_strategies)
        exploitation_count = sum(strategy_counts.get(s, 0) for s in exploitation_strategies)
        
        self.assertGreater(exploration_count, 0, "应该有探索策略")
        self.assertGreater(exploitation_count, 0, "应该有开发策略")


if __name__ == '__main__':
    # 配置日志
    logging.basicConfig(level=logging.INFO)
    
    # 运行测试
    unittest.main(verbosity=2)
