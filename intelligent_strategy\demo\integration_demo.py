"""
Demonstration script for the Intelligent Strategy Selection System integration.

This script shows how to integrate the intelligent strategy selection system
with the EoH-TSP-Solver framework and demonstrates its key capabilities.
"""

import os
import sys
import time
import numpy as np
import logging
from typing import List, Dict, Any

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from intelligent_strategy.integration import get_eoh_integrator, integrate_with_eoh_evolution
from intelligent_strategy.core.data_structures import StrategyType
from intelligent_strategy.system import IntelligentStrategySystem


def setup_logging():
    """Setup logging for the demonstration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('demo_integration.log')
        ]
    )


def create_mock_tsp_instance(n_cities: int = 20) -> Dict[str, Any]:
    """Create a mock TSP instance for demonstration."""
    # Generate random city coordinates
    coordinates = np.random.rand(n_cities, 2) * 100
    
    # Calculate distance matrix
    distance_matrix = np.zeros((n_cities, n_cities))
    for i in range(n_cities):
        for j in range(n_cities):
            if i != j:
                distance_matrix[i][j] = np.linalg.norm(coordinates[i] - coordinates[j])
    
    return {
        'func_name': f'demo_tsp_{n_cities}',
        'coordinate': coordinates,
        'distance_matrix': distance_matrix,
        'n_cities': n_cities
    }


def create_mock_population(n_cities: int, pop_size: int = 10) -> List[Dict]:
    """Create a mock population for demonstration."""
    population = []
    
    for i in range(pop_size):
        # Create random tour
        tour = np.random.permutation(n_cities).astype(np.int64)
        
        # Calculate tour cost (simplified)
        cost = np.random.uniform(100, 1000)  # Mock cost
        
        individual = {
            "tour": tour,
            "cur_cost": cost
        }
        population.append(individual)
    
    return population


def create_mock_landscape_report() -> Dict[str, Any]:
    """Create a mock landscape analysis report."""
    return {
        'ruggedness': {
            'autocorrelation': np.random.uniform(0.3, 0.8),
            'variance': np.random.uniform(0.2, 0.7)
        },
        'modality': {
            'estimated_peaks': np.random.randint(2, 10),
            'peak_distribution': 'scattered'
        },
        'diversity': {
            'diversity_index': np.random.uniform(0.4, 0.9),
            'population_spread': np.random.uniform(0.3, 0.8)
        },
        'gradient': {
            'average_gradient': np.random.uniform(0.1, 0.6),
            'gradient_variance': np.random.uniform(0.05, 0.3)
        }
    }


def create_mock_strategies(pop_size: int) -> Dict[int, str]:
    """Create mock strategy assignments from EoH framework."""
    strategies = ['exploration', 'exploitation', 'hybrid', 'diversification']
    return {i: np.random.choice(strategies) for i in range(pop_size)}


def demonstrate_system_initialization():
    """Demonstrate system initialization."""
    print("\n" + "="*60)
    print("INTELLIGENT STRATEGY SELECTION SYSTEM DEMONSTRATION")
    print("="*60)
    
    print("\n1. Initializing Intelligent Strategy System...")
    
    # Configuration for demonstration
    config = {
        'enabled': True,
        'integration_mode': 'full',
        'llm_interface': {
            'provider': 'mock',  # Use mock provider for demo
            'fallback_enabled': True
        },
        'landscape_analysis': {
            'enable_caching': False  # Disable caching for demo
        },
        'collaborative_escape': {
            'enabled': True,
            'max_groups': 5
        },
        'strategy_coordination': {
            'enabled': True,
            'max_concurrent_strategies': 3
        }
    }
    
    # Initialize the system
    strategy_system = IntelligentStrategySystem(config)
    print("   ✓ System initialized successfully")
    
    # Get the integrator
    integrator = get_eoh_integrator(config)
    print("   ✓ EoH integrator initialized")
    
    return strategy_system, integrator


def demonstrate_integration_workflow():
    """Demonstrate the complete integration workflow."""
    print("\n2. Demonstrating Integration Workflow...")
    
    # Initialize system
    strategy_system, integrator = demonstrate_system_initialization()
    
    # Create mock TSP instance
    print("\n   Creating mock TSP instance...")
    tsp_instance = create_mock_tsp_instance(n_cities=20)
    print(f"   ✓ TSP instance created: {tsp_instance['n_cities']} cities")
    
    # Simulate evolution iterations
    n_iterations = 5
    pop_size = 10
    
    print(f"\n   Simulating {n_iterations} evolution iterations...")
    
    # Initialize population
    populations = create_mock_population(tsp_instance['n_cities'], pop_size)
    print(f"   ✓ Initial population created: {len(populations)} individuals")
    
    for iteration in range(n_iterations):
        print(f"\n   --- Iteration {iteration + 1} ---")
        
        # Create mock inputs for this iteration
        landscape_report = create_mock_landscape_report()
        eoh_strategies = create_mock_strategies(pop_size)
        res_populations = populations[:3]  # Mock elite solutions
        
        print(f"      Landscape ruggedness: {landscape_report['ruggedness']['autocorrelation']:.3f}")
        print(f"      Population diversity: {landscape_report['diversity']['diversity_index']:.3f}")
        
        # Integrate with intelligent strategy system
        start_time = time.time()
        
        updated_populations = integrate_with_eoh_evolution(
            populations=populations,
            strategies=eoh_strategies,
            landscape_report=landscape_report,
            distance_matrix=tsp_instance['distance_matrix'],
            iteration=iteration,
            res_populations=res_populations,
            config=config
        )
        
        integration_time = time.time() - start_time
        
        print(f"      ✓ Integration completed in {integration_time:.3f}s")
        print(f"      ✓ Population updated: {len(updated_populations)} individuals")
        
        # Update population for next iteration
        populations = updated_populations
        
        # Show some statistics
        if hasattr(integrator, 'adapter'):
            stats = integrator.adapter.get_integration_statistics()
            print(f"      Strategy selections: {stats['integration_stats']['total_strategy_selections']}")
            print(f"      Successful executions: {stats['integration_stats']['successful_executions']}")
    
    return integrator


def demonstrate_strategy_selection():
    """Demonstrate intelligent strategy selection."""
    print("\n3. Demonstrating Strategy Selection...")
    
    # Initialize system
    strategy_system, integrator = demonstrate_system_initialization()
    
    # Create test scenario
    tsp_instance = create_mock_tsp_instance(n_cities=15)
    populations = create_mock_population(tsp_instance['n_cities'], 8)
    landscape_report = create_mock_landscape_report()
    
    print("   Test scenario:")
    print(f"      Cities: {tsp_instance['n_cities']}")
    print(f"      Population size: {len(populations)}")
    print(f"      Landscape ruggedness: {landscape_report['ruggedness']['autocorrelation']:.3f}")
    
    # Select strategies
    print("\n   Selecting strategies...")
    strategy_assignments = strategy_system.select_strategies(
        population=populations,
        iteration=0,
        landscape_report=landscape_report,
        distance_matrix=tsp_instance['distance_matrix']
    )
    
    print(f"   ✓ Strategies selected for {len(strategy_assignments)} individuals")
    
    # Show strategy distribution
    strategy_counts = {}
    for assignment in strategy_assignments.values():
        strategy_type = assignment.strategy_type.value
        strategy_counts[strategy_type] = strategy_counts.get(strategy_type, 0) + 1
    
    print("\n   Strategy distribution:")
    for strategy, count in strategy_counts.items():
        print(f"      {strategy}: {count} individuals")
    
    return strategy_assignments


def demonstrate_coordination_mechanisms():
    """Demonstrate coordination mechanisms."""
    print("\n4. Demonstrating Coordination Mechanisms...")
    
    # Initialize system
    strategy_system, integrator = demonstrate_system_initialization()
    
    # Access coordination components
    escape_coordinator = integrator.adapter.escape_coordinator
    strategy_coordinator = integrator.adapter.strategy_coordinator
    
    print("   Testing collaborative escape coordination...")
    
    # Simulate stagnated individuals requesting collaboration
    from intelligent_strategy.core.individual_state import IndividualState, StagnationLevel
    
    stagnated_individual = IndividualState(
        individual_id=1,
        fitness_value=500.0,
        fitness_rank=8,
        fitness_percentile=0.8,
        stagnation_duration=10,
        stagnation_level=StagnationLevel.HIGH
    )
    
    collaboration_info = escape_coordinator.request_collaborative_escape(
        individual_id="1",
        individual_state=stagnated_individual,
        current_fitness=500.0,
        solution_features={'tour_length': 15, 'avg_edge_length': 5.2}
    )
    
    if collaboration_info:
        print(f"   ✓ Collaboration group formed: {collaboration_info['group_id']}")
        print(f"      Coordination strategy: {collaboration_info['coordination_strategy']}")
        print(f"      Recommended strategy: {collaboration_info['recommended_strategy']}")
    else:
        print("   ✓ No collaboration needed at this time")
    
    print("\n   Testing strategy coordination...")
    
    # Request strategy execution coordination
    coordination_decision = strategy_coordinator.request_strategy_execution(
        individual_id="test_individual",
        strategy_type=StrategyType.STRONG_EXPLORATION,
        priority=0.8,
        resource_requirements={'cpu': 0.3, 'memory': 0.2}
    )
    
    print(f"   ✓ Coordination decision: {coordination_decision['decision']}")
    if 'estimated_wait_time' in coordination_decision:
        print(f"      Estimated wait time: {coordination_decision['estimated_wait_time']:.2f}s")


def demonstrate_performance_monitoring():
    """Demonstrate performance monitoring capabilities."""
    print("\n5. Demonstrating Performance Monitoring...")
    
    # Initialize system
    strategy_system, integrator = demonstrate_system_initialization()
    
    # Run a small integration to generate some data
    tsp_instance = create_mock_tsp_instance(n_cities=10)
    populations = create_mock_population(tsp_instance['n_cities'], 5)
    
    # Perform integration
    integrate_with_eoh_evolution(
        populations=populations,
        strategies=create_mock_strategies(5),
        landscape_report=create_mock_landscape_report(),
        distance_matrix=tsp_instance['distance_matrix'],
        iteration=0,
        res_populations=populations[:2]
    )
    
    # Get performance statistics
    if hasattr(integrator, 'adapter'):
        stats = integrator.adapter.get_integration_statistics()
        
        print("   Integration Statistics:")
        print(f"      Total strategy selections: {stats['integration_stats']['total_strategy_selections']}")
        print(f"      Total strategy executions: {stats['integration_stats']['total_strategy_executions']}")
        print(f"      Successful executions: {stats['integration_stats']['successful_executions']}")
        print(f"      Total integration time: {stats['integration_stats']['total_integration_time']:.3f}s")
        
        if 'strategy_factory_stats' in stats:
            factory_stats = stats['strategy_factory_stats']
            print(f"      Available strategies: {factory_stats['total_strategies']}")
            print(f"      Instantiated strategies: {factory_stats['instantiated_strategies']}")
        
        if 'escape_coordinator_stats' in stats:
            coord_stats = stats['escape_coordinator_stats']
            print(f"      Active collaboration groups: {coord_stats['active_groups']}")
            print(f"      Successful collaborations: {coord_stats['successful_collaborations']}")


def main():
    """Main demonstration function."""
    setup_logging()
    
    try:
        print("Starting Intelligent Strategy Selection System Demonstration...")
        
        # Run demonstrations
        demonstrate_integration_workflow()
        demonstrate_strategy_selection()
        demonstrate_coordination_mechanisms()
        demonstrate_performance_monitoring()
        
        print("\n" + "="*60)
        print("DEMONSTRATION COMPLETED SUCCESSFULLY")
        print("="*60)
        print("\nKey Features Demonstrated:")
        print("✓ System initialization and configuration")
        print("✓ Integration with EoH-TSP-Solver evolution loop")
        print("✓ Intelligent strategy selection using landscape analysis")
        print("✓ Collaborative escape coordination")
        print("✓ Strategy execution coordination")
        print("✓ Performance monitoring and statistics")
        print("\nThe system is ready for integration with the full EoH-TSP-Solver framework!")
        
    except Exception as e:
        print(f"\nError during demonstration: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
