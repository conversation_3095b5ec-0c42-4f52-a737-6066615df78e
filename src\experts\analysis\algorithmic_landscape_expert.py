# -*- coding: utf-8 -*-
"""
算法化适应度景观专家

基于纯算法实现的适应度景观分析专家，替换原有的LLM依赖实现。
保持与现有LandscapeExpert完全相同的接口和输出格式。
"""

import json
import logging
from typing import Dict, List, Any, Optional
import numpy as np
import time

try:
    from ...core.algorithms.fitness_landscape_analyzer import FitnessLandscapeAnalyzer
    from ...utils.utils import ResponseParser
except ImportError:
    # 绝对导入作为备选
    from core.algorithms.fitness_landscape_analyzer import FitnessLandscapeAnalyzer
    # 创建简单的ResponseParser替代
    class ResponseParser:
        @staticmethod
        def extract_data(text, key):
            return f"提取的{key}数据"


class AlgorithmicLandscapeExpert:
    """算法化适应度景观专家类"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化算法化景观专家

        参数:
            config: 配置参数
        """
        self.config = config or {}

        # 初始化核心分析器
        self.analyzer = FitnessLandscapeAnalyzer(self.config)

        # 初始化响应解析器（保持兼容性）
        self.response_parser = ResponseParser()

        # 历史数据存储
        self.fitness_history = []
        self.analysis_history = []

        self.logger = logging.getLogger(__name__)
        self.logger.info("算法化适应度景观专家初始化完成")

    def analyze(self, stats_report: Dict[str, Any], path_report: Dict[str, Any],
                elite_report: Dict[str, Any], iteration: int, total_iterations: int,
                history_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        分析适应度景观（主要接口，与原LandscapeExpert保持一致）

        参数:
            stats_report: 统计报告
            path_report: 路径报告
            elite_report: 精英报告
            iteration: 当前迭代次数
            total_iterations: 总迭代次数
            history_data: 历史数据

        返回:
            景观分析报告（格式与原LandscapeExpert一致）
        """
        try:
            start_time = time.time()

            # 提取种群和适应度数据
            populations, fitness_values = self._extract_population_data(
                stats_report, path_report, elite_report
            )

            if not populations or not fitness_values:
                return self._get_default_landscape_report(iteration, total_iterations)

            # 更新历史数据
            self._update_history(fitness_values, iteration)

            # 执行景观分析
            analysis_results = self.analyzer.analyze_landscape(
                populations=populations,
                fitness_values=fitness_values,
                fitness_history=self.fitness_history,
                current_iteration=iteration
            )

            # 转换为兼容格式
            landscape_report = self._convert_to_landscape_report(
                analysis_results, iteration, total_iterations,
                stats_report, path_report, elite_report
            )

            # 记录分析历史
            self.analysis_history.append({
                'iteration': iteration,
                'analysis_time': time.time() - start_time,
                'results': analysis_results
            })

            self.logger.info(f"第{iteration}代景观分析完成，耗时{time.time() - start_time:.3f}秒")

            return landscape_report

        except Exception as e:
            self.logger.error(f"景观分析失败: {e}")
            return self._get_default_landscape_report(iteration, total_iterations)

    def _extract_population_data(self, stats_report: Dict[str, Any],
                               path_report: Dict[str, Any],
                               elite_report: Dict[str, Any]) -> tuple:
        """从报告中提取种群和适应度数据"""
        populations = []
        fitness_values = []

        try:
            # 从stats_report提取适应度值
            if 'fitness_values' in stats_report:
                fitness_values = stats_report['fitness_values']
            elif 'population_fitness' in stats_report:
                fitness_values = stats_report['population_fitness']
            elif 'current_fitness' in stats_report:
                fitness_values = stats_report['current_fitness']

            # 从path_report提取路径信息
            if 'paths' in path_report:
                populations = path_report['paths']
            elif 'population_paths' in path_report:
                populations = path_report['population_paths']
            elif 'current_paths' in path_report:
                populations = path_report['current_paths']

            # 从elite_report补充精英个体
            if 'elite_paths' in elite_report and 'elite_fitness' in elite_report:
                elite_paths = elite_report['elite_paths']
                elite_fitness = elite_report['elite_fitness']

                # 确保精英个体包含在种群中
                for i, (path, fitness) in enumerate(zip(elite_paths, elite_fitness)):
                    if i < len(populations):
                        populations[i] = path
                        fitness_values[i] = fitness
                    else:
                        populations.append(path)
                        fitness_values.append(fitness)

            # 数据验证和清理
            if len(populations) != len(fitness_values):
                min_len = min(len(populations), len(fitness_values))
                populations = populations[:min_len]
                fitness_values = fitness_values[:min_len]

            # 确保数据类型正确
            populations = [list(path) if not isinstance(path, list) else path
                         for path in populations]
            fitness_values = [float(f) for f in fitness_values]

        except Exception as e:
            self.logger.warning(f"提取种群数据失败: {e}")
            populations = []
            fitness_values = []

        return populations, fitness_values

    def _update_history(self, fitness_values: List[float], iteration: int):
        """更新历史数据"""
        # 添加当前代的适应度值
        self.fitness_history.append(fitness_values.copy())

        # 限制历史数据长度
        max_history = self.config.get('max_history_length', 50)
        if len(self.fitness_history) > max_history:
            self.fitness_history = self.fitness_history[-max_history:]

        # 限制分析历史长度
        if len(self.analysis_history) > max_history:
            self.analysis_history = self.analysis_history[-max_history:]

    def _convert_to_landscape_report(self, analysis_results: Dict[str, Any],
                                   iteration: int, total_iterations: int,
                                   stats_report: Dict[str, Any],
                                   path_report: Dict[str, Any],
                                   elite_report: Dict[str, Any]) -> Dict[str, Any]:
        """将分析结果转换为与原LandscapeExpert兼容的格式"""

        # 基于分析结果生成景观特征描述
        search_space_features = self._generate_search_space_features(analysis_results)
        population_state = self._generate_population_state(analysis_results, stats_report)
        difficult_regions = self._identify_difficult_regions(analysis_results)
        opportunity_regions = self._identify_opportunity_regions(analysis_results)
        evolution_phase = self._determine_evolution_phase(analysis_results, iteration, total_iterations)
        evolution_direction = self._determine_evolution_direction(analysis_results)

        # 构建兼容的报告格式
        landscape_report = {
            "search_space_features": search_space_features,
            "population_state": population_state,
            "difficult_regions": difficult_regions,
            "opportunity_regions": opportunity_regions,
            "evolution_phase": evolution_phase,
            "evolution_direction": evolution_direction,

            # 添加原始分析数据（用于调试和扩展）
            "_algorithmic_analysis": analysis_results,
            "_metadata": {
                "analysis_method": "algorithmic",
                "iteration": iteration,
                "total_iterations": total_iterations,
                "timestamp": time.time()
            }
        }

        return landscape_report

    def _generate_search_space_features(self, analysis_results: Dict[str, Any]) -> str:
        """生成搜索空间特征描述"""
        try:
            local_optima = analysis_results.get('local_optima', {})
            gradient = analysis_results.get('gradient', {})
            clustering = analysis_results.get('clustering', {})
            coverage = analysis_results.get('coverage', {})

            # 基于量化指标生成描述
            optima_density = local_optima.get('local_optima_density', 0.0)
            gradient_variance = gradient.get('gradient_variance', 0.0)
            n_clusters = clustering.get('n_clusters', 0)
            coverage_ratio = coverage.get('coverage_ratio', 0.0)

            features = []

            # 局部最优密度特征
            if optima_density > 0.3:
                features.append("搜索空间存在大量局部最优，景观复杂度较高")
            elif optima_density > 0.1:
                features.append("搜索空间具有中等复杂度，存在一定数量的局部最优")
            else:
                features.append("搜索空间相对平滑，局部最优较少")

            # 梯度特征
            if gradient_variance > 1.0:
                features.append("适应度梯度变化剧烈，存在陡峭的适应度悬崖")
            elif gradient_variance > 0.1:
                features.append("适应度梯度变化适中，景观起伏有序")
            else:
                features.append("适应度梯度平缓，景观相对平坦")

            # 聚类特征
            if n_clusters > 5:
                features.append("种群呈现多聚类分布，搜索多样化")
            elif n_clusters > 2:
                features.append("种群形成若干聚类，存在多个搜索焦点")
            else:
                features.append("种群聚集度较高，搜索集中在少数区域")

            # 覆盖特征
            if coverage_ratio > 0.7:
                features.append("搜索空间覆盖广泛，探索充分")
            elif coverage_ratio > 0.3:
                features.append("搜索空间覆盖适中，仍有探索潜力")
            else:
                features.append("搜索空间覆盖有限，需要增强探索")

            return "; ".join(features)

        except Exception as e:
            self.logger.warning(f"生成搜索空间特征失败: {e}")
            return "搜索空间特征分析暂不可用"

    def _generate_population_state(self, analysis_results: Dict[str, Any],
                                 stats_report: Dict[str, Any]) -> str:
        """生成种群状态描述"""
        try:
            diversity = analysis_results.get('diversity', {})
            convergence = analysis_results.get('convergence', {})

            diversity_index = diversity.get('diversity_index', 0.0)
            convergence_rate = convergence.get('convergence_rate', 0.0)
            convergence_stability = convergence.get('convergence_stability', 0.0)

            # 从统计报告获取基本信息
            population_size = stats_report.get('population_size', 0)
            best_fitness = stats_report.get('best_fitness', 0.0)
            avg_fitness = stats_report.get('average_fitness', 0.0)

            state_desc = []

            # 多样性状态
            if diversity_index > 0.7:
                state_desc.append("种群多样性丰富，个体差异显著")
            elif diversity_index > 0.3:
                state_desc.append("种群多样性适中，保持一定的探索能力")
            else:
                state_desc.append("种群多样性较低，存在过早收敛风险")

            # 收敛状态
            if convergence_stability > 0.8:
                state_desc.append("种群收敛稳定，适应度改进趋于平缓")
            elif convergence_rate > 0.1:
                state_desc.append("种群正在快速收敛，适应度持续改进")
            else:
                state_desc.append("种群收敛缓慢，仍在积极搜索")

            # 适应度分布
            if best_fitness > 0 and avg_fitness > 0:
                fitness_gap = abs(best_fitness - avg_fitness) / max(abs(best_fitness), abs(avg_fitness))
                if fitness_gap > 0.5:
                    state_desc.append("种群适应度分布不均，存在明显的精英个体")
                else:
                    state_desc.append("种群适应度分布相对均匀")

            return "; ".join(state_desc)

        except Exception as e:
            self.logger.warning(f"生成种群状态失败: {e}")
            return "种群状态分析暂不可用"

    def _identify_difficult_regions(self, analysis_results: Dict[str, Any]) -> str:
        """识别困难区域"""
        try:
            local_optima = analysis_results.get('local_optima', {})
            gradient = analysis_results.get('gradient', {})
            clustering = analysis_results.get('clustering', {})

            optima_count = local_optima.get('local_optima_count', 0)
            anomaly_ratio = gradient.get('anomaly_ratio', 0.0)
            noise_ratio = clustering.get('noise_ratio', 0.0)

            difficult_regions = []

            # 基于局部最优识别困难区域
            if optima_count > 10:
                difficult_regions.append("存在大量局部最优陷阱，容易导致搜索停滞")

            # 基于梯度异常识别困难区域
            if anomaly_ratio > 0.2:
                difficult_regions.append("存在适应度梯度异常区域，搜索方向难以确定")

            # 基于噪声点识别困难区域
            if noise_ratio > 0.3:
                difficult_regions.append("存在大量孤立解，难以形成有效的搜索路径")

            if not difficult_regions:
                difficult_regions.append("当前未发现明显的困难搜索区域")

            return "; ".join(difficult_regions)

        except Exception as e:
            self.logger.warning(f"识别困难区域失败: {e}")
            return "困难区域识别暂不可用"

    def _identify_opportunity_regions(self, analysis_results: Dict[str, Any]) -> str:
        """识别机会区域"""
        try:
            gradient = analysis_results.get('gradient', {})
            coverage = analysis_results.get('coverage', {})
            diversity = analysis_results.get('diversity', {})

            gradient_mean = gradient.get('gradient_mean', 0.0)
            coverage_ratio = coverage.get('coverage_ratio', 0.0)
            diversity_index = diversity.get('diversity_index', 0.0)

            opportunities = []

            # 基于梯度识别机会区域
            if gradient_mean < -0.1:  # 负梯度表示改进方向
                opportunities.append("存在明显的适应度改进方向，可加强局部搜索")

            # 基于覆盖率识别机会区域
            if coverage_ratio < 0.5:
                opportunities.append("搜索空间仍有大量未探索区域，可增强全局探索")

            # 基于多样性识别机会区域
            if diversity_index > 0.6:
                opportunities.append("种群多样性良好，适合进行多方向并行搜索")

            if not opportunities:
                opportunities.append("当前搜索状态良好，建议保持现有策略")

            return "; ".join(opportunities)

        except Exception as e:
            self.logger.warning(f"识别机会区域失败: {e}")
            return "机会区域识别暂不可用"

    def _determine_evolution_phase(self, analysis_results: Dict[str, Any],
                                 iteration: int, total_iterations: int) -> str:
        """确定进化阶段"""
        try:
            convergence = analysis_results.get('convergence', {})
            diversity = analysis_results.get('diversity', {})

            convergence_rate = convergence.get('convergence_rate', 0.0)
            stagnation = convergence.get('stagnation_detection', False)
            diversity_index = diversity.get('diversity_index', 0.0)

            progress_ratio = iteration / total_iterations if total_iterations > 0 else 0.0

            # 基于多个指标判断进化阶段
            if progress_ratio < 0.3:
                if diversity_index > 0.7:
                    return "初期探索阶段：种群多样性丰富，正在广泛探索搜索空间"
                else:
                    return "初期收敛阶段：种群快速向优质区域聚集"
            elif progress_ratio < 0.7:
                if convergence_rate > 0.1:
                    return "中期优化阶段：种群正在稳定改进，适应度持续提升"
                elif stagnation:
                    return "中期停滞阶段：种群陷入局部最优，需要扰动策略"
                else:
                    return "中期平衡阶段：探索与开发达到动态平衡"
            else:
                if stagnation:
                    return "后期停滞阶段：种群收敛完成，建议终止或重启"
                elif convergence_rate > 0.05:
                    return "后期精化阶段：种群仍在缓慢改进，接近最优解"
                else:
                    return "后期收敛阶段：种群基本收敛，搜索即将完成"

        except Exception as e:
            self.logger.warning(f"确定进化阶段失败: {e}")
            return "进化阶段判断暂不可用"

    def _determine_evolution_direction(self, analysis_results: Dict[str, Any]) -> str:
        """确定进化方向"""
        try:
            convergence = analysis_results.get('convergence', {})
            diversity = analysis_results.get('diversity', {})
            coverage = analysis_results.get('coverage', {})

            convergence_trend = convergence.get('convergence_trend', 0.0)
            diversity_loss_rate = diversity.get('diversity_loss_rate', 0.0)
            coverage_ratio = coverage.get('coverage_ratio', 0.0)

            directions = []

            # 基于收敛趋势确定方向
            if convergence_trend < -0.1:
                directions.append("适应度持续改进，建议继续当前搜索策略")
            elif convergence_trend > 0.1:
                directions.append("适应度出现恶化趋势，建议调整搜索参数")
            else:
                directions.append("适应度趋于稳定，建议平衡探索与开发")

            # 基于多样性变化确定方向
            if diversity_loss_rate > 0.7:
                directions.append("多样性快速丢失，建议增强探索机制")
            elif diversity_loss_rate < 0.3:
                directions.append("多样性保持良好，可适当加强局部搜索")

            # 基于覆盖率确定方向
            if coverage_ratio < 0.3:
                directions.append("搜索覆盖不足，建议扩大搜索范围")
            elif coverage_ratio > 0.8:
                directions.append("搜索覆盖充分，可专注于精细优化")

            return "; ".join(directions) if directions else "进化方向建议暂不可用"

        except Exception as e:
            self.logger.warning(f"确定进化方向失败: {e}")
            return "进化方向判断暂不可用"

    def _get_default_landscape_report(self, iteration: int, total_iterations: int) -> Dict[str, Any]:
        """获取默认的景观报告（错误情况下使用）"""
        return {
            "search_space_features": "搜索空间特征分析暂不可用，可能由于数据不足或分析错误",
            "population_state": "种群状态分析暂不可用，请检查输入数据的完整性",
            "difficult_regions": "困难区域识别暂不可用，建议检查算法参数设置",
            "opportunity_regions": "机会区域识别暂不可用，可能需要更多历史数据",
            "evolution_phase": f"当前处于第{iteration}/{total_iterations}代，进化阶段判断暂不可用",
            "evolution_direction": "进化方向建议暂不可用，建议保持当前搜索策略",

            "_algorithmic_analysis": self.analyzer._get_default_results(),
            "_metadata": {
                "analysis_method": "algorithmic",
                "iteration": iteration,
                "total_iterations": total_iterations,
                "timestamp": time.time(),
                "status": "default_fallback"
            }
        }

    def get_analysis_summary(self) -> Dict[str, Any]:
        """获取分析历史摘要"""
        if not self.analysis_history:
            return {"message": "暂无分析历史数据"}

        # 计算统计信息
        analysis_times = [record['analysis_time'] for record in self.analysis_history]
        iterations = [record['iteration'] for record in self.analysis_history]

        summary = {
            "total_analyses": len(self.analysis_history),
            "iterations_analyzed": iterations,
            "average_analysis_time": np.mean(analysis_times),
            "total_analysis_time": sum(analysis_times),
            "latest_iteration": max(iterations) if iterations else 0,
            "performance_trend": self._calculate_performance_trend(analysis_times)
        }

        return summary

    def _calculate_performance_trend(self, analysis_times: List[float]) -> str:
        """计算性能趋势"""
        if len(analysis_times) < 3:
            return "数据不足，无法判断趋势"

        # 使用最近几次的分析时间计算趋势
        recent_times = analysis_times[-5:]
        if len(recent_times) >= 2:
            trend = (recent_times[-1] - recent_times[0]) / len(recent_times)
            if trend > 0.01:
                return "分析时间呈上升趋势，可能需要优化"
            elif trend < -0.01:
                return "分析时间呈下降趋势，性能正在改善"
            else:
                return "分析时间保持稳定"

        return "趋势判断暂不可用"

    def reset_history(self):
        """重置历史数据"""
        self.fitness_history.clear()
        self.analysis_history.clear()
        self.analyzer.reset()
        self.logger.info("算法化景观专家历史数据已重置")

    def export_analysis_data(self, filepath: str):
        """导出分析数据"""
        try:
            export_data = {
                "config": self.config,
                "fitness_history": self.fitness_history,
                "analysis_history": self.analysis_history,
                "summary": self.get_analysis_summary()
            }

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"分析数据已导出到: {filepath}")

        except Exception as e:
            self.logger.error(f"导出分析数据失败: {e}")

    def load_analysis_data(self, filepath: str):
        """加载分析数据"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                import_data = json.load(f)

            self.fitness_history = import_data.get('fitness_history', [])
            self.analysis_history = import_data.get('analysis_history', [])

            self.logger.info(f"分析数据已从{filepath}加载")

        except Exception as e:
            self.logger.error(f"加载分析数据失败: {e}")


# 工厂函数，用于创建算法化景观专家实例
def create_algorithmic_landscape_expert(config: Optional[Dict[str, Any]] = None) -> AlgorithmicLandscapeExpert:
    """
    创建算法化景观专家实例

    参数:
        config: 配置参数

    返回:
        AlgorithmicLandscapeExpert实例
    """
    return AlgorithmicLandscapeExpert(config)