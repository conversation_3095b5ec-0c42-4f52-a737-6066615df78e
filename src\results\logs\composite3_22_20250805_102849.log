2025-08-05 10:28:49,354 - __main__ - INFO - composite3_22 开始进化第 1 代
2025-08-05 10:28:49,355 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 10:28:49,356 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:49,358 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9633.000, 多样性=0.931
2025-08-05 10:28:49,359 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:49,361 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.931
2025-08-05 10:28:49,388 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:49,391 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 10:28:49,391 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:49,392 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 10:28:49,392 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 10:28:49,401 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -4937.800, 聚类评分: 0.000, 覆盖率: 0.085, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:49,401 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 10:28:49,401 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 10:28:49,402 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite3_22
2025-08-05 10:28:49,410 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=8.0%, 梯度: 961.74 → 884.96
2025-08-05 10:28:49,514 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite3_22\landscape_composite3_22_iter_71_20250805_102849.html
2025-08-05 10:28:49,584 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite3_22\dashboard_composite3_22_iter_71_20250805_102849.html
2025-08-05 10:28:49,584 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 71
2025-08-05 10:28:49,585 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 10:28:49,585 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1944秒
2025-08-05 10:28:49,585 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 142, 'max_size': 500, 'hits': 0, 'misses': 142, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 478, 'misses': 240, 'hit_rate': 0.6657381615598886, 'evictions': 140, 'ttl': 7200}}
2025-08-05 10:28:49,585 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -4937.8, 'local_optima_density': 0.1, 'gradient_variance': 200500507.57599998, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0847, 'fitness_entropy': 0.9349775297671233, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -4937.800)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.085)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360929.401593, 'performance_metrics': {}}}
2025-08-05 10:28:49,586 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:49,586 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:49,586 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:49,586 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:49,587 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 10:28:49,587 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 10:28:49,587 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 10:28:49,587 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:49,587 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:49,588 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 10:28:49,588 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:49,588 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:49,588 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:49,588 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:49,588 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:49,588 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:49,591 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 22
2025-08-05 10:28:49,592 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,593 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20767.0, 路径长度: 22, 收集中间解: 0
2025-08-05 10:28:49,593 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [3, 21, 1, 2, 19, 17, 0, 9, 11, 10, 16, 13, 18, 20, 7, 4, 8, 5, 6, 15, 12, 14], 'cur_cost': 20767.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:49,594 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 20767.00)
2025-08-05 10:28:49,594 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:49,594 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:49,594 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:49,595 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 10:28:49,595 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,596 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12188.0, 路径长度: 22, 收集中间解: 0
2025-08-05 10:28:49,596 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 21, 17, 11, 9, 10, 14, 13, 12, 16, 15, 18, 19, 20, 1, 2, 3, 7, 4, 5, 6, 8], 'cur_cost': 12188.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:49,596 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 12188.00)
2025-08-05 10:28:49,596 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:49,597 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:49,597 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:49,597 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 10:28:49,598 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,598 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14142.0, 路径长度: 22, 收集中间解: 0
2025-08-05 10:28:49,598 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 19, 2, 14, 9, 10, 11, 12, 13, 17, 15, 16, 21, 20, 18, 1, 3, 7, 4, 5, 6, 8], 'cur_cost': 14142.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:49,599 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 14142.00)
2025-08-05 10:28:49,599 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:49,599 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:49,599 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:49,600 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 10:28:49,600 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,600 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17369.0, 路径长度: 22, 收集中间解: 0
2025-08-05 10:28:49,600 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [1, 8, 7, 0, 4, 5, 6, 21, 15, 16, 17, 18, 19, 20, 3, 2, 13, 9, 10, 11, 12, 14], 'cur_cost': 17369.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:49,600 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 17369.00)
2025-08-05 10:28:49,600 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:49,601 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:49,601 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:49,601 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 10:28:49,602 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,602 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14895.0, 路径长度: 22, 收集中间解: 0
2025-08-05 10:28:49,602 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 17, 11, 4, 5, 6, 7, 8, 21, 15, 16, 18, 19, 20, 1, 2, 3, 13, 9, 10, 14, 12], 'cur_cost': 14895.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:49,602 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 14895.00)
2025-08-05 10:28:49,602 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:49,602 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:49,602 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:49,603 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 22
2025-08-05 10:28:49,603 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,603 - ExplorationExpert - INFO - 探索路径生成完成，成本: 36854.0, 路径长度: 22, 收集中间解: 0
2025-08-05 10:28:49,604 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [1, 17, 7, 2, 19, 5, 0, 9, 11, 13, 18, 3, 8, 6, 15, 16, 14, 4, 10, 21, 12, 20], 'cur_cost': 36854.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:49,604 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 36854.00)
2025-08-05 10:28:49,604 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:49,604 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:49,604 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:49,606 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 22
2025-08-05 10:28:49,606 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,606 - ExplorationExpert - INFO - 探索路径生成完成，成本: 25620.0, 路径长度: 22, 收集中间解: 0
2025-08-05 10:28:49,606 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [21, 0, 19, 13, 11, 10, 17, 3, 14, 16, 15, 20, 1, 9, 18, 7, 8, 5, 6, 4, 2, 12], 'cur_cost': 25620.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:49,607 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 25620.00)
2025-08-05 10:28:49,607 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:28:49,607 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:49,607 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:49,607 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 29936.0
2025-08-05 10:28:49,613 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 10:28:49,613 - ExploitationExpert - INFO - res_population_costs: [9473.0, 9473]
2025-08-05 10:28:49,614 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 16, 15, 20, 19, 18, 17, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 12, 11,
       10, 14,  9, 13,  3], dtype=int64)]
2025-08-05 10:28:49,614 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:49,615 - ExploitationExpert - INFO - populations: [{'tour': [3, 21, 1, 2, 19, 17, 0, 9, 11, 10, 16, 13, 18, 20, 7, 4, 8, 5, 6, 15, 12, 14], 'cur_cost': 20767.0}, {'tour': [0, 21, 17, 11, 9, 10, 14, 13, 12, 16, 15, 18, 19, 20, 1, 2, 3, 7, 4, 5, 6, 8], 'cur_cost': 12188.0}, {'tour': [0, 19, 2, 14, 9, 10, 11, 12, 13, 17, 15, 16, 21, 20, 18, 1, 3, 7, 4, 5, 6, 8], 'cur_cost': 14142.0}, {'tour': [1, 8, 7, 0, 4, 5, 6, 21, 15, 16, 17, 18, 19, 20, 3, 2, 13, 9, 10, 11, 12, 14], 'cur_cost': 17369.0}, {'tour': [0, 17, 11, 4, 5, 6, 7, 8, 21, 15, 16, 18, 19, 20, 1, 2, 3, 13, 9, 10, 14, 12], 'cur_cost': 14895.0}, {'tour': [1, 17, 7, 2, 19, 5, 0, 9, 11, 13, 18, 3, 8, 6, 15, 16, 14, 4, 10, 21, 12, 20], 'cur_cost': 36854.0}, {'tour': [21, 0, 19, 13, 11, 10, 17, 3, 14, 16, 15, 20, 1, 9, 18, 7, 8, 5, 6, 4, 2, 12], 'cur_cost': 25620.0}, {'tour': array([12,  4,  7,  6,  5, 14,  9, 16, 20, 13,  2,  1, 18,  8, 11, 15, 10,
        3, 19, 21,  0, 17], dtype=int64), 'cur_cost': 29936.0}, {'tour': array([18,  6,  9, 17,  4,  7, 10,  5, 13,  1, 15, 16, 21, 19,  0, 14,  3,
       12, 11,  8, 20,  2], dtype=int64), 'cur_cost': 38649.0}, {'tour': array([ 3, 18, 21, 16, 10,  8, 17,  2,  1, 14,  7, 20, 11, 13, 12,  5, 19,
        6, 15,  4,  9,  0], dtype=int64), 'cur_cost': 35983.0}]
2025-08-05 10:28:49,616 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:49,616 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 183, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 183, 'cache_hits': 0, 'similarity_calculations': 810, 'cache_hit_rate': 0.0, 'cache_size': 810}}
2025-08-05 10:28:49,616 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([12,  4,  7,  6,  5, 14,  9, 16, 20, 13,  2,  1, 18,  8, 11, 15, 10,
        3, 19, 21,  0, 17], dtype=int64), 'cur_cost': 29936.0, 'intermediate_solutions': [{'tour': array([11, 21,  6,  1, 12, 19, 20, 17, 13, 16,  7,  0, 18,  3, 15,  9,  5,
        4, 14,  8,  2, 10], dtype=int64), 'cur_cost': 38950.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1, 11, 21,  6, 12, 19, 20, 17, 13, 16,  7,  0, 18,  3, 15,  9,  5,
        4, 14,  8,  2, 10], dtype=int64), 'cur_cost': 41778.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([12,  1, 11, 21,  6, 19, 20, 17, 13, 16,  7,  0, 18,  3, 15,  9,  5,
        4, 14,  8,  2, 10], dtype=int64), 'cur_cost': 39083.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6,  1, 11, 21, 12, 19, 20, 17, 13, 16,  7,  0, 18,  3, 15,  9,  5,
        4, 14,  8,  2, 10], dtype=int64), 'cur_cost': 41818.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6, 12,  1, 11, 21, 19, 20, 17, 13, 16,  7,  0, 18,  3, 15,  9,  5,
        4, 14,  8,  2, 10], dtype=int64), 'cur_cost': 41491.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:49,617 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 29936.00)
2025-08-05 10:28:49,617 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:49,617 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:49,617 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:49,617 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 39570.0
2025-08-05 10:28:49,627 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 10:28:49,627 - ExploitationExpert - INFO - res_population_costs: [9473.0, 9473, 9455.0, 9455, 9455, 9455]
2025-08-05 10:28:49,627 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 16, 15, 20, 19, 18, 17, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13,  9, 14, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64)]
2025-08-05 10:28:49,630 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:49,631 - ExploitationExpert - INFO - populations: [{'tour': [3, 21, 1, 2, 19, 17, 0, 9, 11, 10, 16, 13, 18, 20, 7, 4, 8, 5, 6, 15, 12, 14], 'cur_cost': 20767.0}, {'tour': [0, 21, 17, 11, 9, 10, 14, 13, 12, 16, 15, 18, 19, 20, 1, 2, 3, 7, 4, 5, 6, 8], 'cur_cost': 12188.0}, {'tour': [0, 19, 2, 14, 9, 10, 11, 12, 13, 17, 15, 16, 21, 20, 18, 1, 3, 7, 4, 5, 6, 8], 'cur_cost': 14142.0}, {'tour': [1, 8, 7, 0, 4, 5, 6, 21, 15, 16, 17, 18, 19, 20, 3, 2, 13, 9, 10, 11, 12, 14], 'cur_cost': 17369.0}, {'tour': [0, 17, 11, 4, 5, 6, 7, 8, 21, 15, 16, 18, 19, 20, 1, 2, 3, 13, 9, 10, 14, 12], 'cur_cost': 14895.0}, {'tour': [1, 17, 7, 2, 19, 5, 0, 9, 11, 13, 18, 3, 8, 6, 15, 16, 14, 4, 10, 21, 12, 20], 'cur_cost': 36854.0}, {'tour': [21, 0, 19, 13, 11, 10, 17, 3, 14, 16, 15, 20, 1, 9, 18, 7, 8, 5, 6, 4, 2, 12], 'cur_cost': 25620.0}, {'tour': array([12,  4,  7,  6,  5, 14,  9, 16, 20, 13,  2,  1, 18,  8, 11, 15, 10,
        3, 19, 21,  0, 17], dtype=int64), 'cur_cost': 29936.0}, {'tour': array([12,  7, 18,  1,  9, 17, 10, 19, 15,  2, 16,  4, 11,  5,  0, 21, 14,
        3,  6,  8, 20, 13], dtype=int64), 'cur_cost': 39570.0}, {'tour': array([ 3, 18, 21, 16, 10,  8, 17,  2,  1, 14,  7, 20, 11, 13, 12,  5, 19,
        6, 15,  4,  9,  0], dtype=int64), 'cur_cost': 35983.0}]
2025-08-05 10:28:49,632 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:49,632 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 184, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 184, 'cache_hits': 0, 'similarity_calculations': 811, 'cache_hit_rate': 0.0, 'cache_size': 811}}
2025-08-05 10:28:49,633 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([12,  7, 18,  1,  9, 17, 10, 19, 15,  2, 16,  4, 11,  5,  0, 21, 14,
        3,  6,  8, 20, 13], dtype=int64), 'cur_cost': 39570.0, 'intermediate_solutions': [{'tour': array([ 9,  6, 18, 17,  4,  7, 10,  5, 13,  1, 15, 16, 21, 19,  0, 14,  3,
       12, 11,  8, 20,  2], dtype=int64), 'cur_cost': 38350.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([17,  9,  6, 18,  4,  7, 10,  5, 13,  1, 15, 16, 21, 19,  0, 14,  3,
       12, 11,  8, 20,  2], dtype=int64), 'cur_cost': 38704.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 4, 17,  9,  6, 18,  7, 10,  5, 13,  1, 15, 16, 21, 19,  0, 14,  3,
       12, 11,  8, 20,  2], dtype=int64), 'cur_cost': 42225.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([18, 17,  9,  6,  4,  7, 10,  5, 13,  1, 15, 16, 21, 19,  0, 14,  3,
       12, 11,  8, 20,  2], dtype=int64), 'cur_cost': 34613.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([18,  4, 17,  9,  6,  7, 10,  5, 13,  1, 15, 16, 21, 19,  0, 14,  3,
       12, 11,  8, 20,  2], dtype=int64), 'cur_cost': 38650.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:49,633 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 39570.00)
2025-08-05 10:28:49,634 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:49,634 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:49,634 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:49,635 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 22
2025-08-05 10:28:49,635 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,635 - ExplorationExpert - INFO - 探索路径生成完成，成本: 25734.0, 路径长度: 22, 收集中间解: 0
2025-08-05 10:28:49,636 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [21, 8, 17, 11, 4, 6, 14, 12, 13, 10, 18, 16, 1, 9, 7, 5, 20, 3, 2, 0, 15, 19], 'cur_cost': 25734.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:49,636 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 25734.00)
2025-08-05 10:28:49,636 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:49,636 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:49,638 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [3, 21, 1, 2, 19, 17, 0, 9, 11, 10, 16, 13, 18, 20, 7, 4, 8, 5, 6, 15, 12, 14], 'cur_cost': 20767.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 17, 11, 9, 10, 14, 13, 12, 16, 15, 18, 19, 20, 1, 2, 3, 7, 4, 5, 6, 8], 'cur_cost': 12188.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 2, 14, 9, 10, 11, 12, 13, 17, 15, 16, 21, 20, 18, 1, 3, 7, 4, 5, 6, 8], 'cur_cost': 14142.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [1, 8, 7, 0, 4, 5, 6, 21, 15, 16, 17, 18, 19, 20, 3, 2, 13, 9, 10, 11, 12, 14], 'cur_cost': 17369.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 11, 4, 5, 6, 7, 8, 21, 15, 16, 18, 19, 20, 1, 2, 3, 13, 9, 10, 14, 12], 'cur_cost': 14895.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [1, 17, 7, 2, 19, 5, 0, 9, 11, 13, 18, 3, 8, 6, 15, 16, 14, 4, 10, 21, 12, 20], 'cur_cost': 36854.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [21, 0, 19, 13, 11, 10, 17, 3, 14, 16, 15, 20, 1, 9, 18, 7, 8, 5, 6, 4, 2, 12], 'cur_cost': 25620.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([12,  4,  7,  6,  5, 14,  9, 16, 20, 13,  2,  1, 18,  8, 11, 15, 10,
        3, 19, 21,  0, 17], dtype=int64), 'cur_cost': 29936.0, 'intermediate_solutions': [{'tour': array([11, 21,  6,  1, 12, 19, 20, 17, 13, 16,  7,  0, 18,  3, 15,  9,  5,
        4, 14,  8,  2, 10], dtype=int64), 'cur_cost': 38950.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1, 11, 21,  6, 12, 19, 20, 17, 13, 16,  7,  0, 18,  3, 15,  9,  5,
        4, 14,  8,  2, 10], dtype=int64), 'cur_cost': 41778.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([12,  1, 11, 21,  6, 19, 20, 17, 13, 16,  7,  0, 18,  3, 15,  9,  5,
        4, 14,  8,  2, 10], dtype=int64), 'cur_cost': 39083.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6,  1, 11, 21, 12, 19, 20, 17, 13, 16,  7,  0, 18,  3, 15,  9,  5,
        4, 14,  8,  2, 10], dtype=int64), 'cur_cost': 41818.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6, 12,  1, 11, 21, 19, 20, 17, 13, 16,  7,  0, 18,  3, 15,  9,  5,
        4, 14,  8,  2, 10], dtype=int64), 'cur_cost': 41491.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([12,  7, 18,  1,  9, 17, 10, 19, 15,  2, 16,  4, 11,  5,  0, 21, 14,
        3,  6,  8, 20, 13], dtype=int64), 'cur_cost': 39570.0, 'intermediate_solutions': [{'tour': array([ 9,  6, 18, 17,  4,  7, 10,  5, 13,  1, 15, 16, 21, 19,  0, 14,  3,
       12, 11,  8, 20,  2], dtype=int64), 'cur_cost': 38350.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([17,  9,  6, 18,  4,  7, 10,  5, 13,  1, 15, 16, 21, 19,  0, 14,  3,
       12, 11,  8, 20,  2], dtype=int64), 'cur_cost': 38704.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 4, 17,  9,  6, 18,  7, 10,  5, 13,  1, 15, 16, 21, 19,  0, 14,  3,
       12, 11,  8, 20,  2], dtype=int64), 'cur_cost': 42225.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([18, 17,  9,  6,  4,  7, 10,  5, 13,  1, 15, 16, 21, 19,  0, 14,  3,
       12, 11,  8, 20,  2], dtype=int64), 'cur_cost': 34613.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([18,  4, 17,  9,  6,  7, 10,  5, 13,  1, 15, 16, 21, 19,  0, 14,  3,
       12, 11,  8, 20,  2], dtype=int64), 'cur_cost': 38650.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [21, 8, 17, 11, 4, 6, 14, 12, 13, 10, 18, 16, 1, 9, 7, 5, 20, 3, 2, 0, 15, 19], 'cur_cost': 25734.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:49,638 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:49,638 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:49,643 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12188.000, 多样性=0.903
2025-08-05 10:28:49,643 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 10:28:49,643 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 10:28:49,643 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:49,644 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.01870427064965388, 'best_improvement': -0.26523409114502233}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.030368763557483924}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.058344284609943155, 'recent_improvements': [-0.0024406706036040355, -0.04687194726164887, -0.11912923982349037], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 6, 'new_count': 6, 'count_change': 0, 'old_best_cost': 9455.0, 'new_best_cost': 9455.0, 'quality_improvement': 0.0, 'old_diversity': 0.5969696969696969, 'new_diversity': 0.5969696969696969, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配']}
2025-08-05 10:28:49,645 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 10:28:49,645 - __main__ - INFO - composite3_22 开始进化第 2 代
2025-08-05 10:28:49,645 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 10:28:49,646 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:49,646 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12188.000, 多样性=0.903
2025-08-05 10:28:49,647 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:49,648 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.903
2025-08-05 10:28:49,648 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:49,650 - EliteExpert - INFO - 精英解分析完成: 精英解数量=6, 多样性=0.597
2025-08-05 10:28:49,652 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 10:28:49,652 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:49,652 - LandscapeExpert - INFO - 添加精英解数据: 6个精英解
2025-08-05 10:28:49,652 - LandscapeExpert - INFO - 数据提取成功: 16个路径, 16个适应度值
2025-08-05 10:28:49,683 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.250, 适应度梯度: -2689.200, 聚类评分: 0.000, 覆盖率: 0.086, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:49,683 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 10:28:49,684 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:49,684 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite3_22
2025-08-05 10:28:49,690 - visualization.landscape_visualizer - INFO - 插值约束: 58 个点被约束到最小值 9455.00
2025-08-05 10:28:49,693 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.5%, 梯度: 1016.06 → 939.98
2025-08-05 10:28:49,819 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite3_22\landscape_composite3_22_iter_72_20250805_102849.html
2025-08-05 10:28:49,895 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite3_22\dashboard_composite3_22_iter_72_20250805_102849.html
2025-08-05 10:28:49,895 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 72
2025-08-05 10:28:49,895 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 10:28:49,895 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2433秒
2025-08-05 10:28:49,895 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.25, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -2689.2, 'local_optima_density': 0.25, 'gradient_variance': 55090951.90499999, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0863, 'fitness_entropy': 0.8537510532665803, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -2689.200)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.086)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360929.6830828, 'performance_metrics': {}}}
2025-08-05 10:28:49,896 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:49,896 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:49,896 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:49,896 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:49,896 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 10:28:49,896 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 10:28:49,897 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 10:28:49,897 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:49,897 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:49,897 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 10:28:49,897 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:49,897 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:49,897 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:49,897 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:49,897 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:49,897 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:49,899 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 22
2025-08-05 10:28:49,899 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,899 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,899 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,899 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,900 - ExplorationExpert - INFO - 探索路径生成完成，成本: 18700.0, 路径长度: 22, 收集中间解: 3
2025-08-05 10:28:49,900 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [6, 21, 0, 1, 2, 20, 18, 15, 16, 19, 17, 3, 9, 13, 11, 14, 7, 8, 4, 5, 12, 10], 'cur_cost': 18700.0, 'intermediate_solutions': [{'tour': [3, 21, 1, 2, 19, 17, 0, 9, 11, 10, 20, 13, 18, 16, 7, 4, 8, 5, 6, 15, 12, 14], 'cur_cost': 20898.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 21, 1, 2, 19, 17, 0, 18, 13, 16, 10, 11, 9, 20, 7, 4, 8, 5, 6, 15, 12, 14], 'cur_cost': 21132.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 21, 1, 2, 19, 0, 9, 11, 10, 16, 13, 18, 20, 7, 4, 8, 5, 6, 15, 12, 14, 17], 'cur_cost': 21078.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:49,900 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 18700.00)
2025-08-05 10:28:49,900 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:49,900 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:49,900 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:49,901 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 10:28:49,901 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,901 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,901 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,901 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,901 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17379.0, 路径长度: 22, 收集中间解: 3
2025-08-05 10:28:49,902 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 4, 3, 7, 6, 5, 8, 21, 15, 16, 17, 18, 19, 20, 1, 2, 13, 9, 10, 11, 12, 14], 'cur_cost': 17379.0, 'intermediate_solutions': [{'tour': [0, 21, 17, 11, 13, 10, 14, 9, 12, 16, 15, 18, 19, 20, 1, 2, 3, 7, 4, 5, 6, 8], 'cur_cost': 12248.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 9, 11, 17, 21, 0, 14, 13, 12, 16, 15, 18, 19, 20, 1, 2, 3, 7, 4, 5, 6, 8], 'cur_cost': 14907.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 17, 11, 1, 9, 10, 14, 13, 12, 16, 15, 18, 19, 20, 2, 3, 7, 4, 5, 6, 8], 'cur_cost': 17091.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:49,902 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 17379.00)
2025-08-05 10:28:49,902 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:49,902 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:49,902 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:49,903 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 22
2025-08-05 10:28:49,903 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,903 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,903 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,903 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,904 - ExplorationExpert - INFO - 探索路径生成完成，成本: 26759.0, 路径长度: 22, 收集中间解: 3
2025-08-05 10:28:49,904 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [17, 1, 2, 21, 13, 16, 19, 3, 9, 10, 20, 14, 15, 12, 0, 18, 7, 4, 8, 5, 6, 11], 'cur_cost': 26759.0, 'intermediate_solutions': [{'tour': [0, 19, 2, 14, 9, 10, 11, 12, 13, 8, 15, 16, 21, 20, 18, 1, 3, 7, 4, 5, 6, 17], 'cur_cost': 18116.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 2, 14, 9, 10, 11, 12, 13, 17, 20, 21, 16, 15, 18, 1, 3, 7, 4, 5, 6, 8], 'cur_cost': 14153.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 2, 14, 9, 11, 12, 13, 17, 15, 16, 21, 20, 18, 1, 3, 7, 10, 4, 5, 6, 8], 'cur_cost': 20502.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:49,904 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 26759.00)
2025-08-05 10:28:49,904 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:49,904 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:49,904 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:49,905 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 22
2025-08-05 10:28:49,905 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,905 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,906 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,906 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,906 - ExplorationExpert - INFO - 探索路径生成完成，成本: 22315.0, 路径长度: 22, 收集中间解: 3
2025-08-05 10:28:49,906 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [9, 12, 10, 13, 19, 17, 15, 1, 16, 20, 18, 3, 14, 2, 11, 0, 7, 4, 5, 6, 8, 21], 'cur_cost': 22315.0, 'intermediate_solutions': [{'tour': [1, 8, 7, 0, 4, 5, 6, 21, 15, 16, 17, 18, 19, 20, 3, 2, 14, 9, 10, 11, 12, 13], 'cur_cost': 17338.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 7, 0, 4, 5, 6, 21, 15, 16, 17, 18, 19, 20, 3, 2, 13, 9, 10, 14, 12, 11], 'cur_cost': 17436.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 8, 7, 0, 4, 5, 6, 21, 16, 17, 18, 19, 15, 20, 3, 2, 13, 9, 10, 11, 12, 14], 'cur_cost': 17369.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:49,906 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 22315.00)
2025-08-05 10:28:49,906 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:49,906 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:49,907 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:49,907 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 10:28:49,907 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,907 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,907 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,908 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,908 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17268.0, 路径长度: 22, 收集中间解: 3
2025-08-05 10:28:49,908 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 7, 3, 10, 9, 11, 12, 13, 14, 17, 15, 16, 21, 20, 19, 18, 1, 2, 6, 4, 5, 8], 'cur_cost': 17268.0, 'intermediate_solutions': [{'tour': [0, 17, 11, 4, 5, 6, 7, 8, 21, 15, 16, 18, 19, 20, 10, 2, 3, 13, 9, 1, 14, 12], 'cur_cost': 22601.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 11, 4, 5, 6, 7, 8, 21, 15, 16, 18, 2, 1, 20, 19, 3, 13, 9, 10, 14, 12], 'cur_cost': 17271.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 11, 4, 10, 5, 6, 7, 8, 21, 15, 16, 18, 19, 20, 1, 2, 3, 13, 9, 14, 12], 'cur_cost': 21310.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:49,908 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 17268.00)
2025-08-05 10:28:49,908 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:28:49,908 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:49,908 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:49,909 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 38296.0
2025-08-05 10:28:49,918 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 10:28:49,918 - ExploitationExpert - INFO - res_population_costs: [9455.0, 9455, 9455, 9455, 9473.0, 9473, 9455, 9455, 9455, 9455]
2025-08-05 10:28:49,918 - ExploitationExpert - INFO - res_populations: [array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13,  9, 14, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 16, 15, 20, 19, 18, 17, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64)]
2025-08-05 10:28:49,920 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:49,920 - ExploitationExpert - INFO - populations: [{'tour': [6, 21, 0, 1, 2, 20, 18, 15, 16, 19, 17, 3, 9, 13, 11, 14, 7, 8, 4, 5, 12, 10], 'cur_cost': 18700.0}, {'tour': [0, 4, 3, 7, 6, 5, 8, 21, 15, 16, 17, 18, 19, 20, 1, 2, 13, 9, 10, 11, 12, 14], 'cur_cost': 17379.0}, {'tour': [17, 1, 2, 21, 13, 16, 19, 3, 9, 10, 20, 14, 15, 12, 0, 18, 7, 4, 8, 5, 6, 11], 'cur_cost': 26759.0}, {'tour': [9, 12, 10, 13, 19, 17, 15, 1, 16, 20, 18, 3, 14, 2, 11, 0, 7, 4, 5, 6, 8, 21], 'cur_cost': 22315.0}, {'tour': [0, 7, 3, 10, 9, 11, 12, 13, 14, 17, 15, 16, 21, 20, 19, 18, 1, 2, 6, 4, 5, 8], 'cur_cost': 17268.0}, {'tour': array([20,  2, 10,  8,  4,  6, 11, 18,  3,  5, 16, 21, 13, 15,  7,  1, 12,
       19, 14,  0,  9, 17], dtype=int64), 'cur_cost': 38296.0}, {'tour': [21, 0, 19, 13, 11, 10, 17, 3, 14, 16, 15, 20, 1, 9, 18, 7, 8, 5, 6, 4, 2, 12], 'cur_cost': 25620.0}, {'tour': [12, 4, 7, 6, 5, 14, 9, 16, 20, 13, 2, 1, 18, 8, 11, 15, 10, 3, 19, 21, 0, 17], 'cur_cost': 29936.0}, {'tour': [12, 7, 18, 1, 9, 17, 10, 19, 15, 2, 16, 4, 11, 5, 0, 21, 14, 3, 6, 8, 20, 13], 'cur_cost': 39570.0}, {'tour': [21, 8, 17, 11, 4, 6, 14, 12, 13, 10, 18, 16, 1, 9, 7, 5, 20, 3, 2, 0, 15, 19], 'cur_cost': 25734.0}]
2025-08-05 10:28:49,921 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:49,921 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 185, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 185, 'cache_hits': 0, 'similarity_calculations': 813, 'cache_hit_rate': 0.0, 'cache_size': 813}}
2025-08-05 10:28:49,921 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([20,  2, 10,  8,  4,  6, 11, 18,  3,  5, 16, 21, 13, 15,  7,  1, 12,
       19, 14,  0,  9, 17], dtype=int64), 'cur_cost': 38296.0, 'intermediate_solutions': [{'tour': array([ 7, 17,  1,  2, 19,  5,  0,  9, 11, 13, 18,  3,  8,  6, 15, 16, 14,
        4, 10, 21, 12, 20]), 'cur_cost': 34808.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2,  7, 17,  1, 19,  5,  0,  9, 11, 13, 18,  3,  8,  6, 15, 16, 14,
        4, 10, 21, 12, 20]), 'cur_cost': 36854.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([19,  2,  7, 17,  1,  5,  0,  9, 11, 13, 18,  3,  8,  6, 15, 16, 14,
        4, 10, 21, 12, 20]), 'cur_cost': 36461.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 1,  2,  7, 17, 19,  5,  0,  9, 11, 13, 18,  3,  8,  6, 15, 16, 14,
        4, 10, 21, 12, 20]), 'cur_cost': 34457.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 1, 19,  2,  7, 17,  5,  0,  9, 11, 13, 18,  3,  8,  6, 15, 16, 14,
        4, 10, 21, 12, 20]), 'cur_cost': 36803.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:49,922 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 38296.00)
2025-08-05 10:28:49,922 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:49,922 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:49,922 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:49,923 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 22
2025-08-05 10:28:49,923 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,923 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,923 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,923 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,923 - ExplorationExpert - INFO - 探索路径生成完成，成本: 33185.0, 路径长度: 22, 收集中间解: 3
2025-08-05 10:28:49,923 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [6, 1, 2, 7, 13, 20, 18, 21, 9, 19, 17, 14, 15, 12, 0, 5, 8, 4, 10, 11, 16, 3], 'cur_cost': 33185.0, 'intermediate_solutions': [{'tour': [21, 0, 19, 13, 11, 7, 17, 3, 14, 16, 15, 20, 1, 9, 18, 10, 8, 5, 6, 4, 2, 12], 'cur_cost': 31914.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [21, 0, 19, 13, 11, 2, 4, 6, 5, 8, 7, 18, 9, 1, 20, 15, 16, 14, 3, 17, 10, 12], 'cur_cost': 25686.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [21, 0, 19, 13, 11, 10, 17, 3, 14, 12, 16, 15, 20, 1, 9, 18, 7, 8, 5, 6, 4, 2], 'cur_cost': 22901.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:49,924 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 33185.00)
2025-08-05 10:28:49,924 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:49,924 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:49,924 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:49,925 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 22
2025-08-05 10:28:49,925 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,925 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,925 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,926 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,926 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20536.0, 路径长度: 22, 收集中间解: 3
2025-08-05 10:28:49,926 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [16, 15, 19, 1, 2, 14, 11, 10, 21, 3, 18, 12, 13, 20, 17, 7, 4, 5, 6, 8, 0, 9], 'cur_cost': 20536.0, 'intermediate_solutions': [{'tour': [12, 4, 7, 6, 5, 14, 9, 16, 20, 13, 2, 1, 21, 8, 11, 15, 10, 3, 19, 18, 0, 17], 'cur_cost': 29825.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [12, 4, 7, 6, 5, 14, 9, 16, 20, 13, 2, 1, 18, 8, 11, 15, 10, 3, 19, 21, 17, 0], 'cur_cost': 29622.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [12, 4, 7, 6, 5, 14, 9, 16, 20, 13, 2, 1, 18, 8, 11, 15, 10, 3, 19, 21, 0, 17], 'cur_cost': 29936.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:49,926 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 20536.00)
2025-08-05 10:28:49,926 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:49,926 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:49,926 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:49,926 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 38492.0
2025-08-05 10:28:49,935 - ExploitationExpert - INFO - res_population_num: 12
2025-08-05 10:28:49,935 - ExploitationExpert - INFO - res_population_costs: [9455.0, 9455, 9455, 9455, 9473.0, 9473, 9455, 9455, 9455, 9455, 9455.0, 9455]
2025-08-05 10:28:49,935 - ExploitationExpert - INFO - res_populations: [array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13,  9, 14, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 16, 15, 20, 19, 18, 17, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64)]
2025-08-05 10:28:49,937 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:49,937 - ExploitationExpert - INFO - populations: [{'tour': [6, 21, 0, 1, 2, 20, 18, 15, 16, 19, 17, 3, 9, 13, 11, 14, 7, 8, 4, 5, 12, 10], 'cur_cost': 18700.0}, {'tour': [0, 4, 3, 7, 6, 5, 8, 21, 15, 16, 17, 18, 19, 20, 1, 2, 13, 9, 10, 11, 12, 14], 'cur_cost': 17379.0}, {'tour': [17, 1, 2, 21, 13, 16, 19, 3, 9, 10, 20, 14, 15, 12, 0, 18, 7, 4, 8, 5, 6, 11], 'cur_cost': 26759.0}, {'tour': [9, 12, 10, 13, 19, 17, 15, 1, 16, 20, 18, 3, 14, 2, 11, 0, 7, 4, 5, 6, 8, 21], 'cur_cost': 22315.0}, {'tour': [0, 7, 3, 10, 9, 11, 12, 13, 14, 17, 15, 16, 21, 20, 19, 18, 1, 2, 6, 4, 5, 8], 'cur_cost': 17268.0}, {'tour': array([20,  2, 10,  8,  4,  6, 11, 18,  3,  5, 16, 21, 13, 15,  7,  1, 12,
       19, 14,  0,  9, 17], dtype=int64), 'cur_cost': 38296.0}, {'tour': [6, 1, 2, 7, 13, 20, 18, 21, 9, 19, 17, 14, 15, 12, 0, 5, 8, 4, 10, 11, 16, 3], 'cur_cost': 33185.0}, {'tour': [16, 15, 19, 1, 2, 14, 11, 10, 21, 3, 18, 12, 13, 20, 17, 7, 4, 5, 6, 8, 0, 9], 'cur_cost': 20536.0}, {'tour': array([13,  7,  1,  3, 12, 20,  8, 14, 10,  6, 21, 11, 18,  0,  2,  4, 16,
       19,  5,  9, 17, 15], dtype=int64), 'cur_cost': 38492.0}, {'tour': [21, 8, 17, 11, 4, 6, 14, 12, 13, 10, 18, 16, 1, 9, 7, 5, 20, 3, 2, 0, 15, 19], 'cur_cost': 25734.0}]
2025-08-05 10:28:49,939 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:49,939 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 186, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 186, 'cache_hits': 0, 'similarity_calculations': 816, 'cache_hit_rate': 0.0, 'cache_size': 816}}
2025-08-05 10:28:49,940 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([13,  7,  1,  3, 12, 20,  8, 14, 10,  6, 21, 11, 18,  0,  2,  4, 16,
       19,  5,  9, 17, 15], dtype=int64), 'cur_cost': 38492.0, 'intermediate_solutions': [{'tour': array([18,  7, 12,  1,  9, 17, 10, 19, 15,  2, 16,  4, 11,  5,  0, 21, 14,
        3,  6,  8, 20, 13]), 'cur_cost': 42300.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1, 18,  7, 12,  9, 17, 10, 19, 15,  2, 16,  4, 11,  5,  0, 21, 14,
        3,  6,  8, 20, 13]), 'cur_cost': 39525.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9,  1, 18,  7, 12, 17, 10, 19, 15,  2, 16,  4, 11,  5,  0, 21, 14,
        3,  6,  8, 20, 13]), 'cur_cost': 39549.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([12,  1, 18,  7,  9, 17, 10, 19, 15,  2, 16,  4, 11,  5,  0, 21, 14,
        3,  6,  8, 20, 13]), 'cur_cost': 39606.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([12,  9,  1, 18,  7, 17, 10, 19, 15,  2, 16,  4, 11,  5,  0, 21, 14,
        3,  6,  8, 20, 13]), 'cur_cost': 36917.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:49,940 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 38492.00)
2025-08-05 10:28:49,941 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:49,941 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:49,941 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:49,942 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 10:28:49,942 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,942 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,942 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,942 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:49,943 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17781.0, 路径长度: 22, 收集中间解: 3
2025-08-05 10:28:49,944 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 21, 1, 4, 9, 10, 11, 12, 13, 14, 17, 15, 16, 18, 19, 20, 3, 2, 7, 6, 5, 8], 'cur_cost': 17781.0, 'intermediate_solutions': [{'tour': [21, 8, 17, 20, 4, 6, 14, 12, 13, 10, 18, 16, 1, 9, 7, 5, 11, 3, 2, 0, 15, 19], 'cur_cost': 25425.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [21, 8, 17, 11, 4, 6, 14, 12, 13, 10, 18, 16, 1, 9, 7, 5, 20, 3, 2, 19, 15, 0], 'cur_cost': 28160.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [21, 8, 17, 11, 4, 6, 14, 12, 13, 10, 16, 1, 9, 7, 5, 20, 3, 2, 0, 15, 19, 18], 'cur_cost': 25715.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:49,944 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 17781.00)
2025-08-05 10:28:49,944 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:49,944 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:49,945 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [6, 21, 0, 1, 2, 20, 18, 15, 16, 19, 17, 3, 9, 13, 11, 14, 7, 8, 4, 5, 12, 10], 'cur_cost': 18700.0, 'intermediate_solutions': [{'tour': [3, 21, 1, 2, 19, 17, 0, 9, 11, 10, 20, 13, 18, 16, 7, 4, 8, 5, 6, 15, 12, 14], 'cur_cost': 20898.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 21, 1, 2, 19, 17, 0, 18, 13, 16, 10, 11, 9, 20, 7, 4, 8, 5, 6, 15, 12, 14], 'cur_cost': 21132.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 21, 1, 2, 19, 0, 9, 11, 10, 16, 13, 18, 20, 7, 4, 8, 5, 6, 15, 12, 14, 17], 'cur_cost': 21078.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 3, 7, 6, 5, 8, 21, 15, 16, 17, 18, 19, 20, 1, 2, 13, 9, 10, 11, 12, 14], 'cur_cost': 17379.0, 'intermediate_solutions': [{'tour': [0, 21, 17, 11, 13, 10, 14, 9, 12, 16, 15, 18, 19, 20, 1, 2, 3, 7, 4, 5, 6, 8], 'cur_cost': 12248.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 9, 11, 17, 21, 0, 14, 13, 12, 16, 15, 18, 19, 20, 1, 2, 3, 7, 4, 5, 6, 8], 'cur_cost': 14907.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 17, 11, 1, 9, 10, 14, 13, 12, 16, 15, 18, 19, 20, 2, 3, 7, 4, 5, 6, 8], 'cur_cost': 17091.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [17, 1, 2, 21, 13, 16, 19, 3, 9, 10, 20, 14, 15, 12, 0, 18, 7, 4, 8, 5, 6, 11], 'cur_cost': 26759.0, 'intermediate_solutions': [{'tour': [0, 19, 2, 14, 9, 10, 11, 12, 13, 8, 15, 16, 21, 20, 18, 1, 3, 7, 4, 5, 6, 17], 'cur_cost': 18116.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 2, 14, 9, 10, 11, 12, 13, 17, 20, 21, 16, 15, 18, 1, 3, 7, 4, 5, 6, 8], 'cur_cost': 14153.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 2, 14, 9, 11, 12, 13, 17, 15, 16, 21, 20, 18, 1, 3, 7, 10, 4, 5, 6, 8], 'cur_cost': 20502.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [9, 12, 10, 13, 19, 17, 15, 1, 16, 20, 18, 3, 14, 2, 11, 0, 7, 4, 5, 6, 8, 21], 'cur_cost': 22315.0, 'intermediate_solutions': [{'tour': [1, 8, 7, 0, 4, 5, 6, 21, 15, 16, 17, 18, 19, 20, 3, 2, 14, 9, 10, 11, 12, 13], 'cur_cost': 17338.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 7, 0, 4, 5, 6, 21, 15, 16, 17, 18, 19, 20, 3, 2, 13, 9, 10, 14, 12, 11], 'cur_cost': 17436.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 8, 7, 0, 4, 5, 6, 21, 16, 17, 18, 19, 15, 20, 3, 2, 13, 9, 10, 11, 12, 14], 'cur_cost': 17369.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 3, 10, 9, 11, 12, 13, 14, 17, 15, 16, 21, 20, 19, 18, 1, 2, 6, 4, 5, 8], 'cur_cost': 17268.0, 'intermediate_solutions': [{'tour': [0, 17, 11, 4, 5, 6, 7, 8, 21, 15, 16, 18, 19, 20, 10, 2, 3, 13, 9, 1, 14, 12], 'cur_cost': 22601.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 11, 4, 5, 6, 7, 8, 21, 15, 16, 18, 2, 1, 20, 19, 3, 13, 9, 10, 14, 12], 'cur_cost': 17271.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 11, 4, 10, 5, 6, 7, 8, 21, 15, 16, 18, 19, 20, 1, 2, 3, 13, 9, 14, 12], 'cur_cost': 21310.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([20,  2, 10,  8,  4,  6, 11, 18,  3,  5, 16, 21, 13, 15,  7,  1, 12,
       19, 14,  0,  9, 17], dtype=int64), 'cur_cost': 38296.0, 'intermediate_solutions': [{'tour': array([ 7, 17,  1,  2, 19,  5,  0,  9, 11, 13, 18,  3,  8,  6, 15, 16, 14,
        4, 10, 21, 12, 20]), 'cur_cost': 34808.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2,  7, 17,  1, 19,  5,  0,  9, 11, 13, 18,  3,  8,  6, 15, 16, 14,
        4, 10, 21, 12, 20]), 'cur_cost': 36854.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([19,  2,  7, 17,  1,  5,  0,  9, 11, 13, 18,  3,  8,  6, 15, 16, 14,
        4, 10, 21, 12, 20]), 'cur_cost': 36461.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 1,  2,  7, 17, 19,  5,  0,  9, 11, 13, 18,  3,  8,  6, 15, 16, 14,
        4, 10, 21, 12, 20]), 'cur_cost': 34457.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 1, 19,  2,  7, 17,  5,  0,  9, 11, 13, 18,  3,  8,  6, 15, 16, 14,
        4, 10, 21, 12, 20]), 'cur_cost': 36803.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [6, 1, 2, 7, 13, 20, 18, 21, 9, 19, 17, 14, 15, 12, 0, 5, 8, 4, 10, 11, 16, 3], 'cur_cost': 33185.0, 'intermediate_solutions': [{'tour': [21, 0, 19, 13, 11, 7, 17, 3, 14, 16, 15, 20, 1, 9, 18, 10, 8, 5, 6, 4, 2, 12], 'cur_cost': 31914.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [21, 0, 19, 13, 11, 2, 4, 6, 5, 8, 7, 18, 9, 1, 20, 15, 16, 14, 3, 17, 10, 12], 'cur_cost': 25686.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [21, 0, 19, 13, 11, 10, 17, 3, 14, 12, 16, 15, 20, 1, 9, 18, 7, 8, 5, 6, 4, 2], 'cur_cost': 22901.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [16, 15, 19, 1, 2, 14, 11, 10, 21, 3, 18, 12, 13, 20, 17, 7, 4, 5, 6, 8, 0, 9], 'cur_cost': 20536.0, 'intermediate_solutions': [{'tour': [12, 4, 7, 6, 5, 14, 9, 16, 20, 13, 2, 1, 21, 8, 11, 15, 10, 3, 19, 18, 0, 17], 'cur_cost': 29825.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [12, 4, 7, 6, 5, 14, 9, 16, 20, 13, 2, 1, 18, 8, 11, 15, 10, 3, 19, 21, 17, 0], 'cur_cost': 29622.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [12, 4, 7, 6, 5, 14, 9, 16, 20, 13, 2, 1, 18, 8, 11, 15, 10, 3, 19, 21, 0, 17], 'cur_cost': 29936.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([13,  7,  1,  3, 12, 20,  8, 14, 10,  6, 21, 11, 18,  0,  2,  4, 16,
       19,  5,  9, 17, 15], dtype=int64), 'cur_cost': 38492.0, 'intermediate_solutions': [{'tour': array([18,  7, 12,  1,  9, 17, 10, 19, 15,  2, 16,  4, 11,  5,  0, 21, 14,
        3,  6,  8, 20, 13]), 'cur_cost': 42300.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1, 18,  7, 12,  9, 17, 10, 19, 15,  2, 16,  4, 11,  5,  0, 21, 14,
        3,  6,  8, 20, 13]), 'cur_cost': 39525.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9,  1, 18,  7, 12, 17, 10, 19, 15,  2, 16,  4, 11,  5,  0, 21, 14,
        3,  6,  8, 20, 13]), 'cur_cost': 39549.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([12,  1, 18,  7,  9, 17, 10, 19, 15,  2, 16,  4, 11,  5,  0, 21, 14,
        3,  6,  8, 20, 13]), 'cur_cost': 39606.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([12,  9,  1, 18,  7, 17, 10, 19, 15,  2, 16,  4, 11,  5,  0, 21, 14,
        3,  6,  8, 20, 13]), 'cur_cost': 36917.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 1, 4, 9, 10, 11, 12, 13, 14, 17, 15, 16, 18, 19, 20, 3, 2, 7, 6, 5, 8], 'cur_cost': 17781.0, 'intermediate_solutions': [{'tour': [21, 8, 17, 20, 4, 6, 14, 12, 13, 10, 18, 16, 1, 9, 7, 5, 11, 3, 2, 0, 15, 19], 'cur_cost': 25425.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [21, 8, 17, 11, 4, 6, 14, 12, 13, 10, 18, 16, 1, 9, 7, 5, 20, 3, 2, 19, 15, 0], 'cur_cost': 28160.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [21, 8, 17, 11, 4, 6, 14, 12, 13, 10, 16, 1, 9, 7, 5, 20, 3, 2, 0, 15, 19, 18], 'cur_cost': 25715.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:49,946 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:49,946 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:49,947 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=17268.000, 多样性=0.935
2025-08-05 10:28:49,947 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 10:28:49,947 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 10:28:49,947 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:49,948 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.14902607187565475, 'best_improvement': -0.4168034131933049}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.03579418344519027}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.014083838305997501, 'recent_improvements': [-0.04687194726164887, -0.11912923982349037, -0.01870427064965388], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 12, 'new_count': 12, 'count_change': 0, 'old_best_cost': 9455.0, 'new_best_cost': 9455.0, 'quality_improvement': 0.0, 'old_diversity': 0.6150137741046833, 'new_diversity': 0.6150137741046833, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 10:28:49,949 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 10:28:49,949 - __main__ - INFO - composite3_22 开始进化第 3 代
2025-08-05 10:28:49,949 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 10:28:49,949 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:49,950 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=17268.000, 多样性=0.935
2025-08-05 10:28:49,950 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:49,951 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.935
2025-08-05 10:28:49,951 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:49,954 - EliteExpert - INFO - 精英解分析完成: 精英解数量=12, 多样性=0.615
2025-08-05 10:28:49,956 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 10:28:49,956 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:49,956 - LandscapeExpert - INFO - 添加精英解数据: 12个精英解
2025-08-05 10:28:49,957 - LandscapeExpert - INFO - 数据提取成功: 22个路径, 22个适应度值
2025-08-05 10:28:49,992 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.455, 适应度梯度: -4083.227, 聚类评分: 0.000, 覆盖率: 0.088, 收敛趋势: 0.000, 多样性: 0.842
2025-08-05 10:28:49,992 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 10:28:49,992 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:49,992 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite3_22
2025-08-05 10:28:50,000 - visualization.landscape_visualizer - INFO - 插值约束: 11 个点被约束到最小值 9455.00
2025-08-05 10:28:50,002 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=6.1%, 梯度: 1075.89 → 1010.78
2025-08-05 10:28:50,129 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite3_22\landscape_composite3_22_iter_73_20250805_102850.html
2025-08-05 10:28:50,195 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite3_22\dashboard_composite3_22_iter_73_20250805_102850.html
2025-08-05 10:28:50,195 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 73
2025-08-05 10:28:50,195 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 10:28:50,195 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2393秒
2025-08-05 10:28:50,195 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.45454545454545453, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -4083.227272727272, 'local_optima_density': 0.45454545454545453, 'gradient_variance': 80372449.26652895, 'cluster_count': 0}, 'population_state': {'diversity': 0.8417945690672964, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0877, 'fitness_entropy': 0.7501894679831699, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -4083.227)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.088)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.842)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360929.992693, 'performance_metrics': {}}}
2025-08-05 10:28:50,195 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:50,196 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:50,196 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:50,196 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:50,196 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:28:50,196 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 10:28:50,196 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:28:50,196 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:50,197 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:50,197 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:28:50,197 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:50,197 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:50,197 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 4} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:50,197 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:50,197 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:50,197 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:50,198 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 10:28:50,198 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,198 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,198 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,198 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,198 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20278.0, 路径长度: 22, 收集中间解: 3
2025-08-05 10:28:50,199 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [1, 5, 0, 11, 4, 6, 7, 8, 21, 15, 16, 17, 18, 19, 20, 3, 2, 13, 9, 10, 14, 12], 'cur_cost': 20278.0, 'intermediate_solutions': [{'tour': [6, 21, 0, 1, 2, 20, 18, 15, 16, 9, 17, 3, 19, 13, 11, 14, 7, 8, 4, 5, 12, 10], 'cur_cost': 22040.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 21, 0, 1, 2, 20, 18, 15, 16, 13, 9, 3, 17, 19, 11, 14, 7, 8, 4, 5, 12, 10], 'cur_cost': 21741.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 21, 0, 1, 20, 18, 15, 16, 19, 17, 3, 9, 13, 11, 2, 14, 7, 8, 4, 5, 12, 10], 'cur_cost': 23528.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:50,199 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 20278.00)
2025-08-05 10:28:50,199 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:50,199 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:50,199 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:50,199 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 22
2025-08-05 10:28:50,199 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,200 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,200 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,200 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,200 - ExplorationExpert - INFO - 探索路径生成完成，成本: 23024.0, 路径长度: 22, 收集中间解: 3
2025-08-05 10:28:50,200 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [1, 4, 0, 21, 6, 5, 7, 8, 16, 15, 20, 17, 19, 2, 11, 3, 13, 9, 14, 10, 12, 18], 'cur_cost': 23024.0, 'intermediate_solutions': [{'tour': [0, 4, 3, 7, 6, 5, 8, 21, 15, 16, 17, 1, 19, 20, 18, 2, 13, 9, 10, 11, 12, 14], 'cur_cost': 19830.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 3, 7, 6, 5, 8, 21, 15, 16, 17, 18, 19, 20, 1, 2, 13, 10, 9, 11, 12, 14], 'cur_cost': 17409.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 3, 7, 6, 5, 8, 21, 15, 16, 18, 19, 20, 1, 2, 13, 9, 10, 11, 12, 14, 17], 'cur_cost': 17781.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:50,200 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 23024.00)
2025-08-05 10:28:50,200 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:50,201 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:50,201 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:50,202 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 22
2025-08-05 10:28:50,202 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,202 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,202 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,202 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,202 - ExplorationExpert - INFO - 探索路径生成完成，成本: 31601.0, 路径长度: 22, 收集中间解: 3
2025-08-05 10:28:50,202 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [12, 17, 20, 15, 3, 16, 14, 19, 21, 0, 9, 18, 13, 2, 7, 6, 8, 5, 1, 10, 11, 4], 'cur_cost': 31601.0, 'intermediate_solutions': [{'tour': [20, 1, 2, 21, 13, 16, 19, 3, 9, 10, 17, 14, 15, 12, 0, 18, 7, 4, 8, 5, 6, 11], 'cur_cost': 26644.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [17, 1, 2, 21, 13, 16, 19, 3, 9, 10, 20, 14, 15, 12, 0, 18, 7, 4, 8, 11, 6, 5], 'cur_cost': 30369.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 17, 1, 2, 21, 13, 16, 19, 3, 9, 10, 20, 14, 15, 12, 0, 18, 7, 4, 5, 6, 11], 'cur_cost': 30318.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:50,203 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 31601.00)
2025-08-05 10:28:50,203 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:50,203 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:50,203 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:50,203 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 10:28:50,203 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,204 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,204 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,204 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,204 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12186.0, 路径长度: 22, 收集中间解: 3
2025-08-05 10:28:50,204 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 20, 3, 2, 19, 15, 16, 17, 18, 21, 13, 9, 10, 11, 12, 14, 7, 4, 5, 6, 8], 'cur_cost': 12186.0, 'intermediate_solutions': [{'tour': [9, 12, 10, 13, 16, 17, 15, 1, 19, 20, 18, 3, 14, 2, 11, 0, 7, 4, 5, 6, 8, 21], 'cur_cost': 22133.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 12, 10, 13, 19, 17, 15, 1, 16, 20, 18, 3, 14, 2, 11, 0, 7, 4, 5, 6, 21, 8], 'cur_cost': 25775.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 10, 13, 12, 19, 17, 15, 1, 16, 20, 18, 3, 14, 2, 11, 0, 7, 4, 5, 6, 8, 21], 'cur_cost': 22312.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:50,204 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 12186.00)
2025-08-05 10:28:50,204 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:50,205 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:50,205 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:50,205 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 10:28:50,206 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,206 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,206 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,206 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,206 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11655.0, 路径长度: 22, 收集中间解: 3
2025-08-05 10:28:50,206 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 2, 7, 4, 5, 6, 8, 21, 15, 16, 17, 18, 19, 20, 1, 3, 13, 9, 10, 11, 12, 14], 'cur_cost': 11655.0, 'intermediate_solutions': [{'tour': [0, 7, 3, 10, 9, 11, 12, 1, 14, 17, 15, 16, 21, 20, 19, 18, 13, 2, 6, 4, 5, 8], 'cur_cost': 24784.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 3, 13, 12, 11, 9, 10, 14, 17, 15, 16, 21, 20, 19, 18, 1, 2, 6, 4, 5, 8], 'cur_cost': 17211.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 3, 10, 9, 11, 12, 13, 14, 17, 15, 16, 21, 20, 19, 18, 1, 2, 6, 4, 5, 8], 'cur_cost': 17268.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:50,207 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 11655.00)
2025-08-05 10:28:50,207 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:28:50,207 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:50,207 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:50,207 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 30845.0
2025-08-05 10:28:50,216 - ExploitationExpert - INFO - res_population_num: 14
2025-08-05 10:28:50,216 - ExploitationExpert - INFO - res_population_costs: [9455.0, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455.0, 9455, 9473.0, 9473, 9455, 9455]
2025-08-05 10:28:50,216 - ExploitationExpert - INFO - res_populations: [array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13,  9, 14, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 16, 15, 20, 19, 18, 17, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10, 11,  9, 12,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64)]
2025-08-05 10:28:50,219 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:50,219 - ExploitationExpert - INFO - populations: [{'tour': [1, 5, 0, 11, 4, 6, 7, 8, 21, 15, 16, 17, 18, 19, 20, 3, 2, 13, 9, 10, 14, 12], 'cur_cost': 20278.0}, {'tour': [1, 4, 0, 21, 6, 5, 7, 8, 16, 15, 20, 17, 19, 2, 11, 3, 13, 9, 14, 10, 12, 18], 'cur_cost': 23024.0}, {'tour': [12, 17, 20, 15, 3, 16, 14, 19, 21, 0, 9, 18, 13, 2, 7, 6, 8, 5, 1, 10, 11, 4], 'cur_cost': 31601.0}, {'tour': [0, 1, 20, 3, 2, 19, 15, 16, 17, 18, 21, 13, 9, 10, 11, 12, 14, 7, 4, 5, 6, 8], 'cur_cost': 12186.0}, {'tour': [0, 2, 7, 4, 5, 6, 8, 21, 15, 16, 17, 18, 19, 20, 1, 3, 13, 9, 10, 11, 12, 14], 'cur_cost': 11655.0}, {'tour': array([11, 19, 18,  2, 13, 20, 10,  0,  3,  6,  7,  5, 21,  8,  9, 14, 12,
        4, 16, 17,  1, 15], dtype=int64), 'cur_cost': 30845.0}, {'tour': [6, 1, 2, 7, 13, 20, 18, 21, 9, 19, 17, 14, 15, 12, 0, 5, 8, 4, 10, 11, 16, 3], 'cur_cost': 33185.0}, {'tour': [16, 15, 19, 1, 2, 14, 11, 10, 21, 3, 18, 12, 13, 20, 17, 7, 4, 5, 6, 8, 0, 9], 'cur_cost': 20536.0}, {'tour': [13, 7, 1, 3, 12, 20, 8, 14, 10, 6, 21, 11, 18, 0, 2, 4, 16, 19, 5, 9, 17, 15], 'cur_cost': 38492.0}, {'tour': [0, 21, 1, 4, 9, 10, 11, 12, 13, 14, 17, 15, 16, 18, 19, 20, 3, 2, 7, 6, 5, 8], 'cur_cost': 17781.0}]
2025-08-05 10:28:50,220 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:50,220 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 187, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 187, 'cache_hits': 0, 'similarity_calculations': 820, 'cache_hit_rate': 0.0, 'cache_size': 820}}
2025-08-05 10:28:50,220 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([11, 19, 18,  2, 13, 20, 10,  0,  3,  6,  7,  5, 21,  8,  9, 14, 12,
        4, 16, 17,  1, 15], dtype=int64), 'cur_cost': 30845.0, 'intermediate_solutions': [{'tour': array([10,  2, 20,  8,  4,  6, 11, 18,  3,  5, 16, 21, 13, 15,  7,  1, 12,
       19, 14,  0,  9, 17]), 'cur_cost': 38585.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 8, 10,  2, 20,  4,  6, 11, 18,  3,  5, 16, 21, 13, 15,  7,  1, 12,
       19, 14,  0,  9, 17]), 'cur_cost': 42198.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 4,  8, 10,  2, 20,  6, 11, 18,  3,  5, 16, 21, 13, 15,  7,  1, 12,
       19, 14,  0,  9, 17]), 'cur_cost': 42227.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([20,  8, 10,  2,  4,  6, 11, 18,  3,  5, 16, 21, 13, 15,  7,  1, 12,
       19, 14,  0,  9, 17]), 'cur_cost': 41826.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([20,  4,  8, 10,  2,  6, 11, 18,  3,  5, 16, 21, 13, 15,  7,  1, 12,
       19, 14,  0,  9, 17]), 'cur_cost': 41838.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:50,220 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 30845.00)
2025-08-05 10:28:50,220 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:28:50,221 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:50,221 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:50,221 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 38461.0
2025-08-05 10:28:50,229 - ExploitationExpert - INFO - res_population_num: 16
2025-08-05 10:28:50,230 - ExploitationExpert - INFO - res_population_costs: [9455.0, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455.0, 9455, 9473.0, 9473, 9455, 9455, 9455.0, 9455]
2025-08-05 10:28:50,230 - ExploitationExpert - INFO - res_populations: [array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13,  9, 14, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 16, 15, 20, 19, 18, 17, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10, 11,  9, 12,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64)]
2025-08-05 10:28:50,233 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:50,233 - ExploitationExpert - INFO - populations: [{'tour': [1, 5, 0, 11, 4, 6, 7, 8, 21, 15, 16, 17, 18, 19, 20, 3, 2, 13, 9, 10, 14, 12], 'cur_cost': 20278.0}, {'tour': [1, 4, 0, 21, 6, 5, 7, 8, 16, 15, 20, 17, 19, 2, 11, 3, 13, 9, 14, 10, 12, 18], 'cur_cost': 23024.0}, {'tour': [12, 17, 20, 15, 3, 16, 14, 19, 21, 0, 9, 18, 13, 2, 7, 6, 8, 5, 1, 10, 11, 4], 'cur_cost': 31601.0}, {'tour': [0, 1, 20, 3, 2, 19, 15, 16, 17, 18, 21, 13, 9, 10, 11, 12, 14, 7, 4, 5, 6, 8], 'cur_cost': 12186.0}, {'tour': [0, 2, 7, 4, 5, 6, 8, 21, 15, 16, 17, 18, 19, 20, 1, 3, 13, 9, 10, 11, 12, 14], 'cur_cost': 11655.0}, {'tour': array([11, 19, 18,  2, 13, 20, 10,  0,  3,  6,  7,  5, 21,  8,  9, 14, 12,
        4, 16, 17,  1, 15], dtype=int64), 'cur_cost': 30845.0}, {'tour': array([ 5,  0, 12, 18, 20, 11, 13,  4,  1, 10,  6, 21, 17, 16, 19,  7,  9,
        3, 14, 15,  2,  8], dtype=int64), 'cur_cost': 38461.0}, {'tour': [16, 15, 19, 1, 2, 14, 11, 10, 21, 3, 18, 12, 13, 20, 17, 7, 4, 5, 6, 8, 0, 9], 'cur_cost': 20536.0}, {'tour': [13, 7, 1, 3, 12, 20, 8, 14, 10, 6, 21, 11, 18, 0, 2, 4, 16, 19, 5, 9, 17, 15], 'cur_cost': 38492.0}, {'tour': [0, 21, 1, 4, 9, 10, 11, 12, 13, 14, 17, 15, 16, 18, 19, 20, 3, 2, 7, 6, 5, 8], 'cur_cost': 17781.0}]
2025-08-05 10:28:50,234 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:50,234 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 188, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 188, 'cache_hits': 0, 'similarity_calculations': 825, 'cache_hit_rate': 0.0, 'cache_size': 825}}
2025-08-05 10:28:50,235 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([ 5,  0, 12, 18, 20, 11, 13,  4,  1, 10,  6, 21, 17, 16, 19,  7,  9,
        3, 14, 15,  2,  8], dtype=int64), 'cur_cost': 38461.0, 'intermediate_solutions': [{'tour': array([ 2,  1,  6,  7, 13, 20, 18, 21,  9, 19, 17, 14, 15, 12,  0,  5,  8,
        4, 10, 11, 16,  3]), 'cur_cost': 27610.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 7,  2,  1,  6, 13, 20, 18, 21,  9, 19, 17, 14, 15, 12,  0,  5,  8,
        4, 10, 11, 16,  3]), 'cur_cost': 33201.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([13,  7,  2,  1,  6, 20, 18, 21,  9, 19, 17, 14, 15, 12,  0,  5,  8,
        4, 10, 11, 16,  3]), 'cur_cost': 33093.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6,  7,  2,  1, 13, 20, 18, 21,  9, 19, 17, 14, 15, 12,  0,  5,  8,
        4, 10, 11, 16,  3]), 'cur_cost': 29693.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6, 13,  7,  2,  1, 20, 18, 21,  9, 19, 17, 14, 15, 12,  0,  5,  8,
        4, 10, 11, 16,  3]), 'cur_cost': 33175.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:50,235 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 38461.00)
2025-08-05 10:28:50,235 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:50,235 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:50,235 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:50,237 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 22
2025-08-05 10:28:50,237 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,237 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,238 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,238 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,238 - ExplorationExpert - INFO - 探索路径生成完成，成本: 23055.0, 路径长度: 22, 收集中间解: 3
2025-08-05 10:28:50,238 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [12, 14, 9, 21, 0, 20, 3, 17, 13, 15, 16, 19, 1, 11, 2, 7, 8, 4, 6, 5, 18, 10], 'cur_cost': 23055.0, 'intermediate_solutions': [{'tour': [16, 15, 19, 17, 2, 14, 11, 10, 21, 3, 18, 12, 13, 20, 1, 7, 4, 5, 6, 8, 0, 9], 'cur_cost': 22590.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [16, 15, 19, 1, 10, 11, 14, 2, 21, 3, 18, 12, 13, 20, 17, 7, 4, 5, 6, 8, 0, 9], 'cur_cost': 22619.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [16, 15, 19, 2, 14, 11, 1, 10, 21, 3, 18, 12, 13, 20, 17, 7, 4, 5, 6, 8, 0, 9], 'cur_cost': 25453.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:50,238 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 23055.00)
2025-08-05 10:28:50,238 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:50,238 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:50,239 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:50,239 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 40069.0
2025-08-05 10:28:50,246 - ExploitationExpert - INFO - res_population_num: 16
2025-08-05 10:28:50,247 - ExploitationExpert - INFO - res_population_costs: [9455.0, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455.0, 9455, 9473.0, 9473, 9455, 9455, 9455.0, 9455]
2025-08-05 10:28:50,247 - ExploitationExpert - INFO - res_populations: [array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13,  9, 14, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 16, 15, 20, 19, 18, 17, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10, 11,  9, 12,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64)]
2025-08-05 10:28:50,250 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:50,250 - ExploitationExpert - INFO - populations: [{'tour': [1, 5, 0, 11, 4, 6, 7, 8, 21, 15, 16, 17, 18, 19, 20, 3, 2, 13, 9, 10, 14, 12], 'cur_cost': 20278.0}, {'tour': [1, 4, 0, 21, 6, 5, 7, 8, 16, 15, 20, 17, 19, 2, 11, 3, 13, 9, 14, 10, 12, 18], 'cur_cost': 23024.0}, {'tour': [12, 17, 20, 15, 3, 16, 14, 19, 21, 0, 9, 18, 13, 2, 7, 6, 8, 5, 1, 10, 11, 4], 'cur_cost': 31601.0}, {'tour': [0, 1, 20, 3, 2, 19, 15, 16, 17, 18, 21, 13, 9, 10, 11, 12, 14, 7, 4, 5, 6, 8], 'cur_cost': 12186.0}, {'tour': [0, 2, 7, 4, 5, 6, 8, 21, 15, 16, 17, 18, 19, 20, 1, 3, 13, 9, 10, 11, 12, 14], 'cur_cost': 11655.0}, {'tour': array([11, 19, 18,  2, 13, 20, 10,  0,  3,  6,  7,  5, 21,  8,  9, 14, 12,
        4, 16, 17,  1, 15], dtype=int64), 'cur_cost': 30845.0}, {'tour': array([ 5,  0, 12, 18, 20, 11, 13,  4,  1, 10,  6, 21, 17, 16, 19,  7,  9,
        3, 14, 15,  2,  8], dtype=int64), 'cur_cost': 38461.0}, {'tour': [12, 14, 9, 21, 0, 20, 3, 17, 13, 15, 16, 19, 1, 11, 2, 7, 8, 4, 6, 5, 18, 10], 'cur_cost': 23055.0}, {'tour': array([ 2, 13, 14, 17, 15,  7,  1, 11,  6, 10, 20,  0, 16, 18,  3,  5, 21,
        8, 19,  4, 12,  9], dtype=int64), 'cur_cost': 40069.0}, {'tour': [0, 21, 1, 4, 9, 10, 11, 12, 13, 14, 17, 15, 16, 18, 19, 20, 3, 2, 7, 6, 5, 8], 'cur_cost': 17781.0}]
2025-08-05 10:28:50,251 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:50,251 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 189, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 189, 'cache_hits': 0, 'similarity_calculations': 831, 'cache_hit_rate': 0.0, 'cache_size': 831}}
2025-08-05 10:28:50,252 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 2, 13, 14, 17, 15,  7,  1, 11,  6, 10, 20,  0, 16, 18,  3,  5, 21,
        8, 19,  4, 12,  9], dtype=int64), 'cur_cost': 40069.0, 'intermediate_solutions': [{'tour': array([ 1,  7, 13,  3, 12, 20,  8, 14, 10,  6, 21, 11, 18,  0,  2,  4, 16,
       19,  5,  9, 17, 15]), 'cur_cost': 40491.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 3,  1,  7, 13, 12, 20,  8, 14, 10,  6, 21, 11, 18,  0,  2,  4, 16,
       19,  5,  9, 17, 15]), 'cur_cost': 35813.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([12,  3,  1,  7, 13, 20,  8, 14, 10,  6, 21, 11, 18,  0,  2,  4, 16,
       19,  5,  9, 17, 15]), 'cur_cost': 38493.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([13,  3,  1,  7, 12, 20,  8, 14, 10,  6, 21, 11, 18,  0,  2,  4, 16,
       19,  5,  9, 17, 15]), 'cur_cost': 38451.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([13, 12,  3,  1,  7, 20,  8, 14, 10,  6, 21, 11, 18,  0,  2,  4, 16,
       19,  5,  9, 17, 15]), 'cur_cost': 35732.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:50,252 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 40069.00)
2025-08-05 10:28:50,252 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:50,252 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:50,252 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:50,253 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 10:28:50,253 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,253 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,253 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,253 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,254 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12081.0, 路径长度: 22, 收集中间解: 3
2025-08-05 10:28:50,254 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 18, 12, 13, 9, 10, 11, 14, 17, 15, 16, 21, 20, 19, 1, 2, 3, 7, 4, 5, 6, 8], 'cur_cost': 12081.0, 'intermediate_solutions': [{'tour': [0, 21, 1, 4, 9, 10, 11, 12, 15, 14, 17, 13, 16, 18, 19, 20, 3, 2, 7, 6, 5, 8], 'cur_cost': 23850.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 1, 4, 9, 10, 11, 12, 13, 14, 17, 15, 16, 7, 2, 3, 20, 19, 18, 6, 5, 8], 'cur_cost': 21695.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 1, 9, 10, 11, 12, 13, 14, 4, 17, 15, 16, 18, 19, 20, 3, 2, 7, 6, 5, 8], 'cur_cost': 17909.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:50,254 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 12081.00)
2025-08-05 10:28:50,254 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:50,254 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:50,256 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [1, 5, 0, 11, 4, 6, 7, 8, 21, 15, 16, 17, 18, 19, 20, 3, 2, 13, 9, 10, 14, 12], 'cur_cost': 20278.0, 'intermediate_solutions': [{'tour': [6, 21, 0, 1, 2, 20, 18, 15, 16, 9, 17, 3, 19, 13, 11, 14, 7, 8, 4, 5, 12, 10], 'cur_cost': 22040.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 21, 0, 1, 2, 20, 18, 15, 16, 13, 9, 3, 17, 19, 11, 14, 7, 8, 4, 5, 12, 10], 'cur_cost': 21741.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 21, 0, 1, 20, 18, 15, 16, 19, 17, 3, 9, 13, 11, 2, 14, 7, 8, 4, 5, 12, 10], 'cur_cost': 23528.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 0, 21, 6, 5, 7, 8, 16, 15, 20, 17, 19, 2, 11, 3, 13, 9, 14, 10, 12, 18], 'cur_cost': 23024.0, 'intermediate_solutions': [{'tour': [0, 4, 3, 7, 6, 5, 8, 21, 15, 16, 17, 1, 19, 20, 18, 2, 13, 9, 10, 11, 12, 14], 'cur_cost': 19830.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 3, 7, 6, 5, 8, 21, 15, 16, 17, 18, 19, 20, 1, 2, 13, 10, 9, 11, 12, 14], 'cur_cost': 17409.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 3, 7, 6, 5, 8, 21, 15, 16, 18, 19, 20, 1, 2, 13, 9, 10, 11, 12, 14, 17], 'cur_cost': 17781.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [12, 17, 20, 15, 3, 16, 14, 19, 21, 0, 9, 18, 13, 2, 7, 6, 8, 5, 1, 10, 11, 4], 'cur_cost': 31601.0, 'intermediate_solutions': [{'tour': [20, 1, 2, 21, 13, 16, 19, 3, 9, 10, 17, 14, 15, 12, 0, 18, 7, 4, 8, 5, 6, 11], 'cur_cost': 26644.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [17, 1, 2, 21, 13, 16, 19, 3, 9, 10, 20, 14, 15, 12, 0, 18, 7, 4, 8, 11, 6, 5], 'cur_cost': 30369.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 17, 1, 2, 21, 13, 16, 19, 3, 9, 10, 20, 14, 15, 12, 0, 18, 7, 4, 5, 6, 11], 'cur_cost': 30318.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 20, 3, 2, 19, 15, 16, 17, 18, 21, 13, 9, 10, 11, 12, 14, 7, 4, 5, 6, 8], 'cur_cost': 12186.0, 'intermediate_solutions': [{'tour': [9, 12, 10, 13, 16, 17, 15, 1, 19, 20, 18, 3, 14, 2, 11, 0, 7, 4, 5, 6, 8, 21], 'cur_cost': 22133.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 12, 10, 13, 19, 17, 15, 1, 16, 20, 18, 3, 14, 2, 11, 0, 7, 4, 5, 6, 21, 8], 'cur_cost': 25775.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 10, 13, 12, 19, 17, 15, 1, 16, 20, 18, 3, 14, 2, 11, 0, 7, 4, 5, 6, 8, 21], 'cur_cost': 22312.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 7, 4, 5, 6, 8, 21, 15, 16, 17, 18, 19, 20, 1, 3, 13, 9, 10, 11, 12, 14], 'cur_cost': 11655.0, 'intermediate_solutions': [{'tour': [0, 7, 3, 10, 9, 11, 12, 1, 14, 17, 15, 16, 21, 20, 19, 18, 13, 2, 6, 4, 5, 8], 'cur_cost': 24784.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 3, 13, 12, 11, 9, 10, 14, 17, 15, 16, 21, 20, 19, 18, 1, 2, 6, 4, 5, 8], 'cur_cost': 17211.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 3, 10, 9, 11, 12, 13, 14, 17, 15, 16, 21, 20, 19, 18, 1, 2, 6, 4, 5, 8], 'cur_cost': 17268.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([11, 19, 18,  2, 13, 20, 10,  0,  3,  6,  7,  5, 21,  8,  9, 14, 12,
        4, 16, 17,  1, 15], dtype=int64), 'cur_cost': 30845.0, 'intermediate_solutions': [{'tour': array([10,  2, 20,  8,  4,  6, 11, 18,  3,  5, 16, 21, 13, 15,  7,  1, 12,
       19, 14,  0,  9, 17]), 'cur_cost': 38585.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 8, 10,  2, 20,  4,  6, 11, 18,  3,  5, 16, 21, 13, 15,  7,  1, 12,
       19, 14,  0,  9, 17]), 'cur_cost': 42198.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 4,  8, 10,  2, 20,  6, 11, 18,  3,  5, 16, 21, 13, 15,  7,  1, 12,
       19, 14,  0,  9, 17]), 'cur_cost': 42227.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([20,  8, 10,  2,  4,  6, 11, 18,  3,  5, 16, 21, 13, 15,  7,  1, 12,
       19, 14,  0,  9, 17]), 'cur_cost': 41826.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([20,  4,  8, 10,  2,  6, 11, 18,  3,  5, 16, 21, 13, 15,  7,  1, 12,
       19, 14,  0,  9, 17]), 'cur_cost': 41838.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 5,  0, 12, 18, 20, 11, 13,  4,  1, 10,  6, 21, 17, 16, 19,  7,  9,
        3, 14, 15,  2,  8], dtype=int64), 'cur_cost': 38461.0, 'intermediate_solutions': [{'tour': array([ 2,  1,  6,  7, 13, 20, 18, 21,  9, 19, 17, 14, 15, 12,  0,  5,  8,
        4, 10, 11, 16,  3]), 'cur_cost': 27610.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 7,  2,  1,  6, 13, 20, 18, 21,  9, 19, 17, 14, 15, 12,  0,  5,  8,
        4, 10, 11, 16,  3]), 'cur_cost': 33201.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([13,  7,  2,  1,  6, 20, 18, 21,  9, 19, 17, 14, 15, 12,  0,  5,  8,
        4, 10, 11, 16,  3]), 'cur_cost': 33093.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6,  7,  2,  1, 13, 20, 18, 21,  9, 19, 17, 14, 15, 12,  0,  5,  8,
        4, 10, 11, 16,  3]), 'cur_cost': 29693.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6, 13,  7,  2,  1, 20, 18, 21,  9, 19, 17, 14, 15, 12,  0,  5,  8,
        4, 10, 11, 16,  3]), 'cur_cost': 33175.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [12, 14, 9, 21, 0, 20, 3, 17, 13, 15, 16, 19, 1, 11, 2, 7, 8, 4, 6, 5, 18, 10], 'cur_cost': 23055.0, 'intermediate_solutions': [{'tour': [16, 15, 19, 17, 2, 14, 11, 10, 21, 3, 18, 12, 13, 20, 1, 7, 4, 5, 6, 8, 0, 9], 'cur_cost': 22590.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [16, 15, 19, 1, 10, 11, 14, 2, 21, 3, 18, 12, 13, 20, 17, 7, 4, 5, 6, 8, 0, 9], 'cur_cost': 22619.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [16, 15, 19, 2, 14, 11, 1, 10, 21, 3, 18, 12, 13, 20, 17, 7, 4, 5, 6, 8, 0, 9], 'cur_cost': 25453.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 2, 13, 14, 17, 15,  7,  1, 11,  6, 10, 20,  0, 16, 18,  3,  5, 21,
        8, 19,  4, 12,  9], dtype=int64), 'cur_cost': 40069.0, 'intermediate_solutions': [{'tour': array([ 1,  7, 13,  3, 12, 20,  8, 14, 10,  6, 21, 11, 18,  0,  2,  4, 16,
       19,  5,  9, 17, 15]), 'cur_cost': 40491.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 3,  1,  7, 13, 12, 20,  8, 14, 10,  6, 21, 11, 18,  0,  2,  4, 16,
       19,  5,  9, 17, 15]), 'cur_cost': 35813.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([12,  3,  1,  7, 13, 20,  8, 14, 10,  6, 21, 11, 18,  0,  2,  4, 16,
       19,  5,  9, 17, 15]), 'cur_cost': 38493.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([13,  3,  1,  7, 12, 20,  8, 14, 10,  6, 21, 11, 18,  0,  2,  4, 16,
       19,  5,  9, 17, 15]), 'cur_cost': 38451.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([13, 12,  3,  1,  7, 20,  8, 14, 10,  6, 21, 11, 18,  0,  2,  4, 16,
       19,  5,  9, 17, 15]), 'cur_cost': 35732.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 12, 13, 9, 10, 11, 14, 17, 15, 16, 21, 20, 19, 1, 2, 3, 7, 4, 5, 6, 8], 'cur_cost': 12081.0, 'intermediate_solutions': [{'tour': [0, 21, 1, 4, 9, 10, 11, 12, 15, 14, 17, 13, 16, 18, 19, 20, 3, 2, 7, 6, 5, 8], 'cur_cost': 23850.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 1, 4, 9, 10, 11, 12, 13, 14, 17, 15, 16, 7, 2, 3, 20, 19, 18, 6, 5, 8], 'cur_cost': 21695.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 1, 9, 10, 11, 12, 13, 14, 4, 17, 15, 16, 18, 19, 20, 3, 2, 7, 6, 5, 8], 'cur_cost': 17909.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:50,256 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:50,256 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:50,258 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=11655.000, 多样性=0.941
2025-08-05 10:28:50,258 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 10:28:50,258 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 10:28:50,258 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:50,259 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.1046073294997182, 'best_improvement': 0.3250521195274496}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.006479481641468605}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.014948416026082175, 'recent_improvements': [-0.11912923982349037, -0.01870427064965388, -0.14902607187565475], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 16, 'new_count': 16, 'count_change': 0, 'old_best_cost': 9455.0, 'new_best_cost': 9455.0, 'quality_improvement': 0.0, 'old_diversity': 0.6727272727272727, 'new_diversity': 0.6727272727272727, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:50,260 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 10:28:50,261 - __main__ - INFO - composite3_22 开始进化第 4 代
2025-08-05 10:28:50,261 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 10:28:50,261 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:50,261 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=11655.000, 多样性=0.941
2025-08-05 10:28:50,261 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:50,263 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.941
2025-08-05 10:28:50,263 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:50,268 - EliteExpert - INFO - 精英解分析完成: 精英解数量=16, 多样性=0.673
2025-08-05 10:28:50,271 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 10:28:50,271 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:50,271 - LandscapeExpert - INFO - 添加精英解数据: 16个精英解
2025-08-05 10:28:50,272 - LandscapeExpert - INFO - 数据提取成功: 26个路径, 26个适应度值
2025-08-05 10:28:50,334 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.538, 适应度梯度: -4125.631, 聚类评分: 0.000, 覆盖率: 0.089, 收敛趋势: 0.000, 多样性: 0.716
2025-08-05 10:28:50,335 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 10:28:50,335 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:50,335 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite3_22
2025-08-05 10:28:50,343 - visualization.landscape_visualizer - INFO - 插值约束: 137 个点被约束到最小值 9455.00
2025-08-05 10:28:50,344 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=8.4%, 梯度: 1087.67 → 996.01
2025-08-05 10:28:50,461 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite3_22\landscape_composite3_22_iter_74_20250805_102850.html
2025-08-05 10:28:50,515 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite3_22\dashboard_composite3_22_iter_74_20250805_102850.html
2025-08-05 10:28:50,515 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 74
2025-08-05 10:28:50,515 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 10:28:50,515 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2441秒
2025-08-05 10:28:50,516 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5384615384615384, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -4125.630769230769, 'local_optima_density': 0.5384615384615384, 'gradient_variance': 70523935.30982248, 'cluster_count': 0}, 'population_state': {'diversity': 0.7159763313609468, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0891, 'fitness_entropy': 0.5579728303717879, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -4125.631)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.089)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.716)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360930.3343081, 'performance_metrics': {}}}
2025-08-05 10:28:50,516 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:50,516 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:50,516 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:50,516 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:50,516 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:28:50,516 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 10:28:50,517 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:28:50,517 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:50,517 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:50,517 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:28:50,517 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:50,517 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:50,517 - experts.management.collaboration_manager - INFO - 识别精英个体: {9, 4} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:50,517 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:50,517 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:50,518 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:50,518 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 10:28:50,518 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,519 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,519 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,519 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,519 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14409.0, 路径长度: 22, 收集中间解: 3
2025-08-05 10:28:50,519 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 14, 9, 7, 4, 5, 6, 8, 21, 15, 16, 17, 18, 19, 20, 1, 2, 3, 13, 12, 11, 10], 'cur_cost': 14409.0, 'intermediate_solutions': [{'tour': [1, 5, 0, 11, 4, 6, 7, 8, 21, 15, 16, 3, 18, 19, 20, 17, 2, 13, 9, 10, 14, 12], 'cur_cost': 22806.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 5, 0, 11, 4, 6, 2, 3, 20, 19, 18, 17, 16, 15, 21, 8, 7, 13, 9, 10, 14, 12], 'cur_cost': 23763.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 5, 0, 11, 4, 6, 7, 8, 21, 15, 16, 17, 18, 19, 20, 3, 14, 2, 13, 9, 10, 12], 'cur_cost': 25015.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:50,520 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 14409.00)
2025-08-05 10:28:50,520 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:50,520 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:50,520 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:50,520 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 10:28:50,520 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,521 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,521 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,521 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,521 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12162.0, 路径长度: 22, 收集中间解: 3
2025-08-05 10:28:50,521 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 17, 11, 9, 10, 14, 13, 12, 16, 15, 18, 19, 20, 21, 3, 2, 7, 4, 5, 6, 8], 'cur_cost': 12162.0, 'intermediate_solutions': [{'tour': [1, 4, 0, 21, 6, 5, 7, 8, 16, 15, 20, 17, 19, 2, 11, 3, 13, 9, 18, 10, 12, 14], 'cur_cost': 25848.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 13, 3, 11, 2, 19, 17, 20, 15, 16, 8, 7, 5, 6, 21, 0, 4, 9, 14, 10, 12, 18], 'cur_cost': 25768.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 0, 21, 6, 5, 7, 8, 15, 20, 17, 19, 2, 11, 3, 13, 9, 14, 10, 12, 16, 18], 'cur_cost': 23064.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:50,521 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 12162.00)
2025-08-05 10:28:50,521 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-05 10:28:50,521 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:50,522 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:50,522 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 41091.0
2025-08-05 10:28:50,531 - ExploitationExpert - INFO - res_population_num: 17
2025-08-05 10:28:50,531 - ExploitationExpert - INFO - res_population_costs: [9455.0, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455, 9455.0, 9455, 9473.0, 9473, 9455.0]
2025-08-05 10:28:50,531 - ExploitationExpert - INFO - res_populations: [array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13,  9, 14, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10, 11,  9, 12,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 16, 15, 20, 19, 18, 17, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64)]
2025-08-05 10:28:50,535 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:50,535 - ExploitationExpert - INFO - populations: [{'tour': [0, 14, 9, 7, 4, 5, 6, 8, 21, 15, 16, 17, 18, 19, 20, 1, 2, 3, 13, 12, 11, 10], 'cur_cost': 14409.0}, {'tour': [0, 1, 17, 11, 9, 10, 14, 13, 12, 16, 15, 18, 19, 20, 21, 3, 2, 7, 4, 5, 6, 8], 'cur_cost': 12162.0}, {'tour': array([ 9, 16,  3, 14, 20, 10,  4, 13, 11,  8, 15,  5, 12, 21, 19,  1,  2,
        7, 17,  6, 18,  0], dtype=int64), 'cur_cost': 41091.0}, {'tour': [0, 1, 20, 3, 2, 19, 15, 16, 17, 18, 21, 13, 9, 10, 11, 12, 14, 7, 4, 5, 6, 8], 'cur_cost': 12186.0}, {'tour': [0, 2, 7, 4, 5, 6, 8, 21, 15, 16, 17, 18, 19, 20, 1, 3, 13, 9, 10, 11, 12, 14], 'cur_cost': 11655.0}, {'tour': [11, 19, 18, 2, 13, 20, 10, 0, 3, 6, 7, 5, 21, 8, 9, 14, 12, 4, 16, 17, 1, 15], 'cur_cost': 30845.0}, {'tour': [5, 0, 12, 18, 20, 11, 13, 4, 1, 10, 6, 21, 17, 16, 19, 7, 9, 3, 14, 15, 2, 8], 'cur_cost': 38461.0}, {'tour': [12, 14, 9, 21, 0, 20, 3, 17, 13, 15, 16, 19, 1, 11, 2, 7, 8, 4, 6, 5, 18, 10], 'cur_cost': 23055.0}, {'tour': [2, 13, 14, 17, 15, 7, 1, 11, 6, 10, 20, 0, 16, 18, 3, 5, 21, 8, 19, 4, 12, 9], 'cur_cost': 40069.0}, {'tour': [0, 18, 12, 13, 9, 10, 11, 14, 17, 15, 16, 21, 20, 19, 1, 2, 3, 7, 4, 5, 6, 8], 'cur_cost': 12081.0}]
2025-08-05 10:28:50,535 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:50,535 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 190, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 190, 'cache_hits': 0, 'similarity_calculations': 838, 'cache_hit_rate': 0.0, 'cache_size': 838}}
2025-08-05 10:28:50,536 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([ 9, 16,  3, 14, 20, 10,  4, 13, 11,  8, 15,  5, 12, 21, 19,  1,  2,
        7, 17,  6, 18,  0], dtype=int64), 'cur_cost': 41091.0, 'intermediate_solutions': [{'tour': array([20, 17, 12, 15,  3, 16, 14, 19, 21,  0,  9, 18, 13,  2,  7,  6,  8,
        5,  1, 10, 11,  4]), 'cur_cost': 31981.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([15, 20, 17, 12,  3, 16, 14, 19, 21,  0,  9, 18, 13,  2,  7,  6,  8,
        5,  1, 10, 11,  4]), 'cur_cost': 31620.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3, 15, 20, 17, 12, 16, 14, 19, 21,  0,  9, 18, 13,  2,  7,  6,  8,
        5,  1, 10, 11,  4]), 'cur_cost': 31585.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([12, 15, 20, 17,  3, 16, 14, 19, 21,  0,  9, 18, 13,  2,  7,  6,  8,
        5,  1, 10, 11,  4]), 'cur_cost': 31657.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([12,  3, 15, 20, 17, 16, 14, 19, 21,  0,  9, 18, 13,  2,  7,  6,  8,
        5,  1, 10, 11,  4]), 'cur_cost': 31217.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:50,536 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 41091.00)
2025-08-05 10:28:50,536 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:50,537 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:50,537 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:50,537 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 10:28:50,537 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,538 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,538 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,538 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,538 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15473.0, 路径长度: 22, 收集中间解: 3
2025-08-05 10:28:50,538 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 5, 2, 3, 1, 19, 15, 16, 17, 18, 20, 21, 13, 9, 10, 11, 12, 14, 7, 4, 6, 8], 'cur_cost': 15473.0, 'intermediate_solutions': [{'tour': [0, 1, 20, 3, 2, 19, 15, 16, 17, 18, 21, 13, 9, 10, 12, 11, 14, 7, 4, 5, 6, 8], 'cur_cost': 12216.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 20, 3, 2, 19, 15, 16, 17, 18, 21, 13, 5, 4, 7, 14, 12, 11, 10, 9, 6, 8], 'cur_cost': 18505.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 20, 15, 3, 2, 19, 16, 17, 18, 21, 13, 9, 10, 11, 12, 14, 7, 4, 5, 6, 8], 'cur_cost': 12241.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:50,538 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 15473.00)
2025-08-05 10:28:50,539 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:50,539 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:50,539 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:50,540 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 22
2025-08-05 10:28:50,540 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,540 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,540 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,540 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,540 - ExplorationExpert - INFO - 探索路径生成完成，成本: 26513.0, 路径长度: 22, 收集中间解: 3
2025-08-05 10:28:50,541 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [11, 18, 0, 17, 2, 19, 16, 1, 21, 3, 12, 9, 15, 20, 7, 4, 5, 6, 13, 14, 10, 8], 'cur_cost': 26513.0, 'intermediate_solutions': [{'tour': [0, 2, 7, 4, 5, 15, 8, 21, 6, 16, 17, 18, 19, 20, 1, 3, 13, 9, 10, 11, 12, 14], 'cur_cost': 19510.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 7, 4, 5, 6, 8, 21, 15, 16, 17, 9, 13, 3, 1, 20, 19, 18, 10, 11, 12, 14], 'cur_cost': 14756.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 15, 7, 4, 5, 6, 8, 21, 16, 17, 18, 19, 20, 1, 3, 13, 9, 10, 11, 12, 14], 'cur_cost': 12065.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:50,541 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 26513.00)
2025-08-05 10:28:50,541 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:50,541 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:50,541 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:50,542 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 22
2025-08-05 10:28:50,542 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,542 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,542 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,543 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,543 - ExplorationExpert - INFO - 探索路径生成完成，成本: 18839.0, 路径长度: 22, 收集中间解: 3
2025-08-05 10:28:50,543 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [9, 14, 12, 18, 15, 3, 2, 0, 19, 16, 1, 17, 13, 20, 11, 10, 21, 7, 4, 8, 5, 6], 'cur_cost': 18839.0, 'intermediate_solutions': [{'tour': [11, 19, 18, 2, 13, 20, 10, 0, 3, 6, 7, 5, 21, 8, 9, 14, 15, 4, 16, 17, 1, 12], 'cur_cost': 30838.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 19, 18, 2, 13, 20, 10, 21, 5, 7, 6, 3, 0, 8, 9, 14, 12, 4, 16, 17, 1, 15], 'cur_cost': 30932.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 19, 18, 2, 13, 10, 0, 3, 6, 7, 5, 21, 8, 9, 14, 12, 4, 20, 16, 17, 1, 15], 'cur_cost': 27703.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:50,543 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 18839.00)
2025-08-05 10:28:50,543 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:28:50,543 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:50,544 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:50,544 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 39896.0
2025-08-05 10:28:50,553 - ExploitationExpert - INFO - res_population_num: 18
2025-08-05 10:28:50,553 - ExploitationExpert - INFO - res_population_costs: [9455.0, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455, 9455.0, 9455, 9473.0, 9473, 9455.0, 9455.0]
2025-08-05 10:28:50,553 - ExploitationExpert - INFO - res_populations: [array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13,  9, 14, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10, 11,  9, 12,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 16, 15, 20, 19, 18, 17, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64)]
2025-08-05 10:28:50,556 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:50,556 - ExploitationExpert - INFO - populations: [{'tour': [0, 14, 9, 7, 4, 5, 6, 8, 21, 15, 16, 17, 18, 19, 20, 1, 2, 3, 13, 12, 11, 10], 'cur_cost': 14409.0}, {'tour': [0, 1, 17, 11, 9, 10, 14, 13, 12, 16, 15, 18, 19, 20, 21, 3, 2, 7, 4, 5, 6, 8], 'cur_cost': 12162.0}, {'tour': array([ 9, 16,  3, 14, 20, 10,  4, 13, 11,  8, 15,  5, 12, 21, 19,  1,  2,
        7, 17,  6, 18,  0], dtype=int64), 'cur_cost': 41091.0}, {'tour': [0, 5, 2, 3, 1, 19, 15, 16, 17, 18, 20, 21, 13, 9, 10, 11, 12, 14, 7, 4, 6, 8], 'cur_cost': 15473.0}, {'tour': [11, 18, 0, 17, 2, 19, 16, 1, 21, 3, 12, 9, 15, 20, 7, 4, 5, 6, 13, 14, 10, 8], 'cur_cost': 26513.0}, {'tour': [9, 14, 12, 18, 15, 3, 2, 0, 19, 16, 1, 17, 13, 20, 11, 10, 21, 7, 4, 8, 5, 6], 'cur_cost': 18839.0}, {'tour': array([19,  3, 20,  5,  1, 17, 12,  6,  9,  2,  4, 18, 16, 14, 10,  0,  8,
       21, 15, 11, 13,  7], dtype=int64), 'cur_cost': 39896.0}, {'tour': [12, 14, 9, 21, 0, 20, 3, 17, 13, 15, 16, 19, 1, 11, 2, 7, 8, 4, 6, 5, 18, 10], 'cur_cost': 23055.0}, {'tour': [2, 13, 14, 17, 15, 7, 1, 11, 6, 10, 20, 0, 16, 18, 3, 5, 21, 8, 19, 4, 12, 9], 'cur_cost': 40069.0}, {'tour': [0, 18, 12, 13, 9, 10, 11, 14, 17, 15, 16, 21, 20, 19, 1, 2, 3, 7, 4, 5, 6, 8], 'cur_cost': 12081.0}]
2025-08-05 10:28:50,557 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:50,557 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 191, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 191, 'cache_hits': 0, 'similarity_calculations': 846, 'cache_hit_rate': 0.0, 'cache_size': 846}}
2025-08-05 10:28:50,558 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([19,  3, 20,  5,  1, 17, 12,  6,  9,  2,  4, 18, 16, 14, 10,  0,  8,
       21, 15, 11, 13,  7], dtype=int64), 'cur_cost': 39896.0, 'intermediate_solutions': [{'tour': array([12,  0,  5, 18, 20, 11, 13,  4,  1, 10,  6, 21, 17, 16, 19,  7,  9,
        3, 14, 15,  2,  8]), 'cur_cost': 42064.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([18, 12,  0,  5, 20, 11, 13,  4,  1, 10,  6, 21, 17, 16, 19,  7,  9,
        3, 14, 15,  2,  8]), 'cur_cost': 42411.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([20, 18, 12,  0,  5, 11, 13,  4,  1, 10,  6, 21, 17, 16, 19,  7,  9,
        3, 14, 15,  2,  8]), 'cur_cost': 41948.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 5, 18, 12,  0, 20, 11, 13,  4,  1, 10,  6, 21, 17, 16, 19,  7,  9,
        3, 14, 15,  2,  8]), 'cur_cost': 38832.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 5, 20, 18, 12,  0, 11, 13,  4,  1, 10,  6, 21, 17, 16, 19,  7,  9,
        3, 14, 15,  2,  8]), 'cur_cost': 38404.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:50,558 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 39896.00)
2025-08-05 10:28:50,558 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:50,558 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:50,558 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:50,559 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 22
2025-08-05 10:28:50,559 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,559 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,559 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,560 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,560 - ExplorationExpert - INFO - 探索路径生成完成，成本: 29329.0, 路径长度: 22, 收集中间解: 3
2025-08-05 10:28:50,560 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [11, 7, 4, 5, 6, 1, 12, 15, 9, 18, 20, 10, 2, 14, 13, 19, 21, 16, 17, 8, 3, 0], 'cur_cost': 29329.0, 'intermediate_solutions': [{'tour': [12, 14, 9, 21, 0, 20, 3, 17, 13, 15, 16, 2, 1, 11, 19, 7, 8, 4, 6, 5, 18, 10], 'cur_cost': 21448.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [12, 14, 9, 21, 0, 20, 3, 17, 13, 15, 16, 19, 1, 11, 2, 8, 7, 4, 6, 5, 18, 10], 'cur_cost': 23096.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [12, 14, 9, 21, 0, 13, 20, 3, 17, 15, 16, 19, 1, 11, 2, 7, 8, 4, 6, 5, 18, 10], 'cur_cost': 22777.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:50,560 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 29329.00)
2025-08-05 10:28:50,560 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:50,560 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:50,560 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:50,560 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 31350.0
2025-08-05 10:28:50,572 - ExploitationExpert - INFO - res_population_num: 18
2025-08-05 10:28:50,572 - ExploitationExpert - INFO - res_population_costs: [9455.0, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455, 9455.0, 9455, 9473.0, 9473, 9455.0, 9455.0]
2025-08-05 10:28:50,572 - ExploitationExpert - INFO - res_populations: [array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13,  9, 14, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10, 11,  9, 12,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 16, 15, 20, 19, 18, 17, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64)]
2025-08-05 10:28:50,576 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:50,576 - ExploitationExpert - INFO - populations: [{'tour': [0, 14, 9, 7, 4, 5, 6, 8, 21, 15, 16, 17, 18, 19, 20, 1, 2, 3, 13, 12, 11, 10], 'cur_cost': 14409.0}, {'tour': [0, 1, 17, 11, 9, 10, 14, 13, 12, 16, 15, 18, 19, 20, 21, 3, 2, 7, 4, 5, 6, 8], 'cur_cost': 12162.0}, {'tour': array([ 9, 16,  3, 14, 20, 10,  4, 13, 11,  8, 15,  5, 12, 21, 19,  1,  2,
        7, 17,  6, 18,  0], dtype=int64), 'cur_cost': 41091.0}, {'tour': [0, 5, 2, 3, 1, 19, 15, 16, 17, 18, 20, 21, 13, 9, 10, 11, 12, 14, 7, 4, 6, 8], 'cur_cost': 15473.0}, {'tour': [11, 18, 0, 17, 2, 19, 16, 1, 21, 3, 12, 9, 15, 20, 7, 4, 5, 6, 13, 14, 10, 8], 'cur_cost': 26513.0}, {'tour': [9, 14, 12, 18, 15, 3, 2, 0, 19, 16, 1, 17, 13, 20, 11, 10, 21, 7, 4, 8, 5, 6], 'cur_cost': 18839.0}, {'tour': array([19,  3, 20,  5,  1, 17, 12,  6,  9,  2,  4, 18, 16, 14, 10,  0,  8,
       21, 15, 11, 13,  7], dtype=int64), 'cur_cost': 39896.0}, {'tour': [11, 7, 4, 5, 6, 1, 12, 15, 9, 18, 20, 10, 2, 14, 13, 19, 21, 16, 17, 8, 3, 0], 'cur_cost': 29329.0}, {'tour': array([ 1,  3, 17, 15, 21,  8, 20, 12, 10,  6,  9, 11, 16, 19,  0, 14, 13,
        2,  5, 18,  7,  4], dtype=int64), 'cur_cost': 31350.0}, {'tour': [0, 18, 12, 13, 9, 10, 11, 14, 17, 15, 16, 21, 20, 19, 1, 2, 3, 7, 4, 5, 6, 8], 'cur_cost': 12081.0}]
2025-08-05 10:28:50,577 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:50,577 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 192, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 192, 'cache_hits': 0, 'similarity_calculations': 855, 'cache_hit_rate': 0.0, 'cache_size': 855}}
2025-08-05 10:28:50,579 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 1,  3, 17, 15, 21,  8, 20, 12, 10,  6,  9, 11, 16, 19,  0, 14, 13,
        2,  5, 18,  7,  4], dtype=int64), 'cur_cost': 31350.0, 'intermediate_solutions': [{'tour': array([14, 13,  2, 17, 15,  7,  1, 11,  6, 10, 20,  0, 16, 18,  3,  5, 21,
        8, 19,  4, 12,  9]), 'cur_cost': 37373.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([17, 14, 13,  2, 15,  7,  1, 11,  6, 10, 20,  0, 16, 18,  3,  5, 21,
        8, 19,  4, 12,  9]), 'cur_cost': 40390.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([15, 17, 14, 13,  2,  7,  1, 11,  6, 10, 20,  0, 16, 18,  3,  5, 21,
        8, 19,  4, 12,  9]), 'cur_cost': 40020.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2, 17, 14, 13, 15,  7,  1, 11,  6, 10, 20,  0, 16, 18,  3,  5, 21,
        8, 19,  4, 12,  9]), 'cur_cost': 40444.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2, 15, 17, 14, 13,  7,  1, 11,  6, 10, 20,  0, 16, 18,  3,  5, 21,
        8, 19,  4, 12,  9]), 'cur_cost': 40058.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:50,579 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 31350.00)
2025-08-05 10:28:50,579 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:50,579 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:50,580 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:50,581 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 22
2025-08-05 10:28:50,581 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,581 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,581 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,582 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,582 - ExplorationExpert - INFO - 探索路径生成完成，成本: 25988.0, 路径长度: 22, 收集中间解: 3
2025-08-05 10:28:50,582 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [7, 5, 20, 19, 0, 21, 18, 3, 13, 9, 14, 10, 15, 16, 12, 17, 11, 2, 6, 4, 8, 1], 'cur_cost': 25988.0, 'intermediate_solutions': [{'tour': [0, 17, 12, 13, 9, 10, 11, 14, 18, 15, 16, 21, 20, 19, 1, 2, 3, 7, 4, 5, 6, 8], 'cur_cost': 12119.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 12, 13, 9, 10, 11, 14, 17, 15, 16, 1, 19, 20, 21, 2, 3, 7, 4, 5, 6, 8], 'cur_cost': 14592.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [18, 12, 13, 9, 10, 11, 14, 17, 15, 16, 21, 20, 19, 1, 2, 3, 7, 4, 5, 6, 8, 0], 'cur_cost': 12081.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:50,582 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 25988.00)
2025-08-05 10:28:50,582 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:50,582 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:50,584 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 9, 7, 4, 5, 6, 8, 21, 15, 16, 17, 18, 19, 20, 1, 2, 3, 13, 12, 11, 10], 'cur_cost': 14409.0, 'intermediate_solutions': [{'tour': [1, 5, 0, 11, 4, 6, 7, 8, 21, 15, 16, 3, 18, 19, 20, 17, 2, 13, 9, 10, 14, 12], 'cur_cost': 22806.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 5, 0, 11, 4, 6, 2, 3, 20, 19, 18, 17, 16, 15, 21, 8, 7, 13, 9, 10, 14, 12], 'cur_cost': 23763.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 5, 0, 11, 4, 6, 7, 8, 21, 15, 16, 17, 18, 19, 20, 3, 14, 2, 13, 9, 10, 12], 'cur_cost': 25015.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 17, 11, 9, 10, 14, 13, 12, 16, 15, 18, 19, 20, 21, 3, 2, 7, 4, 5, 6, 8], 'cur_cost': 12162.0, 'intermediate_solutions': [{'tour': [1, 4, 0, 21, 6, 5, 7, 8, 16, 15, 20, 17, 19, 2, 11, 3, 13, 9, 18, 10, 12, 14], 'cur_cost': 25848.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 13, 3, 11, 2, 19, 17, 20, 15, 16, 8, 7, 5, 6, 21, 0, 4, 9, 14, 10, 12, 18], 'cur_cost': 25768.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 0, 21, 6, 5, 7, 8, 15, 20, 17, 19, 2, 11, 3, 13, 9, 14, 10, 12, 16, 18], 'cur_cost': 23064.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 9, 16,  3, 14, 20, 10,  4, 13, 11,  8, 15,  5, 12, 21, 19,  1,  2,
        7, 17,  6, 18,  0], dtype=int64), 'cur_cost': 41091.0, 'intermediate_solutions': [{'tour': array([20, 17, 12, 15,  3, 16, 14, 19, 21,  0,  9, 18, 13,  2,  7,  6,  8,
        5,  1, 10, 11,  4]), 'cur_cost': 31981.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([15, 20, 17, 12,  3, 16, 14, 19, 21,  0,  9, 18, 13,  2,  7,  6,  8,
        5,  1, 10, 11,  4]), 'cur_cost': 31620.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3, 15, 20, 17, 12, 16, 14, 19, 21,  0,  9, 18, 13,  2,  7,  6,  8,
        5,  1, 10, 11,  4]), 'cur_cost': 31585.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([12, 15, 20, 17,  3, 16, 14, 19, 21,  0,  9, 18, 13,  2,  7,  6,  8,
        5,  1, 10, 11,  4]), 'cur_cost': 31657.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([12,  3, 15, 20, 17, 16, 14, 19, 21,  0,  9, 18, 13,  2,  7,  6,  8,
        5,  1, 10, 11,  4]), 'cur_cost': 31217.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 2, 3, 1, 19, 15, 16, 17, 18, 20, 21, 13, 9, 10, 11, 12, 14, 7, 4, 6, 8], 'cur_cost': 15473.0, 'intermediate_solutions': [{'tour': [0, 1, 20, 3, 2, 19, 15, 16, 17, 18, 21, 13, 9, 10, 12, 11, 14, 7, 4, 5, 6, 8], 'cur_cost': 12216.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 20, 3, 2, 19, 15, 16, 17, 18, 21, 13, 5, 4, 7, 14, 12, 11, 10, 9, 6, 8], 'cur_cost': 18505.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 20, 15, 3, 2, 19, 16, 17, 18, 21, 13, 9, 10, 11, 12, 14, 7, 4, 5, 6, 8], 'cur_cost': 12241.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [11, 18, 0, 17, 2, 19, 16, 1, 21, 3, 12, 9, 15, 20, 7, 4, 5, 6, 13, 14, 10, 8], 'cur_cost': 26513.0, 'intermediate_solutions': [{'tour': [0, 2, 7, 4, 5, 15, 8, 21, 6, 16, 17, 18, 19, 20, 1, 3, 13, 9, 10, 11, 12, 14], 'cur_cost': 19510.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 7, 4, 5, 6, 8, 21, 15, 16, 17, 9, 13, 3, 1, 20, 19, 18, 10, 11, 12, 14], 'cur_cost': 14756.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 15, 7, 4, 5, 6, 8, 21, 16, 17, 18, 19, 20, 1, 3, 13, 9, 10, 11, 12, 14], 'cur_cost': 12065.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [9, 14, 12, 18, 15, 3, 2, 0, 19, 16, 1, 17, 13, 20, 11, 10, 21, 7, 4, 8, 5, 6], 'cur_cost': 18839.0, 'intermediate_solutions': [{'tour': [11, 19, 18, 2, 13, 20, 10, 0, 3, 6, 7, 5, 21, 8, 9, 14, 15, 4, 16, 17, 1, 12], 'cur_cost': 30838.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 19, 18, 2, 13, 20, 10, 21, 5, 7, 6, 3, 0, 8, 9, 14, 12, 4, 16, 17, 1, 15], 'cur_cost': 30932.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 19, 18, 2, 13, 10, 0, 3, 6, 7, 5, 21, 8, 9, 14, 12, 4, 20, 16, 17, 1, 15], 'cur_cost': 27703.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([19,  3, 20,  5,  1, 17, 12,  6,  9,  2,  4, 18, 16, 14, 10,  0,  8,
       21, 15, 11, 13,  7], dtype=int64), 'cur_cost': 39896.0, 'intermediate_solutions': [{'tour': array([12,  0,  5, 18, 20, 11, 13,  4,  1, 10,  6, 21, 17, 16, 19,  7,  9,
        3, 14, 15,  2,  8]), 'cur_cost': 42064.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([18, 12,  0,  5, 20, 11, 13,  4,  1, 10,  6, 21, 17, 16, 19,  7,  9,
        3, 14, 15,  2,  8]), 'cur_cost': 42411.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([20, 18, 12,  0,  5, 11, 13,  4,  1, 10,  6, 21, 17, 16, 19,  7,  9,
        3, 14, 15,  2,  8]), 'cur_cost': 41948.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 5, 18, 12,  0, 20, 11, 13,  4,  1, 10,  6, 21, 17, 16, 19,  7,  9,
        3, 14, 15,  2,  8]), 'cur_cost': 38832.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 5, 20, 18, 12,  0, 11, 13,  4,  1, 10,  6, 21, 17, 16, 19,  7,  9,
        3, 14, 15,  2,  8]), 'cur_cost': 38404.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [11, 7, 4, 5, 6, 1, 12, 15, 9, 18, 20, 10, 2, 14, 13, 19, 21, 16, 17, 8, 3, 0], 'cur_cost': 29329.0, 'intermediate_solutions': [{'tour': [12, 14, 9, 21, 0, 20, 3, 17, 13, 15, 16, 2, 1, 11, 19, 7, 8, 4, 6, 5, 18, 10], 'cur_cost': 21448.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [12, 14, 9, 21, 0, 20, 3, 17, 13, 15, 16, 19, 1, 11, 2, 8, 7, 4, 6, 5, 18, 10], 'cur_cost': 23096.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [12, 14, 9, 21, 0, 13, 20, 3, 17, 15, 16, 19, 1, 11, 2, 7, 8, 4, 6, 5, 18, 10], 'cur_cost': 22777.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 1,  3, 17, 15, 21,  8, 20, 12, 10,  6,  9, 11, 16, 19,  0, 14, 13,
        2,  5, 18,  7,  4], dtype=int64), 'cur_cost': 31350.0, 'intermediate_solutions': [{'tour': array([14, 13,  2, 17, 15,  7,  1, 11,  6, 10, 20,  0, 16, 18,  3,  5, 21,
        8, 19,  4, 12,  9]), 'cur_cost': 37373.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([17, 14, 13,  2, 15,  7,  1, 11,  6, 10, 20,  0, 16, 18,  3,  5, 21,
        8, 19,  4, 12,  9]), 'cur_cost': 40390.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([15, 17, 14, 13,  2,  7,  1, 11,  6, 10, 20,  0, 16, 18,  3,  5, 21,
        8, 19,  4, 12,  9]), 'cur_cost': 40020.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2, 17, 14, 13, 15,  7,  1, 11,  6, 10, 20,  0, 16, 18,  3,  5, 21,
        8, 19,  4, 12,  9]), 'cur_cost': 40444.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2, 15, 17, 14, 13,  7,  1, 11,  6, 10, 20,  0, 16, 18,  3,  5, 21,
        8, 19,  4, 12,  9]), 'cur_cost': 40058.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [7, 5, 20, 19, 0, 21, 18, 3, 13, 9, 14, 10, 15, 16, 12, 17, 11, 2, 6, 4, 8, 1], 'cur_cost': 25988.0, 'intermediate_solutions': [{'tour': [0, 17, 12, 13, 9, 10, 11, 14, 18, 15, 16, 21, 20, 19, 1, 2, 3, 7, 4, 5, 6, 8], 'cur_cost': 12119.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 12, 13, 9, 10, 11, 14, 17, 15, 16, 1, 19, 20, 21, 2, 3, 7, 4, 5, 6, 8], 'cur_cost': 14592.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [18, 12, 13, 9, 10, 11, 14, 17, 15, 16, 21, 20, 19, 1, 2, 3, 7, 4, 5, 6, 8, 0], 'cur_cost': 12081.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:50,584 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:50,585 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:50,587 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12162.000, 多样性=0.946
2025-08-05 10:28:50,587 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 10:28:50,587 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 10:28:50,588 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:50,591 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.03916495253519512, 'best_improvement': -0.0435006435006435}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.005364806866952706}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.06165580007468604, 'recent_improvements': [-0.01870427064965388, -0.14902607187565475, 0.1046073294997182], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 18, 'new_count': 18, 'count_change': 0, 'old_best_cost': 9455.0, 'new_best_cost': 9455.0, 'quality_improvement': 0.0, 'old_diversity': 0.7046939988116458, 'new_diversity': 0.7046939988116458, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:50,596 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 10:28:50,596 - __main__ - INFO - composite3_22 开始进化第 5 代
2025-08-05 10:28:50,596 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 10:28:50,597 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:50,597 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12162.000, 多样性=0.946
2025-08-05 10:28:50,598 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:50,600 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.946
2025-08-05 10:28:50,600 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:50,609 - EliteExpert - INFO - 精英解分析完成: 精英解数量=18, 多样性=0.705
2025-08-05 10:28:50,612 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 10:28:50,612 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:50,612 - LandscapeExpert - INFO - 添加精英解数据: 18个精英解
2025-08-05 10:28:50,612 - LandscapeExpert - INFO - 数据提取成功: 28个路径, 28个适应度值
2025-08-05 10:28:50,670 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.571, 适应度梯度: -3932.043, 聚类评分: 0.000, 覆盖率: 0.090, 收敛趋势: 0.000, 多样性: 0.663
2025-08-05 10:28:50,670 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 10:28:50,670 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:50,670 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite3_22
2025-08-05 10:28:50,676 - visualization.landscape_visualizer - INFO - 插值约束: 175 个点被约束到最小值 9455.00
2025-08-05 10:28:50,677 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=6.8%, 梯度: 1371.48 → 1277.56
2025-08-05 10:28:50,798 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite3_22\landscape_composite3_22_iter_75_20250805_102850.html
2025-08-05 10:28:50,877 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite3_22\dashboard_composite3_22_iter_75_20250805_102850.html
2025-08-05 10:28:50,877 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 75
2025-08-05 10:28:50,877 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 10:28:50,877 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2653秒
2025-08-05 10:28:50,878 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5714285714285714, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -3932.042857142857, 'local_optima_density': 0.5714285714285714, 'gradient_variance': 60749066.8667347, 'cluster_count': 0}, 'population_state': {'diversity': 0.6633597883597883, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0903, 'fitness_entropy': 0.6340975525257116, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -3932.043)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.090)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360930.6706617, 'performance_metrics': {}}}
2025-08-05 10:28:50,878 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:50,878 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:50,878 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:50,878 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:50,878 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:28:50,879 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 10:28:50,879 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:28:50,879 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:50,879 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:50,879 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:28:50,879 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:50,880 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:50,880 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:50,880 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:50,880 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:50,880 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:50,881 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 22
2025-08-05 10:28:50,881 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,882 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,882 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,882 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,882 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19888.0, 路径长度: 22, 收集中间解: 3
2025-08-05 10:28:50,882 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [8, 7, 5, 4, 15, 18, 0, 20, 3, 16, 17, 2, 14, 10, 9, 11, 13, 21, 19, 1, 12, 6], 'cur_cost': 19888.0, 'intermediate_solutions': [{'tour': [0, 14, 9, 7, 4, 2, 6, 8, 21, 15, 16, 17, 18, 19, 20, 1, 5, 3, 13, 12, 11, 10], 'cur_cost': 25704.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 9, 7, 4, 5, 18, 17, 16, 15, 21, 8, 6, 19, 20, 1, 2, 3, 13, 12, 11, 10], 'cur_cost': 18457.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 9, 7, 17, 4, 5, 6, 8, 21, 15, 16, 18, 19, 20, 1, 2, 3, 13, 12, 11, 10], 'cur_cost': 18407.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:50,883 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 19888.00)
2025-08-05 10:28:50,883 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:50,883 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:50,883 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:50,883 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 10:28:50,884 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,884 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,884 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,884 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,884 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14901.0, 路径长度: 22, 收集中间解: 3
2025-08-05 10:28:50,885 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 15, 11, 5, 4, 6, 7, 8, 21, 16, 17, 18, 19, 20, 1, 2, 3, 13, 9, 10, 14, 12], 'cur_cost': 14901.0, 'intermediate_solutions': [{'tour': [0, 16, 17, 11, 9, 10, 14, 13, 12, 1, 15, 18, 19, 20, 21, 3, 2, 7, 4, 5, 6, 8], 'cur_cost': 14300.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 17, 11, 9, 10, 14, 13, 12, 16, 15, 18, 19, 20, 2, 3, 21, 7, 4, 5, 6, 8], 'cur_cost': 12515.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 17, 11, 9, 10, 14, 13, 12, 16, 15, 18, 19, 21, 3, 20, 2, 7, 4, 5, 6, 8], 'cur_cost': 14597.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:50,885 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 14901.00)
2025-08-05 10:28:50,885 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-05 10:28:50,885 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:50,885 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:50,885 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 41831.0
2025-08-05 10:28:50,896 - ExploitationExpert - INFO - res_population_num: 22
2025-08-05 10:28:50,896 - ExploitationExpert - INFO - res_population_costs: [9455.0, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455, 9455.0, 9455, 9455.0, 9455.0, 9473.0, 9473, 9455, 9455, 9455, 9455]
2025-08-05 10:28:50,896 - ExploitationExpert - INFO - res_populations: [array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13,  9, 14, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10, 11,  9, 12,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 16, 15, 20, 19, 18, 17, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64)]
2025-08-05 10:28:50,901 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:50,901 - ExploitationExpert - INFO - populations: [{'tour': [8, 7, 5, 4, 15, 18, 0, 20, 3, 16, 17, 2, 14, 10, 9, 11, 13, 21, 19, 1, 12, 6], 'cur_cost': 19888.0}, {'tour': [0, 15, 11, 5, 4, 6, 7, 8, 21, 16, 17, 18, 19, 20, 1, 2, 3, 13, 9, 10, 14, 12], 'cur_cost': 14901.0}, {'tour': array([ 9, 17, 13, 20,  3,  8,  7, 14,  1,  5, 12, 18, 19, 15,  2, 16, 11,
        0, 10,  4, 21,  6], dtype=int64), 'cur_cost': 41831.0}, {'tour': [0, 5, 2, 3, 1, 19, 15, 16, 17, 18, 20, 21, 13, 9, 10, 11, 12, 14, 7, 4, 6, 8], 'cur_cost': 15473.0}, {'tour': [11, 18, 0, 17, 2, 19, 16, 1, 21, 3, 12, 9, 15, 20, 7, 4, 5, 6, 13, 14, 10, 8], 'cur_cost': 26513.0}, {'tour': [9, 14, 12, 18, 15, 3, 2, 0, 19, 16, 1, 17, 13, 20, 11, 10, 21, 7, 4, 8, 5, 6], 'cur_cost': 18839.0}, {'tour': [19, 3, 20, 5, 1, 17, 12, 6, 9, 2, 4, 18, 16, 14, 10, 0, 8, 21, 15, 11, 13, 7], 'cur_cost': 39896.0}, {'tour': [11, 7, 4, 5, 6, 1, 12, 15, 9, 18, 20, 10, 2, 14, 13, 19, 21, 16, 17, 8, 3, 0], 'cur_cost': 29329.0}, {'tour': [1, 3, 17, 15, 21, 8, 20, 12, 10, 6, 9, 11, 16, 19, 0, 14, 13, 2, 5, 18, 7, 4], 'cur_cost': 31350.0}, {'tour': [7, 5, 20, 19, 0, 21, 18, 3, 13, 9, 14, 10, 15, 16, 12, 17, 11, 2, 6, 4, 8, 1], 'cur_cost': 25988.0}]
2025-08-05 10:28:50,902 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:50,902 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 193, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 193, 'cache_hits': 0, 'similarity_calculations': 865, 'cache_hit_rate': 0.0, 'cache_size': 865}}
2025-08-05 10:28:50,903 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([ 9, 17, 13, 20,  3,  8,  7, 14,  1,  5, 12, 18, 19, 15,  2, 16, 11,
        0, 10,  4, 21,  6], dtype=int64), 'cur_cost': 41831.0, 'intermediate_solutions': [{'tour': array([ 3, 16,  9, 14, 20, 10,  4, 13, 11,  8, 15,  5, 12, 21, 19,  1,  2,
        7, 17,  6, 18,  0]), 'cur_cost': 36316.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([14,  3, 16,  9, 20, 10,  4, 13, 11,  8, 15,  5, 12, 21, 19,  1,  2,
        7, 17,  6, 18,  0]), 'cur_cost': 41072.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([20, 14,  3, 16,  9, 10,  4, 13, 11,  8, 15,  5, 12, 21, 19,  1,  2,
        7, 17,  6, 18,  0]), 'cur_cost': 38253.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 9, 14,  3, 16, 20, 10,  4, 13, 11,  8, 15,  5, 12, 21, 19,  1,  2,
        7, 17,  6, 18,  0]), 'cur_cost': 37985.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 9, 20, 14,  3, 16, 10,  4, 13, 11,  8, 15,  5, 12, 21, 19,  1,  2,
        7, 17,  6, 18,  0]), 'cur_cost': 41091.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:50,903 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 41831.00)
2025-08-05 10:28:50,903 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:50,903 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:50,903 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:50,904 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 10:28:50,904 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,904 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,904 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,904 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,905 - ExplorationExpert - INFO - 探索路径生成完成，成本: 18129.0, 路径长度: 22, 收集中间解: 3
2025-08-05 10:28:50,905 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 5, 1, 2, 3, 19, 15, 16, 17, 18, 20, 21, 13, 9, 10, 11, 14, 7, 4, 6, 8], 'cur_cost': 18129.0, 'intermediate_solutions': [{'tour': [0, 5, 2, 3, 1, 19, 15, 16, 17, 18, 20, 21, 13, 9, 10, 11, 4, 14, 7, 12, 6, 8], 'cur_cost': 28017.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 2, 3, 1, 19, 15, 16, 17, 18, 20, 12, 11, 10, 9, 13, 21, 14, 7, 4, 6, 8], 'cur_cost': 18597.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 2, 3, 1, 19, 15, 16, 17, 18, 20, 21, 13, 10, 9, 11, 12, 14, 7, 4, 6, 8], 'cur_cost': 15503.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:50,905 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 18129.00)
2025-08-05 10:28:50,905 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:50,905 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:50,905 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:50,907 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 22
2025-08-05 10:28:50,907 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,907 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,907 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,908 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,908 - ExplorationExpert - INFO - 探索路径生成完成，成本: 22054.0, 路径长度: 22, 收集中间解: 3
2025-08-05 10:28:50,908 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [17, 2, 0, 3, 21, 13, 15, 20, 14, 12, 10, 18, 11, 19, 7, 4, 8, 5, 6, 16, 1, 9], 'cur_cost': 22054.0, 'intermediate_solutions': [{'tour': [11, 1, 0, 17, 2, 19, 16, 18, 21, 3, 12, 9, 15, 20, 7, 4, 5, 6, 13, 14, 10, 8], 'cur_cost': 23782.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 18, 0, 17, 2, 19, 16, 1, 21, 20, 15, 9, 12, 3, 7, 4, 5, 6, 13, 14, 10, 8], 'cur_cost': 26152.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 6, 18, 0, 17, 2, 19, 16, 1, 21, 3, 12, 9, 15, 20, 7, 4, 5, 13, 14, 10, 8], 'cur_cost': 30151.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:50,908 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 22054.00)
2025-08-05 10:28:50,908 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:50,908 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:50,908 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:50,909 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 22
2025-08-05 10:28:50,909 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,909 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,909 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,910 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,910 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17527.0, 路径长度: 22, 收集中间解: 3
2025-08-05 10:28:50,910 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [1, 4, 0, 20, 18, 15, 16, 17, 19, 21, 3, 2, 13, 9, 10, 11, 12, 14, 7, 6, 5, 8], 'cur_cost': 17527.0, 'intermediate_solutions': [{'tour': [9, 14, 12, 18, 15, 3, 2, 0, 19, 16, 1, 17, 10, 20, 11, 13, 21, 7, 4, 8, 5, 6], 'cur_cost': 18944.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 14, 12, 18, 15, 3, 2, 0, 19, 16, 1, 17, 13, 20, 11, 10, 21, 7, 4, 5, 8, 6], 'cur_cost': 18862.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 14, 18, 15, 3, 2, 0, 19, 16, 1, 17, 13, 20, 12, 11, 10, 21, 7, 4, 8, 5, 6], 'cur_cost': 18764.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:50,910 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 17527.00)
2025-08-05 10:28:50,910 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:28:50,910 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:50,910 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:50,911 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 37449.0
2025-08-05 10:28:50,929 - ExploitationExpert - INFO - res_population_num: 22
2025-08-05 10:28:50,929 - ExploitationExpert - INFO - res_population_costs: [9455.0, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455, 9455.0, 9455, 9455.0, 9455.0, 9473.0, 9473, 9455, 9455, 9455, 9455]
2025-08-05 10:28:50,930 - ExploitationExpert - INFO - res_populations: [array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13,  9, 14, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10, 11,  9, 12,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 16, 15, 20, 19, 18, 17, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64)]
2025-08-05 10:28:50,937 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:50,937 - ExploitationExpert - INFO - populations: [{'tour': [8, 7, 5, 4, 15, 18, 0, 20, 3, 16, 17, 2, 14, 10, 9, 11, 13, 21, 19, 1, 12, 6], 'cur_cost': 19888.0}, {'tour': [0, 15, 11, 5, 4, 6, 7, 8, 21, 16, 17, 18, 19, 20, 1, 2, 3, 13, 9, 10, 14, 12], 'cur_cost': 14901.0}, {'tour': array([ 9, 17, 13, 20,  3,  8,  7, 14,  1,  5, 12, 18, 19, 15,  2, 16, 11,
        0, 10,  4, 21,  6], dtype=int64), 'cur_cost': 41831.0}, {'tour': [0, 12, 5, 1, 2, 3, 19, 15, 16, 17, 18, 20, 21, 13, 9, 10, 11, 14, 7, 4, 6, 8], 'cur_cost': 18129.0}, {'tour': [17, 2, 0, 3, 21, 13, 15, 20, 14, 12, 10, 18, 11, 19, 7, 4, 8, 5, 6, 16, 1, 9], 'cur_cost': 22054.0}, {'tour': [1, 4, 0, 20, 18, 15, 16, 17, 19, 21, 3, 2, 13, 9, 10, 11, 12, 14, 7, 6, 5, 8], 'cur_cost': 17527.0}, {'tour': array([ 2, 18, 19, 12,  3,  1,  7, 17,  9, 16,  5, 20, 21, 15, 13, 14, 11,
        8, 10,  6,  0,  4], dtype=int64), 'cur_cost': 37449.0}, {'tour': [11, 7, 4, 5, 6, 1, 12, 15, 9, 18, 20, 10, 2, 14, 13, 19, 21, 16, 17, 8, 3, 0], 'cur_cost': 29329.0}, {'tour': [1, 3, 17, 15, 21, 8, 20, 12, 10, 6, 9, 11, 16, 19, 0, 14, 13, 2, 5, 18, 7, 4], 'cur_cost': 31350.0}, {'tour': [7, 5, 20, 19, 0, 21, 18, 3, 13, 9, 14, 10, 15, 16, 12, 17, 11, 2, 6, 4, 8, 1], 'cur_cost': 25988.0}]
2025-08-05 10:28:50,938 - ExploitationExpert - INFO - 局部搜索耗时: 0.03秒，最大迭代次数: 10
2025-08-05 10:28:50,939 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 194, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 194, 'cache_hits': 0, 'similarity_calculations': 876, 'cache_hit_rate': 0.0, 'cache_size': 876}}
2025-08-05 10:28:50,940 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([ 2, 18, 19, 12,  3,  1,  7, 17,  9, 16,  5, 20, 21, 15, 13, 14, 11,
        8, 10,  6,  0,  4], dtype=int64), 'cur_cost': 37449.0, 'intermediate_solutions': [{'tour': array([20,  3, 19,  5,  1, 17, 12,  6,  9,  2,  4, 18, 16, 14, 10,  0,  8,
       21, 15, 11, 13,  7]), 'cur_cost': 39895.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 5, 20,  3, 19,  1, 17, 12,  6,  9,  2,  4, 18, 16, 14, 10,  0,  8,
       21, 15, 11, 13,  7]), 'cur_cost': 36315.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1,  5, 20,  3, 19, 17, 12,  6,  9,  2,  4, 18, 16, 14, 10,  0,  8,
       21, 15, 11, 13,  7]), 'cur_cost': 39492.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([19,  5, 20,  3,  1, 17, 12,  6,  9,  2,  4, 18, 16, 14, 10,  0,  8,
       21, 15, 11, 13,  7]), 'cur_cost': 37948.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([19,  1,  5, 20,  3, 17, 12,  6,  9,  2,  4, 18, 16, 14, 10,  0,  8,
       21, 15, 11, 13,  7]), 'cur_cost': 39894.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:50,940 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 37449.00)
2025-08-05 10:28:50,940 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:50,940 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:50,940 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:50,941 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 22
2025-08-05 10:28:50,941 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,942 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,942 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,942 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,942 - ExplorationExpert - INFO - 探索路径生成完成，成本: 37422.0, 路径长度: 22, 收集中间解: 3
2025-08-05 10:28:50,943 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [15, 11, 4, 18, 3, 0, 8, 21, 2, 14, 10, 1, 13, 17, 19, 6, 7, 20, 12, 5, 16, 9], 'cur_cost': 37422.0, 'intermediate_solutions': [{'tour': [15, 7, 4, 5, 6, 1, 12, 11, 9, 18, 20, 10, 2, 14, 13, 19, 21, 16, 17, 8, 3, 0], 'cur_cost': 23799.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 12, 1, 6, 5, 4, 7, 15, 9, 18, 20, 10, 2, 14, 13, 19, 21, 16, 17, 8, 3, 0], 'cur_cost': 26586.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 7, 4, 5, 6, 1, 2, 12, 15, 9, 18, 20, 10, 14, 13, 19, 21, 16, 17, 8, 3, 0], 'cur_cost': 24486.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:50,943 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 37422.00)
2025-08-05 10:28:50,943 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:50,943 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:50,943 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:50,944 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 39844.0
2025-08-05 10:28:50,964 - ExploitationExpert - INFO - res_population_num: 27
2025-08-05 10:28:50,964 - ExploitationExpert - INFO - res_population_costs: [9455.0, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455.0, 9455, 9455, 9455, 9455.0, 9455, 9455.0, 9455.0, 9473.0, 9473, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455]
2025-08-05 10:28:50,964 - ExploitationExpert - INFO - res_populations: [array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13,  9, 14, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10, 11,  9, 12,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 16, 15, 20, 19, 18, 17, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  4,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64)]
2025-08-05 10:28:50,972 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:50,972 - ExploitationExpert - INFO - populations: [{'tour': [8, 7, 5, 4, 15, 18, 0, 20, 3, 16, 17, 2, 14, 10, 9, 11, 13, 21, 19, 1, 12, 6], 'cur_cost': 19888.0}, {'tour': [0, 15, 11, 5, 4, 6, 7, 8, 21, 16, 17, 18, 19, 20, 1, 2, 3, 13, 9, 10, 14, 12], 'cur_cost': 14901.0}, {'tour': array([ 9, 17, 13, 20,  3,  8,  7, 14,  1,  5, 12, 18, 19, 15,  2, 16, 11,
        0, 10,  4, 21,  6], dtype=int64), 'cur_cost': 41831.0}, {'tour': [0, 12, 5, 1, 2, 3, 19, 15, 16, 17, 18, 20, 21, 13, 9, 10, 11, 14, 7, 4, 6, 8], 'cur_cost': 18129.0}, {'tour': [17, 2, 0, 3, 21, 13, 15, 20, 14, 12, 10, 18, 11, 19, 7, 4, 8, 5, 6, 16, 1, 9], 'cur_cost': 22054.0}, {'tour': [1, 4, 0, 20, 18, 15, 16, 17, 19, 21, 3, 2, 13, 9, 10, 11, 12, 14, 7, 6, 5, 8], 'cur_cost': 17527.0}, {'tour': array([ 2, 18, 19, 12,  3,  1,  7, 17,  9, 16,  5, 20, 21, 15, 13, 14, 11,
        8, 10,  6,  0,  4], dtype=int64), 'cur_cost': 37449.0}, {'tour': [15, 11, 4, 18, 3, 0, 8, 21, 2, 14, 10, 1, 13, 17, 19, 6, 7, 20, 12, 5, 16, 9], 'cur_cost': 37422.0}, {'tour': array([18, 12,  0,  6,  1, 19, 21,  8, 11,  3, 20, 13, 14,  9, 17, 16,  5,
       10,  4, 15,  2,  7], dtype=int64), 'cur_cost': 39844.0}, {'tour': [7, 5, 20, 19, 0, 21, 18, 3, 13, 9, 14, 10, 15, 16, 12, 17, 11, 2, 6, 4, 8, 1], 'cur_cost': 25988.0}]
2025-08-05 10:28:50,974 - ExploitationExpert - INFO - 局部搜索耗时: 0.03秒，最大迭代次数: 10
2025-08-05 10:28:50,974 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 195, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 195, 'cache_hits': 0, 'similarity_calculations': 888, 'cache_hit_rate': 0.0, 'cache_size': 888}}
2025-08-05 10:28:50,975 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([18, 12,  0,  6,  1, 19, 21,  8, 11,  3, 20, 13, 14,  9, 17, 16,  5,
       10,  4, 15,  2,  7], dtype=int64), 'cur_cost': 39844.0, 'intermediate_solutions': [{'tour': array([17,  3,  1, 15, 21,  8, 20, 12, 10,  6,  9, 11, 16, 19,  0, 14, 13,
        2,  5, 18,  7,  4]), 'cur_cost': 31771.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([15, 17,  3,  1, 21,  8, 20, 12, 10,  6,  9, 11, 16, 19,  0, 14, 13,
        2,  5, 18,  7,  4]), 'cur_cost': 31769.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([21, 15, 17,  3,  1,  8, 20, 12, 10,  6,  9, 11, 16, 19,  0, 14, 13,
        2,  5, 18,  7,  4]), 'cur_cost': 31366.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 1, 15, 17,  3, 21,  8, 20, 12, 10,  6,  9, 11, 16, 19,  0, 14, 13,
        2,  5, 18,  7,  4]), 'cur_cost': 33773.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 1, 21, 15, 17,  3,  8, 20, 12, 10,  6,  9, 11, 16, 19,  0, 14, 13,
        2,  5, 18,  7,  4]), 'cur_cost': 33469.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:50,975 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 39844.00)
2025-08-05 10:28:50,976 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:50,976 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:50,976 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:50,977 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 22
2025-08-05 10:28:50,977 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,977 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,978 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,978 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:50,978 - ExplorationExpert - INFO - 探索路径生成完成，成本: 39509.0, 路径长度: 22, 收集中间解: 3
2025-08-05 10:28:50,978 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [17, 15, 4, 2, 6, 0, 21, 1, 11, 10, 7, 3, 19, 18, 20, 9, 16, 5, 13, 12, 8, 14], 'cur_cost': 39509.0, 'intermediate_solutions': [{'tour': [7, 13, 20, 19, 0, 21, 18, 3, 5, 9, 14, 10, 15, 16, 12, 17, 11, 2, 6, 4, 8, 1], 'cur_cost': 32337.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 20, 19, 3, 18, 21, 0, 13, 9, 14, 10, 15, 16, 12, 17, 11, 2, 6, 4, 8, 1], 'cur_cost': 26012.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 5, 20, 19, 0, 21, 18, 3, 13, 9, 14, 10, 15, 16, 12, 6, 17, 11, 2, 4, 8, 1], 'cur_cost': 29652.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:50,979 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 39509.00)
2025-08-05 10:28:50,979 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:50,979 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:50,981 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [8, 7, 5, 4, 15, 18, 0, 20, 3, 16, 17, 2, 14, 10, 9, 11, 13, 21, 19, 1, 12, 6], 'cur_cost': 19888.0, 'intermediate_solutions': [{'tour': [0, 14, 9, 7, 4, 2, 6, 8, 21, 15, 16, 17, 18, 19, 20, 1, 5, 3, 13, 12, 11, 10], 'cur_cost': 25704.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 9, 7, 4, 5, 18, 17, 16, 15, 21, 8, 6, 19, 20, 1, 2, 3, 13, 12, 11, 10], 'cur_cost': 18457.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 9, 7, 17, 4, 5, 6, 8, 21, 15, 16, 18, 19, 20, 1, 2, 3, 13, 12, 11, 10], 'cur_cost': 18407.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 11, 5, 4, 6, 7, 8, 21, 16, 17, 18, 19, 20, 1, 2, 3, 13, 9, 10, 14, 12], 'cur_cost': 14901.0, 'intermediate_solutions': [{'tour': [0, 16, 17, 11, 9, 10, 14, 13, 12, 1, 15, 18, 19, 20, 21, 3, 2, 7, 4, 5, 6, 8], 'cur_cost': 14300.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 17, 11, 9, 10, 14, 13, 12, 16, 15, 18, 19, 20, 2, 3, 21, 7, 4, 5, 6, 8], 'cur_cost': 12515.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 17, 11, 9, 10, 14, 13, 12, 16, 15, 18, 19, 21, 3, 20, 2, 7, 4, 5, 6, 8], 'cur_cost': 14597.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 9, 17, 13, 20,  3,  8,  7, 14,  1,  5, 12, 18, 19, 15,  2, 16, 11,
        0, 10,  4, 21,  6], dtype=int64), 'cur_cost': 41831.0, 'intermediate_solutions': [{'tour': array([ 3, 16,  9, 14, 20, 10,  4, 13, 11,  8, 15,  5, 12, 21, 19,  1,  2,
        7, 17,  6, 18,  0]), 'cur_cost': 36316.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([14,  3, 16,  9, 20, 10,  4, 13, 11,  8, 15,  5, 12, 21, 19,  1,  2,
        7, 17,  6, 18,  0]), 'cur_cost': 41072.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([20, 14,  3, 16,  9, 10,  4, 13, 11,  8, 15,  5, 12, 21, 19,  1,  2,
        7, 17,  6, 18,  0]), 'cur_cost': 38253.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 9, 14,  3, 16, 20, 10,  4, 13, 11,  8, 15,  5, 12, 21, 19,  1,  2,
        7, 17,  6, 18,  0]), 'cur_cost': 37985.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 9, 20, 14,  3, 16, 10,  4, 13, 11,  8, 15,  5, 12, 21, 19,  1,  2,
        7, 17,  6, 18,  0]), 'cur_cost': 41091.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 1, 2, 3, 19, 15, 16, 17, 18, 20, 21, 13, 9, 10, 11, 14, 7, 4, 6, 8], 'cur_cost': 18129.0, 'intermediate_solutions': [{'tour': [0, 5, 2, 3, 1, 19, 15, 16, 17, 18, 20, 21, 13, 9, 10, 11, 4, 14, 7, 12, 6, 8], 'cur_cost': 28017.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 2, 3, 1, 19, 15, 16, 17, 18, 20, 12, 11, 10, 9, 13, 21, 14, 7, 4, 6, 8], 'cur_cost': 18597.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 2, 3, 1, 19, 15, 16, 17, 18, 20, 21, 13, 10, 9, 11, 12, 14, 7, 4, 6, 8], 'cur_cost': 15503.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [17, 2, 0, 3, 21, 13, 15, 20, 14, 12, 10, 18, 11, 19, 7, 4, 8, 5, 6, 16, 1, 9], 'cur_cost': 22054.0, 'intermediate_solutions': [{'tour': [11, 1, 0, 17, 2, 19, 16, 18, 21, 3, 12, 9, 15, 20, 7, 4, 5, 6, 13, 14, 10, 8], 'cur_cost': 23782.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 18, 0, 17, 2, 19, 16, 1, 21, 20, 15, 9, 12, 3, 7, 4, 5, 6, 13, 14, 10, 8], 'cur_cost': 26152.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 6, 18, 0, 17, 2, 19, 16, 1, 21, 3, 12, 9, 15, 20, 7, 4, 5, 13, 14, 10, 8], 'cur_cost': 30151.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 0, 20, 18, 15, 16, 17, 19, 21, 3, 2, 13, 9, 10, 11, 12, 14, 7, 6, 5, 8], 'cur_cost': 17527.0, 'intermediate_solutions': [{'tour': [9, 14, 12, 18, 15, 3, 2, 0, 19, 16, 1, 17, 10, 20, 11, 13, 21, 7, 4, 8, 5, 6], 'cur_cost': 18944.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 14, 12, 18, 15, 3, 2, 0, 19, 16, 1, 17, 13, 20, 11, 10, 21, 7, 4, 5, 8, 6], 'cur_cost': 18862.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 14, 18, 15, 3, 2, 0, 19, 16, 1, 17, 13, 20, 12, 11, 10, 21, 7, 4, 8, 5, 6], 'cur_cost': 18764.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 2, 18, 19, 12,  3,  1,  7, 17,  9, 16,  5, 20, 21, 15, 13, 14, 11,
        8, 10,  6,  0,  4], dtype=int64), 'cur_cost': 37449.0, 'intermediate_solutions': [{'tour': array([20,  3, 19,  5,  1, 17, 12,  6,  9,  2,  4, 18, 16, 14, 10,  0,  8,
       21, 15, 11, 13,  7]), 'cur_cost': 39895.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 5, 20,  3, 19,  1, 17, 12,  6,  9,  2,  4, 18, 16, 14, 10,  0,  8,
       21, 15, 11, 13,  7]), 'cur_cost': 36315.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1,  5, 20,  3, 19, 17, 12,  6,  9,  2,  4, 18, 16, 14, 10,  0,  8,
       21, 15, 11, 13,  7]), 'cur_cost': 39492.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([19,  5, 20,  3,  1, 17, 12,  6,  9,  2,  4, 18, 16, 14, 10,  0,  8,
       21, 15, 11, 13,  7]), 'cur_cost': 37948.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([19,  1,  5, 20,  3, 17, 12,  6,  9,  2,  4, 18, 16, 14, 10,  0,  8,
       21, 15, 11, 13,  7]), 'cur_cost': 39894.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [15, 11, 4, 18, 3, 0, 8, 21, 2, 14, 10, 1, 13, 17, 19, 6, 7, 20, 12, 5, 16, 9], 'cur_cost': 37422.0, 'intermediate_solutions': [{'tour': [15, 7, 4, 5, 6, 1, 12, 11, 9, 18, 20, 10, 2, 14, 13, 19, 21, 16, 17, 8, 3, 0], 'cur_cost': 23799.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 12, 1, 6, 5, 4, 7, 15, 9, 18, 20, 10, 2, 14, 13, 19, 21, 16, 17, 8, 3, 0], 'cur_cost': 26586.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 7, 4, 5, 6, 1, 2, 12, 15, 9, 18, 20, 10, 14, 13, 19, 21, 16, 17, 8, 3, 0], 'cur_cost': 24486.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([18, 12,  0,  6,  1, 19, 21,  8, 11,  3, 20, 13, 14,  9, 17, 16,  5,
       10,  4, 15,  2,  7], dtype=int64), 'cur_cost': 39844.0, 'intermediate_solutions': [{'tour': array([17,  3,  1, 15, 21,  8, 20, 12, 10,  6,  9, 11, 16, 19,  0, 14, 13,
        2,  5, 18,  7,  4]), 'cur_cost': 31771.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([15, 17,  3,  1, 21,  8, 20, 12, 10,  6,  9, 11, 16, 19,  0, 14, 13,
        2,  5, 18,  7,  4]), 'cur_cost': 31769.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([21, 15, 17,  3,  1,  8, 20, 12, 10,  6,  9, 11, 16, 19,  0, 14, 13,
        2,  5, 18,  7,  4]), 'cur_cost': 31366.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 1, 15, 17,  3, 21,  8, 20, 12, 10,  6,  9, 11, 16, 19,  0, 14, 13,
        2,  5, 18,  7,  4]), 'cur_cost': 33773.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 1, 21, 15, 17,  3,  8, 20, 12, 10,  6,  9, 11, 16, 19,  0, 14, 13,
        2,  5, 18,  7,  4]), 'cur_cost': 33469.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [17, 15, 4, 2, 6, 0, 21, 1, 11, 10, 7, 3, 19, 18, 20, 9, 16, 5, 13, 12, 8, 14], 'cur_cost': 39509.0, 'intermediate_solutions': [{'tour': [7, 13, 20, 19, 0, 21, 18, 3, 5, 9, 14, 10, 15, 16, 12, 17, 11, 2, 6, 4, 8, 1], 'cur_cost': 32337.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 20, 19, 3, 18, 21, 0, 13, 9, 14, 10, 15, 16, 12, 17, 11, 2, 6, 4, 8, 1], 'cur_cost': 26012.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 5, 20, 19, 0, 21, 18, 3, 13, 9, 14, 10, 15, 16, 12, 6, 17, 11, 2, 4, 8, 1], 'cur_cost': 29652.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:50,982 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:50,982 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:50,985 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=14901.000, 多样性=0.944
2025-08-05 10:28:50,986 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 10:28:50,986 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 10:28:50,986 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:50,990 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.12486031904092774, 'best_improvement': -0.2252096694622595}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.00213447171824963}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.05493055967022981, 'recent_improvements': [-0.14902607187565475, 0.1046073294997182, -0.03916495253519512], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 27, 'new_count': 27, 'count_change': 0, 'old_best_cost': 9455.0, 'new_best_cost': 9455.0, 'quality_improvement': 0.0, 'old_diversity': 0.7258482258482258, 'new_diversity': 0.7258482258482258, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:50,995 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 10:28:51,003 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite3_22_solution.json
2025-08-05 10:28:51,003 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite3_22_20250805_102850.solution
2025-08-05 10:28:51,004 - __main__ - INFO - 实例执行完成 - 运行时间: 1.65s, 最佳成本: 9455.0
2025-08-05 10:28:51,004 - __main__ - INFO - 实例 composite3_22 处理完成
