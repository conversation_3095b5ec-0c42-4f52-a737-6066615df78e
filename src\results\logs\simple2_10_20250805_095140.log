2025-08-05 09:51:40,190 - __main__ - INFO - simple2_10 开始进化第 1 代
2025-08-05 09:51:40,190 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 09:51:40,191 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:40,192 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1346.000, 多样性=0.902
2025-08-05 09:51:40,193 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:40,194 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.902
2025-08-05 09:51:40,195 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:40,197 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 09:51:40,197 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:40,197 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 09:51:40,197 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 09:51:40,204 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.200, 适应度梯度: -49.740, 聚类评分: 0.000, 覆盖率: 0.006, 收敛趋势: 0.000, 多样性: 0.902
2025-08-05 09:51:40,205 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 09:51:40,205 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 09:51:40,205 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple2_10
2025-08-05 09:51:40,306 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple2_10\landscape_simple2_10_iter_6_20250805_095140.html
2025-08-05 09:51:40,350 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple2_10\dashboard_simple2_10_iter_6_20250805_095140.html
2025-08-05 09:51:40,350 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 6
2025-08-05 09:51:40,350 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 09:51:40,350 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1530秒
2025-08-05 09:51:40,350 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 12, 'max_size': 500, 'hits': 0, 'misses': 12, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 18, 'max_size': 100, 'hits': 37, 'misses': 18, 'hit_rate': 0.6727272727272727, 'evictions': 0, 'ttl': 7200}}
2025-08-05 09:51:40,351 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -49.74000000000002, 'local_optima_density': 0.2, 'gradient_variance': 248059.96839999998, 'cluster_count': 0}, 'population_state': {'diversity': 0.9022222222222223, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0063, 'fitness_entropy': 0.9464119282150149, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -49.740)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.006)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.902)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358700.2054086, 'performance_metrics': {}}}
2025-08-05 09:51:40,351 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:40,351 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:40,351 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:40,351 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:40,352 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:51:40,352 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 09:51:40,352 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:51:40,352 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:40,352 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:40,352 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:51:40,353 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:40,353 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:40,353 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:40,353 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:51:40,353 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:51:40,353 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,353 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 09:51:40,353 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,353 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2253.0, 路径长度: 10, 收集中间解: 0
2025-08-05 09:51:40,354 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [7, 2, 9, 3, 0, 1, 6, 4, 8, 5], 'cur_cost': 2253.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,355 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 2253.00)
2025-08-05 09:51:40,355 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:51:40,355 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:51:40,355 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,355 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:40,355 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,355 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1942.0, 路径长度: 10, 收集中间解: 0
2025-08-05 09:51:40,355 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [2, 4, 5, 9, 3, 1, 0, 8, 6, 7], 'cur_cost': 1942.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,355 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1942.00)
2025-08-05 09:51:40,355 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:51:40,356 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:51:40,356 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,356 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:40,356 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,357 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2139.0, 路径长度: 10, 收集中间解: 0
2025-08-05 09:51:40,357 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 8, 2, 4, 5, 6, 7, 9, 3, 1], 'cur_cost': 2139.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,357 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 2139.00)
2025-08-05 09:51:40,357 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:51:40,357 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:51:40,357 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,357 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:40,358 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,358 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2116.0, 路径长度: 10, 收集中间解: 0
2025-08-05 09:51:40,358 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 5, 8, 9, 2, 3, 1, 4, 6, 7], 'cur_cost': 2116.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,358 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 2116.00)
2025-08-05 09:51:40,358 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 09:51:40,358 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:40,359 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:40,359 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 2334.0
2025-08-05 09:51:40,363 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 09:51:40,363 - ExploitationExpert - INFO - res_population_costs: [1265.0, 1265]
2025-08-05 09:51:40,363 - ExploitationExpert - INFO - res_populations: [array([0, 4, 6, 8, 7, 9, 5, 1, 3, 2], dtype=int64), array([0, 4, 6, 8, 7, 9, 3, 5, 1, 2], dtype=int64)]
2025-08-05 09:51:40,364 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:40,364 - ExploitationExpert - INFO - populations: [{'tour': [7, 2, 9, 3, 0, 1, 6, 4, 8, 5], 'cur_cost': 2253.0}, {'tour': [2, 4, 5, 9, 3, 1, 0, 8, 6, 7], 'cur_cost': 1942.0}, {'tour': [0, 8, 2, 4, 5, 6, 7, 9, 3, 1], 'cur_cost': 2139.0}, {'tour': [0, 5, 8, 9, 2, 3, 1, 4, 6, 7], 'cur_cost': 2116.0}, {'tour': array([4, 1, 2, 6, 3, 0, 5, 8, 7, 9], dtype=int64), 'cur_cost': 2334.0}, {'tour': array([2, 1, 8, 4, 3, 7, 0, 6, 9, 5], dtype=int64), 'cur_cost': 2106.0}, {'tour': array([3, 6, 0, 9, 5, 1, 4, 7, 8, 2], dtype=int64), 'cur_cost': 2090.0}, {'tour': array([6, 9, 4, 1, 7, 8, 3, 5, 2, 0], dtype=int64), 'cur_cost': 2138.0}, {'tour': array([0, 3, 6, 2, 1, 7, 4, 8, 5, 9], dtype=int64), 'cur_cost': 2354.0}, {'tour': array([0, 5, 2, 8, 7, 6, 9, 1, 3, 4], dtype=int64), 'cur_cost': 1939.0}]
2025-08-05 09:51:40,365 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:40,365 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 14, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 14, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-08-05 09:51:40,366 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([4, 1, 2, 6, 3, 0, 5, 8, 7, 9], dtype=int64), 'cur_cost': 2334.0, 'intermediate_solutions': [{'tour': array([6, 4, 3, 0, 7, 1, 8, 2, 5, 9], dtype=int64), 'cur_cost': 2372.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 6, 4, 3, 7, 1, 8, 2, 5, 9], dtype=int64), 'cur_cost': 2322.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 0, 6, 4, 3, 1, 8, 2, 5, 9], dtype=int64), 'cur_cost': 2082.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 0, 6, 4, 7, 1, 8, 2, 5, 9], dtype=int64), 'cur_cost': 2246.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 7, 0, 6, 4, 1, 8, 2, 5, 9], dtype=int64), 'cur_cost': 2255.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:40,366 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 2334.00)
2025-08-05 09:51:40,366 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:51:40,366 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:51:40,367 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,367 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 09:51:40,367 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,367 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1826.0, 路径长度: 10, 收集中间解: 0
2025-08-05 09:51:40,367 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [7, 6, 4, 8, 9, 2, 1, 3, 5, 0], 'cur_cost': 1826.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,368 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1826.00)
2025-08-05 09:51:40,368 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:51:40,368 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:51:40,368 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,368 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:40,368 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,368 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1649.0, 路径长度: 10, 收集中间解: 0
2025-08-05 09:51:40,368 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [6, 8, 3, 7, 5, 1, 2, 9, 4, 0], 'cur_cost': 1649.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,369 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1649.00)
2025-08-05 09:51:40,369 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:51:40,369 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:51:40,369 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,369 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 09:51:40,369 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,370 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1680.0, 路径长度: 10, 收集中间解: 0
2025-08-05 09:51:40,370 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [8, 7, 5, 9, 6, 4, 0, 2, 3, 1], 'cur_cost': 1680.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,370 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1680.00)
2025-08-05 09:51:40,370 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:51:40,370 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:40,370 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:40,371 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 1941.0
2025-08-05 09:51:40,376 - ExploitationExpert - INFO - res_population_num: 3
2025-08-05 09:51:40,376 - ExploitationExpert - INFO - res_population_costs: [1265.0, 1265, 1265]
2025-08-05 09:51:40,376 - ExploitationExpert - INFO - res_populations: [array([0, 4, 6, 8, 7, 9, 5, 1, 3, 2], dtype=int64), array([0, 4, 6, 8, 7, 9, 3, 5, 1, 2], dtype=int64), array([0, 4, 8, 6, 7, 9, 5, 1, 3, 2], dtype=int64)]
2025-08-05 09:51:40,377 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:40,377 - ExploitationExpert - INFO - populations: [{'tour': [7, 2, 9, 3, 0, 1, 6, 4, 8, 5], 'cur_cost': 2253.0}, {'tour': [2, 4, 5, 9, 3, 1, 0, 8, 6, 7], 'cur_cost': 1942.0}, {'tour': [0, 8, 2, 4, 5, 6, 7, 9, 3, 1], 'cur_cost': 2139.0}, {'tour': [0, 5, 8, 9, 2, 3, 1, 4, 6, 7], 'cur_cost': 2116.0}, {'tour': array([4, 1, 2, 6, 3, 0, 5, 8, 7, 9], dtype=int64), 'cur_cost': 2334.0}, {'tour': [7, 6, 4, 8, 9, 2, 1, 3, 5, 0], 'cur_cost': 1826.0}, {'tour': [6, 8, 3, 7, 5, 1, 2, 9, 4, 0], 'cur_cost': 1649.0}, {'tour': [8, 7, 5, 9, 6, 4, 0, 2, 3, 1], 'cur_cost': 1680.0}, {'tour': array([4, 7, 5, 3, 1, 0, 8, 6, 2, 9], dtype=int64), 'cur_cost': 1941.0}, {'tour': array([0, 5, 2, 8, 7, 6, 9, 1, 3, 4], dtype=int64), 'cur_cost': 1939.0}]
2025-08-05 09:51:40,378 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:40,378 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 15, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 15, 'cache_hits': 0, 'similarity_calculations': 79, 'cache_hit_rate': 0.0, 'cache_size': 79}}
2025-08-05 09:51:40,378 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([4, 7, 5, 3, 1, 0, 8, 6, 2, 9], dtype=int64), 'cur_cost': 1941.0, 'intermediate_solutions': [{'tour': array([6, 3, 0, 2, 1, 7, 4, 8, 5, 9], dtype=int64), 'cur_cost': 2207.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 6, 3, 0, 1, 7, 4, 8, 5, 9], dtype=int64), 'cur_cost': 2426.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 2, 6, 3, 0, 7, 4, 8, 5, 9], dtype=int64), 'cur_cost': 2328.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 2, 6, 3, 1, 7, 4, 8, 5, 9], dtype=int64), 'cur_cost': 2331.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 1, 2, 6, 3, 7, 4, 8, 5, 9], dtype=int64), 'cur_cost': 2285.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:40,379 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1941.00)
2025-08-05 09:51:40,379 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:51:40,379 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:51:40,379 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,379 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:40,379 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,379 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1909.0, 路径长度: 10, 收集中间解: 0
2025-08-05 09:51:40,380 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [2, 5, 3, 8, 1, 7, 9, 6, 4, 0], 'cur_cost': 1909.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,380 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1909.00)
2025-08-05 09:51:40,380 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:40,380 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:40,380 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [7, 2, 9, 3, 0, 1, 6, 4, 8, 5], 'cur_cost': 2253.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [2, 4, 5, 9, 3, 1, 0, 8, 6, 7], 'cur_cost': 1942.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 2, 4, 5, 6, 7, 9, 3, 1], 'cur_cost': 2139.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 8, 9, 2, 3, 1, 4, 6, 7], 'cur_cost': 2116.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 1, 2, 6, 3, 0, 5, 8, 7, 9], dtype=int64), 'cur_cost': 2334.0, 'intermediate_solutions': [{'tour': array([6, 4, 3, 0, 7, 1, 8, 2, 5, 9], dtype=int64), 'cur_cost': 2372.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 6, 4, 3, 7, 1, 8, 2, 5, 9], dtype=int64), 'cur_cost': 2322.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 0, 6, 4, 3, 1, 8, 2, 5, 9], dtype=int64), 'cur_cost': 2082.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 0, 6, 4, 7, 1, 8, 2, 5, 9], dtype=int64), 'cur_cost': 2246.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 7, 0, 6, 4, 1, 8, 2, 5, 9], dtype=int64), 'cur_cost': 2255.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [7, 6, 4, 8, 9, 2, 1, 3, 5, 0], 'cur_cost': 1826.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [6, 8, 3, 7, 5, 1, 2, 9, 4, 0], 'cur_cost': 1649.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [8, 7, 5, 9, 6, 4, 0, 2, 3, 1], 'cur_cost': 1680.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 7, 5, 3, 1, 0, 8, 6, 2, 9], dtype=int64), 'cur_cost': 1941.0, 'intermediate_solutions': [{'tour': array([6, 3, 0, 2, 1, 7, 4, 8, 5, 9], dtype=int64), 'cur_cost': 2207.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 6, 3, 0, 1, 7, 4, 8, 5, 9], dtype=int64), 'cur_cost': 2426.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 2, 6, 3, 0, 7, 4, 8, 5, 9], dtype=int64), 'cur_cost': 2328.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 2, 6, 3, 1, 7, 4, 8, 5, 9], dtype=int64), 'cur_cost': 2331.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 1, 2, 6, 3, 7, 4, 8, 5, 9], dtype=int64), 'cur_cost': 2285.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [2, 5, 3, 8, 1, 7, 9, 6, 4, 0], 'cur_cost': 1909.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 09:51:40,381 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:40,381 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:40,382 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1649.000, 多样性=0.913
2025-08-05 09:51:40,382 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 09:51:40,382 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 09:51:40,382 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:40,382 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.08038525904737044, 'best_improvement': -0.22511144130757801}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.012315270935960299}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.009521605730770271, 'recent_improvements': [0.008978211602456624, -0.028590549866207973, -0.01006499985908392], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 1265.0, 'new_best_cost': 1265.0, 'quality_improvement': 0.0, 'old_diversity': 0.33333333333333337, 'new_diversity': 0.33333333333333337, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启']}
2025-08-05 09:51:40,382 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 09:51:40,382 - __main__ - INFO - simple2_10 开始进化第 2 代
2025-08-05 09:51:40,383 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 09:51:40,383 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:40,383 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1649.000, 多样性=0.913
2025-08-05 09:51:40,383 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:40,384 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.913
2025-08-05 09:51:40,384 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:40,385 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.333
2025-08-05 09:51:40,386 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 09:51:40,387 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:40,387 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-05 09:51:40,387 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-05 09:51:40,394 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.308, 适应度梯度: 36.415, 聚类评分: 0.000, 覆盖率: 0.008, 收敛趋势: 0.000, 多样性: 0.690
2025-08-05 09:51:40,395 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 09:51:40,395 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:51:40,395 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple2_10
2025-08-05 09:51:40,399 - visualization.landscape_visualizer - INFO - 插值约束: 15 个点被约束到最小值 1265.00
2025-08-05 09:51:40,477 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple2_10\landscape_simple2_10_iter_7_20250805_095140.html
2025-08-05 09:51:40,535 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple2_10\dashboard_simple2_10_iter_7_20250805_095140.html
2025-08-05 09:51:40,535 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 7
2025-08-05 09:51:40,535 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 09:51:40,535 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1487秒
2025-08-05 09:51:40,535 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3076923076923077, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 36.4153846153846, 'local_optima_density': 0.3076923076923077, 'gradient_variance': 139054.83360946743, 'cluster_count': 0}, 'population_state': {'diversity': 0.6903353057199211, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0076, 'fitness_entropy': 0.9479479190038744, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.008)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 36.415)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358700.3958864, 'performance_metrics': {}}}
2025-08-05 09:51:40,535 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:40,536 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:40,536 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:40,536 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:40,536 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-05 09:51:40,536 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 09:51:40,536 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-05 09:51:40,536 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:40,537 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:40,537 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-05 09:51:40,537 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:40,537 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:40,537 - experts.management.collaboration_manager - INFO - 识别精英个体: {6, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:40,537 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-05 09:51:40,537 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:40,537 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:40,538 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 2078.0
2025-08-05 09:51:40,545 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:40,545 - ExploitationExpert - INFO - res_population_costs: [1265.0, 1265, 1265, 1265.0]
2025-08-05 09:51:40,545 - ExploitationExpert - INFO - res_populations: [array([0, 4, 6, 8, 7, 9, 5, 1, 3, 2], dtype=int64), array([0, 4, 6, 8, 7, 9, 3, 5, 1, 2], dtype=int64), array([0, 4, 8, 6, 7, 9, 5, 1, 3, 2], dtype=int64), array([0, 2, 1, 5, 3, 9, 7, 6, 8, 4], dtype=int64)]
2025-08-05 09:51:40,547 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:40,547 - ExploitationExpert - INFO - populations: [{'tour': array([5, 4, 9, 7, 0, 1, 2, 3, 6, 8], dtype=int64), 'cur_cost': 2078.0}, {'tour': [2, 4, 5, 9, 3, 1, 0, 8, 6, 7], 'cur_cost': 1942.0}, {'tour': [0, 8, 2, 4, 5, 6, 7, 9, 3, 1], 'cur_cost': 2139.0}, {'tour': [0, 5, 8, 9, 2, 3, 1, 4, 6, 7], 'cur_cost': 2116.0}, {'tour': [4, 1, 2, 6, 3, 0, 5, 8, 7, 9], 'cur_cost': 2334.0}, {'tour': [7, 6, 4, 8, 9, 2, 1, 3, 5, 0], 'cur_cost': 1826.0}, {'tour': [6, 8, 3, 7, 5, 1, 2, 9, 4, 0], 'cur_cost': 1649.0}, {'tour': [8, 7, 5, 9, 6, 4, 0, 2, 3, 1], 'cur_cost': 1680.0}, {'tour': [4, 7, 5, 3, 1, 0, 8, 6, 2, 9], 'cur_cost': 1941.0}, {'tour': [2, 5, 3, 8, 1, 7, 9, 6, 4, 0], 'cur_cost': 1909.0}]
2025-08-05 09:51:40,547 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:40,548 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 16, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 16, 'cache_hits': 0, 'similarity_calculations': 81, 'cache_hit_rate': 0.0, 'cache_size': 81}}
2025-08-05 09:51:40,548 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([5, 4, 9, 7, 0, 1, 2, 3, 6, 8], dtype=int64), 'cur_cost': 2078.0, 'intermediate_solutions': [{'tour': array([9, 2, 7, 3, 0, 1, 6, 4, 8, 5]), 'cur_cost': 2297.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 9, 2, 7, 0, 1, 6, 4, 8, 5]), 'cur_cost': 2247.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 3, 9, 2, 7, 1, 6, 4, 8, 5]), 'cur_cost': 2437.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 3, 9, 2, 0, 1, 6, 4, 8, 5]), 'cur_cost': 2173.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 0, 3, 9, 2, 1, 6, 4, 8, 5]), 'cur_cost': 2149.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:40,548 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 2078.00)
2025-08-05 09:51:40,548 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:51:40,549 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:51:40,549 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,549 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 09:51:40,550 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,550 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,550 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,550 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,551 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1732.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:40,551 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [3, 5, 2, 4, 6, 9, 7, 8, 0, 1], 'cur_cost': 1732.0, 'intermediate_solutions': [{'tour': [2, 0, 5, 9, 3, 1, 4, 8, 6, 7], 'cur_cost': 1910.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 4, 5, 9, 3, 1, 7, 6, 8, 0], 'cur_cost': 1931.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 5, 0, 9, 3, 1, 8, 6, 7], 'cur_cost': 2234.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,551 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1732.00)
2025-08-05 09:51:40,552 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:51:40,552 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:51:40,552 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,552 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 09:51:40,553 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,553 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,553 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,553 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,553 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1601.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:40,554 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [6, 7, 3, 2, 5, 1, 0, 4, 8, 9], 'cur_cost': 1601.0, 'intermediate_solutions': [{'tour': [9, 8, 2, 4, 5, 6, 7, 0, 3, 1], 'cur_cost': 2366.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 2, 1, 3, 9, 7, 6, 5, 4], 'cur_cost': 1831.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 2, 4, 5, 7, 9, 3, 1, 6], 'cur_cost': 2163.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,554 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1601.00)
2025-08-05 09:51:40,554 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:51:40,554 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:51:40,554 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,555 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 09:51:40,555 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,555 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,556 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,556 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,556 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1588.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:40,556 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [9, 2, 3, 5, 1, 0, 8, 6, 4, 7], 'cur_cost': 1588.0, 'intermediate_solutions': [{'tour': [9, 5, 8, 0, 2, 3, 1, 4, 6, 7], 'cur_cost': 1813.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 7, 6, 4, 1, 3, 2, 9, 8], 'cur_cost': 1915.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 8, 2, 9, 3, 1, 4, 6, 7], 'cur_cost': 2258.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,556 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1588.00)
2025-08-05 09:51:40,557 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 09:51:40,557 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:40,557 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:40,557 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 2495.0
2025-08-05 09:51:40,566 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:40,566 - ExploitationExpert - INFO - res_population_costs: [1265.0, 1265, 1265, 1265.0]
2025-08-05 09:51:40,567 - ExploitationExpert - INFO - res_populations: [array([0, 4, 6, 8, 7, 9, 5, 1, 3, 2], dtype=int64), array([0, 4, 6, 8, 7, 9, 3, 5, 1, 2], dtype=int64), array([0, 4, 8, 6, 7, 9, 5, 1, 3, 2], dtype=int64), array([0, 2, 1, 5, 3, 9, 7, 6, 8, 4], dtype=int64)]
2025-08-05 09:51:40,568 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:40,568 - ExploitationExpert - INFO - populations: [{'tour': array([5, 4, 9, 7, 0, 1, 2, 3, 6, 8], dtype=int64), 'cur_cost': 2078.0}, {'tour': [3, 5, 2, 4, 6, 9, 7, 8, 0, 1], 'cur_cost': 1732.0}, {'tour': [6, 7, 3, 2, 5, 1, 0, 4, 8, 9], 'cur_cost': 1601.0}, {'tour': [9, 2, 3, 5, 1, 0, 8, 6, 4, 7], 'cur_cost': 1588.0}, {'tour': array([0, 2, 9, 4, 5, 3, 6, 1, 8, 7], dtype=int64), 'cur_cost': 2495.0}, {'tour': [7, 6, 4, 8, 9, 2, 1, 3, 5, 0], 'cur_cost': 1826.0}, {'tour': [6, 8, 3, 7, 5, 1, 2, 9, 4, 0], 'cur_cost': 1649.0}, {'tour': [8, 7, 5, 9, 6, 4, 0, 2, 3, 1], 'cur_cost': 1680.0}, {'tour': [4, 7, 5, 3, 1, 0, 8, 6, 2, 9], 'cur_cost': 1941.0}, {'tour': [2, 5, 3, 8, 1, 7, 9, 6, 4, 0], 'cur_cost': 1909.0}]
2025-08-05 09:51:40,569 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:40,569 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 17, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 17, 'cache_hits': 0, 'similarity_calculations': 84, 'cache_hit_rate': 0.0, 'cache_size': 84}}
2025-08-05 09:51:40,569 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([0, 2, 9, 4, 5, 3, 6, 1, 8, 7], dtype=int64), 'cur_cost': 2495.0, 'intermediate_solutions': [{'tour': array([2, 1, 4, 6, 3, 0, 5, 8, 7, 9]), 'cur_cost': 2161.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 2, 1, 4, 3, 0, 5, 8, 7, 9]), 'cur_cost': 2269.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 6, 2, 1, 4, 0, 5, 8, 7, 9]), 'cur_cost': 2069.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 6, 2, 1, 3, 0, 5, 8, 7, 9]), 'cur_cost': 2051.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 3, 6, 2, 1, 0, 5, 8, 7, 9]), 'cur_cost': 2303.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:40,569 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 2495.00)
2025-08-05 09:51:40,570 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:51:40,570 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:51:40,570 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,570 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:40,570 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,570 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,570 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,571 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,571 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1974.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:40,571 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [5, 6, 8, 1, 9, 3, 2, 4, 0, 7], 'cur_cost': 1974.0, 'intermediate_solutions': [{'tour': [7, 3, 4, 8, 9, 2, 1, 6, 5, 0], 'cur_cost': 2400.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 6, 4, 8, 9, 2, 1, 5, 3, 0], 'cur_cost': 1730.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 6, 8, 9, 4, 2, 1, 3, 5, 0], 'cur_cost': 1851.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,571 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1974.00)
2025-08-05 09:51:40,571 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:51:40,571 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:51:40,571 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,572 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 09:51:40,572 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,572 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,572 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,572 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,572 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1617.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:40,572 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [7, 9, 8, 0, 4, 2, 1, 3, 5, 6], 'cur_cost': 1617.0, 'intermediate_solutions': [{'tour': [6, 8, 5, 7, 3, 1, 2, 9, 4, 0], 'cur_cost': 1705.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 8, 3, 7, 0, 4, 9, 2, 1, 5], 'cur_cost': 1869.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 3, 7, 5, 1, 6, 2, 9, 4, 0], 'cur_cost': 2106.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,573 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 1617.00)
2025-08-05 09:51:40,573 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:51:40,573 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:51:40,573 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,573 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 09:51:40,573 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,574 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,574 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,574 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,574 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1622.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:40,574 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [5, 2, 1, 3, 8, 6, 7, 9, 4, 0], 'cur_cost': 1622.0, 'intermediate_solutions': [{'tour': [8, 7, 5, 9, 6, 1, 0, 2, 3, 4], 'cur_cost': 2021.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 7, 8, 9, 6, 4, 0, 2, 3, 1], 'cur_cost': 1476.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 7, 5, 6, 4, 0, 2, 3, 9, 1], 'cur_cost': 1896.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,574 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 1622.00)
2025-08-05 09:51:40,574 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:51:40,574 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:51:40,574 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,575 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:40,575 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,575 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,575 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,575 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,575 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1922.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:40,575 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [5, 9, 1, 4, 6, 8, 7, 3, 2, 0], 'cur_cost': 1922.0, 'intermediate_solutions': [{'tour': [4, 7, 5, 3, 1, 0, 2, 6, 8, 9], 'cur_cost': 1924.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 7, 5, 3, 1, 6, 8, 0, 2, 9], 'cur_cost': 1959.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 5, 3, 1, 7, 0, 8, 6, 2, 9], 'cur_cost': 2165.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,575 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1922.00)
2025-08-05 09:51:40,575 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:51:40,575 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:51:40,575 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,576 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:40,576 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,577 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,577 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,577 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,577 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2170.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:40,577 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [5, 8, 3, 4, 2, 7, 9, 6, 0, 1], 'cur_cost': 2170.0, 'intermediate_solutions': [{'tour': [6, 5, 3, 8, 1, 7, 9, 2, 4, 0], 'cur_cost': 2121.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 5, 3, 9, 7, 1, 8, 6, 4, 0], 'cur_cost': 1683.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 5, 3, 8, 0, 1, 7, 9, 6, 4], 'cur_cost': 2001.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,578 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 2170.00)
2025-08-05 09:51:40,579 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:40,579 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:40,581 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([5, 4, 9, 7, 0, 1, 2, 3, 6, 8], dtype=int64), 'cur_cost': 2078.0, 'intermediate_solutions': [{'tour': array([9, 2, 7, 3, 0, 1, 6, 4, 8, 5]), 'cur_cost': 2297.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 9, 2, 7, 0, 1, 6, 4, 8, 5]), 'cur_cost': 2247.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 3, 9, 2, 7, 1, 6, 4, 8, 5]), 'cur_cost': 2437.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 3, 9, 2, 0, 1, 6, 4, 8, 5]), 'cur_cost': 2173.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 0, 3, 9, 2, 1, 6, 4, 8, 5]), 'cur_cost': 2149.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [3, 5, 2, 4, 6, 9, 7, 8, 0, 1], 'cur_cost': 1732.0, 'intermediate_solutions': [{'tour': [2, 0, 5, 9, 3, 1, 4, 8, 6, 7], 'cur_cost': 1910.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 4, 5, 9, 3, 1, 7, 6, 8, 0], 'cur_cost': 1931.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 5, 0, 9, 3, 1, 8, 6, 7], 'cur_cost': 2234.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [6, 7, 3, 2, 5, 1, 0, 4, 8, 9], 'cur_cost': 1601.0, 'intermediate_solutions': [{'tour': [9, 8, 2, 4, 5, 6, 7, 0, 3, 1], 'cur_cost': 2366.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 2, 1, 3, 9, 7, 6, 5, 4], 'cur_cost': 1831.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 2, 4, 5, 7, 9, 3, 1, 6], 'cur_cost': 2163.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [9, 2, 3, 5, 1, 0, 8, 6, 4, 7], 'cur_cost': 1588.0, 'intermediate_solutions': [{'tour': [9, 5, 8, 0, 2, 3, 1, 4, 6, 7], 'cur_cost': 1813.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 7, 6, 4, 1, 3, 2, 9, 8], 'cur_cost': 1915.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 8, 2, 9, 3, 1, 4, 6, 7], 'cur_cost': 2258.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 2, 9, 4, 5, 3, 6, 1, 8, 7], dtype=int64), 'cur_cost': 2495.0, 'intermediate_solutions': [{'tour': array([2, 1, 4, 6, 3, 0, 5, 8, 7, 9]), 'cur_cost': 2161.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 2, 1, 4, 3, 0, 5, 8, 7, 9]), 'cur_cost': 2269.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 6, 2, 1, 4, 0, 5, 8, 7, 9]), 'cur_cost': 2069.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 6, 2, 1, 3, 0, 5, 8, 7, 9]), 'cur_cost': 2051.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 3, 6, 2, 1, 0, 5, 8, 7, 9]), 'cur_cost': 2303.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [5, 6, 8, 1, 9, 3, 2, 4, 0, 7], 'cur_cost': 1974.0, 'intermediate_solutions': [{'tour': [7, 3, 4, 8, 9, 2, 1, 6, 5, 0], 'cur_cost': 2400.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 6, 4, 8, 9, 2, 1, 5, 3, 0], 'cur_cost': 1730.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 6, 8, 9, 4, 2, 1, 3, 5, 0], 'cur_cost': 1851.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [7, 9, 8, 0, 4, 2, 1, 3, 5, 6], 'cur_cost': 1617.0, 'intermediate_solutions': [{'tour': [6, 8, 5, 7, 3, 1, 2, 9, 4, 0], 'cur_cost': 1705.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 8, 3, 7, 0, 4, 9, 2, 1, 5], 'cur_cost': 1869.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 3, 7, 5, 1, 6, 2, 9, 4, 0], 'cur_cost': 2106.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [5, 2, 1, 3, 8, 6, 7, 9, 4, 0], 'cur_cost': 1622.0, 'intermediate_solutions': [{'tour': [8, 7, 5, 9, 6, 1, 0, 2, 3, 4], 'cur_cost': 2021.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 7, 8, 9, 6, 4, 0, 2, 3, 1], 'cur_cost': 1476.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 7, 5, 6, 4, 0, 2, 3, 9, 1], 'cur_cost': 1896.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [5, 9, 1, 4, 6, 8, 7, 3, 2, 0], 'cur_cost': 1922.0, 'intermediate_solutions': [{'tour': [4, 7, 5, 3, 1, 0, 2, 6, 8, 9], 'cur_cost': 1924.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 7, 5, 3, 1, 6, 8, 0, 2, 9], 'cur_cost': 1959.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 5, 3, 1, 7, 0, 8, 6, 2, 9], 'cur_cost': 2165.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [5, 8, 3, 4, 2, 7, 9, 6, 0, 1], 'cur_cost': 2170.0, 'intermediate_solutions': [{'tour': [6, 5, 3, 8, 1, 7, 9, 2, 4, 0], 'cur_cost': 2121.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 5, 3, 9, 7, 1, 8, 6, 4, 0], 'cur_cost': 1683.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 5, 3, 8, 0, 1, 7, 9, 6, 4], 'cur_cost': 2001.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:51:40,581 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:40,582 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:40,583 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1588.000, 多样性=0.891
2025-08-05 09:51:40,584 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 09:51:40,584 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 09:51:40,584 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:40,584 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 70, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'slight_improvement', 'improvement_rate': 0.006013206102442446, 'best_improvement': 0.03699211643420255}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.024330900243308796}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.025897354590581224, 'recent_improvements': [-0.028590549866207973, -0.01006499985908392, -0.08038525904737044], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 1265.0, 'new_best_cost': 1265.0, 'quality_improvement': 0.0, 'old_diversity': 0.5666666666666667, 'new_diversity': 0.5666666666666667, 'diversity_change': 0.0}, 'suggestions': ['改进缓慢，建议调整策略平衡或增加扰动强度', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配']}
2025-08-05 09:51:40,585 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 09:51:40,585 - __main__ - INFO - simple2_10 开始进化第 3 代
2025-08-05 09:51:40,585 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 09:51:40,585 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:40,586 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1588.000, 多样性=0.891
2025-08-05 09:51:40,586 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:40,587 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.891
2025-08-05 09:51:40,587 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:40,587 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.567
2025-08-05 09:51:40,589 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 09:51:40,590 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:40,590 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 09:51:40,590 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 09:51:40,601 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.286, 适应度梯度: 101.943, 聚类评分: 0.000, 覆盖率: 0.009, 收敛趋势: 0.000, 多样性: 0.633
2025-08-05 09:51:40,601 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 09:51:40,602 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:51:40,602 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple2_10
2025-08-05 09:51:40,606 - visualization.landscape_visualizer - INFO - 插值约束: 142 个点被约束到最小值 1265.00
2025-08-05 09:51:40,701 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple2_10\landscape_simple2_10_iter_8_20250805_095140.html
2025-08-05 09:51:40,744 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple2_10\dashboard_simple2_10_iter_8_20250805_095140.html
2025-08-05 09:51:40,744 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 8
2025-08-05 09:51:40,744 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 09:51:40,745 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1553秒
2025-08-05 09:51:40,745 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2857142857142857, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 101.94285714285715, 'local_optima_density': 0.2857142857142857, 'gradient_variance': 145084.0481632653, 'cluster_count': 0}, 'population_state': {'diversity': 0.6334379905808477, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0087, 'fitness_entropy': 0.8982265179691364, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.009)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 101.943)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358700.6018076, 'performance_metrics': {}}}
2025-08-05 09:51:40,745 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:40,745 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:40,746 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:40,746 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:40,746 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:51:40,746 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 09:51:40,746 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:51:40,747 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:40,747 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:40,747 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:51:40,747 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:40,747 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:40,747 - experts.management.collaboration_manager - INFO - 识别精英个体: {2, 3} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:40,748 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-05 09:51:40,748 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:40,748 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:40,748 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 1970.0
2025-08-05 09:51:40,753 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:40,753 - ExploitationExpert - INFO - res_population_costs: [1265.0, 1265, 1265, 1265.0]
2025-08-05 09:51:40,754 - ExploitationExpert - INFO - res_populations: [array([0, 4, 6, 8, 7, 9, 5, 1, 3, 2], dtype=int64), array([0, 4, 6, 8, 7, 9, 3, 5, 1, 2], dtype=int64), array([0, 4, 8, 6, 7, 9, 5, 1, 3, 2], dtype=int64), array([0, 2, 1, 5, 3, 9, 7, 6, 8, 4], dtype=int64)]
2025-08-05 09:51:40,755 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:40,755 - ExploitationExpert - INFO - populations: [{'tour': array([1, 7, 3, 6, 8, 9, 0, 4, 5, 2], dtype=int64), 'cur_cost': 1970.0}, {'tour': [3, 5, 2, 4, 6, 9, 7, 8, 0, 1], 'cur_cost': 1732.0}, {'tour': [6, 7, 3, 2, 5, 1, 0, 4, 8, 9], 'cur_cost': 1601.0}, {'tour': [9, 2, 3, 5, 1, 0, 8, 6, 4, 7], 'cur_cost': 1588.0}, {'tour': [0, 2, 9, 4, 5, 3, 6, 1, 8, 7], 'cur_cost': 2495.0}, {'tour': [5, 6, 8, 1, 9, 3, 2, 4, 0, 7], 'cur_cost': 1974.0}, {'tour': [7, 9, 8, 0, 4, 2, 1, 3, 5, 6], 'cur_cost': 1617.0}, {'tour': [5, 2, 1, 3, 8, 6, 7, 9, 4, 0], 'cur_cost': 1622.0}, {'tour': [5, 9, 1, 4, 6, 8, 7, 3, 2, 0], 'cur_cost': 1922.0}, {'tour': [5, 8, 3, 4, 2, 7, 9, 6, 0, 1], 'cur_cost': 2170.0}]
2025-08-05 09:51:40,755 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:40,756 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 18, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 18, 'cache_hits': 0, 'similarity_calculations': 88, 'cache_hit_rate': 0.0, 'cache_size': 88}}
2025-08-05 09:51:40,756 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([1, 7, 3, 6, 8, 9, 0, 4, 5, 2], dtype=int64), 'cur_cost': 1970.0, 'intermediate_solutions': [{'tour': array([9, 4, 5, 7, 0, 1, 2, 3, 6, 8]), 'cur_cost': 2032.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 9, 4, 5, 0, 1, 2, 3, 6, 8]), 'cur_cost': 1965.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 7, 9, 4, 5, 1, 2, 3, 6, 8]), 'cur_cost': 1849.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 7, 9, 4, 0, 1, 2, 3, 6, 8]), 'cur_cost': 1701.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 0, 7, 9, 4, 1, 2, 3, 6, 8]), 'cur_cost': 2116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:40,756 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1970.00)
2025-08-05 09:51:40,756 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:51:40,757 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:51:40,757 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,757 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 09:51:40,757 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,757 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,758 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,758 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,758 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1723.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:40,758 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 2, 3, 9, 7, 8, 4, 6, 5, 1], 'cur_cost': 1723.0, 'intermediate_solutions': [{'tour': [3, 5, 2, 9, 6, 4, 7, 8, 0, 1], 'cur_cost': 1842.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 2, 4, 6, 1, 0, 8, 7, 9], 'cur_cost': 1915.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 5, 2, 4, 9, 7, 8, 6, 0, 1], 'cur_cost': 1685.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,758 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1723.00)
2025-08-05 09:51:40,758 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:51:40,758 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:51:40,758 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,759 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:40,759 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,759 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,759 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,759 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,759 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2084.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:40,759 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [4, 7, 1, 6, 5, 3, 2, 9, 8, 0], 'cur_cost': 2084.0, 'intermediate_solutions': [{'tour': [6, 8, 3, 2, 5, 1, 0, 4, 7, 9], 'cur_cost': 1588.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 3, 2, 5, 1, 4, 0, 8, 9], 'cur_cost': 1687.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 6, 7, 3, 2, 5, 1, 0, 4, 8], 'cur_cost': 1601.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,760 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 2084.00)
2025-08-05 09:51:40,760 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:51:40,760 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:51:40,760 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,760 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:40,760 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,760 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,761 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,761 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,761 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1575.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:40,761 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [4, 8, 0, 3, 5, 1, 2, 9, 7, 6], 'cur_cost': 1575.0, 'intermediate_solutions': [{'tour': [9, 6, 3, 5, 1, 0, 8, 2, 4, 7], 'cur_cost': 2033.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 2, 3, 5, 1, 0, 7, 4, 6, 8], 'cur_cost': 1743.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 9, 2, 3, 5, 1, 0, 8, 6, 4], 'cur_cost': 1588.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,761 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 1575.00)
2025-08-05 09:51:40,761 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 09:51:40,761 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:40,761 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:40,762 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 2117.0
2025-08-05 09:51:40,767 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:40,767 - ExploitationExpert - INFO - res_population_costs: [1265.0, 1265, 1265, 1265.0]
2025-08-05 09:51:40,767 - ExploitationExpert - INFO - res_populations: [array([0, 4, 6, 8, 7, 9, 5, 1, 3, 2], dtype=int64), array([0, 4, 6, 8, 7, 9, 3, 5, 1, 2], dtype=int64), array([0, 4, 8, 6, 7, 9, 5, 1, 3, 2], dtype=int64), array([0, 2, 1, 5, 3, 9, 7, 6, 8, 4], dtype=int64)]
2025-08-05 09:51:40,768 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:40,769 - ExploitationExpert - INFO - populations: [{'tour': array([1, 7, 3, 6, 8, 9, 0, 4, 5, 2], dtype=int64), 'cur_cost': 1970.0}, {'tour': [0, 2, 3, 9, 7, 8, 4, 6, 5, 1], 'cur_cost': 1723.0}, {'tour': [4, 7, 1, 6, 5, 3, 2, 9, 8, 0], 'cur_cost': 2084.0}, {'tour': [4, 8, 0, 3, 5, 1, 2, 9, 7, 6], 'cur_cost': 1575.0}, {'tour': array([9, 0, 6, 4, 5, 3, 8, 2, 1, 7], dtype=int64), 'cur_cost': 2117.0}, {'tour': [5, 6, 8, 1, 9, 3, 2, 4, 0, 7], 'cur_cost': 1974.0}, {'tour': [7, 9, 8, 0, 4, 2, 1, 3, 5, 6], 'cur_cost': 1617.0}, {'tour': [5, 2, 1, 3, 8, 6, 7, 9, 4, 0], 'cur_cost': 1622.0}, {'tour': [5, 9, 1, 4, 6, 8, 7, 3, 2, 0], 'cur_cost': 1922.0}, {'tour': [5, 8, 3, 4, 2, 7, 9, 6, 0, 1], 'cur_cost': 2170.0}]
2025-08-05 09:51:40,769 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:40,769 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 19, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 19, 'cache_hits': 0, 'similarity_calculations': 93, 'cache_hit_rate': 0.0, 'cache_size': 93}}
2025-08-05 09:51:40,770 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([9, 0, 6, 4, 5, 3, 8, 2, 1, 7], dtype=int64), 'cur_cost': 2117.0, 'intermediate_solutions': [{'tour': array([9, 2, 0, 4, 5, 3, 6, 1, 8, 7]), 'cur_cost': 2135.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 9, 2, 0, 5, 3, 6, 1, 8, 7]), 'cur_cost': 2455.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 4, 9, 2, 0, 3, 6, 1, 8, 7]), 'cur_cost': 2501.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 4, 9, 2, 5, 3, 6, 1, 8, 7]), 'cur_cost': 2196.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 5, 4, 9, 2, 3, 6, 1, 8, 7]), 'cur_cost': 2589.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:40,770 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 2117.00)
2025-08-05 09:51:40,770 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:51:40,770 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:51:40,770 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,771 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:40,771 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,771 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,771 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,771 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,771 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2115.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:40,771 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [4, 7, 2, 6, 0, 8, 9, 3, 1, 5], 'cur_cost': 2115.0, 'intermediate_solutions': [{'tour': [5, 6, 8, 1, 0, 3, 2, 4, 9, 7], 'cur_cost': 2072.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 2, 3, 9, 1, 8, 6, 5, 7], 'cur_cost': 1974.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 6, 8, 1, 3, 2, 4, 0, 9, 7], 'cur_cost': 1789.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,772 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 2115.00)
2025-08-05 09:51:40,772 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:51:40,772 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:51:40,772 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,772 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:40,772 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,772 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,772 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,773 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,773 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1947.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:40,773 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [1, 8, 4, 9, 3, 2, 5, 7, 6, 0], 'cur_cost': 1947.0, 'intermediate_solutions': [{'tour': [7, 9, 8, 2, 4, 0, 1, 3, 5, 6], 'cur_cost': 1816.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 9, 8, 0, 2, 4, 1, 3, 5, 6], 'cur_cost': 1941.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 8, 0, 4, 2, 1, 3, 5, 6, 7], 'cur_cost': 1617.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,773 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1947.00)
2025-08-05 09:51:40,773 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:51:40,773 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:51:40,773 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,774 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 09:51:40,774 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,774 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,774 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,774 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,775 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1859.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:40,775 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [4, 2, 3, 5, 9, 8, 6, 7, 0, 1], 'cur_cost': 1859.0, 'intermediate_solutions': [{'tour': [2, 5, 1, 3, 8, 6, 7, 9, 4, 0], 'cur_cost': 1503.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 2, 4, 9, 7, 6, 8, 3, 1, 0], 'cur_cost': 1930.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 4, 2, 1, 3, 8, 6, 7, 9, 0], 'cur_cost': 1969.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,775 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1859.00)
2025-08-05 09:51:40,775 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:51:40,775 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:51:40,775 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,776 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 09:51:40,776 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,776 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,776 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,776 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,776 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2132.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:40,776 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 9, 3, 1, 4, 7, 5, 6, 8, 2], 'cur_cost': 2132.0, 'intermediate_solutions': [{'tour': [9, 5, 1, 4, 6, 8, 7, 3, 2, 0], 'cur_cost': 1736.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 9, 1, 4, 6, 8, 7, 3, 0, 2], 'cur_cost': 1912.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 9, 1, 4, 8, 6, 7, 3, 2, 0], 'cur_cost': 1922.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,776 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 2132.00)
2025-08-05 09:51:40,776 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 09:51:40,777 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:40,777 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:40,777 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 2435.0
2025-08-05 09:51:40,782 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:40,783 - ExploitationExpert - INFO - res_population_costs: [1265.0, 1265, 1265, 1265.0]
2025-08-05 09:51:40,783 - ExploitationExpert - INFO - res_populations: [array([0, 4, 6, 8, 7, 9, 5, 1, 3, 2], dtype=int64), array([0, 4, 6, 8, 7, 9, 3, 5, 1, 2], dtype=int64), array([0, 4, 8, 6, 7, 9, 5, 1, 3, 2], dtype=int64), array([0, 2, 1, 5, 3, 9, 7, 6, 8, 4], dtype=int64)]
2025-08-05 09:51:40,784 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:40,784 - ExploitationExpert - INFO - populations: [{'tour': array([1, 7, 3, 6, 8, 9, 0, 4, 5, 2], dtype=int64), 'cur_cost': 1970.0}, {'tour': [0, 2, 3, 9, 7, 8, 4, 6, 5, 1], 'cur_cost': 1723.0}, {'tour': [4, 7, 1, 6, 5, 3, 2, 9, 8, 0], 'cur_cost': 2084.0}, {'tour': [4, 8, 0, 3, 5, 1, 2, 9, 7, 6], 'cur_cost': 1575.0}, {'tour': array([9, 0, 6, 4, 5, 3, 8, 2, 1, 7], dtype=int64), 'cur_cost': 2117.0}, {'tour': [4, 7, 2, 6, 0, 8, 9, 3, 1, 5], 'cur_cost': 2115.0}, {'tour': [1, 8, 4, 9, 3, 2, 5, 7, 6, 0], 'cur_cost': 1947.0}, {'tour': [4, 2, 3, 5, 9, 8, 6, 7, 0, 1], 'cur_cost': 1859.0}, {'tour': [0, 9, 3, 1, 4, 7, 5, 6, 8, 2], 'cur_cost': 2132.0}, {'tour': array([7, 8, 1, 2, 6, 3, 4, 5, 0, 9], dtype=int64), 'cur_cost': 2435.0}]
2025-08-05 09:51:40,785 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:40,785 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 20, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 20, 'cache_hits': 0, 'similarity_calculations': 99, 'cache_hit_rate': 0.0, 'cache_size': 99}}
2025-08-05 09:51:40,785 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([7, 8, 1, 2, 6, 3, 4, 5, 0, 9], dtype=int64), 'cur_cost': 2435.0, 'intermediate_solutions': [{'tour': array([3, 8, 5, 4, 2, 7, 9, 6, 0, 1]), 'cur_cost': 2259.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 3, 8, 5, 2, 7, 9, 6, 0, 1]), 'cur_cost': 2292.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 4, 3, 8, 5, 7, 9, 6, 0, 1]), 'cur_cost': 2094.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 4, 3, 8, 2, 7, 9, 6, 0, 1]), 'cur_cost': 2237.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 2, 4, 3, 8, 7, 9, 6, 0, 1]), 'cur_cost': 1940.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:40,785 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 2435.00)
2025-08-05 09:51:40,785 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:40,785 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:40,787 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([1, 7, 3, 6, 8, 9, 0, 4, 5, 2], dtype=int64), 'cur_cost': 1970.0, 'intermediate_solutions': [{'tour': array([9, 4, 5, 7, 0, 1, 2, 3, 6, 8]), 'cur_cost': 2032.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 9, 4, 5, 0, 1, 2, 3, 6, 8]), 'cur_cost': 1965.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 7, 9, 4, 5, 1, 2, 3, 6, 8]), 'cur_cost': 1849.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 7, 9, 4, 0, 1, 2, 3, 6, 8]), 'cur_cost': 1701.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 0, 7, 9, 4, 1, 2, 3, 6, 8]), 'cur_cost': 2116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 3, 9, 7, 8, 4, 6, 5, 1], 'cur_cost': 1723.0, 'intermediate_solutions': [{'tour': [3, 5, 2, 9, 6, 4, 7, 8, 0, 1], 'cur_cost': 1842.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 2, 4, 6, 1, 0, 8, 7, 9], 'cur_cost': 1915.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 5, 2, 4, 9, 7, 8, 6, 0, 1], 'cur_cost': 1685.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [4, 7, 1, 6, 5, 3, 2, 9, 8, 0], 'cur_cost': 2084.0, 'intermediate_solutions': [{'tour': [6, 8, 3, 2, 5, 1, 0, 4, 7, 9], 'cur_cost': 1588.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 3, 2, 5, 1, 4, 0, 8, 9], 'cur_cost': 1687.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 6, 7, 3, 2, 5, 1, 0, 4, 8], 'cur_cost': 1601.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [4, 8, 0, 3, 5, 1, 2, 9, 7, 6], 'cur_cost': 1575.0, 'intermediate_solutions': [{'tour': [9, 6, 3, 5, 1, 0, 8, 2, 4, 7], 'cur_cost': 2033.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 2, 3, 5, 1, 0, 7, 4, 6, 8], 'cur_cost': 1743.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 9, 2, 3, 5, 1, 0, 8, 6, 4], 'cur_cost': 1588.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([9, 0, 6, 4, 5, 3, 8, 2, 1, 7], dtype=int64), 'cur_cost': 2117.0, 'intermediate_solutions': [{'tour': array([9, 2, 0, 4, 5, 3, 6, 1, 8, 7]), 'cur_cost': 2135.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 9, 2, 0, 5, 3, 6, 1, 8, 7]), 'cur_cost': 2455.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 4, 9, 2, 0, 3, 6, 1, 8, 7]), 'cur_cost': 2501.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 4, 9, 2, 5, 3, 6, 1, 8, 7]), 'cur_cost': 2196.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 5, 4, 9, 2, 3, 6, 1, 8, 7]), 'cur_cost': 2589.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [4, 7, 2, 6, 0, 8, 9, 3, 1, 5], 'cur_cost': 2115.0, 'intermediate_solutions': [{'tour': [5, 6, 8, 1, 0, 3, 2, 4, 9, 7], 'cur_cost': 2072.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 2, 3, 9, 1, 8, 6, 5, 7], 'cur_cost': 1974.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 6, 8, 1, 3, 2, 4, 0, 9, 7], 'cur_cost': 1789.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [1, 8, 4, 9, 3, 2, 5, 7, 6, 0], 'cur_cost': 1947.0, 'intermediate_solutions': [{'tour': [7, 9, 8, 2, 4, 0, 1, 3, 5, 6], 'cur_cost': 1816.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 9, 8, 0, 2, 4, 1, 3, 5, 6], 'cur_cost': 1941.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 8, 0, 4, 2, 1, 3, 5, 6, 7], 'cur_cost': 1617.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [4, 2, 3, 5, 9, 8, 6, 7, 0, 1], 'cur_cost': 1859.0, 'intermediate_solutions': [{'tour': [2, 5, 1, 3, 8, 6, 7, 9, 4, 0], 'cur_cost': 1503.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 2, 4, 9, 7, 6, 8, 3, 1, 0], 'cur_cost': 1930.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 4, 2, 1, 3, 8, 6, 7, 9, 0], 'cur_cost': 1969.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 3, 1, 4, 7, 5, 6, 8, 2], 'cur_cost': 2132.0, 'intermediate_solutions': [{'tour': [9, 5, 1, 4, 6, 8, 7, 3, 2, 0], 'cur_cost': 1736.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 9, 1, 4, 6, 8, 7, 3, 0, 2], 'cur_cost': 1912.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 9, 1, 4, 8, 6, 7, 3, 2, 0], 'cur_cost': 1922.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 8, 1, 2, 6, 3, 4, 5, 0, 9], dtype=int64), 'cur_cost': 2435.0, 'intermediate_solutions': [{'tour': array([3, 8, 5, 4, 2, 7, 9, 6, 0, 1]), 'cur_cost': 2259.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 3, 8, 5, 2, 7, 9, 6, 0, 1]), 'cur_cost': 2292.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 4, 3, 8, 5, 7, 9, 6, 0, 1]), 'cur_cost': 2094.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 4, 3, 8, 2, 7, 9, 6, 0, 1]), 'cur_cost': 2237.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 2, 4, 3, 8, 7, 9, 6, 0, 1]), 'cur_cost': 1940.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 09:51:40,787 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:40,787 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:40,788 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1575.000, 多样性=0.893
2025-08-05 09:51:40,788 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 09:51:40,788 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 09:51:40,788 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:40,788 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.009788175682420592, 'best_improvement': 0.00818639798488665}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.002493765586035053}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.008039102980763177, 'recent_improvements': [-0.01006499985908392, -0.08038525904737044, 0.006013206102442446], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 1265.0, 'new_best_cost': 1265.0, 'quality_improvement': 0.0, 'old_diversity': 0.5666666666666667, 'new_diversity': 0.5666666666666667, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:51:40,789 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 09:51:40,789 - __main__ - INFO - simple2_10 开始进化第 4 代
2025-08-05 09:51:40,789 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 09:51:40,789 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:40,789 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1575.000, 多样性=0.893
2025-08-05 09:51:40,790 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:40,790 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.893
2025-08-05 09:51:40,790 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:40,791 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.567
2025-08-05 09:51:40,793 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 09:51:40,793 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:40,793 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 09:51:40,793 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 09:51:40,801 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.357, 适应度梯度: 16.557, 聚类评分: 0.000, 覆盖率: 0.010, 收敛趋势: 0.000, 多样性: 0.622
2025-08-05 09:51:40,802 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 09:51:40,802 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:51:40,802 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple2_10
2025-08-05 09:51:40,805 - visualization.landscape_visualizer - INFO - 插值约束: 97 个点被约束到最小值 1265.00
2025-08-05 09:51:40,879 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple2_10\landscape_simple2_10_iter_9_20250805_095140.html
2025-08-05 09:51:40,920 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple2_10\dashboard_simple2_10_iter_9_20250805_095140.html
2025-08-05 09:51:40,921 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 9
2025-08-05 09:51:40,921 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 09:51:40,921 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1279秒
2025-08-05 09:51:40,921 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.35714285714285715, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 16.55714285714286, 'local_optima_density': 0.35714285714285715, 'gradient_variance': 131399.40673469385, 'cluster_count': 0}, 'population_state': {'diversity': 0.6224489795918366, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0097, 'fitness_entropy': 0.8982265179691364, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.010)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 16.557)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358700.80207, 'performance_metrics': {}}}
2025-08-05 09:51:40,921 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:40,921 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:40,922 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:40,922 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:40,922 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 09:51:40,922 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 09:51:40,922 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 09:51:40,922 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:40,922 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:40,922 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 09:51:40,922 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:40,922 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:40,922 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 3} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:40,922 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:51:40,922 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:51:40,922 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,923 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 09:51:40,923 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,923 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,923 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,923 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,923 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1813.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:40,924 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 2, 3, 1, 9, 7, 6, 8, 4, 5], 'cur_cost': 1813.0, 'intermediate_solutions': [{'tour': [1, 2, 3, 6, 8, 9, 0, 4, 5, 7], 'cur_cost': 1865.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 7, 3, 6, 2, 5, 4, 0, 9, 8], 'cur_cost': 2404.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 3, 8, 9, 0, 6, 4, 5, 2], 'cur_cost': 2181.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,924 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1813.00)
2025-08-05 09:51:40,924 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:51:40,924 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:51:40,924 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,924 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:40,924 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,924 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,925 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,925 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,925 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1986.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:40,925 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [3, 4, 7, 2, 5, 1, 9, 8, 6, 0], 'cur_cost': 1986.0, 'intermediate_solutions': [{'tour': [0, 2, 9, 3, 7, 8, 4, 6, 5, 1], 'cur_cost': 1922.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 3, 9, 5, 6, 4, 8, 7, 1], 'cur_cost': 1959.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 2, 3, 7, 8, 4, 6, 5, 1], 'cur_cost': 1924.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,925 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 1986.00)
2025-08-05 09:51:40,925 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:51:40,925 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:51:40,925 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,926 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:40,926 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,926 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,926 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,926 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,926 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1457.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:40,926 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [5, 9, 7, 8, 0, 4, 6, 3, 2, 1], 'cur_cost': 1457.0, 'intermediate_solutions': [{'tour': [4, 7, 1, 6, 0, 3, 2, 9, 8, 5], 'cur_cost': 2448.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 6, 1, 7, 5, 3, 2, 9, 8, 0], 'cur_cost': 1854.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 7, 1, 5, 6, 3, 2, 9, 8, 0], 'cur_cost': 1995.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,926 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1457.00)
2025-08-05 09:51:40,927 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:51:40,927 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:51:40,927 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,927 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 09:51:40,927 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,927 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,927 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,928 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,928 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2251.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:40,928 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [5, 0, 6, 3, 4, 2, 8, 9, 7, 1], 'cur_cost': 2251.0, 'intermediate_solutions': [{'tour': [2, 8, 0, 3, 5, 1, 4, 9, 7, 6], 'cur_cost': 2033.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 0, 5, 3, 1, 2, 9, 7, 6], 'cur_cost': 1671.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 8, 0, 3, 5, 1, 2, 9, 7, 6], 'cur_cost': 1575.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,928 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 2251.00)
2025-08-05 09:51:40,928 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 09:51:40,928 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:40,928 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:40,928 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 2116.0
2025-08-05 09:51:40,934 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:40,934 - ExploitationExpert - INFO - res_population_costs: [1265.0, 1265, 1265, 1265.0]
2025-08-05 09:51:40,935 - ExploitationExpert - INFO - res_populations: [array([0, 4, 6, 8, 7, 9, 5, 1, 3, 2], dtype=int64), array([0, 4, 6, 8, 7, 9, 3, 5, 1, 2], dtype=int64), array([0, 4, 8, 6, 7, 9, 5, 1, 3, 2], dtype=int64), array([0, 2, 1, 5, 3, 9, 7, 6, 8, 4], dtype=int64)]
2025-08-05 09:51:40,936 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:40,936 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 3, 1, 9, 7, 6, 8, 4, 5], 'cur_cost': 1813.0}, {'tour': [3, 4, 7, 2, 5, 1, 9, 8, 6, 0], 'cur_cost': 1986.0}, {'tour': [5, 9, 7, 8, 0, 4, 6, 3, 2, 1], 'cur_cost': 1457.0}, {'tour': [5, 0, 6, 3, 4, 2, 8, 9, 7, 1], 'cur_cost': 2251.0}, {'tour': array([8, 6, 7, 5, 4, 2, 9, 0, 3, 1], dtype=int64), 'cur_cost': 2116.0}, {'tour': [4, 7, 2, 6, 0, 8, 9, 3, 1, 5], 'cur_cost': 2115.0}, {'tour': [1, 8, 4, 9, 3, 2, 5, 7, 6, 0], 'cur_cost': 1947.0}, {'tour': [4, 2, 3, 5, 9, 8, 6, 7, 0, 1], 'cur_cost': 1859.0}, {'tour': [0, 9, 3, 1, 4, 7, 5, 6, 8, 2], 'cur_cost': 2132.0}, {'tour': [7, 8, 1, 2, 6, 3, 4, 5, 0, 9], 'cur_cost': 2435.0}]
2025-08-05 09:51:40,936 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:40,936 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 21, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 21, 'cache_hits': 0, 'similarity_calculations': 106, 'cache_hit_rate': 0.0, 'cache_size': 106}}
2025-08-05 09:51:40,937 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([8, 6, 7, 5, 4, 2, 9, 0, 3, 1], dtype=int64), 'cur_cost': 2116.0, 'intermediate_solutions': [{'tour': array([6, 0, 9, 4, 5, 3, 8, 2, 1, 7]), 'cur_cost': 2236.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 6, 0, 9, 5, 3, 8, 2, 1, 7]), 'cur_cost': 2101.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 4, 6, 0, 9, 3, 8, 2, 1, 7]), 'cur_cost': 2218.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([9, 4, 6, 0, 5, 3, 8, 2, 1, 7]), 'cur_cost': 2091.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([9, 5, 4, 6, 0, 3, 8, 2, 1, 7]), 'cur_cost': 2138.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:40,937 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 2116.00)
2025-08-05 09:51:40,937 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:51:40,937 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:51:40,937 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,937 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:40,937 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,938 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,938 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,938 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,938 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1727.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:40,938 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [4, 6, 9, 5, 3, 2, 1, 0, 8, 7], 'cur_cost': 1727.0, 'intermediate_solutions': [{'tour': [4, 7, 2, 5, 0, 8, 9, 3, 1, 6], 'cur_cost': 2189.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 8, 0, 6, 2, 7, 4, 3, 1, 5], 'cur_cost': 2051.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 2, 6, 0, 8, 9, 3, 1, 5, 4], 'cur_cost': 2115.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,938 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1727.00)
2025-08-05 09:51:40,938 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:51:40,938 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:51:40,938 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,939 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 09:51:40,939 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,939 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,939 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,939 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,939 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1570.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:40,939 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 6, 4, 2, 3, 1, 5, 9, 7, 8], 'cur_cost': 1570.0, 'intermediate_solutions': [{'tour': [1, 8, 7, 9, 3, 2, 5, 4, 6, 0], 'cur_cost': 1964.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 9, 4, 3, 2, 5, 7, 6, 0], 'cur_cost': 2062.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 5, 8, 4, 9, 3, 2, 7, 6, 0], 'cur_cost': 1930.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,940 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1570.00)
2025-08-05 09:51:40,940 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:51:40,940 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:51:40,940 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:40,940 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 09:51:40,940 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,940 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,941 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,941 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:40,941 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1823.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:40,941 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [7, 3, 5, 9, 8, 4, 6, 0, 2, 1], 'cur_cost': 1823.0, 'intermediate_solutions': [{'tour': [4, 2, 3, 5, 9, 6, 8, 7, 0, 1], 'cur_cost': 1880.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 2, 1, 0, 7, 6, 8, 9, 5, 3], 'cur_cost': 1831.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 4, 2, 5, 9, 8, 6, 7, 0, 1], 'cur_cost': 1892.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:40,941 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1823.00)
2025-08-05 09:51:40,941 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:51:40,941 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:40,941 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:40,941 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 2235.0
2025-08-05 09:51:40,947 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:40,947 - ExploitationExpert - INFO - res_population_costs: [1265.0, 1265, 1265, 1265.0]
2025-08-05 09:51:40,947 - ExploitationExpert - INFO - res_populations: [array([0, 4, 6, 8, 7, 9, 5, 1, 3, 2], dtype=int64), array([0, 4, 6, 8, 7, 9, 3, 5, 1, 2], dtype=int64), array([0, 4, 8, 6, 7, 9, 5, 1, 3, 2], dtype=int64), array([0, 2, 1, 5, 3, 9, 7, 6, 8, 4], dtype=int64)]
2025-08-05 09:51:40,948 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:40,948 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 3, 1, 9, 7, 6, 8, 4, 5], 'cur_cost': 1813.0}, {'tour': [3, 4, 7, 2, 5, 1, 9, 8, 6, 0], 'cur_cost': 1986.0}, {'tour': [5, 9, 7, 8, 0, 4, 6, 3, 2, 1], 'cur_cost': 1457.0}, {'tour': [5, 0, 6, 3, 4, 2, 8, 9, 7, 1], 'cur_cost': 2251.0}, {'tour': array([8, 6, 7, 5, 4, 2, 9, 0, 3, 1], dtype=int64), 'cur_cost': 2116.0}, {'tour': [4, 6, 9, 5, 3, 2, 1, 0, 8, 7], 'cur_cost': 1727.0}, {'tour': [0, 6, 4, 2, 3, 1, 5, 9, 7, 8], 'cur_cost': 1570.0}, {'tour': [7, 3, 5, 9, 8, 4, 6, 0, 2, 1], 'cur_cost': 1823.0}, {'tour': array([7, 0, 2, 5, 6, 4, 1, 9, 3, 8], dtype=int64), 'cur_cost': 2235.0}, {'tour': [7, 8, 1, 2, 6, 3, 4, 5, 0, 9], 'cur_cost': 2435.0}]
2025-08-05 09:51:40,949 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:40,949 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 22, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 22, 'cache_hits': 0, 'similarity_calculations': 114, 'cache_hit_rate': 0.0, 'cache_size': 114}}
2025-08-05 09:51:40,949 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([7, 0, 2, 5, 6, 4, 1, 9, 3, 8], dtype=int64), 'cur_cost': 2235.0, 'intermediate_solutions': [{'tour': array([3, 9, 0, 1, 4, 7, 5, 6, 8, 2]), 'cur_cost': 2152.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 3, 9, 0, 4, 7, 5, 6, 8, 2]), 'cur_cost': 1808.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 1, 3, 9, 0, 7, 5, 6, 8, 2]), 'cur_cost': 2194.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 1, 3, 9, 4, 7, 5, 6, 8, 2]), 'cur_cost': 2068.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 4, 1, 3, 9, 7, 5, 6, 8, 2]), 'cur_cost': 1786.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:40,950 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 2235.00)
2025-08-05 09:51:40,950 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 09:51:40,950 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:40,950 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:40,950 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 2258.0
2025-08-05 09:51:40,956 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:40,956 - ExploitationExpert - INFO - res_population_costs: [1265.0, 1265, 1265, 1265.0]
2025-08-05 09:51:40,956 - ExploitationExpert - INFO - res_populations: [array([0, 4, 6, 8, 7, 9, 5, 1, 3, 2], dtype=int64), array([0, 4, 6, 8, 7, 9, 3, 5, 1, 2], dtype=int64), array([0, 4, 8, 6, 7, 9, 5, 1, 3, 2], dtype=int64), array([0, 2, 1, 5, 3, 9, 7, 6, 8, 4], dtype=int64)]
2025-08-05 09:51:40,957 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:40,957 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 3, 1, 9, 7, 6, 8, 4, 5], 'cur_cost': 1813.0}, {'tour': [3, 4, 7, 2, 5, 1, 9, 8, 6, 0], 'cur_cost': 1986.0}, {'tour': [5, 9, 7, 8, 0, 4, 6, 3, 2, 1], 'cur_cost': 1457.0}, {'tour': [5, 0, 6, 3, 4, 2, 8, 9, 7, 1], 'cur_cost': 2251.0}, {'tour': array([8, 6, 7, 5, 4, 2, 9, 0, 3, 1], dtype=int64), 'cur_cost': 2116.0}, {'tour': [4, 6, 9, 5, 3, 2, 1, 0, 8, 7], 'cur_cost': 1727.0}, {'tour': [0, 6, 4, 2, 3, 1, 5, 9, 7, 8], 'cur_cost': 1570.0}, {'tour': [7, 3, 5, 9, 8, 4, 6, 0, 2, 1], 'cur_cost': 1823.0}, {'tour': array([7, 0, 2, 5, 6, 4, 1, 9, 3, 8], dtype=int64), 'cur_cost': 2235.0}, {'tour': array([9, 4, 5, 0, 1, 6, 8, 2, 3, 7], dtype=int64), 'cur_cost': 2258.0}]
2025-08-05 09:51:40,957 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:40,958 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 23, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 23, 'cache_hits': 0, 'similarity_calculations': 123, 'cache_hit_rate': 0.0, 'cache_size': 123}}
2025-08-05 09:51:40,958 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([9, 4, 5, 0, 1, 6, 8, 2, 3, 7], dtype=int64), 'cur_cost': 2258.0, 'intermediate_solutions': [{'tour': array([1, 8, 7, 2, 6, 3, 4, 5, 0, 9]), 'cur_cost': 2706.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 1, 8, 7, 6, 3, 4, 5, 0, 9]), 'cur_cost': 2381.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 2, 1, 8, 7, 3, 4, 5, 0, 9]), 'cur_cost': 2461.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 2, 1, 8, 6, 3, 4, 5, 0, 9]), 'cur_cost': 2324.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 6, 2, 1, 8, 3, 4, 5, 0, 9]), 'cur_cost': 2411.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:40,958 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 2258.00)
2025-08-05 09:51:40,958 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:40,958 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:40,959 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 3, 1, 9, 7, 6, 8, 4, 5], 'cur_cost': 1813.0, 'intermediate_solutions': [{'tour': [1, 2, 3, 6, 8, 9, 0, 4, 5, 7], 'cur_cost': 1865.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 7, 3, 6, 2, 5, 4, 0, 9, 8], 'cur_cost': 2404.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 3, 8, 9, 0, 6, 4, 5, 2], 'cur_cost': 2181.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [3, 4, 7, 2, 5, 1, 9, 8, 6, 0], 'cur_cost': 1986.0, 'intermediate_solutions': [{'tour': [0, 2, 9, 3, 7, 8, 4, 6, 5, 1], 'cur_cost': 1922.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 3, 9, 5, 6, 4, 8, 7, 1], 'cur_cost': 1959.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 2, 3, 7, 8, 4, 6, 5, 1], 'cur_cost': 1924.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [5, 9, 7, 8, 0, 4, 6, 3, 2, 1], 'cur_cost': 1457.0, 'intermediate_solutions': [{'tour': [4, 7, 1, 6, 0, 3, 2, 9, 8, 5], 'cur_cost': 2448.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 6, 1, 7, 5, 3, 2, 9, 8, 0], 'cur_cost': 1854.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 7, 1, 5, 6, 3, 2, 9, 8, 0], 'cur_cost': 1995.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [5, 0, 6, 3, 4, 2, 8, 9, 7, 1], 'cur_cost': 2251.0, 'intermediate_solutions': [{'tour': [2, 8, 0, 3, 5, 1, 4, 9, 7, 6], 'cur_cost': 2033.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 0, 5, 3, 1, 2, 9, 7, 6], 'cur_cost': 1671.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 8, 0, 3, 5, 1, 2, 9, 7, 6], 'cur_cost': 1575.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 6, 7, 5, 4, 2, 9, 0, 3, 1], dtype=int64), 'cur_cost': 2116.0, 'intermediate_solutions': [{'tour': array([6, 0, 9, 4, 5, 3, 8, 2, 1, 7]), 'cur_cost': 2236.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 6, 0, 9, 5, 3, 8, 2, 1, 7]), 'cur_cost': 2101.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 4, 6, 0, 9, 3, 8, 2, 1, 7]), 'cur_cost': 2218.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([9, 4, 6, 0, 5, 3, 8, 2, 1, 7]), 'cur_cost': 2091.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([9, 5, 4, 6, 0, 3, 8, 2, 1, 7]), 'cur_cost': 2138.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [4, 6, 9, 5, 3, 2, 1, 0, 8, 7], 'cur_cost': 1727.0, 'intermediate_solutions': [{'tour': [4, 7, 2, 5, 0, 8, 9, 3, 1, 6], 'cur_cost': 2189.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 8, 0, 6, 2, 7, 4, 3, 1, 5], 'cur_cost': 2051.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 2, 6, 0, 8, 9, 3, 1, 5, 4], 'cur_cost': 2115.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 4, 2, 3, 1, 5, 9, 7, 8], 'cur_cost': 1570.0, 'intermediate_solutions': [{'tour': [1, 8, 7, 9, 3, 2, 5, 4, 6, 0], 'cur_cost': 1964.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 9, 4, 3, 2, 5, 7, 6, 0], 'cur_cost': 2062.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 5, 8, 4, 9, 3, 2, 7, 6, 0], 'cur_cost': 1930.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [7, 3, 5, 9, 8, 4, 6, 0, 2, 1], 'cur_cost': 1823.0, 'intermediate_solutions': [{'tour': [4, 2, 3, 5, 9, 6, 8, 7, 0, 1], 'cur_cost': 1880.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 2, 1, 0, 7, 6, 8, 9, 5, 3], 'cur_cost': 1831.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 4, 2, 5, 9, 8, 6, 7, 0, 1], 'cur_cost': 1892.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 0, 2, 5, 6, 4, 1, 9, 3, 8], dtype=int64), 'cur_cost': 2235.0, 'intermediate_solutions': [{'tour': array([3, 9, 0, 1, 4, 7, 5, 6, 8, 2]), 'cur_cost': 2152.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 3, 9, 0, 4, 7, 5, 6, 8, 2]), 'cur_cost': 1808.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 1, 3, 9, 0, 7, 5, 6, 8, 2]), 'cur_cost': 2194.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 1, 3, 9, 4, 7, 5, 6, 8, 2]), 'cur_cost': 2068.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 4, 1, 3, 9, 7, 5, 6, 8, 2]), 'cur_cost': 1786.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([9, 4, 5, 0, 1, 6, 8, 2, 3, 7], dtype=int64), 'cur_cost': 2258.0, 'intermediate_solutions': [{'tour': array([1, 8, 7, 2, 6, 3, 4, 5, 0, 9]), 'cur_cost': 2706.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 1, 8, 7, 6, 3, 4, 5, 0, 9]), 'cur_cost': 2381.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 2, 1, 8, 7, 3, 4, 5, 0, 9]), 'cur_cost': 2461.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 2, 1, 8, 6, 3, 4, 5, 0, 9]), 'cur_cost': 2324.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 6, 2, 1, 8, 3, 4, 5, 0, 9]), 'cur_cost': 2411.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 09:51:40,960 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:40,960 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:40,961 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1457.000, 多样性=0.887
2025-08-05 09:51:40,961 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 09:51:40,961 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 09:51:40,961 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:40,962 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.06124608260638922, 'best_improvement': 0.07492063492063492}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.007462686567164226}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.035298541682474927, 'recent_improvements': [-0.08038525904737044, 0.006013206102442446, -0.009788175682420592], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 1265.0, 'new_best_cost': 1265.0, 'quality_improvement': 0.0, 'old_diversity': 0.5666666666666667, 'new_diversity': 0.5666666666666667, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:51:40,962 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 09:51:40,962 - __main__ - INFO - simple2_10 开始进化第 5 代
2025-08-05 09:51:40,962 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 09:51:40,962 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:40,963 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1457.000, 多样性=0.887
2025-08-05 09:51:40,963 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:40,964 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.887
2025-08-05 09:51:40,964 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:40,964 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.567
2025-08-05 09:51:40,966 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 09:51:40,966 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:40,966 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 09:51:40,966 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 09:51:40,975 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.357, 适应度梯度: 25.657, 聚类评分: 0.000, 覆盖率: 0.011, 收敛趋势: 0.000, 多样性: 0.628
2025-08-05 09:51:40,975 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 09:51:40,975 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:51:40,975 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple2_10
2025-08-05 09:51:40,981 - visualization.landscape_visualizer - INFO - 插值约束: 45 个点被约束到最小值 1265.00
2025-08-05 09:51:41,055 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple2_10\landscape_simple2_10_iter_10_20250805_095141.html
2025-08-05 09:51:41,119 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple2_10\dashboard_simple2_10_iter_10_20250805_095141.html
2025-08-05 09:51:41,120 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 10
2025-08-05 09:51:41,120 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 09:51:41,120 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1530秒
2025-08-05 09:51:41,120 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.35714285714285715, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 25.657142857142865, 'local_optima_density': 0.35714285714285715, 'gradient_variance': 97439.99673469388, 'cluster_count': 0}, 'population_state': {'diversity': 0.6279434850863422, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0107, 'fitness_entropy': 0.9178114620629283, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.011)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 25.657)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358700.9759824, 'performance_metrics': {}}}
2025-08-05 09:51:41,120 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:41,121 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:41,121 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:41,121 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:41,121 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 09:51:41,121 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 09:51:41,121 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 09:51:41,121 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:41,122 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:41,122 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 09:51:41,122 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:41,122 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:41,122 - experts.management.collaboration_manager - INFO - 识别精英个体: {2, 6} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:41,122 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:51:41,122 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:51:41,122 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,123 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 09:51:41,123 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,123 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,124 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,124 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,124 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1928.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:41,124 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [5, 2, 1, 0, 8, 9, 3, 7, 6, 4], 'cur_cost': 1928.0, 'intermediate_solutions': [{'tour': [0, 2, 3, 1, 9, 7, 6, 4, 8, 5], 'cur_cost': 1870.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 3, 1, 9, 8, 6, 7, 4, 5], 'cur_cost': 1976.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 3, 0, 1, 9, 7, 6, 8, 4, 5], 'cur_cost': 1823.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,124 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1928.00)
2025-08-05 09:51:41,125 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:51:41,125 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:51:41,125 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,125 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 09:51:41,126 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,126 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,126 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,126 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,127 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1874.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:41,127 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [5, 9, 2, 4, 8, 6, 0, 3, 1, 7], 'cur_cost': 1874.0, 'intermediate_solutions': [{'tour': [3, 4, 9, 2, 5, 1, 7, 8, 6, 0], 'cur_cost': 1936.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 4, 7, 2, 6, 8, 9, 1, 5, 0], 'cur_cost': 2220.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 4, 7, 2, 5, 9, 1, 8, 6, 0], 'cur_cost': 2190.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,127 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1874.00)
2025-08-05 09:51:41,127 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:51:41,127 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:51:41,127 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,128 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 09:51:41,128 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,128 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,128 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,129 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,129 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1891.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:41,129 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [7, 9, 3, 2, 4, 8, 0, 6, 5, 1], 'cur_cost': 1891.0, 'intermediate_solutions': [{'tour': [5, 9, 7, 8, 1, 4, 6, 3, 2, 0], 'cur_cost': 2116.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 9, 7, 8, 0, 4, 6, 2, 3, 1], 'cur_cost': 1483.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 9, 7, 8, 0, 4, 6, 3, 2, 1], 'cur_cost': 1457.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,129 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 1891.00)
2025-08-05 09:51:41,129 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:51:41,129 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:41,130 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:41,130 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 2450.0
2025-08-05 09:51:41,136 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:41,136 - ExploitationExpert - INFO - res_population_costs: [1265.0, 1265, 1265, 1265.0]
2025-08-05 09:51:41,136 - ExploitationExpert - INFO - res_populations: [array([0, 4, 6, 8, 7, 9, 5, 1, 3, 2], dtype=int64), array([0, 4, 6, 8, 7, 9, 3, 5, 1, 2], dtype=int64), array([0, 4, 8, 6, 7, 9, 5, 1, 3, 2], dtype=int64), array([0, 2, 1, 5, 3, 9, 7, 6, 8, 4], dtype=int64)]
2025-08-05 09:51:41,137 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:41,137 - ExploitationExpert - INFO - populations: [{'tour': [5, 2, 1, 0, 8, 9, 3, 7, 6, 4], 'cur_cost': 1928.0}, {'tour': [5, 9, 2, 4, 8, 6, 0, 3, 1, 7], 'cur_cost': 1874.0}, {'tour': [7, 9, 3, 2, 4, 8, 0, 6, 5, 1], 'cur_cost': 1891.0}, {'tour': array([2, 4, 1, 6, 3, 5, 0, 7, 9, 8], dtype=int64), 'cur_cost': 2450.0}, {'tour': [8, 6, 7, 5, 4, 2, 9, 0, 3, 1], 'cur_cost': 2116.0}, {'tour': [4, 6, 9, 5, 3, 2, 1, 0, 8, 7], 'cur_cost': 1727.0}, {'tour': [0, 6, 4, 2, 3, 1, 5, 9, 7, 8], 'cur_cost': 1570.0}, {'tour': [7, 3, 5, 9, 8, 4, 6, 0, 2, 1], 'cur_cost': 1823.0}, {'tour': [7, 0, 2, 5, 6, 4, 1, 9, 3, 8], 'cur_cost': 2235.0}, {'tour': [9, 4, 5, 0, 1, 6, 8, 2, 3, 7], 'cur_cost': 2258.0}]
2025-08-05 09:51:41,138 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:41,138 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 24, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 24, 'cache_hits': 0, 'similarity_calculations': 133, 'cache_hit_rate': 0.0, 'cache_size': 133}}
2025-08-05 09:51:41,139 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([2, 4, 1, 6, 3, 5, 0, 7, 9, 8], dtype=int64), 'cur_cost': 2450.0, 'intermediate_solutions': [{'tour': array([6, 0, 5, 3, 4, 2, 8, 9, 7, 1]), 'cur_cost': 2340.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 6, 0, 5, 4, 2, 8, 9, 7, 1]), 'cur_cost': 2340.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 3, 6, 0, 5, 2, 8, 9, 7, 1]), 'cur_cost': 2373.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 3, 6, 0, 4, 2, 8, 9, 7, 1]), 'cur_cost': 1861.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 4, 3, 6, 0, 2, 8, 9, 7, 1]), 'cur_cost': 2229.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:41,139 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 2450.00)
2025-08-05 09:51:41,139 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:51:41,139 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:51:41,139 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,140 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:41,140 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,140 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,140 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,140 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,140 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1724.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:41,140 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [2, 7, 6, 9, 3, 1, 5, 8, 4, 0], 'cur_cost': 1724.0, 'intermediate_solutions': [{'tour': [8, 3, 7, 5, 4, 2, 9, 0, 6, 1], 'cur_cost': 2554.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 6, 7, 5, 4, 2, 0, 9, 3, 1], 'cur_cost': 2039.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 8, 6, 7, 4, 2, 9, 0, 3, 1], 'cur_cost': 1942.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,141 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1724.00)
2025-08-05 09:51:41,141 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:51:41,141 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:51:41,141 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,141 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:41,141 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,141 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,142 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,142 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,142 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1908.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:41,142 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [2, 7, 6, 4, 9, 3, 1, 5, 8, 0], 'cur_cost': 1908.0, 'intermediate_solutions': [{'tour': [0, 6, 9, 5, 3, 2, 1, 4, 8, 7], 'cur_cost': 1807.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 6, 9, 5, 1, 2, 3, 0, 8, 7], 'cur_cost': 1705.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 6, 5, 3, 2, 1, 0, 8, 9, 7], 'cur_cost': 1816.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,142 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1908.00)
2025-08-05 09:51:41,142 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:51:41,142 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:51:41,142 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,143 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:41,143 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,143 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,143 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,143 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,143 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1648.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:41,143 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [4, 7, 6, 9, 3, 2, 1, 5, 8, 0], 'cur_cost': 1648.0, 'intermediate_solutions': [{'tour': [8, 6, 4, 2, 3, 1, 5, 9, 7, 0], 'cur_cost': 1576.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 4, 2, 3, 1, 5, 9, 8, 7], 'cur_cost': 1725.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 4, 2, 3, 1, 0, 5, 9, 7, 8], 'cur_cost': 1692.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,143 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 1648.00)
2025-08-05 09:51:41,144 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:51:41,144 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:51:41,144 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:41,144 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 09:51:41,144 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,144 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,144 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,144 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:41,145 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1803.0, 路径长度: 10, 收集中间解: 3
2025-08-05 09:51:41,145 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [4, 7, 3, 6, 8, 9, 2, 1, 5, 0], 'cur_cost': 1803.0, 'intermediate_solutions': [{'tour': [6, 3, 5, 9, 8, 4, 7, 0, 2, 1], 'cur_cost': 2127.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 5, 2, 0, 6, 4, 8, 9, 1], 'cur_cost': 1953.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 3, 5, 9, 8, 4, 6, 2, 0, 1], 'cur_cost': 2044.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:41,145 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1803.00)
2025-08-05 09:51:41,145 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:51:41,145 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:41,145 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:41,145 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 2007.0
2025-08-05 09:51:41,152 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:41,153 - ExploitationExpert - INFO - res_population_costs: [1265.0, 1265, 1265, 1265.0]
2025-08-05 09:51:41,153 - ExploitationExpert - INFO - res_populations: [array([0, 4, 6, 8, 7, 9, 5, 1, 3, 2], dtype=int64), array([0, 4, 6, 8, 7, 9, 3, 5, 1, 2], dtype=int64), array([0, 4, 8, 6, 7, 9, 5, 1, 3, 2], dtype=int64), array([0, 2, 1, 5, 3, 9, 7, 6, 8, 4], dtype=int64)]
2025-08-05 09:51:41,154 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:41,154 - ExploitationExpert - INFO - populations: [{'tour': [5, 2, 1, 0, 8, 9, 3, 7, 6, 4], 'cur_cost': 1928.0}, {'tour': [5, 9, 2, 4, 8, 6, 0, 3, 1, 7], 'cur_cost': 1874.0}, {'tour': [7, 9, 3, 2, 4, 8, 0, 6, 5, 1], 'cur_cost': 1891.0}, {'tour': array([2, 4, 1, 6, 3, 5, 0, 7, 9, 8], dtype=int64), 'cur_cost': 2450.0}, {'tour': [2, 7, 6, 9, 3, 1, 5, 8, 4, 0], 'cur_cost': 1724.0}, {'tour': [2, 7, 6, 4, 9, 3, 1, 5, 8, 0], 'cur_cost': 1908.0}, {'tour': [4, 7, 6, 9, 3, 2, 1, 5, 8, 0], 'cur_cost': 1648.0}, {'tour': [4, 7, 3, 6, 8, 9, 2, 1, 5, 0], 'cur_cost': 1803.0}, {'tour': array([9, 2, 1, 0, 7, 8, 6, 5, 3, 4], dtype=int64), 'cur_cost': 2007.0}, {'tour': [9, 4, 5, 0, 1, 6, 8, 2, 3, 7], 'cur_cost': 2258.0}]
2025-08-05 09:51:41,155 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:41,155 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 25, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 25, 'cache_hits': 0, 'similarity_calculations': 144, 'cache_hit_rate': 0.0, 'cache_size': 144}}
2025-08-05 09:51:41,156 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([9, 2, 1, 0, 7, 8, 6, 5, 3, 4], dtype=int64), 'cur_cost': 2007.0, 'intermediate_solutions': [{'tour': array([2, 0, 7, 5, 6, 4, 1, 9, 3, 8]), 'cur_cost': 2359.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 2, 0, 7, 6, 4, 1, 9, 3, 8]), 'cur_cost': 2214.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 5, 2, 0, 7, 4, 1, 9, 3, 8]), 'cur_cost': 2251.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 5, 2, 0, 6, 4, 1, 9, 3, 8]), 'cur_cost': 2015.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 6, 5, 2, 0, 4, 1, 9, 3, 8]), 'cur_cost': 1994.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:41,156 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 2007.00)
2025-08-05 09:51:41,156 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 09:51:41,157 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:41,157 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:41,157 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 2042.0
2025-08-05 09:51:41,164 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:41,164 - ExploitationExpert - INFO - res_population_costs: [1265.0, 1265, 1265, 1265.0]
2025-08-05 09:51:41,164 - ExploitationExpert - INFO - res_populations: [array([0, 4, 6, 8, 7, 9, 5, 1, 3, 2], dtype=int64), array([0, 4, 6, 8, 7, 9, 3, 5, 1, 2], dtype=int64), array([0, 4, 8, 6, 7, 9, 5, 1, 3, 2], dtype=int64), array([0, 2, 1, 5, 3, 9, 7, 6, 8, 4], dtype=int64)]
2025-08-05 09:51:41,165 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:41,165 - ExploitationExpert - INFO - populations: [{'tour': [5, 2, 1, 0, 8, 9, 3, 7, 6, 4], 'cur_cost': 1928.0}, {'tour': [5, 9, 2, 4, 8, 6, 0, 3, 1, 7], 'cur_cost': 1874.0}, {'tour': [7, 9, 3, 2, 4, 8, 0, 6, 5, 1], 'cur_cost': 1891.0}, {'tour': array([2, 4, 1, 6, 3, 5, 0, 7, 9, 8], dtype=int64), 'cur_cost': 2450.0}, {'tour': [2, 7, 6, 9, 3, 1, 5, 8, 4, 0], 'cur_cost': 1724.0}, {'tour': [2, 7, 6, 4, 9, 3, 1, 5, 8, 0], 'cur_cost': 1908.0}, {'tour': [4, 7, 6, 9, 3, 2, 1, 5, 8, 0], 'cur_cost': 1648.0}, {'tour': [4, 7, 3, 6, 8, 9, 2, 1, 5, 0], 'cur_cost': 1803.0}, {'tour': array([9, 2, 1, 0, 7, 8, 6, 5, 3, 4], dtype=int64), 'cur_cost': 2007.0}, {'tour': array([0, 7, 8, 2, 6, 4, 9, 3, 5, 1], dtype=int64), 'cur_cost': 2042.0}]
2025-08-05 09:51:41,166 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:41,166 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 26, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 26, 'cache_hits': 0, 'similarity_calculations': 156, 'cache_hit_rate': 0.0, 'cache_size': 156}}
2025-08-05 09:51:41,167 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([0, 7, 8, 2, 6, 4, 9, 3, 5, 1], dtype=int64), 'cur_cost': 2042.0, 'intermediate_solutions': [{'tour': array([5, 4, 9, 0, 1, 6, 8, 2, 3, 7]), 'cur_cost': 2267.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 5, 4, 9, 1, 6, 8, 2, 3, 7]), 'cur_cost': 2425.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 0, 5, 4, 9, 6, 8, 2, 3, 7]), 'cur_cost': 2286.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([9, 0, 5, 4, 1, 6, 8, 2, 3, 7]), 'cur_cost': 2322.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([9, 1, 0, 5, 4, 6, 8, 2, 3, 7]), 'cur_cost': 2062.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:41,167 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 2042.00)
2025-08-05 09:51:41,167 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:41,167 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:41,168 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [5, 2, 1, 0, 8, 9, 3, 7, 6, 4], 'cur_cost': 1928.0, 'intermediate_solutions': [{'tour': [0, 2, 3, 1, 9, 7, 6, 4, 8, 5], 'cur_cost': 1870.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 3, 1, 9, 8, 6, 7, 4, 5], 'cur_cost': 1976.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 3, 0, 1, 9, 7, 6, 8, 4, 5], 'cur_cost': 1823.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [5, 9, 2, 4, 8, 6, 0, 3, 1, 7], 'cur_cost': 1874.0, 'intermediate_solutions': [{'tour': [3, 4, 9, 2, 5, 1, 7, 8, 6, 0], 'cur_cost': 1936.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 4, 7, 2, 6, 8, 9, 1, 5, 0], 'cur_cost': 2220.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 4, 7, 2, 5, 9, 1, 8, 6, 0], 'cur_cost': 2190.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [7, 9, 3, 2, 4, 8, 0, 6, 5, 1], 'cur_cost': 1891.0, 'intermediate_solutions': [{'tour': [5, 9, 7, 8, 1, 4, 6, 3, 2, 0], 'cur_cost': 2116.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 9, 7, 8, 0, 4, 6, 2, 3, 1], 'cur_cost': 1483.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 9, 7, 8, 0, 4, 6, 3, 2, 1], 'cur_cost': 1457.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 4, 1, 6, 3, 5, 0, 7, 9, 8], dtype=int64), 'cur_cost': 2450.0, 'intermediate_solutions': [{'tour': array([6, 0, 5, 3, 4, 2, 8, 9, 7, 1]), 'cur_cost': 2340.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 6, 0, 5, 4, 2, 8, 9, 7, 1]), 'cur_cost': 2340.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 3, 6, 0, 5, 2, 8, 9, 7, 1]), 'cur_cost': 2373.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 3, 6, 0, 4, 2, 8, 9, 7, 1]), 'cur_cost': 1861.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 4, 3, 6, 0, 2, 8, 9, 7, 1]), 'cur_cost': 2229.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [2, 7, 6, 9, 3, 1, 5, 8, 4, 0], 'cur_cost': 1724.0, 'intermediate_solutions': [{'tour': [8, 3, 7, 5, 4, 2, 9, 0, 6, 1], 'cur_cost': 2554.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 6, 7, 5, 4, 2, 0, 9, 3, 1], 'cur_cost': 2039.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 8, 6, 7, 4, 2, 9, 0, 3, 1], 'cur_cost': 1942.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [2, 7, 6, 4, 9, 3, 1, 5, 8, 0], 'cur_cost': 1908.0, 'intermediate_solutions': [{'tour': [0, 6, 9, 5, 3, 2, 1, 4, 8, 7], 'cur_cost': 1807.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 6, 9, 5, 1, 2, 3, 0, 8, 7], 'cur_cost': 1705.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 6, 5, 3, 2, 1, 0, 8, 9, 7], 'cur_cost': 1816.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [4, 7, 6, 9, 3, 2, 1, 5, 8, 0], 'cur_cost': 1648.0, 'intermediate_solutions': [{'tour': [8, 6, 4, 2, 3, 1, 5, 9, 7, 0], 'cur_cost': 1576.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 4, 2, 3, 1, 5, 9, 8, 7], 'cur_cost': 1725.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 4, 2, 3, 1, 0, 5, 9, 7, 8], 'cur_cost': 1692.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [4, 7, 3, 6, 8, 9, 2, 1, 5, 0], 'cur_cost': 1803.0, 'intermediate_solutions': [{'tour': [6, 3, 5, 9, 8, 4, 7, 0, 2, 1], 'cur_cost': 2127.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 5, 2, 0, 6, 4, 8, 9, 1], 'cur_cost': 1953.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 3, 5, 9, 8, 4, 6, 2, 0, 1], 'cur_cost': 2044.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([9, 2, 1, 0, 7, 8, 6, 5, 3, 4], dtype=int64), 'cur_cost': 2007.0, 'intermediate_solutions': [{'tour': array([2, 0, 7, 5, 6, 4, 1, 9, 3, 8]), 'cur_cost': 2359.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 2, 0, 7, 6, 4, 1, 9, 3, 8]), 'cur_cost': 2214.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 5, 2, 0, 7, 4, 1, 9, 3, 8]), 'cur_cost': 2251.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 5, 2, 0, 6, 4, 1, 9, 3, 8]), 'cur_cost': 2015.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 6, 5, 2, 0, 4, 1, 9, 3, 8]), 'cur_cost': 1994.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 7, 8, 2, 6, 4, 9, 3, 5, 1], dtype=int64), 'cur_cost': 2042.0, 'intermediate_solutions': [{'tour': array([5, 4, 9, 0, 1, 6, 8, 2, 3, 7]), 'cur_cost': 2267.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 5, 4, 9, 1, 6, 8, 2, 3, 7]), 'cur_cost': 2425.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 0, 5, 4, 9, 6, 8, 2, 3, 7]), 'cur_cost': 2286.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([9, 0, 5, 4, 1, 6, 8, 2, 3, 7]), 'cur_cost': 2322.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([9, 1, 0, 5, 4, 6, 8, 2, 3, 7]), 'cur_cost': 2062.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 09:51:41,168 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:41,168 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:41,169 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1648.000, 多样性=0.871
2025-08-05 09:51:41,170 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 09:51:41,170 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 09:51:41,170 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:41,170 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.07271657762630035, 'best_improvement': -0.13109128345916266}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.017543859649122917}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.02761643825197338, 'recent_improvements': [0.006013206102442446, -0.009788175682420592, 0.06124608260638922], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 1265.0, 'new_best_cost': 1265.0, 'quality_improvement': 0.0, 'old_diversity': 0.5666666666666667, 'new_diversity': 0.5666666666666667, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:51:41,170 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 09:51:41,173 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple2_10_solution.json
2025-08-05 09:51:41,173 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple2_10_20250805_095141.solution
2025-08-05 09:51:41,173 - __main__ - INFO - 实例执行完成 - 运行时间: 0.98s, 最佳成本: 1265.0
2025-08-05 09:51:41,173 - __main__ - INFO - 实例 simple2_10 处理完成
