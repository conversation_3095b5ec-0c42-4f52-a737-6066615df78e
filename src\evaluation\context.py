"""
评估上下文管理器

提供算法阶段追踪和嵌套上下文管理功能。
"""

import time
from typing import Optional, Any, Dict
from contextlib import contextmanager

from .types import AlgorithmPhase, EvaluationType
from .counter import EvaluationCounter


class EvaluationContext:
    """评估上下文管理器"""
    
    def __init__(self, 
                 phase: AlgorithmPhase,
                 iteration: Optional[int] = None,
                 counter: Optional[EvaluationCounter] = None):
        """
        初始化评估上下文
        
        Args:
            phase: 算法阶段
            iteration: 迭代次数
            counter: 评估计数器
        """
        self.phase = phase
        self.iteration = iteration
        self.counter = counter
        self.start_time = 0.0
        self.end_time = 0.0
        self.start_count = 0
        self.evaluation_count = 0
        self._previous_phase = None
        self._previous_iteration = None
    
    def __enter__(self):
        """进入上下文"""
        self.start_time = time.time()

        if self.counter is not None:
            # 保存之前的状态（兼容不同类型的计数器）
            if hasattr(self.counter, '_current_phase'):
                self._previous_phase = self.counter._current_phase
                self._previous_iteration = getattr(self.counter, '_current_iteration', None)
            else:
                self._previous_phase = None
                self._previous_iteration = None

            # 设置新的状态
            if hasattr(self.counter, 'set_current_phase'):
                if self.iteration is not None and hasattr(self.counter, 'set_current_phase'):
                    # 检查方法签名
                    import inspect
                    sig = inspect.signature(self.counter.set_current_phase)
                    if len(sig.parameters) > 1:
                        self.counter.set_current_phase(self.phase, self.iteration)
                    else:
                        self.counter.set_current_phase(self.phase)
                else:
                    self.counter.set_current_phase(self.phase)

            # 获取起始计数
            if hasattr(self.counter, 'get_statistics'):
                self.start_count = self.counter.get_statistics().total_count
            elif hasattr(self.counter, 'get_total_count'):
                self.start_count = self.counter.get_total_count()
            else:
                self.start_count = 0

        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文"""
        self.end_time = time.time()

        if self.counter is not None:
            # 计算评估次数
            if hasattr(self.counter, 'get_statistics'):
                current_count = self.counter.get_statistics().total_count
            elif hasattr(self.counter, 'get_total_count'):
                current_count = self.counter.get_total_count()
            else:
                current_count = self.start_count

            self.evaluation_count = current_count - self.start_count

            # 恢复之前的状态
            if self._previous_phase is not None and hasattr(self.counter, 'set_current_phase'):
                if self._previous_iteration is not None:
                    # 检查方法签名
                    import inspect
                    sig = inspect.signature(self.counter.set_current_phase)
                    if len(sig.parameters) > 1:
                        self.counter.set_current_phase(self._previous_phase, self._previous_iteration)
                    else:
                        self.counter.set_current_phase(self._previous_phase)
                else:
                    self.counter.set_current_phase(self._previous_phase)
    
    def record_evaluation(self, 
                         evaluation_type: EvaluationType,
                         count: int = 1,
                         **kwargs) -> None:
        """在上下文中记录评估"""
        if self.counter is not None:
            self.counter.increment(evaluation_type, count, **kwargs)
    
    def get_duration(self) -> float:
        """获取上下文持续时间"""
        if self.end_time > 0:
            return self.end_time - self.start_time
        return time.time() - self.start_time
    
    def get_evaluations_per_second(self) -> float:
        """获取每秒评估次数"""
        duration = self.get_duration()
        return self.evaluation_count / duration if duration > 0 else 0.0
    
    def get_summary(self) -> Dict[str, Any]:
        """获取上下文摘要"""
        return {
            'phase': self.phase.value,
            'iteration': self.iteration,
            'duration': self.get_duration(),
            'evaluation_count': self.evaluation_count,
            'evaluations_per_second': self.get_evaluations_per_second(),
            'start_time': self.start_time,
            'end_time': self.end_time
        }


class NestedEvaluationContext:
    """嵌套评估上下文管理器"""
    
    def __init__(self, 
                 phase: AlgorithmPhase,
                 counter: Optional[EvaluationCounter] = None):
        """
        初始化嵌套评估上下文
        
        Args:
            phase: 算法阶段
            counter: 评估计数器
        """
        self.phase = phase
        self.counter = counter
        self.start_time = 0.0
        self.end_time = 0.0
        self.start_count = 0
        self.evaluation_count = 0
        self._phase_stack = []
    
    def __enter__(self):
        """进入嵌套上下文"""
        self.start_time = time.time()
        
        if self.counter is not None:
            # 保存当前阶段到栈中
            self._phase_stack.append(self.counter._current_phase)
            
            # 设置新阶段
            self.counter.set_current_phase(self.phase)
            self.start_count = self.counter.get_statistics().total_count
        
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出嵌套上下文"""
        self.end_time = time.time()
        
        if self.counter is not None:
            current_count = self.counter.get_statistics().total_count
            self.evaluation_count = current_count - self.start_count
            
            # 恢复之前的阶段
            if self._phase_stack:
                previous_phase = self._phase_stack.pop()
                self.counter.set_current_phase(previous_phase)
    
    def record_evaluation(self, 
                         evaluation_type: EvaluationType,
                         count: int = 1,
                         **kwargs) -> None:
        """在嵌套上下文中记录评估"""
        if self.counter is not None:
            self.counter.increment(evaluation_type, count, **kwargs)


@contextmanager
def evaluation_phase(phase: AlgorithmPhase, 
                    counter: Optional[EvaluationCounter] = None,
                    iteration: Optional[int] = None):
    """
    评估阶段上下文管理器（函数形式）
    
    Args:
        phase: 算法阶段
        counter: 评估计数器
        iteration: 迭代次数
    
    Usage:
        with evaluation_phase(AlgorithmPhase.LOCAL_SEARCH, counter):
            # 执行局部搜索
            pass
    """
    context = EvaluationContext(phase, iteration, counter)
    try:
        yield context.__enter__()
    finally:
        context.__exit__(None, None, None)


@contextmanager
def nested_evaluation_phase(phase: AlgorithmPhase,
                           counter: Optional[EvaluationCounter] = None):
    """
    嵌套评估阶段上下文管理器（函数形式）
    
    Args:
        phase: 算法阶段
        counter: 评估计数器
    
    Usage:
        with nested_evaluation_phase(AlgorithmPhase.LOCAL_SEARCH, counter):
            # 执行嵌套的局部搜索
            pass
    """
    context = NestedEvaluationContext(phase, counter)
    try:
        yield context.__enter__()
    finally:
        context.__exit__(None, None, None)
