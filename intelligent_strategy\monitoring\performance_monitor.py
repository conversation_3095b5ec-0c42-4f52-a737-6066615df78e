"""
Performance monitoring for the intelligent strategy selection system.

This module provides comprehensive performance monitoring, metrics collection,
and system health tracking capabilities.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
import time
import psutil
import threading
import logging
from collections import deque, defaultdict
import numpy as np

from ..core.data_structures import ExecutionResult, FeedbackReport


@dataclass
class PerformanceMetrics:
    """Comprehensive performance metrics for the system."""
    
    # Strategy selection performance
    strategy_selection_time: float = 0.0
    llm_inference_time: float = 0.0
    strategy_parsing_time: float = 0.0
    
    # Execution performance
    exploration_success_rate: float = 0.0
    exploitation_improvement_rate: float = 0.0
    escape_success_rate: float = 0.0
    
    # System performance
    memory_usage: float = 0.0
    cpu_utilization: float = 0.0
    thread_utilization: float = 0.0
    cache_hit_rate: float = 0.0
    
    # Algorithm performance
    convergence_speed: float = 0.0
    solution_quality_improvement: float = 0.0
    diversity_maintenance: float = 0.0
    exploration_exploitation_balance: float = 0.0
    
    # Coordination performance
    coordination_overhead: float = 0.0
    communication_latency: float = 0.0
    conflict_resolution_time: float = 0.0
    
    # Timestamp
    timestamp: float = field(default_factory=time.time)
    
    def to_dict(self) -> Dict[str, float]:
        """Convert metrics to dictionary."""
        return {
            'strategy_selection_time': self.strategy_selection_time,
            'llm_inference_time': self.llm_inference_time,
            'strategy_parsing_time': self.strategy_parsing_time,
            'exploration_success_rate': self.exploration_success_rate,
            'exploitation_improvement_rate': self.exploitation_improvement_rate,
            'escape_success_rate': self.escape_success_rate,
            'memory_usage': self.memory_usage,
            'cpu_utilization': self.cpu_utilization,
            'thread_utilization': self.thread_utilization,
            'cache_hit_rate': self.cache_hit_rate,
            'convergence_speed': self.convergence_speed,
            'solution_quality_improvement': self.solution_quality_improvement,
            'diversity_maintenance': self.diversity_maintenance,
            'exploration_exploitation_balance': self.exploration_exploitation_balance,
            'coordination_overhead': self.coordination_overhead,
            'communication_latency': self.communication_latency,
            'conflict_resolution_time': self.conflict_resolution_time,
            'timestamp': self.timestamp
        }


class PerformanceMonitor:
    """
    Comprehensive performance monitoring system.
    
    This class tracks system performance, resource utilization,
    and algorithm effectiveness metrics.
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize the performance monitor."""
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Configuration
        self.collection_interval = self.config.get('collection_interval', 1.0)
        self.history_length = self.config.get('history_length', 1000)
        self.alert_thresholds = self.config.get('alert_thresholds', {
            'memory_usage': 0.85,
            'cpu_utilization': 0.90,
            'strategy_selection_time': 5.0,
            'llm_inference_time': 10.0
        })
        
        # Metrics storage
        self.metrics_history: deque = deque(maxlen=self.history_length)
        self.current_metrics = PerformanceMetrics()
        
        # Performance tracking
        self.timing_data: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        self.success_rates: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        self.improvement_data: deque = deque(maxlen=100)
        
        # System monitoring
        self.system_stats = {
            'start_time': time.time(),
            'total_iterations': 0,
            'total_strategy_selections': 0,
            'total_executions': 0
        }
        
        # Thread safety
        self._lock = threading.Lock()
        
        # Monitoring thread
        self._monitoring_active = False
        self._monitoring_thread = None
    
    def start_monitoring(self) -> None:
        """Start continuous performance monitoring."""
        if self._monitoring_active:
            return
        
        self._monitoring_active = True
        self._monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True
        )
        self._monitoring_thread.start()
        self.logger.info("Performance monitoring started")
    
    def stop_monitoring(self) -> None:
        """Stop continuous performance monitoring."""
        self._monitoring_active = False
        if self._monitoring_thread:
            self._monitoring_thread.join(timeout=5.0)
        self.logger.info("Performance monitoring stopped")
    
    def record_strategy_selection_time(self, duration: float) -> None:
        """Record strategy selection timing."""
        with self._lock:
            self.timing_data['strategy_selection'].append(duration)
            self.current_metrics.strategy_selection_time = duration
    
    def record_llm_inference_time(self, duration: float) -> None:
        """Record LLM inference timing."""
        with self._lock:
            self.timing_data['llm_inference'].append(duration)
            self.current_metrics.llm_inference_time = duration
    
    def record_execution_results(self, results: List[ExecutionResult]) -> None:
        """Record strategy execution results."""
        if not results:
            return
        
        with self._lock:
            # Calculate success rates by strategy type
            strategy_stats = defaultdict(lambda: {'total': 0, 'success': 0, 'improvement': 0.0})
            
            for result in results:
                strategy_type = result.strategy_type.value
                stats = strategy_stats[strategy_type]
                stats['total'] += 1
                
                if result.success:
                    stats['success'] += 1
                    stats['improvement'] += result.fitness_improvement
            
            # Update success rates
            for strategy_type, stats in strategy_stats.items():
                if stats['total'] > 0:
                    success_rate = stats['success'] / stats['total']
                    self.success_rates[strategy_type].append(success_rate)
                    
                    if stats['success'] > 0:
                        avg_improvement = stats['improvement'] / stats['success']
                        self.improvement_data.append(avg_improvement)
            
            # Update current metrics
            self._update_execution_metrics(results)
            
            # Update system stats
            self.system_stats['total_executions'] += len(results)
    
    def record_feedback(self, feedback: FeedbackReport) -> None:
        """Record feedback report."""
        with self._lock:
            # Update algorithm performance metrics
            self.current_metrics.solution_quality_improvement = feedback.average_improvement
            self.current_metrics.exploration_exploitation_balance = self._calculate_balance_score(feedback)
            
            # Update system stats
            self.system_stats['total_iterations'] += 1
    
    def collect_metrics(self) -> PerformanceMetrics:
        """Collect current performance metrics."""
        with self._lock:
            # Update system metrics
            self._update_system_metrics()
            
            # Update algorithm metrics
            self._update_algorithm_metrics()
            
            # Create metrics snapshot
            metrics = PerformanceMetrics(
                strategy_selection_time=self.current_metrics.strategy_selection_time,
                llm_inference_time=self.current_metrics.llm_inference_time,
                strategy_parsing_time=self.current_metrics.strategy_parsing_time,
                exploration_success_rate=self.current_metrics.exploration_success_rate,
                exploitation_improvement_rate=self.current_metrics.exploitation_improvement_rate,
                escape_success_rate=self.current_metrics.escape_success_rate,
                memory_usage=self.current_metrics.memory_usage,
                cpu_utilization=self.current_metrics.cpu_utilization,
                thread_utilization=self.current_metrics.thread_utilization,
                cache_hit_rate=self.current_metrics.cache_hit_rate,
                convergence_speed=self.current_metrics.convergence_speed,
                solution_quality_improvement=self.current_metrics.solution_quality_improvement,
                diversity_maintenance=self.current_metrics.diversity_maintenance,
                exploration_exploitation_balance=self.current_metrics.exploration_exploitation_balance,
                coordination_overhead=self.current_metrics.coordination_overhead,
                communication_latency=self.current_metrics.communication_latency,
                conflict_resolution_time=self.current_metrics.conflict_resolution_time
            )
            
            # Store in history
            self.metrics_history.append(metrics)
            
            # Check for alerts
            self._check_alerts(metrics)
            
            return metrics
    
    def get_performance_summary(self, window_size: int = 10) -> Dict[str, Any]:
        """Get performance summary over recent window."""
        if not self.metrics_history:
            return {}
        
        recent_metrics = list(self.metrics_history)[-window_size:]
        
        if not recent_metrics:
            return {}
        
        summary = {
            'window_size': len(recent_metrics),
            'time_range': {
                'start': recent_metrics[0].timestamp,
                'end': recent_metrics[-1].timestamp
            },
            'averages': {},
            'trends': {},
            'alerts': []
        }
        
        # Calculate averages
        metric_names = [
            'strategy_selection_time', 'llm_inference_time', 'memory_usage',
            'cpu_utilization', 'exploration_success_rate', 'exploitation_improvement_rate'
        ]
        
        for metric_name in metric_names:
            values = [getattr(m, metric_name) for m in recent_metrics]
            summary['averages'][metric_name] = {
                'mean': np.mean(values),
                'std': np.std(values),
                'min': np.min(values),
                'max': np.max(values)
            }
            
            # Calculate trend
            if len(values) > 1:
                x = np.arange(len(values))
                correlation = np.corrcoef(x, values)[0, 1] if np.std(values) > 0 else 0.0
                summary['trends'][metric_name] = correlation
        
        return summary
    
    def _monitoring_loop(self) -> None:
        """Main monitoring loop."""
        while self._monitoring_active:
            try:
                self.collect_metrics()
                time.sleep(self.collection_interval)
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                time.sleep(self.collection_interval)
    
    def _update_system_metrics(self) -> None:
        """Update system resource metrics."""
        try:
            # Memory usage
            memory = psutil.virtual_memory()
            self.current_metrics.memory_usage = memory.percent / 100.0
            
            # CPU utilization
            self.current_metrics.cpu_utilization = psutil.cpu_percent() / 100.0
            
            # Thread utilization (simplified)
            active_threads = threading.active_count()
            max_threads = self.config.get('max_workers', 8)
            self.current_metrics.thread_utilization = min(1.0, active_threads / max_threads)
            
        except Exception as e:
            self.logger.warning(f"Error updating system metrics: {e}")
    
    def _update_algorithm_metrics(self) -> None:
        """Update algorithm performance metrics."""
        # Convergence speed (based on improvement rate)
        if self.improvement_data:
            recent_improvements = list(self.improvement_data)[-10:]
            self.current_metrics.convergence_speed = np.mean(recent_improvements) if recent_improvements else 0.0
        
        # Diversity maintenance (placeholder - would need actual diversity data)
        self.current_metrics.diversity_maintenance = 0.5  # Default value
    
    def _update_execution_metrics(self, results: List[ExecutionResult]) -> None:
        """Update execution-related metrics."""
        if not results:
            return
        
        # Calculate exploration success rate
        exploration_results = [r for r in results if 'exploration' in r.strategy_type.value]
        if exploration_results:
            exploration_successes = sum(1 for r in exploration_results if r.success)
            self.current_metrics.exploration_success_rate = exploration_successes / len(exploration_results)
        
        # Calculate exploitation improvement rate
        exploitation_results = [r for r in results if 'exploitation' in r.strategy_type.value]
        if exploitation_results:
            total_improvement = sum(r.fitness_improvement for r in exploitation_results if r.success)
            self.current_metrics.exploitation_improvement_rate = total_improvement / len(exploitation_results)
    
    def _calculate_balance_score(self, feedback: FeedbackReport) -> float:
        """Calculate exploration-exploitation balance score."""
        if not feedback.strategy_performance:
            return 0.5
        
        exploration_performance = 0.0
        exploitation_performance = 0.0
        exploration_count = 0
        exploitation_count = 0
        
        for strategy_name, performance in feedback.strategy_performance.items():
            if 'exploration' in strategy_name:
                exploration_performance += performance.get('success_rate', 0.0)
                exploration_count += 1
            elif 'exploitation' in strategy_name:
                exploitation_performance += performance.get('success_rate', 0.0)
                exploitation_count += 1
        
        if exploration_count > 0:
            exploration_performance /= exploration_count
        if exploitation_count > 0:
            exploitation_performance /= exploitation_count
        
        # Balance score: closer to 0.5 means better balance
        if exploration_performance + exploitation_performance == 0:
            return 0.5
        
        balance = min(exploration_performance, exploitation_performance) / max(exploration_performance, exploitation_performance)
        return balance
    
    def _check_alerts(self, metrics: PerformanceMetrics) -> None:
        """Check for performance alerts."""
        alerts = []
        
        if metrics.memory_usage > self.alert_thresholds.get('memory_usage', 0.85):
            alerts.append(f"High memory usage: {metrics.memory_usage:.2%}")
        
        if metrics.cpu_utilization > self.alert_thresholds.get('cpu_utilization', 0.90):
            alerts.append(f"High CPU utilization: {metrics.cpu_utilization:.2%}")
        
        if metrics.strategy_selection_time > self.alert_thresholds.get('strategy_selection_time', 5.0):
            alerts.append(f"Slow strategy selection: {metrics.strategy_selection_time:.2f}s")
        
        if metrics.llm_inference_time > self.alert_thresholds.get('llm_inference_time', 10.0):
            alerts.append(f"Slow LLM inference: {metrics.llm_inference_time:.2f}s")
        
        for alert in alerts:
            self.logger.warning(f"Performance Alert: {alert}")
    
    def get_system_statistics(self) -> Dict[str, Any]:
        """Get overall system statistics."""
        current_time = time.time()
        uptime = current_time - self.system_stats['start_time']
        
        return {
            'uptime_seconds': uptime,
            'total_iterations': self.system_stats['total_iterations'],
            'total_strategy_selections': self.system_stats['total_strategy_selections'],
            'total_executions': self.system_stats['total_executions'],
            'average_iterations_per_second': self.system_stats['total_iterations'] / uptime if uptime > 0 else 0,
            'metrics_history_length': len(self.metrics_history),
            'monitoring_active': self._monitoring_active
        }
