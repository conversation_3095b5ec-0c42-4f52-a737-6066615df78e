2025-08-05 09:52:07,878 - __main__ - INFO - composite10_55 开始进化第 1 代
2025-08-05 09:52:07,879 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 09:52:07,880 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:07,883 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10764.000, 多样性=0.966
2025-08-05 09:52:07,886 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:07,889 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.966
2025-08-05 09:52:07,898 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:07,900 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 09:52:07,900 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:07,900 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 09:52:07,901 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 09:52:07,923 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.400, 适应度梯度: -2639.940, 聚类评分: 0.000, 覆盖率: 0.127, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:07,923 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 09:52:07,923 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 09:52:07,924 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite10_55
2025-08-05 09:52:08,033 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite10_55\landscape_composite10_55_iter_106_20250805_095207.html
2025-08-05 09:52:08,079 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite10_55\dashboard_composite10_55_iter_106_20250805_095207.html
2025-08-05 09:52:08,079 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 106
2025-08-05 09:52:08,079 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 09:52:08,079 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1790秒
2025-08-05 09:52:08,080 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 212, 'max_size': 500, 'hits': 0, 'misses': 212, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 695, 'misses': 380, 'hit_rate': 0.6465116279069767, 'evictions': 280, 'ttl': 7200}}
2025-08-05 09:52:08,080 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.4, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -2639.9400000000037, 'local_optima_density': 0.4, 'gradient_variance': 2470241269.6083994, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1272, 'fitness_entropy': 0.9854752972273343, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -2639.940)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.127)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358727.9237337, 'performance_metrics': {}}}
2025-08-05 09:52:08,080 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:08,080 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:08,080 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:08,080 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:08,081 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 09:52:08,081 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 09:52:08,081 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 09:52:08,081 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:08,082 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:08,082 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 09:52:08,082 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:08,082 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:08,082 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:08,082 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:08,082 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:08,082 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:08,089 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 55
2025-08-05 09:52:08,089 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,090 - ExplorationExpert - INFO - 探索路径生成完成，成本: 56359.0, 路径长度: 55, 收集中间解: 0
2025-08-05 09:52:08,090 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [26, 32, 35, 29, 40, 30, 34, 31, 36, 7, 28, 3, 8, 27, 53, 46, 21, 11, 19, 14, 18, 13, 25, 23, 0, 9, 39, 2, 4, 37, 5, 12, 16, 20, 45, 48, 17, 47, 54, 52, 50, 38, 24, 22, 41, 42, 1, 33, 43, 49, 44, 51, 15, 6, 10], 'cur_cost': 56359.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:08,090 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 56359.00)
2025-08-05 09:52:08,090 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:08,090 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:08,090 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:08,097 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 55
2025-08-05 09:52:08,097 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,098 - ExplorationExpert - INFO - 探索路径生成完成，成本: 41266.0, 路径长度: 55, 收集中间解: 0
2025-08-05 09:52:08,098 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [52, 53, 12, 18, 14, 28, 31, 43, 27, 37, 34, 29, 10, 2, 7, 9, 5, 24, 40, 6, 26, 25, 38, 35, 36, 33, 42, 0, 19, 54, 50, 45, 51, 46, 17, 15, 21, 44, 16, 13, 49, 30, 41, 22, 23, 32, 39, 1, 8, 3, 4, 11, 20, 47, 48], 'cur_cost': 41266.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:08,098 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 41266.00)
2025-08-05 09:52:08,098 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:08,099 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:08,099 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:08,101 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 55
2025-08-05 09:52:08,101 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,101 - ExplorationExpert - INFO - 探索路径生成完成，成本: 92677.0, 路径长度: 55, 收集中间解: 0
2025-08-05 09:52:08,101 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [52, 32, 35, 18, 40, 43, 36, 37, 28, 29, 2, 53, 46, 5, 24, 19, 6, 26, 13, 25, 23, 0, 9, 39, 50, 45, 16, 20, 15, 21, 54, 49, 27, 33, 31, 4, 11, 3, 1, 17, 38, 48, 44, 41, 14, 30, 47, 51, 10, 12, 34, 7, 22, 42, 8], 'cur_cost': 92677.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:08,102 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 92677.00)
2025-08-05 09:52:08,102 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:52:08,102 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:52:08,102 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:08,103 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 09:52:08,104 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,104 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16455.0, 路径长度: 55, 收集中间解: 0
2025-08-05 09:52:08,104 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 5, 12, 2, 7, 9, 3, 6, 4, 1, 8, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 19, 21, 11], 'cur_cost': 16455.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:08,104 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 16455.00)
2025-08-05 09:52:08,104 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:08,104 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:08,104 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:08,106 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 09:52:08,106 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,107 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16504.0, 路径长度: 55, 收集中间解: 0
2025-08-05 09:52:08,107 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 20, 7, 21, 19, 17, 13, 14, 16, 15, 18, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 1, 4, 6, 5, 9, 3, 2], 'cur_cost': 16504.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:08,107 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 16504.00)
2025-08-05 09:52:08,107 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:08,107 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:08,107 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:08,112 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 55
2025-08-05 09:52:08,113 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,113 - ExplorationExpert - INFO - 探索路径生成完成，成本: 47998.0, 路径长度: 55, 收集中间解: 0
2025-08-05 09:52:08,113 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [51, 19, 30, 35, 36, 34, 23, 41, 40, 25, 26, 39, 9, 4, 29, 1, 7, 3, 6, 42, 33, 24, 44, 54, 45, 11, 18, 13, 53, 50, 12, 52, 20, 47, 15, 46, 17, 16, 48, 49, 14, 21, 28, 38, 22, 31, 37, 27, 43, 32, 0, 8, 2, 5, 10], 'cur_cost': 47998.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:08,113 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 47998.00)
2025-08-05 09:52:08,114 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:08,114 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:08,114 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:08,118 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 55
2025-08-05 09:52:08,119 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,119 - ExplorationExpert - INFO - 探索路径生成完成，成本: 47707.0, 路径长度: 55, 收集中间解: 0
2025-08-05 09:52:08,119 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [46, 21, 28, 43, 23, 39, 36, 34, 30, 42, 10, 8, 6, 9, 4, 33, 26, 37, 38, 0, 31, 32, 41, 40, 2, 24, 53, 44, 50, 11, 17, 16, 47, 52, 18, 48, 45, 51, 20, 14, 19, 54, 49, 25, 35, 29, 22, 1, 3, 7, 27, 5, 12, 15, 13], 'cur_cost': 47707.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:08,119 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 47707.00)
2025-08-05 09:52:08,119 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:08,119 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:08,120 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:08,121 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 55
2025-08-05 09:52:08,121 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,121 - ExplorationExpert - INFO - 探索路径生成完成，成本: 96534.0, 路径长度: 55, 收集中间解: 0
2025-08-05 09:52:08,121 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [46, 20, 30, 21, 14, 43, 3, 37, 36, 29, 34, 53, 10, 5, 7, 19, 32, 11, 40, 25, 44, 13, 9, 23, 0, 33, 42, 2, 4, 50, 54, 45, 27, 41, 17, 15, 47, 16, 52, 49, 38, 24, 22, 48, 31, 1, 51, 6, 12, 39, 18, 26, 28, 35, 8], 'cur_cost': 96534.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:08,122 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 96534.00)
2025-08-05 09:52:08,122 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:52:08,122 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:08,122 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:08,122 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 93233.0
2025-08-05 09:52:08,130 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 09:52:08,130 - ExploitationExpert - INFO - res_population_costs: [10451.0, 10450]
2025-08-05 09:52:08,130 - ExploitationExpert - INFO - res_populations: [array([ 0, 19, 21, 12, 18, 20, 15, 16, 14, 11, 13, 17, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 30, 24, 26, 31, 25, 23, 28, 32, 27, 22, 29,
       35, 36, 33, 42, 37, 38, 41, 40, 34, 39, 43, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       14, 11, 13, 17, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64)]
2025-08-05 09:52:08,131 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:08,131 - ExploitationExpert - INFO - populations: [{'tour': [26, 32, 35, 29, 40, 30, 34, 31, 36, 7, 28, 3, 8, 27, 53, 46, 21, 11, 19, 14, 18, 13, 25, 23, 0, 9, 39, 2, 4, 37, 5, 12, 16, 20, 45, 48, 17, 47, 54, 52, 50, 38, 24, 22, 41, 42, 1, 33, 43, 49, 44, 51, 15, 6, 10], 'cur_cost': 56359.0}, {'tour': [52, 53, 12, 18, 14, 28, 31, 43, 27, 37, 34, 29, 10, 2, 7, 9, 5, 24, 40, 6, 26, 25, 38, 35, 36, 33, 42, 0, 19, 54, 50, 45, 51, 46, 17, 15, 21, 44, 16, 13, 49, 30, 41, 22, 23, 32, 39, 1, 8, 3, 4, 11, 20, 47, 48], 'cur_cost': 41266.0}, {'tour': [52, 32, 35, 18, 40, 43, 36, 37, 28, 29, 2, 53, 46, 5, 24, 19, 6, 26, 13, 25, 23, 0, 9, 39, 50, 45, 16, 20, 15, 21, 54, 49, 27, 33, 31, 4, 11, 3, 1, 17, 38, 48, 44, 41, 14, 30, 47, 51, 10, 12, 34, 7, 22, 42, 8], 'cur_cost': 92677.0}, {'tour': [0, 5, 12, 2, 7, 9, 3, 6, 4, 1, 8, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 19, 21, 11], 'cur_cost': 16455.0}, {'tour': [0, 20, 7, 21, 19, 17, 13, 14, 16, 15, 18, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 1, 4, 6, 5, 9, 3, 2], 'cur_cost': 16504.0}, {'tour': [51, 19, 30, 35, 36, 34, 23, 41, 40, 25, 26, 39, 9, 4, 29, 1, 7, 3, 6, 42, 33, 24, 44, 54, 45, 11, 18, 13, 53, 50, 12, 52, 20, 47, 15, 46, 17, 16, 48, 49, 14, 21, 28, 38, 22, 31, 37, 27, 43, 32, 0, 8, 2, 5, 10], 'cur_cost': 47998.0}, {'tour': [46, 21, 28, 43, 23, 39, 36, 34, 30, 42, 10, 8, 6, 9, 4, 33, 26, 37, 38, 0, 31, 32, 41, 40, 2, 24, 53, 44, 50, 11, 17, 16, 47, 52, 18, 48, 45, 51, 20, 14, 19, 54, 49, 25, 35, 29, 22, 1, 3, 7, 27, 5, 12, 15, 13], 'cur_cost': 47707.0}, {'tour': [46, 20, 30, 21, 14, 43, 3, 37, 36, 29, 34, 53, 10, 5, 7, 19, 32, 11, 40, 25, 44, 13, 9, 23, 0, 33, 42, 2, 4, 50, 54, 45, 27, 41, 17, 15, 47, 16, 52, 49, 38, 24, 22, 48, 31, 1, 51, 6, 12, 39, 18, 26, 28, 35, 8], 'cur_cost': 96534.0}, {'tour': array([49, 11, 22, 43, 33, 32, 17, 24, 52, 50,  5, 48, 51, 15, 26, 29, 28,
       12, 44, 19,  9,  8, 46, 45, 36,  3, 34, 37,  1, 30, 13, 35,  6, 54,
       18, 31, 53,  7, 47, 40, 25, 39, 10, 41, 23, 38,  2,  4, 21, 14, 20,
       16,  0, 42, 27], dtype=int64), 'cur_cost': 93233.0}, {'tour': array([12, 42,  7, 54, 38, 23, 25, 35, 31, 24, 19,  2, 53, 40, 47, 43, 17,
       26, 39, 33, 52, 41,  5, 37, 50,  0, 45, 46, 27, 48, 29, 14,  6, 22,
       15, 20,  8, 10, 28, 18, 11, 44,  3, 13, 36, 49, 16,  4, 21,  1, 34,
        9, 51, 30, 32], dtype=int64), 'cur_cost': 116164.0}]
2025-08-05 09:52:08,132 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:08,132 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 274, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 274, 'cache_hits': 0, 'similarity_calculations': 1356, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:08,133 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([49, 11, 22, 43, 33, 32, 17, 24, 52, 50,  5, 48, 51, 15, 26, 29, 28,
       12, 44, 19,  9,  8, 46, 45, 36,  3, 34, 37,  1, 30, 13, 35,  6, 54,
       18, 31, 53,  7, 47, 40, 25, 39, 10, 41, 23, 38,  2,  4, 21, 14, 20,
       16,  0, 42, 27], dtype=int64), 'cur_cost': 93233.0, 'intermediate_solutions': [{'tour': array([47, 22,  5, 39, 35, 26, 24, 53,  8,  6, 20, 54,  7, 38, 14, 48, 52,
       32, 18, 34, 28, 41, 45,  3, 12, 23,  2, 31,  0, 29, 17, 16, 51, 40,
       25, 21, 30,  9, 11,  1, 42, 33, 37, 19, 36, 10, 49, 44, 46, 43, 15,
        4, 50, 13, 27], dtype=int64), 'cur_cost': 106089.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([39, 47, 22,  5, 35, 26, 24, 53,  8,  6, 20, 54,  7, 38, 14, 48, 52,
       32, 18, 34, 28, 41, 45,  3, 12, 23,  2, 31,  0, 29, 17, 16, 51, 40,
       25, 21, 30,  9, 11,  1, 42, 33, 37, 19, 36, 10, 49, 44, 46, 43, 15,
        4, 50, 13, 27], dtype=int64), 'cur_cost': 107995.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([35, 39, 47, 22,  5, 26, 24, 53,  8,  6, 20, 54,  7, 38, 14, 48, 52,
       32, 18, 34, 28, 41, 45,  3, 12, 23,  2, 31,  0, 29, 17, 16, 51, 40,
       25, 21, 30,  9, 11,  1, 42, 33, 37, 19, 36, 10, 49, 44, 46, 43, 15,
        4, 50, 13, 27], dtype=int64), 'cur_cost': 106015.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 5, 39, 47, 22, 35, 26, 24, 53,  8,  6, 20, 54,  7, 38, 14, 48, 52,
       32, 18, 34, 28, 41, 45,  3, 12, 23,  2, 31,  0, 29, 17, 16, 51, 40,
       25, 21, 30,  9, 11,  1, 42, 33, 37, 19, 36, 10, 49, 44, 46, 43, 15,
        4, 50, 13, 27], dtype=int64), 'cur_cost': 107998.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 5, 35, 39, 47, 22, 26, 24, 53,  8,  6, 20, 54,  7, 38, 14, 48, 52,
       32, 18, 34, 28, 41, 45,  3, 12, 23,  2, 31,  0, 29, 17, 16, 51, 40,
       25, 21, 30,  9, 11,  1, 42, 33, 37, 19, 36, 10, 49, 44, 46, 43, 15,
        4, 50, 13, 27], dtype=int64), 'cur_cost': 105852.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:08,134 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 93233.00)
2025-08-05 09:52:08,134 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 09:52:08,134 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:08,134 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:08,135 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 110272.0
2025-08-05 09:52:08,139 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 09:52:08,139 - ExploitationExpert - INFO - res_population_costs: [10451.0, 10450]
2025-08-05 09:52:08,139 - ExploitationExpert - INFO - res_populations: [array([ 0, 19, 21, 12, 18, 20, 15, 16, 14, 11, 13, 17, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 30, 24, 26, 31, 25, 23, 28, 32, 27, 22, 29,
       35, 36, 33, 42, 37, 38, 41, 40, 34, 39, 43, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       14, 11, 13, 17, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64)]
2025-08-05 09:52:08,140 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:08,140 - ExploitationExpert - INFO - populations: [{'tour': [26, 32, 35, 29, 40, 30, 34, 31, 36, 7, 28, 3, 8, 27, 53, 46, 21, 11, 19, 14, 18, 13, 25, 23, 0, 9, 39, 2, 4, 37, 5, 12, 16, 20, 45, 48, 17, 47, 54, 52, 50, 38, 24, 22, 41, 42, 1, 33, 43, 49, 44, 51, 15, 6, 10], 'cur_cost': 56359.0}, {'tour': [52, 53, 12, 18, 14, 28, 31, 43, 27, 37, 34, 29, 10, 2, 7, 9, 5, 24, 40, 6, 26, 25, 38, 35, 36, 33, 42, 0, 19, 54, 50, 45, 51, 46, 17, 15, 21, 44, 16, 13, 49, 30, 41, 22, 23, 32, 39, 1, 8, 3, 4, 11, 20, 47, 48], 'cur_cost': 41266.0}, {'tour': [52, 32, 35, 18, 40, 43, 36, 37, 28, 29, 2, 53, 46, 5, 24, 19, 6, 26, 13, 25, 23, 0, 9, 39, 50, 45, 16, 20, 15, 21, 54, 49, 27, 33, 31, 4, 11, 3, 1, 17, 38, 48, 44, 41, 14, 30, 47, 51, 10, 12, 34, 7, 22, 42, 8], 'cur_cost': 92677.0}, {'tour': [0, 5, 12, 2, 7, 9, 3, 6, 4, 1, 8, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 19, 21, 11], 'cur_cost': 16455.0}, {'tour': [0, 20, 7, 21, 19, 17, 13, 14, 16, 15, 18, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 1, 4, 6, 5, 9, 3, 2], 'cur_cost': 16504.0}, {'tour': [51, 19, 30, 35, 36, 34, 23, 41, 40, 25, 26, 39, 9, 4, 29, 1, 7, 3, 6, 42, 33, 24, 44, 54, 45, 11, 18, 13, 53, 50, 12, 52, 20, 47, 15, 46, 17, 16, 48, 49, 14, 21, 28, 38, 22, 31, 37, 27, 43, 32, 0, 8, 2, 5, 10], 'cur_cost': 47998.0}, {'tour': [46, 21, 28, 43, 23, 39, 36, 34, 30, 42, 10, 8, 6, 9, 4, 33, 26, 37, 38, 0, 31, 32, 41, 40, 2, 24, 53, 44, 50, 11, 17, 16, 47, 52, 18, 48, 45, 51, 20, 14, 19, 54, 49, 25, 35, 29, 22, 1, 3, 7, 27, 5, 12, 15, 13], 'cur_cost': 47707.0}, {'tour': [46, 20, 30, 21, 14, 43, 3, 37, 36, 29, 34, 53, 10, 5, 7, 19, 32, 11, 40, 25, 44, 13, 9, 23, 0, 33, 42, 2, 4, 50, 54, 45, 27, 41, 17, 15, 47, 16, 52, 49, 38, 24, 22, 48, 31, 1, 51, 6, 12, 39, 18, 26, 28, 35, 8], 'cur_cost': 96534.0}, {'tour': array([49, 11, 22, 43, 33, 32, 17, 24, 52, 50,  5, 48, 51, 15, 26, 29, 28,
       12, 44, 19,  9,  8, 46, 45, 36,  3, 34, 37,  1, 30, 13, 35,  6, 54,
       18, 31, 53,  7, 47, 40, 25, 39, 10, 41, 23, 38,  2,  4, 21, 14, 20,
       16,  0, 42, 27], dtype=int64), 'cur_cost': 93233.0}, {'tour': array([ 3, 52, 15,  7, 11, 48,  6, 27, 39, 30, 46, 23,  4, 45, 41, 14,  1,
       19, 34, 51, 31, 18, 33, 53, 12, 49, 13, 35, 54, 29,  8,  0, 10, 22,
       25, 42, 40,  9, 38, 26, 32,  5, 36, 24, 44, 16, 37, 50, 43, 20, 17,
       28,  2, 47, 21], dtype=int64), 'cur_cost': 110272.0}]
2025-08-05 09:52:08,142 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:08,142 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 275, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 275, 'cache_hits': 0, 'similarity_calculations': 1357, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:08,143 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([ 3, 52, 15,  7, 11, 48,  6, 27, 39, 30, 46, 23,  4, 45, 41, 14,  1,
       19, 34, 51, 31, 18, 33, 53, 12, 49, 13, 35, 54, 29,  8,  0, 10, 22,
       25, 42, 40,  9, 38, 26, 32,  5, 36, 24, 44, 16, 37, 50, 43, 20, 17,
       28,  2, 47, 21], dtype=int64), 'cur_cost': 110272.0, 'intermediate_solutions': [{'tour': array([ 7, 42, 12, 54, 38, 23, 25, 35, 31, 24, 19,  2, 53, 40, 47, 43, 17,
       26, 39, 33, 52, 41,  5, 37, 50,  0, 45, 46, 27, 48, 29, 14,  6, 22,
       15, 20,  8, 10, 28, 18, 11, 44,  3, 13, 36, 49, 16,  4, 21,  1, 34,
        9, 51, 30, 32], dtype=int64), 'cur_cost': 113383.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([54,  7, 42, 12, 38, 23, 25, 35, 31, 24, 19,  2, 53, 40, 47, 43, 17,
       26, 39, 33, 52, 41,  5, 37, 50,  0, 45, 46, 27, 48, 29, 14,  6, 22,
       15, 20,  8, 10, 28, 18, 11, 44,  3, 13, 36, 49, 16,  4, 21,  1, 34,
        9, 51, 30, 32], dtype=int64), 'cur_cost': 116421.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([38, 54,  7, 42, 12, 23, 25, 35, 31, 24, 19,  2, 53, 40, 47, 43, 17,
       26, 39, 33, 52, 41,  5, 37, 50,  0, 45, 46, 27, 48, 29, 14,  6, 22,
       15, 20,  8, 10, 28, 18, 11, 44,  3, 13, 36, 49, 16,  4, 21,  1, 34,
        9, 51, 30, 32], dtype=int64), 'cur_cost': 116191.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([12, 54,  7, 42, 38, 23, 25, 35, 31, 24, 19,  2, 53, 40, 47, 43, 17,
       26, 39, 33, 52, 41,  5, 37, 50,  0, 45, 46, 27, 48, 29, 14,  6, 22,
       15, 20,  8, 10, 28, 18, 11, 44,  3, 13, 36, 49, 16,  4, 21,  1, 34,
        9, 51, 30, 32], dtype=int64), 'cur_cost': 111066.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([12, 38, 54,  7, 42, 23, 25, 35, 31, 24, 19,  2, 53, 40, 47, 43, 17,
       26, 39, 33, 52, 41,  5, 37, 50,  0, 45, 46, 27, 48, 29, 14,  6, 22,
       15, 20,  8, 10, 28, 18, 11, 44,  3, 13, 36, 49, 16,  4, 21,  1, 34,
        9, 51, 30, 32], dtype=int64), 'cur_cost': 116153.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:08,143 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 110272.00)
2025-08-05 09:52:08,143 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:08,143 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:08,145 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [26, 32, 35, 29, 40, 30, 34, 31, 36, 7, 28, 3, 8, 27, 53, 46, 21, 11, 19, 14, 18, 13, 25, 23, 0, 9, 39, 2, 4, 37, 5, 12, 16, 20, 45, 48, 17, 47, 54, 52, 50, 38, 24, 22, 41, 42, 1, 33, 43, 49, 44, 51, 15, 6, 10], 'cur_cost': 56359.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [52, 53, 12, 18, 14, 28, 31, 43, 27, 37, 34, 29, 10, 2, 7, 9, 5, 24, 40, 6, 26, 25, 38, 35, 36, 33, 42, 0, 19, 54, 50, 45, 51, 46, 17, 15, 21, 44, 16, 13, 49, 30, 41, 22, 23, 32, 39, 1, 8, 3, 4, 11, 20, 47, 48], 'cur_cost': 41266.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [52, 32, 35, 18, 40, 43, 36, 37, 28, 29, 2, 53, 46, 5, 24, 19, 6, 26, 13, 25, 23, 0, 9, 39, 50, 45, 16, 20, 15, 21, 54, 49, 27, 33, 31, 4, 11, 3, 1, 17, 38, 48, 44, 41, 14, 30, 47, 51, 10, 12, 34, 7, 22, 42, 8], 'cur_cost': 92677.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 2, 7, 9, 3, 6, 4, 1, 8, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 19, 21, 11], 'cur_cost': 16455.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 7, 21, 19, 17, 13, 14, 16, 15, 18, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 1, 4, 6, 5, 9, 3, 2], 'cur_cost': 16504.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [51, 19, 30, 35, 36, 34, 23, 41, 40, 25, 26, 39, 9, 4, 29, 1, 7, 3, 6, 42, 33, 24, 44, 54, 45, 11, 18, 13, 53, 50, 12, 52, 20, 47, 15, 46, 17, 16, 48, 49, 14, 21, 28, 38, 22, 31, 37, 27, 43, 32, 0, 8, 2, 5, 10], 'cur_cost': 47998.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [46, 21, 28, 43, 23, 39, 36, 34, 30, 42, 10, 8, 6, 9, 4, 33, 26, 37, 38, 0, 31, 32, 41, 40, 2, 24, 53, 44, 50, 11, 17, 16, 47, 52, 18, 48, 45, 51, 20, 14, 19, 54, 49, 25, 35, 29, 22, 1, 3, 7, 27, 5, 12, 15, 13], 'cur_cost': 47707.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [46, 20, 30, 21, 14, 43, 3, 37, 36, 29, 34, 53, 10, 5, 7, 19, 32, 11, 40, 25, 44, 13, 9, 23, 0, 33, 42, 2, 4, 50, 54, 45, 27, 41, 17, 15, 47, 16, 52, 49, 38, 24, 22, 48, 31, 1, 51, 6, 12, 39, 18, 26, 28, 35, 8], 'cur_cost': 96534.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([49, 11, 22, 43, 33, 32, 17, 24, 52, 50,  5, 48, 51, 15, 26, 29, 28,
       12, 44, 19,  9,  8, 46, 45, 36,  3, 34, 37,  1, 30, 13, 35,  6, 54,
       18, 31, 53,  7, 47, 40, 25, 39, 10, 41, 23, 38,  2,  4, 21, 14, 20,
       16,  0, 42, 27], dtype=int64), 'cur_cost': 93233.0, 'intermediate_solutions': [{'tour': array([47, 22,  5, 39, 35, 26, 24, 53,  8,  6, 20, 54,  7, 38, 14, 48, 52,
       32, 18, 34, 28, 41, 45,  3, 12, 23,  2, 31,  0, 29, 17, 16, 51, 40,
       25, 21, 30,  9, 11,  1, 42, 33, 37, 19, 36, 10, 49, 44, 46, 43, 15,
        4, 50, 13, 27], dtype=int64), 'cur_cost': 106089.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([39, 47, 22,  5, 35, 26, 24, 53,  8,  6, 20, 54,  7, 38, 14, 48, 52,
       32, 18, 34, 28, 41, 45,  3, 12, 23,  2, 31,  0, 29, 17, 16, 51, 40,
       25, 21, 30,  9, 11,  1, 42, 33, 37, 19, 36, 10, 49, 44, 46, 43, 15,
        4, 50, 13, 27], dtype=int64), 'cur_cost': 107995.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([35, 39, 47, 22,  5, 26, 24, 53,  8,  6, 20, 54,  7, 38, 14, 48, 52,
       32, 18, 34, 28, 41, 45,  3, 12, 23,  2, 31,  0, 29, 17, 16, 51, 40,
       25, 21, 30,  9, 11,  1, 42, 33, 37, 19, 36, 10, 49, 44, 46, 43, 15,
        4, 50, 13, 27], dtype=int64), 'cur_cost': 106015.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 5, 39, 47, 22, 35, 26, 24, 53,  8,  6, 20, 54,  7, 38, 14, 48, 52,
       32, 18, 34, 28, 41, 45,  3, 12, 23,  2, 31,  0, 29, 17, 16, 51, 40,
       25, 21, 30,  9, 11,  1, 42, 33, 37, 19, 36, 10, 49, 44, 46, 43, 15,
        4, 50, 13, 27], dtype=int64), 'cur_cost': 107998.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 5, 35, 39, 47, 22, 26, 24, 53,  8,  6, 20, 54,  7, 38, 14, 48, 52,
       32, 18, 34, 28, 41, 45,  3, 12, 23,  2, 31,  0, 29, 17, 16, 51, 40,
       25, 21, 30,  9, 11,  1, 42, 33, 37, 19, 36, 10, 49, 44, 46, 43, 15,
        4, 50, 13, 27], dtype=int64), 'cur_cost': 105852.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 3, 52, 15,  7, 11, 48,  6, 27, 39, 30, 46, 23,  4, 45, 41, 14,  1,
       19, 34, 51, 31, 18, 33, 53, 12, 49, 13, 35, 54, 29,  8,  0, 10, 22,
       25, 42, 40,  9, 38, 26, 32,  5, 36, 24, 44, 16, 37, 50, 43, 20, 17,
       28,  2, 47, 21], dtype=int64), 'cur_cost': 110272.0, 'intermediate_solutions': [{'tour': array([ 7, 42, 12, 54, 38, 23, 25, 35, 31, 24, 19,  2, 53, 40, 47, 43, 17,
       26, 39, 33, 52, 41,  5, 37, 50,  0, 45, 46, 27, 48, 29, 14,  6, 22,
       15, 20,  8, 10, 28, 18, 11, 44,  3, 13, 36, 49, 16,  4, 21,  1, 34,
        9, 51, 30, 32], dtype=int64), 'cur_cost': 113383.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([54,  7, 42, 12, 38, 23, 25, 35, 31, 24, 19,  2, 53, 40, 47, 43, 17,
       26, 39, 33, 52, 41,  5, 37, 50,  0, 45, 46, 27, 48, 29, 14,  6, 22,
       15, 20,  8, 10, 28, 18, 11, 44,  3, 13, 36, 49, 16,  4, 21,  1, 34,
        9, 51, 30, 32], dtype=int64), 'cur_cost': 116421.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([38, 54,  7, 42, 12, 23, 25, 35, 31, 24, 19,  2, 53, 40, 47, 43, 17,
       26, 39, 33, 52, 41,  5, 37, 50,  0, 45, 46, 27, 48, 29, 14,  6, 22,
       15, 20,  8, 10, 28, 18, 11, 44,  3, 13, 36, 49, 16,  4, 21,  1, 34,
        9, 51, 30, 32], dtype=int64), 'cur_cost': 116191.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([12, 54,  7, 42, 38, 23, 25, 35, 31, 24, 19,  2, 53, 40, 47, 43, 17,
       26, 39, 33, 52, 41,  5, 37, 50,  0, 45, 46, 27, 48, 29, 14,  6, 22,
       15, 20,  8, 10, 28, 18, 11, 44,  3, 13, 36, 49, 16,  4, 21,  1, 34,
        9, 51, 30, 32], dtype=int64), 'cur_cost': 111066.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([12, 38, 54,  7, 42, 23, 25, 35, 31, 24, 19,  2, 53, 40, 47, 43, 17,
       26, 39, 33, 52, 41,  5, 37, 50,  0, 45, 46, 27, 48, 29, 14,  6, 22,
       15, 20,  8, 10, 28, 18, 11, 44,  3, 13, 36, 49, 16,  4, 21,  1, 34,
        9, 51, 30, 32], dtype=int64), 'cur_cost': 116153.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 09:52:08,145 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:08,145 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:08,150 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=16455.000, 多样性=0.970
2025-08-05 09:52:08,150 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 09:52:08,151 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 09:52:08,151 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:08,151 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.11012289558592812, 'best_improvement': -0.5287068004459309}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.0041841004184098385}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.003863775521027161, 'recent_improvements': [0.04410510086912065, -0.16624190323921556, 0.036377549827066334], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 10450, 'new_best_cost': 10450, 'quality_improvement': 0.0, 'old_diversity': 0.9818181818181818, 'new_diversity': 0.9818181818181818, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启']}
2025-08-05 09:52:08,152 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 09:52:08,152 - __main__ - INFO - composite10_55 开始进化第 2 代
2025-08-05 09:52:08,152 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 09:52:08,152 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:08,153 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=16455.000, 多样性=0.970
2025-08-05 09:52:08,153 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:08,157 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.970
2025-08-05 09:52:08,158 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:08,160 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.982
2025-08-05 09:52:08,163 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 09:52:08,163 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:08,163 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-05 09:52:08,164 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-05 09:52:08,195 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.250, 适应度梯度: 2852.567, 聚类评分: 0.000, 覆盖率: 0.128, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:08,196 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 09:52:08,196 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:08,196 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite10_55
2025-08-05 09:52:08,201 - visualization.landscape_visualizer - INFO - 插值约束: 162 个点被约束到最小值 10450.00
2025-08-05 09:52:08,321 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite10_55\landscape_composite10_55_iter_107_20250805_095208.html
2025-08-05 09:52:08,366 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite10_55\dashboard_composite10_55_iter_107_20250805_095208.html
2025-08-05 09:52:08,366 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 107
2025-08-05 09:52:08,366 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 09:52:08,366 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2032秒
2025-08-05 09:52:08,366 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.25, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 2852.5666666666675, 'local_optima_density': 0.25, 'gradient_variance': 1226543068.0388887, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1282, 'fitness_entropy': 0.9155385080075168, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.128)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 2852.567)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358728.1965785, 'performance_metrics': {}}}
2025-08-05 09:52:08,366 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:08,367 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:08,367 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:08,367 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:08,367 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 09:52:08,367 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 09:52:08,367 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 09:52:08,367 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:08,367 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:08,368 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 09:52:08,368 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:08,368 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:08,368 - experts.management.collaboration_manager - INFO - 识别精英个体: {3, 4} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:08,368 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:08,368 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:08,368 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:08,370 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 55
2025-08-05 09:52:08,370 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,370 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,370 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,370 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,371 - ExplorationExpert - INFO - 探索路径生成完成，成本: 90176.0, 路径长度: 55, 收集中间解: 3
2025-08-05 09:52:08,371 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [51, 5, 28, 29, 7, 30, 34, 31, 4, 37, 2, 3, 8, 27, 24, 9, 6, 26, 19, 14, 18, 25, 38, 49, 50, 45, 39, 20, 15, 54, 22, 12, 16, 46, 17, 35, 11, 47, 1, 52, 33, 48, 41, 36, 40, 42, 10, 21, 43, 32, 44, 0, 53, 13, 23], 'cur_cost': 90176.0, 'intermediate_solutions': [{'tour': [26, 32, 20, 29, 40, 30, 34, 31, 36, 7, 28, 3, 8, 27, 53, 46, 21, 11, 19, 14, 18, 13, 25, 23, 0, 9, 39, 2, 4, 37, 5, 12, 16, 35, 45, 48, 17, 47, 54, 52, 50, 38, 24, 22, 41, 42, 1, 33, 43, 49, 44, 51, 15, 6, 10], 'cur_cost': 64347.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [26, 32, 35, 29, 40, 30, 34, 31, 36, 7, 28, 51, 44, 49, 43, 33, 1, 42, 41, 22, 24, 38, 50, 52, 54, 47, 17, 48, 45, 20, 16, 12, 5, 37, 4, 2, 39, 9, 0, 23, 25, 13, 18, 14, 19, 11, 21, 46, 53, 27, 8, 3, 15, 6, 10], 'cur_cost': 58630.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [26, 32, 35, 29, 40, 30, 31, 36, 7, 28, 3, 8, 27, 53, 46, 21, 11, 19, 14, 18, 13, 25, 23, 0, 9, 39, 2, 4, 37, 5, 12, 16, 20, 45, 48, 17, 47, 54, 52, 50, 38, 24, 22, 41, 42, 1, 33, 43, 49, 34, 44, 51, 15, 6, 10], 'cur_cost': 59585.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:08,371 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 90176.00)
2025-08-05 09:52:08,371 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:08,372 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:08,372 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:08,373 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 09:52:08,373 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,374 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,374 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,374 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,374 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10753.0, 路径长度: 55, 收集中间解: 3
2025-08-05 09:52:08,375 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 16, 21, 20, 15, 18, 12, 19, 17, 13, 14, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 10753.0, 'intermediate_solutions': [{'tour': [52, 53, 12, 18, 14, 28, 31, 43, 27, 37, 34, 29, 10, 2, 7, 9, 5, 24, 40, 6, 26, 25, 38, 35, 36, 33, 42, 0, 19, 54, 50, 45, 51, 46, 17, 15, 21, 44, 16, 30, 49, 13, 41, 22, 23, 32, 39, 1, 8, 3, 4, 11, 20, 47, 48], 'cur_cost': 45961.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [52, 53, 12, 18, 14, 28, 31, 43, 27, 37, 34, 29, 10, 2, 7, 9, 5, 24, 40, 6, 26, 25, 38, 35, 36, 33, 42, 0, 19, 54, 50, 45, 51, 46, 17, 15, 21, 44, 16, 13, 49, 1, 39, 32, 23, 22, 41, 30, 8, 3, 4, 11, 20, 47, 48], 'cur_cost': 43692.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [52, 53, 12, 18, 14, 28, 31, 43, 27, 37, 34, 29, 10, 2, 7, 9, 5, 24, 40, 6, 26, 25, 38, 35, 36, 33, 42, 11, 0, 19, 54, 50, 45, 51, 46, 17, 15, 21, 44, 16, 13, 49, 30, 41, 22, 23, 32, 39, 1, 8, 3, 4, 20, 47, 48], 'cur_cost': 45472.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:08,375 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 10753.00)
2025-08-05 09:52:08,375 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:08,375 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:08,375 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:08,377 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 09:52:08,377 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,377 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,377 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,377 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,378 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10742.0, 路径长度: 55, 收集中间解: 3
2025-08-05 09:52:08,378 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 5, 9, 7, 3, 6, 4, 8, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 10742.0, 'intermediate_solutions': [{'tour': [52, 32, 35, 18, 40, 43, 36, 27, 28, 29, 2, 53, 46, 5, 24, 19, 6, 26, 13, 25, 23, 0, 9, 39, 50, 45, 16, 20, 15, 21, 54, 49, 37, 33, 31, 4, 11, 3, 1, 17, 38, 48, 44, 41, 14, 30, 47, 51, 10, 12, 34, 7, 22, 42, 8], 'cur_cost': 92360.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [52, 32, 35, 18, 40, 43, 36, 37, 28, 29, 2, 53, 46, 5, 24, 19, 6, 26, 13, 25, 23, 0, 9, 39, 50, 45, 16, 20, 15, 21, 54, 49, 27, 33, 31, 4, 11, 3, 1, 17, 38, 48, 44, 30, 14, 41, 47, 51, 10, 12, 34, 7, 22, 42, 8], 'cur_cost': 92688.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [52, 32, 35, 18, 40, 43, 36, 37, 28, 29, 2, 53, 46, 5, 24, 19, 6, 26, 13, 25, 23, 0, 9, 39, 50, 45, 16, 20, 15, 38, 21, 54, 49, 27, 33, 31, 4, 11, 3, 1, 17, 48, 44, 41, 14, 30, 47, 51, 10, 12, 34, 7, 22, 42, 8], 'cur_cost': 94495.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:08,378 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 10742.00)
2025-08-05 09:52:08,378 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:52:08,378 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:52:08,378 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:08,380 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 09:52:08,380 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,380 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,381 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,381 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,381 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10797.0, 路径长度: 55, 收集中间解: 3
2025-08-05 09:52:08,382 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 9, 4, 5, 3, 7, 8, 1, 6, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 10797.0, 'intermediate_solutions': [{'tour': [0, 5, 12, 2, 7, 9, 3, 6, 4, 1, 8, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 13, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 34, 17, 14, 16, 15, 20, 18, 19, 21, 11], 'cur_cost': 28809.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 12, 2, 7, 9, 3, 6, 4, 1, 8, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 43, 33, 42, 37, 38, 36, 35, 27, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 19, 21, 11], 'cur_cost': 18652.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 12, 2, 7, 9, 3, 6, 4, 1, 8, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 18, 19, 21, 20, 11], 'cur_cost': 16475.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:08,382 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 10797.00)
2025-08-05 09:52:08,382 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:08,382 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:08,382 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:08,386 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 55
2025-08-05 09:52:08,386 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,386 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,386 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,387 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,387 - ExplorationExpert - INFO - 探索路径生成完成，成本: 59169.0, 路径长度: 55, 收集中间解: 3
2025-08-05 09:52:08,387 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [23, 25, 40, 28, 41, 27, 33, 39, 26, 34, 32, 7, 0, 43, 37, 8, 4, 30, 42, 9, 6, 1, 18, 13, 17, 21, 45, 11, 48, 19, 50, 44, 16, 15, 47, 20, 52, 53, 22, 29, 24, 3, 35, 10, 36, 31, 2, 38, 46, 54, 49, 14, 12, 51, 5], 'cur_cost': 59169.0, 'intermediate_solutions': [{'tour': [0, 20, 7, 21, 19, 17, 13, 14, 16, 15, 18, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 5, 4, 6, 1, 9, 3, 2], 'cur_cost': 16571.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 15, 16, 14, 13, 17, 19, 21, 7, 18, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 1, 4, 6, 5, 9, 3, 2], 'cur_cost': 16475.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 7, 21, 19, 17, 13, 14, 16, 15, 18, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 53, 8, 1, 4, 6, 5, 9, 3, 2], 'cur_cost': 22371.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:08,387 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 59169.00)
2025-08-05 09:52:08,387 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:08,387 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:08,387 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:08,391 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 55
2025-08-05 09:52:08,391 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,391 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,392 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,392 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,392 - ExplorationExpert - INFO - 探索路径生成完成，成本: 60480.0, 路径长度: 55, 收集中间解: 3
2025-08-05 09:52:08,392 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [21, 19, 13, 50, 28, 30, 29, 0, 33, 10, 4, 38, 41, 6, 42, 25, 7, 24, 32, 35, 8, 43, 37, 23, 39, 31, 44, 11, 45, 46, 53, 16, 14, 51, 20, 17, 49, 18, 52, 48, 15, 5, 26, 1, 27, 40, 22, 34, 36, 2, 3, 9, 12, 47, 54], 'cur_cost': 60480.0, 'intermediate_solutions': [{'tour': [51, 19, 30, 35, 36, 34, 23, 41, 40, 25, 26, 39, 9, 4, 54, 1, 7, 3, 6, 42, 33, 24, 44, 29, 45, 11, 18, 13, 53, 50, 12, 52, 20, 47, 15, 46, 17, 16, 48, 49, 14, 21, 28, 38, 22, 31, 37, 27, 43, 32, 0, 8, 2, 5, 10], 'cur_cost': 55140.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [51, 19, 30, 35, 36, 34, 23, 41, 40, 25, 26, 39, 9, 4, 29, 1, 7, 3, 6, 42, 33, 24, 44, 54, 45, 11, 18, 13, 53, 50, 12, 52, 20, 47, 15, 46, 17, 16, 48, 49, 14, 21, 28, 38, 22, 31, 37, 27, 10, 5, 2, 8, 0, 32, 43], 'cur_cost': 48190.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [51, 19, 30, 35, 36, 34, 23, 41, 40, 25, 26, 39, 9, 4, 29, 1, 7, 3, 6, 8, 42, 33, 24, 44, 54, 45, 11, 18, 13, 53, 50, 12, 52, 20, 47, 15, 46, 17, 16, 48, 49, 14, 21, 28, 38, 22, 31, 37, 27, 43, 32, 0, 2, 5, 10], 'cur_cost': 48030.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:08,392 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 60480.00)
2025-08-05 09:52:08,392 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:08,393 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:08,393 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:08,394 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 09:52:08,394 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,394 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,394 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,395 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,395 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16475.0, 路径长度: 55, 收集中间解: 3
2025-08-05 09:52:08,395 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 7, 17, 8, 1, 4, 6, 5, 9, 3, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 16475.0, 'intermediate_solutions': [{'tour': [46, 21, 28, 43, 23, 39, 36, 6, 30, 42, 10, 8, 34, 9, 4, 33, 26, 37, 38, 0, 31, 32, 41, 40, 2, 24, 53, 44, 50, 11, 17, 16, 47, 52, 18, 48, 45, 51, 20, 14, 19, 54, 49, 25, 35, 29, 22, 1, 3, 7, 27, 5, 12, 15, 13], 'cur_cost': 54511.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [46, 21, 28, 43, 23, 39, 36, 34, 30, 42, 10, 24, 2, 40, 41, 32, 31, 0, 38, 37, 26, 33, 4, 9, 6, 8, 53, 44, 50, 11, 17, 16, 47, 52, 18, 48, 45, 51, 20, 14, 19, 54, 49, 25, 35, 29, 22, 1, 3, 7, 27, 5, 12, 15, 13], 'cur_cost': 50051.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [46, 21, 28, 43, 23, 39, 36, 34, 30, 42, 10, 8, 6, 9, 4, 33, 26, 37, 38, 0, 31, 32, 41, 40, 2, 24, 53, 44, 50, 11, 17, 16, 47, 52, 18, 48, 45, 51, 20, 14, 25, 19, 54, 49, 35, 29, 22, 1, 3, 7, 27, 5, 12, 15, 13], 'cur_cost': 52223.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:08,395 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 16475.00)
2025-08-05 09:52:08,395 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 09:52:08,395 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:08,395 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:08,396 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 115797.0
2025-08-05 09:52:08,400 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 09:52:08,400 - ExploitationExpert - INFO - res_population_costs: [10450, 10451.0, 10445.0, 10443, 10443]
2025-08-05 09:52:08,400 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       14, 11, 13, 17, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 14, 11, 13, 17, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 30, 24, 26, 31, 25, 23, 28, 32, 27, 22, 29,
       35, 36, 33, 42, 37, 38, 41, 40, 34, 39, 43, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64), array([ 0, 21, 19, 18, 12, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  9,  7,  2,  3,
        5,  6,  4,  1], dtype=int64)]
2025-08-05 09:52:08,402 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:08,402 - ExploitationExpert - INFO - populations: [{'tour': [51, 5, 28, 29, 7, 30, 34, 31, 4, 37, 2, 3, 8, 27, 24, 9, 6, 26, 19, 14, 18, 25, 38, 49, 50, 45, 39, 20, 15, 54, 22, 12, 16, 46, 17, 35, 11, 47, 1, 52, 33, 48, 41, 36, 40, 42, 10, 21, 43, 32, 44, 0, 53, 13, 23], 'cur_cost': 90176.0}, {'tour': [0, 16, 21, 20, 15, 18, 12, 19, 17, 13, 14, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 10753.0}, {'tour': [0, 1, 2, 5, 9, 7, 3, 6, 4, 8, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 10742.0}, {'tour': [0, 9, 4, 5, 3, 7, 8, 1, 6, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 10797.0}, {'tour': [23, 25, 40, 28, 41, 27, 33, 39, 26, 34, 32, 7, 0, 43, 37, 8, 4, 30, 42, 9, 6, 1, 18, 13, 17, 21, 45, 11, 48, 19, 50, 44, 16, 15, 47, 20, 52, 53, 22, 29, 24, 3, 35, 10, 36, 31, 2, 38, 46, 54, 49, 14, 12, 51, 5], 'cur_cost': 59169.0}, {'tour': [21, 19, 13, 50, 28, 30, 29, 0, 33, 10, 4, 38, 41, 6, 42, 25, 7, 24, 32, 35, 8, 43, 37, 23, 39, 31, 44, 11, 45, 46, 53, 16, 14, 51, 20, 17, 49, 18, 52, 48, 15, 5, 26, 1, 27, 40, 22, 34, 36, 2, 3, 9, 12, 47, 54], 'cur_cost': 60480.0}, {'tour': [0, 7, 17, 8, 1, 4, 6, 5, 9, 3, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 16475.0}, {'tour': array([53, 10, 49, 42, 37, 16, 25, 15, 48, 26,  5, 34, 27,  4, 32, 20, 19,
        8, 45, 28, 50, 22,  0, 51,  9, 54,  3, 38,  2, 46, 44, 43, 40, 31,
        7, 21, 33, 11, 14, 47, 39,  1, 24,  6, 17, 23, 30, 41, 52, 36, 18,
       13, 29, 12, 35], dtype=int64), 'cur_cost': 115797.0}, {'tour': [49, 11, 22, 43, 33, 32, 17, 24, 52, 50, 5, 48, 51, 15, 26, 29, 28, 12, 44, 19, 9, 8, 46, 45, 36, 3, 34, 37, 1, 30, 13, 35, 6, 54, 18, 31, 53, 7, 47, 40, 25, 39, 10, 41, 23, 38, 2, 4, 21, 14, 20, 16, 0, 42, 27], 'cur_cost': 93233.0}, {'tour': [3, 52, 15, 7, 11, 48, 6, 27, 39, 30, 46, 23, 4, 45, 41, 14, 1, 19, 34, 51, 31, 18, 33, 53, 12, 49, 13, 35, 54, 29, 8, 0, 10, 22, 25, 42, 40, 9, 38, 26, 32, 5, 36, 24, 44, 16, 37, 50, 43, 20, 17, 28, 2, 47, 21], 'cur_cost': 110272.0}]
2025-08-05 09:52:08,403 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:08,403 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 276, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 276, 'cache_hits': 0, 'similarity_calculations': 1359, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:08,404 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([53, 10, 49, 42, 37, 16, 25, 15, 48, 26,  5, 34, 27,  4, 32, 20, 19,
        8, 45, 28, 50, 22,  0, 51,  9, 54,  3, 38,  2, 46, 44, 43, 40, 31,
        7, 21, 33, 11, 14, 47, 39,  1, 24,  6, 17, 23, 30, 41, 52, 36, 18,
       13, 29, 12, 35], dtype=int64), 'cur_cost': 115797.0, 'intermediate_solutions': [{'tour': array([30, 20, 46, 21, 14, 43,  3, 37, 36, 29, 34, 53, 10,  5,  7, 19, 32,
       11, 40, 25, 44, 13,  9, 23,  0, 33, 42,  2,  4, 50, 54, 45, 27, 41,
       17, 15, 47, 16, 52, 49, 38, 24, 22, 48, 31,  1, 51,  6, 12, 39, 18,
       26, 28, 35,  8]), 'cur_cost': 93773.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([21, 30, 20, 46, 14, 43,  3, 37, 36, 29, 34, 53, 10,  5,  7, 19, 32,
       11, 40, 25, 44, 13,  9, 23,  0, 33, 42,  2,  4, 50, 54, 45, 27, 41,
       17, 15, 47, 16, 52, 49, 38, 24, 22, 48, 31,  1, 51,  6, 12, 39, 18,
       26, 28, 35,  8]), 'cur_cost': 97540.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14, 21, 30, 20, 46, 43,  3, 37, 36, 29, 34, 53, 10,  5,  7, 19, 32,
       11, 40, 25, 44, 13,  9, 23,  0, 33, 42,  2,  4, 50, 54, 45, 27, 41,
       17, 15, 47, 16, 52, 49, 38, 24, 22, 48, 31,  1, 51,  6, 12, 39, 18,
       26, 28, 35,  8]), 'cur_cost': 95699.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([46, 21, 30, 20, 14, 43,  3, 37, 36, 29, 34, 53, 10,  5,  7, 19, 32,
       11, 40, 25, 44, 13,  9, 23,  0, 33, 42,  2,  4, 50, 54, 45, 27, 41,
       17, 15, 47, 16, 52, 49, 38, 24, 22, 48, 31,  1, 51,  6, 12, 39, 18,
       26, 28, 35,  8]), 'cur_cost': 96507.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([46, 14, 21, 30, 20, 43,  3, 37, 36, 29, 34, 53, 10,  5,  7, 19, 32,
       11, 40, 25, 44, 13,  9, 23,  0, 33, 42,  2,  4, 50, 54, 45, 27, 41,
       17, 15, 47, 16, 52, 49, 38, 24, 22, 48, 31,  1, 51,  6, 12, 39, 18,
       26, 28, 35,  8]), 'cur_cost': 96520.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:08,404 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 115797.00)
2025-08-05 09:52:08,404 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:52:08,404 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:52:08,404 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:08,406 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 09:52:08,406 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,406 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,407 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,407 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,407 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10778.0, 路径长度: 55, 收集中间解: 3
2025-08-05 09:52:08,407 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 10, 4, 2, 7, 9, 5, 3, 6, 1, 8, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 10778.0, 'intermediate_solutions': [{'tour': [49, 11, 22, 43, 33, 32, 17, 24, 52, 50, 5, 48, 51, 15, 26, 29, 28, 12, 44, 19, 9, 8, 46, 45, 36, 3, 54, 37, 1, 30, 13, 35, 6, 34, 18, 31, 53, 7, 47, 40, 25, 39, 10, 41, 23, 38, 2, 4, 21, 14, 20, 16, 0, 42, 27], 'cur_cost': 98428.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [49, 11, 22, 50, 52, 24, 17, 32, 33, 43, 5, 48, 51, 15, 26, 29, 28, 12, 44, 19, 9, 8, 46, 45, 36, 3, 34, 37, 1, 30, 13, 35, 6, 54, 18, 31, 53, 7, 47, 40, 25, 39, 10, 41, 23, 38, 2, 4, 21, 14, 20, 16, 0, 42, 27], 'cur_cost': 93351.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [49, 11, 22, 43, 33, 32, 17, 24, 52, 50, 5, 48, 51, 15, 26, 29, 28, 12, 44, 19, 9, 8, 46, 45, 36, 3, 34, 37, 1, 30, 13, 35, 6, 54, 18, 31, 53, 7, 47, 40, 25, 39, 10, 41, 23, 38, 4, 21, 14, 2, 20, 16, 0, 42, 27], 'cur_cost': 98962.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:08,407 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 10778.00)
2025-08-05 09:52:08,407 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 09:52:08,408 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:08,408 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:08,408 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 101141.0
2025-08-05 09:52:08,414 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 09:52:08,414 - ExploitationExpert - INFO - res_population_costs: [10450, 10451.0, 10445.0, 10443, 10443]
2025-08-05 09:52:08,414 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       14, 11, 13, 17, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 14, 11, 13, 17, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 30, 24, 26, 31, 25, 23, 28, 32, 27, 22, 29,
       35, 36, 33, 42, 37, 38, 41, 40, 34, 39, 43, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64), array([ 0, 21, 19, 18, 12, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  9,  7,  2,  3,
        5,  6,  4,  1], dtype=int64)]
2025-08-05 09:52:08,416 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:08,416 - ExploitationExpert - INFO - populations: [{'tour': [51, 5, 28, 29, 7, 30, 34, 31, 4, 37, 2, 3, 8, 27, 24, 9, 6, 26, 19, 14, 18, 25, 38, 49, 50, 45, 39, 20, 15, 54, 22, 12, 16, 46, 17, 35, 11, 47, 1, 52, 33, 48, 41, 36, 40, 42, 10, 21, 43, 32, 44, 0, 53, 13, 23], 'cur_cost': 90176.0}, {'tour': [0, 16, 21, 20, 15, 18, 12, 19, 17, 13, 14, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 10753.0}, {'tour': [0, 1, 2, 5, 9, 7, 3, 6, 4, 8, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 10742.0}, {'tour': [0, 9, 4, 5, 3, 7, 8, 1, 6, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 10797.0}, {'tour': [23, 25, 40, 28, 41, 27, 33, 39, 26, 34, 32, 7, 0, 43, 37, 8, 4, 30, 42, 9, 6, 1, 18, 13, 17, 21, 45, 11, 48, 19, 50, 44, 16, 15, 47, 20, 52, 53, 22, 29, 24, 3, 35, 10, 36, 31, 2, 38, 46, 54, 49, 14, 12, 51, 5], 'cur_cost': 59169.0}, {'tour': [21, 19, 13, 50, 28, 30, 29, 0, 33, 10, 4, 38, 41, 6, 42, 25, 7, 24, 32, 35, 8, 43, 37, 23, 39, 31, 44, 11, 45, 46, 53, 16, 14, 51, 20, 17, 49, 18, 52, 48, 15, 5, 26, 1, 27, 40, 22, 34, 36, 2, 3, 9, 12, 47, 54], 'cur_cost': 60480.0}, {'tour': [0, 7, 17, 8, 1, 4, 6, 5, 9, 3, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 16475.0}, {'tour': array([53, 10, 49, 42, 37, 16, 25, 15, 48, 26,  5, 34, 27,  4, 32, 20, 19,
        8, 45, 28, 50, 22,  0, 51,  9, 54,  3, 38,  2, 46, 44, 43, 40, 31,
        7, 21, 33, 11, 14, 47, 39,  1, 24,  6, 17, 23, 30, 41, 52, 36, 18,
       13, 29, 12, 35], dtype=int64), 'cur_cost': 115797.0}, {'tour': [0, 10, 4, 2, 7, 9, 5, 3, 6, 1, 8, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 10778.0}, {'tour': array([51, 44, 17, 46, 11, 29, 41,  8, 53, 27, 28, 18,  3,  2, 19, 45, 40,
       47, 13,  6, 24, 22, 34, 54, 35, 21, 16, 48, 30, 42, 23, 12,  7,  9,
       31, 20, 32, 26, 10, 49,  5, 33,  1, 14, 38,  0, 25, 36, 39, 15, 50,
       52, 37,  4, 43], dtype=int64), 'cur_cost': 101141.0}]
2025-08-05 09:52:08,417 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:08,417 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 277, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 277, 'cache_hits': 0, 'similarity_calculations': 1362, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:08,418 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([51, 44, 17, 46, 11, 29, 41,  8, 53, 27, 28, 18,  3,  2, 19, 45, 40,
       47, 13,  6, 24, 22, 34, 54, 35, 21, 16, 48, 30, 42, 23, 12,  7,  9,
       31, 20, 32, 26, 10, 49,  5, 33,  1, 14, 38,  0, 25, 36, 39, 15, 50,
       52, 37,  4, 43], dtype=int64), 'cur_cost': 101141.0, 'intermediate_solutions': [{'tour': array([15, 52,  3,  7, 11, 48,  6, 27, 39, 30, 46, 23,  4, 45, 41, 14,  1,
       19, 34, 51, 31, 18, 33, 53, 12, 49, 13, 35, 54, 29,  8,  0, 10, 22,
       25, 42, 40,  9, 38, 26, 32,  5, 36, 24, 44, 16, 37, 50, 43, 20, 17,
       28,  2, 47, 21]), 'cur_cost': 104549.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 7, 15, 52,  3, 11, 48,  6, 27, 39, 30, 46, 23,  4, 45, 41, 14,  1,
       19, 34, 51, 31, 18, 33, 53, 12, 49, 13, 35, 54, 29,  8,  0, 10, 22,
       25, 42, 40,  9, 38, 26, 32,  5, 36, 24, 44, 16, 37, 50, 43, 20, 17,
       28,  2, 47, 21]), 'cur_cost': 110272.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([11,  7, 15, 52,  3, 48,  6, 27, 39, 30, 46, 23,  4, 45, 41, 14,  1,
       19, 34, 51, 31, 18, 33, 53, 12, 49, 13, 35, 54, 29,  8,  0, 10, 22,
       25, 42, 40,  9, 38, 26, 32,  5, 36, 24, 44, 16, 37, 50, 43, 20, 17,
       28,  2, 47, 21]), 'cur_cost': 109391.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 3,  7, 15, 52, 11, 48,  6, 27, 39, 30, 46, 23,  4, 45, 41, 14,  1,
       19, 34, 51, 31, 18, 33, 53, 12, 49, 13, 35, 54, 29,  8,  0, 10, 22,
       25, 42, 40,  9, 38, 26, 32,  5, 36, 24, 44, 16, 37, 50, 43, 20, 17,
       28,  2, 47, 21]), 'cur_cost': 105459.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 3, 11,  7, 15, 52, 48,  6, 27, 39, 30, 46, 23,  4, 45, 41, 14,  1,
       19, 34, 51, 31, 18, 33, 53, 12, 49, 13, 35, 54, 29,  8,  0, 10, 22,
       25, 42, 40,  9, 38, 26, 32,  5, 36, 24, 44, 16, 37, 50, 43, 20, 17,
       28,  2, 47, 21]), 'cur_cost': 109036.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:08,418 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 101141.00)
2025-08-05 09:52:08,418 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:08,418 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:08,421 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [51, 5, 28, 29, 7, 30, 34, 31, 4, 37, 2, 3, 8, 27, 24, 9, 6, 26, 19, 14, 18, 25, 38, 49, 50, 45, 39, 20, 15, 54, 22, 12, 16, 46, 17, 35, 11, 47, 1, 52, 33, 48, 41, 36, 40, 42, 10, 21, 43, 32, 44, 0, 53, 13, 23], 'cur_cost': 90176.0, 'intermediate_solutions': [{'tour': [26, 32, 20, 29, 40, 30, 34, 31, 36, 7, 28, 3, 8, 27, 53, 46, 21, 11, 19, 14, 18, 13, 25, 23, 0, 9, 39, 2, 4, 37, 5, 12, 16, 35, 45, 48, 17, 47, 54, 52, 50, 38, 24, 22, 41, 42, 1, 33, 43, 49, 44, 51, 15, 6, 10], 'cur_cost': 64347.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [26, 32, 35, 29, 40, 30, 34, 31, 36, 7, 28, 51, 44, 49, 43, 33, 1, 42, 41, 22, 24, 38, 50, 52, 54, 47, 17, 48, 45, 20, 16, 12, 5, 37, 4, 2, 39, 9, 0, 23, 25, 13, 18, 14, 19, 11, 21, 46, 53, 27, 8, 3, 15, 6, 10], 'cur_cost': 58630.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [26, 32, 35, 29, 40, 30, 31, 36, 7, 28, 3, 8, 27, 53, 46, 21, 11, 19, 14, 18, 13, 25, 23, 0, 9, 39, 2, 4, 37, 5, 12, 16, 20, 45, 48, 17, 47, 54, 52, 50, 38, 24, 22, 41, 42, 1, 33, 43, 49, 34, 44, 51, 15, 6, 10], 'cur_cost': 59585.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 21, 20, 15, 18, 12, 19, 17, 13, 14, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 10753.0, 'intermediate_solutions': [{'tour': [52, 53, 12, 18, 14, 28, 31, 43, 27, 37, 34, 29, 10, 2, 7, 9, 5, 24, 40, 6, 26, 25, 38, 35, 36, 33, 42, 0, 19, 54, 50, 45, 51, 46, 17, 15, 21, 44, 16, 30, 49, 13, 41, 22, 23, 32, 39, 1, 8, 3, 4, 11, 20, 47, 48], 'cur_cost': 45961.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [52, 53, 12, 18, 14, 28, 31, 43, 27, 37, 34, 29, 10, 2, 7, 9, 5, 24, 40, 6, 26, 25, 38, 35, 36, 33, 42, 0, 19, 54, 50, 45, 51, 46, 17, 15, 21, 44, 16, 13, 49, 1, 39, 32, 23, 22, 41, 30, 8, 3, 4, 11, 20, 47, 48], 'cur_cost': 43692.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [52, 53, 12, 18, 14, 28, 31, 43, 27, 37, 34, 29, 10, 2, 7, 9, 5, 24, 40, 6, 26, 25, 38, 35, 36, 33, 42, 11, 0, 19, 54, 50, 45, 51, 46, 17, 15, 21, 44, 16, 13, 49, 30, 41, 22, 23, 32, 39, 1, 8, 3, 4, 20, 47, 48], 'cur_cost': 45472.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 5, 9, 7, 3, 6, 4, 8, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 10742.0, 'intermediate_solutions': [{'tour': [52, 32, 35, 18, 40, 43, 36, 27, 28, 29, 2, 53, 46, 5, 24, 19, 6, 26, 13, 25, 23, 0, 9, 39, 50, 45, 16, 20, 15, 21, 54, 49, 37, 33, 31, 4, 11, 3, 1, 17, 38, 48, 44, 41, 14, 30, 47, 51, 10, 12, 34, 7, 22, 42, 8], 'cur_cost': 92360.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [52, 32, 35, 18, 40, 43, 36, 37, 28, 29, 2, 53, 46, 5, 24, 19, 6, 26, 13, 25, 23, 0, 9, 39, 50, 45, 16, 20, 15, 21, 54, 49, 27, 33, 31, 4, 11, 3, 1, 17, 38, 48, 44, 30, 14, 41, 47, 51, 10, 12, 34, 7, 22, 42, 8], 'cur_cost': 92688.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [52, 32, 35, 18, 40, 43, 36, 37, 28, 29, 2, 53, 46, 5, 24, 19, 6, 26, 13, 25, 23, 0, 9, 39, 50, 45, 16, 20, 15, 38, 21, 54, 49, 27, 33, 31, 4, 11, 3, 1, 17, 48, 44, 41, 14, 30, 47, 51, 10, 12, 34, 7, 22, 42, 8], 'cur_cost': 94495.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 4, 5, 3, 7, 8, 1, 6, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 10797.0, 'intermediate_solutions': [{'tour': [0, 5, 12, 2, 7, 9, 3, 6, 4, 1, 8, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 13, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 34, 17, 14, 16, 15, 20, 18, 19, 21, 11], 'cur_cost': 28809.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 12, 2, 7, 9, 3, 6, 4, 1, 8, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 43, 33, 42, 37, 38, 36, 35, 27, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 19, 21, 11], 'cur_cost': 18652.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 12, 2, 7, 9, 3, 6, 4, 1, 8, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 18, 19, 21, 20, 11], 'cur_cost': 16475.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [23, 25, 40, 28, 41, 27, 33, 39, 26, 34, 32, 7, 0, 43, 37, 8, 4, 30, 42, 9, 6, 1, 18, 13, 17, 21, 45, 11, 48, 19, 50, 44, 16, 15, 47, 20, 52, 53, 22, 29, 24, 3, 35, 10, 36, 31, 2, 38, 46, 54, 49, 14, 12, 51, 5], 'cur_cost': 59169.0, 'intermediate_solutions': [{'tour': [0, 20, 7, 21, 19, 17, 13, 14, 16, 15, 18, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 5, 4, 6, 1, 9, 3, 2], 'cur_cost': 16571.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 15, 16, 14, 13, 17, 19, 21, 7, 18, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 1, 4, 6, 5, 9, 3, 2], 'cur_cost': 16475.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 7, 21, 19, 17, 13, 14, 16, 15, 18, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 53, 8, 1, 4, 6, 5, 9, 3, 2], 'cur_cost': 22371.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [21, 19, 13, 50, 28, 30, 29, 0, 33, 10, 4, 38, 41, 6, 42, 25, 7, 24, 32, 35, 8, 43, 37, 23, 39, 31, 44, 11, 45, 46, 53, 16, 14, 51, 20, 17, 49, 18, 52, 48, 15, 5, 26, 1, 27, 40, 22, 34, 36, 2, 3, 9, 12, 47, 54], 'cur_cost': 60480.0, 'intermediate_solutions': [{'tour': [51, 19, 30, 35, 36, 34, 23, 41, 40, 25, 26, 39, 9, 4, 54, 1, 7, 3, 6, 42, 33, 24, 44, 29, 45, 11, 18, 13, 53, 50, 12, 52, 20, 47, 15, 46, 17, 16, 48, 49, 14, 21, 28, 38, 22, 31, 37, 27, 43, 32, 0, 8, 2, 5, 10], 'cur_cost': 55140.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [51, 19, 30, 35, 36, 34, 23, 41, 40, 25, 26, 39, 9, 4, 29, 1, 7, 3, 6, 42, 33, 24, 44, 54, 45, 11, 18, 13, 53, 50, 12, 52, 20, 47, 15, 46, 17, 16, 48, 49, 14, 21, 28, 38, 22, 31, 37, 27, 10, 5, 2, 8, 0, 32, 43], 'cur_cost': 48190.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [51, 19, 30, 35, 36, 34, 23, 41, 40, 25, 26, 39, 9, 4, 29, 1, 7, 3, 6, 8, 42, 33, 24, 44, 54, 45, 11, 18, 13, 53, 50, 12, 52, 20, 47, 15, 46, 17, 16, 48, 49, 14, 21, 28, 38, 22, 31, 37, 27, 43, 32, 0, 2, 5, 10], 'cur_cost': 48030.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 17, 8, 1, 4, 6, 5, 9, 3, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 16475.0, 'intermediate_solutions': [{'tour': [46, 21, 28, 43, 23, 39, 36, 6, 30, 42, 10, 8, 34, 9, 4, 33, 26, 37, 38, 0, 31, 32, 41, 40, 2, 24, 53, 44, 50, 11, 17, 16, 47, 52, 18, 48, 45, 51, 20, 14, 19, 54, 49, 25, 35, 29, 22, 1, 3, 7, 27, 5, 12, 15, 13], 'cur_cost': 54511.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [46, 21, 28, 43, 23, 39, 36, 34, 30, 42, 10, 24, 2, 40, 41, 32, 31, 0, 38, 37, 26, 33, 4, 9, 6, 8, 53, 44, 50, 11, 17, 16, 47, 52, 18, 48, 45, 51, 20, 14, 19, 54, 49, 25, 35, 29, 22, 1, 3, 7, 27, 5, 12, 15, 13], 'cur_cost': 50051.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [46, 21, 28, 43, 23, 39, 36, 34, 30, 42, 10, 8, 6, 9, 4, 33, 26, 37, 38, 0, 31, 32, 41, 40, 2, 24, 53, 44, 50, 11, 17, 16, 47, 52, 18, 48, 45, 51, 20, 14, 25, 19, 54, 49, 35, 29, 22, 1, 3, 7, 27, 5, 12, 15, 13], 'cur_cost': 52223.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([53, 10, 49, 42, 37, 16, 25, 15, 48, 26,  5, 34, 27,  4, 32, 20, 19,
        8, 45, 28, 50, 22,  0, 51,  9, 54,  3, 38,  2, 46, 44, 43, 40, 31,
        7, 21, 33, 11, 14, 47, 39,  1, 24,  6, 17, 23, 30, 41, 52, 36, 18,
       13, 29, 12, 35], dtype=int64), 'cur_cost': 115797.0, 'intermediate_solutions': [{'tour': array([30, 20, 46, 21, 14, 43,  3, 37, 36, 29, 34, 53, 10,  5,  7, 19, 32,
       11, 40, 25, 44, 13,  9, 23,  0, 33, 42,  2,  4, 50, 54, 45, 27, 41,
       17, 15, 47, 16, 52, 49, 38, 24, 22, 48, 31,  1, 51,  6, 12, 39, 18,
       26, 28, 35,  8]), 'cur_cost': 93773.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([21, 30, 20, 46, 14, 43,  3, 37, 36, 29, 34, 53, 10,  5,  7, 19, 32,
       11, 40, 25, 44, 13,  9, 23,  0, 33, 42,  2,  4, 50, 54, 45, 27, 41,
       17, 15, 47, 16, 52, 49, 38, 24, 22, 48, 31,  1, 51,  6, 12, 39, 18,
       26, 28, 35,  8]), 'cur_cost': 97540.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14, 21, 30, 20, 46, 43,  3, 37, 36, 29, 34, 53, 10,  5,  7, 19, 32,
       11, 40, 25, 44, 13,  9, 23,  0, 33, 42,  2,  4, 50, 54, 45, 27, 41,
       17, 15, 47, 16, 52, 49, 38, 24, 22, 48, 31,  1, 51,  6, 12, 39, 18,
       26, 28, 35,  8]), 'cur_cost': 95699.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([46, 21, 30, 20, 14, 43,  3, 37, 36, 29, 34, 53, 10,  5,  7, 19, 32,
       11, 40, 25, 44, 13,  9, 23,  0, 33, 42,  2,  4, 50, 54, 45, 27, 41,
       17, 15, 47, 16, 52, 49, 38, 24, 22, 48, 31,  1, 51,  6, 12, 39, 18,
       26, 28, 35,  8]), 'cur_cost': 96507.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([46, 14, 21, 30, 20, 43,  3, 37, 36, 29, 34, 53, 10,  5,  7, 19, 32,
       11, 40, 25, 44, 13,  9, 23,  0, 33, 42,  2,  4, 50, 54, 45, 27, 41,
       17, 15, 47, 16, 52, 49, 38, 24, 22, 48, 31,  1, 51,  6, 12, 39, 18,
       26, 28, 35,  8]), 'cur_cost': 96520.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 4, 2, 7, 9, 5, 3, 6, 1, 8, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 10778.0, 'intermediate_solutions': [{'tour': [49, 11, 22, 43, 33, 32, 17, 24, 52, 50, 5, 48, 51, 15, 26, 29, 28, 12, 44, 19, 9, 8, 46, 45, 36, 3, 54, 37, 1, 30, 13, 35, 6, 34, 18, 31, 53, 7, 47, 40, 25, 39, 10, 41, 23, 38, 2, 4, 21, 14, 20, 16, 0, 42, 27], 'cur_cost': 98428.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [49, 11, 22, 50, 52, 24, 17, 32, 33, 43, 5, 48, 51, 15, 26, 29, 28, 12, 44, 19, 9, 8, 46, 45, 36, 3, 34, 37, 1, 30, 13, 35, 6, 54, 18, 31, 53, 7, 47, 40, 25, 39, 10, 41, 23, 38, 2, 4, 21, 14, 20, 16, 0, 42, 27], 'cur_cost': 93351.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [49, 11, 22, 43, 33, 32, 17, 24, 52, 50, 5, 48, 51, 15, 26, 29, 28, 12, 44, 19, 9, 8, 46, 45, 36, 3, 34, 37, 1, 30, 13, 35, 6, 54, 18, 31, 53, 7, 47, 40, 25, 39, 10, 41, 23, 38, 4, 21, 14, 2, 20, 16, 0, 42, 27], 'cur_cost': 98962.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([51, 44, 17, 46, 11, 29, 41,  8, 53, 27, 28, 18,  3,  2, 19, 45, 40,
       47, 13,  6, 24, 22, 34, 54, 35, 21, 16, 48, 30, 42, 23, 12,  7,  9,
       31, 20, 32, 26, 10, 49,  5, 33,  1, 14, 38,  0, 25, 36, 39, 15, 50,
       52, 37,  4, 43], dtype=int64), 'cur_cost': 101141.0, 'intermediate_solutions': [{'tour': array([15, 52,  3,  7, 11, 48,  6, 27, 39, 30, 46, 23,  4, 45, 41, 14,  1,
       19, 34, 51, 31, 18, 33, 53, 12, 49, 13, 35, 54, 29,  8,  0, 10, 22,
       25, 42, 40,  9, 38, 26, 32,  5, 36, 24, 44, 16, 37, 50, 43, 20, 17,
       28,  2, 47, 21]), 'cur_cost': 104549.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 7, 15, 52,  3, 11, 48,  6, 27, 39, 30, 46, 23,  4, 45, 41, 14,  1,
       19, 34, 51, 31, 18, 33, 53, 12, 49, 13, 35, 54, 29,  8,  0, 10, 22,
       25, 42, 40,  9, 38, 26, 32,  5, 36, 24, 44, 16, 37, 50, 43, 20, 17,
       28,  2, 47, 21]), 'cur_cost': 110272.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([11,  7, 15, 52,  3, 48,  6, 27, 39, 30, 46, 23,  4, 45, 41, 14,  1,
       19, 34, 51, 31, 18, 33, 53, 12, 49, 13, 35, 54, 29,  8,  0, 10, 22,
       25, 42, 40,  9, 38, 26, 32,  5, 36, 24, 44, 16, 37, 50, 43, 20, 17,
       28,  2, 47, 21]), 'cur_cost': 109391.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 3,  7, 15, 52, 11, 48,  6, 27, 39, 30, 46, 23,  4, 45, 41, 14,  1,
       19, 34, 51, 31, 18, 33, 53, 12, 49, 13, 35, 54, 29,  8,  0, 10, 22,
       25, 42, 40,  9, 38, 26, 32,  5, 36, 24, 44, 16, 37, 50, 43, 20, 17,
       28,  2, 47, 21]), 'cur_cost': 105459.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 3, 11,  7, 15, 52, 48,  6, 27, 39, 30, 46, 23,  4, 45, 41, 14,  1,
       19, 34, 51, 31, 18, 33, 53, 12, 49, 13, 35, 54, 29,  8,  0, 10, 22,
       25, 42, 40,  9, 38, 26, 32,  5, 36, 24, 44, 16, 37, 50, 43, 20, 17,
       28,  2, 47, 21]), 'cur_cost': 109036.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 09:52:08,421 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:08,421 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:08,425 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10742.000, 多样性=0.923
2025-08-05 09:52:08,425 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 09:52:08,425 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 09:52:08,425 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:08,426 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.17048579146305695, 'best_improvement': 0.3471893041628684}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.047916666666666524}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.028059503826643722, 'recent_improvements': [-0.16624190323921556, 0.036377549827066334, -0.11012289558592812], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 5, 'new_count': 5, 'count_change': 0, 'old_best_cost': 10443, 'new_best_cost': 10443, 'quality_improvement': 0.0, 'old_diversity': 0.5836363636363636, 'new_diversity': 0.5836363636363636, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 09:52:08,426 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 09:52:08,427 - __main__ - INFO - composite10_55 开始进化第 3 代
2025-08-05 09:52:08,427 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 09:52:08,427 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:08,428 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10742.000, 多样性=0.923
2025-08-05 09:52:08,428 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:08,431 - PathExpert - INFO - 路径结构分析完成: 公共边数量=4, 路径相似性=0.923
2025-08-05 09:52:08,431 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:08,433 - EliteExpert - INFO - 精英解分析完成: 精英解数量=5, 多样性=0.584
2025-08-05 09:52:08,435 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 09:52:08,435 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:08,435 - LandscapeExpert - INFO - 添加精英解数据: 5个精英解
2025-08-05 09:52:08,435 - LandscapeExpert - INFO - 数据提取成功: 15个路径, 15个适应度值
2025-08-05 09:52:08,468 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.200, 适应度梯度: -10783.293, 聚类评分: 0.000, 覆盖率: 0.130, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:08,469 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 09:52:08,469 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:08,469 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite10_55
2025-08-05 09:52:08,474 - visualization.landscape_visualizer - INFO - 插值约束: 63 个点被约束到最小值 10443.00
2025-08-05 09:52:08,576 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite10_55\landscape_composite10_55_iter_108_20250805_095208.html
2025-08-05 09:52:08,652 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite10_55\dashboard_composite10_55_iter_108_20250805_095208.html
2025-08-05 09:52:08,652 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 108
2025-08-05 09:52:08,652 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 09:52:08,652 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2166秒
2025-08-05 09:52:08,653 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -10783.293333333337, 'local_optima_density': 0.2, 'gradient_variance': 882707888.9219557, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1295, 'fitness_entropy': 0.6713982813606044, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -10783.293)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.130)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358728.4688041, 'performance_metrics': {}}}
2025-08-05 09:52:08,653 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:08,653 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:08,653 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:08,653 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:08,653 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 09:52:08,653 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 09:52:08,653 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 09:52:08,654 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:08,654 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:08,654 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 09:52:08,654 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:08,654 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:08,654 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:08,654 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-05 09:52:08,654 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:08,654 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:08,655 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 111436.0
2025-08-05 09:52:08,661 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 09:52:08,662 - ExploitationExpert - INFO - res_population_costs: [10443, 10443, 10445.0, 10450, 10451.0, 10443]
2025-08-05 09:52:08,662 - ExploitationExpert - INFO - res_populations: [array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  9,  7,  2,  3,
        5,  6,  4,  1], dtype=int64), array([ 0, 21, 19, 18, 12, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       14, 11, 13, 17, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 14, 11, 13, 17, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 30, 24, 26, 31, 25, 23, 28, 32, 27, 22, 29,
       35, 36, 33, 42, 37, 38, 41, 40, 34, 39, 43, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 43, 39, 34, 40, 41, 38,
       36, 37, 42, 33, 35, 29, 22, 27, 32, 28, 23, 25, 31, 26, 24, 30, 49,
       46, 44, 53, 54, 50, 48, 52, 47, 51, 45, 13, 17, 14, 11, 16, 15, 20,
       18, 12, 21, 19], dtype=int64)]
2025-08-05 09:52:08,664 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:08,664 - ExploitationExpert - INFO - populations: [{'tour': array([32, 27, 10,  3, 33, 37, 35,  4, 45, 39, 50, 34, 15, 26, 46, 16, 36,
       49, 41, 23, 13, 40, 22,  7, 47,  1, 24, 52,  5, 25,  0, 17, 54, 48,
       21,  6, 18, 30, 53,  9, 29,  8, 43, 12, 11, 38, 51, 28,  2, 19, 42,
       44, 20, 14, 31], dtype=int64), 'cur_cost': 111436.0}, {'tour': [0, 16, 21, 20, 15, 18, 12, 19, 17, 13, 14, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 10753.0}, {'tour': [0, 1, 2, 5, 9, 7, 3, 6, 4, 8, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 10742.0}, {'tour': [0, 9, 4, 5, 3, 7, 8, 1, 6, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 10797.0}, {'tour': [23, 25, 40, 28, 41, 27, 33, 39, 26, 34, 32, 7, 0, 43, 37, 8, 4, 30, 42, 9, 6, 1, 18, 13, 17, 21, 45, 11, 48, 19, 50, 44, 16, 15, 47, 20, 52, 53, 22, 29, 24, 3, 35, 10, 36, 31, 2, 38, 46, 54, 49, 14, 12, 51, 5], 'cur_cost': 59169.0}, {'tour': [21, 19, 13, 50, 28, 30, 29, 0, 33, 10, 4, 38, 41, 6, 42, 25, 7, 24, 32, 35, 8, 43, 37, 23, 39, 31, 44, 11, 45, 46, 53, 16, 14, 51, 20, 17, 49, 18, 52, 48, 15, 5, 26, 1, 27, 40, 22, 34, 36, 2, 3, 9, 12, 47, 54], 'cur_cost': 60480.0}, {'tour': [0, 7, 17, 8, 1, 4, 6, 5, 9, 3, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 16475.0}, {'tour': [53, 10, 49, 42, 37, 16, 25, 15, 48, 26, 5, 34, 27, 4, 32, 20, 19, 8, 45, 28, 50, 22, 0, 51, 9, 54, 3, 38, 2, 46, 44, 43, 40, 31, 7, 21, 33, 11, 14, 47, 39, 1, 24, 6, 17, 23, 30, 41, 52, 36, 18, 13, 29, 12, 35], 'cur_cost': 115797.0}, {'tour': [0, 10, 4, 2, 7, 9, 5, 3, 6, 1, 8, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 10778.0}, {'tour': [51, 44, 17, 46, 11, 29, 41, 8, 53, 27, 28, 18, 3, 2, 19, 45, 40, 47, 13, 6, 24, 22, 34, 54, 35, 21, 16, 48, 30, 42, 23, 12, 7, 9, 31, 20, 32, 26, 10, 49, 5, 33, 1, 14, 38, 0, 25, 36, 39, 15, 50, 52, 37, 4, 43], 'cur_cost': 101141.0}]
2025-08-05 09:52:08,665 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:08,665 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 278, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 278, 'cache_hits': 0, 'similarity_calculations': 1366, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:08,666 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([32, 27, 10,  3, 33, 37, 35,  4, 45, 39, 50, 34, 15, 26, 46, 16, 36,
       49, 41, 23, 13, 40, 22,  7, 47,  1, 24, 52,  5, 25,  0, 17, 54, 48,
       21,  6, 18, 30, 53,  9, 29,  8, 43, 12, 11, 38, 51, 28,  2, 19, 42,
       44, 20, 14, 31], dtype=int64), 'cur_cost': 111436.0, 'intermediate_solutions': [{'tour': array([28,  5, 51, 29,  7, 30, 34, 31,  4, 37,  2,  3,  8, 27, 24,  9,  6,
       26, 19, 14, 18, 25, 38, 49, 50, 45, 39, 20, 15, 54, 22, 12, 16, 46,
       17, 35, 11, 47,  1, 52, 33, 48, 41, 36, 40, 42, 10, 21, 43, 32, 44,
        0, 53, 13, 23]), 'cur_cost': 90171.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([29, 28,  5, 51,  7, 30, 34, 31,  4, 37,  2,  3,  8, 27, 24,  9,  6,
       26, 19, 14, 18, 25, 38, 49, 50, 45, 39, 20, 15, 54, 22, 12, 16, 46,
       17, 35, 11, 47,  1, 52, 33, 48, 41, 36, 40, 42, 10, 21, 43, 32, 44,
        0, 53, 13, 23]), 'cur_cost': 89709.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 7, 29, 28,  5, 51, 30, 34, 31,  4, 37,  2,  3,  8, 27, 24,  9,  6,
       26, 19, 14, 18, 25, 38, 49, 50, 45, 39, 20, 15, 54, 22, 12, 16, 46,
       17, 35, 11, 47,  1, 52, 33, 48, 41, 36, 40, 42, 10, 21, 43, 32, 44,
        0, 53, 13, 23]), 'cur_cost': 90143.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([51, 29, 28,  5,  7, 30, 34, 31,  4, 37,  2,  3,  8, 27, 24,  9,  6,
       26, 19, 14, 18, 25, 38, 49, 50, 45, 39, 20, 15, 54, 22, 12, 16, 46,
       17, 35, 11, 47,  1, 52, 33, 48, 41, 36, 40, 42, 10, 21, 43, 32, 44,
        0, 53, 13, 23]), 'cur_cost': 87803.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([51,  7, 29, 28,  5, 30, 34, 31,  4, 37,  2,  3,  8, 27, 24,  9,  6,
       26, 19, 14, 18, 25, 38, 49, 50, 45, 39, 20, 15, 54, 22, 12, 16, 46,
       17, 35, 11, 47,  1, 52, 33, 48, 41, 36, 40, 42, 10, 21, 43, 32, 44,
        0, 53, 13, 23]), 'cur_cost': 90192.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:08,667 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 111436.00)
2025-08-05 09:52:08,667 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:08,667 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:08,667 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:08,671 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 55
2025-08-05 09:52:08,671 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,672 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,672 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,672 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,672 - ExplorationExpert - INFO - 探索路径生成完成，成本: 55302.0, 路径长度: 55, 收集中间解: 3
2025-08-05 09:52:08,673 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [51, 50, 15, 18, 49, 54, 26, 32, 4, 9, 25, 39, 36, 23, 37, 8, 35, 38, 24, 28, 6, 33, 34, 3, 10, 31, 43, 22, 2, 0, 12, 45, 19, 48, 47, 44, 52, 14, 13, 17, 11, 27, 40, 41, 30, 5, 29, 53, 46, 20, 16, 21, 1, 7, 42], 'cur_cost': 55302.0, 'intermediate_solutions': [{'tour': [0, 16, 21, 20, 15, 18, 12, 19, 17, 13, 14, 22, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 11, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 19101.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 16, 21, 20, 15, 18, 12, 19, 17, 13, 8, 10, 41, 40, 34, 39, 43, 33, 42, 37, 38, 36, 35, 29, 27, 28, 32, 22, 23, 25, 31, 26, 24, 30, 49, 46, 53, 44, 54, 47, 48, 50, 52, 45, 51, 11, 14, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 16549.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 16, 21, 20, 15, 18, 12, 19, 17, 13, 14, 11, 51, 45, 52, 53, 50, 48, 47, 54, 44, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 10784.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:08,673 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 55302.00)
2025-08-05 09:52:08,673 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:08,673 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:08,673 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:08,678 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 55
2025-08-05 09:52:08,678 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,678 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,678 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,679 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,679 - ExplorationExpert - INFO - 探索路径生成完成，成本: 48537.0, 路径长度: 55, 收集中间解: 3
2025-08-05 09:52:08,679 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [13, 32, 25, 0, 27, 43, 10, 5, 39, 22, 31, 6, 3, 30, 9, 2, 29, 26, 41, 49, 50, 18, 20, 48, 21, 15, 52, 19, 11, 17, 16, 46, 53, 51, 47, 44, 54, 24, 36, 38, 35, 34, 33, 37, 28, 1, 23, 8, 42, 40, 7, 4, 12, 14, 45], 'cur_cost': 48537.0, 'intermediate_solutions': [{'tour': [0, 1, 2, 5, 9, 7, 3, 6, 4, 8, 10, 31, 25, 23, 22, 32, 41, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 28, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 13451.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 2, 5, 9, 7, 3, 6, 4, 8, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 12, 18, 20, 15, 21, 19, 11], 'cur_cost': 10811.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 2, 5, 9, 7, 3, 6, 4, 8, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 29, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 14823.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:08,679 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 48537.00)
2025-08-05 09:52:08,680 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:52:08,680 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:52:08,680 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:08,681 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 09:52:08,682 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,682 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,682 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,682 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,682 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16496.0, 路径长度: 55, 收集中间解: 3
2025-08-05 09:52:08,682 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 5, 15, 1, 4, 6, 9, 7, 3, 2, 8, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 16496.0, 'intermediate_solutions': [{'tour': [0, 9, 4, 8, 3, 7, 5, 1, 6, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 10840.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 4, 5, 3, 7, 8, 1, 6, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 29, 26, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 10898.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 4, 5, 46, 3, 7, 8, 1, 6, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 16822.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:08,683 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 16496.00)
2025-08-05 09:52:08,683 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:08,683 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:08,683 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:08,684 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 55
2025-08-05 09:52:08,685 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,685 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,685 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,686 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,686 - ExplorationExpert - INFO - 探索路径生成完成，成本: 90912.0, 路径长度: 55, 收集中间解: 3
2025-08-05 09:52:08,686 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [13, 50, 25, 0, 49, 43, 10, 32, 4, 6, 36, 35, 30, 42, 8, 33, 17, 12, 44, 15, 20, 14, 27, 40, 41, 3, 37, 48, 31, 34, 52, 16, 51, 39, 2, 53, 1, 38, 5, 18, 7, 22, 26, 24, 9, 28, 11, 46, 23, 21, 54, 29, 45, 47, 19], 'cur_cost': 90912.0, 'intermediate_solutions': [{'tour': [23, 25, 40, 28, 41, 27, 33, 39, 26, 34, 32, 7, 0, 43, 49, 8, 4, 30, 42, 9, 6, 1, 18, 13, 17, 21, 45, 11, 48, 19, 50, 44, 16, 15, 47, 20, 52, 53, 22, 29, 24, 3, 35, 10, 36, 31, 2, 38, 46, 54, 37, 14, 12, 51, 5], 'cur_cost': 67811.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [23, 25, 40, 28, 41, 27, 33, 39, 26, 34, 32, 7, 0, 43, 37, 8, 4, 30, 42, 9, 6, 20, 47, 15, 16, 44, 50, 19, 48, 11, 45, 21, 17, 13, 18, 1, 52, 53, 22, 29, 24, 3, 35, 10, 36, 31, 2, 38, 46, 54, 49, 14, 12, 51, 5], 'cur_cost': 63820.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [23, 25, 43, 40, 28, 41, 27, 33, 39, 26, 34, 32, 7, 0, 37, 8, 4, 30, 42, 9, 6, 1, 18, 13, 17, 21, 45, 11, 48, 19, 50, 44, 16, 15, 47, 20, 52, 53, 22, 29, 24, 3, 35, 10, 36, 31, 2, 38, 46, 54, 49, 14, 12, 51, 5], 'cur_cost': 59182.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:08,686 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 90912.00)
2025-08-05 09:52:08,687 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:08,687 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:08,687 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:08,688 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 55
2025-08-05 09:52:08,688 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,688 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,688 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,689 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,689 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101275.0, 路径长度: 55, 收集中间解: 3
2025-08-05 09:52:08,689 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [21, 18, 26, 39, 9, 25, 6, 36, 23, 8, 29, 38, 32, 49, 3, 43, 0, 12, 31, 2, 53, 44, 14, 48, 11, 34, 24, 41, 28, 50, 16, 42, 7, 35, 15, 45, 20, 52, 37, 19, 46, 33, 1, 30, 47, 51, 54, 13, 40, 4, 17, 27, 10, 22, 5], 'cur_cost': 101275.0, 'intermediate_solutions': [{'tour': [21, 19, 13, 50, 28, 30, 29, 0, 33, 10, 4, 38, 41, 6, 42, 25, 7, 24, 32, 35, 8, 43, 51, 23, 39, 31, 44, 11, 45, 46, 53, 16, 14, 37, 20, 17, 49, 18, 52, 48, 15, 5, 26, 1, 27, 40, 22, 34, 36, 2, 3, 9, 12, 47, 54], 'cur_cost': 69014.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [21, 19, 13, 50, 28, 30, 29, 0, 33, 10, 4, 38, 41, 6, 42, 25, 7, 24, 32, 35, 8, 43, 37, 23, 39, 31, 44, 11, 45, 46, 53, 16, 14, 51, 18, 49, 17, 20, 52, 48, 15, 5, 26, 1, 27, 40, 22, 34, 36, 2, 3, 9, 12, 47, 54], 'cur_cost': 60480.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [21, 19, 13, 50, 28, 30, 29, 0, 33, 10, 4, 38, 41, 6, 42, 25, 7, 24, 32, 35, 8, 43, 37, 23, 39, 31, 44, 11, 45, 46, 53, 16, 14, 51, 20, 17, 49, 18, 52, 48, 15, 5, 26, 3, 1, 27, 40, 22, 34, 36, 2, 9, 12, 47, 54], 'cur_cost': 60572.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:08,689 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 101275.00)
2025-08-05 09:52:08,689 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:08,689 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:08,689 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:08,691 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 55
2025-08-05 09:52:08,691 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,691 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,691 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,691 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,691 - ExplorationExpert - INFO - 探索路径生成完成，成本: 96834.0, 路径长度: 55, 收集中间解: 3
2025-08-05 09:52:08,692 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [4, 26, 7, 25, 6, 23, 35, 44, 2, 33, 40, 43, 19, 0, 12, 51, 53, 14, 36, 24, 41, 30, 42, 20, 50, 52, 31, 48, 34, 22, 27, 49, 29, 13, 37, 10, 8, 54, 16, 21, 32, 47, 1, 15, 18, 5, 17, 45, 11, 38, 39, 3, 46, 28, 9], 'cur_cost': 96834.0, 'intermediate_solutions': [{'tour': [0, 7, 17, 8, 1, 4, 6, 5, 9, 3, 2, 10, 31, 25, 23, 22, 32, 28, 43, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 24, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 21156.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 17, 8, 1, 46, 49, 41, 40, 34, 39, 43, 33, 42, 37, 38, 36, 35, 27, 29, 26, 30, 24, 28, 32, 22, 23, 25, 31, 10, 2, 3, 9, 5, 6, 4, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 22384.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 17, 8, 1, 4, 6, 5, 9, 3, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 14, 16, 15, 20, 18, 12, 21, 19, 11, 39], 'cur_cost': 19359.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:08,692 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 96834.00)
2025-08-05 09:52:08,692 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 09:52:08,692 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:08,692 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:08,692 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 91530.0
2025-08-05 09:52:08,699 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 09:52:08,699 - ExploitationExpert - INFO - res_population_costs: [10443, 10443, 10445.0, 10450, 10451.0, 10443]
2025-08-05 09:52:08,699 - ExploitationExpert - INFO - res_populations: [array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  9,  7,  2,  3,
        5,  6,  4,  1], dtype=int64), array([ 0, 21, 19, 18, 12, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       14, 11, 13, 17, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 14, 11, 13, 17, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 30, 24, 26, 31, 25, 23, 28, 32, 27, 22, 29,
       35, 36, 33, 42, 37, 38, 41, 40, 34, 39, 43, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 43, 39, 34, 40, 41, 38,
       36, 37, 42, 33, 35, 29, 22, 27, 32, 28, 23, 25, 31, 26, 24, 30, 49,
       46, 44, 53, 54, 50, 48, 52, 47, 51, 45, 13, 17, 14, 11, 16, 15, 20,
       18, 12, 21, 19], dtype=int64)]
2025-08-05 09:52:08,702 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:08,702 - ExploitationExpert - INFO - populations: [{'tour': array([32, 27, 10,  3, 33, 37, 35,  4, 45, 39, 50, 34, 15, 26, 46, 16, 36,
       49, 41, 23, 13, 40, 22,  7, 47,  1, 24, 52,  5, 25,  0, 17, 54, 48,
       21,  6, 18, 30, 53,  9, 29,  8, 43, 12, 11, 38, 51, 28,  2, 19, 42,
       44, 20, 14, 31], dtype=int64), 'cur_cost': 111436.0}, {'tour': [51, 50, 15, 18, 49, 54, 26, 32, 4, 9, 25, 39, 36, 23, 37, 8, 35, 38, 24, 28, 6, 33, 34, 3, 10, 31, 43, 22, 2, 0, 12, 45, 19, 48, 47, 44, 52, 14, 13, 17, 11, 27, 40, 41, 30, 5, 29, 53, 46, 20, 16, 21, 1, 7, 42], 'cur_cost': 55302.0}, {'tour': [13, 32, 25, 0, 27, 43, 10, 5, 39, 22, 31, 6, 3, 30, 9, 2, 29, 26, 41, 49, 50, 18, 20, 48, 21, 15, 52, 19, 11, 17, 16, 46, 53, 51, 47, 44, 54, 24, 36, 38, 35, 34, 33, 37, 28, 1, 23, 8, 42, 40, 7, 4, 12, 14, 45], 'cur_cost': 48537.0}, {'tour': [0, 5, 15, 1, 4, 6, 9, 7, 3, 2, 8, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 16496.0}, {'tour': [13, 50, 25, 0, 49, 43, 10, 32, 4, 6, 36, 35, 30, 42, 8, 33, 17, 12, 44, 15, 20, 14, 27, 40, 41, 3, 37, 48, 31, 34, 52, 16, 51, 39, 2, 53, 1, 38, 5, 18, 7, 22, 26, 24, 9, 28, 11, 46, 23, 21, 54, 29, 45, 47, 19], 'cur_cost': 90912.0}, {'tour': [21, 18, 26, 39, 9, 25, 6, 36, 23, 8, 29, 38, 32, 49, 3, 43, 0, 12, 31, 2, 53, 44, 14, 48, 11, 34, 24, 41, 28, 50, 16, 42, 7, 35, 15, 45, 20, 52, 37, 19, 46, 33, 1, 30, 47, 51, 54, 13, 40, 4, 17, 27, 10, 22, 5], 'cur_cost': 101275.0}, {'tour': [4, 26, 7, 25, 6, 23, 35, 44, 2, 33, 40, 43, 19, 0, 12, 51, 53, 14, 36, 24, 41, 30, 42, 20, 50, 52, 31, 48, 34, 22, 27, 49, 29, 13, 37, 10, 8, 54, 16, 21, 32, 47, 1, 15, 18, 5, 17, 45, 11, 38, 39, 3, 46, 28, 9], 'cur_cost': 96834.0}, {'tour': array([49, 27, 40, 14, 15,  0, 20,  8, 22, 37, 17, 13, 44, 48,  9, 51,  3,
        6, 31, 33, 32, 43, 38, 28, 12, 54, 18, 21, 34,  1, 45, 53, 47, 50,
       16, 30,  2,  4, 25, 19, 41,  5,  7, 52, 36, 29, 26, 11, 39, 24, 46,
       10, 23, 42, 35], dtype=int64), 'cur_cost': 91530.0}, {'tour': [0, 10, 4, 2, 7, 9, 5, 3, 6, 1, 8, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 10778.0}, {'tour': [51, 44, 17, 46, 11, 29, 41, 8, 53, 27, 28, 18, 3, 2, 19, 45, 40, 47, 13, 6, 24, 22, 34, 54, 35, 21, 16, 48, 30, 42, 23, 12, 7, 9, 31, 20, 32, 26, 10, 49, 5, 33, 1, 14, 38, 0, 25, 36, 39, 15, 50, 52, 37, 4, 43], 'cur_cost': 101141.0}]
2025-08-05 09:52:08,703 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:08,703 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 279, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 279, 'cache_hits': 0, 'similarity_calculations': 1371, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:08,704 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([49, 27, 40, 14, 15,  0, 20,  8, 22, 37, 17, 13, 44, 48,  9, 51,  3,
        6, 31, 33, 32, 43, 38, 28, 12, 54, 18, 21, 34,  1, 45, 53, 47, 50,
       16, 30,  2,  4, 25, 19, 41,  5,  7, 52, 36, 29, 26, 11, 39, 24, 46,
       10, 23, 42, 35], dtype=int64), 'cur_cost': 91530.0, 'intermediate_solutions': [{'tour': array([49, 10, 53, 42, 37, 16, 25, 15, 48, 26,  5, 34, 27,  4, 32, 20, 19,
        8, 45, 28, 50, 22,  0, 51,  9, 54,  3, 38,  2, 46, 44, 43, 40, 31,
        7, 21, 33, 11, 14, 47, 39,  1, 24,  6, 17, 23, 30, 41, 52, 36, 18,
       13, 29, 12, 35]), 'cur_cost': 115798.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([42, 49, 10, 53, 37, 16, 25, 15, 48, 26,  5, 34, 27,  4, 32, 20, 19,
        8, 45, 28, 50, 22,  0, 51,  9, 54,  3, 38,  2, 46, 44, 43, 40, 31,
        7, 21, 33, 11, 14, 47, 39,  1, 24,  6, 17, 23, 30, 41, 52, 36, 18,
       13, 29, 12, 35]), 'cur_cost': 115867.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([37, 42, 49, 10, 53, 16, 25, 15, 48, 26,  5, 34, 27,  4, 32, 20, 19,
        8, 45, 28, 50, 22,  0, 51,  9, 54,  3, 38,  2, 46, 44, 43, 40, 31,
        7, 21, 33, 11, 14, 47, 39,  1, 24,  6, 17, 23, 30, 41, 52, 36, 18,
       13, 29, 12, 35]), 'cur_cost': 110694.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([53, 42, 49, 10, 37, 16, 25, 15, 48, 26,  5, 34, 27,  4, 32, 20, 19,
        8, 45, 28, 50, 22,  0, 51,  9, 54,  3, 38,  2, 46, 44, 43, 40, 31,
        7, 21, 33, 11, 14, 47, 39,  1, 24,  6, 17, 23, 30, 41, 52, 36, 18,
       13, 29, 12, 35]), 'cur_cost': 117803.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([53, 37, 42, 49, 10, 16, 25, 15, 48, 26,  5, 34, 27,  4, 32, 20, 19,
        8, 45, 28, 50, 22,  0, 51,  9, 54,  3, 38,  2, 46, 44, 43, 40, 31,
        7, 21, 33, 11, 14, 47, 39,  1, 24,  6, 17, 23, 30, 41, 52, 36, 18,
       13, 29, 12, 35]), 'cur_cost': 115002.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:08,704 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 91530.00)
2025-08-05 09:52:08,704 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:52:08,704 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:52:08,704 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:08,709 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 55
2025-08-05 09:52:08,709 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,709 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,710 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,710 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,710 - ExplorationExpert - INFO - 探索路径生成完成，成本: 63611.0, 路径长度: 55, 收集中间解: 3
2025-08-05 09:52:08,710 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [11, 13, 54, 24, 37, 40, 8, 23, 41, 39, 29, 33, 6, 5, 28, 35, 22, 9, 30, 36, 43, 1, 2, 34, 3, 42, 31, 46, 44, 50, 49, 45, 14, 51, 53, 18, 20, 47, 16, 12, 19, 26, 38, 4, 32, 25, 0, 10, 21, 52, 17, 48, 15, 27, 7], 'cur_cost': 63611.0, 'intermediate_solutions': [{'tour': [0, 10, 4, 2, 7, 9, 5, 3, 6, 1, 8, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 54, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 35, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 20011.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 4, 2, 7, 9, 5, 3, 6, 1, 8, 26, 31, 25, 23, 22, 32, 28, 49, 41, 40, 34, 39, 43, 33, 42, 37, 38, 36, 35, 29, 27, 30, 24, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 14691.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 4, 7, 9, 5, 3, 6, 1, 8, 26, 31, 25, 23, 2, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 13683.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:08,710 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 63611.00)
2025-08-05 09:52:08,710 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 09:52:08,711 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:08,711 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:08,711 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 110078.0
2025-08-05 09:52:08,719 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 09:52:08,720 - ExploitationExpert - INFO - res_population_costs: [10443, 10443, 10445.0, 10450, 10451.0, 10443]
2025-08-05 09:52:08,720 - ExploitationExpert - INFO - res_populations: [array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  9,  7,  2,  3,
        5,  6,  4,  1], dtype=int64), array([ 0, 21, 19, 18, 12, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       14, 11, 13, 17, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 14, 11, 13, 17, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 30, 24, 26, 31, 25, 23, 28, 32, 27, 22, 29,
       35, 36, 33, 42, 37, 38, 41, 40, 34, 39, 43, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 43, 39, 34, 40, 41, 38,
       36, 37, 42, 33, 35, 29, 22, 27, 32, 28, 23, 25, 31, 26, 24, 30, 49,
       46, 44, 53, 54, 50, 48, 52, 47, 51, 45, 13, 17, 14, 11, 16, 15, 20,
       18, 12, 21, 19], dtype=int64)]
2025-08-05 09:52:08,722 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:08,722 - ExploitationExpert - INFO - populations: [{'tour': array([32, 27, 10,  3, 33, 37, 35,  4, 45, 39, 50, 34, 15, 26, 46, 16, 36,
       49, 41, 23, 13, 40, 22,  7, 47,  1, 24, 52,  5, 25,  0, 17, 54, 48,
       21,  6, 18, 30, 53,  9, 29,  8, 43, 12, 11, 38, 51, 28,  2, 19, 42,
       44, 20, 14, 31], dtype=int64), 'cur_cost': 111436.0}, {'tour': [51, 50, 15, 18, 49, 54, 26, 32, 4, 9, 25, 39, 36, 23, 37, 8, 35, 38, 24, 28, 6, 33, 34, 3, 10, 31, 43, 22, 2, 0, 12, 45, 19, 48, 47, 44, 52, 14, 13, 17, 11, 27, 40, 41, 30, 5, 29, 53, 46, 20, 16, 21, 1, 7, 42], 'cur_cost': 55302.0}, {'tour': [13, 32, 25, 0, 27, 43, 10, 5, 39, 22, 31, 6, 3, 30, 9, 2, 29, 26, 41, 49, 50, 18, 20, 48, 21, 15, 52, 19, 11, 17, 16, 46, 53, 51, 47, 44, 54, 24, 36, 38, 35, 34, 33, 37, 28, 1, 23, 8, 42, 40, 7, 4, 12, 14, 45], 'cur_cost': 48537.0}, {'tour': [0, 5, 15, 1, 4, 6, 9, 7, 3, 2, 8, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 16496.0}, {'tour': [13, 50, 25, 0, 49, 43, 10, 32, 4, 6, 36, 35, 30, 42, 8, 33, 17, 12, 44, 15, 20, 14, 27, 40, 41, 3, 37, 48, 31, 34, 52, 16, 51, 39, 2, 53, 1, 38, 5, 18, 7, 22, 26, 24, 9, 28, 11, 46, 23, 21, 54, 29, 45, 47, 19], 'cur_cost': 90912.0}, {'tour': [21, 18, 26, 39, 9, 25, 6, 36, 23, 8, 29, 38, 32, 49, 3, 43, 0, 12, 31, 2, 53, 44, 14, 48, 11, 34, 24, 41, 28, 50, 16, 42, 7, 35, 15, 45, 20, 52, 37, 19, 46, 33, 1, 30, 47, 51, 54, 13, 40, 4, 17, 27, 10, 22, 5], 'cur_cost': 101275.0}, {'tour': [4, 26, 7, 25, 6, 23, 35, 44, 2, 33, 40, 43, 19, 0, 12, 51, 53, 14, 36, 24, 41, 30, 42, 20, 50, 52, 31, 48, 34, 22, 27, 49, 29, 13, 37, 10, 8, 54, 16, 21, 32, 47, 1, 15, 18, 5, 17, 45, 11, 38, 39, 3, 46, 28, 9], 'cur_cost': 96834.0}, {'tour': array([49, 27, 40, 14, 15,  0, 20,  8, 22, 37, 17, 13, 44, 48,  9, 51,  3,
        6, 31, 33, 32, 43, 38, 28, 12, 54, 18, 21, 34,  1, 45, 53, 47, 50,
       16, 30,  2,  4, 25, 19, 41,  5,  7, 52, 36, 29, 26, 11, 39, 24, 46,
       10, 23, 42, 35], dtype=int64), 'cur_cost': 91530.0}, {'tour': [11, 13, 54, 24, 37, 40, 8, 23, 41, 39, 29, 33, 6, 5, 28, 35, 22, 9, 30, 36, 43, 1, 2, 34, 3, 42, 31, 46, 44, 50, 49, 45, 14, 51, 53, 18, 20, 47, 16, 12, 19, 26, 38, 4, 32, 25, 0, 10, 21, 52, 17, 48, 15, 27, 7], 'cur_cost': 63611.0}, {'tour': array([ 0,  2, 29, 36, 13, 11, 34, 37,  3, 46, 31, 25, 53, 40, 51, 21, 26,
       15, 27, 48, 32, 49,  5, 16, 19,  8, 30,  1, 24, 43,  6, 47, 22, 38,
       14, 28, 33, 39, 23, 10, 20, 41, 12,  4, 35,  7, 54, 52, 45, 18,  9,
       44, 42, 17, 50], dtype=int64), 'cur_cost': 110078.0}]
2025-08-05 09:52:08,723 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:08,724 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 280, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 280, 'cache_hits': 0, 'similarity_calculations': 1377, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:08,724 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([ 0,  2, 29, 36, 13, 11, 34, 37,  3, 46, 31, 25, 53, 40, 51, 21, 26,
       15, 27, 48, 32, 49,  5, 16, 19,  8, 30,  1, 24, 43,  6, 47, 22, 38,
       14, 28, 33, 39, 23, 10, 20, 41, 12,  4, 35,  7, 54, 52, 45, 18,  9,
       44, 42, 17, 50], dtype=int64), 'cur_cost': 110078.0, 'intermediate_solutions': [{'tour': array([17, 44, 51, 46, 11, 29, 41,  8, 53, 27, 28, 18,  3,  2, 19, 45, 40,
       47, 13,  6, 24, 22, 34, 54, 35, 21, 16, 48, 30, 42, 23, 12,  7,  9,
       31, 20, 32, 26, 10, 49,  5, 33,  1, 14, 38,  0, 25, 36, 39, 15, 50,
       52, 37,  4, 43]), 'cur_cost': 100684.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([46, 17, 44, 51, 11, 29, 41,  8, 53, 27, 28, 18,  3,  2, 19, 45, 40,
       47, 13,  6, 24, 22, 34, 54, 35, 21, 16, 48, 30, 42, 23, 12,  7,  9,
       31, 20, 32, 26, 10, 49,  5, 33,  1, 14, 38,  0, 25, 36, 39, 15, 50,
       52, 37,  4, 43]), 'cur_cost': 101007.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([11, 46, 17, 44, 51, 29, 41,  8, 53, 27, 28, 18,  3,  2, 19, 45, 40,
       47, 13,  6, 24, 22, 34, 54, 35, 21, 16, 48, 30, 42, 23, 12,  7,  9,
       31, 20, 32, 26, 10, 49,  5, 33,  1, 14, 38,  0, 25, 36, 39, 15, 50,
       52, 37,  4, 43]), 'cur_cost': 101363.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([51, 46, 17, 44, 11, 29, 41,  8, 53, 27, 28, 18,  3,  2, 19, 45, 40,
       47, 13,  6, 24, 22, 34, 54, 35, 21, 16, 48, 30, 42, 23, 12,  7,  9,
       31, 20, 32, 26, 10, 49,  5, 33,  1, 14, 38,  0, 25, 36, 39, 15, 50,
       52, 37,  4, 43]), 'cur_cost': 101137.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([51, 11, 46, 17, 44, 29, 41,  8, 53, 27, 28, 18,  3,  2, 19, 45, 40,
       47, 13,  6, 24, 22, 34, 54, 35, 21, 16, 48, 30, 42, 23, 12,  7,  9,
       31, 20, 32, 26, 10, 49,  5, 33,  1, 14, 38,  0, 25, 36, 39, 15, 50,
       52, 37,  4, 43]), 'cur_cost': 101703.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:08,725 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 110078.00)
2025-08-05 09:52:08,725 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:08,725 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:08,727 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([32, 27, 10,  3, 33, 37, 35,  4, 45, 39, 50, 34, 15, 26, 46, 16, 36,
       49, 41, 23, 13, 40, 22,  7, 47,  1, 24, 52,  5, 25,  0, 17, 54, 48,
       21,  6, 18, 30, 53,  9, 29,  8, 43, 12, 11, 38, 51, 28,  2, 19, 42,
       44, 20, 14, 31], dtype=int64), 'cur_cost': 111436.0, 'intermediate_solutions': [{'tour': array([28,  5, 51, 29,  7, 30, 34, 31,  4, 37,  2,  3,  8, 27, 24,  9,  6,
       26, 19, 14, 18, 25, 38, 49, 50, 45, 39, 20, 15, 54, 22, 12, 16, 46,
       17, 35, 11, 47,  1, 52, 33, 48, 41, 36, 40, 42, 10, 21, 43, 32, 44,
        0, 53, 13, 23]), 'cur_cost': 90171.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([29, 28,  5, 51,  7, 30, 34, 31,  4, 37,  2,  3,  8, 27, 24,  9,  6,
       26, 19, 14, 18, 25, 38, 49, 50, 45, 39, 20, 15, 54, 22, 12, 16, 46,
       17, 35, 11, 47,  1, 52, 33, 48, 41, 36, 40, 42, 10, 21, 43, 32, 44,
        0, 53, 13, 23]), 'cur_cost': 89709.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 7, 29, 28,  5, 51, 30, 34, 31,  4, 37,  2,  3,  8, 27, 24,  9,  6,
       26, 19, 14, 18, 25, 38, 49, 50, 45, 39, 20, 15, 54, 22, 12, 16, 46,
       17, 35, 11, 47,  1, 52, 33, 48, 41, 36, 40, 42, 10, 21, 43, 32, 44,
        0, 53, 13, 23]), 'cur_cost': 90143.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([51, 29, 28,  5,  7, 30, 34, 31,  4, 37,  2,  3,  8, 27, 24,  9,  6,
       26, 19, 14, 18, 25, 38, 49, 50, 45, 39, 20, 15, 54, 22, 12, 16, 46,
       17, 35, 11, 47,  1, 52, 33, 48, 41, 36, 40, 42, 10, 21, 43, 32, 44,
        0, 53, 13, 23]), 'cur_cost': 87803.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([51,  7, 29, 28,  5, 30, 34, 31,  4, 37,  2,  3,  8, 27, 24,  9,  6,
       26, 19, 14, 18, 25, 38, 49, 50, 45, 39, 20, 15, 54, 22, 12, 16, 46,
       17, 35, 11, 47,  1, 52, 33, 48, 41, 36, 40, 42, 10, 21, 43, 32, 44,
        0, 53, 13, 23]), 'cur_cost': 90192.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [51, 50, 15, 18, 49, 54, 26, 32, 4, 9, 25, 39, 36, 23, 37, 8, 35, 38, 24, 28, 6, 33, 34, 3, 10, 31, 43, 22, 2, 0, 12, 45, 19, 48, 47, 44, 52, 14, 13, 17, 11, 27, 40, 41, 30, 5, 29, 53, 46, 20, 16, 21, 1, 7, 42], 'cur_cost': 55302.0, 'intermediate_solutions': [{'tour': [0, 16, 21, 20, 15, 18, 12, 19, 17, 13, 14, 22, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 11, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 19101.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 16, 21, 20, 15, 18, 12, 19, 17, 13, 8, 10, 41, 40, 34, 39, 43, 33, 42, 37, 38, 36, 35, 29, 27, 28, 32, 22, 23, 25, 31, 26, 24, 30, 49, 46, 53, 44, 54, 47, 48, 50, 52, 45, 51, 11, 14, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 16549.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 16, 21, 20, 15, 18, 12, 19, 17, 13, 14, 11, 51, 45, 52, 53, 50, 48, 47, 54, 44, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 10784.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [13, 32, 25, 0, 27, 43, 10, 5, 39, 22, 31, 6, 3, 30, 9, 2, 29, 26, 41, 49, 50, 18, 20, 48, 21, 15, 52, 19, 11, 17, 16, 46, 53, 51, 47, 44, 54, 24, 36, 38, 35, 34, 33, 37, 28, 1, 23, 8, 42, 40, 7, 4, 12, 14, 45], 'cur_cost': 48537.0, 'intermediate_solutions': [{'tour': [0, 1, 2, 5, 9, 7, 3, 6, 4, 8, 10, 31, 25, 23, 22, 32, 41, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 28, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 13451.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 2, 5, 9, 7, 3, 6, 4, 8, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 12, 18, 20, 15, 21, 19, 11], 'cur_cost': 10811.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 2, 5, 9, 7, 3, 6, 4, 8, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 29, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 14823.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 15, 1, 4, 6, 9, 7, 3, 2, 8, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 16496.0, 'intermediate_solutions': [{'tour': [0, 9, 4, 8, 3, 7, 5, 1, 6, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 10840.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 4, 5, 3, 7, 8, 1, 6, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 29, 26, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 10898.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 4, 5, 46, 3, 7, 8, 1, 6, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 16822.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [13, 50, 25, 0, 49, 43, 10, 32, 4, 6, 36, 35, 30, 42, 8, 33, 17, 12, 44, 15, 20, 14, 27, 40, 41, 3, 37, 48, 31, 34, 52, 16, 51, 39, 2, 53, 1, 38, 5, 18, 7, 22, 26, 24, 9, 28, 11, 46, 23, 21, 54, 29, 45, 47, 19], 'cur_cost': 90912.0, 'intermediate_solutions': [{'tour': [23, 25, 40, 28, 41, 27, 33, 39, 26, 34, 32, 7, 0, 43, 49, 8, 4, 30, 42, 9, 6, 1, 18, 13, 17, 21, 45, 11, 48, 19, 50, 44, 16, 15, 47, 20, 52, 53, 22, 29, 24, 3, 35, 10, 36, 31, 2, 38, 46, 54, 37, 14, 12, 51, 5], 'cur_cost': 67811.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [23, 25, 40, 28, 41, 27, 33, 39, 26, 34, 32, 7, 0, 43, 37, 8, 4, 30, 42, 9, 6, 20, 47, 15, 16, 44, 50, 19, 48, 11, 45, 21, 17, 13, 18, 1, 52, 53, 22, 29, 24, 3, 35, 10, 36, 31, 2, 38, 46, 54, 49, 14, 12, 51, 5], 'cur_cost': 63820.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [23, 25, 43, 40, 28, 41, 27, 33, 39, 26, 34, 32, 7, 0, 37, 8, 4, 30, 42, 9, 6, 1, 18, 13, 17, 21, 45, 11, 48, 19, 50, 44, 16, 15, 47, 20, 52, 53, 22, 29, 24, 3, 35, 10, 36, 31, 2, 38, 46, 54, 49, 14, 12, 51, 5], 'cur_cost': 59182.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [21, 18, 26, 39, 9, 25, 6, 36, 23, 8, 29, 38, 32, 49, 3, 43, 0, 12, 31, 2, 53, 44, 14, 48, 11, 34, 24, 41, 28, 50, 16, 42, 7, 35, 15, 45, 20, 52, 37, 19, 46, 33, 1, 30, 47, 51, 54, 13, 40, 4, 17, 27, 10, 22, 5], 'cur_cost': 101275.0, 'intermediate_solutions': [{'tour': [21, 19, 13, 50, 28, 30, 29, 0, 33, 10, 4, 38, 41, 6, 42, 25, 7, 24, 32, 35, 8, 43, 51, 23, 39, 31, 44, 11, 45, 46, 53, 16, 14, 37, 20, 17, 49, 18, 52, 48, 15, 5, 26, 1, 27, 40, 22, 34, 36, 2, 3, 9, 12, 47, 54], 'cur_cost': 69014.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [21, 19, 13, 50, 28, 30, 29, 0, 33, 10, 4, 38, 41, 6, 42, 25, 7, 24, 32, 35, 8, 43, 37, 23, 39, 31, 44, 11, 45, 46, 53, 16, 14, 51, 18, 49, 17, 20, 52, 48, 15, 5, 26, 1, 27, 40, 22, 34, 36, 2, 3, 9, 12, 47, 54], 'cur_cost': 60480.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [21, 19, 13, 50, 28, 30, 29, 0, 33, 10, 4, 38, 41, 6, 42, 25, 7, 24, 32, 35, 8, 43, 37, 23, 39, 31, 44, 11, 45, 46, 53, 16, 14, 51, 20, 17, 49, 18, 52, 48, 15, 5, 26, 3, 1, 27, 40, 22, 34, 36, 2, 9, 12, 47, 54], 'cur_cost': 60572.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [4, 26, 7, 25, 6, 23, 35, 44, 2, 33, 40, 43, 19, 0, 12, 51, 53, 14, 36, 24, 41, 30, 42, 20, 50, 52, 31, 48, 34, 22, 27, 49, 29, 13, 37, 10, 8, 54, 16, 21, 32, 47, 1, 15, 18, 5, 17, 45, 11, 38, 39, 3, 46, 28, 9], 'cur_cost': 96834.0, 'intermediate_solutions': [{'tour': [0, 7, 17, 8, 1, 4, 6, 5, 9, 3, 2, 10, 31, 25, 23, 22, 32, 28, 43, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 24, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 21156.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 17, 8, 1, 46, 49, 41, 40, 34, 39, 43, 33, 42, 37, 38, 36, 35, 27, 29, 26, 30, 24, 28, 32, 22, 23, 25, 31, 10, 2, 3, 9, 5, 6, 4, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 22384.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 17, 8, 1, 4, 6, 5, 9, 3, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 14, 16, 15, 20, 18, 12, 21, 19, 11, 39], 'cur_cost': 19359.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([49, 27, 40, 14, 15,  0, 20,  8, 22, 37, 17, 13, 44, 48,  9, 51,  3,
        6, 31, 33, 32, 43, 38, 28, 12, 54, 18, 21, 34,  1, 45, 53, 47, 50,
       16, 30,  2,  4, 25, 19, 41,  5,  7, 52, 36, 29, 26, 11, 39, 24, 46,
       10, 23, 42, 35], dtype=int64), 'cur_cost': 91530.0, 'intermediate_solutions': [{'tour': array([49, 10, 53, 42, 37, 16, 25, 15, 48, 26,  5, 34, 27,  4, 32, 20, 19,
        8, 45, 28, 50, 22,  0, 51,  9, 54,  3, 38,  2, 46, 44, 43, 40, 31,
        7, 21, 33, 11, 14, 47, 39,  1, 24,  6, 17, 23, 30, 41, 52, 36, 18,
       13, 29, 12, 35]), 'cur_cost': 115798.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([42, 49, 10, 53, 37, 16, 25, 15, 48, 26,  5, 34, 27,  4, 32, 20, 19,
        8, 45, 28, 50, 22,  0, 51,  9, 54,  3, 38,  2, 46, 44, 43, 40, 31,
        7, 21, 33, 11, 14, 47, 39,  1, 24,  6, 17, 23, 30, 41, 52, 36, 18,
       13, 29, 12, 35]), 'cur_cost': 115867.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([37, 42, 49, 10, 53, 16, 25, 15, 48, 26,  5, 34, 27,  4, 32, 20, 19,
        8, 45, 28, 50, 22,  0, 51,  9, 54,  3, 38,  2, 46, 44, 43, 40, 31,
        7, 21, 33, 11, 14, 47, 39,  1, 24,  6, 17, 23, 30, 41, 52, 36, 18,
       13, 29, 12, 35]), 'cur_cost': 110694.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([53, 42, 49, 10, 37, 16, 25, 15, 48, 26,  5, 34, 27,  4, 32, 20, 19,
        8, 45, 28, 50, 22,  0, 51,  9, 54,  3, 38,  2, 46, 44, 43, 40, 31,
        7, 21, 33, 11, 14, 47, 39,  1, 24,  6, 17, 23, 30, 41, 52, 36, 18,
       13, 29, 12, 35]), 'cur_cost': 117803.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([53, 37, 42, 49, 10, 16, 25, 15, 48, 26,  5, 34, 27,  4, 32, 20, 19,
        8, 45, 28, 50, 22,  0, 51,  9, 54,  3, 38,  2, 46, 44, 43, 40, 31,
        7, 21, 33, 11, 14, 47, 39,  1, 24,  6, 17, 23, 30, 41, 52, 36, 18,
       13, 29, 12, 35]), 'cur_cost': 115002.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [11, 13, 54, 24, 37, 40, 8, 23, 41, 39, 29, 33, 6, 5, 28, 35, 22, 9, 30, 36, 43, 1, 2, 34, 3, 42, 31, 46, 44, 50, 49, 45, 14, 51, 53, 18, 20, 47, 16, 12, 19, 26, 38, 4, 32, 25, 0, 10, 21, 52, 17, 48, 15, 27, 7], 'cur_cost': 63611.0, 'intermediate_solutions': [{'tour': [0, 10, 4, 2, 7, 9, 5, 3, 6, 1, 8, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 54, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 35, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 20011.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 4, 2, 7, 9, 5, 3, 6, 1, 8, 26, 31, 25, 23, 22, 32, 28, 49, 41, 40, 34, 39, 43, 33, 42, 37, 38, 36, 35, 29, 27, 30, 24, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 14691.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 4, 7, 9, 5, 3, 6, 1, 8, 26, 31, 25, 23, 2, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 13683.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 0,  2, 29, 36, 13, 11, 34, 37,  3, 46, 31, 25, 53, 40, 51, 21, 26,
       15, 27, 48, 32, 49,  5, 16, 19,  8, 30,  1, 24, 43,  6, 47, 22, 38,
       14, 28, 33, 39, 23, 10, 20, 41, 12,  4, 35,  7, 54, 52, 45, 18,  9,
       44, 42, 17, 50], dtype=int64), 'cur_cost': 110078.0, 'intermediate_solutions': [{'tour': array([17, 44, 51, 46, 11, 29, 41,  8, 53, 27, 28, 18,  3,  2, 19, 45, 40,
       47, 13,  6, 24, 22, 34, 54, 35, 21, 16, 48, 30, 42, 23, 12,  7,  9,
       31, 20, 32, 26, 10, 49,  5, 33,  1, 14, 38,  0, 25, 36, 39, 15, 50,
       52, 37,  4, 43]), 'cur_cost': 100684.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([46, 17, 44, 51, 11, 29, 41,  8, 53, 27, 28, 18,  3,  2, 19, 45, 40,
       47, 13,  6, 24, 22, 34, 54, 35, 21, 16, 48, 30, 42, 23, 12,  7,  9,
       31, 20, 32, 26, 10, 49,  5, 33,  1, 14, 38,  0, 25, 36, 39, 15, 50,
       52, 37,  4, 43]), 'cur_cost': 101007.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([11, 46, 17, 44, 51, 29, 41,  8, 53, 27, 28, 18,  3,  2, 19, 45, 40,
       47, 13,  6, 24, 22, 34, 54, 35, 21, 16, 48, 30, 42, 23, 12,  7,  9,
       31, 20, 32, 26, 10, 49,  5, 33,  1, 14, 38,  0, 25, 36, 39, 15, 50,
       52, 37,  4, 43]), 'cur_cost': 101363.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([51, 46, 17, 44, 11, 29, 41,  8, 53, 27, 28, 18,  3,  2, 19, 45, 40,
       47, 13,  6, 24, 22, 34, 54, 35, 21, 16, 48, 30, 42, 23, 12,  7,  9,
       31, 20, 32, 26, 10, 49,  5, 33,  1, 14, 38,  0, 25, 36, 39, 15, 50,
       52, 37,  4, 43]), 'cur_cost': 101137.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([51, 11, 46, 17, 44, 29, 41,  8, 53, 27, 28, 18,  3,  2, 19, 45, 40,
       47, 13,  6, 24, 22, 34, 54, 35, 21, 16, 48, 30, 42, 23, 12,  7,  9,
       31, 20, 32, 26, 10, 49,  5, 33,  1, 14, 38,  0, 25, 36, 39, 15, 50,
       52, 37,  4, 43]), 'cur_cost': 101703.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 09:52:08,728 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:08,728 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:08,731 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=16496.000, 多样性=0.979
2025-08-05 09:52:08,731 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 09:52:08,731 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 09:52:08,731 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:08,732 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.37142532680957613, 'best_improvement': -0.5356544405138708}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.059956236323851345}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.06705412081799529, 'recent_improvements': [0.036377549827066334, -0.11012289558592812, 0.17048579146305695], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 6, 'new_count': 6, 'count_change': 0, 'old_best_cost': 10443, 'new_best_cost': 10443, 'quality_improvement': 0.0, 'old_diversity': 0.7139393939393939, 'new_diversity': 0.7139393939393939, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:52:08,732 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 09:52:08,732 - __main__ - INFO - composite10_55 开始进化第 4 代
2025-08-05 09:52:08,732 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 09:52:08,733 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:08,733 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=16496.000, 多样性=0.979
2025-08-05 09:52:08,733 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:08,736 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.979
2025-08-05 09:52:08,736 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:08,738 - EliteExpert - INFO - 精英解分析完成: 精英解数量=6, 多样性=0.714
2025-08-05 09:52:08,740 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 09:52:08,740 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:08,740 - LandscapeExpert - INFO - 添加精英解数据: 6个精英解
2025-08-05 09:52:08,741 - LandscapeExpert - INFO - 数据提取成功: 16个路径, 16个适应度值
2025-08-05 09:52:08,780 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.188, 适应度梯度: -1223.687, 聚类评分: 0.000, 覆盖率: 0.130, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:08,780 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 09:52:08,780 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:08,780 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite10_55
2025-08-05 09:52:08,785 - visualization.landscape_visualizer - INFO - 插值约束: 102 个点被约束到最小值 10443.00
2025-08-05 09:52:08,887 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite10_55\landscape_composite10_55_iter_109_20250805_095208.html
2025-08-05 09:52:08,935 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite10_55\dashboard_composite10_55_iter_109_20250805_095208.html
2025-08-05 09:52:08,935 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 109
2025-08-05 09:52:08,935 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 09:52:08,936 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1957秒
2025-08-05 09:52:08,936 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1875, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -1223.6874999999995, 'local_optima_density': 0.1875, 'gradient_variance': 1409221076.5448437, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1303, 'fitness_entropy': 0.8369450311585397, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -1223.687)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.130)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358728.7803574, 'performance_metrics': {}}}
2025-08-05 09:52:08,936 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:08,936 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:08,936 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:08,936 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:08,936 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:52:08,937 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 09:52:08,937 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:52:08,937 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:08,937 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:08,937 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:52:08,937 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:08,937 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:08,937 - experts.management.collaboration_manager - INFO - 识别精英个体: {2, 3} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:08,937 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-05 09:52:08,937 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:08,938 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:08,938 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 100096.0
2025-08-05 09:52:08,943 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 09:52:08,943 - ExploitationExpert - INFO - res_population_costs: [10443, 10443, 10443, 10445.0, 10450, 10451.0]
2025-08-05 09:52:08,943 - ExploitationExpert - INFO - res_populations: [array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  9,  7,  2,  3,
        5,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 43, 39, 34, 40, 41, 38,
       36, 37, 42, 33, 35, 29, 22, 27, 32, 28, 23, 25, 31, 26, 24, 30, 49,
       46, 44, 53, 54, 50, 48, 52, 47, 51, 45, 13, 17, 14, 11, 16, 15, 20,
       18, 12, 21, 19], dtype=int64), array([ 0, 21, 19, 18, 12, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       14, 11, 13, 17, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 14, 11, 13, 17, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 30, 24, 26, 31, 25, 23, 28, 32, 27, 22, 29,
       35, 36, 33, 42, 37, 38, 41, 40, 34, 39, 43, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64)]
2025-08-05 09:52:08,946 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:08,946 - ExploitationExpert - INFO - populations: [{'tour': array([46, 33, 50, 38, 31, 28, 51, 32,  0, 19, 12, 10, 34, 42,  5, 22,  9,
       18,  8, 14, 21, 23, 45, 49, 15, 26, 48,  6,  3, 11, 35, 27, 16, 47,
       29, 52, 37,  7, 53, 25,  1, 30, 17, 39,  4, 54, 20, 13,  2, 24, 40,
       43, 36, 41, 44], dtype=int64), 'cur_cost': 100096.0}, {'tour': [51, 50, 15, 18, 49, 54, 26, 32, 4, 9, 25, 39, 36, 23, 37, 8, 35, 38, 24, 28, 6, 33, 34, 3, 10, 31, 43, 22, 2, 0, 12, 45, 19, 48, 47, 44, 52, 14, 13, 17, 11, 27, 40, 41, 30, 5, 29, 53, 46, 20, 16, 21, 1, 7, 42], 'cur_cost': 55302.0}, {'tour': [13, 32, 25, 0, 27, 43, 10, 5, 39, 22, 31, 6, 3, 30, 9, 2, 29, 26, 41, 49, 50, 18, 20, 48, 21, 15, 52, 19, 11, 17, 16, 46, 53, 51, 47, 44, 54, 24, 36, 38, 35, 34, 33, 37, 28, 1, 23, 8, 42, 40, 7, 4, 12, 14, 45], 'cur_cost': 48537.0}, {'tour': [0, 5, 15, 1, 4, 6, 9, 7, 3, 2, 8, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 16496.0}, {'tour': [13, 50, 25, 0, 49, 43, 10, 32, 4, 6, 36, 35, 30, 42, 8, 33, 17, 12, 44, 15, 20, 14, 27, 40, 41, 3, 37, 48, 31, 34, 52, 16, 51, 39, 2, 53, 1, 38, 5, 18, 7, 22, 26, 24, 9, 28, 11, 46, 23, 21, 54, 29, 45, 47, 19], 'cur_cost': 90912.0}, {'tour': [21, 18, 26, 39, 9, 25, 6, 36, 23, 8, 29, 38, 32, 49, 3, 43, 0, 12, 31, 2, 53, 44, 14, 48, 11, 34, 24, 41, 28, 50, 16, 42, 7, 35, 15, 45, 20, 52, 37, 19, 46, 33, 1, 30, 47, 51, 54, 13, 40, 4, 17, 27, 10, 22, 5], 'cur_cost': 101275.0}, {'tour': [4, 26, 7, 25, 6, 23, 35, 44, 2, 33, 40, 43, 19, 0, 12, 51, 53, 14, 36, 24, 41, 30, 42, 20, 50, 52, 31, 48, 34, 22, 27, 49, 29, 13, 37, 10, 8, 54, 16, 21, 32, 47, 1, 15, 18, 5, 17, 45, 11, 38, 39, 3, 46, 28, 9], 'cur_cost': 96834.0}, {'tour': [49, 27, 40, 14, 15, 0, 20, 8, 22, 37, 17, 13, 44, 48, 9, 51, 3, 6, 31, 33, 32, 43, 38, 28, 12, 54, 18, 21, 34, 1, 45, 53, 47, 50, 16, 30, 2, 4, 25, 19, 41, 5, 7, 52, 36, 29, 26, 11, 39, 24, 46, 10, 23, 42, 35], 'cur_cost': 91530.0}, {'tour': [11, 13, 54, 24, 37, 40, 8, 23, 41, 39, 29, 33, 6, 5, 28, 35, 22, 9, 30, 36, 43, 1, 2, 34, 3, 42, 31, 46, 44, 50, 49, 45, 14, 51, 53, 18, 20, 47, 16, 12, 19, 26, 38, 4, 32, 25, 0, 10, 21, 52, 17, 48, 15, 27, 7], 'cur_cost': 63611.0}, {'tour': [0, 2, 29, 36, 13, 11, 34, 37, 3, 46, 31, 25, 53, 40, 51, 21, 26, 15, 27, 48, 32, 49, 5, 16, 19, 8, 30, 1, 24, 43, 6, 47, 22, 38, 14, 28, 33, 39, 23, 10, 20, 41, 12, 4, 35, 7, 54, 52, 45, 18, 9, 44, 42, 17, 50], 'cur_cost': 110078.0}]
2025-08-05 09:52:08,947 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:08,947 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 281, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 281, 'cache_hits': 0, 'similarity_calculations': 1384, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:08,948 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([46, 33, 50, 38, 31, 28, 51, 32,  0, 19, 12, 10, 34, 42,  5, 22,  9,
       18,  8, 14, 21, 23, 45, 49, 15, 26, 48,  6,  3, 11, 35, 27, 16, 47,
       29, 52, 37,  7, 53, 25,  1, 30, 17, 39,  4, 54, 20, 13,  2, 24, 40,
       43, 36, 41, 44], dtype=int64), 'cur_cost': 100096.0, 'intermediate_solutions': [{'tour': array([10, 27, 32,  3, 33, 37, 35,  4, 45, 39, 50, 34, 15, 26, 46, 16, 36,
       49, 41, 23, 13, 40, 22,  7, 47,  1, 24, 52,  5, 25,  0, 17, 54, 48,
       21,  6, 18, 30, 53,  9, 29,  8, 43, 12, 11, 38, 51, 28,  2, 19, 42,
       44, 20, 14, 31]), 'cur_cost': 114125.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 3, 10, 27, 32, 33, 37, 35,  4, 45, 39, 50, 34, 15, 26, 46, 16, 36,
       49, 41, 23, 13, 40, 22,  7, 47,  1, 24, 52,  5, 25,  0, 17, 54, 48,
       21,  6, 18, 30, 53,  9, 29,  8, 43, 12, 11, 38, 51, 28,  2, 19, 42,
       44, 20, 14, 31]), 'cur_cost': 111672.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([33,  3, 10, 27, 32, 37, 35,  4, 45, 39, 50, 34, 15, 26, 46, 16, 36,
       49, 41, 23, 13, 40, 22,  7, 47,  1, 24, 52,  5, 25,  0, 17, 54, 48,
       21,  6, 18, 30, 53,  9, 29,  8, 43, 12, 11, 38, 51, 28,  2, 19, 42,
       44, 20, 14, 31]), 'cur_cost': 113632.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([32,  3, 10, 27, 33, 37, 35,  4, 45, 39, 50, 34, 15, 26, 46, 16, 36,
       49, 41, 23, 13, 40, 22,  7, 47,  1, 24, 52,  5, 25,  0, 17, 54, 48,
       21,  6, 18, 30, 53,  9, 29,  8, 43, 12, 11, 38, 51, 28,  2, 19, 42,
       44, 20, 14, 31]), 'cur_cost': 111769.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([32, 33,  3, 10, 27, 37, 35,  4, 45, 39, 50, 34, 15, 26, 46, 16, 36,
       49, 41, 23, 13, 40, 22,  7, 47,  1, 24, 52,  5, 25,  0, 17, 54, 48,
       21,  6, 18, 30, 53,  9, 29,  8, 43, 12, 11, 38, 51, 28,  2, 19, 42,
       44, 20, 14, 31]), 'cur_cost': 113639.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:08,948 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 100096.00)
2025-08-05 09:52:08,948 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:08,948 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:08,948 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:08,950 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 09:52:08,950 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,950 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,950 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,951 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,951 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16454.0, 路径长度: 55, 收集中间解: 3
2025-08-05 09:52:08,951 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 13, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 16454.0, 'intermediate_solutions': [{'tour': [51, 50, 15, 18, 49, 54, 26, 29, 4, 9, 25, 39, 36, 23, 37, 8, 35, 38, 24, 28, 6, 33, 34, 3, 10, 31, 43, 22, 2, 0, 12, 45, 19, 48, 47, 44, 52, 14, 13, 17, 11, 27, 40, 41, 30, 5, 32, 53, 46, 20, 16, 21, 1, 7, 42], 'cur_cost': 55254.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [51, 50, 15, 5, 30, 41, 40, 27, 11, 17, 13, 14, 52, 44, 47, 48, 19, 45, 12, 0, 2, 22, 43, 31, 10, 3, 34, 33, 6, 28, 24, 38, 35, 8, 37, 23, 36, 39, 25, 9, 4, 32, 26, 54, 49, 18, 29, 53, 46, 20, 16, 21, 1, 7, 42], 'cur_cost': 59254.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [51, 50, 15, 18, 49, 54, 26, 32, 4, 9, 25, 39, 36, 23, 37, 8, 35, 38, 24, 28, 6, 46, 33, 34, 3, 10, 31, 43, 22, 2, 0, 12, 45, 19, 48, 47, 44, 52, 14, 13, 17, 11, 27, 40, 41, 30, 5, 29, 53, 20, 16, 21, 1, 7, 42], 'cur_cost': 58749.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:08,951 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 16454.00)
2025-08-05 09:52:08,952 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:08,952 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:08,952 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:08,956 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 55
2025-08-05 09:52:08,956 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,956 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,957 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,957 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,957 - ExplorationExpert - INFO - 探索路径生成完成，成本: 64551.0, 路径长度: 55, 收集中间解: 3
2025-08-05 09:52:08,957 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [15, 46, 53, 23, 36, 1, 7, 38, 29, 32, 10, 22, 39, 40, 33, 26, 25, 3, 28, 5, 37, 44, 16, 11, 21, 18, 48, 54, 12, 52, 30, 27, 0, 4, 2, 24, 41, 42, 49, 19, 51, 45, 50, 17, 31, 6, 43, 8, 9, 34, 47, 14, 13, 20, 35], 'cur_cost': 64551.0, 'intermediate_solutions': [{'tour': [13, 32, 25, 39, 27, 43, 10, 5, 0, 22, 31, 6, 3, 30, 9, 2, 29, 26, 41, 49, 50, 18, 20, 48, 21, 15, 52, 19, 11, 17, 16, 46, 53, 51, 47, 44, 54, 24, 36, 38, 35, 34, 33, 37, 28, 1, 23, 8, 42, 40, 7, 4, 12, 14, 45], 'cur_cost': 46130.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [13, 32, 25, 0, 27, 43, 10, 5, 39, 22, 31, 6, 3, 30, 9, 2, 29, 26, 41, 49, 50, 18, 20, 48, 21, 15, 52, 19, 11, 17, 16, 46, 53, 51, 47, 44, 54, 24, 36, 38, 35, 34, 33, 37, 28, 1, 23, 8, 14, 12, 4, 7, 40, 42, 45], 'cur_cost': 50970.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [13, 32, 25, 0, 27, 12, 43, 10, 5, 39, 22, 31, 6, 3, 30, 9, 2, 29, 26, 41, 49, 50, 18, 20, 48, 21, 15, 52, 19, 11, 17, 16, 46, 53, 51, 47, 44, 54, 24, 36, 38, 35, 34, 33, 37, 28, 1, 23, 8, 42, 40, 7, 4, 14, 45], 'cur_cost': 53443.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:08,957 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 64551.00)
2025-08-05 09:52:08,957 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:52:08,958 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:52:08,958 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:08,959 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 09:52:08,959 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,960 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,960 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,960 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,960 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11726.0, 路径长度: 55, 收集中间解: 3
2025-08-05 09:52:08,960 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 6, 23, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4], 'cur_cost': 11726.0, 'intermediate_solutions': [{'tour': [0, 5, 15, 1, 4, 6, 9, 7, 3, 2, 8, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 13, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 33, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 28675.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 15, 1, 4, 6, 9, 7, 3, 43, 33, 42, 37, 38, 36, 35, 27, 29, 26, 30, 24, 28, 32, 22, 23, 25, 31, 10, 8, 2, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 20945.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 15, 1, 4, 6, 9, 7, 3, 2, 8, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 48, 13, 17, 14, 16, 18, 20, 12, 50, 21, 19, 11], 'cur_cost': 18888.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:08,960 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 11726.00)
2025-08-05 09:52:08,961 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:08,961 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:08,961 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:08,962 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 09:52:08,962 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,963 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,963 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,963 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,963 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16467.0, 路径长度: 55, 收集中间解: 3
2025-08-05 09:52:08,963 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 6, 14, 1, 4, 9, 7, 3, 5, 8, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 19, 21, 12, 18, 16, 15, 20, 11], 'cur_cost': 16467.0, 'intermediate_solutions': [{'tour': [13, 50, 25, 0, 49, 43, 10, 32, 4, 6, 36, 35, 30, 42, 8, 33, 17, 12, 44, 18, 20, 14, 27, 40, 41, 3, 37, 48, 31, 34, 52, 16, 51, 39, 2, 53, 1, 38, 5, 15, 7, 22, 26, 24, 9, 28, 11, 46, 23, 21, 54, 29, 45, 47, 19], 'cur_cost': 90970.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [13, 50, 25, 0, 49, 43, 10, 32, 4, 6, 36, 35, 30, 42, 8, 3, 41, 40, 27, 14, 20, 15, 44, 12, 17, 33, 37, 48, 31, 34, 52, 16, 51, 39, 2, 53, 1, 38, 5, 18, 7, 22, 26, 24, 9, 28, 11, 46, 23, 21, 54, 29, 45, 47, 19], 'cur_cost': 86510.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [13, 50, 25, 0, 49, 43, 10, 32, 4, 6, 36, 35, 30, 42, 8, 33, 17, 12, 44, 15, 20, 14, 27, 40, 41, 3, 37, 48, 31, 34, 52, 16, 51, 39, 2, 53, 1, 38, 5, 18, 7, 22, 26, 24, 9, 28, 21, 11, 46, 23, 54, 29, 45, 47, 19], 'cur_cost': 89306.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:08,963 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 16467.00)
2025-08-05 09:52:08,964 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 09:52:08,964 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:08,964 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:08,964 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 117290.0
2025-08-05 09:52:08,969 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 09:52:08,969 - ExploitationExpert - INFO - res_population_costs: [10443, 10443, 10443, 10445.0, 10450, 10451.0]
2025-08-05 09:52:08,970 - ExploitationExpert - INFO - res_populations: [array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  9,  7,  2,  3,
        5,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 43, 39, 34, 40, 41, 38,
       36, 37, 42, 33, 35, 29, 22, 27, 32, 28, 23, 25, 31, 26, 24, 30, 49,
       46, 44, 53, 54, 50, 48, 52, 47, 51, 45, 13, 17, 14, 11, 16, 15, 20,
       18, 12, 21, 19], dtype=int64), array([ 0, 21, 19, 18, 12, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       14, 11, 13, 17, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 14, 11, 13, 17, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 30, 24, 26, 31, 25, 23, 28, 32, 27, 22, 29,
       35, 36, 33, 42, 37, 38, 41, 40, 34, 39, 43, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64)]
2025-08-05 09:52:08,972 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:08,972 - ExploitationExpert - INFO - populations: [{'tour': array([46, 33, 50, 38, 31, 28, 51, 32,  0, 19, 12, 10, 34, 42,  5, 22,  9,
       18,  8, 14, 21, 23, 45, 49, 15, 26, 48,  6,  3, 11, 35, 27, 16, 47,
       29, 52, 37,  7, 53, 25,  1, 30, 17, 39,  4, 54, 20, 13,  2, 24, 40,
       43, 36, 41, 44], dtype=int64), 'cur_cost': 100096.0}, {'tour': [0, 13, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 16454.0}, {'tour': [15, 46, 53, 23, 36, 1, 7, 38, 29, 32, 10, 22, 39, 40, 33, 26, 25, 3, 28, 5, 37, 44, 16, 11, 21, 18, 48, 54, 12, 52, 30, 27, 0, 4, 2, 24, 41, 42, 49, 19, 51, 45, 50, 17, 31, 6, 43, 8, 9, 34, 47, 14, 13, 20, 35], 'cur_cost': 64551.0}, {'tour': [0, 6, 23, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4], 'cur_cost': 11726.0}, {'tour': [0, 6, 14, 1, 4, 9, 7, 3, 5, 8, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 19, 21, 12, 18, 16, 15, 20, 11], 'cur_cost': 16467.0}, {'tour': array([24, 37,  1, 50, 26, 12, 33, 42,  7, 21, 16, 35,  5, 11, 29, 53, 45,
       49, 28, 17, 41, 46, 22, 47, 13,  9, 51, 32, 36, 18, 27,  0, 43,  6,
       34, 19,  8, 52, 23, 30,  3, 10, 39, 44, 25, 38, 14,  4, 54, 20, 31,
        2, 48, 15, 40], dtype=int64), 'cur_cost': 117290.0}, {'tour': [4, 26, 7, 25, 6, 23, 35, 44, 2, 33, 40, 43, 19, 0, 12, 51, 53, 14, 36, 24, 41, 30, 42, 20, 50, 52, 31, 48, 34, 22, 27, 49, 29, 13, 37, 10, 8, 54, 16, 21, 32, 47, 1, 15, 18, 5, 17, 45, 11, 38, 39, 3, 46, 28, 9], 'cur_cost': 96834.0}, {'tour': [49, 27, 40, 14, 15, 0, 20, 8, 22, 37, 17, 13, 44, 48, 9, 51, 3, 6, 31, 33, 32, 43, 38, 28, 12, 54, 18, 21, 34, 1, 45, 53, 47, 50, 16, 30, 2, 4, 25, 19, 41, 5, 7, 52, 36, 29, 26, 11, 39, 24, 46, 10, 23, 42, 35], 'cur_cost': 91530.0}, {'tour': [11, 13, 54, 24, 37, 40, 8, 23, 41, 39, 29, 33, 6, 5, 28, 35, 22, 9, 30, 36, 43, 1, 2, 34, 3, 42, 31, 46, 44, 50, 49, 45, 14, 51, 53, 18, 20, 47, 16, 12, 19, 26, 38, 4, 32, 25, 0, 10, 21, 52, 17, 48, 15, 27, 7], 'cur_cost': 63611.0}, {'tour': [0, 2, 29, 36, 13, 11, 34, 37, 3, 46, 31, 25, 53, 40, 51, 21, 26, 15, 27, 48, 32, 49, 5, 16, 19, 8, 30, 1, 24, 43, 6, 47, 22, 38, 14, 28, 33, 39, 23, 10, 20, 41, 12, 4, 35, 7, 54, 52, 45, 18, 9, 44, 42, 17, 50], 'cur_cost': 110078.0}]
2025-08-05 09:52:08,973 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:08,973 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 282, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 282, 'cache_hits': 0, 'similarity_calculations': 1392, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:08,974 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([24, 37,  1, 50, 26, 12, 33, 42,  7, 21, 16, 35,  5, 11, 29, 53, 45,
       49, 28, 17, 41, 46, 22, 47, 13,  9, 51, 32, 36, 18, 27,  0, 43,  6,
       34, 19,  8, 52, 23, 30,  3, 10, 39, 44, 25, 38, 14,  4, 54, 20, 31,
        2, 48, 15, 40], dtype=int64), 'cur_cost': 117290.0, 'intermediate_solutions': [{'tour': array([26, 18, 21, 39,  9, 25,  6, 36, 23,  8, 29, 38, 32, 49,  3, 43,  0,
       12, 31,  2, 53, 44, 14, 48, 11, 34, 24, 41, 28, 50, 16, 42,  7, 35,
       15, 45, 20, 52, 37, 19, 46, 33,  1, 30, 47, 51, 54, 13, 40,  4, 17,
       27, 10, 22,  5]), 'cur_cost': 102176.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([39, 26, 18, 21,  9, 25,  6, 36, 23,  8, 29, 38, 32, 49,  3, 43,  0,
       12, 31,  2, 53, 44, 14, 48, 11, 34, 24, 41, 28, 50, 16, 42,  7, 35,
       15, 45, 20, 52, 37, 19, 46, 33,  1, 30, 47, 51, 54, 13, 40,  4, 17,
       27, 10, 22,  5]), 'cur_cost': 101302.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9, 39, 26, 18, 21, 25,  6, 36, 23,  8, 29, 38, 32, 49,  3, 43,  0,
       12, 31,  2, 53, 44, 14, 48, 11, 34, 24, 41, 28, 50, 16, 42,  7, 35,
       15, 45, 20, 52, 37, 19, 46, 33,  1, 30, 47, 51, 54, 13, 40,  4, 17,
       27, 10, 22,  5]), 'cur_cost': 99484.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([21, 39, 26, 18,  9, 25,  6, 36, 23,  8, 29, 38, 32, 49,  3, 43,  0,
       12, 31,  2, 53, 44, 14, 48, 11, 34, 24, 41, 28, 50, 16, 42,  7, 35,
       15, 45, 20, 52, 37, 19, 46, 33,  1, 30, 47, 51, 54, 13, 40,  4, 17,
       27, 10, 22,  5]), 'cur_cost': 105441.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([21,  9, 39, 26, 18, 25,  6, 36, 23,  8, 29, 38, 32, 49,  3, 43,  0,
       12, 31,  2, 53, 44, 14, 48, 11, 34, 24, 41, 28, 50, 16, 42,  7, 35,
       15, 45, 20, 52, 37, 19, 46, 33,  1, 30, 47, 51, 54, 13, 40,  4, 17,
       27, 10, 22,  5]), 'cur_cost': 105169.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:08,974 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 117290.00)
2025-08-05 09:52:08,974 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:08,974 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:08,974 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:08,976 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 09:52:08,976 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,976 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,976 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,977 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,977 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16395.0, 路径长度: 55, 收集中间解: 3
2025-08-05 09:52:08,977 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 17, 9, 12, 21, 19, 18, 16, 15, 20, 14, 11, 13, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 3, 5, 6, 4, 1, 2], 'cur_cost': 16395.0, 'intermediate_solutions': [{'tour': [4, 26, 7, 25, 6, 23, 35, 44, 2, 33, 40, 43, 19, 0, 12, 51, 28, 14, 36, 24, 41, 30, 42, 20, 50, 52, 31, 48, 34, 22, 27, 49, 29, 13, 37, 10, 8, 54, 16, 21, 32, 47, 1, 15, 18, 5, 17, 45, 11, 38, 39, 3, 46, 53, 9], 'cur_cost': 99680.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 26, 7, 25, 6, 23, 35, 44, 2, 33, 53, 51, 12, 0, 19, 43, 40, 14, 36, 24, 41, 30, 42, 20, 50, 52, 31, 48, 34, 22, 27, 49, 29, 13, 37, 10, 8, 54, 16, 21, 32, 47, 1, 15, 18, 5, 17, 45, 11, 38, 39, 3, 46, 28, 9], 'cur_cost': 101970.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 26, 7, 25, 6, 23, 35, 44, 2, 33, 40, 43, 19, 0, 12, 51, 53, 14, 36, 24, 41, 30, 42, 20, 50, 52, 31, 48, 34, 22, 27, 49, 29, 13, 37, 10, 8, 54, 16, 21, 38, 32, 47, 1, 15, 18, 5, 17, 45, 11, 39, 3, 46, 28, 9], 'cur_cost': 99035.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:08,977 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 16395.00)
2025-08-05 09:52:08,977 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:08,977 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:08,978 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:08,979 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 09:52:08,979 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,979 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,979 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,980 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,980 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10786.0, 路径长度: 55, 收集中间解: 3
2025-08-05 09:52:08,980 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 5, 18, 21, 19, 17, 13, 14, 16, 15, 20, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 3, 2, 1, 4, 6], 'cur_cost': 10786.0, 'intermediate_solutions': [{'tour': [49, 27, 40, 14, 15, 0, 20, 8, 23, 37, 17, 13, 44, 48, 9, 51, 3, 6, 31, 33, 32, 43, 38, 28, 12, 54, 18, 21, 34, 1, 45, 53, 47, 50, 16, 30, 2, 4, 25, 19, 41, 5, 7, 52, 36, 29, 26, 11, 39, 24, 46, 10, 22, 42, 35], 'cur_cost': 91530.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [49, 27, 40, 14, 15, 0, 20, 8, 22, 37, 17, 13, 44, 48, 9, 51, 3, 6, 31, 33, 32, 43, 38, 28, 12, 54, 18, 21, 34, 1, 45, 53, 47, 50, 16, 30, 2, 4, 25, 26, 29, 36, 52, 7, 5, 41, 19, 11, 39, 24, 46, 10, 23, 42, 35], 'cur_cost': 86664.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [49, 27, 40, 14, 15, 0, 20, 16, 8, 22, 37, 17, 13, 44, 48, 9, 51, 3, 6, 31, 33, 32, 43, 38, 28, 12, 54, 18, 21, 34, 1, 45, 53, 47, 50, 30, 2, 4, 25, 19, 41, 5, 7, 52, 36, 29, 26, 11, 39, 24, 46, 10, 23, 42, 35], 'cur_cost': 89924.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:08,980 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 10786.00)
2025-08-05 09:52:08,980 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:52:08,980 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:52:08,980 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:08,982 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 09:52:08,982 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,982 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,982 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,983 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:08,983 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10783.0, 路径长度: 55, 收集中间解: 3
2025-08-05 09:52:08,983 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 9, 8, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 1, 4, 6, 5, 3, 7, 2], 'cur_cost': 10783.0, 'intermediate_solutions': [{'tour': [11, 13, 54, 24, 37, 40, 8, 23, 41, 39, 29, 33, 6, 5, 28, 35, 22, 9, 30, 36, 43, 1, 2, 34, 3, 42, 31, 46, 44, 50, 49, 45, 14, 51, 19, 18, 20, 47, 16, 12, 53, 26, 38, 4, 32, 25, 0, 10, 21, 52, 17, 48, 15, 27, 7], 'cur_cost': 64277.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 13, 54, 24, 37, 40, 8, 23, 41, 39, 29, 33, 6, 5, 28, 35, 22, 9, 30, 36, 43, 1, 2, 34, 3, 42, 31, 46, 44, 50, 49, 45, 14, 51, 53, 18, 20, 47, 16, 12, 19, 26, 38, 4, 32, 25, 17, 52, 21, 10, 0, 48, 15, 27, 7], 'cur_cost': 66563.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 13, 54, 24, 37, 40, 8, 23, 41, 39, 29, 33, 6, 5, 28, 35, 22, 9, 30, 36, 43, 1, 2, 34, 3, 42, 31, 46, 44, 50, 49, 45, 14, 51, 53, 18, 47, 16, 12, 19, 26, 38, 4, 32, 25, 20, 0, 10, 21, 52, 17, 48, 15, 27, 7], 'cur_cost': 67613.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:08,983 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 10783.00)
2025-08-05 09:52:08,983 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 09:52:08,983 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:08,983 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:08,984 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 102391.0
2025-08-05 09:52:08,992 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 09:52:08,992 - ExploitationExpert - INFO - res_population_costs: [10443, 10443, 10443, 10445.0, 10450, 10451.0]
2025-08-05 09:52:08,992 - ExploitationExpert - INFO - res_populations: [array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  9,  7,  2,  3,
        5,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 43, 39, 34, 40, 41, 38,
       36, 37, 42, 33, 35, 29, 22, 27, 32, 28, 23, 25, 31, 26, 24, 30, 49,
       46, 44, 53, 54, 50, 48, 52, 47, 51, 45, 13, 17, 14, 11, 16, 15, 20,
       18, 12, 21, 19], dtype=int64), array([ 0, 21, 19, 18, 12, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       14, 11, 13, 17, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 14, 11, 13, 17, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 30, 24, 26, 31, 25, 23, 28, 32, 27, 22, 29,
       35, 36, 33, 42, 37, 38, 41, 40, 34, 39, 43, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64)]
2025-08-05 09:52:08,995 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:08,995 - ExploitationExpert - INFO - populations: [{'tour': array([46, 33, 50, 38, 31, 28, 51, 32,  0, 19, 12, 10, 34, 42,  5, 22,  9,
       18,  8, 14, 21, 23, 45, 49, 15, 26, 48,  6,  3, 11, 35, 27, 16, 47,
       29, 52, 37,  7, 53, 25,  1, 30, 17, 39,  4, 54, 20, 13,  2, 24, 40,
       43, 36, 41, 44], dtype=int64), 'cur_cost': 100096.0}, {'tour': [0, 13, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 16454.0}, {'tour': [15, 46, 53, 23, 36, 1, 7, 38, 29, 32, 10, 22, 39, 40, 33, 26, 25, 3, 28, 5, 37, 44, 16, 11, 21, 18, 48, 54, 12, 52, 30, 27, 0, 4, 2, 24, 41, 42, 49, 19, 51, 45, 50, 17, 31, 6, 43, 8, 9, 34, 47, 14, 13, 20, 35], 'cur_cost': 64551.0}, {'tour': [0, 6, 23, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4], 'cur_cost': 11726.0}, {'tour': [0, 6, 14, 1, 4, 9, 7, 3, 5, 8, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 19, 21, 12, 18, 16, 15, 20, 11], 'cur_cost': 16467.0}, {'tour': array([24, 37,  1, 50, 26, 12, 33, 42,  7, 21, 16, 35,  5, 11, 29, 53, 45,
       49, 28, 17, 41, 46, 22, 47, 13,  9, 51, 32, 36, 18, 27,  0, 43,  6,
       34, 19,  8, 52, 23, 30,  3, 10, 39, 44, 25, 38, 14,  4, 54, 20, 31,
        2, 48, 15, 40], dtype=int64), 'cur_cost': 117290.0}, {'tour': [0, 17, 9, 12, 21, 19, 18, 16, 15, 20, 14, 11, 13, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 3, 5, 6, 4, 1, 2], 'cur_cost': 16395.0}, {'tour': [0, 5, 18, 21, 19, 17, 13, 14, 16, 15, 20, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 3, 2, 1, 4, 6], 'cur_cost': 10786.0}, {'tour': [0, 9, 8, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 1, 4, 6, 5, 3, 7, 2], 'cur_cost': 10783.0}, {'tour': array([16, 21, 27, 47, 25, 40,  6,  5, 33, 13, 32, 12, 24, 14, 35,  9, 51,
       42, 50, 41, 48, 17, 22, 10, 31, 49, 26,  8, 53, 52, 30, 11, 23,  1,
       18, 44,  2,  7, 15, 19, 45, 29, 36,  4, 46, 54, 43, 38, 39, 37, 34,
       20,  3,  0, 28], dtype=int64), 'cur_cost': 102391.0}]
2025-08-05 09:52:08,996 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:08,997 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 283, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 283, 'cache_hits': 0, 'similarity_calculations': 1401, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:08,997 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([16, 21, 27, 47, 25, 40,  6,  5, 33, 13, 32, 12, 24, 14, 35,  9, 51,
       42, 50, 41, 48, 17, 22, 10, 31, 49, 26,  8, 53, 52, 30, 11, 23,  1,
       18, 44,  2,  7, 15, 19, 45, 29, 36,  4, 46, 54, 43, 38, 39, 37, 34,
       20,  3,  0, 28], dtype=int64), 'cur_cost': 102391.0, 'intermediate_solutions': [{'tour': array([29,  2,  0, 36, 13, 11, 34, 37,  3, 46, 31, 25, 53, 40, 51, 21, 26,
       15, 27, 48, 32, 49,  5, 16, 19,  8, 30,  1, 24, 43,  6, 47, 22, 38,
       14, 28, 33, 39, 23, 10, 20, 41, 12,  4, 35,  7, 54, 52, 45, 18,  9,
       44, 42, 17, 50]), 'cur_cost': 110245.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([36, 29,  2,  0, 13, 11, 34, 37,  3, 46, 31, 25, 53, 40, 51, 21, 26,
       15, 27, 48, 32, 49,  5, 16, 19,  8, 30,  1, 24, 43,  6, 47, 22, 38,
       14, 28, 33, 39, 23, 10, 20, 41, 12,  4, 35,  7, 54, 52, 45, 18,  9,
       44, 42, 17, 50]), 'cur_cost': 109293.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([13, 36, 29,  2,  0, 11, 34, 37,  3, 46, 31, 25, 53, 40, 51, 21, 26,
       15, 27, 48, 32, 49,  5, 16, 19,  8, 30,  1, 24, 43,  6, 47, 22, 38,
       14, 28, 33, 39, 23, 10, 20, 41, 12,  4, 35,  7, 54, 52, 45, 18,  9,
       44, 42, 17, 50]), 'cur_cost': 111112.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 0, 36, 29,  2, 13, 11, 34, 37,  3, 46, 31, 25, 53, 40, 51, 21, 26,
       15, 27, 48, 32, 49,  5, 16, 19,  8, 30,  1, 24, 43,  6, 47, 22, 38,
       14, 28, 33, 39, 23, 10, 20, 41, 12,  4, 35,  7, 54, 52, 45, 18,  9,
       44, 42, 17, 50]), 'cur_cost': 111609.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 0, 13, 36, 29,  2, 11, 34, 37,  3, 46, 31, 25, 53, 40, 51, 21, 26,
       15, 27, 48, 32, 49,  5, 16, 19,  8, 30,  1, 24, 43,  6, 47, 22, 38,
       14, 28, 33, 39, 23, 10, 20, 41, 12,  4, 35,  7, 54, 52, 45, 18,  9,
       44, 42, 17, 50]), 'cur_cost': 115805.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:08,998 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 102391.00)
2025-08-05 09:52:08,998 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:08,998 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:09,000 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([46, 33, 50, 38, 31, 28, 51, 32,  0, 19, 12, 10, 34, 42,  5, 22,  9,
       18,  8, 14, 21, 23, 45, 49, 15, 26, 48,  6,  3, 11, 35, 27, 16, 47,
       29, 52, 37,  7, 53, 25,  1, 30, 17, 39,  4, 54, 20, 13,  2, 24, 40,
       43, 36, 41, 44], dtype=int64), 'cur_cost': 100096.0, 'intermediate_solutions': [{'tour': array([10, 27, 32,  3, 33, 37, 35,  4, 45, 39, 50, 34, 15, 26, 46, 16, 36,
       49, 41, 23, 13, 40, 22,  7, 47,  1, 24, 52,  5, 25,  0, 17, 54, 48,
       21,  6, 18, 30, 53,  9, 29,  8, 43, 12, 11, 38, 51, 28,  2, 19, 42,
       44, 20, 14, 31]), 'cur_cost': 114125.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 3, 10, 27, 32, 33, 37, 35,  4, 45, 39, 50, 34, 15, 26, 46, 16, 36,
       49, 41, 23, 13, 40, 22,  7, 47,  1, 24, 52,  5, 25,  0, 17, 54, 48,
       21,  6, 18, 30, 53,  9, 29,  8, 43, 12, 11, 38, 51, 28,  2, 19, 42,
       44, 20, 14, 31]), 'cur_cost': 111672.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([33,  3, 10, 27, 32, 37, 35,  4, 45, 39, 50, 34, 15, 26, 46, 16, 36,
       49, 41, 23, 13, 40, 22,  7, 47,  1, 24, 52,  5, 25,  0, 17, 54, 48,
       21,  6, 18, 30, 53,  9, 29,  8, 43, 12, 11, 38, 51, 28,  2, 19, 42,
       44, 20, 14, 31]), 'cur_cost': 113632.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([32,  3, 10, 27, 33, 37, 35,  4, 45, 39, 50, 34, 15, 26, 46, 16, 36,
       49, 41, 23, 13, 40, 22,  7, 47,  1, 24, 52,  5, 25,  0, 17, 54, 48,
       21,  6, 18, 30, 53,  9, 29,  8, 43, 12, 11, 38, 51, 28,  2, 19, 42,
       44, 20, 14, 31]), 'cur_cost': 111769.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([32, 33,  3, 10, 27, 37, 35,  4, 45, 39, 50, 34, 15, 26, 46, 16, 36,
       49, 41, 23, 13, 40, 22,  7, 47,  1, 24, 52,  5, 25,  0, 17, 54, 48,
       21,  6, 18, 30, 53,  9, 29,  8, 43, 12, 11, 38, 51, 28,  2, 19, 42,
       44, 20, 14, 31]), 'cur_cost': 113639.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 16454.0, 'intermediate_solutions': [{'tour': [51, 50, 15, 18, 49, 54, 26, 29, 4, 9, 25, 39, 36, 23, 37, 8, 35, 38, 24, 28, 6, 33, 34, 3, 10, 31, 43, 22, 2, 0, 12, 45, 19, 48, 47, 44, 52, 14, 13, 17, 11, 27, 40, 41, 30, 5, 32, 53, 46, 20, 16, 21, 1, 7, 42], 'cur_cost': 55254.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [51, 50, 15, 5, 30, 41, 40, 27, 11, 17, 13, 14, 52, 44, 47, 48, 19, 45, 12, 0, 2, 22, 43, 31, 10, 3, 34, 33, 6, 28, 24, 38, 35, 8, 37, 23, 36, 39, 25, 9, 4, 32, 26, 54, 49, 18, 29, 53, 46, 20, 16, 21, 1, 7, 42], 'cur_cost': 59254.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [51, 50, 15, 18, 49, 54, 26, 32, 4, 9, 25, 39, 36, 23, 37, 8, 35, 38, 24, 28, 6, 46, 33, 34, 3, 10, 31, 43, 22, 2, 0, 12, 45, 19, 48, 47, 44, 52, 14, 13, 17, 11, 27, 40, 41, 30, 5, 29, 53, 20, 16, 21, 1, 7, 42], 'cur_cost': 58749.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [15, 46, 53, 23, 36, 1, 7, 38, 29, 32, 10, 22, 39, 40, 33, 26, 25, 3, 28, 5, 37, 44, 16, 11, 21, 18, 48, 54, 12, 52, 30, 27, 0, 4, 2, 24, 41, 42, 49, 19, 51, 45, 50, 17, 31, 6, 43, 8, 9, 34, 47, 14, 13, 20, 35], 'cur_cost': 64551.0, 'intermediate_solutions': [{'tour': [13, 32, 25, 39, 27, 43, 10, 5, 0, 22, 31, 6, 3, 30, 9, 2, 29, 26, 41, 49, 50, 18, 20, 48, 21, 15, 52, 19, 11, 17, 16, 46, 53, 51, 47, 44, 54, 24, 36, 38, 35, 34, 33, 37, 28, 1, 23, 8, 42, 40, 7, 4, 12, 14, 45], 'cur_cost': 46130.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [13, 32, 25, 0, 27, 43, 10, 5, 39, 22, 31, 6, 3, 30, 9, 2, 29, 26, 41, 49, 50, 18, 20, 48, 21, 15, 52, 19, 11, 17, 16, 46, 53, 51, 47, 44, 54, 24, 36, 38, 35, 34, 33, 37, 28, 1, 23, 8, 14, 12, 4, 7, 40, 42, 45], 'cur_cost': 50970.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [13, 32, 25, 0, 27, 12, 43, 10, 5, 39, 22, 31, 6, 3, 30, 9, 2, 29, 26, 41, 49, 50, 18, 20, 48, 21, 15, 52, 19, 11, 17, 16, 46, 53, 51, 47, 44, 54, 24, 36, 38, 35, 34, 33, 37, 28, 1, 23, 8, 42, 40, 7, 4, 14, 45], 'cur_cost': 53443.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 23, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4], 'cur_cost': 11726.0, 'intermediate_solutions': [{'tour': [0, 5, 15, 1, 4, 6, 9, 7, 3, 2, 8, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 13, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 33, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 28675.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 15, 1, 4, 6, 9, 7, 3, 43, 33, 42, 37, 38, 36, 35, 27, 29, 26, 30, 24, 28, 32, 22, 23, 25, 31, 10, 8, 2, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 20945.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 15, 1, 4, 6, 9, 7, 3, 2, 8, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 48, 13, 17, 14, 16, 18, 20, 12, 50, 21, 19, 11], 'cur_cost': 18888.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 14, 1, 4, 9, 7, 3, 5, 8, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 19, 21, 12, 18, 16, 15, 20, 11], 'cur_cost': 16467.0, 'intermediate_solutions': [{'tour': [13, 50, 25, 0, 49, 43, 10, 32, 4, 6, 36, 35, 30, 42, 8, 33, 17, 12, 44, 18, 20, 14, 27, 40, 41, 3, 37, 48, 31, 34, 52, 16, 51, 39, 2, 53, 1, 38, 5, 15, 7, 22, 26, 24, 9, 28, 11, 46, 23, 21, 54, 29, 45, 47, 19], 'cur_cost': 90970.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [13, 50, 25, 0, 49, 43, 10, 32, 4, 6, 36, 35, 30, 42, 8, 3, 41, 40, 27, 14, 20, 15, 44, 12, 17, 33, 37, 48, 31, 34, 52, 16, 51, 39, 2, 53, 1, 38, 5, 18, 7, 22, 26, 24, 9, 28, 11, 46, 23, 21, 54, 29, 45, 47, 19], 'cur_cost': 86510.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [13, 50, 25, 0, 49, 43, 10, 32, 4, 6, 36, 35, 30, 42, 8, 33, 17, 12, 44, 15, 20, 14, 27, 40, 41, 3, 37, 48, 31, 34, 52, 16, 51, 39, 2, 53, 1, 38, 5, 18, 7, 22, 26, 24, 9, 28, 21, 11, 46, 23, 54, 29, 45, 47, 19], 'cur_cost': 89306.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([24, 37,  1, 50, 26, 12, 33, 42,  7, 21, 16, 35,  5, 11, 29, 53, 45,
       49, 28, 17, 41, 46, 22, 47, 13,  9, 51, 32, 36, 18, 27,  0, 43,  6,
       34, 19,  8, 52, 23, 30,  3, 10, 39, 44, 25, 38, 14,  4, 54, 20, 31,
        2, 48, 15, 40], dtype=int64), 'cur_cost': 117290.0, 'intermediate_solutions': [{'tour': array([26, 18, 21, 39,  9, 25,  6, 36, 23,  8, 29, 38, 32, 49,  3, 43,  0,
       12, 31,  2, 53, 44, 14, 48, 11, 34, 24, 41, 28, 50, 16, 42,  7, 35,
       15, 45, 20, 52, 37, 19, 46, 33,  1, 30, 47, 51, 54, 13, 40,  4, 17,
       27, 10, 22,  5]), 'cur_cost': 102176.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([39, 26, 18, 21,  9, 25,  6, 36, 23,  8, 29, 38, 32, 49,  3, 43,  0,
       12, 31,  2, 53, 44, 14, 48, 11, 34, 24, 41, 28, 50, 16, 42,  7, 35,
       15, 45, 20, 52, 37, 19, 46, 33,  1, 30, 47, 51, 54, 13, 40,  4, 17,
       27, 10, 22,  5]), 'cur_cost': 101302.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9, 39, 26, 18, 21, 25,  6, 36, 23,  8, 29, 38, 32, 49,  3, 43,  0,
       12, 31,  2, 53, 44, 14, 48, 11, 34, 24, 41, 28, 50, 16, 42,  7, 35,
       15, 45, 20, 52, 37, 19, 46, 33,  1, 30, 47, 51, 54, 13, 40,  4, 17,
       27, 10, 22,  5]), 'cur_cost': 99484.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([21, 39, 26, 18,  9, 25,  6, 36, 23,  8, 29, 38, 32, 49,  3, 43,  0,
       12, 31,  2, 53, 44, 14, 48, 11, 34, 24, 41, 28, 50, 16, 42,  7, 35,
       15, 45, 20, 52, 37, 19, 46, 33,  1, 30, 47, 51, 54, 13, 40,  4, 17,
       27, 10, 22,  5]), 'cur_cost': 105441.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([21,  9, 39, 26, 18, 25,  6, 36, 23,  8, 29, 38, 32, 49,  3, 43,  0,
       12, 31,  2, 53, 44, 14, 48, 11, 34, 24, 41, 28, 50, 16, 42,  7, 35,
       15, 45, 20, 52, 37, 19, 46, 33,  1, 30, 47, 51, 54, 13, 40,  4, 17,
       27, 10, 22,  5]), 'cur_cost': 105169.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 9, 12, 21, 19, 18, 16, 15, 20, 14, 11, 13, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 3, 5, 6, 4, 1, 2], 'cur_cost': 16395.0, 'intermediate_solutions': [{'tour': [4, 26, 7, 25, 6, 23, 35, 44, 2, 33, 40, 43, 19, 0, 12, 51, 28, 14, 36, 24, 41, 30, 42, 20, 50, 52, 31, 48, 34, 22, 27, 49, 29, 13, 37, 10, 8, 54, 16, 21, 32, 47, 1, 15, 18, 5, 17, 45, 11, 38, 39, 3, 46, 53, 9], 'cur_cost': 99680.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 26, 7, 25, 6, 23, 35, 44, 2, 33, 53, 51, 12, 0, 19, 43, 40, 14, 36, 24, 41, 30, 42, 20, 50, 52, 31, 48, 34, 22, 27, 49, 29, 13, 37, 10, 8, 54, 16, 21, 32, 47, 1, 15, 18, 5, 17, 45, 11, 38, 39, 3, 46, 28, 9], 'cur_cost': 101970.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 26, 7, 25, 6, 23, 35, 44, 2, 33, 40, 43, 19, 0, 12, 51, 53, 14, 36, 24, 41, 30, 42, 20, 50, 52, 31, 48, 34, 22, 27, 49, 29, 13, 37, 10, 8, 54, 16, 21, 38, 32, 47, 1, 15, 18, 5, 17, 45, 11, 39, 3, 46, 28, 9], 'cur_cost': 99035.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 18, 21, 19, 17, 13, 14, 16, 15, 20, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 3, 2, 1, 4, 6], 'cur_cost': 10786.0, 'intermediate_solutions': [{'tour': [49, 27, 40, 14, 15, 0, 20, 8, 23, 37, 17, 13, 44, 48, 9, 51, 3, 6, 31, 33, 32, 43, 38, 28, 12, 54, 18, 21, 34, 1, 45, 53, 47, 50, 16, 30, 2, 4, 25, 19, 41, 5, 7, 52, 36, 29, 26, 11, 39, 24, 46, 10, 22, 42, 35], 'cur_cost': 91530.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [49, 27, 40, 14, 15, 0, 20, 8, 22, 37, 17, 13, 44, 48, 9, 51, 3, 6, 31, 33, 32, 43, 38, 28, 12, 54, 18, 21, 34, 1, 45, 53, 47, 50, 16, 30, 2, 4, 25, 26, 29, 36, 52, 7, 5, 41, 19, 11, 39, 24, 46, 10, 23, 42, 35], 'cur_cost': 86664.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [49, 27, 40, 14, 15, 0, 20, 16, 8, 22, 37, 17, 13, 44, 48, 9, 51, 3, 6, 31, 33, 32, 43, 38, 28, 12, 54, 18, 21, 34, 1, 45, 53, 47, 50, 30, 2, 4, 25, 19, 41, 5, 7, 52, 36, 29, 26, 11, 39, 24, 46, 10, 23, 42, 35], 'cur_cost': 89924.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 8, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 1, 4, 6, 5, 3, 7, 2], 'cur_cost': 10783.0, 'intermediate_solutions': [{'tour': [11, 13, 54, 24, 37, 40, 8, 23, 41, 39, 29, 33, 6, 5, 28, 35, 22, 9, 30, 36, 43, 1, 2, 34, 3, 42, 31, 46, 44, 50, 49, 45, 14, 51, 19, 18, 20, 47, 16, 12, 53, 26, 38, 4, 32, 25, 0, 10, 21, 52, 17, 48, 15, 27, 7], 'cur_cost': 64277.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 13, 54, 24, 37, 40, 8, 23, 41, 39, 29, 33, 6, 5, 28, 35, 22, 9, 30, 36, 43, 1, 2, 34, 3, 42, 31, 46, 44, 50, 49, 45, 14, 51, 53, 18, 20, 47, 16, 12, 19, 26, 38, 4, 32, 25, 17, 52, 21, 10, 0, 48, 15, 27, 7], 'cur_cost': 66563.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 13, 54, 24, 37, 40, 8, 23, 41, 39, 29, 33, 6, 5, 28, 35, 22, 9, 30, 36, 43, 1, 2, 34, 3, 42, 31, 46, 44, 50, 49, 45, 14, 51, 53, 18, 47, 16, 12, 19, 26, 38, 4, 32, 25, 20, 0, 10, 21, 52, 17, 48, 15, 27, 7], 'cur_cost': 67613.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([16, 21, 27, 47, 25, 40,  6,  5, 33, 13, 32, 12, 24, 14, 35,  9, 51,
       42, 50, 41, 48, 17, 22, 10, 31, 49, 26,  8, 53, 52, 30, 11, 23,  1,
       18, 44,  2,  7, 15, 19, 45, 29, 36,  4, 46, 54, 43, 38, 39, 37, 34,
       20,  3,  0, 28], dtype=int64), 'cur_cost': 102391.0, 'intermediate_solutions': [{'tour': array([29,  2,  0, 36, 13, 11, 34, 37,  3, 46, 31, 25, 53, 40, 51, 21, 26,
       15, 27, 48, 32, 49,  5, 16, 19,  8, 30,  1, 24, 43,  6, 47, 22, 38,
       14, 28, 33, 39, 23, 10, 20, 41, 12,  4, 35,  7, 54, 52, 45, 18,  9,
       44, 42, 17, 50]), 'cur_cost': 110245.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([36, 29,  2,  0, 13, 11, 34, 37,  3, 46, 31, 25, 53, 40, 51, 21, 26,
       15, 27, 48, 32, 49,  5, 16, 19,  8, 30,  1, 24, 43,  6, 47, 22, 38,
       14, 28, 33, 39, 23, 10, 20, 41, 12,  4, 35,  7, 54, 52, 45, 18,  9,
       44, 42, 17, 50]), 'cur_cost': 109293.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([13, 36, 29,  2,  0, 11, 34, 37,  3, 46, 31, 25, 53, 40, 51, 21, 26,
       15, 27, 48, 32, 49,  5, 16, 19,  8, 30,  1, 24, 43,  6, 47, 22, 38,
       14, 28, 33, 39, 23, 10, 20, 41, 12,  4, 35,  7, 54, 52, 45, 18,  9,
       44, 42, 17, 50]), 'cur_cost': 111112.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 0, 36, 29,  2, 13, 11, 34, 37,  3, 46, 31, 25, 53, 40, 51, 21, 26,
       15, 27, 48, 32, 49,  5, 16, 19,  8, 30,  1, 24, 43,  6, 47, 22, 38,
       14, 28, 33, 39, 23, 10, 20, 41, 12,  4, 35,  7, 54, 52, 45, 18,  9,
       44, 42, 17, 50]), 'cur_cost': 111609.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 0, 13, 36, 29,  2, 11, 34, 37,  3, 46, 31, 25, 53, 40, 51, 21, 26,
       15, 27, 48, 32, 49,  5, 16, 19,  8, 30,  1, 24, 43,  6, 47, 22, 38,
       14, 28, 33, 39, 23, 10, 20, 41, 12,  4, 35,  7, 54, 52, 45, 18,  9,
       44, 42, 17, 50]), 'cur_cost': 115805.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 09:52:09,001 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:09,001 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:09,004 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10783.000, 多样性=0.922
2025-08-05 09:52:09,005 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 09:52:09,005 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 09:52:09,005 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:09,005 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.23324410872078835, 'best_improvement': 0.3463263821532493}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.05821635012386471}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.130651215611824, 'recent_improvements': [-0.11012289558592812, 0.17048579146305695, -0.37142532680957613], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 6, 'new_count': 6, 'count_change': 0, 'old_best_cost': 10443, 'new_best_cost': 10443, 'quality_improvement': 0.0, 'old_diversity': 0.7139393939393939, 'new_diversity': 0.7139393939393939, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:52:09,006 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 09:52:09,006 - __main__ - INFO - composite10_55 开始进化第 5 代
2025-08-05 09:52:09,006 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 09:52:09,006 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:09,007 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10783.000, 多样性=0.922
2025-08-05 09:52:09,007 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:09,009 - PathExpert - INFO - 路径结构分析完成: 公共边数量=25, 路径相似性=0.922
2025-08-05 09:52:09,010 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:09,011 - EliteExpert - INFO - 精英解分析完成: 精英解数量=6, 多样性=0.714
2025-08-05 09:52:09,013 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 09:52:09,013 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:09,013 - LandscapeExpert - INFO - 添加精英解数据: 6个精英解
2025-08-05 09:52:09,014 - LandscapeExpert - INFO - 数据提取成功: 16个路径, 16个适应度值
2025-08-05 09:52:09,050 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.188, 适应度梯度: -9860.850, 聚类评分: 0.000, 覆盖率: 0.131, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:09,050 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 09:52:09,050 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:09,051 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite10_55
2025-08-05 09:52:09,058 - visualization.landscape_visualizer - INFO - 插值约束: 207 个点被约束到最小值 10443.00
2025-08-05 09:52:09,156 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite10_55\landscape_composite10_55_iter_110_20250805_095209.html
2025-08-05 09:52:09,199 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite10_55\dashboard_composite10_55_iter_110_20250805_095209.html
2025-08-05 09:52:09,199 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 110
2025-08-05 09:52:09,200 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 09:52:09,200 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1876秒
2025-08-05 09:52:09,200 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1875, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -9860.849999999999, 'local_optima_density': 0.1875, 'gradient_variance': 1215552233.4425, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1311, 'fitness_entropy': 0.5931390622295665, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -9860.850)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.131)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358729.0500176, 'performance_metrics': {}}}
2025-08-05 09:52:09,200 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:09,200 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:09,200 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:09,201 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:09,201 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:52:09,201 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 09:52:09,201 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:52:09,202 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:09,202 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:09,202 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:52:09,202 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:09,202 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:09,203 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:09,203 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-05 09:52:09,203 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:09,203 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:09,203 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 105583.0
2025-08-05 09:52:09,211 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 09:52:09,211 - ExploitationExpert - INFO - res_population_costs: [10443, 10443, 10443, 10445.0, 10450, 10451.0]
2025-08-05 09:52:09,211 - ExploitationExpert - INFO - res_populations: [array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  9,  7,  2,  3,
        5,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 43, 39, 34, 40, 41, 38,
       36, 37, 42, 33, 35, 29, 22, 27, 32, 28, 23, 25, 31, 26, 24, 30, 49,
       46, 44, 53, 54, 50, 48, 52, 47, 51, 45, 13, 17, 14, 11, 16, 15, 20,
       18, 12, 21, 19], dtype=int64), array([ 0, 21, 19, 18, 12, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       14, 11, 13, 17, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 14, 11, 13, 17, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 30, 24, 26, 31, 25, 23, 28, 32, 27, 22, 29,
       35, 36, 33, 42, 37, 38, 41, 40, 34, 39, 43, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64)]
2025-08-05 09:52:09,214 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:09,214 - ExploitationExpert - INFO - populations: [{'tour': array([34, 37,  3, 51,  4, 35,  0, 30, 43, 50, 19, 27, 42, 38,  1, 26, 14,
       17, 23, 18, 25, 54, 33, 52, 40, 46,  9, 31, 36, 48, 10, 41, 21, 47,
       13,  7,  6, 44, 28, 24, 22, 49, 45, 16,  8, 39, 12, 53, 32, 15, 29,
       11, 20,  2,  5], dtype=int64), 'cur_cost': 105583.0}, {'tour': [0, 13, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 16454.0}, {'tour': [15, 46, 53, 23, 36, 1, 7, 38, 29, 32, 10, 22, 39, 40, 33, 26, 25, 3, 28, 5, 37, 44, 16, 11, 21, 18, 48, 54, 12, 52, 30, 27, 0, 4, 2, 24, 41, 42, 49, 19, 51, 45, 50, 17, 31, 6, 43, 8, 9, 34, 47, 14, 13, 20, 35], 'cur_cost': 64551.0}, {'tour': [0, 6, 23, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4], 'cur_cost': 11726.0}, {'tour': [0, 6, 14, 1, 4, 9, 7, 3, 5, 8, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 19, 21, 12, 18, 16, 15, 20, 11], 'cur_cost': 16467.0}, {'tour': [24, 37, 1, 50, 26, 12, 33, 42, 7, 21, 16, 35, 5, 11, 29, 53, 45, 49, 28, 17, 41, 46, 22, 47, 13, 9, 51, 32, 36, 18, 27, 0, 43, 6, 34, 19, 8, 52, 23, 30, 3, 10, 39, 44, 25, 38, 14, 4, 54, 20, 31, 2, 48, 15, 40], 'cur_cost': 117290.0}, {'tour': [0, 17, 9, 12, 21, 19, 18, 16, 15, 20, 14, 11, 13, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 3, 5, 6, 4, 1, 2], 'cur_cost': 16395.0}, {'tour': [0, 5, 18, 21, 19, 17, 13, 14, 16, 15, 20, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 3, 2, 1, 4, 6], 'cur_cost': 10786.0}, {'tour': [0, 9, 8, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 1, 4, 6, 5, 3, 7, 2], 'cur_cost': 10783.0}, {'tour': [16, 21, 27, 47, 25, 40, 6, 5, 33, 13, 32, 12, 24, 14, 35, 9, 51, 42, 50, 41, 48, 17, 22, 10, 31, 49, 26, 8, 53, 52, 30, 11, 23, 1, 18, 44, 2, 7, 15, 19, 45, 29, 36, 4, 46, 54, 43, 38, 39, 37, 34, 20, 3, 0, 28], 'cur_cost': 102391.0}]
2025-08-05 09:52:09,215 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:09,215 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 284, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 284, 'cache_hits': 0, 'similarity_calculations': 1411, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:09,216 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([34, 37,  3, 51,  4, 35,  0, 30, 43, 50, 19, 27, 42, 38,  1, 26, 14,
       17, 23, 18, 25, 54, 33, 52, 40, 46,  9, 31, 36, 48, 10, 41, 21, 47,
       13,  7,  6, 44, 28, 24, 22, 49, 45, 16,  8, 39, 12, 53, 32, 15, 29,
       11, 20,  2,  5], dtype=int64), 'cur_cost': 105583.0, 'intermediate_solutions': [{'tour': array([50, 33, 46, 38, 31, 28, 51, 32,  0, 19, 12, 10, 34, 42,  5, 22,  9,
       18,  8, 14, 21, 23, 45, 49, 15, 26, 48,  6,  3, 11, 35, 27, 16, 47,
       29, 52, 37,  7, 53, 25,  1, 30, 17, 39,  4, 54, 20, 13,  2, 24, 40,
       43, 36, 41, 44]), 'cur_cost': 100055.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([38, 50, 33, 46, 31, 28, 51, 32,  0, 19, 12, 10, 34, 42,  5, 22,  9,
       18,  8, 14, 21, 23, 45, 49, 15, 26, 48,  6,  3, 11, 35, 27, 16, 47,
       29, 52, 37,  7, 53, 25,  1, 30, 17, 39,  4, 54, 20, 13,  2, 24, 40,
       43, 36, 41, 44]), 'cur_cost': 103726.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([31, 38, 50, 33, 46, 28, 51, 32,  0, 19, 12, 10, 34, 42,  5, 22,  9,
       18,  8, 14, 21, 23, 45, 49, 15, 26, 48,  6,  3, 11, 35, 27, 16, 47,
       29, 52, 37,  7, 53, 25,  1, 30, 17, 39,  4, 54, 20, 13,  2, 24, 40,
       43, 36, 41, 44]), 'cur_cost': 104001.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([46, 38, 50, 33, 31, 28, 51, 32,  0, 19, 12, 10, 34, 42,  5, 22,  9,
       18,  8, 14, 21, 23, 45, 49, 15, 26, 48,  6,  3, 11, 35, 27, 16, 47,
       29, 52, 37,  7, 53, 25,  1, 30, 17, 39,  4, 54, 20, 13,  2, 24, 40,
       43, 36, 41, 44]), 'cur_cost': 100071.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([46, 31, 38, 50, 33, 28, 51, 32,  0, 19, 12, 10, 34, 42,  5, 22,  9,
       18,  8, 14, 21, 23, 45, 49, 15, 26, 48,  6,  3, 11, 35, 27, 16, 47,
       29, 52, 37,  7, 53, 25,  1, 30, 17, 39,  4, 54, 20, 13,  2, 24, 40,
       43, 36, 41, 44]), 'cur_cost': 100442.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:09,216 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 105583.00)
2025-08-05 09:52:09,216 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:09,216 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:09,216 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:09,220 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 55
2025-08-05 09:52:09,221 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,221 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,221 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,221 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,222 - ExplorationExpert - INFO - 探索路径生成完成，成本: 57929.0, 路径长度: 55, 收集中间解: 3
2025-08-05 09:52:09,222 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [10, 36, 34, 0, 1, 5, 43, 33, 32, 24, 38, 25, 8, 41, 31, 3, 9, 39, 40, 29, 28, 30, 2, 37, 6, 13, 52, 45, 53, 51, 44, 17, 12, 54, 47, 14, 48, 16, 20, 50, 18, 26, 22, 7, 4, 35, 23, 49, 11, 19, 21, 46, 15, 27, 42], 'cur_cost': 57929.0, 'intermediate_solutions': [{'tour': [0, 13, 10, 8, 7, 9, 5, 3, 2, 37, 4, 6, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 1, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 25266.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 14, 17, 48, 50, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 18767.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6, 26, 31, 25, 23, 22, 32, 28, 16, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 17, 14, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 21352.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:09,222 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 57929.00)
2025-08-05 09:52:09,222 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:09,223 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:09,223 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:09,225 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 09:52:09,225 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,225 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,225 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,226 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,226 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17027.0, 路径长度: 55, 收集中间解: 3
2025-08-05 09:52:09,226 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 15, 13, 22, 23, 25, 31, 26, 24, 30, 28, 32, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6, 21, 19, 17, 14, 16, 18, 20, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 17027.0, 'intermediate_solutions': [{'tour': [15, 46, 53, 23, 36, 1, 7, 38, 29, 32, 10, 22, 39, 40, 33, 26, 25, 3, 4, 5, 37, 44, 16, 11, 21, 18, 48, 54, 12, 52, 30, 27, 0, 28, 2, 24, 41, 42, 49, 19, 51, 45, 50, 17, 31, 6, 43, 8, 9, 34, 47, 14, 13, 20, 35], 'cur_cost': 64491.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [15, 46, 53, 23, 36, 1, 7, 38, 29, 32, 10, 22, 39, 40, 33, 26, 25, 3, 28, 5, 37, 44, 16, 11, 21, 18, 48, 54, 12, 52, 30, 27, 0, 4, 2, 24, 47, 34, 9, 8, 43, 6, 31, 17, 50, 45, 51, 19, 49, 42, 41, 14, 13, 20, 35], 'cur_cost': 67738.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [15, 21, 46, 53, 23, 36, 1, 7, 38, 29, 32, 10, 22, 39, 40, 33, 26, 25, 3, 28, 5, 37, 44, 16, 11, 18, 48, 54, 12, 52, 30, 27, 0, 4, 2, 24, 41, 42, 49, 19, 51, 45, 50, 17, 31, 6, 43, 8, 9, 34, 47, 14, 13, 20, 35], 'cur_cost': 64539.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:09,226 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 17027.00)
2025-08-05 09:52:09,226 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:52:09,226 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:52:09,226 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:09,228 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 09:52:09,228 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,229 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,229 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,229 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,229 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10695.0, 路径长度: 55, 收集中间解: 3
2025-08-05 09:52:09,229 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 17, 12, 21, 19, 18, 16, 15, 20, 14, 11, 13, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 10695.0, 'intermediate_solutions': [{'tour': [0, 6, 23, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 42, 46, 49, 30, 24, 26, 31, 25, 22, 32, 28, 27, 29, 35, 36, 38, 37, 53, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4], 'cur_cost': 22810.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 23, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 22, 32, 28, 27, 29, 35, 41, 40, 34, 39, 43, 33, 42, 37, 38, 36, 10, 8, 7, 9, 5, 3, 2, 1, 4], 'cur_cost': 11722.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 23, 13, 17, 14, 16, 15, 20, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 18, 49, 30, 24, 26, 31, 25, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4], 'cur_cost': 14204.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:09,230 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 10695.00)
2025-08-05 09:52:09,230 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:09,230 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:09,230 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:09,231 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 55
2025-08-05 09:52:09,231 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,232 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,232 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,232 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,232 - ExplorationExpert - INFO - 探索路径生成完成，成本: 86581.0, 路径长度: 55, 收集中间解: 3
2025-08-05 09:52:09,232 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [36, 13, 23, 25, 24, 14, 31, 9, 54, 43, 39, 37, 52, 45, 53, 2, 6, 44, 16, 50, 12, 11, 35, 41, 4, 5, 1, 3, 21, 32, 26, 28, 42, 20, 18, 40, 38, 22, 47, 49, 7, 46, 19, 29, 15, 48, 30, 27, 10, 51, 8, 0, 33, 17, 34], 'cur_cost': 86581.0, 'intermediate_solutions': [{'tour': [0, 6, 14, 1, 4, 9, 7, 3, 5, 8, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 50, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 27, 48, 13, 17, 19, 21, 12, 18, 16, 15, 20, 11], 'cur_cost': 24256.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 14, 1, 4, 9, 7, 3, 5, 8, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 15, 16, 18, 12, 21, 19, 20, 11], 'cur_cost': 16548.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 14, 1, 4, 9, 7, 3, 5, 8, 2, 38, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 19, 21, 12, 18, 16, 15, 20, 11], 'cur_cost': 20824.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:09,233 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 86581.00)
2025-08-05 09:52:09,233 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 09:52:09,233 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:09,233 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:09,233 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 85852.0
2025-08-05 09:52:09,240 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 09:52:09,240 - ExploitationExpert - INFO - res_population_costs: [10443, 10443, 10443, 10445.0, 10450, 10451.0]
2025-08-05 09:52:09,240 - ExploitationExpert - INFO - res_populations: [array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  9,  7,  2,  3,
        5,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 43, 39, 34, 40, 41, 38,
       36, 37, 42, 33, 35, 29, 22, 27, 32, 28, 23, 25, 31, 26, 24, 30, 49,
       46, 44, 53, 54, 50, 48, 52, 47, 51, 45, 13, 17, 14, 11, 16, 15, 20,
       18, 12, 21, 19], dtype=int64), array([ 0, 21, 19, 18, 12, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       14, 11, 13, 17, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 14, 11, 13, 17, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 30, 24, 26, 31, 25, 23, 28, 32, 27, 22, 29,
       35, 36, 33, 42, 37, 38, 41, 40, 34, 39, 43, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64)]
2025-08-05 09:52:09,242 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:09,242 - ExploitationExpert - INFO - populations: [{'tour': array([34, 37,  3, 51,  4, 35,  0, 30, 43, 50, 19, 27, 42, 38,  1, 26, 14,
       17, 23, 18, 25, 54, 33, 52, 40, 46,  9, 31, 36, 48, 10, 41, 21, 47,
       13,  7,  6, 44, 28, 24, 22, 49, 45, 16,  8, 39, 12, 53, 32, 15, 29,
       11, 20,  2,  5], dtype=int64), 'cur_cost': 105583.0}, {'tour': [10, 36, 34, 0, 1, 5, 43, 33, 32, 24, 38, 25, 8, 41, 31, 3, 9, 39, 40, 29, 28, 30, 2, 37, 6, 13, 52, 45, 53, 51, 44, 17, 12, 54, 47, 14, 48, 16, 20, 50, 18, 26, 22, 7, 4, 35, 23, 49, 11, 19, 21, 46, 15, 27, 42], 'cur_cost': 57929.0}, {'tour': [0, 15, 13, 22, 23, 25, 31, 26, 24, 30, 28, 32, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6, 21, 19, 17, 14, 16, 18, 20, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 17027.0}, {'tour': [0, 17, 12, 21, 19, 18, 16, 15, 20, 14, 11, 13, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 10695.0}, {'tour': [36, 13, 23, 25, 24, 14, 31, 9, 54, 43, 39, 37, 52, 45, 53, 2, 6, 44, 16, 50, 12, 11, 35, 41, 4, 5, 1, 3, 21, 32, 26, 28, 42, 20, 18, 40, 38, 22, 47, 49, 7, 46, 19, 29, 15, 48, 30, 27, 10, 51, 8, 0, 33, 17, 34], 'cur_cost': 86581.0}, {'tour': array([10, 26, 15, 28, 50, 20, 19,  5, 12, 46, 34, 39, 36, 43, 22, 29,  9,
       35, 31, 32,  7, 45, 17, 48, 18, 49,  0, 53, 27,  4, 38, 42,  2, 47,
       14, 11, 30, 37, 23, 24, 16, 44, 21,  8, 54, 51, 25, 33, 41, 52, 40,
        3,  6, 13,  1], dtype=int64), 'cur_cost': 85852.0}, {'tour': [0, 17, 9, 12, 21, 19, 18, 16, 15, 20, 14, 11, 13, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 3, 5, 6, 4, 1, 2], 'cur_cost': 16395.0}, {'tour': [0, 5, 18, 21, 19, 17, 13, 14, 16, 15, 20, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 3, 2, 1, 4, 6], 'cur_cost': 10786.0}, {'tour': [0, 9, 8, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 1, 4, 6, 5, 3, 7, 2], 'cur_cost': 10783.0}, {'tour': [16, 21, 27, 47, 25, 40, 6, 5, 33, 13, 32, 12, 24, 14, 35, 9, 51, 42, 50, 41, 48, 17, 22, 10, 31, 49, 26, 8, 53, 52, 30, 11, 23, 1, 18, 44, 2, 7, 15, 19, 45, 29, 36, 4, 46, 54, 43, 38, 39, 37, 34, 20, 3, 0, 28], 'cur_cost': 102391.0}]
2025-08-05 09:52:09,243 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:09,243 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 285, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 285, 'cache_hits': 0, 'similarity_calculations': 1422, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:09,244 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([10, 26, 15, 28, 50, 20, 19,  5, 12, 46, 34, 39, 36, 43, 22, 29,  9,
       35, 31, 32,  7, 45, 17, 48, 18, 49,  0, 53, 27,  4, 38, 42,  2, 47,
       14, 11, 30, 37, 23, 24, 16, 44, 21,  8, 54, 51, 25, 33, 41, 52, 40,
        3,  6, 13,  1], dtype=int64), 'cur_cost': 85852.0, 'intermediate_solutions': [{'tour': array([ 1, 37, 24, 50, 26, 12, 33, 42,  7, 21, 16, 35,  5, 11, 29, 53, 45,
       49, 28, 17, 41, 46, 22, 47, 13,  9, 51, 32, 36, 18, 27,  0, 43,  6,
       34, 19,  8, 52, 23, 30,  3, 10, 39, 44, 25, 38, 14,  4, 54, 20, 31,
        2, 48, 15, 40]), 'cur_cost': 117317.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([50,  1, 37, 24, 26, 12, 33, 42,  7, 21, 16, 35,  5, 11, 29, 53, 45,
       49, 28, 17, 41, 46, 22, 47, 13,  9, 51, 32, 36, 18, 27,  0, 43,  6,
       34, 19,  8, 52, 23, 30,  3, 10, 39, 44, 25, 38, 14,  4, 54, 20, 31,
        2, 48, 15, 40]), 'cur_cost': 116888.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([26, 50,  1, 37, 24, 12, 33, 42,  7, 21, 16, 35,  5, 11, 29, 53, 45,
       49, 28, 17, 41, 46, 22, 47, 13,  9, 51, 32, 36, 18, 27,  0, 43,  6,
       34, 19,  8, 52, 23, 30,  3, 10, 39, 44, 25, 38, 14,  4, 54, 20, 31,
        2, 48, 15, 40]), 'cur_cost': 117260.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([24, 50,  1, 37, 26, 12, 33, 42,  7, 21, 16, 35,  5, 11, 29, 53, 45,
       49, 28, 17, 41, 46, 22, 47, 13,  9, 51, 32, 36, 18, 27,  0, 43,  6,
       34, 19,  8, 52, 23, 30,  3, 10, 39, 44, 25, 38, 14,  4, 54, 20, 31,
        2, 48, 15, 40]), 'cur_cost': 117251.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([24, 26, 50,  1, 37, 12, 33, 42,  7, 21, 16, 35,  5, 11, 29, 53, 45,
       49, 28, 17, 41, 46, 22, 47, 13,  9, 51, 32, 36, 18, 27,  0, 43,  6,
       34, 19,  8, 52, 23, 30,  3, 10, 39, 44, 25, 38, 14,  4, 54, 20, 31,
        2, 48, 15, 40]), 'cur_cost': 117198.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:09,244 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 85852.00)
2025-08-05 09:52:09,244 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:09,244 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:09,245 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:09,246 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 09:52:09,246 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,246 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,247 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,247 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,247 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11892.0, 路径长度: 55, 收集中间解: 3
2025-08-05 09:52:09,247 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 3, 22, 14, 16, 15, 20, 18, 12, 21, 19, 17, 13, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 29, 27, 32, 28, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 6, 4, 1, 2], 'cur_cost': 11892.0, 'intermediate_solutions': [{'tour': [0, 17, 9, 12, 21, 19, 18, 16, 15, 20, 14, 11, 13, 51, 45, 52, 50, 47, 48, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 3, 5, 6, 4, 1, 2], 'cur_cost': 16430.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 9, 12, 21, 19, 18, 16, 15, 20, 14, 11, 13, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 38, 36, 35, 29, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 3, 5, 6, 4, 1, 2], 'cur_cost': 18572.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 9, 12, 21, 19, 18, 16, 15, 20, 14, 11, 13, 51, 45, 52, 50, 29, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 3, 5, 6, 4, 1, 2], 'cur_cost': 20502.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:09,247 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 11892.00)
2025-08-05 09:52:09,247 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:09,247 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:09,248 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:09,249 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 09:52:09,249 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,249 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,250 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,250 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,250 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10745.0, 路径长度: 55, 收集中间解: 3
2025-08-05 09:52:09,250 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 5, 1, 20, 15, 16, 18, 12, 21, 19, 17, 13, 14, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 3, 2, 6, 4], 'cur_cost': 10745.0, 'intermediate_solutions': [{'tour': [0, 5, 18, 21, 19, 17, 13, 14, 16, 15, 9, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 20, 3, 2, 1, 4, 6], 'cur_cost': 22349.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 18, 21, 19, 17, 16, 14, 13, 15, 20, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 3, 2, 1, 4, 6], 'cur_cost': 10861.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 18, 21, 19, 17, 13, 14, 16, 15, 20, 12, 11, 51, 45, 52, 50, 6, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 3, 2, 1, 4], 'cur_cost': 16740.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:09,250 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 10745.00)
2025-08-05 09:52:09,250 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:52:09,250 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:52:09,251 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:09,252 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 09:52:09,252 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,252 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,252 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,253 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,253 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10712.0, 路径长度: 55, 收集中间解: 3
2025-08-05 09:52:09,253 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 4, 12, 18, 16, 15, 20, 14, 11, 13, 17, 19, 21, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 6], 'cur_cost': 10712.0, 'intermediate_solutions': [{'tour': [0, 9, 8, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 30, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 11, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 1, 4, 6, 5, 3, 7, 2], 'cur_cost': 15702.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 8, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 24, 30, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 1, 4, 6, 5, 3, 7, 2], 'cur_cost': 10817.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 8, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 36, 38, 37, 42, 33, 43, 39, 34, 35, 40, 41, 10, 1, 4, 6, 5, 3, 7, 2], 'cur_cost': 10911.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:09,253 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 10712.00)
2025-08-05 09:52:09,253 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 09:52:09,253 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:09,253 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:09,254 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 99887.0
2025-08-05 09:52:09,262 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 09:52:09,262 - ExploitationExpert - INFO - res_population_costs: [10443, 10443, 10443, 10445.0, 10450, 10451.0]
2025-08-05 09:52:09,262 - ExploitationExpert - INFO - res_populations: [array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  9,  7,  2,  3,
        5,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 43, 39, 34, 40, 41, 38,
       36, 37, 42, 33, 35, 29, 22, 27, 32, 28, 23, 25, 31, 26, 24, 30, 49,
       46, 44, 53, 54, 50, 48, 52, 47, 51, 45, 13, 17, 14, 11, 16, 15, 20,
       18, 12, 21, 19], dtype=int64), array([ 0, 21, 19, 18, 12, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       14, 11, 13, 17, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 14, 11, 13, 17, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 30, 24, 26, 31, 25, 23, 28, 32, 27, 22, 29,
       35, 36, 33, 42, 37, 38, 41, 40, 34, 39, 43, 10,  8,  7,  2,  3,  5,
        9,  6,  4,  1], dtype=int64)]
2025-08-05 09:52:09,264 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:09,264 - ExploitationExpert - INFO - populations: [{'tour': array([34, 37,  3, 51,  4, 35,  0, 30, 43, 50, 19, 27, 42, 38,  1, 26, 14,
       17, 23, 18, 25, 54, 33, 52, 40, 46,  9, 31, 36, 48, 10, 41, 21, 47,
       13,  7,  6, 44, 28, 24, 22, 49, 45, 16,  8, 39, 12, 53, 32, 15, 29,
       11, 20,  2,  5], dtype=int64), 'cur_cost': 105583.0}, {'tour': [10, 36, 34, 0, 1, 5, 43, 33, 32, 24, 38, 25, 8, 41, 31, 3, 9, 39, 40, 29, 28, 30, 2, 37, 6, 13, 52, 45, 53, 51, 44, 17, 12, 54, 47, 14, 48, 16, 20, 50, 18, 26, 22, 7, 4, 35, 23, 49, 11, 19, 21, 46, 15, 27, 42], 'cur_cost': 57929.0}, {'tour': [0, 15, 13, 22, 23, 25, 31, 26, 24, 30, 28, 32, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6, 21, 19, 17, 14, 16, 18, 20, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 17027.0}, {'tour': [0, 17, 12, 21, 19, 18, 16, 15, 20, 14, 11, 13, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 10695.0}, {'tour': [36, 13, 23, 25, 24, 14, 31, 9, 54, 43, 39, 37, 52, 45, 53, 2, 6, 44, 16, 50, 12, 11, 35, 41, 4, 5, 1, 3, 21, 32, 26, 28, 42, 20, 18, 40, 38, 22, 47, 49, 7, 46, 19, 29, 15, 48, 30, 27, 10, 51, 8, 0, 33, 17, 34], 'cur_cost': 86581.0}, {'tour': array([10, 26, 15, 28, 50, 20, 19,  5, 12, 46, 34, 39, 36, 43, 22, 29,  9,
       35, 31, 32,  7, 45, 17, 48, 18, 49,  0, 53, 27,  4, 38, 42,  2, 47,
       14, 11, 30, 37, 23, 24, 16, 44, 21,  8, 54, 51, 25, 33, 41, 52, 40,
        3,  6, 13,  1], dtype=int64), 'cur_cost': 85852.0}, {'tour': [0, 3, 22, 14, 16, 15, 20, 18, 12, 21, 19, 17, 13, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 29, 27, 32, 28, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 6, 4, 1, 2], 'cur_cost': 11892.0}, {'tour': [0, 5, 1, 20, 15, 16, 18, 12, 21, 19, 17, 13, 14, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 3, 2, 6, 4], 'cur_cost': 10745.0}, {'tour': [0, 4, 12, 18, 16, 15, 20, 14, 11, 13, 17, 19, 21, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 6], 'cur_cost': 10712.0}, {'tour': array([20, 41, 21,  3, 51, 35, 37, 54, 24, 15, 23,  0, 40, 32, 25,  6, 47,
       43, 10, 36, 19,  4, 50, 49, 11, 12, 13, 28, 45, 44, 52, 17, 46, 34,
       26, 39, 18, 48, 29,  2, 22, 38, 53,  1, 31, 14, 30, 16, 33, 42, 27,
        7,  9,  5,  8], dtype=int64), 'cur_cost': 99887.0}]
2025-08-05 09:52:09,266 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:09,266 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 286, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 286, 'cache_hits': 0, 'similarity_calculations': 1434, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:09,267 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([20, 41, 21,  3, 51, 35, 37, 54, 24, 15, 23,  0, 40, 32, 25,  6, 47,
       43, 10, 36, 19,  4, 50, 49, 11, 12, 13, 28, 45, 44, 52, 17, 46, 34,
       26, 39, 18, 48, 29,  2, 22, 38, 53,  1, 31, 14, 30, 16, 33, 42, 27,
        7,  9,  5,  8], dtype=int64), 'cur_cost': 99887.0, 'intermediate_solutions': [{'tour': array([27, 21, 16, 47, 25, 40,  6,  5, 33, 13, 32, 12, 24, 14, 35,  9, 51,
       42, 50, 41, 48, 17, 22, 10, 31, 49, 26,  8, 53, 52, 30, 11, 23,  1,
       18, 44,  2,  7, 15, 19, 45, 29, 36,  4, 46, 54, 43, 38, 39, 37, 34,
       20,  3,  0, 28]), 'cur_cost': 99123.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([47, 27, 21, 16, 25, 40,  6,  5, 33, 13, 32, 12, 24, 14, 35,  9, 51,
       42, 50, 41, 48, 17, 22, 10, 31, 49, 26,  8, 53, 52, 30, 11, 23,  1,
       18, 44,  2,  7, 15, 19, 45, 29, 36,  4, 46, 54, 43, 38, 39, 37, 34,
       20,  3,  0, 28]), 'cur_cost': 102377.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([25, 47, 27, 21, 16, 40,  6,  5, 33, 13, 32, 12, 24, 14, 35,  9, 51,
       42, 50, 41, 48, 17, 22, 10, 31, 49, 26,  8, 53, 52, 30, 11, 23,  1,
       18, 44,  2,  7, 15, 19, 45, 29, 36,  4, 46, 54, 43, 38, 39, 37, 34,
       20,  3,  0, 28]), 'cur_cost': 102356.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([16, 47, 27, 21, 25, 40,  6,  5, 33, 13, 32, 12, 24, 14, 35,  9, 51,
       42, 50, 41, 48, 17, 22, 10, 31, 49, 26,  8, 53, 52, 30, 11, 23,  1,
       18, 44,  2,  7, 15, 19, 45, 29, 36,  4, 46, 54, 43, 38, 39, 37, 34,
       20,  3,  0, 28]), 'cur_cost': 103927.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([16, 25, 47, 27, 21, 40,  6,  5, 33, 13, 32, 12, 24, 14, 35,  9, 51,
       42, 50, 41, 48, 17, 22, 10, 31, 49, 26,  8, 53, 52, 30, 11, 23,  1,
       18, 44,  2,  7, 15, 19, 45, 29, 36,  4, 46, 54, 43, 38, 39, 37, 34,
       20,  3,  0, 28]), 'cur_cost': 107194.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:09,267 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 99887.00)
2025-08-05 09:52:09,267 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:09,267 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:09,270 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([34, 37,  3, 51,  4, 35,  0, 30, 43, 50, 19, 27, 42, 38,  1, 26, 14,
       17, 23, 18, 25, 54, 33, 52, 40, 46,  9, 31, 36, 48, 10, 41, 21, 47,
       13,  7,  6, 44, 28, 24, 22, 49, 45, 16,  8, 39, 12, 53, 32, 15, 29,
       11, 20,  2,  5], dtype=int64), 'cur_cost': 105583.0, 'intermediate_solutions': [{'tour': array([50, 33, 46, 38, 31, 28, 51, 32,  0, 19, 12, 10, 34, 42,  5, 22,  9,
       18,  8, 14, 21, 23, 45, 49, 15, 26, 48,  6,  3, 11, 35, 27, 16, 47,
       29, 52, 37,  7, 53, 25,  1, 30, 17, 39,  4, 54, 20, 13,  2, 24, 40,
       43, 36, 41, 44]), 'cur_cost': 100055.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([38, 50, 33, 46, 31, 28, 51, 32,  0, 19, 12, 10, 34, 42,  5, 22,  9,
       18,  8, 14, 21, 23, 45, 49, 15, 26, 48,  6,  3, 11, 35, 27, 16, 47,
       29, 52, 37,  7, 53, 25,  1, 30, 17, 39,  4, 54, 20, 13,  2, 24, 40,
       43, 36, 41, 44]), 'cur_cost': 103726.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([31, 38, 50, 33, 46, 28, 51, 32,  0, 19, 12, 10, 34, 42,  5, 22,  9,
       18,  8, 14, 21, 23, 45, 49, 15, 26, 48,  6,  3, 11, 35, 27, 16, 47,
       29, 52, 37,  7, 53, 25,  1, 30, 17, 39,  4, 54, 20, 13,  2, 24, 40,
       43, 36, 41, 44]), 'cur_cost': 104001.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([46, 38, 50, 33, 31, 28, 51, 32,  0, 19, 12, 10, 34, 42,  5, 22,  9,
       18,  8, 14, 21, 23, 45, 49, 15, 26, 48,  6,  3, 11, 35, 27, 16, 47,
       29, 52, 37,  7, 53, 25,  1, 30, 17, 39,  4, 54, 20, 13,  2, 24, 40,
       43, 36, 41, 44]), 'cur_cost': 100071.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([46, 31, 38, 50, 33, 28, 51, 32,  0, 19, 12, 10, 34, 42,  5, 22,  9,
       18,  8, 14, 21, 23, 45, 49, 15, 26, 48,  6,  3, 11, 35, 27, 16, 47,
       29, 52, 37,  7, 53, 25,  1, 30, 17, 39,  4, 54, 20, 13,  2, 24, 40,
       43, 36, 41, 44]), 'cur_cost': 100442.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [10, 36, 34, 0, 1, 5, 43, 33, 32, 24, 38, 25, 8, 41, 31, 3, 9, 39, 40, 29, 28, 30, 2, 37, 6, 13, 52, 45, 53, 51, 44, 17, 12, 54, 47, 14, 48, 16, 20, 50, 18, 26, 22, 7, 4, 35, 23, 49, 11, 19, 21, 46, 15, 27, 42], 'cur_cost': 57929.0, 'intermediate_solutions': [{'tour': [0, 13, 10, 8, 7, 9, 5, 3, 2, 37, 4, 6, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 1, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 25266.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 14, 17, 48, 50, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 18767.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6, 26, 31, 25, 23, 22, 32, 28, 16, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 17, 14, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 21352.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 13, 22, 23, 25, 31, 26, 24, 30, 28, 32, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6, 21, 19, 17, 14, 16, 18, 20, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 17027.0, 'intermediate_solutions': [{'tour': [15, 46, 53, 23, 36, 1, 7, 38, 29, 32, 10, 22, 39, 40, 33, 26, 25, 3, 4, 5, 37, 44, 16, 11, 21, 18, 48, 54, 12, 52, 30, 27, 0, 28, 2, 24, 41, 42, 49, 19, 51, 45, 50, 17, 31, 6, 43, 8, 9, 34, 47, 14, 13, 20, 35], 'cur_cost': 64491.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [15, 46, 53, 23, 36, 1, 7, 38, 29, 32, 10, 22, 39, 40, 33, 26, 25, 3, 28, 5, 37, 44, 16, 11, 21, 18, 48, 54, 12, 52, 30, 27, 0, 4, 2, 24, 47, 34, 9, 8, 43, 6, 31, 17, 50, 45, 51, 19, 49, 42, 41, 14, 13, 20, 35], 'cur_cost': 67738.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [15, 21, 46, 53, 23, 36, 1, 7, 38, 29, 32, 10, 22, 39, 40, 33, 26, 25, 3, 28, 5, 37, 44, 16, 11, 18, 48, 54, 12, 52, 30, 27, 0, 4, 2, 24, 41, 42, 49, 19, 51, 45, 50, 17, 31, 6, 43, 8, 9, 34, 47, 14, 13, 20, 35], 'cur_cost': 64539.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 12, 21, 19, 18, 16, 15, 20, 14, 11, 13, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 10695.0, 'intermediate_solutions': [{'tour': [0, 6, 23, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 42, 46, 49, 30, 24, 26, 31, 25, 22, 32, 28, 27, 29, 35, 36, 38, 37, 53, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4], 'cur_cost': 22810.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 23, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 22, 32, 28, 27, 29, 35, 41, 40, 34, 39, 43, 33, 42, 37, 38, 36, 10, 8, 7, 9, 5, 3, 2, 1, 4], 'cur_cost': 11722.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 23, 13, 17, 14, 16, 15, 20, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 18, 49, 30, 24, 26, 31, 25, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4], 'cur_cost': 14204.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [36, 13, 23, 25, 24, 14, 31, 9, 54, 43, 39, 37, 52, 45, 53, 2, 6, 44, 16, 50, 12, 11, 35, 41, 4, 5, 1, 3, 21, 32, 26, 28, 42, 20, 18, 40, 38, 22, 47, 49, 7, 46, 19, 29, 15, 48, 30, 27, 10, 51, 8, 0, 33, 17, 34], 'cur_cost': 86581.0, 'intermediate_solutions': [{'tour': [0, 6, 14, 1, 4, 9, 7, 3, 5, 8, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 50, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 27, 48, 13, 17, 19, 21, 12, 18, 16, 15, 20, 11], 'cur_cost': 24256.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 14, 1, 4, 9, 7, 3, 5, 8, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 15, 16, 18, 12, 21, 19, 20, 11], 'cur_cost': 16548.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 14, 1, 4, 9, 7, 3, 5, 8, 2, 38, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 19, 21, 12, 18, 16, 15, 20, 11], 'cur_cost': 20824.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([10, 26, 15, 28, 50, 20, 19,  5, 12, 46, 34, 39, 36, 43, 22, 29,  9,
       35, 31, 32,  7, 45, 17, 48, 18, 49,  0, 53, 27,  4, 38, 42,  2, 47,
       14, 11, 30, 37, 23, 24, 16, 44, 21,  8, 54, 51, 25, 33, 41, 52, 40,
        3,  6, 13,  1], dtype=int64), 'cur_cost': 85852.0, 'intermediate_solutions': [{'tour': array([ 1, 37, 24, 50, 26, 12, 33, 42,  7, 21, 16, 35,  5, 11, 29, 53, 45,
       49, 28, 17, 41, 46, 22, 47, 13,  9, 51, 32, 36, 18, 27,  0, 43,  6,
       34, 19,  8, 52, 23, 30,  3, 10, 39, 44, 25, 38, 14,  4, 54, 20, 31,
        2, 48, 15, 40]), 'cur_cost': 117317.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([50,  1, 37, 24, 26, 12, 33, 42,  7, 21, 16, 35,  5, 11, 29, 53, 45,
       49, 28, 17, 41, 46, 22, 47, 13,  9, 51, 32, 36, 18, 27,  0, 43,  6,
       34, 19,  8, 52, 23, 30,  3, 10, 39, 44, 25, 38, 14,  4, 54, 20, 31,
        2, 48, 15, 40]), 'cur_cost': 116888.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([26, 50,  1, 37, 24, 12, 33, 42,  7, 21, 16, 35,  5, 11, 29, 53, 45,
       49, 28, 17, 41, 46, 22, 47, 13,  9, 51, 32, 36, 18, 27,  0, 43,  6,
       34, 19,  8, 52, 23, 30,  3, 10, 39, 44, 25, 38, 14,  4, 54, 20, 31,
        2, 48, 15, 40]), 'cur_cost': 117260.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([24, 50,  1, 37, 26, 12, 33, 42,  7, 21, 16, 35,  5, 11, 29, 53, 45,
       49, 28, 17, 41, 46, 22, 47, 13,  9, 51, 32, 36, 18, 27,  0, 43,  6,
       34, 19,  8, 52, 23, 30,  3, 10, 39, 44, 25, 38, 14,  4, 54, 20, 31,
        2, 48, 15, 40]), 'cur_cost': 117251.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([24, 26, 50,  1, 37, 12, 33, 42,  7, 21, 16, 35,  5, 11, 29, 53, 45,
       49, 28, 17, 41, 46, 22, 47, 13,  9, 51, 32, 36, 18, 27,  0, 43,  6,
       34, 19,  8, 52, 23, 30,  3, 10, 39, 44, 25, 38, 14,  4, 54, 20, 31,
        2, 48, 15, 40]), 'cur_cost': 117198.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 22, 14, 16, 15, 20, 18, 12, 21, 19, 17, 13, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 29, 27, 32, 28, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 6, 4, 1, 2], 'cur_cost': 11892.0, 'intermediate_solutions': [{'tour': [0, 17, 9, 12, 21, 19, 18, 16, 15, 20, 14, 11, 13, 51, 45, 52, 50, 47, 48, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 3, 5, 6, 4, 1, 2], 'cur_cost': 16430.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 9, 12, 21, 19, 18, 16, 15, 20, 14, 11, 13, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 38, 36, 35, 29, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 3, 5, 6, 4, 1, 2], 'cur_cost': 18572.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 9, 12, 21, 19, 18, 16, 15, 20, 14, 11, 13, 51, 45, 52, 50, 29, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 3, 5, 6, 4, 1, 2], 'cur_cost': 20502.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 1, 20, 15, 16, 18, 12, 21, 19, 17, 13, 14, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 3, 2, 6, 4], 'cur_cost': 10745.0, 'intermediate_solutions': [{'tour': [0, 5, 18, 21, 19, 17, 13, 14, 16, 15, 9, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 20, 3, 2, 1, 4, 6], 'cur_cost': 22349.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 18, 21, 19, 17, 16, 14, 13, 15, 20, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 3, 2, 1, 4, 6], 'cur_cost': 10861.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 18, 21, 19, 17, 13, 14, 16, 15, 20, 12, 11, 51, 45, 52, 50, 6, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 3, 2, 1, 4], 'cur_cost': 16740.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 12, 18, 16, 15, 20, 14, 11, 13, 17, 19, 21, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 6], 'cur_cost': 10712.0, 'intermediate_solutions': [{'tour': [0, 9, 8, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 30, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 11, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 1, 4, 6, 5, 3, 7, 2], 'cur_cost': 15702.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 8, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 24, 30, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 1, 4, 6, 5, 3, 7, 2], 'cur_cost': 10817.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 8, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 36, 38, 37, 42, 33, 43, 39, 34, 35, 40, 41, 10, 1, 4, 6, 5, 3, 7, 2], 'cur_cost': 10911.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([20, 41, 21,  3, 51, 35, 37, 54, 24, 15, 23,  0, 40, 32, 25,  6, 47,
       43, 10, 36, 19,  4, 50, 49, 11, 12, 13, 28, 45, 44, 52, 17, 46, 34,
       26, 39, 18, 48, 29,  2, 22, 38, 53,  1, 31, 14, 30, 16, 33, 42, 27,
        7,  9,  5,  8], dtype=int64), 'cur_cost': 99887.0, 'intermediate_solutions': [{'tour': array([27, 21, 16, 47, 25, 40,  6,  5, 33, 13, 32, 12, 24, 14, 35,  9, 51,
       42, 50, 41, 48, 17, 22, 10, 31, 49, 26,  8, 53, 52, 30, 11, 23,  1,
       18, 44,  2,  7, 15, 19, 45, 29, 36,  4, 46, 54, 43, 38, 39, 37, 34,
       20,  3,  0, 28]), 'cur_cost': 99123.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([47, 27, 21, 16, 25, 40,  6,  5, 33, 13, 32, 12, 24, 14, 35,  9, 51,
       42, 50, 41, 48, 17, 22, 10, 31, 49, 26,  8, 53, 52, 30, 11, 23,  1,
       18, 44,  2,  7, 15, 19, 45, 29, 36,  4, 46, 54, 43, 38, 39, 37, 34,
       20,  3,  0, 28]), 'cur_cost': 102377.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([25, 47, 27, 21, 16, 40,  6,  5, 33, 13, 32, 12, 24, 14, 35,  9, 51,
       42, 50, 41, 48, 17, 22, 10, 31, 49, 26,  8, 53, 52, 30, 11, 23,  1,
       18, 44,  2,  7, 15, 19, 45, 29, 36,  4, 46, 54, 43, 38, 39, 37, 34,
       20,  3,  0, 28]), 'cur_cost': 102356.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([16, 47, 27, 21, 25, 40,  6,  5, 33, 13, 32, 12, 24, 14, 35,  9, 51,
       42, 50, 41, 48, 17, 22, 10, 31, 49, 26,  8, 53, 52, 30, 11, 23,  1,
       18, 44,  2,  7, 15, 19, 45, 29, 36,  4, 46, 54, 43, 38, 39, 37, 34,
       20,  3,  0, 28]), 'cur_cost': 103927.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([16, 25, 47, 27, 21, 40,  6,  5, 33, 13, 32, 12, 24, 14, 35,  9, 51,
       42, 50, 41, 48, 17, 22, 10, 31, 49, 26,  8, 53, 52, 30, 11, 23,  1,
       18, 44,  2,  7, 15, 19, 45, 29, 36,  4, 46, 54, 43, 38, 39, 37, 34,
       20,  3,  0, 28]), 'cur_cost': 107194.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 09:52:09,270 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:09,270 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:09,273 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10695.000, 多样性=0.969
2025-08-05 09:52:09,273 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 09:52:09,273 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 09:52:09,274 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:09,274 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.014600767296691623, 'best_improvement': 0.008160994157470092}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.05173169662428756}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.0313791586288657, 'recent_improvements': [0.17048579146305695, -0.37142532680957613, 0.23324410872078835], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 6, 'new_count': 6, 'count_change': 0, 'old_best_cost': 10443, 'new_best_cost': 10443, 'quality_improvement': 0.0, 'old_diversity': 0.7139393939393939, 'new_diversity': 0.7139393939393939, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:52:09,275 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 09:52:09,278 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite10_55_solution.json
2025-08-05 09:52:09,278 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite10_55_20250805_095209.solution
2025-08-05 09:52:09,278 - __main__ - INFO - 实例执行完成 - 运行时间: 1.40s, 最佳成本: 10443
2025-08-05 09:52:09,279 - __main__ - INFO - 实例 composite10_55 处理完成
