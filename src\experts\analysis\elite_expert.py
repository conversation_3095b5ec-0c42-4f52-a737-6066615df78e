# -*- coding: utf-8 -*-
"""
精英解分析专家模块

包含EliteExpert类，负责分析已发现的精英解。
重构版本：使用统一的工具模块，消除代码冗余。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from experts.base.expert_base import ExpertBase
from collections import defaultdict
from utils import utils
from utils.analysis_utils import AnalysisUtils, PathUtils, DataValidator
from utils.similarity_utils import SimilarityCalculator, DiversityAnalyzer, PatternAnalyzer


class EliteExpert(ExpertBase):
    """精英解分析专家，分析已发现的精英解"""
    
    def __init__(self):
        super().__init__()
    
    def analyze(self, elite_solutions, populations, distance_matrix):
        """分析精英解特征
        
        参数:
            elite_solutions: 精英解列表
            populations: 当前种群
            distance_matrix: 距离矩阵
            
        返回:
            精英解分析结果
        """
        self.logger.info("开始精英解分析")
        
        if not elite_solutions:
            return {
                "elite_count": 0,
                "elite_features": {},
                "elite_diversity": {},
                "elite_quality": {},
                "common_patterns": []
            }
        
        # 分析精英解特征
        elite_features = self._analyze_elite_features(elite_solutions, distance_matrix)
        
        # 分析精英解多样性
        elite_diversity = self._analyze_elite_diversity(elite_solutions)
        
        # 分析精英解质量
        elite_quality = self._analyze_elite_quality(elite_solutions, populations)
        
        # 分析公共模式
        common_patterns = self._analyze_common_patterns(elite_solutions)
        
        analysis_result = {
            "elite_count": len(elite_solutions),
            "elite_solutions": elite_solutions,  # 添加原始精英解数据
            "elite_features": elite_features,
            "elite_diversity": elite_diversity,
            "elite_quality": elite_quality,
            "common_patterns": common_patterns
        }
        
        self.logger.info(f"精英解分析完成: 精英解数量={len(elite_solutions)}, 多样性={elite_diversity.get('diversity_score', 0):.3f}")
        return analysis_result
    
    def generate_report(self, analysis_result, instance_name=None):
        """生成精英解分析报告

        参数:
            analysis_result: 分析结果
            instance_name: 实例名称（可选）

        返回:
            精英解分析报告
        """
        return {
            "instance_name": instance_name,  # 添加实例名信息
            "elite_count": analysis_result["elite_count"],
            "elite_features": analysis_result["elite_features"],
            "elite_diversity": analysis_result["elite_diversity"],
            "elite_quality": analysis_result["elite_quality"],
            "common_patterns": analysis_result["common_patterns"],
            "summary": {
                "best_cost": analysis_result["elite_quality"].get("best_cost", float('inf')),
                "avg_cost": analysis_result["elite_quality"].get("avg_cost", 0),
                "diversity_level": analysis_result["elite_diversity"].get("diversity_score", 0),
                "pattern_count": len(analysis_result["common_patterns"])
            }
        }
    
    def _analyze_elite_features(self, elite_solutions, distance_matrix):
        """分析精英解特征
        
        参数:
            elite_solutions: 精英解列表
            distance_matrix: 距离矩阵
            
        返回:
            精英解特征字典
        """
        features = {
            "fixed_nodes": [],
            "high_quality_edges": [],
            "structural_properties": {}
        }
        
        if not elite_solutions or distance_matrix is None:
            return features
        
        # 分析固定节点（在多个精英解中位置相对稳定的节点）
        node_positions = defaultdict(list)
        for solution in elite_solutions:
            if "tour" in solution:
                tour = solution["tour"]
                for i, node in enumerate(tour):
                    node_positions[node].append(i / len(tour))  # 标准化位置
        
        # 找出位置变化较小的节点
        fixed_nodes = []
        for node, positions in node_positions.items():
            if len(positions) > 1:
                position_variance = sum((p - sum(positions)/len(positions))**2 for p in positions) / len(positions)
                if position_variance < 0.1:  # 位置变化小于10%
                    fixed_nodes.append(node)
        
        features["fixed_nodes"] = fixed_nodes
        
        # 分析高质量边（在精英解中频繁出现的边）
        edge_counts = defaultdict(int)
        total_solutions = len(elite_solutions)
        
        for solution in elite_solutions:
            if "tour" in solution:
                tour = solution["tour"]
                for i in range(len(tour)):
                    edge = tuple(sorted([tour[i], tour[(i + 1) % len(tour)]]))
                    edge_counts[edge] += 1
        
        # 选择出现频率超过50%的边作为高质量边
        high_quality_edges = [edge for edge, count in edge_counts.items() if count / total_solutions > 0.5]
        features["high_quality_edges"] = high_quality_edges
        
        # 分析结构属性
        if elite_solutions:
            tour_lengths = [len(solution["tour"]) for solution in elite_solutions if "tour" in solution]
            costs = [solution["cur_cost"] for solution in elite_solutions if "cur_cost" in solution]
            
            features["structural_properties"] = {
                "avg_tour_length": sum(tour_lengths) / len(tour_lengths) if tour_lengths else 0,
                "cost_range": max(costs) - min(costs) if costs else 0,
                "cost_std": AnalysisUtils.calculate_std(costs) if costs else 0
            }
        
        return features
    
    def _analyze_elite_diversity(self, elite_solutions):
        """分析精英解多样性

        参数:
            elite_solutions: 精英解列表

        返回:
            多样性分析结果
        """
        if len(elite_solutions) < 2:
            return {"diversity_score": 0.0, "pairwise_distances": []}

        # 提取路径数据
        tours = PathUtils.extract_tours_safely(elite_solutions)

        if len(tours) < 2:
            return {"diversity_score": 0.0, "pairwise_distances": []}

        # 使用统一的多样性分析工具
        diversity_analysis = DiversityAnalyzer.analyze_population_diversity(tours)

        # 计算成对距离
        distance_matrix = SimilarityCalculator.calculate_pairwise_distances(tours, 'hamming')

        # 提取上三角矩阵的值（避免重复和对角线）
        import numpy as np
        upper_triangle = distance_matrix[np.triu_indices_from(distance_matrix, k=1)]
        pairwise_distances = upper_triangle.tolist()

        return {
            "diversity_score": diversity_analysis.get("hamming_diversity", 0.0),
            "edge_diversity": diversity_analysis.get("edge_diversity", 0.0),
            "pairwise_distances": pairwise_distances,
            "min_distance": diversity_analysis.get("min_distance", 0.0),
            "max_distance": diversity_analysis.get("max_distance", 0.0),
            "avg_pairwise_distance": diversity_analysis.get("avg_pairwise_distance", 0.0),
            "std_distance": diversity_analysis.get("std_distance", 0.0)
        }
    
    def _analyze_elite_quality(self, elite_solutions, populations):
        """分析精英解质量
        
        参数:
            elite_solutions: 精英解列表
            populations: 当前种群
            
        返回:
            质量分析结果
        """
        if not elite_solutions:
            return {"best_cost": float('inf'), "avg_cost": 0, "improvement_over_population": 0}
        
        elite_costs = [solution["cur_cost"] for solution in elite_solutions if "cur_cost" in solution]
        population_costs = [individual["cur_cost"] for individual in populations if "cur_cost" in individual]
        
        if not elite_costs:
            return {"best_cost": float('inf'), "avg_cost": 0, "improvement_over_population": 0}
        
        best_cost = min(elite_costs)
        avg_cost = sum(elite_costs) / len(elite_costs)
        
        # 计算相对于种群的改进
        improvement_over_population = 0
        if population_costs:
            population_avg = sum(population_costs) / len(population_costs)
            if population_avg > 0:
                improvement_over_population = (population_avg - avg_cost) / population_avg
        
        return {
            "best_cost": best_cost,
            "avg_cost": avg_cost,
            "worst_cost": max(elite_costs),
            "cost_std": AnalysisUtils.calculate_std(elite_costs),
            "improvement_over_population": improvement_over_population
        }
    
    def _analyze_common_patterns(self, elite_solutions):
        """分析精英解中的公共模式
        
        参数:
            elite_solutions: 精英解列表
            
        返回:
            公共模式列表
        """
        if not elite_solutions:
            return []
        
        # 分析子序列模式（长度为3的连续城市序列）
        pattern_counts = defaultdict(int)
        
        for solution in elite_solutions:
            if "tour" in solution:
                tour = solution["tour"]
                for i in range(len(tour)):
                    # 提取长度为3的子序列
                    pattern = tuple([tour[i], tour[(i + 1) % len(tour)], tour[(i + 2) % len(tour)]])
                    pattern_counts[pattern] += 1
        
        # 选择出现频率较高的模式
        total_solutions = len(elite_solutions)
        common_patterns = []
        
        for pattern, count in pattern_counts.items():
            if count / total_solutions > 0.3:  # 出现在30%以上的精英解中
                common_patterns.append({
                    "pattern": pattern,
                    "frequency": count / total_solutions,
                    "count": count
                })
        
        # 按频率排序
        common_patterns.sort(key=lambda x: x["frequency"], reverse=True)
        
        return common_patterns
    

