2025-08-01 09:27:55,032 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-08-01 09:27:55,033 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-01 09:27:55,033 - StatsExpert - INFO - 开始统计分析
2025-08-01 09:27:55,049 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9955.0, 多样性=0.934
2025-08-01 09:27:55,049 - PathExpert - INFO - 开始路径结构分析
2025-08-01 09:27:55,053 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.015
2025-08-01 09:27:55,054 - EliteExpert - INFO - 开始精英解分析
2025-08-01 09:27:55,056 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-01 09:27:55,056 - LandscapeExpert - INFO - 从统计报告生成模拟数据: 10个路径, 10个适应度值
2025-08-01 09:27:55,057 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-01 09:27:55,199 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-01 09:27:55,464 - visualization.landscape_visualizer - INFO - 图表已保存: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_iter_1.html
2025-08-01 09:27:55,501 - visualization.landscape_visualizer - INFO - 图表已保存: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_iter_1.html
2025-08-01 09:27:55,501 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-01 09:27:55,501 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-01 09:27:55,501 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.4452秒
2025-08-01 09:27:55,502 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754011675.1998281, 'performance_metrics': {}}}
2025-08-01 09:27:55,502 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-01 09:27:55,502 - StrategyExpert - INFO - 开始策略分配分析
2025-08-01 09:27:55,502 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 9955.0
  • mean_cost: 78386.7
- Search-space: ruggedness 0.5, modality uni-modal, deceptiveness low
- Evolution phase: exploration
- Landscape focus suggestion: exploration
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 1): [{"type": "unexplored_space", "potential": "high", "description": "未充分探索的搜索空间", "recommendation": "增加探索性操作"}]
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-08-01 09:27:55,502 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-08-01 09:27:55,502 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-01 09:27:57,277 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.80,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "The recommended focus is exploration, and unexplored search space identified. High global exploration ratio to discover new solutions. Some exploitation is needed."
}
```
2025-08-01 09:27:57,277 - utils.utils - INFO - 成功从JSON中提取数据
2025-08-01 09:27:57,278 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-08-01 09:27:57,278 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-08-01 09:27:57,278 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.80,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "The recommended focus is exploration, and unexplored search space identified. High global exploration ratio to discover new solutions. Some exploitation is needed."
}
```
2025-08-01 09:27:57,278 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-01 09:27:57,278 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-08-01 09:27:57,278 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.80,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "The recommended focus is exploration, and unexplored search space identified. High global exploration ratio to discover new solutions. Some exploitation is needed."
}
```
2025-08-01 09:27:57,278 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-01 09:27:57,278 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-01 09:27:57,278 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-01 09:27:57,279 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:27:57,286 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-01 09:27:57,286 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:27:57,400 - ExplorationExpert - INFO - 探索路径生成完成，成本: 60381.0, 路径长度: 66
2025-08-01 09:27:57,400 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [36, 35, 17, 20, 13, 11, 52, 2, 18, 37, 30, 22, 9, 7, 54, 5, 16, 0, 59, 12, 3, 23, 28, 4, 55, 56, 58, 57, 53, 10, 27, 31, 19, 43, 51, 40, 41, 34, 49, 15, 25, 48, 38, 39, 24, 47, 44, 46, 42, 50, 32, 29, 1, 65, 62, 60, 63, 61, 8, 6, 14, 21, 26, 33, 45, 64], 'cur_cost': 60381.0}
2025-08-01 09:27:57,400 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-01 09:27:57,400 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-01 09:27:57,400 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:27:57,402 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-01 09:27:57,402 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:27:57,403 - ExplorationExpert - INFO - 探索路径生成完成，成本: 88504.0, 路径长度: 66
2025-08-01 09:27:57,403 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [11, 52, 18, 9, 7, 54, 0, 55, 57, 53, 27, 19, 43, 41, 34, 49, 48, 38, 39, 24, 44, 46, 42, 50, 32, 2, 26, 28, 23, 30, 10, 31, 35, 13, 56, 25, 58, 5, 6, 8, 17, 33, 3, 36, 59, 45, 60, 29, 64, 63, 37, 21, 12, 4, 61, 47, 40, 16, 15, 22, 51, 1, 65, 20, 62, 14], 'cur_cost': 88504.0}
2025-08-01 09:27:57,403 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-01 09:27:57,403 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-01 09:27:57,403 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:27:57,405 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 09:27:57,406 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:27:57,406 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12891.0, 路径长度: 66
2025-08-01 09:27:57,406 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 13, 4, 9, 11, 7, 3, 1, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12891.0}
2025-08-01 09:27:57,406 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-01 09:27:57,406 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-01 09:27:57,406 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:27:57,414 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-01 09:27:57,414 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:27:57,415 - ExplorationExpert - INFO - 探索路径生成完成，成本: 59008.0, 路径长度: 66
2025-08-01 09:27:57,415 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [43, 48, 49, 15, 19, 25, 5, 64, 9, 58, 62, 52, 10, 7, 54, 59, 8, 6, 53, 22, 20, 3, 55, 40, 46, 13, 17, 24, 26, 14, 28, 27, 23, 37, 33, 12, 21, 35, 30, 4, 65, 63, 11, 60, 1, 18, 2, 57, 44, 47, 42, 38, 39, 41, 34, 29, 31, 0, 56, 45, 50, 51, 16, 36, 32, 61], 'cur_cost': 59008.0}
2025-08-01 09:27:57,415 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-01 09:27:57,415 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-01 09:27:57,415 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:27:57,417 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-01 09:27:57,417 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:27:57,418 - ExplorationExpert - INFO - 探索路径生成完成，成本: 96703.0, 路径长度: 66
2025-08-01 09:27:57,418 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [11, 35, 17, 19, 7, 52, 55, 57, 53, 2, 6, 41, 61, 5, 16, 0, 56, 12, 3, 46, 42, 4, 65, 13, 58, 28, 23, 10, 27, 18, 43, 51, 25, 20, 49, 15, 33, 48, 38, 39, 24, 45, 60, 29, 64, 50, 32, 21, 1, 62, 47, 40, 36, 8, 14, 9, 26, 31, 30, 37, 44, 59, 54, 34, 63, 22], 'cur_cost': 96703.0}
2025-08-01 09:27:57,418 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-01 09:27:57,418 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-01 09:27:57,418 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:27:57,420 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 09:27:57,420 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:27:57,421 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12702.0, 路径长度: 66
2025-08-01 09:27:57,421 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 17, 4, 6, 2, 8, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12702.0}
2025-08-01 09:27:57,421 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-01 09:27:57,421 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-01 09:27:57,421 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:27:57,424 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 09:27:57,424 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:27:57,424 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12377.0, 路径长度: 66
2025-08-01 09:27:57,424 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 3, 14, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12377.0}
2025-08-01 09:27:57,424 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-01 09:27:57,424 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:27:57,454 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:27:57,456 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 100720.0
2025-08-01 09:27:58,728 - ExploitationExpert - INFO - res_population_num: 1
2025-08-01 09:27:58,729 - ExploitationExpert - INFO - res_population_costs: [9592.0]
2025-08-01 09:27:58,729 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 09:27:58,730 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 09:27:58,730 - ExploitationExpert - INFO - populations: [{'tour': [36, 35, 17, 20, 13, 11, 52, 2, 18, 37, 30, 22, 9, 7, 54, 5, 16, 0, 59, 12, 3, 23, 28, 4, 55, 56, 58, 57, 53, 10, 27, 31, 19, 43, 51, 40, 41, 34, 49, 15, 25, 48, 38, 39, 24, 47, 44, 46, 42, 50, 32, 29, 1, 65, 62, 60, 63, 61, 8, 6, 14, 21, 26, 33, 45, 64], 'cur_cost': 60381.0}, {'tour': [11, 52, 18, 9, 7, 54, 0, 55, 57, 53, 27, 19, 43, 41, 34, 49, 48, 38, 39, 24, 44, 46, 42, 50, 32, 2, 26, 28, 23, 30, 10, 31, 35, 13, 56, 25, 58, 5, 6, 8, 17, 33, 3, 36, 59, 45, 60, 29, 64, 63, 37, 21, 12, 4, 61, 47, 40, 16, 15, 22, 51, 1, 65, 20, 62, 14], 'cur_cost': 88504.0}, {'tour': [0, 13, 4, 9, 11, 7, 3, 1, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12891.0}, {'tour': [43, 48, 49, 15, 19, 25, 5, 64, 9, 58, 62, 52, 10, 7, 54, 59, 8, 6, 53, 22, 20, 3, 55, 40, 46, 13, 17, 24, 26, 14, 28, 27, 23, 37, 33, 12, 21, 35, 30, 4, 65, 63, 11, 60, 1, 18, 2, 57, 44, 47, 42, 38, 39, 41, 34, 29, 31, 0, 56, 45, 50, 51, 16, 36, 32, 61], 'cur_cost': 59008.0}, {'tour': [11, 35, 17, 19, 7, 52, 55, 57, 53, 2, 6, 41, 61, 5, 16, 0, 56, 12, 3, 46, 42, 4, 65, 13, 58, 28, 23, 10, 27, 18, 43, 51, 25, 20, 49, 15, 33, 48, 38, 39, 24, 45, 60, 29, 64, 50, 32, 21, 1, 62, 47, 40, 36, 8, 14, 9, 26, 31, 30, 37, 44, 59, 54, 34, 63, 22], 'cur_cost': 96703.0}, {'tour': [0, 17, 4, 6, 2, 8, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12702.0}, {'tour': [0, 3, 14, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12377.0}, {'tour': array([61, 63, 47, 53,  1, 34, 35, 57, 27, 50, 13, 42,  0, 41, 56, 18, 28,
       10,  4, 52, 21,  9, 39, 38, 60, 16, 12,  6, 45,  5, 11, 26, 20, 17,
       14, 19, 25, 30, 46, 59, 40, 51, 44,  2, 49,  3, 64, 55, 58,  7, 15,
       62, 33, 23, 36, 24, 29,  8, 37, 54, 65, 22, 31, 48, 32, 43],
      dtype=int64), 'cur_cost': 100720.0}, {'tour': array([19, 45, 20, 43,  1, 63, 62, 57, 32, 46, 28,  2, 30,  8, 21, 17, 24,
       16, 37, 55, 44, 53, 48, 35, 29, 60,  6, 49,  7,  3, 54, 50, 39, 27,
       26,  9, 22, 13, 47, 41, 25, 10, 31, 12, 15, 40, 23, 52, 36, 34, 56,
        4, 18, 42, 38,  5, 51, 33, 65, 59, 11, 61, 64, 14, 58,  0],
      dtype=int64), 'cur_cost': 99612.0}, {'tour': array([42, 16, 14,  2, 18, 40, 13, 12, 64,  4, 56, 37, 36, 46, 53, 22, 65,
       35, 44, 60, 32, 58, 25, 11, 43, 34, 28, 19, 26, 38, 59, 10, 20,  8,
        3, 55, 15, 49, 48, 63, 27, 29, 54, 51,  6, 30, 41, 62, 21,  5, 39,
       23, 31,  9, 61, 52,  0,  1, 50, 47, 57,  7, 45, 33, 17, 24],
      dtype=int64), 'cur_cost': 110400.0}]
2025-08-01 09:27:58,732 - ExploitationExpert - INFO - 局部搜索耗时: 1.28秒
2025-08-01 09:27:58,732 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-01 09:27:58,732 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([61, 63, 47, 53,  1, 34, 35, 57, 27, 50, 13, 42,  0, 41, 56, 18, 28,
       10,  4, 52, 21,  9, 39, 38, 60, 16, 12,  6, 45,  5, 11, 26, 20, 17,
       14, 19, 25, 30, 46, 59, 40, 51, 44,  2, 49,  3, 64, 55, 58,  7, 15,
       62, 33, 23, 36, 24, 29,  8, 37, 54, 65, 22, 31, 48, 32, 43],
      dtype=int64), 'cur_cost': 100720.0}
2025-08-01 09:27:58,732 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-01 09:27:58,732 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:27:58,732 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:27:58,733 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 109085.0
2025-08-01 09:28:00,273 - ExploitationExpert - INFO - res_population_num: 2
2025-08-01 09:28:00,273 - ExploitationExpert - INFO - res_population_costs: [9592.0, 9555.0]
2025-08-01 09:28:00,273 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64)]
2025-08-01 09:28:00,274 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 09:28:00,275 - ExploitationExpert - INFO - populations: [{'tour': [36, 35, 17, 20, 13, 11, 52, 2, 18, 37, 30, 22, 9, 7, 54, 5, 16, 0, 59, 12, 3, 23, 28, 4, 55, 56, 58, 57, 53, 10, 27, 31, 19, 43, 51, 40, 41, 34, 49, 15, 25, 48, 38, 39, 24, 47, 44, 46, 42, 50, 32, 29, 1, 65, 62, 60, 63, 61, 8, 6, 14, 21, 26, 33, 45, 64], 'cur_cost': 60381.0}, {'tour': [11, 52, 18, 9, 7, 54, 0, 55, 57, 53, 27, 19, 43, 41, 34, 49, 48, 38, 39, 24, 44, 46, 42, 50, 32, 2, 26, 28, 23, 30, 10, 31, 35, 13, 56, 25, 58, 5, 6, 8, 17, 33, 3, 36, 59, 45, 60, 29, 64, 63, 37, 21, 12, 4, 61, 47, 40, 16, 15, 22, 51, 1, 65, 20, 62, 14], 'cur_cost': 88504.0}, {'tour': [0, 13, 4, 9, 11, 7, 3, 1, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12891.0}, {'tour': [43, 48, 49, 15, 19, 25, 5, 64, 9, 58, 62, 52, 10, 7, 54, 59, 8, 6, 53, 22, 20, 3, 55, 40, 46, 13, 17, 24, 26, 14, 28, 27, 23, 37, 33, 12, 21, 35, 30, 4, 65, 63, 11, 60, 1, 18, 2, 57, 44, 47, 42, 38, 39, 41, 34, 29, 31, 0, 56, 45, 50, 51, 16, 36, 32, 61], 'cur_cost': 59008.0}, {'tour': [11, 35, 17, 19, 7, 52, 55, 57, 53, 2, 6, 41, 61, 5, 16, 0, 56, 12, 3, 46, 42, 4, 65, 13, 58, 28, 23, 10, 27, 18, 43, 51, 25, 20, 49, 15, 33, 48, 38, 39, 24, 45, 60, 29, 64, 50, 32, 21, 1, 62, 47, 40, 36, 8, 14, 9, 26, 31, 30, 37, 44, 59, 54, 34, 63, 22], 'cur_cost': 96703.0}, {'tour': [0, 17, 4, 6, 2, 8, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12702.0}, {'tour': [0, 3, 14, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12377.0}, {'tour': array([61, 63, 47, 53,  1, 34, 35, 57, 27, 50, 13, 42,  0, 41, 56, 18, 28,
       10,  4, 52, 21,  9, 39, 38, 60, 16, 12,  6, 45,  5, 11, 26, 20, 17,
       14, 19, 25, 30, 46, 59, 40, 51, 44,  2, 49,  3, 64, 55, 58,  7, 15,
       62, 33, 23, 36, 24, 29,  8, 37, 54, 65, 22, 31, 48, 32, 43],
      dtype=int64), 'cur_cost': 100720.0}, {'tour': array([16, 42, 31, 15, 22, 38, 62, 24, 57, 59,  9, 28, 41,  6, 44, 46, 23,
       36, 27, 10, 52, 35, 58,  5, 25, 14, 12, 34, 56, 40, 50, 19,  8, 53,
       39, 21, 26,  7, 18, 33,  0, 37, 29,  4, 48,  1, 63, 49, 32, 54, 60,
       30,  2, 13, 20, 51, 17, 45, 11,  3, 65, 47, 61, 43, 64, 55],
      dtype=int64), 'cur_cost': 109085.0}, {'tour': array([42, 16, 14,  2, 18, 40, 13, 12, 64,  4, 56, 37, 36, 46, 53, 22, 65,
       35, 44, 60, 32, 58, 25, 11, 43, 34, 28, 19, 26, 38, 59, 10, 20,  8,
        3, 55, 15, 49, 48, 63, 27, 29, 54, 51,  6, 30, 41, 62, 21,  5, 39,
       23, 31,  9, 61, 52,  0,  1, 50, 47, 57,  7, 45, 33, 17, 24],
      dtype=int64), 'cur_cost': 110400.0}]
2025-08-01 09:28:00,277 - ExploitationExpert - INFO - 局部搜索耗时: 1.54秒
2025-08-01 09:28:00,277 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-01 09:28:00,277 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([16, 42, 31, 15, 22, 38, 62, 24, 57, 59,  9, 28, 41,  6, 44, 46, 23,
       36, 27, 10, 52, 35, 58,  5, 25, 14, 12, 34, 56, 40, 50, 19,  8, 53,
       39, 21, 26,  7, 18, 33,  0, 37, 29,  4, 48,  1, 63, 49, 32, 54, 60,
       30,  2, 13, 20, 51, 17, 45, 11,  3, 65, 47, 61, 43, 64, 55],
      dtype=int64), 'cur_cost': 109085.0}
2025-08-01 09:28:00,277 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-01 09:28:00,277 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:28:00,277 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:28:00,278 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 100244.0
2025-08-01 09:28:00,681 - ExploitationExpert - INFO - res_population_num: 6
2025-08-01 09:28:00,681 - ExploitationExpert - INFO - res_population_costs: [9592.0, 9555.0, 9555, 9542, 9521, 9521]
2025-08-01 09:28:00,681 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-01 09:28:00,684 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 09:28:00,684 - ExploitationExpert - INFO - populations: [{'tour': [36, 35, 17, 20, 13, 11, 52, 2, 18, 37, 30, 22, 9, 7, 54, 5, 16, 0, 59, 12, 3, 23, 28, 4, 55, 56, 58, 57, 53, 10, 27, 31, 19, 43, 51, 40, 41, 34, 49, 15, 25, 48, 38, 39, 24, 47, 44, 46, 42, 50, 32, 29, 1, 65, 62, 60, 63, 61, 8, 6, 14, 21, 26, 33, 45, 64], 'cur_cost': 60381.0}, {'tour': [11, 52, 18, 9, 7, 54, 0, 55, 57, 53, 27, 19, 43, 41, 34, 49, 48, 38, 39, 24, 44, 46, 42, 50, 32, 2, 26, 28, 23, 30, 10, 31, 35, 13, 56, 25, 58, 5, 6, 8, 17, 33, 3, 36, 59, 45, 60, 29, 64, 63, 37, 21, 12, 4, 61, 47, 40, 16, 15, 22, 51, 1, 65, 20, 62, 14], 'cur_cost': 88504.0}, {'tour': [0, 13, 4, 9, 11, 7, 3, 1, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12891.0}, {'tour': [43, 48, 49, 15, 19, 25, 5, 64, 9, 58, 62, 52, 10, 7, 54, 59, 8, 6, 53, 22, 20, 3, 55, 40, 46, 13, 17, 24, 26, 14, 28, 27, 23, 37, 33, 12, 21, 35, 30, 4, 65, 63, 11, 60, 1, 18, 2, 57, 44, 47, 42, 38, 39, 41, 34, 29, 31, 0, 56, 45, 50, 51, 16, 36, 32, 61], 'cur_cost': 59008.0}, {'tour': [11, 35, 17, 19, 7, 52, 55, 57, 53, 2, 6, 41, 61, 5, 16, 0, 56, 12, 3, 46, 42, 4, 65, 13, 58, 28, 23, 10, 27, 18, 43, 51, 25, 20, 49, 15, 33, 48, 38, 39, 24, 45, 60, 29, 64, 50, 32, 21, 1, 62, 47, 40, 36, 8, 14, 9, 26, 31, 30, 37, 44, 59, 54, 34, 63, 22], 'cur_cost': 96703.0}, {'tour': [0, 17, 4, 6, 2, 8, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12702.0}, {'tour': [0, 3, 14, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12377.0}, {'tour': array([61, 63, 47, 53,  1, 34, 35, 57, 27, 50, 13, 42,  0, 41, 56, 18, 28,
       10,  4, 52, 21,  9, 39, 38, 60, 16, 12,  6, 45,  5, 11, 26, 20, 17,
       14, 19, 25, 30, 46, 59, 40, 51, 44,  2, 49,  3, 64, 55, 58,  7, 15,
       62, 33, 23, 36, 24, 29,  8, 37, 54, 65, 22, 31, 48, 32, 43],
      dtype=int64), 'cur_cost': 100720.0}, {'tour': array([16, 42, 31, 15, 22, 38, 62, 24, 57, 59,  9, 28, 41,  6, 44, 46, 23,
       36, 27, 10, 52, 35, 58,  5, 25, 14, 12, 34, 56, 40, 50, 19,  8, 53,
       39, 21, 26,  7, 18, 33,  0, 37, 29,  4, 48,  1, 63, 49, 32, 54, 60,
       30,  2, 13, 20, 51, 17, 45, 11,  3, 65, 47, 61, 43, 64, 55],
      dtype=int64), 'cur_cost': 109085.0}, {'tour': array([26,  4, 64, 44,  0, 13, 52, 21, 37, 57, 25, 23, 29, 53, 50, 24, 42,
       20, 51, 34, 12, 16,  7, 11, 27,  6,  3, 33, 55, 54, 58, 61, 65,  8,
       15, 10, 49, 60, 41, 48,  9, 18, 39, 14, 63, 17,  1, 36, 30, 47, 56,
       62,  5,  2, 28, 32, 38, 59, 22, 35, 46, 31, 43, 40, 45, 19],
      dtype=int64), 'cur_cost': 100244.0}]
2025-08-01 09:28:00,686 - ExploitationExpert - INFO - 局部搜索耗时: 0.41秒
2025-08-01 09:28:00,686 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-01 09:28:00,687 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([26,  4, 64, 44,  0, 13, 52, 21, 37, 57, 25, 23, 29, 53, 50, 24, 42,
       20, 51, 34, 12, 16,  7, 11, 27,  6,  3, 33, 55, 54, 58, 61, 65,  8,
       15, 10, 49, 60, 41, 48,  9, 18, 39, 14, 63, 17,  1, 36, 30, 47, 56,
       62,  5,  2, 28, 32, 38, 59, 22, 35, 46, 31, 43, 40, 45, 19],
      dtype=int64), 'cur_cost': 100244.0}
2025-08-01 09:28:00,688 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [36, 35, 17, 20, 13, 11, 52, 2, 18, 37, 30, 22, 9, 7, 54, 5, 16, 0, 59, 12, 3, 23, 28, 4, 55, 56, 58, 57, 53, 10, 27, 31, 19, 43, 51, 40, 41, 34, 49, 15, 25, 48, 38, 39, 24, 47, 44, 46, 42, 50, 32, 29, 1, 65, 62, 60, 63, 61, 8, 6, 14, 21, 26, 33, 45, 64], 'cur_cost': 60381.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [11, 52, 18, 9, 7, 54, 0, 55, 57, 53, 27, 19, 43, 41, 34, 49, 48, 38, 39, 24, 44, 46, 42, 50, 32, 2, 26, 28, 23, 30, 10, 31, 35, 13, 56, 25, 58, 5, 6, 8, 17, 33, 3, 36, 59, 45, 60, 29, 64, 63, 37, 21, 12, 4, 61, 47, 40, 16, 15, 22, 51, 1, 65, 20, 62, 14], 'cur_cost': 88504.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 4, 9, 11, 7, 3, 1, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12891.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [43, 48, 49, 15, 19, 25, 5, 64, 9, 58, 62, 52, 10, 7, 54, 59, 8, 6, 53, 22, 20, 3, 55, 40, 46, 13, 17, 24, 26, 14, 28, 27, 23, 37, 33, 12, 21, 35, 30, 4, 65, 63, 11, 60, 1, 18, 2, 57, 44, 47, 42, 38, 39, 41, 34, 29, 31, 0, 56, 45, 50, 51, 16, 36, 32, 61], 'cur_cost': 59008.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [11, 35, 17, 19, 7, 52, 55, 57, 53, 2, 6, 41, 61, 5, 16, 0, 56, 12, 3, 46, 42, 4, 65, 13, 58, 28, 23, 10, 27, 18, 43, 51, 25, 20, 49, 15, 33, 48, 38, 39, 24, 45, 60, 29, 64, 50, 32, 21, 1, 62, 47, 40, 36, 8, 14, 9, 26, 31, 30, 37, 44, 59, 54, 34, 63, 22], 'cur_cost': 96703.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 4, 6, 2, 8, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12702.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 14, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12377.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([61, 63, 47, 53,  1, 34, 35, 57, 27, 50, 13, 42,  0, 41, 56, 18, 28,
       10,  4, 52, 21,  9, 39, 38, 60, 16, 12,  6, 45,  5, 11, 26, 20, 17,
       14, 19, 25, 30, 46, 59, 40, 51, 44,  2, 49,  3, 64, 55, 58,  7, 15,
       62, 33, 23, 36, 24, 29,  8, 37, 54, 65, 22, 31, 48, 32, 43],
      dtype=int64), 'cur_cost': 100720.0}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([16, 42, 31, 15, 22, 38, 62, 24, 57, 59,  9, 28, 41,  6, 44, 46, 23,
       36, 27, 10, 52, 35, 58,  5, 25, 14, 12, 34, 56, 40, 50, 19,  8, 53,
       39, 21, 26,  7, 18, 33,  0, 37, 29,  4, 48,  1, 63, 49, 32, 54, 60,
       30,  2, 13, 20, 51, 17, 45, 11,  3, 65, 47, 61, 43, 64, 55],
      dtype=int64), 'cur_cost': 109085.0}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([26,  4, 64, 44,  0, 13, 52, 21, 37, 57, 25, 23, 29, 53, 50, 24, 42,
       20, 51, 34, 12, 16,  7, 11, 27,  6,  3, 33, 55, 54, 58, 61, 65,  8,
       15, 10, 49, 60, 41, 48,  9, 18, 39, 14, 63, 17,  1, 36, 30, 47, 56,
       62,  5,  2, 28, 32, 38, 59, 22, 35, 46, 31, 43, 40, 45, 19],
      dtype=int64), 'cur_cost': 100244.0}}]
2025-08-01 09:28:00,688 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-01 09:28:00,688 - StatsExpert - INFO - 开始统计分析
2025-08-01 09:28:00,705 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12377.0, 多样性=0.902
2025-08-01 09:28:00,705 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-01 09:28:00,705 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-01 09:28:00,705 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-01 09:28:00,706 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.0024664760507677602, 'best_improvement': -0.24329482672024108}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.03424657534246554}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 6, 'new_count': 6, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.7070707070707071, 'new_diversity': 0.7070707070707071, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-01 09:28:00,706 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-01 09:28:00,707 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-08-01 09:28:00,707 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-01 09:28:00,707 - StatsExpert - INFO - 开始统计分析
2025-08-01 09:28:00,724 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12377.0, 多样性=0.902
2025-08-01 09:28:00,724 - PathExpert - INFO - 开始路径结构分析
2025-08-01 09:28:00,728 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.029
2025-08-01 09:28:00,728 - EliteExpert - INFO - 开始精英解分析
2025-08-01 09:28:00,729 - EliteExpert - INFO - 精英解分析完成: 精英解数量=6, 多样性=0.707
2025-08-01 09:28:00,731 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-01 09:28:00,731 - LandscapeExpert - INFO - 从统计报告生成模拟数据: 10个路径, 10个适应度值
2025-08-01 09:28:00,731 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-01 09:28:00,735 - core.algorithms.fitness_landscape_analyzer - ERROR - 景观分析失败: '>' not supported between instances of 'str' and 'int'
2025-08-01 09:28:00,735 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-01 09:28:00,837 - visualization.landscape_visualizer - INFO - 图表已保存: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_iter_2.html
2025-08-01 09:28:00,874 - visualization.landscape_visualizer - INFO - 图表已保存: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_iter_2.html
2025-08-01 09:28:00,874 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 2
2025-08-01 09:28:00,874 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-01 09:28:00,874 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1430秒
2025-08-01 09:28:00,874 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754011680.7353933, 'performance_metrics': {}}}
2025-08-01 09:28:00,874 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-01 09:28:00,874 - StrategyExpert - INFO - 开始策略分配分析
2025-08-01 09:28:00,875 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 12377.0
  • mean_cost: 65261.5
- Search-space: ruggedness 0.5, modality uni-modal, deceptiveness low
- Evolution phase: exploration
- Landscape focus suggestion: exploration
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 1): [{"type": "unexplored_space", "potential": "high", "description": "未充分探索的搜索空间", "recommendation": "增加探索性操作"}]
- Last-iteration feedback: {'overall_score': 40, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvemen...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-08-01 09:28:00,875 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-08-01 09:28:00,875 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-01 09:28:02,638 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High exploration needed. Focus is exploration, and diversity is moderate.  Explore with the majority, exploit the rest."
}
```
2025-08-01 09:28:02,638 - utils.utils - INFO - 成功从JSON中提取数据
2025-08-01 09:28:02,638 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-08-01 09:28:02,639 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-08-01 09:28:02,639 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High exploration needed. Focus is exploration, and diversity is moderate.  Explore with the majority, exploit the rest."
}
```
2025-08-01 09:28:02,639 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-01 09:28:02,639 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-08-01 09:28:02,639 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High exploration needed. Focus is exploration, and diversity is moderate.  Explore with the majority, exploit the rest."
}
```
2025-08-01 09:28:02,640 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-01 09:28:02,640 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-01 09:28:02,640 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-01 09:28:02,640 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:28:02,642 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 09:28:02,642 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:28:02,642 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12849.0, 路径长度: 66
2025-08-01 09:28:02,643 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 18, 14, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12849.0}
2025-08-01 09:28:02,643 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-01 09:28:02,643 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-01 09:28:02,643 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:28:02,644 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-01 09:28:02,645 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:28:02,645 - ExplorationExpert - INFO - 探索路径生成完成，成本: 106647.0, 路径长度: 66
2025-08-01 09:28:02,645 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [61, 52, 49, 5, 19, 7, 0, 1, 53, 11, 27, 6, 43, 10, 34, 59, 8, 62, 39, 24, 44, 46, 42, 57, 32, 2, 17, 63, 23, 14, 28, 31, 35, 13, 56, 25, 58, 20, 30, 4, 65, 33, 3, 36, 64, 45, 60, 29, 54, 47, 37, 21, 12, 41, 55, 9, 40, 16, 15, 22, 51, 38, 50, 48, 18, 26], 'cur_cost': 106647.0}
2025-08-01 09:28:02,645 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-01 09:28:02,645 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-01 09:28:02,645 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:28:02,650 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-01 09:28:02,650 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:28:02,650 - ExplorationExpert - INFO - 探索路径生成完成，成本: 62715.0, 路径长度: 66
2025-08-01 09:28:02,651 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [19, 18, 34, 30, 7, 6, 3, 0, 60, 55, 64, 11, 10, 14, 29, 24, 2, 61, 4, 23, 15, 32, 21, 8, 65, 54, 63, 20, 43, 45, 16, 5, 12, 1, 59, 40, 49, 27, 36, 31, 37, 26, 17, 28, 48, 41, 42, 44, 35, 47, 39, 13, 50, 38, 25, 9, 62, 58, 53, 56, 22, 46, 33, 51, 57, 52], 'cur_cost': 62715.0}
2025-08-01 09:28:02,651 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-01 09:28:02,651 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-01 09:28:02,651 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:28:02,653 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 09:28:02,653 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:28:02,653 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12734.0, 路径长度: 66
2025-08-01 09:28:02,654 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 14, 9, 6, 2, 8, 5, 4, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12734.0}
2025-08-01 09:28:02,654 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-01 09:28:02,654 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:28:02,654 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:28:02,654 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 119881.0
2025-08-01 09:28:02,729 - ExploitationExpert - INFO - res_population_num: 6
2025-08-01 09:28:02,729 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9542, 9555.0, 9555, 9592.0]
2025-08-01 09:28:02,729 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 09:28:02,731 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 09:28:02,731 - ExploitationExpert - INFO - populations: [{'tour': [0, 18, 14, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12849.0}, {'tour': [61, 52, 49, 5, 19, 7, 0, 1, 53, 11, 27, 6, 43, 10, 34, 59, 8, 62, 39, 24, 44, 46, 42, 57, 32, 2, 17, 63, 23, 14, 28, 31, 35, 13, 56, 25, 58, 20, 30, 4, 65, 33, 3, 36, 64, 45, 60, 29, 54, 47, 37, 21, 12, 41, 55, 9, 40, 16, 15, 22, 51, 38, 50, 48, 18, 26], 'cur_cost': 106647.0}, {'tour': [19, 18, 34, 30, 7, 6, 3, 0, 60, 55, 64, 11, 10, 14, 29, 24, 2, 61, 4, 23, 15, 32, 21, 8, 65, 54, 63, 20, 43, 45, 16, 5, 12, 1, 59, 40, 49, 27, 36, 31, 37, 26, 17, 28, 48, 41, 42, 44, 35, 47, 39, 13, 50, 38, 25, 9, 62, 58, 53, 56, 22, 46, 33, 51, 57, 52], 'cur_cost': 62715.0}, {'tour': [0, 14, 9, 6, 2, 8, 5, 4, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12734.0}, {'tour': array([63, 52, 12, 64, 48, 24, 43,  6, 22, 53, 28, 37,  0, 54, 41, 21,  5,
       18, 13, 23, 10, 17, 25, 40,  4, 58, 46, 31, 33, 32, 62, 56, 45, 36,
       15,  9, 51, 65, 26,  7, 30, 57, 29, 61, 44, 60, 19, 55, 20,  3, 35,
        2, 16, 11, 47, 42, 14, 34, 50, 49,  1, 39, 59, 38,  8, 27],
      dtype=int64), 'cur_cost': 119881.0}, {'tour': [0, 17, 4, 6, 2, 8, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12702.0}, {'tour': [0, 3, 14, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12377.0}, {'tour': [61, 63, 47, 53, 1, 34, 35, 57, 27, 50, 13, 42, 0, 41, 56, 18, 28, 10, 4, 52, 21, 9, 39, 38, 60, 16, 12, 6, 45, 5, 11, 26, 20, 17, 14, 19, 25, 30, 46, 59, 40, 51, 44, 2, 49, 3, 64, 55, 58, 7, 15, 62, 33, 23, 36, 24, 29, 8, 37, 54, 65, 22, 31, 48, 32, 43], 'cur_cost': 100720.0}, {'tour': [16, 42, 31, 15, 22, 38, 62, 24, 57, 59, 9, 28, 41, 6, 44, 46, 23, 36, 27, 10, 52, 35, 58, 5, 25, 14, 12, 34, 56, 40, 50, 19, 8, 53, 39, 21, 26, 7, 18, 33, 0, 37, 29, 4, 48, 1, 63, 49, 32, 54, 60, 30, 2, 13, 20, 51, 17, 45, 11, 3, 65, 47, 61, 43, 64, 55], 'cur_cost': 109085.0}, {'tour': [26, 4, 64, 44, 0, 13, 52, 21, 37, 57, 25, 23, 29, 53, 50, 24, 42, 20, 51, 34, 12, 16, 7, 11, 27, 6, 3, 33, 55, 54, 58, 61, 65, 8, 15, 10, 49, 60, 41, 48, 9, 18, 39, 14, 63, 17, 1, 36, 30, 47, 56, 62, 5, 2, 28, 32, 38, 59, 22, 35, 46, 31, 43, 40, 45, 19], 'cur_cost': 100244.0}]
2025-08-01 09:28:02,731 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-08-01 09:28:02,732 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-01 09:28:02,732 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([63, 52, 12, 64, 48, 24, 43,  6, 22, 53, 28, 37,  0, 54, 41, 21,  5,
       18, 13, 23, 10, 17, 25, 40,  4, 58, 46, 31, 33, 32, 62, 56, 45, 36,
       15,  9, 51, 65, 26,  7, 30, 57, 29, 61, 44, 60, 19, 55, 20,  3, 35,
        2, 16, 11, 47, 42, 14, 34, 50, 49,  1, 39, 59, 38,  8, 27],
      dtype=int64), 'cur_cost': 119881.0}
2025-08-01 09:28:02,732 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-01 09:28:02,732 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-01 09:28:02,732 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:28:02,738 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-01 09:28:02,738 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:28:02,740 - ExplorationExpert - INFO - 探索路径生成完成，成本: 56801.0, 路径长度: 66
2025-08-01 09:28:02,740 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [16, 21, 4, 0, 2, 3, 13, 8, 58, 20, 33, 25, 34, 14, 10, 56, 59, 22, 5, 57, 63, 7, 37, 17, 1, 54, 18, 19, 27, 31, 12, 28, 36, 29, 11, 9, 24, 6, 55, 47, 50, 41, 48, 49, 42, 44, 26, 15, 39, 45, 38, 46, 30, 35, 32, 61, 62, 64, 65, 53, 60, 23, 40, 43, 51, 52], 'cur_cost': 56801.0}
2025-08-01 09:28:02,740 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-01 09:28:02,740 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:28:02,740 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:28:02,741 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 102168.0
2025-08-01 09:28:02,798 - ExploitationExpert - INFO - res_population_num: 7
2025-08-01 09:28:02,798 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9542, 9555.0, 9555, 9592.0, 9521]
2025-08-01 09:28:02,798 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 09:28:02,801 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 09:28:02,801 - ExploitationExpert - INFO - populations: [{'tour': [0, 18, 14, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12849.0}, {'tour': [61, 52, 49, 5, 19, 7, 0, 1, 53, 11, 27, 6, 43, 10, 34, 59, 8, 62, 39, 24, 44, 46, 42, 57, 32, 2, 17, 63, 23, 14, 28, 31, 35, 13, 56, 25, 58, 20, 30, 4, 65, 33, 3, 36, 64, 45, 60, 29, 54, 47, 37, 21, 12, 41, 55, 9, 40, 16, 15, 22, 51, 38, 50, 48, 18, 26], 'cur_cost': 106647.0}, {'tour': [19, 18, 34, 30, 7, 6, 3, 0, 60, 55, 64, 11, 10, 14, 29, 24, 2, 61, 4, 23, 15, 32, 21, 8, 65, 54, 63, 20, 43, 45, 16, 5, 12, 1, 59, 40, 49, 27, 36, 31, 37, 26, 17, 28, 48, 41, 42, 44, 35, 47, 39, 13, 50, 38, 25, 9, 62, 58, 53, 56, 22, 46, 33, 51, 57, 52], 'cur_cost': 62715.0}, {'tour': [0, 14, 9, 6, 2, 8, 5, 4, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12734.0}, {'tour': array([63, 52, 12, 64, 48, 24, 43,  6, 22, 53, 28, 37,  0, 54, 41, 21,  5,
       18, 13, 23, 10, 17, 25, 40,  4, 58, 46, 31, 33, 32, 62, 56, 45, 36,
       15,  9, 51, 65, 26,  7, 30, 57, 29, 61, 44, 60, 19, 55, 20,  3, 35,
        2, 16, 11, 47, 42, 14, 34, 50, 49,  1, 39, 59, 38,  8, 27],
      dtype=int64), 'cur_cost': 119881.0}, {'tour': [16, 21, 4, 0, 2, 3, 13, 8, 58, 20, 33, 25, 34, 14, 10, 56, 59, 22, 5, 57, 63, 7, 37, 17, 1, 54, 18, 19, 27, 31, 12, 28, 36, 29, 11, 9, 24, 6, 55, 47, 50, 41, 48, 49, 42, 44, 26, 15, 39, 45, 38, 46, 30, 35, 32, 61, 62, 64, 65, 53, 60, 23, 40, 43, 51, 52], 'cur_cost': 56801.0}, {'tour': array([ 3, 54, 15, 58, 50,  6,  7, 59,  0, 64, 27,  4,  1,  5, 41, 49, 14,
       39, 33, 60, 55, 36, 22, 46, 34, 12, 29, 31, 51, 43, 35,  9, 16, 25,
       65, 20, 48, 21,  2, 62, 10, 57, 52, 53, 26, 17, 24, 47, 63, 30, 45,
       18, 38, 23, 37, 42, 11, 32, 13, 44, 56, 61, 40, 28,  8, 19],
      dtype=int64), 'cur_cost': 102168.0}, {'tour': [61, 63, 47, 53, 1, 34, 35, 57, 27, 50, 13, 42, 0, 41, 56, 18, 28, 10, 4, 52, 21, 9, 39, 38, 60, 16, 12, 6, 45, 5, 11, 26, 20, 17, 14, 19, 25, 30, 46, 59, 40, 51, 44, 2, 49, 3, 64, 55, 58, 7, 15, 62, 33, 23, 36, 24, 29, 8, 37, 54, 65, 22, 31, 48, 32, 43], 'cur_cost': 100720.0}, {'tour': [16, 42, 31, 15, 22, 38, 62, 24, 57, 59, 9, 28, 41, 6, 44, 46, 23, 36, 27, 10, 52, 35, 58, 5, 25, 14, 12, 34, 56, 40, 50, 19, 8, 53, 39, 21, 26, 7, 18, 33, 0, 37, 29, 4, 48, 1, 63, 49, 32, 54, 60, 30, 2, 13, 20, 51, 17, 45, 11, 3, 65, 47, 61, 43, 64, 55], 'cur_cost': 109085.0}, {'tour': [26, 4, 64, 44, 0, 13, 52, 21, 37, 57, 25, 23, 29, 53, 50, 24, 42, 20, 51, 34, 12, 16, 7, 11, 27, 6, 3, 33, 55, 54, 58, 61, 65, 8, 15, 10, 49, 60, 41, 48, 9, 18, 39, 14, 63, 17, 1, 36, 30, 47, 56, 62, 5, 2, 28, 32, 38, 59, 22, 35, 46, 31, 43, 40, 45, 19], 'cur_cost': 100244.0}]
2025-08-01 09:28:02,801 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-08-01 09:28:02,801 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-01 09:28:02,802 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([ 3, 54, 15, 58, 50,  6,  7, 59,  0, 64, 27,  4,  1,  5, 41, 49, 14,
       39, 33, 60, 55, 36, 22, 46, 34, 12, 29, 31, 51, 43, 35,  9, 16, 25,
       65, 20, 48, 21,  2, 62, 10, 57, 52, 53, 26, 17, 24, 47, 63, 30, 45,
       18, 38, 23, 37, 42, 11, 32, 13, 44, 56, 61, 40, 28,  8, 19],
      dtype=int64), 'cur_cost': 102168.0}
2025-08-01 09:28:02,802 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-01 09:28:02,802 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:28:02,802 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:28:02,803 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 106130.0
2025-08-01 09:28:02,862 - ExploitationExpert - INFO - res_population_num: 8
2025-08-01 09:28:02,862 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9542, 9555.0, 9555, 9592.0, 9521, 9521]
2025-08-01 09:28:02,862 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-01 09:28:02,865 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 09:28:02,865 - ExploitationExpert - INFO - populations: [{'tour': [0, 18, 14, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12849.0}, {'tour': [61, 52, 49, 5, 19, 7, 0, 1, 53, 11, 27, 6, 43, 10, 34, 59, 8, 62, 39, 24, 44, 46, 42, 57, 32, 2, 17, 63, 23, 14, 28, 31, 35, 13, 56, 25, 58, 20, 30, 4, 65, 33, 3, 36, 64, 45, 60, 29, 54, 47, 37, 21, 12, 41, 55, 9, 40, 16, 15, 22, 51, 38, 50, 48, 18, 26], 'cur_cost': 106647.0}, {'tour': [19, 18, 34, 30, 7, 6, 3, 0, 60, 55, 64, 11, 10, 14, 29, 24, 2, 61, 4, 23, 15, 32, 21, 8, 65, 54, 63, 20, 43, 45, 16, 5, 12, 1, 59, 40, 49, 27, 36, 31, 37, 26, 17, 28, 48, 41, 42, 44, 35, 47, 39, 13, 50, 38, 25, 9, 62, 58, 53, 56, 22, 46, 33, 51, 57, 52], 'cur_cost': 62715.0}, {'tour': [0, 14, 9, 6, 2, 8, 5, 4, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12734.0}, {'tour': array([63, 52, 12, 64, 48, 24, 43,  6, 22, 53, 28, 37,  0, 54, 41, 21,  5,
       18, 13, 23, 10, 17, 25, 40,  4, 58, 46, 31, 33, 32, 62, 56, 45, 36,
       15,  9, 51, 65, 26,  7, 30, 57, 29, 61, 44, 60, 19, 55, 20,  3, 35,
        2, 16, 11, 47, 42, 14, 34, 50, 49,  1, 39, 59, 38,  8, 27],
      dtype=int64), 'cur_cost': 119881.0}, {'tour': [16, 21, 4, 0, 2, 3, 13, 8, 58, 20, 33, 25, 34, 14, 10, 56, 59, 22, 5, 57, 63, 7, 37, 17, 1, 54, 18, 19, 27, 31, 12, 28, 36, 29, 11, 9, 24, 6, 55, 47, 50, 41, 48, 49, 42, 44, 26, 15, 39, 45, 38, 46, 30, 35, 32, 61, 62, 64, 65, 53, 60, 23, 40, 43, 51, 52], 'cur_cost': 56801.0}, {'tour': array([ 3, 54, 15, 58, 50,  6,  7, 59,  0, 64, 27,  4,  1,  5, 41, 49, 14,
       39, 33, 60, 55, 36, 22, 46, 34, 12, 29, 31, 51, 43, 35,  9, 16, 25,
       65, 20, 48, 21,  2, 62, 10, 57, 52, 53, 26, 17, 24, 47, 63, 30, 45,
       18, 38, 23, 37, 42, 11, 32, 13, 44, 56, 61, 40, 28,  8, 19],
      dtype=int64), 'cur_cost': 102168.0}, {'tour': array([35, 12,  5, 19, 36, 32, 27, 47, 28, 63, 26, 51, 58,  4, 61, 46,  1,
       48, 55, 39, 42, 60, 14, 33, 23, 17, 34, 43,  0,  7, 40,  9,  8, 41,
       11, 30,  6, 25, 37, 18, 21,  2, 53, 52, 38, 22, 16, 64, 59, 49, 65,
       24, 10, 56, 45, 13,  3, 29, 57, 20, 62, 54, 50, 44, 15, 31],
      dtype=int64), 'cur_cost': 106130.0}, {'tour': [16, 42, 31, 15, 22, 38, 62, 24, 57, 59, 9, 28, 41, 6, 44, 46, 23, 36, 27, 10, 52, 35, 58, 5, 25, 14, 12, 34, 56, 40, 50, 19, 8, 53, 39, 21, 26, 7, 18, 33, 0, 37, 29, 4, 48, 1, 63, 49, 32, 54, 60, 30, 2, 13, 20, 51, 17, 45, 11, 3, 65, 47, 61, 43, 64, 55], 'cur_cost': 109085.0}, {'tour': [26, 4, 64, 44, 0, 13, 52, 21, 37, 57, 25, 23, 29, 53, 50, 24, 42, 20, 51, 34, 12, 16, 7, 11, 27, 6, 3, 33, 55, 54, 58, 61, 65, 8, 15, 10, 49, 60, 41, 48, 9, 18, 39, 14, 63, 17, 1, 36, 30, 47, 56, 62, 5, 2, 28, 32, 38, 59, 22, 35, 46, 31, 43, 40, 45, 19], 'cur_cost': 100244.0}]
2025-08-01 09:28:02,866 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-08-01 09:28:02,866 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-01 09:28:02,867 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([35, 12,  5, 19, 36, 32, 27, 47, 28, 63, 26, 51, 58,  4, 61, 46,  1,
       48, 55, 39, 42, 60, 14, 33, 23, 17, 34, 43,  0,  7, 40,  9,  8, 41,
       11, 30,  6, 25, 37, 18, 21,  2, 53, 52, 38, 22, 16, 64, 59, 49, 65,
       24, 10, 56, 45, 13,  3, 29, 57, 20, 62, 54, 50, 44, 15, 31],
      dtype=int64), 'cur_cost': 106130.0}
2025-08-01 09:28:02,867 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-01 09:28:02,867 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:28:02,867 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:28:02,867 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 98312.0
2025-08-01 09:28:02,924 - ExploitationExpert - INFO - res_population_num: 9
2025-08-01 09:28:02,925 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9542, 9555.0, 9555, 9592.0, 9521, 9521, 9521]
2025-08-01 09:28:02,925 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-01 09:28:02,928 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 09:28:02,928 - ExploitationExpert - INFO - populations: [{'tour': [0, 18, 14, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12849.0}, {'tour': [61, 52, 49, 5, 19, 7, 0, 1, 53, 11, 27, 6, 43, 10, 34, 59, 8, 62, 39, 24, 44, 46, 42, 57, 32, 2, 17, 63, 23, 14, 28, 31, 35, 13, 56, 25, 58, 20, 30, 4, 65, 33, 3, 36, 64, 45, 60, 29, 54, 47, 37, 21, 12, 41, 55, 9, 40, 16, 15, 22, 51, 38, 50, 48, 18, 26], 'cur_cost': 106647.0}, {'tour': [19, 18, 34, 30, 7, 6, 3, 0, 60, 55, 64, 11, 10, 14, 29, 24, 2, 61, 4, 23, 15, 32, 21, 8, 65, 54, 63, 20, 43, 45, 16, 5, 12, 1, 59, 40, 49, 27, 36, 31, 37, 26, 17, 28, 48, 41, 42, 44, 35, 47, 39, 13, 50, 38, 25, 9, 62, 58, 53, 56, 22, 46, 33, 51, 57, 52], 'cur_cost': 62715.0}, {'tour': [0, 14, 9, 6, 2, 8, 5, 4, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12734.0}, {'tour': array([63, 52, 12, 64, 48, 24, 43,  6, 22, 53, 28, 37,  0, 54, 41, 21,  5,
       18, 13, 23, 10, 17, 25, 40,  4, 58, 46, 31, 33, 32, 62, 56, 45, 36,
       15,  9, 51, 65, 26,  7, 30, 57, 29, 61, 44, 60, 19, 55, 20,  3, 35,
        2, 16, 11, 47, 42, 14, 34, 50, 49,  1, 39, 59, 38,  8, 27],
      dtype=int64), 'cur_cost': 119881.0}, {'tour': [16, 21, 4, 0, 2, 3, 13, 8, 58, 20, 33, 25, 34, 14, 10, 56, 59, 22, 5, 57, 63, 7, 37, 17, 1, 54, 18, 19, 27, 31, 12, 28, 36, 29, 11, 9, 24, 6, 55, 47, 50, 41, 48, 49, 42, 44, 26, 15, 39, 45, 38, 46, 30, 35, 32, 61, 62, 64, 65, 53, 60, 23, 40, 43, 51, 52], 'cur_cost': 56801.0}, {'tour': array([ 3, 54, 15, 58, 50,  6,  7, 59,  0, 64, 27,  4,  1,  5, 41, 49, 14,
       39, 33, 60, 55, 36, 22, 46, 34, 12, 29, 31, 51, 43, 35,  9, 16, 25,
       65, 20, 48, 21,  2, 62, 10, 57, 52, 53, 26, 17, 24, 47, 63, 30, 45,
       18, 38, 23, 37, 42, 11, 32, 13, 44, 56, 61, 40, 28,  8, 19],
      dtype=int64), 'cur_cost': 102168.0}, {'tour': array([35, 12,  5, 19, 36, 32, 27, 47, 28, 63, 26, 51, 58,  4, 61, 46,  1,
       48, 55, 39, 42, 60, 14, 33, 23, 17, 34, 43,  0,  7, 40,  9,  8, 41,
       11, 30,  6, 25, 37, 18, 21,  2, 53, 52, 38, 22, 16, 64, 59, 49, 65,
       24, 10, 56, 45, 13,  3, 29, 57, 20, 62, 54, 50, 44, 15, 31],
      dtype=int64), 'cur_cost': 106130.0}, {'tour': array([13, 17,  8, 16, 48, 20, 61,  5, 11, 33, 62, 58,  4, 42, 43, 51, 32,
       57,  2, 56, 46, 39, 22, 14, 59, 55, 15, 49, 18, 37, 28, 21, 19,  7,
        3, 45, 36,  0, 64, 54, 41, 10, 24, 12, 38, 52, 35,  9, 34, 50, 25,
       31, 60, 40, 23, 29, 27, 63, 47, 53,  1,  6, 44, 30, 26, 65],
      dtype=int64), 'cur_cost': 98312.0}, {'tour': [26, 4, 64, 44, 0, 13, 52, 21, 37, 57, 25, 23, 29, 53, 50, 24, 42, 20, 51, 34, 12, 16, 7, 11, 27, 6, 3, 33, 55, 54, 58, 61, 65, 8, 15, 10, 49, 60, 41, 48, 9, 18, 39, 14, 63, 17, 1, 36, 30, 47, 56, 62, 5, 2, 28, 32, 38, 59, 22, 35, 46, 31, 43, 40, 45, 19], 'cur_cost': 100244.0}]
2025-08-01 09:28:02,930 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-08-01 09:28:02,930 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-01 09:28:02,930 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([13, 17,  8, 16, 48, 20, 61,  5, 11, 33, 62, 58,  4, 42, 43, 51, 32,
       57,  2, 56, 46, 39, 22, 14, 59, 55, 15, 49, 18, 37, 28, 21, 19,  7,
        3, 45, 36,  0, 64, 54, 41, 10, 24, 12, 38, 52, 35,  9, 34, 50, 25,
       31, 60, 40, 23, 29, 27, 63, 47, 53,  1,  6, 44, 30, 26, 65],
      dtype=int64), 'cur_cost': 98312.0}
2025-08-01 09:28:02,930 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-01 09:28:02,930 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:28:02,930 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:28:02,931 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 107322.0
2025-08-01 09:28:03,004 - ExploitationExpert - INFO - res_population_num: 10
2025-08-01 09:28:03,004 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9542, 9555.0, 9555, 9592.0, 9521, 9521, 9521, 9521]
2025-08-01 09:28:03,005 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-01 09:28:03,008 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 09:28:03,008 - ExploitationExpert - INFO - populations: [{'tour': [0, 18, 14, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12849.0}, {'tour': [61, 52, 49, 5, 19, 7, 0, 1, 53, 11, 27, 6, 43, 10, 34, 59, 8, 62, 39, 24, 44, 46, 42, 57, 32, 2, 17, 63, 23, 14, 28, 31, 35, 13, 56, 25, 58, 20, 30, 4, 65, 33, 3, 36, 64, 45, 60, 29, 54, 47, 37, 21, 12, 41, 55, 9, 40, 16, 15, 22, 51, 38, 50, 48, 18, 26], 'cur_cost': 106647.0}, {'tour': [19, 18, 34, 30, 7, 6, 3, 0, 60, 55, 64, 11, 10, 14, 29, 24, 2, 61, 4, 23, 15, 32, 21, 8, 65, 54, 63, 20, 43, 45, 16, 5, 12, 1, 59, 40, 49, 27, 36, 31, 37, 26, 17, 28, 48, 41, 42, 44, 35, 47, 39, 13, 50, 38, 25, 9, 62, 58, 53, 56, 22, 46, 33, 51, 57, 52], 'cur_cost': 62715.0}, {'tour': [0, 14, 9, 6, 2, 8, 5, 4, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12734.0}, {'tour': array([63, 52, 12, 64, 48, 24, 43,  6, 22, 53, 28, 37,  0, 54, 41, 21,  5,
       18, 13, 23, 10, 17, 25, 40,  4, 58, 46, 31, 33, 32, 62, 56, 45, 36,
       15,  9, 51, 65, 26,  7, 30, 57, 29, 61, 44, 60, 19, 55, 20,  3, 35,
        2, 16, 11, 47, 42, 14, 34, 50, 49,  1, 39, 59, 38,  8, 27],
      dtype=int64), 'cur_cost': 119881.0}, {'tour': [16, 21, 4, 0, 2, 3, 13, 8, 58, 20, 33, 25, 34, 14, 10, 56, 59, 22, 5, 57, 63, 7, 37, 17, 1, 54, 18, 19, 27, 31, 12, 28, 36, 29, 11, 9, 24, 6, 55, 47, 50, 41, 48, 49, 42, 44, 26, 15, 39, 45, 38, 46, 30, 35, 32, 61, 62, 64, 65, 53, 60, 23, 40, 43, 51, 52], 'cur_cost': 56801.0}, {'tour': array([ 3, 54, 15, 58, 50,  6,  7, 59,  0, 64, 27,  4,  1,  5, 41, 49, 14,
       39, 33, 60, 55, 36, 22, 46, 34, 12, 29, 31, 51, 43, 35,  9, 16, 25,
       65, 20, 48, 21,  2, 62, 10, 57, 52, 53, 26, 17, 24, 47, 63, 30, 45,
       18, 38, 23, 37, 42, 11, 32, 13, 44, 56, 61, 40, 28,  8, 19],
      dtype=int64), 'cur_cost': 102168.0}, {'tour': array([35, 12,  5, 19, 36, 32, 27, 47, 28, 63, 26, 51, 58,  4, 61, 46,  1,
       48, 55, 39, 42, 60, 14, 33, 23, 17, 34, 43,  0,  7, 40,  9,  8, 41,
       11, 30,  6, 25, 37, 18, 21,  2, 53, 52, 38, 22, 16, 64, 59, 49, 65,
       24, 10, 56, 45, 13,  3, 29, 57, 20, 62, 54, 50, 44, 15, 31],
      dtype=int64), 'cur_cost': 106130.0}, {'tour': array([13, 17,  8, 16, 48, 20, 61,  5, 11, 33, 62, 58,  4, 42, 43, 51, 32,
       57,  2, 56, 46, 39, 22, 14, 59, 55, 15, 49, 18, 37, 28, 21, 19,  7,
        3, 45, 36,  0, 64, 54, 41, 10, 24, 12, 38, 52, 35,  9, 34, 50, 25,
       31, 60, 40, 23, 29, 27, 63, 47, 53,  1,  6, 44, 30, 26, 65],
      dtype=int64), 'cur_cost': 98312.0}, {'tour': array([ 9, 55, 27, 14, 40, 35, 50, 21, 25,  6, 47, 59, 63, 45, 28, 10, 18,
       36, 48, 49, 51, 43, 60, 41, 22, 65, 37, 20, 38, 17, 54, 19, 39, 58,
       33, 11, 46, 26, 57, 15, 52, 53,  1,  7,  8, 62, 42, 32, 16, 64, 23,
        0,  3, 56, 44, 30,  2,  4, 12, 29, 13, 34,  5, 61, 24, 31],
      dtype=int64), 'cur_cost': 107322.0}]
2025-08-01 09:28:03,010 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-08-01 09:28:03,010 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-08-01 09:28:03,011 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([ 9, 55, 27, 14, 40, 35, 50, 21, 25,  6, 47, 59, 63, 45, 28, 10, 18,
       36, 48, 49, 51, 43, 60, 41, 22, 65, 37, 20, 38, 17, 54, 19, 39, 58,
       33, 11, 46, 26, 57, 15, 52, 53,  1,  7,  8, 62, 42, 32, 16, 64, 23,
        0,  3, 56, 44, 30,  2,  4, 12, 29, 13, 34,  5, 61, 24, 31],
      dtype=int64), 'cur_cost': 107322.0}
2025-08-01 09:28:03,012 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 14, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12849.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [61, 52, 49, 5, 19, 7, 0, 1, 53, 11, 27, 6, 43, 10, 34, 59, 8, 62, 39, 24, 44, 46, 42, 57, 32, 2, 17, 63, 23, 14, 28, 31, 35, 13, 56, 25, 58, 20, 30, 4, 65, 33, 3, 36, 64, 45, 60, 29, 54, 47, 37, 21, 12, 41, 55, 9, 40, 16, 15, 22, 51, 38, 50, 48, 18, 26], 'cur_cost': 106647.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [19, 18, 34, 30, 7, 6, 3, 0, 60, 55, 64, 11, 10, 14, 29, 24, 2, 61, 4, 23, 15, 32, 21, 8, 65, 54, 63, 20, 43, 45, 16, 5, 12, 1, 59, 40, 49, 27, 36, 31, 37, 26, 17, 28, 48, 41, 42, 44, 35, 47, 39, 13, 50, 38, 25, 9, 62, 58, 53, 56, 22, 46, 33, 51, 57, 52], 'cur_cost': 62715.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 9, 6, 2, 8, 5, 4, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12734.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([63, 52, 12, 64, 48, 24, 43,  6, 22, 53, 28, 37,  0, 54, 41, 21,  5,
       18, 13, 23, 10, 17, 25, 40,  4, 58, 46, 31, 33, 32, 62, 56, 45, 36,
       15,  9, 51, 65, 26,  7, 30, 57, 29, 61, 44, 60, 19, 55, 20,  3, 35,
        2, 16, 11, 47, 42, 14, 34, 50, 49,  1, 39, 59, 38,  8, 27],
      dtype=int64), 'cur_cost': 119881.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [16, 21, 4, 0, 2, 3, 13, 8, 58, 20, 33, 25, 34, 14, 10, 56, 59, 22, 5, 57, 63, 7, 37, 17, 1, 54, 18, 19, 27, 31, 12, 28, 36, 29, 11, 9, 24, 6, 55, 47, 50, 41, 48, 49, 42, 44, 26, 15, 39, 45, 38, 46, 30, 35, 32, 61, 62, 64, 65, 53, 60, 23, 40, 43, 51, 52], 'cur_cost': 56801.0}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 3, 54, 15, 58, 50,  6,  7, 59,  0, 64, 27,  4,  1,  5, 41, 49, 14,
       39, 33, 60, 55, 36, 22, 46, 34, 12, 29, 31, 51, 43, 35,  9, 16, 25,
       65, 20, 48, 21,  2, 62, 10, 57, 52, 53, 26, 17, 24, 47, 63, 30, 45,
       18, 38, 23, 37, 42, 11, 32, 13, 44, 56, 61, 40, 28,  8, 19],
      dtype=int64), 'cur_cost': 102168.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([35, 12,  5, 19, 36, 32, 27, 47, 28, 63, 26, 51, 58,  4, 61, 46,  1,
       48, 55, 39, 42, 60, 14, 33, 23, 17, 34, 43,  0,  7, 40,  9,  8, 41,
       11, 30,  6, 25, 37, 18, 21,  2, 53, 52, 38, 22, 16, 64, 59, 49, 65,
       24, 10, 56, 45, 13,  3, 29, 57, 20, 62, 54, 50, 44, 15, 31],
      dtype=int64), 'cur_cost': 106130.0}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([13, 17,  8, 16, 48, 20, 61,  5, 11, 33, 62, 58,  4, 42, 43, 51, 32,
       57,  2, 56, 46, 39, 22, 14, 59, 55, 15, 49, 18, 37, 28, 21, 19,  7,
        3, 45, 36,  0, 64, 54, 41, 10, 24, 12, 38, 52, 35,  9, 34, 50, 25,
       31, 60, 40, 23, 29, 27, 63, 47, 53,  1,  6, 44, 30, 26, 65],
      dtype=int64), 'cur_cost': 98312.0}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 9, 55, 27, 14, 40, 35, 50, 21, 25,  6, 47, 59, 63, 45, 28, 10, 18,
       36, 48, 49, 51, 43, 60, 41, 22, 65, 37, 20, 38, 17, 54, 19, 39, 58,
       33, 11, 46, 26, 57, 15, 52, 53,  1,  7,  8, 62, 42, 32, 16, 64, 23,
        0,  3, 56, 44, 30,  2,  4, 12, 29, 13, 34,  5, 61, 24, 31],
      dtype=int64), 'cur_cost': 107322.0}}]
2025-08-01 09:28:03,012 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-01 09:28:03,012 - StatsExpert - INFO - 开始统计分析
2025-08-01 09:28:03,029 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12734.0, 多样性=0.946
2025-08-01 09:28:03,029 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-01 09:28:03,029 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-01 09:28:03,030 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-01 09:28:03,031 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.11050740279013356, 'best_improvement': -0.028843823220489617}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.04889884285181036}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 10, 'new_count': 10, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-01 09:28:03,032 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-01 09:28:03,033 - __main__ - INFO - composite13_66 开始进化第 3 代
2025-08-01 09:28:03,033 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-01 09:28:03,033 - StatsExpert - INFO - 开始统计分析
2025-08-01 09:28:03,050 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12734.0, 多样性=0.946
2025-08-01 09:28:03,050 - PathExpert - INFO - 开始路径结构分析
2025-08-01 09:28:03,053 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.030
2025-08-01 09:28:03,054 - EliteExpert - INFO - 开始精英解分析
2025-08-01 09:28:03,056 - EliteExpert - INFO - 精英解分析完成: 精英解数量=10, 多样性=0.630
2025-08-01 09:28:03,057 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-01 09:28:03,058 - LandscapeExpert - INFO - 从统计报告生成模拟数据: 10个路径, 10个适应度值
2025-08-01 09:28:03,058 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-01 09:28:03,062 - core.algorithms.fitness_landscape_analyzer - ERROR - 景观分析失败: '>' not supported between instances of 'str' and 'int'
2025-08-01 09:28:03,062 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-01 09:28:03,122 - visualization.landscape_visualizer - INFO - 图表已保存: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_iter_3.html
2025-08-01 09:28:03,154 - visualization.landscape_visualizer - INFO - 图表已保存: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_iter_3.html
2025-08-01 09:28:03,154 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 3
2025-08-01 09:28:03,155 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-01 09:28:03,155 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.0980秒
2025-08-01 09:28:03,155 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754011683.0624316, 'performance_metrics': {}}}
2025-08-01 09:28:03,155 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-01 09:28:03,155 - StrategyExpert - INFO - 开始策略分配分析
2025-08-01 09:28:03,155 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 12734.0
  • mean_cost: 78555.9
- Search-space: ruggedness 0.5, modality uni-modal, deceptiveness low
- Evolution phase: exploitation
- Landscape focus suggestion: exploitation
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 1): [{"type": "unexplored_space", "potential": "high", "description": "未充分探索的搜索空间", "recommendation": "增加探索性操作"}]
- Last-iteration feedback: {'overall_score': 40, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvemen...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-08-01 09:28:03,156 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-08-01 09:28:03,156 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-01 09:28:04,741 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.4,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Exploitation phase with low diversity and unexplored space; moderate exploration is maintained."
}
```
2025-08-01 09:28:04,742 - utils.utils - INFO - 成功从JSON中提取数据
2025-08-01 09:28:04,742 - StrategyExpert - INFO - 策略分配完成: ['exploit', 'exploit', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-08-01 09:28:04,742 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'exploit', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-08-01 09:28:04,742 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.4,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Exploitation phase with low diversity and unexplored space; moderate exploration is maintained."
}
```
2025-08-01 09:28:04,742 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-01 09:28:04,742 - __main__ - INFO - 策略分配: ['exploit', 'exploit', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-08-01 09:28:04,742 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.4,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Exploitation phase with low diversity and unexplored space; moderate exploration is maintained."
}
```
2025-08-01 09:28:04,742 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-01 09:28:04,743 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-01 09:28:04,743 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:28:04,743 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:28:04,743 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 106646.0
2025-08-01 09:28:04,810 - ExploitationExpert - INFO - res_population_num: 11
2025-08-01 09:28:04,810 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9542, 9555.0, 9555, 9592.0, 9521]
2025-08-01 09:28:04,810 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 09:28:04,814 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 09:28:04,814 - ExploitationExpert - INFO - populations: [{'tour': array([53, 57, 56, 10,  5, 33, 55, 41, 32, 26, 52, 35, 36, 59,  9, 63, 21,
        3, 23, 43, 18,  4, 27, 42, 64, 48, 12, 17, 29, 22, 46, 44, 13, 37,
       49, 16,  8, 54,  1, 50, 19,  0, 60, 65, 40, 51, 58, 61, 31,  7, 14,
       25, 45,  6, 39, 11, 47, 34, 62, 38, 24, 15,  2, 30, 20, 28],
      dtype=int64), 'cur_cost': 106646.0}, {'tour': [61, 52, 49, 5, 19, 7, 0, 1, 53, 11, 27, 6, 43, 10, 34, 59, 8, 62, 39, 24, 44, 46, 42, 57, 32, 2, 17, 63, 23, 14, 28, 31, 35, 13, 56, 25, 58, 20, 30, 4, 65, 33, 3, 36, 64, 45, 60, 29, 54, 47, 37, 21, 12, 41, 55, 9, 40, 16, 15, 22, 51, 38, 50, 48, 18, 26], 'cur_cost': 106647.0}, {'tour': [19, 18, 34, 30, 7, 6, 3, 0, 60, 55, 64, 11, 10, 14, 29, 24, 2, 61, 4, 23, 15, 32, 21, 8, 65, 54, 63, 20, 43, 45, 16, 5, 12, 1, 59, 40, 49, 27, 36, 31, 37, 26, 17, 28, 48, 41, 42, 44, 35, 47, 39, 13, 50, 38, 25, 9, 62, 58, 53, 56, 22, 46, 33, 51, 57, 52], 'cur_cost': 62715.0}, {'tour': [0, 14, 9, 6, 2, 8, 5, 4, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12734.0}, {'tour': [63, 52, 12, 64, 48, 24, 43, 6, 22, 53, 28, 37, 0, 54, 41, 21, 5, 18, 13, 23, 10, 17, 25, 40, 4, 58, 46, 31, 33, 32, 62, 56, 45, 36, 15, 9, 51, 65, 26, 7, 30, 57, 29, 61, 44, 60, 19, 55, 20, 3, 35, 2, 16, 11, 47, 42, 14, 34, 50, 49, 1, 39, 59, 38, 8, 27], 'cur_cost': 119881.0}, {'tour': [16, 21, 4, 0, 2, 3, 13, 8, 58, 20, 33, 25, 34, 14, 10, 56, 59, 22, 5, 57, 63, 7, 37, 17, 1, 54, 18, 19, 27, 31, 12, 28, 36, 29, 11, 9, 24, 6, 55, 47, 50, 41, 48, 49, 42, 44, 26, 15, 39, 45, 38, 46, 30, 35, 32, 61, 62, 64, 65, 53, 60, 23, 40, 43, 51, 52], 'cur_cost': 56801.0}, {'tour': [3, 54, 15, 58, 50, 6, 7, 59, 0, 64, 27, 4, 1, 5, 41, 49, 14, 39, 33, 60, 55, 36, 22, 46, 34, 12, 29, 31, 51, 43, 35, 9, 16, 25, 65, 20, 48, 21, 2, 62, 10, 57, 52, 53, 26, 17, 24, 47, 63, 30, 45, 18, 38, 23, 37, 42, 11, 32, 13, 44, 56, 61, 40, 28, 8, 19], 'cur_cost': 102168.0}, {'tour': [35, 12, 5, 19, 36, 32, 27, 47, 28, 63, 26, 51, 58, 4, 61, 46, 1, 48, 55, 39, 42, 60, 14, 33, 23, 17, 34, 43, 0, 7, 40, 9, 8, 41, 11, 30, 6, 25, 37, 18, 21, 2, 53, 52, 38, 22, 16, 64, 59, 49, 65, 24, 10, 56, 45, 13, 3, 29, 57, 20, 62, 54, 50, 44, 15, 31], 'cur_cost': 106130.0}, {'tour': [13, 17, 8, 16, 48, 20, 61, 5, 11, 33, 62, 58, 4, 42, 43, 51, 32, 57, 2, 56, 46, 39, 22, 14, 59, 55, 15, 49, 18, 37, 28, 21, 19, 7, 3, 45, 36, 0, 64, 54, 41, 10, 24, 12, 38, 52, 35, 9, 34, 50, 25, 31, 60, 40, 23, 29, 27, 63, 47, 53, 1, 6, 44, 30, 26, 65], 'cur_cost': 98312.0}, {'tour': [9, 55, 27, 14, 40, 35, 50, 21, 25, 6, 47, 59, 63, 45, 28, 10, 18, 36, 48, 49, 51, 43, 60, 41, 22, 65, 37, 20, 38, 17, 54, 19, 39, 58, 33, 11, 46, 26, 57, 15, 52, 53, 1, 7, 8, 62, 42, 32, 16, 64, 23, 0, 3, 56, 44, 30, 2, 4, 12, 29, 13, 34, 5, 61, 24, 31], 'cur_cost': 107322.0}]
2025-08-01 09:28:04,815 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-08-01 09:28:04,815 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-08-01 09:28:04,816 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([53, 57, 56, 10,  5, 33, 55, 41, 32, 26, 52, 35, 36, 59,  9, 63, 21,
        3, 23, 43, 18,  4, 27, 42, 64, 48, 12, 17, 29, 22, 46, 44, 13, 37,
       49, 16,  8, 54,  1, 50, 19,  0, 60, 65, 40, 51, 58, 61, 31,  7, 14,
       25, 45,  6, 39, 11, 47, 34, 62, 38, 24, 15,  2, 30, 20, 28],
      dtype=int64), 'cur_cost': 106646.0}
2025-08-01 09:28:04,816 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-01 09:28:04,816 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:28:04,816 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:28:04,816 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 103583.0
2025-08-01 09:28:04,879 - ExploitationExpert - INFO - res_population_num: 11
2025-08-01 09:28:04,879 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9542, 9555.0, 9555, 9592.0, 9521]
2025-08-01 09:28:04,879 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 09:28:04,884 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 09:28:04,884 - ExploitationExpert - INFO - populations: [{'tour': array([53, 57, 56, 10,  5, 33, 55, 41, 32, 26, 52, 35, 36, 59,  9, 63, 21,
        3, 23, 43, 18,  4, 27, 42, 64, 48, 12, 17, 29, 22, 46, 44, 13, 37,
       49, 16,  8, 54,  1, 50, 19,  0, 60, 65, 40, 51, 58, 61, 31,  7, 14,
       25, 45,  6, 39, 11, 47, 34, 62, 38, 24, 15,  2, 30, 20, 28],
      dtype=int64), 'cur_cost': 106646.0}, {'tour': array([29, 40, 46, 32, 17, 44, 30, 64, 13, 23, 15, 52,  3,  7, 62,  1, 58,
       59, 10, 21,  4,  9, 60, 47, 24, 31, 16,  2, 18, 50, 57, 36, 56, 48,
       53, 55, 14, 33, 39, 26, 38, 19, 20, 34, 41, 65, 43, 37, 63, 28, 61,
        5, 35, 54, 22, 11,  0,  8, 42, 49,  6, 25, 45, 51, 27, 12],
      dtype=int64), 'cur_cost': 103583.0}, {'tour': [19, 18, 34, 30, 7, 6, 3, 0, 60, 55, 64, 11, 10, 14, 29, 24, 2, 61, 4, 23, 15, 32, 21, 8, 65, 54, 63, 20, 43, 45, 16, 5, 12, 1, 59, 40, 49, 27, 36, 31, 37, 26, 17, 28, 48, 41, 42, 44, 35, 47, 39, 13, 50, 38, 25, 9, 62, 58, 53, 56, 22, 46, 33, 51, 57, 52], 'cur_cost': 62715.0}, {'tour': [0, 14, 9, 6, 2, 8, 5, 4, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12734.0}, {'tour': [63, 52, 12, 64, 48, 24, 43, 6, 22, 53, 28, 37, 0, 54, 41, 21, 5, 18, 13, 23, 10, 17, 25, 40, 4, 58, 46, 31, 33, 32, 62, 56, 45, 36, 15, 9, 51, 65, 26, 7, 30, 57, 29, 61, 44, 60, 19, 55, 20, 3, 35, 2, 16, 11, 47, 42, 14, 34, 50, 49, 1, 39, 59, 38, 8, 27], 'cur_cost': 119881.0}, {'tour': [16, 21, 4, 0, 2, 3, 13, 8, 58, 20, 33, 25, 34, 14, 10, 56, 59, 22, 5, 57, 63, 7, 37, 17, 1, 54, 18, 19, 27, 31, 12, 28, 36, 29, 11, 9, 24, 6, 55, 47, 50, 41, 48, 49, 42, 44, 26, 15, 39, 45, 38, 46, 30, 35, 32, 61, 62, 64, 65, 53, 60, 23, 40, 43, 51, 52], 'cur_cost': 56801.0}, {'tour': [3, 54, 15, 58, 50, 6, 7, 59, 0, 64, 27, 4, 1, 5, 41, 49, 14, 39, 33, 60, 55, 36, 22, 46, 34, 12, 29, 31, 51, 43, 35, 9, 16, 25, 65, 20, 48, 21, 2, 62, 10, 57, 52, 53, 26, 17, 24, 47, 63, 30, 45, 18, 38, 23, 37, 42, 11, 32, 13, 44, 56, 61, 40, 28, 8, 19], 'cur_cost': 102168.0}, {'tour': [35, 12, 5, 19, 36, 32, 27, 47, 28, 63, 26, 51, 58, 4, 61, 46, 1, 48, 55, 39, 42, 60, 14, 33, 23, 17, 34, 43, 0, 7, 40, 9, 8, 41, 11, 30, 6, 25, 37, 18, 21, 2, 53, 52, 38, 22, 16, 64, 59, 49, 65, 24, 10, 56, 45, 13, 3, 29, 57, 20, 62, 54, 50, 44, 15, 31], 'cur_cost': 106130.0}, {'tour': [13, 17, 8, 16, 48, 20, 61, 5, 11, 33, 62, 58, 4, 42, 43, 51, 32, 57, 2, 56, 46, 39, 22, 14, 59, 55, 15, 49, 18, 37, 28, 21, 19, 7, 3, 45, 36, 0, 64, 54, 41, 10, 24, 12, 38, 52, 35, 9, 34, 50, 25, 31, 60, 40, 23, 29, 27, 63, 47, 53, 1, 6, 44, 30, 26, 65], 'cur_cost': 98312.0}, {'tour': [9, 55, 27, 14, 40, 35, 50, 21, 25, 6, 47, 59, 63, 45, 28, 10, 18, 36, 48, 49, 51, 43, 60, 41, 22, 65, 37, 20, 38, 17, 54, 19, 39, 58, 33, 11, 46, 26, 57, 15, 52, 53, 1, 7, 8, 62, 42, 32, 16, 64, 23, 0, 3, 56, 44, 30, 2, 4, 12, 29, 13, 34, 5, 61, 24, 31], 'cur_cost': 107322.0}]
2025-08-01 09:28:04,885 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-08-01 09:28:04,885 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-08-01 09:28:04,885 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([29, 40, 46, 32, 17, 44, 30, 64, 13, 23, 15, 52,  3,  7, 62,  1, 58,
       59, 10, 21,  4,  9, 60, 47, 24, 31, 16,  2, 18, 50, 57, 36, 56, 48,
       53, 55, 14, 33, 39, 26, 38, 19, 20, 34, 41, 65, 43, 37, 63, 28, 61,
        5, 35, 54, 22, 11,  0,  8, 42, 49,  6, 25, 45, 51, 27, 12],
      dtype=int64), 'cur_cost': 103583.0}
2025-08-01 09:28:04,886 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-01 09:28:04,886 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:28:04,886 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:28:04,886 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 104070.0
2025-08-01 09:28:04,947 - ExploitationExpert - INFO - res_population_num: 11
2025-08-01 09:28:04,947 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9542, 9555.0, 9555, 9592.0, 9521]
2025-08-01 09:28:04,947 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 09:28:04,951 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 09:28:04,951 - ExploitationExpert - INFO - populations: [{'tour': array([53, 57, 56, 10,  5, 33, 55, 41, 32, 26, 52, 35, 36, 59,  9, 63, 21,
        3, 23, 43, 18,  4, 27, 42, 64, 48, 12, 17, 29, 22, 46, 44, 13, 37,
       49, 16,  8, 54,  1, 50, 19,  0, 60, 65, 40, 51, 58, 61, 31,  7, 14,
       25, 45,  6, 39, 11, 47, 34, 62, 38, 24, 15,  2, 30, 20, 28],
      dtype=int64), 'cur_cost': 106646.0}, {'tour': array([29, 40, 46, 32, 17, 44, 30, 64, 13, 23, 15, 52,  3,  7, 62,  1, 58,
       59, 10, 21,  4,  9, 60, 47, 24, 31, 16,  2, 18, 50, 57, 36, 56, 48,
       53, 55, 14, 33, 39, 26, 38, 19, 20, 34, 41, 65, 43, 37, 63, 28, 61,
        5, 35, 54, 22, 11,  0,  8, 42, 49,  6, 25, 45, 51, 27, 12],
      dtype=int64), 'cur_cost': 103583.0}, {'tour': array([61, 43, 62, 55, 25, 37, 11, 28, 14, 47, 35, 16, 59, 17, 13, 36, 23,
       45, 39, 18, 56,  4,  9,  1,  8, 53, 27, 49, 34, 65, 54,  2,  6, 24,
       52, 40, 32, 31, 20, 19, 44, 12, 63, 26, 60, 15,  0,  3, 46, 22, 33,
       64,  7, 51, 29, 58, 10, 50, 57, 48,  5, 30, 21, 38, 41, 42],
      dtype=int64), 'cur_cost': 104070.0}, {'tour': [0, 14, 9, 6, 2, 8, 5, 4, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12734.0}, {'tour': [63, 52, 12, 64, 48, 24, 43, 6, 22, 53, 28, 37, 0, 54, 41, 21, 5, 18, 13, 23, 10, 17, 25, 40, 4, 58, 46, 31, 33, 32, 62, 56, 45, 36, 15, 9, 51, 65, 26, 7, 30, 57, 29, 61, 44, 60, 19, 55, 20, 3, 35, 2, 16, 11, 47, 42, 14, 34, 50, 49, 1, 39, 59, 38, 8, 27], 'cur_cost': 119881.0}, {'tour': [16, 21, 4, 0, 2, 3, 13, 8, 58, 20, 33, 25, 34, 14, 10, 56, 59, 22, 5, 57, 63, 7, 37, 17, 1, 54, 18, 19, 27, 31, 12, 28, 36, 29, 11, 9, 24, 6, 55, 47, 50, 41, 48, 49, 42, 44, 26, 15, 39, 45, 38, 46, 30, 35, 32, 61, 62, 64, 65, 53, 60, 23, 40, 43, 51, 52], 'cur_cost': 56801.0}, {'tour': [3, 54, 15, 58, 50, 6, 7, 59, 0, 64, 27, 4, 1, 5, 41, 49, 14, 39, 33, 60, 55, 36, 22, 46, 34, 12, 29, 31, 51, 43, 35, 9, 16, 25, 65, 20, 48, 21, 2, 62, 10, 57, 52, 53, 26, 17, 24, 47, 63, 30, 45, 18, 38, 23, 37, 42, 11, 32, 13, 44, 56, 61, 40, 28, 8, 19], 'cur_cost': 102168.0}, {'tour': [35, 12, 5, 19, 36, 32, 27, 47, 28, 63, 26, 51, 58, 4, 61, 46, 1, 48, 55, 39, 42, 60, 14, 33, 23, 17, 34, 43, 0, 7, 40, 9, 8, 41, 11, 30, 6, 25, 37, 18, 21, 2, 53, 52, 38, 22, 16, 64, 59, 49, 65, 24, 10, 56, 45, 13, 3, 29, 57, 20, 62, 54, 50, 44, 15, 31], 'cur_cost': 106130.0}, {'tour': [13, 17, 8, 16, 48, 20, 61, 5, 11, 33, 62, 58, 4, 42, 43, 51, 32, 57, 2, 56, 46, 39, 22, 14, 59, 55, 15, 49, 18, 37, 28, 21, 19, 7, 3, 45, 36, 0, 64, 54, 41, 10, 24, 12, 38, 52, 35, 9, 34, 50, 25, 31, 60, 40, 23, 29, 27, 63, 47, 53, 1, 6, 44, 30, 26, 65], 'cur_cost': 98312.0}, {'tour': [9, 55, 27, 14, 40, 35, 50, 21, 25, 6, 47, 59, 63, 45, 28, 10, 18, 36, 48, 49, 51, 43, 60, 41, 22, 65, 37, 20, 38, 17, 54, 19, 39, 58, 33, 11, 46, 26, 57, 15, 52, 53, 1, 7, 8, 62, 42, 32, 16, 64, 23, 0, 3, 56, 44, 30, 2, 4, 12, 29, 13, 34, 5, 61, 24, 31], 'cur_cost': 107322.0}]
2025-08-01 09:28:04,953 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-08-01 09:28:04,953 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-08-01 09:28:04,953 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([61, 43, 62, 55, 25, 37, 11, 28, 14, 47, 35, 16, 59, 17, 13, 36, 23,
       45, 39, 18, 56,  4,  9,  1,  8, 53, 27, 49, 34, 65, 54,  2,  6, 24,
       52, 40, 32, 31, 20, 19, 44, 12, 63, 26, 60, 15,  0,  3, 46, 22, 33,
       64,  7, 51, 29, 58, 10, 50, 57, 48,  5, 30, 21, 38, 41, 42],
      dtype=int64), 'cur_cost': 104070.0}
2025-08-01 09:28:04,953 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-01 09:28:04,954 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-01 09:28:04,954 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:28:04,956 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 09:28:04,956 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:28:04,956 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12837.0, 路径长度: 66
2025-08-01 09:28:04,956 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 9, 18, 2, 8, 5, 4, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12837.0}
2025-08-01 09:28:04,957 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-01 09:28:04,957 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-01 09:28:04,957 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:28:04,959 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 09:28:04,959 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:28:04,959 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12771.0, 路径长度: 66
2025-08-01 09:28:04,959 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12771.0}
2025-08-01 09:28:04,959 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-01 09:28:04,959 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:28:04,960 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:28:04,960 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 104767.0
2025-08-01 09:28:05,024 - ExploitationExpert - INFO - res_population_num: 11
2025-08-01 09:28:05,024 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9542, 9555.0, 9555, 9592.0, 9521]
2025-08-01 09:28:05,024 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 09:28:05,029 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 09:28:05,029 - ExploitationExpert - INFO - populations: [{'tour': array([53, 57, 56, 10,  5, 33, 55, 41, 32, 26, 52, 35, 36, 59,  9, 63, 21,
        3, 23, 43, 18,  4, 27, 42, 64, 48, 12, 17, 29, 22, 46, 44, 13, 37,
       49, 16,  8, 54,  1, 50, 19,  0, 60, 65, 40, 51, 58, 61, 31,  7, 14,
       25, 45,  6, 39, 11, 47, 34, 62, 38, 24, 15,  2, 30, 20, 28],
      dtype=int64), 'cur_cost': 106646.0}, {'tour': array([29, 40, 46, 32, 17, 44, 30, 64, 13, 23, 15, 52,  3,  7, 62,  1, 58,
       59, 10, 21,  4,  9, 60, 47, 24, 31, 16,  2, 18, 50, 57, 36, 56, 48,
       53, 55, 14, 33, 39, 26, 38, 19, 20, 34, 41, 65, 43, 37, 63, 28, 61,
        5, 35, 54, 22, 11,  0,  8, 42, 49,  6, 25, 45, 51, 27, 12],
      dtype=int64), 'cur_cost': 103583.0}, {'tour': array([61, 43, 62, 55, 25, 37, 11, 28, 14, 47, 35, 16, 59, 17, 13, 36, 23,
       45, 39, 18, 56,  4,  9,  1,  8, 53, 27, 49, 34, 65, 54,  2,  6, 24,
       52, 40, 32, 31, 20, 19, 44, 12, 63, 26, 60, 15,  0,  3, 46, 22, 33,
       64,  7, 51, 29, 58, 10, 50, 57, 48,  5, 30, 21, 38, 41, 42],
      dtype=int64), 'cur_cost': 104070.0}, {'tour': [0, 9, 18, 2, 8, 5, 4, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12837.0}, {'tour': [0, 12, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12771.0}, {'tour': array([15, 41, 14, 19, 55, 27, 48, 20, 32,  9, 45, 26, 25, 43,  7, 38, 49,
       63, 58, 39,  6, 13, 62, 52, 61, 22, 51, 57, 36, 17, 18, 29, 42,  8,
       11, 33, 23, 47,  0, 54, 56, 37, 59,  3, 53, 10, 16, 44, 46, 21, 64,
       31,  2, 60,  1, 34, 50, 24, 35,  5, 28, 40,  4, 65, 12, 30],
      dtype=int64), 'cur_cost': 104767.0}, {'tour': [3, 54, 15, 58, 50, 6, 7, 59, 0, 64, 27, 4, 1, 5, 41, 49, 14, 39, 33, 60, 55, 36, 22, 46, 34, 12, 29, 31, 51, 43, 35, 9, 16, 25, 65, 20, 48, 21, 2, 62, 10, 57, 52, 53, 26, 17, 24, 47, 63, 30, 45, 18, 38, 23, 37, 42, 11, 32, 13, 44, 56, 61, 40, 28, 8, 19], 'cur_cost': 102168.0}, {'tour': [35, 12, 5, 19, 36, 32, 27, 47, 28, 63, 26, 51, 58, 4, 61, 46, 1, 48, 55, 39, 42, 60, 14, 33, 23, 17, 34, 43, 0, 7, 40, 9, 8, 41, 11, 30, 6, 25, 37, 18, 21, 2, 53, 52, 38, 22, 16, 64, 59, 49, 65, 24, 10, 56, 45, 13, 3, 29, 57, 20, 62, 54, 50, 44, 15, 31], 'cur_cost': 106130.0}, {'tour': [13, 17, 8, 16, 48, 20, 61, 5, 11, 33, 62, 58, 4, 42, 43, 51, 32, 57, 2, 56, 46, 39, 22, 14, 59, 55, 15, 49, 18, 37, 28, 21, 19, 7, 3, 45, 36, 0, 64, 54, 41, 10, 24, 12, 38, 52, 35, 9, 34, 50, 25, 31, 60, 40, 23, 29, 27, 63, 47, 53, 1, 6, 44, 30, 26, 65], 'cur_cost': 98312.0}, {'tour': [9, 55, 27, 14, 40, 35, 50, 21, 25, 6, 47, 59, 63, 45, 28, 10, 18, 36, 48, 49, 51, 43, 60, 41, 22, 65, 37, 20, 38, 17, 54, 19, 39, 58, 33, 11, 46, 26, 57, 15, 52, 53, 1, 7, 8, 62, 42, 32, 16, 64, 23, 0, 3, 56, 44, 30, 2, 4, 12, 29, 13, 34, 5, 61, 24, 31], 'cur_cost': 107322.0}]
2025-08-01 09:28:05,031 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-08-01 09:28:05,031 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-08-01 09:28:05,031 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([15, 41, 14, 19, 55, 27, 48, 20, 32,  9, 45, 26, 25, 43,  7, 38, 49,
       63, 58, 39,  6, 13, 62, 52, 61, 22, 51, 57, 36, 17, 18, 29, 42,  8,
       11, 33, 23, 47,  0, 54, 56, 37, 59,  3, 53, 10, 16, 44, 46, 21, 64,
       31,  2, 60,  1, 34, 50, 24, 35,  5, 28, 40,  4, 65, 12, 30],
      dtype=int64), 'cur_cost': 104767.0}
2025-08-01 09:28:05,031 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-01 09:28:05,032 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-01 09:28:05,032 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:28:05,038 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-01 09:28:05,038 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:28:05,039 - ExplorationExpert - INFO - 探索路径生成完成，成本: 56768.0, 路径长度: 66
2025-08-01 09:28:05,039 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [38, 21, 9, 59, 12, 7, 14, 35, 33, 4, 65, 62, 16, 19, 24, 23, 6, 54, 18, 0, 57, 11, 64, 5, 20, 22, 3, 8, 27, 31, 34, 29, 28, 36, 15, 48, 46, 39, 42, 50, 44, 45, 13, 26, 30, 25, 17, 1, 52, 60, 55, 56, 49, 41, 43, 37, 47, 51, 2, 61, 58, 53, 63, 10, 32, 40], 'cur_cost': 56768.0}
2025-08-01 09:28:05,039 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-01 09:28:05,039 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:28:05,039 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:28:05,040 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 111564.0
2025-08-01 09:28:05,116 - ExploitationExpert - INFO - res_population_num: 12
2025-08-01 09:28:05,117 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9542, 9555.0, 9555, 9592.0, 9521, 9521]
2025-08-01 09:28:05,117 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 09:28:05,123 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 09:28:05,123 - ExploitationExpert - INFO - populations: [{'tour': array([53, 57, 56, 10,  5, 33, 55, 41, 32, 26, 52, 35, 36, 59,  9, 63, 21,
        3, 23, 43, 18,  4, 27, 42, 64, 48, 12, 17, 29, 22, 46, 44, 13, 37,
       49, 16,  8, 54,  1, 50, 19,  0, 60, 65, 40, 51, 58, 61, 31,  7, 14,
       25, 45,  6, 39, 11, 47, 34, 62, 38, 24, 15,  2, 30, 20, 28],
      dtype=int64), 'cur_cost': 106646.0}, {'tour': array([29, 40, 46, 32, 17, 44, 30, 64, 13, 23, 15, 52,  3,  7, 62,  1, 58,
       59, 10, 21,  4,  9, 60, 47, 24, 31, 16,  2, 18, 50, 57, 36, 56, 48,
       53, 55, 14, 33, 39, 26, 38, 19, 20, 34, 41, 65, 43, 37, 63, 28, 61,
        5, 35, 54, 22, 11,  0,  8, 42, 49,  6, 25, 45, 51, 27, 12],
      dtype=int64), 'cur_cost': 103583.0}, {'tour': array([61, 43, 62, 55, 25, 37, 11, 28, 14, 47, 35, 16, 59, 17, 13, 36, 23,
       45, 39, 18, 56,  4,  9,  1,  8, 53, 27, 49, 34, 65, 54,  2,  6, 24,
       52, 40, 32, 31, 20, 19, 44, 12, 63, 26, 60, 15,  0,  3, 46, 22, 33,
       64,  7, 51, 29, 58, 10, 50, 57, 48,  5, 30, 21, 38, 41, 42],
      dtype=int64), 'cur_cost': 104070.0}, {'tour': [0, 9, 18, 2, 8, 5, 4, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12837.0}, {'tour': [0, 12, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12771.0}, {'tour': array([15, 41, 14, 19, 55, 27, 48, 20, 32,  9, 45, 26, 25, 43,  7, 38, 49,
       63, 58, 39,  6, 13, 62, 52, 61, 22, 51, 57, 36, 17, 18, 29, 42,  8,
       11, 33, 23, 47,  0, 54, 56, 37, 59,  3, 53, 10, 16, 44, 46, 21, 64,
       31,  2, 60,  1, 34, 50, 24, 35,  5, 28, 40,  4, 65, 12, 30],
      dtype=int64), 'cur_cost': 104767.0}, {'tour': [38, 21, 9, 59, 12, 7, 14, 35, 33, 4, 65, 62, 16, 19, 24, 23, 6, 54, 18, 0, 57, 11, 64, 5, 20, 22, 3, 8, 27, 31, 34, 29, 28, 36, 15, 48, 46, 39, 42, 50, 44, 45, 13, 26, 30, 25, 17, 1, 52, 60, 55, 56, 49, 41, 43, 37, 47, 51, 2, 61, 58, 53, 63, 10, 32, 40], 'cur_cost': 56768.0}, {'tour': array([11, 56, 43, 42, 32, 15, 59, 19, 37,  5, 47, 36, 57, 50, 12, 46,  3,
       13, 27,  2, 55,  7, 34, 44, 41, 28, 38, 22,  0, 39, 17, 23, 31, 21,
       49, 53, 29, 61,  9, 14,  6, 62, 52, 58, 51, 30, 65, 54, 16,  4, 63,
       20, 45,  8, 33, 35, 60, 48, 10,  1, 18, 24, 40, 25, 64, 26],
      dtype=int64), 'cur_cost': 111564.0}, {'tour': [13, 17, 8, 16, 48, 20, 61, 5, 11, 33, 62, 58, 4, 42, 43, 51, 32, 57, 2, 56, 46, 39, 22, 14, 59, 55, 15, 49, 18, 37, 28, 21, 19, 7, 3, 45, 36, 0, 64, 54, 41, 10, 24, 12, 38, 52, 35, 9, 34, 50, 25, 31, 60, 40, 23, 29, 27, 63, 47, 53, 1, 6, 44, 30, 26, 65], 'cur_cost': 98312.0}, {'tour': [9, 55, 27, 14, 40, 35, 50, 21, 25, 6, 47, 59, 63, 45, 28, 10, 18, 36, 48, 49, 51, 43, 60, 41, 22, 65, 37, 20, 38, 17, 54, 19, 39, 58, 33, 11, 46, 26, 57, 15, 52, 53, 1, 7, 8, 62, 42, 32, 16, 64, 23, 0, 3, 56, 44, 30, 2, 4, 12, 29, 13, 34, 5, 61, 24, 31], 'cur_cost': 107322.0}]
2025-08-01 09:28:05,125 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-01 09:28:05,125 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-08-01 09:28:05,126 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([11, 56, 43, 42, 32, 15, 59, 19, 37,  5, 47, 36, 57, 50, 12, 46,  3,
       13, 27,  2, 55,  7, 34, 44, 41, 28, 38, 22,  0, 39, 17, 23, 31, 21,
       49, 53, 29, 61,  9, 14,  6, 62, 52, 58, 51, 30, 65, 54, 16,  4, 63,
       20, 45,  8, 33, 35, 60, 48, 10,  1, 18, 24, 40, 25, 64, 26],
      dtype=int64), 'cur_cost': 111564.0}
2025-08-01 09:28:05,126 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-01 09:28:05,126 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-01 09:28:05,126 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:28:05,128 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-01 09:28:05,128 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:28:05,128 - ExplorationExpert - INFO - 探索路径生成完成，成本: 109604.0, 路径长度: 66
2025-08-01 09:28:05,129 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [19, 25, 49, 4, 59, 11, 3, 20, 41, 42, 1, 28, 35, 57, 5, 64, 27, 12, 15, 2, 46, 55, 30, 16, 54, 52, 32, 40, 14, 50, 8, 23, 62, 48, 44, 31, 6, 9, 33, 0, 39, 34, 53, 47, 60, 26, 43, 22, 56, 29, 18, 37, 63, 13, 7, 65, 10, 58, 61, 38, 21, 45, 36, 24, 17, 51], 'cur_cost': 109604.0}
2025-08-01 09:28:05,129 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-01 09:28:05,129 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:28:05,129 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:28:05,130 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 100714.0
2025-08-01 09:28:05,199 - ExploitationExpert - INFO - res_population_num: 12
2025-08-01 09:28:05,199 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9542, 9555.0, 9555, 9592.0, 9521, 9521]
2025-08-01 09:28:05,199 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 09:28:05,204 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 09:28:05,204 - ExploitationExpert - INFO - populations: [{'tour': array([53, 57, 56, 10,  5, 33, 55, 41, 32, 26, 52, 35, 36, 59,  9, 63, 21,
        3, 23, 43, 18,  4, 27, 42, 64, 48, 12, 17, 29, 22, 46, 44, 13, 37,
       49, 16,  8, 54,  1, 50, 19,  0, 60, 65, 40, 51, 58, 61, 31,  7, 14,
       25, 45,  6, 39, 11, 47, 34, 62, 38, 24, 15,  2, 30, 20, 28],
      dtype=int64), 'cur_cost': 106646.0}, {'tour': array([29, 40, 46, 32, 17, 44, 30, 64, 13, 23, 15, 52,  3,  7, 62,  1, 58,
       59, 10, 21,  4,  9, 60, 47, 24, 31, 16,  2, 18, 50, 57, 36, 56, 48,
       53, 55, 14, 33, 39, 26, 38, 19, 20, 34, 41, 65, 43, 37, 63, 28, 61,
        5, 35, 54, 22, 11,  0,  8, 42, 49,  6, 25, 45, 51, 27, 12],
      dtype=int64), 'cur_cost': 103583.0}, {'tour': array([61, 43, 62, 55, 25, 37, 11, 28, 14, 47, 35, 16, 59, 17, 13, 36, 23,
       45, 39, 18, 56,  4,  9,  1,  8, 53, 27, 49, 34, 65, 54,  2,  6, 24,
       52, 40, 32, 31, 20, 19, 44, 12, 63, 26, 60, 15,  0,  3, 46, 22, 33,
       64,  7, 51, 29, 58, 10, 50, 57, 48,  5, 30, 21, 38, 41, 42],
      dtype=int64), 'cur_cost': 104070.0}, {'tour': [0, 9, 18, 2, 8, 5, 4, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12837.0}, {'tour': [0, 12, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12771.0}, {'tour': array([15, 41, 14, 19, 55, 27, 48, 20, 32,  9, 45, 26, 25, 43,  7, 38, 49,
       63, 58, 39,  6, 13, 62, 52, 61, 22, 51, 57, 36, 17, 18, 29, 42,  8,
       11, 33, 23, 47,  0, 54, 56, 37, 59,  3, 53, 10, 16, 44, 46, 21, 64,
       31,  2, 60,  1, 34, 50, 24, 35,  5, 28, 40,  4, 65, 12, 30],
      dtype=int64), 'cur_cost': 104767.0}, {'tour': [38, 21, 9, 59, 12, 7, 14, 35, 33, 4, 65, 62, 16, 19, 24, 23, 6, 54, 18, 0, 57, 11, 64, 5, 20, 22, 3, 8, 27, 31, 34, 29, 28, 36, 15, 48, 46, 39, 42, 50, 44, 45, 13, 26, 30, 25, 17, 1, 52, 60, 55, 56, 49, 41, 43, 37, 47, 51, 2, 61, 58, 53, 63, 10, 32, 40], 'cur_cost': 56768.0}, {'tour': array([11, 56, 43, 42, 32, 15, 59, 19, 37,  5, 47, 36, 57, 50, 12, 46,  3,
       13, 27,  2, 55,  7, 34, 44, 41, 28, 38, 22,  0, 39, 17, 23, 31, 21,
       49, 53, 29, 61,  9, 14,  6, 62, 52, 58, 51, 30, 65, 54, 16,  4, 63,
       20, 45,  8, 33, 35, 60, 48, 10,  1, 18, 24, 40, 25, 64, 26],
      dtype=int64), 'cur_cost': 111564.0}, {'tour': [19, 25, 49, 4, 59, 11, 3, 20, 41, 42, 1, 28, 35, 57, 5, 64, 27, 12, 15, 2, 46, 55, 30, 16, 54, 52, 32, 40, 14, 50, 8, 23, 62, 48, 44, 31, 6, 9, 33, 0, 39, 34, 53, 47, 60, 26, 43, 22, 56, 29, 18, 37, 63, 13, 7, 65, 10, 58, 61, 38, 21, 45, 36, 24, 17, 51], 'cur_cost': 109604.0}, {'tour': array([47, 48, 52, 46, 22, 33, 19, 53,  1, 30,  5, 55, 14, 36, 57, 15, 40,
       61, 37, 45, 13, 58, 59,  2, 64, 23, 24, 28, 54,  8, 41, 38, 34,  6,
        0, 32,  3, 42,  9,  4, 12, 17, 16, 25, 60,  7, 50, 43, 20, 44, 27,
       26, 11, 56, 18, 51, 21, 10, 39, 49, 63, 65, 31, 35, 29, 62],
      dtype=int64), 'cur_cost': 100714.0}]
2025-08-01 09:28:05,207 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-08-01 09:28:05,207 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 14, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 14, 'cache_hits': 0, 'similarity_calculations': 91, 'cache_hit_rate': 0.0, 'cache_size': 91}}
2025-08-01 09:28:05,207 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([47, 48, 52, 46, 22, 33, 19, 53,  1, 30,  5, 55, 14, 36, 57, 15, 40,
       61, 37, 45, 13, 58, 59,  2, 64, 23, 24, 28, 54,  8, 41, 38, 34,  6,
        0, 32,  3, 42,  9,  4, 12, 17, 16, 25, 60,  7, 50, 43, 20, 44, 27,
       26, 11, 56, 18, 51, 21, 10, 39, 49, 63, 65, 31, 35, 29, 62],
      dtype=int64), 'cur_cost': 100714.0}
2025-08-01 09:28:05,208 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([53, 57, 56, 10,  5, 33, 55, 41, 32, 26, 52, 35, 36, 59,  9, 63, 21,
        3, 23, 43, 18,  4, 27, 42, 64, 48, 12, 17, 29, 22, 46, 44, 13, 37,
       49, 16,  8, 54,  1, 50, 19,  0, 60, 65, 40, 51, 58, 61, 31,  7, 14,
       25, 45,  6, 39, 11, 47, 34, 62, 38, 24, 15,  2, 30, 20, 28],
      dtype=int64), 'cur_cost': 106646.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([29, 40, 46, 32, 17, 44, 30, 64, 13, 23, 15, 52,  3,  7, 62,  1, 58,
       59, 10, 21,  4,  9, 60, 47, 24, 31, 16,  2, 18, 50, 57, 36, 56, 48,
       53, 55, 14, 33, 39, 26, 38, 19, 20, 34, 41, 65, 43, 37, 63, 28, 61,
        5, 35, 54, 22, 11,  0,  8, 42, 49,  6, 25, 45, 51, 27, 12],
      dtype=int64), 'cur_cost': 103583.0}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([61, 43, 62, 55, 25, 37, 11, 28, 14, 47, 35, 16, 59, 17, 13, 36, 23,
       45, 39, 18, 56,  4,  9,  1,  8, 53, 27, 49, 34, 65, 54,  2,  6, 24,
       52, 40, 32, 31, 20, 19, 44, 12, 63, 26, 60, 15,  0,  3, 46, 22, 33,
       64,  7, 51, 29, 58, 10, 50, 57, 48,  5, 30, 21, 38, 41, 42],
      dtype=int64), 'cur_cost': 104070.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 18, 2, 8, 5, 4, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12837.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12771.0}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([15, 41, 14, 19, 55, 27, 48, 20, 32,  9, 45, 26, 25, 43,  7, 38, 49,
       63, 58, 39,  6, 13, 62, 52, 61, 22, 51, 57, 36, 17, 18, 29, 42,  8,
       11, 33, 23, 47,  0, 54, 56, 37, 59,  3, 53, 10, 16, 44, 46, 21, 64,
       31,  2, 60,  1, 34, 50, 24, 35,  5, 28, 40,  4, 65, 12, 30],
      dtype=int64), 'cur_cost': 104767.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [38, 21, 9, 59, 12, 7, 14, 35, 33, 4, 65, 62, 16, 19, 24, 23, 6, 54, 18, 0, 57, 11, 64, 5, 20, 22, 3, 8, 27, 31, 34, 29, 28, 36, 15, 48, 46, 39, 42, 50, 44, 45, 13, 26, 30, 25, 17, 1, 52, 60, 55, 56, 49, 41, 43, 37, 47, 51, 2, 61, 58, 53, 63, 10, 32, 40], 'cur_cost': 56768.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([11, 56, 43, 42, 32, 15, 59, 19, 37,  5, 47, 36, 57, 50, 12, 46,  3,
       13, 27,  2, 55,  7, 34, 44, 41, 28, 38, 22,  0, 39, 17, 23, 31, 21,
       49, 53, 29, 61,  9, 14,  6, 62, 52, 58, 51, 30, 65, 54, 16,  4, 63,
       20, 45,  8, 33, 35, 60, 48, 10,  1, 18, 24, 40, 25, 64, 26],
      dtype=int64), 'cur_cost': 111564.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [19, 25, 49, 4, 59, 11, 3, 20, 41, 42, 1, 28, 35, 57, 5, 64, 27, 12, 15, 2, 46, 55, 30, 16, 54, 52, 32, 40, 14, 50, 8, 23, 62, 48, 44, 31, 6, 9, 33, 0, 39, 34, 53, 47, 60, 26, 43, 22, 56, 29, 18, 37, 63, 13, 7, 65, 10, 58, 61, 38, 21, 45, 36, 24, 17, 51], 'cur_cost': 109604.0}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([47, 48, 52, 46, 22, 33, 19, 53,  1, 30,  5, 55, 14, 36, 57, 15, 40,
       61, 37, 45, 13, 58, 59,  2, 64, 23, 24, 28, 54,  8, 41, 38, 34,  6,
        0, 32,  3, 42,  9,  4, 12, 17, 16, 25, 60,  7, 50, 43, 20, 44, 27,
       26, 11, 56, 18, 51, 21, 10, 39, 49, 63, 65, 31, 35, 29, 62],
      dtype=int64), 'cur_cost': 100714.0}}]
2025-08-01 09:28:05,208 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-01 09:28:05,208 - StatsExpert - INFO - 开始统计分析
2025-08-01 09:28:05,226 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12771.0, 多样性=0.948
2025-08-01 09:28:05,226 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-01 09:28:05,226 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-01 09:28:05,227 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-01 09:28:05,228 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 80, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'slight_improvement', 'improvement_rate': 0.006132492884099498, 'best_improvement': -0.002905607036280823}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.002135231316725875}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 12, 'new_count': 12, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.6854912764003673, 'new_diversity': 0.6854912764003673, 'diversity_change': 0.0}, 'suggestions': ['改进缓慢，建议调整策略平衡或增加扰动强度', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-01 09:28:05,230 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-01 09:28:05,230 - __main__ - INFO - composite13_66 开始进化第 4 代
2025-08-01 09:28:05,230 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-01 09:28:05,230 - StatsExpert - INFO - 开始统计分析
2025-08-01 09:28:05,247 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12771.0, 多样性=0.948
2025-08-01 09:28:05,247 - PathExpert - INFO - 开始路径结构分析
2025-08-01 09:28:05,250 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.031
2025-08-01 09:28:05,250 - EliteExpert - INFO - 开始精英解分析
2025-08-01 09:28:05,253 - EliteExpert - INFO - 精英解分析完成: 精英解数量=12, 多样性=0.685
2025-08-01 09:28:05,255 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-01 09:28:05,255 - LandscapeExpert - INFO - 从统计报告生成模拟数据: 10个路径, 10个适应度值
2025-08-01 09:28:05,255 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-01 09:28:05,259 - core.algorithms.fitness_landscape_analyzer - ERROR - 景观分析失败: '>' not supported between instances of 'str' and 'int'
2025-08-01 09:28:05,259 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-01 09:28:05,352 - visualization.landscape_visualizer - INFO - 图表已保存: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_iter_4.html
2025-08-01 09:28:05,394 - visualization.landscape_visualizer - INFO - 图表已保存: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_iter_4.html
2025-08-01 09:28:05,394 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 4
2025-08-01 09:28:05,395 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-01 09:28:05,395 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1399秒
2025-08-01 09:28:05,395 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754011685.259714, 'performance_metrics': {}}}
2025-08-01 09:28:05,395 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-01 09:28:05,395 - StrategyExpert - INFO - 开始策略分配分析
2025-08-01 09:28:05,395 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 3
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 12771.0
  • mean_cost: 82332.4
- Search-space: ruggedness 0.5, modality uni-modal, deceptiveness low
- Evolution phase: exploitation
- Landscape focus suggestion: exploitation
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 1): [{"type": "unexplored_space", "potential": "high", "description": "未充分探索的搜索空间", "recommendation": "增加探索性操作"}]
- Last-iteration feedback: {'overall_score': 80, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'slight_improvement', 'impro...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-08-01 09:28:05,396 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-08-01 09:28:05,396 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-01 09:28:07,028 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.40,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "exploit",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "explore",
    "9": "explore"
  },
  "rationale": "Focus is exploitation with a slightly low mean cost. High exploration in the opportunity regions due to the unexplored space."
}
```
2025-08-01 09:28:07,029 - utils.utils - INFO - 成功从JSON中提取数据
2025-08-01 09:28:07,029 - StrategyExpert - INFO - 策略分配完成: ['exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-01 09:28:07,030 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-01 09:28:07,030 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.40,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "exploit",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "explore",
    "9": "explore"
  },
  "rationale": "Focus is exploitation with a slightly low mean cost. High exploration in the opportunity regions due to the unexplored space."
}
```
2025-08-01 09:28:07,030 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-01 09:28:07,030 - __main__ - INFO - 策略分配: ['exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-01 09:28:07,031 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.40,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "exploit",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "explore",
    "9": "explore"
  },
  "rationale": "Focus is exploitation with a slightly low mean cost. High exploration in the opportunity regions due to the unexplored space."
}
```
2025-08-01 09:28:07,032 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-01 09:28:07,032 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-01 09:28:07,032 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:28:07,032 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:28:07,033 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 103314.0
2025-08-01 09:28:07,099 - ExploitationExpert - INFO - res_population_num: 12
2025-08-01 09:28:07,099 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9542, 9555.0, 9555, 9592.0]
2025-08-01 09:28:07,099 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 09:28:07,103 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 09:28:07,103 - ExploitationExpert - INFO - populations: [{'tour': array([58,  2,  9, 31, 16, 33, 35, 61, 46,  1, 38, 63, 37, 62, 65, 54, 42,
       40, 23, 57, 39, 43,  5, 45, 44, 20, 25, 64, 30, 53, 56, 41, 36, 32,
       14,  3, 55, 12,  0,  6, 10,  7, 48, 26, 28, 52, 47, 21, 51,  4, 22,
       59, 34, 29, 49, 27, 50, 19, 11, 17, 60,  8, 13, 18, 24, 15],
      dtype=int64), 'cur_cost': 103314.0}, {'tour': [29, 40, 46, 32, 17, 44, 30, 64, 13, 23, 15, 52, 3, 7, 62, 1, 58, 59, 10, 21, 4, 9, 60, 47, 24, 31, 16, 2, 18, 50, 57, 36, 56, 48, 53, 55, 14, 33, 39, 26, 38, 19, 20, 34, 41, 65, 43, 37, 63, 28, 61, 5, 35, 54, 22, 11, 0, 8, 42, 49, 6, 25, 45, 51, 27, 12], 'cur_cost': 103583.0}, {'tour': [61, 43, 62, 55, 25, 37, 11, 28, 14, 47, 35, 16, 59, 17, 13, 36, 23, 45, 39, 18, 56, 4, 9, 1, 8, 53, 27, 49, 34, 65, 54, 2, 6, 24, 52, 40, 32, 31, 20, 19, 44, 12, 63, 26, 60, 15, 0, 3, 46, 22, 33, 64, 7, 51, 29, 58, 10, 50, 57, 48, 5, 30, 21, 38, 41, 42], 'cur_cost': 104070.0}, {'tour': [0, 9, 18, 2, 8, 5, 4, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12837.0}, {'tour': [0, 12, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12771.0}, {'tour': [15, 41, 14, 19, 55, 27, 48, 20, 32, 9, 45, 26, 25, 43, 7, 38, 49, 63, 58, 39, 6, 13, 62, 52, 61, 22, 51, 57, 36, 17, 18, 29, 42, 8, 11, 33, 23, 47, 0, 54, 56, 37, 59, 3, 53, 10, 16, 44, 46, 21, 64, 31, 2, 60, 1, 34, 50, 24, 35, 5, 28, 40, 4, 65, 12, 30], 'cur_cost': 104767.0}, {'tour': [38, 21, 9, 59, 12, 7, 14, 35, 33, 4, 65, 62, 16, 19, 24, 23, 6, 54, 18, 0, 57, 11, 64, 5, 20, 22, 3, 8, 27, 31, 34, 29, 28, 36, 15, 48, 46, 39, 42, 50, 44, 45, 13, 26, 30, 25, 17, 1, 52, 60, 55, 56, 49, 41, 43, 37, 47, 51, 2, 61, 58, 53, 63, 10, 32, 40], 'cur_cost': 56768.0}, {'tour': [11, 56, 43, 42, 32, 15, 59, 19, 37, 5, 47, 36, 57, 50, 12, 46, 3, 13, 27, 2, 55, 7, 34, 44, 41, 28, 38, 22, 0, 39, 17, 23, 31, 21, 49, 53, 29, 61, 9, 14, 6, 62, 52, 58, 51, 30, 65, 54, 16, 4, 63, 20, 45, 8, 33, 35, 60, 48, 10, 1, 18, 24, 40, 25, 64, 26], 'cur_cost': 111564.0}, {'tour': [19, 25, 49, 4, 59, 11, 3, 20, 41, 42, 1, 28, 35, 57, 5, 64, 27, 12, 15, 2, 46, 55, 30, 16, 54, 52, 32, 40, 14, 50, 8, 23, 62, 48, 44, 31, 6, 9, 33, 0, 39, 34, 53, 47, 60, 26, 43, 22, 56, 29, 18, 37, 63, 13, 7, 65, 10, 58, 61, 38, 21, 45, 36, 24, 17, 51], 'cur_cost': 109604.0}, {'tour': [47, 48, 52, 46, 22, 33, 19, 53, 1, 30, 5, 55, 14, 36, 57, 15, 40, 61, 37, 45, 13, 58, 59, 2, 64, 23, 24, 28, 54, 8, 41, 38, 34, 6, 0, 32, 3, 42, 9, 4, 12, 17, 16, 25, 60, 7, 50, 43, 20, 44, 27, 26, 11, 56, 18, 51, 21, 10, 39, 49, 63, 65, 31, 35, 29, 62], 'cur_cost': 100714.0}]
2025-08-01 09:28:07,104 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-08-01 09:28:07,104 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 15, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 15, 'cache_hits': 0, 'similarity_calculations': 105, 'cache_hit_rate': 0.0, 'cache_size': 105}}
2025-08-01 09:28:07,105 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([58,  2,  9, 31, 16, 33, 35, 61, 46,  1, 38, 63, 37, 62, 65, 54, 42,
       40, 23, 57, 39, 43,  5, 45, 44, 20, 25, 64, 30, 53, 56, 41, 36, 32,
       14,  3, 55, 12,  0,  6, 10,  7, 48, 26, 28, 52, 47, 21, 51,  4, 22,
       59, 34, 29, 49, 27, 50, 19, 11, 17, 60,  8, 13, 18, 24, 15],
      dtype=int64), 'cur_cost': 103314.0}
2025-08-01 09:28:07,105 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-01 09:28:07,105 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:28:07,105 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:28:07,105 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 100887.0
2025-08-01 09:28:07,167 - ExploitationExpert - INFO - res_population_num: 12
2025-08-01 09:28:07,167 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9542, 9555.0, 9555, 9592.0]
2025-08-01 09:28:07,167 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 09:28:07,171 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 09:28:07,171 - ExploitationExpert - INFO - populations: [{'tour': array([58,  2,  9, 31, 16, 33, 35, 61, 46,  1, 38, 63, 37, 62, 65, 54, 42,
       40, 23, 57, 39, 43,  5, 45, 44, 20, 25, 64, 30, 53, 56, 41, 36, 32,
       14,  3, 55, 12,  0,  6, 10,  7, 48, 26, 28, 52, 47, 21, 51,  4, 22,
       59, 34, 29, 49, 27, 50, 19, 11, 17, 60,  8, 13, 18, 24, 15],
      dtype=int64), 'cur_cost': 103314.0}, {'tour': array([19, 36, 22, 49, 57, 34, 38, 15, 60, 46, 26, 24, 59, 47, 40, 43, 65,
       54, 14,  6, 20, 37,  9, 12, 52, 27,  3, 55, 50, 42, 30,  4,  2, 61,
       56, 45, 39, 29, 10,  0, 31, 32, 53, 17, 16, 48,  5,  7, 18, 64, 25,
        1, 62, 58, 44,  8, 13, 28, 41, 35, 33, 23, 63, 11, 21, 51],
      dtype=int64), 'cur_cost': 100887.0}, {'tour': [61, 43, 62, 55, 25, 37, 11, 28, 14, 47, 35, 16, 59, 17, 13, 36, 23, 45, 39, 18, 56, 4, 9, 1, 8, 53, 27, 49, 34, 65, 54, 2, 6, 24, 52, 40, 32, 31, 20, 19, 44, 12, 63, 26, 60, 15, 0, 3, 46, 22, 33, 64, 7, 51, 29, 58, 10, 50, 57, 48, 5, 30, 21, 38, 41, 42], 'cur_cost': 104070.0}, {'tour': [0, 9, 18, 2, 8, 5, 4, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12837.0}, {'tour': [0, 12, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12771.0}, {'tour': [15, 41, 14, 19, 55, 27, 48, 20, 32, 9, 45, 26, 25, 43, 7, 38, 49, 63, 58, 39, 6, 13, 62, 52, 61, 22, 51, 57, 36, 17, 18, 29, 42, 8, 11, 33, 23, 47, 0, 54, 56, 37, 59, 3, 53, 10, 16, 44, 46, 21, 64, 31, 2, 60, 1, 34, 50, 24, 35, 5, 28, 40, 4, 65, 12, 30], 'cur_cost': 104767.0}, {'tour': [38, 21, 9, 59, 12, 7, 14, 35, 33, 4, 65, 62, 16, 19, 24, 23, 6, 54, 18, 0, 57, 11, 64, 5, 20, 22, 3, 8, 27, 31, 34, 29, 28, 36, 15, 48, 46, 39, 42, 50, 44, 45, 13, 26, 30, 25, 17, 1, 52, 60, 55, 56, 49, 41, 43, 37, 47, 51, 2, 61, 58, 53, 63, 10, 32, 40], 'cur_cost': 56768.0}, {'tour': [11, 56, 43, 42, 32, 15, 59, 19, 37, 5, 47, 36, 57, 50, 12, 46, 3, 13, 27, 2, 55, 7, 34, 44, 41, 28, 38, 22, 0, 39, 17, 23, 31, 21, 49, 53, 29, 61, 9, 14, 6, 62, 52, 58, 51, 30, 65, 54, 16, 4, 63, 20, 45, 8, 33, 35, 60, 48, 10, 1, 18, 24, 40, 25, 64, 26], 'cur_cost': 111564.0}, {'tour': [19, 25, 49, 4, 59, 11, 3, 20, 41, 42, 1, 28, 35, 57, 5, 64, 27, 12, 15, 2, 46, 55, 30, 16, 54, 52, 32, 40, 14, 50, 8, 23, 62, 48, 44, 31, 6, 9, 33, 0, 39, 34, 53, 47, 60, 26, 43, 22, 56, 29, 18, 37, 63, 13, 7, 65, 10, 58, 61, 38, 21, 45, 36, 24, 17, 51], 'cur_cost': 109604.0}, {'tour': [47, 48, 52, 46, 22, 33, 19, 53, 1, 30, 5, 55, 14, 36, 57, 15, 40, 61, 37, 45, 13, 58, 59, 2, 64, 23, 24, 28, 54, 8, 41, 38, 34, 6, 0, 32, 3, 42, 9, 4, 12, 17, 16, 25, 60, 7, 50, 43, 20, 44, 27, 26, 11, 56, 18, 51, 21, 10, 39, 49, 63, 65, 31, 35, 29, 62], 'cur_cost': 100714.0}]
2025-08-01 09:28:07,172 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-08-01 09:28:07,172 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 16, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 16, 'cache_hits': 0, 'similarity_calculations': 120, 'cache_hit_rate': 0.0, 'cache_size': 120}}
2025-08-01 09:28:07,173 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([19, 36, 22, 49, 57, 34, 38, 15, 60, 46, 26, 24, 59, 47, 40, 43, 65,
       54, 14,  6, 20, 37,  9, 12, 52, 27,  3, 55, 50, 42, 30,  4,  2, 61,
       56, 45, 39, 29, 10,  0, 31, 32, 53, 17, 16, 48,  5,  7, 18, 64, 25,
        1, 62, 58, 44,  8, 13, 28, 41, 35, 33, 23, 63, 11, 21, 51],
      dtype=int64), 'cur_cost': 100887.0}
2025-08-01 09:28:07,173 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-01 09:28:07,173 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:28:07,173 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:28:07,173 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 111633.0
2025-08-01 09:28:07,241 - ExploitationExpert - INFO - res_population_num: 12
2025-08-01 09:28:07,241 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9542, 9555.0, 9555, 9592.0]
2025-08-01 09:28:07,241 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 09:28:07,246 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 09:28:07,246 - ExploitationExpert - INFO - populations: [{'tour': array([58,  2,  9, 31, 16, 33, 35, 61, 46,  1, 38, 63, 37, 62, 65, 54, 42,
       40, 23, 57, 39, 43,  5, 45, 44, 20, 25, 64, 30, 53, 56, 41, 36, 32,
       14,  3, 55, 12,  0,  6, 10,  7, 48, 26, 28, 52, 47, 21, 51,  4, 22,
       59, 34, 29, 49, 27, 50, 19, 11, 17, 60,  8, 13, 18, 24, 15],
      dtype=int64), 'cur_cost': 103314.0}, {'tour': array([19, 36, 22, 49, 57, 34, 38, 15, 60, 46, 26, 24, 59, 47, 40, 43, 65,
       54, 14,  6, 20, 37,  9, 12, 52, 27,  3, 55, 50, 42, 30,  4,  2, 61,
       56, 45, 39, 29, 10,  0, 31, 32, 53, 17, 16, 48,  5,  7, 18, 64, 25,
        1, 62, 58, 44,  8, 13, 28, 41, 35, 33, 23, 63, 11, 21, 51],
      dtype=int64), 'cur_cost': 100887.0}, {'tour': array([16,  1, 45, 46, 48, 32, 56, 47,  4, 53, 63, 44, 24, 51, 30, 10, 54,
       37, 27,  2, 23,  0, 62, 41, 15, 50, 36, 65, 61, 13, 58, 59, 35, 33,
       14, 60, 29,  3, 21, 64, 43, 57, 34, 20, 52, 18, 40, 22, 28, 49,  6,
       17, 55,  9, 31, 19,  7,  8, 26,  5, 42, 38, 11, 39, 12, 25],
      dtype=int64), 'cur_cost': 111633.0}, {'tour': [0, 9, 18, 2, 8, 5, 4, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12837.0}, {'tour': [0, 12, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12771.0}, {'tour': [15, 41, 14, 19, 55, 27, 48, 20, 32, 9, 45, 26, 25, 43, 7, 38, 49, 63, 58, 39, 6, 13, 62, 52, 61, 22, 51, 57, 36, 17, 18, 29, 42, 8, 11, 33, 23, 47, 0, 54, 56, 37, 59, 3, 53, 10, 16, 44, 46, 21, 64, 31, 2, 60, 1, 34, 50, 24, 35, 5, 28, 40, 4, 65, 12, 30], 'cur_cost': 104767.0}, {'tour': [38, 21, 9, 59, 12, 7, 14, 35, 33, 4, 65, 62, 16, 19, 24, 23, 6, 54, 18, 0, 57, 11, 64, 5, 20, 22, 3, 8, 27, 31, 34, 29, 28, 36, 15, 48, 46, 39, 42, 50, 44, 45, 13, 26, 30, 25, 17, 1, 52, 60, 55, 56, 49, 41, 43, 37, 47, 51, 2, 61, 58, 53, 63, 10, 32, 40], 'cur_cost': 56768.0}, {'tour': [11, 56, 43, 42, 32, 15, 59, 19, 37, 5, 47, 36, 57, 50, 12, 46, 3, 13, 27, 2, 55, 7, 34, 44, 41, 28, 38, 22, 0, 39, 17, 23, 31, 21, 49, 53, 29, 61, 9, 14, 6, 62, 52, 58, 51, 30, 65, 54, 16, 4, 63, 20, 45, 8, 33, 35, 60, 48, 10, 1, 18, 24, 40, 25, 64, 26], 'cur_cost': 111564.0}, {'tour': [19, 25, 49, 4, 59, 11, 3, 20, 41, 42, 1, 28, 35, 57, 5, 64, 27, 12, 15, 2, 46, 55, 30, 16, 54, 52, 32, 40, 14, 50, 8, 23, 62, 48, 44, 31, 6, 9, 33, 0, 39, 34, 53, 47, 60, 26, 43, 22, 56, 29, 18, 37, 63, 13, 7, 65, 10, 58, 61, 38, 21, 45, 36, 24, 17, 51], 'cur_cost': 109604.0}, {'tour': [47, 48, 52, 46, 22, 33, 19, 53, 1, 30, 5, 55, 14, 36, 57, 15, 40, 61, 37, 45, 13, 58, 59, 2, 64, 23, 24, 28, 54, 8, 41, 38, 34, 6, 0, 32, 3, 42, 9, 4, 12, 17, 16, 25, 60, 7, 50, 43, 20, 44, 27, 26, 11, 56, 18, 51, 21, 10, 39, 49, 63, 65, 31, 35, 29, 62], 'cur_cost': 100714.0}]
2025-08-01 09:28:07,247 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-08-01 09:28:07,247 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 17, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 17, 'cache_hits': 0, 'similarity_calculations': 136, 'cache_hit_rate': 0.0, 'cache_size': 136}}
2025-08-01 09:28:07,248 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([16,  1, 45, 46, 48, 32, 56, 47,  4, 53, 63, 44, 24, 51, 30, 10, 54,
       37, 27,  2, 23,  0, 62, 41, 15, 50, 36, 65, 61, 13, 58, 59, 35, 33,
       14, 60, 29,  3, 21, 64, 43, 57, 34, 20, 52, 18, 40, 22, 28, 49,  6,
       17, 55,  9, 31, 19,  7,  8, 26,  5, 42, 38, 11, 39, 12, 25],
      dtype=int64), 'cur_cost': 111633.0}
2025-08-01 09:28:07,248 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-01 09:28:07,248 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:28:07,248 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:28:07,248 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 111334.0
2025-08-01 09:28:07,307 - ExploitationExpert - INFO - res_population_num: 12
2025-08-01 09:28:07,307 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9542, 9555.0, 9555, 9592.0]
2025-08-01 09:28:07,308 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 09:28:07,312 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 09:28:07,312 - ExploitationExpert - INFO - populations: [{'tour': array([58,  2,  9, 31, 16, 33, 35, 61, 46,  1, 38, 63, 37, 62, 65, 54, 42,
       40, 23, 57, 39, 43,  5, 45, 44, 20, 25, 64, 30, 53, 56, 41, 36, 32,
       14,  3, 55, 12,  0,  6, 10,  7, 48, 26, 28, 52, 47, 21, 51,  4, 22,
       59, 34, 29, 49, 27, 50, 19, 11, 17, 60,  8, 13, 18, 24, 15],
      dtype=int64), 'cur_cost': 103314.0}, {'tour': array([19, 36, 22, 49, 57, 34, 38, 15, 60, 46, 26, 24, 59, 47, 40, 43, 65,
       54, 14,  6, 20, 37,  9, 12, 52, 27,  3, 55, 50, 42, 30,  4,  2, 61,
       56, 45, 39, 29, 10,  0, 31, 32, 53, 17, 16, 48,  5,  7, 18, 64, 25,
        1, 62, 58, 44,  8, 13, 28, 41, 35, 33, 23, 63, 11, 21, 51],
      dtype=int64), 'cur_cost': 100887.0}, {'tour': array([16,  1, 45, 46, 48, 32, 56, 47,  4, 53, 63, 44, 24, 51, 30, 10, 54,
       37, 27,  2, 23,  0, 62, 41, 15, 50, 36, 65, 61, 13, 58, 59, 35, 33,
       14, 60, 29,  3, 21, 64, 43, 57, 34, 20, 52, 18, 40, 22, 28, 49,  6,
       17, 55,  9, 31, 19,  7,  8, 26,  5, 42, 38, 11, 39, 12, 25],
      dtype=int64), 'cur_cost': 111633.0}, {'tour': array([ 9, 44, 37, 58,  2, 11, 20, 49, 65, 38, 59, 35, 19,  8, 61, 27, 30,
       43, 55, 42, 14, 62, 16, 26, 29, 54, 24, 57,  4, 52, 36, 18, 60, 53,
       21, 48, 10, 13, 12, 25, 32,  6, 51,  0, 23, 17,  3, 39, 34, 41,  1,
       56, 28, 40, 45, 50,  7, 22, 47, 15, 64, 31, 33, 46, 63,  5],
      dtype=int64), 'cur_cost': 111334.0}, {'tour': [0, 12, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12771.0}, {'tour': [15, 41, 14, 19, 55, 27, 48, 20, 32, 9, 45, 26, 25, 43, 7, 38, 49, 63, 58, 39, 6, 13, 62, 52, 61, 22, 51, 57, 36, 17, 18, 29, 42, 8, 11, 33, 23, 47, 0, 54, 56, 37, 59, 3, 53, 10, 16, 44, 46, 21, 64, 31, 2, 60, 1, 34, 50, 24, 35, 5, 28, 40, 4, 65, 12, 30], 'cur_cost': 104767.0}, {'tour': [38, 21, 9, 59, 12, 7, 14, 35, 33, 4, 65, 62, 16, 19, 24, 23, 6, 54, 18, 0, 57, 11, 64, 5, 20, 22, 3, 8, 27, 31, 34, 29, 28, 36, 15, 48, 46, 39, 42, 50, 44, 45, 13, 26, 30, 25, 17, 1, 52, 60, 55, 56, 49, 41, 43, 37, 47, 51, 2, 61, 58, 53, 63, 10, 32, 40], 'cur_cost': 56768.0}, {'tour': [11, 56, 43, 42, 32, 15, 59, 19, 37, 5, 47, 36, 57, 50, 12, 46, 3, 13, 27, 2, 55, 7, 34, 44, 41, 28, 38, 22, 0, 39, 17, 23, 31, 21, 49, 53, 29, 61, 9, 14, 6, 62, 52, 58, 51, 30, 65, 54, 16, 4, 63, 20, 45, 8, 33, 35, 60, 48, 10, 1, 18, 24, 40, 25, 64, 26], 'cur_cost': 111564.0}, {'tour': [19, 25, 49, 4, 59, 11, 3, 20, 41, 42, 1, 28, 35, 57, 5, 64, 27, 12, 15, 2, 46, 55, 30, 16, 54, 52, 32, 40, 14, 50, 8, 23, 62, 48, 44, 31, 6, 9, 33, 0, 39, 34, 53, 47, 60, 26, 43, 22, 56, 29, 18, 37, 63, 13, 7, 65, 10, 58, 61, 38, 21, 45, 36, 24, 17, 51], 'cur_cost': 109604.0}, {'tour': [47, 48, 52, 46, 22, 33, 19, 53, 1, 30, 5, 55, 14, 36, 57, 15, 40, 61, 37, 45, 13, 58, 59, 2, 64, 23, 24, 28, 54, 8, 41, 38, 34, 6, 0, 32, 3, 42, 9, 4, 12, 17, 16, 25, 60, 7, 50, 43, 20, 44, 27, 26, 11, 56, 18, 51, 21, 10, 39, 49, 63, 65, 31, 35, 29, 62], 'cur_cost': 100714.0}]
2025-08-01 09:28:07,314 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-08-01 09:28:07,314 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 18, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 18, 'cache_hits': 0, 'similarity_calculations': 153, 'cache_hit_rate': 0.0, 'cache_size': 153}}
2025-08-01 09:28:07,314 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 9, 44, 37, 58,  2, 11, 20, 49, 65, 38, 59, 35, 19,  8, 61, 27, 30,
       43, 55, 42, 14, 62, 16, 26, 29, 54, 24, 57,  4, 52, 36, 18, 60, 53,
       21, 48, 10, 13, 12, 25, 32,  6, 51,  0, 23, 17,  3, 39, 34, 41,  1,
       56, 28, 40, 45, 50,  7, 22, 47, 15, 64, 31, 33, 46, 63,  5],
      dtype=int64), 'cur_cost': 111334.0}
2025-08-01 09:28:07,314 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-01 09:28:07,314 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:28:07,314 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:28:07,315 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 122657.0
2025-08-01 09:28:07,379 - ExploitationExpert - INFO - res_population_num: 12
2025-08-01 09:28:07,379 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9542, 9555.0, 9555, 9592.0]
2025-08-01 09:28:07,379 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 09:28:07,383 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 09:28:07,384 - ExploitationExpert - INFO - populations: [{'tour': array([58,  2,  9, 31, 16, 33, 35, 61, 46,  1, 38, 63, 37, 62, 65, 54, 42,
       40, 23, 57, 39, 43,  5, 45, 44, 20, 25, 64, 30, 53, 56, 41, 36, 32,
       14,  3, 55, 12,  0,  6, 10,  7, 48, 26, 28, 52, 47, 21, 51,  4, 22,
       59, 34, 29, 49, 27, 50, 19, 11, 17, 60,  8, 13, 18, 24, 15],
      dtype=int64), 'cur_cost': 103314.0}, {'tour': array([19, 36, 22, 49, 57, 34, 38, 15, 60, 46, 26, 24, 59, 47, 40, 43, 65,
       54, 14,  6, 20, 37,  9, 12, 52, 27,  3, 55, 50, 42, 30,  4,  2, 61,
       56, 45, 39, 29, 10,  0, 31, 32, 53, 17, 16, 48,  5,  7, 18, 64, 25,
        1, 62, 58, 44,  8, 13, 28, 41, 35, 33, 23, 63, 11, 21, 51],
      dtype=int64), 'cur_cost': 100887.0}, {'tour': array([16,  1, 45, 46, 48, 32, 56, 47,  4, 53, 63, 44, 24, 51, 30, 10, 54,
       37, 27,  2, 23,  0, 62, 41, 15, 50, 36, 65, 61, 13, 58, 59, 35, 33,
       14, 60, 29,  3, 21, 64, 43, 57, 34, 20, 52, 18, 40, 22, 28, 49,  6,
       17, 55,  9, 31, 19,  7,  8, 26,  5, 42, 38, 11, 39, 12, 25],
      dtype=int64), 'cur_cost': 111633.0}, {'tour': array([ 9, 44, 37, 58,  2, 11, 20, 49, 65, 38, 59, 35, 19,  8, 61, 27, 30,
       43, 55, 42, 14, 62, 16, 26, 29, 54, 24, 57,  4, 52, 36, 18, 60, 53,
       21, 48, 10, 13, 12, 25, 32,  6, 51,  0, 23, 17,  3, 39, 34, 41,  1,
       56, 28, 40, 45, 50,  7, 22, 47, 15, 64, 31, 33, 46, 63,  5],
      dtype=int64), 'cur_cost': 111334.0}, {'tour': array([62,  3, 41,  9, 43,  4, 65, 46, 37, 45, 49, 61, 10, 38, 29, 56, 25,
        1, 35,  7, 48,  0, 22, 53, 54, 31, 13, 60, 47, 21, 23, 42, 28, 51,
        6, 32,  2, 15, 39, 19, 63, 44, 11, 52, 18, 14, 26, 64, 36, 17, 59,
       33, 16,  5, 40, 58,  8, 12, 27, 30, 20, 34, 57, 24, 50, 55],
      dtype=int64), 'cur_cost': 122657.0}, {'tour': [15, 41, 14, 19, 55, 27, 48, 20, 32, 9, 45, 26, 25, 43, 7, 38, 49, 63, 58, 39, 6, 13, 62, 52, 61, 22, 51, 57, 36, 17, 18, 29, 42, 8, 11, 33, 23, 47, 0, 54, 56, 37, 59, 3, 53, 10, 16, 44, 46, 21, 64, 31, 2, 60, 1, 34, 50, 24, 35, 5, 28, 40, 4, 65, 12, 30], 'cur_cost': 104767.0}, {'tour': [38, 21, 9, 59, 12, 7, 14, 35, 33, 4, 65, 62, 16, 19, 24, 23, 6, 54, 18, 0, 57, 11, 64, 5, 20, 22, 3, 8, 27, 31, 34, 29, 28, 36, 15, 48, 46, 39, 42, 50, 44, 45, 13, 26, 30, 25, 17, 1, 52, 60, 55, 56, 49, 41, 43, 37, 47, 51, 2, 61, 58, 53, 63, 10, 32, 40], 'cur_cost': 56768.0}, {'tour': [11, 56, 43, 42, 32, 15, 59, 19, 37, 5, 47, 36, 57, 50, 12, 46, 3, 13, 27, 2, 55, 7, 34, 44, 41, 28, 38, 22, 0, 39, 17, 23, 31, 21, 49, 53, 29, 61, 9, 14, 6, 62, 52, 58, 51, 30, 65, 54, 16, 4, 63, 20, 45, 8, 33, 35, 60, 48, 10, 1, 18, 24, 40, 25, 64, 26], 'cur_cost': 111564.0}, {'tour': [19, 25, 49, 4, 59, 11, 3, 20, 41, 42, 1, 28, 35, 57, 5, 64, 27, 12, 15, 2, 46, 55, 30, 16, 54, 52, 32, 40, 14, 50, 8, 23, 62, 48, 44, 31, 6, 9, 33, 0, 39, 34, 53, 47, 60, 26, 43, 22, 56, 29, 18, 37, 63, 13, 7, 65, 10, 58, 61, 38, 21, 45, 36, 24, 17, 51], 'cur_cost': 109604.0}, {'tour': [47, 48, 52, 46, 22, 33, 19, 53, 1, 30, 5, 55, 14, 36, 57, 15, 40, 61, 37, 45, 13, 58, 59, 2, 64, 23, 24, 28, 54, 8, 41, 38, 34, 6, 0, 32, 3, 42, 9, 4, 12, 17, 16, 25, 60, 7, 50, 43, 20, 44, 27, 26, 11, 56, 18, 51, 21, 10, 39, 49, 63, 65, 31, 35, 29, 62], 'cur_cost': 100714.0}]
2025-08-01 09:28:07,385 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-08-01 09:28:07,386 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 19, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 19, 'cache_hits': 0, 'similarity_calculations': 171, 'cache_hit_rate': 0.0, 'cache_size': 171}}
2025-08-01 09:28:07,386 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([62,  3, 41,  9, 43,  4, 65, 46, 37, 45, 49, 61, 10, 38, 29, 56, 25,
        1, 35,  7, 48,  0, 22, 53, 54, 31, 13, 60, 47, 21, 23, 42, 28, 51,
        6, 32,  2, 15, 39, 19, 63, 44, 11, 52, 18, 14, 26, 64, 36, 17, 59,
       33, 16,  5, 40, 58,  8, 12, 27, 30, 20, 34, 57, 24, 50, 55],
      dtype=int64), 'cur_cost': 122657.0}
2025-08-01 09:28:07,386 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-01 09:28:07,386 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-01 09:28:07,386 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:28:07,389 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 09:28:07,389 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:28:07,389 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12868.0, 路径长度: 66
2025-08-01 09:28:07,389 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 18, 20, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12868.0}
2025-08-01 09:28:07,389 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-01 09:28:07,390 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-01 09:28:07,390 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:28:07,391 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-01 09:28:07,392 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:28:07,392 - ExplorationExpert - INFO - 探索路径生成完成，成本: 94343.0, 路径长度: 66
2025-08-01 09:28:07,392 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [62, 55, 14, 61, 63, 53, 2, 3, 38, 54, 33, 28, 32, 24, 12, 8, 64, 6, 39, 5, 50, 27, 41, 34, 49, 47, 4, 60, 29, 58, 48, 23, 45, 43, 10, 52, 0, 26, 25, 40, 1, 59, 9, 22, 11, 13, 20, 35, 46, 15, 36, 16, 21, 18, 51, 19, 37, 30, 42, 17, 57, 44, 65, 7, 31, 56], 'cur_cost': 94343.0}
2025-08-01 09:28:07,392 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-01 09:28:07,392 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-01 09:28:07,392 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:28:07,394 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-01 09:28:07,394 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:28:07,394 - ExplorationExpert - INFO - 探索路径生成完成，成本: 109289.0, 路径长度: 66
2025-08-01 09:28:07,394 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [33, 15, 1, 4, 61, 43, 37, 31, 16, 32, 27, 28, 0, 22, 54, 14, 49, 35, 7, 36, 44, 63, 23, 39, 11, 55, 62, 41, 26, 64, 10, 53, 45, 24, 52, 42, 17, 40, 59, 51, 38, 57, 8, 25, 12, 34, 65, 20, 58, 46, 47, 60, 13, 56, 18, 6, 21, 30, 29, 19, 3, 5, 48, 9, 2, 50], 'cur_cost': 109289.0}
2025-08-01 09:28:07,395 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-01 09:28:07,395 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-01 09:28:07,395 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:28:07,398 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 09:28:07,398 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:28:07,398 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12773.0, 路径长度: 66
2025-08-01 09:28:07,399 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 15, 2, 9, 11, 7, 3, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12773.0}
2025-08-01 09:28:07,399 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-01 09:28:07,399 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-01 09:28:07,399 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:28:07,403 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 09:28:07,404 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:28:07,404 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12751.0, 路径长度: 66
2025-08-01 09:28:07,404 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 15, 17, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12751.0}
2025-08-01 09:28:07,405 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([58,  2,  9, 31, 16, 33, 35, 61, 46,  1, 38, 63, 37, 62, 65, 54, 42,
       40, 23, 57, 39, 43,  5, 45, 44, 20, 25, 64, 30, 53, 56, 41, 36, 32,
       14,  3, 55, 12,  0,  6, 10,  7, 48, 26, 28, 52, 47, 21, 51,  4, 22,
       59, 34, 29, 49, 27, 50, 19, 11, 17, 60,  8, 13, 18, 24, 15],
      dtype=int64), 'cur_cost': 103314.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([19, 36, 22, 49, 57, 34, 38, 15, 60, 46, 26, 24, 59, 47, 40, 43, 65,
       54, 14,  6, 20, 37,  9, 12, 52, 27,  3, 55, 50, 42, 30,  4,  2, 61,
       56, 45, 39, 29, 10,  0, 31, 32, 53, 17, 16, 48,  5,  7, 18, 64, 25,
        1, 62, 58, 44,  8, 13, 28, 41, 35, 33, 23, 63, 11, 21, 51],
      dtype=int64), 'cur_cost': 100887.0}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([16,  1, 45, 46, 48, 32, 56, 47,  4, 53, 63, 44, 24, 51, 30, 10, 54,
       37, 27,  2, 23,  0, 62, 41, 15, 50, 36, 65, 61, 13, 58, 59, 35, 33,
       14, 60, 29,  3, 21, 64, 43, 57, 34, 20, 52, 18, 40, 22, 28, 49,  6,
       17, 55,  9, 31, 19,  7,  8, 26,  5, 42, 38, 11, 39, 12, 25],
      dtype=int64), 'cur_cost': 111633.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 9, 44, 37, 58,  2, 11, 20, 49, 65, 38, 59, 35, 19,  8, 61, 27, 30,
       43, 55, 42, 14, 62, 16, 26, 29, 54, 24, 57,  4, 52, 36, 18, 60, 53,
       21, 48, 10, 13, 12, 25, 32,  6, 51,  0, 23, 17,  3, 39, 34, 41,  1,
       56, 28, 40, 45, 50,  7, 22, 47, 15, 64, 31, 33, 46, 63,  5],
      dtype=int64), 'cur_cost': 111334.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([62,  3, 41,  9, 43,  4, 65, 46, 37, 45, 49, 61, 10, 38, 29, 56, 25,
        1, 35,  7, 48,  0, 22, 53, 54, 31, 13, 60, 47, 21, 23, 42, 28, 51,
        6, 32,  2, 15, 39, 19, 63, 44, 11, 52, 18, 14, 26, 64, 36, 17, 59,
       33, 16,  5, 40, 58,  8, 12, 27, 30, 20, 34, 57, 24, 50, 55],
      dtype=int64), 'cur_cost': 122657.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 20, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12868.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [62, 55, 14, 61, 63, 53, 2, 3, 38, 54, 33, 28, 32, 24, 12, 8, 64, 6, 39, 5, 50, 27, 41, 34, 49, 47, 4, 60, 29, 58, 48, 23, 45, 43, 10, 52, 0, 26, 25, 40, 1, 59, 9, 22, 11, 13, 20, 35, 46, 15, 36, 16, 21, 18, 51, 19, 37, 30, 42, 17, 57, 44, 65, 7, 31, 56], 'cur_cost': 94343.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [33, 15, 1, 4, 61, 43, 37, 31, 16, 32, 27, 28, 0, 22, 54, 14, 49, 35, 7, 36, 44, 63, 23, 39, 11, 55, 62, 41, 26, 64, 10, 53, 45, 24, 52, 42, 17, 40, 59, 51, 38, 57, 8, 25, 12, 34, 65, 20, 58, 46, 47, 60, 13, 56, 18, 6, 21, 30, 29, 19, 3, 5, 48, 9, 2, 50], 'cur_cost': 109289.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 2, 9, 11, 7, 3, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12773.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 17, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12751.0}}]
2025-08-01 09:28:07,406 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-01 09:28:07,406 - StatsExpert - INFO - 开始统计分析
2025-08-01 09:28:07,422 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12751.0, 多样性=0.914
2025-08-01 09:28:07,422 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-01 09:28:07,422 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-01 09:28:07,422 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-01 09:28:07,424 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.019878829980037194, 'best_improvement': 0.0015660480776759846}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.036221590909090794}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.00429948446743363, 'recent_improvements': [-0.0024664760507677602, -0.11050740279013356, 0.006132492884099498], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 12, 'new_count': 12, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.6854912764003673, 'new_diversity': 0.6854912764003673, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-01 09:28:07,426 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-01 09:28:07,427 - __main__ - INFO - composite13_66 开始进化第 5 代
2025-08-01 09:28:07,427 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-01 09:28:07,427 - StatsExpert - INFO - 开始统计分析
2025-08-01 09:28:07,442 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12751.0, 多样性=0.914
2025-08-01 09:28:07,442 - PathExpert - INFO - 开始路径结构分析
2025-08-01 09:28:07,445 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.046
2025-08-01 09:28:07,445 - EliteExpert - INFO - 开始精英解分析
2025-08-01 09:28:07,448 - EliteExpert - INFO - 精英解分析完成: 精英解数量=12, 多样性=0.685
2025-08-01 09:28:07,450 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-01 09:28:07,450 - LandscapeExpert - INFO - 从统计报告生成模拟数据: 10个路径, 10个适应度值
2025-08-01 09:28:07,450 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-01 09:28:07,455 - core.algorithms.fitness_landscape_analyzer - ERROR - 景观分析失败: '>' not supported between instances of 'str' and 'int'
2025-08-01 09:28:07,455 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-01 09:28:07,522 - visualization.landscape_visualizer - INFO - 图表已保存: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_iter_5.html
2025-08-01 09:28:07,557 - visualization.landscape_visualizer - INFO - 图表已保存: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_iter_5.html
2025-08-01 09:28:07,557 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 5
2025-08-01 09:28:07,557 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-01 09:28:07,557 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1071秒
2025-08-01 09:28:07,557 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754011687.4556952, 'performance_metrics': {}}}
2025-08-01 09:28:07,558 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-01 09:28:07,558 - StrategyExpert - INFO - 开始策略分配分析
2025-08-01 09:28:07,558 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 4
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 12751.0
  • mean_cost: 79184.9
- Search-space: ruggedness 0.5, modality uni-modal, deceptiveness low
- Evolution phase: convergence
- Landscape focus suggestion: intensification
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 1): [{"type": "unexplored_space", "potential": "high", "description": "未充分探索的搜索空间", "recommendation": "增加探索性操作"}]
- Last-iteration feedback: {'overall_score': 40, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvemen...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-08-01 09:28:07,558 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-08-01 09:28:07,558 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-01 09:28:09,114 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.65,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "exploit",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High mean cost and unexplored space suggest exploration, while convergence phase and low diversity warrant balancing exploration and exploitation."
}
```
2025-08-01 09:28:09,114 - utils.utils - INFO - 成功从JSON中提取数据
2025-08-01 09:28:09,114 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit', 'exploit', 'exploit']
2025-08-01 09:28:09,114 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit', 'exploit', 'exploit']
2025-08-01 09:28:09,115 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.65,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "exploit",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High mean cost and unexplored space suggest exploration, while convergence phase and low diversity warrant balancing exploration and exploitation."
}
```
2025-08-01 09:28:09,115 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-01 09:28:09,115 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit', 'exploit', 'exploit']
2025-08-01 09:28:09,115 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.65,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "exploit",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High mean cost and unexplored space suggest exploration, while convergence phase and low diversity warrant balancing exploration and exploitation."
}
```
2025-08-01 09:28:09,115 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-01 09:28:09,116 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-01 09:28:09,116 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-01 09:28:09,116 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:28:09,118 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-01 09:28:09,118 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:28:09,118 - ExplorationExpert - INFO - 探索路径生成完成，成本: 105691.0, 路径长度: 66
2025-08-01 09:28:09,118 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [58, 36, 22, 31, 16, 32, 56, 61, 60, 46, 38, 24, 37, 62, 40, 54, 65, 43, 23, 6, 39, 27, 5, 45, 52, 20, 25, 64, 50, 53, 30, 4, 2, 33, 21, 3, 29, 12, 0, 19, 10, 7, 34, 26, 18, 48, 47, 35, 51, 49, 1, 59, 55, 9, 44, 8, 13, 28, 11, 15, 42, 57, 63, 14, 41, 17], 'cur_cost': 105691.0}
2025-08-01 09:28:09,118 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-01 09:28:09,119 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-01 09:28:09,119 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:28:09,120 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 09:28:09,120 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:28:09,121 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12905.0, 路径长度: 66
2025-08-01 09:28:09,121 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 21, 19, 11, 9, 3, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12905.0}
2025-08-01 09:28:09,121 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-01 09:28:09,121 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-01 09:28:09,121 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:28:09,126 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-01 09:28:09,126 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:28:09,127 - ExplorationExpert - INFO - 探索路径生成完成，成本: 58847.0, 路径长度: 66
2025-08-01 09:28:09,127 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [58, 20, 12, 16, 13, 19, 5, 8, 18, 34, 40, 15, 25, 35, 11, 56, 53, 63, 22, 21, 28, 6, 0, 23, 4, 27, 37, 30, 49, 26, 9, 3, 59, 62, 2, 54, 10, 65, 17, 43, 46, 41, 42, 38, 50, 14, 32, 24, 31, 39, 45, 51, 47, 48, 29, 33, 55, 52, 60, 64, 57, 1, 7, 61, 44, 36], 'cur_cost': 58847.0}
2025-08-01 09:28:09,127 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-01 09:28:09,127 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:28:09,127 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:28:09,128 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 110108.0
2025-08-01 09:28:09,200 - ExploitationExpert - INFO - res_population_num: 12
2025-08-01 09:28:09,200 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9542, 9555.0, 9555, 9592.0]
2025-08-01 09:28:09,201 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 09:28:09,205 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 09:28:09,205 - ExploitationExpert - INFO - populations: [{'tour': [58, 36, 22, 31, 16, 32, 56, 61, 60, 46, 38, 24, 37, 62, 40, 54, 65, 43, 23, 6, 39, 27, 5, 45, 52, 20, 25, 64, 50, 53, 30, 4, 2, 33, 21, 3, 29, 12, 0, 19, 10, 7, 34, 26, 18, 48, 47, 35, 51, 49, 1, 59, 55, 9, 44, 8, 13, 28, 11, 15, 42, 57, 63, 14, 41, 17], 'cur_cost': 105691.0}, {'tour': [0, 21, 19, 11, 9, 3, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12905.0}, {'tour': [58, 20, 12, 16, 13, 19, 5, 8, 18, 34, 40, 15, 25, 35, 11, 56, 53, 63, 22, 21, 28, 6, 0, 23, 4, 27, 37, 30, 49, 26, 9, 3, 59, 62, 2, 54, 10, 65, 17, 43, 46, 41, 42, 38, 50, 14, 32, 24, 31, 39, 45, 51, 47, 48, 29, 33, 55, 52, 60, 64, 57, 1, 7, 61, 44, 36], 'cur_cost': 58847.0}, {'tour': array([18,  0, 52, 14,  1, 42, 61, 12, 25, 65,  5, 45, 35, 41, 53, 16,  2,
       36,  4, 31, 50, 17, 11, 20,  6, 21, 32, 54, 26, 60, 27, 40, 43, 39,
       44, 49, 29, 55, 15, 23, 24, 10, 62, 56,  9, 46,  3, 63, 38, 47, 30,
       33,  7,  8, 37, 64, 59, 28, 22, 48, 57, 19, 34, 13, 58, 51],
      dtype=int64), 'cur_cost': 110108.0}, {'tour': [62, 3, 41, 9, 43, 4, 65, 46, 37, 45, 49, 61, 10, 38, 29, 56, 25, 1, 35, 7, 48, 0, 22, 53, 54, 31, 13, 60, 47, 21, 23, 42, 28, 51, 6, 32, 2, 15, 39, 19, 63, 44, 11, 52, 18, 14, 26, 64, 36, 17, 59, 33, 16, 5, 40, 58, 8, 12, 27, 30, 20, 34, 57, 24, 50, 55], 'cur_cost': 122657.0}, {'tour': [0, 18, 20, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12868.0}, {'tour': [62, 55, 14, 61, 63, 53, 2, 3, 38, 54, 33, 28, 32, 24, 12, 8, 64, 6, 39, 5, 50, 27, 41, 34, 49, 47, 4, 60, 29, 58, 48, 23, 45, 43, 10, 52, 0, 26, 25, 40, 1, 59, 9, 22, 11, 13, 20, 35, 46, 15, 36, 16, 21, 18, 51, 19, 37, 30, 42, 17, 57, 44, 65, 7, 31, 56], 'cur_cost': 94343.0}, {'tour': [33, 15, 1, 4, 61, 43, 37, 31, 16, 32, 27, 28, 0, 22, 54, 14, 49, 35, 7, 36, 44, 63, 23, 39, 11, 55, 62, 41, 26, 64, 10, 53, 45, 24, 52, 42, 17, 40, 59, 51, 38, 57, 8, 25, 12, 34, 65, 20, 58, 46, 47, 60, 13, 56, 18, 6, 21, 30, 29, 19, 3, 5, 48, 9, 2, 50], 'cur_cost': 109289.0}, {'tour': [0, 15, 2, 9, 11, 7, 3, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12773.0}, {'tour': [0, 15, 17, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12751.0}]
2025-08-01 09:28:09,206 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-08-01 09:28:09,206 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 20, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 20, 'cache_hits': 0, 'similarity_calculations': 190, 'cache_hit_rate': 0.0, 'cache_size': 190}}
2025-08-01 09:28:09,206 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([18,  0, 52, 14,  1, 42, 61, 12, 25, 65,  5, 45, 35, 41, 53, 16,  2,
       36,  4, 31, 50, 17, 11, 20,  6, 21, 32, 54, 26, 60, 27, 40, 43, 39,
       44, 49, 29, 55, 15, 23, 24, 10, 62, 56,  9, 46,  3, 63, 38, 47, 30,
       33,  7,  8, 37, 64, 59, 28, 22, 48, 57, 19, 34, 13, 58, 51],
      dtype=int64), 'cur_cost': 110108.0}
2025-08-01 09:28:09,206 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-01 09:28:09,206 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:28:09,207 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:28:09,207 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 112439.0
2025-08-01 09:28:09,268 - ExploitationExpert - INFO - res_population_num: 12
2025-08-01 09:28:09,268 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9542, 9555.0, 9555, 9592.0]
2025-08-01 09:28:09,268 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 09:28:09,272 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 09:28:09,272 - ExploitationExpert - INFO - populations: [{'tour': [58, 36, 22, 31, 16, 32, 56, 61, 60, 46, 38, 24, 37, 62, 40, 54, 65, 43, 23, 6, 39, 27, 5, 45, 52, 20, 25, 64, 50, 53, 30, 4, 2, 33, 21, 3, 29, 12, 0, 19, 10, 7, 34, 26, 18, 48, 47, 35, 51, 49, 1, 59, 55, 9, 44, 8, 13, 28, 11, 15, 42, 57, 63, 14, 41, 17], 'cur_cost': 105691.0}, {'tour': [0, 21, 19, 11, 9, 3, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12905.0}, {'tour': [58, 20, 12, 16, 13, 19, 5, 8, 18, 34, 40, 15, 25, 35, 11, 56, 53, 63, 22, 21, 28, 6, 0, 23, 4, 27, 37, 30, 49, 26, 9, 3, 59, 62, 2, 54, 10, 65, 17, 43, 46, 41, 42, 38, 50, 14, 32, 24, 31, 39, 45, 51, 47, 48, 29, 33, 55, 52, 60, 64, 57, 1, 7, 61, 44, 36], 'cur_cost': 58847.0}, {'tour': array([18,  0, 52, 14,  1, 42, 61, 12, 25, 65,  5, 45, 35, 41, 53, 16,  2,
       36,  4, 31, 50, 17, 11, 20,  6, 21, 32, 54, 26, 60, 27, 40, 43, 39,
       44, 49, 29, 55, 15, 23, 24, 10, 62, 56,  9, 46,  3, 63, 38, 47, 30,
       33,  7,  8, 37, 64, 59, 28, 22, 48, 57, 19, 34, 13, 58, 51],
      dtype=int64), 'cur_cost': 110108.0}, {'tour': array([10, 23,  7, 39, 26, 50,  8, 51, 62, 61, 49, 11, 35, 19,  6, 55, 60,
       45, 28, 58, 40,  2, 24,  9, 63, 25, 16,  5, 12, 54, 42, 31, 36, 20,
       37, 18, 59, 41, 48, 56,  3, 65, 52, 38, 44, 13, 14, 57, 21, 22, 43,
        0, 53, 32, 33, 47, 15, 34,  1, 27, 17, 46, 64, 30,  4, 29],
      dtype=int64), 'cur_cost': 112439.0}, {'tour': [0, 18, 20, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12868.0}, {'tour': [62, 55, 14, 61, 63, 53, 2, 3, 38, 54, 33, 28, 32, 24, 12, 8, 64, 6, 39, 5, 50, 27, 41, 34, 49, 47, 4, 60, 29, 58, 48, 23, 45, 43, 10, 52, 0, 26, 25, 40, 1, 59, 9, 22, 11, 13, 20, 35, 46, 15, 36, 16, 21, 18, 51, 19, 37, 30, 42, 17, 57, 44, 65, 7, 31, 56], 'cur_cost': 94343.0}, {'tour': [33, 15, 1, 4, 61, 43, 37, 31, 16, 32, 27, 28, 0, 22, 54, 14, 49, 35, 7, 36, 44, 63, 23, 39, 11, 55, 62, 41, 26, 64, 10, 53, 45, 24, 52, 42, 17, 40, 59, 51, 38, 57, 8, 25, 12, 34, 65, 20, 58, 46, 47, 60, 13, 56, 18, 6, 21, 30, 29, 19, 3, 5, 48, 9, 2, 50], 'cur_cost': 109289.0}, {'tour': [0, 15, 2, 9, 11, 7, 3, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12773.0}, {'tour': [0, 15, 17, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12751.0}]
2025-08-01 09:28:09,273 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-08-01 09:28:09,273 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 21, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 21, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 09:28:09,274 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([10, 23,  7, 39, 26, 50,  8, 51, 62, 61, 49, 11, 35, 19,  6, 55, 60,
       45, 28, 58, 40,  2, 24,  9, 63, 25, 16,  5, 12, 54, 42, 31, 36, 20,
       37, 18, 59, 41, 48, 56,  3, 65, 52, 38, 44, 13, 14, 57, 21, 22, 43,
        0, 53, 32, 33, 47, 15, 34,  1, 27, 17, 46, 64, 30,  4, 29],
      dtype=int64), 'cur_cost': 112439.0}
2025-08-01 09:28:09,274 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-01 09:28:09,274 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:28:09,274 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:28:09,275 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 113015.0
2025-08-01 09:28:10,225 - ExploitationExpert - INFO - res_population_num: 13
2025-08-01 09:28:10,225 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9542, 9555.0, 9555, 9592.0, 9521]
2025-08-01 09:28:10,225 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-01 09:28:10,230 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 09:28:10,230 - ExploitationExpert - INFO - populations: [{'tour': [58, 36, 22, 31, 16, 32, 56, 61, 60, 46, 38, 24, 37, 62, 40, 54, 65, 43, 23, 6, 39, 27, 5, 45, 52, 20, 25, 64, 50, 53, 30, 4, 2, 33, 21, 3, 29, 12, 0, 19, 10, 7, 34, 26, 18, 48, 47, 35, 51, 49, 1, 59, 55, 9, 44, 8, 13, 28, 11, 15, 42, 57, 63, 14, 41, 17], 'cur_cost': 105691.0}, {'tour': [0, 21, 19, 11, 9, 3, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12905.0}, {'tour': [58, 20, 12, 16, 13, 19, 5, 8, 18, 34, 40, 15, 25, 35, 11, 56, 53, 63, 22, 21, 28, 6, 0, 23, 4, 27, 37, 30, 49, 26, 9, 3, 59, 62, 2, 54, 10, 65, 17, 43, 46, 41, 42, 38, 50, 14, 32, 24, 31, 39, 45, 51, 47, 48, 29, 33, 55, 52, 60, 64, 57, 1, 7, 61, 44, 36], 'cur_cost': 58847.0}, {'tour': array([18,  0, 52, 14,  1, 42, 61, 12, 25, 65,  5, 45, 35, 41, 53, 16,  2,
       36,  4, 31, 50, 17, 11, 20,  6, 21, 32, 54, 26, 60, 27, 40, 43, 39,
       44, 49, 29, 55, 15, 23, 24, 10, 62, 56,  9, 46,  3, 63, 38, 47, 30,
       33,  7,  8, 37, 64, 59, 28, 22, 48, 57, 19, 34, 13, 58, 51],
      dtype=int64), 'cur_cost': 110108.0}, {'tour': array([10, 23,  7, 39, 26, 50,  8, 51, 62, 61, 49, 11, 35, 19,  6, 55, 60,
       45, 28, 58, 40,  2, 24,  9, 63, 25, 16,  5, 12, 54, 42, 31, 36, 20,
       37, 18, 59, 41, 48, 56,  3, 65, 52, 38, 44, 13, 14, 57, 21, 22, 43,
        0, 53, 32, 33, 47, 15, 34,  1, 27, 17, 46, 64, 30,  4, 29],
      dtype=int64), 'cur_cost': 112439.0}, {'tour': array([24, 33, 65, 61, 49, 41,  1, 51, 18, 39, 26,  9, 28, 35, 15, 64, 21,
       31, 11, 40, 12,  6, 43, 52,  4, 46, 16, 42, 22,  8, 32, 38, 50, 57,
        7,  3, 25,  2, 45,  0, 37, 23, 58, 54, 17, 53, 48,  5, 20, 36, 27,
       55, 13, 34, 47, 10, 19, 63, 30, 56, 59, 29, 60, 62, 14, 44],
      dtype=int64), 'cur_cost': 113015.0}, {'tour': [62, 55, 14, 61, 63, 53, 2, 3, 38, 54, 33, 28, 32, 24, 12, 8, 64, 6, 39, 5, 50, 27, 41, 34, 49, 47, 4, 60, 29, 58, 48, 23, 45, 43, 10, 52, 0, 26, 25, 40, 1, 59, 9, 22, 11, 13, 20, 35, 46, 15, 36, 16, 21, 18, 51, 19, 37, 30, 42, 17, 57, 44, 65, 7, 31, 56], 'cur_cost': 94343.0}, {'tour': [33, 15, 1, 4, 61, 43, 37, 31, 16, 32, 27, 28, 0, 22, 54, 14, 49, 35, 7, 36, 44, 63, 23, 39, 11, 55, 62, 41, 26, 64, 10, 53, 45, 24, 52, 42, 17, 40, 59, 51, 38, 57, 8, 25, 12, 34, 65, 20, 58, 46, 47, 60, 13, 56, 18, 6, 21, 30, 29, 19, 3, 5, 48, 9, 2, 50], 'cur_cost': 109289.0}, {'tour': [0, 15, 2, 9, 11, 7, 3, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12773.0}, {'tour': [0, 15, 17, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12751.0}]
2025-08-01 09:28:10,231 - ExploitationExpert - INFO - 局部搜索耗时: 0.96秒
2025-08-01 09:28:10,232 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 22, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 22, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 09:28:10,232 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([24, 33, 65, 61, 49, 41,  1, 51, 18, 39, 26,  9, 28, 35, 15, 64, 21,
       31, 11, 40, 12,  6, 43, 52,  4, 46, 16, 42, 22,  8, 32, 38, 50, 57,
        7,  3, 25,  2, 45,  0, 37, 23, 58, 54, 17, 53, 48,  5, 20, 36, 27,
       55, 13, 34, 47, 10, 19, 63, 30, 56, 59, 29, 60, 62, 14, 44],
      dtype=int64), 'cur_cost': 113015.0}
2025-08-01 09:28:10,232 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-01 09:28:10,232 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-01 09:28:10,232 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 09:28:10,235 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 09:28:10,235 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 09:28:10,235 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10399.0, 路径长度: 66
2025-08-01 09:28:10,235 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 2, 11, 9, 3, 7, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10399.0}
2025-08-01 09:28:10,236 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-01 09:28:10,236 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:28:10,236 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:28:10,236 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 112424.0
2025-08-01 09:28:10,312 - ExploitationExpert - INFO - res_population_num: 13
2025-08-01 09:28:10,312 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9542, 9555.0, 9555, 9592.0, 9521]
2025-08-01 09:28:10,312 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-01 09:28:10,317 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 09:28:10,317 - ExploitationExpert - INFO - populations: [{'tour': [58, 36, 22, 31, 16, 32, 56, 61, 60, 46, 38, 24, 37, 62, 40, 54, 65, 43, 23, 6, 39, 27, 5, 45, 52, 20, 25, 64, 50, 53, 30, 4, 2, 33, 21, 3, 29, 12, 0, 19, 10, 7, 34, 26, 18, 48, 47, 35, 51, 49, 1, 59, 55, 9, 44, 8, 13, 28, 11, 15, 42, 57, 63, 14, 41, 17], 'cur_cost': 105691.0}, {'tour': [0, 21, 19, 11, 9, 3, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12905.0}, {'tour': [58, 20, 12, 16, 13, 19, 5, 8, 18, 34, 40, 15, 25, 35, 11, 56, 53, 63, 22, 21, 28, 6, 0, 23, 4, 27, 37, 30, 49, 26, 9, 3, 59, 62, 2, 54, 10, 65, 17, 43, 46, 41, 42, 38, 50, 14, 32, 24, 31, 39, 45, 51, 47, 48, 29, 33, 55, 52, 60, 64, 57, 1, 7, 61, 44, 36], 'cur_cost': 58847.0}, {'tour': array([18,  0, 52, 14,  1, 42, 61, 12, 25, 65,  5, 45, 35, 41, 53, 16,  2,
       36,  4, 31, 50, 17, 11, 20,  6, 21, 32, 54, 26, 60, 27, 40, 43, 39,
       44, 49, 29, 55, 15, 23, 24, 10, 62, 56,  9, 46,  3, 63, 38, 47, 30,
       33,  7,  8, 37, 64, 59, 28, 22, 48, 57, 19, 34, 13, 58, 51],
      dtype=int64), 'cur_cost': 110108.0}, {'tour': array([10, 23,  7, 39, 26, 50,  8, 51, 62, 61, 49, 11, 35, 19,  6, 55, 60,
       45, 28, 58, 40,  2, 24,  9, 63, 25, 16,  5, 12, 54, 42, 31, 36, 20,
       37, 18, 59, 41, 48, 56,  3, 65, 52, 38, 44, 13, 14, 57, 21, 22, 43,
        0, 53, 32, 33, 47, 15, 34,  1, 27, 17, 46, 64, 30,  4, 29],
      dtype=int64), 'cur_cost': 112439.0}, {'tour': array([24, 33, 65, 61, 49, 41,  1, 51, 18, 39, 26,  9, 28, 35, 15, 64, 21,
       31, 11, 40, 12,  6, 43, 52,  4, 46, 16, 42, 22,  8, 32, 38, 50, 57,
        7,  3, 25,  2, 45,  0, 37, 23, 58, 54, 17, 53, 48,  5, 20, 36, 27,
       55, 13, 34, 47, 10, 19, 63, 30, 56, 59, 29, 60, 62, 14, 44],
      dtype=int64), 'cur_cost': 113015.0}, {'tour': [0, 2, 11, 9, 3, 7, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10399.0}, {'tour': array([64, 20, 43, 19, 25, 49, 34, 52, 60, 39, 14,  5, 15, 16, 61, 29,  3,
       41, 63, 33, 28, 27, 36, 55, 53, 51, 21, 11,  2, 47, 56, 22,  1, 45,
        4, 65,  8, 46, 12, 48, 32, 37, 54, 18, 42, 57, 13, 38, 59, 62, 35,
       44,  7, 30, 58, 10, 17, 23,  6, 31, 26, 24,  9, 50,  0, 40],
      dtype=int64), 'cur_cost': 112424.0}, {'tour': [0, 15, 2, 9, 11, 7, 3, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12773.0}, {'tour': [0, 15, 17, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12751.0}]
2025-08-01 09:28:10,319 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-08-01 09:28:10,320 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 23, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 23, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 09:28:10,320 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([64, 20, 43, 19, 25, 49, 34, 52, 60, 39, 14,  5, 15, 16, 61, 29,  3,
       41, 63, 33, 28, 27, 36, 55, 53, 51, 21, 11,  2, 47, 56, 22,  1, 45,
        4, 65,  8, 46, 12, 48, 32, 37, 54, 18, 42, 57, 13, 38, 59, 62, 35,
       44,  7, 30, 58, 10, 17, 23,  6, 31, 26, 24,  9, 50,  0, 40],
      dtype=int64), 'cur_cost': 112424.0}
2025-08-01 09:28:10,320 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-01 09:28:10,320 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:28:10,320 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:28:10,321 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 114335.0
2025-08-01 09:28:10,387 - ExploitationExpert - INFO - res_population_num: 13
2025-08-01 09:28:10,387 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9542, 9555.0, 9555, 9592.0, 9521]
2025-08-01 09:28:10,387 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-01 09:28:10,392 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 09:28:10,392 - ExploitationExpert - INFO - populations: [{'tour': [58, 36, 22, 31, 16, 32, 56, 61, 60, 46, 38, 24, 37, 62, 40, 54, 65, 43, 23, 6, 39, 27, 5, 45, 52, 20, 25, 64, 50, 53, 30, 4, 2, 33, 21, 3, 29, 12, 0, 19, 10, 7, 34, 26, 18, 48, 47, 35, 51, 49, 1, 59, 55, 9, 44, 8, 13, 28, 11, 15, 42, 57, 63, 14, 41, 17], 'cur_cost': 105691.0}, {'tour': [0, 21, 19, 11, 9, 3, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12905.0}, {'tour': [58, 20, 12, 16, 13, 19, 5, 8, 18, 34, 40, 15, 25, 35, 11, 56, 53, 63, 22, 21, 28, 6, 0, 23, 4, 27, 37, 30, 49, 26, 9, 3, 59, 62, 2, 54, 10, 65, 17, 43, 46, 41, 42, 38, 50, 14, 32, 24, 31, 39, 45, 51, 47, 48, 29, 33, 55, 52, 60, 64, 57, 1, 7, 61, 44, 36], 'cur_cost': 58847.0}, {'tour': array([18,  0, 52, 14,  1, 42, 61, 12, 25, 65,  5, 45, 35, 41, 53, 16,  2,
       36,  4, 31, 50, 17, 11, 20,  6, 21, 32, 54, 26, 60, 27, 40, 43, 39,
       44, 49, 29, 55, 15, 23, 24, 10, 62, 56,  9, 46,  3, 63, 38, 47, 30,
       33,  7,  8, 37, 64, 59, 28, 22, 48, 57, 19, 34, 13, 58, 51],
      dtype=int64), 'cur_cost': 110108.0}, {'tour': array([10, 23,  7, 39, 26, 50,  8, 51, 62, 61, 49, 11, 35, 19,  6, 55, 60,
       45, 28, 58, 40,  2, 24,  9, 63, 25, 16,  5, 12, 54, 42, 31, 36, 20,
       37, 18, 59, 41, 48, 56,  3, 65, 52, 38, 44, 13, 14, 57, 21, 22, 43,
        0, 53, 32, 33, 47, 15, 34,  1, 27, 17, 46, 64, 30,  4, 29],
      dtype=int64), 'cur_cost': 112439.0}, {'tour': array([24, 33, 65, 61, 49, 41,  1, 51, 18, 39, 26,  9, 28, 35, 15, 64, 21,
       31, 11, 40, 12,  6, 43, 52,  4, 46, 16, 42, 22,  8, 32, 38, 50, 57,
        7,  3, 25,  2, 45,  0, 37, 23, 58, 54, 17, 53, 48,  5, 20, 36, 27,
       55, 13, 34, 47, 10, 19, 63, 30, 56, 59, 29, 60, 62, 14, 44],
      dtype=int64), 'cur_cost': 113015.0}, {'tour': [0, 2, 11, 9, 3, 7, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10399.0}, {'tour': array([64, 20, 43, 19, 25, 49, 34, 52, 60, 39, 14,  5, 15, 16, 61, 29,  3,
       41, 63, 33, 28, 27, 36, 55, 53, 51, 21, 11,  2, 47, 56, 22,  1, 45,
        4, 65,  8, 46, 12, 48, 32, 37, 54, 18, 42, 57, 13, 38, 59, 62, 35,
       44,  7, 30, 58, 10, 17, 23,  6, 31, 26, 24,  9, 50,  0, 40],
      dtype=int64), 'cur_cost': 112424.0}, {'tour': array([ 3, 36, 40, 46, 62,  8, 37,  1,  5, 48, 54,  4, 43, 28, 45, 30, 42,
       19, 38, 15, 61, 12, 60, 49, 29, 53, 18, 35, 14, 31, 11, 22,  7,  2,
        9, 13, 57, 47,  0, 21, 55, 26, 17, 10, 63, 41, 34, 50, 32, 64,  6,
       52, 23, 24, 65, 59, 33, 25, 16, 20, 58, 44, 56, 39, 51, 27],
      dtype=int64), 'cur_cost': 114335.0}, {'tour': [0, 15, 17, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12751.0}]
2025-08-01 09:28:10,394 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-08-01 09:28:10,394 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 24, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 24, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 09:28:10,394 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 3, 36, 40, 46, 62,  8, 37,  1,  5, 48, 54,  4, 43, 28, 45, 30, 42,
       19, 38, 15, 61, 12, 60, 49, 29, 53, 18, 35, 14, 31, 11, 22,  7,  2,
        9, 13, 57, 47,  0, 21, 55, 26, 17, 10, 63, 41, 34, 50, 32, 64,  6,
       52, 23, 24, 65, 59, 33, 25, 16, 20, 58, 44, 56, 39, 51, 27],
      dtype=int64), 'cur_cost': 114335.0}
2025-08-01 09:28:10,394 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-01 09:28:10,394 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 09:28:10,395 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 09:28:10,395 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 101121.0
2025-08-01 09:28:10,464 - ExploitationExpert - INFO - res_population_num: 13
2025-08-01 09:28:10,464 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9542, 9555.0, 9555, 9592.0, 9521]
2025-08-01 09:28:10,464 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 24, 31, 37, 36, 26, 25, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-01 09:28:10,469 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 09:28:10,469 - ExploitationExpert - INFO - populations: [{'tour': [58, 36, 22, 31, 16, 32, 56, 61, 60, 46, 38, 24, 37, 62, 40, 54, 65, 43, 23, 6, 39, 27, 5, 45, 52, 20, 25, 64, 50, 53, 30, 4, 2, 33, 21, 3, 29, 12, 0, 19, 10, 7, 34, 26, 18, 48, 47, 35, 51, 49, 1, 59, 55, 9, 44, 8, 13, 28, 11, 15, 42, 57, 63, 14, 41, 17], 'cur_cost': 105691.0}, {'tour': [0, 21, 19, 11, 9, 3, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12905.0}, {'tour': [58, 20, 12, 16, 13, 19, 5, 8, 18, 34, 40, 15, 25, 35, 11, 56, 53, 63, 22, 21, 28, 6, 0, 23, 4, 27, 37, 30, 49, 26, 9, 3, 59, 62, 2, 54, 10, 65, 17, 43, 46, 41, 42, 38, 50, 14, 32, 24, 31, 39, 45, 51, 47, 48, 29, 33, 55, 52, 60, 64, 57, 1, 7, 61, 44, 36], 'cur_cost': 58847.0}, {'tour': array([18,  0, 52, 14,  1, 42, 61, 12, 25, 65,  5, 45, 35, 41, 53, 16,  2,
       36,  4, 31, 50, 17, 11, 20,  6, 21, 32, 54, 26, 60, 27, 40, 43, 39,
       44, 49, 29, 55, 15, 23, 24, 10, 62, 56,  9, 46,  3, 63, 38, 47, 30,
       33,  7,  8, 37, 64, 59, 28, 22, 48, 57, 19, 34, 13, 58, 51],
      dtype=int64), 'cur_cost': 110108.0}, {'tour': array([10, 23,  7, 39, 26, 50,  8, 51, 62, 61, 49, 11, 35, 19,  6, 55, 60,
       45, 28, 58, 40,  2, 24,  9, 63, 25, 16,  5, 12, 54, 42, 31, 36, 20,
       37, 18, 59, 41, 48, 56,  3, 65, 52, 38, 44, 13, 14, 57, 21, 22, 43,
        0, 53, 32, 33, 47, 15, 34,  1, 27, 17, 46, 64, 30,  4, 29],
      dtype=int64), 'cur_cost': 112439.0}, {'tour': array([24, 33, 65, 61, 49, 41,  1, 51, 18, 39, 26,  9, 28, 35, 15, 64, 21,
       31, 11, 40, 12,  6, 43, 52,  4, 46, 16, 42, 22,  8, 32, 38, 50, 57,
        7,  3, 25,  2, 45,  0, 37, 23, 58, 54, 17, 53, 48,  5, 20, 36, 27,
       55, 13, 34, 47, 10, 19, 63, 30, 56, 59, 29, 60, 62, 14, 44],
      dtype=int64), 'cur_cost': 113015.0}, {'tour': [0, 2, 11, 9, 3, 7, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10399.0}, {'tour': array([64, 20, 43, 19, 25, 49, 34, 52, 60, 39, 14,  5, 15, 16, 61, 29,  3,
       41, 63, 33, 28, 27, 36, 55, 53, 51, 21, 11,  2, 47, 56, 22,  1, 45,
        4, 65,  8, 46, 12, 48, 32, 37, 54, 18, 42, 57, 13, 38, 59, 62, 35,
       44,  7, 30, 58, 10, 17, 23,  6, 31, 26, 24,  9, 50,  0, 40],
      dtype=int64), 'cur_cost': 112424.0}, {'tour': array([ 3, 36, 40, 46, 62,  8, 37,  1,  5, 48, 54,  4, 43, 28, 45, 30, 42,
       19, 38, 15, 61, 12, 60, 49, 29, 53, 18, 35, 14, 31, 11, 22,  7,  2,
        9, 13, 57, 47,  0, 21, 55, 26, 17, 10, 63, 41, 34, 50, 32, 64,  6,
       52, 23, 24, 65, 59, 33, 25, 16, 20, 58, 44, 56, 39, 51, 27],
      dtype=int64), 'cur_cost': 114335.0}, {'tour': array([26, 17,  6, 27,  3, 58, 64, 39, 38, 47, 59,  2, 24, 37,  4, 29, 18,
        1, 40, 52, 16, 11, 19, 65, 34, 54,  7, 56, 57, 55, 21, 25, 13, 22,
       42, 30,  0, 10, 28, 20, 45, 62, 31, 32,  9, 33, 12, 50, 61, 60, 46,
       51, 49, 36,  5, 48, 43, 63, 41, 53, 15, 35, 14,  8, 23, 44],
      dtype=int64), 'cur_cost': 101121.0}]
2025-08-01 09:28:10,472 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-08-01 09:28:10,472 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 25, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 25, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 09:28:10,472 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([26, 17,  6, 27,  3, 58, 64, 39, 38, 47, 59,  2, 24, 37,  4, 29, 18,
        1, 40, 52, 16, 11, 19, 65, 34, 54,  7, 56, 57, 55, 21, 25, 13, 22,
       42, 30,  0, 10, 28, 20, 45, 62, 31, 32,  9, 33, 12, 50, 61, 60, 46,
       51, 49, 36,  5, 48, 43, 63, 41, 53, 15, 35, 14,  8, 23, 44],
      dtype=int64), 'cur_cost': 101121.0}
2025-08-01 09:28:10,473 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [58, 36, 22, 31, 16, 32, 56, 61, 60, 46, 38, 24, 37, 62, 40, 54, 65, 43, 23, 6, 39, 27, 5, 45, 52, 20, 25, 64, 50, 53, 30, 4, 2, 33, 21, 3, 29, 12, 0, 19, 10, 7, 34, 26, 18, 48, 47, 35, 51, 49, 1, 59, 55, 9, 44, 8, 13, 28, 11, 15, 42, 57, 63, 14, 41, 17], 'cur_cost': 105691.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 19, 11, 9, 3, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12905.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [58, 20, 12, 16, 13, 19, 5, 8, 18, 34, 40, 15, 25, 35, 11, 56, 53, 63, 22, 21, 28, 6, 0, 23, 4, 27, 37, 30, 49, 26, 9, 3, 59, 62, 2, 54, 10, 65, 17, 43, 46, 41, 42, 38, 50, 14, 32, 24, 31, 39, 45, 51, 47, 48, 29, 33, 55, 52, 60, 64, 57, 1, 7, 61, 44, 36], 'cur_cost': 58847.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([18,  0, 52, 14,  1, 42, 61, 12, 25, 65,  5, 45, 35, 41, 53, 16,  2,
       36,  4, 31, 50, 17, 11, 20,  6, 21, 32, 54, 26, 60, 27, 40, 43, 39,
       44, 49, 29, 55, 15, 23, 24, 10, 62, 56,  9, 46,  3, 63, 38, 47, 30,
       33,  7,  8, 37, 64, 59, 28, 22, 48, 57, 19, 34, 13, 58, 51],
      dtype=int64), 'cur_cost': 110108.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([10, 23,  7, 39, 26, 50,  8, 51, 62, 61, 49, 11, 35, 19,  6, 55, 60,
       45, 28, 58, 40,  2, 24,  9, 63, 25, 16,  5, 12, 54, 42, 31, 36, 20,
       37, 18, 59, 41, 48, 56,  3, 65, 52, 38, 44, 13, 14, 57, 21, 22, 43,
        0, 53, 32, 33, 47, 15, 34,  1, 27, 17, 46, 64, 30,  4, 29],
      dtype=int64), 'cur_cost': 112439.0}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([24, 33, 65, 61, 49, 41,  1, 51, 18, 39, 26,  9, 28, 35, 15, 64, 21,
       31, 11, 40, 12,  6, 43, 52,  4, 46, 16, 42, 22,  8, 32, 38, 50, 57,
        7,  3, 25,  2, 45,  0, 37, 23, 58, 54, 17, 53, 48,  5, 20, 36, 27,
       55, 13, 34, 47, 10, 19, 63, 30, 56, 59, 29, 60, 62, 14, 44],
      dtype=int64), 'cur_cost': 113015.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 11, 9, 3, 7, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10399.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([64, 20, 43, 19, 25, 49, 34, 52, 60, 39, 14,  5, 15, 16, 61, 29,  3,
       41, 63, 33, 28, 27, 36, 55, 53, 51, 21, 11,  2, 47, 56, 22,  1, 45,
        4, 65,  8, 46, 12, 48, 32, 37, 54, 18, 42, 57, 13, 38, 59, 62, 35,
       44,  7, 30, 58, 10, 17, 23,  6, 31, 26, 24,  9, 50,  0, 40],
      dtype=int64), 'cur_cost': 112424.0}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 3, 36, 40, 46, 62,  8, 37,  1,  5, 48, 54,  4, 43, 28, 45, 30, 42,
       19, 38, 15, 61, 12, 60, 49, 29, 53, 18, 35, 14, 31, 11, 22,  7,  2,
        9, 13, 57, 47,  0, 21, 55, 26, 17, 10, 63, 41, 34, 50, 32, 64,  6,
       52, 23, 24, 65, 59, 33, 25, 16, 20, 58, 44, 56, 39, 51, 27],
      dtype=int64), 'cur_cost': 114335.0}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([26, 17,  6, 27,  3, 58, 64, 39, 38, 47, 59,  2, 24, 37,  4, 29, 18,
        1, 40, 52, 16, 11, 19, 65, 34, 54,  7, 56, 57, 55, 21, 25, 13, 22,
       42, 30,  0, 10, 28, 20, 45, 62, 31, 32,  9, 33, 12, 50, 61, 60, 46,
       51, 49, 36,  5, 48, 43, 63, 41, 53, 15, 35, 14,  8, 23, 44],
      dtype=int64), 'cur_cost': 101121.0}}]
2025-08-01 09:28:10,473 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-01 09:28:10,474 - StatsExpert - INFO - 开始统计分析
2025-08-01 09:28:10,490 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10399.0, 多样性=0.948
2025-08-01 09:28:10,490 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-01 09:28:10,490 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-01 09:28:10,491 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-01 09:28:10,493 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.05908178559138927, 'best_improvement': 0.18445612108854206}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.03758290346352235}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.04531428640504819, 'recent_improvements': [-0.11050740279013356, 0.006132492884099498, -0.019878829980037194], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 13, 'new_count': 13, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.6961926961926962, 'new_diversity': 0.6961926961926962, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-01 09:28:10,495 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-01 09:28:10,505 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_solution.json
2025-08-01 09:28:10,505 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_20250801_092810.solution
2025-08-01 09:28:10,505 - __main__ - INFO - 实例 composite13_66 处理完成
