2025-08-01 10:09:56,125 - __main__ - INFO - simple1_9 开始进化第 1 代
2025-08-01 10:09:56,125 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-01 10:09:56,126 - StatsExpert - INFO - 开始统计分析
2025-08-01 10:09:56,126 - StatsExpert - INFO - 统计分析完成: 种群大小=4, 最优成本=747.0, 多样性=0.685
2025-08-01 10:09:56,126 - PathExpert - INFO - 开始路径结构分析
2025-08-01 10:09:56,127 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.093
2025-08-01 10:09:56,161 - EliteExpert - INFO - 开始精英解分析
2025-08-01 10:09:56,163 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/1)
2025-08-01 10:09:56,163 - LandscapeExpert - INFO - 从统计报告生成模拟数据: 4个路径, 4个适应度值
2025-08-01 10:09:56,163 - LandscapeExpert - INFO - 数据提取成功: 4个路径, 4个适应度值
2025-08-01 10:09:56,403 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-01 10:09:56,405 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-01 10:09:56,524 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记
2025-08-01 10:09:56,858 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_1_20250801_100956.html
2025-08-01 10:09:56,911 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_1_20250801_100956.html
2025-08-01 10:09:56,911 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-01 10:09:56,911 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-01 10:09:56,912 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.7484秒
2025-08-01 10:09:56,912 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 1, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754014196.4030216, 'performance_metrics': {}}}
2025-08-01 10:09:56,912 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-01 10:09:56,913 - StrategyExpert - INFO - 开始策略分配分析
2025-08-01 10:09:56,913 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 4 individuals
  • diversity: 0.5
  • best_cost: 747.0
  • mean_cost: 911.5
- Search-space: ruggedness 0.5, modality uni-modal, deceptiveness low
- Evolution phase: exploration
- Landscape focus suggestion: exploration
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 1): [{"type": "unexplored_space", "potential": "high", "description": "未充分探索的搜索空间", "recommendation": "增加探索性操作"}]
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-08-01 10:09:56,915 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-08-01 10:09:56,916 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-01 10:09:58,213 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.80,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit"
  },
  "rationale": "Exploration phase with unexplored space and low diversity. Focus on exploring the search space with increased exploration."
}
```
2025-08-01 10:09:58,214 - utils.utils - INFO - 成功从JSON中提取数据
2025-08-01 10:09:58,214 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'exploit']
2025-08-01 10:09:58,214 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit']
2025-08-01 10:09:58,215 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.80,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit"
  },
  "rationale": "Exploration phase with unexplored space and low diversity. Focus on exploring the search space with increased exploration."
}
```
2025-08-01 10:09:58,216 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-01 10:09:58,216 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit']
2025-08-01 10:09:58,216 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.80,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit"
  },
  "rationale": "Exploration phase with unexplored space and low diversity. Focus on exploring the search space with increased exploration."
}
```
2025-08-01 10:09:58,217 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-01 10:09:58,217 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-01 10:09:58,217 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-01 10:09:58,217 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:09:58,218 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-01 10:09:58,218 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:09:58,402 - ExplorationExpert - INFO - 探索路径生成完成，成本: 788.0, 路径长度: 9
2025-08-01 10:09:58,403 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 5, 3, 6, 7, 8, 4, 2, 1], 'cur_cost': 788.0}
2025-08-01 10:09:58,403 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-01 10:09:58,404 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-01 10:09:58,405 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:09:58,408 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-01 10:09:58,408 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:09:58,409 - ExplorationExpert - INFO - 探索路径生成完成，成本: 889.0, 路径长度: 9
2025-08-01 10:09:58,409 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [2, 4, 1, 3, 6, 5, 7, 0, 8], 'cur_cost': 889.0}
2025-08-01 10:09:58,410 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-01 10:09:58,410 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-01 10:09:58,411 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:09:58,411 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-01 10:09:58,411 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:09:58,412 - ExplorationExpert - INFO - 探索路径生成完成，成本: 807.0, 路径长度: 9
2025-08-01 10:09:58,412 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 4, 2, 3, 5, 6, 7, 8, 1], 'cur_cost': 807.0}
2025-08-01 10:09:58,413 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-01 10:09:58,413 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:09:58,415 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:09:58,417 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 975.0
2025-08-01 10:10:00,536 - ExploitationExpert - INFO - res_population_num: 1
2025-08-01 10:10:00,539 - ExploitationExpert - INFO - res_population_costs: [789.0]
2025-08-01 10:10:00,541 - ExploitationExpert - INFO - res_populations: [array([0, 1, 2, 8, 5, 6, 3, 7, 4], dtype=int64)]
2025-08-01 10:10:00,543 - ExploitationExpert - INFO - populations_num: 4
2025-08-01 10:10:00,543 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 3, 6, 7, 8, 4, 2, 1], 'cur_cost': 788.0}, {'tour': [2, 4, 1, 3, 6, 5, 7, 0, 8], 'cur_cost': 889.0}, {'tour': [0, 4, 2, 3, 5, 6, 7, 8, 1], 'cur_cost': 807.0}, {'tour': array([8, 1, 7, 0, 4, 2, 6, 3, 5], dtype=int64), 'cur_cost': 975.0}]
2025-08-01 10:10:00,543 - ExploitationExpert - INFO - 局部搜索耗时: 2.13秒
2025-08-01 10:10:00,544 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-01 10:10:00,544 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([8, 1, 7, 0, 4, 2, 6, 3, 5], dtype=int64), 'cur_cost': 975.0}
2025-08-01 10:10:00,545 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 3, 6, 7, 8, 4, 2, 1], 'cur_cost': 788.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [2, 4, 1, 3, 6, 5, 7, 0, 8], 'cur_cost': 889.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 2, 3, 5, 6, 7, 8, 1], 'cur_cost': 807.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 1, 7, 0, 4, 2, 6, 3, 5], dtype=int64), 'cur_cost': 975.0}}]
2025-08-01 10:10:00,545 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-01 10:10:00,545 - StatsExpert - INFO - 开始统计分析
2025-08-01 10:10:00,546 - StatsExpert - INFO - 统计分析完成: 种群大小=4, 最优成本=788.0, 多样性=0.648
2025-08-01 10:10:00,546 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-01 10:10:00,546 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-01 10:10:00,547 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-01 10:10:00,547 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 1, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.012252401041178586, 'best_improvement': -0.05488621151271754}, 'diversity_analysis': {'status': 'moderate_diversity', 'change_rate': -0.054054054054053814}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 1, 'new_count': 1, 'count_change': 0, 'old_best_cost': 789.0, 'new_best_cost': 789.0, 'quality_improvement': 0.0, 'old_diversity': 0.0, 'new_diversity': 0.0, 'diversity_change': 0.0}, 'suggestions': ['当前状态良好，继续保持现有策略']}
2025-08-01 10:10:00,547 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-01 10:10:00,598 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_solution.json
2025-08-01 10:10:00,599 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_20250801_101000.solution
2025-08-01 10:10:00,600 - __main__ - INFO - 实例 simple1_9 处理完成
