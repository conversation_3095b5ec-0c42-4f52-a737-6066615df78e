2025-08-03 16:15:27,876 - main - INFO - composite13_66 开始进化第 1 代
2025-08-03 16:15:27,877 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-03 16:15:27,879 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:15:27,900 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=9984.000, 多样性=0.962
2025-08-03 16:15:27,905 - PathExpert - INFO - 开始路径结构分析
2025-08-03 16:15:27,912 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.962
2025-08-03 16:15:27,918 - EliteExpert - INFO - 开始精英解分析
2025-08-03 16:15:27,926 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/1)
2025-08-03 16:15:27,926 - LandscapeExpert - INFO - 使用直接传入的种群数据: 20个个体
2025-08-03 16:15:27,926 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-03 16:15:27,926 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-03 16:15:28,261 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -13082.550, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 1.000
2025-08-03 16:15:28,262 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-03 16:15:28,262 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-03 16:15:28,344 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记
2025-08-03 16:15:28,767 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_1_20250803_161528.html
2025-08-03 16:15:28,837 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_1_20250803_161528.html
2025-08-03 16:15:28,837 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-03 16:15:28,837 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-03 16:15:28,837 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.9112秒
2025-08-03 16:15:28,838 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-03 16:15:28,838 - main - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -13082.55, 'local_optima_density': 0.1, 'gradient_variance': 1834679866.8315003, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0019, 'fitness_entropy': 0.8941881858672537, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -13082.550)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 1, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754208928.2627785, 'performance_metrics': {}}}
2025-08-03 16:15:28,838 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-03 16:15:28,838 - experts.strategy.enhanced_strategy_expert - INFO - 开始增强策略分配分析 (迭代 0)
2025-08-03 16:15:28,838 - experts.analysis.individual_state_analyzer - INFO - 开始分析种群状态 (迭代 0, 种群大小: 20)
2025-08-03 16:15:28,838 - experts.strategy.enhanced_strategy_expert - ERROR - 策略分配过程中发生错误: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:15:28,838 - experts.strategy.enhanced_strategy_expert - WARNING - 使用回退策略分配
2025-08-03 16:15:28,839 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 16:15:28,839 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:15:28,839 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-03 16:15:28,839 - main - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 16:15:28,839 - main - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:15:28,839 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-03 16:15:28,839 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 2, 4, 5} (总数: 4, 保护比例: 0.20)
2025-08-03 16:15:28,839 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-03 16:15:28,839 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-03 16:15:28,839 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:15:28,857 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:15:28,857 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:15:29,107 - ExplorationExpert - INFO - 探索路径生成完成，成本: 59221.0, 路径长度: 66
2025-08-03 16:15:29,107 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [33, 7, 0, 5, 52, 12, 8, 16, 35, 32, 14, 17, 1, 18, 20, 4, 56, 2, 9, 65, 61, 22, 10, 3, 13, 36, 27, 24, 34, 40, 50, 46, 47, 15, 49, 38, 19, 43, 51, 48, 23, 30, 31, 21, 44, 41, 11, 53, 64, 60, 62, 55, 39, 45, 29, 6, 54, 58, 63, 57, 42, 26, 25, 37, 28, 59], 'cur_cost': 59221.0}
2025-08-03 16:15:29,108 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 59221.00)
2025-08-03 16:15:29,108 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-03 16:15:29,108 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:15:29,110 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:15:29,111 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 103285.0
2025-08-03 16:15:31,384 - ExploitationExpert - INFO - res_population_num: 1
2025-08-03 16:15:31,384 - ExploitationExpert - INFO - res_population_costs: [9844.0]
2025-08-03 16:15:31,384 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  6,  2,  8,  5,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64)]
2025-08-03 16:15:31,385 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:15:31,385 - ExploitationExpert - INFO - populations: [{'tour': [33, 7, 0, 5, 52, 12, 8, 16, 35, 32, 14, 17, 1, 18, 20, 4, 56, 2, 9, 65, 61, 22, 10, 3, 13, 36, 27, 24, 34, 40, 50, 46, 47, 15, 49, 38, 19, 43, 51, 48, 23, 30, 31, 21, 44, 41, 11, 53, 64, 60, 62, 55, 39, 45, 29, 6, 54, 58, 63, 57, 42, 26, 25, 37, 28, 59], 'cur_cost': 59221.0}, {'tour': array([62, 58, 50, 35, 18, 55, 43, 15,  9, 14,  1,  6, 25, 26,  4, 21, 52,
       56, 63, 32, 30, 64,  3, 60, 46, 17, 16,  0, 42, 19, 36, 38, 41, 39,
       29, 37, 13, 28, 49,  2, 40, 22, 20, 45,  8,  5, 51, 57, 53, 31, 11,
       54, 47, 61, 34, 10, 59, 23, 44, 27, 65,  7, 33, 12, 24, 48],
      dtype=int64), 'cur_cost': 103285.0}, {'tour': array([13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36,
       35, 28, 30, 34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,
        2,  6, 10,  0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9984.0}, {'tour': array([ 4,  5,  8,  2,  6,  9, 11,  7,  3,  1,  0, 10, 55, 61, 53, 62, 59,
       56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23,
       13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29,
       32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42],
      dtype=int64), 'cur_cost': 10221.0}, {'tour': array([17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36,
       35, 28, 30, 34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,
        2,  6, 10,  0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 10053.0}, {'tour': array([61, 55, 56, 59, 62, 53, 64, 57, 54, 60, 58, 65, 52, 63,  2,  8,  5,
        4,  6,  9, 11,  7,  3,  1,  0, 10, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29,
       32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42],
      dtype=int64), 'cur_cost': 10090.0}, {'tour': array([45, 37, 59, 14,  7, 28, 13, 48, 34, 22, 60, 17, 18, 46, 30, 41, 39,
       21, 32, 53,  3, 56, 11, 65, 29, 58,  2,  9, 64, 23, 19, 47, 10, 36,
       20, 43, 24, 42, 62,  8, 15, 40, 12,  0, 61, 25, 52, 63, 35, 26, 57,
       31,  6, 49, 33,  1, 27, 38, 55, 44, 50,  5, 54,  4, 16, 51],
      dtype=int64), 'cur_cost': 108724.0}, {'tour': array([34, 57, 63, 25, 42, 48, 10, 44,  1, 21,  7,  3,  0, 64,  2, 18, 30,
       12, 62, 33, 26, 14, 19, 61, 27, 40, 46, 50, 37, 52, 59, 45, 36, 11,
       65,  4, 31, 16,  5, 54, 20, 39, 24, 51, 60, 56, 55, 17, 41,  6, 15,
       58, 49, 13, 38, 29, 47, 22,  9, 35,  8, 53, 23, 28, 32, 43],
      dtype=int64), 'cur_cost': 101300.0}, {'tour': array([48, 23, 54, 38, 56, 34, 62, 47,  7, 25, 15, 58, 20, 55, 28, 18, 19,
        8, 30, 40, 37, 10, 52, 41, 33,  4, 14, 59, 11, 65, 32, 39, 60,  1,
       61, 36, 22, 50, 16, 49, 46, 27, 21, 63,  0, 42, 51, 24, 64, 17, 35,
       31, 57, 44, 43,  9,  5, 12,  6, 26, 45,  2,  3, 29, 13, 53],
      dtype=int64), 'cur_cost': 117116.0}, {'tour': array([ 6, 63, 10, 26,  4,  3, 14, 56, 48, 23, 30, 61, 51, 40, 29,  8, 12,
       50, 25, 11, 42,  2, 53, 58, 28, 45, 31, 59, 34, 57, 24,  9, 52,  5,
       39, 37, 49, 44, 65, 32, 17, 54, 33, 38,  1, 18, 36, 19, 64, 41, 22,
        7, 47, 13, 16, 15, 62, 60, 55, 27,  0, 21, 43, 35, 46, 20],
      dtype=int64), 'cur_cost': 115220.0}, {'tour': array([65, 12, 60, 43, 26, 49, 55, 19, 62, 22,  0, 29,  9, 64, 54, 57,  4,
       51,  7, 45, 47,  2, 10, 33, 27, 48, 38, 40, 16, 11, 34, 58, 36, 30,
       46, 28, 14,  8,  5, 25, 53, 56, 13, 23, 63, 41, 32, 42, 61, 20, 39,
       21, 35, 24,  3,  1, 37, 59,  6, 17, 18, 52, 31, 44, 15, 50],
      dtype=int64), 'cur_cost': 105375.0}, {'tour': array([53, 41, 50, 34, 46, 14, 27, 61, 49, 64, 40,  4, 32, 36,  1, 45, 37,
        6, 33, 28, 19, 48, 16, 10, 29, 44, 39, 59, 58, 56,  9,  8, 38, 47,
       11,  0,  7, 22, 52, 15, 63, 60, 24, 18, 26, 54, 42, 65,  5, 43, 31,
       21, 62, 12,  3, 57, 35, 23,  2, 25, 13, 51, 30, 20, 55, 17],
      dtype=int64), 'cur_cost': 108142.0}, {'tour': array([31, 18,  6, 42, 45, 11, 63, 59, 20, 46, 49, 32, 41, 21, 29, 43, 15,
       17, 56, 47, 24,  0, 34, 57, 36,  8, 10, 60,  3, 30, 28,  2, 35, 23,
       50, 55, 38, 52, 25, 14, 22,  7, 19, 39, 62, 54,  1, 37,  5,  4, 48,
       51, 40, 44, 53, 26, 65, 58, 27, 12, 61,  9, 64, 16, 33, 13],
      dtype=int64), 'cur_cost': 101671.0}, {'tour': array([35, 52, 54, 32, 56, 50, 16, 44, 48, 63, 37, 57, 21,  6, 17, 25, 22,
       13,  9, 61, 46, 55, 26, 42,  5, 51, 62,  2, 64, 36, 18, 29,  0, 10,
       43, 65, 39,  8, 14, 28, 60, 12, 15, 47, 11, 53, 59, 31, 27,  4, 40,
       34,  1, 58,  3, 20,  7, 38, 23, 49, 41, 19, 33, 45, 30, 24],
      dtype=int64), 'cur_cost': 112079.0}, {'tour': array([60, 43, 39, 21,  8,  4, 55, 47, 14, 29, 16,  0,  6, 22, 27, 11, 59,
       28, 61, 10, 49, 17,  7, 36, 20, 44, 37, 54, 25, 41, 56, 62, 57, 63,
       23, 40,  9, 50, 38, 52, 33, 64, 34, 26, 65,  1, 31, 35, 53, 51, 48,
       24,  3, 19, 15, 46, 13, 18, 30, 58, 42, 32,  2, 12,  5, 45],
      dtype=int64), 'cur_cost': 108780.0}, {'tour': array([45,  3,  7, 34, 41, 20,  4, 65, 17, 32, 47, 43, 15,  9, 26, 10, 52,
       40, 53, 28, 37, 39, 49, 12, 38, 21, 63, 18, 36,  5, 59, 23,  0, 29,
       62, 44, 35, 48, 60, 16, 61, 58, 55, 24,  8, 54, 46, 42, 31, 30, 25,
       50,  2, 22, 33,  6, 13, 57, 19, 14, 56, 51,  1, 11, 64, 27],
      dtype=int64), 'cur_cost': 107289.0}, {'tour': array([47, 25, 30, 59, 38, 57, 48, 64,  2, 31, 54,  1, 33, 45,  8, 53, 46,
        6, 37, 42, 11,  7,  4,  9, 58, 13, 14, 65, 63, 20, 51, 19,  3,  0,
       29, 23, 34, 26, 61,  5, 12, 27, 56, 36, 10, 52, 43, 49, 62, 55, 24,
       35, 32, 16, 41, 18, 17, 22, 50, 39, 44, 28, 21, 40, 15, 60],
      dtype=int64), 'cur_cost': 99080.0}, {'tour': array([30, 19, 29,  0, 24, 35,  2, 62, 34,  9, 55, 65, 63, 42, 23, 39, 53,
       60, 49, 56, 46, 51, 54, 32, 15, 26,  3, 57,  6, 31, 20, 45, 33, 16,
       64, 18, 50, 40, 21, 28, 22,  1, 41,  7, 59, 47,  8, 52, 48, 61, 44,
       14, 13, 37,  5, 43, 38, 10, 58, 11, 27, 36, 12,  4, 17, 25],
      dtype=int64), 'cur_cost': 102849.0}, {'tour': array([21, 41, 15,  1, 52, 65, 61, 54, 50, 57,  8, 30, 33, 13, 28, 34, 58,
       22, 37, 17,  5, 43, 38,  7, 56, 23, 31,  3, 24, 45,  0, 18, 29, 26,
       11, 20, 51, 40, 53, 49, 12, 39, 60, 48, 63,  6,  9, 16, 35, 32, 42,
       47, 25, 10, 27, 55, 19, 59, 62,  4,  2, 64, 44, 46, 36, 14],
      dtype=int64), 'cur_cost': 93881.0}, {'tour': array([ 9, 59, 64, 44, 38, 15,  5, 34,  3, 26, 28,  6, 27, 25, 20, 10, 16,
       22, 61, 45, 43, 17, 19, 65, 39,  4, 62, 21,  0, 54, 41, 63, 58, 37,
       60,  8,  1, 30, 42, 46, 23, 57, 48, 11, 13, 24, 35, 18, 53, 33, 31,
       29, 47, 40, 55, 14, 56, 49, 32, 51,  7,  2, 50, 52, 12, 36],
      dtype=int64), 'cur_cost': 102617.0}]
2025-08-03 16:15:31,393 - ExploitationExpert - INFO - 局部搜索耗时: 2.28秒
2025-08-03 16:15:31,393 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-03 16:15:31,394 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([62, 58, 50, 35, 18, 55, 43, 15,  9, 14,  1,  6, 25, 26,  4, 21, 52,
       56, 63, 32, 30, 64,  3, 60, 46, 17, 16,  0, 42, 19, 36, 38, 41, 39,
       29, 37, 13, 28, 49,  2, 40, 22, 20, 45,  8,  5, 51, 57, 53, 31, 11,
       54, 47, 61, 34, 10, 59, 23, 44, 27, 65,  7, 33, 12, 24, 48],
      dtype=int64), 'cur_cost': 103285.0}
2025-08-03 16:15:31,394 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 103285.00)
2025-08-03 16:15:31,394 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-03 16:15:31,394 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-03 16:15:31,394 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:15:31,399 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:15:31,399 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:15:31,400 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12337.0, 路径长度: 66
2025-08-03 16:15:31,400 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 17, 18, 22, 12, 23, 16, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12337.0}
2025-08-03 16:15:31,400 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 12337.00)
2025-08-03 16:15:31,400 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-03 16:15:31,400 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-03 16:15:31,400 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:15:31,417 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:15:31,418 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:15:31,418 - ExplorationExpert - INFO - 探索路径生成完成，成本: 52424.0, 路径长度: 66
2025-08-03 16:15:31,419 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [10, 11, 12, 31, 21, 36, 6, 8, 5, 58, 0, 62, 63, 56, 65, 54, 53, 13, 14, 4, 22, 9, 7, 61, 52, 49, 15, 17, 27, 33, 35, 34, 28, 2, 18, 29, 37, 20, 24, 3, 19, 40, 41, 38, 46, 51, 16, 26, 30, 32, 43, 48, 39, 47, 44, 42, 1, 60, 57, 59, 23, 25, 45, 50, 55, 64], 'cur_cost': 52424.0}
2025-08-03 16:15:31,419 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 52424.00)
2025-08-03 16:15:31,419 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-03 16:15:31,420 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:15:31,420 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:15:31,420 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 105797.0
2025-08-03 16:15:33,875 - ExploitationExpert - INFO - res_population_num: 2
2025-08-03 16:15:33,875 - ExploitationExpert - INFO - res_population_costs: [9844.0, 9582.0]
2025-08-03 16:15:33,875 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  6,  2,  8,  5,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 19, 16, 23, 18, 27,
       37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:15:33,876 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:15:33,876 - ExploitationExpert - INFO - populations: [{'tour': [33, 7, 0, 5, 52, 12, 8, 16, 35, 32, 14, 17, 1, 18, 20, 4, 56, 2, 9, 65, 61, 22, 10, 3, 13, 36, 27, 24, 34, 40, 50, 46, 47, 15, 49, 38, 19, 43, 51, 48, 23, 30, 31, 21, 44, 41, 11, 53, 64, 60, 62, 55, 39, 45, 29, 6, 54, 58, 63, 57, 42, 26, 25, 37, 28, 59], 'cur_cost': 59221.0}, {'tour': array([62, 58, 50, 35, 18, 55, 43, 15,  9, 14,  1,  6, 25, 26,  4, 21, 52,
       56, 63, 32, 30, 64,  3, 60, 46, 17, 16,  0, 42, 19, 36, 38, 41, 39,
       29, 37, 13, 28, 49,  2, 40, 22, 20, 45,  8,  5, 51, 57, 53, 31, 11,
       54, 47, 61, 34, 10, 59, 23, 44, 27, 65,  7, 33, 12, 24, 48],
      dtype=int64), 'cur_cost': 103285.0}, {'tour': [0, 17, 18, 22, 12, 23, 16, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12337.0}, {'tour': [10, 11, 12, 31, 21, 36, 6, 8, 5, 58, 0, 62, 63, 56, 65, 54, 53, 13, 14, 4, 22, 9, 7, 61, 52, 49, 15, 17, 27, 33, 35, 34, 28, 2, 18, 29, 37, 20, 24, 3, 19, 40, 41, 38, 46, 51, 16, 26, 30, 32, 43, 48, 39, 47, 44, 42, 1, 60, 57, 59, 23, 25, 45, 50, 55, 64], 'cur_cost': 52424.0}, {'tour': array([10, 60, 62, 16,  1, 45, 11,  0, 17, 21, 32, 14, 18, 63, 34, 57, 64,
        4, 38, 31, 65, 58,  5, 24, 53, 19, 22, 61, 37,  2,  3, 36, 20, 40,
       39, 43,  7, 48, 29, 47, 52, 51, 50, 15, 44, 28,  6, 30, 12,  8, 54,
       26, 42,  9, 13, 33, 59, 55, 23, 25, 46, 35, 27, 56, 49, 41],
      dtype=int64), 'cur_cost': 105797.0}, {'tour': array([61, 55, 56, 59, 62, 53, 64, 57, 54, 60, 58, 65, 52, 63,  2,  8,  5,
        4,  6,  9, 11,  7,  3,  1,  0, 10, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29,
       32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42],
      dtype=int64), 'cur_cost': 10090.0}, {'tour': array([45, 37, 59, 14,  7, 28, 13, 48, 34, 22, 60, 17, 18, 46, 30, 41, 39,
       21, 32, 53,  3, 56, 11, 65, 29, 58,  2,  9, 64, 23, 19, 47, 10, 36,
       20, 43, 24, 42, 62,  8, 15, 40, 12,  0, 61, 25, 52, 63, 35, 26, 57,
       31,  6, 49, 33,  1, 27, 38, 55, 44, 50,  5, 54,  4, 16, 51],
      dtype=int64), 'cur_cost': 108724.0}, {'tour': array([34, 57, 63, 25, 42, 48, 10, 44,  1, 21,  7,  3,  0, 64,  2, 18, 30,
       12, 62, 33, 26, 14, 19, 61, 27, 40, 46, 50, 37, 52, 59, 45, 36, 11,
       65,  4, 31, 16,  5, 54, 20, 39, 24, 51, 60, 56, 55, 17, 41,  6, 15,
       58, 49, 13, 38, 29, 47, 22,  9, 35,  8, 53, 23, 28, 32, 43],
      dtype=int64), 'cur_cost': 101300.0}, {'tour': array([48, 23, 54, 38, 56, 34, 62, 47,  7, 25, 15, 58, 20, 55, 28, 18, 19,
        8, 30, 40, 37, 10, 52, 41, 33,  4, 14, 59, 11, 65, 32, 39, 60,  1,
       61, 36, 22, 50, 16, 49, 46, 27, 21, 63,  0, 42, 51, 24, 64, 17, 35,
       31, 57, 44, 43,  9,  5, 12,  6, 26, 45,  2,  3, 29, 13, 53],
      dtype=int64), 'cur_cost': 117116.0}, {'tour': array([ 6, 63, 10, 26,  4,  3, 14, 56, 48, 23, 30, 61, 51, 40, 29,  8, 12,
       50, 25, 11, 42,  2, 53, 58, 28, 45, 31, 59, 34, 57, 24,  9, 52,  5,
       39, 37, 49, 44, 65, 32, 17, 54, 33, 38,  1, 18, 36, 19, 64, 41, 22,
        7, 47, 13, 16, 15, 62, 60, 55, 27,  0, 21, 43, 35, 46, 20],
      dtype=int64), 'cur_cost': 115220.0}, {'tour': array([65, 12, 60, 43, 26, 49, 55, 19, 62, 22,  0, 29,  9, 64, 54, 57,  4,
       51,  7, 45, 47,  2, 10, 33, 27, 48, 38, 40, 16, 11, 34, 58, 36, 30,
       46, 28, 14,  8,  5, 25, 53, 56, 13, 23, 63, 41, 32, 42, 61, 20, 39,
       21, 35, 24,  3,  1, 37, 59,  6, 17, 18, 52, 31, 44, 15, 50],
      dtype=int64), 'cur_cost': 105375.0}, {'tour': array([53, 41, 50, 34, 46, 14, 27, 61, 49, 64, 40,  4, 32, 36,  1, 45, 37,
        6, 33, 28, 19, 48, 16, 10, 29, 44, 39, 59, 58, 56,  9,  8, 38, 47,
       11,  0,  7, 22, 52, 15, 63, 60, 24, 18, 26, 54, 42, 65,  5, 43, 31,
       21, 62, 12,  3, 57, 35, 23,  2, 25, 13, 51, 30, 20, 55, 17],
      dtype=int64), 'cur_cost': 108142.0}, {'tour': array([31, 18,  6, 42, 45, 11, 63, 59, 20, 46, 49, 32, 41, 21, 29, 43, 15,
       17, 56, 47, 24,  0, 34, 57, 36,  8, 10, 60,  3, 30, 28,  2, 35, 23,
       50, 55, 38, 52, 25, 14, 22,  7, 19, 39, 62, 54,  1, 37,  5,  4, 48,
       51, 40, 44, 53, 26, 65, 58, 27, 12, 61,  9, 64, 16, 33, 13],
      dtype=int64), 'cur_cost': 101671.0}, {'tour': array([35, 52, 54, 32, 56, 50, 16, 44, 48, 63, 37, 57, 21,  6, 17, 25, 22,
       13,  9, 61, 46, 55, 26, 42,  5, 51, 62,  2, 64, 36, 18, 29,  0, 10,
       43, 65, 39,  8, 14, 28, 60, 12, 15, 47, 11, 53, 59, 31, 27,  4, 40,
       34,  1, 58,  3, 20,  7, 38, 23, 49, 41, 19, 33, 45, 30, 24],
      dtype=int64), 'cur_cost': 112079.0}, {'tour': array([60, 43, 39, 21,  8,  4, 55, 47, 14, 29, 16,  0,  6, 22, 27, 11, 59,
       28, 61, 10, 49, 17,  7, 36, 20, 44, 37, 54, 25, 41, 56, 62, 57, 63,
       23, 40,  9, 50, 38, 52, 33, 64, 34, 26, 65,  1, 31, 35, 53, 51, 48,
       24,  3, 19, 15, 46, 13, 18, 30, 58, 42, 32,  2, 12,  5, 45],
      dtype=int64), 'cur_cost': 108780.0}, {'tour': array([45,  3,  7, 34, 41, 20,  4, 65, 17, 32, 47, 43, 15,  9, 26, 10, 52,
       40, 53, 28, 37, 39, 49, 12, 38, 21, 63, 18, 36,  5, 59, 23,  0, 29,
       62, 44, 35, 48, 60, 16, 61, 58, 55, 24,  8, 54, 46, 42, 31, 30, 25,
       50,  2, 22, 33,  6, 13, 57, 19, 14, 56, 51,  1, 11, 64, 27],
      dtype=int64), 'cur_cost': 107289.0}, {'tour': array([47, 25, 30, 59, 38, 57, 48, 64,  2, 31, 54,  1, 33, 45,  8, 53, 46,
        6, 37, 42, 11,  7,  4,  9, 58, 13, 14, 65, 63, 20, 51, 19,  3,  0,
       29, 23, 34, 26, 61,  5, 12, 27, 56, 36, 10, 52, 43, 49, 62, 55, 24,
       35, 32, 16, 41, 18, 17, 22, 50, 39, 44, 28, 21, 40, 15, 60],
      dtype=int64), 'cur_cost': 99080.0}, {'tour': array([30, 19, 29,  0, 24, 35,  2, 62, 34,  9, 55, 65, 63, 42, 23, 39, 53,
       60, 49, 56, 46, 51, 54, 32, 15, 26,  3, 57,  6, 31, 20, 45, 33, 16,
       64, 18, 50, 40, 21, 28, 22,  1, 41,  7, 59, 47,  8, 52, 48, 61, 44,
       14, 13, 37,  5, 43, 38, 10, 58, 11, 27, 36, 12,  4, 17, 25],
      dtype=int64), 'cur_cost': 102849.0}, {'tour': array([21, 41, 15,  1, 52, 65, 61, 54, 50, 57,  8, 30, 33, 13, 28, 34, 58,
       22, 37, 17,  5, 43, 38,  7, 56, 23, 31,  3, 24, 45,  0, 18, 29, 26,
       11, 20, 51, 40, 53, 49, 12, 39, 60, 48, 63,  6,  9, 16, 35, 32, 42,
       47, 25, 10, 27, 55, 19, 59, 62,  4,  2, 64, 44, 46, 36, 14],
      dtype=int64), 'cur_cost': 93881.0}, {'tour': array([ 9, 59, 64, 44, 38, 15,  5, 34,  3, 26, 28,  6, 27, 25, 20, 10, 16,
       22, 61, 45, 43, 17, 19, 65, 39,  4, 62, 21,  0, 54, 41, 63, 58, 37,
       60,  8,  1, 30, 42, 46, 23, 57, 48, 11, 13, 24, 35, 18, 53, 33, 31,
       29, 47, 40, 55, 14, 56, 49, 32, 51,  7,  2, 50, 52, 12, 36],
      dtype=int64), 'cur_cost': 102617.0}]
2025-08-03 16:15:33,885 - ExploitationExpert - INFO - 局部搜索耗时: 2.46秒
2025-08-03 16:15:33,885 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-03 16:15:33,885 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([10, 60, 62, 16,  1, 45, 11,  0, 17, 21, 32, 14, 18, 63, 34, 57, 64,
        4, 38, 31, 65, 58,  5, 24, 53, 19, 22, 61, 37,  2,  3, 36, 20, 40,
       39, 43,  7, 48, 29, 47, 52, 51, 50, 15, 44, 28,  6, 30, 12,  8, 54,
       26, 42,  9, 13, 33, 59, 55, 23, 25, 46, 35, 27, 56, 49, 41],
      dtype=int64), 'cur_cost': 105797.0}
2025-08-03 16:15:33,886 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 105797.00)
2025-08-03 16:15:33,886 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-03 16:15:33,886 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-03 16:15:33,886 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:15:33,901 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:15:33,901 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:15:33,901 - ExplorationExpert - INFO - 探索路径生成完成，成本: 67283.0, 路径长度: 66
2025-08-03 16:15:33,901 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [13, 9, 1, 11, 5, 61, 10, 53, 12, 17, 37, 15, 18, 3, 58, 23, 4, 19, 27, 32, 2, 60, 57, 8, 22, 7, 21, 16, 28, 30, 20, 36, 6, 52, 44, 14, 43, 47, 35, 42, 48, 25, 0, 64, 63, 59, 49, 39, 51, 40, 26, 29, 46, 33, 31, 50, 45, 38, 56, 55, 62, 54, 65, 41, 34, 24], 'cur_cost': 67283.0}
2025-08-03 16:15:33,901 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 67283.00)
2025-08-03 16:15:33,902 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-03 16:15:33,902 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-03 16:15:33,902 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:15:33,906 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:15:33,906 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:15:33,906 - ExplorationExpert - INFO - 探索路径生成完成，成本: 102854.0, 路径长度: 66
2025-08-03 16:15:33,906 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [33, 17, 18, 22, 12, 36, 16, 5, 14, 1, 27, 65, 54, 2, 9, 24, 29, 21, 7, 40, 50, 4, 8, 10, 48, 19, 62, 59, 56, 46, 51, 53, 30, 55, 45, 58, 63, 25, 39, 11, 43, 23, 31, 13, 37, 44, 61, 26, 28, 47, 34, 0, 3, 41, 64, 35, 42, 32, 60, 49, 20, 6, 57, 52, 15, 38], 'cur_cost': 102854.0}
2025-08-03 16:15:33,906 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 102854.00)
2025-08-03 16:15:33,907 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-03 16:15:33,907 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:15:33,907 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:15:33,907 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 101620.0
2025-08-03 16:15:34,467 - ExploitationExpert - INFO - res_population_num: 4
2025-08-03 16:15:34,467 - ExploitationExpert - INFO - res_population_costs: [9844.0, 9582.0, 9551.0, 9546]
2025-08-03 16:15:34,467 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  6,  2,  8,  5,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 19, 16, 23, 18, 27,
       37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64)]
2025-08-03 16:15:34,469 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:15:34,469 - ExploitationExpert - INFO - populations: [{'tour': [33, 7, 0, 5, 52, 12, 8, 16, 35, 32, 14, 17, 1, 18, 20, 4, 56, 2, 9, 65, 61, 22, 10, 3, 13, 36, 27, 24, 34, 40, 50, 46, 47, 15, 49, 38, 19, 43, 51, 48, 23, 30, 31, 21, 44, 41, 11, 53, 64, 60, 62, 55, 39, 45, 29, 6, 54, 58, 63, 57, 42, 26, 25, 37, 28, 59], 'cur_cost': 59221.0}, {'tour': array([62, 58, 50, 35, 18, 55, 43, 15,  9, 14,  1,  6, 25, 26,  4, 21, 52,
       56, 63, 32, 30, 64,  3, 60, 46, 17, 16,  0, 42, 19, 36, 38, 41, 39,
       29, 37, 13, 28, 49,  2, 40, 22, 20, 45,  8,  5, 51, 57, 53, 31, 11,
       54, 47, 61, 34, 10, 59, 23, 44, 27, 65,  7, 33, 12, 24, 48],
      dtype=int64), 'cur_cost': 103285.0}, {'tour': [0, 17, 18, 22, 12, 23, 16, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12337.0}, {'tour': [10, 11, 12, 31, 21, 36, 6, 8, 5, 58, 0, 62, 63, 56, 65, 54, 53, 13, 14, 4, 22, 9, 7, 61, 52, 49, 15, 17, 27, 33, 35, 34, 28, 2, 18, 29, 37, 20, 24, 3, 19, 40, 41, 38, 46, 51, 16, 26, 30, 32, 43, 48, 39, 47, 44, 42, 1, 60, 57, 59, 23, 25, 45, 50, 55, 64], 'cur_cost': 52424.0}, {'tour': array([10, 60, 62, 16,  1, 45, 11,  0, 17, 21, 32, 14, 18, 63, 34, 57, 64,
        4, 38, 31, 65, 58,  5, 24, 53, 19, 22, 61, 37,  2,  3, 36, 20, 40,
       39, 43,  7, 48, 29, 47, 52, 51, 50, 15, 44, 28,  6, 30, 12,  8, 54,
       26, 42,  9, 13, 33, 59, 55, 23, 25, 46, 35, 27, 56, 49, 41],
      dtype=int64), 'cur_cost': 105797.0}, {'tour': [13, 9, 1, 11, 5, 61, 10, 53, 12, 17, 37, 15, 18, 3, 58, 23, 4, 19, 27, 32, 2, 60, 57, 8, 22, 7, 21, 16, 28, 30, 20, 36, 6, 52, 44, 14, 43, 47, 35, 42, 48, 25, 0, 64, 63, 59, 49, 39, 51, 40, 26, 29, 46, 33, 31, 50, 45, 38, 56, 55, 62, 54, 65, 41, 34, 24], 'cur_cost': 67283.0}, {'tour': [33, 17, 18, 22, 12, 36, 16, 5, 14, 1, 27, 65, 54, 2, 9, 24, 29, 21, 7, 40, 50, 4, 8, 10, 48, 19, 62, 59, 56, 46, 51, 53, 30, 55, 45, 58, 63, 25, 39, 11, 43, 23, 31, 13, 37, 44, 61, 26, 28, 47, 34, 0, 3, 41, 64, 35, 42, 32, 60, 49, 20, 6, 57, 52, 15, 38], 'cur_cost': 102854.0}, {'tour': array([54, 42, 50, 64,  8, 14, 20, 29,  3, 18, 39, 34, 55, 43, 47, 30, 22,
       27,  0, 26, 60, 25, 65, 17, 46, 51, 62,  2,  9, 10, 61, 52, 12, 11,
       24,  6, 35, 57, 59, 58, 13, 32,  1, 40, 19, 41, 44, 23, 48, 49, 15,
       38, 63, 33, 36, 21, 37, 45, 28,  5, 56,  4, 31, 53,  7, 16],
      dtype=int64), 'cur_cost': 101620.0}, {'tour': array([48, 23, 54, 38, 56, 34, 62, 47,  7, 25, 15, 58, 20, 55, 28, 18, 19,
        8, 30, 40, 37, 10, 52, 41, 33,  4, 14, 59, 11, 65, 32, 39, 60,  1,
       61, 36, 22, 50, 16, 49, 46, 27, 21, 63,  0, 42, 51, 24, 64, 17, 35,
       31, 57, 44, 43,  9,  5, 12,  6, 26, 45,  2,  3, 29, 13, 53],
      dtype=int64), 'cur_cost': 117116.0}, {'tour': array([ 6, 63, 10, 26,  4,  3, 14, 56, 48, 23, 30, 61, 51, 40, 29,  8, 12,
       50, 25, 11, 42,  2, 53, 58, 28, 45, 31, 59, 34, 57, 24,  9, 52,  5,
       39, 37, 49, 44, 65, 32, 17, 54, 33, 38,  1, 18, 36, 19, 64, 41, 22,
        7, 47, 13, 16, 15, 62, 60, 55, 27,  0, 21, 43, 35, 46, 20],
      dtype=int64), 'cur_cost': 115220.0}, {'tour': array([65, 12, 60, 43, 26, 49, 55, 19, 62, 22,  0, 29,  9, 64, 54, 57,  4,
       51,  7, 45, 47,  2, 10, 33, 27, 48, 38, 40, 16, 11, 34, 58, 36, 30,
       46, 28, 14,  8,  5, 25, 53, 56, 13, 23, 63, 41, 32, 42, 61, 20, 39,
       21, 35, 24,  3,  1, 37, 59,  6, 17, 18, 52, 31, 44, 15, 50],
      dtype=int64), 'cur_cost': 105375.0}, {'tour': array([53, 41, 50, 34, 46, 14, 27, 61, 49, 64, 40,  4, 32, 36,  1, 45, 37,
        6, 33, 28, 19, 48, 16, 10, 29, 44, 39, 59, 58, 56,  9,  8, 38, 47,
       11,  0,  7, 22, 52, 15, 63, 60, 24, 18, 26, 54, 42, 65,  5, 43, 31,
       21, 62, 12,  3, 57, 35, 23,  2, 25, 13, 51, 30, 20, 55, 17],
      dtype=int64), 'cur_cost': 108142.0}, {'tour': array([31, 18,  6, 42, 45, 11, 63, 59, 20, 46, 49, 32, 41, 21, 29, 43, 15,
       17, 56, 47, 24,  0, 34, 57, 36,  8, 10, 60,  3, 30, 28,  2, 35, 23,
       50, 55, 38, 52, 25, 14, 22,  7, 19, 39, 62, 54,  1, 37,  5,  4, 48,
       51, 40, 44, 53, 26, 65, 58, 27, 12, 61,  9, 64, 16, 33, 13],
      dtype=int64), 'cur_cost': 101671.0}, {'tour': array([35, 52, 54, 32, 56, 50, 16, 44, 48, 63, 37, 57, 21,  6, 17, 25, 22,
       13,  9, 61, 46, 55, 26, 42,  5, 51, 62,  2, 64, 36, 18, 29,  0, 10,
       43, 65, 39,  8, 14, 28, 60, 12, 15, 47, 11, 53, 59, 31, 27,  4, 40,
       34,  1, 58,  3, 20,  7, 38, 23, 49, 41, 19, 33, 45, 30, 24],
      dtype=int64), 'cur_cost': 112079.0}, {'tour': array([60, 43, 39, 21,  8,  4, 55, 47, 14, 29, 16,  0,  6, 22, 27, 11, 59,
       28, 61, 10, 49, 17,  7, 36, 20, 44, 37, 54, 25, 41, 56, 62, 57, 63,
       23, 40,  9, 50, 38, 52, 33, 64, 34, 26, 65,  1, 31, 35, 53, 51, 48,
       24,  3, 19, 15, 46, 13, 18, 30, 58, 42, 32,  2, 12,  5, 45],
      dtype=int64), 'cur_cost': 108780.0}, {'tour': array([45,  3,  7, 34, 41, 20,  4, 65, 17, 32, 47, 43, 15,  9, 26, 10, 52,
       40, 53, 28, 37, 39, 49, 12, 38, 21, 63, 18, 36,  5, 59, 23,  0, 29,
       62, 44, 35, 48, 60, 16, 61, 58, 55, 24,  8, 54, 46, 42, 31, 30, 25,
       50,  2, 22, 33,  6, 13, 57, 19, 14, 56, 51,  1, 11, 64, 27],
      dtype=int64), 'cur_cost': 107289.0}, {'tour': array([47, 25, 30, 59, 38, 57, 48, 64,  2, 31, 54,  1, 33, 45,  8, 53, 46,
        6, 37, 42, 11,  7,  4,  9, 58, 13, 14, 65, 63, 20, 51, 19,  3,  0,
       29, 23, 34, 26, 61,  5, 12, 27, 56, 36, 10, 52, 43, 49, 62, 55, 24,
       35, 32, 16, 41, 18, 17, 22, 50, 39, 44, 28, 21, 40, 15, 60],
      dtype=int64), 'cur_cost': 99080.0}, {'tour': array([30, 19, 29,  0, 24, 35,  2, 62, 34,  9, 55, 65, 63, 42, 23, 39, 53,
       60, 49, 56, 46, 51, 54, 32, 15, 26,  3, 57,  6, 31, 20, 45, 33, 16,
       64, 18, 50, 40, 21, 28, 22,  1, 41,  7, 59, 47,  8, 52, 48, 61, 44,
       14, 13, 37,  5, 43, 38, 10, 58, 11, 27, 36, 12,  4, 17, 25],
      dtype=int64), 'cur_cost': 102849.0}, {'tour': array([21, 41, 15,  1, 52, 65, 61, 54, 50, 57,  8, 30, 33, 13, 28, 34, 58,
       22, 37, 17,  5, 43, 38,  7, 56, 23, 31,  3, 24, 45,  0, 18, 29, 26,
       11, 20, 51, 40, 53, 49, 12, 39, 60, 48, 63,  6,  9, 16, 35, 32, 42,
       47, 25, 10, 27, 55, 19, 59, 62,  4,  2, 64, 44, 46, 36, 14],
      dtype=int64), 'cur_cost': 93881.0}, {'tour': array([ 9, 59, 64, 44, 38, 15,  5, 34,  3, 26, 28,  6, 27, 25, 20, 10, 16,
       22, 61, 45, 43, 17, 19, 65, 39,  4, 62, 21,  0, 54, 41, 63, 58, 37,
       60,  8,  1, 30, 42, 46, 23, 57, 48, 11, 13, 24, 35, 18, 53, 33, 31,
       29, 47, 40, 55, 14, 56, 49, 32, 51,  7,  2, 50, 52, 12, 36],
      dtype=int64), 'cur_cost': 102617.0}]
2025-08-03 16:15:34,474 - ExploitationExpert - INFO - 局部搜索耗时: 0.57秒
2025-08-03 16:15:34,474 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-03 16:15:34,475 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([54, 42, 50, 64,  8, 14, 20, 29,  3, 18, 39, 34, 55, 43, 47, 30, 22,
       27,  0, 26, 60, 25, 65, 17, 46, 51, 62,  2,  9, 10, 61, 52, 12, 11,
       24,  6, 35, 57, 59, 58, 13, 32,  1, 40, 19, 41, 44, 23, 48, 49, 15,
       38, 63, 33, 36, 21, 37, 45, 28,  5, 56,  4, 31, 53,  7, 16],
      dtype=int64), 'cur_cost': 101620.0}
2025-08-03 16:15:34,475 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 101620.00)
2025-08-03 16:15:34,475 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-03 16:15:34,475 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-03 16:15:34,475 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:15:34,479 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:15:34,479 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:15:34,481 - ExplorationExpert - INFO - 探索路径生成完成，成本: 108012.0, 路径长度: 66
2025-08-03 16:15:34,481 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 23, 53, 5, 15, 54, 24, 19, 22, 61, 52, 36, 7, 1, 11, 2, 8, 58, 37, 55, 48, 59, 46, 44, 49, 47, 62, 29, 50, 57, 20, 38, 31, 34, 10, 13, 65, 51, 32, 17, 25, 40, 33, 56, 12, 26, 63, 6, 60, 30, 42, 27, 3, 45, 64, 4, 16, 14, 35, 39, 41, 9, 28, 18, 21, 43], 'cur_cost': 108012.0}
2025-08-03 16:15:34,481 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 108012.00)
2025-08-03 16:15:34,482 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-03 16:15:34,482 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-03 16:15:34,482 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:15:34,489 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:15:34,489 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:15:34,489 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14673.0, 路径长度: 66
2025-08-03 16:15:34,489 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 12, 25, 13, 20, 21, 19, 16, 18, 23, 22, 15, 14, 17, 27, 37, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14673.0}
2025-08-03 16:15:34,490 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 14673.00)
2025-08-03 16:15:34,490 - experts.management.collaboration_manager - INFO - 为个体 10 生成利用路径
2025-08-03 16:15:34,490 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:15:34,490 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:15:34,490 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 10 处的路径，新成本: 101755.0
2025-08-03 16:15:34,578 - ExploitationExpert - INFO - res_population_num: 7
2025-08-03 16:15:34,578 - ExploitationExpert - INFO - res_population_costs: [9844.0, 9582.0, 9551.0, 9546, 9538.0, 9537, 9526]
2025-08-03 16:15:34,579 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  6,  2,  8,  5,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 19, 16, 23, 18, 27,
       37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38,
       45, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       18, 16, 19, 21, 20, 13, 23, 22, 12, 17, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 49,
       40, 43, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 18, 17, 12, 22, 23, 16, 19,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-08-03 16:15:34,585 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:15:34,586 - ExploitationExpert - INFO - populations: [{'tour': [33, 7, 0, 5, 52, 12, 8, 16, 35, 32, 14, 17, 1, 18, 20, 4, 56, 2, 9, 65, 61, 22, 10, 3, 13, 36, 27, 24, 34, 40, 50, 46, 47, 15, 49, 38, 19, 43, 51, 48, 23, 30, 31, 21, 44, 41, 11, 53, 64, 60, 62, 55, 39, 45, 29, 6, 54, 58, 63, 57, 42, 26, 25, 37, 28, 59], 'cur_cost': 59221.0}, {'tour': array([62, 58, 50, 35, 18, 55, 43, 15,  9, 14,  1,  6, 25, 26,  4, 21, 52,
       56, 63, 32, 30, 64,  3, 60, 46, 17, 16,  0, 42, 19, 36, 38, 41, 39,
       29, 37, 13, 28, 49,  2, 40, 22, 20, 45,  8,  5, 51, 57, 53, 31, 11,
       54, 47, 61, 34, 10, 59, 23, 44, 27, 65,  7, 33, 12, 24, 48],
      dtype=int64), 'cur_cost': 103285.0}, {'tour': [0, 17, 18, 22, 12, 23, 16, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12337.0}, {'tour': [10, 11, 12, 31, 21, 36, 6, 8, 5, 58, 0, 62, 63, 56, 65, 54, 53, 13, 14, 4, 22, 9, 7, 61, 52, 49, 15, 17, 27, 33, 35, 34, 28, 2, 18, 29, 37, 20, 24, 3, 19, 40, 41, 38, 46, 51, 16, 26, 30, 32, 43, 48, 39, 47, 44, 42, 1, 60, 57, 59, 23, 25, 45, 50, 55, 64], 'cur_cost': 52424.0}, {'tour': array([10, 60, 62, 16,  1, 45, 11,  0, 17, 21, 32, 14, 18, 63, 34, 57, 64,
        4, 38, 31, 65, 58,  5, 24, 53, 19, 22, 61, 37,  2,  3, 36, 20, 40,
       39, 43,  7, 48, 29, 47, 52, 51, 50, 15, 44, 28,  6, 30, 12,  8, 54,
       26, 42,  9, 13, 33, 59, 55, 23, 25, 46, 35, 27, 56, 49, 41],
      dtype=int64), 'cur_cost': 105797.0}, {'tour': [13, 9, 1, 11, 5, 61, 10, 53, 12, 17, 37, 15, 18, 3, 58, 23, 4, 19, 27, 32, 2, 60, 57, 8, 22, 7, 21, 16, 28, 30, 20, 36, 6, 52, 44, 14, 43, 47, 35, 42, 48, 25, 0, 64, 63, 59, 49, 39, 51, 40, 26, 29, 46, 33, 31, 50, 45, 38, 56, 55, 62, 54, 65, 41, 34, 24], 'cur_cost': 67283.0}, {'tour': [33, 17, 18, 22, 12, 36, 16, 5, 14, 1, 27, 65, 54, 2, 9, 24, 29, 21, 7, 40, 50, 4, 8, 10, 48, 19, 62, 59, 56, 46, 51, 53, 30, 55, 45, 58, 63, 25, 39, 11, 43, 23, 31, 13, 37, 44, 61, 26, 28, 47, 34, 0, 3, 41, 64, 35, 42, 32, 60, 49, 20, 6, 57, 52, 15, 38], 'cur_cost': 102854.0}, {'tour': array([54, 42, 50, 64,  8, 14, 20, 29,  3, 18, 39, 34, 55, 43, 47, 30, 22,
       27,  0, 26, 60, 25, 65, 17, 46, 51, 62,  2,  9, 10, 61, 52, 12, 11,
       24,  6, 35, 57, 59, 58, 13, 32,  1, 40, 19, 41, 44, 23, 48, 49, 15,
       38, 63, 33, 36, 21, 37, 45, 28,  5, 56,  4, 31, 53,  7, 16],
      dtype=int64), 'cur_cost': 101620.0}, {'tour': [0, 23, 53, 5, 15, 54, 24, 19, 22, 61, 52, 36, 7, 1, 11, 2, 8, 58, 37, 55, 48, 59, 46, 44, 49, 47, 62, 29, 50, 57, 20, 38, 31, 34, 10, 13, 65, 51, 32, 17, 25, 40, 33, 56, 12, 26, 63, 6, 60, 30, 42, 27, 3, 45, 64, 4, 16, 14, 35, 39, 41, 9, 28, 18, 21, 43], 'cur_cost': 108012.0}, {'tour': [0, 12, 25, 13, 20, 21, 19, 16, 18, 23, 22, 15, 14, 17, 27, 37, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14673.0}, {'tour': array([ 7, 57, 28,  2, 11, 61, 41, 40, 26,  3, 45, 23, 38, 49, 27, 64,  1,
        0, 16, 17, 43, 29, 50, 44, 54, 36, 47, 58, 10, 48, 42, 13, 12,  5,
       56, 31, 18, 20, 15, 34, 46, 35, 25, 65,  9,  8, 51, 53, 14, 33, 59,
       19,  4, 21, 32, 55,  6, 62, 52, 63, 30, 39, 60, 24, 22, 37],
      dtype=int64), 'cur_cost': 101755.0}, {'tour': array([53, 41, 50, 34, 46, 14, 27, 61, 49, 64, 40,  4, 32, 36,  1, 45, 37,
        6, 33, 28, 19, 48, 16, 10, 29, 44, 39, 59, 58, 56,  9,  8, 38, 47,
       11,  0,  7, 22, 52, 15, 63, 60, 24, 18, 26, 54, 42, 65,  5, 43, 31,
       21, 62, 12,  3, 57, 35, 23,  2, 25, 13, 51, 30, 20, 55, 17],
      dtype=int64), 'cur_cost': 108142.0}, {'tour': array([31, 18,  6, 42, 45, 11, 63, 59, 20, 46, 49, 32, 41, 21, 29, 43, 15,
       17, 56, 47, 24,  0, 34, 57, 36,  8, 10, 60,  3, 30, 28,  2, 35, 23,
       50, 55, 38, 52, 25, 14, 22,  7, 19, 39, 62, 54,  1, 37,  5,  4, 48,
       51, 40, 44, 53, 26, 65, 58, 27, 12, 61,  9, 64, 16, 33, 13],
      dtype=int64), 'cur_cost': 101671.0}, {'tour': array([35, 52, 54, 32, 56, 50, 16, 44, 48, 63, 37, 57, 21,  6, 17, 25, 22,
       13,  9, 61, 46, 55, 26, 42,  5, 51, 62,  2, 64, 36, 18, 29,  0, 10,
       43, 65, 39,  8, 14, 28, 60, 12, 15, 47, 11, 53, 59, 31, 27,  4, 40,
       34,  1, 58,  3, 20,  7, 38, 23, 49, 41, 19, 33, 45, 30, 24],
      dtype=int64), 'cur_cost': 112079.0}, {'tour': array([60, 43, 39, 21,  8,  4, 55, 47, 14, 29, 16,  0,  6, 22, 27, 11, 59,
       28, 61, 10, 49, 17,  7, 36, 20, 44, 37, 54, 25, 41, 56, 62, 57, 63,
       23, 40,  9, 50, 38, 52, 33, 64, 34, 26, 65,  1, 31, 35, 53, 51, 48,
       24,  3, 19, 15, 46, 13, 18, 30, 58, 42, 32,  2, 12,  5, 45],
      dtype=int64), 'cur_cost': 108780.0}, {'tour': array([45,  3,  7, 34, 41, 20,  4, 65, 17, 32, 47, 43, 15,  9, 26, 10, 52,
       40, 53, 28, 37, 39, 49, 12, 38, 21, 63, 18, 36,  5, 59, 23,  0, 29,
       62, 44, 35, 48, 60, 16, 61, 58, 55, 24,  8, 54, 46, 42, 31, 30, 25,
       50,  2, 22, 33,  6, 13, 57, 19, 14, 56, 51,  1, 11, 64, 27],
      dtype=int64), 'cur_cost': 107289.0}, {'tour': array([47, 25, 30, 59, 38, 57, 48, 64,  2, 31, 54,  1, 33, 45,  8, 53, 46,
        6, 37, 42, 11,  7,  4,  9, 58, 13, 14, 65, 63, 20, 51, 19,  3,  0,
       29, 23, 34, 26, 61,  5, 12, 27, 56, 36, 10, 52, 43, 49, 62, 55, 24,
       35, 32, 16, 41, 18, 17, 22, 50, 39, 44, 28, 21, 40, 15, 60],
      dtype=int64), 'cur_cost': 99080.0}, {'tour': array([30, 19, 29,  0, 24, 35,  2, 62, 34,  9, 55, 65, 63, 42, 23, 39, 53,
       60, 49, 56, 46, 51, 54, 32, 15, 26,  3, 57,  6, 31, 20, 45, 33, 16,
       64, 18, 50, 40, 21, 28, 22,  1, 41,  7, 59, 47,  8, 52, 48, 61, 44,
       14, 13, 37,  5, 43, 38, 10, 58, 11, 27, 36, 12,  4, 17, 25],
      dtype=int64), 'cur_cost': 102849.0}, {'tour': array([21, 41, 15,  1, 52, 65, 61, 54, 50, 57,  8, 30, 33, 13, 28, 34, 58,
       22, 37, 17,  5, 43, 38,  7, 56, 23, 31,  3, 24, 45,  0, 18, 29, 26,
       11, 20, 51, 40, 53, 49, 12, 39, 60, 48, 63,  6,  9, 16, 35, 32, 42,
       47, 25, 10, 27, 55, 19, 59, 62,  4,  2, 64, 44, 46, 36, 14],
      dtype=int64), 'cur_cost': 93881.0}, {'tour': array([ 9, 59, 64, 44, 38, 15,  5, 34,  3, 26, 28,  6, 27, 25, 20, 10, 16,
       22, 61, 45, 43, 17, 19, 65, 39,  4, 62, 21,  0, 54, 41, 63, 58, 37,
       60,  8,  1, 30, 42, 46, 23, 57, 48, 11, 13, 24, 35, 18, 53, 33, 31,
       29, 47, 40, 55, 14, 56, 49, 32, 51,  7,  2, 50, 52, 12, 36],
      dtype=int64), 'cur_cost': 102617.0}]
2025-08-03 16:15:34,591 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 16:15:34,591 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-03 16:15:34,592 - experts.management.collaboration_manager - INFO - 个体 10 利用路径生成报告: {'new_tour': array([ 7, 57, 28,  2, 11, 61, 41, 40, 26,  3, 45, 23, 38, 49, 27, 64,  1,
        0, 16, 17, 43, 29, 50, 44, 54, 36, 47, 58, 10, 48, 42, 13, 12,  5,
       56, 31, 18, 20, 15, 34, 46, 35, 25, 65,  9,  8, 51, 53, 14, 33, 59,
       19,  4, 21, 32, 55,  6, 62, 52, 63, 30, 39, 60, 24, 22, 37],
      dtype=int64), 'cur_cost': 101755.0}
2025-08-03 16:15:34,592 - experts.management.collaboration_manager - INFO - 个体 10 接受新路径 (成本: 101755.00)
2025-08-03 16:15:34,592 - experts.management.collaboration_manager - INFO - 为个体 11 生成探索路径
2025-08-03 16:15:34,592 - ExplorationExpert - INFO - 开始为个体 11 生成探索路径（算法实现）
2025-08-03 16:15:34,592 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:15:34,598 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:15:34,599 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:15:34,599 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14517.0, 路径长度: 66
2025-08-03 16:15:34,600 - experts.management.collaboration_manager - INFO - 个体 11 探索路径生成报告: {'new_tour': [0, 22, 27, 18, 16, 23, 12, 17, 15, 14, 20, 21, 13, 19, 36, 26, 25, 31, 33, 28, 30, 35, 34, 37, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14517.0}
2025-08-03 16:15:34,600 - experts.management.collaboration_manager - INFO - 个体 11 接受新路径 (成本: 14517.00)
2025-08-03 16:15:34,601 - experts.management.collaboration_manager - INFO - 为个体 12 生成探索路径
2025-08-03 16:15:34,601 - ExplorationExpert - INFO - 开始为个体 12 生成探索路径（算法实现）
2025-08-03 16:15:34,601 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:15:34,606 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:15:34,607 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:15:34,607 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14815.0, 路径长度: 66
2025-08-03 16:15:34,607 - experts.management.collaboration_manager - INFO - 个体 12 探索路径生成报告: {'new_tour': [0, 18, 7, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14815.0}
2025-08-03 16:15:34,608 - experts.management.collaboration_manager - INFO - 个体 12 接受新路径 (成本: 14815.00)
2025-08-03 16:15:34,608 - experts.management.collaboration_manager - INFO - 为个体 13 生成利用路径
2025-08-03 16:15:34,608 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:15:34,608 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:15:34,609 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 13 处的路径，新成本: 120911.0
2025-08-03 16:15:34,677 - ExploitationExpert - INFO - res_population_num: 8
2025-08-03 16:15:34,677 - ExploitationExpert - INFO - res_population_costs: [9844.0, 9582.0, 9551.0, 9546, 9538.0, 9537, 9526, 9521]
2025-08-03 16:15:34,677 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  6,  2,  8,  5,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 19, 16, 23, 18, 27,
       37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38,
       45, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       18, 16, 19, 21, 20, 13, 23, 22, 12, 17, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 49,
       40, 43, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 18, 17, 12, 22, 23, 16, 19,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:15:34,682 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:15:34,683 - ExploitationExpert - INFO - populations: [{'tour': [33, 7, 0, 5, 52, 12, 8, 16, 35, 32, 14, 17, 1, 18, 20, 4, 56, 2, 9, 65, 61, 22, 10, 3, 13, 36, 27, 24, 34, 40, 50, 46, 47, 15, 49, 38, 19, 43, 51, 48, 23, 30, 31, 21, 44, 41, 11, 53, 64, 60, 62, 55, 39, 45, 29, 6, 54, 58, 63, 57, 42, 26, 25, 37, 28, 59], 'cur_cost': 59221.0}, {'tour': array([62, 58, 50, 35, 18, 55, 43, 15,  9, 14,  1,  6, 25, 26,  4, 21, 52,
       56, 63, 32, 30, 64,  3, 60, 46, 17, 16,  0, 42, 19, 36, 38, 41, 39,
       29, 37, 13, 28, 49,  2, 40, 22, 20, 45,  8,  5, 51, 57, 53, 31, 11,
       54, 47, 61, 34, 10, 59, 23, 44, 27, 65,  7, 33, 12, 24, 48],
      dtype=int64), 'cur_cost': 103285.0}, {'tour': [0, 17, 18, 22, 12, 23, 16, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12337.0}, {'tour': [10, 11, 12, 31, 21, 36, 6, 8, 5, 58, 0, 62, 63, 56, 65, 54, 53, 13, 14, 4, 22, 9, 7, 61, 52, 49, 15, 17, 27, 33, 35, 34, 28, 2, 18, 29, 37, 20, 24, 3, 19, 40, 41, 38, 46, 51, 16, 26, 30, 32, 43, 48, 39, 47, 44, 42, 1, 60, 57, 59, 23, 25, 45, 50, 55, 64], 'cur_cost': 52424.0}, {'tour': array([10, 60, 62, 16,  1, 45, 11,  0, 17, 21, 32, 14, 18, 63, 34, 57, 64,
        4, 38, 31, 65, 58,  5, 24, 53, 19, 22, 61, 37,  2,  3, 36, 20, 40,
       39, 43,  7, 48, 29, 47, 52, 51, 50, 15, 44, 28,  6, 30, 12,  8, 54,
       26, 42,  9, 13, 33, 59, 55, 23, 25, 46, 35, 27, 56, 49, 41],
      dtype=int64), 'cur_cost': 105797.0}, {'tour': [13, 9, 1, 11, 5, 61, 10, 53, 12, 17, 37, 15, 18, 3, 58, 23, 4, 19, 27, 32, 2, 60, 57, 8, 22, 7, 21, 16, 28, 30, 20, 36, 6, 52, 44, 14, 43, 47, 35, 42, 48, 25, 0, 64, 63, 59, 49, 39, 51, 40, 26, 29, 46, 33, 31, 50, 45, 38, 56, 55, 62, 54, 65, 41, 34, 24], 'cur_cost': 67283.0}, {'tour': [33, 17, 18, 22, 12, 36, 16, 5, 14, 1, 27, 65, 54, 2, 9, 24, 29, 21, 7, 40, 50, 4, 8, 10, 48, 19, 62, 59, 56, 46, 51, 53, 30, 55, 45, 58, 63, 25, 39, 11, 43, 23, 31, 13, 37, 44, 61, 26, 28, 47, 34, 0, 3, 41, 64, 35, 42, 32, 60, 49, 20, 6, 57, 52, 15, 38], 'cur_cost': 102854.0}, {'tour': array([54, 42, 50, 64,  8, 14, 20, 29,  3, 18, 39, 34, 55, 43, 47, 30, 22,
       27,  0, 26, 60, 25, 65, 17, 46, 51, 62,  2,  9, 10, 61, 52, 12, 11,
       24,  6, 35, 57, 59, 58, 13, 32,  1, 40, 19, 41, 44, 23, 48, 49, 15,
       38, 63, 33, 36, 21, 37, 45, 28,  5, 56,  4, 31, 53,  7, 16],
      dtype=int64), 'cur_cost': 101620.0}, {'tour': [0, 23, 53, 5, 15, 54, 24, 19, 22, 61, 52, 36, 7, 1, 11, 2, 8, 58, 37, 55, 48, 59, 46, 44, 49, 47, 62, 29, 50, 57, 20, 38, 31, 34, 10, 13, 65, 51, 32, 17, 25, 40, 33, 56, 12, 26, 63, 6, 60, 30, 42, 27, 3, 45, 64, 4, 16, 14, 35, 39, 41, 9, 28, 18, 21, 43], 'cur_cost': 108012.0}, {'tour': [0, 12, 25, 13, 20, 21, 19, 16, 18, 23, 22, 15, 14, 17, 27, 37, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14673.0}, {'tour': array([ 7, 57, 28,  2, 11, 61, 41, 40, 26,  3, 45, 23, 38, 49, 27, 64,  1,
        0, 16, 17, 43, 29, 50, 44, 54, 36, 47, 58, 10, 48, 42, 13, 12,  5,
       56, 31, 18, 20, 15, 34, 46, 35, 25, 65,  9,  8, 51, 53, 14, 33, 59,
       19,  4, 21, 32, 55,  6, 62, 52, 63, 30, 39, 60, 24, 22, 37],
      dtype=int64), 'cur_cost': 101755.0}, {'tour': [0, 22, 27, 18, 16, 23, 12, 17, 15, 14, 20, 21, 13, 19, 36, 26, 25, 31, 33, 28, 30, 35, 34, 37, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14517.0}, {'tour': [0, 18, 7, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14815.0}, {'tour': array([33, 61, 21, 25,  7, 48, 56, 16,  0, 57, 22, 40, 10, 50,  8, 39, 27,
       62, 14, 60, 34, 15, 46, 18, 58,  2, 13, 42, 30, 12,  5, 53, 47, 20,
       19, 65, 37, 24, 23, 63, 28, 45, 35,  6, 52, 26, 44, 32, 64,  1, 31,
       29,  3, 41, 17, 49, 43,  9, 38, 36, 55, 59, 51, 11,  4, 54],
      dtype=int64), 'cur_cost': 120911.0}, {'tour': array([60, 43, 39, 21,  8,  4, 55, 47, 14, 29, 16,  0,  6, 22, 27, 11, 59,
       28, 61, 10, 49, 17,  7, 36, 20, 44, 37, 54, 25, 41, 56, 62, 57, 63,
       23, 40,  9, 50, 38, 52, 33, 64, 34, 26, 65,  1, 31, 35, 53, 51, 48,
       24,  3, 19, 15, 46, 13, 18, 30, 58, 42, 32,  2, 12,  5, 45],
      dtype=int64), 'cur_cost': 108780.0}, {'tour': array([45,  3,  7, 34, 41, 20,  4, 65, 17, 32, 47, 43, 15,  9, 26, 10, 52,
       40, 53, 28, 37, 39, 49, 12, 38, 21, 63, 18, 36,  5, 59, 23,  0, 29,
       62, 44, 35, 48, 60, 16, 61, 58, 55, 24,  8, 54, 46, 42, 31, 30, 25,
       50,  2, 22, 33,  6, 13, 57, 19, 14, 56, 51,  1, 11, 64, 27],
      dtype=int64), 'cur_cost': 107289.0}, {'tour': array([47, 25, 30, 59, 38, 57, 48, 64,  2, 31, 54,  1, 33, 45,  8, 53, 46,
        6, 37, 42, 11,  7,  4,  9, 58, 13, 14, 65, 63, 20, 51, 19,  3,  0,
       29, 23, 34, 26, 61,  5, 12, 27, 56, 36, 10, 52, 43, 49, 62, 55, 24,
       35, 32, 16, 41, 18, 17, 22, 50, 39, 44, 28, 21, 40, 15, 60],
      dtype=int64), 'cur_cost': 99080.0}, {'tour': array([30, 19, 29,  0, 24, 35,  2, 62, 34,  9, 55, 65, 63, 42, 23, 39, 53,
       60, 49, 56, 46, 51, 54, 32, 15, 26,  3, 57,  6, 31, 20, 45, 33, 16,
       64, 18, 50, 40, 21, 28, 22,  1, 41,  7, 59, 47,  8, 52, 48, 61, 44,
       14, 13, 37,  5, 43, 38, 10, 58, 11, 27, 36, 12,  4, 17, 25],
      dtype=int64), 'cur_cost': 102849.0}, {'tour': array([21, 41, 15,  1, 52, 65, 61, 54, 50, 57,  8, 30, 33, 13, 28, 34, 58,
       22, 37, 17,  5, 43, 38,  7, 56, 23, 31,  3, 24, 45,  0, 18, 29, 26,
       11, 20, 51, 40, 53, 49, 12, 39, 60, 48, 63,  6,  9, 16, 35, 32, 42,
       47, 25, 10, 27, 55, 19, 59, 62,  4,  2, 64, 44, 46, 36, 14],
      dtype=int64), 'cur_cost': 93881.0}, {'tour': array([ 9, 59, 64, 44, 38, 15,  5, 34,  3, 26, 28,  6, 27, 25, 20, 10, 16,
       22, 61, 45, 43, 17, 19, 65, 39,  4, 62, 21,  0, 54, 41, 63, 58, 37,
       60,  8,  1, 30, 42, 46, 23, 57, 48, 11, 13, 24, 35, 18, 53, 33, 31,
       29, 47, 40, 55, 14, 56, 49, 32, 51,  7,  2, 50, 52, 12, 36],
      dtype=int64), 'cur_cost': 102617.0}]
2025-08-03 16:15:34,689 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-08-03 16:15:34,689 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-03 16:15:34,690 - experts.management.collaboration_manager - INFO - 个体 13 利用路径生成报告: {'new_tour': array([33, 61, 21, 25,  7, 48, 56, 16,  0, 57, 22, 40, 10, 50,  8, 39, 27,
       62, 14, 60, 34, 15, 46, 18, 58,  2, 13, 42, 30, 12,  5, 53, 47, 20,
       19, 65, 37, 24, 23, 63, 28, 45, 35,  6, 52, 26, 44, 32, 64,  1, 31,
       29,  3, 41, 17, 49, 43,  9, 38, 36, 55, 59, 51, 11,  4, 54],
      dtype=int64), 'cur_cost': 120911.0}
2025-08-03 16:15:34,690 - experts.management.collaboration_manager - INFO - 个体 13 接受新路径 (成本: 120911.00)
2025-08-03 16:15:34,690 - experts.management.collaboration_manager - INFO - 为个体 14 生成探索路径
2025-08-03 16:15:34,690 - ExplorationExpert - INFO - 开始为个体 14 生成探索路径（算法实现）
2025-08-03 16:15:34,690 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:15:34,693 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:15:34,694 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:15:34,694 - ExplorationExpert - INFO - 探索路径生成完成，成本: 110871.0, 路径长度: 66
2025-08-03 16:15:34,694 - experts.management.collaboration_manager - INFO - 个体 14 探索路径生成报告: {'new_tour': [12, 21, 5, 35, 61, 27, 36, 54, 37, 4, 29, 13, 22, 9, 10, 3, 48, 49, 15, 34, 28, 44, 51, 25, 60, 46, 33, 63, 41, 20, 53, 47, 30, 62, 7, 45, 59, 6, 11, 42, 8, 56, 18, 55, 32, 31, 52, 57, 14, 1, 26, 0, 38, 43, 64, 23, 16, 19, 24, 50, 2, 58, 39, 65, 40, 17], 'cur_cost': 110871.0}
2025-08-03 16:15:34,694 - experts.management.collaboration_manager - INFO - 个体 14 接受新路径 (成本: 110871.00)
2025-08-03 16:15:34,694 - experts.management.collaboration_manager - INFO - 为个体 15 生成探索路径
2025-08-03 16:15:34,694 - ExplorationExpert - INFO - 开始为个体 15 生成探索路径（算法实现）
2025-08-03 16:15:34,694 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:15:34,699 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:15:34,699 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:15:34,699 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14787.0, 路径长度: 66
2025-08-03 16:15:34,700 - experts.management.collaboration_manager - INFO - 个体 15 探索路径生成报告: {'new_tour': [0, 22, 7, 20, 21, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14787.0}
2025-08-03 16:15:34,700 - experts.management.collaboration_manager - INFO - 个体 15 接受新路径 (成本: 14787.00)
2025-08-03 16:15:34,701 - experts.management.collaboration_manager - INFO - 为个体 16 生成利用路径
2025-08-03 16:15:34,701 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:15:34,701 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:15:34,702 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 16 处的路径，新成本: 110306.0
2025-08-03 16:15:34,772 - ExploitationExpert - INFO - res_population_num: 9
2025-08-03 16:15:34,772 - ExploitationExpert - INFO - res_population_costs: [9844.0, 9582.0, 9551.0, 9546, 9538.0, 9537, 9526, 9521, 9521]
2025-08-03 16:15:34,772 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  6,  2,  8,  5,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 19, 16, 23, 18, 27,
       37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38,
       45, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       18, 16, 19, 21, 20, 13, 23, 22, 12, 17, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 49,
       40, 43, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 18, 17, 12, 22, 23, 16, 19,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:15:34,775 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:15:34,775 - ExploitationExpert - INFO - populations: [{'tour': [33, 7, 0, 5, 52, 12, 8, 16, 35, 32, 14, 17, 1, 18, 20, 4, 56, 2, 9, 65, 61, 22, 10, 3, 13, 36, 27, 24, 34, 40, 50, 46, 47, 15, 49, 38, 19, 43, 51, 48, 23, 30, 31, 21, 44, 41, 11, 53, 64, 60, 62, 55, 39, 45, 29, 6, 54, 58, 63, 57, 42, 26, 25, 37, 28, 59], 'cur_cost': 59221.0}, {'tour': array([62, 58, 50, 35, 18, 55, 43, 15,  9, 14,  1,  6, 25, 26,  4, 21, 52,
       56, 63, 32, 30, 64,  3, 60, 46, 17, 16,  0, 42, 19, 36, 38, 41, 39,
       29, 37, 13, 28, 49,  2, 40, 22, 20, 45,  8,  5, 51, 57, 53, 31, 11,
       54, 47, 61, 34, 10, 59, 23, 44, 27, 65,  7, 33, 12, 24, 48],
      dtype=int64), 'cur_cost': 103285.0}, {'tour': [0, 17, 18, 22, 12, 23, 16, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12337.0}, {'tour': [10, 11, 12, 31, 21, 36, 6, 8, 5, 58, 0, 62, 63, 56, 65, 54, 53, 13, 14, 4, 22, 9, 7, 61, 52, 49, 15, 17, 27, 33, 35, 34, 28, 2, 18, 29, 37, 20, 24, 3, 19, 40, 41, 38, 46, 51, 16, 26, 30, 32, 43, 48, 39, 47, 44, 42, 1, 60, 57, 59, 23, 25, 45, 50, 55, 64], 'cur_cost': 52424.0}, {'tour': array([10, 60, 62, 16,  1, 45, 11,  0, 17, 21, 32, 14, 18, 63, 34, 57, 64,
        4, 38, 31, 65, 58,  5, 24, 53, 19, 22, 61, 37,  2,  3, 36, 20, 40,
       39, 43,  7, 48, 29, 47, 52, 51, 50, 15, 44, 28,  6, 30, 12,  8, 54,
       26, 42,  9, 13, 33, 59, 55, 23, 25, 46, 35, 27, 56, 49, 41],
      dtype=int64), 'cur_cost': 105797.0}, {'tour': [13, 9, 1, 11, 5, 61, 10, 53, 12, 17, 37, 15, 18, 3, 58, 23, 4, 19, 27, 32, 2, 60, 57, 8, 22, 7, 21, 16, 28, 30, 20, 36, 6, 52, 44, 14, 43, 47, 35, 42, 48, 25, 0, 64, 63, 59, 49, 39, 51, 40, 26, 29, 46, 33, 31, 50, 45, 38, 56, 55, 62, 54, 65, 41, 34, 24], 'cur_cost': 67283.0}, {'tour': [33, 17, 18, 22, 12, 36, 16, 5, 14, 1, 27, 65, 54, 2, 9, 24, 29, 21, 7, 40, 50, 4, 8, 10, 48, 19, 62, 59, 56, 46, 51, 53, 30, 55, 45, 58, 63, 25, 39, 11, 43, 23, 31, 13, 37, 44, 61, 26, 28, 47, 34, 0, 3, 41, 64, 35, 42, 32, 60, 49, 20, 6, 57, 52, 15, 38], 'cur_cost': 102854.0}, {'tour': array([54, 42, 50, 64,  8, 14, 20, 29,  3, 18, 39, 34, 55, 43, 47, 30, 22,
       27,  0, 26, 60, 25, 65, 17, 46, 51, 62,  2,  9, 10, 61, 52, 12, 11,
       24,  6, 35, 57, 59, 58, 13, 32,  1, 40, 19, 41, 44, 23, 48, 49, 15,
       38, 63, 33, 36, 21, 37, 45, 28,  5, 56,  4, 31, 53,  7, 16],
      dtype=int64), 'cur_cost': 101620.0}, {'tour': [0, 23, 53, 5, 15, 54, 24, 19, 22, 61, 52, 36, 7, 1, 11, 2, 8, 58, 37, 55, 48, 59, 46, 44, 49, 47, 62, 29, 50, 57, 20, 38, 31, 34, 10, 13, 65, 51, 32, 17, 25, 40, 33, 56, 12, 26, 63, 6, 60, 30, 42, 27, 3, 45, 64, 4, 16, 14, 35, 39, 41, 9, 28, 18, 21, 43], 'cur_cost': 108012.0}, {'tour': [0, 12, 25, 13, 20, 21, 19, 16, 18, 23, 22, 15, 14, 17, 27, 37, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14673.0}, {'tour': array([ 7, 57, 28,  2, 11, 61, 41, 40, 26,  3, 45, 23, 38, 49, 27, 64,  1,
        0, 16, 17, 43, 29, 50, 44, 54, 36, 47, 58, 10, 48, 42, 13, 12,  5,
       56, 31, 18, 20, 15, 34, 46, 35, 25, 65,  9,  8, 51, 53, 14, 33, 59,
       19,  4, 21, 32, 55,  6, 62, 52, 63, 30, 39, 60, 24, 22, 37],
      dtype=int64), 'cur_cost': 101755.0}, {'tour': [0, 22, 27, 18, 16, 23, 12, 17, 15, 14, 20, 21, 13, 19, 36, 26, 25, 31, 33, 28, 30, 35, 34, 37, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14517.0}, {'tour': [0, 18, 7, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14815.0}, {'tour': array([33, 61, 21, 25,  7, 48, 56, 16,  0, 57, 22, 40, 10, 50,  8, 39, 27,
       62, 14, 60, 34, 15, 46, 18, 58,  2, 13, 42, 30, 12,  5, 53, 47, 20,
       19, 65, 37, 24, 23, 63, 28, 45, 35,  6, 52, 26, 44, 32, 64,  1, 31,
       29,  3, 41, 17, 49, 43,  9, 38, 36, 55, 59, 51, 11,  4, 54],
      dtype=int64), 'cur_cost': 120911.0}, {'tour': [12, 21, 5, 35, 61, 27, 36, 54, 37, 4, 29, 13, 22, 9, 10, 3, 48, 49, 15, 34, 28, 44, 51, 25, 60, 46, 33, 63, 41, 20, 53, 47, 30, 62, 7, 45, 59, 6, 11, 42, 8, 56, 18, 55, 32, 31, 52, 57, 14, 1, 26, 0, 38, 43, 64, 23, 16, 19, 24, 50, 2, 58, 39, 65, 40, 17], 'cur_cost': 110871.0}, {'tour': [0, 22, 7, 20, 21, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14787.0}, {'tour': array([45, 25, 36,  6, 23, 58, 40, 35, 17, 47, 59, 37, 28, 39, 65, 27,  1,
       41, 31,  3, 49, 61, 55, 14, 44, 50, 63, 53,  8, 22, 51, 54, 20, 18,
       46,  2, 21, 11, 30, 56, 52, 16, 15, 60,  9, 33, 38, 48,  5,  4,  7,
       19, 57, 43, 26, 42, 10, 34, 24, 13, 62, 32, 64,  0, 12, 29],
      dtype=int64), 'cur_cost': 110306.0}, {'tour': array([30, 19, 29,  0, 24, 35,  2, 62, 34,  9, 55, 65, 63, 42, 23, 39, 53,
       60, 49, 56, 46, 51, 54, 32, 15, 26,  3, 57,  6, 31, 20, 45, 33, 16,
       64, 18, 50, 40, 21, 28, 22,  1, 41,  7, 59, 47,  8, 52, 48, 61, 44,
       14, 13, 37,  5, 43, 38, 10, 58, 11, 27, 36, 12,  4, 17, 25],
      dtype=int64), 'cur_cost': 102849.0}, {'tour': array([21, 41, 15,  1, 52, 65, 61, 54, 50, 57,  8, 30, 33, 13, 28, 34, 58,
       22, 37, 17,  5, 43, 38,  7, 56, 23, 31,  3, 24, 45,  0, 18, 29, 26,
       11, 20, 51, 40, 53, 49, 12, 39, 60, 48, 63,  6,  9, 16, 35, 32, 42,
       47, 25, 10, 27, 55, 19, 59, 62,  4,  2, 64, 44, 46, 36, 14],
      dtype=int64), 'cur_cost': 93881.0}, {'tour': array([ 9, 59, 64, 44, 38, 15,  5, 34,  3, 26, 28,  6, 27, 25, 20, 10, 16,
       22, 61, 45, 43, 17, 19, 65, 39,  4, 62, 21,  0, 54, 41, 63, 58, 37,
       60,  8,  1, 30, 42, 46, 23, 57, 48, 11, 13, 24, 35, 18, 53, 33, 31,
       29, 47, 40, 55, 14, 56, 49, 32, 51,  7,  2, 50, 52, 12, 36],
      dtype=int64), 'cur_cost': 102617.0}]
2025-08-03 16:15:34,778 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-08-03 16:15:34,778 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-03 16:15:34,779 - experts.management.collaboration_manager - INFO - 个体 16 利用路径生成报告: {'new_tour': array([45, 25, 36,  6, 23, 58, 40, 35, 17, 47, 59, 37, 28, 39, 65, 27,  1,
       41, 31,  3, 49, 61, 55, 14, 44, 50, 63, 53,  8, 22, 51, 54, 20, 18,
       46,  2, 21, 11, 30, 56, 52, 16, 15, 60,  9, 33, 38, 48,  5,  4,  7,
       19, 57, 43, 26, 42, 10, 34, 24, 13, 62, 32, 64,  0, 12, 29],
      dtype=int64), 'cur_cost': 110306.0}
2025-08-03 16:15:34,779 - experts.management.collaboration_manager - INFO - 个体 16 接受新路径 (成本: 110306.00)
2025-08-03 16:15:34,779 - experts.management.collaboration_manager - INFO - 为个体 17 生成探索路径
2025-08-03 16:15:34,779 - ExplorationExpert - INFO - 开始为个体 17 生成探索路径（算法实现）
2025-08-03 16:15:34,779 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:15:34,788 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:15:34,788 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:15:34,788 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101023.0, 路径长度: 66
2025-08-03 16:15:34,788 - experts.management.collaboration_manager - INFO - 个体 17 探索路径生成报告: {'new_tour': [12, 5, 20, 14, 17, 56, 58, 29, 13, 4, 2, 22, 57, 48, 49, 15, 27, 46, 28, 63, 43, 51, 3, 25, 30, 59, 62, 47, 16, 50, 41, 52, 9, 31, 10, 7, 65, 6, 37, 32, 38, 21, 18, 19, 40, 55, 11, 53, 35, 26, 33, 24, 42, 54, 36, 8, 45, 23, 60, 44, 61, 39, 0, 34, 64, 1], 'cur_cost': 101023.0}
2025-08-03 16:15:34,789 - experts.management.collaboration_manager - INFO - 个体 17 接受新路径 (成本: 101023.00)
2025-08-03 16:15:34,789 - experts.management.collaboration_manager - INFO - 为个体 18 生成探索路径
2025-08-03 16:15:34,789 - ExplorationExpert - INFO - 开始为个体 18 生成探索路径（算法实现）
2025-08-03 16:15:34,789 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:15:34,800 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:15:34,800 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:15:34,800 - ExplorationExpert - INFO - 探索路径生成完成，成本: 60414.0, 路径长度: 66
2025-08-03 16:15:34,801 - experts.management.collaboration_manager - INFO - 个体 18 探索路径生成报告: {'new_tour': [11, 54, 58, 63, 22, 35, 34, 27, 15, 7, 10, 3, 60, 5, 65, 12, 20, 36, 28, 24, 0, 14, 25, 13, 16, 21, 40, 26, 43, 23, 1, 4, 56, 8, 2, 19, 6, 62, 57, 59, 18, 30, 42, 51, 39, 17, 47, 44, 49, 48, 38, 33, 37, 9, 61, 52, 46, 45, 31, 29, 41, 50, 32, 55, 53, 64], 'cur_cost': 60414.0}
2025-08-03 16:15:34,801 - experts.management.collaboration_manager - INFO - 个体 18 接受新路径 (成本: 60414.00)
2025-08-03 16:15:34,801 - experts.management.collaboration_manager - INFO - 为个体 19 生成利用路径
2025-08-03 16:15:34,801 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:15:34,801 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:15:34,801 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 19 处的路径，新成本: 120690.0
2025-08-03 16:15:34,874 - ExploitationExpert - INFO - res_population_num: 10
2025-08-03 16:15:34,874 - ExploitationExpert - INFO - res_population_costs: [9844.0, 9582.0, 9551.0, 9546, 9538.0, 9537, 9526, 9521, 9521, 9521]
2025-08-03 16:15:34,874 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  6,  2,  8,  5,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 19, 16, 23, 18, 27,
       37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38,
       45, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       18, 16, 19, 21, 20, 13, 23, 22, 12, 17, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 49,
       40, 43, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 18, 17, 12, 22, 23, 16, 19,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:15:34,877 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:15:34,877 - ExploitationExpert - INFO - populations: [{'tour': [33, 7, 0, 5, 52, 12, 8, 16, 35, 32, 14, 17, 1, 18, 20, 4, 56, 2, 9, 65, 61, 22, 10, 3, 13, 36, 27, 24, 34, 40, 50, 46, 47, 15, 49, 38, 19, 43, 51, 48, 23, 30, 31, 21, 44, 41, 11, 53, 64, 60, 62, 55, 39, 45, 29, 6, 54, 58, 63, 57, 42, 26, 25, 37, 28, 59], 'cur_cost': 59221.0}, {'tour': array([62, 58, 50, 35, 18, 55, 43, 15,  9, 14,  1,  6, 25, 26,  4, 21, 52,
       56, 63, 32, 30, 64,  3, 60, 46, 17, 16,  0, 42, 19, 36, 38, 41, 39,
       29, 37, 13, 28, 49,  2, 40, 22, 20, 45,  8,  5, 51, 57, 53, 31, 11,
       54, 47, 61, 34, 10, 59, 23, 44, 27, 65,  7, 33, 12, 24, 48],
      dtype=int64), 'cur_cost': 103285.0}, {'tour': [0, 17, 18, 22, 12, 23, 16, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12337.0}, {'tour': [10, 11, 12, 31, 21, 36, 6, 8, 5, 58, 0, 62, 63, 56, 65, 54, 53, 13, 14, 4, 22, 9, 7, 61, 52, 49, 15, 17, 27, 33, 35, 34, 28, 2, 18, 29, 37, 20, 24, 3, 19, 40, 41, 38, 46, 51, 16, 26, 30, 32, 43, 48, 39, 47, 44, 42, 1, 60, 57, 59, 23, 25, 45, 50, 55, 64], 'cur_cost': 52424.0}, {'tour': array([10, 60, 62, 16,  1, 45, 11,  0, 17, 21, 32, 14, 18, 63, 34, 57, 64,
        4, 38, 31, 65, 58,  5, 24, 53, 19, 22, 61, 37,  2,  3, 36, 20, 40,
       39, 43,  7, 48, 29, 47, 52, 51, 50, 15, 44, 28,  6, 30, 12,  8, 54,
       26, 42,  9, 13, 33, 59, 55, 23, 25, 46, 35, 27, 56, 49, 41],
      dtype=int64), 'cur_cost': 105797.0}, {'tour': [13, 9, 1, 11, 5, 61, 10, 53, 12, 17, 37, 15, 18, 3, 58, 23, 4, 19, 27, 32, 2, 60, 57, 8, 22, 7, 21, 16, 28, 30, 20, 36, 6, 52, 44, 14, 43, 47, 35, 42, 48, 25, 0, 64, 63, 59, 49, 39, 51, 40, 26, 29, 46, 33, 31, 50, 45, 38, 56, 55, 62, 54, 65, 41, 34, 24], 'cur_cost': 67283.0}, {'tour': [33, 17, 18, 22, 12, 36, 16, 5, 14, 1, 27, 65, 54, 2, 9, 24, 29, 21, 7, 40, 50, 4, 8, 10, 48, 19, 62, 59, 56, 46, 51, 53, 30, 55, 45, 58, 63, 25, 39, 11, 43, 23, 31, 13, 37, 44, 61, 26, 28, 47, 34, 0, 3, 41, 64, 35, 42, 32, 60, 49, 20, 6, 57, 52, 15, 38], 'cur_cost': 102854.0}, {'tour': array([54, 42, 50, 64,  8, 14, 20, 29,  3, 18, 39, 34, 55, 43, 47, 30, 22,
       27,  0, 26, 60, 25, 65, 17, 46, 51, 62,  2,  9, 10, 61, 52, 12, 11,
       24,  6, 35, 57, 59, 58, 13, 32,  1, 40, 19, 41, 44, 23, 48, 49, 15,
       38, 63, 33, 36, 21, 37, 45, 28,  5, 56,  4, 31, 53,  7, 16],
      dtype=int64), 'cur_cost': 101620.0}, {'tour': [0, 23, 53, 5, 15, 54, 24, 19, 22, 61, 52, 36, 7, 1, 11, 2, 8, 58, 37, 55, 48, 59, 46, 44, 49, 47, 62, 29, 50, 57, 20, 38, 31, 34, 10, 13, 65, 51, 32, 17, 25, 40, 33, 56, 12, 26, 63, 6, 60, 30, 42, 27, 3, 45, 64, 4, 16, 14, 35, 39, 41, 9, 28, 18, 21, 43], 'cur_cost': 108012.0}, {'tour': [0, 12, 25, 13, 20, 21, 19, 16, 18, 23, 22, 15, 14, 17, 27, 37, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14673.0}, {'tour': array([ 7, 57, 28,  2, 11, 61, 41, 40, 26,  3, 45, 23, 38, 49, 27, 64,  1,
        0, 16, 17, 43, 29, 50, 44, 54, 36, 47, 58, 10, 48, 42, 13, 12,  5,
       56, 31, 18, 20, 15, 34, 46, 35, 25, 65,  9,  8, 51, 53, 14, 33, 59,
       19,  4, 21, 32, 55,  6, 62, 52, 63, 30, 39, 60, 24, 22, 37],
      dtype=int64), 'cur_cost': 101755.0}, {'tour': [0, 22, 27, 18, 16, 23, 12, 17, 15, 14, 20, 21, 13, 19, 36, 26, 25, 31, 33, 28, 30, 35, 34, 37, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14517.0}, {'tour': [0, 18, 7, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14815.0}, {'tour': array([33, 61, 21, 25,  7, 48, 56, 16,  0, 57, 22, 40, 10, 50,  8, 39, 27,
       62, 14, 60, 34, 15, 46, 18, 58,  2, 13, 42, 30, 12,  5, 53, 47, 20,
       19, 65, 37, 24, 23, 63, 28, 45, 35,  6, 52, 26, 44, 32, 64,  1, 31,
       29,  3, 41, 17, 49, 43,  9, 38, 36, 55, 59, 51, 11,  4, 54],
      dtype=int64), 'cur_cost': 120911.0}, {'tour': [12, 21, 5, 35, 61, 27, 36, 54, 37, 4, 29, 13, 22, 9, 10, 3, 48, 49, 15, 34, 28, 44, 51, 25, 60, 46, 33, 63, 41, 20, 53, 47, 30, 62, 7, 45, 59, 6, 11, 42, 8, 56, 18, 55, 32, 31, 52, 57, 14, 1, 26, 0, 38, 43, 64, 23, 16, 19, 24, 50, 2, 58, 39, 65, 40, 17], 'cur_cost': 110871.0}, {'tour': [0, 22, 7, 20, 21, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14787.0}, {'tour': array([45, 25, 36,  6, 23, 58, 40, 35, 17, 47, 59, 37, 28, 39, 65, 27,  1,
       41, 31,  3, 49, 61, 55, 14, 44, 50, 63, 53,  8, 22, 51, 54, 20, 18,
       46,  2, 21, 11, 30, 56, 52, 16, 15, 60,  9, 33, 38, 48,  5,  4,  7,
       19, 57, 43, 26, 42, 10, 34, 24, 13, 62, 32, 64,  0, 12, 29],
      dtype=int64), 'cur_cost': 110306.0}, {'tour': [12, 5, 20, 14, 17, 56, 58, 29, 13, 4, 2, 22, 57, 48, 49, 15, 27, 46, 28, 63, 43, 51, 3, 25, 30, 59, 62, 47, 16, 50, 41, 52, 9, 31, 10, 7, 65, 6, 37, 32, 38, 21, 18, 19, 40, 55, 11, 53, 35, 26, 33, 24, 42, 54, 36, 8, 45, 23, 60, 44, 61, 39, 0, 34, 64, 1], 'cur_cost': 101023.0}, {'tour': [11, 54, 58, 63, 22, 35, 34, 27, 15, 7, 10, 3, 60, 5, 65, 12, 20, 36, 28, 24, 0, 14, 25, 13, 16, 21, 40, 26, 43, 23, 1, 4, 56, 8, 2, 19, 6, 62, 57, 59, 18, 30, 42, 51, 39, 17, 47, 44, 49, 48, 38, 33, 37, 9, 61, 52, 46, 45, 31, 29, 41, 50, 32, 55, 53, 64], 'cur_cost': 60414.0}, {'tour': array([ 5, 13, 30, 28, 51, 26, 45, 59, 27, 14,  7, 44, 53, 10,  2, 11, 65,
       38,  1, 23, 16, 17, 50, 24,  9, 64, 36, 48, 57, 42, 12, 20, 46,  6,
       37, 60, 18, 49, 25, 19, 61, 39, 52, 43, 55, 63, 15, 35, 47, 62,  8,
       40, 54, 33,  4, 22, 56, 31, 41, 34,  0, 32, 58, 21, 29,  3],
      dtype=int64), 'cur_cost': 120690.0}]
2025-08-03 16:15:34,881 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-08-03 16:15:34,884 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-03 16:15:34,885 - experts.management.collaboration_manager - INFO - 个体 19 利用路径生成报告: {'new_tour': array([ 5, 13, 30, 28, 51, 26, 45, 59, 27, 14,  7, 44, 53, 10,  2, 11, 65,
       38,  1, 23, 16, 17, 50, 24,  9, 64, 36, 48, 57, 42, 12, 20, 46,  6,
       37, 60, 18, 49, 25, 19, 61, 39, 52, 43, 55, 63, 15, 35, 47, 62,  8,
       40, 54, 33,  4, 22, 56, 31, 41, 34,  0, 32, 58, 21, 29,  3],
      dtype=int64), 'cur_cost': 120690.0}
2025-08-03 16:15:34,885 - experts.management.collaboration_manager - INFO - 个体 19 接受新路径 (成本: 120690.00)
2025-08-03 16:15:34,885 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 16, 'rejected': 4, 'elite_protected': 4}
2025-08-03 16:15:34,885 - experts.management.collaboration_manager - INFO - 接受率: 16/20 (80.0%)
2025-08-03 16:15:34,886 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [33, 7, 0, 5, 52, 12, 8, 16, 35, 32, 14, 17, 1, 18, 20, 4, 56, 2, 9, 65, 61, 22, 10, 3, 13, 36, 27, 24, 34, 40, 50, 46, 47, 15, 49, 38, 19, 43, 51, 48, 23, 30, 31, 21, 44, 41, 11, 53, 64, 60, 62, 55, 39, 45, 29, 6, 54, 58, 63, 57, 42, 26, 25, 37, 28, 59], 'cur_cost': 59221.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([62, 58, 50, 35, 18, 55, 43, 15,  9, 14,  1,  6, 25, 26,  4, 21, 52,
       56, 63, 32, 30, 64,  3, 60, 46, 17, 16,  0, 42, 19, 36, 38, 41, 39,
       29, 37, 13, 28, 49,  2, 40, 22, 20, 45,  8,  5, 51, 57, 53, 31, 11,
       54, 47, 61, 34, 10, 59, 23, 44, 27, 65,  7, 33, 12, 24, 48],
      dtype=int64), 'cur_cost': 103285.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 18, 22, 12, 23, 16, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12337.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [10, 11, 12, 31, 21, 36, 6, 8, 5, 58, 0, 62, 63, 56, 65, 54, 53, 13, 14, 4, 22, 9, 7, 61, 52, 49, 15, 17, 27, 33, 35, 34, 28, 2, 18, 29, 37, 20, 24, 3, 19, 40, 41, 38, 46, 51, 16, 26, 30, 32, 43, 48, 39, 47, 44, 42, 1, 60, 57, 59, 23, 25, 45, 50, 55, 64], 'cur_cost': 52424.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([10, 60, 62, 16,  1, 45, 11,  0, 17, 21, 32, 14, 18, 63, 34, 57, 64,
        4, 38, 31, 65, 58,  5, 24, 53, 19, 22, 61, 37,  2,  3, 36, 20, 40,
       39, 43,  7, 48, 29, 47, 52, 51, 50, 15, 44, 28,  6, 30, 12,  8, 54,
       26, 42,  9, 13, 33, 59, 55, 23, 25, 46, 35, 27, 56, 49, 41],
      dtype=int64), 'cur_cost': 105797.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [13, 9, 1, 11, 5, 61, 10, 53, 12, 17, 37, 15, 18, 3, 58, 23, 4, 19, 27, 32, 2, 60, 57, 8, 22, 7, 21, 16, 28, 30, 20, 36, 6, 52, 44, 14, 43, 47, 35, 42, 48, 25, 0, 64, 63, 59, 49, 39, 51, 40, 26, 29, 46, 33, 31, 50, 45, 38, 56, 55, 62, 54, 65, 41, 34, 24], 'cur_cost': 67283.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [33, 17, 18, 22, 12, 36, 16, 5, 14, 1, 27, 65, 54, 2, 9, 24, 29, 21, 7, 40, 50, 4, 8, 10, 48, 19, 62, 59, 56, 46, 51, 53, 30, 55, 45, 58, 63, 25, 39, 11, 43, 23, 31, 13, 37, 44, 61, 26, 28, 47, 34, 0, 3, 41, 64, 35, 42, 32, 60, 49, 20, 6, 57, 52, 15, 38], 'cur_cost': 102854.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([54, 42, 50, 64,  8, 14, 20, 29,  3, 18, 39, 34, 55, 43, 47, 30, 22,
       27,  0, 26, 60, 25, 65, 17, 46, 51, 62,  2,  9, 10, 61, 52, 12, 11,
       24,  6, 35, 57, 59, 58, 13, 32,  1, 40, 19, 41, 44, 23, 48, 49, 15,
       38, 63, 33, 36, 21, 37, 45, 28,  5, 56,  4, 31, 53,  7, 16],
      dtype=int64), 'cur_cost': 101620.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 23, 53, 5, 15, 54, 24, 19, 22, 61, 52, 36, 7, 1, 11, 2, 8, 58, 37, 55, 48, 59, 46, 44, 49, 47, 62, 29, 50, 57, 20, 38, 31, 34, 10, 13, 65, 51, 32, 17, 25, 40, 33, 56, 12, 26, 63, 6, 60, 30, 42, 27, 3, 45, 64, 4, 16, 14, 35, 39, 41, 9, 28, 18, 21, 43], 'cur_cost': 108012.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 25, 13, 20, 21, 19, 16, 18, 23, 22, 15, 14, 17, 27, 37, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14673.0}}, {'individual': 10, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 7, 57, 28,  2, 11, 61, 41, 40, 26,  3, 45, 23, 38, 49, 27, 64,  1,
        0, 16, 17, 43, 29, 50, 44, 54, 36, 47, 58, 10, 48, 42, 13, 12,  5,
       56, 31, 18, 20, 15, 34, 46, 35, 25, 65,  9,  8, 51, 53, 14, 33, 59,
       19,  4, 21, 32, 55,  6, 62, 52, 63, 30, 39, 60, 24, 22, 37],
      dtype=int64), 'cur_cost': 101755.0}}, {'individual': 11, 'strategy': 'explore', 'path_data': {'new_tour': [0, 22, 27, 18, 16, 23, 12, 17, 15, 14, 20, 21, 13, 19, 36, 26, 25, 31, 33, 28, 30, 35, 34, 37, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14517.0}}, {'individual': 12, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 7, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14815.0}}, {'individual': 13, 'strategy': 'exploit', 'path_data': {'new_tour': array([33, 61, 21, 25,  7, 48, 56, 16,  0, 57, 22, 40, 10, 50,  8, 39, 27,
       62, 14, 60, 34, 15, 46, 18, 58,  2, 13, 42, 30, 12,  5, 53, 47, 20,
       19, 65, 37, 24, 23, 63, 28, 45, 35,  6, 52, 26, 44, 32, 64,  1, 31,
       29,  3, 41, 17, 49, 43,  9, 38, 36, 55, 59, 51, 11,  4, 54],
      dtype=int64), 'cur_cost': 120911.0}}, {'individual': 14, 'strategy': 'explore', 'path_data': {'new_tour': [12, 21, 5, 35, 61, 27, 36, 54, 37, 4, 29, 13, 22, 9, 10, 3, 48, 49, 15, 34, 28, 44, 51, 25, 60, 46, 33, 63, 41, 20, 53, 47, 30, 62, 7, 45, 59, 6, 11, 42, 8, 56, 18, 55, 32, 31, 52, 57, 14, 1, 26, 0, 38, 43, 64, 23, 16, 19, 24, 50, 2, 58, 39, 65, 40, 17], 'cur_cost': 110871.0}}, {'individual': 15, 'strategy': 'explore', 'path_data': {'new_tour': [0, 22, 7, 20, 21, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14787.0}}, {'individual': 16, 'strategy': 'exploit', 'path_data': {'new_tour': array([45, 25, 36,  6, 23, 58, 40, 35, 17, 47, 59, 37, 28, 39, 65, 27,  1,
       41, 31,  3, 49, 61, 55, 14, 44, 50, 63, 53,  8, 22, 51, 54, 20, 18,
       46,  2, 21, 11, 30, 56, 52, 16, 15, 60,  9, 33, 38, 48,  5,  4,  7,
       19, 57, 43, 26, 42, 10, 34, 24, 13, 62, 32, 64,  0, 12, 29],
      dtype=int64), 'cur_cost': 110306.0}}, {'individual': 17, 'strategy': 'explore', 'path_data': {'new_tour': [12, 5, 20, 14, 17, 56, 58, 29, 13, 4, 2, 22, 57, 48, 49, 15, 27, 46, 28, 63, 43, 51, 3, 25, 30, 59, 62, 47, 16, 50, 41, 52, 9, 31, 10, 7, 65, 6, 37, 32, 38, 21, 18, 19, 40, 55, 11, 53, 35, 26, 33, 24, 42, 54, 36, 8, 45, 23, 60, 44, 61, 39, 0, 34, 64, 1], 'cur_cost': 101023.0}}, {'individual': 18, 'strategy': 'explore', 'path_data': {'new_tour': [11, 54, 58, 63, 22, 35, 34, 27, 15, 7, 10, 3, 60, 5, 65, 12, 20, 36, 28, 24, 0, 14, 25, 13, 16, 21, 40, 26, 43, 23, 1, 4, 56, 8, 2, 19, 6, 62, 57, 59, 18, 30, 42, 51, 39, 17, 47, 44, 49, 48, 38, 33, 37, 9, 61, 52, 46, 45, 31, 29, 41, 50, 32, 55, 53, 64], 'cur_cost': 60414.0}}, {'individual': 19, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 5, 13, 30, 28, 51, 26, 45, 59, 27, 14,  7, 44, 53, 10,  2, 11, 65,
       38,  1, 23, 16, 17, 50, 24,  9, 64, 36, 48, 57, 42, 12, 20, 46,  6,
       37, 60, 18, 49, 25, 19, 61, 39, 52, 43, 55, 63, 15, 35, 47, 62,  8,
       40, 54, 33,  4, 22, 56, 31, 41, 34,  0, 32, 58, 21, 29,  3],
      dtype=int64), 'cur_cost': 120690.0}}]
2025-08-03 16:15:34,887 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-03 16:15:34,887 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:15:34,898 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=12337.000, 多样性=0.951
2025-08-03 16:15:34,898 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-03 16:15:34,898 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-03 16:15:34,898 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-03 16:15:34,900 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 1, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.07918537802144252, 'best_improvement': -0.23567708333333334}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.011519018811635159}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 10, 'new_count': 10, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.908080808080808, 'new_diversity': 0.908080808080808, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-03 16:15:34,901 - main - INFO - --- Finished Evolution Iteration 1 ---
2025-08-03 16:15:34,905 - main - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_solution.json
2025-08-03 16:15:34,905 - main - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_20250803_161534.solution
2025-08-03 16:15:34,905 - main - INFO - 实例 composite13_66 处理完成
