2025-08-05 09:35:53,988 - __main__ - INFO - simple1_9 开始进化第 1 代
2025-08-05 09:35:53,988 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 09:35:53,990 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:35:53,992 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=681.000, 多样性=0.849
2025-08-05 09:35:53,993 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:35:53,994 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.849
2025-08-05 09:35:53,995 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:35:54,039 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 09:35:54,040 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:35:54,040 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 09:35:54,040 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 09:35:54,428 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -19.660, 聚类评分: 0.000, 覆盖率: 0.001, 收敛趋势: 0.000, 多样性: 0.764
2025-08-05 09:35:54,428 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 09:35:54,429 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 09:35:54,429 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-05 09:35:55,543 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记，坐标系统已统一
2025-08-05 09:36:07,621 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_1_20250805_093607.html
2025-08-05 09:36:07,668 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_1_20250805_093607.html
2025-08-05 09:36:07,669 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-05 09:36:07,669 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 09:36:07,669 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 13.6302秒
2025-08-05 09:36:07,669 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-05 09:36:07,669 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -19.659999999999997, 'local_optima_density': 0.1, 'gradient_variance': 17217.7844, 'cluster_count': 0}, 'population_state': {'diversity': 0.7644444444444445, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.001, 'fitness_entropy': 0.9464119282150146, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -19.660)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.001)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.764)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754357754.4282217, 'performance_metrics': {}}}
2025-08-05 09:36:07,669 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:36:07,669 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:36:07,670 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:36:07,670 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:36:07,670 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:36:07,670 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 09:36:07,670 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:36:07,671 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:36:07,671 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:36:07,671 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:36:07,671 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:36:07,671 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:36:07,671 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 09:36:07,671 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:36:07,671 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:36:07,671 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:07,680 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:36:07,680 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:07,878 - ExplorationExpert - INFO - 探索路径生成完成，成本: 800.0, 路径长度: 9, 收集中间解: 0
2025-08-05 09:36:07,879 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [3, 4, 2, 1, 0, 7, 5, 6, 8], 'cur_cost': 800.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:36:07,879 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 800.00)
2025-08-05 09:36:07,879 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:36:07,879 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:36:07,879 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:07,880 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-05 09:36:07,880 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:07,880 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1092.0, 路径长度: 9, 收集中间解: 0
2025-08-05 09:36:07,880 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [3, 1, 5, 6, 7, 2, 0, 8, 4], 'cur_cost': 1092.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:36:07,880 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 1092.00)
2025-08-05 09:36:07,880 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:36:07,881 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:36:07,881 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:07,881 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 09:36:07,881 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:07,882 - ExplorationExpert - INFO - 探索路径生成完成，成本: 849.0, 路径长度: 9, 收集中间解: 0
2025-08-05 09:36:07,882 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [6, 7, 8, 5, 3, 0, 4, 2, 1], 'cur_cost': 849.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:36:07,882 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 849.00)
2025-08-05 09:36:07,882 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:36:07,882 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:36:07,882 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:07,883 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 09:36:07,883 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:07,883 - ExplorationExpert - INFO - 探索路径生成完成，成本: 951.0, 路径长度: 9, 收集中间解: 0
2025-08-05 09:36:07,883 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [3, 8, 7, 0, 4, 5, 6, 1, 2], 'cur_cost': 951.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:36:07,883 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 951.00)
2025-08-05 09:36:07,883 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:36:07,883 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:36:07,883 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:07,884 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 09:36:07,884 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:07,884 - ExplorationExpert - INFO - 探索路径生成完成，成本: 848.0, 路径长度: 9, 收集中间解: 0
2025-08-05 09:36:07,884 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [5, 6, 0, 1, 7, 8, 3, 4, 2], 'cur_cost': 848.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:36:07,884 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 848.00)
2025-08-05 09:36:07,884 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 09:36:07,885 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:36:07,888 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:36:07,890 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 1070.0
2025-08-05 09:36:10,491 - ExploitationExpert - INFO - res_population_num: 1
2025-08-05 09:36:10,491 - ExploitationExpert - INFO - res_population_costs: [748.0]
2025-08-05 09:36:10,491 - ExploitationExpert - INFO - res_populations: [array([0, 5, 6, 7, 3, 8, 4, 2, 1], dtype=int64)]
2025-08-05 09:36:10,492 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:36:10,492 - ExploitationExpert - INFO - populations: [{'tour': [3, 4, 2, 1, 0, 7, 5, 6, 8], 'cur_cost': 800.0}, {'tour': [3, 1, 5, 6, 7, 2, 0, 8, 4], 'cur_cost': 1092.0}, {'tour': [6, 7, 8, 5, 3, 0, 4, 2, 1], 'cur_cost': 849.0}, {'tour': [3, 8, 7, 0, 4, 5, 6, 1, 2], 'cur_cost': 951.0}, {'tour': [5, 6, 0, 1, 7, 8, 3, 4, 2], 'cur_cost': 848.0}, {'tour': array([5, 4, 1, 6, 3, 8, 0, 7, 2], dtype=int64), 'cur_cost': 1070.0}, {'tour': array([4, 7, 3, 2, 6, 0, 1, 8, 5], dtype=int64), 'cur_cost': 1049.0}, {'tour': array([2, 4, 7, 5, 0, 6, 1, 3, 8], dtype=int64), 'cur_cost': 907.0}, {'tour': array([0, 6, 4, 8, 3, 7, 1, 5, 2], dtype=int64), 'cur_cost': 1070.0}, {'tour': array([7, 4, 2, 0, 5, 8, 6, 1, 3], dtype=int64), 'cur_cost': 987.0}]
2025-08-05 09:36:10,493 - ExploitationExpert - INFO - 局部搜索耗时: 2.60秒，最大迭代次数: 10
2025-08-05 09:36:10,493 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-05 09:36:10,494 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([5, 4, 1, 6, 3, 8, 0, 7, 2], dtype=int64), 'cur_cost': 1070.0, 'intermediate_solutions': [{'tour': array([3, 0, 7, 8, 4, 5, 1, 2, 6], dtype=int64), 'cur_cost': 1124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 3, 0, 7, 4, 5, 1, 2, 6], dtype=int64), 'cur_cost': 1155.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 8, 3, 0, 7, 5, 1, 2, 6], dtype=int64), 'cur_cost': 1094.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 8, 3, 0, 4, 5, 1, 2, 6], dtype=int64), 'cur_cost': 1089.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 4, 8, 3, 0, 5, 1, 2, 6], dtype=int64), 'cur_cost': 1093.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:36:10,494 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1070.00)
2025-08-05 09:36:10,494 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:36:10,494 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:36:10,495 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:10,495 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:36:10,495 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:10,495 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1102.0, 路径长度: 9, 收集中间解: 0
2025-08-05 09:36:10,496 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [4, 6, 2, 0, 3, 7, 5, 8, 1], 'cur_cost': 1102.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:36:10,496 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1102.00)
2025-08-05 09:36:10,496 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:36:10,496 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:36:10,496 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:10,496 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 09:36:10,496 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:10,497 - ExplorationExpert - INFO - 探索路径生成完成，成本: 853.0, 路径长度: 9, 收集中间解: 0
2025-08-05 09:36:10,497 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [4, 0, 6, 5, 3, 8, 7, 2, 1], 'cur_cost': 853.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:36:10,497 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 853.00)
2025-08-05 09:36:10,497 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:36:10,498 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:36:10,498 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:36:10,498 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 1118.0
2025-08-05 09:36:12,492 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 09:36:12,492 - ExploitationExpert - INFO - res_population_costs: [748.0, 680.0]
2025-08-05 09:36:12,493 - ExploitationExpert - INFO - res_populations: [array([0, 5, 6, 7, 3, 8, 4, 2, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-05 09:36:12,493 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:36:12,493 - ExploitationExpert - INFO - populations: [{'tour': [3, 4, 2, 1, 0, 7, 5, 6, 8], 'cur_cost': 800.0}, {'tour': [3, 1, 5, 6, 7, 2, 0, 8, 4], 'cur_cost': 1092.0}, {'tour': [6, 7, 8, 5, 3, 0, 4, 2, 1], 'cur_cost': 849.0}, {'tour': [3, 8, 7, 0, 4, 5, 6, 1, 2], 'cur_cost': 951.0}, {'tour': [5, 6, 0, 1, 7, 8, 3, 4, 2], 'cur_cost': 848.0}, {'tour': array([5, 4, 1, 6, 3, 8, 0, 7, 2], dtype=int64), 'cur_cost': 1070.0}, {'tour': [4, 6, 2, 0, 3, 7, 5, 8, 1], 'cur_cost': 1102.0}, {'tour': [4, 0, 6, 5, 3, 8, 7, 2, 1], 'cur_cost': 853.0}, {'tour': array([2, 7, 1, 5, 0, 4, 8, 6, 3], dtype=int64), 'cur_cost': 1118.0}, {'tour': array([7, 4, 2, 0, 5, 8, 6, 1, 3], dtype=int64), 'cur_cost': 987.0}]
2025-08-05 09:36:12,494 - ExploitationExpert - INFO - 局部搜索耗时: 2.00秒，最大迭代次数: 10
2025-08-05 09:36:12,495 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-05 09:36:12,495 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([2, 7, 1, 5, 0, 4, 8, 6, 3], dtype=int64), 'cur_cost': 1118.0, 'intermediate_solutions': [{'tour': array([4, 6, 0, 8, 3, 7, 1, 5, 2], dtype=int64), 'cur_cost': 1040.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 4, 6, 0, 3, 7, 1, 5, 2], dtype=int64), 'cur_cost': 1089.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 8, 4, 6, 0, 7, 1, 5, 2], dtype=int64), 'cur_cost': 1116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 8, 4, 6, 3, 7, 1, 5, 2], dtype=int64), 'cur_cost': 1132.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 3, 8, 4, 6, 7, 1, 5, 2], dtype=int64), 'cur_cost': 1118.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:36:12,495 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1118.00)
2025-08-05 09:36:12,496 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:36:12,496 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:36:12,496 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:12,496 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:36:12,496 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,497 - ExplorationExpert - INFO - 探索路径生成完成，成本: 976.0, 路径长度: 9, 收集中间解: 0
2025-08-05 09:36:12,497 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [4, 6, 2, 7, 3, 5, 8, 0, 1], 'cur_cost': 976.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:36:12,497 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 976.00)
2025-08-05 09:36:12,497 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:36:12,497 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:36:12,498 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [3, 4, 2, 1, 0, 7, 5, 6, 8], 'cur_cost': 800.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [3, 1, 5, 6, 7, 2, 0, 8, 4], 'cur_cost': 1092.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [6, 7, 8, 5, 3, 0, 4, 2, 1], 'cur_cost': 849.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [3, 8, 7, 0, 4, 5, 6, 1, 2], 'cur_cost': 951.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [5, 6, 0, 1, 7, 8, 3, 4, 2], 'cur_cost': 848.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([5, 4, 1, 6, 3, 8, 0, 7, 2], dtype=int64), 'cur_cost': 1070.0, 'intermediate_solutions': [{'tour': array([3, 0, 7, 8, 4, 5, 1, 2, 6], dtype=int64), 'cur_cost': 1124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 3, 0, 7, 4, 5, 1, 2, 6], dtype=int64), 'cur_cost': 1155.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 8, 3, 0, 7, 5, 1, 2, 6], dtype=int64), 'cur_cost': 1094.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 8, 3, 0, 4, 5, 1, 2, 6], dtype=int64), 'cur_cost': 1089.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 4, 8, 3, 0, 5, 1, 2, 6], dtype=int64), 'cur_cost': 1093.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [4, 6, 2, 0, 3, 7, 5, 8, 1], 'cur_cost': 1102.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [4, 0, 6, 5, 3, 8, 7, 2, 1], 'cur_cost': 853.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 7, 1, 5, 0, 4, 8, 6, 3], dtype=int64), 'cur_cost': 1118.0, 'intermediate_solutions': [{'tour': array([4, 6, 0, 8, 3, 7, 1, 5, 2], dtype=int64), 'cur_cost': 1040.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 4, 6, 0, 3, 7, 1, 5, 2], dtype=int64), 'cur_cost': 1089.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 8, 4, 6, 0, 7, 1, 5, 2], dtype=int64), 'cur_cost': 1116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 8, 4, 6, 3, 7, 1, 5, 2], dtype=int64), 'cur_cost': 1132.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 3, 8, 4, 6, 7, 1, 5, 2], dtype=int64), 'cur_cost': 1118.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [4, 6, 2, 7, 3, 5, 8, 0, 1], 'cur_cost': 976.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 09:36:12,498 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:36:12,498 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:36:12,499 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=800.000, 多样性=0.867
2025-08-05 09:36:12,499 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 09:36:12,499 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 09:36:12,500 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:36:12,500 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.0672722843967131, 'best_improvement': -0.17474302496328928}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.020348837209302445}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.8888888888888888, 'new_diversity': 0.8888888888888888, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 09:36:12,511 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 09:36:12,511 - __main__ - INFO - simple1_9 开始进化第 2 代
2025-08-05 09:36:12,511 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 09:36:12,511 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:36:12,512 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=800.000, 多样性=0.867
2025-08-05 09:36:12,512 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:36:12,513 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.867
2025-08-05 09:36:12,513 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:36:12,513 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.889
2025-08-05 09:36:12,515 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 09:36:12,515 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:36:12,516 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-05 09:36:12,516 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-05 09:36:12,524 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.250, 适应度梯度: 13.717, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 0.654
2025-08-05 09:36:12,524 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 09:36:12,525 - LandscapeExpert - INFO - 提取到 4 个精英解
2025-08-05 09:36:12,525 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-05 09:36:12,528 - visualization.landscape_visualizer - INFO - 插值约束: 152 个点被约束到最小值 680.00
2025-08-05 09:36:12,532 - visualization.landscape_visualizer - INFO - 已添加 4 个精英解标记，坐标系统已统一
2025-08-05 09:36:12,608 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_2_20250805_093612.html
2025-08-05 09:36:12,643 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_2_20250805_093612.html
2025-08-05 09:36:12,643 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 2
2025-08-05 09:36:12,643 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 09:36:12,644 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1295秒
2025-08-05 09:36:12,644 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.25, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 13.716666666666663, 'local_optima_density': 0.25, 'gradient_variance': 31641.536388888886, 'cluster_count': 0}, 'population_state': {'diversity': 0.6540404040404041, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0022, 'fitness_entropy': 0.9353340267248303, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 13.717)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754357772.5241904, 'performance_metrics': {}}}
2025-08-05 09:36:12,644 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:36:12,644 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:36:12,644 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:36:12,644 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:36:12,644 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 09:36:12,645 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 09:36:12,645 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 09:36:12,645 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:36:12,645 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:36:12,645 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 09:36:12,646 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:36:12,646 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:36:12,646 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 4} (总数: 2, 保护比例: 0.20)
2025-08-05 09:36:12,646 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:36:12,646 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:36:12,646 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:12,647 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:36:12,647 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,647 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,647 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,647 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,647 - ExplorationExpert - INFO - 探索路径生成完成，成本: 827.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:36:12,647 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [1, 3, 8, 2, 4, 0, 7, 5, 6], 'cur_cost': 827.0, 'intermediate_solutions': [{'tour': [3, 4, 2, 1, 0, 5, 7, 6, 8], 'cur_cost': 855.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 5, 7, 0, 1, 2, 4, 3, 8], 'cur_cost': 800.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 6, 4, 2, 1, 0, 7, 5, 8], 'cur_cost': 817.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:36:12,647 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 827.00)
2025-08-05 09:36:12,647 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:36:12,647 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:36:12,648 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:12,648 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-05 09:36:12,648 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,648 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,648 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,648 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,649 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1063.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:36:12,649 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [6, 3, 5, 2, 0, 7, 4, 1, 8], 'cur_cost': 1063.0, 'intermediate_solutions': [{'tour': [3, 0, 5, 6, 7, 2, 1, 8, 4], 'cur_cost': 1063.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 1, 5, 6, 7, 2, 0, 4, 8], 'cur_cost': 997.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 1, 5, 6, 7, 2, 0, 4, 8], 'cur_cost': 997.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:36:12,649 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1063.00)
2025-08-05 09:36:12,649 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:36:12,649 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:36:12,649 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:12,650 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 09:36:12,650 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,650 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,650 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,650 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,650 - ExplorationExpert - INFO - 探索路径生成完成，成本: 794.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:36:12,650 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [4, 0, 1, 2, 8, 3, 7, 5, 6], 'cur_cost': 794.0, 'intermediate_solutions': [{'tour': [6, 7, 8, 5, 0, 3, 4, 2, 1], 'cur_cost': 951.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 0, 3, 5, 8, 4, 2, 1], 'cur_cost': 857.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 7, 8, 3, 0, 5, 4, 2, 1], 'cur_cost': 951.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:36:12,650 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 794.00)
2025-08-05 09:36:12,650 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:36:12,651 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:36:12,651 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:12,651 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-05 09:36:12,651 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,651 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,651 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,651 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,651 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1048.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:36:12,652 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [1, 4, 5, 6, 8, 0, 3, 7, 2], 'cur_cost': 1048.0, 'intermediate_solutions': [{'tour': [3, 8, 7, 0, 4, 5, 6, 2, 1], 'cur_cost': 1037.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 7, 1, 6, 5, 4, 0, 2], 'cur_cost': 981.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 4, 8, 7, 0, 5, 6, 1, 2], 'cur_cost': 990.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:36:12,652 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1048.00)
2025-08-05 09:36:12,652 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:36:12,652 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:36:12,652 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:12,653 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 09:36:12,653 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,653 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,653 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,653 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,653 - ExplorationExpert - INFO - 探索路径生成完成，成本: 936.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:36:12,653 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [2, 0, 7, 5, 6, 8, 3, 4, 1], 'cur_cost': 936.0, 'intermediate_solutions': [{'tour': [7, 6, 0, 1, 5, 8, 3, 4, 2], 'cur_cost': 870.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 0, 6, 5, 7, 8, 3, 4, 2], 'cur_cost': 759.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 6, 0, 1, 4, 7, 8, 3, 2], 'cur_cost': 897.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:36:12,654 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 936.00)
2025-08-05 09:36:12,654 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:36:12,654 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:36:12,654 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:12,654 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:36:12,654 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,654 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,654 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,655 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,655 - ExplorationExpert - INFO - 探索路径生成完成，成本: 932.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:36:12,655 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [2, 3, 4, 7, 6, 5, 8, 0, 1], 'cur_cost': 932.0, 'intermediate_solutions': [{'tour': [6, 4, 1, 5, 3, 8, 0, 7, 2], 'cur_cost': 1102.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 6, 1, 4, 5, 8, 0, 7, 2], 'cur_cost': 1067.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 1, 6, 3, 8, 5, 0, 7, 2], 'cur_cost': 900.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:36:12,655 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 932.00)
2025-08-05 09:36:12,655 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:36:12,655 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:36:12,655 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:36:12,655 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 911.0
2025-08-05 09:36:12,760 - ExploitationExpert - INFO - res_population_num: 3
2025-08-05 09:36:12,761 - ExploitationExpert - INFO - res_population_costs: [680.0, 748.0, 680]
2025-08-05 09:36:12,761 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 5, 6, 7, 3, 8, 4, 2, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-05 09:36:12,762 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:36:12,762 - ExploitationExpert - INFO - populations: [{'tour': [1, 3, 8, 2, 4, 0, 7, 5, 6], 'cur_cost': 827.0}, {'tour': [6, 3, 5, 2, 0, 7, 4, 1, 8], 'cur_cost': 1063.0}, {'tour': [4, 0, 1, 2, 8, 3, 7, 5, 6], 'cur_cost': 794.0}, {'tour': [1, 4, 5, 6, 8, 0, 3, 7, 2], 'cur_cost': 1048.0}, {'tour': [2, 0, 7, 5, 6, 8, 3, 4, 1], 'cur_cost': 936.0}, {'tour': [2, 3, 4, 7, 6, 5, 8, 0, 1], 'cur_cost': 932.0}, {'tour': array([7, 3, 6, 4, 8, 2, 0, 1, 5], dtype=int64), 'cur_cost': 911.0}, {'tour': [4, 0, 6, 5, 3, 8, 7, 2, 1], 'cur_cost': 853.0}, {'tour': [2, 7, 1, 5, 0, 4, 8, 6, 3], 'cur_cost': 1118.0}, {'tour': [4, 6, 2, 7, 3, 5, 8, 0, 1], 'cur_cost': 976.0}]
2025-08-05 09:36:12,762 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒，最大迭代次数: 10
2025-08-05 09:36:12,762 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-05 09:36:12,763 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([7, 3, 6, 4, 8, 2, 0, 1, 5], dtype=int64), 'cur_cost': 911.0, 'intermediate_solutions': [{'tour': array([2, 6, 4, 0, 3, 7, 5, 8, 1]), 'cur_cost': 1091.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 2, 6, 4, 3, 7, 5, 8, 1]), 'cur_cost': 1035.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 0, 2, 6, 4, 7, 5, 8, 1]), 'cur_cost': 1194.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 0, 2, 6, 3, 7, 5, 8, 1]), 'cur_cost': 1014.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 3, 0, 2, 6, 7, 5, 8, 1]), 'cur_cost': 1095.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:36:12,764 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 911.00)
2025-08-05 09:36:12,764 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:36:12,764 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:36:12,764 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:12,764 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:36:12,765 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,765 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,765 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,765 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,766 - ExplorationExpert - INFO - 探索路径生成完成，成本: 903.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:36:12,766 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [1, 8, 3, 0, 6, 5, 7, 4, 2], 'cur_cost': 903.0, 'intermediate_solutions': [{'tour': [4, 7, 6, 5, 3, 8, 0, 2, 1], 'cur_cost': 894.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 1, 2, 7, 8, 3, 5, 6, 0], 'cur_cost': 853.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 6, 3, 8, 7, 2, 5, 1], 'cur_cost': 1022.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:36:12,766 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 903.00)
2025-08-05 09:36:12,766 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:36:12,766 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:36:12,767 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:36:12,767 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 792.0
2025-08-05 09:36:12,772 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:36:12,772 - ExploitationExpert - INFO - res_population_costs: [680.0, 748.0, 680, 680.0]
2025-08-05 09:36:12,772 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 5, 6, 7, 3, 8, 4, 2, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-05 09:36:12,773 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:36:12,773 - ExploitationExpert - INFO - populations: [{'tour': [1, 3, 8, 2, 4, 0, 7, 5, 6], 'cur_cost': 827.0}, {'tour': [6, 3, 5, 2, 0, 7, 4, 1, 8], 'cur_cost': 1063.0}, {'tour': [4, 0, 1, 2, 8, 3, 7, 5, 6], 'cur_cost': 794.0}, {'tour': [1, 4, 5, 6, 8, 0, 3, 7, 2], 'cur_cost': 1048.0}, {'tour': [2, 0, 7, 5, 6, 8, 3, 4, 1], 'cur_cost': 936.0}, {'tour': [2, 3, 4, 7, 6, 5, 8, 0, 1], 'cur_cost': 932.0}, {'tour': array([7, 3, 6, 4, 8, 2, 0, 1, 5], dtype=int64), 'cur_cost': 911.0}, {'tour': [1, 8, 3, 0, 6, 5, 7, 4, 2], 'cur_cost': 903.0}, {'tour': array([6, 3, 4, 2, 8, 5, 7, 0, 1], dtype=int64), 'cur_cost': 792.0}, {'tour': [4, 6, 2, 7, 3, 5, 8, 0, 1], 'cur_cost': 976.0}]
2025-08-05 09:36:12,774 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:36:12,774 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-05 09:36:12,775 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([6, 3, 4, 2, 8, 5, 7, 0, 1], dtype=int64), 'cur_cost': 792.0, 'intermediate_solutions': [{'tour': array([1, 7, 2, 5, 0, 4, 8, 6, 3]), 'cur_cost': 1141.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 1, 7, 2, 0, 4, 8, 6, 3]), 'cur_cost': 1030.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 5, 1, 7, 2, 4, 8, 6, 3]), 'cur_cost': 1063.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 5, 1, 7, 0, 4, 8, 6, 3]), 'cur_cost': 1117.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 0, 5, 1, 7, 4, 8, 6, 3]), 'cur_cost': 1124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:36:12,775 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 792.00)
2025-08-05 09:36:12,775 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:36:12,775 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:36:12,775 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:12,775 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:36:12,776 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,776 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,776 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,776 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,776 - ExplorationExpert - INFO - 探索路径生成完成，成本: 909.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:36:12,776 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 5, 1, 6, 7, 3, 8, 4, 2], 'cur_cost': 909.0, 'intermediate_solutions': [{'tour': [4, 5, 2, 7, 3, 6, 8, 0, 1], 'cur_cost': 1032.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 6, 2, 7, 3, 0, 8, 5, 1], 'cur_cost': 1161.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 6, 2, 3, 7, 5, 8, 0, 1], 'cur_cost': 1004.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:36:12,776 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 909.00)
2025-08-05 09:36:12,776 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:36:12,777 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:36:12,777 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [1, 3, 8, 2, 4, 0, 7, 5, 6], 'cur_cost': 827.0, 'intermediate_solutions': [{'tour': [3, 4, 2, 1, 0, 5, 7, 6, 8], 'cur_cost': 855.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 5, 7, 0, 1, 2, 4, 3, 8], 'cur_cost': 800.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 6, 4, 2, 1, 0, 7, 5, 8], 'cur_cost': 817.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [6, 3, 5, 2, 0, 7, 4, 1, 8], 'cur_cost': 1063.0, 'intermediate_solutions': [{'tour': [3, 0, 5, 6, 7, 2, 1, 8, 4], 'cur_cost': 1063.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 1, 5, 6, 7, 2, 0, 4, 8], 'cur_cost': 997.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 1, 5, 6, 7, 2, 0, 4, 8], 'cur_cost': 997.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [4, 0, 1, 2, 8, 3, 7, 5, 6], 'cur_cost': 794.0, 'intermediate_solutions': [{'tour': [6, 7, 8, 5, 0, 3, 4, 2, 1], 'cur_cost': 951.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 0, 3, 5, 8, 4, 2, 1], 'cur_cost': 857.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 7, 8, 3, 0, 5, 4, 2, 1], 'cur_cost': 951.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 5, 6, 8, 0, 3, 7, 2], 'cur_cost': 1048.0, 'intermediate_solutions': [{'tour': [3, 8, 7, 0, 4, 5, 6, 2, 1], 'cur_cost': 1037.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 7, 1, 6, 5, 4, 0, 2], 'cur_cost': 981.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 4, 8, 7, 0, 5, 6, 1, 2], 'cur_cost': 990.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [2, 0, 7, 5, 6, 8, 3, 4, 1], 'cur_cost': 936.0, 'intermediate_solutions': [{'tour': [7, 6, 0, 1, 5, 8, 3, 4, 2], 'cur_cost': 870.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 0, 6, 5, 7, 8, 3, 4, 2], 'cur_cost': 759.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 6, 0, 1, 4, 7, 8, 3, 2], 'cur_cost': 897.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [2, 3, 4, 7, 6, 5, 8, 0, 1], 'cur_cost': 932.0, 'intermediate_solutions': [{'tour': [6, 4, 1, 5, 3, 8, 0, 7, 2], 'cur_cost': 1102.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 6, 1, 4, 5, 8, 0, 7, 2], 'cur_cost': 1067.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 1, 6, 3, 8, 5, 0, 7, 2], 'cur_cost': 900.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 3, 6, 4, 8, 2, 0, 1, 5], dtype=int64), 'cur_cost': 911.0, 'intermediate_solutions': [{'tour': array([2, 6, 4, 0, 3, 7, 5, 8, 1]), 'cur_cost': 1091.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 2, 6, 4, 3, 7, 5, 8, 1]), 'cur_cost': 1035.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 0, 2, 6, 4, 7, 5, 8, 1]), 'cur_cost': 1194.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 0, 2, 6, 3, 7, 5, 8, 1]), 'cur_cost': 1014.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 3, 0, 2, 6, 7, 5, 8, 1]), 'cur_cost': 1095.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [1, 8, 3, 0, 6, 5, 7, 4, 2], 'cur_cost': 903.0, 'intermediate_solutions': [{'tour': [4, 7, 6, 5, 3, 8, 0, 2, 1], 'cur_cost': 894.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 1, 2, 7, 8, 3, 5, 6, 0], 'cur_cost': 853.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 6, 3, 8, 7, 2, 5, 1], 'cur_cost': 1022.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 3, 4, 2, 8, 5, 7, 0, 1], dtype=int64), 'cur_cost': 792.0, 'intermediate_solutions': [{'tour': array([1, 7, 2, 5, 0, 4, 8, 6, 3]), 'cur_cost': 1141.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 1, 7, 2, 0, 4, 8, 6, 3]), 'cur_cost': 1030.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 5, 1, 7, 2, 4, 8, 6, 3]), 'cur_cost': 1063.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 5, 1, 7, 0, 4, 8, 6, 3]), 'cur_cost': 1117.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 0, 5, 1, 7, 4, 8, 6, 3]), 'cur_cost': 1124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 1, 6, 7, 3, 8, 4, 2], 'cur_cost': 909.0, 'intermediate_solutions': [{'tour': [4, 5, 2, 7, 3, 6, 8, 0, 1], 'cur_cost': 1032.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 6, 2, 7, 3, 0, 8, 5, 1], 'cur_cost': 1161.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 6, 2, 3, 7, 5, 8, 0, 1], 'cur_cost': 1004.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:36:12,778 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:36:12,778 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:36:12,779 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=792.000, 多样性=0.849
2025-08-05 09:36:12,779 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 09:36:12,779 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 09:36:12,779 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:36:12,779 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.03850517371034445, 'best_improvement': 0.01}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.019943019943020186}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.7037037037037037, 'new_diversity': 0.7037037037037037, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 09:36:12,780 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 09:36:12,780 - __main__ - INFO - simple1_9 开始进化第 3 代
2025-08-05 09:36:12,780 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 09:36:12,780 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:36:12,781 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=792.000, 多样性=0.849
2025-08-05 09:36:12,781 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:36:12,781 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.849
2025-08-05 09:36:12,781 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:36:12,782 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.704
2025-08-05 09:36:12,784 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 09:36:12,784 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:36:12,784 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 09:36:12,784 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 09:36:12,792 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.214, 适应度梯度: -7.786, 聚类评分: 0.000, 覆盖率: 0.003, 收敛趋势: 0.000, 多样性: 0.546
2025-08-05 09:36:12,792 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 09:36:12,792 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:36:12,793 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-05 09:36:12,797 - visualization.landscape_visualizer - INFO - 插值约束: 86 个点被约束到最小值 680.00
2025-08-05 09:36:12,801 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-05 09:36:12,876 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_3_20250805_093612.html
2025-08-05 09:36:12,935 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_3_20250805_093612.html
2025-08-05 09:36:12,935 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 3
2025-08-05 09:36:12,935 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 09:36:12,935 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1501秒
2025-08-05 09:36:12,935 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.21428571428571427, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -7.785714285714278, 'local_optima_density': 0.21428571428571427, 'gradient_variance': 12842.596938775509, 'cluster_count': 0}, 'population_state': {'diversity': 0.5463108320251178, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0034, 'fitness_entropy': 0.9615862351816215, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -7.786)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.003)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754357772.7929485, 'performance_metrics': {}}}
2025-08-05 09:36:12,935 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:36:12,935 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:36:12,936 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:36:12,936 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:36:12,936 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-05 09:36:12,936 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 09:36:12,936 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-05 09:36:12,936 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:36:12,936 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:36:12,937 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-05 09:36:12,937 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:36:12,937 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:36:12,937 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 09:36:12,937 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:36:12,937 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:36:12,937 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:12,938 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 09:36:12,938 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,938 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,938 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,938 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,938 - ExplorationExpert - INFO - 探索路径生成完成，成本: 814.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:36:12,938 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [2, 8, 7, 5, 6, 3, 4, 0, 1], 'cur_cost': 814.0, 'intermediate_solutions': [{'tour': [7, 3, 8, 2, 4, 0, 1, 5, 6], 'cur_cost': 738.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 5, 7, 0, 4, 2, 8, 3, 1], 'cur_cost': 827.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 1, 3, 8, 2, 4, 0, 7, 5], 'cur_cost': 827.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:36:12,939 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 814.00)
2025-08-05 09:36:12,939 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 09:36:12,939 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:36:12,939 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:36:12,939 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 912.0
2025-08-05 09:36:12,943 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:36:12,944 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0, 748.0]
2025-08-05 09:36:12,944 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 5, 6, 7, 3, 8, 4, 2, 1], dtype=int64)]
2025-08-05 09:36:12,945 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:36:12,945 - ExploitationExpert - INFO - populations: [{'tour': [2, 8, 7, 5, 6, 3, 4, 0, 1], 'cur_cost': 814.0}, {'tour': array([3, 7, 6, 2, 4, 8, 5, 1, 0], dtype=int64), 'cur_cost': 912.0}, {'tour': [4, 0, 1, 2, 8, 3, 7, 5, 6], 'cur_cost': 794.0}, {'tour': [1, 4, 5, 6, 8, 0, 3, 7, 2], 'cur_cost': 1048.0}, {'tour': [2, 0, 7, 5, 6, 8, 3, 4, 1], 'cur_cost': 936.0}, {'tour': [2, 3, 4, 7, 6, 5, 8, 0, 1], 'cur_cost': 932.0}, {'tour': [7, 3, 6, 4, 8, 2, 0, 1, 5], 'cur_cost': 911.0}, {'tour': [1, 8, 3, 0, 6, 5, 7, 4, 2], 'cur_cost': 903.0}, {'tour': [6, 3, 4, 2, 8, 5, 7, 0, 1], 'cur_cost': 792.0}, {'tour': [0, 5, 1, 6, 7, 3, 8, 4, 2], 'cur_cost': 909.0}]
2025-08-05 09:36:12,946 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:36:12,946 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-05 09:36:12,946 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([3, 7, 6, 2, 4, 8, 5, 1, 0], dtype=int64), 'cur_cost': 912.0, 'intermediate_solutions': [{'tour': array([5, 3, 6, 2, 0, 7, 4, 1, 8]), 'cur_cost': 1038.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 5, 3, 6, 0, 7, 4, 1, 8]), 'cur_cost': 993.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 2, 5, 3, 6, 7, 4, 1, 8]), 'cur_cost': 1057.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 2, 5, 3, 0, 7, 4, 1, 8]), 'cur_cost': 1149.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 0, 2, 5, 3, 7, 4, 1, 8]), 'cur_cost': 1036.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:36:12,946 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 912.00)
2025-08-05 09:36:12,946 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:36:12,947 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:36:12,947 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:12,947 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:36:12,947 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,947 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,947 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,948 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,948 - ExplorationExpert - INFO - 探索路径生成完成，成本: 954.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:36:12,948 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 5, 2, 6, 7, 3, 8, 4, 1], 'cur_cost': 954.0, 'intermediate_solutions': [{'tour': [4, 3, 1, 2, 8, 0, 7, 5, 6], 'cur_cost': 1035.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 0, 1, 8, 2, 3, 7, 5, 6], 'cur_cost': 903.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 1, 2, 8, 3, 7, 5, 6], 'cur_cost': 794.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:36:12,948 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 954.00)
2025-08-05 09:36:12,948 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:36:12,948 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:36:12,948 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:36:12,948 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 985.0
2025-08-05 09:36:12,954 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:36:12,954 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0, 748.0]
2025-08-05 09:36:12,954 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 5, 6, 7, 3, 8, 4, 2, 1], dtype=int64)]
2025-08-05 09:36:12,955 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:36:12,955 - ExploitationExpert - INFO - populations: [{'tour': [2, 8, 7, 5, 6, 3, 4, 0, 1], 'cur_cost': 814.0}, {'tour': array([3, 7, 6, 2, 4, 8, 5, 1, 0], dtype=int64), 'cur_cost': 912.0}, {'tour': [0, 5, 2, 6, 7, 3, 8, 4, 1], 'cur_cost': 954.0}, {'tour': array([3, 0, 2, 4, 6, 1, 5, 8, 7], dtype=int64), 'cur_cost': 985.0}, {'tour': [2, 0, 7, 5, 6, 8, 3, 4, 1], 'cur_cost': 936.0}, {'tour': [2, 3, 4, 7, 6, 5, 8, 0, 1], 'cur_cost': 932.0}, {'tour': [7, 3, 6, 4, 8, 2, 0, 1, 5], 'cur_cost': 911.0}, {'tour': [1, 8, 3, 0, 6, 5, 7, 4, 2], 'cur_cost': 903.0}, {'tour': [6, 3, 4, 2, 8, 5, 7, 0, 1], 'cur_cost': 792.0}, {'tour': [0, 5, 1, 6, 7, 3, 8, 4, 2], 'cur_cost': 909.0}]
2025-08-05 09:36:12,956 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:36:12,956 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-05 09:36:12,956 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([3, 0, 2, 4, 6, 1, 5, 8, 7], dtype=int64), 'cur_cost': 985.0, 'intermediate_solutions': [{'tour': array([5, 4, 1, 6, 8, 0, 3, 7, 2]), 'cur_cost': 1132.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 5, 4, 1, 8, 0, 3, 7, 2]), 'cur_cost': 1135.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 6, 5, 4, 1, 0, 3, 7, 2]), 'cur_cost': 916.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 6, 5, 4, 8, 0, 3, 7, 2]), 'cur_cost': 1015.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 8, 6, 5, 4, 0, 3, 7, 2]), 'cur_cost': 1065.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:36:12,957 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 985.00)
2025-08-05 09:36:12,957 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 09:36:12,957 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:36:12,957 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:36:12,957 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 1037.0
2025-08-05 09:36:12,962 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:36:12,962 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0, 748.0]
2025-08-05 09:36:12,962 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 5, 6, 7, 3, 8, 4, 2, 1], dtype=int64)]
2025-08-05 09:36:12,963 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:36:12,963 - ExploitationExpert - INFO - populations: [{'tour': [2, 8, 7, 5, 6, 3, 4, 0, 1], 'cur_cost': 814.0}, {'tour': array([3, 7, 6, 2, 4, 8, 5, 1, 0], dtype=int64), 'cur_cost': 912.0}, {'tour': [0, 5, 2, 6, 7, 3, 8, 4, 1], 'cur_cost': 954.0}, {'tour': array([3, 0, 2, 4, 6, 1, 5, 8, 7], dtype=int64), 'cur_cost': 985.0}, {'tour': array([8, 5, 7, 0, 3, 2, 6, 1, 4], dtype=int64), 'cur_cost': 1037.0}, {'tour': [2, 3, 4, 7, 6, 5, 8, 0, 1], 'cur_cost': 932.0}, {'tour': [7, 3, 6, 4, 8, 2, 0, 1, 5], 'cur_cost': 911.0}, {'tour': [1, 8, 3, 0, 6, 5, 7, 4, 2], 'cur_cost': 903.0}, {'tour': [6, 3, 4, 2, 8, 5, 7, 0, 1], 'cur_cost': 792.0}, {'tour': [0, 5, 1, 6, 7, 3, 8, 4, 2], 'cur_cost': 909.0}]
2025-08-05 09:36:12,964 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:36:12,964 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-05 09:36:12,965 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([8, 5, 7, 0, 3, 2, 6, 1, 4], dtype=int64), 'cur_cost': 1037.0, 'intermediate_solutions': [{'tour': array([7, 0, 2, 5, 6, 8, 3, 4, 1]), 'cur_cost': 1025.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 7, 0, 2, 6, 8, 3, 4, 1]), 'cur_cost': 1083.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 5, 7, 0, 2, 8, 3, 4, 1]), 'cur_cost': 877.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 5, 7, 0, 6, 8, 3, 4, 1]), 'cur_cost': 1009.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 6, 5, 7, 0, 8, 3, 4, 1]), 'cur_cost': 995.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:36:12,965 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1037.00)
2025-08-05 09:36:12,965 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:36:12,965 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:36:12,965 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:12,966 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:36:12,966 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,966 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,966 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,966 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,966 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1114.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:36:12,966 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [3, 5, 2, 7, 0, 8, 4, 6, 1], 'cur_cost': 1114.0, 'intermediate_solutions': [{'tour': [2, 3, 4, 7, 5, 6, 8, 0, 1], 'cur_cost': 963.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 3, 5, 6, 7, 4, 8, 0, 1], 'cur_cost': 869.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 3, 4, 6, 5, 7, 8, 0, 1], 'cur_cost': 964.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:36:12,966 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1114.00)
2025-08-05 09:36:12,967 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:36:12,967 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:36:12,967 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:12,967 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:36:12,967 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,967 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,967 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,968 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,968 - ExplorationExpert - INFO - 探索路径生成完成，成本: 915.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:36:12,968 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [1, 8, 2, 3, 7, 5, 6, 0, 4], 'cur_cost': 915.0, 'intermediate_solutions': [{'tour': [7, 3, 6, 0, 8, 2, 4, 1, 5], 'cur_cost': 893.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 6, 4, 8, 2, 1, 0, 5], 'cur_cost': 884.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 3, 5, 6, 4, 8, 2, 0, 1], 'cur_cost': 831.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:36:12,968 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 915.00)
2025-08-05 09:36:12,968 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:36:12,968 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:36:12,968 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:12,969 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 09:36:12,969 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,969 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,969 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,969 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,970 - ExplorationExpert - INFO - 探索路径生成完成，成本: 718.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:36:12,970 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [6, 3, 7, 0, 1, 4, 2, 8, 5], 'cur_cost': 718.0, 'intermediate_solutions': [{'tour': [1, 8, 3, 0, 6, 5, 7, 2, 4], 'cur_cost': 908.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 3, 0, 6, 2, 4, 7, 5], 'cur_cost': 1050.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 3, 8, 0, 6, 5, 7, 4, 2], 'cur_cost': 905.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:36:12,970 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 718.00)
2025-08-05 09:36:12,970 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:36:12,970 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:36:12,970 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:12,970 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:36:12,971 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,971 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,971 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,971 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,971 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1018.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:36:12,971 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [1, 3, 0, 6, 8, 7, 5, 4, 2], 'cur_cost': 1018.0, 'intermediate_solutions': [{'tour': [1, 3, 4, 2, 8, 5, 7, 0, 6], 'cur_cost': 911.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 5, 8, 2, 4, 3, 7, 0, 1], 'cur_cost': 751.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 3, 4, 2, 8, 7, 5, 0, 1], 'cur_cost': 823.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:36:12,971 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 1018.00)
2025-08-05 09:36:12,971 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:36:12,972 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:36:12,972 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:12,972 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-05 09:36:12,972 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,972 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,973 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,973 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:12,973 - ExplorationExpert - INFO - 探索路径生成完成，成本: 980.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:36:12,973 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [5, 2, 0, 3, 8, 4, 1, 7, 6], 'cur_cost': 980.0, 'intermediate_solutions': [{'tour': [0, 5, 1, 4, 7, 3, 8, 6, 2], 'cur_cost': 1089.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 1, 4, 8, 3, 7, 6, 2], 'cur_cost': 1031.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 1, 6, 7, 3, 0, 8, 4, 2], 'cur_cost': 1010.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:36:12,973 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 980.00)
2025-08-05 09:36:12,973 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:36:12,973 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:36:12,974 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [2, 8, 7, 5, 6, 3, 4, 0, 1], 'cur_cost': 814.0, 'intermediate_solutions': [{'tour': [7, 3, 8, 2, 4, 0, 1, 5, 6], 'cur_cost': 738.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 5, 7, 0, 4, 2, 8, 3, 1], 'cur_cost': 827.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 1, 3, 8, 2, 4, 0, 7, 5], 'cur_cost': 827.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 7, 6, 2, 4, 8, 5, 1, 0], dtype=int64), 'cur_cost': 912.0, 'intermediate_solutions': [{'tour': array([5, 3, 6, 2, 0, 7, 4, 1, 8]), 'cur_cost': 1038.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 5, 3, 6, 0, 7, 4, 1, 8]), 'cur_cost': 993.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 2, 5, 3, 6, 7, 4, 1, 8]), 'cur_cost': 1057.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 2, 5, 3, 0, 7, 4, 1, 8]), 'cur_cost': 1149.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 0, 2, 5, 3, 7, 4, 1, 8]), 'cur_cost': 1036.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 2, 6, 7, 3, 8, 4, 1], 'cur_cost': 954.0, 'intermediate_solutions': [{'tour': [4, 3, 1, 2, 8, 0, 7, 5, 6], 'cur_cost': 1035.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 0, 1, 8, 2, 3, 7, 5, 6], 'cur_cost': 903.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 1, 2, 8, 3, 7, 5, 6], 'cur_cost': 794.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 0, 2, 4, 6, 1, 5, 8, 7], dtype=int64), 'cur_cost': 985.0, 'intermediate_solutions': [{'tour': array([5, 4, 1, 6, 8, 0, 3, 7, 2]), 'cur_cost': 1132.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 5, 4, 1, 8, 0, 3, 7, 2]), 'cur_cost': 1135.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 6, 5, 4, 1, 0, 3, 7, 2]), 'cur_cost': 916.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 6, 5, 4, 8, 0, 3, 7, 2]), 'cur_cost': 1015.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 8, 6, 5, 4, 0, 3, 7, 2]), 'cur_cost': 1065.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 5, 7, 0, 3, 2, 6, 1, 4], dtype=int64), 'cur_cost': 1037.0, 'intermediate_solutions': [{'tour': array([7, 0, 2, 5, 6, 8, 3, 4, 1]), 'cur_cost': 1025.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 7, 0, 2, 6, 8, 3, 4, 1]), 'cur_cost': 1083.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 5, 7, 0, 2, 8, 3, 4, 1]), 'cur_cost': 877.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 5, 7, 0, 6, 8, 3, 4, 1]), 'cur_cost': 1009.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 6, 5, 7, 0, 8, 3, 4, 1]), 'cur_cost': 995.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [3, 5, 2, 7, 0, 8, 4, 6, 1], 'cur_cost': 1114.0, 'intermediate_solutions': [{'tour': [2, 3, 4, 7, 5, 6, 8, 0, 1], 'cur_cost': 963.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 3, 5, 6, 7, 4, 8, 0, 1], 'cur_cost': 869.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 3, 4, 6, 5, 7, 8, 0, 1], 'cur_cost': 964.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [1, 8, 2, 3, 7, 5, 6, 0, 4], 'cur_cost': 915.0, 'intermediate_solutions': [{'tour': [7, 3, 6, 0, 8, 2, 4, 1, 5], 'cur_cost': 893.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 6, 4, 8, 2, 1, 0, 5], 'cur_cost': 884.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 3, 5, 6, 4, 8, 2, 0, 1], 'cur_cost': 831.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [6, 3, 7, 0, 1, 4, 2, 8, 5], 'cur_cost': 718.0, 'intermediate_solutions': [{'tour': [1, 8, 3, 0, 6, 5, 7, 2, 4], 'cur_cost': 908.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 3, 0, 6, 2, 4, 7, 5], 'cur_cost': 1050.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 3, 8, 0, 6, 5, 7, 4, 2], 'cur_cost': 905.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [1, 3, 0, 6, 8, 7, 5, 4, 2], 'cur_cost': 1018.0, 'intermediate_solutions': [{'tour': [1, 3, 4, 2, 8, 5, 7, 0, 6], 'cur_cost': 911.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 5, 8, 2, 4, 3, 7, 0, 1], 'cur_cost': 751.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 3, 4, 2, 8, 7, 5, 0, 1], 'cur_cost': 823.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [5, 2, 0, 3, 8, 4, 1, 7, 6], 'cur_cost': 980.0, 'intermediate_solutions': [{'tour': [0, 5, 1, 4, 7, 3, 8, 6, 2], 'cur_cost': 1089.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 1, 4, 8, 3, 7, 6, 2], 'cur_cost': 1031.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 1, 6, 7, 3, 0, 8, 4, 2], 'cur_cost': 1010.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:36:12,975 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:36:12,975 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:36:12,976 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=718.000, 多样性=0.899
2025-08-05 09:36:12,976 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 09:36:12,976 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 09:36:12,976 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:36:12,976 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 80, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'slight_improvement', 'improvement_rate': 0.003011147753668957, 'best_improvement': 0.09343434343434344}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.05813953488372107}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.7037037037037037, 'new_diversity': 0.7037037037037037, 'diversity_change': 0.0}, 'suggestions': ['改进缓慢，建议调整策略平衡或增加扰动强度', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:36:12,977 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 09:36:12,977 - __main__ - INFO - simple1_9 开始进化第 4 代
2025-08-05 09:36:12,977 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 09:36:12,977 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:36:12,977 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=718.000, 多样性=0.899
2025-08-05 09:36:12,978 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:36:12,978 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.899
2025-08-05 09:36:12,978 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:36:12,979 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.704
2025-08-05 09:36:12,981 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 09:36:12,981 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:36:12,981 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 09:36:12,981 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 09:36:12,989 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.286, 适应度梯度: 11.757, 聚类评分: 0.000, 覆盖率: 0.004, 收敛趋势: 0.000, 多样性: 0.568
2025-08-05 09:36:12,989 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 09:36:12,989 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:36:12,989 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-05 09:36:12,993 - visualization.landscape_visualizer - INFO - 插值约束: 59 个点被约束到最小值 680.00
2025-08-05 09:36:12,997 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-05 09:36:13,074 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_4_20250805_093613.html
2025-08-05 09:36:13,115 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_4_20250805_093613.html
2025-08-05 09:36:13,115 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 4
2025-08-05 09:36:13,115 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 09:36:13,115 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1340秒
2025-08-05 09:36:13,115 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2857142857142857, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 11.757142857142863, 'local_optima_density': 0.2857142857142857, 'gradient_variance': 28528.669591836737, 'cluster_count': 0}, 'population_state': {'diversity': 0.5675039246467818, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0044, 'fitness_entropy': 0.9357849740192012, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.004)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 11.757)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754357772.9890234, 'performance_metrics': {}}}
2025-08-05 09:36:13,115 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:36:13,115 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:36:13,115 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:36:13,115 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:36:13,116 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:36:13,116 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 09:36:13,116 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:36:13,116 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:36:13,116 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:36:13,117 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:36:13,117 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:36:13,117 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:36:13,117 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 09:36:13,117 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:36:13,117 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:36:13,117 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:13,118 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 09:36:13,118 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,118 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,118 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,118 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,118 - ExplorationExpert - INFO - 探索路径生成完成，成本: 863.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:36:13,119 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [3, 6, 7, 8, 4, 0, 1, 2, 5], 'cur_cost': 863.0, 'intermediate_solutions': [{'tour': [4, 8, 7, 5, 6, 3, 2, 0, 1], 'cur_cost': 848.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 7, 5, 6, 3, 1, 0, 4], 'cur_cost': 775.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 8, 7, 5, 3, 4, 6, 0, 1], 'cur_cost': 867.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:36:13,119 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 863.00)
2025-08-05 09:36:13,119 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:36:13,119 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:36:13,119 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:13,119 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:36:13,119 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,119 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,120 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,120 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,120 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1025.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:36:13,120 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [3, 4, 6, 8, 7, 5, 0, 1, 2], 'cur_cost': 1025.0, 'intermediate_solutions': [{'tour': [3, 7, 0, 2, 4, 8, 5, 1, 6], 'cur_cost': 905.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 5, 8, 4, 2, 6, 7, 3], 'cur_cost': 912.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 7, 8, 6, 2, 4, 5, 1, 0], 'cur_cost': 1005.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:36:13,120 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1025.00)
2025-08-05 09:36:13,120 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:36:13,120 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:36:13,120 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:13,120 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 09:36:13,121 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,121 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,121 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,121 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,121 - ExplorationExpert - INFO - 探索路径生成完成，成本: 774.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:36:13,121 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [5, 8, 4, 2, 0, 1, 7, 3, 6], 'cur_cost': 774.0, 'intermediate_solutions': [{'tour': [0, 5, 6, 2, 7, 3, 8, 4, 1], 'cur_cost': 900.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 7, 6, 2, 5, 0, 8, 4, 1], 'cur_cost': 1135.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 2, 6, 7, 3, 8, 4, 1], 'cur_cost': 954.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:36:13,121 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 774.00)
2025-08-05 09:36:13,121 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:36:13,121 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:36:13,121 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:13,122 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 09:36:13,122 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,122 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,123 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,123 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,123 - ExplorationExpert - INFO - 探索路径生成完成，成本: 681.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:36:13,123 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [3, 7, 5, 6, 0, 1, 4, 2, 8], 'cur_cost': 681.0, 'intermediate_solutions': [{'tour': [3, 0, 2, 4, 6, 7, 5, 8, 1], 'cur_cost': 1047.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 0, 2, 7, 8, 5, 1, 6, 4], 'cur_cost': 1121.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 0, 2, 6, 1, 4, 5, 8, 7], 'cur_cost': 1053.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:36:13,123 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 681.00)
2025-08-05 09:36:13,123 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 09:36:13,123 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:36:13,124 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:36:13,124 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 1014.0
2025-08-05 09:36:13,129 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:36:13,129 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0, 748.0]
2025-08-05 09:36:13,129 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 5, 6, 7, 3, 8, 4, 2, 1], dtype=int64)]
2025-08-05 09:36:13,130 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:36:13,130 - ExploitationExpert - INFO - populations: [{'tour': [3, 6, 7, 8, 4, 0, 1, 2, 5], 'cur_cost': 863.0}, {'tour': [3, 4, 6, 8, 7, 5, 0, 1, 2], 'cur_cost': 1025.0}, {'tour': [5, 8, 4, 2, 0, 1, 7, 3, 6], 'cur_cost': 774.0}, {'tour': [3, 7, 5, 6, 0, 1, 4, 2, 8], 'cur_cost': 681.0}, {'tour': array([3, 4, 8, 5, 0, 7, 2, 1, 6], dtype=int64), 'cur_cost': 1014.0}, {'tour': [3, 5, 2, 7, 0, 8, 4, 6, 1], 'cur_cost': 1114.0}, {'tour': [1, 8, 2, 3, 7, 5, 6, 0, 4], 'cur_cost': 915.0}, {'tour': [6, 3, 7, 0, 1, 4, 2, 8, 5], 'cur_cost': 718.0}, {'tour': [1, 3, 0, 6, 8, 7, 5, 4, 2], 'cur_cost': 1018.0}, {'tour': [5, 2, 0, 3, 8, 4, 1, 7, 6], 'cur_cost': 980.0}]
2025-08-05 09:36:13,130 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:36:13,130 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-08-05 09:36:13,131 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([3, 4, 8, 5, 0, 7, 2, 1, 6], dtype=int64), 'cur_cost': 1014.0, 'intermediate_solutions': [{'tour': array([7, 5, 8, 0, 3, 2, 6, 1, 4]), 'cur_cost': 1089.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 7, 5, 8, 3, 2, 6, 1, 4]), 'cur_cost': 981.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 0, 7, 5, 8, 2, 6, 1, 4]), 'cur_cost': 1014.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 0, 7, 5, 3, 2, 6, 1, 4]), 'cur_cost': 1013.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 3, 0, 7, 5, 2, 6, 1, 4]), 'cur_cost': 1040.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:36:13,131 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1014.00)
2025-08-05 09:36:13,131 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 09:36:13,131 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:36:13,131 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:36:13,132 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 1094.0
2025-08-05 09:36:13,137 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:36:13,137 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0, 748.0]
2025-08-05 09:36:13,137 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 5, 6, 7, 3, 8, 4, 2, 1], dtype=int64)]
2025-08-05 09:36:13,139 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:36:13,139 - ExploitationExpert - INFO - populations: [{'tour': [3, 6, 7, 8, 4, 0, 1, 2, 5], 'cur_cost': 863.0}, {'tour': [3, 4, 6, 8, 7, 5, 0, 1, 2], 'cur_cost': 1025.0}, {'tour': [5, 8, 4, 2, 0, 1, 7, 3, 6], 'cur_cost': 774.0}, {'tour': [3, 7, 5, 6, 0, 1, 4, 2, 8], 'cur_cost': 681.0}, {'tour': array([3, 4, 8, 5, 0, 7, 2, 1, 6], dtype=int64), 'cur_cost': 1014.0}, {'tour': array([2, 1, 3, 8, 6, 4, 5, 7, 0], dtype=int64), 'cur_cost': 1094.0}, {'tour': [1, 8, 2, 3, 7, 5, 6, 0, 4], 'cur_cost': 915.0}, {'tour': [6, 3, 7, 0, 1, 4, 2, 8, 5], 'cur_cost': 718.0}, {'tour': [1, 3, 0, 6, 8, 7, 5, 4, 2], 'cur_cost': 1018.0}, {'tour': [5, 2, 0, 3, 8, 4, 1, 7, 6], 'cur_cost': 980.0}]
2025-08-05 09:36:13,140 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:36:13,140 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-08-05 09:36:13,140 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([2, 1, 3, 8, 6, 4, 5, 7, 0], dtype=int64), 'cur_cost': 1094.0, 'intermediate_solutions': [{'tour': array([2, 5, 3, 7, 0, 8, 4, 6, 1]), 'cur_cost': 1017.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 2, 5, 3, 0, 8, 4, 6, 1]), 'cur_cost': 1114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 7, 2, 5, 3, 8, 4, 6, 1]), 'cur_cost': 933.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 7, 2, 5, 0, 8, 4, 6, 1]), 'cur_cost': 1156.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 0, 7, 2, 5, 8, 4, 6, 1]), 'cur_cost': 1138.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:36:13,140 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1094.00)
2025-08-05 09:36:13,140 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:36:13,141 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:36:13,141 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:13,141 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 09:36:13,141 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,141 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,142 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,142 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,142 - ExplorationExpert - INFO - 探索路径生成完成，成本: 810.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:36:13,142 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 6, 3, 8, 7, 5, 4, 2, 1], 'cur_cost': 810.0, 'intermediate_solutions': [{'tour': [1, 8, 2, 3, 7, 4, 6, 0, 5], 'cur_cost': 1119.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 7, 3, 2, 5, 6, 0, 4], 'cur_cost': 1003.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 8, 3, 7, 2, 5, 6, 0, 4], 'cur_cost': 976.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:36:13,142 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 810.00)
2025-08-05 09:36:13,142 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:36:13,142 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:36:13,142 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:13,143 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-05 09:36:13,143 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,143 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,143 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,143 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,143 - ExplorationExpert - INFO - 探索路径生成完成，成本: 978.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:36:13,143 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [7, 4, 0, 1, 3, 8, 6, 5, 2], 'cur_cost': 978.0, 'intermediate_solutions': [{'tour': [6, 8, 7, 0, 1, 4, 2, 3, 5], 'cur_cost': 796.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 3, 7, 0, 8, 2, 4, 1, 5], 'cur_cost': 879.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 3, 7, 1, 4, 2, 8, 0, 5], 'cur_cost': 882.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:36:13,144 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 978.00)
2025-08-05 09:36:13,144 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:36:13,144 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:36:13,144 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:36:13,144 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 1009.0
2025-08-05 09:36:13,149 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:36:13,149 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0, 748.0]
2025-08-05 09:36:13,150 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 5, 6, 7, 3, 8, 4, 2, 1], dtype=int64)]
2025-08-05 09:36:13,150 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:36:13,150 - ExploitationExpert - INFO - populations: [{'tour': [3, 6, 7, 8, 4, 0, 1, 2, 5], 'cur_cost': 863.0}, {'tour': [3, 4, 6, 8, 7, 5, 0, 1, 2], 'cur_cost': 1025.0}, {'tour': [5, 8, 4, 2, 0, 1, 7, 3, 6], 'cur_cost': 774.0}, {'tour': [3, 7, 5, 6, 0, 1, 4, 2, 8], 'cur_cost': 681.0}, {'tour': array([3, 4, 8, 5, 0, 7, 2, 1, 6], dtype=int64), 'cur_cost': 1014.0}, {'tour': array([2, 1, 3, 8, 6, 4, 5, 7, 0], dtype=int64), 'cur_cost': 1094.0}, {'tour': [0, 6, 3, 8, 7, 5, 4, 2, 1], 'cur_cost': 810.0}, {'tour': [7, 4, 0, 1, 3, 8, 6, 5, 2], 'cur_cost': 978.0}, {'tour': array([0, 7, 3, 1, 5, 4, 2, 8, 6], dtype=int64), 'cur_cost': 1009.0}, {'tour': [5, 2, 0, 3, 8, 4, 1, 7, 6], 'cur_cost': 980.0}]
2025-08-05 09:36:13,151 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:36:13,151 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-08-05 09:36:13,152 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([0, 7, 3, 1, 5, 4, 2, 8, 6], dtype=int64), 'cur_cost': 1009.0, 'intermediate_solutions': [{'tour': array([0, 3, 1, 6, 8, 7, 5, 4, 2]), 'cur_cost': 1029.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 0, 3, 1, 8, 7, 5, 4, 2]), 'cur_cost': 1105.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 6, 0, 3, 1, 7, 5, 4, 2]), 'cur_cost': 1019.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 6, 0, 3, 8, 7, 5, 4, 2]), 'cur_cost': 910.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 8, 6, 0, 3, 7, 5, 4, 2]), 'cur_cost': 1003.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:36:13,152 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1009.00)
2025-08-05 09:36:13,152 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:36:13,152 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:36:13,152 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:13,153 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 09:36:13,153 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,153 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,153 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,153 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,154 - ExplorationExpert - INFO - 探索路径生成完成，成本: 884.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:36:13,154 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [6, 3, 7, 4, 2, 0, 1, 5, 8], 'cur_cost': 884.0, 'intermediate_solutions': [{'tour': [5, 2, 0, 4, 8, 3, 1, 7, 6], 'cur_cost': 999.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 3, 0, 2, 5, 1, 7, 6], 'cur_cost': 1118.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 2, 0, 3, 8, 4, 1, 6], 'cur_cost': 921.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:36:13,154 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 884.00)
2025-08-05 09:36:13,154 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:36:13,154 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:36:13,155 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [3, 6, 7, 8, 4, 0, 1, 2, 5], 'cur_cost': 863.0, 'intermediate_solutions': [{'tour': [4, 8, 7, 5, 6, 3, 2, 0, 1], 'cur_cost': 848.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 7, 5, 6, 3, 1, 0, 4], 'cur_cost': 775.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 8, 7, 5, 3, 4, 6, 0, 1], 'cur_cost': 867.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [3, 4, 6, 8, 7, 5, 0, 1, 2], 'cur_cost': 1025.0, 'intermediate_solutions': [{'tour': [3, 7, 0, 2, 4, 8, 5, 1, 6], 'cur_cost': 905.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 5, 8, 4, 2, 6, 7, 3], 'cur_cost': 912.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 7, 8, 6, 2, 4, 5, 1, 0], 'cur_cost': 1005.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [5, 8, 4, 2, 0, 1, 7, 3, 6], 'cur_cost': 774.0, 'intermediate_solutions': [{'tour': [0, 5, 6, 2, 7, 3, 8, 4, 1], 'cur_cost': 900.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 7, 6, 2, 5, 0, 8, 4, 1], 'cur_cost': 1135.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 2, 6, 7, 3, 8, 4, 1], 'cur_cost': 954.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [3, 7, 5, 6, 0, 1, 4, 2, 8], 'cur_cost': 681.0, 'intermediate_solutions': [{'tour': [3, 0, 2, 4, 6, 7, 5, 8, 1], 'cur_cost': 1047.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 0, 2, 7, 8, 5, 1, 6, 4], 'cur_cost': 1121.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 0, 2, 6, 1, 4, 5, 8, 7], 'cur_cost': 1053.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 4, 8, 5, 0, 7, 2, 1, 6], dtype=int64), 'cur_cost': 1014.0, 'intermediate_solutions': [{'tour': array([7, 5, 8, 0, 3, 2, 6, 1, 4]), 'cur_cost': 1089.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 7, 5, 8, 3, 2, 6, 1, 4]), 'cur_cost': 981.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 0, 7, 5, 8, 2, 6, 1, 4]), 'cur_cost': 1014.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 0, 7, 5, 3, 2, 6, 1, 4]), 'cur_cost': 1013.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 3, 0, 7, 5, 2, 6, 1, 4]), 'cur_cost': 1040.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 1, 3, 8, 6, 4, 5, 7, 0], dtype=int64), 'cur_cost': 1094.0, 'intermediate_solutions': [{'tour': array([2, 5, 3, 7, 0, 8, 4, 6, 1]), 'cur_cost': 1017.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 2, 5, 3, 0, 8, 4, 6, 1]), 'cur_cost': 1114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 7, 2, 5, 3, 8, 4, 6, 1]), 'cur_cost': 933.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 7, 2, 5, 0, 8, 4, 6, 1]), 'cur_cost': 1156.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 0, 7, 2, 5, 8, 4, 6, 1]), 'cur_cost': 1138.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 3, 8, 7, 5, 4, 2, 1], 'cur_cost': 810.0, 'intermediate_solutions': [{'tour': [1, 8, 2, 3, 7, 4, 6, 0, 5], 'cur_cost': 1119.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 7, 3, 2, 5, 6, 0, 4], 'cur_cost': 1003.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 8, 3, 7, 2, 5, 6, 0, 4], 'cur_cost': 976.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [7, 4, 0, 1, 3, 8, 6, 5, 2], 'cur_cost': 978.0, 'intermediate_solutions': [{'tour': [6, 8, 7, 0, 1, 4, 2, 3, 5], 'cur_cost': 796.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 3, 7, 0, 8, 2, 4, 1, 5], 'cur_cost': 879.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 3, 7, 1, 4, 2, 8, 0, 5], 'cur_cost': 882.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 7, 3, 1, 5, 4, 2, 8, 6], dtype=int64), 'cur_cost': 1009.0, 'intermediate_solutions': [{'tour': array([0, 3, 1, 6, 8, 7, 5, 4, 2]), 'cur_cost': 1029.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 0, 3, 1, 8, 7, 5, 4, 2]), 'cur_cost': 1105.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 6, 0, 3, 1, 7, 5, 4, 2]), 'cur_cost': 1019.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 6, 0, 3, 8, 7, 5, 4, 2]), 'cur_cost': 910.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 8, 6, 0, 3, 7, 5, 4, 2]), 'cur_cost': 1003.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [6, 3, 7, 4, 2, 0, 1, 5, 8], 'cur_cost': 884.0, 'intermediate_solutions': [{'tour': [5, 2, 0, 4, 8, 3, 1, 7, 6], 'cur_cost': 999.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 3, 0, 2, 5, 1, 7, 6], 'cur_cost': 1118.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 2, 0, 3, 8, 4, 1, 6], 'cur_cost': 921.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:36:13,155 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:36:13,156 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:36:13,157 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=681.000, 多样性=0.891
2025-08-05 09:36:13,157 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 09:36:13,157 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 09:36:13,157 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:36:13,157 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.034276424498328954, 'best_improvement': 0.05153203342618384}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.008241758241758131}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.03514171607519103, 'recent_improvements': [-0.0672722843967131, 0.03850517371034445, 0.003011147753668957], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.7037037037037037, 'new_diversity': 0.7037037037037037, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:36:13,157 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 09:36:13,157 - __main__ - INFO - simple1_9 开始进化第 5 代
2025-08-05 09:36:13,158 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 09:36:13,158 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:36:13,158 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=681.000, 多样性=0.891
2025-08-05 09:36:13,158 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:36:13,159 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.891
2025-08-05 09:36:13,159 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:36:13,159 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.704
2025-08-05 09:36:13,161 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 09:36:13,161 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:36:13,161 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 09:36:13,161 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 09:36:13,171 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.286, 适应度梯度: -2.600, 聚类评分: 0.000, 覆盖率: 0.005, 收敛趋势: 0.000, 多样性: 0.566
2025-08-05 09:36:13,171 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 09:36:13,171 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:36:13,171 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-05 09:36:13,177 - visualization.landscape_visualizer - INFO - 插值约束: 180 个点被约束到最小值 680.00
2025-08-05 09:36:13,181 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-05 09:36:13,276 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_5_20250805_093613.html
2025-08-05 09:36:13,328 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_5_20250805_093613.html
2025-08-05 09:36:13,328 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 5
2025-08-05 09:36:13,328 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 09:36:13,328 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1670秒
2025-08-05 09:36:13,329 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2857142857142857, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -2.599999999999997, 'local_optima_density': 0.2857142857142857, 'gradient_variance': 15099.194285714286, 'cluster_count': 0}, 'population_state': {'diversity': 0.5659340659340659, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0054, 'fitness_entropy': 0.9357849740192014, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -2.600)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.005)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754357773.1710587, 'performance_metrics': {}}}
2025-08-05 09:36:13,329 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:36:13,329 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:36:13,329 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:36:13,329 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:36:13,329 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 09:36:13,330 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 09:36:13,330 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 09:36:13,330 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:36:13,330 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:36:13,330 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 09:36:13,330 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:36:13,330 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:36:13,331 - experts.management.collaboration_manager - INFO - 识别精英个体: {2, 3} (总数: 2, 保护比例: 0.20)
2025-08-05 09:36:13,331 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:36:13,331 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:36:13,331 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:13,331 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 09:36:13,331 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,331 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,332 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,332 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,332 - ExplorationExpert - INFO - 探索路径生成完成，成本: 820.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:36:13,332 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [5, 7, 3, 6, 8, 2, 4, 0, 1], 'cur_cost': 820.0, 'intermediate_solutions': [{'tour': [3, 6, 7, 8, 2, 0, 1, 4, 5], 'cur_cost': 848.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 6, 7, 5, 2, 1, 0, 4, 8], 'cur_cost': 864.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 6, 7, 5, 8, 4, 0, 1, 2], 'cur_cost': 861.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:36:13,332 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 820.00)
2025-08-05 09:36:13,332 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 09:36:13,332 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:36:13,332 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:36:13,332 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 1103.0
2025-08-05 09:36:13,338 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:36:13,338 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0, 748.0]
2025-08-05 09:36:13,338 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 5, 6, 7, 3, 8, 4, 2, 1], dtype=int64)]
2025-08-05 09:36:13,339 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:36:13,339 - ExploitationExpert - INFO - populations: [{'tour': [5, 7, 3, 6, 8, 2, 4, 0, 1], 'cur_cost': 820.0}, {'tour': array([7, 5, 1, 3, 2, 6, 0, 4, 8], dtype=int64), 'cur_cost': 1103.0}, {'tour': [5, 8, 4, 2, 0, 1, 7, 3, 6], 'cur_cost': 774.0}, {'tour': [3, 7, 5, 6, 0, 1, 4, 2, 8], 'cur_cost': 681.0}, {'tour': [3, 4, 8, 5, 0, 7, 2, 1, 6], 'cur_cost': 1014.0}, {'tour': [2, 1, 3, 8, 6, 4, 5, 7, 0], 'cur_cost': 1094.0}, {'tour': [0, 6, 3, 8, 7, 5, 4, 2, 1], 'cur_cost': 810.0}, {'tour': [7, 4, 0, 1, 3, 8, 6, 5, 2], 'cur_cost': 978.0}, {'tour': [0, 7, 3, 1, 5, 4, 2, 8, 6], 'cur_cost': 1009.0}, {'tour': [6, 3, 7, 4, 2, 0, 1, 5, 8], 'cur_cost': 884.0}]
2025-08-05 09:36:13,340 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:36:13,340 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-08-05 09:36:13,340 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([7, 5, 1, 3, 2, 6, 0, 4, 8], dtype=int64), 'cur_cost': 1103.0, 'intermediate_solutions': [{'tour': array([6, 4, 3, 8, 7, 5, 0, 1, 2]), 'cur_cost': 1003.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 6, 4, 3, 7, 5, 0, 1, 2]), 'cur_cost': 950.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 8, 6, 4, 3, 5, 0, 1, 2]), 'cur_cost': 997.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 8, 6, 4, 7, 5, 0, 1, 2]), 'cur_cost': 990.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 7, 8, 6, 4, 5, 0, 1, 2]), 'cur_cost': 1035.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:36:13,340 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1103.00)
2025-08-05 09:36:13,341 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:36:13,341 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:36:13,341 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:13,341 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:36:13,341 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,341 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,341 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,341 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,342 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1034.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:36:13,342 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [4, 6, 0, 8, 1, 7, 3, 5, 2], 'cur_cost': 1034.0, 'intermediate_solutions': [{'tour': [5, 8, 4, 2, 3, 1, 7, 0, 6], 'cur_cost': 939.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 8, 4, 2, 0, 1, 6, 3, 7], 'cur_cost': 769.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 8, 6, 4, 2, 0, 1, 7, 3], 'cur_cost': 860.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:36:13,342 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 1034.00)
2025-08-05 09:36:13,342 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:36:13,342 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:36:13,342 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:13,342 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:36:13,343 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,343 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,343 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,343 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,343 - ExplorationExpert - INFO - 探索路径生成完成，成本: 780.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:36:13,343 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [3, 5, 6, 7, 2, 4, 0, 1, 8], 'cur_cost': 780.0, 'intermediate_solutions': [{'tour': [3, 7, 5, 6, 0, 1, 4, 8, 2], 'cur_cost': 792.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 7, 5, 6, 8, 2, 4, 1, 0], 'cur_cost': 770.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 7, 5, 6, 0, 1, 4, 2, 8], 'cur_cost': 681.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:36:13,343 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 780.00)
2025-08-05 09:36:13,343 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 09:36:13,344 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:36:13,344 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:36:13,344 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 986.0
2025-08-05 09:36:13,349 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:36:13,349 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0, 748.0]
2025-08-05 09:36:13,349 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 5, 6, 7, 3, 8, 4, 2, 1], dtype=int64)]
2025-08-05 09:36:13,350 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:36:13,350 - ExploitationExpert - INFO - populations: [{'tour': [5, 7, 3, 6, 8, 2, 4, 0, 1], 'cur_cost': 820.0}, {'tour': array([7, 5, 1, 3, 2, 6, 0, 4, 8], dtype=int64), 'cur_cost': 1103.0}, {'tour': [4, 6, 0, 8, 1, 7, 3, 5, 2], 'cur_cost': 1034.0}, {'tour': [3, 5, 6, 7, 2, 4, 0, 1, 8], 'cur_cost': 780.0}, {'tour': array([6, 8, 7, 0, 2, 1, 4, 5, 3], dtype=int64), 'cur_cost': 986.0}, {'tour': [2, 1, 3, 8, 6, 4, 5, 7, 0], 'cur_cost': 1094.0}, {'tour': [0, 6, 3, 8, 7, 5, 4, 2, 1], 'cur_cost': 810.0}, {'tour': [7, 4, 0, 1, 3, 8, 6, 5, 2], 'cur_cost': 978.0}, {'tour': [0, 7, 3, 1, 5, 4, 2, 8, 6], 'cur_cost': 1009.0}, {'tour': [6, 3, 7, 4, 2, 0, 1, 5, 8], 'cur_cost': 884.0}]
2025-08-05 09:36:13,350 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:36:13,350 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-08-05 09:36:13,351 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([6, 8, 7, 0, 2, 1, 4, 5, 3], dtype=int64), 'cur_cost': 986.0, 'intermediate_solutions': [{'tour': array([8, 4, 3, 5, 0, 7, 2, 1, 6]), 'cur_cost': 1017.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 8, 4, 3, 0, 7, 2, 1, 6]), 'cur_cost': 980.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 5, 8, 4, 3, 7, 2, 1, 6]), 'cur_cost': 987.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 5, 8, 4, 0, 7, 2, 1, 6]), 'cur_cost': 912.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 0, 5, 8, 4, 7, 2, 1, 6]), 'cur_cost': 1027.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:36:13,351 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 986.00)
2025-08-05 09:36:13,351 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 09:36:13,351 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:36:13,351 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:36:13,351 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 942.0
2025-08-05 09:36:13,357 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:36:13,357 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0, 748.0]
2025-08-05 09:36:13,357 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 5, 6, 7, 3, 8, 4, 2, 1], dtype=int64)]
2025-08-05 09:36:13,358 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:36:13,358 - ExploitationExpert - INFO - populations: [{'tour': [5, 7, 3, 6, 8, 2, 4, 0, 1], 'cur_cost': 820.0}, {'tour': array([7, 5, 1, 3, 2, 6, 0, 4, 8], dtype=int64), 'cur_cost': 1103.0}, {'tour': [4, 6, 0, 8, 1, 7, 3, 5, 2], 'cur_cost': 1034.0}, {'tour': [3, 5, 6, 7, 2, 4, 0, 1, 8], 'cur_cost': 780.0}, {'tour': array([6, 8, 7, 0, 2, 1, 4, 5, 3], dtype=int64), 'cur_cost': 986.0}, {'tour': array([5, 7, 6, 1, 8, 3, 2, 4, 0], dtype=int64), 'cur_cost': 942.0}, {'tour': [0, 6, 3, 8, 7, 5, 4, 2, 1], 'cur_cost': 810.0}, {'tour': [7, 4, 0, 1, 3, 8, 6, 5, 2], 'cur_cost': 978.0}, {'tour': [0, 7, 3, 1, 5, 4, 2, 8, 6], 'cur_cost': 1009.0}, {'tour': [6, 3, 7, 4, 2, 0, 1, 5, 8], 'cur_cost': 884.0}]
2025-08-05 09:36:13,359 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:36:13,359 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-08-05 09:36:13,359 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([5, 7, 6, 1, 8, 3, 2, 4, 0], dtype=int64), 'cur_cost': 942.0, 'intermediate_solutions': [{'tour': array([3, 1, 2, 8, 6, 4, 5, 7, 0]), 'cur_cost': 1113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 3, 1, 2, 6, 4, 5, 7, 0]), 'cur_cost': 1153.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 8, 3, 1, 2, 4, 5, 7, 0]), 'cur_cost': 970.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 8, 3, 1, 6, 4, 5, 7, 0]), 'cur_cost': 1035.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 6, 8, 3, 1, 4, 5, 7, 0]), 'cur_cost': 1103.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:36:13,359 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 942.00)
2025-08-05 09:36:13,359 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:36:13,359 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:36:13,360 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:13,360 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:36:13,360 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,360 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,360 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,360 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,360 - ExplorationExpert - INFO - 探索路径生成完成，成本: 869.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:36:13,361 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [4, 8, 0, 3, 7, 5, 6, 1, 2], 'cur_cost': 869.0, 'intermediate_solutions': [{'tour': [0, 6, 3, 4, 7, 5, 8, 2, 1], 'cur_cost': 863.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 3, 8, 7, 5, 4, 1, 2], 'cur_cost': 946.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 0, 6, 8, 7, 5, 4, 2, 1], 'cur_cost': 1018.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:36:13,361 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 869.00)
2025-08-05 09:36:13,361 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:36:13,361 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:36:13,361 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:13,361 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:36:13,361 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,361 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,362 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,362 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,362 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1037.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:36:13,362 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 3, 4, 5, 8, 7, 6, 1, 2], 'cur_cost': 1037.0, 'intermediate_solutions': [{'tour': [7, 4, 3, 1, 0, 8, 6, 5, 2], 'cur_cost': 1073.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 4, 0, 1, 8, 3, 6, 5, 2], 'cur_cost': 949.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 4, 1, 3, 8, 6, 5, 0, 2], 'cur_cost': 1039.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:36:13,362 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1037.00)
2025-08-05 09:36:13,362 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:36:13,362 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:36:13,362 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:13,363 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 09:36:13,363 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,363 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,363 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,363 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,363 - ExplorationExpert - INFO - 探索路径生成完成，成本: 961.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:36:13,363 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [4, 7, 3, 0, 5, 6, 8, 2, 1], 'cur_cost': 961.0, 'intermediate_solutions': [{'tour': [0, 7, 3, 5, 1, 4, 2, 8, 6], 'cur_cost': 871.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 3, 1, 5, 4, 2, 6, 8], 'cur_cost': 1138.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 1, 5, 7, 4, 2, 8, 6], 'cur_cost': 1012.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:36:13,364 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 961.00)
2025-08-05 09:36:13,364 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:36:13,364 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:36:13,364 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:36:13,364 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 09:36:13,364 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,364 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,365 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,365 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:36:13,365 - ExplorationExpert - INFO - 探索路径生成完成，成本: 747.0, 路径长度: 9, 收集中间解: 3
2025-08-05 09:36:13,365 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 6, 3, 5, 7, 8, 4, 2, 1], 'cur_cost': 747.0, 'intermediate_solutions': [{'tour': [6, 3, 7, 1, 2, 0, 4, 5, 8], 'cur_cost': 1016.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 3, 7, 4, 5, 1, 0, 2, 8], 'cur_cost': 972.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 3, 7, 4, 2, 0, 1, 5, 8], 'cur_cost': 884.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:36:13,365 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 747.00)
2025-08-05 09:36:13,365 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:36:13,365 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:36:13,366 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [5, 7, 3, 6, 8, 2, 4, 0, 1], 'cur_cost': 820.0, 'intermediate_solutions': [{'tour': [3, 6, 7, 8, 2, 0, 1, 4, 5], 'cur_cost': 848.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 6, 7, 5, 2, 1, 0, 4, 8], 'cur_cost': 864.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 6, 7, 5, 8, 4, 0, 1, 2], 'cur_cost': 861.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 5, 1, 3, 2, 6, 0, 4, 8], dtype=int64), 'cur_cost': 1103.0, 'intermediate_solutions': [{'tour': array([6, 4, 3, 8, 7, 5, 0, 1, 2]), 'cur_cost': 1003.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 6, 4, 3, 7, 5, 0, 1, 2]), 'cur_cost': 950.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 8, 6, 4, 3, 5, 0, 1, 2]), 'cur_cost': 997.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 8, 6, 4, 7, 5, 0, 1, 2]), 'cur_cost': 990.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 7, 8, 6, 4, 5, 0, 1, 2]), 'cur_cost': 1035.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [4, 6, 0, 8, 1, 7, 3, 5, 2], 'cur_cost': 1034.0, 'intermediate_solutions': [{'tour': [5, 8, 4, 2, 3, 1, 7, 0, 6], 'cur_cost': 939.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 8, 4, 2, 0, 1, 6, 3, 7], 'cur_cost': 769.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 8, 6, 4, 2, 0, 1, 7, 3], 'cur_cost': 860.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [3, 5, 6, 7, 2, 4, 0, 1, 8], 'cur_cost': 780.0, 'intermediate_solutions': [{'tour': [3, 7, 5, 6, 0, 1, 4, 8, 2], 'cur_cost': 792.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 7, 5, 6, 8, 2, 4, 1, 0], 'cur_cost': 770.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 7, 5, 6, 0, 1, 4, 2, 8], 'cur_cost': 681.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 8, 7, 0, 2, 1, 4, 5, 3], dtype=int64), 'cur_cost': 986.0, 'intermediate_solutions': [{'tour': array([8, 4, 3, 5, 0, 7, 2, 1, 6]), 'cur_cost': 1017.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 8, 4, 3, 0, 7, 2, 1, 6]), 'cur_cost': 980.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 5, 8, 4, 3, 7, 2, 1, 6]), 'cur_cost': 987.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 5, 8, 4, 0, 7, 2, 1, 6]), 'cur_cost': 912.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 0, 5, 8, 4, 7, 2, 1, 6]), 'cur_cost': 1027.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([5, 7, 6, 1, 8, 3, 2, 4, 0], dtype=int64), 'cur_cost': 942.0, 'intermediate_solutions': [{'tour': array([3, 1, 2, 8, 6, 4, 5, 7, 0]), 'cur_cost': 1113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 3, 1, 2, 6, 4, 5, 7, 0]), 'cur_cost': 1153.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 8, 3, 1, 2, 4, 5, 7, 0]), 'cur_cost': 970.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 8, 3, 1, 6, 4, 5, 7, 0]), 'cur_cost': 1035.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 6, 8, 3, 1, 4, 5, 7, 0]), 'cur_cost': 1103.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [4, 8, 0, 3, 7, 5, 6, 1, 2], 'cur_cost': 869.0, 'intermediate_solutions': [{'tour': [0, 6, 3, 4, 7, 5, 8, 2, 1], 'cur_cost': 863.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 3, 8, 7, 5, 4, 1, 2], 'cur_cost': 946.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 0, 6, 8, 7, 5, 4, 2, 1], 'cur_cost': 1018.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 4, 5, 8, 7, 6, 1, 2], 'cur_cost': 1037.0, 'intermediate_solutions': [{'tour': [7, 4, 3, 1, 0, 8, 6, 5, 2], 'cur_cost': 1073.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 4, 0, 1, 8, 3, 6, 5, 2], 'cur_cost': 949.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 4, 1, 3, 8, 6, 5, 0, 2], 'cur_cost': 1039.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [4, 7, 3, 0, 5, 6, 8, 2, 1], 'cur_cost': 961.0, 'intermediate_solutions': [{'tour': [0, 7, 3, 5, 1, 4, 2, 8, 6], 'cur_cost': 871.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 3, 1, 5, 4, 2, 6, 8], 'cur_cost': 1138.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 1, 5, 7, 4, 2, 8, 6], 'cur_cost': 1012.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 3, 5, 7, 8, 4, 2, 1], 'cur_cost': 747.0, 'intermediate_solutions': [{'tour': [6, 3, 7, 1, 2, 0, 4, 5, 8], 'cur_cost': 1016.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 3, 7, 4, 5, 1, 0, 2, 8], 'cur_cost': 972.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 3, 7, 4, 2, 0, 1, 5, 8], 'cur_cost': 884.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:36:13,367 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:36:13,367 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:36:13,368 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=747.000, 多样性=0.886
2025-08-05 09:36:13,368 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 09:36:13,368 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 09:36:13,368 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:36:13,368 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.04041341035819353, 'best_improvement': -0.09691629955947137}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.005540166204986074}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.002114374606007746, 'recent_improvements': [0.03850517371034445, 0.003011147753668957, 0.034276424498328954], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.7037037037037037, 'new_diversity': 0.7037037037037037, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:36:13,368 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 09:36:13,370 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_solution.json
2025-08-05 09:36:13,370 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_20250805_093613.solution
2025-08-05 09:36:13,371 - __main__ - INFO - 实例执行完成 - 运行时间: 24.68s, 最佳成本: 680.0
2025-08-05 09:36:13,371 - __main__ - INFO - 实例 simple1_9 处理完成
