2025-08-05 10:29:08,815 - __main__ - INFO - eil51 开始进化第 1 代
2025-08-05 10:29:08,815 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 10:29:08,817 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:08,819 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=495.000, 多样性=0.970
2025-08-05 10:29:08,821 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:08,824 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.970
2025-08-05 10:29:08,825 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:08,850 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 10:29:08,850 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:08,850 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 10:29:08,850 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 10:29:08,867 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.200, 适应度梯度: -32.260, 聚类评分: 0.000, 覆盖率: 0.144, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:08,867 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 10:29:08,868 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 10:29:08,868 - visualization.landscape_visualizer - INFO - 设置当前实例名: eil51
2025-08-05 10:29:08,873 - visualization.landscape_visualizer - INFO - 插值约束: 64 个点被约束到最小值 495.00
2025-08-05 10:29:08,875 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=3.0%, 梯度: 37.88 → 36.72
2025-08-05 10:29:08,979 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_eil51\landscape_eil51_iter_126_20250805_102908.html
2025-08-05 10:29:09,041 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_eil51\dashboard_eil51_iter_126_20250805_102908.html
2025-08-05 10:29:09,041 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 126
2025-08-05 10:29:09,041 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 10:29:09,042 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1924秒
2025-08-05 10:29:09,042 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 252, 'max_size': 500, 'hits': 0, 'misses': 252, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 843, 'misses': 436, 'hit_rate': 0.6591086786551994, 'evictions': 336, 'ttl': 7200}}
2025-08-05 10:29:09,042 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -32.260000000000005, 'local_optima_density': 0.2, 'gradient_variance': 328665.6884, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1439, 'fitness_entropy': 0.9372305632161296, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -32.260)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.144)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360948.8674111, 'performance_metrics': {}}}
2025-08-05 10:29:09,043 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:09,043 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:09,043 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:09,043 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:09,045 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-05 10:29:09,045 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 10:29:09,046 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-05 10:29:09,046 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:09,046 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:09,046 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-05 10:29:09,046 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:09,047 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:09,047 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:09,047 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:09,047 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:09,047 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:09,049 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 10:29:09,050 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,050 - ExplorationExpert - INFO - 探索路径生成完成，成本: 575.0, 路径长度: 51, 收集中间解: 0
2025-08-05 10:29:09,050 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 19, 20, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 34, 35, 28, 1, 21, 6, 22, 23, 42, 17, 39, 44, 32, 9, 29, 33, 38], 'cur_cost': 575.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:09,050 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 575.00)
2025-08-05 10:29:09,051 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:09,051 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:09,051 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:09,052 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 10:29:09,052 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,053 - ExplorationExpert - INFO - 探索路径生成完成，成本: 668.0, 路径长度: 51, 收集中间解: 0
2025-08-05 10:29:09,053 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 6, 18, 11, 46, 3, 16, 36, 14, 43, 41, 40, 12, 24, 13, 5, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 45, 50, 17, 23, 22, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 42, 39], 'cur_cost': 668.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:09,053 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 668.00)
2025-08-05 10:29:09,054 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:09,054 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:09,054 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:09,058 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 10:29:09,058 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,058 - ExplorationExpert - INFO - 探索路径生成完成，成本: 584.0, 路径长度: 51, 收集中间解: 0
2025-08-05 10:29:09,058 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 5, 16, 12, 40, 18, 41, 43, 14, 36, 11, 46, 3, 17, 13, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 47, 21, 1, 15, 33, 29, 9, 38, 32, 44, 39, 42], 'cur_cost': 584.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:09,059 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 584.00)
2025-08-05 10:29:09,059 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:09,059 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:09,059 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:09,061 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 10:29:09,061 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,061 - ExplorationExpert - INFO - 探索路径生成完成，成本: 617.0, 路径长度: 51, 收集中间解: 0
2025-08-05 10:29:09,062 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 21, 7, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 22, 6, 25, 30, 27, 2, 19, 34, 35, 28, 20, 33, 29, 9, 38, 32, 44, 17, 23, 42, 1, 39], 'cur_cost': 617.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:09,062 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 617.00)
2025-08-05 10:29:09,062 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:29:09,062 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:29:09,062 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:09,064 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 51
2025-08-05 10:29:09,064 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,064 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1576.0, 路径长度: 51, 收集中间解: 0
2025-08-05 10:29:09,064 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [6, 16, 46, 3, 43, 11, 5, 26, 31, 10, 37, 4, 30, 8, 1, 28, 20, 7, 9, 38, 34, 50, 17, 21, 22, 29, 42, 13, 39, 24, 40, 48, 2, 45, 12, 25, 27, 36, 18, 19, 41, 15, 32, 49, 35, 47, 14, 23, 44, 0, 33], 'cur_cost': 1576.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:09,064 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1576.00)
2025-08-05 10:29:09,064 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:29:09,064 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:29:09,065 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:09,067 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 51
2025-08-05 10:29:09,067 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,068 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1614.0, 路径长度: 51, 收集中间解: 0
2025-08-05 10:29:09,068 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [6, 21, 18, 11, 43, 5, 26, 31, 41, 40, 3, 17, 28, 20, 7, 9, 37, 4, 30, 8, 49, 29, 35, 39, 25, 38, 32, 27, 10, 50, 34, 36, 45, 19, 15, 42, 14, 2, 12, 46, 24, 47, 48, 23, 22, 1, 44, 16, 13, 33, 0], 'cur_cost': 1614.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:09,068 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1614.00)
2025-08-05 10:29:09,068 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:29:09,069 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:09,069 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:09,069 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1727.0
2025-08-05 10:29:09,078 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:29:09,079 - ExploitationExpert - INFO - res_population_costs: [441.0, 435, 434, 434]
2025-08-05 10:29:09,079 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 27, 30, 25,  7, 47, 26,  5, 22,  6, 42, 23, 13, 24, 12, 40,
       39, 18, 41, 43, 14, 44, 32, 38,  9, 29,  8, 48,  4, 36, 16,  3, 17,
       46, 11, 50, 45, 10, 37, 15, 49, 33, 20, 28, 19, 34, 35,  2,  1, 31],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 29,  8, 48,  4, 37, 15, 49, 33,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 48,  4, 37,  8, 29, 33, 49, 15,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 18, 39,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 48,  4, 37,  8, 29, 33, 49, 15,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64)]
2025-08-05 10:29:09,082 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:09,082 - ExploitationExpert - INFO - populations: [{'tour': [0, 19, 20, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 34, 35, 28, 1, 21, 6, 22, 23, 42, 17, 39, 44, 32, 9, 29, 33, 38], 'cur_cost': 575.0}, {'tour': [0, 6, 18, 11, 46, 3, 16, 36, 14, 43, 41, 40, 12, 24, 13, 5, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 45, 50, 17, 23, 22, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 42, 39], 'cur_cost': 668.0}, {'tour': [0, 5, 16, 12, 40, 18, 41, 43, 14, 36, 11, 46, 3, 17, 13, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 47, 21, 1, 15, 33, 29, 9, 38, 32, 44, 39, 42], 'cur_cost': 584.0}, {'tour': [0, 21, 7, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 22, 6, 25, 30, 27, 2, 19, 34, 35, 28, 20, 33, 29, 9, 38, 32, 44, 17, 23, 42, 1, 39], 'cur_cost': 617.0}, {'tour': [6, 16, 46, 3, 43, 11, 5, 26, 31, 10, 37, 4, 30, 8, 1, 28, 20, 7, 9, 38, 34, 50, 17, 21, 22, 29, 42, 13, 39, 24, 40, 48, 2, 45, 12, 25, 27, 36, 18, 19, 41, 15, 32, 49, 35, 47, 14, 23, 44, 0, 33], 'cur_cost': 1576.0}, {'tour': [6, 21, 18, 11, 43, 5, 26, 31, 41, 40, 3, 17, 28, 20, 7, 9, 37, 4, 30, 8, 49, 29, 35, 39, 25, 38, 32, 27, 10, 50, 34, 36, 45, 19, 15, 42, 14, 2, 12, 46, 24, 47, 48, 23, 22, 1, 44, 16, 13, 33, 0], 'cur_cost': 1614.0}, {'tour': array([22, 29,  8, 24, 33, 10, 14,  4,  2,  1, 26, 31, 15, 19, 23, 17, 21,
       20, 34, 46,  0, 36, 16, 35, 48, 27, 38, 32,  7, 12, 30, 45, 39, 47,
       41, 11,  9, 13, 28, 50, 43, 40, 37, 25,  5,  3, 42, 18,  6, 49, 44],
      dtype=int64), 'cur_cost': 1727.0}, {'tour': array([40, 37, 20, 50, 23, 28, 15, 21, 19, 43,  5, 10, 24,  7, 42, 14, 49,
        2,  1,  9, 32, 31,  4, 46, 48, 44,  8, 33,  0, 47, 36, 17, 22,  3,
       45, 27, 25,  6, 38, 30, 39, 41, 18, 26, 12, 13, 34, 29, 35, 16, 11],
      dtype=int64), 'cur_cost': 1483.0}, {'tour': array([11, 50, 47, 26,  0, 33, 29, 23, 18, 16, 38,  9,  2, 24, 32, 35, 40,
       42, 46, 39,  8,  5, 49, 43,  6, 34, 36, 10, 22, 25, 21,  7, 27, 41,
       31, 12, 14,  4, 37,  3, 20, 19, 44, 28, 48, 17, 13, 30,  1, 15, 45],
      dtype=int64), 'cur_cost': 1581.0}, {'tour': array([38,  4, 23, 46, 35, 22, 25, 39, 43, 45,  6,  9,  8, 14,  1, 27,  0,
       12, 42, 36, 32,  7, 13, 20, 17, 15, 40, 37, 21, 24, 34, 41, 30, 19,
        3, 29, 26, 31,  2, 47,  5, 50, 11, 10, 44, 33, 48, 28, 16, 18, 49],
      dtype=int64), 'cur_cost': 1684.0}]
2025-08-05 10:29:09,085 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:09,085 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 326, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 326, 'cache_hits': 0, 'similarity_calculations': 1668, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:09,086 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([22, 29,  8, 24, 33, 10, 14,  4,  2,  1, 26, 31, 15, 19, 23, 17, 21,
       20, 34, 46,  0, 36, 16, 35, 48, 27, 38, 32,  7, 12, 30, 45, 39, 47,
       41, 11,  9, 13, 28, 50, 43, 40, 37, 25,  5,  3, 42, 18,  6, 49, 44],
      dtype=int64), 'cur_cost': 1727.0, 'intermediate_solutions': [{'tour': array([ 2,  5, 42, 38,  1, 41, 17,  3,  9, 14, 49, 29, 10, 12, 50,  6, 34,
        8, 39, 43, 11, 48, 44, 19, 15, 31, 21, 13, 25, 35, 26,  7, 27, 16,
       40, 46, 20, 45, 24, 28,  0, 30, 32, 23,  4, 47, 37, 22, 36, 18, 33],
      dtype=int64), 'cur_cost': 1582.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([38,  2,  5, 42,  1, 41, 17,  3,  9, 14, 49, 29, 10, 12, 50,  6, 34,
        8, 39, 43, 11, 48, 44, 19, 15, 31, 21, 13, 25, 35, 26,  7, 27, 16,
       40, 46, 20, 45, 24, 28,  0, 30, 32, 23,  4, 47, 37, 22, 36, 18, 33],
      dtype=int64), 'cur_cost': 1555.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1, 38,  2,  5, 42, 41, 17,  3,  9, 14, 49, 29, 10, 12, 50,  6, 34,
        8, 39, 43, 11, 48, 44, 19, 15, 31, 21, 13, 25, 35, 26,  7, 27, 16,
       40, 46, 20, 45, 24, 28,  0, 30, 32, 23,  4, 47, 37, 22, 36, 18, 33],
      dtype=int64), 'cur_cost': 1554.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([42, 38,  2,  5,  1, 41, 17,  3,  9, 14, 49, 29, 10, 12, 50,  6, 34,
        8, 39, 43, 11, 48, 44, 19, 15, 31, 21, 13, 25, 35, 26,  7, 27, 16,
       40, 46, 20, 45, 24, 28,  0, 30, 32, 23,  4, 47, 37, 22, 36, 18, 33],
      dtype=int64), 'cur_cost': 1633.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([42,  1, 38,  2,  5, 41, 17,  3,  9, 14, 49, 29, 10, 12, 50,  6, 34,
        8, 39, 43, 11, 48, 44, 19, 15, 31, 21, 13, 25, 35, 26,  7, 27, 16,
       40, 46, 20, 45, 24, 28,  0, 30, 32, 23,  4, 47, 37, 22, 36, 18, 33],
      dtype=int64), 'cur_cost': 1602.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:09,087 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1727.00)
2025-08-05 10:29:09,087 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:09,087 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:09,087 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:09,090 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 10:29:09,091 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,091 - ExplorationExpert - INFO - 探索路径生成完成，成本: 609.0, 路径长度: 51, 收集中间解: 0
2025-08-05 10:29:09,091 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 1, 7, 4, 37, 8, 48, 9, 29, 33, 49, 15, 28, 20, 19, 34, 35, 2, 27, 30, 25, 6, 22, 23, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 21, 47, 5, 42, 44, 32, 38], 'cur_cost': 609.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:09,092 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 609.00)
2025-08-05 10:29:09,092 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:29:09,092 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:29:09,092 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:09,093 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 51
2025-08-05 10:29:09,094 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,094 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1379.0, 路径长度: 51, 收集中间解: 0
2025-08-05 10:29:09,094 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [5, 16, 12, 46, 18, 41, 43, 31, 36, 11, 40, 3, 8, 7, 28, 23, 4, 6, 25, 34, 48, 27, 49, 15, 1, 35, 20, 33, 29, 9, 30, 32, 44, 45, 50, 17, 2, 21, 19, 24, 47, 42, 38, 39, 10, 14, 26, 13, 0, 37, 22], 'cur_cost': 1379.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:09,094 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1379.00)
2025-08-05 10:29:09,094 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:29:09,094 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:09,095 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:09,095 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 1380.0
2025-08-05 10:29:09,101 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:29:09,101 - ExploitationExpert - INFO - res_population_costs: [441.0, 435, 434, 434, 434]
2025-08-05 10:29:09,102 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 27, 30, 25,  7, 47, 26,  5, 22,  6, 42, 23, 13, 24, 12, 40,
       39, 18, 41, 43, 14, 44, 32, 38,  9, 29,  8, 48,  4, 36, 16,  3, 17,
       46, 11, 50, 45, 10, 37, 15, 49, 33, 20, 28, 19, 34, 35,  2,  1, 31],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 29,  8, 48,  4, 37, 15, 49, 33,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 48,  4, 37,  8, 29, 33, 49, 15,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 18, 39,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 48,  4, 37,  8, 29, 33, 49, 15,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 31, 10, 37,  4, 48,  9, 38, 29, 33, 49,  8, 15, 20, 28,
       19, 34, 35,  2, 27, 30, 25,  7, 47,  5, 22,  6, 42, 23, 13, 24, 12,
       40, 18, 39, 41, 43, 44, 32, 14, 36, 16,  3, 17, 46, 11, 45, 50, 26],
      dtype=int64)]
2025-08-05 10:29:09,103 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:09,104 - ExploitationExpert - INFO - populations: [{'tour': [0, 19, 20, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 34, 35, 28, 1, 21, 6, 22, 23, 42, 17, 39, 44, 32, 9, 29, 33, 38], 'cur_cost': 575.0}, {'tour': [0, 6, 18, 11, 46, 3, 16, 36, 14, 43, 41, 40, 12, 24, 13, 5, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 45, 50, 17, 23, 22, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 42, 39], 'cur_cost': 668.0}, {'tour': [0, 5, 16, 12, 40, 18, 41, 43, 14, 36, 11, 46, 3, 17, 13, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 47, 21, 1, 15, 33, 29, 9, 38, 32, 44, 39, 42], 'cur_cost': 584.0}, {'tour': [0, 21, 7, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 22, 6, 25, 30, 27, 2, 19, 34, 35, 28, 20, 33, 29, 9, 38, 32, 44, 17, 23, 42, 1, 39], 'cur_cost': 617.0}, {'tour': [6, 16, 46, 3, 43, 11, 5, 26, 31, 10, 37, 4, 30, 8, 1, 28, 20, 7, 9, 38, 34, 50, 17, 21, 22, 29, 42, 13, 39, 24, 40, 48, 2, 45, 12, 25, 27, 36, 18, 19, 41, 15, 32, 49, 35, 47, 14, 23, 44, 0, 33], 'cur_cost': 1576.0}, {'tour': [6, 21, 18, 11, 43, 5, 26, 31, 41, 40, 3, 17, 28, 20, 7, 9, 37, 4, 30, 8, 49, 29, 35, 39, 25, 38, 32, 27, 10, 50, 34, 36, 45, 19, 15, 42, 14, 2, 12, 46, 24, 47, 48, 23, 22, 1, 44, 16, 13, 33, 0], 'cur_cost': 1614.0}, {'tour': array([22, 29,  8, 24, 33, 10, 14,  4,  2,  1, 26, 31, 15, 19, 23, 17, 21,
       20, 34, 46,  0, 36, 16, 35, 48, 27, 38, 32,  7, 12, 30, 45, 39, 47,
       41, 11,  9, 13, 28, 50, 43, 40, 37, 25,  5,  3, 42, 18,  6, 49, 44],
      dtype=int64), 'cur_cost': 1727.0}, {'tour': [0, 1, 7, 4, 37, 8, 48, 9, 29, 33, 49, 15, 28, 20, 19, 34, 35, 2, 27, 30, 25, 6, 22, 23, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 21, 47, 5, 42, 44, 32, 38], 'cur_cost': 609.0}, {'tour': [5, 16, 12, 46, 18, 41, 43, 31, 36, 11, 40, 3, 8, 7, 28, 23, 4, 6, 25, 34, 48, 27, 49, 15, 1, 35, 20, 33, 29, 9, 30, 32, 44, 45, 50, 17, 2, 21, 19, 24, 47, 42, 38, 39, 10, 14, 26, 13, 0, 37, 22], 'cur_cost': 1379.0}, {'tour': array([ 8,  2,  6, 41, 16,  0, 19, 20, 36, 45, 49,  4, 12, 34, 43,  7, 15,
       46, 26, 11, 18, 40, 38, 32, 37, 27,  1,  5, 23, 13, 47, 35, 48, 33,
       29, 30, 17, 24, 22, 50,  9, 39, 44, 14,  3, 25, 42, 21, 28, 31, 10],
      dtype=int64), 'cur_cost': 1380.0}]
2025-08-05 10:29:09,105 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:09,105 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 327, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 327, 'cache_hits': 0, 'similarity_calculations': 1669, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:09,106 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([ 8,  2,  6, 41, 16,  0, 19, 20, 36, 45, 49,  4, 12, 34, 43,  7, 15,
       46, 26, 11, 18, 40, 38, 32, 37, 27,  1,  5, 23, 13, 47, 35, 48, 33,
       29, 30, 17, 24, 22, 50,  9, 39, 44, 14,  3, 25, 42, 21, 28, 31, 10],
      dtype=int64), 'cur_cost': 1380.0, 'intermediate_solutions': [{'tour': array([23,  4, 38, 46, 35, 22, 25, 39, 43, 45,  6,  9,  8, 14,  1, 27,  0,
       12, 42, 36, 32,  7, 13, 20, 17, 15, 40, 37, 21, 24, 34, 41, 30, 19,
        3, 29, 26, 31,  2, 47,  5, 50, 11, 10, 44, 33, 48, 28, 16, 18, 49],
      dtype=int64), 'cur_cost': 1724.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([46, 23,  4, 38, 35, 22, 25, 39, 43, 45,  6,  9,  8, 14,  1, 27,  0,
       12, 42, 36, 32,  7, 13, 20, 17, 15, 40, 37, 21, 24, 34, 41, 30, 19,
        3, 29, 26, 31,  2, 47,  5, 50, 11, 10, 44, 33, 48, 28, 16, 18, 49],
      dtype=int64), 'cur_cost': 1694.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([35, 46, 23,  4, 38, 22, 25, 39, 43, 45,  6,  9,  8, 14,  1, 27,  0,
       12, 42, 36, 32,  7, 13, 20, 17, 15, 40, 37, 21, 24, 34, 41, 30, 19,
        3, 29, 26, 31,  2, 47,  5, 50, 11, 10, 44, 33, 48, 28, 16, 18, 49],
      dtype=int64), 'cur_cost': 1706.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([38, 46, 23,  4, 35, 22, 25, 39, 43, 45,  6,  9,  8, 14,  1, 27,  0,
       12, 42, 36, 32,  7, 13, 20, 17, 15, 40, 37, 21, 24, 34, 41, 30, 19,
        3, 29, 26, 31,  2, 47,  5, 50, 11, 10, 44, 33, 48, 28, 16, 18, 49],
      dtype=int64), 'cur_cost': 1690.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([38, 35, 46, 23,  4, 22, 25, 39, 43, 45,  6,  9,  8, 14,  1, 27,  0,
       12, 42, 36, 32,  7, 13, 20, 17, 15, 40, 37, 21, 24, 34, 41, 30, 19,
        3, 29, 26, 31,  2, 47,  5, 50, 11, 10, 44, 33, 48, 28, 16, 18, 49],
      dtype=int64), 'cur_cost': 1701.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:09,106 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1380.00)
2025-08-05 10:29:09,107 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:09,107 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:09,109 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 20, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 34, 35, 28, 1, 21, 6, 22, 23, 42, 17, 39, 44, 32, 9, 29, 33, 38], 'cur_cost': 575.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 18, 11, 46, 3, 16, 36, 14, 43, 41, 40, 12, 24, 13, 5, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 45, 50, 17, 23, 22, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 42, 39], 'cur_cost': 668.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 16, 12, 40, 18, 41, 43, 14, 36, 11, 46, 3, 17, 13, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 47, 21, 1, 15, 33, 29, 9, 38, 32, 44, 39, 42], 'cur_cost': 584.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 7, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 22, 6, 25, 30, 27, 2, 19, 34, 35, 28, 20, 33, 29, 9, 38, 32, 44, 17, 23, 42, 1, 39], 'cur_cost': 617.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [6, 16, 46, 3, 43, 11, 5, 26, 31, 10, 37, 4, 30, 8, 1, 28, 20, 7, 9, 38, 34, 50, 17, 21, 22, 29, 42, 13, 39, 24, 40, 48, 2, 45, 12, 25, 27, 36, 18, 19, 41, 15, 32, 49, 35, 47, 14, 23, 44, 0, 33], 'cur_cost': 1576.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [6, 21, 18, 11, 43, 5, 26, 31, 41, 40, 3, 17, 28, 20, 7, 9, 37, 4, 30, 8, 49, 29, 35, 39, 25, 38, 32, 27, 10, 50, 34, 36, 45, 19, 15, 42, 14, 2, 12, 46, 24, 47, 48, 23, 22, 1, 44, 16, 13, 33, 0], 'cur_cost': 1614.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([22, 29,  8, 24, 33, 10, 14,  4,  2,  1, 26, 31, 15, 19, 23, 17, 21,
       20, 34, 46,  0, 36, 16, 35, 48, 27, 38, 32,  7, 12, 30, 45, 39, 47,
       41, 11,  9, 13, 28, 50, 43, 40, 37, 25,  5,  3, 42, 18,  6, 49, 44],
      dtype=int64), 'cur_cost': 1727.0, 'intermediate_solutions': [{'tour': array([ 2,  5, 42, 38,  1, 41, 17,  3,  9, 14, 49, 29, 10, 12, 50,  6, 34,
        8, 39, 43, 11, 48, 44, 19, 15, 31, 21, 13, 25, 35, 26,  7, 27, 16,
       40, 46, 20, 45, 24, 28,  0, 30, 32, 23,  4, 47, 37, 22, 36, 18, 33],
      dtype=int64), 'cur_cost': 1582.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([38,  2,  5, 42,  1, 41, 17,  3,  9, 14, 49, 29, 10, 12, 50,  6, 34,
        8, 39, 43, 11, 48, 44, 19, 15, 31, 21, 13, 25, 35, 26,  7, 27, 16,
       40, 46, 20, 45, 24, 28,  0, 30, 32, 23,  4, 47, 37, 22, 36, 18, 33],
      dtype=int64), 'cur_cost': 1555.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1, 38,  2,  5, 42, 41, 17,  3,  9, 14, 49, 29, 10, 12, 50,  6, 34,
        8, 39, 43, 11, 48, 44, 19, 15, 31, 21, 13, 25, 35, 26,  7, 27, 16,
       40, 46, 20, 45, 24, 28,  0, 30, 32, 23,  4, 47, 37, 22, 36, 18, 33],
      dtype=int64), 'cur_cost': 1554.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([42, 38,  2,  5,  1, 41, 17,  3,  9, 14, 49, 29, 10, 12, 50,  6, 34,
        8, 39, 43, 11, 48, 44, 19, 15, 31, 21, 13, 25, 35, 26,  7, 27, 16,
       40, 46, 20, 45, 24, 28,  0, 30, 32, 23,  4, 47, 37, 22, 36, 18, 33],
      dtype=int64), 'cur_cost': 1633.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([42,  1, 38,  2,  5, 41, 17,  3,  9, 14, 49, 29, 10, 12, 50,  6, 34,
        8, 39, 43, 11, 48, 44, 19, 15, 31, 21, 13, 25, 35, 26,  7, 27, 16,
       40, 46, 20, 45, 24, 28,  0, 30, 32, 23,  4, 47, 37, 22, 36, 18, 33],
      dtype=int64), 'cur_cost': 1602.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 7, 4, 37, 8, 48, 9, 29, 33, 49, 15, 28, 20, 19, 34, 35, 2, 27, 30, 25, 6, 22, 23, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 21, 47, 5, 42, 44, 32, 38], 'cur_cost': 609.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [5, 16, 12, 46, 18, 41, 43, 31, 36, 11, 40, 3, 8, 7, 28, 23, 4, 6, 25, 34, 48, 27, 49, 15, 1, 35, 20, 33, 29, 9, 30, 32, 44, 45, 50, 17, 2, 21, 19, 24, 47, 42, 38, 39, 10, 14, 26, 13, 0, 37, 22], 'cur_cost': 1379.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 8,  2,  6, 41, 16,  0, 19, 20, 36, 45, 49,  4, 12, 34, 43,  7, 15,
       46, 26, 11, 18, 40, 38, 32, 37, 27,  1,  5, 23, 13, 47, 35, 48, 33,
       29, 30, 17, 24, 22, 50,  9, 39, 44, 14,  3, 25, 42, 21, 28, 31, 10],
      dtype=int64), 'cur_cost': 1380.0, 'intermediate_solutions': [{'tour': array([23,  4, 38, 46, 35, 22, 25, 39, 43, 45,  6,  9,  8, 14,  1, 27,  0,
       12, 42, 36, 32,  7, 13, 20, 17, 15, 40, 37, 21, 24, 34, 41, 30, 19,
        3, 29, 26, 31,  2, 47,  5, 50, 11, 10, 44, 33, 48, 28, 16, 18, 49],
      dtype=int64), 'cur_cost': 1724.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([46, 23,  4, 38, 35, 22, 25, 39, 43, 45,  6,  9,  8, 14,  1, 27,  0,
       12, 42, 36, 32,  7, 13, 20, 17, 15, 40, 37, 21, 24, 34, 41, 30, 19,
        3, 29, 26, 31,  2, 47,  5, 50, 11, 10, 44, 33, 48, 28, 16, 18, 49],
      dtype=int64), 'cur_cost': 1694.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([35, 46, 23,  4, 38, 22, 25, 39, 43, 45,  6,  9,  8, 14,  1, 27,  0,
       12, 42, 36, 32,  7, 13, 20, 17, 15, 40, 37, 21, 24, 34, 41, 30, 19,
        3, 29, 26, 31,  2, 47,  5, 50, 11, 10, 44, 33, 48, 28, 16, 18, 49],
      dtype=int64), 'cur_cost': 1706.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([38, 46, 23,  4, 35, 22, 25, 39, 43, 45,  6,  9,  8, 14,  1, 27,  0,
       12, 42, 36, 32,  7, 13, 20, 17, 15, 40, 37, 21, 24, 34, 41, 30, 19,
        3, 29, 26, 31,  2, 47,  5, 50, 11, 10, 44, 33, 48, 28, 16, 18, 49],
      dtype=int64), 'cur_cost': 1690.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([38, 35, 46, 23,  4, 22, 25, 39, 43, 45,  6,  9,  8, 14,  1, 27,  0,
       12, 42, 36, 32,  7, 13, 20, 17, 15, 40, 37, 21, 24, 34, 41, 30, 19,
        3, 29, 26, 31,  2, 47,  5, 50, 11, 10, 44, 33, 48, 28, 16, 18, 49],
      dtype=int64), 'cur_cost': 1701.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:29:09,109 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:09,109 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:09,112 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=575.000, 多样性=0.964
2025-08-05 10:29:09,113 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 10:29:09,113 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 10:29:09,113 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:09,114 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.011273475551236858, 'best_improvement': -0.16161616161616163}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.006735518634935093}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.14156111626758, 'recent_improvements': [-0.17531102821400432, 0.05775935717014117, 0.10781120432115567], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 5, 'new_count': 5, 'count_change': 0, 'old_best_cost': 434, 'new_best_cost': 434, 'quality_improvement': 0.0, 'old_diversity': 0.6588235294117647, 'new_diversity': 0.6588235294117647, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 10:29:09,114 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 10:29:09,115 - __main__ - INFO - eil51 开始进化第 2 代
2025-08-05 10:29:09,115 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 10:29:09,115 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:09,116 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=575.000, 多样性=0.964
2025-08-05 10:29:09,116 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:09,119 - PathExpert - INFO - 路径结构分析完成: 公共边数量=10, 路径相似性=0.964
2025-08-05 10:29:09,119 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:09,121 - EliteExpert - INFO - 精英解分析完成: 精英解数量=5, 多样性=0.659
2025-08-05 10:29:09,125 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 10:29:09,125 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:09,125 - LandscapeExpert - INFO - 添加精英解数据: 5个精英解
2025-08-05 10:29:09,125 - LandscapeExpert - INFO - 数据提取成功: 15个路径, 15个适应度值
2025-08-05 10:29:09,154 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.200, 适应度梯度: -121.267, 聚类评分: 0.000, 覆盖率: 0.145, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:09,154 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 10:29:09,154 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:09,154 - visualization.landscape_visualizer - INFO - 设置当前实例名: eil51
2025-08-05 10:29:09,159 - visualization.landscape_visualizer - INFO - 插值约束: 79 个点被约束到最小值 434.00
2025-08-05 10:29:09,160 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=9.3%, 梯度: 46.19 → 41.92
2025-08-05 10:29:09,286 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_eil51\landscape_eil51_iter_127_20250805_102909.html
2025-08-05 10:29:09,354 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_eil51\dashboard_eil51_iter_127_20250805_102909.html
2025-08-05 10:29:09,355 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 127
2025-08-05 10:29:09,355 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 10:29:09,355 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2310秒
2025-08-05 10:29:09,355 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -121.26666666666665, 'local_optima_density': 0.2, 'gradient_variance': 136053.07555555555, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1452, 'fitness_entropy': 0.9010924456664139, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -121.267)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.145)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360949.1540291, 'performance_metrics': {}}}
2025-08-05 10:29:09,356 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:09,356 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:09,356 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:09,356 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:09,357 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:29:09,358 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 10:29:09,358 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:29:09,358 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:09,358 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:09,358 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:29:09,358 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:09,359 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:09,359 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:09,359 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:09,359 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:09,359 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:09,361 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 10:29:09,361 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,362 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,362 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,362 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,362 - ExplorationExpert - INFO - 探索路径生成完成，成本: 635.0, 路径长度: 51, 收集中间解: 3
2025-08-05 10:29:09,362 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 9, 8, 12, 40, 18, 41, 43, 14, 36, 16, 3, 17, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 29, 33, 49, 15, 1, 28, 20, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 5, 47, 21, 42, 39, 44, 32, 38], 'cur_cost': 635.0, 'intermediate_solutions': [{'tour': [0, 19, 20, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 35, 41, 18, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 34, 43, 28, 1, 21, 6, 22, 23, 42, 17, 39, 44, 32, 9, 29, 33, 38], 'cur_cost': 763.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 20, 15, 49, 8, 38, 33, 29, 9, 32, 44, 39, 17, 42, 23, 22, 6, 21, 1, 28, 35, 34, 2, 27, 30, 25, 7, 47, 5, 13, 24, 12, 40, 18, 41, 43, 14, 36, 16, 3, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48], 'cur_cost': 571.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 20, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 47, 41, 18, 40, 12, 24, 13, 5, 7, 25, 30, 27, 2, 34, 35, 28, 1, 21, 6, 22, 23, 42, 17, 39, 44, 32, 9, 29, 33, 38], 'cur_cost': 650.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:09,363 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 635.00)
2025-08-05 10:29:09,363 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:09,363 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:09,363 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:09,367 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 51
2025-08-05 10:29:09,367 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,367 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,368 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,368 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,368 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1091.0, 路径长度: 51, 收集中间解: 3
2025-08-05 10:29:09,368 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [29, 8, 33, 36, 50, 14, 40, 17, 45, 22, 47, 24, 18, 37, 31, 21, 2, 0, 15, 20, 34, 7, 26, 25, 42, 23, 5, 6, 10, 1, 35, 49, 38, 43, 16, 48, 32, 9, 4, 46, 41, 11, 44, 12, 39, 13, 30, 19, 28, 27, 3], 'cur_cost': 1091.0, 'intermediate_solutions': [{'tour': [0, 6, 18, 11, 46, 3, 16, 31, 14, 43, 41, 40, 12, 24, 13, 5, 26, 36, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 45, 50, 17, 23, 22, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 42, 39], 'cur_cost': 744.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 18, 11, 46, 3, 16, 36, 14, 43, 41, 40, 12, 24, 13, 42, 21, 35, 34, 19, 2, 27, 30, 25, 7, 47, 22, 23, 17, 50, 45, 44, 32, 38, 9, 29, 33, 20, 28, 1, 15, 49, 8, 48, 4, 37, 10, 31, 26, 5, 39], 'cur_cost': 667.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 18, 11, 46, 43, 3, 16, 36, 14, 41, 40, 12, 24, 13, 5, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 45, 50, 17, 23, 22, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 42, 39], 'cur_cost': 693.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:09,369 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1091.00)
2025-08-05 10:29:09,369 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:09,369 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:09,369 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:09,371 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 10:29:09,371 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,372 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,372 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,372 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,372 - ExplorationExpert - INFO - 探索路径生成完成，成本: 583.0, 路径长度: 51, 收集中间解: 3
2025-08-05 10:29:09,372 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 5, 7, 25, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 17, 23, 22, 6, 47, 21, 15, 33, 29, 9, 38, 32, 44, 39, 42], 'cur_cost': 583.0, 'intermediate_solutions': [{'tour': [0, 5, 16, 12, 40, 18, 41, 43, 14, 36, 11, 38, 3, 17, 13, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 47, 21, 1, 15, 33, 29, 9, 46, 32, 44, 39, 42], 'cur_cost': 678.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 16, 12, 40, 18, 41, 43, 14, 36, 11, 46, 3, 17, 13, 24, 23, 22, 6, 25, 50, 26, 31, 10, 37, 4, 48, 8, 49, 20, 28, 35, 34, 19, 2, 27, 30, 7, 45, 47, 21, 1, 15, 33, 29, 9, 38, 32, 44, 39, 42], 'cur_cost': 626.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 16, 12, 40, 18, 41, 43, 14, 36, 11, 46, 3, 17, 13, 34, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 47, 21, 1, 15, 33, 29, 9, 38, 32, 44, 39, 42], 'cur_cost': 692.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:09,373 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 583.00)
2025-08-05 10:29:09,373 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:09,373 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:09,373 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:09,374 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 10:29:09,374 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,376 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,376 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,376 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,376 - ExplorationExpert - INFO - 探索路径生成完成，成本: 573.0, 路径长度: 51, 收集中间解: 3
2025-08-05 10:29:09,376 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 17, 21, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 15, 1, 33, 29, 9, 38, 32, 44, 39, 23, 22, 6, 42], 'cur_cost': 573.0, 'intermediate_solutions': [{'tour': [0, 21, 7, 15, 10, 8, 48, 4, 37, 49, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 22, 6, 25, 30, 27, 2, 19, 34, 35, 28, 20, 33, 29, 9, 38, 32, 44, 17, 23, 42, 1, 39], 'cur_cost': 646.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 7, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 22, 6, 25, 30, 39, 1, 42, 23, 17, 44, 32, 38, 9, 29, 33, 20, 28, 35, 34, 19, 2, 27], 'cur_cost': 642.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 21, 7, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 22, 6, 25, 30, 27, 2, 19, 34, 35, 28, 20, 33, 29, 9, 38, 32, 44, 23, 42, 1, 39], 'cur_cost': 672.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:09,377 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 573.00)
2025-08-05 10:29:09,377 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:29:09,377 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:29:09,377 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:09,377 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 10:29:09,377 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,377 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,377 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,378 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,378 - ExplorationExpert - INFO - 探索路径生成完成，成本: 630.0, 路径长度: 51, 收集中间解: 3
2025-08-05 10:29:09,378 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 9, 17, 2, 19, 34, 35, 27, 30, 7, 25, 6, 22, 23, 13, 24, 12, 40, 18, 41, 43, 14, 36, 16, 3, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 38, 32, 44, 39, 5, 47, 21, 42], 'cur_cost': 630.0, 'intermediate_solutions': [{'tour': [6, 16, 46, 19, 43, 11, 5, 26, 31, 10, 37, 4, 30, 8, 1, 28, 20, 7, 9, 38, 34, 50, 17, 21, 22, 29, 42, 13, 39, 24, 40, 48, 2, 45, 12, 25, 27, 36, 18, 3, 41, 15, 32, 49, 35, 47, 14, 23, 44, 0, 33], 'cur_cost': 1553.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 16, 46, 3, 43, 11, 5, 26, 31, 10, 37, 4, 30, 8, 17, 50, 34, 38, 9, 7, 20, 28, 1, 21, 22, 29, 42, 13, 39, 24, 40, 48, 2, 45, 12, 25, 27, 36, 18, 19, 41, 15, 32, 49, 35, 47, 14, 23, 44, 0, 33], 'cur_cost': 1571.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 16, 46, 3, 43, 11, 39, 5, 26, 31, 10, 37, 4, 30, 8, 1, 28, 20, 7, 9, 38, 34, 50, 17, 21, 22, 29, 42, 13, 24, 40, 48, 2, 45, 12, 25, 27, 36, 18, 19, 41, 15, 32, 49, 35, 47, 14, 23, 44, 0, 33], 'cur_cost': 1576.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:09,378 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 630.00)
2025-08-05 10:29:09,378 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:29:09,378 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:09,378 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:09,379 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 1758.0
2025-08-05 10:29:09,387 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 10:29:09,388 - ExploitationExpert - INFO - res_population_costs: [434, 434, 434, 435, 441.0, 430, 428]
2025-08-05 10:29:09,388 - ExploitationExpert - INFO - res_populations: [array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 48,  4, 37,  8, 29, 33, 49, 15,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 18, 39,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 48,  4, 37,  8, 29, 33, 49, 15,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 31, 10, 37,  4, 48,  9, 38, 29, 33, 49,  8, 15, 20, 28,
       19, 34, 35,  2, 27, 30, 25,  7, 47,  5, 22,  6, 42, 23, 13, 24, 12,
       40, 18, 39, 41, 43, 44, 32, 14, 36, 16,  3, 17, 46, 11, 45, 50, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 29,  8, 48,  4, 37, 15, 49, 33,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21, 27, 30, 25,  7, 47, 26,  5, 22,  6, 42, 23, 13, 24, 12, 40,
       39, 18, 41, 43, 14, 44, 32, 38,  9, 29,  8, 48,  4, 36, 16,  3, 17,
       46, 11, 50, 45, 10, 37, 15, 49, 33, 20, 28, 19, 34, 35,  2,  1, 31],
      dtype=int64), array([ 0, 21,  1, 15, 49,  8, 48,  4, 37, 10, 31, 50, 45, 11, 46,  3, 17,
       13, 24, 12, 40, 39, 18, 41, 43, 16, 36, 14, 44, 32, 38,  9, 29, 33,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5,
       26, 50, 45, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18, 41, 43, 16, 36,
       14, 44, 32, 38,  9, 29, 33, 20, 28, 15, 49,  8, 48,  4, 37, 10, 31],
      dtype=int64)]
2025-08-05 10:29:09,392 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:09,393 - ExploitationExpert - INFO - populations: [{'tour': [0, 9, 8, 12, 40, 18, 41, 43, 14, 36, 16, 3, 17, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 29, 33, 49, 15, 1, 28, 20, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 5, 47, 21, 42, 39, 44, 32, 38], 'cur_cost': 635.0}, {'tour': [29, 8, 33, 36, 50, 14, 40, 17, 45, 22, 47, 24, 18, 37, 31, 21, 2, 0, 15, 20, 34, 7, 26, 25, 42, 23, 5, 6, 10, 1, 35, 49, 38, 43, 16, 48, 32, 9, 4, 46, 41, 11, 44, 12, 39, 13, 30, 19, 28, 27, 3], 'cur_cost': 1091.0}, {'tour': [0, 1, 5, 7, 25, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 17, 23, 22, 6, 47, 21, 15, 33, 29, 9, 38, 32, 44, 39, 42], 'cur_cost': 583.0}, {'tour': [0, 17, 21, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 15, 1, 33, 29, 9, 38, 32, 44, 39, 23, 22, 6, 42], 'cur_cost': 573.0}, {'tour': [0, 9, 17, 2, 19, 34, 35, 27, 30, 7, 25, 6, 22, 23, 13, 24, 12, 40, 18, 41, 43, 14, 36, 16, 3, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 38, 32, 44, 39, 5, 47, 21, 42], 'cur_cost': 630.0}, {'tour': array([17, 50, 18, 15, 35,  4, 48,  6, 32, 37, 10,  1, 12, 43,  0, 44, 20,
       25, 41, 22, 30, 31, 19, 36,  2, 42, 34, 49,  7, 33, 45, 23, 14,  5,
       27, 47, 46, 13,  8, 26, 29,  3, 16, 21, 39, 38, 40,  9, 28, 11, 24],
      dtype=int64), 'cur_cost': 1758.0}, {'tour': [22, 29, 8, 24, 33, 10, 14, 4, 2, 1, 26, 31, 15, 19, 23, 17, 21, 20, 34, 46, 0, 36, 16, 35, 48, 27, 38, 32, 7, 12, 30, 45, 39, 47, 41, 11, 9, 13, 28, 50, 43, 40, 37, 25, 5, 3, 42, 18, 6, 49, 44], 'cur_cost': 1727.0}, {'tour': [0, 1, 7, 4, 37, 8, 48, 9, 29, 33, 49, 15, 28, 20, 19, 34, 35, 2, 27, 30, 25, 6, 22, 23, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 21, 47, 5, 42, 44, 32, 38], 'cur_cost': 609.0}, {'tour': [5, 16, 12, 46, 18, 41, 43, 31, 36, 11, 40, 3, 8, 7, 28, 23, 4, 6, 25, 34, 48, 27, 49, 15, 1, 35, 20, 33, 29, 9, 30, 32, 44, 45, 50, 17, 2, 21, 19, 24, 47, 42, 38, 39, 10, 14, 26, 13, 0, 37, 22], 'cur_cost': 1379.0}, {'tour': [8, 2, 6, 41, 16, 0, 19, 20, 36, 45, 49, 4, 12, 34, 43, 7, 15, 46, 26, 11, 18, 40, 38, 32, 37, 27, 1, 5, 23, 13, 47, 35, 48, 33, 29, 30, 17, 24, 22, 50, 9, 39, 44, 14, 3, 25, 42, 21, 28, 31, 10], 'cur_cost': 1380.0}]
2025-08-05 10:29:09,395 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:09,395 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 328, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 328, 'cache_hits': 0, 'similarity_calculations': 1671, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:09,396 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([17, 50, 18, 15, 35,  4, 48,  6, 32, 37, 10,  1, 12, 43,  0, 44, 20,
       25, 41, 22, 30, 31, 19, 36,  2, 42, 34, 49,  7, 33, 45, 23, 14,  5,
       27, 47, 46, 13,  8, 26, 29,  3, 16, 21, 39, 38, 40,  9, 28, 11, 24],
      dtype=int64), 'cur_cost': 1758.0, 'intermediate_solutions': [{'tour': array([18, 21,  6, 11, 43,  5, 26, 31, 41, 40,  3, 17, 28, 20,  7,  9, 37,
        4, 30,  8, 49, 29, 35, 39, 25, 38, 32, 27, 10, 50, 34, 36, 45, 19,
       15, 42, 14,  2, 12, 46, 24, 47, 48, 23, 22,  1, 44, 16, 13, 33,  0]), 'cur_cost': 1645.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([11, 18, 21,  6, 43,  5, 26, 31, 41, 40,  3, 17, 28, 20,  7,  9, 37,
        4, 30,  8, 49, 29, 35, 39, 25, 38, 32, 27, 10, 50, 34, 36, 45, 19,
       15, 42, 14,  2, 12, 46, 24, 47, 48, 23, 22,  1, 44, 16, 13, 33,  0]), 'cur_cost': 1645.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([43, 11, 18, 21,  6,  5, 26, 31, 41, 40,  3, 17, 28, 20,  7,  9, 37,
        4, 30,  8, 49, 29, 35, 39, 25, 38, 32, 27, 10, 50, 34, 36, 45, 19,
       15, 42, 14,  2, 12, 46, 24, 47, 48, 23, 22,  1, 44, 16, 13, 33,  0]), 'cur_cost': 1612.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6, 11, 18, 21, 43,  5, 26, 31, 41, 40,  3, 17, 28, 20,  7,  9, 37,
        4, 30,  8, 49, 29, 35, 39, 25, 38, 32, 27, 10, 50, 34, 36, 45, 19,
       15, 42, 14,  2, 12, 46, 24, 47, 48, 23, 22,  1, 44, 16, 13, 33,  0]), 'cur_cost': 1649.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6, 43, 11, 18, 21,  5, 26, 31, 41, 40,  3, 17, 28, 20,  7,  9, 37,
        4, 30,  8, 49, 29, 35, 39, 25, 38, 32, 27, 10, 50, 34, 36, 45, 19,
       15, 42, 14,  2, 12, 46, 24, 47, 48, 23, 22,  1, 44, 16, 13, 33,  0]), 'cur_cost': 1628.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:09,396 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1758.00)
2025-08-05 10:29:09,396 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:29:09,397 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:09,397 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:09,397 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1593.0
2025-08-05 10:29:09,408 - ExploitationExpert - INFO - res_population_num: 8
2025-08-05 10:29:09,408 - ExploitationExpert - INFO - res_population_costs: [434, 434, 434, 435, 441.0, 430, 428, 426]
2025-08-05 10:29:09,408 - ExploitationExpert - INFO - res_populations: [array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 48,  4, 37,  8, 29, 33, 49, 15,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 18, 39,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 48,  4, 37,  8, 29, 33, 49, 15,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 31, 10, 37,  4, 48,  9, 38, 29, 33, 49,  8, 15, 20, 28,
       19, 34, 35,  2, 27, 30, 25,  7, 47,  5, 22,  6, 42, 23, 13, 24, 12,
       40, 18, 39, 41, 43, 44, 32, 14, 36, 16,  3, 17, 46, 11, 45, 50, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 29,  8, 48,  4, 37, 15, 49, 33,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21, 27, 30, 25,  7, 47, 26,  5, 22,  6, 42, 23, 13, 24, 12, 40,
       39, 18, 41, 43, 14, 44, 32, 38,  9, 29,  8, 48,  4, 36, 16,  3, 17,
       46, 11, 50, 45, 10, 37, 15, 49, 33, 20, 28, 19, 34, 35,  2,  1, 31],
      dtype=int64), array([ 0, 21,  1, 15, 49,  8, 48,  4, 37, 10, 31, 50, 45, 11, 46,  3, 17,
       13, 24, 12, 40, 39, 18, 41, 43, 16, 36, 14, 44, 32, 38,  9, 29, 33,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5,
       26, 50, 45, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18, 41, 43, 16, 36,
       14, 44, 32, 38,  9, 29, 33, 20, 28, 15, 49,  8, 48,  4, 37, 10, 31],
      dtype=int64), array([ 0, 31, 10, 37,  4, 36, 16,  3, 17, 46, 11, 45, 50, 26,  5, 47, 22,
        6, 42, 23, 13, 24, 12, 40, 39, 18, 41, 43, 14, 44, 32, 38,  9, 48,
        8, 29, 33, 49, 15, 20, 28,  1, 19, 34, 35,  2, 27, 30, 25,  7, 21],
      dtype=int64)]
2025-08-05 10:29:09,411 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:09,411 - ExploitationExpert - INFO - populations: [{'tour': [0, 9, 8, 12, 40, 18, 41, 43, 14, 36, 16, 3, 17, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 29, 33, 49, 15, 1, 28, 20, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 5, 47, 21, 42, 39, 44, 32, 38], 'cur_cost': 635.0}, {'tour': [29, 8, 33, 36, 50, 14, 40, 17, 45, 22, 47, 24, 18, 37, 31, 21, 2, 0, 15, 20, 34, 7, 26, 25, 42, 23, 5, 6, 10, 1, 35, 49, 38, 43, 16, 48, 32, 9, 4, 46, 41, 11, 44, 12, 39, 13, 30, 19, 28, 27, 3], 'cur_cost': 1091.0}, {'tour': [0, 1, 5, 7, 25, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 17, 23, 22, 6, 47, 21, 15, 33, 29, 9, 38, 32, 44, 39, 42], 'cur_cost': 583.0}, {'tour': [0, 17, 21, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 15, 1, 33, 29, 9, 38, 32, 44, 39, 23, 22, 6, 42], 'cur_cost': 573.0}, {'tour': [0, 9, 17, 2, 19, 34, 35, 27, 30, 7, 25, 6, 22, 23, 13, 24, 12, 40, 18, 41, 43, 14, 36, 16, 3, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 38, 32, 44, 39, 5, 47, 21, 42], 'cur_cost': 630.0}, {'tour': array([17, 50, 18, 15, 35,  4, 48,  6, 32, 37, 10,  1, 12, 43,  0, 44, 20,
       25, 41, 22, 30, 31, 19, 36,  2, 42, 34, 49,  7, 33, 45, 23, 14,  5,
       27, 47, 46, 13,  8, 26, 29,  3, 16, 21, 39, 38, 40,  9, 28, 11, 24],
      dtype=int64), 'cur_cost': 1758.0}, {'tour': array([38,  5, 15,  0,  7, 22, 39, 48, 18, 13, 19, 24, 29, 25, 46, 30, 40,
       49, 23,  6,  1, 45, 21, 32, 27, 34,  2, 42, 16, 17, 20, 28, 10,  3,
       14, 47, 26, 35,  4, 37, 50, 36, 11, 31, 12, 44, 41,  8, 33, 43,  9],
      dtype=int64), 'cur_cost': 1593.0}, {'tour': [0, 1, 7, 4, 37, 8, 48, 9, 29, 33, 49, 15, 28, 20, 19, 34, 35, 2, 27, 30, 25, 6, 22, 23, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 21, 47, 5, 42, 44, 32, 38], 'cur_cost': 609.0}, {'tour': [5, 16, 12, 46, 18, 41, 43, 31, 36, 11, 40, 3, 8, 7, 28, 23, 4, 6, 25, 34, 48, 27, 49, 15, 1, 35, 20, 33, 29, 9, 30, 32, 44, 45, 50, 17, 2, 21, 19, 24, 47, 42, 38, 39, 10, 14, 26, 13, 0, 37, 22], 'cur_cost': 1379.0}, {'tour': [8, 2, 6, 41, 16, 0, 19, 20, 36, 45, 49, 4, 12, 34, 43, 7, 15, 46, 26, 11, 18, 40, 38, 32, 37, 27, 1, 5, 23, 13, 47, 35, 48, 33, 29, 30, 17, 24, 22, 50, 9, 39, 44, 14, 3, 25, 42, 21, 28, 31, 10], 'cur_cost': 1380.0}]
2025-08-05 10:29:09,412 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:09,412 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 329, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 329, 'cache_hits': 0, 'similarity_calculations': 1674, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:09,413 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([38,  5, 15,  0,  7, 22, 39, 48, 18, 13, 19, 24, 29, 25, 46, 30, 40,
       49, 23,  6,  1, 45, 21, 32, 27, 34,  2, 42, 16, 17, 20, 28, 10,  3,
       14, 47, 26, 35,  4, 37, 50, 36, 11, 31, 12, 44, 41,  8, 33, 43,  9],
      dtype=int64), 'cur_cost': 1593.0, 'intermediate_solutions': [{'tour': array([ 8, 29, 22, 24, 33, 10, 14,  4,  2,  1, 26, 31, 15, 19, 23, 17, 21,
       20, 34, 46,  0, 36, 16, 35, 48, 27, 38, 32,  7, 12, 30, 45, 39, 47,
       41, 11,  9, 13, 28, 50, 43, 40, 37, 25,  5,  3, 42, 18,  6, 49, 44]), 'cur_cost': 1677.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([24,  8, 29, 22, 33, 10, 14,  4,  2,  1, 26, 31, 15, 19, 23, 17, 21,
       20, 34, 46,  0, 36, 16, 35, 48, 27, 38, 32,  7, 12, 30, 45, 39, 47,
       41, 11,  9, 13, 28, 50, 43, 40, 37, 25,  5,  3, 42, 18,  6, 49, 44]), 'cur_cost': 1715.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([33, 24,  8, 29, 22, 10, 14,  4,  2,  1, 26, 31, 15, 19, 23, 17, 21,
       20, 34, 46,  0, 36, 16, 35, 48, 27, 38, 32,  7, 12, 30, 45, 39, 47,
       41, 11,  9, 13, 28, 50, 43, 40, 37, 25,  5,  3, 42, 18,  6, 49, 44]), 'cur_cost': 1717.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([22, 24,  8, 29, 33, 10, 14,  4,  2,  1, 26, 31, 15, 19, 23, 17, 21,
       20, 34, 46,  0, 36, 16, 35, 48, 27, 38, 32,  7, 12, 30, 45, 39, 47,
       41, 11,  9, 13, 28, 50, 43, 40, 37, 25,  5,  3, 42, 18,  6, 49, 44]), 'cur_cost': 1649.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([22, 33, 24,  8, 29, 10, 14,  4,  2,  1, 26, 31, 15, 19, 23, 17, 21,
       20, 34, 46,  0, 36, 16, 35, 48, 27, 38, 32,  7, 12, 30, 45, 39, 47,
       41, 11,  9, 13, 28, 50, 43, 40, 37, 25,  5,  3, 42, 18,  6, 49, 44]), 'cur_cost': 1726.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:09,414 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1593.00)
2025-08-05 10:29:09,414 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:09,414 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:09,414 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:09,419 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 51
2025-08-05 10:29:09,420 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,420 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,420 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,421 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,421 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1201.0, 路径长度: 51, 收集中间解: 3
2025-08-05 10:29:09,421 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [17, 40, 16, 10, 28, 19, 7, 2, 49, 31, 36, 18, 4, 11, 14, 8, 37, 48, 32, 46, 43, 29, 50, 22, 0, 13, 12, 45, 5, 24, 6, 23, 30, 27, 26, 1, 33, 15, 35, 9, 38, 21, 34, 3, 44, 41, 39, 42, 25, 47, 20], 'cur_cost': 1201.0, 'intermediate_solutions': [{'tour': [0, 1, 7, 4, 16, 8, 48, 9, 29, 33, 49, 15, 28, 20, 19, 34, 35, 2, 27, 30, 25, 6, 22, 23, 13, 24, 17, 3, 37, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 21, 47, 5, 42, 44, 32, 38], 'cur_cost': 669.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 7, 4, 37, 8, 48, 9, 29, 33, 49, 15, 28, 20, 19, 34, 35, 2, 27, 30, 25, 16, 3, 17, 24, 13, 23, 22, 6, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 21, 47, 5, 42, 44, 32, 38], 'cur_cost': 682.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 7, 6, 4, 37, 8, 48, 9, 29, 33, 49, 15, 28, 20, 19, 34, 35, 2, 27, 30, 25, 22, 23, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 21, 47, 5, 42, 44, 32, 38], 'cur_cost': 629.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:09,422 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1201.00)
2025-08-05 10:29:09,422 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:29:09,422 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:29:09,422 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:09,424 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 51
2025-08-05 10:29:09,424 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,424 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,424 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,425 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,425 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1674.0, 路径长度: 51, 收集中间解: 3
2025-08-05 10:29:09,425 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [29, 17, 5, 36, 25, 18, 41, 43, 14, 22, 47, 16, 20, 37, 31, 48, 2, 26, 15, 10, 34, 7, 11, 33, 49, 27, 1, 28, 24, 32, 35, 12, 8, 13, 4, 9, 6, 46, 23, 38, 44, 0, 30, 39, 42, 50, 40, 19, 3, 45, 21], 'cur_cost': 1674.0, 'intermediate_solutions': [{'tour': [5, 7, 12, 46, 18, 41, 43, 31, 36, 11, 40, 3, 8, 16, 28, 23, 4, 6, 25, 34, 48, 27, 49, 15, 1, 35, 20, 33, 29, 9, 30, 32, 44, 45, 50, 17, 2, 21, 19, 24, 47, 42, 38, 39, 10, 14, 26, 13, 0, 37, 22], 'cur_cost': 1396.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 16, 12, 46, 18, 41, 43, 31, 36, 11, 40, 3, 8, 7, 28, 23, 4, 6, 25, 34, 48, 27, 49, 15, 1, 35, 20, 33, 29, 9, 30, 32, 44, 45, 50, 17, 2, 21, 24, 19, 47, 42, 38, 39, 10, 14, 26, 13, 0, 37, 22], 'cur_cost': 1411.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 16, 12, 46, 18, 41, 21, 43, 31, 36, 11, 40, 3, 8, 7, 28, 23, 4, 6, 25, 34, 48, 27, 49, 15, 1, 35, 20, 33, 29, 9, 30, 32, 44, 45, 50, 17, 2, 19, 24, 47, 42, 38, 39, 10, 14, 26, 13, 0, 37, 22], 'cur_cost': 1445.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:09,425 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1674.00)
2025-08-05 10:29:09,425 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:29:09,425 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:29:09,425 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:09,428 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 10:29:09,428 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,429 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,429 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,429 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,430 - ExplorationExpert - INFO - 探索路径生成完成，成本: 598.0, 路径长度: 51, 收集中间解: 3
2025-08-05 10:29:09,430 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 11, 18, 14, 43, 36, 16, 3, 17, 46, 50, 45, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 41, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 6, 22, 23, 42, 39], 'cur_cost': 598.0, 'intermediate_solutions': [{'tour': [8, 2, 6, 41, 16, 0, 19, 20, 36, 45, 49, 4, 12, 34, 43, 7, 15, 46, 26, 11, 18, 40, 38, 32, 37, 27, 1, 5, 23, 48, 47, 35, 13, 33, 29, 30, 17, 24, 22, 50, 9, 39, 44, 14, 3, 25, 42, 21, 28, 31, 10], 'cur_cost': 1483.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 2, 6, 41, 16, 0, 19, 20, 36, 45, 49, 4, 12, 34, 43, 7, 15, 46, 26, 11, 18, 40, 38, 32, 37, 27, 1, 5, 23, 13, 47, 35, 48, 33, 29, 30, 17, 24, 25, 3, 14, 44, 39, 9, 50, 22, 42, 21, 28, 31, 10], 'cur_cost': 1386.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 2, 6, 41, 16, 0, 20, 36, 45, 49, 4, 12, 34, 43, 7, 15, 19, 46, 26, 11, 18, 40, 38, 32, 37, 27, 1, 5, 23, 13, 47, 35, 48, 33, 29, 30, 17, 24, 22, 50, 9, 39, 44, 14, 3, 25, 42, 21, 28, 31, 10], 'cur_cost': 1400.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:09,430 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 598.00)
2025-08-05 10:29:09,430 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:09,430 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:09,433 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 8, 12, 40, 18, 41, 43, 14, 36, 16, 3, 17, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 29, 33, 49, 15, 1, 28, 20, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 5, 47, 21, 42, 39, 44, 32, 38], 'cur_cost': 635.0, 'intermediate_solutions': [{'tour': [0, 19, 20, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 35, 41, 18, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 34, 43, 28, 1, 21, 6, 22, 23, 42, 17, 39, 44, 32, 9, 29, 33, 38], 'cur_cost': 763.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 20, 15, 49, 8, 38, 33, 29, 9, 32, 44, 39, 17, 42, 23, 22, 6, 21, 1, 28, 35, 34, 2, 27, 30, 25, 7, 47, 5, 13, 24, 12, 40, 18, 41, 43, 14, 36, 16, 3, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48], 'cur_cost': 571.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 20, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 47, 41, 18, 40, 12, 24, 13, 5, 7, 25, 30, 27, 2, 34, 35, 28, 1, 21, 6, 22, 23, 42, 17, 39, 44, 32, 9, 29, 33, 38], 'cur_cost': 650.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [29, 8, 33, 36, 50, 14, 40, 17, 45, 22, 47, 24, 18, 37, 31, 21, 2, 0, 15, 20, 34, 7, 26, 25, 42, 23, 5, 6, 10, 1, 35, 49, 38, 43, 16, 48, 32, 9, 4, 46, 41, 11, 44, 12, 39, 13, 30, 19, 28, 27, 3], 'cur_cost': 1091.0, 'intermediate_solutions': [{'tour': [0, 6, 18, 11, 46, 3, 16, 31, 14, 43, 41, 40, 12, 24, 13, 5, 26, 36, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 45, 50, 17, 23, 22, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 42, 39], 'cur_cost': 744.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 18, 11, 46, 3, 16, 36, 14, 43, 41, 40, 12, 24, 13, 42, 21, 35, 34, 19, 2, 27, 30, 25, 7, 47, 22, 23, 17, 50, 45, 44, 32, 38, 9, 29, 33, 20, 28, 1, 15, 49, 8, 48, 4, 37, 10, 31, 26, 5, 39], 'cur_cost': 667.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 18, 11, 46, 43, 3, 16, 36, 14, 41, 40, 12, 24, 13, 5, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 45, 50, 17, 23, 22, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 42, 39], 'cur_cost': 693.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 5, 7, 25, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 17, 23, 22, 6, 47, 21, 15, 33, 29, 9, 38, 32, 44, 39, 42], 'cur_cost': 583.0, 'intermediate_solutions': [{'tour': [0, 5, 16, 12, 40, 18, 41, 43, 14, 36, 11, 38, 3, 17, 13, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 47, 21, 1, 15, 33, 29, 9, 46, 32, 44, 39, 42], 'cur_cost': 678.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 16, 12, 40, 18, 41, 43, 14, 36, 11, 46, 3, 17, 13, 24, 23, 22, 6, 25, 50, 26, 31, 10, 37, 4, 48, 8, 49, 20, 28, 35, 34, 19, 2, 27, 30, 7, 45, 47, 21, 1, 15, 33, 29, 9, 38, 32, 44, 39, 42], 'cur_cost': 626.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 16, 12, 40, 18, 41, 43, 14, 36, 11, 46, 3, 17, 13, 34, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 47, 21, 1, 15, 33, 29, 9, 38, 32, 44, 39, 42], 'cur_cost': 692.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 21, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 15, 1, 33, 29, 9, 38, 32, 44, 39, 23, 22, 6, 42], 'cur_cost': 573.0, 'intermediate_solutions': [{'tour': [0, 21, 7, 15, 10, 8, 48, 4, 37, 49, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 22, 6, 25, 30, 27, 2, 19, 34, 35, 28, 20, 33, 29, 9, 38, 32, 44, 17, 23, 42, 1, 39], 'cur_cost': 646.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 7, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 22, 6, 25, 30, 39, 1, 42, 23, 17, 44, 32, 38, 9, 29, 33, 20, 28, 35, 34, 19, 2, 27], 'cur_cost': 642.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 21, 7, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 22, 6, 25, 30, 27, 2, 19, 34, 35, 28, 20, 33, 29, 9, 38, 32, 44, 23, 42, 1, 39], 'cur_cost': 672.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 17, 2, 19, 34, 35, 27, 30, 7, 25, 6, 22, 23, 13, 24, 12, 40, 18, 41, 43, 14, 36, 16, 3, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 38, 32, 44, 39, 5, 47, 21, 42], 'cur_cost': 630.0, 'intermediate_solutions': [{'tour': [6, 16, 46, 19, 43, 11, 5, 26, 31, 10, 37, 4, 30, 8, 1, 28, 20, 7, 9, 38, 34, 50, 17, 21, 22, 29, 42, 13, 39, 24, 40, 48, 2, 45, 12, 25, 27, 36, 18, 3, 41, 15, 32, 49, 35, 47, 14, 23, 44, 0, 33], 'cur_cost': 1553.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 16, 46, 3, 43, 11, 5, 26, 31, 10, 37, 4, 30, 8, 17, 50, 34, 38, 9, 7, 20, 28, 1, 21, 22, 29, 42, 13, 39, 24, 40, 48, 2, 45, 12, 25, 27, 36, 18, 19, 41, 15, 32, 49, 35, 47, 14, 23, 44, 0, 33], 'cur_cost': 1571.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 16, 46, 3, 43, 11, 39, 5, 26, 31, 10, 37, 4, 30, 8, 1, 28, 20, 7, 9, 38, 34, 50, 17, 21, 22, 29, 42, 13, 24, 40, 48, 2, 45, 12, 25, 27, 36, 18, 19, 41, 15, 32, 49, 35, 47, 14, 23, 44, 0, 33], 'cur_cost': 1576.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([17, 50, 18, 15, 35,  4, 48,  6, 32, 37, 10,  1, 12, 43,  0, 44, 20,
       25, 41, 22, 30, 31, 19, 36,  2, 42, 34, 49,  7, 33, 45, 23, 14,  5,
       27, 47, 46, 13,  8, 26, 29,  3, 16, 21, 39, 38, 40,  9, 28, 11, 24],
      dtype=int64), 'cur_cost': 1758.0, 'intermediate_solutions': [{'tour': array([18, 21,  6, 11, 43,  5, 26, 31, 41, 40,  3, 17, 28, 20,  7,  9, 37,
        4, 30,  8, 49, 29, 35, 39, 25, 38, 32, 27, 10, 50, 34, 36, 45, 19,
       15, 42, 14,  2, 12, 46, 24, 47, 48, 23, 22,  1, 44, 16, 13, 33,  0]), 'cur_cost': 1645.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([11, 18, 21,  6, 43,  5, 26, 31, 41, 40,  3, 17, 28, 20,  7,  9, 37,
        4, 30,  8, 49, 29, 35, 39, 25, 38, 32, 27, 10, 50, 34, 36, 45, 19,
       15, 42, 14,  2, 12, 46, 24, 47, 48, 23, 22,  1, 44, 16, 13, 33,  0]), 'cur_cost': 1645.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([43, 11, 18, 21,  6,  5, 26, 31, 41, 40,  3, 17, 28, 20,  7,  9, 37,
        4, 30,  8, 49, 29, 35, 39, 25, 38, 32, 27, 10, 50, 34, 36, 45, 19,
       15, 42, 14,  2, 12, 46, 24, 47, 48, 23, 22,  1, 44, 16, 13, 33,  0]), 'cur_cost': 1612.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6, 11, 18, 21, 43,  5, 26, 31, 41, 40,  3, 17, 28, 20,  7,  9, 37,
        4, 30,  8, 49, 29, 35, 39, 25, 38, 32, 27, 10, 50, 34, 36, 45, 19,
       15, 42, 14,  2, 12, 46, 24, 47, 48, 23, 22,  1, 44, 16, 13, 33,  0]), 'cur_cost': 1649.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6, 43, 11, 18, 21,  5, 26, 31, 41, 40,  3, 17, 28, 20,  7,  9, 37,
        4, 30,  8, 49, 29, 35, 39, 25, 38, 32, 27, 10, 50, 34, 36, 45, 19,
       15, 42, 14,  2, 12, 46, 24, 47, 48, 23, 22,  1, 44, 16, 13, 33,  0]), 'cur_cost': 1628.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([38,  5, 15,  0,  7, 22, 39, 48, 18, 13, 19, 24, 29, 25, 46, 30, 40,
       49, 23,  6,  1, 45, 21, 32, 27, 34,  2, 42, 16, 17, 20, 28, 10,  3,
       14, 47, 26, 35,  4, 37, 50, 36, 11, 31, 12, 44, 41,  8, 33, 43,  9],
      dtype=int64), 'cur_cost': 1593.0, 'intermediate_solutions': [{'tour': array([ 8, 29, 22, 24, 33, 10, 14,  4,  2,  1, 26, 31, 15, 19, 23, 17, 21,
       20, 34, 46,  0, 36, 16, 35, 48, 27, 38, 32,  7, 12, 30, 45, 39, 47,
       41, 11,  9, 13, 28, 50, 43, 40, 37, 25,  5,  3, 42, 18,  6, 49, 44]), 'cur_cost': 1677.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([24,  8, 29, 22, 33, 10, 14,  4,  2,  1, 26, 31, 15, 19, 23, 17, 21,
       20, 34, 46,  0, 36, 16, 35, 48, 27, 38, 32,  7, 12, 30, 45, 39, 47,
       41, 11,  9, 13, 28, 50, 43, 40, 37, 25,  5,  3, 42, 18,  6, 49, 44]), 'cur_cost': 1715.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([33, 24,  8, 29, 22, 10, 14,  4,  2,  1, 26, 31, 15, 19, 23, 17, 21,
       20, 34, 46,  0, 36, 16, 35, 48, 27, 38, 32,  7, 12, 30, 45, 39, 47,
       41, 11,  9, 13, 28, 50, 43, 40, 37, 25,  5,  3, 42, 18,  6, 49, 44]), 'cur_cost': 1717.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([22, 24,  8, 29, 33, 10, 14,  4,  2,  1, 26, 31, 15, 19, 23, 17, 21,
       20, 34, 46,  0, 36, 16, 35, 48, 27, 38, 32,  7, 12, 30, 45, 39, 47,
       41, 11,  9, 13, 28, 50, 43, 40, 37, 25,  5,  3, 42, 18,  6, 49, 44]), 'cur_cost': 1649.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([22, 33, 24,  8, 29, 10, 14,  4,  2,  1, 26, 31, 15, 19, 23, 17, 21,
       20, 34, 46,  0, 36, 16, 35, 48, 27, 38, 32,  7, 12, 30, 45, 39, 47,
       41, 11,  9, 13, 28, 50, 43, 40, 37, 25,  5,  3, 42, 18,  6, 49, 44]), 'cur_cost': 1726.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [17, 40, 16, 10, 28, 19, 7, 2, 49, 31, 36, 18, 4, 11, 14, 8, 37, 48, 32, 46, 43, 29, 50, 22, 0, 13, 12, 45, 5, 24, 6, 23, 30, 27, 26, 1, 33, 15, 35, 9, 38, 21, 34, 3, 44, 41, 39, 42, 25, 47, 20], 'cur_cost': 1201.0, 'intermediate_solutions': [{'tour': [0, 1, 7, 4, 16, 8, 48, 9, 29, 33, 49, 15, 28, 20, 19, 34, 35, 2, 27, 30, 25, 6, 22, 23, 13, 24, 17, 3, 37, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 21, 47, 5, 42, 44, 32, 38], 'cur_cost': 669.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 7, 4, 37, 8, 48, 9, 29, 33, 49, 15, 28, 20, 19, 34, 35, 2, 27, 30, 25, 16, 3, 17, 24, 13, 23, 22, 6, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 21, 47, 5, 42, 44, 32, 38], 'cur_cost': 682.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 7, 6, 4, 37, 8, 48, 9, 29, 33, 49, 15, 28, 20, 19, 34, 35, 2, 27, 30, 25, 22, 23, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 21, 47, 5, 42, 44, 32, 38], 'cur_cost': 629.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [29, 17, 5, 36, 25, 18, 41, 43, 14, 22, 47, 16, 20, 37, 31, 48, 2, 26, 15, 10, 34, 7, 11, 33, 49, 27, 1, 28, 24, 32, 35, 12, 8, 13, 4, 9, 6, 46, 23, 38, 44, 0, 30, 39, 42, 50, 40, 19, 3, 45, 21], 'cur_cost': 1674.0, 'intermediate_solutions': [{'tour': [5, 7, 12, 46, 18, 41, 43, 31, 36, 11, 40, 3, 8, 16, 28, 23, 4, 6, 25, 34, 48, 27, 49, 15, 1, 35, 20, 33, 29, 9, 30, 32, 44, 45, 50, 17, 2, 21, 19, 24, 47, 42, 38, 39, 10, 14, 26, 13, 0, 37, 22], 'cur_cost': 1396.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 16, 12, 46, 18, 41, 43, 31, 36, 11, 40, 3, 8, 7, 28, 23, 4, 6, 25, 34, 48, 27, 49, 15, 1, 35, 20, 33, 29, 9, 30, 32, 44, 45, 50, 17, 2, 21, 24, 19, 47, 42, 38, 39, 10, 14, 26, 13, 0, 37, 22], 'cur_cost': 1411.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 16, 12, 46, 18, 41, 21, 43, 31, 36, 11, 40, 3, 8, 7, 28, 23, 4, 6, 25, 34, 48, 27, 49, 15, 1, 35, 20, 33, 29, 9, 30, 32, 44, 45, 50, 17, 2, 19, 24, 47, 42, 38, 39, 10, 14, 26, 13, 0, 37, 22], 'cur_cost': 1445.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 18, 14, 43, 36, 16, 3, 17, 46, 50, 45, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 41, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 6, 22, 23, 42, 39], 'cur_cost': 598.0, 'intermediate_solutions': [{'tour': [8, 2, 6, 41, 16, 0, 19, 20, 36, 45, 49, 4, 12, 34, 43, 7, 15, 46, 26, 11, 18, 40, 38, 32, 37, 27, 1, 5, 23, 48, 47, 35, 13, 33, 29, 30, 17, 24, 22, 50, 9, 39, 44, 14, 3, 25, 42, 21, 28, 31, 10], 'cur_cost': 1483.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 2, 6, 41, 16, 0, 19, 20, 36, 45, 49, 4, 12, 34, 43, 7, 15, 46, 26, 11, 18, 40, 38, 32, 37, 27, 1, 5, 23, 13, 47, 35, 48, 33, 29, 30, 17, 24, 25, 3, 14, 44, 39, 9, 50, 22, 42, 21, 28, 31, 10], 'cur_cost': 1386.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 2, 6, 41, 16, 0, 20, 36, 45, 49, 4, 12, 34, 43, 7, 15, 19, 46, 26, 11, 18, 40, 38, 32, 37, 27, 1, 5, 23, 13, 47, 35, 48, 33, 29, 30, 17, 24, 22, 50, 9, 39, 44, 14, 3, 25, 42, 21, 28, 31, 10], 'cur_cost': 1400.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:29:09,434 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:09,434 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:09,439 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=573.000, 多样性=0.968
2025-08-05 10:29:09,439 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 10:29:09,439 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 10:29:09,439 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:09,440 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 70, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'slight_improvement', 'improvement_rate': 0.007385917808184476, 'best_improvement': 0.0034782608695652175}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.004068716094032535}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.03451641636068901, 'recent_improvements': [0.05775935717014117, 0.10781120432115567, -0.011273475551236858], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 8, 'new_count': 8, 'count_change': 0, 'old_best_cost': 426, 'new_best_cost': 426, 'quality_improvement': 0.0, 'old_diversity': 0.8130252100840336, 'new_diversity': 0.8130252100840336, 'diversity_change': 0.0}, 'suggestions': ['改进缓慢，建议调整策略平衡或增加扰动强度', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配']}
2025-08-05 10:29:09,441 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 10:29:09,441 - __main__ - INFO - eil51 开始进化第 3 代
2025-08-05 10:29:09,441 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 10:29:09,442 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:09,442 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=573.000, 多样性=0.968
2025-08-05 10:29:09,443 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:09,445 - PathExpert - INFO - 路径结构分析完成: 公共边数量=6, 路径相似性=0.968
2025-08-05 10:29:09,445 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:09,449 - EliteExpert - INFO - 精英解分析完成: 精英解数量=8, 多样性=0.813
2025-08-05 10:29:09,451 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 10:29:09,451 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:09,451 - LandscapeExpert - INFO - 添加精英解数据: 8个精英解
2025-08-05 10:29:09,451 - LandscapeExpert - INFO - 数据提取成功: 18个路径, 18个适应度值
2025-08-05 10:29:09,507 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.167, 适应度梯度: -139.278, 聚类评分: 0.000, 覆盖率: 0.146, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:09,507 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 10:29:09,507 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:09,507 - visualization.landscape_visualizer - INFO - 设置当前实例名: eil51
2025-08-05 10:29:09,514 - visualization.landscape_visualizer - INFO - 插值约束: 207 个点被约束到最小值 426.00
2025-08-05 10:29:09,516 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=6.5%, 梯度: 56.10 → 52.47
2025-08-05 10:29:09,620 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_eil51\landscape_eil51_iter_128_20250805_102909.html
2025-08-05 10:29:09,671 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_eil51\dashboard_eil51_iter_128_20250805_102909.html
2025-08-05 10:29:09,671 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 128
2025-08-05 10:29:09,672 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 10:29:09,672 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2210秒
2025-08-05 10:29:09,672 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.16666666666666666, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -139.2777777777778, 'local_optima_density': 0.16666666666666666, 'gradient_variance': 161015.26728395058, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1462, 'fitness_entropy': 0.8048474707311407, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -139.278)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.146)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360949.507043, 'performance_metrics': {}}}
2025-08-05 10:29:09,673 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:09,673 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:09,673 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:09,673 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:09,674 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:29:09,674 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 10:29:09,674 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:29:09,674 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:09,674 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:09,674 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:29:09,675 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:09,675 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:09,675 - experts.management.collaboration_manager - INFO - 识别精英个体: {2, 3} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:09,675 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:09,675 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:09,675 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:09,676 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 51
2025-08-05 10:29:09,677 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,677 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,677 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,677 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,677 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1479.0, 路径长度: 51, 收集中间解: 3
2025-08-05 10:29:09,678 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [38, 8, 33, 7, 40, 30, 50, 45, 19, 34, 16, 3, 17, 49, 43, 41, 18, 0, 10, 20, 37, 4, 26, 29, 42, 23, 15, 1, 28, 24, 31, 12, 35, 2, 48, 13, 32, 25, 6, 22, 47, 11, 44, 5, 39, 9, 36, 27, 21, 14, 46], 'cur_cost': 1479.0, 'intermediate_solutions': [{'tour': [0, 9, 8, 12, 40, 18, 41, 6, 14, 36, 16, 3, 17, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 29, 33, 49, 15, 1, 28, 20, 19, 34, 35, 2, 27, 30, 7, 25, 43, 22, 23, 13, 24, 5, 47, 21, 42, 39, 44, 32, 38], 'cur_cost': 803.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 8, 12, 40, 18, 41, 43, 14, 36, 16, 3, 17, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 29, 33, 25, 7, 30, 27, 2, 35, 34, 19, 20, 28, 1, 15, 49, 6, 22, 23, 13, 24, 5, 47, 21, 42, 39, 44, 32, 38], 'cur_cost': 714.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 8, 12, 40, 18, 41, 43, 14, 36, 16, 3, 17, 46, 11, 45, 50, 26, 31, 10, 37, 4, 29, 33, 49, 48, 15, 1, 28, 20, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 5, 47, 21, 42, 39, 44, 32, 38], 'cur_cost': 655.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:09,678 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1479.00)
2025-08-05 10:29:09,678 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:09,678 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:09,678 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:09,679 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 51
2025-08-05 10:29:09,679 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,679 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,680 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,680 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,680 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1421.0, 路径长度: 51, 收集中间解: 3
2025-08-05 10:29:09,680 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [38, 9, 17, 15, 50, 26, 40, 27, 11, 37, 35, 3, 18, 23, 13, 24, 12, 25, 41, 31, 30, 5, 47, 29, 2, 46, 16, 1, 10, 43, 45, 49, 20, 4, 8, 48, 33, 7, 6, 22, 44, 36, 34, 21, 42, 39, 14, 32, 28, 19, 0], 'cur_cost': 1421.0, 'intermediate_solutions': [{'tour': [29, 8, 4, 36, 50, 14, 40, 17, 45, 22, 47, 24, 18, 37, 31, 21, 2, 0, 15, 20, 34, 7, 26, 25, 42, 23, 5, 6, 10, 1, 35, 49, 38, 43, 16, 48, 32, 9, 33, 46, 41, 11, 44, 12, 39, 13, 30, 19, 28, 27, 3], 'cur_cost': 1097.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [29, 8, 33, 36, 50, 14, 40, 17, 45, 22, 47, 24, 18, 37, 31, 21, 2, 0, 15, 20, 34, 7, 26, 25, 5, 23, 42, 6, 10, 1, 35, 49, 38, 43, 16, 48, 32, 9, 4, 46, 41, 11, 44, 12, 39, 13, 30, 19, 28, 27, 3], 'cur_cost': 1087.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [29, 8, 0, 33, 36, 50, 14, 40, 17, 45, 22, 47, 24, 18, 37, 31, 21, 2, 15, 20, 34, 7, 26, 25, 42, 23, 5, 6, 10, 1, 35, 49, 38, 43, 16, 48, 32, 9, 4, 46, 41, 11, 44, 12, 39, 13, 30, 19, 28, 27, 3], 'cur_cost': 1122.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:09,680 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1421.00)
2025-08-05 10:29:09,681 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:09,681 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:09,681 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:09,684 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 51
2025-08-05 10:29:09,684 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,684 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,684 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,684 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,685 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1076.0, 路径长度: 51, 收集中间解: 3
2025-08-05 10:29:09,685 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [13, 24, 17, 39, 5, 25, 47, 6, 30, 0, 46, 14, 16, 43, 12, 10, 15, 37, 20, 1, 8, 33, 34, 4, 11, 18, 23, 22, 42, 31, 28, 27, 35, 21, 2, 50, 36, 45, 7, 26, 49, 38, 48, 29, 32, 44, 9, 3, 40, 41, 19], 'cur_cost': 1076.0, 'intermediate_solutions': [{'tour': [0, 1, 5, 7, 25, 30, 27, 2, 19, 34, 35, 11, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 28, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 17, 23, 22, 6, 47, 21, 15, 33, 29, 9, 38, 32, 44, 39, 42], 'cur_cost': 688.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 5, 7, 25, 30, 27, 2, 14, 36, 16, 3, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 20, 28, 35, 34, 19, 43, 41, 18, 40, 12, 24, 13, 17, 23, 22, 6, 47, 21, 15, 33, 29, 9, 38, 32, 44, 39, 42], 'cur_cost': 671.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 5, 7, 25, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 17, 23, 22, 33, 6, 47, 21, 15, 29, 9, 38, 32, 44, 39, 42], 'cur_cost': 677.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:09,685 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 1076.00)
2025-08-05 10:29:09,685 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:09,685 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:09,685 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:09,686 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 51
2025-08-05 10:29:09,686 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,687 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,687 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,687 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,687 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1523.0, 路径长度: 51, 收集中间解: 3
2025-08-05 10:29:09,687 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [13, 24, 21, 39, 40, 25, 47, 2, 32, 0, 35, 6, 18, 14, 12, 10, 15, 37, 20, 31, 8, 4, 34, 7, 11, 30, 16, 22, 28, 43, 41, 49, 26, 5, 27, 50, 36, 45, 1, 9, 33, 38, 44, 29, 42, 3, 46, 23, 48, 19, 17], 'cur_cost': 1523.0, 'intermediate_solutions': [{'tour': [44, 17, 21, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 15, 1, 33, 29, 9, 38, 32, 0, 39, 23, 22, 6, 42], 'cur_cost': 665.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 21, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 19, 34, 28, 35, 20, 49, 8, 48, 4, 37, 15, 1, 33, 29, 9, 38, 32, 44, 39, 23, 22, 6, 42], 'cur_cost': 603.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 21, 10, 31, 50, 45, 11, 46, 3, 26, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 15, 1, 33, 29, 9, 38, 32, 44, 39, 23, 22, 6, 42], 'cur_cost': 608.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:09,687 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 1523.00)
2025-08-05 10:29:09,688 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:29:09,688 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:29:09,688 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:09,689 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 10:29:09,689 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,689 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,689 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,690 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,690 - ExplorationExpert - INFO - 探索路径生成完成，成本: 629.0, 路径长度: 51, 收集中间解: 3
2025-08-05 10:29:09,690 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 7, 15, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 26, 31, 10, 37, 4, 48, 8, 49, 33, 29, 9, 38, 32, 44, 11, 46, 3, 17, 50, 45, 47, 22, 6, 25, 30, 27, 2, 19, 34, 35, 28, 20, 1, 21, 23, 42, 39], 'cur_cost': 629.0, 'intermediate_solutions': [{'tour': [0, 9, 17, 2, 19, 34, 35, 27, 30, 7, 25, 6, 22, 23, 5, 24, 12, 40, 18, 41, 43, 14, 36, 16, 3, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 38, 32, 44, 39, 13, 47, 21, 42], 'cur_cost': 646.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 17, 2, 19, 34, 35, 27, 30, 7, 25, 6, 22, 23, 13, 24, 12, 40, 18, 41, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 38, 32, 44, 39, 5, 47, 21, 42], 'cur_cost': 683.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 17, 2, 19, 34, 35, 30, 7, 25, 6, 22, 23, 13, 24, 12, 40, 18, 41, 43, 14, 36, 16, 3, 46, 11, 45, 50, 26, 27, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 38, 32, 44, 39, 5, 47, 21, 42], 'cur_cost': 667.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:09,690 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 629.00)
2025-08-05 10:29:09,690 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:29:09,690 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:09,690 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:09,691 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 1544.0
2025-08-05 10:29:09,697 - ExploitationExpert - INFO - res_population_num: 8
2025-08-05 10:29:09,697 - ExploitationExpert - INFO - res_population_costs: [426, 428, 430, 434, 434, 434, 435, 441.0]
2025-08-05 10:29:09,697 - ExploitationExpert - INFO - res_populations: [array([ 0, 31, 10, 37,  4, 36, 16,  3, 17, 46, 11, 45, 50, 26,  5, 47, 22,
        6, 42, 23, 13, 24, 12, 40, 39, 18, 41, 43, 14, 44, 32, 38,  9, 48,
        8, 29, 33, 49, 15, 20, 28,  1, 19, 34, 35,  2, 27, 30, 25,  7, 21],
      dtype=int64), array([ 0, 21,  1, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5,
       26, 50, 45, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18, 41, 43, 16, 36,
       14, 44, 32, 38,  9, 29, 33, 20, 28, 15, 49,  8, 48,  4, 37, 10, 31],
      dtype=int64), array([ 0, 21,  1, 15, 49,  8, 48,  4, 37, 10, 31, 50, 45, 11, 46,  3, 17,
       13, 24, 12, 40, 39, 18, 41, 43, 16, 36, 14, 44, 32, 38,  9, 29, 33,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 48,  4, 37,  8, 29, 33, 49, 15,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 18, 39,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 48,  4, 37,  8, 29, 33, 49, 15,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 31, 10, 37,  4, 48,  9, 38, 29, 33, 49,  8, 15, 20, 28,
       19, 34, 35,  2, 27, 30, 25,  7, 47,  5, 22,  6, 42, 23, 13, 24, 12,
       40, 18, 39, 41, 43, 44, 32, 14, 36, 16,  3, 17, 46, 11, 45, 50, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 29,  8, 48,  4, 37, 15, 49, 33,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21, 27, 30, 25,  7, 47, 26,  5, 22,  6, 42, 23, 13, 24, 12, 40,
       39, 18, 41, 43, 14, 44, 32, 38,  9, 29,  8, 48,  4, 36, 16,  3, 17,
       46, 11, 50, 45, 10, 37, 15, 49, 33, 20, 28, 19, 34, 35,  2,  1, 31],
      dtype=int64)]
2025-08-05 10:29:09,700 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:09,700 - ExploitationExpert - INFO - populations: [{'tour': [38, 8, 33, 7, 40, 30, 50, 45, 19, 34, 16, 3, 17, 49, 43, 41, 18, 0, 10, 20, 37, 4, 26, 29, 42, 23, 15, 1, 28, 24, 31, 12, 35, 2, 48, 13, 32, 25, 6, 22, 47, 11, 44, 5, 39, 9, 36, 27, 21, 14, 46], 'cur_cost': 1479.0}, {'tour': [38, 9, 17, 15, 50, 26, 40, 27, 11, 37, 35, 3, 18, 23, 13, 24, 12, 25, 41, 31, 30, 5, 47, 29, 2, 46, 16, 1, 10, 43, 45, 49, 20, 4, 8, 48, 33, 7, 6, 22, 44, 36, 34, 21, 42, 39, 14, 32, 28, 19, 0], 'cur_cost': 1421.0}, {'tour': [13, 24, 17, 39, 5, 25, 47, 6, 30, 0, 46, 14, 16, 43, 12, 10, 15, 37, 20, 1, 8, 33, 34, 4, 11, 18, 23, 22, 42, 31, 28, 27, 35, 21, 2, 50, 36, 45, 7, 26, 49, 38, 48, 29, 32, 44, 9, 3, 40, 41, 19], 'cur_cost': 1076.0}, {'tour': [13, 24, 21, 39, 40, 25, 47, 2, 32, 0, 35, 6, 18, 14, 12, 10, 15, 37, 20, 31, 8, 4, 34, 7, 11, 30, 16, 22, 28, 43, 41, 49, 26, 5, 27, 50, 36, 45, 1, 9, 33, 38, 44, 29, 42, 3, 46, 23, 48, 19, 17], 'cur_cost': 1523.0}, {'tour': [0, 7, 15, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 26, 31, 10, 37, 4, 48, 8, 49, 33, 29, 9, 38, 32, 44, 11, 46, 3, 17, 50, 45, 47, 22, 6, 25, 30, 27, 2, 19, 34, 35, 28, 20, 1, 21, 23, 42, 39], 'cur_cost': 629.0}, {'tour': array([ 9, 43, 50, 18, 32, 27, 29, 20, 49, 39, 38, 16,  5, 22, 36, 31, 19,
        6, 11, 33, 48, 37,  8, 14, 15,  1, 44, 40, 25, 17,  2, 46, 28, 35,
       26, 12, 24,  0, 30, 23, 10, 45, 34,  7, 13,  4,  3, 42, 21, 47, 41],
      dtype=int64), 'cur_cost': 1544.0}, {'tour': [38, 5, 15, 0, 7, 22, 39, 48, 18, 13, 19, 24, 29, 25, 46, 30, 40, 49, 23, 6, 1, 45, 21, 32, 27, 34, 2, 42, 16, 17, 20, 28, 10, 3, 14, 47, 26, 35, 4, 37, 50, 36, 11, 31, 12, 44, 41, 8, 33, 43, 9], 'cur_cost': 1593.0}, {'tour': [17, 40, 16, 10, 28, 19, 7, 2, 49, 31, 36, 18, 4, 11, 14, 8, 37, 48, 32, 46, 43, 29, 50, 22, 0, 13, 12, 45, 5, 24, 6, 23, 30, 27, 26, 1, 33, 15, 35, 9, 38, 21, 34, 3, 44, 41, 39, 42, 25, 47, 20], 'cur_cost': 1201.0}, {'tour': [29, 17, 5, 36, 25, 18, 41, 43, 14, 22, 47, 16, 20, 37, 31, 48, 2, 26, 15, 10, 34, 7, 11, 33, 49, 27, 1, 28, 24, 32, 35, 12, 8, 13, 4, 9, 6, 46, 23, 38, 44, 0, 30, 39, 42, 50, 40, 19, 3, 45, 21], 'cur_cost': 1674.0}, {'tour': [0, 11, 18, 14, 43, 36, 16, 3, 17, 46, 50, 45, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 41, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 6, 22, 23, 42, 39], 'cur_cost': 598.0}]
2025-08-05 10:29:09,701 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:09,701 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 330, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 330, 'cache_hits': 0, 'similarity_calculations': 1678, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:09,701 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([ 9, 43, 50, 18, 32, 27, 29, 20, 49, 39, 38, 16,  5, 22, 36, 31, 19,
        6, 11, 33, 48, 37,  8, 14, 15,  1, 44, 40, 25, 17,  2, 46, 28, 35,
       26, 12, 24,  0, 30, 23, 10, 45, 34,  7, 13,  4,  3, 42, 21, 47, 41],
      dtype=int64), 'cur_cost': 1544.0, 'intermediate_solutions': [{'tour': array([18, 50, 17, 15, 35,  4, 48,  6, 32, 37, 10,  1, 12, 43,  0, 44, 20,
       25, 41, 22, 30, 31, 19, 36,  2, 42, 34, 49,  7, 33, 45, 23, 14,  5,
       27, 47, 46, 13,  8, 26, 29,  3, 16, 21, 39, 38, 40,  9, 28, 11, 24]), 'cur_cost': 1761.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([15, 18, 50, 17, 35,  4, 48,  6, 32, 37, 10,  1, 12, 43,  0, 44, 20,
       25, 41, 22, 30, 31, 19, 36,  2, 42, 34, 49,  7, 33, 45, 23, 14,  5,
       27, 47, 46, 13,  8, 26, 29,  3, 16, 21, 39, 38, 40,  9, 28, 11, 24]), 'cur_cost': 1820.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([35, 15, 18, 50, 17,  4, 48,  6, 32, 37, 10,  1, 12, 43,  0, 44, 20,
       25, 41, 22, 30, 31, 19, 36,  2, 42, 34, 49,  7, 33, 45, 23, 14,  5,
       27, 47, 46, 13,  8, 26, 29,  3, 16, 21, 39, 38, 40,  9, 28, 11, 24]), 'cur_cost': 1789.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([17, 15, 18, 50, 35,  4, 48,  6, 32, 37, 10,  1, 12, 43,  0, 44, 20,
       25, 41, 22, 30, 31, 19, 36,  2, 42, 34, 49,  7, 33, 45, 23, 14,  5,
       27, 47, 46, 13,  8, 26, 29,  3, 16, 21, 39, 38, 40,  9, 28, 11, 24]), 'cur_cost': 1793.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([17, 35, 15, 18, 50,  4, 48,  6, 32, 37, 10,  1, 12, 43,  0, 44, 20,
       25, 41, 22, 30, 31, 19, 36,  2, 42, 34, 49,  7, 33, 45, 23, 14,  5,
       27, 47, 46, 13,  8, 26, 29,  3, 16, 21, 39, 38, 40,  9, 28, 11, 24]), 'cur_cost': 1770.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:09,702 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1544.00)
2025-08-05 10:29:09,702 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:29:09,702 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:09,702 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:09,702 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1608.0
2025-08-05 10:29:09,709 - ExploitationExpert - INFO - res_population_num: 8
2025-08-05 10:29:09,709 - ExploitationExpert - INFO - res_population_costs: [426, 428, 430, 434, 434, 434, 435, 441.0]
2025-08-05 10:29:09,709 - ExploitationExpert - INFO - res_populations: [array([ 0, 31, 10, 37,  4, 36, 16,  3, 17, 46, 11, 45, 50, 26,  5, 47, 22,
        6, 42, 23, 13, 24, 12, 40, 39, 18, 41, 43, 14, 44, 32, 38,  9, 48,
        8, 29, 33, 49, 15, 20, 28,  1, 19, 34, 35,  2, 27, 30, 25,  7, 21],
      dtype=int64), array([ 0, 21,  1, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5,
       26, 50, 45, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18, 41, 43, 16, 36,
       14, 44, 32, 38,  9, 29, 33, 20, 28, 15, 49,  8, 48,  4, 37, 10, 31],
      dtype=int64), array([ 0, 21,  1, 15, 49,  8, 48,  4, 37, 10, 31, 50, 45, 11, 46,  3, 17,
       13, 24, 12, 40, 39, 18, 41, 43, 16, 36, 14, 44, 32, 38,  9, 29, 33,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 48,  4, 37,  8, 29, 33, 49, 15,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 18, 39,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 48,  4, 37,  8, 29, 33, 49, 15,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 31, 10, 37,  4, 48,  9, 38, 29, 33, 49,  8, 15, 20, 28,
       19, 34, 35,  2, 27, 30, 25,  7, 47,  5, 22,  6, 42, 23, 13, 24, 12,
       40, 18, 39, 41, 43, 44, 32, 14, 36, 16,  3, 17, 46, 11, 45, 50, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 29,  8, 48,  4, 37, 15, 49, 33,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21, 27, 30, 25,  7, 47, 26,  5, 22,  6, 42, 23, 13, 24, 12, 40,
       39, 18, 41, 43, 14, 44, 32, 38,  9, 29,  8, 48,  4, 36, 16,  3, 17,
       46, 11, 50, 45, 10, 37, 15, 49, 33, 20, 28, 19, 34, 35,  2,  1, 31],
      dtype=int64)]
2025-08-05 10:29:09,711 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:09,712 - ExploitationExpert - INFO - populations: [{'tour': [38, 8, 33, 7, 40, 30, 50, 45, 19, 34, 16, 3, 17, 49, 43, 41, 18, 0, 10, 20, 37, 4, 26, 29, 42, 23, 15, 1, 28, 24, 31, 12, 35, 2, 48, 13, 32, 25, 6, 22, 47, 11, 44, 5, 39, 9, 36, 27, 21, 14, 46], 'cur_cost': 1479.0}, {'tour': [38, 9, 17, 15, 50, 26, 40, 27, 11, 37, 35, 3, 18, 23, 13, 24, 12, 25, 41, 31, 30, 5, 47, 29, 2, 46, 16, 1, 10, 43, 45, 49, 20, 4, 8, 48, 33, 7, 6, 22, 44, 36, 34, 21, 42, 39, 14, 32, 28, 19, 0], 'cur_cost': 1421.0}, {'tour': [13, 24, 17, 39, 5, 25, 47, 6, 30, 0, 46, 14, 16, 43, 12, 10, 15, 37, 20, 1, 8, 33, 34, 4, 11, 18, 23, 22, 42, 31, 28, 27, 35, 21, 2, 50, 36, 45, 7, 26, 49, 38, 48, 29, 32, 44, 9, 3, 40, 41, 19], 'cur_cost': 1076.0}, {'tour': [13, 24, 21, 39, 40, 25, 47, 2, 32, 0, 35, 6, 18, 14, 12, 10, 15, 37, 20, 31, 8, 4, 34, 7, 11, 30, 16, 22, 28, 43, 41, 49, 26, 5, 27, 50, 36, 45, 1, 9, 33, 38, 44, 29, 42, 3, 46, 23, 48, 19, 17], 'cur_cost': 1523.0}, {'tour': [0, 7, 15, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 26, 31, 10, 37, 4, 48, 8, 49, 33, 29, 9, 38, 32, 44, 11, 46, 3, 17, 50, 45, 47, 22, 6, 25, 30, 27, 2, 19, 34, 35, 28, 20, 1, 21, 23, 42, 39], 'cur_cost': 629.0}, {'tour': array([ 9, 43, 50, 18, 32, 27, 29, 20, 49, 39, 38, 16,  5, 22, 36, 31, 19,
        6, 11, 33, 48, 37,  8, 14, 15,  1, 44, 40, 25, 17,  2, 46, 28, 35,
       26, 12, 24,  0, 30, 23, 10, 45, 34,  7, 13,  4,  3, 42, 21, 47, 41],
      dtype=int64), 'cur_cost': 1544.0}, {'tour': array([20, 21, 44, 11,  9, 43, 16, 38, 30, 29, 36,  2, 32, 31, 17,  8, 41,
       33,  7, 14, 40, 18,  5, 35, 42, 23, 22, 28, 12,  6,  1, 13,  3, 47,
       25, 34,  0, 19, 27, 39, 15, 46, 45, 48, 24, 10, 50, 26, 37, 49,  4],
      dtype=int64), 'cur_cost': 1608.0}, {'tour': [17, 40, 16, 10, 28, 19, 7, 2, 49, 31, 36, 18, 4, 11, 14, 8, 37, 48, 32, 46, 43, 29, 50, 22, 0, 13, 12, 45, 5, 24, 6, 23, 30, 27, 26, 1, 33, 15, 35, 9, 38, 21, 34, 3, 44, 41, 39, 42, 25, 47, 20], 'cur_cost': 1201.0}, {'tour': [29, 17, 5, 36, 25, 18, 41, 43, 14, 22, 47, 16, 20, 37, 31, 48, 2, 26, 15, 10, 34, 7, 11, 33, 49, 27, 1, 28, 24, 32, 35, 12, 8, 13, 4, 9, 6, 46, 23, 38, 44, 0, 30, 39, 42, 50, 40, 19, 3, 45, 21], 'cur_cost': 1674.0}, {'tour': [0, 11, 18, 14, 43, 36, 16, 3, 17, 46, 50, 45, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 41, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 6, 22, 23, 42, 39], 'cur_cost': 598.0}]
2025-08-05 10:29:09,713 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:09,713 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 331, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 331, 'cache_hits': 0, 'similarity_calculations': 1683, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:09,713 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([20, 21, 44, 11,  9, 43, 16, 38, 30, 29, 36,  2, 32, 31, 17,  8, 41,
       33,  7, 14, 40, 18,  5, 35, 42, 23, 22, 28, 12,  6,  1, 13,  3, 47,
       25, 34,  0, 19, 27, 39, 15, 46, 45, 48, 24, 10, 50, 26, 37, 49,  4],
      dtype=int64), 'cur_cost': 1608.0, 'intermediate_solutions': [{'tour': array([15,  5, 38,  0,  7, 22, 39, 48, 18, 13, 19, 24, 29, 25, 46, 30, 40,
       49, 23,  6,  1, 45, 21, 32, 27, 34,  2, 42, 16, 17, 20, 28, 10,  3,
       14, 47, 26, 35,  4, 37, 50, 36, 11, 31, 12, 44, 41,  8, 33, 43,  9]), 'cur_cost': 1627.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 0, 15,  5, 38,  7, 22, 39, 48, 18, 13, 19, 24, 29, 25, 46, 30, 40,
       49, 23,  6,  1, 45, 21, 32, 27, 34,  2, 42, 16, 17, 20, 28, 10,  3,
       14, 47, 26, 35,  4, 37, 50, 36, 11, 31, 12, 44, 41,  8, 33, 43,  9]), 'cur_cost': 1660.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 7,  0, 15,  5, 38, 22, 39, 48, 18, 13, 19, 24, 29, 25, 46, 30, 40,
       49, 23,  6,  1, 45, 21, 32, 27, 34,  2, 42, 16, 17, 20, 28, 10,  3,
       14, 47, 26, 35,  4, 37, 50, 36, 11, 31, 12, 44, 41,  8, 33, 43,  9]), 'cur_cost': 1673.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([38,  0, 15,  5,  7, 22, 39, 48, 18, 13, 19, 24, 29, 25, 46, 30, 40,
       49, 23,  6,  1, 45, 21, 32, 27, 34,  2, 42, 16, 17, 20, 28, 10,  3,
       14, 47, 26, 35,  4, 37, 50, 36, 11, 31, 12, 44, 41,  8, 33, 43,  9]), 'cur_cost': 1592.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([38,  7,  0, 15,  5, 22, 39, 48, 18, 13, 19, 24, 29, 25, 46, 30, 40,
       49, 23,  6,  1, 45, 21, 32, 27, 34,  2, 42, 16, 17, 20, 28, 10,  3,
       14, 47, 26, 35,  4, 37, 50, 36, 11, 31, 12, 44, 41,  8, 33, 43,  9]), 'cur_cost': 1593.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:09,714 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1608.00)
2025-08-05 10:29:09,714 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:09,714 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:09,714 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:09,715 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 10:29:09,716 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,716 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,716 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,716 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,716 - ExplorationExpert - INFO - 探索路径生成完成，成本: 609.0, 路径长度: 51, 收集中间解: 3
2025-08-05 10:29:09,717 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 5, 8, 18, 40, 12, 24, 13, 17, 3, 16, 36, 14, 43, 41, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 9, 29, 33, 49, 15, 1, 28, 20, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 21, 38, 32, 44], 'cur_cost': 609.0, 'intermediate_solutions': [{'tour': [17, 40, 16, 10, 28, 19, 7, 2, 49, 31, 36, 18, 4, 11, 14, 8, 37, 48, 32, 46, 43, 29, 50, 22, 0, 15, 12, 45, 5, 24, 6, 23, 30, 27, 26, 1, 33, 13, 35, 9, 38, 21, 34, 3, 44, 41, 39, 42, 25, 47, 20], 'cur_cost': 1291.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [17, 40, 16, 10, 28, 19, 7, 2, 49, 31, 36, 18, 4, 11, 14, 8, 37, 48, 32, 46, 43, 29, 50, 22, 0, 13, 12, 45, 5, 24, 6, 23, 30, 27, 26, 1, 33, 15, 35, 9, 38, 21, 34, 3, 44, 41, 25, 42, 39, 47, 20], 'cur_cost': 1283.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [17, 40, 16, 10, 28, 19, 7, 2, 49, 31, 36, 18, 4, 11, 14, 8, 37, 48, 32, 46, 43, 29, 50, 22, 13, 12, 0, 45, 5, 24, 6, 23, 30, 27, 26, 1, 33, 15, 35, 9, 38, 21, 34, 3, 44, 41, 39, 42, 25, 47, 20], 'cur_cost': 1194.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:09,717 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 609.00)
2025-08-05 10:29:09,717 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:29:09,717 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:09,717 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:09,717 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 1652.0
2025-08-05 10:29:09,726 - ExploitationExpert - INFO - res_population_num: 8
2025-08-05 10:29:09,726 - ExploitationExpert - INFO - res_population_costs: [426, 428, 430, 434, 434, 434, 435, 441.0]
2025-08-05 10:29:09,727 - ExploitationExpert - INFO - res_populations: [array([ 0, 31, 10, 37,  4, 36, 16,  3, 17, 46, 11, 45, 50, 26,  5, 47, 22,
        6, 42, 23, 13, 24, 12, 40, 39, 18, 41, 43, 14, 44, 32, 38,  9, 48,
        8, 29, 33, 49, 15, 20, 28,  1, 19, 34, 35,  2, 27, 30, 25,  7, 21],
      dtype=int64), array([ 0, 21,  1, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5,
       26, 50, 45, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18, 41, 43, 16, 36,
       14, 44, 32, 38,  9, 29, 33, 20, 28, 15, 49,  8, 48,  4, 37, 10, 31],
      dtype=int64), array([ 0, 21,  1, 15, 49,  8, 48,  4, 37, 10, 31, 50, 45, 11, 46,  3, 17,
       13, 24, 12, 40, 39, 18, 41, 43, 16, 36, 14, 44, 32, 38,  9, 29, 33,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 48,  4, 37,  8, 29, 33, 49, 15,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 18, 39,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 48,  4, 37,  8, 29, 33, 49, 15,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 31, 10, 37,  4, 48,  9, 38, 29, 33, 49,  8, 15, 20, 28,
       19, 34, 35,  2, 27, 30, 25,  7, 47,  5, 22,  6, 42, 23, 13, 24, 12,
       40, 18, 39, 41, 43, 44, 32, 14, 36, 16,  3, 17, 46, 11, 45, 50, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 29,  8, 48,  4, 37, 15, 49, 33,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21, 27, 30, 25,  7, 47, 26,  5, 22,  6, 42, 23, 13, 24, 12, 40,
       39, 18, 41, 43, 14, 44, 32, 38,  9, 29,  8, 48,  4, 36, 16,  3, 17,
       46, 11, 50, 45, 10, 37, 15, 49, 33, 20, 28, 19, 34, 35,  2,  1, 31],
      dtype=int64)]
2025-08-05 10:29:09,729 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:09,729 - ExploitationExpert - INFO - populations: [{'tour': [38, 8, 33, 7, 40, 30, 50, 45, 19, 34, 16, 3, 17, 49, 43, 41, 18, 0, 10, 20, 37, 4, 26, 29, 42, 23, 15, 1, 28, 24, 31, 12, 35, 2, 48, 13, 32, 25, 6, 22, 47, 11, 44, 5, 39, 9, 36, 27, 21, 14, 46], 'cur_cost': 1479.0}, {'tour': [38, 9, 17, 15, 50, 26, 40, 27, 11, 37, 35, 3, 18, 23, 13, 24, 12, 25, 41, 31, 30, 5, 47, 29, 2, 46, 16, 1, 10, 43, 45, 49, 20, 4, 8, 48, 33, 7, 6, 22, 44, 36, 34, 21, 42, 39, 14, 32, 28, 19, 0], 'cur_cost': 1421.0}, {'tour': [13, 24, 17, 39, 5, 25, 47, 6, 30, 0, 46, 14, 16, 43, 12, 10, 15, 37, 20, 1, 8, 33, 34, 4, 11, 18, 23, 22, 42, 31, 28, 27, 35, 21, 2, 50, 36, 45, 7, 26, 49, 38, 48, 29, 32, 44, 9, 3, 40, 41, 19], 'cur_cost': 1076.0}, {'tour': [13, 24, 21, 39, 40, 25, 47, 2, 32, 0, 35, 6, 18, 14, 12, 10, 15, 37, 20, 31, 8, 4, 34, 7, 11, 30, 16, 22, 28, 43, 41, 49, 26, 5, 27, 50, 36, 45, 1, 9, 33, 38, 44, 29, 42, 3, 46, 23, 48, 19, 17], 'cur_cost': 1523.0}, {'tour': [0, 7, 15, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 26, 31, 10, 37, 4, 48, 8, 49, 33, 29, 9, 38, 32, 44, 11, 46, 3, 17, 50, 45, 47, 22, 6, 25, 30, 27, 2, 19, 34, 35, 28, 20, 1, 21, 23, 42, 39], 'cur_cost': 629.0}, {'tour': array([ 9, 43, 50, 18, 32, 27, 29, 20, 49, 39, 38, 16,  5, 22, 36, 31, 19,
        6, 11, 33, 48, 37,  8, 14, 15,  1, 44, 40, 25, 17,  2, 46, 28, 35,
       26, 12, 24,  0, 30, 23, 10, 45, 34,  7, 13,  4,  3, 42, 21, 47, 41],
      dtype=int64), 'cur_cost': 1544.0}, {'tour': array([20, 21, 44, 11,  9, 43, 16, 38, 30, 29, 36,  2, 32, 31, 17,  8, 41,
       33,  7, 14, 40, 18,  5, 35, 42, 23, 22, 28, 12,  6,  1, 13,  3, 47,
       25, 34,  0, 19, 27, 39, 15, 46, 45, 48, 24, 10, 50, 26, 37, 49,  4],
      dtype=int64), 'cur_cost': 1608.0}, {'tour': [0, 5, 8, 18, 40, 12, 24, 13, 17, 3, 16, 36, 14, 43, 41, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 9, 29, 33, 49, 15, 1, 28, 20, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 21, 38, 32, 44], 'cur_cost': 609.0}, {'tour': array([35,  0, 28, 48,  5, 20, 16,  6,  7, 19, 24,  9, 18, 25, 47, 34, 37,
       26, 10, 21, 32, 12, 30, 29,  4,  8, 27,  1, 31, 33,  3, 50, 42, 14,
       36, 38, 49, 11, 22, 43, 39,  2, 17, 13, 15, 45, 23, 44, 46, 41, 40],
      dtype=int64), 'cur_cost': 1652.0}, {'tour': [0, 11, 18, 14, 43, 36, 16, 3, 17, 46, 50, 45, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 41, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 6, 22, 23, 42, 39], 'cur_cost': 598.0}]
2025-08-05 10:29:09,731 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:09,731 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 332, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 332, 'cache_hits': 0, 'similarity_calculations': 1689, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:09,732 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([35,  0, 28, 48,  5, 20, 16,  6,  7, 19, 24,  9, 18, 25, 47, 34, 37,
       26, 10, 21, 32, 12, 30, 29,  4,  8, 27,  1, 31, 33,  3, 50, 42, 14,
       36, 38, 49, 11, 22, 43, 39,  2, 17, 13, 15, 45, 23, 44, 46, 41, 40],
      dtype=int64), 'cur_cost': 1652.0, 'intermediate_solutions': [{'tour': array([ 5, 17, 29, 36, 25, 18, 41, 43, 14, 22, 47, 16, 20, 37, 31, 48,  2,
       26, 15, 10, 34,  7, 11, 33, 49, 27,  1, 28, 24, 32, 35, 12,  8, 13,
        4,  9,  6, 46, 23, 38, 44,  0, 30, 39, 42, 50, 40, 19,  3, 45, 21]), 'cur_cost': 1662.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([36,  5, 17, 29, 25, 18, 41, 43, 14, 22, 47, 16, 20, 37, 31, 48,  2,
       26, 15, 10, 34,  7, 11, 33, 49, 27,  1, 28, 24, 32, 35, 12,  8, 13,
        4,  9,  6, 46, 23, 38, 44,  0, 30, 39, 42, 50, 40, 19,  3, 45, 21]), 'cur_cost': 1681.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([25, 36,  5, 17, 29, 18, 41, 43, 14, 22, 47, 16, 20, 37, 31, 48,  2,
       26, 15, 10, 34,  7, 11, 33, 49, 27,  1, 28, 24, 32, 35, 12,  8, 13,
        4,  9,  6, 46, 23, 38, 44,  0, 30, 39, 42, 50, 40, 19,  3, 45, 21]), 'cur_cost': 1649.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([29, 36,  5, 17, 25, 18, 41, 43, 14, 22, 47, 16, 20, 37, 31, 48,  2,
       26, 15, 10, 34,  7, 11, 33, 49, 27,  1, 28, 24, 32, 35, 12,  8, 13,
        4,  9,  6, 46, 23, 38, 44,  0, 30, 39, 42, 50, 40, 19,  3, 45, 21]), 'cur_cost': 1649.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([29, 25, 36,  5, 17, 18, 41, 43, 14, 22, 47, 16, 20, 37, 31, 48,  2,
       26, 15, 10, 34,  7, 11, 33, 49, 27,  1, 28, 24, 32, 35, 12,  8, 13,
        4,  9,  6, 46, 23, 38, 44,  0, 30, 39, 42, 50, 40, 19,  3, 45, 21]), 'cur_cost': 1647.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:09,732 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1652.00)
2025-08-05 10:29:09,732 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:29:09,732 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:29:09,732 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:09,734 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 10:29:09,734 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,734 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,735 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,735 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,735 - ExplorationExpert - INFO - 探索路径生成完成，成本: 600.0, 路径长度: 51, 收集中间解: 3
2025-08-05 10:29:09,735 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 10, 12, 18, 40, 39, 41, 43, 14, 36, 16, 3, 17, 46, 11, 45, 50, 26, 31, 1, 15, 49, 8, 48, 4, 37, 9, 29, 33, 20, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 5, 47, 21, 42, 44, 32, 38], 'cur_cost': 600.0, 'intermediate_solutions': [{'tour': [0, 11, 18, 14, 43, 36, 16, 3, 32, 46, 50, 45, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 17, 44, 41, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 6, 22, 23, 42, 39], 'cur_cost': 700.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 18, 42, 23, 22, 6, 21, 35, 34, 19, 2, 27, 30, 25, 7, 47, 5, 13, 24, 12, 40, 41, 44, 32, 38, 9, 29, 33, 20, 28, 1, 15, 49, 8, 48, 4, 37, 10, 31, 26, 45, 50, 46, 17, 3, 16, 36, 43, 14, 39], 'cur_cost': 602.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 18, 14, 43, 16, 3, 17, 46, 50, 36, 45, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 41, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 6, 22, 23, 42, 39], 'cur_cost': 628.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:09,735 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 600.00)
2025-08-05 10:29:09,736 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:09,736 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:09,738 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [38, 8, 33, 7, 40, 30, 50, 45, 19, 34, 16, 3, 17, 49, 43, 41, 18, 0, 10, 20, 37, 4, 26, 29, 42, 23, 15, 1, 28, 24, 31, 12, 35, 2, 48, 13, 32, 25, 6, 22, 47, 11, 44, 5, 39, 9, 36, 27, 21, 14, 46], 'cur_cost': 1479.0, 'intermediate_solutions': [{'tour': [0, 9, 8, 12, 40, 18, 41, 6, 14, 36, 16, 3, 17, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 29, 33, 49, 15, 1, 28, 20, 19, 34, 35, 2, 27, 30, 7, 25, 43, 22, 23, 13, 24, 5, 47, 21, 42, 39, 44, 32, 38], 'cur_cost': 803.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 8, 12, 40, 18, 41, 43, 14, 36, 16, 3, 17, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 29, 33, 25, 7, 30, 27, 2, 35, 34, 19, 20, 28, 1, 15, 49, 6, 22, 23, 13, 24, 5, 47, 21, 42, 39, 44, 32, 38], 'cur_cost': 714.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 8, 12, 40, 18, 41, 43, 14, 36, 16, 3, 17, 46, 11, 45, 50, 26, 31, 10, 37, 4, 29, 33, 49, 48, 15, 1, 28, 20, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 5, 47, 21, 42, 39, 44, 32, 38], 'cur_cost': 655.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [38, 9, 17, 15, 50, 26, 40, 27, 11, 37, 35, 3, 18, 23, 13, 24, 12, 25, 41, 31, 30, 5, 47, 29, 2, 46, 16, 1, 10, 43, 45, 49, 20, 4, 8, 48, 33, 7, 6, 22, 44, 36, 34, 21, 42, 39, 14, 32, 28, 19, 0], 'cur_cost': 1421.0, 'intermediate_solutions': [{'tour': [29, 8, 4, 36, 50, 14, 40, 17, 45, 22, 47, 24, 18, 37, 31, 21, 2, 0, 15, 20, 34, 7, 26, 25, 42, 23, 5, 6, 10, 1, 35, 49, 38, 43, 16, 48, 32, 9, 33, 46, 41, 11, 44, 12, 39, 13, 30, 19, 28, 27, 3], 'cur_cost': 1097.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [29, 8, 33, 36, 50, 14, 40, 17, 45, 22, 47, 24, 18, 37, 31, 21, 2, 0, 15, 20, 34, 7, 26, 25, 5, 23, 42, 6, 10, 1, 35, 49, 38, 43, 16, 48, 32, 9, 4, 46, 41, 11, 44, 12, 39, 13, 30, 19, 28, 27, 3], 'cur_cost': 1087.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [29, 8, 0, 33, 36, 50, 14, 40, 17, 45, 22, 47, 24, 18, 37, 31, 21, 2, 15, 20, 34, 7, 26, 25, 42, 23, 5, 6, 10, 1, 35, 49, 38, 43, 16, 48, 32, 9, 4, 46, 41, 11, 44, 12, 39, 13, 30, 19, 28, 27, 3], 'cur_cost': 1122.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [13, 24, 17, 39, 5, 25, 47, 6, 30, 0, 46, 14, 16, 43, 12, 10, 15, 37, 20, 1, 8, 33, 34, 4, 11, 18, 23, 22, 42, 31, 28, 27, 35, 21, 2, 50, 36, 45, 7, 26, 49, 38, 48, 29, 32, 44, 9, 3, 40, 41, 19], 'cur_cost': 1076.0, 'intermediate_solutions': [{'tour': [0, 1, 5, 7, 25, 30, 27, 2, 19, 34, 35, 11, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 28, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 17, 23, 22, 6, 47, 21, 15, 33, 29, 9, 38, 32, 44, 39, 42], 'cur_cost': 688.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 5, 7, 25, 30, 27, 2, 14, 36, 16, 3, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 20, 28, 35, 34, 19, 43, 41, 18, 40, 12, 24, 13, 17, 23, 22, 6, 47, 21, 15, 33, 29, 9, 38, 32, 44, 39, 42], 'cur_cost': 671.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 5, 7, 25, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 17, 23, 22, 33, 6, 47, 21, 15, 29, 9, 38, 32, 44, 39, 42], 'cur_cost': 677.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [13, 24, 21, 39, 40, 25, 47, 2, 32, 0, 35, 6, 18, 14, 12, 10, 15, 37, 20, 31, 8, 4, 34, 7, 11, 30, 16, 22, 28, 43, 41, 49, 26, 5, 27, 50, 36, 45, 1, 9, 33, 38, 44, 29, 42, 3, 46, 23, 48, 19, 17], 'cur_cost': 1523.0, 'intermediate_solutions': [{'tour': [44, 17, 21, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 15, 1, 33, 29, 9, 38, 32, 0, 39, 23, 22, 6, 42], 'cur_cost': 665.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 21, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 19, 34, 28, 35, 20, 49, 8, 48, 4, 37, 15, 1, 33, 29, 9, 38, 32, 44, 39, 23, 22, 6, 42], 'cur_cost': 603.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 21, 10, 31, 50, 45, 11, 46, 3, 26, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 15, 1, 33, 29, 9, 38, 32, 44, 39, 23, 22, 6, 42], 'cur_cost': 608.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 15, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 26, 31, 10, 37, 4, 48, 8, 49, 33, 29, 9, 38, 32, 44, 11, 46, 3, 17, 50, 45, 47, 22, 6, 25, 30, 27, 2, 19, 34, 35, 28, 20, 1, 21, 23, 42, 39], 'cur_cost': 629.0, 'intermediate_solutions': [{'tour': [0, 9, 17, 2, 19, 34, 35, 27, 30, 7, 25, 6, 22, 23, 5, 24, 12, 40, 18, 41, 43, 14, 36, 16, 3, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 38, 32, 44, 39, 13, 47, 21, 42], 'cur_cost': 646.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 17, 2, 19, 34, 35, 27, 30, 7, 25, 6, 22, 23, 13, 24, 12, 40, 18, 41, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 38, 32, 44, 39, 5, 47, 21, 42], 'cur_cost': 683.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 17, 2, 19, 34, 35, 30, 7, 25, 6, 22, 23, 13, 24, 12, 40, 18, 41, 43, 14, 36, 16, 3, 46, 11, 45, 50, 26, 27, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 38, 32, 44, 39, 5, 47, 21, 42], 'cur_cost': 667.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 9, 43, 50, 18, 32, 27, 29, 20, 49, 39, 38, 16,  5, 22, 36, 31, 19,
        6, 11, 33, 48, 37,  8, 14, 15,  1, 44, 40, 25, 17,  2, 46, 28, 35,
       26, 12, 24,  0, 30, 23, 10, 45, 34,  7, 13,  4,  3, 42, 21, 47, 41],
      dtype=int64), 'cur_cost': 1544.0, 'intermediate_solutions': [{'tour': array([18, 50, 17, 15, 35,  4, 48,  6, 32, 37, 10,  1, 12, 43,  0, 44, 20,
       25, 41, 22, 30, 31, 19, 36,  2, 42, 34, 49,  7, 33, 45, 23, 14,  5,
       27, 47, 46, 13,  8, 26, 29,  3, 16, 21, 39, 38, 40,  9, 28, 11, 24]), 'cur_cost': 1761.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([15, 18, 50, 17, 35,  4, 48,  6, 32, 37, 10,  1, 12, 43,  0, 44, 20,
       25, 41, 22, 30, 31, 19, 36,  2, 42, 34, 49,  7, 33, 45, 23, 14,  5,
       27, 47, 46, 13,  8, 26, 29,  3, 16, 21, 39, 38, 40,  9, 28, 11, 24]), 'cur_cost': 1820.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([35, 15, 18, 50, 17,  4, 48,  6, 32, 37, 10,  1, 12, 43,  0, 44, 20,
       25, 41, 22, 30, 31, 19, 36,  2, 42, 34, 49,  7, 33, 45, 23, 14,  5,
       27, 47, 46, 13,  8, 26, 29,  3, 16, 21, 39, 38, 40,  9, 28, 11, 24]), 'cur_cost': 1789.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([17, 15, 18, 50, 35,  4, 48,  6, 32, 37, 10,  1, 12, 43,  0, 44, 20,
       25, 41, 22, 30, 31, 19, 36,  2, 42, 34, 49,  7, 33, 45, 23, 14,  5,
       27, 47, 46, 13,  8, 26, 29,  3, 16, 21, 39, 38, 40,  9, 28, 11, 24]), 'cur_cost': 1793.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([17, 35, 15, 18, 50,  4, 48,  6, 32, 37, 10,  1, 12, 43,  0, 44, 20,
       25, 41, 22, 30, 31, 19, 36,  2, 42, 34, 49,  7, 33, 45, 23, 14,  5,
       27, 47, 46, 13,  8, 26, 29,  3, 16, 21, 39, 38, 40,  9, 28, 11, 24]), 'cur_cost': 1770.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([20, 21, 44, 11,  9, 43, 16, 38, 30, 29, 36,  2, 32, 31, 17,  8, 41,
       33,  7, 14, 40, 18,  5, 35, 42, 23, 22, 28, 12,  6,  1, 13,  3, 47,
       25, 34,  0, 19, 27, 39, 15, 46, 45, 48, 24, 10, 50, 26, 37, 49,  4],
      dtype=int64), 'cur_cost': 1608.0, 'intermediate_solutions': [{'tour': array([15,  5, 38,  0,  7, 22, 39, 48, 18, 13, 19, 24, 29, 25, 46, 30, 40,
       49, 23,  6,  1, 45, 21, 32, 27, 34,  2, 42, 16, 17, 20, 28, 10,  3,
       14, 47, 26, 35,  4, 37, 50, 36, 11, 31, 12, 44, 41,  8, 33, 43,  9]), 'cur_cost': 1627.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 0, 15,  5, 38,  7, 22, 39, 48, 18, 13, 19, 24, 29, 25, 46, 30, 40,
       49, 23,  6,  1, 45, 21, 32, 27, 34,  2, 42, 16, 17, 20, 28, 10,  3,
       14, 47, 26, 35,  4, 37, 50, 36, 11, 31, 12, 44, 41,  8, 33, 43,  9]), 'cur_cost': 1660.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 7,  0, 15,  5, 38, 22, 39, 48, 18, 13, 19, 24, 29, 25, 46, 30, 40,
       49, 23,  6,  1, 45, 21, 32, 27, 34,  2, 42, 16, 17, 20, 28, 10,  3,
       14, 47, 26, 35,  4, 37, 50, 36, 11, 31, 12, 44, 41,  8, 33, 43,  9]), 'cur_cost': 1673.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([38,  0, 15,  5,  7, 22, 39, 48, 18, 13, 19, 24, 29, 25, 46, 30, 40,
       49, 23,  6,  1, 45, 21, 32, 27, 34,  2, 42, 16, 17, 20, 28, 10,  3,
       14, 47, 26, 35,  4, 37, 50, 36, 11, 31, 12, 44, 41,  8, 33, 43,  9]), 'cur_cost': 1592.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([38,  7,  0, 15,  5, 22, 39, 48, 18, 13, 19, 24, 29, 25, 46, 30, 40,
       49, 23,  6,  1, 45, 21, 32, 27, 34,  2, 42, 16, 17, 20, 28, 10,  3,
       14, 47, 26, 35,  4, 37, 50, 36, 11, 31, 12, 44, 41,  8, 33, 43,  9]), 'cur_cost': 1593.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 8, 18, 40, 12, 24, 13, 17, 3, 16, 36, 14, 43, 41, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 9, 29, 33, 49, 15, 1, 28, 20, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 21, 38, 32, 44], 'cur_cost': 609.0, 'intermediate_solutions': [{'tour': [17, 40, 16, 10, 28, 19, 7, 2, 49, 31, 36, 18, 4, 11, 14, 8, 37, 48, 32, 46, 43, 29, 50, 22, 0, 15, 12, 45, 5, 24, 6, 23, 30, 27, 26, 1, 33, 13, 35, 9, 38, 21, 34, 3, 44, 41, 39, 42, 25, 47, 20], 'cur_cost': 1291.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [17, 40, 16, 10, 28, 19, 7, 2, 49, 31, 36, 18, 4, 11, 14, 8, 37, 48, 32, 46, 43, 29, 50, 22, 0, 13, 12, 45, 5, 24, 6, 23, 30, 27, 26, 1, 33, 15, 35, 9, 38, 21, 34, 3, 44, 41, 25, 42, 39, 47, 20], 'cur_cost': 1283.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [17, 40, 16, 10, 28, 19, 7, 2, 49, 31, 36, 18, 4, 11, 14, 8, 37, 48, 32, 46, 43, 29, 50, 22, 13, 12, 0, 45, 5, 24, 6, 23, 30, 27, 26, 1, 33, 15, 35, 9, 38, 21, 34, 3, 44, 41, 39, 42, 25, 47, 20], 'cur_cost': 1194.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([35,  0, 28, 48,  5, 20, 16,  6,  7, 19, 24,  9, 18, 25, 47, 34, 37,
       26, 10, 21, 32, 12, 30, 29,  4,  8, 27,  1, 31, 33,  3, 50, 42, 14,
       36, 38, 49, 11, 22, 43, 39,  2, 17, 13, 15, 45, 23, 44, 46, 41, 40],
      dtype=int64), 'cur_cost': 1652.0, 'intermediate_solutions': [{'tour': array([ 5, 17, 29, 36, 25, 18, 41, 43, 14, 22, 47, 16, 20, 37, 31, 48,  2,
       26, 15, 10, 34,  7, 11, 33, 49, 27,  1, 28, 24, 32, 35, 12,  8, 13,
        4,  9,  6, 46, 23, 38, 44,  0, 30, 39, 42, 50, 40, 19,  3, 45, 21]), 'cur_cost': 1662.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([36,  5, 17, 29, 25, 18, 41, 43, 14, 22, 47, 16, 20, 37, 31, 48,  2,
       26, 15, 10, 34,  7, 11, 33, 49, 27,  1, 28, 24, 32, 35, 12,  8, 13,
        4,  9,  6, 46, 23, 38, 44,  0, 30, 39, 42, 50, 40, 19,  3, 45, 21]), 'cur_cost': 1681.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([25, 36,  5, 17, 29, 18, 41, 43, 14, 22, 47, 16, 20, 37, 31, 48,  2,
       26, 15, 10, 34,  7, 11, 33, 49, 27,  1, 28, 24, 32, 35, 12,  8, 13,
        4,  9,  6, 46, 23, 38, 44,  0, 30, 39, 42, 50, 40, 19,  3, 45, 21]), 'cur_cost': 1649.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([29, 36,  5, 17, 25, 18, 41, 43, 14, 22, 47, 16, 20, 37, 31, 48,  2,
       26, 15, 10, 34,  7, 11, 33, 49, 27,  1, 28, 24, 32, 35, 12,  8, 13,
        4,  9,  6, 46, 23, 38, 44,  0, 30, 39, 42, 50, 40, 19,  3, 45, 21]), 'cur_cost': 1649.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([29, 25, 36,  5, 17, 18, 41, 43, 14, 22, 47, 16, 20, 37, 31, 48,  2,
       26, 15, 10, 34,  7, 11, 33, 49, 27,  1, 28, 24, 32, 35, 12,  8, 13,
        4,  9,  6, 46, 23, 38, 44,  0, 30, 39, 42, 50, 40, 19,  3, 45, 21]), 'cur_cost': 1647.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 12, 18, 40, 39, 41, 43, 14, 36, 16, 3, 17, 46, 11, 45, 50, 26, 31, 1, 15, 49, 8, 48, 4, 37, 9, 29, 33, 20, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 5, 47, 21, 42, 44, 32, 38], 'cur_cost': 600.0, 'intermediate_solutions': [{'tour': [0, 11, 18, 14, 43, 36, 16, 3, 32, 46, 50, 45, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 17, 44, 41, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 6, 22, 23, 42, 39], 'cur_cost': 700.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 18, 42, 23, 22, 6, 21, 35, 34, 19, 2, 27, 30, 25, 7, 47, 5, 13, 24, 12, 40, 41, 44, 32, 38, 9, 29, 33, 20, 28, 1, 15, 49, 8, 48, 4, 37, 10, 31, 26, 45, 50, 46, 17, 3, 16, 36, 43, 14, 39], 'cur_cost': 602.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 18, 14, 43, 16, 3, 17, 46, 50, 36, 45, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 41, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 6, 22, 23, 42, 39], 'cur_cost': 628.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:29:09,738 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:09,738 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:09,741 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=600.000, 多样性=0.953
2025-08-05 10:29:09,741 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 10:29:09,741 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 10:29:09,741 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:09,742 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.05381899370604224, 'best_improvement': -0.04712041884816754}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.014858171994597203}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.05021264325648559, 'recent_improvements': [0.10781120432115567, -0.011273475551236858, 0.007385917808184476], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 8, 'new_count': 8, 'count_change': 0, 'old_best_cost': 426, 'new_best_cost': 426, 'quality_improvement': 0.0, 'old_diversity': 0.8130252100840336, 'new_diversity': 0.8130252100840336, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:29:09,743 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 10:29:09,743 - __main__ - INFO - eil51 开始进化第 4 代
2025-08-05 10:29:09,743 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 10:29:09,744 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:09,744 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=600.000, 多样性=0.953
2025-08-05 10:29:09,745 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:09,747 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.953
2025-08-05 10:29:09,747 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:09,748 - EliteExpert - INFO - 精英解分析完成: 精英解数量=8, 多样性=0.813
2025-08-05 10:29:09,750 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 10:29:09,750 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:09,750 - LandscapeExpert - INFO - 添加精英解数据: 8个精英解
2025-08-05 10:29:09,750 - LandscapeExpert - INFO - 数据提取成功: 18个路径, 18个适应度值
2025-08-05 10:29:09,804 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.111, 适应度梯度: -77.356, 聚类评分: 0.000, 覆盖率: 0.147, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:09,804 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 10:29:09,804 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:09,805 - visualization.landscape_visualizer - INFO - 设置当前实例名: eil51
2025-08-05 10:29:09,811 - visualization.landscape_visualizer - INFO - 插值约束: 280 个点被约束到最小值 426.00
2025-08-05 10:29:09,814 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=6.5%, 梯度: 58.22 → 54.45
2025-08-05 10:29:09,935 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_eil51\landscape_eil51_iter_129_20250805_102909.html
2025-08-05 10:29:09,991 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_eil51\dashboard_eil51_iter_129_20250805_102909.html
2025-08-05 10:29:09,992 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 129
2025-08-05 10:29:09,992 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 10:29:09,992 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2419秒
2025-08-05 10:29:09,992 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1111111111111111, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -77.35555555555557, 'local_optima_density': 0.1111111111111111, 'gradient_variance': 174222.4024691358, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1472, 'fitness_entropy': 0.8803504390378468, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -77.356)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.147)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360949.8045259, 'performance_metrics': {}}}
2025-08-05 10:29:09,992 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:09,993 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:09,993 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:09,993 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:09,993 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:29:09,993 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 10:29:09,993 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:29:09,993 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:09,994 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:09,994 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:29:09,994 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:09,994 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:09,994 - experts.management.collaboration_manager - INFO - 识别精英个体: {9, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:09,995 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:09,995 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:09,995 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:09,997 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 10:29:09,997 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,997 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,997 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,997 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:09,998 - ExplorationExpert - INFO - 探索路径生成完成，成本: 625.0, 路径长度: 51, 收集中间解: 3
2025-08-05 10:29:09,998 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 3, 1, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 17, 13, 24, 12, 40, 18, 41, 43, 14, 36, 16, 44, 32, 9, 29, 33, 20, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 21, 38, 39], 'cur_cost': 625.0, 'intermediate_solutions': [{'tour': [38, 8, 33, 49, 40, 30, 50, 45, 19, 34, 16, 3, 17, 7, 43, 41, 18, 0, 10, 20, 37, 4, 26, 29, 42, 23, 15, 1, 28, 24, 31, 12, 35, 2, 48, 13, 32, 25, 6, 22, 47, 11, 44, 5, 39, 9, 36, 27, 21, 14, 46], 'cur_cost': 1449.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [38, 8, 33, 7, 40, 30, 50, 45, 19, 34, 16, 3, 17, 49, 43, 14, 21, 27, 36, 9, 39, 5, 44, 11, 47, 22, 6, 25, 32, 13, 48, 2, 35, 12, 31, 24, 28, 1, 15, 23, 42, 29, 26, 4, 37, 20, 10, 0, 18, 41, 46], 'cur_cost': 1478.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [38, 8, 33, 7, 40, 30, 50, 45, 19, 34, 16, 3, 17, 49, 43, 41, 18, 0, 10, 20, 23, 37, 4, 26, 29, 42, 15, 1, 28, 24, 31, 12, 35, 2, 48, 13, 32, 25, 6, 22, 47, 11, 44, 5, 39, 9, 36, 27, 21, 14, 46], 'cur_cost': 1552.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:09,998 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 625.00)
2025-08-05 10:29:09,998 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:09,998 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:09,998 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:10,000 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 10:29:10,000 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,000 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,000 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,000 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,000 - ExplorationExpert - INFO - 探索路径生成完成，成本: 627.0, 路径长度: 51, 收集中间解: 3
2025-08-05 10:29:10,001 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 17, 1, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 26, 31, 10, 37, 4, 48, 8, 49, 15, 28, 20, 33, 29, 9, 38, 32, 44, 45, 50, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 6, 22, 23, 42, 39], 'cur_cost': 627.0, 'intermediate_solutions': [{'tour': [38, 9, 17, 15, 50, 26, 40, 27, 11, 37, 35, 3, 18, 23, 13, 24, 12, 25, 41, 31, 30, 5, 47, 29, 2, 46, 16, 1, 36, 43, 45, 49, 20, 4, 8, 48, 33, 7, 6, 22, 44, 10, 34, 21, 42, 39, 14, 32, 28, 19, 0], 'cur_cost': 1416.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [38, 9, 17, 15, 50, 26, 40, 27, 11, 37, 35, 3, 18, 23, 13, 24, 12, 25, 41, 31, 30, 5, 47, 29, 2, 46, 16, 1, 10, 43, 45, 49, 20, 4, 8, 48, 33, 7, 6, 22, 44, 36, 34, 21, 42, 28, 32, 14, 39, 19, 0], 'cur_cost': 1482.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [38, 9, 17, 15, 50, 26, 40, 27, 11, 37, 35, 3, 18, 23, 13, 24, 12, 25, 41, 31, 30, 5, 47, 29, 2, 46, 16, 1, 10, 43, 45, 49, 20, 4, 8, 48, 33, 7, 6, 22, 44, 36, 21, 42, 34, 39, 14, 32, 28, 19, 0], 'cur_cost': 1465.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:10,001 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 627.00)
2025-08-05 10:29:10,001 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:10,001 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:10,001 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:10,002 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 51
2025-08-05 10:29:10,002 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,003 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,003 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,003 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,003 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1440.0, 路径长度: 51, 收集中间解: 3
2025-08-05 10:29:10,003 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [9, 3, 15, 16, 49, 14, 43, 4, 37, 10, 41, 18, 40, 12, 26, 46, 17, 13, 24, 48, 32, 33, 5, 8, 42, 36, 28, 20, 11, 29, 2, 38, 35, 44, 45, 22, 47, 25, 7, 27, 39, 19, 6, 34, 23, 21, 1, 50, 31, 30, 0], 'cur_cost': 1440.0, 'intermediate_solutions': [{'tour': [13, 24, 17, 39, 5, 25, 47, 23, 30, 0, 46, 14, 16, 43, 12, 10, 15, 37, 20, 1, 8, 33, 34, 4, 11, 18, 6, 22, 42, 31, 28, 27, 35, 21, 2, 50, 36, 45, 7, 26, 49, 38, 48, 29, 32, 44, 9, 3, 40, 41, 19], 'cur_cost': 1103.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [13, 24, 17, 39, 5, 25, 47, 6, 30, 0, 46, 14, 16, 43, 12, 10, 15, 37, 20, 1, 8, 48, 38, 49, 26, 7, 45, 36, 50, 2, 21, 35, 27, 28, 31, 42, 22, 23, 18, 11, 4, 34, 33, 29, 32, 44, 9, 3, 40, 41, 19], 'cur_cost': 1070.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [13, 24, 17, 39, 5, 25, 47, 6, 30, 0, 46, 14, 16, 43, 12, 21, 10, 15, 37, 20, 1, 8, 33, 34, 4, 11, 18, 23, 22, 42, 31, 28, 27, 35, 2, 50, 36, 45, 7, 26, 49, 38, 48, 29, 32, 44, 9, 3, 40, 41, 19], 'cur_cost': 1077.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:10,003 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1440.00)
2025-08-05 10:29:10,003 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:10,004 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:10,004 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:10,005 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 10:29:10,005 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,006 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,006 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,006 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,006 - ExplorationExpert - INFO - 探索路径生成完成，成本: 584.0, 路径长度: 51, 收集中间解: 3
2025-08-05 10:29:10,006 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 2, 1, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 28, 20, 33, 29, 9, 38, 32, 44, 5, 47, 7, 25, 30, 27, 21, 19, 34, 35, 6, 22, 23, 42], 'cur_cost': 584.0, 'intermediate_solutions': [{'tour': [13, 24, 21, 39, 40, 25, 48, 2, 32, 0, 35, 6, 18, 14, 12, 10, 15, 37, 20, 31, 8, 4, 34, 7, 11, 30, 16, 22, 28, 43, 41, 49, 26, 5, 27, 50, 36, 45, 1, 9, 33, 38, 44, 29, 42, 3, 46, 23, 47, 19, 17], 'cur_cost': 1534.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [13, 24, 21, 39, 40, 25, 47, 2, 32, 0, 35, 6, 18, 14, 12, 10, 15, 37, 20, 31, 8, 4, 34, 7, 11, 30, 16, 22, 28, 43, 41, 49, 26, 5, 27, 50, 36, 45, 1, 9, 33, 38, 44, 29, 42, 3, 17, 19, 48, 23, 46], 'cur_cost': 1529.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [13, 24, 21, 39, 40, 25, 47, 2, 32, 0, 35, 6, 18, 14, 12, 10, 15, 37, 20, 31, 8, 4, 34, 7, 11, 30, 16, 22, 28, 43, 41, 49, 26, 5, 27, 50, 36, 45, 29, 1, 9, 33, 38, 44, 42, 3, 46, 23, 48, 19, 17], 'cur_cost': 1530.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:10,006 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 584.00)
2025-08-05 10:29:10,007 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:29:10,007 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:29:10,007 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:10,010 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 51
2025-08-05 10:29:10,010 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,011 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,011 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,011 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,011 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1102.0, 路径长度: 51, 收集中间解: 3
2025-08-05 10:29:10,011 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [34, 48, 36, 9, 32, 37, 19, 27, 30, 6, 47, 0, 25, 31, 1, 7, 23, 24, 45, 22, 11, 4, 14, 49, 33, 50, 43, 44, 3, 18, 12, 41, 40, 46, 15, 29, 38, 10, 28, 21, 35, 5, 42, 17, 16, 13, 39, 26, 2, 20, 8], 'cur_cost': 1102.0, 'intermediate_solutions': [{'tour': [0, 7, 15, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 26, 31, 10, 37, 4, 48, 8, 49, 33, 29, 9, 38, 32, 44, 11, 46, 39, 17, 50, 45, 47, 22, 6, 25, 30, 27, 2, 19, 34, 35, 28, 20, 1, 21, 23, 42, 3], 'cur_cost': 634.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 29, 33, 49, 8, 48, 4, 37, 10, 31, 26, 5, 13, 24, 12, 40, 18, 41, 43, 14, 36, 16, 15, 9, 38, 32, 44, 11, 46, 3, 17, 50, 45, 47, 22, 6, 25, 30, 27, 2, 19, 34, 35, 28, 20, 1, 21, 23, 42, 39], 'cur_cost': 654.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 15, 16, 14, 43, 41, 18, 40, 12, 24, 13, 5, 26, 31, 36, 10, 37, 4, 48, 8, 49, 33, 29, 9, 38, 32, 44, 11, 46, 3, 17, 50, 45, 47, 22, 6, 25, 30, 27, 2, 19, 34, 35, 28, 20, 1, 21, 23, 42, 39], 'cur_cost': 668.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:10,012 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1102.00)
2025-08-05 10:29:10,012 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:29:10,012 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:10,012 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:10,012 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 1794.0
2025-08-05 10:29:10,019 - ExploitationExpert - INFO - res_population_num: 8
2025-08-05 10:29:10,019 - ExploitationExpert - INFO - res_population_costs: [426, 428, 430, 434, 434, 434, 435, 441.0]
2025-08-05 10:29:10,019 - ExploitationExpert - INFO - res_populations: [array([ 0, 31, 10, 37,  4, 36, 16,  3, 17, 46, 11, 45, 50, 26,  5, 47, 22,
        6, 42, 23, 13, 24, 12, 40, 39, 18, 41, 43, 14, 44, 32, 38,  9, 48,
        8, 29, 33, 49, 15, 20, 28,  1, 19, 34, 35,  2, 27, 30, 25,  7, 21],
      dtype=int64), array([ 0, 21,  1, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5,
       26, 50, 45, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18, 41, 43, 16, 36,
       14, 44, 32, 38,  9, 29, 33, 20, 28, 15, 49,  8, 48,  4, 37, 10, 31],
      dtype=int64), array([ 0, 21,  1, 15, 49,  8, 48,  4, 37, 10, 31, 50, 45, 11, 46,  3, 17,
       13, 24, 12, 40, 39, 18, 41, 43, 16, 36, 14, 44, 32, 38,  9, 29, 33,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 48,  4, 37,  8, 29, 33, 49, 15,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 18, 39,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 48,  4, 37,  8, 29, 33, 49, 15,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 31, 10, 37,  4, 48,  9, 38, 29, 33, 49,  8, 15, 20, 28,
       19, 34, 35,  2, 27, 30, 25,  7, 47,  5, 22,  6, 42, 23, 13, 24, 12,
       40, 18, 39, 41, 43, 44, 32, 14, 36, 16,  3, 17, 46, 11, 45, 50, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 29,  8, 48,  4, 37, 15, 49, 33,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21, 27, 30, 25,  7, 47, 26,  5, 22,  6, 42, 23, 13, 24, 12, 40,
       39, 18, 41, 43, 14, 44, 32, 38,  9, 29,  8, 48,  4, 36, 16,  3, 17,
       46, 11, 50, 45, 10, 37, 15, 49, 33, 20, 28, 19, 34, 35,  2,  1, 31],
      dtype=int64)]
2025-08-05 10:29:10,022 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:10,022 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 1, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 17, 13, 24, 12, 40, 18, 41, 43, 14, 36, 16, 44, 32, 9, 29, 33, 20, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 21, 38, 39], 'cur_cost': 625.0}, {'tour': [0, 17, 1, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 26, 31, 10, 37, 4, 48, 8, 49, 15, 28, 20, 33, 29, 9, 38, 32, 44, 45, 50, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 6, 22, 23, 42, 39], 'cur_cost': 627.0}, {'tour': [9, 3, 15, 16, 49, 14, 43, 4, 37, 10, 41, 18, 40, 12, 26, 46, 17, 13, 24, 48, 32, 33, 5, 8, 42, 36, 28, 20, 11, 29, 2, 38, 35, 44, 45, 22, 47, 25, 7, 27, 39, 19, 6, 34, 23, 21, 1, 50, 31, 30, 0], 'cur_cost': 1440.0}, {'tour': [0, 2, 1, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 28, 20, 33, 29, 9, 38, 32, 44, 5, 47, 7, 25, 30, 27, 21, 19, 34, 35, 6, 22, 23, 42], 'cur_cost': 584.0}, {'tour': [34, 48, 36, 9, 32, 37, 19, 27, 30, 6, 47, 0, 25, 31, 1, 7, 23, 24, 45, 22, 11, 4, 14, 49, 33, 50, 43, 44, 3, 18, 12, 41, 40, 46, 15, 29, 38, 10, 28, 21, 35, 5, 42, 17, 16, 13, 39, 26, 2, 20, 8], 'cur_cost': 1102.0}, {'tour': array([33, 11, 28, 37,  3, 12,  8, 17, 16, 27, 50,  6, 40, 44, 42, 47, 38,
       39, 19,  4, 20, 36, 48, 35, 49, 23, 43, 10,  0, 41, 25, 34,  5, 29,
       21,  1, 13, 45,  2, 14, 15,  9, 30, 24, 32, 31, 22, 46, 18, 26,  7],
      dtype=int64), 'cur_cost': 1794.0}, {'tour': [20, 21, 44, 11, 9, 43, 16, 38, 30, 29, 36, 2, 32, 31, 17, 8, 41, 33, 7, 14, 40, 18, 5, 35, 42, 23, 22, 28, 12, 6, 1, 13, 3, 47, 25, 34, 0, 19, 27, 39, 15, 46, 45, 48, 24, 10, 50, 26, 37, 49, 4], 'cur_cost': 1608.0}, {'tour': [0, 5, 8, 18, 40, 12, 24, 13, 17, 3, 16, 36, 14, 43, 41, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 9, 29, 33, 49, 15, 1, 28, 20, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 21, 38, 32, 44], 'cur_cost': 609.0}, {'tour': [35, 0, 28, 48, 5, 20, 16, 6, 7, 19, 24, 9, 18, 25, 47, 34, 37, 26, 10, 21, 32, 12, 30, 29, 4, 8, 27, 1, 31, 33, 3, 50, 42, 14, 36, 38, 49, 11, 22, 43, 39, 2, 17, 13, 15, 45, 23, 44, 46, 41, 40], 'cur_cost': 1652.0}, {'tour': [0, 10, 12, 18, 40, 39, 41, 43, 14, 36, 16, 3, 17, 46, 11, 45, 50, 26, 31, 1, 15, 49, 8, 48, 4, 37, 9, 29, 33, 20, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 5, 47, 21, 42, 44, 32, 38], 'cur_cost': 600.0}]
2025-08-05 10:29:10,023 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:10,023 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 333, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 333, 'cache_hits': 0, 'similarity_calculations': 1696, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:10,024 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([33, 11, 28, 37,  3, 12,  8, 17, 16, 27, 50,  6, 40, 44, 42, 47, 38,
       39, 19,  4, 20, 36, 48, 35, 49, 23, 43, 10,  0, 41, 25, 34,  5, 29,
       21,  1, 13, 45,  2, 14, 15,  9, 30, 24, 32, 31, 22, 46, 18, 26,  7],
      dtype=int64), 'cur_cost': 1794.0, 'intermediate_solutions': [{'tour': array([50, 43,  9, 18, 32, 27, 29, 20, 49, 39, 38, 16,  5, 22, 36, 31, 19,
        6, 11, 33, 48, 37,  8, 14, 15,  1, 44, 40, 25, 17,  2, 46, 28, 35,
       26, 12, 24,  0, 30, 23, 10, 45, 34,  7, 13,  4,  3, 42, 21, 47, 41]), 'cur_cost': 1550.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([18, 50, 43,  9, 32, 27, 29, 20, 49, 39, 38, 16,  5, 22, 36, 31, 19,
        6, 11, 33, 48, 37,  8, 14, 15,  1, 44, 40, 25, 17,  2, 46, 28, 35,
       26, 12, 24,  0, 30, 23, 10, 45, 34,  7, 13,  4,  3, 42, 21, 47, 41]), 'cur_cost': 1500.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([32, 18, 50, 43,  9, 27, 29, 20, 49, 39, 38, 16,  5, 22, 36, 31, 19,
        6, 11, 33, 48, 37,  8, 14, 15,  1, 44, 40, 25, 17,  2, 46, 28, 35,
       26, 12, 24,  0, 30, 23, 10, 45, 34,  7, 13,  4,  3, 42, 21, 47, 41]), 'cur_cost': 1527.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 9, 18, 50, 43, 32, 27, 29, 20, 49, 39, 38, 16,  5, 22, 36, 31, 19,
        6, 11, 33, 48, 37,  8, 14, 15,  1, 44, 40, 25, 17,  2, 46, 28, 35,
       26, 12, 24,  0, 30, 23, 10, 45, 34,  7, 13,  4,  3, 42, 21, 47, 41]), 'cur_cost': 1545.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 9, 32, 18, 50, 43, 27, 29, 20, 49, 39, 38, 16,  5, 22, 36, 31, 19,
        6, 11, 33, 48, 37,  8, 14, 15,  1, 44, 40, 25, 17,  2, 46, 28, 35,
       26, 12, 24,  0, 30, 23, 10, 45, 34,  7, 13,  4,  3, 42, 21, 47, 41]), 'cur_cost': 1531.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:10,024 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1794.00)
2025-08-05 10:29:10,024 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:29:10,024 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:10,024 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:10,024 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1531.0
2025-08-05 10:29:10,030 - ExploitationExpert - INFO - res_population_num: 8
2025-08-05 10:29:10,030 - ExploitationExpert - INFO - res_population_costs: [426, 428, 430, 434, 434, 434, 435, 441.0]
2025-08-05 10:29:10,031 - ExploitationExpert - INFO - res_populations: [array([ 0, 31, 10, 37,  4, 36, 16,  3, 17, 46, 11, 45, 50, 26,  5, 47, 22,
        6, 42, 23, 13, 24, 12, 40, 39, 18, 41, 43, 14, 44, 32, 38,  9, 48,
        8, 29, 33, 49, 15, 20, 28,  1, 19, 34, 35,  2, 27, 30, 25,  7, 21],
      dtype=int64), array([ 0, 21,  1, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5,
       26, 50, 45, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18, 41, 43, 16, 36,
       14, 44, 32, 38,  9, 29, 33, 20, 28, 15, 49,  8, 48,  4, 37, 10, 31],
      dtype=int64), array([ 0, 21,  1, 15, 49,  8, 48,  4, 37, 10, 31, 50, 45, 11, 46,  3, 17,
       13, 24, 12, 40, 39, 18, 41, 43, 16, 36, 14, 44, 32, 38,  9, 29, 33,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 48,  4, 37,  8, 29, 33, 49, 15,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 18, 39,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 48,  4, 37,  8, 29, 33, 49, 15,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 31, 10, 37,  4, 48,  9, 38, 29, 33, 49,  8, 15, 20, 28,
       19, 34, 35,  2, 27, 30, 25,  7, 47,  5, 22,  6, 42, 23, 13, 24, 12,
       40, 18, 39, 41, 43, 44, 32, 14, 36, 16,  3, 17, 46, 11, 45, 50, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 29,  8, 48,  4, 37, 15, 49, 33,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21, 27, 30, 25,  7, 47, 26,  5, 22,  6, 42, 23, 13, 24, 12, 40,
       39, 18, 41, 43, 14, 44, 32, 38,  9, 29,  8, 48,  4, 36, 16,  3, 17,
       46, 11, 50, 45, 10, 37, 15, 49, 33, 20, 28, 19, 34, 35,  2,  1, 31],
      dtype=int64)]
2025-08-05 10:29:10,033 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:10,033 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 1, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 17, 13, 24, 12, 40, 18, 41, 43, 14, 36, 16, 44, 32, 9, 29, 33, 20, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 21, 38, 39], 'cur_cost': 625.0}, {'tour': [0, 17, 1, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 26, 31, 10, 37, 4, 48, 8, 49, 15, 28, 20, 33, 29, 9, 38, 32, 44, 45, 50, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 6, 22, 23, 42, 39], 'cur_cost': 627.0}, {'tour': [9, 3, 15, 16, 49, 14, 43, 4, 37, 10, 41, 18, 40, 12, 26, 46, 17, 13, 24, 48, 32, 33, 5, 8, 42, 36, 28, 20, 11, 29, 2, 38, 35, 44, 45, 22, 47, 25, 7, 27, 39, 19, 6, 34, 23, 21, 1, 50, 31, 30, 0], 'cur_cost': 1440.0}, {'tour': [0, 2, 1, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 28, 20, 33, 29, 9, 38, 32, 44, 5, 47, 7, 25, 30, 27, 21, 19, 34, 35, 6, 22, 23, 42], 'cur_cost': 584.0}, {'tour': [34, 48, 36, 9, 32, 37, 19, 27, 30, 6, 47, 0, 25, 31, 1, 7, 23, 24, 45, 22, 11, 4, 14, 49, 33, 50, 43, 44, 3, 18, 12, 41, 40, 46, 15, 29, 38, 10, 28, 21, 35, 5, 42, 17, 16, 13, 39, 26, 2, 20, 8], 'cur_cost': 1102.0}, {'tour': array([33, 11, 28, 37,  3, 12,  8, 17, 16, 27, 50,  6, 40, 44, 42, 47, 38,
       39, 19,  4, 20, 36, 48, 35, 49, 23, 43, 10,  0, 41, 25, 34,  5, 29,
       21,  1, 13, 45,  2, 14, 15,  9, 30, 24, 32, 31, 22, 46, 18, 26,  7],
      dtype=int64), 'cur_cost': 1794.0}, {'tour': array([10, 46, 16, 11, 18, 14, 49,  9, 33, 39, 21,  0, 38, 41, 25, 15, 48,
       43, 34, 19, 20, 31, 47,  5, 28,  2, 36, 22, 29, 27, 23, 40, 12, 42,
       24,  6,  8,  7, 32, 50,  4, 44,  1, 30, 17, 13, 37, 26, 35, 45,  3],
      dtype=int64), 'cur_cost': 1531.0}, {'tour': [0, 5, 8, 18, 40, 12, 24, 13, 17, 3, 16, 36, 14, 43, 41, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 9, 29, 33, 49, 15, 1, 28, 20, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 21, 38, 32, 44], 'cur_cost': 609.0}, {'tour': [35, 0, 28, 48, 5, 20, 16, 6, 7, 19, 24, 9, 18, 25, 47, 34, 37, 26, 10, 21, 32, 12, 30, 29, 4, 8, 27, 1, 31, 33, 3, 50, 42, 14, 36, 38, 49, 11, 22, 43, 39, 2, 17, 13, 15, 45, 23, 44, 46, 41, 40], 'cur_cost': 1652.0}, {'tour': [0, 10, 12, 18, 40, 39, 41, 43, 14, 36, 16, 3, 17, 46, 11, 45, 50, 26, 31, 1, 15, 49, 8, 48, 4, 37, 9, 29, 33, 20, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 5, 47, 21, 42, 44, 32, 38], 'cur_cost': 600.0}]
2025-08-05 10:29:10,034 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:10,034 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 334, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 334, 'cache_hits': 0, 'similarity_calculations': 1704, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:10,035 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([10, 46, 16, 11, 18, 14, 49,  9, 33, 39, 21,  0, 38, 41, 25, 15, 48,
       43, 34, 19, 20, 31, 47,  5, 28,  2, 36, 22, 29, 27, 23, 40, 12, 42,
       24,  6,  8,  7, 32, 50,  4, 44,  1, 30, 17, 13, 37, 26, 35, 45,  3],
      dtype=int64), 'cur_cost': 1531.0, 'intermediate_solutions': [{'tour': array([44, 21, 20, 11,  9, 43, 16, 38, 30, 29, 36,  2, 32, 31, 17,  8, 41,
       33,  7, 14, 40, 18,  5, 35, 42, 23, 22, 28, 12,  6,  1, 13,  3, 47,
       25, 34,  0, 19, 27, 39, 15, 46, 45, 48, 24, 10, 50, 26, 37, 49,  4]), 'cur_cost': 1613.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([11, 44, 21, 20,  9, 43, 16, 38, 30, 29, 36,  2, 32, 31, 17,  8, 41,
       33,  7, 14, 40, 18,  5, 35, 42, 23, 22, 28, 12,  6,  1, 13,  3, 47,
       25, 34,  0, 19, 27, 39, 15, 46, 45, 48, 24, 10, 50, 26, 37, 49,  4]), 'cur_cost': 1593.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9, 11, 44, 21, 20, 43, 16, 38, 30, 29, 36,  2, 32, 31, 17,  8, 41,
       33,  7, 14, 40, 18,  5, 35, 42, 23, 22, 28, 12,  6,  1, 13,  3, 47,
       25, 34,  0, 19, 27, 39, 15, 46, 45, 48, 24, 10, 50, 26, 37, 49,  4]), 'cur_cost': 1617.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([20, 11, 44, 21,  9, 43, 16, 38, 30, 29, 36,  2, 32, 31, 17,  8, 41,
       33,  7, 14, 40, 18,  5, 35, 42, 23, 22, 28, 12,  6,  1, 13,  3, 47,
       25, 34,  0, 19, 27, 39, 15, 46, 45, 48, 24, 10, 50, 26, 37, 49,  4]), 'cur_cost': 1630.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([20,  9, 11, 44, 21, 43, 16, 38, 30, 29, 36,  2, 32, 31, 17,  8, 41,
       33,  7, 14, 40, 18,  5, 35, 42, 23, 22, 28, 12,  6,  1, 13,  3, 47,
       25, 34,  0, 19, 27, 39, 15, 46, 45, 48, 24, 10, 50, 26, 37, 49,  4]), 'cur_cost': 1629.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:10,035 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1531.00)
2025-08-05 10:29:10,035 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:10,035 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:10,035 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:10,037 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 10:29:10,037 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,037 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,037 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,038 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,038 - ExplorationExpert - INFO - 探索路径生成完成，成本: 629.0, 路径长度: 51, 收集中间解: 3
2025-08-05 10:29:10,038 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 13, 20, 16, 36, 14, 43, 41, 18, 40, 12, 24, 17, 3, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 21, 33, 29, 9, 38, 32, 44, 39], 'cur_cost': 629.0, 'intermediate_solutions': [{'tour': [0, 5, 8, 18, 40, 12, 24, 13, 17, 3, 16, 36, 14, 43, 41, 39, 46, 11, 34, 50, 26, 31, 10, 37, 4, 48, 9, 29, 33, 49, 15, 1, 28, 20, 19, 45, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 21, 38, 32, 44], 'cur_cost': 744.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [14, 36, 16, 3, 17, 13, 24, 12, 40, 18, 8, 5, 0, 43, 41, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 9, 29, 33, 49, 15, 1, 28, 20, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 21, 38, 32, 44], 'cur_cost': 606.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 8, 18, 40, 12, 24, 13, 17, 49, 3, 16, 36, 14, 43, 41, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 9, 29, 33, 15, 1, 28, 20, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 21, 38, 32, 44], 'cur_cost': 678.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:10,038 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 629.00)
2025-08-05 10:29:10,038 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:29:10,038 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:10,039 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:10,039 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 1358.0
2025-08-05 10:29:10,045 - ExploitationExpert - INFO - res_population_num: 8
2025-08-05 10:29:10,046 - ExploitationExpert - INFO - res_population_costs: [426, 428, 430, 434, 434, 434, 435, 441.0]
2025-08-05 10:29:10,046 - ExploitationExpert - INFO - res_populations: [array([ 0, 31, 10, 37,  4, 36, 16,  3, 17, 46, 11, 45, 50, 26,  5, 47, 22,
        6, 42, 23, 13, 24, 12, 40, 39, 18, 41, 43, 14, 44, 32, 38,  9, 48,
        8, 29, 33, 49, 15, 20, 28,  1, 19, 34, 35,  2, 27, 30, 25,  7, 21],
      dtype=int64), array([ 0, 21,  1, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5,
       26, 50, 45, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18, 41, 43, 16, 36,
       14, 44, 32, 38,  9, 29, 33, 20, 28, 15, 49,  8, 48,  4, 37, 10, 31],
      dtype=int64), array([ 0, 21,  1, 15, 49,  8, 48,  4, 37, 10, 31, 50, 45, 11, 46,  3, 17,
       13, 24, 12, 40, 39, 18, 41, 43, 16, 36, 14, 44, 32, 38,  9, 29, 33,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 48,  4, 37,  8, 29, 33, 49, 15,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 18, 39,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 48,  4, 37,  8, 29, 33, 49, 15,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 31, 10, 37,  4, 48,  9, 38, 29, 33, 49,  8, 15, 20, 28,
       19, 34, 35,  2, 27, 30, 25,  7, 47,  5, 22,  6, 42, 23, 13, 24, 12,
       40, 18, 39, 41, 43, 44, 32, 14, 36, 16,  3, 17, 46, 11, 45, 50, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 29,  8, 48,  4, 37, 15, 49, 33,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21, 27, 30, 25,  7, 47, 26,  5, 22,  6, 42, 23, 13, 24, 12, 40,
       39, 18, 41, 43, 14, 44, 32, 38,  9, 29,  8, 48,  4, 36, 16,  3, 17,
       46, 11, 50, 45, 10, 37, 15, 49, 33, 20, 28, 19, 34, 35,  2,  1, 31],
      dtype=int64)]
2025-08-05 10:29:10,048 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:10,048 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 1, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 17, 13, 24, 12, 40, 18, 41, 43, 14, 36, 16, 44, 32, 9, 29, 33, 20, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 21, 38, 39], 'cur_cost': 625.0}, {'tour': [0, 17, 1, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 26, 31, 10, 37, 4, 48, 8, 49, 15, 28, 20, 33, 29, 9, 38, 32, 44, 45, 50, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 6, 22, 23, 42, 39], 'cur_cost': 627.0}, {'tour': [9, 3, 15, 16, 49, 14, 43, 4, 37, 10, 41, 18, 40, 12, 26, 46, 17, 13, 24, 48, 32, 33, 5, 8, 42, 36, 28, 20, 11, 29, 2, 38, 35, 44, 45, 22, 47, 25, 7, 27, 39, 19, 6, 34, 23, 21, 1, 50, 31, 30, 0], 'cur_cost': 1440.0}, {'tour': [0, 2, 1, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 28, 20, 33, 29, 9, 38, 32, 44, 5, 47, 7, 25, 30, 27, 21, 19, 34, 35, 6, 22, 23, 42], 'cur_cost': 584.0}, {'tour': [34, 48, 36, 9, 32, 37, 19, 27, 30, 6, 47, 0, 25, 31, 1, 7, 23, 24, 45, 22, 11, 4, 14, 49, 33, 50, 43, 44, 3, 18, 12, 41, 40, 46, 15, 29, 38, 10, 28, 21, 35, 5, 42, 17, 16, 13, 39, 26, 2, 20, 8], 'cur_cost': 1102.0}, {'tour': array([33, 11, 28, 37,  3, 12,  8, 17, 16, 27, 50,  6, 40, 44, 42, 47, 38,
       39, 19,  4, 20, 36, 48, 35, 49, 23, 43, 10,  0, 41, 25, 34,  5, 29,
       21,  1, 13, 45,  2, 14, 15,  9, 30, 24, 32, 31, 22, 46, 18, 26,  7],
      dtype=int64), 'cur_cost': 1794.0}, {'tour': array([10, 46, 16, 11, 18, 14, 49,  9, 33, 39, 21,  0, 38, 41, 25, 15, 48,
       43, 34, 19, 20, 31, 47,  5, 28,  2, 36, 22, 29, 27, 23, 40, 12, 42,
       24,  6,  8,  7, 32, 50,  4, 44,  1, 30, 17, 13, 37, 26, 35, 45,  3],
      dtype=int64), 'cur_cost': 1531.0}, {'tour': [0, 13, 20, 16, 36, 14, 43, 41, 18, 40, 12, 24, 17, 3, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 21, 33, 29, 9, 38, 32, 44, 39], 'cur_cost': 629.0}, {'tour': array([29, 26, 38, 14, 37,  0, 10,  3, 11, 21,  4, 16,  7,  5, 22, 35,  8,
        9, 20, 49, 34,  6, 23, 47, 15, 18, 43, 46, 41, 40, 39, 17, 31, 45,
       48, 50, 25,  1, 19, 36, 32, 12, 44, 33, 13, 42, 30, 24,  2, 28, 27],
      dtype=int64), 'cur_cost': 1358.0}, {'tour': [0, 10, 12, 18, 40, 39, 41, 43, 14, 36, 16, 3, 17, 46, 11, 45, 50, 26, 31, 1, 15, 49, 8, 48, 4, 37, 9, 29, 33, 20, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 5, 47, 21, 42, 44, 32, 38], 'cur_cost': 600.0}]
2025-08-05 10:29:10,050 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:10,050 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 335, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 335, 'cache_hits': 0, 'similarity_calculations': 1713, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:10,051 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([29, 26, 38, 14, 37,  0, 10,  3, 11, 21,  4, 16,  7,  5, 22, 35,  8,
        9, 20, 49, 34,  6, 23, 47, 15, 18, 43, 46, 41, 40, 39, 17, 31, 45,
       48, 50, 25,  1, 19, 36, 32, 12, 44, 33, 13, 42, 30, 24,  2, 28, 27],
      dtype=int64), 'cur_cost': 1358.0, 'intermediate_solutions': [{'tour': array([28,  0, 35, 48,  5, 20, 16,  6,  7, 19, 24,  9, 18, 25, 47, 34, 37,
       26, 10, 21, 32, 12, 30, 29,  4,  8, 27,  1, 31, 33,  3, 50, 42, 14,
       36, 38, 49, 11, 22, 43, 39,  2, 17, 13, 15, 45, 23, 44, 46, 41, 40]), 'cur_cost': 1657.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([48, 28,  0, 35,  5, 20, 16,  6,  7, 19, 24,  9, 18, 25, 47, 34, 37,
       26, 10, 21, 32, 12, 30, 29,  4,  8, 27,  1, 31, 33,  3, 50, 42, 14,
       36, 38, 49, 11, 22, 43, 39,  2, 17, 13, 15, 45, 23, 44, 46, 41, 40]), 'cur_cost': 1632.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 5, 48, 28,  0, 35, 20, 16,  6,  7, 19, 24,  9, 18, 25, 47, 34, 37,
       26, 10, 21, 32, 12, 30, 29,  4,  8, 27,  1, 31, 33,  3, 50, 42, 14,
       36, 38, 49, 11, 22, 43, 39,  2, 17, 13, 15, 45, 23, 44, 46, 41, 40]), 'cur_cost': 1596.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([35, 48, 28,  0,  5, 20, 16,  6,  7, 19, 24,  9, 18, 25, 47, 34, 37,
       26, 10, 21, 32, 12, 30, 29,  4,  8, 27,  1, 31, 33,  3, 50, 42, 14,
       36, 38, 49, 11, 22, 43, 39,  2, 17, 13, 15, 45, 23, 44, 46, 41, 40]), 'cur_cost': 1649.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([35,  5, 48, 28,  0, 20, 16,  6,  7, 19, 24,  9, 18, 25, 47, 34, 37,
       26, 10, 21, 32, 12, 30, 29,  4,  8, 27,  1, 31, 33,  3, 50, 42, 14,
       36, 38, 49, 11, 22, 43, 39,  2, 17, 13, 15, 45, 23, 44, 46, 41, 40]), 'cur_cost': 1654.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:10,051 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1358.00)
2025-08-05 10:29:10,051 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:29:10,051 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:29:10,051 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:10,052 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 51
2025-08-05 10:29:10,053 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,053 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,053 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,053 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,053 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1463.0, 路径长度: 51, 收集中间解: 3
2025-08-05 10:29:10,053 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [34, 48, 36, 13, 24, 8, 3, 27, 30, 14, 43, 41, 50, 45, 12, 39, 5, 11, 10, 40, 33, 37, 42, 15, 16, 49, 32, 9, 2, 35, 20, 28, 38, 22, 44, 25, 7, 21, 23, 17, 29, 6, 0, 4, 31, 19, 1, 47, 46, 18, 26], 'cur_cost': 1463.0, 'intermediate_solutions': [{'tour': [0, 10, 12, 18, 40, 39, 41, 43, 14, 36, 16, 3, 17, 28, 11, 45, 50, 26, 31, 1, 15, 49, 8, 48, 4, 37, 9, 29, 33, 20, 46, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 5, 47, 21, 42, 44, 32, 38], 'cur_cost': 723.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 12, 18, 40, 39, 41, 43, 14, 36, 16, 3, 17, 35, 34, 19, 28, 20, 33, 29, 9, 37, 4, 48, 8, 49, 15, 1, 31, 26, 50, 45, 11, 46, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 5, 47, 21, 42, 44, 32, 38], 'cur_cost': 680.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 12, 18, 40, 39, 41, 43, 14, 36, 16, 3, 17, 46, 11, 45, 50, 26, 31, 1, 15, 49, 30, 8, 48, 4, 37, 9, 29, 33, 20, 28, 19, 34, 35, 2, 27, 7, 25, 6, 22, 23, 13, 24, 5, 47, 21, 42, 44, 32, 38], 'cur_cost': 668.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:10,054 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 1463.00)
2025-08-05 10:29:10,054 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:10,054 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:10,056 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 1, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 17, 13, 24, 12, 40, 18, 41, 43, 14, 36, 16, 44, 32, 9, 29, 33, 20, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 21, 38, 39], 'cur_cost': 625.0, 'intermediate_solutions': [{'tour': [38, 8, 33, 49, 40, 30, 50, 45, 19, 34, 16, 3, 17, 7, 43, 41, 18, 0, 10, 20, 37, 4, 26, 29, 42, 23, 15, 1, 28, 24, 31, 12, 35, 2, 48, 13, 32, 25, 6, 22, 47, 11, 44, 5, 39, 9, 36, 27, 21, 14, 46], 'cur_cost': 1449.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [38, 8, 33, 7, 40, 30, 50, 45, 19, 34, 16, 3, 17, 49, 43, 14, 21, 27, 36, 9, 39, 5, 44, 11, 47, 22, 6, 25, 32, 13, 48, 2, 35, 12, 31, 24, 28, 1, 15, 23, 42, 29, 26, 4, 37, 20, 10, 0, 18, 41, 46], 'cur_cost': 1478.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [38, 8, 33, 7, 40, 30, 50, 45, 19, 34, 16, 3, 17, 49, 43, 41, 18, 0, 10, 20, 23, 37, 4, 26, 29, 42, 15, 1, 28, 24, 31, 12, 35, 2, 48, 13, 32, 25, 6, 22, 47, 11, 44, 5, 39, 9, 36, 27, 21, 14, 46], 'cur_cost': 1552.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 1, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 26, 31, 10, 37, 4, 48, 8, 49, 15, 28, 20, 33, 29, 9, 38, 32, 44, 45, 50, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 6, 22, 23, 42, 39], 'cur_cost': 627.0, 'intermediate_solutions': [{'tour': [38, 9, 17, 15, 50, 26, 40, 27, 11, 37, 35, 3, 18, 23, 13, 24, 12, 25, 41, 31, 30, 5, 47, 29, 2, 46, 16, 1, 36, 43, 45, 49, 20, 4, 8, 48, 33, 7, 6, 22, 44, 10, 34, 21, 42, 39, 14, 32, 28, 19, 0], 'cur_cost': 1416.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [38, 9, 17, 15, 50, 26, 40, 27, 11, 37, 35, 3, 18, 23, 13, 24, 12, 25, 41, 31, 30, 5, 47, 29, 2, 46, 16, 1, 10, 43, 45, 49, 20, 4, 8, 48, 33, 7, 6, 22, 44, 36, 34, 21, 42, 28, 32, 14, 39, 19, 0], 'cur_cost': 1482.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [38, 9, 17, 15, 50, 26, 40, 27, 11, 37, 35, 3, 18, 23, 13, 24, 12, 25, 41, 31, 30, 5, 47, 29, 2, 46, 16, 1, 10, 43, 45, 49, 20, 4, 8, 48, 33, 7, 6, 22, 44, 36, 21, 42, 34, 39, 14, 32, 28, 19, 0], 'cur_cost': 1465.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [9, 3, 15, 16, 49, 14, 43, 4, 37, 10, 41, 18, 40, 12, 26, 46, 17, 13, 24, 48, 32, 33, 5, 8, 42, 36, 28, 20, 11, 29, 2, 38, 35, 44, 45, 22, 47, 25, 7, 27, 39, 19, 6, 34, 23, 21, 1, 50, 31, 30, 0], 'cur_cost': 1440.0, 'intermediate_solutions': [{'tour': [13, 24, 17, 39, 5, 25, 47, 23, 30, 0, 46, 14, 16, 43, 12, 10, 15, 37, 20, 1, 8, 33, 34, 4, 11, 18, 6, 22, 42, 31, 28, 27, 35, 21, 2, 50, 36, 45, 7, 26, 49, 38, 48, 29, 32, 44, 9, 3, 40, 41, 19], 'cur_cost': 1103.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [13, 24, 17, 39, 5, 25, 47, 6, 30, 0, 46, 14, 16, 43, 12, 10, 15, 37, 20, 1, 8, 48, 38, 49, 26, 7, 45, 36, 50, 2, 21, 35, 27, 28, 31, 42, 22, 23, 18, 11, 4, 34, 33, 29, 32, 44, 9, 3, 40, 41, 19], 'cur_cost': 1070.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [13, 24, 17, 39, 5, 25, 47, 6, 30, 0, 46, 14, 16, 43, 12, 21, 10, 15, 37, 20, 1, 8, 33, 34, 4, 11, 18, 23, 22, 42, 31, 28, 27, 35, 2, 50, 36, 45, 7, 26, 49, 38, 48, 29, 32, 44, 9, 3, 40, 41, 19], 'cur_cost': 1077.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 1, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 28, 20, 33, 29, 9, 38, 32, 44, 5, 47, 7, 25, 30, 27, 21, 19, 34, 35, 6, 22, 23, 42], 'cur_cost': 584.0, 'intermediate_solutions': [{'tour': [13, 24, 21, 39, 40, 25, 48, 2, 32, 0, 35, 6, 18, 14, 12, 10, 15, 37, 20, 31, 8, 4, 34, 7, 11, 30, 16, 22, 28, 43, 41, 49, 26, 5, 27, 50, 36, 45, 1, 9, 33, 38, 44, 29, 42, 3, 46, 23, 47, 19, 17], 'cur_cost': 1534.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [13, 24, 21, 39, 40, 25, 47, 2, 32, 0, 35, 6, 18, 14, 12, 10, 15, 37, 20, 31, 8, 4, 34, 7, 11, 30, 16, 22, 28, 43, 41, 49, 26, 5, 27, 50, 36, 45, 1, 9, 33, 38, 44, 29, 42, 3, 17, 19, 48, 23, 46], 'cur_cost': 1529.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [13, 24, 21, 39, 40, 25, 47, 2, 32, 0, 35, 6, 18, 14, 12, 10, 15, 37, 20, 31, 8, 4, 34, 7, 11, 30, 16, 22, 28, 43, 41, 49, 26, 5, 27, 50, 36, 45, 29, 1, 9, 33, 38, 44, 42, 3, 46, 23, 48, 19, 17], 'cur_cost': 1530.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [34, 48, 36, 9, 32, 37, 19, 27, 30, 6, 47, 0, 25, 31, 1, 7, 23, 24, 45, 22, 11, 4, 14, 49, 33, 50, 43, 44, 3, 18, 12, 41, 40, 46, 15, 29, 38, 10, 28, 21, 35, 5, 42, 17, 16, 13, 39, 26, 2, 20, 8], 'cur_cost': 1102.0, 'intermediate_solutions': [{'tour': [0, 7, 15, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 26, 31, 10, 37, 4, 48, 8, 49, 33, 29, 9, 38, 32, 44, 11, 46, 39, 17, 50, 45, 47, 22, 6, 25, 30, 27, 2, 19, 34, 35, 28, 20, 1, 21, 23, 42, 3], 'cur_cost': 634.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 29, 33, 49, 8, 48, 4, 37, 10, 31, 26, 5, 13, 24, 12, 40, 18, 41, 43, 14, 36, 16, 15, 9, 38, 32, 44, 11, 46, 3, 17, 50, 45, 47, 22, 6, 25, 30, 27, 2, 19, 34, 35, 28, 20, 1, 21, 23, 42, 39], 'cur_cost': 654.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 15, 16, 14, 43, 41, 18, 40, 12, 24, 13, 5, 26, 31, 36, 10, 37, 4, 48, 8, 49, 33, 29, 9, 38, 32, 44, 11, 46, 3, 17, 50, 45, 47, 22, 6, 25, 30, 27, 2, 19, 34, 35, 28, 20, 1, 21, 23, 42, 39], 'cur_cost': 668.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([33, 11, 28, 37,  3, 12,  8, 17, 16, 27, 50,  6, 40, 44, 42, 47, 38,
       39, 19,  4, 20, 36, 48, 35, 49, 23, 43, 10,  0, 41, 25, 34,  5, 29,
       21,  1, 13, 45,  2, 14, 15,  9, 30, 24, 32, 31, 22, 46, 18, 26,  7],
      dtype=int64), 'cur_cost': 1794.0, 'intermediate_solutions': [{'tour': array([50, 43,  9, 18, 32, 27, 29, 20, 49, 39, 38, 16,  5, 22, 36, 31, 19,
        6, 11, 33, 48, 37,  8, 14, 15,  1, 44, 40, 25, 17,  2, 46, 28, 35,
       26, 12, 24,  0, 30, 23, 10, 45, 34,  7, 13,  4,  3, 42, 21, 47, 41]), 'cur_cost': 1550.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([18, 50, 43,  9, 32, 27, 29, 20, 49, 39, 38, 16,  5, 22, 36, 31, 19,
        6, 11, 33, 48, 37,  8, 14, 15,  1, 44, 40, 25, 17,  2, 46, 28, 35,
       26, 12, 24,  0, 30, 23, 10, 45, 34,  7, 13,  4,  3, 42, 21, 47, 41]), 'cur_cost': 1500.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([32, 18, 50, 43,  9, 27, 29, 20, 49, 39, 38, 16,  5, 22, 36, 31, 19,
        6, 11, 33, 48, 37,  8, 14, 15,  1, 44, 40, 25, 17,  2, 46, 28, 35,
       26, 12, 24,  0, 30, 23, 10, 45, 34,  7, 13,  4,  3, 42, 21, 47, 41]), 'cur_cost': 1527.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 9, 18, 50, 43, 32, 27, 29, 20, 49, 39, 38, 16,  5, 22, 36, 31, 19,
        6, 11, 33, 48, 37,  8, 14, 15,  1, 44, 40, 25, 17,  2, 46, 28, 35,
       26, 12, 24,  0, 30, 23, 10, 45, 34,  7, 13,  4,  3, 42, 21, 47, 41]), 'cur_cost': 1545.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 9, 32, 18, 50, 43, 27, 29, 20, 49, 39, 38, 16,  5, 22, 36, 31, 19,
        6, 11, 33, 48, 37,  8, 14, 15,  1, 44, 40, 25, 17,  2, 46, 28, 35,
       26, 12, 24,  0, 30, 23, 10, 45, 34,  7, 13,  4,  3, 42, 21, 47, 41]), 'cur_cost': 1531.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([10, 46, 16, 11, 18, 14, 49,  9, 33, 39, 21,  0, 38, 41, 25, 15, 48,
       43, 34, 19, 20, 31, 47,  5, 28,  2, 36, 22, 29, 27, 23, 40, 12, 42,
       24,  6,  8,  7, 32, 50,  4, 44,  1, 30, 17, 13, 37, 26, 35, 45,  3],
      dtype=int64), 'cur_cost': 1531.0, 'intermediate_solutions': [{'tour': array([44, 21, 20, 11,  9, 43, 16, 38, 30, 29, 36,  2, 32, 31, 17,  8, 41,
       33,  7, 14, 40, 18,  5, 35, 42, 23, 22, 28, 12,  6,  1, 13,  3, 47,
       25, 34,  0, 19, 27, 39, 15, 46, 45, 48, 24, 10, 50, 26, 37, 49,  4]), 'cur_cost': 1613.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([11, 44, 21, 20,  9, 43, 16, 38, 30, 29, 36,  2, 32, 31, 17,  8, 41,
       33,  7, 14, 40, 18,  5, 35, 42, 23, 22, 28, 12,  6,  1, 13,  3, 47,
       25, 34,  0, 19, 27, 39, 15, 46, 45, 48, 24, 10, 50, 26, 37, 49,  4]), 'cur_cost': 1593.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9, 11, 44, 21, 20, 43, 16, 38, 30, 29, 36,  2, 32, 31, 17,  8, 41,
       33,  7, 14, 40, 18,  5, 35, 42, 23, 22, 28, 12,  6,  1, 13,  3, 47,
       25, 34,  0, 19, 27, 39, 15, 46, 45, 48, 24, 10, 50, 26, 37, 49,  4]), 'cur_cost': 1617.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([20, 11, 44, 21,  9, 43, 16, 38, 30, 29, 36,  2, 32, 31, 17,  8, 41,
       33,  7, 14, 40, 18,  5, 35, 42, 23, 22, 28, 12,  6,  1, 13,  3, 47,
       25, 34,  0, 19, 27, 39, 15, 46, 45, 48, 24, 10, 50, 26, 37, 49,  4]), 'cur_cost': 1630.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([20,  9, 11, 44, 21, 43, 16, 38, 30, 29, 36,  2, 32, 31, 17,  8, 41,
       33,  7, 14, 40, 18,  5, 35, 42, 23, 22, 28, 12,  6,  1, 13,  3, 47,
       25, 34,  0, 19, 27, 39, 15, 46, 45, 48, 24, 10, 50, 26, 37, 49,  4]), 'cur_cost': 1629.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 20, 16, 36, 14, 43, 41, 18, 40, 12, 24, 17, 3, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 21, 33, 29, 9, 38, 32, 44, 39], 'cur_cost': 629.0, 'intermediate_solutions': [{'tour': [0, 5, 8, 18, 40, 12, 24, 13, 17, 3, 16, 36, 14, 43, 41, 39, 46, 11, 34, 50, 26, 31, 10, 37, 4, 48, 9, 29, 33, 49, 15, 1, 28, 20, 19, 45, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 21, 38, 32, 44], 'cur_cost': 744.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [14, 36, 16, 3, 17, 13, 24, 12, 40, 18, 8, 5, 0, 43, 41, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 9, 29, 33, 49, 15, 1, 28, 20, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 21, 38, 32, 44], 'cur_cost': 606.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 8, 18, 40, 12, 24, 13, 17, 49, 3, 16, 36, 14, 43, 41, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 9, 29, 33, 15, 1, 28, 20, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 21, 38, 32, 44], 'cur_cost': 678.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([29, 26, 38, 14, 37,  0, 10,  3, 11, 21,  4, 16,  7,  5, 22, 35,  8,
        9, 20, 49, 34,  6, 23, 47, 15, 18, 43, 46, 41, 40, 39, 17, 31, 45,
       48, 50, 25,  1, 19, 36, 32, 12, 44, 33, 13, 42, 30, 24,  2, 28, 27],
      dtype=int64), 'cur_cost': 1358.0, 'intermediate_solutions': [{'tour': array([28,  0, 35, 48,  5, 20, 16,  6,  7, 19, 24,  9, 18, 25, 47, 34, 37,
       26, 10, 21, 32, 12, 30, 29,  4,  8, 27,  1, 31, 33,  3, 50, 42, 14,
       36, 38, 49, 11, 22, 43, 39,  2, 17, 13, 15, 45, 23, 44, 46, 41, 40]), 'cur_cost': 1657.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([48, 28,  0, 35,  5, 20, 16,  6,  7, 19, 24,  9, 18, 25, 47, 34, 37,
       26, 10, 21, 32, 12, 30, 29,  4,  8, 27,  1, 31, 33,  3, 50, 42, 14,
       36, 38, 49, 11, 22, 43, 39,  2, 17, 13, 15, 45, 23, 44, 46, 41, 40]), 'cur_cost': 1632.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 5, 48, 28,  0, 35, 20, 16,  6,  7, 19, 24,  9, 18, 25, 47, 34, 37,
       26, 10, 21, 32, 12, 30, 29,  4,  8, 27,  1, 31, 33,  3, 50, 42, 14,
       36, 38, 49, 11, 22, 43, 39,  2, 17, 13, 15, 45, 23, 44, 46, 41, 40]), 'cur_cost': 1596.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([35, 48, 28,  0,  5, 20, 16,  6,  7, 19, 24,  9, 18, 25, 47, 34, 37,
       26, 10, 21, 32, 12, 30, 29,  4,  8, 27,  1, 31, 33,  3, 50, 42, 14,
       36, 38, 49, 11, 22, 43, 39,  2, 17, 13, 15, 45, 23, 44, 46, 41, 40]), 'cur_cost': 1649.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([35,  5, 48, 28,  0, 20, 16,  6,  7, 19, 24,  9, 18, 25, 47, 34, 37,
       26, 10, 21, 32, 12, 30, 29,  4,  8, 27,  1, 31, 33,  3, 50, 42, 14,
       36, 38, 49, 11, 22, 43, 39,  2, 17, 13, 15, 45, 23, 44, 46, 41, 40]), 'cur_cost': 1654.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [34, 48, 36, 13, 24, 8, 3, 27, 30, 14, 43, 41, 50, 45, 12, 39, 5, 11, 10, 40, 33, 37, 42, 15, 16, 49, 32, 9, 2, 35, 20, 28, 38, 22, 44, 25, 7, 21, 23, 17, 29, 6, 0, 4, 31, 19, 1, 47, 46, 18, 26], 'cur_cost': 1463.0, 'intermediate_solutions': [{'tour': [0, 10, 12, 18, 40, 39, 41, 43, 14, 36, 16, 3, 17, 28, 11, 45, 50, 26, 31, 1, 15, 49, 8, 48, 4, 37, 9, 29, 33, 20, 46, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 5, 47, 21, 42, 44, 32, 38], 'cur_cost': 723.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 12, 18, 40, 39, 41, 43, 14, 36, 16, 3, 17, 35, 34, 19, 28, 20, 33, 29, 9, 37, 4, 48, 8, 49, 15, 1, 31, 26, 50, 45, 11, 46, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 5, 47, 21, 42, 44, 32, 38], 'cur_cost': 680.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 12, 18, 40, 39, 41, 43, 14, 36, 16, 3, 17, 46, 11, 45, 50, 26, 31, 1, 15, 49, 30, 8, 48, 4, 37, 9, 29, 33, 20, 28, 19, 34, 35, 2, 27, 7, 25, 6, 22, 23, 13, 24, 5, 47, 21, 42, 44, 32, 38], 'cur_cost': 668.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:29:10,056 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:10,056 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:10,059 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=584.000, 多样性=0.961
2025-08-05 10:29:10,059 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 10:29:10,060 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 10:29:10,060 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:10,060 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 70, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'slight_improvement', 'improvement_rate': 0.00736246733382087, 'best_improvement': 0.02666666666666667}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.008226691042047736}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.021272759077402698, 'recent_improvements': [-0.011273475551236858, 0.007385917808184476, -0.05381899370604224], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 8, 'new_count': 8, 'count_change': 0, 'old_best_cost': 426, 'new_best_cost': 426, 'quality_improvement': 0.0, 'old_diversity': 0.8130252100840336, 'new_diversity': 0.8130252100840336, 'diversity_change': 0.0}, 'suggestions': ['改进缓慢，建议调整策略平衡或增加扰动强度', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:29:10,061 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 10:29:10,061 - __main__ - INFO - eil51 开始进化第 5 代
2025-08-05 10:29:10,061 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 10:29:10,062 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:10,062 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=584.000, 多样性=0.961
2025-08-05 10:29:10,062 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:10,064 - PathExpert - INFO - 路径结构分析完成: 公共边数量=4, 路径相似性=0.961
2025-08-05 10:29:10,065 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:10,066 - EliteExpert - INFO - 精英解分析完成: 精英解数量=8, 多样性=0.813
2025-08-05 10:29:10,068 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 10:29:10,068 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:10,068 - LandscapeExpert - INFO - 添加精英解数据: 8个精英解
2025-08-05 10:29:10,069 - LandscapeExpert - INFO - 数据提取成功: 18个路径, 18个适应度值
2025-08-05 10:29:10,118 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.167, 适应度梯度: -155.833, 聚类评分: 0.000, 覆盖率: 0.148, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:10,119 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 10:29:10,119 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:10,119 - visualization.landscape_visualizer - INFO - 设置当前实例名: eil51
2025-08-05 10:29:10,125 - visualization.landscape_visualizer - INFO - 插值约束: 313 个点被约束到最小值 426.00
2025-08-05 10:29:10,127 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=6.8%, 梯度: 44.37 → 41.34
2025-08-05 10:29:10,250 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_eil51\landscape_eil51_iter_130_20250805_102910.html
2025-08-05 10:29:10,348 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_eil51\dashboard_eil51_iter_130_20250805_102910.html
2025-08-05 10:29:10,348 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 130
2025-08-05 10:29:10,348 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 10:29:10,348 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2807秒
2025-08-05 10:29:10,350 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.16666666666666666, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -155.83333333333337, 'local_optima_density': 0.16666666666666666, 'gradient_variance': 213945.88111111114, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.148, 'fitness_entropy': 0.8125214573444728, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -155.833)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.148)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360950.1199114, 'performance_metrics': {}}}
2025-08-05 10:29:10,350 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:10,350 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:10,350 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:10,350 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:10,350 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'exploit']
2025-08-05 10:29:10,350 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 10:29:10,350 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'exploit']
2025-08-05 10:29:10,350 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:10,350 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:10,350 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'exploit']
2025-08-05 10:29:10,350 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:10,351 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:10,351 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 3} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:10,351 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:10,351 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:10,351 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:10,353 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 10:29:10,353 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,353 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,354 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,354 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,354 - ExplorationExpert - INFO - 探索路径生成完成，成本: 633.0, 路径长度: 51, 收集中间解: 3
2025-08-05 10:29:10,354 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 21, 18, 6, 22, 23, 13, 24, 17, 3, 16, 36, 14, 43, 41, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 42], 'cur_cost': 633.0, 'intermediate_solutions': [{'tour': [0, 3, 1, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 17, 13, 24, 29, 40, 18, 41, 43, 14, 36, 16, 44, 32, 9, 12, 33, 20, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 21, 38, 39], 'cur_cost': 791.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 1, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 17, 13, 24, 12, 40, 18, 41, 43, 14, 36, 16, 44, 32, 9, 29, 33, 20, 28, 19, 34, 25, 7, 30, 27, 2, 35, 6, 22, 23, 42, 47, 5, 21, 38, 39], 'cur_cost': 689.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 1, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 17, 13, 24, 43, 12, 40, 18, 41, 14, 36, 16, 44, 32, 9, 29, 33, 20, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 21, 38, 39], 'cur_cost': 672.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:10,354 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 633.00)
2025-08-05 10:29:10,354 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:10,355 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:10,355 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:10,356 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 10:29:10,356 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,356 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,356 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,357 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,357 - ExplorationExpert - INFO - 探索路径生成完成，成本: 603.0, 路径长度: 51, 收集中间解: 3
2025-08-05 10:29:10,357 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 17, 20, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 12, 40, 18, 41, 43, 14, 36, 16, 3, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 33, 29, 9, 38, 32, 44, 39, 5, 47, 21, 42], 'cur_cost': 603.0, 'intermediate_solutions': [{'tour': [0, 17, 1, 3, 46, 11, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 26, 31, 10, 37, 4, 48, 8, 49, 15, 28, 20, 33, 29, 9, 38, 32, 44, 45, 50, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 6, 22, 23, 42, 39], 'cur_cost': 641.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 1, 11, 2, 27, 30, 25, 7, 47, 50, 45, 44, 32, 38, 9, 29, 33, 20, 28, 15, 49, 8, 48, 4, 37, 10, 31, 26, 5, 13, 24, 12, 40, 18, 41, 43, 14, 36, 16, 3, 46, 19, 34, 35, 21, 6, 22, 23, 42, 39], 'cur_cost': 692.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 1, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 26, 31, 10, 37, 4, 48, 8, 49, 15, 28, 20, 50, 33, 29, 9, 38, 32, 44, 45, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 6, 22, 23, 42, 39], 'cur_cost': 681.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:10,357 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 603.00)
2025-08-05 10:29:10,357 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:10,357 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:10,357 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:10,358 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 51
2025-08-05 10:29:10,358 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,359 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,359 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,359 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,359 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1323.0, 路径长度: 51, 收集中间解: 3
2025-08-05 10:29:10,359 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [33, 17, 15, 19, 34, 35, 13, 4, 36, 7, 16, 18, 22, 23, 41, 24, 38, 43, 46, 11, 45, 50, 5, 31, 10, 37, 28, 48, 8, 49, 2, 1, 29, 20, 21, 32, 9, 25, 47, 44, 39, 30, 6, 14, 0, 27, 42, 26, 3, 12, 40], 'cur_cost': 1323.0, 'intermediate_solutions': [{'tour': [9, 3, 15, 16, 49, 14, 43, 4, 37, 10, 41, 18, 40, 39, 26, 46, 17, 13, 24, 48, 32, 33, 5, 8, 42, 36, 28, 20, 11, 29, 2, 38, 35, 44, 45, 22, 47, 25, 7, 27, 12, 19, 6, 34, 23, 21, 1, 50, 31, 30, 0], 'cur_cost': 1431.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 3, 15, 16, 49, 14, 43, 4, 37, 10, 41, 18, 40, 12, 39, 27, 7, 25, 47, 22, 45, 44, 35, 38, 2, 29, 11, 20, 28, 36, 42, 8, 5, 33, 32, 48, 24, 13, 17, 46, 26, 19, 6, 34, 23, 21, 1, 50, 31, 30, 0], 'cur_cost': 1380.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 15, 16, 49, 14, 43, 4, 37, 10, 41, 18, 40, 12, 26, 46, 17, 13, 24, 48, 32, 33, 5, 9, 8, 42, 36, 28, 20, 11, 29, 2, 38, 35, 44, 45, 22, 47, 25, 7, 27, 39, 19, 6, 34, 23, 21, 1, 50, 31, 30, 0], 'cur_cost': 1424.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:10,360 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1323.00)
2025-08-05 10:29:10,360 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:10,360 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:10,360 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:10,361 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 10:29:10,361 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,361 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,362 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,362 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,362 - ExplorationExpert - INFO - 探索路径生成完成，成本: 591.0, 路径长度: 51, 收集中间解: 3
2025-08-05 10:29:10,362 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 20, 11, 7, 25, 30, 27, 2, 19, 34, 35, 28, 1, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 22, 6, 42, 23, 17, 39, 44, 32, 9, 29, 33, 38, 21], 'cur_cost': 591.0, 'intermediate_solutions': [{'tour': [0, 2, 1, 13, 24, 17, 3, 16, 36, 46, 43, 41, 18, 40, 12, 39, 14, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 28, 20, 33, 29, 9, 38, 32, 44, 5, 47, 7, 25, 30, 27, 21, 19, 34, 35, 6, 22, 23, 42], 'cur_cost': 612.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 1, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 10, 31, 26, 50, 45, 11, 46, 39, 37, 4, 48, 8, 49, 15, 28, 20, 33, 29, 9, 38, 32, 44, 5, 47, 7, 25, 30, 27, 21, 19, 34, 35, 6, 22, 23, 42], 'cur_cost': 647.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 1, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 6, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 28, 20, 33, 29, 9, 38, 32, 44, 5, 47, 7, 25, 30, 27, 21, 19, 34, 35, 22, 23, 42], 'cur_cost': 659.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:10,362 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 591.00)
2025-08-05 10:29:10,362 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:29:10,363 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:29:10,363 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:10,366 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 51
2025-08-05 10:29:10,366 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,366 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,366 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,366 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,367 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1151.0, 路径长度: 51, 收集中间解: 3
2025-08-05 10:29:10,367 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [16, 48, 32, 33, 4, 49, 0, 1, 19, 34, 7, 23, 24, 6, 17, 18, 3, 50, 26, 37, 21, 25, 31, 10, 2, 5, 45, 46, 14, 40, 44, 9, 15, 29, 43, 13, 11, 47, 22, 42, 27, 30, 20, 38, 28, 36, 41, 39, 12, 8, 35], 'cur_cost': 1151.0, 'intermediate_solutions': [{'tour': [34, 48, 36, 9, 32, 37, 19, 27, 30, 6, 47, 0, 25, 31, 1, 7, 23, 24, 45, 22, 11, 4, 14, 49, 33, 50, 43, 44, 3, 18, 12, 28, 40, 46, 15, 29, 38, 10, 41, 21, 35, 5, 42, 17, 16, 13, 39, 26, 2, 20, 8], 'cur_cost': 1235.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [34, 48, 36, 9, 32, 37, 19, 27, 30, 6, 47, 0, 25, 31, 1, 7, 23, 24, 45, 22, 11, 4, 14, 49, 33, 50, 43, 44, 3, 18, 12, 41, 40, 46, 15, 29, 38, 10, 28, 21, 35, 5, 2, 26, 39, 13, 16, 17, 42, 20, 8], 'cur_cost': 1151.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [34, 48, 36, 9, 32, 37, 19, 27, 30, 6, 47, 0, 25, 31, 1, 23, 24, 45, 7, 22, 11, 4, 14, 49, 33, 50, 43, 44, 3, 18, 12, 41, 40, 46, 15, 29, 38, 10, 28, 21, 35, 5, 42, 17, 16, 13, 39, 26, 2, 20, 8], 'cur_cost': 1111.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:10,367 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1151.00)
2025-08-05 10:29:10,367 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:29:10,367 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:10,367 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:10,367 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 1584.0
2025-08-05 10:29:10,374 - ExploitationExpert - INFO - res_population_num: 8
2025-08-05 10:29:10,375 - ExploitationExpert - INFO - res_population_costs: [426, 428, 430, 434, 434, 434, 435, 441.0]
2025-08-05 10:29:10,375 - ExploitationExpert - INFO - res_populations: [array([ 0, 31, 10, 37,  4, 36, 16,  3, 17, 46, 11, 45, 50, 26,  5, 47, 22,
        6, 42, 23, 13, 24, 12, 40, 39, 18, 41, 43, 14, 44, 32, 38,  9, 48,
        8, 29, 33, 49, 15, 20, 28,  1, 19, 34, 35,  2, 27, 30, 25,  7, 21],
      dtype=int64), array([ 0, 21,  1, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5,
       26, 50, 45, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18, 41, 43, 16, 36,
       14, 44, 32, 38,  9, 29, 33, 20, 28, 15, 49,  8, 48,  4, 37, 10, 31],
      dtype=int64), array([ 0, 21,  1, 15, 49,  8, 48,  4, 37, 10, 31, 50, 45, 11, 46,  3, 17,
       13, 24, 12, 40, 39, 18, 41, 43, 16, 36, 14, 44, 32, 38,  9, 29, 33,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 48,  4, 37,  8, 29, 33, 49, 15,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 18, 39,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 48,  4, 37,  8, 29, 33, 49, 15,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 31, 10, 37,  4, 48,  9, 38, 29, 33, 49,  8, 15, 20, 28,
       19, 34, 35,  2, 27, 30, 25,  7, 47,  5, 22,  6, 42, 23, 13, 24, 12,
       40, 18, 39, 41, 43, 44, 32, 14, 36, 16,  3, 17, 46, 11, 45, 50, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 29,  8, 48,  4, 37, 15, 49, 33,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21, 27, 30, 25,  7, 47, 26,  5, 22,  6, 42, 23, 13, 24, 12, 40,
       39, 18, 41, 43, 14, 44, 32, 38,  9, 29,  8, 48,  4, 36, 16,  3, 17,
       46, 11, 50, 45, 10, 37, 15, 49, 33, 20, 28, 19, 34, 35,  2,  1, 31],
      dtype=int64)]
2025-08-05 10:29:10,377 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:10,378 - ExploitationExpert - INFO - populations: [{'tour': [0, 21, 18, 6, 22, 23, 13, 24, 17, 3, 16, 36, 14, 43, 41, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 42], 'cur_cost': 633.0}, {'tour': [0, 17, 20, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 12, 40, 18, 41, 43, 14, 36, 16, 3, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 33, 29, 9, 38, 32, 44, 39, 5, 47, 21, 42], 'cur_cost': 603.0}, {'tour': [33, 17, 15, 19, 34, 35, 13, 4, 36, 7, 16, 18, 22, 23, 41, 24, 38, 43, 46, 11, 45, 50, 5, 31, 10, 37, 28, 48, 8, 49, 2, 1, 29, 20, 21, 32, 9, 25, 47, 44, 39, 30, 6, 14, 0, 27, 42, 26, 3, 12, 40], 'cur_cost': 1323.0}, {'tour': [0, 20, 11, 7, 25, 30, 27, 2, 19, 34, 35, 28, 1, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 22, 6, 42, 23, 17, 39, 44, 32, 9, 29, 33, 38, 21], 'cur_cost': 591.0}, {'tour': [16, 48, 32, 33, 4, 49, 0, 1, 19, 34, 7, 23, 24, 6, 17, 18, 3, 50, 26, 37, 21, 25, 31, 10, 2, 5, 45, 46, 14, 40, 44, 9, 15, 29, 43, 13, 11, 47, 22, 42, 27, 30, 20, 38, 28, 36, 41, 39, 12, 8, 35], 'cur_cost': 1151.0}, {'tour': array([17, 27, 42, 49, 38, 11, 30, 47, 46, 14,  2,  0, 35, 19,  7, 25, 36,
       16,  1, 39, 29,  4, 28, 37,  5,  9, 12, 48, 18, 45, 20, 23, 13, 26,
       44, 32, 50, 34, 40, 21, 22,  6, 41,  3, 31, 43, 15,  8, 10, 33, 24],
      dtype=int64), 'cur_cost': 1584.0}, {'tour': [10, 46, 16, 11, 18, 14, 49, 9, 33, 39, 21, 0, 38, 41, 25, 15, 48, 43, 34, 19, 20, 31, 47, 5, 28, 2, 36, 22, 29, 27, 23, 40, 12, 42, 24, 6, 8, 7, 32, 50, 4, 44, 1, 30, 17, 13, 37, 26, 35, 45, 3], 'cur_cost': 1531.0}, {'tour': [0, 13, 20, 16, 36, 14, 43, 41, 18, 40, 12, 24, 17, 3, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 21, 33, 29, 9, 38, 32, 44, 39], 'cur_cost': 629.0}, {'tour': [29, 26, 38, 14, 37, 0, 10, 3, 11, 21, 4, 16, 7, 5, 22, 35, 8, 9, 20, 49, 34, 6, 23, 47, 15, 18, 43, 46, 41, 40, 39, 17, 31, 45, 48, 50, 25, 1, 19, 36, 32, 12, 44, 33, 13, 42, 30, 24, 2, 28, 27], 'cur_cost': 1358.0}, {'tour': [34, 48, 36, 13, 24, 8, 3, 27, 30, 14, 43, 41, 50, 45, 12, 39, 5, 11, 10, 40, 33, 37, 42, 15, 16, 49, 32, 9, 2, 35, 20, 28, 38, 22, 44, 25, 7, 21, 23, 17, 29, 6, 0, 4, 31, 19, 1, 47, 46, 18, 26], 'cur_cost': 1463.0}]
2025-08-05 10:29:10,378 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:10,378 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 336, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 336, 'cache_hits': 0, 'similarity_calculations': 1723, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:10,380 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([17, 27, 42, 49, 38, 11, 30, 47, 46, 14,  2,  0, 35, 19,  7, 25, 36,
       16,  1, 39, 29,  4, 28, 37,  5,  9, 12, 48, 18, 45, 20, 23, 13, 26,
       44, 32, 50, 34, 40, 21, 22,  6, 41,  3, 31, 43, 15,  8, 10, 33, 24],
      dtype=int64), 'cur_cost': 1584.0, 'intermediate_solutions': [{'tour': array([28, 11, 33, 37,  3, 12,  8, 17, 16, 27, 50,  6, 40, 44, 42, 47, 38,
       39, 19,  4, 20, 36, 48, 35, 49, 23, 43, 10,  0, 41, 25, 34,  5, 29,
       21,  1, 13, 45,  2, 14, 15,  9, 30, 24, 32, 31, 22, 46, 18, 26,  7]), 'cur_cost': 1780.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([37, 28, 11, 33,  3, 12,  8, 17, 16, 27, 50,  6, 40, 44, 42, 47, 38,
       39, 19,  4, 20, 36, 48, 35, 49, 23, 43, 10,  0, 41, 25, 34,  5, 29,
       21,  1, 13, 45,  2, 14, 15,  9, 30, 24, 32, 31, 22, 46, 18, 26,  7]), 'cur_cost': 1797.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3, 37, 28, 11, 33, 12,  8, 17, 16, 27, 50,  6, 40, 44, 42, 47, 38,
       39, 19,  4, 20, 36, 48, 35, 49, 23, 43, 10,  0, 41, 25, 34,  5, 29,
       21,  1, 13, 45,  2, 14, 15,  9, 30, 24, 32, 31, 22, 46, 18, 26,  7]), 'cur_cost': 1832.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([33, 37, 28, 11,  3, 12,  8, 17, 16, 27, 50,  6, 40, 44, 42, 47, 38,
       39, 19,  4, 20, 36, 48, 35, 49, 23, 43, 10,  0, 41, 25, 34,  5, 29,
       21,  1, 13, 45,  2, 14, 15,  9, 30, 24, 32, 31, 22, 46, 18, 26,  7]), 'cur_cost': 1766.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([33,  3, 37, 28, 11, 12,  8, 17, 16, 27, 50,  6, 40, 44, 42, 47, 38,
       39, 19,  4, 20, 36, 48, 35, 49, 23, 43, 10,  0, 41, 25, 34,  5, 29,
       21,  1, 13, 45,  2, 14, 15,  9, 30, 24, 32, 31, 22, 46, 18, 26,  7]), 'cur_cost': 1818.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:10,380 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1584.00)
2025-08-05 10:29:10,381 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:29:10,381 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:10,381 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:10,381 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1707.0
2025-08-05 10:29:10,389 - ExploitationExpert - INFO - res_population_num: 8
2025-08-05 10:29:10,389 - ExploitationExpert - INFO - res_population_costs: [426, 428, 430, 434, 434, 434, 435, 441.0]
2025-08-05 10:29:10,389 - ExploitationExpert - INFO - res_populations: [array([ 0, 31, 10, 37,  4, 36, 16,  3, 17, 46, 11, 45, 50, 26,  5, 47, 22,
        6, 42, 23, 13, 24, 12, 40, 39, 18, 41, 43, 14, 44, 32, 38,  9, 48,
        8, 29, 33, 49, 15, 20, 28,  1, 19, 34, 35,  2, 27, 30, 25,  7, 21],
      dtype=int64), array([ 0, 21,  1, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5,
       26, 50, 45, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18, 41, 43, 16, 36,
       14, 44, 32, 38,  9, 29, 33, 20, 28, 15, 49,  8, 48,  4, 37, 10, 31],
      dtype=int64), array([ 0, 21,  1, 15, 49,  8, 48,  4, 37, 10, 31, 50, 45, 11, 46,  3, 17,
       13, 24, 12, 40, 39, 18, 41, 43, 16, 36, 14, 44, 32, 38,  9, 29, 33,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 48,  4, 37,  8, 29, 33, 49, 15,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 18, 39,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 48,  4, 37,  8, 29, 33, 49, 15,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 31, 10, 37,  4, 48,  9, 38, 29, 33, 49,  8, 15, 20, 28,
       19, 34, 35,  2, 27, 30, 25,  7, 47,  5, 22,  6, 42, 23, 13, 24, 12,
       40, 18, 39, 41, 43, 44, 32, 14, 36, 16,  3, 17, 46, 11, 45, 50, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 29,  8, 48,  4, 37, 15, 49, 33,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21, 27, 30, 25,  7, 47, 26,  5, 22,  6, 42, 23, 13, 24, 12, 40,
       39, 18, 41, 43, 14, 44, 32, 38,  9, 29,  8, 48,  4, 36, 16,  3, 17,
       46, 11, 50, 45, 10, 37, 15, 49, 33, 20, 28, 19, 34, 35,  2,  1, 31],
      dtype=int64)]
2025-08-05 10:29:10,392 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:10,392 - ExploitationExpert - INFO - populations: [{'tour': [0, 21, 18, 6, 22, 23, 13, 24, 17, 3, 16, 36, 14, 43, 41, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 42], 'cur_cost': 633.0}, {'tour': [0, 17, 20, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 12, 40, 18, 41, 43, 14, 36, 16, 3, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 33, 29, 9, 38, 32, 44, 39, 5, 47, 21, 42], 'cur_cost': 603.0}, {'tour': [33, 17, 15, 19, 34, 35, 13, 4, 36, 7, 16, 18, 22, 23, 41, 24, 38, 43, 46, 11, 45, 50, 5, 31, 10, 37, 28, 48, 8, 49, 2, 1, 29, 20, 21, 32, 9, 25, 47, 44, 39, 30, 6, 14, 0, 27, 42, 26, 3, 12, 40], 'cur_cost': 1323.0}, {'tour': [0, 20, 11, 7, 25, 30, 27, 2, 19, 34, 35, 28, 1, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 22, 6, 42, 23, 17, 39, 44, 32, 9, 29, 33, 38, 21], 'cur_cost': 591.0}, {'tour': [16, 48, 32, 33, 4, 49, 0, 1, 19, 34, 7, 23, 24, 6, 17, 18, 3, 50, 26, 37, 21, 25, 31, 10, 2, 5, 45, 46, 14, 40, 44, 9, 15, 29, 43, 13, 11, 47, 22, 42, 27, 30, 20, 38, 28, 36, 41, 39, 12, 8, 35], 'cur_cost': 1151.0}, {'tour': array([17, 27, 42, 49, 38, 11, 30, 47, 46, 14,  2,  0, 35, 19,  7, 25, 36,
       16,  1, 39, 29,  4, 28, 37,  5,  9, 12, 48, 18, 45, 20, 23, 13, 26,
       44, 32, 50, 34, 40, 21, 22,  6, 41,  3, 31, 43, 15,  8, 10, 33, 24],
      dtype=int64), 'cur_cost': 1584.0}, {'tour': array([11, 37, 42, 32,  2,  7,  6, 34, 17, 49, 13, 12, 15,  8, 16, 46, 23,
        1,  3, 10, 20, 27, 28, 50,  9, 31, 35, 48,  4, 22, 39, 38, 43, 41,
       21, 29,  0, 44, 26, 24, 30, 33, 45, 18,  5, 36, 40, 19, 47, 25, 14],
      dtype=int64), 'cur_cost': 1707.0}, {'tour': [0, 13, 20, 16, 36, 14, 43, 41, 18, 40, 12, 24, 17, 3, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 21, 33, 29, 9, 38, 32, 44, 39], 'cur_cost': 629.0}, {'tour': [29, 26, 38, 14, 37, 0, 10, 3, 11, 21, 4, 16, 7, 5, 22, 35, 8, 9, 20, 49, 34, 6, 23, 47, 15, 18, 43, 46, 41, 40, 39, 17, 31, 45, 48, 50, 25, 1, 19, 36, 32, 12, 44, 33, 13, 42, 30, 24, 2, 28, 27], 'cur_cost': 1358.0}, {'tour': [34, 48, 36, 13, 24, 8, 3, 27, 30, 14, 43, 41, 50, 45, 12, 39, 5, 11, 10, 40, 33, 37, 42, 15, 16, 49, 32, 9, 2, 35, 20, 28, 38, 22, 44, 25, 7, 21, 23, 17, 29, 6, 0, 4, 31, 19, 1, 47, 46, 18, 26], 'cur_cost': 1463.0}]
2025-08-05 10:29:10,393 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:10,394 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 337, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 337, 'cache_hits': 0, 'similarity_calculations': 1734, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:10,394 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([11, 37, 42, 32,  2,  7,  6, 34, 17, 49, 13, 12, 15,  8, 16, 46, 23,
        1,  3, 10, 20, 27, 28, 50,  9, 31, 35, 48,  4, 22, 39, 38, 43, 41,
       21, 29,  0, 44, 26, 24, 30, 33, 45, 18,  5, 36, 40, 19, 47, 25, 14],
      dtype=int64), 'cur_cost': 1707.0, 'intermediate_solutions': [{'tour': array([16, 46, 10, 11, 18, 14, 49,  9, 33, 39, 21,  0, 38, 41, 25, 15, 48,
       43, 34, 19, 20, 31, 47,  5, 28,  2, 36, 22, 29, 27, 23, 40, 12, 42,
       24,  6,  8,  7, 32, 50,  4, 44,  1, 30, 17, 13, 37, 26, 35, 45,  3]), 'cur_cost': 1516.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([11, 16, 46, 10, 18, 14, 49,  9, 33, 39, 21,  0, 38, 41, 25, 15, 48,
       43, 34, 19, 20, 31, 47,  5, 28,  2, 36, 22, 29, 27, 23, 40, 12, 42,
       24,  6,  8,  7, 32, 50,  4, 44,  1, 30, 17, 13, 37, 26, 35, 45,  3]), 'cur_cost': 1531.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([18, 11, 16, 46, 10, 14, 49,  9, 33, 39, 21,  0, 38, 41, 25, 15, 48,
       43, 34, 19, 20, 31, 47,  5, 28,  2, 36, 22, 29, 27, 23, 40, 12, 42,
       24,  6,  8,  7, 32, 50,  4, 44,  1, 30, 17, 13, 37, 26, 35, 45,  3]), 'cur_cost': 1522.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([10, 11, 16, 46, 18, 14, 49,  9, 33, 39, 21,  0, 38, 41, 25, 15, 48,
       43, 34, 19, 20, 31, 47,  5, 28,  2, 36, 22, 29, 27, 23, 40, 12, 42,
       24,  6,  8,  7, 32, 50,  4, 44,  1, 30, 17, 13, 37, 26, 35, 45,  3]), 'cur_cost': 1522.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([10, 18, 11, 16, 46, 14, 49,  9, 33, 39, 21,  0, 38, 41, 25, 15, 48,
       43, 34, 19, 20, 31, 47,  5, 28,  2, 36, 22, 29, 27, 23, 40, 12, 42,
       24,  6,  8,  7, 32, 50,  4, 44,  1, 30, 17, 13, 37, 26, 35, 45,  3]), 'cur_cost': 1548.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:10,395 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1707.00)
2025-08-05 10:29:10,395 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:10,395 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:10,395 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:10,396 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 51
2025-08-05 10:29:10,396 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,396 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,397 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,397 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,397 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1578.0, 路径长度: 51, 收集中间解: 3
2025-08-05 10:29:10,397 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [33, 20, 18, 7, 22, 30, 2, 24, 36, 3, 35, 6, 14, 15, 13, 40, 48, 39, 37, 41, 31, 26, 5, 16, 46, 11, 45, 50, 43, 44, 28, 4, 21, 8, 49, 38, 32, 42, 27, 29, 17, 25, 0, 19, 1, 47, 34, 12, 10, 9, 23], 'cur_cost': 1578.0, 'intermediate_solutions': [{'tour': [0, 13, 20, 16, 36, 14, 43, 41, 18, 40, 12, 24, 42, 3, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 17, 47, 5, 21, 33, 29, 9, 38, 32, 44, 39], 'cur_cost': 687.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 20, 16, 36, 14, 43, 41, 18, 40, 12, 24, 17, 3, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 19, 34, 35, 2, 27, 30, 7, 32, 38, 9, 29, 33, 21, 5, 47, 42, 23, 22, 6, 25, 44, 39], 'cur_cost': 728.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 20, 16, 36, 43, 14, 41, 18, 40, 12, 24, 17, 3, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 21, 33, 29, 9, 38, 32, 44, 39], 'cur_cost': 635.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:10,398 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1578.00)
2025-08-05 10:29:10,398 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:29:10,398 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:29:10,398 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:10,399 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 51
2025-08-05 10:29:10,399 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,399 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,399 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,400 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:10,400 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1722.0, 路径长度: 51, 收集中间解: 3
2025-08-05 10:29:10,400 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [15, 6, 4, 23, 2, 7, 36, 1, 43, 18, 3, 40, 26, 37, 21, 14, 50, 45, 11, 48, 41, 31, 10, 28, 12, 33, 8, 25, 32, 42, 39, 47, 20, 19, 44, 34, 29, 24, 13, 35, 38, 0, 49, 9, 5, 27, 46, 16, 22, 17, 30], 'cur_cost': 1722.0, 'intermediate_solutions': [{'tour': [29, 26, 38, 14, 37, 0, 10, 3, 11, 21, 4, 16, 7, 5, 22, 35, 8, 9, 20, 49, 34, 6, 23, 47, 15, 18, 43, 46, 41, 40, 39, 17, 31, 45, 48, 50, 25, 1, 19, 36, 32, 12, 44, 33, 13, 42, 30, 24, 28, 2, 27], 'cur_cost': 1343.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [29, 26, 38, 14, 37, 0, 10, 3, 11, 21, 4, 16, 7, 6, 34, 49, 20, 9, 8, 35, 22, 5, 23, 47, 15, 18, 43, 46, 41, 40, 39, 17, 31, 45, 48, 50, 25, 1, 19, 36, 32, 12, 44, 33, 13, 42, 30, 24, 2, 28, 27], 'cur_cost': 1354.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [29, 26, 38, 14, 37, 0, 10, 3, 11, 21, 4, 16, 7, 5, 22, 35, 8, 9, 20, 34, 6, 23, 47, 15, 18, 43, 46, 41, 40, 39, 17, 49, 31, 45, 48, 50, 25, 1, 19, 36, 32, 12, 44, 33, 13, 42, 30, 24, 2, 28, 27], 'cur_cost': 1378.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:10,400 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1722.00)
2025-08-05 10:29:10,400 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:29:10,400 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:10,401 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:10,401 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 1727.0
2025-08-05 10:29:10,409 - ExploitationExpert - INFO - res_population_num: 8
2025-08-05 10:29:10,409 - ExploitationExpert - INFO - res_population_costs: [426, 428, 430, 434, 434, 434, 435, 441.0]
2025-08-05 10:29:10,409 - ExploitationExpert - INFO - res_populations: [array([ 0, 31, 10, 37,  4, 36, 16,  3, 17, 46, 11, 45, 50, 26,  5, 47, 22,
        6, 42, 23, 13, 24, 12, 40, 39, 18, 41, 43, 14, 44, 32, 38,  9, 48,
        8, 29, 33, 49, 15, 20, 28,  1, 19, 34, 35,  2, 27, 30, 25,  7, 21],
      dtype=int64), array([ 0, 21,  1, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5,
       26, 50, 45, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18, 41, 43, 16, 36,
       14, 44, 32, 38,  9, 29, 33, 20, 28, 15, 49,  8, 48,  4, 37, 10, 31],
      dtype=int64), array([ 0, 21,  1, 15, 49,  8, 48,  4, 37, 10, 31, 50, 45, 11, 46,  3, 17,
       13, 24, 12, 40, 39, 18, 41, 43, 16, 36, 14, 44, 32, 38,  9, 29, 33,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 48,  4, 37,  8, 29, 33, 49, 15,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 18, 39,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 48,  4, 37,  8, 29, 33, 49, 15,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 31, 10, 37,  4, 48,  9, 38, 29, 33, 49,  8, 15, 20, 28,
       19, 34, 35,  2, 27, 30, 25,  7, 47,  5, 22,  6, 42, 23, 13, 24, 12,
       40, 18, 39, 41, 43, 44, 32, 14, 36, 16,  3, 17, 46, 11, 45, 50, 26],
      dtype=int64), array([ 0, 21,  1, 10, 31, 45, 50, 11, 46,  3, 17, 13, 24, 12, 40, 39, 18,
       41, 43, 16, 36, 14, 44, 32, 38,  9, 29,  8, 48,  4, 37, 15, 49, 33,
       20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6, 42, 23, 22, 47,  5, 26],
      dtype=int64), array([ 0, 21, 27, 30, 25,  7, 47, 26,  5, 22,  6, 42, 23, 13, 24, 12, 40,
       39, 18, 41, 43, 14, 44, 32, 38,  9, 29,  8, 48,  4, 36, 16,  3, 17,
       46, 11, 50, 45, 10, 37, 15, 49, 33, 20, 28, 19, 34, 35,  2,  1, 31],
      dtype=int64)]
2025-08-05 10:29:10,412 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:10,412 - ExploitationExpert - INFO - populations: [{'tour': [0, 21, 18, 6, 22, 23, 13, 24, 17, 3, 16, 36, 14, 43, 41, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 42], 'cur_cost': 633.0}, {'tour': [0, 17, 20, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 12, 40, 18, 41, 43, 14, 36, 16, 3, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 33, 29, 9, 38, 32, 44, 39, 5, 47, 21, 42], 'cur_cost': 603.0}, {'tour': [33, 17, 15, 19, 34, 35, 13, 4, 36, 7, 16, 18, 22, 23, 41, 24, 38, 43, 46, 11, 45, 50, 5, 31, 10, 37, 28, 48, 8, 49, 2, 1, 29, 20, 21, 32, 9, 25, 47, 44, 39, 30, 6, 14, 0, 27, 42, 26, 3, 12, 40], 'cur_cost': 1323.0}, {'tour': [0, 20, 11, 7, 25, 30, 27, 2, 19, 34, 35, 28, 1, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 22, 6, 42, 23, 17, 39, 44, 32, 9, 29, 33, 38, 21], 'cur_cost': 591.0}, {'tour': [16, 48, 32, 33, 4, 49, 0, 1, 19, 34, 7, 23, 24, 6, 17, 18, 3, 50, 26, 37, 21, 25, 31, 10, 2, 5, 45, 46, 14, 40, 44, 9, 15, 29, 43, 13, 11, 47, 22, 42, 27, 30, 20, 38, 28, 36, 41, 39, 12, 8, 35], 'cur_cost': 1151.0}, {'tour': array([17, 27, 42, 49, 38, 11, 30, 47, 46, 14,  2,  0, 35, 19,  7, 25, 36,
       16,  1, 39, 29,  4, 28, 37,  5,  9, 12, 48, 18, 45, 20, 23, 13, 26,
       44, 32, 50, 34, 40, 21, 22,  6, 41,  3, 31, 43, 15,  8, 10, 33, 24],
      dtype=int64), 'cur_cost': 1584.0}, {'tour': array([11, 37, 42, 32,  2,  7,  6, 34, 17, 49, 13, 12, 15,  8, 16, 46, 23,
        1,  3, 10, 20, 27, 28, 50,  9, 31, 35, 48,  4, 22, 39, 38, 43, 41,
       21, 29,  0, 44, 26, 24, 30, 33, 45, 18,  5, 36, 40, 19, 47, 25, 14],
      dtype=int64), 'cur_cost': 1707.0}, {'tour': [33, 20, 18, 7, 22, 30, 2, 24, 36, 3, 35, 6, 14, 15, 13, 40, 48, 39, 37, 41, 31, 26, 5, 16, 46, 11, 45, 50, 43, 44, 28, 4, 21, 8, 49, 38, 32, 42, 27, 29, 17, 25, 0, 19, 1, 47, 34, 12, 10, 9, 23], 'cur_cost': 1578.0}, {'tour': [15, 6, 4, 23, 2, 7, 36, 1, 43, 18, 3, 40, 26, 37, 21, 14, 50, 45, 11, 48, 41, 31, 10, 28, 12, 33, 8, 25, 32, 42, 39, 47, 20, 19, 44, 34, 29, 24, 13, 35, 38, 0, 49, 9, 5, 27, 46, 16, 22, 17, 30], 'cur_cost': 1722.0}, {'tour': array([49, 42,  7, 10,  6, 24, 29, 45, 30, 47, 40, 32,  2, 38, 15, 27, 34,
       16, 44, 19,  9, 26, 22, 50, 25, 37, 12, 48, 43, 35, 39, 33, 13, 36,
        8, 46, 14,  4, 28, 23, 41, 20,  5,  0, 21, 17, 18, 11,  3,  1, 31],
      dtype=int64), 'cur_cost': 1727.0}]
2025-08-05 10:29:10,413 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:10,413 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 338, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 338, 'cache_hits': 0, 'similarity_calculations': 1746, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:10,414 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([49, 42,  7, 10,  6, 24, 29, 45, 30, 47, 40, 32,  2, 38, 15, 27, 34,
       16, 44, 19,  9, 26, 22, 50, 25, 37, 12, 48, 43, 35, 39, 33, 13, 36,
        8, 46, 14,  4, 28, 23, 41, 20,  5,  0, 21, 17, 18, 11,  3,  1, 31],
      dtype=int64), 'cur_cost': 1727.0, 'intermediate_solutions': [{'tour': array([36, 48, 34, 13, 24,  8,  3, 27, 30, 14, 43, 41, 50, 45, 12, 39,  5,
       11, 10, 40, 33, 37, 42, 15, 16, 49, 32,  9,  2, 35, 20, 28, 38, 22,
       44, 25,  7, 21, 23, 17, 29,  6,  0,  4, 31, 19,  1, 47, 46, 18, 26]), 'cur_cost': 1480.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([13, 36, 48, 34, 24,  8,  3, 27, 30, 14, 43, 41, 50, 45, 12, 39,  5,
       11, 10, 40, 33, 37, 42, 15, 16, 49, 32,  9,  2, 35, 20, 28, 38, 22,
       44, 25,  7, 21, 23, 17, 29,  6,  0,  4, 31, 19,  1, 47, 46, 18, 26]), 'cur_cost': 1501.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([24, 13, 36, 48, 34,  8,  3, 27, 30, 14, 43, 41, 50, 45, 12, 39,  5,
       11, 10, 40, 33, 37, 42, 15, 16, 49, 32,  9,  2, 35, 20, 28, 38, 22,
       44, 25,  7, 21, 23, 17, 29,  6,  0,  4, 31, 19,  1, 47, 46, 18, 26]), 'cur_cost': 1440.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([34, 13, 36, 48, 24,  8,  3, 27, 30, 14, 43, 41, 50, 45, 12, 39,  5,
       11, 10, 40, 33, 37, 42, 15, 16, 49, 32,  9,  2, 35, 20, 28, 38, 22,
       44, 25,  7, 21, 23, 17, 29,  6,  0,  4, 31, 19,  1, 47, 46, 18, 26]), 'cur_cost': 1515.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([34, 24, 13, 36, 48,  8,  3, 27, 30, 14, 43, 41, 50, 45, 12, 39,  5,
       11, 10, 40, 33, 37, 42, 15, 16, 49, 32,  9,  2, 35, 20, 28, 38, 22,
       44, 25,  7, 21, 23, 17, 29,  6,  0,  4, 31, 19,  1, 47, 46, 18, 26]), 'cur_cost': 1446.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:10,415 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1727.00)
2025-08-05 10:29:10,415 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:10,415 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:10,418 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 18, 6, 22, 23, 13, 24, 17, 3, 16, 36, 14, 43, 41, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 42], 'cur_cost': 633.0, 'intermediate_solutions': [{'tour': [0, 3, 1, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 17, 13, 24, 29, 40, 18, 41, 43, 14, 36, 16, 44, 32, 9, 12, 33, 20, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 21, 38, 39], 'cur_cost': 791.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 1, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 17, 13, 24, 12, 40, 18, 41, 43, 14, 36, 16, 44, 32, 9, 29, 33, 20, 28, 19, 34, 25, 7, 30, 27, 2, 35, 6, 22, 23, 42, 47, 5, 21, 38, 39], 'cur_cost': 689.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 1, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 17, 13, 24, 43, 12, 40, 18, 41, 14, 36, 16, 44, 32, 9, 29, 33, 20, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 21, 38, 39], 'cur_cost': 672.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 20, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 12, 40, 18, 41, 43, 14, 36, 16, 3, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 33, 29, 9, 38, 32, 44, 39, 5, 47, 21, 42], 'cur_cost': 603.0, 'intermediate_solutions': [{'tour': [0, 17, 1, 3, 46, 11, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 26, 31, 10, 37, 4, 48, 8, 49, 15, 28, 20, 33, 29, 9, 38, 32, 44, 45, 50, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 6, 22, 23, 42, 39], 'cur_cost': 641.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 1, 11, 2, 27, 30, 25, 7, 47, 50, 45, 44, 32, 38, 9, 29, 33, 20, 28, 15, 49, 8, 48, 4, 37, 10, 31, 26, 5, 13, 24, 12, 40, 18, 41, 43, 14, 36, 16, 3, 46, 19, 34, 35, 21, 6, 22, 23, 42, 39], 'cur_cost': 692.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 1, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 26, 31, 10, 37, 4, 48, 8, 49, 15, 28, 20, 50, 33, 29, 9, 38, 32, 44, 45, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 6, 22, 23, 42, 39], 'cur_cost': 681.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [33, 17, 15, 19, 34, 35, 13, 4, 36, 7, 16, 18, 22, 23, 41, 24, 38, 43, 46, 11, 45, 50, 5, 31, 10, 37, 28, 48, 8, 49, 2, 1, 29, 20, 21, 32, 9, 25, 47, 44, 39, 30, 6, 14, 0, 27, 42, 26, 3, 12, 40], 'cur_cost': 1323.0, 'intermediate_solutions': [{'tour': [9, 3, 15, 16, 49, 14, 43, 4, 37, 10, 41, 18, 40, 39, 26, 46, 17, 13, 24, 48, 32, 33, 5, 8, 42, 36, 28, 20, 11, 29, 2, 38, 35, 44, 45, 22, 47, 25, 7, 27, 12, 19, 6, 34, 23, 21, 1, 50, 31, 30, 0], 'cur_cost': 1431.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 3, 15, 16, 49, 14, 43, 4, 37, 10, 41, 18, 40, 12, 39, 27, 7, 25, 47, 22, 45, 44, 35, 38, 2, 29, 11, 20, 28, 36, 42, 8, 5, 33, 32, 48, 24, 13, 17, 46, 26, 19, 6, 34, 23, 21, 1, 50, 31, 30, 0], 'cur_cost': 1380.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 15, 16, 49, 14, 43, 4, 37, 10, 41, 18, 40, 12, 26, 46, 17, 13, 24, 48, 32, 33, 5, 9, 8, 42, 36, 28, 20, 11, 29, 2, 38, 35, 44, 45, 22, 47, 25, 7, 27, 39, 19, 6, 34, 23, 21, 1, 50, 31, 30, 0], 'cur_cost': 1424.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 11, 7, 25, 30, 27, 2, 19, 34, 35, 28, 1, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 22, 6, 42, 23, 17, 39, 44, 32, 9, 29, 33, 38, 21], 'cur_cost': 591.0, 'intermediate_solutions': [{'tour': [0, 2, 1, 13, 24, 17, 3, 16, 36, 46, 43, 41, 18, 40, 12, 39, 14, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 28, 20, 33, 29, 9, 38, 32, 44, 5, 47, 7, 25, 30, 27, 21, 19, 34, 35, 6, 22, 23, 42], 'cur_cost': 612.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 1, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 10, 31, 26, 50, 45, 11, 46, 39, 37, 4, 48, 8, 49, 15, 28, 20, 33, 29, 9, 38, 32, 44, 5, 47, 7, 25, 30, 27, 21, 19, 34, 35, 6, 22, 23, 42], 'cur_cost': 647.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 1, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 6, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 28, 20, 33, 29, 9, 38, 32, 44, 5, 47, 7, 25, 30, 27, 21, 19, 34, 35, 22, 23, 42], 'cur_cost': 659.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [16, 48, 32, 33, 4, 49, 0, 1, 19, 34, 7, 23, 24, 6, 17, 18, 3, 50, 26, 37, 21, 25, 31, 10, 2, 5, 45, 46, 14, 40, 44, 9, 15, 29, 43, 13, 11, 47, 22, 42, 27, 30, 20, 38, 28, 36, 41, 39, 12, 8, 35], 'cur_cost': 1151.0, 'intermediate_solutions': [{'tour': [34, 48, 36, 9, 32, 37, 19, 27, 30, 6, 47, 0, 25, 31, 1, 7, 23, 24, 45, 22, 11, 4, 14, 49, 33, 50, 43, 44, 3, 18, 12, 28, 40, 46, 15, 29, 38, 10, 41, 21, 35, 5, 42, 17, 16, 13, 39, 26, 2, 20, 8], 'cur_cost': 1235.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [34, 48, 36, 9, 32, 37, 19, 27, 30, 6, 47, 0, 25, 31, 1, 7, 23, 24, 45, 22, 11, 4, 14, 49, 33, 50, 43, 44, 3, 18, 12, 41, 40, 46, 15, 29, 38, 10, 28, 21, 35, 5, 2, 26, 39, 13, 16, 17, 42, 20, 8], 'cur_cost': 1151.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [34, 48, 36, 9, 32, 37, 19, 27, 30, 6, 47, 0, 25, 31, 1, 23, 24, 45, 7, 22, 11, 4, 14, 49, 33, 50, 43, 44, 3, 18, 12, 41, 40, 46, 15, 29, 38, 10, 28, 21, 35, 5, 42, 17, 16, 13, 39, 26, 2, 20, 8], 'cur_cost': 1111.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([17, 27, 42, 49, 38, 11, 30, 47, 46, 14,  2,  0, 35, 19,  7, 25, 36,
       16,  1, 39, 29,  4, 28, 37,  5,  9, 12, 48, 18, 45, 20, 23, 13, 26,
       44, 32, 50, 34, 40, 21, 22,  6, 41,  3, 31, 43, 15,  8, 10, 33, 24],
      dtype=int64), 'cur_cost': 1584.0, 'intermediate_solutions': [{'tour': array([28, 11, 33, 37,  3, 12,  8, 17, 16, 27, 50,  6, 40, 44, 42, 47, 38,
       39, 19,  4, 20, 36, 48, 35, 49, 23, 43, 10,  0, 41, 25, 34,  5, 29,
       21,  1, 13, 45,  2, 14, 15,  9, 30, 24, 32, 31, 22, 46, 18, 26,  7]), 'cur_cost': 1780.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([37, 28, 11, 33,  3, 12,  8, 17, 16, 27, 50,  6, 40, 44, 42, 47, 38,
       39, 19,  4, 20, 36, 48, 35, 49, 23, 43, 10,  0, 41, 25, 34,  5, 29,
       21,  1, 13, 45,  2, 14, 15,  9, 30, 24, 32, 31, 22, 46, 18, 26,  7]), 'cur_cost': 1797.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3, 37, 28, 11, 33, 12,  8, 17, 16, 27, 50,  6, 40, 44, 42, 47, 38,
       39, 19,  4, 20, 36, 48, 35, 49, 23, 43, 10,  0, 41, 25, 34,  5, 29,
       21,  1, 13, 45,  2, 14, 15,  9, 30, 24, 32, 31, 22, 46, 18, 26,  7]), 'cur_cost': 1832.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([33, 37, 28, 11,  3, 12,  8, 17, 16, 27, 50,  6, 40, 44, 42, 47, 38,
       39, 19,  4, 20, 36, 48, 35, 49, 23, 43, 10,  0, 41, 25, 34,  5, 29,
       21,  1, 13, 45,  2, 14, 15,  9, 30, 24, 32, 31, 22, 46, 18, 26,  7]), 'cur_cost': 1766.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([33,  3, 37, 28, 11, 12,  8, 17, 16, 27, 50,  6, 40, 44, 42, 47, 38,
       39, 19,  4, 20, 36, 48, 35, 49, 23, 43, 10,  0, 41, 25, 34,  5, 29,
       21,  1, 13, 45,  2, 14, 15,  9, 30, 24, 32, 31, 22, 46, 18, 26,  7]), 'cur_cost': 1818.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([11, 37, 42, 32,  2,  7,  6, 34, 17, 49, 13, 12, 15,  8, 16, 46, 23,
        1,  3, 10, 20, 27, 28, 50,  9, 31, 35, 48,  4, 22, 39, 38, 43, 41,
       21, 29,  0, 44, 26, 24, 30, 33, 45, 18,  5, 36, 40, 19, 47, 25, 14],
      dtype=int64), 'cur_cost': 1707.0, 'intermediate_solutions': [{'tour': array([16, 46, 10, 11, 18, 14, 49,  9, 33, 39, 21,  0, 38, 41, 25, 15, 48,
       43, 34, 19, 20, 31, 47,  5, 28,  2, 36, 22, 29, 27, 23, 40, 12, 42,
       24,  6,  8,  7, 32, 50,  4, 44,  1, 30, 17, 13, 37, 26, 35, 45,  3]), 'cur_cost': 1516.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([11, 16, 46, 10, 18, 14, 49,  9, 33, 39, 21,  0, 38, 41, 25, 15, 48,
       43, 34, 19, 20, 31, 47,  5, 28,  2, 36, 22, 29, 27, 23, 40, 12, 42,
       24,  6,  8,  7, 32, 50,  4, 44,  1, 30, 17, 13, 37, 26, 35, 45,  3]), 'cur_cost': 1531.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([18, 11, 16, 46, 10, 14, 49,  9, 33, 39, 21,  0, 38, 41, 25, 15, 48,
       43, 34, 19, 20, 31, 47,  5, 28,  2, 36, 22, 29, 27, 23, 40, 12, 42,
       24,  6,  8,  7, 32, 50,  4, 44,  1, 30, 17, 13, 37, 26, 35, 45,  3]), 'cur_cost': 1522.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([10, 11, 16, 46, 18, 14, 49,  9, 33, 39, 21,  0, 38, 41, 25, 15, 48,
       43, 34, 19, 20, 31, 47,  5, 28,  2, 36, 22, 29, 27, 23, 40, 12, 42,
       24,  6,  8,  7, 32, 50,  4, 44,  1, 30, 17, 13, 37, 26, 35, 45,  3]), 'cur_cost': 1522.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([10, 18, 11, 16, 46, 14, 49,  9, 33, 39, 21,  0, 38, 41, 25, 15, 48,
       43, 34, 19, 20, 31, 47,  5, 28,  2, 36, 22, 29, 27, 23, 40, 12, 42,
       24,  6,  8,  7, 32, 50,  4, 44,  1, 30, 17, 13, 37, 26, 35, 45,  3]), 'cur_cost': 1548.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [33, 20, 18, 7, 22, 30, 2, 24, 36, 3, 35, 6, 14, 15, 13, 40, 48, 39, 37, 41, 31, 26, 5, 16, 46, 11, 45, 50, 43, 44, 28, 4, 21, 8, 49, 38, 32, 42, 27, 29, 17, 25, 0, 19, 1, 47, 34, 12, 10, 9, 23], 'cur_cost': 1578.0, 'intermediate_solutions': [{'tour': [0, 13, 20, 16, 36, 14, 43, 41, 18, 40, 12, 24, 42, 3, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 17, 47, 5, 21, 33, 29, 9, 38, 32, 44, 39], 'cur_cost': 687.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 20, 16, 36, 14, 43, 41, 18, 40, 12, 24, 17, 3, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 19, 34, 35, 2, 27, 30, 7, 32, 38, 9, 29, 33, 21, 5, 47, 42, 23, 22, 6, 25, 44, 39], 'cur_cost': 728.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 20, 16, 36, 43, 14, 41, 18, 40, 12, 24, 17, 3, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 21, 33, 29, 9, 38, 32, 44, 39], 'cur_cost': 635.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [15, 6, 4, 23, 2, 7, 36, 1, 43, 18, 3, 40, 26, 37, 21, 14, 50, 45, 11, 48, 41, 31, 10, 28, 12, 33, 8, 25, 32, 42, 39, 47, 20, 19, 44, 34, 29, 24, 13, 35, 38, 0, 49, 9, 5, 27, 46, 16, 22, 17, 30], 'cur_cost': 1722.0, 'intermediate_solutions': [{'tour': [29, 26, 38, 14, 37, 0, 10, 3, 11, 21, 4, 16, 7, 5, 22, 35, 8, 9, 20, 49, 34, 6, 23, 47, 15, 18, 43, 46, 41, 40, 39, 17, 31, 45, 48, 50, 25, 1, 19, 36, 32, 12, 44, 33, 13, 42, 30, 24, 28, 2, 27], 'cur_cost': 1343.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [29, 26, 38, 14, 37, 0, 10, 3, 11, 21, 4, 16, 7, 6, 34, 49, 20, 9, 8, 35, 22, 5, 23, 47, 15, 18, 43, 46, 41, 40, 39, 17, 31, 45, 48, 50, 25, 1, 19, 36, 32, 12, 44, 33, 13, 42, 30, 24, 2, 28, 27], 'cur_cost': 1354.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [29, 26, 38, 14, 37, 0, 10, 3, 11, 21, 4, 16, 7, 5, 22, 35, 8, 9, 20, 34, 6, 23, 47, 15, 18, 43, 46, 41, 40, 39, 17, 49, 31, 45, 48, 50, 25, 1, 19, 36, 32, 12, 44, 33, 13, 42, 30, 24, 2, 28, 27], 'cur_cost': 1378.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([49, 42,  7, 10,  6, 24, 29, 45, 30, 47, 40, 32,  2, 38, 15, 27, 34,
       16, 44, 19,  9, 26, 22, 50, 25, 37, 12, 48, 43, 35, 39, 33, 13, 36,
        8, 46, 14,  4, 28, 23, 41, 20,  5,  0, 21, 17, 18, 11,  3,  1, 31],
      dtype=int64), 'cur_cost': 1727.0, 'intermediate_solutions': [{'tour': array([36, 48, 34, 13, 24,  8,  3, 27, 30, 14, 43, 41, 50, 45, 12, 39,  5,
       11, 10, 40, 33, 37, 42, 15, 16, 49, 32,  9,  2, 35, 20, 28, 38, 22,
       44, 25,  7, 21, 23, 17, 29,  6,  0,  4, 31, 19,  1, 47, 46, 18, 26]), 'cur_cost': 1480.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([13, 36, 48, 34, 24,  8,  3, 27, 30, 14, 43, 41, 50, 45, 12, 39,  5,
       11, 10, 40, 33, 37, 42, 15, 16, 49, 32,  9,  2, 35, 20, 28, 38, 22,
       44, 25,  7, 21, 23, 17, 29,  6,  0,  4, 31, 19,  1, 47, 46, 18, 26]), 'cur_cost': 1501.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([24, 13, 36, 48, 34,  8,  3, 27, 30, 14, 43, 41, 50, 45, 12, 39,  5,
       11, 10, 40, 33, 37, 42, 15, 16, 49, 32,  9,  2, 35, 20, 28, 38, 22,
       44, 25,  7, 21, 23, 17, 29,  6,  0,  4, 31, 19,  1, 47, 46, 18, 26]), 'cur_cost': 1440.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([34, 13, 36, 48, 24,  8,  3, 27, 30, 14, 43, 41, 50, 45, 12, 39,  5,
       11, 10, 40, 33, 37, 42, 15, 16, 49, 32,  9,  2, 35, 20, 28, 38, 22,
       44, 25,  7, 21, 23, 17, 29,  6,  0,  4, 31, 19,  1, 47, 46, 18, 26]), 'cur_cost': 1515.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([34, 24, 13, 36, 48,  8,  3, 27, 30, 14, 43, 41, 50, 45, 12, 39,  5,
       11, 10, 40, 33, 37, 42, 15, 16, 49, 32,  9,  2, 35, 20, 28, 38, 22,
       44, 25,  7, 21, 23, 17, 29,  6,  0,  4, 31, 19,  1, 47, 46, 18, 26]), 'cur_cost': 1446.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:29:10,418 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:10,418 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:10,420 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=591.000, 多样性=0.954
2025-08-05 10:29:10,421 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 10:29:10,421 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 10:29:10,421 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:10,422 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.03536134817233149, 'best_improvement': -0.011986301369863013}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.007706255666364586}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 1.1725237181803757e-05, 'recent_improvements': [0.007385917808184476, -0.05381899370604224, 0.00736246733382087], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 8, 'new_count': 8, 'count_change': 0, 'old_best_cost': 426, 'new_best_cost': 426, 'quality_improvement': 0.0, 'old_diversity': 0.8130252100840336, 'new_diversity': 0.8130252100840336, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:29:10,422 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 10:29:10,425 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\eil51_solution.json
2025-08-05 10:29:10,426 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\eil51_20250805_102910.solution
2025-08-05 10:29:10,426 - __main__ - INFO - 实例执行完成 - 运行时间: 1.61s, 最佳成本: 426
2025-08-05 10:29:10,426 - __main__ - INFO - 实例 eil51 处理完成
