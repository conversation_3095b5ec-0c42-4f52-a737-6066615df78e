2025-08-05 10:29:01,733 - __main__ - INFO - composite10_55 开始进化第 1 代
2025-08-05 10:29:01,734 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 10:29:01,735 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:01,738 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10711.000, 多样性=0.983
2025-08-05 10:29:01,740 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:01,744 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.983
2025-08-05 10:29:01,747 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:01,750 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 10:29:01,750 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:01,750 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 10:29:01,750 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 10:29:01,775 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -7070.760, 聚类评分: 0.000, 覆盖率: 0.124, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:01,775 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 10:29:01,775 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 10:29:01,775 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite10_55
2025-08-05 10:29:01,784 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=8.3%, 梯度: 2952.16 → 2707.52
2025-08-05 10:29:01,917 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite10_55\landscape_composite10_55_iter_106_20250805_102901.html
2025-08-05 10:29:01,992 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite10_55\dashboard_composite10_55_iter_106_20250805_102901.html
2025-08-05 10:29:01,992 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 106
2025-08-05 10:29:01,992 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 10:29:01,992 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2420秒
2025-08-05 10:29:01,993 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 212, 'max_size': 500, 'hits': 0, 'misses': 212, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 705, 'misses': 370, 'hit_rate': 0.6558139534883721, 'evictions': 270, 'ttl': 7200}}
2025-08-05 10:29:01,993 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -7070.7599999999975, 'local_optima_density': 0.1, 'gradient_variance': 3281774827.2064, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1241, 'fitness_entropy': 0.947730922119161, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -7070.760)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.124)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360941.7753654, 'performance_metrics': {}}}
2025-08-05 10:29:01,994 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:01,994 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:01,994 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:01,994 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:01,995 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:29:01,995 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 10:29:01,995 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:29:01,995 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:01,995 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:01,995 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:29:01,995 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:01,995 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:01,996 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:01,996 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:01,996 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:01,996 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:02,002 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 55
2025-08-05 10:29:02,003 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,003 - ExplorationExpert - INFO - 探索路径生成完成，成本: 50129.0, 路径长度: 55, 收集中间解: 0
2025-08-05 10:29:02,004 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [19, 21, 12, 14, 24, 23, 43, 35, 28, 30, 37, 10, 36, 31, 6, 9, 29, 22, 41, 38, 25, 33, 27, 26, 3, 18, 17, 20, 50, 44, 49, 46, 16, 48, 15, 51, 45, 13, 47, 34, 42, 4, 1, 2, 5, 7, 0, 32, 40, 39, 53, 52, 54, 11, 8], 'cur_cost': 50129.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:02,004 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 50129.00)
2025-08-05 10:29:02,004 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:02,004 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:02,004 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:02,009 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 10:29:02,009 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,010 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13709.0, 路径长度: 55, 收集中间解: 0
2025-08-05 10:29:02,010 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 8, 22, 9, 7, 3, 5, 6, 4, 1, 10, 2, 26, 31, 25, 23, 29, 27, 32, 28, 24, 30, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 13709.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:02,010 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 13709.00)
2025-08-05 10:29:02,010 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:02,010 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:02,010 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:02,012 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 55
2025-08-05 10:29:02,012 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,012 - ExplorationExpert - INFO - 探索路径生成完成，成本: 79157.0, 路径长度: 55, 收集中间解: 0
2025-08-05 10:29:02,012 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 8, 12, 7, 3, 5, 6, 4, 1, 10, 23, 22, 41, 28, 25, 30, 35, 26, 18, 17, 43, 44, 34, 46, 16, 48, 15, 51, 45, 53, 42, 40, 9, 38, 24, 11, 37, 49, 2, 14, 54, 19, 13, 47, 29, 21, 27, 50, 32, 33, 36, 39, 20, 52, 31], 'cur_cost': 79157.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:02,013 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 79157.00)
2025-08-05 10:29:02,013 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:29:02,013 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:02,013 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:02,013 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 111618.0
2025-08-05 10:29:02,021 - ExploitationExpert - INFO - res_population_num: 3
2025-08-05 10:29:02,021 - ExploitationExpert - INFO - res_population_costs: [10468.0, 10448, 10445]
2025-08-05 10:29:02,021 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  4,  6,  5,  9,  7,  3,  2,  8, 10, 43, 42, 37, 39, 34, 40,
       41, 38, 36, 33, 35, 29, 31, 26, 25, 23, 22, 27, 32, 28, 24, 30, 49,
       46, 44, 53, 54, 50, 48, 52, 47, 51, 45, 13, 17, 14, 11, 16, 15, 20,
       18, 12, 21, 19], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 30, 24, 26, 31, 25, 23, 28, 32, 27, 22, 29,
       35, 33, 36, 38, 41, 40, 34, 39, 37, 42, 43, 10,  8,  2,  3,  7,  9,
        5,  6,  4,  1], dtype=int64), array([ 0, 21, 19, 18, 12, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  9,  7,  2,  3,
        5,  6,  4,  1], dtype=int64)]
2025-08-05 10:29:02,023 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:02,023 - ExploitationExpert - INFO - populations: [{'tour': [19, 21, 12, 14, 24, 23, 43, 35, 28, 30, 37, 10, 36, 31, 6, 9, 29, 22, 41, 38, 25, 33, 27, 26, 3, 18, 17, 20, 50, 44, 49, 46, 16, 48, 15, 51, 45, 13, 47, 34, 42, 4, 1, 2, 5, 7, 0, 32, 40, 39, 53, 52, 54, 11, 8], 'cur_cost': 50129.0}, {'tour': [0, 8, 22, 9, 7, 3, 5, 6, 4, 1, 10, 2, 26, 31, 25, 23, 29, 27, 32, 28, 24, 30, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 13709.0}, {'tour': [0, 8, 12, 7, 3, 5, 6, 4, 1, 10, 23, 22, 41, 28, 25, 30, 35, 26, 18, 17, 43, 44, 34, 46, 16, 48, 15, 51, 45, 53, 42, 40, 9, 38, 24, 11, 37, 49, 2, 14, 54, 19, 13, 47, 29, 21, 27, 50, 32, 33, 36, 39, 20, 52, 31], 'cur_cost': 79157.0}, {'tour': array([38, 47, 12, 27, 46, 54,  5, 30, 16,  9, 13,  3, 52,  2, 33, 32, 22,
       45, 17,  7, 44, 51, 25, 50,  8, 11, 29, 34, 23, 26, 53, 31, 15, 28,
       43, 35, 19, 48, 36, 42,  4,  0, 49,  6, 21, 20, 40, 18, 37, 41,  1,
       39, 10, 24, 14], dtype=int64), 'cur_cost': 111618.0}, {'tour': array([51, 32, 26, 45, 16, 17, 47, 34,  7, 30, 20, 46, 37, 53, 44,  5, 42,
       40, 15, 27, 12, 52,  3, 33, 22,  4, 50, 29, 28,  0, 10, 18, 41, 19,
       36,  8,  1,  6, 23, 13, 38, 14, 49, 43, 21,  2, 48, 31, 39, 11, 25,
       35,  9, 54, 24], dtype=int64), 'cur_cost': 112135.0}, {'tour': array([ 2, 23, 11, 12,  0, 22,  6, 42, 40, 29, 27, 38,  7, 13,  3,  9, 25,
       31, 30, 10, 39,  5, 24, 45, 20, 43, 28, 16, 46, 34,  8, 50, 14, 52,
       21, 49, 35, 37,  1,  4, 32, 51, 41, 19, 17, 44, 54, 33, 47, 18, 48,
       15, 53, 36, 26], dtype=int64), 'cur_cost': 87861.0}, {'tour': array([23,  9, 41, 14,  5, 51, 31, 28, 11, 16, 24, 47, 52, 42, 27,  1, 44,
       48,  0, 12, 37, 32, 19,  4, 15, 17,  7, 45, 26, 10, 22,  2, 25, 18,
       43, 35, 30, 54, 50, 46, 40, 53, 29, 36, 34, 38, 33,  8,  3, 20, 39,
       21, 49, 13,  6], dtype=int64), 'cur_cost': 100809.0}, {'tour': array([ 2, 32, 28, 41, 14, 50,  0,  3, 22, 33, 47,  6, 35, 10, 13, 52,  8,
       43, 19, 42,  4, 27, 25, 24, 49,  1, 21, 46, 31, 48, 17, 20, 36, 30,
       23, 53, 18, 38,  5, 26, 29,  7, 11, 12, 37, 44, 45, 40, 16, 51, 54,
       15,  9, 39, 34], dtype=int64), 'cur_cost': 100562.0}, {'tour': array([48, 27,  5, 32, 36, 28,  4, 30, 12, 43, 45, 47, 23, 11, 49, 54, 44,
       31,  3, 41, 38, 16, 20, 24,  6, 35, 13,  2, 40, 15, 33, 25, 39, 52,
       21, 10,  8, 42, 51, 14, 37,  7,  1,  9, 29, 17, 53, 19, 22, 26, 34,
        0, 50, 18, 46], dtype=int64), 'cur_cost': 95548.0}, {'tour': array([ 1, 42,  0,  8,  6, 33, 11, 25, 21, 53, 23, 32, 54, 41, 40, 16, 17,
       37, 13, 46, 26, 51, 50, 29, 38, 47, 35, 52, 20, 18, 39, 43, 15, 12,
       10, 48,  5, 49,  4, 19,  9, 24, 30,  2,  3, 28, 31, 45, 14, 34, 44,
       27, 22,  7, 36], dtype=int64), 'cur_cost': 102208.0}]
2025-08-05 10:29:02,026 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:02,026 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 274, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 274, 'cache_hits': 0, 'similarity_calculations': 1356, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:02,027 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([38, 47, 12, 27, 46, 54,  5, 30, 16,  9, 13,  3, 52,  2, 33, 32, 22,
       45, 17,  7, 44, 51, 25, 50,  8, 11, 29, 34, 23, 26, 53, 31, 15, 28,
       43, 35, 19, 48, 36, 42,  4,  0, 49,  6, 21, 20, 40, 18, 37, 41,  1,
       39, 10, 24, 14], dtype=int64), 'cur_cost': 111618.0, 'intermediate_solutions': [{'tour': array([17, 41,  9, 39, 34, 14, 47, 16, 31, 50, 13, 28, 26, 32, 22,  3, 40,
       21, 24, 36,  7, 12,  6, 20,  5, 35, 48, 49, 37,  1,  2, 33, 10, 52,
       54, 42, 53, 45, 46, 27, 44, 25, 51, 11,  4, 38, 23, 29, 15, 19,  8,
       43, 18, 30,  0], dtype=int64), 'cur_cost': 103968.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([39, 17, 41,  9, 34, 14, 47, 16, 31, 50, 13, 28, 26, 32, 22,  3, 40,
       21, 24, 36,  7, 12,  6, 20,  5, 35, 48, 49, 37,  1,  2, 33, 10, 52,
       54, 42, 53, 45, 46, 27, 44, 25, 51, 11,  4, 38, 23, 29, 15, 19,  8,
       43, 18, 30,  0], dtype=int64), 'cur_cost': 106856.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([34, 39, 17, 41,  9, 14, 47, 16, 31, 50, 13, 28, 26, 32, 22,  3, 40,
       21, 24, 36,  7, 12,  6, 20,  5, 35, 48, 49, 37,  1,  2, 33, 10, 52,
       54, 42, 53, 45, 46, 27, 44, 25, 51, 11,  4, 38, 23, 29, 15, 19,  8,
       43, 18, 30,  0], dtype=int64), 'cur_cost': 103949.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 9, 39, 17, 41, 34, 14, 47, 16, 31, 50, 13, 28, 26, 32, 22,  3, 40,
       21, 24, 36,  7, 12,  6, 20,  5, 35, 48, 49, 37,  1,  2, 33, 10, 52,
       54, 42, 53, 45, 46, 27, 44, 25, 51, 11,  4, 38, 23, 29, 15, 19,  8,
       43, 18, 30,  0], dtype=int64), 'cur_cost': 102460.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 9, 34, 39, 17, 41, 14, 47, 16, 31, 50, 13, 28, 26, 32, 22,  3, 40,
       21, 24, 36,  7, 12,  6, 20,  5, 35, 48, 49, 37,  1,  2, 33, 10, 52,
       54, 42, 53, 45, 46, 27, 44, 25, 51, 11,  4, 38, 23, 29, 15, 19,  8,
       43, 18, 30,  0], dtype=int64), 'cur_cost': 102432.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:02,028 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 111618.00)
2025-08-05 10:29:02,028 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:29:02,028 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:02,028 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:02,029 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 103555.0
2025-08-05 10:29:02,037 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:29:02,038 - ExploitationExpert - INFO - res_population_costs: [10468.0, 10448, 10445, 10442]
2025-08-05 10:29:02,038 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  4,  6,  5,  9,  7,  3,  2,  8, 10, 43, 42, 37, 39, 34, 40,
       41, 38, 36, 33, 35, 29, 31, 26, 25, 23, 22, 27, 32, 28, 24, 30, 49,
       46, 44, 53, 54, 50, 48, 52, 47, 51, 45, 13, 17, 14, 11, 16, 15, 20,
       18, 12, 21, 19], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 30, 24, 26, 31, 25, 23, 28, 32, 27, 22, 29,
       35, 33, 36, 38, 41, 40, 34, 39, 37, 42, 43, 10,  8,  2,  3,  7,  9,
        5,  6,  4,  1], dtype=int64), array([ 0, 21, 19, 18, 12, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  9,  7,  2,  3,
        5,  6,  4,  1], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64)]
2025-08-05 10:29:02,040 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:02,040 - ExploitationExpert - INFO - populations: [{'tour': [19, 21, 12, 14, 24, 23, 43, 35, 28, 30, 37, 10, 36, 31, 6, 9, 29, 22, 41, 38, 25, 33, 27, 26, 3, 18, 17, 20, 50, 44, 49, 46, 16, 48, 15, 51, 45, 13, 47, 34, 42, 4, 1, 2, 5, 7, 0, 32, 40, 39, 53, 52, 54, 11, 8], 'cur_cost': 50129.0}, {'tour': [0, 8, 22, 9, 7, 3, 5, 6, 4, 1, 10, 2, 26, 31, 25, 23, 29, 27, 32, 28, 24, 30, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 13709.0}, {'tour': [0, 8, 12, 7, 3, 5, 6, 4, 1, 10, 23, 22, 41, 28, 25, 30, 35, 26, 18, 17, 43, 44, 34, 46, 16, 48, 15, 51, 45, 53, 42, 40, 9, 38, 24, 11, 37, 49, 2, 14, 54, 19, 13, 47, 29, 21, 27, 50, 32, 33, 36, 39, 20, 52, 31], 'cur_cost': 79157.0}, {'tour': array([38, 47, 12, 27, 46, 54,  5, 30, 16,  9, 13,  3, 52,  2, 33, 32, 22,
       45, 17,  7, 44, 51, 25, 50,  8, 11, 29, 34, 23, 26, 53, 31, 15, 28,
       43, 35, 19, 48, 36, 42,  4,  0, 49,  6, 21, 20, 40, 18, 37, 41,  1,
       39, 10, 24, 14], dtype=int64), 'cur_cost': 111618.0}, {'tour': array([30, 28, 23, 29, 21, 50, 19, 44, 41, 17, 38, 10, 51,  1, 16, 52, 35,
       45, 14, 49, 40, 27, 37,  8, 32,  0, 22,  9, 53, 18, 48,  6, 13, 46,
        5, 31, 26,  4, 33, 11, 15,  7, 43, 42, 24, 20, 34, 36,  3, 39, 25,
       12, 54,  2, 47], dtype=int64), 'cur_cost': 103555.0}, {'tour': array([ 2, 23, 11, 12,  0, 22,  6, 42, 40, 29, 27, 38,  7, 13,  3,  9, 25,
       31, 30, 10, 39,  5, 24, 45, 20, 43, 28, 16, 46, 34,  8, 50, 14, 52,
       21, 49, 35, 37,  1,  4, 32, 51, 41, 19, 17, 44, 54, 33, 47, 18, 48,
       15, 53, 36, 26], dtype=int64), 'cur_cost': 87861.0}, {'tour': array([23,  9, 41, 14,  5, 51, 31, 28, 11, 16, 24, 47, 52, 42, 27,  1, 44,
       48,  0, 12, 37, 32, 19,  4, 15, 17,  7, 45, 26, 10, 22,  2, 25, 18,
       43, 35, 30, 54, 50, 46, 40, 53, 29, 36, 34, 38, 33,  8,  3, 20, 39,
       21, 49, 13,  6], dtype=int64), 'cur_cost': 100809.0}, {'tour': array([ 2, 32, 28, 41, 14, 50,  0,  3, 22, 33, 47,  6, 35, 10, 13, 52,  8,
       43, 19, 42,  4, 27, 25, 24, 49,  1, 21, 46, 31, 48, 17, 20, 36, 30,
       23, 53, 18, 38,  5, 26, 29,  7, 11, 12, 37, 44, 45, 40, 16, 51, 54,
       15,  9, 39, 34], dtype=int64), 'cur_cost': 100562.0}, {'tour': array([48, 27,  5, 32, 36, 28,  4, 30, 12, 43, 45, 47, 23, 11, 49, 54, 44,
       31,  3, 41, 38, 16, 20, 24,  6, 35, 13,  2, 40, 15, 33, 25, 39, 52,
       21, 10,  8, 42, 51, 14, 37,  7,  1,  9, 29, 17, 53, 19, 22, 26, 34,
        0, 50, 18, 46], dtype=int64), 'cur_cost': 95548.0}, {'tour': array([ 1, 42,  0,  8,  6, 33, 11, 25, 21, 53, 23, 32, 54, 41, 40, 16, 17,
       37, 13, 46, 26, 51, 50, 29, 38, 47, 35, 52, 20, 18, 39, 43, 15, 12,
       10, 48,  5, 49,  4, 19,  9, 24, 30,  2,  3, 28, 31, 45, 14, 34, 44,
       27, 22,  7, 36], dtype=int64), 'cur_cost': 102208.0}]
2025-08-05 10:29:02,044 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:02,044 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 275, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 275, 'cache_hits': 0, 'similarity_calculations': 1357, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:02,047 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([30, 28, 23, 29, 21, 50, 19, 44, 41, 17, 38, 10, 51,  1, 16, 52, 35,
       45, 14, 49, 40, 27, 37,  8, 32,  0, 22,  9, 53, 18, 48,  6, 13, 46,
        5, 31, 26,  4, 33, 11, 15,  7, 43, 42, 24, 20, 34, 36,  3, 39, 25,
       12, 54,  2, 47], dtype=int64), 'cur_cost': 103555.0, 'intermediate_solutions': [{'tour': array([26, 32, 51, 45, 16, 17, 47, 34,  7, 30, 20, 46, 37, 53, 44,  5, 42,
       40, 15, 27, 12, 52,  3, 33, 22,  4, 50, 29, 28,  0, 10, 18, 41, 19,
       36,  8,  1,  6, 23, 13, 38, 14, 49, 43, 21,  2, 48, 31, 39, 11, 25,
       35,  9, 54, 24], dtype=int64), 'cur_cost': 108108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([45, 26, 32, 51, 16, 17, 47, 34,  7, 30, 20, 46, 37, 53, 44,  5, 42,
       40, 15, 27, 12, 52,  3, 33, 22,  4, 50, 29, 28,  0, 10, 18, 41, 19,
       36,  8,  1,  6, 23, 13, 38, 14, 49, 43, 21,  2, 48, 31, 39, 11, 25,
       35,  9, 54, 24], dtype=int64), 'cur_cost': 112147.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([16, 45, 26, 32, 51, 17, 47, 34,  7, 30, 20, 46, 37, 53, 44,  5, 42,
       40, 15, 27, 12, 52,  3, 33, 22,  4, 50, 29, 28,  0, 10, 18, 41, 19,
       36,  8,  1,  6, 23, 13, 38, 14, 49, 43, 21,  2, 48, 31, 39, 11, 25,
       35,  9, 54, 24], dtype=int64), 'cur_cost': 113650.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([51, 45, 26, 32, 16, 17, 47, 34,  7, 30, 20, 46, 37, 53, 44,  5, 42,
       40, 15, 27, 12, 52,  3, 33, 22,  4, 50, 29, 28,  0, 10, 18, 41, 19,
       36,  8,  1,  6, 23, 13, 38, 14, 49, 43, 21,  2, 48, 31, 39, 11, 25,
       35,  9, 54, 24], dtype=int64), 'cur_cost': 111472.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([51, 16, 45, 26, 32, 17, 47, 34,  7, 30, 20, 46, 37, 53, 44,  5, 42,
       40, 15, 27, 12, 52,  3, 33, 22,  4, 50, 29, 28,  0, 10, 18, 41, 19,
       36,  8,  1,  6, 23, 13, 38, 14, 49, 43, 21,  2, 48, 31, 39, 11, 25,
       35,  9, 54, 24], dtype=int64), 'cur_cost': 113673.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:02,047 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 103555.00)
2025-08-05 10:29:02,047 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:29:02,047 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:29:02,048 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:02,051 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 55
2025-08-05 10:29:02,051 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,052 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101458.0, 路径长度: 55, 收集中间解: 0
2025-08-05 10:29:02,052 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [21, 7, 3, 43, 35, 4, 1, 37, 2, 36, 28, 6, 30, 27, 41, 17, 25, 44, 34, 46, 16, 18, 42, 33, 45, 39, 49, 9, 48, 15, 51, 13, 52, 47, 5, 0, 40, 53, 12, 50, 24, 32, 11, 10, 22, 26, 38, 19, 8, 20, 29, 54, 23, 14, 31], 'cur_cost': 101458.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:02,052 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 101458.00)
2025-08-05 10:29:02,052 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:02,052 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:02,052 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:02,055 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 10:29:02,055 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,055 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16540.0, 路径长度: 55, 收集中间解: 0
2025-08-05 10:29:02,056 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 15, 5, 10, 8, 7, 9, 3, 2, 1, 4, 6, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 16540.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:02,057 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 16540.00)
2025-08-05 10:29:02,057 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:02,057 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:02,057 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:02,060 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 10:29:02,060 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,061 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10768.0, 路径长度: 55, 收集中间解: 0
2025-08-05 10:29:02,061 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 5, 9, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 3, 2, 1, 4, 6], 'cur_cost': 10768.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:02,061 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 10768.00)
2025-08-05 10:29:02,061 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:29:02,061 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:29:02,061 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:02,063 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 10:29:02,063 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,064 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15660.0, 路径长度: 55, 收集中间解: 0
2025-08-05 10:29:02,064 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 13, 23, 15, 16, 18, 20, 12, 21, 19, 17, 14, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 15660.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:02,064 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 15660.00)
2025-08-05 10:29:02,064 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:29:02,064 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:29:02,064 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:02,069 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 55
2025-08-05 10:29:02,070 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,070 - ExplorationExpert - INFO - 探索路径生成完成，成本: 48098.0, 路径长度: 55, 收集中间解: 0
2025-08-05 10:29:02,070 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [24, 30, 31, 40, 0, 8, 28, 23, 26, 1, 2, 37, 34, 36, 32, 6, 33, 46, 16, 20, 15, 17, 14, 11, 53, 12, 49, 47, 51, 50, 52, 44, 21, 54, 48, 38, 35, 42, 25, 10, 4, 5, 9, 27, 39, 29, 41, 7, 22, 43, 3, 18, 19, 13, 45], 'cur_cost': 48098.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:02,071 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 48098.00)
2025-08-05 10:29:02,071 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:02,071 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:02,073 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [19, 21, 12, 14, 24, 23, 43, 35, 28, 30, 37, 10, 36, 31, 6, 9, 29, 22, 41, 38, 25, 33, 27, 26, 3, 18, 17, 20, 50, 44, 49, 46, 16, 48, 15, 51, 45, 13, 47, 34, 42, 4, 1, 2, 5, 7, 0, 32, 40, 39, 53, 52, 54, 11, 8], 'cur_cost': 50129.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 22, 9, 7, 3, 5, 6, 4, 1, 10, 2, 26, 31, 25, 23, 29, 27, 32, 28, 24, 30, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 13709.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 12, 7, 3, 5, 6, 4, 1, 10, 23, 22, 41, 28, 25, 30, 35, 26, 18, 17, 43, 44, 34, 46, 16, 48, 15, 51, 45, 53, 42, 40, 9, 38, 24, 11, 37, 49, 2, 14, 54, 19, 13, 47, 29, 21, 27, 50, 32, 33, 36, 39, 20, 52, 31], 'cur_cost': 79157.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([38, 47, 12, 27, 46, 54,  5, 30, 16,  9, 13,  3, 52,  2, 33, 32, 22,
       45, 17,  7, 44, 51, 25, 50,  8, 11, 29, 34, 23, 26, 53, 31, 15, 28,
       43, 35, 19, 48, 36, 42,  4,  0, 49,  6, 21, 20, 40, 18, 37, 41,  1,
       39, 10, 24, 14], dtype=int64), 'cur_cost': 111618.0, 'intermediate_solutions': [{'tour': array([17, 41,  9, 39, 34, 14, 47, 16, 31, 50, 13, 28, 26, 32, 22,  3, 40,
       21, 24, 36,  7, 12,  6, 20,  5, 35, 48, 49, 37,  1,  2, 33, 10, 52,
       54, 42, 53, 45, 46, 27, 44, 25, 51, 11,  4, 38, 23, 29, 15, 19,  8,
       43, 18, 30,  0], dtype=int64), 'cur_cost': 103968.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([39, 17, 41,  9, 34, 14, 47, 16, 31, 50, 13, 28, 26, 32, 22,  3, 40,
       21, 24, 36,  7, 12,  6, 20,  5, 35, 48, 49, 37,  1,  2, 33, 10, 52,
       54, 42, 53, 45, 46, 27, 44, 25, 51, 11,  4, 38, 23, 29, 15, 19,  8,
       43, 18, 30,  0], dtype=int64), 'cur_cost': 106856.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([34, 39, 17, 41,  9, 14, 47, 16, 31, 50, 13, 28, 26, 32, 22,  3, 40,
       21, 24, 36,  7, 12,  6, 20,  5, 35, 48, 49, 37,  1,  2, 33, 10, 52,
       54, 42, 53, 45, 46, 27, 44, 25, 51, 11,  4, 38, 23, 29, 15, 19,  8,
       43, 18, 30,  0], dtype=int64), 'cur_cost': 103949.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 9, 39, 17, 41, 34, 14, 47, 16, 31, 50, 13, 28, 26, 32, 22,  3, 40,
       21, 24, 36,  7, 12,  6, 20,  5, 35, 48, 49, 37,  1,  2, 33, 10, 52,
       54, 42, 53, 45, 46, 27, 44, 25, 51, 11,  4, 38, 23, 29, 15, 19,  8,
       43, 18, 30,  0], dtype=int64), 'cur_cost': 102460.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 9, 34, 39, 17, 41, 14, 47, 16, 31, 50, 13, 28, 26, 32, 22,  3, 40,
       21, 24, 36,  7, 12,  6, 20,  5, 35, 48, 49, 37,  1,  2, 33, 10, 52,
       54, 42, 53, 45, 46, 27, 44, 25, 51, 11,  4, 38, 23, 29, 15, 19,  8,
       43, 18, 30,  0], dtype=int64), 'cur_cost': 102432.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([30, 28, 23, 29, 21, 50, 19, 44, 41, 17, 38, 10, 51,  1, 16, 52, 35,
       45, 14, 49, 40, 27, 37,  8, 32,  0, 22,  9, 53, 18, 48,  6, 13, 46,
        5, 31, 26,  4, 33, 11, 15,  7, 43, 42, 24, 20, 34, 36,  3, 39, 25,
       12, 54,  2, 47], dtype=int64), 'cur_cost': 103555.0, 'intermediate_solutions': [{'tour': array([26, 32, 51, 45, 16, 17, 47, 34,  7, 30, 20, 46, 37, 53, 44,  5, 42,
       40, 15, 27, 12, 52,  3, 33, 22,  4, 50, 29, 28,  0, 10, 18, 41, 19,
       36,  8,  1,  6, 23, 13, 38, 14, 49, 43, 21,  2, 48, 31, 39, 11, 25,
       35,  9, 54, 24], dtype=int64), 'cur_cost': 108108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([45, 26, 32, 51, 16, 17, 47, 34,  7, 30, 20, 46, 37, 53, 44,  5, 42,
       40, 15, 27, 12, 52,  3, 33, 22,  4, 50, 29, 28,  0, 10, 18, 41, 19,
       36,  8,  1,  6, 23, 13, 38, 14, 49, 43, 21,  2, 48, 31, 39, 11, 25,
       35,  9, 54, 24], dtype=int64), 'cur_cost': 112147.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([16, 45, 26, 32, 51, 17, 47, 34,  7, 30, 20, 46, 37, 53, 44,  5, 42,
       40, 15, 27, 12, 52,  3, 33, 22,  4, 50, 29, 28,  0, 10, 18, 41, 19,
       36,  8,  1,  6, 23, 13, 38, 14, 49, 43, 21,  2, 48, 31, 39, 11, 25,
       35,  9, 54, 24], dtype=int64), 'cur_cost': 113650.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([51, 45, 26, 32, 16, 17, 47, 34,  7, 30, 20, 46, 37, 53, 44,  5, 42,
       40, 15, 27, 12, 52,  3, 33, 22,  4, 50, 29, 28,  0, 10, 18, 41, 19,
       36,  8,  1,  6, 23, 13, 38, 14, 49, 43, 21,  2, 48, 31, 39, 11, 25,
       35,  9, 54, 24], dtype=int64), 'cur_cost': 111472.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([51, 16, 45, 26, 32, 17, 47, 34,  7, 30, 20, 46, 37, 53, 44,  5, 42,
       40, 15, 27, 12, 52,  3, 33, 22,  4, 50, 29, 28,  0, 10, 18, 41, 19,
       36,  8,  1,  6, 23, 13, 38, 14, 49, 43, 21,  2, 48, 31, 39, 11, 25,
       35,  9, 54, 24], dtype=int64), 'cur_cost': 113673.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [21, 7, 3, 43, 35, 4, 1, 37, 2, 36, 28, 6, 30, 27, 41, 17, 25, 44, 34, 46, 16, 18, 42, 33, 45, 39, 49, 9, 48, 15, 51, 13, 52, 47, 5, 0, 40, 53, 12, 50, 24, 32, 11, 10, 22, 26, 38, 19, 8, 20, 29, 54, 23, 14, 31], 'cur_cost': 101458.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 5, 10, 8, 7, 9, 3, 2, 1, 4, 6, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 16540.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 9, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 3, 2, 1, 4, 6], 'cur_cost': 10768.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 23, 15, 16, 18, 20, 12, 21, 19, 17, 14, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 15660.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [24, 30, 31, 40, 0, 8, 28, 23, 26, 1, 2, 37, 34, 36, 32, 6, 33, 46, 16, 20, 15, 17, 14, 11, 53, 12, 49, 47, 51, 50, 52, 44, 21, 54, 48, 38, 35, 42, 25, 10, 4, 5, 9, 27, 39, 29, 41, 7, 22, 43, 3, 18, 19, 13, 45], 'cur_cost': 48098.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 10:29:02,073 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:02,073 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:02,077 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10768.000, 多样性=0.971
2025-08-05 10:29:02,077 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 10:29:02,077 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 10:29:02,077 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:02,077 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.08313510282330731, 'best_improvement': -0.005321631967136589}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.012330456226880135}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.048462135774710834, 'recent_improvements': [-0.0997461098060808, 0.09680525143500417, -0.002821838256659138], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 10442, 'new_best_cost': 10442, 'quality_improvement': 0.0, 'old_diversity': 0.9060606060606061, 'new_diversity': 0.9060606060606061, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 10:29:02,078 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 10:29:02,078 - __main__ - INFO - composite10_55 开始进化第 2 代
2025-08-05 10:29:02,078 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 10:29:02,078 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:02,079 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10768.000, 多样性=0.971
2025-08-05 10:29:02,079 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:02,081 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.971
2025-08-05 10:29:02,081 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:02,083 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.906
2025-08-05 10:29:02,085 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 10:29:02,086 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:02,086 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 10:29:02,086 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 10:29:02,115 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.143, 适应度梯度: 1041.957, 聚类评分: 0.000, 覆盖率: 0.125, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:02,115 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 10:29:02,115 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:02,115 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite10_55
2025-08-05 10:29:02,122 - visualization.landscape_visualizer - INFO - 插值约束: 138 个点被约束到最小值 10442.00
2025-08-05 10:29:02,124 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=3.8%, 梯度: 3589.12 → 3452.77
2025-08-05 10:29:02,332 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite10_55\landscape_composite10_55_iter_107_20250805_102902.html
2025-08-05 10:29:02,387 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite10_55\dashboard_composite10_55_iter_107_20250805_102902.html
2025-08-05 10:29:02,388 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 107
2025-08-05 10:29:02,389 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 10:29:02,389 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.3038秒
2025-08-05 10:29:02,390 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.14285714285714285, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 1041.9571428571435, 'local_optima_density': 0.14285714285714285, 'gradient_variance': 1297057693.3724492, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1253, 'fitness_entropy': 0.7783853970487746, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.125)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 1041.957)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360942.1153297, 'performance_metrics': {}}}
2025-08-05 10:29:02,390 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:02,390 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:02,390 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:02,390 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:02,391 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:29:02,392 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 10:29:02,392 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:29:02,392 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:02,392 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:02,392 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:29:02,392 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:02,393 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:02,393 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:02,393 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:02,393 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:02,393 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:02,395 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 10:29:02,396 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,396 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,396 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,396 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,397 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16545.0, 路径长度: 55, 收集中间解: 3
2025-08-05 10:29:02,397 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 15, 10, 9, 7, 3, 5, 6, 4, 1, 8, 2, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 16545.0, 'intermediate_solutions': [{'tour': [19, 21, 12, 5, 24, 23, 43, 35, 28, 30, 37, 10, 36, 31, 6, 9, 29, 22, 41, 38, 25, 33, 27, 26, 3, 18, 17, 20, 50, 44, 49, 46, 16, 48, 15, 51, 45, 13, 47, 34, 42, 4, 1, 2, 14, 7, 0, 32, 40, 39, 53, 52, 54, 11, 8], 'cur_cost': 57699.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [19, 21, 12, 14, 24, 23, 43, 35, 28, 30, 37, 34, 47, 13, 45, 51, 15, 48, 16, 46, 49, 44, 50, 20, 17, 18, 3, 26, 27, 33, 25, 38, 41, 22, 29, 9, 6, 31, 36, 10, 42, 4, 1, 2, 5, 7, 0, 32, 40, 39, 53, 52, 54, 11, 8], 'cur_cost': 50109.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [19, 21, 12, 14, 24, 23, 43, 35, 28, 30, 37, 10, 36, 31, 6, 9, 29, 22, 41, 38, 25, 33, 27, 26, 3, 18, 17, 20, 50, 44, 49, 46, 16, 48, 15, 51, 45, 13, 47, 34, 42, 4, 1, 2, 5, 7, 0, 32, 40, 39, 53, 52, 54, 11, 8], 'cur_cost': 50129.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:02,397 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 16545.00)
2025-08-05 10:29:02,397 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:02,397 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:02,398 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:02,400 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 55
2025-08-05 10:29:02,400 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,400 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,401 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,401 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,401 - ExplorationExpert - INFO - 探索路径生成完成，成本: 82793.0, 路径长度: 55, 收集中间解: 3
2025-08-05 10:29:02,402 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [38, 28, 22, 27, 21, 5, 6, 4, 1, 10, 23, 3, 52, 2, 16, 30, 29, 44, 18, 17, 24, 51, 35, 36, 8, 37, 42, 33, 43, 53, 34, 31, 9, 49, 46, 11, 54, 48, 47, 14, 45, 19, 50, 39, 13, 26, 40, 7, 15, 41, 25, 12, 20, 0, 32], 'cur_cost': 82793.0, 'intermediate_solutions': [{'tour': [0, 8, 22, 9, 7, 3, 5, 6, 4, 1, 10, 2, 26, 31, 25, 23, 29, 27, 32, 28, 24, 30, 35, 36, 15, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 38, 20, 18, 12, 21, 19, 11], 'cur_cost': 27894.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 22, 9, 7, 3, 5, 6, 4, 1, 10, 2, 26, 31, 25, 38, 36, 35, 30, 24, 28, 32, 27, 29, 23, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 15980.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 8, 22, 9, 7, 3, 5, 6, 4, 1, 10, 2, 26, 31, 25, 23, 29, 27, 32, 28, 24, 30, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 21, 19, 11], 'cur_cost': 19376.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:02,402 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 82793.00)
2025-08-05 10:29:02,402 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:02,402 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:02,402 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:02,404 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 55
2025-08-05 10:29:02,405 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,405 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,405 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,406 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,406 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101088.0, 路径长度: 55, 收集中间解: 3
2025-08-05 10:29:02,406 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [30, 8, 22, 9, 7, 3, 1, 44, 16, 17, 38, 2, 41, 28, 33, 32, 29, 26, 14, 46, 24, 18, 35, 36, 45, 37, 42, 51, 43, 15, 53, 40, 13, 49, 27, 31, 54, 4, 47, 11, 21, 0, 50, 6, 34, 10, 52, 19, 20, 39, 25, 12, 23, 48, 5], 'cur_cost': 101088.0, 'intermediate_solutions': [{'tour': [0, 8, 12, 7, 3, 5, 6, 4, 1, 10, 23, 22, 41, 28, 25, 30, 35, 26, 18, 51, 43, 44, 34, 46, 16, 48, 15, 17, 45, 53, 42, 40, 9, 38, 24, 11, 37, 49, 2, 14, 54, 19, 13, 47, 29, 21, 27, 50, 32, 33, 36, 39, 20, 52, 31], 'cur_cost': 79611.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 12, 7, 3, 5, 6, 4, 1, 10, 2, 49, 37, 11, 24, 38, 9, 40, 42, 53, 45, 51, 15, 48, 16, 46, 34, 44, 43, 17, 18, 26, 35, 30, 25, 28, 41, 22, 23, 14, 54, 19, 13, 47, 29, 21, 27, 50, 32, 33, 36, 39, 20, 52, 31], 'cur_cost': 77385.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 12, 7, 3, 5, 6, 4, 1, 10, 23, 22, 41, 28, 25, 30, 35, 26, 18, 17, 43, 44, 34, 46, 16, 48, 15, 51, 45, 53, 42, 40, 9, 24, 11, 38, 37, 49, 2, 14, 54, 19, 13, 47, 29, 21, 27, 50, 32, 33, 36, 39, 20, 52, 31], 'cur_cost': 77137.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:02,407 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 101088.00)
2025-08-05 10:29:02,407 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:29:02,407 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:02,408 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:02,408 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 115543.0
2025-08-05 10:29:02,416 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:29:02,416 - ExploitationExpert - INFO - res_population_costs: [10442, 10445, 10448, 10468.0]
2025-08-05 10:29:02,416 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 21, 19, 18, 12, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  9,  7,  2,  3,
        5,  6,  4,  1], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 30, 24, 26, 31, 25, 23, 28, 32, 27, 22, 29,
       35, 33, 36, 38, 41, 40, 34, 39, 37, 42, 43, 10,  8,  2,  3,  7,  9,
        5,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  9,  7,  3,  2,  8, 10, 43, 42, 37, 39, 34, 40,
       41, 38, 36, 33, 35, 29, 31, 26, 25, 23, 22, 27, 32, 28, 24, 30, 49,
       46, 44, 53, 54, 50, 48, 52, 47, 51, 45, 13, 17, 14, 11, 16, 15, 20,
       18, 12, 21, 19], dtype=int64)]
2025-08-05 10:29:02,417 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:02,418 - ExploitationExpert - INFO - populations: [{'tour': [0, 15, 10, 9, 7, 3, 5, 6, 4, 1, 8, 2, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 16545.0}, {'tour': [38, 28, 22, 27, 21, 5, 6, 4, 1, 10, 23, 3, 52, 2, 16, 30, 29, 44, 18, 17, 24, 51, 35, 36, 8, 37, 42, 33, 43, 53, 34, 31, 9, 49, 46, 11, 54, 48, 47, 14, 45, 19, 50, 39, 13, 26, 40, 7, 15, 41, 25, 12, 20, 0, 32], 'cur_cost': 82793.0}, {'tour': [30, 8, 22, 9, 7, 3, 1, 44, 16, 17, 38, 2, 41, 28, 33, 32, 29, 26, 14, 46, 24, 18, 35, 36, 45, 37, 42, 51, 43, 15, 53, 40, 13, 49, 27, 31, 54, 4, 47, 11, 21, 0, 50, 6, 34, 10, 52, 19, 20, 39, 25, 12, 23, 48, 5], 'cur_cost': 101088.0}, {'tour': array([27, 54, 26, 15, 29, 13, 10, 48, 11, 17,  3, 46, 39, 45, 38, 49, 25,
       20, 33, 14,  1, 42, 44, 47,  6, 34, 24, 50, 35,  4, 31,  7, 37,  8,
        0,  2, 32, 18, 52, 28, 16, 40, 22, 51, 30,  9, 12, 19,  5, 21, 43,
       53, 36, 41, 23], dtype=int64), 'cur_cost': 115543.0}, {'tour': [30, 28, 23, 29, 21, 50, 19, 44, 41, 17, 38, 10, 51, 1, 16, 52, 35, 45, 14, 49, 40, 27, 37, 8, 32, 0, 22, 9, 53, 18, 48, 6, 13, 46, 5, 31, 26, 4, 33, 11, 15, 7, 43, 42, 24, 20, 34, 36, 3, 39, 25, 12, 54, 2, 47], 'cur_cost': 103555.0}, {'tour': [21, 7, 3, 43, 35, 4, 1, 37, 2, 36, 28, 6, 30, 27, 41, 17, 25, 44, 34, 46, 16, 18, 42, 33, 45, 39, 49, 9, 48, 15, 51, 13, 52, 47, 5, 0, 40, 53, 12, 50, 24, 32, 11, 10, 22, 26, 38, 19, 8, 20, 29, 54, 23, 14, 31], 'cur_cost': 101458.0}, {'tour': [0, 15, 5, 10, 8, 7, 9, 3, 2, 1, 4, 6, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 16540.0}, {'tour': [0, 5, 9, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 3, 2, 1, 4, 6], 'cur_cost': 10768.0}, {'tour': [0, 13, 23, 15, 16, 18, 20, 12, 21, 19, 17, 14, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 15660.0}, {'tour': [24, 30, 31, 40, 0, 8, 28, 23, 26, 1, 2, 37, 34, 36, 32, 6, 33, 46, 16, 20, 15, 17, 14, 11, 53, 12, 49, 47, 51, 50, 52, 44, 21, 54, 48, 38, 35, 42, 25, 10, 4, 5, 9, 27, 39, 29, 41, 7, 22, 43, 3, 18, 19, 13, 45], 'cur_cost': 48098.0}]
2025-08-05 10:29:02,418 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:02,418 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 276, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 276, 'cache_hits': 0, 'similarity_calculations': 1359, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:02,419 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([27, 54, 26, 15, 29, 13, 10, 48, 11, 17,  3, 46, 39, 45, 38, 49, 25,
       20, 33, 14,  1, 42, 44, 47,  6, 34, 24, 50, 35,  4, 31,  7, 37,  8,
        0,  2, 32, 18, 52, 28, 16, 40, 22, 51, 30,  9, 12, 19,  5, 21, 43,
       53, 36, 41, 23], dtype=int64), 'cur_cost': 115543.0, 'intermediate_solutions': [{'tour': array([12, 47, 38, 27, 46, 54,  5, 30, 16,  9, 13,  3, 52,  2, 33, 32, 22,
       45, 17,  7, 44, 51, 25, 50,  8, 11, 29, 34, 23, 26, 53, 31, 15, 28,
       43, 35, 19, 48, 36, 42,  4,  0, 49,  6, 21, 20, 40, 18, 37, 41,  1,
       39, 10, 24, 14]), 'cur_cost': 106777.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([27, 12, 47, 38, 46, 54,  5, 30, 16,  9, 13,  3, 52,  2, 33, 32, 22,
       45, 17,  7, 44, 51, 25, 50,  8, 11, 29, 34, 23, 26, 53, 31, 15, 28,
       43, 35, 19, 48, 36, 42,  4,  0, 49,  6, 21, 20, 40, 18, 37, 41,  1,
       39, 10, 24, 14]), 'cur_cost': 111365.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([46, 27, 12, 47, 38, 54,  5, 30, 16,  9, 13,  3, 52,  2, 33, 32, 22,
       45, 17,  7, 44, 51, 25, 50,  8, 11, 29, 34, 23, 26, 53, 31, 15, 28,
       43, 35, 19, 48, 36, 42,  4,  0, 49,  6, 21, 20, 40, 18, 37, 41,  1,
       39, 10, 24, 14]), 'cur_cost': 112055.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([38, 27, 12, 47, 46, 54,  5, 30, 16,  9, 13,  3, 52,  2, 33, 32, 22,
       45, 17,  7, 44, 51, 25, 50,  8, 11, 29, 34, 23, 26, 53, 31, 15, 28,
       43, 35, 19, 48, 36, 42,  4,  0, 49,  6, 21, 20, 40, 18, 37, 41,  1,
       39, 10, 24, 14]), 'cur_cost': 107991.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([38, 46, 27, 12, 47, 54,  5, 30, 16,  9, 13,  3, 52,  2, 33, 32, 22,
       45, 17,  7, 44, 51, 25, 50,  8, 11, 29, 34, 23, 26, 53, 31, 15, 28,
       43, 35, 19, 48, 36, 42,  4,  0, 49,  6, 21, 20, 40, 18, 37, 41,  1,
       39, 10, 24, 14]), 'cur_cost': 111554.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:02,419 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 115543.00)
2025-08-05 10:29:02,419 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:29:02,420 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:02,420 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:02,420 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 102291.0
2025-08-05 10:29:02,427 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:29:02,427 - ExploitationExpert - INFO - res_population_costs: [10442, 10445, 10448, 10468.0]
2025-08-05 10:29:02,427 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 21, 19, 18, 12, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  9,  7,  2,  3,
        5,  6,  4,  1], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 30, 24, 26, 31, 25, 23, 28, 32, 27, 22, 29,
       35, 33, 36, 38, 41, 40, 34, 39, 37, 42, 43, 10,  8,  2,  3,  7,  9,
        5,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  9,  7,  3,  2,  8, 10, 43, 42, 37, 39, 34, 40,
       41, 38, 36, 33, 35, 29, 31, 26, 25, 23, 22, 27, 32, 28, 24, 30, 49,
       46, 44, 53, 54, 50, 48, 52, 47, 51, 45, 13, 17, 14, 11, 16, 15, 20,
       18, 12, 21, 19], dtype=int64)]
2025-08-05 10:29:02,429 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:02,429 - ExploitationExpert - INFO - populations: [{'tour': [0, 15, 10, 9, 7, 3, 5, 6, 4, 1, 8, 2, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 16545.0}, {'tour': [38, 28, 22, 27, 21, 5, 6, 4, 1, 10, 23, 3, 52, 2, 16, 30, 29, 44, 18, 17, 24, 51, 35, 36, 8, 37, 42, 33, 43, 53, 34, 31, 9, 49, 46, 11, 54, 48, 47, 14, 45, 19, 50, 39, 13, 26, 40, 7, 15, 41, 25, 12, 20, 0, 32], 'cur_cost': 82793.0}, {'tour': [30, 8, 22, 9, 7, 3, 1, 44, 16, 17, 38, 2, 41, 28, 33, 32, 29, 26, 14, 46, 24, 18, 35, 36, 45, 37, 42, 51, 43, 15, 53, 40, 13, 49, 27, 31, 54, 4, 47, 11, 21, 0, 50, 6, 34, 10, 52, 19, 20, 39, 25, 12, 23, 48, 5], 'cur_cost': 101088.0}, {'tour': array([27, 54, 26, 15, 29, 13, 10, 48, 11, 17,  3, 46, 39, 45, 38, 49, 25,
       20, 33, 14,  1, 42, 44, 47,  6, 34, 24, 50, 35,  4, 31,  7, 37,  8,
        0,  2, 32, 18, 52, 28, 16, 40, 22, 51, 30,  9, 12, 19,  5, 21, 43,
       53, 36, 41, 23], dtype=int64), 'cur_cost': 115543.0}, {'tour': array([ 6, 35,  0, 52, 13,  5, 41, 31, 39, 34, 30, 21, 53, 44, 33, 43, 25,
       36, 24, 22, 16, 40, 11, 32, 26,  1, 17, 18, 48, 51,  3, 45, 46, 29,
       10, 54,  2, 50,  9, 14, 47, 28, 20,  8, 37, 49,  4, 23, 27,  7, 19,
       42, 38, 15, 12], dtype=int64), 'cur_cost': 102291.0}, {'tour': [21, 7, 3, 43, 35, 4, 1, 37, 2, 36, 28, 6, 30, 27, 41, 17, 25, 44, 34, 46, 16, 18, 42, 33, 45, 39, 49, 9, 48, 15, 51, 13, 52, 47, 5, 0, 40, 53, 12, 50, 24, 32, 11, 10, 22, 26, 38, 19, 8, 20, 29, 54, 23, 14, 31], 'cur_cost': 101458.0}, {'tour': [0, 15, 5, 10, 8, 7, 9, 3, 2, 1, 4, 6, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 16540.0}, {'tour': [0, 5, 9, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 3, 2, 1, 4, 6], 'cur_cost': 10768.0}, {'tour': [0, 13, 23, 15, 16, 18, 20, 12, 21, 19, 17, 14, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 15660.0}, {'tour': [24, 30, 31, 40, 0, 8, 28, 23, 26, 1, 2, 37, 34, 36, 32, 6, 33, 46, 16, 20, 15, 17, 14, 11, 53, 12, 49, 47, 51, 50, 52, 44, 21, 54, 48, 38, 35, 42, 25, 10, 4, 5, 9, 27, 39, 29, 41, 7, 22, 43, 3, 18, 19, 13, 45], 'cur_cost': 48098.0}]
2025-08-05 10:29:02,430 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:02,430 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 277, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 277, 'cache_hits': 0, 'similarity_calculations': 1362, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:02,431 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([ 6, 35,  0, 52, 13,  5, 41, 31, 39, 34, 30, 21, 53, 44, 33, 43, 25,
       36, 24, 22, 16, 40, 11, 32, 26,  1, 17, 18, 48, 51,  3, 45, 46, 29,
       10, 54,  2, 50,  9, 14, 47, 28, 20,  8, 37, 49,  4, 23, 27,  7, 19,
       42, 38, 15, 12], dtype=int64), 'cur_cost': 102291.0, 'intermediate_solutions': [{'tour': array([23, 28, 30, 29, 21, 50, 19, 44, 41, 17, 38, 10, 51,  1, 16, 52, 35,
       45, 14, 49, 40, 27, 37,  8, 32,  0, 22,  9, 53, 18, 48,  6, 13, 46,
        5, 31, 26,  4, 33, 11, 15,  7, 43, 42, 24, 20, 34, 36,  3, 39, 25,
       12, 54,  2, 47]), 'cur_cost': 103672.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([29, 23, 28, 30, 21, 50, 19, 44, 41, 17, 38, 10, 51,  1, 16, 52, 35,
       45, 14, 49, 40, 27, 37,  8, 32,  0, 22,  9, 53, 18, 48,  6, 13, 46,
        5, 31, 26,  4, 33, 11, 15,  7, 43, 42, 24, 20, 34, 36,  3, 39, 25,
       12, 54,  2, 47]), 'cur_cost': 103529.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([21, 29, 23, 28, 30, 50, 19, 44, 41, 17, 38, 10, 51,  1, 16, 52, 35,
       45, 14, 49, 40, 27, 37,  8, 32,  0, 22,  9, 53, 18, 48,  6, 13, 46,
        5, 31, 26,  4, 33, 11, 15,  7, 43, 42, 24, 20, 34, 36,  3, 39, 25,
       12, 54,  2, 47]), 'cur_cost': 103543.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([30, 29, 23, 28, 21, 50, 19, 44, 41, 17, 38, 10, 51,  1, 16, 52, 35,
       45, 14, 49, 40, 27, 37,  8, 32,  0, 22,  9, 53, 18, 48,  6, 13, 46,
        5, 31, 26,  4, 33, 11, 15,  7, 43, 42, 24, 20, 34, 36,  3, 39, 25,
       12, 54,  2, 47]), 'cur_cost': 103542.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([30, 21, 29, 23, 28, 50, 19, 44, 41, 17, 38, 10, 51,  1, 16, 52, 35,
       45, 14, 49, 40, 27, 37,  8, 32,  0, 22,  9, 53, 18, 48,  6, 13, 46,
        5, 31, 26,  4, 33, 11, 15,  7, 43, 42, 24, 20, 34, 36,  3, 39, 25,
       12, 54,  2, 47]), 'cur_cost': 106726.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:02,431 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 102291.00)
2025-08-05 10:29:02,431 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:29:02,431 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:29:02,431 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:02,432 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 10:29:02,433 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,433 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,433 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,433 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,434 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17099.0, 路径长度: 55, 收集中间解: 3
2025-08-05 10:29:02,434 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 16, 25, 23, 22, 32, 28, 24, 30, 26, 31, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 6, 4, 21, 19, 17, 13, 14, 11, 15, 20, 18, 12, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 17099.0, 'intermediate_solutions': [{'tour': [21, 7, 3, 44, 35, 4, 1, 37, 2, 36, 28, 6, 30, 27, 41, 17, 25, 43, 34, 46, 16, 18, 42, 33, 45, 39, 49, 9, 48, 15, 51, 13, 52, 47, 5, 0, 40, 53, 12, 50, 24, 32, 11, 10, 22, 26, 38, 19, 8, 20, 29, 54, 23, 14, 31], 'cur_cost': 101290.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [21, 7, 3, 43, 35, 4, 1, 37, 2, 36, 28, 6, 30, 27, 41, 17, 25, 44, 34, 46, 16, 18, 42, 33, 45, 39, 49, 9, 48, 15, 51, 13, 52, 47, 5, 0, 40, 53, 12, 50, 14, 23, 54, 29, 20, 8, 19, 38, 26, 22, 10, 11, 32, 24, 31], 'cur_cost': 98163.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [21, 7, 3, 43, 35, 4, 1, 37, 2, 36, 28, 6, 30, 27, 41, 17, 25, 44, 34, 46, 16, 18, 42, 33, 45, 39, 49, 9, 48, 15, 51, 13, 52, 47, 5, 0, 40, 53, 12, 50, 24, 11, 10, 22, 26, 38, 19, 8, 20, 29, 54, 23, 14, 31, 32], 'cur_cost': 101427.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:02,434 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 17099.00)
2025-08-05 10:29:02,434 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:02,434 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:02,434 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:02,436 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 10:29:02,436 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,436 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,436 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,437 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,437 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10727.0, 路径长度: 55, 收集中间解: 3
2025-08-05 10:29:02,437 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 2, 21, 18, 16, 15, 20, 12, 19, 17, 13, 14, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 6, 4, 1], 'cur_cost': 10727.0, 'intermediate_solutions': [{'tour': [0, 15, 5, 10, 8, 7, 9, 3, 2, 1, 4, 6, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 45, 40, 41, 49, 46, 44, 54, 53, 47, 51, 34, 52, 50, 48, 13, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 27998.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 5, 10, 8, 7, 9, 3, 2, 1, 4, 6, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 52, 45, 51, 47, 50, 48, 13, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 16573.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 5, 10, 8, 7, 9, 3, 2, 1, 4, 6, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 26, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 20528.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:02,437 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 10727.00)
2025-08-05 10:29:02,437 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:02,437 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:02,437 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:02,441 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 55
2025-08-05 10:29:02,442 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,442 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,442 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,442 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,442 - ExplorationExpert - INFO - 探索路径生成完成，成本: 55250.0, 路径长度: 55, 收集中间解: 3
2025-08-05 10:29:02,442 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [27, 23, 30, 29, 24, 9, 2, 36, 40, 8, 26, 38, 32, 34, 25, 1, 35, 49, 16, 21, 13, 11, 45, 53, 17, 51, 14, 18, 54, 12, 20, 6, 28, 22, 41, 39, 31, 33, 37, 10, 7, 3, 19, 50, 47, 48, 52, 44, 46, 42, 0, 5, 4, 43, 15], 'cur_cost': 55250.0, 'intermediate_solutions': [{'tour': [0, 5, 9, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 2, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 3, 28, 1, 4, 6], 'cur_cost': 16561.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 9, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 2, 3, 1, 4, 6], 'cur_cost': 10767.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 9, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 26, 24, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 3, 2, 1, 4, 6], 'cur_cost': 10827.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:02,443 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 55250.00)
2025-08-05 10:29:02,443 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:29:02,443 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:29:02,443 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:02,444 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 10:29:02,445 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,445 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,445 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,445 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,445 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16432.0, 路径长度: 55, 收集中间解: 3
2025-08-05 10:29:02,445 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 15, 6, 4, 1, 8, 7, 9, 5, 3, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 16432.0, 'intermediate_solutions': [{'tour': [0, 13, 23, 15, 16, 18, 20, 12, 21, 19, 17, 14, 11, 51, 45, 52, 4, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 50, 6], 'cur_cost': 27600.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 23, 15, 16, 18, 20, 12, 21, 19, 17, 14, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 2, 3, 5, 9, 1, 4, 6], 'cur_cost': 15642.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 23, 15, 16, 18, 20, 12, 21, 19, 17, 14, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 30, 24, 26, 31, 25, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 19240.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:02,446 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 16432.00)
2025-08-05 10:29:02,446 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:29:02,446 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:29:02,446 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:02,447 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 10:29:02,447 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,448 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,448 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,448 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,448 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10685.0, 路径长度: 55, 收集中间解: 3
2025-08-05 10:29:02,448 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 10685.0, 'intermediate_solutions': [{'tour': [24, 30, 31, 40, 0, 8, 28, 23, 26, 1, 2, 37, 34, 36, 32, 6, 33, 46, 16, 13, 15, 17, 14, 11, 53, 12, 49, 47, 51, 50, 52, 44, 21, 54, 48, 38, 35, 42, 25, 10, 4, 5, 9, 27, 39, 29, 41, 7, 22, 43, 3, 18, 19, 20, 45], 'cur_cost': 48260.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [24, 30, 31, 40, 0, 8, 28, 23, 26, 1, 2, 37, 34, 36, 32, 6, 33, 46, 16, 20, 15, 17, 14, 11, 53, 12, 49, 47, 51, 50, 52, 44, 21, 54, 48, 38, 35, 42, 25, 10, 13, 19, 18, 3, 43, 22, 7, 41, 29, 39, 27, 9, 5, 4, 45], 'cur_cost': 52843.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [24, 30, 31, 40, 0, 8, 28, 23, 26, 1, 2, 37, 34, 36, 32, 6, 33, 46, 16, 20, 15, 17, 14, 11, 53, 12, 49, 47, 51, 50, 52, 44, 21, 54, 48, 38, 41, 35, 42, 25, 10, 4, 5, 9, 27, 39, 29, 7, 22, 43, 3, 18, 19, 13, 45], 'cur_cost': 46199.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:02,449 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 10685.00)
2025-08-05 10:29:02,449 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:02,449 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:02,451 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 10, 9, 7, 3, 5, 6, 4, 1, 8, 2, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 16545.0, 'intermediate_solutions': [{'tour': [19, 21, 12, 5, 24, 23, 43, 35, 28, 30, 37, 10, 36, 31, 6, 9, 29, 22, 41, 38, 25, 33, 27, 26, 3, 18, 17, 20, 50, 44, 49, 46, 16, 48, 15, 51, 45, 13, 47, 34, 42, 4, 1, 2, 14, 7, 0, 32, 40, 39, 53, 52, 54, 11, 8], 'cur_cost': 57699.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [19, 21, 12, 14, 24, 23, 43, 35, 28, 30, 37, 34, 47, 13, 45, 51, 15, 48, 16, 46, 49, 44, 50, 20, 17, 18, 3, 26, 27, 33, 25, 38, 41, 22, 29, 9, 6, 31, 36, 10, 42, 4, 1, 2, 5, 7, 0, 32, 40, 39, 53, 52, 54, 11, 8], 'cur_cost': 50109.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [19, 21, 12, 14, 24, 23, 43, 35, 28, 30, 37, 10, 36, 31, 6, 9, 29, 22, 41, 38, 25, 33, 27, 26, 3, 18, 17, 20, 50, 44, 49, 46, 16, 48, 15, 51, 45, 13, 47, 34, 42, 4, 1, 2, 5, 7, 0, 32, 40, 39, 53, 52, 54, 11, 8], 'cur_cost': 50129.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [38, 28, 22, 27, 21, 5, 6, 4, 1, 10, 23, 3, 52, 2, 16, 30, 29, 44, 18, 17, 24, 51, 35, 36, 8, 37, 42, 33, 43, 53, 34, 31, 9, 49, 46, 11, 54, 48, 47, 14, 45, 19, 50, 39, 13, 26, 40, 7, 15, 41, 25, 12, 20, 0, 32], 'cur_cost': 82793.0, 'intermediate_solutions': [{'tour': [0, 8, 22, 9, 7, 3, 5, 6, 4, 1, 10, 2, 26, 31, 25, 23, 29, 27, 32, 28, 24, 30, 35, 36, 15, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 38, 20, 18, 12, 21, 19, 11], 'cur_cost': 27894.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 22, 9, 7, 3, 5, 6, 4, 1, 10, 2, 26, 31, 25, 38, 36, 35, 30, 24, 28, 32, 27, 29, 23, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 15980.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 8, 22, 9, 7, 3, 5, 6, 4, 1, 10, 2, 26, 31, 25, 23, 29, 27, 32, 28, 24, 30, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 21, 19, 11], 'cur_cost': 19376.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [30, 8, 22, 9, 7, 3, 1, 44, 16, 17, 38, 2, 41, 28, 33, 32, 29, 26, 14, 46, 24, 18, 35, 36, 45, 37, 42, 51, 43, 15, 53, 40, 13, 49, 27, 31, 54, 4, 47, 11, 21, 0, 50, 6, 34, 10, 52, 19, 20, 39, 25, 12, 23, 48, 5], 'cur_cost': 101088.0, 'intermediate_solutions': [{'tour': [0, 8, 12, 7, 3, 5, 6, 4, 1, 10, 23, 22, 41, 28, 25, 30, 35, 26, 18, 51, 43, 44, 34, 46, 16, 48, 15, 17, 45, 53, 42, 40, 9, 38, 24, 11, 37, 49, 2, 14, 54, 19, 13, 47, 29, 21, 27, 50, 32, 33, 36, 39, 20, 52, 31], 'cur_cost': 79611.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 12, 7, 3, 5, 6, 4, 1, 10, 2, 49, 37, 11, 24, 38, 9, 40, 42, 53, 45, 51, 15, 48, 16, 46, 34, 44, 43, 17, 18, 26, 35, 30, 25, 28, 41, 22, 23, 14, 54, 19, 13, 47, 29, 21, 27, 50, 32, 33, 36, 39, 20, 52, 31], 'cur_cost': 77385.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 12, 7, 3, 5, 6, 4, 1, 10, 23, 22, 41, 28, 25, 30, 35, 26, 18, 17, 43, 44, 34, 46, 16, 48, 15, 51, 45, 53, 42, 40, 9, 24, 11, 38, 37, 49, 2, 14, 54, 19, 13, 47, 29, 21, 27, 50, 32, 33, 36, 39, 20, 52, 31], 'cur_cost': 77137.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([27, 54, 26, 15, 29, 13, 10, 48, 11, 17,  3, 46, 39, 45, 38, 49, 25,
       20, 33, 14,  1, 42, 44, 47,  6, 34, 24, 50, 35,  4, 31,  7, 37,  8,
        0,  2, 32, 18, 52, 28, 16, 40, 22, 51, 30,  9, 12, 19,  5, 21, 43,
       53, 36, 41, 23], dtype=int64), 'cur_cost': 115543.0, 'intermediate_solutions': [{'tour': array([12, 47, 38, 27, 46, 54,  5, 30, 16,  9, 13,  3, 52,  2, 33, 32, 22,
       45, 17,  7, 44, 51, 25, 50,  8, 11, 29, 34, 23, 26, 53, 31, 15, 28,
       43, 35, 19, 48, 36, 42,  4,  0, 49,  6, 21, 20, 40, 18, 37, 41,  1,
       39, 10, 24, 14]), 'cur_cost': 106777.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([27, 12, 47, 38, 46, 54,  5, 30, 16,  9, 13,  3, 52,  2, 33, 32, 22,
       45, 17,  7, 44, 51, 25, 50,  8, 11, 29, 34, 23, 26, 53, 31, 15, 28,
       43, 35, 19, 48, 36, 42,  4,  0, 49,  6, 21, 20, 40, 18, 37, 41,  1,
       39, 10, 24, 14]), 'cur_cost': 111365.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([46, 27, 12, 47, 38, 54,  5, 30, 16,  9, 13,  3, 52,  2, 33, 32, 22,
       45, 17,  7, 44, 51, 25, 50,  8, 11, 29, 34, 23, 26, 53, 31, 15, 28,
       43, 35, 19, 48, 36, 42,  4,  0, 49,  6, 21, 20, 40, 18, 37, 41,  1,
       39, 10, 24, 14]), 'cur_cost': 112055.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([38, 27, 12, 47, 46, 54,  5, 30, 16,  9, 13,  3, 52,  2, 33, 32, 22,
       45, 17,  7, 44, 51, 25, 50,  8, 11, 29, 34, 23, 26, 53, 31, 15, 28,
       43, 35, 19, 48, 36, 42,  4,  0, 49,  6, 21, 20, 40, 18, 37, 41,  1,
       39, 10, 24, 14]), 'cur_cost': 107991.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([38, 46, 27, 12, 47, 54,  5, 30, 16,  9, 13,  3, 52,  2, 33, 32, 22,
       45, 17,  7, 44, 51, 25, 50,  8, 11, 29, 34, 23, 26, 53, 31, 15, 28,
       43, 35, 19, 48, 36, 42,  4,  0, 49,  6, 21, 20, 40, 18, 37, 41,  1,
       39, 10, 24, 14]), 'cur_cost': 111554.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 6, 35,  0, 52, 13,  5, 41, 31, 39, 34, 30, 21, 53, 44, 33, 43, 25,
       36, 24, 22, 16, 40, 11, 32, 26,  1, 17, 18, 48, 51,  3, 45, 46, 29,
       10, 54,  2, 50,  9, 14, 47, 28, 20,  8, 37, 49,  4, 23, 27,  7, 19,
       42, 38, 15, 12], dtype=int64), 'cur_cost': 102291.0, 'intermediate_solutions': [{'tour': array([23, 28, 30, 29, 21, 50, 19, 44, 41, 17, 38, 10, 51,  1, 16, 52, 35,
       45, 14, 49, 40, 27, 37,  8, 32,  0, 22,  9, 53, 18, 48,  6, 13, 46,
        5, 31, 26,  4, 33, 11, 15,  7, 43, 42, 24, 20, 34, 36,  3, 39, 25,
       12, 54,  2, 47]), 'cur_cost': 103672.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([29, 23, 28, 30, 21, 50, 19, 44, 41, 17, 38, 10, 51,  1, 16, 52, 35,
       45, 14, 49, 40, 27, 37,  8, 32,  0, 22,  9, 53, 18, 48,  6, 13, 46,
        5, 31, 26,  4, 33, 11, 15,  7, 43, 42, 24, 20, 34, 36,  3, 39, 25,
       12, 54,  2, 47]), 'cur_cost': 103529.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([21, 29, 23, 28, 30, 50, 19, 44, 41, 17, 38, 10, 51,  1, 16, 52, 35,
       45, 14, 49, 40, 27, 37,  8, 32,  0, 22,  9, 53, 18, 48,  6, 13, 46,
        5, 31, 26,  4, 33, 11, 15,  7, 43, 42, 24, 20, 34, 36,  3, 39, 25,
       12, 54,  2, 47]), 'cur_cost': 103543.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([30, 29, 23, 28, 21, 50, 19, 44, 41, 17, 38, 10, 51,  1, 16, 52, 35,
       45, 14, 49, 40, 27, 37,  8, 32,  0, 22,  9, 53, 18, 48,  6, 13, 46,
        5, 31, 26,  4, 33, 11, 15,  7, 43, 42, 24, 20, 34, 36,  3, 39, 25,
       12, 54,  2, 47]), 'cur_cost': 103542.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([30, 21, 29, 23, 28, 50, 19, 44, 41, 17, 38, 10, 51,  1, 16, 52, 35,
       45, 14, 49, 40, 27, 37,  8, 32,  0, 22,  9, 53, 18, 48,  6, 13, 46,
        5, 31, 26,  4, 33, 11, 15,  7, 43, 42, 24, 20, 34, 36,  3, 39, 25,
       12, 54,  2, 47]), 'cur_cost': 106726.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 16, 25, 23, 22, 32, 28, 24, 30, 26, 31, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 6, 4, 21, 19, 17, 13, 14, 11, 15, 20, 18, 12, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 17099.0, 'intermediate_solutions': [{'tour': [21, 7, 3, 44, 35, 4, 1, 37, 2, 36, 28, 6, 30, 27, 41, 17, 25, 43, 34, 46, 16, 18, 42, 33, 45, 39, 49, 9, 48, 15, 51, 13, 52, 47, 5, 0, 40, 53, 12, 50, 24, 32, 11, 10, 22, 26, 38, 19, 8, 20, 29, 54, 23, 14, 31], 'cur_cost': 101290.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [21, 7, 3, 43, 35, 4, 1, 37, 2, 36, 28, 6, 30, 27, 41, 17, 25, 44, 34, 46, 16, 18, 42, 33, 45, 39, 49, 9, 48, 15, 51, 13, 52, 47, 5, 0, 40, 53, 12, 50, 14, 23, 54, 29, 20, 8, 19, 38, 26, 22, 10, 11, 32, 24, 31], 'cur_cost': 98163.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [21, 7, 3, 43, 35, 4, 1, 37, 2, 36, 28, 6, 30, 27, 41, 17, 25, 44, 34, 46, 16, 18, 42, 33, 45, 39, 49, 9, 48, 15, 51, 13, 52, 47, 5, 0, 40, 53, 12, 50, 24, 11, 10, 22, 26, 38, 19, 8, 20, 29, 54, 23, 14, 31, 32], 'cur_cost': 101427.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 21, 18, 16, 15, 20, 12, 19, 17, 13, 14, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 6, 4, 1], 'cur_cost': 10727.0, 'intermediate_solutions': [{'tour': [0, 15, 5, 10, 8, 7, 9, 3, 2, 1, 4, 6, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 45, 40, 41, 49, 46, 44, 54, 53, 47, 51, 34, 52, 50, 48, 13, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 27998.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 5, 10, 8, 7, 9, 3, 2, 1, 4, 6, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 52, 45, 51, 47, 50, 48, 13, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 16573.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 5, 10, 8, 7, 9, 3, 2, 1, 4, 6, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 26, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 20528.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [27, 23, 30, 29, 24, 9, 2, 36, 40, 8, 26, 38, 32, 34, 25, 1, 35, 49, 16, 21, 13, 11, 45, 53, 17, 51, 14, 18, 54, 12, 20, 6, 28, 22, 41, 39, 31, 33, 37, 10, 7, 3, 19, 50, 47, 48, 52, 44, 46, 42, 0, 5, 4, 43, 15], 'cur_cost': 55250.0, 'intermediate_solutions': [{'tour': [0, 5, 9, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 2, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 3, 28, 1, 4, 6], 'cur_cost': 16561.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 9, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 2, 3, 1, 4, 6], 'cur_cost': 10767.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 9, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 26, 24, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 3, 2, 1, 4, 6], 'cur_cost': 10827.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 6, 4, 1, 8, 7, 9, 5, 3, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 16432.0, 'intermediate_solutions': [{'tour': [0, 13, 23, 15, 16, 18, 20, 12, 21, 19, 17, 14, 11, 51, 45, 52, 4, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 50, 6], 'cur_cost': 27600.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 23, 15, 16, 18, 20, 12, 21, 19, 17, 14, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 2, 3, 5, 9, 1, 4, 6], 'cur_cost': 15642.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 23, 15, 16, 18, 20, 12, 21, 19, 17, 14, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 30, 24, 26, 31, 25, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 19240.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 10685.0, 'intermediate_solutions': [{'tour': [24, 30, 31, 40, 0, 8, 28, 23, 26, 1, 2, 37, 34, 36, 32, 6, 33, 46, 16, 13, 15, 17, 14, 11, 53, 12, 49, 47, 51, 50, 52, 44, 21, 54, 48, 38, 35, 42, 25, 10, 4, 5, 9, 27, 39, 29, 41, 7, 22, 43, 3, 18, 19, 20, 45], 'cur_cost': 48260.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [24, 30, 31, 40, 0, 8, 28, 23, 26, 1, 2, 37, 34, 36, 32, 6, 33, 46, 16, 20, 15, 17, 14, 11, 53, 12, 49, 47, 51, 50, 52, 44, 21, 54, 48, 38, 35, 42, 25, 10, 13, 19, 18, 3, 43, 22, 7, 41, 29, 39, 27, 9, 5, 4, 45], 'cur_cost': 52843.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [24, 30, 31, 40, 0, 8, 28, 23, 26, 1, 2, 37, 34, 36, 32, 6, 33, 46, 16, 20, 15, 17, 14, 11, 53, 12, 49, 47, 51, 50, 52, 44, 21, 54, 48, 38, 41, 35, 42, 25, 10, 4, 5, 9, 27, 39, 29, 7, 22, 43, 3, 18, 19, 13, 45], 'cur_cost': 46199.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:29:02,451 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:02,451 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:02,454 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10685.000, 多样性=0.962
2025-08-05 10:29:02,454 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 10:29:02,454 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 10:29:02,454 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:02,455 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 80, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'slight_improvement', 'improvement_rate': 0.004309059985786012, 'best_improvement': 0.007708023774145617}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.009571369121931208}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.006835074305848417, 'recent_improvements': [0.09680525143500417, -0.002821838256659138, 0.08313510282330731], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 10442, 'new_best_cost': 10442, 'quality_improvement': 0.0, 'old_diversity': 0.9060606060606061, 'new_diversity': 0.9060606060606061, 'diversity_change': 0.0}, 'suggestions': ['改进缓慢，建议调整策略平衡或增加扰动强度', '种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启']}
2025-08-05 10:29:02,455 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 10:29:02,455 - __main__ - INFO - composite10_55 开始进化第 3 代
2025-08-05 10:29:02,455 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 10:29:02,455 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:02,456 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10685.000, 多样性=0.962
2025-08-05 10:29:02,456 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:02,459 - PathExpert - INFO - 路径结构分析完成: 公共边数量=8, 路径相似性=0.962
2025-08-05 10:29:02,459 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:02,460 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.906
2025-08-05 10:29:02,462 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 10:29:02,462 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:02,462 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 10:29:02,462 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 10:29:02,497 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.214, 适应度梯度: -4472.929, 聚类评分: 0.000, 覆盖率: 0.126, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:02,498 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 10:29:02,498 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:02,498 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite10_55
2025-08-05 10:29:02,502 - visualization.landscape_visualizer - INFO - 插值约束: 286 个点被约束到最小值 10442.00
2025-08-05 10:29:02,504 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.1%, 梯度: 3850.86 → 3575.88
2025-08-05 10:29:02,640 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite10_55\landscape_composite10_55_iter_108_20250805_102902.html
2025-08-05 10:29:02,734 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite10_55\dashboard_composite10_55_iter_108_20250805_102902.html
2025-08-05 10:29:02,734 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 108
2025-08-05 10:29:02,734 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 10:29:02,735 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2736秒
2025-08-05 10:29:02,735 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.21428571428571427, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -4472.9285714285725, 'local_optima_density': 0.21428571428571427, 'gradient_variance': 875173000.7920407, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1263, 'fitness_entropy': 0.700576754845952, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -4472.929)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.126)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360942.4984434, 'performance_metrics': {}}}
2025-08-05 10:29:02,735 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:02,735 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:02,736 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:02,736 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:02,736 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:29:02,736 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 10:29:02,736 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:29:02,737 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:02,737 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:02,737 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:29:02,737 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:02,737 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:02,737 - experts.management.collaboration_manager - INFO - 识别精英个体: {9, 6} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:02,737 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:02,738 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:02,738 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:02,739 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 10:29:02,739 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,739 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,740 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,740 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,740 - ExplorationExpert - INFO - 探索路径生成完成，成本: 18015.0, 路径长度: 55, 收集中间解: 3
2025-08-05 10:29:02,740 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 22, 18, 24, 30, 28, 32, 27, 29, 23, 25, 31, 26, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6, 21, 19, 17, 13, 14, 16, 15, 20, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 18015.0, 'intermediate_solutions': [{'tour': [0, 15, 10, 9, 7, 3, 5, 6, 4, 1, 8, 2, 26, 31, 25, 23, 22, 32, 36, 24, 30, 27, 29, 35, 28, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 21088.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 10, 9, 7, 3, 5, 6, 4, 1, 8, 2, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 18, 20, 21, 12, 19, 11], 'cur_cost': 16589.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 10, 9, 7, 3, 5, 6, 4, 1, 8, 2, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 26, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 18843.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:02,740 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 18015.00)
2025-08-05 10:29:02,741 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:02,741 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:02,741 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:02,744 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 55
2025-08-05 10:29:02,744 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,745 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,745 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,745 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,745 - ExplorationExpert - INFO - 探索路径生成完成，成本: 53307.0, 路径长度: 55, 收集中间解: 3
2025-08-05 10:29:02,745 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [27, 34, 29, 28, 41, 0, 23, 30, 38, 10, 43, 36, 33, 5, 37, 6, 31, 32, 1, 8, 9, 3, 7, 22, 46, 13, 47, 19, 51, 44, 54, 11, 16, 12, 18, 17, 52, 15, 14, 24, 25, 40, 4, 39, 35, 42, 2, 21, 53, 50, 45, 48, 49, 20, 26], 'cur_cost': 53307.0, 'intermediate_solutions': [{'tour': [38, 28, 22, 27, 21, 5, 6, 4, 1, 10, 23, 3, 52, 42, 16, 30, 29, 44, 18, 17, 24, 51, 35, 36, 8, 37, 2, 33, 43, 53, 34, 31, 9, 49, 46, 11, 54, 48, 47, 14, 45, 19, 50, 39, 13, 26, 40, 7, 15, 41, 25, 12, 20, 0, 32], 'cur_cost': 87649.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [38, 28, 22, 27, 21, 5, 6, 4, 1, 10, 23, 3, 52, 2, 16, 30, 29, 44, 18, 17, 24, 51, 35, 36, 8, 37, 42, 33, 43, 53, 34, 31, 9, 49, 46, 47, 48, 54, 11, 14, 45, 19, 50, 39, 13, 26, 40, 7, 15, 41, 25, 12, 20, 0, 32], 'cur_cost': 80531.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [38, 28, 22, 27, 21, 5, 6, 4, 1, 10, 23, 3, 52, 19, 2, 16, 30, 29, 44, 18, 17, 24, 51, 35, 36, 8, 37, 42, 33, 43, 53, 34, 31, 9, 49, 46, 11, 54, 48, 47, 14, 45, 50, 39, 13, 26, 40, 7, 15, 41, 25, 12, 20, 0, 32], 'cur_cost': 81519.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:02,746 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 53307.00)
2025-08-05 10:29:02,746 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-05 10:29:02,746 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:02,746 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:02,746 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 90447.0
2025-08-05 10:29:02,753 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:29:02,753 - ExploitationExpert - INFO - res_population_costs: [10442, 10445, 10448, 10468.0]
2025-08-05 10:29:02,753 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 21, 19, 18, 12, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  9,  7,  2,  3,
        5,  6,  4,  1], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 30, 24, 26, 31, 25, 23, 28, 32, 27, 22, 29,
       35, 33, 36, 38, 41, 40, 34, 39, 37, 42, 43, 10,  8,  2,  3,  7,  9,
        5,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  9,  7,  3,  2,  8, 10, 43, 42, 37, 39, 34, 40,
       41, 38, 36, 33, 35, 29, 31, 26, 25, 23, 22, 27, 32, 28, 24, 30, 49,
       46, 44, 53, 54, 50, 48, 52, 47, 51, 45, 13, 17, 14, 11, 16, 15, 20,
       18, 12, 21, 19], dtype=int64)]
2025-08-05 10:29:02,755 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:02,755 - ExploitationExpert - INFO - populations: [{'tour': [0, 22, 18, 24, 30, 28, 32, 27, 29, 23, 25, 31, 26, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6, 21, 19, 17, 13, 14, 16, 15, 20, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 18015.0}, {'tour': [27, 34, 29, 28, 41, 0, 23, 30, 38, 10, 43, 36, 33, 5, 37, 6, 31, 32, 1, 8, 9, 3, 7, 22, 46, 13, 47, 19, 51, 44, 54, 11, 16, 12, 18, 17, 52, 15, 14, 24, 25, 40, 4, 39, 35, 42, 2, 21, 53, 50, 45, 48, 49, 20, 26], 'cur_cost': 53307.0}, {'tour': array([11, 43, 23, 21, 14, 47, 28, 37, 40, 39, 44, 20, 22, 36,  4,  1,  9,
       13, 52, 31, 24, 19, 17, 25, 12, 50, 16, 46, 26, 45,  3,  5, 27, 48,
        2, 42, 33,  0,  6, 34, 18, 35, 54,  8, 10, 41, 38, 15, 53, 49, 32,
       29,  7, 51, 30], dtype=int64), 'cur_cost': 90447.0}, {'tour': [27, 54, 26, 15, 29, 13, 10, 48, 11, 17, 3, 46, 39, 45, 38, 49, 25, 20, 33, 14, 1, 42, 44, 47, 6, 34, 24, 50, 35, 4, 31, 7, 37, 8, 0, 2, 32, 18, 52, 28, 16, 40, 22, 51, 30, 9, 12, 19, 5, 21, 43, 53, 36, 41, 23], 'cur_cost': 115543.0}, {'tour': [6, 35, 0, 52, 13, 5, 41, 31, 39, 34, 30, 21, 53, 44, 33, 43, 25, 36, 24, 22, 16, 40, 11, 32, 26, 1, 17, 18, 48, 51, 3, 45, 46, 29, 10, 54, 2, 50, 9, 14, 47, 28, 20, 8, 37, 49, 4, 23, 27, 7, 19, 42, 38, 15, 12], 'cur_cost': 102291.0}, {'tour': [0, 1, 16, 25, 23, 22, 32, 28, 24, 30, 26, 31, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 6, 4, 21, 19, 17, 13, 14, 11, 15, 20, 18, 12, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 17099.0}, {'tour': [0, 2, 21, 18, 16, 15, 20, 12, 19, 17, 13, 14, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 6, 4, 1], 'cur_cost': 10727.0}, {'tour': [27, 23, 30, 29, 24, 9, 2, 36, 40, 8, 26, 38, 32, 34, 25, 1, 35, 49, 16, 21, 13, 11, 45, 53, 17, 51, 14, 18, 54, 12, 20, 6, 28, 22, 41, 39, 31, 33, 37, 10, 7, 3, 19, 50, 47, 48, 52, 44, 46, 42, 0, 5, 4, 43, 15], 'cur_cost': 55250.0}, {'tour': [0, 15, 6, 4, 1, 8, 7, 9, 5, 3, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 16432.0}, {'tour': [0, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 10685.0}]
2025-08-05 10:29:02,755 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:02,756 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 278, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 278, 'cache_hits': 0, 'similarity_calculations': 1366, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:02,756 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([11, 43, 23, 21, 14, 47, 28, 37, 40, 39, 44, 20, 22, 36,  4,  1,  9,
       13, 52, 31, 24, 19, 17, 25, 12, 50, 16, 46, 26, 45,  3,  5, 27, 48,
        2, 42, 33,  0,  6, 34, 18, 35, 54,  8, 10, 41, 38, 15, 53, 49, 32,
       29,  7, 51, 30], dtype=int64), 'cur_cost': 90447.0, 'intermediate_solutions': [{'tour': array([22,  8, 30,  9,  7,  3,  1, 44, 16, 17, 38,  2, 41, 28, 33, 32, 29,
       26, 14, 46, 24, 18, 35, 36, 45, 37, 42, 51, 43, 15, 53, 40, 13, 49,
       27, 31, 54,  4, 47, 11, 21,  0, 50,  6, 34, 10, 52, 19, 20, 39, 25,
       12, 23, 48,  5]), 'cur_cost': 101089.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9, 22,  8, 30,  7,  3,  1, 44, 16, 17, 38,  2, 41, 28, 33, 32, 29,
       26, 14, 46, 24, 18, 35, 36, 45, 37, 42, 51, 43, 15, 53, 40, 13, 49,
       27, 31, 54,  4, 47, 11, 21,  0, 50,  6, 34, 10, 52, 19, 20, 39, 25,
       12, 23, 48,  5]), 'cur_cost': 101094.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 7,  9, 22,  8, 30,  3,  1, 44, 16, 17, 38,  2, 41, 28, 33, 32, 29,
       26, 14, 46, 24, 18, 35, 36, 45, 37, 42, 51, 43, 15, 53, 40, 13, 49,
       27, 31, 54,  4, 47, 11, 21,  0, 50,  6, 34, 10, 52, 19, 20, 39, 25,
       12, 23, 48,  5]), 'cur_cost': 101113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([30,  9, 22,  8,  7,  3,  1, 44, 16, 17, 38,  2, 41, 28, 33, 32, 29,
       26, 14, 46, 24, 18, 35, 36, 45, 37, 42, 51, 43, 15, 53, 40, 13, 49,
       27, 31, 54,  4, 47, 11, 21,  0, 50,  6, 34, 10, 52, 19, 20, 39, 25,
       12, 23, 48,  5]), 'cur_cost': 101103.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([30,  7,  9, 22,  8,  3,  1, 44, 16, 17, 38,  2, 41, 28, 33, 32, 29,
       26, 14, 46, 24, 18, 35, 36, 45, 37, 42, 51, 43, 15, 53, 40, 13, 49,
       27, 31, 54,  4, 47, 11, 21,  0, 50,  6, 34, 10, 52, 19, 20, 39, 25,
       12, 23, 48,  5]), 'cur_cost': 101125.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:02,757 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 90447.00)
2025-08-05 10:29:02,757 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:29:02,757 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:02,757 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:02,757 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 106170.0
2025-08-05 10:29:02,768 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:29:02,768 - ExploitationExpert - INFO - res_population_costs: [10442, 10445, 10448, 10468.0]
2025-08-05 10:29:02,768 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 21, 19, 18, 12, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  9,  7,  2,  3,
        5,  6,  4,  1], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 30, 24, 26, 31, 25, 23, 28, 32, 27, 22, 29,
       35, 33, 36, 38, 41, 40, 34, 39, 37, 42, 43, 10,  8,  2,  3,  7,  9,
        5,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  9,  7,  3,  2,  8, 10, 43, 42, 37, 39, 34, 40,
       41, 38, 36, 33, 35, 29, 31, 26, 25, 23, 22, 27, 32, 28, 24, 30, 49,
       46, 44, 53, 54, 50, 48, 52, 47, 51, 45, 13, 17, 14, 11, 16, 15, 20,
       18, 12, 21, 19], dtype=int64)]
2025-08-05 10:29:02,770 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:02,770 - ExploitationExpert - INFO - populations: [{'tour': [0, 22, 18, 24, 30, 28, 32, 27, 29, 23, 25, 31, 26, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6, 21, 19, 17, 13, 14, 16, 15, 20, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 18015.0}, {'tour': [27, 34, 29, 28, 41, 0, 23, 30, 38, 10, 43, 36, 33, 5, 37, 6, 31, 32, 1, 8, 9, 3, 7, 22, 46, 13, 47, 19, 51, 44, 54, 11, 16, 12, 18, 17, 52, 15, 14, 24, 25, 40, 4, 39, 35, 42, 2, 21, 53, 50, 45, 48, 49, 20, 26], 'cur_cost': 53307.0}, {'tour': array([11, 43, 23, 21, 14, 47, 28, 37, 40, 39, 44, 20, 22, 36,  4,  1,  9,
       13, 52, 31, 24, 19, 17, 25, 12, 50, 16, 46, 26, 45,  3,  5, 27, 48,
        2, 42, 33,  0,  6, 34, 18, 35, 54,  8, 10, 41, 38, 15, 53, 49, 32,
       29,  7, 51, 30], dtype=int64), 'cur_cost': 90447.0}, {'tour': array([40, 32,  9, 37,  7,  3, 38, 29, 36, 18, 13, 39, 46, 44, 21,  5, 43,
       53, 19, 10, 45, 33, 47, 48, 35, 17, 23, 31,  4, 34,  6,  0, 16, 27,
       25, 22, 54, 42, 14, 15, 30,  1, 26, 51, 41,  8, 49, 20, 28, 24, 11,
       52,  2, 50, 12], dtype=int64), 'cur_cost': 106170.0}, {'tour': [6, 35, 0, 52, 13, 5, 41, 31, 39, 34, 30, 21, 53, 44, 33, 43, 25, 36, 24, 22, 16, 40, 11, 32, 26, 1, 17, 18, 48, 51, 3, 45, 46, 29, 10, 54, 2, 50, 9, 14, 47, 28, 20, 8, 37, 49, 4, 23, 27, 7, 19, 42, 38, 15, 12], 'cur_cost': 102291.0}, {'tour': [0, 1, 16, 25, 23, 22, 32, 28, 24, 30, 26, 31, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 6, 4, 21, 19, 17, 13, 14, 11, 15, 20, 18, 12, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 17099.0}, {'tour': [0, 2, 21, 18, 16, 15, 20, 12, 19, 17, 13, 14, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 6, 4, 1], 'cur_cost': 10727.0}, {'tour': [27, 23, 30, 29, 24, 9, 2, 36, 40, 8, 26, 38, 32, 34, 25, 1, 35, 49, 16, 21, 13, 11, 45, 53, 17, 51, 14, 18, 54, 12, 20, 6, 28, 22, 41, 39, 31, 33, 37, 10, 7, 3, 19, 50, 47, 48, 52, 44, 46, 42, 0, 5, 4, 43, 15], 'cur_cost': 55250.0}, {'tour': [0, 15, 6, 4, 1, 8, 7, 9, 5, 3, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 16432.0}, {'tour': [0, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 10685.0}]
2025-08-05 10:29:02,771 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:02,771 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 279, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 279, 'cache_hits': 0, 'similarity_calculations': 1371, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:02,772 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([40, 32,  9, 37,  7,  3, 38, 29, 36, 18, 13, 39, 46, 44, 21,  5, 43,
       53, 19, 10, 45, 33, 47, 48, 35, 17, 23, 31,  4, 34,  6,  0, 16, 27,
       25, 22, 54, 42, 14, 15, 30,  1, 26, 51, 41,  8, 49, 20, 28, 24, 11,
       52,  2, 50, 12], dtype=int64), 'cur_cost': 106170.0, 'intermediate_solutions': [{'tour': array([26, 54, 27, 15, 29, 13, 10, 48, 11, 17,  3, 46, 39, 45, 38, 49, 25,
       20, 33, 14,  1, 42, 44, 47,  6, 34, 24, 50, 35,  4, 31,  7, 37,  8,
        0,  2, 32, 18, 52, 28, 16, 40, 22, 51, 30,  9, 12, 19,  5, 21, 43,
       53, 36, 41, 23]), 'cur_cost': 115576.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([15, 26, 54, 27, 29, 13, 10, 48, 11, 17,  3, 46, 39, 45, 38, 49, 25,
       20, 33, 14,  1, 42, 44, 47,  6, 34, 24, 50, 35,  4, 31,  7, 37,  8,
        0,  2, 32, 18, 52, 28, 16, 40, 22, 51, 30,  9, 12, 19,  5, 21, 43,
       53, 36, 41, 23]), 'cur_cost': 115514.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([29, 15, 26, 54, 27, 13, 10, 48, 11, 17,  3, 46, 39, 45, 38, 49, 25,
       20, 33, 14,  1, 42, 44, 47,  6, 34, 24, 50, 35,  4, 31,  7, 37,  8,
        0,  2, 32, 18, 52, 28, 16, 40, 22, 51, 30,  9, 12, 19,  5, 21, 43,
       53, 36, 41, 23]), 'cur_cost': 115499.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([27, 15, 26, 54, 29, 13, 10, 48, 11, 17,  3, 46, 39, 45, 38, 49, 25,
       20, 33, 14,  1, 42, 44, 47,  6, 34, 24, 50, 35,  4, 31,  7, 37,  8,
        0,  2, 32, 18, 52, 28, 16, 40, 22, 51, 30,  9, 12, 19,  5, 21, 43,
       53, 36, 41, 23]), 'cur_cost': 115558.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([27, 29, 15, 26, 54, 13, 10, 48, 11, 17,  3, 46, 39, 45, 38, 49, 25,
       20, 33, 14,  1, 42, 44, 47,  6, 34, 24, 50, 35,  4, 31,  7, 37,  8,
        0,  2, 32, 18, 52, 28, 16, 40, 22, 51, 30,  9, 12, 19,  5, 21, 43,
       53, 36, 41, 23]), 'cur_cost': 112214.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:02,773 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 106170.00)
2025-08-05 10:29:02,773 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:29:02,773 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:02,773 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:02,773 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 98270.0
2025-08-05 10:29:02,779 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:29:02,779 - ExploitationExpert - INFO - res_population_costs: [10442, 10445, 10448, 10468.0]
2025-08-05 10:29:02,779 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 21, 19, 18, 12, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  9,  7,  2,  3,
        5,  6,  4,  1], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 30, 24, 26, 31, 25, 23, 28, 32, 27, 22, 29,
       35, 33, 36, 38, 41, 40, 34, 39, 37, 42, 43, 10,  8,  2,  3,  7,  9,
        5,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  9,  7,  3,  2,  8, 10, 43, 42, 37, 39, 34, 40,
       41, 38, 36, 33, 35, 29, 31, 26, 25, 23, 22, 27, 32, 28, 24, 30, 49,
       46, 44, 53, 54, 50, 48, 52, 47, 51, 45, 13, 17, 14, 11, 16, 15, 20,
       18, 12, 21, 19], dtype=int64)]
2025-08-05 10:29:02,781 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:02,781 - ExploitationExpert - INFO - populations: [{'tour': [0, 22, 18, 24, 30, 28, 32, 27, 29, 23, 25, 31, 26, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6, 21, 19, 17, 13, 14, 16, 15, 20, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 18015.0}, {'tour': [27, 34, 29, 28, 41, 0, 23, 30, 38, 10, 43, 36, 33, 5, 37, 6, 31, 32, 1, 8, 9, 3, 7, 22, 46, 13, 47, 19, 51, 44, 54, 11, 16, 12, 18, 17, 52, 15, 14, 24, 25, 40, 4, 39, 35, 42, 2, 21, 53, 50, 45, 48, 49, 20, 26], 'cur_cost': 53307.0}, {'tour': array([11, 43, 23, 21, 14, 47, 28, 37, 40, 39, 44, 20, 22, 36,  4,  1,  9,
       13, 52, 31, 24, 19, 17, 25, 12, 50, 16, 46, 26, 45,  3,  5, 27, 48,
        2, 42, 33,  0,  6, 34, 18, 35, 54,  8, 10, 41, 38, 15, 53, 49, 32,
       29,  7, 51, 30], dtype=int64), 'cur_cost': 90447.0}, {'tour': array([40, 32,  9, 37,  7,  3, 38, 29, 36, 18, 13, 39, 46, 44, 21,  5, 43,
       53, 19, 10, 45, 33, 47, 48, 35, 17, 23, 31,  4, 34,  6,  0, 16, 27,
       25, 22, 54, 42, 14, 15, 30,  1, 26, 51, 41,  8, 49, 20, 28, 24, 11,
       52,  2, 50, 12], dtype=int64), 'cur_cost': 106170.0}, {'tour': array([47, 10, 23, 44, 18, 45,  1,  9,  3,  4,  8, 14, 22, 25, 51, 12, 53,
        2, 24, 15, 33, 43, 17, 38, 34, 19, 48, 50, 52, 21, 39,  5, 42, 20,
       16, 36, 49,  6, 40, 37, 54, 35, 11, 41,  7, 26, 31, 46, 28,  0, 13,
       32, 30, 27, 29], dtype=int64), 'cur_cost': 98270.0}, {'tour': [0, 1, 16, 25, 23, 22, 32, 28, 24, 30, 26, 31, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 6, 4, 21, 19, 17, 13, 14, 11, 15, 20, 18, 12, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 17099.0}, {'tour': [0, 2, 21, 18, 16, 15, 20, 12, 19, 17, 13, 14, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 6, 4, 1], 'cur_cost': 10727.0}, {'tour': [27, 23, 30, 29, 24, 9, 2, 36, 40, 8, 26, 38, 32, 34, 25, 1, 35, 49, 16, 21, 13, 11, 45, 53, 17, 51, 14, 18, 54, 12, 20, 6, 28, 22, 41, 39, 31, 33, 37, 10, 7, 3, 19, 50, 47, 48, 52, 44, 46, 42, 0, 5, 4, 43, 15], 'cur_cost': 55250.0}, {'tour': [0, 15, 6, 4, 1, 8, 7, 9, 5, 3, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 16432.0}, {'tour': [0, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 10685.0}]
2025-08-05 10:29:02,783 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:02,783 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 280, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 280, 'cache_hits': 0, 'similarity_calculations': 1377, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:02,784 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([47, 10, 23, 44, 18, 45,  1,  9,  3,  4,  8, 14, 22, 25, 51, 12, 53,
        2, 24, 15, 33, 43, 17, 38, 34, 19, 48, 50, 52, 21, 39,  5, 42, 20,
       16, 36, 49,  6, 40, 37, 54, 35, 11, 41,  7, 26, 31, 46, 28,  0, 13,
       32, 30, 27, 29], dtype=int64), 'cur_cost': 98270.0, 'intermediate_solutions': [{'tour': array([ 0, 35,  6, 52, 13,  5, 41, 31, 39, 34, 30, 21, 53, 44, 33, 43, 25,
       36, 24, 22, 16, 40, 11, 32, 26,  1, 17, 18, 48, 51,  3, 45, 46, 29,
       10, 54,  2, 50,  9, 14, 47, 28, 20,  8, 37, 49,  4, 23, 27,  7, 19,
       42, 38, 15, 12]), 'cur_cost': 102313.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([52,  0, 35,  6, 13,  5, 41, 31, 39, 34, 30, 21, 53, 44, 33, 43, 25,
       36, 24, 22, 16, 40, 11, 32, 26,  1, 17, 18, 48, 51,  3, 45, 46, 29,
       10, 54,  2, 50,  9, 14, 47, 28, 20,  8, 37, 49,  4, 23, 27,  7, 19,
       42, 38, 15, 12]), 'cur_cost': 102408.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([13, 52,  0, 35,  6,  5, 41, 31, 39, 34, 30, 21, 53, 44, 33, 43, 25,
       36, 24, 22, 16, 40, 11, 32, 26,  1, 17, 18, 48, 51,  3, 45, 46, 29,
       10, 54,  2, 50,  9, 14, 47, 28, 20,  8, 37, 49,  4, 23, 27,  7, 19,
       42, 38, 15, 12]), 'cur_cost': 96663.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6, 52,  0, 35, 13,  5, 41, 31, 39, 34, 30, 21, 53, 44, 33, 43, 25,
       36, 24, 22, 16, 40, 11, 32, 26,  1, 17, 18, 48, 51,  3, 45, 46, 29,
       10, 54,  2, 50,  9, 14, 47, 28, 20,  8, 37, 49,  4, 23, 27,  7, 19,
       42, 38, 15, 12]), 'cur_cost': 105423.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6, 13, 52,  0, 35,  5, 41, 31, 39, 34, 30, 21, 53, 44, 33, 43, 25,
       36, 24, 22, 16, 40, 11, 32, 26,  1, 17, 18, 48, 51,  3, 45, 46, 29,
       10, 54,  2, 50,  9, 14, 47, 28, 20,  8, 37, 49,  4, 23, 27,  7, 19,
       42, 38, 15, 12]), 'cur_cost': 102298.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:02,784 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 98270.00)
2025-08-05 10:29:02,784 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:29:02,784 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:29:02,784 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:02,786 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 10:29:02,786 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,786 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,786 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,787 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,787 - ExplorationExpert - INFO - 探索路径生成完成，成本: 18958.0, 路径长度: 55, 收集中间解: 3
2025-08-05 10:29:02,787 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 15, 7, 23, 22, 32, 28, 24, 30, 26, 31, 25, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 1, 4, 6, 5, 9, 3, 2, 21, 19, 17, 13, 14, 16, 18, 20, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 18958.0, 'intermediate_solutions': [{'tour': [0, 1, 16, 25, 23, 22, 32, 28, 51, 30, 26, 31, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 6, 4, 21, 19, 17, 13, 14, 11, 15, 20, 18, 12, 24, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 24316.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 16, 25, 23, 22, 32, 6, 2, 3, 5, 9, 7, 8, 10, 41, 40, 34, 39, 43, 33, 42, 37, 38, 36, 35, 27, 29, 31, 26, 30, 24, 28, 4, 21, 19, 17, 13, 14, 11, 15, 20, 18, 12, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 19945.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 16, 25, 23, 22, 32, 28, 24, 30, 26, 31, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 6, 5, 3, 2, 4, 21, 19, 17, 13, 14, 11, 15, 20, 18, 12, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 17113.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:02,787 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 18958.00)
2025-08-05 10:29:02,787 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:02,787 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:02,787 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:02,789 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 55
2025-08-05 10:29:02,789 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,789 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,789 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,789 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,790 - ExplorationExpert - INFO - 探索路径生成完成，成本: 79464.0, 路径长度: 55, 收集中间解: 3
2025-08-05 10:29:02,790 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [29, 28, 30, 23, 38, 31, 26, 35, 6, 42, 33, 8, 34, 40, 41, 10, 13, 7, 5, 3, 2, 1, 16, 12, 18, 17, 52, 15, 53, 27, 25, 45, 48, 36, 21, 20, 50, 0, 14, 19, 22, 24, 51, 11, 39, 49, 43, 46, 54, 37, 47, 9, 44, 32, 4], 'cur_cost': 79464.0, 'intermediate_solutions': [{'tour': [0, 2, 21, 18, 16, 15, 20, 12, 19, 17, 13, 14, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 8, 33, 43, 39, 34, 40, 41, 10, 42, 7, 9, 5, 3, 6, 4, 1], 'cur_cost': 19470.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 21, 18, 16, 15, 20, 12, 10, 41, 40, 34, 39, 43, 33, 42, 37, 38, 36, 35, 29, 27, 28, 32, 22, 23, 25, 31, 26, 24, 30, 49, 46, 53, 44, 54, 47, 48, 50, 52, 45, 51, 11, 14, 13, 17, 19, 8, 7, 9, 5, 3, 6, 4, 1], 'cur_cost': 16421.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 21, 18, 16, 15, 20, 12, 19, 17, 13, 14, 11, 51, 45, 52, 50, 48, 47, 54, 25, 44, 53, 46, 49, 30, 24, 26, 31, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 6, 4, 1], 'cur_cost': 14755.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:02,790 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 79464.00)
2025-08-05 10:29:02,790 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:02,790 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:02,790 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:02,791 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 55
2025-08-05 10:29:02,792 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,792 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,792 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,792 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,792 - ExplorationExpert - INFO - 探索路径生成完成，成本: 72811.0, 路径长度: 55, 收集中间解: 3
2025-08-05 10:29:02,793 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [29, 28, 41, 23, 27, 38, 25, 31, 26, 35, 36, 6, 37, 42, 1, 34, 40, 22, 46, 13, 7, 9, 51, 44, 2, 4, 12, 18, 17, 52, 15, 53, 19, 45, 47, 49, 32, 21, 16, 50, 20, 48, 5, 14, 33, 30, 10, 24, 54, 11, 3, 8, 39, 0, 43], 'cur_cost': 72811.0, 'intermediate_solutions': [{'tour': [27, 23, 30, 29, 24, 9, 2, 36, 40, 8, 26, 38, 32, 34, 25, 1, 35, 49, 16, 21, 13, 11, 45, 53, 17, 51, 14, 18, 54, 12, 20, 6, 28, 10, 41, 39, 31, 33, 37, 22, 7, 3, 19, 50, 47, 48, 52, 44, 46, 42, 0, 5, 4, 43, 15], 'cur_cost': 58047.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [27, 23, 30, 29, 24, 9, 2, 36, 40, 8, 26, 38, 32, 34, 25, 1, 35, 49, 16, 21, 13, 11, 45, 53, 17, 51, 15, 43, 4, 5, 0, 42, 46, 44, 52, 48, 47, 50, 19, 3, 7, 10, 37, 33, 31, 39, 41, 22, 28, 6, 20, 12, 54, 18, 14], 'cur_cost': 55256.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [27, 23, 30, 29, 18, 24, 9, 2, 36, 40, 8, 26, 38, 32, 34, 25, 1, 35, 49, 16, 21, 13, 11, 45, 53, 17, 51, 14, 54, 12, 20, 6, 28, 22, 41, 39, 31, 33, 37, 10, 7, 3, 19, 50, 47, 48, 52, 44, 46, 42, 0, 5, 4, 43, 15], 'cur_cost': 60085.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:02,793 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 72811.00)
2025-08-05 10:29:02,793 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:29:02,793 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:29:02,793 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:02,798 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 55
2025-08-05 10:29:02,798 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,798 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,798 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,798 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,799 - ExplorationExpert - INFO - 探索路径生成完成，成本: 55129.0, 路径长度: 55, 收集中间解: 3
2025-08-05 10:29:02,799 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [32, 30, 29, 28, 36, 0, 22, 27, 33, 5, 26, 10, 31, 53, 46, 20, 44, 11, 52, 15, 18, 25, 41, 42, 24, 23, 43, 40, 9, 7, 38, 54, 17, 51, 45, 50, 49, 16, 13, 12, 21, 1, 4, 2, 6, 3, 37, 39, 34, 47, 14, 19, 48, 35, 8], 'cur_cost': 55129.0, 'intermediate_solutions': [{'tour': [0, 15, 32, 4, 1, 8, 7, 9, 5, 3, 2, 10, 31, 25, 23, 22, 6, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 20321.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 6, 4, 1, 8, 7, 9, 5, 3, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 16, 14, 17, 18, 20, 12, 21, 19, 11], 'cur_cost': 16490.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 6, 4, 1, 8, 7, 9, 5, 3, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 48, 13, 17, 14, 16, 50, 18, 20, 12, 21, 19, 11], 'cur_cost': 18796.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:02,800 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 55129.00)
2025-08-05 10:29:02,800 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:29:02,800 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:29:02,800 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:02,806 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 55
2025-08-05 10:29:02,806 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,807 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,807 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,807 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:02,807 - ExplorationExpert - INFO - 探索路径生成完成，成本: 54471.0, 路径长度: 55, 收集中间解: 3
2025-08-05 10:29:02,807 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [27, 4, 32, 38, 35, 26, 39, 28, 10, 8, 29, 7, 25, 34, 36, 22, 37, 0, 31, 40, 23, 41, 43, 42, 54, 48, 45, 16, 20, 13, 18, 21, 53, 17, 15, 52, 11, 47, 51, 46, 30, 9, 2, 5, 1, 19, 44, 12, 14, 24, 33, 6, 3, 49, 50], 'cur_cost': 54471.0, 'intermediate_solutions': [{'tour': [0, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 40, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 51, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 21693.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 3, 5, 9, 7, 8, 10, 41, 2, 1, 4, 6], 'cur_cost': 15227.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 32, 2, 1, 4, 6], 'cur_cost': 13660.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:02,808 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 54471.00)
2025-08-05 10:29:02,808 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:02,808 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:02,810 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 22, 18, 24, 30, 28, 32, 27, 29, 23, 25, 31, 26, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6, 21, 19, 17, 13, 14, 16, 15, 20, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 18015.0, 'intermediate_solutions': [{'tour': [0, 15, 10, 9, 7, 3, 5, 6, 4, 1, 8, 2, 26, 31, 25, 23, 22, 32, 36, 24, 30, 27, 29, 35, 28, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 21088.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 10, 9, 7, 3, 5, 6, 4, 1, 8, 2, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 18, 20, 21, 12, 19, 11], 'cur_cost': 16589.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 10, 9, 7, 3, 5, 6, 4, 1, 8, 2, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 26, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 18843.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [27, 34, 29, 28, 41, 0, 23, 30, 38, 10, 43, 36, 33, 5, 37, 6, 31, 32, 1, 8, 9, 3, 7, 22, 46, 13, 47, 19, 51, 44, 54, 11, 16, 12, 18, 17, 52, 15, 14, 24, 25, 40, 4, 39, 35, 42, 2, 21, 53, 50, 45, 48, 49, 20, 26], 'cur_cost': 53307.0, 'intermediate_solutions': [{'tour': [38, 28, 22, 27, 21, 5, 6, 4, 1, 10, 23, 3, 52, 42, 16, 30, 29, 44, 18, 17, 24, 51, 35, 36, 8, 37, 2, 33, 43, 53, 34, 31, 9, 49, 46, 11, 54, 48, 47, 14, 45, 19, 50, 39, 13, 26, 40, 7, 15, 41, 25, 12, 20, 0, 32], 'cur_cost': 87649.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [38, 28, 22, 27, 21, 5, 6, 4, 1, 10, 23, 3, 52, 2, 16, 30, 29, 44, 18, 17, 24, 51, 35, 36, 8, 37, 42, 33, 43, 53, 34, 31, 9, 49, 46, 47, 48, 54, 11, 14, 45, 19, 50, 39, 13, 26, 40, 7, 15, 41, 25, 12, 20, 0, 32], 'cur_cost': 80531.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [38, 28, 22, 27, 21, 5, 6, 4, 1, 10, 23, 3, 52, 19, 2, 16, 30, 29, 44, 18, 17, 24, 51, 35, 36, 8, 37, 42, 33, 43, 53, 34, 31, 9, 49, 46, 11, 54, 48, 47, 14, 45, 50, 39, 13, 26, 40, 7, 15, 41, 25, 12, 20, 0, 32], 'cur_cost': 81519.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([11, 43, 23, 21, 14, 47, 28, 37, 40, 39, 44, 20, 22, 36,  4,  1,  9,
       13, 52, 31, 24, 19, 17, 25, 12, 50, 16, 46, 26, 45,  3,  5, 27, 48,
        2, 42, 33,  0,  6, 34, 18, 35, 54,  8, 10, 41, 38, 15, 53, 49, 32,
       29,  7, 51, 30], dtype=int64), 'cur_cost': 90447.0, 'intermediate_solutions': [{'tour': array([22,  8, 30,  9,  7,  3,  1, 44, 16, 17, 38,  2, 41, 28, 33, 32, 29,
       26, 14, 46, 24, 18, 35, 36, 45, 37, 42, 51, 43, 15, 53, 40, 13, 49,
       27, 31, 54,  4, 47, 11, 21,  0, 50,  6, 34, 10, 52, 19, 20, 39, 25,
       12, 23, 48,  5]), 'cur_cost': 101089.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9, 22,  8, 30,  7,  3,  1, 44, 16, 17, 38,  2, 41, 28, 33, 32, 29,
       26, 14, 46, 24, 18, 35, 36, 45, 37, 42, 51, 43, 15, 53, 40, 13, 49,
       27, 31, 54,  4, 47, 11, 21,  0, 50,  6, 34, 10, 52, 19, 20, 39, 25,
       12, 23, 48,  5]), 'cur_cost': 101094.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 7,  9, 22,  8, 30,  3,  1, 44, 16, 17, 38,  2, 41, 28, 33, 32, 29,
       26, 14, 46, 24, 18, 35, 36, 45, 37, 42, 51, 43, 15, 53, 40, 13, 49,
       27, 31, 54,  4, 47, 11, 21,  0, 50,  6, 34, 10, 52, 19, 20, 39, 25,
       12, 23, 48,  5]), 'cur_cost': 101113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([30,  9, 22,  8,  7,  3,  1, 44, 16, 17, 38,  2, 41, 28, 33, 32, 29,
       26, 14, 46, 24, 18, 35, 36, 45, 37, 42, 51, 43, 15, 53, 40, 13, 49,
       27, 31, 54,  4, 47, 11, 21,  0, 50,  6, 34, 10, 52, 19, 20, 39, 25,
       12, 23, 48,  5]), 'cur_cost': 101103.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([30,  7,  9, 22,  8,  3,  1, 44, 16, 17, 38,  2, 41, 28, 33, 32, 29,
       26, 14, 46, 24, 18, 35, 36, 45, 37, 42, 51, 43, 15, 53, 40, 13, 49,
       27, 31, 54,  4, 47, 11, 21,  0, 50,  6, 34, 10, 52, 19, 20, 39, 25,
       12, 23, 48,  5]), 'cur_cost': 101125.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([40, 32,  9, 37,  7,  3, 38, 29, 36, 18, 13, 39, 46, 44, 21,  5, 43,
       53, 19, 10, 45, 33, 47, 48, 35, 17, 23, 31,  4, 34,  6,  0, 16, 27,
       25, 22, 54, 42, 14, 15, 30,  1, 26, 51, 41,  8, 49, 20, 28, 24, 11,
       52,  2, 50, 12], dtype=int64), 'cur_cost': 106170.0, 'intermediate_solutions': [{'tour': array([26, 54, 27, 15, 29, 13, 10, 48, 11, 17,  3, 46, 39, 45, 38, 49, 25,
       20, 33, 14,  1, 42, 44, 47,  6, 34, 24, 50, 35,  4, 31,  7, 37,  8,
        0,  2, 32, 18, 52, 28, 16, 40, 22, 51, 30,  9, 12, 19,  5, 21, 43,
       53, 36, 41, 23]), 'cur_cost': 115576.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([15, 26, 54, 27, 29, 13, 10, 48, 11, 17,  3, 46, 39, 45, 38, 49, 25,
       20, 33, 14,  1, 42, 44, 47,  6, 34, 24, 50, 35,  4, 31,  7, 37,  8,
        0,  2, 32, 18, 52, 28, 16, 40, 22, 51, 30,  9, 12, 19,  5, 21, 43,
       53, 36, 41, 23]), 'cur_cost': 115514.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([29, 15, 26, 54, 27, 13, 10, 48, 11, 17,  3, 46, 39, 45, 38, 49, 25,
       20, 33, 14,  1, 42, 44, 47,  6, 34, 24, 50, 35,  4, 31,  7, 37,  8,
        0,  2, 32, 18, 52, 28, 16, 40, 22, 51, 30,  9, 12, 19,  5, 21, 43,
       53, 36, 41, 23]), 'cur_cost': 115499.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([27, 15, 26, 54, 29, 13, 10, 48, 11, 17,  3, 46, 39, 45, 38, 49, 25,
       20, 33, 14,  1, 42, 44, 47,  6, 34, 24, 50, 35,  4, 31,  7, 37,  8,
        0,  2, 32, 18, 52, 28, 16, 40, 22, 51, 30,  9, 12, 19,  5, 21, 43,
       53, 36, 41, 23]), 'cur_cost': 115558.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([27, 29, 15, 26, 54, 13, 10, 48, 11, 17,  3, 46, 39, 45, 38, 49, 25,
       20, 33, 14,  1, 42, 44, 47,  6, 34, 24, 50, 35,  4, 31,  7, 37,  8,
        0,  2, 32, 18, 52, 28, 16, 40, 22, 51, 30,  9, 12, 19,  5, 21, 43,
       53, 36, 41, 23]), 'cur_cost': 112214.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([47, 10, 23, 44, 18, 45,  1,  9,  3,  4,  8, 14, 22, 25, 51, 12, 53,
        2, 24, 15, 33, 43, 17, 38, 34, 19, 48, 50, 52, 21, 39,  5, 42, 20,
       16, 36, 49,  6, 40, 37, 54, 35, 11, 41,  7, 26, 31, 46, 28,  0, 13,
       32, 30, 27, 29], dtype=int64), 'cur_cost': 98270.0, 'intermediate_solutions': [{'tour': array([ 0, 35,  6, 52, 13,  5, 41, 31, 39, 34, 30, 21, 53, 44, 33, 43, 25,
       36, 24, 22, 16, 40, 11, 32, 26,  1, 17, 18, 48, 51,  3, 45, 46, 29,
       10, 54,  2, 50,  9, 14, 47, 28, 20,  8, 37, 49,  4, 23, 27,  7, 19,
       42, 38, 15, 12]), 'cur_cost': 102313.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([52,  0, 35,  6, 13,  5, 41, 31, 39, 34, 30, 21, 53, 44, 33, 43, 25,
       36, 24, 22, 16, 40, 11, 32, 26,  1, 17, 18, 48, 51,  3, 45, 46, 29,
       10, 54,  2, 50,  9, 14, 47, 28, 20,  8, 37, 49,  4, 23, 27,  7, 19,
       42, 38, 15, 12]), 'cur_cost': 102408.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([13, 52,  0, 35,  6,  5, 41, 31, 39, 34, 30, 21, 53, 44, 33, 43, 25,
       36, 24, 22, 16, 40, 11, 32, 26,  1, 17, 18, 48, 51,  3, 45, 46, 29,
       10, 54,  2, 50,  9, 14, 47, 28, 20,  8, 37, 49,  4, 23, 27,  7, 19,
       42, 38, 15, 12]), 'cur_cost': 96663.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6, 52,  0, 35, 13,  5, 41, 31, 39, 34, 30, 21, 53, 44, 33, 43, 25,
       36, 24, 22, 16, 40, 11, 32, 26,  1, 17, 18, 48, 51,  3, 45, 46, 29,
       10, 54,  2, 50,  9, 14, 47, 28, 20,  8, 37, 49,  4, 23, 27,  7, 19,
       42, 38, 15, 12]), 'cur_cost': 105423.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6, 13, 52,  0, 35,  5, 41, 31, 39, 34, 30, 21, 53, 44, 33, 43, 25,
       36, 24, 22, 16, 40, 11, 32, 26,  1, 17, 18, 48, 51,  3, 45, 46, 29,
       10, 54,  2, 50,  9, 14, 47, 28, 20,  8, 37, 49,  4, 23, 27,  7, 19,
       42, 38, 15, 12]), 'cur_cost': 102298.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 7, 23, 22, 32, 28, 24, 30, 26, 31, 25, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 1, 4, 6, 5, 9, 3, 2, 21, 19, 17, 13, 14, 16, 18, 20, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 18958.0, 'intermediate_solutions': [{'tour': [0, 1, 16, 25, 23, 22, 32, 28, 51, 30, 26, 31, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 6, 4, 21, 19, 17, 13, 14, 11, 15, 20, 18, 12, 24, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 24316.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 16, 25, 23, 22, 32, 6, 2, 3, 5, 9, 7, 8, 10, 41, 40, 34, 39, 43, 33, 42, 37, 38, 36, 35, 27, 29, 31, 26, 30, 24, 28, 4, 21, 19, 17, 13, 14, 11, 15, 20, 18, 12, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 19945.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 16, 25, 23, 22, 32, 28, 24, 30, 26, 31, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 6, 5, 3, 2, 4, 21, 19, 17, 13, 14, 11, 15, 20, 18, 12, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 17113.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [29, 28, 30, 23, 38, 31, 26, 35, 6, 42, 33, 8, 34, 40, 41, 10, 13, 7, 5, 3, 2, 1, 16, 12, 18, 17, 52, 15, 53, 27, 25, 45, 48, 36, 21, 20, 50, 0, 14, 19, 22, 24, 51, 11, 39, 49, 43, 46, 54, 37, 47, 9, 44, 32, 4], 'cur_cost': 79464.0, 'intermediate_solutions': [{'tour': [0, 2, 21, 18, 16, 15, 20, 12, 19, 17, 13, 14, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 8, 33, 43, 39, 34, 40, 41, 10, 42, 7, 9, 5, 3, 6, 4, 1], 'cur_cost': 19470.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 21, 18, 16, 15, 20, 12, 10, 41, 40, 34, 39, 43, 33, 42, 37, 38, 36, 35, 29, 27, 28, 32, 22, 23, 25, 31, 26, 24, 30, 49, 46, 53, 44, 54, 47, 48, 50, 52, 45, 51, 11, 14, 13, 17, 19, 8, 7, 9, 5, 3, 6, 4, 1], 'cur_cost': 16421.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 21, 18, 16, 15, 20, 12, 19, 17, 13, 14, 11, 51, 45, 52, 50, 48, 47, 54, 25, 44, 53, 46, 49, 30, 24, 26, 31, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 6, 4, 1], 'cur_cost': 14755.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [29, 28, 41, 23, 27, 38, 25, 31, 26, 35, 36, 6, 37, 42, 1, 34, 40, 22, 46, 13, 7, 9, 51, 44, 2, 4, 12, 18, 17, 52, 15, 53, 19, 45, 47, 49, 32, 21, 16, 50, 20, 48, 5, 14, 33, 30, 10, 24, 54, 11, 3, 8, 39, 0, 43], 'cur_cost': 72811.0, 'intermediate_solutions': [{'tour': [27, 23, 30, 29, 24, 9, 2, 36, 40, 8, 26, 38, 32, 34, 25, 1, 35, 49, 16, 21, 13, 11, 45, 53, 17, 51, 14, 18, 54, 12, 20, 6, 28, 10, 41, 39, 31, 33, 37, 22, 7, 3, 19, 50, 47, 48, 52, 44, 46, 42, 0, 5, 4, 43, 15], 'cur_cost': 58047.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [27, 23, 30, 29, 24, 9, 2, 36, 40, 8, 26, 38, 32, 34, 25, 1, 35, 49, 16, 21, 13, 11, 45, 53, 17, 51, 15, 43, 4, 5, 0, 42, 46, 44, 52, 48, 47, 50, 19, 3, 7, 10, 37, 33, 31, 39, 41, 22, 28, 6, 20, 12, 54, 18, 14], 'cur_cost': 55256.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [27, 23, 30, 29, 18, 24, 9, 2, 36, 40, 8, 26, 38, 32, 34, 25, 1, 35, 49, 16, 21, 13, 11, 45, 53, 17, 51, 14, 54, 12, 20, 6, 28, 22, 41, 39, 31, 33, 37, 10, 7, 3, 19, 50, 47, 48, 52, 44, 46, 42, 0, 5, 4, 43, 15], 'cur_cost': 60085.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [32, 30, 29, 28, 36, 0, 22, 27, 33, 5, 26, 10, 31, 53, 46, 20, 44, 11, 52, 15, 18, 25, 41, 42, 24, 23, 43, 40, 9, 7, 38, 54, 17, 51, 45, 50, 49, 16, 13, 12, 21, 1, 4, 2, 6, 3, 37, 39, 34, 47, 14, 19, 48, 35, 8], 'cur_cost': 55129.0, 'intermediate_solutions': [{'tour': [0, 15, 32, 4, 1, 8, 7, 9, 5, 3, 2, 10, 31, 25, 23, 22, 6, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 18, 20, 12, 21, 19, 11], 'cur_cost': 20321.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 6, 4, 1, 8, 7, 9, 5, 3, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 16, 14, 17, 18, 20, 12, 21, 19, 11], 'cur_cost': 16490.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 6, 4, 1, 8, 7, 9, 5, 3, 2, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 48, 13, 17, 14, 16, 50, 18, 20, 12, 21, 19, 11], 'cur_cost': 18796.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [27, 4, 32, 38, 35, 26, 39, 28, 10, 8, 29, 7, 25, 34, 36, 22, 37, 0, 31, 40, 23, 41, 43, 42, 54, 48, 45, 16, 20, 13, 18, 21, 53, 17, 15, 52, 11, 47, 51, 46, 30, 9, 2, 5, 1, 19, 44, 12, 14, 24, 33, 6, 3, 49, 50], 'cur_cost': 54471.0, 'intermediate_solutions': [{'tour': [0, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 40, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 51, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 21693.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 3, 5, 9, 7, 8, 10, 41, 2, 1, 4, 6], 'cur_cost': 15227.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 32, 2, 1, 4, 6], 'cur_cost': 13660.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:29:02,810 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:02,810 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:02,813 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=18015.000, 多样性=0.971
2025-08-05 10:29:02,814 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 10:29:02,814 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 10:29:02,814 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:02,814 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.2764316519785348, 'best_improvement': -0.6860084230229293}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.010084033613445443}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.003565449121222567, 'recent_improvements': [-0.002821838256659138, 0.08313510282330731, 0.004309059985786012], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 10442, 'new_best_cost': 10442, 'quality_improvement': 0.0, 'old_diversity': 0.9060606060606061, 'new_diversity': 0.9060606060606061, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:29:02,814 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 10:29:02,815 - __main__ - INFO - composite10_55 开始进化第 4 代
2025-08-05 10:29:02,815 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 10:29:02,815 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:02,816 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=18015.000, 多样性=0.971
2025-08-05 10:29:02,816 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:02,818 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.971
2025-08-05 10:29:02,818 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:02,820 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.906
2025-08-05 10:29:02,821 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 10:29:02,822 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:02,822 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 10:29:02,822 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 10:29:02,848 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.214, 适应度梯度: 3391.443, 聚类评分: 0.000, 覆盖率: 0.127, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:02,848 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 10:29:02,848 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:02,848 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite10_55
2025-08-05 10:29:02,855 - visualization.landscape_visualizer - INFO - 插值约束: 70 个点被约束到最小值 10442.00
2025-08-05 10:29:02,856 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=5.8%, 梯度: 3521.18 → 3317.96
2025-08-05 10:29:02,952 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite10_55\landscape_composite10_55_iter_109_20250805_102902.html
2025-08-05 10:29:03,044 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite10_55\dashboard_composite10_55_iter_109_20250805_102902.html
2025-08-05 10:29:03,045 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 109
2025-08-05 10:29:03,045 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 10:29:03,045 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2246秒
2025-08-05 10:29:03,046 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.21428571428571427, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 3391.442857142859, 'local_optima_density': 0.21428571428571427, 'gradient_variance': 1346430400.2524486, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.127, 'fitness_entropy': 0.8576611402529865, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.127)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 3391.443)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360942.8482275, 'performance_metrics': {}}}
2025-08-05 10:29:03,046 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:03,046 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:03,046 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:03,046 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:03,047 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:29:03,047 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 10:29:03,047 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:29:03,047 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:03,047 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:03,047 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:29:03,048 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:03,048 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:03,048 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 5} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:03,048 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:03,048 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:03,049 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:03,051 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 10:29:03,051 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,052 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,052 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,052 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,053 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10798.0, 路径长度: 55, 收集中间解: 3
2025-08-05 10:29:03,053 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 9, 5, 10, 8, 7, 3, 2, 1, 4, 6, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 10798.0, 'intermediate_solutions': [{'tour': [0, 22, 18, 24, 30, 28, 32, 17, 29, 23, 25, 31, 26, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6, 21, 19, 27, 13, 14, 16, 15, 20, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 27785.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 22, 18, 24, 30, 28, 32, 27, 29, 23, 25, 31, 26, 35, 36, 38, 37, 42, 33, 6, 4, 1, 2, 3, 5, 9, 7, 8, 10, 41, 40, 34, 39, 43, 21, 19, 17, 13, 14, 16, 15, 20, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 20898.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 22, 18, 24, 30, 28, 32, 27, 29, 23, 25, 31, 26, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6, 21, 19, 17, 13, 14, 16, 15, 20, 12, 11, 51, 52, 50, 48, 47, 54, 44, 53, 46, 45, 49], 'cur_cost': 18175.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:03,054 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 10798.00)
2025-08-05 10:29:03,054 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:03,054 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:03,054 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:03,056 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 55
2025-08-05 10:29:03,056 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,057 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,057 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,057 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,058 - ExplorationExpert - INFO - 探索路径生成完成，成本: 90661.0, 路径长度: 55, 收集中间解: 3
2025-08-05 10:29:03,058 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [11, 43, 5, 21, 41, 47, 3, 30, 1, 10, 44, 36, 33, 27, 23, 6, 32, 13, 19, 8, 9, 29, 35, 25, 46, 37, 42, 50, 51, 39, 34, 40, 48, 49, 18, 17, 52, 53, 16, 24, 45, 20, 54, 14, 7, 26, 2, 15, 28, 0, 22, 12, 31, 38, 4], 'cur_cost': 90661.0, 'intermediate_solutions': [{'tour': [27, 34, 29, 28, 41, 0, 23, 30, 38, 10, 43, 36, 33, 5, 37, 6, 31, 32, 51, 8, 9, 3, 7, 22, 46, 13, 47, 19, 1, 44, 54, 11, 16, 12, 18, 17, 52, 15, 14, 24, 25, 40, 4, 39, 35, 42, 2, 21, 53, 50, 45, 48, 49, 20, 26], 'cur_cost': 61499.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [27, 34, 29, 28, 41, 0, 23, 30, 38, 10, 43, 36, 33, 5, 37, 6, 31, 32, 1, 8, 9, 3, 7, 22, 46, 13, 47, 19, 51, 44, 54, 11, 16, 12, 18, 17, 52, 15, 14, 24, 25, 40, 4, 39, 35, 42, 2, 50, 53, 21, 45, 48, 49, 20, 26], 'cur_cost': 54606.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [27, 34, 29, 28, 41, 0, 23, 30, 38, 10, 43, 36, 33, 5, 37, 6, 31, 32, 1, 8, 9, 3, 7, 22, 46, 13, 47, 19, 51, 44, 54, 11, 16, 12, 18, 17, 52, 15, 14, 24, 25, 40, 4, 39, 35, 42, 2, 21, 53, 50, 45, 49, 20, 48, 26], 'cur_cost': 54075.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:03,058 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 90661.00)
2025-08-05 10:29:03,058 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-05 10:29:03,058 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:03,058 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:03,059 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 105972.0
2025-08-05 10:29:03,071 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:29:03,072 - ExploitationExpert - INFO - res_population_costs: [10442, 10445, 10448, 10468.0]
2025-08-05 10:29:03,072 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 21, 19, 18, 12, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  9,  7,  2,  3,
        5,  6,  4,  1], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 30, 24, 26, 31, 25, 23, 28, 32, 27, 22, 29,
       35, 33, 36, 38, 41, 40, 34, 39, 37, 42, 43, 10,  8,  2,  3,  7,  9,
        5,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  9,  7,  3,  2,  8, 10, 43, 42, 37, 39, 34, 40,
       41, 38, 36, 33, 35, 29, 31, 26, 25, 23, 22, 27, 32, 28, 24, 30, 49,
       46, 44, 53, 54, 50, 48, 52, 47, 51, 45, 13, 17, 14, 11, 16, 15, 20,
       18, 12, 21, 19], dtype=int64)]
2025-08-05 10:29:03,074 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:03,075 - ExploitationExpert - INFO - populations: [{'tour': [0, 9, 5, 10, 8, 7, 3, 2, 1, 4, 6, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 10798.0}, {'tour': [11, 43, 5, 21, 41, 47, 3, 30, 1, 10, 44, 36, 33, 27, 23, 6, 32, 13, 19, 8, 9, 29, 35, 25, 46, 37, 42, 50, 51, 39, 34, 40, 48, 49, 18, 17, 52, 53, 16, 24, 45, 20, 54, 14, 7, 26, 2, 15, 28, 0, 22, 12, 31, 38, 4], 'cur_cost': 90661.0}, {'tour': array([37, 15, 16, 41, 28, 27, 11, 47, 38, 36, 21,  9, 22, 54, 53,  4, 44,
        1,  0, 13, 48, 12, 18, 17, 35, 46, 43, 30, 45,  3, 26, 19, 51, 23,
       10, 32,  2, 25, 40, 20, 39,  8, 52, 24, 34, 31, 50,  6, 49,  5,  7,
       33, 42, 29, 14], dtype=int64), 'cur_cost': 105972.0}, {'tour': [40, 32, 9, 37, 7, 3, 38, 29, 36, 18, 13, 39, 46, 44, 21, 5, 43, 53, 19, 10, 45, 33, 47, 48, 35, 17, 23, 31, 4, 34, 6, 0, 16, 27, 25, 22, 54, 42, 14, 15, 30, 1, 26, 51, 41, 8, 49, 20, 28, 24, 11, 52, 2, 50, 12], 'cur_cost': 106170.0}, {'tour': [47, 10, 23, 44, 18, 45, 1, 9, 3, 4, 8, 14, 22, 25, 51, 12, 53, 2, 24, 15, 33, 43, 17, 38, 34, 19, 48, 50, 52, 21, 39, 5, 42, 20, 16, 36, 49, 6, 40, 37, 54, 35, 11, 41, 7, 26, 31, 46, 28, 0, 13, 32, 30, 27, 29], 'cur_cost': 98270.0}, {'tour': [0, 15, 7, 23, 22, 32, 28, 24, 30, 26, 31, 25, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 1, 4, 6, 5, 9, 3, 2, 21, 19, 17, 13, 14, 16, 18, 20, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 18958.0}, {'tour': [29, 28, 30, 23, 38, 31, 26, 35, 6, 42, 33, 8, 34, 40, 41, 10, 13, 7, 5, 3, 2, 1, 16, 12, 18, 17, 52, 15, 53, 27, 25, 45, 48, 36, 21, 20, 50, 0, 14, 19, 22, 24, 51, 11, 39, 49, 43, 46, 54, 37, 47, 9, 44, 32, 4], 'cur_cost': 79464.0}, {'tour': [29, 28, 41, 23, 27, 38, 25, 31, 26, 35, 36, 6, 37, 42, 1, 34, 40, 22, 46, 13, 7, 9, 51, 44, 2, 4, 12, 18, 17, 52, 15, 53, 19, 45, 47, 49, 32, 21, 16, 50, 20, 48, 5, 14, 33, 30, 10, 24, 54, 11, 3, 8, 39, 0, 43], 'cur_cost': 72811.0}, {'tour': [32, 30, 29, 28, 36, 0, 22, 27, 33, 5, 26, 10, 31, 53, 46, 20, 44, 11, 52, 15, 18, 25, 41, 42, 24, 23, 43, 40, 9, 7, 38, 54, 17, 51, 45, 50, 49, 16, 13, 12, 21, 1, 4, 2, 6, 3, 37, 39, 34, 47, 14, 19, 48, 35, 8], 'cur_cost': 55129.0}, {'tour': [27, 4, 32, 38, 35, 26, 39, 28, 10, 8, 29, 7, 25, 34, 36, 22, 37, 0, 31, 40, 23, 41, 43, 42, 54, 48, 45, 16, 20, 13, 18, 21, 53, 17, 15, 52, 11, 47, 51, 46, 30, 9, 2, 5, 1, 19, 44, 12, 14, 24, 33, 6, 3, 49, 50], 'cur_cost': 54471.0}]
2025-08-05 10:29:03,076 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:03,076 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 281, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 281, 'cache_hits': 0, 'similarity_calculations': 1384, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:03,077 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([37, 15, 16, 41, 28, 27, 11, 47, 38, 36, 21,  9, 22, 54, 53,  4, 44,
        1,  0, 13, 48, 12, 18, 17, 35, 46, 43, 30, 45,  3, 26, 19, 51, 23,
       10, 32,  2, 25, 40, 20, 39,  8, 52, 24, 34, 31, 50,  6, 49,  5,  7,
       33, 42, 29, 14], dtype=int64), 'cur_cost': 105972.0, 'intermediate_solutions': [{'tour': array([23, 43, 11, 21, 14, 47, 28, 37, 40, 39, 44, 20, 22, 36,  4,  1,  9,
       13, 52, 31, 24, 19, 17, 25, 12, 50, 16, 46, 26, 45,  3,  5, 27, 48,
        2, 42, 33,  0,  6, 34, 18, 35, 54,  8, 10, 41, 38, 15, 53, 49, 32,
       29,  7, 51, 30]), 'cur_cost': 85672.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([21, 23, 43, 11, 14, 47, 28, 37, 40, 39, 44, 20, 22, 36,  4,  1,  9,
       13, 52, 31, 24, 19, 17, 25, 12, 50, 16, 46, 26, 45,  3,  5, 27, 48,
        2, 42, 33,  0,  6, 34, 18, 35, 54,  8, 10, 41, 38, 15, 53, 49, 32,
       29,  7, 51, 30]), 'cur_cost': 90370.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14, 21, 23, 43, 11, 47, 28, 37, 40, 39, 44, 20, 22, 36,  4,  1,  9,
       13, 52, 31, 24, 19, 17, 25, 12, 50, 16, 46, 26, 45,  3,  5, 27, 48,
        2, 42, 33,  0,  6, 34, 18, 35, 54,  8, 10, 41, 38, 15, 53, 49, 32,
       29,  7, 51, 30]), 'cur_cost': 90425.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([11, 21, 23, 43, 14, 47, 28, 37, 40, 39, 44, 20, 22, 36,  4,  1,  9,
       13, 52, 31, 24, 19, 17, 25, 12, 50, 16, 46, 26, 45,  3,  5, 27, 48,
        2, 42, 33,  0,  6, 34, 18, 35, 54,  8, 10, 41, 38, 15, 53, 49, 32,
       29,  7, 51, 30]), 'cur_cost': 90453.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([11, 14, 21, 23, 43, 47, 28, 37, 40, 39, 44, 20, 22, 36,  4,  1,  9,
       13, 52, 31, 24, 19, 17, 25, 12, 50, 16, 46, 26, 45,  3,  5, 27, 48,
        2, 42, 33,  0,  6, 34, 18, 35, 54,  8, 10, 41, 38, 15, 53, 49, 32,
       29,  7, 51, 30]), 'cur_cost': 88615.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:03,078 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 105972.00)
2025-08-05 10:29:03,078 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:29:03,078 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:03,078 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:03,079 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 103058.0
2025-08-05 10:29:03,087 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:29:03,088 - ExploitationExpert - INFO - res_population_costs: [10442, 10445, 10448, 10468.0]
2025-08-05 10:29:03,088 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 21, 19, 18, 12, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  9,  7,  2,  3,
        5,  6,  4,  1], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 30, 24, 26, 31, 25, 23, 28, 32, 27, 22, 29,
       35, 33, 36, 38, 41, 40, 34, 39, 37, 42, 43, 10,  8,  2,  3,  7,  9,
        5,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  9,  7,  3,  2,  8, 10, 43, 42, 37, 39, 34, 40,
       41, 38, 36, 33, 35, 29, 31, 26, 25, 23, 22, 27, 32, 28, 24, 30, 49,
       46, 44, 53, 54, 50, 48, 52, 47, 51, 45, 13, 17, 14, 11, 16, 15, 20,
       18, 12, 21, 19], dtype=int64)]
2025-08-05 10:29:03,089 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:03,090 - ExploitationExpert - INFO - populations: [{'tour': [0, 9, 5, 10, 8, 7, 3, 2, 1, 4, 6, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 10798.0}, {'tour': [11, 43, 5, 21, 41, 47, 3, 30, 1, 10, 44, 36, 33, 27, 23, 6, 32, 13, 19, 8, 9, 29, 35, 25, 46, 37, 42, 50, 51, 39, 34, 40, 48, 49, 18, 17, 52, 53, 16, 24, 45, 20, 54, 14, 7, 26, 2, 15, 28, 0, 22, 12, 31, 38, 4], 'cur_cost': 90661.0}, {'tour': array([37, 15, 16, 41, 28, 27, 11, 47, 38, 36, 21,  9, 22, 54, 53,  4, 44,
        1,  0, 13, 48, 12, 18, 17, 35, 46, 43, 30, 45,  3, 26, 19, 51, 23,
       10, 32,  2, 25, 40, 20, 39,  8, 52, 24, 34, 31, 50,  6, 49,  5,  7,
       33, 42, 29, 14], dtype=int64), 'cur_cost': 105972.0}, {'tour': array([ 4, 47, 19, 22, 40, 21, 26, 41, 27, 42,  3, 35, 34, 38,  7, 20,  1,
       45, 48, 25, 37, 54, 14, 33, 12, 15,  0, 17, 11, 23,  2, 30, 44, 28,
        8, 31, 52, 51, 10,  9, 46, 13, 49, 18, 36, 53, 24, 29, 32, 43,  6,
       16,  5, 50, 39], dtype=int64), 'cur_cost': 103058.0}, {'tour': [47, 10, 23, 44, 18, 45, 1, 9, 3, 4, 8, 14, 22, 25, 51, 12, 53, 2, 24, 15, 33, 43, 17, 38, 34, 19, 48, 50, 52, 21, 39, 5, 42, 20, 16, 36, 49, 6, 40, 37, 54, 35, 11, 41, 7, 26, 31, 46, 28, 0, 13, 32, 30, 27, 29], 'cur_cost': 98270.0}, {'tour': [0, 15, 7, 23, 22, 32, 28, 24, 30, 26, 31, 25, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 1, 4, 6, 5, 9, 3, 2, 21, 19, 17, 13, 14, 16, 18, 20, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 18958.0}, {'tour': [29, 28, 30, 23, 38, 31, 26, 35, 6, 42, 33, 8, 34, 40, 41, 10, 13, 7, 5, 3, 2, 1, 16, 12, 18, 17, 52, 15, 53, 27, 25, 45, 48, 36, 21, 20, 50, 0, 14, 19, 22, 24, 51, 11, 39, 49, 43, 46, 54, 37, 47, 9, 44, 32, 4], 'cur_cost': 79464.0}, {'tour': [29, 28, 41, 23, 27, 38, 25, 31, 26, 35, 36, 6, 37, 42, 1, 34, 40, 22, 46, 13, 7, 9, 51, 44, 2, 4, 12, 18, 17, 52, 15, 53, 19, 45, 47, 49, 32, 21, 16, 50, 20, 48, 5, 14, 33, 30, 10, 24, 54, 11, 3, 8, 39, 0, 43], 'cur_cost': 72811.0}, {'tour': [32, 30, 29, 28, 36, 0, 22, 27, 33, 5, 26, 10, 31, 53, 46, 20, 44, 11, 52, 15, 18, 25, 41, 42, 24, 23, 43, 40, 9, 7, 38, 54, 17, 51, 45, 50, 49, 16, 13, 12, 21, 1, 4, 2, 6, 3, 37, 39, 34, 47, 14, 19, 48, 35, 8], 'cur_cost': 55129.0}, {'tour': [27, 4, 32, 38, 35, 26, 39, 28, 10, 8, 29, 7, 25, 34, 36, 22, 37, 0, 31, 40, 23, 41, 43, 42, 54, 48, 45, 16, 20, 13, 18, 21, 53, 17, 15, 52, 11, 47, 51, 46, 30, 9, 2, 5, 1, 19, 44, 12, 14, 24, 33, 6, 3, 49, 50], 'cur_cost': 54471.0}]
2025-08-05 10:29:03,091 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:03,091 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 282, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 282, 'cache_hits': 0, 'similarity_calculations': 1392, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:03,091 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 4, 47, 19, 22, 40, 21, 26, 41, 27, 42,  3, 35, 34, 38,  7, 20,  1,
       45, 48, 25, 37, 54, 14, 33, 12, 15,  0, 17, 11, 23,  2, 30, 44, 28,
        8, 31, 52, 51, 10,  9, 46, 13, 49, 18, 36, 53, 24, 29, 32, 43,  6,
       16,  5, 50, 39], dtype=int64), 'cur_cost': 103058.0, 'intermediate_solutions': [{'tour': array([ 9, 32, 40, 37,  7,  3, 38, 29, 36, 18, 13, 39, 46, 44, 21,  5, 43,
       53, 19, 10, 45, 33, 47, 48, 35, 17, 23, 31,  4, 34,  6,  0, 16, 27,
       25, 22, 54, 42, 14, 15, 30,  1, 26, 51, 41,  8, 49, 20, 28, 24, 11,
       52,  2, 50, 12]), 'cur_cost': 103237.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([37,  9, 32, 40,  7,  3, 38, 29, 36, 18, 13, 39, 46, 44, 21,  5, 43,
       53, 19, 10, 45, 33, 47, 48, 35, 17, 23, 31,  4, 34,  6,  0, 16, 27,
       25, 22, 54, 42, 14, 15, 30,  1, 26, 51, 41,  8, 49, 20, 28, 24, 11,
       52,  2, 50, 12]), 'cur_cost': 106176.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 7, 37,  9, 32, 40,  3, 38, 29, 36, 18, 13, 39, 46, 44, 21,  5, 43,
       53, 19, 10, 45, 33, 47, 48, 35, 17, 23, 31,  4, 34,  6,  0, 16, 27,
       25, 22, 54, 42, 14, 15, 30,  1, 26, 51, 41,  8, 49, 20, 28, 24, 11,
       52,  2, 50, 12]), 'cur_cost': 107759.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([40, 37,  9, 32,  7,  3, 38, 29, 36, 18, 13, 39, 46, 44, 21,  5, 43,
       53, 19, 10, 45, 33, 47, 48, 35, 17, 23, 31,  4, 34,  6,  0, 16, 27,
       25, 22, 54, 42, 14, 15, 30,  1, 26, 51, 41,  8, 49, 20, 28, 24, 11,
       52,  2, 50, 12]), 'cur_cost': 104242.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([40,  7, 37,  9, 32,  3, 38, 29, 36, 18, 13, 39, 46, 44, 21,  5, 43,
       53, 19, 10, 45, 33, 47, 48, 35, 17, 23, 31,  4, 34,  6,  0, 16, 27,
       25, 22, 54, 42, 14, 15, 30,  1, 26, 51, 41,  8, 49, 20, 28, 24, 11,
       52,  2, 50, 12]), 'cur_cost': 108746.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:03,092 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 103058.00)
2025-08-05 10:29:03,092 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:29:03,092 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:03,092 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:03,092 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 103819.0
2025-08-05 10:29:03,100 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:29:03,100 - ExploitationExpert - INFO - res_population_costs: [10442, 10445, 10448, 10468.0]
2025-08-05 10:29:03,100 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 21, 19, 18, 12, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  9,  7,  2,  3,
        5,  6,  4,  1], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 30, 24, 26, 31, 25, 23, 28, 32, 27, 22, 29,
       35, 33, 36, 38, 41, 40, 34, 39, 37, 42, 43, 10,  8,  2,  3,  7,  9,
        5,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  9,  7,  3,  2,  8, 10, 43, 42, 37, 39, 34, 40,
       41, 38, 36, 33, 35, 29, 31, 26, 25, 23, 22, 27, 32, 28, 24, 30, 49,
       46, 44, 53, 54, 50, 48, 52, 47, 51, 45, 13, 17, 14, 11, 16, 15, 20,
       18, 12, 21, 19], dtype=int64)]
2025-08-05 10:29:03,101 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:03,102 - ExploitationExpert - INFO - populations: [{'tour': [0, 9, 5, 10, 8, 7, 3, 2, 1, 4, 6, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 10798.0}, {'tour': [11, 43, 5, 21, 41, 47, 3, 30, 1, 10, 44, 36, 33, 27, 23, 6, 32, 13, 19, 8, 9, 29, 35, 25, 46, 37, 42, 50, 51, 39, 34, 40, 48, 49, 18, 17, 52, 53, 16, 24, 45, 20, 54, 14, 7, 26, 2, 15, 28, 0, 22, 12, 31, 38, 4], 'cur_cost': 90661.0}, {'tour': array([37, 15, 16, 41, 28, 27, 11, 47, 38, 36, 21,  9, 22, 54, 53,  4, 44,
        1,  0, 13, 48, 12, 18, 17, 35, 46, 43, 30, 45,  3, 26, 19, 51, 23,
       10, 32,  2, 25, 40, 20, 39,  8, 52, 24, 34, 31, 50,  6, 49,  5,  7,
       33, 42, 29, 14], dtype=int64), 'cur_cost': 105972.0}, {'tour': array([ 4, 47, 19, 22, 40, 21, 26, 41, 27, 42,  3, 35, 34, 38,  7, 20,  1,
       45, 48, 25, 37, 54, 14, 33, 12, 15,  0, 17, 11, 23,  2, 30, 44, 28,
        8, 31, 52, 51, 10,  9, 46, 13, 49, 18, 36, 53, 24, 29, 32, 43,  6,
       16,  5, 50, 39], dtype=int64), 'cur_cost': 103058.0}, {'tour': array([38, 12,  7,  3, 51, 29,  6, 19, 16, 15, 14, 20, 39, 54, 27, 11, 10,
       17, 30,  8, 28, 35,  2, 26, 37, 45, 49,  5, 36, 21, 48, 13, 33, 41,
        0, 42, 44, 40, 53, 18, 46, 32, 52, 22, 23, 50,  1, 25,  9, 34, 47,
        4, 43, 24, 31], dtype=int64), 'cur_cost': 103819.0}, {'tour': [0, 15, 7, 23, 22, 32, 28, 24, 30, 26, 31, 25, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 1, 4, 6, 5, 9, 3, 2, 21, 19, 17, 13, 14, 16, 18, 20, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 18958.0}, {'tour': [29, 28, 30, 23, 38, 31, 26, 35, 6, 42, 33, 8, 34, 40, 41, 10, 13, 7, 5, 3, 2, 1, 16, 12, 18, 17, 52, 15, 53, 27, 25, 45, 48, 36, 21, 20, 50, 0, 14, 19, 22, 24, 51, 11, 39, 49, 43, 46, 54, 37, 47, 9, 44, 32, 4], 'cur_cost': 79464.0}, {'tour': [29, 28, 41, 23, 27, 38, 25, 31, 26, 35, 36, 6, 37, 42, 1, 34, 40, 22, 46, 13, 7, 9, 51, 44, 2, 4, 12, 18, 17, 52, 15, 53, 19, 45, 47, 49, 32, 21, 16, 50, 20, 48, 5, 14, 33, 30, 10, 24, 54, 11, 3, 8, 39, 0, 43], 'cur_cost': 72811.0}, {'tour': [32, 30, 29, 28, 36, 0, 22, 27, 33, 5, 26, 10, 31, 53, 46, 20, 44, 11, 52, 15, 18, 25, 41, 42, 24, 23, 43, 40, 9, 7, 38, 54, 17, 51, 45, 50, 49, 16, 13, 12, 21, 1, 4, 2, 6, 3, 37, 39, 34, 47, 14, 19, 48, 35, 8], 'cur_cost': 55129.0}, {'tour': [27, 4, 32, 38, 35, 26, 39, 28, 10, 8, 29, 7, 25, 34, 36, 22, 37, 0, 31, 40, 23, 41, 43, 42, 54, 48, 45, 16, 20, 13, 18, 21, 53, 17, 15, 52, 11, 47, 51, 46, 30, 9, 2, 5, 1, 19, 44, 12, 14, 24, 33, 6, 3, 49, 50], 'cur_cost': 54471.0}]
2025-08-05 10:29:03,103 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:03,103 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 283, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 283, 'cache_hits': 0, 'similarity_calculations': 1401, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:03,104 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([38, 12,  7,  3, 51, 29,  6, 19, 16, 15, 14, 20, 39, 54, 27, 11, 10,
       17, 30,  8, 28, 35,  2, 26, 37, 45, 49,  5, 36, 21, 48, 13, 33, 41,
        0, 42, 44, 40, 53, 18, 46, 32, 52, 22, 23, 50,  1, 25,  9, 34, 47,
        4, 43, 24, 31], dtype=int64), 'cur_cost': 103819.0, 'intermediate_solutions': [{'tour': array([23, 10, 47, 44, 18, 45,  1,  9,  3,  4,  8, 14, 22, 25, 51, 12, 53,
        2, 24, 15, 33, 43, 17, 38, 34, 19, 48, 50, 52, 21, 39,  5, 42, 20,
       16, 36, 49,  6, 40, 37, 54, 35, 11, 41,  7, 26, 31, 46, 28,  0, 13,
       32, 30, 27, 29]), 'cur_cost': 94269.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([44, 23, 10, 47, 18, 45,  1,  9,  3,  4,  8, 14, 22, 25, 51, 12, 53,
        2, 24, 15, 33, 43, 17, 38, 34, 19, 48, 50, 52, 21, 39,  5, 42, 20,
       16, 36, 49,  6, 40, 37, 54, 35, 11, 41,  7, 26, 31, 46, 28,  0, 13,
       32, 30, 27, 29]), 'cur_cost': 98210.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([18, 44, 23, 10, 47, 45,  1,  9,  3,  4,  8, 14, 22, 25, 51, 12, 53,
        2, 24, 15, 33, 43, 17, 38, 34, 19, 48, 50, 52, 21, 39,  5, 42, 20,
       16, 36, 49,  6, 40, 37, 54, 35, 11, 41,  7, 26, 31, 46, 28,  0, 13,
       32, 30, 27, 29]), 'cur_cost': 97602.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([47, 44, 23, 10, 18, 45,  1,  9,  3,  4,  8, 14, 22, 25, 51, 12, 53,
        2, 24, 15, 33, 43, 17, 38, 34, 19, 48, 50, 52, 21, 39,  5, 42, 20,
       16, 36, 49,  6, 40, 37, 54, 35, 11, 41,  7, 26, 31, 46, 28,  0, 13,
       32, 30, 27, 29]), 'cur_cost': 97017.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([47, 18, 44, 23, 10, 45,  1,  9,  3,  4,  8, 14, 22, 25, 51, 12, 53,
        2, 24, 15, 33, 43, 17, 38, 34, 19, 48, 50, 52, 21, 39,  5, 42, 20,
       16, 36, 49,  6, 40, 37, 54, 35, 11, 41,  7, 26, 31, 46, 28,  0, 13,
       32, 30, 27, 29]), 'cur_cost': 98299.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:03,104 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 103819.00)
2025-08-05 10:29:03,104 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:29:03,104 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:29:03,104 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:03,106 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 55
2025-08-05 10:29:03,106 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,107 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,107 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,107 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,107 - ExplorationExpert - INFO - 探索路径生成完成，成本: 89978.0, 路径长度: 55, 收集中间解: 3
2025-08-05 10:29:03,107 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [9, 10, 7, 2, 4, 26, 33, 25, 6, 13, 19, 30, 36, 46, 50, 43, 5, 45, 41, 44, 54, 47, 24, 52, 17, 14, 20, 40, 31, 38, 35, 51, 48, 28, 27, 0, 23, 18, 1, 39, 53, 21, 3, 29, 22, 49, 8, 12, 11, 37, 15, 34, 42, 16, 32], 'cur_cost': 89978.0, 'intermediate_solutions': [{'tour': [0, 8, 7, 23, 22, 32, 28, 24, 30, 26, 31, 25, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 15, 1, 4, 6, 5, 9, 3, 2, 21, 19, 17, 13, 14, 16, 18, 20, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 18986.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 7, 23, 22, 32, 28, 24, 30, 26, 31, 25, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 14, 13, 17, 19, 21, 2, 3, 9, 5, 6, 4, 1, 16, 18, 20, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 24739.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 7, 23, 22, 32, 28, 24, 30, 26, 31, 25, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 52, 1, 4, 6, 5, 9, 3, 2, 21, 19, 17, 13, 14, 16, 18, 20, 12, 11, 51, 45, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 24969.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:03,107 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 89978.00)
2025-08-05 10:29:03,108 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:03,108 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:03,108 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:03,113 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 55
2025-08-05 10:29:03,113 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,113 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,113 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,113 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,114 - ExplorationExpert - INFO - 探索路径生成完成，成本: 59025.0, 路径长度: 55, 收集中间解: 3
2025-08-05 10:29:03,114 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [40, 32, 36, 34, 4, 37, 39, 7, 3, 42, 9, 23, 30, 28, 38, 46, 47, 49, 50, 16, 13, 24, 5, 22, 2, 8, 29, 1, 31, 10, 0, 21, 14, 53, 17, 20, 48, 18, 12, 52, 45, 51, 44, 54, 15, 27, 25, 26, 43, 41, 35, 6, 33, 19, 11], 'cur_cost': 59025.0, 'intermediate_solutions': [{'tour': [29, 28, 30, 23, 38, 31, 26, 0, 6, 42, 33, 8, 34, 40, 41, 10, 13, 7, 5, 3, 2, 1, 16, 12, 18, 17, 52, 15, 53, 27, 25, 45, 48, 36, 21, 20, 50, 35, 14, 19, 22, 24, 51, 11, 39, 49, 43, 46, 54, 37, 47, 9, 44, 32, 4], 'cur_cost': 77917.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [29, 52, 17, 18, 12, 16, 1, 2, 3, 5, 7, 13, 10, 41, 40, 34, 8, 33, 42, 6, 35, 26, 31, 38, 23, 30, 28, 15, 53, 27, 25, 45, 48, 36, 21, 20, 50, 0, 14, 19, 22, 24, 51, 11, 39, 49, 43, 46, 54, 37, 47, 9, 44, 32, 4], 'cur_cost': 82757.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [29, 3, 28, 30, 23, 38, 31, 26, 35, 6, 42, 33, 8, 34, 40, 41, 10, 13, 7, 5, 2, 1, 16, 12, 18, 17, 52, 15, 53, 27, 25, 45, 48, 36, 21, 20, 50, 0, 14, 19, 22, 24, 51, 11, 39, 49, 43, 46, 54, 37, 47, 9, 44, 32, 4], 'cur_cost': 82367.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:03,114 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 59025.00)
2025-08-05 10:29:03,114 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:03,114 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:03,114 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:03,116 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 10:29:03,116 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,116 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,116 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,117 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,117 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16550.0, 路径长度: 55, 收集中间解: 3
2025-08-05 10:29:03,117 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 3, 19, 8, 7, 9, 5, 6, 4, 1, 10, 2, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 11], 'cur_cost': 16550.0, 'intermediate_solutions': [{'tour': [29, 28, 41, 23, 27, 38, 14, 31, 26, 35, 36, 6, 37, 42, 1, 34, 40, 22, 46, 13, 7, 9, 51, 44, 2, 4, 12, 18, 17, 52, 15, 53, 19, 45, 47, 49, 32, 21, 16, 50, 20, 48, 5, 25, 33, 30, 10, 24, 54, 11, 3, 8, 39, 0, 43], 'cur_cost': 73813.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [29, 28, 41, 23, 27, 38, 25, 31, 26, 35, 36, 6, 37, 42, 1, 34, 40, 22, 46, 13, 7, 9, 51, 44, 2, 4, 12, 18, 17, 52, 15, 53, 19, 45, 47, 49, 32, 21, 16, 50, 20, 48, 5, 14, 33, 30, 10, 24, 0, 39, 8, 3, 11, 54, 43], 'cur_cost': 72857.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [29, 28, 41, 23, 27, 38, 25, 31, 26, 35, 36, 6, 37, 42, 1, 34, 40, 22, 46, 13, 7, 9, 51, 44, 2, 4, 12, 18, 17, 52, 15, 53, 19, 45, 24, 47, 49, 32, 21, 16, 50, 20, 48, 5, 14, 33, 30, 10, 54, 11, 3, 8, 39, 0, 43], 'cur_cost': 76440.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:03,117 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 16550.00)
2025-08-05 10:29:03,117 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:29:03,117 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:29:03,117 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:03,119 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 10:29:03,119 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,120 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,120 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,120 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,120 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10699.0, 路径长度: 55, 收集中间解: 3
2025-08-05 10:29:03,120 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 1, 6, 5, 9, 7, 3, 2, 8, 4, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 10699.0, 'intermediate_solutions': [{'tour': [32, 30, 29, 28, 10, 0, 22, 27, 33, 5, 26, 36, 31, 53, 46, 20, 44, 11, 52, 15, 18, 25, 41, 42, 24, 23, 43, 40, 9, 7, 38, 54, 17, 51, 45, 50, 49, 16, 13, 12, 21, 1, 4, 2, 6, 3, 37, 39, 34, 47, 14, 19, 48, 35, 8], 'cur_cost': 52875.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [32, 30, 29, 28, 36, 0, 22, 27, 33, 5, 26, 10, 31, 53, 46, 20, 44, 11, 52, 15, 18, 25, 41, 42, 24, 23, 43, 40, 9, 7, 38, 54, 17, 51, 45, 50, 49, 16, 13, 12, 21, 1, 4, 2, 6, 3, 37, 39, 34, 47, 19, 14, 48, 35, 8], 'cur_cost': 55128.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [32, 30, 29, 28, 36, 0, 22, 27, 33, 5, 26, 38, 10, 31, 53, 46, 20, 44, 11, 52, 15, 18, 25, 41, 42, 24, 23, 43, 40, 9, 7, 54, 17, 51, 45, 50, 49, 16, 13, 12, 21, 1, 4, 2, 6, 3, 37, 39, 34, 47, 14, 19, 48, 35, 8], 'cur_cost': 55108.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:03,121 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 10699.00)
2025-08-05 10:29:03,121 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:29:03,121 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:29:03,121 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:03,122 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 55
2025-08-05 10:29:03,123 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,123 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,123 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,123 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,123 - ExplorationExpert - INFO - 探索路径生成完成，成本: 103980.0, 路径长度: 55, 收集中间解: 3
2025-08-05 10:29:03,124 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [11, 36, 8, 47, 30, 10, 44, 31, 25, 13, 19, 16, 50, 51, 34, 40, 18, 17, 54, 24, 29, 27, 4, 37, 28, 0, 45, 5, 21, 42, 52, 3, 14, 41, 1, 48, 35, 15, 23, 6, 7, 38, 33, 39, 26, 43, 20, 53, 46, 22, 2, 12, 32, 49, 9], 'cur_cost': 103980.0, 'intermediate_solutions': [{'tour': [27, 4, 32, 38, 35, 26, 44, 28, 10, 8, 29, 7, 25, 34, 36, 22, 37, 0, 31, 40, 23, 41, 43, 42, 54, 48, 45, 16, 20, 13, 18, 21, 53, 17, 15, 52, 11, 47, 51, 46, 30, 9, 2, 5, 1, 19, 39, 12, 14, 24, 33, 6, 3, 49, 50], 'cur_cost': 60736.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [27, 4, 32, 38, 35, 26, 39, 28, 10, 8, 29, 7, 25, 34, 36, 22, 37, 0, 31, 40, 23, 41, 43, 42, 54, 48, 45, 16, 20, 13, 18, 21, 53, 17, 15, 52, 11, 47, 51, 46, 30, 9, 50, 49, 3, 6, 33, 24, 14, 12, 44, 19, 1, 5, 2], 'cur_cost': 56952.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [27, 4, 32, 38, 35, 26, 39, 28, 10, 8, 1, 29, 7, 25, 34, 36, 22, 37, 0, 31, 40, 23, 41, 43, 42, 54, 48, 45, 16, 20, 13, 18, 21, 53, 17, 15, 52, 11, 47, 51, 46, 30, 9, 2, 5, 19, 44, 12, 14, 24, 33, 6, 3, 49, 50], 'cur_cost': 54430.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:03,124 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 103980.00)
2025-08-05 10:29:03,124 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:03,124 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:03,126 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 5, 10, 8, 7, 3, 2, 1, 4, 6, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 10798.0, 'intermediate_solutions': [{'tour': [0, 22, 18, 24, 30, 28, 32, 17, 29, 23, 25, 31, 26, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6, 21, 19, 27, 13, 14, 16, 15, 20, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 27785.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 22, 18, 24, 30, 28, 32, 27, 29, 23, 25, 31, 26, 35, 36, 38, 37, 42, 33, 6, 4, 1, 2, 3, 5, 9, 7, 8, 10, 41, 40, 34, 39, 43, 21, 19, 17, 13, 14, 16, 15, 20, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 20898.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 22, 18, 24, 30, 28, 32, 27, 29, 23, 25, 31, 26, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6, 21, 19, 17, 13, 14, 16, 15, 20, 12, 11, 51, 52, 50, 48, 47, 54, 44, 53, 46, 45, 49], 'cur_cost': 18175.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [11, 43, 5, 21, 41, 47, 3, 30, 1, 10, 44, 36, 33, 27, 23, 6, 32, 13, 19, 8, 9, 29, 35, 25, 46, 37, 42, 50, 51, 39, 34, 40, 48, 49, 18, 17, 52, 53, 16, 24, 45, 20, 54, 14, 7, 26, 2, 15, 28, 0, 22, 12, 31, 38, 4], 'cur_cost': 90661.0, 'intermediate_solutions': [{'tour': [27, 34, 29, 28, 41, 0, 23, 30, 38, 10, 43, 36, 33, 5, 37, 6, 31, 32, 51, 8, 9, 3, 7, 22, 46, 13, 47, 19, 1, 44, 54, 11, 16, 12, 18, 17, 52, 15, 14, 24, 25, 40, 4, 39, 35, 42, 2, 21, 53, 50, 45, 48, 49, 20, 26], 'cur_cost': 61499.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [27, 34, 29, 28, 41, 0, 23, 30, 38, 10, 43, 36, 33, 5, 37, 6, 31, 32, 1, 8, 9, 3, 7, 22, 46, 13, 47, 19, 51, 44, 54, 11, 16, 12, 18, 17, 52, 15, 14, 24, 25, 40, 4, 39, 35, 42, 2, 50, 53, 21, 45, 48, 49, 20, 26], 'cur_cost': 54606.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [27, 34, 29, 28, 41, 0, 23, 30, 38, 10, 43, 36, 33, 5, 37, 6, 31, 32, 1, 8, 9, 3, 7, 22, 46, 13, 47, 19, 51, 44, 54, 11, 16, 12, 18, 17, 52, 15, 14, 24, 25, 40, 4, 39, 35, 42, 2, 21, 53, 50, 45, 49, 20, 48, 26], 'cur_cost': 54075.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([37, 15, 16, 41, 28, 27, 11, 47, 38, 36, 21,  9, 22, 54, 53,  4, 44,
        1,  0, 13, 48, 12, 18, 17, 35, 46, 43, 30, 45,  3, 26, 19, 51, 23,
       10, 32,  2, 25, 40, 20, 39,  8, 52, 24, 34, 31, 50,  6, 49,  5,  7,
       33, 42, 29, 14], dtype=int64), 'cur_cost': 105972.0, 'intermediate_solutions': [{'tour': array([23, 43, 11, 21, 14, 47, 28, 37, 40, 39, 44, 20, 22, 36,  4,  1,  9,
       13, 52, 31, 24, 19, 17, 25, 12, 50, 16, 46, 26, 45,  3,  5, 27, 48,
        2, 42, 33,  0,  6, 34, 18, 35, 54,  8, 10, 41, 38, 15, 53, 49, 32,
       29,  7, 51, 30]), 'cur_cost': 85672.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([21, 23, 43, 11, 14, 47, 28, 37, 40, 39, 44, 20, 22, 36,  4,  1,  9,
       13, 52, 31, 24, 19, 17, 25, 12, 50, 16, 46, 26, 45,  3,  5, 27, 48,
        2, 42, 33,  0,  6, 34, 18, 35, 54,  8, 10, 41, 38, 15, 53, 49, 32,
       29,  7, 51, 30]), 'cur_cost': 90370.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14, 21, 23, 43, 11, 47, 28, 37, 40, 39, 44, 20, 22, 36,  4,  1,  9,
       13, 52, 31, 24, 19, 17, 25, 12, 50, 16, 46, 26, 45,  3,  5, 27, 48,
        2, 42, 33,  0,  6, 34, 18, 35, 54,  8, 10, 41, 38, 15, 53, 49, 32,
       29,  7, 51, 30]), 'cur_cost': 90425.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([11, 21, 23, 43, 14, 47, 28, 37, 40, 39, 44, 20, 22, 36,  4,  1,  9,
       13, 52, 31, 24, 19, 17, 25, 12, 50, 16, 46, 26, 45,  3,  5, 27, 48,
        2, 42, 33,  0,  6, 34, 18, 35, 54,  8, 10, 41, 38, 15, 53, 49, 32,
       29,  7, 51, 30]), 'cur_cost': 90453.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([11, 14, 21, 23, 43, 47, 28, 37, 40, 39, 44, 20, 22, 36,  4,  1,  9,
       13, 52, 31, 24, 19, 17, 25, 12, 50, 16, 46, 26, 45,  3,  5, 27, 48,
        2, 42, 33,  0,  6, 34, 18, 35, 54,  8, 10, 41, 38, 15, 53, 49, 32,
       29,  7, 51, 30]), 'cur_cost': 88615.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 4, 47, 19, 22, 40, 21, 26, 41, 27, 42,  3, 35, 34, 38,  7, 20,  1,
       45, 48, 25, 37, 54, 14, 33, 12, 15,  0, 17, 11, 23,  2, 30, 44, 28,
        8, 31, 52, 51, 10,  9, 46, 13, 49, 18, 36, 53, 24, 29, 32, 43,  6,
       16,  5, 50, 39], dtype=int64), 'cur_cost': 103058.0, 'intermediate_solutions': [{'tour': array([ 9, 32, 40, 37,  7,  3, 38, 29, 36, 18, 13, 39, 46, 44, 21,  5, 43,
       53, 19, 10, 45, 33, 47, 48, 35, 17, 23, 31,  4, 34,  6,  0, 16, 27,
       25, 22, 54, 42, 14, 15, 30,  1, 26, 51, 41,  8, 49, 20, 28, 24, 11,
       52,  2, 50, 12]), 'cur_cost': 103237.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([37,  9, 32, 40,  7,  3, 38, 29, 36, 18, 13, 39, 46, 44, 21,  5, 43,
       53, 19, 10, 45, 33, 47, 48, 35, 17, 23, 31,  4, 34,  6,  0, 16, 27,
       25, 22, 54, 42, 14, 15, 30,  1, 26, 51, 41,  8, 49, 20, 28, 24, 11,
       52,  2, 50, 12]), 'cur_cost': 106176.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 7, 37,  9, 32, 40,  3, 38, 29, 36, 18, 13, 39, 46, 44, 21,  5, 43,
       53, 19, 10, 45, 33, 47, 48, 35, 17, 23, 31,  4, 34,  6,  0, 16, 27,
       25, 22, 54, 42, 14, 15, 30,  1, 26, 51, 41,  8, 49, 20, 28, 24, 11,
       52,  2, 50, 12]), 'cur_cost': 107759.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([40, 37,  9, 32,  7,  3, 38, 29, 36, 18, 13, 39, 46, 44, 21,  5, 43,
       53, 19, 10, 45, 33, 47, 48, 35, 17, 23, 31,  4, 34,  6,  0, 16, 27,
       25, 22, 54, 42, 14, 15, 30,  1, 26, 51, 41,  8, 49, 20, 28, 24, 11,
       52,  2, 50, 12]), 'cur_cost': 104242.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([40,  7, 37,  9, 32,  3, 38, 29, 36, 18, 13, 39, 46, 44, 21,  5, 43,
       53, 19, 10, 45, 33, 47, 48, 35, 17, 23, 31,  4, 34,  6,  0, 16, 27,
       25, 22, 54, 42, 14, 15, 30,  1, 26, 51, 41,  8, 49, 20, 28, 24, 11,
       52,  2, 50, 12]), 'cur_cost': 108746.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([38, 12,  7,  3, 51, 29,  6, 19, 16, 15, 14, 20, 39, 54, 27, 11, 10,
       17, 30,  8, 28, 35,  2, 26, 37, 45, 49,  5, 36, 21, 48, 13, 33, 41,
        0, 42, 44, 40, 53, 18, 46, 32, 52, 22, 23, 50,  1, 25,  9, 34, 47,
        4, 43, 24, 31], dtype=int64), 'cur_cost': 103819.0, 'intermediate_solutions': [{'tour': array([23, 10, 47, 44, 18, 45,  1,  9,  3,  4,  8, 14, 22, 25, 51, 12, 53,
        2, 24, 15, 33, 43, 17, 38, 34, 19, 48, 50, 52, 21, 39,  5, 42, 20,
       16, 36, 49,  6, 40, 37, 54, 35, 11, 41,  7, 26, 31, 46, 28,  0, 13,
       32, 30, 27, 29]), 'cur_cost': 94269.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([44, 23, 10, 47, 18, 45,  1,  9,  3,  4,  8, 14, 22, 25, 51, 12, 53,
        2, 24, 15, 33, 43, 17, 38, 34, 19, 48, 50, 52, 21, 39,  5, 42, 20,
       16, 36, 49,  6, 40, 37, 54, 35, 11, 41,  7, 26, 31, 46, 28,  0, 13,
       32, 30, 27, 29]), 'cur_cost': 98210.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([18, 44, 23, 10, 47, 45,  1,  9,  3,  4,  8, 14, 22, 25, 51, 12, 53,
        2, 24, 15, 33, 43, 17, 38, 34, 19, 48, 50, 52, 21, 39,  5, 42, 20,
       16, 36, 49,  6, 40, 37, 54, 35, 11, 41,  7, 26, 31, 46, 28,  0, 13,
       32, 30, 27, 29]), 'cur_cost': 97602.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([47, 44, 23, 10, 18, 45,  1,  9,  3,  4,  8, 14, 22, 25, 51, 12, 53,
        2, 24, 15, 33, 43, 17, 38, 34, 19, 48, 50, 52, 21, 39,  5, 42, 20,
       16, 36, 49,  6, 40, 37, 54, 35, 11, 41,  7, 26, 31, 46, 28,  0, 13,
       32, 30, 27, 29]), 'cur_cost': 97017.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([47, 18, 44, 23, 10, 45,  1,  9,  3,  4,  8, 14, 22, 25, 51, 12, 53,
        2, 24, 15, 33, 43, 17, 38, 34, 19, 48, 50, 52, 21, 39,  5, 42, 20,
       16, 36, 49,  6, 40, 37, 54, 35, 11, 41,  7, 26, 31, 46, 28,  0, 13,
       32, 30, 27, 29]), 'cur_cost': 98299.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [9, 10, 7, 2, 4, 26, 33, 25, 6, 13, 19, 30, 36, 46, 50, 43, 5, 45, 41, 44, 54, 47, 24, 52, 17, 14, 20, 40, 31, 38, 35, 51, 48, 28, 27, 0, 23, 18, 1, 39, 53, 21, 3, 29, 22, 49, 8, 12, 11, 37, 15, 34, 42, 16, 32], 'cur_cost': 89978.0, 'intermediate_solutions': [{'tour': [0, 8, 7, 23, 22, 32, 28, 24, 30, 26, 31, 25, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 15, 1, 4, 6, 5, 9, 3, 2, 21, 19, 17, 13, 14, 16, 18, 20, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 18986.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 7, 23, 22, 32, 28, 24, 30, 26, 31, 25, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 14, 13, 17, 19, 21, 2, 3, 9, 5, 6, 4, 1, 16, 18, 20, 12, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 24739.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 7, 23, 22, 32, 28, 24, 30, 26, 31, 25, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 52, 1, 4, 6, 5, 9, 3, 2, 21, 19, 17, 13, 14, 16, 18, 20, 12, 11, 51, 45, 50, 48, 47, 54, 44, 53, 46, 49], 'cur_cost': 24969.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [40, 32, 36, 34, 4, 37, 39, 7, 3, 42, 9, 23, 30, 28, 38, 46, 47, 49, 50, 16, 13, 24, 5, 22, 2, 8, 29, 1, 31, 10, 0, 21, 14, 53, 17, 20, 48, 18, 12, 52, 45, 51, 44, 54, 15, 27, 25, 26, 43, 41, 35, 6, 33, 19, 11], 'cur_cost': 59025.0, 'intermediate_solutions': [{'tour': [29, 28, 30, 23, 38, 31, 26, 0, 6, 42, 33, 8, 34, 40, 41, 10, 13, 7, 5, 3, 2, 1, 16, 12, 18, 17, 52, 15, 53, 27, 25, 45, 48, 36, 21, 20, 50, 35, 14, 19, 22, 24, 51, 11, 39, 49, 43, 46, 54, 37, 47, 9, 44, 32, 4], 'cur_cost': 77917.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [29, 52, 17, 18, 12, 16, 1, 2, 3, 5, 7, 13, 10, 41, 40, 34, 8, 33, 42, 6, 35, 26, 31, 38, 23, 30, 28, 15, 53, 27, 25, 45, 48, 36, 21, 20, 50, 0, 14, 19, 22, 24, 51, 11, 39, 49, 43, 46, 54, 37, 47, 9, 44, 32, 4], 'cur_cost': 82757.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [29, 3, 28, 30, 23, 38, 31, 26, 35, 6, 42, 33, 8, 34, 40, 41, 10, 13, 7, 5, 2, 1, 16, 12, 18, 17, 52, 15, 53, 27, 25, 45, 48, 36, 21, 20, 50, 0, 14, 19, 22, 24, 51, 11, 39, 49, 43, 46, 54, 37, 47, 9, 44, 32, 4], 'cur_cost': 82367.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 19, 8, 7, 9, 5, 6, 4, 1, 10, 2, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 11], 'cur_cost': 16550.0, 'intermediate_solutions': [{'tour': [29, 28, 41, 23, 27, 38, 14, 31, 26, 35, 36, 6, 37, 42, 1, 34, 40, 22, 46, 13, 7, 9, 51, 44, 2, 4, 12, 18, 17, 52, 15, 53, 19, 45, 47, 49, 32, 21, 16, 50, 20, 48, 5, 25, 33, 30, 10, 24, 54, 11, 3, 8, 39, 0, 43], 'cur_cost': 73813.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [29, 28, 41, 23, 27, 38, 25, 31, 26, 35, 36, 6, 37, 42, 1, 34, 40, 22, 46, 13, 7, 9, 51, 44, 2, 4, 12, 18, 17, 52, 15, 53, 19, 45, 47, 49, 32, 21, 16, 50, 20, 48, 5, 14, 33, 30, 10, 24, 0, 39, 8, 3, 11, 54, 43], 'cur_cost': 72857.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [29, 28, 41, 23, 27, 38, 25, 31, 26, 35, 36, 6, 37, 42, 1, 34, 40, 22, 46, 13, 7, 9, 51, 44, 2, 4, 12, 18, 17, 52, 15, 53, 19, 45, 24, 47, 49, 32, 21, 16, 50, 20, 48, 5, 14, 33, 30, 10, 54, 11, 3, 8, 39, 0, 43], 'cur_cost': 76440.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 6, 5, 9, 7, 3, 2, 8, 4, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 10699.0, 'intermediate_solutions': [{'tour': [32, 30, 29, 28, 10, 0, 22, 27, 33, 5, 26, 36, 31, 53, 46, 20, 44, 11, 52, 15, 18, 25, 41, 42, 24, 23, 43, 40, 9, 7, 38, 54, 17, 51, 45, 50, 49, 16, 13, 12, 21, 1, 4, 2, 6, 3, 37, 39, 34, 47, 14, 19, 48, 35, 8], 'cur_cost': 52875.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [32, 30, 29, 28, 36, 0, 22, 27, 33, 5, 26, 10, 31, 53, 46, 20, 44, 11, 52, 15, 18, 25, 41, 42, 24, 23, 43, 40, 9, 7, 38, 54, 17, 51, 45, 50, 49, 16, 13, 12, 21, 1, 4, 2, 6, 3, 37, 39, 34, 47, 19, 14, 48, 35, 8], 'cur_cost': 55128.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [32, 30, 29, 28, 36, 0, 22, 27, 33, 5, 26, 38, 10, 31, 53, 46, 20, 44, 11, 52, 15, 18, 25, 41, 42, 24, 23, 43, 40, 9, 7, 54, 17, 51, 45, 50, 49, 16, 13, 12, 21, 1, 4, 2, 6, 3, 37, 39, 34, 47, 14, 19, 48, 35, 8], 'cur_cost': 55108.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [11, 36, 8, 47, 30, 10, 44, 31, 25, 13, 19, 16, 50, 51, 34, 40, 18, 17, 54, 24, 29, 27, 4, 37, 28, 0, 45, 5, 21, 42, 52, 3, 14, 41, 1, 48, 35, 15, 23, 6, 7, 38, 33, 39, 26, 43, 20, 53, 46, 22, 2, 12, 32, 49, 9], 'cur_cost': 103980.0, 'intermediate_solutions': [{'tour': [27, 4, 32, 38, 35, 26, 44, 28, 10, 8, 29, 7, 25, 34, 36, 22, 37, 0, 31, 40, 23, 41, 43, 42, 54, 48, 45, 16, 20, 13, 18, 21, 53, 17, 15, 52, 11, 47, 51, 46, 30, 9, 2, 5, 1, 19, 39, 12, 14, 24, 33, 6, 3, 49, 50], 'cur_cost': 60736.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [27, 4, 32, 38, 35, 26, 39, 28, 10, 8, 29, 7, 25, 34, 36, 22, 37, 0, 31, 40, 23, 41, 43, 42, 54, 48, 45, 16, 20, 13, 18, 21, 53, 17, 15, 52, 11, 47, 51, 46, 30, 9, 50, 49, 3, 6, 33, 24, 14, 12, 44, 19, 1, 5, 2], 'cur_cost': 56952.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [27, 4, 32, 38, 35, 26, 39, 28, 10, 8, 1, 29, 7, 25, 34, 36, 22, 37, 0, 31, 40, 23, 41, 43, 42, 54, 48, 45, 16, 20, 13, 18, 21, 53, 17, 15, 52, 11, 47, 51, 46, 30, 9, 2, 5, 19, 44, 12, 14, 24, 33, 6, 3, 49, 50], 'cur_cost': 54430.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:29:03,127 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:03,127 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:03,130 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10699.000, 多样性=0.954
2025-08-05 10:29:03,131 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 10:29:03,131 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 10:29:03,131 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:03,131 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.11152101591674986, 'best_improvement': 0.4061060227588121}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.01830282861896816}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.17978337740092107, 'recent_improvements': [0.08313510282330731, 0.004309059985786012, -0.2764316519785348], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 10442, 'new_best_cost': 10442, 'quality_improvement': 0.0, 'old_diversity': 0.9060606060606061, 'new_diversity': 0.9060606060606061, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:29:03,131 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 10:29:03,132 - __main__ - INFO - composite10_55 开始进化第 5 代
2025-08-05 10:29:03,132 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 10:29:03,132 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:03,133 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10699.000, 多样性=0.954
2025-08-05 10:29:03,133 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:03,137 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.954
2025-08-05 10:29:03,138 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:03,140 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.906
2025-08-05 10:29:03,143 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 10:29:03,143 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:03,143 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 10:29:03,143 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 10:29:03,171 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.143, 适应度梯度: 32.071, 聚类评分: 0.000, 覆盖率: 0.128, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:03,171 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 10:29:03,171 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:03,172 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite10_55
2025-08-05 10:29:03,176 - visualization.landscape_visualizer - INFO - 插值约束: 210 个点被约束到最小值 10442.00
2025-08-05 10:29:03,178 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=8.7%, 梯度: 3718.46 → 3393.28
2025-08-05 10:29:03,292 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite10_55\landscape_composite10_55_iter_110_20250805_102903.html
2025-08-05 10:29:03,353 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite10_55\dashboard_composite10_55_iter_110_20250805_102903.html
2025-08-05 10:29:03,353 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 110
2025-08-05 10:29:03,354 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 10:29:03,354 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2120秒
2025-08-05 10:29:03,354 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.14285714285714285, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 32.07142857142701, 'local_optima_density': 0.14285714285714285, 'gradient_variance': 873934277.5406122, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1279, 'fitness_entropy': 0.8446958733715437, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.128)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 32.071)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360943.1714046, 'performance_metrics': {}}}
2025-08-05 10:29:03,354 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:03,354 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:03,355 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:03,355 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:03,355 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:29:03,355 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 10:29:03,356 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:29:03,356 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:03,356 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:03,356 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:29:03,356 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:03,357 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:03,357 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 0} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:03,357 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:03,357 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:03,357 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:03,360 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 10:29:03,360 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,361 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,361 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,361 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,362 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10892.0, 路径长度: 55, 收集中间解: 3
2025-08-05 10:29:03,362 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 3, 20, 13, 17, 14, 16, 15, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 6, 4, 1, 2], 'cur_cost': 10892.0, 'intermediate_solutions': [{'tour': [0, 9, 5, 10, 8, 7, 3, 2, 1, 4, 6, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 53, 40, 41, 49, 46, 44, 54, 34, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 22094.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 5, 10, 8, 7, 3, 2, 1, 4, 6, 32, 22, 23, 25, 31, 26, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 10914.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 5, 10, 8, 7, 3, 2, 1, 4, 6, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 29, 19, 11], 'cur_cost': 15739.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:03,362 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 10892.00)
2025-08-05 10:29:03,362 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:03,362 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:03,362 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:03,366 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 10:29:03,367 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,367 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,367 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,368 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,368 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13496.0, 路径长度: 55, 收集中间解: 3
2025-08-05 10:29:03,368 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 23, 2, 19, 21, 12, 18, 16, 15, 20, 14, 11, 13, 17, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 6, 4, 1], 'cur_cost': 13496.0, 'intermediate_solutions': [{'tour': [11, 43, 5, 21, 41, 47, 3, 30, 1, 10, 44, 36, 33, 27, 23, 6, 16, 13, 19, 8, 9, 29, 35, 25, 46, 37, 42, 50, 51, 39, 34, 40, 48, 49, 18, 17, 52, 53, 32, 24, 45, 20, 54, 14, 7, 26, 2, 15, 28, 0, 22, 12, 31, 38, 4], 'cur_cost': 88061.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 43, 5, 21, 41, 47, 3, 30, 1, 10, 44, 36, 33, 27, 23, 6, 32, 13, 19, 8, 9, 29, 35, 25, 46, 37, 42, 50, 51, 39, 34, 40, 48, 45, 24, 16, 53, 52, 17, 18, 49, 20, 54, 14, 7, 26, 2, 15, 28, 0, 22, 12, 31, 38, 4], 'cur_cost': 90706.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 43, 5, 21, 41, 47, 3, 30, 1, 10, 44, 36, 33, 48, 27, 23, 6, 32, 13, 19, 8, 9, 29, 35, 25, 46, 37, 42, 50, 51, 39, 34, 40, 49, 18, 17, 52, 53, 16, 24, 45, 20, 54, 14, 7, 26, 2, 15, 28, 0, 22, 12, 31, 38, 4], 'cur_cost': 94306.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:03,368 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 13496.00)
2025-08-05 10:29:03,369 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-05 10:29:03,369 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:03,369 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:03,369 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 86895.0
2025-08-05 10:29:03,379 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:29:03,380 - ExploitationExpert - INFO - res_population_costs: [10442, 10445, 10448, 10468.0]
2025-08-05 10:29:03,380 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 21, 19, 18, 12, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  9,  7,  2,  3,
        5,  6,  4,  1], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 30, 24, 26, 31, 25, 23, 28, 32, 27, 22, 29,
       35, 33, 36, 38, 41, 40, 34, 39, 37, 42, 43, 10,  8,  2,  3,  7,  9,
        5,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  9,  7,  3,  2,  8, 10, 43, 42, 37, 39, 34, 40,
       41, 38, 36, 33, 35, 29, 31, 26, 25, 23, 22, 27, 32, 28, 24, 30, 49,
       46, 44, 53, 54, 50, 48, 52, 47, 51, 45, 13, 17, 14, 11, 16, 15, 20,
       18, 12, 21, 19], dtype=int64)]
2025-08-05 10:29:03,382 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:03,382 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 20, 13, 17, 14, 16, 15, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 6, 4, 1, 2], 'cur_cost': 10892.0}, {'tour': [0, 23, 2, 19, 21, 12, 18, 16, 15, 20, 14, 11, 13, 17, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 6, 4, 1], 'cur_cost': 13496.0}, {'tour': array([41, 33, 11, 12,  6, 18, 13, 37, 29, 35, 32, 30,  7, 22, 53, 52, 31,
       23,  3, 17, 39, 34, 44, 51, 19, 36,  9,  4, 49, 46, 14, 42, 43, 48,
        8, 10,  1, 21, 20, 28, 16, 27,  0, 15, 47, 45,  5,  2, 40, 26, 54,
       24, 50, 25, 38], dtype=int64), 'cur_cost': 86895.0}, {'tour': [4, 47, 19, 22, 40, 21, 26, 41, 27, 42, 3, 35, 34, 38, 7, 20, 1, 45, 48, 25, 37, 54, 14, 33, 12, 15, 0, 17, 11, 23, 2, 30, 44, 28, 8, 31, 52, 51, 10, 9, 46, 13, 49, 18, 36, 53, 24, 29, 32, 43, 6, 16, 5, 50, 39], 'cur_cost': 103058.0}, {'tour': [38, 12, 7, 3, 51, 29, 6, 19, 16, 15, 14, 20, 39, 54, 27, 11, 10, 17, 30, 8, 28, 35, 2, 26, 37, 45, 49, 5, 36, 21, 48, 13, 33, 41, 0, 42, 44, 40, 53, 18, 46, 32, 52, 22, 23, 50, 1, 25, 9, 34, 47, 4, 43, 24, 31], 'cur_cost': 103819.0}, {'tour': [9, 10, 7, 2, 4, 26, 33, 25, 6, 13, 19, 30, 36, 46, 50, 43, 5, 45, 41, 44, 54, 47, 24, 52, 17, 14, 20, 40, 31, 38, 35, 51, 48, 28, 27, 0, 23, 18, 1, 39, 53, 21, 3, 29, 22, 49, 8, 12, 11, 37, 15, 34, 42, 16, 32], 'cur_cost': 89978.0}, {'tour': [40, 32, 36, 34, 4, 37, 39, 7, 3, 42, 9, 23, 30, 28, 38, 46, 47, 49, 50, 16, 13, 24, 5, 22, 2, 8, 29, 1, 31, 10, 0, 21, 14, 53, 17, 20, 48, 18, 12, 52, 45, 51, 44, 54, 15, 27, 25, 26, 43, 41, 35, 6, 33, 19, 11], 'cur_cost': 59025.0}, {'tour': [0, 3, 19, 8, 7, 9, 5, 6, 4, 1, 10, 2, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 11], 'cur_cost': 16550.0}, {'tour': [0, 1, 6, 5, 9, 7, 3, 2, 8, 4, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 10699.0}, {'tour': [11, 36, 8, 47, 30, 10, 44, 31, 25, 13, 19, 16, 50, 51, 34, 40, 18, 17, 54, 24, 29, 27, 4, 37, 28, 0, 45, 5, 21, 42, 52, 3, 14, 41, 1, 48, 35, 15, 23, 6, 7, 38, 33, 39, 26, 43, 20, 53, 46, 22, 2, 12, 32, 49, 9], 'cur_cost': 103980.0}]
2025-08-05 10:29:03,384 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:03,384 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 284, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 284, 'cache_hits': 0, 'similarity_calculations': 1411, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:03,385 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([41, 33, 11, 12,  6, 18, 13, 37, 29, 35, 32, 30,  7, 22, 53, 52, 31,
       23,  3, 17, 39, 34, 44, 51, 19, 36,  9,  4, 49, 46, 14, 42, 43, 48,
        8, 10,  1, 21, 20, 28, 16, 27,  0, 15, 47, 45,  5,  2, 40, 26, 54,
       24, 50, 25, 38], dtype=int64), 'cur_cost': 86895.0, 'intermediate_solutions': [{'tour': array([16, 15, 37, 41, 28, 27, 11, 47, 38, 36, 21,  9, 22, 54, 53,  4, 44,
        1,  0, 13, 48, 12, 18, 17, 35, 46, 43, 30, 45,  3, 26, 19, 51, 23,
       10, 32,  2, 25, 40, 20, 39,  8, 52, 24, 34, 31, 50,  6, 49,  5,  7,
       33, 42, 29, 14]), 'cur_cost': 98908.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([41, 16, 15, 37, 28, 27, 11, 47, 38, 36, 21,  9, 22, 54, 53,  4, 44,
        1,  0, 13, 48, 12, 18, 17, 35, 46, 43, 30, 45,  3, 26, 19, 51, 23,
       10, 32,  2, 25, 40, 20, 39,  8, 52, 24, 34, 31, 50,  6, 49,  5,  7,
       33, 42, 29, 14]), 'cur_cost': 105963.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([28, 41, 16, 15, 37, 27, 11, 47, 38, 36, 21,  9, 22, 54, 53,  4, 44,
        1,  0, 13, 48, 12, 18, 17, 35, 46, 43, 30, 45,  3, 26, 19, 51, 23,
       10, 32,  2, 25, 40, 20, 39,  8, 52, 24, 34, 31, 50,  6, 49,  5,  7,
       33, 42, 29, 14]), 'cur_cost': 105961.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([37, 41, 16, 15, 28, 27, 11, 47, 38, 36, 21,  9, 22, 54, 53,  4, 44,
        1,  0, 13, 48, 12, 18, 17, 35, 46, 43, 30, 45,  3, 26, 19, 51, 23,
       10, 32,  2, 25, 40, 20, 39,  8, 52, 24, 34, 31, 50,  6, 49,  5,  7,
       33, 42, 29, 14]), 'cur_cost': 103705.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([37, 28, 41, 16, 15, 27, 11, 47, 38, 36, 21,  9, 22, 54, 53,  4, 44,
        1,  0, 13, 48, 12, 18, 17, 35, 46, 43, 30, 45,  3, 26, 19, 51, 23,
       10, 32,  2, 25, 40, 20, 39,  8, 52, 24, 34, 31, 50,  6, 49,  5,  7,
       33, 42, 29, 14]), 'cur_cost': 106055.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:03,386 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 86895.00)
2025-08-05 10:29:03,386 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:03,386 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:03,386 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:03,389 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 55
2025-08-05 10:29:03,389 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,390 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,390 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,390 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,391 - ExplorationExpert - INFO - 探索路径生成完成，成本: 69771.0, 路径长度: 55, 收集中间解: 3
2025-08-05 10:29:03,391 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [4, 20, 13, 21, 14, 16, 15, 18, 12, 11, 38, 45, 43, 50, 48, 54, 44, 53, 46, 49, 24, 26, 17, 25, 10, 2, 40, 27, 0, 31, 51, 47, 35, 52, 39, 22, 36, 41, 29, 9, 34, 6, 30, 23, 8, 37, 33, 19, 3, 32, 42, 5, 7, 28, 1], 'cur_cost': 69771.0, 'intermediate_solutions': [{'tour': [4, 47, 19, 22, 40, 21, 26, 41, 27, 42, 3, 35, 5, 38, 7, 20, 1, 45, 48, 25, 37, 54, 14, 33, 12, 15, 0, 17, 11, 23, 2, 30, 44, 28, 8, 31, 52, 51, 10, 9, 46, 13, 49, 18, 36, 53, 24, 29, 32, 43, 6, 16, 34, 50, 39], 'cur_cost': 107984.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 47, 19, 22, 40, 21, 26, 41, 27, 42, 3, 35, 20, 7, 38, 34, 1, 45, 48, 25, 37, 54, 14, 33, 12, 15, 0, 17, 11, 23, 2, 30, 44, 28, 8, 31, 52, 51, 10, 9, 46, 13, 49, 18, 36, 53, 24, 29, 32, 43, 6, 16, 5, 50, 39], 'cur_cost': 105865.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 47, 19, 22, 40, 21, 26, 41, 27, 42, 3, 35, 34, 38, 7, 20, 1, 45, 48, 25, 37, 54, 14, 33, 12, 15, 0, 17, 11, 23, 2, 30, 44, 28, 8, 31, 52, 51, 10, 9, 46, 13, 49, 18, 36, 53, 24, 6, 29, 32, 43, 16, 5, 50, 39], 'cur_cost': 104296.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:03,392 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 69771.00)
2025-08-05 10:29:03,392 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:29:03,392 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:03,392 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:03,393 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 101158.0
2025-08-05 10:29:03,407 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:29:03,408 - ExploitationExpert - INFO - res_population_costs: [10442, 10445, 10448, 10468.0]
2025-08-05 10:29:03,408 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 21, 19, 18, 12, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  9,  7,  2,  3,
        5,  6,  4,  1], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 30, 24, 26, 31, 25, 23, 28, 32, 27, 22, 29,
       35, 33, 36, 38, 41, 40, 34, 39, 37, 42, 43, 10,  8,  2,  3,  7,  9,
        5,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  9,  7,  3,  2,  8, 10, 43, 42, 37, 39, 34, 40,
       41, 38, 36, 33, 35, 29, 31, 26, 25, 23, 22, 27, 32, 28, 24, 30, 49,
       46, 44, 53, 54, 50, 48, 52, 47, 51, 45, 13, 17, 14, 11, 16, 15, 20,
       18, 12, 21, 19], dtype=int64)]
2025-08-05 10:29:03,410 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:03,410 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 20, 13, 17, 14, 16, 15, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 6, 4, 1, 2], 'cur_cost': 10892.0}, {'tour': [0, 23, 2, 19, 21, 12, 18, 16, 15, 20, 14, 11, 13, 17, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 6, 4, 1], 'cur_cost': 13496.0}, {'tour': array([41, 33, 11, 12,  6, 18, 13, 37, 29, 35, 32, 30,  7, 22, 53, 52, 31,
       23,  3, 17, 39, 34, 44, 51, 19, 36,  9,  4, 49, 46, 14, 42, 43, 48,
        8, 10,  1, 21, 20, 28, 16, 27,  0, 15, 47, 45,  5,  2, 40, 26, 54,
       24, 50, 25, 38], dtype=int64), 'cur_cost': 86895.0}, {'tour': [4, 20, 13, 21, 14, 16, 15, 18, 12, 11, 38, 45, 43, 50, 48, 54, 44, 53, 46, 49, 24, 26, 17, 25, 10, 2, 40, 27, 0, 31, 51, 47, 35, 52, 39, 22, 36, 41, 29, 9, 34, 6, 30, 23, 8, 37, 33, 19, 3, 32, 42, 5, 7, 28, 1], 'cur_cost': 69771.0}, {'tour': array([10, 48, 31,  2, 26, 47, 23, 11, 35, 39, 22, 16, 38, 43, 29,  0, 24,
       41, 30, 42, 40, 18, 33, 44, 14, 13,  8, 19, 28,  9, 52, 51, 25,  4,
        1,  3, 45, 53, 49,  6,  7, 37, 17,  5, 20, 12, 32, 46, 21, 54, 36,
       15, 27, 34, 50], dtype=int64), 'cur_cost': 101158.0}, {'tour': [9, 10, 7, 2, 4, 26, 33, 25, 6, 13, 19, 30, 36, 46, 50, 43, 5, 45, 41, 44, 54, 47, 24, 52, 17, 14, 20, 40, 31, 38, 35, 51, 48, 28, 27, 0, 23, 18, 1, 39, 53, 21, 3, 29, 22, 49, 8, 12, 11, 37, 15, 34, 42, 16, 32], 'cur_cost': 89978.0}, {'tour': [40, 32, 36, 34, 4, 37, 39, 7, 3, 42, 9, 23, 30, 28, 38, 46, 47, 49, 50, 16, 13, 24, 5, 22, 2, 8, 29, 1, 31, 10, 0, 21, 14, 53, 17, 20, 48, 18, 12, 52, 45, 51, 44, 54, 15, 27, 25, 26, 43, 41, 35, 6, 33, 19, 11], 'cur_cost': 59025.0}, {'tour': [0, 3, 19, 8, 7, 9, 5, 6, 4, 1, 10, 2, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 11], 'cur_cost': 16550.0}, {'tour': [0, 1, 6, 5, 9, 7, 3, 2, 8, 4, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 10699.0}, {'tour': [11, 36, 8, 47, 30, 10, 44, 31, 25, 13, 19, 16, 50, 51, 34, 40, 18, 17, 54, 24, 29, 27, 4, 37, 28, 0, 45, 5, 21, 42, 52, 3, 14, 41, 1, 48, 35, 15, 23, 6, 7, 38, 33, 39, 26, 43, 20, 53, 46, 22, 2, 12, 32, 49, 9], 'cur_cost': 103980.0}]
2025-08-05 10:29:03,412 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:03,413 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 285, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 285, 'cache_hits': 0, 'similarity_calculations': 1422, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:03,414 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([10, 48, 31,  2, 26, 47, 23, 11, 35, 39, 22, 16, 38, 43, 29,  0, 24,
       41, 30, 42, 40, 18, 33, 44, 14, 13,  8, 19, 28,  9, 52, 51, 25,  4,
        1,  3, 45, 53, 49,  6,  7, 37, 17,  5, 20, 12, 32, 46, 21, 54, 36,
       15, 27, 34, 50], dtype=int64), 'cur_cost': 101158.0, 'intermediate_solutions': [{'tour': array([ 7, 12, 38,  3, 51, 29,  6, 19, 16, 15, 14, 20, 39, 54, 27, 11, 10,
       17, 30,  8, 28, 35,  2, 26, 37, 45, 49,  5, 36, 21, 48, 13, 33, 41,
        0, 42, 44, 40, 53, 18, 46, 32, 52, 22, 23, 50,  1, 25,  9, 34, 47,
        4, 43, 24, 31]), 'cur_cost': 106321.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 3,  7, 12, 38, 51, 29,  6, 19, 16, 15, 14, 20, 39, 54, 27, 11, 10,
       17, 30,  8, 28, 35,  2, 26, 37, 45, 49,  5, 36, 21, 48, 13, 33, 41,
        0, 42, 44, 40, 53, 18, 46, 32, 52, 22, 23, 50,  1, 25,  9, 34, 47,
        4, 43, 24, 31]), 'cur_cost': 103888.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([51,  3,  7, 12, 38, 29,  6, 19, 16, 15, 14, 20, 39, 54, 27, 11, 10,
       17, 30,  8, 28, 35,  2, 26, 37, 45, 49,  5, 36, 21, 48, 13, 33, 41,
        0, 42, 44, 40, 53, 18, 46, 32, 52, 22, 23, 50,  1, 25,  9, 34, 47,
        4, 43, 24, 31]), 'cur_cost': 103771.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([38,  3,  7, 12, 51, 29,  6, 19, 16, 15, 14, 20, 39, 54, 27, 11, 10,
       17, 30,  8, 28, 35,  2, 26, 37, 45, 49,  5, 36, 21, 48, 13, 33, 41,
        0, 42, 44, 40, 53, 18, 46, 32, 52, 22, 23, 50,  1, 25,  9, 34, 47,
        4, 43, 24, 31]), 'cur_cost': 100722.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([38, 51,  3,  7, 12, 29,  6, 19, 16, 15, 14, 20, 39, 54, 27, 11, 10,
       17, 30,  8, 28, 35,  2, 26, 37, 45, 49,  5, 36, 21, 48, 13, 33, 41,
        0, 42, 44, 40, 53, 18, 46, 32, 52, 22, 23, 50,  1, 25,  9, 34, 47,
        4, 43, 24, 31]), 'cur_cost': 103569.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:03,414 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 101158.00)
2025-08-05 10:29:03,414 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:29:03,414 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:29:03,415 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:03,417 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 10:29:03,417 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,417 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,418 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,418 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,418 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10795.0, 路径长度: 55, 收集中间解: 3
2025-08-05 10:29:03,418 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 15, 16, 12, 21, 19, 17, 13, 14, 11, 18, 20, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 10795.0, 'intermediate_solutions': [{'tour': [9, 10, 7, 2, 4, 26, 33, 45, 6, 13, 19, 30, 36, 46, 50, 43, 5, 25, 41, 44, 54, 47, 24, 52, 17, 14, 20, 40, 31, 38, 35, 51, 48, 28, 27, 0, 23, 18, 1, 39, 53, 21, 3, 29, 22, 49, 8, 12, 11, 37, 15, 34, 42, 16, 32], 'cur_cost': 90012.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 10, 7, 2, 4, 26, 33, 25, 6, 13, 19, 30, 36, 46, 50, 52, 24, 47, 54, 44, 41, 45, 5, 43, 17, 14, 20, 40, 31, 38, 35, 51, 48, 28, 27, 0, 23, 18, 1, 39, 53, 21, 3, 29, 22, 49, 8, 12, 11, 37, 15, 34, 42, 16, 32], 'cur_cost': 89515.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 10, 2, 4, 26, 33, 25, 6, 13, 19, 30, 36, 46, 50, 43, 5, 45, 41, 44, 54, 47, 24, 52, 17, 14, 20, 40, 31, 38, 35, 51, 48, 28, 27, 0, 23, 18, 1, 39, 53, 21, 3, 29, 22, 49, 7, 8, 12, 11, 37, 15, 34, 42, 16, 32], 'cur_cost': 89988.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:03,418 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 10795.00)
2025-08-05 10:29:03,418 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:03,418 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:03,419 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:03,424 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 55
2025-08-05 10:29:03,425 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,425 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,425 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,426 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,426 - ExplorationExpert - INFO - 探索路径生成完成，成本: 70474.0, 路径长度: 55, 收集中间解: 3
2025-08-05 10:29:03,426 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [3, 31, 23, 0, 35, 10, 26, 27, 36, 22, 24, 28, 6, 39, 41, 37, 38, 7, 21, 53, 17, 14, 54, 13, 45, 18, 51, 15, 46, 19, 32, 34, 4, 42, 43, 2, 40, 5, 29, 44, 50, 12, 52, 11, 48, 30, 1, 33, 25, 9, 16, 20, 47, 49, 8], 'cur_cost': 70474.0, 'intermediate_solutions': [{'tour': [40, 32, 36, 34, 4, 37, 39, 7, 3, 42, 9, 23, 30, 28, 38, 46, 47, 49, 12, 16, 13, 24, 5, 22, 2, 8, 29, 1, 31, 10, 0, 21, 14, 53, 17, 20, 48, 18, 50, 52, 45, 51, 44, 54, 15, 27, 25, 26, 43, 41, 35, 6, 33, 19, 11], 'cur_cost': 59079.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [40, 32, 36, 34, 4, 37, 39, 35, 41, 43, 26, 25, 27, 15, 54, 44, 51, 45, 52, 12, 18, 48, 20, 17, 53, 14, 21, 0, 10, 31, 1, 29, 8, 2, 22, 5, 24, 13, 16, 50, 49, 47, 46, 38, 28, 30, 23, 9, 42, 3, 7, 6, 33, 19, 11], 'cur_cost': 54661.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [40, 32, 36, 34, 4, 29, 37, 39, 7, 3, 42, 9, 23, 30, 28, 38, 46, 47, 49, 50, 16, 13, 24, 5, 22, 2, 8, 1, 31, 10, 0, 21, 14, 53, 17, 20, 48, 18, 12, 52, 45, 51, 44, 54, 15, 27, 25, 26, 43, 41, 35, 6, 33, 19, 11], 'cur_cost': 56494.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:03,427 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 70474.00)
2025-08-05 10:29:03,427 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:03,427 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:03,427 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:03,432 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 55
2025-08-05 10:29:03,432 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,433 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,433 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,433 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,434 - ExplorationExpert - INFO - 探索路径生成完成，成本: 56435.0, 路径长度: 55, 收集中间解: 3
2025-08-05 10:29:03,434 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [26, 36, 29, 1, 2, 9, 3, 23, 8, 32, 39, 33, 30, 53, 21, 16, 19, 48, 46, 44, 50, 24, 31, 6, 38, 41, 4, 28, 37, 7, 40, 22, 27, 0, 42, 52, 17, 15, 18, 45, 54, 51, 47, 13, 20, 49, 25, 35, 43, 5, 10, 12, 14, 11, 34], 'cur_cost': 56435.0, 'intermediate_solutions': [{'tour': [0, 3, 19, 8, 7, 9, 5, 29, 4, 1, 10, 2, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 6, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 11], 'cur_cost': 21970.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 19, 8, 7, 9, 5, 6, 4, 1, 10, 2, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 18, 20, 15, 16, 14, 17, 13, 48, 12, 21, 11], 'cur_cost': 18925.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 19, 8, 7, 9, 5, 6, 4, 1, 10, 2, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 14, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 16, 15, 20, 18, 12, 21, 11], 'cur_cost': 18879.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:03,434 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 56435.00)
2025-08-05 10:29:03,434 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:29:03,435 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:29:03,435 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:03,437 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 55
2025-08-05 10:29:03,437 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,437 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,438 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,438 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,438 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10817.0, 路径长度: 55, 收集中间解: 3
2025-08-05 10:29:03,438 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 5, 13, 12, 21, 19, 17, 14, 16, 15, 20, 18, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 3, 2, 1, 4, 6], 'cur_cost': 10817.0, 'intermediate_solutions': [{'tour': [0, 1, 6, 5, 9, 7, 3, 2, 8, 4, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 21, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 44, 19, 11], 'cur_cost': 15469.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 6, 5, 9, 7, 3, 2, 8, 4, 10, 31, 47, 53, 54, 44, 46, 49, 41, 40, 34, 39, 43, 33, 42, 37, 38, 36, 35, 27, 29, 26, 30, 24, 28, 32, 22, 23, 25, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 14771.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 6, 5, 9, 7, 3, 2, 8, 4, 10, 31, 25, 23, 22, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 32], 'cur_cost': 11700.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:03,439 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 10817.00)
2025-08-05 10:29:03,439 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:29:03,439 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:03,439 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:03,440 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 108948.0
2025-08-05 10:29:03,450 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:29:03,450 - ExploitationExpert - INFO - res_population_costs: [10442, 10445, 10448, 10468.0, 10442]
2025-08-05 10:29:03,450 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 21, 19, 18, 12, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 35, 36, 38, 41, 40, 34, 39, 43, 42, 37, 33,
       29, 23, 22, 27, 32, 28, 30, 24, 26, 25, 31, 10,  8,  9,  7,  2,  3,
        5,  6,  4,  1], dtype=int64), array([ 0, 19, 21, 12, 18, 20, 15, 16, 11, 14, 17, 13, 45, 51, 47, 52, 48,
       50, 54, 53, 44, 46, 49, 30, 24, 26, 31, 25, 23, 28, 32, 27, 22, 29,
       35, 33, 36, 38, 41, 40, 34, 39, 37, 42, 43, 10,  8,  2,  3,  7,  9,
        5,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  9,  7,  3,  2,  8, 10, 43, 42, 37, 39, 34, 40,
       41, 38, 36, 33, 35, 29, 31, 26, 25, 23, 22, 27, 32, 28, 24, 30, 49,
       46, 44, 53, 54, 50, 48, 52, 47, 51, 45, 13, 17, 14, 11, 16, 15, 20,
       18, 12, 21, 19], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64)]
2025-08-05 10:29:03,453 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:03,453 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 20, 13, 17, 14, 16, 15, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 6, 4, 1, 2], 'cur_cost': 10892.0}, {'tour': [0, 23, 2, 19, 21, 12, 18, 16, 15, 20, 14, 11, 13, 17, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 6, 4, 1], 'cur_cost': 13496.0}, {'tour': array([41, 33, 11, 12,  6, 18, 13, 37, 29, 35, 32, 30,  7, 22, 53, 52, 31,
       23,  3, 17, 39, 34, 44, 51, 19, 36,  9,  4, 49, 46, 14, 42, 43, 48,
        8, 10,  1, 21, 20, 28, 16, 27,  0, 15, 47, 45,  5,  2, 40, 26, 54,
       24, 50, 25, 38], dtype=int64), 'cur_cost': 86895.0}, {'tour': [4, 20, 13, 21, 14, 16, 15, 18, 12, 11, 38, 45, 43, 50, 48, 54, 44, 53, 46, 49, 24, 26, 17, 25, 10, 2, 40, 27, 0, 31, 51, 47, 35, 52, 39, 22, 36, 41, 29, 9, 34, 6, 30, 23, 8, 37, 33, 19, 3, 32, 42, 5, 7, 28, 1], 'cur_cost': 69771.0}, {'tour': array([10, 48, 31,  2, 26, 47, 23, 11, 35, 39, 22, 16, 38, 43, 29,  0, 24,
       41, 30, 42, 40, 18, 33, 44, 14, 13,  8, 19, 28,  9, 52, 51, 25,  4,
        1,  3, 45, 53, 49,  6,  7, 37, 17,  5, 20, 12, 32, 46, 21, 54, 36,
       15, 27, 34, 50], dtype=int64), 'cur_cost': 101158.0}, {'tour': [0, 15, 16, 12, 21, 19, 17, 13, 14, 11, 18, 20, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 10795.0}, {'tour': [3, 31, 23, 0, 35, 10, 26, 27, 36, 22, 24, 28, 6, 39, 41, 37, 38, 7, 21, 53, 17, 14, 54, 13, 45, 18, 51, 15, 46, 19, 32, 34, 4, 42, 43, 2, 40, 5, 29, 44, 50, 12, 52, 11, 48, 30, 1, 33, 25, 9, 16, 20, 47, 49, 8], 'cur_cost': 70474.0}, {'tour': [26, 36, 29, 1, 2, 9, 3, 23, 8, 32, 39, 33, 30, 53, 21, 16, 19, 48, 46, 44, 50, 24, 31, 6, 38, 41, 4, 28, 37, 7, 40, 22, 27, 0, 42, 52, 17, 15, 18, 45, 54, 51, 47, 13, 20, 49, 25, 35, 43, 5, 10, 12, 14, 11, 34], 'cur_cost': 56435.0}, {'tour': [0, 5, 13, 12, 21, 19, 17, 14, 16, 15, 20, 18, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 3, 2, 1, 4, 6], 'cur_cost': 10817.0}, {'tour': array([ 5,  2, 13,  0, 11,  8,  9,  1, 27, 49, 28, 14, 35, 17,  6, 44, 52,
       50, 22, 32, 54, 42, 12, 48, 34, 46, 21, 23, 40,  4, 30, 41, 19,  7,
       24, 38, 10, 16, 31, 39, 47, 45, 25,  3, 53, 15, 43, 29, 26, 20, 33,
       18, 51, 36, 37], dtype=int64), 'cur_cost': 108948.0}]
2025-08-05 10:29:03,455 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:03,455 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 286, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 286, 'cache_hits': 0, 'similarity_calculations': 1434, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:03,455 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([ 5,  2, 13,  0, 11,  8,  9,  1, 27, 49, 28, 14, 35, 17,  6, 44, 52,
       50, 22, 32, 54, 42, 12, 48, 34, 46, 21, 23, 40,  4, 30, 41, 19,  7,
       24, 38, 10, 16, 31, 39, 47, 45, 25,  3, 53, 15, 43, 29, 26, 20, 33,
       18, 51, 36, 37], dtype=int64), 'cur_cost': 108948.0, 'intermediate_solutions': [{'tour': array([ 8, 36, 11, 47, 30, 10, 44, 31, 25, 13, 19, 16, 50, 51, 34, 40, 18,
       17, 54, 24, 29, 27,  4, 37, 28,  0, 45,  5, 21, 42, 52,  3, 14, 41,
        1, 48, 35, 15, 23,  6,  7, 38, 33, 39, 26, 43, 20, 53, 46, 22,  2,
       12, 32, 49,  9]), 'cur_cost': 99207.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([47,  8, 36, 11, 30, 10, 44, 31, 25, 13, 19, 16, 50, 51, 34, 40, 18,
       17, 54, 24, 29, 27,  4, 37, 28,  0, 45,  5, 21, 42, 52,  3, 14, 41,
        1, 48, 35, 15, 23,  6,  7, 38, 33, 39, 26, 43, 20, 53, 46, 22,  2,
       12, 32, 49,  9]), 'cur_cost': 104512.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([30, 47,  8, 36, 11, 10, 44, 31, 25, 13, 19, 16, 50, 51, 34, 40, 18,
       17, 54, 24, 29, 27,  4, 37, 28,  0, 45,  5, 21, 42, 52,  3, 14, 41,
        1, 48, 35, 15, 23,  6,  7, 38, 33, 39, 26, 43, 20, 53, 46, 22,  2,
       12, 32, 49,  9]), 'cur_cost': 104048.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([11, 47,  8, 36, 30, 10, 44, 31, 25, 13, 19, 16, 50, 51, 34, 40, 18,
       17, 54, 24, 29, 27,  4, 37, 28,  0, 45,  5, 21, 42, 52,  3, 14, 41,
        1, 48, 35, 15, 23,  6,  7, 38, 33, 39, 26, 43, 20, 53, 46, 22,  2,
       12, 32, 49,  9]), 'cur_cost': 100795.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([11, 30, 47,  8, 36, 10, 44, 31, 25, 13, 19, 16, 50, 51, 34, 40, 18,
       17, 54, 24, 29, 27,  4, 37, 28,  0, 45,  5, 21, 42, 52,  3, 14, 41,
        1, 48, 35, 15, 23,  6,  7, 38, 33, 39, 26, 43, 20, 53, 46, 22,  2,
       12, 32, 49,  9]), 'cur_cost': 103675.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:03,456 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 108948.00)
2025-08-05 10:29:03,456 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:03,456 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:03,458 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 20, 13, 17, 14, 16, 15, 18, 12, 21, 19, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 6, 4, 1, 2], 'cur_cost': 10892.0, 'intermediate_solutions': [{'tour': [0, 9, 5, 10, 8, 7, 3, 2, 1, 4, 6, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 53, 40, 41, 49, 46, 44, 54, 34, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 22094.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 5, 10, 8, 7, 3, 2, 1, 4, 6, 32, 22, 23, 25, 31, 26, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 10914.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 5, 10, 8, 7, 3, 2, 1, 4, 6, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 29, 19, 11], 'cur_cost': 15739.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 23, 2, 19, 21, 12, 18, 16, 15, 20, 14, 11, 13, 17, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 6, 4, 1], 'cur_cost': 13496.0, 'intermediate_solutions': [{'tour': [11, 43, 5, 21, 41, 47, 3, 30, 1, 10, 44, 36, 33, 27, 23, 6, 16, 13, 19, 8, 9, 29, 35, 25, 46, 37, 42, 50, 51, 39, 34, 40, 48, 49, 18, 17, 52, 53, 32, 24, 45, 20, 54, 14, 7, 26, 2, 15, 28, 0, 22, 12, 31, 38, 4], 'cur_cost': 88061.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 43, 5, 21, 41, 47, 3, 30, 1, 10, 44, 36, 33, 27, 23, 6, 32, 13, 19, 8, 9, 29, 35, 25, 46, 37, 42, 50, 51, 39, 34, 40, 48, 45, 24, 16, 53, 52, 17, 18, 49, 20, 54, 14, 7, 26, 2, 15, 28, 0, 22, 12, 31, 38, 4], 'cur_cost': 90706.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 43, 5, 21, 41, 47, 3, 30, 1, 10, 44, 36, 33, 48, 27, 23, 6, 32, 13, 19, 8, 9, 29, 35, 25, 46, 37, 42, 50, 51, 39, 34, 40, 49, 18, 17, 52, 53, 16, 24, 45, 20, 54, 14, 7, 26, 2, 15, 28, 0, 22, 12, 31, 38, 4], 'cur_cost': 94306.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([41, 33, 11, 12,  6, 18, 13, 37, 29, 35, 32, 30,  7, 22, 53, 52, 31,
       23,  3, 17, 39, 34, 44, 51, 19, 36,  9,  4, 49, 46, 14, 42, 43, 48,
        8, 10,  1, 21, 20, 28, 16, 27,  0, 15, 47, 45,  5,  2, 40, 26, 54,
       24, 50, 25, 38], dtype=int64), 'cur_cost': 86895.0, 'intermediate_solutions': [{'tour': array([16, 15, 37, 41, 28, 27, 11, 47, 38, 36, 21,  9, 22, 54, 53,  4, 44,
        1,  0, 13, 48, 12, 18, 17, 35, 46, 43, 30, 45,  3, 26, 19, 51, 23,
       10, 32,  2, 25, 40, 20, 39,  8, 52, 24, 34, 31, 50,  6, 49,  5,  7,
       33, 42, 29, 14]), 'cur_cost': 98908.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([41, 16, 15, 37, 28, 27, 11, 47, 38, 36, 21,  9, 22, 54, 53,  4, 44,
        1,  0, 13, 48, 12, 18, 17, 35, 46, 43, 30, 45,  3, 26, 19, 51, 23,
       10, 32,  2, 25, 40, 20, 39,  8, 52, 24, 34, 31, 50,  6, 49,  5,  7,
       33, 42, 29, 14]), 'cur_cost': 105963.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([28, 41, 16, 15, 37, 27, 11, 47, 38, 36, 21,  9, 22, 54, 53,  4, 44,
        1,  0, 13, 48, 12, 18, 17, 35, 46, 43, 30, 45,  3, 26, 19, 51, 23,
       10, 32,  2, 25, 40, 20, 39,  8, 52, 24, 34, 31, 50,  6, 49,  5,  7,
       33, 42, 29, 14]), 'cur_cost': 105961.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([37, 41, 16, 15, 28, 27, 11, 47, 38, 36, 21,  9, 22, 54, 53,  4, 44,
        1,  0, 13, 48, 12, 18, 17, 35, 46, 43, 30, 45,  3, 26, 19, 51, 23,
       10, 32,  2, 25, 40, 20, 39,  8, 52, 24, 34, 31, 50,  6, 49,  5,  7,
       33, 42, 29, 14]), 'cur_cost': 103705.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([37, 28, 41, 16, 15, 27, 11, 47, 38, 36, 21,  9, 22, 54, 53,  4, 44,
        1,  0, 13, 48, 12, 18, 17, 35, 46, 43, 30, 45,  3, 26, 19, 51, 23,
       10, 32,  2, 25, 40, 20, 39,  8, 52, 24, 34, 31, 50,  6, 49,  5,  7,
       33, 42, 29, 14]), 'cur_cost': 106055.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [4, 20, 13, 21, 14, 16, 15, 18, 12, 11, 38, 45, 43, 50, 48, 54, 44, 53, 46, 49, 24, 26, 17, 25, 10, 2, 40, 27, 0, 31, 51, 47, 35, 52, 39, 22, 36, 41, 29, 9, 34, 6, 30, 23, 8, 37, 33, 19, 3, 32, 42, 5, 7, 28, 1], 'cur_cost': 69771.0, 'intermediate_solutions': [{'tour': [4, 47, 19, 22, 40, 21, 26, 41, 27, 42, 3, 35, 5, 38, 7, 20, 1, 45, 48, 25, 37, 54, 14, 33, 12, 15, 0, 17, 11, 23, 2, 30, 44, 28, 8, 31, 52, 51, 10, 9, 46, 13, 49, 18, 36, 53, 24, 29, 32, 43, 6, 16, 34, 50, 39], 'cur_cost': 107984.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 47, 19, 22, 40, 21, 26, 41, 27, 42, 3, 35, 20, 7, 38, 34, 1, 45, 48, 25, 37, 54, 14, 33, 12, 15, 0, 17, 11, 23, 2, 30, 44, 28, 8, 31, 52, 51, 10, 9, 46, 13, 49, 18, 36, 53, 24, 29, 32, 43, 6, 16, 5, 50, 39], 'cur_cost': 105865.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 47, 19, 22, 40, 21, 26, 41, 27, 42, 3, 35, 34, 38, 7, 20, 1, 45, 48, 25, 37, 54, 14, 33, 12, 15, 0, 17, 11, 23, 2, 30, 44, 28, 8, 31, 52, 51, 10, 9, 46, 13, 49, 18, 36, 53, 24, 6, 29, 32, 43, 16, 5, 50, 39], 'cur_cost': 104296.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([10, 48, 31,  2, 26, 47, 23, 11, 35, 39, 22, 16, 38, 43, 29,  0, 24,
       41, 30, 42, 40, 18, 33, 44, 14, 13,  8, 19, 28,  9, 52, 51, 25,  4,
        1,  3, 45, 53, 49,  6,  7, 37, 17,  5, 20, 12, 32, 46, 21, 54, 36,
       15, 27, 34, 50], dtype=int64), 'cur_cost': 101158.0, 'intermediate_solutions': [{'tour': array([ 7, 12, 38,  3, 51, 29,  6, 19, 16, 15, 14, 20, 39, 54, 27, 11, 10,
       17, 30,  8, 28, 35,  2, 26, 37, 45, 49,  5, 36, 21, 48, 13, 33, 41,
        0, 42, 44, 40, 53, 18, 46, 32, 52, 22, 23, 50,  1, 25,  9, 34, 47,
        4, 43, 24, 31]), 'cur_cost': 106321.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 3,  7, 12, 38, 51, 29,  6, 19, 16, 15, 14, 20, 39, 54, 27, 11, 10,
       17, 30,  8, 28, 35,  2, 26, 37, 45, 49,  5, 36, 21, 48, 13, 33, 41,
        0, 42, 44, 40, 53, 18, 46, 32, 52, 22, 23, 50,  1, 25,  9, 34, 47,
        4, 43, 24, 31]), 'cur_cost': 103888.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([51,  3,  7, 12, 38, 29,  6, 19, 16, 15, 14, 20, 39, 54, 27, 11, 10,
       17, 30,  8, 28, 35,  2, 26, 37, 45, 49,  5, 36, 21, 48, 13, 33, 41,
        0, 42, 44, 40, 53, 18, 46, 32, 52, 22, 23, 50,  1, 25,  9, 34, 47,
        4, 43, 24, 31]), 'cur_cost': 103771.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([38,  3,  7, 12, 51, 29,  6, 19, 16, 15, 14, 20, 39, 54, 27, 11, 10,
       17, 30,  8, 28, 35,  2, 26, 37, 45, 49,  5, 36, 21, 48, 13, 33, 41,
        0, 42, 44, 40, 53, 18, 46, 32, 52, 22, 23, 50,  1, 25,  9, 34, 47,
        4, 43, 24, 31]), 'cur_cost': 100722.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([38, 51,  3,  7, 12, 29,  6, 19, 16, 15, 14, 20, 39, 54, 27, 11, 10,
       17, 30,  8, 28, 35,  2, 26, 37, 45, 49,  5, 36, 21, 48, 13, 33, 41,
        0, 42, 44, 40, 53, 18, 46, 32, 52, 22, 23, 50,  1, 25,  9, 34, 47,
        4, 43, 24, 31]), 'cur_cost': 103569.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 16, 12, 21, 19, 17, 13, 14, 11, 18, 20, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 5, 3, 2, 1, 4, 6], 'cur_cost': 10795.0, 'intermediate_solutions': [{'tour': [9, 10, 7, 2, 4, 26, 33, 45, 6, 13, 19, 30, 36, 46, 50, 43, 5, 25, 41, 44, 54, 47, 24, 52, 17, 14, 20, 40, 31, 38, 35, 51, 48, 28, 27, 0, 23, 18, 1, 39, 53, 21, 3, 29, 22, 49, 8, 12, 11, 37, 15, 34, 42, 16, 32], 'cur_cost': 90012.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 10, 7, 2, 4, 26, 33, 25, 6, 13, 19, 30, 36, 46, 50, 52, 24, 47, 54, 44, 41, 45, 5, 43, 17, 14, 20, 40, 31, 38, 35, 51, 48, 28, 27, 0, 23, 18, 1, 39, 53, 21, 3, 29, 22, 49, 8, 12, 11, 37, 15, 34, 42, 16, 32], 'cur_cost': 89515.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 10, 2, 4, 26, 33, 25, 6, 13, 19, 30, 36, 46, 50, 43, 5, 45, 41, 44, 54, 47, 24, 52, 17, 14, 20, 40, 31, 38, 35, 51, 48, 28, 27, 0, 23, 18, 1, 39, 53, 21, 3, 29, 22, 49, 7, 8, 12, 11, 37, 15, 34, 42, 16, 32], 'cur_cost': 89988.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [3, 31, 23, 0, 35, 10, 26, 27, 36, 22, 24, 28, 6, 39, 41, 37, 38, 7, 21, 53, 17, 14, 54, 13, 45, 18, 51, 15, 46, 19, 32, 34, 4, 42, 43, 2, 40, 5, 29, 44, 50, 12, 52, 11, 48, 30, 1, 33, 25, 9, 16, 20, 47, 49, 8], 'cur_cost': 70474.0, 'intermediate_solutions': [{'tour': [40, 32, 36, 34, 4, 37, 39, 7, 3, 42, 9, 23, 30, 28, 38, 46, 47, 49, 12, 16, 13, 24, 5, 22, 2, 8, 29, 1, 31, 10, 0, 21, 14, 53, 17, 20, 48, 18, 50, 52, 45, 51, 44, 54, 15, 27, 25, 26, 43, 41, 35, 6, 33, 19, 11], 'cur_cost': 59079.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [40, 32, 36, 34, 4, 37, 39, 35, 41, 43, 26, 25, 27, 15, 54, 44, 51, 45, 52, 12, 18, 48, 20, 17, 53, 14, 21, 0, 10, 31, 1, 29, 8, 2, 22, 5, 24, 13, 16, 50, 49, 47, 46, 38, 28, 30, 23, 9, 42, 3, 7, 6, 33, 19, 11], 'cur_cost': 54661.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [40, 32, 36, 34, 4, 29, 37, 39, 7, 3, 42, 9, 23, 30, 28, 38, 46, 47, 49, 50, 16, 13, 24, 5, 22, 2, 8, 1, 31, 10, 0, 21, 14, 53, 17, 20, 48, 18, 12, 52, 45, 51, 44, 54, 15, 27, 25, 26, 43, 41, 35, 6, 33, 19, 11], 'cur_cost': 56494.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [26, 36, 29, 1, 2, 9, 3, 23, 8, 32, 39, 33, 30, 53, 21, 16, 19, 48, 46, 44, 50, 24, 31, 6, 38, 41, 4, 28, 37, 7, 40, 22, 27, 0, 42, 52, 17, 15, 18, 45, 54, 51, 47, 13, 20, 49, 25, 35, 43, 5, 10, 12, 14, 11, 34], 'cur_cost': 56435.0, 'intermediate_solutions': [{'tour': [0, 3, 19, 8, 7, 9, 5, 29, 4, 1, 10, 2, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 6, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 11], 'cur_cost': 21970.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 19, 8, 7, 9, 5, 6, 4, 1, 10, 2, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 18, 20, 15, 16, 14, 17, 13, 48, 12, 21, 11], 'cur_cost': 18925.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 19, 8, 7, 9, 5, 6, 4, 1, 10, 2, 26, 31, 25, 23, 22, 32, 28, 24, 30, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 14, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 16, 15, 20, 18, 12, 21, 11], 'cur_cost': 18879.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 13, 12, 21, 19, 17, 14, 16, 15, 20, 18, 11, 51, 45, 52, 50, 48, 47, 54, 44, 53, 46, 49, 30, 24, 26, 31, 25, 23, 22, 32, 28, 27, 29, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 10, 8, 7, 9, 3, 2, 1, 4, 6], 'cur_cost': 10817.0, 'intermediate_solutions': [{'tour': [0, 1, 6, 5, 9, 7, 3, 2, 8, 4, 10, 31, 25, 23, 22, 32, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 21, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 44, 19, 11], 'cur_cost': 15469.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 6, 5, 9, 7, 3, 2, 8, 4, 10, 31, 47, 53, 54, 44, 46, 49, 41, 40, 34, 39, 43, 33, 42, 37, 38, 36, 35, 27, 29, 26, 30, 24, 28, 32, 22, 23, 25, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11], 'cur_cost': 14771.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 6, 5, 9, 7, 3, 2, 8, 4, 10, 31, 25, 23, 22, 28, 24, 30, 26, 29, 27, 35, 36, 38, 37, 42, 33, 43, 39, 34, 40, 41, 49, 46, 44, 54, 53, 47, 51, 45, 52, 50, 48, 13, 17, 14, 16, 15, 20, 18, 12, 21, 19, 11, 32], 'cur_cost': 11700.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 5,  2, 13,  0, 11,  8,  9,  1, 27, 49, 28, 14, 35, 17,  6, 44, 52,
       50, 22, 32, 54, 42, 12, 48, 34, 46, 21, 23, 40,  4, 30, 41, 19,  7,
       24, 38, 10, 16, 31, 39, 47, 45, 25,  3, 53, 15, 43, 29, 26, 20, 33,
       18, 51, 36, 37], dtype=int64), 'cur_cost': 108948.0, 'intermediate_solutions': [{'tour': array([ 8, 36, 11, 47, 30, 10, 44, 31, 25, 13, 19, 16, 50, 51, 34, 40, 18,
       17, 54, 24, 29, 27,  4, 37, 28,  0, 45,  5, 21, 42, 52,  3, 14, 41,
        1, 48, 35, 15, 23,  6,  7, 38, 33, 39, 26, 43, 20, 53, 46, 22,  2,
       12, 32, 49,  9]), 'cur_cost': 99207.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([47,  8, 36, 11, 30, 10, 44, 31, 25, 13, 19, 16, 50, 51, 34, 40, 18,
       17, 54, 24, 29, 27,  4, 37, 28,  0, 45,  5, 21, 42, 52,  3, 14, 41,
        1, 48, 35, 15, 23,  6,  7, 38, 33, 39, 26, 43, 20, 53, 46, 22,  2,
       12, 32, 49,  9]), 'cur_cost': 104512.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([30, 47,  8, 36, 11, 10, 44, 31, 25, 13, 19, 16, 50, 51, 34, 40, 18,
       17, 54, 24, 29, 27,  4, 37, 28,  0, 45,  5, 21, 42, 52,  3, 14, 41,
        1, 48, 35, 15, 23,  6,  7, 38, 33, 39, 26, 43, 20, 53, 46, 22,  2,
       12, 32, 49,  9]), 'cur_cost': 104048.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([11, 47,  8, 36, 30, 10, 44, 31, 25, 13, 19, 16, 50, 51, 34, 40, 18,
       17, 54, 24, 29, 27,  4, 37, 28,  0, 45,  5, 21, 42, 52,  3, 14, 41,
        1, 48, 35, 15, 23,  6,  7, 38, 33, 39, 26, 43, 20, 53, 46, 22,  2,
       12, 32, 49,  9]), 'cur_cost': 100795.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([11, 30, 47,  8, 36, 10, 44, 31, 25, 13, 19, 16, 50, 51, 34, 40, 18,
       17, 54, 24, 29, 27,  4, 37, 28,  0, 45,  5, 21, 42, 52,  3, 14, 41,
        1, 48, 35, 15, 23,  6,  7, 38, 33, 39, 26, 43, 20, 53, 46, 22,  2,
       12, 32, 49,  9]), 'cur_cost': 103675.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:29:03,458 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:03,459 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:03,462 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10795.000, 多样性=0.949
2025-08-05 10:29:03,462 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 10:29:03,462 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 10:29:03,462 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:03,463 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.06197019627452271, 'best_improvement': -0.008972801196373493}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.005084745762712245}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.05360597796548192, 'recent_improvements': [0.004309059985786012, -0.2764316519785348, 0.11152101591674986], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 5, 'new_count': 5, 'count_change': 0, 'old_best_cost': 10442, 'new_best_cost': 10442, 'quality_improvement': 0.0, 'old_diversity': 0.9309090909090909, 'new_diversity': 0.9309090909090909, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:29:03,463 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 10:29:03,466 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite10_55_solution.json
2025-08-05 10:29:03,466 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite10_55_20250805_102903.solution
2025-08-05 10:29:03,467 - __main__ - INFO - 实例执行完成 - 运行时间: 1.73s, 最佳成本: 10442
2025-08-05 10:29:03,467 - __main__ - INFO - 实例 composite10_55 处理完成
