2025-08-05 10:28:35,880 - __main__ - INFO - simple6_12 开始进化第 1 代
2025-08-05 10:28:35,880 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 10:28:35,881 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:35,882 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=948.000, 多样性=0.906
2025-08-05 10:28:35,883 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:35,884 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.906
2025-08-05 10:28:35,885 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:35,887 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 10:28:35,887 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:35,887 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 10:28:35,887 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 10:28:35,893 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.300, 适应度梯度: -15.080, 聚类评分: 0.000, 覆盖率: 0.028, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:35,893 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 10:28:35,894 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 10:28:35,894 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple6_12
2025-08-05 10:28:35,897 - visualization.landscape_visualizer - INFO - 插值约束: 199 个点被约束到最小值 948.00
2025-08-05 10:28:35,898 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.7%, 梯度: 25.26 → 23.33
2025-08-05 10:28:36,020 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple6_12\landscape_simple6_12_iter_26_20250805_102835.html
2025-08-05 10:28:36,066 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple6_12\dashboard_simple6_12_iter_26_20250805_102835.html
2025-08-05 10:28:36,066 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 26
2025-08-05 10:28:36,066 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 10:28:36,066 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1793秒
2025-08-05 10:28:36,066 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 52, 'max_size': 500, 'hits': 0, 'misses': 52, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 80, 'max_size': 100, 'hits': 179, 'misses': 80, 'hit_rate': 0.6911196911196911, 'evictions': 0, 'ttl': 7200}}
2025-08-05 10:28:36,066 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -15.079999999999995, 'local_optima_density': 0.3, 'gradient_variance': 87250.28159999999, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0283, 'fitness_entropy': 0.9464119282150146, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -15.080)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.028)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360915.8934848, 'performance_metrics': {}}}
2025-08-05 10:28:36,066 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:36,067 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:36,067 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:36,067 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:36,067 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:36,067 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 10:28:36,067 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:36,068 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:36,068 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:36,068 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:36,068 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:36,068 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:36,068 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:36,068 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:36,068 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:36,068 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:36,069 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:36,069 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,070 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1074.0, 路径长度: 12, 收集中间解: 0
2025-08-05 10:28:36,070 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [4, 5, 2, 7, 0, 11, 10, 1, 3, 8, 9, 6], 'cur_cost': 1074.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:36,070 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1074.00)
2025-08-05 10:28:36,070 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:36,070 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:36,070 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:36,071 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:36,071 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,071 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1346.0, 路径长度: 12, 收集中间解: 0
2025-08-05 10:28:36,071 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [3, 9, 2, 7, 1, 8, 5, 6, 10, 0, 11, 4], 'cur_cost': 1346.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:36,072 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 1346.00)
2025-08-05 10:28:36,072 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:36,072 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:36,072 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:36,073 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:36,073 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,074 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1120.0, 路径长度: 12, 收集中间解: 0
2025-08-05 10:28:36,074 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [11, 0, 7, 3, 10, 1, 6, 5, 4, 2, 8, 9], 'cur_cost': 1120.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:36,074 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 1120.00)
2025-08-05 10:28:36,074 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:36,075 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:36,075 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:36,076 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:36,076 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,076 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1169.0, 路径长度: 12, 收集中间解: 0
2025-08-05 10:28:36,076 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [1, 9, 3, 11, 4, 2, 5, 6, 8, 7, 10, 0], 'cur_cost': 1169.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:36,076 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1169.00)
2025-08-05 10:28:36,076 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:36,076 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:36,076 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:36,077 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:36,077 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,077 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1251.0, 路径长度: 12, 收集中间解: 0
2025-08-05 10:28:36,078 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [11, 10, 0, 3, 1, 5, 6, 8, 7, 4, 2, 9], 'cur_cost': 1251.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:36,078 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1251.00)
2025-08-05 10:28:36,078 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:28:36,078 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:36,078 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:36,078 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 1558.0
2025-08-05 10:28:36,083 - ExploitationExpert - INFO - res_population_num: 1
2025-08-05 10:28:36,083 - ExploitationExpert - INFO - res_population_costs: [845.0]
2025-08-05 10:28:36,084 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  7,  3,  8,  9,  1,  5,  6,  2,  4, 10], dtype=int64)]
2025-08-05 10:28:36,084 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:36,084 - ExploitationExpert - INFO - populations: [{'tour': [4, 5, 2, 7, 0, 11, 10, 1, 3, 8, 9, 6], 'cur_cost': 1074.0}, {'tour': [3, 9, 2, 7, 1, 8, 5, 6, 10, 0, 11, 4], 'cur_cost': 1346.0}, {'tour': [11, 0, 7, 3, 10, 1, 6, 5, 4, 2, 8, 9], 'cur_cost': 1120.0}, {'tour': [1, 9, 3, 11, 4, 2, 5, 6, 8, 7, 10, 0], 'cur_cost': 1169.0}, {'tour': [11, 10, 0, 3, 1, 5, 6, 8, 7, 4, 2, 9], 'cur_cost': 1251.0}, {'tour': array([ 2, 10, 11,  9,  7,  5,  6,  0,  3,  1,  4,  8], dtype=int64), 'cur_cost': 1558.0}, {'tour': array([ 0,  8, 11,  9,  7,  5,  1,  4,  6, 10,  2,  3], dtype=int64), 'cur_cost': 1570.0}, {'tour': array([10,  6,  5,  9,  0,  1,  3,  2, 11,  7,  4,  8], dtype=int64), 'cur_cost': 1682.0}, {'tour': array([10,  6,  2,  5,  4,  7,  3,  8,  9, 11,  1,  0], dtype=int64), 'cur_cost': 1216.0}, {'tour': array([ 8,  3,  1,  0, 11,  7,  9,  4,  5,  6,  2, 10], dtype=int64), 'cur_cost': 1275.0}]
2025-08-05 10:28:36,085 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:36,085 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 66, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 66, 'cache_hits': 0, 'similarity_calculations': 457, 'cache_hit_rate': 0.0, 'cache_size': 457}}
2025-08-05 10:28:36,086 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([ 2, 10, 11,  9,  7,  5,  6,  0,  3,  1,  4,  8], dtype=int64), 'cur_cost': 1558.0, 'intermediate_solutions': [{'tour': array([ 4,  8,  5,  6,  0,  2,  3,  1, 10, 11,  9,  7], dtype=int64), 'cur_cost': 1646.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 6,  4,  8,  5,  0,  2,  3,  1, 10, 11,  9,  7], dtype=int64), 'cur_cost': 1651.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 0,  6,  4,  8,  5,  2,  3,  1, 10, 11,  9,  7], dtype=int64), 'cur_cost': 1564.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 5,  6,  4,  8,  0,  2,  3,  1, 10, 11,  9,  7], dtype=int64), 'cur_cost': 1597.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 5,  0,  6,  4,  8,  2,  3,  1, 10, 11,  9,  7], dtype=int64), 'cur_cost': 1671.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:36,086 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1558.00)
2025-08-05 10:28:36,087 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:36,087 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:36,087 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:36,088 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:36,088 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,088 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1082.0, 路径长度: 12, 收集中间解: 0
2025-08-05 10:28:36,088 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [3, 7, 10, 4, 2, 5, 8, 1, 6, 9, 0, 11], 'cur_cost': 1082.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:36,088 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1082.00)
2025-08-05 10:28:36,088 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:28:36,089 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:36,089 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:36,089 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 1214.0
2025-08-05 10:28:36,093 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 10:28:36,094 - ExploitationExpert - INFO - res_population_costs: [845.0, 845.0]
2025-08-05 10:28:36,094 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  7,  3,  8,  9,  1,  5,  6,  2,  4, 10], dtype=int64), array([ 0, 10,  4,  2,  6,  9,  8,  5,  1,  7,  3, 11], dtype=int64)]
2025-08-05 10:28:36,094 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:36,094 - ExploitationExpert - INFO - populations: [{'tour': [4, 5, 2, 7, 0, 11, 10, 1, 3, 8, 9, 6], 'cur_cost': 1074.0}, {'tour': [3, 9, 2, 7, 1, 8, 5, 6, 10, 0, 11, 4], 'cur_cost': 1346.0}, {'tour': [11, 0, 7, 3, 10, 1, 6, 5, 4, 2, 8, 9], 'cur_cost': 1120.0}, {'tour': [1, 9, 3, 11, 4, 2, 5, 6, 8, 7, 10, 0], 'cur_cost': 1169.0}, {'tour': [11, 10, 0, 3, 1, 5, 6, 8, 7, 4, 2, 9], 'cur_cost': 1251.0}, {'tour': array([ 2, 10, 11,  9,  7,  5,  6,  0,  3,  1,  4,  8], dtype=int64), 'cur_cost': 1558.0}, {'tour': [3, 7, 10, 4, 2, 5, 8, 1, 6, 9, 0, 11], 'cur_cost': 1082.0}, {'tour': array([ 7,  1,  3, 11, 10,  8,  9,  5,  4,  6,  2,  0], dtype=int64), 'cur_cost': 1214.0}, {'tour': array([10,  6,  2,  5,  4,  7,  3,  8,  9, 11,  1,  0], dtype=int64), 'cur_cost': 1216.0}, {'tour': array([ 8,  3,  1,  0, 11,  7,  9,  4,  5,  6,  2, 10], dtype=int64), 'cur_cost': 1275.0}]
2025-08-05 10:28:36,095 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:36,095 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 67, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 67, 'cache_hits': 0, 'similarity_calculations': 471, 'cache_hit_rate': 0.0, 'cache_size': 471}}
2025-08-05 10:28:36,096 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([ 7,  1,  3, 11, 10,  8,  9,  5,  4,  6,  2,  0], dtype=int64), 'cur_cost': 1214.0, 'intermediate_solutions': [{'tour': array([ 5,  6, 10,  9,  0,  1,  3,  2, 11,  7,  4,  8], dtype=int64), 'cur_cost': 1698.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9,  5,  6, 10,  0,  1,  3,  2, 11,  7,  4,  8], dtype=int64), 'cur_cost': 1442.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 0,  9,  5,  6, 10,  1,  3,  2, 11,  7,  4,  8], dtype=int64), 'cur_cost': 1662.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([10,  9,  5,  6,  0,  1,  3,  2, 11,  7,  4,  8], dtype=int64), 'cur_cost': 1708.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([10,  0,  9,  5,  6,  1,  3,  2, 11,  7,  4,  8], dtype=int64), 'cur_cost': 1591.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:36,096 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1214.00)
2025-08-05 10:28:36,096 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:36,096 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:36,096 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:36,097 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 12
2025-08-05 10:28:36,097 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,097 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1491.0, 路径长度: 12, 收集中间解: 0
2025-08-05 10:28:36,097 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [4, 0, 1, 10, 6, 8, 2, 5, 3, 11, 9, 7], 'cur_cost': 1491.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:36,097 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1491.00)
2025-08-05 10:28:36,097 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:36,097 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:36,097 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:36,098 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:36,098 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,098 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1243.0, 路径长度: 12, 收集中间解: 0
2025-08-05 10:28:36,098 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 7, 10, 2, 1, 3, 5, 6, 9, 8, 11, 4], 'cur_cost': 1243.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:36,098 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1243.00)
2025-08-05 10:28:36,099 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:36,099 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:36,100 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [4, 5, 2, 7, 0, 11, 10, 1, 3, 8, 9, 6], 'cur_cost': 1074.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [3, 9, 2, 7, 1, 8, 5, 6, 10, 0, 11, 4], 'cur_cost': 1346.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [11, 0, 7, 3, 10, 1, 6, 5, 4, 2, 8, 9], 'cur_cost': 1120.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [1, 9, 3, 11, 4, 2, 5, 6, 8, 7, 10, 0], 'cur_cost': 1169.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [11, 10, 0, 3, 1, 5, 6, 8, 7, 4, 2, 9], 'cur_cost': 1251.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 2, 10, 11,  9,  7,  5,  6,  0,  3,  1,  4,  8], dtype=int64), 'cur_cost': 1558.0, 'intermediate_solutions': [{'tour': array([ 4,  8,  5,  6,  0,  2,  3,  1, 10, 11,  9,  7], dtype=int64), 'cur_cost': 1646.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 6,  4,  8,  5,  0,  2,  3,  1, 10, 11,  9,  7], dtype=int64), 'cur_cost': 1651.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 0,  6,  4,  8,  5,  2,  3,  1, 10, 11,  9,  7], dtype=int64), 'cur_cost': 1564.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 5,  6,  4,  8,  0,  2,  3,  1, 10, 11,  9,  7], dtype=int64), 'cur_cost': 1597.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 5,  0,  6,  4,  8,  2,  3,  1, 10, 11,  9,  7], dtype=int64), 'cur_cost': 1671.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [3, 7, 10, 4, 2, 5, 8, 1, 6, 9, 0, 11], 'cur_cost': 1082.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 7,  1,  3, 11, 10,  8,  9,  5,  4,  6,  2,  0], dtype=int64), 'cur_cost': 1214.0, 'intermediate_solutions': [{'tour': array([ 5,  6, 10,  9,  0,  1,  3,  2, 11,  7,  4,  8], dtype=int64), 'cur_cost': 1698.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9,  5,  6, 10,  0,  1,  3,  2, 11,  7,  4,  8], dtype=int64), 'cur_cost': 1442.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 0,  9,  5,  6, 10,  1,  3,  2, 11,  7,  4,  8], dtype=int64), 'cur_cost': 1662.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([10,  9,  5,  6,  0,  1,  3,  2, 11,  7,  4,  8], dtype=int64), 'cur_cost': 1708.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([10,  0,  9,  5,  6,  1,  3,  2, 11,  7,  4,  8], dtype=int64), 'cur_cost': 1591.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [4, 0, 1, 10, 6, 8, 2, 5, 3, 11, 9, 7], 'cur_cost': 1491.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 10, 2, 1, 3, 5, 6, 9, 8, 11, 4], 'cur_cost': 1243.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:36,100 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:36,100 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:36,101 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1074.000, 多样性=0.913
2025-08-05 10:28:36,101 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 10:28:36,102 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 10:28:36,102 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:36,102 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.004881784940588478, 'best_improvement': -0.13291139240506328}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.00817995910020451}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.012903324886395793, 'recent_improvements': [0.03684674797197012, -0.11938174269352904, 0.06265339774476171], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 845.0, 'new_best_cost': 845.0, 'quality_improvement': 0.0, 'old_diversity': 0.75, 'new_diversity': 0.75, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 10:28:36,102 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 10:28:36,102 - __main__ - INFO - simple6_12 开始进化第 2 代
2025-08-05 10:28:36,102 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 10:28:36,102 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:36,103 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1074.000, 多样性=0.913
2025-08-05 10:28:36,103 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:36,103 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.913
2025-08-05 10:28:36,104 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:36,104 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.750
2025-08-05 10:28:36,106 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 10:28:36,106 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:36,106 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-05 10:28:36,106 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-05 10:28:36,113 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.250, 适应度梯度: 10.933, 聚类评分: 0.000, 覆盖率: 0.029, 收敛趋势: 0.000, 多样性: 0.910
2025-08-05 10:28:36,113 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 10:28:36,113 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:36,113 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple6_12
2025-08-05 10:28:36,117 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=8.1%, 梯度: 18.93 → 17.40
2025-08-05 10:28:36,257 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple6_12\landscape_simple6_12_iter_27_20250805_102836.html
2025-08-05 10:28:36,325 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple6_12\dashboard_simple6_12_iter_27_20250805_102836.html
2025-08-05 10:28:36,325 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 27
2025-08-05 10:28:36,325 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 10:28:36,325 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2196秒
2025-08-05 10:28:36,326 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.25, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 10.933333333333332, 'local_optima_density': 0.25, 'gradient_variance': 51942.20888888888, 'cluster_count': 0}, 'population_state': {'diversity': 0.9103535353535354, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0295, 'fitness_entropy': 0.9513282751069652, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.029)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.910)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 10.933)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360916.113297, 'performance_metrics': {}}}
2025-08-05 10:28:36,326 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:36,326 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:36,326 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:36,326 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:36,326 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 10:28:36,327 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 10:28:36,327 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 10:28:36,327 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:36,327 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:36,327 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 10:28:36,327 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:36,327 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:36,328 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 6} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:36,328 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:36,328 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:36,328 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:36,328 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:36,329 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,329 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,329 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,329 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,329 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1218.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:36,329 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [8, 7, 3, 1, 10, 0, 4, 2, 6, 5, 9, 11], 'cur_cost': 1218.0, 'intermediate_solutions': [{'tour': [4, 0, 2, 7, 5, 11, 10, 1, 3, 8, 9, 6], 'cur_cost': 1300.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 5, 2, 7, 0, 11, 10, 1, 3, 6, 9, 8], 'cur_cost': 1208.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 5, 2, 7, 0, 10, 1, 3, 8, 9, 11, 6], 'cur_cost': 1289.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:36,329 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 1218.00)
2025-08-05 10:28:36,329 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:36,330 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:36,330 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:36,330 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:36,330 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,330 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,330 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,330 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,330 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1047.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:36,331 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 8, 9, 3, 10, 7, 1, 5, 6, 2, 4, 11], 'cur_cost': 1047.0, 'intermediate_solutions': [{'tour': [10, 9, 2, 7, 1, 8, 5, 6, 3, 0, 11, 4], 'cur_cost': 1360.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 0, 10, 6, 5, 8, 1, 7, 2, 9, 11, 4], 'cur_cost': 1478.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 9, 2, 7, 1, 8, 5, 6, 10, 0, 11, 4], 'cur_cost': 1346.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:36,331 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1047.00)
2025-08-05 10:28:36,331 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:36,331 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:36,331 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:36,331 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:36,331 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,331 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,332 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,332 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,332 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1205.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:36,332 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 6, 5, 1, 11, 3, 7, 10, 4, 2, 8, 9], 'cur_cost': 1205.0, 'intermediate_solutions': [{'tour': [1, 0, 7, 3, 10, 11, 6, 5, 4, 2, 8, 9], 'cur_cost': 1236.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 0, 7, 3, 10, 1, 6, 9, 8, 2, 4, 5], 'cur_cost': 1104.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 3, 0, 7, 10, 1, 6, 5, 4, 2, 8, 9], 'cur_cost': 1157.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:36,332 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1205.00)
2025-08-05 10:28:36,333 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:36,333 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:36,333 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:36,333 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 12
2025-08-05 10:28:36,333 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,334 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,334 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,334 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,334 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1289.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:36,335 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [1, 6, 5, 4, 11, 0, 7, 2, 8, 9, 10, 3], 'cur_cost': 1289.0, 'intermediate_solutions': [{'tour': [1, 9, 3, 11, 4, 2, 5, 10, 8, 7, 6, 0], 'cur_cost': 1388.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 9, 3, 11, 4, 2, 7, 8, 6, 5, 10, 0], 'cur_cost': 1242.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 9, 3, 11, 2, 5, 6, 8, 7, 10, 4, 0], 'cur_cost': 1279.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:36,335 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1289.00)
2025-08-05 10:28:36,335 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:36,335 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:36,335 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:36,336 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:36,336 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,337 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,337 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,337 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,337 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1278.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:36,337 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [2, 1, 3, 7, 0, 10, 5, 4, 6, 8, 9, 11], 'cur_cost': 1278.0, 'intermediate_solutions': [{'tour': [4, 10, 0, 3, 1, 5, 6, 8, 7, 11, 2, 9], 'cur_cost': 1330.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 10, 0, 3, 1, 8, 6, 5, 7, 4, 2, 9], 'cur_cost': 1275.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 6, 10, 0, 3, 1, 5, 8, 7, 4, 2, 9], 'cur_cost': 1412.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:36,338 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1278.00)
2025-08-05 10:28:36,338 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:28:36,338 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:36,338 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:36,338 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 1537.0
2025-08-05 10:28:36,343 - ExploitationExpert - INFO - res_population_num: 3
2025-08-05 10:28:36,343 - ExploitationExpert - INFO - res_population_costs: [845.0, 845.0, 845]
2025-08-05 10:28:36,344 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  7,  3,  8,  9,  1,  5,  6,  2,  4, 10], dtype=int64), array([ 0, 10,  4,  2,  6,  9,  8,  5,  1,  7,  3, 11], dtype=int64), array([ 0, 11,  3,  8,  9,  6,  2,  4, 10,  5,  1,  7], dtype=int64)]
2025-08-05 10:28:36,344 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:36,344 - ExploitationExpert - INFO - populations: [{'tour': [8, 7, 3, 1, 10, 0, 4, 2, 6, 5, 9, 11], 'cur_cost': 1218.0}, {'tour': [0, 8, 9, 3, 10, 7, 1, 5, 6, 2, 4, 11], 'cur_cost': 1047.0}, {'tour': [0, 6, 5, 1, 11, 3, 7, 10, 4, 2, 8, 9], 'cur_cost': 1205.0}, {'tour': [1, 6, 5, 4, 11, 0, 7, 2, 8, 9, 10, 3], 'cur_cost': 1289.0}, {'tour': [2, 1, 3, 7, 0, 10, 5, 4, 6, 8, 9, 11], 'cur_cost': 1278.0}, {'tour': array([ 1, 11,  3,  0,  2,  9,  5,  6,  7,  4,  8, 10], dtype=int64), 'cur_cost': 1537.0}, {'tour': [3, 7, 10, 4, 2, 5, 8, 1, 6, 9, 0, 11], 'cur_cost': 1082.0}, {'tour': [7, 1, 3, 11, 10, 8, 9, 5, 4, 6, 2, 0], 'cur_cost': 1214.0}, {'tour': [4, 0, 1, 10, 6, 8, 2, 5, 3, 11, 9, 7], 'cur_cost': 1491.0}, {'tour': [0, 7, 10, 2, 1, 3, 5, 6, 9, 8, 11, 4], 'cur_cost': 1243.0}]
2025-08-05 10:28:36,345 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:36,345 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 68, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 68, 'cache_hits': 0, 'similarity_calculations': 486, 'cache_hit_rate': 0.0, 'cache_size': 486}}
2025-08-05 10:28:36,345 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([ 1, 11,  3,  0,  2,  9,  5,  6,  7,  4,  8, 10], dtype=int64), 'cur_cost': 1537.0, 'intermediate_solutions': [{'tour': array([11, 10,  2,  9,  7,  5,  6,  0,  3,  1,  4,  8]), 'cur_cost': 1511.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9, 11, 10,  2,  7,  5,  6,  0,  3,  1,  4,  8]), 'cur_cost': 1439.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 7,  9, 11, 10,  2,  5,  6,  0,  3,  1,  4,  8]), 'cur_cost': 1488.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2,  9, 11, 10,  7,  5,  6,  0,  3,  1,  4,  8]), 'cur_cost': 1540.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2,  7,  9, 11, 10,  5,  6,  0,  3,  1,  4,  8]), 'cur_cost': 1586.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:36,346 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1537.00)
2025-08-05 10:28:36,346 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:36,346 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:36,346 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:36,346 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:36,346 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,346 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,347 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,347 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,347 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1367.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:36,347 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [4, 9, 2, 11, 10, 0, 7, 1, 5, 6, 8, 3], 'cur_cost': 1367.0, 'intermediate_solutions': [{'tour': [3, 7, 8, 4, 2, 5, 10, 1, 6, 9, 0, 11], 'cur_cost': 1189.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 7, 10, 9, 6, 1, 8, 5, 2, 4, 0, 11], 'cur_cost': 1113.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 7, 10, 4, 5, 2, 8, 1, 6, 9, 0, 11], 'cur_cost': 1171.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:36,347 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 1367.00)
2025-08-05 10:28:36,347 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:36,348 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:36,348 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:36,348 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:36,348 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,348 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,348 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,348 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,349 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1364.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:36,349 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 9, 2, 3, 4, 10, 7, 1, 5, 6, 8, 11], 'cur_cost': 1364.0, 'intermediate_solutions': [{'tour': [7, 1, 3, 11, 6, 8, 9, 5, 4, 10, 2, 0], 'cur_cost': 1256.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 1, 3, 11, 10, 8, 9, 5, 4, 2, 6, 0], 'cur_cost': 1158.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 1, 3, 11, 10, 8, 9, 5, 4, 6, 2, 0], 'cur_cost': 1214.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:36,349 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1364.00)
2025-08-05 10:28:36,349 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:36,349 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:36,349 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:36,349 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 1494.0
2025-08-05 10:28:36,355 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:36,356 - ExploitationExpert - INFO - res_population_costs: [845.0, 845.0, 845, 845]
2025-08-05 10:28:36,356 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  7,  3,  8,  9,  1,  5,  6,  2,  4, 10], dtype=int64), array([ 0, 10,  4,  2,  6,  9,  8,  5,  1,  7,  3, 11], dtype=int64), array([ 0, 11,  3,  8,  9,  6,  2,  4, 10,  5,  1,  7], dtype=int64), array([ 0, 11,  3,  8,  9,  6,  2,  4,  5,  1,  7, 10], dtype=int64)]
2025-08-05 10:28:36,357 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:36,357 - ExploitationExpert - INFO - populations: [{'tour': [8, 7, 3, 1, 10, 0, 4, 2, 6, 5, 9, 11], 'cur_cost': 1218.0}, {'tour': [0, 8, 9, 3, 10, 7, 1, 5, 6, 2, 4, 11], 'cur_cost': 1047.0}, {'tour': [0, 6, 5, 1, 11, 3, 7, 10, 4, 2, 8, 9], 'cur_cost': 1205.0}, {'tour': [1, 6, 5, 4, 11, 0, 7, 2, 8, 9, 10, 3], 'cur_cost': 1289.0}, {'tour': [2, 1, 3, 7, 0, 10, 5, 4, 6, 8, 9, 11], 'cur_cost': 1278.0}, {'tour': array([ 1, 11,  3,  0,  2,  9,  5,  6,  7,  4,  8, 10], dtype=int64), 'cur_cost': 1537.0}, {'tour': [4, 9, 2, 11, 10, 0, 7, 1, 5, 6, 8, 3], 'cur_cost': 1367.0}, {'tour': [0, 9, 2, 3, 4, 10, 7, 1, 5, 6, 8, 11], 'cur_cost': 1364.0}, {'tour': array([ 5,  6,  3, 10,  7,  9,  2,  1,  0,  4, 11,  8], dtype=int64), 'cur_cost': 1494.0}, {'tour': [0, 7, 10, 2, 1, 3, 5, 6, 9, 8, 11, 4], 'cur_cost': 1243.0}]
2025-08-05 10:28:36,357 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:36,357 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 69, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 69, 'cache_hits': 0, 'similarity_calculations': 502, 'cache_hit_rate': 0.0, 'cache_size': 502}}
2025-08-05 10:28:36,358 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 5,  6,  3, 10,  7,  9,  2,  1,  0,  4, 11,  8], dtype=int64), 'cur_cost': 1494.0, 'intermediate_solutions': [{'tour': array([ 1,  0,  4, 10,  6,  8,  2,  5,  3, 11,  9,  7]), 'cur_cost': 1410.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([10,  1,  0,  4,  6,  8,  2,  5,  3, 11,  9,  7]), 'cur_cost': 1428.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 6, 10,  1,  0,  4,  8,  2,  5,  3, 11,  9,  7]), 'cur_cost': 1566.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4, 10,  1,  0,  6,  8,  2,  5,  3, 11,  9,  7]), 'cur_cost': 1486.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4,  6, 10,  1,  0,  8,  2,  5,  3, 11,  9,  7]), 'cur_cost': 1543.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:36,358 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1494.00)
2025-08-05 10:28:36,358 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:36,358 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:36,358 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:36,359 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:36,359 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,359 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,359 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,359 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,359 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1357.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:36,359 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [1, 9, 3, 8, 0, 6, 5, 7, 10, 4, 2, 11], 'cur_cost': 1357.0, 'intermediate_solutions': [{'tour': [0, 7, 10, 2, 1, 8, 5, 6, 9, 3, 11, 4], 'cur_cost': 1247.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 10, 2, 1, 6, 5, 3, 9, 8, 11, 4], 'cur_cost': 1267.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 10, 2, 1, 3, 5, 6, 8, 9, 11, 4], 'cur_cost': 1300.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:36,360 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1357.00)
2025-08-05 10:28:36,360 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:36,360 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:36,361 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [8, 7, 3, 1, 10, 0, 4, 2, 6, 5, 9, 11], 'cur_cost': 1218.0, 'intermediate_solutions': [{'tour': [4, 0, 2, 7, 5, 11, 10, 1, 3, 8, 9, 6], 'cur_cost': 1300.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 5, 2, 7, 0, 11, 10, 1, 3, 6, 9, 8], 'cur_cost': 1208.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 5, 2, 7, 0, 10, 1, 3, 8, 9, 11, 6], 'cur_cost': 1289.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 9, 3, 10, 7, 1, 5, 6, 2, 4, 11], 'cur_cost': 1047.0, 'intermediate_solutions': [{'tour': [10, 9, 2, 7, 1, 8, 5, 6, 3, 0, 11, 4], 'cur_cost': 1360.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 0, 10, 6, 5, 8, 1, 7, 2, 9, 11, 4], 'cur_cost': 1478.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 9, 2, 7, 1, 8, 5, 6, 10, 0, 11, 4], 'cur_cost': 1346.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 5, 1, 11, 3, 7, 10, 4, 2, 8, 9], 'cur_cost': 1205.0, 'intermediate_solutions': [{'tour': [1, 0, 7, 3, 10, 11, 6, 5, 4, 2, 8, 9], 'cur_cost': 1236.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 0, 7, 3, 10, 1, 6, 9, 8, 2, 4, 5], 'cur_cost': 1104.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 3, 0, 7, 10, 1, 6, 5, 4, 2, 8, 9], 'cur_cost': 1157.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [1, 6, 5, 4, 11, 0, 7, 2, 8, 9, 10, 3], 'cur_cost': 1289.0, 'intermediate_solutions': [{'tour': [1, 9, 3, 11, 4, 2, 5, 10, 8, 7, 6, 0], 'cur_cost': 1388.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 9, 3, 11, 4, 2, 7, 8, 6, 5, 10, 0], 'cur_cost': 1242.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 9, 3, 11, 2, 5, 6, 8, 7, 10, 4, 0], 'cur_cost': 1279.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [2, 1, 3, 7, 0, 10, 5, 4, 6, 8, 9, 11], 'cur_cost': 1278.0, 'intermediate_solutions': [{'tour': [4, 10, 0, 3, 1, 5, 6, 8, 7, 11, 2, 9], 'cur_cost': 1330.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 10, 0, 3, 1, 8, 6, 5, 7, 4, 2, 9], 'cur_cost': 1275.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 6, 10, 0, 3, 1, 5, 8, 7, 4, 2, 9], 'cur_cost': 1412.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 1, 11,  3,  0,  2,  9,  5,  6,  7,  4,  8, 10], dtype=int64), 'cur_cost': 1537.0, 'intermediate_solutions': [{'tour': array([11, 10,  2,  9,  7,  5,  6,  0,  3,  1,  4,  8]), 'cur_cost': 1511.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9, 11, 10,  2,  7,  5,  6,  0,  3,  1,  4,  8]), 'cur_cost': 1439.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 7,  9, 11, 10,  2,  5,  6,  0,  3,  1,  4,  8]), 'cur_cost': 1488.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2,  9, 11, 10,  7,  5,  6,  0,  3,  1,  4,  8]), 'cur_cost': 1540.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2,  7,  9, 11, 10,  5,  6,  0,  3,  1,  4,  8]), 'cur_cost': 1586.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [4, 9, 2, 11, 10, 0, 7, 1, 5, 6, 8, 3], 'cur_cost': 1367.0, 'intermediate_solutions': [{'tour': [3, 7, 8, 4, 2, 5, 10, 1, 6, 9, 0, 11], 'cur_cost': 1189.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 7, 10, 9, 6, 1, 8, 5, 2, 4, 0, 11], 'cur_cost': 1113.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 7, 10, 4, 5, 2, 8, 1, 6, 9, 0, 11], 'cur_cost': 1171.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 2, 3, 4, 10, 7, 1, 5, 6, 8, 11], 'cur_cost': 1364.0, 'intermediate_solutions': [{'tour': [7, 1, 3, 11, 6, 8, 9, 5, 4, 10, 2, 0], 'cur_cost': 1256.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 1, 3, 11, 10, 8, 9, 5, 4, 2, 6, 0], 'cur_cost': 1158.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 1, 3, 11, 10, 8, 9, 5, 4, 6, 2, 0], 'cur_cost': 1214.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 5,  6,  3, 10,  7,  9,  2,  1,  0,  4, 11,  8], dtype=int64), 'cur_cost': 1494.0, 'intermediate_solutions': [{'tour': array([ 1,  0,  4, 10,  6,  8,  2,  5,  3, 11,  9,  7]), 'cur_cost': 1410.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([10,  1,  0,  4,  6,  8,  2,  5,  3, 11,  9,  7]), 'cur_cost': 1428.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 6, 10,  1,  0,  4,  8,  2,  5,  3, 11,  9,  7]), 'cur_cost': 1566.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4, 10,  1,  0,  6,  8,  2,  5,  3, 11,  9,  7]), 'cur_cost': 1486.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4,  6, 10,  1,  0,  8,  2,  5,  3, 11,  9,  7]), 'cur_cost': 1543.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [1, 9, 3, 8, 0, 6, 5, 7, 10, 4, 2, 11], 'cur_cost': 1357.0, 'intermediate_solutions': [{'tour': [0, 7, 10, 2, 1, 8, 5, 6, 9, 3, 11, 4], 'cur_cost': 1247.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 10, 2, 1, 6, 5, 3, 9, 8, 11, 4], 'cur_cost': 1267.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 10, 2, 1, 3, 5, 6, 8, 9, 11, 4], 'cur_cost': 1300.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:36,361 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:36,361 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:36,363 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1047.000, 多样性=0.859
2025-08-05 10:28:36,363 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 10:28:36,363 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 10:28:36,363 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:36,363 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.0032784843597286934, 'best_improvement': 0.025139664804469275}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.05882352941176482}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.057249978876470285, 'recent_improvements': [-0.11938174269352904, 0.06265339774476171, -0.004881784940588478], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 845.0, 'new_best_cost': 845.0, 'quality_improvement': 0.0, 'old_diversity': 0.75, 'new_diversity': 0.75, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 10:28:36,363 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 10:28:36,363 - __main__ - INFO - simple6_12 开始进化第 3 代
2025-08-05 10:28:36,364 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 10:28:36,364 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:36,364 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1047.000, 多样性=0.859
2025-08-05 10:28:36,364 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:36,365 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.859
2025-08-05 10:28:36,365 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:36,366 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.750
2025-08-05 10:28:36,368 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 10:28:36,368 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:36,368 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 10:28:36,368 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 10:28:36,377 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.286, 适应度梯度: 17.671, 聚类评分: 0.000, 覆盖率: 0.031, 收敛趋势: 0.000, 多样性: 0.737
2025-08-05 10:28:36,377 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 10:28:36,378 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:36,378 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple6_12
2025-08-05 10:28:36,381 - visualization.landscape_visualizer - INFO - 插值约束: 102 个点被约束到最小值 845.00
2025-08-05 10:28:36,383 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.9%, 梯度: 27.16 → 25.01
2025-08-05 10:28:36,490 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple6_12\landscape_simple6_12_iter_28_20250805_102836.html
2025-08-05 10:28:36,563 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple6_12\dashboard_simple6_12_iter_28_20250805_102836.html
2025-08-05 10:28:36,563 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 28
2025-08-05 10:28:36,563 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 10:28:36,563 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1949秒
2025-08-05 10:28:36,564 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2857142857142857, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 17.671428571428574, 'local_optima_density': 0.2857142857142857, 'gradient_variance': 53507.3406122449, 'cluster_count': 0}, 'population_state': {'diversity': 0.7370486656200942, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0307, 'fitness_entropy': 0.9546444483376649, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.031)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.737)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 17.671)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360916.3778903, 'performance_metrics': {}}}
2025-08-05 10:28:36,564 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:36,564 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:36,564 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:36,565 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:36,565 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:28:36,565 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 10:28:36,565 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:28:36,565 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:36,565 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:36,566 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:28:36,566 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:36,566 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:36,566 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:36,566 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:36,566 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:36,566 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:36,567 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 12
2025-08-05 10:28:36,567 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,567 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,567 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,567 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,567 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1472.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:36,568 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [2, 7, 9, 4, 11, 3, 1, 5, 8, 6, 10, 0], 'cur_cost': 1472.0, 'intermediate_solutions': [{'tour': [8, 7, 3, 1, 10, 0, 4, 2, 6, 9, 5, 11], 'cur_cost': 1202.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 7, 3, 1, 10, 0, 6, 2, 4, 5, 9, 11], 'cur_cost': 1300.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 9, 7, 3, 1, 10, 0, 4, 2, 6, 5, 11], 'cur_cost': 1129.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:36,568 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1472.00)
2025-08-05 10:28:36,568 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:36,568 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:36,568 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:36,568 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:36,569 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,569 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,569 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,569 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,569 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1184.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:36,569 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [4, 8, 3, 5, 1, 9, 6, 2, 10, 0, 11, 7], 'cur_cost': 1184.0, 'intermediate_solutions': [{'tour': [0, 10, 9, 3, 8, 7, 1, 5, 6, 2, 4, 11], 'cur_cost': 1080.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 9, 10, 3, 7, 1, 5, 6, 2, 4, 11], 'cur_cost': 1097.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 9, 3, 10, 1, 7, 5, 6, 2, 4, 11], 'cur_cost': 1091.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:36,569 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 1184.00)
2025-08-05 10:28:36,569 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:36,569 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:36,570 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:36,570 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:36,570 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,570 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,570 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,570 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,570 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1125.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:36,571 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [1, 8, 10, 0, 11, 3, 7, 5, 6, 2, 4, 9], 'cur_cost': 1125.0, 'intermediate_solutions': [{'tour': [0, 6, 5, 3, 11, 1, 7, 10, 4, 2, 8, 9], 'cur_cost': 1271.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 5, 1, 11, 3, 7, 10, 4, 8, 2, 9], 'cur_cost': 1450.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 6, 5, 11, 3, 7, 10, 4, 2, 8, 9], 'cur_cost': 1206.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:36,571 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 1125.00)
2025-08-05 10:28:36,571 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:36,571 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:36,571 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:36,571 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:36,571 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,572 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,572 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,572 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,572 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1234.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:36,572 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 4, 3, 7, 10, 5, 6, 2, 8, 9, 11], 'cur_cost': 1234.0, 'intermediate_solutions': [{'tour': [1, 4, 5, 6, 11, 0, 7, 2, 8, 9, 10, 3], 'cur_cost': 1358.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 5, 4, 11, 3, 10, 9, 8, 2, 7, 0], 'cur_cost': 1353.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 5, 4, 0, 7, 2, 8, 9, 11, 10, 3], 'cur_cost': 1322.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:36,572 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1234.00)
2025-08-05 10:28:36,572 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:36,572 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:36,572 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:36,573 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:36,573 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,574 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,574 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,574 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,574 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1088.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:36,574 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [8, 7, 3, 11, 1, 6, 5, 10, 0, 4, 2, 9], 'cur_cost': 1088.0, 'intermediate_solutions': [{'tour': [2, 1, 3, 7, 0, 6, 5, 4, 10, 8, 9, 11], 'cur_cost': 1360.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 1, 3, 7, 0, 10, 5, 8, 6, 4, 9, 11], 'cur_cost': 1454.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 1, 6, 3, 7, 0, 10, 5, 4, 8, 9, 11], 'cur_cost': 1379.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:36,574 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1088.00)
2025-08-05 10:28:36,574 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:28:36,574 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:36,574 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:36,575 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 1575.0
2025-08-05 10:28:36,585 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:36,585 - ExploitationExpert - INFO - res_population_costs: [845.0, 845.0, 845, 845]
2025-08-05 10:28:36,585 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  7,  3,  8,  9,  1,  5,  6,  2,  4, 10], dtype=int64), array([ 0, 10,  4,  2,  6,  9,  8,  5,  1,  7,  3, 11], dtype=int64), array([ 0, 11,  3,  8,  9,  6,  2,  4, 10,  5,  1,  7], dtype=int64), array([ 0, 11,  3,  8,  9,  6,  2,  4,  5,  1,  7, 10], dtype=int64)]
2025-08-05 10:28:36,586 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:36,586 - ExploitationExpert - INFO - populations: [{'tour': [2, 7, 9, 4, 11, 3, 1, 5, 8, 6, 10, 0], 'cur_cost': 1472.0}, {'tour': [4, 8, 3, 5, 1, 9, 6, 2, 10, 0, 11, 7], 'cur_cost': 1184.0}, {'tour': [1, 8, 10, 0, 11, 3, 7, 5, 6, 2, 4, 9], 'cur_cost': 1125.0}, {'tour': [0, 1, 4, 3, 7, 10, 5, 6, 2, 8, 9, 11], 'cur_cost': 1234.0}, {'tour': [8, 7, 3, 11, 1, 6, 5, 10, 0, 4, 2, 9], 'cur_cost': 1088.0}, {'tour': array([ 4,  3, 11,  2, 10,  5,  6,  7,  9,  0,  8,  1], dtype=int64), 'cur_cost': 1575.0}, {'tour': [4, 9, 2, 11, 10, 0, 7, 1, 5, 6, 8, 3], 'cur_cost': 1367.0}, {'tour': [0, 9, 2, 3, 4, 10, 7, 1, 5, 6, 8, 11], 'cur_cost': 1364.0}, {'tour': [5, 6, 3, 10, 7, 9, 2, 1, 0, 4, 11, 8], 'cur_cost': 1494.0}, {'tour': [1, 9, 3, 8, 0, 6, 5, 7, 10, 4, 2, 11], 'cur_cost': 1357.0}]
2025-08-05 10:28:36,587 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:36,587 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 70, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 70, 'cache_hits': 0, 'similarity_calculations': 519, 'cache_hit_rate': 0.0, 'cache_size': 519}}
2025-08-05 10:28:36,587 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([ 4,  3, 11,  2, 10,  5,  6,  7,  9,  0,  8,  1], dtype=int64), 'cur_cost': 1575.0, 'intermediate_solutions': [{'tour': array([ 3, 11,  1,  0,  2,  9,  5,  6,  7,  4,  8, 10]), 'cur_cost': 1580.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 0,  3, 11,  1,  2,  9,  5,  6,  7,  4,  8, 10]), 'cur_cost': 1464.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 2,  0,  3, 11,  1,  9,  5,  6,  7,  4,  8, 10]), 'cur_cost': 1523.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 1,  0,  3, 11,  2,  9,  5,  6,  7,  4,  8, 10]), 'cur_cost': 1552.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 1,  2,  0,  3, 11,  9,  5,  6,  7,  4,  8, 10]), 'cur_cost': 1553.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:36,588 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1575.00)
2025-08-05 10:28:36,588 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:28:36,588 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:36,588 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:36,588 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1446.0
2025-08-05 10:28:36,596 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:36,597 - ExploitationExpert - INFO - res_population_costs: [845.0, 845.0, 845, 845]
2025-08-05 10:28:36,597 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  7,  3,  8,  9,  1,  5,  6,  2,  4, 10], dtype=int64), array([ 0, 10,  4,  2,  6,  9,  8,  5,  1,  7,  3, 11], dtype=int64), array([ 0, 11,  3,  8,  9,  6,  2,  4, 10,  5,  1,  7], dtype=int64), array([ 0, 11,  3,  8,  9,  6,  2,  4,  5,  1,  7, 10], dtype=int64)]
2025-08-05 10:28:36,598 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:36,598 - ExploitationExpert - INFO - populations: [{'tour': [2, 7, 9, 4, 11, 3, 1, 5, 8, 6, 10, 0], 'cur_cost': 1472.0}, {'tour': [4, 8, 3, 5, 1, 9, 6, 2, 10, 0, 11, 7], 'cur_cost': 1184.0}, {'tour': [1, 8, 10, 0, 11, 3, 7, 5, 6, 2, 4, 9], 'cur_cost': 1125.0}, {'tour': [0, 1, 4, 3, 7, 10, 5, 6, 2, 8, 9, 11], 'cur_cost': 1234.0}, {'tour': [8, 7, 3, 11, 1, 6, 5, 10, 0, 4, 2, 9], 'cur_cost': 1088.0}, {'tour': array([ 4,  3, 11,  2, 10,  5,  6,  7,  9,  0,  8,  1], dtype=int64), 'cur_cost': 1575.0}, {'tour': array([10,  6,  1,  9,  4, 11,  7,  8,  5,  2,  3,  0], dtype=int64), 'cur_cost': 1446.0}, {'tour': [0, 9, 2, 3, 4, 10, 7, 1, 5, 6, 8, 11], 'cur_cost': 1364.0}, {'tour': [5, 6, 3, 10, 7, 9, 2, 1, 0, 4, 11, 8], 'cur_cost': 1494.0}, {'tour': [1, 9, 3, 8, 0, 6, 5, 7, 10, 4, 2, 11], 'cur_cost': 1357.0}]
2025-08-05 10:28:36,598 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:36,599 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 71, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 71, 'cache_hits': 0, 'similarity_calculations': 537, 'cache_hit_rate': 0.0, 'cache_size': 537}}
2025-08-05 10:28:36,599 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([10,  6,  1,  9,  4, 11,  7,  8,  5,  2,  3,  0], dtype=int64), 'cur_cost': 1446.0, 'intermediate_solutions': [{'tour': array([ 2,  9,  4, 11, 10,  0,  7,  1,  5,  6,  8,  3]), 'cur_cost': 1332.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([11,  2,  9,  4, 10,  0,  7,  1,  5,  6,  8,  3]), 'cur_cost': 1250.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([10, 11,  2,  9,  4,  0,  7,  1,  5,  6,  8,  3]), 'cur_cost': 1366.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4, 11,  2,  9, 10,  0,  7,  1,  5,  6,  8,  3]), 'cur_cost': 1402.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4, 10, 11,  2,  9,  0,  7,  1,  5,  6,  8,  3]), 'cur_cost': 1370.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:36,599 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1446.00)
2025-08-05 10:28:36,599 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:36,599 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:36,600 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:36,600 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:36,600 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,600 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,600 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,601 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,601 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1168.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:36,601 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [7, 0, 10, 4, 5, 6, 1, 8, 9, 3, 11, 2], 'cur_cost': 1168.0, 'intermediate_solutions': [{'tour': [0, 9, 5, 3, 4, 10, 7, 1, 2, 6, 8, 11], 'cur_cost': 1338.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 2, 3, 4, 7, 10, 1, 5, 6, 8, 11], 'cur_cost': 1445.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 2, 3, 4, 10, 7, 8, 1, 5, 6, 11], 'cur_cost': 1430.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:36,601 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1168.00)
2025-08-05 10:28:36,601 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:36,601 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:36,601 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:36,602 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 1485.0
2025-08-05 10:28:36,607 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:36,607 - ExploitationExpert - INFO - res_population_costs: [845.0, 845.0, 845, 845]
2025-08-05 10:28:36,608 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  7,  3,  8,  9,  1,  5,  6,  2,  4, 10], dtype=int64), array([ 0, 10,  4,  2,  6,  9,  8,  5,  1,  7,  3, 11], dtype=int64), array([ 0, 11,  3,  8,  9,  6,  2,  4, 10,  5,  1,  7], dtype=int64), array([ 0, 11,  3,  8,  9,  6,  2,  4,  5,  1,  7, 10], dtype=int64)]
2025-08-05 10:28:36,608 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:36,609 - ExploitationExpert - INFO - populations: [{'tour': [2, 7, 9, 4, 11, 3, 1, 5, 8, 6, 10, 0], 'cur_cost': 1472.0}, {'tour': [4, 8, 3, 5, 1, 9, 6, 2, 10, 0, 11, 7], 'cur_cost': 1184.0}, {'tour': [1, 8, 10, 0, 11, 3, 7, 5, 6, 2, 4, 9], 'cur_cost': 1125.0}, {'tour': [0, 1, 4, 3, 7, 10, 5, 6, 2, 8, 9, 11], 'cur_cost': 1234.0}, {'tour': [8, 7, 3, 11, 1, 6, 5, 10, 0, 4, 2, 9], 'cur_cost': 1088.0}, {'tour': array([ 4,  3, 11,  2, 10,  5,  6,  7,  9,  0,  8,  1], dtype=int64), 'cur_cost': 1575.0}, {'tour': array([10,  6,  1,  9,  4, 11,  7,  8,  5,  2,  3,  0], dtype=int64), 'cur_cost': 1446.0}, {'tour': [7, 0, 10, 4, 5, 6, 1, 8, 9, 3, 11, 2], 'cur_cost': 1168.0}, {'tour': array([ 0,  1,  3,  9,  2, 10,  4,  6,  7,  5, 11,  8], dtype=int64), 'cur_cost': 1485.0}, {'tour': [1, 9, 3, 8, 0, 6, 5, 7, 10, 4, 2, 11], 'cur_cost': 1357.0}]
2025-08-05 10:28:36,609 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:36,609 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 72, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 72, 'cache_hits': 0, 'similarity_calculations': 556, 'cache_hit_rate': 0.0, 'cache_size': 556}}
2025-08-05 10:28:36,610 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 0,  1,  3,  9,  2, 10,  4,  6,  7,  5, 11,  8], dtype=int64), 'cur_cost': 1485.0, 'intermediate_solutions': [{'tour': array([ 3,  6,  5, 10,  7,  9,  2,  1,  0,  4, 11,  8]), 'cur_cost': 1427.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([10,  3,  6,  5,  7,  9,  2,  1,  0,  4, 11,  8]), 'cur_cost': 1543.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 7, 10,  3,  6,  5,  9,  2,  1,  0,  4, 11,  8]), 'cur_cost': 1471.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 5, 10,  3,  6,  7,  9,  2,  1,  0,  4, 11,  8]), 'cur_cost': 1563.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 5,  7, 10,  3,  6,  9,  2,  1,  0,  4, 11,  8]), 'cur_cost': 1467.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:36,610 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1485.00)
2025-08-05 10:28:36,610 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:36,610 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:36,610 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:36,611 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:36,611 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,611 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,611 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,611 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,611 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1354.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:36,611 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [6, 1, 5, 7, 10, 0, 4, 3, 8, 9, 2, 11], 'cur_cost': 1354.0, 'intermediate_solutions': [{'tour': [1, 10, 3, 8, 0, 6, 5, 7, 9, 4, 2, 11], 'cur_cost': 1516.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 9, 3, 8, 0, 6, 4, 10, 7, 5, 2, 11], 'cur_cost': 1433.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 9, 4, 3, 8, 0, 6, 5, 7, 10, 2, 11], 'cur_cost': 1594.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:36,612 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1354.00)
2025-08-05 10:28:36,612 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:36,612 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:36,613 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [2, 7, 9, 4, 11, 3, 1, 5, 8, 6, 10, 0], 'cur_cost': 1472.0, 'intermediate_solutions': [{'tour': [8, 7, 3, 1, 10, 0, 4, 2, 6, 9, 5, 11], 'cur_cost': 1202.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 7, 3, 1, 10, 0, 6, 2, 4, 5, 9, 11], 'cur_cost': 1300.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 9, 7, 3, 1, 10, 0, 4, 2, 6, 5, 11], 'cur_cost': 1129.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [4, 8, 3, 5, 1, 9, 6, 2, 10, 0, 11, 7], 'cur_cost': 1184.0, 'intermediate_solutions': [{'tour': [0, 10, 9, 3, 8, 7, 1, 5, 6, 2, 4, 11], 'cur_cost': 1080.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 9, 10, 3, 7, 1, 5, 6, 2, 4, 11], 'cur_cost': 1097.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 9, 3, 10, 1, 7, 5, 6, 2, 4, 11], 'cur_cost': 1091.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [1, 8, 10, 0, 11, 3, 7, 5, 6, 2, 4, 9], 'cur_cost': 1125.0, 'intermediate_solutions': [{'tour': [0, 6, 5, 3, 11, 1, 7, 10, 4, 2, 8, 9], 'cur_cost': 1271.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 5, 1, 11, 3, 7, 10, 4, 8, 2, 9], 'cur_cost': 1450.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 6, 5, 11, 3, 7, 10, 4, 2, 8, 9], 'cur_cost': 1206.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 4, 3, 7, 10, 5, 6, 2, 8, 9, 11], 'cur_cost': 1234.0, 'intermediate_solutions': [{'tour': [1, 4, 5, 6, 11, 0, 7, 2, 8, 9, 10, 3], 'cur_cost': 1358.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 5, 4, 11, 3, 10, 9, 8, 2, 7, 0], 'cur_cost': 1353.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 5, 4, 0, 7, 2, 8, 9, 11, 10, 3], 'cur_cost': 1322.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [8, 7, 3, 11, 1, 6, 5, 10, 0, 4, 2, 9], 'cur_cost': 1088.0, 'intermediate_solutions': [{'tour': [2, 1, 3, 7, 0, 6, 5, 4, 10, 8, 9, 11], 'cur_cost': 1360.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 1, 3, 7, 0, 10, 5, 8, 6, 4, 9, 11], 'cur_cost': 1454.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 1, 6, 3, 7, 0, 10, 5, 4, 8, 9, 11], 'cur_cost': 1379.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 4,  3, 11,  2, 10,  5,  6,  7,  9,  0,  8,  1], dtype=int64), 'cur_cost': 1575.0, 'intermediate_solutions': [{'tour': array([ 3, 11,  1,  0,  2,  9,  5,  6,  7,  4,  8, 10]), 'cur_cost': 1580.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 0,  3, 11,  1,  2,  9,  5,  6,  7,  4,  8, 10]), 'cur_cost': 1464.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 2,  0,  3, 11,  1,  9,  5,  6,  7,  4,  8, 10]), 'cur_cost': 1523.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 1,  0,  3, 11,  2,  9,  5,  6,  7,  4,  8, 10]), 'cur_cost': 1552.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 1,  2,  0,  3, 11,  9,  5,  6,  7,  4,  8, 10]), 'cur_cost': 1553.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([10,  6,  1,  9,  4, 11,  7,  8,  5,  2,  3,  0], dtype=int64), 'cur_cost': 1446.0, 'intermediate_solutions': [{'tour': array([ 2,  9,  4, 11, 10,  0,  7,  1,  5,  6,  8,  3]), 'cur_cost': 1332.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([11,  2,  9,  4, 10,  0,  7,  1,  5,  6,  8,  3]), 'cur_cost': 1250.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([10, 11,  2,  9,  4,  0,  7,  1,  5,  6,  8,  3]), 'cur_cost': 1366.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4, 11,  2,  9, 10,  0,  7,  1,  5,  6,  8,  3]), 'cur_cost': 1402.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4, 10, 11,  2,  9,  0,  7,  1,  5,  6,  8,  3]), 'cur_cost': 1370.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [7, 0, 10, 4, 5, 6, 1, 8, 9, 3, 11, 2], 'cur_cost': 1168.0, 'intermediate_solutions': [{'tour': [0, 9, 5, 3, 4, 10, 7, 1, 2, 6, 8, 11], 'cur_cost': 1338.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 2, 3, 4, 7, 10, 1, 5, 6, 8, 11], 'cur_cost': 1445.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 2, 3, 4, 10, 7, 8, 1, 5, 6, 11], 'cur_cost': 1430.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 0,  1,  3,  9,  2, 10,  4,  6,  7,  5, 11,  8], dtype=int64), 'cur_cost': 1485.0, 'intermediate_solutions': [{'tour': array([ 3,  6,  5, 10,  7,  9,  2,  1,  0,  4, 11,  8]), 'cur_cost': 1427.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([10,  3,  6,  5,  7,  9,  2,  1,  0,  4, 11,  8]), 'cur_cost': 1543.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 7, 10,  3,  6,  5,  9,  2,  1,  0,  4, 11,  8]), 'cur_cost': 1471.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 5, 10,  3,  6,  7,  9,  2,  1,  0,  4, 11,  8]), 'cur_cost': 1563.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 5,  7, 10,  3,  6,  9,  2,  1,  0,  4, 11,  8]), 'cur_cost': 1467.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [6, 1, 5, 7, 10, 0, 4, 3, 8, 9, 2, 11], 'cur_cost': 1354.0, 'intermediate_solutions': [{'tour': [1, 10, 3, 8, 0, 6, 5, 7, 9, 4, 2, 11], 'cur_cost': 1516.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 9, 3, 8, 0, 6, 4, 10, 7, 5, 2, 11], 'cur_cost': 1433.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 9, 4, 3, 8, 0, 6, 5, 7, 10, 2, 11], 'cur_cost': 1594.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:36,613 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:36,613 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:36,614 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1088.000, 多样性=0.930
2025-08-05 10:28:36,614 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 10:28:36,614 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 10:28:36,615 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:36,615 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.020660905672142554, 'best_improvement': -0.039159503342884434}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.08189655172413812}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.0329659410522452, 'recent_improvements': [0.06265339774476171, -0.004881784940588478, -0.0032784843597286934], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 845.0, 'new_best_cost': 845.0, 'quality_improvement': 0.0, 'old_diversity': 0.75, 'new_diversity': 0.75, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:36,615 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 10:28:36,615 - __main__ - INFO - simple6_12 开始进化第 4 代
2025-08-05 10:28:36,615 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 10:28:36,615 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:36,616 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1088.000, 多样性=0.930
2025-08-05 10:28:36,616 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:36,616 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.930
2025-08-05 10:28:36,617 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:36,617 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.750
2025-08-05 10:28:36,619 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 10:28:36,619 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:36,619 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 10:28:36,619 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 10:28:36,630 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.286, 适应度梯度: -6.986, 聚类评分: 0.000, 覆盖率: 0.032, 收敛趋势: 0.000, 多样性: 0.782
2025-08-05 10:28:36,631 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 10:28:36,631 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:36,631 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple6_12
2025-08-05 10:28:36,635 - visualization.landscape_visualizer - INFO - 插值约束: 55 个点被约束到最小值 845.00
2025-08-05 10:28:36,636 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.3%, 梯度: 38.30 → 35.51
2025-08-05 10:28:36,739 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple6_12\landscape_simple6_12_iter_29_20250805_102836.html
2025-08-05 10:28:36,786 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple6_12\dashboard_simple6_12_iter_29_20250805_102836.html
2025-08-05 10:28:36,786 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 29
2025-08-05 10:28:36,786 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 10:28:36,787 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1676秒
2025-08-05 10:28:36,787 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2857142857142857, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -6.98571428571428, 'local_optima_density': 0.2857142857142857, 'gradient_variance': 56083.19122448979, 'cluster_count': 0}, 'population_state': {'diversity': 0.7817896389324961, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0317, 'fitness_entropy': 0.929906376575379, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -6.986)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.032)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.782)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360916.6314106, 'performance_metrics': {}}}
2025-08-05 10:28:36,787 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:36,787 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:36,788 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:36,788 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:36,788 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 10:28:36,788 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 10:28:36,788 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 10:28:36,788 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:36,789 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:36,789 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 10:28:36,789 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:36,789 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:36,789 - experts.management.collaboration_manager - INFO - 识别精英个体: {2, 4} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:36,789 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-05 10:28:36,789 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:36,789 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:36,789 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 1510.0
2025-08-05 10:28:36,797 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:36,797 - ExploitationExpert - INFO - res_population_costs: [845.0, 845.0, 845, 845]
2025-08-05 10:28:36,797 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  7,  3,  8,  9,  1,  5,  6,  2,  4, 10], dtype=int64), array([ 0, 10,  4,  2,  6,  9,  8,  5,  1,  7,  3, 11], dtype=int64), array([ 0, 11,  3,  8,  9,  6,  2,  4, 10,  5,  1,  7], dtype=int64), array([ 0, 11,  3,  8,  9,  6,  2,  4,  5,  1,  7, 10], dtype=int64)]
2025-08-05 10:28:36,798 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:36,798 - ExploitationExpert - INFO - populations: [{'tour': array([ 6,  1,  5,  7,  3,  0, 10,  8,  2, 11,  9,  4], dtype=int64), 'cur_cost': 1510.0}, {'tour': [4, 8, 3, 5, 1, 9, 6, 2, 10, 0, 11, 7], 'cur_cost': 1184.0}, {'tour': [1, 8, 10, 0, 11, 3, 7, 5, 6, 2, 4, 9], 'cur_cost': 1125.0}, {'tour': [0, 1, 4, 3, 7, 10, 5, 6, 2, 8, 9, 11], 'cur_cost': 1234.0}, {'tour': [8, 7, 3, 11, 1, 6, 5, 10, 0, 4, 2, 9], 'cur_cost': 1088.0}, {'tour': [4, 3, 11, 2, 10, 5, 6, 7, 9, 0, 8, 1], 'cur_cost': 1575.0}, {'tour': [10, 6, 1, 9, 4, 11, 7, 8, 5, 2, 3, 0], 'cur_cost': 1446.0}, {'tour': [7, 0, 10, 4, 5, 6, 1, 8, 9, 3, 11, 2], 'cur_cost': 1168.0}, {'tour': [0, 1, 3, 9, 2, 10, 4, 6, 7, 5, 11, 8], 'cur_cost': 1485.0}, {'tour': [6, 1, 5, 7, 10, 0, 4, 3, 8, 9, 2, 11], 'cur_cost': 1354.0}]
2025-08-05 10:28:36,799 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:36,799 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 73, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 73, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:36,800 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([ 6,  1,  5,  7,  3,  0, 10,  8,  2, 11,  9,  4], dtype=int64), 'cur_cost': 1510.0, 'intermediate_solutions': [{'tour': array([ 9,  7,  2,  4, 11,  3,  1,  5,  8,  6, 10,  0]), 'cur_cost': 1361.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 4,  9,  7,  2, 11,  3,  1,  5,  8,  6, 10,  0]), 'cur_cost': 1468.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([11,  4,  9,  7,  2,  3,  1,  5,  8,  6, 10,  0]), 'cur_cost': 1445.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2,  4,  9,  7, 11,  3,  1,  5,  8,  6, 10,  0]), 'cur_cost': 1321.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2, 11,  4,  9,  7,  3,  1,  5,  8,  6, 10,  0]), 'cur_cost': 1507.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:36,800 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1510.00)
2025-08-05 10:28:36,800 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:36,800 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:36,800 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:36,800 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:36,801 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,801 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,801 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,801 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,801 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1183.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:36,801 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [4, 1, 10, 5, 2, 6, 7, 3, 11, 0, 8, 9], 'cur_cost': 1183.0, 'intermediate_solutions': [{'tour': [4, 8, 3, 5, 1, 9, 10, 2, 6, 0, 11, 7], 'cur_cost': 1380.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 3, 5, 1, 9, 6, 0, 10, 2, 11, 7], 'cur_cost': 1440.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 8, 3, 5, 1, 9, 6, 2, 10, 0, 11, 7], 'cur_cost': 1184.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:36,802 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1183.00)
2025-08-05 10:28:36,802 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:36,802 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:36,802 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:36,802 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:36,802 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,803 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,803 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,803 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,803 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1308.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:36,803 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [3, 6, 4, 7, 8, 9, 1, 5, 10, 0, 11, 2], 'cur_cost': 1308.0, 'intermediate_solutions': [{'tour': [1, 8, 11, 0, 10, 3, 7, 5, 6, 2, 4, 9], 'cur_cost': 1159.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 10, 0, 11, 3, 6, 5, 7, 2, 4, 9], 'cur_cost': 1279.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 10, 8, 0, 11, 3, 7, 5, 6, 2, 4, 9], 'cur_cost': 1211.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:36,803 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 1308.00)
2025-08-05 10:28:36,805 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:36,805 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:36,805 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:36,805 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:36,805 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,805 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,805 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,805 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,805 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1224.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:36,805 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [1, 3, 4, 7, 8, 9, 6, 5, 10, 0, 11, 2], 'cur_cost': 1224.0, 'intermediate_solutions': [{'tour': [0, 1, 4, 3, 7, 10, 5, 11, 2, 8, 9, 6], 'cur_cost': 1474.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 7, 3, 4, 1, 5, 6, 2, 8, 9, 11], 'cur_cost': 1154.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 4, 3, 7, 6, 10, 5, 2, 8, 9, 11], 'cur_cost': 1356.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:36,807 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1224.00)
2025-08-05 10:28:36,807 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:36,807 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:36,807 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:36,807 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 12
2025-08-05 10:28:36,807 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,807 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,807 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,807 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,807 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1366.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:36,807 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [1, 7, 11, 5, 10, 6, 4, 0, 2, 3, 8, 9], 'cur_cost': 1366.0, 'intermediate_solutions': [{'tour': [8, 7, 3, 4, 1, 6, 5, 10, 0, 11, 2, 9], 'cur_cost': 1228.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 7, 3, 11, 1, 6, 5, 10, 4, 0, 2, 9], 'cur_cost': 1202.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 7, 3, 11, 0, 1, 6, 5, 10, 4, 2, 9], 'cur_cost': 1001.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:36,808 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 1366.00)
2025-08-05 10:28:36,808 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:28:36,808 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:36,808 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:36,808 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 1538.0
2025-08-05 10:28:36,814 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:36,814 - ExploitationExpert - INFO - res_population_costs: [845.0, 845.0, 845, 845]
2025-08-05 10:28:36,814 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  7,  3,  8,  9,  1,  5,  6,  2,  4, 10], dtype=int64), array([ 0, 10,  4,  2,  6,  9,  8,  5,  1,  7,  3, 11], dtype=int64), array([ 0, 11,  3,  8,  9,  6,  2,  4, 10,  5,  1,  7], dtype=int64), array([ 0, 11,  3,  8,  9,  6,  2,  4,  5,  1,  7, 10], dtype=int64)]
2025-08-05 10:28:36,815 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:36,815 - ExploitationExpert - INFO - populations: [{'tour': array([ 6,  1,  5,  7,  3,  0, 10,  8,  2, 11,  9,  4], dtype=int64), 'cur_cost': 1510.0}, {'tour': [4, 1, 10, 5, 2, 6, 7, 3, 11, 0, 8, 9], 'cur_cost': 1183.0}, {'tour': [3, 6, 4, 7, 8, 9, 1, 5, 10, 0, 11, 2], 'cur_cost': 1308.0}, {'tour': [1, 3, 4, 7, 8, 9, 6, 5, 10, 0, 11, 2], 'cur_cost': 1224.0}, {'tour': [1, 7, 11, 5, 10, 6, 4, 0, 2, 3, 8, 9], 'cur_cost': 1366.0}, {'tour': array([ 6,  7,  4,  0, 11,  5,  8,  1,  3,  2,  9, 10], dtype=int64), 'cur_cost': 1538.0}, {'tour': [10, 6, 1, 9, 4, 11, 7, 8, 5, 2, 3, 0], 'cur_cost': 1446.0}, {'tour': [7, 0, 10, 4, 5, 6, 1, 8, 9, 3, 11, 2], 'cur_cost': 1168.0}, {'tour': [0, 1, 3, 9, 2, 10, 4, 6, 7, 5, 11, 8], 'cur_cost': 1485.0}, {'tour': [6, 1, 5, 7, 10, 0, 4, 3, 8, 9, 2, 11], 'cur_cost': 1354.0}]
2025-08-05 10:28:36,815 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:36,815 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 74, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 74, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:36,816 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([ 6,  7,  4,  0, 11,  5,  8,  1,  3,  2,  9, 10], dtype=int64), 'cur_cost': 1538.0, 'intermediate_solutions': [{'tour': array([11,  3,  4,  2, 10,  5,  6,  7,  9,  0,  8,  1]), 'cur_cost': 1447.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2, 11,  3,  4, 10,  5,  6,  7,  9,  0,  8,  1]), 'cur_cost': 1528.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([10,  2, 11,  3,  4,  5,  6,  7,  9,  0,  8,  1]), 'cur_cost': 1552.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4,  2, 11,  3, 10,  5,  6,  7,  9,  0,  8,  1]), 'cur_cost': 1465.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4, 10,  2, 11,  3,  5,  6,  7,  9,  0,  8,  1]), 'cur_cost': 1510.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:36,816 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1538.00)
2025-08-05 10:28:36,816 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:36,816 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:36,816 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:36,817 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:36,817 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,817 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,817 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,817 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,817 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1203.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:36,818 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [8, 5, 2, 6, 10, 7, 3, 1, 0, 11, 4, 9], 'cur_cost': 1203.0, 'intermediate_solutions': [{'tour': [0, 6, 1, 9, 4, 11, 7, 8, 5, 2, 3, 10], 'cur_cost': 1504.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 6, 1, 9, 4, 11, 2, 5, 8, 7, 3, 0], 'cur_cost': 1439.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 1, 10, 9, 4, 11, 7, 8, 5, 2, 3, 0], 'cur_cost': 1572.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:36,818 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1203.00)
2025-08-05 10:28:36,818 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:36,818 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:36,818 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:36,819 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:36,819 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,819 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,819 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,819 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,819 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1186.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:36,819 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [10, 1, 7, 3, 0, 5, 2, 6, 8, 9, 11, 4], 'cur_cost': 1186.0, 'intermediate_solutions': [{'tour': [7, 0, 10, 4, 5, 6, 1, 3, 9, 8, 11, 2], 'cur_cost': 1233.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 0, 1, 6, 5, 4, 10, 8, 9, 3, 11, 2], 'cur_cost': 1274.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 2, 0, 10, 4, 5, 6, 1, 8, 9, 3, 11], 'cur_cost': 1146.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:36,819 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1186.00)
2025-08-05 10:28:36,820 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:36,820 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:36,820 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:36,820 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 1418.0
2025-08-05 10:28:36,827 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:36,827 - ExploitationExpert - INFO - res_population_costs: [845.0, 845.0, 845, 845]
2025-08-05 10:28:36,827 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  7,  3,  8,  9,  1,  5,  6,  2,  4, 10], dtype=int64), array([ 0, 10,  4,  2,  6,  9,  8,  5,  1,  7,  3, 11], dtype=int64), array([ 0, 11,  3,  8,  9,  6,  2,  4, 10,  5,  1,  7], dtype=int64), array([ 0, 11,  3,  8,  9,  6,  2,  4,  5,  1,  7, 10], dtype=int64)]
2025-08-05 10:28:36,829 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:36,829 - ExploitationExpert - INFO - populations: [{'tour': array([ 6,  1,  5,  7,  3,  0, 10,  8,  2, 11,  9,  4], dtype=int64), 'cur_cost': 1510.0}, {'tour': [4, 1, 10, 5, 2, 6, 7, 3, 11, 0, 8, 9], 'cur_cost': 1183.0}, {'tour': [3, 6, 4, 7, 8, 9, 1, 5, 10, 0, 11, 2], 'cur_cost': 1308.0}, {'tour': [1, 3, 4, 7, 8, 9, 6, 5, 10, 0, 11, 2], 'cur_cost': 1224.0}, {'tour': [1, 7, 11, 5, 10, 6, 4, 0, 2, 3, 8, 9], 'cur_cost': 1366.0}, {'tour': array([ 6,  7,  4,  0, 11,  5,  8,  1,  3,  2,  9, 10], dtype=int64), 'cur_cost': 1538.0}, {'tour': [8, 5, 2, 6, 10, 7, 3, 1, 0, 11, 4, 9], 'cur_cost': 1203.0}, {'tour': [10, 1, 7, 3, 0, 5, 2, 6, 8, 9, 11, 4], 'cur_cost': 1186.0}, {'tour': array([ 0, 11,  1,  5,  8,  2, 10,  3,  7,  4,  9,  6], dtype=int64), 'cur_cost': 1418.0}, {'tour': [6, 1, 5, 7, 10, 0, 4, 3, 8, 9, 2, 11], 'cur_cost': 1354.0}]
2025-08-05 10:28:36,829 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:36,829 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 75, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 75, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:36,830 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 0, 11,  1,  5,  8,  2, 10,  3,  7,  4,  9,  6], dtype=int64), 'cur_cost': 1418.0, 'intermediate_solutions': [{'tour': array([ 3,  1,  0,  9,  2, 10,  4,  6,  7,  5, 11,  8]), 'cur_cost': 1483.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9,  3,  1,  0,  2, 10,  4,  6,  7,  5, 11,  8]), 'cur_cost': 1376.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 2,  9,  3,  1,  0, 10,  4,  6,  7,  5, 11,  8]), 'cur_cost': 1444.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 0,  9,  3,  1,  2, 10,  4,  6,  7,  5, 11,  8]), 'cur_cost': 1523.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 0,  2,  9,  3,  1, 10,  4,  6,  7,  5, 11,  8]), 'cur_cost': 1510.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:36,830 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1418.00)
2025-08-05 10:28:36,830 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:36,830 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:36,830 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:36,831 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:36,831 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,831 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,831 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,831 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:36,831 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1203.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:36,832 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [1, 2, 8, 3, 9, 6, 5, 7, 10, 0, 11, 4], 'cur_cost': 1203.0, 'intermediate_solutions': [{'tour': [6, 1, 7, 5, 10, 0, 4, 3, 8, 9, 2, 11], 'cur_cost': 1373.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 1, 5, 7, 10, 0, 4, 3, 8, 2, 9, 11], 'cur_cost': 1471.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 1, 5, 8, 7, 10, 0, 4, 3, 9, 2, 11], 'cur_cost': 1481.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:36,832 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1203.00)
2025-08-05 10:28:36,832 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:36,832 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:36,833 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 6,  1,  5,  7,  3,  0, 10,  8,  2, 11,  9,  4], dtype=int64), 'cur_cost': 1510.0, 'intermediate_solutions': [{'tour': array([ 9,  7,  2,  4, 11,  3,  1,  5,  8,  6, 10,  0]), 'cur_cost': 1361.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 4,  9,  7,  2, 11,  3,  1,  5,  8,  6, 10,  0]), 'cur_cost': 1468.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([11,  4,  9,  7,  2,  3,  1,  5,  8,  6, 10,  0]), 'cur_cost': 1445.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2,  4,  9,  7, 11,  3,  1,  5,  8,  6, 10,  0]), 'cur_cost': 1321.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2, 11,  4,  9,  7,  3,  1,  5,  8,  6, 10,  0]), 'cur_cost': 1507.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [4, 1, 10, 5, 2, 6, 7, 3, 11, 0, 8, 9], 'cur_cost': 1183.0, 'intermediate_solutions': [{'tour': [4, 8, 3, 5, 1, 9, 10, 2, 6, 0, 11, 7], 'cur_cost': 1380.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 3, 5, 1, 9, 6, 0, 10, 2, 11, 7], 'cur_cost': 1440.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 8, 3, 5, 1, 9, 6, 2, 10, 0, 11, 7], 'cur_cost': 1184.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [3, 6, 4, 7, 8, 9, 1, 5, 10, 0, 11, 2], 'cur_cost': 1308.0, 'intermediate_solutions': [{'tour': [1, 8, 11, 0, 10, 3, 7, 5, 6, 2, 4, 9], 'cur_cost': 1159.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 10, 0, 11, 3, 6, 5, 7, 2, 4, 9], 'cur_cost': 1279.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 10, 8, 0, 11, 3, 7, 5, 6, 2, 4, 9], 'cur_cost': 1211.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [1, 3, 4, 7, 8, 9, 6, 5, 10, 0, 11, 2], 'cur_cost': 1224.0, 'intermediate_solutions': [{'tour': [0, 1, 4, 3, 7, 10, 5, 11, 2, 8, 9, 6], 'cur_cost': 1474.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 7, 3, 4, 1, 5, 6, 2, 8, 9, 11], 'cur_cost': 1154.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 4, 3, 7, 6, 10, 5, 2, 8, 9, 11], 'cur_cost': 1356.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 11, 5, 10, 6, 4, 0, 2, 3, 8, 9], 'cur_cost': 1366.0, 'intermediate_solutions': [{'tour': [8, 7, 3, 4, 1, 6, 5, 10, 0, 11, 2, 9], 'cur_cost': 1228.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 7, 3, 11, 1, 6, 5, 10, 4, 0, 2, 9], 'cur_cost': 1202.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 7, 3, 11, 0, 1, 6, 5, 10, 4, 2, 9], 'cur_cost': 1001.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 6,  7,  4,  0, 11,  5,  8,  1,  3,  2,  9, 10], dtype=int64), 'cur_cost': 1538.0, 'intermediate_solutions': [{'tour': array([11,  3,  4,  2, 10,  5,  6,  7,  9,  0,  8,  1]), 'cur_cost': 1447.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2, 11,  3,  4, 10,  5,  6,  7,  9,  0,  8,  1]), 'cur_cost': 1528.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([10,  2, 11,  3,  4,  5,  6,  7,  9,  0,  8,  1]), 'cur_cost': 1552.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4,  2, 11,  3, 10,  5,  6,  7,  9,  0,  8,  1]), 'cur_cost': 1465.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4, 10,  2, 11,  3,  5,  6,  7,  9,  0,  8,  1]), 'cur_cost': 1510.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [8, 5, 2, 6, 10, 7, 3, 1, 0, 11, 4, 9], 'cur_cost': 1203.0, 'intermediate_solutions': [{'tour': [0, 6, 1, 9, 4, 11, 7, 8, 5, 2, 3, 10], 'cur_cost': 1504.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 6, 1, 9, 4, 11, 2, 5, 8, 7, 3, 0], 'cur_cost': 1439.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 1, 10, 9, 4, 11, 7, 8, 5, 2, 3, 0], 'cur_cost': 1572.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [10, 1, 7, 3, 0, 5, 2, 6, 8, 9, 11, 4], 'cur_cost': 1186.0, 'intermediate_solutions': [{'tour': [7, 0, 10, 4, 5, 6, 1, 3, 9, 8, 11, 2], 'cur_cost': 1233.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 0, 1, 6, 5, 4, 10, 8, 9, 3, 11, 2], 'cur_cost': 1274.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 2, 0, 10, 4, 5, 6, 1, 8, 9, 3, 11], 'cur_cost': 1146.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 0, 11,  1,  5,  8,  2, 10,  3,  7,  4,  9,  6], dtype=int64), 'cur_cost': 1418.0, 'intermediate_solutions': [{'tour': array([ 3,  1,  0,  9,  2, 10,  4,  6,  7,  5, 11,  8]), 'cur_cost': 1483.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9,  3,  1,  0,  2, 10,  4,  6,  7,  5, 11,  8]), 'cur_cost': 1376.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 2,  9,  3,  1,  0, 10,  4,  6,  7,  5, 11,  8]), 'cur_cost': 1444.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 0,  9,  3,  1,  2, 10,  4,  6,  7,  5, 11,  8]), 'cur_cost': 1523.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 0,  2,  9,  3,  1, 10,  4,  6,  7,  5, 11,  8]), 'cur_cost': 1510.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [1, 2, 8, 3, 9, 6, 5, 7, 10, 0, 11, 4], 'cur_cost': 1203.0, 'intermediate_solutions': [{'tour': [6, 1, 7, 5, 10, 0, 4, 3, 8, 9, 2, 11], 'cur_cost': 1373.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 1, 5, 7, 10, 0, 4, 3, 8, 2, 9, 11], 'cur_cost': 1471.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 1, 5, 8, 7, 10, 0, 4, 3, 9, 2, 11], 'cur_cost': 1481.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:36,833 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:36,834 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:36,835 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1183.000, 多样性=0.891
2025-08-05 10:28:36,836 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 10:28:36,836 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 10:28:36,836 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:36,836 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.021477786091970912, 'best_improvement': -0.08731617647058823}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.04183266932270937}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.00788956036577704, 'recent_improvements': [-0.004881784940588478, -0.0032784843597286934, -0.020660905672142554], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 845.0, 'new_best_cost': 845.0, 'quality_improvement': 0.0, 'old_diversity': 0.75, 'new_diversity': 0.75, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:36,836 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 10:28:36,837 - __main__ - INFO - simple6_12 开始进化第 5 代
2025-08-05 10:28:36,837 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 10:28:36,837 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:36,838 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1183.000, 多样性=0.891
2025-08-05 10:28:36,839 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:36,841 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.891
2025-08-05 10:28:36,841 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:36,842 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.750
2025-08-05 10:28:36,846 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 10:28:36,846 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:36,847 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 10:28:36,847 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 10:28:36,867 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.357, 适应度梯度: 9.457, 聚类评分: 0.000, 覆盖率: 0.033, 收敛趋势: 0.000, 多样性: 0.763
2025-08-05 10:28:36,867 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 10:28:36,867 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:36,867 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple6_12
2025-08-05 10:28:36,873 - visualization.landscape_visualizer - INFO - 插值约束: 83 个点被约束到最小值 845.00
2025-08-05 10:28:36,876 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=8.3%, 梯度: 23.88 → 21.90
2025-08-05 10:28:37,048 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple6_12\landscape_simple6_12_iter_30_20250805_102836.html
2025-08-05 10:28:37,119 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple6_12\dashboard_simple6_12_iter_30_20250805_102836.html
2025-08-05 10:28:37,119 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 30
2025-08-05 10:28:37,119 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 10:28:37,119 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2724秒
2025-08-05 10:28:37,120 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.35714285714285715, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 9.457142857142864, 'local_optima_density': 0.35714285714285715, 'gradient_variance': 57416.728163265325, 'cluster_count': 0}, 'population_state': {'diversity': 0.7629513343799058, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0327, 'fitness_entropy': 0.929906376575379, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.033)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.763)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 9.457)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360916.8679886, 'performance_metrics': {}}}
2025-08-05 10:28:37,120 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:37,120 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:37,120 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:37,120 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:37,120 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 10:28:37,120 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 10:28:37,120 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 10:28:37,120 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:37,121 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:37,121 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 10:28:37,121 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:37,121 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:37,121 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:37,121 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-05 10:28:37,121 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:37,121 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:37,122 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 1351.0
2025-08-05 10:28:37,128 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:37,128 - ExploitationExpert - INFO - res_population_costs: [845.0, 845.0, 845, 845]
2025-08-05 10:28:37,128 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  7,  3,  8,  9,  1,  5,  6,  2,  4, 10], dtype=int64), array([ 0, 10,  4,  2,  6,  9,  8,  5,  1,  7,  3, 11], dtype=int64), array([ 0, 11,  3,  8,  9,  6,  2,  4, 10,  5,  1,  7], dtype=int64), array([ 0, 11,  3,  8,  9,  6,  2,  4,  5,  1,  7, 10], dtype=int64)]
2025-08-05 10:28:37,129 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:37,129 - ExploitationExpert - INFO - populations: [{'tour': array([ 7,  0,  1,  3, 11,  8, 10,  5,  2,  6,  9,  4], dtype=int64), 'cur_cost': 1351.0}, {'tour': [4, 1, 10, 5, 2, 6, 7, 3, 11, 0, 8, 9], 'cur_cost': 1183.0}, {'tour': [3, 6, 4, 7, 8, 9, 1, 5, 10, 0, 11, 2], 'cur_cost': 1308.0}, {'tour': [1, 3, 4, 7, 8, 9, 6, 5, 10, 0, 11, 2], 'cur_cost': 1224.0}, {'tour': [1, 7, 11, 5, 10, 6, 4, 0, 2, 3, 8, 9], 'cur_cost': 1366.0}, {'tour': [6, 7, 4, 0, 11, 5, 8, 1, 3, 2, 9, 10], 'cur_cost': 1538.0}, {'tour': [8, 5, 2, 6, 10, 7, 3, 1, 0, 11, 4, 9], 'cur_cost': 1203.0}, {'tour': [10, 1, 7, 3, 0, 5, 2, 6, 8, 9, 11, 4], 'cur_cost': 1186.0}, {'tour': [0, 11, 1, 5, 8, 2, 10, 3, 7, 4, 9, 6], 'cur_cost': 1418.0}, {'tour': [1, 2, 8, 3, 9, 6, 5, 7, 10, 0, 11, 4], 'cur_cost': 1203.0}]
2025-08-05 10:28:37,130 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:37,130 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 76, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 76, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:37,130 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([ 7,  0,  1,  3, 11,  8, 10,  5,  2,  6,  9,  4], dtype=int64), 'cur_cost': 1351.0, 'intermediate_solutions': [{'tour': array([ 5,  1,  6,  7,  3,  0, 10,  8,  2, 11,  9,  4]), 'cur_cost': 1523.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 7,  5,  1,  6,  3,  0, 10,  8,  2, 11,  9,  4]), 'cur_cost': 1601.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3,  7,  5,  1,  6,  0, 10,  8,  2, 11,  9,  4]), 'cur_cost': 1632.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6,  7,  5,  1,  3,  0, 10,  8,  2, 11,  9,  4]), 'cur_cost': 1575.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6,  3,  7,  5,  1,  0, 10,  8,  2, 11,  9,  4]), 'cur_cost': 1581.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:37,131 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1351.00)
2025-08-05 10:28:37,131 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:37,131 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:37,131 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:37,131 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 12
2025-08-05 10:28:37,131 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,131 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,132 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,132 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,132 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1525.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:37,132 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [11, 6, 2, 1, 3, 10, 9, 4, 5, 8, 0, 7], 'cur_cost': 1525.0, 'intermediate_solutions': [{'tour': [4, 1, 10, 5, 6, 2, 7, 3, 11, 0, 8, 9], 'cur_cost': 1187.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 1, 10, 5, 2, 6, 7, 3, 9, 8, 0, 11], 'cur_cost': 1173.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 1, 10, 5, 2, 6, 0, 7, 3, 11, 8, 9], 'cur_cost': 1259.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:37,132 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 1525.00)
2025-08-05 10:28:37,132 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:37,132 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:37,132 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:37,133 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:37,133 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,133 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,133 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,133 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,133 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1352.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:37,133 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 9, 3, 1, 11, 7, 10, 5, 6, 2, 4, 8], 'cur_cost': 1352.0, 'intermediate_solutions': [{'tour': [3, 6, 1, 7, 8, 9, 4, 5, 10, 0, 11, 2], 'cur_cost': 1332.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 7, 4, 6, 3, 9, 1, 5, 10, 0, 11, 2], 'cur_cost': 1370.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 6, 4, 10, 7, 8, 9, 1, 5, 0, 11, 2], 'cur_cost': 1302.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:37,134 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1352.00)
2025-08-05 10:28:37,134 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:37,134 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:37,134 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:37,134 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 12
2025-08-05 10:28:37,134 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,134 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,134 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,135 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,135 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1522.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:37,135 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [11, 6, 3, 7, 0, 9, 4, 5, 2, 10, 1, 8], 'cur_cost': 1522.0, 'intermediate_solutions': [{'tour': [1, 3, 4, 7, 8, 5, 6, 9, 10, 0, 11, 2], 'cur_cost': 1390.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 3, 8, 7, 4, 9, 6, 5, 10, 0, 11, 2], 'cur_cost': 1288.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 3, 4, 7, 8, 9, 5, 10, 0, 6, 11, 2], 'cur_cost': 1515.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:37,135 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1522.00)
2025-08-05 10:28:37,135 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:37,135 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:37,135 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:37,136 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:37,136 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,136 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,136 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,136 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,136 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1269.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:37,136 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [1, 4, 0, 6, 2, 5, 7, 3, 8, 9, 10, 11], 'cur_cost': 1269.0, 'intermediate_solutions': [{'tour': [1, 7, 11, 5, 10, 6, 4, 8, 2, 3, 0, 9], 'cur_cost': 1611.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 7, 2, 0, 4, 6, 10, 5, 11, 3, 8, 9], 'cur_cost': 1324.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 11, 5, 10, 0, 6, 4, 2, 3, 8, 9], 'cur_cost': 1247.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:37,136 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1269.00)
2025-08-05 10:28:37,137 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:28:37,137 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:37,137 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:37,137 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 1429.0
2025-08-05 10:28:37,143 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:37,143 - ExploitationExpert - INFO - res_population_costs: [845.0, 845.0, 845, 845]
2025-08-05 10:28:37,143 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  7,  3,  8,  9,  1,  5,  6,  2,  4, 10], dtype=int64), array([ 0, 10,  4,  2,  6,  9,  8,  5,  1,  7,  3, 11], dtype=int64), array([ 0, 11,  3,  8,  9,  6,  2,  4, 10,  5,  1,  7], dtype=int64), array([ 0, 11,  3,  8,  9,  6,  2,  4,  5,  1,  7, 10], dtype=int64)]
2025-08-05 10:28:37,144 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:37,144 - ExploitationExpert - INFO - populations: [{'tour': array([ 7,  0,  1,  3, 11,  8, 10,  5,  2,  6,  9,  4], dtype=int64), 'cur_cost': 1351.0}, {'tour': [11, 6, 2, 1, 3, 10, 9, 4, 5, 8, 0, 7], 'cur_cost': 1525.0}, {'tour': [0, 9, 3, 1, 11, 7, 10, 5, 6, 2, 4, 8], 'cur_cost': 1352.0}, {'tour': [11, 6, 3, 7, 0, 9, 4, 5, 2, 10, 1, 8], 'cur_cost': 1522.0}, {'tour': [1, 4, 0, 6, 2, 5, 7, 3, 8, 9, 10, 11], 'cur_cost': 1269.0}, {'tour': array([ 1,  0,  6,  3,  8,  9,  7, 10,  5,  2, 11,  4], dtype=int64), 'cur_cost': 1429.0}, {'tour': [8, 5, 2, 6, 10, 7, 3, 1, 0, 11, 4, 9], 'cur_cost': 1203.0}, {'tour': [10, 1, 7, 3, 0, 5, 2, 6, 8, 9, 11, 4], 'cur_cost': 1186.0}, {'tour': [0, 11, 1, 5, 8, 2, 10, 3, 7, 4, 9, 6], 'cur_cost': 1418.0}, {'tour': [1, 2, 8, 3, 9, 6, 5, 7, 10, 0, 11, 4], 'cur_cost': 1203.0}]
2025-08-05 10:28:37,145 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:37,145 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 77, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 77, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:37,145 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([ 1,  0,  6,  3,  8,  9,  7, 10,  5,  2, 11,  4], dtype=int64), 'cur_cost': 1429.0, 'intermediate_solutions': [{'tour': array([ 4,  7,  6,  0, 11,  5,  8,  1,  3,  2,  9, 10]), 'cur_cost': 1533.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 0,  4,  7,  6, 11,  5,  8,  1,  3,  2,  9, 10]), 'cur_cost': 1623.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([11,  0,  4,  7,  6,  5,  8,  1,  3,  2,  9, 10]), 'cur_cost': 1424.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6,  0,  4,  7, 11,  5,  8,  1,  3,  2,  9, 10]), 'cur_cost': 1631.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6, 11,  0,  4,  7,  5,  8,  1,  3,  2,  9, 10]), 'cur_cost': 1541.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:37,145 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1429.00)
2025-08-05 10:28:37,145 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:37,146 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:37,146 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:37,146 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:37,146 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,146 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,146 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,147 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,147 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1243.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:37,147 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [7, 3, 0, 10, 1, 6, 8, 5, 4, 2, 9, 11], 'cur_cost': 1243.0, 'intermediate_solutions': [{'tour': [0, 5, 2, 6, 10, 7, 3, 1, 8, 11, 4, 9], 'cur_cost': 1459.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 5, 2, 6, 10, 7, 3, 4, 11, 0, 1, 9], 'cur_cost': 1202.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 6, 5, 2, 10, 7, 3, 1, 0, 11, 4, 9], 'cur_cost': 1176.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:37,147 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1243.00)
2025-08-05 10:28:37,147 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:37,147 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:37,147 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:37,148 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 12
2025-08-05 10:28:37,148 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,148 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,148 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,148 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,148 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1589.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:37,148 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [10, 4, 7, 2, 9, 3, 5, 0, 1, 11, 6, 8], 'cur_cost': 1589.0, 'intermediate_solutions': [{'tour': [10, 1, 7, 3, 11, 5, 2, 6, 8, 9, 0, 4], 'cur_cost': 1161.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 5, 0, 3, 7, 1, 10, 6, 8, 9, 11, 4], 'cur_cost': 1231.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 1, 7, 4, 3, 0, 5, 2, 6, 8, 9, 11], 'cur_cost': 1297.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:37,148 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 1589.00)
2025-08-05 10:28:37,149 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:37,149 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:37,149 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:37,149 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 1419.0
2025-08-05 10:28:37,154 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:37,155 - ExploitationExpert - INFO - res_population_costs: [845.0, 845.0, 845, 845]
2025-08-05 10:28:37,155 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  7,  3,  8,  9,  1,  5,  6,  2,  4, 10], dtype=int64), array([ 0, 10,  4,  2,  6,  9,  8,  5,  1,  7,  3, 11], dtype=int64), array([ 0, 11,  3,  8,  9,  6,  2,  4, 10,  5,  1,  7], dtype=int64), array([ 0, 11,  3,  8,  9,  6,  2,  4,  5,  1,  7, 10], dtype=int64)]
2025-08-05 10:28:37,156 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:37,156 - ExploitationExpert - INFO - populations: [{'tour': array([ 7,  0,  1,  3, 11,  8, 10,  5,  2,  6,  9,  4], dtype=int64), 'cur_cost': 1351.0}, {'tour': [11, 6, 2, 1, 3, 10, 9, 4, 5, 8, 0, 7], 'cur_cost': 1525.0}, {'tour': [0, 9, 3, 1, 11, 7, 10, 5, 6, 2, 4, 8], 'cur_cost': 1352.0}, {'tour': [11, 6, 3, 7, 0, 9, 4, 5, 2, 10, 1, 8], 'cur_cost': 1522.0}, {'tour': [1, 4, 0, 6, 2, 5, 7, 3, 8, 9, 10, 11], 'cur_cost': 1269.0}, {'tour': array([ 1,  0,  6,  3,  8,  9,  7, 10,  5,  2, 11,  4], dtype=int64), 'cur_cost': 1429.0}, {'tour': [7, 3, 0, 10, 1, 6, 8, 5, 4, 2, 9, 11], 'cur_cost': 1243.0}, {'tour': [10, 4, 7, 2, 9, 3, 5, 0, 1, 11, 6, 8], 'cur_cost': 1589.0}, {'tour': array([11,  1,  8,  0,  7,  3,  5,  2, 10,  4,  9,  6], dtype=int64), 'cur_cost': 1419.0}, {'tour': [1, 2, 8, 3, 9, 6, 5, 7, 10, 0, 11, 4], 'cur_cost': 1203.0}]
2025-08-05 10:28:37,156 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:37,157 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 78, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 78, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:37,157 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([11,  1,  8,  0,  7,  3,  5,  2, 10,  4,  9,  6], dtype=int64), 'cur_cost': 1419.0, 'intermediate_solutions': [{'tour': array([ 1, 11,  0,  5,  8,  2, 10,  3,  7,  4,  9,  6]), 'cur_cost': 1406.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 5,  1, 11,  0,  8,  2, 10,  3,  7,  4,  9,  6]), 'cur_cost': 1357.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 8,  5,  1, 11,  0,  2, 10,  3,  7,  4,  9,  6]), 'cur_cost': 1354.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 0,  5,  1, 11,  8,  2, 10,  3,  7,  4,  9,  6]), 'cur_cost': 1540.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 0,  8,  5,  1, 11,  2, 10,  3,  7,  4,  9,  6]), 'cur_cost': 1566.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:37,157 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1419.00)
2025-08-05 10:28:37,157 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:37,157 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:37,157 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:37,158 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:37,158 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,158 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,158 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,158 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:37,158 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1232.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:37,159 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [5, 9, 2, 8, 0, 11, 3, 7, 1, 10, 4, 6], 'cur_cost': 1232.0, 'intermediate_solutions': [{'tour': [1, 2, 8, 3, 9, 6, 10, 7, 5, 0, 11, 4], 'cur_cost': 1328.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 2, 8, 4, 11, 0, 10, 7, 5, 6, 9, 3], 'cur_cost': 1283.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 2, 8, 3, 9, 6, 4, 5, 7, 10, 0, 11], 'cur_cost': 1209.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:37,159 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1232.00)
2025-08-05 10:28:37,159 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:37,159 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:37,160 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 7,  0,  1,  3, 11,  8, 10,  5,  2,  6,  9,  4], dtype=int64), 'cur_cost': 1351.0, 'intermediate_solutions': [{'tour': array([ 5,  1,  6,  7,  3,  0, 10,  8,  2, 11,  9,  4]), 'cur_cost': 1523.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 7,  5,  1,  6,  3,  0, 10,  8,  2, 11,  9,  4]), 'cur_cost': 1601.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3,  7,  5,  1,  6,  0, 10,  8,  2, 11,  9,  4]), 'cur_cost': 1632.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6,  7,  5,  1,  3,  0, 10,  8,  2, 11,  9,  4]), 'cur_cost': 1575.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6,  3,  7,  5,  1,  0, 10,  8,  2, 11,  9,  4]), 'cur_cost': 1581.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [11, 6, 2, 1, 3, 10, 9, 4, 5, 8, 0, 7], 'cur_cost': 1525.0, 'intermediate_solutions': [{'tour': [4, 1, 10, 5, 6, 2, 7, 3, 11, 0, 8, 9], 'cur_cost': 1187.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 1, 10, 5, 2, 6, 7, 3, 9, 8, 0, 11], 'cur_cost': 1173.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 1, 10, 5, 2, 6, 0, 7, 3, 11, 8, 9], 'cur_cost': 1259.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 3, 1, 11, 7, 10, 5, 6, 2, 4, 8], 'cur_cost': 1352.0, 'intermediate_solutions': [{'tour': [3, 6, 1, 7, 8, 9, 4, 5, 10, 0, 11, 2], 'cur_cost': 1332.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 7, 4, 6, 3, 9, 1, 5, 10, 0, 11, 2], 'cur_cost': 1370.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 6, 4, 10, 7, 8, 9, 1, 5, 0, 11, 2], 'cur_cost': 1302.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [11, 6, 3, 7, 0, 9, 4, 5, 2, 10, 1, 8], 'cur_cost': 1522.0, 'intermediate_solutions': [{'tour': [1, 3, 4, 7, 8, 5, 6, 9, 10, 0, 11, 2], 'cur_cost': 1390.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 3, 8, 7, 4, 9, 6, 5, 10, 0, 11, 2], 'cur_cost': 1288.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 3, 4, 7, 8, 9, 5, 10, 0, 6, 11, 2], 'cur_cost': 1515.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 0, 6, 2, 5, 7, 3, 8, 9, 10, 11], 'cur_cost': 1269.0, 'intermediate_solutions': [{'tour': [1, 7, 11, 5, 10, 6, 4, 8, 2, 3, 0, 9], 'cur_cost': 1611.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 7, 2, 0, 4, 6, 10, 5, 11, 3, 8, 9], 'cur_cost': 1324.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 11, 5, 10, 0, 6, 4, 2, 3, 8, 9], 'cur_cost': 1247.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 1,  0,  6,  3,  8,  9,  7, 10,  5,  2, 11,  4], dtype=int64), 'cur_cost': 1429.0, 'intermediate_solutions': [{'tour': array([ 4,  7,  6,  0, 11,  5,  8,  1,  3,  2,  9, 10]), 'cur_cost': 1533.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 0,  4,  7,  6, 11,  5,  8,  1,  3,  2,  9, 10]), 'cur_cost': 1623.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([11,  0,  4,  7,  6,  5,  8,  1,  3,  2,  9, 10]), 'cur_cost': 1424.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6,  0,  4,  7, 11,  5,  8,  1,  3,  2,  9, 10]), 'cur_cost': 1631.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6, 11,  0,  4,  7,  5,  8,  1,  3,  2,  9, 10]), 'cur_cost': 1541.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [7, 3, 0, 10, 1, 6, 8, 5, 4, 2, 9, 11], 'cur_cost': 1243.0, 'intermediate_solutions': [{'tour': [0, 5, 2, 6, 10, 7, 3, 1, 8, 11, 4, 9], 'cur_cost': 1459.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 5, 2, 6, 10, 7, 3, 4, 11, 0, 1, 9], 'cur_cost': 1202.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 6, 5, 2, 10, 7, 3, 1, 0, 11, 4, 9], 'cur_cost': 1176.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [10, 4, 7, 2, 9, 3, 5, 0, 1, 11, 6, 8], 'cur_cost': 1589.0, 'intermediate_solutions': [{'tour': [10, 1, 7, 3, 11, 5, 2, 6, 8, 9, 0, 4], 'cur_cost': 1161.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 5, 0, 3, 7, 1, 10, 6, 8, 9, 11, 4], 'cur_cost': 1231.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 1, 7, 4, 3, 0, 5, 2, 6, 8, 9, 11], 'cur_cost': 1297.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([11,  1,  8,  0,  7,  3,  5,  2, 10,  4,  9,  6], dtype=int64), 'cur_cost': 1419.0, 'intermediate_solutions': [{'tour': array([ 1, 11,  0,  5,  8,  2, 10,  3,  7,  4,  9,  6]), 'cur_cost': 1406.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 5,  1, 11,  0,  8,  2, 10,  3,  7,  4,  9,  6]), 'cur_cost': 1357.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 8,  5,  1, 11,  0,  2, 10,  3,  7,  4,  9,  6]), 'cur_cost': 1354.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 0,  5,  1, 11,  8,  2, 10,  3,  7,  4,  9,  6]), 'cur_cost': 1540.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 0,  8,  5,  1, 11,  2, 10,  3,  7,  4,  9,  6]), 'cur_cost': 1566.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [5, 9, 2, 8, 0, 11, 3, 7, 1, 10, 4, 6], 'cur_cost': 1232.0, 'intermediate_solutions': [{'tour': [1, 2, 8, 3, 9, 6, 10, 7, 5, 0, 11, 4], 'cur_cost': 1328.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 2, 8, 4, 11, 0, 10, 7, 5, 6, 9, 3], 'cur_cost': 1283.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 2, 8, 3, 9, 6, 4, 5, 7, 10, 0, 11], 'cur_cost': 1209.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:37,160 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:37,160 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:37,161 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1232.000, 多样性=0.919
2025-08-05 10:28:37,161 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 10:28:37,162 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 10:28:37,162 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:37,162 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.044952875446589304, 'best_improvement': -0.04142011834319527}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.031185031185031454}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.009099650866121106, 'recent_improvements': [-0.0032784843597286934, -0.020660905672142554, -0.021477786091970912], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 845.0, 'new_best_cost': 845.0, 'quality_improvement': 0.0, 'old_diversity': 0.75, 'new_diversity': 0.75, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:37,162 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 10:28:37,164 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple6_12_solution.json
2025-08-05 10:28:37,164 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple6_12_20250805_102837.solution
2025-08-05 10:28:37,164 - __main__ - INFO - 实例执行完成 - 运行时间: 1.28s, 最佳成本: 845.0
2025-08-05 10:28:37,164 - __main__ - INFO - 实例 simple6_12 处理完成
