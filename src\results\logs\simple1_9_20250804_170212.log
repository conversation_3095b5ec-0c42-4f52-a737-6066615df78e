2025-08-04 17:02:12,144 - __main__ - INFO - simple1_9 开始进化第 1 代
2025-08-04 17:02:12,144 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-04 17:02:12,145 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:02:12,147 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=681.000, 多样性=0.901
2025-08-04 17:02:12,148 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:02:12,149 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.901
2025-08-04 17:02:12,150 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:02:12,182 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-04 17:02:12,182 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:02:12,182 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-04 17:02:12,182 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-04 17:02:12,486 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -53.100, 聚类评分: 0.000, 覆盖率: 0.001, 收敛趋势: 0.000, 多样性: 0.811
2025-08-04 17:02:12,486 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-04 17:02:12,487 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-04 17:02:12,487 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:02:12,523 - visualization.landscape_visualizer - INFO - 插值约束: 2 个点被约束到最小值 681.00
2025-08-04 17:02:12,935 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记，坐标系统已统一
2025-08-04 17:02:16,828 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_1_20250804_170216.html
2025-08-04 17:02:16,866 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_1_20250804_170216.html
2025-08-04 17:02:16,866 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-04 17:02:16,866 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-04 17:02:16,866 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 4.6839秒
2025-08-04 17:02:16,866 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-04 17:02:16,866 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -53.1, 'local_optima_density': 0.1, 'gradient_variance': 25746.314000000002, 'cluster_count': 0}, 'population_state': {'diversity': 0.8111111111111111, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.001, 'fitness_entropy': 0.9426807148172599, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -53.100)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.001)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.811)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754298132.4867027, 'performance_metrics': {}}}
2025-08-04 17:02:16,867 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:02:16,867 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:02:16,867 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:02:16,867 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:02:16,868 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-04 17:02:16,868 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-04 17:02:16,868 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-04 17:02:16,868 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:02:16,868 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:02:16,869 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-04 17:02:16,869 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:02:16,869 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:02:16,869 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2} (总数: 2, 保护比例: 0.20)
2025-08-04 17:02:16,869 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:02:16,869 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:02:16,869 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:16,881 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:02:16,882 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:17,074 - ExplorationExpert - INFO - 探索路径生成完成，成本: 966.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:02:17,075 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [2, 3, 4, 5, 6, 7, 0, 1, 8], 'cur_cost': 966.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:02:17,075 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 966.00)
2025-08-04 17:02:17,075 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:02:17,075 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:02:17,075 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:17,076 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:02:17,076 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:17,076 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1074.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:02:17,076 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [2, 8, 4, 3, 0, 7, 1, 5, 6], 'cur_cost': 1074.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:02:17,077 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 1074.00)
2025-08-04 17:02:17,077 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:02:17,077 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:02:17,077 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:17,077 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:02:17,078 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:17,078 - ExplorationExpert - INFO - 探索路径生成完成，成本: 861.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:02:17,078 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 4, 2, 7, 1, 6, 5, 3, 8], 'cur_cost': 861.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:02:17,078 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 861.00)
2025-08-04 17:02:17,078 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:02:17,078 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:02:17,078 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:17,079 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:02:17,079 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:17,079 - ExplorationExpert - INFO - 探索路径生成完成，成本: 846.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:02:17,079 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [7, 5, 6, 0, 4, 2, 8, 3, 1], 'cur_cost': 846.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:02:17,080 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 846.00)
2025-08-04 17:02:17,080 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:02:17,080 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:02:17,080 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:17,080 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:02:17,080 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:17,080 - ExplorationExpert - INFO - 探索路径生成完成，成本: 908.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:02:17,080 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [7, 5, 3, 4, 8, 6, 1, 0, 2], 'cur_cost': 908.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:02:17,080 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 908.00)
2025-08-04 17:02:17,080 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:02:17,080 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:02:17,080 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:17,081 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:02:17,082 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:17,082 - ExplorationExpert - INFO - 探索路径生成完成，成本: 819.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:02:17,082 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [1, 4, 0, 6, 7, 5, 3, 8, 2], 'cur_cost': 819.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:02:17,082 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 819.00)
2025-08-04 17:02:17,082 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-04 17:02:17,082 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:02:17,109 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:02:17,110 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1110.0
2025-08-04 17:02:18,016 - ExploitationExpert - INFO - res_population_num: 1
2025-08-04 17:02:18,016 - ExploitationExpert - INFO - res_population_costs: [680.0]
2025-08-04 17:02:18,016 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64)]
2025-08-04 17:02:18,016 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:02:18,016 - ExploitationExpert - INFO - populations: [{'tour': [2, 3, 4, 5, 6, 7, 0, 1, 8], 'cur_cost': 966.0}, {'tour': [2, 8, 4, 3, 0, 7, 1, 5, 6], 'cur_cost': 1074.0}, {'tour': [0, 4, 2, 7, 1, 6, 5, 3, 8], 'cur_cost': 861.0}, {'tour': [7, 5, 6, 0, 4, 2, 8, 3, 1], 'cur_cost': 846.0}, {'tour': [7, 5, 3, 4, 8, 6, 1, 0, 2], 'cur_cost': 908.0}, {'tour': [1, 4, 0, 6, 7, 5, 3, 8, 2], 'cur_cost': 819.0}, {'tour': array([3, 1, 8, 4, 0, 7, 6, 2, 5], dtype=int64), 'cur_cost': 1110.0}, {'tour': array([5, 7, 3, 8, 1, 2, 4, 6, 0], dtype=int64), 'cur_cost': 952.0}, {'tour': array([1, 2, 7, 4, 6, 8, 3, 5, 0], dtype=int64), 'cur_cost': 962.0}, {'tour': array([5, 8, 6, 2, 0, 3, 1, 4, 7], dtype=int64), 'cur_cost': 1116.0}]
2025-08-04 17:02:18,018 - ExploitationExpert - INFO - 局部搜索耗时: 0.91秒，最大迭代次数: 10
2025-08-04 17:02:18,018 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-04 17:02:18,019 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([3, 1, 8, 4, 0, 7, 6, 2, 5], dtype=int64), 'cur_cost': 1110.0, 'intermediate_solutions': [{'tour': array([7, 1, 3, 4, 5, 8, 2, 0, 6], dtype=int64), 'cur_cost': 1057.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 7, 1, 3, 5, 8, 2, 0, 6], dtype=int64), 'cur_cost': 1018.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 4, 7, 1, 3, 8, 2, 0, 6], dtype=int64), 'cur_cost': 998.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 4, 7, 1, 5, 8, 2, 0, 6], dtype=int64), 'cur_cost': 1029.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 5, 4, 7, 1, 8, 2, 0, 6], dtype=int64), 'cur_cost': 1023.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:02:18,019 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1110.00)
2025-08-04 17:02:18,019 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:02:18,019 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:02:18,019 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:18,019 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:02:18,020 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:18,020 - ExplorationExpert - INFO - 探索路径生成完成，成本: 732.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:02:18,020 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [3, 7, 5, 8, 2, 4, 0, 1, 6], 'cur_cost': 732.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:02:18,020 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 732.00)
2025-08-04 17:02:18,020 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:02:18,020 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:02:18,020 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:18,021 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:02:18,021 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:18,021 - ExplorationExpert - INFO - 探索路径生成完成，成本: 999.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:02:18,021 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 8, 4, 5, 2, 3, 7, 6, 1], 'cur_cost': 999.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:02:18,021 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 999.00)
2025-08-04 17:02:18,021 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-04 17:02:18,021 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:02:18,022 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:02:18,022 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 1041.0
2025-08-04 17:02:19,433 - ExploitationExpert - INFO - res_population_num: 2
2025-08-04 17:02:19,433 - ExploitationExpert - INFO - res_population_costs: [680.0, 680]
2025-08-04 17:02:19,433 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64)]
2025-08-04 17:02:19,434 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:02:19,434 - ExploitationExpert - INFO - populations: [{'tour': [2, 3, 4, 5, 6, 7, 0, 1, 8], 'cur_cost': 966.0}, {'tour': [2, 8, 4, 3, 0, 7, 1, 5, 6], 'cur_cost': 1074.0}, {'tour': [0, 4, 2, 7, 1, 6, 5, 3, 8], 'cur_cost': 861.0}, {'tour': [7, 5, 6, 0, 4, 2, 8, 3, 1], 'cur_cost': 846.0}, {'tour': [7, 5, 3, 4, 8, 6, 1, 0, 2], 'cur_cost': 908.0}, {'tour': [1, 4, 0, 6, 7, 5, 3, 8, 2], 'cur_cost': 819.0}, {'tour': array([3, 1, 8, 4, 0, 7, 6, 2, 5], dtype=int64), 'cur_cost': 1110.0}, {'tour': [3, 7, 5, 8, 2, 4, 0, 1, 6], 'cur_cost': 732.0}, {'tour': [0, 8, 4, 5, 2, 3, 7, 6, 1], 'cur_cost': 999.0}, {'tour': array([5, 6, 4, 0, 2, 7, 3, 8, 1], dtype=int64), 'cur_cost': 1041.0}]
2025-08-04 17:02:19,435 - ExploitationExpert - INFO - 局部搜索耗时: 1.41秒，最大迭代次数: 10
2025-08-04 17:02:19,435 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-04 17:02:19,436 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([5, 6, 4, 0, 2, 7, 3, 8, 1], dtype=int64), 'cur_cost': 1041.0, 'intermediate_solutions': [{'tour': array([6, 8, 5, 2, 0, 3, 1, 4, 7], dtype=int64), 'cur_cost': 1110.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 6, 8, 5, 0, 3, 1, 4, 7], dtype=int64), 'cur_cost': 1176.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 2, 6, 8, 5, 3, 1, 4, 7], dtype=int64), 'cur_cost': 1067.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 2, 6, 8, 0, 3, 1, 4, 7], dtype=int64), 'cur_cost': 1200.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 0, 2, 6, 8, 3, 1, 4, 7], dtype=int64), 'cur_cost': 1099.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:02:19,436 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1041.00)
2025-08-04 17:02:19,436 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:02:19,436 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:02:19,437 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [2, 3, 4, 5, 6, 7, 0, 1, 8], 'cur_cost': 966.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [2, 8, 4, 3, 0, 7, 1, 5, 6], 'cur_cost': 1074.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 2, 7, 1, 6, 5, 3, 8], 'cur_cost': 861.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [7, 5, 6, 0, 4, 2, 8, 3, 1], 'cur_cost': 846.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [7, 5, 3, 4, 8, 6, 1, 0, 2], 'cur_cost': 908.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 0, 6, 7, 5, 3, 8, 2], 'cur_cost': 819.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 1, 8, 4, 0, 7, 6, 2, 5], dtype=int64), 'cur_cost': 1110.0, 'intermediate_solutions': [{'tour': array([7, 1, 3, 4, 5, 8, 2, 0, 6], dtype=int64), 'cur_cost': 1057.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 7, 1, 3, 5, 8, 2, 0, 6], dtype=int64), 'cur_cost': 1018.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 4, 7, 1, 3, 8, 2, 0, 6], dtype=int64), 'cur_cost': 998.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 4, 7, 1, 5, 8, 2, 0, 6], dtype=int64), 'cur_cost': 1029.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 5, 4, 7, 1, 8, 2, 0, 6], dtype=int64), 'cur_cost': 1023.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [3, 7, 5, 8, 2, 4, 0, 1, 6], 'cur_cost': 732.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 4, 5, 2, 3, 7, 6, 1], 'cur_cost': 999.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([5, 6, 4, 0, 2, 7, 3, 8, 1], dtype=int64), 'cur_cost': 1041.0, 'intermediate_solutions': [{'tour': array([6, 8, 5, 2, 0, 3, 1, 4, 7], dtype=int64), 'cur_cost': 1110.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 6, 8, 5, 0, 3, 1, 4, 7], dtype=int64), 'cur_cost': 1176.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 2, 6, 8, 5, 3, 1, 4, 7], dtype=int64), 'cur_cost': 1067.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 2, 6, 8, 0, 3, 1, 4, 7], dtype=int64), 'cur_cost': 1200.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 0, 2, 6, 8, 3, 1, 4, 7], dtype=int64), 'cur_cost': 1099.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-04 17:02:19,437 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:02:19,438 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:02:19,439 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=732.000, 多样性=0.904
2025-08-04 17:02:19,439 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-04 17:02:19,439 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-04 17:02:19,439 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:02:19,439 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.026154746963719593, 'best_improvement': -0.07488986784140969}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.0027397260273973466}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.4444444444444444, 'new_diversity': 0.4444444444444444, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-04 17:02:19,445 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-04 17:02:19,446 - __main__ - INFO - simple1_9 开始进化第 2 代
2025-08-04 17:02:19,446 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-04 17:02:19,446 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:02:19,446 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=732.000, 多样性=0.904
2025-08-04 17:02:19,447 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:02:19,447 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.904
2025-08-04 17:02:19,447 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:02:19,448 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.444
2025-08-04 17:02:19,450 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-04 17:02:19,450 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:02:19,450 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-04 17:02:19,450 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-04 17:02:19,458 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.250, 适应度梯度: 40.117, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 0.672
2025-08-04 17:02:19,458 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-04 17:02:19,458 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:02:19,458 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:02:19,463 - visualization.landscape_visualizer - INFO - 插值约束: 133 个点被约束到最小值 680.00
2025-08-04 17:02:19,467 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:02:19,542 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_2_20250804_170219.html
2025-08-04 17:02:19,579 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_2_20250804_170219.html
2025-08-04 17:02:19,579 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 2
2025-08-04 17:02:19,579 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-04 17:02:19,579 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1285秒
2025-08-04 17:02:19,579 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.25, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 40.11666666666667, 'local_optima_density': 0.25, 'gradient_variance': 20000.063055555558, 'cluster_count': 0}, 'population_state': {'diversity': 0.6717171717171717, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0022, 'fitness_entropy': 0.9731973151785931, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 40.117)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754298139.4587786, 'performance_metrics': {}}}
2025-08-04 17:02:19,580 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:02:19,580 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:02:19,580 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:02:19,580 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:02:19,580 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:02:19,581 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-04 17:02:19,581 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:02:19,581 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:02:19,581 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:02:19,581 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:02:19,581 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:02:19,581 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:02:19,582 - experts.management.collaboration_manager - INFO - 识别精英个体: {5, 7} (总数: 2, 保护比例: 0.20)
2025-08-04 17:02:19,582 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:02:19,582 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:02:19,582 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:19,582 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:02:19,583 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,583 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,583 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,583 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,583 - ExplorationExpert - INFO - 探索路径生成完成，成本: 831.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:02:19,583 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [6, 0, 4, 2, 8, 3, 7, 5, 1], 'cur_cost': 831.0, 'intermediate_solutions': [{'tour': [2, 1, 4, 5, 6, 7, 0, 3, 8], 'cur_cost': 924.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 3, 4, 5, 8, 1, 0, 7, 6], 'cur_cost': 1084.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 3, 4, 6, 7, 5, 0, 1, 8], 'cur_cost': 1018.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:02:19,583 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 831.00)
2025-08-04 17:02:19,584 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-04 17:02:19,584 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:02:19,584 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:02:19,584 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 971.0
2025-08-04 17:02:19,650 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:02:19,650 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 17:02:19,650 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64)]
2025-08-04 17:02:19,650 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:02:19,651 - ExploitationExpert - INFO - populations: [{'tour': [6, 0, 4, 2, 8, 3, 7, 5, 1], 'cur_cost': 831.0}, {'tour': array([0, 5, 7, 3, 2, 4, 6, 8, 1], dtype=int64), 'cur_cost': 971.0}, {'tour': [0, 4, 2, 7, 1, 6, 5, 3, 8], 'cur_cost': 861.0}, {'tour': [7, 5, 6, 0, 4, 2, 8, 3, 1], 'cur_cost': 846.0}, {'tour': [7, 5, 3, 4, 8, 6, 1, 0, 2], 'cur_cost': 908.0}, {'tour': [1, 4, 0, 6, 7, 5, 3, 8, 2], 'cur_cost': 819.0}, {'tour': [3, 1, 8, 4, 0, 7, 6, 2, 5], 'cur_cost': 1110.0}, {'tour': [3, 7, 5, 8, 2, 4, 0, 1, 6], 'cur_cost': 732.0}, {'tour': [0, 8, 4, 5, 2, 3, 7, 6, 1], 'cur_cost': 999.0}, {'tour': [5, 6, 4, 0, 2, 7, 3, 8, 1], 'cur_cost': 1041.0}]
2025-08-04 17:02:19,651 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:02:19,652 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-04 17:02:19,652 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([0, 5, 7, 3, 2, 4, 6, 8, 1], dtype=int64), 'cur_cost': 971.0, 'intermediate_solutions': [{'tour': array([4, 8, 2, 3, 0, 7, 1, 5, 6]), 'cur_cost': 1062.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 4, 8, 2, 0, 7, 1, 5, 6]), 'cur_cost': 988.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 3, 4, 8, 2, 7, 1, 5, 6]), 'cur_cost': 1028.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 3, 4, 8, 0, 7, 1, 5, 6]), 'cur_cost': 1136.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 0, 3, 4, 8, 7, 1, 5, 6]), 'cur_cost': 1103.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:02:19,652 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 971.00)
2025-08-04 17:02:19,652 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:02:19,652 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:02:19,653 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:19,653 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:02:19,653 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,653 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,653 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,654 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,654 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1020.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:02:19,654 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [4, 5, 0, 3, 7, 6, 8, 2, 1], 'cur_cost': 1020.0, 'intermediate_solutions': [{'tour': [0, 4, 2, 7, 1, 5, 6, 3, 8], 'cur_cost': 946.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 2, 7, 1, 6, 5, 8, 3], 'cur_cost': 885.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 7, 4, 1, 6, 5, 3, 8], 'cur_cost': 924.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:02:19,654 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1020.00)
2025-08-04 17:02:19,654 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:02:19,654 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:02:19,654 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:19,655 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:02:19,655 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,655 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,655 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,655 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,655 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1035.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:02:19,655 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [6, 4, 2, 0, 5, 1, 3, 8, 7], 'cur_cost': 1035.0, 'intermediate_solutions': [{'tour': [7, 5, 6, 0, 4, 2, 3, 8, 1], 'cur_cost': 906.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 6, 2, 4, 0, 8, 3, 1], 'cur_cost': 975.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 5, 6, 0, 4, 2, 3, 8, 1], 'cur_cost': 906.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:02:19,655 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1035.00)
2025-08-04 17:02:19,656 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:02:19,656 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:02:19,656 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:19,656 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:02:19,656 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,656 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,656 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,656 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,657 - ExplorationExpert - INFO - 探索路径生成完成，成本: 959.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:02:19,657 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [4, 1, 2, 6, 5, 3, 8, 0, 7], 'cur_cost': 959.0, 'intermediate_solutions': [{'tour': [7, 5, 3, 4, 8, 6, 0, 1, 2], 'cur_cost': 897.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 3, 4, 0, 1, 6, 8, 2], 'cur_cost': 871.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 3, 4, 8, 6, 1, 0, 7, 2], 'cur_cost': 967.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:02:19,657 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 959.00)
2025-08-04 17:02:19,657 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:02:19,657 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:02:19,657 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:19,658 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:02:19,658 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,658 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,658 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,658 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,658 - ExplorationExpert - INFO - 探索路径生成完成，成本: 680.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:02:19,658 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [5, 6, 7, 0, 1, 4, 2, 8, 3], 'cur_cost': 680.0, 'intermediate_solutions': [{'tour': [1, 4, 0, 6, 2, 5, 3, 8, 7], 'cur_cost': 1002.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 0, 6, 7, 5, 3, 2, 8], 'cur_cost': 928.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 0, 6, 7, 5, 3, 8, 2], 'cur_cost': 819.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:02:19,659 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 680.00)
2025-08-04 17:02:19,659 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-04 17:02:19,659 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:02:19,659 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:02:19,659 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 994.0
2025-08-04 17:02:19,720 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:02:19,721 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 17:02:19,721 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64)]
2025-08-04 17:02:19,721 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:02:19,722 - ExploitationExpert - INFO - populations: [{'tour': [6, 0, 4, 2, 8, 3, 7, 5, 1], 'cur_cost': 831.0}, {'tour': array([0, 5, 7, 3, 2, 4, 6, 8, 1], dtype=int64), 'cur_cost': 971.0}, {'tour': [4, 5, 0, 3, 7, 6, 8, 2, 1], 'cur_cost': 1020.0}, {'tour': [6, 4, 2, 0, 5, 1, 3, 8, 7], 'cur_cost': 1035.0}, {'tour': [4, 1, 2, 6, 5, 3, 8, 0, 7], 'cur_cost': 959.0}, {'tour': [5, 6, 7, 0, 1, 4, 2, 8, 3], 'cur_cost': 680.0}, {'tour': array([4, 3, 1, 0, 2, 6, 5, 8, 7], dtype=int64), 'cur_cost': 994.0}, {'tour': [3, 7, 5, 8, 2, 4, 0, 1, 6], 'cur_cost': 732.0}, {'tour': [0, 8, 4, 5, 2, 3, 7, 6, 1], 'cur_cost': 999.0}, {'tour': [5, 6, 4, 0, 2, 7, 3, 8, 1], 'cur_cost': 1041.0}]
2025-08-04 17:02:19,722 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒，最大迭代次数: 10
2025-08-04 17:02:19,722 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-04 17:02:19,723 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([4, 3, 1, 0, 2, 6, 5, 8, 7], dtype=int64), 'cur_cost': 994.0, 'intermediate_solutions': [{'tour': array([8, 1, 3, 4, 0, 7, 6, 2, 5]), 'cur_cost': 1173.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 8, 1, 3, 0, 7, 6, 2, 5]), 'cur_cost': 1229.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 4, 8, 1, 3, 7, 6, 2, 5]), 'cur_cost': 1152.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 4, 8, 1, 0, 7, 6, 2, 5]), 'cur_cost': 1024.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 0, 4, 8, 1, 7, 6, 2, 5]), 'cur_cost': 1110.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:02:19,723 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 994.00)
2025-08-04 17:02:19,723 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:02:19,723 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:02:19,724 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:19,724 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:02:19,724 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,724 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,724 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,725 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,725 - ExplorationExpert - INFO - 探索路径生成完成，成本: 842.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:02:19,725 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [8, 5, 3, 7, 0, 4, 2, 1, 6], 'cur_cost': 842.0, 'intermediate_solutions': [{'tour': [3, 4, 5, 8, 2, 7, 0, 1, 6], 'cur_cost': 938.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 7, 4, 2, 8, 5, 0, 1, 6], 'cur_cost': 798.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 7, 5, 0, 8, 2, 4, 1, 6], 'cur_cost': 877.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:02:19,725 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 842.00)
2025-08-04 17:02:19,725 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:02:19,725 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:02:19,725 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:19,725 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:02:19,726 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,726 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,726 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,726 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,726 - ExplorationExpert - INFO - 探索路径生成完成，成本: 944.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:02:19,726 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [2, 7, 5, 0, 6, 3, 8, 4, 1], 'cur_cost': 944.0, 'intermediate_solutions': [{'tour': [0, 3, 4, 5, 2, 8, 7, 6, 1], 'cur_cost': 989.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 3, 2, 5, 4, 8, 6, 1], 'cur_cost': 1005.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 4, 5, 2, 3, 7, 0, 6, 1], 'cur_cost': 1095.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:02:19,726 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 944.00)
2025-08-04 17:02:19,726 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:02:19,726 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:02:19,727 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:19,727 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:02:19,727 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,727 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,727 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,727 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,727 - ExplorationExpert - INFO - 探索路径生成完成，成本: 872.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:02:19,727 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [8, 7, 6, 3, 5, 4, 2, 0, 1], 'cur_cost': 872.0, 'intermediate_solutions': [{'tour': [5, 6, 4, 0, 2, 3, 7, 8, 1], 'cur_cost': 1068.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 6, 4, 2, 0, 7, 3, 8, 1], 'cur_cost': 965.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 6, 4, 2, 7, 3, 8, 1], 'cur_cost': 889.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:02:19,727 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 872.00)
2025-08-04 17:02:19,727 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:02:19,727 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:02:19,728 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [6, 0, 4, 2, 8, 3, 7, 5, 1], 'cur_cost': 831.0, 'intermediate_solutions': [{'tour': [2, 1, 4, 5, 6, 7, 0, 3, 8], 'cur_cost': 924.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 3, 4, 5, 8, 1, 0, 7, 6], 'cur_cost': 1084.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 3, 4, 6, 7, 5, 0, 1, 8], 'cur_cost': 1018.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 5, 7, 3, 2, 4, 6, 8, 1], dtype=int64), 'cur_cost': 971.0, 'intermediate_solutions': [{'tour': array([4, 8, 2, 3, 0, 7, 1, 5, 6]), 'cur_cost': 1062.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 4, 8, 2, 0, 7, 1, 5, 6]), 'cur_cost': 988.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 3, 4, 8, 2, 7, 1, 5, 6]), 'cur_cost': 1028.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 3, 4, 8, 0, 7, 1, 5, 6]), 'cur_cost': 1136.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 0, 3, 4, 8, 7, 1, 5, 6]), 'cur_cost': 1103.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [4, 5, 0, 3, 7, 6, 8, 2, 1], 'cur_cost': 1020.0, 'intermediate_solutions': [{'tour': [0, 4, 2, 7, 1, 5, 6, 3, 8], 'cur_cost': 946.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 2, 7, 1, 6, 5, 8, 3], 'cur_cost': 885.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 7, 4, 1, 6, 5, 3, 8], 'cur_cost': 924.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [6, 4, 2, 0, 5, 1, 3, 8, 7], 'cur_cost': 1035.0, 'intermediate_solutions': [{'tour': [7, 5, 6, 0, 4, 2, 3, 8, 1], 'cur_cost': 906.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 6, 2, 4, 0, 8, 3, 1], 'cur_cost': 975.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 5, 6, 0, 4, 2, 3, 8, 1], 'cur_cost': 906.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [4, 1, 2, 6, 5, 3, 8, 0, 7], 'cur_cost': 959.0, 'intermediate_solutions': [{'tour': [7, 5, 3, 4, 8, 6, 0, 1, 2], 'cur_cost': 897.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 3, 4, 0, 1, 6, 8, 2], 'cur_cost': 871.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 3, 4, 8, 6, 1, 0, 7, 2], 'cur_cost': 967.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [5, 6, 7, 0, 1, 4, 2, 8, 3], 'cur_cost': 680.0, 'intermediate_solutions': [{'tour': [1, 4, 0, 6, 2, 5, 3, 8, 7], 'cur_cost': 1002.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 0, 6, 7, 5, 3, 2, 8], 'cur_cost': 928.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 0, 6, 7, 5, 3, 8, 2], 'cur_cost': 819.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 3, 1, 0, 2, 6, 5, 8, 7], dtype=int64), 'cur_cost': 994.0, 'intermediate_solutions': [{'tour': array([8, 1, 3, 4, 0, 7, 6, 2, 5]), 'cur_cost': 1173.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 8, 1, 3, 0, 7, 6, 2, 5]), 'cur_cost': 1229.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 4, 8, 1, 3, 7, 6, 2, 5]), 'cur_cost': 1152.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 4, 8, 1, 0, 7, 6, 2, 5]), 'cur_cost': 1024.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 0, 4, 8, 1, 7, 6, 2, 5]), 'cur_cost': 1110.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [8, 5, 3, 7, 0, 4, 2, 1, 6], 'cur_cost': 842.0, 'intermediate_solutions': [{'tour': [3, 4, 5, 8, 2, 7, 0, 1, 6], 'cur_cost': 938.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 7, 4, 2, 8, 5, 0, 1, 6], 'cur_cost': 798.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 7, 5, 0, 8, 2, 4, 1, 6], 'cur_cost': 877.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [2, 7, 5, 0, 6, 3, 8, 4, 1], 'cur_cost': 944.0, 'intermediate_solutions': [{'tour': [0, 3, 4, 5, 2, 8, 7, 6, 1], 'cur_cost': 989.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 3, 2, 5, 4, 8, 6, 1], 'cur_cost': 1005.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 4, 5, 2, 3, 7, 0, 6, 1], 'cur_cost': 1095.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [8, 7, 6, 3, 5, 4, 2, 0, 1], 'cur_cost': 872.0, 'intermediate_solutions': [{'tour': [5, 6, 4, 0, 2, 3, 7, 8, 1], 'cur_cost': 1068.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 6, 4, 2, 0, 7, 3, 8, 1], 'cur_cost': 965.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 6, 4, 2, 7, 3, 8, 1], 'cur_cost': 889.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:02:19,728 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:02:19,729 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:02:19,730 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=680.000, 多样性=0.852
2025-08-04 17:02:19,730 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-04 17:02:19,730 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-04 17:02:19,730 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:02:19,730 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.05361251396407238, 'best_improvement': 0.07103825136612021}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.05737704918032783}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-04 17:02:19,730 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-04 17:02:19,730 - __main__ - INFO - simple1_9 开始进化第 3 代
2025-08-04 17:02:19,731 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-04 17:02:19,731 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:02:19,731 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=680.000, 多样性=0.852
2025-08-04 17:02:19,731 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:02:19,732 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.852
2025-08-04 17:02:19,732 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:02:19,733 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.630
2025-08-04 17:02:19,734 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-04 17:02:19,734 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:02:19,734 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-04 17:02:19,734 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-04 17:02:19,742 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.308, 适应度梯度: 25.369, 聚类评分: 0.000, 覆盖率: 0.003, 收敛趋势: 0.000, 多样性: 0.581
2025-08-04 17:02:19,742 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-04 17:02:19,742 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:02:19,742 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:02:19,745 - visualization.landscape_visualizer - INFO - 插值约束: 110 个点被约束到最小值 680.00
2025-08-04 17:02:19,749 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:02:19,826 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_3_20250804_170219.html
2025-08-04 17:02:19,862 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_3_20250804_170219.html
2025-08-04 17:02:19,863 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 3
2025-08-04 17:02:19,863 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-04 17:02:19,863 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1290秒
2025-08-04 17:02:19,863 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3076923076923077, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 25.369230769230764, 'local_optima_density': 0.3076923076923077, 'gradient_variance': 21421.00828402367, 'cluster_count': 0}, 'population_state': {'diversity': 0.5808678500986193, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0033, 'fitness_entropy': 0.9553989609841033, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.003)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 25.369)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754298139.7429116, 'performance_metrics': {}}}
2025-08-04 17:02:19,863 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:02:19,863 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:02:19,863 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:02:19,864 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:02:19,864 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:02:19,864 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-04 17:02:19,864 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:02:19,864 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:02:19,865 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:02:19,865 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:02:19,865 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:02:19,865 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:02:19,865 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 5} (总数: 2, 保护比例: 0.20)
2025-08-04 17:02:19,865 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:02:19,865 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:02:19,865 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:19,866 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:02:19,866 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,866 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,866 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,867 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,867 - ExplorationExpert - INFO - 探索路径生成完成，成本: 758.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:02:19,867 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [3, 5, 7, 8, 4, 2, 0, 1, 6], 'cur_cost': 758.0, 'intermediate_solutions': [{'tour': [6, 0, 4, 2, 8, 7, 3, 5, 1], 'cur_cost': 830.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 2, 4, 0, 8, 3, 7, 5, 1], 'cur_cost': 960.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 4, 2, 5, 8, 3, 7, 1], 'cur_cost': 891.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:02:19,867 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 758.00)
2025-08-04 17:02:19,867 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:02:19,867 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:02:19,867 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:19,867 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:02:19,868 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,868 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,868 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,868 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,868 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1120.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:02:19,868 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [1, 5, 0, 3, 2, 7, 6, 8, 4], 'cur_cost': 1120.0, 'intermediate_solutions': [{'tour': [0, 5, 7, 3, 8, 4, 6, 2, 1], 'cur_cost': 951.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 7, 3, 2, 6, 4, 8, 1], 'cur_cost': 1060.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 3, 2, 4, 7, 6, 8, 1], 'cur_cost': 928.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:02:19,868 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1120.00)
2025-08-04 17:02:19,868 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-04 17:02:19,868 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:02:19,869 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:02:19,869 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 1054.0
2025-08-04 17:02:19,930 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:02:19,930 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 17:02:19,930 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64)]
2025-08-04 17:02:19,931 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:02:19,931 - ExploitationExpert - INFO - populations: [{'tour': [3, 5, 7, 8, 4, 2, 0, 1, 6], 'cur_cost': 758.0}, {'tour': [1, 5, 0, 3, 2, 7, 6, 8, 4], 'cur_cost': 1120.0}, {'tour': array([7, 4, 2, 5, 6, 3, 0, 8, 1], dtype=int64), 'cur_cost': 1054.0}, {'tour': [6, 4, 2, 0, 5, 1, 3, 8, 7], 'cur_cost': 1035.0}, {'tour': [4, 1, 2, 6, 5, 3, 8, 0, 7], 'cur_cost': 959.0}, {'tour': [5, 6, 7, 0, 1, 4, 2, 8, 3], 'cur_cost': 680.0}, {'tour': [4, 3, 1, 0, 2, 6, 5, 8, 7], 'cur_cost': 994.0}, {'tour': [8, 5, 3, 7, 0, 4, 2, 1, 6], 'cur_cost': 842.0}, {'tour': [2, 7, 5, 0, 6, 3, 8, 4, 1], 'cur_cost': 944.0}, {'tour': [8, 7, 6, 3, 5, 4, 2, 0, 1], 'cur_cost': 872.0}]
2025-08-04 17:02:19,932 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒，最大迭代次数: 10
2025-08-04 17:02:19,932 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-04 17:02:19,933 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([7, 4, 2, 5, 6, 3, 0, 8, 1], dtype=int64), 'cur_cost': 1054.0, 'intermediate_solutions': [{'tour': array([0, 5, 4, 3, 7, 6, 8, 2, 1]), 'cur_cost': 953.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 0, 5, 4, 7, 6, 8, 2, 1]), 'cur_cost': 1112.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 3, 0, 5, 4, 6, 8, 2, 1]), 'cur_cost': 1106.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 3, 0, 5, 7, 6, 8, 2, 1]), 'cur_cost': 1010.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 7, 3, 0, 5, 6, 8, 2, 1]), 'cur_cost': 961.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:02:19,933 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1054.00)
2025-08-04 17:02:19,933 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-04 17:02:19,933 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:02:19,933 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:02:19,933 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 861.0
2025-08-04 17:02:19,992 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:02:19,992 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 17:02:19,992 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64)]
2025-08-04 17:02:19,993 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:02:19,993 - ExploitationExpert - INFO - populations: [{'tour': [3, 5, 7, 8, 4, 2, 0, 1, 6], 'cur_cost': 758.0}, {'tour': [1, 5, 0, 3, 2, 7, 6, 8, 4], 'cur_cost': 1120.0}, {'tour': array([7, 4, 2, 5, 6, 3, 0, 8, 1], dtype=int64), 'cur_cost': 1054.0}, {'tour': array([6, 7, 1, 2, 0, 4, 8, 3, 5], dtype=int64), 'cur_cost': 861.0}, {'tour': [4, 1, 2, 6, 5, 3, 8, 0, 7], 'cur_cost': 959.0}, {'tour': [5, 6, 7, 0, 1, 4, 2, 8, 3], 'cur_cost': 680.0}, {'tour': [4, 3, 1, 0, 2, 6, 5, 8, 7], 'cur_cost': 994.0}, {'tour': [8, 5, 3, 7, 0, 4, 2, 1, 6], 'cur_cost': 842.0}, {'tour': [2, 7, 5, 0, 6, 3, 8, 4, 1], 'cur_cost': 944.0}, {'tour': [8, 7, 6, 3, 5, 4, 2, 0, 1], 'cur_cost': 872.0}]
2025-08-04 17:02:19,994 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒，最大迭代次数: 10
2025-08-04 17:02:19,994 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-04 17:02:19,994 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([6, 7, 1, 2, 0, 4, 8, 3, 5], dtype=int64), 'cur_cost': 861.0, 'intermediate_solutions': [{'tour': array([2, 4, 6, 0, 5, 1, 3, 8, 7]), 'cur_cost': 1054.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 2, 4, 6, 5, 1, 3, 8, 7]), 'cur_cost': 980.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 0, 2, 4, 6, 1, 3, 8, 7]), 'cur_cost': 978.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 0, 2, 4, 5, 1, 3, 8, 7]), 'cur_cost': 997.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 5, 0, 2, 4, 1, 3, 8, 7]), 'cur_cost': 897.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:02:19,995 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 861.00)
2025-08-04 17:02:19,995 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:02:19,995 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:02:19,995 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:19,995 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:02:19,995 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,996 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,996 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,996 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,996 - ExplorationExpert - INFO - 探索路径生成完成，成本: 916.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:02:19,996 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [4, 6, 3, 7, 2, 8, 5, 0, 1], 'cur_cost': 916.0, 'intermediate_solutions': [{'tour': [4, 1, 2, 6, 3, 5, 8, 0, 7], 'cur_cost': 1010.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 1, 3, 5, 6, 2, 8, 0, 7], 'cur_cost': 1008.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 1, 2, 6, 5, 8, 3, 0, 7], 'cur_cost': 983.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:02:19,997 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 916.00)
2025-08-04 17:02:19,997 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:02:19,997 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:02:19,997 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:19,997 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:02:19,997 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,997 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,998 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,998 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:19,998 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1144.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:02:19,998 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [4, 6, 2, 5, 0, 7, 3, 8, 1], 'cur_cost': 1144.0, 'intermediate_solutions': [{'tour': [5, 6, 7, 0, 1, 4, 8, 2, 3], 'cur_cost': 791.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 2, 4, 1, 0, 7, 6, 8, 3], 'cur_cost': 823.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 0, 1, 4, 6, 2, 8, 3], 'cur_cost': 883.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:02:19,998 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 1144.00)
2025-08-04 17:02:19,998 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-04 17:02:19,998 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:02:19,998 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:02:19,999 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1003.0
2025-08-04 17:02:20,061 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:02:20,061 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 17:02:20,062 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64)]
2025-08-04 17:02:20,062 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:02:20,063 - ExploitationExpert - INFO - populations: [{'tour': [3, 5, 7, 8, 4, 2, 0, 1, 6], 'cur_cost': 758.0}, {'tour': [1, 5, 0, 3, 2, 7, 6, 8, 4], 'cur_cost': 1120.0}, {'tour': array([7, 4, 2, 5, 6, 3, 0, 8, 1], dtype=int64), 'cur_cost': 1054.0}, {'tour': array([6, 7, 1, 2, 0, 4, 8, 3, 5], dtype=int64), 'cur_cost': 861.0}, {'tour': [4, 6, 3, 7, 2, 8, 5, 0, 1], 'cur_cost': 916.0}, {'tour': [4, 6, 2, 5, 0, 7, 3, 8, 1], 'cur_cost': 1144.0}, {'tour': array([3, 1, 6, 5, 2, 8, 7, 0, 4], dtype=int64), 'cur_cost': 1003.0}, {'tour': [8, 5, 3, 7, 0, 4, 2, 1, 6], 'cur_cost': 842.0}, {'tour': [2, 7, 5, 0, 6, 3, 8, 4, 1], 'cur_cost': 944.0}, {'tour': [8, 7, 6, 3, 5, 4, 2, 0, 1], 'cur_cost': 872.0}]
2025-08-04 17:02:20,064 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:02:20,064 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-04 17:02:20,065 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([3, 1, 6, 5, 2, 8, 7, 0, 4], dtype=int64), 'cur_cost': 1003.0, 'intermediate_solutions': [{'tour': array([1, 3, 4, 0, 2, 6, 5, 8, 7]), 'cur_cost': 1067.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 1, 3, 4, 2, 6, 5, 8, 7]), 'cur_cost': 912.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 0, 1, 3, 4, 6, 5, 8, 7]), 'cur_cost': 990.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 0, 1, 3, 2, 6, 5, 8, 7]), 'cur_cost': 980.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 2, 0, 1, 3, 6, 5, 8, 7]), 'cur_cost': 839.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:02:20,065 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1003.00)
2025-08-04 17:02:20,065 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:02:20,065 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:02:20,065 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:20,066 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:02:20,066 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,066 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,066 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,066 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,066 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1121.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:02:20,067 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [1, 7, 8, 5, 2, 6, 0, 3, 4], 'cur_cost': 1121.0, 'intermediate_solutions': [{'tour': [8, 5, 3, 6, 0, 4, 2, 1, 7], 'cur_cost': 847.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 5, 3, 7, 6, 1, 2, 4, 0], 'cur_cost': 836.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 3, 7, 0, 4, 2, 1, 6, 8], 'cur_cost': 842.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:02:20,067 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1121.00)
2025-08-04 17:02:20,067 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:02:20,067 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:02:20,067 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:20,067 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:02:20,067 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,067 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,068 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,068 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,068 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1030.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:02:20,068 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [7, 8, 5, 2, 3, 1, 0, 4, 6], 'cur_cost': 1030.0, 'intermediate_solutions': [{'tour': [2, 4, 5, 0, 6, 3, 8, 7, 1], 'cur_cost': 949.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 7, 8, 3, 6, 0, 5, 4, 1], 'cur_cost': 1006.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 7, 5, 0, 4, 6, 3, 8, 1], 'cur_cost': 1055.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:02:20,068 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1030.00)
2025-08-04 17:02:20,068 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:02:20,068 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:02:20,068 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:20,069 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:02:20,069 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,069 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,069 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,069 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,069 - ExplorationExpert - INFO - 探索路径生成完成，成本: 946.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:02:20,069 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [6, 8, 0, 1, 5, 3, 7, 4, 2], 'cur_cost': 946.0, 'intermediate_solutions': [{'tour': [8, 7, 6, 3, 1, 4, 2, 0, 5], 'cur_cost': 948.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 7, 6, 3, 5, 0, 2, 4, 1], 'cur_cost': 922.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 6, 3, 5, 4, 2, 8, 0, 1], 'cur_cost': 845.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:02:20,070 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 946.00)
2025-08-04 17:02:20,070 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:02:20,070 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:02:20,071 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [3, 5, 7, 8, 4, 2, 0, 1, 6], 'cur_cost': 758.0, 'intermediate_solutions': [{'tour': [6, 0, 4, 2, 8, 7, 3, 5, 1], 'cur_cost': 830.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 2, 4, 0, 8, 3, 7, 5, 1], 'cur_cost': 960.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 4, 2, 5, 8, 3, 7, 1], 'cur_cost': 891.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [1, 5, 0, 3, 2, 7, 6, 8, 4], 'cur_cost': 1120.0, 'intermediate_solutions': [{'tour': [0, 5, 7, 3, 8, 4, 6, 2, 1], 'cur_cost': 951.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 7, 3, 2, 6, 4, 8, 1], 'cur_cost': 1060.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 3, 2, 4, 7, 6, 8, 1], 'cur_cost': 928.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 4, 2, 5, 6, 3, 0, 8, 1], dtype=int64), 'cur_cost': 1054.0, 'intermediate_solutions': [{'tour': array([0, 5, 4, 3, 7, 6, 8, 2, 1]), 'cur_cost': 953.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 0, 5, 4, 7, 6, 8, 2, 1]), 'cur_cost': 1112.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 3, 0, 5, 4, 6, 8, 2, 1]), 'cur_cost': 1106.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 3, 0, 5, 7, 6, 8, 2, 1]), 'cur_cost': 1010.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 7, 3, 0, 5, 6, 8, 2, 1]), 'cur_cost': 961.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 7, 1, 2, 0, 4, 8, 3, 5], dtype=int64), 'cur_cost': 861.0, 'intermediate_solutions': [{'tour': array([2, 4, 6, 0, 5, 1, 3, 8, 7]), 'cur_cost': 1054.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 2, 4, 6, 5, 1, 3, 8, 7]), 'cur_cost': 980.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 0, 2, 4, 6, 1, 3, 8, 7]), 'cur_cost': 978.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 0, 2, 4, 5, 1, 3, 8, 7]), 'cur_cost': 997.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 5, 0, 2, 4, 1, 3, 8, 7]), 'cur_cost': 897.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [4, 6, 3, 7, 2, 8, 5, 0, 1], 'cur_cost': 916.0, 'intermediate_solutions': [{'tour': [4, 1, 2, 6, 3, 5, 8, 0, 7], 'cur_cost': 1010.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 1, 3, 5, 6, 2, 8, 0, 7], 'cur_cost': 1008.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 1, 2, 6, 5, 8, 3, 0, 7], 'cur_cost': 983.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [4, 6, 2, 5, 0, 7, 3, 8, 1], 'cur_cost': 1144.0, 'intermediate_solutions': [{'tour': [5, 6, 7, 0, 1, 4, 8, 2, 3], 'cur_cost': 791.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 2, 4, 1, 0, 7, 6, 8, 3], 'cur_cost': 823.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 0, 1, 4, 6, 2, 8, 3], 'cur_cost': 883.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 1, 6, 5, 2, 8, 7, 0, 4], dtype=int64), 'cur_cost': 1003.0, 'intermediate_solutions': [{'tour': array([1, 3, 4, 0, 2, 6, 5, 8, 7]), 'cur_cost': 1067.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 1, 3, 4, 2, 6, 5, 8, 7]), 'cur_cost': 912.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 0, 1, 3, 4, 6, 5, 8, 7]), 'cur_cost': 990.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 0, 1, 3, 2, 6, 5, 8, 7]), 'cur_cost': 980.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 2, 0, 1, 3, 6, 5, 8, 7]), 'cur_cost': 839.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 8, 5, 2, 6, 0, 3, 4], 'cur_cost': 1121.0, 'intermediate_solutions': [{'tour': [8, 5, 3, 6, 0, 4, 2, 1, 7], 'cur_cost': 847.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 5, 3, 7, 6, 1, 2, 4, 0], 'cur_cost': 836.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 3, 7, 0, 4, 2, 1, 6, 8], 'cur_cost': 842.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [7, 8, 5, 2, 3, 1, 0, 4, 6], 'cur_cost': 1030.0, 'intermediate_solutions': [{'tour': [2, 4, 5, 0, 6, 3, 8, 7, 1], 'cur_cost': 949.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 7, 8, 3, 6, 0, 5, 4, 1], 'cur_cost': 1006.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 7, 5, 0, 4, 6, 3, 8, 1], 'cur_cost': 1055.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [6, 8, 0, 1, 5, 3, 7, 4, 2], 'cur_cost': 946.0, 'intermediate_solutions': [{'tour': [8, 7, 6, 3, 1, 4, 2, 0, 5], 'cur_cost': 948.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 7, 6, 3, 5, 0, 2, 4, 1], 'cur_cost': 922.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 6, 3, 5, 4, 2, 8, 0, 1], 'cur_cost': 845.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:02:20,071 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:02:20,071 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:02:20,072 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=758.000, 多样性=0.881
2025-08-04 17:02:20,073 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-04 17:02:20,073 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-04 17:02:20,073 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:02:20,073 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.10267242283016977, 'best_improvement': -0.11470588235294117}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.03478260869565209}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:02:20,073 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-04 17:02:20,073 - __main__ - INFO - simple1_9 开始进化第 4 代
2025-08-04 17:02:20,074 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-04 17:02:20,074 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:02:20,074 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=758.000, 多样性=0.881
2025-08-04 17:02:20,074 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:02:20,075 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.881
2025-08-04 17:02:20,075 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:02:20,076 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.630
2025-08-04 17:02:20,077 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-04 17:02:20,078 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:02:20,078 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-04 17:02:20,078 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-04 17:02:20,085 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.308, 适应度梯度: 13.477, 聚类评分: 0.000, 覆盖率: 0.004, 收敛趋势: 0.000, 多样性: 0.600
2025-08-04 17:02:20,085 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-04 17:02:20,086 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:02:20,086 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:02:20,089 - visualization.landscape_visualizer - INFO - 插值约束: 115 个点被约束到最小值 680.00
2025-08-04 17:02:20,093 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:02:20,254 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_4_20250804_170220.html
2025-08-04 17:02:20,291 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_4_20250804_170220.html
2025-08-04 17:02:20,292 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 4
2025-08-04 17:02:20,292 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-04 17:02:20,292 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2155秒
2025-08-04 17:02:20,292 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3076923076923077, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 13.476923076923075, 'local_optima_density': 0.3076923076923077, 'gradient_variance': 35236.514082840244, 'cluster_count': 0}, 'population_state': {'diversity': 0.5996055226824457, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0043, 'fitness_entropy': 0.9383574700386479, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.004)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 13.477)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754298140.085079, 'performance_metrics': {}}}
2025-08-04 17:02:20,292 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:02:20,292 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:02:20,292 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:02:20,292 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:02:20,293 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-04 17:02:20,293 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-04 17:02:20,293 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-04 17:02:20,293 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:02:20,293 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:02:20,294 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-04 17:02:20,294 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:02:20,294 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:02:20,294 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 3} (总数: 2, 保护比例: 0.20)
2025-08-04 17:02:20,294 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:02:20,294 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:02:20,294 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:20,295 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:02:20,295 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,295 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,296 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,296 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,296 - ExplorationExpert - INFO - 探索路径生成完成，成本: 782.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:02:20,296 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [2, 7, 6, 5, 3, 8, 4, 0, 1], 'cur_cost': 782.0, 'intermediate_solutions': [{'tour': [3, 5, 7, 8, 4, 1, 0, 2, 6], 'cur_cost': 880.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 4, 8, 7, 2, 0, 1, 6], 'cur_cost': 904.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 5, 7, 8, 2, 0, 1, 4, 6], 'cur_cost': 845.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:02:20,296 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 782.00)
2025-08-04 17:02:20,296 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-04 17:02:20,296 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:02:20,296 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:02:20,297 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 936.0
2025-08-04 17:02:20,357 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:02:20,357 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 17:02:20,357 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64)]
2025-08-04 17:02:20,358 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:02:20,358 - ExploitationExpert - INFO - populations: [{'tour': [2, 7, 6, 5, 3, 8, 4, 0, 1], 'cur_cost': 782.0}, {'tour': array([5, 3, 1, 7, 0, 6, 4, 2, 8], dtype=int64), 'cur_cost': 936.0}, {'tour': [7, 4, 2, 5, 6, 3, 0, 8, 1], 'cur_cost': 1054.0}, {'tour': [6, 7, 1, 2, 0, 4, 8, 3, 5], 'cur_cost': 861.0}, {'tour': [4, 6, 3, 7, 2, 8, 5, 0, 1], 'cur_cost': 916.0}, {'tour': [4, 6, 2, 5, 0, 7, 3, 8, 1], 'cur_cost': 1144.0}, {'tour': [3, 1, 6, 5, 2, 8, 7, 0, 4], 'cur_cost': 1003.0}, {'tour': [1, 7, 8, 5, 2, 6, 0, 3, 4], 'cur_cost': 1121.0}, {'tour': [7, 8, 5, 2, 3, 1, 0, 4, 6], 'cur_cost': 1030.0}, {'tour': [6, 8, 0, 1, 5, 3, 7, 4, 2], 'cur_cost': 946.0}]
2025-08-04 17:02:20,359 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒，最大迭代次数: 10
2025-08-04 17:02:20,359 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-08-04 17:02:20,360 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([5, 3, 1, 7, 0, 6, 4, 2, 8], dtype=int64), 'cur_cost': 936.0, 'intermediate_solutions': [{'tour': array([0, 5, 1, 3, 2, 7, 6, 8, 4]), 'cur_cost': 1139.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 0, 5, 1, 2, 7, 6, 8, 4]), 'cur_cost': 1123.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 3, 0, 5, 1, 7, 6, 8, 4]), 'cur_cost': 1063.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 3, 0, 5, 2, 7, 6, 8, 4]), 'cur_cost': 1143.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 2, 3, 0, 5, 7, 6, 8, 4]), 'cur_cost': 1033.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:02:20,360 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 936.00)
2025-08-04 17:02:20,360 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:02:20,360 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:02:20,360 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:20,361 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:02:20,361 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,361 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,361 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,361 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,361 - ExplorationExpert - INFO - 探索路径生成完成，成本: 863.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:02:20,361 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [2, 3, 5, 7, 4, 0, 1, 6, 8], 'cur_cost': 863.0, 'intermediate_solutions': [{'tour': [7, 4, 2, 0, 6, 3, 5, 8, 1], 'cur_cost': 935.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 4, 2, 5, 8, 0, 3, 6, 1], 'cur_cost': 1026.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 7, 4, 2, 5, 6, 3, 0, 1], 'cur_cost': 921.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:02:20,362 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 863.00)
2025-08-04 17:02:20,362 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:02:20,362 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:02:20,362 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:20,362 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:02:20,362 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,362 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,362 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,363 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,363 - ExplorationExpert - INFO - 探索路径生成完成，成本: 886.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:02:20,363 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 8, 2, 3, 7, 5, 6, 4, 1], 'cur_cost': 886.0, 'intermediate_solutions': [{'tour': [6, 7, 1, 8, 0, 4, 2, 3, 5], 'cur_cost': 940.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 1, 3, 8, 4, 0, 2, 5], 'cur_cost': 999.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 2, 7, 1, 0, 4, 8, 3, 5], 'cur_cost': 877.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:02:20,363 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 886.00)
2025-08-04 17:02:20,363 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:02:20,363 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:02:20,363 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:20,363 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:02:20,364 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,364 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,364 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,364 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,364 - ExplorationExpert - INFO - 探索路径生成完成，成本: 907.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:02:20,364 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [1, 2, 5, 4, 8, 3, 7, 6, 0], 'cur_cost': 907.0, 'intermediate_solutions': [{'tour': [8, 6, 3, 7, 2, 4, 5, 0, 1], 'cur_cost': 974.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 3, 6, 7, 2, 8, 5, 0, 1], 'cur_cost': 909.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 1, 6, 3, 7, 2, 8, 5, 0], 'cur_cost': 928.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:02:20,364 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 907.00)
2025-08-04 17:02:20,364 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-04 17:02:20,364 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:02:20,365 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:02:20,365 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 958.0
2025-08-04 17:02:20,425 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:02:20,425 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 17:02:20,425 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64)]
2025-08-04 17:02:20,426 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:02:20,426 - ExploitationExpert - INFO - populations: [{'tour': [2, 7, 6, 5, 3, 8, 4, 0, 1], 'cur_cost': 782.0}, {'tour': array([5, 3, 1, 7, 0, 6, 4, 2, 8], dtype=int64), 'cur_cost': 936.0}, {'tour': [2, 3, 5, 7, 4, 0, 1, 6, 8], 'cur_cost': 863.0}, {'tour': [0, 8, 2, 3, 7, 5, 6, 4, 1], 'cur_cost': 886.0}, {'tour': [1, 2, 5, 4, 8, 3, 7, 6, 0], 'cur_cost': 907.0}, {'tour': array([7, 6, 8, 5, 4, 2, 3, 1, 0], dtype=int64), 'cur_cost': 958.0}, {'tour': [3, 1, 6, 5, 2, 8, 7, 0, 4], 'cur_cost': 1003.0}, {'tour': [1, 7, 8, 5, 2, 6, 0, 3, 4], 'cur_cost': 1121.0}, {'tour': [7, 8, 5, 2, 3, 1, 0, 4, 6], 'cur_cost': 1030.0}, {'tour': [6, 8, 0, 1, 5, 3, 7, 4, 2], 'cur_cost': 946.0}]
2025-08-04 17:02:20,427 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒，最大迭代次数: 10
2025-08-04 17:02:20,427 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-08-04 17:02:20,428 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([7, 6, 8, 5, 4, 2, 3, 1, 0], dtype=int64), 'cur_cost': 958.0, 'intermediate_solutions': [{'tour': array([2, 6, 4, 5, 0, 7, 3, 8, 1]), 'cur_cost': 1144.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 2, 6, 4, 0, 7, 3, 8, 1]), 'cur_cost': 1160.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 5, 2, 6, 4, 7, 3, 8, 1]), 'cur_cost': 1090.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 5, 2, 6, 0, 7, 3, 8, 1]), 'cur_cost': 1106.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 0, 5, 2, 6, 7, 3, 8, 1]), 'cur_cost': 1077.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:02:20,428 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 958.00)
2025-08-04 17:02:20,428 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:02:20,428 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:02:20,428 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:20,428 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:02:20,429 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,429 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,429 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,429 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,429 - ExplorationExpert - INFO - 探索路径生成完成，成本: 998.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:02:20,429 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 2, 3, 6, 1, 5, 7, 8, 4], 'cur_cost': 998.0, 'intermediate_solutions': [{'tour': [7, 1, 6, 5, 2, 8, 3, 0, 4], 'cur_cost': 968.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 1, 6, 5, 0, 7, 8, 2, 4], 'cur_cost': 928.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 1, 6, 7, 5, 2, 8, 0, 4], 'cur_cost': 1052.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:02:20,429 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 998.00)
2025-08-04 17:02:20,430 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-04 17:02:20,430 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:02:20,430 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:02:20,430 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 1143.0
2025-08-04 17:02:20,492 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:02:20,492 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 17:02:20,492 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64)]
2025-08-04 17:02:20,493 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:02:20,493 - ExploitationExpert - INFO - populations: [{'tour': [2, 7, 6, 5, 3, 8, 4, 0, 1], 'cur_cost': 782.0}, {'tour': array([5, 3, 1, 7, 0, 6, 4, 2, 8], dtype=int64), 'cur_cost': 936.0}, {'tour': [2, 3, 5, 7, 4, 0, 1, 6, 8], 'cur_cost': 863.0}, {'tour': [0, 8, 2, 3, 7, 5, 6, 4, 1], 'cur_cost': 886.0}, {'tour': [1, 2, 5, 4, 8, 3, 7, 6, 0], 'cur_cost': 907.0}, {'tour': array([7, 6, 8, 5, 4, 2, 3, 1, 0], dtype=int64), 'cur_cost': 958.0}, {'tour': [0, 2, 3, 6, 1, 5, 7, 8, 4], 'cur_cost': 998.0}, {'tour': array([4, 5, 1, 6, 7, 2, 3, 0, 8], dtype=int64), 'cur_cost': 1143.0}, {'tour': [7, 8, 5, 2, 3, 1, 0, 4, 6], 'cur_cost': 1030.0}, {'tour': [6, 8, 0, 1, 5, 3, 7, 4, 2], 'cur_cost': 946.0}]
2025-08-04 17:02:20,494 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒，最大迭代次数: 10
2025-08-04 17:02:20,494 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-08-04 17:02:20,495 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([4, 5, 1, 6, 7, 2, 3, 0, 8], dtype=int64), 'cur_cost': 1143.0, 'intermediate_solutions': [{'tour': array([8, 7, 1, 5, 2, 6, 0, 3, 4]), 'cur_cost': 1176.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 8, 7, 1, 2, 6, 0, 3, 4]), 'cur_cost': 1121.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 5, 8, 7, 1, 6, 0, 3, 4]), 'cur_cost': 999.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 5, 8, 7, 2, 6, 0, 3, 4]), 'cur_cost': 1119.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 2, 5, 8, 7, 6, 0, 3, 4]), 'cur_cost': 1026.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:02:20,495 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1143.00)
2025-08-04 17:02:20,495 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:02:20,496 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:02:20,496 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:20,496 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:02:20,496 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,497 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,497 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,497 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,497 - ExplorationExpert - INFO - 探索路径生成完成，成本: 847.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:02:20,497 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [3, 8, 7, 6, 5, 4, 2, 0, 1], 'cur_cost': 847.0, 'intermediate_solutions': [{'tour': [7, 6, 5, 2, 3, 1, 0, 4, 8], 'cur_cost': 947.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 8, 5, 2, 3, 1, 4, 0, 6], 'cur_cost': 1042.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 8, 3, 5, 2, 1, 0, 4, 6], 'cur_cost': 895.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:02:20,497 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 847.00)
2025-08-04 17:02:20,497 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:02:20,498 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:02:20,498 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:20,498 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:02:20,498 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,498 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,499 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,499 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,499 - ExplorationExpert - INFO - 探索路径生成完成，成本: 945.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:02:20,499 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [1, 4, 3, 6, 2, 8, 7, 5, 0], 'cur_cost': 945.0, 'intermediate_solutions': [{'tour': [0, 8, 6, 1, 5, 3, 7, 4, 2], 'cur_cost': 960.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 8, 0, 1, 5, 4, 7, 3, 2], 'cur_cost': 1120.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 8, 0, 4, 1, 5, 3, 7, 2], 'cur_cost': 1076.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:02:20,499 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 945.00)
2025-08-04 17:02:20,499 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:02:20,499 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:02:20,500 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [2, 7, 6, 5, 3, 8, 4, 0, 1], 'cur_cost': 782.0, 'intermediate_solutions': [{'tour': [3, 5, 7, 8, 4, 1, 0, 2, 6], 'cur_cost': 880.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 4, 8, 7, 2, 0, 1, 6], 'cur_cost': 904.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 5, 7, 8, 2, 0, 1, 4, 6], 'cur_cost': 845.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([5, 3, 1, 7, 0, 6, 4, 2, 8], dtype=int64), 'cur_cost': 936.0, 'intermediate_solutions': [{'tour': array([0, 5, 1, 3, 2, 7, 6, 8, 4]), 'cur_cost': 1139.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 0, 5, 1, 2, 7, 6, 8, 4]), 'cur_cost': 1123.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 3, 0, 5, 1, 7, 6, 8, 4]), 'cur_cost': 1063.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 3, 0, 5, 2, 7, 6, 8, 4]), 'cur_cost': 1143.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 2, 3, 0, 5, 7, 6, 8, 4]), 'cur_cost': 1033.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [2, 3, 5, 7, 4, 0, 1, 6, 8], 'cur_cost': 863.0, 'intermediate_solutions': [{'tour': [7, 4, 2, 0, 6, 3, 5, 8, 1], 'cur_cost': 935.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 4, 2, 5, 8, 0, 3, 6, 1], 'cur_cost': 1026.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 7, 4, 2, 5, 6, 3, 0, 1], 'cur_cost': 921.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 2, 3, 7, 5, 6, 4, 1], 'cur_cost': 886.0, 'intermediate_solutions': [{'tour': [6, 7, 1, 8, 0, 4, 2, 3, 5], 'cur_cost': 940.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 1, 3, 8, 4, 0, 2, 5], 'cur_cost': 999.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 2, 7, 1, 0, 4, 8, 3, 5], 'cur_cost': 877.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [1, 2, 5, 4, 8, 3, 7, 6, 0], 'cur_cost': 907.0, 'intermediate_solutions': [{'tour': [8, 6, 3, 7, 2, 4, 5, 0, 1], 'cur_cost': 974.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 3, 6, 7, 2, 8, 5, 0, 1], 'cur_cost': 909.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 1, 6, 3, 7, 2, 8, 5, 0], 'cur_cost': 928.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 6, 8, 5, 4, 2, 3, 1, 0], dtype=int64), 'cur_cost': 958.0, 'intermediate_solutions': [{'tour': array([2, 6, 4, 5, 0, 7, 3, 8, 1]), 'cur_cost': 1144.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 2, 6, 4, 0, 7, 3, 8, 1]), 'cur_cost': 1160.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 5, 2, 6, 4, 7, 3, 8, 1]), 'cur_cost': 1090.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 5, 2, 6, 0, 7, 3, 8, 1]), 'cur_cost': 1106.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 0, 5, 2, 6, 7, 3, 8, 1]), 'cur_cost': 1077.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 3, 6, 1, 5, 7, 8, 4], 'cur_cost': 998.0, 'intermediate_solutions': [{'tour': [7, 1, 6, 5, 2, 8, 3, 0, 4], 'cur_cost': 968.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 1, 6, 5, 0, 7, 8, 2, 4], 'cur_cost': 928.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 1, 6, 7, 5, 2, 8, 0, 4], 'cur_cost': 1052.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 5, 1, 6, 7, 2, 3, 0, 8], dtype=int64), 'cur_cost': 1143.0, 'intermediate_solutions': [{'tour': array([8, 7, 1, 5, 2, 6, 0, 3, 4]), 'cur_cost': 1176.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 8, 7, 1, 2, 6, 0, 3, 4]), 'cur_cost': 1121.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 5, 8, 7, 1, 6, 0, 3, 4]), 'cur_cost': 999.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 5, 8, 7, 2, 6, 0, 3, 4]), 'cur_cost': 1119.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 2, 5, 8, 7, 6, 0, 3, 4]), 'cur_cost': 1026.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [3, 8, 7, 6, 5, 4, 2, 0, 1], 'cur_cost': 847.0, 'intermediate_solutions': [{'tour': [7, 6, 5, 2, 3, 1, 0, 4, 8], 'cur_cost': 947.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 8, 5, 2, 3, 1, 4, 0, 6], 'cur_cost': 1042.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 8, 3, 5, 2, 1, 0, 4, 6], 'cur_cost': 895.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 3, 6, 2, 8, 7, 5, 0], 'cur_cost': 945.0, 'intermediate_solutions': [{'tour': [0, 8, 6, 1, 5, 3, 7, 4, 2], 'cur_cost': 960.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 8, 0, 1, 5, 4, 7, 3, 2], 'cur_cost': 1120.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 8, 0, 4, 1, 5, 3, 7, 2], 'cur_cost': 1076.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:02:20,501 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:02:20,501 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:02:20,502 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=782.000, 多样性=0.901
2025-08-04 17:02:20,502 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-04 17:02:20,502 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-04 17:02:20,502 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:02:20,503 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.012778914571197125, 'best_improvement': -0.0316622691292876}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.02240896358543425}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.038258837933225084, 'recent_improvements': [-0.026154746963719593, 0.05361251396407238, -0.10267242283016977], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:02:20,503 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-04 17:02:20,503 - __main__ - INFO - simple1_9 开始进化第 5 代
2025-08-04 17:02:20,503 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-04 17:02:20,503 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:02:20,504 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=782.000, 多样性=0.901
2025-08-04 17:02:20,504 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:02:20,505 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.901
2025-08-04 17:02:20,505 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:02:20,505 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.630
2025-08-04 17:02:20,507 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-04 17:02:20,507 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:02:20,508 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-04 17:02:20,508 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-04 17:02:20,515 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.231, 适应度梯度: -7.462, 聚类评分: 0.000, 覆盖率: 0.005, 收敛趋势: 0.000, 多样性: 0.603
2025-08-04 17:02:20,516 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-04 17:02:20,516 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:02:20,516 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:02:20,521 - visualization.landscape_visualizer - INFO - 插值约束: 42 个点被约束到最小值 680.00
2025-08-04 17:02:20,526 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:02:20,607 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_5_20250804_170220.html
2025-08-04 17:02:20,676 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_5_20250804_170220.html
2025-08-04 17:02:20,676 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 5
2025-08-04 17:02:20,676 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-04 17:02:20,676 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1690秒
2025-08-04 17:02:20,676 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.23076923076923078, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -7.461538461538457, 'local_optima_density': 0.23076923076923078, 'gradient_variance': 9470.999289940828, 'cluster_count': 0}, 'population_state': {'diversity': 0.6025641025641025, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0053, 'fitness_entropy': 0.9686322539060781, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -7.462)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.005)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754298140.5160499, 'performance_metrics': {}}}
2025-08-04 17:02:20,677 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:02:20,677 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:02:20,677 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:02:20,678 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:02:20,678 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore']
2025-08-04 17:02:20,678 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-04 17:02:20,678 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore']
2025-08-04 17:02:20,678 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:02:20,679 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:02:20,679 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore']
2025-08-04 17:02:20,679 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:02:20,679 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:02:20,680 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 8} (总数: 2, 保护比例: 0.20)
2025-08-04 17:02:20,680 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:02:20,680 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:02:20,680 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:20,681 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:02:20,681 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,681 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,681 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,682 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,682 - ExplorationExpert - INFO - 探索路径生成完成，成本: 935.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:02:20,682 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [1, 7, 8, 2, 5, 3, 6, 0, 4], 'cur_cost': 935.0, 'intermediate_solutions': [{'tour': [2, 7, 6, 3, 5, 8, 4, 0, 1], 'cur_cost': 833.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 7, 6, 5, 3, 8, 1, 0, 4], 'cur_cost': 780.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 6, 5, 3, 8, 4, 0, 2, 1], 'cur_cost': 861.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:02:20,683 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 935.00)
2025-08-04 17:02:20,683 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:02:20,683 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:02:20,683 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:20,684 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:02:20,684 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,684 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,684 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,684 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,685 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1159.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:02:20,685 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [3, 4, 1, 8, 0, 5, 7, 6, 2], 'cur_cost': 1159.0, 'intermediate_solutions': [{'tour': [5, 0, 1, 7, 3, 6, 4, 2, 8], 'cur_cost': 859.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 3, 1, 7, 0, 8, 2, 4, 6], 'cur_cost': 947.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 3, 7, 0, 6, 1, 4, 2, 8], 'cur_cost': 783.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:02:20,685 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1159.00)
2025-08-04 17:02:20,685 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:02:20,685 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:02:20,685 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:20,686 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:02:20,686 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,687 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,687 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,687 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,687 - ExplorationExpert - INFO - 探索路径生成完成，成本: 987.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:02:20,687 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [7, 6, 2, 8, 0, 4, 1, 5, 3], 'cur_cost': 987.0, 'intermediate_solutions': [{'tour': [2, 3, 0, 7, 4, 5, 1, 6, 8], 'cur_cost': 1118.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 3, 5, 7, 4, 0, 6, 1, 8], 'cur_cost': 953.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 5, 7, 4, 2, 0, 1, 6, 8], 'cur_cost': 789.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:02:20,688 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 987.00)
2025-08-04 17:02:20,688 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:02:20,688 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:02:20,688 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:20,688 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:02:20,689 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,689 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,689 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,689 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,689 - ExplorationExpert - INFO - 探索路径生成完成，成本: 937.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:02:20,690 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 3, 7, 6, 8, 4, 2, 5, 1], 'cur_cost': 937.0, 'intermediate_solutions': [{'tour': [0, 6, 2, 3, 7, 5, 8, 4, 1], 'cur_cost': 910.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 2, 3, 7, 5, 1, 4, 6], 'cur_cost': 1036.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 2, 3, 7, 1, 5, 6, 4], 'cur_cost': 1041.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:02:20,690 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 937.00)
2025-08-04 17:02:20,690 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:02:20,690 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:02:20,690 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:20,690 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:02:20,691 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,691 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,691 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,691 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,692 - ExplorationExpert - INFO - 探索路径生成完成，成本: 975.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:02:20,692 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [4, 6, 0, 7, 1, 2, 8, 3, 5], 'cur_cost': 975.0, 'intermediate_solutions': [{'tour': [1, 2, 5, 4, 3, 8, 7, 6, 0], 'cur_cost': 959.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 2, 5, 4, 8, 3, 6, 7, 0], 'cur_cost': 934.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 2, 5, 4, 8, 3, 7, 6, 0], 'cur_cost': 907.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:02:20,692 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 975.00)
2025-08-04 17:02:20,692 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-04 17:02:20,692 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:02:20,693 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:02:20,693 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 895.0
2025-08-04 17:02:20,776 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:02:20,776 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 17:02:20,776 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64)]
2025-08-04 17:02:20,777 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:02:20,777 - ExploitationExpert - INFO - populations: [{'tour': [1, 7, 8, 2, 5, 3, 6, 0, 4], 'cur_cost': 935.0}, {'tour': [3, 4, 1, 8, 0, 5, 7, 6, 2], 'cur_cost': 1159.0}, {'tour': [7, 6, 2, 8, 0, 4, 1, 5, 3], 'cur_cost': 987.0}, {'tour': [0, 3, 7, 6, 8, 4, 2, 5, 1], 'cur_cost': 937.0}, {'tour': [4, 6, 0, 7, 1, 2, 8, 3, 5], 'cur_cost': 975.0}, {'tour': array([8, 6, 5, 7, 3, 0, 4, 1, 2], dtype=int64), 'cur_cost': 895.0}, {'tour': [0, 2, 3, 6, 1, 5, 7, 8, 4], 'cur_cost': 998.0}, {'tour': [4, 5, 1, 6, 7, 2, 3, 0, 8], 'cur_cost': 1143.0}, {'tour': [3, 8, 7, 6, 5, 4, 2, 0, 1], 'cur_cost': 847.0}, {'tour': [1, 4, 3, 6, 2, 8, 7, 5, 0], 'cur_cost': 945.0}]
2025-08-04 17:02:20,777 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒，最大迭代次数: 10
2025-08-04 17:02:20,778 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-08-04 17:02:20,778 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([8, 6, 5, 7, 3, 0, 4, 1, 2], dtype=int64), 'cur_cost': 895.0, 'intermediate_solutions': [{'tour': array([8, 6, 7, 5, 4, 2, 3, 1, 0]), 'cur_cost': 983.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 8, 6, 7, 4, 2, 3, 1, 0]), 'cur_cost': 954.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 5, 8, 6, 7, 2, 3, 1, 0]), 'cur_cost': 1034.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 5, 8, 6, 4, 2, 3, 1, 0]), 'cur_cost': 955.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 4, 5, 8, 6, 2, 3, 1, 0]), 'cur_cost': 1105.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:02:20,778 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 895.00)
2025-08-04 17:02:20,778 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-04 17:02:20,778 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:02:20,779 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:02:20,779 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 961.0
2025-08-04 17:02:20,848 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:02:20,849 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 17:02:20,849 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64)]
2025-08-04 17:02:20,850 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:02:20,850 - ExploitationExpert - INFO - populations: [{'tour': [1, 7, 8, 2, 5, 3, 6, 0, 4], 'cur_cost': 935.0}, {'tour': [3, 4, 1, 8, 0, 5, 7, 6, 2], 'cur_cost': 1159.0}, {'tour': [7, 6, 2, 8, 0, 4, 1, 5, 3], 'cur_cost': 987.0}, {'tour': [0, 3, 7, 6, 8, 4, 2, 5, 1], 'cur_cost': 937.0}, {'tour': [4, 6, 0, 7, 1, 2, 8, 3, 5], 'cur_cost': 975.0}, {'tour': array([8, 6, 5, 7, 3, 0, 4, 1, 2], dtype=int64), 'cur_cost': 895.0}, {'tour': array([1, 0, 7, 2, 8, 5, 6, 4, 3], dtype=int64), 'cur_cost': 961.0}, {'tour': [4, 5, 1, 6, 7, 2, 3, 0, 8], 'cur_cost': 1143.0}, {'tour': [3, 8, 7, 6, 5, 4, 2, 0, 1], 'cur_cost': 847.0}, {'tour': [1, 4, 3, 6, 2, 8, 7, 5, 0], 'cur_cost': 945.0}]
2025-08-04 17:02:20,851 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:02:20,851 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-08-04 17:02:20,852 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([1, 0, 7, 2, 8, 5, 6, 4, 3], dtype=int64), 'cur_cost': 961.0, 'intermediate_solutions': [{'tour': array([3, 2, 0, 6, 1, 5, 7, 8, 4]), 'cur_cost': 1031.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 3, 2, 0, 1, 5, 7, 8, 4]), 'cur_cost': 986.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 6, 3, 2, 0, 5, 7, 8, 4]), 'cur_cost': 982.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 6, 3, 2, 1, 5, 7, 8, 4]), 'cur_cost': 987.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 1, 6, 3, 2, 5, 7, 8, 4]), 'cur_cost': 921.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:02:20,852 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 961.00)
2025-08-04 17:02:20,852 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-04 17:02:20,852 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:02:20,852 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:02:20,853 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 1004.0
2025-08-04 17:02:20,923 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:02:20,923 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 17:02:20,923 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64)]
2025-08-04 17:02:20,924 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:02:20,924 - ExploitationExpert - INFO - populations: [{'tour': [1, 7, 8, 2, 5, 3, 6, 0, 4], 'cur_cost': 935.0}, {'tour': [3, 4, 1, 8, 0, 5, 7, 6, 2], 'cur_cost': 1159.0}, {'tour': [7, 6, 2, 8, 0, 4, 1, 5, 3], 'cur_cost': 987.0}, {'tour': [0, 3, 7, 6, 8, 4, 2, 5, 1], 'cur_cost': 937.0}, {'tour': [4, 6, 0, 7, 1, 2, 8, 3, 5], 'cur_cost': 975.0}, {'tour': array([8, 6, 5, 7, 3, 0, 4, 1, 2], dtype=int64), 'cur_cost': 895.0}, {'tour': array([1, 0, 7, 2, 8, 5, 6, 4, 3], dtype=int64), 'cur_cost': 961.0}, {'tour': array([1, 8, 3, 6, 2, 4, 0, 7, 5], dtype=int64), 'cur_cost': 1004.0}, {'tour': [3, 8, 7, 6, 5, 4, 2, 0, 1], 'cur_cost': 847.0}, {'tour': [1, 4, 3, 6, 2, 8, 7, 5, 0], 'cur_cost': 945.0}]
2025-08-04 17:02:20,925 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:02:20,926 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-08-04 17:02:20,926 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([1, 8, 3, 6, 2, 4, 0, 7, 5], dtype=int64), 'cur_cost': 1004.0, 'intermediate_solutions': [{'tour': array([1, 5, 4, 6, 7, 2, 3, 0, 8]), 'cur_cost': 1254.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 1, 5, 4, 7, 2, 3, 0, 8]), 'cur_cost': 1201.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 6, 1, 5, 4, 2, 3, 0, 8]), 'cur_cost': 1059.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 6, 1, 5, 7, 2, 3, 0, 8]), 'cur_cost': 1140.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 7, 6, 1, 5, 2, 3, 0, 8]), 'cur_cost': 1138.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:02:20,927 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1004.00)
2025-08-04 17:02:20,927 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:02:20,927 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:02:20,927 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:20,927 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:02:20,927 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,928 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,928 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,928 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,928 - ExplorationExpert - INFO - 探索路径生成完成，成本: 876.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:02:20,928 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [7, 3, 8, 2, 1, 6, 5, 4, 0], 'cur_cost': 876.0, 'intermediate_solutions': [{'tour': [3, 6, 7, 8, 5, 4, 2, 0, 1], 'cur_cost': 898.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 0, 2, 4, 5, 6, 7, 1], 'cur_cost': 980.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 3, 8, 7, 5, 4, 2, 0, 1], 'cur_cost': 821.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:02:20,928 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 876.00)
2025-08-04 17:02:20,928 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:02:20,928 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:02:20,929 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:02:20,929 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:02:20,929 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,929 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,929 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,929 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:02:20,929 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1059.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:02:20,930 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [4, 7, 1, 6, 2, 3, 5, 8, 0], 'cur_cost': 1059.0, 'intermediate_solutions': [{'tour': [1, 4, 3, 8, 2, 6, 7, 5, 0], 'cur_cost': 918.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 8, 2, 6, 3, 7, 5, 0], 'cur_cost': 893.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 3, 4, 6, 2, 8, 7, 5, 0], 'cur_cost': 1052.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:02:20,930 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1059.00)
2025-08-04 17:02:20,930 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:02:20,930 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:02:20,931 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 8, 2, 5, 3, 6, 0, 4], 'cur_cost': 935.0, 'intermediate_solutions': [{'tour': [2, 7, 6, 3, 5, 8, 4, 0, 1], 'cur_cost': 833.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 7, 6, 5, 3, 8, 1, 0, 4], 'cur_cost': 780.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 6, 5, 3, 8, 4, 0, 2, 1], 'cur_cost': 861.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [3, 4, 1, 8, 0, 5, 7, 6, 2], 'cur_cost': 1159.0, 'intermediate_solutions': [{'tour': [5, 0, 1, 7, 3, 6, 4, 2, 8], 'cur_cost': 859.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 3, 1, 7, 0, 8, 2, 4, 6], 'cur_cost': 947.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 3, 7, 0, 6, 1, 4, 2, 8], 'cur_cost': 783.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [7, 6, 2, 8, 0, 4, 1, 5, 3], 'cur_cost': 987.0, 'intermediate_solutions': [{'tour': [2, 3, 0, 7, 4, 5, 1, 6, 8], 'cur_cost': 1118.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 3, 5, 7, 4, 0, 6, 1, 8], 'cur_cost': 953.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 5, 7, 4, 2, 0, 1, 6, 8], 'cur_cost': 789.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 7, 6, 8, 4, 2, 5, 1], 'cur_cost': 937.0, 'intermediate_solutions': [{'tour': [0, 6, 2, 3, 7, 5, 8, 4, 1], 'cur_cost': 910.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 2, 3, 7, 5, 1, 4, 6], 'cur_cost': 1036.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 2, 3, 7, 1, 5, 6, 4], 'cur_cost': 1041.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [4, 6, 0, 7, 1, 2, 8, 3, 5], 'cur_cost': 975.0, 'intermediate_solutions': [{'tour': [1, 2, 5, 4, 3, 8, 7, 6, 0], 'cur_cost': 959.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 2, 5, 4, 8, 3, 6, 7, 0], 'cur_cost': 934.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 2, 5, 4, 8, 3, 7, 6, 0], 'cur_cost': 907.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 6, 5, 7, 3, 0, 4, 1, 2], dtype=int64), 'cur_cost': 895.0, 'intermediate_solutions': [{'tour': array([8, 6, 7, 5, 4, 2, 3, 1, 0]), 'cur_cost': 983.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 8, 6, 7, 4, 2, 3, 1, 0]), 'cur_cost': 954.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 5, 8, 6, 7, 2, 3, 1, 0]), 'cur_cost': 1034.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 5, 8, 6, 4, 2, 3, 1, 0]), 'cur_cost': 955.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 4, 5, 8, 6, 2, 3, 1, 0]), 'cur_cost': 1105.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([1, 0, 7, 2, 8, 5, 6, 4, 3], dtype=int64), 'cur_cost': 961.0, 'intermediate_solutions': [{'tour': array([3, 2, 0, 6, 1, 5, 7, 8, 4]), 'cur_cost': 1031.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 3, 2, 0, 1, 5, 7, 8, 4]), 'cur_cost': 986.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 6, 3, 2, 0, 5, 7, 8, 4]), 'cur_cost': 982.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 6, 3, 2, 1, 5, 7, 8, 4]), 'cur_cost': 987.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 1, 6, 3, 2, 5, 7, 8, 4]), 'cur_cost': 921.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([1, 8, 3, 6, 2, 4, 0, 7, 5], dtype=int64), 'cur_cost': 1004.0, 'intermediate_solutions': [{'tour': array([1, 5, 4, 6, 7, 2, 3, 0, 8]), 'cur_cost': 1254.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 1, 5, 4, 7, 2, 3, 0, 8]), 'cur_cost': 1201.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 6, 1, 5, 4, 2, 3, 0, 8]), 'cur_cost': 1059.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 6, 1, 5, 7, 2, 3, 0, 8]), 'cur_cost': 1140.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 7, 6, 1, 5, 2, 3, 0, 8]), 'cur_cost': 1138.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [7, 3, 8, 2, 1, 6, 5, 4, 0], 'cur_cost': 876.0, 'intermediate_solutions': [{'tour': [3, 6, 7, 8, 5, 4, 2, 0, 1], 'cur_cost': 898.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 0, 2, 4, 5, 6, 7, 1], 'cur_cost': 980.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 3, 8, 7, 5, 4, 2, 0, 1], 'cur_cost': 821.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [4, 7, 1, 6, 2, 3, 5, 8, 0], 'cur_cost': 1059.0, 'intermediate_solutions': [{'tour': [1, 4, 3, 8, 2, 6, 7, 5, 0], 'cur_cost': 918.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 8, 2, 6, 3, 7, 5, 0], 'cur_cost': 893.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 3, 4, 6, 2, 8, 7, 5, 0], 'cur_cost': 1052.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:02:20,931 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:02:20,932 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:02:20,933 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=876.000, 多样性=0.906
2025-08-04 17:02:20,933 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-04 17:02:20,933 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-04 17:02:20,933 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:02:20,933 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.0635506184727605, 'best_improvement': -0.12020460358056266}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.005479452054794323}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.020416799696437626, 'recent_improvements': [0.05361251396407238, -0.10267242283016977, 0.012778914571197125], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:02:20,934 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-04 17:02:20,935 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_solution.json
2025-08-04 17:02:20,935 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_20250804_170220.solution
2025-08-04 17:02:20,947 - __main__ - INFO - 评估统计 - 总次数: 252302.99999977706, 运行时间: 8.99s, 最佳成本: 680.0
2025-08-04 17:02:20,948 - __main__ - INFO - 实例 simple1_9 处理完成
