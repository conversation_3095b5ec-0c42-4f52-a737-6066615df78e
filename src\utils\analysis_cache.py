# -*- coding: utf-8 -*-
"""
分析缓存系统

提供分析结果缓存功能，避免重复计算，提升性能。
"""

import hashlib
import json
import logging
from typing import Any, Dict, List, Optional, Tuple
from functools import wraps
import time


class AnalysisCache:
    """分析结果缓存管理器"""
    
    def __init__(self, max_size: int = 1000, ttl: int = 3600):
        """
        初始化缓存管理器
        
        参数:
            max_size: 最大缓存条目数
            ttl: 缓存生存时间（秒）
        """
        self.cache = {}
        self.access_times = {}
        self.creation_times = {}
        self.max_size = max_size
        self.ttl = ttl
        self.logger = logging.getLogger(__name__)
        
        # 统计信息
        self.hits = 0
        self.misses = 0
        self.evictions = 0
    
    def _generate_key(self, data: Any, prefix: str = "") -> str:
        """
        生成缓存键
        
        参数:
            data: 要缓存的数据
            prefix: 键前缀
            
        返回:
            str: 缓存键
        """
        try:
            # 对于复杂数据结构，生成哈希值
            if isinstance(data, (list, dict)):
                data_str = json.dumps(data, sort_keys=True, default=str)
            else:
                data_str = str(data)
            
            hash_obj = hashlib.md5(data_str.encode())
            return f"{prefix}_{hash_obj.hexdigest()}"
        
        except Exception as e:
            self.logger.warning(f"生成缓存键时出错: {e}")
            return f"{prefix}_{hash(str(data))}"
    
    def get(self, key: str) -> Optional[Any]:
        """
        获取缓存值
        
        参数:
            key: 缓存键
            
        返回:
            缓存值或None
        """
        current_time = time.time()
        
        if key in self.cache:
            # 检查是否过期
            if current_time - self.creation_times[key] > self.ttl:
                self._remove(key)
                self.misses += 1
                return None
            
            # 更新访问时间
            self.access_times[key] = current_time
            self.hits += 1
            return self.cache[key]
        
        self.misses += 1
        return None
    
    def put(self, key: str, value: Any) -> None:
        """
        存储缓存值
        
        参数:
            key: 缓存键
            value: 缓存值
        """
        current_time = time.time()
        
        # 如果缓存已满，执行LRU淘汰
        if len(self.cache) >= self.max_size and key not in self.cache:
            self._evict_lru()
        
        self.cache[key] = value
        self.access_times[key] = current_time
        self.creation_times[key] = current_time
    
    def _remove(self, key: str) -> None:
        """移除缓存项"""
        if key in self.cache:
            del self.cache[key]
            del self.access_times[key]
            del self.creation_times[key]
    
    def _evict_lru(self) -> None:
        """淘汰最近最少使用的缓存项"""
        if not self.access_times:
            return
        
        # 找到最少使用的键
        lru_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        self._remove(lru_key)
        self.evictions += 1
    
    def clear(self) -> None:
        """清空缓存"""
        self.cache.clear()
        self.access_times.clear()
        self.creation_times.clear()
        self.hits = 0
        self.misses = 0
        self.evictions = 0
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_requests = self.hits + self.misses
        hit_rate = self.hits / total_requests if total_requests > 0 else 0
        
        return {
            "size": len(self.cache),
            "max_size": self.max_size,
            "hits": self.hits,
            "misses": self.misses,
            "hit_rate": hit_rate,
            "evictions": self.evictions,
            "ttl": self.ttl
        }


# 全局缓存实例
_global_cache = AnalysisCache()


def cached_analysis(cache_prefix: str = "", ttl: Optional[int] = None):
    """
    分析函数缓存装饰器
    
    参数:
        cache_prefix: 缓存键前缀
        ttl: 缓存生存时间（覆盖全局设置）
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key_data = {
                "func_name": func.__name__,
                "args": args,
                "kwargs": kwargs
            }
            cache_key = _global_cache._generate_key(cache_key_data, cache_prefix)
            
            # 尝试从缓存获取
            cached_result = _global_cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            _global_cache.put(cache_key, result)
            
            return result
        
        return wrapper
    return decorator


class PopulationAnalysisCache:
    """种群分析专用缓存"""
    
    def __init__(self):
        self.cache = AnalysisCache(max_size=500, ttl=1800)  # 30分钟TTL
        self.logger = logging.getLogger(__name__)
    
    def get_population_stats(self, populations: List[Dict]) -> Optional[Dict]:
        """获取种群统计缓存"""
        key = self._generate_population_key(populations, "stats")
        return self.cache.get(key)
    
    def cache_population_stats(self, populations: List[Dict], stats: Dict) -> None:
        """缓存种群统计"""
        key = self._generate_population_key(populations, "stats")
        self.cache.put(key, stats)
    
    def get_diversity_analysis(self, populations: List[Dict]) -> Optional[Dict]:
        """获取多样性分析缓存"""
        key = self._generate_population_key(populations, "diversity")
        return self.cache.get(key)
    
    def cache_diversity_analysis(self, populations: List[Dict], diversity: Dict) -> None:
        """缓存多样性分析"""
        key = self._generate_population_key(populations, "diversity")
        self.cache.put(key, diversity)
    
    def get_path_analysis(self, populations: List[Dict]) -> Optional[Dict]:
        """获取路径分析缓存"""
        key = self._generate_population_key(populations, "path")
        return self.cache.get(key)
    
    def cache_path_analysis(self, populations: List[Dict], path_analysis: Dict) -> None:
        """缓存路径分析"""
        key = self._generate_population_key(populations, "path")
        self.cache.put(key, path_analysis)
    
    def _generate_population_key(self, populations: List[Dict], analysis_type: str) -> str:
        """生成种群相关的缓存键"""
        try:
            # 提取关键信息生成键
            key_data = {
                "type": analysis_type,
                "size": len(populations),
                "tours": [p.get("tour", []) for p in populations if "tour" in p],
                "costs": [p.get("cur_cost", 0) for p in populations if "cur_cost" in p]
            }
            
            return self.cache._generate_key(key_data, f"pop_{analysis_type}")
        
        except Exception as e:
            self.logger.warning(f"生成种群缓存键时出错: {e}")
            return f"pop_{analysis_type}_{hash(str(populations))}"
    
    def clear(self) -> None:
        """清空缓存"""
        self.cache.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        return self.cache.get_stats()


class DistanceMatrixCache:
    """距离矩阵计算缓存"""
    
    def __init__(self):
        self.cache = AnalysisCache(max_size=100, ttl=7200)  # 2小时TTL
        self.logger = logging.getLogger(__name__)
    
    def get_pairwise_distances(self, tours: List[List[int]], method: str) -> Optional[Any]:
        """获取成对距离矩阵缓存"""
        key = self._generate_distance_key(tours, method)
        return self.cache.get(key)
    
    def cache_pairwise_distances(self, tours: List[List[int]], method: str, distances: Any) -> None:
        """缓存成对距离矩阵"""
        key = self._generate_distance_key(tours, method)
        self.cache.put(key, distances)
    
    def _generate_distance_key(self, tours: List[List[int]], method: str) -> str:
        """生成距离计算缓存键"""
        try:
            key_data = {
                "method": method,
                "tours": tours,
                "size": len(tours)
            }
            
            return self.cache._generate_key(key_data, f"dist_{method}")
        
        except Exception as e:
            self.logger.warning(f"生成距离缓存键时出错: {e}")
            return f"dist_{method}_{hash(str(tours))}"
    
    def clear(self) -> None:
        """清空缓存"""
        self.cache.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        return self.cache.get_stats()


# 全局缓存实例
population_cache = PopulationAnalysisCache()
distance_cache = DistanceMatrixCache()


def get_cache_manager() -> AnalysisCache:
    """获取全局缓存管理器"""
    return _global_cache


def clear_all_caches() -> None:
    """清空所有缓存"""
    _global_cache.clear()
    population_cache.clear()
    distance_cache.clear()


def get_all_cache_stats() -> Dict[str, Dict[str, Any]]:
    """获取所有缓存的统计信息"""
    return {
        "global_cache": _global_cache.get_stats(),
        "population_cache": population_cache.get_stats(),
        "distance_cache": distance_cache.get_stats()
    }
