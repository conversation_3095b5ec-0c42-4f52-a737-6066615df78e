2025-08-03 16:14:44,841 - main - INFO - composite13_66 开始进化第 1 代
2025-08-03 16:14:44,842 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-03 16:14:44,844 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:14:44,859 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=9948.000, 多样性=0.958
2025-08-03 16:14:44,864 - PathExpert - INFO - 开始路径结构分析
2025-08-03 16:14:44,872 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.958
2025-08-03 16:14:44,921 - EliteExpert - INFO - 开始精英解分析
2025-08-03 16:14:44,924 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/2)
2025-08-03 16:14:44,924 - LandscapeExpert - INFO - 使用直接传入的种群数据: 20个个体
2025-08-03 16:14:44,924 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-03 16:14:44,925 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-03 16:14:45,192 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.150, 适应度梯度: -6607.420, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 1.000
2025-08-03 16:14:45,192 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-03 16:14:45,193 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-03 16:14:45,276 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记
2025-08-03 16:14:45,612 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_1_20250803_161445.html
2025-08-03 16:14:45,662 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_1_20250803_161445.html
2025-08-03 16:14:45,662 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-03 16:14:45,662 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-03 16:14:45,663 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.7392秒
2025-08-03 16:14:45,663 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-03 16:14:45,663 - main - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.15, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -6607.420000000001, 'local_optima_density': 0.15, 'gradient_variance': 2018577189.9796002, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0019, 'fitness_entropy': 0.9763620978123273, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -6607.420)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 2, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754208885.1927862, 'performance_metrics': {}}}
2025-08-03 16:14:45,665 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-03 16:14:45,665 - experts.strategy.enhanced_strategy_expert - INFO - 开始增强策略分配分析 (迭代 0)
2025-08-03 16:14:45,666 - experts.analysis.individual_state_analyzer - INFO - 开始分析种群状态 (迭代 0, 种群大小: 20)
2025-08-03 16:14:45,666 - experts.strategy.enhanced_strategy_expert - ERROR - 策略分配过程中发生错误: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:14:45,667 - experts.strategy.enhanced_strategy_expert - WARNING - 使用回退策略分配
2025-08-03 16:14:45,667 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 16:14:45,668 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:14:45,668 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-03 16:14:45,669 - main - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 16:14:45,669 - main - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:14:45,669 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-03 16:14:45,670 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1, 3, 5} (总数: 4, 保护比例: 0.20)
2025-08-03 16:14:45,670 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-03 16:14:45,670 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-03 16:14:45,670 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:14:45,675 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:14:45,675 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:14:45,891 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12905.0, 路径长度: 66
2025-08-03 16:14:45,891 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 2, 16, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 14, 9, 11, 7, 3, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12905.0}
2025-08-03 16:14:45,892 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 12905.00)
2025-08-03 16:14:45,892 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-03 16:14:45,893 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:14:45,895 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:14:45,896 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 107960.0
2025-08-03 16:14:48,001 - ExploitationExpert - INFO - res_population_num: 1
2025-08-03 16:14:48,001 - ExploitationExpert - INFO - res_population_costs: [9836.0]
2025-08-03 16:14:48,001 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 48, 43, 40,
       49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:14:48,002 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:14:48,002 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 16, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 14, 9, 11, 7, 3, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12905.0}, {'tour': array([52, 21, 28, 20, 53, 57, 47, 11, 63, 19, 41, 31, 44,  5, 54, 29, 43,
       30, 61, 49, 22, 55,  9, 33, 23, 50, 36, 32,  0, 51, 14, 27, 42, 62,
       10, 39, 40, 26,  1, 12, 48, 65, 60, 13, 64,  3, 46, 18, 45,  4,  8,
       24, 16, 56, 58, 17,  7,  6,  2, 35, 38, 34, 37, 25, 59, 15],
      dtype=int64), 'cur_cost': 107960.0}, {'tour': array([16, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 14, 27, 37, 25, 26, 36,
       35, 28, 30, 34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,
        2,  6, 10,  0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 10076.0}, {'tour': array([20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36,
       35, 28, 30, 34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,
        2,  6, 10,  0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9958.0}, {'tour': array([16, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 14, 27, 37, 25, 26, 36,
       35, 28, 30, 34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,
        2,  6, 10,  0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 10076.0}, {'tour': array([46, 47, 49, 40, 43, 48, 39, 44, 45, 38, 51, 50, 41, 42, 21, 20, 13,
       23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30,
       34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,  2,  6, 10,
        0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63],
      dtype=int64), 'cur_cost': 9976.0}, {'tour': array([56,  1, 47, 14, 32, 11, 21, 45, 44, 59, 12, 46, 10, 61, 30, 24,  3,
       35, 41, 55, 28, 40,  5, 13, 38, 34, 36,  4,  2, 17, 39, 16, 43, 19,
       63,  7, 54, 27,  9, 25,  6, 37, 62, 52, 22, 23, 58, 29,  8, 20, 53,
       51, 57, 60, 65, 49,  0, 42, 26, 48, 31, 50, 64, 18, 33, 15],
      dtype=int64), 'cur_cost': 114783.0}, {'tour': array([19, 60,  9, 61, 55, 41, 27, 65, 24,  6, 63, 54, 57, 47, 13, 12,  8,
        7, 58,  5, 17, 56, 35, 44,  2, 22, 21, 20, 28, 25, 15, 34, 29, 46,
       64,  3, 37, 31, 18, 23,  1, 62, 16, 39, 48, 36,  4, 26, 10, 53, 59,
       11, 45, 43, 52, 50, 51, 14, 49, 40, 30, 38, 33, 32,  0, 42],
      dtype=int64), 'cur_cost': 90006.0}, {'tour': array([19, 22, 50, 14, 56,  3,  8, 49, 55, 42,  6, 45, 37,  4, 46, 16, 26,
       31,  1, 32, 51, 24, 30, 27, 18, 39, 28, 54, 57, 65, 10, 41, 38,  5,
       64, 33, 35, 13, 21, 11, 59, 43, 15, 60, 58, 20, 53, 44,  7, 47,  9,
       62, 25, 17, 61, 48, 36,  0, 63, 40, 52, 34, 29,  2, 12, 23],
      dtype=int64), 'cur_cost': 105558.0}, {'tour': array([38, 15, 14,  7, 21, 44, 46, 45, 57,  4, 35, 65, 24, 20, 31, 17, 62,
       22, 60, 64, 12, 34,  6, 63, 19, 36, 54, 13, 33, 26, 40, 23,  8, 37,
       32,  1, 28, 29,  0,  3, 25, 16, 27, 55, 59, 41, 50, 48, 42, 53, 47,
        2, 61, 43, 51, 18, 11,  9, 56,  5, 39, 58, 52, 49, 10, 30],
      dtype=int64), 'cur_cost': 96878.0}, {'tour': array([14, 41, 61, 60, 13, 27, 46, 12, 63, 37, 56, 48, 39, 29, 36,  8, 54,
       24, 52, 34,  9,  3, 25,  4, 50, 51,  7, 55, 43, 33,  2, 65, 28, 21,
        0, 11, 64,  5, 53, 62, 30, 45, 18, 42,  6, 10, 38, 32, 26, 40, 16,
       58, 17, 22, 19, 59, 23, 57, 49, 47, 15, 31, 35, 44, 20,  1],
      dtype=int64), 'cur_cost': 104551.0}, {'tour': array([ 0, 50,  5, 49, 32, 22, 16, 63, 42, 62,  6, 23, 28,  7, 33,  8, 18,
       34, 26,  4, 53, 60, 27, 57, 30, 47, 14, 11, 48,  9, 12, 35, 39, 55,
       29, 31, 36, 25, 19, 13, 41, 65, 45, 52, 43, 10, 15, 64, 38, 20, 40,
       54,  3, 51, 56,  2, 37, 24, 44, 59,  1, 17, 46, 58, 61, 21],
      dtype=int64), 'cur_cost': 114816.0}, {'tour': array([45, 62, 41, 17, 49, 31, 39, 12, 50, 57, 44,  5, 47, 65, 60, 33, 14,
       34, 27, 59, 21, 15, 13, 35,  6, 43, 24, 26, 42, 52, 53, 16,  2, 11,
       18, 58, 61,  1, 20, 30, 22, 55,  9, 40,  4, 63, 29, 36, 19, 25, 38,
       64, 10, 37, 23,  8, 54, 56, 51, 48, 32, 28,  0,  3,  7, 46],
      dtype=int64), 'cur_cost': 99359.0}, {'tour': array([61, 65, 44, 47,  9, 28,  4, 14, 42, 27, 13, 53, 21, 33,  5, 26, 11,
       22, 48, 46, 43, 31,  6, 29, 56, 62, 18, 32, 15, 25, 34, 19, 41, 17,
        8, 57,  7,  3, 55, 64, 54, 10, 37, 45, 63, 58, 52, 30,  2, 40, 51,
       20, 36,  0, 59, 50, 38,  1, 39, 24, 12, 49, 35, 60, 23, 16],
      dtype=int64), 'cur_cost': 95442.0}, {'tour': array([31, 34, 21, 55, 35, 45, 64, 42, 37, 36, 16, 30, 10,  9, 60,  7, 46,
       43, 44, 19, 11,  6, 57, 20, 18, 15,  0,  1, 14, 49, 38, 52,  2, 61,
       48,  8,  4, 22, 40,  5, 28,  3, 50, 47, 53, 62, 58, 56, 24, 41, 26,
       39, 63, 54, 59, 25, 51, 32, 29, 17, 27, 33, 12, 23, 13, 65],
      dtype=int64), 'cur_cost': 88151.0}, {'tour': array([18, 10, 19, 65, 43, 37, 61,  6, 56, 36, 59, 63, 50, 52, 33,  8, 51,
       60, 30, 28, 39, 23,  1, 54,  4, 20, 32,  0,  9, 25, 35, 14, 47, 46,
       49, 38, 17, 12, 31, 15, 64, 13, 44,  5, 22, 34, 24, 42, 11, 58, 45,
       16, 41, 53, 55,  2, 26, 27, 62, 29, 57, 40, 21,  3, 48,  7],
      dtype=int64), 'cur_cost': 107601.0}, {'tour': array([25, 22, 42, 14, 11, 28, 47, 65, 58, 45, 16, 35, 17, 26,  0, 34, 50,
       44, 10, 57, 49, 51, 60, 43, 54,  3, 33, 19, 55, 61,  2,  5, 20, 36,
       40, 32, 59,  4, 56, 21, 39, 37, 46, 12,  7, 13, 48,  6,  8, 62, 30,
       41, 52, 23, 18, 27, 38, 31,  1, 64,  9, 15, 29, 24, 63, 53],
      dtype=int64), 'cur_cost': 104191.0}, {'tour': array([57, 62, 43,  6, 21, 48, 15, 25, 38, 28, 10, 27, 53, 55, 23, 50, 14,
       47,  8, 61, 64, 30, 22, 11, 58, 59, 52, 46, 63, 60, 20, 34, 42,  2,
       26, 32, 49, 37, 17,  3,  7, 35, 31, 45,  0, 41, 51, 33, 12, 54, 56,
       18,  5, 16, 29, 40, 65, 36, 13,  1,  9, 19, 44, 39, 24,  4],
      dtype=int64), 'cur_cost': 98937.0}, {'tour': array([26, 19, 46, 22, 24, 48,  2, 55, 61, 29,  6, 38, 64, 16, 63,  8, 51,
       10, 44,  5, 33, 39, 53, 56, 37, 14,  9, 49,  1, 11,  0, 59, 45, 57,
       62,  7, 34, 13, 25, 35, 23, 50, 15, 65, 20, 58, 40, 17, 31, 42, 60,
       18, 43,  3, 27, 36, 41, 54, 52,  4, 12, 32, 21, 28, 47, 30],
      dtype=int64), 'cur_cost': 108939.0}, {'tour': array([ 7, 48, 38, 39, 21,  5, 60, 61,  2, 46, 50, 58, 36, 12, 43, 20, 54,
       18, 14, 10, 33, 26, 24, 28, 34, 41, 35, 62,  8,  0, 23, 15,  6, 53,
       45, 40,  3,  9, 56, 57, 22, 55, 52, 63, 51, 31,  4, 13, 42, 25, 64,
       37, 65, 44, 30, 29, 59, 19,  1, 11, 49, 32, 27, 17, 16, 47],
      dtype=int64), 'cur_cost': 90908.0}]
2025-08-03 16:14:48,014 - ExploitationExpert - INFO - 局部搜索耗时: 2.12秒
2025-08-03 16:14:48,016 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-03 16:14:48,019 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([52, 21, 28, 20, 53, 57, 47, 11, 63, 19, 41, 31, 44,  5, 54, 29, 43,
       30, 61, 49, 22, 55,  9, 33, 23, 50, 36, 32,  0, 51, 14, 27, 42, 62,
       10, 39, 40, 26,  1, 12, 48, 65, 60, 13, 64,  3, 46, 18, 45,  4,  8,
       24, 16, 56, 58, 17,  7,  6,  2, 35, 38, 34, 37, 25, 59, 15],
      dtype=int64), 'cur_cost': 107960.0}
2025-08-03 16:14:48,021 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 107960.00)
2025-08-03 16:14:48,021 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-03 16:14:48,021 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-03 16:14:48,022 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:14:48,037 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:14:48,038 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:14:48,038 - ExplorationExpert - INFO - 探索路径生成完成，成本: 50494.0, 路径长度: 66
2025-08-03 16:14:48,038 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [64, 6, 52, 5, 61, 60, 53, 62, 23, 4, 10, 9, 11, 3, 63, 14, 25, 37, 22, 26, 33, 21, 16, 12, 7, 55, 54, 47, 40, 49, 41, 46, 51, 50, 20, 35, 28, 32, 30, 18, 29, 1, 59, 2, 65, 15, 17, 31, 27, 8, 0, 57, 56, 43, 44, 48, 45, 39, 13, 34, 19, 36, 24, 42, 38, 58], 'cur_cost': 50494.0}
2025-08-03 16:14:48,039 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 50494.00)
2025-08-03 16:14:48,039 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-03 16:14:48,039 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-03 16:14:48,039 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:14:48,043 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:14:48,044 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:14:48,044 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12362.0, 路径长度: 66
2025-08-03 16:14:48,044 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 15, 13, 12, 22, 23, 16, 18, 17, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12362.0}
2025-08-03 16:14:48,045 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 12362.00)
2025-08-03 16:14:48,045 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-03 16:14:48,045 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:14:48,046 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:14:48,046 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 108065.0
2025-08-03 16:14:50,449 - ExploitationExpert - INFO - res_population_num: 2
2025-08-03 16:14:50,452 - ExploitationExpert - INFO - res_population_costs: [9836.0, 9577.0]
2025-08-03 16:14:50,452 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 48, 43, 40,
       49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:14:50,455 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:14:50,455 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 16, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 14, 9, 11, 7, 3, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12905.0}, {'tour': array([52, 21, 28, 20, 53, 57, 47, 11, 63, 19, 41, 31, 44,  5, 54, 29, 43,
       30, 61, 49, 22, 55,  9, 33, 23, 50, 36, 32,  0, 51, 14, 27, 42, 62,
       10, 39, 40, 26,  1, 12, 48, 65, 60, 13, 64,  3, 46, 18, 45,  4,  8,
       24, 16, 56, 58, 17,  7,  6,  2, 35, 38, 34, 37, 25, 59, 15],
      dtype=int64), 'cur_cost': 107960.0}, {'tour': [64, 6, 52, 5, 61, 60, 53, 62, 23, 4, 10, 9, 11, 3, 63, 14, 25, 37, 22, 26, 33, 21, 16, 12, 7, 55, 54, 47, 40, 49, 41, 46, 51, 50, 20, 35, 28, 32, 30, 18, 29, 1, 59, 2, 65, 15, 17, 31, 27, 8, 0, 57, 56, 43, 44, 48, 45, 39, 13, 34, 19, 36, 24, 42, 38, 58], 'cur_cost': 50494.0}, {'tour': [0, 15, 13, 12, 22, 23, 16, 18, 17, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12362.0}, {'tour': array([13, 43, 23, 26, 10, 41, 32, 33, 47,  8, 34, 53, 61, 50, 39,  4,  6,
       37, 30, 45, 63,  5, 64, 55, 16, 12, 11,  3, 40, 60, 21, 49, 14, 25,
        2, 42, 65, 15, 28, 38, 57, 17, 22, 54, 56, 35,  9, 58, 29, 62, 18,
       24,  0, 52, 46, 51, 36, 20, 48, 19, 27, 59, 44, 31,  1,  7],
      dtype=int64), 'cur_cost': 108065.0}, {'tour': array([46, 47, 49, 40, 43, 48, 39, 44, 45, 38, 51, 50, 41, 42, 21, 20, 13,
       23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30,
       34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,  2,  6, 10,
        0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63],
      dtype=int64), 'cur_cost': 9976.0}, {'tour': array([56,  1, 47, 14, 32, 11, 21, 45, 44, 59, 12, 46, 10, 61, 30, 24,  3,
       35, 41, 55, 28, 40,  5, 13, 38, 34, 36,  4,  2, 17, 39, 16, 43, 19,
       63,  7, 54, 27,  9, 25,  6, 37, 62, 52, 22, 23, 58, 29,  8, 20, 53,
       51, 57, 60, 65, 49,  0, 42, 26, 48, 31, 50, 64, 18, 33, 15],
      dtype=int64), 'cur_cost': 114783.0}, {'tour': array([19, 60,  9, 61, 55, 41, 27, 65, 24,  6, 63, 54, 57, 47, 13, 12,  8,
        7, 58,  5, 17, 56, 35, 44,  2, 22, 21, 20, 28, 25, 15, 34, 29, 46,
       64,  3, 37, 31, 18, 23,  1, 62, 16, 39, 48, 36,  4, 26, 10, 53, 59,
       11, 45, 43, 52, 50, 51, 14, 49, 40, 30, 38, 33, 32,  0, 42],
      dtype=int64), 'cur_cost': 90006.0}, {'tour': array([19, 22, 50, 14, 56,  3,  8, 49, 55, 42,  6, 45, 37,  4, 46, 16, 26,
       31,  1, 32, 51, 24, 30, 27, 18, 39, 28, 54, 57, 65, 10, 41, 38,  5,
       64, 33, 35, 13, 21, 11, 59, 43, 15, 60, 58, 20, 53, 44,  7, 47,  9,
       62, 25, 17, 61, 48, 36,  0, 63, 40, 52, 34, 29,  2, 12, 23],
      dtype=int64), 'cur_cost': 105558.0}, {'tour': array([38, 15, 14,  7, 21, 44, 46, 45, 57,  4, 35, 65, 24, 20, 31, 17, 62,
       22, 60, 64, 12, 34,  6, 63, 19, 36, 54, 13, 33, 26, 40, 23,  8, 37,
       32,  1, 28, 29,  0,  3, 25, 16, 27, 55, 59, 41, 50, 48, 42, 53, 47,
        2, 61, 43, 51, 18, 11,  9, 56,  5, 39, 58, 52, 49, 10, 30],
      dtype=int64), 'cur_cost': 96878.0}, {'tour': array([14, 41, 61, 60, 13, 27, 46, 12, 63, 37, 56, 48, 39, 29, 36,  8, 54,
       24, 52, 34,  9,  3, 25,  4, 50, 51,  7, 55, 43, 33,  2, 65, 28, 21,
        0, 11, 64,  5, 53, 62, 30, 45, 18, 42,  6, 10, 38, 32, 26, 40, 16,
       58, 17, 22, 19, 59, 23, 57, 49, 47, 15, 31, 35, 44, 20,  1],
      dtype=int64), 'cur_cost': 104551.0}, {'tour': array([ 0, 50,  5, 49, 32, 22, 16, 63, 42, 62,  6, 23, 28,  7, 33,  8, 18,
       34, 26,  4, 53, 60, 27, 57, 30, 47, 14, 11, 48,  9, 12, 35, 39, 55,
       29, 31, 36, 25, 19, 13, 41, 65, 45, 52, 43, 10, 15, 64, 38, 20, 40,
       54,  3, 51, 56,  2, 37, 24, 44, 59,  1, 17, 46, 58, 61, 21],
      dtype=int64), 'cur_cost': 114816.0}, {'tour': array([45, 62, 41, 17, 49, 31, 39, 12, 50, 57, 44,  5, 47, 65, 60, 33, 14,
       34, 27, 59, 21, 15, 13, 35,  6, 43, 24, 26, 42, 52, 53, 16,  2, 11,
       18, 58, 61,  1, 20, 30, 22, 55,  9, 40,  4, 63, 29, 36, 19, 25, 38,
       64, 10, 37, 23,  8, 54, 56, 51, 48, 32, 28,  0,  3,  7, 46],
      dtype=int64), 'cur_cost': 99359.0}, {'tour': array([61, 65, 44, 47,  9, 28,  4, 14, 42, 27, 13, 53, 21, 33,  5, 26, 11,
       22, 48, 46, 43, 31,  6, 29, 56, 62, 18, 32, 15, 25, 34, 19, 41, 17,
        8, 57,  7,  3, 55, 64, 54, 10, 37, 45, 63, 58, 52, 30,  2, 40, 51,
       20, 36,  0, 59, 50, 38,  1, 39, 24, 12, 49, 35, 60, 23, 16],
      dtype=int64), 'cur_cost': 95442.0}, {'tour': array([31, 34, 21, 55, 35, 45, 64, 42, 37, 36, 16, 30, 10,  9, 60,  7, 46,
       43, 44, 19, 11,  6, 57, 20, 18, 15,  0,  1, 14, 49, 38, 52,  2, 61,
       48,  8,  4, 22, 40,  5, 28,  3, 50, 47, 53, 62, 58, 56, 24, 41, 26,
       39, 63, 54, 59, 25, 51, 32, 29, 17, 27, 33, 12, 23, 13, 65],
      dtype=int64), 'cur_cost': 88151.0}, {'tour': array([18, 10, 19, 65, 43, 37, 61,  6, 56, 36, 59, 63, 50, 52, 33,  8, 51,
       60, 30, 28, 39, 23,  1, 54,  4, 20, 32,  0,  9, 25, 35, 14, 47, 46,
       49, 38, 17, 12, 31, 15, 64, 13, 44,  5, 22, 34, 24, 42, 11, 58, 45,
       16, 41, 53, 55,  2, 26, 27, 62, 29, 57, 40, 21,  3, 48,  7],
      dtype=int64), 'cur_cost': 107601.0}, {'tour': array([25, 22, 42, 14, 11, 28, 47, 65, 58, 45, 16, 35, 17, 26,  0, 34, 50,
       44, 10, 57, 49, 51, 60, 43, 54,  3, 33, 19, 55, 61,  2,  5, 20, 36,
       40, 32, 59,  4, 56, 21, 39, 37, 46, 12,  7, 13, 48,  6,  8, 62, 30,
       41, 52, 23, 18, 27, 38, 31,  1, 64,  9, 15, 29, 24, 63, 53],
      dtype=int64), 'cur_cost': 104191.0}, {'tour': array([57, 62, 43,  6, 21, 48, 15, 25, 38, 28, 10, 27, 53, 55, 23, 50, 14,
       47,  8, 61, 64, 30, 22, 11, 58, 59, 52, 46, 63, 60, 20, 34, 42,  2,
       26, 32, 49, 37, 17,  3,  7, 35, 31, 45,  0, 41, 51, 33, 12, 54, 56,
       18,  5, 16, 29, 40, 65, 36, 13,  1,  9, 19, 44, 39, 24,  4],
      dtype=int64), 'cur_cost': 98937.0}, {'tour': array([26, 19, 46, 22, 24, 48,  2, 55, 61, 29,  6, 38, 64, 16, 63,  8, 51,
       10, 44,  5, 33, 39, 53, 56, 37, 14,  9, 49,  1, 11,  0, 59, 45, 57,
       62,  7, 34, 13, 25, 35, 23, 50, 15, 65, 20, 58, 40, 17, 31, 42, 60,
       18, 43,  3, 27, 36, 41, 54, 52,  4, 12, 32, 21, 28, 47, 30],
      dtype=int64), 'cur_cost': 108939.0}, {'tour': array([ 7, 48, 38, 39, 21,  5, 60, 61,  2, 46, 50, 58, 36, 12, 43, 20, 54,
       18, 14, 10, 33, 26, 24, 28, 34, 41, 35, 62,  8,  0, 23, 15,  6, 53,
       45, 40,  3,  9, 56, 57, 22, 55, 52, 63, 51, 31,  4, 13, 42, 25, 64,
       37, 65, 44, 30, 29, 59, 19,  1, 11, 49, 32, 27, 17, 16, 47],
      dtype=int64), 'cur_cost': 90908.0}]
2025-08-03 16:14:50,466 - ExploitationExpert - INFO - 局部搜索耗时: 2.42秒
2025-08-03 16:14:50,466 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-03 16:14:50,467 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([13, 43, 23, 26, 10, 41, 32, 33, 47,  8, 34, 53, 61, 50, 39,  4,  6,
       37, 30, 45, 63,  5, 64, 55, 16, 12, 11,  3, 40, 60, 21, 49, 14, 25,
        2, 42, 65, 15, 28, 38, 57, 17, 22, 54, 56, 35,  9, 58, 29, 62, 18,
       24,  0, 52, 46, 51, 36, 20, 48, 19, 27, 59, 44, 31,  1,  7],
      dtype=int64), 'cur_cost': 108065.0}
2025-08-03 16:14:50,468 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 108065.00)
2025-08-03 16:14:50,468 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-03 16:14:50,468 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-03 16:14:50,469 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:14:50,474 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:14:50,475 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:14:50,475 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12833.0, 路径长度: 66
2025-08-03 16:14:50,476 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 18, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12833.0}
2025-08-03 16:14:50,476 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 12833.00)
2025-08-03 16:14:50,477 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-03 16:14:50,477 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-03 16:14:50,478 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:14:50,497 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:14:50,499 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:14:50,500 - ExplorationExpert - INFO - 探索路径生成完成，成本: 71548.0, 路径长度: 66
2025-08-03 16:14:50,500 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [12, 5, 16, 14, 17, 13, 11, 10, 7, 59, 54, 62, 52, 53, 40, 19, 2, 56, 47, 21, 9, 22, 33, 15, 37, 26, 27, 4, 57, 23, 0, 6, 65, 39, 42, 36, 29, 1, 25, 24, 3, 8, 34, 48, 18, 49, 50, 45, 44, 35, 28, 38, 51, 46, 30, 41, 31, 55, 58, 61, 60, 63, 20, 32, 43, 64], 'cur_cost': 71548.0}
2025-08-03 16:14:50,501 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 71548.00)
2025-08-03 16:14:50,502 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-03 16:14:50,502 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:14:50,502 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:14:50,502 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 107771.0
2025-08-03 16:14:51,105 - ExploitationExpert - INFO - res_population_num: 3
2025-08-03 16:14:51,106 - ExploitationExpert - INFO - res_population_costs: [9836.0, 9577.0, 9575]
2025-08-03 16:14:51,106 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 48, 43, 40,
       49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:14:51,108 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:14:51,108 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 16, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 14, 9, 11, 7, 3, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12905.0}, {'tour': array([52, 21, 28, 20, 53, 57, 47, 11, 63, 19, 41, 31, 44,  5, 54, 29, 43,
       30, 61, 49, 22, 55,  9, 33, 23, 50, 36, 32,  0, 51, 14, 27, 42, 62,
       10, 39, 40, 26,  1, 12, 48, 65, 60, 13, 64,  3, 46, 18, 45,  4,  8,
       24, 16, 56, 58, 17,  7,  6,  2, 35, 38, 34, 37, 25, 59, 15],
      dtype=int64), 'cur_cost': 107960.0}, {'tour': [64, 6, 52, 5, 61, 60, 53, 62, 23, 4, 10, 9, 11, 3, 63, 14, 25, 37, 22, 26, 33, 21, 16, 12, 7, 55, 54, 47, 40, 49, 41, 46, 51, 50, 20, 35, 28, 32, 30, 18, 29, 1, 59, 2, 65, 15, 17, 31, 27, 8, 0, 57, 56, 43, 44, 48, 45, 39, 13, 34, 19, 36, 24, 42, 38, 58], 'cur_cost': 50494.0}, {'tour': [0, 15, 13, 12, 22, 23, 16, 18, 17, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12362.0}, {'tour': array([13, 43, 23, 26, 10, 41, 32, 33, 47,  8, 34, 53, 61, 50, 39,  4,  6,
       37, 30, 45, 63,  5, 64, 55, 16, 12, 11,  3, 40, 60, 21, 49, 14, 25,
        2, 42, 65, 15, 28, 38, 57, 17, 22, 54, 56, 35,  9, 58, 29, 62, 18,
       24,  0, 52, 46, 51, 36, 20, 48, 19, 27, 59, 44, 31,  1,  7],
      dtype=int64), 'cur_cost': 108065.0}, {'tour': [0, 18, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12833.0}, {'tour': [12, 5, 16, 14, 17, 13, 11, 10, 7, 59, 54, 62, 52, 53, 40, 19, 2, 56, 47, 21, 9, 22, 33, 15, 37, 26, 27, 4, 57, 23, 0, 6, 65, 39, 42, 36, 29, 1, 25, 24, 3, 8, 34, 48, 18, 49, 50, 45, 44, 35, 28, 38, 51, 46, 30, 41, 31, 55, 58, 61, 60, 63, 20, 32, 43, 64], 'cur_cost': 71548.0}, {'tour': array([27, 40, 47,  0, 63, 60,  9, 11, 57, 14,  2, 17, 35, 58, 13, 36, 16,
       62, 12, 41, 32,  6, 48, 55, 37,  4, 38, 52, 45, 39, 19,  3, 24, 42,
       30, 43,  1, 10, 49,  7, 20, 28, 65, 31, 51, 53, 23, 44,  5, 22, 50,
       59, 56, 18, 21, 33, 34, 54, 64, 61, 46, 25, 15,  8, 29, 26],
      dtype=int64), 'cur_cost': 107771.0}, {'tour': array([19, 22, 50, 14, 56,  3,  8, 49, 55, 42,  6, 45, 37,  4, 46, 16, 26,
       31,  1, 32, 51, 24, 30, 27, 18, 39, 28, 54, 57, 65, 10, 41, 38,  5,
       64, 33, 35, 13, 21, 11, 59, 43, 15, 60, 58, 20, 53, 44,  7, 47,  9,
       62, 25, 17, 61, 48, 36,  0, 63, 40, 52, 34, 29,  2, 12, 23],
      dtype=int64), 'cur_cost': 105558.0}, {'tour': array([38, 15, 14,  7, 21, 44, 46, 45, 57,  4, 35, 65, 24, 20, 31, 17, 62,
       22, 60, 64, 12, 34,  6, 63, 19, 36, 54, 13, 33, 26, 40, 23,  8, 37,
       32,  1, 28, 29,  0,  3, 25, 16, 27, 55, 59, 41, 50, 48, 42, 53, 47,
        2, 61, 43, 51, 18, 11,  9, 56,  5, 39, 58, 52, 49, 10, 30],
      dtype=int64), 'cur_cost': 96878.0}, {'tour': array([14, 41, 61, 60, 13, 27, 46, 12, 63, 37, 56, 48, 39, 29, 36,  8, 54,
       24, 52, 34,  9,  3, 25,  4, 50, 51,  7, 55, 43, 33,  2, 65, 28, 21,
        0, 11, 64,  5, 53, 62, 30, 45, 18, 42,  6, 10, 38, 32, 26, 40, 16,
       58, 17, 22, 19, 59, 23, 57, 49, 47, 15, 31, 35, 44, 20,  1],
      dtype=int64), 'cur_cost': 104551.0}, {'tour': array([ 0, 50,  5, 49, 32, 22, 16, 63, 42, 62,  6, 23, 28,  7, 33,  8, 18,
       34, 26,  4, 53, 60, 27, 57, 30, 47, 14, 11, 48,  9, 12, 35, 39, 55,
       29, 31, 36, 25, 19, 13, 41, 65, 45, 52, 43, 10, 15, 64, 38, 20, 40,
       54,  3, 51, 56,  2, 37, 24, 44, 59,  1, 17, 46, 58, 61, 21],
      dtype=int64), 'cur_cost': 114816.0}, {'tour': array([45, 62, 41, 17, 49, 31, 39, 12, 50, 57, 44,  5, 47, 65, 60, 33, 14,
       34, 27, 59, 21, 15, 13, 35,  6, 43, 24, 26, 42, 52, 53, 16,  2, 11,
       18, 58, 61,  1, 20, 30, 22, 55,  9, 40,  4, 63, 29, 36, 19, 25, 38,
       64, 10, 37, 23,  8, 54, 56, 51, 48, 32, 28,  0,  3,  7, 46],
      dtype=int64), 'cur_cost': 99359.0}, {'tour': array([61, 65, 44, 47,  9, 28,  4, 14, 42, 27, 13, 53, 21, 33,  5, 26, 11,
       22, 48, 46, 43, 31,  6, 29, 56, 62, 18, 32, 15, 25, 34, 19, 41, 17,
        8, 57,  7,  3, 55, 64, 54, 10, 37, 45, 63, 58, 52, 30,  2, 40, 51,
       20, 36,  0, 59, 50, 38,  1, 39, 24, 12, 49, 35, 60, 23, 16],
      dtype=int64), 'cur_cost': 95442.0}, {'tour': array([31, 34, 21, 55, 35, 45, 64, 42, 37, 36, 16, 30, 10,  9, 60,  7, 46,
       43, 44, 19, 11,  6, 57, 20, 18, 15,  0,  1, 14, 49, 38, 52,  2, 61,
       48,  8,  4, 22, 40,  5, 28,  3, 50, 47, 53, 62, 58, 56, 24, 41, 26,
       39, 63, 54, 59, 25, 51, 32, 29, 17, 27, 33, 12, 23, 13, 65],
      dtype=int64), 'cur_cost': 88151.0}, {'tour': array([18, 10, 19, 65, 43, 37, 61,  6, 56, 36, 59, 63, 50, 52, 33,  8, 51,
       60, 30, 28, 39, 23,  1, 54,  4, 20, 32,  0,  9, 25, 35, 14, 47, 46,
       49, 38, 17, 12, 31, 15, 64, 13, 44,  5, 22, 34, 24, 42, 11, 58, 45,
       16, 41, 53, 55,  2, 26, 27, 62, 29, 57, 40, 21,  3, 48,  7],
      dtype=int64), 'cur_cost': 107601.0}, {'tour': array([25, 22, 42, 14, 11, 28, 47, 65, 58, 45, 16, 35, 17, 26,  0, 34, 50,
       44, 10, 57, 49, 51, 60, 43, 54,  3, 33, 19, 55, 61,  2,  5, 20, 36,
       40, 32, 59,  4, 56, 21, 39, 37, 46, 12,  7, 13, 48,  6,  8, 62, 30,
       41, 52, 23, 18, 27, 38, 31,  1, 64,  9, 15, 29, 24, 63, 53],
      dtype=int64), 'cur_cost': 104191.0}, {'tour': array([57, 62, 43,  6, 21, 48, 15, 25, 38, 28, 10, 27, 53, 55, 23, 50, 14,
       47,  8, 61, 64, 30, 22, 11, 58, 59, 52, 46, 63, 60, 20, 34, 42,  2,
       26, 32, 49, 37, 17,  3,  7, 35, 31, 45,  0, 41, 51, 33, 12, 54, 56,
       18,  5, 16, 29, 40, 65, 36, 13,  1,  9, 19, 44, 39, 24,  4],
      dtype=int64), 'cur_cost': 98937.0}, {'tour': array([26, 19, 46, 22, 24, 48,  2, 55, 61, 29,  6, 38, 64, 16, 63,  8, 51,
       10, 44,  5, 33, 39, 53, 56, 37, 14,  9, 49,  1, 11,  0, 59, 45, 57,
       62,  7, 34, 13, 25, 35, 23, 50, 15, 65, 20, 58, 40, 17, 31, 42, 60,
       18, 43,  3, 27, 36, 41, 54, 52,  4, 12, 32, 21, 28, 47, 30],
      dtype=int64), 'cur_cost': 108939.0}, {'tour': array([ 7, 48, 38, 39, 21,  5, 60, 61,  2, 46, 50, 58, 36, 12, 43, 20, 54,
       18, 14, 10, 33, 26, 24, 28, 34, 41, 35, 62,  8,  0, 23, 15,  6, 53,
       45, 40,  3,  9, 56, 57, 22, 55, 52, 63, 51, 31,  4, 13, 42, 25, 64,
       37, 65, 44, 30, 29, 59, 19,  1, 11, 49, 32, 27, 17, 16, 47],
      dtype=int64), 'cur_cost': 90908.0}]
2025-08-03 16:14:51,121 - ExploitationExpert - INFO - 局部搜索耗时: 0.62秒
2025-08-03 16:14:51,121 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-03 16:14:51,123 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([27, 40, 47,  0, 63, 60,  9, 11, 57, 14,  2, 17, 35, 58, 13, 36, 16,
       62, 12, 41, 32,  6, 48, 55, 37,  4, 38, 52, 45, 39, 19,  3, 24, 42,
       30, 43,  1, 10, 49,  7, 20, 28, 65, 31, 51, 53, 23, 44,  5, 22, 50,
       59, 56, 18, 21, 33, 34, 54, 64, 61, 46, 25, 15,  8, 29, 26],
      dtype=int64), 'cur_cost': 107771.0}
2025-08-03 16:14:51,123 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 107771.00)
2025-08-03 16:14:51,124 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-03 16:14:51,124 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-03 16:14:51,125 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:14:51,129 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:14:51,130 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:14:51,131 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14698.0, 路径长度: 66
2025-08-03 16:14:51,131 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 18, 6, 17, 12, 22, 23, 16, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14698.0}
2025-08-03 16:14:51,132 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 14698.00)
2025-08-03 16:14:51,132 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-03 16:14:51,133 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-03 16:14:51,133 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:14:51,137 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:14:51,138 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:14:51,138 - ExplorationExpert - INFO - 探索路径生成完成，成本: 117875.0, 路径长度: 66
2025-08-03 16:14:51,138 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [5, 31, 25, 53, 9, 23, 13, 19, 3, 2, 56, 20, 12, 24, 27, 4, 22, 51, 35, 32, 18, 8, 36, 15, 44, 57, 34, 65, 43, 11, 40, 60, 30, 58, 49, 21, 7, 42, 33, 47, 26, 59, 16, 6, 0, 46, 17, 55, 41, 52, 14, 10, 29, 62, 50, 38, 64, 61, 63, 45, 28, 39, 1, 37, 54, 48], 'cur_cost': 117875.0}
2025-08-03 16:14:51,139 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 117875.00)
2025-08-03 16:14:51,139 - experts.management.collaboration_manager - INFO - 为个体 10 生成利用路径
2025-08-03 16:14:51,139 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:14:51,140 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:14:51,140 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 10 处的路径，新成本: 103268.0
2025-08-03 16:14:51,223 - ExploitationExpert - INFO - res_population_num: 10
2025-08-03 16:14:51,223 - ExploitationExpert - INFO - res_population_costs: [9836.0, 9577.0, 9575, 9554.0, 9554, 9533, 9532, 9527, 9522, 9521]
2025-08-03 16:14:51,224 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 48, 43, 40,
       49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64, 52, 63,
       65, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64, 52, 63,
       65, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 22, 14, 15, 17, 12, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:14:51,230 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:14:51,230 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 16, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 14, 9, 11, 7, 3, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12905.0}, {'tour': array([52, 21, 28, 20, 53, 57, 47, 11, 63, 19, 41, 31, 44,  5, 54, 29, 43,
       30, 61, 49, 22, 55,  9, 33, 23, 50, 36, 32,  0, 51, 14, 27, 42, 62,
       10, 39, 40, 26,  1, 12, 48, 65, 60, 13, 64,  3, 46, 18, 45,  4,  8,
       24, 16, 56, 58, 17,  7,  6,  2, 35, 38, 34, 37, 25, 59, 15],
      dtype=int64), 'cur_cost': 107960.0}, {'tour': [64, 6, 52, 5, 61, 60, 53, 62, 23, 4, 10, 9, 11, 3, 63, 14, 25, 37, 22, 26, 33, 21, 16, 12, 7, 55, 54, 47, 40, 49, 41, 46, 51, 50, 20, 35, 28, 32, 30, 18, 29, 1, 59, 2, 65, 15, 17, 31, 27, 8, 0, 57, 56, 43, 44, 48, 45, 39, 13, 34, 19, 36, 24, 42, 38, 58], 'cur_cost': 50494.0}, {'tour': [0, 15, 13, 12, 22, 23, 16, 18, 17, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12362.0}, {'tour': array([13, 43, 23, 26, 10, 41, 32, 33, 47,  8, 34, 53, 61, 50, 39,  4,  6,
       37, 30, 45, 63,  5, 64, 55, 16, 12, 11,  3, 40, 60, 21, 49, 14, 25,
        2, 42, 65, 15, 28, 38, 57, 17, 22, 54, 56, 35,  9, 58, 29, 62, 18,
       24,  0, 52, 46, 51, 36, 20, 48, 19, 27, 59, 44, 31,  1,  7],
      dtype=int64), 'cur_cost': 108065.0}, {'tour': [0, 18, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12833.0}, {'tour': [12, 5, 16, 14, 17, 13, 11, 10, 7, 59, 54, 62, 52, 53, 40, 19, 2, 56, 47, 21, 9, 22, 33, 15, 37, 26, 27, 4, 57, 23, 0, 6, 65, 39, 42, 36, 29, 1, 25, 24, 3, 8, 34, 48, 18, 49, 50, 45, 44, 35, 28, 38, 51, 46, 30, 41, 31, 55, 58, 61, 60, 63, 20, 32, 43, 64], 'cur_cost': 71548.0}, {'tour': array([27, 40, 47,  0, 63, 60,  9, 11, 57, 14,  2, 17, 35, 58, 13, 36, 16,
       62, 12, 41, 32,  6, 48, 55, 37,  4, 38, 52, 45, 39, 19,  3, 24, 42,
       30, 43,  1, 10, 49,  7, 20, 28, 65, 31, 51, 53, 23, 44,  5, 22, 50,
       59, 56, 18, 21, 33, 34, 54, 64, 61, 46, 25, 15,  8, 29, 26],
      dtype=int64), 'cur_cost': 107771.0}, {'tour': [0, 18, 6, 17, 12, 22, 23, 16, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14698.0}, {'tour': [5, 31, 25, 53, 9, 23, 13, 19, 3, 2, 56, 20, 12, 24, 27, 4, 22, 51, 35, 32, 18, 8, 36, 15, 44, 57, 34, 65, 43, 11, 40, 60, 30, 58, 49, 21, 7, 42, 33, 47, 26, 59, 16, 6, 0, 46, 17, 55, 41, 52, 14, 10, 29, 62, 50, 38, 64, 61, 63, 45, 28, 39, 1, 37, 54, 48], 'cur_cost': 117875.0}, {'tour': array([26, 38, 23, 12, 49, 59,  0, 41, 21, 46, 13, 29, 58, 60, 22, 27, 47,
       16, 43, 30, 42, 35, 44,  1, 52,  3, 25, 48,  6, 65, 15, 45, 36, 63,
       33, 61, 18, 64, 14, 40, 39, 50, 19, 28,  8, 17,  9, 54, 55, 57, 51,
       53, 37,  7, 34, 31, 20, 11, 32, 24, 62,  2,  5,  4, 56, 10],
      dtype=int64), 'cur_cost': 103268.0}, {'tour': array([ 0, 50,  5, 49, 32, 22, 16, 63, 42, 62,  6, 23, 28,  7, 33,  8, 18,
       34, 26,  4, 53, 60, 27, 57, 30, 47, 14, 11, 48,  9, 12, 35, 39, 55,
       29, 31, 36, 25, 19, 13, 41, 65, 45, 52, 43, 10, 15, 64, 38, 20, 40,
       54,  3, 51, 56,  2, 37, 24, 44, 59,  1, 17, 46, 58, 61, 21],
      dtype=int64), 'cur_cost': 114816.0}, {'tour': array([45, 62, 41, 17, 49, 31, 39, 12, 50, 57, 44,  5, 47, 65, 60, 33, 14,
       34, 27, 59, 21, 15, 13, 35,  6, 43, 24, 26, 42, 52, 53, 16,  2, 11,
       18, 58, 61,  1, 20, 30, 22, 55,  9, 40,  4, 63, 29, 36, 19, 25, 38,
       64, 10, 37, 23,  8, 54, 56, 51, 48, 32, 28,  0,  3,  7, 46],
      dtype=int64), 'cur_cost': 99359.0}, {'tour': array([61, 65, 44, 47,  9, 28,  4, 14, 42, 27, 13, 53, 21, 33,  5, 26, 11,
       22, 48, 46, 43, 31,  6, 29, 56, 62, 18, 32, 15, 25, 34, 19, 41, 17,
        8, 57,  7,  3, 55, 64, 54, 10, 37, 45, 63, 58, 52, 30,  2, 40, 51,
       20, 36,  0, 59, 50, 38,  1, 39, 24, 12, 49, 35, 60, 23, 16],
      dtype=int64), 'cur_cost': 95442.0}, {'tour': array([31, 34, 21, 55, 35, 45, 64, 42, 37, 36, 16, 30, 10,  9, 60,  7, 46,
       43, 44, 19, 11,  6, 57, 20, 18, 15,  0,  1, 14, 49, 38, 52,  2, 61,
       48,  8,  4, 22, 40,  5, 28,  3, 50, 47, 53, 62, 58, 56, 24, 41, 26,
       39, 63, 54, 59, 25, 51, 32, 29, 17, 27, 33, 12, 23, 13, 65],
      dtype=int64), 'cur_cost': 88151.0}, {'tour': array([18, 10, 19, 65, 43, 37, 61,  6, 56, 36, 59, 63, 50, 52, 33,  8, 51,
       60, 30, 28, 39, 23,  1, 54,  4, 20, 32,  0,  9, 25, 35, 14, 47, 46,
       49, 38, 17, 12, 31, 15, 64, 13, 44,  5, 22, 34, 24, 42, 11, 58, 45,
       16, 41, 53, 55,  2, 26, 27, 62, 29, 57, 40, 21,  3, 48,  7],
      dtype=int64), 'cur_cost': 107601.0}, {'tour': array([25, 22, 42, 14, 11, 28, 47, 65, 58, 45, 16, 35, 17, 26,  0, 34, 50,
       44, 10, 57, 49, 51, 60, 43, 54,  3, 33, 19, 55, 61,  2,  5, 20, 36,
       40, 32, 59,  4, 56, 21, 39, 37, 46, 12,  7, 13, 48,  6,  8, 62, 30,
       41, 52, 23, 18, 27, 38, 31,  1, 64,  9, 15, 29, 24, 63, 53],
      dtype=int64), 'cur_cost': 104191.0}, {'tour': array([57, 62, 43,  6, 21, 48, 15, 25, 38, 28, 10, 27, 53, 55, 23, 50, 14,
       47,  8, 61, 64, 30, 22, 11, 58, 59, 52, 46, 63, 60, 20, 34, 42,  2,
       26, 32, 49, 37, 17,  3,  7, 35, 31, 45,  0, 41, 51, 33, 12, 54, 56,
       18,  5, 16, 29, 40, 65, 36, 13,  1,  9, 19, 44, 39, 24,  4],
      dtype=int64), 'cur_cost': 98937.0}, {'tour': array([26, 19, 46, 22, 24, 48,  2, 55, 61, 29,  6, 38, 64, 16, 63,  8, 51,
       10, 44,  5, 33, 39, 53, 56, 37, 14,  9, 49,  1, 11,  0, 59, 45, 57,
       62,  7, 34, 13, 25, 35, 23, 50, 15, 65, 20, 58, 40, 17, 31, 42, 60,
       18, 43,  3, 27, 36, 41, 54, 52,  4, 12, 32, 21, 28, 47, 30],
      dtype=int64), 'cur_cost': 108939.0}, {'tour': array([ 7, 48, 38, 39, 21,  5, 60, 61,  2, 46, 50, 58, 36, 12, 43, 20, 54,
       18, 14, 10, 33, 26, 24, 28, 34, 41, 35, 62,  8,  0, 23, 15,  6, 53,
       45, 40,  3,  9, 56, 57, 22, 55, 52, 63, 51, 31,  4, 13, 42, 25, 64,
       37, 65, 44, 30, 29, 59, 19,  1, 11, 49, 32, 27, 17, 16, 47],
      dtype=int64), 'cur_cost': 90908.0}]
2025-08-03 16:14:51,241 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 16:14:51,241 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-03 16:14:51,242 - experts.management.collaboration_manager - INFO - 个体 10 利用路径生成报告: {'new_tour': array([26, 38, 23, 12, 49, 59,  0, 41, 21, 46, 13, 29, 58, 60, 22, 27, 47,
       16, 43, 30, 42, 35, 44,  1, 52,  3, 25, 48,  6, 65, 15, 45, 36, 63,
       33, 61, 18, 64, 14, 40, 39, 50, 19, 28,  8, 17,  9, 54, 55, 57, 51,
       53, 37,  7, 34, 31, 20, 11, 32, 24, 62,  2,  5,  4, 56, 10],
      dtype=int64), 'cur_cost': 103268.0}
2025-08-03 16:14:51,242 - experts.management.collaboration_manager - INFO - 个体 10 接受新路径 (成本: 103268.00)
2025-08-03 16:14:51,242 - experts.management.collaboration_manager - INFO - 为个体 11 生成探索路径
2025-08-03 16:14:51,243 - ExplorationExpert - INFO - 开始为个体 11 生成探索路径（算法实现）
2025-08-03 16:14:51,243 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:14:51,250 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:14:51,252 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:14:51,253 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101516.0, 路径长度: 66
2025-08-03 16:14:51,254 - experts.management.collaboration_manager - INFO - 个体 11 探索路径生成报告: {'new_tour': [2, 4, 18, 11, 59, 33, 1, 19, 53, 37, 14, 26, 22, 21, 17, 54, 7, 41, 65, 42, 13, 8, 34, 50, 44, 24, 0, 38, 61, 60, 20, 9, 62, 55, 63, 23, 46, 43, 32, 30, 36, 47, 10, 51, 58, 35, 5, 52, 27, 25, 28, 49, 6, 48, 64, 3, 39, 45, 15, 16, 31, 29, 56, 40, 12, 57], 'cur_cost': 101516.0}
2025-08-03 16:14:51,254 - experts.management.collaboration_manager - INFO - 个体 11 接受新路径 (成本: 101516.00)
2025-08-03 16:14:51,254 - experts.management.collaboration_manager - INFO - 为个体 12 生成探索路径
2025-08-03 16:14:51,255 - ExplorationExpert - INFO - 开始为个体 12 生成探索路径（算法实现）
2025-08-03 16:14:51,255 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:14:51,260 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:14:51,260 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:14:51,261 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14963.0, 路径长度: 66
2025-08-03 16:14:51,262 - experts.management.collaboration_manager - INFO - 个体 12 探索路径生成报告: {'new_tour': [0, 13, 10, 19, 16, 18, 12, 22, 23, 15, 14, 20, 21, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14963.0}
2025-08-03 16:14:51,263 - experts.management.collaboration_manager - INFO - 个体 12 接受新路径 (成本: 14963.00)
2025-08-03 16:14:51,263 - experts.management.collaboration_manager - INFO - 为个体 13 生成利用路径
2025-08-03 16:14:51,263 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:14:51,264 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:14:51,265 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 13 处的路径，新成本: 86776.0
2025-08-03 16:14:51,372 - ExploitationExpert - INFO - res_population_num: 10
2025-08-03 16:14:51,372 - ExploitationExpert - INFO - res_population_costs: [9836.0, 9577.0, 9575, 9554.0, 9554, 9533, 9532, 9527, 9522, 9521]
2025-08-03 16:14:51,372 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 48, 43, 40,
       49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64, 52, 63,
       65, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64, 52, 63,
       65, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 22, 14, 15, 17, 12, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:14:51,377 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:14:51,377 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 16, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 14, 9, 11, 7, 3, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12905.0}, {'tour': array([52, 21, 28, 20, 53, 57, 47, 11, 63, 19, 41, 31, 44,  5, 54, 29, 43,
       30, 61, 49, 22, 55,  9, 33, 23, 50, 36, 32,  0, 51, 14, 27, 42, 62,
       10, 39, 40, 26,  1, 12, 48, 65, 60, 13, 64,  3, 46, 18, 45,  4,  8,
       24, 16, 56, 58, 17,  7,  6,  2, 35, 38, 34, 37, 25, 59, 15],
      dtype=int64), 'cur_cost': 107960.0}, {'tour': [64, 6, 52, 5, 61, 60, 53, 62, 23, 4, 10, 9, 11, 3, 63, 14, 25, 37, 22, 26, 33, 21, 16, 12, 7, 55, 54, 47, 40, 49, 41, 46, 51, 50, 20, 35, 28, 32, 30, 18, 29, 1, 59, 2, 65, 15, 17, 31, 27, 8, 0, 57, 56, 43, 44, 48, 45, 39, 13, 34, 19, 36, 24, 42, 38, 58], 'cur_cost': 50494.0}, {'tour': [0, 15, 13, 12, 22, 23, 16, 18, 17, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12362.0}, {'tour': array([13, 43, 23, 26, 10, 41, 32, 33, 47,  8, 34, 53, 61, 50, 39,  4,  6,
       37, 30, 45, 63,  5, 64, 55, 16, 12, 11,  3, 40, 60, 21, 49, 14, 25,
        2, 42, 65, 15, 28, 38, 57, 17, 22, 54, 56, 35,  9, 58, 29, 62, 18,
       24,  0, 52, 46, 51, 36, 20, 48, 19, 27, 59, 44, 31,  1,  7],
      dtype=int64), 'cur_cost': 108065.0}, {'tour': [0, 18, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12833.0}, {'tour': [12, 5, 16, 14, 17, 13, 11, 10, 7, 59, 54, 62, 52, 53, 40, 19, 2, 56, 47, 21, 9, 22, 33, 15, 37, 26, 27, 4, 57, 23, 0, 6, 65, 39, 42, 36, 29, 1, 25, 24, 3, 8, 34, 48, 18, 49, 50, 45, 44, 35, 28, 38, 51, 46, 30, 41, 31, 55, 58, 61, 60, 63, 20, 32, 43, 64], 'cur_cost': 71548.0}, {'tour': array([27, 40, 47,  0, 63, 60,  9, 11, 57, 14,  2, 17, 35, 58, 13, 36, 16,
       62, 12, 41, 32,  6, 48, 55, 37,  4, 38, 52, 45, 39, 19,  3, 24, 42,
       30, 43,  1, 10, 49,  7, 20, 28, 65, 31, 51, 53, 23, 44,  5, 22, 50,
       59, 56, 18, 21, 33, 34, 54, 64, 61, 46, 25, 15,  8, 29, 26],
      dtype=int64), 'cur_cost': 107771.0}, {'tour': [0, 18, 6, 17, 12, 22, 23, 16, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14698.0}, {'tour': [5, 31, 25, 53, 9, 23, 13, 19, 3, 2, 56, 20, 12, 24, 27, 4, 22, 51, 35, 32, 18, 8, 36, 15, 44, 57, 34, 65, 43, 11, 40, 60, 30, 58, 49, 21, 7, 42, 33, 47, 26, 59, 16, 6, 0, 46, 17, 55, 41, 52, 14, 10, 29, 62, 50, 38, 64, 61, 63, 45, 28, 39, 1, 37, 54, 48], 'cur_cost': 117875.0}, {'tour': array([26, 38, 23, 12, 49, 59,  0, 41, 21, 46, 13, 29, 58, 60, 22, 27, 47,
       16, 43, 30, 42, 35, 44,  1, 52,  3, 25, 48,  6, 65, 15, 45, 36, 63,
       33, 61, 18, 64, 14, 40, 39, 50, 19, 28,  8, 17,  9, 54, 55, 57, 51,
       53, 37,  7, 34, 31, 20, 11, 32, 24, 62,  2,  5,  4, 56, 10],
      dtype=int64), 'cur_cost': 103268.0}, {'tour': [2, 4, 18, 11, 59, 33, 1, 19, 53, 37, 14, 26, 22, 21, 17, 54, 7, 41, 65, 42, 13, 8, 34, 50, 44, 24, 0, 38, 61, 60, 20, 9, 62, 55, 63, 23, 46, 43, 32, 30, 36, 47, 10, 51, 58, 35, 5, 52, 27, 25, 28, 49, 6, 48, 64, 3, 39, 45, 15, 16, 31, 29, 56, 40, 12, 57], 'cur_cost': 101516.0}, {'tour': [0, 13, 10, 19, 16, 18, 12, 22, 23, 15, 14, 20, 21, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14963.0}, {'tour': array([19, 48, 40, 35, 16, 36,  8,  9, 15,  6, 10,  4, 27, 49, 18,  5, 62,
       55, 53,  3, 51, 13, 30, 26, 12, 29, 14, 58, 45, 64, 46, 41, 44, 17,
       56, 24, 20,  1, 39, 22,  2, 32, 63, 54, 60, 47, 42, 52, 59, 61, 50,
       11, 31, 34, 25,  0,  7, 57, 38, 23, 33, 37, 28, 65, 43, 21],
      dtype=int64), 'cur_cost': 86776.0}, {'tour': array([31, 34, 21, 55, 35, 45, 64, 42, 37, 36, 16, 30, 10,  9, 60,  7, 46,
       43, 44, 19, 11,  6, 57, 20, 18, 15,  0,  1, 14, 49, 38, 52,  2, 61,
       48,  8,  4, 22, 40,  5, 28,  3, 50, 47, 53, 62, 58, 56, 24, 41, 26,
       39, 63, 54, 59, 25, 51, 32, 29, 17, 27, 33, 12, 23, 13, 65],
      dtype=int64), 'cur_cost': 88151.0}, {'tour': array([18, 10, 19, 65, 43, 37, 61,  6, 56, 36, 59, 63, 50, 52, 33,  8, 51,
       60, 30, 28, 39, 23,  1, 54,  4, 20, 32,  0,  9, 25, 35, 14, 47, 46,
       49, 38, 17, 12, 31, 15, 64, 13, 44,  5, 22, 34, 24, 42, 11, 58, 45,
       16, 41, 53, 55,  2, 26, 27, 62, 29, 57, 40, 21,  3, 48,  7],
      dtype=int64), 'cur_cost': 107601.0}, {'tour': array([25, 22, 42, 14, 11, 28, 47, 65, 58, 45, 16, 35, 17, 26,  0, 34, 50,
       44, 10, 57, 49, 51, 60, 43, 54,  3, 33, 19, 55, 61,  2,  5, 20, 36,
       40, 32, 59,  4, 56, 21, 39, 37, 46, 12,  7, 13, 48,  6,  8, 62, 30,
       41, 52, 23, 18, 27, 38, 31,  1, 64,  9, 15, 29, 24, 63, 53],
      dtype=int64), 'cur_cost': 104191.0}, {'tour': array([57, 62, 43,  6, 21, 48, 15, 25, 38, 28, 10, 27, 53, 55, 23, 50, 14,
       47,  8, 61, 64, 30, 22, 11, 58, 59, 52, 46, 63, 60, 20, 34, 42,  2,
       26, 32, 49, 37, 17,  3,  7, 35, 31, 45,  0, 41, 51, 33, 12, 54, 56,
       18,  5, 16, 29, 40, 65, 36, 13,  1,  9, 19, 44, 39, 24,  4],
      dtype=int64), 'cur_cost': 98937.0}, {'tour': array([26, 19, 46, 22, 24, 48,  2, 55, 61, 29,  6, 38, 64, 16, 63,  8, 51,
       10, 44,  5, 33, 39, 53, 56, 37, 14,  9, 49,  1, 11,  0, 59, 45, 57,
       62,  7, 34, 13, 25, 35, 23, 50, 15, 65, 20, 58, 40, 17, 31, 42, 60,
       18, 43,  3, 27, 36, 41, 54, 52,  4, 12, 32, 21, 28, 47, 30],
      dtype=int64), 'cur_cost': 108939.0}, {'tour': array([ 7, 48, 38, 39, 21,  5, 60, 61,  2, 46, 50, 58, 36, 12, 43, 20, 54,
       18, 14, 10, 33, 26, 24, 28, 34, 41, 35, 62,  8,  0, 23, 15,  6, 53,
       45, 40,  3,  9, 56, 57, 22, 55, 52, 63, 51, 31,  4, 13, 42, 25, 64,
       37, 65, 44, 30, 29, 59, 19,  1, 11, 49, 32, 27, 17, 16, 47],
      dtype=int64), 'cur_cost': 90908.0}]
2025-08-03 16:14:51,391 - ExploitationExpert - INFO - 局部搜索耗时: 0.13秒
2025-08-03 16:14:51,391 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-03 16:14:51,392 - experts.management.collaboration_manager - INFO - 个体 13 利用路径生成报告: {'new_tour': array([19, 48, 40, 35, 16, 36,  8,  9, 15,  6, 10,  4, 27, 49, 18,  5, 62,
       55, 53,  3, 51, 13, 30, 26, 12, 29, 14, 58, 45, 64, 46, 41, 44, 17,
       56, 24, 20,  1, 39, 22,  2, 32, 63, 54, 60, 47, 42, 52, 59, 61, 50,
       11, 31, 34, 25,  0,  7, 57, 38, 23, 33, 37, 28, 65, 43, 21],
      dtype=int64), 'cur_cost': 86776.0}
2025-08-03 16:14:51,392 - experts.management.collaboration_manager - INFO - 个体 13 接受新路径 (成本: 86776.00)
2025-08-03 16:14:51,393 - experts.management.collaboration_manager - INFO - 为个体 14 生成探索路径
2025-08-03 16:14:51,393 - ExplorationExpert - INFO - 开始为个体 14 生成探索路径（算法实现）
2025-08-03 16:14:51,393 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:14:51,407 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:14:51,408 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:14:51,408 - ExplorationExpert - INFO - 探索路径生成完成，成本: 55001.0, 路径长度: 66
2025-08-03 16:14:51,409 - experts.management.collaboration_manager - INFO - 个体 14 探索路径生成报告: {'new_tour': [45, 13, 27, 16, 26, 14, 17, 35, 4, 7, 59, 55, 0, 54, 1, 57, 11, 9, 12, 34, 24, 15, 19, 49, 46, 25, 32, 31, 3, 2, 8, 58, 5, 10, 64, 23, 21, 43, 38, 40, 42, 36, 22, 29, 39, 44, 50, 51, 20, 30, 47, 48, 6, 61, 63, 53, 65, 56, 62, 60, 52, 41, 37, 33, 28, 18], 'cur_cost': 55001.0}
2025-08-03 16:14:51,409 - experts.management.collaboration_manager - INFO - 个体 14 接受新路径 (成本: 55001.00)
2025-08-03 16:14:51,409 - experts.management.collaboration_manager - INFO - 为个体 15 生成探索路径
2025-08-03 16:14:51,409 - ExplorationExpert - INFO - 开始为个体 15 生成探索路径（算法实现）
2025-08-03 16:14:51,410 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:14:51,418 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:14:51,420 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:14:51,421 - ExplorationExpert - INFO - 探索路径生成完成，成本: 97099.0, 路径长度: 66
2025-08-03 16:14:51,421 - experts.management.collaboration_manager - INFO - 个体 15 探索路径生成报告: {'new_tour': [52, 24, 31, 16, 10, 33, 62, 30, 32, 56, 47, 23, 12, 19, 15, 65, 1, 41, 17, 36, 29, 18, 25, 59, 35, 49, 44, 38, 40, 43, 3, 64, 5, 54, 7, 9, 11, 57, 58, 2, 42, 50, 27, 4, 28, 8, 48, 0, 46, 6, 22, 55, 20, 34, 14, 60, 61, 13, 21, 45, 26, 39, 63, 53, 51, 37], 'cur_cost': 97099.0}
2025-08-03 16:14:51,422 - experts.management.collaboration_manager - INFO - 个体 15 接受新路径 (成本: 97099.00)
2025-08-03 16:14:51,422 - experts.management.collaboration_manager - INFO - 为个体 16 生成利用路径
2025-08-03 16:14:51,423 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:14:51,423 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:14:51,424 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 16 处的路径，新成本: 94236.0
2025-08-03 16:14:51,523 - ExploitationExpert - INFO - res_population_num: 10
2025-08-03 16:14:51,524 - ExploitationExpert - INFO - res_population_costs: [9836.0, 9577.0, 9575, 9554.0, 9554, 9533, 9532, 9527, 9522, 9521]
2025-08-03 16:14:51,524 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 48, 43, 40,
       49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64, 52, 63,
       65, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64, 52, 63,
       65, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 22, 14, 15, 17, 12, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:14:51,530 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:14:51,530 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 16, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 14, 9, 11, 7, 3, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12905.0}, {'tour': array([52, 21, 28, 20, 53, 57, 47, 11, 63, 19, 41, 31, 44,  5, 54, 29, 43,
       30, 61, 49, 22, 55,  9, 33, 23, 50, 36, 32,  0, 51, 14, 27, 42, 62,
       10, 39, 40, 26,  1, 12, 48, 65, 60, 13, 64,  3, 46, 18, 45,  4,  8,
       24, 16, 56, 58, 17,  7,  6,  2, 35, 38, 34, 37, 25, 59, 15],
      dtype=int64), 'cur_cost': 107960.0}, {'tour': [64, 6, 52, 5, 61, 60, 53, 62, 23, 4, 10, 9, 11, 3, 63, 14, 25, 37, 22, 26, 33, 21, 16, 12, 7, 55, 54, 47, 40, 49, 41, 46, 51, 50, 20, 35, 28, 32, 30, 18, 29, 1, 59, 2, 65, 15, 17, 31, 27, 8, 0, 57, 56, 43, 44, 48, 45, 39, 13, 34, 19, 36, 24, 42, 38, 58], 'cur_cost': 50494.0}, {'tour': [0, 15, 13, 12, 22, 23, 16, 18, 17, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12362.0}, {'tour': array([13, 43, 23, 26, 10, 41, 32, 33, 47,  8, 34, 53, 61, 50, 39,  4,  6,
       37, 30, 45, 63,  5, 64, 55, 16, 12, 11,  3, 40, 60, 21, 49, 14, 25,
        2, 42, 65, 15, 28, 38, 57, 17, 22, 54, 56, 35,  9, 58, 29, 62, 18,
       24,  0, 52, 46, 51, 36, 20, 48, 19, 27, 59, 44, 31,  1,  7],
      dtype=int64), 'cur_cost': 108065.0}, {'tour': [0, 18, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12833.0}, {'tour': [12, 5, 16, 14, 17, 13, 11, 10, 7, 59, 54, 62, 52, 53, 40, 19, 2, 56, 47, 21, 9, 22, 33, 15, 37, 26, 27, 4, 57, 23, 0, 6, 65, 39, 42, 36, 29, 1, 25, 24, 3, 8, 34, 48, 18, 49, 50, 45, 44, 35, 28, 38, 51, 46, 30, 41, 31, 55, 58, 61, 60, 63, 20, 32, 43, 64], 'cur_cost': 71548.0}, {'tour': array([27, 40, 47,  0, 63, 60,  9, 11, 57, 14,  2, 17, 35, 58, 13, 36, 16,
       62, 12, 41, 32,  6, 48, 55, 37,  4, 38, 52, 45, 39, 19,  3, 24, 42,
       30, 43,  1, 10, 49,  7, 20, 28, 65, 31, 51, 53, 23, 44,  5, 22, 50,
       59, 56, 18, 21, 33, 34, 54, 64, 61, 46, 25, 15,  8, 29, 26],
      dtype=int64), 'cur_cost': 107771.0}, {'tour': [0, 18, 6, 17, 12, 22, 23, 16, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14698.0}, {'tour': [5, 31, 25, 53, 9, 23, 13, 19, 3, 2, 56, 20, 12, 24, 27, 4, 22, 51, 35, 32, 18, 8, 36, 15, 44, 57, 34, 65, 43, 11, 40, 60, 30, 58, 49, 21, 7, 42, 33, 47, 26, 59, 16, 6, 0, 46, 17, 55, 41, 52, 14, 10, 29, 62, 50, 38, 64, 61, 63, 45, 28, 39, 1, 37, 54, 48], 'cur_cost': 117875.0}, {'tour': array([26, 38, 23, 12, 49, 59,  0, 41, 21, 46, 13, 29, 58, 60, 22, 27, 47,
       16, 43, 30, 42, 35, 44,  1, 52,  3, 25, 48,  6, 65, 15, 45, 36, 63,
       33, 61, 18, 64, 14, 40, 39, 50, 19, 28,  8, 17,  9, 54, 55, 57, 51,
       53, 37,  7, 34, 31, 20, 11, 32, 24, 62,  2,  5,  4, 56, 10],
      dtype=int64), 'cur_cost': 103268.0}, {'tour': [2, 4, 18, 11, 59, 33, 1, 19, 53, 37, 14, 26, 22, 21, 17, 54, 7, 41, 65, 42, 13, 8, 34, 50, 44, 24, 0, 38, 61, 60, 20, 9, 62, 55, 63, 23, 46, 43, 32, 30, 36, 47, 10, 51, 58, 35, 5, 52, 27, 25, 28, 49, 6, 48, 64, 3, 39, 45, 15, 16, 31, 29, 56, 40, 12, 57], 'cur_cost': 101516.0}, {'tour': [0, 13, 10, 19, 16, 18, 12, 22, 23, 15, 14, 20, 21, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14963.0}, {'tour': array([19, 48, 40, 35, 16, 36,  8,  9, 15,  6, 10,  4, 27, 49, 18,  5, 62,
       55, 53,  3, 51, 13, 30, 26, 12, 29, 14, 58, 45, 64, 46, 41, 44, 17,
       56, 24, 20,  1, 39, 22,  2, 32, 63, 54, 60, 47, 42, 52, 59, 61, 50,
       11, 31, 34, 25,  0,  7, 57, 38, 23, 33, 37, 28, 65, 43, 21],
      dtype=int64), 'cur_cost': 86776.0}, {'tour': [45, 13, 27, 16, 26, 14, 17, 35, 4, 7, 59, 55, 0, 54, 1, 57, 11, 9, 12, 34, 24, 15, 19, 49, 46, 25, 32, 31, 3, 2, 8, 58, 5, 10, 64, 23, 21, 43, 38, 40, 42, 36, 22, 29, 39, 44, 50, 51, 20, 30, 47, 48, 6, 61, 63, 53, 65, 56, 62, 60, 52, 41, 37, 33, 28, 18], 'cur_cost': 55001.0}, {'tour': [52, 24, 31, 16, 10, 33, 62, 30, 32, 56, 47, 23, 12, 19, 15, 65, 1, 41, 17, 36, 29, 18, 25, 59, 35, 49, 44, 38, 40, 43, 3, 64, 5, 54, 7, 9, 11, 57, 58, 2, 42, 50, 27, 4, 28, 8, 48, 0, 46, 6, 22, 55, 20, 34, 14, 60, 61, 13, 21, 45, 26, 39, 63, 53, 51, 37], 'cur_cost': 97099.0}, {'tour': array([41, 27, 29, 48, 12, 49, 63, 62, 33, 37, 11, 44, 50, 57, 59, 31,  1,
       14,  3, 36, 26, 25, 13,  7, 39, 23,  2, 65,  9,  4, 52, 53,  0, 20,
       10, 47,  6, 51, 60,  8, 42, 38, 43, 32, 61, 18, 34, 19, 22, 54, 46,
       16, 35, 28, 45, 17, 24, 56, 64, 40, 15, 21, 55, 58,  5, 30],
      dtype=int64), 'cur_cost': 94236.0}, {'tour': array([57, 62, 43,  6, 21, 48, 15, 25, 38, 28, 10, 27, 53, 55, 23, 50, 14,
       47,  8, 61, 64, 30, 22, 11, 58, 59, 52, 46, 63, 60, 20, 34, 42,  2,
       26, 32, 49, 37, 17,  3,  7, 35, 31, 45,  0, 41, 51, 33, 12, 54, 56,
       18,  5, 16, 29, 40, 65, 36, 13,  1,  9, 19, 44, 39, 24,  4],
      dtype=int64), 'cur_cost': 98937.0}, {'tour': array([26, 19, 46, 22, 24, 48,  2, 55, 61, 29,  6, 38, 64, 16, 63,  8, 51,
       10, 44,  5, 33, 39, 53, 56, 37, 14,  9, 49,  1, 11,  0, 59, 45, 57,
       62,  7, 34, 13, 25, 35, 23, 50, 15, 65, 20, 58, 40, 17, 31, 42, 60,
       18, 43,  3, 27, 36, 41, 54, 52,  4, 12, 32, 21, 28, 47, 30],
      dtype=int64), 'cur_cost': 108939.0}, {'tour': array([ 7, 48, 38, 39, 21,  5, 60, 61,  2, 46, 50, 58, 36, 12, 43, 20, 54,
       18, 14, 10, 33, 26, 24, 28, 34, 41, 35, 62,  8,  0, 23, 15,  6, 53,
       45, 40,  3,  9, 56, 57, 22, 55, 52, 63, 51, 31,  4, 13, 42, 25, 64,
       37, 65, 44, 30, 29, 59, 19,  1, 11, 49, 32, 27, 17, 16, 47],
      dtype=int64), 'cur_cost': 90908.0}]
2025-08-03 16:14:51,537 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒
2025-08-03 16:14:51,538 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-03 16:14:51,538 - experts.management.collaboration_manager - INFO - 个体 16 利用路径生成报告: {'new_tour': array([41, 27, 29, 48, 12, 49, 63, 62, 33, 37, 11, 44, 50, 57, 59, 31,  1,
       14,  3, 36, 26, 25, 13,  7, 39, 23,  2, 65,  9,  4, 52, 53,  0, 20,
       10, 47,  6, 51, 60,  8, 42, 38, 43, 32, 61, 18, 34, 19, 22, 54, 46,
       16, 35, 28, 45, 17, 24, 56, 64, 40, 15, 21, 55, 58,  5, 30],
      dtype=int64), 'cur_cost': 94236.0}
2025-08-03 16:14:51,538 - experts.management.collaboration_manager - INFO - 个体 16 接受新路径 (成本: 94236.00)
2025-08-03 16:14:51,539 - experts.management.collaboration_manager - INFO - 为个体 17 生成探索路径
2025-08-03 16:14:51,539 - ExplorationExpert - INFO - 开始为个体 17 生成探索路径（算法实现）
2025-08-03 16:14:51,539 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:14:51,544 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:14:51,544 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:14:51,545 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12733.0, 路径长度: 66
2025-08-03 16:14:51,545 - experts.management.collaboration_manager - INFO - 个体 17 探索路径生成报告: {'new_tour': [0, 10, 17, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12733.0}
2025-08-03 16:14:51,545 - experts.management.collaboration_manager - INFO - 个体 17 接受新路径 (成本: 12733.00)
2025-08-03 16:14:51,546 - experts.management.collaboration_manager - INFO - 为个体 18 生成探索路径
2025-08-03 16:14:51,546 - ExplorationExpert - INFO - 开始为个体 18 生成探索路径（算法实现）
2025-08-03 16:14:51,546 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:14:51,555 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:14:51,556 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:14:51,556 - ExplorationExpert - INFO - 探索路径生成完成，成本: 100351.0, 路径长度: 66
2025-08-03 16:14:51,556 - experts.management.collaboration_manager - INFO - 个体 18 探索路径生成报告: {'new_tour': [14, 61, 18, 17, 59, 3, 11, 27, 37, 29, 22, 9, 12, 4, 46, 65, 16, 42, 24, 8, 6, 5, 28, 57, 44, 54, 52, 63, 26, 7, 55, 47, 25, 13, 40, 45, 56, 2, 64, 38, 53, 58, 35, 21, 50, 30, 48, 39, 19, 33, 34, 15, 36, 32, 43, 1, 0, 23, 51, 60, 41, 62, 49, 31, 10, 20], 'cur_cost': 100351.0}
2025-08-03 16:14:51,557 - experts.management.collaboration_manager - INFO - 个体 18 接受新路径 (成本: 100351.00)
2025-08-03 16:14:51,557 - experts.management.collaboration_manager - INFO - 为个体 19 生成利用路径
2025-08-03 16:14:51,557 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:14:51,558 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:14:51,558 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 19 处的路径，新成本: 106851.0
2025-08-03 16:14:51,643 - ExploitationExpert - INFO - res_population_num: 10
2025-08-03 16:14:51,644 - ExploitationExpert - INFO - res_population_costs: [9836.0, 9577.0, 9575, 9554.0, 9554, 9533, 9532, 9527, 9522, 9521]
2025-08-03 16:14:51,644 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 48, 43, 40,
       49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64, 52, 63,
       65, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64, 52, 63,
       65, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 22, 14, 15, 17, 12, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:14:51,654 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:14:51,655 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 16, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 14, 9, 11, 7, 3, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12905.0}, {'tour': array([52, 21, 28, 20, 53, 57, 47, 11, 63, 19, 41, 31, 44,  5, 54, 29, 43,
       30, 61, 49, 22, 55,  9, 33, 23, 50, 36, 32,  0, 51, 14, 27, 42, 62,
       10, 39, 40, 26,  1, 12, 48, 65, 60, 13, 64,  3, 46, 18, 45,  4,  8,
       24, 16, 56, 58, 17,  7,  6,  2, 35, 38, 34, 37, 25, 59, 15],
      dtype=int64), 'cur_cost': 107960.0}, {'tour': [64, 6, 52, 5, 61, 60, 53, 62, 23, 4, 10, 9, 11, 3, 63, 14, 25, 37, 22, 26, 33, 21, 16, 12, 7, 55, 54, 47, 40, 49, 41, 46, 51, 50, 20, 35, 28, 32, 30, 18, 29, 1, 59, 2, 65, 15, 17, 31, 27, 8, 0, 57, 56, 43, 44, 48, 45, 39, 13, 34, 19, 36, 24, 42, 38, 58], 'cur_cost': 50494.0}, {'tour': [0, 15, 13, 12, 22, 23, 16, 18, 17, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12362.0}, {'tour': array([13, 43, 23, 26, 10, 41, 32, 33, 47,  8, 34, 53, 61, 50, 39,  4,  6,
       37, 30, 45, 63,  5, 64, 55, 16, 12, 11,  3, 40, 60, 21, 49, 14, 25,
        2, 42, 65, 15, 28, 38, 57, 17, 22, 54, 56, 35,  9, 58, 29, 62, 18,
       24,  0, 52, 46, 51, 36, 20, 48, 19, 27, 59, 44, 31,  1,  7],
      dtype=int64), 'cur_cost': 108065.0}, {'tour': [0, 18, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12833.0}, {'tour': [12, 5, 16, 14, 17, 13, 11, 10, 7, 59, 54, 62, 52, 53, 40, 19, 2, 56, 47, 21, 9, 22, 33, 15, 37, 26, 27, 4, 57, 23, 0, 6, 65, 39, 42, 36, 29, 1, 25, 24, 3, 8, 34, 48, 18, 49, 50, 45, 44, 35, 28, 38, 51, 46, 30, 41, 31, 55, 58, 61, 60, 63, 20, 32, 43, 64], 'cur_cost': 71548.0}, {'tour': array([27, 40, 47,  0, 63, 60,  9, 11, 57, 14,  2, 17, 35, 58, 13, 36, 16,
       62, 12, 41, 32,  6, 48, 55, 37,  4, 38, 52, 45, 39, 19,  3, 24, 42,
       30, 43,  1, 10, 49,  7, 20, 28, 65, 31, 51, 53, 23, 44,  5, 22, 50,
       59, 56, 18, 21, 33, 34, 54, 64, 61, 46, 25, 15,  8, 29, 26],
      dtype=int64), 'cur_cost': 107771.0}, {'tour': [0, 18, 6, 17, 12, 22, 23, 16, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14698.0}, {'tour': [5, 31, 25, 53, 9, 23, 13, 19, 3, 2, 56, 20, 12, 24, 27, 4, 22, 51, 35, 32, 18, 8, 36, 15, 44, 57, 34, 65, 43, 11, 40, 60, 30, 58, 49, 21, 7, 42, 33, 47, 26, 59, 16, 6, 0, 46, 17, 55, 41, 52, 14, 10, 29, 62, 50, 38, 64, 61, 63, 45, 28, 39, 1, 37, 54, 48], 'cur_cost': 117875.0}, {'tour': array([26, 38, 23, 12, 49, 59,  0, 41, 21, 46, 13, 29, 58, 60, 22, 27, 47,
       16, 43, 30, 42, 35, 44,  1, 52,  3, 25, 48,  6, 65, 15, 45, 36, 63,
       33, 61, 18, 64, 14, 40, 39, 50, 19, 28,  8, 17,  9, 54, 55, 57, 51,
       53, 37,  7, 34, 31, 20, 11, 32, 24, 62,  2,  5,  4, 56, 10],
      dtype=int64), 'cur_cost': 103268.0}, {'tour': [2, 4, 18, 11, 59, 33, 1, 19, 53, 37, 14, 26, 22, 21, 17, 54, 7, 41, 65, 42, 13, 8, 34, 50, 44, 24, 0, 38, 61, 60, 20, 9, 62, 55, 63, 23, 46, 43, 32, 30, 36, 47, 10, 51, 58, 35, 5, 52, 27, 25, 28, 49, 6, 48, 64, 3, 39, 45, 15, 16, 31, 29, 56, 40, 12, 57], 'cur_cost': 101516.0}, {'tour': [0, 13, 10, 19, 16, 18, 12, 22, 23, 15, 14, 20, 21, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14963.0}, {'tour': array([19, 48, 40, 35, 16, 36,  8,  9, 15,  6, 10,  4, 27, 49, 18,  5, 62,
       55, 53,  3, 51, 13, 30, 26, 12, 29, 14, 58, 45, 64, 46, 41, 44, 17,
       56, 24, 20,  1, 39, 22,  2, 32, 63, 54, 60, 47, 42, 52, 59, 61, 50,
       11, 31, 34, 25,  0,  7, 57, 38, 23, 33, 37, 28, 65, 43, 21],
      dtype=int64), 'cur_cost': 86776.0}, {'tour': [45, 13, 27, 16, 26, 14, 17, 35, 4, 7, 59, 55, 0, 54, 1, 57, 11, 9, 12, 34, 24, 15, 19, 49, 46, 25, 32, 31, 3, 2, 8, 58, 5, 10, 64, 23, 21, 43, 38, 40, 42, 36, 22, 29, 39, 44, 50, 51, 20, 30, 47, 48, 6, 61, 63, 53, 65, 56, 62, 60, 52, 41, 37, 33, 28, 18], 'cur_cost': 55001.0}, {'tour': [52, 24, 31, 16, 10, 33, 62, 30, 32, 56, 47, 23, 12, 19, 15, 65, 1, 41, 17, 36, 29, 18, 25, 59, 35, 49, 44, 38, 40, 43, 3, 64, 5, 54, 7, 9, 11, 57, 58, 2, 42, 50, 27, 4, 28, 8, 48, 0, 46, 6, 22, 55, 20, 34, 14, 60, 61, 13, 21, 45, 26, 39, 63, 53, 51, 37], 'cur_cost': 97099.0}, {'tour': array([41, 27, 29, 48, 12, 49, 63, 62, 33, 37, 11, 44, 50, 57, 59, 31,  1,
       14,  3, 36, 26, 25, 13,  7, 39, 23,  2, 65,  9,  4, 52, 53,  0, 20,
       10, 47,  6, 51, 60,  8, 42, 38, 43, 32, 61, 18, 34, 19, 22, 54, 46,
       16, 35, 28, 45, 17, 24, 56, 64, 40, 15, 21, 55, 58,  5, 30],
      dtype=int64), 'cur_cost': 94236.0}, {'tour': [0, 10, 17, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12733.0}, {'tour': [14, 61, 18, 17, 59, 3, 11, 27, 37, 29, 22, 9, 12, 4, 46, 65, 16, 42, 24, 8, 6, 5, 28, 57, 44, 54, 52, 63, 26, 7, 55, 47, 25, 13, 40, 45, 56, 2, 64, 38, 53, 58, 35, 21, 50, 30, 48, 39, 19, 33, 34, 15, 36, 32, 43, 1, 0, 23, 51, 60, 41, 62, 49, 31, 10, 20], 'cur_cost': 100351.0}, {'tour': array([26, 21, 11, 61,  3,  7, 43, 40,  1, 60, 49, 32, 15, 52,  8, 14, 33,
       20, 29, 62, 12, 44, 46,  4, 36, 37, 16, 34, 38, 50, 17, 39, 53, 23,
       31,  0, 57, 42,  2, 65, 18, 35, 27,  9, 28, 48, 56, 19, 64, 59, 10,
       45, 63, 41, 47, 30, 58,  6, 51, 22, 13, 25, 55,  5, 24, 54],
      dtype=int64), 'cur_cost': 106851.0}]
2025-08-03 16:14:51,662 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 16:14:51,662 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-03 16:14:51,663 - experts.management.collaboration_manager - INFO - 个体 19 利用路径生成报告: {'new_tour': array([26, 21, 11, 61,  3,  7, 43, 40,  1, 60, 49, 32, 15, 52,  8, 14, 33,
       20, 29, 62, 12, 44, 46,  4, 36, 37, 16, 34, 38, 50, 17, 39, 53, 23,
       31,  0, 57, 42,  2, 65, 18, 35, 27,  9, 28, 48, 56, 19, 64, 59, 10,
       45, 63, 41, 47, 30, 58,  6, 51, 22, 13, 25, 55,  5, 24, 54],
      dtype=int64), 'cur_cost': 106851.0}
2025-08-03 16:14:51,663 - experts.management.collaboration_manager - INFO - 个体 19 接受新路径 (成本: 106851.00)
2025-08-03 16:14:51,663 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 16, 'rejected': 4, 'elite_protected': 4}
2025-08-03 16:14:51,664 - experts.management.collaboration_manager - INFO - 接受率: 16/20 (80.0%)
2025-08-03 16:14:51,666 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 16, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 14, 9, 11, 7, 3, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12905.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([52, 21, 28, 20, 53, 57, 47, 11, 63, 19, 41, 31, 44,  5, 54, 29, 43,
       30, 61, 49, 22, 55,  9, 33, 23, 50, 36, 32,  0, 51, 14, 27, 42, 62,
       10, 39, 40, 26,  1, 12, 48, 65, 60, 13, 64,  3, 46, 18, 45,  4,  8,
       24, 16, 56, 58, 17,  7,  6,  2, 35, 38, 34, 37, 25, 59, 15],
      dtype=int64), 'cur_cost': 107960.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [64, 6, 52, 5, 61, 60, 53, 62, 23, 4, 10, 9, 11, 3, 63, 14, 25, 37, 22, 26, 33, 21, 16, 12, 7, 55, 54, 47, 40, 49, 41, 46, 51, 50, 20, 35, 28, 32, 30, 18, 29, 1, 59, 2, 65, 15, 17, 31, 27, 8, 0, 57, 56, 43, 44, 48, 45, 39, 13, 34, 19, 36, 24, 42, 38, 58], 'cur_cost': 50494.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 13, 12, 22, 23, 16, 18, 17, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12362.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([13, 43, 23, 26, 10, 41, 32, 33, 47,  8, 34, 53, 61, 50, 39,  4,  6,
       37, 30, 45, 63,  5, 64, 55, 16, 12, 11,  3, 40, 60, 21, 49, 14, 25,
        2, 42, 65, 15, 28, 38, 57, 17, 22, 54, 56, 35,  9, 58, 29, 62, 18,
       24,  0, 52, 46, 51, 36, 20, 48, 19, 27, 59, 44, 31,  1,  7],
      dtype=int64), 'cur_cost': 108065.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12833.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [12, 5, 16, 14, 17, 13, 11, 10, 7, 59, 54, 62, 52, 53, 40, 19, 2, 56, 47, 21, 9, 22, 33, 15, 37, 26, 27, 4, 57, 23, 0, 6, 65, 39, 42, 36, 29, 1, 25, 24, 3, 8, 34, 48, 18, 49, 50, 45, 44, 35, 28, 38, 51, 46, 30, 41, 31, 55, 58, 61, 60, 63, 20, 32, 43, 64], 'cur_cost': 71548.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([27, 40, 47,  0, 63, 60,  9, 11, 57, 14,  2, 17, 35, 58, 13, 36, 16,
       62, 12, 41, 32,  6, 48, 55, 37,  4, 38, 52, 45, 39, 19,  3, 24, 42,
       30, 43,  1, 10, 49,  7, 20, 28, 65, 31, 51, 53, 23, 44,  5, 22, 50,
       59, 56, 18, 21, 33, 34, 54, 64, 61, 46, 25, 15,  8, 29, 26],
      dtype=int64), 'cur_cost': 107771.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 6, 17, 12, 22, 23, 16, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14698.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [5, 31, 25, 53, 9, 23, 13, 19, 3, 2, 56, 20, 12, 24, 27, 4, 22, 51, 35, 32, 18, 8, 36, 15, 44, 57, 34, 65, 43, 11, 40, 60, 30, 58, 49, 21, 7, 42, 33, 47, 26, 59, 16, 6, 0, 46, 17, 55, 41, 52, 14, 10, 29, 62, 50, 38, 64, 61, 63, 45, 28, 39, 1, 37, 54, 48], 'cur_cost': 117875.0}}, {'individual': 10, 'strategy': 'exploit', 'path_data': {'new_tour': array([26, 38, 23, 12, 49, 59,  0, 41, 21, 46, 13, 29, 58, 60, 22, 27, 47,
       16, 43, 30, 42, 35, 44,  1, 52,  3, 25, 48,  6, 65, 15, 45, 36, 63,
       33, 61, 18, 64, 14, 40, 39, 50, 19, 28,  8, 17,  9, 54, 55, 57, 51,
       53, 37,  7, 34, 31, 20, 11, 32, 24, 62,  2,  5,  4, 56, 10],
      dtype=int64), 'cur_cost': 103268.0}}, {'individual': 11, 'strategy': 'explore', 'path_data': {'new_tour': [2, 4, 18, 11, 59, 33, 1, 19, 53, 37, 14, 26, 22, 21, 17, 54, 7, 41, 65, 42, 13, 8, 34, 50, 44, 24, 0, 38, 61, 60, 20, 9, 62, 55, 63, 23, 46, 43, 32, 30, 36, 47, 10, 51, 58, 35, 5, 52, 27, 25, 28, 49, 6, 48, 64, 3, 39, 45, 15, 16, 31, 29, 56, 40, 12, 57], 'cur_cost': 101516.0}}, {'individual': 12, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 10, 19, 16, 18, 12, 22, 23, 15, 14, 20, 21, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14963.0}}, {'individual': 13, 'strategy': 'exploit', 'path_data': {'new_tour': array([19, 48, 40, 35, 16, 36,  8,  9, 15,  6, 10,  4, 27, 49, 18,  5, 62,
       55, 53,  3, 51, 13, 30, 26, 12, 29, 14, 58, 45, 64, 46, 41, 44, 17,
       56, 24, 20,  1, 39, 22,  2, 32, 63, 54, 60, 47, 42, 52, 59, 61, 50,
       11, 31, 34, 25,  0,  7, 57, 38, 23, 33, 37, 28, 65, 43, 21],
      dtype=int64), 'cur_cost': 86776.0}}, {'individual': 14, 'strategy': 'explore', 'path_data': {'new_tour': [45, 13, 27, 16, 26, 14, 17, 35, 4, 7, 59, 55, 0, 54, 1, 57, 11, 9, 12, 34, 24, 15, 19, 49, 46, 25, 32, 31, 3, 2, 8, 58, 5, 10, 64, 23, 21, 43, 38, 40, 42, 36, 22, 29, 39, 44, 50, 51, 20, 30, 47, 48, 6, 61, 63, 53, 65, 56, 62, 60, 52, 41, 37, 33, 28, 18], 'cur_cost': 55001.0}}, {'individual': 15, 'strategy': 'explore', 'path_data': {'new_tour': [52, 24, 31, 16, 10, 33, 62, 30, 32, 56, 47, 23, 12, 19, 15, 65, 1, 41, 17, 36, 29, 18, 25, 59, 35, 49, 44, 38, 40, 43, 3, 64, 5, 54, 7, 9, 11, 57, 58, 2, 42, 50, 27, 4, 28, 8, 48, 0, 46, 6, 22, 55, 20, 34, 14, 60, 61, 13, 21, 45, 26, 39, 63, 53, 51, 37], 'cur_cost': 97099.0}}, {'individual': 16, 'strategy': 'exploit', 'path_data': {'new_tour': array([41, 27, 29, 48, 12, 49, 63, 62, 33, 37, 11, 44, 50, 57, 59, 31,  1,
       14,  3, 36, 26, 25, 13,  7, 39, 23,  2, 65,  9,  4, 52, 53,  0, 20,
       10, 47,  6, 51, 60,  8, 42, 38, 43, 32, 61, 18, 34, 19, 22, 54, 46,
       16, 35, 28, 45, 17, 24, 56, 64, 40, 15, 21, 55, 58,  5, 30],
      dtype=int64), 'cur_cost': 94236.0}}, {'individual': 17, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 17, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12733.0}}, {'individual': 18, 'strategy': 'explore', 'path_data': {'new_tour': [14, 61, 18, 17, 59, 3, 11, 27, 37, 29, 22, 9, 12, 4, 46, 65, 16, 42, 24, 8, 6, 5, 28, 57, 44, 54, 52, 63, 26, 7, 55, 47, 25, 13, 40, 45, 56, 2, 64, 38, 53, 58, 35, 21, 50, 30, 48, 39, 19, 33, 34, 15, 36, 32, 43, 1, 0, 23, 51, 60, 41, 62, 49, 31, 10, 20], 'cur_cost': 100351.0}}, {'individual': 19, 'strategy': 'exploit', 'path_data': {'new_tour': array([26, 21, 11, 61,  3,  7, 43, 40,  1, 60, 49, 32, 15, 52,  8, 14, 33,
       20, 29, 62, 12, 44, 46,  4, 36, 37, 16, 34, 38, 50, 17, 39, 53, 23,
       31,  0, 57, 42,  2, 65, 18, 35, 27,  9, 28, 48, 56, 19, 64, 59, 10,
       45, 63, 41, 47, 30, 58,  6, 51, 22, 13, 25, 55,  5, 24, 54],
      dtype=int64), 'cur_cost': 106851.0}}]
2025-08-03 16:14:51,669 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-03 16:14:51,669 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:14:51,686 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=12362.000, 多样性=0.964
2025-08-03 16:14:51,687 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-03 16:14:51,689 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-03 16:14:51,689 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-03 16:14:51,690 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 2, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.06929928720526692, 'best_improvement': -0.24266184157619622}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.006328586893163502}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 10, 'new_count': 10, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.6952861952861953, 'new_diversity': 0.6952861952861953, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-03 16:14:51,691 - main - INFO - --- Finished Evolution Iteration 1 ---
2025-08-03 16:14:51,691 - main - INFO - composite13_66 开始进化第 2 代
2025-08-03 16:14:51,691 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-03 16:14:51,692 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:14:51,693 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=12362.000, 多样性=0.964
2025-08-03 16:14:51,694 - PathExpert - INFO - 开始路径结构分析
2025-08-03 16:14:51,700 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.964
2025-08-03 16:14:51,703 - EliteExpert - INFO - 开始精英解分析
2025-08-03 16:14:51,707 - EliteExpert - INFO - 精英解分析完成: 精英解数量=10, 多样性=0.695
2025-08-03 16:14:51,709 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/2)
2025-08-03 16:14:51,709 - LandscapeExpert - INFO - 使用直接传入的种群数据: 20个个体
2025-08-03 16:14:51,709 - LandscapeExpert - INFO - 添加精英解数据: 10个精英解
2025-08-03 16:14:51,709 - LandscapeExpert - INFO - 数据提取成功: 30个路径, 30个适应度值
2025-08-03 16:14:51,875 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.067, 适应度梯度: -15913.687, 聚类评分: 0.000, 覆盖率: 0.005, 收敛趋势: 0.000, 多样性: 1.000
2025-08-03 16:14:51,876 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-03 16:14:51,876 - LandscapeExpert - INFO - 提取到 10 个精英解
2025-08-03 16:14:51,891 - visualization.landscape_visualizer - INFO - 已添加 10 个精英解标记
2025-08-03 16:14:51,977 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_2_20250803_161451.html
2025-08-03 16:14:52,031 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_2_20250803_161451.html
2025-08-03 16:14:52,033 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 2
2025-08-03 16:14:52,034 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-03 16:14:52,034 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.3246秒
2025-08-03 16:14:52,035 - main - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.06666666666666667, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -15913.686666666665, 'local_optima_density': 0.06666666666666667, 'gradient_variance': 906576602.4578222, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0049, 'fitness_entropy': 0.7313773890842439, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -15913.687)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.005)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 1, 'total': 2, 'progress': 0.5}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754208891.876032, 'performance_metrics': {}}}
2025-08-03 16:14:52,036 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-03 16:14:52,037 - experts.strategy.enhanced_strategy_expert - INFO - 开始增强策略分配分析 (迭代 1)
2025-08-03 16:14:52,037 - experts.analysis.individual_state_analyzer - INFO - 开始分析种群状态 (迭代 1, 种群大小: 20)
2025-08-03 16:14:52,038 - experts.strategy.enhanced_strategy_expert - ERROR - 策略分配过程中发生错误: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:14:52,038 - experts.strategy.enhanced_strategy_expert - WARNING - 使用回退策略分配
2025-08-03 16:14:52,038 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 16:14:52,039 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:14:52,039 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-03 16:14:52,039 - main - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 16:14:52,040 - main - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:14:52,040 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-03 16:14:52,040 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 17, 3, 5} (总数: 4, 保护比例: 0.20)
2025-08-03 16:14:52,041 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-03 16:14:52,041 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-03 16:14:52,041 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:14:52,044 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:14:52,044 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:14:52,045 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14720.0, 路径长度: 66
2025-08-03 16:14:52,045 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 14, 3, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14720.0}
2025-08-03 16:14:52,046 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 14720.00)
2025-08-03 16:14:52,046 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-03 16:14:52,046 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:14:52,047 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:14:52,049 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 98106.0
2025-08-03 16:14:52,133 - ExploitationExpert - INFO - res_population_num: 10
2025-08-03 16:14:52,133 - ExploitationExpert - INFO - res_population_costs: [9521, 9522, 9527, 9532, 9533, 9554.0, 9554, 9575, 9577.0, 9836.0]
2025-08-03 16:14:52,134 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 22, 14, 15, 17, 12, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64, 52, 63,
       65, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64, 52, 63,
       65, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 48, 43, 40,
       49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:14:52,140 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:14:52,140 - ExploitationExpert - INFO - populations: [{'tour': [0, 14, 3, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14720.0}, {'tour': array([22, 29, 32, 24, 55, 48, 25, 65, 43, 46, 30, 37,  3, 44, 52, 49, 23,
       47,  8,  9,  7, 57,  5, 59, 51,  0, 31, 26, 60, 12, 17, 28, 21, 19,
       45, 39, 11, 10,  6, 36, 41, 38, 27, 58, 61, 16, 56, 15, 40,  1, 63,
       53, 50, 13, 54,  4, 42, 35, 62, 20, 14,  2, 64, 18, 33, 34],
      dtype=int64), 'cur_cost': 98106.0}, {'tour': [64, 6, 52, 5, 61, 60, 53, 62, 23, 4, 10, 9, 11, 3, 63, 14, 25, 37, 22, 26, 33, 21, 16, 12, 7, 55, 54, 47, 40, 49, 41, 46, 51, 50, 20, 35, 28, 32, 30, 18, 29, 1, 59, 2, 65, 15, 17, 31, 27, 8, 0, 57, 56, 43, 44, 48, 45, 39, 13, 34, 19, 36, 24, 42, 38, 58], 'cur_cost': 50494.0}, {'tour': [0, 15, 13, 12, 22, 23, 16, 18, 17, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12362.0}, {'tour': [13, 43, 23, 26, 10, 41, 32, 33, 47, 8, 34, 53, 61, 50, 39, 4, 6, 37, 30, 45, 63, 5, 64, 55, 16, 12, 11, 3, 40, 60, 21, 49, 14, 25, 2, 42, 65, 15, 28, 38, 57, 17, 22, 54, 56, 35, 9, 58, 29, 62, 18, 24, 0, 52, 46, 51, 36, 20, 48, 19, 27, 59, 44, 31, 1, 7], 'cur_cost': 108065.0}, {'tour': [0, 18, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12833.0}, {'tour': [12, 5, 16, 14, 17, 13, 11, 10, 7, 59, 54, 62, 52, 53, 40, 19, 2, 56, 47, 21, 9, 22, 33, 15, 37, 26, 27, 4, 57, 23, 0, 6, 65, 39, 42, 36, 29, 1, 25, 24, 3, 8, 34, 48, 18, 49, 50, 45, 44, 35, 28, 38, 51, 46, 30, 41, 31, 55, 58, 61, 60, 63, 20, 32, 43, 64], 'cur_cost': 71548.0}, {'tour': [27, 40, 47, 0, 63, 60, 9, 11, 57, 14, 2, 17, 35, 58, 13, 36, 16, 62, 12, 41, 32, 6, 48, 55, 37, 4, 38, 52, 45, 39, 19, 3, 24, 42, 30, 43, 1, 10, 49, 7, 20, 28, 65, 31, 51, 53, 23, 44, 5, 22, 50, 59, 56, 18, 21, 33, 34, 54, 64, 61, 46, 25, 15, 8, 29, 26], 'cur_cost': 107771.0}, {'tour': [0, 18, 6, 17, 12, 22, 23, 16, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14698.0}, {'tour': [5, 31, 25, 53, 9, 23, 13, 19, 3, 2, 56, 20, 12, 24, 27, 4, 22, 51, 35, 32, 18, 8, 36, 15, 44, 57, 34, 65, 43, 11, 40, 60, 30, 58, 49, 21, 7, 42, 33, 47, 26, 59, 16, 6, 0, 46, 17, 55, 41, 52, 14, 10, 29, 62, 50, 38, 64, 61, 63, 45, 28, 39, 1, 37, 54, 48], 'cur_cost': 117875.0}, {'tour': [26, 38, 23, 12, 49, 59, 0, 41, 21, 46, 13, 29, 58, 60, 22, 27, 47, 16, 43, 30, 42, 35, 44, 1, 52, 3, 25, 48, 6, 65, 15, 45, 36, 63, 33, 61, 18, 64, 14, 40, 39, 50, 19, 28, 8, 17, 9, 54, 55, 57, 51, 53, 37, 7, 34, 31, 20, 11, 32, 24, 62, 2, 5, 4, 56, 10], 'cur_cost': 103268.0}, {'tour': [2, 4, 18, 11, 59, 33, 1, 19, 53, 37, 14, 26, 22, 21, 17, 54, 7, 41, 65, 42, 13, 8, 34, 50, 44, 24, 0, 38, 61, 60, 20, 9, 62, 55, 63, 23, 46, 43, 32, 30, 36, 47, 10, 51, 58, 35, 5, 52, 27, 25, 28, 49, 6, 48, 64, 3, 39, 45, 15, 16, 31, 29, 56, 40, 12, 57], 'cur_cost': 101516.0}, {'tour': [0, 13, 10, 19, 16, 18, 12, 22, 23, 15, 14, 20, 21, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14963.0}, {'tour': [19, 48, 40, 35, 16, 36, 8, 9, 15, 6, 10, 4, 27, 49, 18, 5, 62, 55, 53, 3, 51, 13, 30, 26, 12, 29, 14, 58, 45, 64, 46, 41, 44, 17, 56, 24, 20, 1, 39, 22, 2, 32, 63, 54, 60, 47, 42, 52, 59, 61, 50, 11, 31, 34, 25, 0, 7, 57, 38, 23, 33, 37, 28, 65, 43, 21], 'cur_cost': 86776.0}, {'tour': [45, 13, 27, 16, 26, 14, 17, 35, 4, 7, 59, 55, 0, 54, 1, 57, 11, 9, 12, 34, 24, 15, 19, 49, 46, 25, 32, 31, 3, 2, 8, 58, 5, 10, 64, 23, 21, 43, 38, 40, 42, 36, 22, 29, 39, 44, 50, 51, 20, 30, 47, 48, 6, 61, 63, 53, 65, 56, 62, 60, 52, 41, 37, 33, 28, 18], 'cur_cost': 55001.0}, {'tour': [52, 24, 31, 16, 10, 33, 62, 30, 32, 56, 47, 23, 12, 19, 15, 65, 1, 41, 17, 36, 29, 18, 25, 59, 35, 49, 44, 38, 40, 43, 3, 64, 5, 54, 7, 9, 11, 57, 58, 2, 42, 50, 27, 4, 28, 8, 48, 0, 46, 6, 22, 55, 20, 34, 14, 60, 61, 13, 21, 45, 26, 39, 63, 53, 51, 37], 'cur_cost': 97099.0}, {'tour': [41, 27, 29, 48, 12, 49, 63, 62, 33, 37, 11, 44, 50, 57, 59, 31, 1, 14, 3, 36, 26, 25, 13, 7, 39, 23, 2, 65, 9, 4, 52, 53, 0, 20, 10, 47, 6, 51, 60, 8, 42, 38, 43, 32, 61, 18, 34, 19, 22, 54, 46, 16, 35, 28, 45, 17, 24, 56, 64, 40, 15, 21, 55, 58, 5, 30], 'cur_cost': 94236.0}, {'tour': [0, 10, 17, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12733.0}, {'tour': [14, 61, 18, 17, 59, 3, 11, 27, 37, 29, 22, 9, 12, 4, 46, 65, 16, 42, 24, 8, 6, 5, 28, 57, 44, 54, 52, 63, 26, 7, 55, 47, 25, 13, 40, 45, 56, 2, 64, 38, 53, 58, 35, 21, 50, 30, 48, 39, 19, 33, 34, 15, 36, 32, 43, 1, 0, 23, 51, 60, 41, 62, 49, 31, 10, 20], 'cur_cost': 100351.0}, {'tour': [26, 21, 11, 61, 3, 7, 43, 40, 1, 60, 49, 32, 15, 52, 8, 14, 33, 20, 29, 62, 12, 44, 46, 4, 36, 37, 16, 34, 38, 50, 17, 39, 53, 23, 31, 0, 57, 42, 2, 65, 18, 35, 27, 9, 28, 48, 56, 19, 64, 59, 10, 45, 63, 41, 47, 30, 58, 6, 51, 22, 13, 25, 55, 5, 24, 54], 'cur_cost': 106851.0}]
2025-08-03 16:14:52,144 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 16:14:52,144 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-08-03 16:14:52,144 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([22, 29, 32, 24, 55, 48, 25, 65, 43, 46, 30, 37,  3, 44, 52, 49, 23,
       47,  8,  9,  7, 57,  5, 59, 51,  0, 31, 26, 60, 12, 17, 28, 21, 19,
       45, 39, 11, 10,  6, 36, 41, 38, 27, 58, 61, 16, 56, 15, 40,  1, 63,
       53, 50, 13, 54,  4, 42, 35, 62, 20, 14,  2, 64, 18, 33, 34],
      dtype=int64), 'cur_cost': 98106.0}
2025-08-03 16:14:52,145 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 98106.00)
2025-08-03 16:14:52,145 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-03 16:14:52,145 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-03 16:14:52,145 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:14:52,161 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:14:52,161 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:14:52,162 - ExplorationExpert - INFO - 探索路径生成完成，成本: 66008.0, 路径长度: 66
2025-08-03 16:14:52,162 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [14, 17, 20, 25, 19, 32, 26, 5, 65, 7, 8, 11, 23, 12, 10, 58, 0, 3, 2, 21, 36, 24, 4, 27, 6, 54, 56, 52, 22, 37, 43, 51, 49, 50, 34, 40, 15, 29, 18, 9, 1, 16, 44, 39, 30, 48, 13, 28, 45, 35, 46, 55, 61, 53, 62, 64, 59, 63, 41, 38, 42, 47, 31, 33, 60, 57], 'cur_cost': 66008.0}
2025-08-03 16:14:52,162 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 66008.00)
2025-08-03 16:14:52,162 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-03 16:14:52,163 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-03 16:14:52,163 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:14:52,167 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:14:52,167 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:14:52,167 - ExplorationExpert - INFO - 探索路径生成完成，成本: 95006.0, 路径长度: 66
2025-08-03 16:14:52,168 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [12, 5, 19, 21, 6, 10, 65, 2, 34, 13, 58, 22, 56, 30, 28, 36, 4, 31, 26, 27, 48, 43, 23, 7, 1, 14, 25, 42, 15, 29, 49, 47, 16, 63, 9, 0, 53, 17, 45, 62, 18, 38, 52, 64, 59, 20, 61, 60, 8, 55, 33, 46, 11, 35, 44, 40, 32, 3, 24, 57, 41, 50, 37, 39, 54, 51], 'cur_cost': 95006.0}
2025-08-03 16:14:52,168 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 95006.00)
2025-08-03 16:14:52,168 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-03 16:14:52,168 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:14:52,169 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:14:52,169 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 106222.0
2025-08-03 16:14:52,266 - ExploitationExpert - INFO - res_population_num: 11
2025-08-03 16:14:52,267 - ExploitationExpert - INFO - res_population_costs: [9521, 9522, 9527, 9532, 9533, 9554.0, 9554, 9575, 9577.0, 9836.0, 9521]
2025-08-03 16:14:52,267 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 22, 14, 15, 17, 12, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64, 52, 63,
       65, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64, 52, 63,
       65, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 48, 43, 40,
       49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:14:52,274 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:14:52,275 - ExploitationExpert - INFO - populations: [{'tour': [0, 14, 3, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14720.0}, {'tour': array([22, 29, 32, 24, 55, 48, 25, 65, 43, 46, 30, 37,  3, 44, 52, 49, 23,
       47,  8,  9,  7, 57,  5, 59, 51,  0, 31, 26, 60, 12, 17, 28, 21, 19,
       45, 39, 11, 10,  6, 36, 41, 38, 27, 58, 61, 16, 56, 15, 40,  1, 63,
       53, 50, 13, 54,  4, 42, 35, 62, 20, 14,  2, 64, 18, 33, 34],
      dtype=int64), 'cur_cost': 98106.0}, {'tour': [14, 17, 20, 25, 19, 32, 26, 5, 65, 7, 8, 11, 23, 12, 10, 58, 0, 3, 2, 21, 36, 24, 4, 27, 6, 54, 56, 52, 22, 37, 43, 51, 49, 50, 34, 40, 15, 29, 18, 9, 1, 16, 44, 39, 30, 48, 13, 28, 45, 35, 46, 55, 61, 53, 62, 64, 59, 63, 41, 38, 42, 47, 31, 33, 60, 57], 'cur_cost': 66008.0}, {'tour': [12, 5, 19, 21, 6, 10, 65, 2, 34, 13, 58, 22, 56, 30, 28, 36, 4, 31, 26, 27, 48, 43, 23, 7, 1, 14, 25, 42, 15, 29, 49, 47, 16, 63, 9, 0, 53, 17, 45, 62, 18, 38, 52, 64, 59, 20, 61, 60, 8, 55, 33, 46, 11, 35, 44, 40, 32, 3, 24, 57, 41, 50, 37, 39, 54, 51], 'cur_cost': 95006.0}, {'tour': array([ 1, 50, 38, 52, 53, 18, 28, 15, 29,  7, 19, 16, 48, 30, 20,  4, 59,
        5, 45, 17, 12, 21,  3, 22, 23, 41, 31,  2, 42, 35,  6, 54, 55, 60,
       51, 14,  8, 46, 64, 32, 43, 33, 39, 37, 61, 65, 57, 24, 58, 25, 40,
       13, 34, 62, 56, 27, 44, 36,  0, 63, 47, 11,  9, 10, 26, 49],
      dtype=int64), 'cur_cost': 106222.0}, {'tour': [0, 18, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12833.0}, {'tour': [12, 5, 16, 14, 17, 13, 11, 10, 7, 59, 54, 62, 52, 53, 40, 19, 2, 56, 47, 21, 9, 22, 33, 15, 37, 26, 27, 4, 57, 23, 0, 6, 65, 39, 42, 36, 29, 1, 25, 24, 3, 8, 34, 48, 18, 49, 50, 45, 44, 35, 28, 38, 51, 46, 30, 41, 31, 55, 58, 61, 60, 63, 20, 32, 43, 64], 'cur_cost': 71548.0}, {'tour': [27, 40, 47, 0, 63, 60, 9, 11, 57, 14, 2, 17, 35, 58, 13, 36, 16, 62, 12, 41, 32, 6, 48, 55, 37, 4, 38, 52, 45, 39, 19, 3, 24, 42, 30, 43, 1, 10, 49, 7, 20, 28, 65, 31, 51, 53, 23, 44, 5, 22, 50, 59, 56, 18, 21, 33, 34, 54, 64, 61, 46, 25, 15, 8, 29, 26], 'cur_cost': 107771.0}, {'tour': [0, 18, 6, 17, 12, 22, 23, 16, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14698.0}, {'tour': [5, 31, 25, 53, 9, 23, 13, 19, 3, 2, 56, 20, 12, 24, 27, 4, 22, 51, 35, 32, 18, 8, 36, 15, 44, 57, 34, 65, 43, 11, 40, 60, 30, 58, 49, 21, 7, 42, 33, 47, 26, 59, 16, 6, 0, 46, 17, 55, 41, 52, 14, 10, 29, 62, 50, 38, 64, 61, 63, 45, 28, 39, 1, 37, 54, 48], 'cur_cost': 117875.0}, {'tour': [26, 38, 23, 12, 49, 59, 0, 41, 21, 46, 13, 29, 58, 60, 22, 27, 47, 16, 43, 30, 42, 35, 44, 1, 52, 3, 25, 48, 6, 65, 15, 45, 36, 63, 33, 61, 18, 64, 14, 40, 39, 50, 19, 28, 8, 17, 9, 54, 55, 57, 51, 53, 37, 7, 34, 31, 20, 11, 32, 24, 62, 2, 5, 4, 56, 10], 'cur_cost': 103268.0}, {'tour': [2, 4, 18, 11, 59, 33, 1, 19, 53, 37, 14, 26, 22, 21, 17, 54, 7, 41, 65, 42, 13, 8, 34, 50, 44, 24, 0, 38, 61, 60, 20, 9, 62, 55, 63, 23, 46, 43, 32, 30, 36, 47, 10, 51, 58, 35, 5, 52, 27, 25, 28, 49, 6, 48, 64, 3, 39, 45, 15, 16, 31, 29, 56, 40, 12, 57], 'cur_cost': 101516.0}, {'tour': [0, 13, 10, 19, 16, 18, 12, 22, 23, 15, 14, 20, 21, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14963.0}, {'tour': [19, 48, 40, 35, 16, 36, 8, 9, 15, 6, 10, 4, 27, 49, 18, 5, 62, 55, 53, 3, 51, 13, 30, 26, 12, 29, 14, 58, 45, 64, 46, 41, 44, 17, 56, 24, 20, 1, 39, 22, 2, 32, 63, 54, 60, 47, 42, 52, 59, 61, 50, 11, 31, 34, 25, 0, 7, 57, 38, 23, 33, 37, 28, 65, 43, 21], 'cur_cost': 86776.0}, {'tour': [45, 13, 27, 16, 26, 14, 17, 35, 4, 7, 59, 55, 0, 54, 1, 57, 11, 9, 12, 34, 24, 15, 19, 49, 46, 25, 32, 31, 3, 2, 8, 58, 5, 10, 64, 23, 21, 43, 38, 40, 42, 36, 22, 29, 39, 44, 50, 51, 20, 30, 47, 48, 6, 61, 63, 53, 65, 56, 62, 60, 52, 41, 37, 33, 28, 18], 'cur_cost': 55001.0}, {'tour': [52, 24, 31, 16, 10, 33, 62, 30, 32, 56, 47, 23, 12, 19, 15, 65, 1, 41, 17, 36, 29, 18, 25, 59, 35, 49, 44, 38, 40, 43, 3, 64, 5, 54, 7, 9, 11, 57, 58, 2, 42, 50, 27, 4, 28, 8, 48, 0, 46, 6, 22, 55, 20, 34, 14, 60, 61, 13, 21, 45, 26, 39, 63, 53, 51, 37], 'cur_cost': 97099.0}, {'tour': [41, 27, 29, 48, 12, 49, 63, 62, 33, 37, 11, 44, 50, 57, 59, 31, 1, 14, 3, 36, 26, 25, 13, 7, 39, 23, 2, 65, 9, 4, 52, 53, 0, 20, 10, 47, 6, 51, 60, 8, 42, 38, 43, 32, 61, 18, 34, 19, 22, 54, 46, 16, 35, 28, 45, 17, 24, 56, 64, 40, 15, 21, 55, 58, 5, 30], 'cur_cost': 94236.0}, {'tour': [0, 10, 17, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12733.0}, {'tour': [14, 61, 18, 17, 59, 3, 11, 27, 37, 29, 22, 9, 12, 4, 46, 65, 16, 42, 24, 8, 6, 5, 28, 57, 44, 54, 52, 63, 26, 7, 55, 47, 25, 13, 40, 45, 56, 2, 64, 38, 53, 58, 35, 21, 50, 30, 48, 39, 19, 33, 34, 15, 36, 32, 43, 1, 0, 23, 51, 60, 41, 62, 49, 31, 10, 20], 'cur_cost': 100351.0}, {'tour': [26, 21, 11, 61, 3, 7, 43, 40, 1, 60, 49, 32, 15, 52, 8, 14, 33, 20, 29, 62, 12, 44, 46, 4, 36, 37, 16, 34, 38, 50, 17, 39, 53, 23, 31, 0, 57, 42, 2, 65, 18, 35, 27, 9, 28, 48, 56, 19, 64, 59, 10, 45, 63, 41, 47, 30, 58, 6, 51, 22, 13, 25, 55, 5, 24, 54], 'cur_cost': 106851.0}]
2025-08-03 16:14:52,278 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒
2025-08-03 16:14:52,279 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-08-03 16:14:52,279 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([ 1, 50, 38, 52, 53, 18, 28, 15, 29,  7, 19, 16, 48, 30, 20,  4, 59,
        5, 45, 17, 12, 21,  3, 22, 23, 41, 31,  2, 42, 35,  6, 54, 55, 60,
       51, 14,  8, 46, 64, 32, 43, 33, 39, 37, 61, 65, 57, 24, 58, 25, 40,
       13, 34, 62, 56, 27, 44, 36,  0, 63, 47, 11,  9, 10, 26, 49],
      dtype=int64), 'cur_cost': 106222.0}
2025-08-03 16:14:52,280 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 106222.00)
2025-08-03 16:14:52,281 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-03 16:14:52,286 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-03 16:14:52,287 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:14:52,293 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:14:52,294 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:14:52,295 - ExplorationExpert - INFO - 探索路径生成完成，成本: 118010.0, 路径长度: 66
2025-08-03 16:14:52,296 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [27, 20, 2, 26, 11, 14, 56, 10, 54, 31, 41, 36, 4, 1, 6, 57, 34, 42, 37, 43, 65, 50, 8, 40, 53, 17, 49, 3, 52, 59, 46, 13, 51, 5, 22, 38, 61, 35, 62, 64, 39, 30, 47, 7, 25, 12, 48, 60, 18, 9, 0, 29, 55, 15, 24, 45, 58, 19, 23, 21, 32, 33, 16, 28, 63, 44], 'cur_cost': 118010.0}
2025-08-03 16:14:52,296 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 118010.00)
2025-08-03 16:14:52,297 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-03 16:14:52,297 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-03 16:14:52,297 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:14:52,309 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:14:52,310 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:14:52,311 - ExplorationExpert - INFO - 探索路径生成完成，成本: 56071.0, 路径长度: 66
2025-08-03 16:14:52,311 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [33, 26, 9, 7, 14, 12, 16, 30, 17, 0, 5, 3, 60, 62, 15, 6, 10, 19, 25, 18, 29, 34, 35, 28, 40, 43, 51, 21, 13, 22, 32, 27, 46, 42, 31, 2, 55, 8, 52, 59, 65, 58, 11, 54, 23, 36, 37, 49, 39, 48, 24, 44, 50, 47, 41, 4, 53, 61, 63, 56, 57, 1, 20, 45, 38, 64], 'cur_cost': 56071.0}
2025-08-03 16:14:52,312 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 56071.00)
2025-08-03 16:14:52,312 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-03 16:14:52,312 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:14:52,313 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:14:52,315 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 111815.0
2025-08-03 16:14:52,429 - ExploitationExpert - INFO - res_population_num: 11
2025-08-03 16:14:52,430 - ExploitationExpert - INFO - res_population_costs: [9521, 9522, 9527, 9532, 9533, 9554.0, 9554, 9575, 9577.0, 9836.0, 9521]
2025-08-03 16:14:52,430 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 22, 14, 15, 17, 12, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64, 52, 63,
       65, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64, 52, 63,
       65, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 48, 43, 40,
       49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:14:52,438 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:14:52,438 - ExploitationExpert - INFO - populations: [{'tour': [0, 14, 3, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14720.0}, {'tour': array([22, 29, 32, 24, 55, 48, 25, 65, 43, 46, 30, 37,  3, 44, 52, 49, 23,
       47,  8,  9,  7, 57,  5, 59, 51,  0, 31, 26, 60, 12, 17, 28, 21, 19,
       45, 39, 11, 10,  6, 36, 41, 38, 27, 58, 61, 16, 56, 15, 40,  1, 63,
       53, 50, 13, 54,  4, 42, 35, 62, 20, 14,  2, 64, 18, 33, 34],
      dtype=int64), 'cur_cost': 98106.0}, {'tour': [14, 17, 20, 25, 19, 32, 26, 5, 65, 7, 8, 11, 23, 12, 10, 58, 0, 3, 2, 21, 36, 24, 4, 27, 6, 54, 56, 52, 22, 37, 43, 51, 49, 50, 34, 40, 15, 29, 18, 9, 1, 16, 44, 39, 30, 48, 13, 28, 45, 35, 46, 55, 61, 53, 62, 64, 59, 63, 41, 38, 42, 47, 31, 33, 60, 57], 'cur_cost': 66008.0}, {'tour': [12, 5, 19, 21, 6, 10, 65, 2, 34, 13, 58, 22, 56, 30, 28, 36, 4, 31, 26, 27, 48, 43, 23, 7, 1, 14, 25, 42, 15, 29, 49, 47, 16, 63, 9, 0, 53, 17, 45, 62, 18, 38, 52, 64, 59, 20, 61, 60, 8, 55, 33, 46, 11, 35, 44, 40, 32, 3, 24, 57, 41, 50, 37, 39, 54, 51], 'cur_cost': 95006.0}, {'tour': array([ 1, 50, 38, 52, 53, 18, 28, 15, 29,  7, 19, 16, 48, 30, 20,  4, 59,
        5, 45, 17, 12, 21,  3, 22, 23, 41, 31,  2, 42, 35,  6, 54, 55, 60,
       51, 14,  8, 46, 64, 32, 43, 33, 39, 37, 61, 65, 57, 24, 58, 25, 40,
       13, 34, 62, 56, 27, 44, 36,  0, 63, 47, 11,  9, 10, 26, 49],
      dtype=int64), 'cur_cost': 106222.0}, {'tour': [27, 20, 2, 26, 11, 14, 56, 10, 54, 31, 41, 36, 4, 1, 6, 57, 34, 42, 37, 43, 65, 50, 8, 40, 53, 17, 49, 3, 52, 59, 46, 13, 51, 5, 22, 38, 61, 35, 62, 64, 39, 30, 47, 7, 25, 12, 48, 60, 18, 9, 0, 29, 55, 15, 24, 45, 58, 19, 23, 21, 32, 33, 16, 28, 63, 44], 'cur_cost': 118010.0}, {'tour': [33, 26, 9, 7, 14, 12, 16, 30, 17, 0, 5, 3, 60, 62, 15, 6, 10, 19, 25, 18, 29, 34, 35, 28, 40, 43, 51, 21, 13, 22, 32, 27, 46, 42, 31, 2, 55, 8, 52, 59, 65, 58, 11, 54, 23, 36, 37, 49, 39, 48, 24, 44, 50, 47, 41, 4, 53, 61, 63, 56, 57, 1, 20, 45, 38, 64], 'cur_cost': 56071.0}, {'tour': array([29, 52, 43, 57, 10,  8,  9, 31, 15, 16,  6, 18, 40, 23, 51, 49, 56,
       59, 22, 63, 26, 54,  4, 32, 64, 65, 44, 36, 48, 37,  7, 14, 46, 13,
       61, 25, 17,  3, 27, 28, 33, 39, 19, 55, 35, 41, 20, 62, 45, 60, 12,
       30,  0, 42, 58, 53, 34,  1, 21, 11,  2, 47, 50,  5, 24, 38],
      dtype=int64), 'cur_cost': 111815.0}, {'tour': [0, 18, 6, 17, 12, 22, 23, 16, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14698.0}, {'tour': [5, 31, 25, 53, 9, 23, 13, 19, 3, 2, 56, 20, 12, 24, 27, 4, 22, 51, 35, 32, 18, 8, 36, 15, 44, 57, 34, 65, 43, 11, 40, 60, 30, 58, 49, 21, 7, 42, 33, 47, 26, 59, 16, 6, 0, 46, 17, 55, 41, 52, 14, 10, 29, 62, 50, 38, 64, 61, 63, 45, 28, 39, 1, 37, 54, 48], 'cur_cost': 117875.0}, {'tour': [26, 38, 23, 12, 49, 59, 0, 41, 21, 46, 13, 29, 58, 60, 22, 27, 47, 16, 43, 30, 42, 35, 44, 1, 52, 3, 25, 48, 6, 65, 15, 45, 36, 63, 33, 61, 18, 64, 14, 40, 39, 50, 19, 28, 8, 17, 9, 54, 55, 57, 51, 53, 37, 7, 34, 31, 20, 11, 32, 24, 62, 2, 5, 4, 56, 10], 'cur_cost': 103268.0}, {'tour': [2, 4, 18, 11, 59, 33, 1, 19, 53, 37, 14, 26, 22, 21, 17, 54, 7, 41, 65, 42, 13, 8, 34, 50, 44, 24, 0, 38, 61, 60, 20, 9, 62, 55, 63, 23, 46, 43, 32, 30, 36, 47, 10, 51, 58, 35, 5, 52, 27, 25, 28, 49, 6, 48, 64, 3, 39, 45, 15, 16, 31, 29, 56, 40, 12, 57], 'cur_cost': 101516.0}, {'tour': [0, 13, 10, 19, 16, 18, 12, 22, 23, 15, 14, 20, 21, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14963.0}, {'tour': [19, 48, 40, 35, 16, 36, 8, 9, 15, 6, 10, 4, 27, 49, 18, 5, 62, 55, 53, 3, 51, 13, 30, 26, 12, 29, 14, 58, 45, 64, 46, 41, 44, 17, 56, 24, 20, 1, 39, 22, 2, 32, 63, 54, 60, 47, 42, 52, 59, 61, 50, 11, 31, 34, 25, 0, 7, 57, 38, 23, 33, 37, 28, 65, 43, 21], 'cur_cost': 86776.0}, {'tour': [45, 13, 27, 16, 26, 14, 17, 35, 4, 7, 59, 55, 0, 54, 1, 57, 11, 9, 12, 34, 24, 15, 19, 49, 46, 25, 32, 31, 3, 2, 8, 58, 5, 10, 64, 23, 21, 43, 38, 40, 42, 36, 22, 29, 39, 44, 50, 51, 20, 30, 47, 48, 6, 61, 63, 53, 65, 56, 62, 60, 52, 41, 37, 33, 28, 18], 'cur_cost': 55001.0}, {'tour': [52, 24, 31, 16, 10, 33, 62, 30, 32, 56, 47, 23, 12, 19, 15, 65, 1, 41, 17, 36, 29, 18, 25, 59, 35, 49, 44, 38, 40, 43, 3, 64, 5, 54, 7, 9, 11, 57, 58, 2, 42, 50, 27, 4, 28, 8, 48, 0, 46, 6, 22, 55, 20, 34, 14, 60, 61, 13, 21, 45, 26, 39, 63, 53, 51, 37], 'cur_cost': 97099.0}, {'tour': [41, 27, 29, 48, 12, 49, 63, 62, 33, 37, 11, 44, 50, 57, 59, 31, 1, 14, 3, 36, 26, 25, 13, 7, 39, 23, 2, 65, 9, 4, 52, 53, 0, 20, 10, 47, 6, 51, 60, 8, 42, 38, 43, 32, 61, 18, 34, 19, 22, 54, 46, 16, 35, 28, 45, 17, 24, 56, 64, 40, 15, 21, 55, 58, 5, 30], 'cur_cost': 94236.0}, {'tour': [0, 10, 17, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12733.0}, {'tour': [14, 61, 18, 17, 59, 3, 11, 27, 37, 29, 22, 9, 12, 4, 46, 65, 16, 42, 24, 8, 6, 5, 28, 57, 44, 54, 52, 63, 26, 7, 55, 47, 25, 13, 40, 45, 56, 2, 64, 38, 53, 58, 35, 21, 50, 30, 48, 39, 19, 33, 34, 15, 36, 32, 43, 1, 0, 23, 51, 60, 41, 62, 49, 31, 10, 20], 'cur_cost': 100351.0}, {'tour': [26, 21, 11, 61, 3, 7, 43, 40, 1, 60, 49, 32, 15, 52, 8, 14, 33, 20, 29, 62, 12, 44, 46, 4, 36, 37, 16, 34, 38, 50, 17, 39, 53, 23, 31, 0, 57, 42, 2, 65, 18, 35, 27, 9, 28, 48, 56, 19, 64, 59, 10, 45, 63, 41, 47, 30, 58, 6, 51, 22, 13, 25, 55, 5, 24, 54], 'cur_cost': 106851.0}]
2025-08-03 16:14:52,443 - ExploitationExpert - INFO - 局部搜索耗时: 0.13秒
2025-08-03 16:14:52,443 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-08-03 16:14:52,443 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([29, 52, 43, 57, 10,  8,  9, 31, 15, 16,  6, 18, 40, 23, 51, 49, 56,
       59, 22, 63, 26, 54,  4, 32, 64, 65, 44, 36, 48, 37,  7, 14, 46, 13,
       61, 25, 17,  3, 27, 28, 33, 39, 19, 55, 35, 41, 20, 62, 45, 60, 12,
       30,  0, 42, 58, 53, 34,  1, 21, 11,  2, 47, 50,  5, 24, 38],
      dtype=int64), 'cur_cost': 111815.0}
2025-08-03 16:14:52,444 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 111815.00)
2025-08-03 16:14:52,444 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-03 16:14:52,444 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-03 16:14:52,444 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:14:52,450 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:14:52,453 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:14:52,455 - ExplorationExpert - INFO - 探索路径生成完成，成本: 108849.0, 路径长度: 66
2025-08-03 16:14:52,456 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [33, 20, 3, 21, 6, 32, 19, 5, 18, 31, 41, 22, 62, 27, 16, 37, 65, 50, 35, 15, 1, 14, 56, 29, 43, 60, 58, 38, 52, 2, 30, 64, 25, 12, 13, 28, 57, 44, 61, 53, 0, 11, 24, 23, 47, 49, 7, 4, 17, 8, 45, 39, 54, 9, 55, 34, 10, 63, 48, 26, 36, 46, 51, 59, 40, 42], 'cur_cost': 108849.0}
2025-08-03 16:14:52,456 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 108849.00)
2025-08-03 16:14:52,457 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-03 16:14:52,457 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-03 16:14:52,458 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:14:52,461 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:14:52,461 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:14:52,462 - ExplorationExpert - INFO - 探索路径生成完成，成本: 109965.0, 路径长度: 66
2025-08-03 16:14:52,462 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [27, 20, 25, 11, 65, 56, 1, 28, 54, 4, 36, 24, 23, 40, 53, 49, 42, 13, 29, 15, 51, 12, 22, 52, 64, 44, 30, 61, 18, 35, 10, 47, 55, 3, 59, 19, 32, 38, 57, 26, 7, 60, 17, 9, 14, 5, 39, 34, 21, 58, 37, 2, 31, 33, 48, 8, 46, 41, 16, 0, 45, 6, 50, 43, 63, 62], 'cur_cost': 109965.0}
2025-08-03 16:14:52,463 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 109965.00)
2025-08-03 16:14:52,463 - experts.management.collaboration_manager - INFO - 为个体 10 生成利用路径
2025-08-03 16:14:52,464 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:14:52,464 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:14:52,465 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 10 处的路径，新成本: 112334.0
2025-08-03 16:14:52,588 - ExploitationExpert - INFO - res_population_num: 11
2025-08-03 16:14:52,589 - ExploitationExpert - INFO - res_population_costs: [9521, 9522, 9527, 9532, 9533, 9554.0, 9554, 9575, 9577.0, 9836.0, 9521]
2025-08-03 16:14:52,589 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 22, 14, 15, 17, 12, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64, 52, 63,
       65, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64, 52, 63,
       65, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 48, 43, 40,
       49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:14:52,598 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:14:52,600 - ExploitationExpert - INFO - populations: [{'tour': [0, 14, 3, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14720.0}, {'tour': array([22, 29, 32, 24, 55, 48, 25, 65, 43, 46, 30, 37,  3, 44, 52, 49, 23,
       47,  8,  9,  7, 57,  5, 59, 51,  0, 31, 26, 60, 12, 17, 28, 21, 19,
       45, 39, 11, 10,  6, 36, 41, 38, 27, 58, 61, 16, 56, 15, 40,  1, 63,
       53, 50, 13, 54,  4, 42, 35, 62, 20, 14,  2, 64, 18, 33, 34],
      dtype=int64), 'cur_cost': 98106.0}, {'tour': [14, 17, 20, 25, 19, 32, 26, 5, 65, 7, 8, 11, 23, 12, 10, 58, 0, 3, 2, 21, 36, 24, 4, 27, 6, 54, 56, 52, 22, 37, 43, 51, 49, 50, 34, 40, 15, 29, 18, 9, 1, 16, 44, 39, 30, 48, 13, 28, 45, 35, 46, 55, 61, 53, 62, 64, 59, 63, 41, 38, 42, 47, 31, 33, 60, 57], 'cur_cost': 66008.0}, {'tour': [12, 5, 19, 21, 6, 10, 65, 2, 34, 13, 58, 22, 56, 30, 28, 36, 4, 31, 26, 27, 48, 43, 23, 7, 1, 14, 25, 42, 15, 29, 49, 47, 16, 63, 9, 0, 53, 17, 45, 62, 18, 38, 52, 64, 59, 20, 61, 60, 8, 55, 33, 46, 11, 35, 44, 40, 32, 3, 24, 57, 41, 50, 37, 39, 54, 51], 'cur_cost': 95006.0}, {'tour': array([ 1, 50, 38, 52, 53, 18, 28, 15, 29,  7, 19, 16, 48, 30, 20,  4, 59,
        5, 45, 17, 12, 21,  3, 22, 23, 41, 31,  2, 42, 35,  6, 54, 55, 60,
       51, 14,  8, 46, 64, 32, 43, 33, 39, 37, 61, 65, 57, 24, 58, 25, 40,
       13, 34, 62, 56, 27, 44, 36,  0, 63, 47, 11,  9, 10, 26, 49],
      dtype=int64), 'cur_cost': 106222.0}, {'tour': [27, 20, 2, 26, 11, 14, 56, 10, 54, 31, 41, 36, 4, 1, 6, 57, 34, 42, 37, 43, 65, 50, 8, 40, 53, 17, 49, 3, 52, 59, 46, 13, 51, 5, 22, 38, 61, 35, 62, 64, 39, 30, 47, 7, 25, 12, 48, 60, 18, 9, 0, 29, 55, 15, 24, 45, 58, 19, 23, 21, 32, 33, 16, 28, 63, 44], 'cur_cost': 118010.0}, {'tour': [33, 26, 9, 7, 14, 12, 16, 30, 17, 0, 5, 3, 60, 62, 15, 6, 10, 19, 25, 18, 29, 34, 35, 28, 40, 43, 51, 21, 13, 22, 32, 27, 46, 42, 31, 2, 55, 8, 52, 59, 65, 58, 11, 54, 23, 36, 37, 49, 39, 48, 24, 44, 50, 47, 41, 4, 53, 61, 63, 56, 57, 1, 20, 45, 38, 64], 'cur_cost': 56071.0}, {'tour': array([29, 52, 43, 57, 10,  8,  9, 31, 15, 16,  6, 18, 40, 23, 51, 49, 56,
       59, 22, 63, 26, 54,  4, 32, 64, 65, 44, 36, 48, 37,  7, 14, 46, 13,
       61, 25, 17,  3, 27, 28, 33, 39, 19, 55, 35, 41, 20, 62, 45, 60, 12,
       30,  0, 42, 58, 53, 34,  1, 21, 11,  2, 47, 50,  5, 24, 38],
      dtype=int64), 'cur_cost': 111815.0}, {'tour': [33, 20, 3, 21, 6, 32, 19, 5, 18, 31, 41, 22, 62, 27, 16, 37, 65, 50, 35, 15, 1, 14, 56, 29, 43, 60, 58, 38, 52, 2, 30, 64, 25, 12, 13, 28, 57, 44, 61, 53, 0, 11, 24, 23, 47, 49, 7, 4, 17, 8, 45, 39, 54, 9, 55, 34, 10, 63, 48, 26, 36, 46, 51, 59, 40, 42], 'cur_cost': 108849.0}, {'tour': [27, 20, 25, 11, 65, 56, 1, 28, 54, 4, 36, 24, 23, 40, 53, 49, 42, 13, 29, 15, 51, 12, 22, 52, 64, 44, 30, 61, 18, 35, 10, 47, 55, 3, 59, 19, 32, 38, 57, 26, 7, 60, 17, 9, 14, 5, 39, 34, 21, 58, 37, 2, 31, 33, 48, 8, 46, 41, 16, 0, 45, 6, 50, 43, 63, 62], 'cur_cost': 109965.0}, {'tour': array([50, 54, 38,  5, 31, 49, 58, 18, 45, 20, 44, 63,  6, 48, 26,  4, 62,
       16, 36, 61, 10, 17, 39, 19, 14, 12,  0, 22, 41, 47, 59,  1, 30, 57,
        7, 32, 24, 40, 35,  9, 46, 21,  3, 56, 33, 23, 52, 29, 28, 25, 11,
       51, 13, 34, 27, 43, 60, 64,  2, 37, 55,  8, 42, 53, 15, 65],
      dtype=int64), 'cur_cost': 112334.0}, {'tour': [2, 4, 18, 11, 59, 33, 1, 19, 53, 37, 14, 26, 22, 21, 17, 54, 7, 41, 65, 42, 13, 8, 34, 50, 44, 24, 0, 38, 61, 60, 20, 9, 62, 55, 63, 23, 46, 43, 32, 30, 36, 47, 10, 51, 58, 35, 5, 52, 27, 25, 28, 49, 6, 48, 64, 3, 39, 45, 15, 16, 31, 29, 56, 40, 12, 57], 'cur_cost': 101516.0}, {'tour': [0, 13, 10, 19, 16, 18, 12, 22, 23, 15, 14, 20, 21, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14963.0}, {'tour': [19, 48, 40, 35, 16, 36, 8, 9, 15, 6, 10, 4, 27, 49, 18, 5, 62, 55, 53, 3, 51, 13, 30, 26, 12, 29, 14, 58, 45, 64, 46, 41, 44, 17, 56, 24, 20, 1, 39, 22, 2, 32, 63, 54, 60, 47, 42, 52, 59, 61, 50, 11, 31, 34, 25, 0, 7, 57, 38, 23, 33, 37, 28, 65, 43, 21], 'cur_cost': 86776.0}, {'tour': [45, 13, 27, 16, 26, 14, 17, 35, 4, 7, 59, 55, 0, 54, 1, 57, 11, 9, 12, 34, 24, 15, 19, 49, 46, 25, 32, 31, 3, 2, 8, 58, 5, 10, 64, 23, 21, 43, 38, 40, 42, 36, 22, 29, 39, 44, 50, 51, 20, 30, 47, 48, 6, 61, 63, 53, 65, 56, 62, 60, 52, 41, 37, 33, 28, 18], 'cur_cost': 55001.0}, {'tour': [52, 24, 31, 16, 10, 33, 62, 30, 32, 56, 47, 23, 12, 19, 15, 65, 1, 41, 17, 36, 29, 18, 25, 59, 35, 49, 44, 38, 40, 43, 3, 64, 5, 54, 7, 9, 11, 57, 58, 2, 42, 50, 27, 4, 28, 8, 48, 0, 46, 6, 22, 55, 20, 34, 14, 60, 61, 13, 21, 45, 26, 39, 63, 53, 51, 37], 'cur_cost': 97099.0}, {'tour': [41, 27, 29, 48, 12, 49, 63, 62, 33, 37, 11, 44, 50, 57, 59, 31, 1, 14, 3, 36, 26, 25, 13, 7, 39, 23, 2, 65, 9, 4, 52, 53, 0, 20, 10, 47, 6, 51, 60, 8, 42, 38, 43, 32, 61, 18, 34, 19, 22, 54, 46, 16, 35, 28, 45, 17, 24, 56, 64, 40, 15, 21, 55, 58, 5, 30], 'cur_cost': 94236.0}, {'tour': [0, 10, 17, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12733.0}, {'tour': [14, 61, 18, 17, 59, 3, 11, 27, 37, 29, 22, 9, 12, 4, 46, 65, 16, 42, 24, 8, 6, 5, 28, 57, 44, 54, 52, 63, 26, 7, 55, 47, 25, 13, 40, 45, 56, 2, 64, 38, 53, 58, 35, 21, 50, 30, 48, 39, 19, 33, 34, 15, 36, 32, 43, 1, 0, 23, 51, 60, 41, 62, 49, 31, 10, 20], 'cur_cost': 100351.0}, {'tour': [26, 21, 11, 61, 3, 7, 43, 40, 1, 60, 49, 32, 15, 52, 8, 14, 33, 20, 29, 62, 12, 44, 46, 4, 36, 37, 16, 34, 38, 50, 17, 39, 53, 23, 31, 0, 57, 42, 2, 65, 18, 35, 27, 9, 28, 48, 56, 19, 64, 59, 10, 45, 63, 41, 47, 30, 58, 6, 51, 22, 13, 25, 55, 5, 24, 54], 'cur_cost': 106851.0}]
2025-08-03 16:14:52,605 - ExploitationExpert - INFO - 局部搜索耗时: 0.14秒
2025-08-03 16:14:52,605 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-08-03 16:14:52,606 - experts.management.collaboration_manager - INFO - 个体 10 利用路径生成报告: {'new_tour': array([50, 54, 38,  5, 31, 49, 58, 18, 45, 20, 44, 63,  6, 48, 26,  4, 62,
       16, 36, 61, 10, 17, 39, 19, 14, 12,  0, 22, 41, 47, 59,  1, 30, 57,
        7, 32, 24, 40, 35,  9, 46, 21,  3, 56, 33, 23, 52, 29, 28, 25, 11,
       51, 13, 34, 27, 43, 60, 64,  2, 37, 55,  8, 42, 53, 15, 65],
      dtype=int64), 'cur_cost': 112334.0}
2025-08-03 16:14:52,607 - experts.management.collaboration_manager - INFO - 个体 10 接受新路径 (成本: 112334.00)
2025-08-03 16:14:52,607 - experts.management.collaboration_manager - INFO - 为个体 11 生成探索路径
2025-08-03 16:14:52,607 - ExplorationExpert - INFO - 开始为个体 11 生成探索路径（算法实现）
2025-08-03 16:14:52,608 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:14:52,626 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:14:52,627 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:14:52,628 - ExplorationExpert - INFO - 探索路径生成完成，成本: 59801.0, 路径长度: 66
2025-08-03 16:14:52,628 - experts.management.collaboration_manager - INFO - 个体 11 探索路径生成报告: {'new_tour': [65, 62, 14, 19, 5, 22, 31, 16, 26, 0, 23, 28, 30, 27, 33, 8, 9, 1, 10, 11, 63, 52, 18, 34, 25, 7, 21, 13, 47, 50, 45, 41, 36, 40, 46, 37, 4, 60, 2, 6, 55, 15, 43, 39, 51, 44, 49, 32, 29, 12, 48, 35, 38, 20, 58, 59, 64, 53, 54, 57, 56, 61, 3, 17, 24, 42], 'cur_cost': 59801.0}
2025-08-03 16:14:52,628 - experts.management.collaboration_manager - INFO - 个体 11 接受新路径 (成本: 59801.00)
2025-08-03 16:14:52,629 - experts.management.collaboration_manager - INFO - 为个体 12 生成探索路径
2025-08-03 16:14:52,629 - ExplorationExpert - INFO - 开始为个体 12 生成探索路径（算法实现）
2025-08-03 16:14:52,629 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:14:52,644 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:14:52,645 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:14:52,647 - ExplorationExpert - INFO - 探索路径生成完成，成本: 71307.0, 路径长度: 66
2025-08-03 16:14:52,651 - experts.management.collaboration_manager - INFO - 个体 12 探索路径生成报告: {'new_tour': [50, 39, 44, 41, 16, 35, 20, 28, 7, 53, 8, 17, 31, 13, 11, 63, 55, 62, 59, 5, 15, 36, 19, 2, 56, 12, 3, 54, 14, 26, 6, 65, 0, 64, 22, 24, 34, 4, 58, 18, 29, 30, 49, 51, 42, 48, 45, 27, 1, 57, 21, 37, 33, 23, 25, 9, 61, 52, 10, 32, 43, 40, 46, 47, 38, 60], 'cur_cost': 71307.0}
2025-08-03 16:14:52,654 - experts.management.collaboration_manager - INFO - 个体 12 接受新路径 (成本: 71307.00)
2025-08-03 16:14:52,656 - experts.management.collaboration_manager - INFO - 为个体 13 生成利用路径
2025-08-03 16:14:52,657 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:14:52,661 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:14:52,663 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 13 处的路径，新成本: 115430.0
2025-08-03 16:14:52,776 - ExploitationExpert - INFO - res_population_num: 11
2025-08-03 16:14:52,777 - ExploitationExpert - INFO - res_population_costs: [9521, 9522, 9527, 9532, 9533, 9554.0, 9554, 9575, 9577.0, 9836.0, 9521]
2025-08-03 16:14:52,777 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 22, 14, 15, 17, 12, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64, 52, 63,
       65, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64, 52, 63,
       65, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 48, 43, 40,
       49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:14:52,787 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:14:52,788 - ExploitationExpert - INFO - populations: [{'tour': [0, 14, 3, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14720.0}, {'tour': array([22, 29, 32, 24, 55, 48, 25, 65, 43, 46, 30, 37,  3, 44, 52, 49, 23,
       47,  8,  9,  7, 57,  5, 59, 51,  0, 31, 26, 60, 12, 17, 28, 21, 19,
       45, 39, 11, 10,  6, 36, 41, 38, 27, 58, 61, 16, 56, 15, 40,  1, 63,
       53, 50, 13, 54,  4, 42, 35, 62, 20, 14,  2, 64, 18, 33, 34],
      dtype=int64), 'cur_cost': 98106.0}, {'tour': [14, 17, 20, 25, 19, 32, 26, 5, 65, 7, 8, 11, 23, 12, 10, 58, 0, 3, 2, 21, 36, 24, 4, 27, 6, 54, 56, 52, 22, 37, 43, 51, 49, 50, 34, 40, 15, 29, 18, 9, 1, 16, 44, 39, 30, 48, 13, 28, 45, 35, 46, 55, 61, 53, 62, 64, 59, 63, 41, 38, 42, 47, 31, 33, 60, 57], 'cur_cost': 66008.0}, {'tour': [12, 5, 19, 21, 6, 10, 65, 2, 34, 13, 58, 22, 56, 30, 28, 36, 4, 31, 26, 27, 48, 43, 23, 7, 1, 14, 25, 42, 15, 29, 49, 47, 16, 63, 9, 0, 53, 17, 45, 62, 18, 38, 52, 64, 59, 20, 61, 60, 8, 55, 33, 46, 11, 35, 44, 40, 32, 3, 24, 57, 41, 50, 37, 39, 54, 51], 'cur_cost': 95006.0}, {'tour': array([ 1, 50, 38, 52, 53, 18, 28, 15, 29,  7, 19, 16, 48, 30, 20,  4, 59,
        5, 45, 17, 12, 21,  3, 22, 23, 41, 31,  2, 42, 35,  6, 54, 55, 60,
       51, 14,  8, 46, 64, 32, 43, 33, 39, 37, 61, 65, 57, 24, 58, 25, 40,
       13, 34, 62, 56, 27, 44, 36,  0, 63, 47, 11,  9, 10, 26, 49],
      dtype=int64), 'cur_cost': 106222.0}, {'tour': [27, 20, 2, 26, 11, 14, 56, 10, 54, 31, 41, 36, 4, 1, 6, 57, 34, 42, 37, 43, 65, 50, 8, 40, 53, 17, 49, 3, 52, 59, 46, 13, 51, 5, 22, 38, 61, 35, 62, 64, 39, 30, 47, 7, 25, 12, 48, 60, 18, 9, 0, 29, 55, 15, 24, 45, 58, 19, 23, 21, 32, 33, 16, 28, 63, 44], 'cur_cost': 118010.0}, {'tour': [33, 26, 9, 7, 14, 12, 16, 30, 17, 0, 5, 3, 60, 62, 15, 6, 10, 19, 25, 18, 29, 34, 35, 28, 40, 43, 51, 21, 13, 22, 32, 27, 46, 42, 31, 2, 55, 8, 52, 59, 65, 58, 11, 54, 23, 36, 37, 49, 39, 48, 24, 44, 50, 47, 41, 4, 53, 61, 63, 56, 57, 1, 20, 45, 38, 64], 'cur_cost': 56071.0}, {'tour': array([29, 52, 43, 57, 10,  8,  9, 31, 15, 16,  6, 18, 40, 23, 51, 49, 56,
       59, 22, 63, 26, 54,  4, 32, 64, 65, 44, 36, 48, 37,  7, 14, 46, 13,
       61, 25, 17,  3, 27, 28, 33, 39, 19, 55, 35, 41, 20, 62, 45, 60, 12,
       30,  0, 42, 58, 53, 34,  1, 21, 11,  2, 47, 50,  5, 24, 38],
      dtype=int64), 'cur_cost': 111815.0}, {'tour': [33, 20, 3, 21, 6, 32, 19, 5, 18, 31, 41, 22, 62, 27, 16, 37, 65, 50, 35, 15, 1, 14, 56, 29, 43, 60, 58, 38, 52, 2, 30, 64, 25, 12, 13, 28, 57, 44, 61, 53, 0, 11, 24, 23, 47, 49, 7, 4, 17, 8, 45, 39, 54, 9, 55, 34, 10, 63, 48, 26, 36, 46, 51, 59, 40, 42], 'cur_cost': 108849.0}, {'tour': [27, 20, 25, 11, 65, 56, 1, 28, 54, 4, 36, 24, 23, 40, 53, 49, 42, 13, 29, 15, 51, 12, 22, 52, 64, 44, 30, 61, 18, 35, 10, 47, 55, 3, 59, 19, 32, 38, 57, 26, 7, 60, 17, 9, 14, 5, 39, 34, 21, 58, 37, 2, 31, 33, 48, 8, 46, 41, 16, 0, 45, 6, 50, 43, 63, 62], 'cur_cost': 109965.0}, {'tour': array([50, 54, 38,  5, 31, 49, 58, 18, 45, 20, 44, 63,  6, 48, 26,  4, 62,
       16, 36, 61, 10, 17, 39, 19, 14, 12,  0, 22, 41, 47, 59,  1, 30, 57,
        7, 32, 24, 40, 35,  9, 46, 21,  3, 56, 33, 23, 52, 29, 28, 25, 11,
       51, 13, 34, 27, 43, 60, 64,  2, 37, 55,  8, 42, 53, 15, 65],
      dtype=int64), 'cur_cost': 112334.0}, {'tour': [65, 62, 14, 19, 5, 22, 31, 16, 26, 0, 23, 28, 30, 27, 33, 8, 9, 1, 10, 11, 63, 52, 18, 34, 25, 7, 21, 13, 47, 50, 45, 41, 36, 40, 46, 37, 4, 60, 2, 6, 55, 15, 43, 39, 51, 44, 49, 32, 29, 12, 48, 35, 38, 20, 58, 59, 64, 53, 54, 57, 56, 61, 3, 17, 24, 42], 'cur_cost': 59801.0}, {'tour': [50, 39, 44, 41, 16, 35, 20, 28, 7, 53, 8, 17, 31, 13, 11, 63, 55, 62, 59, 5, 15, 36, 19, 2, 56, 12, 3, 54, 14, 26, 6, 65, 0, 64, 22, 24, 34, 4, 58, 18, 29, 30, 49, 51, 42, 48, 45, 27, 1, 57, 21, 37, 33, 23, 25, 9, 61, 52, 10, 32, 43, 40, 46, 47, 38, 60], 'cur_cost': 71307.0}, {'tour': array([ 5, 47, 28,  8,  0, 41, 61, 33, 13, 29, 60, 22, 65, 19, 62, 42,  3,
       26,  9, 16, 21, 10, 49, 45, 17, 52, 20, 57, 53, 18, 25, 30, 11, 44,
       46,  2, 34, 36, 35, 56, 38, 48, 31,  1, 59,  7, 24, 15, 64, 14, 40,
       54, 37, 39, 23, 55, 51, 27, 43, 63,  6, 12, 50, 58, 32,  4],
      dtype=int64), 'cur_cost': 115430.0}, {'tour': [45, 13, 27, 16, 26, 14, 17, 35, 4, 7, 59, 55, 0, 54, 1, 57, 11, 9, 12, 34, 24, 15, 19, 49, 46, 25, 32, 31, 3, 2, 8, 58, 5, 10, 64, 23, 21, 43, 38, 40, 42, 36, 22, 29, 39, 44, 50, 51, 20, 30, 47, 48, 6, 61, 63, 53, 65, 56, 62, 60, 52, 41, 37, 33, 28, 18], 'cur_cost': 55001.0}, {'tour': [52, 24, 31, 16, 10, 33, 62, 30, 32, 56, 47, 23, 12, 19, 15, 65, 1, 41, 17, 36, 29, 18, 25, 59, 35, 49, 44, 38, 40, 43, 3, 64, 5, 54, 7, 9, 11, 57, 58, 2, 42, 50, 27, 4, 28, 8, 48, 0, 46, 6, 22, 55, 20, 34, 14, 60, 61, 13, 21, 45, 26, 39, 63, 53, 51, 37], 'cur_cost': 97099.0}, {'tour': [41, 27, 29, 48, 12, 49, 63, 62, 33, 37, 11, 44, 50, 57, 59, 31, 1, 14, 3, 36, 26, 25, 13, 7, 39, 23, 2, 65, 9, 4, 52, 53, 0, 20, 10, 47, 6, 51, 60, 8, 42, 38, 43, 32, 61, 18, 34, 19, 22, 54, 46, 16, 35, 28, 45, 17, 24, 56, 64, 40, 15, 21, 55, 58, 5, 30], 'cur_cost': 94236.0}, {'tour': [0, 10, 17, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12733.0}, {'tour': [14, 61, 18, 17, 59, 3, 11, 27, 37, 29, 22, 9, 12, 4, 46, 65, 16, 42, 24, 8, 6, 5, 28, 57, 44, 54, 52, 63, 26, 7, 55, 47, 25, 13, 40, 45, 56, 2, 64, 38, 53, 58, 35, 21, 50, 30, 48, 39, 19, 33, 34, 15, 36, 32, 43, 1, 0, 23, 51, 60, 41, 62, 49, 31, 10, 20], 'cur_cost': 100351.0}, {'tour': [26, 21, 11, 61, 3, 7, 43, 40, 1, 60, 49, 32, 15, 52, 8, 14, 33, 20, 29, 62, 12, 44, 46, 4, 36, 37, 16, 34, 38, 50, 17, 39, 53, 23, 31, 0, 57, 42, 2, 65, 18, 35, 27, 9, 28, 48, 56, 19, 64, 59, 10, 45, 63, 41, 47, 30, 58, 6, 51, 22, 13, 25, 55, 5, 24, 54], 'cur_cost': 106851.0}]
2025-08-03 16:14:52,794 - ExploitationExpert - INFO - 局部搜索耗时: 0.13秒
2025-08-03 16:14:52,794 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-08-03 16:14:52,795 - experts.management.collaboration_manager - INFO - 个体 13 利用路径生成报告: {'new_tour': array([ 5, 47, 28,  8,  0, 41, 61, 33, 13, 29, 60, 22, 65, 19, 62, 42,  3,
       26,  9, 16, 21, 10, 49, 45, 17, 52, 20, 57, 53, 18, 25, 30, 11, 44,
       46,  2, 34, 36, 35, 56, 38, 48, 31,  1, 59,  7, 24, 15, 64, 14, 40,
       54, 37, 39, 23, 55, 51, 27, 43, 63,  6, 12, 50, 58, 32,  4],
      dtype=int64), 'cur_cost': 115430.0}
2025-08-03 16:14:52,795 - experts.management.collaboration_manager - INFO - 个体 13 接受新路径 (成本: 115430.00)
2025-08-03 16:14:52,795 - experts.management.collaboration_manager - INFO - 为个体 14 生成探索路径
2025-08-03 16:14:52,795 - ExplorationExpert - INFO - 开始为个体 14 生成探索路径（算法实现）
2025-08-03 16:14:52,796 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:14:52,800 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:14:52,801 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:14:52,801 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10419.0, 路径长度: 66
2025-08-03 16:14:52,801 - experts.management.collaboration_manager - INFO - 个体 14 探索路径生成报告: {'new_tour': [0, 10, 6, 5, 4, 8, 2, 11, 9, 3, 7, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10419.0}
2025-08-03 16:14:52,802 - experts.management.collaboration_manager - INFO - 个体 14 接受新路径 (成本: 10419.00)
2025-08-03 16:14:52,802 - experts.management.collaboration_manager - INFO - 为个体 15 生成探索路径
2025-08-03 16:14:52,803 - ExplorationExpert - INFO - 开始为个体 15 生成探索路径（算法实现）
2025-08-03 16:14:52,803 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:14:52,806 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:14:52,807 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:14:52,807 - ExplorationExpert - INFO - 探索路径生成完成，成本: 99546.0, 路径长度: 66
2025-08-03 16:14:52,807 - experts.management.collaboration_manager - INFO - 个体 15 探索路径生成报告: {'new_tour': [17, 65, 60, 62, 27, 57, 0, 36, 40, 6, 14, 56, 18, 26, 30, 49, 50, 34, 38, 53, 35, 45, 61, 59, 23, 20, 64, 28, 39, 12, 52, 63, 11, 44, 43, 3, 10, 13, 22, 31, 29, 51, 7, 15, 37, 19, 21, 1, 2, 55, 47, 42, 32, 41, 16, 54, 58, 24, 33, 8, 48, 9, 4, 46, 5, 25], 'cur_cost': 99546.0}
2025-08-03 16:14:52,808 - experts.management.collaboration_manager - INFO - 个体 15 接受新路径 (成本: 99546.00)
2025-08-03 16:14:52,808 - experts.management.collaboration_manager - INFO - 为个体 16 生成利用路径
2025-08-03 16:14:52,808 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:14:52,808 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:14:52,809 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 16 处的路径，新成本: 117959.0
2025-08-03 16:14:52,909 - ExploitationExpert - INFO - res_population_num: 11
2025-08-03 16:14:52,910 - ExploitationExpert - INFO - res_population_costs: [9521, 9522, 9527, 9532, 9533, 9554.0, 9554, 9575, 9577.0, 9836.0, 9521]
2025-08-03 16:14:52,910 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 22, 14, 15, 17, 12, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64, 52, 63,
       65, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64, 52, 63,
       65, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 48, 43, 40,
       49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:14:52,922 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:14:52,923 - ExploitationExpert - INFO - populations: [{'tour': [0, 14, 3, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14720.0}, {'tour': array([22, 29, 32, 24, 55, 48, 25, 65, 43, 46, 30, 37,  3, 44, 52, 49, 23,
       47,  8,  9,  7, 57,  5, 59, 51,  0, 31, 26, 60, 12, 17, 28, 21, 19,
       45, 39, 11, 10,  6, 36, 41, 38, 27, 58, 61, 16, 56, 15, 40,  1, 63,
       53, 50, 13, 54,  4, 42, 35, 62, 20, 14,  2, 64, 18, 33, 34],
      dtype=int64), 'cur_cost': 98106.0}, {'tour': [14, 17, 20, 25, 19, 32, 26, 5, 65, 7, 8, 11, 23, 12, 10, 58, 0, 3, 2, 21, 36, 24, 4, 27, 6, 54, 56, 52, 22, 37, 43, 51, 49, 50, 34, 40, 15, 29, 18, 9, 1, 16, 44, 39, 30, 48, 13, 28, 45, 35, 46, 55, 61, 53, 62, 64, 59, 63, 41, 38, 42, 47, 31, 33, 60, 57], 'cur_cost': 66008.0}, {'tour': [12, 5, 19, 21, 6, 10, 65, 2, 34, 13, 58, 22, 56, 30, 28, 36, 4, 31, 26, 27, 48, 43, 23, 7, 1, 14, 25, 42, 15, 29, 49, 47, 16, 63, 9, 0, 53, 17, 45, 62, 18, 38, 52, 64, 59, 20, 61, 60, 8, 55, 33, 46, 11, 35, 44, 40, 32, 3, 24, 57, 41, 50, 37, 39, 54, 51], 'cur_cost': 95006.0}, {'tour': array([ 1, 50, 38, 52, 53, 18, 28, 15, 29,  7, 19, 16, 48, 30, 20,  4, 59,
        5, 45, 17, 12, 21,  3, 22, 23, 41, 31,  2, 42, 35,  6, 54, 55, 60,
       51, 14,  8, 46, 64, 32, 43, 33, 39, 37, 61, 65, 57, 24, 58, 25, 40,
       13, 34, 62, 56, 27, 44, 36,  0, 63, 47, 11,  9, 10, 26, 49],
      dtype=int64), 'cur_cost': 106222.0}, {'tour': [27, 20, 2, 26, 11, 14, 56, 10, 54, 31, 41, 36, 4, 1, 6, 57, 34, 42, 37, 43, 65, 50, 8, 40, 53, 17, 49, 3, 52, 59, 46, 13, 51, 5, 22, 38, 61, 35, 62, 64, 39, 30, 47, 7, 25, 12, 48, 60, 18, 9, 0, 29, 55, 15, 24, 45, 58, 19, 23, 21, 32, 33, 16, 28, 63, 44], 'cur_cost': 118010.0}, {'tour': [33, 26, 9, 7, 14, 12, 16, 30, 17, 0, 5, 3, 60, 62, 15, 6, 10, 19, 25, 18, 29, 34, 35, 28, 40, 43, 51, 21, 13, 22, 32, 27, 46, 42, 31, 2, 55, 8, 52, 59, 65, 58, 11, 54, 23, 36, 37, 49, 39, 48, 24, 44, 50, 47, 41, 4, 53, 61, 63, 56, 57, 1, 20, 45, 38, 64], 'cur_cost': 56071.0}, {'tour': array([29, 52, 43, 57, 10,  8,  9, 31, 15, 16,  6, 18, 40, 23, 51, 49, 56,
       59, 22, 63, 26, 54,  4, 32, 64, 65, 44, 36, 48, 37,  7, 14, 46, 13,
       61, 25, 17,  3, 27, 28, 33, 39, 19, 55, 35, 41, 20, 62, 45, 60, 12,
       30,  0, 42, 58, 53, 34,  1, 21, 11,  2, 47, 50,  5, 24, 38],
      dtype=int64), 'cur_cost': 111815.0}, {'tour': [33, 20, 3, 21, 6, 32, 19, 5, 18, 31, 41, 22, 62, 27, 16, 37, 65, 50, 35, 15, 1, 14, 56, 29, 43, 60, 58, 38, 52, 2, 30, 64, 25, 12, 13, 28, 57, 44, 61, 53, 0, 11, 24, 23, 47, 49, 7, 4, 17, 8, 45, 39, 54, 9, 55, 34, 10, 63, 48, 26, 36, 46, 51, 59, 40, 42], 'cur_cost': 108849.0}, {'tour': [27, 20, 25, 11, 65, 56, 1, 28, 54, 4, 36, 24, 23, 40, 53, 49, 42, 13, 29, 15, 51, 12, 22, 52, 64, 44, 30, 61, 18, 35, 10, 47, 55, 3, 59, 19, 32, 38, 57, 26, 7, 60, 17, 9, 14, 5, 39, 34, 21, 58, 37, 2, 31, 33, 48, 8, 46, 41, 16, 0, 45, 6, 50, 43, 63, 62], 'cur_cost': 109965.0}, {'tour': array([50, 54, 38,  5, 31, 49, 58, 18, 45, 20, 44, 63,  6, 48, 26,  4, 62,
       16, 36, 61, 10, 17, 39, 19, 14, 12,  0, 22, 41, 47, 59,  1, 30, 57,
        7, 32, 24, 40, 35,  9, 46, 21,  3, 56, 33, 23, 52, 29, 28, 25, 11,
       51, 13, 34, 27, 43, 60, 64,  2, 37, 55,  8, 42, 53, 15, 65],
      dtype=int64), 'cur_cost': 112334.0}, {'tour': [65, 62, 14, 19, 5, 22, 31, 16, 26, 0, 23, 28, 30, 27, 33, 8, 9, 1, 10, 11, 63, 52, 18, 34, 25, 7, 21, 13, 47, 50, 45, 41, 36, 40, 46, 37, 4, 60, 2, 6, 55, 15, 43, 39, 51, 44, 49, 32, 29, 12, 48, 35, 38, 20, 58, 59, 64, 53, 54, 57, 56, 61, 3, 17, 24, 42], 'cur_cost': 59801.0}, {'tour': [50, 39, 44, 41, 16, 35, 20, 28, 7, 53, 8, 17, 31, 13, 11, 63, 55, 62, 59, 5, 15, 36, 19, 2, 56, 12, 3, 54, 14, 26, 6, 65, 0, 64, 22, 24, 34, 4, 58, 18, 29, 30, 49, 51, 42, 48, 45, 27, 1, 57, 21, 37, 33, 23, 25, 9, 61, 52, 10, 32, 43, 40, 46, 47, 38, 60], 'cur_cost': 71307.0}, {'tour': array([ 5, 47, 28,  8,  0, 41, 61, 33, 13, 29, 60, 22, 65, 19, 62, 42,  3,
       26,  9, 16, 21, 10, 49, 45, 17, 52, 20, 57, 53, 18, 25, 30, 11, 44,
       46,  2, 34, 36, 35, 56, 38, 48, 31,  1, 59,  7, 24, 15, 64, 14, 40,
       54, 37, 39, 23, 55, 51, 27, 43, 63,  6, 12, 50, 58, 32,  4],
      dtype=int64), 'cur_cost': 115430.0}, {'tour': [0, 10, 6, 5, 4, 8, 2, 11, 9, 3, 7, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10419.0}, {'tour': [17, 65, 60, 62, 27, 57, 0, 36, 40, 6, 14, 56, 18, 26, 30, 49, 50, 34, 38, 53, 35, 45, 61, 59, 23, 20, 64, 28, 39, 12, 52, 63, 11, 44, 43, 3, 10, 13, 22, 31, 29, 51, 7, 15, 37, 19, 21, 1, 2, 55, 47, 42, 32, 41, 16, 54, 58, 24, 33, 8, 48, 9, 4, 46, 5, 25], 'cur_cost': 99546.0}, {'tour': array([38, 20, 23, 46, 32, 33,  6, 54, 60, 41, 26, 55, 50, 63, 43,  5, 24,
        9, 51, 11, 52, 37, 44, 45, 28,  8, 49, 34, 12, 18, 57, 35, 13, 19,
       31, 39, 21, 53,  1,  2, 59, 30,  7, 42, 14, 58, 36, 17, 47,  4, 10,
       16, 65, 40, 15, 64, 48, 29, 22, 61, 27,  3, 62,  0, 25, 56],
      dtype=int64), 'cur_cost': 117959.0}, {'tour': [0, 10, 17, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12733.0}, {'tour': [14, 61, 18, 17, 59, 3, 11, 27, 37, 29, 22, 9, 12, 4, 46, 65, 16, 42, 24, 8, 6, 5, 28, 57, 44, 54, 52, 63, 26, 7, 55, 47, 25, 13, 40, 45, 56, 2, 64, 38, 53, 58, 35, 21, 50, 30, 48, 39, 19, 33, 34, 15, 36, 32, 43, 1, 0, 23, 51, 60, 41, 62, 49, 31, 10, 20], 'cur_cost': 100351.0}, {'tour': [26, 21, 11, 61, 3, 7, 43, 40, 1, 60, 49, 32, 15, 52, 8, 14, 33, 20, 29, 62, 12, 44, 46, 4, 36, 37, 16, 34, 38, 50, 17, 39, 53, 23, 31, 0, 57, 42, 2, 65, 18, 35, 27, 9, 28, 48, 56, 19, 64, 59, 10, 45, 63, 41, 47, 30, 58, 6, 51, 22, 13, 25, 55, 5, 24, 54], 'cur_cost': 106851.0}]
2025-08-03 16:14:52,933 - ExploitationExpert - INFO - 局部搜索耗时: 0.12秒
2025-08-03 16:14:52,933 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-08-03 16:14:52,934 - experts.management.collaboration_manager - INFO - 个体 16 利用路径生成报告: {'new_tour': array([38, 20, 23, 46, 32, 33,  6, 54, 60, 41, 26, 55, 50, 63, 43,  5, 24,
        9, 51, 11, 52, 37, 44, 45, 28,  8, 49, 34, 12, 18, 57, 35, 13, 19,
       31, 39, 21, 53,  1,  2, 59, 30,  7, 42, 14, 58, 36, 17, 47,  4, 10,
       16, 65, 40, 15, 64, 48, 29, 22, 61, 27,  3, 62,  0, 25, 56],
      dtype=int64), 'cur_cost': 117959.0}
2025-08-03 16:14:52,935 - experts.management.collaboration_manager - INFO - 个体 16 接受新路径 (成本: 117959.00)
2025-08-03 16:14:52,936 - experts.management.collaboration_manager - INFO - 为个体 17 生成探索路径
2025-08-03 16:14:52,936 - ExplorationExpert - INFO - 开始为个体 17 生成探索路径（算法实现）
2025-08-03 16:14:52,937 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:14:52,941 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:14:52,941 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:14:52,942 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12885.0, 路径长度: 66
2025-08-03 16:14:52,942 - experts.management.collaboration_manager - INFO - 个体 17 探索路径生成报告: {'new_tour': [0, 17, 21, 8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12885.0}
2025-08-03 16:14:52,944 - experts.management.collaboration_manager - INFO - 个体 17 保留原路径 (成本: 12885.00)
2025-08-03 16:14:52,944 - experts.management.collaboration_manager - INFO - 为个体 18 生成探索路径
2025-08-03 16:14:52,944 - ExplorationExpert - INFO - 开始为个体 18 生成探索路径（算法实现）
2025-08-03 16:14:52,945 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:14:52,951 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:14:52,954 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:14:52,956 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12761.0, 路径长度: 66
2025-08-03 16:14:52,956 - experts.management.collaboration_manager - INFO - 个体 18 探索路径生成报告: {'new_tour': [0, 15, 22, 11, 9, 3, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 23, 16, 18, 12, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12761.0}
2025-08-03 16:14:52,957 - experts.management.collaboration_manager - INFO - 个体 18 接受新路径 (成本: 12761.00)
2025-08-03 16:14:52,957 - experts.management.collaboration_manager - INFO - 为个体 19 生成利用路径
2025-08-03 16:14:52,958 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:14:52,958 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:14:52,959 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 19 处的路径，新成本: 110843.0
2025-08-03 16:14:53,076 - ExploitationExpert - INFO - res_population_num: 11
2025-08-03 16:14:53,076 - ExploitationExpert - INFO - res_population_costs: [9521, 9522, 9527, 9532, 9533, 9554.0, 9554, 9575, 9577.0, 9836.0, 9521]
2025-08-03 16:14:53,076 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 22, 14, 15, 17, 12, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64, 52, 63,
       65, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64, 52, 63,
       65, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 48, 43, 40,
       49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:14:53,087 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:14:53,088 - ExploitationExpert - INFO - populations: [{'tour': [0, 14, 3, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14720.0}, {'tour': array([22, 29, 32, 24, 55, 48, 25, 65, 43, 46, 30, 37,  3, 44, 52, 49, 23,
       47,  8,  9,  7, 57,  5, 59, 51,  0, 31, 26, 60, 12, 17, 28, 21, 19,
       45, 39, 11, 10,  6, 36, 41, 38, 27, 58, 61, 16, 56, 15, 40,  1, 63,
       53, 50, 13, 54,  4, 42, 35, 62, 20, 14,  2, 64, 18, 33, 34],
      dtype=int64), 'cur_cost': 98106.0}, {'tour': [14, 17, 20, 25, 19, 32, 26, 5, 65, 7, 8, 11, 23, 12, 10, 58, 0, 3, 2, 21, 36, 24, 4, 27, 6, 54, 56, 52, 22, 37, 43, 51, 49, 50, 34, 40, 15, 29, 18, 9, 1, 16, 44, 39, 30, 48, 13, 28, 45, 35, 46, 55, 61, 53, 62, 64, 59, 63, 41, 38, 42, 47, 31, 33, 60, 57], 'cur_cost': 66008.0}, {'tour': [12, 5, 19, 21, 6, 10, 65, 2, 34, 13, 58, 22, 56, 30, 28, 36, 4, 31, 26, 27, 48, 43, 23, 7, 1, 14, 25, 42, 15, 29, 49, 47, 16, 63, 9, 0, 53, 17, 45, 62, 18, 38, 52, 64, 59, 20, 61, 60, 8, 55, 33, 46, 11, 35, 44, 40, 32, 3, 24, 57, 41, 50, 37, 39, 54, 51], 'cur_cost': 95006.0}, {'tour': array([ 1, 50, 38, 52, 53, 18, 28, 15, 29,  7, 19, 16, 48, 30, 20,  4, 59,
        5, 45, 17, 12, 21,  3, 22, 23, 41, 31,  2, 42, 35,  6, 54, 55, 60,
       51, 14,  8, 46, 64, 32, 43, 33, 39, 37, 61, 65, 57, 24, 58, 25, 40,
       13, 34, 62, 56, 27, 44, 36,  0, 63, 47, 11,  9, 10, 26, 49],
      dtype=int64), 'cur_cost': 106222.0}, {'tour': [27, 20, 2, 26, 11, 14, 56, 10, 54, 31, 41, 36, 4, 1, 6, 57, 34, 42, 37, 43, 65, 50, 8, 40, 53, 17, 49, 3, 52, 59, 46, 13, 51, 5, 22, 38, 61, 35, 62, 64, 39, 30, 47, 7, 25, 12, 48, 60, 18, 9, 0, 29, 55, 15, 24, 45, 58, 19, 23, 21, 32, 33, 16, 28, 63, 44], 'cur_cost': 118010.0}, {'tour': [33, 26, 9, 7, 14, 12, 16, 30, 17, 0, 5, 3, 60, 62, 15, 6, 10, 19, 25, 18, 29, 34, 35, 28, 40, 43, 51, 21, 13, 22, 32, 27, 46, 42, 31, 2, 55, 8, 52, 59, 65, 58, 11, 54, 23, 36, 37, 49, 39, 48, 24, 44, 50, 47, 41, 4, 53, 61, 63, 56, 57, 1, 20, 45, 38, 64], 'cur_cost': 56071.0}, {'tour': array([29, 52, 43, 57, 10,  8,  9, 31, 15, 16,  6, 18, 40, 23, 51, 49, 56,
       59, 22, 63, 26, 54,  4, 32, 64, 65, 44, 36, 48, 37,  7, 14, 46, 13,
       61, 25, 17,  3, 27, 28, 33, 39, 19, 55, 35, 41, 20, 62, 45, 60, 12,
       30,  0, 42, 58, 53, 34,  1, 21, 11,  2, 47, 50,  5, 24, 38],
      dtype=int64), 'cur_cost': 111815.0}, {'tour': [33, 20, 3, 21, 6, 32, 19, 5, 18, 31, 41, 22, 62, 27, 16, 37, 65, 50, 35, 15, 1, 14, 56, 29, 43, 60, 58, 38, 52, 2, 30, 64, 25, 12, 13, 28, 57, 44, 61, 53, 0, 11, 24, 23, 47, 49, 7, 4, 17, 8, 45, 39, 54, 9, 55, 34, 10, 63, 48, 26, 36, 46, 51, 59, 40, 42], 'cur_cost': 108849.0}, {'tour': [27, 20, 25, 11, 65, 56, 1, 28, 54, 4, 36, 24, 23, 40, 53, 49, 42, 13, 29, 15, 51, 12, 22, 52, 64, 44, 30, 61, 18, 35, 10, 47, 55, 3, 59, 19, 32, 38, 57, 26, 7, 60, 17, 9, 14, 5, 39, 34, 21, 58, 37, 2, 31, 33, 48, 8, 46, 41, 16, 0, 45, 6, 50, 43, 63, 62], 'cur_cost': 109965.0}, {'tour': array([50, 54, 38,  5, 31, 49, 58, 18, 45, 20, 44, 63,  6, 48, 26,  4, 62,
       16, 36, 61, 10, 17, 39, 19, 14, 12,  0, 22, 41, 47, 59,  1, 30, 57,
        7, 32, 24, 40, 35,  9, 46, 21,  3, 56, 33, 23, 52, 29, 28, 25, 11,
       51, 13, 34, 27, 43, 60, 64,  2, 37, 55,  8, 42, 53, 15, 65],
      dtype=int64), 'cur_cost': 112334.0}, {'tour': [65, 62, 14, 19, 5, 22, 31, 16, 26, 0, 23, 28, 30, 27, 33, 8, 9, 1, 10, 11, 63, 52, 18, 34, 25, 7, 21, 13, 47, 50, 45, 41, 36, 40, 46, 37, 4, 60, 2, 6, 55, 15, 43, 39, 51, 44, 49, 32, 29, 12, 48, 35, 38, 20, 58, 59, 64, 53, 54, 57, 56, 61, 3, 17, 24, 42], 'cur_cost': 59801.0}, {'tour': [50, 39, 44, 41, 16, 35, 20, 28, 7, 53, 8, 17, 31, 13, 11, 63, 55, 62, 59, 5, 15, 36, 19, 2, 56, 12, 3, 54, 14, 26, 6, 65, 0, 64, 22, 24, 34, 4, 58, 18, 29, 30, 49, 51, 42, 48, 45, 27, 1, 57, 21, 37, 33, 23, 25, 9, 61, 52, 10, 32, 43, 40, 46, 47, 38, 60], 'cur_cost': 71307.0}, {'tour': array([ 5, 47, 28,  8,  0, 41, 61, 33, 13, 29, 60, 22, 65, 19, 62, 42,  3,
       26,  9, 16, 21, 10, 49, 45, 17, 52, 20, 57, 53, 18, 25, 30, 11, 44,
       46,  2, 34, 36, 35, 56, 38, 48, 31,  1, 59,  7, 24, 15, 64, 14, 40,
       54, 37, 39, 23, 55, 51, 27, 43, 63,  6, 12, 50, 58, 32,  4],
      dtype=int64), 'cur_cost': 115430.0}, {'tour': [0, 10, 6, 5, 4, 8, 2, 11, 9, 3, 7, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10419.0}, {'tour': [17, 65, 60, 62, 27, 57, 0, 36, 40, 6, 14, 56, 18, 26, 30, 49, 50, 34, 38, 53, 35, 45, 61, 59, 23, 20, 64, 28, 39, 12, 52, 63, 11, 44, 43, 3, 10, 13, 22, 31, 29, 51, 7, 15, 37, 19, 21, 1, 2, 55, 47, 42, 32, 41, 16, 54, 58, 24, 33, 8, 48, 9, 4, 46, 5, 25], 'cur_cost': 99546.0}, {'tour': array([38, 20, 23, 46, 32, 33,  6, 54, 60, 41, 26, 55, 50, 63, 43,  5, 24,
        9, 51, 11, 52, 37, 44, 45, 28,  8, 49, 34, 12, 18, 57, 35, 13, 19,
       31, 39, 21, 53,  1,  2, 59, 30,  7, 42, 14, 58, 36, 17, 47,  4, 10,
       16, 65, 40, 15, 64, 48, 29, 22, 61, 27,  3, 62,  0, 25, 56],
      dtype=int64), 'cur_cost': 117959.0}, {'tour': [0, 17, 21, 8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12885.0}, {'tour': [0, 15, 22, 11, 9, 3, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 23, 16, 18, 12, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12761.0}, {'tour': array([ 3, 35, 47, 38, 15,  2, 61, 25, 41,  7, 12, 59, 16, 46, 11, 64, 37,
       24, 22, 27,  9,  1,  6, 19,  0, 51, 62, 58, 34, 26, 45, 28,  4, 23,
       14, 56, 10, 50, 17, 53, 32, 43, 60, 39, 63,  5, 52, 65, 30, 42, 21,
       55, 44,  8, 57, 18, 40, 31, 49, 54, 20, 29, 36, 48, 13, 33],
      dtype=int64), 'cur_cost': 110843.0}]
2025-08-03 16:14:53,095 - ExploitationExpert - INFO - 局部搜索耗时: 0.14秒
2025-08-03 16:14:53,095 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 14, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 14, 'cache_hits': 0, 'similarity_calculations': 91, 'cache_hit_rate': 0.0, 'cache_size': 91}}
2025-08-03 16:14:53,096 - experts.management.collaboration_manager - INFO - 个体 19 利用路径生成报告: {'new_tour': array([ 3, 35, 47, 38, 15,  2, 61, 25, 41,  7, 12, 59, 16, 46, 11, 64, 37,
       24, 22, 27,  9,  1,  6, 19,  0, 51, 62, 58, 34, 26, 45, 28,  4, 23,
       14, 56, 10, 50, 17, 53, 32, 43, 60, 39, 63,  5, 52, 65, 30, 42, 21,
       55, 44,  8, 57, 18, 40, 31, 49, 54, 20, 29, 36, 48, 13, 33],
      dtype=int64), 'cur_cost': 110843.0}
2025-08-03 16:14:53,096 - experts.management.collaboration_manager - INFO - 个体 19 接受新路径 (成本: 110843.00)
2025-08-03 16:14:53,097 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 16, 'rejected': 4, 'elite_protected': 4}
2025-08-03 16:14:53,098 - experts.management.collaboration_manager - INFO - 接受率: 16/20 (80.0%)
2025-08-03 16:14:53,100 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 3, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14720.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([22, 29, 32, 24, 55, 48, 25, 65, 43, 46, 30, 37,  3, 44, 52, 49, 23,
       47,  8,  9,  7, 57,  5, 59, 51,  0, 31, 26, 60, 12, 17, 28, 21, 19,
       45, 39, 11, 10,  6, 36, 41, 38, 27, 58, 61, 16, 56, 15, 40,  1, 63,
       53, 50, 13, 54,  4, 42, 35, 62, 20, 14,  2, 64, 18, 33, 34],
      dtype=int64), 'cur_cost': 98106.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [14, 17, 20, 25, 19, 32, 26, 5, 65, 7, 8, 11, 23, 12, 10, 58, 0, 3, 2, 21, 36, 24, 4, 27, 6, 54, 56, 52, 22, 37, 43, 51, 49, 50, 34, 40, 15, 29, 18, 9, 1, 16, 44, 39, 30, 48, 13, 28, 45, 35, 46, 55, 61, 53, 62, 64, 59, 63, 41, 38, 42, 47, 31, 33, 60, 57], 'cur_cost': 66008.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [12, 5, 19, 21, 6, 10, 65, 2, 34, 13, 58, 22, 56, 30, 28, 36, 4, 31, 26, 27, 48, 43, 23, 7, 1, 14, 25, 42, 15, 29, 49, 47, 16, 63, 9, 0, 53, 17, 45, 62, 18, 38, 52, 64, 59, 20, 61, 60, 8, 55, 33, 46, 11, 35, 44, 40, 32, 3, 24, 57, 41, 50, 37, 39, 54, 51], 'cur_cost': 95006.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 1, 50, 38, 52, 53, 18, 28, 15, 29,  7, 19, 16, 48, 30, 20,  4, 59,
        5, 45, 17, 12, 21,  3, 22, 23, 41, 31,  2, 42, 35,  6, 54, 55, 60,
       51, 14,  8, 46, 64, 32, 43, 33, 39, 37, 61, 65, 57, 24, 58, 25, 40,
       13, 34, 62, 56, 27, 44, 36,  0, 63, 47, 11,  9, 10, 26, 49],
      dtype=int64), 'cur_cost': 106222.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [27, 20, 2, 26, 11, 14, 56, 10, 54, 31, 41, 36, 4, 1, 6, 57, 34, 42, 37, 43, 65, 50, 8, 40, 53, 17, 49, 3, 52, 59, 46, 13, 51, 5, 22, 38, 61, 35, 62, 64, 39, 30, 47, 7, 25, 12, 48, 60, 18, 9, 0, 29, 55, 15, 24, 45, 58, 19, 23, 21, 32, 33, 16, 28, 63, 44], 'cur_cost': 118010.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [33, 26, 9, 7, 14, 12, 16, 30, 17, 0, 5, 3, 60, 62, 15, 6, 10, 19, 25, 18, 29, 34, 35, 28, 40, 43, 51, 21, 13, 22, 32, 27, 46, 42, 31, 2, 55, 8, 52, 59, 65, 58, 11, 54, 23, 36, 37, 49, 39, 48, 24, 44, 50, 47, 41, 4, 53, 61, 63, 56, 57, 1, 20, 45, 38, 64], 'cur_cost': 56071.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([29, 52, 43, 57, 10,  8,  9, 31, 15, 16,  6, 18, 40, 23, 51, 49, 56,
       59, 22, 63, 26, 54,  4, 32, 64, 65, 44, 36, 48, 37,  7, 14, 46, 13,
       61, 25, 17,  3, 27, 28, 33, 39, 19, 55, 35, 41, 20, 62, 45, 60, 12,
       30,  0, 42, 58, 53, 34,  1, 21, 11,  2, 47, 50,  5, 24, 38],
      dtype=int64), 'cur_cost': 111815.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [33, 20, 3, 21, 6, 32, 19, 5, 18, 31, 41, 22, 62, 27, 16, 37, 65, 50, 35, 15, 1, 14, 56, 29, 43, 60, 58, 38, 52, 2, 30, 64, 25, 12, 13, 28, 57, 44, 61, 53, 0, 11, 24, 23, 47, 49, 7, 4, 17, 8, 45, 39, 54, 9, 55, 34, 10, 63, 48, 26, 36, 46, 51, 59, 40, 42], 'cur_cost': 108849.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [27, 20, 25, 11, 65, 56, 1, 28, 54, 4, 36, 24, 23, 40, 53, 49, 42, 13, 29, 15, 51, 12, 22, 52, 64, 44, 30, 61, 18, 35, 10, 47, 55, 3, 59, 19, 32, 38, 57, 26, 7, 60, 17, 9, 14, 5, 39, 34, 21, 58, 37, 2, 31, 33, 48, 8, 46, 41, 16, 0, 45, 6, 50, 43, 63, 62], 'cur_cost': 109965.0}}, {'individual': 10, 'strategy': 'exploit', 'path_data': {'new_tour': array([50, 54, 38,  5, 31, 49, 58, 18, 45, 20, 44, 63,  6, 48, 26,  4, 62,
       16, 36, 61, 10, 17, 39, 19, 14, 12,  0, 22, 41, 47, 59,  1, 30, 57,
        7, 32, 24, 40, 35,  9, 46, 21,  3, 56, 33, 23, 52, 29, 28, 25, 11,
       51, 13, 34, 27, 43, 60, 64,  2, 37, 55,  8, 42, 53, 15, 65],
      dtype=int64), 'cur_cost': 112334.0}}, {'individual': 11, 'strategy': 'explore', 'path_data': {'new_tour': [65, 62, 14, 19, 5, 22, 31, 16, 26, 0, 23, 28, 30, 27, 33, 8, 9, 1, 10, 11, 63, 52, 18, 34, 25, 7, 21, 13, 47, 50, 45, 41, 36, 40, 46, 37, 4, 60, 2, 6, 55, 15, 43, 39, 51, 44, 49, 32, 29, 12, 48, 35, 38, 20, 58, 59, 64, 53, 54, 57, 56, 61, 3, 17, 24, 42], 'cur_cost': 59801.0}}, {'individual': 12, 'strategy': 'explore', 'path_data': {'new_tour': [50, 39, 44, 41, 16, 35, 20, 28, 7, 53, 8, 17, 31, 13, 11, 63, 55, 62, 59, 5, 15, 36, 19, 2, 56, 12, 3, 54, 14, 26, 6, 65, 0, 64, 22, 24, 34, 4, 58, 18, 29, 30, 49, 51, 42, 48, 45, 27, 1, 57, 21, 37, 33, 23, 25, 9, 61, 52, 10, 32, 43, 40, 46, 47, 38, 60], 'cur_cost': 71307.0}}, {'individual': 13, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 5, 47, 28,  8,  0, 41, 61, 33, 13, 29, 60, 22, 65, 19, 62, 42,  3,
       26,  9, 16, 21, 10, 49, 45, 17, 52, 20, 57, 53, 18, 25, 30, 11, 44,
       46,  2, 34, 36, 35, 56, 38, 48, 31,  1, 59,  7, 24, 15, 64, 14, 40,
       54, 37, 39, 23, 55, 51, 27, 43, 63,  6, 12, 50, 58, 32,  4],
      dtype=int64), 'cur_cost': 115430.0}}, {'individual': 14, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 6, 5, 4, 8, 2, 11, 9, 3, 7, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10419.0}}, {'individual': 15, 'strategy': 'explore', 'path_data': {'new_tour': [17, 65, 60, 62, 27, 57, 0, 36, 40, 6, 14, 56, 18, 26, 30, 49, 50, 34, 38, 53, 35, 45, 61, 59, 23, 20, 64, 28, 39, 12, 52, 63, 11, 44, 43, 3, 10, 13, 22, 31, 29, 51, 7, 15, 37, 19, 21, 1, 2, 55, 47, 42, 32, 41, 16, 54, 58, 24, 33, 8, 48, 9, 4, 46, 5, 25], 'cur_cost': 99546.0}}, {'individual': 16, 'strategy': 'exploit', 'path_data': {'new_tour': array([38, 20, 23, 46, 32, 33,  6, 54, 60, 41, 26, 55, 50, 63, 43,  5, 24,
        9, 51, 11, 52, 37, 44, 45, 28,  8, 49, 34, 12, 18, 57, 35, 13, 19,
       31, 39, 21, 53,  1,  2, 59, 30,  7, 42, 14, 58, 36, 17, 47,  4, 10,
       16, 65, 40, 15, 64, 48, 29, 22, 61, 27,  3, 62,  0, 25, 56],
      dtype=int64), 'cur_cost': 117959.0}}, {'individual': 17, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 21, 8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12885.0}}, {'individual': 18, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 22, 11, 9, 3, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 23, 16, 18, 12, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12761.0}}, {'individual': 19, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 3, 35, 47, 38, 15,  2, 61, 25, 41,  7, 12, 59, 16, 46, 11, 64, 37,
       24, 22, 27,  9,  1,  6, 19,  0, 51, 62, 58, 34, 26, 45, 28,  4, 23,
       14, 56, 10, 50, 17, 53, 32, 43, 60, 39, 63,  5, 52, 65, 30, 42, 21,
       55, 44,  8, 57, 18, 40, 31, 49, 54, 20, 29, 36, 48, 13, 33],
      dtype=int64), 'cur_cost': 110843.0}}]
2025-08-03 16:14:53,103 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-03 16:14:53,103 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:14:53,121 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=10419.000, 多样性=0.976
2025-08-03 16:14:53,121 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-03 16:14:53,121 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-03 16:14:53,122 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-03 16:14:53,123 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 1, 'total_iterations': 2, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.00047477778437216743, 'best_improvement': 0.15717521436660734}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.012660322714108344}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 11, 'new_count': 11, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.687603305785124, 'new_diversity': 0.687603305785124, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-03 16:14:53,125 - main - INFO - --- Finished Evolution Iteration 2 ---
2025-08-03 16:14:53,128 - main - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_solution.json
2025-08-03 16:14:53,129 - main - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_20250803_161453.solution
2025-08-03 16:14:53,129 - main - INFO - 实例 composite13_66 处理完成
