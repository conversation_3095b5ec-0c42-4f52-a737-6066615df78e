2025-08-01 10:18:17,383 - __main__ - INFO - simple1_9 开始进化第 1 代
2025-08-01 10:18:17,383 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-01 10:18:17,384 - StatsExpert - INFO - 开始统计分析
2025-08-01 10:18:17,384 - StatsExpert - INFO - 统计分析完成: 种群大小=6, 最优成本=681.0, 多样性=0.785
2025-08-01 10:18:17,385 - PathExpert - INFO - 开始路径结构分析
2025-08-01 10:18:17,386 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.104
2025-08-01 10:18:17,386 - EliteExpert - INFO - 开始精英解分析
2025-08-01 10:18:17,388 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/2)
2025-08-01 10:18:17,388 - LandscapeExpert - INFO - 从统计报告生成模拟数据: 6个路径, 6个适应度值
2025-08-01 10:18:17,389 - LandscapeExpert - INFO - 数据提取成功: 6个路径, 6个适应度值
2025-08-01 10:18:17,603 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-01 10:18:17,604 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-01 10:18:17,672 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记
2025-08-01 10:18:17,986 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_1_20250801_101817.html
2025-08-01 10:18:18,058 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_1_20250801_101817.html
2025-08-01 10:18:18,058 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-01 10:18:18,058 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-01 10:18:18,058 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.6697秒
2025-08-01 10:18:18,059 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 2, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754014697.6034334, 'performance_metrics': {}}}
2025-08-01 10:18:18,059 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-01 10:18:18,060 - StrategyExpert - INFO - 开始策略分配分析
2025-08-01 10:18:18,060 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 6 individuals
  • diversity: 0.5
  • best_cost: 681.0
  • mean_cost: 1035.17
- Search-space: ruggedness 0.5, modality uni-modal, deceptiveness low
- Evolution phase: exploration
- Landscape focus suggestion: exploration
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 1): [{"type": "unexplored_space", "potential": "high", "description": "未充分探索的搜索空间", "recommendation": "增加探索性操作"}]
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-08-01 10:18:18,061 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-08-01 10:18:18,062 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-01 10:18:19,717 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.80,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit"
  },
  "rationale": "Focus on exploration due to unexplored search space and landscape focus suggestion. High global explore ratio is needed. Assign most indiviuals to explore."
}
```
2025-08-01 10:18:19,717 - utils.utils - INFO - 成功从JSON中提取数据
2025-08-01 10:18:19,717 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-01 10:18:19,717 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-01 10:18:19,718 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.80,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit"
  },
  "rationale": "Focus on exploration due to unexplored search space and landscape focus suggestion. High global explore ratio is needed. Assign most indiviuals to explore."
}
```
2025-08-01 10:18:19,718 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-01 10:18:19,718 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-01 10:18:19,718 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.80,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit"
  },
  "rationale": "Focus on exploration due to unexplored search space and landscape focus suggestion. High global explore ratio is needed. Assign most indiviuals to explore."
}
```
2025-08-01 10:18:19,719 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-01 10:18:19,719 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-01 10:18:19,719 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-01 10:18:19,720 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:18:19,720 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-01 10:18:19,720 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:18:19,901 - ExplorationExpert - INFO - 探索路径生成完成，成本: 912.0, 路径长度: 9
2025-08-01 10:18:19,901 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [2, 7, 0, 4, 8, 5, 3, 6, 1], 'cur_cost': 912.0}
2025-08-01 10:18:19,902 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-01 10:18:19,902 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-01 10:18:19,902 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:18:19,903 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-01 10:18:19,903 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:18:19,903 - ExplorationExpert - INFO - 探索路径生成完成，成本: 776.0, 路径长度: 9
2025-08-01 10:18:19,904 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [5, 7, 3, 4, 2, 8, 0, 1, 6], 'cur_cost': 776.0}
2025-08-01 10:18:19,904 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-01 10:18:19,905 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-01 10:18:19,907 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:18:19,908 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-01 10:18:19,909 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:18:19,909 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1123.0, 路径长度: 9
2025-08-01 10:18:19,910 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [5, 0, 2, 6, 7, 4, 8, 3, 1], 'cur_cost': 1123.0}
2025-08-01 10:18:19,910 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-01 10:18:19,910 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-01 10:18:19,910 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:18:19,911 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-01 10:18:19,911 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:18:19,911 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1086.0, 路径长度: 9
2025-08-01 10:18:19,911 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [2, 6, 8, 5, 3, 1, 7, 4, 0], 'cur_cost': 1086.0}
2025-08-01 10:18:19,911 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-01 10:18:19,912 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-01 10:18:19,912 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:18:19,912 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-01 10:18:19,913 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:18:19,913 - ExplorationExpert - INFO - 探索路径生成完成，成本: 826.0, 路径长度: 9
2025-08-01 10:18:19,913 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [7, 8, 5, 6, 3, 4, 2, 0, 1], 'cur_cost': 826.0}
2025-08-01 10:18:19,914 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-01 10:18:19,914 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:18:19,917 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:18:19,918 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 965.0
2025-08-01 10:18:22,030 - ExploitationExpert - INFO - res_population_num: 1
2025-08-01 10:18:22,031 - ExploitationExpert - INFO - res_population_costs: [734.0]
2025-08-01 10:18:22,031 - ExploitationExpert - INFO - res_populations: [array([0, 1, 2, 4, 8, 3, 6, 5, 7], dtype=int64)]
2025-08-01 10:18:22,032 - ExploitationExpert - INFO - populations_num: 6
2025-08-01 10:18:22,032 - ExploitationExpert - INFO - populations: [{'tour': [2, 7, 0, 4, 8, 5, 3, 6, 1], 'cur_cost': 912.0}, {'tour': [5, 7, 3, 4, 2, 8, 0, 1, 6], 'cur_cost': 776.0}, {'tour': [5, 0, 2, 6, 7, 4, 8, 3, 1], 'cur_cost': 1123.0}, {'tour': [2, 6, 8, 5, 3, 1, 7, 4, 0], 'cur_cost': 1086.0}, {'tour': [7, 8, 5, 6, 3, 4, 2, 0, 1], 'cur_cost': 826.0}, {'tour': array([1, 3, 2, 8, 5, 6, 0, 7, 4], dtype=int64), 'cur_cost': 965.0}]
2025-08-01 10:18:22,033 - ExploitationExpert - INFO - 局部搜索耗时: 2.12秒
2025-08-01 10:18:22,033 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-01 10:18:22,033 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([1, 3, 2, 8, 5, 6, 0, 7, 4], dtype=int64), 'cur_cost': 965.0}
2025-08-01 10:18:22,034 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [2, 7, 0, 4, 8, 5, 3, 6, 1], 'cur_cost': 912.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [5, 7, 3, 4, 2, 8, 0, 1, 6], 'cur_cost': 776.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [5, 0, 2, 6, 7, 4, 8, 3, 1], 'cur_cost': 1123.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [2, 6, 8, 5, 3, 1, 7, 4, 0], 'cur_cost': 1086.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [7, 8, 5, 6, 3, 4, 2, 0, 1], 'cur_cost': 826.0}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([1, 3, 2, 8, 5, 6, 0, 7, 4], dtype=int64), 'cur_cost': 965.0}}]
2025-08-01 10:18:22,034 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-01 10:18:22,035 - StatsExpert - INFO - 开始统计分析
2025-08-01 10:18:22,035 - StatsExpert - INFO - 统计分析完成: 种群大小=6, 最优成本=776.0, 多样性=0.778
2025-08-01 10:18:22,035 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-01 10:18:22,035 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-01 10:18:22,035 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-01 10:18:22,036 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 80, 'iteration': 0, 'total_iterations': 2, 'cost_improvement': {'status': 'slight_improvement', 'improvement_rate': 0.005790641639771314, 'best_improvement': -0.1395007342143906}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.009433962264150815}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 1, 'new_count': 1, 'count_change': 0, 'old_best_cost': 734.0, 'new_best_cost': 734.0, 'quality_improvement': 0.0, 'old_diversity': 0.0, 'new_diversity': 0.0, 'diversity_change': 0.0}, 'suggestions': ['改进缓慢，建议调整策略平衡或增加扰动强度', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-01 10:18:22,036 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-01 10:18:22,037 - __main__ - INFO - simple1_9 开始进化第 2 代
2025-08-01 10:18:22,037 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-01 10:18:22,038 - StatsExpert - INFO - 开始统计分析
2025-08-01 10:18:22,040 - StatsExpert - INFO - 统计分析完成: 种群大小=6, 最优成本=776.0, 多样性=0.778
2025-08-01 10:18:22,042 - PathExpert - INFO - 开始路径结构分析
2025-08-01 10:18:22,044 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.089
2025-08-01 10:18:22,044 - EliteExpert - INFO - 开始精英解分析
2025-08-01 10:18:22,044 - EliteExpert - INFO - 精英解分析完成: 精英解数量=1, 多样性=0.000
2025-08-01 10:18:22,047 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/2)
2025-08-01 10:18:22,047 - LandscapeExpert - INFO - 从统计报告生成模拟数据: 6个路径, 6个适应度值
2025-08-01 10:18:22,048 - LandscapeExpert - INFO - 数据提取成功: 6个路径, 6个适应度值
2025-08-01 10:18:22,051 - core.algorithms.fitness_landscape_analyzer - ERROR - 景观分析失败: '>' not supported between instances of 'str' and 'int'
2025-08-01 10:18:22,052 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-01 10:18:22,052 - LandscapeExpert - INFO - 提取到 1 个精英解
2025-08-01 10:18:22,058 - visualization.landscape_visualizer - ERROR - 种群映射到2D失败: n_components=2 must be between 0 and min(n_samples, n_features)=1 with svd_solver='full'
2025-08-01 10:18:22,059 - visualization.landscape_visualizer - INFO - 已添加 1 个精英解标记
2025-08-01 10:18:22,163 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_2_20250801_101822.html
2025-08-01 10:18:22,225 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_2_20250801_101822.html
2025-08-01 10:18:22,225 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 2
2025-08-01 10:18:22,225 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-01 10:18:22,225 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1781秒
2025-08-01 10:18:22,226 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 1, 'total': 2, 'progress': 0.5}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754014702.0521514, 'performance_metrics': {}}}
2025-08-01 10:18:22,226 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-01 10:18:22,226 - StrategyExpert - INFO - 开始策略分配分析
2025-08-01 10:18:22,227 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 6 individuals
  • diversity: 0.5
  • best_cost: 776.0
  • mean_cost: 948.0
- Search-space: ruggedness 0.5, modality uni-modal, deceptiveness low
- Evolution phase: exploitation
- Landscape focus suggestion: exploitation
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 1): [{"type": "unexplored_space", "potential": "high", "description": "未充分探索的搜索空间", "recommendation": "增加探索性操作"}]
- Last-iteration feedback: {'overall_score': 80, 'iteration': 0, 'total_iterations': 2, 'cost_improvement': {'status': 'slight_improvement', 'impro...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-08-01 10:18:22,228 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-08-01 10:18:22,228 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-01 10:18:23,570 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.40,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "explore",
    "4": "explore",
    "5": "explore"
  },
  "rationale": "Exploitation phase with a slight diversity. Focus on exploring the unexplored space as suggested."
}
```
2025-08-01 10:18:23,572 - utils.utils - INFO - 成功从JSON中提取数据
2025-08-01 10:18:23,574 - StrategyExpert - INFO - 策略分配完成: ['exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-01 10:18:23,575 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-01 10:18:23,575 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.40,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "explore",
    "4": "explore",
    "5": "explore"
  },
  "rationale": "Exploitation phase with a slight diversity. Focus on exploring the unexplored space as suggested."
}
```
2025-08-01 10:18:23,576 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-01 10:18:23,577 - __main__ - INFO - 策略分配: ['exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-01 10:18:23,577 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.40,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "explore",
    "4": "explore",
    "5": "explore"
  },
  "rationale": "Exploitation phase with a slight diversity. Focus on exploring the unexplored space as suggested."
}
```
2025-08-01 10:18:23,578 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-01 10:18:23,578 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-01 10:18:23,578 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:18:23,579 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:18:23,579 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 1012.0
2025-08-01 10:18:26,002 - ExploitationExpert - INFO - res_population_num: 2
2025-08-01 10:18:26,002 - ExploitationExpert - INFO - res_population_costs: [734.0, 680.0]
2025-08-01 10:18:26,002 - ExploitationExpert - INFO - res_populations: [array([0, 1, 2, 4, 8, 3, 6, 5, 7], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64)]
2025-08-01 10:18:26,003 - ExploitationExpert - INFO - populations_num: 6
2025-08-01 10:18:26,004 - ExploitationExpert - INFO - populations: [{'tour': array([2, 1, 5, 0, 4, 6, 3, 7, 8], dtype=int64), 'cur_cost': 1012.0}, {'tour': [5, 7, 3, 4, 2, 8, 0, 1, 6], 'cur_cost': 776.0}, {'tour': [5, 0, 2, 6, 7, 4, 8, 3, 1], 'cur_cost': 1123.0}, {'tour': [2, 6, 8, 5, 3, 1, 7, 4, 0], 'cur_cost': 1086.0}, {'tour': [7, 8, 5, 6, 3, 4, 2, 0, 1], 'cur_cost': 826.0}, {'tour': [1, 3, 2, 8, 5, 6, 0, 7, 4], 'cur_cost': 965.0}]
2025-08-01 10:18:26,007 - ExploitationExpert - INFO - 局部搜索耗时: 2.43秒
2025-08-01 10:18:26,008 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-01 10:18:26,009 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([2, 1, 5, 0, 4, 6, 3, 7, 8], dtype=int64), 'cur_cost': 1012.0}
2025-08-01 10:18:26,009 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-01 10:18:26,009 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:18:26,010 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:18:26,010 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 1022.0
2025-08-01 10:18:26,619 - ExploitationExpert - INFO - res_population_num: 2
2025-08-01 10:18:26,619 - ExploitationExpert - INFO - res_population_costs: [734.0, 680.0]
2025-08-01 10:18:26,619 - ExploitationExpert - INFO - res_populations: [array([0, 1, 2, 4, 8, 3, 6, 5, 7], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64)]
2025-08-01 10:18:26,620 - ExploitationExpert - INFO - populations_num: 6
2025-08-01 10:18:26,620 - ExploitationExpert - INFO - populations: [{'tour': array([2, 1, 5, 0, 4, 6, 3, 7, 8], dtype=int64), 'cur_cost': 1012.0}, {'tour': array([3, 6, 8, 0, 1, 2, 7, 5, 4], dtype=int64), 'cur_cost': 1022.0}, {'tour': [5, 0, 2, 6, 7, 4, 8, 3, 1], 'cur_cost': 1123.0}, {'tour': [2, 6, 8, 5, 3, 1, 7, 4, 0], 'cur_cost': 1086.0}, {'tour': [7, 8, 5, 6, 3, 4, 2, 0, 1], 'cur_cost': 826.0}, {'tour': [1, 3, 2, 8, 5, 6, 0, 7, 4], 'cur_cost': 965.0}]
2025-08-01 10:18:26,622 - ExploitationExpert - INFO - 局部搜索耗时: 0.61秒
2025-08-01 10:18:26,622 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-01 10:18:26,623 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([3, 6, 8, 0, 1, 2, 7, 5, 4], dtype=int64), 'cur_cost': 1022.0}
2025-08-01 10:18:26,623 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-01 10:18:26,623 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:18:26,624 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:18:26,624 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 939.0
2025-08-01 10:18:26,675 - ExploitationExpert - INFO - res_population_num: 4
2025-08-01 10:18:26,676 - ExploitationExpert - INFO - res_population_costs: [734.0, 680.0, 680.0, 680]
2025-08-01 10:18:26,676 - ExploitationExpert - INFO - res_populations: [array([0, 1, 2, 4, 8, 3, 6, 5, 7], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-01 10:18:26,677 - ExploitationExpert - INFO - populations_num: 6
2025-08-01 10:18:26,677 - ExploitationExpert - INFO - populations: [{'tour': array([2, 1, 5, 0, 4, 6, 3, 7, 8], dtype=int64), 'cur_cost': 1012.0}, {'tour': array([3, 6, 8, 0, 1, 2, 7, 5, 4], dtype=int64), 'cur_cost': 1022.0}, {'tour': array([4, 0, 6, 1, 3, 7, 8, 5, 2], dtype=int64), 'cur_cost': 939.0}, {'tour': [2, 6, 8, 5, 3, 1, 7, 4, 0], 'cur_cost': 1086.0}, {'tour': [7, 8, 5, 6, 3, 4, 2, 0, 1], 'cur_cost': 826.0}, {'tour': [1, 3, 2, 8, 5, 6, 0, 7, 4], 'cur_cost': 965.0}]
2025-08-01 10:18:26,678 - ExploitationExpert - INFO - 局部搜索耗时: 0.05秒
2025-08-01 10:18:26,679 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-01 10:18:26,679 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([4, 0, 6, 1, 3, 7, 8, 5, 2], dtype=int64), 'cur_cost': 939.0}
2025-08-01 10:18:26,679 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-01 10:18:26,680 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-01 10:18:26,680 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:18:26,681 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-01 10:18:26,681 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:18:26,682 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1014.0, 路径长度: 9
2025-08-01 10:18:26,682 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [1, 8, 3, 4, 6, 5, 7, 0, 2], 'cur_cost': 1014.0}
2025-08-01 10:18:26,683 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-01 10:18:26,683 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-01 10:18:26,683 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:18:26,684 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-01 10:18:26,684 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:18:26,684 - ExplorationExpert - INFO - 探索路径生成完成，成本: 969.0, 路径长度: 9
2025-08-01 10:18:26,685 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [3, 5, 6, 8, 4, 0, 7, 2, 1], 'cur_cost': 969.0}
2025-08-01 10:18:26,686 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-01 10:18:26,686 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-01 10:18:26,686 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:18:26,687 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-01 10:18:26,687 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:18:26,687 - ExplorationExpert - INFO - 探索路径生成完成，成本: 975.0, 路径长度: 9
2025-08-01 10:18:26,687 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [4, 5, 3, 1, 6, 7, 0, 2, 8], 'cur_cost': 975.0}
2025-08-01 10:18:26,688 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 1, 5, 0, 4, 6, 3, 7, 8], dtype=int64), 'cur_cost': 1012.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 6, 8, 0, 1, 2, 7, 5, 4], dtype=int64), 'cur_cost': 1022.0}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 0, 6, 1, 3, 7, 8, 5, 2], dtype=int64), 'cur_cost': 939.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [1, 8, 3, 4, 6, 5, 7, 0, 2], 'cur_cost': 1014.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [3, 5, 6, 8, 4, 0, 7, 2, 1], 'cur_cost': 969.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [4, 5, 3, 1, 6, 7, 0, 2, 8], 'cur_cost': 975.0}}]
2025-08-01 10:18:26,688 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-01 10:18:26,688 - StatsExpert - INFO - 开始统计分析
2025-08-01 10:18:26,689 - StatsExpert - INFO - 统计分析完成: 种群大小=6, 最优成本=939.0, 多样性=0.778
2025-08-01 10:18:26,689 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-01 10:18:26,689 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-01 10:18:26,690 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-01 10:18:26,690 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 1, 'total_iterations': 2, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.05427846613853457, 'best_improvement': -0.21005154639175258}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.0}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.5925925925925926, 'new_diversity': 0.5925925925925926, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-01 10:18:26,690 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-01 10:18:26,735 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_solution.json
2025-08-01 10:18:26,736 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_20250801_101826.solution
2025-08-01 10:18:26,737 - __main__ - INFO - 实例 simple1_9 处理完成
