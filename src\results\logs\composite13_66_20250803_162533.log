2025-08-03 16:25:33,741 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-08-03 16:25:33,742 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-03 16:25:33,745 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:25:33,761 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=9903.000, 多样性=0.973
2025-08-03 16:25:33,766 - PathExpert - INFO - 开始路径结构分析
2025-08-03 16:25:33,773 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.973
2025-08-03 16:25:33,796 - EliteExpert - INFO - 开始精英解分析
2025-08-03 16:25:33,802 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/1)
2025-08-03 16:25:33,802 - LandscapeExpert - INFO - 使用直接传入的种群数据: 20个个体
2025-08-03 16:25:33,803 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-03 16:25:33,803 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-03 16:25:34,091 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.150, 适应度梯度: -2427.050, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 1.000
2025-08-03 16:25:34,091 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-03 16:25:34,092 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-03 16:25:34,166 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记
2025-08-03 16:25:34,498 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_1_20250803_162534.html
2025-08-03 16:25:34,575 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_1_20250803_162534.html
2025-08-03 16:25:34,575 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-03 16:25:34,576 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-03 16:25:34,576 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.7747秒
2025-08-03 16:25:34,576 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-03 16:25:34,577 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.15, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -2427.0500000000006, 'local_optima_density': 0.15, 'gradient_variance': 2603989195.4635, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.002, 'fitness_entropy': 0.8941881858672537, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -2427.050)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 1, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754209534.091099, 'performance_metrics': {}}}
2025-08-03 16:25:34,577 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-03 16:25:34,578 - experts.strategy.enhanced_strategy_expert - INFO - 开始增强策略分配分析 (迭代 0)
2025-08-03 16:25:34,578 - experts.analysis.individual_state_analyzer - INFO - 开始分析种群状态 (迭代 0, 种群大小: 20)
2025-08-03 16:25:34,579 - experts.strategy.enhanced_strategy_expert - ERROR - 策略分配过程中发生错误: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:25:34,579 - experts.strategy.enhanced_strategy_expert - WARNING - 使用回退策略分配
2025-08-03 16:25:34,580 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 16:25:34,582 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:25:34,583 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-03 16:25:34,583 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 16:25:34,584 - __main__ - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:25:34,584 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-03 16:25:34,585 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 2, 3, 5} (总数: 4, 保护比例: 0.20)
2025-08-03 16:25:34,586 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-03 16:25:34,586 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-03 16:25:34,586 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:25:34,603 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:25:34,603 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:25:34,797 - ExplorationExpert - INFO - 探索路径生成完成，成本: 51550.0, 路径长度: 66
2025-08-03 16:25:34,798 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [13, 12, 37, 31, 14, 7, 65, 64, 63, 9, 57, 59, 23, 32, 33, 11, 52, 2, 55, 4, 62, 22, 27, 25, 15, 1, 60, 10, 24, 21, 40, 41, 20, 16, 17, 35, 30, 18, 19, 34, 29, 26, 49, 51, 38, 39, 42, 47, 44, 45, 46, 6, 61, 54, 5, 0, 8, 58, 3, 56, 53, 43, 48, 50, 36, 28], 'cur_cost': 51550.0}
2025-08-03 16:25:34,798 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 51550.00)
2025-08-03 16:25:34,799 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-03 16:25:34,799 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:25:34,802 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:25:34,803 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 108303.0
2025-08-03 16:25:36,945 - ExploitationExpert - INFO - res_population_num: 1
2025-08-03 16:25:36,945 - ExploitationExpert - INFO - res_population_costs: [9844.0]
2025-08-03 16:25:36,946 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  6,  2,  8,  5,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64)]
2025-08-03 16:25:36,950 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:25:36,950 - ExploitationExpert - INFO - populations: [{'tour': [13, 12, 37, 31, 14, 7, 65, 64, 63, 9, 57, 59, 23, 32, 33, 11, 52, 2, 55, 4, 62, 22, 27, 25, 15, 1, 60, 10, 24, 21, 40, 41, 20, 16, 17, 35, 30, 18, 19, 34, 29, 26, 49, 51, 38, 39, 42, 47, 44, 45, 46, 6, 61, 54, 5, 0, 8, 58, 3, 56, 53, 43, 48, 50, 36, 28], 'cur_cost': 51550.0}, {'tour': array([54, 31, 42, 58, 26, 32, 14, 39, 34, 20, 44, 24, 33,  8,  5, 62, 53,
        1, 45, 10, 52, 59, 21, 35,  6, 11, 25,  4, 27, 46,  7, 16, 55, 60,
       63, 19, 18, 36, 22, 13, 57, 37, 43,  0, 30, 47, 38, 65, 40, 17, 28,
       50, 12, 61, 56, 29,  3, 41, 64, 15, 23, 51,  9, 48,  2, 49],
      dtype=int64), 'cur_cost': 108303.0}, {'tour': array([48, 49, 40, 43, 46, 47, 39, 44, 45, 38, 51, 50, 41, 42, 21, 20, 13,
       23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30,
       34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,  2,  6, 10,
        0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63],
      dtype=int64), 'cur_cost': 9971.0}, {'tour': array([37, 25, 26, 36, 27, 31, 33, 28, 30, 35, 34, 32, 29, 24, 18, 16, 23,
       22, 12, 17, 15, 14, 20, 21, 13, 19,  9, 11,  7,  3,  1,  0, 10,  8,
        2,  6,  4,  5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9936.0}, {'tour': array([53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,
        4,  6,  9, 11,  7,  3,  1,  0, 10, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29,
       32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42],
      dtype=int64), 'cur_cost': 10049.0}, {'tour': array([32, 28, 30, 35, 34, 26, 25, 31, 33, 29, 24, 37, 36, 27, 18, 16, 23,
       22, 12, 17, 15, 14, 20, 21, 13, 19,  9, 11,  7,  3,  1,  0, 10,  8,
        2,  6,  4,  5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9903.0}, {'tour': array([60, 57, 62,  7, 45, 25, 28, 49, 12, 20,  8, 31, 22, 44, 48,  9, 58,
       41, 39, 59, 54, 13, 21,  0, 55, 52, 27, 56, 17, 10, 15, 65, 34, 51,
       29, 36,  3, 14, 40,  4,  2, 43, 33, 30, 50, 46, 47, 16, 23, 32, 63,
       18, 53,  5, 38, 42,  6,  1, 24, 35, 37, 19, 26, 61, 11, 64],
      dtype=int64), 'cur_cost': 87514.0}, {'tour': array([ 1, 56, 13, 49, 63, 20, 54, 26, 61,  2, 14,  4,  9, 15, 44, 24, 38,
       36, 48, 65, 41, 46, 47,  6, 12, 16, 64, 53, 17, 23, 35, 55, 62, 30,
       34, 18, 37, 40, 32, 50,  7,  3, 58,  0, 39, 51, 31, 11, 43, 25, 29,
       57, 27, 10, 52, 19, 21, 45, 60, 33,  5,  8, 59, 28, 22, 42],
      dtype=int64), 'cur_cost': 106253.0}, {'tour': array([42, 18, 25, 37,  0, 50, 57, 61, 19, 12, 13, 41, 52, 16, 32, 15, 38,
       63, 10,  9, 28,  1, 64, 55, 22, 59,  5, 43, 27,  3, 34, 56, 30,  8,
       53, 51, 20,  6, 35, 24, 58, 36, 65, 21, 17,  7, 46, 23, 40,  4, 47,
       49, 29, 62,  2, 11, 33, 26, 45, 39, 54, 60, 48, 44, 14, 31],
      dtype=int64), 'cur_cost': 104223.0}, {'tour': array([54,  7, 36, 26, 32, 22, 33, 27,  6, 19, 16, 47, 50,  4, 23, 51, 56,
       14, 44, 53, 37, 13, 18,  5, 17,  9, 40, 49, 20, 62,  0, 10, 61, 57,
       39, 55, 41,  8, 12, 34, 28, 11, 24, 21, 15, 38,  3,  1, 59, 42, 30,
       48, 63, 35, 29, 31, 43, 25,  2, 60, 52, 46, 65, 45, 64, 58],
      dtype=int64), 'cur_cost': 95544.0}, {'tour': array([12, 15, 30, 11, 53, 51, 26, 46, 34, 40, 18, 17,  8, 42, 29,  2, 48,
       58, 23, 38, 55, 61, 45, 14, 27,  1, 65, 20, 35, 44, 54,  9,  7,  0,
       31, 21, 32, 49, 36, 13, 24, 62, 50, 52, 39, 57,  5, 33, 28, 63, 10,
       41, 47, 25, 43,  6, 59, 19,  4, 16, 64, 22, 60, 37,  3, 56],
      dtype=int64), 'cur_cost': 114903.0}, {'tour': array([43, 41, 48, 40,  6, 50, 12, 62, 10,  3, 28, 59, 49, 55, 39, 32, 45,
       65, 47, 42, 60, 64, 36,  0, 54,  7, 23, 33, 61, 37,  8,  4, 63, 52,
       25, 19, 35, 26,  1, 58, 20, 38, 27, 56, 13, 29, 24, 34, 17, 30, 21,
       44, 14, 57,  5, 46, 22, 31, 15, 18, 11, 51, 16,  2,  9, 53],
      dtype=int64), 'cur_cost': 100525.0}, {'tour': array([60, 32,  1, 15, 36, 46, 19, 41,  4, 65,  5, 33,  9, 21, 27, 39, 28,
       16, 12, 26, 17, 38, 62,  2, 20, 42, 40, 13, 24, 29, 18, 48, 11, 59,
       64, 10, 52, 50, 35, 23, 25, 44, 30, 51, 31, 22, 61, 34, 54, 53, 43,
       47,  3,  0, 14, 56, 55, 58, 45, 37,  6, 49,  8,  7, 63, 57],
      dtype=int64), 'cur_cost': 96623.0}, {'tour': array([42, 25, 45,  7, 49, 30, 47, 64,  3,  2, 52, 24, 13, 27, 28, 38, 63,
        0, 46, 40, 48, 56, 51, 23, 32, 37, 57, 29,  9, 53, 36,  4, 61, 31,
       19, 21,  1, 16, 39, 41,  5, 12, 22, 11, 18, 65, 54,  8, 62, 44, 55,
       26, 43, 17, 58, 14, 10, 15, 34, 50, 35, 20, 60, 33, 59,  6],
      dtype=int64), 'cur_cost': 110501.0}, {'tour': array([56, 14, 17, 50,  2, 32, 33, 46,  8, 34, 29, 37, 11, 10, 53, 27, 20,
       45,  7, 55, 64,  9, 42, 44, 12, 47, 63, 59, 35, 58,  3,  4, 57, 30,
       39, 52, 51, 36, 28,  1, 19, 31, 18,  0,  6, 23, 60, 54,  5, 21, 40,
       62, 41, 16, 13, 15, 22, 25, 65, 26, 49, 24, 61, 43, 38, 48],
      dtype=int64), 'cur_cost': 98454.0}, {'tour': array([42, 48, 65, 28, 13, 15, 40, 62,  7, 33, 14, 27, 30, 29, 25, 44, 61,
       23, 41, 39,  4, 52, 45, 31, 51, 32,  2, 19,  8,  6, 36, 55, 56, 58,
       57,  3, 18,  1, 20, 64,  0, 60, 43, 24, 35, 54, 47, 37, 63, 22, 12,
       59,  5, 10,  9, 16, 53, 26, 11, 50, 21, 38, 46, 49, 17, 34],
      dtype=int64), 'cur_cost': 95999.0}, {'tour': array([10, 23, 52, 29, 36, 26, 38, 45, 51,  2, 60, 41, 32,  3,  7, 48, 37,
       58, 25, 59, 17, 54, 44, 18,  6,  1, 39, 53, 57, 34, 46, 63, 24, 12,
       28, 55,  8, 64,  5, 40, 35, 65, 20, 27,  0, 43, 11, 13, 14, 19, 31,
       33, 50,  9, 62, 21, 61, 42, 15, 56, 22, 30, 49,  4, 47, 16],
      dtype=int64), 'cur_cost': 115298.0}, {'tour': array([ 6, 30, 56, 39, 63, 20, 35, 23, 32, 38, 61,  0, 37,  8, 52, 40, 26,
       25, 17, 60, 54, 55, 16, 62, 14,  9, 57, 51,  7, 44, 31, 21, 29, 34,
       36, 53, 11, 45, 28, 49,  4, 65, 19, 50, 59, 64, 48,  2, 10, 42, 27,
        5, 18, 13, 33, 47, 41, 22,  1, 58, 43,  3, 46, 15, 24, 12],
      dtype=int64), 'cur_cost': 109896.0}, {'tour': array([31, 38,  8, 26, 33,  9, 53, 23, 55, 12, 63, 48, 54, 35, 25, 57, 50,
       17, 18, 24, 65, 62, 28, 56,  0, 42, 16, 45,  6,  1, 64, 29, 60, 37,
       58, 44, 39, 47, 15,  4, 32, 43, 36, 11,  7, 49, 27, 20, 21, 22, 52,
       51,  3, 46, 14, 30, 13, 61,  5, 59, 41, 40, 10,  2, 19, 34],
      dtype=int64), 'cur_cost': 110594.0}, {'tour': array([50,  6, 46,  3, 56, 52,  1, 11, 37, 27, 39, 47, 53, 49, 20, 41, 65,
       42, 59,  2, 18, 63, 29, 48, 40, 61, 17, 64, 13, 57, 35,  4, 30, 25,
       23, 43, 36, 55, 51,  9, 19,  0, 15, 45, 16, 54, 62, 31, 10, 60, 21,
       32, 28, 44, 14, 24, 12, 34,  7, 38, 22,  8,  5, 26, 33, 58],
      dtype=int64), 'cur_cost': 110726.0}]
2025-08-03 16:25:36,965 - ExploitationExpert - INFO - 局部搜索耗时: 2.16秒
2025-08-03 16:25:36,965 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-03 16:25:36,966 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([54, 31, 42, 58, 26, 32, 14, 39, 34, 20, 44, 24, 33,  8,  5, 62, 53,
        1, 45, 10, 52, 59, 21, 35,  6, 11, 25,  4, 27, 46,  7, 16, 55, 60,
       63, 19, 18, 36, 22, 13, 57, 37, 43,  0, 30, 47, 38, 65, 40, 17, 28,
       50, 12, 61, 56, 29,  3, 41, 64, 15, 23, 51,  9, 48,  2, 49],
      dtype=int64), 'cur_cost': 108303.0}
2025-08-03 16:25:36,967 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 108303.00)
2025-08-03 16:25:36,967 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-03 16:25:36,968 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-03 16:25:36,968 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:25:36,973 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:25:36,974 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:25:36,974 - ExplorationExpert - INFO - 探索路径生成完成，成本: 89494.0, 路径长度: 66
2025-08-03 16:25:36,974 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [37, 14, 65, 23, 2, 10, 40, 41, 35, 30, 19, 38, 39, 42, 47, 44, 45, 46, 61, 13, 4, 34, 36, 17, 11, 55, 51, 43, 59, 49, 7, 52, 27, 6, 33, 28, 50, 48, 56, 32, 26, 60, 5, 62, 3, 29, 1, 31, 18, 24, 8, 21, 22, 12, 54, 15, 0, 58, 53, 25, 64, 20, 16, 57, 63, 9], 'cur_cost': 89494.0}
2025-08-03 16:25:36,975 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 89494.00)
2025-08-03 16:25:36,975 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-03 16:25:36,975 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-03 16:25:36,975 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:25:36,981 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:25:36,983 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:25:36,985 - ExplorationExpert - INFO - 探索路径生成完成，成本: 113666.0, 路径长度: 66
2025-08-03 16:25:36,985 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [13, 65, 9, 19, 23, 47, 11, 55, 22, 17, 51, 49, 41, 6, 35, 50, 26, 42, 18, 45, 21, 61, 0, 4, 14, 34, 57, 7, 25, 52, 28, 53, 37, 20, 31, 15, 27, 2, 30, 59, 10, 1, 48, 58, 39, 32, 64, 56, 24, 46, 8, 54, 40, 33, 16, 38, 12, 63, 62, 36, 3, 29, 43, 5, 60, 44], 'cur_cost': 113666.0}
2025-08-03 16:25:36,987 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 113666.00)
2025-08-03 16:25:36,989 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-03 16:25:36,990 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:25:36,991 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:25:36,992 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 107087.0
2025-08-03 16:25:39,477 - ExploitationExpert - INFO - res_population_num: 2
2025-08-03 16:25:39,477 - ExploitationExpert - INFO - res_population_costs: [9844.0, 9548.0]
2025-08-03 16:25:39,477 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  6,  2,  8,  5,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56, 59, 62,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64)]
2025-08-03 16:25:39,479 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:25:39,479 - ExploitationExpert - INFO - populations: [{'tour': [13, 12, 37, 31, 14, 7, 65, 64, 63, 9, 57, 59, 23, 32, 33, 11, 52, 2, 55, 4, 62, 22, 27, 25, 15, 1, 60, 10, 24, 21, 40, 41, 20, 16, 17, 35, 30, 18, 19, 34, 29, 26, 49, 51, 38, 39, 42, 47, 44, 45, 46, 6, 61, 54, 5, 0, 8, 58, 3, 56, 53, 43, 48, 50, 36, 28], 'cur_cost': 51550.0}, {'tour': array([54, 31, 42, 58, 26, 32, 14, 39, 34, 20, 44, 24, 33,  8,  5, 62, 53,
        1, 45, 10, 52, 59, 21, 35,  6, 11, 25,  4, 27, 46,  7, 16, 55, 60,
       63, 19, 18, 36, 22, 13, 57, 37, 43,  0, 30, 47, 38, 65, 40, 17, 28,
       50, 12, 61, 56, 29,  3, 41, 64, 15, 23, 51,  9, 48,  2, 49],
      dtype=int64), 'cur_cost': 108303.0}, {'tour': [37, 14, 65, 23, 2, 10, 40, 41, 35, 30, 19, 38, 39, 42, 47, 44, 45, 46, 61, 13, 4, 34, 36, 17, 11, 55, 51, 43, 59, 49, 7, 52, 27, 6, 33, 28, 50, 48, 56, 32, 26, 60, 5, 62, 3, 29, 1, 31, 18, 24, 8, 21, 22, 12, 54, 15, 0, 58, 53, 25, 64, 20, 16, 57, 63, 9], 'cur_cost': 89494.0}, {'tour': [13, 65, 9, 19, 23, 47, 11, 55, 22, 17, 51, 49, 41, 6, 35, 50, 26, 42, 18, 45, 21, 61, 0, 4, 14, 34, 57, 7, 25, 52, 28, 53, 37, 20, 31, 15, 27, 2, 30, 59, 10, 1, 48, 58, 39, 32, 64, 56, 24, 46, 8, 54, 40, 33, 16, 38, 12, 63, 62, 36, 3, 29, 43, 5, 60, 44], 'cur_cost': 113666.0}, {'tour': array([43, 38, 40, 12, 35, 60, 32,  5, 20, 44, 34, 10, 54, 39, 49, 26, 42,
       23, 30,  8, 18, 27, 17, 28, 48, 58, 22, 46, 36, 53, 64, 14,  7, 51,
       25, 24, 62, 59,  0, 57, 16, 33,  1, 45,  4, 15, 19, 47,  2,  9,  6,
        3, 37, 11, 41, 56, 63, 31, 21, 50, 52, 13, 65, 29, 61, 55],
      dtype=int64), 'cur_cost': 107087.0}, {'tour': array([32, 28, 30, 35, 34, 26, 25, 31, 33, 29, 24, 37, 36, 27, 18, 16, 23,
       22, 12, 17, 15, 14, 20, 21, 13, 19,  9, 11,  7,  3,  1,  0, 10,  8,
        2,  6,  4,  5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9903.0}, {'tour': array([60, 57, 62,  7, 45, 25, 28, 49, 12, 20,  8, 31, 22, 44, 48,  9, 58,
       41, 39, 59, 54, 13, 21,  0, 55, 52, 27, 56, 17, 10, 15, 65, 34, 51,
       29, 36,  3, 14, 40,  4,  2, 43, 33, 30, 50, 46, 47, 16, 23, 32, 63,
       18, 53,  5, 38, 42,  6,  1, 24, 35, 37, 19, 26, 61, 11, 64],
      dtype=int64), 'cur_cost': 87514.0}, {'tour': array([ 1, 56, 13, 49, 63, 20, 54, 26, 61,  2, 14,  4,  9, 15, 44, 24, 38,
       36, 48, 65, 41, 46, 47,  6, 12, 16, 64, 53, 17, 23, 35, 55, 62, 30,
       34, 18, 37, 40, 32, 50,  7,  3, 58,  0, 39, 51, 31, 11, 43, 25, 29,
       57, 27, 10, 52, 19, 21, 45, 60, 33,  5,  8, 59, 28, 22, 42],
      dtype=int64), 'cur_cost': 106253.0}, {'tour': array([42, 18, 25, 37,  0, 50, 57, 61, 19, 12, 13, 41, 52, 16, 32, 15, 38,
       63, 10,  9, 28,  1, 64, 55, 22, 59,  5, 43, 27,  3, 34, 56, 30,  8,
       53, 51, 20,  6, 35, 24, 58, 36, 65, 21, 17,  7, 46, 23, 40,  4, 47,
       49, 29, 62,  2, 11, 33, 26, 45, 39, 54, 60, 48, 44, 14, 31],
      dtype=int64), 'cur_cost': 104223.0}, {'tour': array([54,  7, 36, 26, 32, 22, 33, 27,  6, 19, 16, 47, 50,  4, 23, 51, 56,
       14, 44, 53, 37, 13, 18,  5, 17,  9, 40, 49, 20, 62,  0, 10, 61, 57,
       39, 55, 41,  8, 12, 34, 28, 11, 24, 21, 15, 38,  3,  1, 59, 42, 30,
       48, 63, 35, 29, 31, 43, 25,  2, 60, 52, 46, 65, 45, 64, 58],
      dtype=int64), 'cur_cost': 95544.0}, {'tour': array([12, 15, 30, 11, 53, 51, 26, 46, 34, 40, 18, 17,  8, 42, 29,  2, 48,
       58, 23, 38, 55, 61, 45, 14, 27,  1, 65, 20, 35, 44, 54,  9,  7,  0,
       31, 21, 32, 49, 36, 13, 24, 62, 50, 52, 39, 57,  5, 33, 28, 63, 10,
       41, 47, 25, 43,  6, 59, 19,  4, 16, 64, 22, 60, 37,  3, 56],
      dtype=int64), 'cur_cost': 114903.0}, {'tour': array([43, 41, 48, 40,  6, 50, 12, 62, 10,  3, 28, 59, 49, 55, 39, 32, 45,
       65, 47, 42, 60, 64, 36,  0, 54,  7, 23, 33, 61, 37,  8,  4, 63, 52,
       25, 19, 35, 26,  1, 58, 20, 38, 27, 56, 13, 29, 24, 34, 17, 30, 21,
       44, 14, 57,  5, 46, 22, 31, 15, 18, 11, 51, 16,  2,  9, 53],
      dtype=int64), 'cur_cost': 100525.0}, {'tour': array([60, 32,  1, 15, 36, 46, 19, 41,  4, 65,  5, 33,  9, 21, 27, 39, 28,
       16, 12, 26, 17, 38, 62,  2, 20, 42, 40, 13, 24, 29, 18, 48, 11, 59,
       64, 10, 52, 50, 35, 23, 25, 44, 30, 51, 31, 22, 61, 34, 54, 53, 43,
       47,  3,  0, 14, 56, 55, 58, 45, 37,  6, 49,  8,  7, 63, 57],
      dtype=int64), 'cur_cost': 96623.0}, {'tour': array([42, 25, 45,  7, 49, 30, 47, 64,  3,  2, 52, 24, 13, 27, 28, 38, 63,
        0, 46, 40, 48, 56, 51, 23, 32, 37, 57, 29,  9, 53, 36,  4, 61, 31,
       19, 21,  1, 16, 39, 41,  5, 12, 22, 11, 18, 65, 54,  8, 62, 44, 55,
       26, 43, 17, 58, 14, 10, 15, 34, 50, 35, 20, 60, 33, 59,  6],
      dtype=int64), 'cur_cost': 110501.0}, {'tour': array([56, 14, 17, 50,  2, 32, 33, 46,  8, 34, 29, 37, 11, 10, 53, 27, 20,
       45,  7, 55, 64,  9, 42, 44, 12, 47, 63, 59, 35, 58,  3,  4, 57, 30,
       39, 52, 51, 36, 28,  1, 19, 31, 18,  0,  6, 23, 60, 54,  5, 21, 40,
       62, 41, 16, 13, 15, 22, 25, 65, 26, 49, 24, 61, 43, 38, 48],
      dtype=int64), 'cur_cost': 98454.0}, {'tour': array([42, 48, 65, 28, 13, 15, 40, 62,  7, 33, 14, 27, 30, 29, 25, 44, 61,
       23, 41, 39,  4, 52, 45, 31, 51, 32,  2, 19,  8,  6, 36, 55, 56, 58,
       57,  3, 18,  1, 20, 64,  0, 60, 43, 24, 35, 54, 47, 37, 63, 22, 12,
       59,  5, 10,  9, 16, 53, 26, 11, 50, 21, 38, 46, 49, 17, 34],
      dtype=int64), 'cur_cost': 95999.0}, {'tour': array([10, 23, 52, 29, 36, 26, 38, 45, 51,  2, 60, 41, 32,  3,  7, 48, 37,
       58, 25, 59, 17, 54, 44, 18,  6,  1, 39, 53, 57, 34, 46, 63, 24, 12,
       28, 55,  8, 64,  5, 40, 35, 65, 20, 27,  0, 43, 11, 13, 14, 19, 31,
       33, 50,  9, 62, 21, 61, 42, 15, 56, 22, 30, 49,  4, 47, 16],
      dtype=int64), 'cur_cost': 115298.0}, {'tour': array([ 6, 30, 56, 39, 63, 20, 35, 23, 32, 38, 61,  0, 37,  8, 52, 40, 26,
       25, 17, 60, 54, 55, 16, 62, 14,  9, 57, 51,  7, 44, 31, 21, 29, 34,
       36, 53, 11, 45, 28, 49,  4, 65, 19, 50, 59, 64, 48,  2, 10, 42, 27,
        5, 18, 13, 33, 47, 41, 22,  1, 58, 43,  3, 46, 15, 24, 12],
      dtype=int64), 'cur_cost': 109896.0}, {'tour': array([31, 38,  8, 26, 33,  9, 53, 23, 55, 12, 63, 48, 54, 35, 25, 57, 50,
       17, 18, 24, 65, 62, 28, 56,  0, 42, 16, 45,  6,  1, 64, 29, 60, 37,
       58, 44, 39, 47, 15,  4, 32, 43, 36, 11,  7, 49, 27, 20, 21, 22, 52,
       51,  3, 46, 14, 30, 13, 61,  5, 59, 41, 40, 10,  2, 19, 34],
      dtype=int64), 'cur_cost': 110594.0}, {'tour': array([50,  6, 46,  3, 56, 52,  1, 11, 37, 27, 39, 47, 53, 49, 20, 41, 65,
       42, 59,  2, 18, 63, 29, 48, 40, 61, 17, 64, 13, 57, 35,  4, 30, 25,
       23, 43, 36, 55, 51,  9, 19,  0, 15, 45, 16, 54, 62, 31, 10, 60, 21,
       32, 28, 44, 14, 24, 12, 34,  7, 38, 22,  8,  5, 26, 33, 58],
      dtype=int64), 'cur_cost': 110726.0}]
2025-08-03 16:25:39,492 - ExploitationExpert - INFO - 局部搜索耗时: 2.50秒
2025-08-03 16:25:39,493 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-03 16:25:39,493 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([43, 38, 40, 12, 35, 60, 32,  5, 20, 44, 34, 10, 54, 39, 49, 26, 42,
       23, 30,  8, 18, 27, 17, 28, 48, 58, 22, 46, 36, 53, 64, 14,  7, 51,
       25, 24, 62, 59,  0, 57, 16, 33,  1, 45,  4, 15, 19, 47,  2,  9,  6,
        3, 37, 11, 41, 56, 63, 31, 21, 50, 52, 13, 65, 29, 61, 55],
      dtype=int64), 'cur_cost': 107087.0}
2025-08-03 16:25:39,494 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 107087.00)
2025-08-03 16:25:39,494 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-03 16:25:39,494 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-03 16:25:39,495 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:25:39,501 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:25:39,502 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:25:39,502 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10468.0, 路径长度: 66
2025-08-03 16:25:39,502 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 8, 1, 5, 4, 6, 2, 10, 11, 9, 3, 7, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10468.0}
2025-08-03 16:25:39,503 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 10468.00)
2025-08-03 16:25:39,503 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-03 16:25:39,504 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-03 16:25:39,505 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:25:39,511 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:25:39,511 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:25:39,512 - ExplorationExpert - INFO - 探索路径生成完成，成本: 102321.0, 路径长度: 66
2025-08-03 16:25:39,512 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [65, 9, 31, 6, 2, 10, 22, 17, 3, 49, 52, 55, 45, 0, 25, 11, 14, 15, 59, 40, 53, 20, 23, 50, 19, 56, 32, 5, 62, 38, 42, 18, 21, 61, 54, 16, 8, 43, 26, 51, 60, 63, 34, 12, 27, 37, 4, 1, 36, 48, 47, 13, 33, 41, 44, 39, 30, 24, 7, 28, 46, 57, 35, 58, 64, 29], 'cur_cost': 102321.0}
2025-08-03 16:25:39,513 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 102321.00)
2025-08-03 16:25:39,514 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-03 16:25:39,517 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:25:39,518 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:25:39,520 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 83096.0
2025-08-03 16:25:40,198 - ExploitationExpert - INFO - res_population_num: 2
2025-08-03 16:25:40,198 - ExploitationExpert - INFO - res_population_costs: [9844.0, 9548.0]
2025-08-03 16:25:40,198 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  6,  2,  8,  5,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56, 59, 62,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64)]
2025-08-03 16:25:40,200 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:25:40,200 - ExploitationExpert - INFO - populations: [{'tour': [13, 12, 37, 31, 14, 7, 65, 64, 63, 9, 57, 59, 23, 32, 33, 11, 52, 2, 55, 4, 62, 22, 27, 25, 15, 1, 60, 10, 24, 21, 40, 41, 20, 16, 17, 35, 30, 18, 19, 34, 29, 26, 49, 51, 38, 39, 42, 47, 44, 45, 46, 6, 61, 54, 5, 0, 8, 58, 3, 56, 53, 43, 48, 50, 36, 28], 'cur_cost': 51550.0}, {'tour': array([54, 31, 42, 58, 26, 32, 14, 39, 34, 20, 44, 24, 33,  8,  5, 62, 53,
        1, 45, 10, 52, 59, 21, 35,  6, 11, 25,  4, 27, 46,  7, 16, 55, 60,
       63, 19, 18, 36, 22, 13, 57, 37, 43,  0, 30, 47, 38, 65, 40, 17, 28,
       50, 12, 61, 56, 29,  3, 41, 64, 15, 23, 51,  9, 48,  2, 49],
      dtype=int64), 'cur_cost': 108303.0}, {'tour': [37, 14, 65, 23, 2, 10, 40, 41, 35, 30, 19, 38, 39, 42, 47, 44, 45, 46, 61, 13, 4, 34, 36, 17, 11, 55, 51, 43, 59, 49, 7, 52, 27, 6, 33, 28, 50, 48, 56, 32, 26, 60, 5, 62, 3, 29, 1, 31, 18, 24, 8, 21, 22, 12, 54, 15, 0, 58, 53, 25, 64, 20, 16, 57, 63, 9], 'cur_cost': 89494.0}, {'tour': [13, 65, 9, 19, 23, 47, 11, 55, 22, 17, 51, 49, 41, 6, 35, 50, 26, 42, 18, 45, 21, 61, 0, 4, 14, 34, 57, 7, 25, 52, 28, 53, 37, 20, 31, 15, 27, 2, 30, 59, 10, 1, 48, 58, 39, 32, 64, 56, 24, 46, 8, 54, 40, 33, 16, 38, 12, 63, 62, 36, 3, 29, 43, 5, 60, 44], 'cur_cost': 113666.0}, {'tour': array([43, 38, 40, 12, 35, 60, 32,  5, 20, 44, 34, 10, 54, 39, 49, 26, 42,
       23, 30,  8, 18, 27, 17, 28, 48, 58, 22, 46, 36, 53, 64, 14,  7, 51,
       25, 24, 62, 59,  0, 57, 16, 33,  1, 45,  4, 15, 19, 47,  2,  9,  6,
        3, 37, 11, 41, 56, 63, 31, 21, 50, 52, 13, 65, 29, 61, 55],
      dtype=int64), 'cur_cost': 107087.0}, {'tour': [0, 8, 1, 5, 4, 6, 2, 10, 11, 9, 3, 7, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10468.0}, {'tour': [65, 9, 31, 6, 2, 10, 22, 17, 3, 49, 52, 55, 45, 0, 25, 11, 14, 15, 59, 40, 53, 20, 23, 50, 19, 56, 32, 5, 62, 38, 42, 18, 21, 61, 54, 16, 8, 43, 26, 51, 60, 63, 34, 12, 27, 37, 4, 1, 36, 48, 47, 13, 33, 41, 44, 39, 30, 24, 7, 28, 46, 57, 35, 58, 64, 29], 'cur_cost': 102321.0}, {'tour': array([ 6, 57, 56,  1, 41, 38, 62,  3, 51, 46, 27, 43, 16, 63, 55, 59, 47,
       42, 44, 61,  4, 52, 24, 20, 18, 48, 58, 21,  2, 36, 35, 10, 26, 31,
       12, 45,  0, 64, 50, 34, 32, 29, 19, 54,  7,  9, 23, 14, 22, 15, 28,
       25, 17, 53, 37, 33,  5, 60, 39, 40, 49, 30,  8, 11, 13, 65],
      dtype=int64), 'cur_cost': 83096.0}, {'tour': array([42, 18, 25, 37,  0, 50, 57, 61, 19, 12, 13, 41, 52, 16, 32, 15, 38,
       63, 10,  9, 28,  1, 64, 55, 22, 59,  5, 43, 27,  3, 34, 56, 30,  8,
       53, 51, 20,  6, 35, 24, 58, 36, 65, 21, 17,  7, 46, 23, 40,  4, 47,
       49, 29, 62,  2, 11, 33, 26, 45, 39, 54, 60, 48, 44, 14, 31],
      dtype=int64), 'cur_cost': 104223.0}, {'tour': array([54,  7, 36, 26, 32, 22, 33, 27,  6, 19, 16, 47, 50,  4, 23, 51, 56,
       14, 44, 53, 37, 13, 18,  5, 17,  9, 40, 49, 20, 62,  0, 10, 61, 57,
       39, 55, 41,  8, 12, 34, 28, 11, 24, 21, 15, 38,  3,  1, 59, 42, 30,
       48, 63, 35, 29, 31, 43, 25,  2, 60, 52, 46, 65, 45, 64, 58],
      dtype=int64), 'cur_cost': 95544.0}, {'tour': array([12, 15, 30, 11, 53, 51, 26, 46, 34, 40, 18, 17,  8, 42, 29,  2, 48,
       58, 23, 38, 55, 61, 45, 14, 27,  1, 65, 20, 35, 44, 54,  9,  7,  0,
       31, 21, 32, 49, 36, 13, 24, 62, 50, 52, 39, 57,  5, 33, 28, 63, 10,
       41, 47, 25, 43,  6, 59, 19,  4, 16, 64, 22, 60, 37,  3, 56],
      dtype=int64), 'cur_cost': 114903.0}, {'tour': array([43, 41, 48, 40,  6, 50, 12, 62, 10,  3, 28, 59, 49, 55, 39, 32, 45,
       65, 47, 42, 60, 64, 36,  0, 54,  7, 23, 33, 61, 37,  8,  4, 63, 52,
       25, 19, 35, 26,  1, 58, 20, 38, 27, 56, 13, 29, 24, 34, 17, 30, 21,
       44, 14, 57,  5, 46, 22, 31, 15, 18, 11, 51, 16,  2,  9, 53],
      dtype=int64), 'cur_cost': 100525.0}, {'tour': array([60, 32,  1, 15, 36, 46, 19, 41,  4, 65,  5, 33,  9, 21, 27, 39, 28,
       16, 12, 26, 17, 38, 62,  2, 20, 42, 40, 13, 24, 29, 18, 48, 11, 59,
       64, 10, 52, 50, 35, 23, 25, 44, 30, 51, 31, 22, 61, 34, 54, 53, 43,
       47,  3,  0, 14, 56, 55, 58, 45, 37,  6, 49,  8,  7, 63, 57],
      dtype=int64), 'cur_cost': 96623.0}, {'tour': array([42, 25, 45,  7, 49, 30, 47, 64,  3,  2, 52, 24, 13, 27, 28, 38, 63,
        0, 46, 40, 48, 56, 51, 23, 32, 37, 57, 29,  9, 53, 36,  4, 61, 31,
       19, 21,  1, 16, 39, 41,  5, 12, 22, 11, 18, 65, 54,  8, 62, 44, 55,
       26, 43, 17, 58, 14, 10, 15, 34, 50, 35, 20, 60, 33, 59,  6],
      dtype=int64), 'cur_cost': 110501.0}, {'tour': array([56, 14, 17, 50,  2, 32, 33, 46,  8, 34, 29, 37, 11, 10, 53, 27, 20,
       45,  7, 55, 64,  9, 42, 44, 12, 47, 63, 59, 35, 58,  3,  4, 57, 30,
       39, 52, 51, 36, 28,  1, 19, 31, 18,  0,  6, 23, 60, 54,  5, 21, 40,
       62, 41, 16, 13, 15, 22, 25, 65, 26, 49, 24, 61, 43, 38, 48],
      dtype=int64), 'cur_cost': 98454.0}, {'tour': array([42, 48, 65, 28, 13, 15, 40, 62,  7, 33, 14, 27, 30, 29, 25, 44, 61,
       23, 41, 39,  4, 52, 45, 31, 51, 32,  2, 19,  8,  6, 36, 55, 56, 58,
       57,  3, 18,  1, 20, 64,  0, 60, 43, 24, 35, 54, 47, 37, 63, 22, 12,
       59,  5, 10,  9, 16, 53, 26, 11, 50, 21, 38, 46, 49, 17, 34],
      dtype=int64), 'cur_cost': 95999.0}, {'tour': array([10, 23, 52, 29, 36, 26, 38, 45, 51,  2, 60, 41, 32,  3,  7, 48, 37,
       58, 25, 59, 17, 54, 44, 18,  6,  1, 39, 53, 57, 34, 46, 63, 24, 12,
       28, 55,  8, 64,  5, 40, 35, 65, 20, 27,  0, 43, 11, 13, 14, 19, 31,
       33, 50,  9, 62, 21, 61, 42, 15, 56, 22, 30, 49,  4, 47, 16],
      dtype=int64), 'cur_cost': 115298.0}, {'tour': array([ 6, 30, 56, 39, 63, 20, 35, 23, 32, 38, 61,  0, 37,  8, 52, 40, 26,
       25, 17, 60, 54, 55, 16, 62, 14,  9, 57, 51,  7, 44, 31, 21, 29, 34,
       36, 53, 11, 45, 28, 49,  4, 65, 19, 50, 59, 64, 48,  2, 10, 42, 27,
        5, 18, 13, 33, 47, 41, 22,  1, 58, 43,  3, 46, 15, 24, 12],
      dtype=int64), 'cur_cost': 109896.0}, {'tour': array([31, 38,  8, 26, 33,  9, 53, 23, 55, 12, 63, 48, 54, 35, 25, 57, 50,
       17, 18, 24, 65, 62, 28, 56,  0, 42, 16, 45,  6,  1, 64, 29, 60, 37,
       58, 44, 39, 47, 15,  4, 32, 43, 36, 11,  7, 49, 27, 20, 21, 22, 52,
       51,  3, 46, 14, 30, 13, 61,  5, 59, 41, 40, 10,  2, 19, 34],
      dtype=int64), 'cur_cost': 110594.0}, {'tour': array([50,  6, 46,  3, 56, 52,  1, 11, 37, 27, 39, 47, 53, 49, 20, 41, 65,
       42, 59,  2, 18, 63, 29, 48, 40, 61, 17, 64, 13, 57, 35,  4, 30, 25,
       23, 43, 36, 55, 51,  9, 19,  0, 15, 45, 16, 54, 62, 31, 10, 60, 21,
       32, 28, 44, 14, 24, 12, 34,  7, 38, 22,  8,  5, 26, 33, 58],
      dtype=int64), 'cur_cost': 110726.0}]
2025-08-03 16:25:40,209 - ExploitationExpert - INFO - 局部搜索耗时: 0.69秒
2025-08-03 16:25:40,210 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-03 16:25:40,210 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([ 6, 57, 56,  1, 41, 38, 62,  3, 51, 46, 27, 43, 16, 63, 55, 59, 47,
       42, 44, 61,  4, 52, 24, 20, 18, 48, 58, 21,  2, 36, 35, 10, 26, 31,
       12, 45,  0, 64, 50, 34, 32, 29, 19, 54,  7,  9, 23, 14, 22, 15, 28,
       25, 17, 53, 37, 33,  5, 60, 39, 40, 49, 30,  8, 11, 13, 65],
      dtype=int64), 'cur_cost': 83096.0}
2025-08-03 16:25:40,211 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 83096.00)
2025-08-03 16:25:40,211 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-03 16:25:40,211 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-03 16:25:40,212 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:25:40,223 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:25:40,223 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:25:40,224 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10421.0, 路径长度: 66
2025-08-03 16:25:40,224 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 9, 5, 10, 8, 2, 6, 4, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10421.0}
2025-08-03 16:25:40,225 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 10421.00)
2025-08-03 16:25:40,225 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-03 16:25:40,226 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-03 16:25:40,226 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:25:40,232 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:25:40,234 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:25:40,234 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12404.0, 路径长度: 66
2025-08-03 16:25:40,235 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 2, 11, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12404.0}
2025-08-03 16:25:40,235 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 12404.00)
2025-08-03 16:25:40,236 - experts.management.collaboration_manager - INFO - 为个体 10 生成利用路径
2025-08-03 16:25:40,236 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:25:40,236 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:25:40,237 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 10 处的路径，新成本: 103493.0
2025-08-03 16:25:40,307 - ExploitationExpert - INFO - res_population_num: 11
2025-08-03 16:25:40,307 - ExploitationExpert - INFO - res_population_costs: [9844.0, 9548.0, 9544, 9542, 9536, 9535, 9535, 9535, 9524, 9524, 9521]
2025-08-03 16:25:40,307 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  6,  2,  8,  5,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56, 59, 62,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62, 59, 56, 60, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:25:40,317 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:25:40,318 - ExploitationExpert - INFO - populations: [{'tour': [13, 12, 37, 31, 14, 7, 65, 64, 63, 9, 57, 59, 23, 32, 33, 11, 52, 2, 55, 4, 62, 22, 27, 25, 15, 1, 60, 10, 24, 21, 40, 41, 20, 16, 17, 35, 30, 18, 19, 34, 29, 26, 49, 51, 38, 39, 42, 47, 44, 45, 46, 6, 61, 54, 5, 0, 8, 58, 3, 56, 53, 43, 48, 50, 36, 28], 'cur_cost': 51550.0}, {'tour': array([54, 31, 42, 58, 26, 32, 14, 39, 34, 20, 44, 24, 33,  8,  5, 62, 53,
        1, 45, 10, 52, 59, 21, 35,  6, 11, 25,  4, 27, 46,  7, 16, 55, 60,
       63, 19, 18, 36, 22, 13, 57, 37, 43,  0, 30, 47, 38, 65, 40, 17, 28,
       50, 12, 61, 56, 29,  3, 41, 64, 15, 23, 51,  9, 48,  2, 49],
      dtype=int64), 'cur_cost': 108303.0}, {'tour': [37, 14, 65, 23, 2, 10, 40, 41, 35, 30, 19, 38, 39, 42, 47, 44, 45, 46, 61, 13, 4, 34, 36, 17, 11, 55, 51, 43, 59, 49, 7, 52, 27, 6, 33, 28, 50, 48, 56, 32, 26, 60, 5, 62, 3, 29, 1, 31, 18, 24, 8, 21, 22, 12, 54, 15, 0, 58, 53, 25, 64, 20, 16, 57, 63, 9], 'cur_cost': 89494.0}, {'tour': [13, 65, 9, 19, 23, 47, 11, 55, 22, 17, 51, 49, 41, 6, 35, 50, 26, 42, 18, 45, 21, 61, 0, 4, 14, 34, 57, 7, 25, 52, 28, 53, 37, 20, 31, 15, 27, 2, 30, 59, 10, 1, 48, 58, 39, 32, 64, 56, 24, 46, 8, 54, 40, 33, 16, 38, 12, 63, 62, 36, 3, 29, 43, 5, 60, 44], 'cur_cost': 113666.0}, {'tour': array([43, 38, 40, 12, 35, 60, 32,  5, 20, 44, 34, 10, 54, 39, 49, 26, 42,
       23, 30,  8, 18, 27, 17, 28, 48, 58, 22, 46, 36, 53, 64, 14,  7, 51,
       25, 24, 62, 59,  0, 57, 16, 33,  1, 45,  4, 15, 19, 47,  2,  9,  6,
        3, 37, 11, 41, 56, 63, 31, 21, 50, 52, 13, 65, 29, 61, 55],
      dtype=int64), 'cur_cost': 107087.0}, {'tour': [0, 8, 1, 5, 4, 6, 2, 10, 11, 9, 3, 7, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10468.0}, {'tour': [65, 9, 31, 6, 2, 10, 22, 17, 3, 49, 52, 55, 45, 0, 25, 11, 14, 15, 59, 40, 53, 20, 23, 50, 19, 56, 32, 5, 62, 38, 42, 18, 21, 61, 54, 16, 8, 43, 26, 51, 60, 63, 34, 12, 27, 37, 4, 1, 36, 48, 47, 13, 33, 41, 44, 39, 30, 24, 7, 28, 46, 57, 35, 58, 64, 29], 'cur_cost': 102321.0}, {'tour': array([ 6, 57, 56,  1, 41, 38, 62,  3, 51, 46, 27, 43, 16, 63, 55, 59, 47,
       42, 44, 61,  4, 52, 24, 20, 18, 48, 58, 21,  2, 36, 35, 10, 26, 31,
       12, 45,  0, 64, 50, 34, 32, 29, 19, 54,  7,  9, 23, 14, 22, 15, 28,
       25, 17, 53, 37, 33,  5, 60, 39, 40, 49, 30,  8, 11, 13, 65],
      dtype=int64), 'cur_cost': 83096.0}, {'tour': [0, 9, 5, 10, 8, 2, 6, 4, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10421.0}, {'tour': [0, 2, 11, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12404.0}, {'tour': array([13,  3,  6, 49, 46, 28, 54, 24, 34, 15, 31, 11, 62, 60, 50, 21, 22,
        7, 23, 47,  9, 48, 64,  4, 36,  8, 57, 43, 30, 59, 58, 56, 14, 17,
       53, 55, 40, 63, 12,  0, 26, 35, 33, 37, 29, 51, 61, 16, 38, 39, 32,
        2, 44, 27, 52, 18, 65,  1, 19, 10, 41, 20, 45, 25,  5, 42],
      dtype=int64), 'cur_cost': 103493.0}, {'tour': array([43, 41, 48, 40,  6, 50, 12, 62, 10,  3, 28, 59, 49, 55, 39, 32, 45,
       65, 47, 42, 60, 64, 36,  0, 54,  7, 23, 33, 61, 37,  8,  4, 63, 52,
       25, 19, 35, 26,  1, 58, 20, 38, 27, 56, 13, 29, 24, 34, 17, 30, 21,
       44, 14, 57,  5, 46, 22, 31, 15, 18, 11, 51, 16,  2,  9, 53],
      dtype=int64), 'cur_cost': 100525.0}, {'tour': array([60, 32,  1, 15, 36, 46, 19, 41,  4, 65,  5, 33,  9, 21, 27, 39, 28,
       16, 12, 26, 17, 38, 62,  2, 20, 42, 40, 13, 24, 29, 18, 48, 11, 59,
       64, 10, 52, 50, 35, 23, 25, 44, 30, 51, 31, 22, 61, 34, 54, 53, 43,
       47,  3,  0, 14, 56, 55, 58, 45, 37,  6, 49,  8,  7, 63, 57],
      dtype=int64), 'cur_cost': 96623.0}, {'tour': array([42, 25, 45,  7, 49, 30, 47, 64,  3,  2, 52, 24, 13, 27, 28, 38, 63,
        0, 46, 40, 48, 56, 51, 23, 32, 37, 57, 29,  9, 53, 36,  4, 61, 31,
       19, 21,  1, 16, 39, 41,  5, 12, 22, 11, 18, 65, 54,  8, 62, 44, 55,
       26, 43, 17, 58, 14, 10, 15, 34, 50, 35, 20, 60, 33, 59,  6],
      dtype=int64), 'cur_cost': 110501.0}, {'tour': array([56, 14, 17, 50,  2, 32, 33, 46,  8, 34, 29, 37, 11, 10, 53, 27, 20,
       45,  7, 55, 64,  9, 42, 44, 12, 47, 63, 59, 35, 58,  3,  4, 57, 30,
       39, 52, 51, 36, 28,  1, 19, 31, 18,  0,  6, 23, 60, 54,  5, 21, 40,
       62, 41, 16, 13, 15, 22, 25, 65, 26, 49, 24, 61, 43, 38, 48],
      dtype=int64), 'cur_cost': 98454.0}, {'tour': array([42, 48, 65, 28, 13, 15, 40, 62,  7, 33, 14, 27, 30, 29, 25, 44, 61,
       23, 41, 39,  4, 52, 45, 31, 51, 32,  2, 19,  8,  6, 36, 55, 56, 58,
       57,  3, 18,  1, 20, 64,  0, 60, 43, 24, 35, 54, 47, 37, 63, 22, 12,
       59,  5, 10,  9, 16, 53, 26, 11, 50, 21, 38, 46, 49, 17, 34],
      dtype=int64), 'cur_cost': 95999.0}, {'tour': array([10, 23, 52, 29, 36, 26, 38, 45, 51,  2, 60, 41, 32,  3,  7, 48, 37,
       58, 25, 59, 17, 54, 44, 18,  6,  1, 39, 53, 57, 34, 46, 63, 24, 12,
       28, 55,  8, 64,  5, 40, 35, 65, 20, 27,  0, 43, 11, 13, 14, 19, 31,
       33, 50,  9, 62, 21, 61, 42, 15, 56, 22, 30, 49,  4, 47, 16],
      dtype=int64), 'cur_cost': 115298.0}, {'tour': array([ 6, 30, 56, 39, 63, 20, 35, 23, 32, 38, 61,  0, 37,  8, 52, 40, 26,
       25, 17, 60, 54, 55, 16, 62, 14,  9, 57, 51,  7, 44, 31, 21, 29, 34,
       36, 53, 11, 45, 28, 49,  4, 65, 19, 50, 59, 64, 48,  2, 10, 42, 27,
        5, 18, 13, 33, 47, 41, 22,  1, 58, 43,  3, 46, 15, 24, 12],
      dtype=int64), 'cur_cost': 109896.0}, {'tour': array([31, 38,  8, 26, 33,  9, 53, 23, 55, 12, 63, 48, 54, 35, 25, 57, 50,
       17, 18, 24, 65, 62, 28, 56,  0, 42, 16, 45,  6,  1, 64, 29, 60, 37,
       58, 44, 39, 47, 15,  4, 32, 43, 36, 11,  7, 49, 27, 20, 21, 22, 52,
       51,  3, 46, 14, 30, 13, 61,  5, 59, 41, 40, 10,  2, 19, 34],
      dtype=int64), 'cur_cost': 110594.0}, {'tour': array([50,  6, 46,  3, 56, 52,  1, 11, 37, 27, 39, 47, 53, 49, 20, 41, 65,
       42, 59,  2, 18, 63, 29, 48, 40, 61, 17, 64, 13, 57, 35,  4, 30, 25,
       23, 43, 36, 55, 51,  9, 19,  0, 15, 45, 16, 54, 62, 31, 10, 60, 21,
       32, 28, 44, 14, 24, 12, 34,  7, 38, 22,  8,  5, 26, 33, 58],
      dtype=int64), 'cur_cost': 110726.0}]
2025-08-03 16:25:40,330 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-03 16:25:40,331 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-03 16:25:40,332 - experts.management.collaboration_manager - INFO - 个体 10 利用路径生成报告: {'new_tour': array([13,  3,  6, 49, 46, 28, 54, 24, 34, 15, 31, 11, 62, 60, 50, 21, 22,
        7, 23, 47,  9, 48, 64,  4, 36,  8, 57, 43, 30, 59, 58, 56, 14, 17,
       53, 55, 40, 63, 12,  0, 26, 35, 33, 37, 29, 51, 61, 16, 38, 39, 32,
        2, 44, 27, 52, 18, 65,  1, 19, 10, 41, 20, 45, 25,  5, 42],
      dtype=int64), 'cur_cost': 103493.0}
2025-08-03 16:25:40,332 - experts.management.collaboration_manager - INFO - 个体 10 接受新路径 (成本: 103493.00)
2025-08-03 16:25:40,332 - experts.management.collaboration_manager - INFO - 为个体 11 生成探索路径
2025-08-03 16:25:40,333 - ExplorationExpert - INFO - 开始为个体 11 生成探索路径（算法实现）
2025-08-03 16:25:40,333 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:25:40,339 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:25:40,339 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:25:40,340 - ExplorationExpert - INFO - 探索路径生成完成，成本: 112827.0, 路径长度: 66
2025-08-03 16:25:40,340 - experts.management.collaboration_manager - INFO - 个体 11 探索路径生成报告: {'new_tour': [37, 11, 5, 23, 22, 55, 51, 7, 45, 32, 47, 52, 13, 53, 34, 0, 25, 15, 56, 10, 40, 41, 27, 33, 35, 30, 48, 19, 29, 63, 49, 62, 39, 4, 24, 61, 54, 16, 8, 3, 28, 64, 43, 21, 46, 18, 12, 59, 58, 36, 14, 57, 2, 1, 38, 42, 9, 60, 17, 44, 6, 50, 26, 20, 31, 65], 'cur_cost': 112827.0}
2025-08-03 16:25:40,341 - experts.management.collaboration_manager - INFO - 个体 11 接受新路径 (成本: 112827.00)
2025-08-03 16:25:40,341 - experts.management.collaboration_manager - INFO - 为个体 12 生成探索路径
2025-08-03 16:25:40,341 - ExplorationExpert - INFO - 开始为个体 12 生成探索路径（算法实现）
2025-08-03 16:25:40,341 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:25:40,358 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:25:40,359 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:25:40,360 - ExplorationExpert - INFO - 探索路径生成完成，成本: 58855.0, 路径长度: 66
2025-08-03 16:25:40,360 - experts.management.collaboration_manager - INFO - 个体 12 探索路径生成报告: {'new_tour': [19, 26, 11, 14, 16, 4, 2, 17, 13, 21, 8, 3, 65, 0, 6, 23, 27, 35, 33, 1, 55, 64, 54, 56, 10, 37, 43, 40, 46, 34, 5, 9, 53, 7, 15, 31, 28, 47, 20, 30, 32, 45, 12, 29, 24, 39, 48, 41, 42, 36, 50, 22, 49, 58, 61, 52, 62, 59, 60, 57, 63, 44, 51, 38, 18, 25], 'cur_cost': 58855.0}
2025-08-03 16:25:40,361 - experts.management.collaboration_manager - INFO - 个体 12 接受新路径 (成本: 58855.00)
2025-08-03 16:25:40,361 - experts.management.collaboration_manager - INFO - 为个体 13 生成利用路径
2025-08-03 16:25:40,361 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:25:40,361 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:25:40,362 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 13 处的路径，新成本: 116675.0
2025-08-03 16:25:40,455 - ExploitationExpert - INFO - res_population_num: 12
2025-08-03 16:25:40,456 - ExploitationExpert - INFO - res_population_costs: [9844.0, 9548.0, 9544, 9542, 9536, 9535, 9535, 9535, 9524, 9524, 9521, 9521]
2025-08-03 16:25:40,456 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  6,  2,  8,  5,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56, 59, 62,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62, 59, 56, 60, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:25:40,463 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:25:40,463 - ExploitationExpert - INFO - populations: [{'tour': [13, 12, 37, 31, 14, 7, 65, 64, 63, 9, 57, 59, 23, 32, 33, 11, 52, 2, 55, 4, 62, 22, 27, 25, 15, 1, 60, 10, 24, 21, 40, 41, 20, 16, 17, 35, 30, 18, 19, 34, 29, 26, 49, 51, 38, 39, 42, 47, 44, 45, 46, 6, 61, 54, 5, 0, 8, 58, 3, 56, 53, 43, 48, 50, 36, 28], 'cur_cost': 51550.0}, {'tour': array([54, 31, 42, 58, 26, 32, 14, 39, 34, 20, 44, 24, 33,  8,  5, 62, 53,
        1, 45, 10, 52, 59, 21, 35,  6, 11, 25,  4, 27, 46,  7, 16, 55, 60,
       63, 19, 18, 36, 22, 13, 57, 37, 43,  0, 30, 47, 38, 65, 40, 17, 28,
       50, 12, 61, 56, 29,  3, 41, 64, 15, 23, 51,  9, 48,  2, 49],
      dtype=int64), 'cur_cost': 108303.0}, {'tour': [37, 14, 65, 23, 2, 10, 40, 41, 35, 30, 19, 38, 39, 42, 47, 44, 45, 46, 61, 13, 4, 34, 36, 17, 11, 55, 51, 43, 59, 49, 7, 52, 27, 6, 33, 28, 50, 48, 56, 32, 26, 60, 5, 62, 3, 29, 1, 31, 18, 24, 8, 21, 22, 12, 54, 15, 0, 58, 53, 25, 64, 20, 16, 57, 63, 9], 'cur_cost': 89494.0}, {'tour': [13, 65, 9, 19, 23, 47, 11, 55, 22, 17, 51, 49, 41, 6, 35, 50, 26, 42, 18, 45, 21, 61, 0, 4, 14, 34, 57, 7, 25, 52, 28, 53, 37, 20, 31, 15, 27, 2, 30, 59, 10, 1, 48, 58, 39, 32, 64, 56, 24, 46, 8, 54, 40, 33, 16, 38, 12, 63, 62, 36, 3, 29, 43, 5, 60, 44], 'cur_cost': 113666.0}, {'tour': array([43, 38, 40, 12, 35, 60, 32,  5, 20, 44, 34, 10, 54, 39, 49, 26, 42,
       23, 30,  8, 18, 27, 17, 28, 48, 58, 22, 46, 36, 53, 64, 14,  7, 51,
       25, 24, 62, 59,  0, 57, 16, 33,  1, 45,  4, 15, 19, 47,  2,  9,  6,
        3, 37, 11, 41, 56, 63, 31, 21, 50, 52, 13, 65, 29, 61, 55],
      dtype=int64), 'cur_cost': 107087.0}, {'tour': [0, 8, 1, 5, 4, 6, 2, 10, 11, 9, 3, 7, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10468.0}, {'tour': [65, 9, 31, 6, 2, 10, 22, 17, 3, 49, 52, 55, 45, 0, 25, 11, 14, 15, 59, 40, 53, 20, 23, 50, 19, 56, 32, 5, 62, 38, 42, 18, 21, 61, 54, 16, 8, 43, 26, 51, 60, 63, 34, 12, 27, 37, 4, 1, 36, 48, 47, 13, 33, 41, 44, 39, 30, 24, 7, 28, 46, 57, 35, 58, 64, 29], 'cur_cost': 102321.0}, {'tour': array([ 6, 57, 56,  1, 41, 38, 62,  3, 51, 46, 27, 43, 16, 63, 55, 59, 47,
       42, 44, 61,  4, 52, 24, 20, 18, 48, 58, 21,  2, 36, 35, 10, 26, 31,
       12, 45,  0, 64, 50, 34, 32, 29, 19, 54,  7,  9, 23, 14, 22, 15, 28,
       25, 17, 53, 37, 33,  5, 60, 39, 40, 49, 30,  8, 11, 13, 65],
      dtype=int64), 'cur_cost': 83096.0}, {'tour': [0, 9, 5, 10, 8, 2, 6, 4, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10421.0}, {'tour': [0, 2, 11, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12404.0}, {'tour': array([13,  3,  6, 49, 46, 28, 54, 24, 34, 15, 31, 11, 62, 60, 50, 21, 22,
        7, 23, 47,  9, 48, 64,  4, 36,  8, 57, 43, 30, 59, 58, 56, 14, 17,
       53, 55, 40, 63, 12,  0, 26, 35, 33, 37, 29, 51, 61, 16, 38, 39, 32,
        2, 44, 27, 52, 18, 65,  1, 19, 10, 41, 20, 45, 25,  5, 42],
      dtype=int64), 'cur_cost': 103493.0}, {'tour': [37, 11, 5, 23, 22, 55, 51, 7, 45, 32, 47, 52, 13, 53, 34, 0, 25, 15, 56, 10, 40, 41, 27, 33, 35, 30, 48, 19, 29, 63, 49, 62, 39, 4, 24, 61, 54, 16, 8, 3, 28, 64, 43, 21, 46, 18, 12, 59, 58, 36, 14, 57, 2, 1, 38, 42, 9, 60, 17, 44, 6, 50, 26, 20, 31, 65], 'cur_cost': 112827.0}, {'tour': [19, 26, 11, 14, 16, 4, 2, 17, 13, 21, 8, 3, 65, 0, 6, 23, 27, 35, 33, 1, 55, 64, 54, 56, 10, 37, 43, 40, 46, 34, 5, 9, 53, 7, 15, 31, 28, 47, 20, 30, 32, 45, 12, 29, 24, 39, 48, 41, 42, 36, 50, 22, 49, 58, 61, 52, 62, 59, 60, 57, 63, 44, 51, 38, 18, 25], 'cur_cost': 58855.0}, {'tour': array([47,  3, 62, 35, 33, 55,  1, 16, 19, 56, 23, 53, 18, 40, 57, 44,  8,
       20, 41, 59, 31, 38, 32, 21, 64,  0, 34,  4, 24, 42, 36, 10, 25, 63,
       51, 48, 65, 45, 58, 27, 49, 61, 50, 26, 29, 12, 22, 43, 60, 54, 52,
       28,  9,  6, 37, 11, 30, 13, 15, 46,  5, 17,  2, 14,  7, 39],
      dtype=int64), 'cur_cost': 116675.0}, {'tour': array([56, 14, 17, 50,  2, 32, 33, 46,  8, 34, 29, 37, 11, 10, 53, 27, 20,
       45,  7, 55, 64,  9, 42, 44, 12, 47, 63, 59, 35, 58,  3,  4, 57, 30,
       39, 52, 51, 36, 28,  1, 19, 31, 18,  0,  6, 23, 60, 54,  5, 21, 40,
       62, 41, 16, 13, 15, 22, 25, 65, 26, 49, 24, 61, 43, 38, 48],
      dtype=int64), 'cur_cost': 98454.0}, {'tour': array([42, 48, 65, 28, 13, 15, 40, 62,  7, 33, 14, 27, 30, 29, 25, 44, 61,
       23, 41, 39,  4, 52, 45, 31, 51, 32,  2, 19,  8,  6, 36, 55, 56, 58,
       57,  3, 18,  1, 20, 64,  0, 60, 43, 24, 35, 54, 47, 37, 63, 22, 12,
       59,  5, 10,  9, 16, 53, 26, 11, 50, 21, 38, 46, 49, 17, 34],
      dtype=int64), 'cur_cost': 95999.0}, {'tour': array([10, 23, 52, 29, 36, 26, 38, 45, 51,  2, 60, 41, 32,  3,  7, 48, 37,
       58, 25, 59, 17, 54, 44, 18,  6,  1, 39, 53, 57, 34, 46, 63, 24, 12,
       28, 55,  8, 64,  5, 40, 35, 65, 20, 27,  0, 43, 11, 13, 14, 19, 31,
       33, 50,  9, 62, 21, 61, 42, 15, 56, 22, 30, 49,  4, 47, 16],
      dtype=int64), 'cur_cost': 115298.0}, {'tour': array([ 6, 30, 56, 39, 63, 20, 35, 23, 32, 38, 61,  0, 37,  8, 52, 40, 26,
       25, 17, 60, 54, 55, 16, 62, 14,  9, 57, 51,  7, 44, 31, 21, 29, 34,
       36, 53, 11, 45, 28, 49,  4, 65, 19, 50, 59, 64, 48,  2, 10, 42, 27,
        5, 18, 13, 33, 47, 41, 22,  1, 58, 43,  3, 46, 15, 24, 12],
      dtype=int64), 'cur_cost': 109896.0}, {'tour': array([31, 38,  8, 26, 33,  9, 53, 23, 55, 12, 63, 48, 54, 35, 25, 57, 50,
       17, 18, 24, 65, 62, 28, 56,  0, 42, 16, 45,  6,  1, 64, 29, 60, 37,
       58, 44, 39, 47, 15,  4, 32, 43, 36, 11,  7, 49, 27, 20, 21, 22, 52,
       51,  3, 46, 14, 30, 13, 61,  5, 59, 41, 40, 10,  2, 19, 34],
      dtype=int64), 'cur_cost': 110594.0}, {'tour': array([50,  6, 46,  3, 56, 52,  1, 11, 37, 27, 39, 47, 53, 49, 20, 41, 65,
       42, 59,  2, 18, 63, 29, 48, 40, 61, 17, 64, 13, 57, 35,  4, 30, 25,
       23, 43, 36, 55, 51,  9, 19,  0, 15, 45, 16, 54, 62, 31, 10, 60, 21,
       32, 28, 44, 14, 24, 12, 34,  7, 38, 22,  8,  5, 26, 33, 58],
      dtype=int64), 'cur_cost': 110726.0}]
2025-08-03 16:25:40,472 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒
2025-08-03 16:25:40,472 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-03 16:25:40,473 - experts.management.collaboration_manager - INFO - 个体 13 利用路径生成报告: {'new_tour': array([47,  3, 62, 35, 33, 55,  1, 16, 19, 56, 23, 53, 18, 40, 57, 44,  8,
       20, 41, 59, 31, 38, 32, 21, 64,  0, 34,  4, 24, 42, 36, 10, 25, 63,
       51, 48, 65, 45, 58, 27, 49, 61, 50, 26, 29, 12, 22, 43, 60, 54, 52,
       28,  9,  6, 37, 11, 30, 13, 15, 46,  5, 17,  2, 14,  7, 39],
      dtype=int64), 'cur_cost': 116675.0}
2025-08-03 16:25:40,473 - experts.management.collaboration_manager - INFO - 个体 13 接受新路径 (成本: 116675.00)
2025-08-03 16:25:40,473 - experts.management.collaboration_manager - INFO - 为个体 14 生成探索路径
2025-08-03 16:25:40,473 - ExplorationExpert - INFO - 开始为个体 14 生成探索路径（算法实现）
2025-08-03 16:25:40,474 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:25:40,478 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:25:40,478 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:25:40,478 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14733.0, 路径长度: 66
2025-08-03 16:25:40,478 - experts.management.collaboration_manager - INFO - 个体 14 探索路径生成报告: {'new_tour': [0, 12, 7, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14733.0}
2025-08-03 16:25:40,479 - experts.management.collaboration_manager - INFO - 个体 14 接受新路径 (成本: 14733.00)
2025-08-03 16:25:40,480 - experts.management.collaboration_manager - INFO - 为个体 15 生成探索路径
2025-08-03 16:25:40,481 - ExplorationExpert - INFO - 开始为个体 15 生成探索路径（算法实现）
2025-08-03 16:25:40,482 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:25:40,499 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:25:40,501 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:25:40,502 - ExplorationExpert - INFO - 探索路径生成完成，成本: 59329.0, 路径长度: 66
2025-08-03 16:25:40,502 - experts.management.collaboration_manager - INFO - 个体 15 探索路径生成报告: {'new_tour': [28, 12, 31, 22, 14, 36, 0, 64, 2, 11, 5, 4, 54, 10, 57, 23, 35, 29, 33, 16, 40, 46, 48, 44, 13, 26, 15, 17, 6, 65, 58, 21, 25, 7, 56, 1, 20, 3, 52, 19, 34, 24, 37, 18, 9, 62, 8, 60, 53, 49, 43, 47, 39, 50, 41, 45, 27, 32, 30, 51, 55, 59, 61, 63, 38, 42], 'cur_cost': 59329.0}
2025-08-03 16:25:40,503 - experts.management.collaboration_manager - INFO - 个体 15 接受新路径 (成本: 59329.00)
2025-08-03 16:25:40,504 - experts.management.collaboration_manager - INFO - 为个体 16 生成利用路径
2025-08-03 16:25:40,504 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:25:40,504 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:25:40,505 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 16 处的路径，新成本: 110160.0
2025-08-03 16:25:40,595 - ExploitationExpert - INFO - res_population_num: 15
2025-08-03 16:25:40,595 - ExploitationExpert - INFO - res_population_costs: [9844.0, 9548.0, 9544, 9542, 9536, 9535, 9535, 9535, 9524, 9524, 9521, 9521, 9521, 9521, 9521]
2025-08-03 16:25:40,596 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  6,  2,  8,  5,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56, 59, 62,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62, 59, 56, 60, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:25:40,604 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:25:40,604 - ExploitationExpert - INFO - populations: [{'tour': [13, 12, 37, 31, 14, 7, 65, 64, 63, 9, 57, 59, 23, 32, 33, 11, 52, 2, 55, 4, 62, 22, 27, 25, 15, 1, 60, 10, 24, 21, 40, 41, 20, 16, 17, 35, 30, 18, 19, 34, 29, 26, 49, 51, 38, 39, 42, 47, 44, 45, 46, 6, 61, 54, 5, 0, 8, 58, 3, 56, 53, 43, 48, 50, 36, 28], 'cur_cost': 51550.0}, {'tour': array([54, 31, 42, 58, 26, 32, 14, 39, 34, 20, 44, 24, 33,  8,  5, 62, 53,
        1, 45, 10, 52, 59, 21, 35,  6, 11, 25,  4, 27, 46,  7, 16, 55, 60,
       63, 19, 18, 36, 22, 13, 57, 37, 43,  0, 30, 47, 38, 65, 40, 17, 28,
       50, 12, 61, 56, 29,  3, 41, 64, 15, 23, 51,  9, 48,  2, 49],
      dtype=int64), 'cur_cost': 108303.0}, {'tour': [37, 14, 65, 23, 2, 10, 40, 41, 35, 30, 19, 38, 39, 42, 47, 44, 45, 46, 61, 13, 4, 34, 36, 17, 11, 55, 51, 43, 59, 49, 7, 52, 27, 6, 33, 28, 50, 48, 56, 32, 26, 60, 5, 62, 3, 29, 1, 31, 18, 24, 8, 21, 22, 12, 54, 15, 0, 58, 53, 25, 64, 20, 16, 57, 63, 9], 'cur_cost': 89494.0}, {'tour': [13, 65, 9, 19, 23, 47, 11, 55, 22, 17, 51, 49, 41, 6, 35, 50, 26, 42, 18, 45, 21, 61, 0, 4, 14, 34, 57, 7, 25, 52, 28, 53, 37, 20, 31, 15, 27, 2, 30, 59, 10, 1, 48, 58, 39, 32, 64, 56, 24, 46, 8, 54, 40, 33, 16, 38, 12, 63, 62, 36, 3, 29, 43, 5, 60, 44], 'cur_cost': 113666.0}, {'tour': array([43, 38, 40, 12, 35, 60, 32,  5, 20, 44, 34, 10, 54, 39, 49, 26, 42,
       23, 30,  8, 18, 27, 17, 28, 48, 58, 22, 46, 36, 53, 64, 14,  7, 51,
       25, 24, 62, 59,  0, 57, 16, 33,  1, 45,  4, 15, 19, 47,  2,  9,  6,
        3, 37, 11, 41, 56, 63, 31, 21, 50, 52, 13, 65, 29, 61, 55],
      dtype=int64), 'cur_cost': 107087.0}, {'tour': [0, 8, 1, 5, 4, 6, 2, 10, 11, 9, 3, 7, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10468.0}, {'tour': [65, 9, 31, 6, 2, 10, 22, 17, 3, 49, 52, 55, 45, 0, 25, 11, 14, 15, 59, 40, 53, 20, 23, 50, 19, 56, 32, 5, 62, 38, 42, 18, 21, 61, 54, 16, 8, 43, 26, 51, 60, 63, 34, 12, 27, 37, 4, 1, 36, 48, 47, 13, 33, 41, 44, 39, 30, 24, 7, 28, 46, 57, 35, 58, 64, 29], 'cur_cost': 102321.0}, {'tour': array([ 6, 57, 56,  1, 41, 38, 62,  3, 51, 46, 27, 43, 16, 63, 55, 59, 47,
       42, 44, 61,  4, 52, 24, 20, 18, 48, 58, 21,  2, 36, 35, 10, 26, 31,
       12, 45,  0, 64, 50, 34, 32, 29, 19, 54,  7,  9, 23, 14, 22, 15, 28,
       25, 17, 53, 37, 33,  5, 60, 39, 40, 49, 30,  8, 11, 13, 65],
      dtype=int64), 'cur_cost': 83096.0}, {'tour': [0, 9, 5, 10, 8, 2, 6, 4, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10421.0}, {'tour': [0, 2, 11, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12404.0}, {'tour': array([13,  3,  6, 49, 46, 28, 54, 24, 34, 15, 31, 11, 62, 60, 50, 21, 22,
        7, 23, 47,  9, 48, 64,  4, 36,  8, 57, 43, 30, 59, 58, 56, 14, 17,
       53, 55, 40, 63, 12,  0, 26, 35, 33, 37, 29, 51, 61, 16, 38, 39, 32,
        2, 44, 27, 52, 18, 65,  1, 19, 10, 41, 20, 45, 25,  5, 42],
      dtype=int64), 'cur_cost': 103493.0}, {'tour': [37, 11, 5, 23, 22, 55, 51, 7, 45, 32, 47, 52, 13, 53, 34, 0, 25, 15, 56, 10, 40, 41, 27, 33, 35, 30, 48, 19, 29, 63, 49, 62, 39, 4, 24, 61, 54, 16, 8, 3, 28, 64, 43, 21, 46, 18, 12, 59, 58, 36, 14, 57, 2, 1, 38, 42, 9, 60, 17, 44, 6, 50, 26, 20, 31, 65], 'cur_cost': 112827.0}, {'tour': [19, 26, 11, 14, 16, 4, 2, 17, 13, 21, 8, 3, 65, 0, 6, 23, 27, 35, 33, 1, 55, 64, 54, 56, 10, 37, 43, 40, 46, 34, 5, 9, 53, 7, 15, 31, 28, 47, 20, 30, 32, 45, 12, 29, 24, 39, 48, 41, 42, 36, 50, 22, 49, 58, 61, 52, 62, 59, 60, 57, 63, 44, 51, 38, 18, 25], 'cur_cost': 58855.0}, {'tour': array([47,  3, 62, 35, 33, 55,  1, 16, 19, 56, 23, 53, 18, 40, 57, 44,  8,
       20, 41, 59, 31, 38, 32, 21, 64,  0, 34,  4, 24, 42, 36, 10, 25, 63,
       51, 48, 65, 45, 58, 27, 49, 61, 50, 26, 29, 12, 22, 43, 60, 54, 52,
       28,  9,  6, 37, 11, 30, 13, 15, 46,  5, 17,  2, 14,  7, 39],
      dtype=int64), 'cur_cost': 116675.0}, {'tour': [0, 12, 7, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14733.0}, {'tour': [28, 12, 31, 22, 14, 36, 0, 64, 2, 11, 5, 4, 54, 10, 57, 23, 35, 29, 33, 16, 40, 46, 48, 44, 13, 26, 15, 17, 6, 65, 58, 21, 25, 7, 56, 1, 20, 3, 52, 19, 34, 24, 37, 18, 9, 62, 8, 60, 53, 49, 43, 47, 39, 50, 41, 45, 27, 32, 30, 51, 55, 59, 61, 63, 38, 42], 'cur_cost': 59329.0}, {'tour': array([54, 30, 41, 11, 36, 45, 33, 37, 62, 23, 64, 51, 10, 16, 24, 58, 34,
        7, 61, 17,  1, 49, 38, 26, 42, 40, 35, 46, 43, 21,  4, 32, 60,  5,
        2, 50, 12, 55, 28,  6, 47, 39, 27,  8, 65, 63, 56, 44, 29, 48, 13,
       59, 19, 31, 52, 53, 20,  0, 18, 15, 22, 57, 14,  9,  3, 25],
      dtype=int64), 'cur_cost': 110160.0}, {'tour': array([ 6, 30, 56, 39, 63, 20, 35, 23, 32, 38, 61,  0, 37,  8, 52, 40, 26,
       25, 17, 60, 54, 55, 16, 62, 14,  9, 57, 51,  7, 44, 31, 21, 29, 34,
       36, 53, 11, 45, 28, 49,  4, 65, 19, 50, 59, 64, 48,  2, 10, 42, 27,
        5, 18, 13, 33, 47, 41, 22,  1, 58, 43,  3, 46, 15, 24, 12],
      dtype=int64), 'cur_cost': 109896.0}, {'tour': array([31, 38,  8, 26, 33,  9, 53, 23, 55, 12, 63, 48, 54, 35, 25, 57, 50,
       17, 18, 24, 65, 62, 28, 56,  0, 42, 16, 45,  6,  1, 64, 29, 60, 37,
       58, 44, 39, 47, 15,  4, 32, 43, 36, 11,  7, 49, 27, 20, 21, 22, 52,
       51,  3, 46, 14, 30, 13, 61,  5, 59, 41, 40, 10,  2, 19, 34],
      dtype=int64), 'cur_cost': 110594.0}, {'tour': array([50,  6, 46,  3, 56, 52,  1, 11, 37, 27, 39, 47, 53, 49, 20, 41, 65,
       42, 59,  2, 18, 63, 29, 48, 40, 61, 17, 64, 13, 57, 35,  4, 30, 25,
       23, 43, 36, 55, 51,  9, 19,  0, 15, 45, 16, 54, 62, 31, 10, 60, 21,
       32, 28, 44, 14, 24, 12, 34,  7, 38, 22,  8,  5, 26, 33, 58],
      dtype=int64), 'cur_cost': 110726.0}]
2025-08-03 16:25:40,611 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒
2025-08-03 16:25:40,611 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-03 16:25:40,612 - experts.management.collaboration_manager - INFO - 个体 16 利用路径生成报告: {'new_tour': array([54, 30, 41, 11, 36, 45, 33, 37, 62, 23, 64, 51, 10, 16, 24, 58, 34,
        7, 61, 17,  1, 49, 38, 26, 42, 40, 35, 46, 43, 21,  4, 32, 60,  5,
        2, 50, 12, 55, 28,  6, 47, 39, 27,  8, 65, 63, 56, 44, 29, 48, 13,
       59, 19, 31, 52, 53, 20,  0, 18, 15, 22, 57, 14,  9,  3, 25],
      dtype=int64), 'cur_cost': 110160.0}
2025-08-03 16:25:40,613 - experts.management.collaboration_manager - INFO - 个体 16 接受新路径 (成本: 110160.00)
2025-08-03 16:25:40,616 - experts.management.collaboration_manager - INFO - 为个体 17 生成探索路径
2025-08-03 16:25:40,616 - ExplorationExpert - INFO - 开始为个体 17 生成探索路径（算法实现）
2025-08-03 16:25:40,617 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:25:40,624 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:25:40,624 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:25:40,625 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12271.0, 路径长度: 66
2025-08-03 16:25:40,625 - experts.management.collaboration_manager - INFO - 个体 17 探索路径生成报告: {'new_tour': [0, 9, 22, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12271.0}
2025-08-03 16:25:40,626 - experts.management.collaboration_manager - INFO - 个体 17 接受新路径 (成本: 12271.00)
2025-08-03 16:25:40,626 - experts.management.collaboration_manager - INFO - 为个体 18 生成探索路径
2025-08-03 16:25:40,627 - ExplorationExpert - INFO - 开始为个体 18 生成探索路径（算法实现）
2025-08-03 16:25:40,627 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:25:40,640 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:25:40,640 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:25:40,641 - ExplorationExpert - INFO - 探索路径生成完成，成本: 59889.0, 路径长度: 66
2025-08-03 16:25:40,641 - experts.management.collaboration_manager - INFO - 个体 18 探索路径生成报告: {'new_tour': [15, 19, 17, 28, 21, 12, 31, 29, 37, 6, 62, 23, 32, 34, 9, 5, 8, 55, 56, 53, 1, 63, 16, 25, 13, 2, 58, 64, 11, 59, 18, 10, 27, 33, 7, 61, 0, 22, 20, 4, 40, 44, 48, 45, 51, 39, 50, 49, 14, 43, 46, 42, 38, 30, 26, 24, 41, 3, 65, 57, 54, 52, 47, 36, 35, 60], 'cur_cost': 59889.0}
2025-08-03 16:25:40,642 - experts.management.collaboration_manager - INFO - 个体 18 接受新路径 (成本: 59889.00)
2025-08-03 16:25:40,642 - experts.management.collaboration_manager - INFO - 为个体 19 生成利用路径
2025-08-03 16:25:40,643 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:25:40,643 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:25:40,643 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 19 处的路径，新成本: 100493.0
2025-08-03 16:25:40,758 - ExploitationExpert - INFO - res_population_num: 16
2025-08-03 16:25:40,758 - ExploitationExpert - INFO - res_population_costs: [9844.0, 9548.0, 9544, 9542, 9536, 9535, 9535, 9535, 9524, 9524, 9521, 9521, 9521, 9521, 9521, 9521]
2025-08-03 16:25:40,759 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  6,  2,  8,  5,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56, 59, 62,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62, 59, 56, 60, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:25:40,768 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:25:40,768 - ExploitationExpert - INFO - populations: [{'tour': [13, 12, 37, 31, 14, 7, 65, 64, 63, 9, 57, 59, 23, 32, 33, 11, 52, 2, 55, 4, 62, 22, 27, 25, 15, 1, 60, 10, 24, 21, 40, 41, 20, 16, 17, 35, 30, 18, 19, 34, 29, 26, 49, 51, 38, 39, 42, 47, 44, 45, 46, 6, 61, 54, 5, 0, 8, 58, 3, 56, 53, 43, 48, 50, 36, 28], 'cur_cost': 51550.0}, {'tour': array([54, 31, 42, 58, 26, 32, 14, 39, 34, 20, 44, 24, 33,  8,  5, 62, 53,
        1, 45, 10, 52, 59, 21, 35,  6, 11, 25,  4, 27, 46,  7, 16, 55, 60,
       63, 19, 18, 36, 22, 13, 57, 37, 43,  0, 30, 47, 38, 65, 40, 17, 28,
       50, 12, 61, 56, 29,  3, 41, 64, 15, 23, 51,  9, 48,  2, 49],
      dtype=int64), 'cur_cost': 108303.0}, {'tour': [37, 14, 65, 23, 2, 10, 40, 41, 35, 30, 19, 38, 39, 42, 47, 44, 45, 46, 61, 13, 4, 34, 36, 17, 11, 55, 51, 43, 59, 49, 7, 52, 27, 6, 33, 28, 50, 48, 56, 32, 26, 60, 5, 62, 3, 29, 1, 31, 18, 24, 8, 21, 22, 12, 54, 15, 0, 58, 53, 25, 64, 20, 16, 57, 63, 9], 'cur_cost': 89494.0}, {'tour': [13, 65, 9, 19, 23, 47, 11, 55, 22, 17, 51, 49, 41, 6, 35, 50, 26, 42, 18, 45, 21, 61, 0, 4, 14, 34, 57, 7, 25, 52, 28, 53, 37, 20, 31, 15, 27, 2, 30, 59, 10, 1, 48, 58, 39, 32, 64, 56, 24, 46, 8, 54, 40, 33, 16, 38, 12, 63, 62, 36, 3, 29, 43, 5, 60, 44], 'cur_cost': 113666.0}, {'tour': array([43, 38, 40, 12, 35, 60, 32,  5, 20, 44, 34, 10, 54, 39, 49, 26, 42,
       23, 30,  8, 18, 27, 17, 28, 48, 58, 22, 46, 36, 53, 64, 14,  7, 51,
       25, 24, 62, 59,  0, 57, 16, 33,  1, 45,  4, 15, 19, 47,  2,  9,  6,
        3, 37, 11, 41, 56, 63, 31, 21, 50, 52, 13, 65, 29, 61, 55],
      dtype=int64), 'cur_cost': 107087.0}, {'tour': [0, 8, 1, 5, 4, 6, 2, 10, 11, 9, 3, 7, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10468.0}, {'tour': [65, 9, 31, 6, 2, 10, 22, 17, 3, 49, 52, 55, 45, 0, 25, 11, 14, 15, 59, 40, 53, 20, 23, 50, 19, 56, 32, 5, 62, 38, 42, 18, 21, 61, 54, 16, 8, 43, 26, 51, 60, 63, 34, 12, 27, 37, 4, 1, 36, 48, 47, 13, 33, 41, 44, 39, 30, 24, 7, 28, 46, 57, 35, 58, 64, 29], 'cur_cost': 102321.0}, {'tour': array([ 6, 57, 56,  1, 41, 38, 62,  3, 51, 46, 27, 43, 16, 63, 55, 59, 47,
       42, 44, 61,  4, 52, 24, 20, 18, 48, 58, 21,  2, 36, 35, 10, 26, 31,
       12, 45,  0, 64, 50, 34, 32, 29, 19, 54,  7,  9, 23, 14, 22, 15, 28,
       25, 17, 53, 37, 33,  5, 60, 39, 40, 49, 30,  8, 11, 13, 65],
      dtype=int64), 'cur_cost': 83096.0}, {'tour': [0, 9, 5, 10, 8, 2, 6, 4, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10421.0}, {'tour': [0, 2, 11, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12404.0}, {'tour': array([13,  3,  6, 49, 46, 28, 54, 24, 34, 15, 31, 11, 62, 60, 50, 21, 22,
        7, 23, 47,  9, 48, 64,  4, 36,  8, 57, 43, 30, 59, 58, 56, 14, 17,
       53, 55, 40, 63, 12,  0, 26, 35, 33, 37, 29, 51, 61, 16, 38, 39, 32,
        2, 44, 27, 52, 18, 65,  1, 19, 10, 41, 20, 45, 25,  5, 42],
      dtype=int64), 'cur_cost': 103493.0}, {'tour': [37, 11, 5, 23, 22, 55, 51, 7, 45, 32, 47, 52, 13, 53, 34, 0, 25, 15, 56, 10, 40, 41, 27, 33, 35, 30, 48, 19, 29, 63, 49, 62, 39, 4, 24, 61, 54, 16, 8, 3, 28, 64, 43, 21, 46, 18, 12, 59, 58, 36, 14, 57, 2, 1, 38, 42, 9, 60, 17, 44, 6, 50, 26, 20, 31, 65], 'cur_cost': 112827.0}, {'tour': [19, 26, 11, 14, 16, 4, 2, 17, 13, 21, 8, 3, 65, 0, 6, 23, 27, 35, 33, 1, 55, 64, 54, 56, 10, 37, 43, 40, 46, 34, 5, 9, 53, 7, 15, 31, 28, 47, 20, 30, 32, 45, 12, 29, 24, 39, 48, 41, 42, 36, 50, 22, 49, 58, 61, 52, 62, 59, 60, 57, 63, 44, 51, 38, 18, 25], 'cur_cost': 58855.0}, {'tour': array([47,  3, 62, 35, 33, 55,  1, 16, 19, 56, 23, 53, 18, 40, 57, 44,  8,
       20, 41, 59, 31, 38, 32, 21, 64,  0, 34,  4, 24, 42, 36, 10, 25, 63,
       51, 48, 65, 45, 58, 27, 49, 61, 50, 26, 29, 12, 22, 43, 60, 54, 52,
       28,  9,  6, 37, 11, 30, 13, 15, 46,  5, 17,  2, 14,  7, 39],
      dtype=int64), 'cur_cost': 116675.0}, {'tour': [0, 12, 7, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14733.0}, {'tour': [28, 12, 31, 22, 14, 36, 0, 64, 2, 11, 5, 4, 54, 10, 57, 23, 35, 29, 33, 16, 40, 46, 48, 44, 13, 26, 15, 17, 6, 65, 58, 21, 25, 7, 56, 1, 20, 3, 52, 19, 34, 24, 37, 18, 9, 62, 8, 60, 53, 49, 43, 47, 39, 50, 41, 45, 27, 32, 30, 51, 55, 59, 61, 63, 38, 42], 'cur_cost': 59329.0}, {'tour': array([54, 30, 41, 11, 36, 45, 33, 37, 62, 23, 64, 51, 10, 16, 24, 58, 34,
        7, 61, 17,  1, 49, 38, 26, 42, 40, 35, 46, 43, 21,  4, 32, 60,  5,
        2, 50, 12, 55, 28,  6, 47, 39, 27,  8, 65, 63, 56, 44, 29, 48, 13,
       59, 19, 31, 52, 53, 20,  0, 18, 15, 22, 57, 14,  9,  3, 25],
      dtype=int64), 'cur_cost': 110160.0}, {'tour': [0, 9, 22, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12271.0}, {'tour': [15, 19, 17, 28, 21, 12, 31, 29, 37, 6, 62, 23, 32, 34, 9, 5, 8, 55, 56, 53, 1, 63, 16, 25, 13, 2, 58, 64, 11, 59, 18, 10, 27, 33, 7, 61, 0, 22, 20, 4, 40, 44, 48, 45, 51, 39, 50, 49, 14, 43, 46, 42, 38, 30, 26, 24, 41, 3, 65, 57, 54, 52, 47, 36, 35, 60], 'cur_cost': 59889.0}, {'tour': array([49, 31, 50, 41, 62,  8, 48,  3, 25, 20,  1, 40, 39, 61, 55, 47, 19,
       54,  2,  5,  4, 28, 36,  7, 16, 34, 11,  9, 24, 32, 33, 42, 35, 17,
       64, 43, 29, 14, 21, 38, 53, 63, 22,  6, 37, 56, 23, 46, 15, 59, 26,
       12, 27, 30, 13, 10, 51, 57, 45,  0, 18, 44, 60, 52, 58, 65],
      dtype=int64), 'cur_cost': 100493.0}]
2025-08-03 16:25:40,773 - ExploitationExpert - INFO - 局部搜索耗时: 0.13秒
2025-08-03 16:25:40,774 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-03 16:25:40,774 - experts.management.collaboration_manager - INFO - 个体 19 利用路径生成报告: {'new_tour': array([49, 31, 50, 41, 62,  8, 48,  3, 25, 20,  1, 40, 39, 61, 55, 47, 19,
       54,  2,  5,  4, 28, 36,  7, 16, 34, 11,  9, 24, 32, 33, 42, 35, 17,
       64, 43, 29, 14, 21, 38, 53, 63, 22,  6, 37, 56, 23, 46, 15, 59, 26,
       12, 27, 30, 13, 10, 51, 57, 45,  0, 18, 44, 60, 52, 58, 65],
      dtype=int64), 'cur_cost': 100493.0}
2025-08-03 16:25:40,775 - experts.management.collaboration_manager - INFO - 个体 19 接受新路径 (成本: 100493.00)
2025-08-03 16:25:40,775 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 16, 'rejected': 4, 'elite_protected': 4}
2025-08-03 16:25:40,775 - experts.management.collaboration_manager - INFO - 接受率: 16/20 (80.0%)
2025-08-03 16:25:40,776 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [13, 12, 37, 31, 14, 7, 65, 64, 63, 9, 57, 59, 23, 32, 33, 11, 52, 2, 55, 4, 62, 22, 27, 25, 15, 1, 60, 10, 24, 21, 40, 41, 20, 16, 17, 35, 30, 18, 19, 34, 29, 26, 49, 51, 38, 39, 42, 47, 44, 45, 46, 6, 61, 54, 5, 0, 8, 58, 3, 56, 53, 43, 48, 50, 36, 28], 'cur_cost': 51550.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([54, 31, 42, 58, 26, 32, 14, 39, 34, 20, 44, 24, 33,  8,  5, 62, 53,
        1, 45, 10, 52, 59, 21, 35,  6, 11, 25,  4, 27, 46,  7, 16, 55, 60,
       63, 19, 18, 36, 22, 13, 57, 37, 43,  0, 30, 47, 38, 65, 40, 17, 28,
       50, 12, 61, 56, 29,  3, 41, 64, 15, 23, 51,  9, 48,  2, 49],
      dtype=int64), 'cur_cost': 108303.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [37, 14, 65, 23, 2, 10, 40, 41, 35, 30, 19, 38, 39, 42, 47, 44, 45, 46, 61, 13, 4, 34, 36, 17, 11, 55, 51, 43, 59, 49, 7, 52, 27, 6, 33, 28, 50, 48, 56, 32, 26, 60, 5, 62, 3, 29, 1, 31, 18, 24, 8, 21, 22, 12, 54, 15, 0, 58, 53, 25, 64, 20, 16, 57, 63, 9], 'cur_cost': 89494.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [13, 65, 9, 19, 23, 47, 11, 55, 22, 17, 51, 49, 41, 6, 35, 50, 26, 42, 18, 45, 21, 61, 0, 4, 14, 34, 57, 7, 25, 52, 28, 53, 37, 20, 31, 15, 27, 2, 30, 59, 10, 1, 48, 58, 39, 32, 64, 56, 24, 46, 8, 54, 40, 33, 16, 38, 12, 63, 62, 36, 3, 29, 43, 5, 60, 44], 'cur_cost': 113666.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([43, 38, 40, 12, 35, 60, 32,  5, 20, 44, 34, 10, 54, 39, 49, 26, 42,
       23, 30,  8, 18, 27, 17, 28, 48, 58, 22, 46, 36, 53, 64, 14,  7, 51,
       25, 24, 62, 59,  0, 57, 16, 33,  1, 45,  4, 15, 19, 47,  2,  9,  6,
        3, 37, 11, 41, 56, 63, 31, 21, 50, 52, 13, 65, 29, 61, 55],
      dtype=int64), 'cur_cost': 107087.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 1, 5, 4, 6, 2, 10, 11, 9, 3, 7, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10468.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [65, 9, 31, 6, 2, 10, 22, 17, 3, 49, 52, 55, 45, 0, 25, 11, 14, 15, 59, 40, 53, 20, 23, 50, 19, 56, 32, 5, 62, 38, 42, 18, 21, 61, 54, 16, 8, 43, 26, 51, 60, 63, 34, 12, 27, 37, 4, 1, 36, 48, 47, 13, 33, 41, 44, 39, 30, 24, 7, 28, 46, 57, 35, 58, 64, 29], 'cur_cost': 102321.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 6, 57, 56,  1, 41, 38, 62,  3, 51, 46, 27, 43, 16, 63, 55, 59, 47,
       42, 44, 61,  4, 52, 24, 20, 18, 48, 58, 21,  2, 36, 35, 10, 26, 31,
       12, 45,  0, 64, 50, 34, 32, 29, 19, 54,  7,  9, 23, 14, 22, 15, 28,
       25, 17, 53, 37, 33,  5, 60, 39, 40, 49, 30,  8, 11, 13, 65],
      dtype=int64), 'cur_cost': 83096.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 5, 10, 8, 2, 6, 4, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10421.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 11, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12404.0}}, {'individual': 10, 'strategy': 'exploit', 'path_data': {'new_tour': array([13,  3,  6, 49, 46, 28, 54, 24, 34, 15, 31, 11, 62, 60, 50, 21, 22,
        7, 23, 47,  9, 48, 64,  4, 36,  8, 57, 43, 30, 59, 58, 56, 14, 17,
       53, 55, 40, 63, 12,  0, 26, 35, 33, 37, 29, 51, 61, 16, 38, 39, 32,
        2, 44, 27, 52, 18, 65,  1, 19, 10, 41, 20, 45, 25,  5, 42],
      dtype=int64), 'cur_cost': 103493.0}}, {'individual': 11, 'strategy': 'explore', 'path_data': {'new_tour': [37, 11, 5, 23, 22, 55, 51, 7, 45, 32, 47, 52, 13, 53, 34, 0, 25, 15, 56, 10, 40, 41, 27, 33, 35, 30, 48, 19, 29, 63, 49, 62, 39, 4, 24, 61, 54, 16, 8, 3, 28, 64, 43, 21, 46, 18, 12, 59, 58, 36, 14, 57, 2, 1, 38, 42, 9, 60, 17, 44, 6, 50, 26, 20, 31, 65], 'cur_cost': 112827.0}}, {'individual': 12, 'strategy': 'explore', 'path_data': {'new_tour': [19, 26, 11, 14, 16, 4, 2, 17, 13, 21, 8, 3, 65, 0, 6, 23, 27, 35, 33, 1, 55, 64, 54, 56, 10, 37, 43, 40, 46, 34, 5, 9, 53, 7, 15, 31, 28, 47, 20, 30, 32, 45, 12, 29, 24, 39, 48, 41, 42, 36, 50, 22, 49, 58, 61, 52, 62, 59, 60, 57, 63, 44, 51, 38, 18, 25], 'cur_cost': 58855.0}}, {'individual': 13, 'strategy': 'exploit', 'path_data': {'new_tour': array([47,  3, 62, 35, 33, 55,  1, 16, 19, 56, 23, 53, 18, 40, 57, 44,  8,
       20, 41, 59, 31, 38, 32, 21, 64,  0, 34,  4, 24, 42, 36, 10, 25, 63,
       51, 48, 65, 45, 58, 27, 49, 61, 50, 26, 29, 12, 22, 43, 60, 54, 52,
       28,  9,  6, 37, 11, 30, 13, 15, 46,  5, 17,  2, 14,  7, 39],
      dtype=int64), 'cur_cost': 116675.0}}, {'individual': 14, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 7, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14733.0}}, {'individual': 15, 'strategy': 'explore', 'path_data': {'new_tour': [28, 12, 31, 22, 14, 36, 0, 64, 2, 11, 5, 4, 54, 10, 57, 23, 35, 29, 33, 16, 40, 46, 48, 44, 13, 26, 15, 17, 6, 65, 58, 21, 25, 7, 56, 1, 20, 3, 52, 19, 34, 24, 37, 18, 9, 62, 8, 60, 53, 49, 43, 47, 39, 50, 41, 45, 27, 32, 30, 51, 55, 59, 61, 63, 38, 42], 'cur_cost': 59329.0}}, {'individual': 16, 'strategy': 'exploit', 'path_data': {'new_tour': array([54, 30, 41, 11, 36, 45, 33, 37, 62, 23, 64, 51, 10, 16, 24, 58, 34,
        7, 61, 17,  1, 49, 38, 26, 42, 40, 35, 46, 43, 21,  4, 32, 60,  5,
        2, 50, 12, 55, 28,  6, 47, 39, 27,  8, 65, 63, 56, 44, 29, 48, 13,
       59, 19, 31, 52, 53, 20,  0, 18, 15, 22, 57, 14,  9,  3, 25],
      dtype=int64), 'cur_cost': 110160.0}}, {'individual': 17, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 22, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12271.0}}, {'individual': 18, 'strategy': 'explore', 'path_data': {'new_tour': [15, 19, 17, 28, 21, 12, 31, 29, 37, 6, 62, 23, 32, 34, 9, 5, 8, 55, 56, 53, 1, 63, 16, 25, 13, 2, 58, 64, 11, 59, 18, 10, 27, 33, 7, 61, 0, 22, 20, 4, 40, 44, 48, 45, 51, 39, 50, 49, 14, 43, 46, 42, 38, 30, 26, 24, 41, 3, 65, 57, 54, 52, 47, 36, 35, 60], 'cur_cost': 59889.0}}, {'individual': 19, 'strategy': 'exploit', 'path_data': {'new_tour': array([49, 31, 50, 41, 62,  8, 48,  3, 25, 20,  1, 40, 39, 61, 55, 47, 19,
       54,  2,  5,  4, 28, 36,  7, 16, 34, 11,  9, 24, 32, 33, 42, 35, 17,
       64, 43, 29, 14, 21, 38, 53, 63, 22,  6, 37, 56, 23, 46, 15, 59, 26,
       12, 27, 30, 13, 10, 51, 57, 45,  0, 18, 44, 60, 52, 58, 65],
      dtype=int64), 'cur_cost': 100493.0}}]
2025-08-03 16:25:40,781 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-03 16:25:40,784 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:25:40,800 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=10421.000, 多样性=0.970
2025-08-03 16:25:40,801 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-03 16:25:40,801 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-03 16:25:40,802 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-03 16:25:40,805 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 1, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.00393558846492692, 'best_improvement': -0.05230738160153489}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.003687013519049447}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 16, 'new_count': 16, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.756060606060606, 'new_diversity': 0.756060606060606, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-03 16:25:40,808 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-03 16:25:40,839 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_solution.json
2025-08-03 16:25:40,840 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_20250803_162540.solution
2025-08-03 16:25:40,840 - __main__ - INFO - 实例 composite13_66 处理完成
