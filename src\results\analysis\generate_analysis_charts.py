#!/usr/bin/env python3
"""
TSP Composite13_66 实验数据可视化分析脚本
生成实验报告中的数据图表和统计分析
"""

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns
from matplotlib.font_manager import FontProperties
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def create_analysis_charts():
    """创建实验分析图表"""
    
    # 创建输出目录
    output_dir = "src/results/analysis/charts"
    os.makedirs(output_dir, exist_ok=True)
    
    # 1. 景观特征演化趋势图
    create_landscape_evolution_chart(output_dir)
    
    # 2. 策略分配动态调整图
    create_strategy_allocation_chart(output_dir)
    
    # 3. 适应度导向选择统计图
    create_fitness_selection_chart(output_dir)
    
    # 4. 算法性能评估图
    create_performance_evaluation_chart(output_dir)
    
    # 5. 综合分析仪表板
    create_comprehensive_dashboard(output_dir)
    
    print(f"所有图表已生成并保存到: {output_dir}")

def create_landscape_evolution_chart(output_dir):
    """创建景观特征演化趋势图"""
    
    # 实验数据
    iterations = [0, 1, 2, 8, 9]
    local_optima_density = [0.100, 0.130, 0.267, 0.324, 0.324]
    gradient_strength = [3033.49, 3287.43, 11477.75, 15738.11, 17040.33]
    coverage = [0.002, 0.004, 0.007, 0.019, 0.021]
    diversity = [1.000, 1.000, 1.000, 1.000, 1.000]
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('景观特征演化趋势分析', fontsize=16, fontweight='bold')
    
    # 局部最优密度
    ax1.plot(iterations, local_optima_density, 'o-', color='#2E86AB', linewidth=2, markersize=8)
    ax1.set_title('局部最优密度变化', fontweight='bold')
    ax1.set_xlabel('迭代次数')
    ax1.set_ylabel('密度值')
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(0, 0.4)
    
    # 适应度梯度强度
    ax2.plot(iterations, gradient_strength, 's-', color='#A23B72', linewidth=2, markersize=8)
    ax2.set_title('适应度梯度强度变化', fontweight='bold')
    ax2.set_xlabel('迭代次数')
    ax2.set_ylabel('梯度强度')
    ax2.grid(True, alpha=0.3)
    ax2.ticklabel_format(style='scientific', axis='y', scilimits=(0,0))
    
    # 搜索空间覆盖率
    ax3.plot(iterations, coverage, '^-', color='#F18F01', linewidth=2, markersize=8)
    ax3.set_title('搜索空间覆盖率变化', fontweight='bold')
    ax3.set_xlabel('迭代次数')
    ax3.set_ylabel('覆盖率')
    ax3.grid(True, alpha=0.3)
    ax3.set_ylim(0, 0.025)
    
    # 种群多样性
    ax4.plot(iterations, diversity, 'd-', color='#C73E1D', linewidth=2, markersize=8)
    ax4.set_title('种群多样性维持', fontweight='bold')
    ax4.set_xlabel('迭代次数')
    ax4.set_ylabel('多样性指数')
    ax4.grid(True, alpha=0.3)
    ax4.set_ylim(0.95, 1.05)
    
    plt.tight_layout()
    plt.savefig(f'{output_dir}/landscape_evolution.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_strategy_allocation_chart(output_dir):
    """创建策略分配动态调整图"""
    
    iterations = [0, 1, 2, 8, 9]
    exploration_ratio = [0.800, 0.750, 0.700, 0.550, 0.500]
    exploration_count = [16, 15, 14, 11, 10]
    exploitation_count = [4, 5, 6, 9, 10]
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    fig.suptitle('策略分配动态调整分析', fontsize=16, fontweight='bold')
    
    # 探索比例变化
    ax1.plot(iterations, exploration_ratio, 'o-', color='#2E86AB', linewidth=3, markersize=10)
    ax1.set_title('探索比例动态调整', fontweight='bold')
    ax1.set_xlabel('迭代次数')
    ax1.set_ylabel('探索比例')
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(0.4, 0.9)
    
    # 添加阶段标注
    ax1.axvspan(-0.5, 2.5, alpha=0.2, color='green', label='探索阶段')
    ax1.axvspan(7.5, 9.5, alpha=0.2, color='red', label='收敛阶段')
    ax1.legend()
    
    # 策略分配堆叠图
    width = 0.6
    ax2.bar(iterations, exploration_count, width, label='探索策略', color='#2E86AB', alpha=0.8)
    ax2.bar(iterations, exploitation_count, width, bottom=exploration_count, 
            label='利用策略', color='#A23B72', alpha=0.8)
    
    ax2.set_title('探索vs利用策略分配', fontweight='bold')
    ax2.set_xlabel('迭代次数')
    ax2.set_ylabel('个体数量')
    ax2.legend()
    ax2.grid(True, alpha=0.3, axis='y')
    
    plt.tight_layout()
    plt.savefig(f'{output_dir}/strategy_allocation.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_fitness_selection_chart(output_dir):
    """创建适应度导向选择统计图"""
    
    iterations = list(range(10))
    acceptance_rates = [65.0, 65.0, 65.0, 35.0, 55.0, 55.0, 35.0, 50.0, 45.0, 35.0]
    accepted_counts = [13, 13, 13, 7, 11, 11, 7, 10, 9, 7]
    rejected_counts = [7, 7, 7, 13, 9, 9, 13, 10, 11, 13]
    elite_protected = [4] * 10
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    fig.suptitle('适应度导向选择策略分析', fontsize=16, fontweight='bold')
    
    # 接受率变化趋势
    ax1.plot(iterations, acceptance_rates, 'o-', color='#2E86AB', linewidth=3, markersize=8)
    ax1.fill_between(iterations, acceptance_rates, alpha=0.3, color='#2E86AB')
    ax1.set_title('选择接受率变化趋势', fontweight='bold')
    ax1.set_xlabel('迭代次数')
    ax1.set_ylabel('接受率 (%)')
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(30, 70)
    
    # 添加趋势线
    z = np.polyfit(iterations, acceptance_rates, 1)
    p = np.poly1d(z)
    ax1.plot(iterations, p(iterations), "--", color='red', alpha=0.8, label=f'趋势线 (斜率: {z[0]:.1f})')
    ax1.legend()
    
    # 接受/拒绝统计堆叠图
    width = 0.6
    ax2.bar(iterations, accepted_counts, width, label='接受', color='#2E86AB', alpha=0.8)
    ax2.bar(iterations, rejected_counts, width, bottom=accepted_counts, 
            label='拒绝', color='#A23B72', alpha=0.8)
    
    # 添加精英保护线
    ax2.axhline(y=4, color='#F18F01', linestyle='--', linewidth=2, label='精英保护数')
    
    ax2.set_title('接受/拒绝决策统计', fontweight='bold')
    ax2.set_xlabel('迭代次数')
    ax2.set_ylabel('个体数量')
    ax2.legend()
    ax2.grid(True, alpha=0.3, axis='y')
    
    plt.tight_layout()
    plt.savefig(f'{output_dir}/fitness_selection.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_performance_evaluation_chart(output_dir):
    """创建算法性能评估图"""
    
    iterations = [0, 1, 8, 9]
    overall_scores = [40, 100, 90, 60]
    improvement_rates = [-3.54, 5.43, 9.39, -11.87]
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    fig.suptitle('算法性能评估分析', fontsize=16, fontweight='bold')
    
    # 总体评分变化
    colors = ['#C73E1D' if score < 70 else '#F18F01' if score < 90 else '#2E86AB' for score in overall_scores]
    bars1 = ax1.bar(iterations, overall_scores, color=colors, alpha=0.8, width=0.6)
    ax1.set_title('算法总体评分变化', fontweight='bold')
    ax1.set_xlabel('迭代次数')
    ax1.set_ylabel('评分')
    ax1.set_ylim(0, 110)
    ax1.grid(True, alpha=0.3, axis='y')
    
    # 添加评分标签
    for bar, score in zip(bars1, overall_scores):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 2,
                f'{score}', ha='center', va='bottom', fontweight='bold')
    
    # 成本改进率
    colors2 = ['#C73E1D' if rate < 0 else '#2E86AB' for rate in improvement_rates]
    bars2 = ax2.bar(iterations, improvement_rates, color=colors2, alpha=0.8, width=0.6)
    ax2.set_title('成本改进率变化', fontweight='bold')
    ax2.set_xlabel('迭代次数')
    ax2.set_ylabel('改进率 (%)')
    ax2.axhline(y=0, color='black', linestyle='-', linewidth=1)
    ax2.grid(True, alpha=0.3)
    
    # 添加改进率标签
    for bar, rate in zip(bars2, improvement_rates):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., 
                height + (1 if height > 0 else -2),
                f'{rate:.1f}%', ha='center', 
                va='bottom' if height > 0 else 'top', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig(f'{output_dir}/performance_evaluation.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_comprehensive_dashboard(output_dir):
    """创建综合分析仪表板"""
    
    fig = plt.figure(figsize=(20, 12))
    fig.suptitle('TSP Composite13_66 实验综合分析仪表板', fontsize=20, fontweight='bold')
    
    # 创建网格布局
    gs = fig.add_gridspec(3, 4, hspace=0.3, wspace=0.3)
    
    # 1. 最优解成本展示
    ax1 = fig.add_subplot(gs[0, 0])
    ax1.text(0.5, 0.5, '9521', ha='center', va='center', fontsize=36, fontweight='bold', color='#2E86AB')
    ax1.text(0.5, 0.2, '最优解成本', ha='center', va='center', fontsize=14, fontweight='bold')
    ax1.set_xlim(0, 1)
    ax1.set_ylim(0, 1)
    ax1.axis('off')
    
    # 2. 实验时长
    ax2 = fig.add_subplot(gs[0, 1])
    ax2.text(0.5, 0.5, '20秒', ha='center', va='center', fontsize=36, fontweight='bold', color='#F18F01')
    ax2.text(0.5, 0.2, '实验时长', ha='center', va='center', fontsize=14, fontweight='bold')
    ax2.set_xlim(0, 1)
    ax2.set_ylim(0, 1)
    ax2.axis('off')
    
    # 3. 精英解数量
    ax3 = fig.add_subplot(gs[0, 2])
    ax3.text(0.5, 0.5, '3', ha='center', va='center', fontsize=36, fontweight='bold', color='#A23B72')
    ax3.text(0.5, 0.2, '精英解数量', ha='center', va='center', fontsize=14, fontweight='bold')
    ax3.set_xlim(0, 1)
    ax3.set_ylim(0, 1)
    ax3.axis('off')
    
    # 4. 多样性指数
    ax4 = fig.add_subplot(gs[0, 3])
    ax4.text(0.5, 0.5, '1.000', ha='center', va='center', fontsize=36, fontweight='bold', color='#C73E1D')
    ax4.text(0.5, 0.2, '多样性指数', ha='center', va='center', fontsize=14, fontweight='bold')
    ax4.set_xlim(0, 1)
    ax4.set_ylim(0, 1)
    ax4.axis('off')
    
    # 5. 接受率趋势 (占据2列)
    ax5 = fig.add_subplot(gs[1, :2])
    iterations = list(range(10))
    acceptance_rates = [65.0, 65.0, 65.0, 35.0, 55.0, 55.0, 35.0, 50.0, 45.0, 35.0]
    ax5.plot(iterations, acceptance_rates, 'o-', color='#2E86AB', linewidth=3, markersize=8)
    ax5.fill_between(iterations, acceptance_rates, alpha=0.3, color='#2E86AB')
    ax5.set_title('选择接受率趋势', fontweight='bold')
    ax5.set_xlabel('迭代次数')
    ax5.set_ylabel('接受率 (%)')
    ax5.grid(True, alpha=0.3)
    
    # 6. 策略分配比例 (占据2列)
    ax6 = fig.add_subplot(gs[1, 2:])
    iterations_strategy = [0, 1, 2, 8, 9]
    exploration_ratio = [0.800, 0.750, 0.700, 0.550, 0.500]
    exploitation_ratio = [1-x for x in exploration_ratio]
    
    ax6.stackplot(iterations_strategy, exploration_ratio, exploitation_ratio,
                 labels=['探索策略', '利用策略'], colors=['#2E86AB', '#A23B72'], alpha=0.8)
    ax6.set_title('策略分配比例变化', fontweight='bold')
    ax6.set_xlabel('迭代次数')
    ax6.set_ylabel('策略比例')
    ax6.legend(loc='center right')
    ax6.grid(True, alpha=0.3)
    
    # 7. 景观特征雷达图 (占据4列)
    ax7 = fig.add_subplot(gs[2, :], projection='polar')
    
    # 最终迭代的景观特征 (归一化)
    features = ['局部最优密度', '梯度强度', '覆盖率', '多样性', '收敛趋势']
    values = [0.324, 0.85, 0.42, 1.0, 0.9]  # 归一化后的值
    
    # 闭合雷达图
    angles = np.linspace(0, 2 * np.pi, len(features), endpoint=False).tolist()
    values += values[:1]
    angles += angles[:1]
    
    ax7.plot(angles, values, 'o-', linewidth=2, color='#2E86AB')
    ax7.fill(angles, values, alpha=0.25, color='#2E86AB')
    ax7.set_xticks(angles[:-1])
    ax7.set_xticklabels(features)
    ax7.set_ylim(0, 1)
    ax7.set_title('最终景观特征分析', fontweight='bold', pad=20)
    ax7.grid(True)
    
    plt.savefig(f'{output_dir}/comprehensive_dashboard.png', dpi=300, bbox_inches='tight')
    plt.close()

if __name__ == "__main__":
    create_analysis_charts()
