2025-08-04 17:40:32,966 - __main__ - INFO - simple1_9 开始进化第 1 代
2025-08-04 17:40:32,967 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-04 17:40:32,968 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:40:32,969 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=681.000, 多样性=0.884
2025-08-04 17:40:32,970 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:40:32,971 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.884
2025-08-04 17:40:32,972 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:40:32,974 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-04 17:40:32,974 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:40:32,974 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-04 17:40:32,974 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-04 17:40:33,150 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -31.640, 聚类评分: 0.000, 覆盖率: 0.001, 收敛趋势: 0.000, 多样性: 0.796
2025-08-04 17:40:33,150 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-04 17:40:33,151 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-04 17:40:33,151 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:40:33,157 - visualization.landscape_visualizer - INFO - 插值约束: 3 个点被约束到最小值 681.00
2025-08-04 17:40:33,209 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记，坐标系统已统一
2025-08-04 17:40:33,783 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_1_20250804_174033.html
2025-08-04 17:40:33,821 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_1_20250804_174033.html
2025-08-04 17:40:33,822 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-04 17:40:33,822 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-04 17:40:33,822 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.8488秒
2025-08-04 17:40:33,823 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-04 17:40:33,823 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -31.639999999999997, 'local_optima_density': 0.1, 'gradient_variance': 18590.8304, 'cluster_count': 0}, 'population_state': {'diversity': 0.7955555555555556, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.001, 'fitness_entropy': 0.9739760316291207, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -31.640)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.001)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.796)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754300433.1506147, 'performance_metrics': {}}}
2025-08-04 17:40:33,825 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:40:33,825 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:40:33,825 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:40:33,825 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:40:33,826 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-04 17:40:33,826 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-04 17:40:33,826 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-04 17:40:33,827 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:40:33,827 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:40:33,828 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-04 17:40:33,828 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:40:33,828 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:40:33,829 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-04 17:40:33,829 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:40:33,829 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:40:33,829 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:33,830 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:40:33,830 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:33,987 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1199.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:40:33,987 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [3, 4, 5, 1, 8, 0, 2, 6, 7], 'cur_cost': 1199.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:40:33,988 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 1199.00)
2025-08-04 17:40:33,988 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:40:33,988 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:40:33,988 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:33,989 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:40:33,990 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:33,990 - ExplorationExpert - INFO - 探索路径生成完成，成本: 989.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:40:33,990 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [8, 5, 6, 0, 7, 4, 2, 3, 1], 'cur_cost': 989.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:40:33,990 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 989.00)
2025-08-04 17:40:33,990 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:40:33,991 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:40:33,991 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:33,991 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:40:33,991 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:33,992 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1032.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:40:33,992 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [3, 5, 1, 4, 8, 6, 7, 2, 0], 'cur_cost': 1032.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:40:33,993 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1032.00)
2025-08-04 17:40:33,993 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-04 17:40:33,993 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:40:33,994 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:40:33,995 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1006.0
2025-08-04 17:40:35,779 - ExploitationExpert - INFO - res_population_num: 1
2025-08-04 17:40:35,780 - ExploitationExpert - INFO - res_population_costs: [775.0]
2025-08-04 17:40:35,780 - ExploitationExpert - INFO - res_populations: [array([0, 1, 2, 4, 7, 5, 8, 3, 6], dtype=int64)]
2025-08-04 17:40:35,781 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:40:35,781 - ExploitationExpert - INFO - populations: [{'tour': [3, 4, 5, 1, 8, 0, 2, 6, 7], 'cur_cost': 1199.0}, {'tour': [8, 5, 6, 0, 7, 4, 2, 3, 1], 'cur_cost': 989.0}, {'tour': [3, 5, 1, 4, 8, 6, 7, 2, 0], 'cur_cost': 1032.0}, {'tour': array([7, 0, 3, 6, 8, 2, 1, 4, 5], dtype=int64), 'cur_cost': 1006.0}, {'tour': array([4, 5, 3, 8, 1, 6, 7, 0, 2], dtype=int64), 'cur_cost': 924.0}, {'tour': array([2, 8, 3, 4, 7, 1, 0, 5, 6], dtype=int64), 'cur_cost': 948.0}, {'tour': array([1, 8, 4, 0, 5, 3, 6, 7, 2], dtype=int64), 'cur_cost': 995.0}, {'tour': array([1, 3, 6, 7, 0, 4, 5, 8, 2], dtype=int64), 'cur_cost': 994.0}, {'tour': array([5, 0, 2, 7, 8, 4, 3, 6, 1], dtype=int64), 'cur_cost': 1072.0}, {'tour': array([7, 2, 4, 8, 6, 0, 5, 1, 3], dtype=int64), 'cur_cost': 1036.0}]
2025-08-04 17:40:35,783 - ExploitationExpert - INFO - 局部搜索耗时: 1.79秒，最大迭代次数: 10
2025-08-04 17:40:35,783 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-04 17:40:35,784 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([7, 0, 3, 6, 8, 2, 1, 4, 5], dtype=int64), 'cur_cost': 1006.0, 'intermediate_solutions': [{'tour': array([1, 4, 7, 8, 3, 2, 6, 0, 5], dtype=int64), 'cur_cost': 1094.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 1, 4, 7, 3, 2, 6, 0, 5], dtype=int64), 'cur_cost': 1099.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 8, 1, 4, 7, 2, 6, 0, 5], dtype=int64), 'cur_cost': 1061.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 8, 1, 4, 3, 2, 6, 0, 5], dtype=int64), 'cur_cost': 1124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 3, 8, 1, 4, 2, 6, 0, 5], dtype=int64), 'cur_cost': 961.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:40:35,785 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1006.00)
2025-08-04 17:40:35,785 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:40:35,785 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:40:35,785 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:35,786 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:40:35,786 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:35,786 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1191.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:40:35,786 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [5, 1, 8, 0, 2, 6, 3, 7, 4], 'cur_cost': 1191.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:40:35,786 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1191.00)
2025-08-04 17:40:35,787 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:40:35,787 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:40:35,787 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:35,787 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:40:35,788 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:35,788 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1051.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:40:35,788 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [8, 4, 5, 1, 2, 7, 6, 0, 3], 'cur_cost': 1051.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:40:35,789 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1051.00)
2025-08-04 17:40:35,789 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:40:35,789 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:40:35,789 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:35,790 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:40:35,790 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:35,790 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1046.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:40:35,790 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [7, 8, 4, 6, 2, 5, 3, 0, 1], 'cur_cost': 1046.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:40:35,790 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1046.00)
2025-08-04 17:40:35,791 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:40:35,791 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:40:35,791 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:35,791 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:40:35,791 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:35,792 - ExplorationExpert - INFO - 探索路径生成完成，成本: 960.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:40:35,792 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [8, 4, 7, 6, 0, 5, 3, 1, 2], 'cur_cost': 960.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:40:35,792 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 960.00)
2025-08-04 17:40:35,792 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-04 17:40:35,793 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:40:35,793 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:40:35,793 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 856.0
2025-08-04 17:40:37,974 - ExploitationExpert - INFO - res_population_num: 2
2025-08-04 17:40:37,974 - ExploitationExpert - INFO - res_population_costs: [775.0, 680.0]
2025-08-04 17:40:37,974 - ExploitationExpert - INFO - res_populations: [array([0, 1, 2, 4, 7, 5, 8, 3, 6], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64)]
2025-08-04 17:40:37,975 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:40:37,975 - ExploitationExpert - INFO - populations: [{'tour': [3, 4, 5, 1, 8, 0, 2, 6, 7], 'cur_cost': 1199.0}, {'tour': [8, 5, 6, 0, 7, 4, 2, 3, 1], 'cur_cost': 989.0}, {'tour': [3, 5, 1, 4, 8, 6, 7, 2, 0], 'cur_cost': 1032.0}, {'tour': array([7, 0, 3, 6, 8, 2, 1, 4, 5], dtype=int64), 'cur_cost': 1006.0}, {'tour': [5, 1, 8, 0, 2, 6, 3, 7, 4], 'cur_cost': 1191.0}, {'tour': [8, 4, 5, 1, 2, 7, 6, 0, 3], 'cur_cost': 1051.0}, {'tour': [7, 8, 4, 6, 2, 5, 3, 0, 1], 'cur_cost': 1046.0}, {'tour': [8, 4, 7, 6, 0, 5, 3, 1, 2], 'cur_cost': 960.0}, {'tour': array([3, 8, 7, 5, 6, 0, 2, 4, 1], dtype=int64), 'cur_cost': 856.0}, {'tour': array([7, 2, 4, 8, 6, 0, 5, 1, 3], dtype=int64), 'cur_cost': 1036.0}]
2025-08-04 17:40:37,976 - ExploitationExpert - INFO - 局部搜索耗时: 2.18秒，最大迭代次数: 10
2025-08-04 17:40:37,976 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-04 17:40:37,977 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([3, 8, 7, 5, 6, 0, 2, 4, 1], dtype=int64), 'cur_cost': 856.0, 'intermediate_solutions': [{'tour': array([2, 0, 5, 7, 8, 4, 3, 6, 1], dtype=int64), 'cur_cost': 985.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 2, 0, 5, 8, 4, 3, 6, 1], dtype=int64), 'cur_cost': 1044.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 7, 2, 0, 5, 4, 3, 6, 1], dtype=int64), 'cur_cost': 1129.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 7, 2, 0, 8, 4, 3, 6, 1], dtype=int64), 'cur_cost': 1066.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 8, 7, 2, 0, 4, 3, 6, 1], dtype=int64), 'cur_cost': 1033.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:40:37,978 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 856.00)
2025-08-04 17:40:37,978 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:40:37,978 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:40:37,979 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:37,979 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:40:37,979 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:37,979 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1098.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:40:37,981 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [3, 4, 5, 8, 1, 6, 7, 0, 2], 'cur_cost': 1098.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:40:37,981 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1098.00)
2025-08-04 17:40:37,981 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:40:37,982 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:40:37,983 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [3, 4, 5, 1, 8, 0, 2, 6, 7], 'cur_cost': 1199.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [8, 5, 6, 0, 7, 4, 2, 3, 1], 'cur_cost': 989.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [3, 5, 1, 4, 8, 6, 7, 2, 0], 'cur_cost': 1032.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 0, 3, 6, 8, 2, 1, 4, 5], dtype=int64), 'cur_cost': 1006.0, 'intermediate_solutions': [{'tour': array([1, 4, 7, 8, 3, 2, 6, 0, 5], dtype=int64), 'cur_cost': 1094.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 1, 4, 7, 3, 2, 6, 0, 5], dtype=int64), 'cur_cost': 1099.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 8, 1, 4, 7, 2, 6, 0, 5], dtype=int64), 'cur_cost': 1061.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 8, 1, 4, 3, 2, 6, 0, 5], dtype=int64), 'cur_cost': 1124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 3, 8, 1, 4, 2, 6, 0, 5], dtype=int64), 'cur_cost': 961.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [5, 1, 8, 0, 2, 6, 3, 7, 4], 'cur_cost': 1191.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [8, 4, 5, 1, 2, 7, 6, 0, 3], 'cur_cost': 1051.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [7, 8, 4, 6, 2, 5, 3, 0, 1], 'cur_cost': 1046.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [8, 4, 7, 6, 0, 5, 3, 1, 2], 'cur_cost': 960.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 8, 7, 5, 6, 0, 2, 4, 1], dtype=int64), 'cur_cost': 856.0, 'intermediate_solutions': [{'tour': array([2, 0, 5, 7, 8, 4, 3, 6, 1], dtype=int64), 'cur_cost': 985.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 2, 0, 5, 8, 4, 3, 6, 1], dtype=int64), 'cur_cost': 1044.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 7, 2, 0, 5, 4, 3, 6, 1], dtype=int64), 'cur_cost': 1129.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 7, 2, 0, 8, 4, 3, 6, 1], dtype=int64), 'cur_cost': 1066.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 8, 7, 2, 0, 4, 3, 6, 1], dtype=int64), 'cur_cost': 1033.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [3, 4, 5, 8, 1, 6, 7, 0, 2], 'cur_cost': 1098.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-04 17:40:37,984 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:40:37,985 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:40:37,986 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=856.000, 多样性=0.869
2025-08-04 17:40:37,986 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-04 17:40:37,986 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-04 17:40:37,986 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:40:37,987 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.15425439541430322, 'best_improvement': -0.25697503671071953}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.016759776536312998}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6666666666666666, 'new_diversity': 0.6666666666666666, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-04 17:40:37,987 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-04 17:40:37,988 - __main__ - INFO - simple1_9 开始进化第 2 代
2025-08-04 17:40:37,988 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-04 17:40:37,988 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:40:37,989 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=856.000, 多样性=0.869
2025-08-04 17:40:37,989 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:40:37,990 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.869
2025-08-04 17:40:37,990 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:40:37,990 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.667
2025-08-04 17:40:37,992 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-04 17:40:37,992 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:40:37,992 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-04 17:40:37,993 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-04 17:40:37,999 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.250, 适应度梯度: 24.683, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 0.659
2025-08-04 17:40:38,000 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-04 17:40:38,000 - LandscapeExpert - INFO - 提取到 2 个精英解
2025-08-04 17:40:38,000 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:40:38,003 - visualization.landscape_visualizer - INFO - 插值约束: 192 个点被约束到最小值 680.00
2025-08-04 17:40:38,007 - visualization.landscape_visualizer - INFO - 已添加 2 个精英解标记，坐标系统已统一
2025-08-04 17:40:38,096 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_2_20250804_174038.html
2025-08-04 17:40:38,135 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_2_20250804_174038.html
2025-08-04 17:40:38,136 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 2
2025-08-04 17:40:38,136 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-04 17:40:38,136 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1445秒
2025-08-04 17:40:38,136 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.25, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 24.683333333333326, 'local_optima_density': 0.25, 'gradient_variance': 27422.51638888889, 'cluster_count': 0}, 'population_state': {'diversity': 0.6590909090909091, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0022, 'fitness_entropy': 0.972765278018163, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 24.683)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754300438.000122, 'performance_metrics': {}}}
2025-08-04 17:40:38,137 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:40:38,137 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:40:38,138 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:40:38,138 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:40:38,138 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-04 17:40:38,139 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-04 17:40:38,139 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-04 17:40:38,139 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:40:38,140 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:40:38,140 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-04 17:40:38,140 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:40:38,141 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:40:38,141 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 7} (总数: 2, 保护比例: 0.20)
2025-08-04 17:40:38,141 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-04 17:40:38,141 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:40:38,142 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:40:38,142 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 881.0
2025-08-04 17:40:38,678 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:40:38,679 - ExploitationExpert - INFO - res_population_costs: [680.0, 775.0, 680.0]
2025-08-04 17:40:38,679 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 2, 4, 7, 5, 8, 3, 6], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 17:40:38,681 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:40:38,681 - ExploitationExpert - INFO - populations: [{'tour': array([7, 3, 5, 4, 1, 0, 6, 8, 2], dtype=int64), 'cur_cost': 881.0}, {'tour': [8, 5, 6, 0, 7, 4, 2, 3, 1], 'cur_cost': 989.0}, {'tour': [3, 5, 1, 4, 8, 6, 7, 2, 0], 'cur_cost': 1032.0}, {'tour': [7, 0, 3, 6, 8, 2, 1, 4, 5], 'cur_cost': 1006.0}, {'tour': [5, 1, 8, 0, 2, 6, 3, 7, 4], 'cur_cost': 1191.0}, {'tour': [8, 4, 5, 1, 2, 7, 6, 0, 3], 'cur_cost': 1051.0}, {'tour': [7, 8, 4, 6, 2, 5, 3, 0, 1], 'cur_cost': 1046.0}, {'tour': [8, 4, 7, 6, 0, 5, 3, 1, 2], 'cur_cost': 960.0}, {'tour': [3, 8, 7, 5, 6, 0, 2, 4, 1], 'cur_cost': 856.0}, {'tour': [3, 4, 5, 8, 1, 6, 7, 0, 2], 'cur_cost': 1098.0}]
2025-08-04 17:40:38,682 - ExploitationExpert - INFO - 局部搜索耗时: 0.54秒，最大迭代次数: 10
2025-08-04 17:40:38,682 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-04 17:40:38,683 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([7, 3, 5, 4, 1, 0, 6, 8, 2], dtype=int64), 'cur_cost': 881.0, 'intermediate_solutions': [{'tour': array([5, 4, 3, 1, 8, 0, 2, 6, 7]), 'cur_cost': 1209.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 5, 4, 3, 8, 0, 2, 6, 7]), 'cur_cost': 1166.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 1, 5, 4, 3, 0, 2, 6, 7]), 'cur_cost': 1212.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 1, 5, 4, 8, 0, 2, 6, 7]), 'cur_cost': 1162.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 8, 1, 5, 4, 0, 2, 6, 7]), 'cur_cost': 1104.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:40:38,683 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 881.00)
2025-08-04 17:40:38,683 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:40:38,684 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:40:38,684 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:38,684 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:40:38,685 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,685 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,685 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,685 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,685 - ExplorationExpert - INFO - 探索路径生成完成，成本: 912.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:40:38,686 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [3, 5, 8, 4, 0, 7, 6, 1, 2], 'cur_cost': 912.0, 'intermediate_solutions': [{'tour': [8, 3, 6, 0, 7, 4, 2, 5, 1], 'cur_cost': 1023.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 5, 6, 0, 7, 4, 2, 1, 3], 'cur_cost': 880.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 5, 6, 7, 4, 0, 2, 3, 1], 'cur_cost': 1046.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:40:38,686 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 912.00)
2025-08-04 17:40:38,686 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:40:38,687 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:40:38,687 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:38,687 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:40:38,688 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,688 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,688 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,688 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,688 - ExplorationExpert - INFO - 探索路径生成完成，成本: 681.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:40:38,689 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [1, 4, 2, 8, 3, 7, 5, 6, 0], 'cur_cost': 681.0, 'intermediate_solutions': [{'tour': [1, 5, 3, 4, 8, 6, 7, 2, 0], 'cur_cost': 965.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 0, 2, 7, 6, 8, 4, 1], 'cur_cost': 1035.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 5, 1, 4, 8, 6, 7, 2, 0], 'cur_cost': 1032.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:40:38,689 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 681.00)
2025-08-04 17:40:38,689 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:40:38,689 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:40:38,690 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:38,690 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:40:38,690 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,691 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,691 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,691 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,691 - ExplorationExpert - INFO - 探索路径生成完成，成本: 810.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:40:38,692 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [8, 3, 7, 0, 1, 4, 2, 5, 6], 'cur_cost': 810.0, 'intermediate_solutions': [{'tour': [7, 0, 3, 8, 6, 2, 1, 4, 5], 'cur_cost': 1073.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 1, 2, 8, 6, 3, 0, 7, 5], 'cur_cost': 1006.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 3, 6, 8, 2, 1, 4, 5], 'cur_cost': 999.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:40:38,692 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 810.00)
2025-08-04 17:40:38,692 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-04 17:40:38,692 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:40:38,693 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:40:38,693 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 940.0
2025-08-04 17:40:38,700 - ExploitationExpert - INFO - res_population_num: 4
2025-08-04 17:40:38,700 - ExploitationExpert - INFO - res_population_costs: [680.0, 775.0, 680.0, 680.0]
2025-08-04 17:40:38,700 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 2, 4, 7, 5, 8, 3, 6], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64)]
2025-08-04 17:40:38,701 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:40:38,702 - ExploitationExpert - INFO - populations: [{'tour': array([7, 3, 5, 4, 1, 0, 6, 8, 2], dtype=int64), 'cur_cost': 881.0}, {'tour': [3, 5, 8, 4, 0, 7, 6, 1, 2], 'cur_cost': 912.0}, {'tour': [1, 4, 2, 8, 3, 7, 5, 6, 0], 'cur_cost': 681.0}, {'tour': [8, 3, 7, 0, 1, 4, 2, 5, 6], 'cur_cost': 810.0}, {'tour': array([1, 0, 8, 3, 6, 2, 4, 5, 7], dtype=int64), 'cur_cost': 940.0}, {'tour': [8, 4, 5, 1, 2, 7, 6, 0, 3], 'cur_cost': 1051.0}, {'tour': [7, 8, 4, 6, 2, 5, 3, 0, 1], 'cur_cost': 1046.0}, {'tour': [8, 4, 7, 6, 0, 5, 3, 1, 2], 'cur_cost': 960.0}, {'tour': [3, 8, 7, 5, 6, 0, 2, 4, 1], 'cur_cost': 856.0}, {'tour': [3, 4, 5, 8, 1, 6, 7, 0, 2], 'cur_cost': 1098.0}]
2025-08-04 17:40:38,702 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:40:38,703 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-04 17:40:38,703 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([1, 0, 8, 3, 6, 2, 4, 5, 7], dtype=int64), 'cur_cost': 940.0, 'intermediate_solutions': [{'tour': array([8, 1, 5, 0, 2, 6, 3, 7, 4]), 'cur_cost': 1135.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 8, 1, 5, 2, 6, 3, 7, 4]), 'cur_cost': 1180.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 0, 8, 1, 5, 6, 3, 7, 4]), 'cur_cost': 985.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 0, 8, 1, 2, 6, 3, 7, 4]), 'cur_cost': 1164.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 2, 0, 8, 1, 6, 3, 7, 4]), 'cur_cost': 1128.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:40:38,704 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 940.00)
2025-08-04 17:40:38,704 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:40:38,704 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:40:38,704 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:38,705 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:40:38,705 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,706 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,706 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,706 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,706 - ExplorationExpert - INFO - 探索路径生成完成，成本: 978.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:40:38,707 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [5, 2, 0, 4, 1, 7, 6, 8, 3], 'cur_cost': 978.0, 'intermediate_solutions': [{'tour': [8, 4, 7, 1, 2, 5, 6, 0, 3], 'cur_cost': 994.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 4, 5, 1, 2, 3, 0, 6, 7], 'cur_cost': 1078.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 8, 4, 5, 1, 2, 7, 0, 3], 'cur_cost': 1119.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:40:38,707 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 978.00)
2025-08-04 17:40:38,708 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:40:38,708 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:40:38,708 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:38,708 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:40:38,708 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,709 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,709 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,709 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,709 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1146.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:40:38,709 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 3, 4, 6, 1, 8, 7, 5, 2], 'cur_cost': 1146.0, 'intermediate_solutions': [{'tour': [7, 8, 4, 6, 5, 2, 3, 0, 1], 'cur_cost': 1014.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 5, 2, 6, 4, 8, 0, 1], 'cur_cost': 1033.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 0, 8, 4, 6, 2, 5, 3, 1], 'cur_cost': 1179.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:40:38,710 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1146.00)
2025-08-04 17:40:38,710 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:40:38,710 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:40:38,710 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:38,711 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:40:38,711 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,711 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,712 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,712 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,712 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1113.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:40:38,712 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [3, 6, 2, 7, 0, 4, 8, 5, 1], 'cur_cost': 1113.0, 'intermediate_solutions': [{'tour': [8, 4, 5, 6, 0, 7, 3, 1, 2], 'cur_cost': 951.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 4, 7, 3, 5, 0, 6, 1, 2], 'cur_cost': 893.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 0, 4, 7, 6, 5, 3, 1, 2], 'cur_cost': 932.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:40:38,713 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 1113.00)
2025-08-04 17:40:38,713 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:40:38,713 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:40:38,714 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:38,714 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:40:38,714 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,715 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,715 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,715 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,715 - ExplorationExpert - INFO - 探索路径生成完成，成本: 848.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:40:38,715 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [2, 4, 7, 5, 3, 8, 0, 1, 6], 'cur_cost': 848.0, 'intermediate_solutions': [{'tour': [3, 8, 0, 5, 6, 7, 2, 4, 1], 'cur_cost': 951.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 7, 5, 6, 0, 2, 1, 4], 'cur_cost': 895.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 7, 5, 6, 0, 2, 4, 1, 3], 'cur_cost': 856.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:40:38,716 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 848.00)
2025-08-04 17:40:38,716 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:40:38,716 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:40:38,716 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:38,717 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:40:38,717 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,717 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,717 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,717 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,718 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1065.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:40:38,718 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [8, 4, 5, 1, 2, 7, 3, 6, 0], 'cur_cost': 1065.0, 'intermediate_solutions': [{'tour': [3, 4, 2, 8, 1, 6, 7, 0, 5], 'cur_cost': 926.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 4, 5, 8, 6, 1, 7, 0, 2], 'cur_cost': 1106.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 4, 5, 8, 6, 1, 7, 0, 2], 'cur_cost': 1106.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:40:38,718 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1065.00)
2025-08-04 17:40:38,718 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:40:38,719 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:40:38,720 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 3, 5, 4, 1, 0, 6, 8, 2], dtype=int64), 'cur_cost': 881.0, 'intermediate_solutions': [{'tour': array([5, 4, 3, 1, 8, 0, 2, 6, 7]), 'cur_cost': 1209.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 5, 4, 3, 8, 0, 2, 6, 7]), 'cur_cost': 1166.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 1, 5, 4, 3, 0, 2, 6, 7]), 'cur_cost': 1212.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 1, 5, 4, 8, 0, 2, 6, 7]), 'cur_cost': 1162.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 8, 1, 5, 4, 0, 2, 6, 7]), 'cur_cost': 1104.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [3, 5, 8, 4, 0, 7, 6, 1, 2], 'cur_cost': 912.0, 'intermediate_solutions': [{'tour': [8, 3, 6, 0, 7, 4, 2, 5, 1], 'cur_cost': 1023.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 5, 6, 0, 7, 4, 2, 1, 3], 'cur_cost': 880.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 5, 6, 7, 4, 0, 2, 3, 1], 'cur_cost': 1046.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 2, 8, 3, 7, 5, 6, 0], 'cur_cost': 681.0, 'intermediate_solutions': [{'tour': [1, 5, 3, 4, 8, 6, 7, 2, 0], 'cur_cost': 965.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 0, 2, 7, 6, 8, 4, 1], 'cur_cost': 1035.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 5, 1, 4, 8, 6, 7, 2, 0], 'cur_cost': 1032.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [8, 3, 7, 0, 1, 4, 2, 5, 6], 'cur_cost': 810.0, 'intermediate_solutions': [{'tour': [7, 0, 3, 8, 6, 2, 1, 4, 5], 'cur_cost': 1073.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 1, 2, 8, 6, 3, 0, 7, 5], 'cur_cost': 1006.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 3, 6, 8, 2, 1, 4, 5], 'cur_cost': 999.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([1, 0, 8, 3, 6, 2, 4, 5, 7], dtype=int64), 'cur_cost': 940.0, 'intermediate_solutions': [{'tour': array([8, 1, 5, 0, 2, 6, 3, 7, 4]), 'cur_cost': 1135.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 8, 1, 5, 2, 6, 3, 7, 4]), 'cur_cost': 1180.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 0, 8, 1, 5, 6, 3, 7, 4]), 'cur_cost': 985.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 0, 8, 1, 2, 6, 3, 7, 4]), 'cur_cost': 1164.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 2, 0, 8, 1, 6, 3, 7, 4]), 'cur_cost': 1128.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [5, 2, 0, 4, 1, 7, 6, 8, 3], 'cur_cost': 978.0, 'intermediate_solutions': [{'tour': [8, 4, 7, 1, 2, 5, 6, 0, 3], 'cur_cost': 994.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 4, 5, 1, 2, 3, 0, 6, 7], 'cur_cost': 1078.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 8, 4, 5, 1, 2, 7, 0, 3], 'cur_cost': 1119.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 4, 6, 1, 8, 7, 5, 2], 'cur_cost': 1146.0, 'intermediate_solutions': [{'tour': [7, 8, 4, 6, 5, 2, 3, 0, 1], 'cur_cost': 1014.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 5, 2, 6, 4, 8, 0, 1], 'cur_cost': 1033.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 0, 8, 4, 6, 2, 5, 3, 1], 'cur_cost': 1179.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [3, 6, 2, 7, 0, 4, 8, 5, 1], 'cur_cost': 1113.0, 'intermediate_solutions': [{'tour': [8, 4, 5, 6, 0, 7, 3, 1, 2], 'cur_cost': 951.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 4, 7, 3, 5, 0, 6, 1, 2], 'cur_cost': 893.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 0, 4, 7, 6, 5, 3, 1, 2], 'cur_cost': 932.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [2, 4, 7, 5, 3, 8, 0, 1, 6], 'cur_cost': 848.0, 'intermediate_solutions': [{'tour': [3, 8, 0, 5, 6, 7, 2, 4, 1], 'cur_cost': 951.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 7, 5, 6, 0, 2, 1, 4], 'cur_cost': 895.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 7, 5, 6, 0, 2, 4, 1, 3], 'cur_cost': 856.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [8, 4, 5, 1, 2, 7, 3, 6, 0], 'cur_cost': 1065.0, 'intermediate_solutions': [{'tour': [3, 4, 2, 8, 1, 6, 7, 0, 5], 'cur_cost': 926.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 4, 5, 8, 6, 1, 7, 0, 2], 'cur_cost': 1106.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 4, 5, 8, 6, 1, 7, 0, 2], 'cur_cost': 1106.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:40:38,722 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:40:38,722 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:40:38,723 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=681.000, 多样性=0.879
2025-08-04 17:40:38,723 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-04 17:40:38,723 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-04 17:40:38,724 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:40:38,724 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.11657226223644206, 'best_improvement': 0.2044392523364486}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.011363636363636595}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.7222222222222222, 'new_diversity': 0.7222222222222222, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-04 17:40:38,724 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-04 17:40:38,725 - __main__ - INFO - simple1_9 开始进化第 3 代
2025-08-04 17:40:38,725 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-04 17:40:38,725 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:40:38,726 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=681.000, 多样性=0.879
2025-08-04 17:40:38,726 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:40:38,726 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.879
2025-08-04 17:40:38,727 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:40:38,727 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.722
2025-08-04 17:40:38,729 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-04 17:40:38,729 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:40:38,730 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-04 17:40:38,730 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-04 17:40:38,738 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.286, 适应度梯度: -0.829, 聚类评分: 0.000, 覆盖率: 0.003, 收敛趋势: 0.000, 多样性: 0.565
2025-08-04 17:40:38,738 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-04 17:40:38,738 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:40:38,738 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:40:38,742 - visualization.landscape_visualizer - INFO - 插值约束: 78 个点被约束到最小值 680.00
2025-08-04 17:40:38,747 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:40:38,829 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_3_20250804_174038.html
2025-08-04 17:40:38,871 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_3_20250804_174038.html
2025-08-04 17:40:38,871 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 3
2025-08-04 17:40:38,871 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-04 17:40:38,872 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1434秒
2025-08-04 17:40:38,872 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2857142857142857, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -0.8285714285714187, 'local_optima_density': 0.2857142857142857, 'gradient_variance': 35113.69632653061, 'cluster_count': 0}, 'population_state': {'diversity': 0.565149136577708, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0034, 'fitness_entropy': 0.9357849740192012, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -0.829)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.003)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754300438.7380357, 'performance_metrics': {}}}
2025-08-04 17:40:38,873 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:40:38,873 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:40:38,873 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:40:38,873 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:40:38,874 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit']
2025-08-04 17:40:38,874 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-04 17:40:38,875 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit']
2025-08-04 17:40:38,875 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:40:38,875 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:40:38,876 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit']
2025-08-04 17:40:38,876 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:40:38,876 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:40:38,877 - experts.management.collaboration_manager - INFO - 识别精英个体: {2, 3} (总数: 2, 保护比例: 0.20)
2025-08-04 17:40:38,877 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:40:38,877 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:40:38,877 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:38,878 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:40:38,878 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,879 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,879 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,879 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,879 - ExplorationExpert - INFO - 探索路径生成完成，成本: 896.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:40:38,879 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 6, 7, 8, 4, 3, 5, 2, 1], 'cur_cost': 896.0, 'intermediate_solutions': [{'tour': [0, 3, 5, 4, 1, 7, 6, 8, 2], 'cur_cost': 1008.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 6, 0, 1, 4, 5, 3, 8, 2], 'cur_cost': 840.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 7, 3, 5, 4, 1, 0, 6, 8], 'cur_cost': 881.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:40:38,880 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 896.00)
2025-08-04 17:40:38,880 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:40:38,880 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:40:38,880 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:38,881 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:40:38,881 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,881 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,881 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,882 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,882 - ExplorationExpert - INFO - 探索路径生成完成，成本: 829.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:40:38,882 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [8, 3, 5, 7, 0, 1, 4, 2, 6], 'cur_cost': 829.0, 'intermediate_solutions': [{'tour': [3, 5, 7, 4, 0, 8, 6, 1, 2], 'cur_cost': 995.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 8, 4, 0, 7, 2, 1, 6], 'cur_cost': 912.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 8, 3, 4, 0, 7, 6, 1, 2], 'cur_cost': 978.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:40:38,882 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 829.00)
2025-08-04 17:40:38,883 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:40:38,883 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:40:38,883 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:38,883 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:40:38,884 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,884 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,884 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,884 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,884 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1050.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:40:38,885 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [5, 0, 8, 3, 4, 1, 2, 6, 7], 'cur_cost': 1050.0, 'intermediate_solutions': [{'tour': [1, 4, 2, 7, 3, 8, 5, 6, 0], 'cur_cost': 739.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 2, 8, 3, 7, 5, 0, 6], 'cur_cost': 815.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 2, 5, 8, 3, 7, 6, 0], 'cur_cost': 793.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:40:38,885 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 1050.00)
2025-08-04 17:40:38,885 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:40:38,886 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:40:38,886 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:38,886 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:40:38,887 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,887 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,887 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,887 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,888 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1001.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:40:38,888 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [4, 7, 1, 3, 0, 5, 6, 8, 2], 'cur_cost': 1001.0, 'intermediate_solutions': [{'tour': [8, 3, 7, 5, 1, 4, 2, 0, 6], 'cur_cost': 901.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 3, 7, 0, 1, 4, 2, 6, 5], 'cur_cost': 785.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 3, 7, 1, 4, 2, 5, 0, 6], 'cur_cost': 963.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:40:38,888 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 1001.00)
2025-08-04 17:40:38,889 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:40:38,889 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:40:38,889 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:38,889 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:40:38,889 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,890 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,890 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,890 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,890 - ExplorationExpert - INFO - 探索路径生成完成，成本: 983.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:40:38,890 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [1, 7, 0, 8, 6, 3, 5, 2, 4], 'cur_cost': 983.0, 'intermediate_solutions': [{'tour': [1, 7, 8, 3, 6, 2, 4, 5, 0], 'cur_cost': 946.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 2, 6, 3, 8, 0, 5, 7], 'cur_cost': 990.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 1, 0, 8, 3, 6, 2, 5, 7], 'cur_cost': 992.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:40:38,891 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 983.00)
2025-08-04 17:40:38,891 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:40:38,891 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:40:38,891 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:38,892 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:40:38,892 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,892 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,892 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,893 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,893 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1016.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:40:38,893 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [4, 5, 2, 3, 7, 6, 0, 1, 8], 'cur_cost': 1016.0, 'intermediate_solutions': [{'tour': [5, 2, 0, 7, 1, 4, 6, 8, 3], 'cur_cost': 1045.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 5, 0, 4, 1, 7, 6, 8, 3], 'cur_cost': 1066.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 5, 2, 0, 4, 7, 6, 8, 3], 'cur_cost': 1109.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:40:38,893 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1016.00)
2025-08-04 17:40:38,894 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-04 17:40:38,894 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:40:38,894 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:40:38,894 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1032.0
2025-08-04 17:40:38,901 - ExploitationExpert - INFO - res_population_num: 4
2025-08-04 17:40:38,901 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0, 775.0]
2025-08-04 17:40:38,901 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 2, 4, 7, 5, 8, 3, 6], dtype=int64)]
2025-08-04 17:40:38,902 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:40:38,902 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 7, 8, 4, 3, 5, 2, 1], 'cur_cost': 896.0}, {'tour': [8, 3, 5, 7, 0, 1, 4, 2, 6], 'cur_cost': 829.0}, {'tour': [5, 0, 8, 3, 4, 1, 2, 6, 7], 'cur_cost': 1050.0}, {'tour': [4, 7, 1, 3, 0, 5, 6, 8, 2], 'cur_cost': 1001.0}, {'tour': [1, 7, 0, 8, 6, 3, 5, 2, 4], 'cur_cost': 983.0}, {'tour': [4, 5, 2, 3, 7, 6, 0, 1, 8], 'cur_cost': 1016.0}, {'tour': array([2, 5, 7, 8, 1, 0, 4, 6, 3], dtype=int64), 'cur_cost': 1032.0}, {'tour': [3, 6, 2, 7, 0, 4, 8, 5, 1], 'cur_cost': 1113.0}, {'tour': [2, 4, 7, 5, 3, 8, 0, 1, 6], 'cur_cost': 848.0}, {'tour': [8, 4, 5, 1, 2, 7, 3, 6, 0], 'cur_cost': 1065.0}]
2025-08-04 17:40:38,903 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:40:38,903 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-04 17:40:38,904 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([2, 5, 7, 8, 1, 0, 4, 6, 3], dtype=int64), 'cur_cost': 1032.0, 'intermediate_solutions': [{'tour': array([4, 3, 0, 6, 1, 8, 7, 5, 2]), 'cur_cost': 1022.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 4, 3, 0, 1, 8, 7, 5, 2]), 'cur_cost': 1132.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 6, 4, 3, 0, 8, 7, 5, 2]), 'cur_cost': 1118.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 6, 4, 3, 1, 8, 7, 5, 2]), 'cur_cost': 1165.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 1, 6, 4, 3, 8, 7, 5, 2]), 'cur_cost': 967.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:40:38,904 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1032.00)
2025-08-04 17:40:38,905 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-04 17:40:38,905 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:40:38,905 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:40:38,905 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 993.0
2025-08-04 17:40:38,912 - ExploitationExpert - INFO - res_population_num: 4
2025-08-04 17:40:38,912 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0, 775.0]
2025-08-04 17:40:38,913 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 2, 4, 7, 5, 8, 3, 6], dtype=int64)]
2025-08-04 17:40:38,914 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:40:38,914 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 7, 8, 4, 3, 5, 2, 1], 'cur_cost': 896.0}, {'tour': [8, 3, 5, 7, 0, 1, 4, 2, 6], 'cur_cost': 829.0}, {'tour': [5, 0, 8, 3, 4, 1, 2, 6, 7], 'cur_cost': 1050.0}, {'tour': [4, 7, 1, 3, 0, 5, 6, 8, 2], 'cur_cost': 1001.0}, {'tour': [1, 7, 0, 8, 6, 3, 5, 2, 4], 'cur_cost': 983.0}, {'tour': [4, 5, 2, 3, 7, 6, 0, 1, 8], 'cur_cost': 1016.0}, {'tour': array([2, 5, 7, 8, 1, 0, 4, 6, 3], dtype=int64), 'cur_cost': 1032.0}, {'tour': array([2, 4, 5, 0, 7, 3, 1, 6, 8], dtype=int64), 'cur_cost': 993.0}, {'tour': [2, 4, 7, 5, 3, 8, 0, 1, 6], 'cur_cost': 848.0}, {'tour': [8, 4, 5, 1, 2, 7, 3, 6, 0], 'cur_cost': 1065.0}]
2025-08-04 17:40:38,915 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:40:38,915 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-04 17:40:38,915 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([2, 4, 5, 0, 7, 3, 1, 6, 8], dtype=int64), 'cur_cost': 993.0, 'intermediate_solutions': [{'tour': array([2, 6, 3, 7, 0, 4, 8, 5, 1]), 'cur_cost': 1016.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 2, 6, 3, 0, 4, 8, 5, 1]), 'cur_cost': 1113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 7, 2, 6, 3, 4, 8, 5, 1]), 'cur_cost': 1027.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 7, 2, 6, 0, 4, 8, 5, 1]), 'cur_cost': 1086.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 0, 7, 2, 6, 4, 8, 5, 1]), 'cur_cost': 1201.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:40:38,916 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 993.00)
2025-08-04 17:40:38,916 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:40:38,916 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:40:38,916 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:38,917 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:40:38,917 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,917 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,917 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,918 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:38,918 - ExplorationExpert - INFO - 探索路径生成完成，成本: 974.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:40:38,918 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [5, 6, 8, 7, 2, 3, 4, 1, 0], 'cur_cost': 974.0, 'intermediate_solutions': [{'tour': [8, 4, 7, 5, 3, 2, 0, 1, 6], 'cur_cost': 900.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 4, 7, 5, 3, 8, 6, 1, 0], 'cur_cost': 789.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 7, 3, 8, 0, 5, 1, 6], 'cur_cost': 1026.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:40:38,919 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 974.00)
2025-08-04 17:40:38,919 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-04 17:40:38,919 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:40:38,919 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:40:38,919 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 1047.0
2025-08-04 17:40:38,927 - ExploitationExpert - INFO - res_population_num: 4
2025-08-04 17:40:38,927 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0, 775.0]
2025-08-04 17:40:38,927 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 2, 4, 7, 5, 8, 3, 6], dtype=int64)]
2025-08-04 17:40:38,929 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:40:38,929 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 7, 8, 4, 3, 5, 2, 1], 'cur_cost': 896.0}, {'tour': [8, 3, 5, 7, 0, 1, 4, 2, 6], 'cur_cost': 829.0}, {'tour': [5, 0, 8, 3, 4, 1, 2, 6, 7], 'cur_cost': 1050.0}, {'tour': [4, 7, 1, 3, 0, 5, 6, 8, 2], 'cur_cost': 1001.0}, {'tour': [1, 7, 0, 8, 6, 3, 5, 2, 4], 'cur_cost': 983.0}, {'tour': [4, 5, 2, 3, 7, 6, 0, 1, 8], 'cur_cost': 1016.0}, {'tour': array([2, 5, 7, 8, 1, 0, 4, 6, 3], dtype=int64), 'cur_cost': 1032.0}, {'tour': array([2, 4, 5, 0, 7, 3, 1, 6, 8], dtype=int64), 'cur_cost': 993.0}, {'tour': [5, 6, 8, 7, 2, 3, 4, 1, 0], 'cur_cost': 974.0}, {'tour': array([5, 4, 3, 8, 1, 0, 6, 2, 7], dtype=int64), 'cur_cost': 1047.0}]
2025-08-04 17:40:38,931 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:40:38,931 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-04 17:40:38,932 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([5, 4, 3, 8, 1, 0, 6, 2, 7], dtype=int64), 'cur_cost': 1047.0, 'intermediate_solutions': [{'tour': array([5, 4, 8, 1, 2, 7, 3, 6, 0]), 'cur_cost': 1066.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 5, 4, 8, 2, 7, 3, 6, 0]), 'cur_cost': 933.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 1, 5, 4, 8, 7, 3, 6, 0]), 'cur_cost': 1011.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 1, 5, 4, 2, 7, 3, 6, 0]), 'cur_cost': 1063.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 2, 1, 5, 4, 7, 3, 6, 0]), 'cur_cost': 1034.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:40:38,933 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1047.00)
2025-08-04 17:40:38,933 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:40:38,933 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:40:38,935 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 7, 8, 4, 3, 5, 2, 1], 'cur_cost': 896.0, 'intermediate_solutions': [{'tour': [0, 3, 5, 4, 1, 7, 6, 8, 2], 'cur_cost': 1008.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 6, 0, 1, 4, 5, 3, 8, 2], 'cur_cost': 840.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 7, 3, 5, 4, 1, 0, 6, 8], 'cur_cost': 881.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [8, 3, 5, 7, 0, 1, 4, 2, 6], 'cur_cost': 829.0, 'intermediate_solutions': [{'tour': [3, 5, 7, 4, 0, 8, 6, 1, 2], 'cur_cost': 995.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 8, 4, 0, 7, 2, 1, 6], 'cur_cost': 912.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 8, 3, 4, 0, 7, 6, 1, 2], 'cur_cost': 978.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [5, 0, 8, 3, 4, 1, 2, 6, 7], 'cur_cost': 1050.0, 'intermediate_solutions': [{'tour': [1, 4, 2, 7, 3, 8, 5, 6, 0], 'cur_cost': 739.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 2, 8, 3, 7, 5, 0, 6], 'cur_cost': 815.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 2, 5, 8, 3, 7, 6, 0], 'cur_cost': 793.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [4, 7, 1, 3, 0, 5, 6, 8, 2], 'cur_cost': 1001.0, 'intermediate_solutions': [{'tour': [8, 3, 7, 5, 1, 4, 2, 0, 6], 'cur_cost': 901.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 3, 7, 0, 1, 4, 2, 6, 5], 'cur_cost': 785.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 3, 7, 1, 4, 2, 5, 0, 6], 'cur_cost': 963.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 0, 8, 6, 3, 5, 2, 4], 'cur_cost': 983.0, 'intermediate_solutions': [{'tour': [1, 7, 8, 3, 6, 2, 4, 5, 0], 'cur_cost': 946.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 2, 6, 3, 8, 0, 5, 7], 'cur_cost': 990.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 1, 0, 8, 3, 6, 2, 5, 7], 'cur_cost': 992.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [4, 5, 2, 3, 7, 6, 0, 1, 8], 'cur_cost': 1016.0, 'intermediate_solutions': [{'tour': [5, 2, 0, 7, 1, 4, 6, 8, 3], 'cur_cost': 1045.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 5, 0, 4, 1, 7, 6, 8, 3], 'cur_cost': 1066.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 5, 2, 0, 4, 7, 6, 8, 3], 'cur_cost': 1109.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 5, 7, 8, 1, 0, 4, 6, 3], dtype=int64), 'cur_cost': 1032.0, 'intermediate_solutions': [{'tour': array([4, 3, 0, 6, 1, 8, 7, 5, 2]), 'cur_cost': 1022.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 4, 3, 0, 1, 8, 7, 5, 2]), 'cur_cost': 1132.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 6, 4, 3, 0, 8, 7, 5, 2]), 'cur_cost': 1118.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 6, 4, 3, 1, 8, 7, 5, 2]), 'cur_cost': 1165.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 1, 6, 4, 3, 8, 7, 5, 2]), 'cur_cost': 967.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 4, 5, 0, 7, 3, 1, 6, 8], dtype=int64), 'cur_cost': 993.0, 'intermediate_solutions': [{'tour': array([2, 6, 3, 7, 0, 4, 8, 5, 1]), 'cur_cost': 1016.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 2, 6, 3, 0, 4, 8, 5, 1]), 'cur_cost': 1113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 7, 2, 6, 3, 4, 8, 5, 1]), 'cur_cost': 1027.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 7, 2, 6, 0, 4, 8, 5, 1]), 'cur_cost': 1086.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 0, 7, 2, 6, 4, 8, 5, 1]), 'cur_cost': 1201.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [5, 6, 8, 7, 2, 3, 4, 1, 0], 'cur_cost': 974.0, 'intermediate_solutions': [{'tour': [8, 4, 7, 5, 3, 2, 0, 1, 6], 'cur_cost': 900.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 4, 7, 5, 3, 8, 6, 1, 0], 'cur_cost': 789.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 7, 3, 8, 0, 5, 1, 6], 'cur_cost': 1026.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([5, 4, 3, 8, 1, 0, 6, 2, 7], dtype=int64), 'cur_cost': 1047.0, 'intermediate_solutions': [{'tour': array([5, 4, 8, 1, 2, 7, 3, 6, 0]), 'cur_cost': 1066.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 5, 4, 8, 2, 7, 3, 6, 0]), 'cur_cost': 933.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 1, 5, 4, 8, 7, 3, 6, 0]), 'cur_cost': 1011.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 1, 5, 4, 2, 7, 3, 6, 0]), 'cur_cost': 1063.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 2, 1, 5, 4, 7, 3, 6, 0]), 'cur_cost': 1034.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-04 17:40:38,937 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:40:38,937 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:40:38,938 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=829.000, 多样性=0.874
2025-08-04 17:40:38,939 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-04 17:40:38,939 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-04 17:40:38,939 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:40:38,939 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.060414304173190714, 'best_improvement': -0.2173274596182085}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.005617977528089937}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.7222222222222222, 'new_diversity': 0.7222222222222222, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:40:38,940 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-04 17:40:38,940 - __main__ - INFO - simple1_9 开始进化第 4 代
2025-08-04 17:40:38,940 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-04 17:40:38,940 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:40:38,941 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=829.000, 多样性=0.874
2025-08-04 17:40:38,941 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:40:38,942 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.874
2025-08-04 17:40:38,942 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:40:38,943 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.722
2025-08-04 17:40:38,944 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-04 17:40:38,944 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:40:38,945 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-04 17:40:38,945 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-04 17:40:38,953 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.286, 适应度梯度: -19.843, 聚类评分: 0.000, 覆盖率: 0.004, 收敛趋势: 0.000, 多样性: 0.552
2025-08-04 17:40:38,954 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-04 17:40:38,954 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:40:38,955 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:40:38,958 - visualization.landscape_visualizer - INFO - 插值约束: 229 个点被约束到最小值 680.00
2025-08-04 17:40:38,962 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:40:39,048 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_4_20250804_174039.html
2025-08-04 17:40:39,094 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_4_20250804_174039.html
2025-08-04 17:40:39,094 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 4
2025-08-04 17:40:39,095 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-04 17:40:39,095 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1508秒
2025-08-04 17:40:39,095 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2857142857142857, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -19.842857142857138, 'local_optima_density': 0.2857142857142857, 'gradient_variance': 24000.298163265306, 'cluster_count': 0}, 'population_state': {'diversity': 0.5518053375196232, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0044, 'fitness_entropy': 0.9106994941661897, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -19.843)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.004)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754300438.9544265, 'performance_metrics': {}}}
2025-08-04 17:40:39,096 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:40:39,096 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:40:39,096 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:40:39,097 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:40:39,097 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-04 17:40:39,097 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-04 17:40:39,098 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-04 17:40:39,098 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:40:39,098 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:40:39,098 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-04 17:40:39,099 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:40:39,099 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:40:39,099 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-04 17:40:39,099 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:40:39,100 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:40:39,100 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:39,100 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:40:39,100 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,101 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,101 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,101 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,101 - ExplorationExpert - INFO - 探索路径生成完成，成本: 734.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:40:39,101 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 6, 5, 3, 8, 7, 4, 2], 'cur_cost': 734.0, 'intermediate_solutions': [{'tour': [0, 6, 7, 8, 4, 5, 3, 2, 1], 'cur_cost': 893.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 7, 8, 4, 3, 5, 1, 2], 'cur_cost': 973.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 7, 8, 3, 5, 2, 1, 4], 'cur_cost': 907.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:40:39,102 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 734.00)
2025-08-04 17:40:39,102 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:40:39,102 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:40:39,102 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:39,103 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:40:39,103 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,103 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,103 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,103 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,104 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1016.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:40:39,104 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [8, 3, 6, 5, 4, 1, 7, 2, 0], 'cur_cost': 1016.0, 'intermediate_solutions': [{'tour': [8, 2, 5, 7, 0, 1, 4, 3, 6], 'cur_cost': 939.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 3, 8, 0, 1, 4, 2, 6], 'cur_cost': 823.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 3, 7, 0, 1, 4, 2, 6, 5], 'cur_cost': 785.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:40:39,104 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 1016.00)
2025-08-04 17:40:39,105 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-04 17:40:39,105 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:40:39,105 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:40:39,105 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 1060.0
2025-08-04 17:40:39,113 - ExploitationExpert - INFO - res_population_num: 4
2025-08-04 17:40:39,113 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0, 775.0]
2025-08-04 17:40:39,113 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 2, 4, 7, 5, 8, 3, 6], dtype=int64)]
2025-08-04 17:40:39,114 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:40:39,114 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 6, 5, 3, 8, 7, 4, 2], 'cur_cost': 734.0}, {'tour': [8, 3, 6, 5, 4, 1, 7, 2, 0], 'cur_cost': 1016.0}, {'tour': array([6, 8, 5, 1, 4, 0, 7, 2, 3], dtype=int64), 'cur_cost': 1060.0}, {'tour': [4, 7, 1, 3, 0, 5, 6, 8, 2], 'cur_cost': 1001.0}, {'tour': [1, 7, 0, 8, 6, 3, 5, 2, 4], 'cur_cost': 983.0}, {'tour': [4, 5, 2, 3, 7, 6, 0, 1, 8], 'cur_cost': 1016.0}, {'tour': [2, 5, 7, 8, 1, 0, 4, 6, 3], 'cur_cost': 1032.0}, {'tour': [2, 4, 5, 0, 7, 3, 1, 6, 8], 'cur_cost': 993.0}, {'tour': [5, 6, 8, 7, 2, 3, 4, 1, 0], 'cur_cost': 974.0}, {'tour': [5, 4, 3, 8, 1, 0, 6, 2, 7], 'cur_cost': 1047.0}]
2025-08-04 17:40:39,115 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:40:39,115 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-08-04 17:40:39,116 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([6, 8, 5, 1, 4, 0, 7, 2, 3], dtype=int64), 'cur_cost': 1060.0, 'intermediate_solutions': [{'tour': array([8, 0, 5, 3, 4, 1, 2, 6, 7]), 'cur_cost': 1049.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 8, 0, 5, 4, 1, 2, 6, 7]), 'cur_cost': 1060.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 3, 8, 0, 5, 1, 2, 6, 7]), 'cur_cost': 1132.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 3, 8, 0, 4, 1, 2, 6, 7]), 'cur_cost': 948.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 4, 3, 8, 0, 1, 2, 6, 7]), 'cur_cost': 1000.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:40:39,116 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1060.00)
2025-08-04 17:40:39,117 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:40:39,117 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:40:39,117 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:39,118 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:40:39,118 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,119 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,119 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,119 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,120 - ExplorationExpert - INFO - 探索路径生成完成，成本: 997.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:40:39,120 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [8, 3, 0, 6, 5, 4, 2, 7, 1], 'cur_cost': 997.0, 'intermediate_solutions': [{'tour': [4, 7, 1, 0, 3, 5, 6, 8, 2], 'cur_cost': 813.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 7, 1, 3, 0, 5, 6, 2, 8], 'cur_cost': 1090.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 7, 1, 3, 0, 5, 6, 8, 2], 'cur_cost': 1001.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:40:39,120 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 997.00)
2025-08-04 17:40:39,120 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:40:39,120 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:40:39,121 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:39,121 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:40:39,121 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,121 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,122 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,122 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,122 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1089.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:40:39,122 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [1, 5, 2, 6, 4, 7, 3, 8, 0], 'cur_cost': 1089.0, 'intermediate_solutions': [{'tour': [2, 7, 0, 8, 6, 3, 5, 1, 4], 'cur_cost': 981.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 3, 6, 8, 0, 7, 1, 2, 4], 'cur_cost': 983.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 0, 8, 6, 3, 5, 2, 1, 4], 'cur_cost': 1035.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:40:39,123 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1089.00)
2025-08-04 17:40:39,123 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:40:39,123 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:40:39,123 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:39,123 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:40:39,124 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,124 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,124 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,124 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,124 - ExplorationExpert - INFO - 探索路径生成完成，成本: 777.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:40:39,125 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [1, 0, 3, 5, 8, 2, 4, 7, 6], 'cur_cost': 777.0, 'intermediate_solutions': [{'tour': [4, 8, 2, 3, 7, 6, 0, 1, 5], 'cur_cost': 933.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 5, 2, 3, 7, 6, 8, 1, 0], 'cur_cost': 1049.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 2, 3, 7, 6, 0, 4, 1, 8], 'cur_cost': 1027.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:40:39,126 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 777.00)
2025-08-04 17:40:39,126 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-04 17:40:39,126 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:40:39,126 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:40:39,126 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 967.0
2025-08-04 17:40:39,133 - ExploitationExpert - INFO - res_population_num: 4
2025-08-04 17:40:39,133 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0, 775.0]
2025-08-04 17:40:39,134 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 2, 4, 7, 5, 8, 3, 6], dtype=int64)]
2025-08-04 17:40:39,135 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:40:39,135 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 6, 5, 3, 8, 7, 4, 2], 'cur_cost': 734.0}, {'tour': [8, 3, 6, 5, 4, 1, 7, 2, 0], 'cur_cost': 1016.0}, {'tour': array([6, 8, 5, 1, 4, 0, 7, 2, 3], dtype=int64), 'cur_cost': 1060.0}, {'tour': [8, 3, 0, 6, 5, 4, 2, 7, 1], 'cur_cost': 997.0}, {'tour': [1, 5, 2, 6, 4, 7, 3, 8, 0], 'cur_cost': 1089.0}, {'tour': [1, 0, 3, 5, 8, 2, 4, 7, 6], 'cur_cost': 777.0}, {'tour': array([3, 8, 7, 5, 2, 0, 1, 6, 4], dtype=int64), 'cur_cost': 967.0}, {'tour': [2, 4, 5, 0, 7, 3, 1, 6, 8], 'cur_cost': 993.0}, {'tour': [5, 6, 8, 7, 2, 3, 4, 1, 0], 'cur_cost': 974.0}, {'tour': [5, 4, 3, 8, 1, 0, 6, 2, 7], 'cur_cost': 1047.0}]
2025-08-04 17:40:39,136 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:40:39,136 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-08-04 17:40:39,137 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([3, 8, 7, 5, 2, 0, 1, 6, 4], dtype=int64), 'cur_cost': 967.0, 'intermediate_solutions': [{'tour': array([7, 5, 2, 8, 1, 0, 4, 6, 3]), 'cur_cost': 957.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 7, 5, 2, 1, 0, 4, 6, 3]), 'cur_cost': 923.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 8, 7, 5, 2, 0, 4, 6, 3]), 'cur_cost': 1132.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 8, 7, 5, 1, 0, 4, 6, 3]), 'cur_cost': 949.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 1, 8, 7, 5, 0, 4, 6, 3]), 'cur_cost': 1082.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:40:39,137 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 967.00)
2025-08-04 17:40:39,138 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:40:39,138 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:40:39,138 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:39,138 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:40:39,138 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,139 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,139 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,139 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,139 - ExplorationExpert - INFO - 探索路径生成完成，成本: 978.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:40:39,139 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [1, 2, 6, 7, 0, 4, 8, 3, 5], 'cur_cost': 978.0, 'intermediate_solutions': [{'tour': [7, 4, 5, 0, 2, 3, 1, 6, 8], 'cur_cost': 1150.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 4, 5, 7, 0, 3, 1, 6, 8], 'cur_cost': 1000.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 5, 7, 3, 1, 0, 6, 8], 'cur_cost': 873.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:40:39,140 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 978.00)
2025-08-04 17:40:39,140 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:40:39,140 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:40:39,140 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:39,141 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:40:39,141 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,141 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,141 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,141 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,142 - ExplorationExpert - INFO - 探索路径生成完成，成本: 707.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:40:39,142 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [1, 2, 4, 8, 3, 7, 5, 6, 0], 'cur_cost': 707.0, 'intermediate_solutions': [{'tour': [5, 6, 8, 7, 2, 0, 4, 1, 3], 'cur_cost': 972.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 6, 8, 7, 2, 3, 4, 0, 1], 'cur_cost': 990.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 6, 8, 7, 2, 3, 4, 1, 0], 'cur_cost': 974.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:40:39,142 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 707.00)
2025-08-04 17:40:39,143 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-04 17:40:39,143 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:40:39,143 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:40:39,144 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 923.0
2025-08-04 17:40:39,151 - ExploitationExpert - INFO - res_population_num: 4
2025-08-04 17:40:39,151 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0, 775.0]
2025-08-04 17:40:39,152 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 2, 4, 7, 5, 8, 3, 6], dtype=int64)]
2025-08-04 17:40:39,153 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:40:39,153 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 6, 5, 3, 8, 7, 4, 2], 'cur_cost': 734.0}, {'tour': [8, 3, 6, 5, 4, 1, 7, 2, 0], 'cur_cost': 1016.0}, {'tour': array([6, 8, 5, 1, 4, 0, 7, 2, 3], dtype=int64), 'cur_cost': 1060.0}, {'tour': [8, 3, 0, 6, 5, 4, 2, 7, 1], 'cur_cost': 997.0}, {'tour': [1, 5, 2, 6, 4, 7, 3, 8, 0], 'cur_cost': 1089.0}, {'tour': [1, 0, 3, 5, 8, 2, 4, 7, 6], 'cur_cost': 777.0}, {'tour': array([3, 8, 7, 5, 2, 0, 1, 6, 4], dtype=int64), 'cur_cost': 967.0}, {'tour': [1, 2, 6, 7, 0, 4, 8, 3, 5], 'cur_cost': 978.0}, {'tour': [1, 2, 4, 8, 3, 7, 5, 6, 0], 'cur_cost': 707.0}, {'tour': array([5, 1, 2, 8, 7, 3, 4, 0, 6], dtype=int64), 'cur_cost': 923.0}]
2025-08-04 17:40:39,155 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:40:39,155 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-08-04 17:40:39,156 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([5, 1, 2, 8, 7, 3, 4, 0, 6], dtype=int64), 'cur_cost': 923.0, 'intermediate_solutions': [{'tour': array([3, 4, 5, 8, 1, 0, 6, 2, 7]), 'cur_cost': 1057.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 3, 4, 5, 1, 0, 6, 2, 7]), 'cur_cost': 1052.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 8, 3, 4, 5, 0, 6, 2, 7]), 'cur_cost': 1186.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 8, 3, 4, 1, 0, 6, 2, 7]), 'cur_cost': 935.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 1, 8, 3, 4, 0, 6, 2, 7]), 'cur_cost': 1113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:40:39,156 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 923.00)
2025-08-04 17:40:39,157 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:40:39,157 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:40:39,159 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 6, 5, 3, 8, 7, 4, 2], 'cur_cost': 734.0, 'intermediate_solutions': [{'tour': [0, 6, 7, 8, 4, 5, 3, 2, 1], 'cur_cost': 893.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 7, 8, 4, 3, 5, 1, 2], 'cur_cost': 973.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 7, 8, 3, 5, 2, 1, 4], 'cur_cost': 907.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [8, 3, 6, 5, 4, 1, 7, 2, 0], 'cur_cost': 1016.0, 'intermediate_solutions': [{'tour': [8, 2, 5, 7, 0, 1, 4, 3, 6], 'cur_cost': 939.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 3, 8, 0, 1, 4, 2, 6], 'cur_cost': 823.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 3, 7, 0, 1, 4, 2, 6, 5], 'cur_cost': 785.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 8, 5, 1, 4, 0, 7, 2, 3], dtype=int64), 'cur_cost': 1060.0, 'intermediate_solutions': [{'tour': array([8, 0, 5, 3, 4, 1, 2, 6, 7]), 'cur_cost': 1049.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 8, 0, 5, 4, 1, 2, 6, 7]), 'cur_cost': 1060.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 3, 8, 0, 5, 1, 2, 6, 7]), 'cur_cost': 1132.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 3, 8, 0, 4, 1, 2, 6, 7]), 'cur_cost': 948.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 4, 3, 8, 0, 1, 2, 6, 7]), 'cur_cost': 1000.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [8, 3, 0, 6, 5, 4, 2, 7, 1], 'cur_cost': 997.0, 'intermediate_solutions': [{'tour': [4, 7, 1, 0, 3, 5, 6, 8, 2], 'cur_cost': 813.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 7, 1, 3, 0, 5, 6, 2, 8], 'cur_cost': 1090.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 7, 1, 3, 0, 5, 6, 8, 2], 'cur_cost': 1001.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [1, 5, 2, 6, 4, 7, 3, 8, 0], 'cur_cost': 1089.0, 'intermediate_solutions': [{'tour': [2, 7, 0, 8, 6, 3, 5, 1, 4], 'cur_cost': 981.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 3, 6, 8, 0, 7, 1, 2, 4], 'cur_cost': 983.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 0, 8, 6, 3, 5, 2, 1, 4], 'cur_cost': 1035.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [1, 0, 3, 5, 8, 2, 4, 7, 6], 'cur_cost': 777.0, 'intermediate_solutions': [{'tour': [4, 8, 2, 3, 7, 6, 0, 1, 5], 'cur_cost': 933.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 5, 2, 3, 7, 6, 8, 1, 0], 'cur_cost': 1049.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 2, 3, 7, 6, 0, 4, 1, 8], 'cur_cost': 1027.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 8, 7, 5, 2, 0, 1, 6, 4], dtype=int64), 'cur_cost': 967.0, 'intermediate_solutions': [{'tour': array([7, 5, 2, 8, 1, 0, 4, 6, 3]), 'cur_cost': 957.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 7, 5, 2, 1, 0, 4, 6, 3]), 'cur_cost': 923.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 8, 7, 5, 2, 0, 4, 6, 3]), 'cur_cost': 1132.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 8, 7, 5, 1, 0, 4, 6, 3]), 'cur_cost': 949.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 1, 8, 7, 5, 0, 4, 6, 3]), 'cur_cost': 1082.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [1, 2, 6, 7, 0, 4, 8, 3, 5], 'cur_cost': 978.0, 'intermediate_solutions': [{'tour': [7, 4, 5, 0, 2, 3, 1, 6, 8], 'cur_cost': 1150.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 4, 5, 7, 0, 3, 1, 6, 8], 'cur_cost': 1000.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 5, 7, 3, 1, 0, 6, 8], 'cur_cost': 873.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [1, 2, 4, 8, 3, 7, 5, 6, 0], 'cur_cost': 707.0, 'intermediate_solutions': [{'tour': [5, 6, 8, 7, 2, 0, 4, 1, 3], 'cur_cost': 972.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 6, 8, 7, 2, 3, 4, 0, 1], 'cur_cost': 990.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 6, 8, 7, 2, 3, 4, 1, 0], 'cur_cost': 974.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([5, 1, 2, 8, 7, 3, 4, 0, 6], dtype=int64), 'cur_cost': 923.0, 'intermediate_solutions': [{'tour': array([3, 4, 5, 8, 1, 0, 6, 2, 7]), 'cur_cost': 1057.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 3, 4, 5, 1, 0, 6, 2, 7]), 'cur_cost': 1052.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 8, 3, 4, 5, 0, 6, 2, 7]), 'cur_cost': 1186.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 8, 3, 4, 1, 0, 6, 2, 7]), 'cur_cost': 935.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 1, 8, 3, 4, 0, 6, 2, 7]), 'cur_cost': 1113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-04 17:40:39,163 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:40:39,163 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:40:39,165 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=707.000, 多样性=0.899
2025-08-04 17:40:39,165 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-04 17:40:39,165 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-04 17:40:39,166 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:40:39,166 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.05612225544115423, 'best_improvement': 0.1471652593486128}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.02824858757062134}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.046920045620556246, 'recent_improvements': [-0.15425439541430322, 0.11657226223644206, -0.060414304173190714], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.7222222222222222, 'new_diversity': 0.7222222222222222, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:40:39,167 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-04 17:40:39,167 - __main__ - INFO - simple1_9 开始进化第 5 代
2025-08-04 17:40:39,167 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-04 17:40:39,168 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:40:39,169 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=707.000, 多样性=0.899
2025-08-04 17:40:39,169 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:40:39,170 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.899
2025-08-04 17:40:39,170 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:40:39,171 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.722
2025-08-04 17:40:39,174 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-04 17:40:39,174 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:40:39,174 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-04 17:40:39,174 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-04 17:40:39,184 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.214, 适应度梯度: -13.571, 聚类评分: 0.000, 覆盖率: 0.005, 收敛趋势: 0.000, 多样性: 0.570
2025-08-04 17:40:39,184 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-04 17:40:39,185 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:40:39,185 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:40:39,188 - visualization.landscape_visualizer - INFO - 插值约束: 39 个点被约束到最小值 680.00
2025-08-04 17:40:39,192 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:40:39,292 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_5_20250804_174039.html
2025-08-04 17:40:39,332 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_5_20250804_174039.html
2025-08-04 17:40:39,333 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 5
2025-08-04 17:40:39,333 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-04 17:40:39,333 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1595秒
2025-08-04 17:40:39,334 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.21428571428571427, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -13.571428571428571, 'local_optima_density': 0.21428571428571427, 'gradient_variance': 16912.936326530613, 'cluster_count': 0}, 'population_state': {'diversity': 0.5698587127158555, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0054, 'fitness_entropy': 0.929906376575379, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -13.571)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.005)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754300439.1841667, 'performance_metrics': {}}}
2025-08-04 17:40:39,334 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:40:39,335 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:40:39,335 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:40:39,335 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:40:39,335 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-04 17:40:39,336 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-04 17:40:39,336 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-04 17:40:39,336 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:40:39,336 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:40:39,337 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-04 17:40:39,337 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:40:39,337 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:40:39,337 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 0} (总数: 2, 保护比例: 0.20)
2025-08-04 17:40:39,337 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:40:39,338 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:40:39,338 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:39,338 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:40:39,339 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,339 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,339 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,339 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,340 - ExplorationExpert - INFO - 探索路径生成完成，成本: 958.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:40:39,340 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 5, 3, 1, 8, 2, 4, 7, 6], 'cur_cost': 958.0, 'intermediate_solutions': [{'tour': [2, 1, 6, 5, 3, 8, 7, 4, 0], 'cur_cost': 859.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 3, 5, 6, 8, 7, 4, 2], 'cur_cost': 842.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 1, 6, 3, 8, 7, 4, 2], 'cur_cost': 953.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:40:39,341 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 958.00)
2025-08-04 17:40:39,341 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-04 17:40:39,341 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:40:39,341 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:40:39,342 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 1194.0
2025-08-04 17:40:39,348 - ExploitationExpert - INFO - res_population_num: 4
2025-08-04 17:40:39,349 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0, 775.0]
2025-08-04 17:40:39,349 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 2, 4, 7, 5, 8, 3, 6], dtype=int64)]
2025-08-04 17:40:39,350 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:40:39,350 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 3, 1, 8, 2, 4, 7, 6], 'cur_cost': 958.0}, {'tour': array([4, 3, 2, 5, 1, 8, 0, 6, 7], dtype=int64), 'cur_cost': 1194.0}, {'tour': [6, 8, 5, 1, 4, 0, 7, 2, 3], 'cur_cost': 1060.0}, {'tour': [8, 3, 0, 6, 5, 4, 2, 7, 1], 'cur_cost': 997.0}, {'tour': [1, 5, 2, 6, 4, 7, 3, 8, 0], 'cur_cost': 1089.0}, {'tour': [1, 0, 3, 5, 8, 2, 4, 7, 6], 'cur_cost': 777.0}, {'tour': [3, 8, 7, 5, 2, 0, 1, 6, 4], 'cur_cost': 967.0}, {'tour': [1, 2, 6, 7, 0, 4, 8, 3, 5], 'cur_cost': 978.0}, {'tour': [1, 2, 4, 8, 3, 7, 5, 6, 0], 'cur_cost': 707.0}, {'tour': [5, 1, 2, 8, 7, 3, 4, 0, 6], 'cur_cost': 923.0}]
2025-08-04 17:40:39,351 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:40:39,351 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-08-04 17:40:39,352 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([4, 3, 2, 5, 1, 8, 0, 6, 7], dtype=int64), 'cur_cost': 1194.0, 'intermediate_solutions': [{'tour': array([6, 3, 8, 5, 4, 1, 7, 2, 0]), 'cur_cost': 1005.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 6, 3, 8, 4, 1, 7, 2, 0]), 'cur_cost': 960.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 5, 6, 3, 8, 1, 7, 2, 0]), 'cur_cost': 1033.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 5, 6, 3, 4, 1, 7, 2, 0]), 'cur_cost': 1016.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 4, 5, 6, 3, 1, 7, 2, 0]), 'cur_cost': 1091.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:40:39,352 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1194.00)
2025-08-04 17:40:39,352 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-04 17:40:39,352 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:40:39,353 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:40:39,353 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 1110.0
2025-08-04 17:40:39,360 - ExploitationExpert - INFO - res_population_num: 4
2025-08-04 17:40:39,361 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0, 775.0]
2025-08-04 17:40:39,361 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 2, 4, 7, 5, 8, 3, 6], dtype=int64)]
2025-08-04 17:40:39,362 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:40:39,362 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 3, 1, 8, 2, 4, 7, 6], 'cur_cost': 958.0}, {'tour': array([4, 3, 2, 5, 1, 8, 0, 6, 7], dtype=int64), 'cur_cost': 1194.0}, {'tour': array([5, 8, 7, 2, 3, 1, 4, 6, 0], dtype=int64), 'cur_cost': 1110.0}, {'tour': [8, 3, 0, 6, 5, 4, 2, 7, 1], 'cur_cost': 997.0}, {'tour': [1, 5, 2, 6, 4, 7, 3, 8, 0], 'cur_cost': 1089.0}, {'tour': [1, 0, 3, 5, 8, 2, 4, 7, 6], 'cur_cost': 777.0}, {'tour': [3, 8, 7, 5, 2, 0, 1, 6, 4], 'cur_cost': 967.0}, {'tour': [1, 2, 6, 7, 0, 4, 8, 3, 5], 'cur_cost': 978.0}, {'tour': [1, 2, 4, 8, 3, 7, 5, 6, 0], 'cur_cost': 707.0}, {'tour': [5, 1, 2, 8, 7, 3, 4, 0, 6], 'cur_cost': 923.0}]
2025-08-04 17:40:39,363 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:40:39,363 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-08-04 17:40:39,364 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([5, 8, 7, 2, 3, 1, 4, 6, 0], dtype=int64), 'cur_cost': 1110.0, 'intermediate_solutions': [{'tour': array([5, 8, 6, 1, 4, 0, 7, 2, 3]), 'cur_cost': 975.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 5, 8, 6, 4, 0, 7, 2, 3]), 'cur_cost': 1167.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 1, 5, 8, 6, 0, 7, 2, 3]), 'cur_cost': 1093.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 1, 5, 8, 4, 0, 7, 2, 3]), 'cur_cost': 1027.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 4, 1, 5, 8, 0, 7, 2, 3]), 'cur_cost': 1121.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:40:39,364 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1110.00)
2025-08-04 17:40:39,365 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:40:39,365 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:40:39,365 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:39,365 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:40:39,366 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,366 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,366 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,366 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,366 - ExplorationExpert - INFO - 探索路径生成完成，成本: 853.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:40:39,367 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [1, 4, 0, 3, 5, 6, 7, 8, 2], 'cur_cost': 853.0, 'intermediate_solutions': [{'tour': [8, 3, 0, 6, 1, 4, 2, 7, 5], 'cur_cost': 880.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 3, 0, 6, 1, 7, 2, 4, 5], 'cur_cost': 969.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 3, 0, 6, 4, 2, 7, 1, 5], 'cur_cost': 1023.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:40:39,367 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 853.00)
2025-08-04 17:40:39,367 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-04 17:40:39,367 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:40:39,368 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:40:39,368 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 902.0
2025-08-04 17:40:39,374 - ExploitationExpert - INFO - res_population_num: 4
2025-08-04 17:40:39,375 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0, 775.0]
2025-08-04 17:40:39,375 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 2, 4, 7, 5, 8, 3, 6], dtype=int64)]
2025-08-04 17:40:39,376 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:40:39,376 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 3, 1, 8, 2, 4, 7, 6], 'cur_cost': 958.0}, {'tour': array([4, 3, 2, 5, 1, 8, 0, 6, 7], dtype=int64), 'cur_cost': 1194.0}, {'tour': array([5, 8, 7, 2, 3, 1, 4, 6, 0], dtype=int64), 'cur_cost': 1110.0}, {'tour': [1, 4, 0, 3, 5, 6, 7, 8, 2], 'cur_cost': 853.0}, {'tour': array([7, 4, 0, 3, 5, 8, 2, 1, 6], dtype=int64), 'cur_cost': 902.0}, {'tour': [1, 0, 3, 5, 8, 2, 4, 7, 6], 'cur_cost': 777.0}, {'tour': [3, 8, 7, 5, 2, 0, 1, 6, 4], 'cur_cost': 967.0}, {'tour': [1, 2, 6, 7, 0, 4, 8, 3, 5], 'cur_cost': 978.0}, {'tour': [1, 2, 4, 8, 3, 7, 5, 6, 0], 'cur_cost': 707.0}, {'tour': [5, 1, 2, 8, 7, 3, 4, 0, 6], 'cur_cost': 923.0}]
2025-08-04 17:40:39,377 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:40:39,377 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-08-04 17:40:39,378 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([7, 4, 0, 3, 5, 8, 2, 1, 6], dtype=int64), 'cur_cost': 902.0, 'intermediate_solutions': [{'tour': array([2, 5, 1, 6, 4, 7, 3, 8, 0]), 'cur_cost': 1103.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 2, 5, 1, 4, 7, 3, 8, 0]), 'cur_cost': 1101.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 6, 2, 5, 1, 7, 3, 8, 0]), 'cur_cost': 1162.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 6, 2, 5, 4, 7, 3, 8, 0]), 'cur_cost': 1035.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 4, 6, 2, 5, 7, 3, 8, 0]), 'cur_cost': 1007.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:40:39,379 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 902.00)
2025-08-04 17:40:39,379 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:40:39,379 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:40:39,379 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:39,380 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:40:39,380 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,380 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,380 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,380 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,381 - ExplorationExpert - INFO - 探索路径生成完成，成本: 822.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:40:39,381 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 4, 7, 3, 8, 2, 5, 6, 1], 'cur_cost': 822.0, 'intermediate_solutions': [{'tour': [5, 0, 3, 1, 8, 2, 4, 7, 6], 'cur_cost': 993.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 3, 0, 1, 8, 2, 4, 7, 6], 'cur_cost': 805.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 3, 5, 8, 2, 4, 7, 6], 'cur_cost': 777.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:40:39,381 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 822.00)
2025-08-04 17:40:39,381 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:40:39,382 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:40:39,382 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:39,382 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:40:39,382 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,383 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,383 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,383 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,383 - ExplorationExpert - INFO - 探索路径生成完成，成本: 757.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:40:39,383 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [1, 0, 7, 6, 3, 5, 8, 4, 2], 'cur_cost': 757.0, 'intermediate_solutions': [{'tour': [8, 3, 7, 5, 2, 0, 1, 6, 4], 'cur_cost': 915.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 7, 2, 5, 0, 1, 6, 4], 'cur_cost': 1027.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 8, 7, 6, 5, 2, 0, 1, 4], 'cur_cost': 886.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:40:39,384 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 757.00)
2025-08-04 17:40:39,384 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:40:39,384 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:40:39,384 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:39,385 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:40:39,385 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,385 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,385 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,386 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,386 - ExplorationExpert - INFO - 探索路径生成完成，成本: 831.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:40:39,386 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [5, 3, 1, 0, 6, 7, 8, 2, 4], 'cur_cost': 831.0, 'intermediate_solutions': [{'tour': [1, 2, 8, 7, 0, 4, 6, 3, 5], 'cur_cost': 970.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 0, 7, 6, 2, 4, 8, 3, 5], 'cur_cost': 853.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 1, 2, 6, 7, 0, 4, 8, 5], 'cur_cost': 998.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:40:39,387 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 831.00)
2025-08-04 17:40:39,387 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:40:39,387 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:40:39,387 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:39,388 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:40:39,388 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,388 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,388 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,388 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,389 - ExplorationExpert - INFO - 探索路径生成完成，成本: 905.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:40:39,389 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [3, 8, 4, 7, 0, 1, 6, 5, 2], 'cur_cost': 905.0, 'intermediate_solutions': [{'tour': [2, 1, 4, 8, 3, 7, 5, 6, 0], 'cur_cost': 843.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 3, 8, 4, 2, 7, 5, 6, 0], 'cur_cost': 804.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 2, 4, 8, 3, 5, 6, 0], 'cur_cost': 755.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:40:39,389 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 905.00)
2025-08-04 17:40:39,389 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:40:39,390 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:40:39,390 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:40:39,391 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:40:39,391 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,391 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,391 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,391 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:40:39,392 - ExplorationExpert - INFO - 探索路径生成完成，成本: 980.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:40:39,392 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [5, 3, 6, 7, 8, 0, 2, 1, 4], 'cur_cost': 980.0, 'intermediate_solutions': [{'tour': [5, 4, 2, 8, 7, 3, 1, 0, 6], 'cur_cost': 818.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 7, 8, 2, 1, 5, 4, 0, 6], 'cur_cost': 974.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 1, 2, 8, 7, 3, 4, 0, 6], 'cur_cost': 923.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:40:39,392 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 980.00)
2025-08-04 17:40:39,392 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:40:39,393 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:40:39,394 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 3, 1, 8, 2, 4, 7, 6], 'cur_cost': 958.0, 'intermediate_solutions': [{'tour': [2, 1, 6, 5, 3, 8, 7, 4, 0], 'cur_cost': 859.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 3, 5, 6, 8, 7, 4, 2], 'cur_cost': 842.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 1, 6, 3, 8, 7, 4, 2], 'cur_cost': 953.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 3, 2, 5, 1, 8, 0, 6, 7], dtype=int64), 'cur_cost': 1194.0, 'intermediate_solutions': [{'tour': array([6, 3, 8, 5, 4, 1, 7, 2, 0]), 'cur_cost': 1005.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 6, 3, 8, 4, 1, 7, 2, 0]), 'cur_cost': 960.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 5, 6, 3, 8, 1, 7, 2, 0]), 'cur_cost': 1033.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 5, 6, 3, 4, 1, 7, 2, 0]), 'cur_cost': 1016.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 4, 5, 6, 3, 1, 7, 2, 0]), 'cur_cost': 1091.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([5, 8, 7, 2, 3, 1, 4, 6, 0], dtype=int64), 'cur_cost': 1110.0, 'intermediate_solutions': [{'tour': array([5, 8, 6, 1, 4, 0, 7, 2, 3]), 'cur_cost': 975.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 5, 8, 6, 4, 0, 7, 2, 3]), 'cur_cost': 1167.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 1, 5, 8, 6, 0, 7, 2, 3]), 'cur_cost': 1093.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 1, 5, 8, 4, 0, 7, 2, 3]), 'cur_cost': 1027.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 4, 1, 5, 8, 0, 7, 2, 3]), 'cur_cost': 1121.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 0, 3, 5, 6, 7, 8, 2], 'cur_cost': 853.0, 'intermediate_solutions': [{'tour': [8, 3, 0, 6, 1, 4, 2, 7, 5], 'cur_cost': 880.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 3, 0, 6, 1, 7, 2, 4, 5], 'cur_cost': 969.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 3, 0, 6, 4, 2, 7, 1, 5], 'cur_cost': 1023.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 4, 0, 3, 5, 8, 2, 1, 6], dtype=int64), 'cur_cost': 902.0, 'intermediate_solutions': [{'tour': array([2, 5, 1, 6, 4, 7, 3, 8, 0]), 'cur_cost': 1103.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 2, 5, 1, 4, 7, 3, 8, 0]), 'cur_cost': 1101.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 6, 2, 5, 1, 7, 3, 8, 0]), 'cur_cost': 1162.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 6, 2, 5, 4, 7, 3, 8, 0]), 'cur_cost': 1035.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 4, 6, 2, 5, 7, 3, 8, 0]), 'cur_cost': 1007.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 7, 3, 8, 2, 5, 6, 1], 'cur_cost': 822.0, 'intermediate_solutions': [{'tour': [5, 0, 3, 1, 8, 2, 4, 7, 6], 'cur_cost': 993.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 3, 0, 1, 8, 2, 4, 7, 6], 'cur_cost': 805.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 3, 5, 8, 2, 4, 7, 6], 'cur_cost': 777.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [1, 0, 7, 6, 3, 5, 8, 4, 2], 'cur_cost': 757.0, 'intermediate_solutions': [{'tour': [8, 3, 7, 5, 2, 0, 1, 6, 4], 'cur_cost': 915.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 7, 2, 5, 0, 1, 6, 4], 'cur_cost': 1027.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 8, 7, 6, 5, 2, 0, 1, 4], 'cur_cost': 886.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [5, 3, 1, 0, 6, 7, 8, 2, 4], 'cur_cost': 831.0, 'intermediate_solutions': [{'tour': [1, 2, 8, 7, 0, 4, 6, 3, 5], 'cur_cost': 970.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 0, 7, 6, 2, 4, 8, 3, 5], 'cur_cost': 853.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 1, 2, 6, 7, 0, 4, 8, 5], 'cur_cost': 998.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [3, 8, 4, 7, 0, 1, 6, 5, 2], 'cur_cost': 905.0, 'intermediate_solutions': [{'tour': [2, 1, 4, 8, 3, 7, 5, 6, 0], 'cur_cost': 843.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 3, 8, 4, 2, 7, 5, 6, 0], 'cur_cost': 804.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 2, 4, 8, 3, 5, 6, 0], 'cur_cost': 755.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [5, 3, 6, 7, 8, 0, 2, 1, 4], 'cur_cost': 980.0, 'intermediate_solutions': [{'tour': [5, 4, 2, 8, 7, 3, 1, 0, 6], 'cur_cost': 818.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 7, 8, 2, 1, 5, 4, 0, 6], 'cur_cost': 974.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 1, 2, 8, 7, 3, 4, 0, 6], 'cur_cost': 923.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:40:39,396 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:40:39,397 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:40:39,398 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=757.000, 多样性=0.901
2025-08-04 17:40:39,398 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-04 17:40:39,398 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-04 17:40:39,398 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:40:39,399 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.05802016861911782, 'best_improvement': -0.07072135785007072}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.002747252747253081}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.030225003397643918, 'recent_improvements': [0.11657226223644206, -0.060414304173190714, 0.05612225544115423], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.7222222222222222, 'new_diversity': 0.7222222222222222, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:40:39,399 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-04 17:40:39,401 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_solution.json
2025-08-04 17:40:39,401 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_20250804_174039.solution
2025-08-04 17:40:39,402 - __main__ - INFO - 实例执行完成 - 运行时间: 6.55s, 最佳成本: 680.0
2025-08-04 17:40:39,402 - __main__ - INFO - 实例 simple1_9 处理完成
