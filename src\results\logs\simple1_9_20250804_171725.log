2025-08-04 17:17:25,804 - __main__ - INFO - simple1_9 开始进化第 1 代
2025-08-04 17:17:25,804 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-04 17:17:25,807 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:17:25,810 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=830.000, 多样性=0.854
2025-08-04 17:17:25,812 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:17:25,813 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.854
2025-08-04 17:17:25,814 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:17:25,829 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-04 17:17:25,829 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:17:25,829 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-04 17:17:25,830 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-04 17:17:26,067 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -20.780, 聚类评分: 0.000, 覆盖率: 0.001, 收敛趋势: 0.000, 多样性: 0.769
2025-08-04 17:17:26,067 - LandscapeExpert - WARNING - 可视化器未初始化，跳过可视化更新
2025-08-04 17:17:26,067 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2387秒
2025-08-04 17:17:26,068 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-04 17:17:26,069 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': np.float64(-20.779999999999994), 'local_optima_density': 0.1, 'gradient_variance': np.float64(19694.6116), 'cluster_count': 0}, 'population_state': {'diversity': np.float64(0.768888888888889), 'convergence': 0.0, 'clustering': 0.0, 'coverage': np.float64(0.001), 'fitness_entropy': np.float64(0.969570350190125), 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -20.780)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.001)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.769)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754299046.0675428, 'performance_metrics': {}}}
2025-08-04 17:17:26,070 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:17:26,070 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:17:26,070 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:17:26,070 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:17:26,071 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:17:26,071 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-04 17:17:26,071 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:17:26,072 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:17:26,072 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:17:26,072 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:17:26,072 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:17:26,073 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:17:26,073 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-04 17:17:26,074 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:17:26,074 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:17:26,074 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:26,075 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:17:26,075 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:26,266 - ExplorationExpert - INFO - 探索路径生成完成，成本: 790.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:17:26,267 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [1, 4, 7, 5, 6, 3, 8, 2, 0], 'cur_cost': 790.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:17:26,267 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 790.00)
2025-08-04 17:17:26,268 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:17:26,268 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:17:26,268 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:26,269 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:17:26,269 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:26,269 - ExplorationExpert - INFO - 探索路径生成完成，成本: 842.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:17:26,270 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [2, 0, 4, 8, 7, 3, 5, 6, 1], 'cur_cost': 842.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:17:26,270 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 842.00)
2025-08-04 17:17:26,270 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:17:26,270 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:17:26,270 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:26,271 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:17:26,271 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:26,271 - ExplorationExpert - INFO - 探索路径生成完成，成本: 887.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:17:26,272 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [7, 3, 6, 0, 4, 2, 8, 5, 1], 'cur_cost': 887.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:17:26,272 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 887.00)
2025-08-04 17:17:26,272 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:17:26,273 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:17:26,273 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:26,273 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:17:26,274 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:26,274 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1093.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:17:26,274 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [1, 4, 6, 5, 2, 7, 0, 8, 3], 'cur_cost': 1093.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:17:26,274 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1093.00)
2025-08-04 17:17:26,275 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:17:26,275 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:17:26,275 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:26,275 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:17:26,276 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:26,276 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1107.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:17:26,276 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 6, 2, 5, 7, 8, 4, 1, 3], 'cur_cost': 1107.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:17:26,276 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1107.00)
2025-08-04 17:17:26,277 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-04 17:17:26,277 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:17:26,294 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:17:26,297 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 955.0
2025-08-04 17:17:28,127 - ExploitationExpert - INFO - res_population_num: 1
2025-08-04 17:17:28,127 - ExploitationExpert - INFO - res_population_costs: [680.0]
2025-08-04 17:17:28,128 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1])]
2025-08-04 17:17:28,128 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:17:28,128 - ExploitationExpert - INFO - populations: [{'tour': [1, 4, 7, 5, 6, 3, 8, 2, 0], 'cur_cost': 790.0}, {'tour': [2, 0, 4, 8, 7, 3, 5, 6, 1], 'cur_cost': 842.0}, {'tour': [7, 3, 6, 0, 4, 2, 8, 5, 1], 'cur_cost': 887.0}, {'tour': [1, 4, 6, 5, 2, 7, 0, 8, 3], 'cur_cost': 1093.0}, {'tour': [0, 6, 2, 5, 7, 8, 4, 1, 3], 'cur_cost': 1107.0}, {'tour': array([0, 6, 5, 7, 3, 4, 8, 1, 2]), 'cur_cost': np.float64(955.0)}, {'tour': array([5, 3, 0, 8, 7, 4, 6, 2, 1]), 'cur_cost': 1145.0}, {'tour': array([8, 3, 2, 6, 5, 1, 0, 7, 4]), 'cur_cost': 968.0}, {'tour': array([8, 6, 0, 2, 3, 4, 1, 5, 7]), 'cur_cost': 1064.0}, {'tour': array([0, 5, 1, 2, 4, 6, 8, 7, 3]), 'cur_cost': 1046.0}]
2025-08-04 17:17:28,130 - ExploitationExpert - INFO - 局部搜索耗时: 1.84秒，最大迭代次数: 10
2025-08-04 17:17:28,130 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-04 17:17:28,130 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([0, 6, 5, 7, 3, 4, 8, 1, 2]), 'cur_cost': np.float64(955.0), 'intermediate_solutions': [{'tour': array([8, 1, 3, 7, 5, 4, 0, 6, 2]), 'cur_cost': np.float64(1085.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 8, 1, 3, 5, 4, 0, 6, 2]), 'cur_cost': np.float64(1132.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 7, 8, 1, 3, 4, 0, 6, 2]), 'cur_cost': np.float64(1163.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 7, 8, 1, 5, 4, 0, 6, 2]), 'cur_cost': np.float64(1150.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 5, 7, 8, 1, 4, 0, 6, 2]), 'cur_cost': np.float64(1022.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:17:28,131 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 955.00)
2025-08-04 17:17:28,131 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-04 17:17:28,131 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:17:28,132 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:17:28,132 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1081.0
2025-08-04 17:17:31,605 - ExploitationExpert - INFO - res_population_num: 2
2025-08-04 17:17:31,606 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0]
2025-08-04 17:17:31,606 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1]), array([0, 1, 4, 2, 8, 7, 3, 5, 6])]
2025-08-04 17:17:31,607 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:17:31,607 - ExploitationExpert - INFO - populations: [{'tour': [1, 4, 7, 5, 6, 3, 8, 2, 0], 'cur_cost': 790.0}, {'tour': [2, 0, 4, 8, 7, 3, 5, 6, 1], 'cur_cost': 842.0}, {'tour': [7, 3, 6, 0, 4, 2, 8, 5, 1], 'cur_cost': 887.0}, {'tour': [1, 4, 6, 5, 2, 7, 0, 8, 3], 'cur_cost': 1093.0}, {'tour': [0, 6, 2, 5, 7, 8, 4, 1, 3], 'cur_cost': 1107.0}, {'tour': array([0, 6, 5, 7, 3, 4, 8, 1, 2]), 'cur_cost': np.float64(955.0)}, {'tour': array([6, 1, 3, 0, 5, 7, 2, 8, 4]), 'cur_cost': np.float64(1081.0)}, {'tour': array([8, 3, 2, 6, 5, 1, 0, 7, 4]), 'cur_cost': 968.0}, {'tour': array([8, 6, 0, 2, 3, 4, 1, 5, 7]), 'cur_cost': 1064.0}, {'tour': array([0, 5, 1, 2, 4, 6, 8, 7, 3]), 'cur_cost': 1046.0}]
2025-08-04 17:17:31,608 - ExploitationExpert - INFO - 局部搜索耗时: 3.48秒，最大迭代次数: 10
2025-08-04 17:17:31,608 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-04 17:17:31,609 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([6, 1, 3, 0, 5, 7, 2, 8, 4]), 'cur_cost': np.float64(1081.0), 'intermediate_solutions': [{'tour': array([0, 3, 5, 8, 7, 4, 6, 2, 1]), 'cur_cost': np.float64(984.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 0, 3, 5, 7, 4, 6, 2, 1]), 'cur_cost': np.float64(1140.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 8, 0, 3, 5, 4, 6, 2, 1]), 'cur_cost': np.float64(1152.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 8, 0, 3, 7, 4, 6, 2, 1]), 'cur_cost': np.float64(1156.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 7, 8, 0, 3, 4, 6, 2, 1]), 'cur_cost': np.float64(1181.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:17:31,610 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1081.00)
2025-08-04 17:17:31,610 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:17:31,610 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:17:31,610 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:31,611 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:17:31,611 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,612 - ExplorationExpert - INFO - 探索路径生成完成，成本: 951.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:17:31,612 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [1, 8, 3, 5, 0, 7, 6, 4, 2], 'cur_cost': 951.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:17:31,612 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 951.00)
2025-08-04 17:17:31,613 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:17:31,613 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:17:31,613 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:31,613 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:17:31,614 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,614 - ExplorationExpert - INFO - 探索路径生成完成，成本: 856.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:17:31,614 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [2, 0, 4, 8, 3, 5, 7, 6, 1], 'cur_cost': 856.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:17:31,615 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 856.00)
2025-08-04 17:17:31,615 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:17:31,615 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:17:31,615 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:31,616 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:17:31,616 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,616 - ExplorationExpert - INFO - 探索路径生成完成，成本: 961.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:17:31,616 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 8, 4, 5, 3, 7, 6, 1, 2], 'cur_cost': 961.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:17:31,617 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 961.00)
2025-08-04 17:17:31,617 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:17:31,617 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:17:31,618 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 7, 5, 6, 3, 8, 2, 0], 'cur_cost': 790.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [2, 0, 4, 8, 7, 3, 5, 6, 1], 'cur_cost': 842.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [7, 3, 6, 0, 4, 2, 8, 5, 1], 'cur_cost': 887.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 6, 5, 2, 7, 0, 8, 3], 'cur_cost': 1093.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 2, 5, 7, 8, 4, 1, 3], 'cur_cost': 1107.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 6, 5, 7, 3, 4, 8, 1, 2]), 'cur_cost': np.float64(955.0), 'intermediate_solutions': [{'tour': array([8, 1, 3, 7, 5, 4, 0, 6, 2]), 'cur_cost': np.float64(1085.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 8, 1, 3, 5, 4, 0, 6, 2]), 'cur_cost': np.float64(1132.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 7, 8, 1, 3, 4, 0, 6, 2]), 'cur_cost': np.float64(1163.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 7, 8, 1, 5, 4, 0, 6, 2]), 'cur_cost': np.float64(1150.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 5, 7, 8, 1, 4, 0, 6, 2]), 'cur_cost': np.float64(1022.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 1, 3, 0, 5, 7, 2, 8, 4]), 'cur_cost': np.float64(1081.0), 'intermediate_solutions': [{'tour': array([0, 3, 5, 8, 7, 4, 6, 2, 1]), 'cur_cost': np.float64(984.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 0, 3, 5, 7, 4, 6, 2, 1]), 'cur_cost': np.float64(1140.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 8, 0, 3, 5, 4, 6, 2, 1]), 'cur_cost': np.float64(1152.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 8, 0, 3, 7, 4, 6, 2, 1]), 'cur_cost': np.float64(1156.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 7, 8, 0, 3, 4, 6, 2, 1]), 'cur_cost': np.float64(1181.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [1, 8, 3, 5, 0, 7, 6, 4, 2], 'cur_cost': 951.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [2, 0, 4, 8, 3, 5, 7, 6, 1], 'cur_cost': 856.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 4, 5, 3, 7, 6, 1, 2], 'cur_cost': 961.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-04 17:17:31,619 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:17:31,620 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:17:31,622 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=790.000, 多样性=0.864
2025-08-04 17:17:31,623 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-04 17:17:31,623 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-04 17:17:31,624 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:17:31,625 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.04543860328886846, 'best_improvement': 0.04819277108433735}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': np.float64(0.01156069364161847)}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.8888888888888888, 'new_diversity': 0.8888888888888888, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-04 17:17:31,625 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-04 17:17:31,626 - __main__ - INFO - simple1_9 开始进化第 2 代
2025-08-04 17:17:31,626 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-04 17:17:31,627 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:17:31,627 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=790.000, 多样性=0.864
2025-08-04 17:17:31,627 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:17:31,628 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.864
2025-08-04 17:17:31,628 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:17:31,629 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.889
2025-08-04 17:17:31,631 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-04 17:17:31,631 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:17:31,631 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-04 17:17:31,631 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-04 17:17:31,639 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.167, 适应度梯度: 8.133, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 0.641
2025-08-04 17:17:31,639 - LandscapeExpert - WARNING - 可视化器未初始化，跳过可视化更新
2025-08-04 17:17:31,639 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.0083秒
2025-08-04 17:17:31,639 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.16666666666666666, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': np.float64(8.133333333333331), 'local_optima_density': 0.16666666666666666, 'gradient_variance': np.float64(22321.022222222222), 'cluster_count': 0}, 'population_state': {'diversity': np.float64(0.6414141414141414), 'convergence': 0.0, 'clustering': 0.0, 'coverage': np.float64(0.0022), 'fitness_entropy': np.float64(0.9513282751069652), 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 8.133)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754299051.639338, 'performance_metrics': {}}}
2025-08-04 17:17:31,640 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:17:31,641 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:17:31,641 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:17:31,641 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:17:31,641 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-04 17:17:31,642 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-04 17:17:31,642 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-04 17:17:31,642 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:17:31,642 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:17:31,643 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-04 17:17:31,643 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:17:31,643 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:17:31,644 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-04 17:17:31,644 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:17:31,644 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:17:31,644 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:31,645 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:17:31,645 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,645 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,646 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,646 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,646 - ExplorationExpert - INFO - 探索路径生成完成，成本: 812.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:17:31,646 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [3, 8, 5, 6, 7, 4, 2, 0, 1], 'cur_cost': 812.0, 'intermediate_solutions': [{'tour': [1, 3, 7, 5, 6, 4, 8, 2, 0], 'cur_cost': 880.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 3, 6, 5, 7, 4, 8, 2, 0], 'cur_cost': 865.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 7, 5, 6, 3, 8, 2, 0], 'cur_cost': 790.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:17:31,647 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 812.00)
2025-08-04 17:17:31,647 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:17:31,647 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:17:31,647 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:31,648 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:17:31,648 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,649 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,649 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,649 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,649 - ExplorationExpert - INFO - 探索路径生成完成，成本: 897.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:17:31,650 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 3, 6, 4, 2, 8, 7, 5], 'cur_cost': 897.0, 'intermediate_solutions': [{'tour': [8, 0, 4, 2, 7, 3, 5, 6, 1], 'cur_cost': 894.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 3, 7, 8, 4, 0, 5, 6, 1], 'cur_cost': 930.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 0, 4, 8, 7, 3, 6, 5, 1], 'cur_cost': 927.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:17:31,650 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 897.00)
2025-08-04 17:17:31,650 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:17:31,650 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:17:31,651 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:31,651 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:17:31,651 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,651 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,652 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,652 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,652 - ExplorationExpert - INFO - 探索路径生成完成，成本: 875.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:17:31,652 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [7, 4, 2, 8, 5, 3, 0, 6, 1], 'cur_cost': 875.0, 'intermediate_solutions': [{'tour': [7, 3, 6, 0, 4, 8, 2, 5, 1], 'cur_cost': 1001.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 6, 0, 4, 2, 8, 1, 5], 'cur_cost': 910.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 7, 3, 6, 0, 4, 8, 5, 1], 'cur_cost': 970.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:17:31,653 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 875.00)
2025-08-04 17:17:31,653 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-04 17:17:31,653 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:17:31,654 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:17:31,654 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1127.0
2025-08-04 17:17:31,694 - ExploitationExpert - INFO - res_population_num: 2
2025-08-04 17:17:31,695 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0]
2025-08-04 17:17:31,695 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1]), array([0, 1, 4, 2, 8, 7, 3, 5, 6])]
2025-08-04 17:17:31,695 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:17:31,696 - ExploitationExpert - INFO - populations: [{'tour': [3, 8, 5, 6, 7, 4, 2, 0, 1], 'cur_cost': 812.0}, {'tour': [0, 1, 3, 6, 4, 2, 8, 7, 5], 'cur_cost': 897.0}, {'tour': [7, 4, 2, 8, 5, 3, 0, 6, 1], 'cur_cost': 875.0}, {'tour': array([7, 4, 0, 6, 8, 5, 2, 3, 1]), 'cur_cost': np.float64(1127.0)}, {'tour': [0, 6, 2, 5, 7, 8, 4, 1, 3], 'cur_cost': 1107.0}, {'tour': [0, 6, 5, 7, 3, 4, 8, 1, 2], 'cur_cost': 955.0}, {'tour': [6, 1, 3, 0, 5, 7, 2, 8, 4], 'cur_cost': 1081.0}, {'tour': [1, 8, 3, 5, 0, 7, 6, 4, 2], 'cur_cost': 951.0}, {'tour': [2, 0, 4, 8, 3, 5, 7, 6, 1], 'cur_cost': 856.0}, {'tour': [0, 8, 4, 5, 3, 7, 6, 1, 2], 'cur_cost': 961.0}]
2025-08-04 17:17:31,696 - ExploitationExpert - INFO - 局部搜索耗时: 0.04秒，最大迭代次数: 10
2025-08-04 17:17:31,696 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-04 17:17:31,697 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([7, 4, 0, 6, 8, 5, 2, 3, 1]), 'cur_cost': np.float64(1127.0), 'intermediate_solutions': [{'tour': array([6, 4, 1, 5, 2, 7, 0, 8, 3]), 'cur_cost': np.float64(1124.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 6, 4, 1, 2, 7, 0, 8, 3]), 'cur_cost': np.float64(955.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 5, 6, 4, 1, 7, 0, 8, 3]), 'cur_cost': np.float64(1072.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 5, 6, 4, 2, 7, 0, 8, 3]), 'cur_cost': np.float64(1034.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 2, 5, 6, 4, 7, 0, 8, 3]), 'cur_cost': np.float64(1088.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:17:31,698 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1127.00)
2025-08-04 17:17:31,698 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-04 17:17:31,698 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:17:31,698 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:17:31,699 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 1086.0
2025-08-04 17:17:31,736 - ExploitationExpert - INFO - res_population_num: 2
2025-08-04 17:17:31,736 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0]
2025-08-04 17:17:31,736 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1]), array([0, 1, 4, 2, 8, 7, 3, 5, 6])]
2025-08-04 17:17:31,737 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:17:31,737 - ExploitationExpert - INFO - populations: [{'tour': [3, 8, 5, 6, 7, 4, 2, 0, 1], 'cur_cost': 812.0}, {'tour': [0, 1, 3, 6, 4, 2, 8, 7, 5], 'cur_cost': 897.0}, {'tour': [7, 4, 2, 8, 5, 3, 0, 6, 1], 'cur_cost': 875.0}, {'tour': array([7, 4, 0, 6, 8, 5, 2, 3, 1]), 'cur_cost': np.float64(1127.0)}, {'tour': array([2, 3, 0, 4, 1, 5, 8, 7, 6]), 'cur_cost': np.float64(1086.0)}, {'tour': [0, 6, 5, 7, 3, 4, 8, 1, 2], 'cur_cost': 955.0}, {'tour': [6, 1, 3, 0, 5, 7, 2, 8, 4], 'cur_cost': 1081.0}, {'tour': [1, 8, 3, 5, 0, 7, 6, 4, 2], 'cur_cost': 951.0}, {'tour': [2, 0, 4, 8, 3, 5, 7, 6, 1], 'cur_cost': 856.0}, {'tour': [0, 8, 4, 5, 3, 7, 6, 1, 2], 'cur_cost': 961.0}]
2025-08-04 17:17:31,738 - ExploitationExpert - INFO - 局部搜索耗时: 0.04秒，最大迭代次数: 10
2025-08-04 17:17:31,738 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-04 17:17:31,739 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([2, 3, 0, 4, 1, 5, 8, 7, 6]), 'cur_cost': np.float64(1086.0), 'intermediate_solutions': [{'tour': array([2, 6, 0, 5, 7, 8, 4, 1, 3]), 'cur_cost': np.float64(1087.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 2, 6, 0, 7, 8, 4, 1, 3]), 'cur_cost': np.float64(1058.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 5, 2, 6, 0, 8, 4, 1, 3]), 'cur_cost': np.float64(1094.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 5, 2, 6, 7, 8, 4, 1, 3]), 'cur_cost': np.float64(1148.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 7, 5, 2, 6, 8, 4, 1, 3]), 'cur_cost': np.float64(1148.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:17:31,740 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1086.00)
2025-08-04 17:17:31,740 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:17:31,740 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:17:31,740 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:31,740 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:17:31,741 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,741 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,741 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,741 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,741 - ExplorationExpert - INFO - 探索路径生成完成，成本: 921.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:17:31,742 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 8, 2, 6, 7, 3, 5, 4, 1], 'cur_cost': 921.0, 'intermediate_solutions': [{'tour': [0, 6, 3, 7, 5, 4, 8, 1, 2], 'cur_cost': 1006.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 4, 3, 7, 5, 8, 1, 2], 'cur_cost': 1038.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 7, 3, 4, 8, 6, 1, 2], 'cur_cost': 999.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:17:31,742 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 921.00)
2025-08-04 17:17:31,742 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:17:31,742 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:17:31,743 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:31,743 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:17:31,743 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,743 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,744 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,744 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,744 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1121.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:17:31,744 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [3, 0, 5, 2, 7, 1, 6, 4, 8], 'cur_cost': 1121.0, 'intermediate_solutions': [{'tour': [5, 1, 3, 0, 6, 7, 2, 8, 4], 'cur_cost': 1100.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 5, 0, 3, 1, 2, 8, 4], 'cur_cost': 1051.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 1, 3, 0, 5, 2, 7, 8, 4], 'cur_cost': 1169.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:17:31,745 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1121.00)
2025-08-04 17:17:31,745 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:17:31,745 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:17:31,745 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:31,746 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:17:31,746 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,746 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,746 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,746 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,747 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1085.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:17:31,747 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [1, 5, 2, 0, 7, 3, 4, 8, 6], 'cur_cost': 1085.0, 'intermediate_solutions': [{'tour': [1, 2, 3, 5, 0, 7, 6, 4, 8], 'cur_cost': 1062.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 3, 5, 0, 7, 4, 6, 2], 'cur_cost': 1098.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 8, 3, 0, 7, 6, 5, 4, 2], 'cur_cost': 948.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:17:31,747 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1085.00)
2025-08-04 17:17:31,747 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:17:31,748 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:17:31,748 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:31,748 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:17:31,749 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,749 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,749 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,749 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,750 - ExplorationExpert - INFO - 探索路径生成完成，成本: 868.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:17:31,750 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [4, 2, 0, 7, 5, 6, 3, 8, 1], 'cur_cost': 868.0, 'intermediate_solutions': [{'tour': [2, 0, 5, 8, 3, 4, 7, 6, 1], 'cur_cost': 985.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 0, 7, 5, 3, 8, 4, 6, 1], 'cur_cost': 923.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 0, 4, 8, 5, 3, 7, 6, 1], 'cur_cost': 866.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:17:31,750 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 868.00)
2025-08-04 17:17:31,750 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:17:31,751 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:17:31,751 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:31,751 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:17:31,751 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,751 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,752 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,752 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,752 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1053.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:17:31,752 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [1, 2, 3, 4, 7, 5, 6, 0, 8], 'cur_cost': 1053.0, 'intermediate_solutions': [{'tour': [8, 0, 4, 5, 3, 7, 6, 1, 2], 'cur_cost': 924.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 4, 1, 6, 7, 3, 5, 2], 'cur_cost': 961.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 4, 5, 0, 3, 7, 6, 1, 2], 'cur_cost': 987.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:17:31,753 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1053.00)
2025-08-04 17:17:31,753 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:17:31,753 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:17:31,754 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [3, 8, 5, 6, 7, 4, 2, 0, 1], 'cur_cost': 812.0, 'intermediate_solutions': [{'tour': [1, 3, 7, 5, 6, 4, 8, 2, 0], 'cur_cost': 880.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 3, 6, 5, 7, 4, 8, 2, 0], 'cur_cost': 865.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 7, 5, 6, 3, 8, 2, 0], 'cur_cost': 790.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 3, 6, 4, 2, 8, 7, 5], 'cur_cost': 897.0, 'intermediate_solutions': [{'tour': [8, 0, 4, 2, 7, 3, 5, 6, 1], 'cur_cost': 894.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 3, 7, 8, 4, 0, 5, 6, 1], 'cur_cost': 930.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 0, 4, 8, 7, 3, 6, 5, 1], 'cur_cost': 927.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [7, 4, 2, 8, 5, 3, 0, 6, 1], 'cur_cost': 875.0, 'intermediate_solutions': [{'tour': [7, 3, 6, 0, 4, 8, 2, 5, 1], 'cur_cost': 1001.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 6, 0, 4, 2, 8, 1, 5], 'cur_cost': 910.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 7, 3, 6, 0, 4, 8, 5, 1], 'cur_cost': 970.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 4, 0, 6, 8, 5, 2, 3, 1]), 'cur_cost': np.float64(1127.0), 'intermediate_solutions': [{'tour': array([6, 4, 1, 5, 2, 7, 0, 8, 3]), 'cur_cost': np.float64(1124.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 6, 4, 1, 2, 7, 0, 8, 3]), 'cur_cost': np.float64(955.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 5, 6, 4, 1, 7, 0, 8, 3]), 'cur_cost': np.float64(1072.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 5, 6, 4, 2, 7, 0, 8, 3]), 'cur_cost': np.float64(1034.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 2, 5, 6, 4, 7, 0, 8, 3]), 'cur_cost': np.float64(1088.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 3, 0, 4, 1, 5, 8, 7, 6]), 'cur_cost': np.float64(1086.0), 'intermediate_solutions': [{'tour': array([2, 6, 0, 5, 7, 8, 4, 1, 3]), 'cur_cost': np.float64(1087.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 2, 6, 0, 7, 8, 4, 1, 3]), 'cur_cost': np.float64(1058.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 5, 2, 6, 0, 8, 4, 1, 3]), 'cur_cost': np.float64(1094.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 5, 2, 6, 7, 8, 4, 1, 3]), 'cur_cost': np.float64(1148.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 7, 5, 2, 6, 8, 4, 1, 3]), 'cur_cost': np.float64(1148.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 2, 6, 7, 3, 5, 4, 1], 'cur_cost': 921.0, 'intermediate_solutions': [{'tour': [0, 6, 3, 7, 5, 4, 8, 1, 2], 'cur_cost': 1006.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 4, 3, 7, 5, 8, 1, 2], 'cur_cost': 1038.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 7, 3, 4, 8, 6, 1, 2], 'cur_cost': 999.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [3, 0, 5, 2, 7, 1, 6, 4, 8], 'cur_cost': 1121.0, 'intermediate_solutions': [{'tour': [5, 1, 3, 0, 6, 7, 2, 8, 4], 'cur_cost': 1100.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 5, 0, 3, 1, 2, 8, 4], 'cur_cost': 1051.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 1, 3, 0, 5, 2, 7, 8, 4], 'cur_cost': 1169.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [1, 5, 2, 0, 7, 3, 4, 8, 6], 'cur_cost': 1085.0, 'intermediate_solutions': [{'tour': [1, 2, 3, 5, 0, 7, 6, 4, 8], 'cur_cost': 1062.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 3, 5, 0, 7, 4, 6, 2], 'cur_cost': 1098.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 8, 3, 0, 7, 6, 5, 4, 2], 'cur_cost': 948.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [4, 2, 0, 7, 5, 6, 3, 8, 1], 'cur_cost': 868.0, 'intermediate_solutions': [{'tour': [2, 0, 5, 8, 3, 4, 7, 6, 1], 'cur_cost': 985.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 0, 7, 5, 3, 8, 4, 6, 1], 'cur_cost': 923.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 0, 4, 8, 5, 3, 7, 6, 1], 'cur_cost': 866.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [1, 2, 3, 4, 7, 5, 6, 0, 8], 'cur_cost': 1053.0, 'intermediate_solutions': [{'tour': [8, 0, 4, 5, 3, 7, 6, 1, 2], 'cur_cost': 924.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 4, 1, 6, 7, 3, 5, 2], 'cur_cost': 961.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 4, 5, 0, 3, 7, 6, 1, 2], 'cur_cost': 987.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:17:31,756 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:17:31,757 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:17:31,758 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=812.000, 多样性=0.857
2025-08-04 17:17:31,758 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-04 17:17:31,758 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-04 17:17:31,758 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:17:31,758 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.02657594089842029, 'best_improvement': -0.027848101265822784}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': np.float64(-0.008571428571428712)}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.8888888888888888, 'new_diversity': 0.8888888888888888, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-04 17:17:31,759 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-04 17:17:31,759 - __main__ - INFO - simple1_9 开始进化第 3 代
2025-08-04 17:17:31,760 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-04 17:17:31,760 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:17:31,761 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=812.000, 多样性=0.857
2025-08-04 17:17:31,761 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:17:31,761 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.857
2025-08-04 17:17:31,762 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:17:31,762 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.889
2025-08-04 17:17:31,764 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-04 17:17:31,764 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:17:31,764 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-04 17:17:31,764 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-04 17:17:31,771 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.167, 适应度梯度: 6.767, 聚类评分: 0.000, 覆盖率: 0.003, 收敛趋势: 0.000, 多样性: 0.636
2025-08-04 17:17:31,771 - LandscapeExpert - WARNING - 可视化器未初始化，跳过可视化更新
2025-08-04 17:17:31,771 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.0077秒
2025-08-04 17:17:31,772 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.16666666666666666, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': np.float64(6.7666666666666755), 'local_optima_density': 0.16666666666666666, 'gradient_variance': np.float64(35669.25888888889), 'cluster_count': 0}, 'population_state': {'diversity': np.float64(0.6363636363636364), 'convergence': np.float64(0.0), 'clustering': 0.0, 'coverage': np.float64(0.0032), 'fitness_entropy': np.float64(0.9111886696810589), 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.003)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 6.767)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754299051.7715776, 'performance_metrics': {}}}
2025-08-04 17:17:31,773 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:17:31,773 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:17:31,773 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:17:31,773 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:17:31,773 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:17:31,774 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-04 17:17:31,774 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:17:31,774 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:17:31,774 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:17:31,775 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:17:31,775 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:17:31,775 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:17:31,776 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 8} (总数: 2, 保护比例: 0.20)
2025-08-04 17:17:31,776 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:17:31,776 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:17:31,776 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:31,776 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:17:31,777 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,777 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,777 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,777 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,777 - ExplorationExpert - INFO - 探索路径生成完成，成本: 975.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:17:31,778 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [1, 6, 7, 0, 3, 8, 4, 2, 5], 'cur_cost': 975.0, 'intermediate_solutions': [{'tour': [3, 0, 5, 6, 7, 4, 2, 8, 1], 'cur_cost': 993.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 5, 6, 7, 4, 1, 0, 2], 'cur_cost': 848.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 5, 6, 7, 3, 4, 2, 0, 1], 'cur_cost': 832.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:17:31,778 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 975.00)
2025-08-04 17:17:31,778 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:17:31,778 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:17:31,779 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:31,779 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:17:31,779 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,779 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,780 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,780 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,780 - ExplorationExpert - INFO - 探索路径生成完成，成本: 948.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:17:31,780 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [8, 7, 5, 0, 4, 3, 6, 1, 2], 'cur_cost': 948.0, 'intermediate_solutions': [{'tour': [0, 6, 3, 1, 4, 2, 8, 7, 5], 'cur_cost': 909.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 8, 2, 4, 6, 3, 1, 5], 'cur_cost': 1033.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 3, 6, 0, 4, 2, 8, 7, 5], 'cur_cost': 925.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:17:31,781 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 948.00)
2025-08-04 17:17:31,781 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:17:31,781 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:17:31,781 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:31,782 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:17:31,782 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,782 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,782 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,783 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,783 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1034.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:17:31,783 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [8, 6, 7, 2, 3, 1, 0, 4, 5], 'cur_cost': 1034.0, 'intermediate_solutions': [{'tour': [7, 4, 2, 0, 5, 3, 8, 6, 1], 'cur_cost': 928.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 4, 2, 8, 5, 3, 1, 6, 0], 'cur_cost': 875.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 4, 2, 8, 5, 3, 0, 6, 1], 'cur_cost': 875.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:17:31,784 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1034.00)
2025-08-04 17:17:31,784 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-04 17:17:31,784 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:17:31,784 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:17:31,785 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 880.0
2025-08-04 17:17:31,821 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:17:31,821 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-08-04 17:17:31,821 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1]), array([0, 1, 4, 2, 8, 7, 3, 5, 6]), array([0, 7, 6, 5, 3, 8, 2, 4, 1])]
2025-08-04 17:17:31,822 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:17:31,822 - ExploitationExpert - INFO - populations: [{'tour': [1, 6, 7, 0, 3, 8, 4, 2, 5], 'cur_cost': 975.0}, {'tour': [8, 7, 5, 0, 4, 3, 6, 1, 2], 'cur_cost': 948.0}, {'tour': [8, 6, 7, 2, 3, 1, 0, 4, 5], 'cur_cost': 1034.0}, {'tour': array([4, 8, 7, 0, 1, 5, 3, 6, 2]), 'cur_cost': np.float64(880.0)}, {'tour': [2, 3, 0, 4, 1, 5, 8, 7, 6], 'cur_cost': 1086.0}, {'tour': [0, 8, 2, 6, 7, 3, 5, 4, 1], 'cur_cost': 921.0}, {'tour': [3, 0, 5, 2, 7, 1, 6, 4, 8], 'cur_cost': 1121.0}, {'tour': [1, 5, 2, 0, 7, 3, 4, 8, 6], 'cur_cost': 1085.0}, {'tour': [4, 2, 0, 7, 5, 6, 3, 8, 1], 'cur_cost': 868.0}, {'tour': [1, 2, 3, 4, 7, 5, 6, 0, 8], 'cur_cost': 1053.0}]
2025-08-04 17:17:31,822 - ExploitationExpert - INFO - 局部搜索耗时: 0.04秒，最大迭代次数: 10
2025-08-04 17:17:31,822 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-04 17:17:31,823 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([4, 8, 7, 0, 1, 5, 3, 6, 2]), 'cur_cost': np.float64(880.0), 'intermediate_solutions': [{'tour': array([0, 4, 7, 6, 8, 5, 2, 3, 1]), 'cur_cost': np.float64(1029.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 0, 4, 7, 8, 5, 2, 3, 1]), 'cur_cost': np.float64(1067.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 6, 0, 4, 7, 5, 2, 3, 1]), 'cur_cost': np.float64(1150.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 6, 0, 4, 8, 5, 2, 3, 1]), 'cur_cost': np.float64(1069.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 8, 6, 0, 4, 5, 2, 3, 1]), 'cur_cost': np.float64(1162.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:17:31,823 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 880.00)
2025-08-04 17:17:31,824 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-04 17:17:31,824 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:17:31,824 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:17:31,824 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 1042.0
2025-08-04 17:17:31,863 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:17:31,863 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-08-04 17:17:31,863 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1]), array([0, 1, 4, 2, 8, 7, 3, 5, 6]), array([0, 7, 6, 5, 3, 8, 2, 4, 1])]
2025-08-04 17:17:31,864 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:17:31,864 - ExploitationExpert - INFO - populations: [{'tour': [1, 6, 7, 0, 3, 8, 4, 2, 5], 'cur_cost': 975.0}, {'tour': [8, 7, 5, 0, 4, 3, 6, 1, 2], 'cur_cost': 948.0}, {'tour': [8, 6, 7, 2, 3, 1, 0, 4, 5], 'cur_cost': 1034.0}, {'tour': array([4, 8, 7, 0, 1, 5, 3, 6, 2]), 'cur_cost': np.float64(880.0)}, {'tour': array([0, 7, 1, 4, 6, 8, 5, 3, 2]), 'cur_cost': np.float64(1042.0)}, {'tour': [0, 8, 2, 6, 7, 3, 5, 4, 1], 'cur_cost': 921.0}, {'tour': [3, 0, 5, 2, 7, 1, 6, 4, 8], 'cur_cost': 1121.0}, {'tour': [1, 5, 2, 0, 7, 3, 4, 8, 6], 'cur_cost': 1085.0}, {'tour': [4, 2, 0, 7, 5, 6, 3, 8, 1], 'cur_cost': 868.0}, {'tour': [1, 2, 3, 4, 7, 5, 6, 0, 8], 'cur_cost': 1053.0}]
2025-08-04 17:17:31,865 - ExploitationExpert - INFO - 局部搜索耗时: 0.04秒，最大迭代次数: 10
2025-08-04 17:17:31,865 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-04 17:17:31,866 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([0, 7, 1, 4, 6, 8, 5, 3, 2]), 'cur_cost': np.float64(1042.0), 'intermediate_solutions': [{'tour': array([0, 3, 2, 4, 1, 5, 8, 7, 6]), 'cur_cost': np.float64(964.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 0, 3, 2, 1, 5, 8, 7, 6]), 'cur_cost': np.float64(1077.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 4, 0, 3, 2, 5, 8, 7, 6]), 'cur_cost': np.float64(1023.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 4, 0, 3, 1, 5, 8, 7, 6]), 'cur_cost': np.float64(1050.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 1, 4, 0, 3, 5, 8, 7, 6]), 'cur_cost': np.float64(971.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:17:31,867 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1042.00)
2025-08-04 17:17:31,867 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:17:31,867 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:17:31,867 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:31,868 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:17:31,868 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,868 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,869 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,869 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,869 - ExplorationExpert - INFO - 探索路径生成完成，成本: 887.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:17:31,869 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [7, 3, 6, 0, 4, 2, 8, 5, 1], 'cur_cost': 887.0, 'intermediate_solutions': [{'tour': [5, 8, 2, 6, 7, 3, 0, 4, 1], 'cur_cost': 1011.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 2, 6, 7, 3, 4, 5, 1], 'cur_cost': 1039.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 1, 2, 6, 7, 3, 5, 4], 'cur_cost': 1070.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:17:31,870 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 887.00)
2025-08-04 17:17:31,870 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-04 17:17:31,870 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:17:31,870 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:17:31,870 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 915.0
2025-08-04 17:17:31,907 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:17:31,907 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-08-04 17:17:31,907 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1]), array([0, 1, 4, 2, 8, 7, 3, 5, 6]), array([0, 7, 6, 5, 3, 8, 2, 4, 1])]
2025-08-04 17:17:31,908 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:17:31,908 - ExploitationExpert - INFO - populations: [{'tour': [1, 6, 7, 0, 3, 8, 4, 2, 5], 'cur_cost': 975.0}, {'tour': [8, 7, 5, 0, 4, 3, 6, 1, 2], 'cur_cost': 948.0}, {'tour': [8, 6, 7, 2, 3, 1, 0, 4, 5], 'cur_cost': 1034.0}, {'tour': array([4, 8, 7, 0, 1, 5, 3, 6, 2]), 'cur_cost': np.float64(880.0)}, {'tour': array([0, 7, 1, 4, 6, 8, 5, 3, 2]), 'cur_cost': np.float64(1042.0)}, {'tour': [7, 3, 6, 0, 4, 2, 8, 5, 1], 'cur_cost': 887.0}, {'tour': array([4, 0, 7, 6, 1, 2, 5, 3, 8]), 'cur_cost': np.float64(915.0)}, {'tour': [1, 5, 2, 0, 7, 3, 4, 8, 6], 'cur_cost': 1085.0}, {'tour': [4, 2, 0, 7, 5, 6, 3, 8, 1], 'cur_cost': 868.0}, {'tour': [1, 2, 3, 4, 7, 5, 6, 0, 8], 'cur_cost': 1053.0}]
2025-08-04 17:17:31,909 - ExploitationExpert - INFO - 局部搜索耗时: 0.04秒，最大迭代次数: 10
2025-08-04 17:17:31,909 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-04 17:17:31,910 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([4, 0, 7, 6, 1, 2, 5, 3, 8]), 'cur_cost': np.float64(915.0), 'intermediate_solutions': [{'tour': array([5, 0, 3, 2, 7, 1, 6, 4, 8]), 'cur_cost': np.float64(1118.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 5, 0, 3, 7, 1, 6, 4, 8]), 'cur_cost': np.float64(1073.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 2, 5, 0, 3, 1, 6, 4, 8]), 'cur_cost': np.float64(1169.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 2, 5, 0, 7, 1, 6, 4, 8]), 'cur_cost': np.float64(1100.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 7, 2, 5, 0, 1, 6, 4, 8]), 'cur_cost': np.float64(975.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:17:31,910 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 915.00)
2025-08-04 17:17:31,910 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:17:31,911 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:17:31,911 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:31,911 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:17:31,911 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,912 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,912 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,912 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,912 - ExplorationExpert - INFO - 探索路径生成完成，成本: 910.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:17:31,912 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 7, 8, 4, 3, 6, 5, 2, 1], 'cur_cost': 910.0, 'intermediate_solutions': [{'tour': [1, 5, 2, 0, 7, 3, 6, 8, 4], 'cur_cost': 1052.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 8, 4, 3, 7, 0, 2, 5], 'cur_cost': 1085.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 2, 0, 7, 3, 4, 5, 8, 6], 'cur_cost': 1030.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:17:31,913 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 910.00)
2025-08-04 17:17:31,913 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:17:31,913 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:17:31,913 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:31,913 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:17:31,914 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,914 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,914 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,914 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,914 - ExplorationExpert - INFO - 探索路径生成完成，成本: 924.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:17:31,915 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 3, 5, 4, 7, 6, 8, 2, 1], 'cur_cost': 924.0, 'intermediate_solutions': [{'tour': [4, 2, 0, 5, 7, 6, 3, 8, 1], 'cur_cost': 923.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 2, 7, 0, 5, 6, 3, 8, 1], 'cur_cost': 928.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 0, 7, 5, 6, 3, 8, 1], 'cur_cost': 857.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:17:31,915 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 924.00)
2025-08-04 17:17:31,915 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:17:31,916 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:17:31,916 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:31,916 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:17:31,916 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,917 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,917 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,917 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,917 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1030.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:17:31,917 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [2, 6, 1, 4, 3, 7, 5, 8, 0], 'cur_cost': 1030.0, 'intermediate_solutions': [{'tour': [1, 2, 3, 4, 7, 0, 6, 5, 8], 'cur_cost': 1028.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 3, 2, 7, 5, 6, 0, 8], 'cur_cost': 1058.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 2, 3, 7, 4, 5, 6, 0, 8], 'cur_cost': 1063.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:17:31,918 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1030.00)
2025-08-04 17:17:31,918 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:17:31,918 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:17:31,920 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [1, 6, 7, 0, 3, 8, 4, 2, 5], 'cur_cost': 975.0, 'intermediate_solutions': [{'tour': [3, 0, 5, 6, 7, 4, 2, 8, 1], 'cur_cost': 993.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 5, 6, 7, 4, 1, 0, 2], 'cur_cost': 848.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 5, 6, 7, 3, 4, 2, 0, 1], 'cur_cost': 832.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [8, 7, 5, 0, 4, 3, 6, 1, 2], 'cur_cost': 948.0, 'intermediate_solutions': [{'tour': [0, 6, 3, 1, 4, 2, 8, 7, 5], 'cur_cost': 909.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 8, 2, 4, 6, 3, 1, 5], 'cur_cost': 1033.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 3, 6, 0, 4, 2, 8, 7, 5], 'cur_cost': 925.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [8, 6, 7, 2, 3, 1, 0, 4, 5], 'cur_cost': 1034.0, 'intermediate_solutions': [{'tour': [7, 4, 2, 0, 5, 3, 8, 6, 1], 'cur_cost': 928.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 4, 2, 8, 5, 3, 1, 6, 0], 'cur_cost': 875.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 4, 2, 8, 5, 3, 0, 6, 1], 'cur_cost': 875.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 8, 7, 0, 1, 5, 3, 6, 2]), 'cur_cost': np.float64(880.0), 'intermediate_solutions': [{'tour': array([0, 4, 7, 6, 8, 5, 2, 3, 1]), 'cur_cost': np.float64(1029.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 0, 4, 7, 8, 5, 2, 3, 1]), 'cur_cost': np.float64(1067.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 6, 0, 4, 7, 5, 2, 3, 1]), 'cur_cost': np.float64(1150.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 6, 0, 4, 8, 5, 2, 3, 1]), 'cur_cost': np.float64(1069.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 8, 6, 0, 4, 5, 2, 3, 1]), 'cur_cost': np.float64(1162.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 7, 1, 4, 6, 8, 5, 3, 2]), 'cur_cost': np.float64(1042.0), 'intermediate_solutions': [{'tour': array([0, 3, 2, 4, 1, 5, 8, 7, 6]), 'cur_cost': np.float64(964.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 0, 3, 2, 1, 5, 8, 7, 6]), 'cur_cost': np.float64(1077.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 4, 0, 3, 2, 5, 8, 7, 6]), 'cur_cost': np.float64(1023.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 4, 0, 3, 1, 5, 8, 7, 6]), 'cur_cost': np.float64(1050.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 1, 4, 0, 3, 5, 8, 7, 6]), 'cur_cost': np.float64(971.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [7, 3, 6, 0, 4, 2, 8, 5, 1], 'cur_cost': 887.0, 'intermediate_solutions': [{'tour': [5, 8, 2, 6, 7, 3, 0, 4, 1], 'cur_cost': 1011.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 2, 6, 7, 3, 4, 5, 1], 'cur_cost': 1039.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 1, 2, 6, 7, 3, 5, 4], 'cur_cost': 1070.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 0, 7, 6, 1, 2, 5, 3, 8]), 'cur_cost': np.float64(915.0), 'intermediate_solutions': [{'tour': array([5, 0, 3, 2, 7, 1, 6, 4, 8]), 'cur_cost': np.float64(1118.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 5, 0, 3, 7, 1, 6, 4, 8]), 'cur_cost': np.float64(1073.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 2, 5, 0, 3, 1, 6, 4, 8]), 'cur_cost': np.float64(1169.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 2, 5, 0, 7, 1, 6, 4, 8]), 'cur_cost': np.float64(1100.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 7, 2, 5, 0, 1, 6, 4, 8]), 'cur_cost': np.float64(975.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 8, 4, 3, 6, 5, 2, 1], 'cur_cost': 910.0, 'intermediate_solutions': [{'tour': [1, 5, 2, 0, 7, 3, 6, 8, 4], 'cur_cost': 1052.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 8, 4, 3, 7, 0, 2, 5], 'cur_cost': 1085.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 2, 0, 7, 3, 4, 5, 8, 6], 'cur_cost': 1030.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 5, 4, 7, 6, 8, 2, 1], 'cur_cost': 924.0, 'intermediate_solutions': [{'tour': [4, 2, 0, 5, 7, 6, 3, 8, 1], 'cur_cost': 923.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 2, 7, 0, 5, 6, 3, 8, 1], 'cur_cost': 928.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 0, 7, 5, 6, 3, 8, 1], 'cur_cost': 857.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [2, 6, 1, 4, 3, 7, 5, 8, 0], 'cur_cost': 1030.0, 'intermediate_solutions': [{'tour': [1, 2, 3, 4, 7, 0, 6, 5, 8], 'cur_cost': 1028.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 3, 2, 7, 5, 6, 0, 8], 'cur_cost': 1058.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 2, 3, 7, 4, 5, 6, 0, 8], 'cur_cost': 1063.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:17:31,922 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:17:31,923 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:17:31,924 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=880.000, 多样性=0.849
2025-08-04 17:17:31,924 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-04 17:17:31,924 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-04 17:17:31,924 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:17:31,925 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 80, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'slight_improvement', 'improvement_rate': 0.007383317182527713, 'best_improvement': -0.08374384236453201}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': np.float64(-0.00864553314121026)}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.8518518518518519, 'new_diversity': 0.8518518518518519, 'diversity_change': 0.0}, 'suggestions': ['改进缓慢，建议调整策略平衡或增加扰动强度', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:17:31,925 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-04 17:17:31,926 - __main__ - INFO - simple1_9 开始进化第 4 代
2025-08-04 17:17:31,926 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-04 17:17:31,926 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:17:31,927 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=880.000, 多样性=0.849
2025-08-04 17:17:31,927 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:17:31,928 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.849
2025-08-04 17:17:31,928 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:17:31,928 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.852
2025-08-04 17:17:31,930 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-04 17:17:31,930 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:17:31,930 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-04 17:17:31,930 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-04 17:17:31,938 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.308, 适应度梯度: 21.215, 聚类评分: 0.000, 覆盖率: 0.004, 收敛趋势: 0.000, 多样性: 0.592
2025-08-04 17:17:31,938 - LandscapeExpert - WARNING - 可视化器未初始化，跳过可视化更新
2025-08-04 17:17:31,938 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.0085秒
2025-08-04 17:17:31,939 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3076923076923077, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': np.float64(21.21538461538462), 'local_optima_density': 0.3076923076923077, 'gradient_variance': np.float64(18718.181301775145), 'cluster_count': 0}, 'population_state': {'diversity': np.float64(0.591715976331361), 'convergence': np.float64(0.0), 'clustering': 0.0, 'coverage': np.float64(0.0041), 'fitness_entropy': np.float64(0.9475231583990614), 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.004)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 21.215)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754299051.938565, 'performance_metrics': {}}}
2025-08-04 17:17:31,940 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:17:31,940 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:17:31,940 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:17:31,940 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:17:31,940 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-04 17:17:31,940 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-04 17:17:31,941 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-04 17:17:31,941 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:17:31,941 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:17:31,941 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-04 17:17:31,941 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:17:31,942 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:17:31,942 - experts.management.collaboration_manager - INFO - 识别精英个体: {3, 5} (总数: 2, 保护比例: 0.20)
2025-08-04 17:17:31,942 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:17:31,942 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:17:31,943 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:31,943 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:17:31,943 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,943 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,944 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,944 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,944 - ExplorationExpert - INFO - 探索路径生成完成，成本: 814.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:17:31,944 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [2, 8, 7, 5, 6, 3, 4, 0, 1], 'cur_cost': 814.0, 'intermediate_solutions': [{'tour': [1, 6, 3, 0, 7, 8, 4, 2, 5], 'cur_cost': 1002.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 8, 3, 0, 7, 6, 2, 5], 'cur_cost': 1097.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 7, 3, 0, 8, 4, 2, 5], 'cur_cost': 1010.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:17:31,944 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 814.00)
2025-08-04 17:17:31,945 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:17:31,945 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:17:31,945 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:31,945 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:17:31,945 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,946 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,946 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,946 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,946 - ExplorationExpert - INFO - 探索路径生成完成，成本: 912.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:17:31,946 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [2, 7, 0, 6, 3, 5, 8, 4, 1], 'cur_cost': 912.0, 'intermediate_solutions': [{'tour': [8, 7, 5, 1, 4, 3, 6, 0, 2], 'cur_cost': 975.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 7, 1, 6, 3, 4, 0, 5, 2], 'cur_cost': 1037.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 7, 5, 0, 3, 4, 6, 1, 2], 'cur_cost': 1036.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:17:31,947 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 912.00)
2025-08-04 17:17:31,947 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-04 17:17:31,947 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:17:31,947 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:17:31,947 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 1017.0
2025-08-04 17:17:31,985 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:17:31,985 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-08-04 17:17:31,985 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1]), array([0, 1, 4, 2, 8, 7, 3, 5, 6]), array([0, 7, 6, 5, 3, 8, 2, 4, 1])]
2025-08-04 17:17:31,986 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:17:31,987 - ExploitationExpert - INFO - populations: [{'tour': [2, 8, 7, 5, 6, 3, 4, 0, 1], 'cur_cost': 814.0}, {'tour': [2, 7, 0, 6, 3, 5, 8, 4, 1], 'cur_cost': 912.0}, {'tour': array([7, 5, 1, 4, 8, 3, 6, 2, 0]), 'cur_cost': np.float64(1017.0)}, {'tour': [4, 8, 7, 0, 1, 5, 3, 6, 2], 'cur_cost': 880.0}, {'tour': [0, 7, 1, 4, 6, 8, 5, 3, 2], 'cur_cost': 1042.0}, {'tour': [7, 3, 6, 0, 4, 2, 8, 5, 1], 'cur_cost': 887.0}, {'tour': [4, 0, 7, 6, 1, 2, 5, 3, 8], 'cur_cost': 915.0}, {'tour': [0, 7, 8, 4, 3, 6, 5, 2, 1], 'cur_cost': 910.0}, {'tour': [0, 3, 5, 4, 7, 6, 8, 2, 1], 'cur_cost': 924.0}, {'tour': [2, 6, 1, 4, 3, 7, 5, 8, 0], 'cur_cost': 1030.0}]
2025-08-04 17:17:31,987 - ExploitationExpert - INFO - 局部搜索耗时: 0.04秒，最大迭代次数: 10
2025-08-04 17:17:31,988 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-08-04 17:17:31,988 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([7, 5, 1, 4, 8, 3, 6, 2, 0]), 'cur_cost': np.float64(1017.0), 'intermediate_solutions': [{'tour': array([7, 6, 8, 2, 3, 1, 0, 4, 5]), 'cur_cost': np.float64(976.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 7, 6, 8, 3, 1, 0, 4, 5]), 'cur_cost': np.float64(1037.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 2, 7, 6, 8, 1, 0, 4, 5]), 'cur_cost': np.float64(1008.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 2, 7, 6, 3, 1, 0, 4, 5]), 'cur_cost': np.float64(945.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 3, 2, 7, 6, 1, 0, 4, 5]), 'cur_cost': np.float64(926.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:17:31,989 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1017.00)
2025-08-04 17:17:31,989 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:17:31,989 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:17:31,989 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:31,990 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:17:31,990 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,990 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,990 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,991 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:31,991 - ExplorationExpert - INFO - 探索路径生成完成，成本: 947.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:17:31,991 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [1, 8, 2, 7, 0, 4, 3, 5, 6], 'cur_cost': 947.0, 'intermediate_solutions': [{'tour': [4, 8, 7, 0, 1, 5, 6, 3, 2], 'cur_cost': 848.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 7, 8, 0, 1, 5, 3, 6, 2], 'cur_cost': 932.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 8, 0, 1, 5, 3, 6, 7, 2], 'cur_cost': 869.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:17:31,991 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 947.00)
2025-08-04 17:17:31,991 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-04 17:17:31,992 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:17:31,992 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:17:31,992 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 996.0
2025-08-04 17:17:32,031 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:17:32,031 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-08-04 17:17:32,031 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1]), array([0, 1, 4, 2, 8, 7, 3, 5, 6]), array([0, 7, 6, 5, 3, 8, 2, 4, 1])]
2025-08-04 17:17:32,032 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:17:32,032 - ExploitationExpert - INFO - populations: [{'tour': [2, 8, 7, 5, 6, 3, 4, 0, 1], 'cur_cost': 814.0}, {'tour': [2, 7, 0, 6, 3, 5, 8, 4, 1], 'cur_cost': 912.0}, {'tour': array([7, 5, 1, 4, 8, 3, 6, 2, 0]), 'cur_cost': np.float64(1017.0)}, {'tour': [1, 8, 2, 7, 0, 4, 3, 5, 6], 'cur_cost': 947.0}, {'tour': array([1, 0, 2, 3, 4, 7, 8, 6, 5]), 'cur_cost': np.float64(996.0)}, {'tour': [7, 3, 6, 0, 4, 2, 8, 5, 1], 'cur_cost': 887.0}, {'tour': [4, 0, 7, 6, 1, 2, 5, 3, 8], 'cur_cost': 915.0}, {'tour': [0, 7, 8, 4, 3, 6, 5, 2, 1], 'cur_cost': 910.0}, {'tour': [0, 3, 5, 4, 7, 6, 8, 2, 1], 'cur_cost': 924.0}, {'tour': [2, 6, 1, 4, 3, 7, 5, 8, 0], 'cur_cost': 1030.0}]
2025-08-04 17:17:32,033 - ExploitationExpert - INFO - 局部搜索耗时: 0.04秒，最大迭代次数: 10
2025-08-04 17:17:32,034 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-08-04 17:17:32,035 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([1, 0, 2, 3, 4, 7, 8, 6, 5]), 'cur_cost': np.float64(996.0), 'intermediate_solutions': [{'tour': array([1, 7, 0, 4, 6, 8, 5, 3, 2]), 'cur_cost': np.float64(1031.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 1, 7, 0, 6, 8, 5, 3, 2]), 'cur_cost': np.float64(918.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 4, 1, 7, 0, 8, 5, 3, 2]), 'cur_cost': np.float64(1101.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 4, 1, 7, 6, 8, 5, 3, 2]), 'cur_cost': np.float64(975.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 6, 4, 1, 7, 8, 5, 3, 2]), 'cur_cost': np.float64(1001.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:17:32,035 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 996.00)
2025-08-04 17:17:32,036 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:17:32,036 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:17:32,036 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:32,036 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:17:32,037 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,037 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,037 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,037 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,037 - ExplorationExpert - INFO - 探索路径生成完成，成本: 849.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:17:32,038 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [7, 6, 0, 4, 2, 8, 3, 5, 1], 'cur_cost': 849.0, 'intermediate_solutions': [{'tour': [7, 3, 5, 0, 4, 2, 8, 6, 1], 'cur_cost': 874.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 6, 0, 4, 5, 8, 2, 1], 'cur_cost': 946.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 3, 6, 0, 2, 4, 8, 5, 1], 'cur_cost': 924.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:17:32,038 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 849.00)
2025-08-04 17:17:32,038 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:17:32,038 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:17:32,038 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:32,039 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:17:32,039 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,039 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,039 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,040 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,040 - ExplorationExpert - INFO - 探索路径生成完成，成本: 986.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:17:32,040 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [1, 8, 0, 5, 3, 7, 6, 4, 2], 'cur_cost': 986.0, 'intermediate_solutions': [{'tour': [4, 8, 7, 6, 1, 2, 5, 3, 0], 'cur_cost': 963.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 0, 2, 1, 6, 7, 5, 3, 8], 'cur_cost': 856.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 7, 6, 1, 2, 5, 3, 0, 8], 'cur_cost': 1023.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:17:32,040 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 986.00)
2025-08-04 17:17:32,041 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:17:32,041 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:17:32,041 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:32,041 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:17:32,041 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,041 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,042 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,042 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,042 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1135.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:17:32,042 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [8, 6, 0, 4, 3, 1, 7, 2, 5], 'cur_cost': 1135.0, 'intermediate_solutions': [{'tour': [0, 8, 7, 4, 3, 6, 5, 2, 1], 'cur_cost': 962.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 8, 4, 3, 6, 2, 5, 1], 'cur_cost': 1057.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 7, 8, 4, 3, 6, 5, 1], 'cur_cost': 938.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:17:32,043 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1135.00)
2025-08-04 17:17:32,043 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:17:32,043 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:17:32,043 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:32,043 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:17:32,044 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,044 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,044 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,044 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,044 - ExplorationExpert - INFO - 探索路径生成完成，成本: 859.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:17:32,045 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 1, 6, 3, 7, 8, 4, 2, 5], 'cur_cost': 859.0, 'intermediate_solutions': [{'tour': [0, 3, 7, 4, 5, 6, 8, 2, 1], 'cur_cost': 911.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 5, 4, 1, 2, 8, 6, 7], 'cur_cost': 978.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 5, 4, 7, 6, 8, 2, 0, 1], 'cur_cost': 954.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:17:32,045 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 859.00)
2025-08-04 17:17:32,045 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-04 17:17:32,045 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:17:32,045 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:17:32,046 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 1038.0
2025-08-04 17:17:32,080 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:17:32,081 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-08-04 17:17:32,081 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1]), array([0, 1, 4, 2, 8, 7, 3, 5, 6]), array([0, 7, 6, 5, 3, 8, 2, 4, 1])]
2025-08-04 17:17:32,082 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:17:32,082 - ExploitationExpert - INFO - populations: [{'tour': [2, 8, 7, 5, 6, 3, 4, 0, 1], 'cur_cost': 814.0}, {'tour': [2, 7, 0, 6, 3, 5, 8, 4, 1], 'cur_cost': 912.0}, {'tour': array([7, 5, 1, 4, 8, 3, 6, 2, 0]), 'cur_cost': np.float64(1017.0)}, {'tour': [1, 8, 2, 7, 0, 4, 3, 5, 6], 'cur_cost': 947.0}, {'tour': array([1, 0, 2, 3, 4, 7, 8, 6, 5]), 'cur_cost': np.float64(996.0)}, {'tour': [7, 6, 0, 4, 2, 8, 3, 5, 1], 'cur_cost': 849.0}, {'tour': [1, 8, 0, 5, 3, 7, 6, 4, 2], 'cur_cost': 986.0}, {'tour': [8, 6, 0, 4, 3, 1, 7, 2, 5], 'cur_cost': 1135.0}, {'tour': [0, 1, 6, 3, 7, 8, 4, 2, 5], 'cur_cost': 859.0}, {'tour': array([6, 8, 2, 3, 4, 1, 7, 5, 0]), 'cur_cost': np.float64(1038.0)}]
2025-08-04 17:17:32,083 - ExploitationExpert - INFO - 局部搜索耗时: 0.04秒，最大迭代次数: 10
2025-08-04 17:17:32,084 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-08-04 17:17:32,084 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([6, 8, 2, 3, 4, 1, 7, 5, 0]), 'cur_cost': np.float64(1038.0), 'intermediate_solutions': [{'tour': array([1, 6, 2, 4, 3, 7, 5, 8, 0]), 'cur_cost': np.float64(894.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 1, 6, 2, 3, 7, 5, 8, 0]), 'cur_cost': np.float64(1016.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 4, 1, 6, 2, 7, 5, 8, 0]), 'cur_cost': np.float64(1097.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 4, 1, 6, 3, 7, 5, 8, 0]), 'cur_cost': np.float64(875.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 3, 4, 1, 6, 7, 5, 8, 0]), 'cur_cost': np.float64(1011.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:17:32,085 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1038.00)
2025-08-04 17:17:32,085 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:17:32,085 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:17:32,087 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [2, 8, 7, 5, 6, 3, 4, 0, 1], 'cur_cost': 814.0, 'intermediate_solutions': [{'tour': [1, 6, 3, 0, 7, 8, 4, 2, 5], 'cur_cost': 1002.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 8, 3, 0, 7, 6, 2, 5], 'cur_cost': 1097.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 7, 3, 0, 8, 4, 2, 5], 'cur_cost': 1010.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [2, 7, 0, 6, 3, 5, 8, 4, 1], 'cur_cost': 912.0, 'intermediate_solutions': [{'tour': [8, 7, 5, 1, 4, 3, 6, 0, 2], 'cur_cost': 975.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 7, 1, 6, 3, 4, 0, 5, 2], 'cur_cost': 1037.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 7, 5, 0, 3, 4, 6, 1, 2], 'cur_cost': 1036.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 5, 1, 4, 8, 3, 6, 2, 0]), 'cur_cost': np.float64(1017.0), 'intermediate_solutions': [{'tour': array([7, 6, 8, 2, 3, 1, 0, 4, 5]), 'cur_cost': np.float64(976.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 7, 6, 8, 3, 1, 0, 4, 5]), 'cur_cost': np.float64(1037.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 2, 7, 6, 8, 1, 0, 4, 5]), 'cur_cost': np.float64(1008.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 2, 7, 6, 3, 1, 0, 4, 5]), 'cur_cost': np.float64(945.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 3, 2, 7, 6, 1, 0, 4, 5]), 'cur_cost': np.float64(926.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [1, 8, 2, 7, 0, 4, 3, 5, 6], 'cur_cost': 947.0, 'intermediate_solutions': [{'tour': [4, 8, 7, 0, 1, 5, 6, 3, 2], 'cur_cost': 848.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 7, 8, 0, 1, 5, 3, 6, 2], 'cur_cost': 932.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 8, 0, 1, 5, 3, 6, 7, 2], 'cur_cost': 869.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([1, 0, 2, 3, 4, 7, 8, 6, 5]), 'cur_cost': np.float64(996.0), 'intermediate_solutions': [{'tour': array([1, 7, 0, 4, 6, 8, 5, 3, 2]), 'cur_cost': np.float64(1031.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 1, 7, 0, 6, 8, 5, 3, 2]), 'cur_cost': np.float64(918.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 4, 1, 7, 0, 8, 5, 3, 2]), 'cur_cost': np.float64(1101.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 4, 1, 7, 6, 8, 5, 3, 2]), 'cur_cost': np.float64(975.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 6, 4, 1, 7, 8, 5, 3, 2]), 'cur_cost': np.float64(1001.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [7, 6, 0, 4, 2, 8, 3, 5, 1], 'cur_cost': 849.0, 'intermediate_solutions': [{'tour': [7, 3, 5, 0, 4, 2, 8, 6, 1], 'cur_cost': 874.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 6, 0, 4, 5, 8, 2, 1], 'cur_cost': 946.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 3, 6, 0, 2, 4, 8, 5, 1], 'cur_cost': 924.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [1, 8, 0, 5, 3, 7, 6, 4, 2], 'cur_cost': 986.0, 'intermediate_solutions': [{'tour': [4, 8, 7, 6, 1, 2, 5, 3, 0], 'cur_cost': 963.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 0, 2, 1, 6, 7, 5, 3, 8], 'cur_cost': 856.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 7, 6, 1, 2, 5, 3, 0, 8], 'cur_cost': 1023.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [8, 6, 0, 4, 3, 1, 7, 2, 5], 'cur_cost': 1135.0, 'intermediate_solutions': [{'tour': [0, 8, 7, 4, 3, 6, 5, 2, 1], 'cur_cost': 962.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 8, 4, 3, 6, 2, 5, 1], 'cur_cost': 1057.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 7, 8, 4, 3, 6, 5, 1], 'cur_cost': 938.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 6, 3, 7, 8, 4, 2, 5], 'cur_cost': 859.0, 'intermediate_solutions': [{'tour': [0, 3, 7, 4, 5, 6, 8, 2, 1], 'cur_cost': 911.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 5, 4, 1, 2, 8, 6, 7], 'cur_cost': 978.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 5, 4, 7, 6, 8, 2, 0, 1], 'cur_cost': 954.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 8, 2, 3, 4, 1, 7, 5, 0]), 'cur_cost': np.float64(1038.0), 'intermediate_solutions': [{'tour': array([1, 6, 2, 4, 3, 7, 5, 8, 0]), 'cur_cost': np.float64(894.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 1, 6, 2, 3, 7, 5, 8, 0]), 'cur_cost': np.float64(1016.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 4, 1, 6, 2, 7, 5, 8, 0]), 'cur_cost': np.float64(1097.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 4, 1, 6, 3, 7, 5, 8, 0]), 'cur_cost': np.float64(875.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 3, 4, 1, 6, 7, 5, 8, 0]), 'cur_cost': np.float64(1011.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-04 17:17:32,089 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:17:32,089 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:17:32,090 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=814.000, 多样性=0.864
2025-08-04 17:17:32,091 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-04 17:17:32,091 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-04 17:17:32,091 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:17:32,091 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.005029858229546727, 'best_improvement': 0.075}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': np.float64(0.017441860465116307)}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.01902764305317037, 'recent_improvements': [0.04543860328886846, -0.02657594089842029, 0.007383317182527713], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.8518518518518519, 'new_diversity': 0.8518518518518519, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:17:32,092 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-04 17:17:32,092 - __main__ - INFO - simple1_9 开始进化第 5 代
2025-08-04 17:17:32,093 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-04 17:17:32,093 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:17:32,093 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=814.000, 多样性=0.864
2025-08-04 17:17:32,094 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:17:32,094 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.864
2025-08-04 17:17:32,094 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:17:32,095 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.852
2025-08-04 17:17:32,096 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-04 17:17:32,096 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:17:32,097 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-04 17:17:32,097 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-04 17:17:32,104 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.231, 适应度梯度: 7.523, 聚类评分: 0.000, 覆盖率: 0.005, 收敛趋势: 0.000, 多样性: 0.598
2025-08-04 17:17:32,104 - LandscapeExpert - WARNING - 可视化器未初始化，跳过可视化更新
2025-08-04 17:17:32,104 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.0080秒
2025-08-04 17:17:32,105 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.23076923076923078, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': np.float64(7.523076923076924), 'local_optima_density': 0.23076923076923078, 'gradient_variance': np.float64(16326.44023668639), 'cluster_count': 0}, 'population_state': {'diversity': np.float64(0.5976331360946745), 'convergence': np.float64(0.0), 'clustering': 0.0, 'coverage': np.float64(0.0051), 'fitness_entropy': np.float64(0.9686322539060781), 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.005)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 7.523)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754299052.1045227, 'performance_metrics': {}}}
2025-08-04 17:17:32,106 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:17:32,106 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:17:32,106 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:17:32,106 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:17:32,106 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-04 17:17:32,107 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-04 17:17:32,107 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-04 17:17:32,107 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:17:32,107 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:17:32,108 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-04 17:17:32,108 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:17:32,109 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:17:32,109 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 5} (总数: 2, 保护比例: 0.20)
2025-08-04 17:17:32,109 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:17:32,109 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:17:32,109 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:32,110 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:17:32,110 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,110 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,110 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,110 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,111 - ExplorationExpert - INFO - 探索路径生成完成，成本: 847.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:17:32,111 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [7, 5, 8, 3, 6, 4, 2, 0, 1], 'cur_cost': 847.0, 'intermediate_solutions': [{'tour': [2, 8, 7, 5, 6, 3, 0, 4, 1], 'cur_cost': 881.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 7, 5, 6, 1, 0, 4, 3], 'cur_cost': 844.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 8, 7, 0, 5, 6, 3, 4, 1], 'cur_cost': 934.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:17:32,111 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 847.00)
2025-08-04 17:17:32,111 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:17:32,112 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:17:32,112 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:32,112 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:17:32,112 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,113 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,113 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,113 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,113 - ExplorationExpert - INFO - 探索路径生成完成，成本: 744.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:17:32,113 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 7, 3, 6, 5, 8, 4, 2, 1], 'cur_cost': 744.0, 'intermediate_solutions': [{'tour': [2, 7, 0, 4, 3, 5, 8, 6, 1], 'cur_cost': 978.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 7, 0, 6, 3, 5, 1, 4, 8], 'cur_cost': 941.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 6, 7, 0, 3, 5, 8, 4, 1], 'cur_cost': 979.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:17:32,114 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 744.00)
2025-08-04 17:17:32,114 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-04 17:17:32,114 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:17:32,114 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:17:32,114 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 1121.0
2025-08-04 17:17:32,152 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:17:32,152 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-08-04 17:17:32,152 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1]), array([0, 1, 4, 2, 8, 7, 3, 5, 6]), array([0, 7, 6, 5, 3, 8, 2, 4, 1])]
2025-08-04 17:17:32,153 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:17:32,153 - ExploitationExpert - INFO - populations: [{'tour': [7, 5, 8, 3, 6, 4, 2, 0, 1], 'cur_cost': 847.0}, {'tour': [0, 7, 3, 6, 5, 8, 4, 2, 1], 'cur_cost': 744.0}, {'tour': array([2, 1, 7, 8, 5, 4, 3, 0, 6]), 'cur_cost': np.float64(1121.0)}, {'tour': [1, 8, 2, 7, 0, 4, 3, 5, 6], 'cur_cost': 947.0}, {'tour': [1, 0, 2, 3, 4, 7, 8, 6, 5], 'cur_cost': 996.0}, {'tour': [7, 6, 0, 4, 2, 8, 3, 5, 1], 'cur_cost': 849.0}, {'tour': [1, 8, 0, 5, 3, 7, 6, 4, 2], 'cur_cost': 986.0}, {'tour': [8, 6, 0, 4, 3, 1, 7, 2, 5], 'cur_cost': 1135.0}, {'tour': [0, 1, 6, 3, 7, 8, 4, 2, 5], 'cur_cost': 859.0}, {'tour': [6, 8, 2, 3, 4, 1, 7, 5, 0], 'cur_cost': 1038.0}]
2025-08-04 17:17:32,154 - ExploitationExpert - INFO - 局部搜索耗时: 0.04秒，最大迭代次数: 10
2025-08-04 17:17:32,154 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-08-04 17:17:32,155 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([2, 1, 7, 8, 5, 4, 3, 0, 6]), 'cur_cost': np.float64(1121.0), 'intermediate_solutions': [{'tour': array([1, 5, 7, 4, 8, 3, 6, 2, 0]), 'cur_cost': np.float64(963.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 1, 5, 7, 8, 3, 6, 2, 0]), 'cur_cost': np.float64(1009.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 4, 1, 5, 7, 3, 6, 2, 0]), 'cur_cost': np.float64(1052.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 4, 1, 5, 8, 3, 6, 2, 0]), 'cur_cost': np.float64(1044.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 8, 4, 1, 5, 3, 6, 2, 0]), 'cur_cost': np.float64(1016.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:17:32,155 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1121.00)
2025-08-04 17:17:32,155 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:17:32,156 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:17:32,156 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:32,156 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:17:32,157 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,157 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,157 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,157 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,157 - ExplorationExpert - INFO - 探索路径生成完成，成本: 910.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:17:32,158 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [8, 5, 3, 6, 0, 7, 2, 4, 1], 'cur_cost': 910.0, 'intermediate_solutions': [{'tour': [1, 5, 2, 7, 0, 4, 3, 8, 6], 'cur_cost': 1096.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 2, 7, 0, 5, 3, 4, 6], 'cur_cost': 1069.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 8, 2, 4, 7, 0, 3, 5, 6], 'cur_cost': 884.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:17:32,158 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 910.00)
2025-08-04 17:17:32,158 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:17:32,158 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:17:32,159 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:32,159 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:17:32,159 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,159 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,159 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,160 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,160 - ExplorationExpert - INFO - 探索路径生成完成，成本: 937.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:17:32,160 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [6, 7, 8, 5, 0, 4, 2, 1, 3], 'cur_cost': 937.0, 'intermediate_solutions': [{'tour': [1, 0, 7, 3, 4, 2, 8, 6, 5], 'cur_cost': 839.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 0, 6, 8, 7, 4, 3, 2, 5], 'cur_cost': 1069.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 3, 4, 7, 8, 2, 6, 5], 'cur_cost': 993.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:17:32,160 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 937.00)
2025-08-04 17:17:32,161 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:17:32,161 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:17:32,161 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:32,161 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:17:32,161 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,162 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,162 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,162 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,162 - ExplorationExpert - INFO - 探索路径生成完成，成本: 920.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:17:32,162 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [4, 7, 5, 0, 8, 3, 6, 1, 2], 'cur_cost': 920.0, 'intermediate_solutions': [{'tour': [4, 6, 0, 7, 2, 8, 3, 5, 1], 'cur_cost': 973.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 6, 0, 3, 8, 2, 4, 5, 1], 'cur_cost': 968.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 6, 0, 4, 8, 3, 5, 1, 2], 'cur_cost': 932.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:17:32,163 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 920.00)
2025-08-04 17:17:32,163 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:17:32,163 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:17:32,163 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:32,163 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:17:32,164 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,164 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,164 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,164 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,164 - ExplorationExpert - INFO - 探索路径生成完成，成本: 802.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:17:32,165 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [6, 3, 7, 8, 4, 2, 0, 1, 5], 'cur_cost': 802.0, 'intermediate_solutions': [{'tour': [1, 8, 7, 5, 3, 0, 6, 4, 2], 'cur_cost': 958.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 0, 5, 3, 7, 6, 2, 4], 'cur_cost': 995.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 0, 5, 3, 7, 6, 4, 2, 1], 'cur_cost': 986.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:17:32,165 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 802.00)
2025-08-04 17:17:32,165 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-04 17:17:32,166 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:17:32,166 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:17:32,166 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 1126.0
2025-08-04 17:17:32,220 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:17:32,221 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-08-04 17:17:32,221 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1]), array([0, 1, 4, 2, 8, 7, 3, 5, 6]), array([0, 7, 6, 5, 3, 8, 2, 4, 1])]
2025-08-04 17:17:32,222 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:17:32,223 - ExploitationExpert - INFO - populations: [{'tour': [7, 5, 8, 3, 6, 4, 2, 0, 1], 'cur_cost': 847.0}, {'tour': [0, 7, 3, 6, 5, 8, 4, 2, 1], 'cur_cost': 744.0}, {'tour': array([2, 1, 7, 8, 5, 4, 3, 0, 6]), 'cur_cost': np.float64(1121.0)}, {'tour': [8, 5, 3, 6, 0, 7, 2, 4, 1], 'cur_cost': 910.0}, {'tour': [6, 7, 8, 5, 0, 4, 2, 1, 3], 'cur_cost': 937.0}, {'tour': [4, 7, 5, 0, 8, 3, 6, 1, 2], 'cur_cost': 920.0}, {'tour': [6, 3, 7, 8, 4, 2, 0, 1, 5], 'cur_cost': 802.0}, {'tour': array([7, 0, 8, 3, 6, 4, 5, 2, 1]), 'cur_cost': np.float64(1126.0)}, {'tour': [0, 1, 6, 3, 7, 8, 4, 2, 5], 'cur_cost': 859.0}, {'tour': [6, 8, 2, 3, 4, 1, 7, 5, 0], 'cur_cost': 1038.0}]
2025-08-04 17:17:32,224 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒，最大迭代次数: 10
2025-08-04 17:17:32,224 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-08-04 17:17:32,225 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([7, 0, 8, 3, 6, 4, 5, 2, 1]), 'cur_cost': np.float64(1126.0), 'intermediate_solutions': [{'tour': array([0, 6, 8, 4, 3, 1, 7, 2, 5]), 'cur_cost': np.float64(1174.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 0, 6, 8, 3, 1, 7, 2, 5]), 'cur_cost': np.float64(1135.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 4, 0, 6, 8, 1, 7, 2, 5]), 'cur_cost': np.float64(1109.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 4, 0, 6, 3, 1, 7, 2, 5]), 'cur_cost': np.float64(1069.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 3, 4, 0, 6, 1, 7, 2, 5]), 'cur_cost': np.float64(1027.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:17:32,225 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1126.00)
2025-08-04 17:17:32,226 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:17:32,226 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:17:32,226 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:17:32,227 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:17:32,227 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,228 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,228 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,228 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:17:32,229 - ExplorationExpert - INFO - 探索路径生成完成，成本: 923.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:17:32,229 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [2, 3, 0, 1, 8, 7, 5, 6, 4], 'cur_cost': 923.0, 'intermediate_solutions': [{'tour': [0, 1, 8, 3, 7, 6, 4, 2, 5], 'cur_cost': 943.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 6, 1, 7, 8, 4, 2, 5], 'cur_cost': 1005.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 3, 6, 7, 8, 4, 2, 5], 'cur_cost': 926.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:17:32,230 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 923.00)
2025-08-04 17:17:32,230 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-04 17:17:32,230 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:17:32,231 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:17:32,231 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 1104.0
2025-08-04 17:17:32,277 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:17:32,277 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-08-04 17:17:32,277 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1]), array([0, 1, 4, 2, 8, 7, 3, 5, 6]), array([0, 7, 6, 5, 3, 8, 2, 4, 1])]
2025-08-04 17:17:32,278 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:17:32,278 - ExploitationExpert - INFO - populations: [{'tour': [7, 5, 8, 3, 6, 4, 2, 0, 1], 'cur_cost': 847.0}, {'tour': [0, 7, 3, 6, 5, 8, 4, 2, 1], 'cur_cost': 744.0}, {'tour': array([2, 1, 7, 8, 5, 4, 3, 0, 6]), 'cur_cost': np.float64(1121.0)}, {'tour': [8, 5, 3, 6, 0, 7, 2, 4, 1], 'cur_cost': 910.0}, {'tour': [6, 7, 8, 5, 0, 4, 2, 1, 3], 'cur_cost': 937.0}, {'tour': [4, 7, 5, 0, 8, 3, 6, 1, 2], 'cur_cost': 920.0}, {'tour': [6, 3, 7, 8, 4, 2, 0, 1, 5], 'cur_cost': 802.0}, {'tour': array([7, 0, 8, 3, 6, 4, 5, 2, 1]), 'cur_cost': np.float64(1126.0)}, {'tour': [2, 3, 0, 1, 8, 7, 5, 6, 4], 'cur_cost': 923.0}, {'tour': array([3, 6, 8, 7, 0, 2, 5, 1, 4]), 'cur_cost': np.float64(1104.0)}]
2025-08-04 17:17:32,279 - ExploitationExpert - INFO - 局部搜索耗时: 0.05秒，最大迭代次数: 10
2025-08-04 17:17:32,279 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-08-04 17:17:32,280 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([3, 6, 8, 7, 0, 2, 5, 1, 4]), 'cur_cost': np.float64(1104.0), 'intermediate_solutions': [{'tour': array([2, 8, 6, 3, 4, 1, 7, 5, 0]), 'cur_cost': np.float64(1019.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 2, 8, 6, 4, 1, 7, 5, 0]), 'cur_cost': np.float64(1093.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 3, 2, 8, 6, 1, 7, 5, 0]), 'cur_cost': np.float64(1038.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 3, 2, 8, 4, 1, 7, 5, 0]), 'cur_cost': np.float64(972.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 4, 3, 2, 8, 1, 7, 5, 0]), 'cur_cost': np.float64(1116.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:17:32,280 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1104.00)
2025-08-04 17:17:32,281 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:17:32,281 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:17:32,282 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [7, 5, 8, 3, 6, 4, 2, 0, 1], 'cur_cost': 847.0, 'intermediate_solutions': [{'tour': [2, 8, 7, 5, 6, 3, 0, 4, 1], 'cur_cost': 881.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 7, 5, 6, 1, 0, 4, 3], 'cur_cost': 844.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 8, 7, 0, 5, 6, 3, 4, 1], 'cur_cost': 934.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 3, 6, 5, 8, 4, 2, 1], 'cur_cost': 744.0, 'intermediate_solutions': [{'tour': [2, 7, 0, 4, 3, 5, 8, 6, 1], 'cur_cost': 978.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 7, 0, 6, 3, 5, 1, 4, 8], 'cur_cost': 941.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 6, 7, 0, 3, 5, 8, 4, 1], 'cur_cost': 979.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 1, 7, 8, 5, 4, 3, 0, 6]), 'cur_cost': np.float64(1121.0), 'intermediate_solutions': [{'tour': array([1, 5, 7, 4, 8, 3, 6, 2, 0]), 'cur_cost': np.float64(963.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 1, 5, 7, 8, 3, 6, 2, 0]), 'cur_cost': np.float64(1009.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 4, 1, 5, 7, 3, 6, 2, 0]), 'cur_cost': np.float64(1052.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 4, 1, 5, 8, 3, 6, 2, 0]), 'cur_cost': np.float64(1044.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 8, 4, 1, 5, 3, 6, 2, 0]), 'cur_cost': np.float64(1016.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [8, 5, 3, 6, 0, 7, 2, 4, 1], 'cur_cost': 910.0, 'intermediate_solutions': [{'tour': [1, 5, 2, 7, 0, 4, 3, 8, 6], 'cur_cost': 1096.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 2, 7, 0, 5, 3, 4, 6], 'cur_cost': 1069.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 8, 2, 4, 7, 0, 3, 5, 6], 'cur_cost': 884.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [6, 7, 8, 5, 0, 4, 2, 1, 3], 'cur_cost': 937.0, 'intermediate_solutions': [{'tour': [1, 0, 7, 3, 4, 2, 8, 6, 5], 'cur_cost': 839.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 0, 6, 8, 7, 4, 3, 2, 5], 'cur_cost': 1069.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 3, 4, 7, 8, 2, 6, 5], 'cur_cost': 993.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [4, 7, 5, 0, 8, 3, 6, 1, 2], 'cur_cost': 920.0, 'intermediate_solutions': [{'tour': [4, 6, 0, 7, 2, 8, 3, 5, 1], 'cur_cost': 973.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 6, 0, 3, 8, 2, 4, 5, 1], 'cur_cost': 968.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 6, 0, 4, 8, 3, 5, 1, 2], 'cur_cost': 932.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [6, 3, 7, 8, 4, 2, 0, 1, 5], 'cur_cost': 802.0, 'intermediate_solutions': [{'tour': [1, 8, 7, 5, 3, 0, 6, 4, 2], 'cur_cost': 958.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 0, 5, 3, 7, 6, 2, 4], 'cur_cost': 995.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 0, 5, 3, 7, 6, 4, 2, 1], 'cur_cost': 986.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 0, 8, 3, 6, 4, 5, 2, 1]), 'cur_cost': np.float64(1126.0), 'intermediate_solutions': [{'tour': array([0, 6, 8, 4, 3, 1, 7, 2, 5]), 'cur_cost': np.float64(1174.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 0, 6, 8, 3, 1, 7, 2, 5]), 'cur_cost': np.float64(1135.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 4, 0, 6, 8, 1, 7, 2, 5]), 'cur_cost': np.float64(1109.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 4, 0, 6, 3, 1, 7, 2, 5]), 'cur_cost': np.float64(1069.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 3, 4, 0, 6, 1, 7, 2, 5]), 'cur_cost': np.float64(1027.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [2, 3, 0, 1, 8, 7, 5, 6, 4], 'cur_cost': 923.0, 'intermediate_solutions': [{'tour': [0, 1, 8, 3, 7, 6, 4, 2, 5], 'cur_cost': 943.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 6, 1, 7, 8, 4, 2, 5], 'cur_cost': 1005.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 3, 6, 7, 8, 4, 2, 5], 'cur_cost': 926.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 6, 8, 7, 0, 2, 5, 1, 4]), 'cur_cost': np.float64(1104.0), 'intermediate_solutions': [{'tour': array([2, 8, 6, 3, 4, 1, 7, 5, 0]), 'cur_cost': np.float64(1019.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 2, 8, 6, 4, 1, 7, 5, 0]), 'cur_cost': np.float64(1093.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 3, 2, 8, 6, 1, 7, 5, 0]), 'cur_cost': np.float64(1038.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 3, 2, 8, 4, 1, 7, 5, 0]), 'cur_cost': np.float64(972.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 4, 3, 2, 8, 1, 7, 5, 0]), 'cur_cost': np.float64(1116.0), 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-04 17:17:32,285 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:17:32,285 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:17:32,286 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=744.000, 多样性=0.867
2025-08-04 17:17:32,287 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-04 17:17:32,287 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-04 17:17:32,288 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:17:32,288 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.03546047375358554, 'best_improvement': 0.085995085995086}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': np.float64(0.002857142857142562)}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.010773041334436781, 'recent_improvements': [-0.02657594089842029, 0.007383317182527713, -0.005029858229546727], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.8518518518518519, 'new_diversity': 0.8518518518518519, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:17:32,289 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-04 17:17:32,291 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_solution.json
2025-08-04 17:17:32,291 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_20250804_171732.solution
