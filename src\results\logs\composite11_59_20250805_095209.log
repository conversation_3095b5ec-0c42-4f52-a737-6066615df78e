2025-08-05 09:52:09,281 - __main__ - INFO - composite11_59 开始进化第 1 代
2025-08-05 09:52:09,281 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 09:52:09,283 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:09,286 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=24845.000, 多样性=0.975
2025-08-05 09:52:09,288 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:09,291 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.975
2025-08-05 09:52:09,293 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:09,295 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 09:52:09,295 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:09,295 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 09:52:09,295 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 09:52:09,314 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -19729.760, 聚类评分: 0.000, 覆盖率: 0.132, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:09,314 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 09:52:09,315 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 09:52:09,315 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite11_59
2025-08-05 09:52:09,319 - visualization.landscape_visualizer - INFO - 插值约束: 28 个点被约束到最小值 24845.00
2025-08-05 09:52:09,431 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite11_59\landscape_composite11_59_iter_111_20250805_095209.html
2025-08-05 09:52:09,481 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite11_59\dashboard_composite11_59_iter_111_20250805_095209.html
2025-08-05 09:52:09,481 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 111
2025-08-05 09:52:09,482 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 09:52:09,482 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1867秒
2025-08-05 09:52:09,482 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 222, 'max_size': 500, 'hits': 0, 'misses': 222, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 728, 'misses': 398, 'hit_rate': 0.6465364120781527, 'evictions': 298, 'ttl': 7200}}
2025-08-05 09:52:09,482 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -19729.760000000002, 'local_optima_density': 0.1, 'gradient_variance': 12202156989.430399, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.132, 'fitness_entropy': 0.9372305632161296, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -19729.760)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.132)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358729.3146827, 'performance_metrics': {}}}
2025-08-05 09:52:09,482 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:09,482 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:09,482 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:09,482 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:09,483 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:52:09,483 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 09:52:09,483 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:52:09,483 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:09,484 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:09,484 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:52:09,484 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:09,484 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:09,484 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:09,484 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:09,484 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:09,484 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:09,486 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 59
2025-08-05 09:52:09,486 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,487 - ExplorationExpert - INFO - 探索路径生成完成，成本: 236414.0, 路径长度: 59, 收集中间解: 0
2025-08-05 09:52:09,487 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [2, 12, 3, 32, 28, 5, 21, 9, 40, 42, 20, 8, 25, 49, 47, 10, 15, 58, 17, 26, 1, 37, 54, 6, 16, 36, 57, 11, 27, 29, 45, 22, 19, 14, 44, 24, 39, 38, 31, 41, 13, 50, 55, 30, 33, 51, 48, 53, 23, 18, 4, 56, 7, 46, 43, 34, 0, 35, 52], 'cur_cost': 236414.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:09,487 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 236414.00)
2025-08-05 09:52:09,487 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:09,487 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:09,487 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:09,489 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 09:52:09,489 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,490 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32452.0, 路径长度: 59, 收集中间解: 0
2025-08-05 09:52:09,490 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 19, 1, 6, 4, 9, 3, 5, 8, 7, 10, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21], 'cur_cost': 32452.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:09,490 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 32452.00)
2025-08-05 09:52:09,490 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:09,491 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:09,491 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:09,493 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 09:52:09,493 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,493 - ExplorationExpert - INFO - 探索路径生成完成，成本: 34579.0, 路径长度: 59, 收集中间解: 0
2025-08-05 09:52:09,494 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 15, 6, 13, 18, 22, 14, 20, 16, 17, 21, 12, 11, 19, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34579.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:09,494 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 34579.00)
2025-08-05 09:52:09,494 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:52:09,494 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:09,494 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:09,494 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 233123.0
2025-08-05 09:52:09,503 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 09:52:09,503 - ExploitationExpert - INFO - res_population_costs: [24529.0, 24511, 24495, 24495, 24482]
2025-08-05 09:52:09,503 - ExploitationExpert - INFO - res_populations: [array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 46, 35, 44, 37,
       36, 42, 45, 41, 38, 43,  2,  3], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 15, 17, 12, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39, 40, 42, 45,
       41, 38, 36, 37, 43, 44,  2,  3], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 15, 17, 12, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41, 38,
       36, 43, 37, 44, 46, 35,  2,  3], dtype=int64), array([ 0, 10,  6,  1,  7,  4,  9,  3,  5,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 17, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  9,  4,  1,  6, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26,
       28, 39, 40, 42, 45, 41, 38, 36, 43, 37, 44, 46, 35, 54, 50, 53, 48,
       58, 52, 56, 47, 55, 51, 57, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11,
       12, 21, 19,  7,  8,  5,  3,  2], dtype=int64)]
2025-08-05 09:52:09,505 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:09,505 - ExploitationExpert - INFO - populations: [{'tour': [2, 12, 3, 32, 28, 5, 21, 9, 40, 42, 20, 8, 25, 49, 47, 10, 15, 58, 17, 26, 1, 37, 54, 6, 16, 36, 57, 11, 27, 29, 45, 22, 19, 14, 44, 24, 39, 38, 31, 41, 13, 50, 55, 30, 33, 51, 48, 53, 23, 18, 4, 56, 7, 46, 43, 34, 0, 35, 52], 'cur_cost': 236414.0}, {'tour': [0, 19, 1, 6, 4, 9, 3, 5, 8, 7, 10, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21], 'cur_cost': 32452.0}, {'tour': [0, 15, 6, 13, 18, 22, 14, 20, 16, 17, 21, 12, 11, 19, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34579.0}, {'tour': array([29, 58,  4, 54, 31, 42, 56, 15, 53, 49, 36, 25, 57,  6, 26, 50, 52,
       35, 27, 44,  3,  2, 10,  8, 37, 40, 39, 34, 47, 19, 28, 45, 41, 30,
       33, 48, 55, 21, 32, 51,  5,  9, 17, 46,  1, 13,  7, 23, 11, 18, 20,
        0, 12, 16, 24, 38, 14, 22, 43], dtype=int64), 'cur_cost': 233123.0}, {'tour': array([58, 51, 19, 32,  7, 30,  6, 13,  5, 48, 56, 12, 20, 46, 15, 25,  8,
        9, 47, 52, 43, 17, 18, 28, 29,  2,  3, 45, 37, 42, 36, 11, 35, 49,
       26,  4, 31, 39, 10, 23, 38, 34, 54, 53, 55, 16,  1, 24, 40, 33, 41,
       57, 27, 44, 22, 14,  0, 21, 50], dtype=int64), 'cur_cost': 224393.0}, {'tour': array([46, 39, 32,  1, 31, 17,  4, 52, 25,  8, 40, 55,  6, 11,  3, 49, 18,
       10, 30, 33, 41,  9, 19, 29, 28, 27, 56, 21, 44,  0, 26, 53, 15, 12,
       16, 50, 51, 37,  5,  7, 24, 35, 58, 54, 13,  2, 38, 23, 36, 48, 43,
       14, 22, 47, 45, 57, 20, 34, 42], dtype=int64), 'cur_cost': 236866.0}, {'tour': array([37, 11, 24, 31, 22, 12,  2, 51, 23,  3, 52, 34,  1, 38, 43, 48, 17,
       46, 32, 14, 16, 28,  6, 27, 10, 21, 39, 19,  5, 20, 49,  0, 45, 55,
       47, 41, 53, 50, 15, 18,  9, 54, 40,  8, 56, 44, 58, 36, 25,  7,  4,
       57, 35, 42, 30, 33, 29, 26, 13], dtype=int64), 'cur_cost': 237198.0}, {'tour': array([52, 25, 26, 36, 12, 39, 44, 14, 55, 20, 40,  6, 24, 13, 38, 45,  5,
        7, 19, 56,  3, 58, 16,  2, 31,  1,  0, 32, 21, 34, 28, 15, 33, 22,
       18,  4, 23, 57, 54, 17, 30,  9, 48, 37, 41, 42, 46,  8, 35, 51, 43,
       49, 53, 10, 11, 29, 47, 27, 50], dtype=int64), 'cur_cost': 239742.0}, {'tour': array([ 0,  4,  6,  7, 41, 39, 37, 27, 46, 44, 29, 20, 55, 56, 48, 35, 45,
       53, 51, 57,  9, 43,  8, 31, 24, 38, 16, 42, 40, 18, 26,  5, 22,  2,
       34, 11, 58, 28, 36, 12, 32, 19, 23, 33, 17, 52, 49,  3, 30, 14, 25,
       47, 13, 54, 21,  1, 10, 15, 50], dtype=int64), 'cur_cost': 220484.0}, {'tour': array([21, 16, 45, 32, 31, 14, 57, 53, 47, 50, 42, 39, 28, 23,  2, 40, 24,
        7,  3, 27,  9, 34, 25, 12, 55,  5, 37, 44, 52,  1, 18, 10, 20, 33,
        8, 26, 30, 11,  0, 17, 54, 15, 41,  4, 56, 13, 46, 58,  6, 48, 51,
       29, 35, 36, 43, 19, 38, 49, 22], dtype=int64), 'cur_cost': 217570.0}]
2025-08-05 09:52:09,508 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:09,508 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 287, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 287, 'cache_hits': 0, 'similarity_calculations': 1434, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:09,509 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([29, 58,  4, 54, 31, 42, 56, 15, 53, 49, 36, 25, 57,  6, 26, 50, 52,
       35, 27, 44,  3,  2, 10,  8, 37, 40, 39, 34, 47, 19, 28, 45, 41, 30,
       33, 48, 55, 21, 32, 51,  5,  9, 17, 46,  1, 13,  7, 23, 11, 18, 20,
        0, 12, 16, 24, 38, 14, 22, 43], dtype=int64), 'cur_cost': 233123.0, 'intermediate_solutions': [{'tour': array([33,  3,  5, 34,  8,  4, 43, 55, 51, 10, 53, 48, 24, 54, 28, 38, 35,
       57, 31,  9, 49, 46, 40,  1, 39, 22, 27, 18, 17, 37, 30, 29, 11,  0,
        6, 12, 25, 56, 32, 23, 58, 21, 13, 20, 41,  2, 52, 19, 50, 42, 26,
       44, 14, 45,  7, 16, 15, 47, 36], dtype=int64), 'cur_cost': 247110.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([34, 33,  3,  5,  8,  4, 43, 55, 51, 10, 53, 48, 24, 54, 28, 38, 35,
       57, 31,  9, 49, 46, 40,  1, 39, 22, 27, 18, 17, 37, 30, 29, 11,  0,
        6, 12, 25, 56, 32, 23, 58, 21, 13, 20, 41,  2, 52, 19, 50, 42, 26,
       44, 14, 45,  7, 16, 15, 47, 36], dtype=int64), 'cur_cost': 240297.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 8, 34, 33,  3,  5,  4, 43, 55, 51, 10, 53, 48, 24, 54, 28, 38, 35,
       57, 31,  9, 49, 46, 40,  1, 39, 22, 27, 18, 17, 37, 30, 29, 11,  0,
        6, 12, 25, 56, 32, 23, 58, 21, 13, 20, 41,  2, 52, 19, 50, 42, 26,
       44, 14, 45,  7, 16, 15, 47, 36], dtype=int64), 'cur_cost': 242677.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 5, 34, 33,  3,  8,  4, 43, 55, 51, 10, 53, 48, 24, 54, 28, 38, 35,
       57, 31,  9, 49, 46, 40,  1, 39, 22, 27, 18, 17, 37, 30, 29, 11,  0,
        6, 12, 25, 56, 32, 23, 58, 21, 13, 20, 41,  2, 52, 19, 50, 42, 26,
       44, 14, 45,  7, 16, 15, 47, 36], dtype=int64), 'cur_cost': 242665.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 5,  8, 34, 33,  3,  4, 43, 55, 51, 10, 53, 48, 24, 54, 28, 38, 35,
       57, 31,  9, 49, 46, 40,  1, 39, 22, 27, 18, 17, 37, 30, 29, 11,  0,
        6, 12, 25, 56, 32, 23, 58, 21, 13, 20, 41,  2, 52, 19, 50, 42, 26,
       44, 14, 45,  7, 16, 15, 47, 36], dtype=int64), 'cur_cost': 242661.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:09,509 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 233123.00)
2025-08-05 09:52:09,509 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:09,509 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:09,509 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:09,511 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 59
2025-08-05 09:52:09,511 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,511 - ExplorationExpert - INFO - 探索路径生成完成，成本: 225103.0, 路径长度: 59, 收集中间解: 0
2025-08-05 09:52:09,512 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [19, 1, 13, 4, 9, 21, 5, 40, 17, 20, 2, 25, 49, 47, 7, 15, 58, 24, 27, 37, 23, 6, 35, 36, 11, 43, 45, 22, 34, 38, 39, 46, 48, 31, 41, 56, 50, 55, 30, 33, 51, 16, 12, 52, 57, 32, 28, 54, 10, 18, 42, 3, 44, 26, 29, 8, 53, 0, 14], 'cur_cost': 225103.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:09,512 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 225103.00)
2025-08-05 09:52:09,512 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:09,512 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:09,512 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:09,514 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 09:52:09,514 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,514 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32428.0, 路径长度: 59, 收集中间解: 0
2025-08-05 09:52:09,514 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 5, 12, 1, 7, 8, 4, 9, 3, 2, 10, 6, 33, 31, 27, 34, 23, 25, 32, 29, 30, 26, 28, 24, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 21, 19], 'cur_cost': 32428.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:09,515 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 32428.00)
2025-08-05 09:52:09,515 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:09,515 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:09,515 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:09,517 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 09:52:09,517 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,517 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27112.0, 路径长度: 59, 收集中间解: 0
2025-08-05 09:52:09,517 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 7, 14, 18, 13, 22, 21, 12, 11, 15, 17, 16, 20, 19, 6, 1, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27112.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:09,517 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 27112.00)
2025-08-05 09:52:09,517 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 09:52:09,518 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:09,518 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:09,518 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 269832.0
2025-08-05 09:52:09,525 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 09:52:09,525 - ExploitationExpert - INFO - res_population_costs: [24529.0, 24511, 24495, 24495, 24482, 24482]
2025-08-05 09:52:09,525 - ExploitationExpert - INFO - res_populations: [array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 46, 35, 44, 37,
       36, 42, 45, 41, 38, 43,  2,  3], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 15, 17, 12, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39, 40, 42, 45,
       41, 38, 36, 37, 43, 44,  2,  3], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 15, 17, 12, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41, 38,
       36, 43, 37, 44, 46, 35,  2,  3], dtype=int64), array([ 0, 10,  6,  1,  7,  4,  9,  3,  5,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 17, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  9,  4,  1,  6, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26,
       28, 39, 40, 42, 45, 41, 38, 36, 43, 37, 44, 46, 35, 54, 50, 53, 48,
       58, 52, 56, 47, 55, 51, 57, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11,
       12, 21, 19,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39,
       40, 46, 35, 44, 37, 36, 42, 45, 41, 38, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64)]
2025-08-05 09:52:09,528 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:09,528 - ExploitationExpert - INFO - populations: [{'tour': [2, 12, 3, 32, 28, 5, 21, 9, 40, 42, 20, 8, 25, 49, 47, 10, 15, 58, 17, 26, 1, 37, 54, 6, 16, 36, 57, 11, 27, 29, 45, 22, 19, 14, 44, 24, 39, 38, 31, 41, 13, 50, 55, 30, 33, 51, 48, 53, 23, 18, 4, 56, 7, 46, 43, 34, 0, 35, 52], 'cur_cost': 236414.0}, {'tour': [0, 19, 1, 6, 4, 9, 3, 5, 8, 7, 10, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21], 'cur_cost': 32452.0}, {'tour': [0, 15, 6, 13, 18, 22, 14, 20, 16, 17, 21, 12, 11, 19, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34579.0}, {'tour': array([29, 58,  4, 54, 31, 42, 56, 15, 53, 49, 36, 25, 57,  6, 26, 50, 52,
       35, 27, 44,  3,  2, 10,  8, 37, 40, 39, 34, 47, 19, 28, 45, 41, 30,
       33, 48, 55, 21, 32, 51,  5,  9, 17, 46,  1, 13,  7, 23, 11, 18, 20,
        0, 12, 16, 24, 38, 14, 22, 43], dtype=int64), 'cur_cost': 233123.0}, {'tour': [19, 1, 13, 4, 9, 21, 5, 40, 17, 20, 2, 25, 49, 47, 7, 15, 58, 24, 27, 37, 23, 6, 35, 36, 11, 43, 45, 22, 34, 38, 39, 46, 48, 31, 41, 56, 50, 55, 30, 33, 51, 16, 12, 52, 57, 32, 28, 54, 10, 18, 42, 3, 44, 26, 29, 8, 53, 0, 14], 'cur_cost': 225103.0}, {'tour': [0, 5, 12, 1, 7, 8, 4, 9, 3, 2, 10, 6, 33, 31, 27, 34, 23, 25, 32, 29, 30, 26, 28, 24, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 21, 19], 'cur_cost': 32428.0}, {'tour': [0, 7, 14, 18, 13, 22, 21, 12, 11, 15, 17, 16, 20, 19, 6, 1, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27112.0}, {'tour': array([32, 37, 12, 24, 26,  7, 17, 41, 56, 43, 19, 35, 53,  6, 36, 13,  4,
       42, 22, 52, 20,  1, 21, 58, 16, 27, 30, 55, 39, 38, 48, 10, 14,  9,
        2, 15, 54, 23, 34, 57, 50, 31, 49,  5, 18, 46, 47, 25, 33,  0, 51,
       40, 28,  3,  8, 44, 45, 29, 11], dtype=int64), 'cur_cost': 269832.0}, {'tour': array([ 0,  4,  6,  7, 41, 39, 37, 27, 46, 44, 29, 20, 55, 56, 48, 35, 45,
       53, 51, 57,  9, 43,  8, 31, 24, 38, 16, 42, 40, 18, 26,  5, 22,  2,
       34, 11, 58, 28, 36, 12, 32, 19, 23, 33, 17, 52, 49,  3, 30, 14, 25,
       47, 13, 54, 21,  1, 10, 15, 50], dtype=int64), 'cur_cost': 220484.0}, {'tour': array([21, 16, 45, 32, 31, 14, 57, 53, 47, 50, 42, 39, 28, 23,  2, 40, 24,
        7,  3, 27,  9, 34, 25, 12, 55,  5, 37, 44, 52,  1, 18, 10, 20, 33,
        8, 26, 30, 11,  0, 17, 54, 15, 41,  4, 56, 13, 46, 58,  6, 48, 51,
       29, 35, 36, 43, 19, 38, 49, 22], dtype=int64), 'cur_cost': 217570.0}]
2025-08-05 09:52:09,529 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:09,529 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 288, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 288, 'cache_hits': 0, 'similarity_calculations': 1435, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:09,530 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([32, 37, 12, 24, 26,  7, 17, 41, 56, 43, 19, 35, 53,  6, 36, 13,  4,
       42, 22, 52, 20,  1, 21, 58, 16, 27, 30, 55, 39, 38, 48, 10, 14,  9,
        2, 15, 54, 23, 34, 57, 50, 31, 49,  5, 18, 46, 47, 25, 33,  0, 51,
       40, 28,  3,  8, 44, 45, 29, 11], dtype=int64), 'cur_cost': 269832.0, 'intermediate_solutions': [{'tour': array([26, 25, 52, 36, 12, 39, 44, 14, 55, 20, 40,  6, 24, 13, 38, 45,  5,
        7, 19, 56,  3, 58, 16,  2, 31,  1,  0, 32, 21, 34, 28, 15, 33, 22,
       18,  4, 23, 57, 54, 17, 30,  9, 48, 37, 41, 42, 46,  8, 35, 51, 43,
       49, 53, 10, 11, 29, 47, 27, 50], dtype=int64), 'cur_cost': 247697.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([36, 26, 25, 52, 12, 39, 44, 14, 55, 20, 40,  6, 24, 13, 38, 45,  5,
        7, 19, 56,  3, 58, 16,  2, 31,  1,  0, 32, 21, 34, 28, 15, 33, 22,
       18,  4, 23, 57, 54, 17, 30,  9, 48, 37, 41, 42, 46,  8, 35, 51, 43,
       49, 53, 10, 11, 29, 47, 27, 50], dtype=int64), 'cur_cost': 243237.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([12, 36, 26, 25, 52, 39, 44, 14, 55, 20, 40,  6, 24, 13, 38, 45,  5,
        7, 19, 56,  3, 58, 16,  2, 31,  1,  0, 32, 21, 34, 28, 15, 33, 22,
       18,  4, 23, 57, 54, 17, 30,  9, 48, 37, 41, 42, 46,  8, 35, 51, 43,
       49, 53, 10, 11, 29, 47, 27, 50], dtype=int64), 'cur_cost': 243293.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([52, 36, 26, 25, 12, 39, 44, 14, 55, 20, 40,  6, 24, 13, 38, 45,  5,
        7, 19, 56,  3, 58, 16,  2, 31,  1,  0, 32, 21, 34, 28, 15, 33, 22,
       18,  4, 23, 57, 54, 17, 30,  9, 48, 37, 41, 42, 46,  8, 35, 51, 43,
       49, 53, 10, 11, 29, 47, 27, 50], dtype=int64), 'cur_cost': 235432.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([52, 12, 36, 26, 25, 39, 44, 14, 55, 20, 40,  6, 24, 13, 38, 45,  5,
        7, 19, 56,  3, 58, 16,  2, 31,  1,  0, 32, 21, 34, 28, 15, 33, 22,
       18,  4, 23, 57, 54, 17, 30,  9, 48, 37, 41, 42, 46,  8, 35, 51, 43,
       49, 53, 10, 11, 29, 47, 27, 50], dtype=int64), 'cur_cost': 235232.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:09,531 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 269832.00)
2025-08-05 09:52:09,531 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:52:09,531 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:52:09,531 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:09,533 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 09:52:09,534 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,534 - ExplorationExpert - INFO - 探索路径生成完成，成本: 34667.0, 路径长度: 59, 收集中间解: 0
2025-08-05 09:52:09,534 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 21, 4, 15, 17, 16, 11, 12, 19, 18, 13, 22, 14, 20, 6, 1, 7, 8, 5, 3, 2, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34667.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:09,534 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 34667.00)
2025-08-05 09:52:09,534 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:09,534 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:09,535 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:09,540 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 59
2025-08-05 09:52:09,540 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,540 - ExplorationExpert - INFO - 探索路径生成完成，成本: 147519.0, 路径长度: 59, 收集中间解: 0
2025-08-05 09:52:09,540 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [44, 32, 4, 18, 34, 7, 23, 33, 3, 12, 2, 11, 20, 9, 5, 21, 31, 46, 42, 25, 6, 24, 28, 45, 30, 8, 19, 16, 17, 10, 27, 1, 13, 57, 53, 54, 14, 29, 26, 36, 40, 39, 41, 43, 35, 58, 55, 51, 50, 49, 47, 56, 0, 15, 22, 52, 48, 37, 38], 'cur_cost': 147519.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:09,541 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 147519.00)
2025-08-05 09:52:09,541 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:09,541 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:09,543 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [2, 12, 3, 32, 28, 5, 21, 9, 40, 42, 20, 8, 25, 49, 47, 10, 15, 58, 17, 26, 1, 37, 54, 6, 16, 36, 57, 11, 27, 29, 45, 22, 19, 14, 44, 24, 39, 38, 31, 41, 13, 50, 55, 30, 33, 51, 48, 53, 23, 18, 4, 56, 7, 46, 43, 34, 0, 35, 52], 'cur_cost': 236414.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 1, 6, 4, 9, 3, 5, 8, 7, 10, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21], 'cur_cost': 32452.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 6, 13, 18, 22, 14, 20, 16, 17, 21, 12, 11, 19, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34579.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([29, 58,  4, 54, 31, 42, 56, 15, 53, 49, 36, 25, 57,  6, 26, 50, 52,
       35, 27, 44,  3,  2, 10,  8, 37, 40, 39, 34, 47, 19, 28, 45, 41, 30,
       33, 48, 55, 21, 32, 51,  5,  9, 17, 46,  1, 13,  7, 23, 11, 18, 20,
        0, 12, 16, 24, 38, 14, 22, 43], dtype=int64), 'cur_cost': 233123.0, 'intermediate_solutions': [{'tour': array([33,  3,  5, 34,  8,  4, 43, 55, 51, 10, 53, 48, 24, 54, 28, 38, 35,
       57, 31,  9, 49, 46, 40,  1, 39, 22, 27, 18, 17, 37, 30, 29, 11,  0,
        6, 12, 25, 56, 32, 23, 58, 21, 13, 20, 41,  2, 52, 19, 50, 42, 26,
       44, 14, 45,  7, 16, 15, 47, 36], dtype=int64), 'cur_cost': 247110.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([34, 33,  3,  5,  8,  4, 43, 55, 51, 10, 53, 48, 24, 54, 28, 38, 35,
       57, 31,  9, 49, 46, 40,  1, 39, 22, 27, 18, 17, 37, 30, 29, 11,  0,
        6, 12, 25, 56, 32, 23, 58, 21, 13, 20, 41,  2, 52, 19, 50, 42, 26,
       44, 14, 45,  7, 16, 15, 47, 36], dtype=int64), 'cur_cost': 240297.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 8, 34, 33,  3,  5,  4, 43, 55, 51, 10, 53, 48, 24, 54, 28, 38, 35,
       57, 31,  9, 49, 46, 40,  1, 39, 22, 27, 18, 17, 37, 30, 29, 11,  0,
        6, 12, 25, 56, 32, 23, 58, 21, 13, 20, 41,  2, 52, 19, 50, 42, 26,
       44, 14, 45,  7, 16, 15, 47, 36], dtype=int64), 'cur_cost': 242677.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 5, 34, 33,  3,  8,  4, 43, 55, 51, 10, 53, 48, 24, 54, 28, 38, 35,
       57, 31,  9, 49, 46, 40,  1, 39, 22, 27, 18, 17, 37, 30, 29, 11,  0,
        6, 12, 25, 56, 32, 23, 58, 21, 13, 20, 41,  2, 52, 19, 50, 42, 26,
       44, 14, 45,  7, 16, 15, 47, 36], dtype=int64), 'cur_cost': 242665.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 5,  8, 34, 33,  3,  4, 43, 55, 51, 10, 53, 48, 24, 54, 28, 38, 35,
       57, 31,  9, 49, 46, 40,  1, 39, 22, 27, 18, 17, 37, 30, 29, 11,  0,
        6, 12, 25, 56, 32, 23, 58, 21, 13, 20, 41,  2, 52, 19, 50, 42, 26,
       44, 14, 45,  7, 16, 15, 47, 36], dtype=int64), 'cur_cost': 242661.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [19, 1, 13, 4, 9, 21, 5, 40, 17, 20, 2, 25, 49, 47, 7, 15, 58, 24, 27, 37, 23, 6, 35, 36, 11, 43, 45, 22, 34, 38, 39, 46, 48, 31, 41, 56, 50, 55, 30, 33, 51, 16, 12, 52, 57, 32, 28, 54, 10, 18, 42, 3, 44, 26, 29, 8, 53, 0, 14], 'cur_cost': 225103.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 1, 7, 8, 4, 9, 3, 2, 10, 6, 33, 31, 27, 34, 23, 25, 32, 29, 30, 26, 28, 24, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 21, 19], 'cur_cost': 32428.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 14, 18, 13, 22, 21, 12, 11, 15, 17, 16, 20, 19, 6, 1, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27112.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([32, 37, 12, 24, 26,  7, 17, 41, 56, 43, 19, 35, 53,  6, 36, 13,  4,
       42, 22, 52, 20,  1, 21, 58, 16, 27, 30, 55, 39, 38, 48, 10, 14,  9,
        2, 15, 54, 23, 34, 57, 50, 31, 49,  5, 18, 46, 47, 25, 33,  0, 51,
       40, 28,  3,  8, 44, 45, 29, 11], dtype=int64), 'cur_cost': 269832.0, 'intermediate_solutions': [{'tour': array([26, 25, 52, 36, 12, 39, 44, 14, 55, 20, 40,  6, 24, 13, 38, 45,  5,
        7, 19, 56,  3, 58, 16,  2, 31,  1,  0, 32, 21, 34, 28, 15, 33, 22,
       18,  4, 23, 57, 54, 17, 30,  9, 48, 37, 41, 42, 46,  8, 35, 51, 43,
       49, 53, 10, 11, 29, 47, 27, 50], dtype=int64), 'cur_cost': 247697.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([36, 26, 25, 52, 12, 39, 44, 14, 55, 20, 40,  6, 24, 13, 38, 45,  5,
        7, 19, 56,  3, 58, 16,  2, 31,  1,  0, 32, 21, 34, 28, 15, 33, 22,
       18,  4, 23, 57, 54, 17, 30,  9, 48, 37, 41, 42, 46,  8, 35, 51, 43,
       49, 53, 10, 11, 29, 47, 27, 50], dtype=int64), 'cur_cost': 243237.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([12, 36, 26, 25, 52, 39, 44, 14, 55, 20, 40,  6, 24, 13, 38, 45,  5,
        7, 19, 56,  3, 58, 16,  2, 31,  1,  0, 32, 21, 34, 28, 15, 33, 22,
       18,  4, 23, 57, 54, 17, 30,  9, 48, 37, 41, 42, 46,  8, 35, 51, 43,
       49, 53, 10, 11, 29, 47, 27, 50], dtype=int64), 'cur_cost': 243293.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([52, 36, 26, 25, 12, 39, 44, 14, 55, 20, 40,  6, 24, 13, 38, 45,  5,
        7, 19, 56,  3, 58, 16,  2, 31,  1,  0, 32, 21, 34, 28, 15, 33, 22,
       18,  4, 23, 57, 54, 17, 30,  9, 48, 37, 41, 42, 46,  8, 35, 51, 43,
       49, 53, 10, 11, 29, 47, 27, 50], dtype=int64), 'cur_cost': 235432.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([52, 12, 36, 26, 25, 39, 44, 14, 55, 20, 40,  6, 24, 13, 38, 45,  5,
        7, 19, 56,  3, 58, 16,  2, 31,  1,  0, 32, 21, 34, 28, 15, 33, 22,
       18,  4, 23, 57, 54, 17, 30,  9, 48, 37, 41, 42, 46,  8, 35, 51, 43,
       49, 53, 10, 11, 29, 47, 27, 50], dtype=int64), 'cur_cost': 235232.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 4, 15, 17, 16, 11, 12, 19, 18, 13, 22, 14, 20, 6, 1, 7, 8, 5, 3, 2, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34667.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [44, 32, 4, 18, 34, 7, 23, 33, 3, 12, 2, 11, 20, 9, 5, 21, 31, 46, 42, 25, 6, 24, 28, 45, 30, 8, 19, 16, 17, 10, 27, 1, 13, 57, 53, 54, 14, 29, 26, 36, 40, 39, 41, 43, 35, 58, 55, 51, 50, 49, 47, 56, 0, 15, 22, 52, 48, 37, 38], 'cur_cost': 147519.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:09,543 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:09,543 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:09,546 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=27112.000, 多样性=0.921
2025-08-05 09:52:09,547 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 09:52:09,547 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 09:52:09,547 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:09,547 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.015298059344662185, 'best_improvement': -0.09124572348561079}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.05600617999227503}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.19301304705313385, 'recent_improvements': [-0.37142532680957613, 0.23324410872078835, 0.014600767296691623], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 6, 'new_count': 6, 'count_change': 0, 'old_best_cost': 24482, 'new_best_cost': 24482, 'quality_improvement': 0.0, 'old_diversity': 0.8259887005649718, 'new_diversity': 0.8259887005649718, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 09:52:09,548 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 09:52:09,548 - __main__ - INFO - composite11_59 开始进化第 2 代
2025-08-05 09:52:09,548 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 09:52:09,548 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:09,549 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=27112.000, 多样性=0.921
2025-08-05 09:52:09,550 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:09,551 - PathExpert - INFO - 路径结构分析完成: 公共边数量=5, 路径相似性=0.921
2025-08-05 09:52:09,552 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:09,554 - EliteExpert - INFO - 精英解分析完成: 精英解数量=6, 多样性=0.826
2025-08-05 09:52:09,556 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 09:52:09,556 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:09,556 - LandscapeExpert - INFO - 添加精英解数据: 6个精英解
2025-08-05 09:52:09,556 - LandscapeExpert - INFO - 数据提取成功: 16个路径, 16个适应度值
2025-08-05 09:52:09,595 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.312, 适应度梯度: -4908.838, 聚类评分: 0.000, 覆盖率: 0.133, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:09,595 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 09:52:09,595 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:09,595 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite11_59
2025-08-05 09:52:09,600 - visualization.landscape_visualizer - INFO - 插值约束: 25 个点被约束到最小值 24482.00
2025-08-05 09:52:09,695 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite11_59\landscape_composite11_59_iter_112_20250805_095209.html
2025-08-05 09:52:09,786 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite11_59\dashboard_composite11_59_iter_112_20250805_095209.html
2025-08-05 09:52:09,787 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 112
2025-08-05 09:52:09,787 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 09:52:09,787 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2302秒
2025-08-05 09:52:09,787 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3125, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -4908.837500000001, 'local_optima_density': 0.3125, 'gradient_variance': 11442968111.081093, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1333, 'fitness_entropy': 0.6622301466508207, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -4908.838)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.133)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358729.5955577, 'performance_metrics': {}}}
2025-08-05 09:52:09,787 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:09,787 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:09,788 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:09,788 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:09,788 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:52:09,788 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 09:52:09,788 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:52:09,789 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:09,789 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:09,789 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:52:09,789 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:09,789 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:09,789 - experts.management.collaboration_manager - INFO - 识别精英个体: {5, 6} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:09,789 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-05 09:52:09,789 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:09,790 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:09,790 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 249636.0
2025-08-05 09:52:09,804 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:09,805 - ExploitationExpert - INFO - res_population_costs: [24482, 24482, 24495, 24495, 24511, 24529.0, 24473]
2025-08-05 09:52:09,805 - ExploitationExpert - INFO - res_populations: [array([ 0,  9,  4,  1,  6, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26,
       28, 39, 40, 42, 45, 41, 38, 36, 43, 37, 44, 46, 35, 54, 50, 53, 48,
       58, 52, 56, 47, 55, 51, 57, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11,
       12, 21, 19,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39,
       40, 46, 35, 44, 37, 36, 42, 45, 41, 38, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 15, 17, 12, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41, 38,
       36, 43, 37, 44, 46, 35,  2,  3], dtype=int64), array([ 0, 10,  6,  1,  7,  4,  9,  3,  5,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 17, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 15, 17, 12, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39, 40, 42, 45,
       41, 38, 36, 37, 43, 44,  2,  3], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 46, 35, 44, 37,
       36, 42, 45, 41, 38, 43,  2,  3], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 53, 48, 58, 52, 50, 54,
       49, 57, 56, 47, 55, 51, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64)]
2025-08-05 09:52:09,809 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:09,810 - ExploitationExpert - INFO - populations: [{'tour': array([14, 47, 55, 24, 45, 40,  6, 49, 31, 21, 15,  5, 30,  7, 12, 32, 42,
       54, 27, 25, 57, 23,  2,  8, 36, 51, 53, 16,  4, 58, 20, 34, 44,  9,
       56, 10, 33, 41, 50, 52, 39, 26, 28, 43, 19, 37, 29, 11, 22,  1, 18,
       13,  0, 35, 48, 46, 17, 38,  3], dtype=int64), 'cur_cost': 249636.0}, {'tour': [0, 19, 1, 6, 4, 9, 3, 5, 8, 7, 10, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21], 'cur_cost': 32452.0}, {'tour': [0, 15, 6, 13, 18, 22, 14, 20, 16, 17, 21, 12, 11, 19, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34579.0}, {'tour': [29, 58, 4, 54, 31, 42, 56, 15, 53, 49, 36, 25, 57, 6, 26, 50, 52, 35, 27, 44, 3, 2, 10, 8, 37, 40, 39, 34, 47, 19, 28, 45, 41, 30, 33, 48, 55, 21, 32, 51, 5, 9, 17, 46, 1, 13, 7, 23, 11, 18, 20, 0, 12, 16, 24, 38, 14, 22, 43], 'cur_cost': 233123.0}, {'tour': [19, 1, 13, 4, 9, 21, 5, 40, 17, 20, 2, 25, 49, 47, 7, 15, 58, 24, 27, 37, 23, 6, 35, 36, 11, 43, 45, 22, 34, 38, 39, 46, 48, 31, 41, 56, 50, 55, 30, 33, 51, 16, 12, 52, 57, 32, 28, 54, 10, 18, 42, 3, 44, 26, 29, 8, 53, 0, 14], 'cur_cost': 225103.0}, {'tour': [0, 5, 12, 1, 7, 8, 4, 9, 3, 2, 10, 6, 33, 31, 27, 34, 23, 25, 32, 29, 30, 26, 28, 24, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 21, 19], 'cur_cost': 32428.0}, {'tour': [0, 7, 14, 18, 13, 22, 21, 12, 11, 15, 17, 16, 20, 19, 6, 1, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27112.0}, {'tour': [32, 37, 12, 24, 26, 7, 17, 41, 56, 43, 19, 35, 53, 6, 36, 13, 4, 42, 22, 52, 20, 1, 21, 58, 16, 27, 30, 55, 39, 38, 48, 10, 14, 9, 2, 15, 54, 23, 34, 57, 50, 31, 49, 5, 18, 46, 47, 25, 33, 0, 51, 40, 28, 3, 8, 44, 45, 29, 11], 'cur_cost': 269832.0}, {'tour': [0, 21, 4, 15, 17, 16, 11, 12, 19, 18, 13, 22, 14, 20, 6, 1, 7, 8, 5, 3, 2, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34667.0}, {'tour': [44, 32, 4, 18, 34, 7, 23, 33, 3, 12, 2, 11, 20, 9, 5, 21, 31, 46, 42, 25, 6, 24, 28, 45, 30, 8, 19, 16, 17, 10, 27, 1, 13, 57, 53, 54, 14, 29, 26, 36, 40, 39, 41, 43, 35, 58, 55, 51, 50, 49, 47, 56, 0, 15, 22, 52, 48, 37, 38], 'cur_cost': 147519.0}]
2025-08-05 09:52:09,811 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:09,811 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 289, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 289, 'cache_hits': 0, 'similarity_calculations': 1437, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:09,812 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([14, 47, 55, 24, 45, 40,  6, 49, 31, 21, 15,  5, 30,  7, 12, 32, 42,
       54, 27, 25, 57, 23,  2,  8, 36, 51, 53, 16,  4, 58, 20, 34, 44,  9,
       56, 10, 33, 41, 50, 52, 39, 26, 28, 43, 19, 37, 29, 11, 22,  1, 18,
       13,  0, 35, 48, 46, 17, 38,  3], dtype=int64), 'cur_cost': 249636.0, 'intermediate_solutions': [{'tour': array([ 3, 12,  2, 32, 28,  5, 21,  9, 40, 42, 20,  8, 25, 49, 47, 10, 15,
       58, 17, 26,  1, 37, 54,  6, 16, 36, 57, 11, 27, 29, 45, 22, 19, 14,
       44, 24, 39, 38, 31, 41, 13, 50, 55, 30, 33, 51, 48, 53, 23, 18,  4,
       56,  7, 46, 43, 34,  0, 35, 52]), 'cur_cost': 236410.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([32,  3, 12,  2, 28,  5, 21,  9, 40, 42, 20,  8, 25, 49, 47, 10, 15,
       58, 17, 26,  1, 37, 54,  6, 16, 36, 57, 11, 27, 29, 45, 22, 19, 14,
       44, 24, 39, 38, 31, 41, 13, 50, 55, 30, 33, 51, 48, 53, 23, 18,  4,
       56,  7, 46, 43, 34,  0, 35, 52]), 'cur_cost': 243219.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([28, 32,  3, 12,  2,  5, 21,  9, 40, 42, 20,  8, 25, 49, 47, 10, 15,
       58, 17, 26,  1, 37, 54,  6, 16, 36, 57, 11, 27, 29, 45, 22, 19, 14,
       44, 24, 39, 38, 31, 41, 13, 50, 55, 30, 33, 51, 48, 53, 23, 18,  4,
       56,  7, 46, 43, 34,  0, 35, 52]), 'cur_cost': 236426.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2, 32,  3, 12, 28,  5, 21,  9, 40, 42, 20,  8, 25, 49, 47, 10, 15,
       58, 17, 26,  1, 37, 54,  6, 16, 36, 57, 11, 27, 29, 45, 22, 19, 14,
       44, 24, 39, 38, 31, 41, 13, 50, 55, 30, 33, 51, 48, 53, 23, 18,  4,
       56,  7, 46, 43, 34,  0, 35, 52]), 'cur_cost': 241049.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2, 28, 32,  3, 12,  5, 21,  9, 40, 42, 20,  8, 25, 49, 47, 10, 15,
       58, 17, 26,  1, 37, 54,  6, 16, 36, 57, 11, 27, 29, 45, 22, 19, 14,
       44, 24, 39, 38, 31, 41, 13, 50, 55, 30, 33, 51, 48, 53, 23, 18,  4,
       56,  7, 46, 43, 34,  0, 35, 52]), 'cur_cost': 236371.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:09,813 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 249636.00)
2025-08-05 09:52:09,813 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:09,813 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:09,813 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:09,816 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 59
2025-08-05 09:52:09,817 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,817 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,818 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,818 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,818 - ExplorationExpert - INFO - 探索路径生成完成，成本: 237723.0, 路径长度: 59, 收集中间解: 3
2025-08-05 09:52:09,819 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [29, 19, 13, 4, 14, 8, 2, 1, 5, 24, 44, 35, 33, 34, 47, 28, 46, 48, 31, 50, 26, 9, 17, 18, 45, 0, 20, 3, 12, 16, 38, 54, 6, 25, 27, 41, 11, 30, 42, 22, 10, 36, 21, 49, 56, 52, 53, 39, 32, 7, 37, 40, 43, 55, 15, 57, 23, 51, 58], 'cur_cost': 237723.0, 'intermediate_solutions': [{'tour': [0, 19, 1, 6, 4, 9, 3, 5, 8, 7, 10, 2, 28, 36, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 26, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21], 'cur_cost': 52112.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 1, 6, 4, 9, 3, 5, 8, 7, 10, 2, 36, 43, 37, 44, 46, 35, 33, 23, 34, 27, 31, 24, 29, 32, 25, 30, 26, 28, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21], 'cur_cost': 37829.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 1, 6, 4, 9, 3, 5, 8, 7, 10, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 34, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21], 'cur_cost': 42472.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:09,819 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 237723.00)
2025-08-05 09:52:09,819 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:09,820 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:09,820 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:09,822 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 09:52:09,822 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,822 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,822 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,823 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,823 - ExplorationExpert - INFO - 探索路径生成完成，成本: 24908.0, 路径长度: 59, 收集中间解: 3
2025-08-05 09:52:09,823 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 3, 2, 5, 4, 9, 10, 6, 1, 7, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24908.0, 'intermediate_solutions': [{'tour': [0, 15, 6, 13, 10, 22, 14, 20, 16, 17, 21, 12, 11, 19, 1, 7, 8, 5, 3, 2, 9, 4, 18, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 47625.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 6, 13, 18, 22, 14, 20, 16, 17, 21, 12, 11, 19, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 47, 50, 54], 'cur_cost': 34542.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 6, 13, 18, 22, 14, 20, 16, 17, 21, 12, 11, 19, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 35, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 45860.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:09,823 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 24908.00)
2025-08-05 09:52:09,823 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:52:09,823 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:52:09,824 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:09,826 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 09:52:09,826 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,827 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,827 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,827 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,827 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32398.0, 路径长度: 59, 收集中间解: 3
2025-08-05 09:52:09,827 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 9, 21, 6, 1, 7, 8, 5, 3, 2, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 19], 'cur_cost': 32398.0, 'intermediate_solutions': [{'tour': [29, 58, 4, 54, 31, 26, 56, 15, 53, 49, 36, 25, 57, 6, 42, 50, 52, 35, 27, 44, 3, 2, 10, 8, 37, 40, 39, 34, 47, 19, 28, 45, 41, 30, 33, 48, 55, 21, 32, 51, 5, 9, 17, 46, 1, 13, 7, 23, 11, 18, 20, 0, 12, 16, 24, 38, 14, 22, 43], 'cur_cost': 228759.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [56, 42, 31, 54, 4, 58, 29, 15, 53, 49, 36, 25, 57, 6, 26, 50, 52, 35, 27, 44, 3, 2, 10, 8, 37, 40, 39, 34, 47, 19, 28, 45, 41, 30, 33, 48, 55, 21, 32, 51, 5, 9, 17, 46, 1, 13, 7, 23, 11, 18, 20, 0, 12, 16, 24, 38, 14, 22, 43], 'cur_cost': 233300.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [29, 58, 4, 54, 31, 42, 56, 15, 53, 49, 36, 25, 57, 6, 26, 50, 52, 35, 27, 44, 3, 2, 10, 8, 37, 40, 39, 34, 47, 19, 28, 45, 41, 30, 33, 48, 32, 55, 21, 51, 5, 9, 17, 46, 1, 13, 7, 23, 11, 18, 20, 0, 12, 16, 24, 38, 14, 22, 43], 'cur_cost': 240848.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:09,828 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 32398.00)
2025-08-05 09:52:09,828 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:09,828 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:09,828 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:09,834 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 59
2025-08-05 09:52:09,834 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,834 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,835 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,835 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,835 - ExplorationExpert - INFO - 探索路径生成完成，成本: 183182.0, 路径长度: 59, 收集中间解: 3
2025-08-05 09:52:09,835 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [26, 39, 7, 31, 29, 42, 33, 23, 37, 30, 32, 46, 9, 18, 2, 21, 13, 28, 38, 1, 15, 8, 3, 25, 10, 0, 49, 19, 5, 57, 48, 6, 27, 43, 24, 41, 58, 56, 55, 22, 14, 51, 47, 17, 16, 34, 45, 44, 53, 54, 4, 12, 11, 20, 50, 52, 35, 36, 40], 'cur_cost': 183182.0, 'intermediate_solutions': [{'tour': [19, 1, 13, 4, 48, 21, 5, 40, 17, 20, 2, 25, 49, 47, 7, 15, 58, 24, 27, 37, 23, 6, 35, 36, 11, 43, 45, 22, 34, 38, 39, 46, 9, 31, 41, 56, 50, 55, 30, 33, 51, 16, 12, 52, 57, 32, 28, 54, 10, 18, 42, 3, 44, 26, 29, 8, 53, 0, 14], 'cur_cost': 225135.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [19, 1, 13, 4, 9, 21, 5, 40, 0, 53, 8, 29, 26, 44, 3, 42, 18, 10, 54, 28, 32, 57, 52, 12, 16, 51, 33, 30, 55, 50, 56, 41, 31, 48, 46, 39, 38, 34, 22, 45, 43, 11, 36, 35, 6, 23, 37, 27, 24, 58, 15, 7, 47, 49, 25, 2, 20, 17, 14], 'cur_cost': 217482.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [19, 1, 13, 4, 35, 9, 21, 5, 40, 17, 20, 2, 25, 49, 47, 7, 15, 58, 24, 27, 37, 23, 6, 36, 11, 43, 45, 22, 34, 38, 39, 46, 48, 31, 41, 56, 50, 55, 30, 33, 51, 16, 12, 52, 57, 32, 28, 54, 10, 18, 42, 3, 44, 26, 29, 8, 53, 0, 14], 'cur_cost': 232862.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:09,836 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 183182.00)
2025-08-05 09:52:09,836 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:09,836 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:09,837 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:09,843 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 59
2025-08-05 09:52:09,844 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,844 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,844 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,845 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,846 - ExplorationExpert - INFO - 探索路径生成完成，成本: 149073.0, 路径长度: 59, 收集中间解: 3
2025-08-05 09:52:09,846 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [43, 30, 27, 33, 42, 31, 1, 29, 9, 34, 32, 0, 5, 54, 10, 24, 40, 25, 28, 23, 37, 38, 35, 3, 49, 7, 14, 20, 11, 16, 4, 15, 6, 21, 17, 19, 51, 47, 58, 53, 13, 2, 50, 57, 22, 8, 52, 48, 56, 44, 46, 36, 41, 39, 45, 55, 18, 12, 26], 'cur_cost': 149073.0, 'intermediate_solutions': [{'tour': [0, 5, 12, 1, 7, 8, 4, 9, 3, 2, 10, 6, 33, 31, 27, 34, 23, 25, 32, 29, 30, 26, 28, 24, 35, 46, 44, 37, 55, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 43, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 21, 19], 'cur_cost': 55075.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [28, 26, 30, 29, 32, 25, 23, 34, 27, 31, 33, 6, 10, 2, 3, 9, 4, 8, 7, 1, 12, 5, 0, 24, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 21, 19], 'cur_cost': 37097.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 12, 1, 7, 8, 4, 9, 3, 2, 10, 6, 33, 31, 27, 34, 23, 25, 32, 29, 30, 26, 28, 24, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 48, 39, 53, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 21, 19], 'cur_cost': 43781.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:09,846 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 149073.00)
2025-08-05 09:52:09,846 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:09,846 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:09,846 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:09,853 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 59
2025-08-05 09:52:09,853 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,854 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,854 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,854 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,855 - ExplorationExpert - INFO - 探索路径生成完成，成本: 176849.0, 路径长度: 59, 收集中间解: 3
2025-08-05 09:52:09,855 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [26, 44, 4, 33, 37, 0, 9, 31, 10, 21, 2, 23, 39, 29, 36, 24, 1, 34, 6, 17, 20, 12, 25, 42, 32, 38, 7, 19, 51, 52, 47, 55, 50, 58, 48, 22, 14, 18, 15, 30, 46, 35, 40, 27, 8, 54, 56, 3, 11, 16, 49, 5, 28, 43, 45, 41, 53, 57, 13], 'cur_cost': 176849.0, 'intermediate_solutions': [{'tour': [0, 7, 16, 18, 13, 22, 21, 12, 11, 15, 17, 14, 20, 19, 6, 1, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27200.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 14, 18, 13, 22, 21, 12, 11, 15, 17, 16, 20, 19, 6, 1, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 49, 51, 57, 55, 56, 52, 58, 48, 53, 39, 40, 54, 50, 47], 'cur_cost': 38348.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 18, 13, 22, 21, 7, 12, 11, 15, 17, 16, 20, 19, 6, 1, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34628.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:09,855 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 176849.00)
2025-08-05 09:52:09,855 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 09:52:09,855 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:09,855 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:09,856 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 235214.0
2025-08-05 09:52:09,867 - ExploitationExpert - INFO - res_population_num: 8
2025-08-05 09:52:09,867 - ExploitationExpert - INFO - res_population_costs: [24482, 24482, 24495, 24495, 24511, 24529.0, 24473, 24472]
2025-08-05 09:52:09,867 - ExploitationExpert - INFO - res_populations: [array([ 0,  9,  4,  1,  6, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26,
       28, 39, 40, 42, 45, 41, 38, 36, 43, 37, 44, 46, 35, 54, 50, 53, 48,
       58, 52, 56, 47, 55, 51, 57, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11,
       12, 21, 19,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39,
       40, 46, 35, 44, 37, 36, 42, 45, 41, 38, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 15, 17, 12, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41, 38,
       36, 43, 37, 44, 46, 35,  2,  3], dtype=int64), array([ 0, 10,  6,  1,  7,  4,  9,  3,  5,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 17, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 15, 17, 12, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39, 40, 42, 45,
       41, 38, 36, 37, 43, 44,  2,  3], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 46, 35, 44, 37,
       36, 42, 45, 41, 38, 43,  2,  3], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 53, 48, 58, 52, 50, 54,
       49, 57, 56, 47, 55, 51, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39, 40, 42,
       45, 41, 38, 36, 37, 43, 44,  2], dtype=int64)]
2025-08-05 09:52:09,871 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:09,871 - ExploitationExpert - INFO - populations: [{'tour': array([14, 47, 55, 24, 45, 40,  6, 49, 31, 21, 15,  5, 30,  7, 12, 32, 42,
       54, 27, 25, 57, 23,  2,  8, 36, 51, 53, 16,  4, 58, 20, 34, 44,  9,
       56, 10, 33, 41, 50, 52, 39, 26, 28, 43, 19, 37, 29, 11, 22,  1, 18,
       13,  0, 35, 48, 46, 17, 38,  3], dtype=int64), 'cur_cost': 249636.0}, {'tour': [29, 19, 13, 4, 14, 8, 2, 1, 5, 24, 44, 35, 33, 34, 47, 28, 46, 48, 31, 50, 26, 9, 17, 18, 45, 0, 20, 3, 12, 16, 38, 54, 6, 25, 27, 41, 11, 30, 42, 22, 10, 36, 21, 49, 56, 52, 53, 39, 32, 7, 37, 40, 43, 55, 15, 57, 23, 51, 58], 'cur_cost': 237723.0}, {'tour': [0, 3, 2, 5, 4, 9, 10, 6, 1, 7, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24908.0}, {'tour': [0, 9, 21, 6, 1, 7, 8, 5, 3, 2, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 19], 'cur_cost': 32398.0}, {'tour': [26, 39, 7, 31, 29, 42, 33, 23, 37, 30, 32, 46, 9, 18, 2, 21, 13, 28, 38, 1, 15, 8, 3, 25, 10, 0, 49, 19, 5, 57, 48, 6, 27, 43, 24, 41, 58, 56, 55, 22, 14, 51, 47, 17, 16, 34, 45, 44, 53, 54, 4, 12, 11, 20, 50, 52, 35, 36, 40], 'cur_cost': 183182.0}, {'tour': [43, 30, 27, 33, 42, 31, 1, 29, 9, 34, 32, 0, 5, 54, 10, 24, 40, 25, 28, 23, 37, 38, 35, 3, 49, 7, 14, 20, 11, 16, 4, 15, 6, 21, 17, 19, 51, 47, 58, 53, 13, 2, 50, 57, 22, 8, 52, 48, 56, 44, 46, 36, 41, 39, 45, 55, 18, 12, 26], 'cur_cost': 149073.0}, {'tour': [26, 44, 4, 33, 37, 0, 9, 31, 10, 21, 2, 23, 39, 29, 36, 24, 1, 34, 6, 17, 20, 12, 25, 42, 32, 38, 7, 19, 51, 52, 47, 55, 50, 58, 48, 22, 14, 18, 15, 30, 46, 35, 40, 27, 8, 54, 56, 3, 11, 16, 49, 5, 28, 43, 45, 41, 53, 57, 13], 'cur_cost': 176849.0}, {'tour': array([25, 54, 49, 17, 13, 33,  3, 42, 30, 44, 48, 37,  4, 26, 24, 34, 56,
       16, 53, 31, 35,  9,  1, 38,  6, 36, 41, 45, 23, 19, 47, 57, 27, 10,
       18, 29,  7, 22, 32,  8, 52, 11, 21, 58, 46, 15, 39, 43,  0, 40, 50,
       51, 12,  5, 20, 14, 28,  2, 55], dtype=int64), 'cur_cost': 235214.0}, {'tour': [0, 21, 4, 15, 17, 16, 11, 12, 19, 18, 13, 22, 14, 20, 6, 1, 7, 8, 5, 3, 2, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34667.0}, {'tour': [44, 32, 4, 18, 34, 7, 23, 33, 3, 12, 2, 11, 20, 9, 5, 21, 31, 46, 42, 25, 6, 24, 28, 45, 30, 8, 19, 16, 17, 10, 27, 1, 13, 57, 53, 54, 14, 29, 26, 36, 40, 39, 41, 43, 35, 58, 55, 51, 50, 49, 47, 56, 0, 15, 22, 52, 48, 37, 38], 'cur_cost': 147519.0}]
2025-08-05 09:52:09,872 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:09,873 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 290, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 290, 'cache_hits': 0, 'similarity_calculations': 1440, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:09,874 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([25, 54, 49, 17, 13, 33,  3, 42, 30, 44, 48, 37,  4, 26, 24, 34, 56,
       16, 53, 31, 35,  9,  1, 38,  6, 36, 41, 45, 23, 19, 47, 57, 27, 10,
       18, 29,  7, 22, 32,  8, 52, 11, 21, 58, 46, 15, 39, 43,  0, 40, 50,
       51, 12,  5, 20, 14, 28,  2, 55], dtype=int64), 'cur_cost': 235214.0, 'intermediate_solutions': [{'tour': array([12, 37, 32, 24, 26,  7, 17, 41, 56, 43, 19, 35, 53,  6, 36, 13,  4,
       42, 22, 52, 20,  1, 21, 58, 16, 27, 30, 55, 39, 38, 48, 10, 14,  9,
        2, 15, 54, 23, 34, 57, 50, 31, 49,  5, 18, 46, 47, 25, 33,  0, 51,
       40, 28,  3,  8, 44, 45, 29, 11]), 'cur_cost': 259643.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([24, 12, 37, 32, 26,  7, 17, 41, 56, 43, 19, 35, 53,  6, 36, 13,  4,
       42, 22, 52, 20,  1, 21, 58, 16, 27, 30, 55, 39, 38, 48, 10, 14,  9,
        2, 15, 54, 23, 34, 57, 50, 31, 49,  5, 18, 46, 47, 25, 33,  0, 51,
       40, 28,  3,  8, 44, 45, 29, 11]), 'cur_cost': 269843.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([26, 24, 12, 37, 32,  7, 17, 41, 56, 43, 19, 35, 53,  6, 36, 13,  4,
       42, 22, 52, 20,  1, 21, 58, 16, 27, 30, 55, 39, 38, 48, 10, 14,  9,
        2, 15, 54, 23, 34, 57, 50, 31, 49,  5, 18, 46, 47, 25, 33,  0, 51,
       40, 28,  3,  8, 44, 45, 29, 11]), 'cur_cost': 269845.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([32, 24, 12, 37, 26,  7, 17, 41, 56, 43, 19, 35, 53,  6, 36, 13,  4,
       42, 22, 52, 20,  1, 21, 58, 16, 27, 30, 55, 39, 38, 48, 10, 14,  9,
        2, 15, 54, 23, 34, 57, 50, 31, 49,  5, 18, 46, 47, 25, 33,  0, 51,
       40, 28,  3,  8, 44, 45, 29, 11]), 'cur_cost': 269783.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([32, 26, 24, 12, 37,  7, 17, 41, 56, 43, 19, 35, 53,  6, 36, 13,  4,
       42, 22, 52, 20,  1, 21, 58, 16, 27, 30, 55, 39, 38, 48, 10, 14,  9,
        2, 15, 54, 23, 34, 57, 50, 31, 49,  5, 18, 46, 47, 25, 33,  0, 51,
       40, 28,  3,  8, 44, 45, 29, 11]), 'cur_cost': 265434.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:09,874 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 235214.00)
2025-08-05 09:52:09,874 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:52:09,874 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:52:09,874 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:09,876 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 09:52:09,877 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,877 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,877 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,877 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,877 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32606.0, 路径长度: 59, 收集中间解: 3
2025-08-05 09:52:09,878 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 22, 13, 2, 3, 5, 4, 9, 10, 6, 1, 7, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 18, 19, 21, 12, 11, 15, 17, 16, 20, 14], 'cur_cost': 32606.0, 'intermediate_solutions': [{'tour': [0, 21, 4, 15, 17, 16, 11, 12, 19, 18, 13, 22, 14, 20, 6, 1, 7, 3, 5, 8, 2, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34730.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 4, 15, 17, 16, 11, 12, 2, 3, 5, 8, 7, 1, 6, 20, 14, 22, 13, 18, 19, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 42189.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 4, 15, 17, 16, 11, 12, 19, 18, 13, 22, 14, 20, 6, 1, 7, 8, 5, 3, 2, 9, 10, 28, 30, 25, 32, 29, 24, 31, 26, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34745.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:09,878 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 32606.00)
2025-08-05 09:52:09,878 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:09,878 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:09,878 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:09,881 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 09:52:09,882 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,882 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,882 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,883 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:09,883 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27221.0, 路径长度: 59, 收集中间解: 3
2025-08-05 09:52:09,883 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 20, 12, 16, 17, 15, 11, 21, 22, 13, 18, 19, 14, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27221.0, 'intermediate_solutions': [{'tour': [44, 32, 4, 18, 34, 7, 23, 33, 3, 12, 2, 35, 20, 9, 5, 21, 31, 46, 42, 25, 6, 24, 28, 45, 30, 8, 19, 16, 17, 10, 27, 1, 13, 57, 53, 54, 14, 29, 26, 36, 40, 39, 41, 43, 11, 58, 55, 51, 50, 49, 47, 56, 0, 15, 22, 52, 48, 37, 38], 'cur_cost': 162829.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [44, 32, 40, 36, 26, 29, 14, 54, 53, 57, 13, 1, 27, 10, 17, 16, 19, 8, 30, 45, 28, 24, 6, 25, 42, 46, 31, 21, 5, 9, 20, 11, 2, 12, 3, 33, 23, 7, 34, 18, 4, 39, 41, 43, 35, 58, 55, 51, 50, 49, 47, 56, 0, 15, 22, 52, 48, 37, 38], 'cur_cost': 152883.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [44, 32, 4, 18, 34, 7, 23, 33, 3, 12, 2, 11, 20, 9, 5, 21, 31, 46, 42, 25, 6, 24, 28, 45, 30, 8, 19, 16, 17, 10, 27, 1, 13, 57, 53, 54, 14, 26, 36, 40, 39, 41, 43, 35, 58, 55, 51, 50, 49, 47, 56, 0, 29, 15, 22, 52, 48, 37, 38], 'cur_cost': 152143.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:09,884 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 27221.00)
2025-08-05 09:52:09,884 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:09,884 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:09,886 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([14, 47, 55, 24, 45, 40,  6, 49, 31, 21, 15,  5, 30,  7, 12, 32, 42,
       54, 27, 25, 57, 23,  2,  8, 36, 51, 53, 16,  4, 58, 20, 34, 44,  9,
       56, 10, 33, 41, 50, 52, 39, 26, 28, 43, 19, 37, 29, 11, 22,  1, 18,
       13,  0, 35, 48, 46, 17, 38,  3], dtype=int64), 'cur_cost': 249636.0, 'intermediate_solutions': [{'tour': array([ 3, 12,  2, 32, 28,  5, 21,  9, 40, 42, 20,  8, 25, 49, 47, 10, 15,
       58, 17, 26,  1, 37, 54,  6, 16, 36, 57, 11, 27, 29, 45, 22, 19, 14,
       44, 24, 39, 38, 31, 41, 13, 50, 55, 30, 33, 51, 48, 53, 23, 18,  4,
       56,  7, 46, 43, 34,  0, 35, 52]), 'cur_cost': 236410.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([32,  3, 12,  2, 28,  5, 21,  9, 40, 42, 20,  8, 25, 49, 47, 10, 15,
       58, 17, 26,  1, 37, 54,  6, 16, 36, 57, 11, 27, 29, 45, 22, 19, 14,
       44, 24, 39, 38, 31, 41, 13, 50, 55, 30, 33, 51, 48, 53, 23, 18,  4,
       56,  7, 46, 43, 34,  0, 35, 52]), 'cur_cost': 243219.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([28, 32,  3, 12,  2,  5, 21,  9, 40, 42, 20,  8, 25, 49, 47, 10, 15,
       58, 17, 26,  1, 37, 54,  6, 16, 36, 57, 11, 27, 29, 45, 22, 19, 14,
       44, 24, 39, 38, 31, 41, 13, 50, 55, 30, 33, 51, 48, 53, 23, 18,  4,
       56,  7, 46, 43, 34,  0, 35, 52]), 'cur_cost': 236426.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2, 32,  3, 12, 28,  5, 21,  9, 40, 42, 20,  8, 25, 49, 47, 10, 15,
       58, 17, 26,  1, 37, 54,  6, 16, 36, 57, 11, 27, 29, 45, 22, 19, 14,
       44, 24, 39, 38, 31, 41, 13, 50, 55, 30, 33, 51, 48, 53, 23, 18,  4,
       56,  7, 46, 43, 34,  0, 35, 52]), 'cur_cost': 241049.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2, 28, 32,  3, 12,  5, 21,  9, 40, 42, 20,  8, 25, 49, 47, 10, 15,
       58, 17, 26,  1, 37, 54,  6, 16, 36, 57, 11, 27, 29, 45, 22, 19, 14,
       44, 24, 39, 38, 31, 41, 13, 50, 55, 30, 33, 51, 48, 53, 23, 18,  4,
       56,  7, 46, 43, 34,  0, 35, 52]), 'cur_cost': 236371.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [29, 19, 13, 4, 14, 8, 2, 1, 5, 24, 44, 35, 33, 34, 47, 28, 46, 48, 31, 50, 26, 9, 17, 18, 45, 0, 20, 3, 12, 16, 38, 54, 6, 25, 27, 41, 11, 30, 42, 22, 10, 36, 21, 49, 56, 52, 53, 39, 32, 7, 37, 40, 43, 55, 15, 57, 23, 51, 58], 'cur_cost': 237723.0, 'intermediate_solutions': [{'tour': [0, 19, 1, 6, 4, 9, 3, 5, 8, 7, 10, 2, 28, 36, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 26, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21], 'cur_cost': 52112.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 1, 6, 4, 9, 3, 5, 8, 7, 10, 2, 36, 43, 37, 44, 46, 35, 33, 23, 34, 27, 31, 24, 29, 32, 25, 30, 26, 28, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21], 'cur_cost': 37829.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 1, 6, 4, 9, 3, 5, 8, 7, 10, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 34, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21], 'cur_cost': 42472.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 2, 5, 4, 9, 10, 6, 1, 7, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24908.0, 'intermediate_solutions': [{'tour': [0, 15, 6, 13, 10, 22, 14, 20, 16, 17, 21, 12, 11, 19, 1, 7, 8, 5, 3, 2, 9, 4, 18, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 47625.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 6, 13, 18, 22, 14, 20, 16, 17, 21, 12, 11, 19, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 47, 50, 54], 'cur_cost': 34542.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 6, 13, 18, 22, 14, 20, 16, 17, 21, 12, 11, 19, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 35, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 45860.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 21, 6, 1, 7, 8, 5, 3, 2, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 19], 'cur_cost': 32398.0, 'intermediate_solutions': [{'tour': [29, 58, 4, 54, 31, 26, 56, 15, 53, 49, 36, 25, 57, 6, 42, 50, 52, 35, 27, 44, 3, 2, 10, 8, 37, 40, 39, 34, 47, 19, 28, 45, 41, 30, 33, 48, 55, 21, 32, 51, 5, 9, 17, 46, 1, 13, 7, 23, 11, 18, 20, 0, 12, 16, 24, 38, 14, 22, 43], 'cur_cost': 228759.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [56, 42, 31, 54, 4, 58, 29, 15, 53, 49, 36, 25, 57, 6, 26, 50, 52, 35, 27, 44, 3, 2, 10, 8, 37, 40, 39, 34, 47, 19, 28, 45, 41, 30, 33, 48, 55, 21, 32, 51, 5, 9, 17, 46, 1, 13, 7, 23, 11, 18, 20, 0, 12, 16, 24, 38, 14, 22, 43], 'cur_cost': 233300.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [29, 58, 4, 54, 31, 42, 56, 15, 53, 49, 36, 25, 57, 6, 26, 50, 52, 35, 27, 44, 3, 2, 10, 8, 37, 40, 39, 34, 47, 19, 28, 45, 41, 30, 33, 48, 32, 55, 21, 51, 5, 9, 17, 46, 1, 13, 7, 23, 11, 18, 20, 0, 12, 16, 24, 38, 14, 22, 43], 'cur_cost': 240848.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [26, 39, 7, 31, 29, 42, 33, 23, 37, 30, 32, 46, 9, 18, 2, 21, 13, 28, 38, 1, 15, 8, 3, 25, 10, 0, 49, 19, 5, 57, 48, 6, 27, 43, 24, 41, 58, 56, 55, 22, 14, 51, 47, 17, 16, 34, 45, 44, 53, 54, 4, 12, 11, 20, 50, 52, 35, 36, 40], 'cur_cost': 183182.0, 'intermediate_solutions': [{'tour': [19, 1, 13, 4, 48, 21, 5, 40, 17, 20, 2, 25, 49, 47, 7, 15, 58, 24, 27, 37, 23, 6, 35, 36, 11, 43, 45, 22, 34, 38, 39, 46, 9, 31, 41, 56, 50, 55, 30, 33, 51, 16, 12, 52, 57, 32, 28, 54, 10, 18, 42, 3, 44, 26, 29, 8, 53, 0, 14], 'cur_cost': 225135.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [19, 1, 13, 4, 9, 21, 5, 40, 0, 53, 8, 29, 26, 44, 3, 42, 18, 10, 54, 28, 32, 57, 52, 12, 16, 51, 33, 30, 55, 50, 56, 41, 31, 48, 46, 39, 38, 34, 22, 45, 43, 11, 36, 35, 6, 23, 37, 27, 24, 58, 15, 7, 47, 49, 25, 2, 20, 17, 14], 'cur_cost': 217482.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [19, 1, 13, 4, 35, 9, 21, 5, 40, 17, 20, 2, 25, 49, 47, 7, 15, 58, 24, 27, 37, 23, 6, 36, 11, 43, 45, 22, 34, 38, 39, 46, 48, 31, 41, 56, 50, 55, 30, 33, 51, 16, 12, 52, 57, 32, 28, 54, 10, 18, 42, 3, 44, 26, 29, 8, 53, 0, 14], 'cur_cost': 232862.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [43, 30, 27, 33, 42, 31, 1, 29, 9, 34, 32, 0, 5, 54, 10, 24, 40, 25, 28, 23, 37, 38, 35, 3, 49, 7, 14, 20, 11, 16, 4, 15, 6, 21, 17, 19, 51, 47, 58, 53, 13, 2, 50, 57, 22, 8, 52, 48, 56, 44, 46, 36, 41, 39, 45, 55, 18, 12, 26], 'cur_cost': 149073.0, 'intermediate_solutions': [{'tour': [0, 5, 12, 1, 7, 8, 4, 9, 3, 2, 10, 6, 33, 31, 27, 34, 23, 25, 32, 29, 30, 26, 28, 24, 35, 46, 44, 37, 55, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 43, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 21, 19], 'cur_cost': 55075.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [28, 26, 30, 29, 32, 25, 23, 34, 27, 31, 33, 6, 10, 2, 3, 9, 4, 8, 7, 1, 12, 5, 0, 24, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 21, 19], 'cur_cost': 37097.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 12, 1, 7, 8, 4, 9, 3, 2, 10, 6, 33, 31, 27, 34, 23, 25, 32, 29, 30, 26, 28, 24, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 48, 39, 53, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 21, 19], 'cur_cost': 43781.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [26, 44, 4, 33, 37, 0, 9, 31, 10, 21, 2, 23, 39, 29, 36, 24, 1, 34, 6, 17, 20, 12, 25, 42, 32, 38, 7, 19, 51, 52, 47, 55, 50, 58, 48, 22, 14, 18, 15, 30, 46, 35, 40, 27, 8, 54, 56, 3, 11, 16, 49, 5, 28, 43, 45, 41, 53, 57, 13], 'cur_cost': 176849.0, 'intermediate_solutions': [{'tour': [0, 7, 16, 18, 13, 22, 21, 12, 11, 15, 17, 14, 20, 19, 6, 1, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27200.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 14, 18, 13, 22, 21, 12, 11, 15, 17, 16, 20, 19, 6, 1, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 49, 51, 57, 55, 56, 52, 58, 48, 53, 39, 40, 54, 50, 47], 'cur_cost': 38348.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 18, 13, 22, 21, 7, 12, 11, 15, 17, 16, 20, 19, 6, 1, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34628.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([25, 54, 49, 17, 13, 33,  3, 42, 30, 44, 48, 37,  4, 26, 24, 34, 56,
       16, 53, 31, 35,  9,  1, 38,  6, 36, 41, 45, 23, 19, 47, 57, 27, 10,
       18, 29,  7, 22, 32,  8, 52, 11, 21, 58, 46, 15, 39, 43,  0, 40, 50,
       51, 12,  5, 20, 14, 28,  2, 55], dtype=int64), 'cur_cost': 235214.0, 'intermediate_solutions': [{'tour': array([12, 37, 32, 24, 26,  7, 17, 41, 56, 43, 19, 35, 53,  6, 36, 13,  4,
       42, 22, 52, 20,  1, 21, 58, 16, 27, 30, 55, 39, 38, 48, 10, 14,  9,
        2, 15, 54, 23, 34, 57, 50, 31, 49,  5, 18, 46, 47, 25, 33,  0, 51,
       40, 28,  3,  8, 44, 45, 29, 11]), 'cur_cost': 259643.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([24, 12, 37, 32, 26,  7, 17, 41, 56, 43, 19, 35, 53,  6, 36, 13,  4,
       42, 22, 52, 20,  1, 21, 58, 16, 27, 30, 55, 39, 38, 48, 10, 14,  9,
        2, 15, 54, 23, 34, 57, 50, 31, 49,  5, 18, 46, 47, 25, 33,  0, 51,
       40, 28,  3,  8, 44, 45, 29, 11]), 'cur_cost': 269843.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([26, 24, 12, 37, 32,  7, 17, 41, 56, 43, 19, 35, 53,  6, 36, 13,  4,
       42, 22, 52, 20,  1, 21, 58, 16, 27, 30, 55, 39, 38, 48, 10, 14,  9,
        2, 15, 54, 23, 34, 57, 50, 31, 49,  5, 18, 46, 47, 25, 33,  0, 51,
       40, 28,  3,  8, 44, 45, 29, 11]), 'cur_cost': 269845.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([32, 24, 12, 37, 26,  7, 17, 41, 56, 43, 19, 35, 53,  6, 36, 13,  4,
       42, 22, 52, 20,  1, 21, 58, 16, 27, 30, 55, 39, 38, 48, 10, 14,  9,
        2, 15, 54, 23, 34, 57, 50, 31, 49,  5, 18, 46, 47, 25, 33,  0, 51,
       40, 28,  3,  8, 44, 45, 29, 11]), 'cur_cost': 269783.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([32, 26, 24, 12, 37,  7, 17, 41, 56, 43, 19, 35, 53,  6, 36, 13,  4,
       42, 22, 52, 20,  1, 21, 58, 16, 27, 30, 55, 39, 38, 48, 10, 14,  9,
        2, 15, 54, 23, 34, 57, 50, 31, 49,  5, 18, 46, 47, 25, 33,  0, 51,
       40, 28,  3,  8, 44, 45, 29, 11]), 'cur_cost': 265434.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 22, 13, 2, 3, 5, 4, 9, 10, 6, 1, 7, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 18, 19, 21, 12, 11, 15, 17, 16, 20, 14], 'cur_cost': 32606.0, 'intermediate_solutions': [{'tour': [0, 21, 4, 15, 17, 16, 11, 12, 19, 18, 13, 22, 14, 20, 6, 1, 7, 3, 5, 8, 2, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34730.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 4, 15, 17, 16, 11, 12, 2, 3, 5, 8, 7, 1, 6, 20, 14, 22, 13, 18, 19, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 42189.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 4, 15, 17, 16, 11, 12, 19, 18, 13, 22, 14, 20, 6, 1, 7, 8, 5, 3, 2, 9, 10, 28, 30, 25, 32, 29, 24, 31, 26, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34745.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 12, 16, 17, 15, 11, 21, 22, 13, 18, 19, 14, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27221.0, 'intermediate_solutions': [{'tour': [44, 32, 4, 18, 34, 7, 23, 33, 3, 12, 2, 35, 20, 9, 5, 21, 31, 46, 42, 25, 6, 24, 28, 45, 30, 8, 19, 16, 17, 10, 27, 1, 13, 57, 53, 54, 14, 29, 26, 36, 40, 39, 41, 43, 11, 58, 55, 51, 50, 49, 47, 56, 0, 15, 22, 52, 48, 37, 38], 'cur_cost': 162829.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [44, 32, 40, 36, 26, 29, 14, 54, 53, 57, 13, 1, 27, 10, 17, 16, 19, 8, 30, 45, 28, 24, 6, 25, 42, 46, 31, 21, 5, 9, 20, 11, 2, 12, 3, 33, 23, 7, 34, 18, 4, 39, 41, 43, 35, 58, 55, 51, 50, 49, 47, 56, 0, 15, 22, 52, 48, 37, 38], 'cur_cost': 152883.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [44, 32, 4, 18, 34, 7, 23, 33, 3, 12, 2, 11, 20, 9, 5, 21, 31, 46, 42, 25, 6, 24, 28, 45, 30, 8, 19, 16, 17, 10, 27, 1, 13, 57, 53, 54, 14, 26, 36, 40, 39, 41, 43, 35, 58, 55, 51, 50, 49, 47, 56, 0, 29, 15, 22, 52, 48, 37, 38], 'cur_cost': 152143.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:09,887 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:09,887 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:09,891 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=24908.000, 多样性=0.984
2025-08-05 09:52:09,891 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 09:52:09,892 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 09:52:09,892 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:09,893 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.032259105877261406, 'best_improvement': 0.08129241664207731}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.0687397708674306}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.1089730246880631, 'recent_improvements': [0.23324410872078835, 0.014600767296691623, 0.015298059344662185], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 8, 'new_count': 8, 'count_change': 0, 'old_best_cost': 24472, 'new_best_cost': 24472, 'quality_improvement': 0.0, 'old_diversity': 0.8426150121065376, 'new_diversity': 0.8426150121065376, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配']}
2025-08-05 09:52:09,894 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 09:52:09,894 - __main__ - INFO - composite11_59 开始进化第 3 代
2025-08-05 09:52:09,894 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 09:52:09,894 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:09,895 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=24908.000, 多样性=0.984
2025-08-05 09:52:09,895 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:09,898 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.984
2025-08-05 09:52:09,898 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:09,902 - EliteExpert - INFO - 精英解分析完成: 精英解数量=8, 多样性=0.843
2025-08-05 09:52:09,904 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 09:52:09,904 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:09,904 - LandscapeExpert - INFO - 添加精英解数据: 8个精英解
2025-08-05 09:52:09,904 - LandscapeExpert - INFO - 数据提取成功: 18个路径, 18个适应度值
2025-08-05 09:52:09,957 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.111, 适应度梯度: -28499.711, 聚类评分: 0.000, 覆盖率: 0.134, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:09,958 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 09:52:09,958 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:09,958 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite11_59
2025-08-05 09:52:09,965 - visualization.landscape_visualizer - INFO - 插值约束: 152 个点被约束到最小值 24472.00
2025-08-05 09:52:10,073 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite11_59\landscape_composite11_59_iter_113_20250805_095210.html
2025-08-05 09:52:10,178 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite11_59\dashboard_composite11_59_iter_113_20250805_095210.html
2025-08-05 09:52:10,178 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 113
2025-08-05 09:52:10,178 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 09:52:10,178 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2740秒
2025-08-05 09:52:10,179 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1111111111111111, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -28499.711111111115, 'local_optima_density': 0.1111111111111111, 'gradient_variance': 6972300367.18321, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1342, 'fitness_entropy': 0.6528150408758517, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -28499.711)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.134)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358729.958458, 'performance_metrics': {}}}
2025-08-05 09:52:10,180 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:10,180 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:10,180 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:10,181 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:10,181 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:52:10,182 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 09:52:10,182 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:52:10,182 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:10,182 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:10,182 - __main__ - INFO - 策略分配: ['exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:52:10,183 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:10,183 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:10,183 - experts.management.collaboration_manager - INFO - 识别精英个体: {9, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:10,183 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-05 09:52:10,183 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:10,183 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:10,184 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 283787.0
2025-08-05 09:52:10,193 - ExploitationExpert - INFO - res_population_num: 8
2025-08-05 09:52:10,193 - ExploitationExpert - INFO - res_population_costs: [24472, 24473, 24482, 24482, 24495, 24495, 24511, 24529.0]
2025-08-05 09:52:10,193 - ExploitationExpert - INFO - res_populations: [array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39, 40, 42,
       45, 41, 38, 36, 37, 43, 44,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 53, 48, 58, 52, 50, 54,
       49, 57, 56, 47, 55, 51, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  9,  4,  1,  6, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26,
       28, 39, 40, 42, 45, 41, 38, 36, 43, 37, 44, 46, 35, 54, 50, 53, 48,
       58, 52, 56, 47, 55, 51, 57, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11,
       12, 21, 19,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39,
       40, 46, 35, 44, 37, 36, 42, 45, 41, 38, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 15, 17, 12, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41, 38,
       36, 43, 37, 44, 46, 35,  2,  3], dtype=int64), array([ 0, 10,  6,  1,  7,  4,  9,  3,  5,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 17, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 15, 17, 12, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39, 40, 42, 45,
       41, 38, 36, 37, 43, 44,  2,  3], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 46, 35, 44, 37,
       36, 42, 45, 41, 38, 43,  2,  3], dtype=int64)]
2025-08-05 09:52:10,198 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:10,198 - ExploitationExpert - INFO - populations: [{'tour': array([20,  3, 31, 18, 58, 32, 37, 28, 51, 34, 53, 17, 47, 14, 40, 26,  7,
        4, 24,  0, 54, 49, 38, 21, 45, 10, 36, 39, 42, 52, 29, 57,  2, 41,
        8, 19, 46, 44, 13, 12, 55, 30, 56, 43,  6,  9, 50,  5, 27, 15, 23,
       16,  1, 25, 48, 11, 33, 22, 35], dtype=int64), 'cur_cost': 283787.0}, {'tour': [29, 19, 13, 4, 14, 8, 2, 1, 5, 24, 44, 35, 33, 34, 47, 28, 46, 48, 31, 50, 26, 9, 17, 18, 45, 0, 20, 3, 12, 16, 38, 54, 6, 25, 27, 41, 11, 30, 42, 22, 10, 36, 21, 49, 56, 52, 53, 39, 32, 7, 37, 40, 43, 55, 15, 57, 23, 51, 58], 'cur_cost': 237723.0}, {'tour': [0, 3, 2, 5, 4, 9, 10, 6, 1, 7, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24908.0}, {'tour': [0, 9, 21, 6, 1, 7, 8, 5, 3, 2, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 19], 'cur_cost': 32398.0}, {'tour': [26, 39, 7, 31, 29, 42, 33, 23, 37, 30, 32, 46, 9, 18, 2, 21, 13, 28, 38, 1, 15, 8, 3, 25, 10, 0, 49, 19, 5, 57, 48, 6, 27, 43, 24, 41, 58, 56, 55, 22, 14, 51, 47, 17, 16, 34, 45, 44, 53, 54, 4, 12, 11, 20, 50, 52, 35, 36, 40], 'cur_cost': 183182.0}, {'tour': [43, 30, 27, 33, 42, 31, 1, 29, 9, 34, 32, 0, 5, 54, 10, 24, 40, 25, 28, 23, 37, 38, 35, 3, 49, 7, 14, 20, 11, 16, 4, 15, 6, 21, 17, 19, 51, 47, 58, 53, 13, 2, 50, 57, 22, 8, 52, 48, 56, 44, 46, 36, 41, 39, 45, 55, 18, 12, 26], 'cur_cost': 149073.0}, {'tour': [26, 44, 4, 33, 37, 0, 9, 31, 10, 21, 2, 23, 39, 29, 36, 24, 1, 34, 6, 17, 20, 12, 25, 42, 32, 38, 7, 19, 51, 52, 47, 55, 50, 58, 48, 22, 14, 18, 15, 30, 46, 35, 40, 27, 8, 54, 56, 3, 11, 16, 49, 5, 28, 43, 45, 41, 53, 57, 13], 'cur_cost': 176849.0}, {'tour': [25, 54, 49, 17, 13, 33, 3, 42, 30, 44, 48, 37, 4, 26, 24, 34, 56, 16, 53, 31, 35, 9, 1, 38, 6, 36, 41, 45, 23, 19, 47, 57, 27, 10, 18, 29, 7, 22, 32, 8, 52, 11, 21, 58, 46, 15, 39, 43, 0, 40, 50, 51, 12, 5, 20, 14, 28, 2, 55], 'cur_cost': 235214.0}, {'tour': [0, 22, 13, 2, 3, 5, 4, 9, 10, 6, 1, 7, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 18, 19, 21, 12, 11, 15, 17, 16, 20, 14], 'cur_cost': 32606.0}, {'tour': [0, 20, 12, 16, 17, 15, 11, 21, 22, 13, 18, 19, 14, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27221.0}]
2025-08-05 09:52:10,198 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:10,198 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 291, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 291, 'cache_hits': 0, 'similarity_calculations': 1444, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:10,199 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([20,  3, 31, 18, 58, 32, 37, 28, 51, 34, 53, 17, 47, 14, 40, 26,  7,
        4, 24,  0, 54, 49, 38, 21, 45, 10, 36, 39, 42, 52, 29, 57,  2, 41,
        8, 19, 46, 44, 13, 12, 55, 30, 56, 43,  6,  9, 50,  5, 27, 15, 23,
       16,  1, 25, 48, 11, 33, 22, 35], dtype=int64), 'cur_cost': 283787.0, 'intermediate_solutions': [{'tour': array([55, 47, 14, 24, 45, 40,  6, 49, 31, 21, 15,  5, 30,  7, 12, 32, 42,
       54, 27, 25, 57, 23,  2,  8, 36, 51, 53, 16,  4, 58, 20, 34, 44,  9,
       56, 10, 33, 41, 50, 52, 39, 26, 28, 43, 19, 37, 29, 11, 22,  1, 18,
       13,  0, 35, 48, 46, 17, 38,  3]), 'cur_cost': 247534.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([24, 55, 47, 14, 45, 40,  6, 49, 31, 21, 15,  5, 30,  7, 12, 32, 42,
       54, 27, 25, 57, 23,  2,  8, 36, 51, 53, 16,  4, 58, 20, 34, 44,  9,
       56, 10, 33, 41, 50, 52, 39, 26, 28, 43, 19, 37, 29, 11, 22,  1, 18,
       13,  0, 35, 48, 46, 17, 38,  3]), 'cur_cost': 252075.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([45, 24, 55, 47, 14, 40,  6, 49, 31, 21, 15,  5, 30,  7, 12, 32, 42,
       54, 27, 25, 57, 23,  2,  8, 36, 51, 53, 16,  4, 58, 20, 34, 44,  9,
       56, 10, 33, 41, 50, 52, 39, 26, 28, 43, 19, 37, 29, 11, 22,  1, 18,
       13,  0, 35, 48, 46, 17, 38,  3]), 'cur_cost': 257453.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([14, 24, 55, 47, 45, 40,  6, 49, 31, 21, 15,  5, 30,  7, 12, 32, 42,
       54, 27, 25, 57, 23,  2,  8, 36, 51, 53, 16,  4, 58, 20, 34, 44,  9,
       56, 10, 33, 41, 50, 52, 39, 26, 28, 43, 19, 37, 29, 11, 22,  1, 18,
       13,  0, 35, 48, 46, 17, 38,  3]), 'cur_cost': 250069.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([14, 45, 24, 55, 47, 40,  6, 49, 31, 21, 15,  5, 30,  7, 12, 32, 42,
       54, 27, 25, 57, 23,  2,  8, 36, 51, 53, 16,  4, 58, 20, 34, 44,  9,
       56, 10, 33, 41, 50, 52, 39, 26, 28, 43, 19, 37, 29, 11, 22,  1, 18,
       13,  0, 35, 48, 46, 17, 38,  3]), 'cur_cost': 257577.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:10,199 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 283787.00)
2025-08-05 09:52:10,199 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 09:52:10,200 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:10,200 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:10,200 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 204385.0
2025-08-05 09:52:10,206 - ExploitationExpert - INFO - res_population_num: 8
2025-08-05 09:52:10,206 - ExploitationExpert - INFO - res_population_costs: [24472, 24473, 24482, 24482, 24495, 24495, 24511, 24529.0]
2025-08-05 09:52:10,206 - ExploitationExpert - INFO - res_populations: [array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39, 40, 42,
       45, 41, 38, 36, 37, 43, 44,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 53, 48, 58, 52, 50, 54,
       49, 57, 56, 47, 55, 51, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  9,  4,  1,  6, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26,
       28, 39, 40, 42, 45, 41, 38, 36, 43, 37, 44, 46, 35, 54, 50, 53, 48,
       58, 52, 56, 47, 55, 51, 57, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11,
       12, 21, 19,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39,
       40, 46, 35, 44, 37, 36, 42, 45, 41, 38, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 15, 17, 12, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41, 38,
       36, 43, 37, 44, 46, 35,  2,  3], dtype=int64), array([ 0, 10,  6,  1,  7,  4,  9,  3,  5,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 17, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 15, 17, 12, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39, 40, 42, 45,
       41, 38, 36, 37, 43, 44,  2,  3], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 46, 35, 44, 37,
       36, 42, 45, 41, 38, 43,  2,  3], dtype=int64)]
2025-08-05 09:52:10,209 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:10,209 - ExploitationExpert - INFO - populations: [{'tour': array([20,  3, 31, 18, 58, 32, 37, 28, 51, 34, 53, 17, 47, 14, 40, 26,  7,
        4, 24,  0, 54, 49, 38, 21, 45, 10, 36, 39, 42, 52, 29, 57,  2, 41,
        8, 19, 46, 44, 13, 12, 55, 30, 56, 43,  6,  9, 50,  5, 27, 15, 23,
       16,  1, 25, 48, 11, 33, 22, 35], dtype=int64), 'cur_cost': 283787.0}, {'tour': array([17, 12, 38, 32, 45,  9, 58, 47, 55, 14, 20, 22, 43, 21,  6, 28, 33,
       23, 24, 16,  1, 40,  7, 37, 10, 39, 36, 53, 56, 50, 15, 11,  0, 41,
       57, 27, 34, 52,  5, 44, 46, 29, 35, 48,  4,  2, 49, 18, 13, 30, 19,
       42, 54, 51, 31, 25,  8, 26,  3], dtype=int64), 'cur_cost': 204385.0}, {'tour': [0, 3, 2, 5, 4, 9, 10, 6, 1, 7, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24908.0}, {'tour': [0, 9, 21, 6, 1, 7, 8, 5, 3, 2, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 19], 'cur_cost': 32398.0}, {'tour': [26, 39, 7, 31, 29, 42, 33, 23, 37, 30, 32, 46, 9, 18, 2, 21, 13, 28, 38, 1, 15, 8, 3, 25, 10, 0, 49, 19, 5, 57, 48, 6, 27, 43, 24, 41, 58, 56, 55, 22, 14, 51, 47, 17, 16, 34, 45, 44, 53, 54, 4, 12, 11, 20, 50, 52, 35, 36, 40], 'cur_cost': 183182.0}, {'tour': [43, 30, 27, 33, 42, 31, 1, 29, 9, 34, 32, 0, 5, 54, 10, 24, 40, 25, 28, 23, 37, 38, 35, 3, 49, 7, 14, 20, 11, 16, 4, 15, 6, 21, 17, 19, 51, 47, 58, 53, 13, 2, 50, 57, 22, 8, 52, 48, 56, 44, 46, 36, 41, 39, 45, 55, 18, 12, 26], 'cur_cost': 149073.0}, {'tour': [26, 44, 4, 33, 37, 0, 9, 31, 10, 21, 2, 23, 39, 29, 36, 24, 1, 34, 6, 17, 20, 12, 25, 42, 32, 38, 7, 19, 51, 52, 47, 55, 50, 58, 48, 22, 14, 18, 15, 30, 46, 35, 40, 27, 8, 54, 56, 3, 11, 16, 49, 5, 28, 43, 45, 41, 53, 57, 13], 'cur_cost': 176849.0}, {'tour': [25, 54, 49, 17, 13, 33, 3, 42, 30, 44, 48, 37, 4, 26, 24, 34, 56, 16, 53, 31, 35, 9, 1, 38, 6, 36, 41, 45, 23, 19, 47, 57, 27, 10, 18, 29, 7, 22, 32, 8, 52, 11, 21, 58, 46, 15, 39, 43, 0, 40, 50, 51, 12, 5, 20, 14, 28, 2, 55], 'cur_cost': 235214.0}, {'tour': [0, 22, 13, 2, 3, 5, 4, 9, 10, 6, 1, 7, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 18, 19, 21, 12, 11, 15, 17, 16, 20, 14], 'cur_cost': 32606.0}, {'tour': [0, 20, 12, 16, 17, 15, 11, 21, 22, 13, 18, 19, 14, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27221.0}]
2025-08-05 09:52:10,210 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:10,211 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 292, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 292, 'cache_hits': 0, 'similarity_calculations': 1449, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:10,212 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([17, 12, 38, 32, 45,  9, 58, 47, 55, 14, 20, 22, 43, 21,  6, 28, 33,
       23, 24, 16,  1, 40,  7, 37, 10, 39, 36, 53, 56, 50, 15, 11,  0, 41,
       57, 27, 34, 52,  5, 44, 46, 29, 35, 48,  4,  2, 49, 18, 13, 30, 19,
       42, 54, 51, 31, 25,  8, 26,  3], dtype=int64), 'cur_cost': 204385.0, 'intermediate_solutions': [{'tour': array([13, 19, 29,  4, 14,  8,  2,  1,  5, 24, 44, 35, 33, 34, 47, 28, 46,
       48, 31, 50, 26,  9, 17, 18, 45,  0, 20,  3, 12, 16, 38, 54,  6, 25,
       27, 41, 11, 30, 42, 22, 10, 36, 21, 49, 56, 52, 53, 39, 32,  7, 37,
       40, 43, 55, 15, 57, 23, 51, 58]), 'cur_cost': 235609.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 4, 13, 19, 29, 14,  8,  2,  1,  5, 24, 44, 35, 33, 34, 47, 28, 46,
       48, 31, 50, 26,  9, 17, 18, 45,  0, 20,  3, 12, 16, 38, 54,  6, 25,
       27, 41, 11, 30, 42, 22, 10, 36, 21, 49, 56, 52, 53, 39, 32,  7, 37,
       40, 43, 55, 15, 57, 23, 51, 58]), 'cur_cost': 235613.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14,  4, 13, 19, 29,  8,  2,  1,  5, 24, 44, 35, 33, 34, 47, 28, 46,
       48, 31, 50, 26,  9, 17, 18, 45,  0, 20,  3, 12, 16, 38, 54,  6, 25,
       27, 41, 11, 30, 42, 22, 10, 36, 21, 49, 56, 52, 53, 39, 32,  7, 37,
       40, 43, 55, 15, 57, 23, 51, 58]), 'cur_cost': 235622.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([29,  4, 13, 19, 14,  8,  2,  1,  5, 24, 44, 35, 33, 34, 47, 28, 46,
       48, 31, 50, 26,  9, 17, 18, 45,  0, 20,  3, 12, 16, 38, 54,  6, 25,
       27, 41, 11, 30, 42, 22, 10, 36, 21, 49, 56, 52, 53, 39, 32,  7, 37,
       40, 43, 55, 15, 57, 23, 51, 58]), 'cur_cost': 232346.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([29, 14,  4, 13, 19,  8,  2,  1,  5, 24, 44, 35, 33, 34, 47, 28, 46,
       48, 31, 50, 26,  9, 17, 18, 45,  0, 20,  3, 12, 16, 38, 54,  6, 25,
       27, 41, 11, 30, 42, 22, 10, 36, 21, 49, 56, 52, 53, 39, 32,  7, 37,
       40, 43, 55, 15, 57, 23, 51, 58]), 'cur_cost': 237743.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:10,212 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 204385.00)
2025-08-05 09:52:10,212 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:10,212 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:10,212 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:10,214 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 09:52:10,214 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,215 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,215 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,215 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,215 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27143.0, 路径长度: 59, 收集中间解: 3
2025-08-05 09:52:10,215 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 2, 5, 16, 17, 15, 11, 12, 21, 22, 13, 18, 19, 14, 20, 6, 1, 7, 8, 4, 9, 3, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27143.0, 'intermediate_solutions': [{'tour': [0, 3, 2, 5, 4, 9, 10, 6, 1, 7, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 19, 22, 14, 20, 16, 17, 15, 11, 12, 21, 18], 'cur_cost': 24996.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 2, 5, 25, 30, 26, 28, 8, 7, 1, 6, 10, 9, 4, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 31808.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 2, 5, 4, 9, 10, 6, 1, 7, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 45, 12, 21, 19], 'cur_cost': 40415.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:10,216 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 27143.00)
2025-08-05 09:52:10,216 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:52:10,216 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:52:10,216 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:10,221 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 59
2025-08-05 09:52:10,221 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,221 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,221 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,221 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,221 - ExplorationExpert - INFO - 探索路径生成完成，成本: 139595.0, 路径长度: 59, 收集中间解: 3
2025-08-05 09:52:10,222 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [56, 6, 8, 33, 25, 35, 0, 23, 42, 41, 30, 32, 36, 29, 26, 38, 43, 39, 7, 24, 40, 37, 1, 31, 4, 28, 11, 3, 22, 16, 18, 5, 9, 12, 27, 45, 50, 52, 54, 58, 48, 55, 51, 53, 49, 19, 21, 15, 20, 13, 2, 46, 44, 34, 17, 14, 10, 57, 47], 'cur_cost': 139595.0, 'intermediate_solutions': [{'tour': [0, 9, 21, 6, 1, 7, 8, 33, 3, 2, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 5, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 19], 'cur_cost': 41585.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 21, 6, 1, 7, 8, 5, 3, 2, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 15, 17, 16, 20, 14, 22, 18, 13, 47, 50, 54, 49, 51, 57, 55, 56, 52, 58, 48, 53, 39, 40, 38, 41, 45, 11, 12, 19], 'cur_cost': 47881.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 53, 9, 21, 6, 1, 7, 8, 5, 3, 2, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 19], 'cur_cost': 40147.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:10,222 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 139595.00)
2025-08-05 09:52:10,222 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:10,222 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:10,222 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:10,223 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 59
2025-08-05 09:52:10,224 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,224 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,224 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,224 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,224 - ExplorationExpert - INFO - 探索路径生成完成，成本: 211556.0, 路径长度: 59, 收集中间解: 3
2025-08-05 09:52:10,225 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [2, 22, 46, 7, 3, 35, 25, 4, 11, 57, 5, 6, 12, 56, 48, 51, 58, 49, 19, 15, 20, 16, 52, 36, 38, 14, 41, 28, 32, 24, 9, 40, 29, 1, 33, 13, 23, 27, 43, 45, 30, 21, 39, 26, 17, 10, 55, 54, 37, 31, 42, 53, 50, 0, 34, 44, 18, 8, 47], 'cur_cost': 211556.0, 'intermediate_solutions': [{'tour': [26, 39, 7, 31, 29, 42, 33, 23, 37, 30, 32, 46, 9, 13, 2, 21, 18, 28, 38, 1, 15, 8, 3, 25, 10, 0, 49, 19, 5, 57, 48, 6, 27, 43, 24, 41, 58, 56, 55, 22, 14, 51, 47, 17, 16, 34, 45, 44, 53, 54, 4, 12, 11, 20, 50, 52, 35, 36, 40], 'cur_cost': 183176.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [26, 39, 7, 31, 29, 42, 33, 23, 37, 30, 32, 46, 9, 18, 2, 21, 13, 28, 38, 1, 15, 8, 3, 25, 10, 0, 49, 19, 5, 57, 48, 6, 27, 43, 24, 41, 58, 56, 55, 22, 14, 51, 47, 17, 16, 34, 45, 44, 53, 54, 4, 12, 11, 20, 50, 52, 40, 36, 35], 'cur_cost': 183218.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [26, 39, 7, 31, 29, 33, 23, 37, 30, 32, 46, 9, 18, 2, 21, 13, 28, 38, 1, 15, 8, 3, 25, 10, 0, 49, 19, 5, 57, 48, 6, 27, 43, 24, 41, 58, 56, 55, 22, 14, 51, 47, 17, 16, 34, 45, 44, 53, 54, 4, 12, 11, 20, 50, 42, 52, 35, 36, 40], 'cur_cost': 184644.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:10,225 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 211556.00)
2025-08-05 09:52:10,225 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:10,225 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:10,225 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:10,226 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 59
2025-08-05 09:52:10,226 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,227 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,227 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,227 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,227 - ExplorationExpert - INFO - 探索路径生成完成，成本: 240745.0, 路径长度: 59, 收集中间解: 3
2025-08-05 09:52:10,227 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [22, 19, 56, 8, 52, 36, 11, 3, 5, 1, 27, 13, 18, 43, 10, 21, 15, 0, 38, 46, 41, 33, 14, 24, 57, 54, 45, 35, 58, 25, 30, 26, 48, 44, 28, 17, 40, 29, 53, 23, 2, 50, 42, 34, 20, 32, 7, 37, 31, 55, 6, 9, 51, 12, 47, 39, 49, 4, 16], 'cur_cost': 240745.0, 'intermediate_solutions': [{'tour': [43, 30, 27, 33, 42, 31, 1, 29, 9, 34, 32, 0, 5, 54, 10, 24, 40, 25, 28, 23, 37, 38, 35, 3, 49, 7, 14, 20, 11, 16, 4, 15, 6, 21, 17, 52, 51, 47, 58, 53, 13, 2, 50, 57, 22, 8, 19, 48, 56, 44, 46, 36, 41, 39, 45, 55, 18, 12, 26], 'cur_cost': 154650.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [43, 30, 27, 33, 42, 31, 1, 29, 9, 34, 17, 21, 6, 15, 4, 16, 11, 20, 14, 7, 49, 3, 35, 38, 37, 23, 28, 25, 40, 24, 10, 54, 5, 0, 32, 19, 51, 47, 58, 53, 13, 2, 50, 57, 22, 8, 52, 48, 56, 44, 46, 36, 41, 39, 45, 55, 18, 12, 26], 'cur_cost': 159118.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [43, 30, 24, 27, 33, 42, 31, 1, 29, 9, 34, 32, 0, 5, 54, 10, 40, 25, 28, 23, 37, 38, 35, 3, 49, 7, 14, 20, 11, 16, 4, 15, 6, 21, 17, 19, 51, 47, 58, 53, 13, 2, 50, 57, 22, 8, 52, 48, 56, 44, 46, 36, 41, 39, 45, 55, 18, 12, 26], 'cur_cost': 144760.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:10,228 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 240745.00)
2025-08-05 09:52:10,228 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:10,228 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:10,228 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:10,234 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 59
2025-08-05 09:52:10,234 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,235 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,235 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,235 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,235 - ExplorationExpert - INFO - 探索路径生成完成，成本: 157248.0, 路径长度: 59, 收集中间解: 3
2025-08-05 09:52:10,235 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [42, 35, 5, 13, 26, 40, 33, 30, 6, 0, 1, 10, 11, 21, 22, 9, 29, 24, 41, 31, 34, 4, 23, 36, 2, 7, 25, 38, 32, 28, 46, 3, 12, 8, 14, 55, 52, 48, 58, 53, 49, 57, 54, 17, 16, 51, 50, 43, 39, 37, 56, 47, 20, 19, 18, 27, 44, 45, 15], 'cur_cost': 157248.0, 'intermediate_solutions': [{'tour': [45, 44, 4, 33, 37, 0, 9, 31, 10, 21, 2, 23, 39, 29, 36, 24, 1, 34, 6, 17, 20, 12, 25, 42, 32, 38, 7, 19, 51, 52, 47, 55, 50, 58, 48, 22, 14, 18, 15, 30, 46, 35, 40, 27, 8, 54, 56, 3, 11, 16, 49, 5, 28, 43, 26, 41, 53, 57, 13], 'cur_cost': 184458.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [26, 44, 4, 33, 37, 0, 9, 31, 10, 21, 2, 23, 39, 29, 36, 24, 1, 34, 6, 17, 20, 12, 25, 42, 32, 38, 7, 19, 51, 52, 47, 55, 50, 58, 48, 22, 14, 18, 53, 41, 45, 43, 28, 5, 49, 16, 11, 3, 56, 54, 8, 27, 40, 35, 46, 30, 15, 57, 13], 'cur_cost': 187883.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [26, 44, 4, 33, 37, 0, 9, 31, 10, 21, 2, 23, 39, 29, 36, 24, 1, 34, 6, 17, 20, 12, 25, 42, 32, 38, 7, 19, 51, 52, 47, 55, 50, 58, 48, 22, 14, 18, 15, 30, 46, 35, 40, 27, 8, 54, 56, 3, 11, 16, 49, 5, 28, 45, 43, 41, 53, 57, 13], 'cur_cost': 176898.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:10,236 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 157248.00)
2025-08-05 09:52:10,236 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 09:52:10,236 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:10,236 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:10,236 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 233160.0
2025-08-05 09:52:10,244 - ExploitationExpert - INFO - res_population_num: 9
2025-08-05 09:52:10,244 - ExploitationExpert - INFO - res_population_costs: [24472, 24473, 24482, 24482, 24495, 24495, 24511, 24529.0, 24451.0]
2025-08-05 09:52:10,244 - ExploitationExpert - INFO - res_populations: [array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39, 40, 42,
       45, 41, 38, 36, 37, 43, 44,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 53, 48, 58, 52, 50, 54,
       49, 57, 56, 47, 55, 51, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  9,  4,  1,  6, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26,
       28, 39, 40, 42, 45, 41, 38, 36, 43, 37, 44, 46, 35, 54, 50, 53, 48,
       58, 52, 56, 47, 55, 51, 57, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11,
       12, 21, 19,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39,
       40, 46, 35, 44, 37, 36, 42, 45, 41, 38, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 15, 17, 12, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41, 38,
       36, 43, 37, 44, 46, 35,  2,  3], dtype=int64), array([ 0, 10,  6,  1,  7,  4,  9,  3,  5,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 17, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 15, 17, 12, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39, 40, 42, 45,
       41, 38, 36, 37, 43, 44,  2,  3], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 46, 35, 44, 37,
       36, 42, 45, 41, 38, 43,  2,  3], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64)]
2025-08-05 09:52:10,247 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:10,248 - ExploitationExpert - INFO - populations: [{'tour': array([20,  3, 31, 18, 58, 32, 37, 28, 51, 34, 53, 17, 47, 14, 40, 26,  7,
        4, 24,  0, 54, 49, 38, 21, 45, 10, 36, 39, 42, 52, 29, 57,  2, 41,
        8, 19, 46, 44, 13, 12, 55, 30, 56, 43,  6,  9, 50,  5, 27, 15, 23,
       16,  1, 25, 48, 11, 33, 22, 35], dtype=int64), 'cur_cost': 283787.0}, {'tour': array([17, 12, 38, 32, 45,  9, 58, 47, 55, 14, 20, 22, 43, 21,  6, 28, 33,
       23, 24, 16,  1, 40,  7, 37, 10, 39, 36, 53, 56, 50, 15, 11,  0, 41,
       57, 27, 34, 52,  5, 44, 46, 29, 35, 48,  4,  2, 49, 18, 13, 30, 19,
       42, 54, 51, 31, 25,  8, 26,  3], dtype=int64), 'cur_cost': 204385.0}, {'tour': [0, 2, 5, 16, 17, 15, 11, 12, 21, 22, 13, 18, 19, 14, 20, 6, 1, 7, 8, 4, 9, 3, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27143.0}, {'tour': [56, 6, 8, 33, 25, 35, 0, 23, 42, 41, 30, 32, 36, 29, 26, 38, 43, 39, 7, 24, 40, 37, 1, 31, 4, 28, 11, 3, 22, 16, 18, 5, 9, 12, 27, 45, 50, 52, 54, 58, 48, 55, 51, 53, 49, 19, 21, 15, 20, 13, 2, 46, 44, 34, 17, 14, 10, 57, 47], 'cur_cost': 139595.0}, {'tour': [2, 22, 46, 7, 3, 35, 25, 4, 11, 57, 5, 6, 12, 56, 48, 51, 58, 49, 19, 15, 20, 16, 52, 36, 38, 14, 41, 28, 32, 24, 9, 40, 29, 1, 33, 13, 23, 27, 43, 45, 30, 21, 39, 26, 17, 10, 55, 54, 37, 31, 42, 53, 50, 0, 34, 44, 18, 8, 47], 'cur_cost': 211556.0}, {'tour': [22, 19, 56, 8, 52, 36, 11, 3, 5, 1, 27, 13, 18, 43, 10, 21, 15, 0, 38, 46, 41, 33, 14, 24, 57, 54, 45, 35, 58, 25, 30, 26, 48, 44, 28, 17, 40, 29, 53, 23, 2, 50, 42, 34, 20, 32, 7, 37, 31, 55, 6, 9, 51, 12, 47, 39, 49, 4, 16], 'cur_cost': 240745.0}, {'tour': [42, 35, 5, 13, 26, 40, 33, 30, 6, 0, 1, 10, 11, 21, 22, 9, 29, 24, 41, 31, 34, 4, 23, 36, 2, 7, 25, 38, 32, 28, 46, 3, 12, 8, 14, 55, 52, 48, 58, 53, 49, 57, 54, 17, 16, 51, 50, 43, 39, 37, 56, 47, 20, 19, 18, 27, 44, 45, 15], 'cur_cost': 157248.0}, {'tour': array([40,  1, 46, 55, 42, 32,  7, 31,  0, 21, 13, 24, 17, 52, 51, 58, 49,
        8, 56, 36, 57, 26, 29, 47, 27, 44, 18, 54,  9, 39, 53,  3, 25, 15,
       50,  4,  5, 19, 30, 38, 23, 20, 16, 34, 22,  6, 12, 41, 43, 10, 35,
        2, 11, 14, 28, 33, 48, 45, 37], dtype=int64), 'cur_cost': 233160.0}, {'tour': [0, 22, 13, 2, 3, 5, 4, 9, 10, 6, 1, 7, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 18, 19, 21, 12, 11, 15, 17, 16, 20, 14], 'cur_cost': 32606.0}, {'tour': [0, 20, 12, 16, 17, 15, 11, 21, 22, 13, 18, 19, 14, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27221.0}]
2025-08-05 09:52:10,249 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:10,249 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 293, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 293, 'cache_hits': 0, 'similarity_calculations': 1455, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:10,250 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([40,  1, 46, 55, 42, 32,  7, 31,  0, 21, 13, 24, 17, 52, 51, 58, 49,
        8, 56, 36, 57, 26, 29, 47, 27, 44, 18, 54,  9, 39, 53,  3, 25, 15,
       50,  4,  5, 19, 30, 38, 23, 20, 16, 34, 22,  6, 12, 41, 43, 10, 35,
        2, 11, 14, 28, 33, 48, 45, 37], dtype=int64), 'cur_cost': 233160.0, 'intermediate_solutions': [{'tour': array([49, 54, 25, 17, 13, 33,  3, 42, 30, 44, 48, 37,  4, 26, 24, 34, 56,
       16, 53, 31, 35,  9,  1, 38,  6, 36, 41, 45, 23, 19, 47, 57, 27, 10,
       18, 29,  7, 22, 32,  8, 52, 11, 21, 58, 46, 15, 39, 43,  0, 40, 50,
       51, 12,  5, 20, 14, 28,  2, 55]), 'cur_cost': 227469.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([17, 49, 54, 25, 13, 33,  3, 42, 30, 44, 48, 37,  4, 26, 24, 34, 56,
       16, 53, 31, 35,  9,  1, 38,  6, 36, 41, 45, 23, 19, 47, 57, 27, 10,
       18, 29,  7, 22, 32,  8, 52, 11, 21, 58, 46, 15, 39, 43,  0, 40, 50,
       51, 12,  5, 20, 14, 28,  2, 55]), 'cur_cost': 238553.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([13, 17, 49, 54, 25, 33,  3, 42, 30, 44, 48, 37,  4, 26, 24, 34, 56,
       16, 53, 31, 35,  9,  1, 38,  6, 36, 41, 45, 23, 19, 47, 57, 27, 10,
       18, 29,  7, 22, 32,  8, 52, 11, 21, 58, 46, 15, 39, 43,  0, 40, 50,
       51, 12,  5, 20, 14, 28,  2, 55]), 'cur_cost': 228357.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([25, 17, 49, 54, 13, 33,  3, 42, 30, 44, 48, 37,  4, 26, 24, 34, 56,
       16, 53, 31, 35,  9,  1, 38,  6, 36, 41, 45, 23, 19, 47, 57, 27, 10,
       18, 29,  7, 22, 32,  8, 52, 11, 21, 58, 46, 15, 39, 43,  0, 40, 50,
       51, 12,  5, 20, 14, 28,  2, 55]), 'cur_cost': 238496.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([25, 13, 17, 49, 54, 33,  3, 42, 30, 44, 48, 37,  4, 26, 24, 34, 56,
       16, 53, 31, 35,  9,  1, 38,  6, 36, 41, 45, 23, 19, 47, 57, 27, 10,
       18, 29,  7, 22, 32,  8, 52, 11, 21, 58, 46, 15, 39, 43,  0, 40, 50,
       51, 12,  5, 20, 14, 28,  2, 55]), 'cur_cost': 235215.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:10,250 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 233160.00)
2025-08-05 09:52:10,250 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:52:10,250 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:52:10,250 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:10,252 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 09:52:10,252 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,253 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,253 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,253 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,253 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32438.0, 路径长度: 59, 收集中间解: 3
2025-08-05 09:52:10,253 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 8, 13, 10, 9, 4, 3, 5, 2, 1, 7, 6, 33, 31, 27, 34, 23, 25, 32, 29, 30, 26, 28, 24, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 32438.0, 'intermediate_solutions': [{'tour': [0, 22, 13, 2, 3, 5, 4, 9, 10, 6, 1, 7, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 40, 38, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 18, 19, 21, 12, 11, 15, 17, 16, 20, 14], 'cur_cost': 32722.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 22, 13, 2, 3, 5, 4, 9, 10, 6, 1, 7, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 17, 15, 11, 12, 21, 19, 18, 47, 16, 20, 14], 'cur_cost': 43766.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 22, 13, 2, 3, 5, 4, 9, 10, 6, 1, 7, 8, 28, 26, 30, 33, 25, 32, 29, 24, 31, 27, 34, 23, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 18, 19, 21, 12, 11, 15, 17, 16, 20, 14], 'cur_cost': 32681.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:10,254 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 32438.00)
2025-08-05 09:52:10,254 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:10,254 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:10,254 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:10,256 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 09:52:10,256 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,256 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,256 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,256 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,257 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27031.0, 路径长度: 59, 收集中间解: 3
2025-08-05 09:52:10,257 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 4, 12, 19, 18, 13, 22, 14, 20, 16, 17, 15, 11, 21, 6, 1, 7, 8, 5, 3, 2, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27031.0, 'intermediate_solutions': [{'tour': [0, 20, 12, 16, 17, 15, 11, 21, 22, 13, 18, 19, 41, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 14, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 50626.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 12, 16, 17, 15, 11, 21, 22, 13, 18, 19, 14, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 48, 53, 39, 40, 38, 41, 45, 42, 36, 43, 37, 44, 46, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 38494.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 12, 17, 15, 11, 21, 22, 13, 18, 19, 14, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 16, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 42687.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:10,257 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 27031.00)
2025-08-05 09:52:10,257 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:10,257 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:10,259 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([20,  3, 31, 18, 58, 32, 37, 28, 51, 34, 53, 17, 47, 14, 40, 26,  7,
        4, 24,  0, 54, 49, 38, 21, 45, 10, 36, 39, 42, 52, 29, 57,  2, 41,
        8, 19, 46, 44, 13, 12, 55, 30, 56, 43,  6,  9, 50,  5, 27, 15, 23,
       16,  1, 25, 48, 11, 33, 22, 35], dtype=int64), 'cur_cost': 283787.0, 'intermediate_solutions': [{'tour': array([55, 47, 14, 24, 45, 40,  6, 49, 31, 21, 15,  5, 30,  7, 12, 32, 42,
       54, 27, 25, 57, 23,  2,  8, 36, 51, 53, 16,  4, 58, 20, 34, 44,  9,
       56, 10, 33, 41, 50, 52, 39, 26, 28, 43, 19, 37, 29, 11, 22,  1, 18,
       13,  0, 35, 48, 46, 17, 38,  3]), 'cur_cost': 247534.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([24, 55, 47, 14, 45, 40,  6, 49, 31, 21, 15,  5, 30,  7, 12, 32, 42,
       54, 27, 25, 57, 23,  2,  8, 36, 51, 53, 16,  4, 58, 20, 34, 44,  9,
       56, 10, 33, 41, 50, 52, 39, 26, 28, 43, 19, 37, 29, 11, 22,  1, 18,
       13,  0, 35, 48, 46, 17, 38,  3]), 'cur_cost': 252075.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([45, 24, 55, 47, 14, 40,  6, 49, 31, 21, 15,  5, 30,  7, 12, 32, 42,
       54, 27, 25, 57, 23,  2,  8, 36, 51, 53, 16,  4, 58, 20, 34, 44,  9,
       56, 10, 33, 41, 50, 52, 39, 26, 28, 43, 19, 37, 29, 11, 22,  1, 18,
       13,  0, 35, 48, 46, 17, 38,  3]), 'cur_cost': 257453.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([14, 24, 55, 47, 45, 40,  6, 49, 31, 21, 15,  5, 30,  7, 12, 32, 42,
       54, 27, 25, 57, 23,  2,  8, 36, 51, 53, 16,  4, 58, 20, 34, 44,  9,
       56, 10, 33, 41, 50, 52, 39, 26, 28, 43, 19, 37, 29, 11, 22,  1, 18,
       13,  0, 35, 48, 46, 17, 38,  3]), 'cur_cost': 250069.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([14, 45, 24, 55, 47, 40,  6, 49, 31, 21, 15,  5, 30,  7, 12, 32, 42,
       54, 27, 25, 57, 23,  2,  8, 36, 51, 53, 16,  4, 58, 20, 34, 44,  9,
       56, 10, 33, 41, 50, 52, 39, 26, 28, 43, 19, 37, 29, 11, 22,  1, 18,
       13,  0, 35, 48, 46, 17, 38,  3]), 'cur_cost': 257577.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([17, 12, 38, 32, 45,  9, 58, 47, 55, 14, 20, 22, 43, 21,  6, 28, 33,
       23, 24, 16,  1, 40,  7, 37, 10, 39, 36, 53, 56, 50, 15, 11,  0, 41,
       57, 27, 34, 52,  5, 44, 46, 29, 35, 48,  4,  2, 49, 18, 13, 30, 19,
       42, 54, 51, 31, 25,  8, 26,  3], dtype=int64), 'cur_cost': 204385.0, 'intermediate_solutions': [{'tour': array([13, 19, 29,  4, 14,  8,  2,  1,  5, 24, 44, 35, 33, 34, 47, 28, 46,
       48, 31, 50, 26,  9, 17, 18, 45,  0, 20,  3, 12, 16, 38, 54,  6, 25,
       27, 41, 11, 30, 42, 22, 10, 36, 21, 49, 56, 52, 53, 39, 32,  7, 37,
       40, 43, 55, 15, 57, 23, 51, 58]), 'cur_cost': 235609.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 4, 13, 19, 29, 14,  8,  2,  1,  5, 24, 44, 35, 33, 34, 47, 28, 46,
       48, 31, 50, 26,  9, 17, 18, 45,  0, 20,  3, 12, 16, 38, 54,  6, 25,
       27, 41, 11, 30, 42, 22, 10, 36, 21, 49, 56, 52, 53, 39, 32,  7, 37,
       40, 43, 55, 15, 57, 23, 51, 58]), 'cur_cost': 235613.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14,  4, 13, 19, 29,  8,  2,  1,  5, 24, 44, 35, 33, 34, 47, 28, 46,
       48, 31, 50, 26,  9, 17, 18, 45,  0, 20,  3, 12, 16, 38, 54,  6, 25,
       27, 41, 11, 30, 42, 22, 10, 36, 21, 49, 56, 52, 53, 39, 32,  7, 37,
       40, 43, 55, 15, 57, 23, 51, 58]), 'cur_cost': 235622.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([29,  4, 13, 19, 14,  8,  2,  1,  5, 24, 44, 35, 33, 34, 47, 28, 46,
       48, 31, 50, 26,  9, 17, 18, 45,  0, 20,  3, 12, 16, 38, 54,  6, 25,
       27, 41, 11, 30, 42, 22, 10, 36, 21, 49, 56, 52, 53, 39, 32,  7, 37,
       40, 43, 55, 15, 57, 23, 51, 58]), 'cur_cost': 232346.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([29, 14,  4, 13, 19,  8,  2,  1,  5, 24, 44, 35, 33, 34, 47, 28, 46,
       48, 31, 50, 26,  9, 17, 18, 45,  0, 20,  3, 12, 16, 38, 54,  6, 25,
       27, 41, 11, 30, 42, 22, 10, 36, 21, 49, 56, 52, 53, 39, 32,  7, 37,
       40, 43, 55, 15, 57, 23, 51, 58]), 'cur_cost': 237743.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 5, 16, 17, 15, 11, 12, 21, 22, 13, 18, 19, 14, 20, 6, 1, 7, 8, 4, 9, 3, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27143.0, 'intermediate_solutions': [{'tour': [0, 3, 2, 5, 4, 9, 10, 6, 1, 7, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 19, 22, 14, 20, 16, 17, 15, 11, 12, 21, 18], 'cur_cost': 24996.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 2, 5, 25, 30, 26, 28, 8, 7, 1, 6, 10, 9, 4, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 31808.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 2, 5, 4, 9, 10, 6, 1, 7, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 45, 12, 21, 19], 'cur_cost': 40415.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [56, 6, 8, 33, 25, 35, 0, 23, 42, 41, 30, 32, 36, 29, 26, 38, 43, 39, 7, 24, 40, 37, 1, 31, 4, 28, 11, 3, 22, 16, 18, 5, 9, 12, 27, 45, 50, 52, 54, 58, 48, 55, 51, 53, 49, 19, 21, 15, 20, 13, 2, 46, 44, 34, 17, 14, 10, 57, 47], 'cur_cost': 139595.0, 'intermediate_solutions': [{'tour': [0, 9, 21, 6, 1, 7, 8, 33, 3, 2, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 5, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 19], 'cur_cost': 41585.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 21, 6, 1, 7, 8, 5, 3, 2, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 15, 17, 16, 20, 14, 22, 18, 13, 47, 50, 54, 49, 51, 57, 55, 56, 52, 58, 48, 53, 39, 40, 38, 41, 45, 11, 12, 19], 'cur_cost': 47881.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 53, 9, 21, 6, 1, 7, 8, 5, 3, 2, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 19], 'cur_cost': 40147.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [2, 22, 46, 7, 3, 35, 25, 4, 11, 57, 5, 6, 12, 56, 48, 51, 58, 49, 19, 15, 20, 16, 52, 36, 38, 14, 41, 28, 32, 24, 9, 40, 29, 1, 33, 13, 23, 27, 43, 45, 30, 21, 39, 26, 17, 10, 55, 54, 37, 31, 42, 53, 50, 0, 34, 44, 18, 8, 47], 'cur_cost': 211556.0, 'intermediate_solutions': [{'tour': [26, 39, 7, 31, 29, 42, 33, 23, 37, 30, 32, 46, 9, 13, 2, 21, 18, 28, 38, 1, 15, 8, 3, 25, 10, 0, 49, 19, 5, 57, 48, 6, 27, 43, 24, 41, 58, 56, 55, 22, 14, 51, 47, 17, 16, 34, 45, 44, 53, 54, 4, 12, 11, 20, 50, 52, 35, 36, 40], 'cur_cost': 183176.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [26, 39, 7, 31, 29, 42, 33, 23, 37, 30, 32, 46, 9, 18, 2, 21, 13, 28, 38, 1, 15, 8, 3, 25, 10, 0, 49, 19, 5, 57, 48, 6, 27, 43, 24, 41, 58, 56, 55, 22, 14, 51, 47, 17, 16, 34, 45, 44, 53, 54, 4, 12, 11, 20, 50, 52, 40, 36, 35], 'cur_cost': 183218.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [26, 39, 7, 31, 29, 33, 23, 37, 30, 32, 46, 9, 18, 2, 21, 13, 28, 38, 1, 15, 8, 3, 25, 10, 0, 49, 19, 5, 57, 48, 6, 27, 43, 24, 41, 58, 56, 55, 22, 14, 51, 47, 17, 16, 34, 45, 44, 53, 54, 4, 12, 11, 20, 50, 42, 52, 35, 36, 40], 'cur_cost': 184644.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [22, 19, 56, 8, 52, 36, 11, 3, 5, 1, 27, 13, 18, 43, 10, 21, 15, 0, 38, 46, 41, 33, 14, 24, 57, 54, 45, 35, 58, 25, 30, 26, 48, 44, 28, 17, 40, 29, 53, 23, 2, 50, 42, 34, 20, 32, 7, 37, 31, 55, 6, 9, 51, 12, 47, 39, 49, 4, 16], 'cur_cost': 240745.0, 'intermediate_solutions': [{'tour': [43, 30, 27, 33, 42, 31, 1, 29, 9, 34, 32, 0, 5, 54, 10, 24, 40, 25, 28, 23, 37, 38, 35, 3, 49, 7, 14, 20, 11, 16, 4, 15, 6, 21, 17, 52, 51, 47, 58, 53, 13, 2, 50, 57, 22, 8, 19, 48, 56, 44, 46, 36, 41, 39, 45, 55, 18, 12, 26], 'cur_cost': 154650.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [43, 30, 27, 33, 42, 31, 1, 29, 9, 34, 17, 21, 6, 15, 4, 16, 11, 20, 14, 7, 49, 3, 35, 38, 37, 23, 28, 25, 40, 24, 10, 54, 5, 0, 32, 19, 51, 47, 58, 53, 13, 2, 50, 57, 22, 8, 52, 48, 56, 44, 46, 36, 41, 39, 45, 55, 18, 12, 26], 'cur_cost': 159118.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [43, 30, 24, 27, 33, 42, 31, 1, 29, 9, 34, 32, 0, 5, 54, 10, 40, 25, 28, 23, 37, 38, 35, 3, 49, 7, 14, 20, 11, 16, 4, 15, 6, 21, 17, 19, 51, 47, 58, 53, 13, 2, 50, 57, 22, 8, 52, 48, 56, 44, 46, 36, 41, 39, 45, 55, 18, 12, 26], 'cur_cost': 144760.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [42, 35, 5, 13, 26, 40, 33, 30, 6, 0, 1, 10, 11, 21, 22, 9, 29, 24, 41, 31, 34, 4, 23, 36, 2, 7, 25, 38, 32, 28, 46, 3, 12, 8, 14, 55, 52, 48, 58, 53, 49, 57, 54, 17, 16, 51, 50, 43, 39, 37, 56, 47, 20, 19, 18, 27, 44, 45, 15], 'cur_cost': 157248.0, 'intermediate_solutions': [{'tour': [45, 44, 4, 33, 37, 0, 9, 31, 10, 21, 2, 23, 39, 29, 36, 24, 1, 34, 6, 17, 20, 12, 25, 42, 32, 38, 7, 19, 51, 52, 47, 55, 50, 58, 48, 22, 14, 18, 15, 30, 46, 35, 40, 27, 8, 54, 56, 3, 11, 16, 49, 5, 28, 43, 26, 41, 53, 57, 13], 'cur_cost': 184458.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [26, 44, 4, 33, 37, 0, 9, 31, 10, 21, 2, 23, 39, 29, 36, 24, 1, 34, 6, 17, 20, 12, 25, 42, 32, 38, 7, 19, 51, 52, 47, 55, 50, 58, 48, 22, 14, 18, 53, 41, 45, 43, 28, 5, 49, 16, 11, 3, 56, 54, 8, 27, 40, 35, 46, 30, 15, 57, 13], 'cur_cost': 187883.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [26, 44, 4, 33, 37, 0, 9, 31, 10, 21, 2, 23, 39, 29, 36, 24, 1, 34, 6, 17, 20, 12, 25, 42, 32, 38, 7, 19, 51, 52, 47, 55, 50, 58, 48, 22, 14, 18, 15, 30, 46, 35, 40, 27, 8, 54, 56, 3, 11, 16, 49, 5, 28, 45, 43, 41, 53, 57, 13], 'cur_cost': 176898.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([40,  1, 46, 55, 42, 32,  7, 31,  0, 21, 13, 24, 17, 52, 51, 58, 49,
        8, 56, 36, 57, 26, 29, 47, 27, 44, 18, 54,  9, 39, 53,  3, 25, 15,
       50,  4,  5, 19, 30, 38, 23, 20, 16, 34, 22,  6, 12, 41, 43, 10, 35,
        2, 11, 14, 28, 33, 48, 45, 37], dtype=int64), 'cur_cost': 233160.0, 'intermediate_solutions': [{'tour': array([49, 54, 25, 17, 13, 33,  3, 42, 30, 44, 48, 37,  4, 26, 24, 34, 56,
       16, 53, 31, 35,  9,  1, 38,  6, 36, 41, 45, 23, 19, 47, 57, 27, 10,
       18, 29,  7, 22, 32,  8, 52, 11, 21, 58, 46, 15, 39, 43,  0, 40, 50,
       51, 12,  5, 20, 14, 28,  2, 55]), 'cur_cost': 227469.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([17, 49, 54, 25, 13, 33,  3, 42, 30, 44, 48, 37,  4, 26, 24, 34, 56,
       16, 53, 31, 35,  9,  1, 38,  6, 36, 41, 45, 23, 19, 47, 57, 27, 10,
       18, 29,  7, 22, 32,  8, 52, 11, 21, 58, 46, 15, 39, 43,  0, 40, 50,
       51, 12,  5, 20, 14, 28,  2, 55]), 'cur_cost': 238553.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([13, 17, 49, 54, 25, 33,  3, 42, 30, 44, 48, 37,  4, 26, 24, 34, 56,
       16, 53, 31, 35,  9,  1, 38,  6, 36, 41, 45, 23, 19, 47, 57, 27, 10,
       18, 29,  7, 22, 32,  8, 52, 11, 21, 58, 46, 15, 39, 43,  0, 40, 50,
       51, 12,  5, 20, 14, 28,  2, 55]), 'cur_cost': 228357.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([25, 17, 49, 54, 13, 33,  3, 42, 30, 44, 48, 37,  4, 26, 24, 34, 56,
       16, 53, 31, 35,  9,  1, 38,  6, 36, 41, 45, 23, 19, 47, 57, 27, 10,
       18, 29,  7, 22, 32,  8, 52, 11, 21, 58, 46, 15, 39, 43,  0, 40, 50,
       51, 12,  5, 20, 14, 28,  2, 55]), 'cur_cost': 238496.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([25, 13, 17, 49, 54, 33,  3, 42, 30, 44, 48, 37,  4, 26, 24, 34, 56,
       16, 53, 31, 35,  9,  1, 38,  6, 36, 41, 45, 23, 19, 47, 57, 27, 10,
       18, 29,  7, 22, 32,  8, 52, 11, 21, 58, 46, 15, 39, 43,  0, 40, 50,
       51, 12,  5, 20, 14, 28,  2, 55]), 'cur_cost': 235215.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 13, 10, 9, 4, 3, 5, 2, 1, 7, 6, 33, 31, 27, 34, 23, 25, 32, 29, 30, 26, 28, 24, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 32438.0, 'intermediate_solutions': [{'tour': [0, 22, 13, 2, 3, 5, 4, 9, 10, 6, 1, 7, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 40, 38, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 18, 19, 21, 12, 11, 15, 17, 16, 20, 14], 'cur_cost': 32722.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 22, 13, 2, 3, 5, 4, 9, 10, 6, 1, 7, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 17, 15, 11, 12, 21, 19, 18, 47, 16, 20, 14], 'cur_cost': 43766.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 22, 13, 2, 3, 5, 4, 9, 10, 6, 1, 7, 8, 28, 26, 30, 33, 25, 32, 29, 24, 31, 27, 34, 23, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 18, 19, 21, 12, 11, 15, 17, 16, 20, 14], 'cur_cost': 32681.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 12, 19, 18, 13, 22, 14, 20, 16, 17, 15, 11, 21, 6, 1, 7, 8, 5, 3, 2, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27031.0, 'intermediate_solutions': [{'tour': [0, 20, 12, 16, 17, 15, 11, 21, 22, 13, 18, 19, 41, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 14, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 50626.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 12, 16, 17, 15, 11, 21, 22, 13, 18, 19, 14, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 48, 53, 39, 40, 38, 41, 45, 42, 36, 43, 37, 44, 46, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 38494.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 12, 17, 15, 11, 21, 22, 13, 18, 19, 14, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 16, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 42687.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:10,260 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:10,260 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:10,263 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=27031.000, 多样性=0.966
2025-08-05 09:52:10,264 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 09:52:10,264 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 09:52:10,264 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:10,265 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.12548432008402668, 'best_improvement': -0.0852336598683154}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.01799387442572733}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.008829169290284896, 'recent_improvements': [0.014600767296691623, 0.015298059344662185, 0.032259105877261406], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 9, 'new_count': 9, 'count_change': 0, 'old_best_cost': 24451.0, 'new_best_cost': 24451.0, 'quality_improvement': 0.0, 'old_diversity': 0.8286252354048964, 'new_diversity': 0.8286252354048964, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:52:10,266 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 09:52:10,266 - __main__ - INFO - composite11_59 开始进化第 4 代
2025-08-05 09:52:10,266 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 09:52:10,266 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:10,267 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=27031.000, 多样性=0.966
2025-08-05 09:52:10,267 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:10,270 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.966
2025-08-05 09:52:10,270 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:10,274 - EliteExpert - INFO - 精英解分析完成: 精英解数量=9, 多样性=0.829
2025-08-05 09:52:10,276 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 09:52:10,276 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:10,276 - LandscapeExpert - INFO - 添加精英解数据: 9个精英解
2025-08-05 09:52:10,276 - LandscapeExpert - INFO - 数据提取成功: 19个路径, 19个适应度值
2025-08-05 09:52:10,332 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.105, 适应度梯度: -30067.211, 聚类评分: 0.000, 覆盖率: 0.135, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:10,332 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 09:52:10,332 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:10,332 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite11_59
2025-08-05 09:52:10,337 - visualization.landscape_visualizer - INFO - 插值约束: 27 个点被约束到最小值 24451.00
2025-08-05 09:52:10,455 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite11_59\landscape_composite11_59_iter_114_20250805_095210.html
2025-08-05 09:52:10,540 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite11_59\dashboard_composite11_59_iter_114_20250805_095210.html
2025-08-05 09:52:10,540 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 114
2025-08-05 09:52:10,541 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 09:52:10,541 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2647秒
2025-08-05 09:52:10,541 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.10526315789473684, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -30067.210526315797, 'local_optima_density': 0.10526315789473684, 'gradient_variance': 9221752311.33252, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1352, 'fitness_entropy': 0.6691271909471708, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -30067.211)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.135)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358730.3324056, 'performance_metrics': {}}}
2025-08-05 09:52:10,541 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:10,541 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:10,541 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:10,541 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:10,542 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:52:10,542 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 09:52:10,542 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:52:10,542 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:10,542 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:10,542 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:52:10,542 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:10,543 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:10,543 - experts.management.collaboration_manager - INFO - 识别精英个体: {9, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:10,543 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-05 09:52:10,543 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:10,543 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:10,543 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 245462.0
2025-08-05 09:52:10,556 - ExploitationExpert - INFO - res_population_num: 9
2025-08-05 09:52:10,556 - ExploitationExpert - INFO - res_population_costs: [24451.0, 24472, 24473, 24482, 24482, 24495, 24495, 24511, 24529.0]
2025-08-05 09:52:10,556 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39, 40, 42,
       45, 41, 38, 36, 37, 43, 44,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 53, 48, 58, 52, 50, 54,
       49, 57, 56, 47, 55, 51, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  9,  4,  1,  6, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26,
       28, 39, 40, 42, 45, 41, 38, 36, 43, 37, 44, 46, 35, 54, 50, 53, 48,
       58, 52, 56, 47, 55, 51, 57, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11,
       12, 21, 19,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39,
       40, 46, 35, 44, 37, 36, 42, 45, 41, 38, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 15, 17, 12, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41, 38,
       36, 43, 37, 44, 46, 35,  2,  3], dtype=int64), array([ 0, 10,  6,  1,  7,  4,  9,  3,  5,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 17, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 15, 17, 12, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39, 40, 42, 45,
       41, 38, 36, 37, 43, 44,  2,  3], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 46, 35, 44, 37,
       36, 42, 45, 41, 38, 43,  2,  3], dtype=int64)]
2025-08-05 09:52:10,560 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:10,560 - ExploitationExpert - INFO - populations: [{'tour': array([21,  5, 19, 39, 18, 45,  3, 49, 38, 56, 51, 55,  1,  8, 36, 27, 31,
       16, 54, 34,  2, 33, 53, 52, 57, 13, 44, 37, 41, 47, 32, 24, 14, 50,
        6, 40, 29, 22, 43,  0,  7, 46, 11, 23, 26, 28, 48, 42, 12, 15, 25,
        4, 30, 17, 58,  9, 35, 20, 10], dtype=int64), 'cur_cost': 245462.0}, {'tour': [17, 12, 38, 32, 45, 9, 58, 47, 55, 14, 20, 22, 43, 21, 6, 28, 33, 23, 24, 16, 1, 40, 7, 37, 10, 39, 36, 53, 56, 50, 15, 11, 0, 41, 57, 27, 34, 52, 5, 44, 46, 29, 35, 48, 4, 2, 49, 18, 13, 30, 19, 42, 54, 51, 31, 25, 8, 26, 3], 'cur_cost': 204385.0}, {'tour': [0, 2, 5, 16, 17, 15, 11, 12, 21, 22, 13, 18, 19, 14, 20, 6, 1, 7, 8, 4, 9, 3, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27143.0}, {'tour': [56, 6, 8, 33, 25, 35, 0, 23, 42, 41, 30, 32, 36, 29, 26, 38, 43, 39, 7, 24, 40, 37, 1, 31, 4, 28, 11, 3, 22, 16, 18, 5, 9, 12, 27, 45, 50, 52, 54, 58, 48, 55, 51, 53, 49, 19, 21, 15, 20, 13, 2, 46, 44, 34, 17, 14, 10, 57, 47], 'cur_cost': 139595.0}, {'tour': [2, 22, 46, 7, 3, 35, 25, 4, 11, 57, 5, 6, 12, 56, 48, 51, 58, 49, 19, 15, 20, 16, 52, 36, 38, 14, 41, 28, 32, 24, 9, 40, 29, 1, 33, 13, 23, 27, 43, 45, 30, 21, 39, 26, 17, 10, 55, 54, 37, 31, 42, 53, 50, 0, 34, 44, 18, 8, 47], 'cur_cost': 211556.0}, {'tour': [22, 19, 56, 8, 52, 36, 11, 3, 5, 1, 27, 13, 18, 43, 10, 21, 15, 0, 38, 46, 41, 33, 14, 24, 57, 54, 45, 35, 58, 25, 30, 26, 48, 44, 28, 17, 40, 29, 53, 23, 2, 50, 42, 34, 20, 32, 7, 37, 31, 55, 6, 9, 51, 12, 47, 39, 49, 4, 16], 'cur_cost': 240745.0}, {'tour': [42, 35, 5, 13, 26, 40, 33, 30, 6, 0, 1, 10, 11, 21, 22, 9, 29, 24, 41, 31, 34, 4, 23, 36, 2, 7, 25, 38, 32, 28, 46, 3, 12, 8, 14, 55, 52, 48, 58, 53, 49, 57, 54, 17, 16, 51, 50, 43, 39, 37, 56, 47, 20, 19, 18, 27, 44, 45, 15], 'cur_cost': 157248.0}, {'tour': [40, 1, 46, 55, 42, 32, 7, 31, 0, 21, 13, 24, 17, 52, 51, 58, 49, 8, 56, 36, 57, 26, 29, 47, 27, 44, 18, 54, 9, 39, 53, 3, 25, 15, 50, 4, 5, 19, 30, 38, 23, 20, 16, 34, 22, 6, 12, 41, 43, 10, 35, 2, 11, 14, 28, 33, 48, 45, 37], 'cur_cost': 233160.0}, {'tour': [0, 8, 13, 10, 9, 4, 3, 5, 2, 1, 7, 6, 33, 31, 27, 34, 23, 25, 32, 29, 30, 26, 28, 24, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 32438.0}, {'tour': [0, 4, 12, 19, 18, 13, 22, 14, 20, 16, 17, 15, 11, 21, 6, 1, 7, 8, 5, 3, 2, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27031.0}]
2025-08-05 09:52:10,561 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:10,561 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 294, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 294, 'cache_hits': 0, 'similarity_calculations': 1462, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:10,562 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([21,  5, 19, 39, 18, 45,  3, 49, 38, 56, 51, 55,  1,  8, 36, 27, 31,
       16, 54, 34,  2, 33, 53, 52, 57, 13, 44, 37, 41, 47, 32, 24, 14, 50,
        6, 40, 29, 22, 43,  0,  7, 46, 11, 23, 26, 28, 48, 42, 12, 15, 25,
        4, 30, 17, 58,  9, 35, 20, 10], dtype=int64), 'cur_cost': 245462.0, 'intermediate_solutions': [{'tour': array([31,  3, 20, 18, 58, 32, 37, 28, 51, 34, 53, 17, 47, 14, 40, 26,  7,
        4, 24,  0, 54, 49, 38, 21, 45, 10, 36, 39, 42, 52, 29, 57,  2, 41,
        8, 19, 46, 44, 13, 12, 55, 30, 56, 43,  6,  9, 50,  5, 27, 15, 23,
       16,  1, 25, 48, 11, 33, 22, 35]), 'cur_cost': 275952.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([18, 31,  3, 20, 58, 32, 37, 28, 51, 34, 53, 17, 47, 14, 40, 26,  7,
        4, 24,  0, 54, 49, 38, 21, 45, 10, 36, 39, 42, 52, 29, 57,  2, 41,
        8, 19, 46, 44, 13, 12, 55, 30, 56, 43,  6,  9, 50,  5, 27, 15, 23,
       16,  1, 25, 48, 11, 33, 22, 35]), 'cur_cost': 283762.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([58, 18, 31,  3, 20, 32, 37, 28, 51, 34, 53, 17, 47, 14, 40, 26,  7,
        4, 24,  0, 54, 49, 38, 21, 45, 10, 36, 39, 42, 52, 29, 57,  2, 41,
        8, 19, 46, 44, 13, 12, 55, 30, 56, 43,  6,  9, 50,  5, 27, 15, 23,
       16,  1, 25, 48, 11, 33, 22, 35]), 'cur_cost': 279539.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([20, 18, 31,  3, 58, 32, 37, 28, 51, 34, 53, 17, 47, 14, 40, 26,  7,
        4, 24,  0, 54, 49, 38, 21, 45, 10, 36, 39, 42, 52, 29, 57,  2, 41,
        8, 19, 46, 44, 13, 12, 55, 30, 56, 43,  6,  9, 50,  5, 27, 15, 23,
       16,  1, 25, 48, 11, 33, 22, 35]), 'cur_cost': 278287.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([20, 58, 18, 31,  3, 32, 37, 28, 51, 34, 53, 17, 47, 14, 40, 26,  7,
        4, 24,  0, 54, 49, 38, 21, 45, 10, 36, 39, 42, 52, 29, 57,  2, 41,
        8, 19, 46, 44, 13, 12, 55, 30, 56, 43,  6,  9, 50,  5, 27, 15, 23,
       16,  1, 25, 48, 11, 33, 22, 35]), 'cur_cost': 281643.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:10,563 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 245462.00)
2025-08-05 09:52:10,563 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:10,563 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:10,563 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:10,565 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 09:52:10,565 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,565 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,565 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,566 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,566 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32564.0, 路径长度: 59, 收集中间解: 3
2025-08-05 09:52:10,566 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 17, 3, 5, 4, 9, 10, 6, 1, 7, 8, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 15, 11, 12, 21, 19], 'cur_cost': 32564.0, 'intermediate_solutions': [{'tour': [17, 12, 38, 32, 45, 9, 58, 47, 55, 14, 20, 22, 43, 21, 6, 28, 33, 23, 24, 16, 1, 40, 7, 37, 10, 39, 36, 53, 56, 50, 15, 11, 0, 41, 57, 27, 34, 52, 5, 44, 46, 29, 35, 48, 19, 2, 49, 18, 13, 30, 4, 42, 54, 51, 31, 25, 8, 26, 3], 'cur_cost': 204439.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [17, 12, 38, 32, 45, 9, 58, 47, 55, 14, 20, 22, 43, 21, 6, 28, 33, 23, 24, 16, 1, 40, 7, 37, 10, 39, 36, 53, 56, 50, 15, 11, 0, 41, 57, 27, 34, 52, 5, 44, 46, 29, 35, 48, 4, 2, 42, 19, 30, 13, 18, 49, 54, 51, 31, 25, 8, 26, 3], 'cur_cost': 198814.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [17, 12, 38, 32, 45, 9, 58, 47, 55, 14, 20, 30, 22, 43, 21, 6, 28, 33, 23, 24, 16, 1, 40, 7, 37, 10, 39, 36, 53, 56, 50, 15, 11, 0, 41, 57, 27, 34, 52, 5, 44, 46, 29, 35, 48, 4, 2, 49, 18, 13, 19, 42, 54, 51, 31, 25, 8, 26, 3], 'cur_cost': 204491.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:10,566 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 32564.00)
2025-08-05 09:52:10,566 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:10,566 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:10,566 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:10,568 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 59
2025-08-05 09:52:10,568 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,569 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,569 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,569 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,569 - ExplorationExpert - INFO - 探索路径生成完成，成本: 222038.0, 路径长度: 59, 收集中间解: 3
2025-08-05 09:52:10,569 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [17, 16, 25, 10, 6, 1, 7, 28, 30, 32, 29, 40, 37, 33, 38, 11, 3, 18, 12, 4, 23, 48, 39, 26, 20, 5, 56, 31, 2, 9, 44, 57, 49, 52, 36, 53, 8, 21, 0, 58, 27, 22, 43, 45, 14, 15, 54, 41, 35, 19, 55, 50, 24, 42, 13, 34, 46, 47, 51], 'cur_cost': 222038.0, 'intermediate_solutions': [{'tour': [0, 2, 5, 16, 17, 15, 7, 12, 21, 22, 13, 18, 19, 14, 20, 6, 1, 11, 8, 4, 9, 3, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 42240.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 5, 16, 17, 15, 11, 12, 21, 22, 13, 18, 19, 14, 20, 6, 26, 28, 10, 3, 9, 4, 8, 7, 1, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 33945.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 5, 16, 17, 15, 11, 12, 21, 22, 13, 18, 19, 14, 20, 6, 1, 8, 4, 9, 3, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 7, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 35077.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:10,570 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 222038.00)
2025-08-05 09:52:10,570 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:52:10,570 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:52:10,570 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:10,572 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 59
2025-08-05 09:52:10,572 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,572 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,573 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,573 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,573 - ExplorationExpert - INFO - 探索路径生成完成，成本: 222840.0, 路径长度: 59, 收集中间解: 3
2025-08-05 09:52:10,573 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [17, 4, 0, 6, 1, 2, 37, 26, 30, 25, 3, 23, 48, 52, 31, 20, 28, 11, 35, 36, 53, 38, 43, 13, 49, 15, 21, 18, 55, 9, 44, 57, 27, 10, 8, 33, 29, 50, 32, 41, 34, 51, 45, 40, 14, 5, 16, 7, 39, 24, 19, 12, 22, 42, 54, 56, 47, 46, 58], 'cur_cost': 222840.0, 'intermediate_solutions': [{'tour': [56, 6, 8, 33, 25, 35, 0, 23, 42, 41, 30, 32, 36, 29, 26, 38, 43, 39, 7, 24, 40, 37, 1, 31, 4, 28, 11, 3, 22, 16, 18, 5, 9, 12, 27, 45, 50, 52, 54, 58, 48, 55, 51, 34, 49, 19, 21, 15, 20, 13, 2, 46, 44, 53, 17, 14, 10, 57, 47], 'cur_cost': 155221.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [56, 6, 8, 33, 25, 35, 0, 47, 57, 10, 14, 17, 34, 44, 46, 2, 13, 20, 15, 21, 19, 49, 53, 51, 55, 48, 58, 54, 52, 50, 45, 27, 12, 9, 5, 18, 16, 22, 3, 11, 28, 4, 31, 1, 37, 40, 24, 7, 39, 43, 38, 26, 29, 36, 32, 30, 41, 42, 23], 'cur_cost': 147434.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [56, 16, 6, 8, 33, 25, 35, 0, 23, 42, 41, 30, 32, 36, 29, 26, 38, 43, 39, 7, 24, 40, 37, 1, 31, 4, 28, 11, 3, 22, 18, 5, 9, 12, 27, 45, 50, 52, 54, 58, 48, 55, 51, 53, 49, 19, 21, 15, 20, 13, 2, 46, 44, 34, 17, 14, 10, 57, 47], 'cur_cost': 145022.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:10,573 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 222840.00)
2025-08-05 09:52:10,573 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:10,573 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:10,574 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:10,577 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 09:52:10,577 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,578 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,578 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,578 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,579 - ExplorationExpert - INFO - 探索路径生成完成，成本: 34739.0, 路径长度: 59, 收集中间解: 3
2025-08-05 09:52:10,579 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 20, 5, 14, 13, 18, 22, 21, 12, 11, 15, 17, 16, 19, 6, 1, 7, 8, 4, 9, 3, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34739.0, 'intermediate_solutions': [{'tour': [2, 22, 46, 7, 3, 35, 25, 4, 11, 57, 5, 6, 12, 56, 48, 51, 58, 49, 19, 15, 20, 1, 52, 36, 38, 14, 41, 28, 32, 24, 9, 40, 29, 16, 33, 13, 23, 27, 43, 45, 30, 21, 39, 26, 17, 10, 55, 54, 37, 31, 42, 53, 50, 0, 34, 44, 18, 8, 47], 'cur_cost': 216919.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 22, 46, 7, 3, 35, 25, 4, 11, 57, 5, 6, 12, 56, 48, 51, 58, 49, 19, 15, 41, 14, 38, 36, 52, 16, 20, 28, 32, 24, 9, 40, 29, 1, 33, 13, 23, 27, 43, 45, 30, 21, 39, 26, 17, 10, 55, 54, 37, 31, 42, 53, 50, 0, 34, 44, 18, 8, 47], 'cur_cost': 219491.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 22, 46, 7, 3, 35, 25, 4, 11, 57, 5, 6, 12, 56, 48, 51, 58, 36, 49, 19, 15, 20, 16, 52, 38, 14, 41, 28, 32, 24, 9, 40, 29, 1, 33, 13, 23, 27, 43, 45, 30, 21, 39, 26, 17, 10, 55, 54, 37, 31, 42, 53, 50, 0, 34, 44, 18, 8, 47], 'cur_cost': 222765.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:10,579 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 34739.00)
2025-08-05 09:52:10,579 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 09:52:10,580 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:10,580 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:10,580 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 247898.0
2025-08-05 09:52:10,587 - ExploitationExpert - INFO - res_population_num: 12
2025-08-05 09:52:10,587 - ExploitationExpert - INFO - res_population_costs: [24451.0, 24472, 24473, 24482, 24482, 24495, 24495, 24511, 24529.0, 24451, 24451, 24451]
2025-08-05 09:52:10,588 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39, 40, 42,
       45, 41, 38, 36, 37, 43, 44,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 53, 48, 58, 52, 50, 54,
       49, 57, 56, 47, 55, 51, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  9,  4,  1,  6, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26,
       28, 39, 40, 42, 45, 41, 38, 36, 43, 37, 44, 46, 35, 54, 50, 53, 48,
       58, 52, 56, 47, 55, 51, 57, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11,
       12, 21, 19,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39,
       40, 46, 35, 44, 37, 36, 42, 45, 41, 38, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 15, 17, 12, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41, 38,
       36, 43, 37, 44, 46, 35,  2,  3], dtype=int64), array([ 0, 10,  6,  1,  7,  4,  9,  3,  5,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 17, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 15, 17, 12, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39, 40, 42, 45,
       41, 38, 36, 37, 43, 44,  2,  3], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 46, 35, 44, 37,
       36, 42, 45, 41, 38, 43,  2,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64)]
2025-08-05 09:52:10,592 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:10,592 - ExploitationExpert - INFO - populations: [{'tour': array([21,  5, 19, 39, 18, 45,  3, 49, 38, 56, 51, 55,  1,  8, 36, 27, 31,
       16, 54, 34,  2, 33, 53, 52, 57, 13, 44, 37, 41, 47, 32, 24, 14, 50,
        6, 40, 29, 22, 43,  0,  7, 46, 11, 23, 26, 28, 48, 42, 12, 15, 25,
        4, 30, 17, 58,  9, 35, 20, 10], dtype=int64), 'cur_cost': 245462.0}, {'tour': [0, 17, 3, 5, 4, 9, 10, 6, 1, 7, 8, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 15, 11, 12, 21, 19], 'cur_cost': 32564.0}, {'tour': [17, 16, 25, 10, 6, 1, 7, 28, 30, 32, 29, 40, 37, 33, 38, 11, 3, 18, 12, 4, 23, 48, 39, 26, 20, 5, 56, 31, 2, 9, 44, 57, 49, 52, 36, 53, 8, 21, 0, 58, 27, 22, 43, 45, 14, 15, 54, 41, 35, 19, 55, 50, 24, 42, 13, 34, 46, 47, 51], 'cur_cost': 222038.0}, {'tour': [17, 4, 0, 6, 1, 2, 37, 26, 30, 25, 3, 23, 48, 52, 31, 20, 28, 11, 35, 36, 53, 38, 43, 13, 49, 15, 21, 18, 55, 9, 44, 57, 27, 10, 8, 33, 29, 50, 32, 41, 34, 51, 45, 40, 14, 5, 16, 7, 39, 24, 19, 12, 22, 42, 54, 56, 47, 46, 58], 'cur_cost': 222840.0}, {'tour': [0, 20, 5, 14, 13, 18, 22, 21, 12, 11, 15, 17, 16, 19, 6, 1, 7, 8, 4, 9, 3, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34739.0}, {'tour': array([57, 18, 22, 41, 37, 11,  3, 23,  5, 46, 54, 16, 49,  4,  1, 14, 28,
       48, 55, 32, 24, 10, 20, 39, 17, 27, 33, 45, 44,  6, 12, 47, 34, 43,
       35,  8, 53, 36, 30, 21, 38, 52, 50, 25, 29, 13, 26, 51, 58,  2, 56,
       40,  9,  7, 42, 15, 31, 19,  0], dtype=int64), 'cur_cost': 247898.0}, {'tour': [42, 35, 5, 13, 26, 40, 33, 30, 6, 0, 1, 10, 11, 21, 22, 9, 29, 24, 41, 31, 34, 4, 23, 36, 2, 7, 25, 38, 32, 28, 46, 3, 12, 8, 14, 55, 52, 48, 58, 53, 49, 57, 54, 17, 16, 51, 50, 43, 39, 37, 56, 47, 20, 19, 18, 27, 44, 45, 15], 'cur_cost': 157248.0}, {'tour': [40, 1, 46, 55, 42, 32, 7, 31, 0, 21, 13, 24, 17, 52, 51, 58, 49, 8, 56, 36, 57, 26, 29, 47, 27, 44, 18, 54, 9, 39, 53, 3, 25, 15, 50, 4, 5, 19, 30, 38, 23, 20, 16, 34, 22, 6, 12, 41, 43, 10, 35, 2, 11, 14, 28, 33, 48, 45, 37], 'cur_cost': 233160.0}, {'tour': [0, 8, 13, 10, 9, 4, 3, 5, 2, 1, 7, 6, 33, 31, 27, 34, 23, 25, 32, 29, 30, 26, 28, 24, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 32438.0}, {'tour': [0, 4, 12, 19, 18, 13, 22, 14, 20, 16, 17, 15, 11, 21, 6, 1, 7, 8, 5, 3, 2, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27031.0}]
2025-08-05 09:52:10,594 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:10,594 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 295, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 295, 'cache_hits': 0, 'similarity_calculations': 1470, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:10,595 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([57, 18, 22, 41, 37, 11,  3, 23,  5, 46, 54, 16, 49,  4,  1, 14, 28,
       48, 55, 32, 24, 10, 20, 39, 17, 27, 33, 45, 44,  6, 12, 47, 34, 43,
       35,  8, 53, 36, 30, 21, 38, 52, 50, 25, 29, 13, 26, 51, 58,  2, 56,
       40,  9,  7, 42, 15, 31, 19,  0], dtype=int64), 'cur_cost': 247898.0, 'intermediate_solutions': [{'tour': array([56, 19, 22,  8, 52, 36, 11,  3,  5,  1, 27, 13, 18, 43, 10, 21, 15,
        0, 38, 46, 41, 33, 14, 24, 57, 54, 45, 35, 58, 25, 30, 26, 48, 44,
       28, 17, 40, 29, 53, 23,  2, 50, 42, 34, 20, 32,  7, 37, 31, 55,  6,
        9, 51, 12, 47, 39, 49,  4, 16]), 'cur_cost': 246266.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 8, 56, 19, 22, 52, 36, 11,  3,  5,  1, 27, 13, 18, 43, 10, 21, 15,
        0, 38, 46, 41, 33, 14, 24, 57, 54, 45, 35, 58, 25, 30, 26, 48, 44,
       28, 17, 40, 29, 53, 23,  2, 50, 42, 34, 20, 32,  7, 37, 31, 55,  6,
        9, 51, 12, 47, 39, 49,  4, 16]), 'cur_cost': 246268.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([52,  8, 56, 19, 22, 36, 11,  3,  5,  1, 27, 13, 18, 43, 10, 21, 15,
        0, 38, 46, 41, 33, 14, 24, 57, 54, 45, 35, 58, 25, 30, 26, 48, 44,
       28, 17, 40, 29, 53, 23,  2, 50, 42, 34, 20, 32,  7, 37, 31, 55,  6,
        9, 51, 12, 47, 39, 49,  4, 16]), 'cur_cost': 248421.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([22,  8, 56, 19, 52, 36, 11,  3,  5,  1, 27, 13, 18, 43, 10, 21, 15,
        0, 38, 46, 41, 33, 14, 24, 57, 54, 45, 35, 58, 25, 30, 26, 48, 44,
       28, 17, 40, 29, 53, 23,  2, 50, 42, 34, 20, 32,  7, 37, 31, 55,  6,
        9, 51, 12, 47, 39, 49,  4, 16]), 'cur_cost': 246211.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([22, 52,  8, 56, 19, 36, 11,  3,  5,  1, 27, 13, 18, 43, 10, 21, 15,
        0, 38, 46, 41, 33, 14, 24, 57, 54, 45, 35, 58, 25, 30, 26, 48, 44,
       28, 17, 40, 29, 53, 23,  2, 50, 42, 34, 20, 32,  7, 37, 31, 55,  6,
        9, 51, 12, 47, 39, 49,  4, 16]), 'cur_cost': 248308.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:10,595 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 247898.00)
2025-08-05 09:52:10,595 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:10,595 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:10,596 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:10,598 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 09:52:10,598 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,598 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,598 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,599 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,599 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27148.0, 路径长度: 59, 收集中间解: 3
2025-08-05 09:52:10,599 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 7, 10, 20, 14, 13, 18, 22, 21, 12, 11, 15, 17, 16, 19, 6, 1, 4, 9, 3, 5, 8, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27148.0, 'intermediate_solutions': [{'tour': [42, 35, 5, 13, 26, 40, 33, 30, 6, 0, 1, 10, 11, 57, 22, 9, 29, 24, 41, 31, 34, 4, 23, 36, 2, 7, 25, 38, 32, 28, 46, 3, 12, 8, 14, 55, 52, 48, 58, 53, 49, 21, 54, 17, 16, 51, 50, 43, 39, 37, 56, 47, 20, 19, 18, 27, 44, 45, 15], 'cur_cost': 179366.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [42, 35, 5, 13, 26, 40, 33, 30, 6, 0, 1, 10, 11, 21, 22, 9, 29, 24, 41, 31, 34, 58, 48, 52, 55, 14, 8, 12, 3, 46, 28, 32, 38, 25, 7, 2, 36, 23, 4, 53, 49, 57, 54, 17, 16, 51, 50, 43, 39, 37, 56, 47, 20, 19, 18, 27, 44, 45, 15], 'cur_cost': 164983.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [42, 35, 5, 34, 13, 26, 40, 33, 30, 6, 0, 1, 10, 11, 21, 22, 9, 29, 24, 41, 31, 4, 23, 36, 2, 7, 25, 38, 32, 28, 46, 3, 12, 8, 14, 55, 52, 48, 58, 53, 49, 57, 54, 17, 16, 51, 50, 43, 39, 37, 56, 47, 20, 19, 18, 27, 44, 45, 15], 'cur_cost': 161941.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:10,599 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 27148.00)
2025-08-05 09:52:10,599 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 09:52:10,599 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:10,599 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:10,600 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 252224.0
2025-08-05 09:52:10,611 - ExploitationExpert - INFO - res_population_num: 12
2025-08-05 09:52:10,611 - ExploitationExpert - INFO - res_population_costs: [24451.0, 24472, 24473, 24482, 24482, 24495, 24495, 24511, 24529.0, 24451, 24451, 24451]
2025-08-05 09:52:10,611 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39, 40, 42,
       45, 41, 38, 36, 37, 43, 44,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 53, 48, 58, 52, 50, 54,
       49, 57, 56, 47, 55, 51, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  9,  4,  1,  6, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26,
       28, 39, 40, 42, 45, 41, 38, 36, 43, 37, 44, 46, 35, 54, 50, 53, 48,
       58, 52, 56, 47, 55, 51, 57, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11,
       12, 21, 19,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39,
       40, 46, 35, 44, 37, 36, 42, 45, 41, 38, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 15, 17, 12, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41, 38,
       36, 43, 37, 44, 46, 35,  2,  3], dtype=int64), array([ 0, 10,  6,  1,  7,  4,  9,  3,  5,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 17, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 15, 17, 12, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39, 40, 42, 45,
       41, 38, 36, 37, 43, 44,  2,  3], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 46, 35, 44, 37,
       36, 42, 45, 41, 38, 43,  2,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64)]
2025-08-05 09:52:10,617 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:10,617 - ExploitationExpert - INFO - populations: [{'tour': array([21,  5, 19, 39, 18, 45,  3, 49, 38, 56, 51, 55,  1,  8, 36, 27, 31,
       16, 54, 34,  2, 33, 53, 52, 57, 13, 44, 37, 41, 47, 32, 24, 14, 50,
        6, 40, 29, 22, 43,  0,  7, 46, 11, 23, 26, 28, 48, 42, 12, 15, 25,
        4, 30, 17, 58,  9, 35, 20, 10], dtype=int64), 'cur_cost': 245462.0}, {'tour': [0, 17, 3, 5, 4, 9, 10, 6, 1, 7, 8, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 15, 11, 12, 21, 19], 'cur_cost': 32564.0}, {'tour': [17, 16, 25, 10, 6, 1, 7, 28, 30, 32, 29, 40, 37, 33, 38, 11, 3, 18, 12, 4, 23, 48, 39, 26, 20, 5, 56, 31, 2, 9, 44, 57, 49, 52, 36, 53, 8, 21, 0, 58, 27, 22, 43, 45, 14, 15, 54, 41, 35, 19, 55, 50, 24, 42, 13, 34, 46, 47, 51], 'cur_cost': 222038.0}, {'tour': [17, 4, 0, 6, 1, 2, 37, 26, 30, 25, 3, 23, 48, 52, 31, 20, 28, 11, 35, 36, 53, 38, 43, 13, 49, 15, 21, 18, 55, 9, 44, 57, 27, 10, 8, 33, 29, 50, 32, 41, 34, 51, 45, 40, 14, 5, 16, 7, 39, 24, 19, 12, 22, 42, 54, 56, 47, 46, 58], 'cur_cost': 222840.0}, {'tour': [0, 20, 5, 14, 13, 18, 22, 21, 12, 11, 15, 17, 16, 19, 6, 1, 7, 8, 4, 9, 3, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34739.0}, {'tour': array([57, 18, 22, 41, 37, 11,  3, 23,  5, 46, 54, 16, 49,  4,  1, 14, 28,
       48, 55, 32, 24, 10, 20, 39, 17, 27, 33, 45, 44,  6, 12, 47, 34, 43,
       35,  8, 53, 36, 30, 21, 38, 52, 50, 25, 29, 13, 26, 51, 58,  2, 56,
       40,  9,  7, 42, 15, 31, 19,  0], dtype=int64), 'cur_cost': 247898.0}, {'tour': [0, 7, 10, 20, 14, 13, 18, 22, 21, 12, 11, 15, 17, 16, 19, 6, 1, 4, 9, 3, 5, 8, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27148.0}, {'tour': array([44, 17, 55,  2, 33, 16, 28,  3, 25, 26, 39,  0, 35, 53,  1, 13, 58,
       48, 42, 30, 19, 31, 50, 20, 29, 34, 46,  8, 37, 18,  7, 24, 11, 23,
       38, 56,  4, 10, 41, 32, 36, 40, 22, 12,  9, 15, 52, 47,  6, 49, 43,
       57, 54, 14, 27,  5, 45, 21, 51], dtype=int64), 'cur_cost': 252224.0}, {'tour': [0, 8, 13, 10, 9, 4, 3, 5, 2, 1, 7, 6, 33, 31, 27, 34, 23, 25, 32, 29, 30, 26, 28, 24, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 32438.0}, {'tour': [0, 4, 12, 19, 18, 13, 22, 14, 20, 16, 17, 15, 11, 21, 6, 1, 7, 8, 5, 3, 2, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27031.0}]
2025-08-05 09:52:10,618 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:10,618 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 296, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 296, 'cache_hits': 0, 'similarity_calculations': 1479, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:10,619 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([44, 17, 55,  2, 33, 16, 28,  3, 25, 26, 39,  0, 35, 53,  1, 13, 58,
       48, 42, 30, 19, 31, 50, 20, 29, 34, 46,  8, 37, 18,  7, 24, 11, 23,
       38, 56,  4, 10, 41, 32, 36, 40, 22, 12,  9, 15, 52, 47,  6, 49, 43,
       57, 54, 14, 27,  5, 45, 21, 51], dtype=int64), 'cur_cost': 252224.0, 'intermediate_solutions': [{'tour': array([46,  1, 40, 55, 42, 32,  7, 31,  0, 21, 13, 24, 17, 52, 51, 58, 49,
        8, 56, 36, 57, 26, 29, 47, 27, 44, 18, 54,  9, 39, 53,  3, 25, 15,
       50,  4,  5, 19, 30, 38, 23, 20, 16, 34, 22,  6, 12, 41, 43, 10, 35,
        2, 11, 14, 28, 33, 48, 45, 37]), 'cur_cost': 233170.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([55, 46,  1, 40, 42, 32,  7, 31,  0, 21, 13, 24, 17, 52, 51, 58, 49,
        8, 56, 36, 57, 26, 29, 47, 27, 44, 18, 54,  9, 39, 53,  3, 25, 15,
       50,  4,  5, 19, 30, 38, 23, 20, 16, 34, 22,  6, 12, 41, 43, 10, 35,
        2, 11, 14, 28, 33, 48, 45, 37]), 'cur_cost': 233089.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([42, 55, 46,  1, 40, 32,  7, 31,  0, 21, 13, 24, 17, 52, 51, 58, 49,
        8, 56, 36, 57, 26, 29, 47, 27, 44, 18, 54,  9, 39, 53,  3, 25, 15,
       50,  4,  5, 19, 30, 38, 23, 20, 16, 34, 22,  6, 12, 41, 43, 10, 35,
        2, 11, 14, 28, 33, 48, 45, 37]), 'cur_cost': 233103.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([40, 55, 46,  1, 42, 32,  7, 31,  0, 21, 13, 24, 17, 52, 51, 58, 49,
        8, 56, 36, 57, 26, 29, 47, 27, 44, 18, 54,  9, 39, 53,  3, 25, 15,
       50,  4,  5, 19, 30, 38, 23, 20, 16, 34, 22,  6, 12, 41, 43, 10, 35,
        2, 11, 14, 28, 33, 48, 45, 37]), 'cur_cost': 233184.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([40, 42, 55, 46,  1, 32,  7, 31,  0, 21, 13, 24, 17, 52, 51, 58, 49,
        8, 56, 36, 57, 26, 29, 47, 27, 44, 18, 54,  9, 39, 53,  3, 25, 15,
       50,  4,  5, 19, 30, 38, 23, 20, 16, 34, 22,  6, 12, 41, 43, 10, 35,
        2, 11, 14, 28, 33, 48, 45, 37]), 'cur_cost': 227739.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:10,619 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 252224.00)
2025-08-05 09:52:10,619 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:52:10,619 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:52:10,620 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:10,621 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 09:52:10,622 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,622 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,622 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,622 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,622 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27140.0, 路径长度: 59, 收集中间解: 3
2025-08-05 09:52:10,623 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 12, 15, 21, 17, 16, 11, 19, 18, 13, 22, 14, 20, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27140.0, 'intermediate_solutions': [{'tour': [0, 8, 13, 10, 9, 4, 3, 5, 2, 1, 7, 6, 33, 31, 27, 34, 23, 25, 32, 29, 30, 26, 28, 24, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 19, 22, 14, 20, 16, 17, 15, 11, 12, 21, 18], 'cur_cost': 32493.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 13, 10, 9, 4, 3, 5, 2, 1, 7, 6, 33, 31, 27, 34, 23, 25, 32, 29, 30, 26, 28, 24, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 18, 22, 14, 20, 16, 17, 11, 15, 12, 21, 19], 'cur_cost': 32468.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 13, 10, 9, 4, 3, 5, 2, 1, 7, 6, 33, 31, 27, 16, 34, 23, 25, 32, 29, 30, 26, 28, 24, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 18, 22, 14, 20, 17, 15, 11, 12, 21, 19], 'cur_cost': 42586.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:10,623 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 27140.00)
2025-08-05 09:52:10,623 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:10,623 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:10,623 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:10,625 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 09:52:10,625 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,625 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,626 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,626 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,626 - ExplorationExpert - INFO - 探索路径生成完成，成本: 31772.0, 路径长度: 59, 收集中间解: 3
2025-08-05 09:52:10,626 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 23, 12, 16, 17, 15, 11, 21, 22, 13, 18, 19, 14, 20, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 31772.0, 'intermediate_solutions': [{'tour': [0, 4, 12, 19, 18, 13, 22, 14, 20, 16, 17, 15, 11, 21, 6, 29, 7, 8, 5, 3, 2, 9, 10, 28, 26, 30, 25, 32, 1, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 40829.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 12, 19, 18, 13, 22, 14, 20, 16, 17, 15, 11, 21, 6, 1, 7, 8, 5, 23, 34, 27, 31, 24, 29, 32, 25, 30, 26, 28, 10, 9, 2, 3, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 33837.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 12, 19, 18, 13, 22, 20, 16, 17, 15, 11, 21, 6, 14, 1, 7, 8, 5, 3, 2, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34588.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:10,626 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 31772.00)
2025-08-05 09:52:10,626 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:10,626 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:10,629 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([21,  5, 19, 39, 18, 45,  3, 49, 38, 56, 51, 55,  1,  8, 36, 27, 31,
       16, 54, 34,  2, 33, 53, 52, 57, 13, 44, 37, 41, 47, 32, 24, 14, 50,
        6, 40, 29, 22, 43,  0,  7, 46, 11, 23, 26, 28, 48, 42, 12, 15, 25,
        4, 30, 17, 58,  9, 35, 20, 10], dtype=int64), 'cur_cost': 245462.0, 'intermediate_solutions': [{'tour': array([31,  3, 20, 18, 58, 32, 37, 28, 51, 34, 53, 17, 47, 14, 40, 26,  7,
        4, 24,  0, 54, 49, 38, 21, 45, 10, 36, 39, 42, 52, 29, 57,  2, 41,
        8, 19, 46, 44, 13, 12, 55, 30, 56, 43,  6,  9, 50,  5, 27, 15, 23,
       16,  1, 25, 48, 11, 33, 22, 35]), 'cur_cost': 275952.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([18, 31,  3, 20, 58, 32, 37, 28, 51, 34, 53, 17, 47, 14, 40, 26,  7,
        4, 24,  0, 54, 49, 38, 21, 45, 10, 36, 39, 42, 52, 29, 57,  2, 41,
        8, 19, 46, 44, 13, 12, 55, 30, 56, 43,  6,  9, 50,  5, 27, 15, 23,
       16,  1, 25, 48, 11, 33, 22, 35]), 'cur_cost': 283762.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([58, 18, 31,  3, 20, 32, 37, 28, 51, 34, 53, 17, 47, 14, 40, 26,  7,
        4, 24,  0, 54, 49, 38, 21, 45, 10, 36, 39, 42, 52, 29, 57,  2, 41,
        8, 19, 46, 44, 13, 12, 55, 30, 56, 43,  6,  9, 50,  5, 27, 15, 23,
       16,  1, 25, 48, 11, 33, 22, 35]), 'cur_cost': 279539.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([20, 18, 31,  3, 58, 32, 37, 28, 51, 34, 53, 17, 47, 14, 40, 26,  7,
        4, 24,  0, 54, 49, 38, 21, 45, 10, 36, 39, 42, 52, 29, 57,  2, 41,
        8, 19, 46, 44, 13, 12, 55, 30, 56, 43,  6,  9, 50,  5, 27, 15, 23,
       16,  1, 25, 48, 11, 33, 22, 35]), 'cur_cost': 278287.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([20, 58, 18, 31,  3, 32, 37, 28, 51, 34, 53, 17, 47, 14, 40, 26,  7,
        4, 24,  0, 54, 49, 38, 21, 45, 10, 36, 39, 42, 52, 29, 57,  2, 41,
        8, 19, 46, 44, 13, 12, 55, 30, 56, 43,  6,  9, 50,  5, 27, 15, 23,
       16,  1, 25, 48, 11, 33, 22, 35]), 'cur_cost': 281643.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 3, 5, 4, 9, 10, 6, 1, 7, 8, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 15, 11, 12, 21, 19], 'cur_cost': 32564.0, 'intermediate_solutions': [{'tour': [17, 12, 38, 32, 45, 9, 58, 47, 55, 14, 20, 22, 43, 21, 6, 28, 33, 23, 24, 16, 1, 40, 7, 37, 10, 39, 36, 53, 56, 50, 15, 11, 0, 41, 57, 27, 34, 52, 5, 44, 46, 29, 35, 48, 19, 2, 49, 18, 13, 30, 4, 42, 54, 51, 31, 25, 8, 26, 3], 'cur_cost': 204439.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [17, 12, 38, 32, 45, 9, 58, 47, 55, 14, 20, 22, 43, 21, 6, 28, 33, 23, 24, 16, 1, 40, 7, 37, 10, 39, 36, 53, 56, 50, 15, 11, 0, 41, 57, 27, 34, 52, 5, 44, 46, 29, 35, 48, 4, 2, 42, 19, 30, 13, 18, 49, 54, 51, 31, 25, 8, 26, 3], 'cur_cost': 198814.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [17, 12, 38, 32, 45, 9, 58, 47, 55, 14, 20, 30, 22, 43, 21, 6, 28, 33, 23, 24, 16, 1, 40, 7, 37, 10, 39, 36, 53, 56, 50, 15, 11, 0, 41, 57, 27, 34, 52, 5, 44, 46, 29, 35, 48, 4, 2, 49, 18, 13, 19, 42, 54, 51, 31, 25, 8, 26, 3], 'cur_cost': 204491.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [17, 16, 25, 10, 6, 1, 7, 28, 30, 32, 29, 40, 37, 33, 38, 11, 3, 18, 12, 4, 23, 48, 39, 26, 20, 5, 56, 31, 2, 9, 44, 57, 49, 52, 36, 53, 8, 21, 0, 58, 27, 22, 43, 45, 14, 15, 54, 41, 35, 19, 55, 50, 24, 42, 13, 34, 46, 47, 51], 'cur_cost': 222038.0, 'intermediate_solutions': [{'tour': [0, 2, 5, 16, 17, 15, 7, 12, 21, 22, 13, 18, 19, 14, 20, 6, 1, 11, 8, 4, 9, 3, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 42240.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 5, 16, 17, 15, 11, 12, 21, 22, 13, 18, 19, 14, 20, 6, 26, 28, 10, 3, 9, 4, 8, 7, 1, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 33945.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 5, 16, 17, 15, 11, 12, 21, 22, 13, 18, 19, 14, 20, 6, 1, 8, 4, 9, 3, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 7, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 35077.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [17, 4, 0, 6, 1, 2, 37, 26, 30, 25, 3, 23, 48, 52, 31, 20, 28, 11, 35, 36, 53, 38, 43, 13, 49, 15, 21, 18, 55, 9, 44, 57, 27, 10, 8, 33, 29, 50, 32, 41, 34, 51, 45, 40, 14, 5, 16, 7, 39, 24, 19, 12, 22, 42, 54, 56, 47, 46, 58], 'cur_cost': 222840.0, 'intermediate_solutions': [{'tour': [56, 6, 8, 33, 25, 35, 0, 23, 42, 41, 30, 32, 36, 29, 26, 38, 43, 39, 7, 24, 40, 37, 1, 31, 4, 28, 11, 3, 22, 16, 18, 5, 9, 12, 27, 45, 50, 52, 54, 58, 48, 55, 51, 34, 49, 19, 21, 15, 20, 13, 2, 46, 44, 53, 17, 14, 10, 57, 47], 'cur_cost': 155221.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [56, 6, 8, 33, 25, 35, 0, 47, 57, 10, 14, 17, 34, 44, 46, 2, 13, 20, 15, 21, 19, 49, 53, 51, 55, 48, 58, 54, 52, 50, 45, 27, 12, 9, 5, 18, 16, 22, 3, 11, 28, 4, 31, 1, 37, 40, 24, 7, 39, 43, 38, 26, 29, 36, 32, 30, 41, 42, 23], 'cur_cost': 147434.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [56, 16, 6, 8, 33, 25, 35, 0, 23, 42, 41, 30, 32, 36, 29, 26, 38, 43, 39, 7, 24, 40, 37, 1, 31, 4, 28, 11, 3, 22, 18, 5, 9, 12, 27, 45, 50, 52, 54, 58, 48, 55, 51, 53, 49, 19, 21, 15, 20, 13, 2, 46, 44, 34, 17, 14, 10, 57, 47], 'cur_cost': 145022.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 5, 14, 13, 18, 22, 21, 12, 11, 15, 17, 16, 19, 6, 1, 7, 8, 4, 9, 3, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34739.0, 'intermediate_solutions': [{'tour': [2, 22, 46, 7, 3, 35, 25, 4, 11, 57, 5, 6, 12, 56, 48, 51, 58, 49, 19, 15, 20, 1, 52, 36, 38, 14, 41, 28, 32, 24, 9, 40, 29, 16, 33, 13, 23, 27, 43, 45, 30, 21, 39, 26, 17, 10, 55, 54, 37, 31, 42, 53, 50, 0, 34, 44, 18, 8, 47], 'cur_cost': 216919.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 22, 46, 7, 3, 35, 25, 4, 11, 57, 5, 6, 12, 56, 48, 51, 58, 49, 19, 15, 41, 14, 38, 36, 52, 16, 20, 28, 32, 24, 9, 40, 29, 1, 33, 13, 23, 27, 43, 45, 30, 21, 39, 26, 17, 10, 55, 54, 37, 31, 42, 53, 50, 0, 34, 44, 18, 8, 47], 'cur_cost': 219491.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 22, 46, 7, 3, 35, 25, 4, 11, 57, 5, 6, 12, 56, 48, 51, 58, 36, 49, 19, 15, 20, 16, 52, 38, 14, 41, 28, 32, 24, 9, 40, 29, 1, 33, 13, 23, 27, 43, 45, 30, 21, 39, 26, 17, 10, 55, 54, 37, 31, 42, 53, 50, 0, 34, 44, 18, 8, 47], 'cur_cost': 222765.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([57, 18, 22, 41, 37, 11,  3, 23,  5, 46, 54, 16, 49,  4,  1, 14, 28,
       48, 55, 32, 24, 10, 20, 39, 17, 27, 33, 45, 44,  6, 12, 47, 34, 43,
       35,  8, 53, 36, 30, 21, 38, 52, 50, 25, 29, 13, 26, 51, 58,  2, 56,
       40,  9,  7, 42, 15, 31, 19,  0], dtype=int64), 'cur_cost': 247898.0, 'intermediate_solutions': [{'tour': array([56, 19, 22,  8, 52, 36, 11,  3,  5,  1, 27, 13, 18, 43, 10, 21, 15,
        0, 38, 46, 41, 33, 14, 24, 57, 54, 45, 35, 58, 25, 30, 26, 48, 44,
       28, 17, 40, 29, 53, 23,  2, 50, 42, 34, 20, 32,  7, 37, 31, 55,  6,
        9, 51, 12, 47, 39, 49,  4, 16]), 'cur_cost': 246266.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 8, 56, 19, 22, 52, 36, 11,  3,  5,  1, 27, 13, 18, 43, 10, 21, 15,
        0, 38, 46, 41, 33, 14, 24, 57, 54, 45, 35, 58, 25, 30, 26, 48, 44,
       28, 17, 40, 29, 53, 23,  2, 50, 42, 34, 20, 32,  7, 37, 31, 55,  6,
        9, 51, 12, 47, 39, 49,  4, 16]), 'cur_cost': 246268.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([52,  8, 56, 19, 22, 36, 11,  3,  5,  1, 27, 13, 18, 43, 10, 21, 15,
        0, 38, 46, 41, 33, 14, 24, 57, 54, 45, 35, 58, 25, 30, 26, 48, 44,
       28, 17, 40, 29, 53, 23,  2, 50, 42, 34, 20, 32,  7, 37, 31, 55,  6,
        9, 51, 12, 47, 39, 49,  4, 16]), 'cur_cost': 248421.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([22,  8, 56, 19, 52, 36, 11,  3,  5,  1, 27, 13, 18, 43, 10, 21, 15,
        0, 38, 46, 41, 33, 14, 24, 57, 54, 45, 35, 58, 25, 30, 26, 48, 44,
       28, 17, 40, 29, 53, 23,  2, 50, 42, 34, 20, 32,  7, 37, 31, 55,  6,
        9, 51, 12, 47, 39, 49,  4, 16]), 'cur_cost': 246211.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([22, 52,  8, 56, 19, 36, 11,  3,  5,  1, 27, 13, 18, 43, 10, 21, 15,
        0, 38, 46, 41, 33, 14, 24, 57, 54, 45, 35, 58, 25, 30, 26, 48, 44,
       28, 17, 40, 29, 53, 23,  2, 50, 42, 34, 20, 32,  7, 37, 31, 55,  6,
        9, 51, 12, 47, 39, 49,  4, 16]), 'cur_cost': 248308.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 10, 20, 14, 13, 18, 22, 21, 12, 11, 15, 17, 16, 19, 6, 1, 4, 9, 3, 5, 8, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27148.0, 'intermediate_solutions': [{'tour': [42, 35, 5, 13, 26, 40, 33, 30, 6, 0, 1, 10, 11, 57, 22, 9, 29, 24, 41, 31, 34, 4, 23, 36, 2, 7, 25, 38, 32, 28, 46, 3, 12, 8, 14, 55, 52, 48, 58, 53, 49, 21, 54, 17, 16, 51, 50, 43, 39, 37, 56, 47, 20, 19, 18, 27, 44, 45, 15], 'cur_cost': 179366.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [42, 35, 5, 13, 26, 40, 33, 30, 6, 0, 1, 10, 11, 21, 22, 9, 29, 24, 41, 31, 34, 58, 48, 52, 55, 14, 8, 12, 3, 46, 28, 32, 38, 25, 7, 2, 36, 23, 4, 53, 49, 57, 54, 17, 16, 51, 50, 43, 39, 37, 56, 47, 20, 19, 18, 27, 44, 45, 15], 'cur_cost': 164983.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [42, 35, 5, 34, 13, 26, 40, 33, 30, 6, 0, 1, 10, 11, 21, 22, 9, 29, 24, 41, 31, 4, 23, 36, 2, 7, 25, 38, 32, 28, 46, 3, 12, 8, 14, 55, 52, 48, 58, 53, 49, 57, 54, 17, 16, 51, 50, 43, 39, 37, 56, 47, 20, 19, 18, 27, 44, 45, 15], 'cur_cost': 161941.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([44, 17, 55,  2, 33, 16, 28,  3, 25, 26, 39,  0, 35, 53,  1, 13, 58,
       48, 42, 30, 19, 31, 50, 20, 29, 34, 46,  8, 37, 18,  7, 24, 11, 23,
       38, 56,  4, 10, 41, 32, 36, 40, 22, 12,  9, 15, 52, 47,  6, 49, 43,
       57, 54, 14, 27,  5, 45, 21, 51], dtype=int64), 'cur_cost': 252224.0, 'intermediate_solutions': [{'tour': array([46,  1, 40, 55, 42, 32,  7, 31,  0, 21, 13, 24, 17, 52, 51, 58, 49,
        8, 56, 36, 57, 26, 29, 47, 27, 44, 18, 54,  9, 39, 53,  3, 25, 15,
       50,  4,  5, 19, 30, 38, 23, 20, 16, 34, 22,  6, 12, 41, 43, 10, 35,
        2, 11, 14, 28, 33, 48, 45, 37]), 'cur_cost': 233170.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([55, 46,  1, 40, 42, 32,  7, 31,  0, 21, 13, 24, 17, 52, 51, 58, 49,
        8, 56, 36, 57, 26, 29, 47, 27, 44, 18, 54,  9, 39, 53,  3, 25, 15,
       50,  4,  5, 19, 30, 38, 23, 20, 16, 34, 22,  6, 12, 41, 43, 10, 35,
        2, 11, 14, 28, 33, 48, 45, 37]), 'cur_cost': 233089.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([42, 55, 46,  1, 40, 32,  7, 31,  0, 21, 13, 24, 17, 52, 51, 58, 49,
        8, 56, 36, 57, 26, 29, 47, 27, 44, 18, 54,  9, 39, 53,  3, 25, 15,
       50,  4,  5, 19, 30, 38, 23, 20, 16, 34, 22,  6, 12, 41, 43, 10, 35,
        2, 11, 14, 28, 33, 48, 45, 37]), 'cur_cost': 233103.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([40, 55, 46,  1, 42, 32,  7, 31,  0, 21, 13, 24, 17, 52, 51, 58, 49,
        8, 56, 36, 57, 26, 29, 47, 27, 44, 18, 54,  9, 39, 53,  3, 25, 15,
       50,  4,  5, 19, 30, 38, 23, 20, 16, 34, 22,  6, 12, 41, 43, 10, 35,
        2, 11, 14, 28, 33, 48, 45, 37]), 'cur_cost': 233184.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([40, 42, 55, 46,  1, 32,  7, 31,  0, 21, 13, 24, 17, 52, 51, 58, 49,
        8, 56, 36, 57, 26, 29, 47, 27, 44, 18, 54,  9, 39, 53,  3, 25, 15,
       50,  4,  5, 19, 30, 38, 23, 20, 16, 34, 22,  6, 12, 41, 43, 10, 35,
        2, 11, 14, 28, 33, 48, 45, 37]), 'cur_cost': 227739.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 15, 21, 17, 16, 11, 19, 18, 13, 22, 14, 20, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27140.0, 'intermediate_solutions': [{'tour': [0, 8, 13, 10, 9, 4, 3, 5, 2, 1, 7, 6, 33, 31, 27, 34, 23, 25, 32, 29, 30, 26, 28, 24, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 19, 22, 14, 20, 16, 17, 15, 11, 12, 21, 18], 'cur_cost': 32493.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 13, 10, 9, 4, 3, 5, 2, 1, 7, 6, 33, 31, 27, 34, 23, 25, 32, 29, 30, 26, 28, 24, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 18, 22, 14, 20, 16, 17, 11, 15, 12, 21, 19], 'cur_cost': 32468.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 13, 10, 9, 4, 3, 5, 2, 1, 7, 6, 33, 31, 27, 16, 34, 23, 25, 32, 29, 30, 26, 28, 24, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 18, 22, 14, 20, 17, 15, 11, 12, 21, 19], 'cur_cost': 42586.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 23, 12, 16, 17, 15, 11, 21, 22, 13, 18, 19, 14, 20, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 31772.0, 'intermediate_solutions': [{'tour': [0, 4, 12, 19, 18, 13, 22, 14, 20, 16, 17, 15, 11, 21, 6, 29, 7, 8, 5, 3, 2, 9, 10, 28, 26, 30, 25, 32, 1, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 40829.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 12, 19, 18, 13, 22, 14, 20, 16, 17, 15, 11, 21, 6, 1, 7, 8, 5, 23, 34, 27, 31, 24, 29, 32, 25, 30, 26, 28, 10, 9, 2, 3, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 33837.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 12, 19, 18, 13, 22, 20, 16, 17, 15, 11, 21, 6, 14, 1, 7, 8, 5, 3, 2, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34588.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:10,630 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:10,630 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:10,633 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=27140.000, 多样性=0.908
2025-08-05 09:52:10,633 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 09:52:10,634 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 09:52:10,634 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:10,635 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.08138368242629952, 'best_improvement': -0.004032407236136288}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.05964912280701774}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.07039118971434442, 'recent_improvements': [0.015298059344662185, 0.032259105877261406, -0.12548432008402668], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 12, 'new_count': 12, 'count_change': 0, 'old_best_cost': 24451.0, 'new_best_cost': 24451.0, 'quality_improvement': 0.0, 'old_diversity': 0.7886492039034412, 'new_diversity': 0.7886492039034412, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:52:10,637 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 09:52:10,637 - __main__ - INFO - composite11_59 开始进化第 5 代
2025-08-05 09:52:10,637 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 09:52:10,637 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:10,638 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=27140.000, 多样性=0.908
2025-08-05 09:52:10,638 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:10,641 - PathExpert - INFO - 路径结构分析完成: 公共边数量=8, 路径相似性=0.908
2025-08-05 09:52:10,641 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:10,646 - EliteExpert - INFO - 精英解分析完成: 精英解数量=12, 多样性=0.789
2025-08-05 09:52:10,648 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 09:52:10,648 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:10,648 - LandscapeExpert - INFO - 添加精英解数据: 12个精英解
2025-08-05 09:52:10,648 - LandscapeExpert - INFO - 数据提取成功: 22个路径, 22个适应度值
2025-08-05 09:52:10,741 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.182, 适应度梯度: -24355.809, 聚类评分: 0.000, 覆盖率: 0.136, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:10,741 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 09:52:10,741 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:10,742 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite11_59
2025-08-05 09:52:10,749 - visualization.landscape_visualizer - INFO - 插值约束: 35 个点被约束到最小值 24451.00
2025-08-05 09:52:10,867 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite11_59\landscape_composite11_59_iter_115_20250805_095210.html
2025-08-05 09:52:10,920 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite11_59\dashboard_composite11_59_iter_115_20250805_095210.html
2025-08-05 09:52:10,921 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 115
2025-08-05 09:52:10,921 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 09:52:10,921 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2727秒
2025-08-05 09:52:10,921 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.18181818181818182, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -24355.809090909093, 'local_optima_density': 0.18181818181818182, 'gradient_variance': 7189654178.361737, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1362, 'fitness_entropy': 0.6270793558107531, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -24355.809)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.136)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358730.741567, 'performance_metrics': {}}}
2025-08-05 09:52:10,921 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:10,921 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:10,921 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:10,921 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:10,922 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:52:10,922 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 09:52:10,922 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:52:10,922 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:10,922 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:10,922 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:52:10,922 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:10,923 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:10,923 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 6} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:10,923 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-05 09:52:10,923 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:10,923 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:10,923 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 256420.0
2025-08-05 09:52:10,931 - ExploitationExpert - INFO - res_population_num: 13
2025-08-05 09:52:10,932 - ExploitationExpert - INFO - res_population_costs: [24451.0, 24451, 24451, 24451, 24472, 24473, 24482, 24482, 24495, 24495, 24511, 24529.0, 24451]
2025-08-05 09:52:10,932 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39, 40, 42,
       45, 41, 38, 36, 37, 43, 44,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 53, 48, 58, 52, 50, 54,
       49, 57, 56, 47, 55, 51, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  9,  4,  1,  6, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26,
       28, 39, 40, 42, 45, 41, 38, 36, 43, 37, 44, 46, 35, 54, 50, 53, 48,
       58, 52, 56, 47, 55, 51, 57, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11,
       12, 21, 19,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39,
       40, 46, 35, 44, 37, 36, 42, 45, 41, 38, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 15, 17, 12, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41, 38,
       36, 43, 37, 44, 46, 35,  2,  3], dtype=int64), array([ 0, 10,  6,  1,  7,  4,  9,  3,  5,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 17, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 15, 17, 12, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39, 40, 42, 45,
       41, 38, 36, 37, 43, 44,  2,  3], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 46, 35, 44, 37,
       36, 42, 45, 41, 38, 43,  2,  3], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16,
       15, 11, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49,  8,  5,  3,  2], dtype=int64)]
2025-08-05 09:52:10,937 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:10,937 - ExploitationExpert - INFO - populations: [{'tour': array([48, 13, 38, 20, 29, 16,  2, 37, 21, 50, 52,  6, 58, 39, 43, 45,  1,
       17, 35, 55, 36, 26, 30, 42, 27, 34, 41, 19, 14, 49,  4, 57, 10, 33,
        8, 22, 28,  0, 56,  5,  3, 53, 44, 54, 32,  9, 18, 40, 12, 46, 47,
       24, 25, 31,  7, 51, 11, 15, 23], dtype=int64), 'cur_cost': 256420.0}, {'tour': [0, 17, 3, 5, 4, 9, 10, 6, 1, 7, 8, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 15, 11, 12, 21, 19], 'cur_cost': 32564.0}, {'tour': [17, 16, 25, 10, 6, 1, 7, 28, 30, 32, 29, 40, 37, 33, 38, 11, 3, 18, 12, 4, 23, 48, 39, 26, 20, 5, 56, 31, 2, 9, 44, 57, 49, 52, 36, 53, 8, 21, 0, 58, 27, 22, 43, 45, 14, 15, 54, 41, 35, 19, 55, 50, 24, 42, 13, 34, 46, 47, 51], 'cur_cost': 222038.0}, {'tour': [17, 4, 0, 6, 1, 2, 37, 26, 30, 25, 3, 23, 48, 52, 31, 20, 28, 11, 35, 36, 53, 38, 43, 13, 49, 15, 21, 18, 55, 9, 44, 57, 27, 10, 8, 33, 29, 50, 32, 41, 34, 51, 45, 40, 14, 5, 16, 7, 39, 24, 19, 12, 22, 42, 54, 56, 47, 46, 58], 'cur_cost': 222840.0}, {'tour': [0, 20, 5, 14, 13, 18, 22, 21, 12, 11, 15, 17, 16, 19, 6, 1, 7, 8, 4, 9, 3, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34739.0}, {'tour': [57, 18, 22, 41, 37, 11, 3, 23, 5, 46, 54, 16, 49, 4, 1, 14, 28, 48, 55, 32, 24, 10, 20, 39, 17, 27, 33, 45, 44, 6, 12, 47, 34, 43, 35, 8, 53, 36, 30, 21, 38, 52, 50, 25, 29, 13, 26, 51, 58, 2, 56, 40, 9, 7, 42, 15, 31, 19, 0], 'cur_cost': 247898.0}, {'tour': [0, 7, 10, 20, 14, 13, 18, 22, 21, 12, 11, 15, 17, 16, 19, 6, 1, 4, 9, 3, 5, 8, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27148.0}, {'tour': [44, 17, 55, 2, 33, 16, 28, 3, 25, 26, 39, 0, 35, 53, 1, 13, 58, 48, 42, 30, 19, 31, 50, 20, 29, 34, 46, 8, 37, 18, 7, 24, 11, 23, 38, 56, 4, 10, 41, 32, 36, 40, 22, 12, 9, 15, 52, 47, 6, 49, 43, 57, 54, 14, 27, 5, 45, 21, 51], 'cur_cost': 252224.0}, {'tour': [0, 12, 15, 21, 17, 16, 11, 19, 18, 13, 22, 14, 20, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27140.0}, {'tour': [0, 23, 12, 16, 17, 15, 11, 21, 22, 13, 18, 19, 14, 20, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 31772.0}]
2025-08-05 09:52:10,938 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:10,938 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 297, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 297, 'cache_hits': 0, 'similarity_calculations': 1489, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:10,940 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([48, 13, 38, 20, 29, 16,  2, 37, 21, 50, 52,  6, 58, 39, 43, 45,  1,
       17, 35, 55, 36, 26, 30, 42, 27, 34, 41, 19, 14, 49,  4, 57, 10, 33,
        8, 22, 28,  0, 56,  5,  3, 53, 44, 54, 32,  9, 18, 40, 12, 46, 47,
       24, 25, 31,  7, 51, 11, 15, 23], dtype=int64), 'cur_cost': 256420.0, 'intermediate_solutions': [{'tour': array([19,  5, 21, 39, 18, 45,  3, 49, 38, 56, 51, 55,  1,  8, 36, 27, 31,
       16, 54, 34,  2, 33, 53, 52, 57, 13, 44, 37, 41, 47, 32, 24, 14, 50,
        6, 40, 29, 22, 43,  0,  7, 46, 11, 23, 26, 28, 48, 42, 12, 15, 25,
        4, 30, 17, 58,  9, 35, 20, 10]), 'cur_cost': 245461.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([39, 19,  5, 21, 18, 45,  3, 49, 38, 56, 51, 55,  1,  8, 36, 27, 31,
       16, 54, 34,  2, 33, 53, 52, 57, 13, 44, 37, 41, 47, 32, 24, 14, 50,
        6, 40, 29, 22, 43,  0,  7, 46, 11, 23, 26, 28, 48, 42, 12, 15, 25,
        4, 30, 17, 58,  9, 35, 20, 10]), 'cur_cost': 237935.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([18, 39, 19,  5, 21, 45,  3, 49, 38, 56, 51, 55,  1,  8, 36, 27, 31,
       16, 54, 34,  2, 33, 53, 52, 57, 13, 44, 37, 41, 47, 32, 24, 14, 50,
        6, 40, 29, 22, 43,  0,  7, 46, 11, 23, 26, 28, 48, 42, 12, 15, 25,
        4, 30, 17, 58,  9, 35, 20, 10]), 'cur_cost': 245460.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([21, 39, 19,  5, 18, 45,  3, 49, 38, 56, 51, 55,  1,  8, 36, 27, 31,
       16, 54, 34,  2, 33, 53, 52, 57, 13, 44, 37, 41, 47, 32, 24, 14, 50,
        6, 40, 29, 22, 43,  0,  7, 46, 11, 23, 26, 28, 48, 42, 12, 15, 25,
        4, 30, 17, 58,  9, 35, 20, 10]), 'cur_cost': 245460.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([21, 18, 39, 19,  5, 45,  3, 49, 38, 56, 51, 55,  1,  8, 36, 27, 31,
       16, 54, 34,  2, 33, 53, 52, 57, 13, 44, 37, 41, 47, 32, 24, 14, 50,
        6, 40, 29, 22, 43,  0,  7, 46, 11, 23, 26, 28, 48, 42, 12, 15, 25,
        4, 30, 17, 58,  9, 35, 20, 10]), 'cur_cost': 237884.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:10,940 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 256420.00)
2025-08-05 09:52:10,940 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:10,940 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:10,940 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:10,942 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 59
2025-08-05 09:52:10,942 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,942 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,942 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,943 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,943 - ExplorationExpert - INFO - 探索路径生成完成，成本: 221857.0, 路径长度: 59, 收集中间解: 3
2025-08-05 09:52:10,943 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [16, 3, 5, 4, 1, 6, 25, 7, 8, 40, 32, 24, 34, 33, 46, 42, 47, 38, 35, 52, 13, 29, 51, 26, 50, 57, 20, 15, 11, 21, 37, 58, 10, 17, 14, 43, 55, 39, 9, 28, 48, 53, 19, 44, 49, 0, 54, 22, 30, 2, 12, 18, 23, 36, 31, 45, 56, 41, 27], 'cur_cost': 221857.0, 'intermediate_solutions': [{'tour': [0, 17, 3, 5, 4, 9, 15, 6, 1, 7, 8, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 10, 11, 12, 21, 19], 'cur_cost': 47747.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 3, 5, 4, 9, 10, 6, 1, 7, 8, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 54, 49, 51, 57, 55, 56, 52, 58, 48, 53, 39, 40, 50, 47, 13, 18, 22, 14, 20, 16, 15, 11, 12, 21, 19], 'cur_cost': 43784.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 3, 5, 4, 9, 10, 6, 1, 51, 7, 8, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 15, 11, 12, 21, 19], 'cur_cost': 40243.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:10,943 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 221857.00)
2025-08-05 09:52:10,943 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:10,944 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:10,944 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:10,946 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 09:52:10,946 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,946 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,946 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,947 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,947 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32516.0, 路径长度: 59, 收集中间解: 3
2025-08-05 09:52:10,947 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 12, 9, 4, 3, 5, 8, 7, 6, 10, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 21, 19], 'cur_cost': 32516.0, 'intermediate_solutions': [{'tour': [17, 16, 25, 10, 6, 1, 7, 28, 30, 32, 29, 40, 37, 33, 38, 11, 3, 18, 12, 4, 23, 48, 39, 0, 20, 5, 56, 31, 2, 9, 44, 57, 49, 52, 36, 53, 8, 21, 26, 58, 27, 22, 43, 45, 14, 15, 54, 41, 35, 19, 55, 50, 24, 42, 13, 34, 46, 47, 51], 'cur_cost': 224419.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [17, 16, 25, 10, 6, 1, 7, 28, 30, 32, 29, 40, 37, 33, 38, 11, 3, 18, 12, 4, 23, 48, 39, 26, 20, 5, 56, 31, 2, 9, 44, 57, 49, 52, 36, 53, 8, 21, 0, 58, 27, 22, 43, 46, 34, 13, 42, 24, 50, 55, 19, 35, 41, 54, 15, 14, 45, 47, 51], 'cur_cost': 222058.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [17, 16, 25, 10, 6, 1, 7, 28, 30, 32, 29, 40, 37, 14, 33, 38, 11, 3, 18, 12, 4, 23, 48, 39, 26, 20, 5, 56, 31, 2, 9, 44, 57, 49, 52, 36, 53, 8, 21, 0, 58, 27, 22, 43, 45, 15, 54, 41, 35, 19, 55, 50, 24, 42, 13, 34, 46, 47, 51], 'cur_cost': 229861.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:10,947 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 32516.00)
2025-08-05 09:52:10,948 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:52:10,948 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:52:10,948 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:10,953 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 59
2025-08-05 09:52:10,953 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,953 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,953 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,954 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,954 - ExplorationExpert - INFO - 探索路径生成完成，成本: 169043.0, 路径长度: 59, 收集中间解: 3
2025-08-05 09:52:10,954 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [53, 7, 9, 23, 34, 5, 25, 27, 26, 46, 38, 32, 35, 31, 39, 41, 30, 36, 29, 44, 10, 17, 12, 2, 3, 24, 8, 50, 6, 13, 0, 21, 11, 22, 1, 49, 48, 51, 47, 52, 14, 15, 33, 37, 4, 54, 57, 19, 20, 18, 28, 43, 45, 42, 58, 56, 55, 16, 40], 'cur_cost': 169043.0, 'intermediate_solutions': [{'tour': [17, 4, 0, 6, 56, 2, 37, 26, 30, 25, 3, 23, 48, 52, 31, 20, 28, 11, 35, 36, 53, 38, 43, 13, 49, 15, 21, 18, 55, 9, 44, 57, 27, 10, 8, 33, 29, 50, 32, 41, 34, 51, 45, 40, 14, 5, 16, 7, 39, 24, 19, 12, 22, 42, 54, 1, 47, 46, 58], 'cur_cost': 238242.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [17, 4, 0, 6, 1, 2, 37, 26, 30, 25, 3, 23, 48, 52, 31, 20, 28, 11, 35, 36, 53, 38, 43, 13, 49, 15, 21, 18, 55, 9, 44, 57, 27, 10, 8, 33, 29, 50, 32, 41, 34, 51, 45, 40, 14, 5, 16, 7, 39, 24, 19, 56, 54, 42, 22, 12, 47, 46, 58], 'cur_cost': 233937.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [47, 17, 4, 0, 6, 1, 2, 37, 26, 30, 25, 3, 23, 48, 52, 31, 20, 28, 11, 35, 36, 53, 38, 43, 13, 49, 15, 21, 18, 55, 9, 44, 57, 27, 10, 8, 33, 29, 50, 32, 41, 34, 51, 45, 40, 14, 5, 16, 7, 39, 24, 19, 12, 22, 42, 54, 56, 46, 58], 'cur_cost': 222837.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:10,954 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 169043.00)
2025-08-05 09:52:10,954 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:10,954 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:10,954 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:10,960 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 59
2025-08-05 09:52:10,961 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,961 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,961 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,961 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,962 - ExplorationExpert - INFO - 探索路径生成完成，成本: 166100.0, 路径长度: 59, 收集中间解: 3
2025-08-05 09:52:10,962 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [57, 22, 4, 1, 0, 5, 8, 32, 10, 9, 13, 19, 20, 27, 37, 6, 15, 23, 26, 36, 25, 34, 44, 33, 45, 31, 3, 28, 2, 35, 46, 43, 40, 50, 18, 17, 24, 38, 29, 7, 30, 11, 55, 52, 53, 51, 56, 14, 12, 47, 54, 49, 21, 16, 58, 42, 39, 41, 48], 'cur_cost': 166100.0, 'intermediate_solutions': [{'tour': [0, 20, 5, 14, 13, 18, 22, 21, 12, 11, 15, 17, 16, 19, 6, 1, 7, 8, 4, 9, 3, 2, 10, 28, 26, 30, 45, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 25, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 54562.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 5, 14, 13, 18, 22, 21, 12, 11, 15, 17, 16, 19, 6, 1, 7, 8, 4, 9, 3, 2, 10, 28, 26, 44, 46, 35, 33, 23, 34, 27, 31, 24, 29, 32, 25, 30, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 44569.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 5, 14, 13, 18, 22, 21, 12, 11, 15, 16, 19, 6, 1, 7, 8, 4, 9, 3, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 17, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 45939.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:10,962 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 166100.00)
2025-08-05 09:52:10,963 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 09:52:10,963 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:10,963 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:10,963 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 242690.0
2025-08-05 09:52:10,972 - ExploitationExpert - INFO - res_population_num: 13
2025-08-05 09:52:10,972 - ExploitationExpert - INFO - res_population_costs: [24451.0, 24451, 24451, 24451, 24472, 24473, 24482, 24482, 24495, 24495, 24511, 24529.0, 24451]
2025-08-05 09:52:10,972 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39, 40, 42,
       45, 41, 38, 36, 37, 43, 44,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 53, 48, 58, 52, 50, 54,
       49, 57, 56, 47, 55, 51, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  9,  4,  1,  6, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26,
       28, 39, 40, 42, 45, 41, 38, 36, 43, 37, 44, 46, 35, 54, 50, 53, 48,
       58, 52, 56, 47, 55, 51, 57, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11,
       12, 21, 19,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39,
       40, 46, 35, 44, 37, 36, 42, 45, 41, 38, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 15, 17, 12, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41, 38,
       36, 43, 37, 44, 46, 35,  2,  3], dtype=int64), array([ 0, 10,  6,  1,  7,  4,  9,  3,  5,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 17, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 15, 17, 12, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39, 40, 42, 45,
       41, 38, 36, 37, 43, 44,  2,  3], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 46, 35, 44, 37,
       36, 42, 45, 41, 38, 43,  2,  3], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16,
       15, 11, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49,  8,  5,  3,  2], dtype=int64)]
2025-08-05 09:52:10,976 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:10,977 - ExploitationExpert - INFO - populations: [{'tour': array([48, 13, 38, 20, 29, 16,  2, 37, 21, 50, 52,  6, 58, 39, 43, 45,  1,
       17, 35, 55, 36, 26, 30, 42, 27, 34, 41, 19, 14, 49,  4, 57, 10, 33,
        8, 22, 28,  0, 56,  5,  3, 53, 44, 54, 32,  9, 18, 40, 12, 46, 47,
       24, 25, 31,  7, 51, 11, 15, 23], dtype=int64), 'cur_cost': 256420.0}, {'tour': [16, 3, 5, 4, 1, 6, 25, 7, 8, 40, 32, 24, 34, 33, 46, 42, 47, 38, 35, 52, 13, 29, 51, 26, 50, 57, 20, 15, 11, 21, 37, 58, 10, 17, 14, 43, 55, 39, 9, 28, 48, 53, 19, 44, 49, 0, 54, 22, 30, 2, 12, 18, 23, 36, 31, 45, 56, 41, 27], 'cur_cost': 221857.0}, {'tour': [0, 1, 12, 9, 4, 3, 5, 8, 7, 6, 10, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 21, 19], 'cur_cost': 32516.0}, {'tour': [53, 7, 9, 23, 34, 5, 25, 27, 26, 46, 38, 32, 35, 31, 39, 41, 30, 36, 29, 44, 10, 17, 12, 2, 3, 24, 8, 50, 6, 13, 0, 21, 11, 22, 1, 49, 48, 51, 47, 52, 14, 15, 33, 37, 4, 54, 57, 19, 20, 18, 28, 43, 45, 42, 58, 56, 55, 16, 40], 'cur_cost': 169043.0}, {'tour': [57, 22, 4, 1, 0, 5, 8, 32, 10, 9, 13, 19, 20, 27, 37, 6, 15, 23, 26, 36, 25, 34, 44, 33, 45, 31, 3, 28, 2, 35, 46, 43, 40, 50, 18, 17, 24, 38, 29, 7, 30, 11, 55, 52, 53, 51, 56, 14, 12, 47, 54, 49, 21, 16, 58, 42, 39, 41, 48], 'cur_cost': 166100.0}, {'tour': array([36, 28, 46, 12, 30, 41, 45, 32, 49, 21, 16, 44, 58, 27, 38, 15,  1,
       13, 35,  6, 23, 24, 56, 18, 39,  3, 26, 31, 52,  0,  5, 47, 19, 57,
       11, 48,  9, 10, 42,  8, 17, 33, 40, 43, 37, 51, 53, 55,  4,  7, 29,
       25,  2, 14, 50, 20, 22, 54, 34], dtype=int64), 'cur_cost': 242690.0}, {'tour': [0, 7, 10, 20, 14, 13, 18, 22, 21, 12, 11, 15, 17, 16, 19, 6, 1, 4, 9, 3, 5, 8, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27148.0}, {'tour': [44, 17, 55, 2, 33, 16, 28, 3, 25, 26, 39, 0, 35, 53, 1, 13, 58, 48, 42, 30, 19, 31, 50, 20, 29, 34, 46, 8, 37, 18, 7, 24, 11, 23, 38, 56, 4, 10, 41, 32, 36, 40, 22, 12, 9, 15, 52, 47, 6, 49, 43, 57, 54, 14, 27, 5, 45, 21, 51], 'cur_cost': 252224.0}, {'tour': [0, 12, 15, 21, 17, 16, 11, 19, 18, 13, 22, 14, 20, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27140.0}, {'tour': [0, 23, 12, 16, 17, 15, 11, 21, 22, 13, 18, 19, 14, 20, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 31772.0}]
2025-08-05 09:52:10,978 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:10,978 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 298, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 298, 'cache_hits': 0, 'similarity_calculations': 1500, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:10,979 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([36, 28, 46, 12, 30, 41, 45, 32, 49, 21, 16, 44, 58, 27, 38, 15,  1,
       13, 35,  6, 23, 24, 56, 18, 39,  3, 26, 31, 52,  0,  5, 47, 19, 57,
       11, 48,  9, 10, 42,  8, 17, 33, 40, 43, 37, 51, 53, 55,  4,  7, 29,
       25,  2, 14, 50, 20, 22, 54, 34], dtype=int64), 'cur_cost': 242690.0, 'intermediate_solutions': [{'tour': array([22, 18, 57, 41, 37, 11,  3, 23,  5, 46, 54, 16, 49,  4,  1, 14, 28,
       48, 55, 32, 24, 10, 20, 39, 17, 27, 33, 45, 44,  6, 12, 47, 34, 43,
       35,  8, 53, 36, 30, 21, 38, 52, 50, 25, 29, 13, 26, 51, 58,  2, 56,
       40,  9,  7, 42, 15, 31, 19,  0]), 'cur_cost': 245768.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([41, 22, 18, 57, 37, 11,  3, 23,  5, 46, 54, 16, 49,  4,  1, 14, 28,
       48, 55, 32, 24, 10, 20, 39, 17, 27, 33, 45, 44,  6, 12, 47, 34, 43,
       35,  8, 53, 36, 30, 21, 38, 52, 50, 25, 29, 13, 26, 51, 58,  2, 56,
       40,  9,  7, 42, 15, 31, 19,  0]), 'cur_cost': 253564.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([37, 41, 22, 18, 57, 11,  3, 23,  5, 46, 54, 16, 49,  4,  1, 14, 28,
       48, 55, 32, 24, 10, 20, 39, 17, 27, 33, 45, 44,  6, 12, 47, 34, 43,
       35,  8, 53, 36, 30, 21, 38, 52, 50, 25, 29, 13, 26, 51, 58,  2, 56,
       40,  9,  7, 42, 15, 31, 19,  0]), 'cur_cost': 245780.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([57, 41, 22, 18, 37, 11,  3, 23,  5, 46, 54, 16, 49,  4,  1, 14, 28,
       48, 55, 32, 24, 10, 20, 39, 17, 27, 33, 45, 44,  6, 12, 47, 34, 43,
       35,  8, 53, 36, 30, 21, 38, 52, 50, 25, 29, 13, 26, 51, 58,  2, 56,
       40,  9,  7, 42, 15, 31, 19,  0]), 'cur_cost': 255723.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([57, 37, 41, 22, 18, 11,  3, 23,  5, 46, 54, 16, 49,  4,  1, 14, 28,
       48, 55, 32, 24, 10, 20, 39, 17, 27, 33, 45, 44,  6, 12, 47, 34, 43,
       35,  8, 53, 36, 30, 21, 38, 52, 50, 25, 29, 13, 26, 51, 58,  2, 56,
       40,  9,  7, 42, 15, 31, 19,  0]), 'cur_cost': 240395.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:10,979 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 242690.00)
2025-08-05 09:52:10,979 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:10,980 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:10,980 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:10,984 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 59
2025-08-05 09:52:10,985 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,985 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,985 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,985 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:10,986 - ExplorationExpert - INFO - 探索路径生成完成，成本: 160982.0, 路径长度: 59, 收集中间解: 3
2025-08-05 09:52:10,986 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [33, 31, 6, 7, 23, 43, 44, 0, 17, 13, 16, 5, 10, 3, 1, 12, 25, 24, 39, 29, 27, 8, 34, 19, 14, 30, 11, 26, 42, 46, 45, 54, 52, 51, 53, 47, 4, 15, 32, 41, 9, 21, 2, 35, 50, 56, 48, 57, 18, 49, 22, 55, 58, 20, 37, 38, 36, 40, 28], 'cur_cost': 160982.0, 'intermediate_solutions': [{'tour': [0, 7, 10, 20, 14, 13, 18, 22, 21, 12, 11, 15, 17, 16, 19, 6, 1, 4, 9, 3, 5, 8, 2, 28, 26, 30, 25, 37, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 32, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 46948.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 10, 20, 14, 13, 18, 22, 21, 12, 11, 15, 17, 16, 19, 6, 1, 4, 9, 3, 5, 8, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 47, 50, 54, 49, 51, 57, 55, 56, 52, 58, 48, 53, 39, 40, 38, 41, 45, 42, 36, 43, 37, 44, 46], 'cur_cost': 32784.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 10, 20, 14, 13, 18, 22, 21, 12, 11, 15, 17, 16, 19, 6, 1, 4, 9, 3, 5, 8, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 50, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 47], 'cur_cost': 27148.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:10,986 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 160982.00)
2025-08-05 09:52:10,986 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 09:52:10,986 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:10,986 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:10,987 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 246100.0
2025-08-05 09:52:10,994 - ExploitationExpert - INFO - res_population_num: 13
2025-08-05 09:52:10,994 - ExploitationExpert - INFO - res_population_costs: [24451.0, 24451, 24451, 24451, 24472, 24473, 24482, 24482, 24495, 24495, 24511, 24529.0, 24451]
2025-08-05 09:52:10,994 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39, 40, 42,
       45, 41, 38, 36, 37, 43, 44,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 53, 48, 58, 52, 50, 54,
       49, 57, 56, 47, 55, 51, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  9,  4,  1,  6, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26,
       28, 39, 40, 42, 45, 41, 38, 36, 43, 37, 44, 46, 35, 54, 50, 53, 48,
       58, 52, 56, 47, 55, 51, 57, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11,
       12, 21, 19,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39,
       40, 46, 35, 44, 37, 36, 42, 45, 41, 38, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 15, 17, 12, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41, 38,
       36, 43, 37, 44, 46, 35,  2,  3], dtype=int64), array([ 0, 10,  6,  1,  7,  4,  9,  3,  5,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 17, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 21, 22, 18, 13, 14, 20, 16, 15, 17, 12, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39, 40, 42, 45,
       41, 38, 36, 37, 43, 44,  2,  3], dtype=int64), array([ 0,  9, 10,  6,  1,  7,  4,  5,  8, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11, 33,
       31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 46, 35, 44, 37,
       36, 42, 45, 41, 38, 43,  2,  3], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16,
       15, 11, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49,  8,  5,  3,  2], dtype=int64)]
2025-08-05 09:52:10,999 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:10,999 - ExploitationExpert - INFO - populations: [{'tour': array([48, 13, 38, 20, 29, 16,  2, 37, 21, 50, 52,  6, 58, 39, 43, 45,  1,
       17, 35, 55, 36, 26, 30, 42, 27, 34, 41, 19, 14, 49,  4, 57, 10, 33,
        8, 22, 28,  0, 56,  5,  3, 53, 44, 54, 32,  9, 18, 40, 12, 46, 47,
       24, 25, 31,  7, 51, 11, 15, 23], dtype=int64), 'cur_cost': 256420.0}, {'tour': [16, 3, 5, 4, 1, 6, 25, 7, 8, 40, 32, 24, 34, 33, 46, 42, 47, 38, 35, 52, 13, 29, 51, 26, 50, 57, 20, 15, 11, 21, 37, 58, 10, 17, 14, 43, 55, 39, 9, 28, 48, 53, 19, 44, 49, 0, 54, 22, 30, 2, 12, 18, 23, 36, 31, 45, 56, 41, 27], 'cur_cost': 221857.0}, {'tour': [0, 1, 12, 9, 4, 3, 5, 8, 7, 6, 10, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 21, 19], 'cur_cost': 32516.0}, {'tour': [53, 7, 9, 23, 34, 5, 25, 27, 26, 46, 38, 32, 35, 31, 39, 41, 30, 36, 29, 44, 10, 17, 12, 2, 3, 24, 8, 50, 6, 13, 0, 21, 11, 22, 1, 49, 48, 51, 47, 52, 14, 15, 33, 37, 4, 54, 57, 19, 20, 18, 28, 43, 45, 42, 58, 56, 55, 16, 40], 'cur_cost': 169043.0}, {'tour': [57, 22, 4, 1, 0, 5, 8, 32, 10, 9, 13, 19, 20, 27, 37, 6, 15, 23, 26, 36, 25, 34, 44, 33, 45, 31, 3, 28, 2, 35, 46, 43, 40, 50, 18, 17, 24, 38, 29, 7, 30, 11, 55, 52, 53, 51, 56, 14, 12, 47, 54, 49, 21, 16, 58, 42, 39, 41, 48], 'cur_cost': 166100.0}, {'tour': array([36, 28, 46, 12, 30, 41, 45, 32, 49, 21, 16, 44, 58, 27, 38, 15,  1,
       13, 35,  6, 23, 24, 56, 18, 39,  3, 26, 31, 52,  0,  5, 47, 19, 57,
       11, 48,  9, 10, 42,  8, 17, 33, 40, 43, 37, 51, 53, 55,  4,  7, 29,
       25,  2, 14, 50, 20, 22, 54, 34], dtype=int64), 'cur_cost': 242690.0}, {'tour': [33, 31, 6, 7, 23, 43, 44, 0, 17, 13, 16, 5, 10, 3, 1, 12, 25, 24, 39, 29, 27, 8, 34, 19, 14, 30, 11, 26, 42, 46, 45, 54, 52, 51, 53, 47, 4, 15, 32, 41, 9, 21, 2, 35, 50, 56, 48, 57, 18, 49, 22, 55, 58, 20, 37, 38, 36, 40, 28], 'cur_cost': 160982.0}, {'tour': array([ 2, 53, 58, 25, 31,  8, 23, 20, 15, 32, 27, 10, 36, 18,  3, 46, 19,
       57, 43, 34, 47,  7, 55, 35, 29,  6, 49, 12, 48,  5, 33, 17, 54, 51,
        4,  1, 40, 50, 13, 16, 22, 44, 56, 41, 38, 42, 24,  9, 45, 28, 21,
       26,  0, 39, 11, 30, 14, 37, 52], dtype=int64), 'cur_cost': 246100.0}, {'tour': [0, 12, 15, 21, 17, 16, 11, 19, 18, 13, 22, 14, 20, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27140.0}, {'tour': [0, 23, 12, 16, 17, 15, 11, 21, 22, 13, 18, 19, 14, 20, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 31772.0}]
2025-08-05 09:52:11,000 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:11,000 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 299, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 299, 'cache_hits': 0, 'similarity_calculations': 1512, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:11,001 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([ 2, 53, 58, 25, 31,  8, 23, 20, 15, 32, 27, 10, 36, 18,  3, 46, 19,
       57, 43, 34, 47,  7, 55, 35, 29,  6, 49, 12, 48,  5, 33, 17, 54, 51,
        4,  1, 40, 50, 13, 16, 22, 44, 56, 41, 38, 42, 24,  9, 45, 28, 21,
       26,  0, 39, 11, 30, 14, 37, 52], dtype=int64), 'cur_cost': 246100.0, 'intermediate_solutions': [{'tour': array([55, 17, 44,  2, 33, 16, 28,  3, 25, 26, 39,  0, 35, 53,  1, 13, 58,
       48, 42, 30, 19, 31, 50, 20, 29, 34, 46,  8, 37, 18,  7, 24, 11, 23,
       38, 56,  4, 10, 41, 32, 36, 40, 22, 12,  9, 15, 52, 47,  6, 49, 43,
       57, 54, 14, 27,  5, 45, 21, 51]), 'cur_cost': 246537.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2, 55, 17, 44, 33, 16, 28,  3, 25, 26, 39,  0, 35, 53,  1, 13, 58,
       48, 42, 30, 19, 31, 50, 20, 29, 34, 46,  8, 37, 18,  7, 24, 11, 23,
       38, 56,  4, 10, 41, 32, 36, 40, 22, 12,  9, 15, 52, 47,  6, 49, 43,
       57, 54, 14, 27,  5, 45, 21, 51]), 'cur_cost': 251957.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([33,  2, 55, 17, 44, 16, 28,  3, 25, 26, 39,  0, 35, 53,  1, 13, 58,
       48, 42, 30, 19, 31, 50, 20, 29, 34, 46,  8, 37, 18,  7, 24, 11, 23,
       38, 56,  4, 10, 41, 32, 36, 40, 22, 12,  9, 15, 52, 47,  6, 49, 43,
       57, 54, 14, 27,  5, 45, 21, 51]), 'cur_cost': 256486.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([44,  2, 55, 17, 33, 16, 28,  3, 25, 26, 39,  0, 35, 53,  1, 13, 58,
       48, 42, 30, 19, 31, 50, 20, 29, 34, 46,  8, 37, 18,  7, 24, 11, 23,
       38, 56,  4, 10, 41, 32, 36, 40, 22, 12,  9, 15, 52, 47,  6, 49, 43,
       57, 54, 14, 27,  5, 45, 21, 51]), 'cur_cost': 250033.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([44, 33,  2, 55, 17, 16, 28,  3, 25, 26, 39,  0, 35, 53,  1, 13, 58,
       48, 42, 30, 19, 31, 50, 20, 29, 34, 46,  8, 37, 18,  7, 24, 11, 23,
       38, 56,  4, 10, 41, 32, 36, 40, 22, 12,  9, 15, 52, 47,  6, 49, 43,
       57, 54, 14, 27,  5, 45, 21, 51]), 'cur_cost': 244363.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:11,002 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 246100.00)
2025-08-05 09:52:11,002 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:52:11,002 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:52:11,002 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:11,004 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 09:52:11,005 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,005 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,005 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,005 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,006 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27129.0, 路径长度: 59, 收集中间解: 3
2025-08-05 09:52:11,006 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 18, 20, 19, 21, 12, 11, 15, 17, 16, 22, 13, 14, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27129.0, 'intermediate_solutions': [{'tour': [0, 12, 15, 21, 17, 16, 3, 19, 18, 13, 22, 14, 20, 6, 1, 7, 8, 5, 11, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 42336.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 12, 15, 21, 17, 16, 11, 19, 18, 13, 22, 14, 20, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 50, 54, 49, 51, 57, 55, 56, 52, 58, 48, 53, 39, 47], 'cur_cost': 38461.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 15, 21, 17, 16, 11, 19, 18, 22, 14, 20, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 13, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 37334.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:11,006 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 27129.00)
2025-08-05 09:52:11,007 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:11,007 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:11,007 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:11,009 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 09:52:11,010 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,010 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,010 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,010 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,011 - ExplorationExpert - INFO - 探索路径生成完成，成本: 34436.0, 路径长度: 59, 收集中间解: 3
2025-08-05 09:52:11,011 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 18, 6, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34436.0, 'intermediate_solutions': [{'tour': [0, 23, 12, 16, 17, 15, 11, 21, 22, 13, 18, 19, 14, 20, 2, 1, 7, 8, 5, 3, 6, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 31936.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 23, 12, 16, 17, 15, 11, 21, 22, 13, 18, 19, 14, 20, 6, 1, 7, 8, 5, 51, 57, 55, 56, 52, 58, 48, 53, 39, 40, 38, 41, 45, 42, 36, 43, 37, 44, 46, 35, 33, 34, 27, 31, 24, 29, 32, 25, 30, 26, 28, 10, 4, 9, 2, 3, 49, 54, 50, 47], 'cur_cost': 39424.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 23, 12, 16, 17, 15, 11, 21, 22, 18, 19, 14, 20, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 13, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 37322.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:11,011 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 34436.00)
2025-08-05 09:52:11,011 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:11,011 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:11,015 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([48, 13, 38, 20, 29, 16,  2, 37, 21, 50, 52,  6, 58, 39, 43, 45,  1,
       17, 35, 55, 36, 26, 30, 42, 27, 34, 41, 19, 14, 49,  4, 57, 10, 33,
        8, 22, 28,  0, 56,  5,  3, 53, 44, 54, 32,  9, 18, 40, 12, 46, 47,
       24, 25, 31,  7, 51, 11, 15, 23], dtype=int64), 'cur_cost': 256420.0, 'intermediate_solutions': [{'tour': array([19,  5, 21, 39, 18, 45,  3, 49, 38, 56, 51, 55,  1,  8, 36, 27, 31,
       16, 54, 34,  2, 33, 53, 52, 57, 13, 44, 37, 41, 47, 32, 24, 14, 50,
        6, 40, 29, 22, 43,  0,  7, 46, 11, 23, 26, 28, 48, 42, 12, 15, 25,
        4, 30, 17, 58,  9, 35, 20, 10]), 'cur_cost': 245461.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([39, 19,  5, 21, 18, 45,  3, 49, 38, 56, 51, 55,  1,  8, 36, 27, 31,
       16, 54, 34,  2, 33, 53, 52, 57, 13, 44, 37, 41, 47, 32, 24, 14, 50,
        6, 40, 29, 22, 43,  0,  7, 46, 11, 23, 26, 28, 48, 42, 12, 15, 25,
        4, 30, 17, 58,  9, 35, 20, 10]), 'cur_cost': 237935.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([18, 39, 19,  5, 21, 45,  3, 49, 38, 56, 51, 55,  1,  8, 36, 27, 31,
       16, 54, 34,  2, 33, 53, 52, 57, 13, 44, 37, 41, 47, 32, 24, 14, 50,
        6, 40, 29, 22, 43,  0,  7, 46, 11, 23, 26, 28, 48, 42, 12, 15, 25,
        4, 30, 17, 58,  9, 35, 20, 10]), 'cur_cost': 245460.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([21, 39, 19,  5, 18, 45,  3, 49, 38, 56, 51, 55,  1,  8, 36, 27, 31,
       16, 54, 34,  2, 33, 53, 52, 57, 13, 44, 37, 41, 47, 32, 24, 14, 50,
        6, 40, 29, 22, 43,  0,  7, 46, 11, 23, 26, 28, 48, 42, 12, 15, 25,
        4, 30, 17, 58,  9, 35, 20, 10]), 'cur_cost': 245460.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([21, 18, 39, 19,  5, 45,  3, 49, 38, 56, 51, 55,  1,  8, 36, 27, 31,
       16, 54, 34,  2, 33, 53, 52, 57, 13, 44, 37, 41, 47, 32, 24, 14, 50,
        6, 40, 29, 22, 43,  0,  7, 46, 11, 23, 26, 28, 48, 42, 12, 15, 25,
        4, 30, 17, 58,  9, 35, 20, 10]), 'cur_cost': 237884.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [16, 3, 5, 4, 1, 6, 25, 7, 8, 40, 32, 24, 34, 33, 46, 42, 47, 38, 35, 52, 13, 29, 51, 26, 50, 57, 20, 15, 11, 21, 37, 58, 10, 17, 14, 43, 55, 39, 9, 28, 48, 53, 19, 44, 49, 0, 54, 22, 30, 2, 12, 18, 23, 36, 31, 45, 56, 41, 27], 'cur_cost': 221857.0, 'intermediate_solutions': [{'tour': [0, 17, 3, 5, 4, 9, 15, 6, 1, 7, 8, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 10, 11, 12, 21, 19], 'cur_cost': 47747.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 3, 5, 4, 9, 10, 6, 1, 7, 8, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 54, 49, 51, 57, 55, 56, 52, 58, 48, 53, 39, 40, 50, 47, 13, 18, 22, 14, 20, 16, 15, 11, 12, 21, 19], 'cur_cost': 43784.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 3, 5, 4, 9, 10, 6, 1, 51, 7, 8, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 15, 11, 12, 21, 19], 'cur_cost': 40243.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 12, 9, 4, 3, 5, 8, 7, 6, 10, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 21, 19], 'cur_cost': 32516.0, 'intermediate_solutions': [{'tour': [17, 16, 25, 10, 6, 1, 7, 28, 30, 32, 29, 40, 37, 33, 38, 11, 3, 18, 12, 4, 23, 48, 39, 0, 20, 5, 56, 31, 2, 9, 44, 57, 49, 52, 36, 53, 8, 21, 26, 58, 27, 22, 43, 45, 14, 15, 54, 41, 35, 19, 55, 50, 24, 42, 13, 34, 46, 47, 51], 'cur_cost': 224419.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [17, 16, 25, 10, 6, 1, 7, 28, 30, 32, 29, 40, 37, 33, 38, 11, 3, 18, 12, 4, 23, 48, 39, 26, 20, 5, 56, 31, 2, 9, 44, 57, 49, 52, 36, 53, 8, 21, 0, 58, 27, 22, 43, 46, 34, 13, 42, 24, 50, 55, 19, 35, 41, 54, 15, 14, 45, 47, 51], 'cur_cost': 222058.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [17, 16, 25, 10, 6, 1, 7, 28, 30, 32, 29, 40, 37, 14, 33, 38, 11, 3, 18, 12, 4, 23, 48, 39, 26, 20, 5, 56, 31, 2, 9, 44, 57, 49, 52, 36, 53, 8, 21, 0, 58, 27, 22, 43, 45, 15, 54, 41, 35, 19, 55, 50, 24, 42, 13, 34, 46, 47, 51], 'cur_cost': 229861.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [53, 7, 9, 23, 34, 5, 25, 27, 26, 46, 38, 32, 35, 31, 39, 41, 30, 36, 29, 44, 10, 17, 12, 2, 3, 24, 8, 50, 6, 13, 0, 21, 11, 22, 1, 49, 48, 51, 47, 52, 14, 15, 33, 37, 4, 54, 57, 19, 20, 18, 28, 43, 45, 42, 58, 56, 55, 16, 40], 'cur_cost': 169043.0, 'intermediate_solutions': [{'tour': [17, 4, 0, 6, 56, 2, 37, 26, 30, 25, 3, 23, 48, 52, 31, 20, 28, 11, 35, 36, 53, 38, 43, 13, 49, 15, 21, 18, 55, 9, 44, 57, 27, 10, 8, 33, 29, 50, 32, 41, 34, 51, 45, 40, 14, 5, 16, 7, 39, 24, 19, 12, 22, 42, 54, 1, 47, 46, 58], 'cur_cost': 238242.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [17, 4, 0, 6, 1, 2, 37, 26, 30, 25, 3, 23, 48, 52, 31, 20, 28, 11, 35, 36, 53, 38, 43, 13, 49, 15, 21, 18, 55, 9, 44, 57, 27, 10, 8, 33, 29, 50, 32, 41, 34, 51, 45, 40, 14, 5, 16, 7, 39, 24, 19, 56, 54, 42, 22, 12, 47, 46, 58], 'cur_cost': 233937.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [47, 17, 4, 0, 6, 1, 2, 37, 26, 30, 25, 3, 23, 48, 52, 31, 20, 28, 11, 35, 36, 53, 38, 43, 13, 49, 15, 21, 18, 55, 9, 44, 57, 27, 10, 8, 33, 29, 50, 32, 41, 34, 51, 45, 40, 14, 5, 16, 7, 39, 24, 19, 12, 22, 42, 54, 56, 46, 58], 'cur_cost': 222837.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [57, 22, 4, 1, 0, 5, 8, 32, 10, 9, 13, 19, 20, 27, 37, 6, 15, 23, 26, 36, 25, 34, 44, 33, 45, 31, 3, 28, 2, 35, 46, 43, 40, 50, 18, 17, 24, 38, 29, 7, 30, 11, 55, 52, 53, 51, 56, 14, 12, 47, 54, 49, 21, 16, 58, 42, 39, 41, 48], 'cur_cost': 166100.0, 'intermediate_solutions': [{'tour': [0, 20, 5, 14, 13, 18, 22, 21, 12, 11, 15, 17, 16, 19, 6, 1, 7, 8, 4, 9, 3, 2, 10, 28, 26, 30, 45, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 25, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 54562.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 5, 14, 13, 18, 22, 21, 12, 11, 15, 17, 16, 19, 6, 1, 7, 8, 4, 9, 3, 2, 10, 28, 26, 44, 46, 35, 33, 23, 34, 27, 31, 24, 29, 32, 25, 30, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 44569.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 5, 14, 13, 18, 22, 21, 12, 11, 15, 16, 19, 6, 1, 7, 8, 4, 9, 3, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 17, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 45939.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([36, 28, 46, 12, 30, 41, 45, 32, 49, 21, 16, 44, 58, 27, 38, 15,  1,
       13, 35,  6, 23, 24, 56, 18, 39,  3, 26, 31, 52,  0,  5, 47, 19, 57,
       11, 48,  9, 10, 42,  8, 17, 33, 40, 43, 37, 51, 53, 55,  4,  7, 29,
       25,  2, 14, 50, 20, 22, 54, 34], dtype=int64), 'cur_cost': 242690.0, 'intermediate_solutions': [{'tour': array([22, 18, 57, 41, 37, 11,  3, 23,  5, 46, 54, 16, 49,  4,  1, 14, 28,
       48, 55, 32, 24, 10, 20, 39, 17, 27, 33, 45, 44,  6, 12, 47, 34, 43,
       35,  8, 53, 36, 30, 21, 38, 52, 50, 25, 29, 13, 26, 51, 58,  2, 56,
       40,  9,  7, 42, 15, 31, 19,  0]), 'cur_cost': 245768.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([41, 22, 18, 57, 37, 11,  3, 23,  5, 46, 54, 16, 49,  4,  1, 14, 28,
       48, 55, 32, 24, 10, 20, 39, 17, 27, 33, 45, 44,  6, 12, 47, 34, 43,
       35,  8, 53, 36, 30, 21, 38, 52, 50, 25, 29, 13, 26, 51, 58,  2, 56,
       40,  9,  7, 42, 15, 31, 19,  0]), 'cur_cost': 253564.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([37, 41, 22, 18, 57, 11,  3, 23,  5, 46, 54, 16, 49,  4,  1, 14, 28,
       48, 55, 32, 24, 10, 20, 39, 17, 27, 33, 45, 44,  6, 12, 47, 34, 43,
       35,  8, 53, 36, 30, 21, 38, 52, 50, 25, 29, 13, 26, 51, 58,  2, 56,
       40,  9,  7, 42, 15, 31, 19,  0]), 'cur_cost': 245780.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([57, 41, 22, 18, 37, 11,  3, 23,  5, 46, 54, 16, 49,  4,  1, 14, 28,
       48, 55, 32, 24, 10, 20, 39, 17, 27, 33, 45, 44,  6, 12, 47, 34, 43,
       35,  8, 53, 36, 30, 21, 38, 52, 50, 25, 29, 13, 26, 51, 58,  2, 56,
       40,  9,  7, 42, 15, 31, 19,  0]), 'cur_cost': 255723.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([57, 37, 41, 22, 18, 11,  3, 23,  5, 46, 54, 16, 49,  4,  1, 14, 28,
       48, 55, 32, 24, 10, 20, 39, 17, 27, 33, 45, 44,  6, 12, 47, 34, 43,
       35,  8, 53, 36, 30, 21, 38, 52, 50, 25, 29, 13, 26, 51, 58,  2, 56,
       40,  9,  7, 42, 15, 31, 19,  0]), 'cur_cost': 240395.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [33, 31, 6, 7, 23, 43, 44, 0, 17, 13, 16, 5, 10, 3, 1, 12, 25, 24, 39, 29, 27, 8, 34, 19, 14, 30, 11, 26, 42, 46, 45, 54, 52, 51, 53, 47, 4, 15, 32, 41, 9, 21, 2, 35, 50, 56, 48, 57, 18, 49, 22, 55, 58, 20, 37, 38, 36, 40, 28], 'cur_cost': 160982.0, 'intermediate_solutions': [{'tour': [0, 7, 10, 20, 14, 13, 18, 22, 21, 12, 11, 15, 17, 16, 19, 6, 1, 4, 9, 3, 5, 8, 2, 28, 26, 30, 25, 37, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 32, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 46948.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 10, 20, 14, 13, 18, 22, 21, 12, 11, 15, 17, 16, 19, 6, 1, 4, 9, 3, 5, 8, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 47, 50, 54, 49, 51, 57, 55, 56, 52, 58, 48, 53, 39, 40, 38, 41, 45, 42, 36, 43, 37, 44, 46], 'cur_cost': 32784.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 10, 20, 14, 13, 18, 22, 21, 12, 11, 15, 17, 16, 19, 6, 1, 4, 9, 3, 5, 8, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 50, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 47], 'cur_cost': 27148.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 2, 53, 58, 25, 31,  8, 23, 20, 15, 32, 27, 10, 36, 18,  3, 46, 19,
       57, 43, 34, 47,  7, 55, 35, 29,  6, 49, 12, 48,  5, 33, 17, 54, 51,
        4,  1, 40, 50, 13, 16, 22, 44, 56, 41, 38, 42, 24,  9, 45, 28, 21,
       26,  0, 39, 11, 30, 14, 37, 52], dtype=int64), 'cur_cost': 246100.0, 'intermediate_solutions': [{'tour': array([55, 17, 44,  2, 33, 16, 28,  3, 25, 26, 39,  0, 35, 53,  1, 13, 58,
       48, 42, 30, 19, 31, 50, 20, 29, 34, 46,  8, 37, 18,  7, 24, 11, 23,
       38, 56,  4, 10, 41, 32, 36, 40, 22, 12,  9, 15, 52, 47,  6, 49, 43,
       57, 54, 14, 27,  5, 45, 21, 51]), 'cur_cost': 246537.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2, 55, 17, 44, 33, 16, 28,  3, 25, 26, 39,  0, 35, 53,  1, 13, 58,
       48, 42, 30, 19, 31, 50, 20, 29, 34, 46,  8, 37, 18,  7, 24, 11, 23,
       38, 56,  4, 10, 41, 32, 36, 40, 22, 12,  9, 15, 52, 47,  6, 49, 43,
       57, 54, 14, 27,  5, 45, 21, 51]), 'cur_cost': 251957.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([33,  2, 55, 17, 44, 16, 28,  3, 25, 26, 39,  0, 35, 53,  1, 13, 58,
       48, 42, 30, 19, 31, 50, 20, 29, 34, 46,  8, 37, 18,  7, 24, 11, 23,
       38, 56,  4, 10, 41, 32, 36, 40, 22, 12,  9, 15, 52, 47,  6, 49, 43,
       57, 54, 14, 27,  5, 45, 21, 51]), 'cur_cost': 256486.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([44,  2, 55, 17, 33, 16, 28,  3, 25, 26, 39,  0, 35, 53,  1, 13, 58,
       48, 42, 30, 19, 31, 50, 20, 29, 34, 46,  8, 37, 18,  7, 24, 11, 23,
       38, 56,  4, 10, 41, 32, 36, 40, 22, 12,  9, 15, 52, 47,  6, 49, 43,
       57, 54, 14, 27,  5, 45, 21, 51]), 'cur_cost': 250033.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([44, 33,  2, 55, 17, 16, 28,  3, 25, 26, 39,  0, 35, 53,  1, 13, 58,
       48, 42, 30, 19, 31, 50, 20, 29, 34, 46,  8, 37, 18,  7, 24, 11, 23,
       38, 56,  4, 10, 41, 32, 36, 40, 22, 12,  9, 15, 52, 47,  6, 49, 43,
       57, 54, 14, 27,  5, 45, 21, 51]), 'cur_cost': 244363.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 20, 19, 21, 12, 11, 15, 17, 16, 22, 13, 14, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27129.0, 'intermediate_solutions': [{'tour': [0, 12, 15, 21, 17, 16, 3, 19, 18, 13, 22, 14, 20, 6, 1, 7, 8, 5, 11, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 42336.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 12, 15, 21, 17, 16, 11, 19, 18, 13, 22, 14, 20, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 50, 54, 49, 51, 57, 55, 56, 52, 58, 48, 53, 39, 47], 'cur_cost': 38461.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 15, 21, 17, 16, 11, 19, 18, 22, 14, 20, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 13, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 37334.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 6, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34436.0, 'intermediate_solutions': [{'tour': [0, 23, 12, 16, 17, 15, 11, 21, 22, 13, 18, 19, 14, 20, 2, 1, 7, 8, 5, 3, 6, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 31936.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 23, 12, 16, 17, 15, 11, 21, 22, 13, 18, 19, 14, 20, 6, 1, 7, 8, 5, 51, 57, 55, 56, 52, 58, 48, 53, 39, 40, 38, 41, 45, 42, 36, 43, 37, 44, 46, 35, 33, 34, 27, 31, 24, 29, 32, 25, 30, 26, 28, 10, 4, 9, 2, 3, 49, 54, 50, 47], 'cur_cost': 39424.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 23, 12, 16, 17, 15, 11, 21, 22, 18, 19, 14, 20, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 13, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 37322.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:11,016 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:11,016 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:11,019 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=27129.000, 多样性=0.967
2025-08-05 09:52:11,020 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 09:52:11,020 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 09:52:11,020 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:11,022 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.05835561941667983, 'best_improvement': 0.00040530582166543844}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.06426202321724704}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.02456228827451906, 'recent_improvements': [0.032259105877261406, -0.12548432008402668, 0.08138368242629952], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 13, 'new_count': 13, 'count_change': 0, 'old_best_cost': 24451.0, 'new_best_cost': 24451.0, 'quality_improvement': 0.0, 'old_diversity': 0.8111690569317688, 'new_diversity': 0.8111690569317688, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:52:11,024 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 09:52:11,030 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite11_59_solution.json
2025-08-05 09:52:11,030 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite11_59_20250805_095211.solution
2025-08-05 09:52:11,030 - __main__ - INFO - 实例执行完成 - 运行时间: 1.75s, 最佳成本: 24451.0
2025-08-05 09:52:11,030 - __main__ - INFO - 实例 composite11_59 处理完成
