2025-08-01 17:46:04,267 - main - INFO - composite13_66 开始进化第 1 代
2025-08-01 17:46:04,268 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-01 17:46:04,271 - StatsExpert - INFO - 开始统计分析
2025-08-01 17:46:04,281 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9957.000, 多样性=0.970
2025-08-01 17:46:04,286 - PathExpert - INFO - 开始路径结构分析
2025-08-01 17:46:04,293 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.970
2025-08-01 17:46:04,344 - EliteExpert - INFO - 开始精英解分析
2025-08-01 17:46:04,353 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-01 17:46:04,354 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-01 17:46:04,355 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-01 17:46:04,355 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-01 17:46:04,631 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-01 17:46:04,632 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-01 17:46:04,705 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记
2025-08-01 17:46:05,086 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_1_20250801_174604.html
2025-08-01 17:46:05,138 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_1_20250801_174604.html
2025-08-01 17:46:05,140 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-01 17:46:05,141 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-01 17:46:05,141 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.7879秒
2025-08-01 17:46:05,142 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-01 17:46:05,142 - main - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754041564.631114, 'performance_metrics': {}}}
2025-08-01 17:46:05,143 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-01 17:46:05,143 - StrategyExpert - INFO - 开始策略分配分析
2025-08-01 17:46:05,144 - StrategyExpert - INFO - 消融实验模式：使用纯exploit策略
2025-08-01 17:46:05,144 - StrategyExpert - INFO - 策略分配分析完成（消融实验模式）
2025-08-01 17:46:05,144 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-08-01 17:46:05,145 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 消融实验模式：所有10个个体都使用exploit策略
2025-08-01 17:46:05,146 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-01 17:46:05,146 - main - INFO - 策略分配: ['exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-08-01 17:46:05,147 - main - INFO - 策略分配完整报告: 消融实验模式：所有10个个体都使用exploit策略
2025-08-01 17:46:05,148 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-01 17:46:05,148 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-01 17:46:05,148 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:05,150 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:05,151 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 110301.0
2025-08-01 17:46:07,439 - ExploitationExpert - INFO - res_population_num: 1
2025-08-01 17:46:07,442 - ExploitationExpert - INFO - res_population_costs: [9856.0]
2025-08-01 17:46:07,442 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64)]
2025-08-01 17:46:07,445 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:07,445 - ExploitationExpert - INFO - populations: [{'tour': array([16, 60, 26, 52, 14, 27, 21,  3, 55, 50, 37, 57, 63, 41, 51, 29, 24,
       18, 44, 54, 42,  6, 59, 64, 20, 48, 62, 22, 53, 12, 30,  0, 65, 40,
       58, 23,  4, 19, 10, 25, 28, 36, 56, 39, 43, 11, 45,  2, 31, 47, 33,
       17, 35, 15, 46, 13,  5, 34,  9,  8, 49, 38, 32, 61,  7,  1],
      dtype=int64), 'cur_cost': 110301.0}, {'tour': array([63, 52, 65, 53, 62, 59, 56, 58, 60, 64, 57, 54, 61, 55,  2,  8,  5,
        4,  6,  9, 11,  7,  3,  1,  0, 10, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29,
       32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42],
      dtype=int64), 'cur_cost': 10093.0}, {'tour': array([19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 15, 17, 27, 37, 25, 26, 36,
       35, 28, 30, 34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,
        2,  6, 10,  0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9957.0}, {'tour': array([21, 29, 52, 32, 64, 22,  5, 35, 41,  4, 38, 20, 12, 49, 36, 25,  2,
       46, 24, 13, 31, 54, 15,  6, 59,  0, 45, 58,  7, 62, 63, 48, 51, 14,
       27, 17, 42, 53, 40,  3, 19, 43, 57, 26,  9, 16,  8, 33, 56, 28, 65,
       60, 47, 11, 50, 55, 30, 39, 61,  1, 44, 18, 34, 23, 37, 10],
      dtype=int64), 'cur_cost': 122719.0}, {'tour': array([56, 21, 16, 18,  4, 51,  3,  1, 32, 61,  9, 44,  0, 30, 10, 63, 17,
       53, 46, 31, 13, 50, 11, 39, 12, 54, 42, 35, 58, 65, 38, 45, 23, 52,
       37, 27, 59,  8, 20, 19, 26, 36,  7, 60, 47,  6, 64, 48, 55, 49, 29,
       25, 15,  5, 34, 24, 43,  2, 22, 40, 28, 41, 57, 62, 14, 33],
      dtype=int64), 'cur_cost': 112723.0}, {'tour': array([34, 17, 62, 36, 10, 56, 40, 58, 64, 51, 15, 12, 48, 65, 28, 50, 19,
        3, 42, 29, 59, 25, 20, 30, 52, 45, 33, 54, 22, 14, 47, 24, 26, 11,
       55,  1, 21, 16, 37,  4, 61,  2,  7, 27, 41, 53,  5, 31, 44, 35, 23,
        0, 49, 18,  8, 13, 32, 38, 43, 57, 46,  6, 39,  9, 63, 60],
      dtype=int64), 'cur_cost': 114942.0}, {'tour': array([ 7, 54, 18, 11,  3,  6, 40,  1, 28, 36, 44, 38, 51, 56, 53, 60, 55,
       20,  0, 47, 63, 57,  5, 13, 24,  4, 16, 34, 43, 46, 17, 52, 15, 58,
       42,  2, 50, 59, 12, 29, 32, 65, 14, 37, 23, 26, 41, 31,  8, 21, 35,
       61, 27, 62, 30, 39, 22, 33, 25, 45, 19, 48, 49,  9, 10, 64],
      dtype=int64), 'cur_cost': 99684.0}, {'tour': array([34, 56, 45,  7, 16, 62,  9, 11, 18, 44, 19, 40, 32, 48, 30, 59, 39,
       36,  8, 29, 46,  0, 54, 50, 21,  1, 51, 42, 41, 55, 25, 14, 23, 28,
       63, 20, 26, 27, 60, 13, 37,  3, 52, 15, 31, 49, 58,  4, 65, 61, 57,
       24, 53, 17, 43, 38, 22,  6, 47, 33,  5, 64, 12, 10,  2, 35],
      dtype=int64), 'cur_cost': 108695.0}, {'tour': array([43, 65, 52, 48, 14,  0, 56,  6, 32, 21, 30, 25, 46,  5, 59, 57, 23,
       24, 22, 11, 26, 47, 55, 27, 53, 60,  4, 13, 54, 34, 42, 35, 64,  1,
       20, 31, 38, 16, 51, 36, 62, 10, 61, 39,  3,  2, 45, 50, 58, 37, 33,
       19,  7, 12, 63, 44, 28, 15,  9, 18, 29, 17,  8, 41, 49, 40],
      dtype=int64), 'cur_cost': 103144.0}, {'tour': array([ 1, 49, 19, 30, 26, 57, 11, 56, 31, 33, 39, 23, 18, 50, 20, 12, 24,
       40,  5, 60, 21, 54, 44, 53, 43,  3,  7, 28,  6, 62, 37, 10,  4, 17,
       64,  8, 55, 35, 65, 38, 51, 46,  9, 15, 58, 22,  0, 45, 34, 14, 61,
       48, 27, 13,  2, 36, 29, 32, 42, 25, 41, 16, 63, 52, 59, 47],
      dtype=int64), 'cur_cost': 106402.0}]
2025-08-01 17:46:07,454 - ExploitationExpert - INFO - 局部搜索耗时: 2.30秒
2025-08-01 17:46:07,455 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-01 17:46:07,455 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([16, 60, 26, 52, 14, 27, 21,  3, 55, 50, 37, 57, 63, 41, 51, 29, 24,
       18, 44, 54, 42,  6, 59, 64, 20, 48, 62, 22, 53, 12, 30,  0, 65, 40,
       58, 23,  4, 19, 10, 25, 28, 36, 56, 39, 43, 11, 45,  2, 31, 47, 33,
       17, 35, 15, 46, 13,  5, 34,  9,  8, 49, 38, 32, 61,  7,  1],
      dtype=int64), 'cur_cost': 110301.0}
2025-08-01 17:46:07,456 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-01 17:46:07,456 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:07,457 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:07,457 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 106689.0
2025-08-01 17:46:09,984 - ExploitationExpert - INFO - res_population_num: 2
2025-08-01 17:46:09,984 - ExploitationExpert - INFO - res_population_costs: [9856.0, 9529.0]
2025-08-01 17:46:09,984 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-08-01 17:46:09,986 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:09,986 - ExploitationExpert - INFO - populations: [{'tour': array([16, 60, 26, 52, 14, 27, 21,  3, 55, 50, 37, 57, 63, 41, 51, 29, 24,
       18, 44, 54, 42,  6, 59, 64, 20, 48, 62, 22, 53, 12, 30,  0, 65, 40,
       58, 23,  4, 19, 10, 25, 28, 36, 56, 39, 43, 11, 45,  2, 31, 47, 33,
       17, 35, 15, 46, 13,  5, 34,  9,  8, 49, 38, 32, 61,  7,  1],
      dtype=int64), 'cur_cost': 110301.0}, {'tour': array([59, 56, 25, 16,  8, 28, 27, 34, 17, 42,  5, 19, 11, 65,  9,  6, 55,
       39, 45, 63,  0, 13, 36, 57, 18, 51, 26, 64, 20, 52, 31, 43, 54, 44,
       21, 12, 48, 10, 22,  7, 53, 41,  3, 60, 24,  1, 61, 32, 14, 33, 37,
       38, 50, 49, 58, 40, 30, 46, 62,  4, 47,  2, 35, 29, 15, 23],
      dtype=int64), 'cur_cost': 106689.0}, {'tour': array([19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 15, 17, 27, 37, 25, 26, 36,
       35, 28, 30, 34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,
        2,  6, 10,  0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9957.0}, {'tour': array([21, 29, 52, 32, 64, 22,  5, 35, 41,  4, 38, 20, 12, 49, 36, 25,  2,
       46, 24, 13, 31, 54, 15,  6, 59,  0, 45, 58,  7, 62, 63, 48, 51, 14,
       27, 17, 42, 53, 40,  3, 19, 43, 57, 26,  9, 16,  8, 33, 56, 28, 65,
       60, 47, 11, 50, 55, 30, 39, 61,  1, 44, 18, 34, 23, 37, 10],
      dtype=int64), 'cur_cost': 122719.0}, {'tour': array([56, 21, 16, 18,  4, 51,  3,  1, 32, 61,  9, 44,  0, 30, 10, 63, 17,
       53, 46, 31, 13, 50, 11, 39, 12, 54, 42, 35, 58, 65, 38, 45, 23, 52,
       37, 27, 59,  8, 20, 19, 26, 36,  7, 60, 47,  6, 64, 48, 55, 49, 29,
       25, 15,  5, 34, 24, 43,  2, 22, 40, 28, 41, 57, 62, 14, 33],
      dtype=int64), 'cur_cost': 112723.0}, {'tour': array([34, 17, 62, 36, 10, 56, 40, 58, 64, 51, 15, 12, 48, 65, 28, 50, 19,
        3, 42, 29, 59, 25, 20, 30, 52, 45, 33, 54, 22, 14, 47, 24, 26, 11,
       55,  1, 21, 16, 37,  4, 61,  2,  7, 27, 41, 53,  5, 31, 44, 35, 23,
        0, 49, 18,  8, 13, 32, 38, 43, 57, 46,  6, 39,  9, 63, 60],
      dtype=int64), 'cur_cost': 114942.0}, {'tour': array([ 7, 54, 18, 11,  3,  6, 40,  1, 28, 36, 44, 38, 51, 56, 53, 60, 55,
       20,  0, 47, 63, 57,  5, 13, 24,  4, 16, 34, 43, 46, 17, 52, 15, 58,
       42,  2, 50, 59, 12, 29, 32, 65, 14, 37, 23, 26, 41, 31,  8, 21, 35,
       61, 27, 62, 30, 39, 22, 33, 25, 45, 19, 48, 49,  9, 10, 64],
      dtype=int64), 'cur_cost': 99684.0}, {'tour': array([34, 56, 45,  7, 16, 62,  9, 11, 18, 44, 19, 40, 32, 48, 30, 59, 39,
       36,  8, 29, 46,  0, 54, 50, 21,  1, 51, 42, 41, 55, 25, 14, 23, 28,
       63, 20, 26, 27, 60, 13, 37,  3, 52, 15, 31, 49, 58,  4, 65, 61, 57,
       24, 53, 17, 43, 38, 22,  6, 47, 33,  5, 64, 12, 10,  2, 35],
      dtype=int64), 'cur_cost': 108695.0}, {'tour': array([43, 65, 52, 48, 14,  0, 56,  6, 32, 21, 30, 25, 46,  5, 59, 57, 23,
       24, 22, 11, 26, 47, 55, 27, 53, 60,  4, 13, 54, 34, 42, 35, 64,  1,
       20, 31, 38, 16, 51, 36, 62, 10, 61, 39,  3,  2, 45, 50, 58, 37, 33,
       19,  7, 12, 63, 44, 28, 15,  9, 18, 29, 17,  8, 41, 49, 40],
      dtype=int64), 'cur_cost': 103144.0}, {'tour': array([ 1, 49, 19, 30, 26, 57, 11, 56, 31, 33, 39, 23, 18, 50, 20, 12, 24,
       40,  5, 60, 21, 54, 44, 53, 43,  3,  7, 28,  6, 62, 37, 10,  4, 17,
       64,  8, 55, 35, 65, 38, 51, 46,  9, 15, 58, 22,  0, 45, 34, 14, 61,
       48, 27, 13,  2, 36, 29, 32, 42, 25, 41, 16, 63, 52, 59, 47],
      dtype=int64), 'cur_cost': 106402.0}]
2025-08-01 17:46:09,995 - ExploitationExpert - INFO - 局部搜索耗时: 2.54秒
2025-08-01 17:46:09,996 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-01 17:46:09,997 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([59, 56, 25, 16,  8, 28, 27, 34, 17, 42,  5, 19, 11, 65,  9,  6, 55,
       39, 45, 63,  0, 13, 36, 57, 18, 51, 26, 64, 20, 52, 31, 43, 54, 44,
       21, 12, 48, 10, 22,  7, 53, 41,  3, 60, 24,  1, 61, 32, 14, 33, 37,
       38, 50, 49, 58, 40, 30, 46, 62,  4, 47,  2, 35, 29, 15, 23],
      dtype=int64), 'cur_cost': 106689.0}
2025-08-01 17:46:09,998 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-01 17:46:09,999 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:09,999 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:09,999 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 102927.0
2025-08-01 17:46:10,627 - ExploitationExpert - INFO - res_population_num: 2
2025-08-01 17:46:10,628 - ExploitationExpert - INFO - res_population_costs: [9856.0, 9529.0]
2025-08-01 17:46:10,628 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-08-01 17:46:10,631 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:10,631 - ExploitationExpert - INFO - populations: [{'tour': array([16, 60, 26, 52, 14, 27, 21,  3, 55, 50, 37, 57, 63, 41, 51, 29, 24,
       18, 44, 54, 42,  6, 59, 64, 20, 48, 62, 22, 53, 12, 30,  0, 65, 40,
       58, 23,  4, 19, 10, 25, 28, 36, 56, 39, 43, 11, 45,  2, 31, 47, 33,
       17, 35, 15, 46, 13,  5, 34,  9,  8, 49, 38, 32, 61,  7,  1],
      dtype=int64), 'cur_cost': 110301.0}, {'tour': array([59, 56, 25, 16,  8, 28, 27, 34, 17, 42,  5, 19, 11, 65,  9,  6, 55,
       39, 45, 63,  0, 13, 36, 57, 18, 51, 26, 64, 20, 52, 31, 43, 54, 44,
       21, 12, 48, 10, 22,  7, 53, 41,  3, 60, 24,  1, 61, 32, 14, 33, 37,
       38, 50, 49, 58, 40, 30, 46, 62,  4, 47,  2, 35, 29, 15, 23],
      dtype=int64), 'cur_cost': 106689.0}, {'tour': array([62,  1, 35, 51, 65, 20, 29,  6,  2, 41, 50, 53, 34, 13, 16, 42, 39,
       48, 47, 36, 22, 32, 58,  4, 52, 21, 55, 19, 54,  7, 38, 26,  5, 43,
       56, 64, 25, 17, 31, 23, 15, 28, 44, 40, 46,  0, 10, 18, 14, 57, 27,
       33, 61,  3,  9, 30, 63, 45, 37, 59, 49, 60,  8, 12, 24, 11],
      dtype=int64), 'cur_cost': 102927.0}, {'tour': array([21, 29, 52, 32, 64, 22,  5, 35, 41,  4, 38, 20, 12, 49, 36, 25,  2,
       46, 24, 13, 31, 54, 15,  6, 59,  0, 45, 58,  7, 62, 63, 48, 51, 14,
       27, 17, 42, 53, 40,  3, 19, 43, 57, 26,  9, 16,  8, 33, 56, 28, 65,
       60, 47, 11, 50, 55, 30, 39, 61,  1, 44, 18, 34, 23, 37, 10],
      dtype=int64), 'cur_cost': 122719.0}, {'tour': array([56, 21, 16, 18,  4, 51,  3,  1, 32, 61,  9, 44,  0, 30, 10, 63, 17,
       53, 46, 31, 13, 50, 11, 39, 12, 54, 42, 35, 58, 65, 38, 45, 23, 52,
       37, 27, 59,  8, 20, 19, 26, 36,  7, 60, 47,  6, 64, 48, 55, 49, 29,
       25, 15,  5, 34, 24, 43,  2, 22, 40, 28, 41, 57, 62, 14, 33],
      dtype=int64), 'cur_cost': 112723.0}, {'tour': array([34, 17, 62, 36, 10, 56, 40, 58, 64, 51, 15, 12, 48, 65, 28, 50, 19,
        3, 42, 29, 59, 25, 20, 30, 52, 45, 33, 54, 22, 14, 47, 24, 26, 11,
       55,  1, 21, 16, 37,  4, 61,  2,  7, 27, 41, 53,  5, 31, 44, 35, 23,
        0, 49, 18,  8, 13, 32, 38, 43, 57, 46,  6, 39,  9, 63, 60],
      dtype=int64), 'cur_cost': 114942.0}, {'tour': array([ 7, 54, 18, 11,  3,  6, 40,  1, 28, 36, 44, 38, 51, 56, 53, 60, 55,
       20,  0, 47, 63, 57,  5, 13, 24,  4, 16, 34, 43, 46, 17, 52, 15, 58,
       42,  2, 50, 59, 12, 29, 32, 65, 14, 37, 23, 26, 41, 31,  8, 21, 35,
       61, 27, 62, 30, 39, 22, 33, 25, 45, 19, 48, 49,  9, 10, 64],
      dtype=int64), 'cur_cost': 99684.0}, {'tour': array([34, 56, 45,  7, 16, 62,  9, 11, 18, 44, 19, 40, 32, 48, 30, 59, 39,
       36,  8, 29, 46,  0, 54, 50, 21,  1, 51, 42, 41, 55, 25, 14, 23, 28,
       63, 20, 26, 27, 60, 13, 37,  3, 52, 15, 31, 49, 58,  4, 65, 61, 57,
       24, 53, 17, 43, 38, 22,  6, 47, 33,  5, 64, 12, 10,  2, 35],
      dtype=int64), 'cur_cost': 108695.0}, {'tour': array([43, 65, 52, 48, 14,  0, 56,  6, 32, 21, 30, 25, 46,  5, 59, 57, 23,
       24, 22, 11, 26, 47, 55, 27, 53, 60,  4, 13, 54, 34, 42, 35, 64,  1,
       20, 31, 38, 16, 51, 36, 62, 10, 61, 39,  3,  2, 45, 50, 58, 37, 33,
       19,  7, 12, 63, 44, 28, 15,  9, 18, 29, 17,  8, 41, 49, 40],
      dtype=int64), 'cur_cost': 103144.0}, {'tour': array([ 1, 49, 19, 30, 26, 57, 11, 56, 31, 33, 39, 23, 18, 50, 20, 12, 24,
       40,  5, 60, 21, 54, 44, 53, 43,  3,  7, 28,  6, 62, 37, 10,  4, 17,
       64,  8, 55, 35, 65, 38, 51, 46,  9, 15, 58, 22,  0, 45, 34, 14, 61,
       48, 27, 13,  2, 36, 29, 32, 42, 25, 41, 16, 63, 52, 59, 47],
      dtype=int64), 'cur_cost': 106402.0}]
2025-08-01 17:46:10,643 - ExploitationExpert - INFO - 局部搜索耗时: 0.64秒
2025-08-01 17:46:10,644 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-01 17:46:10,645 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([62,  1, 35, 51, 65, 20, 29,  6,  2, 41, 50, 53, 34, 13, 16, 42, 39,
       48, 47, 36, 22, 32, 58,  4, 52, 21, 55, 19, 54,  7, 38, 26,  5, 43,
       56, 64, 25, 17, 31, 23, 15, 28, 44, 40, 46,  0, 10, 18, 14, 57, 27,
       33, 61,  3,  9, 30, 63, 45, 37, 59, 49, 60,  8, 12, 24, 11],
      dtype=int64), 'cur_cost': 102927.0}
2025-08-01 17:46:10,646 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-01 17:46:10,647 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:10,648 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:10,648 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 99089.0
2025-08-01 17:46:10,724 - ExploitationExpert - INFO - res_population_num: 4
2025-08-01 17:46:10,725 - ExploitationExpert - INFO - res_population_costs: [9856.0, 9529.0, 9521, 9521]
2025-08-01 17:46:10,725 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 17:46:10,729 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:10,729 - ExploitationExpert - INFO - populations: [{'tour': array([16, 60, 26, 52, 14, 27, 21,  3, 55, 50, 37, 57, 63, 41, 51, 29, 24,
       18, 44, 54, 42,  6, 59, 64, 20, 48, 62, 22, 53, 12, 30,  0, 65, 40,
       58, 23,  4, 19, 10, 25, 28, 36, 56, 39, 43, 11, 45,  2, 31, 47, 33,
       17, 35, 15, 46, 13,  5, 34,  9,  8, 49, 38, 32, 61,  7,  1],
      dtype=int64), 'cur_cost': 110301.0}, {'tour': array([59, 56, 25, 16,  8, 28, 27, 34, 17, 42,  5, 19, 11, 65,  9,  6, 55,
       39, 45, 63,  0, 13, 36, 57, 18, 51, 26, 64, 20, 52, 31, 43, 54, 44,
       21, 12, 48, 10, 22,  7, 53, 41,  3, 60, 24,  1, 61, 32, 14, 33, 37,
       38, 50, 49, 58, 40, 30, 46, 62,  4, 47,  2, 35, 29, 15, 23],
      dtype=int64), 'cur_cost': 106689.0}, {'tour': array([62,  1, 35, 51, 65, 20, 29,  6,  2, 41, 50, 53, 34, 13, 16, 42, 39,
       48, 47, 36, 22, 32, 58,  4, 52, 21, 55, 19, 54,  7, 38, 26,  5, 43,
       56, 64, 25, 17, 31, 23, 15, 28, 44, 40, 46,  0, 10, 18, 14, 57, 27,
       33, 61,  3,  9, 30, 63, 45, 37, 59, 49, 60,  8, 12, 24, 11],
      dtype=int64), 'cur_cost': 102927.0}, {'tour': array([10, 44, 41,  1,  5,  7, 48, 33,  8, 64, 42, 32, 65,  3, 23, 52, 61,
       34, 53,  0, 11, 29, 15, 19, 57, 14, 31, 60, 46, 22, 13, 27, 16, 59,
       30,  6, 35, 39, 40,  9, 49, 25, 38, 18,  4, 12, 26, 36, 24, 37,  2,
       21, 51, 20, 62, 43, 17, 63, 58, 55, 50, 45, 47, 28, 54, 56],
      dtype=int64), 'cur_cost': 99089.0}, {'tour': array([56, 21, 16, 18,  4, 51,  3,  1, 32, 61,  9, 44,  0, 30, 10, 63, 17,
       53, 46, 31, 13, 50, 11, 39, 12, 54, 42, 35, 58, 65, 38, 45, 23, 52,
       37, 27, 59,  8, 20, 19, 26, 36,  7, 60, 47,  6, 64, 48, 55, 49, 29,
       25, 15,  5, 34, 24, 43,  2, 22, 40, 28, 41, 57, 62, 14, 33],
      dtype=int64), 'cur_cost': 112723.0}, {'tour': array([34, 17, 62, 36, 10, 56, 40, 58, 64, 51, 15, 12, 48, 65, 28, 50, 19,
        3, 42, 29, 59, 25, 20, 30, 52, 45, 33, 54, 22, 14, 47, 24, 26, 11,
       55,  1, 21, 16, 37,  4, 61,  2,  7, 27, 41, 53,  5, 31, 44, 35, 23,
        0, 49, 18,  8, 13, 32, 38, 43, 57, 46,  6, 39,  9, 63, 60],
      dtype=int64), 'cur_cost': 114942.0}, {'tour': array([ 7, 54, 18, 11,  3,  6, 40,  1, 28, 36, 44, 38, 51, 56, 53, 60, 55,
       20,  0, 47, 63, 57,  5, 13, 24,  4, 16, 34, 43, 46, 17, 52, 15, 58,
       42,  2, 50, 59, 12, 29, 32, 65, 14, 37, 23, 26, 41, 31,  8, 21, 35,
       61, 27, 62, 30, 39, 22, 33, 25, 45, 19, 48, 49,  9, 10, 64],
      dtype=int64), 'cur_cost': 99684.0}, {'tour': array([34, 56, 45,  7, 16, 62,  9, 11, 18, 44, 19, 40, 32, 48, 30, 59, 39,
       36,  8, 29, 46,  0, 54, 50, 21,  1, 51, 42, 41, 55, 25, 14, 23, 28,
       63, 20, 26, 27, 60, 13, 37,  3, 52, 15, 31, 49, 58,  4, 65, 61, 57,
       24, 53, 17, 43, 38, 22,  6, 47, 33,  5, 64, 12, 10,  2, 35],
      dtype=int64), 'cur_cost': 108695.0}, {'tour': array([43, 65, 52, 48, 14,  0, 56,  6, 32, 21, 30, 25, 46,  5, 59, 57, 23,
       24, 22, 11, 26, 47, 55, 27, 53, 60,  4, 13, 54, 34, 42, 35, 64,  1,
       20, 31, 38, 16, 51, 36, 62, 10, 61, 39,  3,  2, 45, 50, 58, 37, 33,
       19,  7, 12, 63, 44, 28, 15,  9, 18, 29, 17,  8, 41, 49, 40],
      dtype=int64), 'cur_cost': 103144.0}, {'tour': array([ 1, 49, 19, 30, 26, 57, 11, 56, 31, 33, 39, 23, 18, 50, 20, 12, 24,
       40,  5, 60, 21, 54, 44, 53, 43,  3,  7, 28,  6, 62, 37, 10,  4, 17,
       64,  8, 55, 35, 65, 38, 51, 46,  9, 15, 58, 22,  0, 45, 34, 14, 61,
       48, 27, 13,  2, 36, 29, 32, 42, 25, 41, 16, 63, 52, 59, 47],
      dtype=int64), 'cur_cost': 106402.0}]
2025-08-01 17:46:10,741 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-01 17:46:10,742 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-01 17:46:10,743 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([10, 44, 41,  1,  5,  7, 48, 33,  8, 64, 42, 32, 65,  3, 23, 52, 61,
       34, 53,  0, 11, 29, 15, 19, 57, 14, 31, 60, 46, 22, 13, 27, 16, 59,
       30,  6, 35, 39, 40,  9, 49, 25, 38, 18,  4, 12, 26, 36, 24, 37,  2,
       21, 51, 20, 62, 43, 17, 63, 58, 55, 50, 45, 47, 28, 54, 56],
      dtype=int64), 'cur_cost': 99089.0}
2025-08-01 17:46:10,744 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-01 17:46:10,744 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:10,744 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:10,745 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 105593.0
2025-08-01 17:46:10,822 - ExploitationExpert - INFO - res_population_num: 4
2025-08-01 17:46:10,822 - ExploitationExpert - INFO - res_population_costs: [9856.0, 9529.0, 9521, 9521]
2025-08-01 17:46:10,822 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 17:46:10,825 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:10,826 - ExploitationExpert - INFO - populations: [{'tour': array([16, 60, 26, 52, 14, 27, 21,  3, 55, 50, 37, 57, 63, 41, 51, 29, 24,
       18, 44, 54, 42,  6, 59, 64, 20, 48, 62, 22, 53, 12, 30,  0, 65, 40,
       58, 23,  4, 19, 10, 25, 28, 36, 56, 39, 43, 11, 45,  2, 31, 47, 33,
       17, 35, 15, 46, 13,  5, 34,  9,  8, 49, 38, 32, 61,  7,  1],
      dtype=int64), 'cur_cost': 110301.0}, {'tour': array([59, 56, 25, 16,  8, 28, 27, 34, 17, 42,  5, 19, 11, 65,  9,  6, 55,
       39, 45, 63,  0, 13, 36, 57, 18, 51, 26, 64, 20, 52, 31, 43, 54, 44,
       21, 12, 48, 10, 22,  7, 53, 41,  3, 60, 24,  1, 61, 32, 14, 33, 37,
       38, 50, 49, 58, 40, 30, 46, 62,  4, 47,  2, 35, 29, 15, 23],
      dtype=int64), 'cur_cost': 106689.0}, {'tour': array([62,  1, 35, 51, 65, 20, 29,  6,  2, 41, 50, 53, 34, 13, 16, 42, 39,
       48, 47, 36, 22, 32, 58,  4, 52, 21, 55, 19, 54,  7, 38, 26,  5, 43,
       56, 64, 25, 17, 31, 23, 15, 28, 44, 40, 46,  0, 10, 18, 14, 57, 27,
       33, 61,  3,  9, 30, 63, 45, 37, 59, 49, 60,  8, 12, 24, 11],
      dtype=int64), 'cur_cost': 102927.0}, {'tour': array([10, 44, 41,  1,  5,  7, 48, 33,  8, 64, 42, 32, 65,  3, 23, 52, 61,
       34, 53,  0, 11, 29, 15, 19, 57, 14, 31, 60, 46, 22, 13, 27, 16, 59,
       30,  6, 35, 39, 40,  9, 49, 25, 38, 18,  4, 12, 26, 36, 24, 37,  2,
       21, 51, 20, 62, 43, 17, 63, 58, 55, 50, 45, 47, 28, 54, 56],
      dtype=int64), 'cur_cost': 99089.0}, {'tour': array([64,  6, 33, 46, 11, 15, 13, 21,  4, 57, 17, 42, 22, 23, 35,  3, 28,
       14,  2, 34, 43, 47, 31,  5, 58, 24, 29, 38, 49,  1, 50, 63,  7, 59,
       56, 55, 61, 62, 44, 60, 48, 52, 39, 16, 65, 41, 10, 36,  8, 40,  0,
       51, 25, 12, 20, 32, 37, 19, 53, 30, 26, 18, 54, 27, 45,  9],
      dtype=int64), 'cur_cost': 105593.0}, {'tour': array([34, 17, 62, 36, 10, 56, 40, 58, 64, 51, 15, 12, 48, 65, 28, 50, 19,
        3, 42, 29, 59, 25, 20, 30, 52, 45, 33, 54, 22, 14, 47, 24, 26, 11,
       55,  1, 21, 16, 37,  4, 61,  2,  7, 27, 41, 53,  5, 31, 44, 35, 23,
        0, 49, 18,  8, 13, 32, 38, 43, 57, 46,  6, 39,  9, 63, 60],
      dtype=int64), 'cur_cost': 114942.0}, {'tour': array([ 7, 54, 18, 11,  3,  6, 40,  1, 28, 36, 44, 38, 51, 56, 53, 60, 55,
       20,  0, 47, 63, 57,  5, 13, 24,  4, 16, 34, 43, 46, 17, 52, 15, 58,
       42,  2, 50, 59, 12, 29, 32, 65, 14, 37, 23, 26, 41, 31,  8, 21, 35,
       61, 27, 62, 30, 39, 22, 33, 25, 45, 19, 48, 49,  9, 10, 64],
      dtype=int64), 'cur_cost': 99684.0}, {'tour': array([34, 56, 45,  7, 16, 62,  9, 11, 18, 44, 19, 40, 32, 48, 30, 59, 39,
       36,  8, 29, 46,  0, 54, 50, 21,  1, 51, 42, 41, 55, 25, 14, 23, 28,
       63, 20, 26, 27, 60, 13, 37,  3, 52, 15, 31, 49, 58,  4, 65, 61, 57,
       24, 53, 17, 43, 38, 22,  6, 47, 33,  5, 64, 12, 10,  2, 35],
      dtype=int64), 'cur_cost': 108695.0}, {'tour': array([43, 65, 52, 48, 14,  0, 56,  6, 32, 21, 30, 25, 46,  5, 59, 57, 23,
       24, 22, 11, 26, 47, 55, 27, 53, 60,  4, 13, 54, 34, 42, 35, 64,  1,
       20, 31, 38, 16, 51, 36, 62, 10, 61, 39,  3,  2, 45, 50, 58, 37, 33,
       19,  7, 12, 63, 44, 28, 15,  9, 18, 29, 17,  8, 41, 49, 40],
      dtype=int64), 'cur_cost': 103144.0}, {'tour': array([ 1, 49, 19, 30, 26, 57, 11, 56, 31, 33, 39, 23, 18, 50, 20, 12, 24,
       40,  5, 60, 21, 54, 44, 53, 43,  3,  7, 28,  6, 62, 37, 10,  4, 17,
       64,  8, 55, 35, 65, 38, 51, 46,  9, 15, 58, 22,  0, 45, 34, 14, 61,
       48, 27, 13,  2, 36, 29, 32, 42, 25, 41, 16, 63, 52, 59, 47],
      dtype=int64), 'cur_cost': 106402.0}]
2025-08-01 17:46:10,835 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-01 17:46:10,836 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-01 17:46:10,837 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([64,  6, 33, 46, 11, 15, 13, 21,  4, 57, 17, 42, 22, 23, 35,  3, 28,
       14,  2, 34, 43, 47, 31,  5, 58, 24, 29, 38, 49,  1, 50, 63,  7, 59,
       56, 55, 61, 62, 44, 60, 48, 52, 39, 16, 65, 41, 10, 36,  8, 40,  0,
       51, 25, 12, 20, 32, 37, 19, 53, 30, 26, 18, 54, 27, 45,  9],
      dtype=int64), 'cur_cost': 105593.0}
2025-08-01 17:46:10,840 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-01 17:46:10,843 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:10,843 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:10,844 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 87456.0
2025-08-01 17:46:10,919 - ExploitationExpert - INFO - res_population_num: 4
2025-08-01 17:46:10,919 - ExploitationExpert - INFO - res_population_costs: [9856.0, 9529.0, 9521, 9521]
2025-08-01 17:46:10,919 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 17:46:10,923 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:10,923 - ExploitationExpert - INFO - populations: [{'tour': array([16, 60, 26, 52, 14, 27, 21,  3, 55, 50, 37, 57, 63, 41, 51, 29, 24,
       18, 44, 54, 42,  6, 59, 64, 20, 48, 62, 22, 53, 12, 30,  0, 65, 40,
       58, 23,  4, 19, 10, 25, 28, 36, 56, 39, 43, 11, 45,  2, 31, 47, 33,
       17, 35, 15, 46, 13,  5, 34,  9,  8, 49, 38, 32, 61,  7,  1],
      dtype=int64), 'cur_cost': 110301.0}, {'tour': array([59, 56, 25, 16,  8, 28, 27, 34, 17, 42,  5, 19, 11, 65,  9,  6, 55,
       39, 45, 63,  0, 13, 36, 57, 18, 51, 26, 64, 20, 52, 31, 43, 54, 44,
       21, 12, 48, 10, 22,  7, 53, 41,  3, 60, 24,  1, 61, 32, 14, 33, 37,
       38, 50, 49, 58, 40, 30, 46, 62,  4, 47,  2, 35, 29, 15, 23],
      dtype=int64), 'cur_cost': 106689.0}, {'tour': array([62,  1, 35, 51, 65, 20, 29,  6,  2, 41, 50, 53, 34, 13, 16, 42, 39,
       48, 47, 36, 22, 32, 58,  4, 52, 21, 55, 19, 54,  7, 38, 26,  5, 43,
       56, 64, 25, 17, 31, 23, 15, 28, 44, 40, 46,  0, 10, 18, 14, 57, 27,
       33, 61,  3,  9, 30, 63, 45, 37, 59, 49, 60,  8, 12, 24, 11],
      dtype=int64), 'cur_cost': 102927.0}, {'tour': array([10, 44, 41,  1,  5,  7, 48, 33,  8, 64, 42, 32, 65,  3, 23, 52, 61,
       34, 53,  0, 11, 29, 15, 19, 57, 14, 31, 60, 46, 22, 13, 27, 16, 59,
       30,  6, 35, 39, 40,  9, 49, 25, 38, 18,  4, 12, 26, 36, 24, 37,  2,
       21, 51, 20, 62, 43, 17, 63, 58, 55, 50, 45, 47, 28, 54, 56],
      dtype=int64), 'cur_cost': 99089.0}, {'tour': array([64,  6, 33, 46, 11, 15, 13, 21,  4, 57, 17, 42, 22, 23, 35,  3, 28,
       14,  2, 34, 43, 47, 31,  5, 58, 24, 29, 38, 49,  1, 50, 63,  7, 59,
       56, 55, 61, 62, 44, 60, 48, 52, 39, 16, 65, 41, 10, 36,  8, 40,  0,
       51, 25, 12, 20, 32, 37, 19, 53, 30, 26, 18, 54, 27, 45,  9],
      dtype=int64), 'cur_cost': 105593.0}, {'tour': array([27, 59, 58, 10, 55, 63, 54,  5, 46, 16, 57, 20,  2, 24, 23, 12, 42,
       40, 30, 56, 18, 51, 21, 36, 17, 33, 37, 26, 45, 39,  9, 48, 38, 49,
        3, 62,  6, 35, 25,  1, 32, 53,  4, 64, 41, 15,  8, 65,  0, 28, 29,
       50, 47, 13,  7, 11, 14, 60, 52, 43, 19, 34, 31, 22, 61, 44],
      dtype=int64), 'cur_cost': 87456.0}, {'tour': array([ 7, 54, 18, 11,  3,  6, 40,  1, 28, 36, 44, 38, 51, 56, 53, 60, 55,
       20,  0, 47, 63, 57,  5, 13, 24,  4, 16, 34, 43, 46, 17, 52, 15, 58,
       42,  2, 50, 59, 12, 29, 32, 65, 14, 37, 23, 26, 41, 31,  8, 21, 35,
       61, 27, 62, 30, 39, 22, 33, 25, 45, 19, 48, 49,  9, 10, 64],
      dtype=int64), 'cur_cost': 99684.0}, {'tour': array([34, 56, 45,  7, 16, 62,  9, 11, 18, 44, 19, 40, 32, 48, 30, 59, 39,
       36,  8, 29, 46,  0, 54, 50, 21,  1, 51, 42, 41, 55, 25, 14, 23, 28,
       63, 20, 26, 27, 60, 13, 37,  3, 52, 15, 31, 49, 58,  4, 65, 61, 57,
       24, 53, 17, 43, 38, 22,  6, 47, 33,  5, 64, 12, 10,  2, 35],
      dtype=int64), 'cur_cost': 108695.0}, {'tour': array([43, 65, 52, 48, 14,  0, 56,  6, 32, 21, 30, 25, 46,  5, 59, 57, 23,
       24, 22, 11, 26, 47, 55, 27, 53, 60,  4, 13, 54, 34, 42, 35, 64,  1,
       20, 31, 38, 16, 51, 36, 62, 10, 61, 39,  3,  2, 45, 50, 58, 37, 33,
       19,  7, 12, 63, 44, 28, 15,  9, 18, 29, 17,  8, 41, 49, 40],
      dtype=int64), 'cur_cost': 103144.0}, {'tour': array([ 1, 49, 19, 30, 26, 57, 11, 56, 31, 33, 39, 23, 18, 50, 20, 12, 24,
       40,  5, 60, 21, 54, 44, 53, 43,  3,  7, 28,  6, 62, 37, 10,  4, 17,
       64,  8, 55, 35, 65, 38, 51, 46,  9, 15, 58, 22,  0, 45, 34, 14, 61,
       48, 27, 13,  2, 36, 29, 32, 42, 25, 41, 16, 63, 52, 59, 47],
      dtype=int64), 'cur_cost': 106402.0}]
2025-08-01 17:46:10,931 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-01 17:46:10,932 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-01 17:46:10,933 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([27, 59, 58, 10, 55, 63, 54,  5, 46, 16, 57, 20,  2, 24, 23, 12, 42,
       40, 30, 56, 18, 51, 21, 36, 17, 33, 37, 26, 45, 39,  9, 48, 38, 49,
        3, 62,  6, 35, 25,  1, 32, 53,  4, 64, 41, 15,  8, 65,  0, 28, 29,
       50, 47, 13,  7, 11, 14, 60, 52, 43, 19, 34, 31, 22, 61, 44],
      dtype=int64), 'cur_cost': 87456.0}
2025-08-01 17:46:10,934 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-01 17:46:10,935 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:10,935 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:10,936 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 98224.0
2025-08-01 17:46:11,018 - ExploitationExpert - INFO - res_population_num: 7
2025-08-01 17:46:11,018 - ExploitationExpert - INFO - res_population_costs: [9856.0, 9529.0, 9521, 9521, 9521, 9521, 9521]
2025-08-01 17:46:11,018 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 17:46:11,023 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:11,024 - ExploitationExpert - INFO - populations: [{'tour': array([16, 60, 26, 52, 14, 27, 21,  3, 55, 50, 37, 57, 63, 41, 51, 29, 24,
       18, 44, 54, 42,  6, 59, 64, 20, 48, 62, 22, 53, 12, 30,  0, 65, 40,
       58, 23,  4, 19, 10, 25, 28, 36, 56, 39, 43, 11, 45,  2, 31, 47, 33,
       17, 35, 15, 46, 13,  5, 34,  9,  8, 49, 38, 32, 61,  7,  1],
      dtype=int64), 'cur_cost': 110301.0}, {'tour': array([59, 56, 25, 16,  8, 28, 27, 34, 17, 42,  5, 19, 11, 65,  9,  6, 55,
       39, 45, 63,  0, 13, 36, 57, 18, 51, 26, 64, 20, 52, 31, 43, 54, 44,
       21, 12, 48, 10, 22,  7, 53, 41,  3, 60, 24,  1, 61, 32, 14, 33, 37,
       38, 50, 49, 58, 40, 30, 46, 62,  4, 47,  2, 35, 29, 15, 23],
      dtype=int64), 'cur_cost': 106689.0}, {'tour': array([62,  1, 35, 51, 65, 20, 29,  6,  2, 41, 50, 53, 34, 13, 16, 42, 39,
       48, 47, 36, 22, 32, 58,  4, 52, 21, 55, 19, 54,  7, 38, 26,  5, 43,
       56, 64, 25, 17, 31, 23, 15, 28, 44, 40, 46,  0, 10, 18, 14, 57, 27,
       33, 61,  3,  9, 30, 63, 45, 37, 59, 49, 60,  8, 12, 24, 11],
      dtype=int64), 'cur_cost': 102927.0}, {'tour': array([10, 44, 41,  1,  5,  7, 48, 33,  8, 64, 42, 32, 65,  3, 23, 52, 61,
       34, 53,  0, 11, 29, 15, 19, 57, 14, 31, 60, 46, 22, 13, 27, 16, 59,
       30,  6, 35, 39, 40,  9, 49, 25, 38, 18,  4, 12, 26, 36, 24, 37,  2,
       21, 51, 20, 62, 43, 17, 63, 58, 55, 50, 45, 47, 28, 54, 56],
      dtype=int64), 'cur_cost': 99089.0}, {'tour': array([64,  6, 33, 46, 11, 15, 13, 21,  4, 57, 17, 42, 22, 23, 35,  3, 28,
       14,  2, 34, 43, 47, 31,  5, 58, 24, 29, 38, 49,  1, 50, 63,  7, 59,
       56, 55, 61, 62, 44, 60, 48, 52, 39, 16, 65, 41, 10, 36,  8, 40,  0,
       51, 25, 12, 20, 32, 37, 19, 53, 30, 26, 18, 54, 27, 45,  9],
      dtype=int64), 'cur_cost': 105593.0}, {'tour': array([27, 59, 58, 10, 55, 63, 54,  5, 46, 16, 57, 20,  2, 24, 23, 12, 42,
       40, 30, 56, 18, 51, 21, 36, 17, 33, 37, 26, 45, 39,  9, 48, 38, 49,
        3, 62,  6, 35, 25,  1, 32, 53,  4, 64, 41, 15,  8, 65,  0, 28, 29,
       50, 47, 13,  7, 11, 14, 60, 52, 43, 19, 34, 31, 22, 61, 44],
      dtype=int64), 'cur_cost': 87456.0}, {'tour': array([39, 10, 28, 40, 50, 25,  8, 59,  7, 51, 60, 27, 19, 64, 44, 38, 43,
       24, 47, 14, 22, 56, 12, 45, 37,  4,  2, 18, 20, 34,  1, 48, 41,  5,
        9, 62, 58, 26, 63, 23, 54, 16,  0, 30, 36, 49, 53, 46, 21, 29, 13,
        6, 32, 11,  3, 65, 61, 55, 33, 31, 35, 17, 42, 15, 57, 52],
      dtype=int64), 'cur_cost': 98224.0}, {'tour': array([34, 56, 45,  7, 16, 62,  9, 11, 18, 44, 19, 40, 32, 48, 30, 59, 39,
       36,  8, 29, 46,  0, 54, 50, 21,  1, 51, 42, 41, 55, 25, 14, 23, 28,
       63, 20, 26, 27, 60, 13, 37,  3, 52, 15, 31, 49, 58,  4, 65, 61, 57,
       24, 53, 17, 43, 38, 22,  6, 47, 33,  5, 64, 12, 10,  2, 35],
      dtype=int64), 'cur_cost': 108695.0}, {'tour': array([43, 65, 52, 48, 14,  0, 56,  6, 32, 21, 30, 25, 46,  5, 59, 57, 23,
       24, 22, 11, 26, 47, 55, 27, 53, 60,  4, 13, 54, 34, 42, 35, 64,  1,
       20, 31, 38, 16, 51, 36, 62, 10, 61, 39,  3,  2, 45, 50, 58, 37, 33,
       19,  7, 12, 63, 44, 28, 15,  9, 18, 29, 17,  8, 41, 49, 40],
      dtype=int64), 'cur_cost': 103144.0}, {'tour': array([ 1, 49, 19, 30, 26, 57, 11, 56, 31, 33, 39, 23, 18, 50, 20, 12, 24,
       40,  5, 60, 21, 54, 44, 53, 43,  3,  7, 28,  6, 62, 37, 10,  4, 17,
       64,  8, 55, 35, 65, 38, 51, 46,  9, 15, 58, 22,  0, 45, 34, 14, 61,
       48, 27, 13,  2, 36, 29, 32, 42, 25, 41, 16, 63, 52, 59, 47],
      dtype=int64), 'cur_cost': 106402.0}]
2025-08-01 17:46:11,032 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-01 17:46:11,032 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-01 17:46:11,033 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([39, 10, 28, 40, 50, 25,  8, 59,  7, 51, 60, 27, 19, 64, 44, 38, 43,
       24, 47, 14, 22, 56, 12, 45, 37,  4,  2, 18, 20, 34,  1, 48, 41,  5,
        9, 62, 58, 26, 63, 23, 54, 16,  0, 30, 36, 49, 53, 46, 21, 29, 13,
        6, 32, 11,  3, 65, 61, 55, 33, 31, 35, 17, 42, 15, 57, 52],
      dtype=int64), 'cur_cost': 98224.0}
2025-08-01 17:46:11,034 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-01 17:46:11,034 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:11,035 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:11,035 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 101381.0
2025-08-01 17:46:11,111 - ExploitationExpert - INFO - res_population_num: 8
2025-08-01 17:46:11,112 - ExploitationExpert - INFO - res_population_costs: [9856.0, 9529.0, 9521, 9521, 9521, 9521, 9521, 9521]
2025-08-01 17:46:11,113 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 17:46:11,119 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:11,119 - ExploitationExpert - INFO - populations: [{'tour': array([16, 60, 26, 52, 14, 27, 21,  3, 55, 50, 37, 57, 63, 41, 51, 29, 24,
       18, 44, 54, 42,  6, 59, 64, 20, 48, 62, 22, 53, 12, 30,  0, 65, 40,
       58, 23,  4, 19, 10, 25, 28, 36, 56, 39, 43, 11, 45,  2, 31, 47, 33,
       17, 35, 15, 46, 13,  5, 34,  9,  8, 49, 38, 32, 61,  7,  1],
      dtype=int64), 'cur_cost': 110301.0}, {'tour': array([59, 56, 25, 16,  8, 28, 27, 34, 17, 42,  5, 19, 11, 65,  9,  6, 55,
       39, 45, 63,  0, 13, 36, 57, 18, 51, 26, 64, 20, 52, 31, 43, 54, 44,
       21, 12, 48, 10, 22,  7, 53, 41,  3, 60, 24,  1, 61, 32, 14, 33, 37,
       38, 50, 49, 58, 40, 30, 46, 62,  4, 47,  2, 35, 29, 15, 23],
      dtype=int64), 'cur_cost': 106689.0}, {'tour': array([62,  1, 35, 51, 65, 20, 29,  6,  2, 41, 50, 53, 34, 13, 16, 42, 39,
       48, 47, 36, 22, 32, 58,  4, 52, 21, 55, 19, 54,  7, 38, 26,  5, 43,
       56, 64, 25, 17, 31, 23, 15, 28, 44, 40, 46,  0, 10, 18, 14, 57, 27,
       33, 61,  3,  9, 30, 63, 45, 37, 59, 49, 60,  8, 12, 24, 11],
      dtype=int64), 'cur_cost': 102927.0}, {'tour': array([10, 44, 41,  1,  5,  7, 48, 33,  8, 64, 42, 32, 65,  3, 23, 52, 61,
       34, 53,  0, 11, 29, 15, 19, 57, 14, 31, 60, 46, 22, 13, 27, 16, 59,
       30,  6, 35, 39, 40,  9, 49, 25, 38, 18,  4, 12, 26, 36, 24, 37,  2,
       21, 51, 20, 62, 43, 17, 63, 58, 55, 50, 45, 47, 28, 54, 56],
      dtype=int64), 'cur_cost': 99089.0}, {'tour': array([64,  6, 33, 46, 11, 15, 13, 21,  4, 57, 17, 42, 22, 23, 35,  3, 28,
       14,  2, 34, 43, 47, 31,  5, 58, 24, 29, 38, 49,  1, 50, 63,  7, 59,
       56, 55, 61, 62, 44, 60, 48, 52, 39, 16, 65, 41, 10, 36,  8, 40,  0,
       51, 25, 12, 20, 32, 37, 19, 53, 30, 26, 18, 54, 27, 45,  9],
      dtype=int64), 'cur_cost': 105593.0}, {'tour': array([27, 59, 58, 10, 55, 63, 54,  5, 46, 16, 57, 20,  2, 24, 23, 12, 42,
       40, 30, 56, 18, 51, 21, 36, 17, 33, 37, 26, 45, 39,  9, 48, 38, 49,
        3, 62,  6, 35, 25,  1, 32, 53,  4, 64, 41, 15,  8, 65,  0, 28, 29,
       50, 47, 13,  7, 11, 14, 60, 52, 43, 19, 34, 31, 22, 61, 44],
      dtype=int64), 'cur_cost': 87456.0}, {'tour': array([39, 10, 28, 40, 50, 25,  8, 59,  7, 51, 60, 27, 19, 64, 44, 38, 43,
       24, 47, 14, 22, 56, 12, 45, 37,  4,  2, 18, 20, 34,  1, 48, 41,  5,
        9, 62, 58, 26, 63, 23, 54, 16,  0, 30, 36, 49, 53, 46, 21, 29, 13,
        6, 32, 11,  3, 65, 61, 55, 33, 31, 35, 17, 42, 15, 57, 52],
      dtype=int64), 'cur_cost': 98224.0}, {'tour': array([56, 53, 19, 16, 21, 50, 49, 52, 58,  1, 59, 22, 32, 51, 25, 26, 39,
       24, 55, 12,  4, 37, 18, 34, 41, 54, 40,  8, 64,  0, 11, 47,  5,  9,
       65,  7,  3, 44, 31,  2, 33, 48, 23, 10, 63, 15, 60, 42, 29, 38, 45,
       61, 62, 43, 35, 57, 27, 30, 13, 14, 20,  6, 36, 46, 28, 17],
      dtype=int64), 'cur_cost': 101381.0}, {'tour': array([43, 65, 52, 48, 14,  0, 56,  6, 32, 21, 30, 25, 46,  5, 59, 57, 23,
       24, 22, 11, 26, 47, 55, 27, 53, 60,  4, 13, 54, 34, 42, 35, 64,  1,
       20, 31, 38, 16, 51, 36, 62, 10, 61, 39,  3,  2, 45, 50, 58, 37, 33,
       19,  7, 12, 63, 44, 28, 15,  9, 18, 29, 17,  8, 41, 49, 40],
      dtype=int64), 'cur_cost': 103144.0}, {'tour': array([ 1, 49, 19, 30, 26, 57, 11, 56, 31, 33, 39, 23, 18, 50, 20, 12, 24,
       40,  5, 60, 21, 54, 44, 53, 43,  3,  7, 28,  6, 62, 37, 10,  4, 17,
       64,  8, 55, 35, 65, 38, 51, 46,  9, 15, 58, 22,  0, 45, 34, 14, 61,
       48, 27, 13,  2, 36, 29, 32, 42, 25, 41, 16, 63, 52, 59, 47],
      dtype=int64), 'cur_cost': 106402.0}]
2025-08-01 17:46:11,127 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-01 17:46:11,127 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-08-01 17:46:11,128 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([56, 53, 19, 16, 21, 50, 49, 52, 58,  1, 59, 22, 32, 51, 25, 26, 39,
       24, 55, 12,  4, 37, 18, 34, 41, 54, 40,  8, 64,  0, 11, 47,  5,  9,
       65,  7,  3, 44, 31,  2, 33, 48, 23, 10, 63, 15, 60, 42, 29, 38, 45,
       61, 62, 43, 35, 57, 27, 30, 13, 14, 20,  6, 36, 46, 28, 17],
      dtype=int64), 'cur_cost': 101381.0}
2025-08-01 17:46:11,129 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-01 17:46:11,130 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:11,130 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:11,130 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 108923.0
2025-08-01 17:46:11,216 - ExploitationExpert - INFO - res_population_num: 8
2025-08-01 17:46:11,216 - ExploitationExpert - INFO - res_population_costs: [9856.0, 9529.0, 9521, 9521, 9521, 9521, 9521, 9521]
2025-08-01 17:46:11,217 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 17:46:11,222 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:11,223 - ExploitationExpert - INFO - populations: [{'tour': array([16, 60, 26, 52, 14, 27, 21,  3, 55, 50, 37, 57, 63, 41, 51, 29, 24,
       18, 44, 54, 42,  6, 59, 64, 20, 48, 62, 22, 53, 12, 30,  0, 65, 40,
       58, 23,  4, 19, 10, 25, 28, 36, 56, 39, 43, 11, 45,  2, 31, 47, 33,
       17, 35, 15, 46, 13,  5, 34,  9,  8, 49, 38, 32, 61,  7,  1],
      dtype=int64), 'cur_cost': 110301.0}, {'tour': array([59, 56, 25, 16,  8, 28, 27, 34, 17, 42,  5, 19, 11, 65,  9,  6, 55,
       39, 45, 63,  0, 13, 36, 57, 18, 51, 26, 64, 20, 52, 31, 43, 54, 44,
       21, 12, 48, 10, 22,  7, 53, 41,  3, 60, 24,  1, 61, 32, 14, 33, 37,
       38, 50, 49, 58, 40, 30, 46, 62,  4, 47,  2, 35, 29, 15, 23],
      dtype=int64), 'cur_cost': 106689.0}, {'tour': array([62,  1, 35, 51, 65, 20, 29,  6,  2, 41, 50, 53, 34, 13, 16, 42, 39,
       48, 47, 36, 22, 32, 58,  4, 52, 21, 55, 19, 54,  7, 38, 26,  5, 43,
       56, 64, 25, 17, 31, 23, 15, 28, 44, 40, 46,  0, 10, 18, 14, 57, 27,
       33, 61,  3,  9, 30, 63, 45, 37, 59, 49, 60,  8, 12, 24, 11],
      dtype=int64), 'cur_cost': 102927.0}, {'tour': array([10, 44, 41,  1,  5,  7, 48, 33,  8, 64, 42, 32, 65,  3, 23, 52, 61,
       34, 53,  0, 11, 29, 15, 19, 57, 14, 31, 60, 46, 22, 13, 27, 16, 59,
       30,  6, 35, 39, 40,  9, 49, 25, 38, 18,  4, 12, 26, 36, 24, 37,  2,
       21, 51, 20, 62, 43, 17, 63, 58, 55, 50, 45, 47, 28, 54, 56],
      dtype=int64), 'cur_cost': 99089.0}, {'tour': array([64,  6, 33, 46, 11, 15, 13, 21,  4, 57, 17, 42, 22, 23, 35,  3, 28,
       14,  2, 34, 43, 47, 31,  5, 58, 24, 29, 38, 49,  1, 50, 63,  7, 59,
       56, 55, 61, 62, 44, 60, 48, 52, 39, 16, 65, 41, 10, 36,  8, 40,  0,
       51, 25, 12, 20, 32, 37, 19, 53, 30, 26, 18, 54, 27, 45,  9],
      dtype=int64), 'cur_cost': 105593.0}, {'tour': array([27, 59, 58, 10, 55, 63, 54,  5, 46, 16, 57, 20,  2, 24, 23, 12, 42,
       40, 30, 56, 18, 51, 21, 36, 17, 33, 37, 26, 45, 39,  9, 48, 38, 49,
        3, 62,  6, 35, 25,  1, 32, 53,  4, 64, 41, 15,  8, 65,  0, 28, 29,
       50, 47, 13,  7, 11, 14, 60, 52, 43, 19, 34, 31, 22, 61, 44],
      dtype=int64), 'cur_cost': 87456.0}, {'tour': array([39, 10, 28, 40, 50, 25,  8, 59,  7, 51, 60, 27, 19, 64, 44, 38, 43,
       24, 47, 14, 22, 56, 12, 45, 37,  4,  2, 18, 20, 34,  1, 48, 41,  5,
        9, 62, 58, 26, 63, 23, 54, 16,  0, 30, 36, 49, 53, 46, 21, 29, 13,
        6, 32, 11,  3, 65, 61, 55, 33, 31, 35, 17, 42, 15, 57, 52],
      dtype=int64), 'cur_cost': 98224.0}, {'tour': array([56, 53, 19, 16, 21, 50, 49, 52, 58,  1, 59, 22, 32, 51, 25, 26, 39,
       24, 55, 12,  4, 37, 18, 34, 41, 54, 40,  8, 64,  0, 11, 47,  5,  9,
       65,  7,  3, 44, 31,  2, 33, 48, 23, 10, 63, 15, 60, 42, 29, 38, 45,
       61, 62, 43, 35, 57, 27, 30, 13, 14, 20,  6, 36, 46, 28, 17],
      dtype=int64), 'cur_cost': 101381.0}, {'tour': array([57, 20, 16, 50, 21, 11,  3, 38, 42, 58,  2, 17, 31, 33, 27, 55, 64,
        4, 15,  5, 23, 24, 13, 51,  9,  8, 53, 30, 48, 47, 12, 63,  0, 32,
       28,  1, 49, 18, 44, 25, 62, 14, 29, 52, 43, 65, 39, 46, 61, 26, 54,
       36, 45, 56, 22, 35,  6, 40,  7, 59, 34, 37, 60, 10, 19, 41],
      dtype=int64), 'cur_cost': 108923.0}, {'tour': array([ 1, 49, 19, 30, 26, 57, 11, 56, 31, 33, 39, 23, 18, 50, 20, 12, 24,
       40,  5, 60, 21, 54, 44, 53, 43,  3,  7, 28,  6, 62, 37, 10,  4, 17,
       64,  8, 55, 35, 65, 38, 51, 46,  9, 15, 58, 22,  0, 45, 34, 14, 61,
       48, 27, 13,  2, 36, 29, 32, 42, 25, 41, 16, 63, 52, 59, 47],
      dtype=int64), 'cur_cost': 106402.0}]
2025-08-01 17:46:11,231 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-01 17:46:11,232 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-08-01 17:46:11,232 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([57, 20, 16, 50, 21, 11,  3, 38, 42, 58,  2, 17, 31, 33, 27, 55, 64,
        4, 15,  5, 23, 24, 13, 51,  9,  8, 53, 30, 48, 47, 12, 63,  0, 32,
       28,  1, 49, 18, 44, 25, 62, 14, 29, 52, 43, 65, 39, 46, 61, 26, 54,
       36, 45, 56, 22, 35,  6, 40,  7, 59, 34, 37, 60, 10, 19, 41],
      dtype=int64), 'cur_cost': 108923.0}
2025-08-01 17:46:11,233 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-01 17:46:11,233 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:11,233 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:11,234 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 109318.0
2025-08-01 17:46:11,313 - ExploitationExpert - INFO - res_population_num: 8
2025-08-01 17:46:11,314 - ExploitationExpert - INFO - res_population_costs: [9856.0, 9529.0, 9521, 9521, 9521, 9521, 9521, 9521]
2025-08-01 17:46:11,314 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 17:46:11,320 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:11,320 - ExploitationExpert - INFO - populations: [{'tour': array([16, 60, 26, 52, 14, 27, 21,  3, 55, 50, 37, 57, 63, 41, 51, 29, 24,
       18, 44, 54, 42,  6, 59, 64, 20, 48, 62, 22, 53, 12, 30,  0, 65, 40,
       58, 23,  4, 19, 10, 25, 28, 36, 56, 39, 43, 11, 45,  2, 31, 47, 33,
       17, 35, 15, 46, 13,  5, 34,  9,  8, 49, 38, 32, 61,  7,  1],
      dtype=int64), 'cur_cost': 110301.0}, {'tour': array([59, 56, 25, 16,  8, 28, 27, 34, 17, 42,  5, 19, 11, 65,  9,  6, 55,
       39, 45, 63,  0, 13, 36, 57, 18, 51, 26, 64, 20, 52, 31, 43, 54, 44,
       21, 12, 48, 10, 22,  7, 53, 41,  3, 60, 24,  1, 61, 32, 14, 33, 37,
       38, 50, 49, 58, 40, 30, 46, 62,  4, 47,  2, 35, 29, 15, 23],
      dtype=int64), 'cur_cost': 106689.0}, {'tour': array([62,  1, 35, 51, 65, 20, 29,  6,  2, 41, 50, 53, 34, 13, 16, 42, 39,
       48, 47, 36, 22, 32, 58,  4, 52, 21, 55, 19, 54,  7, 38, 26,  5, 43,
       56, 64, 25, 17, 31, 23, 15, 28, 44, 40, 46,  0, 10, 18, 14, 57, 27,
       33, 61,  3,  9, 30, 63, 45, 37, 59, 49, 60,  8, 12, 24, 11],
      dtype=int64), 'cur_cost': 102927.0}, {'tour': array([10, 44, 41,  1,  5,  7, 48, 33,  8, 64, 42, 32, 65,  3, 23, 52, 61,
       34, 53,  0, 11, 29, 15, 19, 57, 14, 31, 60, 46, 22, 13, 27, 16, 59,
       30,  6, 35, 39, 40,  9, 49, 25, 38, 18,  4, 12, 26, 36, 24, 37,  2,
       21, 51, 20, 62, 43, 17, 63, 58, 55, 50, 45, 47, 28, 54, 56],
      dtype=int64), 'cur_cost': 99089.0}, {'tour': array([64,  6, 33, 46, 11, 15, 13, 21,  4, 57, 17, 42, 22, 23, 35,  3, 28,
       14,  2, 34, 43, 47, 31,  5, 58, 24, 29, 38, 49,  1, 50, 63,  7, 59,
       56, 55, 61, 62, 44, 60, 48, 52, 39, 16, 65, 41, 10, 36,  8, 40,  0,
       51, 25, 12, 20, 32, 37, 19, 53, 30, 26, 18, 54, 27, 45,  9],
      dtype=int64), 'cur_cost': 105593.0}, {'tour': array([27, 59, 58, 10, 55, 63, 54,  5, 46, 16, 57, 20,  2, 24, 23, 12, 42,
       40, 30, 56, 18, 51, 21, 36, 17, 33, 37, 26, 45, 39,  9, 48, 38, 49,
        3, 62,  6, 35, 25,  1, 32, 53,  4, 64, 41, 15,  8, 65,  0, 28, 29,
       50, 47, 13,  7, 11, 14, 60, 52, 43, 19, 34, 31, 22, 61, 44],
      dtype=int64), 'cur_cost': 87456.0}, {'tour': array([39, 10, 28, 40, 50, 25,  8, 59,  7, 51, 60, 27, 19, 64, 44, 38, 43,
       24, 47, 14, 22, 56, 12, 45, 37,  4,  2, 18, 20, 34,  1, 48, 41,  5,
        9, 62, 58, 26, 63, 23, 54, 16,  0, 30, 36, 49, 53, 46, 21, 29, 13,
        6, 32, 11,  3, 65, 61, 55, 33, 31, 35, 17, 42, 15, 57, 52],
      dtype=int64), 'cur_cost': 98224.0}, {'tour': array([56, 53, 19, 16, 21, 50, 49, 52, 58,  1, 59, 22, 32, 51, 25, 26, 39,
       24, 55, 12,  4, 37, 18, 34, 41, 54, 40,  8, 64,  0, 11, 47,  5,  9,
       65,  7,  3, 44, 31,  2, 33, 48, 23, 10, 63, 15, 60, 42, 29, 38, 45,
       61, 62, 43, 35, 57, 27, 30, 13, 14, 20,  6, 36, 46, 28, 17],
      dtype=int64), 'cur_cost': 101381.0}, {'tour': array([57, 20, 16, 50, 21, 11,  3, 38, 42, 58,  2, 17, 31, 33, 27, 55, 64,
        4, 15,  5, 23, 24, 13, 51,  9,  8, 53, 30, 48, 47, 12, 63,  0, 32,
       28,  1, 49, 18, 44, 25, 62, 14, 29, 52, 43, 65, 39, 46, 61, 26, 54,
       36, 45, 56, 22, 35,  6, 40,  7, 59, 34, 37, 60, 10, 19, 41],
      dtype=int64), 'cur_cost': 108923.0}, {'tour': array([12,  6, 62, 19, 39, 46, 44, 30, 45, 63, 16, 33, 58, 15, 29, 41, 24,
       36,  0, 14, 20, 32,  4, 56, 31, 60, 23, 21, 53,  3,  8, 34, 11, 50,
       59,  9, 13, 22, 47, 40, 25, 65,  5,  7, 51, 43, 61, 18, 10, 27, 48,
        1, 55, 49, 57, 54, 26, 38,  2, 52, 37, 17, 28, 64, 42, 35],
      dtype=int64), 'cur_cost': 109318.0}]
2025-08-01 17:46:11,328 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-01 17:46:11,329 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-08-01 17:46:11,329 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([12,  6, 62, 19, 39, 46, 44, 30, 45, 63, 16, 33, 58, 15, 29, 41, 24,
       36,  0, 14, 20, 32,  4, 56, 31, 60, 23, 21, 53,  3,  8, 34, 11, 50,
       59,  9, 13, 22, 47, 40, 25, 65,  5,  7, 51, 43, 61, 18, 10, 27, 48,
        1, 55, 49, 57, 54, 26, 38,  2, 52, 37, 17, 28, 64, 42, 35],
      dtype=int64), 'cur_cost': 109318.0}
2025-08-01 17:46:11,332 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([16, 60, 26, 52, 14, 27, 21,  3, 55, 50, 37, 57, 63, 41, 51, 29, 24,
       18, 44, 54, 42,  6, 59, 64, 20, 48, 62, 22, 53, 12, 30,  0, 65, 40,
       58, 23,  4, 19, 10, 25, 28, 36, 56, 39, 43, 11, 45,  2, 31, 47, 33,
       17, 35, 15, 46, 13,  5, 34,  9,  8, 49, 38, 32, 61,  7,  1],
      dtype=int64), 'cur_cost': 110301.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([59, 56, 25, 16,  8, 28, 27, 34, 17, 42,  5, 19, 11, 65,  9,  6, 55,
       39, 45, 63,  0, 13, 36, 57, 18, 51, 26, 64, 20, 52, 31, 43, 54, 44,
       21, 12, 48, 10, 22,  7, 53, 41,  3, 60, 24,  1, 61, 32, 14, 33, 37,
       38, 50, 49, 58, 40, 30, 46, 62,  4, 47,  2, 35, 29, 15, 23],
      dtype=int64), 'cur_cost': 106689.0}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([62,  1, 35, 51, 65, 20, 29,  6,  2, 41, 50, 53, 34, 13, 16, 42, 39,
       48, 47, 36, 22, 32, 58,  4, 52, 21, 55, 19, 54,  7, 38, 26,  5, 43,
       56, 64, 25, 17, 31, 23, 15, 28, 44, 40, 46,  0, 10, 18, 14, 57, 27,
       33, 61,  3,  9, 30, 63, 45, 37, 59, 49, 60,  8, 12, 24, 11],
      dtype=int64), 'cur_cost': 102927.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([10, 44, 41,  1,  5,  7, 48, 33,  8, 64, 42, 32, 65,  3, 23, 52, 61,
       34, 53,  0, 11, 29, 15, 19, 57, 14, 31, 60, 46, 22, 13, 27, 16, 59,
       30,  6, 35, 39, 40,  9, 49, 25, 38, 18,  4, 12, 26, 36, 24, 37,  2,
       21, 51, 20, 62, 43, 17, 63, 58, 55, 50, 45, 47, 28, 54, 56],
      dtype=int64), 'cur_cost': 99089.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([64,  6, 33, 46, 11, 15, 13, 21,  4, 57, 17, 42, 22, 23, 35,  3, 28,
       14,  2, 34, 43, 47, 31,  5, 58, 24, 29, 38, 49,  1, 50, 63,  7, 59,
       56, 55, 61, 62, 44, 60, 48, 52, 39, 16, 65, 41, 10, 36,  8, 40,  0,
       51, 25, 12, 20, 32, 37, 19, 53, 30, 26, 18, 54, 27, 45,  9],
      dtype=int64), 'cur_cost': 105593.0}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([27, 59, 58, 10, 55, 63, 54,  5, 46, 16, 57, 20,  2, 24, 23, 12, 42,
       40, 30, 56, 18, 51, 21, 36, 17, 33, 37, 26, 45, 39,  9, 48, 38, 49,
        3, 62,  6, 35, 25,  1, 32, 53,  4, 64, 41, 15,  8, 65,  0, 28, 29,
       50, 47, 13,  7, 11, 14, 60, 52, 43, 19, 34, 31, 22, 61, 44],
      dtype=int64), 'cur_cost': 87456.0}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([39, 10, 28, 40, 50, 25,  8, 59,  7, 51, 60, 27, 19, 64, 44, 38, 43,
       24, 47, 14, 22, 56, 12, 45, 37,  4,  2, 18, 20, 34,  1, 48, 41,  5,
        9, 62, 58, 26, 63, 23, 54, 16,  0, 30, 36, 49, 53, 46, 21, 29, 13,
        6, 32, 11,  3, 65, 61, 55, 33, 31, 35, 17, 42, 15, 57, 52],
      dtype=int64), 'cur_cost': 98224.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([56, 53, 19, 16, 21, 50, 49, 52, 58,  1, 59, 22, 32, 51, 25, 26, 39,
       24, 55, 12,  4, 37, 18, 34, 41, 54, 40,  8, 64,  0, 11, 47,  5,  9,
       65,  7,  3, 44, 31,  2, 33, 48, 23, 10, 63, 15, 60, 42, 29, 38, 45,
       61, 62, 43, 35, 57, 27, 30, 13, 14, 20,  6, 36, 46, 28, 17],
      dtype=int64), 'cur_cost': 101381.0}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([57, 20, 16, 50, 21, 11,  3, 38, 42, 58,  2, 17, 31, 33, 27, 55, 64,
        4, 15,  5, 23, 24, 13, 51,  9,  8, 53, 30, 48, 47, 12, 63,  0, 32,
       28,  1, 49, 18, 44, 25, 62, 14, 29, 52, 43, 65, 39, 46, 61, 26, 54,
       36, 45, 56, 22, 35,  6, 40,  7, 59, 34, 37, 60, 10, 19, 41],
      dtype=int64), 'cur_cost': 108923.0}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([12,  6, 62, 19, 39, 46, 44, 30, 45, 63, 16, 33, 58, 15, 29, 41, 24,
       36,  0, 14, 20, 32,  4, 56, 31, 60, 23, 21, 53,  3,  8, 34, 11, 50,
       59,  9, 13, 22, 47, 40, 25, 65,  5,  7, 51, 43, 61, 18, 10, 27, 48,
        1, 55, 49, 57, 54, 26, 38,  2, 52, 37, 17, 28, 64, 42, 35],
      dtype=int64), 'cur_cost': 109318.0}}]
2025-08-01 17:46:11,336 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-01 17:46:11,336 - StatsExpert - INFO - 开始统计分析
2025-08-01 17:46:11,343 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=87456.000, 多样性=0.988
2025-08-01 17:46:11,343 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-01 17:46:11,343 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-01 17:46:11,344 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-01 17:46:11,345 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -2.6573769635894275, 'best_improvement': -7.783368484483278}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.019097222222222307}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 8, 'new_count': 8, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.7343073593073594, 'new_diversity': 0.7343073593073594, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-01 17:46:11,348 - main - INFO - --- Finished Evolution Iteration 1 ---
2025-08-01 17:46:11,348 - main - INFO - composite13_66 开始进化第 2 代
2025-08-01 17:46:11,348 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-01 17:46:11,349 - StatsExpert - INFO - 开始统计分析
2025-08-01 17:46:11,350 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=87456.000, 多样性=0.988
2025-08-01 17:46:11,350 - PathExpert - INFO - 开始路径结构分析
2025-08-01 17:46:11,358 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.988
2025-08-01 17:46:11,361 - EliteExpert - INFO - 开始精英解分析
2025-08-01 17:46:11,370 - EliteExpert - INFO - 精英解分析完成: 精英解数量=8, 多样性=0.734
2025-08-01 17:46:11,382 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-01 17:46:11,383 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-01 17:46:11,384 - LandscapeExpert - INFO - 添加精英解数据: 8个精英解
2025-08-01 17:46:11,384 - LandscapeExpert - INFO - 数据提取成功: 18个路径, 18个适应度值
2025-08-01 17:46:11,476 - core.algorithms.fitness_landscape_analyzer - ERROR - 景观分析失败: '>' not supported between instances of 'str' and 'int'
2025-08-01 17:46:11,478 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-01 17:46:11,478 - LandscapeExpert - INFO - 提取到 8 个精英解
2025-08-01 17:46:11,492 - visualization.landscape_visualizer - INFO - 已添加 8 个精英解标记
2025-08-01 17:46:11,575 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_2_20250801_174611.html
2025-08-01 17:46:11,625 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_2_20250801_174611.html
2025-08-01 17:46:11,625 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 2
2025-08-01 17:46:11,625 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-01 17:46:11,626 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2441秒
2025-08-01 17:46:11,627 - main - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754041571.4787111, 'performance_metrics': {}}}
2025-08-01 17:46:11,628 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-01 17:46:11,628 - StrategyExpert - INFO - 开始策略分配分析
2025-08-01 17:46:11,629 - StrategyExpert - INFO - 消融实验模式：使用纯exploit策略
2025-08-01 17:46:11,629 - StrategyExpert - INFO - 策略分配分析完成（消融实验模式）
2025-08-01 17:46:11,630 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-08-01 17:46:11,630 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 消融实验模式：所有10个个体都使用exploit策略
2025-08-01 17:46:11,630 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-01 17:46:11,631 - main - INFO - 策略分配: ['exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-08-01 17:46:11,631 - main - INFO - 策略分配完整报告: 消融实验模式：所有10个个体都使用exploit策略
2025-08-01 17:46:11,631 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-01 17:46:11,632 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-01 17:46:11,632 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:11,632 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:11,633 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 107270.0
2025-08-01 17:46:11,715 - ExploitationExpert - INFO - res_population_num: 8
2025-08-01 17:46:11,715 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0]
2025-08-01 17:46:11,716 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64)]
2025-08-01 17:46:11,721 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:11,722 - ExploitationExpert - INFO - populations: [{'tour': array([44,  4, 52, 49, 12,  9, 42, 25,  7, 11, 46,  2,  8, 62, 31, 16, 29,
        6, 41, 39, 58, 65, 21,  0, 40, 10, 23, 33, 35, 18, 26, 27, 57, 37,
       19, 48, 45, 56, 14, 30, 55, 32, 22,  5, 28, 43, 51, 13, 15, 53,  3,
       47, 54, 50, 63, 34, 36,  1, 59, 60, 64, 38, 20, 17, 24, 61],
      dtype=int64), 'cur_cost': 107270.0}, {'tour': [59, 56, 25, 16, 8, 28, 27, 34, 17, 42, 5, 19, 11, 65, 9, 6, 55, 39, 45, 63, 0, 13, 36, 57, 18, 51, 26, 64, 20, 52, 31, 43, 54, 44, 21, 12, 48, 10, 22, 7, 53, 41, 3, 60, 24, 1, 61, 32, 14, 33, 37, 38, 50, 49, 58, 40, 30, 46, 62, 4, 47, 2, 35, 29, 15, 23], 'cur_cost': 106689.0}, {'tour': [62, 1, 35, 51, 65, 20, 29, 6, 2, 41, 50, 53, 34, 13, 16, 42, 39, 48, 47, 36, 22, 32, 58, 4, 52, 21, 55, 19, 54, 7, 38, 26, 5, 43, 56, 64, 25, 17, 31, 23, 15, 28, 44, 40, 46, 0, 10, 18, 14, 57, 27, 33, 61, 3, 9, 30, 63, 45, 37, 59, 49, 60, 8, 12, 24, 11], 'cur_cost': 102927.0}, {'tour': [10, 44, 41, 1, 5, 7, 48, 33, 8, 64, 42, 32, 65, 3, 23, 52, 61, 34, 53, 0, 11, 29, 15, 19, 57, 14, 31, 60, 46, 22, 13, 27, 16, 59, 30, 6, 35, 39, 40, 9, 49, 25, 38, 18, 4, 12, 26, 36, 24, 37, 2, 21, 51, 20, 62, 43, 17, 63, 58, 55, 50, 45, 47, 28, 54, 56], 'cur_cost': 99089.0}, {'tour': [64, 6, 33, 46, 11, 15, 13, 21, 4, 57, 17, 42, 22, 23, 35, 3, 28, 14, 2, 34, 43, 47, 31, 5, 58, 24, 29, 38, 49, 1, 50, 63, 7, 59, 56, 55, 61, 62, 44, 60, 48, 52, 39, 16, 65, 41, 10, 36, 8, 40, 0, 51, 25, 12, 20, 32, 37, 19, 53, 30, 26, 18, 54, 27, 45, 9], 'cur_cost': 105593.0}, {'tour': [27, 59, 58, 10, 55, 63, 54, 5, 46, 16, 57, 20, 2, 24, 23, 12, 42, 40, 30, 56, 18, 51, 21, 36, 17, 33, 37, 26, 45, 39, 9, 48, 38, 49, 3, 62, 6, 35, 25, 1, 32, 53, 4, 64, 41, 15, 8, 65, 0, 28, 29, 50, 47, 13, 7, 11, 14, 60, 52, 43, 19, 34, 31, 22, 61, 44], 'cur_cost': 87456.0}, {'tour': [39, 10, 28, 40, 50, 25, 8, 59, 7, 51, 60, 27, 19, 64, 44, 38, 43, 24, 47, 14, 22, 56, 12, 45, 37, 4, 2, 18, 20, 34, 1, 48, 41, 5, 9, 62, 58, 26, 63, 23, 54, 16, 0, 30, 36, 49, 53, 46, 21, 29, 13, 6, 32, 11, 3, 65, 61, 55, 33, 31, 35, 17, 42, 15, 57, 52], 'cur_cost': 98224.0}, {'tour': [56, 53, 19, 16, 21, 50, 49, 52, 58, 1, 59, 22, 32, 51, 25, 26, 39, 24, 55, 12, 4, 37, 18, 34, 41, 54, 40, 8, 64, 0, 11, 47, 5, 9, 65, 7, 3, 44, 31, 2, 33, 48, 23, 10, 63, 15, 60, 42, 29, 38, 45, 61, 62, 43, 35, 57, 27, 30, 13, 14, 20, 6, 36, 46, 28, 17], 'cur_cost': 101381.0}, {'tour': [57, 20, 16, 50, 21, 11, 3, 38, 42, 58, 2, 17, 31, 33, 27, 55, 64, 4, 15, 5, 23, 24, 13, 51, 9, 8, 53, 30, 48, 47, 12, 63, 0, 32, 28, 1, 49, 18, 44, 25, 62, 14, 29, 52, 43, 65, 39, 46, 61, 26, 54, 36, 45, 56, 22, 35, 6, 40, 7, 59, 34, 37, 60, 10, 19, 41], 'cur_cost': 108923.0}, {'tour': [12, 6, 62, 19, 39, 46, 44, 30, 45, 63, 16, 33, 58, 15, 29, 41, 24, 36, 0, 14, 20, 32, 4, 56, 31, 60, 23, 21, 53, 3, 8, 34, 11, 50, 59, 9, 13, 22, 47, 40, 25, 65, 5, 7, 51, 43, 61, 18, 10, 27, 48, 1, 55, 49, 57, 54, 26, 38, 2, 52, 37, 17, 28, 64, 42, 35], 'cur_cost': 109318.0}]
2025-08-01 17:46:11,725 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-01 17:46:11,725 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-08-01 17:46:11,725 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([44,  4, 52, 49, 12,  9, 42, 25,  7, 11, 46,  2,  8, 62, 31, 16, 29,
        6, 41, 39, 58, 65, 21,  0, 40, 10, 23, 33, 35, 18, 26, 27, 57, 37,
       19, 48, 45, 56, 14, 30, 55, 32, 22,  5, 28, 43, 51, 13, 15, 53,  3,
       47, 54, 50, 63, 34, 36,  1, 59, 60, 64, 38, 20, 17, 24, 61],
      dtype=int64), 'cur_cost': 107270.0}
2025-08-01 17:46:11,726 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-01 17:46:11,726 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:11,726 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:11,727 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 97462.0
2025-08-01 17:46:11,813 - ExploitationExpert - INFO - res_population_num: 9
2025-08-01 17:46:11,814 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0, 9521]
2025-08-01 17:46:11,814 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-01 17:46:11,821 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:11,821 - ExploitationExpert - INFO - populations: [{'tour': array([44,  4, 52, 49, 12,  9, 42, 25,  7, 11, 46,  2,  8, 62, 31, 16, 29,
        6, 41, 39, 58, 65, 21,  0, 40, 10, 23, 33, 35, 18, 26, 27, 57, 37,
       19, 48, 45, 56, 14, 30, 55, 32, 22,  5, 28, 43, 51, 13, 15, 53,  3,
       47, 54, 50, 63, 34, 36,  1, 59, 60, 64, 38, 20, 17, 24, 61],
      dtype=int64), 'cur_cost': 107270.0}, {'tour': array([25, 18, 40,  7, 22,  9, 26, 58,  1,  5,  4, 37, 28, 42, 35, 15, 55,
       12, 61, 64, 62, 21, 63, 48, 13, 16, 39, 45, 53, 46, 43,  6,  0, 20,
       19, 51, 56, 65, 11, 31, 38, 44, 27, 54,  8, 41, 60,  3, 29,  2, 59,
       23, 52, 34, 24, 32, 36, 47, 10, 49, 33, 30, 17, 14, 57, 50],
      dtype=int64), 'cur_cost': 97462.0}, {'tour': [62, 1, 35, 51, 65, 20, 29, 6, 2, 41, 50, 53, 34, 13, 16, 42, 39, 48, 47, 36, 22, 32, 58, 4, 52, 21, 55, 19, 54, 7, 38, 26, 5, 43, 56, 64, 25, 17, 31, 23, 15, 28, 44, 40, 46, 0, 10, 18, 14, 57, 27, 33, 61, 3, 9, 30, 63, 45, 37, 59, 49, 60, 8, 12, 24, 11], 'cur_cost': 102927.0}, {'tour': [10, 44, 41, 1, 5, 7, 48, 33, 8, 64, 42, 32, 65, 3, 23, 52, 61, 34, 53, 0, 11, 29, 15, 19, 57, 14, 31, 60, 46, 22, 13, 27, 16, 59, 30, 6, 35, 39, 40, 9, 49, 25, 38, 18, 4, 12, 26, 36, 24, 37, 2, 21, 51, 20, 62, 43, 17, 63, 58, 55, 50, 45, 47, 28, 54, 56], 'cur_cost': 99089.0}, {'tour': [64, 6, 33, 46, 11, 15, 13, 21, 4, 57, 17, 42, 22, 23, 35, 3, 28, 14, 2, 34, 43, 47, 31, 5, 58, 24, 29, 38, 49, 1, 50, 63, 7, 59, 56, 55, 61, 62, 44, 60, 48, 52, 39, 16, 65, 41, 10, 36, 8, 40, 0, 51, 25, 12, 20, 32, 37, 19, 53, 30, 26, 18, 54, 27, 45, 9], 'cur_cost': 105593.0}, {'tour': [27, 59, 58, 10, 55, 63, 54, 5, 46, 16, 57, 20, 2, 24, 23, 12, 42, 40, 30, 56, 18, 51, 21, 36, 17, 33, 37, 26, 45, 39, 9, 48, 38, 49, 3, 62, 6, 35, 25, 1, 32, 53, 4, 64, 41, 15, 8, 65, 0, 28, 29, 50, 47, 13, 7, 11, 14, 60, 52, 43, 19, 34, 31, 22, 61, 44], 'cur_cost': 87456.0}, {'tour': [39, 10, 28, 40, 50, 25, 8, 59, 7, 51, 60, 27, 19, 64, 44, 38, 43, 24, 47, 14, 22, 56, 12, 45, 37, 4, 2, 18, 20, 34, 1, 48, 41, 5, 9, 62, 58, 26, 63, 23, 54, 16, 0, 30, 36, 49, 53, 46, 21, 29, 13, 6, 32, 11, 3, 65, 61, 55, 33, 31, 35, 17, 42, 15, 57, 52], 'cur_cost': 98224.0}, {'tour': [56, 53, 19, 16, 21, 50, 49, 52, 58, 1, 59, 22, 32, 51, 25, 26, 39, 24, 55, 12, 4, 37, 18, 34, 41, 54, 40, 8, 64, 0, 11, 47, 5, 9, 65, 7, 3, 44, 31, 2, 33, 48, 23, 10, 63, 15, 60, 42, 29, 38, 45, 61, 62, 43, 35, 57, 27, 30, 13, 14, 20, 6, 36, 46, 28, 17], 'cur_cost': 101381.0}, {'tour': [57, 20, 16, 50, 21, 11, 3, 38, 42, 58, 2, 17, 31, 33, 27, 55, 64, 4, 15, 5, 23, 24, 13, 51, 9, 8, 53, 30, 48, 47, 12, 63, 0, 32, 28, 1, 49, 18, 44, 25, 62, 14, 29, 52, 43, 65, 39, 46, 61, 26, 54, 36, 45, 56, 22, 35, 6, 40, 7, 59, 34, 37, 60, 10, 19, 41], 'cur_cost': 108923.0}, {'tour': [12, 6, 62, 19, 39, 46, 44, 30, 45, 63, 16, 33, 58, 15, 29, 41, 24, 36, 0, 14, 20, 32, 4, 56, 31, 60, 23, 21, 53, 3, 8, 34, 11, 50, 59, 9, 13, 22, 47, 40, 25, 65, 5, 7, 51, 43, 61, 18, 10, 27, 48, 1, 55, 49, 57, 54, 26, 38, 2, 52, 37, 17, 28, 64, 42, 35], 'cur_cost': 109318.0}]
2025-08-01 17:46:11,825 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-01 17:46:11,826 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-08-01 17:46:11,826 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([25, 18, 40,  7, 22,  9, 26, 58,  1,  5,  4, 37, 28, 42, 35, 15, 55,
       12, 61, 64, 62, 21, 63, 48, 13, 16, 39, 45, 53, 46, 43,  6,  0, 20,
       19, 51, 56, 65, 11, 31, 38, 44, 27, 54,  8, 41, 60,  3, 29,  2, 59,
       23, 52, 34, 24, 32, 36, 47, 10, 49, 33, 30, 17, 14, 57, 50],
      dtype=int64), 'cur_cost': 97462.0}
2025-08-01 17:46:11,827 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-01 17:46:11,827 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:11,827 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:11,828 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 98153.0
2025-08-01 17:46:11,908 - ExploitationExpert - INFO - res_population_num: 10
2025-08-01 17:46:11,908 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0, 9521, 9521]
2025-08-01 17:46:11,909 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-01 17:46:11,917 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:11,917 - ExploitationExpert - INFO - populations: [{'tour': array([44,  4, 52, 49, 12,  9, 42, 25,  7, 11, 46,  2,  8, 62, 31, 16, 29,
        6, 41, 39, 58, 65, 21,  0, 40, 10, 23, 33, 35, 18, 26, 27, 57, 37,
       19, 48, 45, 56, 14, 30, 55, 32, 22,  5, 28, 43, 51, 13, 15, 53,  3,
       47, 54, 50, 63, 34, 36,  1, 59, 60, 64, 38, 20, 17, 24, 61],
      dtype=int64), 'cur_cost': 107270.0}, {'tour': array([25, 18, 40,  7, 22,  9, 26, 58,  1,  5,  4, 37, 28, 42, 35, 15, 55,
       12, 61, 64, 62, 21, 63, 48, 13, 16, 39, 45, 53, 46, 43,  6,  0, 20,
       19, 51, 56, 65, 11, 31, 38, 44, 27, 54,  8, 41, 60,  3, 29,  2, 59,
       23, 52, 34, 24, 32, 36, 47, 10, 49, 33, 30, 17, 14, 57, 50],
      dtype=int64), 'cur_cost': 97462.0}, {'tour': array([50, 11, 10, 18,  7, 59, 13, 30, 54, 61, 51,  9, 39, 33, 23, 36, 25,
       32, 52,  8,  5, 48, 19,  1, 17,  2, 27, 14, 22, 55, 20, 57, 43, 24,
       12, 45,  3, 16, 44, 64,  4, 49,  0, 58, 60, 28, 34, 26, 38, 40, 42,
       21, 65, 53,  6, 31, 63, 56, 47, 35, 46, 41, 37, 29, 15, 62],
      dtype=int64), 'cur_cost': 98153.0}, {'tour': [10, 44, 41, 1, 5, 7, 48, 33, 8, 64, 42, 32, 65, 3, 23, 52, 61, 34, 53, 0, 11, 29, 15, 19, 57, 14, 31, 60, 46, 22, 13, 27, 16, 59, 30, 6, 35, 39, 40, 9, 49, 25, 38, 18, 4, 12, 26, 36, 24, 37, 2, 21, 51, 20, 62, 43, 17, 63, 58, 55, 50, 45, 47, 28, 54, 56], 'cur_cost': 99089.0}, {'tour': [64, 6, 33, 46, 11, 15, 13, 21, 4, 57, 17, 42, 22, 23, 35, 3, 28, 14, 2, 34, 43, 47, 31, 5, 58, 24, 29, 38, 49, 1, 50, 63, 7, 59, 56, 55, 61, 62, 44, 60, 48, 52, 39, 16, 65, 41, 10, 36, 8, 40, 0, 51, 25, 12, 20, 32, 37, 19, 53, 30, 26, 18, 54, 27, 45, 9], 'cur_cost': 105593.0}, {'tour': [27, 59, 58, 10, 55, 63, 54, 5, 46, 16, 57, 20, 2, 24, 23, 12, 42, 40, 30, 56, 18, 51, 21, 36, 17, 33, 37, 26, 45, 39, 9, 48, 38, 49, 3, 62, 6, 35, 25, 1, 32, 53, 4, 64, 41, 15, 8, 65, 0, 28, 29, 50, 47, 13, 7, 11, 14, 60, 52, 43, 19, 34, 31, 22, 61, 44], 'cur_cost': 87456.0}, {'tour': [39, 10, 28, 40, 50, 25, 8, 59, 7, 51, 60, 27, 19, 64, 44, 38, 43, 24, 47, 14, 22, 56, 12, 45, 37, 4, 2, 18, 20, 34, 1, 48, 41, 5, 9, 62, 58, 26, 63, 23, 54, 16, 0, 30, 36, 49, 53, 46, 21, 29, 13, 6, 32, 11, 3, 65, 61, 55, 33, 31, 35, 17, 42, 15, 57, 52], 'cur_cost': 98224.0}, {'tour': [56, 53, 19, 16, 21, 50, 49, 52, 58, 1, 59, 22, 32, 51, 25, 26, 39, 24, 55, 12, 4, 37, 18, 34, 41, 54, 40, 8, 64, 0, 11, 47, 5, 9, 65, 7, 3, 44, 31, 2, 33, 48, 23, 10, 63, 15, 60, 42, 29, 38, 45, 61, 62, 43, 35, 57, 27, 30, 13, 14, 20, 6, 36, 46, 28, 17], 'cur_cost': 101381.0}, {'tour': [57, 20, 16, 50, 21, 11, 3, 38, 42, 58, 2, 17, 31, 33, 27, 55, 64, 4, 15, 5, 23, 24, 13, 51, 9, 8, 53, 30, 48, 47, 12, 63, 0, 32, 28, 1, 49, 18, 44, 25, 62, 14, 29, 52, 43, 65, 39, 46, 61, 26, 54, 36, 45, 56, 22, 35, 6, 40, 7, 59, 34, 37, 60, 10, 19, 41], 'cur_cost': 108923.0}, {'tour': [12, 6, 62, 19, 39, 46, 44, 30, 45, 63, 16, 33, 58, 15, 29, 41, 24, 36, 0, 14, 20, 32, 4, 56, 31, 60, 23, 21, 53, 3, 8, 34, 11, 50, 59, 9, 13, 22, 47, 40, 25, 65, 5, 7, 51, 43, 61, 18, 10, 27, 48, 1, 55, 49, 57, 54, 26, 38, 2, 52, 37, 17, 28, 64, 42, 35], 'cur_cost': 109318.0}]
2025-08-01 17:46:11,921 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-01 17:46:11,922 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-08-01 17:46:11,922 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([50, 11, 10, 18,  7, 59, 13, 30, 54, 61, 51,  9, 39, 33, 23, 36, 25,
       32, 52,  8,  5, 48, 19,  1, 17,  2, 27, 14, 22, 55, 20, 57, 43, 24,
       12, 45,  3, 16, 44, 64,  4, 49,  0, 58, 60, 28, 34, 26, 38, 40, 42,
       21, 65, 53,  6, 31, 63, 56, 47, 35, 46, 41, 37, 29, 15, 62],
      dtype=int64), 'cur_cost': 98153.0}
2025-08-01 17:46:11,923 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-01 17:46:11,923 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:11,924 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:11,924 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 106763.0
2025-08-01 17:46:12,025 - ExploitationExpert - INFO - res_population_num: 10
2025-08-01 17:46:12,025 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0, 9521, 9521]
2025-08-01 17:46:12,026 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-01 17:46:12,035 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:12,035 - ExploitationExpert - INFO - populations: [{'tour': array([44,  4, 52, 49, 12,  9, 42, 25,  7, 11, 46,  2,  8, 62, 31, 16, 29,
        6, 41, 39, 58, 65, 21,  0, 40, 10, 23, 33, 35, 18, 26, 27, 57, 37,
       19, 48, 45, 56, 14, 30, 55, 32, 22,  5, 28, 43, 51, 13, 15, 53,  3,
       47, 54, 50, 63, 34, 36,  1, 59, 60, 64, 38, 20, 17, 24, 61],
      dtype=int64), 'cur_cost': 107270.0}, {'tour': array([25, 18, 40,  7, 22,  9, 26, 58,  1,  5,  4, 37, 28, 42, 35, 15, 55,
       12, 61, 64, 62, 21, 63, 48, 13, 16, 39, 45, 53, 46, 43,  6,  0, 20,
       19, 51, 56, 65, 11, 31, 38, 44, 27, 54,  8, 41, 60,  3, 29,  2, 59,
       23, 52, 34, 24, 32, 36, 47, 10, 49, 33, 30, 17, 14, 57, 50],
      dtype=int64), 'cur_cost': 97462.0}, {'tour': array([50, 11, 10, 18,  7, 59, 13, 30, 54, 61, 51,  9, 39, 33, 23, 36, 25,
       32, 52,  8,  5, 48, 19,  1, 17,  2, 27, 14, 22, 55, 20, 57, 43, 24,
       12, 45,  3, 16, 44, 64,  4, 49,  0, 58, 60, 28, 34, 26, 38, 40, 42,
       21, 65, 53,  6, 31, 63, 56, 47, 35, 46, 41, 37, 29, 15, 62],
      dtype=int64), 'cur_cost': 98153.0}, {'tour': array([64, 25, 32, 37, 34, 24, 21, 14, 35,  5,  6, 13, 28,  9,  0, 43, 45,
       61, 56, 22, 60, 41, 33, 65, 16, 30, 48, 23,  8, 19, 54, 47, 29,  1,
       36, 57, 55, 59, 27, 42, 11, 26, 46, 17,  4, 39, 53, 44, 18, 63, 62,
       12, 40, 58, 15, 52, 49,  7, 50, 51, 20, 38,  2,  3, 10, 31],
      dtype=int64), 'cur_cost': 106763.0}, {'tour': [64, 6, 33, 46, 11, 15, 13, 21, 4, 57, 17, 42, 22, 23, 35, 3, 28, 14, 2, 34, 43, 47, 31, 5, 58, 24, 29, 38, 49, 1, 50, 63, 7, 59, 56, 55, 61, 62, 44, 60, 48, 52, 39, 16, 65, 41, 10, 36, 8, 40, 0, 51, 25, 12, 20, 32, 37, 19, 53, 30, 26, 18, 54, 27, 45, 9], 'cur_cost': 105593.0}, {'tour': [27, 59, 58, 10, 55, 63, 54, 5, 46, 16, 57, 20, 2, 24, 23, 12, 42, 40, 30, 56, 18, 51, 21, 36, 17, 33, 37, 26, 45, 39, 9, 48, 38, 49, 3, 62, 6, 35, 25, 1, 32, 53, 4, 64, 41, 15, 8, 65, 0, 28, 29, 50, 47, 13, 7, 11, 14, 60, 52, 43, 19, 34, 31, 22, 61, 44], 'cur_cost': 87456.0}, {'tour': [39, 10, 28, 40, 50, 25, 8, 59, 7, 51, 60, 27, 19, 64, 44, 38, 43, 24, 47, 14, 22, 56, 12, 45, 37, 4, 2, 18, 20, 34, 1, 48, 41, 5, 9, 62, 58, 26, 63, 23, 54, 16, 0, 30, 36, 49, 53, 46, 21, 29, 13, 6, 32, 11, 3, 65, 61, 55, 33, 31, 35, 17, 42, 15, 57, 52], 'cur_cost': 98224.0}, {'tour': [56, 53, 19, 16, 21, 50, 49, 52, 58, 1, 59, 22, 32, 51, 25, 26, 39, 24, 55, 12, 4, 37, 18, 34, 41, 54, 40, 8, 64, 0, 11, 47, 5, 9, 65, 7, 3, 44, 31, 2, 33, 48, 23, 10, 63, 15, 60, 42, 29, 38, 45, 61, 62, 43, 35, 57, 27, 30, 13, 14, 20, 6, 36, 46, 28, 17], 'cur_cost': 101381.0}, {'tour': [57, 20, 16, 50, 21, 11, 3, 38, 42, 58, 2, 17, 31, 33, 27, 55, 64, 4, 15, 5, 23, 24, 13, 51, 9, 8, 53, 30, 48, 47, 12, 63, 0, 32, 28, 1, 49, 18, 44, 25, 62, 14, 29, 52, 43, 65, 39, 46, 61, 26, 54, 36, 45, 56, 22, 35, 6, 40, 7, 59, 34, 37, 60, 10, 19, 41], 'cur_cost': 108923.0}, {'tour': [12, 6, 62, 19, 39, 46, 44, 30, 45, 63, 16, 33, 58, 15, 29, 41, 24, 36, 0, 14, 20, 32, 4, 56, 31, 60, 23, 21, 53, 3, 8, 34, 11, 50, 59, 9, 13, 22, 47, 40, 25, 65, 5, 7, 51, 43, 61, 18, 10, 27, 48, 1, 55, 49, 57, 54, 26, 38, 2, 52, 37, 17, 28, 64, 42, 35], 'cur_cost': 109318.0}]
2025-08-01 17:46:12,044 - ExploitationExpert - INFO - 局部搜索耗时: 0.12秒
2025-08-01 17:46:12,045 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 14, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 14, 'cache_hits': 0, 'similarity_calculations': 91, 'cache_hit_rate': 0.0, 'cache_size': 91}}
2025-08-01 17:46:12,046 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([64, 25, 32, 37, 34, 24, 21, 14, 35,  5,  6, 13, 28,  9,  0, 43, 45,
       61, 56, 22, 60, 41, 33, 65, 16, 30, 48, 23,  8, 19, 54, 47, 29,  1,
       36, 57, 55, 59, 27, 42, 11, 26, 46, 17,  4, 39, 53, 44, 18, 63, 62,
       12, 40, 58, 15, 52, 49,  7, 50, 51, 20, 38,  2,  3, 10, 31],
      dtype=int64), 'cur_cost': 106763.0}
2025-08-01 17:46:12,047 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-01 17:46:12,048 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:12,048 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:12,048 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 103841.0
2025-08-01 17:46:12,126 - ExploitationExpert - INFO - res_population_num: 10
2025-08-01 17:46:12,126 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0, 9521, 9521]
2025-08-01 17:46:12,126 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-01 17:46:12,133 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:12,134 - ExploitationExpert - INFO - populations: [{'tour': array([44,  4, 52, 49, 12,  9, 42, 25,  7, 11, 46,  2,  8, 62, 31, 16, 29,
        6, 41, 39, 58, 65, 21,  0, 40, 10, 23, 33, 35, 18, 26, 27, 57, 37,
       19, 48, 45, 56, 14, 30, 55, 32, 22,  5, 28, 43, 51, 13, 15, 53,  3,
       47, 54, 50, 63, 34, 36,  1, 59, 60, 64, 38, 20, 17, 24, 61],
      dtype=int64), 'cur_cost': 107270.0}, {'tour': array([25, 18, 40,  7, 22,  9, 26, 58,  1,  5,  4, 37, 28, 42, 35, 15, 55,
       12, 61, 64, 62, 21, 63, 48, 13, 16, 39, 45, 53, 46, 43,  6,  0, 20,
       19, 51, 56, 65, 11, 31, 38, 44, 27, 54,  8, 41, 60,  3, 29,  2, 59,
       23, 52, 34, 24, 32, 36, 47, 10, 49, 33, 30, 17, 14, 57, 50],
      dtype=int64), 'cur_cost': 97462.0}, {'tour': array([50, 11, 10, 18,  7, 59, 13, 30, 54, 61, 51,  9, 39, 33, 23, 36, 25,
       32, 52,  8,  5, 48, 19,  1, 17,  2, 27, 14, 22, 55, 20, 57, 43, 24,
       12, 45,  3, 16, 44, 64,  4, 49,  0, 58, 60, 28, 34, 26, 38, 40, 42,
       21, 65, 53,  6, 31, 63, 56, 47, 35, 46, 41, 37, 29, 15, 62],
      dtype=int64), 'cur_cost': 98153.0}, {'tour': array([64, 25, 32, 37, 34, 24, 21, 14, 35,  5,  6, 13, 28,  9,  0, 43, 45,
       61, 56, 22, 60, 41, 33, 65, 16, 30, 48, 23,  8, 19, 54, 47, 29,  1,
       36, 57, 55, 59, 27, 42, 11, 26, 46, 17,  4, 39, 53, 44, 18, 63, 62,
       12, 40, 58, 15, 52, 49,  7, 50, 51, 20, 38,  2,  3, 10, 31],
      dtype=int64), 'cur_cost': 106763.0}, {'tour': array([64, 24, 51,  4, 46,  2, 49, 43, 31, 30, 29, 34, 48, 36, 40, 10, 13,
       50, 47, 65, 63, 28, 15, 39, 62, 56, 25, 11,  6, 44,  3, 12,  7, 59,
       20, 23, 18, 58, 14, 52,  5, 60,  1, 22, 32, 38, 55, 42, 16, 57, 17,
       41, 27, 45, 35, 26,  0, 19, 61,  9, 53, 54, 21,  8, 37, 33],
      dtype=int64), 'cur_cost': 103841.0}, {'tour': [27, 59, 58, 10, 55, 63, 54, 5, 46, 16, 57, 20, 2, 24, 23, 12, 42, 40, 30, 56, 18, 51, 21, 36, 17, 33, 37, 26, 45, 39, 9, 48, 38, 49, 3, 62, 6, 35, 25, 1, 32, 53, 4, 64, 41, 15, 8, 65, 0, 28, 29, 50, 47, 13, 7, 11, 14, 60, 52, 43, 19, 34, 31, 22, 61, 44], 'cur_cost': 87456.0}, {'tour': [39, 10, 28, 40, 50, 25, 8, 59, 7, 51, 60, 27, 19, 64, 44, 38, 43, 24, 47, 14, 22, 56, 12, 45, 37, 4, 2, 18, 20, 34, 1, 48, 41, 5, 9, 62, 58, 26, 63, 23, 54, 16, 0, 30, 36, 49, 53, 46, 21, 29, 13, 6, 32, 11, 3, 65, 61, 55, 33, 31, 35, 17, 42, 15, 57, 52], 'cur_cost': 98224.0}, {'tour': [56, 53, 19, 16, 21, 50, 49, 52, 58, 1, 59, 22, 32, 51, 25, 26, 39, 24, 55, 12, 4, 37, 18, 34, 41, 54, 40, 8, 64, 0, 11, 47, 5, 9, 65, 7, 3, 44, 31, 2, 33, 48, 23, 10, 63, 15, 60, 42, 29, 38, 45, 61, 62, 43, 35, 57, 27, 30, 13, 14, 20, 6, 36, 46, 28, 17], 'cur_cost': 101381.0}, {'tour': [57, 20, 16, 50, 21, 11, 3, 38, 42, 58, 2, 17, 31, 33, 27, 55, 64, 4, 15, 5, 23, 24, 13, 51, 9, 8, 53, 30, 48, 47, 12, 63, 0, 32, 28, 1, 49, 18, 44, 25, 62, 14, 29, 52, 43, 65, 39, 46, 61, 26, 54, 36, 45, 56, 22, 35, 6, 40, 7, 59, 34, 37, 60, 10, 19, 41], 'cur_cost': 108923.0}, {'tour': [12, 6, 62, 19, 39, 46, 44, 30, 45, 63, 16, 33, 58, 15, 29, 41, 24, 36, 0, 14, 20, 32, 4, 56, 31, 60, 23, 21, 53, 3, 8, 34, 11, 50, 59, 9, 13, 22, 47, 40, 25, 65, 5, 7, 51, 43, 61, 18, 10, 27, 48, 1, 55, 49, 57, 54, 26, 38, 2, 52, 37, 17, 28, 64, 42, 35], 'cur_cost': 109318.0}]
2025-08-01 17:46:12,142 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-01 17:46:12,143 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 15, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 15, 'cache_hits': 0, 'similarity_calculations': 105, 'cache_hit_rate': 0.0, 'cache_size': 105}}
2025-08-01 17:46:12,144 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([64, 24, 51,  4, 46,  2, 49, 43, 31, 30, 29, 34, 48, 36, 40, 10, 13,
       50, 47, 65, 63, 28, 15, 39, 62, 56, 25, 11,  6, 44,  3, 12,  7, 59,
       20, 23, 18, 58, 14, 52,  5, 60,  1, 22, 32, 38, 55, 42, 16, 57, 17,
       41, 27, 45, 35, 26,  0, 19, 61,  9, 53, 54, 21,  8, 37, 33],
      dtype=int64), 'cur_cost': 103841.0}
2025-08-01 17:46:12,145 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-01 17:46:12,146 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:12,146 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:12,147 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 94629.0
2025-08-01 17:46:12,257 - ExploitationExpert - INFO - res_population_num: 10
2025-08-01 17:46:12,257 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0, 9521, 9521]
2025-08-01 17:46:12,258 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-01 17:46:12,267 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:12,267 - ExploitationExpert - INFO - populations: [{'tour': array([44,  4, 52, 49, 12,  9, 42, 25,  7, 11, 46,  2,  8, 62, 31, 16, 29,
        6, 41, 39, 58, 65, 21,  0, 40, 10, 23, 33, 35, 18, 26, 27, 57, 37,
       19, 48, 45, 56, 14, 30, 55, 32, 22,  5, 28, 43, 51, 13, 15, 53,  3,
       47, 54, 50, 63, 34, 36,  1, 59, 60, 64, 38, 20, 17, 24, 61],
      dtype=int64), 'cur_cost': 107270.0}, {'tour': array([25, 18, 40,  7, 22,  9, 26, 58,  1,  5,  4, 37, 28, 42, 35, 15, 55,
       12, 61, 64, 62, 21, 63, 48, 13, 16, 39, 45, 53, 46, 43,  6,  0, 20,
       19, 51, 56, 65, 11, 31, 38, 44, 27, 54,  8, 41, 60,  3, 29,  2, 59,
       23, 52, 34, 24, 32, 36, 47, 10, 49, 33, 30, 17, 14, 57, 50],
      dtype=int64), 'cur_cost': 97462.0}, {'tour': array([50, 11, 10, 18,  7, 59, 13, 30, 54, 61, 51,  9, 39, 33, 23, 36, 25,
       32, 52,  8,  5, 48, 19,  1, 17,  2, 27, 14, 22, 55, 20, 57, 43, 24,
       12, 45,  3, 16, 44, 64,  4, 49,  0, 58, 60, 28, 34, 26, 38, 40, 42,
       21, 65, 53,  6, 31, 63, 56, 47, 35, 46, 41, 37, 29, 15, 62],
      dtype=int64), 'cur_cost': 98153.0}, {'tour': array([64, 25, 32, 37, 34, 24, 21, 14, 35,  5,  6, 13, 28,  9,  0, 43, 45,
       61, 56, 22, 60, 41, 33, 65, 16, 30, 48, 23,  8, 19, 54, 47, 29,  1,
       36, 57, 55, 59, 27, 42, 11, 26, 46, 17,  4, 39, 53, 44, 18, 63, 62,
       12, 40, 58, 15, 52, 49,  7, 50, 51, 20, 38,  2,  3, 10, 31],
      dtype=int64), 'cur_cost': 106763.0}, {'tour': array([64, 24, 51,  4, 46,  2, 49, 43, 31, 30, 29, 34, 48, 36, 40, 10, 13,
       50, 47, 65, 63, 28, 15, 39, 62, 56, 25, 11,  6, 44,  3, 12,  7, 59,
       20, 23, 18, 58, 14, 52,  5, 60,  1, 22, 32, 38, 55, 42, 16, 57, 17,
       41, 27, 45, 35, 26,  0, 19, 61,  9, 53, 54, 21,  8, 37, 33],
      dtype=int64), 'cur_cost': 103841.0}, {'tour': array([ 4, 28, 52, 59, 55, 61, 60, 26, 42,  1, 36, 63,  6, 27, 37, 64, 44,
       14, 65, 53, 38, 51, 46, 29, 19, 11, 48, 21, 10, 33,  9, 18,  3, 57,
        5, 22, 25, 31, 30,  7, 54,  8, 39, 24, 13, 12, 35, 45,  2, 50, 43,
       23, 41, 34, 62, 47, 49, 17, 15, 20, 16, 32, 58, 56,  0, 40],
      dtype=int64), 'cur_cost': 94629.0}, {'tour': [39, 10, 28, 40, 50, 25, 8, 59, 7, 51, 60, 27, 19, 64, 44, 38, 43, 24, 47, 14, 22, 56, 12, 45, 37, 4, 2, 18, 20, 34, 1, 48, 41, 5, 9, 62, 58, 26, 63, 23, 54, 16, 0, 30, 36, 49, 53, 46, 21, 29, 13, 6, 32, 11, 3, 65, 61, 55, 33, 31, 35, 17, 42, 15, 57, 52], 'cur_cost': 98224.0}, {'tour': [56, 53, 19, 16, 21, 50, 49, 52, 58, 1, 59, 22, 32, 51, 25, 26, 39, 24, 55, 12, 4, 37, 18, 34, 41, 54, 40, 8, 64, 0, 11, 47, 5, 9, 65, 7, 3, 44, 31, 2, 33, 48, 23, 10, 63, 15, 60, 42, 29, 38, 45, 61, 62, 43, 35, 57, 27, 30, 13, 14, 20, 6, 36, 46, 28, 17], 'cur_cost': 101381.0}, {'tour': [57, 20, 16, 50, 21, 11, 3, 38, 42, 58, 2, 17, 31, 33, 27, 55, 64, 4, 15, 5, 23, 24, 13, 51, 9, 8, 53, 30, 48, 47, 12, 63, 0, 32, 28, 1, 49, 18, 44, 25, 62, 14, 29, 52, 43, 65, 39, 46, 61, 26, 54, 36, 45, 56, 22, 35, 6, 40, 7, 59, 34, 37, 60, 10, 19, 41], 'cur_cost': 108923.0}, {'tour': [12, 6, 62, 19, 39, 46, 44, 30, 45, 63, 16, 33, 58, 15, 29, 41, 24, 36, 0, 14, 20, 32, 4, 56, 31, 60, 23, 21, 53, 3, 8, 34, 11, 50, 59, 9, 13, 22, 47, 40, 25, 65, 5, 7, 51, 43, 61, 18, 10, 27, 48, 1, 55, 49, 57, 54, 26, 38, 2, 52, 37, 17, 28, 64, 42, 35], 'cur_cost': 109318.0}]
2025-08-01 17:46:12,280 - ExploitationExpert - INFO - 局部搜索耗时: 0.13秒
2025-08-01 17:46:12,281 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 16, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 16, 'cache_hits': 0, 'similarity_calculations': 120, 'cache_hit_rate': 0.0, 'cache_size': 120}}
2025-08-01 17:46:12,283 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([ 4, 28, 52, 59, 55, 61, 60, 26, 42,  1, 36, 63,  6, 27, 37, 64, 44,
       14, 65, 53, 38, 51, 46, 29, 19, 11, 48, 21, 10, 33,  9, 18,  3, 57,
        5, 22, 25, 31, 30,  7, 54,  8, 39, 24, 13, 12, 35, 45,  2, 50, 43,
       23, 41, 34, 62, 47, 49, 17, 15, 20, 16, 32, 58, 56,  0, 40],
      dtype=int64), 'cur_cost': 94629.0}
2025-08-01 17:46:12,284 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-01 17:46:12,285 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:12,286 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:12,287 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 113911.0
2025-08-01 17:46:12,391 - ExploitationExpert - INFO - res_population_num: 10
2025-08-01 17:46:12,392 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0, 9521, 9521]
2025-08-01 17:46:12,392 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-01 17:46:12,402 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:12,402 - ExploitationExpert - INFO - populations: [{'tour': array([44,  4, 52, 49, 12,  9, 42, 25,  7, 11, 46,  2,  8, 62, 31, 16, 29,
        6, 41, 39, 58, 65, 21,  0, 40, 10, 23, 33, 35, 18, 26, 27, 57, 37,
       19, 48, 45, 56, 14, 30, 55, 32, 22,  5, 28, 43, 51, 13, 15, 53,  3,
       47, 54, 50, 63, 34, 36,  1, 59, 60, 64, 38, 20, 17, 24, 61],
      dtype=int64), 'cur_cost': 107270.0}, {'tour': array([25, 18, 40,  7, 22,  9, 26, 58,  1,  5,  4, 37, 28, 42, 35, 15, 55,
       12, 61, 64, 62, 21, 63, 48, 13, 16, 39, 45, 53, 46, 43,  6,  0, 20,
       19, 51, 56, 65, 11, 31, 38, 44, 27, 54,  8, 41, 60,  3, 29,  2, 59,
       23, 52, 34, 24, 32, 36, 47, 10, 49, 33, 30, 17, 14, 57, 50],
      dtype=int64), 'cur_cost': 97462.0}, {'tour': array([50, 11, 10, 18,  7, 59, 13, 30, 54, 61, 51,  9, 39, 33, 23, 36, 25,
       32, 52,  8,  5, 48, 19,  1, 17,  2, 27, 14, 22, 55, 20, 57, 43, 24,
       12, 45,  3, 16, 44, 64,  4, 49,  0, 58, 60, 28, 34, 26, 38, 40, 42,
       21, 65, 53,  6, 31, 63, 56, 47, 35, 46, 41, 37, 29, 15, 62],
      dtype=int64), 'cur_cost': 98153.0}, {'tour': array([64, 25, 32, 37, 34, 24, 21, 14, 35,  5,  6, 13, 28,  9,  0, 43, 45,
       61, 56, 22, 60, 41, 33, 65, 16, 30, 48, 23,  8, 19, 54, 47, 29,  1,
       36, 57, 55, 59, 27, 42, 11, 26, 46, 17,  4, 39, 53, 44, 18, 63, 62,
       12, 40, 58, 15, 52, 49,  7, 50, 51, 20, 38,  2,  3, 10, 31],
      dtype=int64), 'cur_cost': 106763.0}, {'tour': array([64, 24, 51,  4, 46,  2, 49, 43, 31, 30, 29, 34, 48, 36, 40, 10, 13,
       50, 47, 65, 63, 28, 15, 39, 62, 56, 25, 11,  6, 44,  3, 12,  7, 59,
       20, 23, 18, 58, 14, 52,  5, 60,  1, 22, 32, 38, 55, 42, 16, 57, 17,
       41, 27, 45, 35, 26,  0, 19, 61,  9, 53, 54, 21,  8, 37, 33],
      dtype=int64), 'cur_cost': 103841.0}, {'tour': array([ 4, 28, 52, 59, 55, 61, 60, 26, 42,  1, 36, 63,  6, 27, 37, 64, 44,
       14, 65, 53, 38, 51, 46, 29, 19, 11, 48, 21, 10, 33,  9, 18,  3, 57,
        5, 22, 25, 31, 30,  7, 54,  8, 39, 24, 13, 12, 35, 45,  2, 50, 43,
       23, 41, 34, 62, 47, 49, 17, 15, 20, 16, 32, 58, 56,  0, 40],
      dtype=int64), 'cur_cost': 94629.0}, {'tour': array([36, 20, 32, 47, 64, 16, 25, 15, 34, 10, 60, 42, 33, 51, 37, 46, 24,
       22, 52, 63, 44, 45,  8,  7, 54, 65, 48, 43,  2, 27, 55, 35, 39, 59,
       13,  6, 61,  5,  0, 53, 17, 40, 28, 19, 18, 57, 26, 41,  1, 31, 12,
       21, 62,  3, 29, 11, 23, 50,  9, 30, 58, 49,  4, 14, 38, 56],
      dtype=int64), 'cur_cost': 113911.0}, {'tour': [56, 53, 19, 16, 21, 50, 49, 52, 58, 1, 59, 22, 32, 51, 25, 26, 39, 24, 55, 12, 4, 37, 18, 34, 41, 54, 40, 8, 64, 0, 11, 47, 5, 9, 65, 7, 3, 44, 31, 2, 33, 48, 23, 10, 63, 15, 60, 42, 29, 38, 45, 61, 62, 43, 35, 57, 27, 30, 13, 14, 20, 6, 36, 46, 28, 17], 'cur_cost': 101381.0}, {'tour': [57, 20, 16, 50, 21, 11, 3, 38, 42, 58, 2, 17, 31, 33, 27, 55, 64, 4, 15, 5, 23, 24, 13, 51, 9, 8, 53, 30, 48, 47, 12, 63, 0, 32, 28, 1, 49, 18, 44, 25, 62, 14, 29, 52, 43, 65, 39, 46, 61, 26, 54, 36, 45, 56, 22, 35, 6, 40, 7, 59, 34, 37, 60, 10, 19, 41], 'cur_cost': 108923.0}, {'tour': [12, 6, 62, 19, 39, 46, 44, 30, 45, 63, 16, 33, 58, 15, 29, 41, 24, 36, 0, 14, 20, 32, 4, 56, 31, 60, 23, 21, 53, 3, 8, 34, 11, 50, 59, 9, 13, 22, 47, 40, 25, 65, 5, 7, 51, 43, 61, 18, 10, 27, 48, 1, 55, 49, 57, 54, 26, 38, 2, 52, 37, 17, 28, 64, 42, 35], 'cur_cost': 109318.0}]
2025-08-01 17:46:12,413 - ExploitationExpert - INFO - 局部搜索耗时: 0.13秒
2025-08-01 17:46:12,414 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 17, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 17, 'cache_hits': 0, 'similarity_calculations': 136, 'cache_hit_rate': 0.0, 'cache_size': 136}}
2025-08-01 17:46:12,415 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([36, 20, 32, 47, 64, 16, 25, 15, 34, 10, 60, 42, 33, 51, 37, 46, 24,
       22, 52, 63, 44, 45,  8,  7, 54, 65, 48, 43,  2, 27, 55, 35, 39, 59,
       13,  6, 61,  5,  0, 53, 17, 40, 28, 19, 18, 57, 26, 41,  1, 31, 12,
       21, 62,  3, 29, 11, 23, 50,  9, 30, 58, 49,  4, 14, 38, 56],
      dtype=int64), 'cur_cost': 113911.0}
2025-08-01 17:46:12,416 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-01 17:46:12,416 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:12,416 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:12,417 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 107316.0
2025-08-01 17:46:12,490 - ExploitationExpert - INFO - res_population_num: 10
2025-08-01 17:46:12,490 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0, 9521, 9521]
2025-08-01 17:46:12,491 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-01 17:46:12,499 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:12,500 - ExploitationExpert - INFO - populations: [{'tour': array([44,  4, 52, 49, 12,  9, 42, 25,  7, 11, 46,  2,  8, 62, 31, 16, 29,
        6, 41, 39, 58, 65, 21,  0, 40, 10, 23, 33, 35, 18, 26, 27, 57, 37,
       19, 48, 45, 56, 14, 30, 55, 32, 22,  5, 28, 43, 51, 13, 15, 53,  3,
       47, 54, 50, 63, 34, 36,  1, 59, 60, 64, 38, 20, 17, 24, 61],
      dtype=int64), 'cur_cost': 107270.0}, {'tour': array([25, 18, 40,  7, 22,  9, 26, 58,  1,  5,  4, 37, 28, 42, 35, 15, 55,
       12, 61, 64, 62, 21, 63, 48, 13, 16, 39, 45, 53, 46, 43,  6,  0, 20,
       19, 51, 56, 65, 11, 31, 38, 44, 27, 54,  8, 41, 60,  3, 29,  2, 59,
       23, 52, 34, 24, 32, 36, 47, 10, 49, 33, 30, 17, 14, 57, 50],
      dtype=int64), 'cur_cost': 97462.0}, {'tour': array([50, 11, 10, 18,  7, 59, 13, 30, 54, 61, 51,  9, 39, 33, 23, 36, 25,
       32, 52,  8,  5, 48, 19,  1, 17,  2, 27, 14, 22, 55, 20, 57, 43, 24,
       12, 45,  3, 16, 44, 64,  4, 49,  0, 58, 60, 28, 34, 26, 38, 40, 42,
       21, 65, 53,  6, 31, 63, 56, 47, 35, 46, 41, 37, 29, 15, 62],
      dtype=int64), 'cur_cost': 98153.0}, {'tour': array([64, 25, 32, 37, 34, 24, 21, 14, 35,  5,  6, 13, 28,  9,  0, 43, 45,
       61, 56, 22, 60, 41, 33, 65, 16, 30, 48, 23,  8, 19, 54, 47, 29,  1,
       36, 57, 55, 59, 27, 42, 11, 26, 46, 17,  4, 39, 53, 44, 18, 63, 62,
       12, 40, 58, 15, 52, 49,  7, 50, 51, 20, 38,  2,  3, 10, 31],
      dtype=int64), 'cur_cost': 106763.0}, {'tour': array([64, 24, 51,  4, 46,  2, 49, 43, 31, 30, 29, 34, 48, 36, 40, 10, 13,
       50, 47, 65, 63, 28, 15, 39, 62, 56, 25, 11,  6, 44,  3, 12,  7, 59,
       20, 23, 18, 58, 14, 52,  5, 60,  1, 22, 32, 38, 55, 42, 16, 57, 17,
       41, 27, 45, 35, 26,  0, 19, 61,  9, 53, 54, 21,  8, 37, 33],
      dtype=int64), 'cur_cost': 103841.0}, {'tour': array([ 4, 28, 52, 59, 55, 61, 60, 26, 42,  1, 36, 63,  6, 27, 37, 64, 44,
       14, 65, 53, 38, 51, 46, 29, 19, 11, 48, 21, 10, 33,  9, 18,  3, 57,
        5, 22, 25, 31, 30,  7, 54,  8, 39, 24, 13, 12, 35, 45,  2, 50, 43,
       23, 41, 34, 62, 47, 49, 17, 15, 20, 16, 32, 58, 56,  0, 40],
      dtype=int64), 'cur_cost': 94629.0}, {'tour': array([36, 20, 32, 47, 64, 16, 25, 15, 34, 10, 60, 42, 33, 51, 37, 46, 24,
       22, 52, 63, 44, 45,  8,  7, 54, 65, 48, 43,  2, 27, 55, 35, 39, 59,
       13,  6, 61,  5,  0, 53, 17, 40, 28, 19, 18, 57, 26, 41,  1, 31, 12,
       21, 62,  3, 29, 11, 23, 50,  9, 30, 58, 49,  4, 14, 38, 56],
      dtype=int64), 'cur_cost': 113911.0}, {'tour': array([12, 65, 59, 29,  8, 23,  4, 54, 21,  7,  2, 19, 42, 28, 53, 63, 30,
       34, 51, 11, 52, 25, 27, 35, 22,  5, 18, 31, 61, 33, 13, 14, 62, 55,
       43, 37, 10, 44,  9, 17,  3,  0, 60, 39, 56, 48, 49, 32, 26,  6, 41,
       20,  1, 46, 36, 58, 45, 40, 50, 57, 24, 64, 15, 38, 16, 47],
      dtype=int64), 'cur_cost': 107316.0}, {'tour': [57, 20, 16, 50, 21, 11, 3, 38, 42, 58, 2, 17, 31, 33, 27, 55, 64, 4, 15, 5, 23, 24, 13, 51, 9, 8, 53, 30, 48, 47, 12, 63, 0, 32, 28, 1, 49, 18, 44, 25, 62, 14, 29, 52, 43, 65, 39, 46, 61, 26, 54, 36, 45, 56, 22, 35, 6, 40, 7, 59, 34, 37, 60, 10, 19, 41], 'cur_cost': 108923.0}, {'tour': [12, 6, 62, 19, 39, 46, 44, 30, 45, 63, 16, 33, 58, 15, 29, 41, 24, 36, 0, 14, 20, 32, 4, 56, 31, 60, 23, 21, 53, 3, 8, 34, 11, 50, 59, 9, 13, 22, 47, 40, 25, 65, 5, 7, 51, 43, 61, 18, 10, 27, 48, 1, 55, 49, 57, 54, 26, 38, 2, 52, 37, 17, 28, 64, 42, 35], 'cur_cost': 109318.0}]
2025-08-01 17:46:12,510 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-01 17:46:12,511 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 18, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 18, 'cache_hits': 0, 'similarity_calculations': 153, 'cache_hit_rate': 0.0, 'cache_size': 153}}
2025-08-01 17:46:12,511 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([12, 65, 59, 29,  8, 23,  4, 54, 21,  7,  2, 19, 42, 28, 53, 63, 30,
       34, 51, 11, 52, 25, 27, 35, 22,  5, 18, 31, 61, 33, 13, 14, 62, 55,
       43, 37, 10, 44,  9, 17,  3,  0, 60, 39, 56, 48, 49, 32, 26,  6, 41,
       20,  1, 46, 36, 58, 45, 40, 50, 57, 24, 64, 15, 38, 16, 47],
      dtype=int64), 'cur_cost': 107316.0}
2025-08-01 17:46:12,513 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-01 17:46:12,513 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:12,514 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:12,515 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 103809.0
2025-08-01 17:46:12,625 - ExploitationExpert - INFO - res_population_num: 10
2025-08-01 17:46:12,626 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0, 9521, 9521]
2025-08-01 17:46:12,626 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-01 17:46:12,635 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:12,635 - ExploitationExpert - INFO - populations: [{'tour': array([44,  4, 52, 49, 12,  9, 42, 25,  7, 11, 46,  2,  8, 62, 31, 16, 29,
        6, 41, 39, 58, 65, 21,  0, 40, 10, 23, 33, 35, 18, 26, 27, 57, 37,
       19, 48, 45, 56, 14, 30, 55, 32, 22,  5, 28, 43, 51, 13, 15, 53,  3,
       47, 54, 50, 63, 34, 36,  1, 59, 60, 64, 38, 20, 17, 24, 61],
      dtype=int64), 'cur_cost': 107270.0}, {'tour': array([25, 18, 40,  7, 22,  9, 26, 58,  1,  5,  4, 37, 28, 42, 35, 15, 55,
       12, 61, 64, 62, 21, 63, 48, 13, 16, 39, 45, 53, 46, 43,  6,  0, 20,
       19, 51, 56, 65, 11, 31, 38, 44, 27, 54,  8, 41, 60,  3, 29,  2, 59,
       23, 52, 34, 24, 32, 36, 47, 10, 49, 33, 30, 17, 14, 57, 50],
      dtype=int64), 'cur_cost': 97462.0}, {'tour': array([50, 11, 10, 18,  7, 59, 13, 30, 54, 61, 51,  9, 39, 33, 23, 36, 25,
       32, 52,  8,  5, 48, 19,  1, 17,  2, 27, 14, 22, 55, 20, 57, 43, 24,
       12, 45,  3, 16, 44, 64,  4, 49,  0, 58, 60, 28, 34, 26, 38, 40, 42,
       21, 65, 53,  6, 31, 63, 56, 47, 35, 46, 41, 37, 29, 15, 62],
      dtype=int64), 'cur_cost': 98153.0}, {'tour': array([64, 25, 32, 37, 34, 24, 21, 14, 35,  5,  6, 13, 28,  9,  0, 43, 45,
       61, 56, 22, 60, 41, 33, 65, 16, 30, 48, 23,  8, 19, 54, 47, 29,  1,
       36, 57, 55, 59, 27, 42, 11, 26, 46, 17,  4, 39, 53, 44, 18, 63, 62,
       12, 40, 58, 15, 52, 49,  7, 50, 51, 20, 38,  2,  3, 10, 31],
      dtype=int64), 'cur_cost': 106763.0}, {'tour': array([64, 24, 51,  4, 46,  2, 49, 43, 31, 30, 29, 34, 48, 36, 40, 10, 13,
       50, 47, 65, 63, 28, 15, 39, 62, 56, 25, 11,  6, 44,  3, 12,  7, 59,
       20, 23, 18, 58, 14, 52,  5, 60,  1, 22, 32, 38, 55, 42, 16, 57, 17,
       41, 27, 45, 35, 26,  0, 19, 61,  9, 53, 54, 21,  8, 37, 33],
      dtype=int64), 'cur_cost': 103841.0}, {'tour': array([ 4, 28, 52, 59, 55, 61, 60, 26, 42,  1, 36, 63,  6, 27, 37, 64, 44,
       14, 65, 53, 38, 51, 46, 29, 19, 11, 48, 21, 10, 33,  9, 18,  3, 57,
        5, 22, 25, 31, 30,  7, 54,  8, 39, 24, 13, 12, 35, 45,  2, 50, 43,
       23, 41, 34, 62, 47, 49, 17, 15, 20, 16, 32, 58, 56,  0, 40],
      dtype=int64), 'cur_cost': 94629.0}, {'tour': array([36, 20, 32, 47, 64, 16, 25, 15, 34, 10, 60, 42, 33, 51, 37, 46, 24,
       22, 52, 63, 44, 45,  8,  7, 54, 65, 48, 43,  2, 27, 55, 35, 39, 59,
       13,  6, 61,  5,  0, 53, 17, 40, 28, 19, 18, 57, 26, 41,  1, 31, 12,
       21, 62,  3, 29, 11, 23, 50,  9, 30, 58, 49,  4, 14, 38, 56],
      dtype=int64), 'cur_cost': 113911.0}, {'tour': array([12, 65, 59, 29,  8, 23,  4, 54, 21,  7,  2, 19, 42, 28, 53, 63, 30,
       34, 51, 11, 52, 25, 27, 35, 22,  5, 18, 31, 61, 33, 13, 14, 62, 55,
       43, 37, 10, 44,  9, 17,  3,  0, 60, 39, 56, 48, 49, 32, 26,  6, 41,
       20,  1, 46, 36, 58, 45, 40, 50, 57, 24, 64, 15, 38, 16, 47],
      dtype=int64), 'cur_cost': 107316.0}, {'tour': array([43, 46, 32, 56, 54,  5,  4, 17, 34, 51, 50, 20, 12,  0, 29,  6, 63,
       25, 53, 28, 47, 58, 26, 15, 62, 59, 33,  2, 41, 48, 42, 38, 44, 18,
       30, 61, 37,  9, 21,  3, 35, 65, 60, 11, 16,  7, 36, 55, 40, 13,  8,
       10, 45, 52, 24, 57, 19, 23, 31, 27, 39, 14, 49, 64,  1, 22],
      dtype=int64), 'cur_cost': 103809.0}, {'tour': [12, 6, 62, 19, 39, 46, 44, 30, 45, 63, 16, 33, 58, 15, 29, 41, 24, 36, 0, 14, 20, 32, 4, 56, 31, 60, 23, 21, 53, 3, 8, 34, 11, 50, 59, 9, 13, 22, 47, 40, 25, 65, 5, 7, 51, 43, 61, 18, 10, 27, 48, 1, 55, 49, 57, 54, 26, 38, 2, 52, 37, 17, 28, 64, 42, 35], 'cur_cost': 109318.0}]
2025-08-01 17:46:12,650 - ExploitationExpert - INFO - 局部搜索耗时: 0.14秒
2025-08-01 17:46:12,650 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 19, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 19, 'cache_hits': 0, 'similarity_calculations': 171, 'cache_hit_rate': 0.0, 'cache_size': 171}}
2025-08-01 17:46:12,652 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([43, 46, 32, 56, 54,  5,  4, 17, 34, 51, 50, 20, 12,  0, 29,  6, 63,
       25, 53, 28, 47, 58, 26, 15, 62, 59, 33,  2, 41, 48, 42, 38, 44, 18,
       30, 61, 37,  9, 21,  3, 35, 65, 60, 11, 16,  7, 36, 55, 40, 13,  8,
       10, 45, 52, 24, 57, 19, 23, 31, 27, 39, 14, 49, 64,  1, 22],
      dtype=int64), 'cur_cost': 103809.0}
2025-08-01 17:46:12,653 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-01 17:46:12,654 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:12,655 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:12,655 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 114990.0
2025-08-01 17:46:12,762 - ExploitationExpert - INFO - res_population_num: 10
2025-08-01 17:46:12,762 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0, 9521, 9521]
2025-08-01 17:46:12,763 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-01 17:46:12,769 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:12,770 - ExploitationExpert - INFO - populations: [{'tour': array([44,  4, 52, 49, 12,  9, 42, 25,  7, 11, 46,  2,  8, 62, 31, 16, 29,
        6, 41, 39, 58, 65, 21,  0, 40, 10, 23, 33, 35, 18, 26, 27, 57, 37,
       19, 48, 45, 56, 14, 30, 55, 32, 22,  5, 28, 43, 51, 13, 15, 53,  3,
       47, 54, 50, 63, 34, 36,  1, 59, 60, 64, 38, 20, 17, 24, 61],
      dtype=int64), 'cur_cost': 107270.0}, {'tour': array([25, 18, 40,  7, 22,  9, 26, 58,  1,  5,  4, 37, 28, 42, 35, 15, 55,
       12, 61, 64, 62, 21, 63, 48, 13, 16, 39, 45, 53, 46, 43,  6,  0, 20,
       19, 51, 56, 65, 11, 31, 38, 44, 27, 54,  8, 41, 60,  3, 29,  2, 59,
       23, 52, 34, 24, 32, 36, 47, 10, 49, 33, 30, 17, 14, 57, 50],
      dtype=int64), 'cur_cost': 97462.0}, {'tour': array([50, 11, 10, 18,  7, 59, 13, 30, 54, 61, 51,  9, 39, 33, 23, 36, 25,
       32, 52,  8,  5, 48, 19,  1, 17,  2, 27, 14, 22, 55, 20, 57, 43, 24,
       12, 45,  3, 16, 44, 64,  4, 49,  0, 58, 60, 28, 34, 26, 38, 40, 42,
       21, 65, 53,  6, 31, 63, 56, 47, 35, 46, 41, 37, 29, 15, 62],
      dtype=int64), 'cur_cost': 98153.0}, {'tour': array([64, 25, 32, 37, 34, 24, 21, 14, 35,  5,  6, 13, 28,  9,  0, 43, 45,
       61, 56, 22, 60, 41, 33, 65, 16, 30, 48, 23,  8, 19, 54, 47, 29,  1,
       36, 57, 55, 59, 27, 42, 11, 26, 46, 17,  4, 39, 53, 44, 18, 63, 62,
       12, 40, 58, 15, 52, 49,  7, 50, 51, 20, 38,  2,  3, 10, 31],
      dtype=int64), 'cur_cost': 106763.0}, {'tour': array([64, 24, 51,  4, 46,  2, 49, 43, 31, 30, 29, 34, 48, 36, 40, 10, 13,
       50, 47, 65, 63, 28, 15, 39, 62, 56, 25, 11,  6, 44,  3, 12,  7, 59,
       20, 23, 18, 58, 14, 52,  5, 60,  1, 22, 32, 38, 55, 42, 16, 57, 17,
       41, 27, 45, 35, 26,  0, 19, 61,  9, 53, 54, 21,  8, 37, 33],
      dtype=int64), 'cur_cost': 103841.0}, {'tour': array([ 4, 28, 52, 59, 55, 61, 60, 26, 42,  1, 36, 63,  6, 27, 37, 64, 44,
       14, 65, 53, 38, 51, 46, 29, 19, 11, 48, 21, 10, 33,  9, 18,  3, 57,
        5, 22, 25, 31, 30,  7, 54,  8, 39, 24, 13, 12, 35, 45,  2, 50, 43,
       23, 41, 34, 62, 47, 49, 17, 15, 20, 16, 32, 58, 56,  0, 40],
      dtype=int64), 'cur_cost': 94629.0}, {'tour': array([36, 20, 32, 47, 64, 16, 25, 15, 34, 10, 60, 42, 33, 51, 37, 46, 24,
       22, 52, 63, 44, 45,  8,  7, 54, 65, 48, 43,  2, 27, 55, 35, 39, 59,
       13,  6, 61,  5,  0, 53, 17, 40, 28, 19, 18, 57, 26, 41,  1, 31, 12,
       21, 62,  3, 29, 11, 23, 50,  9, 30, 58, 49,  4, 14, 38, 56],
      dtype=int64), 'cur_cost': 113911.0}, {'tour': array([12, 65, 59, 29,  8, 23,  4, 54, 21,  7,  2, 19, 42, 28, 53, 63, 30,
       34, 51, 11, 52, 25, 27, 35, 22,  5, 18, 31, 61, 33, 13, 14, 62, 55,
       43, 37, 10, 44,  9, 17,  3,  0, 60, 39, 56, 48, 49, 32, 26,  6, 41,
       20,  1, 46, 36, 58, 45, 40, 50, 57, 24, 64, 15, 38, 16, 47],
      dtype=int64), 'cur_cost': 107316.0}, {'tour': array([43, 46, 32, 56, 54,  5,  4, 17, 34, 51, 50, 20, 12,  0, 29,  6, 63,
       25, 53, 28, 47, 58, 26, 15, 62, 59, 33,  2, 41, 48, 42, 38, 44, 18,
       30, 61, 37,  9, 21,  3, 35, 65, 60, 11, 16,  7, 36, 55, 40, 13,  8,
       10, 45, 52, 24, 57, 19, 23, 31, 27, 39, 14, 49, 64,  1, 22],
      dtype=int64), 'cur_cost': 103809.0}, {'tour': array([ 5, 37, 59, 20, 33, 25, 60, 51, 26, 16,  1, 40,  3, 34, 48, 55, 62,
       31, 11, 36, 57, 58, 53, 39, 19,  8, 49,  6, 35, 44, 32, 38,  9,  4,
       14, 12, 15, 24, 41, 27, 50, 63, 46, 56, 43, 61,  0, 47, 23, 54,  7,
       28, 29, 17, 65, 52, 30, 10, 13, 18, 42, 64,  2, 45, 22, 21],
      dtype=int64), 'cur_cost': 114990.0}]
2025-08-01 17:46:12,781 - ExploitationExpert - INFO - 局部搜索耗时: 0.13秒
2025-08-01 17:46:12,782 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 20, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 20, 'cache_hits': 0, 'similarity_calculations': 190, 'cache_hit_rate': 0.0, 'cache_size': 190}}
2025-08-01 17:46:12,782 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([ 5, 37, 59, 20, 33, 25, 60, 51, 26, 16,  1, 40,  3, 34, 48, 55, 62,
       31, 11, 36, 57, 58, 53, 39, 19,  8, 49,  6, 35, 44, 32, 38,  9,  4,
       14, 12, 15, 24, 41, 27, 50, 63, 46, 56, 43, 61,  0, 47, 23, 54,  7,
       28, 29, 17, 65, 52, 30, 10, 13, 18, 42, 64,  2, 45, 22, 21],
      dtype=int64), 'cur_cost': 114990.0}
2025-08-01 17:46:12,784 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([44,  4, 52, 49, 12,  9, 42, 25,  7, 11, 46,  2,  8, 62, 31, 16, 29,
        6, 41, 39, 58, 65, 21,  0, 40, 10, 23, 33, 35, 18, 26, 27, 57, 37,
       19, 48, 45, 56, 14, 30, 55, 32, 22,  5, 28, 43, 51, 13, 15, 53,  3,
       47, 54, 50, 63, 34, 36,  1, 59, 60, 64, 38, 20, 17, 24, 61],
      dtype=int64), 'cur_cost': 107270.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([25, 18, 40,  7, 22,  9, 26, 58,  1,  5,  4, 37, 28, 42, 35, 15, 55,
       12, 61, 64, 62, 21, 63, 48, 13, 16, 39, 45, 53, 46, 43,  6,  0, 20,
       19, 51, 56, 65, 11, 31, 38, 44, 27, 54,  8, 41, 60,  3, 29,  2, 59,
       23, 52, 34, 24, 32, 36, 47, 10, 49, 33, 30, 17, 14, 57, 50],
      dtype=int64), 'cur_cost': 97462.0}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([50, 11, 10, 18,  7, 59, 13, 30, 54, 61, 51,  9, 39, 33, 23, 36, 25,
       32, 52,  8,  5, 48, 19,  1, 17,  2, 27, 14, 22, 55, 20, 57, 43, 24,
       12, 45,  3, 16, 44, 64,  4, 49,  0, 58, 60, 28, 34, 26, 38, 40, 42,
       21, 65, 53,  6, 31, 63, 56, 47, 35, 46, 41, 37, 29, 15, 62],
      dtype=int64), 'cur_cost': 98153.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([64, 25, 32, 37, 34, 24, 21, 14, 35,  5,  6, 13, 28,  9,  0, 43, 45,
       61, 56, 22, 60, 41, 33, 65, 16, 30, 48, 23,  8, 19, 54, 47, 29,  1,
       36, 57, 55, 59, 27, 42, 11, 26, 46, 17,  4, 39, 53, 44, 18, 63, 62,
       12, 40, 58, 15, 52, 49,  7, 50, 51, 20, 38,  2,  3, 10, 31],
      dtype=int64), 'cur_cost': 106763.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([64, 24, 51,  4, 46,  2, 49, 43, 31, 30, 29, 34, 48, 36, 40, 10, 13,
       50, 47, 65, 63, 28, 15, 39, 62, 56, 25, 11,  6, 44,  3, 12,  7, 59,
       20, 23, 18, 58, 14, 52,  5, 60,  1, 22, 32, 38, 55, 42, 16, 57, 17,
       41, 27, 45, 35, 26,  0, 19, 61,  9, 53, 54, 21,  8, 37, 33],
      dtype=int64), 'cur_cost': 103841.0}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 4, 28, 52, 59, 55, 61, 60, 26, 42,  1, 36, 63,  6, 27, 37, 64, 44,
       14, 65, 53, 38, 51, 46, 29, 19, 11, 48, 21, 10, 33,  9, 18,  3, 57,
        5, 22, 25, 31, 30,  7, 54,  8, 39, 24, 13, 12, 35, 45,  2, 50, 43,
       23, 41, 34, 62, 47, 49, 17, 15, 20, 16, 32, 58, 56,  0, 40],
      dtype=int64), 'cur_cost': 94629.0}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([36, 20, 32, 47, 64, 16, 25, 15, 34, 10, 60, 42, 33, 51, 37, 46, 24,
       22, 52, 63, 44, 45,  8,  7, 54, 65, 48, 43,  2, 27, 55, 35, 39, 59,
       13,  6, 61,  5,  0, 53, 17, 40, 28, 19, 18, 57, 26, 41,  1, 31, 12,
       21, 62,  3, 29, 11, 23, 50,  9, 30, 58, 49,  4, 14, 38, 56],
      dtype=int64), 'cur_cost': 113911.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([12, 65, 59, 29,  8, 23,  4, 54, 21,  7,  2, 19, 42, 28, 53, 63, 30,
       34, 51, 11, 52, 25, 27, 35, 22,  5, 18, 31, 61, 33, 13, 14, 62, 55,
       43, 37, 10, 44,  9, 17,  3,  0, 60, 39, 56, 48, 49, 32, 26,  6, 41,
       20,  1, 46, 36, 58, 45, 40, 50, 57, 24, 64, 15, 38, 16, 47],
      dtype=int64), 'cur_cost': 107316.0}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([43, 46, 32, 56, 54,  5,  4, 17, 34, 51, 50, 20, 12,  0, 29,  6, 63,
       25, 53, 28, 47, 58, 26, 15, 62, 59, 33,  2, 41, 48, 42, 38, 44, 18,
       30, 61, 37,  9, 21,  3, 35, 65, 60, 11, 16,  7, 36, 55, 40, 13,  8,
       10, 45, 52, 24, 57, 19, 23, 31, 27, 39, 14, 49, 64,  1, 22],
      dtype=int64), 'cur_cost': 103809.0}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 5, 37, 59, 20, 33, 25, 60, 51, 26, 16,  1, 40,  3, 34, 48, 55, 62,
       31, 11, 36, 57, 58, 53, 39, 19,  8, 49,  6, 35, 44, 32, 38,  9,  4,
       14, 12, 15, 24, 41, 27, 50, 63, 46, 56, 43, 61,  0, 47, 23, 54,  7,
       28, 29, 17, 65, 52, 30, 10, 13, 18, 42, 64,  2, 45, 22, 21],
      dtype=int64), 'cur_cost': 114990.0}}]
2025-08-01 17:46:12,788 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-01 17:46:12,788 - StatsExpert - INFO - 开始统计分析
2025-08-01 17:46:12,791 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=94629.000, 多样性=0.986
2025-08-01 17:46:12,792 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-01 17:46:12,792 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-01 17:46:12,792 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-01 17:46:12,793 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.04741422891216359, 'best_improvement': -0.08201838638858397}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.0023850085178876543}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 10, 'new_count': 10, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.7569023569023569, 'new_diversity': 0.7569023569023569, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-01 17:46:12,796 - main - INFO - --- Finished Evolution Iteration 2 ---
2025-08-01 17:46:12,797 - main - INFO - composite13_66 开始进化第 3 代
2025-08-01 17:46:12,798 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-01 17:46:12,799 - StatsExpert - INFO - 开始统计分析
2025-08-01 17:46:12,800 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=94629.000, 多样性=0.986
2025-08-01 17:46:12,800 - PathExpert - INFO - 开始路径结构分析
2025-08-01 17:46:12,806 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.986
2025-08-01 17:46:12,807 - EliteExpert - INFO - 开始精英解分析
2025-08-01 17:46:12,814 - EliteExpert - INFO - 精英解分析完成: 精英解数量=10, 多样性=0.757
2025-08-01 17:46:12,817 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-01 17:46:12,817 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-01 17:46:12,818 - LandscapeExpert - INFO - 添加精英解数据: 10个精英解
2025-08-01 17:46:12,818 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-01 17:46:12,892 - core.algorithms.fitness_landscape_analyzer - ERROR - 景观分析失败: '>' not supported between instances of 'str' and 'int'
2025-08-01 17:46:12,893 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-01 17:46:12,894 - LandscapeExpert - INFO - 提取到 10 个精英解
2025-08-01 17:46:12,902 - visualization.landscape_visualizer - INFO - 已添加 10 个精英解标记
2025-08-01 17:46:12,999 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_3_20250801_174612.html
2025-08-01 17:46:13,061 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_3_20250801_174612.html
2025-08-01 17:46:13,062 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 3
2025-08-01 17:46:13,062 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-01 17:46:13,062 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2450秒
2025-08-01 17:46:13,063 - main - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754041572.893964, 'performance_metrics': {}}}
2025-08-01 17:46:13,064 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-01 17:46:13,064 - StrategyExpert - INFO - 开始策略分配分析
2025-08-01 17:46:13,065 - StrategyExpert - INFO - 消融实验模式：使用纯exploit策略
2025-08-01 17:46:13,065 - StrategyExpert - INFO - 策略分配分析完成（消融实验模式）
2025-08-01 17:46:13,065 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-08-01 17:46:13,066 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 消融实验模式：所有10个个体都使用exploit策略
2025-08-01 17:46:13,066 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-01 17:46:13,066 - main - INFO - 策略分配: ['exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-08-01 17:46:13,066 - main - INFO - 策略分配完整报告: 消融实验模式：所有10个个体都使用exploit策略
2025-08-01 17:46:13,067 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-01 17:46:13,067 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-01 17:46:13,067 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:13,068 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:13,068 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 100033.0
2025-08-01 17:46:13,158 - ExploitationExpert - INFO - res_population_num: 11
2025-08-01 17:46:13,159 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0, 9521]
2025-08-01 17:46:13,159 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-01 17:46:13,168 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:13,169 - ExploitationExpert - INFO - populations: [{'tour': array([40, 62, 23,  6, 43, 46, 15,  9, 13, 32, 59,  5, 53, 17, 63, 44, 45,
       28, 35, 57, 19, 55, 41,  1,  2, 47, 31, 29,  8, 39, 27, 52, 58, 22,
       34, 10, 18, 12, 24, 36,  4, 14,  7, 26, 25, 33, 20, 37, 61, 48, 30,
       50,  0, 51, 54, 60, 56, 11, 65, 64,  3, 38, 21, 42, 49, 16],
      dtype=int64), 'cur_cost': 100033.0}, {'tour': [25, 18, 40, 7, 22, 9, 26, 58, 1, 5, 4, 37, 28, 42, 35, 15, 55, 12, 61, 64, 62, 21, 63, 48, 13, 16, 39, 45, 53, 46, 43, 6, 0, 20, 19, 51, 56, 65, 11, 31, 38, 44, 27, 54, 8, 41, 60, 3, 29, 2, 59, 23, 52, 34, 24, 32, 36, 47, 10, 49, 33, 30, 17, 14, 57, 50], 'cur_cost': 97462.0}, {'tour': [50, 11, 10, 18, 7, 59, 13, 30, 54, 61, 51, 9, 39, 33, 23, 36, 25, 32, 52, 8, 5, 48, 19, 1, 17, 2, 27, 14, 22, 55, 20, 57, 43, 24, 12, 45, 3, 16, 44, 64, 4, 49, 0, 58, 60, 28, 34, 26, 38, 40, 42, 21, 65, 53, 6, 31, 63, 56, 47, 35, 46, 41, 37, 29, 15, 62], 'cur_cost': 98153.0}, {'tour': [64, 25, 32, 37, 34, 24, 21, 14, 35, 5, 6, 13, 28, 9, 0, 43, 45, 61, 56, 22, 60, 41, 33, 65, 16, 30, 48, 23, 8, 19, 54, 47, 29, 1, 36, 57, 55, 59, 27, 42, 11, 26, 46, 17, 4, 39, 53, 44, 18, 63, 62, 12, 40, 58, 15, 52, 49, 7, 50, 51, 20, 38, 2, 3, 10, 31], 'cur_cost': 106763.0}, {'tour': [64, 24, 51, 4, 46, 2, 49, 43, 31, 30, 29, 34, 48, 36, 40, 10, 13, 50, 47, 65, 63, 28, 15, 39, 62, 56, 25, 11, 6, 44, 3, 12, 7, 59, 20, 23, 18, 58, 14, 52, 5, 60, 1, 22, 32, 38, 55, 42, 16, 57, 17, 41, 27, 45, 35, 26, 0, 19, 61, 9, 53, 54, 21, 8, 37, 33], 'cur_cost': 103841.0}, {'tour': [4, 28, 52, 59, 55, 61, 60, 26, 42, 1, 36, 63, 6, 27, 37, 64, 44, 14, 65, 53, 38, 51, 46, 29, 19, 11, 48, 21, 10, 33, 9, 18, 3, 57, 5, 22, 25, 31, 30, 7, 54, 8, 39, 24, 13, 12, 35, 45, 2, 50, 43, 23, 41, 34, 62, 47, 49, 17, 15, 20, 16, 32, 58, 56, 0, 40], 'cur_cost': 94629.0}, {'tour': [36, 20, 32, 47, 64, 16, 25, 15, 34, 10, 60, 42, 33, 51, 37, 46, 24, 22, 52, 63, 44, 45, 8, 7, 54, 65, 48, 43, 2, 27, 55, 35, 39, 59, 13, 6, 61, 5, 0, 53, 17, 40, 28, 19, 18, 57, 26, 41, 1, 31, 12, 21, 62, 3, 29, 11, 23, 50, 9, 30, 58, 49, 4, 14, 38, 56], 'cur_cost': 113911.0}, {'tour': [12, 65, 59, 29, 8, 23, 4, 54, 21, 7, 2, 19, 42, 28, 53, 63, 30, 34, 51, 11, 52, 25, 27, 35, 22, 5, 18, 31, 61, 33, 13, 14, 62, 55, 43, 37, 10, 44, 9, 17, 3, 0, 60, 39, 56, 48, 49, 32, 26, 6, 41, 20, 1, 46, 36, 58, 45, 40, 50, 57, 24, 64, 15, 38, 16, 47], 'cur_cost': 107316.0}, {'tour': [43, 46, 32, 56, 54, 5, 4, 17, 34, 51, 50, 20, 12, 0, 29, 6, 63, 25, 53, 28, 47, 58, 26, 15, 62, 59, 33, 2, 41, 48, 42, 38, 44, 18, 30, 61, 37, 9, 21, 3, 35, 65, 60, 11, 16, 7, 36, 55, 40, 13, 8, 10, 45, 52, 24, 57, 19, 23, 31, 27, 39, 14, 49, 64, 1, 22], 'cur_cost': 103809.0}, {'tour': [5, 37, 59, 20, 33, 25, 60, 51, 26, 16, 1, 40, 3, 34, 48, 55, 62, 31, 11, 36, 57, 58, 53, 39, 19, 8, 49, 6, 35, 44, 32, 38, 9, 4, 14, 12, 15, 24, 41, 27, 50, 63, 46, 56, 43, 61, 0, 47, 23, 54, 7, 28, 29, 17, 65, 52, 30, 10, 13, 18, 42, 64, 2, 45, 22, 21], 'cur_cost': 114990.0}]
2025-08-01 17:46:13,179 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒
2025-08-01 17:46:13,179 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 21, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 21, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:46:13,181 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([40, 62, 23,  6, 43, 46, 15,  9, 13, 32, 59,  5, 53, 17, 63, 44, 45,
       28, 35, 57, 19, 55, 41,  1,  2, 47, 31, 29,  8, 39, 27, 52, 58, 22,
       34, 10, 18, 12, 24, 36,  4, 14,  7, 26, 25, 33, 20, 37, 61, 48, 30,
       50,  0, 51, 54, 60, 56, 11, 65, 64,  3, 38, 21, 42, 49, 16],
      dtype=int64), 'cur_cost': 100033.0}
2025-08-01 17:46:13,182 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-01 17:46:13,182 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:13,183 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:13,184 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 100605.0
2025-08-01 17:46:14,548 - ExploitationExpert - INFO - res_population_num: 11
2025-08-01 17:46:14,549 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0, 9521]
2025-08-01 17:46:14,551 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-01 17:46:14,559 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:14,559 - ExploitationExpert - INFO - populations: [{'tour': array([40, 62, 23,  6, 43, 46, 15,  9, 13, 32, 59,  5, 53, 17, 63, 44, 45,
       28, 35, 57, 19, 55, 41,  1,  2, 47, 31, 29,  8, 39, 27, 52, 58, 22,
       34, 10, 18, 12, 24, 36,  4, 14,  7, 26, 25, 33, 20, 37, 61, 48, 30,
       50,  0, 51, 54, 60, 56, 11, 65, 64,  3, 38, 21, 42, 49, 16],
      dtype=int64), 'cur_cost': 100033.0}, {'tour': array([55, 24, 63,  1, 26, 54, 64, 42, 18, 60, 16,  7, 29, 14, 34, 22, 12,
       58,  3, 53, 10, 17, 28, 48, 41, 49, 32, 13, 50,  5, 62, 33,  9, 31,
       19, 37, 25, 21, 39, 51, 52,  8, 35, 57,  0, 36, 20, 61, 11, 56, 59,
        2,  4, 43, 45, 65, 38, 30, 46, 23, 27, 15, 44,  6, 40, 47],
      dtype=int64), 'cur_cost': 100605.0}, {'tour': [50, 11, 10, 18, 7, 59, 13, 30, 54, 61, 51, 9, 39, 33, 23, 36, 25, 32, 52, 8, 5, 48, 19, 1, 17, 2, 27, 14, 22, 55, 20, 57, 43, 24, 12, 45, 3, 16, 44, 64, 4, 49, 0, 58, 60, 28, 34, 26, 38, 40, 42, 21, 65, 53, 6, 31, 63, 56, 47, 35, 46, 41, 37, 29, 15, 62], 'cur_cost': 98153.0}, {'tour': [64, 25, 32, 37, 34, 24, 21, 14, 35, 5, 6, 13, 28, 9, 0, 43, 45, 61, 56, 22, 60, 41, 33, 65, 16, 30, 48, 23, 8, 19, 54, 47, 29, 1, 36, 57, 55, 59, 27, 42, 11, 26, 46, 17, 4, 39, 53, 44, 18, 63, 62, 12, 40, 58, 15, 52, 49, 7, 50, 51, 20, 38, 2, 3, 10, 31], 'cur_cost': 106763.0}, {'tour': [64, 24, 51, 4, 46, 2, 49, 43, 31, 30, 29, 34, 48, 36, 40, 10, 13, 50, 47, 65, 63, 28, 15, 39, 62, 56, 25, 11, 6, 44, 3, 12, 7, 59, 20, 23, 18, 58, 14, 52, 5, 60, 1, 22, 32, 38, 55, 42, 16, 57, 17, 41, 27, 45, 35, 26, 0, 19, 61, 9, 53, 54, 21, 8, 37, 33], 'cur_cost': 103841.0}, {'tour': [4, 28, 52, 59, 55, 61, 60, 26, 42, 1, 36, 63, 6, 27, 37, 64, 44, 14, 65, 53, 38, 51, 46, 29, 19, 11, 48, 21, 10, 33, 9, 18, 3, 57, 5, 22, 25, 31, 30, 7, 54, 8, 39, 24, 13, 12, 35, 45, 2, 50, 43, 23, 41, 34, 62, 47, 49, 17, 15, 20, 16, 32, 58, 56, 0, 40], 'cur_cost': 94629.0}, {'tour': [36, 20, 32, 47, 64, 16, 25, 15, 34, 10, 60, 42, 33, 51, 37, 46, 24, 22, 52, 63, 44, 45, 8, 7, 54, 65, 48, 43, 2, 27, 55, 35, 39, 59, 13, 6, 61, 5, 0, 53, 17, 40, 28, 19, 18, 57, 26, 41, 1, 31, 12, 21, 62, 3, 29, 11, 23, 50, 9, 30, 58, 49, 4, 14, 38, 56], 'cur_cost': 113911.0}, {'tour': [12, 65, 59, 29, 8, 23, 4, 54, 21, 7, 2, 19, 42, 28, 53, 63, 30, 34, 51, 11, 52, 25, 27, 35, 22, 5, 18, 31, 61, 33, 13, 14, 62, 55, 43, 37, 10, 44, 9, 17, 3, 0, 60, 39, 56, 48, 49, 32, 26, 6, 41, 20, 1, 46, 36, 58, 45, 40, 50, 57, 24, 64, 15, 38, 16, 47], 'cur_cost': 107316.0}, {'tour': [43, 46, 32, 56, 54, 5, 4, 17, 34, 51, 50, 20, 12, 0, 29, 6, 63, 25, 53, 28, 47, 58, 26, 15, 62, 59, 33, 2, 41, 48, 42, 38, 44, 18, 30, 61, 37, 9, 21, 3, 35, 65, 60, 11, 16, 7, 36, 55, 40, 13, 8, 10, 45, 52, 24, 57, 19, 23, 31, 27, 39, 14, 49, 64, 1, 22], 'cur_cost': 103809.0}, {'tour': [5, 37, 59, 20, 33, 25, 60, 51, 26, 16, 1, 40, 3, 34, 48, 55, 62, 31, 11, 36, 57, 58, 53, 39, 19, 8, 49, 6, 35, 44, 32, 38, 9, 4, 14, 12, 15, 24, 41, 27, 50, 63, 46, 56, 43, 61, 0, 47, 23, 54, 7, 28, 29, 17, 65, 52, 30, 10, 13, 18, 42, 64, 2, 45, 22, 21], 'cur_cost': 114990.0}]
2025-08-01 17:46:14,564 - ExploitationExpert - INFO - 局部搜索耗时: 1.38秒
2025-08-01 17:46:14,564 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 22, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 22, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:46:14,565 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([55, 24, 63,  1, 26, 54, 64, 42, 18, 60, 16,  7, 29, 14, 34, 22, 12,
       58,  3, 53, 10, 17, 28, 48, 41, 49, 32, 13, 50,  5, 62, 33,  9, 31,
       19, 37, 25, 21, 39, 51, 52,  8, 35, 57,  0, 36, 20, 61, 11, 56, 59,
        2,  4, 43, 45, 65, 38, 30, 46, 23, 27, 15, 44,  6, 40, 47],
      dtype=int64), 'cur_cost': 100605.0}
2025-08-01 17:46:14,566 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-01 17:46:14,566 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:14,567 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:14,567 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 111115.0
2025-08-01 17:46:14,656 - ExploitationExpert - INFO - res_population_num: 11
2025-08-01 17:46:14,656 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0, 9521]
2025-08-01 17:46:14,657 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-01 17:46:14,664 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:14,665 - ExploitationExpert - INFO - populations: [{'tour': array([40, 62, 23,  6, 43, 46, 15,  9, 13, 32, 59,  5, 53, 17, 63, 44, 45,
       28, 35, 57, 19, 55, 41,  1,  2, 47, 31, 29,  8, 39, 27, 52, 58, 22,
       34, 10, 18, 12, 24, 36,  4, 14,  7, 26, 25, 33, 20, 37, 61, 48, 30,
       50,  0, 51, 54, 60, 56, 11, 65, 64,  3, 38, 21, 42, 49, 16],
      dtype=int64), 'cur_cost': 100033.0}, {'tour': array([55, 24, 63,  1, 26, 54, 64, 42, 18, 60, 16,  7, 29, 14, 34, 22, 12,
       58,  3, 53, 10, 17, 28, 48, 41, 49, 32, 13, 50,  5, 62, 33,  9, 31,
       19, 37, 25, 21, 39, 51, 52,  8, 35, 57,  0, 36, 20, 61, 11, 56, 59,
        2,  4, 43, 45, 65, 38, 30, 46, 23, 27, 15, 44,  6, 40, 47],
      dtype=int64), 'cur_cost': 100605.0}, {'tour': array([59, 60, 18, 14,  6, 13, 34, 47, 30, 15, 63, 37, 52, 11, 46, 12, 62,
        2, 36, 57, 23, 38,  1, 22, 17, 64, 31, 33, 35,  4, 26, 40, 39, 53,
       16, 49, 56, 43, 42, 32, 48,  9, 29, 55, 65, 27, 58,  5, 50, 45, 44,
       21, 19,  7, 24,  8, 10, 28,  0, 54, 25,  3, 51, 20, 61, 41],
      dtype=int64), 'cur_cost': 111115.0}, {'tour': [64, 25, 32, 37, 34, 24, 21, 14, 35, 5, 6, 13, 28, 9, 0, 43, 45, 61, 56, 22, 60, 41, 33, 65, 16, 30, 48, 23, 8, 19, 54, 47, 29, 1, 36, 57, 55, 59, 27, 42, 11, 26, 46, 17, 4, 39, 53, 44, 18, 63, 62, 12, 40, 58, 15, 52, 49, 7, 50, 51, 20, 38, 2, 3, 10, 31], 'cur_cost': 106763.0}, {'tour': [64, 24, 51, 4, 46, 2, 49, 43, 31, 30, 29, 34, 48, 36, 40, 10, 13, 50, 47, 65, 63, 28, 15, 39, 62, 56, 25, 11, 6, 44, 3, 12, 7, 59, 20, 23, 18, 58, 14, 52, 5, 60, 1, 22, 32, 38, 55, 42, 16, 57, 17, 41, 27, 45, 35, 26, 0, 19, 61, 9, 53, 54, 21, 8, 37, 33], 'cur_cost': 103841.0}, {'tour': [4, 28, 52, 59, 55, 61, 60, 26, 42, 1, 36, 63, 6, 27, 37, 64, 44, 14, 65, 53, 38, 51, 46, 29, 19, 11, 48, 21, 10, 33, 9, 18, 3, 57, 5, 22, 25, 31, 30, 7, 54, 8, 39, 24, 13, 12, 35, 45, 2, 50, 43, 23, 41, 34, 62, 47, 49, 17, 15, 20, 16, 32, 58, 56, 0, 40], 'cur_cost': 94629.0}, {'tour': [36, 20, 32, 47, 64, 16, 25, 15, 34, 10, 60, 42, 33, 51, 37, 46, 24, 22, 52, 63, 44, 45, 8, 7, 54, 65, 48, 43, 2, 27, 55, 35, 39, 59, 13, 6, 61, 5, 0, 53, 17, 40, 28, 19, 18, 57, 26, 41, 1, 31, 12, 21, 62, 3, 29, 11, 23, 50, 9, 30, 58, 49, 4, 14, 38, 56], 'cur_cost': 113911.0}, {'tour': [12, 65, 59, 29, 8, 23, 4, 54, 21, 7, 2, 19, 42, 28, 53, 63, 30, 34, 51, 11, 52, 25, 27, 35, 22, 5, 18, 31, 61, 33, 13, 14, 62, 55, 43, 37, 10, 44, 9, 17, 3, 0, 60, 39, 56, 48, 49, 32, 26, 6, 41, 20, 1, 46, 36, 58, 45, 40, 50, 57, 24, 64, 15, 38, 16, 47], 'cur_cost': 107316.0}, {'tour': [43, 46, 32, 56, 54, 5, 4, 17, 34, 51, 50, 20, 12, 0, 29, 6, 63, 25, 53, 28, 47, 58, 26, 15, 62, 59, 33, 2, 41, 48, 42, 38, 44, 18, 30, 61, 37, 9, 21, 3, 35, 65, 60, 11, 16, 7, 36, 55, 40, 13, 8, 10, 45, 52, 24, 57, 19, 23, 31, 27, 39, 14, 49, 64, 1, 22], 'cur_cost': 103809.0}, {'tour': [5, 37, 59, 20, 33, 25, 60, 51, 26, 16, 1, 40, 3, 34, 48, 55, 62, 31, 11, 36, 57, 58, 53, 39, 19, 8, 49, 6, 35, 44, 32, 38, 9, 4, 14, 12, 15, 24, 41, 27, 50, 63, 46, 56, 43, 61, 0, 47, 23, 54, 7, 28, 29, 17, 65, 52, 30, 10, 13, 18, 42, 64, 2, 45, 22, 21], 'cur_cost': 114990.0}]
2025-08-01 17:46:14,669 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-01 17:46:14,669 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 23, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 23, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:46:14,672 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([59, 60, 18, 14,  6, 13, 34, 47, 30, 15, 63, 37, 52, 11, 46, 12, 62,
        2, 36, 57, 23, 38,  1, 22, 17, 64, 31, 33, 35,  4, 26, 40, 39, 53,
       16, 49, 56, 43, 42, 32, 48,  9, 29, 55, 65, 27, 58,  5, 50, 45, 44,
       21, 19,  7, 24,  8, 10, 28,  0, 54, 25,  3, 51, 20, 61, 41],
      dtype=int64), 'cur_cost': 111115.0}
2025-08-01 17:46:14,674 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-01 17:46:14,674 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:14,674 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:14,675 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 111374.0
2025-08-01 17:46:14,765 - ExploitationExpert - INFO - res_population_num: 11
2025-08-01 17:46:14,766 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0, 9521]
2025-08-01 17:46:14,766 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-01 17:46:14,777 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:14,777 - ExploitationExpert - INFO - populations: [{'tour': array([40, 62, 23,  6, 43, 46, 15,  9, 13, 32, 59,  5, 53, 17, 63, 44, 45,
       28, 35, 57, 19, 55, 41,  1,  2, 47, 31, 29,  8, 39, 27, 52, 58, 22,
       34, 10, 18, 12, 24, 36,  4, 14,  7, 26, 25, 33, 20, 37, 61, 48, 30,
       50,  0, 51, 54, 60, 56, 11, 65, 64,  3, 38, 21, 42, 49, 16],
      dtype=int64), 'cur_cost': 100033.0}, {'tour': array([55, 24, 63,  1, 26, 54, 64, 42, 18, 60, 16,  7, 29, 14, 34, 22, 12,
       58,  3, 53, 10, 17, 28, 48, 41, 49, 32, 13, 50,  5, 62, 33,  9, 31,
       19, 37, 25, 21, 39, 51, 52,  8, 35, 57,  0, 36, 20, 61, 11, 56, 59,
        2,  4, 43, 45, 65, 38, 30, 46, 23, 27, 15, 44,  6, 40, 47],
      dtype=int64), 'cur_cost': 100605.0}, {'tour': array([59, 60, 18, 14,  6, 13, 34, 47, 30, 15, 63, 37, 52, 11, 46, 12, 62,
        2, 36, 57, 23, 38,  1, 22, 17, 64, 31, 33, 35,  4, 26, 40, 39, 53,
       16, 49, 56, 43, 42, 32, 48,  9, 29, 55, 65, 27, 58,  5, 50, 45, 44,
       21, 19,  7, 24,  8, 10, 28,  0, 54, 25,  3, 51, 20, 61, 41],
      dtype=int64), 'cur_cost': 111115.0}, {'tour': array([30, 18, 54, 61, 47, 58, 20, 42, 21, 17, 39, 10, 51, 25, 28, 63, 31,
       12,  3, 52, 32, 56, 11, 36, 22, 45, 49, 55, 34, 38, 53,  4,  8, 64,
       14, 65,  9, 16, 60, 46,  5, 57, 48, 37, 26, 15, 33, 13, 27, 40, 41,
       23,  0, 43,  6, 50, 29,  2,  7, 59, 35, 19, 24, 44, 62,  1],
      dtype=int64), 'cur_cost': 111374.0}, {'tour': [64, 24, 51, 4, 46, 2, 49, 43, 31, 30, 29, 34, 48, 36, 40, 10, 13, 50, 47, 65, 63, 28, 15, 39, 62, 56, 25, 11, 6, 44, 3, 12, 7, 59, 20, 23, 18, 58, 14, 52, 5, 60, 1, 22, 32, 38, 55, 42, 16, 57, 17, 41, 27, 45, 35, 26, 0, 19, 61, 9, 53, 54, 21, 8, 37, 33], 'cur_cost': 103841.0}, {'tour': [4, 28, 52, 59, 55, 61, 60, 26, 42, 1, 36, 63, 6, 27, 37, 64, 44, 14, 65, 53, 38, 51, 46, 29, 19, 11, 48, 21, 10, 33, 9, 18, 3, 57, 5, 22, 25, 31, 30, 7, 54, 8, 39, 24, 13, 12, 35, 45, 2, 50, 43, 23, 41, 34, 62, 47, 49, 17, 15, 20, 16, 32, 58, 56, 0, 40], 'cur_cost': 94629.0}, {'tour': [36, 20, 32, 47, 64, 16, 25, 15, 34, 10, 60, 42, 33, 51, 37, 46, 24, 22, 52, 63, 44, 45, 8, 7, 54, 65, 48, 43, 2, 27, 55, 35, 39, 59, 13, 6, 61, 5, 0, 53, 17, 40, 28, 19, 18, 57, 26, 41, 1, 31, 12, 21, 62, 3, 29, 11, 23, 50, 9, 30, 58, 49, 4, 14, 38, 56], 'cur_cost': 113911.0}, {'tour': [12, 65, 59, 29, 8, 23, 4, 54, 21, 7, 2, 19, 42, 28, 53, 63, 30, 34, 51, 11, 52, 25, 27, 35, 22, 5, 18, 31, 61, 33, 13, 14, 62, 55, 43, 37, 10, 44, 9, 17, 3, 0, 60, 39, 56, 48, 49, 32, 26, 6, 41, 20, 1, 46, 36, 58, 45, 40, 50, 57, 24, 64, 15, 38, 16, 47], 'cur_cost': 107316.0}, {'tour': [43, 46, 32, 56, 54, 5, 4, 17, 34, 51, 50, 20, 12, 0, 29, 6, 63, 25, 53, 28, 47, 58, 26, 15, 62, 59, 33, 2, 41, 48, 42, 38, 44, 18, 30, 61, 37, 9, 21, 3, 35, 65, 60, 11, 16, 7, 36, 55, 40, 13, 8, 10, 45, 52, 24, 57, 19, 23, 31, 27, 39, 14, 49, 64, 1, 22], 'cur_cost': 103809.0}, {'tour': [5, 37, 59, 20, 33, 25, 60, 51, 26, 16, 1, 40, 3, 34, 48, 55, 62, 31, 11, 36, 57, 58, 53, 39, 19, 8, 49, 6, 35, 44, 32, 38, 9, 4, 14, 12, 15, 24, 41, 27, 50, 63, 46, 56, 43, 61, 0, 47, 23, 54, 7, 28, 29, 17, 65, 52, 30, 10, 13, 18, 42, 64, 2, 45, 22, 21], 'cur_cost': 114990.0}]
2025-08-01 17:46:14,783 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒
2025-08-01 17:46:14,784 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 24, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 24, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:46:14,785 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([30, 18, 54, 61, 47, 58, 20, 42, 21, 17, 39, 10, 51, 25, 28, 63, 31,
       12,  3, 52, 32, 56, 11, 36, 22, 45, 49, 55, 34, 38, 53,  4,  8, 64,
       14, 65,  9, 16, 60, 46,  5, 57, 48, 37, 26, 15, 33, 13, 27, 40, 41,
       23,  0, 43,  6, 50, 29,  2,  7, 59, 35, 19, 24, 44, 62,  1],
      dtype=int64), 'cur_cost': 111374.0}
2025-08-01 17:46:14,785 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-01 17:46:14,785 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:14,786 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:14,786 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 96267.0
2025-08-01 17:46:14,878 - ExploitationExpert - INFO - res_population_num: 11
2025-08-01 17:46:14,878 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0, 9521]
2025-08-01 17:46:14,879 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-01 17:46:14,887 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:14,887 - ExploitationExpert - INFO - populations: [{'tour': array([40, 62, 23,  6, 43, 46, 15,  9, 13, 32, 59,  5, 53, 17, 63, 44, 45,
       28, 35, 57, 19, 55, 41,  1,  2, 47, 31, 29,  8, 39, 27, 52, 58, 22,
       34, 10, 18, 12, 24, 36,  4, 14,  7, 26, 25, 33, 20, 37, 61, 48, 30,
       50,  0, 51, 54, 60, 56, 11, 65, 64,  3, 38, 21, 42, 49, 16],
      dtype=int64), 'cur_cost': 100033.0}, {'tour': array([55, 24, 63,  1, 26, 54, 64, 42, 18, 60, 16,  7, 29, 14, 34, 22, 12,
       58,  3, 53, 10, 17, 28, 48, 41, 49, 32, 13, 50,  5, 62, 33,  9, 31,
       19, 37, 25, 21, 39, 51, 52,  8, 35, 57,  0, 36, 20, 61, 11, 56, 59,
        2,  4, 43, 45, 65, 38, 30, 46, 23, 27, 15, 44,  6, 40, 47],
      dtype=int64), 'cur_cost': 100605.0}, {'tour': array([59, 60, 18, 14,  6, 13, 34, 47, 30, 15, 63, 37, 52, 11, 46, 12, 62,
        2, 36, 57, 23, 38,  1, 22, 17, 64, 31, 33, 35,  4, 26, 40, 39, 53,
       16, 49, 56, 43, 42, 32, 48,  9, 29, 55, 65, 27, 58,  5, 50, 45, 44,
       21, 19,  7, 24,  8, 10, 28,  0, 54, 25,  3, 51, 20, 61, 41],
      dtype=int64), 'cur_cost': 111115.0}, {'tour': array([30, 18, 54, 61, 47, 58, 20, 42, 21, 17, 39, 10, 51, 25, 28, 63, 31,
       12,  3, 52, 32, 56, 11, 36, 22, 45, 49, 55, 34, 38, 53,  4,  8, 64,
       14, 65,  9, 16, 60, 46,  5, 57, 48, 37, 26, 15, 33, 13, 27, 40, 41,
       23,  0, 43,  6, 50, 29,  2,  7, 59, 35, 19, 24, 44, 62,  1],
      dtype=int64), 'cur_cost': 111374.0}, {'tour': array([47, 17, 20,  9, 44, 51, 64, 14, 25, 59, 63,  5, 43, 28,  2, 61, 62,
        8, 50, 33, 19, 42, 22, 35, 39, 15, 49, 46, 10, 54, 31, 29, 21, 55,
        3, 26, 30, 52, 53, 23, 18, 58, 38, 27, 56,  1,  0, 11, 37, 36,  7,
       13, 12, 60, 34, 40, 48, 45, 41,  6,  4, 24, 65, 16, 57, 32],
      dtype=int64), 'cur_cost': 96267.0}, {'tour': [4, 28, 52, 59, 55, 61, 60, 26, 42, 1, 36, 63, 6, 27, 37, 64, 44, 14, 65, 53, 38, 51, 46, 29, 19, 11, 48, 21, 10, 33, 9, 18, 3, 57, 5, 22, 25, 31, 30, 7, 54, 8, 39, 24, 13, 12, 35, 45, 2, 50, 43, 23, 41, 34, 62, 47, 49, 17, 15, 20, 16, 32, 58, 56, 0, 40], 'cur_cost': 94629.0}, {'tour': [36, 20, 32, 47, 64, 16, 25, 15, 34, 10, 60, 42, 33, 51, 37, 46, 24, 22, 52, 63, 44, 45, 8, 7, 54, 65, 48, 43, 2, 27, 55, 35, 39, 59, 13, 6, 61, 5, 0, 53, 17, 40, 28, 19, 18, 57, 26, 41, 1, 31, 12, 21, 62, 3, 29, 11, 23, 50, 9, 30, 58, 49, 4, 14, 38, 56], 'cur_cost': 113911.0}, {'tour': [12, 65, 59, 29, 8, 23, 4, 54, 21, 7, 2, 19, 42, 28, 53, 63, 30, 34, 51, 11, 52, 25, 27, 35, 22, 5, 18, 31, 61, 33, 13, 14, 62, 55, 43, 37, 10, 44, 9, 17, 3, 0, 60, 39, 56, 48, 49, 32, 26, 6, 41, 20, 1, 46, 36, 58, 45, 40, 50, 57, 24, 64, 15, 38, 16, 47], 'cur_cost': 107316.0}, {'tour': [43, 46, 32, 56, 54, 5, 4, 17, 34, 51, 50, 20, 12, 0, 29, 6, 63, 25, 53, 28, 47, 58, 26, 15, 62, 59, 33, 2, 41, 48, 42, 38, 44, 18, 30, 61, 37, 9, 21, 3, 35, 65, 60, 11, 16, 7, 36, 55, 40, 13, 8, 10, 45, 52, 24, 57, 19, 23, 31, 27, 39, 14, 49, 64, 1, 22], 'cur_cost': 103809.0}, {'tour': [5, 37, 59, 20, 33, 25, 60, 51, 26, 16, 1, 40, 3, 34, 48, 55, 62, 31, 11, 36, 57, 58, 53, 39, 19, 8, 49, 6, 35, 44, 32, 38, 9, 4, 14, 12, 15, 24, 41, 27, 50, 63, 46, 56, 43, 61, 0, 47, 23, 54, 7, 28, 29, 17, 65, 52, 30, 10, 13, 18, 42, 64, 2, 45, 22, 21], 'cur_cost': 114990.0}]
2025-08-01 17:46:14,892 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒
2025-08-01 17:46:14,892 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 25, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 25, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:46:14,894 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([47, 17, 20,  9, 44, 51, 64, 14, 25, 59, 63,  5, 43, 28,  2, 61, 62,
        8, 50, 33, 19, 42, 22, 35, 39, 15, 49, 46, 10, 54, 31, 29, 21, 55,
        3, 26, 30, 52, 53, 23, 18, 58, 38, 27, 56,  1,  0, 11, 37, 36,  7,
       13, 12, 60, 34, 40, 48, 45, 41,  6,  4, 24, 65, 16, 57, 32],
      dtype=int64), 'cur_cost': 96267.0}
2025-08-01 17:46:14,894 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-01 17:46:14,894 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:14,894 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:14,895 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 104851.0
2025-08-01 17:46:14,990 - ExploitationExpert - INFO - res_population_num: 11
2025-08-01 17:46:14,991 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0, 9521]
2025-08-01 17:46:14,991 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-01 17:46:15,003 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:15,004 - ExploitationExpert - INFO - populations: [{'tour': array([40, 62, 23,  6, 43, 46, 15,  9, 13, 32, 59,  5, 53, 17, 63, 44, 45,
       28, 35, 57, 19, 55, 41,  1,  2, 47, 31, 29,  8, 39, 27, 52, 58, 22,
       34, 10, 18, 12, 24, 36,  4, 14,  7, 26, 25, 33, 20, 37, 61, 48, 30,
       50,  0, 51, 54, 60, 56, 11, 65, 64,  3, 38, 21, 42, 49, 16],
      dtype=int64), 'cur_cost': 100033.0}, {'tour': array([55, 24, 63,  1, 26, 54, 64, 42, 18, 60, 16,  7, 29, 14, 34, 22, 12,
       58,  3, 53, 10, 17, 28, 48, 41, 49, 32, 13, 50,  5, 62, 33,  9, 31,
       19, 37, 25, 21, 39, 51, 52,  8, 35, 57,  0, 36, 20, 61, 11, 56, 59,
        2,  4, 43, 45, 65, 38, 30, 46, 23, 27, 15, 44,  6, 40, 47],
      dtype=int64), 'cur_cost': 100605.0}, {'tour': array([59, 60, 18, 14,  6, 13, 34, 47, 30, 15, 63, 37, 52, 11, 46, 12, 62,
        2, 36, 57, 23, 38,  1, 22, 17, 64, 31, 33, 35,  4, 26, 40, 39, 53,
       16, 49, 56, 43, 42, 32, 48,  9, 29, 55, 65, 27, 58,  5, 50, 45, 44,
       21, 19,  7, 24,  8, 10, 28,  0, 54, 25,  3, 51, 20, 61, 41],
      dtype=int64), 'cur_cost': 111115.0}, {'tour': array([30, 18, 54, 61, 47, 58, 20, 42, 21, 17, 39, 10, 51, 25, 28, 63, 31,
       12,  3, 52, 32, 56, 11, 36, 22, 45, 49, 55, 34, 38, 53,  4,  8, 64,
       14, 65,  9, 16, 60, 46,  5, 57, 48, 37, 26, 15, 33, 13, 27, 40, 41,
       23,  0, 43,  6, 50, 29,  2,  7, 59, 35, 19, 24, 44, 62,  1],
      dtype=int64), 'cur_cost': 111374.0}, {'tour': array([47, 17, 20,  9, 44, 51, 64, 14, 25, 59, 63,  5, 43, 28,  2, 61, 62,
        8, 50, 33, 19, 42, 22, 35, 39, 15, 49, 46, 10, 54, 31, 29, 21, 55,
        3, 26, 30, 52, 53, 23, 18, 58, 38, 27, 56,  1,  0, 11, 37, 36,  7,
       13, 12, 60, 34, 40, 48, 45, 41,  6,  4, 24, 65, 16, 57, 32],
      dtype=int64), 'cur_cost': 96267.0}, {'tour': array([65, 20,  1,  9, 33, 16,  6,  8, 12, 32, 28, 17, 37, 34,  3, 13, 57,
       55, 22, 25, 56, 47, 52, 24, 21, 50, 27, 43, 40, 35, 36, 59, 44,  2,
       42, 63, 26, 15, 18, 31, 45, 38, 39, 64, 62, 30, 49,  5, 46,  4,  0,
       51, 54, 11, 58, 61, 19, 53, 29, 60, 41, 23, 14, 48,  7, 10],
      dtype=int64), 'cur_cost': 104851.0}, {'tour': [36, 20, 32, 47, 64, 16, 25, 15, 34, 10, 60, 42, 33, 51, 37, 46, 24, 22, 52, 63, 44, 45, 8, 7, 54, 65, 48, 43, 2, 27, 55, 35, 39, 59, 13, 6, 61, 5, 0, 53, 17, 40, 28, 19, 18, 57, 26, 41, 1, 31, 12, 21, 62, 3, 29, 11, 23, 50, 9, 30, 58, 49, 4, 14, 38, 56], 'cur_cost': 113911.0}, {'tour': [12, 65, 59, 29, 8, 23, 4, 54, 21, 7, 2, 19, 42, 28, 53, 63, 30, 34, 51, 11, 52, 25, 27, 35, 22, 5, 18, 31, 61, 33, 13, 14, 62, 55, 43, 37, 10, 44, 9, 17, 3, 0, 60, 39, 56, 48, 49, 32, 26, 6, 41, 20, 1, 46, 36, 58, 45, 40, 50, 57, 24, 64, 15, 38, 16, 47], 'cur_cost': 107316.0}, {'tour': [43, 46, 32, 56, 54, 5, 4, 17, 34, 51, 50, 20, 12, 0, 29, 6, 63, 25, 53, 28, 47, 58, 26, 15, 62, 59, 33, 2, 41, 48, 42, 38, 44, 18, 30, 61, 37, 9, 21, 3, 35, 65, 60, 11, 16, 7, 36, 55, 40, 13, 8, 10, 45, 52, 24, 57, 19, 23, 31, 27, 39, 14, 49, 64, 1, 22], 'cur_cost': 103809.0}, {'tour': [5, 37, 59, 20, 33, 25, 60, 51, 26, 16, 1, 40, 3, 34, 48, 55, 62, 31, 11, 36, 57, 58, 53, 39, 19, 8, 49, 6, 35, 44, 32, 38, 9, 4, 14, 12, 15, 24, 41, 27, 50, 63, 46, 56, 43, 61, 0, 47, 23, 54, 7, 28, 29, 17, 65, 52, 30, 10, 13, 18, 42, 64, 2, 45, 22, 21], 'cur_cost': 114990.0}]
2025-08-01 17:46:15,016 - ExploitationExpert - INFO - 局部搜索耗时: 0.12秒
2025-08-01 17:46:15,017 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 26, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 26, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:46:15,018 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([65, 20,  1,  9, 33, 16,  6,  8, 12, 32, 28, 17, 37, 34,  3, 13, 57,
       55, 22, 25, 56, 47, 52, 24, 21, 50, 27, 43, 40, 35, 36, 59, 44,  2,
       42, 63, 26, 15, 18, 31, 45, 38, 39, 64, 62, 30, 49,  5, 46,  4,  0,
       51, 54, 11, 58, 61, 19, 53, 29, 60, 41, 23, 14, 48,  7, 10],
      dtype=int64), 'cur_cost': 104851.0}
2025-08-01 17:46:15,019 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-01 17:46:15,019 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:15,019 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:15,020 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 107109.0
2025-08-01 17:46:15,102 - ExploitationExpert - INFO - res_population_num: 12
2025-08-01 17:46:15,103 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0, 9521, 9521]
2025-08-01 17:46:15,103 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 17:46:15,117 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:15,118 - ExploitationExpert - INFO - populations: [{'tour': array([40, 62, 23,  6, 43, 46, 15,  9, 13, 32, 59,  5, 53, 17, 63, 44, 45,
       28, 35, 57, 19, 55, 41,  1,  2, 47, 31, 29,  8, 39, 27, 52, 58, 22,
       34, 10, 18, 12, 24, 36,  4, 14,  7, 26, 25, 33, 20, 37, 61, 48, 30,
       50,  0, 51, 54, 60, 56, 11, 65, 64,  3, 38, 21, 42, 49, 16],
      dtype=int64), 'cur_cost': 100033.0}, {'tour': array([55, 24, 63,  1, 26, 54, 64, 42, 18, 60, 16,  7, 29, 14, 34, 22, 12,
       58,  3, 53, 10, 17, 28, 48, 41, 49, 32, 13, 50,  5, 62, 33,  9, 31,
       19, 37, 25, 21, 39, 51, 52,  8, 35, 57,  0, 36, 20, 61, 11, 56, 59,
        2,  4, 43, 45, 65, 38, 30, 46, 23, 27, 15, 44,  6, 40, 47],
      dtype=int64), 'cur_cost': 100605.0}, {'tour': array([59, 60, 18, 14,  6, 13, 34, 47, 30, 15, 63, 37, 52, 11, 46, 12, 62,
        2, 36, 57, 23, 38,  1, 22, 17, 64, 31, 33, 35,  4, 26, 40, 39, 53,
       16, 49, 56, 43, 42, 32, 48,  9, 29, 55, 65, 27, 58,  5, 50, 45, 44,
       21, 19,  7, 24,  8, 10, 28,  0, 54, 25,  3, 51, 20, 61, 41],
      dtype=int64), 'cur_cost': 111115.0}, {'tour': array([30, 18, 54, 61, 47, 58, 20, 42, 21, 17, 39, 10, 51, 25, 28, 63, 31,
       12,  3, 52, 32, 56, 11, 36, 22, 45, 49, 55, 34, 38, 53,  4,  8, 64,
       14, 65,  9, 16, 60, 46,  5, 57, 48, 37, 26, 15, 33, 13, 27, 40, 41,
       23,  0, 43,  6, 50, 29,  2,  7, 59, 35, 19, 24, 44, 62,  1],
      dtype=int64), 'cur_cost': 111374.0}, {'tour': array([47, 17, 20,  9, 44, 51, 64, 14, 25, 59, 63,  5, 43, 28,  2, 61, 62,
        8, 50, 33, 19, 42, 22, 35, 39, 15, 49, 46, 10, 54, 31, 29, 21, 55,
        3, 26, 30, 52, 53, 23, 18, 58, 38, 27, 56,  1,  0, 11, 37, 36,  7,
       13, 12, 60, 34, 40, 48, 45, 41,  6,  4, 24, 65, 16, 57, 32],
      dtype=int64), 'cur_cost': 96267.0}, {'tour': array([65, 20,  1,  9, 33, 16,  6,  8, 12, 32, 28, 17, 37, 34,  3, 13, 57,
       55, 22, 25, 56, 47, 52, 24, 21, 50, 27, 43, 40, 35, 36, 59, 44,  2,
       42, 63, 26, 15, 18, 31, 45, 38, 39, 64, 62, 30, 49,  5, 46,  4,  0,
       51, 54, 11, 58, 61, 19, 53, 29, 60, 41, 23, 14, 48,  7, 10],
      dtype=int64), 'cur_cost': 104851.0}, {'tour': array([ 7, 51, 20, 24, 31, 45, 57, 10, 62, 49, 54, 58, 32, 18, 39,  8, 53,
       27,  0, 65, 37, 47,  1, 35, 42, 43,  3,  9, 55, 13, 15,  5, 50, 33,
       64, 30, 34, 21, 12,  4, 22, 63, 56, 23, 25, 11, 19, 17, 38, 28,  2,
       16, 41, 48, 36,  6, 40, 46, 61, 26, 14, 59, 44, 29, 52, 60],
      dtype=int64), 'cur_cost': 107109.0}, {'tour': [12, 65, 59, 29, 8, 23, 4, 54, 21, 7, 2, 19, 42, 28, 53, 63, 30, 34, 51, 11, 52, 25, 27, 35, 22, 5, 18, 31, 61, 33, 13, 14, 62, 55, 43, 37, 10, 44, 9, 17, 3, 0, 60, 39, 56, 48, 49, 32, 26, 6, 41, 20, 1, 46, 36, 58, 45, 40, 50, 57, 24, 64, 15, 38, 16, 47], 'cur_cost': 107316.0}, {'tour': [43, 46, 32, 56, 54, 5, 4, 17, 34, 51, 50, 20, 12, 0, 29, 6, 63, 25, 53, 28, 47, 58, 26, 15, 62, 59, 33, 2, 41, 48, 42, 38, 44, 18, 30, 61, 37, 9, 21, 3, 35, 65, 60, 11, 16, 7, 36, 55, 40, 13, 8, 10, 45, 52, 24, 57, 19, 23, 31, 27, 39, 14, 49, 64, 1, 22], 'cur_cost': 103809.0}, {'tour': [5, 37, 59, 20, 33, 25, 60, 51, 26, 16, 1, 40, 3, 34, 48, 55, 62, 31, 11, 36, 57, 58, 53, 39, 19, 8, 49, 6, 35, 44, 32, 38, 9, 4, 14, 12, 15, 24, 41, 27, 50, 63, 46, 56, 43, 61, 0, 47, 23, 54, 7, 28, 29, 17, 65, 52, 30, 10, 13, 18, 42, 64, 2, 45, 22, 21], 'cur_cost': 114990.0}]
2025-08-01 17:46:15,124 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-01 17:46:15,124 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 27, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 27, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:46:15,125 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([ 7, 51, 20, 24, 31, 45, 57, 10, 62, 49, 54, 58, 32, 18, 39,  8, 53,
       27,  0, 65, 37, 47,  1, 35, 42, 43,  3,  9, 55, 13, 15,  5, 50, 33,
       64, 30, 34, 21, 12,  4, 22, 63, 56, 23, 25, 11, 19, 17, 38, 28,  2,
       16, 41, 48, 36,  6, 40, 46, 61, 26, 14, 59, 44, 29, 52, 60],
      dtype=int64), 'cur_cost': 107109.0}
2025-08-01 17:46:15,126 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-01 17:46:15,126 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:15,126 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:15,127 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 99565.0
2025-08-01 17:46:15,213 - ExploitationExpert - INFO - res_population_num: 12
2025-08-01 17:46:15,213 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0, 9521, 9521]
2025-08-01 17:46:15,214 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 17:46:15,226 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:15,226 - ExploitationExpert - INFO - populations: [{'tour': array([40, 62, 23,  6, 43, 46, 15,  9, 13, 32, 59,  5, 53, 17, 63, 44, 45,
       28, 35, 57, 19, 55, 41,  1,  2, 47, 31, 29,  8, 39, 27, 52, 58, 22,
       34, 10, 18, 12, 24, 36,  4, 14,  7, 26, 25, 33, 20, 37, 61, 48, 30,
       50,  0, 51, 54, 60, 56, 11, 65, 64,  3, 38, 21, 42, 49, 16],
      dtype=int64), 'cur_cost': 100033.0}, {'tour': array([55, 24, 63,  1, 26, 54, 64, 42, 18, 60, 16,  7, 29, 14, 34, 22, 12,
       58,  3, 53, 10, 17, 28, 48, 41, 49, 32, 13, 50,  5, 62, 33,  9, 31,
       19, 37, 25, 21, 39, 51, 52,  8, 35, 57,  0, 36, 20, 61, 11, 56, 59,
        2,  4, 43, 45, 65, 38, 30, 46, 23, 27, 15, 44,  6, 40, 47],
      dtype=int64), 'cur_cost': 100605.0}, {'tour': array([59, 60, 18, 14,  6, 13, 34, 47, 30, 15, 63, 37, 52, 11, 46, 12, 62,
        2, 36, 57, 23, 38,  1, 22, 17, 64, 31, 33, 35,  4, 26, 40, 39, 53,
       16, 49, 56, 43, 42, 32, 48,  9, 29, 55, 65, 27, 58,  5, 50, 45, 44,
       21, 19,  7, 24,  8, 10, 28,  0, 54, 25,  3, 51, 20, 61, 41],
      dtype=int64), 'cur_cost': 111115.0}, {'tour': array([30, 18, 54, 61, 47, 58, 20, 42, 21, 17, 39, 10, 51, 25, 28, 63, 31,
       12,  3, 52, 32, 56, 11, 36, 22, 45, 49, 55, 34, 38, 53,  4,  8, 64,
       14, 65,  9, 16, 60, 46,  5, 57, 48, 37, 26, 15, 33, 13, 27, 40, 41,
       23,  0, 43,  6, 50, 29,  2,  7, 59, 35, 19, 24, 44, 62,  1],
      dtype=int64), 'cur_cost': 111374.0}, {'tour': array([47, 17, 20,  9, 44, 51, 64, 14, 25, 59, 63,  5, 43, 28,  2, 61, 62,
        8, 50, 33, 19, 42, 22, 35, 39, 15, 49, 46, 10, 54, 31, 29, 21, 55,
        3, 26, 30, 52, 53, 23, 18, 58, 38, 27, 56,  1,  0, 11, 37, 36,  7,
       13, 12, 60, 34, 40, 48, 45, 41,  6,  4, 24, 65, 16, 57, 32],
      dtype=int64), 'cur_cost': 96267.0}, {'tour': array([65, 20,  1,  9, 33, 16,  6,  8, 12, 32, 28, 17, 37, 34,  3, 13, 57,
       55, 22, 25, 56, 47, 52, 24, 21, 50, 27, 43, 40, 35, 36, 59, 44,  2,
       42, 63, 26, 15, 18, 31, 45, 38, 39, 64, 62, 30, 49,  5, 46,  4,  0,
       51, 54, 11, 58, 61, 19, 53, 29, 60, 41, 23, 14, 48,  7, 10],
      dtype=int64), 'cur_cost': 104851.0}, {'tour': array([ 7, 51, 20, 24, 31, 45, 57, 10, 62, 49, 54, 58, 32, 18, 39,  8, 53,
       27,  0, 65, 37, 47,  1, 35, 42, 43,  3,  9, 55, 13, 15,  5, 50, 33,
       64, 30, 34, 21, 12,  4, 22, 63, 56, 23, 25, 11, 19, 17, 38, 28,  2,
       16, 41, 48, 36,  6, 40, 46, 61, 26, 14, 59, 44, 29, 52, 60],
      dtype=int64), 'cur_cost': 107109.0}, {'tour': array([54, 11, 36,  4, 65, 45, 49,  0, 37, 55, 14, 13, 43, 15, 38, 60,  3,
       12, 48, 29, 18,  7, 27, 42, 32, 22,  6, 61, 25, 17, 30,  2, 62, 23,
       24,  5, 57, 52, 59, 10, 28, 56, 58,  9, 34, 41, 40, 63, 39, 50, 33,
       19, 44, 51, 21, 64,  1, 31, 16,  8, 20, 53, 26, 35, 46, 47],
      dtype=int64), 'cur_cost': 99565.0}, {'tour': [43, 46, 32, 56, 54, 5, 4, 17, 34, 51, 50, 20, 12, 0, 29, 6, 63, 25, 53, 28, 47, 58, 26, 15, 62, 59, 33, 2, 41, 48, 42, 38, 44, 18, 30, 61, 37, 9, 21, 3, 35, 65, 60, 11, 16, 7, 36, 55, 40, 13, 8, 10, 45, 52, 24, 57, 19, 23, 31, 27, 39, 14, 49, 64, 1, 22], 'cur_cost': 103809.0}, {'tour': [5, 37, 59, 20, 33, 25, 60, 51, 26, 16, 1, 40, 3, 34, 48, 55, 62, 31, 11, 36, 57, 58, 53, 39, 19, 8, 49, 6, 35, 44, 32, 38, 9, 4, 14, 12, 15, 24, 41, 27, 50, 63, 46, 56, 43, 61, 0, 47, 23, 54, 7, 28, 29, 17, 65, 52, 30, 10, 13, 18, 42, 64, 2, 45, 22, 21], 'cur_cost': 114990.0}]
2025-08-01 17:46:15,238 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒
2025-08-01 17:46:15,242 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 28, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 28, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:46:15,244 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([54, 11, 36,  4, 65, 45, 49,  0, 37, 55, 14, 13, 43, 15, 38, 60,  3,
       12, 48, 29, 18,  7, 27, 42, 32, 22,  6, 61, 25, 17, 30,  2, 62, 23,
       24,  5, 57, 52, 59, 10, 28, 56, 58,  9, 34, 41, 40, 63, 39, 50, 33,
       19, 44, 51, 21, 64,  1, 31, 16,  8, 20, 53, 26, 35, 46, 47],
      dtype=int64), 'cur_cost': 99565.0}
2025-08-01 17:46:15,245 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-01 17:46:15,246 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:15,247 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:15,248 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 103377.0
2025-08-01 17:46:15,383 - ExploitationExpert - INFO - res_population_num: 12
2025-08-01 17:46:15,383 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0, 9521, 9521]
2025-08-01 17:46:15,384 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 17:46:15,392 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:15,392 - ExploitationExpert - INFO - populations: [{'tour': array([40, 62, 23,  6, 43, 46, 15,  9, 13, 32, 59,  5, 53, 17, 63, 44, 45,
       28, 35, 57, 19, 55, 41,  1,  2, 47, 31, 29,  8, 39, 27, 52, 58, 22,
       34, 10, 18, 12, 24, 36,  4, 14,  7, 26, 25, 33, 20, 37, 61, 48, 30,
       50,  0, 51, 54, 60, 56, 11, 65, 64,  3, 38, 21, 42, 49, 16],
      dtype=int64), 'cur_cost': 100033.0}, {'tour': array([55, 24, 63,  1, 26, 54, 64, 42, 18, 60, 16,  7, 29, 14, 34, 22, 12,
       58,  3, 53, 10, 17, 28, 48, 41, 49, 32, 13, 50,  5, 62, 33,  9, 31,
       19, 37, 25, 21, 39, 51, 52,  8, 35, 57,  0, 36, 20, 61, 11, 56, 59,
        2,  4, 43, 45, 65, 38, 30, 46, 23, 27, 15, 44,  6, 40, 47],
      dtype=int64), 'cur_cost': 100605.0}, {'tour': array([59, 60, 18, 14,  6, 13, 34, 47, 30, 15, 63, 37, 52, 11, 46, 12, 62,
        2, 36, 57, 23, 38,  1, 22, 17, 64, 31, 33, 35,  4, 26, 40, 39, 53,
       16, 49, 56, 43, 42, 32, 48,  9, 29, 55, 65, 27, 58,  5, 50, 45, 44,
       21, 19,  7, 24,  8, 10, 28,  0, 54, 25,  3, 51, 20, 61, 41],
      dtype=int64), 'cur_cost': 111115.0}, {'tour': array([30, 18, 54, 61, 47, 58, 20, 42, 21, 17, 39, 10, 51, 25, 28, 63, 31,
       12,  3, 52, 32, 56, 11, 36, 22, 45, 49, 55, 34, 38, 53,  4,  8, 64,
       14, 65,  9, 16, 60, 46,  5, 57, 48, 37, 26, 15, 33, 13, 27, 40, 41,
       23,  0, 43,  6, 50, 29,  2,  7, 59, 35, 19, 24, 44, 62,  1],
      dtype=int64), 'cur_cost': 111374.0}, {'tour': array([47, 17, 20,  9, 44, 51, 64, 14, 25, 59, 63,  5, 43, 28,  2, 61, 62,
        8, 50, 33, 19, 42, 22, 35, 39, 15, 49, 46, 10, 54, 31, 29, 21, 55,
        3, 26, 30, 52, 53, 23, 18, 58, 38, 27, 56,  1,  0, 11, 37, 36,  7,
       13, 12, 60, 34, 40, 48, 45, 41,  6,  4, 24, 65, 16, 57, 32],
      dtype=int64), 'cur_cost': 96267.0}, {'tour': array([65, 20,  1,  9, 33, 16,  6,  8, 12, 32, 28, 17, 37, 34,  3, 13, 57,
       55, 22, 25, 56, 47, 52, 24, 21, 50, 27, 43, 40, 35, 36, 59, 44,  2,
       42, 63, 26, 15, 18, 31, 45, 38, 39, 64, 62, 30, 49,  5, 46,  4,  0,
       51, 54, 11, 58, 61, 19, 53, 29, 60, 41, 23, 14, 48,  7, 10],
      dtype=int64), 'cur_cost': 104851.0}, {'tour': array([ 7, 51, 20, 24, 31, 45, 57, 10, 62, 49, 54, 58, 32, 18, 39,  8, 53,
       27,  0, 65, 37, 47,  1, 35, 42, 43,  3,  9, 55, 13, 15,  5, 50, 33,
       64, 30, 34, 21, 12,  4, 22, 63, 56, 23, 25, 11, 19, 17, 38, 28,  2,
       16, 41, 48, 36,  6, 40, 46, 61, 26, 14, 59, 44, 29, 52, 60],
      dtype=int64), 'cur_cost': 107109.0}, {'tour': array([54, 11, 36,  4, 65, 45, 49,  0, 37, 55, 14, 13, 43, 15, 38, 60,  3,
       12, 48, 29, 18,  7, 27, 42, 32, 22,  6, 61, 25, 17, 30,  2, 62, 23,
       24,  5, 57, 52, 59, 10, 28, 56, 58,  9, 34, 41, 40, 63, 39, 50, 33,
       19, 44, 51, 21, 64,  1, 31, 16,  8, 20, 53, 26, 35, 46, 47],
      dtype=int64), 'cur_cost': 99565.0}, {'tour': array([56, 29, 38, 55,  6, 27, 35, 49,  3, 64, 43, 18, 41, 32, 39, 10, 25,
       51,  2, 26, 37, 44, 21, 13, 23,  9, 28, 62, 46, 16, 34, 36, 60, 17,
        4, 47,  1, 14, 30, 65, 57, 59, 22, 19, 61, 12, 20, 50, 31, 15, 45,
       53,  8, 24, 33, 42, 54, 40, 48,  7, 58, 63,  0, 11, 52,  5],
      dtype=int64), 'cur_cost': 103377.0}, {'tour': [5, 37, 59, 20, 33, 25, 60, 51, 26, 16, 1, 40, 3, 34, 48, 55, 62, 31, 11, 36, 57, 58, 53, 39, 19, 8, 49, 6, 35, 44, 32, 38, 9, 4, 14, 12, 15, 24, 41, 27, 50, 63, 46, 56, 43, 61, 0, 47, 23, 54, 7, 28, 29, 17, 65, 52, 30, 10, 13, 18, 42, 64, 2, 45, 22, 21], 'cur_cost': 114990.0}]
2025-08-01 17:46:15,400 - ExploitationExpert - INFO - 局部搜索耗时: 0.15秒
2025-08-01 17:46:15,401 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 29, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 29, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:46:15,401 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([56, 29, 38, 55,  6, 27, 35, 49,  3, 64, 43, 18, 41, 32, 39, 10, 25,
       51,  2, 26, 37, 44, 21, 13, 23,  9, 28, 62, 46, 16, 34, 36, 60, 17,
        4, 47,  1, 14, 30, 65, 57, 59, 22, 19, 61, 12, 20, 50, 31, 15, 45,
       53,  8, 24, 33, 42, 54, 40, 48,  7, 58, 63,  0, 11, 52,  5],
      dtype=int64), 'cur_cost': 103377.0}
2025-08-01 17:46:15,402 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-01 17:46:15,402 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:15,402 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:15,403 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 108089.0
2025-08-01 17:46:15,485 - ExploitationExpert - INFO - res_population_num: 12
2025-08-01 17:46:15,485 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0, 9521, 9521]
2025-08-01 17:46:15,485 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 17:46:15,493 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:15,494 - ExploitationExpert - INFO - populations: [{'tour': array([40, 62, 23,  6, 43, 46, 15,  9, 13, 32, 59,  5, 53, 17, 63, 44, 45,
       28, 35, 57, 19, 55, 41,  1,  2, 47, 31, 29,  8, 39, 27, 52, 58, 22,
       34, 10, 18, 12, 24, 36,  4, 14,  7, 26, 25, 33, 20, 37, 61, 48, 30,
       50,  0, 51, 54, 60, 56, 11, 65, 64,  3, 38, 21, 42, 49, 16],
      dtype=int64), 'cur_cost': 100033.0}, {'tour': array([55, 24, 63,  1, 26, 54, 64, 42, 18, 60, 16,  7, 29, 14, 34, 22, 12,
       58,  3, 53, 10, 17, 28, 48, 41, 49, 32, 13, 50,  5, 62, 33,  9, 31,
       19, 37, 25, 21, 39, 51, 52,  8, 35, 57,  0, 36, 20, 61, 11, 56, 59,
        2,  4, 43, 45, 65, 38, 30, 46, 23, 27, 15, 44,  6, 40, 47],
      dtype=int64), 'cur_cost': 100605.0}, {'tour': array([59, 60, 18, 14,  6, 13, 34, 47, 30, 15, 63, 37, 52, 11, 46, 12, 62,
        2, 36, 57, 23, 38,  1, 22, 17, 64, 31, 33, 35,  4, 26, 40, 39, 53,
       16, 49, 56, 43, 42, 32, 48,  9, 29, 55, 65, 27, 58,  5, 50, 45, 44,
       21, 19,  7, 24,  8, 10, 28,  0, 54, 25,  3, 51, 20, 61, 41],
      dtype=int64), 'cur_cost': 111115.0}, {'tour': array([30, 18, 54, 61, 47, 58, 20, 42, 21, 17, 39, 10, 51, 25, 28, 63, 31,
       12,  3, 52, 32, 56, 11, 36, 22, 45, 49, 55, 34, 38, 53,  4,  8, 64,
       14, 65,  9, 16, 60, 46,  5, 57, 48, 37, 26, 15, 33, 13, 27, 40, 41,
       23,  0, 43,  6, 50, 29,  2,  7, 59, 35, 19, 24, 44, 62,  1],
      dtype=int64), 'cur_cost': 111374.0}, {'tour': array([47, 17, 20,  9, 44, 51, 64, 14, 25, 59, 63,  5, 43, 28,  2, 61, 62,
        8, 50, 33, 19, 42, 22, 35, 39, 15, 49, 46, 10, 54, 31, 29, 21, 55,
        3, 26, 30, 52, 53, 23, 18, 58, 38, 27, 56,  1,  0, 11, 37, 36,  7,
       13, 12, 60, 34, 40, 48, 45, 41,  6,  4, 24, 65, 16, 57, 32],
      dtype=int64), 'cur_cost': 96267.0}, {'tour': array([65, 20,  1,  9, 33, 16,  6,  8, 12, 32, 28, 17, 37, 34,  3, 13, 57,
       55, 22, 25, 56, 47, 52, 24, 21, 50, 27, 43, 40, 35, 36, 59, 44,  2,
       42, 63, 26, 15, 18, 31, 45, 38, 39, 64, 62, 30, 49,  5, 46,  4,  0,
       51, 54, 11, 58, 61, 19, 53, 29, 60, 41, 23, 14, 48,  7, 10],
      dtype=int64), 'cur_cost': 104851.0}, {'tour': array([ 7, 51, 20, 24, 31, 45, 57, 10, 62, 49, 54, 58, 32, 18, 39,  8, 53,
       27,  0, 65, 37, 47,  1, 35, 42, 43,  3,  9, 55, 13, 15,  5, 50, 33,
       64, 30, 34, 21, 12,  4, 22, 63, 56, 23, 25, 11, 19, 17, 38, 28,  2,
       16, 41, 48, 36,  6, 40, 46, 61, 26, 14, 59, 44, 29, 52, 60],
      dtype=int64), 'cur_cost': 107109.0}, {'tour': array([54, 11, 36,  4, 65, 45, 49,  0, 37, 55, 14, 13, 43, 15, 38, 60,  3,
       12, 48, 29, 18,  7, 27, 42, 32, 22,  6, 61, 25, 17, 30,  2, 62, 23,
       24,  5, 57, 52, 59, 10, 28, 56, 58,  9, 34, 41, 40, 63, 39, 50, 33,
       19, 44, 51, 21, 64,  1, 31, 16,  8, 20, 53, 26, 35, 46, 47],
      dtype=int64), 'cur_cost': 99565.0}, {'tour': array([56, 29, 38, 55,  6, 27, 35, 49,  3, 64, 43, 18, 41, 32, 39, 10, 25,
       51,  2, 26, 37, 44, 21, 13, 23,  9, 28, 62, 46, 16, 34, 36, 60, 17,
        4, 47,  1, 14, 30, 65, 57, 59, 22, 19, 61, 12, 20, 50, 31, 15, 45,
       53,  8, 24, 33, 42, 54, 40, 48,  7, 58, 63,  0, 11, 52,  5],
      dtype=int64), 'cur_cost': 103377.0}, {'tour': array([13, 25, 26, 19, 12, 48, 29,  2, 10,  6, 28,  1, 37, 21,  4, 58,  5,
       44, 54, 38, 63, 20, 40, 61,  9, 49, 42, 51, 55,  3, 46,  8, 53, 14,
       50, 22, 60, 59, 23, 31, 43, 64, 27, 18, 34, 35, 57, 62, 24, 56, 11,
       15,  0, 32, 30,  7, 33, 17, 39, 52, 47, 16, 45, 65, 36, 41],
      dtype=int64), 'cur_cost': 108089.0}]
2025-08-01 17:46:15,511 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒
2025-08-01 17:46:15,512 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 30, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 30, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:46:15,514 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([13, 25, 26, 19, 12, 48, 29,  2, 10,  6, 28,  1, 37, 21,  4, 58,  5,
       44, 54, 38, 63, 20, 40, 61,  9, 49, 42, 51, 55,  3, 46,  8, 53, 14,
       50, 22, 60, 59, 23, 31, 43, 64, 27, 18, 34, 35, 57, 62, 24, 56, 11,
       15,  0, 32, 30,  7, 33, 17, 39, 52, 47, 16, 45, 65, 36, 41],
      dtype=int64), 'cur_cost': 108089.0}
2025-08-01 17:46:15,520 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([40, 62, 23,  6, 43, 46, 15,  9, 13, 32, 59,  5, 53, 17, 63, 44, 45,
       28, 35, 57, 19, 55, 41,  1,  2, 47, 31, 29,  8, 39, 27, 52, 58, 22,
       34, 10, 18, 12, 24, 36,  4, 14,  7, 26, 25, 33, 20, 37, 61, 48, 30,
       50,  0, 51, 54, 60, 56, 11, 65, 64,  3, 38, 21, 42, 49, 16],
      dtype=int64), 'cur_cost': 100033.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([55, 24, 63,  1, 26, 54, 64, 42, 18, 60, 16,  7, 29, 14, 34, 22, 12,
       58,  3, 53, 10, 17, 28, 48, 41, 49, 32, 13, 50,  5, 62, 33,  9, 31,
       19, 37, 25, 21, 39, 51, 52,  8, 35, 57,  0, 36, 20, 61, 11, 56, 59,
        2,  4, 43, 45, 65, 38, 30, 46, 23, 27, 15, 44,  6, 40, 47],
      dtype=int64), 'cur_cost': 100605.0}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([59, 60, 18, 14,  6, 13, 34, 47, 30, 15, 63, 37, 52, 11, 46, 12, 62,
        2, 36, 57, 23, 38,  1, 22, 17, 64, 31, 33, 35,  4, 26, 40, 39, 53,
       16, 49, 56, 43, 42, 32, 48,  9, 29, 55, 65, 27, 58,  5, 50, 45, 44,
       21, 19,  7, 24,  8, 10, 28,  0, 54, 25,  3, 51, 20, 61, 41],
      dtype=int64), 'cur_cost': 111115.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([30, 18, 54, 61, 47, 58, 20, 42, 21, 17, 39, 10, 51, 25, 28, 63, 31,
       12,  3, 52, 32, 56, 11, 36, 22, 45, 49, 55, 34, 38, 53,  4,  8, 64,
       14, 65,  9, 16, 60, 46,  5, 57, 48, 37, 26, 15, 33, 13, 27, 40, 41,
       23,  0, 43,  6, 50, 29,  2,  7, 59, 35, 19, 24, 44, 62,  1],
      dtype=int64), 'cur_cost': 111374.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([47, 17, 20,  9, 44, 51, 64, 14, 25, 59, 63,  5, 43, 28,  2, 61, 62,
        8, 50, 33, 19, 42, 22, 35, 39, 15, 49, 46, 10, 54, 31, 29, 21, 55,
        3, 26, 30, 52, 53, 23, 18, 58, 38, 27, 56,  1,  0, 11, 37, 36,  7,
       13, 12, 60, 34, 40, 48, 45, 41,  6,  4, 24, 65, 16, 57, 32],
      dtype=int64), 'cur_cost': 96267.0}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([65, 20,  1,  9, 33, 16,  6,  8, 12, 32, 28, 17, 37, 34,  3, 13, 57,
       55, 22, 25, 56, 47, 52, 24, 21, 50, 27, 43, 40, 35, 36, 59, 44,  2,
       42, 63, 26, 15, 18, 31, 45, 38, 39, 64, 62, 30, 49,  5, 46,  4,  0,
       51, 54, 11, 58, 61, 19, 53, 29, 60, 41, 23, 14, 48,  7, 10],
      dtype=int64), 'cur_cost': 104851.0}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 7, 51, 20, 24, 31, 45, 57, 10, 62, 49, 54, 58, 32, 18, 39,  8, 53,
       27,  0, 65, 37, 47,  1, 35, 42, 43,  3,  9, 55, 13, 15,  5, 50, 33,
       64, 30, 34, 21, 12,  4, 22, 63, 56, 23, 25, 11, 19, 17, 38, 28,  2,
       16, 41, 48, 36,  6, 40, 46, 61, 26, 14, 59, 44, 29, 52, 60],
      dtype=int64), 'cur_cost': 107109.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([54, 11, 36,  4, 65, 45, 49,  0, 37, 55, 14, 13, 43, 15, 38, 60,  3,
       12, 48, 29, 18,  7, 27, 42, 32, 22,  6, 61, 25, 17, 30,  2, 62, 23,
       24,  5, 57, 52, 59, 10, 28, 56, 58,  9, 34, 41, 40, 63, 39, 50, 33,
       19, 44, 51, 21, 64,  1, 31, 16,  8, 20, 53, 26, 35, 46, 47],
      dtype=int64), 'cur_cost': 99565.0}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([56, 29, 38, 55,  6, 27, 35, 49,  3, 64, 43, 18, 41, 32, 39, 10, 25,
       51,  2, 26, 37, 44, 21, 13, 23,  9, 28, 62, 46, 16, 34, 36, 60, 17,
        4, 47,  1, 14, 30, 65, 57, 59, 22, 19, 61, 12, 20, 50, 31, 15, 45,
       53,  8, 24, 33, 42, 54, 40, 48,  7, 58, 63,  0, 11, 52,  5],
      dtype=int64), 'cur_cost': 103377.0}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([13, 25, 26, 19, 12, 48, 29,  2, 10,  6, 28,  1, 37, 21,  4, 58,  5,
       44, 54, 38, 63, 20, 40, 61,  9, 49, 42, 51, 55,  3, 46,  8, 53, 14,
       50, 22, 60, 59, 23, 31, 43, 64, 27, 18, 34, 35, 57, 62, 24, 56, 11,
       15,  0, 32, 30,  7, 33, 17, 39, 52, 47, 16, 45, 65, 36, 41],
      dtype=int64), 'cur_cost': 108089.0}}]
2025-08-01 17:46:15,527 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-01 17:46:15,527 - StatsExpert - INFO - 开始统计分析
2025-08-01 17:46:15,533 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=96267.000, 多样性=0.985
2025-08-01 17:46:15,534 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-01 17:46:15,534 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-01 17:46:15,535 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-01 17:46:15,537 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 80, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'slight_improvement', 'improvement_rate': 0.006543660847529195, 'best_improvement': -0.017309704213296136}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.001024590163934433}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 12, 'new_count': 12, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.7378328741965106, 'new_diversity': 0.7378328741965106, 'diversity_change': 0.0}, 'suggestions': ['改进缓慢，建议调整策略平衡或增加扰动强度', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-01 17:46:15,543 - main - INFO - --- Finished Evolution Iteration 3 ---
2025-08-01 17:46:15,543 - main - INFO - composite13_66 开始进化第 4 代
2025-08-01 17:46:15,544 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-01 17:46:15,544 - StatsExpert - INFO - 开始统计分析
2025-08-01 17:46:15,547 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=96267.000, 多样性=0.985
2025-08-01 17:46:15,548 - PathExpert - INFO - 开始路径结构分析
2025-08-01 17:46:15,553 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.985
2025-08-01 17:46:15,553 - EliteExpert - INFO - 开始精英解分析
2025-08-01 17:46:15,562 - EliteExpert - INFO - 精英解分析完成: 精英解数量=12, 多样性=0.738
2025-08-01 17:46:15,565 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-01 17:46:15,566 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-01 17:46:15,566 - LandscapeExpert - INFO - 添加精英解数据: 12个精英解
2025-08-01 17:46:15,567 - LandscapeExpert - INFO - 数据提取成功: 22个路径, 22个适应度值
2025-08-01 17:46:15,659 - core.algorithms.fitness_landscape_analyzer - ERROR - 景观分析失败: '>' not supported between instances of 'str' and 'int'
2025-08-01 17:46:15,659 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-01 17:46:15,660 - LandscapeExpert - INFO - 提取到 12 个精英解
2025-08-01 17:46:15,669 - visualization.landscape_visualizer - INFO - 已添加 12 个精英解标记
2025-08-01 17:46:15,757 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_4_20250801_174615.html
2025-08-01 17:46:15,814 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_4_20250801_174615.html
2025-08-01 17:46:15,816 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 4
2025-08-01 17:46:15,816 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-01 17:46:15,817 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2515秒
2025-08-01 17:46:15,818 - main - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754041575.6594815, 'performance_metrics': {}}}
2025-08-01 17:46:15,819 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-01 17:46:15,819 - StrategyExpert - INFO - 开始策略分配分析
2025-08-01 17:46:15,820 - StrategyExpert - INFO - 消融实验模式：使用纯exploit策略
2025-08-01 17:46:15,820 - StrategyExpert - INFO - 策略分配分析完成（消融实验模式）
2025-08-01 17:46:15,820 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-08-01 17:46:15,820 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 消融实验模式：所有10个个体都使用exploit策略
2025-08-01 17:46:15,821 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-01 17:46:15,821 - main - INFO - 策略分配: ['exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-08-01 17:46:15,821 - main - INFO - 策略分配完整报告: 消融实验模式：所有10个个体都使用exploit策略
2025-08-01 17:46:15,822 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-01 17:46:15,822 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-01 17:46:15,822 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:15,822 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:15,823 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 97336.0
2025-08-01 17:46:15,911 - ExploitationExpert - INFO - res_population_num: 12
2025-08-01 17:46:15,912 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0]
2025-08-01 17:46:15,913 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64)]
2025-08-01 17:46:15,922 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:15,922 - ExploitationExpert - INFO - populations: [{'tour': array([20, 58, 63, 30, 59, 34,  4, 44, 57, 39, 49,  5, 46, 45, 40, 28, 42,
       12, 51,  1, 38, 23, 47, 36,  2, 35, 48, 10, 41, 13, 33, 37, 21,  9,
        6,  0, 18,  7, 64, 62, 61, 17, 26, 29, 19, 50,  8, 52, 54, 11,  3,
       53, 15, 16, 65, 56, 24, 32, 31, 55, 27, 43, 22, 14, 60, 25],
      dtype=int64), 'cur_cost': 97336.0}, {'tour': [55, 24, 63, 1, 26, 54, 64, 42, 18, 60, 16, 7, 29, 14, 34, 22, 12, 58, 3, 53, 10, 17, 28, 48, 41, 49, 32, 13, 50, 5, 62, 33, 9, 31, 19, 37, 25, 21, 39, 51, 52, 8, 35, 57, 0, 36, 20, 61, 11, 56, 59, 2, 4, 43, 45, 65, 38, 30, 46, 23, 27, 15, 44, 6, 40, 47], 'cur_cost': 100605.0}, {'tour': [59, 60, 18, 14, 6, 13, 34, 47, 30, 15, 63, 37, 52, 11, 46, 12, 62, 2, 36, 57, 23, 38, 1, 22, 17, 64, 31, 33, 35, 4, 26, 40, 39, 53, 16, 49, 56, 43, 42, 32, 48, 9, 29, 55, 65, 27, 58, 5, 50, 45, 44, 21, 19, 7, 24, 8, 10, 28, 0, 54, 25, 3, 51, 20, 61, 41], 'cur_cost': 111115.0}, {'tour': [30, 18, 54, 61, 47, 58, 20, 42, 21, 17, 39, 10, 51, 25, 28, 63, 31, 12, 3, 52, 32, 56, 11, 36, 22, 45, 49, 55, 34, 38, 53, 4, 8, 64, 14, 65, 9, 16, 60, 46, 5, 57, 48, 37, 26, 15, 33, 13, 27, 40, 41, 23, 0, 43, 6, 50, 29, 2, 7, 59, 35, 19, 24, 44, 62, 1], 'cur_cost': 111374.0}, {'tour': [47, 17, 20, 9, 44, 51, 64, 14, 25, 59, 63, 5, 43, 28, 2, 61, 62, 8, 50, 33, 19, 42, 22, 35, 39, 15, 49, 46, 10, 54, 31, 29, 21, 55, 3, 26, 30, 52, 53, 23, 18, 58, 38, 27, 56, 1, 0, 11, 37, 36, 7, 13, 12, 60, 34, 40, 48, 45, 41, 6, 4, 24, 65, 16, 57, 32], 'cur_cost': 96267.0}, {'tour': [65, 20, 1, 9, 33, 16, 6, 8, 12, 32, 28, 17, 37, 34, 3, 13, 57, 55, 22, 25, 56, 47, 52, 24, 21, 50, 27, 43, 40, 35, 36, 59, 44, 2, 42, 63, 26, 15, 18, 31, 45, 38, 39, 64, 62, 30, 49, 5, 46, 4, 0, 51, 54, 11, 58, 61, 19, 53, 29, 60, 41, 23, 14, 48, 7, 10], 'cur_cost': 104851.0}, {'tour': [7, 51, 20, 24, 31, 45, 57, 10, 62, 49, 54, 58, 32, 18, 39, 8, 53, 27, 0, 65, 37, 47, 1, 35, 42, 43, 3, 9, 55, 13, 15, 5, 50, 33, 64, 30, 34, 21, 12, 4, 22, 63, 56, 23, 25, 11, 19, 17, 38, 28, 2, 16, 41, 48, 36, 6, 40, 46, 61, 26, 14, 59, 44, 29, 52, 60], 'cur_cost': 107109.0}, {'tour': [54, 11, 36, 4, 65, 45, 49, 0, 37, 55, 14, 13, 43, 15, 38, 60, 3, 12, 48, 29, 18, 7, 27, 42, 32, 22, 6, 61, 25, 17, 30, 2, 62, 23, 24, 5, 57, 52, 59, 10, 28, 56, 58, 9, 34, 41, 40, 63, 39, 50, 33, 19, 44, 51, 21, 64, 1, 31, 16, 8, 20, 53, 26, 35, 46, 47], 'cur_cost': 99565.0}, {'tour': [56, 29, 38, 55, 6, 27, 35, 49, 3, 64, 43, 18, 41, 32, 39, 10, 25, 51, 2, 26, 37, 44, 21, 13, 23, 9, 28, 62, 46, 16, 34, 36, 60, 17, 4, 47, 1, 14, 30, 65, 57, 59, 22, 19, 61, 12, 20, 50, 31, 15, 45, 53, 8, 24, 33, 42, 54, 40, 48, 7, 58, 63, 0, 11, 52, 5], 'cur_cost': 103377.0}, {'tour': [13, 25, 26, 19, 12, 48, 29, 2, 10, 6, 28, 1, 37, 21, 4, 58, 5, 44, 54, 38, 63, 20, 40, 61, 9, 49, 42, 51, 55, 3, 46, 8, 53, 14, 50, 22, 60, 59, 23, 31, 43, 64, 27, 18, 34, 35, 57, 62, 24, 56, 11, 15, 0, 32, 30, 7, 33, 17, 39, 52, 47, 16, 45, 65, 36, 41], 'cur_cost': 108089.0}]
2025-08-01 17:46:15,925 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-01 17:46:15,925 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 31, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 31, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:46:15,926 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([20, 58, 63, 30, 59, 34,  4, 44, 57, 39, 49,  5, 46, 45, 40, 28, 42,
       12, 51,  1, 38, 23, 47, 36,  2, 35, 48, 10, 41, 13, 33, 37, 21,  9,
        6,  0, 18,  7, 64, 62, 61, 17, 26, 29, 19, 50,  8, 52, 54, 11,  3,
       53, 15, 16, 65, 56, 24, 32, 31, 55, 27, 43, 22, 14, 60, 25],
      dtype=int64), 'cur_cost': 97336.0}
2025-08-01 17:46:15,926 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-01 17:46:15,927 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:15,927 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:15,927 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 93840.0
2025-08-01 17:46:16,040 - ExploitationExpert - INFO - res_population_num: 12
2025-08-01 17:46:16,041 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0]
2025-08-01 17:46:16,042 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64)]
2025-08-01 17:46:16,052 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:16,052 - ExploitationExpert - INFO - populations: [{'tour': array([20, 58, 63, 30, 59, 34,  4, 44, 57, 39, 49,  5, 46, 45, 40, 28, 42,
       12, 51,  1, 38, 23, 47, 36,  2, 35, 48, 10, 41, 13, 33, 37, 21,  9,
        6,  0, 18,  7, 64, 62, 61, 17, 26, 29, 19, 50,  8, 52, 54, 11,  3,
       53, 15, 16, 65, 56, 24, 32, 31, 55, 27, 43, 22, 14, 60, 25],
      dtype=int64), 'cur_cost': 97336.0}, {'tour': array([20, 29, 11, 15, 44, 25, 19,  6, 12, 49, 41, 40,  7, 21, 37, 34, 48,
       57,  9, 55, 13, 18, 62, 47, 36, 60, 58,  8, 61, 54, 53, 31,  2, 27,
       10,  5, 22, 56, 63, 52,  1, 35, 16, 38, 46,  4, 50, 30, 59,  0, 64,
       23, 17, 42, 26, 32, 51, 43, 28, 65, 24, 33, 45,  3, 39, 14],
      dtype=int64), 'cur_cost': 93840.0}, {'tour': [59, 60, 18, 14, 6, 13, 34, 47, 30, 15, 63, 37, 52, 11, 46, 12, 62, 2, 36, 57, 23, 38, 1, 22, 17, 64, 31, 33, 35, 4, 26, 40, 39, 53, 16, 49, 56, 43, 42, 32, 48, 9, 29, 55, 65, 27, 58, 5, 50, 45, 44, 21, 19, 7, 24, 8, 10, 28, 0, 54, 25, 3, 51, 20, 61, 41], 'cur_cost': 111115.0}, {'tour': [30, 18, 54, 61, 47, 58, 20, 42, 21, 17, 39, 10, 51, 25, 28, 63, 31, 12, 3, 52, 32, 56, 11, 36, 22, 45, 49, 55, 34, 38, 53, 4, 8, 64, 14, 65, 9, 16, 60, 46, 5, 57, 48, 37, 26, 15, 33, 13, 27, 40, 41, 23, 0, 43, 6, 50, 29, 2, 7, 59, 35, 19, 24, 44, 62, 1], 'cur_cost': 111374.0}, {'tour': [47, 17, 20, 9, 44, 51, 64, 14, 25, 59, 63, 5, 43, 28, 2, 61, 62, 8, 50, 33, 19, 42, 22, 35, 39, 15, 49, 46, 10, 54, 31, 29, 21, 55, 3, 26, 30, 52, 53, 23, 18, 58, 38, 27, 56, 1, 0, 11, 37, 36, 7, 13, 12, 60, 34, 40, 48, 45, 41, 6, 4, 24, 65, 16, 57, 32], 'cur_cost': 96267.0}, {'tour': [65, 20, 1, 9, 33, 16, 6, 8, 12, 32, 28, 17, 37, 34, 3, 13, 57, 55, 22, 25, 56, 47, 52, 24, 21, 50, 27, 43, 40, 35, 36, 59, 44, 2, 42, 63, 26, 15, 18, 31, 45, 38, 39, 64, 62, 30, 49, 5, 46, 4, 0, 51, 54, 11, 58, 61, 19, 53, 29, 60, 41, 23, 14, 48, 7, 10], 'cur_cost': 104851.0}, {'tour': [7, 51, 20, 24, 31, 45, 57, 10, 62, 49, 54, 58, 32, 18, 39, 8, 53, 27, 0, 65, 37, 47, 1, 35, 42, 43, 3, 9, 55, 13, 15, 5, 50, 33, 64, 30, 34, 21, 12, 4, 22, 63, 56, 23, 25, 11, 19, 17, 38, 28, 2, 16, 41, 48, 36, 6, 40, 46, 61, 26, 14, 59, 44, 29, 52, 60], 'cur_cost': 107109.0}, {'tour': [54, 11, 36, 4, 65, 45, 49, 0, 37, 55, 14, 13, 43, 15, 38, 60, 3, 12, 48, 29, 18, 7, 27, 42, 32, 22, 6, 61, 25, 17, 30, 2, 62, 23, 24, 5, 57, 52, 59, 10, 28, 56, 58, 9, 34, 41, 40, 63, 39, 50, 33, 19, 44, 51, 21, 64, 1, 31, 16, 8, 20, 53, 26, 35, 46, 47], 'cur_cost': 99565.0}, {'tour': [56, 29, 38, 55, 6, 27, 35, 49, 3, 64, 43, 18, 41, 32, 39, 10, 25, 51, 2, 26, 37, 44, 21, 13, 23, 9, 28, 62, 46, 16, 34, 36, 60, 17, 4, 47, 1, 14, 30, 65, 57, 59, 22, 19, 61, 12, 20, 50, 31, 15, 45, 53, 8, 24, 33, 42, 54, 40, 48, 7, 58, 63, 0, 11, 52, 5], 'cur_cost': 103377.0}, {'tour': [13, 25, 26, 19, 12, 48, 29, 2, 10, 6, 28, 1, 37, 21, 4, 58, 5, 44, 54, 38, 63, 20, 40, 61, 9, 49, 42, 51, 55, 3, 46, 8, 53, 14, 50, 22, 60, 59, 23, 31, 43, 64, 27, 18, 34, 35, 57, 62, 24, 56, 11, 15, 0, 32, 30, 7, 33, 17, 39, 52, 47, 16, 45, 65, 36, 41], 'cur_cost': 108089.0}]
2025-08-01 17:46:16,056 - ExploitationExpert - INFO - 局部搜索耗时: 0.13秒
2025-08-01 17:46:16,056 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 32, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 32, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:46:16,057 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([20, 29, 11, 15, 44, 25, 19,  6, 12, 49, 41, 40,  7, 21, 37, 34, 48,
       57,  9, 55, 13, 18, 62, 47, 36, 60, 58,  8, 61, 54, 53, 31,  2, 27,
       10,  5, 22, 56, 63, 52,  1, 35, 16, 38, 46,  4, 50, 30, 59,  0, 64,
       23, 17, 42, 26, 32, 51, 43, 28, 65, 24, 33, 45,  3, 39, 14],
      dtype=int64), 'cur_cost': 93840.0}
2025-08-01 17:46:16,057 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-01 17:46:16,058 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:16,058 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:16,059 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 97557.0
2025-08-01 17:46:16,148 - ExploitationExpert - INFO - res_population_num: 12
2025-08-01 17:46:16,148 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0]
2025-08-01 17:46:16,148 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64)]
2025-08-01 17:46:16,156 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:16,157 - ExploitationExpert - INFO - populations: [{'tour': array([20, 58, 63, 30, 59, 34,  4, 44, 57, 39, 49,  5, 46, 45, 40, 28, 42,
       12, 51,  1, 38, 23, 47, 36,  2, 35, 48, 10, 41, 13, 33, 37, 21,  9,
        6,  0, 18,  7, 64, 62, 61, 17, 26, 29, 19, 50,  8, 52, 54, 11,  3,
       53, 15, 16, 65, 56, 24, 32, 31, 55, 27, 43, 22, 14, 60, 25],
      dtype=int64), 'cur_cost': 97336.0}, {'tour': array([20, 29, 11, 15, 44, 25, 19,  6, 12, 49, 41, 40,  7, 21, 37, 34, 48,
       57,  9, 55, 13, 18, 62, 47, 36, 60, 58,  8, 61, 54, 53, 31,  2, 27,
       10,  5, 22, 56, 63, 52,  1, 35, 16, 38, 46,  4, 50, 30, 59,  0, 64,
       23, 17, 42, 26, 32, 51, 43, 28, 65, 24, 33, 45,  3, 39, 14],
      dtype=int64), 'cur_cost': 93840.0}, {'tour': array([30, 18, 11, 33, 54, 59,  0, 63, 42, 13, 52, 10, 62, 35, 56, 32,  9,
        3, 15, 34, 51, 47, 39, 25, 16, 31, 48, 49, 41, 17, 46,  1, 57, 12,
       24, 38, 36, 45, 40, 55,  2, 27,  6,  5,  4, 53,  8, 22, 65, 64, 23,
       21,  7, 28, 44, 29, 19, 58, 50, 26, 60, 61, 37, 43, 14, 20],
      dtype=int64), 'cur_cost': 97557.0}, {'tour': [30, 18, 54, 61, 47, 58, 20, 42, 21, 17, 39, 10, 51, 25, 28, 63, 31, 12, 3, 52, 32, 56, 11, 36, 22, 45, 49, 55, 34, 38, 53, 4, 8, 64, 14, 65, 9, 16, 60, 46, 5, 57, 48, 37, 26, 15, 33, 13, 27, 40, 41, 23, 0, 43, 6, 50, 29, 2, 7, 59, 35, 19, 24, 44, 62, 1], 'cur_cost': 111374.0}, {'tour': [47, 17, 20, 9, 44, 51, 64, 14, 25, 59, 63, 5, 43, 28, 2, 61, 62, 8, 50, 33, 19, 42, 22, 35, 39, 15, 49, 46, 10, 54, 31, 29, 21, 55, 3, 26, 30, 52, 53, 23, 18, 58, 38, 27, 56, 1, 0, 11, 37, 36, 7, 13, 12, 60, 34, 40, 48, 45, 41, 6, 4, 24, 65, 16, 57, 32], 'cur_cost': 96267.0}, {'tour': [65, 20, 1, 9, 33, 16, 6, 8, 12, 32, 28, 17, 37, 34, 3, 13, 57, 55, 22, 25, 56, 47, 52, 24, 21, 50, 27, 43, 40, 35, 36, 59, 44, 2, 42, 63, 26, 15, 18, 31, 45, 38, 39, 64, 62, 30, 49, 5, 46, 4, 0, 51, 54, 11, 58, 61, 19, 53, 29, 60, 41, 23, 14, 48, 7, 10], 'cur_cost': 104851.0}, {'tour': [7, 51, 20, 24, 31, 45, 57, 10, 62, 49, 54, 58, 32, 18, 39, 8, 53, 27, 0, 65, 37, 47, 1, 35, 42, 43, 3, 9, 55, 13, 15, 5, 50, 33, 64, 30, 34, 21, 12, 4, 22, 63, 56, 23, 25, 11, 19, 17, 38, 28, 2, 16, 41, 48, 36, 6, 40, 46, 61, 26, 14, 59, 44, 29, 52, 60], 'cur_cost': 107109.0}, {'tour': [54, 11, 36, 4, 65, 45, 49, 0, 37, 55, 14, 13, 43, 15, 38, 60, 3, 12, 48, 29, 18, 7, 27, 42, 32, 22, 6, 61, 25, 17, 30, 2, 62, 23, 24, 5, 57, 52, 59, 10, 28, 56, 58, 9, 34, 41, 40, 63, 39, 50, 33, 19, 44, 51, 21, 64, 1, 31, 16, 8, 20, 53, 26, 35, 46, 47], 'cur_cost': 99565.0}, {'tour': [56, 29, 38, 55, 6, 27, 35, 49, 3, 64, 43, 18, 41, 32, 39, 10, 25, 51, 2, 26, 37, 44, 21, 13, 23, 9, 28, 62, 46, 16, 34, 36, 60, 17, 4, 47, 1, 14, 30, 65, 57, 59, 22, 19, 61, 12, 20, 50, 31, 15, 45, 53, 8, 24, 33, 42, 54, 40, 48, 7, 58, 63, 0, 11, 52, 5], 'cur_cost': 103377.0}, {'tour': [13, 25, 26, 19, 12, 48, 29, 2, 10, 6, 28, 1, 37, 21, 4, 58, 5, 44, 54, 38, 63, 20, 40, 61, 9, 49, 42, 51, 55, 3, 46, 8, 53, 14, 50, 22, 60, 59, 23, 31, 43, 64, 27, 18, 34, 35, 57, 62, 24, 56, 11, 15, 0, 32, 30, 7, 33, 17, 39, 52, 47, 16, 45, 65, 36, 41], 'cur_cost': 108089.0}]
2025-08-01 17:46:16,161 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-01 17:46:16,162 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 33, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 33, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:46:16,163 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([30, 18, 11, 33, 54, 59,  0, 63, 42, 13, 52, 10, 62, 35, 56, 32,  9,
        3, 15, 34, 51, 47, 39, 25, 16, 31, 48, 49, 41, 17, 46,  1, 57, 12,
       24, 38, 36, 45, 40, 55,  2, 27,  6,  5,  4, 53,  8, 22, 65, 64, 23,
       21,  7, 28, 44, 29, 19, 58, 50, 26, 60, 61, 37, 43, 14, 20],
      dtype=int64), 'cur_cost': 97557.0}
2025-08-01 17:46:16,164 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-01 17:46:16,164 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:16,165 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:16,165 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 103490.0
2025-08-01 17:46:16,253 - ExploitationExpert - INFO - res_population_num: 12
2025-08-01 17:46:16,254 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0]
2025-08-01 17:46:16,254 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64)]
2025-08-01 17:46:16,264 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:16,265 - ExploitationExpert - INFO - populations: [{'tour': array([20, 58, 63, 30, 59, 34,  4, 44, 57, 39, 49,  5, 46, 45, 40, 28, 42,
       12, 51,  1, 38, 23, 47, 36,  2, 35, 48, 10, 41, 13, 33, 37, 21,  9,
        6,  0, 18,  7, 64, 62, 61, 17, 26, 29, 19, 50,  8, 52, 54, 11,  3,
       53, 15, 16, 65, 56, 24, 32, 31, 55, 27, 43, 22, 14, 60, 25],
      dtype=int64), 'cur_cost': 97336.0}, {'tour': array([20, 29, 11, 15, 44, 25, 19,  6, 12, 49, 41, 40,  7, 21, 37, 34, 48,
       57,  9, 55, 13, 18, 62, 47, 36, 60, 58,  8, 61, 54, 53, 31,  2, 27,
       10,  5, 22, 56, 63, 52,  1, 35, 16, 38, 46,  4, 50, 30, 59,  0, 64,
       23, 17, 42, 26, 32, 51, 43, 28, 65, 24, 33, 45,  3, 39, 14],
      dtype=int64), 'cur_cost': 93840.0}, {'tour': array([30, 18, 11, 33, 54, 59,  0, 63, 42, 13, 52, 10, 62, 35, 56, 32,  9,
        3, 15, 34, 51, 47, 39, 25, 16, 31, 48, 49, 41, 17, 46,  1, 57, 12,
       24, 38, 36, 45, 40, 55,  2, 27,  6,  5,  4, 53,  8, 22, 65, 64, 23,
       21,  7, 28, 44, 29, 19, 58, 50, 26, 60, 61, 37, 43, 14, 20],
      dtype=int64), 'cur_cost': 97557.0}, {'tour': array([46, 48, 29, 40,  2,  3, 30, 64,  7, 27, 20, 52, 55, 60, 23, 15, 19,
       56, 54, 53,  9, 17, 49, 28, 14, 24, 39, 58, 59, 10, 35, 26, 62, 33,
       50, 61, 31, 22, 41, 36, 44,  1, 12,  8, 57, 51, 45, 43, 13, 18, 38,
       32, 47,  0, 16, 65, 34,  6,  5, 42, 11, 25,  4, 37, 63, 21],
      dtype=int64), 'cur_cost': 103490.0}, {'tour': [47, 17, 20, 9, 44, 51, 64, 14, 25, 59, 63, 5, 43, 28, 2, 61, 62, 8, 50, 33, 19, 42, 22, 35, 39, 15, 49, 46, 10, 54, 31, 29, 21, 55, 3, 26, 30, 52, 53, 23, 18, 58, 38, 27, 56, 1, 0, 11, 37, 36, 7, 13, 12, 60, 34, 40, 48, 45, 41, 6, 4, 24, 65, 16, 57, 32], 'cur_cost': 96267.0}, {'tour': [65, 20, 1, 9, 33, 16, 6, 8, 12, 32, 28, 17, 37, 34, 3, 13, 57, 55, 22, 25, 56, 47, 52, 24, 21, 50, 27, 43, 40, 35, 36, 59, 44, 2, 42, 63, 26, 15, 18, 31, 45, 38, 39, 64, 62, 30, 49, 5, 46, 4, 0, 51, 54, 11, 58, 61, 19, 53, 29, 60, 41, 23, 14, 48, 7, 10], 'cur_cost': 104851.0}, {'tour': [7, 51, 20, 24, 31, 45, 57, 10, 62, 49, 54, 58, 32, 18, 39, 8, 53, 27, 0, 65, 37, 47, 1, 35, 42, 43, 3, 9, 55, 13, 15, 5, 50, 33, 64, 30, 34, 21, 12, 4, 22, 63, 56, 23, 25, 11, 19, 17, 38, 28, 2, 16, 41, 48, 36, 6, 40, 46, 61, 26, 14, 59, 44, 29, 52, 60], 'cur_cost': 107109.0}, {'tour': [54, 11, 36, 4, 65, 45, 49, 0, 37, 55, 14, 13, 43, 15, 38, 60, 3, 12, 48, 29, 18, 7, 27, 42, 32, 22, 6, 61, 25, 17, 30, 2, 62, 23, 24, 5, 57, 52, 59, 10, 28, 56, 58, 9, 34, 41, 40, 63, 39, 50, 33, 19, 44, 51, 21, 64, 1, 31, 16, 8, 20, 53, 26, 35, 46, 47], 'cur_cost': 99565.0}, {'tour': [56, 29, 38, 55, 6, 27, 35, 49, 3, 64, 43, 18, 41, 32, 39, 10, 25, 51, 2, 26, 37, 44, 21, 13, 23, 9, 28, 62, 46, 16, 34, 36, 60, 17, 4, 47, 1, 14, 30, 65, 57, 59, 22, 19, 61, 12, 20, 50, 31, 15, 45, 53, 8, 24, 33, 42, 54, 40, 48, 7, 58, 63, 0, 11, 52, 5], 'cur_cost': 103377.0}, {'tour': [13, 25, 26, 19, 12, 48, 29, 2, 10, 6, 28, 1, 37, 21, 4, 58, 5, 44, 54, 38, 63, 20, 40, 61, 9, 49, 42, 51, 55, 3, 46, 8, 53, 14, 50, 22, 60, 59, 23, 31, 43, 64, 27, 18, 34, 35, 57, 62, 24, 56, 11, 15, 0, 32, 30, 7, 33, 17, 39, 52, 47, 16, 45, 65, 36, 41], 'cur_cost': 108089.0}]
2025-08-01 17:46:16,270 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒
2025-08-01 17:46:16,272 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 34, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 34, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:46:16,275 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([46, 48, 29, 40,  2,  3, 30, 64,  7, 27, 20, 52, 55, 60, 23, 15, 19,
       56, 54, 53,  9, 17, 49, 28, 14, 24, 39, 58, 59, 10, 35, 26, 62, 33,
       50, 61, 31, 22, 41, 36, 44,  1, 12,  8, 57, 51, 45, 43, 13, 18, 38,
       32, 47,  0, 16, 65, 34,  6,  5, 42, 11, 25,  4, 37, 63, 21],
      dtype=int64), 'cur_cost': 103490.0}
2025-08-01 17:46:16,276 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-01 17:46:16,276 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:16,277 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:16,277 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 109600.0
2025-08-01 17:46:16,389 - ExploitationExpert - INFO - res_population_num: 13
2025-08-01 17:46:16,390 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0, 9521]
2025-08-01 17:46:16,390 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-08-01 17:46:16,401 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:16,403 - ExploitationExpert - INFO - populations: [{'tour': array([20, 58, 63, 30, 59, 34,  4, 44, 57, 39, 49,  5, 46, 45, 40, 28, 42,
       12, 51,  1, 38, 23, 47, 36,  2, 35, 48, 10, 41, 13, 33, 37, 21,  9,
        6,  0, 18,  7, 64, 62, 61, 17, 26, 29, 19, 50,  8, 52, 54, 11,  3,
       53, 15, 16, 65, 56, 24, 32, 31, 55, 27, 43, 22, 14, 60, 25],
      dtype=int64), 'cur_cost': 97336.0}, {'tour': array([20, 29, 11, 15, 44, 25, 19,  6, 12, 49, 41, 40,  7, 21, 37, 34, 48,
       57,  9, 55, 13, 18, 62, 47, 36, 60, 58,  8, 61, 54, 53, 31,  2, 27,
       10,  5, 22, 56, 63, 52,  1, 35, 16, 38, 46,  4, 50, 30, 59,  0, 64,
       23, 17, 42, 26, 32, 51, 43, 28, 65, 24, 33, 45,  3, 39, 14],
      dtype=int64), 'cur_cost': 93840.0}, {'tour': array([30, 18, 11, 33, 54, 59,  0, 63, 42, 13, 52, 10, 62, 35, 56, 32,  9,
        3, 15, 34, 51, 47, 39, 25, 16, 31, 48, 49, 41, 17, 46,  1, 57, 12,
       24, 38, 36, 45, 40, 55,  2, 27,  6,  5,  4, 53,  8, 22, 65, 64, 23,
       21,  7, 28, 44, 29, 19, 58, 50, 26, 60, 61, 37, 43, 14, 20],
      dtype=int64), 'cur_cost': 97557.0}, {'tour': array([46, 48, 29, 40,  2,  3, 30, 64,  7, 27, 20, 52, 55, 60, 23, 15, 19,
       56, 54, 53,  9, 17, 49, 28, 14, 24, 39, 58, 59, 10, 35, 26, 62, 33,
       50, 61, 31, 22, 41, 36, 44,  1, 12,  8, 57, 51, 45, 43, 13, 18, 38,
       32, 47,  0, 16, 65, 34,  6,  5, 42, 11, 25,  4, 37, 63, 21],
      dtype=int64), 'cur_cost': 103490.0}, {'tour': array([55, 20, 62, 49, 13, 43, 38,  2, 41, 15,  8, 46, 25, 23, 34, 27, 52,
       33, 24, 63, 54,  6,  3, 14, 42, 12, 37, 17, 56, 29, 18, 59, 26, 51,
       58, 22, 28, 64, 40, 61, 16, 57, 44, 48, 11, 53,  7, 31, 32, 50,  9,
        4, 10, 21, 60, 65, 30,  0, 36, 47,  5, 19, 35, 39, 45,  1],
      dtype=int64), 'cur_cost': 109600.0}, {'tour': [65, 20, 1, 9, 33, 16, 6, 8, 12, 32, 28, 17, 37, 34, 3, 13, 57, 55, 22, 25, 56, 47, 52, 24, 21, 50, 27, 43, 40, 35, 36, 59, 44, 2, 42, 63, 26, 15, 18, 31, 45, 38, 39, 64, 62, 30, 49, 5, 46, 4, 0, 51, 54, 11, 58, 61, 19, 53, 29, 60, 41, 23, 14, 48, 7, 10], 'cur_cost': 104851.0}, {'tour': [7, 51, 20, 24, 31, 45, 57, 10, 62, 49, 54, 58, 32, 18, 39, 8, 53, 27, 0, 65, 37, 47, 1, 35, 42, 43, 3, 9, 55, 13, 15, 5, 50, 33, 64, 30, 34, 21, 12, 4, 22, 63, 56, 23, 25, 11, 19, 17, 38, 28, 2, 16, 41, 48, 36, 6, 40, 46, 61, 26, 14, 59, 44, 29, 52, 60], 'cur_cost': 107109.0}, {'tour': [54, 11, 36, 4, 65, 45, 49, 0, 37, 55, 14, 13, 43, 15, 38, 60, 3, 12, 48, 29, 18, 7, 27, 42, 32, 22, 6, 61, 25, 17, 30, 2, 62, 23, 24, 5, 57, 52, 59, 10, 28, 56, 58, 9, 34, 41, 40, 63, 39, 50, 33, 19, 44, 51, 21, 64, 1, 31, 16, 8, 20, 53, 26, 35, 46, 47], 'cur_cost': 99565.0}, {'tour': [56, 29, 38, 55, 6, 27, 35, 49, 3, 64, 43, 18, 41, 32, 39, 10, 25, 51, 2, 26, 37, 44, 21, 13, 23, 9, 28, 62, 46, 16, 34, 36, 60, 17, 4, 47, 1, 14, 30, 65, 57, 59, 22, 19, 61, 12, 20, 50, 31, 15, 45, 53, 8, 24, 33, 42, 54, 40, 48, 7, 58, 63, 0, 11, 52, 5], 'cur_cost': 103377.0}, {'tour': [13, 25, 26, 19, 12, 48, 29, 2, 10, 6, 28, 1, 37, 21, 4, 58, 5, 44, 54, 38, 63, 20, 40, 61, 9, 49, 42, 51, 55, 3, 46, 8, 53, 14, 50, 22, 60, 59, 23, 31, 43, 64, 27, 18, 34, 35, 57, 62, 24, 56, 11, 15, 0, 32, 30, 7, 33, 17, 39, 52, 47, 16, 45, 65, 36, 41], 'cur_cost': 108089.0}]
2025-08-01 17:46:16,413 - ExploitationExpert - INFO - 局部搜索耗时: 0.14秒
2025-08-01 17:46:16,414 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 35, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 35, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:46:16,415 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([55, 20, 62, 49, 13, 43, 38,  2, 41, 15,  8, 46, 25, 23, 34, 27, 52,
       33, 24, 63, 54,  6,  3, 14, 42, 12, 37, 17, 56, 29, 18, 59, 26, 51,
       58, 22, 28, 64, 40, 61, 16, 57, 44, 48, 11, 53,  7, 31, 32, 50,  9,
        4, 10, 21, 60, 65, 30,  0, 36, 47,  5, 19, 35, 39, 45,  1],
      dtype=int64), 'cur_cost': 109600.0}
2025-08-01 17:46:16,416 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-01 17:46:16,417 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:16,417 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:16,418 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 113542.0
2025-08-01 17:46:16,524 - ExploitationExpert - INFO - res_population_num: 13
2025-08-01 17:46:16,524 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0, 9521]
2025-08-01 17:46:16,524 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-08-01 17:46:16,533 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:16,534 - ExploitationExpert - INFO - populations: [{'tour': array([20, 58, 63, 30, 59, 34,  4, 44, 57, 39, 49,  5, 46, 45, 40, 28, 42,
       12, 51,  1, 38, 23, 47, 36,  2, 35, 48, 10, 41, 13, 33, 37, 21,  9,
        6,  0, 18,  7, 64, 62, 61, 17, 26, 29, 19, 50,  8, 52, 54, 11,  3,
       53, 15, 16, 65, 56, 24, 32, 31, 55, 27, 43, 22, 14, 60, 25],
      dtype=int64), 'cur_cost': 97336.0}, {'tour': array([20, 29, 11, 15, 44, 25, 19,  6, 12, 49, 41, 40,  7, 21, 37, 34, 48,
       57,  9, 55, 13, 18, 62, 47, 36, 60, 58,  8, 61, 54, 53, 31,  2, 27,
       10,  5, 22, 56, 63, 52,  1, 35, 16, 38, 46,  4, 50, 30, 59,  0, 64,
       23, 17, 42, 26, 32, 51, 43, 28, 65, 24, 33, 45,  3, 39, 14],
      dtype=int64), 'cur_cost': 93840.0}, {'tour': array([30, 18, 11, 33, 54, 59,  0, 63, 42, 13, 52, 10, 62, 35, 56, 32,  9,
        3, 15, 34, 51, 47, 39, 25, 16, 31, 48, 49, 41, 17, 46,  1, 57, 12,
       24, 38, 36, 45, 40, 55,  2, 27,  6,  5,  4, 53,  8, 22, 65, 64, 23,
       21,  7, 28, 44, 29, 19, 58, 50, 26, 60, 61, 37, 43, 14, 20],
      dtype=int64), 'cur_cost': 97557.0}, {'tour': array([46, 48, 29, 40,  2,  3, 30, 64,  7, 27, 20, 52, 55, 60, 23, 15, 19,
       56, 54, 53,  9, 17, 49, 28, 14, 24, 39, 58, 59, 10, 35, 26, 62, 33,
       50, 61, 31, 22, 41, 36, 44,  1, 12,  8, 57, 51, 45, 43, 13, 18, 38,
       32, 47,  0, 16, 65, 34,  6,  5, 42, 11, 25,  4, 37, 63, 21],
      dtype=int64), 'cur_cost': 103490.0}, {'tour': array([55, 20, 62, 49, 13, 43, 38,  2, 41, 15,  8, 46, 25, 23, 34, 27, 52,
       33, 24, 63, 54,  6,  3, 14, 42, 12, 37, 17, 56, 29, 18, 59, 26, 51,
       58, 22, 28, 64, 40, 61, 16, 57, 44, 48, 11, 53,  7, 31, 32, 50,  9,
        4, 10, 21, 60, 65, 30,  0, 36, 47,  5, 19, 35, 39, 45,  1],
      dtype=int64), 'cur_cost': 109600.0}, {'tour': array([19, 63, 49, 37, 42, 62, 21,  7, 51, 64, 20,  1, 22, 59, 38,  2, 58,
       46, 44, 16, 14,  0, 45, 25, 15,  5, 40, 56, 13,  9, 18, 55, 23, 39,
       60, 41,  3, 11, 12, 35, 31, 43, 17,  4, 29, 32, 28, 54, 48,  6, 53,
       27, 61, 36, 50, 57,  8, 10, 65, 26, 24, 34, 33, 30, 52, 47],
      dtype=int64), 'cur_cost': 113542.0}, {'tour': [7, 51, 20, 24, 31, 45, 57, 10, 62, 49, 54, 58, 32, 18, 39, 8, 53, 27, 0, 65, 37, 47, 1, 35, 42, 43, 3, 9, 55, 13, 15, 5, 50, 33, 64, 30, 34, 21, 12, 4, 22, 63, 56, 23, 25, 11, 19, 17, 38, 28, 2, 16, 41, 48, 36, 6, 40, 46, 61, 26, 14, 59, 44, 29, 52, 60], 'cur_cost': 107109.0}, {'tour': [54, 11, 36, 4, 65, 45, 49, 0, 37, 55, 14, 13, 43, 15, 38, 60, 3, 12, 48, 29, 18, 7, 27, 42, 32, 22, 6, 61, 25, 17, 30, 2, 62, 23, 24, 5, 57, 52, 59, 10, 28, 56, 58, 9, 34, 41, 40, 63, 39, 50, 33, 19, 44, 51, 21, 64, 1, 31, 16, 8, 20, 53, 26, 35, 46, 47], 'cur_cost': 99565.0}, {'tour': [56, 29, 38, 55, 6, 27, 35, 49, 3, 64, 43, 18, 41, 32, 39, 10, 25, 51, 2, 26, 37, 44, 21, 13, 23, 9, 28, 62, 46, 16, 34, 36, 60, 17, 4, 47, 1, 14, 30, 65, 57, 59, 22, 19, 61, 12, 20, 50, 31, 15, 45, 53, 8, 24, 33, 42, 54, 40, 48, 7, 58, 63, 0, 11, 52, 5], 'cur_cost': 103377.0}, {'tour': [13, 25, 26, 19, 12, 48, 29, 2, 10, 6, 28, 1, 37, 21, 4, 58, 5, 44, 54, 38, 63, 20, 40, 61, 9, 49, 42, 51, 55, 3, 46, 8, 53, 14, 50, 22, 60, 59, 23, 31, 43, 64, 27, 18, 34, 35, 57, 62, 24, 56, 11, 15, 0, 32, 30, 7, 33, 17, 39, 52, 47, 16, 45, 65, 36, 41], 'cur_cost': 108089.0}]
2025-08-01 17:46:16,543 - ExploitationExpert - INFO - 局部搜索耗时: 0.13秒
2025-08-01 17:46:16,543 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 36, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 36, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:46:16,544 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([19, 63, 49, 37, 42, 62, 21,  7, 51, 64, 20,  1, 22, 59, 38,  2, 58,
       46, 44, 16, 14,  0, 45, 25, 15,  5, 40, 56, 13,  9, 18, 55, 23, 39,
       60, 41,  3, 11, 12, 35, 31, 43, 17,  4, 29, 32, 28, 54, 48,  6, 53,
       27, 61, 36, 50, 57,  8, 10, 65, 26, 24, 34, 33, 30, 52, 47],
      dtype=int64), 'cur_cost': 113542.0}
2025-08-01 17:46:16,545 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-01 17:46:16,546 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:16,546 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:16,547 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 115071.0
2025-08-01 17:46:16,644 - ExploitationExpert - INFO - res_population_num: 13
2025-08-01 17:46:16,645 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0, 9521]
2025-08-01 17:46:16,645 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-08-01 17:46:16,654 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:16,655 - ExploitationExpert - INFO - populations: [{'tour': array([20, 58, 63, 30, 59, 34,  4, 44, 57, 39, 49,  5, 46, 45, 40, 28, 42,
       12, 51,  1, 38, 23, 47, 36,  2, 35, 48, 10, 41, 13, 33, 37, 21,  9,
        6,  0, 18,  7, 64, 62, 61, 17, 26, 29, 19, 50,  8, 52, 54, 11,  3,
       53, 15, 16, 65, 56, 24, 32, 31, 55, 27, 43, 22, 14, 60, 25],
      dtype=int64), 'cur_cost': 97336.0}, {'tour': array([20, 29, 11, 15, 44, 25, 19,  6, 12, 49, 41, 40,  7, 21, 37, 34, 48,
       57,  9, 55, 13, 18, 62, 47, 36, 60, 58,  8, 61, 54, 53, 31,  2, 27,
       10,  5, 22, 56, 63, 52,  1, 35, 16, 38, 46,  4, 50, 30, 59,  0, 64,
       23, 17, 42, 26, 32, 51, 43, 28, 65, 24, 33, 45,  3, 39, 14],
      dtype=int64), 'cur_cost': 93840.0}, {'tour': array([30, 18, 11, 33, 54, 59,  0, 63, 42, 13, 52, 10, 62, 35, 56, 32,  9,
        3, 15, 34, 51, 47, 39, 25, 16, 31, 48, 49, 41, 17, 46,  1, 57, 12,
       24, 38, 36, 45, 40, 55,  2, 27,  6,  5,  4, 53,  8, 22, 65, 64, 23,
       21,  7, 28, 44, 29, 19, 58, 50, 26, 60, 61, 37, 43, 14, 20],
      dtype=int64), 'cur_cost': 97557.0}, {'tour': array([46, 48, 29, 40,  2,  3, 30, 64,  7, 27, 20, 52, 55, 60, 23, 15, 19,
       56, 54, 53,  9, 17, 49, 28, 14, 24, 39, 58, 59, 10, 35, 26, 62, 33,
       50, 61, 31, 22, 41, 36, 44,  1, 12,  8, 57, 51, 45, 43, 13, 18, 38,
       32, 47,  0, 16, 65, 34,  6,  5, 42, 11, 25,  4, 37, 63, 21],
      dtype=int64), 'cur_cost': 103490.0}, {'tour': array([55, 20, 62, 49, 13, 43, 38,  2, 41, 15,  8, 46, 25, 23, 34, 27, 52,
       33, 24, 63, 54,  6,  3, 14, 42, 12, 37, 17, 56, 29, 18, 59, 26, 51,
       58, 22, 28, 64, 40, 61, 16, 57, 44, 48, 11, 53,  7, 31, 32, 50,  9,
        4, 10, 21, 60, 65, 30,  0, 36, 47,  5, 19, 35, 39, 45,  1],
      dtype=int64), 'cur_cost': 109600.0}, {'tour': array([19, 63, 49, 37, 42, 62, 21,  7, 51, 64, 20,  1, 22, 59, 38,  2, 58,
       46, 44, 16, 14,  0, 45, 25, 15,  5, 40, 56, 13,  9, 18, 55, 23, 39,
       60, 41,  3, 11, 12, 35, 31, 43, 17,  4, 29, 32, 28, 54, 48,  6, 53,
       27, 61, 36, 50, 57,  8, 10, 65, 26, 24, 34, 33, 30, 52, 47],
      dtype=int64), 'cur_cost': 113542.0}, {'tour': array([50,  1, 27,  6, 16, 24, 30, 60, 37, 12, 11, 43, 18,  2, 28,  0, 20,
       15, 63,  4,  7, 48, 22, 26, 51, 10,  9, 25, 17, 19, 44, 32, 39, 61,
       38, 13, 41, 21, 54, 45, 42,  3, 53,  5, 14, 59, 33, 49, 52, 47, 56,
       31, 62, 58, 36,  8, 46, 34, 35, 40, 29, 64, 23, 65, 55, 57],
      dtype=int64), 'cur_cost': 115071.0}, {'tour': [54, 11, 36, 4, 65, 45, 49, 0, 37, 55, 14, 13, 43, 15, 38, 60, 3, 12, 48, 29, 18, 7, 27, 42, 32, 22, 6, 61, 25, 17, 30, 2, 62, 23, 24, 5, 57, 52, 59, 10, 28, 56, 58, 9, 34, 41, 40, 63, 39, 50, 33, 19, 44, 51, 21, 64, 1, 31, 16, 8, 20, 53, 26, 35, 46, 47], 'cur_cost': 99565.0}, {'tour': [56, 29, 38, 55, 6, 27, 35, 49, 3, 64, 43, 18, 41, 32, 39, 10, 25, 51, 2, 26, 37, 44, 21, 13, 23, 9, 28, 62, 46, 16, 34, 36, 60, 17, 4, 47, 1, 14, 30, 65, 57, 59, 22, 19, 61, 12, 20, 50, 31, 15, 45, 53, 8, 24, 33, 42, 54, 40, 48, 7, 58, 63, 0, 11, 52, 5], 'cur_cost': 103377.0}, {'tour': [13, 25, 26, 19, 12, 48, 29, 2, 10, 6, 28, 1, 37, 21, 4, 58, 5, 44, 54, 38, 63, 20, 40, 61, 9, 49, 42, 51, 55, 3, 46, 8, 53, 14, 50, 22, 60, 59, 23, 31, 43, 64, 27, 18, 34, 35, 57, 62, 24, 56, 11, 15, 0, 32, 30, 7, 33, 17, 39, 52, 47, 16, 45, 65, 36, 41], 'cur_cost': 108089.0}]
2025-08-01 17:46:16,662 - ExploitationExpert - INFO - 局部搜索耗时: 0.12秒
2025-08-01 17:46:16,662 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 37, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 37, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:46:16,663 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([50,  1, 27,  6, 16, 24, 30, 60, 37, 12, 11, 43, 18,  2, 28,  0, 20,
       15, 63,  4,  7, 48, 22, 26, 51, 10,  9, 25, 17, 19, 44, 32, 39, 61,
       38, 13, 41, 21, 54, 45, 42,  3, 53,  5, 14, 59, 33, 49, 52, 47, 56,
       31, 62, 58, 36,  8, 46, 34, 35, 40, 29, 64, 23, 65, 55, 57],
      dtype=int64), 'cur_cost': 115071.0}
2025-08-01 17:46:16,664 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-01 17:46:16,664 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:16,665 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:16,666 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 107536.0
2025-08-01 17:46:16,766 - ExploitationExpert - INFO - res_population_num: 13
2025-08-01 17:46:16,766 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0, 9521]
2025-08-01 17:46:16,767 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-08-01 17:46:16,781 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:16,782 - ExploitationExpert - INFO - populations: [{'tour': array([20, 58, 63, 30, 59, 34,  4, 44, 57, 39, 49,  5, 46, 45, 40, 28, 42,
       12, 51,  1, 38, 23, 47, 36,  2, 35, 48, 10, 41, 13, 33, 37, 21,  9,
        6,  0, 18,  7, 64, 62, 61, 17, 26, 29, 19, 50,  8, 52, 54, 11,  3,
       53, 15, 16, 65, 56, 24, 32, 31, 55, 27, 43, 22, 14, 60, 25],
      dtype=int64), 'cur_cost': 97336.0}, {'tour': array([20, 29, 11, 15, 44, 25, 19,  6, 12, 49, 41, 40,  7, 21, 37, 34, 48,
       57,  9, 55, 13, 18, 62, 47, 36, 60, 58,  8, 61, 54, 53, 31,  2, 27,
       10,  5, 22, 56, 63, 52,  1, 35, 16, 38, 46,  4, 50, 30, 59,  0, 64,
       23, 17, 42, 26, 32, 51, 43, 28, 65, 24, 33, 45,  3, 39, 14],
      dtype=int64), 'cur_cost': 93840.0}, {'tour': array([30, 18, 11, 33, 54, 59,  0, 63, 42, 13, 52, 10, 62, 35, 56, 32,  9,
        3, 15, 34, 51, 47, 39, 25, 16, 31, 48, 49, 41, 17, 46,  1, 57, 12,
       24, 38, 36, 45, 40, 55,  2, 27,  6,  5,  4, 53,  8, 22, 65, 64, 23,
       21,  7, 28, 44, 29, 19, 58, 50, 26, 60, 61, 37, 43, 14, 20],
      dtype=int64), 'cur_cost': 97557.0}, {'tour': array([46, 48, 29, 40,  2,  3, 30, 64,  7, 27, 20, 52, 55, 60, 23, 15, 19,
       56, 54, 53,  9, 17, 49, 28, 14, 24, 39, 58, 59, 10, 35, 26, 62, 33,
       50, 61, 31, 22, 41, 36, 44,  1, 12,  8, 57, 51, 45, 43, 13, 18, 38,
       32, 47,  0, 16, 65, 34,  6,  5, 42, 11, 25,  4, 37, 63, 21],
      dtype=int64), 'cur_cost': 103490.0}, {'tour': array([55, 20, 62, 49, 13, 43, 38,  2, 41, 15,  8, 46, 25, 23, 34, 27, 52,
       33, 24, 63, 54,  6,  3, 14, 42, 12, 37, 17, 56, 29, 18, 59, 26, 51,
       58, 22, 28, 64, 40, 61, 16, 57, 44, 48, 11, 53,  7, 31, 32, 50,  9,
        4, 10, 21, 60, 65, 30,  0, 36, 47,  5, 19, 35, 39, 45,  1],
      dtype=int64), 'cur_cost': 109600.0}, {'tour': array([19, 63, 49, 37, 42, 62, 21,  7, 51, 64, 20,  1, 22, 59, 38,  2, 58,
       46, 44, 16, 14,  0, 45, 25, 15,  5, 40, 56, 13,  9, 18, 55, 23, 39,
       60, 41,  3, 11, 12, 35, 31, 43, 17,  4, 29, 32, 28, 54, 48,  6, 53,
       27, 61, 36, 50, 57,  8, 10, 65, 26, 24, 34, 33, 30, 52, 47],
      dtype=int64), 'cur_cost': 113542.0}, {'tour': array([50,  1, 27,  6, 16, 24, 30, 60, 37, 12, 11, 43, 18,  2, 28,  0, 20,
       15, 63,  4,  7, 48, 22, 26, 51, 10,  9, 25, 17, 19, 44, 32, 39, 61,
       38, 13, 41, 21, 54, 45, 42,  3, 53,  5, 14, 59, 33, 49, 52, 47, 56,
       31, 62, 58, 36,  8, 46, 34, 35, 40, 29, 64, 23, 65, 55, 57],
      dtype=int64), 'cur_cost': 115071.0}, {'tour': array([50, 65, 12, 55, 45, 20, 17, 36, 52, 11,  2,  4,  6,  5, 15, 58, 13,
       51, 56, 64, 48, 62, 53,  3, 63, 16,  0, 37, 38, 28, 60, 31, 25, 23,
        9, 35,  7, 27, 18, 42, 49, 32, 39, 22, 26,  1, 43, 34, 41, 14, 46,
       33, 29, 47, 59, 57, 24, 10, 19, 61, 40, 30, 44,  8, 54, 21],
      dtype=int64), 'cur_cost': 107536.0}, {'tour': [56, 29, 38, 55, 6, 27, 35, 49, 3, 64, 43, 18, 41, 32, 39, 10, 25, 51, 2, 26, 37, 44, 21, 13, 23, 9, 28, 62, 46, 16, 34, 36, 60, 17, 4, 47, 1, 14, 30, 65, 57, 59, 22, 19, 61, 12, 20, 50, 31, 15, 45, 53, 8, 24, 33, 42, 54, 40, 48, 7, 58, 63, 0, 11, 52, 5], 'cur_cost': 103377.0}, {'tour': [13, 25, 26, 19, 12, 48, 29, 2, 10, 6, 28, 1, 37, 21, 4, 58, 5, 44, 54, 38, 63, 20, 40, 61, 9, 49, 42, 51, 55, 3, 46, 8, 53, 14, 50, 22, 60, 59, 23, 31, 43, 64, 27, 18, 34, 35, 57, 62, 24, 56, 11, 15, 0, 32, 30, 7, 33, 17, 39, 52, 47, 16, 45, 65, 36, 41], 'cur_cost': 108089.0}]
2025-08-01 17:46:16,789 - ExploitationExpert - INFO - 局部搜索耗时: 0.12秒
2025-08-01 17:46:16,790 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 38, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 38, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:46:16,791 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([50, 65, 12, 55, 45, 20, 17, 36, 52, 11,  2,  4,  6,  5, 15, 58, 13,
       51, 56, 64, 48, 62, 53,  3, 63, 16,  0, 37, 38, 28, 60, 31, 25, 23,
        9, 35,  7, 27, 18, 42, 49, 32, 39, 22, 26,  1, 43, 34, 41, 14, 46,
       33, 29, 47, 59, 57, 24, 10, 19, 61, 40, 30, 44,  8, 54, 21],
      dtype=int64), 'cur_cost': 107536.0}
2025-08-01 17:46:16,791 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-01 17:46:16,792 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:16,792 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:16,793 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 108852.0
2025-08-01 17:46:16,887 - ExploitationExpert - INFO - res_population_num: 13
2025-08-01 17:46:16,887 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0, 9521]
2025-08-01 17:46:16,887 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-08-01 17:46:16,897 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:16,898 - ExploitationExpert - INFO - populations: [{'tour': array([20, 58, 63, 30, 59, 34,  4, 44, 57, 39, 49,  5, 46, 45, 40, 28, 42,
       12, 51,  1, 38, 23, 47, 36,  2, 35, 48, 10, 41, 13, 33, 37, 21,  9,
        6,  0, 18,  7, 64, 62, 61, 17, 26, 29, 19, 50,  8, 52, 54, 11,  3,
       53, 15, 16, 65, 56, 24, 32, 31, 55, 27, 43, 22, 14, 60, 25],
      dtype=int64), 'cur_cost': 97336.0}, {'tour': array([20, 29, 11, 15, 44, 25, 19,  6, 12, 49, 41, 40,  7, 21, 37, 34, 48,
       57,  9, 55, 13, 18, 62, 47, 36, 60, 58,  8, 61, 54, 53, 31,  2, 27,
       10,  5, 22, 56, 63, 52,  1, 35, 16, 38, 46,  4, 50, 30, 59,  0, 64,
       23, 17, 42, 26, 32, 51, 43, 28, 65, 24, 33, 45,  3, 39, 14],
      dtype=int64), 'cur_cost': 93840.0}, {'tour': array([30, 18, 11, 33, 54, 59,  0, 63, 42, 13, 52, 10, 62, 35, 56, 32,  9,
        3, 15, 34, 51, 47, 39, 25, 16, 31, 48, 49, 41, 17, 46,  1, 57, 12,
       24, 38, 36, 45, 40, 55,  2, 27,  6,  5,  4, 53,  8, 22, 65, 64, 23,
       21,  7, 28, 44, 29, 19, 58, 50, 26, 60, 61, 37, 43, 14, 20],
      dtype=int64), 'cur_cost': 97557.0}, {'tour': array([46, 48, 29, 40,  2,  3, 30, 64,  7, 27, 20, 52, 55, 60, 23, 15, 19,
       56, 54, 53,  9, 17, 49, 28, 14, 24, 39, 58, 59, 10, 35, 26, 62, 33,
       50, 61, 31, 22, 41, 36, 44,  1, 12,  8, 57, 51, 45, 43, 13, 18, 38,
       32, 47,  0, 16, 65, 34,  6,  5, 42, 11, 25,  4, 37, 63, 21],
      dtype=int64), 'cur_cost': 103490.0}, {'tour': array([55, 20, 62, 49, 13, 43, 38,  2, 41, 15,  8, 46, 25, 23, 34, 27, 52,
       33, 24, 63, 54,  6,  3, 14, 42, 12, 37, 17, 56, 29, 18, 59, 26, 51,
       58, 22, 28, 64, 40, 61, 16, 57, 44, 48, 11, 53,  7, 31, 32, 50,  9,
        4, 10, 21, 60, 65, 30,  0, 36, 47,  5, 19, 35, 39, 45,  1],
      dtype=int64), 'cur_cost': 109600.0}, {'tour': array([19, 63, 49, 37, 42, 62, 21,  7, 51, 64, 20,  1, 22, 59, 38,  2, 58,
       46, 44, 16, 14,  0, 45, 25, 15,  5, 40, 56, 13,  9, 18, 55, 23, 39,
       60, 41,  3, 11, 12, 35, 31, 43, 17,  4, 29, 32, 28, 54, 48,  6, 53,
       27, 61, 36, 50, 57,  8, 10, 65, 26, 24, 34, 33, 30, 52, 47],
      dtype=int64), 'cur_cost': 113542.0}, {'tour': array([50,  1, 27,  6, 16, 24, 30, 60, 37, 12, 11, 43, 18,  2, 28,  0, 20,
       15, 63,  4,  7, 48, 22, 26, 51, 10,  9, 25, 17, 19, 44, 32, 39, 61,
       38, 13, 41, 21, 54, 45, 42,  3, 53,  5, 14, 59, 33, 49, 52, 47, 56,
       31, 62, 58, 36,  8, 46, 34, 35, 40, 29, 64, 23, 65, 55, 57],
      dtype=int64), 'cur_cost': 115071.0}, {'tour': array([50, 65, 12, 55, 45, 20, 17, 36, 52, 11,  2,  4,  6,  5, 15, 58, 13,
       51, 56, 64, 48, 62, 53,  3, 63, 16,  0, 37, 38, 28, 60, 31, 25, 23,
        9, 35,  7, 27, 18, 42, 49, 32, 39, 22, 26,  1, 43, 34, 41, 14, 46,
       33, 29, 47, 59, 57, 24, 10, 19, 61, 40, 30, 44,  8, 54, 21],
      dtype=int64), 'cur_cost': 107536.0}, {'tour': array([30, 14, 19, 58, 13, 62, 28, 41, 59,  3, 20, 53, 15, 16, 26,  2, 43,
       46, 44, 21, 18, 17, 27, 38, 37, 48, 35, 11, 29, 50, 31,  4,  1, 61,
       65, 64,  0, 40, 47, 55, 25, 57, 63, 51, 54, 39, 52, 32, 23, 34, 24,
        8, 49, 56, 12,  6,  9, 36, 22, 42, 60,  5, 45, 10, 33,  7],
      dtype=int64), 'cur_cost': 108852.0}, {'tour': [13, 25, 26, 19, 12, 48, 29, 2, 10, 6, 28, 1, 37, 21, 4, 58, 5, 44, 54, 38, 63, 20, 40, 61, 9, 49, 42, 51, 55, 3, 46, 8, 53, 14, 50, 22, 60, 59, 23, 31, 43, 64, 27, 18, 34, 35, 57, 62, 24, 56, 11, 15, 0, 32, 30, 7, 33, 17, 39, 52, 47, 16, 45, 65, 36, 41], 'cur_cost': 108089.0}]
2025-08-01 17:46:16,910 - ExploitationExpert - INFO - 局部搜索耗时: 0.12秒
2025-08-01 17:46:16,910 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 39, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 39, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:46:16,911 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([30, 14, 19, 58, 13, 62, 28, 41, 59,  3, 20, 53, 15, 16, 26,  2, 43,
       46, 44, 21, 18, 17, 27, 38, 37, 48, 35, 11, 29, 50, 31,  4,  1, 61,
       65, 64,  0, 40, 47, 55, 25, 57, 63, 51, 54, 39, 52, 32, 23, 34, 24,
        8, 49, 56, 12,  6,  9, 36, 22, 42, 60,  5, 45, 10, 33,  7],
      dtype=int64), 'cur_cost': 108852.0}
2025-08-01 17:46:16,912 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-01 17:46:16,913 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:16,913 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:16,914 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 96662.0
2025-08-01 17:46:17,020 - ExploitationExpert - INFO - res_population_num: 13
2025-08-01 17:46:17,020 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0, 9521]
2025-08-01 17:46:17,021 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-08-01 17:46:17,030 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:17,031 - ExploitationExpert - INFO - populations: [{'tour': array([20, 58, 63, 30, 59, 34,  4, 44, 57, 39, 49,  5, 46, 45, 40, 28, 42,
       12, 51,  1, 38, 23, 47, 36,  2, 35, 48, 10, 41, 13, 33, 37, 21,  9,
        6,  0, 18,  7, 64, 62, 61, 17, 26, 29, 19, 50,  8, 52, 54, 11,  3,
       53, 15, 16, 65, 56, 24, 32, 31, 55, 27, 43, 22, 14, 60, 25],
      dtype=int64), 'cur_cost': 97336.0}, {'tour': array([20, 29, 11, 15, 44, 25, 19,  6, 12, 49, 41, 40,  7, 21, 37, 34, 48,
       57,  9, 55, 13, 18, 62, 47, 36, 60, 58,  8, 61, 54, 53, 31,  2, 27,
       10,  5, 22, 56, 63, 52,  1, 35, 16, 38, 46,  4, 50, 30, 59,  0, 64,
       23, 17, 42, 26, 32, 51, 43, 28, 65, 24, 33, 45,  3, 39, 14],
      dtype=int64), 'cur_cost': 93840.0}, {'tour': array([30, 18, 11, 33, 54, 59,  0, 63, 42, 13, 52, 10, 62, 35, 56, 32,  9,
        3, 15, 34, 51, 47, 39, 25, 16, 31, 48, 49, 41, 17, 46,  1, 57, 12,
       24, 38, 36, 45, 40, 55,  2, 27,  6,  5,  4, 53,  8, 22, 65, 64, 23,
       21,  7, 28, 44, 29, 19, 58, 50, 26, 60, 61, 37, 43, 14, 20],
      dtype=int64), 'cur_cost': 97557.0}, {'tour': array([46, 48, 29, 40,  2,  3, 30, 64,  7, 27, 20, 52, 55, 60, 23, 15, 19,
       56, 54, 53,  9, 17, 49, 28, 14, 24, 39, 58, 59, 10, 35, 26, 62, 33,
       50, 61, 31, 22, 41, 36, 44,  1, 12,  8, 57, 51, 45, 43, 13, 18, 38,
       32, 47,  0, 16, 65, 34,  6,  5, 42, 11, 25,  4, 37, 63, 21],
      dtype=int64), 'cur_cost': 103490.0}, {'tour': array([55, 20, 62, 49, 13, 43, 38,  2, 41, 15,  8, 46, 25, 23, 34, 27, 52,
       33, 24, 63, 54,  6,  3, 14, 42, 12, 37, 17, 56, 29, 18, 59, 26, 51,
       58, 22, 28, 64, 40, 61, 16, 57, 44, 48, 11, 53,  7, 31, 32, 50,  9,
        4, 10, 21, 60, 65, 30,  0, 36, 47,  5, 19, 35, 39, 45,  1],
      dtype=int64), 'cur_cost': 109600.0}, {'tour': array([19, 63, 49, 37, 42, 62, 21,  7, 51, 64, 20,  1, 22, 59, 38,  2, 58,
       46, 44, 16, 14,  0, 45, 25, 15,  5, 40, 56, 13,  9, 18, 55, 23, 39,
       60, 41,  3, 11, 12, 35, 31, 43, 17,  4, 29, 32, 28, 54, 48,  6, 53,
       27, 61, 36, 50, 57,  8, 10, 65, 26, 24, 34, 33, 30, 52, 47],
      dtype=int64), 'cur_cost': 113542.0}, {'tour': array([50,  1, 27,  6, 16, 24, 30, 60, 37, 12, 11, 43, 18,  2, 28,  0, 20,
       15, 63,  4,  7, 48, 22, 26, 51, 10,  9, 25, 17, 19, 44, 32, 39, 61,
       38, 13, 41, 21, 54, 45, 42,  3, 53,  5, 14, 59, 33, 49, 52, 47, 56,
       31, 62, 58, 36,  8, 46, 34, 35, 40, 29, 64, 23, 65, 55, 57],
      dtype=int64), 'cur_cost': 115071.0}, {'tour': array([50, 65, 12, 55, 45, 20, 17, 36, 52, 11,  2,  4,  6,  5, 15, 58, 13,
       51, 56, 64, 48, 62, 53,  3, 63, 16,  0, 37, 38, 28, 60, 31, 25, 23,
        9, 35,  7, 27, 18, 42, 49, 32, 39, 22, 26,  1, 43, 34, 41, 14, 46,
       33, 29, 47, 59, 57, 24, 10, 19, 61, 40, 30, 44,  8, 54, 21],
      dtype=int64), 'cur_cost': 107536.0}, {'tour': array([30, 14, 19, 58, 13, 62, 28, 41, 59,  3, 20, 53, 15, 16, 26,  2, 43,
       46, 44, 21, 18, 17, 27, 38, 37, 48, 35, 11, 29, 50, 31,  4,  1, 61,
       65, 64,  0, 40, 47, 55, 25, 57, 63, 51, 54, 39, 52, 32, 23, 34, 24,
        8, 49, 56, 12,  6,  9, 36, 22, 42, 60,  5, 45, 10, 33,  7],
      dtype=int64), 'cur_cost': 108852.0}, {'tour': array([ 2, 50, 42,  9, 26, 19, 10, 59, 15, 32, 60, 27, 22, 33,  0,  3, 61,
        6, 38, 12,  1, 63, 13, 58, 34, 46, 53,  4, 37, 29, 11, 28,  7,  5,
       21, 56, 62,  8, 45, 17, 14, 49, 31, 39, 54, 65, 57, 16, 47, 41, 55,
       20, 51, 48, 43, 30, 36, 23, 40, 18, 52, 25, 35, 24, 44, 64],
      dtype=int64), 'cur_cost': 96662.0}]
2025-08-01 17:46:17,042 - ExploitationExpert - INFO - 局部搜索耗时: 0.13秒
2025-08-01 17:46:17,043 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 40, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 40, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:46:17,044 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([ 2, 50, 42,  9, 26, 19, 10, 59, 15, 32, 60, 27, 22, 33,  0,  3, 61,
        6, 38, 12,  1, 63, 13, 58, 34, 46, 53,  4, 37, 29, 11, 28,  7,  5,
       21, 56, 62,  8, 45, 17, 14, 49, 31, 39, 54, 65, 57, 16, 47, 41, 55,
       20, 51, 48, 43, 30, 36, 23, 40, 18, 52, 25, 35, 24, 44, 64],
      dtype=int64), 'cur_cost': 96662.0}
2025-08-01 17:46:17,046 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([20, 58, 63, 30, 59, 34,  4, 44, 57, 39, 49,  5, 46, 45, 40, 28, 42,
       12, 51,  1, 38, 23, 47, 36,  2, 35, 48, 10, 41, 13, 33, 37, 21,  9,
        6,  0, 18,  7, 64, 62, 61, 17, 26, 29, 19, 50,  8, 52, 54, 11,  3,
       53, 15, 16, 65, 56, 24, 32, 31, 55, 27, 43, 22, 14, 60, 25],
      dtype=int64), 'cur_cost': 97336.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([20, 29, 11, 15, 44, 25, 19,  6, 12, 49, 41, 40,  7, 21, 37, 34, 48,
       57,  9, 55, 13, 18, 62, 47, 36, 60, 58,  8, 61, 54, 53, 31,  2, 27,
       10,  5, 22, 56, 63, 52,  1, 35, 16, 38, 46,  4, 50, 30, 59,  0, 64,
       23, 17, 42, 26, 32, 51, 43, 28, 65, 24, 33, 45,  3, 39, 14],
      dtype=int64), 'cur_cost': 93840.0}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([30, 18, 11, 33, 54, 59,  0, 63, 42, 13, 52, 10, 62, 35, 56, 32,  9,
        3, 15, 34, 51, 47, 39, 25, 16, 31, 48, 49, 41, 17, 46,  1, 57, 12,
       24, 38, 36, 45, 40, 55,  2, 27,  6,  5,  4, 53,  8, 22, 65, 64, 23,
       21,  7, 28, 44, 29, 19, 58, 50, 26, 60, 61, 37, 43, 14, 20],
      dtype=int64), 'cur_cost': 97557.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([46, 48, 29, 40,  2,  3, 30, 64,  7, 27, 20, 52, 55, 60, 23, 15, 19,
       56, 54, 53,  9, 17, 49, 28, 14, 24, 39, 58, 59, 10, 35, 26, 62, 33,
       50, 61, 31, 22, 41, 36, 44,  1, 12,  8, 57, 51, 45, 43, 13, 18, 38,
       32, 47,  0, 16, 65, 34,  6,  5, 42, 11, 25,  4, 37, 63, 21],
      dtype=int64), 'cur_cost': 103490.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([55, 20, 62, 49, 13, 43, 38,  2, 41, 15,  8, 46, 25, 23, 34, 27, 52,
       33, 24, 63, 54,  6,  3, 14, 42, 12, 37, 17, 56, 29, 18, 59, 26, 51,
       58, 22, 28, 64, 40, 61, 16, 57, 44, 48, 11, 53,  7, 31, 32, 50,  9,
        4, 10, 21, 60, 65, 30,  0, 36, 47,  5, 19, 35, 39, 45,  1],
      dtype=int64), 'cur_cost': 109600.0}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([19, 63, 49, 37, 42, 62, 21,  7, 51, 64, 20,  1, 22, 59, 38,  2, 58,
       46, 44, 16, 14,  0, 45, 25, 15,  5, 40, 56, 13,  9, 18, 55, 23, 39,
       60, 41,  3, 11, 12, 35, 31, 43, 17,  4, 29, 32, 28, 54, 48,  6, 53,
       27, 61, 36, 50, 57,  8, 10, 65, 26, 24, 34, 33, 30, 52, 47],
      dtype=int64), 'cur_cost': 113542.0}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([50,  1, 27,  6, 16, 24, 30, 60, 37, 12, 11, 43, 18,  2, 28,  0, 20,
       15, 63,  4,  7, 48, 22, 26, 51, 10,  9, 25, 17, 19, 44, 32, 39, 61,
       38, 13, 41, 21, 54, 45, 42,  3, 53,  5, 14, 59, 33, 49, 52, 47, 56,
       31, 62, 58, 36,  8, 46, 34, 35, 40, 29, 64, 23, 65, 55, 57],
      dtype=int64), 'cur_cost': 115071.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([50, 65, 12, 55, 45, 20, 17, 36, 52, 11,  2,  4,  6,  5, 15, 58, 13,
       51, 56, 64, 48, 62, 53,  3, 63, 16,  0, 37, 38, 28, 60, 31, 25, 23,
        9, 35,  7, 27, 18, 42, 49, 32, 39, 22, 26,  1, 43, 34, 41, 14, 46,
       33, 29, 47, 59, 57, 24, 10, 19, 61, 40, 30, 44,  8, 54, 21],
      dtype=int64), 'cur_cost': 107536.0}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([30, 14, 19, 58, 13, 62, 28, 41, 59,  3, 20, 53, 15, 16, 26,  2, 43,
       46, 44, 21, 18, 17, 27, 38, 37, 48, 35, 11, 29, 50, 31,  4,  1, 61,
       65, 64,  0, 40, 47, 55, 25, 57, 63, 51, 54, 39, 52, 32, 23, 34, 24,
        8, 49, 56, 12,  6,  9, 36, 22, 42, 60,  5, 45, 10, 33,  7],
      dtype=int64), 'cur_cost': 108852.0}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 2, 50, 42,  9, 26, 19, 10, 59, 15, 32, 60, 27, 22, 33,  0,  3, 61,
        6, 38, 12,  1, 63, 13, 58, 34, 46, 53,  4, 37, 29, 11, 28,  7,  5,
       21, 56, 62,  8, 45, 17, 14, 49, 31, 39, 54, 65, 57, 16, 47, 41, 55,
       20, 51, 48, 43, 30, 36, 23, 40, 18, 52, 25, 35, 24, 44, 64],
      dtype=int64), 'cur_cost': 96662.0}}]
2025-08-01 17:46:17,051 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-01 17:46:17,051 - StatsExpert - INFO - 开始统计分析
2025-08-01 17:46:17,054 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=93840.000, 多样性=0.986
2025-08-01 17:46:17,055 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-01 17:46:17,055 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-01 17:46:17,056 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-01 17:46:17,058 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.0030131873284689943, 'best_improvement': 0.025211131540403255}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.0013675213675215644}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 1.3319603122184784, 'recent_improvements': [-2.6573769635894275, -0.04741422891216359, 0.006543660847529195], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 13, 'new_count': 13, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.7698135198135198, 'new_diversity': 0.7698135198135198, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-01 17:46:17,060 - main - INFO - --- Finished Evolution Iteration 4 ---
2025-08-01 17:46:17,061 - main - INFO - composite13_66 开始进化第 5 代
2025-08-01 17:46:17,061 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-01 17:46:17,062 - StatsExpert - INFO - 开始统计分析
2025-08-01 17:46:17,063 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=93840.000, 多样性=0.986
2025-08-01 17:46:17,064 - PathExpert - INFO - 开始路径结构分析
2025-08-01 17:46:17,067 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.986
2025-08-01 17:46:17,068 - EliteExpert - INFO - 开始精英解分析
2025-08-01 17:46:17,078 - EliteExpert - INFO - 精英解分析完成: 精英解数量=13, 多样性=0.770
2025-08-01 17:46:17,082 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-01 17:46:17,083 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-01 17:46:17,083 - LandscapeExpert - INFO - 添加精英解数据: 13个精英解
2025-08-01 17:46:17,084 - LandscapeExpert - INFO - 数据提取成功: 23个路径, 23个适应度值
2025-08-01 17:46:17,181 - core.algorithms.fitness_landscape_analyzer - ERROR - 景观分析失败: '>' not supported between instances of 'str' and 'int'
2025-08-01 17:46:17,182 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-01 17:46:17,183 - LandscapeExpert - INFO - 提取到 13 个精英解
2025-08-01 17:46:17,190 - visualization.landscape_visualizer - INFO - 已添加 13 个精英解标记
2025-08-01 17:46:17,298 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_5_20250801_174617.html
2025-08-01 17:46:17,355 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_5_20250801_174617.html
2025-08-01 17:46:17,356 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 5
2025-08-01 17:46:17,357 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-01 17:46:17,357 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2757秒
2025-08-01 17:46:17,358 - main - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754041577.1823385, 'performance_metrics': {}}}
2025-08-01 17:46:17,359 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-01 17:46:17,360 - StrategyExpert - INFO - 开始策略分配分析
2025-08-01 17:46:17,360 - StrategyExpert - INFO - 消融实验模式：使用纯exploit策略
2025-08-01 17:46:17,360 - StrategyExpert - INFO - 策略分配分析完成（消融实验模式）
2025-08-01 17:46:17,361 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-08-01 17:46:17,362 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 消融实验模式：所有10个个体都使用exploit策略
2025-08-01 17:46:17,362 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-01 17:46:17,363 - main - INFO - 策略分配: ['exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-08-01 17:46:17,363 - main - INFO - 策略分配完整报告: 消融实验模式：所有10个个体都使用exploit策略
2025-08-01 17:46:17,364 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-01 17:46:17,364 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-01 17:46:17,364 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:17,365 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:17,365 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 121600.0
2025-08-01 17:46:17,456 - ExploitationExpert - INFO - res_population_num: 13
2025-08-01 17:46:17,457 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0]
2025-08-01 17:46:17,457 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64)]
2025-08-01 17:46:17,467 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:17,467 - ExploitationExpert - INFO - populations: [{'tour': array([ 0, 50, 45, 12, 51, 43, 21, 36, 14, 46,  6, 22, 19,  4, 39, 25,  2,
       23, 48, 42, 35, 54, 28, 10, 38, 55, 37, 62, 32, 53, 29,  7, 20, 58,
       17, 31,  1, 41, 13, 27, 47, 18, 24, 56, 33, 65, 64,  8, 63, 16, 49,
        3,  5, 60, 44, 57, 30, 59, 11, 34, 52, 40, 26, 61, 15,  9],
      dtype=int64), 'cur_cost': 121600.0}, {'tour': [20, 29, 11, 15, 44, 25, 19, 6, 12, 49, 41, 40, 7, 21, 37, 34, 48, 57, 9, 55, 13, 18, 62, 47, 36, 60, 58, 8, 61, 54, 53, 31, 2, 27, 10, 5, 22, 56, 63, 52, 1, 35, 16, 38, 46, 4, 50, 30, 59, 0, 64, 23, 17, 42, 26, 32, 51, 43, 28, 65, 24, 33, 45, 3, 39, 14], 'cur_cost': 93840.0}, {'tour': [30, 18, 11, 33, 54, 59, 0, 63, 42, 13, 52, 10, 62, 35, 56, 32, 9, 3, 15, 34, 51, 47, 39, 25, 16, 31, 48, 49, 41, 17, 46, 1, 57, 12, 24, 38, 36, 45, 40, 55, 2, 27, 6, 5, 4, 53, 8, 22, 65, 64, 23, 21, 7, 28, 44, 29, 19, 58, 50, 26, 60, 61, 37, 43, 14, 20], 'cur_cost': 97557.0}, {'tour': [46, 48, 29, 40, 2, 3, 30, 64, 7, 27, 20, 52, 55, 60, 23, 15, 19, 56, 54, 53, 9, 17, 49, 28, 14, 24, 39, 58, 59, 10, 35, 26, 62, 33, 50, 61, 31, 22, 41, 36, 44, 1, 12, 8, 57, 51, 45, 43, 13, 18, 38, 32, 47, 0, 16, 65, 34, 6, 5, 42, 11, 25, 4, 37, 63, 21], 'cur_cost': 103490.0}, {'tour': [55, 20, 62, 49, 13, 43, 38, 2, 41, 15, 8, 46, 25, 23, 34, 27, 52, 33, 24, 63, 54, 6, 3, 14, 42, 12, 37, 17, 56, 29, 18, 59, 26, 51, 58, 22, 28, 64, 40, 61, 16, 57, 44, 48, 11, 53, 7, 31, 32, 50, 9, 4, 10, 21, 60, 65, 30, 0, 36, 47, 5, 19, 35, 39, 45, 1], 'cur_cost': 109600.0}, {'tour': [19, 63, 49, 37, 42, 62, 21, 7, 51, 64, 20, 1, 22, 59, 38, 2, 58, 46, 44, 16, 14, 0, 45, 25, 15, 5, 40, 56, 13, 9, 18, 55, 23, 39, 60, 41, 3, 11, 12, 35, 31, 43, 17, 4, 29, 32, 28, 54, 48, 6, 53, 27, 61, 36, 50, 57, 8, 10, 65, 26, 24, 34, 33, 30, 52, 47], 'cur_cost': 113542.0}, {'tour': [50, 1, 27, 6, 16, 24, 30, 60, 37, 12, 11, 43, 18, 2, 28, 0, 20, 15, 63, 4, 7, 48, 22, 26, 51, 10, 9, 25, 17, 19, 44, 32, 39, 61, 38, 13, 41, 21, 54, 45, 42, 3, 53, 5, 14, 59, 33, 49, 52, 47, 56, 31, 62, 58, 36, 8, 46, 34, 35, 40, 29, 64, 23, 65, 55, 57], 'cur_cost': 115071.0}, {'tour': [50, 65, 12, 55, 45, 20, 17, 36, 52, 11, 2, 4, 6, 5, 15, 58, 13, 51, 56, 64, 48, 62, 53, 3, 63, 16, 0, 37, 38, 28, 60, 31, 25, 23, 9, 35, 7, 27, 18, 42, 49, 32, 39, 22, 26, 1, 43, 34, 41, 14, 46, 33, 29, 47, 59, 57, 24, 10, 19, 61, 40, 30, 44, 8, 54, 21], 'cur_cost': 107536.0}, {'tour': [30, 14, 19, 58, 13, 62, 28, 41, 59, 3, 20, 53, 15, 16, 26, 2, 43, 46, 44, 21, 18, 17, 27, 38, 37, 48, 35, 11, 29, 50, 31, 4, 1, 61, 65, 64, 0, 40, 47, 55, 25, 57, 63, 51, 54, 39, 52, 32, 23, 34, 24, 8, 49, 56, 12, 6, 9, 36, 22, 42, 60, 5, 45, 10, 33, 7], 'cur_cost': 108852.0}, {'tour': [2, 50, 42, 9, 26, 19, 10, 59, 15, 32, 60, 27, 22, 33, 0, 3, 61, 6, 38, 12, 1, 63, 13, 58, 34, 46, 53, 4, 37, 29, 11, 28, 7, 5, 21, 56, 62, 8, 45, 17, 14, 49, 31, 39, 54, 65, 57, 16, 47, 41, 55, 20, 51, 48, 43, 30, 36, 23, 40, 18, 52, 25, 35, 24, 44, 64], 'cur_cost': 96662.0}]
2025-08-01 17:46:17,473 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒
2025-08-01 17:46:17,474 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 41, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 41, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:46:17,475 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([ 0, 50, 45, 12, 51, 43, 21, 36, 14, 46,  6, 22, 19,  4, 39, 25,  2,
       23, 48, 42, 35, 54, 28, 10, 38, 55, 37, 62, 32, 53, 29,  7, 20, 58,
       17, 31,  1, 41, 13, 27, 47, 18, 24, 56, 33, 65, 64,  8, 63, 16, 49,
        3,  5, 60, 44, 57, 30, 59, 11, 34, 52, 40, 26, 61, 15,  9],
      dtype=int64), 'cur_cost': 121600.0}
2025-08-01 17:46:17,476 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-01 17:46:17,476 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:17,477 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:17,477 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 102113.0
2025-08-01 17:46:17,562 - ExploitationExpert - INFO - res_population_num: 13
2025-08-01 17:46:17,562 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0]
2025-08-01 17:46:17,563 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64)]
2025-08-01 17:46:17,576 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:17,576 - ExploitationExpert - INFO - populations: [{'tour': array([ 0, 50, 45, 12, 51, 43, 21, 36, 14, 46,  6, 22, 19,  4, 39, 25,  2,
       23, 48, 42, 35, 54, 28, 10, 38, 55, 37, 62, 32, 53, 29,  7, 20, 58,
       17, 31,  1, 41, 13, 27, 47, 18, 24, 56, 33, 65, 64,  8, 63, 16, 49,
        3,  5, 60, 44, 57, 30, 59, 11, 34, 52, 40, 26, 61, 15,  9],
      dtype=int64), 'cur_cost': 121600.0}, {'tour': array([12, 54, 35, 10, 57, 29, 27, 50, 13, 62, 16, 42, 41,  4, 18, 52, 58,
       39, 55,  9, 21, 64, 36, 24,  6,  1, 56, 45, 33, 30, 47, 43, 53, 15,
       19, 40, 26, 11, 59, 17,  7, 44, 61, 34, 37, 32,  5, 23, 20, 49, 22,
       65,  0,  8, 46, 25, 28, 48, 38,  3, 51, 14, 63,  2, 60, 31],
      dtype=int64), 'cur_cost': 102113.0}, {'tour': [30, 18, 11, 33, 54, 59, 0, 63, 42, 13, 52, 10, 62, 35, 56, 32, 9, 3, 15, 34, 51, 47, 39, 25, 16, 31, 48, 49, 41, 17, 46, 1, 57, 12, 24, 38, 36, 45, 40, 55, 2, 27, 6, 5, 4, 53, 8, 22, 65, 64, 23, 21, 7, 28, 44, 29, 19, 58, 50, 26, 60, 61, 37, 43, 14, 20], 'cur_cost': 97557.0}, {'tour': [46, 48, 29, 40, 2, 3, 30, 64, 7, 27, 20, 52, 55, 60, 23, 15, 19, 56, 54, 53, 9, 17, 49, 28, 14, 24, 39, 58, 59, 10, 35, 26, 62, 33, 50, 61, 31, 22, 41, 36, 44, 1, 12, 8, 57, 51, 45, 43, 13, 18, 38, 32, 47, 0, 16, 65, 34, 6, 5, 42, 11, 25, 4, 37, 63, 21], 'cur_cost': 103490.0}, {'tour': [55, 20, 62, 49, 13, 43, 38, 2, 41, 15, 8, 46, 25, 23, 34, 27, 52, 33, 24, 63, 54, 6, 3, 14, 42, 12, 37, 17, 56, 29, 18, 59, 26, 51, 58, 22, 28, 64, 40, 61, 16, 57, 44, 48, 11, 53, 7, 31, 32, 50, 9, 4, 10, 21, 60, 65, 30, 0, 36, 47, 5, 19, 35, 39, 45, 1], 'cur_cost': 109600.0}, {'tour': [19, 63, 49, 37, 42, 62, 21, 7, 51, 64, 20, 1, 22, 59, 38, 2, 58, 46, 44, 16, 14, 0, 45, 25, 15, 5, 40, 56, 13, 9, 18, 55, 23, 39, 60, 41, 3, 11, 12, 35, 31, 43, 17, 4, 29, 32, 28, 54, 48, 6, 53, 27, 61, 36, 50, 57, 8, 10, 65, 26, 24, 34, 33, 30, 52, 47], 'cur_cost': 113542.0}, {'tour': [50, 1, 27, 6, 16, 24, 30, 60, 37, 12, 11, 43, 18, 2, 28, 0, 20, 15, 63, 4, 7, 48, 22, 26, 51, 10, 9, 25, 17, 19, 44, 32, 39, 61, 38, 13, 41, 21, 54, 45, 42, 3, 53, 5, 14, 59, 33, 49, 52, 47, 56, 31, 62, 58, 36, 8, 46, 34, 35, 40, 29, 64, 23, 65, 55, 57], 'cur_cost': 115071.0}, {'tour': [50, 65, 12, 55, 45, 20, 17, 36, 52, 11, 2, 4, 6, 5, 15, 58, 13, 51, 56, 64, 48, 62, 53, 3, 63, 16, 0, 37, 38, 28, 60, 31, 25, 23, 9, 35, 7, 27, 18, 42, 49, 32, 39, 22, 26, 1, 43, 34, 41, 14, 46, 33, 29, 47, 59, 57, 24, 10, 19, 61, 40, 30, 44, 8, 54, 21], 'cur_cost': 107536.0}, {'tour': [30, 14, 19, 58, 13, 62, 28, 41, 59, 3, 20, 53, 15, 16, 26, 2, 43, 46, 44, 21, 18, 17, 27, 38, 37, 48, 35, 11, 29, 50, 31, 4, 1, 61, 65, 64, 0, 40, 47, 55, 25, 57, 63, 51, 54, 39, 52, 32, 23, 34, 24, 8, 49, 56, 12, 6, 9, 36, 22, 42, 60, 5, 45, 10, 33, 7], 'cur_cost': 108852.0}, {'tour': [2, 50, 42, 9, 26, 19, 10, 59, 15, 32, 60, 27, 22, 33, 0, 3, 61, 6, 38, 12, 1, 63, 13, 58, 34, 46, 53, 4, 37, 29, 11, 28, 7, 5, 21, 56, 62, 8, 45, 17, 14, 49, 31, 39, 54, 65, 57, 16, 47, 41, 55, 20, 51, 48, 43, 30, 36, 23, 40, 18, 52, 25, 35, 24, 44, 64], 'cur_cost': 96662.0}]
2025-08-01 17:46:17,582 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-01 17:46:17,582 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 42, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 42, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:46:17,583 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([12, 54, 35, 10, 57, 29, 27, 50, 13, 62, 16, 42, 41,  4, 18, 52, 58,
       39, 55,  9, 21, 64, 36, 24,  6,  1, 56, 45, 33, 30, 47, 43, 53, 15,
       19, 40, 26, 11, 59, 17,  7, 44, 61, 34, 37, 32,  5, 23, 20, 49, 22,
       65,  0,  8, 46, 25, 28, 48, 38,  3, 51, 14, 63,  2, 60, 31],
      dtype=int64), 'cur_cost': 102113.0}
2025-08-01 17:46:17,584 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-01 17:46:17,584 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:17,584 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:17,584 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 107468.0
2025-08-01 17:46:17,683 - ExploitationExpert - INFO - res_population_num: 13
2025-08-01 17:46:17,683 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0]
2025-08-01 17:46:17,684 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64)]
2025-08-01 17:46:17,694 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:17,696 - ExploitationExpert - INFO - populations: [{'tour': array([ 0, 50, 45, 12, 51, 43, 21, 36, 14, 46,  6, 22, 19,  4, 39, 25,  2,
       23, 48, 42, 35, 54, 28, 10, 38, 55, 37, 62, 32, 53, 29,  7, 20, 58,
       17, 31,  1, 41, 13, 27, 47, 18, 24, 56, 33, 65, 64,  8, 63, 16, 49,
        3,  5, 60, 44, 57, 30, 59, 11, 34, 52, 40, 26, 61, 15,  9],
      dtype=int64), 'cur_cost': 121600.0}, {'tour': array([12, 54, 35, 10, 57, 29, 27, 50, 13, 62, 16, 42, 41,  4, 18, 52, 58,
       39, 55,  9, 21, 64, 36, 24,  6,  1, 56, 45, 33, 30, 47, 43, 53, 15,
       19, 40, 26, 11, 59, 17,  7, 44, 61, 34, 37, 32,  5, 23, 20, 49, 22,
       65,  0,  8, 46, 25, 28, 48, 38,  3, 51, 14, 63,  2, 60, 31],
      dtype=int64), 'cur_cost': 102113.0}, {'tour': array([64, 38, 26, 16, 41, 32, 20, 49,  1, 21, 36,  4,  3, 61,  5, 24, 56,
       55, 27, 30, 53,  9, 50, 40, 28, 51, 57, 42, 17, 25, 63, 15, 33, 14,
       13, 44, 43, 54, 22, 39, 23,  6, 34,  7, 47, 46,  0, 60, 58, 18, 10,
       65, 37, 19, 48, 62, 12, 11, 45, 59, 31, 29,  8, 35,  2, 52],
      dtype=int64), 'cur_cost': 107468.0}, {'tour': [46, 48, 29, 40, 2, 3, 30, 64, 7, 27, 20, 52, 55, 60, 23, 15, 19, 56, 54, 53, 9, 17, 49, 28, 14, 24, 39, 58, 59, 10, 35, 26, 62, 33, 50, 61, 31, 22, 41, 36, 44, 1, 12, 8, 57, 51, 45, 43, 13, 18, 38, 32, 47, 0, 16, 65, 34, 6, 5, 42, 11, 25, 4, 37, 63, 21], 'cur_cost': 103490.0}, {'tour': [55, 20, 62, 49, 13, 43, 38, 2, 41, 15, 8, 46, 25, 23, 34, 27, 52, 33, 24, 63, 54, 6, 3, 14, 42, 12, 37, 17, 56, 29, 18, 59, 26, 51, 58, 22, 28, 64, 40, 61, 16, 57, 44, 48, 11, 53, 7, 31, 32, 50, 9, 4, 10, 21, 60, 65, 30, 0, 36, 47, 5, 19, 35, 39, 45, 1], 'cur_cost': 109600.0}, {'tour': [19, 63, 49, 37, 42, 62, 21, 7, 51, 64, 20, 1, 22, 59, 38, 2, 58, 46, 44, 16, 14, 0, 45, 25, 15, 5, 40, 56, 13, 9, 18, 55, 23, 39, 60, 41, 3, 11, 12, 35, 31, 43, 17, 4, 29, 32, 28, 54, 48, 6, 53, 27, 61, 36, 50, 57, 8, 10, 65, 26, 24, 34, 33, 30, 52, 47], 'cur_cost': 113542.0}, {'tour': [50, 1, 27, 6, 16, 24, 30, 60, 37, 12, 11, 43, 18, 2, 28, 0, 20, 15, 63, 4, 7, 48, 22, 26, 51, 10, 9, 25, 17, 19, 44, 32, 39, 61, 38, 13, 41, 21, 54, 45, 42, 3, 53, 5, 14, 59, 33, 49, 52, 47, 56, 31, 62, 58, 36, 8, 46, 34, 35, 40, 29, 64, 23, 65, 55, 57], 'cur_cost': 115071.0}, {'tour': [50, 65, 12, 55, 45, 20, 17, 36, 52, 11, 2, 4, 6, 5, 15, 58, 13, 51, 56, 64, 48, 62, 53, 3, 63, 16, 0, 37, 38, 28, 60, 31, 25, 23, 9, 35, 7, 27, 18, 42, 49, 32, 39, 22, 26, 1, 43, 34, 41, 14, 46, 33, 29, 47, 59, 57, 24, 10, 19, 61, 40, 30, 44, 8, 54, 21], 'cur_cost': 107536.0}, {'tour': [30, 14, 19, 58, 13, 62, 28, 41, 59, 3, 20, 53, 15, 16, 26, 2, 43, 46, 44, 21, 18, 17, 27, 38, 37, 48, 35, 11, 29, 50, 31, 4, 1, 61, 65, 64, 0, 40, 47, 55, 25, 57, 63, 51, 54, 39, 52, 32, 23, 34, 24, 8, 49, 56, 12, 6, 9, 36, 22, 42, 60, 5, 45, 10, 33, 7], 'cur_cost': 108852.0}, {'tour': [2, 50, 42, 9, 26, 19, 10, 59, 15, 32, 60, 27, 22, 33, 0, 3, 61, 6, 38, 12, 1, 63, 13, 58, 34, 46, 53, 4, 37, 29, 11, 28, 7, 5, 21, 56, 62, 8, 45, 17, 14, 49, 31, 39, 54, 65, 57, 16, 47, 41, 55, 20, 51, 48, 43, 30, 36, 23, 40, 18, 52, 25, 35, 24, 44, 64], 'cur_cost': 96662.0}]
2025-08-01 17:46:17,700 - ExploitationExpert - INFO - 局部搜索耗时: 0.12秒
2025-08-01 17:46:17,700 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 43, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 43, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:46:17,701 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([64, 38, 26, 16, 41, 32, 20, 49,  1, 21, 36,  4,  3, 61,  5, 24, 56,
       55, 27, 30, 53,  9, 50, 40, 28, 51, 57, 42, 17, 25, 63, 15, 33, 14,
       13, 44, 43, 54, 22, 39, 23,  6, 34,  7, 47, 46,  0, 60, 58, 18, 10,
       65, 37, 19, 48, 62, 12, 11, 45, 59, 31, 29,  8, 35,  2, 52],
      dtype=int64), 'cur_cost': 107468.0}
2025-08-01 17:46:17,701 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-01 17:46:17,701 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:17,702 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:17,702 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 95332.0
2025-08-01 17:46:17,814 - ExploitationExpert - INFO - res_population_num: 13
2025-08-01 17:46:17,815 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0]
2025-08-01 17:46:17,816 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64)]
2025-08-01 17:46:17,825 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:17,826 - ExploitationExpert - INFO - populations: [{'tour': array([ 0, 50, 45, 12, 51, 43, 21, 36, 14, 46,  6, 22, 19,  4, 39, 25,  2,
       23, 48, 42, 35, 54, 28, 10, 38, 55, 37, 62, 32, 53, 29,  7, 20, 58,
       17, 31,  1, 41, 13, 27, 47, 18, 24, 56, 33, 65, 64,  8, 63, 16, 49,
        3,  5, 60, 44, 57, 30, 59, 11, 34, 52, 40, 26, 61, 15,  9],
      dtype=int64), 'cur_cost': 121600.0}, {'tour': array([12, 54, 35, 10, 57, 29, 27, 50, 13, 62, 16, 42, 41,  4, 18, 52, 58,
       39, 55,  9, 21, 64, 36, 24,  6,  1, 56, 45, 33, 30, 47, 43, 53, 15,
       19, 40, 26, 11, 59, 17,  7, 44, 61, 34, 37, 32,  5, 23, 20, 49, 22,
       65,  0,  8, 46, 25, 28, 48, 38,  3, 51, 14, 63,  2, 60, 31],
      dtype=int64), 'cur_cost': 102113.0}, {'tour': array([64, 38, 26, 16, 41, 32, 20, 49,  1, 21, 36,  4,  3, 61,  5, 24, 56,
       55, 27, 30, 53,  9, 50, 40, 28, 51, 57, 42, 17, 25, 63, 15, 33, 14,
       13, 44, 43, 54, 22, 39, 23,  6, 34,  7, 47, 46,  0, 60, 58, 18, 10,
       65, 37, 19, 48, 62, 12, 11, 45, 59, 31, 29,  8, 35,  2, 52],
      dtype=int64), 'cur_cost': 107468.0}, {'tour': array([64, 26, 30, 42, 23, 28, 22, 47, 20, 54, 52, 27, 48, 50, 38, 24, 59,
       15, 10,  6, 56, 53, 16, 51,  7,  9, 65, 62, 55, 25, 35, 13, 34,  2,
       40, 39, 31,  5, 33, 21, 36,  3, 43, 44, 37, 19, 17,  0, 63,  1,  4,
       46, 45, 32, 60, 12, 61,  8, 14, 18, 49, 58, 29, 57, 11, 41],
      dtype=int64), 'cur_cost': 95332.0}, {'tour': [55, 20, 62, 49, 13, 43, 38, 2, 41, 15, 8, 46, 25, 23, 34, 27, 52, 33, 24, 63, 54, 6, 3, 14, 42, 12, 37, 17, 56, 29, 18, 59, 26, 51, 58, 22, 28, 64, 40, 61, 16, 57, 44, 48, 11, 53, 7, 31, 32, 50, 9, 4, 10, 21, 60, 65, 30, 0, 36, 47, 5, 19, 35, 39, 45, 1], 'cur_cost': 109600.0}, {'tour': [19, 63, 49, 37, 42, 62, 21, 7, 51, 64, 20, 1, 22, 59, 38, 2, 58, 46, 44, 16, 14, 0, 45, 25, 15, 5, 40, 56, 13, 9, 18, 55, 23, 39, 60, 41, 3, 11, 12, 35, 31, 43, 17, 4, 29, 32, 28, 54, 48, 6, 53, 27, 61, 36, 50, 57, 8, 10, 65, 26, 24, 34, 33, 30, 52, 47], 'cur_cost': 113542.0}, {'tour': [50, 1, 27, 6, 16, 24, 30, 60, 37, 12, 11, 43, 18, 2, 28, 0, 20, 15, 63, 4, 7, 48, 22, 26, 51, 10, 9, 25, 17, 19, 44, 32, 39, 61, 38, 13, 41, 21, 54, 45, 42, 3, 53, 5, 14, 59, 33, 49, 52, 47, 56, 31, 62, 58, 36, 8, 46, 34, 35, 40, 29, 64, 23, 65, 55, 57], 'cur_cost': 115071.0}, {'tour': [50, 65, 12, 55, 45, 20, 17, 36, 52, 11, 2, 4, 6, 5, 15, 58, 13, 51, 56, 64, 48, 62, 53, 3, 63, 16, 0, 37, 38, 28, 60, 31, 25, 23, 9, 35, 7, 27, 18, 42, 49, 32, 39, 22, 26, 1, 43, 34, 41, 14, 46, 33, 29, 47, 59, 57, 24, 10, 19, 61, 40, 30, 44, 8, 54, 21], 'cur_cost': 107536.0}, {'tour': [30, 14, 19, 58, 13, 62, 28, 41, 59, 3, 20, 53, 15, 16, 26, 2, 43, 46, 44, 21, 18, 17, 27, 38, 37, 48, 35, 11, 29, 50, 31, 4, 1, 61, 65, 64, 0, 40, 47, 55, 25, 57, 63, 51, 54, 39, 52, 32, 23, 34, 24, 8, 49, 56, 12, 6, 9, 36, 22, 42, 60, 5, 45, 10, 33, 7], 'cur_cost': 108852.0}, {'tour': [2, 50, 42, 9, 26, 19, 10, 59, 15, 32, 60, 27, 22, 33, 0, 3, 61, 6, 38, 12, 1, 63, 13, 58, 34, 46, 53, 4, 37, 29, 11, 28, 7, 5, 21, 56, 62, 8, 45, 17, 14, 49, 31, 39, 54, 65, 57, 16, 47, 41, 55, 20, 51, 48, 43, 30, 36, 23, 40, 18, 52, 25, 35, 24, 44, 64], 'cur_cost': 96662.0}]
2025-08-01 17:46:17,830 - ExploitationExpert - INFO - 局部搜索耗时: 0.13秒
2025-08-01 17:46:17,831 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 44, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 44, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:46:17,831 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([64, 26, 30, 42, 23, 28, 22, 47, 20, 54, 52, 27, 48, 50, 38, 24, 59,
       15, 10,  6, 56, 53, 16, 51,  7,  9, 65, 62, 55, 25, 35, 13, 34,  2,
       40, 39, 31,  5, 33, 21, 36,  3, 43, 44, 37, 19, 17,  0, 63,  1,  4,
       46, 45, 32, 60, 12, 61,  8, 14, 18, 49, 58, 29, 57, 11, 41],
      dtype=int64), 'cur_cost': 95332.0}
2025-08-01 17:46:17,832 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-01 17:46:17,833 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:17,833 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:17,833 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 120467.0
2025-08-01 17:46:17,948 - ExploitationExpert - INFO - res_population_num: 13
2025-08-01 17:46:17,948 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0]
2025-08-01 17:46:17,949 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64)]
2025-08-01 17:46:17,959 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:17,959 - ExploitationExpert - INFO - populations: [{'tour': array([ 0, 50, 45, 12, 51, 43, 21, 36, 14, 46,  6, 22, 19,  4, 39, 25,  2,
       23, 48, 42, 35, 54, 28, 10, 38, 55, 37, 62, 32, 53, 29,  7, 20, 58,
       17, 31,  1, 41, 13, 27, 47, 18, 24, 56, 33, 65, 64,  8, 63, 16, 49,
        3,  5, 60, 44, 57, 30, 59, 11, 34, 52, 40, 26, 61, 15,  9],
      dtype=int64), 'cur_cost': 121600.0}, {'tour': array([12, 54, 35, 10, 57, 29, 27, 50, 13, 62, 16, 42, 41,  4, 18, 52, 58,
       39, 55,  9, 21, 64, 36, 24,  6,  1, 56, 45, 33, 30, 47, 43, 53, 15,
       19, 40, 26, 11, 59, 17,  7, 44, 61, 34, 37, 32,  5, 23, 20, 49, 22,
       65,  0,  8, 46, 25, 28, 48, 38,  3, 51, 14, 63,  2, 60, 31],
      dtype=int64), 'cur_cost': 102113.0}, {'tour': array([64, 38, 26, 16, 41, 32, 20, 49,  1, 21, 36,  4,  3, 61,  5, 24, 56,
       55, 27, 30, 53,  9, 50, 40, 28, 51, 57, 42, 17, 25, 63, 15, 33, 14,
       13, 44, 43, 54, 22, 39, 23,  6, 34,  7, 47, 46,  0, 60, 58, 18, 10,
       65, 37, 19, 48, 62, 12, 11, 45, 59, 31, 29,  8, 35,  2, 52],
      dtype=int64), 'cur_cost': 107468.0}, {'tour': array([64, 26, 30, 42, 23, 28, 22, 47, 20, 54, 52, 27, 48, 50, 38, 24, 59,
       15, 10,  6, 56, 53, 16, 51,  7,  9, 65, 62, 55, 25, 35, 13, 34,  2,
       40, 39, 31,  5, 33, 21, 36,  3, 43, 44, 37, 19, 17,  0, 63,  1,  4,
       46, 45, 32, 60, 12, 61,  8, 14, 18, 49, 58, 29, 57, 11, 41],
      dtype=int64), 'cur_cost': 95332.0}, {'tour': array([56,  3, 47, 10, 57,  4, 22, 16, 13, 37, 15, 32, 54, 26, 52, 19, 38,
        0, 40,  6, 25, 51,  1, 21, 20, 11, 27, 64, 42, 29, 63,  9, 61, 49,
       53, 55, 36,  2, 34, 60, 48, 43, 41, 62, 24, 23,  8, 35, 45, 33,  7,
       50, 65, 28, 31, 44, 30, 18, 14, 17, 58, 12, 46,  5, 59, 39],
      dtype=int64), 'cur_cost': 120467.0}, {'tour': [19, 63, 49, 37, 42, 62, 21, 7, 51, 64, 20, 1, 22, 59, 38, 2, 58, 46, 44, 16, 14, 0, 45, 25, 15, 5, 40, 56, 13, 9, 18, 55, 23, 39, 60, 41, 3, 11, 12, 35, 31, 43, 17, 4, 29, 32, 28, 54, 48, 6, 53, 27, 61, 36, 50, 57, 8, 10, 65, 26, 24, 34, 33, 30, 52, 47], 'cur_cost': 113542.0}, {'tour': [50, 1, 27, 6, 16, 24, 30, 60, 37, 12, 11, 43, 18, 2, 28, 0, 20, 15, 63, 4, 7, 48, 22, 26, 51, 10, 9, 25, 17, 19, 44, 32, 39, 61, 38, 13, 41, 21, 54, 45, 42, 3, 53, 5, 14, 59, 33, 49, 52, 47, 56, 31, 62, 58, 36, 8, 46, 34, 35, 40, 29, 64, 23, 65, 55, 57], 'cur_cost': 115071.0}, {'tour': [50, 65, 12, 55, 45, 20, 17, 36, 52, 11, 2, 4, 6, 5, 15, 58, 13, 51, 56, 64, 48, 62, 53, 3, 63, 16, 0, 37, 38, 28, 60, 31, 25, 23, 9, 35, 7, 27, 18, 42, 49, 32, 39, 22, 26, 1, 43, 34, 41, 14, 46, 33, 29, 47, 59, 57, 24, 10, 19, 61, 40, 30, 44, 8, 54, 21], 'cur_cost': 107536.0}, {'tour': [30, 14, 19, 58, 13, 62, 28, 41, 59, 3, 20, 53, 15, 16, 26, 2, 43, 46, 44, 21, 18, 17, 27, 38, 37, 48, 35, 11, 29, 50, 31, 4, 1, 61, 65, 64, 0, 40, 47, 55, 25, 57, 63, 51, 54, 39, 52, 32, 23, 34, 24, 8, 49, 56, 12, 6, 9, 36, 22, 42, 60, 5, 45, 10, 33, 7], 'cur_cost': 108852.0}, {'tour': [2, 50, 42, 9, 26, 19, 10, 59, 15, 32, 60, 27, 22, 33, 0, 3, 61, 6, 38, 12, 1, 63, 13, 58, 34, 46, 53, 4, 37, 29, 11, 28, 7, 5, 21, 56, 62, 8, 45, 17, 14, 49, 31, 39, 54, 65, 57, 16, 47, 41, 55, 20, 51, 48, 43, 30, 36, 23, 40, 18, 52, 25, 35, 24, 44, 64], 'cur_cost': 96662.0}]
2025-08-01 17:46:17,966 - ExploitationExpert - INFO - 局部搜索耗时: 0.13秒
2025-08-01 17:46:17,967 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 45, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 45, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:46:17,968 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([56,  3, 47, 10, 57,  4, 22, 16, 13, 37, 15, 32, 54, 26, 52, 19, 38,
        0, 40,  6, 25, 51,  1, 21, 20, 11, 27, 64, 42, 29, 63,  9, 61, 49,
       53, 55, 36,  2, 34, 60, 48, 43, 41, 62, 24, 23,  8, 35, 45, 33,  7,
       50, 65, 28, 31, 44, 30, 18, 14, 17, 58, 12, 46,  5, 59, 39],
      dtype=int64), 'cur_cost': 120467.0}
2025-08-01 17:46:17,968 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-01 17:46:17,969 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:17,969 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:17,970 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 92725.0
2025-08-01 17:46:18,083 - ExploitationExpert - INFO - res_population_num: 13
2025-08-01 17:46:18,083 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0]
2025-08-01 17:46:18,083 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64)]
2025-08-01 17:46:18,092 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:18,092 - ExploitationExpert - INFO - populations: [{'tour': array([ 0, 50, 45, 12, 51, 43, 21, 36, 14, 46,  6, 22, 19,  4, 39, 25,  2,
       23, 48, 42, 35, 54, 28, 10, 38, 55, 37, 62, 32, 53, 29,  7, 20, 58,
       17, 31,  1, 41, 13, 27, 47, 18, 24, 56, 33, 65, 64,  8, 63, 16, 49,
        3,  5, 60, 44, 57, 30, 59, 11, 34, 52, 40, 26, 61, 15,  9],
      dtype=int64), 'cur_cost': 121600.0}, {'tour': array([12, 54, 35, 10, 57, 29, 27, 50, 13, 62, 16, 42, 41,  4, 18, 52, 58,
       39, 55,  9, 21, 64, 36, 24,  6,  1, 56, 45, 33, 30, 47, 43, 53, 15,
       19, 40, 26, 11, 59, 17,  7, 44, 61, 34, 37, 32,  5, 23, 20, 49, 22,
       65,  0,  8, 46, 25, 28, 48, 38,  3, 51, 14, 63,  2, 60, 31],
      dtype=int64), 'cur_cost': 102113.0}, {'tour': array([64, 38, 26, 16, 41, 32, 20, 49,  1, 21, 36,  4,  3, 61,  5, 24, 56,
       55, 27, 30, 53,  9, 50, 40, 28, 51, 57, 42, 17, 25, 63, 15, 33, 14,
       13, 44, 43, 54, 22, 39, 23,  6, 34,  7, 47, 46,  0, 60, 58, 18, 10,
       65, 37, 19, 48, 62, 12, 11, 45, 59, 31, 29,  8, 35,  2, 52],
      dtype=int64), 'cur_cost': 107468.0}, {'tour': array([64, 26, 30, 42, 23, 28, 22, 47, 20, 54, 52, 27, 48, 50, 38, 24, 59,
       15, 10,  6, 56, 53, 16, 51,  7,  9, 65, 62, 55, 25, 35, 13, 34,  2,
       40, 39, 31,  5, 33, 21, 36,  3, 43, 44, 37, 19, 17,  0, 63,  1,  4,
       46, 45, 32, 60, 12, 61,  8, 14, 18, 49, 58, 29, 57, 11, 41],
      dtype=int64), 'cur_cost': 95332.0}, {'tour': array([56,  3, 47, 10, 57,  4, 22, 16, 13, 37, 15, 32, 54, 26, 52, 19, 38,
        0, 40,  6, 25, 51,  1, 21, 20, 11, 27, 64, 42, 29, 63,  9, 61, 49,
       53, 55, 36,  2, 34, 60, 48, 43, 41, 62, 24, 23,  8, 35, 45, 33,  7,
       50, 65, 28, 31, 44, 30, 18, 14, 17, 58, 12, 46,  5, 59, 39],
      dtype=int64), 'cur_cost': 120467.0}, {'tour': array([28, 35, 58, 29, 32, 64, 25, 20, 48, 12,  7,  3,  8,  6, 36, 51, 33,
       37, 46, 45, 43, 56, 50, 30, 13, 15,  5, 40, 18,  4, 54, 57, 44,  0,
       52, 38, 47,  1, 27, 59, 60, 63, 19, 17, 23, 34, 39, 24, 53, 55, 62,
       65, 22, 61, 41, 26, 16, 42, 21, 31,  2, 49, 14, 10, 11,  9],
      dtype=int64), 'cur_cost': 92725.0}, {'tour': [50, 1, 27, 6, 16, 24, 30, 60, 37, 12, 11, 43, 18, 2, 28, 0, 20, 15, 63, 4, 7, 48, 22, 26, 51, 10, 9, 25, 17, 19, 44, 32, 39, 61, 38, 13, 41, 21, 54, 45, 42, 3, 53, 5, 14, 59, 33, 49, 52, 47, 56, 31, 62, 58, 36, 8, 46, 34, 35, 40, 29, 64, 23, 65, 55, 57], 'cur_cost': 115071.0}, {'tour': [50, 65, 12, 55, 45, 20, 17, 36, 52, 11, 2, 4, 6, 5, 15, 58, 13, 51, 56, 64, 48, 62, 53, 3, 63, 16, 0, 37, 38, 28, 60, 31, 25, 23, 9, 35, 7, 27, 18, 42, 49, 32, 39, 22, 26, 1, 43, 34, 41, 14, 46, 33, 29, 47, 59, 57, 24, 10, 19, 61, 40, 30, 44, 8, 54, 21], 'cur_cost': 107536.0}, {'tour': [30, 14, 19, 58, 13, 62, 28, 41, 59, 3, 20, 53, 15, 16, 26, 2, 43, 46, 44, 21, 18, 17, 27, 38, 37, 48, 35, 11, 29, 50, 31, 4, 1, 61, 65, 64, 0, 40, 47, 55, 25, 57, 63, 51, 54, 39, 52, 32, 23, 34, 24, 8, 49, 56, 12, 6, 9, 36, 22, 42, 60, 5, 45, 10, 33, 7], 'cur_cost': 108852.0}, {'tour': [2, 50, 42, 9, 26, 19, 10, 59, 15, 32, 60, 27, 22, 33, 0, 3, 61, 6, 38, 12, 1, 63, 13, 58, 34, 46, 53, 4, 37, 29, 11, 28, 7, 5, 21, 56, 62, 8, 45, 17, 14, 49, 31, 39, 54, 65, 57, 16, 47, 41, 55, 20, 51, 48, 43, 30, 36, 23, 40, 18, 52, 25, 35, 24, 44, 64], 'cur_cost': 96662.0}]
2025-08-01 17:46:18,099 - ExploitationExpert - INFO - 局部搜索耗时: 0.13秒
2025-08-01 17:46:18,099 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 46, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 46, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:46:18,100 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([28, 35, 58, 29, 32, 64, 25, 20, 48, 12,  7,  3,  8,  6, 36, 51, 33,
       37, 46, 45, 43, 56, 50, 30, 13, 15,  5, 40, 18,  4, 54, 57, 44,  0,
       52, 38, 47,  1, 27, 59, 60, 63, 19, 17, 23, 34, 39, 24, 53, 55, 62,
       65, 22, 61, 41, 26, 16, 42, 21, 31,  2, 49, 14, 10, 11,  9],
      dtype=int64), 'cur_cost': 92725.0}
2025-08-01 17:46:18,101 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-01 17:46:18,101 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:18,101 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:18,101 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 100703.0
2025-08-01 17:46:18,183 - ExploitationExpert - INFO - res_population_num: 13
2025-08-01 17:46:18,184 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0]
2025-08-01 17:46:18,184 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64)]
2025-08-01 17:46:18,192 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:18,192 - ExploitationExpert - INFO - populations: [{'tour': array([ 0, 50, 45, 12, 51, 43, 21, 36, 14, 46,  6, 22, 19,  4, 39, 25,  2,
       23, 48, 42, 35, 54, 28, 10, 38, 55, 37, 62, 32, 53, 29,  7, 20, 58,
       17, 31,  1, 41, 13, 27, 47, 18, 24, 56, 33, 65, 64,  8, 63, 16, 49,
        3,  5, 60, 44, 57, 30, 59, 11, 34, 52, 40, 26, 61, 15,  9],
      dtype=int64), 'cur_cost': 121600.0}, {'tour': array([12, 54, 35, 10, 57, 29, 27, 50, 13, 62, 16, 42, 41,  4, 18, 52, 58,
       39, 55,  9, 21, 64, 36, 24,  6,  1, 56, 45, 33, 30, 47, 43, 53, 15,
       19, 40, 26, 11, 59, 17,  7, 44, 61, 34, 37, 32,  5, 23, 20, 49, 22,
       65,  0,  8, 46, 25, 28, 48, 38,  3, 51, 14, 63,  2, 60, 31],
      dtype=int64), 'cur_cost': 102113.0}, {'tour': array([64, 38, 26, 16, 41, 32, 20, 49,  1, 21, 36,  4,  3, 61,  5, 24, 56,
       55, 27, 30, 53,  9, 50, 40, 28, 51, 57, 42, 17, 25, 63, 15, 33, 14,
       13, 44, 43, 54, 22, 39, 23,  6, 34,  7, 47, 46,  0, 60, 58, 18, 10,
       65, 37, 19, 48, 62, 12, 11, 45, 59, 31, 29,  8, 35,  2, 52],
      dtype=int64), 'cur_cost': 107468.0}, {'tour': array([64, 26, 30, 42, 23, 28, 22, 47, 20, 54, 52, 27, 48, 50, 38, 24, 59,
       15, 10,  6, 56, 53, 16, 51,  7,  9, 65, 62, 55, 25, 35, 13, 34,  2,
       40, 39, 31,  5, 33, 21, 36,  3, 43, 44, 37, 19, 17,  0, 63,  1,  4,
       46, 45, 32, 60, 12, 61,  8, 14, 18, 49, 58, 29, 57, 11, 41],
      dtype=int64), 'cur_cost': 95332.0}, {'tour': array([56,  3, 47, 10, 57,  4, 22, 16, 13, 37, 15, 32, 54, 26, 52, 19, 38,
        0, 40,  6, 25, 51,  1, 21, 20, 11, 27, 64, 42, 29, 63,  9, 61, 49,
       53, 55, 36,  2, 34, 60, 48, 43, 41, 62, 24, 23,  8, 35, 45, 33,  7,
       50, 65, 28, 31, 44, 30, 18, 14, 17, 58, 12, 46,  5, 59, 39],
      dtype=int64), 'cur_cost': 120467.0}, {'tour': array([28, 35, 58, 29, 32, 64, 25, 20, 48, 12,  7,  3,  8,  6, 36, 51, 33,
       37, 46, 45, 43, 56, 50, 30, 13, 15,  5, 40, 18,  4, 54, 57, 44,  0,
       52, 38, 47,  1, 27, 59, 60, 63, 19, 17, 23, 34, 39, 24, 53, 55, 62,
       65, 22, 61, 41, 26, 16, 42, 21, 31,  2, 49, 14, 10, 11,  9],
      dtype=int64), 'cur_cost': 92725.0}, {'tour': array([ 6, 28, 64, 26, 21, 16, 41,  9, 35, 27, 23, 32,  5, 14, 60, 19, 22,
       52, 40, 56, 51, 18, 24,  1, 34, 57,  3, 43, 45, 25, 48, 44, 49, 37,
       31, 17, 47, 58, 53, 10, 30,  2, 46, 65, 61, 33, 29, 62, 36,  8, 63,
       11, 20,  0, 59, 39, 38, 13, 12, 15,  7, 54, 50, 42, 55,  4],
      dtype=int64), 'cur_cost': 100703.0}, {'tour': [50, 65, 12, 55, 45, 20, 17, 36, 52, 11, 2, 4, 6, 5, 15, 58, 13, 51, 56, 64, 48, 62, 53, 3, 63, 16, 0, 37, 38, 28, 60, 31, 25, 23, 9, 35, 7, 27, 18, 42, 49, 32, 39, 22, 26, 1, 43, 34, 41, 14, 46, 33, 29, 47, 59, 57, 24, 10, 19, 61, 40, 30, 44, 8, 54, 21], 'cur_cost': 107536.0}, {'tour': [30, 14, 19, 58, 13, 62, 28, 41, 59, 3, 20, 53, 15, 16, 26, 2, 43, 46, 44, 21, 18, 17, 27, 38, 37, 48, 35, 11, 29, 50, 31, 4, 1, 61, 65, 64, 0, 40, 47, 55, 25, 57, 63, 51, 54, 39, 52, 32, 23, 34, 24, 8, 49, 56, 12, 6, 9, 36, 22, 42, 60, 5, 45, 10, 33, 7], 'cur_cost': 108852.0}, {'tour': [2, 50, 42, 9, 26, 19, 10, 59, 15, 32, 60, 27, 22, 33, 0, 3, 61, 6, 38, 12, 1, 63, 13, 58, 34, 46, 53, 4, 37, 29, 11, 28, 7, 5, 21, 56, 62, 8, 45, 17, 14, 49, 31, 39, 54, 65, 57, 16, 47, 41, 55, 20, 51, 48, 43, 30, 36, 23, 40, 18, 52, 25, 35, 24, 44, 64], 'cur_cost': 96662.0}]
2025-08-01 17:46:18,200 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-01 17:46:18,200 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 47, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 47, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:46:18,201 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([ 6, 28, 64, 26, 21, 16, 41,  9, 35, 27, 23, 32,  5, 14, 60, 19, 22,
       52, 40, 56, 51, 18, 24,  1, 34, 57,  3, 43, 45, 25, 48, 44, 49, 37,
       31, 17, 47, 58, 53, 10, 30,  2, 46, 65, 61, 33, 29, 62, 36,  8, 63,
       11, 20,  0, 59, 39, 38, 13, 12, 15,  7, 54, 50, 42, 55,  4],
      dtype=int64), 'cur_cost': 100703.0}
2025-08-01 17:46:18,202 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-01 17:46:18,202 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:18,202 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:18,203 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 105542.0
2025-08-01 17:46:18,305 - ExploitationExpert - INFO - res_population_num: 13
2025-08-01 17:46:18,307 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0]
2025-08-01 17:46:18,308 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64)]
2025-08-01 17:46:18,321 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:18,322 - ExploitationExpert - INFO - populations: [{'tour': array([ 0, 50, 45, 12, 51, 43, 21, 36, 14, 46,  6, 22, 19,  4, 39, 25,  2,
       23, 48, 42, 35, 54, 28, 10, 38, 55, 37, 62, 32, 53, 29,  7, 20, 58,
       17, 31,  1, 41, 13, 27, 47, 18, 24, 56, 33, 65, 64,  8, 63, 16, 49,
        3,  5, 60, 44, 57, 30, 59, 11, 34, 52, 40, 26, 61, 15,  9],
      dtype=int64), 'cur_cost': 121600.0}, {'tour': array([12, 54, 35, 10, 57, 29, 27, 50, 13, 62, 16, 42, 41,  4, 18, 52, 58,
       39, 55,  9, 21, 64, 36, 24,  6,  1, 56, 45, 33, 30, 47, 43, 53, 15,
       19, 40, 26, 11, 59, 17,  7, 44, 61, 34, 37, 32,  5, 23, 20, 49, 22,
       65,  0,  8, 46, 25, 28, 48, 38,  3, 51, 14, 63,  2, 60, 31],
      dtype=int64), 'cur_cost': 102113.0}, {'tour': array([64, 38, 26, 16, 41, 32, 20, 49,  1, 21, 36,  4,  3, 61,  5, 24, 56,
       55, 27, 30, 53,  9, 50, 40, 28, 51, 57, 42, 17, 25, 63, 15, 33, 14,
       13, 44, 43, 54, 22, 39, 23,  6, 34,  7, 47, 46,  0, 60, 58, 18, 10,
       65, 37, 19, 48, 62, 12, 11, 45, 59, 31, 29,  8, 35,  2, 52],
      dtype=int64), 'cur_cost': 107468.0}, {'tour': array([64, 26, 30, 42, 23, 28, 22, 47, 20, 54, 52, 27, 48, 50, 38, 24, 59,
       15, 10,  6, 56, 53, 16, 51,  7,  9, 65, 62, 55, 25, 35, 13, 34,  2,
       40, 39, 31,  5, 33, 21, 36,  3, 43, 44, 37, 19, 17,  0, 63,  1,  4,
       46, 45, 32, 60, 12, 61,  8, 14, 18, 49, 58, 29, 57, 11, 41],
      dtype=int64), 'cur_cost': 95332.0}, {'tour': array([56,  3, 47, 10, 57,  4, 22, 16, 13, 37, 15, 32, 54, 26, 52, 19, 38,
        0, 40,  6, 25, 51,  1, 21, 20, 11, 27, 64, 42, 29, 63,  9, 61, 49,
       53, 55, 36,  2, 34, 60, 48, 43, 41, 62, 24, 23,  8, 35, 45, 33,  7,
       50, 65, 28, 31, 44, 30, 18, 14, 17, 58, 12, 46,  5, 59, 39],
      dtype=int64), 'cur_cost': 120467.0}, {'tour': array([28, 35, 58, 29, 32, 64, 25, 20, 48, 12,  7,  3,  8,  6, 36, 51, 33,
       37, 46, 45, 43, 56, 50, 30, 13, 15,  5, 40, 18,  4, 54, 57, 44,  0,
       52, 38, 47,  1, 27, 59, 60, 63, 19, 17, 23, 34, 39, 24, 53, 55, 62,
       65, 22, 61, 41, 26, 16, 42, 21, 31,  2, 49, 14, 10, 11,  9],
      dtype=int64), 'cur_cost': 92725.0}, {'tour': array([ 6, 28, 64, 26, 21, 16, 41,  9, 35, 27, 23, 32,  5, 14, 60, 19, 22,
       52, 40, 56, 51, 18, 24,  1, 34, 57,  3, 43, 45, 25, 48, 44, 49, 37,
       31, 17, 47, 58, 53, 10, 30,  2, 46, 65, 61, 33, 29, 62, 36,  8, 63,
       11, 20,  0, 59, 39, 38, 13, 12, 15,  7, 54, 50, 42, 55,  4],
      dtype=int64), 'cur_cost': 100703.0}, {'tour': array([37,  4, 22,  5, 39, 30, 61, 40, 50, 62, 55, 46, 57, 11, 33, 26, 47,
       28, 36, 27, 38,  7, 10, 21, 65, 18, 54,  2, 60,  1, 48, 25,  3, 44,
       29, 43,  0, 15, 53, 23, 13, 31, 51, 16,  9, 12, 42, 52, 58,  6, 63,
       41, 17, 35, 14, 34, 49, 32, 20,  8, 59, 56, 45, 19, 64, 24],
      dtype=int64), 'cur_cost': 105542.0}, {'tour': [30, 14, 19, 58, 13, 62, 28, 41, 59, 3, 20, 53, 15, 16, 26, 2, 43, 46, 44, 21, 18, 17, 27, 38, 37, 48, 35, 11, 29, 50, 31, 4, 1, 61, 65, 64, 0, 40, 47, 55, 25, 57, 63, 51, 54, 39, 52, 32, 23, 34, 24, 8, 49, 56, 12, 6, 9, 36, 22, 42, 60, 5, 45, 10, 33, 7], 'cur_cost': 108852.0}, {'tour': [2, 50, 42, 9, 26, 19, 10, 59, 15, 32, 60, 27, 22, 33, 0, 3, 61, 6, 38, 12, 1, 63, 13, 58, 34, 46, 53, 4, 37, 29, 11, 28, 7, 5, 21, 56, 62, 8, 45, 17, 14, 49, 31, 39, 54, 65, 57, 16, 47, 41, 55, 20, 51, 48, 43, 30, 36, 23, 40, 18, 52, 25, 35, 24, 44, 64], 'cur_cost': 96662.0}]
2025-08-01 17:46:18,329 - ExploitationExpert - INFO - 局部搜索耗时: 0.13秒
2025-08-01 17:46:18,330 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 48, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 48, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:46:18,331 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([37,  4, 22,  5, 39, 30, 61, 40, 50, 62, 55, 46, 57, 11, 33, 26, 47,
       28, 36, 27, 38,  7, 10, 21, 65, 18, 54,  2, 60,  1, 48, 25,  3, 44,
       29, 43,  0, 15, 53, 23, 13, 31, 51, 16,  9, 12, 42, 52, 58,  6, 63,
       41, 17, 35, 14, 34, 49, 32, 20,  8, 59, 56, 45, 19, 64, 24],
      dtype=int64), 'cur_cost': 105542.0}
2025-08-01 17:46:18,332 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-01 17:46:18,333 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:18,333 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:18,333 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 112730.0
2025-08-01 17:46:18,421 - ExploitationExpert - INFO - res_population_num: 13
2025-08-01 17:46:18,421 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0]
2025-08-01 17:46:18,422 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64)]
2025-08-01 17:46:18,431 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:18,432 - ExploitationExpert - INFO - populations: [{'tour': array([ 0, 50, 45, 12, 51, 43, 21, 36, 14, 46,  6, 22, 19,  4, 39, 25,  2,
       23, 48, 42, 35, 54, 28, 10, 38, 55, 37, 62, 32, 53, 29,  7, 20, 58,
       17, 31,  1, 41, 13, 27, 47, 18, 24, 56, 33, 65, 64,  8, 63, 16, 49,
        3,  5, 60, 44, 57, 30, 59, 11, 34, 52, 40, 26, 61, 15,  9],
      dtype=int64), 'cur_cost': 121600.0}, {'tour': array([12, 54, 35, 10, 57, 29, 27, 50, 13, 62, 16, 42, 41,  4, 18, 52, 58,
       39, 55,  9, 21, 64, 36, 24,  6,  1, 56, 45, 33, 30, 47, 43, 53, 15,
       19, 40, 26, 11, 59, 17,  7, 44, 61, 34, 37, 32,  5, 23, 20, 49, 22,
       65,  0,  8, 46, 25, 28, 48, 38,  3, 51, 14, 63,  2, 60, 31],
      dtype=int64), 'cur_cost': 102113.0}, {'tour': array([64, 38, 26, 16, 41, 32, 20, 49,  1, 21, 36,  4,  3, 61,  5, 24, 56,
       55, 27, 30, 53,  9, 50, 40, 28, 51, 57, 42, 17, 25, 63, 15, 33, 14,
       13, 44, 43, 54, 22, 39, 23,  6, 34,  7, 47, 46,  0, 60, 58, 18, 10,
       65, 37, 19, 48, 62, 12, 11, 45, 59, 31, 29,  8, 35,  2, 52],
      dtype=int64), 'cur_cost': 107468.0}, {'tour': array([64, 26, 30, 42, 23, 28, 22, 47, 20, 54, 52, 27, 48, 50, 38, 24, 59,
       15, 10,  6, 56, 53, 16, 51,  7,  9, 65, 62, 55, 25, 35, 13, 34,  2,
       40, 39, 31,  5, 33, 21, 36,  3, 43, 44, 37, 19, 17,  0, 63,  1,  4,
       46, 45, 32, 60, 12, 61,  8, 14, 18, 49, 58, 29, 57, 11, 41],
      dtype=int64), 'cur_cost': 95332.0}, {'tour': array([56,  3, 47, 10, 57,  4, 22, 16, 13, 37, 15, 32, 54, 26, 52, 19, 38,
        0, 40,  6, 25, 51,  1, 21, 20, 11, 27, 64, 42, 29, 63,  9, 61, 49,
       53, 55, 36,  2, 34, 60, 48, 43, 41, 62, 24, 23,  8, 35, 45, 33,  7,
       50, 65, 28, 31, 44, 30, 18, 14, 17, 58, 12, 46,  5, 59, 39],
      dtype=int64), 'cur_cost': 120467.0}, {'tour': array([28, 35, 58, 29, 32, 64, 25, 20, 48, 12,  7,  3,  8,  6, 36, 51, 33,
       37, 46, 45, 43, 56, 50, 30, 13, 15,  5, 40, 18,  4, 54, 57, 44,  0,
       52, 38, 47,  1, 27, 59, 60, 63, 19, 17, 23, 34, 39, 24, 53, 55, 62,
       65, 22, 61, 41, 26, 16, 42, 21, 31,  2, 49, 14, 10, 11,  9],
      dtype=int64), 'cur_cost': 92725.0}, {'tour': array([ 6, 28, 64, 26, 21, 16, 41,  9, 35, 27, 23, 32,  5, 14, 60, 19, 22,
       52, 40, 56, 51, 18, 24,  1, 34, 57,  3, 43, 45, 25, 48, 44, 49, 37,
       31, 17, 47, 58, 53, 10, 30,  2, 46, 65, 61, 33, 29, 62, 36,  8, 63,
       11, 20,  0, 59, 39, 38, 13, 12, 15,  7, 54, 50, 42, 55,  4],
      dtype=int64), 'cur_cost': 100703.0}, {'tour': array([37,  4, 22,  5, 39, 30, 61, 40, 50, 62, 55, 46, 57, 11, 33, 26, 47,
       28, 36, 27, 38,  7, 10, 21, 65, 18, 54,  2, 60,  1, 48, 25,  3, 44,
       29, 43,  0, 15, 53, 23, 13, 31, 51, 16,  9, 12, 42, 52, 58,  6, 63,
       41, 17, 35, 14, 34, 49, 32, 20,  8, 59, 56, 45, 19, 64, 24],
      dtype=int64), 'cur_cost': 105542.0}, {'tour': array([33, 19, 64,  0, 25, 38, 20, 31,  1, 48, 37, 47, 36,  3, 30, 42, 61,
       28, 24, 15, 46, 14, 11, 22, 45, 41, 13, 55,  9, 65,  8,  6, 52, 57,
       16, 54,  5, 44, 51, 59, 35, 56, 23, 10, 49, 12, 62, 32, 60, 50, 34,
       58, 29, 18, 27, 53,  7, 39, 63, 17,  2,  4, 21, 26, 40, 43],
      dtype=int64), 'cur_cost': 112730.0}, {'tour': [2, 50, 42, 9, 26, 19, 10, 59, 15, 32, 60, 27, 22, 33, 0, 3, 61, 6, 38, 12, 1, 63, 13, 58, 34, 46, 53, 4, 37, 29, 11, 28, 7, 5, 21, 56, 62, 8, 45, 17, 14, 49, 31, 39, 54, 65, 57, 16, 47, 41, 55, 20, 51, 48, 43, 30, 36, 23, 40, 18, 52, 25, 35, 24, 44, 64], 'cur_cost': 96662.0}]
2025-08-01 17:46:18,442 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒
2025-08-01 17:46:18,442 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 49, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 49, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:46:18,443 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([33, 19, 64,  0, 25, 38, 20, 31,  1, 48, 37, 47, 36,  3, 30, 42, 61,
       28, 24, 15, 46, 14, 11, 22, 45, 41, 13, 55,  9, 65,  8,  6, 52, 57,
       16, 54,  5, 44, 51, 59, 35, 56, 23, 10, 49, 12, 62, 32, 60, 50, 34,
       58, 29, 18, 27, 53,  7, 39, 63, 17,  2,  4, 21, 26, 40, 43],
      dtype=int64), 'cur_cost': 112730.0}
2025-08-01 17:46:18,444 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-01 17:46:18,444 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:46:18,444 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:46:18,445 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 102845.0
2025-08-01 17:46:18,534 - ExploitationExpert - INFO - res_population_num: 13
2025-08-01 17:46:18,535 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9529.0, 9856.0]
2025-08-01 17:46:18,535 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49,
       47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 60, 62, 59, 56, 58,
       53, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,  4,  6, 10],
      dtype=int64)]
2025-08-01 17:46:18,548 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:46:18,548 - ExploitationExpert - INFO - populations: [{'tour': array([ 0, 50, 45, 12, 51, 43, 21, 36, 14, 46,  6, 22, 19,  4, 39, 25,  2,
       23, 48, 42, 35, 54, 28, 10, 38, 55, 37, 62, 32, 53, 29,  7, 20, 58,
       17, 31,  1, 41, 13, 27, 47, 18, 24, 56, 33, 65, 64,  8, 63, 16, 49,
        3,  5, 60, 44, 57, 30, 59, 11, 34, 52, 40, 26, 61, 15,  9],
      dtype=int64), 'cur_cost': 121600.0}, {'tour': array([12, 54, 35, 10, 57, 29, 27, 50, 13, 62, 16, 42, 41,  4, 18, 52, 58,
       39, 55,  9, 21, 64, 36, 24,  6,  1, 56, 45, 33, 30, 47, 43, 53, 15,
       19, 40, 26, 11, 59, 17,  7, 44, 61, 34, 37, 32,  5, 23, 20, 49, 22,
       65,  0,  8, 46, 25, 28, 48, 38,  3, 51, 14, 63,  2, 60, 31],
      dtype=int64), 'cur_cost': 102113.0}, {'tour': array([64, 38, 26, 16, 41, 32, 20, 49,  1, 21, 36,  4,  3, 61,  5, 24, 56,
       55, 27, 30, 53,  9, 50, 40, 28, 51, 57, 42, 17, 25, 63, 15, 33, 14,
       13, 44, 43, 54, 22, 39, 23,  6, 34,  7, 47, 46,  0, 60, 58, 18, 10,
       65, 37, 19, 48, 62, 12, 11, 45, 59, 31, 29,  8, 35,  2, 52],
      dtype=int64), 'cur_cost': 107468.0}, {'tour': array([64, 26, 30, 42, 23, 28, 22, 47, 20, 54, 52, 27, 48, 50, 38, 24, 59,
       15, 10,  6, 56, 53, 16, 51,  7,  9, 65, 62, 55, 25, 35, 13, 34,  2,
       40, 39, 31,  5, 33, 21, 36,  3, 43, 44, 37, 19, 17,  0, 63,  1,  4,
       46, 45, 32, 60, 12, 61,  8, 14, 18, 49, 58, 29, 57, 11, 41],
      dtype=int64), 'cur_cost': 95332.0}, {'tour': array([56,  3, 47, 10, 57,  4, 22, 16, 13, 37, 15, 32, 54, 26, 52, 19, 38,
        0, 40,  6, 25, 51,  1, 21, 20, 11, 27, 64, 42, 29, 63,  9, 61, 49,
       53, 55, 36,  2, 34, 60, 48, 43, 41, 62, 24, 23,  8, 35, 45, 33,  7,
       50, 65, 28, 31, 44, 30, 18, 14, 17, 58, 12, 46,  5, 59, 39],
      dtype=int64), 'cur_cost': 120467.0}, {'tour': array([28, 35, 58, 29, 32, 64, 25, 20, 48, 12,  7,  3,  8,  6, 36, 51, 33,
       37, 46, 45, 43, 56, 50, 30, 13, 15,  5, 40, 18,  4, 54, 57, 44,  0,
       52, 38, 47,  1, 27, 59, 60, 63, 19, 17, 23, 34, 39, 24, 53, 55, 62,
       65, 22, 61, 41, 26, 16, 42, 21, 31,  2, 49, 14, 10, 11,  9],
      dtype=int64), 'cur_cost': 92725.0}, {'tour': array([ 6, 28, 64, 26, 21, 16, 41,  9, 35, 27, 23, 32,  5, 14, 60, 19, 22,
       52, 40, 56, 51, 18, 24,  1, 34, 57,  3, 43, 45, 25, 48, 44, 49, 37,
       31, 17, 47, 58, 53, 10, 30,  2, 46, 65, 61, 33, 29, 62, 36,  8, 63,
       11, 20,  0, 59, 39, 38, 13, 12, 15,  7, 54, 50, 42, 55,  4],
      dtype=int64), 'cur_cost': 100703.0}, {'tour': array([37,  4, 22,  5, 39, 30, 61, 40, 50, 62, 55, 46, 57, 11, 33, 26, 47,
       28, 36, 27, 38,  7, 10, 21, 65, 18, 54,  2, 60,  1, 48, 25,  3, 44,
       29, 43,  0, 15, 53, 23, 13, 31, 51, 16,  9, 12, 42, 52, 58,  6, 63,
       41, 17, 35, 14, 34, 49, 32, 20,  8, 59, 56, 45, 19, 64, 24],
      dtype=int64), 'cur_cost': 105542.0}, {'tour': array([33, 19, 64,  0, 25, 38, 20, 31,  1, 48, 37, 47, 36,  3, 30, 42, 61,
       28, 24, 15, 46, 14, 11, 22, 45, 41, 13, 55,  9, 65,  8,  6, 52, 57,
       16, 54,  5, 44, 51, 59, 35, 56, 23, 10, 49, 12, 62, 32, 60, 50, 34,
       58, 29, 18, 27, 53,  7, 39, 63, 17,  2,  4, 21, 26, 40, 43],
      dtype=int64), 'cur_cost': 112730.0}, {'tour': array([22,  2, 33, 32, 13, 23, 58, 11, 31, 30, 65, 53,  5, 50, 51, 36, 27,
        4, 38, 56, 17, 64, 62, 10, 55, 34, 57, 16, 15, 12, 28,  7, 54, 18,
       41, 49, 63, 24, 39, 45,  0, 26, 42, 52, 46, 21, 59, 25, 47, 35,  1,
        3, 29, 19, 43, 44, 61, 48, 37, 14, 40,  8,  9, 20, 60,  6],
      dtype=int64), 'cur_cost': 102845.0}]
2025-08-01 17:46:18,556 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒
2025-08-01 17:46:18,556 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 50, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 50, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:46:18,557 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([22,  2, 33, 32, 13, 23, 58, 11, 31, 30, 65, 53,  5, 50, 51, 36, 27,
        4, 38, 56, 17, 64, 62, 10, 55, 34, 57, 16, 15, 12, 28,  7, 54, 18,
       41, 49, 63, 24, 39, 45,  0, 26, 42, 52, 46, 21, 59, 25, 47, 35,  1,
        3, 29, 19, 43, 44, 61, 48, 37, 14, 40,  8,  9, 20, 60,  6],
      dtype=int64), 'cur_cost': 102845.0}
2025-08-01 17:46:18,559 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 0, 50, 45, 12, 51, 43, 21, 36, 14, 46,  6, 22, 19,  4, 39, 25,  2,
       23, 48, 42, 35, 54, 28, 10, 38, 55, 37, 62, 32, 53, 29,  7, 20, 58,
       17, 31,  1, 41, 13, 27, 47, 18, 24, 56, 33, 65, 64,  8, 63, 16, 49,
        3,  5, 60, 44, 57, 30, 59, 11, 34, 52, 40, 26, 61, 15,  9],
      dtype=int64), 'cur_cost': 121600.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([12, 54, 35, 10, 57, 29, 27, 50, 13, 62, 16, 42, 41,  4, 18, 52, 58,
       39, 55,  9, 21, 64, 36, 24,  6,  1, 56, 45, 33, 30, 47, 43, 53, 15,
       19, 40, 26, 11, 59, 17,  7, 44, 61, 34, 37, 32,  5, 23, 20, 49, 22,
       65,  0,  8, 46, 25, 28, 48, 38,  3, 51, 14, 63,  2, 60, 31],
      dtype=int64), 'cur_cost': 102113.0}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([64, 38, 26, 16, 41, 32, 20, 49,  1, 21, 36,  4,  3, 61,  5, 24, 56,
       55, 27, 30, 53,  9, 50, 40, 28, 51, 57, 42, 17, 25, 63, 15, 33, 14,
       13, 44, 43, 54, 22, 39, 23,  6, 34,  7, 47, 46,  0, 60, 58, 18, 10,
       65, 37, 19, 48, 62, 12, 11, 45, 59, 31, 29,  8, 35,  2, 52],
      dtype=int64), 'cur_cost': 107468.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([64, 26, 30, 42, 23, 28, 22, 47, 20, 54, 52, 27, 48, 50, 38, 24, 59,
       15, 10,  6, 56, 53, 16, 51,  7,  9, 65, 62, 55, 25, 35, 13, 34,  2,
       40, 39, 31,  5, 33, 21, 36,  3, 43, 44, 37, 19, 17,  0, 63,  1,  4,
       46, 45, 32, 60, 12, 61,  8, 14, 18, 49, 58, 29, 57, 11, 41],
      dtype=int64), 'cur_cost': 95332.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([56,  3, 47, 10, 57,  4, 22, 16, 13, 37, 15, 32, 54, 26, 52, 19, 38,
        0, 40,  6, 25, 51,  1, 21, 20, 11, 27, 64, 42, 29, 63,  9, 61, 49,
       53, 55, 36,  2, 34, 60, 48, 43, 41, 62, 24, 23,  8, 35, 45, 33,  7,
       50, 65, 28, 31, 44, 30, 18, 14, 17, 58, 12, 46,  5, 59, 39],
      dtype=int64), 'cur_cost': 120467.0}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([28, 35, 58, 29, 32, 64, 25, 20, 48, 12,  7,  3,  8,  6, 36, 51, 33,
       37, 46, 45, 43, 56, 50, 30, 13, 15,  5, 40, 18,  4, 54, 57, 44,  0,
       52, 38, 47,  1, 27, 59, 60, 63, 19, 17, 23, 34, 39, 24, 53, 55, 62,
       65, 22, 61, 41, 26, 16, 42, 21, 31,  2, 49, 14, 10, 11,  9],
      dtype=int64), 'cur_cost': 92725.0}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 6, 28, 64, 26, 21, 16, 41,  9, 35, 27, 23, 32,  5, 14, 60, 19, 22,
       52, 40, 56, 51, 18, 24,  1, 34, 57,  3, 43, 45, 25, 48, 44, 49, 37,
       31, 17, 47, 58, 53, 10, 30,  2, 46, 65, 61, 33, 29, 62, 36,  8, 63,
       11, 20,  0, 59, 39, 38, 13, 12, 15,  7, 54, 50, 42, 55,  4],
      dtype=int64), 'cur_cost': 100703.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([37,  4, 22,  5, 39, 30, 61, 40, 50, 62, 55, 46, 57, 11, 33, 26, 47,
       28, 36, 27, 38,  7, 10, 21, 65, 18, 54,  2, 60,  1, 48, 25,  3, 44,
       29, 43,  0, 15, 53, 23, 13, 31, 51, 16,  9, 12, 42, 52, 58,  6, 63,
       41, 17, 35, 14, 34, 49, 32, 20,  8, 59, 56, 45, 19, 64, 24],
      dtype=int64), 'cur_cost': 105542.0}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([33, 19, 64,  0, 25, 38, 20, 31,  1, 48, 37, 47, 36,  3, 30, 42, 61,
       28, 24, 15, 46, 14, 11, 22, 45, 41, 13, 55,  9, 65,  8,  6, 52, 57,
       16, 54,  5, 44, 51, 59, 35, 56, 23, 10, 49, 12, 62, 32, 60, 50, 34,
       58, 29, 18, 27, 53,  7, 39, 63, 17,  2,  4, 21, 26, 40, 43],
      dtype=int64), 'cur_cost': 112730.0}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([22,  2, 33, 32, 13, 23, 58, 11, 31, 30, 65, 53,  5, 50, 51, 36, 27,
        4, 38, 56, 17, 64, 62, 10, 55, 34, 57, 16, 15, 12, 28,  7, 54, 18,
       41, 49, 63, 24, 39, 45,  0, 26, 42, 52, 46, 21, 59, 25, 47, 35,  1,
        3, 29, 19, 43, 44, 61, 48, 37, 14, 40,  8,  9, 20, 60,  6],
      dtype=int64), 'cur_cost': 102845.0}}]
2025-08-01 17:46:18,563 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-01 17:46:18,563 - StatsExpert - INFO - 开始统计分析
2025-08-01 17:46:18,568 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=92725.000, 多样性=0.981
2025-08-01 17:46:18,568 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-01 17:46:18,568 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-01 17:46:18,569 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-01 17:46:18,572 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.020714734351020725, 'best_improvement': 0.011881926683716964}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.004779788323660133}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.022200520791847297, 'recent_improvements': [-0.04741422891216359, 0.006543660847529195, -0.0030131873284689943], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 13, 'new_count': 13, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.7698135198135198, 'new_diversity': 0.7698135198135198, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-01 17:46:18,576 - main - INFO - --- Finished Evolution Iteration 5 ---
2025-08-01 17:46:18,589 - main - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_solution.json
2025-08-01 17:46:18,589 - main - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_20250801_174618.solution
2025-08-01 17:46:18,590 - main - INFO - 实例 composite13_66 处理完成
