"""
Strategy configuration classes for the intelligent strategy selection system.

This module defines configuration classes for exploration and exploitation
strategies, including their parameters and execution settings.
"""

from dataclasses import dataclass, field
from typing import Dict, Any, Optional


@dataclass
class ExplorationConfig:
    """Configuration for exploration strategies."""
    
    # Strong exploration settings
    strong_perturbation_intensity: float = 0.8
    strong_diversification_factor: float = 0.9
    strong_time_budget_multiplier: float = 1.5
    
    # Balanced exploration settings
    balanced_perturbation_intensity: float = 0.5
    balanced_diversification_factor: float = 0.6
    balanced_time_budget_multiplier: float = 1.0
    
    # Intelligent exploration settings
    intelligent_perturbation_intensity: float = 0.6
    intelligent_diversification_factor: float = 0.7
    intelligent_time_budget_multiplier: float = 1.2
    intelligent_adaptive_factor: float = 0.3
    
    # General exploration settings
    min_perturbation_intensity: float = 0.1
    max_perturbation_intensity: float = 1.0
    diversification_threshold: float = 0.5
    exploration_timeout: float = 10.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'strong_perturbation_intensity': self.strong_perturbation_intensity,
            'strong_diversification_factor': self.strong_diversification_factor,
            'strong_time_budget_multiplier': self.strong_time_budget_multiplier,
            'balanced_perturbation_intensity': self.balanced_perturbation_intensity,
            'balanced_diversification_factor': self.balanced_diversification_factor,
            'balanced_time_budget_multiplier': self.balanced_time_budget_multiplier,
            'intelligent_perturbation_intensity': self.intelligent_perturbation_intensity,
            'intelligent_diversification_factor': self.intelligent_diversification_factor,
            'intelligent_time_budget_multiplier': self.intelligent_time_budget_multiplier,
            'intelligent_adaptive_factor': self.intelligent_adaptive_factor,
            'min_perturbation_intensity': self.min_perturbation_intensity,
            'max_perturbation_intensity': self.max_perturbation_intensity,
            'diversification_threshold': self.diversification_threshold,
            'exploration_timeout': self.exploration_timeout
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ExplorationConfig':
        """Create from dictionary."""
        config = cls()
        for key, value in data.items():
            if hasattr(config, key):
                setattr(config, key, value)
        return config


@dataclass
class ExploitationConfig:
    """Configuration for exploitation strategies."""
    
    # Cautious exploitation settings
    cautious_local_search_intensity: float = 0.3
    cautious_neighborhood_size: int = 10
    cautious_time_budget_multiplier: float = 0.8
    
    # Moderate exploitation settings
    moderate_local_search_intensity: float = 0.5
    moderate_neighborhood_size: int = 20
    moderate_time_budget_multiplier: float = 1.0
    
    # Aggressive exploitation settings
    aggressive_local_search_intensity: float = 0.8
    aggressive_neighborhood_size: int = 50
    aggressive_time_budget_multiplier: float = 1.5
    
    # Intensive exploitation settings
    intensive_local_search_intensity: float = 1.0
    intensive_neighborhood_size: int = 100
    intensive_time_budget_multiplier: float = 2.0
    intensive_max_iterations: int = 1000
    
    # General exploitation settings
    min_local_search_intensity: float = 0.1
    max_local_search_intensity: float = 1.0
    default_neighborhood_size: int = 30
    exploitation_timeout: float = 15.0
    improvement_threshold: float = 0.001
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'cautious_local_search_intensity': self.cautious_local_search_intensity,
            'cautious_neighborhood_size': self.cautious_neighborhood_size,
            'cautious_time_budget_multiplier': self.cautious_time_budget_multiplier,
            'moderate_local_search_intensity': self.moderate_local_search_intensity,
            'moderate_neighborhood_size': self.moderate_neighborhood_size,
            'moderate_time_budget_multiplier': self.moderate_time_budget_multiplier,
            'aggressive_local_search_intensity': self.aggressive_local_search_intensity,
            'aggressive_neighborhood_size': self.aggressive_neighborhood_size,
            'aggressive_time_budget_multiplier': self.aggressive_time_budget_multiplier,
            'intensive_local_search_intensity': self.intensive_local_search_intensity,
            'intensive_neighborhood_size': self.intensive_neighborhood_size,
            'intensive_time_budget_multiplier': self.intensive_time_budget_multiplier,
            'intensive_max_iterations': self.intensive_max_iterations,
            'min_local_search_intensity': self.min_local_search_intensity,
            'max_local_search_intensity': self.max_local_search_intensity,
            'default_neighborhood_size': self.default_neighborhood_size,
            'exploitation_timeout': self.exploitation_timeout,
            'improvement_threshold': self.improvement_threshold
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ExploitationConfig':
        """Create from dictionary."""
        config = cls()
        for key, value in data.items():
            if hasattr(config, key):
                setattr(config, key, value)
        return config


@dataclass
class HybridConfig:
    """Configuration for hybrid strategies."""
    
    # Adaptive hybrid settings
    adaptive_exploration_ratio: float = 0.6
    adaptive_exploitation_ratio: float = 0.4
    adaptive_adjustment_factor: float = 0.1
    adaptive_min_exploration_ratio: float = 0.2
    adaptive_max_exploration_ratio: float = 0.8
    
    # Iterative hybrid settings
    iterative_exploration_phases: int = 3
    iterative_exploitation_phases: int = 2
    iterative_phase_duration: float = 5.0
    iterative_transition_threshold: float = 0.05
    
    # General hybrid settings
    hybrid_timeout: float = 20.0
    balance_adjustment_frequency: int = 10
    performance_evaluation_window: int = 50
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'adaptive_exploration_ratio': self.adaptive_exploration_ratio,
            'adaptive_exploitation_ratio': self.adaptive_exploitation_ratio,
            'adaptive_adjustment_factor': self.adaptive_adjustment_factor,
            'adaptive_min_exploration_ratio': self.adaptive_min_exploration_ratio,
            'adaptive_max_exploration_ratio': self.adaptive_max_exploration_ratio,
            'iterative_exploration_phases': self.iterative_exploration_phases,
            'iterative_exploitation_phases': self.iterative_exploitation_phases,
            'iterative_phase_duration': self.iterative_phase_duration,
            'iterative_transition_threshold': self.iterative_transition_threshold,
            'hybrid_timeout': self.hybrid_timeout,
            'balance_adjustment_frequency': self.balance_adjustment_frequency,
            'performance_evaluation_window': self.performance_evaluation_window
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'HybridConfig':
        """Create from dictionary."""
        config = cls()
        for key, value in data.items():
            if hasattr(config, key):
                setattr(config, key, value)
        return config


# Default configurations
DEFAULT_EXPLORATION_CONFIG = ExplorationConfig()
DEFAULT_EXPLOITATION_CONFIG = ExploitationConfig()
DEFAULT_HYBRID_CONFIG = HybridConfig()
