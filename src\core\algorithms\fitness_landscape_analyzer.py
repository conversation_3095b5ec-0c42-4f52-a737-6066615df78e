# -*- coding: utf-8 -*-
"""
适应度景观分析器

实现6个核心量化指标的计算：局部最优密度、适应度梯度、解聚类程度、
搜索空间覆盖率、收敛趋势和多样性保持度。
"""

import numpy as np
from typing import Dict, List, Any, Tuple, Optional, Union
import logging
from numba import jit
from sklearn.cluster import DBSCAN
from sklearn.metrics import silhouette_score
from scipy.spatial.distance import pdist, squareform
from collections import defaultdict
import time

from .incremental_updater import IncrementalUpdater

logger = logging.getLogger(__name__)


class DataTypeUtils:
    """数据类型处理工具类"""

    @staticmethod
    def ensure_numeric_type(value: Union[str, int, float, None]) -> float:
        """确保值为数值类型"""
        if isinstance(value, str):
            try:
                return float(value)
            except (ValueError, TypeError):
                return 0.0
        elif value is None:
            return 0.0
        else:
            try:
                return float(value)
            except (ValueError, TypeError):
                return 0.0

    @staticmethod
    def safe_compare(a: Union[str, int, float], b: Union[str, int, float], operation: str = 'gt') -> bool:
        """安全的数值比较"""
        a_num = DataTypeUtils.ensure_numeric_type(a)
        b_num = DataTypeUtils.ensure_numeric_type(b)

        if operation == 'gt':
            return a_num > b_num
        elif operation == 'lt':
            return a_num < b_num
        elif operation == 'eq':
            return abs(a_num - b_num) < 1e-10
        elif operation == 'ge':
            return a_num >= b_num
        elif operation == 'le':
            return a_num <= b_num
        else:
            raise ValueError(f"Unsupported operation: {operation}")

    @staticmethod
    def standardize_fitness_values(fitness_values: List[Union[str, int, float]]) -> List[float]:
        """标准化适应度值列表"""
        return [DataTypeUtils.ensure_numeric_type(val) for val in fitness_values]

    @staticmethod
    def standardize_elite_data(elite_solutions: List[Any]) -> List[Dict[str, Any]]:
        """标准化精英解数据格式"""
        standardized = []
        for solution in elite_solutions:
            if isinstance(solution, dict):
                # 确保包含必要字段
                std_solution = {
                    'tour': solution.get('tour', []),
                    'cur_cost': DataTypeUtils.ensure_numeric_type(solution.get('cur_cost', 0)),
                    'fitness': DataTypeUtils.ensure_numeric_type(solution.get('fitness', solution.get('cur_cost', 0)))
                }
            else:
                # 处理其他格式
                std_solution = {
                    'tour': getattr(solution, 'tour', []),
                    'cur_cost': DataTypeUtils.ensure_numeric_type(getattr(solution, 'cur_cost', 0)),
                    'fitness': DataTypeUtils.ensure_numeric_type(getattr(solution, 'fitness', getattr(solution, 'cur_cost', 0)))
                }
            standardized.append(std_solution)
        return standardized


class LocalOptimaAnalyzer:
    """局部最优密度分析器"""

    def __init__(self, k_neighbors: int = 5):
        """
        初始化局部最优分析器

        参数:
            k_neighbors: k-近邻数量
        """
        self.k_neighbors = k_neighbors
        self.logger = logging.getLogger(__name__)

    def analyze(self, populations: List[List[int]], fitness_values: List[Union[str, int, float]],
                neighbor_graph: Optional[Dict[str, Any]] = None) -> Dict[str, float]:
        """
        分析局部最优密度

        参数:
            populations: 种群个体（路径表示）
            fitness_values: 适应度值
            neighbor_graph: 预计算的邻居图（可选）

        返回:
            局部最优密度指标
        """
        if len(populations) != len(fitness_values):
            raise ValueError("种群大小与适应度值数量不匹配")

        n = len(populations)
        if n == 0:
            return {'local_optima_density': 0.0, 'local_optima_count': 0}

        # 标准化适应度值
        standardized_fitness = DataTypeUtils.standardize_fitness_values(fitness_values)

        # 使用预计算的邻居图或重新计算
        if neighbor_graph is None:
            neighbor_graph = self._build_neighbor_graph(populations)

        # 识别局部最优个体
        local_optima_count = 0
        local_optima_indices = []

        for i in range(n):
            if self._is_local_optimum(i, standardized_fitness, neighbor_graph['graph']):
                local_optima_count += 1
                local_optima_indices.append(i)

        density = local_optima_count / n if n > 0 else 0.0

        # 计算局部最优的质量分布
        if local_optima_indices:
            local_optima_fitness = [standardized_fitness[i] for i in local_optima_indices]
            quality_variance = np.var(local_optima_fitness)
            avg_quality = np.mean(local_optima_fitness)
        else:
            quality_variance = 0.0
            avg_quality = 0.0

        return {
            'local_optima_density': density,
            'local_optima_count': local_optima_count,
            'quality_variance': quality_variance,
            'average_quality': avg_quality,
            'local_optima_indices': local_optima_indices
        }

    def _build_neighbor_graph(self, populations: List[List[int]]) -> Dict[str, Any]:
        """构建k-近邻图"""
        n = len(populations)
        k = min(self.k_neighbors, n - 1)

        # 计算距离矩阵
        distance_matrix = np.zeros((n, n))
        for i in range(n):
            for j in range(i + 1, n):
                dist = self._hamming_distance(populations[i], populations[j])
                distance_matrix[i, j] = dist
                distance_matrix[j, i] = dist

        # 构建邻居图
        neighbor_graph = {}
        for i in range(n):
            distances = distance_matrix[i]
            neighbor_indices = np.argsort(distances)[1:k+1]  # 排除自己
            neighbor_graph[i] = {
                'neighbors': neighbor_indices.tolist(),
                'distances': distances[neighbor_indices].tolist()
            }

        return {'graph': neighbor_graph, 'distance_matrix': distance_matrix}

    def _is_local_optimum(self, index: int, fitness_values: List[Union[str, int, float]],
                         neighbor_graph: Dict[int, Dict[str, List]]) -> bool:
        """判断个体是否为局部最优"""
        if index not in neighbor_graph:
            return False

        current_fitness = DataTypeUtils.ensure_numeric_type(fitness_values[index])
        neighbors = neighbor_graph[index]['neighbors']

        # 检查是否比所有邻居都好（适应度更小）
        for neighbor_idx in neighbors:
            neighbor_fitness = DataTypeUtils.ensure_numeric_type(fitness_values[neighbor_idx])
            if neighbor_fitness < current_fitness:
                return False

        return True

    @staticmethod
    @jit(nopython=True)
    def _hamming_distance(path1: List[int], path2: List[int]) -> int:
        """计算汉明距离"""
        if len(path1) != len(path2):
            return max(len(path1), len(path2))

        distance = 0
        for i in range(len(path1)):
            if path1[i] != path2[i]:
                distance += 1
        return distance


class GradientAnalyzer:
    """适应度梯度分析器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def analyze(self, populations: List[List[int]], fitness_values: List[Union[str, int, float]],
                neighbor_graph: Optional[Dict[str, Any]] = None) -> Dict[str, float]:
        """
        分析适应度梯度

        参数:
            populations: 种群个体
            fitness_values: 适应度值
            neighbor_graph: 邻居图

        返回:
            梯度分析结果
        """
        if len(populations) != len(fitness_values):
            raise ValueError("种群大小与适应度值数量不匹配")

        n = len(populations)
        if n == 0:
            return {'gradient_mean': 0.0, 'gradient_variance': 0.0}

        # 标准化适应度值
        standardized_fitness = DataTypeUtils.standardize_fitness_values(fitness_values)

        if neighbor_graph is None:
            # 简化版本：使用全连接图
            gradients = self._calculate_global_gradients(standardized_fitness)
        else:
            # 使用邻居图计算局部梯度
            gradients = self._calculate_local_gradients(standardized_fitness, neighbor_graph['graph'])

        if not gradients:
            return {'gradient_mean': 0.0, 'gradient_variance': 0.0}

        gradient_mean = np.mean(gradients)
        gradient_variance = np.var(gradients)
        gradient_std = np.std(gradients)

        # 识别梯度异常区域
        threshold = gradient_mean + 2 * gradient_std
        anomaly_count = sum(1 for g in gradients if abs(g) > threshold)
        anomaly_ratio = anomaly_count / len(gradients)

        return {
            'gradient_mean': gradient_mean,
            'gradient_variance': gradient_variance,
            'gradient_std': gradient_std,
            'anomaly_ratio': anomaly_ratio,
            'anomaly_count': anomaly_count
        }

    def _calculate_local_gradients(self, fitness_values: List[float],
                                 neighbor_graph: Dict[int, Dict[str, List]]) -> List[float]:
        """基于邻居图计算局部梯度"""
        gradients = []

        for i, fitness in enumerate(fitness_values):
            if i not in neighbor_graph:
                continue

            neighbors = neighbor_graph[i]['neighbors']
            if not neighbors:
                continue

            # 计算与邻居的适应度差的平均值
            neighbor_fitness = [fitness_values[j] for j in neighbors]
            gradient = np.mean([neighbor_f - fitness for neighbor_f in neighbor_fitness])
            gradients.append(gradient)

        return gradients

    def _calculate_global_gradients(self, fitness_values: List[float]) -> List[float]:
        """计算全局梯度（简化版本）"""
        if len(fitness_values) < 2:
            return []

        # 使用相邻个体的适应度差作为梯度近似
        gradients = []
        for i in range(len(fitness_values) - 1):
            gradient = fitness_values[i + 1] - fitness_values[i]
            gradients.append(gradient)

        return gradients


class ClusteringAnalyzer:
    """解聚类程度分析器"""

    def __init__(self, eps: float = 0.5, min_samples: int = 3):
        """
        初始化聚类分析器

        参数:
            eps: DBSCAN的邻域半径
            min_samples: DBSCAN的最小样本数
        """
        self.eps = eps
        self.min_samples = min_samples
        self.logger = logging.getLogger(__name__)

    def analyze(self, populations: List[List[int]], fitness_values: List[Union[str, int, float]],
                distance_matrix: Optional[np.ndarray] = None) -> Dict[str, float]:
        """
        分析解的聚类程度

        参数:
            populations: 种群个体
            fitness_values: 适应度值
            distance_matrix: 预计算的距离矩阵

        返回:
            聚类分析结果
        """
        if len(populations) != len(fitness_values):
            raise ValueError("种群大小与适应度值数量不匹配")

        n = len(populations)
        if n < 3:
            return {'clustering_coefficient': 0.0, 'n_clusters': 0, 'silhouette_score': 0.0}

        # 标准化适应度值
        standardized_fitness = DataTypeUtils.standardize_fitness_values(fitness_values)

        # 计算或使用预计算的距离矩阵
        if distance_matrix is None:
            distance_matrix = self._compute_distance_matrix(populations)

        # 执行DBSCAN聚类
        clustering = DBSCAN(eps=self.eps, min_samples=self.min_samples, metric='precomputed')
        cluster_labels = clustering.fit_predict(distance_matrix)

        # 计算聚类指标
        n_clusters = len(set(cluster_labels)) - (1 if -1 in cluster_labels else 0)
        n_noise = list(cluster_labels).count(-1)

        # 计算聚类系数
        clustering_coefficient = self._calculate_clustering_coefficient(
            distance_matrix, cluster_labels
        )

        # 计算轮廓系数
        silhouette_avg = 0.0
        if n_clusters > 1 and n_noise < n:
            try:
                silhouette_avg = silhouette_score(distance_matrix, cluster_labels, metric='precomputed')
            except ValueError:
                silhouette_avg = 0.0

        # 计算聚类质量分布
        cluster_quality = self._analyze_cluster_quality(cluster_labels, standardized_fitness)

        return {
            'clustering_coefficient': clustering_coefficient,
            'n_clusters': n_clusters,
            'n_noise': n_noise,
            'noise_ratio': n_noise / n,
            'silhouette_score': silhouette_avg,
            'cluster_quality_variance': cluster_quality['variance'],
            'cluster_quality_range': cluster_quality['range']
        }

    def _compute_distance_matrix(self, populations: List[List[int]]) -> np.ndarray:
        """计算距离矩阵"""
        n = len(populations)
        distance_matrix = np.zeros((n, n))

        for i in range(n):
            for j in range(i + 1, n):
                dist = self._hamming_distance(populations[i], populations[j])
                distance_matrix[i, j] = dist
                distance_matrix[j, i] = dist

        return distance_matrix

    def _calculate_clustering_coefficient(self, distance_matrix: np.ndarray,
                                        cluster_labels: np.ndarray) -> float:
        """计算聚类系数"""
        n = len(cluster_labels)
        if n < 3:
            return 0.0

        total_coefficient = 0.0
        valid_nodes = 0

        for i in range(n):
            if cluster_labels[i] == -1:  # 噪声点
                continue

            # 找到同一聚类的邻居
            cluster_neighbors = [j for j in range(n)
                               if j != i and cluster_labels[j] == cluster_labels[i]]

            if len(cluster_neighbors) < 2:
                continue

            # 计算邻居间的连接数
            connections = 0
            possible_connections = len(cluster_neighbors) * (len(cluster_neighbors) - 1) // 2

            for j in range(len(cluster_neighbors)):
                for k in range(j + 1, len(cluster_neighbors)):
                    idx_j, idx_k = cluster_neighbors[j], cluster_neighbors[k]
                    # 如果距离小于阈值，认为有连接
                    if distance_matrix[idx_j, idx_k] < self.eps:
                        connections += 1

            if possible_connections > 0:
                coefficient = connections / possible_connections
                total_coefficient += coefficient
                valid_nodes += 1

        return total_coefficient / valid_nodes if valid_nodes > 0 else 0.0

    def _analyze_cluster_quality(self, cluster_labels: np.ndarray,
                               fitness_values: List[float]) -> Dict[str, float]:
        """分析聚类质量"""
        clusters = defaultdict(list)

        for i, label in enumerate(cluster_labels):
            if label != -1:  # 排除噪声点
                clusters[label].append(fitness_values[i])

        if not clusters:
            return {'variance': 0.0, 'range': 0.0}

        # 计算每个聚类的平均适应度
        cluster_means = [np.mean(fitness_list) for fitness_list in clusters.values()]

        # 计算聚类质量的方差和范围
        quality_variance = np.var(cluster_means) if len(cluster_means) > 1 else 0.0
        quality_range = max(cluster_means) - min(cluster_means) if cluster_means else 0.0

        return {'variance': quality_variance, 'range': quality_range}

    @staticmethod
    @jit(nopython=True)
    def _hamming_distance(path1: List[int], path2: List[int]) -> int:
        """计算汉明距离"""
        if len(path1) != len(path2):
            return max(len(path1), len(path2))

        distance = 0
        for i in range(len(path1)):
            if path1[i] != path2[i]:
                distance += 1
        return distance


class CoverageAnalyzer:
    """搜索空间覆盖分析器"""

    def __init__(self, grid_size: int = 100):
        """
        初始化覆盖分析器

        参数:
            grid_size: 网格大小
        """
        self.grid_size = grid_size
        self.logger = logging.getLogger(__name__)

    def analyze(self, populations: List[List[int]], fitness_values: List[float],
                grid_state: Optional[Dict[str, Any]] = None) -> Dict[str, float]:
        """
        分析搜索空间覆盖率

        参数:
            populations: 种群个体
            fitness_values: 适应度值
            grid_state: 预计算的网格状态

        返回:
            覆盖率分析结果
        """
        if not populations:
            return {'coverage_ratio': 0.0, 'coverage_density': 0.0}

        # 使用预计算的网格状态或重新计算
        if grid_state is None:
            grid = self._build_coverage_grid(populations)
        else:
            grid = grid_state['grid']

        # 计算基本覆盖指标
        total_cells = self.grid_size * self.grid_size
        covered_cells = np.sum(grid > 0)
        coverage_ratio = covered_cells / total_cells

        # 计算覆盖密度分布
        non_zero_cells = grid[grid > 0]
        coverage_density = np.mean(non_zero_cells) if len(non_zero_cells) > 0 else 0.0
        coverage_variance = np.var(non_zero_cells) if len(non_zero_cells) > 0 else 0.0

        # 计算覆盖的均匀性
        uniformity = self._calculate_coverage_uniformity(grid)

        # 分析覆盖的空间分布
        spatial_distribution = self._analyze_spatial_distribution(grid)

        return {
            'coverage_ratio': coverage_ratio,
            'covered_cells': int(covered_cells),
            'total_cells': total_cells,
            'coverage_density': coverage_density,
            'coverage_variance': coverage_variance,
            'coverage_uniformity': uniformity,
            'spatial_concentration': spatial_distribution['concentration'],
            'spatial_spread': spatial_distribution['spread']
        }

    def _build_coverage_grid(self, populations: List[List[int]]) -> np.ndarray:
        """构建覆盖网格"""
        grid = np.zeros((self.grid_size, self.grid_size), dtype=int)

        for path in populations:
            x, y = self._path_to_grid_coords(path)
            grid[x, y] += 1

        return grid

    def _path_to_grid_coords(self, path: List[int]) -> Tuple[int, int]:
        """将路径转换为网格坐标"""
        # 使用路径的哈希值映射到网格坐标
        path_hash = hash(tuple(path))
        x = abs(path_hash) % self.grid_size
        y = abs(path_hash // self.grid_size) % self.grid_size
        return x, y

    def _calculate_coverage_uniformity(self, grid: np.ndarray) -> float:
        """计算覆盖均匀性"""
        non_zero_cells = grid[grid > 0]
        if len(non_zero_cells) == 0:
            return 0.0

        # 使用变异系数衡量均匀性
        mean_density = np.mean(non_zero_cells)
        std_density = np.std(non_zero_cells)

        if mean_density == 0:
            return 0.0

        # 均匀性 = 1 - 变异系数
        coefficient_of_variation = std_density / mean_density
        uniformity = 1.0 / (1.0 + coefficient_of_variation)

        return uniformity

    def _analyze_spatial_distribution(self, grid: np.ndarray) -> Dict[str, float]:
        """分析空间分布特征"""
        # 计算质心
        y_indices, x_indices = np.nonzero(grid)
        if len(x_indices) == 0:
            return {'concentration': 0.0, 'spread': 0.0}

        weights = grid[y_indices, x_indices]
        centroid_x = np.average(x_indices, weights=weights)
        centroid_y = np.average(y_indices, weights=weights)

        # 计算集中度（到质心的加权平均距离）
        distances = np.sqrt((x_indices - centroid_x)**2 + (y_indices - centroid_y)**2)
        concentration = 1.0 / (1.0 + np.average(distances, weights=weights))

        # 计算扩散度（覆盖区域的标准差）
        spread_x = np.sqrt(np.average((x_indices - centroid_x)**2, weights=weights))
        spread_y = np.sqrt(np.average((y_indices - centroid_y)**2, weights=weights))
        spread = (spread_x + spread_y) / 2.0

        return {'concentration': concentration, 'spread': spread}


class ConvergenceAnalyzer:
    """收敛趋势分析器"""

    def __init__(self, window_size: int = 10):
        """
        初始化收敛分析器

        参数:
            window_size: 滑动窗口大小
        """
        self.window_size = window_size
        self.logger = logging.getLogger(__name__)

    def analyze(self, fitness_history: List[List[float]],
                current_iteration: int) -> Dict[str, float]:
        """
        分析收敛趋势

        参数:
            fitness_history: 历史适应度数据
            current_iteration: 当前迭代次数

        返回:
            收敛趋势分析结果
        """
        if not fitness_history or len(fitness_history) < 2:
            return {'convergence_rate': 0.0, 'convergence_trend': 0.0}

        # 提取最佳适应度序列
        best_fitness_history = [min(generation) for generation in fitness_history]

        # 计算收敛率
        convergence_rate = self._calculate_convergence_rate(best_fitness_history)

        # 计算收敛趋势
        convergence_trend = self._calculate_convergence_trend(best_fitness_history)

        # 预测收敛时间
        predicted_convergence = self._predict_convergence_time(
            best_fitness_history, current_iteration
        )

        # 计算收敛稳定性
        stability = self._calculate_convergence_stability(best_fitness_history)

        return {
            'convergence_rate': convergence_rate,
            'convergence_trend': convergence_trend,
            'predicted_convergence_time': predicted_convergence,
            'convergence_stability': stability,
            'stagnation_detection': self._detect_stagnation(best_fitness_history)
        }

    def _calculate_convergence_rate(self, fitness_history: List[float]) -> float:
        """计算收敛率"""
        if len(fitness_history) < 2:
            return 0.0

        # 使用最近几代的改进率
        recent_window = min(self.window_size, len(fitness_history))
        recent_fitness = fitness_history[-recent_window:]

        if len(recent_fitness) < 2:
            return 0.0

        # 计算平均改进率
        improvements = []
        for i in range(1, len(recent_fitness)):
            if recent_fitness[i-1] > 0:
                improvement = (recent_fitness[i-1] - recent_fitness[i]) / recent_fitness[i-1]
                improvements.append(improvement)

        return np.mean(improvements) if improvements else 0.0

    def _calculate_convergence_trend(self, fitness_history: List[float]) -> float:
        """计算收敛趋势（斜率）"""
        if len(fitness_history) < 3:
            return 0.0

        # 使用最小二乘法计算趋势线斜率
        n = len(fitness_history)
        x = np.arange(n)
        y = np.array(fitness_history)

        # 计算斜率
        x_mean = x.mean()
        y_mean = y.mean()
        numerator = np.sum((x - x_mean) * (y - y_mean))
        denominator = np.sum((x - x_mean) ** 2)

        if denominator == 0:
            return 0.0

        slope = numerator / denominator
        return slope

    def _predict_convergence_time(self, fitness_history: List[float],
                                current_iteration: int) -> float:
        """预测收敛时间"""
        if len(fitness_history) < 3:
            return float('inf')

        # 使用指数拟合预测收敛
        try:
            x = np.arange(len(fitness_history))
            y = np.array(fitness_history)

            # 简单的线性外推
            if len(fitness_history) >= 2:
                recent_rate = (fitness_history[-1] - fitness_history[-2])
                if abs(recent_rate) < 1e-10:  # 已经收敛
                    return current_iteration

                # 预测到达某个阈值的时间
                target_improvement = fitness_history[-1] * 0.01  # 1%改进
                if recent_rate < 0:
                    predicted_iterations = target_improvement / abs(recent_rate)
                    return current_iteration + predicted_iterations

            return float('inf')
        except:
            return float('inf')

    def _calculate_convergence_stability(self, fitness_history: List[float]) -> float:
        """计算收敛稳定性"""
        if len(fitness_history) < self.window_size:
            return 0.0

        # 使用最近窗口的变异系数
        recent_window = fitness_history[-self.window_size:]
        mean_fitness = np.mean(recent_window)
        std_fitness = np.std(recent_window)

        if mean_fitness == 0:
            return 1.0 if std_fitness == 0 else 0.0

        # 稳定性 = 1 - 变异系数
        coefficient_of_variation = std_fitness / abs(mean_fitness)
        stability = 1.0 / (1.0 + coefficient_of_variation)

        return stability

    def _detect_stagnation(self, fitness_history: List[float]) -> bool:
        """检测是否陷入停滞"""
        if len(fitness_history) < self.window_size:
            return False

        # 检查最近窗口内的改进
        recent_window = fitness_history[-self.window_size:]
        max_improvement = max(recent_window) - min(recent_window)

        # 如果改进小于阈值，认为停滞
        stagnation_threshold = abs(recent_window[0]) * 0.001  # 0.1%
        return max_improvement < stagnation_threshold


class DiversityAnalyzer:
    """多样性保持度分析器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def analyze(self, populations: List[List[int]], fitness_values: List[Union[str, int, float]],
                distance_matrix: Optional[np.ndarray] = None) -> Dict[str, float]:
        """
        分析多样性保持度

        参数:
            populations: 种群个体
            fitness_values: 适应度值
            distance_matrix: 距离矩阵

        返回:
            多样性分析结果
        """
        if len(populations) != len(fitness_values):
            raise ValueError("种群大小与适应度值数量不匹配")

        n = len(populations)
        if n < 2:
            return {'diversity_index': 0.0, 'entropy': 0.0}

        # 标准化适应度值
        standardized_fitness = DataTypeUtils.standardize_fitness_values(fitness_values)

        # 计算或使用预计算的距离矩阵
        if distance_matrix is None:
            distance_matrix = self._compute_distance_matrix(populations)

        # 计算多样性指数
        diversity_index = self._calculate_diversity_index(distance_matrix)

        # 计算适应度熵
        fitness_entropy = self._calculate_fitness_entropy(standardized_fitness)

        # 计算基因型多样性
        genotype_diversity = self._calculate_genotype_diversity(populations)

        # 计算多样性丢失率
        diversity_loss = self._calculate_diversity_loss_rate(distance_matrix)

        return {
            'diversity_index': diversity_index,
            'fitness_entropy': fitness_entropy,
            'genotype_diversity': genotype_diversity,
            'diversity_loss_rate': diversity_loss,
            'average_distance': np.mean(distance_matrix[np.triu_indices(n, k=1)]),
            'distance_variance': np.var(distance_matrix[np.triu_indices(n, k=1)])
        }

    def _compute_distance_matrix(self, populations: List[List[int]]) -> np.ndarray:
        """计算距离矩阵"""
        n = len(populations)
        distance_matrix = np.zeros((n, n))

        for i in range(n):
            for j in range(i + 1, n):
                dist = self._hamming_distance(populations[i], populations[j])
                distance_matrix[i, j] = dist
                distance_matrix[j, i] = dist

        return distance_matrix

    def _calculate_diversity_index(self, distance_matrix: np.ndarray) -> float:
        """计算多样性指数"""
        n = distance_matrix.shape[0]
        if n < 2:
            return 0.0

        # 使用平均距离作为多样性指数
        upper_triangle = distance_matrix[np.triu_indices(n, k=1)]
        if len(upper_triangle) == 0:
            return 0.0

        # 归一化到[0,1]
        max_possible_distance = max(distance_matrix.shape)  # 假设最大距离
        diversity_index = np.mean(upper_triangle) / max_possible_distance

        return min(diversity_index, 1.0)

    def _calculate_fitness_entropy(self, fitness_values: List[float]) -> float:
        """计算适应度熵"""
        if not fitness_values:
            return 0.0

        # 将适应度值分箱
        hist, _ = np.histogram(fitness_values, bins=min(10, len(fitness_values)))
        hist = hist[hist > 0]  # 移除空箱

        if len(hist) <= 1:
            return 0.0

        # 计算概率分布
        probs = hist / np.sum(hist)

        # 计算熵
        entropy = -np.sum(probs * np.log2(probs))

        # 归一化
        max_entropy = np.log2(len(hist))
        return entropy / max_entropy if max_entropy > 0 else 0.0

    def _calculate_genotype_diversity(self, populations: List[List[int]]) -> float:
        """计算基因型多样性"""
        if len(populations) < 2:
            return 0.0

        # 计算唯一个体的比例
        unique_genotypes = set()
        for individual in populations:
            unique_genotypes.add(tuple(individual))

        genotype_diversity = len(unique_genotypes) / len(populations)
        return genotype_diversity

    def _calculate_diversity_loss_rate(self, distance_matrix: np.ndarray) -> float:
        """计算多样性丢失率（简化版本）"""
        n = distance_matrix.shape[0]
        if n < 2:
            return 0.0

        # 计算最小距离的分布
        min_distances = []
        for i in range(n):
            row = distance_matrix[i]
            non_zero_distances = row[row > 0]
            if len(non_zero_distances) > 0:
                min_distances.append(np.min(non_zero_distances))

        if not min_distances:
            return 0.0

        # 如果最小距离很小，说明多样性在丢失
        avg_min_distance = np.mean(min_distances)
        max_distance = np.max(distance_matrix)

        if max_distance == 0:
            return 1.0  # 完全丢失多样性

        # 多样性丢失率 = 1 - (平均最小距离 / 最大距离)
        diversity_loss_rate = 1.0 - (avg_min_distance / max_distance)
        return max(0.0, min(1.0, diversity_loss_rate))

    @staticmethod
    @jit(nopython=True)
    def _hamming_distance(path1: List[int], path2: List[int]) -> int:
        """计算汉明距离"""
        if len(path1) != len(path2):
            return max(len(path1), len(path2))

        distance = 0
        for i in range(len(path1)):
            if path1[i] != path2[i]:
                distance += 1
        return distance


class FitnessLandscapeAnalyzer:
    """适应度景观分析器主类，整合所有分析器"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化适应度景观分析器

        参数:
            config: 配置参数
        """
        self.config = config or {}

        # 初始化各个分析器
        self.local_optima_analyzer = LocalOptimaAnalyzer(
            k_neighbors=self.config.get('k_neighbors', 5)
        )
        self.gradient_analyzer = GradientAnalyzer()
        self.clustering_analyzer = ClusteringAnalyzer(
            eps=self.config.get('clustering_eps', 0.5),
            min_samples=self.config.get('clustering_min_samples', 3)
        )
        self.coverage_analyzer = CoverageAnalyzer(
            grid_size=self.config.get('grid_size', 100)
        )
        self.convergence_analyzer = ConvergenceAnalyzer(
            window_size=self.config.get('window_size', 10)
        )
        self.diversity_analyzer = DiversityAnalyzer()

        # 初始化增量更新器
        self.incremental_updater = IncrementalUpdater(
            window_size=self.config.get('window_size', 10),
            cache_size=self.config.get('cache_size', 1000)
        )

        self.logger = logging.getLogger(__name__)

    def analyze_landscape(self, populations: List[List[int]],
                         fitness_values: List[Union[str, int, float]],
                         fitness_history: Optional[List[List[Union[str, int, float]]]] = None,
                         current_iteration: int = 0) -> Dict[str, Any]:
        """
        执行完整的适应度景观分析

        参数:
            populations: 当前种群
            fitness_values: 适应度值
            fitness_history: 历史适应度数据
            current_iteration: 当前迭代次数

        返回:
            完整的景观分析结果
        """
        start_time = time.time()

        try:
            # 标准化适应度值
            standardized_fitness = DataTypeUtils.standardize_fitness_values(fitness_values)

            # 更新增量计算器
            self.incremental_updater.update_fitness_statistics(standardized_fitness)
            diversity_metrics = self.incremental_updater.update_diversity_metrics(
                populations, standardized_fitness
            )
            neighbor_graph = self.incremental_updater.update_neighbor_graph(populations)
            coverage_metrics = self.incremental_updater.update_grid_coverage(populations)

            # 执行各项分析
            results = {}

            # 1. 局部最优密度分析
            results['local_optima'] = self.local_optima_analyzer.analyze(
                populations, standardized_fitness, neighbor_graph
            )

            # 2. 适应度梯度分析
            results['gradient'] = self.gradient_analyzer.analyze(
                populations, standardized_fitness, neighbor_graph
            )

            # 3. 解聚类程度分析
            distance_matrix = neighbor_graph.get('distance_matrix')
            results['clustering'] = self.clustering_analyzer.analyze(
                populations, standardized_fitness, distance_matrix
            )

            # 4. 搜索空间覆盖率分析
            results['coverage'] = coverage_metrics

            # 5. 收敛趋势分析
            if fitness_history:
                # 标准化历史适应度数据
                standardized_history = []
                for hist_gen in fitness_history:
                    standardized_history.append(DataTypeUtils.standardize_fitness_values(hist_gen))
                results['convergence'] = self.convergence_analyzer.analyze(
                    standardized_history, current_iteration
                )
            else:
                results['convergence'] = {'convergence_rate': 0.0, 'convergence_trend': 0.0}

            # 6. 多样性保持度分析
            results['diversity'] = self.diversity_analyzer.analyze(
                populations, standardized_fitness, distance_matrix
            )

            # 添加综合指标
            results['summary'] = self._calculate_summary_metrics(results)

            # 添加性能信息
            results['performance'] = {
                'analysis_time': time.time() - start_time,
                'population_size': len(populations),
                'iteration': current_iteration
            }

            return results

        except Exception as e:
            self.logger.error(f"景观分析失败: {e}")
            return self._get_default_results()

    def _calculate_summary_metrics(self, results: Dict[str, Any]) -> Dict[str, float]:
        """计算综合指标"""
        try:
            # 景观复杂度：结合局部最优密度和聚类数量
            local_optima_density = results['local_optima'].get('local_optima_density', 0.0)
            n_clusters = results['clustering'].get('n_clusters', 0)
            landscape_complexity = (local_optima_density + n_clusters / 10.0) / 2.0

            # 搜索效率：结合覆盖率和多样性
            coverage_ratio = results['coverage'].get('coverage_ratio', 0.0)
            diversity_index = results['diversity'].get('diversity_index', 0.0)
            search_efficiency = (coverage_ratio + diversity_index) / 2.0

            # 收敛质量：结合收敛率和稳定性
            convergence_rate = abs(results['convergence'].get('convergence_rate', 0.0))
            convergence_stability = results['convergence'].get('convergence_stability', 0.0)
            convergence_quality = (convergence_rate + convergence_stability) / 2.0

            return {
                'landscape_complexity': landscape_complexity,
                'search_efficiency': search_efficiency,
                'convergence_quality': convergence_quality,
                'overall_score': (landscape_complexity + search_efficiency + convergence_quality) / 3.0
            }
        except Exception as e:
            self.logger.warning(f"计算综合指标失败: {e}")
            return {
                'landscape_complexity': 0.0,
                'search_efficiency': 0.0,
                'convergence_quality': 0.0,
                'overall_score': 0.0
            }

    def _get_default_results(self) -> Dict[str, Any]:
        """获取默认结果（错误情况下）"""
        return {
            'local_optima': {'local_optima_density': 0.0, 'local_optima_count': 0},
            'gradient': {'gradient_mean': 0.0, 'gradient_variance': 0.0},
            'clustering': {'clustering_coefficient': 0.0, 'n_clusters': 0},
            'coverage': {'coverage_ratio': 0.0, 'covered_cells': 0},
            'convergence': {'convergence_rate': 0.0, 'convergence_trend': 0.0},
            'diversity': {'diversity_index': 0.0, 'entropy': 0.0},
            'summary': {
                'landscape_complexity': 0.0,
                'search_efficiency': 0.0,
                'convergence_quality': 0.0,
                'overall_score': 0.0
            },
            'performance': {'analysis_time': 0.0, 'population_size': 0, 'iteration': 0}
        }

    def reset(self):
        """重置分析器状态"""
        self.incremental_updater.reset()
        self.logger.info("适应度景观分析器已重置")