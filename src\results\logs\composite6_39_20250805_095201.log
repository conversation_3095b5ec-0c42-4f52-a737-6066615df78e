2025-08-05 09:52:01,644 - __main__ - INFO - composite6_39 开始进化第 1 代
2025-08-05 09:52:01,645 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 09:52:01,646 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:01,648 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=24018.000, 多样性=0.973
2025-08-05 09:52:01,650 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:01,653 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.973
2025-08-05 09:52:01,654 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:01,656 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 09:52:01,657 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:01,657 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 09:52:01,657 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 09:52:01,670 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.200, 适应度梯度: -9986.080, 聚类评分: 0.000, 覆盖率: 0.107, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:01,670 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 09:52:01,670 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 09:52:01,670 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite6_39
2025-08-05 09:52:01,676 - visualization.landscape_visualizer - INFO - 插值约束: 22 个点被约束到最小值 24018.00
2025-08-05 09:52:01,774 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite6_39\landscape_composite6_39_iter_86_20250805_095201.html
2025-08-05 09:52:01,837 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite6_39\dashboard_composite6_39_iter_86_20250805_095201.html
2025-08-05 09:52:01,838 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 86
2025-08-05 09:52:01,838 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 09:52:01,838 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1817秒
2025-08-05 09:52:01,838 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 172, 'max_size': 500, 'hits': 0, 'misses': 172, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 561, 'misses': 310, 'hit_rate': 0.6440872560275546, 'evictions': 210, 'ttl': 7200}}
2025-08-05 09:52:01,839 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -9986.079999999998, 'local_optima_density': 0.2, 'gradient_variance': 5533647509.0896, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1071, 'fitness_entropy': 0.9372305632161296, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -9986.080)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.107)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358721.6700068, 'performance_metrics': {}}}
2025-08-05 09:52:01,839 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:01,839 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:01,839 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:01,839 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:01,840 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:52:01,840 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 09:52:01,841 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:52:01,841 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:01,841 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:01,841 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:52:01,841 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:01,841 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:01,841 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:01,841 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:01,841 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:01,841 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:01,843 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 39
2025-08-05 09:52:01,843 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,843 - ExplorationExpert - INFO - 探索路径生成完成，成本: 132350.0, 路径长度: 39, 收集中间解: 0
2025-08-05 09:52:01,844 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [2, 21, 3, 37, 5, 30, 38, 15, 23, 28, 29, 26, 18, 19, 24, 22, 0, 31, 16, 11, 6, 32, 27, 12, 1, 4, 8, 13, 35, 36, 9, 20, 10, 17, 25, 7, 34, 14, 33], 'cur_cost': 132350.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:01,844 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 132350.00)
2025-08-05 09:52:01,844 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:01,844 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:01,844 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:01,849 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 39
2025-08-05 09:52:01,849 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,850 - ExplorationExpert - INFO - 探索路径生成完成，成本: 108477.0, 路径长度: 39, 收集中间解: 0
2025-08-05 09:52:01,850 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [24, 4, 8, 25, 7, 5, 11, 35, 22, 1, 27, 26, 31, 2, 3, 28, 17, 34, 21, 37, 38, 23, 33, 18, 19, 36, 12, 32, 6, 10, 20, 9, 0, 13, 30, 29, 16, 15, 14], 'cur_cost': 108477.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:01,850 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 108477.00)
2025-08-05 09:52:01,851 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:01,851 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:01,851 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:01,857 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 39
2025-08-05 09:52:01,857 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,858 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101098.0, 路径长度: 39, 收集中间解: 0
2025-08-05 09:52:01,858 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [8, 25, 23, 2, 3, 4, 5, 11, 34, 32, 24, 21, 6, 10, 33, 7, 36, 22, 37, 38, 19, 15, 17, 0, 27, 20, 31, 9, 14, 26, 29, 28, 1, 30, 16, 35, 12, 13, 18], 'cur_cost': 101098.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:01,858 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 101098.00)
2025-08-05 09:52:01,858 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:52:01,858 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:52:01,858 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:01,861 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 39
2025-08-05 09:52:01,861 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,861 - ExplorationExpert - INFO - 探索路径生成完成，成本: 148553.0, 路径长度: 39, 收集中间解: 0
2025-08-05 09:52:01,861 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [8, 21, 23, 2, 5, 4, 15, 32, 29, 18, 10, 24, 7, 0, 31, 16, 11, 6, 33, 12, 1, 13, 35, 26, 20, 28, 30, 25, 37, 38, 19, 9, 22, 27, 36, 34, 14, 3, 17], 'cur_cost': 148553.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:01,862 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 148553.00)
2025-08-05 09:52:01,862 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:01,862 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:01,862 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:01,867 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 39
2025-08-05 09:52:01,867 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,868 - ExplorationExpert - INFO - 探索路径生成完成，成本: 89445.0, 路径长度: 39, 收集中间解: 0
2025-08-05 09:52:01,868 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [10, 7, 23, 34, 35, 8, 6, 38, 36, 18, 21, 1, 25, 3, 0, 4, 28, 22, 24, 33, 9, 11, 5, 14, 13, 37, 12, 2, 29, 27, 30, 17, 15, 20, 32, 16, 19, 31, 26], 'cur_cost': 89445.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:01,868 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 89445.00)
2025-08-05 09:52:01,868 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 09:52:01,868 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:01,868 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:01,869 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 115219.0
2025-08-05 09:52:01,882 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 09:52:01,883 - ExploitationExpert - INFO - res_population_costs: [23771.0, 23771]
2025-08-05 09:52:01,883 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 24, 23, 22,
       21, 25, 20, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       10, 14, 13,  2,  1], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 20, 25, 21, 22, 23, 24, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64)]
2025-08-05 09:52:01,884 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:01,884 - ExploitationExpert - INFO - populations: [{'tour': [2, 21, 3, 37, 5, 30, 38, 15, 23, 28, 29, 26, 18, 19, 24, 22, 0, 31, 16, 11, 6, 32, 27, 12, 1, 4, 8, 13, 35, 36, 9, 20, 10, 17, 25, 7, 34, 14, 33], 'cur_cost': 132350.0}, {'tour': [24, 4, 8, 25, 7, 5, 11, 35, 22, 1, 27, 26, 31, 2, 3, 28, 17, 34, 21, 37, 38, 23, 33, 18, 19, 36, 12, 32, 6, 10, 20, 9, 0, 13, 30, 29, 16, 15, 14], 'cur_cost': 108477.0}, {'tour': [8, 25, 23, 2, 3, 4, 5, 11, 34, 32, 24, 21, 6, 10, 33, 7, 36, 22, 37, 38, 19, 15, 17, 0, 27, 20, 31, 9, 14, 26, 29, 28, 1, 30, 16, 35, 12, 13, 18], 'cur_cost': 101098.0}, {'tour': [8, 21, 23, 2, 5, 4, 15, 32, 29, 18, 10, 24, 7, 0, 31, 16, 11, 6, 33, 12, 1, 13, 35, 26, 20, 28, 30, 25, 37, 38, 19, 9, 22, 27, 36, 34, 14, 3, 17], 'cur_cost': 148553.0}, {'tour': [10, 7, 23, 34, 35, 8, 6, 38, 36, 18, 21, 1, 25, 3, 0, 4, 28, 22, 24, 33, 9, 11, 5, 14, 13, 37, 12, 2, 29, 27, 30, 17, 15, 20, 32, 16, 19, 31, 26], 'cur_cost': 89445.0}, {'tour': array([ 9, 25, 26,  0, 19,  7, 22, 34,  6, 36,  8, 35,  2, 28, 30, 18, 17,
       21, 15, 13, 37,  5, 33, 32, 20, 16, 23,  4, 27, 29, 24, 12, 38, 14,
        1,  3, 31, 10, 11], dtype=int64), 'cur_cost': 115219.0}, {'tour': array([ 6, 26,  0,  3,  8, 30, 22, 38, 34, 12,  7, 21, 17, 35, 16,  1, 28,
       32, 20, 14, 31,  5, 18, 27, 29, 23, 19, 33,  9,  2, 11, 37, 36, 24,
       13, 15, 25, 10,  4], dtype=int64), 'cur_cost': 140105.0}, {'tour': array([ 7, 32,  4, 18, 31, 33, 11, 24, 22, 14, 25,  1, 30, 19, 21, 12, 26,
       10, 37, 27, 35,  5,  8, 38, 28,  3,  9,  0,  6, 23, 15, 16, 36, 17,
       20,  2, 13, 34, 29], dtype=int64), 'cur_cost': 153533.0}, {'tour': array([31, 30, 14,  6, 37, 17, 38,  4, 29, 22,  1, 27, 32,  0, 20, 10,  2,
       16, 13,  3, 33, 11, 21, 19, 18,  9, 28,  5, 12, 35, 15, 25, 34, 23,
        7, 36, 24, 26,  8], dtype=int64), 'cur_cost': 149074.0}, {'tour': array([24, 17, 35, 14, 21,  7,  0, 33, 30,  1, 38, 18, 13, 36, 28, 32, 27,
       11, 26, 37, 31, 16, 15, 10,  8,  5, 20,  4, 29, 12, 19, 25, 34,  9,
        6,  2, 22,  3, 23], dtype=int64), 'cur_cost': 158140.0}]
2025-08-05 09:52:01,886 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:01,886 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 222, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 222, 'cache_hits': 0, 'similarity_calculations': 1044, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:01,887 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([ 9, 25, 26,  0, 19,  7, 22, 34,  6, 36,  8, 35,  2, 28, 30, 18, 17,
       21, 15, 13, 37,  5, 33, 32, 20, 16, 23,  4, 27, 29, 24, 12, 38, 14,
        1,  3, 31, 10, 11], dtype=int64), 'cur_cost': 115219.0, 'intermediate_solutions': [{'tour': array([ 6, 26, 18, 25, 19, 23,  0, 17, 27, 37, 32,  7, 14,  1, 22, 31, 29,
       36,  3, 12, 11, 33, 24, 35, 30,  5, 34, 20,  8, 16, 13, 21, 15, 10,
       28, 38,  2,  9,  4], dtype=int64), 'cur_cost': 154956.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([25,  6, 26, 18, 19, 23,  0, 17, 27, 37, 32,  7, 14,  1, 22, 31, 29,
       36,  3, 12, 11, 33, 24, 35, 30,  5, 34, 20,  8, 16, 13, 21, 15, 10,
       28, 38,  2,  9,  4], dtype=int64), 'cur_cost': 150649.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([19, 25,  6, 26, 18, 23,  0, 17, 27, 37, 32,  7, 14,  1, 22, 31, 29,
       36,  3, 12, 11, 33, 24, 35, 30,  5, 34, 20,  8, 16, 13, 21, 15, 10,
       28, 38,  2,  9,  4], dtype=int64), 'cur_cost': 155858.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([18, 25,  6, 26, 19, 23,  0, 17, 27, 37, 32,  7, 14,  1, 22, 31, 29,
       36,  3, 12, 11, 33, 24, 35, 30,  5, 34, 20,  8, 16, 13, 21, 15, 10,
       28, 38,  2,  9,  4], dtype=int64), 'cur_cost': 155858.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([18, 19, 25,  6, 26, 23,  0, 17, 27, 37, 32,  7, 14,  1, 22, 31, 29,
       36,  3, 12, 11, 33, 24, 35, 30,  5, 34, 20,  8, 16, 13, 21, 15, 10,
       28, 38,  2,  9,  4], dtype=int64), 'cur_cost': 151708.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:01,887 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 115219.00)
2025-08-05 09:52:01,888 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:01,888 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:01,888 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:01,889 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 39
2025-08-05 09:52:01,889 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,889 - ExplorationExpert - INFO - 探索路径生成完成，成本: 125043.0, 路径长度: 39, 收集中间解: 0
2025-08-05 09:52:01,890 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [10, 7, 8, 25, 3, 5, 6, 11, 36, 28, 29, 18, 2, 33, 22, 0, 31, 21, 38, 19, 32, 27, 12, 1, 4, 30, 13, 14, 26, 9, 20, 15, 16, 34, 37, 17, 35, 24, 23], 'cur_cost': 125043.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:01,890 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 125043.00)
2025-08-05 09:52:01,890 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:01,890 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:01,890 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:01,891 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 39
2025-08-05 09:52:01,892 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,892 - ExplorationExpert - INFO - 探索路径生成完成，成本: 34103.0, 路径长度: 39, 收集中间解: 0
2025-08-05 09:52:01,892 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 10, 12, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29, 20, 25, 21, 22, 23, 24, 6, 8, 9, 5, 7, 11, 14, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19], 'cur_cost': 34103.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:01,892 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 34103.00)
2025-08-05 09:52:01,892 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:52:01,892 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:52:01,893 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:01,894 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 39
2025-08-05 09:52:01,894 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,894 - ExplorationExpert - INFO - 探索路径生成完成，成本: 36347.0, 路径长度: 39, 收集中间解: 0
2025-08-05 09:52:01,894 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 21, 14, 15, 16, 17, 18, 19, 22, 23, 24, 25, 20, 6, 8, 9, 5, 7, 12, 11, 10, 13, 32, 35, 38, 36, 33, 34, 37, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 36347.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:01,894 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 36347.00)
2025-08-05 09:52:01,894 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 09:52:01,894 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:01,895 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:01,895 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 145177.0
2025-08-05 09:52:01,905 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 09:52:01,905 - ExploitationExpert - INFO - res_population_costs: [23771.0, 23771, 23771.0, 23763, 23763]
2025-08-05 09:52:01,905 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 24, 23, 22,
       21, 25, 20, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       10, 14, 13,  2,  1], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 20, 25, 21, 22, 23, 24, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 20, 25, 21, 22, 23, 24, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  1,  2, 13, 10, 14, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64)]
2025-08-05 09:52:01,907 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:01,907 - ExploitationExpert - INFO - populations: [{'tour': [2, 21, 3, 37, 5, 30, 38, 15, 23, 28, 29, 26, 18, 19, 24, 22, 0, 31, 16, 11, 6, 32, 27, 12, 1, 4, 8, 13, 35, 36, 9, 20, 10, 17, 25, 7, 34, 14, 33], 'cur_cost': 132350.0}, {'tour': [24, 4, 8, 25, 7, 5, 11, 35, 22, 1, 27, 26, 31, 2, 3, 28, 17, 34, 21, 37, 38, 23, 33, 18, 19, 36, 12, 32, 6, 10, 20, 9, 0, 13, 30, 29, 16, 15, 14], 'cur_cost': 108477.0}, {'tour': [8, 25, 23, 2, 3, 4, 5, 11, 34, 32, 24, 21, 6, 10, 33, 7, 36, 22, 37, 38, 19, 15, 17, 0, 27, 20, 31, 9, 14, 26, 29, 28, 1, 30, 16, 35, 12, 13, 18], 'cur_cost': 101098.0}, {'tour': [8, 21, 23, 2, 5, 4, 15, 32, 29, 18, 10, 24, 7, 0, 31, 16, 11, 6, 33, 12, 1, 13, 35, 26, 20, 28, 30, 25, 37, 38, 19, 9, 22, 27, 36, 34, 14, 3, 17], 'cur_cost': 148553.0}, {'tour': [10, 7, 23, 34, 35, 8, 6, 38, 36, 18, 21, 1, 25, 3, 0, 4, 28, 22, 24, 33, 9, 11, 5, 14, 13, 37, 12, 2, 29, 27, 30, 17, 15, 20, 32, 16, 19, 31, 26], 'cur_cost': 89445.0}, {'tour': array([ 9, 25, 26,  0, 19,  7, 22, 34,  6, 36,  8, 35,  2, 28, 30, 18, 17,
       21, 15, 13, 37,  5, 33, 32, 20, 16, 23,  4, 27, 29, 24, 12, 38, 14,
        1,  3, 31, 10, 11], dtype=int64), 'cur_cost': 115219.0}, {'tour': [10, 7, 8, 25, 3, 5, 6, 11, 36, 28, 29, 18, 2, 33, 22, 0, 31, 21, 38, 19, 32, 27, 12, 1, 4, 30, 13, 14, 26, 9, 20, 15, 16, 34, 37, 17, 35, 24, 23], 'cur_cost': 125043.0}, {'tour': [0, 10, 12, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29, 20, 25, 21, 22, 23, 24, 6, 8, 9, 5, 7, 11, 14, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19], 'cur_cost': 34103.0}, {'tour': [0, 21, 14, 15, 16, 17, 18, 19, 22, 23, 24, 25, 20, 6, 8, 9, 5, 7, 12, 11, 10, 13, 32, 35, 38, 36, 33, 34, 37, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 36347.0}, {'tour': array([ 8, 11, 21, 25,  1, 32, 35,  5, 12, 20, 27, 29, 26, 34, 22,  0,  6,
       36, 18,  2,  4,  7, 10, 19, 30, 15, 24, 38,  3,  9, 17, 31, 13, 16,
       14, 28, 33, 23, 37], dtype=int64), 'cur_cost': 145177.0}]
2025-08-05 09:52:01,908 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:01,908 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 223, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 223, 'cache_hits': 0, 'similarity_calculations': 1045, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:01,909 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([ 8, 11, 21, 25,  1, 32, 35,  5, 12, 20, 27, 29, 26, 34, 22,  0,  6,
       36, 18,  2,  4,  7, 10, 19, 30, 15, 24, 38,  3,  9, 17, 31, 13, 16,
       14, 28, 33, 23, 37], dtype=int64), 'cur_cost': 145177.0, 'intermediate_solutions': [{'tour': array([35, 17, 24, 14, 21,  7,  0, 33, 30,  1, 38, 18, 13, 36, 28, 32, 27,
       11, 26, 37, 31, 16, 15, 10,  8,  5, 20,  4, 29, 12, 19, 25, 34,  9,
        6,  2, 22,  3, 23], dtype=int64), 'cur_cost': 161093.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([14, 35, 17, 24, 21,  7,  0, 33, 30,  1, 38, 18, 13, 36, 28, 32, 27,
       11, 26, 37, 31, 16, 15, 10,  8,  5, 20,  4, 29, 12, 19, 25, 34,  9,
        6,  2, 22,  3, 23], dtype=int64), 'cur_cost': 158162.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([21, 14, 35, 17, 24,  7,  0, 33, 30,  1, 38, 18, 13, 36, 28, 32, 27,
       11, 26, 37, 31, 16, 15, 10,  8,  5, 20,  4, 29, 12, 19, 25, 34,  9,
        6,  2, 22,  3, 23], dtype=int64), 'cur_cost': 158119.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([24, 14, 35, 17, 21,  7,  0, 33, 30,  1, 38, 18, 13, 36, 28, 32, 27,
       11, 26, 37, 31, 16, 15, 10,  8,  5, 20,  4, 29, 12, 19, 25, 34,  9,
        6,  2, 22,  3, 23], dtype=int64), 'cur_cost': 158103.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([24, 21, 14, 35, 17,  7,  0, 33, 30,  1, 38, 18, 13, 36, 28, 32, 27,
       11, 26, 37, 31, 16, 15, 10,  8,  5, 20,  4, 29, 12, 19, 25, 34,  9,
        6,  2, 22,  3, 23], dtype=int64), 'cur_cost': 158114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:01,909 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 145177.00)
2025-08-05 09:52:01,909 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:01,910 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:01,911 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [2, 21, 3, 37, 5, 30, 38, 15, 23, 28, 29, 26, 18, 19, 24, 22, 0, 31, 16, 11, 6, 32, 27, 12, 1, 4, 8, 13, 35, 36, 9, 20, 10, 17, 25, 7, 34, 14, 33], 'cur_cost': 132350.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [24, 4, 8, 25, 7, 5, 11, 35, 22, 1, 27, 26, 31, 2, 3, 28, 17, 34, 21, 37, 38, 23, 33, 18, 19, 36, 12, 32, 6, 10, 20, 9, 0, 13, 30, 29, 16, 15, 14], 'cur_cost': 108477.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [8, 25, 23, 2, 3, 4, 5, 11, 34, 32, 24, 21, 6, 10, 33, 7, 36, 22, 37, 38, 19, 15, 17, 0, 27, 20, 31, 9, 14, 26, 29, 28, 1, 30, 16, 35, 12, 13, 18], 'cur_cost': 101098.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [8, 21, 23, 2, 5, 4, 15, 32, 29, 18, 10, 24, 7, 0, 31, 16, 11, 6, 33, 12, 1, 13, 35, 26, 20, 28, 30, 25, 37, 38, 19, 9, 22, 27, 36, 34, 14, 3, 17], 'cur_cost': 148553.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [10, 7, 23, 34, 35, 8, 6, 38, 36, 18, 21, 1, 25, 3, 0, 4, 28, 22, 24, 33, 9, 11, 5, 14, 13, 37, 12, 2, 29, 27, 30, 17, 15, 20, 32, 16, 19, 31, 26], 'cur_cost': 89445.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 9, 25, 26,  0, 19,  7, 22, 34,  6, 36,  8, 35,  2, 28, 30, 18, 17,
       21, 15, 13, 37,  5, 33, 32, 20, 16, 23,  4, 27, 29, 24, 12, 38, 14,
        1,  3, 31, 10, 11], dtype=int64), 'cur_cost': 115219.0, 'intermediate_solutions': [{'tour': array([ 6, 26, 18, 25, 19, 23,  0, 17, 27, 37, 32,  7, 14,  1, 22, 31, 29,
       36,  3, 12, 11, 33, 24, 35, 30,  5, 34, 20,  8, 16, 13, 21, 15, 10,
       28, 38,  2,  9,  4], dtype=int64), 'cur_cost': 154956.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([25,  6, 26, 18, 19, 23,  0, 17, 27, 37, 32,  7, 14,  1, 22, 31, 29,
       36,  3, 12, 11, 33, 24, 35, 30,  5, 34, 20,  8, 16, 13, 21, 15, 10,
       28, 38,  2,  9,  4], dtype=int64), 'cur_cost': 150649.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([19, 25,  6, 26, 18, 23,  0, 17, 27, 37, 32,  7, 14,  1, 22, 31, 29,
       36,  3, 12, 11, 33, 24, 35, 30,  5, 34, 20,  8, 16, 13, 21, 15, 10,
       28, 38,  2,  9,  4], dtype=int64), 'cur_cost': 155858.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([18, 25,  6, 26, 19, 23,  0, 17, 27, 37, 32,  7, 14,  1, 22, 31, 29,
       36,  3, 12, 11, 33, 24, 35, 30,  5, 34, 20,  8, 16, 13, 21, 15, 10,
       28, 38,  2,  9,  4], dtype=int64), 'cur_cost': 155858.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([18, 19, 25,  6, 26, 23,  0, 17, 27, 37, 32,  7, 14,  1, 22, 31, 29,
       36,  3, 12, 11, 33, 24, 35, 30,  5, 34, 20,  8, 16, 13, 21, 15, 10,
       28, 38,  2,  9,  4], dtype=int64), 'cur_cost': 151708.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [10, 7, 8, 25, 3, 5, 6, 11, 36, 28, 29, 18, 2, 33, 22, 0, 31, 21, 38, 19, 32, 27, 12, 1, 4, 30, 13, 14, 26, 9, 20, 15, 16, 34, 37, 17, 35, 24, 23], 'cur_cost': 125043.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 12, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29, 20, 25, 21, 22, 23, 24, 6, 8, 9, 5, 7, 11, 14, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19], 'cur_cost': 34103.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 14, 15, 16, 17, 18, 19, 22, 23, 24, 25, 20, 6, 8, 9, 5, 7, 12, 11, 10, 13, 32, 35, 38, 36, 33, 34, 37, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 36347.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 8, 11, 21, 25,  1, 32, 35,  5, 12, 20, 27, 29, 26, 34, 22,  0,  6,
       36, 18,  2,  4,  7, 10, 19, 30, 15, 24, 38,  3,  9, 17, 31, 13, 16,
       14, 28, 33, 23, 37], dtype=int64), 'cur_cost': 145177.0, 'intermediate_solutions': [{'tour': array([35, 17, 24, 14, 21,  7,  0, 33, 30,  1, 38, 18, 13, 36, 28, 32, 27,
       11, 26, 37, 31, 16, 15, 10,  8,  5, 20,  4, 29, 12, 19, 25, 34,  9,
        6,  2, 22,  3, 23], dtype=int64), 'cur_cost': 161093.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([14, 35, 17, 24, 21,  7,  0, 33, 30,  1, 38, 18, 13, 36, 28, 32, 27,
       11, 26, 37, 31, 16, 15, 10,  8,  5, 20,  4, 29, 12, 19, 25, 34,  9,
        6,  2, 22,  3, 23], dtype=int64), 'cur_cost': 158162.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([21, 14, 35, 17, 24,  7,  0, 33, 30,  1, 38, 18, 13, 36, 28, 32, 27,
       11, 26, 37, 31, 16, 15, 10,  8,  5, 20,  4, 29, 12, 19, 25, 34,  9,
        6,  2, 22,  3, 23], dtype=int64), 'cur_cost': 158119.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([24, 14, 35, 17, 21,  7,  0, 33, 30,  1, 38, 18, 13, 36, 28, 32, 27,
       11, 26, 37, 31, 16, 15, 10,  8,  5, 20,  4, 29, 12, 19, 25, 34,  9,
        6,  2, 22,  3, 23], dtype=int64), 'cur_cost': 158103.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([24, 21, 14, 35, 17,  7,  0, 33, 30,  1, 38, 18, 13, 36, 28, 32, 27,
       11, 26, 37, 31, 16, 15, 10,  8,  5, 20,  4, 29, 12, 19, 25, 34,  9,
        6,  2, 22,  3, 23], dtype=int64), 'cur_cost': 158114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 09:52:01,912 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:01,912 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:01,915 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=34103.000, 多样性=0.968
2025-08-05 09:52:01,915 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 09:52:01,915 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 09:52:01,915 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:01,916 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.09310239505201928, 'best_improvement': -0.4198934132733783}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.00526932084309143}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.054836568940455965, 'recent_improvements': [-0.10695099348534214, 0.07689233514077545, 0.002722144395569812], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 5, 'new_count': 5, 'count_change': 0, 'old_best_cost': 23763, 'new_best_cost': 23763, 'quality_improvement': 0.0, 'old_diversity': 0.7025641025641025, 'new_diversity': 0.7025641025641025, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 09:52:01,916 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 09:52:01,916 - __main__ - INFO - composite6_39 开始进化第 2 代
2025-08-05 09:52:01,917 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 09:52:01,917 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:01,917 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=34103.000, 多样性=0.968
2025-08-05 09:52:01,918 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:01,920 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.968
2025-08-05 09:52:01,920 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:01,921 - EliteExpert - INFO - 精英解分析完成: 精英解数量=5, 多样性=0.703
2025-08-05 09:52:01,923 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 09:52:01,923 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:01,923 - LandscapeExpert - INFO - 添加精英解数据: 5个精英解
2025-08-05 09:52:01,924 - LandscapeExpert - INFO - 数据提取成功: 15个路径, 15个适应度值
2025-08-05 09:52:01,961 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.133, 适应度梯度: 8681.320, 聚类评分: 0.000, 覆盖率: 0.108, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:01,961 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 09:52:01,961 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:01,962 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite6_39
2025-08-05 09:52:01,966 - visualization.landscape_visualizer - INFO - 插值约束: 43 个点被约束到最小值 23763.00
2025-08-05 09:52:02,062 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite6_39\landscape_composite6_39_iter_87_20250805_095202.html
2025-08-05 09:52:02,120 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite6_39\dashboard_composite6_39_iter_87_20250805_095202.html
2025-08-05 09:52:02,120 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 87
2025-08-05 09:52:02,120 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 09:52:02,120 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1976秒
2025-08-05 09:52:02,120 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.13333333333333333, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 8681.32, 'local_optima_density': 0.13333333333333333, 'gradient_variance': 1785189111.6736, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1084, 'fitness_entropy': 0.8808667460926463, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.108)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 8681.320)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358721.9611313, 'performance_metrics': {}}}
2025-08-05 09:52:02,121 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:02,121 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:02,121 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:02,121 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:02,121 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:52:02,121 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 09:52:02,122 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:52:02,122 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:02,122 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:02,122 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:52:02,122 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:02,123 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:02,123 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:02,123 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:02,123 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:02,123 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:02,124 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 39
2025-08-05 09:52:02,125 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,125 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,125 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,125 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,125 - ExplorationExpert - INFO - 探索路径生成完成，成本: 35524.0, 路径长度: 39, 收集中间解: 3
2025-08-05 09:52:02,125 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 13, 1, 16, 17, 15, 19, 18, 22, 23, 24, 21, 25, 20, 6, 8, 9, 5, 7, 12, 11, 10, 14, 32, 35, 38, 36, 33, 34, 37, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 35524.0, 'intermediate_solutions': [{'tour': [2, 21, 3, 37, 5, 30, 38, 15, 23, 28, 29, 26, 18, 19, 24, 22, 0, 31, 16, 11, 6, 32, 27, 12, 1, 4, 8, 13, 35, 36, 9, 25, 10, 17, 20, 7, 34, 14, 33], 'cur_cost': 132458.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 21, 3, 37, 5, 30, 38, 15, 23, 28, 29, 26, 18, 19, 24, 22, 0, 31, 16, 11, 6, 32, 27, 12, 1, 4, 8, 13, 35, 36, 9, 20, 34, 7, 25, 17, 10, 14, 33], 'cur_cost': 126493.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 21, 3, 37, 5, 31, 30, 38, 15, 23, 28, 29, 26, 18, 19, 24, 22, 0, 16, 11, 6, 32, 27, 12, 1, 4, 8, 13, 35, 36, 9, 20, 10, 17, 25, 7, 34, 14, 33], 'cur_cost': 129700.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:02,126 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 35524.00)
2025-08-05 09:52:02,126 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:02,126 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:02,126 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:02,130 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 39
2025-08-05 09:52:02,130 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,131 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,131 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,131 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,132 - ExplorationExpert - INFO - 探索路径生成完成，成本: 86996.0, 路径长度: 39, 收集中间解: 3
2025-08-05 09:52:02,132 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [14, 10, 7, 9, 24, 33, 35, 18, 21, 6, 12, 5, 36, 32, 13, 0, 28, 3, 31, 29, 22, 23, 34, 8, 1, 25, 4, 26, 30, 17, 19, 15, 16, 20, 2, 27, 11, 38, 37], 'cur_cost': 86996.0, 'intermediate_solutions': [{'tour': [24, 4, 8, 25, 7, 5, 11, 35, 22, 1, 27, 26, 31, 2, 3, 28, 17, 34, 21, 20, 38, 23, 33, 18, 19, 36, 12, 32, 6, 10, 37, 9, 0, 13, 30, 29, 16, 15, 14], 'cur_cost': 109164.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [24, 4, 8, 25, 7, 5, 11, 35, 22, 1, 27, 26, 31, 9, 20, 10, 6, 32, 12, 36, 19, 18, 33, 23, 38, 37, 21, 34, 17, 28, 3, 2, 0, 13, 30, 29, 16, 15, 14], 'cur_cost': 108439.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [24, 4, 8, 25, 7, 5, 11, 35, 22, 1, 27, 26, 31, 2, 3, 28, 17, 34, 21, 37, 38, 23, 33, 18, 19, 36, 12, 32, 6, 10, 20, 9, 0, 13, 30, 29, 16, 15, 14], 'cur_cost': 108477.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:02,132 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 86996.00)
2025-08-05 09:52:02,133 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:02,133 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:02,133 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:02,135 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 39
2025-08-05 09:52:02,135 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,135 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,136 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,136 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,136 - ExplorationExpert - INFO - 探索路径生成完成，成本: 132235.0, 路径长度: 39, 收集中间解: 3
2025-08-05 09:52:02,136 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [14, 13, 1, 9, 17, 33, 19, 32, 34, 6, 12, 5, 36, 10, 31, 8, 11, 3, 37, 29, 22, 23, 35, 0, 27, 38, 4, 26, 30, 7, 24, 28, 15, 16, 2, 18, 20, 25, 21], 'cur_cost': 132235.0, 'intermediate_solutions': [{'tour': [8, 25, 23, 2, 3, 4, 5, 11, 34, 32, 24, 21, 6, 10, 33, 7, 22, 36, 37, 38, 19, 15, 17, 0, 27, 20, 31, 9, 14, 26, 29, 28, 1, 30, 16, 35, 12, 13, 18], 'cur_cost': 97628.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 25, 23, 2, 3, 4, 5, 11, 34, 32, 24, 21, 6, 10, 33, 7, 36, 22, 37, 38, 19, 15, 17, 0, 27, 31, 20, 9, 14, 26, 29, 28, 1, 30, 16, 35, 12, 13, 18], 'cur_cost': 93960.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 25, 23, 2, 3, 4, 5, 11, 34, 32, 24, 21, 6, 10, 33, 7, 36, 22, 37, 38, 19, 15, 17, 0, 27, 20, 31, 9, 14, 26, 29, 28, 1, 30, 35, 12, 13, 16, 18], 'cur_cost': 98447.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:02,136 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 132235.00)
2025-08-05 09:52:02,137 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:52:02,137 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:02,137 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:02,137 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 147433.0
2025-08-05 09:52:02,154 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 09:52:02,154 - ExploitationExpert - INFO - res_population_costs: [23763, 23763, 23771.0, 23771, 23771.0, 23763]
2025-08-05 09:52:02,154 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  1,  2, 13, 10, 14, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 24, 23, 22,
       21, 25, 20, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       10, 14, 13,  2,  1], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 20, 25, 21, 22, 23, 24, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 20, 25, 21, 22, 23, 24, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64)]
2025-08-05 09:52:02,157 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:02,157 - ExploitationExpert - INFO - populations: [{'tour': [0, 13, 1, 16, 17, 15, 19, 18, 22, 23, 24, 21, 25, 20, 6, 8, 9, 5, 7, 12, 11, 10, 14, 32, 35, 38, 36, 33, 34, 37, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 35524.0}, {'tour': [14, 10, 7, 9, 24, 33, 35, 18, 21, 6, 12, 5, 36, 32, 13, 0, 28, 3, 31, 29, 22, 23, 34, 8, 1, 25, 4, 26, 30, 17, 19, 15, 16, 20, 2, 27, 11, 38, 37], 'cur_cost': 86996.0}, {'tour': [14, 13, 1, 9, 17, 33, 19, 32, 34, 6, 12, 5, 36, 10, 31, 8, 11, 3, 37, 29, 22, 23, 35, 0, 27, 38, 4, 26, 30, 7, 24, 28, 15, 16, 2, 18, 20, 25, 21], 'cur_cost': 132235.0}, {'tour': array([ 7, 31, 29,  1,  5, 14, 28, 38,  6, 37, 20,  0, 36,  3, 34, 22, 27,
        8, 23, 33, 11, 16, 12, 30, 17, 24, 15, 19,  2,  4, 10, 26, 32, 18,
       35, 13,  9, 21, 25], dtype=int64), 'cur_cost': 147433.0}, {'tour': [10, 7, 23, 34, 35, 8, 6, 38, 36, 18, 21, 1, 25, 3, 0, 4, 28, 22, 24, 33, 9, 11, 5, 14, 13, 37, 12, 2, 29, 27, 30, 17, 15, 20, 32, 16, 19, 31, 26], 'cur_cost': 89445.0}, {'tour': [9, 25, 26, 0, 19, 7, 22, 34, 6, 36, 8, 35, 2, 28, 30, 18, 17, 21, 15, 13, 37, 5, 33, 32, 20, 16, 23, 4, 27, 29, 24, 12, 38, 14, 1, 3, 31, 10, 11], 'cur_cost': 115219.0}, {'tour': [10, 7, 8, 25, 3, 5, 6, 11, 36, 28, 29, 18, 2, 33, 22, 0, 31, 21, 38, 19, 32, 27, 12, 1, 4, 30, 13, 14, 26, 9, 20, 15, 16, 34, 37, 17, 35, 24, 23], 'cur_cost': 125043.0}, {'tour': [0, 10, 12, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29, 20, 25, 21, 22, 23, 24, 6, 8, 9, 5, 7, 11, 14, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19], 'cur_cost': 34103.0}, {'tour': [0, 21, 14, 15, 16, 17, 18, 19, 22, 23, 24, 25, 20, 6, 8, 9, 5, 7, 12, 11, 10, 13, 32, 35, 38, 36, 33, 34, 37, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 36347.0}, {'tour': [8, 11, 21, 25, 1, 32, 35, 5, 12, 20, 27, 29, 26, 34, 22, 0, 6, 36, 18, 2, 4, 7, 10, 19, 30, 15, 24, 38, 3, 9, 17, 31, 13, 16, 14, 28, 33, 23, 37], 'cur_cost': 145177.0}]
2025-08-05 09:52:02,158 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:02,159 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 224, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 224, 'cache_hits': 0, 'similarity_calculations': 1047, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:02,159 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 7, 31, 29,  1,  5, 14, 28, 38,  6, 37, 20,  0, 36,  3, 34, 22, 27,
        8, 23, 33, 11, 16, 12, 30, 17, 24, 15, 19,  2,  4, 10, 26, 32, 18,
       35, 13,  9, 21, 25], dtype=int64), 'cur_cost': 147433.0, 'intermediate_solutions': [{'tour': array([23, 21,  8,  2,  5,  4, 15, 32, 29, 18, 10, 24,  7,  0, 31, 16, 11,
        6, 33, 12,  1, 13, 35, 26, 20, 28, 30, 25, 37, 38, 19,  9, 22, 27,
       36, 34, 14,  3, 17]), 'cur_cost': 146973.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2, 23, 21,  8,  5,  4, 15, 32, 29, 18, 10, 24,  7,  0, 31, 16, 11,
        6, 33, 12,  1, 13, 35, 26, 20, 28, 30, 25, 37, 38, 19,  9, 22, 27,
       36, 34, 14,  3, 17]), 'cur_cost': 145678.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 5,  2, 23, 21,  8,  4, 15, 32, 29, 18, 10, 24,  7,  0, 31, 16, 11,
        6, 33, 12,  1, 13, 35, 26, 20, 28, 30, 25, 37, 38, 19,  9, 22, 27,
       36, 34, 14,  3, 17]), 'cur_cost': 148549.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 8,  2, 23, 21,  5,  4, 15, 32, 29, 18, 10, 24,  7,  0, 31, 16, 11,
        6, 33, 12,  1, 13, 35, 26, 20, 28, 30, 25, 37, 38, 19,  9, 22, 27,
       36, 34, 14,  3, 17]), 'cur_cost': 148551.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 8,  5,  2, 23, 21,  4, 15, 32, 29, 18, 10, 24,  7,  0, 31, 16, 11,
        6, 33, 12,  1, 13, 35, 26, 20, 28, 30, 25, 37, 38, 19,  9, 22, 27,
       36, 34, 14,  3, 17]), 'cur_cost': 146213.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:02,159 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 147433.00)
2025-08-05 09:52:02,161 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:02,161 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:02,161 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:02,163 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 39
2025-08-05 09:52:02,163 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,163 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,164 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,164 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,164 - ExplorationExpert - INFO - 探索路径生成完成，成本: 127529.0, 路径长度: 39, 收集中间解: 3
2025-08-05 09:52:02,165 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [9, 7, 16, 24, 22, 32, 21, 1, 29, 28, 6, 4, 11, 5, 12, 8, 10, 34, 0, 35, 25, 36, 33, 17, 19, 38, 30, 3, 15, 27, 13, 14, 2, 18, 26, 20, 23, 31, 37], 'cur_cost': 127529.0, 'intermediate_solutions': [{'tour': [10, 7, 23, 34, 35, 8, 6, 38, 36, 18, 31, 1, 25, 3, 0, 4, 28, 22, 24, 33, 9, 11, 5, 14, 13, 37, 12, 2, 29, 27, 30, 17, 15, 20, 32, 16, 19, 21, 26], 'cur_cost': 93150.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 7, 23, 34, 35, 8, 6, 38, 36, 18, 21, 1, 25, 3, 0, 4, 28, 22, 24, 29, 2, 12, 37, 13, 14, 5, 11, 9, 33, 27, 30, 17, 15, 20, 32, 16, 19, 31, 26], 'cur_cost': 97720.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 7, 23, 34, 35, 8, 6, 38, 36, 18, 21, 1, 25, 3, 0, 4, 28, 22, 24, 33, 9, 11, 5, 14, 37, 12, 2, 29, 27, 30, 17, 13, 15, 20, 32, 16, 19, 31, 26], 'cur_cost': 104021.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:02,165 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 127529.00)
2025-08-05 09:52:02,165 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:02,165 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:02,165 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:02,166 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 39
2025-08-05 09:52:02,166 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,168 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,168 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,168 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,168 - ExplorationExpert - INFO - 探索路径生成完成，成本: 38076.0, 路径长度: 39, 收集中间解: 3
2025-08-05 09:52:02,169 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 10, 23, 11, 12, 14, 13, 7, 5, 8, 9, 6, 20, 25, 21, 22, 24, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37], 'cur_cost': 38076.0, 'intermediate_solutions': [{'tour': [9, 8, 26, 0, 19, 7, 22, 34, 6, 36, 25, 35, 2, 28, 30, 18, 17, 21, 15, 13, 37, 5, 33, 32, 20, 16, 23, 4, 27, 29, 24, 12, 38, 14, 1, 3, 31, 10, 11], 'cur_cost': 114596.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 25, 33, 5, 37, 13, 15, 21, 17, 18, 30, 28, 2, 35, 8, 36, 6, 34, 22, 7, 19, 0, 26, 32, 20, 16, 23, 4, 27, 29, 24, 12, 38, 14, 1, 3, 31, 10, 11], 'cur_cost': 120737.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 25, 26, 0, 19, 7, 22, 34, 6, 36, 8, 35, 2, 28, 30, 18, 17, 21, 15, 13, 37, 5, 33, 32, 20, 23, 4, 27, 29, 24, 12, 38, 14, 1, 3, 31, 10, 16, 11], 'cur_cost': 123875.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:02,169 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 38076.00)
2025-08-05 09:52:02,169 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:02,169 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:02,169 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:02,170 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 39
2025-08-05 09:52:02,170 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,171 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,171 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,171 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,171 - ExplorationExpert - INFO - 探索路径生成完成，成本: 33831.0, 路径长度: 39, 收集中间解: 3
2025-08-05 09:52:02,171 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 11, 23, 16, 17, 15, 19, 18, 22, 21, 25, 20, 24, 6, 8, 9, 5, 7, 12, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 33831.0, 'intermediate_solutions': [{'tour': [10, 7, 8, 25, 3, 5, 6, 1, 36, 28, 29, 18, 2, 33, 22, 0, 31, 21, 38, 19, 32, 27, 12, 11, 4, 30, 13, 14, 26, 9, 20, 15, 16, 34, 37, 17, 35, 24, 23], 'cur_cost': 126201.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 7, 8, 25, 3, 29, 28, 36, 11, 6, 5, 18, 2, 33, 22, 0, 31, 21, 38, 19, 32, 27, 12, 1, 4, 30, 13, 14, 26, 9, 20, 15, 16, 34, 37, 17, 35, 24, 23], 'cur_cost': 123667.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 7, 24, 8, 25, 3, 5, 6, 11, 36, 28, 29, 18, 2, 33, 22, 0, 31, 21, 38, 19, 32, 27, 12, 1, 4, 30, 13, 14, 26, 9, 20, 15, 16, 34, 37, 17, 35, 23], 'cur_cost': 128910.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:02,172 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 33831.00)
2025-08-05 09:52:02,172 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:02,172 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:02,172 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:02,174 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 39
2025-08-05 09:52:02,175 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,175 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,175 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,175 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,175 - ExplorationExpert - INFO - 探索路径生成完成，成本: 110373.0, 路径长度: 39, 收集中间解: 3
2025-08-05 09:52:02,175 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [4, 25, 38, 7, 20, 0, 29, 9, 21, 37, 5, 10, 34, 16, 35, 12, 36, 6, 1, 2, 26, 27, 3, 13, 33, 8, 22, 23, 32, 19, 15, 30, 24, 17, 18, 31, 28, 11, 14], 'cur_cost': 110373.0, 'intermediate_solutions': [{'tour': [0, 10, 11, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29, 20, 25, 21, 22, 23, 24, 6, 8, 9, 5, 7, 12, 14, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19], 'cur_cost': 34124.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 12, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29, 20, 25, 21, 22, 23, 24, 6, 8, 9, 5, 7, 11, 14, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 15, 17, 19], 'cur_cost': 34141.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 12, 3, 4, 1, 2, 26, 31, 27, 28, 29, 20, 25, 21, 22, 23, 24, 6, 8, 9, 5, 7, 11, 14, 30, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19], 'cur_cost': 47136.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:02,176 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 110373.00)
2025-08-05 09:52:02,176 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:52:02,176 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:52:02,176 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:02,177 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 39
2025-08-05 09:52:02,177 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,177 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,177 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,178 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,178 - ExplorationExpert - INFO - 探索路径生成完成，成本: 34900.0, 路径长度: 39, 收集中间解: 3
2025-08-05 09:52:02,178 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 12, 9, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29, 20, 25, 21, 22, 23, 24, 6, 8, 5, 7, 11, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19], 'cur_cost': 34900.0, 'intermediate_solutions': [{'tour': [0, 21, 14, 15, 22, 17, 18, 19, 16, 23, 24, 25, 20, 6, 8, 9, 5, 7, 12, 11, 10, 13, 32, 35, 38, 36, 33, 34, 37, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 42238.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 14, 15, 16, 17, 18, 9, 8, 6, 20, 25, 24, 23, 22, 19, 5, 7, 12, 11, 10, 13, 32, 35, 38, 36, 33, 34, 37, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 45914.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 14, 15, 16, 17, 18, 19, 22, 23, 24, 25, 20, 6, 8, 9, 5, 7, 12, 11, 10, 13, 31, 32, 35, 38, 36, 33, 34, 37, 1, 2, 4, 3, 26, 27, 30, 28, 29], 'cur_cost': 45470.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:02,178 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 34900.00)
2025-08-05 09:52:02,178 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 09:52:02,178 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:02,178 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:02,179 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 143478.0
2025-08-05 09:52:02,194 - ExploitationExpert - INFO - res_population_num: 9
2025-08-05 09:52:02,194 - ExploitationExpert - INFO - res_population_costs: [23763, 23763, 23771.0, 23771, 23771.0, 23763, 23763.0, 23763, 23763]
2025-08-05 09:52:02,194 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  1,  2, 13, 10, 14, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 24, 23, 22,
       21, 25, 20, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       10, 14, 13,  2,  1], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 20, 25, 21, 22, 23, 24, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 20, 25, 21, 22, 23, 24, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32, 12, 11, 14, 10, 13,  7,  5,
        8,  9,  6,  1,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12,
       11, 14, 10, 13,  2], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12, 11,
       10, 14, 13,  2,  1], dtype=int64)]
2025-08-05 09:52:02,197 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:02,197 - ExploitationExpert - INFO - populations: [{'tour': [0, 13, 1, 16, 17, 15, 19, 18, 22, 23, 24, 21, 25, 20, 6, 8, 9, 5, 7, 12, 11, 10, 14, 32, 35, 38, 36, 33, 34, 37, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 35524.0}, {'tour': [14, 10, 7, 9, 24, 33, 35, 18, 21, 6, 12, 5, 36, 32, 13, 0, 28, 3, 31, 29, 22, 23, 34, 8, 1, 25, 4, 26, 30, 17, 19, 15, 16, 20, 2, 27, 11, 38, 37], 'cur_cost': 86996.0}, {'tour': [14, 13, 1, 9, 17, 33, 19, 32, 34, 6, 12, 5, 36, 10, 31, 8, 11, 3, 37, 29, 22, 23, 35, 0, 27, 38, 4, 26, 30, 7, 24, 28, 15, 16, 2, 18, 20, 25, 21], 'cur_cost': 132235.0}, {'tour': array([ 7, 31, 29,  1,  5, 14, 28, 38,  6, 37, 20,  0, 36,  3, 34, 22, 27,
        8, 23, 33, 11, 16, 12, 30, 17, 24, 15, 19,  2,  4, 10, 26, 32, 18,
       35, 13,  9, 21, 25], dtype=int64), 'cur_cost': 147433.0}, {'tour': [9, 7, 16, 24, 22, 32, 21, 1, 29, 28, 6, 4, 11, 5, 12, 8, 10, 34, 0, 35, 25, 36, 33, 17, 19, 38, 30, 3, 15, 27, 13, 14, 2, 18, 26, 20, 23, 31, 37], 'cur_cost': 127529.0}, {'tour': [0, 10, 23, 11, 12, 14, 13, 7, 5, 8, 9, 6, 20, 25, 21, 22, 24, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37], 'cur_cost': 38076.0}, {'tour': [0, 11, 23, 16, 17, 15, 19, 18, 22, 21, 25, 20, 24, 6, 8, 9, 5, 7, 12, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 33831.0}, {'tour': [4, 25, 38, 7, 20, 0, 29, 9, 21, 37, 5, 10, 34, 16, 35, 12, 36, 6, 1, 2, 26, 27, 3, 13, 33, 8, 22, 23, 32, 19, 15, 30, 24, 17, 18, 31, 28, 11, 14], 'cur_cost': 110373.0}, {'tour': [0, 12, 9, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29, 20, 25, 21, 22, 23, 24, 6, 8, 5, 7, 11, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19], 'cur_cost': 34900.0}, {'tour': array([36, 17,  3, 31,  6, 19,  7, 29,  9, 10,  8, 33, 15, 28, 30, 11, 32,
       22,  1, 37,  5, 24, 20,  0, 16, 21, 34, 12, 26, 18, 13,  2, 14,  4,
       27, 38, 25, 23, 35], dtype=int64), 'cur_cost': 143478.0}]
2025-08-05 09:52:02,198 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:02,198 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 225, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 225, 'cache_hits': 0, 'similarity_calculations': 1050, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:02,199 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([36, 17,  3, 31,  6, 19,  7, 29,  9, 10,  8, 33, 15, 28, 30, 11, 32,
       22,  1, 37,  5, 24, 20,  0, 16, 21, 34, 12, 26, 18, 13,  2, 14,  4,
       27, 38, 25, 23, 35], dtype=int64), 'cur_cost': 143478.0, 'intermediate_solutions': [{'tour': array([21, 11,  8, 25,  1, 32, 35,  5, 12, 20, 27, 29, 26, 34, 22,  0,  6,
       36, 18,  2,  4,  7, 10, 19, 30, 15, 24, 38,  3,  9, 17, 31, 13, 16,
       14, 28, 33, 23, 37]), 'cur_cost': 147309.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([25, 21, 11,  8,  1, 32, 35,  5, 12, 20, 27, 29, 26, 34, 22,  0,  6,
       36, 18,  2,  4,  7, 10, 19, 30, 15, 24, 38,  3,  9, 17, 31, 13, 16,
       14, 28, 33, 23, 37]), 'cur_cost': 145778.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1, 25, 21, 11,  8, 32, 35,  5, 12, 20, 27, 29, 26, 34, 22,  0,  6,
       36, 18,  2,  4,  7, 10, 19, 30, 15, 24, 38,  3,  9, 17, 31, 13, 16,
       14, 28, 33, 23, 37]), 'cur_cost': 145158.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 8, 25, 21, 11,  1, 32, 35,  5, 12, 20, 27, 29, 26, 34, 22,  0,  6,
       36, 18,  2,  4,  7, 10, 19, 30, 15, 24, 38,  3,  9, 17, 31, 13, 16,
       14, 28, 33, 23, 37]), 'cur_cost': 146651.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 8,  1, 25, 21, 11, 32, 35,  5, 12, 20, 27, 29, 26, 34, 22,  0,  6,
       36, 18,  2,  4,  7, 10, 19, 30, 15, 24, 38,  3,  9, 17, 31, 13, 16,
       14, 28, 33, 23, 37]), 'cur_cost': 144724.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:02,199 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 143478.00)
2025-08-05 09:52:02,199 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:02,200 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:02,201 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 1, 16, 17, 15, 19, 18, 22, 23, 24, 21, 25, 20, 6, 8, 9, 5, 7, 12, 11, 10, 14, 32, 35, 38, 36, 33, 34, 37, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 35524.0, 'intermediate_solutions': [{'tour': [2, 21, 3, 37, 5, 30, 38, 15, 23, 28, 29, 26, 18, 19, 24, 22, 0, 31, 16, 11, 6, 32, 27, 12, 1, 4, 8, 13, 35, 36, 9, 25, 10, 17, 20, 7, 34, 14, 33], 'cur_cost': 132458.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 21, 3, 37, 5, 30, 38, 15, 23, 28, 29, 26, 18, 19, 24, 22, 0, 31, 16, 11, 6, 32, 27, 12, 1, 4, 8, 13, 35, 36, 9, 20, 34, 7, 25, 17, 10, 14, 33], 'cur_cost': 126493.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 21, 3, 37, 5, 31, 30, 38, 15, 23, 28, 29, 26, 18, 19, 24, 22, 0, 16, 11, 6, 32, 27, 12, 1, 4, 8, 13, 35, 36, 9, 20, 10, 17, 25, 7, 34, 14, 33], 'cur_cost': 129700.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [14, 10, 7, 9, 24, 33, 35, 18, 21, 6, 12, 5, 36, 32, 13, 0, 28, 3, 31, 29, 22, 23, 34, 8, 1, 25, 4, 26, 30, 17, 19, 15, 16, 20, 2, 27, 11, 38, 37], 'cur_cost': 86996.0, 'intermediate_solutions': [{'tour': [24, 4, 8, 25, 7, 5, 11, 35, 22, 1, 27, 26, 31, 2, 3, 28, 17, 34, 21, 20, 38, 23, 33, 18, 19, 36, 12, 32, 6, 10, 37, 9, 0, 13, 30, 29, 16, 15, 14], 'cur_cost': 109164.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [24, 4, 8, 25, 7, 5, 11, 35, 22, 1, 27, 26, 31, 9, 20, 10, 6, 32, 12, 36, 19, 18, 33, 23, 38, 37, 21, 34, 17, 28, 3, 2, 0, 13, 30, 29, 16, 15, 14], 'cur_cost': 108439.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [24, 4, 8, 25, 7, 5, 11, 35, 22, 1, 27, 26, 31, 2, 3, 28, 17, 34, 21, 37, 38, 23, 33, 18, 19, 36, 12, 32, 6, 10, 20, 9, 0, 13, 30, 29, 16, 15, 14], 'cur_cost': 108477.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [14, 13, 1, 9, 17, 33, 19, 32, 34, 6, 12, 5, 36, 10, 31, 8, 11, 3, 37, 29, 22, 23, 35, 0, 27, 38, 4, 26, 30, 7, 24, 28, 15, 16, 2, 18, 20, 25, 21], 'cur_cost': 132235.0, 'intermediate_solutions': [{'tour': [8, 25, 23, 2, 3, 4, 5, 11, 34, 32, 24, 21, 6, 10, 33, 7, 22, 36, 37, 38, 19, 15, 17, 0, 27, 20, 31, 9, 14, 26, 29, 28, 1, 30, 16, 35, 12, 13, 18], 'cur_cost': 97628.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 25, 23, 2, 3, 4, 5, 11, 34, 32, 24, 21, 6, 10, 33, 7, 36, 22, 37, 38, 19, 15, 17, 0, 27, 31, 20, 9, 14, 26, 29, 28, 1, 30, 16, 35, 12, 13, 18], 'cur_cost': 93960.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 25, 23, 2, 3, 4, 5, 11, 34, 32, 24, 21, 6, 10, 33, 7, 36, 22, 37, 38, 19, 15, 17, 0, 27, 20, 31, 9, 14, 26, 29, 28, 1, 30, 35, 12, 13, 16, 18], 'cur_cost': 98447.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 7, 31, 29,  1,  5, 14, 28, 38,  6, 37, 20,  0, 36,  3, 34, 22, 27,
        8, 23, 33, 11, 16, 12, 30, 17, 24, 15, 19,  2,  4, 10, 26, 32, 18,
       35, 13,  9, 21, 25], dtype=int64), 'cur_cost': 147433.0, 'intermediate_solutions': [{'tour': array([23, 21,  8,  2,  5,  4, 15, 32, 29, 18, 10, 24,  7,  0, 31, 16, 11,
        6, 33, 12,  1, 13, 35, 26, 20, 28, 30, 25, 37, 38, 19,  9, 22, 27,
       36, 34, 14,  3, 17]), 'cur_cost': 146973.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2, 23, 21,  8,  5,  4, 15, 32, 29, 18, 10, 24,  7,  0, 31, 16, 11,
        6, 33, 12,  1, 13, 35, 26, 20, 28, 30, 25, 37, 38, 19,  9, 22, 27,
       36, 34, 14,  3, 17]), 'cur_cost': 145678.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 5,  2, 23, 21,  8,  4, 15, 32, 29, 18, 10, 24,  7,  0, 31, 16, 11,
        6, 33, 12,  1, 13, 35, 26, 20, 28, 30, 25, 37, 38, 19,  9, 22, 27,
       36, 34, 14,  3, 17]), 'cur_cost': 148549.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 8,  2, 23, 21,  5,  4, 15, 32, 29, 18, 10, 24,  7,  0, 31, 16, 11,
        6, 33, 12,  1, 13, 35, 26, 20, 28, 30, 25, 37, 38, 19,  9, 22, 27,
       36, 34, 14,  3, 17]), 'cur_cost': 148551.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 8,  5,  2, 23, 21,  4, 15, 32, 29, 18, 10, 24,  7,  0, 31, 16, 11,
        6, 33, 12,  1, 13, 35, 26, 20, 28, 30, 25, 37, 38, 19,  9, 22, 27,
       36, 34, 14,  3, 17]), 'cur_cost': 146213.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [9, 7, 16, 24, 22, 32, 21, 1, 29, 28, 6, 4, 11, 5, 12, 8, 10, 34, 0, 35, 25, 36, 33, 17, 19, 38, 30, 3, 15, 27, 13, 14, 2, 18, 26, 20, 23, 31, 37], 'cur_cost': 127529.0, 'intermediate_solutions': [{'tour': [10, 7, 23, 34, 35, 8, 6, 38, 36, 18, 31, 1, 25, 3, 0, 4, 28, 22, 24, 33, 9, 11, 5, 14, 13, 37, 12, 2, 29, 27, 30, 17, 15, 20, 32, 16, 19, 21, 26], 'cur_cost': 93150.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 7, 23, 34, 35, 8, 6, 38, 36, 18, 21, 1, 25, 3, 0, 4, 28, 22, 24, 29, 2, 12, 37, 13, 14, 5, 11, 9, 33, 27, 30, 17, 15, 20, 32, 16, 19, 31, 26], 'cur_cost': 97720.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 7, 23, 34, 35, 8, 6, 38, 36, 18, 21, 1, 25, 3, 0, 4, 28, 22, 24, 33, 9, 11, 5, 14, 37, 12, 2, 29, 27, 30, 17, 13, 15, 20, 32, 16, 19, 31, 26], 'cur_cost': 104021.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 23, 11, 12, 14, 13, 7, 5, 8, 9, 6, 20, 25, 21, 22, 24, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37], 'cur_cost': 38076.0, 'intermediate_solutions': [{'tour': [9, 8, 26, 0, 19, 7, 22, 34, 6, 36, 25, 35, 2, 28, 30, 18, 17, 21, 15, 13, 37, 5, 33, 32, 20, 16, 23, 4, 27, 29, 24, 12, 38, 14, 1, 3, 31, 10, 11], 'cur_cost': 114596.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 25, 33, 5, 37, 13, 15, 21, 17, 18, 30, 28, 2, 35, 8, 36, 6, 34, 22, 7, 19, 0, 26, 32, 20, 16, 23, 4, 27, 29, 24, 12, 38, 14, 1, 3, 31, 10, 11], 'cur_cost': 120737.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 25, 26, 0, 19, 7, 22, 34, 6, 36, 8, 35, 2, 28, 30, 18, 17, 21, 15, 13, 37, 5, 33, 32, 20, 23, 4, 27, 29, 24, 12, 38, 14, 1, 3, 31, 10, 16, 11], 'cur_cost': 123875.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 23, 16, 17, 15, 19, 18, 22, 21, 25, 20, 24, 6, 8, 9, 5, 7, 12, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 33831.0, 'intermediate_solutions': [{'tour': [10, 7, 8, 25, 3, 5, 6, 1, 36, 28, 29, 18, 2, 33, 22, 0, 31, 21, 38, 19, 32, 27, 12, 11, 4, 30, 13, 14, 26, 9, 20, 15, 16, 34, 37, 17, 35, 24, 23], 'cur_cost': 126201.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 7, 8, 25, 3, 29, 28, 36, 11, 6, 5, 18, 2, 33, 22, 0, 31, 21, 38, 19, 32, 27, 12, 1, 4, 30, 13, 14, 26, 9, 20, 15, 16, 34, 37, 17, 35, 24, 23], 'cur_cost': 123667.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 7, 24, 8, 25, 3, 5, 6, 11, 36, 28, 29, 18, 2, 33, 22, 0, 31, 21, 38, 19, 32, 27, 12, 1, 4, 30, 13, 14, 26, 9, 20, 15, 16, 34, 37, 17, 35, 23], 'cur_cost': 128910.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [4, 25, 38, 7, 20, 0, 29, 9, 21, 37, 5, 10, 34, 16, 35, 12, 36, 6, 1, 2, 26, 27, 3, 13, 33, 8, 22, 23, 32, 19, 15, 30, 24, 17, 18, 31, 28, 11, 14], 'cur_cost': 110373.0, 'intermediate_solutions': [{'tour': [0, 10, 11, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29, 20, 25, 21, 22, 23, 24, 6, 8, 9, 5, 7, 12, 14, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19], 'cur_cost': 34124.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 12, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29, 20, 25, 21, 22, 23, 24, 6, 8, 9, 5, 7, 11, 14, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 15, 17, 19], 'cur_cost': 34141.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 12, 3, 4, 1, 2, 26, 31, 27, 28, 29, 20, 25, 21, 22, 23, 24, 6, 8, 9, 5, 7, 11, 14, 30, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19], 'cur_cost': 47136.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 9, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29, 20, 25, 21, 22, 23, 24, 6, 8, 5, 7, 11, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19], 'cur_cost': 34900.0, 'intermediate_solutions': [{'tour': [0, 21, 14, 15, 22, 17, 18, 19, 16, 23, 24, 25, 20, 6, 8, 9, 5, 7, 12, 11, 10, 13, 32, 35, 38, 36, 33, 34, 37, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 42238.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 14, 15, 16, 17, 18, 9, 8, 6, 20, 25, 24, 23, 22, 19, 5, 7, 12, 11, 10, 13, 32, 35, 38, 36, 33, 34, 37, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 45914.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 14, 15, 16, 17, 18, 19, 22, 23, 24, 25, 20, 6, 8, 9, 5, 7, 12, 11, 10, 13, 31, 32, 35, 38, 36, 33, 34, 37, 1, 2, 4, 3, 26, 27, 30, 28, 29], 'cur_cost': 45470.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([36, 17,  3, 31,  6, 19,  7, 29,  9, 10,  8, 33, 15, 28, 30, 11, 32,
       22,  1, 37,  5, 24, 20,  0, 16, 21, 34, 12, 26, 18, 13,  2, 14,  4,
       27, 38, 25, 23, 35], dtype=int64), 'cur_cost': 143478.0, 'intermediate_solutions': [{'tour': array([21, 11,  8, 25,  1, 32, 35,  5, 12, 20, 27, 29, 26, 34, 22,  0,  6,
       36, 18,  2,  4,  7, 10, 19, 30, 15, 24, 38,  3,  9, 17, 31, 13, 16,
       14, 28, 33, 23, 37]), 'cur_cost': 147309.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([25, 21, 11,  8,  1, 32, 35,  5, 12, 20, 27, 29, 26, 34, 22,  0,  6,
       36, 18,  2,  4,  7, 10, 19, 30, 15, 24, 38,  3,  9, 17, 31, 13, 16,
       14, 28, 33, 23, 37]), 'cur_cost': 145778.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1, 25, 21, 11,  8, 32, 35,  5, 12, 20, 27, 29, 26, 34, 22,  0,  6,
       36, 18,  2,  4,  7, 10, 19, 30, 15, 24, 38,  3,  9, 17, 31, 13, 16,
       14, 28, 33, 23, 37]), 'cur_cost': 145158.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 8, 25, 21, 11,  1, 32, 35,  5, 12, 20, 27, 29, 26, 34, 22,  0,  6,
       36, 18,  2,  4,  7, 10, 19, 30, 15, 24, 38,  3,  9, 17, 31, 13, 16,
       14, 28, 33, 23, 37]), 'cur_cost': 146651.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 8,  1, 25, 21, 11, 32, 35,  5, 12, 20, 27, 29, 26, 34, 22,  0,  6,
       36, 18,  2,  4,  7, 10, 19, 30, 15, 24, 38,  3,  9, 17, 31, 13, 16,
       14, 28, 33, 23, 37]), 'cur_cost': 144724.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 09:52:02,201 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:02,201 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:02,203 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=33831.000, 多样性=0.958
2025-08-05 09:52:02,203 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 09:52:02,203 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 09:52:02,203 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:02,204 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.05197463960594492, 'best_improvement': 0.007975837902823798}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.010594467333725684}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.08499736509639738, 'recent_improvements': [0.07689233514077545, 0.002722144395569812, -0.09310239505201928], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 9, 'new_count': 9, 'count_change': 0, 'old_best_cost': 23763, 'new_best_cost': 23763, 'quality_improvement': 0.0, 'old_diversity': 0.7656695156695157, 'new_diversity': 0.7656695156695157, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配']}
2025-08-05 09:52:02,205 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 09:52:02,205 - __main__ - INFO - composite6_39 开始进化第 3 代
2025-08-05 09:52:02,205 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 09:52:02,205 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:02,205 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=33831.000, 多样性=0.958
2025-08-05 09:52:02,206 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:02,208 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.958
2025-08-05 09:52:02,208 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:02,211 - EliteExpert - INFO - 精英解分析完成: 精英解数量=9, 多样性=0.766
2025-08-05 09:52:02,212 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 09:52:02,212 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:02,212 - LandscapeExpert - INFO - 添加精英解数据: 9个精英解
2025-08-05 09:52:02,212 - LandscapeExpert - INFO - 数据提取成功: 19个路径, 19个适应度值
2025-08-05 09:52:02,250 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.316, 适应度梯度: -18938.916, 聚类评分: 0.000, 覆盖率: 0.110, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:02,250 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 09:52:02,250 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:02,250 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite6_39
2025-08-05 09:52:02,531 - visualization.landscape_visualizer - INFO - 插值约束: 440 个点被约束到最小值 23763.00
2025-08-05 09:52:02,607 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite6_39\landscape_composite6_39_iter_88_20250805_095202.html
2025-08-05 09:52:02,648 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite6_39\dashboard_composite6_39_iter_88_20250805_095202.html
2025-08-05 09:52:02,648 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 88
2025-08-05 09:52:02,648 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 09:52:02,648 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.4351秒
2025-08-05 09:52:02,648 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3157894736842105, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -18938.915789473685, 'local_optima_density': 0.3157894736842105, 'gradient_variance': 2495845997.142382, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1097, 'fitness_entropy': 0.685972951096462, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -18938.916)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.110)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358722.2500622, 'performance_metrics': {}}}
2025-08-05 09:52:02,649 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:02,649 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:02,649 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:02,649 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:02,649 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:52:02,649 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 09:52:02,649 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:52:02,649 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:02,649 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:02,650 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:52:02,650 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:02,650 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:02,650 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 6} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:02,650 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:02,650 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:02,650 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:02,652 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 39
2025-08-05 09:52:02,652 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,652 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,653 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,653 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,653 - ExplorationExpert - INFO - 探索路径生成完成，成本: 33960.0, 路径长度: 39, 收集中间解: 3
2025-08-05 09:52:02,654 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 14, 10, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 20, 25, 21, 22, 23, 24, 6, 8, 9, 5, 7, 12, 11, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19], 'cur_cost': 33960.0, 'intermediate_solutions': [{'tour': [26, 13, 1, 16, 17, 15, 19, 18, 22, 23, 24, 21, 25, 20, 6, 8, 9, 5, 7, 12, 11, 10, 14, 32, 35, 38, 36, 33, 34, 37, 2, 4, 3, 0, 31, 27, 30, 28, 29], 'cur_cost': 35580.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 1, 16, 17, 31, 26, 3, 4, 2, 37, 34, 33, 36, 38, 35, 32, 14, 10, 11, 12, 7, 5, 9, 8, 6, 20, 25, 21, 24, 23, 22, 18, 19, 15, 27, 30, 28, 29], 'cur_cost': 46195.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 1, 16, 17, 15, 19, 18, 22, 23, 24, 21, 25, 20, 6, 8, 9, 5, 28, 7, 12, 11, 10, 14, 32, 35, 38, 36, 33, 34, 37, 2, 4, 3, 26, 31, 27, 30, 29], 'cur_cost': 45507.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:02,654 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 33960.00)
2025-08-05 09:52:02,654 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:02,654 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:02,654 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:02,657 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 39
2025-08-05 09:52:02,657 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,657 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,657 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,657 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,658 - ExplorationExpert - INFO - 探索路径生成完成，成本: 92330.0, 路径长度: 39, 收集中间解: 3
2025-08-05 09:52:02,658 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [4, 23, 9, 12, 5, 8, 10, 7, 22, 1, 26, 29, 0, 30, 17, 16, 25, 24, 34, 37, 36, 33, 38, 35, 6, 2, 27, 3, 13, 21, 19, 15, 20, 32, 11, 31, 28, 18, 14], 'cur_cost': 92330.0, 'intermediate_solutions': [{'tour': [14, 10, 7, 9, 24, 33, 32, 18, 21, 6, 12, 5, 36, 35, 13, 0, 28, 3, 31, 29, 22, 23, 34, 8, 1, 25, 4, 26, 30, 17, 19, 15, 16, 20, 2, 27, 11, 38, 37], 'cur_cost': 87039.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [14, 10, 7, 9, 24, 33, 17, 30, 26, 4, 25, 1, 8, 34, 23, 22, 29, 31, 3, 28, 0, 13, 32, 36, 5, 12, 6, 21, 18, 35, 19, 15, 16, 20, 2, 27, 11, 38, 37], 'cur_cost': 95377.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [14, 10, 7, 9, 24, 33, 35, 18, 21, 6, 12, 5, 36, 32, 28, 13, 0, 3, 31, 29, 22, 23, 34, 8, 1, 25, 4, 26, 30, 17, 19, 15, 16, 20, 2, 27, 11, 38, 37], 'cur_cost': 92009.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:02,658 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 92330.00)
2025-08-05 09:52:02,658 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-05 09:52:02,658 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:02,658 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:02,659 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 138737.0
2025-08-05 09:52:02,670 - ExploitationExpert - INFO - res_population_num: 9
2025-08-05 09:52:02,670 - ExploitationExpert - INFO - res_population_costs: [23763, 23763, 23763, 23763.0, 23763, 23763, 23771.0, 23771, 23771.0]
2025-08-05 09:52:02,670 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  1,  2, 13, 10, 14, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32, 12, 11, 14, 10, 13,  7,  5,
        8,  9,  6,  1,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12,
       11, 14, 10, 13,  2], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12, 11,
       10, 14, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 24, 23, 22,
       21, 25, 20, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       10, 14, 13,  2,  1], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 20, 25, 21, 22, 23, 24, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 20, 25, 21, 22, 23, 24, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64)]
2025-08-05 09:52:02,673 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:02,674 - ExploitationExpert - INFO - populations: [{'tour': [0, 14, 10, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 20, 25, 21, 22, 23, 24, 6, 8, 9, 5, 7, 12, 11, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19], 'cur_cost': 33960.0}, {'tour': [4, 23, 9, 12, 5, 8, 10, 7, 22, 1, 26, 29, 0, 30, 17, 16, 25, 24, 34, 37, 36, 33, 38, 35, 6, 2, 27, 3, 13, 21, 19, 15, 20, 32, 11, 31, 28, 18, 14], 'cur_cost': 92330.0}, {'tour': array([23, 27,  5, 10, 21, 12, 37, 33, 28, 18, 24,  0, 15,  7, 30, 32, 19,
       38, 20, 36, 35, 31, 22, 25, 29, 16, 34,  8, 26, 14, 11,  6, 17,  2,
        1,  4,  3, 13,  9], dtype=int64), 'cur_cost': 138737.0}, {'tour': [7, 31, 29, 1, 5, 14, 28, 38, 6, 37, 20, 0, 36, 3, 34, 22, 27, 8, 23, 33, 11, 16, 12, 30, 17, 24, 15, 19, 2, 4, 10, 26, 32, 18, 35, 13, 9, 21, 25], 'cur_cost': 147433.0}, {'tour': [9, 7, 16, 24, 22, 32, 21, 1, 29, 28, 6, 4, 11, 5, 12, 8, 10, 34, 0, 35, 25, 36, 33, 17, 19, 38, 30, 3, 15, 27, 13, 14, 2, 18, 26, 20, 23, 31, 37], 'cur_cost': 127529.0}, {'tour': [0, 10, 23, 11, 12, 14, 13, 7, 5, 8, 9, 6, 20, 25, 21, 22, 24, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37], 'cur_cost': 38076.0}, {'tour': [0, 11, 23, 16, 17, 15, 19, 18, 22, 21, 25, 20, 24, 6, 8, 9, 5, 7, 12, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 33831.0}, {'tour': [4, 25, 38, 7, 20, 0, 29, 9, 21, 37, 5, 10, 34, 16, 35, 12, 36, 6, 1, 2, 26, 27, 3, 13, 33, 8, 22, 23, 32, 19, 15, 30, 24, 17, 18, 31, 28, 11, 14], 'cur_cost': 110373.0}, {'tour': [0, 12, 9, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29, 20, 25, 21, 22, 23, 24, 6, 8, 5, 7, 11, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19], 'cur_cost': 34900.0}, {'tour': [36, 17, 3, 31, 6, 19, 7, 29, 9, 10, 8, 33, 15, 28, 30, 11, 32, 22, 1, 37, 5, 24, 20, 0, 16, 21, 34, 12, 26, 18, 13, 2, 14, 4, 27, 38, 25, 23, 35], 'cur_cost': 143478.0}]
2025-08-05 09:52:02,674 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:02,674 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 226, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 226, 'cache_hits': 0, 'similarity_calculations': 1054, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:02,675 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([23, 27,  5, 10, 21, 12, 37, 33, 28, 18, 24,  0, 15,  7, 30, 32, 19,
       38, 20, 36, 35, 31, 22, 25, 29, 16, 34,  8, 26, 14, 11,  6, 17,  2,
        1,  4,  3, 13,  9], dtype=int64), 'cur_cost': 138737.0, 'intermediate_solutions': [{'tour': array([ 1, 13, 14,  9, 17, 33, 19, 32, 34,  6, 12,  5, 36, 10, 31,  8, 11,
        3, 37, 29, 22, 23, 35,  0, 27, 38,  4, 26, 30,  7, 24, 28, 15, 16,
        2, 18, 20, 25, 21]), 'cur_cost': 129921.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9,  1, 13, 14, 17, 33, 19, 32, 34,  6, 12,  5, 36, 10, 31,  8, 11,
        3, 37, 29, 22, 23, 35,  0, 27, 38,  4, 26, 30,  7, 24, 28, 15, 16,
        2, 18, 20, 25, 21]), 'cur_cost': 132244.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([17,  9,  1, 13, 14, 33, 19, 32, 34,  6, 12,  5, 36, 10, 31,  8, 11,
        3, 37, 29, 22, 23, 35,  0, 27, 38,  4, 26, 30,  7, 24, 28, 15, 16,
        2, 18, 20, 25, 21]), 'cur_cost': 130789.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([14,  9,  1, 13, 17, 33, 19, 32, 34,  6, 12,  5, 36, 10, 31,  8, 11,
        3, 37, 29, 22, 23, 35,  0, 27, 38,  4, 26, 30,  7, 24, 28, 15, 16,
        2, 18, 20, 25, 21]), 'cur_cost': 137187.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([14, 17,  9,  1, 13, 33, 19, 32, 34,  6, 12,  5, 36, 10, 31,  8, 11,
        3, 37, 29, 22, 23, 35,  0, 27, 38,  4, 26, 30,  7, 24, 28, 15, 16,
        2, 18, 20, 25, 21]), 'cur_cost': 139603.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:02,675 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 138737.00)
2025-08-05 09:52:02,675 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:52:02,675 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:02,675 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:02,676 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 145142.0
2025-08-05 09:52:02,686 - ExploitationExpert - INFO - res_population_num: 9
2025-08-05 09:52:02,686 - ExploitationExpert - INFO - res_population_costs: [23763, 23763, 23763, 23763.0, 23763, 23763, 23771.0, 23771, 23771.0]
2025-08-05 09:52:02,686 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  1,  2, 13, 10, 14, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32, 12, 11, 14, 10, 13,  7,  5,
        8,  9,  6,  1,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12,
       11, 14, 10, 13,  2], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12, 11,
       10, 14, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 24, 23, 22,
       21, 25, 20, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       10, 14, 13,  2,  1], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 20, 25, 21, 22, 23, 24, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 20, 25, 21, 22, 23, 24, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64)]
2025-08-05 09:52:02,689 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:02,689 - ExploitationExpert - INFO - populations: [{'tour': [0, 14, 10, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 20, 25, 21, 22, 23, 24, 6, 8, 9, 5, 7, 12, 11, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19], 'cur_cost': 33960.0}, {'tour': [4, 23, 9, 12, 5, 8, 10, 7, 22, 1, 26, 29, 0, 30, 17, 16, 25, 24, 34, 37, 36, 33, 38, 35, 6, 2, 27, 3, 13, 21, 19, 15, 20, 32, 11, 31, 28, 18, 14], 'cur_cost': 92330.0}, {'tour': array([23, 27,  5, 10, 21, 12, 37, 33, 28, 18, 24,  0, 15,  7, 30, 32, 19,
       38, 20, 36, 35, 31, 22, 25, 29, 16, 34,  8, 26, 14, 11,  6, 17,  2,
        1,  4,  3, 13,  9], dtype=int64), 'cur_cost': 138737.0}, {'tour': array([30,  3, 28, 15, 14, 34, 17,  8,  5, 18, 25, 31, 27,  6,  7,  2, 22,
       29, 20, 21, 36, 11, 24, 26, 35, 16,  9, 32, 10,  1, 33,  0, 19, 38,
       12, 37, 13, 23,  4], dtype=int64), 'cur_cost': 145142.0}, {'tour': [9, 7, 16, 24, 22, 32, 21, 1, 29, 28, 6, 4, 11, 5, 12, 8, 10, 34, 0, 35, 25, 36, 33, 17, 19, 38, 30, 3, 15, 27, 13, 14, 2, 18, 26, 20, 23, 31, 37], 'cur_cost': 127529.0}, {'tour': [0, 10, 23, 11, 12, 14, 13, 7, 5, 8, 9, 6, 20, 25, 21, 22, 24, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37], 'cur_cost': 38076.0}, {'tour': [0, 11, 23, 16, 17, 15, 19, 18, 22, 21, 25, 20, 24, 6, 8, 9, 5, 7, 12, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 33831.0}, {'tour': [4, 25, 38, 7, 20, 0, 29, 9, 21, 37, 5, 10, 34, 16, 35, 12, 36, 6, 1, 2, 26, 27, 3, 13, 33, 8, 22, 23, 32, 19, 15, 30, 24, 17, 18, 31, 28, 11, 14], 'cur_cost': 110373.0}, {'tour': [0, 12, 9, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29, 20, 25, 21, 22, 23, 24, 6, 8, 5, 7, 11, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19], 'cur_cost': 34900.0}, {'tour': [36, 17, 3, 31, 6, 19, 7, 29, 9, 10, 8, 33, 15, 28, 30, 11, 32, 22, 1, 37, 5, 24, 20, 0, 16, 21, 34, 12, 26, 18, 13, 2, 14, 4, 27, 38, 25, 23, 35], 'cur_cost': 143478.0}]
2025-08-05 09:52:02,690 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:02,690 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 227, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 227, 'cache_hits': 0, 'similarity_calculations': 1059, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:02,691 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([30,  3, 28, 15, 14, 34, 17,  8,  5, 18, 25, 31, 27,  6,  7,  2, 22,
       29, 20, 21, 36, 11, 24, 26, 35, 16,  9, 32, 10,  1, 33,  0, 19, 38,
       12, 37, 13, 23,  4], dtype=int64), 'cur_cost': 145142.0, 'intermediate_solutions': [{'tour': array([29, 31,  7,  1,  5, 14, 28, 38,  6, 37, 20,  0, 36,  3, 34, 22, 27,
        8, 23, 33, 11, 16, 12, 30, 17, 24, 15, 19,  2,  4, 10, 26, 32, 18,
       35, 13,  9, 21, 25]), 'cur_cost': 150252.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1, 29, 31,  7,  5, 14, 28, 38,  6, 37, 20,  0, 36,  3, 34, 22, 27,
        8, 23, 33, 11, 16, 12, 30, 17, 24, 15, 19,  2,  4, 10, 26, 32, 18,
       35, 13,  9, 21, 25]), 'cur_cost': 145093.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 5,  1, 29, 31,  7, 14, 28, 38,  6, 37, 20,  0, 36,  3, 34, 22, 27,
        8, 23, 33, 11, 16, 12, 30, 17, 24, 15, 19,  2,  4, 10, 26, 32, 18,
       35, 13,  9, 21, 25]), 'cur_cost': 147397.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 7,  1, 29, 31,  5, 14, 28, 38,  6, 37, 20,  0, 36,  3, 34, 22, 27,
        8, 23, 33, 11, 16, 12, 30, 17, 24, 15, 19,  2,  4, 10, 26, 32, 18,
       35, 13,  9, 21, 25]), 'cur_cost': 147430.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 7,  5,  1, 29, 31, 14, 28, 38,  6, 37, 20,  0, 36,  3, 34, 22, 27,
        8, 23, 33, 11, 16, 12, 30, 17, 24, 15, 19,  2,  4, 10, 26, 32, 18,
       35, 13,  9, 21, 25]), 'cur_cost': 146556.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:02,692 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 145142.00)
2025-08-05 09:52:02,692 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:02,692 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:02,692 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:02,696 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 39
2025-08-05 09:52:02,696 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,696 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,697 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,697 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,697 - ExplorationExpert - INFO - 探索路径生成完成，成本: 89204.0, 路径长度: 39, 收集中间解: 3
2025-08-05 09:52:02,697 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [33, 21, 34, 36, 12, 23, 25, 7, 5, 32, 18, 22, 0, 20, 6, 24, 35, 15, 4, 28, 1, 27, 30, 3, 13, 11, 37, 38, 9, 10, 14, 2, 29, 26, 31, 17, 16, 19, 8], 'cur_cost': 89204.0, 'intermediate_solutions': [{'tour': [9, 32, 16, 24, 22, 7, 21, 1, 29, 28, 6, 4, 11, 5, 12, 8, 10, 34, 0, 35, 25, 36, 33, 17, 19, 38, 30, 3, 15, 27, 13, 14, 2, 18, 26, 20, 23, 31, 37], 'cur_cost': 127842.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 7, 16, 0, 34, 10, 8, 12, 5, 11, 4, 6, 28, 29, 1, 21, 32, 22, 24, 35, 25, 36, 33, 17, 19, 38, 30, 3, 15, 27, 13, 14, 2, 18, 26, 20, 23, 31, 37], 'cur_cost': 127109.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 7, 16, 37, 24, 22, 32, 21, 1, 29, 28, 6, 4, 11, 5, 12, 8, 10, 34, 0, 35, 25, 36, 33, 17, 19, 38, 30, 3, 15, 27, 13, 14, 2, 18, 26, 20, 23, 31], 'cur_cost': 127047.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:02,697 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 89204.00)
2025-08-05 09:52:02,697 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:02,698 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:02,698 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:02,699 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 39
2025-08-05 09:52:02,699 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,699 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,699 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,700 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,700 - ExplorationExpert - INFO - 探索路径生成完成，成本: 36960.0, 路径长度: 39, 收集中间解: 3
2025-08-05 09:52:02,700 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 16, 15, 10, 14, 13, 12, 11, 7, 5, 8, 9, 6, 20, 25, 21, 22, 23, 24, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 17, 18, 19, 34, 33, 36, 38, 35, 32, 37], 'cur_cost': 36960.0, 'intermediate_solutions': [{'tour': [0, 10, 23, 11, 12, 14, 13, 7, 5, 8, 9, 6, 20, 25, 21, 22, 24, 1, 2, 4, 3, 26, 31, 27, 30, 28, 38, 17, 16, 18, 15, 19, 34, 33, 36, 29, 35, 32, 37], 'cur_cost': 57775.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 23, 11, 20, 6, 9, 8, 5, 7, 13, 14, 12, 25, 21, 22, 24, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37], 'cur_cost': 46716.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 8, 23, 11, 12, 14, 13, 7, 5, 9, 6, 20, 25, 21, 22, 24, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37], 'cur_cost': 38078.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:02,700 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 36960.00)
2025-08-05 09:52:02,700 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:02,700 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:02,700 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:02,702 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 39
2025-08-05 09:52:02,702 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,703 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,703 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,704 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,704 - ExplorationExpert - INFO - 探索路径生成完成，成本: 33857.0, 路径长度: 39, 收集中间解: 3
2025-08-05 09:52:02,704 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 21, 1, 6, 8, 9, 5, 7, 20, 25, 22, 23, 24, 3, 4, 2, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37, 12, 11, 10, 14, 13], 'cur_cost': 33857.0, 'intermediate_solutions': [{'tour': [0, 11, 23, 16, 17, 15, 19, 18, 22, 21, 33, 20, 24, 6, 8, 9, 5, 7, 12, 10, 14, 13, 32, 35, 38, 36, 25, 34, 37, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 44950.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 23, 16, 17, 15, 19, 18, 22, 21, 25, 20, 24, 6, 8, 9, 5, 7, 12, 10, 2, 1, 37, 34, 33, 36, 38, 35, 32, 13, 14, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 42675.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 23, 16, 17, 15, 19, 18, 22, 21, 25, 20, 24, 6, 8, 9, 5, 7, 12, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 11, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 31152.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:02,705 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 33857.00)
2025-08-05 09:52:02,705 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:02,705 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:02,705 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:02,709 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 39
2025-08-05 09:52:02,709 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,709 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,710 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,710 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,710 - ExplorationExpert - INFO - 探索路径生成完成，成本: 99211.0, 路径长度: 39, 收集中间解: 3
2025-08-05 09:52:02,710 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [29, 3, 7, 20, 4, 1, 26, 27, 31, 25, 0, 9, 12, 8, 23, 24, 2, 11, 6, 5, 14, 36, 34, 33, 18, 21, 16, 17, 22, 35, 37, 10, 38, 19, 32, 13, 30, 28, 15], 'cur_cost': 99211.0, 'intermediate_solutions': [{'tour': [4, 25, 38, 7, 20, 0, 29, 9, 21, 37, 5, 26, 34, 16, 35, 12, 36, 6, 1, 2, 10, 27, 3, 13, 33, 8, 22, 23, 32, 19, 15, 30, 24, 17, 18, 31, 28, 11, 14], 'cur_cost': 124200.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 25, 38, 7, 20, 0, 29, 9, 21, 37, 5, 10, 34, 16, 35, 12, 36, 6, 1, 15, 19, 32, 23, 22, 8, 33, 13, 3, 27, 26, 2, 30, 24, 17, 18, 31, 28, 11, 14], 'cur_cost': 111966.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 25, 38, 7, 20, 0, 29, 9, 21, 37, 5, 10, 34, 16, 31, 35, 12, 36, 6, 1, 2, 26, 27, 3, 13, 33, 8, 22, 23, 32, 19, 15, 30, 24, 17, 18, 28, 11, 14], 'cur_cost': 118310.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:02,711 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 99211.00)
2025-08-05 09:52:02,711 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:52:02,711 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:52:02,711 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:02,714 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 39
2025-08-05 09:52:02,714 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,715 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,715 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,715 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:02,716 - ExplorationExpert - INFO - 探索路径生成完成，成本: 99002.0, 路径长度: 39, 收集中间解: 3
2025-08-05 09:52:02,716 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [35, 20, 23, 2, 21, 5, 14, 10, 11, 34, 6, 8, 36, 33, 24, 38, 16, 4, 26, 22, 32, 18, 15, 0, 31, 30, 29, 3, 28, 7, 9, 13, 12, 25, 1, 17, 19, 37, 27], 'cur_cost': 99002.0, 'intermediate_solutions': [{'tour': [0, 12, 9, 3, 4, 1, 2, 26, 31, 27, 30, 11, 29, 20, 25, 21, 22, 23, 24, 6, 8, 5, 7, 28, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19], 'cur_cost': 57087.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 12, 9, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29, 20, 25, 21, 22, 23, 37, 34, 33, 36, 38, 35, 32, 13, 14, 10, 11, 7, 5, 8, 6, 24, 18, 16, 17, 15, 19], 'cur_cost': 36382.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 9, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29, 20, 25, 21, 22, 23, 24, 6, 8, 5, 11, 10, 14, 13, 32, 35, 38, 7, 36, 33, 34, 37, 18, 16, 17, 15, 19], 'cur_cost': 40167.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:02,716 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 99002.00)
2025-08-05 09:52:02,716 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 09:52:02,717 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:02,717 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:02,717 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 128931.0
2025-08-05 09:52:02,728 - ExploitationExpert - INFO - res_population_num: 9
2025-08-05 09:52:02,729 - ExploitationExpert - INFO - res_population_costs: [23763, 23763, 23763, 23763.0, 23763, 23763, 23771.0, 23771, 23771.0]
2025-08-05 09:52:02,729 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  1,  2, 13, 10, 14, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32, 12, 11, 14, 10, 13,  7,  5,
        8,  9,  6,  1,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12,
       11, 14, 10, 13,  2], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12, 11,
       10, 14, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 24, 23, 22,
       21, 25, 20, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       10, 14, 13,  2,  1], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 20, 25, 21, 22, 23, 24, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 20, 25, 21, 22, 23, 24, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64)]
2025-08-05 09:52:02,731 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:02,731 - ExploitationExpert - INFO - populations: [{'tour': [0, 14, 10, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 20, 25, 21, 22, 23, 24, 6, 8, 9, 5, 7, 12, 11, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19], 'cur_cost': 33960.0}, {'tour': [4, 23, 9, 12, 5, 8, 10, 7, 22, 1, 26, 29, 0, 30, 17, 16, 25, 24, 34, 37, 36, 33, 38, 35, 6, 2, 27, 3, 13, 21, 19, 15, 20, 32, 11, 31, 28, 18, 14], 'cur_cost': 92330.0}, {'tour': array([23, 27,  5, 10, 21, 12, 37, 33, 28, 18, 24,  0, 15,  7, 30, 32, 19,
       38, 20, 36, 35, 31, 22, 25, 29, 16, 34,  8, 26, 14, 11,  6, 17,  2,
        1,  4,  3, 13,  9], dtype=int64), 'cur_cost': 138737.0}, {'tour': array([30,  3, 28, 15, 14, 34, 17,  8,  5, 18, 25, 31, 27,  6,  7,  2, 22,
       29, 20, 21, 36, 11, 24, 26, 35, 16,  9, 32, 10,  1, 33,  0, 19, 38,
       12, 37, 13, 23,  4], dtype=int64), 'cur_cost': 145142.0}, {'tour': [33, 21, 34, 36, 12, 23, 25, 7, 5, 32, 18, 22, 0, 20, 6, 24, 35, 15, 4, 28, 1, 27, 30, 3, 13, 11, 37, 38, 9, 10, 14, 2, 29, 26, 31, 17, 16, 19, 8], 'cur_cost': 89204.0}, {'tour': [0, 16, 15, 10, 14, 13, 12, 11, 7, 5, 8, 9, 6, 20, 25, 21, 22, 23, 24, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 17, 18, 19, 34, 33, 36, 38, 35, 32, 37], 'cur_cost': 36960.0}, {'tour': [0, 21, 1, 6, 8, 9, 5, 7, 20, 25, 22, 23, 24, 3, 4, 2, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37, 12, 11, 10, 14, 13], 'cur_cost': 33857.0}, {'tour': [29, 3, 7, 20, 4, 1, 26, 27, 31, 25, 0, 9, 12, 8, 23, 24, 2, 11, 6, 5, 14, 36, 34, 33, 18, 21, 16, 17, 22, 35, 37, 10, 38, 19, 32, 13, 30, 28, 15], 'cur_cost': 99211.0}, {'tour': [35, 20, 23, 2, 21, 5, 14, 10, 11, 34, 6, 8, 36, 33, 24, 38, 16, 4, 26, 22, 32, 18, 15, 0, 31, 30, 29, 3, 28, 7, 9, 13, 12, 25, 1, 17, 19, 37, 27], 'cur_cost': 99002.0}, {'tour': array([17, 15, 22, 38, 32, 12, 27, 14, 30,  2, 18, 34, 16, 19, 35,  1, 26,
       13,  6, 21, 33,  4, 24,  8, 28,  7, 36, 10, 23,  9, 29, 31, 25, 20,
        3,  0, 11,  5, 37], dtype=int64), 'cur_cost': 128931.0}]
2025-08-05 09:52:02,733 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:02,733 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 228, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 228, 'cache_hits': 0, 'similarity_calculations': 1065, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:02,733 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([17, 15, 22, 38, 32, 12, 27, 14, 30,  2, 18, 34, 16, 19, 35,  1, 26,
       13,  6, 21, 33,  4, 24,  8, 28,  7, 36, 10, 23,  9, 29, 31, 25, 20,
        3,  0, 11,  5, 37], dtype=int64), 'cur_cost': 128931.0, 'intermediate_solutions': [{'tour': array([ 3, 17, 36, 31,  6, 19,  7, 29,  9, 10,  8, 33, 15, 28, 30, 11, 32,
       22,  1, 37,  5, 24, 20,  0, 16, 21, 34, 12, 26, 18, 13,  2, 14,  4,
       27, 38, 25, 23, 35]), 'cur_cost': 153355.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([31,  3, 17, 36,  6, 19,  7, 29,  9, 10,  8, 33, 15, 28, 30, 11, 32,
       22,  1, 37,  5, 24, 20,  0, 16, 21, 34, 12, 26, 18, 13,  2, 14,  4,
       27, 38, 25, 23, 35]), 'cur_cost': 148098.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 6, 31,  3, 17, 36, 19,  7, 29,  9, 10,  8, 33, 15, 28, 30, 11, 32,
       22,  1, 37,  5, 24, 20,  0, 16, 21, 34, 12, 26, 18, 13,  2, 14,  4,
       27, 38, 25, 23, 35]), 'cur_cost': 145523.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([36, 31,  3, 17,  6, 19,  7, 29,  9, 10,  8, 33, 15, 28, 30, 11, 32,
       22,  1, 37,  5, 24, 20,  0, 16, 21, 34, 12, 26, 18, 13,  2, 14,  4,
       27, 38, 25, 23, 35]), 'cur_cost': 145993.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([36,  6, 31,  3, 17, 19,  7, 29,  9, 10,  8, 33, 15, 28, 30, 11, 32,
       22,  1, 37,  5, 24, 20,  0, 16, 21, 34, 12, 26, 18, 13,  2, 14,  4,
       27, 38, 25, 23, 35]), 'cur_cost': 137094.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:02,734 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 128931.00)
2025-08-05 09:52:02,734 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:02,734 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:02,736 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 10, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 20, 25, 21, 22, 23, 24, 6, 8, 9, 5, 7, 12, 11, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19], 'cur_cost': 33960.0, 'intermediate_solutions': [{'tour': [26, 13, 1, 16, 17, 15, 19, 18, 22, 23, 24, 21, 25, 20, 6, 8, 9, 5, 7, 12, 11, 10, 14, 32, 35, 38, 36, 33, 34, 37, 2, 4, 3, 0, 31, 27, 30, 28, 29], 'cur_cost': 35580.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 1, 16, 17, 31, 26, 3, 4, 2, 37, 34, 33, 36, 38, 35, 32, 14, 10, 11, 12, 7, 5, 9, 8, 6, 20, 25, 21, 24, 23, 22, 18, 19, 15, 27, 30, 28, 29], 'cur_cost': 46195.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 1, 16, 17, 15, 19, 18, 22, 23, 24, 21, 25, 20, 6, 8, 9, 5, 28, 7, 12, 11, 10, 14, 32, 35, 38, 36, 33, 34, 37, 2, 4, 3, 26, 31, 27, 30, 29], 'cur_cost': 45507.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [4, 23, 9, 12, 5, 8, 10, 7, 22, 1, 26, 29, 0, 30, 17, 16, 25, 24, 34, 37, 36, 33, 38, 35, 6, 2, 27, 3, 13, 21, 19, 15, 20, 32, 11, 31, 28, 18, 14], 'cur_cost': 92330.0, 'intermediate_solutions': [{'tour': [14, 10, 7, 9, 24, 33, 32, 18, 21, 6, 12, 5, 36, 35, 13, 0, 28, 3, 31, 29, 22, 23, 34, 8, 1, 25, 4, 26, 30, 17, 19, 15, 16, 20, 2, 27, 11, 38, 37], 'cur_cost': 87039.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [14, 10, 7, 9, 24, 33, 17, 30, 26, 4, 25, 1, 8, 34, 23, 22, 29, 31, 3, 28, 0, 13, 32, 36, 5, 12, 6, 21, 18, 35, 19, 15, 16, 20, 2, 27, 11, 38, 37], 'cur_cost': 95377.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [14, 10, 7, 9, 24, 33, 35, 18, 21, 6, 12, 5, 36, 32, 28, 13, 0, 3, 31, 29, 22, 23, 34, 8, 1, 25, 4, 26, 30, 17, 19, 15, 16, 20, 2, 27, 11, 38, 37], 'cur_cost': 92009.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([23, 27,  5, 10, 21, 12, 37, 33, 28, 18, 24,  0, 15,  7, 30, 32, 19,
       38, 20, 36, 35, 31, 22, 25, 29, 16, 34,  8, 26, 14, 11,  6, 17,  2,
        1,  4,  3, 13,  9], dtype=int64), 'cur_cost': 138737.0, 'intermediate_solutions': [{'tour': array([ 1, 13, 14,  9, 17, 33, 19, 32, 34,  6, 12,  5, 36, 10, 31,  8, 11,
        3, 37, 29, 22, 23, 35,  0, 27, 38,  4, 26, 30,  7, 24, 28, 15, 16,
        2, 18, 20, 25, 21]), 'cur_cost': 129921.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9,  1, 13, 14, 17, 33, 19, 32, 34,  6, 12,  5, 36, 10, 31,  8, 11,
        3, 37, 29, 22, 23, 35,  0, 27, 38,  4, 26, 30,  7, 24, 28, 15, 16,
        2, 18, 20, 25, 21]), 'cur_cost': 132244.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([17,  9,  1, 13, 14, 33, 19, 32, 34,  6, 12,  5, 36, 10, 31,  8, 11,
        3, 37, 29, 22, 23, 35,  0, 27, 38,  4, 26, 30,  7, 24, 28, 15, 16,
        2, 18, 20, 25, 21]), 'cur_cost': 130789.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([14,  9,  1, 13, 17, 33, 19, 32, 34,  6, 12,  5, 36, 10, 31,  8, 11,
        3, 37, 29, 22, 23, 35,  0, 27, 38,  4, 26, 30,  7, 24, 28, 15, 16,
        2, 18, 20, 25, 21]), 'cur_cost': 137187.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([14, 17,  9,  1, 13, 33, 19, 32, 34,  6, 12,  5, 36, 10, 31,  8, 11,
        3, 37, 29, 22, 23, 35,  0, 27, 38,  4, 26, 30,  7, 24, 28, 15, 16,
        2, 18, 20, 25, 21]), 'cur_cost': 139603.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([30,  3, 28, 15, 14, 34, 17,  8,  5, 18, 25, 31, 27,  6,  7,  2, 22,
       29, 20, 21, 36, 11, 24, 26, 35, 16,  9, 32, 10,  1, 33,  0, 19, 38,
       12, 37, 13, 23,  4], dtype=int64), 'cur_cost': 145142.0, 'intermediate_solutions': [{'tour': array([29, 31,  7,  1,  5, 14, 28, 38,  6, 37, 20,  0, 36,  3, 34, 22, 27,
        8, 23, 33, 11, 16, 12, 30, 17, 24, 15, 19,  2,  4, 10, 26, 32, 18,
       35, 13,  9, 21, 25]), 'cur_cost': 150252.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1, 29, 31,  7,  5, 14, 28, 38,  6, 37, 20,  0, 36,  3, 34, 22, 27,
        8, 23, 33, 11, 16, 12, 30, 17, 24, 15, 19,  2,  4, 10, 26, 32, 18,
       35, 13,  9, 21, 25]), 'cur_cost': 145093.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 5,  1, 29, 31,  7, 14, 28, 38,  6, 37, 20,  0, 36,  3, 34, 22, 27,
        8, 23, 33, 11, 16, 12, 30, 17, 24, 15, 19,  2,  4, 10, 26, 32, 18,
       35, 13,  9, 21, 25]), 'cur_cost': 147397.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 7,  1, 29, 31,  5, 14, 28, 38,  6, 37, 20,  0, 36,  3, 34, 22, 27,
        8, 23, 33, 11, 16, 12, 30, 17, 24, 15, 19,  2,  4, 10, 26, 32, 18,
       35, 13,  9, 21, 25]), 'cur_cost': 147430.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 7,  5,  1, 29, 31, 14, 28, 38,  6, 37, 20,  0, 36,  3, 34, 22, 27,
        8, 23, 33, 11, 16, 12, 30, 17, 24, 15, 19,  2,  4, 10, 26, 32, 18,
       35, 13,  9, 21, 25]), 'cur_cost': 146556.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [33, 21, 34, 36, 12, 23, 25, 7, 5, 32, 18, 22, 0, 20, 6, 24, 35, 15, 4, 28, 1, 27, 30, 3, 13, 11, 37, 38, 9, 10, 14, 2, 29, 26, 31, 17, 16, 19, 8], 'cur_cost': 89204.0, 'intermediate_solutions': [{'tour': [9, 32, 16, 24, 22, 7, 21, 1, 29, 28, 6, 4, 11, 5, 12, 8, 10, 34, 0, 35, 25, 36, 33, 17, 19, 38, 30, 3, 15, 27, 13, 14, 2, 18, 26, 20, 23, 31, 37], 'cur_cost': 127842.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 7, 16, 0, 34, 10, 8, 12, 5, 11, 4, 6, 28, 29, 1, 21, 32, 22, 24, 35, 25, 36, 33, 17, 19, 38, 30, 3, 15, 27, 13, 14, 2, 18, 26, 20, 23, 31, 37], 'cur_cost': 127109.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 7, 16, 37, 24, 22, 32, 21, 1, 29, 28, 6, 4, 11, 5, 12, 8, 10, 34, 0, 35, 25, 36, 33, 17, 19, 38, 30, 3, 15, 27, 13, 14, 2, 18, 26, 20, 23, 31], 'cur_cost': 127047.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 15, 10, 14, 13, 12, 11, 7, 5, 8, 9, 6, 20, 25, 21, 22, 23, 24, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 17, 18, 19, 34, 33, 36, 38, 35, 32, 37], 'cur_cost': 36960.0, 'intermediate_solutions': [{'tour': [0, 10, 23, 11, 12, 14, 13, 7, 5, 8, 9, 6, 20, 25, 21, 22, 24, 1, 2, 4, 3, 26, 31, 27, 30, 28, 38, 17, 16, 18, 15, 19, 34, 33, 36, 29, 35, 32, 37], 'cur_cost': 57775.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 23, 11, 20, 6, 9, 8, 5, 7, 13, 14, 12, 25, 21, 22, 24, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37], 'cur_cost': 46716.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 8, 23, 11, 12, 14, 13, 7, 5, 9, 6, 20, 25, 21, 22, 24, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37], 'cur_cost': 38078.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 1, 6, 8, 9, 5, 7, 20, 25, 22, 23, 24, 3, 4, 2, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37, 12, 11, 10, 14, 13], 'cur_cost': 33857.0, 'intermediate_solutions': [{'tour': [0, 11, 23, 16, 17, 15, 19, 18, 22, 21, 33, 20, 24, 6, 8, 9, 5, 7, 12, 10, 14, 13, 32, 35, 38, 36, 25, 34, 37, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 44950.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 23, 16, 17, 15, 19, 18, 22, 21, 25, 20, 24, 6, 8, 9, 5, 7, 12, 10, 2, 1, 37, 34, 33, 36, 38, 35, 32, 13, 14, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 42675.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 23, 16, 17, 15, 19, 18, 22, 21, 25, 20, 24, 6, 8, 9, 5, 7, 12, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 11, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 31152.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [29, 3, 7, 20, 4, 1, 26, 27, 31, 25, 0, 9, 12, 8, 23, 24, 2, 11, 6, 5, 14, 36, 34, 33, 18, 21, 16, 17, 22, 35, 37, 10, 38, 19, 32, 13, 30, 28, 15], 'cur_cost': 99211.0, 'intermediate_solutions': [{'tour': [4, 25, 38, 7, 20, 0, 29, 9, 21, 37, 5, 26, 34, 16, 35, 12, 36, 6, 1, 2, 10, 27, 3, 13, 33, 8, 22, 23, 32, 19, 15, 30, 24, 17, 18, 31, 28, 11, 14], 'cur_cost': 124200.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 25, 38, 7, 20, 0, 29, 9, 21, 37, 5, 10, 34, 16, 35, 12, 36, 6, 1, 15, 19, 32, 23, 22, 8, 33, 13, 3, 27, 26, 2, 30, 24, 17, 18, 31, 28, 11, 14], 'cur_cost': 111966.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 25, 38, 7, 20, 0, 29, 9, 21, 37, 5, 10, 34, 16, 31, 35, 12, 36, 6, 1, 2, 26, 27, 3, 13, 33, 8, 22, 23, 32, 19, 15, 30, 24, 17, 18, 28, 11, 14], 'cur_cost': 118310.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [35, 20, 23, 2, 21, 5, 14, 10, 11, 34, 6, 8, 36, 33, 24, 38, 16, 4, 26, 22, 32, 18, 15, 0, 31, 30, 29, 3, 28, 7, 9, 13, 12, 25, 1, 17, 19, 37, 27], 'cur_cost': 99002.0, 'intermediate_solutions': [{'tour': [0, 12, 9, 3, 4, 1, 2, 26, 31, 27, 30, 11, 29, 20, 25, 21, 22, 23, 24, 6, 8, 5, 7, 28, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19], 'cur_cost': 57087.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 12, 9, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29, 20, 25, 21, 22, 23, 37, 34, 33, 36, 38, 35, 32, 13, 14, 10, 11, 7, 5, 8, 6, 24, 18, 16, 17, 15, 19], 'cur_cost': 36382.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 9, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29, 20, 25, 21, 22, 23, 24, 6, 8, 5, 11, 10, 14, 13, 32, 35, 38, 7, 36, 33, 34, 37, 18, 16, 17, 15, 19], 'cur_cost': 40167.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([17, 15, 22, 38, 32, 12, 27, 14, 30,  2, 18, 34, 16, 19, 35,  1, 26,
       13,  6, 21, 33,  4, 24,  8, 28,  7, 36, 10, 23,  9, 29, 31, 25, 20,
        3,  0, 11,  5, 37], dtype=int64), 'cur_cost': 128931.0, 'intermediate_solutions': [{'tour': array([ 3, 17, 36, 31,  6, 19,  7, 29,  9, 10,  8, 33, 15, 28, 30, 11, 32,
       22,  1, 37,  5, 24, 20,  0, 16, 21, 34, 12, 26, 18, 13,  2, 14,  4,
       27, 38, 25, 23, 35]), 'cur_cost': 153355.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([31,  3, 17, 36,  6, 19,  7, 29,  9, 10,  8, 33, 15, 28, 30, 11, 32,
       22,  1, 37,  5, 24, 20,  0, 16, 21, 34, 12, 26, 18, 13,  2, 14,  4,
       27, 38, 25, 23, 35]), 'cur_cost': 148098.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 6, 31,  3, 17, 36, 19,  7, 29,  9, 10,  8, 33, 15, 28, 30, 11, 32,
       22,  1, 37,  5, 24, 20,  0, 16, 21, 34, 12, 26, 18, 13,  2, 14,  4,
       27, 38, 25, 23, 35]), 'cur_cost': 145523.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([36, 31,  3, 17,  6, 19,  7, 29,  9, 10,  8, 33, 15, 28, 30, 11, 32,
       22,  1, 37,  5, 24, 20,  0, 16, 21, 34, 12, 26, 18, 13,  2, 14,  4,
       27, 38, 25, 23, 35]), 'cur_cost': 145993.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([36,  6, 31,  3, 17, 19,  7, 29,  9, 10,  8, 33, 15, 28, 30, 11, 32,
       22,  1, 37,  5, 24, 20,  0, 16, 21, 34, 12, 26, 18, 13,  2, 14,  4,
       27, 38, 25, 23, 35]), 'cur_cost': 137094.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 09:52:02,736 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:02,736 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:02,740 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=33857.000, 多样性=0.971
2025-08-05 09:52:02,740 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 09:52:02,740 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 09:52:02,740 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:02,741 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'slight_improvement', 'improvement_rate': 0.0023183093492452436, 'best_improvement': -0.0007685259081907127}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.013682331945270701}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.024626247605187557, 'recent_improvements': [0.002722144395569812, -0.09310239505201928, 0.05197463960594492], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 9, 'new_count': 9, 'count_change': 0, 'old_best_cost': 23763, 'new_best_cost': 23763, 'quality_improvement': 0.0, 'old_diversity': 0.7656695156695157, 'new_diversity': 0.7656695156695157, 'diversity_change': 0.0}, 'suggestions': ['改进缓慢，建议调整策略平衡或增加扰动强度', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:52:02,741 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 09:52:02,742 - __main__ - INFO - composite6_39 开始进化第 4 代
2025-08-05 09:52:02,742 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 09:52:02,742 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:02,743 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=33857.000, 多样性=0.971
2025-08-05 09:52:02,743 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:02,745 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.971
2025-08-05 09:52:02,745 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:02,747 - EliteExpert - INFO - 精英解分析完成: 精英解数量=9, 多样性=0.766
2025-08-05 09:52:02,749 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 09:52:02,749 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:02,749 - LandscapeExpert - INFO - 添加精英解数据: 9个精英解
2025-08-05 09:52:02,749 - LandscapeExpert - INFO - 数据提取成功: 19个路径, 19个适应度值
2025-08-05 09:52:02,789 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.316, 适应度梯度: -12486.021, 聚类评分: 0.000, 覆盖率: 0.111, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:02,790 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 09:52:02,790 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:02,790 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite6_39
2025-08-05 09:52:02,796 - visualization.landscape_visualizer - INFO - 插值约束: 84 个点被约束到最小值 23763.00
