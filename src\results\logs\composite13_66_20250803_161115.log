2025-08-03 16:11:15,938 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-08-03 16:11:15,939 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-03 16:11:15,940 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:11:15,943 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9945.000, 多样性=0.965
2025-08-03 16:11:15,948 - PathExpert - INFO - 开始路径结构分析
2025-08-03 16:11:15,958 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.965
2025-08-03 16:11:15,960 - EliteExpert - INFO - 开始精英解分析
2025-08-03 16:11:15,962 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/2)
2025-08-03 16:11:15,962 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-03 16:11:15,962 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-03 16:11:15,963 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-03 16:11:16,193 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -12507.660, 聚类评分: 0.000, 覆盖率: 0.001, 收敛趋势: 0.000, 多样性: 1.000
2025-08-03 16:11:16,193 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-03 16:11:16,193 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-03 16:11:16,268 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记
2025-08-03 16:11:16,636 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_1_20250803_161116.html
2025-08-03 16:11:16,680 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_1_20250803_161116.html
2025-08-03 16:11:16,683 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-03 16:11:16,684 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-03 16:11:16,685 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.7228秒
2025-08-03 16:11:16,686 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-03 16:11:16,687 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -12507.659999999998, 'local_optima_density': 0.1, 'gradient_variance': 2844716059.4244003, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.001, 'fitness_entropy': 0.9372305632161296, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -12507.660)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.001)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 2, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754208676.1939769, 'performance_metrics': {}}}
2025-08-03 16:11:16,689 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-03 16:11:16,689 - experts.strategy.enhanced_strategy_expert - INFO - 开始增强策略分配分析 (迭代 0)
2025-08-03 16:11:16,689 - experts.analysis.individual_state_analyzer - INFO - 开始分析种群状态 (迭代 0, 种群大小: 10)
2025-08-03 16:11:16,690 - experts.strategy.enhanced_strategy_expert - ERROR - 策略分配过程中发生错误: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:11:16,690 - experts.strategy.enhanced_strategy_expert - WARNING - 使用回退策略分配
2025-08-03 16:11:16,691 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-03 16:11:16,691 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:11:16,691 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-03 16:11:16,692 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-03 16:11:16,692 - __main__ - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:11:16,692 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-03 16:11:16,692 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2} (总数: 2, 保护比例: 0.20)
2025-08-03 16:11:16,693 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-03 16:11:16,693 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-03 16:11:16,693 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:11:16,695 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:11:16,695 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:11:16,868 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12401.0, 路径长度: 66
2025-08-03 16:11:16,868 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 21, 15, 14, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12401.0}
2025-08-03 16:11:16,869 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 12401.00)
2025-08-03 16:11:16,869 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-03 16:11:16,869 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:11:16,871 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:11:16,871 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 110062.0
2025-08-03 16:11:19,025 - ExploitationExpert - INFO - res_population_num: 1
2025-08-03 16:11:19,025 - ExploitationExpert - INFO - res_population_costs: [9819.0]
2025-08-03 16:11:19,025 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 42, 48, 43, 40,
       49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:11:19,028 - ExploitationExpert - INFO - populations_num: 10
2025-08-03 16:11:19,028 - ExploitationExpert - INFO - populations: [{'tour': [0, 21, 15, 14, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12401.0}, {'tour': array([30, 23, 45, 61, 54, 46, 59, 35,  8, 64,  6, 12, 57, 32, 27, 20, 56,
        3, 50, 17, 28, 37,  1, 11, 51, 26, 18,  9, 48, 47, 14, 31,  0, 13,
       34, 16, 43, 65,  4, 53,  5, 21, 55, 25, 40, 58, 29, 39, 15, 49, 19,
       52, 42, 22, 44, 33, 36, 60, 38, 63,  2, 41, 10,  7, 62, 24],
      dtype=int64), 'cur_cost': 110062.0}, {'tour': array([23, 16, 18, 12, 22, 15, 14, 20, 21, 13, 19, 17, 27, 37, 25, 26, 36,
       35, 28, 30, 34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,
        2,  6, 10,  0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 10014.0}, {'tour': array([26, 41, 13, 39, 22,  5, 62, 10, 44,  0, 42, 60, 16, 40,  6, 14,  3,
       63, 34, 53,  8,  9, 29, 17, 31, 52, 59, 48, 12,  2, 18, 54, 43, 20,
       19, 15, 61, 38, 25,  7, 55,  4, 51, 32, 30, 58, 28, 21, 27, 37, 33,
       49, 46, 47, 35, 24, 45, 65, 23, 64, 56, 11, 36, 50, 57,  1],
      dtype=int64), 'cur_cost': 105082.0}, {'tour': array([ 6, 39, 47, 20, 24, 60, 37, 57, 53, 32, 31, 18, 56, 40, 63,  7, 45,
       26, 48, 41, 14, 11, 23, 10, 51, 62, 38, 44, 30, 35, 17,  9, 15, 64,
       22, 50, 13, 21, 27, 34,  8, 16, 54, 33,  0, 36,  2, 29, 43,  3, 65,
       55, 42, 61, 52, 59, 25, 12,  4, 58,  1, 46, 19, 28, 49,  5],
      dtype=int64), 'cur_cost': 105621.0}, {'tour': array([32, 62, 56, 39, 13, 17, 28, 48,  6, 64, 65, 16, 43, 41, 35, 29, 52,
       33, 15, 63,  3, 30, 60, 18,  0, 10, 31, 42, 19, 59, 51, 38, 23, 50,
       24, 40, 61, 22, 55,  4,  2, 54, 49,  5,  1, 12,  8, 44, 20, 25, 53,
       36, 26, 14, 45, 57, 47, 27,  7,  9, 37, 11, 46, 34, 21, 58],
      dtype=int64), 'cur_cost': 109970.0}, {'tour': array([ 0, 47, 26, 44, 39, 42, 34, 15, 57,  3, 12, 35,  8, 14, 63, 46, 31,
       18, 29, 41, 10, 38, 64, 49, 51,  4, 24, 45, 28, 48, 62, 21, 11, 16,
       13, 59, 58, 37, 50, 43,  9, 36, 33,  6, 55,  5, 27,  1, 54,  7, 22,
       52, 30, 40, 32,  2, 61, 19, 60, 23, 25, 53, 56, 65, 17, 20],
      dtype=int64), 'cur_cost': 107932.0}, {'tour': array([20, 43,  8, 53, 33, 56, 31, 62, 14, 50, 40, 58, 55, 22, 29, 47, 54,
        1, 39, 26,  3,  9,  0, 37, 30, 61, 57,  4, 60,  5, 48, 28, 36, 15,
       52, 18, 23, 63, 27, 17, 19, 45, 13, 44,  2, 25, 34, 41, 64, 32, 24,
       12, 46, 59, 65,  6, 42, 49, 51, 35, 16, 10, 38,  7, 21, 11],
      dtype=int64), 'cur_cost': 102135.0}, {'tour': array([ 4, 64, 20, 46, 25, 62, 53, 10, 58, 47, 48, 15, 17, 11, 13,  5, 28,
        3, 34, 16, 55, 56, 42, 27, 12, 21, 49, 31, 18,  1, 36,  2, 50,  8,
       60, 38, 35, 22, 33,  9, 51, 29, 39, 23, 37, 52, 43,  6, 63,  0, 30,
       65, 45, 61, 24, 32, 59, 26, 40, 19, 41, 14,  7, 54, 44, 57],
      dtype=int64), 'cur_cost': 114414.0}, {'tour': array([ 6, 60, 64, 14, 12, 37, 52, 61,  0, 45, 27,  4, 53, 36, 21, 39,  7,
       20, 40, 35, 19, 10, 17, 34, 46, 54, 16,  5,  1, 55, 26,  2, 15, 63,
       23, 25, 65, 24, 57, 29, 11,  8, 51,  3, 49, 32, 59, 33, 41, 31, 44,
       56,  9, 48, 28, 42, 47, 50, 13, 38, 18, 43, 62, 22, 30, 58],
      dtype=int64), 'cur_cost': 115986.0}]
2025-08-03 16:11:19,033 - ExploitationExpert - INFO - 局部搜索耗时: 2.16秒
2025-08-03 16:11:19,033 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-03 16:11:19,034 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([30, 23, 45, 61, 54, 46, 59, 35,  8, 64,  6, 12, 57, 32, 27, 20, 56,
        3, 50, 17, 28, 37,  1, 11, 51, 26, 18,  9, 48, 47, 14, 31,  0, 13,
       34, 16, 43, 65,  4, 53,  5, 21, 55, 25, 40, 58, 29, 39, 15, 49, 19,
       52, 42, 22, 44, 33, 36, 60, 38, 63,  2, 41, 10,  7, 62, 24],
      dtype=int64), 'cur_cost': 110062.0}
2025-08-03 16:11:19,034 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 110062.00)
2025-08-03 16:11:19,034 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-03 16:11:19,035 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-03 16:11:19,035 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:11:19,041 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:11:19,041 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:11:19,041 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12889.0, 路径长度: 66
2025-08-03 16:11:19,042 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 2, 13, 3, 7, 1, 11, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12889.0}
2025-08-03 16:11:19,042 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 12889.00)
2025-08-03 16:11:19,043 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-03 16:11:19,043 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-03 16:11:19,043 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:11:19,054 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:11:19,056 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:11:19,056 - ExplorationExpert - INFO - 探索路径生成完成，成本: 51024.0, 路径长度: 66
2025-08-03 16:11:19,056 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [38, 50, 44, 21, 36, 33, 25, 11, 56, 57, 64, 58, 54, 60, 4, 15, 20, 22, 9, 10, 23, 29, 34, 14, 3, 12, 13, 27, 30, 6, 62, 18, 17, 26, 37, 31, 1, 5, 52, 39, 42, 40, 49, 43, 48, 45, 16, 28, 35, 47, 46, 32, 8, 7, 2, 0, 65, 59, 61, 53, 55, 19, 24, 41, 51, 63], 'cur_cost': 51024.0}
2025-08-03 16:11:19,056 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 51024.00)
2025-08-03 16:11:19,057 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-03 16:11:19,057 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:11:19,057 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:11:19,058 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 103914.0
2025-08-03 16:11:21,279 - ExploitationExpert - INFO - res_population_num: 2
2025-08-03 16:11:21,280 - ExploitationExpert - INFO - res_population_costs: [9819.0, 9539.0]
2025-08-03 16:11:21,281 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 42, 48, 43, 40,
       49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:11:21,286 - ExploitationExpert - INFO - populations_num: 10
2025-08-03 16:11:21,287 - ExploitationExpert - INFO - populations: [{'tour': [0, 21, 15, 14, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12401.0}, {'tour': array([30, 23, 45, 61, 54, 46, 59, 35,  8, 64,  6, 12, 57, 32, 27, 20, 56,
        3, 50, 17, 28, 37,  1, 11, 51, 26, 18,  9, 48, 47, 14, 31,  0, 13,
       34, 16, 43, 65,  4, 53,  5, 21, 55, 25, 40, 58, 29, 39, 15, 49, 19,
       52, 42, 22, 44, 33, 36, 60, 38, 63,  2, 41, 10,  7, 62, 24],
      dtype=int64), 'cur_cost': 110062.0}, {'tour': [0, 2, 13, 3, 7, 1, 11, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12889.0}, {'tour': [38, 50, 44, 21, 36, 33, 25, 11, 56, 57, 64, 58, 54, 60, 4, 15, 20, 22, 9, 10, 23, 29, 34, 14, 3, 12, 13, 27, 30, 6, 62, 18, 17, 26, 37, 31, 1, 5, 52, 39, 42, 40, 49, 43, 48, 45, 16, 28, 35, 47, 46, 32, 8, 7, 2, 0, 65, 59, 61, 53, 55, 19, 24, 41, 51, 63], 'cur_cost': 51024.0}, {'tour': array([55, 50, 19, 24, 44, 10,  3, 47, 20,  0, 45, 11, 17, 64, 15,  2, 58,
       54, 43, 28, 42, 26, 63, 23, 27, 41,  8, 36, 62,  4, 38, 29, 34, 22,
       21, 48, 59, 61, 12, 13,  5, 30, 25, 56, 35, 16,  7, 60, 40, 37, 14,
       65, 53, 57, 33, 32,  6, 31, 39, 49,  1,  9, 18, 51, 46, 52],
      dtype=int64), 'cur_cost': 103914.0}, {'tour': array([32, 62, 56, 39, 13, 17, 28, 48,  6, 64, 65, 16, 43, 41, 35, 29, 52,
       33, 15, 63,  3, 30, 60, 18,  0, 10, 31, 42, 19, 59, 51, 38, 23, 50,
       24, 40, 61, 22, 55,  4,  2, 54, 49,  5,  1, 12,  8, 44, 20, 25, 53,
       36, 26, 14, 45, 57, 47, 27,  7,  9, 37, 11, 46, 34, 21, 58],
      dtype=int64), 'cur_cost': 109970.0}, {'tour': array([ 0, 47, 26, 44, 39, 42, 34, 15, 57,  3, 12, 35,  8, 14, 63, 46, 31,
       18, 29, 41, 10, 38, 64, 49, 51,  4, 24, 45, 28, 48, 62, 21, 11, 16,
       13, 59, 58, 37, 50, 43,  9, 36, 33,  6, 55,  5, 27,  1, 54,  7, 22,
       52, 30, 40, 32,  2, 61, 19, 60, 23, 25, 53, 56, 65, 17, 20],
      dtype=int64), 'cur_cost': 107932.0}, {'tour': array([20, 43,  8, 53, 33, 56, 31, 62, 14, 50, 40, 58, 55, 22, 29, 47, 54,
        1, 39, 26,  3,  9,  0, 37, 30, 61, 57,  4, 60,  5, 48, 28, 36, 15,
       52, 18, 23, 63, 27, 17, 19, 45, 13, 44,  2, 25, 34, 41, 64, 32, 24,
       12, 46, 59, 65,  6, 42, 49, 51, 35, 16, 10, 38,  7, 21, 11],
      dtype=int64), 'cur_cost': 102135.0}, {'tour': array([ 4, 64, 20, 46, 25, 62, 53, 10, 58, 47, 48, 15, 17, 11, 13,  5, 28,
        3, 34, 16, 55, 56, 42, 27, 12, 21, 49, 31, 18,  1, 36,  2, 50,  8,
       60, 38, 35, 22, 33,  9, 51, 29, 39, 23, 37, 52, 43,  6, 63,  0, 30,
       65, 45, 61, 24, 32, 59, 26, 40, 19, 41, 14,  7, 54, 44, 57],
      dtype=int64), 'cur_cost': 114414.0}, {'tour': array([ 6, 60, 64, 14, 12, 37, 52, 61,  0, 45, 27,  4, 53, 36, 21, 39,  7,
       20, 40, 35, 19, 10, 17, 34, 46, 54, 16,  5,  1, 55, 26,  2, 15, 63,
       23, 25, 65, 24, 57, 29, 11,  8, 51,  3, 49, 32, 59, 33, 41, 31, 44,
       56,  9, 48, 28, 42, 47, 50, 13, 38, 18, 43, 62, 22, 30, 58],
      dtype=int64), 'cur_cost': 115986.0}]
2025-08-03 16:11:21,294 - ExploitationExpert - INFO - 局部搜索耗时: 2.24秒
2025-08-03 16:11:21,295 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-03 16:11:21,295 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([55, 50, 19, 24, 44, 10,  3, 47, 20,  0, 45, 11, 17, 64, 15,  2, 58,
       54, 43, 28, 42, 26, 63, 23, 27, 41,  8, 36, 62,  4, 38, 29, 34, 22,
       21, 48, 59, 61, 12, 13,  5, 30, 25, 56, 35, 16,  7, 60, 40, 37, 14,
       65, 53, 57, 33, 32,  6, 31, 39, 49,  1,  9, 18, 51, 46, 52],
      dtype=int64), 'cur_cost': 103914.0}
2025-08-03 16:11:21,296 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 103914.00)
2025-08-03 16:11:21,296 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-03 16:11:21,296 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-03 16:11:21,296 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:11:21,301 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:11:21,301 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:11:21,302 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14761.0, 路径长度: 66
2025-08-03 16:11:21,302 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 22, 8, 21, 20, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14761.0}
2025-08-03 16:11:21,302 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 14761.00)
2025-08-03 16:11:21,303 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-03 16:11:21,303 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-03 16:11:21,303 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:11:21,306 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:11:21,307 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:11:21,307 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12679.0, 路径长度: 66
2025-08-03 16:11:21,307 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 7, 14, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12679.0}
2025-08-03 16:11:21,308 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 12679.00)
2025-08-03 16:11:21,308 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-03 16:11:21,308 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:11:21,308 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:11:21,309 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 108385.0
2025-08-03 16:11:21,988 - ExploitationExpert - INFO - res_population_num: 2
2025-08-03 16:11:21,988 - ExploitationExpert - INFO - res_population_costs: [9819.0, 9539.0]
2025-08-03 16:11:21,988 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 42, 48, 43, 40,
       49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:11:21,990 - ExploitationExpert - INFO - populations_num: 10
2025-08-03 16:11:21,990 - ExploitationExpert - INFO - populations: [{'tour': [0, 21, 15, 14, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12401.0}, {'tour': array([30, 23, 45, 61, 54, 46, 59, 35,  8, 64,  6, 12, 57, 32, 27, 20, 56,
        3, 50, 17, 28, 37,  1, 11, 51, 26, 18,  9, 48, 47, 14, 31,  0, 13,
       34, 16, 43, 65,  4, 53,  5, 21, 55, 25, 40, 58, 29, 39, 15, 49, 19,
       52, 42, 22, 44, 33, 36, 60, 38, 63,  2, 41, 10,  7, 62, 24],
      dtype=int64), 'cur_cost': 110062.0}, {'tour': [0, 2, 13, 3, 7, 1, 11, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12889.0}, {'tour': [38, 50, 44, 21, 36, 33, 25, 11, 56, 57, 64, 58, 54, 60, 4, 15, 20, 22, 9, 10, 23, 29, 34, 14, 3, 12, 13, 27, 30, 6, 62, 18, 17, 26, 37, 31, 1, 5, 52, 39, 42, 40, 49, 43, 48, 45, 16, 28, 35, 47, 46, 32, 8, 7, 2, 0, 65, 59, 61, 53, 55, 19, 24, 41, 51, 63], 'cur_cost': 51024.0}, {'tour': array([55, 50, 19, 24, 44, 10,  3, 47, 20,  0, 45, 11, 17, 64, 15,  2, 58,
       54, 43, 28, 42, 26, 63, 23, 27, 41,  8, 36, 62,  4, 38, 29, 34, 22,
       21, 48, 59, 61, 12, 13,  5, 30, 25, 56, 35, 16,  7, 60, 40, 37, 14,
       65, 53, 57, 33, 32,  6, 31, 39, 49,  1,  9, 18, 51, 46, 52],
      dtype=int64), 'cur_cost': 103914.0}, {'tour': [0, 22, 8, 21, 20, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14761.0}, {'tour': [0, 7, 14, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12679.0}, {'tour': array([ 6, 31, 54, 30, 49, 64, 27, 47, 41, 26, 35,  8, 65, 52,  5, 18, 55,
       16, 63, 59, 21, 10, 50, 11, 56, 17,  3, 25,  7, 43, 37, 61, 32, 23,
       44, 29,  9,  1, 45, 38,  2, 24, 42, 20, 22, 15, 12, 36, 14, 39, 46,
       62, 53, 40, 19, 58, 60, 33, 57, 48, 51,  4, 13, 34,  0, 28],
      dtype=int64), 'cur_cost': 108385.0}, {'tour': array([ 4, 64, 20, 46, 25, 62, 53, 10, 58, 47, 48, 15, 17, 11, 13,  5, 28,
        3, 34, 16, 55, 56, 42, 27, 12, 21, 49, 31, 18,  1, 36,  2, 50,  8,
       60, 38, 35, 22, 33,  9, 51, 29, 39, 23, 37, 52, 43,  6, 63,  0, 30,
       65, 45, 61, 24, 32, 59, 26, 40, 19, 41, 14,  7, 54, 44, 57],
      dtype=int64), 'cur_cost': 114414.0}, {'tour': array([ 6, 60, 64, 14, 12, 37, 52, 61,  0, 45, 27,  4, 53, 36, 21, 39,  7,
       20, 40, 35, 19, 10, 17, 34, 46, 54, 16,  5,  1, 55, 26,  2, 15, 63,
       23, 25, 65, 24, 57, 29, 11,  8, 51,  3, 49, 32, 59, 33, 41, 31, 44,
       56,  9, 48, 28, 42, 47, 50, 13, 38, 18, 43, 62, 22, 30, 58],
      dtype=int64), 'cur_cost': 115986.0}]
2025-08-03 16:11:21,994 - ExploitationExpert - INFO - 局部搜索耗时: 0.69秒
2025-08-03 16:11:21,994 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-03 16:11:21,995 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([ 6, 31, 54, 30, 49, 64, 27, 47, 41, 26, 35,  8, 65, 52,  5, 18, 55,
       16, 63, 59, 21, 10, 50, 11, 56, 17,  3, 25,  7, 43, 37, 61, 32, 23,
       44, 29,  9,  1, 45, 38,  2, 24, 42, 20, 22, 15, 12, 36, 14, 39, 46,
       62, 53, 40, 19, 58, 60, 33, 57, 48, 51,  4, 13, 34,  0, 28],
      dtype=int64), 'cur_cost': 108385.0}
2025-08-03 16:11:21,995 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 108385.00)
2025-08-03 16:11:21,996 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-03 16:11:21,996 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-03 16:11:21,996 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:11:21,999 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:11:22,000 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:11:22,001 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12384.0, 路径长度: 66
2025-08-03 16:11:22,001 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 1, 20, 12, 22, 23, 16, 18, 17, 15, 14, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12384.0}
2025-08-03 16:11:22,002 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 12384.00)
2025-08-03 16:11:22,002 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-03 16:11:22,002 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-03 16:11:22,002 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:11:22,010 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:11:22,011 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:11:22,011 - ExplorationExpert - INFO - 探索路径生成完成，成本: 52032.0, 路径长度: 66
2025-08-03 16:11:22,011 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [19, 30, 11, 61, 56, 60, 59, 65, 55, 57, 22, 34, 3, 15, 4, 9, 64, 53, 16, 23, 24, 7, 58, 20, 13, 2, 36, 37, 5, 18, 32, 26, 29, 25, 28, 17, 8, 6, 1, 10, 33, 31, 47, 41, 43, 38, 46, 51, 21, 0, 12, 48, 39, 50, 40, 42, 14, 49, 44, 45, 62, 52, 63, 54, 27, 35], 'cur_cost': 52032.0}
2025-08-03 16:11:22,012 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 52032.00)
2025-08-03 16:11:22,012 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-03 16:11:22,013 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-03 16:11:22,016 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 15, 14, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12401.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([30, 23, 45, 61, 54, 46, 59, 35,  8, 64,  6, 12, 57, 32, 27, 20, 56,
        3, 50, 17, 28, 37,  1, 11, 51, 26, 18,  9, 48, 47, 14, 31,  0, 13,
       34, 16, 43, 65,  4, 53,  5, 21, 55, 25, 40, 58, 29, 39, 15, 49, 19,
       52, 42, 22, 44, 33, 36, 60, 38, 63,  2, 41, 10,  7, 62, 24],
      dtype=int64), 'cur_cost': 110062.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 13, 3, 7, 1, 11, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12889.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [38, 50, 44, 21, 36, 33, 25, 11, 56, 57, 64, 58, 54, 60, 4, 15, 20, 22, 9, 10, 23, 29, 34, 14, 3, 12, 13, 27, 30, 6, 62, 18, 17, 26, 37, 31, 1, 5, 52, 39, 42, 40, 49, 43, 48, 45, 16, 28, 35, 47, 46, 32, 8, 7, 2, 0, 65, 59, 61, 53, 55, 19, 24, 41, 51, 63], 'cur_cost': 51024.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([55, 50, 19, 24, 44, 10,  3, 47, 20,  0, 45, 11, 17, 64, 15,  2, 58,
       54, 43, 28, 42, 26, 63, 23, 27, 41,  8, 36, 62,  4, 38, 29, 34, 22,
       21, 48, 59, 61, 12, 13,  5, 30, 25, 56, 35, 16,  7, 60, 40, 37, 14,
       65, 53, 57, 33, 32,  6, 31, 39, 49,  1,  9, 18, 51, 46, 52],
      dtype=int64), 'cur_cost': 103914.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 22, 8, 21, 20, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14761.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 14, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12679.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 6, 31, 54, 30, 49, 64, 27, 47, 41, 26, 35,  8, 65, 52,  5, 18, 55,
       16, 63, 59, 21, 10, 50, 11, 56, 17,  3, 25,  7, 43, 37, 61, 32, 23,
       44, 29,  9,  1, 45, 38,  2, 24, 42, 20, 22, 15, 12, 36, 14, 39, 46,
       62, 53, 40, 19, 58, 60, 33, 57, 48, 51,  4, 13, 34,  0, 28],
      dtype=int64), 'cur_cost': 108385.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 20, 12, 22, 23, 16, 18, 17, 15, 14, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12384.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [19, 30, 11, 61, 56, 60, 59, 65, 55, 57, 22, 34, 3, 15, 4, 9, 64, 53, 16, 23, 24, 7, 58, 20, 13, 2, 36, 37, 5, 18, 32, 26, 29, 25, 28, 17, 8, 6, 1, 10, 33, 31, 47, 41, 43, 38, 46, 51, 21, 0, 12, 48, 39, 50, 40, 42, 14, 49, 44, 45, 62, 52, 63, 54, 27, 35], 'cur_cost': 52032.0}}]
2025-08-03 16:11:22,020 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-03 16:11:22,021 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:11:22,026 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12384.000, 多样性=0.931
2025-08-03 16:11:22,027 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-03 16:11:22,028 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-03 16:11:22,028 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-03 16:11:22,029 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 2, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.06193580925130566, 'best_improvement': -0.24524886877828053}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.03592605510987069}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 9539.0, 'new_best_cost': 9539.0, 'quality_improvement': 0.0, 'old_diversity': 0.9696969696969697, 'new_diversity': 0.9696969696969697, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-03 16:11:22,030 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-03 16:11:22,030 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-08-03 16:11:22,031 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-03 16:11:22,032 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:11:22,033 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12384.000, 多样性=0.931
2025-08-03 16:11:22,034 - PathExpert - INFO - 开始路径结构分析
2025-08-03 16:11:22,038 - PathExpert - INFO - 路径结构分析完成: 公共边数量=14, 路径相似性=0.931
2025-08-03 16:11:22,040 - EliteExpert - INFO - 开始精英解分析
2025-08-03 16:11:22,042 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.970
2025-08-03 16:11:22,044 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/2)
2025-08-03 16:11:22,045 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-03 16:11:22,045 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-03 16:11:22,045 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-03 16:11:22,076 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.083, 适应度梯度: -6527.150, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 1.000
2025-08-03 16:11:22,077 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-03 16:11:22,077 - LandscapeExpert - INFO - 提取到 2 个精英解
2025-08-03 16:11:22,097 - visualization.landscape_visualizer - INFO - 已添加 2 个精英解标记
2025-08-03 16:11:22,177 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_2_20250803_161122.html
2025-08-03 16:11:22,233 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_2_20250803_161122.html
2025-08-03 16:11:22,234 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 2
2025-08-03 16:11:22,234 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-03 16:11:22,234 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1902秒
2025-08-03 16:11:22,235 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.08333333333333333, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -6527.1500000000015, 'local_optima_density': 0.08333333333333333, 'gradient_variance': 840732586.8741668, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0022, 'fitness_entropy': 0.8734790278701756, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -6527.150)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 1, 'total': 2, 'progress': 0.5}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754208682.0772395, 'performance_metrics': {}}}
2025-08-03 16:11:22,236 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-03 16:11:22,236 - experts.strategy.enhanced_strategy_expert - INFO - 开始增强策略分配分析 (迭代 1)
2025-08-03 16:11:22,237 - experts.analysis.individual_state_analyzer - INFO - 开始分析种群状态 (迭代 1, 种群大小: 10)
2025-08-03 16:11:22,237 - experts.strategy.enhanced_strategy_expert - ERROR - 策略分配过程中发生错误: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:11:22,237 - experts.strategy.enhanced_strategy_expert - WARNING - 使用回退策略分配
2025-08-03 16:11:22,238 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-03 16:11:22,238 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:11:22,238 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-03 16:11:22,239 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-03 16:11:22,239 - __main__ - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:11:22,239 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-03 16:11:22,240 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 0} (总数: 2, 保护比例: 0.20)
2025-08-03 16:11:22,240 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-03 16:11:22,240 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-03 16:11:22,240 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:11:22,247 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:11:22,250 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:11:22,252 - ExplorationExpert - INFO - 探索路径生成完成，成本: 67363.0, 路径长度: 66
2025-08-03 16:11:22,252 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [61, 11, 56, 12, 28, 7, 4, 6, 8, 13, 30, 15, 17, 14, 18, 25, 1, 58, 52, 9, 64, 23, 26, 36, 22, 40, 35, 21, 31, 10, 16, 34, 47, 48, 33, 19, 20, 32, 42, 44, 37, 2, 3, 5, 59, 0, 57, 49, 41, 43, 51, 38, 27, 29, 39, 45, 60, 55, 62, 54, 65, 63, 53, 46, 50, 24], 'cur_cost': 67363.0}
2025-08-03 16:11:22,253 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 67363.00)
2025-08-03 16:11:22,253 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-03 16:11:22,253 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:11:22,253 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:11:22,254 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 114215.0
2025-08-03 16:11:22,322 - ExploitationExpert - INFO - res_population_num: 4
2025-08-03 16:11:22,322 - ExploitationExpert - INFO - res_population_costs: [9539.0, 9819.0, 9533, 9533]
2025-08-03 16:11:22,323 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 42, 48, 43, 40,
       49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:11:22,327 - ExploitationExpert - INFO - populations_num: 10
2025-08-03 16:11:22,327 - ExploitationExpert - INFO - populations: [{'tour': [61, 11, 56, 12, 28, 7, 4, 6, 8, 13, 30, 15, 17, 14, 18, 25, 1, 58, 52, 9, 64, 23, 26, 36, 22, 40, 35, 21, 31, 10, 16, 34, 47, 48, 33, 19, 20, 32, 42, 44, 37, 2, 3, 5, 59, 0, 57, 49, 41, 43, 51, 38, 27, 29, 39, 45, 60, 55, 62, 54, 65, 63, 53, 46, 50, 24], 'cur_cost': 67363.0}, {'tour': array([59,  5, 56,  7, 50, 64, 14,  6, 40,  4, 12, 63, 16,  3, 46, 57, 15,
        0, 52, 55, 51, 13, 28, 18, 32, 39, 30, 42, 34, 22, 26, 31, 20,  9,
       19, 54, 49, 17, 33, 53, 38, 24, 29,  8, 10,  2, 11, 21, 62, 37, 41,
       61, 45, 36,  1, 44, 60, 48, 65, 43, 23, 27, 35, 47, 25, 58],
      dtype=int64), 'cur_cost': 114215.0}, {'tour': [0, 2, 13, 3, 7, 1, 11, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12889.0}, {'tour': [38, 50, 44, 21, 36, 33, 25, 11, 56, 57, 64, 58, 54, 60, 4, 15, 20, 22, 9, 10, 23, 29, 34, 14, 3, 12, 13, 27, 30, 6, 62, 18, 17, 26, 37, 31, 1, 5, 52, 39, 42, 40, 49, 43, 48, 45, 16, 28, 35, 47, 46, 32, 8, 7, 2, 0, 65, 59, 61, 53, 55, 19, 24, 41, 51, 63], 'cur_cost': 51024.0}, {'tour': [55, 50, 19, 24, 44, 10, 3, 47, 20, 0, 45, 11, 17, 64, 15, 2, 58, 54, 43, 28, 42, 26, 63, 23, 27, 41, 8, 36, 62, 4, 38, 29, 34, 22, 21, 48, 59, 61, 12, 13, 5, 30, 25, 56, 35, 16, 7, 60, 40, 37, 14, 65, 53, 57, 33, 32, 6, 31, 39, 49, 1, 9, 18, 51, 46, 52], 'cur_cost': 103914.0}, {'tour': [0, 22, 8, 21, 20, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14761.0}, {'tour': [0, 7, 14, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12679.0}, {'tour': [6, 31, 54, 30, 49, 64, 27, 47, 41, 26, 35, 8, 65, 52, 5, 18, 55, 16, 63, 59, 21, 10, 50, 11, 56, 17, 3, 25, 7, 43, 37, 61, 32, 23, 44, 29, 9, 1, 45, 38, 2, 24, 42, 20, 22, 15, 12, 36, 14, 39, 46, 62, 53, 40, 19, 58, 60, 33, 57, 48, 51, 4, 13, 34, 0, 28], 'cur_cost': 108385.0}, {'tour': [0, 1, 20, 12, 22, 23, 16, 18, 17, 15, 14, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12384.0}, {'tour': [19, 30, 11, 61, 56, 60, 59, 65, 55, 57, 22, 34, 3, 15, 4, 9, 64, 53, 16, 23, 24, 7, 58, 20, 13, 2, 36, 37, 5, 18, 32, 26, 29, 25, 28, 17, 8, 6, 1, 10, 33, 31, 47, 41, 43, 38, 46, 51, 21, 0, 12, 48, 39, 50, 40, 42, 14, 49, 44, 45, 62, 52, 63, 54, 27, 35], 'cur_cost': 52032.0}]
2025-08-03 16:11:22,329 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-08-03 16:11:22,329 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-03 16:11:22,330 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([59,  5, 56,  7, 50, 64, 14,  6, 40,  4, 12, 63, 16,  3, 46, 57, 15,
        0, 52, 55, 51, 13, 28, 18, 32, 39, 30, 42, 34, 22, 26, 31, 20,  9,
       19, 54, 49, 17, 33, 53, 38, 24, 29,  8, 10,  2, 11, 21, 62, 37, 41,
       61, 45, 36,  1, 44, 60, 48, 65, 43, 23, 27, 35, 47, 25, 58],
      dtype=int64), 'cur_cost': 114215.0}
2025-08-03 16:11:22,330 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 114215.00)
2025-08-03 16:11:22,331 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-03 16:11:22,331 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-03 16:11:22,331 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:11:22,339 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:11:22,340 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:11:22,341 - ExplorationExpert - INFO - 探索路径生成完成，成本: 65451.0, 路径长度: 66
2025-08-03 16:11:22,341 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [28, 20, 15, 7, 61, 21, 22, 4, 1, 58, 56, 6, 57, 65, 17, 30, 37, 14, 0, 52, 13, 35, 31, 29, 9, 8, 53, 19, 40, 46, 43, 34, 26, 11, 16, 33, 49, 39, 18, 23, 47, 27, 5, 54, 10, 63, 60, 3, 59, 2, 62, 48, 42, 41, 45, 50, 12, 24, 36, 32, 38, 44, 51, 25, 55, 64], 'cur_cost': 65451.0}
2025-08-03 16:11:22,342 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 65451.00)
2025-08-03 16:11:22,342 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-03 16:11:22,342 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-03 16:11:22,342 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:11:22,344 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:11:22,344 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:11:22,345 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10429.0, 路径长度: 66
2025-08-03 16:11:22,345 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 6, 9, 8, 2, 4, 5, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10429.0}
2025-08-03 16:11:22,345 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 10429.00)
2025-08-03 16:11:22,346 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-03 16:11:22,346 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:11:22,346 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:11:22,347 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 108408.0
2025-08-03 16:11:22,429 - ExploitationExpert - INFO - res_population_num: 6
2025-08-03 16:11:22,430 - ExploitationExpert - INFO - res_population_costs: [9539.0, 9819.0, 9533, 9533, 9526, 9524]
2025-08-03 16:11:22,430 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 42, 48, 43, 40,
       49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:11:22,434 - ExploitationExpert - INFO - populations_num: 10
2025-08-03 16:11:22,435 - ExploitationExpert - INFO - populations: [{'tour': [61, 11, 56, 12, 28, 7, 4, 6, 8, 13, 30, 15, 17, 14, 18, 25, 1, 58, 52, 9, 64, 23, 26, 36, 22, 40, 35, 21, 31, 10, 16, 34, 47, 48, 33, 19, 20, 32, 42, 44, 37, 2, 3, 5, 59, 0, 57, 49, 41, 43, 51, 38, 27, 29, 39, 45, 60, 55, 62, 54, 65, 63, 53, 46, 50, 24], 'cur_cost': 67363.0}, {'tour': array([59,  5, 56,  7, 50, 64, 14,  6, 40,  4, 12, 63, 16,  3, 46, 57, 15,
        0, 52, 55, 51, 13, 28, 18, 32, 39, 30, 42, 34, 22, 26, 31, 20,  9,
       19, 54, 49, 17, 33, 53, 38, 24, 29,  8, 10,  2, 11, 21, 62, 37, 41,
       61, 45, 36,  1, 44, 60, 48, 65, 43, 23, 27, 35, 47, 25, 58],
      dtype=int64), 'cur_cost': 114215.0}, {'tour': [28, 20, 15, 7, 61, 21, 22, 4, 1, 58, 56, 6, 57, 65, 17, 30, 37, 14, 0, 52, 13, 35, 31, 29, 9, 8, 53, 19, 40, 46, 43, 34, 26, 11, 16, 33, 49, 39, 18, 23, 47, 27, 5, 54, 10, 63, 60, 3, 59, 2, 62, 48, 42, 41, 45, 50, 12, 24, 36, 32, 38, 44, 51, 25, 55, 64], 'cur_cost': 65451.0}, {'tour': [0, 6, 9, 8, 2, 4, 5, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10429.0}, {'tour': array([41, 49,  9, 34, 11, 38, 56, 48, 64,  4, 59, 46, 23,  2, 47, 51, 55,
       39,  7, 58, 19, 30, 43, 54, 25, 14, 12, 27, 62,  3, 31, 32, 28, 60,
       65, 21, 26, 13, 20, 29, 42, 15, 50, 52, 10, 63,  6, 36, 35, 57, 61,
       16,  5, 18, 24,  0,  1, 17, 53, 44,  8, 40, 37, 22, 45, 33],
      dtype=int64), 'cur_cost': 108408.0}, {'tour': [0, 22, 8, 21, 20, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14761.0}, {'tour': [0, 7, 14, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12679.0}, {'tour': [6, 31, 54, 30, 49, 64, 27, 47, 41, 26, 35, 8, 65, 52, 5, 18, 55, 16, 63, 59, 21, 10, 50, 11, 56, 17, 3, 25, 7, 43, 37, 61, 32, 23, 44, 29, 9, 1, 45, 38, 2, 24, 42, 20, 22, 15, 12, 36, 14, 39, 46, 62, 53, 40, 19, 58, 60, 33, 57, 48, 51, 4, 13, 34, 0, 28], 'cur_cost': 108385.0}, {'tour': [0, 1, 20, 12, 22, 23, 16, 18, 17, 15, 14, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12384.0}, {'tour': [19, 30, 11, 61, 56, 60, 59, 65, 55, 57, 22, 34, 3, 15, 4, 9, 64, 53, 16, 23, 24, 7, 58, 20, 13, 2, 36, 37, 5, 18, 32, 26, 29, 25, 28, 17, 8, 6, 1, 10, 33, 31, 47, 41, 43, 38, 46, 51, 21, 0, 12, 48, 39, 50, 40, 42, 14, 49, 44, 45, 62, 52, 63, 54, 27, 35], 'cur_cost': 52032.0}]
2025-08-03 16:11:22,437 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-03 16:11:22,437 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-03 16:11:22,438 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([41, 49,  9, 34, 11, 38, 56, 48, 64,  4, 59, 46, 23,  2, 47, 51, 55,
       39,  7, 58, 19, 30, 43, 54, 25, 14, 12, 27, 62,  3, 31, 32, 28, 60,
       65, 21, 26, 13, 20, 29, 42, 15, 50, 52, 10, 63,  6, 36, 35, 57, 61,
       16,  5, 18, 24,  0,  1, 17, 53, 44,  8, 40, 37, 22, 45, 33],
      dtype=int64), 'cur_cost': 108408.0}
2025-08-03 16:11:22,438 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 108408.00)
2025-08-03 16:11:22,439 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-03 16:11:22,439 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-03 16:11:22,439 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:11:22,441 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:11:22,442 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:11:22,442 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12757.0, 路径长度: 66
2025-08-03 16:11:22,442 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 6, 17, 8, 2, 4, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12757.0}
2025-08-03 16:11:22,443 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 12757.00)
2025-08-03 16:11:22,443 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-03 16:11:22,443 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-03 16:11:22,443 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:11:22,453 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:11:22,454 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:11:22,455 - ExplorationExpert - INFO - 探索路径生成完成，成本: 59438.0, 路径长度: 66
2025-08-03 16:11:22,455 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [14, 9, 64, 8, 60, 55, 22, 28, 13, 29, 15, 5, 17, 20, 21, 12, 33, 37, 40, 18, 30, 7, 56, 6, 0, 59, 61, 1, 63, 19, 3, 16, 24, 35, 11, 57, 2, 53, 65, 58, 10, 26, 31, 27, 34, 32, 43, 49, 45, 41, 51, 39, 46, 47, 23, 4, 62, 54, 48, 42, 44, 38, 36, 25, 50, 52], 'cur_cost': 59438.0}
2025-08-03 16:11:22,456 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 59438.00)
2025-08-03 16:11:22,456 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-03 16:11:22,456 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:11:22,457 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:11:22,457 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 107408.0
2025-08-03 16:11:22,541 - ExploitationExpert - INFO - res_population_num: 10
2025-08-03 16:11:22,541 - ExploitationExpert - INFO - res_population_costs: [9539.0, 9819.0, 9533, 9533, 9526, 9524, 9524, 9524, 9521, 9521]
2025-08-03 16:11:22,541 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 42, 48, 43, 40,
       49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:11:22,547 - ExploitationExpert - INFO - populations_num: 10
2025-08-03 16:11:22,548 - ExploitationExpert - INFO - populations: [{'tour': [61, 11, 56, 12, 28, 7, 4, 6, 8, 13, 30, 15, 17, 14, 18, 25, 1, 58, 52, 9, 64, 23, 26, 36, 22, 40, 35, 21, 31, 10, 16, 34, 47, 48, 33, 19, 20, 32, 42, 44, 37, 2, 3, 5, 59, 0, 57, 49, 41, 43, 51, 38, 27, 29, 39, 45, 60, 55, 62, 54, 65, 63, 53, 46, 50, 24], 'cur_cost': 67363.0}, {'tour': array([59,  5, 56,  7, 50, 64, 14,  6, 40,  4, 12, 63, 16,  3, 46, 57, 15,
        0, 52, 55, 51, 13, 28, 18, 32, 39, 30, 42, 34, 22, 26, 31, 20,  9,
       19, 54, 49, 17, 33, 53, 38, 24, 29,  8, 10,  2, 11, 21, 62, 37, 41,
       61, 45, 36,  1, 44, 60, 48, 65, 43, 23, 27, 35, 47, 25, 58],
      dtype=int64), 'cur_cost': 114215.0}, {'tour': [28, 20, 15, 7, 61, 21, 22, 4, 1, 58, 56, 6, 57, 65, 17, 30, 37, 14, 0, 52, 13, 35, 31, 29, 9, 8, 53, 19, 40, 46, 43, 34, 26, 11, 16, 33, 49, 39, 18, 23, 47, 27, 5, 54, 10, 63, 60, 3, 59, 2, 62, 48, 42, 41, 45, 50, 12, 24, 36, 32, 38, 44, 51, 25, 55, 64], 'cur_cost': 65451.0}, {'tour': [0, 6, 9, 8, 2, 4, 5, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10429.0}, {'tour': array([41, 49,  9, 34, 11, 38, 56, 48, 64,  4, 59, 46, 23,  2, 47, 51, 55,
       39,  7, 58, 19, 30, 43, 54, 25, 14, 12, 27, 62,  3, 31, 32, 28, 60,
       65, 21, 26, 13, 20, 29, 42, 15, 50, 52, 10, 63,  6, 36, 35, 57, 61,
       16,  5, 18, 24,  0,  1, 17, 53, 44,  8, 40, 37, 22, 45, 33],
      dtype=int64), 'cur_cost': 108408.0}, {'tour': [0, 6, 17, 8, 2, 4, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12757.0}, {'tour': [14, 9, 64, 8, 60, 55, 22, 28, 13, 29, 15, 5, 17, 20, 21, 12, 33, 37, 40, 18, 30, 7, 56, 6, 0, 59, 61, 1, 63, 19, 3, 16, 24, 35, 11, 57, 2, 53, 65, 58, 10, 26, 31, 27, 34, 32, 43, 49, 45, 41, 51, 39, 46, 47, 23, 4, 62, 54, 48, 42, 44, 38, 36, 25, 50, 52], 'cur_cost': 59438.0}, {'tour': array([33, 28, 29,  0, 27, 46, 15, 32, 44, 16, 20, 58, 26, 21,  9, 12,  8,
       41,  6, 22, 40, 61, 11, 38, 24, 25,  7, 36, 13, 52,  3, 35, 14,  4,
       34, 60, 19, 50, 23, 45, 56, 48, 30, 59, 37, 55,  1, 64, 63, 65,  2,
       62, 53, 47, 18, 39, 42, 10, 51, 43, 49, 57, 17, 54, 31,  5],
      dtype=int64), 'cur_cost': 107408.0}, {'tour': [0, 1, 20, 12, 22, 23, 16, 18, 17, 15, 14, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12384.0}, {'tour': [19, 30, 11, 61, 56, 60, 59, 65, 55, 57, 22, 34, 3, 15, 4, 9, 64, 53, 16, 23, 24, 7, 58, 20, 13, 2, 36, 37, 5, 18, 32, 26, 29, 25, 28, 17, 8, 6, 1, 10, 33, 31, 47, 41, 43, 38, 46, 51, 21, 0, 12, 48, 39, 50, 40, 42, 14, 49, 44, 45, 62, 52, 63, 54, 27, 35], 'cur_cost': 52032.0}]
2025-08-03 16:11:22,556 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 16:11:22,556 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-03 16:11:22,557 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([33, 28, 29,  0, 27, 46, 15, 32, 44, 16, 20, 58, 26, 21,  9, 12,  8,
       41,  6, 22, 40, 61, 11, 38, 24, 25,  7, 36, 13, 52,  3, 35, 14,  4,
       34, 60, 19, 50, 23, 45, 56, 48, 30, 59, 37, 55,  1, 64, 63, 65,  2,
       62, 53, 47, 18, 39, 42, 10, 51, 43, 49, 57, 17, 54, 31,  5],
      dtype=int64), 'cur_cost': 107408.0}
2025-08-03 16:11:22,557 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 107408.00)
2025-08-03 16:11:22,558 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-03 16:11:22,558 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-03 16:11:22,558 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:11:22,562 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:11:22,563 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:11:22,563 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12365.0, 路径长度: 66
2025-08-03 16:11:22,563 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 5, 8, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12365.0}
2025-08-03 16:11:22,564 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 12365.00)
2025-08-03 16:11:22,564 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-03 16:11:22,564 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-03 16:11:22,564 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:11:22,567 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:11:22,568 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:11:22,568 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12675.0, 路径长度: 66
2025-08-03 16:11:22,568 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 11, 15, 1, 7, 3, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12675.0}
2025-08-03 16:11:22,569 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 12675.00)
2025-08-03 16:11:22,569 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-03 16:11:22,569 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-03 16:11:22,570 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [61, 11, 56, 12, 28, 7, 4, 6, 8, 13, 30, 15, 17, 14, 18, 25, 1, 58, 52, 9, 64, 23, 26, 36, 22, 40, 35, 21, 31, 10, 16, 34, 47, 48, 33, 19, 20, 32, 42, 44, 37, 2, 3, 5, 59, 0, 57, 49, 41, 43, 51, 38, 27, 29, 39, 45, 60, 55, 62, 54, 65, 63, 53, 46, 50, 24], 'cur_cost': 67363.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([59,  5, 56,  7, 50, 64, 14,  6, 40,  4, 12, 63, 16,  3, 46, 57, 15,
        0, 52, 55, 51, 13, 28, 18, 32, 39, 30, 42, 34, 22, 26, 31, 20,  9,
       19, 54, 49, 17, 33, 53, 38, 24, 29,  8, 10,  2, 11, 21, 62, 37, 41,
       61, 45, 36,  1, 44, 60, 48, 65, 43, 23, 27, 35, 47, 25, 58],
      dtype=int64), 'cur_cost': 114215.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [28, 20, 15, 7, 61, 21, 22, 4, 1, 58, 56, 6, 57, 65, 17, 30, 37, 14, 0, 52, 13, 35, 31, 29, 9, 8, 53, 19, 40, 46, 43, 34, 26, 11, 16, 33, 49, 39, 18, 23, 47, 27, 5, 54, 10, 63, 60, 3, 59, 2, 62, 48, 42, 41, 45, 50, 12, 24, 36, 32, 38, 44, 51, 25, 55, 64], 'cur_cost': 65451.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 9, 8, 2, 4, 5, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10429.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([41, 49,  9, 34, 11, 38, 56, 48, 64,  4, 59, 46, 23,  2, 47, 51, 55,
       39,  7, 58, 19, 30, 43, 54, 25, 14, 12, 27, 62,  3, 31, 32, 28, 60,
       65, 21, 26, 13, 20, 29, 42, 15, 50, 52, 10, 63,  6, 36, 35, 57, 61,
       16,  5, 18, 24,  0,  1, 17, 53, 44,  8, 40, 37, 22, 45, 33],
      dtype=int64), 'cur_cost': 108408.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 17, 8, 2, 4, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12757.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [14, 9, 64, 8, 60, 55, 22, 28, 13, 29, 15, 5, 17, 20, 21, 12, 33, 37, 40, 18, 30, 7, 56, 6, 0, 59, 61, 1, 63, 19, 3, 16, 24, 35, 11, 57, 2, 53, 65, 58, 10, 26, 31, 27, 34, 32, 43, 49, 45, 41, 51, 39, 46, 47, 23, 4, 62, 54, 48, 42, 44, 38, 36, 25, 50, 52], 'cur_cost': 59438.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([33, 28, 29,  0, 27, 46, 15, 32, 44, 16, 20, 58, 26, 21,  9, 12,  8,
       41,  6, 22, 40, 61, 11, 38, 24, 25,  7, 36, 13, 52,  3, 35, 14,  4,
       34, 60, 19, 50, 23, 45, 56, 48, 30, 59, 37, 55,  1, 64, 63, 65,  2,
       62, 53, 47, 18, 39, 42, 10, 51, 43, 49, 57, 17, 54, 31,  5],
      dtype=int64), 'cur_cost': 107408.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 8, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12365.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 15, 1, 7, 3, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12675.0}}]
2025-08-03 16:11:22,573 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-03 16:11:22,573 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:11:22,577 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10429.000, 多样性=0.939
2025-08-03 16:11:22,577 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-03 16:11:22,577 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-03 16:11:22,577 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-03 16:11:22,579 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 1, 'total_iterations': 2, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.014304004178146418, 'best_improvement': 0.15786498708010335}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.008683068017366073}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 10, 'new_count': 10, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.6693602693602694, 'new_diversity': 0.6693602693602694, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-03 16:11:22,582 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-03 16:11:22,625 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_solution.json
2025-08-03 16:11:22,626 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_20250803_161122.solution
2025-08-03 16:11:22,626 - __main__ - INFO - 实例 composite13_66 处理完成
