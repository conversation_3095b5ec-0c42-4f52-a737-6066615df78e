# -*- coding: utf-8 -*-
"""
模拟LLM接口
用于测试LLM策略选择功能，无需真实的API密钥
"""

import json
import logging
import random
import time
from typing import Dict, List, Any

class MockLLMInterface:
    """模拟LLM接口，用于测试LLM策略选择功能"""
    
    def __init__(self, response_mode='intelligent', debug_mode=True):
        """
        初始化模拟LLM接口
        
        Args:
            response_mode: 响应模式 ('intelligent', 'random', 'fixed', 'error')
            debug_mode: 是否启用调试模式
        """
        self.response_mode = response_mode
        self.debug_mode = debug_mode
        self.logger = logging.getLogger(__name__)
        self.call_count = 0
        self.response_history = []
        
        # 策略类型映射
        self.strategy_types = [
            "STRONG_EXPLORATION",
            "INTELLIGENT_EXPLORATION", 
            "BALANCED_EXPLORATION",
            "MODERATE_EXPLOITATION",
            "INTENSIVE_EXPLOITATION"
        ]
        
        self.logger.info(f"模拟LLM接口初始化完成，响应模式: {response_mode}")
    
    def get_response(self, prompt: str, max_retries: int = 3, retry_delay: int = 2) -> str:
        """
        模拟LLM响应
        
        Args:
            prompt: 输入提示词
            max_retries: 最大重试次数（模拟用）
            retry_delay: 重试延迟（模拟用）
            
        Returns:
            模拟的LLM响应JSON字符串
        """
        self.call_count += 1
        
        # 模拟API调用延迟
        time.sleep(0.1)
        
        if self.debug_mode:
            self.logger.info(f"模拟LLM调用 #{self.call_count}")
            self.logger.debug(f"输入提示词长度: {len(prompt)} 字符")
        
        # 从提示词中提取个体数量
        individual_count = self._extract_individual_count(prompt)
        
        # 根据响应模式生成响应
        if self.response_mode == 'intelligent':
            response = self._generate_intelligent_response(prompt, individual_count)
        elif self.response_mode == 'random':
            response = self._generate_random_response(individual_count)
        elif self.response_mode == 'fixed':
            response = self._generate_fixed_response(individual_count)
        elif self.response_mode == 'error':
            response = self._generate_error_response()
        else:
            response = self._generate_intelligent_response(prompt, individual_count)
        
        # 记录响应历史
        self.response_history.append({
            'call_count': self.call_count,
            'prompt_length': len(prompt),
            'response': response,
            'timestamp': time.time()
        })
        
        if self.debug_mode:
            self.logger.info(f"生成响应长度: {len(response)} 字符")
        
        return response
    
    def _extract_individual_count(self, prompt: str) -> int:
        """从提示词中提取个体数量"""
        # 简单的启发式方法提取个体数量
        lines = prompt.split('\n')
        individual_count = 10  # 默认值改为10

        # 尝试从提示词中找到种群大小信息
        import re

        # 查找种群大小的模式
        population_patterns = [
            r'种群大小[：:]\s*(\d+)',
            r'population\s+size[：:]\s*(\d+)',
            r'(\d+)\s*个个体',
            r'(\d+)\s*individuals'
        ]

        for pattern in population_patterns:
            matches = re.findall(pattern, prompt, re.IGNORECASE)
            if matches:
                individual_count = int(matches[0])
                break

        # 如果没有找到，尝试查找个体ID的最大值
        if individual_count == 10:  # 仍然是默认值
            for line in lines:
                if 'individual' in line.lower() and 'id' in line.lower():
                    try:
                        # 查找类似 "Individual 0", "Individual 1" 等模式
                        matches = re.findall(r'individual[s]?\s*(\d+)', line.lower())
                        if matches:
                            max_id = max(int(m) for m in matches)
                            individual_count = max_id + 1
                            break
                    except:
                        pass

        return individual_count
    
    def _generate_intelligent_response(self, prompt: str, individual_count: int) -> str:
        """生成智能响应，基于提示词内容"""
        
        # 分析提示词中的景观特征
        landscape_analysis = self._analyze_prompt_landscape(prompt)
        
        # 生成策略分配
        strategy_assignments = []
        for i in range(individual_count):
            # 基于景观特征和个体ID智能分配策略
            strategy_type = self._intelligent_strategy_selection(i, individual_count, landscape_analysis)
            
            assignment = {
                "individual_id": i,
                "strategy_type": strategy_type,
                "confidence": round(random.uniform(0.7, 0.95), 2),
                "reasoning": self._generate_reasoning(strategy_type, landscape_analysis),
                "parameters": {
                    "intensity": round(random.uniform(0.3, 0.8), 2),
                    "priority": round(random.uniform(0.4, 0.9), 2)
                }
            }
            strategy_assignments.append(assignment)
        
        # 计算探索比例
        exploration_strategies = ["STRONG_EXPLORATION", "INTELLIGENT_EXPLORATION", "BALANCED_EXPLORATION"]
        exploration_count = sum(1 for a in strategy_assignments if a["strategy_type"] in exploration_strategies)
        exploration_ratio = exploration_count / individual_count
        
        response = {
            "strategy_assignments": strategy_assignments,
            "global_analysis": {
                "exploration_ratio": round(exploration_ratio, 2),
                "landscape_complexity": landscape_analysis.get("complexity", 0.5),
                "key_insights": [
                    f"基于景观复杂度 {landscape_analysis.get('complexity', 0.5):.2f} 的策略分配",
                    f"探索策略比例: {exploration_ratio:.1%}",
                    "平衡探索与利用以优化收敛性能"
                ],
                "reasoning": "基于当前景观特征和个体状态的智能策略分配"
            }
        }
        
        return json.dumps(response, indent=2, ensure_ascii=False)
    
    def _analyze_prompt_landscape(self, prompt: str) -> Dict[str, Any]:
        """分析提示词中的景观信息"""
        analysis = {
            "complexity": 0.5,
            "ruggedness": 0.5,
            "diversity": 0.5,
            "convergence": 0.5
        }
        
        # 简单的关键词分析
        prompt_lower = prompt.lower()
        
        if "high" in prompt_lower and "ruggedness" in prompt_lower:
            analysis["complexity"] = 0.8
            analysis["ruggedness"] = 0.8
        elif "low" in prompt_lower and "ruggedness" in prompt_lower:
            analysis["complexity"] = 0.3
            analysis["ruggedness"] = 0.3
        
        if "diversity" in prompt_lower:
            if "high" in prompt_lower:
                analysis["diversity"] = 0.8
            elif "low" in prompt_lower:
                analysis["diversity"] = 0.3
        
        return analysis
    
    def _intelligent_strategy_selection(self, individual_id: int, total_count: int, landscape: Dict) -> str:
        """智能策略选择逻辑"""
        
        # 基于个体位置的策略倾向
        position_ratio = individual_id / max(total_count - 1, 1)
        
        # 基于景观复杂度调整
        complexity = landscape.get("complexity", 0.5)
        
        if complexity > 0.7:  # 高复杂度景观
            if position_ratio < 0.7:
                return random.choice(["STRONG_EXPLORATION", "INTELLIGENT_EXPLORATION"])
            else:
                return "BALANCED_EXPLORATION"
        elif complexity < 0.3:  # 低复杂度景观
            if position_ratio < 0.3:
                return "BALANCED_EXPLORATION"
            else:
                return random.choice(["MODERATE_EXPLOITATION", "INTENSIVE_EXPLOITATION"])
        else:  # 中等复杂度
            if position_ratio < 0.4:
                return "INTELLIGENT_EXPLORATION"
            elif position_ratio < 0.7:
                return "BALANCED_EXPLORATION"
            else:
                return "MODERATE_EXPLOITATION"
    
    def _generate_reasoning(self, strategy_type: str, landscape: Dict) -> str:
        """生成策略选择推理"""
        complexity = landscape.get("complexity", 0.5)
        
        reasoning_templates = {
            "STRONG_EXPLORATION": f"景观复杂度 {complexity:.2f}，需要强探索以发现新区域",
            "INTELLIGENT_EXPLORATION": f"基于景观特征的智能探索策略",
            "BALANCED_EXPLORATION": f"平衡探索与利用，适应中等复杂度景观",
            "MODERATE_EXPLOITATION": f"景观相对平滑，适合适度利用策略",
            "INTENSIVE_EXPLOITATION": f"低复杂度景观，集中利用当前优势区域"
        }
        
        return reasoning_templates.get(strategy_type, "基于当前状态的策略选择")
    
    def _generate_random_response(self, individual_count: int) -> str:
        """生成随机响应"""
        strategy_assignments = []
        for i in range(individual_count):
            assignment = {
                "individual_id": i,
                "strategy_type": random.choice(self.strategy_types),
                "confidence": round(random.uniform(0.5, 0.9), 2),
                "reasoning": "随机策略分配",
                "parameters": {
                    "intensity": round(random.uniform(0.2, 0.8), 2),
                    "priority": round(random.uniform(0.3, 0.9), 2)
                }
            }
            strategy_assignments.append(assignment)
        
        response = {
            "strategy_assignments": strategy_assignments,
            "global_analysis": {
                "exploration_ratio": round(random.uniform(0.4, 0.8), 2),
                "key_insights": ["随机策略分配用于测试"],
                "reasoning": "随机模式响应"
            }
        }
        
        return json.dumps(response, indent=2, ensure_ascii=False)
    
    def _generate_fixed_response(self, individual_count: int) -> str:
        """生成固定响应"""
        strategy_assignments = []
        for i in range(individual_count):
            # 固定模式：前60%探索，后40%利用
            if i < individual_count * 0.6:
                strategy_type = "INTELLIGENT_EXPLORATION"
            else:
                strategy_type = "MODERATE_EXPLOITATION"
            
            assignment = {
                "individual_id": i,
                "strategy_type": strategy_type,
                "confidence": 0.8,
                "reasoning": "固定策略分配模式",
                "parameters": {
                    "intensity": 0.6,
                    "priority": 0.7
                }
            }
            strategy_assignments.append(assignment)
        
        response = {
            "strategy_assignments": strategy_assignments,
            "global_analysis": {
                "exploration_ratio": 0.6,
                "key_insights": ["固定60%探索，40%利用的策略分配"],
                "reasoning": "固定模式响应"
            }
        }
        
        return json.dumps(response, indent=2, ensure_ascii=False)
    
    def _generate_error_response(self) -> str:
        """生成错误响应（用于测试错误处理）"""
        return "ERROR: 模拟LLM调用失败"
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取调用统计信息"""
        return {
            "total_calls": self.call_count,
            "response_mode": self.response_mode,
            "average_response_time": 0.1,  # 模拟值
            "success_rate": 1.0 if self.response_mode != 'error' else 0.0
        }
