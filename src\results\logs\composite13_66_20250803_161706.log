2025-08-03 16:17:06,691 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-08-03 16:17:06,691 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-03 16:17:06,695 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:17:06,708 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=9890.000, 多样性=0.966
2025-08-03 16:17:06,713 - PathExpert - INFO - 开始路径结构分析
2025-08-03 16:17:06,725 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.966
2025-08-03 16:17:06,789 - EliteExpert - INFO - 开始精英解分析
2025-08-03 16:17:06,795 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/1)
2025-08-03 16:17:06,796 - LandscapeExpert - INFO - 使用直接传入的种群数据: 20个个体
2025-08-03 16:17:06,796 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-03 16:17:06,797 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-03 16:17:07,077 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.150, 适应度梯度: -7612.330, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 1.000
2025-08-03 16:17:07,078 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-03 16:17:07,078 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-03 16:17:07,148 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记
2025-08-03 16:17:07,485 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_1_20250803_161707.html
2025-08-03 16:17:07,529 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_1_20250803_161707.html
2025-08-03 16:17:07,529 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-03 16:17:07,529 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-03 16:17:07,529 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.7342秒
2025-08-03 16:17:07,530 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-03 16:17:07,531 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.15, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -7612.33, 'local_optima_density': 0.15, 'gradient_variance': 2888644603.4771004, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.002, 'fitness_entropy': 0.9416915491145067, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -7612.330)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 1, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754209027.0770853, 'performance_metrics': {}}}
2025-08-03 16:17:07,532 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-03 16:17:07,533 - experts.strategy.enhanced_strategy_expert - INFO - 开始增强策略分配分析 (迭代 0)
2025-08-03 16:17:07,533 - experts.analysis.individual_state_analyzer - INFO - 开始分析种群状态 (迭代 0, 种群大小: 20)
2025-08-03 16:17:07,534 - experts.strategy.enhanced_strategy_expert - ERROR - 策略分配过程中发生错误: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:17:07,534 - experts.strategy.enhanced_strategy_expert - WARNING - 使用回退策略分配
2025-08-03 16:17:07,534 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 16:17:07,535 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:17:07,535 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-03 16:17:07,536 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 16:17:07,536 - __main__ - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:17:07,537 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-03 16:17:07,537 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2, 3, 4} (总数: 4, 保护比例: 0.20)
2025-08-03 16:17:07,537 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-03 16:17:07,537 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-03 16:17:07,537 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:17:07,542 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:17:07,542 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:17:07,736 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12459.0, 路径长度: 66
2025-08-03 16:17:07,736 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 16, 17, 13, 20, 21, 19, 18, 12, 22, 23, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12459.0}
2025-08-03 16:17:07,737 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 12459.00)
2025-08-03 16:17:07,738 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-03 16:17:07,738 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:17:07,741 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:17:07,742 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 108665.0
2025-08-03 16:17:09,852 - ExploitationExpert - INFO - res_population_num: 1
2025-08-03 16:17:09,853 - ExploitationExpert - INFO - res_population_costs: [9860.0]
2025-08-03 16:17:09,853 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 17, 19, 13, 21, 20, 14, 15, 12, 22, 23, 16,
       18, 24, 29, 32, 28, 30, 35, 34, 26, 25, 31, 33, 37, 36, 27, 42, 48,
       43, 40, 49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57,
       64, 60, 58, 56, 59, 62, 53, 61, 55,  5,  4,  6,  2,  8, 10],
      dtype=int64)]
2025-08-03 16:17:09,855 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:17:09,855 - ExploitationExpert - INFO - populations: [{'tour': [0, 16, 17, 13, 20, 21, 19, 18, 12, 22, 23, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12459.0}, {'tour': array([55, 36, 62, 53,  3, 49, 23, 34,  4, 45, 30, 17, 58, 15, 50, 64, 46,
       20, 48, 40, 29, 61, 28, 11, 39, 42,  9, 65,  5,  0,  1, 56, 31, 19,
       51,  7, 44, 63, 57,  2, 35, 25, 12, 24, 22, 41, 27, 52, 14, 16, 33,
       43, 10, 18, 54, 21,  6, 47, 38, 26, 59, 32, 37,  8, 13, 60],
      dtype=int64), 'cur_cost': 108665.0}, {'tour': array([48, 49, 40, 43, 46, 47, 39, 44, 45, 38, 51, 50, 41, 42, 21, 20, 13,
       23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30,
       34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,  2,  6, 10,
        0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63],
      dtype=int64), 'cur_cost': 9971.0}, {'tour': array([32, 28, 30, 35, 34, 26, 25, 31, 33, 29, 24, 37, 36, 27, 18, 16, 23,
       22, 12, 17, 15, 14, 20, 21, 13, 19,  9, 11,  7,  3,  1,  0, 10,  8,
        2,  6,  4,  5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9903.0}, {'tour': array([25, 26, 36, 37, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23,
       22, 12, 17, 15, 14, 20, 21, 13, 19,  9, 11,  7,  3,  1,  0, 10,  8,
        2,  6,  4,  5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9890.0}, {'tour': array([58, 56, 59, 62, 53, 64, 57, 54, 60, 55, 61, 65, 52, 63,  2,  8,  5,
        4,  6,  9, 11,  7,  3,  1,  0, 10, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29,
       32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42],
      dtype=int64), 'cur_cost': 10027.0}, {'tour': array([34, 38, 52, 62, 19, 45,  7, 39, 23,  0,  2, 47, 16,  5, 33, 50,  1,
       63, 35, 51, 13, 46, 26, 65, 64, 11, 37, 30, 56, 49, 36, 48,  3, 59,
       18, 31, 28, 21, 20, 40, 42, 24, 41, 53, 44, 27, 14, 54, 32, 12, 60,
       58, 10, 25, 22, 17, 55,  6,  4, 29, 15, 61,  9, 43,  8, 57],
      dtype=int64), 'cur_cost': 108113.0}, {'tour': array([13,  9, 33, 49, 20, 37,  8, 41, 32, 27,  2, 36,  0, 63,  4, 57, 43,
       12, 45, 22, 23, 29, 44, 30, 47, 56, 25, 59, 21, 10, 46, 58, 31,  1,
        6, 15, 55, 19, 26, 14,  5, 62, 54, 17, 35, 40, 53, 11, 18, 34, 16,
       28, 52, 48, 65, 24, 39, 42, 60, 51, 61,  3, 38, 50,  7, 64],
      dtype=int64), 'cur_cost': 115818.0}, {'tour': array([62,  3, 51,  5, 24, 37,  4,  9, 55, 59, 39, 57, 21,  7, 36, 48, 50,
       25, 65, 11, 28, 38, 54, 16, 61, 56, 52, 40, 44, 27, 41, 17,  0,  8,
       19, 58, 18, 15, 53, 32, 12, 13, 63,  1, 45, 14, 30,  2, 64, 60, 31,
       35,  6, 29, 33, 10, 22, 47, 42, 43, 46, 34, 23, 26, 49, 20],
      dtype=int64), 'cur_cost': 95864.0}, {'tour': array([42, 51, 61, 30, 53, 43, 22, 19, 33, 25, 14, 36, 31, 27,  4, 52, 20,
       56, 26, 16, 37, 44, 29, 62, 12, 32, 28, 63, 38, 41, 65,  0,  5, 40,
       11, 17, 64, 24, 35, 48,  3,  9, 59, 13, 47,  8, 10, 58, 50,  2, 57,
       39, 46, 55, 60, 45, 21,  1, 18,  7, 23, 34, 15, 49,  6, 54],
      dtype=int64), 'cur_cost': 104286.0}, {'tour': array([11,  9, 56, 64, 65, 24, 14, 55, 35, 16,  5, 62, 36, 43, 30, 53, 63,
       19, 26, 15,  4, 54, 13, 27, 28, 12, 22, 61, 10, 33, 47, 52, 17, 23,
       51, 57, 59, 41, 29,  7, 45, 18,  1, 21, 34, 38, 50, 31, 49, 48,  8,
       20, 25, 39, 60, 44, 37, 58,  6,  2,  3, 32, 46, 40,  0, 42],
      dtype=int64), 'cur_cost': 102758.0}, {'tour': array([38, 60, 35, 45, 42, 40, 31, 22, 59, 58, 17, 57, 62, 36, 50, 54, 19,
       13, 51, 61,  6, 53, 39, 34, 44, 20, 29, 55, 46, 43, 33, 41, 12,  8,
       16, 56,  0, 14, 27, 11,  2, 26, 49, 64, 23, 32, 63, 28, 15, 25,  1,
       24,  3, 65, 37,  9,  5, 48, 30, 10, 52, 18, 21, 47,  7,  4],
      dtype=int64), 'cur_cost': 110826.0}, {'tour': array([ 2, 24, 58, 33, 21,  1, 10,  4, 38, 18,  5, 48,  0, 32, 27, 63, 60,
       20, 62, 44, 16, 14,  9, 15, 26, 41,  7, 57,  8, 17, 40, 59, 56, 65,
       45, 34, 13, 37, 30, 53, 52, 46, 22, 35, 31, 12, 43, 54,  6, 49, 36,
       47, 39, 64, 29, 23, 42, 11, 25, 50, 19, 51, 55, 61, 28,  3],
      dtype=int64), 'cur_cost': 104717.0}, {'tour': array([45, 15, 48, 54, 61, 43,  9, 49, 35, 12,  6,  8, 10, 41, 29, 22, 39,
        1, 47, 33, 46,  2, 51, 65, 28, 34, 21, 57, 25, 14, 60,  3, 38, 63,
       27, 36, 52, 64, 18, 13, 53,  5, 17, 50, 44, 31, 23, 19,  0, 20, 59,
        4, 24, 56, 40, 16, 62, 55, 58,  7, 11, 30, 26, 32, 37, 42],
      dtype=int64), 'cur_cost': 100560.0}, {'tour': array([12, 47, 11, 39,  3, 64, 27, 35, 56, 13, 18, 22, 42, 31,  1, 43, 29,
       40, 24, 23, 14,  2, 17, 51, 26, 54, 46, 30,  8,  9, 59, 50, 25, 52,
       44, 60, 38, 65, 16, 33, 32,  4,  6, 45, 20, 61,  5, 36, 10, 28, 63,
       62,  7, 21, 57, 49, 48, 53,  0, 37, 58, 15, 34, 55, 41, 19],
      dtype=int64), 'cur_cost': 117261.0}, {'tour': array([38, 20, 51, 14, 17, 44, 16, 29,  1, 35, 10, 42, 40, 46, 48, 39, 23,
       49, 62, 11, 15,  2, 22, 56, 28,  4, 34, 54, 19,  9,  0, 18,  6, 59,
       60, 26, 30, 25, 47, 63, 43, 27, 53, 31, 64,  5, 37,  7, 65, 58, 36,
       21, 33, 55, 41, 45, 50, 24, 13,  8,  3, 12, 57, 32, 52, 61],
      dtype=int64), 'cur_cost': 101847.0}, {'tour': array([30, 15, 48, 13, 63, 45, 42, 34,  2,  8, 37, 49, 43, 50, 24,  5, 35,
       18, 65, 27, 29, 12, 52, 53, 44, 51, 40, 11, 14, 59, 32, 20,  9,  0,
        4, 60, 61, 10, 54,  1, 25, 62, 41, 33, 23, 28, 21, 36, 19, 17, 26,
        6, 22, 31, 57, 47, 16,  7, 46, 39, 58, 64, 55, 56, 38,  3],
      dtype=int64), 'cur_cost': 93836.0}, {'tour': array([58, 65, 55, 26, 57, 22, 43, 47, 23, 48, 59, 41, 51, 12,  3, 29, 54,
       61, 42, 20, 64,  0, 31, 46,  6, 15, 49, 53,  7, 60, 44, 17, 52, 19,
       63,  9, 30, 35, 56, 24, 32, 25,  1,  5, 28, 37, 40, 16, 27, 34, 36,
       18,  2,  8, 38, 14,  4, 50, 11, 33, 13, 21, 45, 39, 10, 62],
      dtype=int64), 'cur_cost': 95708.0}, {'tour': array([40, 43, 11,  4, 31, 64,  7, 12, 36, 56, 24, 54, 34,  5, 38, 30, 23,
       28, 55, 29, 16, 37, 58, 50, 47, 59, 49, 44, 35, 42, 52,  8, 51, 57,
       10,  1, 21,  0, 39, 60, 65,  6, 15, 61,  9, 18, 33, 25, 13, 20, 48,
       14, 22, 46, 63, 19, 32, 41, 17, 45, 26, 27, 53,  3, 62,  2],
      dtype=int64), 'cur_cost': 108950.0}, {'tour': array([42,  0, 63, 14, 38, 12, 23, 59, 54, 16,  3, 48, 34, 65, 62, 11, 21,
       18, 56, 29, 30, 53, 64, 22, 45, 61, 46,  7, 28, 39,  5, 60, 43, 36,
       32, 31,  8, 44, 57, 17, 37, 15,  9, 50, 41, 25, 58,  2, 10,  6, 27,
       13, 51,  1, 49, 40, 47,  4, 24, 19, 20, 26, 35, 52, 55, 33],
      dtype=int64), 'cur_cost': 101901.0}]
2025-08-03 16:17:09,867 - ExploitationExpert - INFO - 局部搜索耗时: 2.13秒
2025-08-03 16:17:09,867 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-03 16:17:09,868 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([55, 36, 62, 53,  3, 49, 23, 34,  4, 45, 30, 17, 58, 15, 50, 64, 46,
       20, 48, 40, 29, 61, 28, 11, 39, 42,  9, 65,  5,  0,  1, 56, 31, 19,
       51,  7, 44, 63, 57,  2, 35, 25, 12, 24, 22, 41, 27, 52, 14, 16, 33,
       43, 10, 18, 54, 21,  6, 47, 38, 26, 59, 32, 37,  8, 13, 60],
      dtype=int64), 'cur_cost': 108665.0}
2025-08-03 16:17:09,869 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 108665.00)
2025-08-03 16:17:09,869 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-03 16:17:09,869 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-03 16:17:09,870 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:17:09,875 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:17:09,875 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:17:09,876 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12478.0, 路径长度: 66
2025-08-03 16:17:09,876 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 4, 11, 16, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12478.0}
2025-08-03 16:17:09,876 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 12478.00)
2025-08-03 16:17:09,877 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-03 16:17:09,877 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-03 16:17:09,877 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:17:09,895 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:17:09,895 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:17:09,895 - ExplorationExpert - INFO - 探索路径生成完成，成本: 72732.0, 路径长度: 66
2025-08-03 16:17:09,896 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [62, 65, 23, 17, 21, 11, 63, 4, 20, 35, 5, 55, 57, 54, 59, 58, 40, 46, 43, 36, 30, 22, 26, 1, 12, 27, 9, 0, 18, 29, 8, 15, 37, 7, 31, 49, 39, 42, 51, 41, 19, 24, 32, 3, 61, 2, 53, 64, 47, 44, 34, 16, 33, 13, 6, 56, 48, 45, 50, 25, 10, 60, 52, 14, 28, 38], 'cur_cost': 72732.0}
2025-08-03 16:17:09,896 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 72732.00)
2025-08-03 16:17:09,896 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-03 16:17:09,897 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:17:09,897 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:17:09,898 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 105760.0
2025-08-03 16:17:12,257 - ExploitationExpert - INFO - res_population_num: 2
2025-08-03 16:17:12,258 - ExploitationExpert - INFO - res_population_costs: [9860.0, 9581.0]
2025-08-03 16:17:12,258 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 17, 19, 13, 21, 20, 14, 15, 12, 22, 23, 16,
       18, 24, 29, 32, 28, 30, 35, 34, 26, 25, 31, 33, 37, 36, 27, 42, 48,
       43, 40, 49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57,
       64, 60, 58, 56, 59, 62, 53, 61, 55,  5,  4,  6,  2,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59,
       62, 53, 61, 55, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:17:12,259 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:17:12,260 - ExploitationExpert - INFO - populations: [{'tour': [0, 16, 17, 13, 20, 21, 19, 18, 12, 22, 23, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12459.0}, {'tour': array([55, 36, 62, 53,  3, 49, 23, 34,  4, 45, 30, 17, 58, 15, 50, 64, 46,
       20, 48, 40, 29, 61, 28, 11, 39, 42,  9, 65,  5,  0,  1, 56, 31, 19,
       51,  7, 44, 63, 57,  2, 35, 25, 12, 24, 22, 41, 27, 52, 14, 16, 33,
       43, 10, 18, 54, 21,  6, 47, 38, 26, 59, 32, 37,  8, 13, 60],
      dtype=int64), 'cur_cost': 108665.0}, {'tour': [0, 4, 11, 16, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12478.0}, {'tour': [62, 65, 23, 17, 21, 11, 63, 4, 20, 35, 5, 55, 57, 54, 59, 58, 40, 46, 43, 36, 30, 22, 26, 1, 12, 27, 9, 0, 18, 29, 8, 15, 37, 7, 31, 49, 39, 42, 51, 41, 19, 24, 32, 3, 61, 2, 53, 64, 47, 44, 34, 16, 33, 13, 6, 56, 48, 45, 50, 25, 10, 60, 52, 14, 28, 38], 'cur_cost': 72732.0}, {'tour': array([42, 19,  0, 43,  9, 29, 46, 52, 31,  7, 17,  6, 41,  3, 53, 45, 54,
       58, 12, 18, 37, 65,  2, 56, 60, 23, 33, 25, 48, 39, 47, 49, 27, 59,
       30,  8, 10, 21,  4, 40, 61, 11, 62, 51, 64, 24, 44, 22, 28, 35, 34,
        5, 55, 13, 15, 50, 38, 63, 26, 57, 20,  1, 32, 16, 14, 36],
      dtype=int64), 'cur_cost': 105760.0}, {'tour': array([58, 56, 59, 62, 53, 64, 57, 54, 60, 55, 61, 65, 52, 63,  2,  8,  5,
        4,  6,  9, 11,  7,  3,  1,  0, 10, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29,
       32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42],
      dtype=int64), 'cur_cost': 10027.0}, {'tour': array([34, 38, 52, 62, 19, 45,  7, 39, 23,  0,  2, 47, 16,  5, 33, 50,  1,
       63, 35, 51, 13, 46, 26, 65, 64, 11, 37, 30, 56, 49, 36, 48,  3, 59,
       18, 31, 28, 21, 20, 40, 42, 24, 41, 53, 44, 27, 14, 54, 32, 12, 60,
       58, 10, 25, 22, 17, 55,  6,  4, 29, 15, 61,  9, 43,  8, 57],
      dtype=int64), 'cur_cost': 108113.0}, {'tour': array([13,  9, 33, 49, 20, 37,  8, 41, 32, 27,  2, 36,  0, 63,  4, 57, 43,
       12, 45, 22, 23, 29, 44, 30, 47, 56, 25, 59, 21, 10, 46, 58, 31,  1,
        6, 15, 55, 19, 26, 14,  5, 62, 54, 17, 35, 40, 53, 11, 18, 34, 16,
       28, 52, 48, 65, 24, 39, 42, 60, 51, 61,  3, 38, 50,  7, 64],
      dtype=int64), 'cur_cost': 115818.0}, {'tour': array([62,  3, 51,  5, 24, 37,  4,  9, 55, 59, 39, 57, 21,  7, 36, 48, 50,
       25, 65, 11, 28, 38, 54, 16, 61, 56, 52, 40, 44, 27, 41, 17,  0,  8,
       19, 58, 18, 15, 53, 32, 12, 13, 63,  1, 45, 14, 30,  2, 64, 60, 31,
       35,  6, 29, 33, 10, 22, 47, 42, 43, 46, 34, 23, 26, 49, 20],
      dtype=int64), 'cur_cost': 95864.0}, {'tour': array([42, 51, 61, 30, 53, 43, 22, 19, 33, 25, 14, 36, 31, 27,  4, 52, 20,
       56, 26, 16, 37, 44, 29, 62, 12, 32, 28, 63, 38, 41, 65,  0,  5, 40,
       11, 17, 64, 24, 35, 48,  3,  9, 59, 13, 47,  8, 10, 58, 50,  2, 57,
       39, 46, 55, 60, 45, 21,  1, 18,  7, 23, 34, 15, 49,  6, 54],
      dtype=int64), 'cur_cost': 104286.0}, {'tour': array([11,  9, 56, 64, 65, 24, 14, 55, 35, 16,  5, 62, 36, 43, 30, 53, 63,
       19, 26, 15,  4, 54, 13, 27, 28, 12, 22, 61, 10, 33, 47, 52, 17, 23,
       51, 57, 59, 41, 29,  7, 45, 18,  1, 21, 34, 38, 50, 31, 49, 48,  8,
       20, 25, 39, 60, 44, 37, 58,  6,  2,  3, 32, 46, 40,  0, 42],
      dtype=int64), 'cur_cost': 102758.0}, {'tour': array([38, 60, 35, 45, 42, 40, 31, 22, 59, 58, 17, 57, 62, 36, 50, 54, 19,
       13, 51, 61,  6, 53, 39, 34, 44, 20, 29, 55, 46, 43, 33, 41, 12,  8,
       16, 56,  0, 14, 27, 11,  2, 26, 49, 64, 23, 32, 63, 28, 15, 25,  1,
       24,  3, 65, 37,  9,  5, 48, 30, 10, 52, 18, 21, 47,  7,  4],
      dtype=int64), 'cur_cost': 110826.0}, {'tour': array([ 2, 24, 58, 33, 21,  1, 10,  4, 38, 18,  5, 48,  0, 32, 27, 63, 60,
       20, 62, 44, 16, 14,  9, 15, 26, 41,  7, 57,  8, 17, 40, 59, 56, 65,
       45, 34, 13, 37, 30, 53, 52, 46, 22, 35, 31, 12, 43, 54,  6, 49, 36,
       47, 39, 64, 29, 23, 42, 11, 25, 50, 19, 51, 55, 61, 28,  3],
      dtype=int64), 'cur_cost': 104717.0}, {'tour': array([45, 15, 48, 54, 61, 43,  9, 49, 35, 12,  6,  8, 10, 41, 29, 22, 39,
        1, 47, 33, 46,  2, 51, 65, 28, 34, 21, 57, 25, 14, 60,  3, 38, 63,
       27, 36, 52, 64, 18, 13, 53,  5, 17, 50, 44, 31, 23, 19,  0, 20, 59,
        4, 24, 56, 40, 16, 62, 55, 58,  7, 11, 30, 26, 32, 37, 42],
      dtype=int64), 'cur_cost': 100560.0}, {'tour': array([12, 47, 11, 39,  3, 64, 27, 35, 56, 13, 18, 22, 42, 31,  1, 43, 29,
       40, 24, 23, 14,  2, 17, 51, 26, 54, 46, 30,  8,  9, 59, 50, 25, 52,
       44, 60, 38, 65, 16, 33, 32,  4,  6, 45, 20, 61,  5, 36, 10, 28, 63,
       62,  7, 21, 57, 49, 48, 53,  0, 37, 58, 15, 34, 55, 41, 19],
      dtype=int64), 'cur_cost': 117261.0}, {'tour': array([38, 20, 51, 14, 17, 44, 16, 29,  1, 35, 10, 42, 40, 46, 48, 39, 23,
       49, 62, 11, 15,  2, 22, 56, 28,  4, 34, 54, 19,  9,  0, 18,  6, 59,
       60, 26, 30, 25, 47, 63, 43, 27, 53, 31, 64,  5, 37,  7, 65, 58, 36,
       21, 33, 55, 41, 45, 50, 24, 13,  8,  3, 12, 57, 32, 52, 61],
      dtype=int64), 'cur_cost': 101847.0}, {'tour': array([30, 15, 48, 13, 63, 45, 42, 34,  2,  8, 37, 49, 43, 50, 24,  5, 35,
       18, 65, 27, 29, 12, 52, 53, 44, 51, 40, 11, 14, 59, 32, 20,  9,  0,
        4, 60, 61, 10, 54,  1, 25, 62, 41, 33, 23, 28, 21, 36, 19, 17, 26,
        6, 22, 31, 57, 47, 16,  7, 46, 39, 58, 64, 55, 56, 38,  3],
      dtype=int64), 'cur_cost': 93836.0}, {'tour': array([58, 65, 55, 26, 57, 22, 43, 47, 23, 48, 59, 41, 51, 12,  3, 29, 54,
       61, 42, 20, 64,  0, 31, 46,  6, 15, 49, 53,  7, 60, 44, 17, 52, 19,
       63,  9, 30, 35, 56, 24, 32, 25,  1,  5, 28, 37, 40, 16, 27, 34, 36,
       18,  2,  8, 38, 14,  4, 50, 11, 33, 13, 21, 45, 39, 10, 62],
      dtype=int64), 'cur_cost': 95708.0}, {'tour': array([40, 43, 11,  4, 31, 64,  7, 12, 36, 56, 24, 54, 34,  5, 38, 30, 23,
       28, 55, 29, 16, 37, 58, 50, 47, 59, 49, 44, 35, 42, 52,  8, 51, 57,
       10,  1, 21,  0, 39, 60, 65,  6, 15, 61,  9, 18, 33, 25, 13, 20, 48,
       14, 22, 46, 63, 19, 32, 41, 17, 45, 26, 27, 53,  3, 62,  2],
      dtype=int64), 'cur_cost': 108950.0}, {'tour': array([42,  0, 63, 14, 38, 12, 23, 59, 54, 16,  3, 48, 34, 65, 62, 11, 21,
       18, 56, 29, 30, 53, 64, 22, 45, 61, 46,  7, 28, 39,  5, 60, 43, 36,
       32, 31,  8, 44, 57, 17, 37, 15,  9, 50, 41, 25, 58,  2, 10,  6, 27,
       13, 51,  1, 49, 40, 47,  4, 24, 19, 20, 26, 35, 52, 55, 33],
      dtype=int64), 'cur_cost': 101901.0}]
2025-08-03 16:17:12,271 - ExploitationExpert - INFO - 局部搜索耗时: 2.37秒
2025-08-03 16:17:12,272 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-03 16:17:12,272 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([42, 19,  0, 43,  9, 29, 46, 52, 31,  7, 17,  6, 41,  3, 53, 45, 54,
       58, 12, 18, 37, 65,  2, 56, 60, 23, 33, 25, 48, 39, 47, 49, 27, 59,
       30,  8, 10, 21,  4, 40, 61, 11, 62, 51, 64, 24, 44, 22, 28, 35, 34,
        5, 55, 13, 15, 50, 38, 63, 26, 57, 20,  1, 32, 16, 14, 36],
      dtype=int64), 'cur_cost': 105760.0}
2025-08-03 16:17:12,273 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 105760.00)
2025-08-03 16:17:12,273 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-03 16:17:12,273 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-03 16:17:12,274 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:17:12,279 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:17:12,281 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:17:12,282 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14670.0, 路径长度: 66
2025-08-03 16:17:12,282 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 22, 24, 17, 12, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14670.0}
2025-08-03 16:17:12,285 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 14670.00)
2025-08-03 16:17:12,285 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-03 16:17:12,287 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-03 16:17:12,289 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:17:12,306 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:17:12,306 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:17:12,307 - ExplorationExpert - INFO - 探索路径生成完成，成本: 65573.0, 路径长度: 66
2025-08-03 16:17:12,307 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [20, 12, 13, 19, 7, 14, 1, 9, 11, 53, 61, 17, 35, 36, 2, 58, 6, 4, 64, 10, 3, 59, 60, 65, 21, 29, 25, 16, 43, 42, 41, 45, 47, 44, 39, 27, 31, 18, 34, 0, 22, 26, 23, 5, 63, 49, 38, 40, 30, 15, 37, 24, 8, 57, 62, 52, 48, 28, 46, 33, 50, 32, 55, 56, 54, 51], 'cur_cost': 65573.0}
2025-08-03 16:17:12,308 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 65573.00)
2025-08-03 16:17:12,308 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-03 16:17:12,308 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:17:12,308 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:17:12,308 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 105989.0
2025-08-03 16:17:12,900 - ExploitationExpert - INFO - res_population_num: 3
2025-08-03 16:17:12,900 - ExploitationExpert - INFO - res_population_costs: [9860.0, 9581.0, 9540.0]
2025-08-03 16:17:12,901 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 17, 19, 13, 21, 20, 14, 15, 12, 22, 23, 16,
       18, 24, 29, 32, 28, 30, 35, 34, 26, 25, 31, 33, 37, 36, 27, 42, 48,
       43, 40, 49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57,
       64, 60, 58, 56, 59, 62, 53, 61, 55,  5,  4,  6,  2,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59,
       62, 53, 61, 55, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64)]
2025-08-03 16:17:12,902 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:17:12,903 - ExploitationExpert - INFO - populations: [{'tour': [0, 16, 17, 13, 20, 21, 19, 18, 12, 22, 23, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12459.0}, {'tour': array([55, 36, 62, 53,  3, 49, 23, 34,  4, 45, 30, 17, 58, 15, 50, 64, 46,
       20, 48, 40, 29, 61, 28, 11, 39, 42,  9, 65,  5,  0,  1, 56, 31, 19,
       51,  7, 44, 63, 57,  2, 35, 25, 12, 24, 22, 41, 27, 52, 14, 16, 33,
       43, 10, 18, 54, 21,  6, 47, 38, 26, 59, 32, 37,  8, 13, 60],
      dtype=int64), 'cur_cost': 108665.0}, {'tour': [0, 4, 11, 16, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12478.0}, {'tour': [62, 65, 23, 17, 21, 11, 63, 4, 20, 35, 5, 55, 57, 54, 59, 58, 40, 46, 43, 36, 30, 22, 26, 1, 12, 27, 9, 0, 18, 29, 8, 15, 37, 7, 31, 49, 39, 42, 51, 41, 19, 24, 32, 3, 61, 2, 53, 64, 47, 44, 34, 16, 33, 13, 6, 56, 48, 45, 50, 25, 10, 60, 52, 14, 28, 38], 'cur_cost': 72732.0}, {'tour': array([42, 19,  0, 43,  9, 29, 46, 52, 31,  7, 17,  6, 41,  3, 53, 45, 54,
       58, 12, 18, 37, 65,  2, 56, 60, 23, 33, 25, 48, 39, 47, 49, 27, 59,
       30,  8, 10, 21,  4, 40, 61, 11, 62, 51, 64, 24, 44, 22, 28, 35, 34,
        5, 55, 13, 15, 50, 38, 63, 26, 57, 20,  1, 32, 16, 14, 36],
      dtype=int64), 'cur_cost': 105760.0}, {'tour': [0, 22, 24, 17, 12, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14670.0}, {'tour': [20, 12, 13, 19, 7, 14, 1, 9, 11, 53, 61, 17, 35, 36, 2, 58, 6, 4, 64, 10, 3, 59, 60, 65, 21, 29, 25, 16, 43, 42, 41, 45, 47, 44, 39, 27, 31, 18, 34, 0, 22, 26, 23, 5, 63, 49, 38, 40, 30, 15, 37, 24, 8, 57, 62, 52, 48, 28, 46, 33, 50, 32, 55, 56, 54, 51], 'cur_cost': 65573.0}, {'tour': array([52, 23, 11, 53, 38, 46, 25,  9, 62, 29,  3, 31, 22, 30,  6,  2, 20,
       19, 33, 59, 35, 36, 24, 27, 56, 34,  5, 55, 61, 43, 47, 64, 15, 49,
       14, 17,  7, 21, 51, 10, 45,  8,  4, 28, 48, 42, 63, 44, 18, 13, 54,
       65, 58, 26, 57, 41, 16, 40,  0, 37, 12, 39, 60, 50, 32,  1],
      dtype=int64), 'cur_cost': 105989.0}, {'tour': array([62,  3, 51,  5, 24, 37,  4,  9, 55, 59, 39, 57, 21,  7, 36, 48, 50,
       25, 65, 11, 28, 38, 54, 16, 61, 56, 52, 40, 44, 27, 41, 17,  0,  8,
       19, 58, 18, 15, 53, 32, 12, 13, 63,  1, 45, 14, 30,  2, 64, 60, 31,
       35,  6, 29, 33, 10, 22, 47, 42, 43, 46, 34, 23, 26, 49, 20],
      dtype=int64), 'cur_cost': 95864.0}, {'tour': array([42, 51, 61, 30, 53, 43, 22, 19, 33, 25, 14, 36, 31, 27,  4, 52, 20,
       56, 26, 16, 37, 44, 29, 62, 12, 32, 28, 63, 38, 41, 65,  0,  5, 40,
       11, 17, 64, 24, 35, 48,  3,  9, 59, 13, 47,  8, 10, 58, 50,  2, 57,
       39, 46, 55, 60, 45, 21,  1, 18,  7, 23, 34, 15, 49,  6, 54],
      dtype=int64), 'cur_cost': 104286.0}, {'tour': array([11,  9, 56, 64, 65, 24, 14, 55, 35, 16,  5, 62, 36, 43, 30, 53, 63,
       19, 26, 15,  4, 54, 13, 27, 28, 12, 22, 61, 10, 33, 47, 52, 17, 23,
       51, 57, 59, 41, 29,  7, 45, 18,  1, 21, 34, 38, 50, 31, 49, 48,  8,
       20, 25, 39, 60, 44, 37, 58,  6,  2,  3, 32, 46, 40,  0, 42],
      dtype=int64), 'cur_cost': 102758.0}, {'tour': array([38, 60, 35, 45, 42, 40, 31, 22, 59, 58, 17, 57, 62, 36, 50, 54, 19,
       13, 51, 61,  6, 53, 39, 34, 44, 20, 29, 55, 46, 43, 33, 41, 12,  8,
       16, 56,  0, 14, 27, 11,  2, 26, 49, 64, 23, 32, 63, 28, 15, 25,  1,
       24,  3, 65, 37,  9,  5, 48, 30, 10, 52, 18, 21, 47,  7,  4],
      dtype=int64), 'cur_cost': 110826.0}, {'tour': array([ 2, 24, 58, 33, 21,  1, 10,  4, 38, 18,  5, 48,  0, 32, 27, 63, 60,
       20, 62, 44, 16, 14,  9, 15, 26, 41,  7, 57,  8, 17, 40, 59, 56, 65,
       45, 34, 13, 37, 30, 53, 52, 46, 22, 35, 31, 12, 43, 54,  6, 49, 36,
       47, 39, 64, 29, 23, 42, 11, 25, 50, 19, 51, 55, 61, 28,  3],
      dtype=int64), 'cur_cost': 104717.0}, {'tour': array([45, 15, 48, 54, 61, 43,  9, 49, 35, 12,  6,  8, 10, 41, 29, 22, 39,
        1, 47, 33, 46,  2, 51, 65, 28, 34, 21, 57, 25, 14, 60,  3, 38, 63,
       27, 36, 52, 64, 18, 13, 53,  5, 17, 50, 44, 31, 23, 19,  0, 20, 59,
        4, 24, 56, 40, 16, 62, 55, 58,  7, 11, 30, 26, 32, 37, 42],
      dtype=int64), 'cur_cost': 100560.0}, {'tour': array([12, 47, 11, 39,  3, 64, 27, 35, 56, 13, 18, 22, 42, 31,  1, 43, 29,
       40, 24, 23, 14,  2, 17, 51, 26, 54, 46, 30,  8,  9, 59, 50, 25, 52,
       44, 60, 38, 65, 16, 33, 32,  4,  6, 45, 20, 61,  5, 36, 10, 28, 63,
       62,  7, 21, 57, 49, 48, 53,  0, 37, 58, 15, 34, 55, 41, 19],
      dtype=int64), 'cur_cost': 117261.0}, {'tour': array([38, 20, 51, 14, 17, 44, 16, 29,  1, 35, 10, 42, 40, 46, 48, 39, 23,
       49, 62, 11, 15,  2, 22, 56, 28,  4, 34, 54, 19,  9,  0, 18,  6, 59,
       60, 26, 30, 25, 47, 63, 43, 27, 53, 31, 64,  5, 37,  7, 65, 58, 36,
       21, 33, 55, 41, 45, 50, 24, 13,  8,  3, 12, 57, 32, 52, 61],
      dtype=int64), 'cur_cost': 101847.0}, {'tour': array([30, 15, 48, 13, 63, 45, 42, 34,  2,  8, 37, 49, 43, 50, 24,  5, 35,
       18, 65, 27, 29, 12, 52, 53, 44, 51, 40, 11, 14, 59, 32, 20,  9,  0,
        4, 60, 61, 10, 54,  1, 25, 62, 41, 33, 23, 28, 21, 36, 19, 17, 26,
        6, 22, 31, 57, 47, 16,  7, 46, 39, 58, 64, 55, 56, 38,  3],
      dtype=int64), 'cur_cost': 93836.0}, {'tour': array([58, 65, 55, 26, 57, 22, 43, 47, 23, 48, 59, 41, 51, 12,  3, 29, 54,
       61, 42, 20, 64,  0, 31, 46,  6, 15, 49, 53,  7, 60, 44, 17, 52, 19,
       63,  9, 30, 35, 56, 24, 32, 25,  1,  5, 28, 37, 40, 16, 27, 34, 36,
       18,  2,  8, 38, 14,  4, 50, 11, 33, 13, 21, 45, 39, 10, 62],
      dtype=int64), 'cur_cost': 95708.0}, {'tour': array([40, 43, 11,  4, 31, 64,  7, 12, 36, 56, 24, 54, 34,  5, 38, 30, 23,
       28, 55, 29, 16, 37, 58, 50, 47, 59, 49, 44, 35, 42, 52,  8, 51, 57,
       10,  1, 21,  0, 39, 60, 65,  6, 15, 61,  9, 18, 33, 25, 13, 20, 48,
       14, 22, 46, 63, 19, 32, 41, 17, 45, 26, 27, 53,  3, 62,  2],
      dtype=int64), 'cur_cost': 108950.0}, {'tour': array([42,  0, 63, 14, 38, 12, 23, 59, 54, 16,  3, 48, 34, 65, 62, 11, 21,
       18, 56, 29, 30, 53, 64, 22, 45, 61, 46,  7, 28, 39,  5, 60, 43, 36,
       32, 31,  8, 44, 57, 17, 37, 15,  9, 50, 41, 25, 58,  2, 10,  6, 27,
       13, 51,  1, 49, 40, 47,  4, 24, 19, 20, 26, 35, 52, 55, 33],
      dtype=int64), 'cur_cost': 101901.0}]
2025-08-03 16:17:12,912 - ExploitationExpert - INFO - 局部搜索耗时: 0.60秒
2025-08-03 16:17:12,912 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-03 16:17:12,915 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([52, 23, 11, 53, 38, 46, 25,  9, 62, 29,  3, 31, 22, 30,  6,  2, 20,
       19, 33, 59, 35, 36, 24, 27, 56, 34,  5, 55, 61, 43, 47, 64, 15, 49,
       14, 17,  7, 21, 51, 10, 45,  8,  4, 28, 48, 42, 63, 44, 18, 13, 54,
       65, 58, 26, 57, 41, 16, 40,  0, 37, 12, 39, 60, 50, 32,  1],
      dtype=int64), 'cur_cost': 105989.0}
2025-08-03 16:17:12,917 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 105989.00)
2025-08-03 16:17:12,917 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-03 16:17:12,918 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-03 16:17:12,918 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:17:12,926 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:17:12,927 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:17:12,927 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12899.0, 路径长度: 66
2025-08-03 16:17:12,928 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 16, 13, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12899.0}
2025-08-03 16:17:12,928 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 12899.00)
2025-08-03 16:17:12,929 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-03 16:17:12,929 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-03 16:17:12,929 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:17:12,936 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:17:12,937 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:17:12,938 - ExplorationExpert - INFO - 探索路径生成完成，成本: 109649.0, 路径长度: 66
2025-08-03 16:17:12,938 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [62, 17, 7, 20, 1, 11, 55, 6, 10, 37, 46, 3, 28, 34, 57, 27, 25, 0, 15, 8, 45, 47, 31, 41, 24, 61, 2, 38, 33, 59, 43, 50, 52, 13, 9, 39, 36, 54, 14, 65, 42, 35, 29, 19, 32, 56, 4, 5, 64, 44, 63, 12, 51, 40, 23, 22, 49, 53, 48, 18, 30, 58, 16, 26, 21, 60], 'cur_cost': 109649.0}
2025-08-03 16:17:12,939 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 109649.00)
2025-08-03 16:17:12,939 - experts.management.collaboration_manager - INFO - 为个体 10 生成利用路径
2025-08-03 16:17:12,940 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:17:12,940 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:17:12,940 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 10 处的路径，新成本: 104428.0
2025-08-03 16:17:13,024 - ExploitationExpert - INFO - res_population_num: 6
2025-08-03 16:17:13,024 - ExploitationExpert - INFO - res_population_costs: [9860.0, 9581.0, 9540.0, 9536, 9527, 9526]
2025-08-03 16:17:13,024 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 17, 19, 13, 21, 20, 14, 15, 12, 22, 23, 16,
       18, 24, 29, 32, 28, 30, 35, 34, 26, 25, 31, 33, 37, 36, 27, 42, 48,
       43, 40, 49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57,
       64, 60, 58, 56, 59, 62, 53, 61, 55,  5,  4,  6,  2,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59,
       62, 53, 61, 55, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:17:13,028 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:17:13,029 - ExploitationExpert - INFO - populations: [{'tour': [0, 16, 17, 13, 20, 21, 19, 18, 12, 22, 23, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12459.0}, {'tour': array([55, 36, 62, 53,  3, 49, 23, 34,  4, 45, 30, 17, 58, 15, 50, 64, 46,
       20, 48, 40, 29, 61, 28, 11, 39, 42,  9, 65,  5,  0,  1, 56, 31, 19,
       51,  7, 44, 63, 57,  2, 35, 25, 12, 24, 22, 41, 27, 52, 14, 16, 33,
       43, 10, 18, 54, 21,  6, 47, 38, 26, 59, 32, 37,  8, 13, 60],
      dtype=int64), 'cur_cost': 108665.0}, {'tour': [0, 4, 11, 16, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12478.0}, {'tour': [62, 65, 23, 17, 21, 11, 63, 4, 20, 35, 5, 55, 57, 54, 59, 58, 40, 46, 43, 36, 30, 22, 26, 1, 12, 27, 9, 0, 18, 29, 8, 15, 37, 7, 31, 49, 39, 42, 51, 41, 19, 24, 32, 3, 61, 2, 53, 64, 47, 44, 34, 16, 33, 13, 6, 56, 48, 45, 50, 25, 10, 60, 52, 14, 28, 38], 'cur_cost': 72732.0}, {'tour': array([42, 19,  0, 43,  9, 29, 46, 52, 31,  7, 17,  6, 41,  3, 53, 45, 54,
       58, 12, 18, 37, 65,  2, 56, 60, 23, 33, 25, 48, 39, 47, 49, 27, 59,
       30,  8, 10, 21,  4, 40, 61, 11, 62, 51, 64, 24, 44, 22, 28, 35, 34,
        5, 55, 13, 15, 50, 38, 63, 26, 57, 20,  1, 32, 16, 14, 36],
      dtype=int64), 'cur_cost': 105760.0}, {'tour': [0, 22, 24, 17, 12, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14670.0}, {'tour': [20, 12, 13, 19, 7, 14, 1, 9, 11, 53, 61, 17, 35, 36, 2, 58, 6, 4, 64, 10, 3, 59, 60, 65, 21, 29, 25, 16, 43, 42, 41, 45, 47, 44, 39, 27, 31, 18, 34, 0, 22, 26, 23, 5, 63, 49, 38, 40, 30, 15, 37, 24, 8, 57, 62, 52, 48, 28, 46, 33, 50, 32, 55, 56, 54, 51], 'cur_cost': 65573.0}, {'tour': array([52, 23, 11, 53, 38, 46, 25,  9, 62, 29,  3, 31, 22, 30,  6,  2, 20,
       19, 33, 59, 35, 36, 24, 27, 56, 34,  5, 55, 61, 43, 47, 64, 15, 49,
       14, 17,  7, 21, 51, 10, 45,  8,  4, 28, 48, 42, 63, 44, 18, 13, 54,
       65, 58, 26, 57, 41, 16, 40,  0, 37, 12, 39, 60, 50, 32,  1],
      dtype=int64), 'cur_cost': 105989.0}, {'tour': [0, 16, 13, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12899.0}, {'tour': [62, 17, 7, 20, 1, 11, 55, 6, 10, 37, 46, 3, 28, 34, 57, 27, 25, 0, 15, 8, 45, 47, 31, 41, 24, 61, 2, 38, 33, 59, 43, 50, 52, 13, 9, 39, 36, 54, 14, 65, 42, 35, 29, 19, 32, 56, 4, 5, 64, 44, 63, 12, 51, 40, 23, 22, 49, 53, 48, 18, 30, 58, 16, 26, 21, 60], 'cur_cost': 109649.0}, {'tour': array([11,  9, 61,  8, 60, 34, 30, 22,  6, 15, 18,  7, 19, 41, 26, 48,  5,
        4, 27, 55, 31, 35, 47, 64, 37, 33, 65, 24,  1, 32, 49, 51, 13, 20,
       16,  2, 63, 50, 25, 42, 45, 58, 57, 39, 21, 53, 46, 28, 38, 56, 29,
       44, 54, 40, 23, 17, 43,  0,  3, 62, 59, 12, 36, 52, 14, 10],
      dtype=int64), 'cur_cost': 104428.0}, {'tour': array([38, 60, 35, 45, 42, 40, 31, 22, 59, 58, 17, 57, 62, 36, 50, 54, 19,
       13, 51, 61,  6, 53, 39, 34, 44, 20, 29, 55, 46, 43, 33, 41, 12,  8,
       16, 56,  0, 14, 27, 11,  2, 26, 49, 64, 23, 32, 63, 28, 15, 25,  1,
       24,  3, 65, 37,  9,  5, 48, 30, 10, 52, 18, 21, 47,  7,  4],
      dtype=int64), 'cur_cost': 110826.0}, {'tour': array([ 2, 24, 58, 33, 21,  1, 10,  4, 38, 18,  5, 48,  0, 32, 27, 63, 60,
       20, 62, 44, 16, 14,  9, 15, 26, 41,  7, 57,  8, 17, 40, 59, 56, 65,
       45, 34, 13, 37, 30, 53, 52, 46, 22, 35, 31, 12, 43, 54,  6, 49, 36,
       47, 39, 64, 29, 23, 42, 11, 25, 50, 19, 51, 55, 61, 28,  3],
      dtype=int64), 'cur_cost': 104717.0}, {'tour': array([45, 15, 48, 54, 61, 43,  9, 49, 35, 12,  6,  8, 10, 41, 29, 22, 39,
        1, 47, 33, 46,  2, 51, 65, 28, 34, 21, 57, 25, 14, 60,  3, 38, 63,
       27, 36, 52, 64, 18, 13, 53,  5, 17, 50, 44, 31, 23, 19,  0, 20, 59,
        4, 24, 56, 40, 16, 62, 55, 58,  7, 11, 30, 26, 32, 37, 42],
      dtype=int64), 'cur_cost': 100560.0}, {'tour': array([12, 47, 11, 39,  3, 64, 27, 35, 56, 13, 18, 22, 42, 31,  1, 43, 29,
       40, 24, 23, 14,  2, 17, 51, 26, 54, 46, 30,  8,  9, 59, 50, 25, 52,
       44, 60, 38, 65, 16, 33, 32,  4,  6, 45, 20, 61,  5, 36, 10, 28, 63,
       62,  7, 21, 57, 49, 48, 53,  0, 37, 58, 15, 34, 55, 41, 19],
      dtype=int64), 'cur_cost': 117261.0}, {'tour': array([38, 20, 51, 14, 17, 44, 16, 29,  1, 35, 10, 42, 40, 46, 48, 39, 23,
       49, 62, 11, 15,  2, 22, 56, 28,  4, 34, 54, 19,  9,  0, 18,  6, 59,
       60, 26, 30, 25, 47, 63, 43, 27, 53, 31, 64,  5, 37,  7, 65, 58, 36,
       21, 33, 55, 41, 45, 50, 24, 13,  8,  3, 12, 57, 32, 52, 61],
      dtype=int64), 'cur_cost': 101847.0}, {'tour': array([30, 15, 48, 13, 63, 45, 42, 34,  2,  8, 37, 49, 43, 50, 24,  5, 35,
       18, 65, 27, 29, 12, 52, 53, 44, 51, 40, 11, 14, 59, 32, 20,  9,  0,
        4, 60, 61, 10, 54,  1, 25, 62, 41, 33, 23, 28, 21, 36, 19, 17, 26,
        6, 22, 31, 57, 47, 16,  7, 46, 39, 58, 64, 55, 56, 38,  3],
      dtype=int64), 'cur_cost': 93836.0}, {'tour': array([58, 65, 55, 26, 57, 22, 43, 47, 23, 48, 59, 41, 51, 12,  3, 29, 54,
       61, 42, 20, 64,  0, 31, 46,  6, 15, 49, 53,  7, 60, 44, 17, 52, 19,
       63,  9, 30, 35, 56, 24, 32, 25,  1,  5, 28, 37, 40, 16, 27, 34, 36,
       18,  2,  8, 38, 14,  4, 50, 11, 33, 13, 21, 45, 39, 10, 62],
      dtype=int64), 'cur_cost': 95708.0}, {'tour': array([40, 43, 11,  4, 31, 64,  7, 12, 36, 56, 24, 54, 34,  5, 38, 30, 23,
       28, 55, 29, 16, 37, 58, 50, 47, 59, 49, 44, 35, 42, 52,  8, 51, 57,
       10,  1, 21,  0, 39, 60, 65,  6, 15, 61,  9, 18, 33, 25, 13, 20, 48,
       14, 22, 46, 63, 19, 32, 41, 17, 45, 26, 27, 53,  3, 62,  2],
      dtype=int64), 'cur_cost': 108950.0}, {'tour': array([42,  0, 63, 14, 38, 12, 23, 59, 54, 16,  3, 48, 34, 65, 62, 11, 21,
       18, 56, 29, 30, 53, 64, 22, 45, 61, 46,  7, 28, 39,  5, 60, 43, 36,
       32, 31,  8, 44, 57, 17, 37, 15,  9, 50, 41, 25, 58,  2, 10,  6, 27,
       13, 51,  1, 49, 40, 47,  4, 24, 19, 20, 26, 35, 52, 55, 33],
      dtype=int64), 'cur_cost': 101901.0}]
2025-08-03 16:17:13,040 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 16:17:13,040 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-03 16:17:13,041 - experts.management.collaboration_manager - INFO - 个体 10 利用路径生成报告: {'new_tour': array([11,  9, 61,  8, 60, 34, 30, 22,  6, 15, 18,  7, 19, 41, 26, 48,  5,
        4, 27, 55, 31, 35, 47, 64, 37, 33, 65, 24,  1, 32, 49, 51, 13, 20,
       16,  2, 63, 50, 25, 42, 45, 58, 57, 39, 21, 53, 46, 28, 38, 56, 29,
       44, 54, 40, 23, 17, 43,  0,  3, 62, 59, 12, 36, 52, 14, 10],
      dtype=int64), 'cur_cost': 104428.0}
2025-08-03 16:17:13,041 - experts.management.collaboration_manager - INFO - 个体 10 接受新路径 (成本: 104428.00)
2025-08-03 16:17:13,041 - experts.management.collaboration_manager - INFO - 为个体 11 生成探索路径
2025-08-03 16:17:13,042 - ExplorationExpert - INFO - 开始为个体 11 生成探索路径（算法实现）
2025-08-03 16:17:13,042 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:17:13,059 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:17:13,060 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:17:13,060 - ExplorationExpert - INFO - 探索路径生成完成，成本: 55478.0, 路径长度: 66
2025-08-03 16:17:13,061 - experts.management.collaboration_manager - INFO - 个体 11 探索路径生成报告: {'new_tour': [36, 20, 7, 8, 9, 60, 2, 16, 18, 23, 13, 35, 0, 3, 21, 5, 64, 62, 55, 63, 56, 4, 54, 10, 57, 58, 40, 50, 38, 19, 28, 29, 31, 34, 15, 27, 11, 14, 22, 32, 24, 49, 46, 47, 43, 44, 45, 42, 26, 17, 12, 33, 39, 51, 25, 48, 30, 6, 59, 65, 61, 53, 1, 52, 41, 37], 'cur_cost': 55478.0}
2025-08-03 16:17:13,061 - experts.management.collaboration_manager - INFO - 个体 11 接受新路径 (成本: 55478.00)
2025-08-03 16:17:13,062 - experts.management.collaboration_manager - INFO - 为个体 12 生成探索路径
2025-08-03 16:17:13,062 - ExplorationExpert - INFO - 开始为个体 12 生成探索路径（算法实现）
2025-08-03 16:17:13,062 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:17:13,077 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:17:13,078 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:17:13,078 - ExplorationExpert - INFO - 探索路径生成完成，成本: 49989.0, 路径长度: 66
2025-08-03 16:17:13,078 - experts.management.collaboration_manager - INFO - 个体 12 探索路径生成报告: {'new_tour': [48, 42, 49, 51, 39, 13, 21, 16, 25, 35, 17, 28, 24, 20, 18, 32, 6, 64, 0, 9, 1, 57, 54, 10, 58, 5, 60, 63, 11, 53, 22, 31, 4, 23, 3, 52, 14, 29, 19, 7, 55, 59, 8, 37, 26, 33, 34, 30, 27, 36, 43, 45, 46, 41, 50, 40, 47, 12, 38, 44, 56, 62, 61, 65, 2, 15], 'cur_cost': 49989.0}
2025-08-03 16:17:13,079 - experts.management.collaboration_manager - INFO - 个体 12 接受新路径 (成本: 49989.00)
2025-08-03 16:17:13,081 - experts.management.collaboration_manager - INFO - 为个体 13 生成利用路径
2025-08-03 16:17:13,082 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:17:13,083 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:17:13,084 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 13 处的路径，新成本: 106405.0
2025-08-03 16:17:13,162 - ExploitationExpert - INFO - res_population_num: 6
2025-08-03 16:17:13,163 - ExploitationExpert - INFO - res_population_costs: [9860.0, 9581.0, 9540.0, 9536, 9527, 9526]
2025-08-03 16:17:13,163 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 17, 19, 13, 21, 20, 14, 15, 12, 22, 23, 16,
       18, 24, 29, 32, 28, 30, 35, 34, 26, 25, 31, 33, 37, 36, 27, 42, 48,
       43, 40, 49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57,
       64, 60, 58, 56, 59, 62, 53, 61, 55,  5,  4,  6,  2,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59,
       62, 53, 61, 55, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:17:13,168 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:17:13,169 - ExploitationExpert - INFO - populations: [{'tour': [0, 16, 17, 13, 20, 21, 19, 18, 12, 22, 23, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12459.0}, {'tour': array([55, 36, 62, 53,  3, 49, 23, 34,  4, 45, 30, 17, 58, 15, 50, 64, 46,
       20, 48, 40, 29, 61, 28, 11, 39, 42,  9, 65,  5,  0,  1, 56, 31, 19,
       51,  7, 44, 63, 57,  2, 35, 25, 12, 24, 22, 41, 27, 52, 14, 16, 33,
       43, 10, 18, 54, 21,  6, 47, 38, 26, 59, 32, 37,  8, 13, 60],
      dtype=int64), 'cur_cost': 108665.0}, {'tour': [0, 4, 11, 16, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12478.0}, {'tour': [62, 65, 23, 17, 21, 11, 63, 4, 20, 35, 5, 55, 57, 54, 59, 58, 40, 46, 43, 36, 30, 22, 26, 1, 12, 27, 9, 0, 18, 29, 8, 15, 37, 7, 31, 49, 39, 42, 51, 41, 19, 24, 32, 3, 61, 2, 53, 64, 47, 44, 34, 16, 33, 13, 6, 56, 48, 45, 50, 25, 10, 60, 52, 14, 28, 38], 'cur_cost': 72732.0}, {'tour': array([42, 19,  0, 43,  9, 29, 46, 52, 31,  7, 17,  6, 41,  3, 53, 45, 54,
       58, 12, 18, 37, 65,  2, 56, 60, 23, 33, 25, 48, 39, 47, 49, 27, 59,
       30,  8, 10, 21,  4, 40, 61, 11, 62, 51, 64, 24, 44, 22, 28, 35, 34,
        5, 55, 13, 15, 50, 38, 63, 26, 57, 20,  1, 32, 16, 14, 36],
      dtype=int64), 'cur_cost': 105760.0}, {'tour': [0, 22, 24, 17, 12, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14670.0}, {'tour': [20, 12, 13, 19, 7, 14, 1, 9, 11, 53, 61, 17, 35, 36, 2, 58, 6, 4, 64, 10, 3, 59, 60, 65, 21, 29, 25, 16, 43, 42, 41, 45, 47, 44, 39, 27, 31, 18, 34, 0, 22, 26, 23, 5, 63, 49, 38, 40, 30, 15, 37, 24, 8, 57, 62, 52, 48, 28, 46, 33, 50, 32, 55, 56, 54, 51], 'cur_cost': 65573.0}, {'tour': array([52, 23, 11, 53, 38, 46, 25,  9, 62, 29,  3, 31, 22, 30,  6,  2, 20,
       19, 33, 59, 35, 36, 24, 27, 56, 34,  5, 55, 61, 43, 47, 64, 15, 49,
       14, 17,  7, 21, 51, 10, 45,  8,  4, 28, 48, 42, 63, 44, 18, 13, 54,
       65, 58, 26, 57, 41, 16, 40,  0, 37, 12, 39, 60, 50, 32,  1],
      dtype=int64), 'cur_cost': 105989.0}, {'tour': [0, 16, 13, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12899.0}, {'tour': [62, 17, 7, 20, 1, 11, 55, 6, 10, 37, 46, 3, 28, 34, 57, 27, 25, 0, 15, 8, 45, 47, 31, 41, 24, 61, 2, 38, 33, 59, 43, 50, 52, 13, 9, 39, 36, 54, 14, 65, 42, 35, 29, 19, 32, 56, 4, 5, 64, 44, 63, 12, 51, 40, 23, 22, 49, 53, 48, 18, 30, 58, 16, 26, 21, 60], 'cur_cost': 109649.0}, {'tour': array([11,  9, 61,  8, 60, 34, 30, 22,  6, 15, 18,  7, 19, 41, 26, 48,  5,
        4, 27, 55, 31, 35, 47, 64, 37, 33, 65, 24,  1, 32, 49, 51, 13, 20,
       16,  2, 63, 50, 25, 42, 45, 58, 57, 39, 21, 53, 46, 28, 38, 56, 29,
       44, 54, 40, 23, 17, 43,  0,  3, 62, 59, 12, 36, 52, 14, 10],
      dtype=int64), 'cur_cost': 104428.0}, {'tour': [36, 20, 7, 8, 9, 60, 2, 16, 18, 23, 13, 35, 0, 3, 21, 5, 64, 62, 55, 63, 56, 4, 54, 10, 57, 58, 40, 50, 38, 19, 28, 29, 31, 34, 15, 27, 11, 14, 22, 32, 24, 49, 46, 47, 43, 44, 45, 42, 26, 17, 12, 33, 39, 51, 25, 48, 30, 6, 59, 65, 61, 53, 1, 52, 41, 37], 'cur_cost': 55478.0}, {'tour': [48, 42, 49, 51, 39, 13, 21, 16, 25, 35, 17, 28, 24, 20, 18, 32, 6, 64, 0, 9, 1, 57, 54, 10, 58, 5, 60, 63, 11, 53, 22, 31, 4, 23, 3, 52, 14, 29, 19, 7, 55, 59, 8, 37, 26, 33, 34, 30, 27, 36, 43, 45, 46, 41, 50, 40, 47, 12, 38, 44, 56, 62, 61, 65, 2, 15], 'cur_cost': 49989.0}, {'tour': array([28, 24, 53,  0, 37, 30, 15, 21, 64,  5, 49, 29,  8, 36, 38,  6, 27,
       52, 16, 63, 14,  2, 32, 47, 23, 54, 61,  4, 60, 34, 55, 57, 12, 65,
       56, 35, 59, 20,  3, 17, 46,  9, 22, 19, 11, 41, 25, 40, 18, 62, 58,
       42,  7, 44, 10,  1, 43, 13, 31, 48, 45, 39, 51, 26, 50, 33],
      dtype=int64), 'cur_cost': 106405.0}, {'tour': array([12, 47, 11, 39,  3, 64, 27, 35, 56, 13, 18, 22, 42, 31,  1, 43, 29,
       40, 24, 23, 14,  2, 17, 51, 26, 54, 46, 30,  8,  9, 59, 50, 25, 52,
       44, 60, 38, 65, 16, 33, 32,  4,  6, 45, 20, 61,  5, 36, 10, 28, 63,
       62,  7, 21, 57, 49, 48, 53,  0, 37, 58, 15, 34, 55, 41, 19],
      dtype=int64), 'cur_cost': 117261.0}, {'tour': array([38, 20, 51, 14, 17, 44, 16, 29,  1, 35, 10, 42, 40, 46, 48, 39, 23,
       49, 62, 11, 15,  2, 22, 56, 28,  4, 34, 54, 19,  9,  0, 18,  6, 59,
       60, 26, 30, 25, 47, 63, 43, 27, 53, 31, 64,  5, 37,  7, 65, 58, 36,
       21, 33, 55, 41, 45, 50, 24, 13,  8,  3, 12, 57, 32, 52, 61],
      dtype=int64), 'cur_cost': 101847.0}, {'tour': array([30, 15, 48, 13, 63, 45, 42, 34,  2,  8, 37, 49, 43, 50, 24,  5, 35,
       18, 65, 27, 29, 12, 52, 53, 44, 51, 40, 11, 14, 59, 32, 20,  9,  0,
        4, 60, 61, 10, 54,  1, 25, 62, 41, 33, 23, 28, 21, 36, 19, 17, 26,
        6, 22, 31, 57, 47, 16,  7, 46, 39, 58, 64, 55, 56, 38,  3],
      dtype=int64), 'cur_cost': 93836.0}, {'tour': array([58, 65, 55, 26, 57, 22, 43, 47, 23, 48, 59, 41, 51, 12,  3, 29, 54,
       61, 42, 20, 64,  0, 31, 46,  6, 15, 49, 53,  7, 60, 44, 17, 52, 19,
       63,  9, 30, 35, 56, 24, 32, 25,  1,  5, 28, 37, 40, 16, 27, 34, 36,
       18,  2,  8, 38, 14,  4, 50, 11, 33, 13, 21, 45, 39, 10, 62],
      dtype=int64), 'cur_cost': 95708.0}, {'tour': array([40, 43, 11,  4, 31, 64,  7, 12, 36, 56, 24, 54, 34,  5, 38, 30, 23,
       28, 55, 29, 16, 37, 58, 50, 47, 59, 49, 44, 35, 42, 52,  8, 51, 57,
       10,  1, 21,  0, 39, 60, 65,  6, 15, 61,  9, 18, 33, 25, 13, 20, 48,
       14, 22, 46, 63, 19, 32, 41, 17, 45, 26, 27, 53,  3, 62,  2],
      dtype=int64), 'cur_cost': 108950.0}, {'tour': array([42,  0, 63, 14, 38, 12, 23, 59, 54, 16,  3, 48, 34, 65, 62, 11, 21,
       18, 56, 29, 30, 53, 64, 22, 45, 61, 46,  7, 28, 39,  5, 60, 43, 36,
       32, 31,  8, 44, 57, 17, 37, 15,  9, 50, 41, 25, 58,  2, 10,  6, 27,
       13, 51,  1, 49, 40, 47,  4, 24, 19, 20, 26, 35, 52, 55, 33],
      dtype=int64), 'cur_cost': 101901.0}]
2025-08-03 16:17:13,176 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-03 16:17:13,176 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-03 16:17:13,177 - experts.management.collaboration_manager - INFO - 个体 13 利用路径生成报告: {'new_tour': array([28, 24, 53,  0, 37, 30, 15, 21, 64,  5, 49, 29,  8, 36, 38,  6, 27,
       52, 16, 63, 14,  2, 32, 47, 23, 54, 61,  4, 60, 34, 55, 57, 12, 65,
       56, 35, 59, 20,  3, 17, 46,  9, 22, 19, 11, 41, 25, 40, 18, 62, 58,
       42,  7, 44, 10,  1, 43, 13, 31, 48, 45, 39, 51, 26, 50, 33],
      dtype=int64), 'cur_cost': 106405.0}
2025-08-03 16:17:13,177 - experts.management.collaboration_manager - INFO - 个体 13 接受新路径 (成本: 106405.00)
2025-08-03 16:17:13,177 - experts.management.collaboration_manager - INFO - 为个体 14 生成探索路径
2025-08-03 16:17:13,178 - ExplorationExpert - INFO - 开始为个体 14 生成探索路径（算法实现）
2025-08-03 16:17:13,178 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:17:13,188 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:17:13,188 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:17:13,189 - ExplorationExpert - INFO - 探索路径生成完成，成本: 90127.0, 路径长度: 66
2025-08-03 16:17:13,189 - experts.management.collaboration_manager - INFO - 个体 14 探索路径生成报告: {'new_tour': [20, 22, 24, 16, 7, 14, 1, 23, 53, 54, 37, 26, 36, 35, 28, 33, 12, 25, 0, 32, 8, 5, 39, 27, 41, 19, 3, 49, 47, 34, 46, 51, 45, 59, 64, 60, 40, 29, 9, 31, 17, 57, 50, 15, 4, 62, 43, 18, 48, 55, 2, 61, 63, 6, 65, 38, 44, 21, 11, 42, 30, 58, 56, 13, 10, 52], 'cur_cost': 90127.0}
2025-08-03 16:17:13,190 - experts.management.collaboration_manager - INFO - 个体 14 接受新路径 (成本: 90127.00)
2025-08-03 16:17:13,190 - experts.management.collaboration_manager - INFO - 为个体 15 生成探索路径
2025-08-03 16:17:13,190 - ExplorationExpert - INFO - 开始为个体 15 生成探索路径（算法实现）
2025-08-03 16:17:13,191 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:17:13,194 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:17:13,195 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:17:13,195 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12371.0, 路径长度: 66
2025-08-03 16:17:13,195 - experts.management.collaboration_manager - INFO - 个体 15 探索路径生成报告: {'new_tour': [0, 15, 23, 13, 20, 21, 19, 16, 18, 12, 22, 17, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12371.0}
2025-08-03 16:17:13,196 - experts.management.collaboration_manager - INFO - 个体 15 接受新路径 (成本: 12371.00)
2025-08-03 16:17:13,196 - experts.management.collaboration_manager - INFO - 为个体 16 生成利用路径
2025-08-03 16:17:13,196 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:17:13,197 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:17:13,198 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 16 处的路径，新成本: 119885.0
2025-08-03 16:17:13,290 - ExploitationExpert - INFO - res_population_num: 6
2025-08-03 16:17:13,291 - ExploitationExpert - INFO - res_population_costs: [9860.0, 9581.0, 9540.0, 9536, 9527, 9526]
2025-08-03 16:17:13,291 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 17, 19, 13, 21, 20, 14, 15, 12, 22, 23, 16,
       18, 24, 29, 32, 28, 30, 35, 34, 26, 25, 31, 33, 37, 36, 27, 42, 48,
       43, 40, 49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57,
       64, 60, 58, 56, 59, 62, 53, 61, 55,  5,  4,  6,  2,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59,
       62, 53, 61, 55, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:17:13,295 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:17:13,295 - ExploitationExpert - INFO - populations: [{'tour': [0, 16, 17, 13, 20, 21, 19, 18, 12, 22, 23, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12459.0}, {'tour': array([55, 36, 62, 53,  3, 49, 23, 34,  4, 45, 30, 17, 58, 15, 50, 64, 46,
       20, 48, 40, 29, 61, 28, 11, 39, 42,  9, 65,  5,  0,  1, 56, 31, 19,
       51,  7, 44, 63, 57,  2, 35, 25, 12, 24, 22, 41, 27, 52, 14, 16, 33,
       43, 10, 18, 54, 21,  6, 47, 38, 26, 59, 32, 37,  8, 13, 60],
      dtype=int64), 'cur_cost': 108665.0}, {'tour': [0, 4, 11, 16, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12478.0}, {'tour': [62, 65, 23, 17, 21, 11, 63, 4, 20, 35, 5, 55, 57, 54, 59, 58, 40, 46, 43, 36, 30, 22, 26, 1, 12, 27, 9, 0, 18, 29, 8, 15, 37, 7, 31, 49, 39, 42, 51, 41, 19, 24, 32, 3, 61, 2, 53, 64, 47, 44, 34, 16, 33, 13, 6, 56, 48, 45, 50, 25, 10, 60, 52, 14, 28, 38], 'cur_cost': 72732.0}, {'tour': array([42, 19,  0, 43,  9, 29, 46, 52, 31,  7, 17,  6, 41,  3, 53, 45, 54,
       58, 12, 18, 37, 65,  2, 56, 60, 23, 33, 25, 48, 39, 47, 49, 27, 59,
       30,  8, 10, 21,  4, 40, 61, 11, 62, 51, 64, 24, 44, 22, 28, 35, 34,
        5, 55, 13, 15, 50, 38, 63, 26, 57, 20,  1, 32, 16, 14, 36],
      dtype=int64), 'cur_cost': 105760.0}, {'tour': [0, 22, 24, 17, 12, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14670.0}, {'tour': [20, 12, 13, 19, 7, 14, 1, 9, 11, 53, 61, 17, 35, 36, 2, 58, 6, 4, 64, 10, 3, 59, 60, 65, 21, 29, 25, 16, 43, 42, 41, 45, 47, 44, 39, 27, 31, 18, 34, 0, 22, 26, 23, 5, 63, 49, 38, 40, 30, 15, 37, 24, 8, 57, 62, 52, 48, 28, 46, 33, 50, 32, 55, 56, 54, 51], 'cur_cost': 65573.0}, {'tour': array([52, 23, 11, 53, 38, 46, 25,  9, 62, 29,  3, 31, 22, 30,  6,  2, 20,
       19, 33, 59, 35, 36, 24, 27, 56, 34,  5, 55, 61, 43, 47, 64, 15, 49,
       14, 17,  7, 21, 51, 10, 45,  8,  4, 28, 48, 42, 63, 44, 18, 13, 54,
       65, 58, 26, 57, 41, 16, 40,  0, 37, 12, 39, 60, 50, 32,  1],
      dtype=int64), 'cur_cost': 105989.0}, {'tour': [0, 16, 13, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12899.0}, {'tour': [62, 17, 7, 20, 1, 11, 55, 6, 10, 37, 46, 3, 28, 34, 57, 27, 25, 0, 15, 8, 45, 47, 31, 41, 24, 61, 2, 38, 33, 59, 43, 50, 52, 13, 9, 39, 36, 54, 14, 65, 42, 35, 29, 19, 32, 56, 4, 5, 64, 44, 63, 12, 51, 40, 23, 22, 49, 53, 48, 18, 30, 58, 16, 26, 21, 60], 'cur_cost': 109649.0}, {'tour': array([11,  9, 61,  8, 60, 34, 30, 22,  6, 15, 18,  7, 19, 41, 26, 48,  5,
        4, 27, 55, 31, 35, 47, 64, 37, 33, 65, 24,  1, 32, 49, 51, 13, 20,
       16,  2, 63, 50, 25, 42, 45, 58, 57, 39, 21, 53, 46, 28, 38, 56, 29,
       44, 54, 40, 23, 17, 43,  0,  3, 62, 59, 12, 36, 52, 14, 10],
      dtype=int64), 'cur_cost': 104428.0}, {'tour': [36, 20, 7, 8, 9, 60, 2, 16, 18, 23, 13, 35, 0, 3, 21, 5, 64, 62, 55, 63, 56, 4, 54, 10, 57, 58, 40, 50, 38, 19, 28, 29, 31, 34, 15, 27, 11, 14, 22, 32, 24, 49, 46, 47, 43, 44, 45, 42, 26, 17, 12, 33, 39, 51, 25, 48, 30, 6, 59, 65, 61, 53, 1, 52, 41, 37], 'cur_cost': 55478.0}, {'tour': [48, 42, 49, 51, 39, 13, 21, 16, 25, 35, 17, 28, 24, 20, 18, 32, 6, 64, 0, 9, 1, 57, 54, 10, 58, 5, 60, 63, 11, 53, 22, 31, 4, 23, 3, 52, 14, 29, 19, 7, 55, 59, 8, 37, 26, 33, 34, 30, 27, 36, 43, 45, 46, 41, 50, 40, 47, 12, 38, 44, 56, 62, 61, 65, 2, 15], 'cur_cost': 49989.0}, {'tour': array([28, 24, 53,  0, 37, 30, 15, 21, 64,  5, 49, 29,  8, 36, 38,  6, 27,
       52, 16, 63, 14,  2, 32, 47, 23, 54, 61,  4, 60, 34, 55, 57, 12, 65,
       56, 35, 59, 20,  3, 17, 46,  9, 22, 19, 11, 41, 25, 40, 18, 62, 58,
       42,  7, 44, 10,  1, 43, 13, 31, 48, 45, 39, 51, 26, 50, 33],
      dtype=int64), 'cur_cost': 106405.0}, {'tour': [20, 22, 24, 16, 7, 14, 1, 23, 53, 54, 37, 26, 36, 35, 28, 33, 12, 25, 0, 32, 8, 5, 39, 27, 41, 19, 3, 49, 47, 34, 46, 51, 45, 59, 64, 60, 40, 29, 9, 31, 17, 57, 50, 15, 4, 62, 43, 18, 48, 55, 2, 61, 63, 6, 65, 38, 44, 21, 11, 42, 30, 58, 56, 13, 10, 52], 'cur_cost': 90127.0}, {'tour': [0, 15, 23, 13, 20, 21, 19, 16, 18, 12, 22, 17, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12371.0}, {'tour': array([ 5, 19, 28, 22, 62,  0, 44, 15, 20, 50,  2, 42, 52, 60, 23, 47,  4,
       25, 57, 33, 49, 58, 17, 48, 65, 34, 31, 18, 46, 64, 61, 27, 59, 14,
        3, 10, 54, 36, 55,  6, 12,  8,  7, 32, 30, 56, 38, 16, 43, 53, 11,
       29, 21, 51, 37, 39, 26, 45, 63, 35, 41, 24,  9, 40,  1, 13],
      dtype=int64), 'cur_cost': 119885.0}, {'tour': array([58, 65, 55, 26, 57, 22, 43, 47, 23, 48, 59, 41, 51, 12,  3, 29, 54,
       61, 42, 20, 64,  0, 31, 46,  6, 15, 49, 53,  7, 60, 44, 17, 52, 19,
       63,  9, 30, 35, 56, 24, 32, 25,  1,  5, 28, 37, 40, 16, 27, 34, 36,
       18,  2,  8, 38, 14,  4, 50, 11, 33, 13, 21, 45, 39, 10, 62],
      dtype=int64), 'cur_cost': 95708.0}, {'tour': array([40, 43, 11,  4, 31, 64,  7, 12, 36, 56, 24, 54, 34,  5, 38, 30, 23,
       28, 55, 29, 16, 37, 58, 50, 47, 59, 49, 44, 35, 42, 52,  8, 51, 57,
       10,  1, 21,  0, 39, 60, 65,  6, 15, 61,  9, 18, 33, 25, 13, 20, 48,
       14, 22, 46, 63, 19, 32, 41, 17, 45, 26, 27, 53,  3, 62,  2],
      dtype=int64), 'cur_cost': 108950.0}, {'tour': array([42,  0, 63, 14, 38, 12, 23, 59, 54, 16,  3, 48, 34, 65, 62, 11, 21,
       18, 56, 29, 30, 53, 64, 22, 45, 61, 46,  7, 28, 39,  5, 60, 43, 36,
       32, 31,  8, 44, 57, 17, 37, 15,  9, 50, 41, 25, 58,  2, 10,  6, 27,
       13, 51,  1, 49, 40, 47,  4, 24, 19, 20, 26, 35, 52, 55, 33],
      dtype=int64), 'cur_cost': 101901.0}]
2025-08-03 16:17:13,304 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒
2025-08-03 16:17:13,304 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-03 16:17:13,304 - experts.management.collaboration_manager - INFO - 个体 16 利用路径生成报告: {'new_tour': array([ 5, 19, 28, 22, 62,  0, 44, 15, 20, 50,  2, 42, 52, 60, 23, 47,  4,
       25, 57, 33, 49, 58, 17, 48, 65, 34, 31, 18, 46, 64, 61, 27, 59, 14,
        3, 10, 54, 36, 55,  6, 12,  8,  7, 32, 30, 56, 38, 16, 43, 53, 11,
       29, 21, 51, 37, 39, 26, 45, 63, 35, 41, 24,  9, 40,  1, 13],
      dtype=int64), 'cur_cost': 119885.0}
2025-08-03 16:17:13,305 - experts.management.collaboration_manager - INFO - 个体 16 接受新路径 (成本: 119885.00)
2025-08-03 16:17:13,305 - experts.management.collaboration_manager - INFO - 为个体 17 生成探索路径
2025-08-03 16:17:13,305 - ExplorationExpert - INFO - 开始为个体 17 生成探索路径（算法实现）
2025-08-03 16:17:13,306 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:17:13,321 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:17:13,322 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:17:13,322 - ExplorationExpert - INFO - 探索路径生成完成，成本: 54534.0, 路径长度: 66
2025-08-03 16:17:13,323 - experts.management.collaboration_manager - INFO - 个体 17 探索路径生成报告: {'new_tour': [49, 36, 19, 13, 3, 2, 60, 17, 26, 23, 11, 12, 24, 15, 1, 5, 18, 10, 14, 37, 9, 4, 27, 35, 25, 8, 54, 22, 20, 29, 30, 28, 46, 43, 40, 50, 21, 34, 44, 32, 31, 33, 51, 39, 7, 55, 61, 65, 64, 52, 53, 63, 6, 58, 62, 0, 59, 56, 57, 16, 48, 47, 38, 45, 41, 42], 'cur_cost': 54534.0}
2025-08-03 16:17:13,324 - experts.management.collaboration_manager - INFO - 个体 17 接受新路径 (成本: 54534.00)
2025-08-03 16:17:13,324 - experts.management.collaboration_manager - INFO - 为个体 18 生成探索路径
2025-08-03 16:17:13,325 - ExplorationExpert - INFO - 开始为个体 18 生成探索路径（算法实现）
2025-08-03 16:17:13,325 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:17:13,329 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:17:13,330 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:17:13,331 - ExplorationExpert - INFO - 探索路径生成完成，成本: 93836.0, 路径长度: 66
2025-08-03 16:17:13,332 - experts.management.collaboration_manager - INFO - 个体 18 探索路径生成报告: {'new_tour': [36, 17, 12, 11, 22, 5, 15, 10, 26, 30, 1, 14, 3, 8, 37, 44, 23, 49, 27, 19, 35, 32, 4, 47, 52, 57, 6, 59, 56, 60, 13, 51, 20, 21, 64, 50, 34, 45, 16, 65, 38, 40, 62, 63, 54, 42, 53, 18, 39, 9, 7, 61, 43, 0, 58, 41, 29, 2, 33, 25, 28, 31, 48, 46, 55, 24], 'cur_cost': 93836.0}
2025-08-03 16:17:13,332 - experts.management.collaboration_manager - INFO - 个体 18 接受新路径 (成本: 93836.00)
2025-08-03 16:17:13,332 - experts.management.collaboration_manager - INFO - 为个体 19 生成利用路径
2025-08-03 16:17:13,333 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:17:13,333 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:17:13,335 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 19 处的路径，新成本: 112677.0
2025-08-03 16:17:13,416 - ExploitationExpert - INFO - res_population_num: 7
2025-08-03 16:17:13,417 - ExploitationExpert - INFO - res_population_costs: [9860.0, 9581.0, 9540.0, 9536, 9527, 9526, 9521]
2025-08-03 16:17:13,417 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9, 17, 19, 13, 21, 20, 14, 15, 12, 22, 23, 16,
       18, 24, 29, 32, 28, 30, 35, 34, 26, 25, 31, 33, 37, 36, 27, 42, 48,
       43, 40, 49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57,
       64, 60, 58, 56, 59, 62, 53, 61, 55,  5,  4,  6,  2,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59,
       62, 53, 61, 55, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:17:13,426 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:17:13,426 - ExploitationExpert - INFO - populations: [{'tour': [0, 16, 17, 13, 20, 21, 19, 18, 12, 22, 23, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12459.0}, {'tour': array([55, 36, 62, 53,  3, 49, 23, 34,  4, 45, 30, 17, 58, 15, 50, 64, 46,
       20, 48, 40, 29, 61, 28, 11, 39, 42,  9, 65,  5,  0,  1, 56, 31, 19,
       51,  7, 44, 63, 57,  2, 35, 25, 12, 24, 22, 41, 27, 52, 14, 16, 33,
       43, 10, 18, 54, 21,  6, 47, 38, 26, 59, 32, 37,  8, 13, 60],
      dtype=int64), 'cur_cost': 108665.0}, {'tour': [0, 4, 11, 16, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12478.0}, {'tour': [62, 65, 23, 17, 21, 11, 63, 4, 20, 35, 5, 55, 57, 54, 59, 58, 40, 46, 43, 36, 30, 22, 26, 1, 12, 27, 9, 0, 18, 29, 8, 15, 37, 7, 31, 49, 39, 42, 51, 41, 19, 24, 32, 3, 61, 2, 53, 64, 47, 44, 34, 16, 33, 13, 6, 56, 48, 45, 50, 25, 10, 60, 52, 14, 28, 38], 'cur_cost': 72732.0}, {'tour': array([42, 19,  0, 43,  9, 29, 46, 52, 31,  7, 17,  6, 41,  3, 53, 45, 54,
       58, 12, 18, 37, 65,  2, 56, 60, 23, 33, 25, 48, 39, 47, 49, 27, 59,
       30,  8, 10, 21,  4, 40, 61, 11, 62, 51, 64, 24, 44, 22, 28, 35, 34,
        5, 55, 13, 15, 50, 38, 63, 26, 57, 20,  1, 32, 16, 14, 36],
      dtype=int64), 'cur_cost': 105760.0}, {'tour': [0, 22, 24, 17, 12, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14670.0}, {'tour': [20, 12, 13, 19, 7, 14, 1, 9, 11, 53, 61, 17, 35, 36, 2, 58, 6, 4, 64, 10, 3, 59, 60, 65, 21, 29, 25, 16, 43, 42, 41, 45, 47, 44, 39, 27, 31, 18, 34, 0, 22, 26, 23, 5, 63, 49, 38, 40, 30, 15, 37, 24, 8, 57, 62, 52, 48, 28, 46, 33, 50, 32, 55, 56, 54, 51], 'cur_cost': 65573.0}, {'tour': array([52, 23, 11, 53, 38, 46, 25,  9, 62, 29,  3, 31, 22, 30,  6,  2, 20,
       19, 33, 59, 35, 36, 24, 27, 56, 34,  5, 55, 61, 43, 47, 64, 15, 49,
       14, 17,  7, 21, 51, 10, 45,  8,  4, 28, 48, 42, 63, 44, 18, 13, 54,
       65, 58, 26, 57, 41, 16, 40,  0, 37, 12, 39, 60, 50, 32,  1],
      dtype=int64), 'cur_cost': 105989.0}, {'tour': [0, 16, 13, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12899.0}, {'tour': [62, 17, 7, 20, 1, 11, 55, 6, 10, 37, 46, 3, 28, 34, 57, 27, 25, 0, 15, 8, 45, 47, 31, 41, 24, 61, 2, 38, 33, 59, 43, 50, 52, 13, 9, 39, 36, 54, 14, 65, 42, 35, 29, 19, 32, 56, 4, 5, 64, 44, 63, 12, 51, 40, 23, 22, 49, 53, 48, 18, 30, 58, 16, 26, 21, 60], 'cur_cost': 109649.0}, {'tour': array([11,  9, 61,  8, 60, 34, 30, 22,  6, 15, 18,  7, 19, 41, 26, 48,  5,
        4, 27, 55, 31, 35, 47, 64, 37, 33, 65, 24,  1, 32, 49, 51, 13, 20,
       16,  2, 63, 50, 25, 42, 45, 58, 57, 39, 21, 53, 46, 28, 38, 56, 29,
       44, 54, 40, 23, 17, 43,  0,  3, 62, 59, 12, 36, 52, 14, 10],
      dtype=int64), 'cur_cost': 104428.0}, {'tour': [36, 20, 7, 8, 9, 60, 2, 16, 18, 23, 13, 35, 0, 3, 21, 5, 64, 62, 55, 63, 56, 4, 54, 10, 57, 58, 40, 50, 38, 19, 28, 29, 31, 34, 15, 27, 11, 14, 22, 32, 24, 49, 46, 47, 43, 44, 45, 42, 26, 17, 12, 33, 39, 51, 25, 48, 30, 6, 59, 65, 61, 53, 1, 52, 41, 37], 'cur_cost': 55478.0}, {'tour': [48, 42, 49, 51, 39, 13, 21, 16, 25, 35, 17, 28, 24, 20, 18, 32, 6, 64, 0, 9, 1, 57, 54, 10, 58, 5, 60, 63, 11, 53, 22, 31, 4, 23, 3, 52, 14, 29, 19, 7, 55, 59, 8, 37, 26, 33, 34, 30, 27, 36, 43, 45, 46, 41, 50, 40, 47, 12, 38, 44, 56, 62, 61, 65, 2, 15], 'cur_cost': 49989.0}, {'tour': array([28, 24, 53,  0, 37, 30, 15, 21, 64,  5, 49, 29,  8, 36, 38,  6, 27,
       52, 16, 63, 14,  2, 32, 47, 23, 54, 61,  4, 60, 34, 55, 57, 12, 65,
       56, 35, 59, 20,  3, 17, 46,  9, 22, 19, 11, 41, 25, 40, 18, 62, 58,
       42,  7, 44, 10,  1, 43, 13, 31, 48, 45, 39, 51, 26, 50, 33],
      dtype=int64), 'cur_cost': 106405.0}, {'tour': [20, 22, 24, 16, 7, 14, 1, 23, 53, 54, 37, 26, 36, 35, 28, 33, 12, 25, 0, 32, 8, 5, 39, 27, 41, 19, 3, 49, 47, 34, 46, 51, 45, 59, 64, 60, 40, 29, 9, 31, 17, 57, 50, 15, 4, 62, 43, 18, 48, 55, 2, 61, 63, 6, 65, 38, 44, 21, 11, 42, 30, 58, 56, 13, 10, 52], 'cur_cost': 90127.0}, {'tour': [0, 15, 23, 13, 20, 21, 19, 16, 18, 12, 22, 17, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12371.0}, {'tour': array([ 5, 19, 28, 22, 62,  0, 44, 15, 20, 50,  2, 42, 52, 60, 23, 47,  4,
       25, 57, 33, 49, 58, 17, 48, 65, 34, 31, 18, 46, 64, 61, 27, 59, 14,
        3, 10, 54, 36, 55,  6, 12,  8,  7, 32, 30, 56, 38, 16, 43, 53, 11,
       29, 21, 51, 37, 39, 26, 45, 63, 35, 41, 24,  9, 40,  1, 13],
      dtype=int64), 'cur_cost': 119885.0}, {'tour': [49, 36, 19, 13, 3, 2, 60, 17, 26, 23, 11, 12, 24, 15, 1, 5, 18, 10, 14, 37, 9, 4, 27, 35, 25, 8, 54, 22, 20, 29, 30, 28, 46, 43, 40, 50, 21, 34, 44, 32, 31, 33, 51, 39, 7, 55, 61, 65, 64, 52, 53, 63, 6, 58, 62, 0, 59, 56, 57, 16, 48, 47, 38, 45, 41, 42], 'cur_cost': 54534.0}, {'tour': [36, 17, 12, 11, 22, 5, 15, 10, 26, 30, 1, 14, 3, 8, 37, 44, 23, 49, 27, 19, 35, 32, 4, 47, 52, 57, 6, 59, 56, 60, 13, 51, 20, 21, 64, 50, 34, 45, 16, 65, 38, 40, 62, 63, 54, 42, 53, 18, 39, 9, 7, 61, 43, 0, 58, 41, 29, 2, 33, 25, 28, 31, 48, 46, 55, 24], 'cur_cost': 93836.0}, {'tour': array([29, 46, 26, 24, 31, 40, 42, 64, 39, 49, 15, 59, 30, 12, 45, 22, 52,
       14, 10, 19, 57, 33,  9, 50, 38, 34, 48, 11, 20,  8, 56, 43,  5, 36,
       61, 16,  7, 18, 21, 13, 65, 62, 27, 54, 60, 37, 58, 17,  3, 55,  4,
        0, 41, 44, 25, 32,  2, 47,  1, 28, 53, 23, 35, 63, 51,  6],
      dtype=int64), 'cur_cost': 112677.0}]
2025-08-03 16:17:13,433 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 16:17:13,433 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-03 16:17:13,434 - experts.management.collaboration_manager - INFO - 个体 19 利用路径生成报告: {'new_tour': array([29, 46, 26, 24, 31, 40, 42, 64, 39, 49, 15, 59, 30, 12, 45, 22, 52,
       14, 10, 19, 57, 33,  9, 50, 38, 34, 48, 11, 20,  8, 56, 43,  5, 36,
       61, 16,  7, 18, 21, 13, 65, 62, 27, 54, 60, 37, 58, 17,  3, 55,  4,
        0, 41, 44, 25, 32,  2, 47,  1, 28, 53, 23, 35, 63, 51,  6],
      dtype=int64), 'cur_cost': 112677.0}
2025-08-03 16:17:13,434 - experts.management.collaboration_manager - INFO - 个体 19 接受新路径 (成本: 112677.00)
2025-08-03 16:17:13,435 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 16, 'rejected': 4, 'elite_protected': 4}
2025-08-03 16:17:13,435 - experts.management.collaboration_manager - INFO - 接受率: 16/20 (80.0%)
2025-08-03 16:17:13,436 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 17, 13, 20, 21, 19, 18, 12, 22, 23, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12459.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([55, 36, 62, 53,  3, 49, 23, 34,  4, 45, 30, 17, 58, 15, 50, 64, 46,
       20, 48, 40, 29, 61, 28, 11, 39, 42,  9, 65,  5,  0,  1, 56, 31, 19,
       51,  7, 44, 63, 57,  2, 35, 25, 12, 24, 22, 41, 27, 52, 14, 16, 33,
       43, 10, 18, 54, 21,  6, 47, 38, 26, 59, 32, 37,  8, 13, 60],
      dtype=int64), 'cur_cost': 108665.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 11, 16, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12478.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [62, 65, 23, 17, 21, 11, 63, 4, 20, 35, 5, 55, 57, 54, 59, 58, 40, 46, 43, 36, 30, 22, 26, 1, 12, 27, 9, 0, 18, 29, 8, 15, 37, 7, 31, 49, 39, 42, 51, 41, 19, 24, 32, 3, 61, 2, 53, 64, 47, 44, 34, 16, 33, 13, 6, 56, 48, 45, 50, 25, 10, 60, 52, 14, 28, 38], 'cur_cost': 72732.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([42, 19,  0, 43,  9, 29, 46, 52, 31,  7, 17,  6, 41,  3, 53, 45, 54,
       58, 12, 18, 37, 65,  2, 56, 60, 23, 33, 25, 48, 39, 47, 49, 27, 59,
       30,  8, 10, 21,  4, 40, 61, 11, 62, 51, 64, 24, 44, 22, 28, 35, 34,
        5, 55, 13, 15, 50, 38, 63, 26, 57, 20,  1, 32, 16, 14, 36],
      dtype=int64), 'cur_cost': 105760.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 22, 24, 17, 12, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14670.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [20, 12, 13, 19, 7, 14, 1, 9, 11, 53, 61, 17, 35, 36, 2, 58, 6, 4, 64, 10, 3, 59, 60, 65, 21, 29, 25, 16, 43, 42, 41, 45, 47, 44, 39, 27, 31, 18, 34, 0, 22, 26, 23, 5, 63, 49, 38, 40, 30, 15, 37, 24, 8, 57, 62, 52, 48, 28, 46, 33, 50, 32, 55, 56, 54, 51], 'cur_cost': 65573.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([52, 23, 11, 53, 38, 46, 25,  9, 62, 29,  3, 31, 22, 30,  6,  2, 20,
       19, 33, 59, 35, 36, 24, 27, 56, 34,  5, 55, 61, 43, 47, 64, 15, 49,
       14, 17,  7, 21, 51, 10, 45,  8,  4, 28, 48, 42, 63, 44, 18, 13, 54,
       65, 58, 26, 57, 41, 16, 40,  0, 37, 12, 39, 60, 50, 32,  1],
      dtype=int64), 'cur_cost': 105989.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 13, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12899.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [62, 17, 7, 20, 1, 11, 55, 6, 10, 37, 46, 3, 28, 34, 57, 27, 25, 0, 15, 8, 45, 47, 31, 41, 24, 61, 2, 38, 33, 59, 43, 50, 52, 13, 9, 39, 36, 54, 14, 65, 42, 35, 29, 19, 32, 56, 4, 5, 64, 44, 63, 12, 51, 40, 23, 22, 49, 53, 48, 18, 30, 58, 16, 26, 21, 60], 'cur_cost': 109649.0}}, {'individual': 10, 'strategy': 'exploit', 'path_data': {'new_tour': array([11,  9, 61,  8, 60, 34, 30, 22,  6, 15, 18,  7, 19, 41, 26, 48,  5,
        4, 27, 55, 31, 35, 47, 64, 37, 33, 65, 24,  1, 32, 49, 51, 13, 20,
       16,  2, 63, 50, 25, 42, 45, 58, 57, 39, 21, 53, 46, 28, 38, 56, 29,
       44, 54, 40, 23, 17, 43,  0,  3, 62, 59, 12, 36, 52, 14, 10],
      dtype=int64), 'cur_cost': 104428.0}}, {'individual': 11, 'strategy': 'explore', 'path_data': {'new_tour': [36, 20, 7, 8, 9, 60, 2, 16, 18, 23, 13, 35, 0, 3, 21, 5, 64, 62, 55, 63, 56, 4, 54, 10, 57, 58, 40, 50, 38, 19, 28, 29, 31, 34, 15, 27, 11, 14, 22, 32, 24, 49, 46, 47, 43, 44, 45, 42, 26, 17, 12, 33, 39, 51, 25, 48, 30, 6, 59, 65, 61, 53, 1, 52, 41, 37], 'cur_cost': 55478.0}}, {'individual': 12, 'strategy': 'explore', 'path_data': {'new_tour': [48, 42, 49, 51, 39, 13, 21, 16, 25, 35, 17, 28, 24, 20, 18, 32, 6, 64, 0, 9, 1, 57, 54, 10, 58, 5, 60, 63, 11, 53, 22, 31, 4, 23, 3, 52, 14, 29, 19, 7, 55, 59, 8, 37, 26, 33, 34, 30, 27, 36, 43, 45, 46, 41, 50, 40, 47, 12, 38, 44, 56, 62, 61, 65, 2, 15], 'cur_cost': 49989.0}}, {'individual': 13, 'strategy': 'exploit', 'path_data': {'new_tour': array([28, 24, 53,  0, 37, 30, 15, 21, 64,  5, 49, 29,  8, 36, 38,  6, 27,
       52, 16, 63, 14,  2, 32, 47, 23, 54, 61,  4, 60, 34, 55, 57, 12, 65,
       56, 35, 59, 20,  3, 17, 46,  9, 22, 19, 11, 41, 25, 40, 18, 62, 58,
       42,  7, 44, 10,  1, 43, 13, 31, 48, 45, 39, 51, 26, 50, 33],
      dtype=int64), 'cur_cost': 106405.0}}, {'individual': 14, 'strategy': 'explore', 'path_data': {'new_tour': [20, 22, 24, 16, 7, 14, 1, 23, 53, 54, 37, 26, 36, 35, 28, 33, 12, 25, 0, 32, 8, 5, 39, 27, 41, 19, 3, 49, 47, 34, 46, 51, 45, 59, 64, 60, 40, 29, 9, 31, 17, 57, 50, 15, 4, 62, 43, 18, 48, 55, 2, 61, 63, 6, 65, 38, 44, 21, 11, 42, 30, 58, 56, 13, 10, 52], 'cur_cost': 90127.0}}, {'individual': 15, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 23, 13, 20, 21, 19, 16, 18, 12, 22, 17, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12371.0}}, {'individual': 16, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 5, 19, 28, 22, 62,  0, 44, 15, 20, 50,  2, 42, 52, 60, 23, 47,  4,
       25, 57, 33, 49, 58, 17, 48, 65, 34, 31, 18, 46, 64, 61, 27, 59, 14,
        3, 10, 54, 36, 55,  6, 12,  8,  7, 32, 30, 56, 38, 16, 43, 53, 11,
       29, 21, 51, 37, 39, 26, 45, 63, 35, 41, 24,  9, 40,  1, 13],
      dtype=int64), 'cur_cost': 119885.0}}, {'individual': 17, 'strategy': 'explore', 'path_data': {'new_tour': [49, 36, 19, 13, 3, 2, 60, 17, 26, 23, 11, 12, 24, 15, 1, 5, 18, 10, 14, 37, 9, 4, 27, 35, 25, 8, 54, 22, 20, 29, 30, 28, 46, 43, 40, 50, 21, 34, 44, 32, 31, 33, 51, 39, 7, 55, 61, 65, 64, 52, 53, 63, 6, 58, 62, 0, 59, 56, 57, 16, 48, 47, 38, 45, 41, 42], 'cur_cost': 54534.0}}, {'individual': 18, 'strategy': 'explore', 'path_data': {'new_tour': [36, 17, 12, 11, 22, 5, 15, 10, 26, 30, 1, 14, 3, 8, 37, 44, 23, 49, 27, 19, 35, 32, 4, 47, 52, 57, 6, 59, 56, 60, 13, 51, 20, 21, 64, 50, 34, 45, 16, 65, 38, 40, 62, 63, 54, 42, 53, 18, 39, 9, 7, 61, 43, 0, 58, 41, 29, 2, 33, 25, 28, 31, 48, 46, 55, 24], 'cur_cost': 93836.0}}, {'individual': 19, 'strategy': 'exploit', 'path_data': {'new_tour': array([29, 46, 26, 24, 31, 40, 42, 64, 39, 49, 15, 59, 30, 12, 45, 22, 52,
       14, 10, 19, 57, 33,  9, 50, 38, 34, 48, 11, 20,  8, 56, 43,  5, 36,
       61, 16,  7, 18, 21, 13, 65, 62, 27, 54, 60, 37, 58, 17,  3, 55,  4,
        0, 41, 44, 25, 32,  2, 47,  1, 28, 53, 23, 35, 63, 51,  6],
      dtype=int64), 'cur_cost': 112677.0}}]
2025-08-03 16:17:13,440 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-03 16:17:13,440 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:17:13,456 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=12371.000, 多样性=0.964
2025-08-03 16:17:13,457 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-03 16:17:13,457 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-03 16:17:13,457 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-03 16:17:13,458 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 1, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.06883481205066243, 'best_improvement': -0.2508594539939333}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.0024766779493106137}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 7, 'new_count': 7, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.8672438672438673, 'new_diversity': 0.8672438672438673, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-03 16:17:13,459 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-03 16:17:13,462 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_solution.json
2025-08-03 16:17:13,462 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_20250803_161713.solution
2025-08-03 16:17:13,463 - __main__ - INFO - 实例 composite13_66 处理完成
