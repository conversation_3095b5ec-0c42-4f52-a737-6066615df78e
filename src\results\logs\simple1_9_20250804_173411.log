2025-08-04 17:34:11,234 - __main__ - INFO - simple1_9 开始进化第 1 代
2025-08-04 17:34:11,234 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-04 17:34:11,235 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:34:11,236 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=681.000, 多样性=0.867
2025-08-04 17:34:11,237 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:34:11,237 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.867
2025-08-04 17:34:11,238 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:34:11,240 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-04 17:34:11,240 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:34:11,240 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-04 17:34:11,241 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-04 17:34:11,408 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -20.540, 聚类评分: 0.000, 覆盖率: 0.001, 收敛趋势: 0.000, 多样性: 0.780
2025-08-04 17:34:11,408 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-04 17:34:11,408 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-04 17:34:11,409 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:34:11,415 - visualization.landscape_visualizer - INFO - 插值约束: 131 个点被约束到最小值 681.00
2025-08-04 17:34:11,460 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记，坐标系统已统一
2025-08-04 17:34:11,965 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_1_20250804_173411.html
2025-08-04 17:34:12,062 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_1_20250804_173411.html
2025-08-04 17:34:12,063 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-04 17:34:12,063 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-04 17:34:12,063 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.8232秒
2025-08-04 17:34:12,064 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-04 17:34:12,064 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -20.540000000000003, 'local_optima_density': 0.1, 'gradient_variance': 13253.616399999999, 'cluster_count': 0}, 'population_state': {'diversity': 0.78, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0009, 'fitness_entropy': 0.9349775297671233, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -20.540)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.001)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.780)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754300051.408216, 'performance_metrics': {}}}
2025-08-04 17:34:12,064 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:34:12,064 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:34:12,064 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:34:12,065 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:34:12,065 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-04 17:34:12,065 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-04 17:34:12,065 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-04 17:34:12,066 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:34:12,066 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:34:12,066 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-04 17:34:12,066 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:34:12,067 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:34:12,067 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-04 17:34:12,067 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:34:12,067 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:34:12,067 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:12,068 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:34:12,068 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:12,215 - ExplorationExpert - INFO - 探索路径生成完成，成本: 921.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:34:12,215 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [2, 3, 4, 7, 5, 6, 0, 1, 8], 'cur_cost': 921.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:34:12,216 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 921.00)
2025-08-04 17:34:12,216 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:34:12,216 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:34:12,216 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:12,217 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:34:12,217 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:12,217 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1040.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:34:12,218 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [5, 7, 1, 4, 8, 6, 2, 0, 3], 'cur_cost': 1040.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:34:12,218 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 1040.00)
2025-08-04 17:34:12,218 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:34:12,218 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:34:12,219 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:12,219 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:34:12,219 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:12,219 - ExplorationExpert - INFO - 探索路径生成完成，成本: 718.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:34:12,219 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [5, 8, 2, 4, 1, 0, 7, 3, 6], 'cur_cost': 718.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:34:12,220 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 718.00)
2025-08-04 17:34:12,220 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:34:12,220 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:34:12,220 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:12,221 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:34:12,221 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:12,221 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1051.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:34:12,221 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [7, 5, 3, 0, 4, 8, 1, 6, 2], 'cur_cost': 1051.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:34:12,222 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1051.00)
2025-08-04 17:34:12,222 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-04 17:34:12,222 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:34:12,224 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:34:12,224 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 1111.0
2025-08-04 17:34:13,866 - ExploitationExpert - INFO - res_population_num: 1
2025-08-04 17:34:13,866 - ExploitationExpert - INFO - res_population_costs: [736.0]
2025-08-04 17:34:13,866 - ExploitationExpert - INFO - res_populations: [array([0, 7, 2, 4, 8, 3, 5, 6, 1], dtype=int64)]
2025-08-04 17:34:13,867 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:34:13,867 - ExploitationExpert - INFO - populations: [{'tour': [2, 3, 4, 7, 5, 6, 0, 1, 8], 'cur_cost': 921.0}, {'tour': [5, 7, 1, 4, 8, 6, 2, 0, 3], 'cur_cost': 1040.0}, {'tour': [5, 8, 2, 4, 1, 0, 7, 3, 6], 'cur_cost': 718.0}, {'tour': [7, 5, 3, 0, 4, 8, 1, 6, 2], 'cur_cost': 1051.0}, {'tour': array([2, 4, 6, 7, 5, 0, 8, 1, 3], dtype=int64), 'cur_cost': 1111.0}, {'tour': array([7, 1, 3, 5, 2, 0, 4, 8, 6], dtype=int64), 'cur_cost': 1053.0}, {'tour': array([2, 3, 8, 5, 7, 0, 4, 1, 6], dtype=int64), 'cur_cost': 981.0}, {'tour': array([8, 4, 7, 0, 1, 6, 2, 5, 3], dtype=int64), 'cur_cost': 937.0}, {'tour': array([8, 6, 7, 5, 4, 1, 2, 0, 3], dtype=int64), 'cur_cost': 1008.0}, {'tour': array([5, 6, 0, 1, 3, 4, 8, 2, 7], dtype=int64), 'cur_cost': 892.0}]
2025-08-04 17:34:13,868 - ExploitationExpert - INFO - 局部搜索耗时: 1.64秒，最大迭代次数: 10
2025-08-04 17:34:13,868 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-04 17:34:13,869 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([2, 4, 6, 7, 5, 0, 8, 1, 3], dtype=int64), 'cur_cost': 1111.0, 'intermediate_solutions': [{'tour': array([4, 1, 8, 6, 0, 7, 2, 5, 3], dtype=int64), 'cur_cost': 1090.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 4, 1, 8, 0, 7, 2, 5, 3], dtype=int64), 'cur_cost': 1118.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 6, 4, 1, 8, 7, 2, 5, 3], dtype=int64), 'cur_cost': 1104.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 6, 4, 1, 0, 7, 2, 5, 3], dtype=int64), 'cur_cost': 966.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 0, 6, 4, 1, 7, 2, 5, 3], dtype=int64), 'cur_cost': 1058.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:34:13,870 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1111.00)
2025-08-04 17:34:13,870 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-04 17:34:13,870 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:34:13,870 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:34:13,870 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 905.0
2025-08-04 17:34:15,783 - ExploitationExpert - INFO - res_population_num: 2
2025-08-04 17:34:15,783 - ExploitationExpert - INFO - res_population_costs: [736.0, 680.0]
2025-08-04 17:34:15,784 - ExploitationExpert - INFO - res_populations: [array([0, 7, 2, 4, 8, 3, 5, 6, 1], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:34:15,784 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:34:15,785 - ExploitationExpert - INFO - populations: [{'tour': [2, 3, 4, 7, 5, 6, 0, 1, 8], 'cur_cost': 921.0}, {'tour': [5, 7, 1, 4, 8, 6, 2, 0, 3], 'cur_cost': 1040.0}, {'tour': [5, 8, 2, 4, 1, 0, 7, 3, 6], 'cur_cost': 718.0}, {'tour': [7, 5, 3, 0, 4, 8, 1, 6, 2], 'cur_cost': 1051.0}, {'tour': array([2, 4, 6, 7, 5, 0, 8, 1, 3], dtype=int64), 'cur_cost': 1111.0}, {'tour': array([1, 6, 0, 2, 4, 7, 5, 8, 3], dtype=int64), 'cur_cost': 905.0}, {'tour': array([2, 3, 8, 5, 7, 0, 4, 1, 6], dtype=int64), 'cur_cost': 981.0}, {'tour': array([8, 4, 7, 0, 1, 6, 2, 5, 3], dtype=int64), 'cur_cost': 937.0}, {'tour': array([8, 6, 7, 5, 4, 1, 2, 0, 3], dtype=int64), 'cur_cost': 1008.0}, {'tour': array([5, 6, 0, 1, 3, 4, 8, 2, 7], dtype=int64), 'cur_cost': 892.0}]
2025-08-04 17:34:15,786 - ExploitationExpert - INFO - 局部搜索耗时: 1.92秒，最大迭代次数: 10
2025-08-04 17:34:15,786 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-04 17:34:15,787 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([1, 6, 0, 2, 4, 7, 5, 8, 3], dtype=int64), 'cur_cost': 905.0, 'intermediate_solutions': [{'tour': array([3, 1, 7, 5, 2, 0, 4, 8, 6], dtype=int64), 'cur_cost': 1081.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 3, 1, 7, 2, 0, 4, 8, 6], dtype=int64), 'cur_cost': 999.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 5, 3, 1, 7, 0, 4, 8, 6], dtype=int64), 'cur_cost': 1118.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 5, 3, 1, 2, 0, 4, 8, 6], dtype=int64), 'cur_cost': 964.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 2, 5, 3, 1, 0, 4, 8, 6], dtype=int64), 'cur_cost': 974.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:34:15,788 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 905.00)
2025-08-04 17:34:15,788 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:34:15,788 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:34:15,788 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:15,789 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:34:15,789 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:15,789 - ExplorationExpert - INFO - 探索路径生成完成，成本: 928.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:34:15,789 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [7, 8, 4, 0, 6, 5, 3, 1, 2], 'cur_cost': 928.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:34:15,790 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 928.00)
2025-08-04 17:34:15,790 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:34:15,790 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:34:15,790 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:15,791 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:34:15,791 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:15,791 - ExplorationExpert - INFO - 探索路径生成完成，成本: 940.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:34:15,791 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [3, 8, 5, 0, 4, 2, 7, 6, 1], 'cur_cost': 940.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:34:15,791 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 940.00)
2025-08-04 17:34:15,792 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:34:15,792 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:34:15,792 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:15,792 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:34:15,793 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:15,793 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1038.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:34:15,793 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [3, 7, 1, 8, 5, 0, 6, 4, 2], 'cur_cost': 1038.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:34:15,793 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1038.00)
2025-08-04 17:34:15,793 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:34:15,793 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:34:15,794 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:15,794 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:34:15,794 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:15,794 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1074.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:34:15,795 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [3, 2, 8, 6, 0, 4, 5, 7, 1], 'cur_cost': 1074.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:34:15,795 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1074.00)
2025-08-04 17:34:15,795 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:34:15,795 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:34:15,796 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [2, 3, 4, 7, 5, 6, 0, 1, 8], 'cur_cost': 921.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [5, 7, 1, 4, 8, 6, 2, 0, 3], 'cur_cost': 1040.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [5, 8, 2, 4, 1, 0, 7, 3, 6], 'cur_cost': 718.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [7, 5, 3, 0, 4, 8, 1, 6, 2], 'cur_cost': 1051.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 4, 6, 7, 5, 0, 8, 1, 3], dtype=int64), 'cur_cost': 1111.0, 'intermediate_solutions': [{'tour': array([4, 1, 8, 6, 0, 7, 2, 5, 3], dtype=int64), 'cur_cost': 1090.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 4, 1, 8, 0, 7, 2, 5, 3], dtype=int64), 'cur_cost': 1118.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 6, 4, 1, 8, 7, 2, 5, 3], dtype=int64), 'cur_cost': 1104.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 6, 4, 1, 0, 7, 2, 5, 3], dtype=int64), 'cur_cost': 966.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 0, 6, 4, 1, 7, 2, 5, 3], dtype=int64), 'cur_cost': 1058.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([1, 6, 0, 2, 4, 7, 5, 8, 3], dtype=int64), 'cur_cost': 905.0, 'intermediate_solutions': [{'tour': array([3, 1, 7, 5, 2, 0, 4, 8, 6], dtype=int64), 'cur_cost': 1081.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 3, 1, 7, 2, 0, 4, 8, 6], dtype=int64), 'cur_cost': 999.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 5, 3, 1, 7, 0, 4, 8, 6], dtype=int64), 'cur_cost': 1118.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 5, 3, 1, 2, 0, 4, 8, 6], dtype=int64), 'cur_cost': 964.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 2, 5, 3, 1, 0, 4, 8, 6], dtype=int64), 'cur_cost': 974.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [7, 8, 4, 0, 6, 5, 3, 1, 2], 'cur_cost': 928.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [3, 8, 5, 0, 4, 2, 7, 6, 1], 'cur_cost': 940.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [3, 7, 1, 8, 5, 0, 6, 4, 2], 'cur_cost': 1038.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [3, 2, 8, 6, 0, 4, 5, 7, 1], 'cur_cost': 1074.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-04 17:34:15,798 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:34:15,798 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:34:15,799 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=718.000, 多样性=0.901
2025-08-04 17:34:15,799 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-04 17:34:15,799 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-04 17:34:15,800 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:34:15,800 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.04762007034186977, 'best_improvement': -0.05433186490455213}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.03988603988603986}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.8888888888888888, 'new_diversity': 0.8888888888888888, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-04 17:34:15,801 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-04 17:34:15,801 - __main__ - INFO - simple1_9 开始进化第 2 代
2025-08-04 17:34:15,801 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-04 17:34:15,802 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:34:15,803 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=718.000, 多样性=0.901
2025-08-04 17:34:15,803 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:34:15,804 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.901
2025-08-04 17:34:15,804 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:34:15,805 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.889
2025-08-04 17:34:15,806 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-04 17:34:15,807 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:34:15,807 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-04 17:34:15,807 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-04 17:34:15,813 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.250, 适应度梯度: 12.150, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 0.679
2025-08-04 17:34:15,814 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-04 17:34:15,814 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:34:15,814 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:34:15,821 - visualization.landscape_visualizer - INFO - 插值约束: 88 个点被约束到最小值 680.00
2025-08-04 17:34:15,825 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:34:15,907 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_2_20250804_173415.html
2025-08-04 17:34:15,955 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_2_20250804_173415.html
2025-08-04 17:34:15,955 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 2
2025-08-04 17:34:15,956 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-04 17:34:15,956 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1500秒
2025-08-04 17:34:15,957 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.25, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 12.150000000000004, 'local_optima_density': 0.25, 'gradient_variance': 26770.3075, 'cluster_count': 0}, 'population_state': {'diversity': 0.6792929292929294, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0021, 'fitness_entropy': 0.9513282751069652, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 12.150)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754300055.8143306, 'performance_metrics': {}}}
2025-08-04 17:34:15,958 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:34:15,958 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:34:15,958 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:34:15,959 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:34:15,959 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-04 17:34:15,959 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-04 17:34:15,960 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-04 17:34:15,960 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:34:15,961 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:34:15,961 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-04 17:34:15,961 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:34:15,961 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:34:15,961 - experts.management.collaboration_manager - INFO - 识别精英个体: {2, 5} (总数: 2, 保护比例: 0.20)
2025-08-04 17:34:15,962 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:34:15,962 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:34:15,962 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:15,963 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:34:15,963 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:15,964 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:15,964 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:15,964 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:15,965 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1112.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:34:15,965 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [1, 3, 2, 8, 6, 7, 0, 4, 5], 'cur_cost': 1112.0, 'intermediate_solutions': [{'tour': [2, 3, 6, 7, 5, 4, 0, 1, 8], 'cur_cost': 947.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 3, 2, 7, 5, 6, 0, 1, 8], 'cur_cost': 952.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 3, 4, 7, 5, 6, 1, 8, 0], 'cur_cost': 1064.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:34:15,966 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1112.00)
2025-08-04 17:34:15,966 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:34:15,967 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:34:15,967 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:15,968 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:34:15,968 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:15,968 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:15,969 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:15,969 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:15,969 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1109.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:34:15,969 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [1, 5, 2, 3, 6, 7, 0, 4, 8], 'cur_cost': 1109.0, 'intermediate_solutions': [{'tour': [5, 3, 1, 4, 8, 6, 2, 0, 7], 'cur_cost': 1040.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 7, 8, 4, 1, 6, 2, 0, 3], 'cur_cost': 980.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 0, 1, 4, 8, 6, 2, 3], 'cur_cost': 940.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:34:15,970 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1109.00)
2025-08-04 17:34:15,970 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:34:15,970 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:34:15,971 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:15,971 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:34:15,971 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:15,972 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:15,972 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:15,972 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:15,972 - ExplorationExpert - INFO - 探索路径生成完成，成本: 879.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:34:15,973 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [3, 4, 7, 0, 1, 6, 5, 8, 2], 'cur_cost': 879.0, 'intermediate_solutions': [{'tour': [5, 3, 2, 4, 1, 0, 7, 8, 6], 'cur_cost': 796.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 3, 7, 0, 1, 4, 2, 8, 6], 'cur_cost': 721.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 8, 6, 2, 4, 1, 0, 7, 3], 'cur_cost': 839.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:34:15,973 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 879.00)
2025-08-04 17:34:15,973 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:34:15,973 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:34:15,974 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:15,974 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:34:15,974 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:15,974 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:15,975 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:15,975 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:15,975 - ExplorationExpert - INFO - 探索路径生成完成，成本: 718.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:34:15,976 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [3, 7, 0, 1, 4, 2, 8, 5, 6], 'cur_cost': 718.0, 'intermediate_solutions': [{'tour': [7, 5, 3, 0, 4, 1, 8, 6, 2], 'cur_cost': 1084.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 3, 0, 4, 8, 1, 2, 6], 'cur_cost': 1021.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 5, 3, 0, 4, 1, 6, 8, 2], 'cur_cost': 938.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:34:15,976 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 718.00)
2025-08-04 17:34:15,976 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-04 17:34:15,977 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:34:15,977 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:34:15,977 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 1014.0
2025-08-04 17:34:16,484 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:34:16,484 - ExploitationExpert - INFO - res_population_costs: [680.0, 736.0, 680.0]
2025-08-04 17:34:16,485 - ExploitationExpert - INFO - res_populations: [array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 7, 2, 4, 8, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-04 17:34:16,486 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:34:16,486 - ExploitationExpert - INFO - populations: [{'tour': [1, 3, 2, 8, 6, 7, 0, 4, 5], 'cur_cost': 1112.0}, {'tour': [1, 5, 2, 3, 6, 7, 0, 4, 8], 'cur_cost': 1109.0}, {'tour': [3, 4, 7, 0, 1, 6, 5, 8, 2], 'cur_cost': 879.0}, {'tour': [3, 7, 0, 1, 4, 2, 8, 5, 6], 'cur_cost': 718.0}, {'tour': array([4, 5, 6, 3, 2, 8, 0, 7, 1], dtype=int64), 'cur_cost': 1014.0}, {'tour': [1, 6, 0, 2, 4, 7, 5, 8, 3], 'cur_cost': 905.0}, {'tour': [7, 8, 4, 0, 6, 5, 3, 1, 2], 'cur_cost': 928.0}, {'tour': [3, 8, 5, 0, 4, 2, 7, 6, 1], 'cur_cost': 940.0}, {'tour': [3, 7, 1, 8, 5, 0, 6, 4, 2], 'cur_cost': 1038.0}, {'tour': [3, 2, 8, 6, 0, 4, 5, 7, 1], 'cur_cost': 1074.0}]
2025-08-04 17:34:16,487 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒，最大迭代次数: 10
2025-08-04 17:34:16,487 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-04 17:34:16,487 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([4, 5, 6, 3, 2, 8, 0, 7, 1], dtype=int64), 'cur_cost': 1014.0, 'intermediate_solutions': [{'tour': array([6, 4, 2, 7, 5, 0, 8, 1, 3]), 'cur_cost': 1111.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 6, 4, 2, 5, 0, 8, 1, 3]), 'cur_cost': 1124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 7, 6, 4, 2, 0, 8, 1, 3]), 'cur_cost': 1023.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 7, 6, 4, 5, 0, 8, 1, 3]), 'cur_cost': 1257.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 5, 7, 6, 4, 0, 8, 1, 3]), 'cur_cost': 1186.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:34:16,488 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1014.00)
2025-08-04 17:34:16,488 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:34:16,488 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:34:16,488 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:16,489 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:34:16,489 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,489 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,489 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,489 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,490 - ExplorationExpert - INFO - 探索路径生成完成，成本: 902.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:34:16,490 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 3, 4, 6, 5, 7, 8, 2, 1], 'cur_cost': 902.0, 'intermediate_solutions': [{'tour': [5, 6, 0, 2, 4, 7, 1, 8, 3], 'cur_cost': 884.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 1, 0, 2, 4, 7, 5, 8, 3], 'cur_cost': 786.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 2, 1, 4, 7, 5, 8, 3], 'cur_cost': 911.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:34:16,490 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 902.00)
2025-08-04 17:34:16,490 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:34:16,490 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:34:16,491 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:16,491 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:34:16,491 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,491 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,492 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,492 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,492 - ExplorationExpert - INFO - 探索路径生成完成，成本: 844.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:34:16,492 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [4, 7, 5, 3, 6, 8, 2, 0, 1], 'cur_cost': 844.0, 'intermediate_solutions': [{'tour': [7, 1, 4, 0, 6, 5, 3, 8, 2], 'cur_cost': 854.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 8, 4, 0, 2, 1, 3, 5, 6], 'cur_cost': 909.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 8, 4, 0, 6, 1, 5, 3, 2], 'cur_cost': 989.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:34:16,493 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 844.00)
2025-08-04 17:34:16,493 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:34:16,493 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:34:16,493 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:16,494 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:34:16,494 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,494 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,494 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,494 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,494 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1014.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:34:16,495 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [3, 8, 1, 2, 0, 7, 5, 6, 4], 'cur_cost': 1014.0, 'intermediate_solutions': [{'tour': [3, 8, 5, 0, 4, 6, 7, 2, 1], 'cur_cost': 1053.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 5, 6, 7, 2, 4, 0, 1], 'cur_cost': 806.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 8, 5, 0, 2, 4, 7, 6, 1], 'cur_cost': 946.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:34:16,495 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1014.00)
2025-08-04 17:34:16,495 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:34:16,496 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:34:16,496 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:16,496 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:34:16,497 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,497 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,497 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,497 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,498 - ExplorationExpert - INFO - 探索路径生成完成，成本: 956.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:34:16,498 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [3, 5, 2, 8, 7, 6, 0, 4, 1], 'cur_cost': 956.0, 'intermediate_solutions': [{'tour': [3, 5, 1, 8, 7, 0, 6, 4, 2], 'cur_cost': 1024.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 7, 1, 8, 5, 0, 4, 6, 2], 'cur_cost': 1160.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 7, 8, 1, 5, 0, 6, 4, 2], 'cur_cost': 1066.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:34:16,499 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 956.00)
2025-08-04 17:34:16,499 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-04 17:34:16,499 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:34:16,500 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:34:16,500 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 1006.0
2025-08-04 17:34:16,508 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:34:16,509 - ExploitationExpert - INFO - res_population_costs: [680.0, 736.0, 680.0]
2025-08-04 17:34:16,509 - ExploitationExpert - INFO - res_populations: [array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 7, 2, 4, 8, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-04 17:34:16,510 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:34:16,510 - ExploitationExpert - INFO - populations: [{'tour': [1, 3, 2, 8, 6, 7, 0, 4, 5], 'cur_cost': 1112.0}, {'tour': [1, 5, 2, 3, 6, 7, 0, 4, 8], 'cur_cost': 1109.0}, {'tour': [3, 4, 7, 0, 1, 6, 5, 8, 2], 'cur_cost': 879.0}, {'tour': [3, 7, 0, 1, 4, 2, 8, 5, 6], 'cur_cost': 718.0}, {'tour': array([4, 5, 6, 3, 2, 8, 0, 7, 1], dtype=int64), 'cur_cost': 1014.0}, {'tour': [0, 3, 4, 6, 5, 7, 8, 2, 1], 'cur_cost': 902.0}, {'tour': [4, 7, 5, 3, 6, 8, 2, 0, 1], 'cur_cost': 844.0}, {'tour': [3, 8, 1, 2, 0, 7, 5, 6, 4], 'cur_cost': 1014.0}, {'tour': [3, 5, 2, 8, 7, 6, 0, 4, 1], 'cur_cost': 956.0}, {'tour': array([2, 6, 5, 0, 1, 7, 8, 4, 3], dtype=int64), 'cur_cost': 1006.0}]
2025-08-04 17:34:16,511 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:34:16,511 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-04 17:34:16,512 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([2, 6, 5, 0, 1, 7, 8, 4, 3], dtype=int64), 'cur_cost': 1006.0, 'intermediate_solutions': [{'tour': array([8, 2, 3, 6, 0, 4, 5, 7, 1]), 'cur_cost': 1045.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 8, 2, 3, 0, 4, 5, 7, 1]), 'cur_cost': 1055.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 6, 8, 2, 3, 4, 5, 7, 1]), 'cur_cost': 988.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 6, 8, 2, 0, 4, 5, 7, 1]), 'cur_cost': 1055.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 0, 6, 8, 2, 4, 5, 7, 1]), 'cur_cost': 1019.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:34:16,512 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1006.00)
2025-08-04 17:34:16,512 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:34:16,513 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:34:16,514 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [1, 3, 2, 8, 6, 7, 0, 4, 5], 'cur_cost': 1112.0, 'intermediate_solutions': [{'tour': [2, 3, 6, 7, 5, 4, 0, 1, 8], 'cur_cost': 947.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 3, 2, 7, 5, 6, 0, 1, 8], 'cur_cost': 952.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 3, 4, 7, 5, 6, 1, 8, 0], 'cur_cost': 1064.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [1, 5, 2, 3, 6, 7, 0, 4, 8], 'cur_cost': 1109.0, 'intermediate_solutions': [{'tour': [5, 3, 1, 4, 8, 6, 2, 0, 7], 'cur_cost': 1040.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 7, 8, 4, 1, 6, 2, 0, 3], 'cur_cost': 980.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 0, 1, 4, 8, 6, 2, 3], 'cur_cost': 940.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [3, 4, 7, 0, 1, 6, 5, 8, 2], 'cur_cost': 879.0, 'intermediate_solutions': [{'tour': [5, 3, 2, 4, 1, 0, 7, 8, 6], 'cur_cost': 796.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 3, 7, 0, 1, 4, 2, 8, 6], 'cur_cost': 721.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 8, 6, 2, 4, 1, 0, 7, 3], 'cur_cost': 839.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [3, 7, 0, 1, 4, 2, 8, 5, 6], 'cur_cost': 718.0, 'intermediate_solutions': [{'tour': [7, 5, 3, 0, 4, 1, 8, 6, 2], 'cur_cost': 1084.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 3, 0, 4, 8, 1, 2, 6], 'cur_cost': 1021.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 5, 3, 0, 4, 1, 6, 8, 2], 'cur_cost': 938.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 5, 6, 3, 2, 8, 0, 7, 1], dtype=int64), 'cur_cost': 1014.0, 'intermediate_solutions': [{'tour': array([6, 4, 2, 7, 5, 0, 8, 1, 3]), 'cur_cost': 1111.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 6, 4, 2, 5, 0, 8, 1, 3]), 'cur_cost': 1124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 7, 6, 4, 2, 0, 8, 1, 3]), 'cur_cost': 1023.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 7, 6, 4, 5, 0, 8, 1, 3]), 'cur_cost': 1257.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 5, 7, 6, 4, 0, 8, 1, 3]), 'cur_cost': 1186.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 4, 6, 5, 7, 8, 2, 1], 'cur_cost': 902.0, 'intermediate_solutions': [{'tour': [5, 6, 0, 2, 4, 7, 1, 8, 3], 'cur_cost': 884.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 1, 0, 2, 4, 7, 5, 8, 3], 'cur_cost': 786.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 2, 1, 4, 7, 5, 8, 3], 'cur_cost': 911.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [4, 7, 5, 3, 6, 8, 2, 0, 1], 'cur_cost': 844.0, 'intermediate_solutions': [{'tour': [7, 1, 4, 0, 6, 5, 3, 8, 2], 'cur_cost': 854.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 8, 4, 0, 2, 1, 3, 5, 6], 'cur_cost': 909.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 8, 4, 0, 6, 1, 5, 3, 2], 'cur_cost': 989.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [3, 8, 1, 2, 0, 7, 5, 6, 4], 'cur_cost': 1014.0, 'intermediate_solutions': [{'tour': [3, 8, 5, 0, 4, 6, 7, 2, 1], 'cur_cost': 1053.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 5, 6, 7, 2, 4, 0, 1], 'cur_cost': 806.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 8, 5, 0, 2, 4, 7, 6, 1], 'cur_cost': 946.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [3, 5, 2, 8, 7, 6, 0, 4, 1], 'cur_cost': 956.0, 'intermediate_solutions': [{'tour': [3, 5, 1, 8, 7, 0, 6, 4, 2], 'cur_cost': 1024.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 7, 1, 8, 5, 0, 4, 6, 2], 'cur_cost': 1160.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 7, 8, 1, 5, 0, 6, 4, 2], 'cur_cost': 1066.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 6, 5, 0, 1, 7, 8, 4, 3], dtype=int64), 'cur_cost': 1006.0, 'intermediate_solutions': [{'tour': array([8, 2, 3, 6, 0, 4, 5, 7, 1]), 'cur_cost': 1045.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 8, 2, 3, 0, 4, 5, 7, 1]), 'cur_cost': 1055.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 6, 8, 2, 3, 4, 5, 7, 1]), 'cur_cost': 988.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 6, 8, 2, 0, 4, 5, 7, 1]), 'cur_cost': 1055.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 0, 6, 8, 2, 4, 5, 7, 1]), 'cur_cost': 1019.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-04 17:34:16,517 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:34:16,518 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:34:16,519 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=718.000, 多样性=0.852
2025-08-04 17:34:16,519 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-04 17:34:16,519 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-04 17:34:16,520 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:34:16,520 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 80, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'slight_improvement', 'improvement_rate': 0.005594822282968596, 'best_improvement': 0.0}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.0547945205479452}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.7777777777777778, 'new_diversity': 0.7777777777777778, 'diversity_change': 0.0}, 'suggestions': ['改进缓慢，建议调整策略平衡或增加扰动强度', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-04 17:34:16,520 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-04 17:34:16,521 - __main__ - INFO - simple1_9 开始进化第 3 代
2025-08-04 17:34:16,521 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-04 17:34:16,521 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:34:16,522 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=718.000, 多样性=0.852
2025-08-04 17:34:16,522 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:34:16,523 - PathExpert - INFO - 路径结构分析完成: 公共边数量=3, 路径相似性=0.852
2025-08-04 17:34:16,523 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:34:16,524 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.778
2025-08-04 17:34:16,525 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-04 17:34:16,525 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:34:16,526 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-04 17:34:16,526 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-04 17:34:16,534 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.154, 适应度梯度: 15.738, 聚类评分: 0.000, 覆盖率: 0.003, 收敛趋势: 0.000, 多样性: 0.593
2025-08-04 17:34:16,535 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-04 17:34:16,535 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:34:16,535 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:34:16,539 - visualization.landscape_visualizer - INFO - 插值约束: 118 个点被约束到最小值 680.00
2025-08-04 17:34:16,543 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:34:16,636 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_3_20250804_173416.html
2025-08-04 17:34:16,673 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_3_20250804_173416.html
2025-08-04 17:34:16,673 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 3
2025-08-04 17:34:16,673 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-04 17:34:16,674 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1485秒
2025-08-04 17:34:16,674 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.15384615384615385, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 15.73846153846154, 'local_optima_density': 0.15384615384615385, 'gradient_variance': 16121.51621301775, 'cluster_count': 0}, 'population_state': {'diversity': 0.5927021696252466, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0032, 'fitness_entropy': 0.9383574700386479, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.003)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 15.738)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754300056.5352652, 'performance_metrics': {}}}
2025-08-04 17:34:16,674 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:34:16,675 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:34:16,675 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:34:16,675 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:34:16,675 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-04 17:34:16,676 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-04 17:34:16,676 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-04 17:34:16,676 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:34:16,676 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:34:16,677 - __main__ - INFO - 策略分配: ['exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-04 17:34:16,677 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:34:16,677 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:34:16,677 - experts.management.collaboration_manager - INFO - 识别精英个体: {3, 6} (总数: 2, 保护比例: 0.20)
2025-08-04 17:34:16,677 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-04 17:34:16,678 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:34:16,678 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:34:16,678 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 995.0
2025-08-04 17:34:16,687 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:34:16,687 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 736.0]
2025-08-04 17:34:16,688 - ExploitationExpert - INFO - res_populations: [array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 2, 4, 8, 3, 5, 6, 1], dtype=int64)]
2025-08-04 17:34:16,689 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:34:16,689 - ExploitationExpert - INFO - populations: [{'tour': array([4, 2, 1, 8, 5, 0, 3, 6, 7], dtype=int64), 'cur_cost': 995.0}, {'tour': [1, 5, 2, 3, 6, 7, 0, 4, 8], 'cur_cost': 1109.0}, {'tour': [3, 4, 7, 0, 1, 6, 5, 8, 2], 'cur_cost': 879.0}, {'tour': [3, 7, 0, 1, 4, 2, 8, 5, 6], 'cur_cost': 718.0}, {'tour': [4, 5, 6, 3, 2, 8, 0, 7, 1], 'cur_cost': 1014.0}, {'tour': [0, 3, 4, 6, 5, 7, 8, 2, 1], 'cur_cost': 902.0}, {'tour': [4, 7, 5, 3, 6, 8, 2, 0, 1], 'cur_cost': 844.0}, {'tour': [3, 8, 1, 2, 0, 7, 5, 6, 4], 'cur_cost': 1014.0}, {'tour': [3, 5, 2, 8, 7, 6, 0, 4, 1], 'cur_cost': 956.0}, {'tour': [2, 6, 5, 0, 1, 7, 8, 4, 3], 'cur_cost': 1006.0}]
2025-08-04 17:34:16,689 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:34:16,689 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-04 17:34:16,690 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([4, 2, 1, 8, 5, 0, 3, 6, 7], dtype=int64), 'cur_cost': 995.0, 'intermediate_solutions': [{'tour': array([2, 3, 1, 8, 6, 7, 0, 4, 5]), 'cur_cost': 1195.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 2, 3, 1, 6, 7, 0, 4, 5]), 'cur_cost': 1024.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 8, 2, 3, 1, 7, 0, 4, 5]), 'cur_cost': 1060.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 8, 2, 3, 6, 7, 0, 4, 5]), 'cur_cost': 1083.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 6, 8, 2, 3, 7, 0, 4, 5]), 'cur_cost': 1045.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:34:16,691 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 995.00)
2025-08-04 17:34:16,691 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-04 17:34:16,691 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:34:16,691 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:34:16,691 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 1081.0
2025-08-04 17:34:16,700 - ExploitationExpert - INFO - res_population_num: 4
2025-08-04 17:34:16,700 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 736.0, 680]
2025-08-04 17:34:16,700 - ExploitationExpert - INFO - res_populations: [array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 2, 4, 8, 3, 5, 6, 1], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64)]
2025-08-04 17:34:16,701 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:34:16,701 - ExploitationExpert - INFO - populations: [{'tour': array([4, 2, 1, 8, 5, 0, 3, 6, 7], dtype=int64), 'cur_cost': 995.0}, {'tour': array([5, 4, 1, 8, 7, 2, 3, 0, 6], dtype=int64), 'cur_cost': 1081.0}, {'tour': [3, 4, 7, 0, 1, 6, 5, 8, 2], 'cur_cost': 879.0}, {'tour': [3, 7, 0, 1, 4, 2, 8, 5, 6], 'cur_cost': 718.0}, {'tour': [4, 5, 6, 3, 2, 8, 0, 7, 1], 'cur_cost': 1014.0}, {'tour': [0, 3, 4, 6, 5, 7, 8, 2, 1], 'cur_cost': 902.0}, {'tour': [4, 7, 5, 3, 6, 8, 2, 0, 1], 'cur_cost': 844.0}, {'tour': [3, 8, 1, 2, 0, 7, 5, 6, 4], 'cur_cost': 1014.0}, {'tour': [3, 5, 2, 8, 7, 6, 0, 4, 1], 'cur_cost': 956.0}, {'tour': [2, 6, 5, 0, 1, 7, 8, 4, 3], 'cur_cost': 1006.0}]
2025-08-04 17:34:16,702 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:34:16,702 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-04 17:34:16,703 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([5, 4, 1, 8, 7, 2, 3, 0, 6], dtype=int64), 'cur_cost': 1081.0, 'intermediate_solutions': [{'tour': array([2, 5, 1, 3, 6, 7, 0, 4, 8]), 'cur_cost': 1049.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 2, 5, 1, 6, 7, 0, 4, 8]), 'cur_cost': 1030.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 3, 2, 5, 1, 7, 0, 4, 8]), 'cur_cost': 1117.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 3, 2, 5, 6, 7, 0, 4, 8]), 'cur_cost': 1078.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 6, 3, 2, 5, 7, 0, 4, 8]), 'cur_cost': 1052.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:34:16,704 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1081.00)
2025-08-04 17:34:16,704 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:34:16,705 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:34:16,705 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:16,705 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:34:16,706 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,706 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,706 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,707 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,707 - ExplorationExpert - INFO - 探索路径生成完成，成本: 944.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:34:16,707 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [7, 1, 4, 8, 2, 3, 5, 0, 6], 'cur_cost': 944.0, 'intermediate_solutions': [{'tour': [3, 2, 7, 0, 1, 6, 5, 8, 4], 'cur_cost': 910.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 4, 7, 0, 8, 5, 6, 1, 2], 'cur_cost': 1011.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 4, 7, 0, 1, 6, 5, 8, 2], 'cur_cost': 879.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:34:16,708 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 944.00)
2025-08-04 17:34:16,708 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:34:16,708 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:34:16,708 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:16,709 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:34:16,709 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,709 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,710 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,710 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,710 - ExplorationExpert - INFO - 探索路径生成完成，成本: 983.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:34:16,710 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [2, 6, 0, 8, 5, 3, 7, 4, 1], 'cur_cost': 983.0, 'intermediate_solutions': [{'tour': [3, 7, 0, 1, 4, 8, 2, 5, 6], 'cur_cost': 832.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 0, 7, 1, 4, 2, 8, 5, 6], 'cur_cost': 864.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 3, 0, 1, 4, 2, 8, 5, 6], 'cur_cost': 739.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:34:16,711 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 983.00)
2025-08-04 17:34:16,711 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:34:16,711 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:34:16,711 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:16,712 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:34:16,712 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,712 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,712 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,713 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,713 - ExplorationExpert - INFO - 探索路径生成完成，成本: 850.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:34:16,713 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 4, 8, 7, 3, 6, 5, 2, 1], 'cur_cost': 850.0, 'intermediate_solutions': [{'tour': [4, 5, 6, 3, 1, 8, 0, 7, 2], 'cur_cost': 1059.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 3, 6, 5, 4, 8, 0, 7, 1], 'cur_cost': 1040.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 4, 6, 3, 2, 8, 0, 7, 1], 'cur_cost': 1152.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:34:16,714 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 850.00)
2025-08-04 17:34:16,714 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:34:16,714 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:34:16,715 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:16,716 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:34:16,716 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,716 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,716 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,717 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,717 - ExplorationExpert - INFO - 探索路径生成完成，成本: 764.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:34:16,717 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [1, 7, 5, 6, 3, 8, 4, 2, 0], 'cur_cost': 764.0, 'intermediate_solutions': [{'tour': [0, 3, 4, 8, 5, 7, 6, 2, 1], 'cur_cost': 961.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 4, 6, 1, 2, 8, 7, 5], 'cur_cost': 1036.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 3, 4, 5, 7, 8, 2, 1], 'cur_cost': 998.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:34:16,717 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 764.00)
2025-08-04 17:34:16,718 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:34:16,718 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:34:16,718 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:16,719 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:34:16,719 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,719 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,720 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,720 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,720 - ExplorationExpert - INFO - 探索路径生成完成，成本: 680.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:34:16,720 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [2, 4, 0, 1, 6, 5, 3, 7, 8], 'cur_cost': 680.0, 'intermediate_solutions': [{'tour': [4, 8, 5, 3, 6, 7, 2, 0, 1], 'cur_cost': 844.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 7, 5, 3, 6, 2, 8, 0, 1], 'cur_cost': 903.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 7, 5, 3, 6, 8, 2, 1], 'cur_cost': 887.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:34:16,721 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 680.00)
2025-08-04 17:34:16,721 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-04 17:34:16,721 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:34:16,721 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:34:16,722 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 1064.0
2025-08-04 17:34:16,728 - ExploitationExpert - INFO - res_population_num: 4
2025-08-04 17:34:16,728 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 736.0, 680]
2025-08-04 17:34:16,728 - ExploitationExpert - INFO - res_populations: [array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 2, 4, 8, 3, 5, 6, 1], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64)]
2025-08-04 17:34:16,730 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:34:16,730 - ExploitationExpert - INFO - populations: [{'tour': array([4, 2, 1, 8, 5, 0, 3, 6, 7], dtype=int64), 'cur_cost': 995.0}, {'tour': array([5, 4, 1, 8, 7, 2, 3, 0, 6], dtype=int64), 'cur_cost': 1081.0}, {'tour': [7, 1, 4, 8, 2, 3, 5, 0, 6], 'cur_cost': 944.0}, {'tour': [2, 6, 0, 8, 5, 3, 7, 4, 1], 'cur_cost': 983.0}, {'tour': [0, 4, 8, 7, 3, 6, 5, 2, 1], 'cur_cost': 850.0}, {'tour': [1, 7, 5, 6, 3, 8, 4, 2, 0], 'cur_cost': 764.0}, {'tour': [2, 4, 0, 1, 6, 5, 3, 7, 8], 'cur_cost': 680.0}, {'tour': array([5, 3, 2, 7, 0, 8, 1, 4, 6], dtype=int64), 'cur_cost': 1064.0}, {'tour': [3, 5, 2, 8, 7, 6, 0, 4, 1], 'cur_cost': 956.0}, {'tour': [2, 6, 5, 0, 1, 7, 8, 4, 3], 'cur_cost': 1006.0}]
2025-08-04 17:34:16,732 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:34:16,732 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-04 17:34:16,733 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([5, 3, 2, 7, 0, 8, 1, 4, 6], dtype=int64), 'cur_cost': 1064.0, 'intermediate_solutions': [{'tour': array([1, 8, 3, 2, 0, 7, 5, 6, 4]), 'cur_cost': 1011.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 1, 8, 3, 0, 7, 5, 6, 4]), 'cur_cost': 945.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 2, 1, 8, 3, 7, 5, 6, 4]), 'cur_cost': 954.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 2, 1, 8, 0, 7, 5, 6, 4]), 'cur_cost': 1095.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 0, 2, 1, 8, 7, 5, 6, 4]), 'cur_cost': 1062.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:34:16,734 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1064.00)
2025-08-04 17:34:16,734 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:34:16,734 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:34:16,734 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:16,735 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:34:16,735 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,735 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,735 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,736 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,736 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1017.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:34:16,736 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [1, 8, 6, 2, 7, 5, 3, 4, 0], 'cur_cost': 1017.0, 'intermediate_solutions': [{'tour': [7, 5, 2, 8, 3, 6, 0, 4, 1], 'cur_cost': 936.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 2, 8, 7, 6, 0, 1, 4], 'cur_cost': 870.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 5, 2, 8, 7, 6, 4, 1], 'cur_cost': 925.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:34:16,736 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1017.00)
2025-08-04 17:34:16,736 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:34:16,737 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:34:16,737 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:16,737 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:34:16,737 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,737 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,738 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,738 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,738 - ExplorationExpert - INFO - 探索路径生成完成，成本: 924.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:34:16,738 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [1, 5, 0, 7, 4, 2, 8, 3, 6], 'cur_cost': 924.0, 'intermediate_solutions': [{'tour': [2, 6, 5, 1, 0, 7, 8, 4, 3], 'cur_cost': 1003.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 6, 5, 0, 1, 8, 7, 4, 3], 'cur_cost': 1056.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 2, 6, 5, 0, 1, 7, 8, 3], 'cur_cost': 895.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:34:16,739 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 924.00)
2025-08-04 17:34:16,739 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:34:16,739 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:34:16,741 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 2, 1, 8, 5, 0, 3, 6, 7], dtype=int64), 'cur_cost': 995.0, 'intermediate_solutions': [{'tour': array([2, 3, 1, 8, 6, 7, 0, 4, 5]), 'cur_cost': 1195.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 2, 3, 1, 6, 7, 0, 4, 5]), 'cur_cost': 1024.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 8, 2, 3, 1, 7, 0, 4, 5]), 'cur_cost': 1060.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 8, 2, 3, 6, 7, 0, 4, 5]), 'cur_cost': 1083.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 6, 8, 2, 3, 7, 0, 4, 5]), 'cur_cost': 1045.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([5, 4, 1, 8, 7, 2, 3, 0, 6], dtype=int64), 'cur_cost': 1081.0, 'intermediate_solutions': [{'tour': array([2, 5, 1, 3, 6, 7, 0, 4, 8]), 'cur_cost': 1049.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 2, 5, 1, 6, 7, 0, 4, 8]), 'cur_cost': 1030.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 3, 2, 5, 1, 7, 0, 4, 8]), 'cur_cost': 1117.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 3, 2, 5, 6, 7, 0, 4, 8]), 'cur_cost': 1078.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 6, 3, 2, 5, 7, 0, 4, 8]), 'cur_cost': 1052.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [7, 1, 4, 8, 2, 3, 5, 0, 6], 'cur_cost': 944.0, 'intermediate_solutions': [{'tour': [3, 2, 7, 0, 1, 6, 5, 8, 4], 'cur_cost': 910.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 4, 7, 0, 8, 5, 6, 1, 2], 'cur_cost': 1011.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 4, 7, 0, 1, 6, 5, 8, 2], 'cur_cost': 879.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [2, 6, 0, 8, 5, 3, 7, 4, 1], 'cur_cost': 983.0, 'intermediate_solutions': [{'tour': [3, 7, 0, 1, 4, 8, 2, 5, 6], 'cur_cost': 832.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 0, 7, 1, 4, 2, 8, 5, 6], 'cur_cost': 864.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 3, 0, 1, 4, 2, 8, 5, 6], 'cur_cost': 739.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 8, 7, 3, 6, 5, 2, 1], 'cur_cost': 850.0, 'intermediate_solutions': [{'tour': [4, 5, 6, 3, 1, 8, 0, 7, 2], 'cur_cost': 1059.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 3, 6, 5, 4, 8, 0, 7, 1], 'cur_cost': 1040.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 4, 6, 3, 2, 8, 0, 7, 1], 'cur_cost': 1152.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 5, 6, 3, 8, 4, 2, 0], 'cur_cost': 764.0, 'intermediate_solutions': [{'tour': [0, 3, 4, 8, 5, 7, 6, 2, 1], 'cur_cost': 961.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 4, 6, 1, 2, 8, 7, 5], 'cur_cost': 1036.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 3, 4, 5, 7, 8, 2, 1], 'cur_cost': 998.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [2, 4, 0, 1, 6, 5, 3, 7, 8], 'cur_cost': 680.0, 'intermediate_solutions': [{'tour': [4, 8, 5, 3, 6, 7, 2, 0, 1], 'cur_cost': 844.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 7, 5, 3, 6, 2, 8, 0, 1], 'cur_cost': 903.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 7, 5, 3, 6, 8, 2, 1], 'cur_cost': 887.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([5, 3, 2, 7, 0, 8, 1, 4, 6], dtype=int64), 'cur_cost': 1064.0, 'intermediate_solutions': [{'tour': array([1, 8, 3, 2, 0, 7, 5, 6, 4]), 'cur_cost': 1011.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 1, 8, 3, 0, 7, 5, 6, 4]), 'cur_cost': 945.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 2, 1, 8, 3, 7, 5, 6, 4]), 'cur_cost': 954.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 2, 1, 8, 0, 7, 5, 6, 4]), 'cur_cost': 1095.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 0, 2, 1, 8, 7, 5, 6, 4]), 'cur_cost': 1062.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [1, 8, 6, 2, 7, 5, 3, 4, 0], 'cur_cost': 1017.0, 'intermediate_solutions': [{'tour': [7, 5, 2, 8, 3, 6, 0, 4, 1], 'cur_cost': 936.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 2, 8, 7, 6, 0, 1, 4], 'cur_cost': 870.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 5, 2, 8, 7, 6, 4, 1], 'cur_cost': 925.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [1, 5, 0, 7, 4, 2, 8, 3, 6], 'cur_cost': 924.0, 'intermediate_solutions': [{'tour': [2, 6, 5, 1, 0, 7, 8, 4, 3], 'cur_cost': 1003.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 6, 5, 0, 1, 8, 7, 4, 3], 'cur_cost': 1056.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 2, 6, 5, 0, 1, 7, 8, 3], 'cur_cost': 895.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:34:16,743 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:34:16,743 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:34:16,744 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=680.000, 多样性=0.881
2025-08-04 17:34:16,744 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-04 17:34:16,745 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-04 17:34:16,745 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:34:16,745 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.03572629192725046, 'best_improvement': 0.052924791086350974}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.03478260869565209}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.7407407407407408, 'new_diversity': 0.7407407407407408, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:34:16,746 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-04 17:34:16,746 - __main__ - INFO - simple1_9 开始进化第 4 代
2025-08-04 17:34:16,746 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-04 17:34:16,747 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:34:16,747 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=680.000, 多样性=0.881
2025-08-04 17:34:16,748 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:34:16,749 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.881
2025-08-04 17:34:16,749 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:34:16,751 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.741
2025-08-04 17:34:16,753 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-04 17:34:16,753 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:34:16,753 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-04 17:34:16,754 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-04 17:34:16,763 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.286, 适应度梯度: 8.971, 聚类评分: 0.000, 覆盖率: 0.004, 收敛趋势: 0.000, 多样性: 0.561
2025-08-04 17:34:16,764 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-04 17:34:16,764 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:34:16,765 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:34:16,772 - visualization.landscape_visualizer - INFO - 插值约束: 250 个点被约束到最小值 680.00
2025-08-04 17:34:16,778 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:34:16,866 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_4_20250804_173416.html
2025-08-04 17:34:16,906 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_4_20250804_173416.html
2025-08-04 17:34:16,906 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 4
2025-08-04 17:34:16,907 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-04 17:34:16,907 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1534秒
2025-08-04 17:34:16,907 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2857142857142857, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 8.971428571428572, 'local_optima_density': 0.2857142857142857, 'gradient_variance': 22934.14775510204, 'cluster_count': 0}, 'population_state': {'diversity': 0.5612244897959183, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0043, 'fitness_entropy': 0.9357849740192014, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.004)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 8.971)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754300056.7636986, 'performance_metrics': {}}}
2025-08-04 17:34:16,908 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:34:16,908 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:34:16,909 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:34:16,909 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:34:16,910 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-04 17:34:16,910 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-04 17:34:16,910 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-04 17:34:16,911 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:34:16,911 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:34:16,911 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-04 17:34:16,912 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:34:16,912 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:34:16,913 - experts.management.collaboration_manager - INFO - 识别精英个体: {5, 6} (总数: 2, 保护比例: 0.20)
2025-08-04 17:34:16,913 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:34:16,913 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:34:16,913 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:16,914 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:34:16,914 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,915 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,915 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,916 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,916 - ExplorationExpert - INFO - 探索路径生成完成，成本: 769.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:34:16,916 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 6, 5, 8, 7, 3, 4, 2, 1], 'cur_cost': 769.0, 'intermediate_solutions': [{'tour': [7, 2, 1, 8, 5, 0, 3, 6, 4], 'cur_cost': 1138.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 3, 0, 5, 8, 1, 2, 4, 7], 'cur_cost': 995.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 2, 1, 8, 5, 0, 3, 7, 6], 'cur_cost': 1010.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:34:16,917 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 769.00)
2025-08-04 17:34:16,917 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-04 17:34:16,917 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:34:16,918 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:34:16,918 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 1110.0
2025-08-04 17:34:16,925 - ExploitationExpert - INFO - res_population_num: 4
2025-08-04 17:34:16,926 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680, 736.0]
2025-08-04 17:34:16,926 - ExploitationExpert - INFO - res_populations: [array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 7, 2, 4, 8, 3, 5, 6, 1], dtype=int64)]
2025-08-04 17:34:16,927 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:34:16,928 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 5, 8, 7, 3, 4, 2, 1], 'cur_cost': 769.0}, {'tour': array([8, 6, 3, 1, 7, 4, 0, 5, 2], dtype=int64), 'cur_cost': 1110.0}, {'tour': [7, 1, 4, 8, 2, 3, 5, 0, 6], 'cur_cost': 944.0}, {'tour': [2, 6, 0, 8, 5, 3, 7, 4, 1], 'cur_cost': 983.0}, {'tour': [0, 4, 8, 7, 3, 6, 5, 2, 1], 'cur_cost': 850.0}, {'tour': [1, 7, 5, 6, 3, 8, 4, 2, 0], 'cur_cost': 764.0}, {'tour': [2, 4, 0, 1, 6, 5, 3, 7, 8], 'cur_cost': 680.0}, {'tour': [5, 3, 2, 7, 0, 8, 1, 4, 6], 'cur_cost': 1064.0}, {'tour': [1, 8, 6, 2, 7, 5, 3, 4, 0], 'cur_cost': 1017.0}, {'tour': [1, 5, 0, 7, 4, 2, 8, 3, 6], 'cur_cost': 924.0}]
2025-08-04 17:34:16,928 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:34:16,929 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-08-04 17:34:16,929 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([8, 6, 3, 1, 7, 4, 0, 5, 2], dtype=int64), 'cur_cost': 1110.0, 'intermediate_solutions': [{'tour': array([1, 4, 5, 8, 7, 2, 3, 0, 6]), 'cur_cost': 1053.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 1, 4, 5, 7, 2, 3, 0, 6]), 'cur_cost': 1136.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 8, 1, 4, 5, 2, 3, 0, 6]), 'cur_cost': 1135.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 8, 1, 4, 7, 2, 3, 0, 6]), 'cur_cost': 1046.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 7, 8, 1, 4, 2, 3, 0, 6]), 'cur_cost': 935.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:34:16,930 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1110.00)
2025-08-04 17:34:16,930 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:34:16,930 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:34:16,931 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:16,931 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:34:16,931 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,932 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,932 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,932 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,932 - ExplorationExpert - INFO - 探索路径生成完成，成本: 973.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:34:16,933 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [5, 3, 8, 2, 7, 0, 4, 6, 1], 'cur_cost': 973.0, 'intermediate_solutions': [{'tour': [7, 1, 3, 8, 2, 4, 5, 0, 6], 'cur_cost': 971.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 1, 4, 8, 2, 5, 3, 0, 6], 'cur_cost': 964.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 1, 4, 8, 2, 3, 5, 0, 6], 'cur_cost': 944.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:34:16,933 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 973.00)
2025-08-04 17:34:16,934 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:34:16,934 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:34:16,934 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:16,934 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:34:16,935 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,935 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,935 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,935 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,936 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1064.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:34:16,936 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [7, 6, 5, 2, 1, 3, 4, 8, 0], 'cur_cost': 1064.0, 'intermediate_solutions': [{'tour': [8, 6, 0, 2, 5, 3, 7, 4, 1], 'cur_cost': 1036.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 0, 6, 2, 5, 3, 7, 4, 1], 'cur_cost': 1095.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 6, 0, 8, 5, 7, 3, 4, 1], 'cur_cost': 1019.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:34:16,936 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1064.00)
2025-08-04 17:34:16,936 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:34:16,937 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:34:16,937 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:16,937 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:34:16,937 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,938 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,938 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,938 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,938 - ExplorationExpert - INFO - 探索路径生成完成，成本: 863.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:34:16,939 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [6, 7, 2, 3, 5, 8, 4, 0, 1], 'cur_cost': 863.0, 'intermediate_solutions': [{'tour': [0, 4, 8, 6, 3, 7, 5, 2, 1], 'cur_cost': 905.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 8, 7, 3, 5, 6, 2, 1], 'cur_cost': 828.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 8, 5, 7, 3, 6, 2, 1], 'cur_cost': 880.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:34:16,939 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 863.00)
2025-08-04 17:34:16,939 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:34:16,939 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:34:16,940 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:16,940 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:34:16,940 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,941 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,941 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,941 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,941 - ExplorationExpert - INFO - 探索路径生成完成，成本: 877.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:34:16,941 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 6, 4, 5, 3, 7, 8, 2, 1], 'cur_cost': 877.0, 'intermediate_solutions': [{'tour': [7, 1, 5, 6, 3, 8, 4, 2, 0], 'cur_cost': 900.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 1, 5, 6, 3, 8, 4, 2, 0], 'cur_cost': 900.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 5, 6, 3, 8, 4, 2, 0], 'cur_cost': 764.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:34:16,942 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 877.00)
2025-08-04 17:34:16,942 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:34:16,942 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:34:16,942 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:16,943 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:34:16,943 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,943 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,943 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,944 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,944 - ExplorationExpert - INFO - 探索路径生成完成，成本: 847.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:34:16,944 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [7, 8, 5, 3, 6, 0, 4, 2, 1], 'cur_cost': 847.0, 'intermediate_solutions': [{'tour': [2, 6, 0, 1, 4, 5, 3, 7, 8], 'cur_cost': 886.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 4, 3, 5, 6, 1, 0, 7, 8], 'cur_cost': 740.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 0, 6, 5, 3, 7, 1, 8], 'cur_cost': 830.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:34:16,944 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 847.00)
2025-08-04 17:34:16,945 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-04 17:34:16,945 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:34:16,945 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:34:16,946 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 941.0
2025-08-04 17:34:16,954 - ExploitationExpert - INFO - res_population_num: 4
2025-08-04 17:34:16,954 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680, 736.0]
2025-08-04 17:34:16,954 - ExploitationExpert - INFO - res_populations: [array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 7, 2, 4, 8, 3, 5, 6, 1], dtype=int64)]
2025-08-04 17:34:16,956 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:34:16,956 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 5, 8, 7, 3, 4, 2, 1], 'cur_cost': 769.0}, {'tour': array([8, 6, 3, 1, 7, 4, 0, 5, 2], dtype=int64), 'cur_cost': 1110.0}, {'tour': [5, 3, 8, 2, 7, 0, 4, 6, 1], 'cur_cost': 973.0}, {'tour': [7, 6, 5, 2, 1, 3, 4, 8, 0], 'cur_cost': 1064.0}, {'tour': [6, 7, 2, 3, 5, 8, 4, 0, 1], 'cur_cost': 863.0}, {'tour': [0, 6, 4, 5, 3, 7, 8, 2, 1], 'cur_cost': 877.0}, {'tour': [7, 8, 5, 3, 6, 0, 4, 2, 1], 'cur_cost': 847.0}, {'tour': array([8, 3, 7, 5, 1, 0, 4, 6, 2], dtype=int64), 'cur_cost': 941.0}, {'tour': [1, 8, 6, 2, 7, 5, 3, 4, 0], 'cur_cost': 1017.0}, {'tour': [1, 5, 0, 7, 4, 2, 8, 3, 6], 'cur_cost': 924.0}]
2025-08-04 17:34:16,957 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:34:16,957 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-08-04 17:34:16,958 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([8, 3, 7, 5, 1, 0, 4, 6, 2], dtype=int64), 'cur_cost': 941.0, 'intermediate_solutions': [{'tour': array([2, 3, 5, 7, 0, 8, 1, 4, 6]), 'cur_cost': 1124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 2, 3, 5, 0, 8, 1, 4, 6]), 'cur_cost': 1119.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 7, 2, 3, 5, 8, 1, 4, 6]), 'cur_cost': 1053.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 7, 2, 3, 0, 8, 1, 4, 6]), 'cur_cost': 1113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 0, 7, 2, 3, 8, 1, 4, 6]), 'cur_cost': 1071.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:34:16,958 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 941.00)
2025-08-04 17:34:16,958 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-04 17:34:16,959 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:34:16,959 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:34:16,959 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 1054.0
2025-08-04 17:34:16,967 - ExploitationExpert - INFO - res_population_num: 4
2025-08-04 17:34:16,967 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680, 736.0]
2025-08-04 17:34:16,968 - ExploitationExpert - INFO - res_populations: [array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 7, 2, 4, 8, 3, 5, 6, 1], dtype=int64)]
2025-08-04 17:34:16,969 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:34:16,969 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 5, 8, 7, 3, 4, 2, 1], 'cur_cost': 769.0}, {'tour': array([8, 6, 3, 1, 7, 4, 0, 5, 2], dtype=int64), 'cur_cost': 1110.0}, {'tour': [5, 3, 8, 2, 7, 0, 4, 6, 1], 'cur_cost': 973.0}, {'tour': [7, 6, 5, 2, 1, 3, 4, 8, 0], 'cur_cost': 1064.0}, {'tour': [6, 7, 2, 3, 5, 8, 4, 0, 1], 'cur_cost': 863.0}, {'tour': [0, 6, 4, 5, 3, 7, 8, 2, 1], 'cur_cost': 877.0}, {'tour': [7, 8, 5, 3, 6, 0, 4, 2, 1], 'cur_cost': 847.0}, {'tour': array([8, 3, 7, 5, 1, 0, 4, 6, 2], dtype=int64), 'cur_cost': 941.0}, {'tour': array([2, 6, 4, 5, 7, 3, 0, 1, 8], dtype=int64), 'cur_cost': 1054.0}, {'tour': [1, 5, 0, 7, 4, 2, 8, 3, 6], 'cur_cost': 924.0}]
2025-08-04 17:34:16,970 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:34:16,971 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-08-04 17:34:16,971 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([2, 6, 4, 5, 7, 3, 0, 1, 8], dtype=int64), 'cur_cost': 1054.0, 'intermediate_solutions': [{'tour': array([6, 8, 1, 2, 7, 5, 3, 4, 0]), 'cur_cost': 1020.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 6, 8, 1, 7, 5, 3, 4, 0]), 'cur_cost': 1096.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 2, 6, 8, 1, 5, 3, 4, 0]), 'cur_cost': 1153.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 2, 6, 8, 7, 5, 3, 4, 0]), 'cur_cost': 935.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 7, 2, 6, 8, 5, 3, 4, 0]), 'cur_cost': 994.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:34:16,972 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1054.00)
2025-08-04 17:34:16,972 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:34:16,972 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:34:16,972 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:16,973 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:34:16,973 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,973 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,974 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,974 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:16,974 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1018.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:34:16,974 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 8, 1, 4, 6, 5, 3, 7, 2], 'cur_cost': 1018.0, 'intermediate_solutions': [{'tour': [1, 5, 0, 7, 4, 2, 6, 3, 8], 'cur_cost': 1070.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 3, 8, 2, 4, 7, 0, 5, 1], 'cur_cost': 924.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 5, 0, 7, 4, 2, 8, 3, 6], 'cur_cost': 924.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:34:16,975 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1018.00)
2025-08-04 17:34:16,975 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:34:16,975 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:34:16,977 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 5, 8, 7, 3, 4, 2, 1], 'cur_cost': 769.0, 'intermediate_solutions': [{'tour': [7, 2, 1, 8, 5, 0, 3, 6, 4], 'cur_cost': 1138.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 3, 0, 5, 8, 1, 2, 4, 7], 'cur_cost': 995.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 2, 1, 8, 5, 0, 3, 7, 6], 'cur_cost': 1010.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 6, 3, 1, 7, 4, 0, 5, 2], dtype=int64), 'cur_cost': 1110.0, 'intermediate_solutions': [{'tour': array([1, 4, 5, 8, 7, 2, 3, 0, 6]), 'cur_cost': 1053.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 1, 4, 5, 7, 2, 3, 0, 6]), 'cur_cost': 1136.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 8, 1, 4, 5, 2, 3, 0, 6]), 'cur_cost': 1135.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 8, 1, 4, 7, 2, 3, 0, 6]), 'cur_cost': 1046.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 7, 8, 1, 4, 2, 3, 0, 6]), 'cur_cost': 935.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [5, 3, 8, 2, 7, 0, 4, 6, 1], 'cur_cost': 973.0, 'intermediate_solutions': [{'tour': [7, 1, 3, 8, 2, 4, 5, 0, 6], 'cur_cost': 971.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 1, 4, 8, 2, 5, 3, 0, 6], 'cur_cost': 964.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 1, 4, 8, 2, 3, 5, 0, 6], 'cur_cost': 944.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [7, 6, 5, 2, 1, 3, 4, 8, 0], 'cur_cost': 1064.0, 'intermediate_solutions': [{'tour': [8, 6, 0, 2, 5, 3, 7, 4, 1], 'cur_cost': 1036.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 0, 6, 2, 5, 3, 7, 4, 1], 'cur_cost': 1095.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 6, 0, 8, 5, 7, 3, 4, 1], 'cur_cost': 1019.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [6, 7, 2, 3, 5, 8, 4, 0, 1], 'cur_cost': 863.0, 'intermediate_solutions': [{'tour': [0, 4, 8, 6, 3, 7, 5, 2, 1], 'cur_cost': 905.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 8, 7, 3, 5, 6, 2, 1], 'cur_cost': 828.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 8, 5, 7, 3, 6, 2, 1], 'cur_cost': 880.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 4, 5, 3, 7, 8, 2, 1], 'cur_cost': 877.0, 'intermediate_solutions': [{'tour': [7, 1, 5, 6, 3, 8, 4, 2, 0], 'cur_cost': 900.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 1, 5, 6, 3, 8, 4, 2, 0], 'cur_cost': 900.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 5, 6, 3, 8, 4, 2, 0], 'cur_cost': 764.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [7, 8, 5, 3, 6, 0, 4, 2, 1], 'cur_cost': 847.0, 'intermediate_solutions': [{'tour': [2, 6, 0, 1, 4, 5, 3, 7, 8], 'cur_cost': 886.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 4, 3, 5, 6, 1, 0, 7, 8], 'cur_cost': 740.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 0, 6, 5, 3, 7, 1, 8], 'cur_cost': 830.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 3, 7, 5, 1, 0, 4, 6, 2], dtype=int64), 'cur_cost': 941.0, 'intermediate_solutions': [{'tour': array([2, 3, 5, 7, 0, 8, 1, 4, 6]), 'cur_cost': 1124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 2, 3, 5, 0, 8, 1, 4, 6]), 'cur_cost': 1119.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 7, 2, 3, 5, 8, 1, 4, 6]), 'cur_cost': 1053.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 7, 2, 3, 0, 8, 1, 4, 6]), 'cur_cost': 1113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 0, 7, 2, 3, 8, 1, 4, 6]), 'cur_cost': 1071.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 6, 4, 5, 7, 3, 0, 1, 8], dtype=int64), 'cur_cost': 1054.0, 'intermediate_solutions': [{'tour': array([6, 8, 1, 2, 7, 5, 3, 4, 0]), 'cur_cost': 1020.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 6, 8, 1, 7, 5, 3, 4, 0]), 'cur_cost': 1096.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 2, 6, 8, 1, 5, 3, 4, 0]), 'cur_cost': 1153.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 2, 6, 8, 7, 5, 3, 4, 0]), 'cur_cost': 935.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 7, 2, 6, 8, 5, 3, 4, 0]), 'cur_cost': 994.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 1, 4, 6, 5, 3, 7, 2], 'cur_cost': 1018.0, 'intermediate_solutions': [{'tour': [1, 5, 0, 7, 4, 2, 6, 3, 8], 'cur_cost': 1070.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 3, 8, 2, 4, 7, 0, 5, 1], 'cur_cost': 924.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 5, 0, 7, 4, 2, 8, 3, 6], 'cur_cost': 924.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:34:16,980 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:34:16,980 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:34:16,983 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=769.000, 多样性=0.820
2025-08-04 17:34:16,984 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-04 17:34:16,984 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-04 17:34:16,985 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:34:16,985 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.0602383900567535, 'best_improvement': -0.13088235294117648}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.07002801120448186}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.04167318113456011, 'recent_improvements': [-0.04762007034186977, 0.005594822282968596, 0.03572629192725046], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.7407407407407408, 'new_diversity': 0.7407407407407408, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:34:16,986 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-04 17:34:16,987 - __main__ - INFO - simple1_9 开始进化第 5 代
2025-08-04 17:34:16,987 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-04 17:34:16,988 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:34:16,988 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=769.000, 多样性=0.820
2025-08-04 17:34:16,989 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:34:16,989 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.820
2025-08-04 17:34:16,990 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:34:16,991 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.741
2025-08-04 17:34:16,993 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-04 17:34:16,994 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:34:16,994 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-04 17:34:16,995 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-04 17:34:17,006 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.286, 适应度梯度: -9.200, 聚类评分: 0.000, 覆盖率: 0.005, 收敛趋势: 0.000, 多样性: 0.534
2025-08-04 17:34:17,007 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-04 17:34:17,007 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:34:17,008 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:34:17,011 - visualization.landscape_visualizer - INFO - 插值约束: 53 个点被约束到最小值 680.00
2025-08-04 17:34:17,019 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:34:17,114 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_5_20250804_173417.html
2025-08-04 17:34:17,158 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_5_20250804_173417.html
2025-08-04 17:34:17,158 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 5
2025-08-04 17:34:17,158 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-04 17:34:17,159 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1658秒
2025-08-04 17:34:17,159 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2857142857142857, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -9.200000000000003, 'local_optima_density': 0.2857142857142857, 'gradient_variance': 14084.55428571429, 'cluster_count': 0}, 'population_state': {'diversity': 0.533751962323391, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0053, 'fitness_entropy': 0.9587446613154134, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -9.200)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.005)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754300057.007673, 'performance_metrics': {}}}
2025-08-04 17:34:17,159 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:34:17,160 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:34:17,160 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:34:17,160 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:34:17,160 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-04 17:34:17,161 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-04 17:34:17,161 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-04 17:34:17,161 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:34:17,162 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:34:17,162 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore']
2025-08-04 17:34:17,162 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:34:17,163 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:34:17,164 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 6} (总数: 2, 保护比例: 0.20)
2025-08-04 17:34:17,164 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:34:17,164 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:34:17,165 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:17,165 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:34:17,165 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:17,166 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:17,166 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:17,166 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:17,166 - ExplorationExpert - INFO - 探索路径生成完成，成本: 915.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:34:17,166 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [6, 7, 3, 8, 5, 4, 0, 1, 2], 'cur_cost': 915.0, 'intermediate_solutions': [{'tour': [0, 6, 5, 8, 7, 3, 4, 1, 2], 'cur_cost': 905.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 5, 8, 7, 3, 2, 4, 1], 'cur_cost': 766.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 2, 5, 8, 7, 3, 4, 1], 'cur_cost': 975.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:34:17,167 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 915.00)
2025-08-04 17:34:17,167 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-04 17:34:17,167 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:34:17,167 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:34:17,168 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 921.0
2025-08-04 17:34:17,173 - ExploitationExpert - INFO - res_population_num: 4
2025-08-04 17:34:17,174 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680, 736.0]
2025-08-04 17:34:17,174 - ExploitationExpert - INFO - res_populations: [array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 7, 2, 4, 8, 3, 5, 6, 1], dtype=int64)]
2025-08-04 17:34:17,175 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:34:17,175 - ExploitationExpert - INFO - populations: [{'tour': [6, 7, 3, 8, 5, 4, 0, 1, 2], 'cur_cost': 915.0}, {'tour': array([7, 1, 4, 2, 5, 3, 8, 6, 0], dtype=int64), 'cur_cost': 921.0}, {'tour': [5, 3, 8, 2, 7, 0, 4, 6, 1], 'cur_cost': 973.0}, {'tour': [7, 6, 5, 2, 1, 3, 4, 8, 0], 'cur_cost': 1064.0}, {'tour': [6, 7, 2, 3, 5, 8, 4, 0, 1], 'cur_cost': 863.0}, {'tour': [0, 6, 4, 5, 3, 7, 8, 2, 1], 'cur_cost': 877.0}, {'tour': [7, 8, 5, 3, 6, 0, 4, 2, 1], 'cur_cost': 847.0}, {'tour': [8, 3, 7, 5, 1, 0, 4, 6, 2], 'cur_cost': 941.0}, {'tour': [2, 6, 4, 5, 7, 3, 0, 1, 8], 'cur_cost': 1054.0}, {'tour': [0, 8, 1, 4, 6, 5, 3, 7, 2], 'cur_cost': 1018.0}]
2025-08-04 17:34:17,175 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:34:17,176 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-08-04 17:34:17,176 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([7, 1, 4, 2, 5, 3, 8, 6, 0], dtype=int64), 'cur_cost': 921.0, 'intermediate_solutions': [{'tour': array([3, 6, 8, 1, 7, 4, 0, 5, 2]), 'cur_cost': 1170.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 3, 6, 8, 7, 4, 0, 5, 2]), 'cur_cost': 1109.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 1, 3, 6, 8, 4, 0, 5, 2]), 'cur_cost': 1141.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 1, 3, 6, 7, 4, 0, 5, 2]), 'cur_cost': 1102.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 7, 1, 3, 6, 4, 0, 5, 2]), 'cur_cost': 1111.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:34:17,177 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 921.00)
2025-08-04 17:34:17,177 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:34:17,177 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:34:17,177 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:17,178 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:34:17,178 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:17,178 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:17,178 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:17,179 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:17,179 - ExplorationExpert - INFO - 探索路径生成完成，成本: 954.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:34:17,179 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 7, 5, 8, 4, 3, 6, 1, 2], 'cur_cost': 954.0, 'intermediate_solutions': [{'tour': [5, 3, 8, 2, 4, 0, 7, 6, 1], 'cur_cost': 830.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 3, 8, 2, 7, 0, 4, 1, 6], 'cur_cost': 835.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 3, 2, 8, 7, 0, 4, 6, 1], 'cur_cost': 1000.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:34:17,180 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 954.00)
2025-08-04 17:34:17,180 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-04 17:34:17,180 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:34:17,180 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:34:17,181 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 839.0
2025-08-04 17:34:17,187 - ExploitationExpert - INFO - res_population_num: 4
2025-08-04 17:34:17,187 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680, 736.0]
2025-08-04 17:34:17,188 - ExploitationExpert - INFO - res_populations: [array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 7, 2, 4, 8, 3, 5, 6, 1], dtype=int64)]
2025-08-04 17:34:17,188 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:34:17,189 - ExploitationExpert - INFO - populations: [{'tour': [6, 7, 3, 8, 5, 4, 0, 1, 2], 'cur_cost': 915.0}, {'tour': array([7, 1, 4, 2, 5, 3, 8, 6, 0], dtype=int64), 'cur_cost': 921.0}, {'tour': [0, 7, 5, 8, 4, 3, 6, 1, 2], 'cur_cost': 954.0}, {'tour': array([0, 1, 6, 8, 4, 2, 7, 5, 3], dtype=int64), 'cur_cost': 839.0}, {'tour': [6, 7, 2, 3, 5, 8, 4, 0, 1], 'cur_cost': 863.0}, {'tour': [0, 6, 4, 5, 3, 7, 8, 2, 1], 'cur_cost': 877.0}, {'tour': [7, 8, 5, 3, 6, 0, 4, 2, 1], 'cur_cost': 847.0}, {'tour': [8, 3, 7, 5, 1, 0, 4, 6, 2], 'cur_cost': 941.0}, {'tour': [2, 6, 4, 5, 7, 3, 0, 1, 8], 'cur_cost': 1054.0}, {'tour': [0, 8, 1, 4, 6, 5, 3, 7, 2], 'cur_cost': 1018.0}]
2025-08-04 17:34:17,189 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:34:17,190 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-08-04 17:34:17,190 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([0, 1, 6, 8, 4, 2, 7, 5, 3], dtype=int64), 'cur_cost': 839.0, 'intermediate_solutions': [{'tour': array([5, 6, 7, 2, 1, 3, 4, 8, 0]), 'cur_cost': 1065.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 5, 6, 7, 1, 3, 4, 8, 0]), 'cur_cost': 1094.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 2, 5, 6, 7, 3, 4, 8, 0]), 'cur_cost': 918.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 2, 5, 6, 1, 3, 4, 8, 0]), 'cur_cost': 1094.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 1, 2, 5, 6, 3, 4, 8, 0]), 'cur_cost': 1043.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:34:17,191 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 839.00)
2025-08-04 17:34:17,191 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:34:17,191 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:34:17,191 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:17,192 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:34:17,192 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:17,192 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:17,192 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:17,192 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:17,192 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1025.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:34:17,193 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 8, 1, 5, 3, 4, 2, 7, 6], 'cur_cost': 1025.0, 'intermediate_solutions': [{'tour': [1, 7, 2, 3, 5, 8, 4, 0, 6], 'cur_cost': 961.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 2, 4, 8, 5, 3, 0, 1], 'cur_cost': 808.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 2, 3, 5, 8, 4, 0, 7, 1], 'cur_cost': 1007.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:34:17,193 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1025.00)
2025-08-04 17:34:17,193 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:34:17,194 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:34:17,194 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:17,194 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:34:17,194 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:17,195 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:17,195 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:17,195 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:17,195 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1048.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:34:17,195 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [6, 3, 7, 1, 5, 0, 4, 8, 2], 'cur_cost': 1048.0, 'intermediate_solutions': [{'tour': [0, 6, 4, 5, 2, 7, 8, 3, 1], 'cur_cost': 1063.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 4, 5, 2, 8, 7, 3, 1], 'cur_cost': 1015.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 5, 4, 3, 7, 8, 2, 1], 'cur_cost': 857.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:34:17,196 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1048.00)
2025-08-04 17:34:17,196 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:34:17,196 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:34:17,197 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:17,197 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:34:17,198 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:17,198 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:17,198 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:17,199 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:17,199 - ExplorationExpert - INFO - 探索路径生成完成，成本: 954.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:34:17,199 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [7, 8, 1, 3, 2, 4, 0, 6, 5], 'cur_cost': 954.0, 'intermediate_solutions': [{'tour': [7, 8, 5, 6, 3, 0, 4, 2, 1], 'cur_cost': 882.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 8, 5, 3, 6, 0, 4, 1, 2], 'cur_cost': 904.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 5, 3, 6, 7, 0, 4, 2, 1], 'cur_cost': 880.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:34:17,200 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 954.00)
2025-08-04 17:34:17,200 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:34:17,200 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:34:17,200 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:17,201 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:34:17,201 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:17,201 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:17,201 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:17,201 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:17,201 - ExplorationExpert - INFO - 探索路径生成完成，成本: 997.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:34:17,202 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [1, 3, 4, 7, 8, 5, 6, 0, 2], 'cur_cost': 997.0, 'intermediate_solutions': [{'tour': [7, 3, 8, 5, 1, 0, 4, 6, 2], 'cur_cost': 999.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 3, 1, 5, 7, 0, 4, 6, 2], 'cur_cost': 1087.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 3, 7, 5, 1, 0, 4, 6, 2], 'cur_cost': 941.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:34:17,202 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 997.00)
2025-08-04 17:34:17,202 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-04 17:34:17,202 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:34:17,203 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:34:17,203 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 967.0
2025-08-04 17:34:17,209 - ExploitationExpert - INFO - res_population_num: 4
2025-08-04 17:34:17,209 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680, 736.0]
2025-08-04 17:34:17,210 - ExploitationExpert - INFO - res_populations: [array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 7, 6, 5, 3, 8, 2, 4, 1], dtype=int64), array([0, 7, 2, 4, 8, 3, 5, 6, 1], dtype=int64)]
2025-08-04 17:34:17,211 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:34:17,211 - ExploitationExpert - INFO - populations: [{'tour': [6, 7, 3, 8, 5, 4, 0, 1, 2], 'cur_cost': 915.0}, {'tour': array([7, 1, 4, 2, 5, 3, 8, 6, 0], dtype=int64), 'cur_cost': 921.0}, {'tour': [0, 7, 5, 8, 4, 3, 6, 1, 2], 'cur_cost': 954.0}, {'tour': array([0, 1, 6, 8, 4, 2, 7, 5, 3], dtype=int64), 'cur_cost': 839.0}, {'tour': [0, 8, 1, 5, 3, 4, 2, 7, 6], 'cur_cost': 1025.0}, {'tour': [6, 3, 7, 1, 5, 0, 4, 8, 2], 'cur_cost': 1048.0}, {'tour': [7, 8, 1, 3, 2, 4, 0, 6, 5], 'cur_cost': 954.0}, {'tour': [1, 3, 4, 7, 8, 5, 6, 0, 2], 'cur_cost': 997.0}, {'tour': array([3, 1, 0, 7, 5, 4, 2, 6, 8], dtype=int64), 'cur_cost': 967.0}, {'tour': [0, 8, 1, 4, 6, 5, 3, 7, 2], 'cur_cost': 1018.0}]
2025-08-04 17:34:17,212 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:34:17,212 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-08-04 17:34:17,213 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([3, 1, 0, 7, 5, 4, 2, 6, 8], dtype=int64), 'cur_cost': 967.0, 'intermediate_solutions': [{'tour': array([4, 6, 2, 5, 7, 3, 0, 1, 8]), 'cur_cost': 1080.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 4, 6, 2, 7, 3, 0, 1, 8]), 'cur_cost': 1112.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 5, 4, 6, 2, 3, 0, 1, 8]), 'cur_cost': 1129.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 5, 4, 6, 7, 3, 0, 1, 8]), 'cur_cost': 1048.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 7, 5, 4, 6, 3, 0, 1, 8]), 'cur_cost': 1035.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:34:17,214 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 967.00)
2025-08-04 17:34:17,214 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:34:17,214 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:34:17,215 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:34:17,215 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:34:17,216 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:17,216 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:17,216 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:17,216 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:34:17,217 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1015.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:34:17,217 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [1, 7, 3, 8, 0, 4, 6, 5, 2], 'cur_cost': 1015.0, 'intermediate_solutions': [{'tour': [0, 2, 1, 4, 6, 5, 3, 7, 8], 'cur_cost': 936.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 1, 4, 6, 3, 5, 7, 2], 'cur_cost': 1059.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 1, 4, 3, 6, 5, 7, 2], 'cur_cost': 1039.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:34:17,217 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1015.00)
2025-08-04 17:34:17,217 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:34:17,218 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:34:17,219 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [6, 7, 3, 8, 5, 4, 0, 1, 2], 'cur_cost': 915.0, 'intermediate_solutions': [{'tour': [0, 6, 5, 8, 7, 3, 4, 1, 2], 'cur_cost': 905.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 5, 8, 7, 3, 2, 4, 1], 'cur_cost': 766.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 2, 5, 8, 7, 3, 4, 1], 'cur_cost': 975.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 1, 4, 2, 5, 3, 8, 6, 0], dtype=int64), 'cur_cost': 921.0, 'intermediate_solutions': [{'tour': array([3, 6, 8, 1, 7, 4, 0, 5, 2]), 'cur_cost': 1170.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 3, 6, 8, 7, 4, 0, 5, 2]), 'cur_cost': 1109.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 1, 3, 6, 8, 4, 0, 5, 2]), 'cur_cost': 1141.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 1, 3, 6, 7, 4, 0, 5, 2]), 'cur_cost': 1102.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 7, 1, 3, 6, 4, 0, 5, 2]), 'cur_cost': 1111.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 5, 8, 4, 3, 6, 1, 2], 'cur_cost': 954.0, 'intermediate_solutions': [{'tour': [5, 3, 8, 2, 4, 0, 7, 6, 1], 'cur_cost': 830.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 3, 8, 2, 7, 0, 4, 1, 6], 'cur_cost': 835.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 3, 2, 8, 7, 0, 4, 6, 1], 'cur_cost': 1000.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 1, 6, 8, 4, 2, 7, 5, 3], dtype=int64), 'cur_cost': 839.0, 'intermediate_solutions': [{'tour': array([5, 6, 7, 2, 1, 3, 4, 8, 0]), 'cur_cost': 1065.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 5, 6, 7, 1, 3, 4, 8, 0]), 'cur_cost': 1094.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 2, 5, 6, 7, 3, 4, 8, 0]), 'cur_cost': 918.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 2, 5, 6, 1, 3, 4, 8, 0]), 'cur_cost': 1094.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 1, 2, 5, 6, 3, 4, 8, 0]), 'cur_cost': 1043.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 1, 5, 3, 4, 2, 7, 6], 'cur_cost': 1025.0, 'intermediate_solutions': [{'tour': [1, 7, 2, 3, 5, 8, 4, 0, 6], 'cur_cost': 961.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 2, 4, 8, 5, 3, 0, 1], 'cur_cost': 808.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 2, 3, 5, 8, 4, 0, 7, 1], 'cur_cost': 1007.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [6, 3, 7, 1, 5, 0, 4, 8, 2], 'cur_cost': 1048.0, 'intermediate_solutions': [{'tour': [0, 6, 4, 5, 2, 7, 8, 3, 1], 'cur_cost': 1063.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 4, 5, 2, 8, 7, 3, 1], 'cur_cost': 1015.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 5, 4, 3, 7, 8, 2, 1], 'cur_cost': 857.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [7, 8, 1, 3, 2, 4, 0, 6, 5], 'cur_cost': 954.0, 'intermediate_solutions': [{'tour': [7, 8, 5, 6, 3, 0, 4, 2, 1], 'cur_cost': 882.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 8, 5, 3, 6, 0, 4, 1, 2], 'cur_cost': 904.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 5, 3, 6, 7, 0, 4, 2, 1], 'cur_cost': 880.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [1, 3, 4, 7, 8, 5, 6, 0, 2], 'cur_cost': 997.0, 'intermediate_solutions': [{'tour': [7, 3, 8, 5, 1, 0, 4, 6, 2], 'cur_cost': 999.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 3, 1, 5, 7, 0, 4, 6, 2], 'cur_cost': 1087.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 3, 7, 5, 1, 0, 4, 6, 2], 'cur_cost': 941.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 1, 0, 7, 5, 4, 2, 6, 8], dtype=int64), 'cur_cost': 967.0, 'intermediate_solutions': [{'tour': array([4, 6, 2, 5, 7, 3, 0, 1, 8]), 'cur_cost': 1080.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 4, 6, 2, 7, 3, 0, 1, 8]), 'cur_cost': 1112.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 5, 4, 6, 2, 3, 0, 1, 8]), 'cur_cost': 1129.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 5, 4, 6, 7, 3, 0, 1, 8]), 'cur_cost': 1048.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 7, 5, 4, 6, 3, 0, 1, 8]), 'cur_cost': 1035.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 3, 8, 0, 4, 6, 5, 2], 'cur_cost': 1015.0, 'intermediate_solutions': [{'tour': [0, 2, 1, 4, 6, 5, 3, 7, 8], 'cur_cost': 936.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 1, 4, 6, 3, 5, 7, 2], 'cur_cost': 1059.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 1, 4, 3, 6, 5, 7, 2], 'cur_cost': 1039.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:34:17,221 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:34:17,221 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:34:17,222 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=839.000, 多样性=0.847
2025-08-04 17:34:17,222 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-04 17:34:17,222 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-04 17:34:17,222 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:34:17,223 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.01589223554837828, 'best_improvement': -0.09102730819245773}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.03313253012048216}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.03291660616986106, 'recent_improvements': [0.005594822282968596, 0.03572629192725046, -0.0602383900567535], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.7407407407407408, 'new_diversity': 0.7407407407407408, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:34:17,223 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-04 17:34:17,225 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_solution.json
2025-08-04 17:34:17,225 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_20250804_173417.solution
2025-08-04 17:34:17,226 - __main__ - INFO - 实例执行完成 - 运行时间: 6.10s, 最佳成本: 680.0
2025-08-04 17:34:17,226 - __main__ - INFO - 实例 simple1_9 处理完成
