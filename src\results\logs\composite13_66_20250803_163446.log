2025-08-03 16:34:46,737 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-08-03 16:34:46,737 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-03 16:34:46,741 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:34:46,758 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=9976.000, 多样性=0.954
2025-08-03 16:34:46,762 - PathExpert - INFO - 开始路径结构分析
2025-08-03 16:34:46,771 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.954
2025-08-03 16:34:46,799 - EliteExpert - INFO - 开始精英解分析
2025-08-03 16:34:46,803 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/1)
2025-08-03 16:34:46,803 - LandscapeExpert - INFO - 使用直接传入的种群数据: 20个个体
2025-08-03 16:34:46,803 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-03 16:34:46,804 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-03 16:34:47,068 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.150, 适应度梯度: -13684.270, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 1.000
2025-08-03 16:34:47,068 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-03 16:34:47,069 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-03 16:34:47,139 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记
2025-08-03 16:34:47,483 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_1_20250803_163447.html
2025-08-03 16:34:47,567 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_1_20250803_163447.html
2025-08-03 16:34:47,568 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-03 16:34:47,568 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-03 16:34:47,568 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.7657秒
2025-08-03 16:34:47,568 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-03 16:34:47,569 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.15, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -13684.269999999999, 'local_optima_density': 0.15, 'gradient_variance': 1773684154.7611, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0019, 'fitness_entropy': 0.9232196723355077, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -13684.270)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 1, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754210087.068903, 'performance_metrics': {}}}
2025-08-03 16:34:47,570 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-03 16:34:47,570 - experts.strategy.enhanced_strategy_expert - INFO - 开始增强策略分配分析 (迭代 0)
2025-08-03 16:34:47,570 - experts.analysis.individual_state_analyzer - INFO - 开始分析种群状态 (迭代 0, 种群大小: 20)
2025-08-03 16:34:47,571 - experts.strategy.enhanced_strategy_expert - ERROR - 策略分配过程中发生错误: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:34:47,571 - experts.strategy.enhanced_strategy_expert - WARNING - 使用回退策略分配
2025-08-03 16:34:47,572 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 16:34:47,572 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:34:47,572 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-03 16:34:47,573 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 16:34:47,573 - __main__ - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:34:47,573 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-03 16:34:47,574 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2, 4, 5} (总数: 4, 保护比例: 0.20)
2025-08-03 16:34:47,574 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-03 16:34:47,574 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-03 16:34:47,574 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:34:47,591 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:34:47,592 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:34:47,777 - ExplorationExpert - INFO - 探索路径生成完成，成本: 53048.0, 路径长度: 66
2025-08-03 16:34:47,778 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [54, 6, 23, 34, 21, 37, 13, 4, 0, 63, 56, 2, 64, 5, 1, 11, 62, 7, 19, 30, 9, 27, 31, 18, 14, 12, 35, 20, 25, 33, 36, 28, 32, 29, 42, 41, 48, 38, 50, 16, 49, 40, 43, 39, 3, 60, 65, 55, 59, 52, 57, 53, 22, 26, 46, 15, 24, 10, 61, 58, 8, 17, 44, 45, 51, 47], 'cur_cost': 53048.0}
2025-08-03 16:34:47,779 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 53048.00)
2025-08-03 16:34:47,780 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-03 16:34:47,781 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:34:47,788 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:34:47,791 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 108705.0
2025-08-03 16:34:49,885 - ExploitationExpert - INFO - res_population_num: 1
2025-08-03 16:34:49,886 - ExploitationExpert - INFO - res_population_costs: [9864.0]
2025-08-03 16:34:49,886 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 41, 50, 51,
       38, 45, 44, 39, 48, 43, 40, 49, 47, 46, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:34:49,888 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:34:49,888 - ExploitationExpert - INFO - populations: [{'tour': [54, 6, 23, 34, 21, 37, 13, 4, 0, 63, 56, 2, 64, 5, 1, 11, 62, 7, 19, 30, 9, 27, 31, 18, 14, 12, 35, 20, 25, 33, 36, 28, 32, 29, 42, 41, 48, 38, 50, 16, 49, 40, 43, 39, 3, 60, 65, 55, 59, 52, 57, 53, 22, 26, 46, 15, 24, 10, 61, 58, 8, 17, 44, 45, 51, 47], 'cur_cost': 53048.0}, {'tour': array([14, 44, 65, 16,  7, 26, 52, 21,  1, 27, 47, 12, 41, 50, 15, 59, 58,
        6, 25, 62, 10,  0, 30,  3, 54, 61, 39,  5, 51, 29, 33, 24, 13,  2,
       38, 36, 40, 63, 28, 57, 19, 43, 46, 20, 55, 11, 37, 22,  4,  8, 42,
       60, 53, 32, 56, 18, 48, 23, 35, 45, 49, 34,  9, 31, 64, 17],
      dtype=int64), 'cur_cost': 108705.0}, {'tour': array([56, 59, 62, 53, 64, 57, 54, 60, 58, 55, 61, 65, 52, 63,  2,  8,  5,
        4,  6,  9, 11,  7,  3,  1,  0, 10, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29,
       32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42],
      dtype=int64), 'cur_cost': 10030.0}, {'tour': array([ 2,  8,  5,  4,  6,  9, 11,  7,  3,  1,  0, 10, 55, 61, 53, 62, 59,
       56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23,
       13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29,
       32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42],
      dtype=int64), 'cur_cost': 10244.0}, {'tour': array([62, 59, 56, 58, 60, 64, 53, 65, 52, 63, 61, 55, 57, 54,  2,  8,  5,
        4,  6,  9, 11,  7,  3,  1,  0, 10, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29,
       32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42],
      dtype=int64), 'cur_cost': 10158.0}, {'tour': array([58, 56, 59, 62, 53, 64, 57, 54, 60, 55, 61, 65, 52, 63,  2,  8,  5,
        4,  6,  9, 11,  7,  3,  1,  0, 10, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29,
       32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42],
      dtype=int64), 'cur_cost': 10027.0}, {'tour': array([27,  7, 42, 23, 24, 60,  1, 37, 30, 59,  2, 33, 61, 46,  5, 11, 35,
       17, 26, 25, 50, 41, 39, 34, 63, 43, 22, 29, 18, 57, 28, 40, 12,  8,
       13,  0, 10, 15, 20,  9, 21, 65, 48, 64, 36, 51, 32,  4, 31,  6, 52,
       44, 14, 62, 19, 54, 55,  3, 45, 53, 49, 47, 16, 38, 56, 58],
      dtype=int64), 'cur_cost': 111770.0}, {'tour': array([29, 54, 48, 16, 60, 23, 35, 17, 51, 59, 34, 62,  1,  3, 52, 40, 63,
       49, 65,  5,  7, 26,  4, 55, 13, 31, 47, 37, 24, 25, 12, 41, 45, 43,
       22, 27,  0, 36, 56, 44, 21, 61, 19, 46,  2, 50, 30, 64, 14,  9,  6,
       20, 33, 39, 57, 10, 38, 58, 28, 15, 32, 53,  8, 18, 11, 42],
      dtype=int64), 'cur_cost': 116052.0}, {'tour': array([41,  5, 65, 59,  2, 54, 48, 24, 23, 32,  9, 31, 40, 63, 29, 58, 39,
       19,  6, 55, 43,  1, 51, 42, 17, 62, 50, 49, 44,  4, 38, 37, 56,  3,
       14, 30, 46, 35, 34, 60, 20, 28, 47, 25, 57,  8, 12, 13, 15, 64, 10,
        0, 36, 21, 52, 22, 61, 16, 45, 27, 53,  7, 18, 33, 11, 26],
      dtype=int64), 'cur_cost': 111949.0}, {'tour': array([35, 58, 15, 54, 52, 48, 33,  5,  9, 10, 49, 22, 17, 62, 63, 50, 55,
       28,  4, 64, 26,  1,  2, 46, 25, 13, 23, 11, 44, 51, 20, 57, 21, 53,
       32, 47, 27, 59, 45, 37,  8, 19, 30, 36, 39, 34, 12, 56, 65, 14, 16,
        7, 40,  0, 29, 31, 42,  3, 60, 38, 18, 24,  6, 43, 61, 41],
      dtype=int64), 'cur_cost': 114014.0}, {'tour': array([56, 41, 11, 64, 12, 31, 25, 57,  7, 49, 36, 26,  9, 37, 34,  2, 20,
       18, 50, 44, 39, 13, 23, 53, 40, 42, 24, 15,  3, 51, 29, 59, 30, 46,
       52, 48, 54,  6, 62, 32, 10,  5, 58, 35, 61, 14, 19, 65, 43, 27,  8,
       28, 22,  1, 21, 16, 55, 33, 45,  0, 47, 17,  4, 60, 38, 63],
      dtype=int64), 'cur_cost': 111394.0}, {'tour': array([33, 32,  5, 52,  6, 18, 59, 20, 58, 44, 51,  2, 22, 57, 27, 48, 14,
        7, 38, 30, 43, 31, 35, 13,  4, 15, 60, 34,  9, 54, 37,  3, 47, 62,
       36, 45, 41, 25,  8, 29, 53, 39, 55,  0, 56, 46, 64, 63, 65, 21, 61,
       28, 49, 50, 10, 40, 24, 16, 11, 23,  1, 17, 12, 26, 19, 42],
      dtype=int64), 'cur_cost': 113712.0}, {'tour': array([ 9,  1, 52, 63,  2, 15, 13, 53, 56, 11, 59, 17, 36, 21, 25,  6, 43,
        0, 45, 27, 29, 26, 60, 54, 24, 16, 47, 51, 20, 46, 28, 22, 39, 50,
       34, 62,  4, 30, 44, 48, 23, 10, 14,  8, 40, 55, 35, 32,  5, 38,  3,
       58, 64, 65, 33, 49, 41, 31, 19, 12, 42, 37, 57,  7, 18, 61],
      dtype=int64), 'cur_cost': 93236.0}, {'tour': array([29, 61, 26, 15, 63, 38, 31, 57, 25, 48, 47, 10, 58, 35, 30, 32, 43,
        1,  2, 22, 16, 62, 39,  4, 56, 24, 40, 52, 42,  6, 59, 46, 45, 19,
       28, 44, 18, 36, 50, 64,  9, 54, 23, 17, 37, 12, 33, 51,  8, 27,  7,
       53, 55, 41,  3, 34, 60, 49, 11, 20, 13, 14,  5,  0, 21, 65],
      dtype=int64), 'cur_cost': 114038.0}, {'tour': array([33, 62, 54, 63, 35,  4, 51, 30, 19, 11, 41, 15, 40, 21, 53, 22, 29,
       39, 44, 42, 14,  9, 45, 38, 49, 31, 25, 17, 57, 32, 43, 16,  0, 56,
       23, 10, 64, 58, 13,  8, 52, 46, 20, 12,  5, 55,  2, 37, 28, 18,  7,
       24, 61, 34, 59, 36,  1, 50, 47, 27,  3, 60, 65, 26,  6, 48],
      dtype=int64), 'cur_cost': 101840.0}, {'tour': array([61, 63, 15, 14,  3, 46, 24, 26, 13, 21, 60, 42,  5, 34, 23,  9, 35,
        6,  1, 10, 22, 18, 27, 65, 51, 19,  8, 59, 53, 55, 48, 40, 49,  0,
       56, 33, 31, 37, 38, 39, 29, 28, 57, 64, 16,  4, 36, 47, 25,  2, 11,
        7, 44, 58, 43, 12, 54, 62, 41, 20, 30, 50, 45, 32, 52, 17],
      dtype=int64), 'cur_cost': 93374.0}, {'tour': array([56, 22, 37, 57, 39, 15, 23, 48, 36, 27,  1,  9,  2, 53, 31, 34, 47,
       30,  5, 49,  3, 58, 33, 19, 21, 55,  7, 14, 38, 18, 28, 61, 60, 42,
       63, 52, 10,  6, 20, 24, 17, 54, 45, 29,  8, 51, 16, 12, 40, 64, 43,
       13, 44, 41,  0, 11, 59, 35, 46, 26, 25, 32,  4, 65, 50, 62],
      dtype=int64), 'cur_cost': 100399.0}, {'tour': array([31,  4, 25,  1, 42, 15, 41, 53, 37, 44, 50, 43, 27, 11, 28, 30, 56,
       52, 17, 62, 29, 35, 58, 26, 54, 49,  2, 38, 23, 18,  8, 59, 40, 63,
       61, 14, 51, 55, 47,  9, 34,  3,  7, 13,  6, 57, 32, 45, 19, 65,  0,
       36, 10, 20, 48, 16, 46, 64, 39, 21, 33, 24,  5, 22, 60, 12],
      dtype=int64), 'cur_cost': 115798.0}, {'tour': array([30,  9, 21,  2, 11, 26, 61, 15, 23, 53, 43, 10, 24, 58, 32, 36,  4,
       35, 63, 41,  0,  7, 44, 12, 17, 65,  8, 19, 57, 13, 55, 38, 33, 50,
       60,  3,  1, 20, 14, 39, 45, 16, 64,  5, 22, 27,  6, 47, 46, 40, 51,
       25, 34, 29, 28, 49, 52, 59, 54, 62, 31, 48, 42, 56, 37, 18],
      dtype=int64), 'cur_cost': 99069.0}, {'tour': array([19,  9, 26, 34, 50, 22, 38, 28, 18, 64, 61,  3, 60, 62, 36, 30, 65,
       39, 47,  5, 11, 51, 43, 24, 45,  0, 44, 63,  8, 58, 46, 52,  4, 17,
       42, 48, 35, 40, 21, 23, 27, 57, 31, 32, 53,  1, 16, 59,  6,  2, 14,
       41, 55, 20, 54, 12, 33, 29, 25, 56, 15, 10, 49, 37, 13,  7],
      dtype=int64), 'cur_cost': 101302.0}]
2025-08-03 16:34:49,901 - ExploitationExpert - INFO - 局部搜索耗时: 2.11秒
2025-08-03 16:34:49,901 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-03 16:34:49,902 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([14, 44, 65, 16,  7, 26, 52, 21,  1, 27, 47, 12, 41, 50, 15, 59, 58,
        6, 25, 62, 10,  0, 30,  3, 54, 61, 39,  5, 51, 29, 33, 24, 13,  2,
       38, 36, 40, 63, 28, 57, 19, 43, 46, 20, 55, 11, 37, 22,  4,  8, 42,
       60, 53, 32, 56, 18, 48, 23, 35, 45, 49, 34,  9, 31, 64, 17],
      dtype=int64), 'cur_cost': 108705.0}
2025-08-03 16:34:49,902 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 108705.00)
2025-08-03 16:34:49,903 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-03 16:34:49,903 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-03 16:34:49,903 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:34:49,908 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:34:49,909 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:34:49,909 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12336.0, 路径长度: 66
2025-08-03 16:34:49,909 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 8, 12, 16, 18, 23, 22, 15, 14, 20, 21, 13, 19, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12336.0}
2025-08-03 16:34:49,910 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 12336.00)
2025-08-03 16:34:49,910 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-03 16:34:49,910 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-03 16:34:49,911 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:34:49,923 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:34:49,924 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:34:49,925 - ExplorationExpert - INFO - 探索路径生成完成，成本: 86313.0, 路径长度: 66
2025-08-03 16:34:49,925 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [23, 0, 17, 1, 30, 31, 12, 29, 36, 9, 5, 6, 48, 16, 40, 43, 39, 59, 52, 26, 46, 44, 27, 20, 47, 42, 35, 25, 64, 58, 8, 61, 28, 15, 13, 4, 11, 33, 55, 18, 3, 32, 50, 34, 37, 24, 60, 56, 63, 19, 22, 51, 57, 2, 10, 41, 45, 21, 53, 62, 38, 54, 14, 49, 7, 65], 'cur_cost': 86313.0}
2025-08-03 16:34:49,926 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 86313.00)
2025-08-03 16:34:49,926 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-03 16:34:49,926 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:34:49,927 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:34:49,927 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 109453.0
2025-08-03 16:34:52,308 - ExploitationExpert - INFO - res_population_num: 2
2025-08-03 16:34:52,308 - ExploitationExpert - INFO - res_population_costs: [9864.0, 9529.0]
2025-08-03 16:34:52,308 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 41, 50, 51,
       38, 45, 44, 39, 48, 43, 40, 49, 47, 46, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64)]
2025-08-03 16:34:52,309 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:34:52,309 - ExploitationExpert - INFO - populations: [{'tour': [54, 6, 23, 34, 21, 37, 13, 4, 0, 63, 56, 2, 64, 5, 1, 11, 62, 7, 19, 30, 9, 27, 31, 18, 14, 12, 35, 20, 25, 33, 36, 28, 32, 29, 42, 41, 48, 38, 50, 16, 49, 40, 43, 39, 3, 60, 65, 55, 59, 52, 57, 53, 22, 26, 46, 15, 24, 10, 61, 58, 8, 17, 44, 45, 51, 47], 'cur_cost': 53048.0}, {'tour': array([14, 44, 65, 16,  7, 26, 52, 21,  1, 27, 47, 12, 41, 50, 15, 59, 58,
        6, 25, 62, 10,  0, 30,  3, 54, 61, 39,  5, 51, 29, 33, 24, 13,  2,
       38, 36, 40, 63, 28, 57, 19, 43, 46, 20, 55, 11, 37, 22,  4,  8, 42,
       60, 53, 32, 56, 18, 48, 23, 35, 45, 49, 34,  9, 31, 64, 17],
      dtype=int64), 'cur_cost': 108705.0}, {'tour': [0, 8, 12, 16, 18, 23, 22, 15, 14, 20, 21, 13, 19, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12336.0}, {'tour': [23, 0, 17, 1, 30, 31, 12, 29, 36, 9, 5, 6, 48, 16, 40, 43, 39, 59, 52, 26, 46, 44, 27, 20, 47, 42, 35, 25, 64, 58, 8, 61, 28, 15, 13, 4, 11, 33, 55, 18, 3, 32, 50, 34, 37, 24, 60, 56, 63, 19, 22, 51, 57, 2, 10, 41, 45, 21, 53, 62, 38, 54, 14, 49, 7, 65], 'cur_cost': 86313.0}, {'tour': array([11, 39,  4, 59, 54, 46, 42, 56, 20,  9, 47, 53,  3, 38, 55, 35,  6,
       19, 52, 15, 45, 12, 44, 22, 58,  7, 33, 28, 25, 10, 65, 29,  2,  8,
       36,  0, 24, 49, 43, 27, 26, 61, 62,  5, 50, 14,  1, 48, 21, 63, 64,
       41, 13, 31, 17, 18, 30, 40, 23, 57, 34, 51, 37, 16, 60, 32],
      dtype=int64), 'cur_cost': 109453.0}, {'tour': array([58, 56, 59, 62, 53, 64, 57, 54, 60, 55, 61, 65, 52, 63,  2,  8,  5,
        4,  6,  9, 11,  7,  3,  1,  0, 10, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29,
       32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42],
      dtype=int64), 'cur_cost': 10027.0}, {'tour': array([27,  7, 42, 23, 24, 60,  1, 37, 30, 59,  2, 33, 61, 46,  5, 11, 35,
       17, 26, 25, 50, 41, 39, 34, 63, 43, 22, 29, 18, 57, 28, 40, 12,  8,
       13,  0, 10, 15, 20,  9, 21, 65, 48, 64, 36, 51, 32,  4, 31,  6, 52,
       44, 14, 62, 19, 54, 55,  3, 45, 53, 49, 47, 16, 38, 56, 58],
      dtype=int64), 'cur_cost': 111770.0}, {'tour': array([29, 54, 48, 16, 60, 23, 35, 17, 51, 59, 34, 62,  1,  3, 52, 40, 63,
       49, 65,  5,  7, 26,  4, 55, 13, 31, 47, 37, 24, 25, 12, 41, 45, 43,
       22, 27,  0, 36, 56, 44, 21, 61, 19, 46,  2, 50, 30, 64, 14,  9,  6,
       20, 33, 39, 57, 10, 38, 58, 28, 15, 32, 53,  8, 18, 11, 42],
      dtype=int64), 'cur_cost': 116052.0}, {'tour': array([41,  5, 65, 59,  2, 54, 48, 24, 23, 32,  9, 31, 40, 63, 29, 58, 39,
       19,  6, 55, 43,  1, 51, 42, 17, 62, 50, 49, 44,  4, 38, 37, 56,  3,
       14, 30, 46, 35, 34, 60, 20, 28, 47, 25, 57,  8, 12, 13, 15, 64, 10,
        0, 36, 21, 52, 22, 61, 16, 45, 27, 53,  7, 18, 33, 11, 26],
      dtype=int64), 'cur_cost': 111949.0}, {'tour': array([35, 58, 15, 54, 52, 48, 33,  5,  9, 10, 49, 22, 17, 62, 63, 50, 55,
       28,  4, 64, 26,  1,  2, 46, 25, 13, 23, 11, 44, 51, 20, 57, 21, 53,
       32, 47, 27, 59, 45, 37,  8, 19, 30, 36, 39, 34, 12, 56, 65, 14, 16,
        7, 40,  0, 29, 31, 42,  3, 60, 38, 18, 24,  6, 43, 61, 41],
      dtype=int64), 'cur_cost': 114014.0}, {'tour': array([56, 41, 11, 64, 12, 31, 25, 57,  7, 49, 36, 26,  9, 37, 34,  2, 20,
       18, 50, 44, 39, 13, 23, 53, 40, 42, 24, 15,  3, 51, 29, 59, 30, 46,
       52, 48, 54,  6, 62, 32, 10,  5, 58, 35, 61, 14, 19, 65, 43, 27,  8,
       28, 22,  1, 21, 16, 55, 33, 45,  0, 47, 17,  4, 60, 38, 63],
      dtype=int64), 'cur_cost': 111394.0}, {'tour': array([33, 32,  5, 52,  6, 18, 59, 20, 58, 44, 51,  2, 22, 57, 27, 48, 14,
        7, 38, 30, 43, 31, 35, 13,  4, 15, 60, 34,  9, 54, 37,  3, 47, 62,
       36, 45, 41, 25,  8, 29, 53, 39, 55,  0, 56, 46, 64, 63, 65, 21, 61,
       28, 49, 50, 10, 40, 24, 16, 11, 23,  1, 17, 12, 26, 19, 42],
      dtype=int64), 'cur_cost': 113712.0}, {'tour': array([ 9,  1, 52, 63,  2, 15, 13, 53, 56, 11, 59, 17, 36, 21, 25,  6, 43,
        0, 45, 27, 29, 26, 60, 54, 24, 16, 47, 51, 20, 46, 28, 22, 39, 50,
       34, 62,  4, 30, 44, 48, 23, 10, 14,  8, 40, 55, 35, 32,  5, 38,  3,
       58, 64, 65, 33, 49, 41, 31, 19, 12, 42, 37, 57,  7, 18, 61],
      dtype=int64), 'cur_cost': 93236.0}, {'tour': array([29, 61, 26, 15, 63, 38, 31, 57, 25, 48, 47, 10, 58, 35, 30, 32, 43,
        1,  2, 22, 16, 62, 39,  4, 56, 24, 40, 52, 42,  6, 59, 46, 45, 19,
       28, 44, 18, 36, 50, 64,  9, 54, 23, 17, 37, 12, 33, 51,  8, 27,  7,
       53, 55, 41,  3, 34, 60, 49, 11, 20, 13, 14,  5,  0, 21, 65],
      dtype=int64), 'cur_cost': 114038.0}, {'tour': array([33, 62, 54, 63, 35,  4, 51, 30, 19, 11, 41, 15, 40, 21, 53, 22, 29,
       39, 44, 42, 14,  9, 45, 38, 49, 31, 25, 17, 57, 32, 43, 16,  0, 56,
       23, 10, 64, 58, 13,  8, 52, 46, 20, 12,  5, 55,  2, 37, 28, 18,  7,
       24, 61, 34, 59, 36,  1, 50, 47, 27,  3, 60, 65, 26,  6, 48],
      dtype=int64), 'cur_cost': 101840.0}, {'tour': array([61, 63, 15, 14,  3, 46, 24, 26, 13, 21, 60, 42,  5, 34, 23,  9, 35,
        6,  1, 10, 22, 18, 27, 65, 51, 19,  8, 59, 53, 55, 48, 40, 49,  0,
       56, 33, 31, 37, 38, 39, 29, 28, 57, 64, 16,  4, 36, 47, 25,  2, 11,
        7, 44, 58, 43, 12, 54, 62, 41, 20, 30, 50, 45, 32, 52, 17],
      dtype=int64), 'cur_cost': 93374.0}, {'tour': array([56, 22, 37, 57, 39, 15, 23, 48, 36, 27,  1,  9,  2, 53, 31, 34, 47,
       30,  5, 49,  3, 58, 33, 19, 21, 55,  7, 14, 38, 18, 28, 61, 60, 42,
       63, 52, 10,  6, 20, 24, 17, 54, 45, 29,  8, 51, 16, 12, 40, 64, 43,
       13, 44, 41,  0, 11, 59, 35, 46, 26, 25, 32,  4, 65, 50, 62],
      dtype=int64), 'cur_cost': 100399.0}, {'tour': array([31,  4, 25,  1, 42, 15, 41, 53, 37, 44, 50, 43, 27, 11, 28, 30, 56,
       52, 17, 62, 29, 35, 58, 26, 54, 49,  2, 38, 23, 18,  8, 59, 40, 63,
       61, 14, 51, 55, 47,  9, 34,  3,  7, 13,  6, 57, 32, 45, 19, 65,  0,
       36, 10, 20, 48, 16, 46, 64, 39, 21, 33, 24,  5, 22, 60, 12],
      dtype=int64), 'cur_cost': 115798.0}, {'tour': array([30,  9, 21,  2, 11, 26, 61, 15, 23, 53, 43, 10, 24, 58, 32, 36,  4,
       35, 63, 41,  0,  7, 44, 12, 17, 65,  8, 19, 57, 13, 55, 38, 33, 50,
       60,  3,  1, 20, 14, 39, 45, 16, 64,  5, 22, 27,  6, 47, 46, 40, 51,
       25, 34, 29, 28, 49, 52, 59, 54, 62, 31, 48, 42, 56, 37, 18],
      dtype=int64), 'cur_cost': 99069.0}, {'tour': array([19,  9, 26, 34, 50, 22, 38, 28, 18, 64, 61,  3, 60, 62, 36, 30, 65,
       39, 47,  5, 11, 51, 43, 24, 45,  0, 44, 63,  8, 58, 46, 52,  4, 17,
       42, 48, 35, 40, 21, 23, 27, 57, 31, 32, 53,  1, 16, 59,  6,  2, 14,
       41, 55, 20, 54, 12, 33, 29, 25, 56, 15, 10, 49, 37, 13,  7],
      dtype=int64), 'cur_cost': 101302.0}]
2025-08-03 16:34:52,324 - ExploitationExpert - INFO - 局部搜索耗时: 2.40秒
2025-08-03 16:34:52,324 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-03 16:34:52,325 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([11, 39,  4, 59, 54, 46, 42, 56, 20,  9, 47, 53,  3, 38, 55, 35,  6,
       19, 52, 15, 45, 12, 44, 22, 58,  7, 33, 28, 25, 10, 65, 29,  2,  8,
       36,  0, 24, 49, 43, 27, 26, 61, 62,  5, 50, 14,  1, 48, 21, 63, 64,
       41, 13, 31, 17, 18, 30, 40, 23, 57, 34, 51, 37, 16, 60, 32],
      dtype=int64), 'cur_cost': 109453.0}
2025-08-03 16:34:52,325 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 109453.00)
2025-08-03 16:34:52,326 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-03 16:34:52,326 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-03 16:34:52,326 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:34:52,342 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:34:52,343 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:34:52,343 - ExplorationExpert - INFO - 探索路径生成完成，成本: 63269.0, 路径长度: 66
2025-08-03 16:34:52,343 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [5, 8, 59, 17, 29, 3, 57, 22, 30, 14, 12, 11, 20, 25, 10, 58, 2, 54, 53, 39, 36, 35, 0, 56, 52, 64, 1, 61, 62, 23, 31, 18, 26, 33, 7, 6, 55, 4, 13, 27, 34, 24, 16, 9, 60, 21, 28, 49, 48, 50, 45, 51, 46, 40, 42, 38, 43, 19, 32, 47, 44, 41, 37, 15, 65, 63], 'cur_cost': 63269.0}
2025-08-03 16:34:52,344 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 63269.00)
2025-08-03 16:34:52,344 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-03 16:34:52,344 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-03 16:34:52,344 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:34:52,353 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:34:52,355 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:34:52,356 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12342.0, 路径长度: 66
2025-08-03 16:34:52,357 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 1, 14, 19, 16, 18, 12, 22, 23, 13, 20, 21, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12342.0}
2025-08-03 16:34:52,357 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 12342.00)
2025-08-03 16:34:52,358 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-03 16:34:52,358 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:34:52,359 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:34:52,360 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 97297.0
2025-08-03 16:34:52,967 - ExploitationExpert - INFO - res_population_num: 2
2025-08-03 16:34:52,967 - ExploitationExpert - INFO - res_population_costs: [9864.0, 9529.0]
2025-08-03 16:34:52,967 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 41, 50, 51,
       38, 45, 44, 39, 48, 43, 40, 49, 47, 46, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64)]
2025-08-03 16:34:52,969 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:34:52,969 - ExploitationExpert - INFO - populations: [{'tour': [54, 6, 23, 34, 21, 37, 13, 4, 0, 63, 56, 2, 64, 5, 1, 11, 62, 7, 19, 30, 9, 27, 31, 18, 14, 12, 35, 20, 25, 33, 36, 28, 32, 29, 42, 41, 48, 38, 50, 16, 49, 40, 43, 39, 3, 60, 65, 55, 59, 52, 57, 53, 22, 26, 46, 15, 24, 10, 61, 58, 8, 17, 44, 45, 51, 47], 'cur_cost': 53048.0}, {'tour': array([14, 44, 65, 16,  7, 26, 52, 21,  1, 27, 47, 12, 41, 50, 15, 59, 58,
        6, 25, 62, 10,  0, 30,  3, 54, 61, 39,  5, 51, 29, 33, 24, 13,  2,
       38, 36, 40, 63, 28, 57, 19, 43, 46, 20, 55, 11, 37, 22,  4,  8, 42,
       60, 53, 32, 56, 18, 48, 23, 35, 45, 49, 34,  9, 31, 64, 17],
      dtype=int64), 'cur_cost': 108705.0}, {'tour': [0, 8, 12, 16, 18, 23, 22, 15, 14, 20, 21, 13, 19, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12336.0}, {'tour': [23, 0, 17, 1, 30, 31, 12, 29, 36, 9, 5, 6, 48, 16, 40, 43, 39, 59, 52, 26, 46, 44, 27, 20, 47, 42, 35, 25, 64, 58, 8, 61, 28, 15, 13, 4, 11, 33, 55, 18, 3, 32, 50, 34, 37, 24, 60, 56, 63, 19, 22, 51, 57, 2, 10, 41, 45, 21, 53, 62, 38, 54, 14, 49, 7, 65], 'cur_cost': 86313.0}, {'tour': array([11, 39,  4, 59, 54, 46, 42, 56, 20,  9, 47, 53,  3, 38, 55, 35,  6,
       19, 52, 15, 45, 12, 44, 22, 58,  7, 33, 28, 25, 10, 65, 29,  2,  8,
       36,  0, 24, 49, 43, 27, 26, 61, 62,  5, 50, 14,  1, 48, 21, 63, 64,
       41, 13, 31, 17, 18, 30, 40, 23, 57, 34, 51, 37, 16, 60, 32],
      dtype=int64), 'cur_cost': 109453.0}, {'tour': [5, 8, 59, 17, 29, 3, 57, 22, 30, 14, 12, 11, 20, 25, 10, 58, 2, 54, 53, 39, 36, 35, 0, 56, 52, 64, 1, 61, 62, 23, 31, 18, 26, 33, 7, 6, 55, 4, 13, 27, 34, 24, 16, 9, 60, 21, 28, 49, 48, 50, 45, 51, 46, 40, 42, 38, 43, 19, 32, 47, 44, 41, 37, 15, 65, 63], 'cur_cost': 63269.0}, {'tour': [0, 1, 14, 19, 16, 18, 12, 22, 23, 13, 20, 21, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12342.0}, {'tour': array([18,  2, 65, 32, 25, 55, 16, 38, 53, 60,  1, 24, 30,  5, 20, 28, 47,
       11, 44, 42,  6, 64, 23, 56,  8, 39, 62, 52, 40, 37, 29, 27, 19, 43,
       14, 21, 63, 33,  7, 59, 17, 61, 57,  0, 41, 46, 48, 26, 13,  4, 54,
       35, 58, 15, 31,  3, 36, 34, 50, 51, 49, 10, 22,  9, 45, 12],
      dtype=int64), 'cur_cost': 97297.0}, {'tour': array([41,  5, 65, 59,  2, 54, 48, 24, 23, 32,  9, 31, 40, 63, 29, 58, 39,
       19,  6, 55, 43,  1, 51, 42, 17, 62, 50, 49, 44,  4, 38, 37, 56,  3,
       14, 30, 46, 35, 34, 60, 20, 28, 47, 25, 57,  8, 12, 13, 15, 64, 10,
        0, 36, 21, 52, 22, 61, 16, 45, 27, 53,  7, 18, 33, 11, 26],
      dtype=int64), 'cur_cost': 111949.0}, {'tour': array([35, 58, 15, 54, 52, 48, 33,  5,  9, 10, 49, 22, 17, 62, 63, 50, 55,
       28,  4, 64, 26,  1,  2, 46, 25, 13, 23, 11, 44, 51, 20, 57, 21, 53,
       32, 47, 27, 59, 45, 37,  8, 19, 30, 36, 39, 34, 12, 56, 65, 14, 16,
        7, 40,  0, 29, 31, 42,  3, 60, 38, 18, 24,  6, 43, 61, 41],
      dtype=int64), 'cur_cost': 114014.0}, {'tour': array([56, 41, 11, 64, 12, 31, 25, 57,  7, 49, 36, 26,  9, 37, 34,  2, 20,
       18, 50, 44, 39, 13, 23, 53, 40, 42, 24, 15,  3, 51, 29, 59, 30, 46,
       52, 48, 54,  6, 62, 32, 10,  5, 58, 35, 61, 14, 19, 65, 43, 27,  8,
       28, 22,  1, 21, 16, 55, 33, 45,  0, 47, 17,  4, 60, 38, 63],
      dtype=int64), 'cur_cost': 111394.0}, {'tour': array([33, 32,  5, 52,  6, 18, 59, 20, 58, 44, 51,  2, 22, 57, 27, 48, 14,
        7, 38, 30, 43, 31, 35, 13,  4, 15, 60, 34,  9, 54, 37,  3, 47, 62,
       36, 45, 41, 25,  8, 29, 53, 39, 55,  0, 56, 46, 64, 63, 65, 21, 61,
       28, 49, 50, 10, 40, 24, 16, 11, 23,  1, 17, 12, 26, 19, 42],
      dtype=int64), 'cur_cost': 113712.0}, {'tour': array([ 9,  1, 52, 63,  2, 15, 13, 53, 56, 11, 59, 17, 36, 21, 25,  6, 43,
        0, 45, 27, 29, 26, 60, 54, 24, 16, 47, 51, 20, 46, 28, 22, 39, 50,
       34, 62,  4, 30, 44, 48, 23, 10, 14,  8, 40, 55, 35, 32,  5, 38,  3,
       58, 64, 65, 33, 49, 41, 31, 19, 12, 42, 37, 57,  7, 18, 61],
      dtype=int64), 'cur_cost': 93236.0}, {'tour': array([29, 61, 26, 15, 63, 38, 31, 57, 25, 48, 47, 10, 58, 35, 30, 32, 43,
        1,  2, 22, 16, 62, 39,  4, 56, 24, 40, 52, 42,  6, 59, 46, 45, 19,
       28, 44, 18, 36, 50, 64,  9, 54, 23, 17, 37, 12, 33, 51,  8, 27,  7,
       53, 55, 41,  3, 34, 60, 49, 11, 20, 13, 14,  5,  0, 21, 65],
      dtype=int64), 'cur_cost': 114038.0}, {'tour': array([33, 62, 54, 63, 35,  4, 51, 30, 19, 11, 41, 15, 40, 21, 53, 22, 29,
       39, 44, 42, 14,  9, 45, 38, 49, 31, 25, 17, 57, 32, 43, 16,  0, 56,
       23, 10, 64, 58, 13,  8, 52, 46, 20, 12,  5, 55,  2, 37, 28, 18,  7,
       24, 61, 34, 59, 36,  1, 50, 47, 27,  3, 60, 65, 26,  6, 48],
      dtype=int64), 'cur_cost': 101840.0}, {'tour': array([61, 63, 15, 14,  3, 46, 24, 26, 13, 21, 60, 42,  5, 34, 23,  9, 35,
        6,  1, 10, 22, 18, 27, 65, 51, 19,  8, 59, 53, 55, 48, 40, 49,  0,
       56, 33, 31, 37, 38, 39, 29, 28, 57, 64, 16,  4, 36, 47, 25,  2, 11,
        7, 44, 58, 43, 12, 54, 62, 41, 20, 30, 50, 45, 32, 52, 17],
      dtype=int64), 'cur_cost': 93374.0}, {'tour': array([56, 22, 37, 57, 39, 15, 23, 48, 36, 27,  1,  9,  2, 53, 31, 34, 47,
       30,  5, 49,  3, 58, 33, 19, 21, 55,  7, 14, 38, 18, 28, 61, 60, 42,
       63, 52, 10,  6, 20, 24, 17, 54, 45, 29,  8, 51, 16, 12, 40, 64, 43,
       13, 44, 41,  0, 11, 59, 35, 46, 26, 25, 32,  4, 65, 50, 62],
      dtype=int64), 'cur_cost': 100399.0}, {'tour': array([31,  4, 25,  1, 42, 15, 41, 53, 37, 44, 50, 43, 27, 11, 28, 30, 56,
       52, 17, 62, 29, 35, 58, 26, 54, 49,  2, 38, 23, 18,  8, 59, 40, 63,
       61, 14, 51, 55, 47,  9, 34,  3,  7, 13,  6, 57, 32, 45, 19, 65,  0,
       36, 10, 20, 48, 16, 46, 64, 39, 21, 33, 24,  5, 22, 60, 12],
      dtype=int64), 'cur_cost': 115798.0}, {'tour': array([30,  9, 21,  2, 11, 26, 61, 15, 23, 53, 43, 10, 24, 58, 32, 36,  4,
       35, 63, 41,  0,  7, 44, 12, 17, 65,  8, 19, 57, 13, 55, 38, 33, 50,
       60,  3,  1, 20, 14, 39, 45, 16, 64,  5, 22, 27,  6, 47, 46, 40, 51,
       25, 34, 29, 28, 49, 52, 59, 54, 62, 31, 48, 42, 56, 37, 18],
      dtype=int64), 'cur_cost': 99069.0}, {'tour': array([19,  9, 26, 34, 50, 22, 38, 28, 18, 64, 61,  3, 60, 62, 36, 30, 65,
       39, 47,  5, 11, 51, 43, 24, 45,  0, 44, 63,  8, 58, 46, 52,  4, 17,
       42, 48, 35, 40, 21, 23, 27, 57, 31, 32, 53,  1, 16, 59,  6,  2, 14,
       41, 55, 20, 54, 12, 33, 29, 25, 56, 15, 10, 49, 37, 13,  7],
      dtype=int64), 'cur_cost': 101302.0}]
2025-08-03 16:34:52,979 - ExploitationExpert - INFO - 局部搜索耗时: 0.62秒
2025-08-03 16:34:52,979 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-03 16:34:52,982 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([18,  2, 65, 32, 25, 55, 16, 38, 53, 60,  1, 24, 30,  5, 20, 28, 47,
       11, 44, 42,  6, 64, 23, 56,  8, 39, 62, 52, 40, 37, 29, 27, 19, 43,
       14, 21, 63, 33,  7, 59, 17, 61, 57,  0, 41, 46, 48, 26, 13,  4, 54,
       35, 58, 15, 31,  3, 36, 34, 50, 51, 49, 10, 22,  9, 45, 12],
      dtype=int64), 'cur_cost': 97297.0}
2025-08-03 16:34:52,985 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 97297.00)
2025-08-03 16:34:52,985 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-03 16:34:52,986 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-03 16:34:52,986 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:34:52,992 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:34:52,993 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:34:52,993 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12443.0, 路径长度: 66
2025-08-03 16:34:52,994 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 6, 13, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12443.0}
2025-08-03 16:34:52,994 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 12443.00)
2025-08-03 16:34:52,995 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-03 16:34:52,995 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-03 16:34:52,995 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:34:53,004 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:34:53,005 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:34:53,006 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12413.0, 路径长度: 66
2025-08-03 16:34:53,006 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 3, 10, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12413.0}
2025-08-03 16:34:53,007 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 12413.00)
2025-08-03 16:34:53,007 - experts.management.collaboration_manager - INFO - 为个体 10 生成利用路径
2025-08-03 16:34:53,007 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:34:53,008 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:34:53,008 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 10 处的路径，新成本: 106474.0
2025-08-03 16:34:53,059 - ExploitationExpert - INFO - res_population_num: 2
2025-08-03 16:34:53,060 - ExploitationExpert - INFO - res_population_costs: [9864.0, 9529.0]
2025-08-03 16:34:53,060 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 41, 50, 51,
       38, 45, 44, 39, 48, 43, 40, 49, 47, 46, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64)]
2025-08-03 16:34:53,061 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:34:53,061 - ExploitationExpert - INFO - populations: [{'tour': [54, 6, 23, 34, 21, 37, 13, 4, 0, 63, 56, 2, 64, 5, 1, 11, 62, 7, 19, 30, 9, 27, 31, 18, 14, 12, 35, 20, 25, 33, 36, 28, 32, 29, 42, 41, 48, 38, 50, 16, 49, 40, 43, 39, 3, 60, 65, 55, 59, 52, 57, 53, 22, 26, 46, 15, 24, 10, 61, 58, 8, 17, 44, 45, 51, 47], 'cur_cost': 53048.0}, {'tour': array([14, 44, 65, 16,  7, 26, 52, 21,  1, 27, 47, 12, 41, 50, 15, 59, 58,
        6, 25, 62, 10,  0, 30,  3, 54, 61, 39,  5, 51, 29, 33, 24, 13,  2,
       38, 36, 40, 63, 28, 57, 19, 43, 46, 20, 55, 11, 37, 22,  4,  8, 42,
       60, 53, 32, 56, 18, 48, 23, 35, 45, 49, 34,  9, 31, 64, 17],
      dtype=int64), 'cur_cost': 108705.0}, {'tour': [0, 8, 12, 16, 18, 23, 22, 15, 14, 20, 21, 13, 19, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12336.0}, {'tour': [23, 0, 17, 1, 30, 31, 12, 29, 36, 9, 5, 6, 48, 16, 40, 43, 39, 59, 52, 26, 46, 44, 27, 20, 47, 42, 35, 25, 64, 58, 8, 61, 28, 15, 13, 4, 11, 33, 55, 18, 3, 32, 50, 34, 37, 24, 60, 56, 63, 19, 22, 51, 57, 2, 10, 41, 45, 21, 53, 62, 38, 54, 14, 49, 7, 65], 'cur_cost': 86313.0}, {'tour': array([11, 39,  4, 59, 54, 46, 42, 56, 20,  9, 47, 53,  3, 38, 55, 35,  6,
       19, 52, 15, 45, 12, 44, 22, 58,  7, 33, 28, 25, 10, 65, 29,  2,  8,
       36,  0, 24, 49, 43, 27, 26, 61, 62,  5, 50, 14,  1, 48, 21, 63, 64,
       41, 13, 31, 17, 18, 30, 40, 23, 57, 34, 51, 37, 16, 60, 32],
      dtype=int64), 'cur_cost': 109453.0}, {'tour': [5, 8, 59, 17, 29, 3, 57, 22, 30, 14, 12, 11, 20, 25, 10, 58, 2, 54, 53, 39, 36, 35, 0, 56, 52, 64, 1, 61, 62, 23, 31, 18, 26, 33, 7, 6, 55, 4, 13, 27, 34, 24, 16, 9, 60, 21, 28, 49, 48, 50, 45, 51, 46, 40, 42, 38, 43, 19, 32, 47, 44, 41, 37, 15, 65, 63], 'cur_cost': 63269.0}, {'tour': [0, 1, 14, 19, 16, 18, 12, 22, 23, 13, 20, 21, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12342.0}, {'tour': array([18,  2, 65, 32, 25, 55, 16, 38, 53, 60,  1, 24, 30,  5, 20, 28, 47,
       11, 44, 42,  6, 64, 23, 56,  8, 39, 62, 52, 40, 37, 29, 27, 19, 43,
       14, 21, 63, 33,  7, 59, 17, 61, 57,  0, 41, 46, 48, 26, 13,  4, 54,
       35, 58, 15, 31,  3, 36, 34, 50, 51, 49, 10, 22,  9, 45, 12],
      dtype=int64), 'cur_cost': 97297.0}, {'tour': [0, 6, 13, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12443.0}, {'tour': [0, 3, 10, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12413.0}, {'tour': array([ 5, 27, 58, 16,  1, 25, 12, 65, 52, 10, 47,  2, 63, 45, 64, 37, 18,
       11, 13, 20, 53, 40, 39, 49, 35, 50, 14, 46, 22, 28, 61, 33, 51, 36,
       56,  8, 23,  4,  3, 17, 60, 38, 48, 62, 54, 42,  7, 29, 32,  0, 24,
       43,  9, 59, 55, 57, 21, 26,  6, 31, 30, 44, 15, 41, 34, 19],
      dtype=int64), 'cur_cost': 106474.0}, {'tour': array([33, 32,  5, 52,  6, 18, 59, 20, 58, 44, 51,  2, 22, 57, 27, 48, 14,
        7, 38, 30, 43, 31, 35, 13,  4, 15, 60, 34,  9, 54, 37,  3, 47, 62,
       36, 45, 41, 25,  8, 29, 53, 39, 55,  0, 56, 46, 64, 63, 65, 21, 61,
       28, 49, 50, 10, 40, 24, 16, 11, 23,  1, 17, 12, 26, 19, 42],
      dtype=int64), 'cur_cost': 113712.0}, {'tour': array([ 9,  1, 52, 63,  2, 15, 13, 53, 56, 11, 59, 17, 36, 21, 25,  6, 43,
        0, 45, 27, 29, 26, 60, 54, 24, 16, 47, 51, 20, 46, 28, 22, 39, 50,
       34, 62,  4, 30, 44, 48, 23, 10, 14,  8, 40, 55, 35, 32,  5, 38,  3,
       58, 64, 65, 33, 49, 41, 31, 19, 12, 42, 37, 57,  7, 18, 61],
      dtype=int64), 'cur_cost': 93236.0}, {'tour': array([29, 61, 26, 15, 63, 38, 31, 57, 25, 48, 47, 10, 58, 35, 30, 32, 43,
        1,  2, 22, 16, 62, 39,  4, 56, 24, 40, 52, 42,  6, 59, 46, 45, 19,
       28, 44, 18, 36, 50, 64,  9, 54, 23, 17, 37, 12, 33, 51,  8, 27,  7,
       53, 55, 41,  3, 34, 60, 49, 11, 20, 13, 14,  5,  0, 21, 65],
      dtype=int64), 'cur_cost': 114038.0}, {'tour': array([33, 62, 54, 63, 35,  4, 51, 30, 19, 11, 41, 15, 40, 21, 53, 22, 29,
       39, 44, 42, 14,  9, 45, 38, 49, 31, 25, 17, 57, 32, 43, 16,  0, 56,
       23, 10, 64, 58, 13,  8, 52, 46, 20, 12,  5, 55,  2, 37, 28, 18,  7,
       24, 61, 34, 59, 36,  1, 50, 47, 27,  3, 60, 65, 26,  6, 48],
      dtype=int64), 'cur_cost': 101840.0}, {'tour': array([61, 63, 15, 14,  3, 46, 24, 26, 13, 21, 60, 42,  5, 34, 23,  9, 35,
        6,  1, 10, 22, 18, 27, 65, 51, 19,  8, 59, 53, 55, 48, 40, 49,  0,
       56, 33, 31, 37, 38, 39, 29, 28, 57, 64, 16,  4, 36, 47, 25,  2, 11,
        7, 44, 58, 43, 12, 54, 62, 41, 20, 30, 50, 45, 32, 52, 17],
      dtype=int64), 'cur_cost': 93374.0}, {'tour': array([56, 22, 37, 57, 39, 15, 23, 48, 36, 27,  1,  9,  2, 53, 31, 34, 47,
       30,  5, 49,  3, 58, 33, 19, 21, 55,  7, 14, 38, 18, 28, 61, 60, 42,
       63, 52, 10,  6, 20, 24, 17, 54, 45, 29,  8, 51, 16, 12, 40, 64, 43,
       13, 44, 41,  0, 11, 59, 35, 46, 26, 25, 32,  4, 65, 50, 62],
      dtype=int64), 'cur_cost': 100399.0}, {'tour': array([31,  4, 25,  1, 42, 15, 41, 53, 37, 44, 50, 43, 27, 11, 28, 30, 56,
       52, 17, 62, 29, 35, 58, 26, 54, 49,  2, 38, 23, 18,  8, 59, 40, 63,
       61, 14, 51, 55, 47,  9, 34,  3,  7, 13,  6, 57, 32, 45, 19, 65,  0,
       36, 10, 20, 48, 16, 46, 64, 39, 21, 33, 24,  5, 22, 60, 12],
      dtype=int64), 'cur_cost': 115798.0}, {'tour': array([30,  9, 21,  2, 11, 26, 61, 15, 23, 53, 43, 10, 24, 58, 32, 36,  4,
       35, 63, 41,  0,  7, 44, 12, 17, 65,  8, 19, 57, 13, 55, 38, 33, 50,
       60,  3,  1, 20, 14, 39, 45, 16, 64,  5, 22, 27,  6, 47, 46, 40, 51,
       25, 34, 29, 28, 49, 52, 59, 54, 62, 31, 48, 42, 56, 37, 18],
      dtype=int64), 'cur_cost': 99069.0}, {'tour': array([19,  9, 26, 34, 50, 22, 38, 28, 18, 64, 61,  3, 60, 62, 36, 30, 65,
       39, 47,  5, 11, 51, 43, 24, 45,  0, 44, 63,  8, 58, 46, 52,  4, 17,
       42, 48, 35, 40, 21, 23, 27, 57, 31, 32, 53,  1, 16, 59,  6,  2, 14,
       41, 55, 20, 54, 12, 33, 29, 25, 56, 15, 10, 49, 37, 13,  7],
      dtype=int64), 'cur_cost': 101302.0}]
2025-08-03 16:34:53,070 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-08-03 16:34:53,070 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-03 16:34:53,071 - experts.management.collaboration_manager - INFO - 个体 10 利用路径生成报告: {'new_tour': array([ 5, 27, 58, 16,  1, 25, 12, 65, 52, 10, 47,  2, 63, 45, 64, 37, 18,
       11, 13, 20, 53, 40, 39, 49, 35, 50, 14, 46, 22, 28, 61, 33, 51, 36,
       56,  8, 23,  4,  3, 17, 60, 38, 48, 62, 54, 42,  7, 29, 32,  0, 24,
       43,  9, 59, 55, 57, 21, 26,  6, 31, 30, 44, 15, 41, 34, 19],
      dtype=int64), 'cur_cost': 106474.0}
2025-08-03 16:34:53,071 - experts.management.collaboration_manager - INFO - 个体 10 接受新路径 (成本: 106474.00)
2025-08-03 16:34:53,072 - experts.management.collaboration_manager - INFO - 为个体 11 生成探索路径
2025-08-03 16:34:53,072 - ExplorationExpert - INFO - 开始为个体 11 生成探索路径（算法实现）
2025-08-03 16:34:53,072 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:34:53,076 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:34:53,076 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:34:53,077 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10356.0, 路径长度: 66
2025-08-03 16:34:53,077 - experts.management.collaboration_manager - INFO - 个体 11 探索路径生成报告: {'new_tour': [0, 9, 3, 8, 2, 6, 4, 5, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10356.0}
2025-08-03 16:34:53,077 - experts.management.collaboration_manager - INFO - 个体 11 接受新路径 (成本: 10356.00)
2025-08-03 16:34:53,077 - experts.management.collaboration_manager - INFO - 为个体 12 生成探索路径
2025-08-03 16:34:53,078 - ExplorationExpert - INFO - 开始为个体 12 生成探索路径（算法实现）
2025-08-03 16:34:53,078 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:34:53,085 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:34:53,086 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:34:53,087 - ExplorationExpert - INFO - 探索路径生成完成，成本: 95168.0, 路径长度: 66
2025-08-03 16:34:53,087 - experts.management.collaboration_manager - INFO - 个体 12 探索路径生成报告: {'new_tour': [54, 12, 29, 23, 13, 21, 11, 62, 7, 26, 46, 18, 47, 42, 33, 36, 9, 41, 48, 16, 49, 32, 24, 65, 19, 57, 15, 10, 61, 58, 51, 17, 50, 27, 39, 30, 35, 40, 28, 34, 43, 20, 52, 64, 3, 56, 60, 2, 22, 45, 55, 4, 44, 25, 1, 38, 37, 6, 53, 0, 31, 5, 63, 8, 59, 14], 'cur_cost': 95168.0}
2025-08-03 16:34:53,088 - experts.management.collaboration_manager - INFO - 个体 12 接受新路径 (成本: 95168.00)
2025-08-03 16:34:53,088 - experts.management.collaboration_manager - INFO - 为个体 13 生成利用路径
2025-08-03 16:34:53,089 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:34:53,089 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:34:53,089 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 13 处的路径，新成本: 104085.0
2025-08-03 16:34:53,137 - ExploitationExpert - INFO - res_population_num: 2
2025-08-03 16:34:53,137 - ExploitationExpert - INFO - res_population_costs: [9864.0, 9529.0]
2025-08-03 16:34:53,137 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 41, 50, 51,
       38, 45, 44, 39, 48, 43, 40, 49, 47, 46, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64)]
2025-08-03 16:34:53,138 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:34:53,139 - ExploitationExpert - INFO - populations: [{'tour': [54, 6, 23, 34, 21, 37, 13, 4, 0, 63, 56, 2, 64, 5, 1, 11, 62, 7, 19, 30, 9, 27, 31, 18, 14, 12, 35, 20, 25, 33, 36, 28, 32, 29, 42, 41, 48, 38, 50, 16, 49, 40, 43, 39, 3, 60, 65, 55, 59, 52, 57, 53, 22, 26, 46, 15, 24, 10, 61, 58, 8, 17, 44, 45, 51, 47], 'cur_cost': 53048.0}, {'tour': array([14, 44, 65, 16,  7, 26, 52, 21,  1, 27, 47, 12, 41, 50, 15, 59, 58,
        6, 25, 62, 10,  0, 30,  3, 54, 61, 39,  5, 51, 29, 33, 24, 13,  2,
       38, 36, 40, 63, 28, 57, 19, 43, 46, 20, 55, 11, 37, 22,  4,  8, 42,
       60, 53, 32, 56, 18, 48, 23, 35, 45, 49, 34,  9, 31, 64, 17],
      dtype=int64), 'cur_cost': 108705.0}, {'tour': [0, 8, 12, 16, 18, 23, 22, 15, 14, 20, 21, 13, 19, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12336.0}, {'tour': [23, 0, 17, 1, 30, 31, 12, 29, 36, 9, 5, 6, 48, 16, 40, 43, 39, 59, 52, 26, 46, 44, 27, 20, 47, 42, 35, 25, 64, 58, 8, 61, 28, 15, 13, 4, 11, 33, 55, 18, 3, 32, 50, 34, 37, 24, 60, 56, 63, 19, 22, 51, 57, 2, 10, 41, 45, 21, 53, 62, 38, 54, 14, 49, 7, 65], 'cur_cost': 86313.0}, {'tour': array([11, 39,  4, 59, 54, 46, 42, 56, 20,  9, 47, 53,  3, 38, 55, 35,  6,
       19, 52, 15, 45, 12, 44, 22, 58,  7, 33, 28, 25, 10, 65, 29,  2,  8,
       36,  0, 24, 49, 43, 27, 26, 61, 62,  5, 50, 14,  1, 48, 21, 63, 64,
       41, 13, 31, 17, 18, 30, 40, 23, 57, 34, 51, 37, 16, 60, 32],
      dtype=int64), 'cur_cost': 109453.0}, {'tour': [5, 8, 59, 17, 29, 3, 57, 22, 30, 14, 12, 11, 20, 25, 10, 58, 2, 54, 53, 39, 36, 35, 0, 56, 52, 64, 1, 61, 62, 23, 31, 18, 26, 33, 7, 6, 55, 4, 13, 27, 34, 24, 16, 9, 60, 21, 28, 49, 48, 50, 45, 51, 46, 40, 42, 38, 43, 19, 32, 47, 44, 41, 37, 15, 65, 63], 'cur_cost': 63269.0}, {'tour': [0, 1, 14, 19, 16, 18, 12, 22, 23, 13, 20, 21, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12342.0}, {'tour': array([18,  2, 65, 32, 25, 55, 16, 38, 53, 60,  1, 24, 30,  5, 20, 28, 47,
       11, 44, 42,  6, 64, 23, 56,  8, 39, 62, 52, 40, 37, 29, 27, 19, 43,
       14, 21, 63, 33,  7, 59, 17, 61, 57,  0, 41, 46, 48, 26, 13,  4, 54,
       35, 58, 15, 31,  3, 36, 34, 50, 51, 49, 10, 22,  9, 45, 12],
      dtype=int64), 'cur_cost': 97297.0}, {'tour': [0, 6, 13, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12443.0}, {'tour': [0, 3, 10, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12413.0}, {'tour': array([ 5, 27, 58, 16,  1, 25, 12, 65, 52, 10, 47,  2, 63, 45, 64, 37, 18,
       11, 13, 20, 53, 40, 39, 49, 35, 50, 14, 46, 22, 28, 61, 33, 51, 36,
       56,  8, 23,  4,  3, 17, 60, 38, 48, 62, 54, 42,  7, 29, 32,  0, 24,
       43,  9, 59, 55, 57, 21, 26,  6, 31, 30, 44, 15, 41, 34, 19],
      dtype=int64), 'cur_cost': 106474.0}, {'tour': [0, 9, 3, 8, 2, 6, 4, 5, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10356.0}, {'tour': [54, 12, 29, 23, 13, 21, 11, 62, 7, 26, 46, 18, 47, 42, 33, 36, 9, 41, 48, 16, 49, 32, 24, 65, 19, 57, 15, 10, 61, 58, 51, 17, 50, 27, 39, 30, 35, 40, 28, 34, 43, 20, 52, 64, 3, 56, 60, 2, 22, 45, 55, 4, 44, 25, 1, 38, 37, 6, 53, 0, 31, 5, 63, 8, 59, 14], 'cur_cost': 95168.0}, {'tour': array([14, 42, 35, 41, 53, 47, 48, 38, 13, 64, 30, 50,  8, 10,  9, 40, 24,
       26, 49, 54,  6,  0, 44,  1, 57, 11, 29, 18, 23,  2, 63, 51, 32, 56,
       52, 58, 25,  5, 45, 34, 37, 20, 43, 62, 46, 16, 33, 28, 22, 59, 39,
       31, 19, 17, 65, 36, 60, 12, 27, 61,  3, 15, 21, 55,  4,  7],
      dtype=int64), 'cur_cost': 104085.0}, {'tour': array([33, 62, 54, 63, 35,  4, 51, 30, 19, 11, 41, 15, 40, 21, 53, 22, 29,
       39, 44, 42, 14,  9, 45, 38, 49, 31, 25, 17, 57, 32, 43, 16,  0, 56,
       23, 10, 64, 58, 13,  8, 52, 46, 20, 12,  5, 55,  2, 37, 28, 18,  7,
       24, 61, 34, 59, 36,  1, 50, 47, 27,  3, 60, 65, 26,  6, 48],
      dtype=int64), 'cur_cost': 101840.0}, {'tour': array([61, 63, 15, 14,  3, 46, 24, 26, 13, 21, 60, 42,  5, 34, 23,  9, 35,
        6,  1, 10, 22, 18, 27, 65, 51, 19,  8, 59, 53, 55, 48, 40, 49,  0,
       56, 33, 31, 37, 38, 39, 29, 28, 57, 64, 16,  4, 36, 47, 25,  2, 11,
        7, 44, 58, 43, 12, 54, 62, 41, 20, 30, 50, 45, 32, 52, 17],
      dtype=int64), 'cur_cost': 93374.0}, {'tour': array([56, 22, 37, 57, 39, 15, 23, 48, 36, 27,  1,  9,  2, 53, 31, 34, 47,
       30,  5, 49,  3, 58, 33, 19, 21, 55,  7, 14, 38, 18, 28, 61, 60, 42,
       63, 52, 10,  6, 20, 24, 17, 54, 45, 29,  8, 51, 16, 12, 40, 64, 43,
       13, 44, 41,  0, 11, 59, 35, 46, 26, 25, 32,  4, 65, 50, 62],
      dtype=int64), 'cur_cost': 100399.0}, {'tour': array([31,  4, 25,  1, 42, 15, 41, 53, 37, 44, 50, 43, 27, 11, 28, 30, 56,
       52, 17, 62, 29, 35, 58, 26, 54, 49,  2, 38, 23, 18,  8, 59, 40, 63,
       61, 14, 51, 55, 47,  9, 34,  3,  7, 13,  6, 57, 32, 45, 19, 65,  0,
       36, 10, 20, 48, 16, 46, 64, 39, 21, 33, 24,  5, 22, 60, 12],
      dtype=int64), 'cur_cost': 115798.0}, {'tour': array([30,  9, 21,  2, 11, 26, 61, 15, 23, 53, 43, 10, 24, 58, 32, 36,  4,
       35, 63, 41,  0,  7, 44, 12, 17, 65,  8, 19, 57, 13, 55, 38, 33, 50,
       60,  3,  1, 20, 14, 39, 45, 16, 64,  5, 22, 27,  6, 47, 46, 40, 51,
       25, 34, 29, 28, 49, 52, 59, 54, 62, 31, 48, 42, 56, 37, 18],
      dtype=int64), 'cur_cost': 99069.0}, {'tour': array([19,  9, 26, 34, 50, 22, 38, 28, 18, 64, 61,  3, 60, 62, 36, 30, 65,
       39, 47,  5, 11, 51, 43, 24, 45,  0, 44, 63,  8, 58, 46, 52,  4, 17,
       42, 48, 35, 40, 21, 23, 27, 57, 31, 32, 53,  1, 16, 59,  6,  2, 14,
       41, 55, 20, 54, 12, 33, 29, 25, 56, 15, 10, 49, 37, 13,  7],
      dtype=int64), 'cur_cost': 101302.0}]
2025-08-03 16:34:53,146 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-08-03 16:34:53,147 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-03 16:34:53,147 - experts.management.collaboration_manager - INFO - 个体 13 利用路径生成报告: {'new_tour': array([14, 42, 35, 41, 53, 47, 48, 38, 13, 64, 30, 50,  8, 10,  9, 40, 24,
       26, 49, 54,  6,  0, 44,  1, 57, 11, 29, 18, 23,  2, 63, 51, 32, 56,
       52, 58, 25,  5, 45, 34, 37, 20, 43, 62, 46, 16, 33, 28, 22, 59, 39,
       31, 19, 17, 65, 36, 60, 12, 27, 61,  3, 15, 21, 55,  4,  7],
      dtype=int64), 'cur_cost': 104085.0}
2025-08-03 16:34:53,150 - experts.management.collaboration_manager - INFO - 个体 13 接受新路径 (成本: 104085.00)
2025-08-03 16:34:53,150 - experts.management.collaboration_manager - INFO - 为个体 14 生成探索路径
2025-08-03 16:34:53,151 - ExplorationExpert - INFO - 开始为个体 14 生成探索路径（算法实现）
2025-08-03 16:34:53,151 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:34:53,158 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:34:53,159 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:34:53,160 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12335.0, 路径长度: 66
2025-08-03 16:34:53,160 - experts.management.collaboration_manager - INFO - 个体 14 探索路径生成报告: {'new_tour': [0, 17, 19, 18, 16, 23, 22, 12, 15, 14, 20, 21, 13, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12335.0}
2025-08-03 16:34:53,160 - experts.management.collaboration_manager - INFO - 个体 14 接受新路径 (成本: 12335.00)
2025-08-03 16:34:53,161 - experts.management.collaboration_manager - INFO - 为个体 15 生成探索路径
2025-08-03 16:34:53,161 - ExplorationExpert - INFO - 开始为个体 15 生成探索路径（算法实现）
2025-08-03 16:34:53,161 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:34:53,175 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:34:53,176 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:34:53,177 - ExplorationExpert - INFO - 探索路径生成完成，成本: 53162.0, 路径长度: 66
2025-08-03 16:34:53,177 - experts.management.collaboration_manager - INFO - 个体 15 探索路径生成报告: {'new_tour': [64, 57, 13, 37, 24, 16, 14, 26, 11, 2, 7, 4, 56, 21, 20, 27, 17, 19, 36, 40, 50, 12, 15, 3, 0, 10, 58, 55, 5, 62, 1, 18, 8, 33, 35, 22, 29, 49, 46, 34, 25, 28, 48, 45, 47, 43, 39, 44, 31, 6, 60, 52, 65, 54, 59, 63, 53, 9, 32, 23, 30, 42, 38, 51, 41, 61], 'cur_cost': 53162.0}
2025-08-03 16:34:53,177 - experts.management.collaboration_manager - INFO - 个体 15 接受新路径 (成本: 53162.00)
2025-08-03 16:34:53,178 - experts.management.collaboration_manager - INFO - 为个体 16 生成利用路径
2025-08-03 16:34:53,178 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:34:53,179 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:34:53,179 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 16 处的路径，新成本: 106728.0
2025-08-03 16:34:53,238 - ExploitationExpert - INFO - res_population_num: 4
2025-08-03 16:34:53,238 - ExploitationExpert - INFO - res_population_costs: [9864.0, 9529.0, 9521, 9521]
2025-08-03 16:34:53,238 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 41, 50, 51,
       38, 45, 44, 39, 48, 43, 40, 49, 47, 46, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:34:53,240 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:34:53,241 - ExploitationExpert - INFO - populations: [{'tour': [54, 6, 23, 34, 21, 37, 13, 4, 0, 63, 56, 2, 64, 5, 1, 11, 62, 7, 19, 30, 9, 27, 31, 18, 14, 12, 35, 20, 25, 33, 36, 28, 32, 29, 42, 41, 48, 38, 50, 16, 49, 40, 43, 39, 3, 60, 65, 55, 59, 52, 57, 53, 22, 26, 46, 15, 24, 10, 61, 58, 8, 17, 44, 45, 51, 47], 'cur_cost': 53048.0}, {'tour': array([14, 44, 65, 16,  7, 26, 52, 21,  1, 27, 47, 12, 41, 50, 15, 59, 58,
        6, 25, 62, 10,  0, 30,  3, 54, 61, 39,  5, 51, 29, 33, 24, 13,  2,
       38, 36, 40, 63, 28, 57, 19, 43, 46, 20, 55, 11, 37, 22,  4,  8, 42,
       60, 53, 32, 56, 18, 48, 23, 35, 45, 49, 34,  9, 31, 64, 17],
      dtype=int64), 'cur_cost': 108705.0}, {'tour': [0, 8, 12, 16, 18, 23, 22, 15, 14, 20, 21, 13, 19, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12336.0}, {'tour': [23, 0, 17, 1, 30, 31, 12, 29, 36, 9, 5, 6, 48, 16, 40, 43, 39, 59, 52, 26, 46, 44, 27, 20, 47, 42, 35, 25, 64, 58, 8, 61, 28, 15, 13, 4, 11, 33, 55, 18, 3, 32, 50, 34, 37, 24, 60, 56, 63, 19, 22, 51, 57, 2, 10, 41, 45, 21, 53, 62, 38, 54, 14, 49, 7, 65], 'cur_cost': 86313.0}, {'tour': array([11, 39,  4, 59, 54, 46, 42, 56, 20,  9, 47, 53,  3, 38, 55, 35,  6,
       19, 52, 15, 45, 12, 44, 22, 58,  7, 33, 28, 25, 10, 65, 29,  2,  8,
       36,  0, 24, 49, 43, 27, 26, 61, 62,  5, 50, 14,  1, 48, 21, 63, 64,
       41, 13, 31, 17, 18, 30, 40, 23, 57, 34, 51, 37, 16, 60, 32],
      dtype=int64), 'cur_cost': 109453.0}, {'tour': [5, 8, 59, 17, 29, 3, 57, 22, 30, 14, 12, 11, 20, 25, 10, 58, 2, 54, 53, 39, 36, 35, 0, 56, 52, 64, 1, 61, 62, 23, 31, 18, 26, 33, 7, 6, 55, 4, 13, 27, 34, 24, 16, 9, 60, 21, 28, 49, 48, 50, 45, 51, 46, 40, 42, 38, 43, 19, 32, 47, 44, 41, 37, 15, 65, 63], 'cur_cost': 63269.0}, {'tour': [0, 1, 14, 19, 16, 18, 12, 22, 23, 13, 20, 21, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12342.0}, {'tour': array([18,  2, 65, 32, 25, 55, 16, 38, 53, 60,  1, 24, 30,  5, 20, 28, 47,
       11, 44, 42,  6, 64, 23, 56,  8, 39, 62, 52, 40, 37, 29, 27, 19, 43,
       14, 21, 63, 33,  7, 59, 17, 61, 57,  0, 41, 46, 48, 26, 13,  4, 54,
       35, 58, 15, 31,  3, 36, 34, 50, 51, 49, 10, 22,  9, 45, 12],
      dtype=int64), 'cur_cost': 97297.0}, {'tour': [0, 6, 13, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12443.0}, {'tour': [0, 3, 10, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12413.0}, {'tour': array([ 5, 27, 58, 16,  1, 25, 12, 65, 52, 10, 47,  2, 63, 45, 64, 37, 18,
       11, 13, 20, 53, 40, 39, 49, 35, 50, 14, 46, 22, 28, 61, 33, 51, 36,
       56,  8, 23,  4,  3, 17, 60, 38, 48, 62, 54, 42,  7, 29, 32,  0, 24,
       43,  9, 59, 55, 57, 21, 26,  6, 31, 30, 44, 15, 41, 34, 19],
      dtype=int64), 'cur_cost': 106474.0}, {'tour': [0, 9, 3, 8, 2, 6, 4, 5, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10356.0}, {'tour': [54, 12, 29, 23, 13, 21, 11, 62, 7, 26, 46, 18, 47, 42, 33, 36, 9, 41, 48, 16, 49, 32, 24, 65, 19, 57, 15, 10, 61, 58, 51, 17, 50, 27, 39, 30, 35, 40, 28, 34, 43, 20, 52, 64, 3, 56, 60, 2, 22, 45, 55, 4, 44, 25, 1, 38, 37, 6, 53, 0, 31, 5, 63, 8, 59, 14], 'cur_cost': 95168.0}, {'tour': array([14, 42, 35, 41, 53, 47, 48, 38, 13, 64, 30, 50,  8, 10,  9, 40, 24,
       26, 49, 54,  6,  0, 44,  1, 57, 11, 29, 18, 23,  2, 63, 51, 32, 56,
       52, 58, 25,  5, 45, 34, 37, 20, 43, 62, 46, 16, 33, 28, 22, 59, 39,
       31, 19, 17, 65, 36, 60, 12, 27, 61,  3, 15, 21, 55,  4,  7],
      dtype=int64), 'cur_cost': 104085.0}, {'tour': [0, 17, 19, 18, 16, 23, 22, 12, 15, 14, 20, 21, 13, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12335.0}, {'tour': [64, 57, 13, 37, 24, 16, 14, 26, 11, 2, 7, 4, 56, 21, 20, 27, 17, 19, 36, 40, 50, 12, 15, 3, 0, 10, 58, 55, 5, 62, 1, 18, 8, 33, 35, 22, 29, 49, 46, 34, 25, 28, 48, 45, 47, 43, 39, 44, 31, 6, 60, 52, 65, 54, 59, 63, 53, 9, 32, 23, 30, 42, 38, 51, 41, 61], 'cur_cost': 53162.0}, {'tour': array([19, 17, 24, 42, 37, 11, 47,  1, 58,  6,  7, 31, 33,  2, 61, 13, 48,
       56,  0,  9, 32, 41, 29, 35, 14, 23, 10, 53, 52, 30, 40, 54, 63, 22,
       18, 65, 34, 50,  4, 25, 46, 27,  5, 28, 44, 45, 39,  8, 21, 26, 15,
       59, 43, 57, 12, 38, 62, 51, 20, 60,  3, 55, 49, 36, 64, 16],
      dtype=int64), 'cur_cost': 106728.0}, {'tour': array([31,  4, 25,  1, 42, 15, 41, 53, 37, 44, 50, 43, 27, 11, 28, 30, 56,
       52, 17, 62, 29, 35, 58, 26, 54, 49,  2, 38, 23, 18,  8, 59, 40, 63,
       61, 14, 51, 55, 47,  9, 34,  3,  7, 13,  6, 57, 32, 45, 19, 65,  0,
       36, 10, 20, 48, 16, 46, 64, 39, 21, 33, 24,  5, 22, 60, 12],
      dtype=int64), 'cur_cost': 115798.0}, {'tour': array([30,  9, 21,  2, 11, 26, 61, 15, 23, 53, 43, 10, 24, 58, 32, 36,  4,
       35, 63, 41,  0,  7, 44, 12, 17, 65,  8, 19, 57, 13, 55, 38, 33, 50,
       60,  3,  1, 20, 14, 39, 45, 16, 64,  5, 22, 27,  6, 47, 46, 40, 51,
       25, 34, 29, 28, 49, 52, 59, 54, 62, 31, 48, 42, 56, 37, 18],
      dtype=int64), 'cur_cost': 99069.0}, {'tour': array([19,  9, 26, 34, 50, 22, 38, 28, 18, 64, 61,  3, 60, 62, 36, 30, 65,
       39, 47,  5, 11, 51, 43, 24, 45,  0, 44, 63,  8, 58, 46, 52,  4, 17,
       42, 48, 35, 40, 21, 23, 27, 57, 31, 32, 53,  1, 16, 59,  6,  2, 14,
       41, 55, 20, 54, 12, 33, 29, 25, 56, 15, 10, 49, 37, 13,  7],
      dtype=int64), 'cur_cost': 101302.0}]
2025-08-03 16:34:53,252 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-08-03 16:34:53,253 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-03 16:34:53,254 - experts.management.collaboration_manager - INFO - 个体 16 利用路径生成报告: {'new_tour': array([19, 17, 24, 42, 37, 11, 47,  1, 58,  6,  7, 31, 33,  2, 61, 13, 48,
       56,  0,  9, 32, 41, 29, 35, 14, 23, 10, 53, 52, 30, 40, 54, 63, 22,
       18, 65, 34, 50,  4, 25, 46, 27,  5, 28, 44, 45, 39,  8, 21, 26, 15,
       59, 43, 57, 12, 38, 62, 51, 20, 60,  3, 55, 49, 36, 64, 16],
      dtype=int64), 'cur_cost': 106728.0}
2025-08-03 16:34:53,255 - experts.management.collaboration_manager - INFO - 个体 16 接受新路径 (成本: 106728.00)
2025-08-03 16:34:53,256 - experts.management.collaboration_manager - INFO - 为个体 17 生成探索路径
2025-08-03 16:34:53,256 - ExplorationExpert - INFO - 开始为个体 17 生成探索路径（算法实现）
2025-08-03 16:34:53,257 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:34:53,270 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:34:53,271 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:34:53,271 - ExplorationExpert - INFO - 探索路径生成完成，成本: 56935.0, 路径长度: 66
2025-08-03 16:34:53,272 - experts.management.collaboration_manager - INFO - 个体 17 探索路径生成报告: {'new_tour': [6, 10, 58, 3, 61, 14, 15, 7, 9, 5, 4, 12, 28, 13, 37, 0, 60, 1, 24, 35, 20, 18, 23, 26, 32, 29, 34, 11, 57, 55, 54, 62, 22, 40, 19, 16, 2, 63, 53, 59, 17, 49, 45, 38, 46, 43, 50, 51, 36, 48, 21, 25, 27, 8, 64, 47, 39, 42, 41, 44, 30, 33, 56, 65, 52, 31], 'cur_cost': 56935.0}
2025-08-03 16:34:53,272 - experts.management.collaboration_manager - INFO - 个体 17 接受新路径 (成本: 56935.00)
2025-08-03 16:34:53,272 - experts.management.collaboration_manager - INFO - 为个体 18 生成探索路径
2025-08-03 16:34:53,273 - ExplorationExpert - INFO - 开始为个体 18 生成探索路径（算法实现）
2025-08-03 16:34:53,273 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:34:53,277 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:34:53,277 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:34:53,278 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98556.0, 路径长度: 66
2025-08-03 16:34:53,279 - experts.management.collaboration_manager - INFO - 个体 18 探索路径生成报告: {'new_tour': [23, 1, 19, 57, 18, 63, 5, 6, 15, 16, 40, 43, 7, 53, 36, 27, 31, 34, 33, 64, 25, 8, 28, 41, 48, 49, 32, 52, 9, 24, 65, 59, 44, 26, 47, 45, 10, 61, 35, 37, 14, 21, 11, 39, 50, 20, 42, 29, 56, 0, 38, 12, 54, 2, 30, 3, 13, 22, 60, 17, 55, 58, 51, 4, 62, 46], 'cur_cost': 98556.0}
2025-08-03 16:34:53,279 - experts.management.collaboration_manager - INFO - 个体 18 接受新路径 (成本: 98556.00)
2025-08-03 16:34:53,279 - experts.management.collaboration_manager - INFO - 为个体 19 生成利用路径
2025-08-03 16:34:53,280 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:34:53,280 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:34:53,281 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 19 处的路径，新成本: 100487.0
2025-08-03 16:34:53,357 - ExploitationExpert - INFO - res_population_num: 4
2025-08-03 16:34:53,357 - ExploitationExpert - INFO - res_population_costs: [9864.0, 9529.0, 9521, 9521]
2025-08-03 16:34:53,358 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 41, 50, 51,
       38, 45, 44, 39, 48, 43, 40, 49, 47, 46, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:34:53,360 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:34:53,360 - ExploitationExpert - INFO - populations: [{'tour': [54, 6, 23, 34, 21, 37, 13, 4, 0, 63, 56, 2, 64, 5, 1, 11, 62, 7, 19, 30, 9, 27, 31, 18, 14, 12, 35, 20, 25, 33, 36, 28, 32, 29, 42, 41, 48, 38, 50, 16, 49, 40, 43, 39, 3, 60, 65, 55, 59, 52, 57, 53, 22, 26, 46, 15, 24, 10, 61, 58, 8, 17, 44, 45, 51, 47], 'cur_cost': 53048.0}, {'tour': array([14, 44, 65, 16,  7, 26, 52, 21,  1, 27, 47, 12, 41, 50, 15, 59, 58,
        6, 25, 62, 10,  0, 30,  3, 54, 61, 39,  5, 51, 29, 33, 24, 13,  2,
       38, 36, 40, 63, 28, 57, 19, 43, 46, 20, 55, 11, 37, 22,  4,  8, 42,
       60, 53, 32, 56, 18, 48, 23, 35, 45, 49, 34,  9, 31, 64, 17],
      dtype=int64), 'cur_cost': 108705.0}, {'tour': [0, 8, 12, 16, 18, 23, 22, 15, 14, 20, 21, 13, 19, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12336.0}, {'tour': [23, 0, 17, 1, 30, 31, 12, 29, 36, 9, 5, 6, 48, 16, 40, 43, 39, 59, 52, 26, 46, 44, 27, 20, 47, 42, 35, 25, 64, 58, 8, 61, 28, 15, 13, 4, 11, 33, 55, 18, 3, 32, 50, 34, 37, 24, 60, 56, 63, 19, 22, 51, 57, 2, 10, 41, 45, 21, 53, 62, 38, 54, 14, 49, 7, 65], 'cur_cost': 86313.0}, {'tour': array([11, 39,  4, 59, 54, 46, 42, 56, 20,  9, 47, 53,  3, 38, 55, 35,  6,
       19, 52, 15, 45, 12, 44, 22, 58,  7, 33, 28, 25, 10, 65, 29,  2,  8,
       36,  0, 24, 49, 43, 27, 26, 61, 62,  5, 50, 14,  1, 48, 21, 63, 64,
       41, 13, 31, 17, 18, 30, 40, 23, 57, 34, 51, 37, 16, 60, 32],
      dtype=int64), 'cur_cost': 109453.0}, {'tour': [5, 8, 59, 17, 29, 3, 57, 22, 30, 14, 12, 11, 20, 25, 10, 58, 2, 54, 53, 39, 36, 35, 0, 56, 52, 64, 1, 61, 62, 23, 31, 18, 26, 33, 7, 6, 55, 4, 13, 27, 34, 24, 16, 9, 60, 21, 28, 49, 48, 50, 45, 51, 46, 40, 42, 38, 43, 19, 32, 47, 44, 41, 37, 15, 65, 63], 'cur_cost': 63269.0}, {'tour': [0, 1, 14, 19, 16, 18, 12, 22, 23, 13, 20, 21, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12342.0}, {'tour': array([18,  2, 65, 32, 25, 55, 16, 38, 53, 60,  1, 24, 30,  5, 20, 28, 47,
       11, 44, 42,  6, 64, 23, 56,  8, 39, 62, 52, 40, 37, 29, 27, 19, 43,
       14, 21, 63, 33,  7, 59, 17, 61, 57,  0, 41, 46, 48, 26, 13,  4, 54,
       35, 58, 15, 31,  3, 36, 34, 50, 51, 49, 10, 22,  9, 45, 12],
      dtype=int64), 'cur_cost': 97297.0}, {'tour': [0, 6, 13, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12443.0}, {'tour': [0, 3, 10, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12413.0}, {'tour': array([ 5, 27, 58, 16,  1, 25, 12, 65, 52, 10, 47,  2, 63, 45, 64, 37, 18,
       11, 13, 20, 53, 40, 39, 49, 35, 50, 14, 46, 22, 28, 61, 33, 51, 36,
       56,  8, 23,  4,  3, 17, 60, 38, 48, 62, 54, 42,  7, 29, 32,  0, 24,
       43,  9, 59, 55, 57, 21, 26,  6, 31, 30, 44, 15, 41, 34, 19],
      dtype=int64), 'cur_cost': 106474.0}, {'tour': [0, 9, 3, 8, 2, 6, 4, 5, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10356.0}, {'tour': [54, 12, 29, 23, 13, 21, 11, 62, 7, 26, 46, 18, 47, 42, 33, 36, 9, 41, 48, 16, 49, 32, 24, 65, 19, 57, 15, 10, 61, 58, 51, 17, 50, 27, 39, 30, 35, 40, 28, 34, 43, 20, 52, 64, 3, 56, 60, 2, 22, 45, 55, 4, 44, 25, 1, 38, 37, 6, 53, 0, 31, 5, 63, 8, 59, 14], 'cur_cost': 95168.0}, {'tour': array([14, 42, 35, 41, 53, 47, 48, 38, 13, 64, 30, 50,  8, 10,  9, 40, 24,
       26, 49, 54,  6,  0, 44,  1, 57, 11, 29, 18, 23,  2, 63, 51, 32, 56,
       52, 58, 25,  5, 45, 34, 37, 20, 43, 62, 46, 16, 33, 28, 22, 59, 39,
       31, 19, 17, 65, 36, 60, 12, 27, 61,  3, 15, 21, 55,  4,  7],
      dtype=int64), 'cur_cost': 104085.0}, {'tour': [0, 17, 19, 18, 16, 23, 22, 12, 15, 14, 20, 21, 13, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12335.0}, {'tour': [64, 57, 13, 37, 24, 16, 14, 26, 11, 2, 7, 4, 56, 21, 20, 27, 17, 19, 36, 40, 50, 12, 15, 3, 0, 10, 58, 55, 5, 62, 1, 18, 8, 33, 35, 22, 29, 49, 46, 34, 25, 28, 48, 45, 47, 43, 39, 44, 31, 6, 60, 52, 65, 54, 59, 63, 53, 9, 32, 23, 30, 42, 38, 51, 41, 61], 'cur_cost': 53162.0}, {'tour': array([19, 17, 24, 42, 37, 11, 47,  1, 58,  6,  7, 31, 33,  2, 61, 13, 48,
       56,  0,  9, 32, 41, 29, 35, 14, 23, 10, 53, 52, 30, 40, 54, 63, 22,
       18, 65, 34, 50,  4, 25, 46, 27,  5, 28, 44, 45, 39,  8, 21, 26, 15,
       59, 43, 57, 12, 38, 62, 51, 20, 60,  3, 55, 49, 36, 64, 16],
      dtype=int64), 'cur_cost': 106728.0}, {'tour': [6, 10, 58, 3, 61, 14, 15, 7, 9, 5, 4, 12, 28, 13, 37, 0, 60, 1, 24, 35, 20, 18, 23, 26, 32, 29, 34, 11, 57, 55, 54, 62, 22, 40, 19, 16, 2, 63, 53, 59, 17, 49, 45, 38, 46, 43, 50, 51, 36, 48, 21, 25, 27, 8, 64, 47, 39, 42, 41, 44, 30, 33, 56, 65, 52, 31], 'cur_cost': 56935.0}, {'tour': [23, 1, 19, 57, 18, 63, 5, 6, 15, 16, 40, 43, 7, 53, 36, 27, 31, 34, 33, 64, 25, 8, 28, 41, 48, 49, 32, 52, 9, 24, 65, 59, 44, 26, 47, 45, 10, 61, 35, 37, 14, 21, 11, 39, 50, 20, 42, 29, 56, 0, 38, 12, 54, 2, 30, 3, 13, 22, 60, 17, 55, 58, 51, 4, 62, 46], 'cur_cost': 98556.0}, {'tour': array([23, 37, 28, 34, 48, 19, 24, 42, 65,  0, 33, 15, 36, 52, 50, 51, 62,
        3, 20, 58, 25, 43, 22,  9, 47, 32, 31, 41, 55, 21, 17, 60, 56,  7,
       61, 18, 64, 27,  6, 10, 26, 46, 40, 12, 16, 63, 57,  4, 39,  1, 45,
       54, 29, 30, 49, 14, 38, 13,  8,  2, 35, 44, 59, 53, 11,  5],
      dtype=int64), 'cur_cost': 100487.0}]
2025-08-03 16:34:53,367 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-03 16:34:53,367 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-03 16:34:53,368 - experts.management.collaboration_manager - INFO - 个体 19 利用路径生成报告: {'new_tour': array([23, 37, 28, 34, 48, 19, 24, 42, 65,  0, 33, 15, 36, 52, 50, 51, 62,
        3, 20, 58, 25, 43, 22,  9, 47, 32, 31, 41, 55, 21, 17, 60, 56,  7,
       61, 18, 64, 27,  6, 10, 26, 46, 40, 12, 16, 63, 57,  4, 39,  1, 45,
       54, 29, 30, 49, 14, 38, 13,  8,  2, 35, 44, 59, 53, 11,  5],
      dtype=int64), 'cur_cost': 100487.0}
2025-08-03 16:34:53,368 - experts.management.collaboration_manager - INFO - 个体 19 接受新路径 (成本: 100487.00)
2025-08-03 16:34:53,369 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 16, 'rejected': 4, 'elite_protected': 4}
2025-08-03 16:34:53,369 - experts.management.collaboration_manager - INFO - 接受率: 16/20 (80.0%)
2025-08-03 16:34:53,371 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [54, 6, 23, 34, 21, 37, 13, 4, 0, 63, 56, 2, 64, 5, 1, 11, 62, 7, 19, 30, 9, 27, 31, 18, 14, 12, 35, 20, 25, 33, 36, 28, 32, 29, 42, 41, 48, 38, 50, 16, 49, 40, 43, 39, 3, 60, 65, 55, 59, 52, 57, 53, 22, 26, 46, 15, 24, 10, 61, 58, 8, 17, 44, 45, 51, 47], 'cur_cost': 53048.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([14, 44, 65, 16,  7, 26, 52, 21,  1, 27, 47, 12, 41, 50, 15, 59, 58,
        6, 25, 62, 10,  0, 30,  3, 54, 61, 39,  5, 51, 29, 33, 24, 13,  2,
       38, 36, 40, 63, 28, 57, 19, 43, 46, 20, 55, 11, 37, 22,  4,  8, 42,
       60, 53, 32, 56, 18, 48, 23, 35, 45, 49, 34,  9, 31, 64, 17],
      dtype=int64), 'cur_cost': 108705.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 12, 16, 18, 23, 22, 15, 14, 20, 21, 13, 19, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12336.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [23, 0, 17, 1, 30, 31, 12, 29, 36, 9, 5, 6, 48, 16, 40, 43, 39, 59, 52, 26, 46, 44, 27, 20, 47, 42, 35, 25, 64, 58, 8, 61, 28, 15, 13, 4, 11, 33, 55, 18, 3, 32, 50, 34, 37, 24, 60, 56, 63, 19, 22, 51, 57, 2, 10, 41, 45, 21, 53, 62, 38, 54, 14, 49, 7, 65], 'cur_cost': 86313.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([11, 39,  4, 59, 54, 46, 42, 56, 20,  9, 47, 53,  3, 38, 55, 35,  6,
       19, 52, 15, 45, 12, 44, 22, 58,  7, 33, 28, 25, 10, 65, 29,  2,  8,
       36,  0, 24, 49, 43, 27, 26, 61, 62,  5, 50, 14,  1, 48, 21, 63, 64,
       41, 13, 31, 17, 18, 30, 40, 23, 57, 34, 51, 37, 16, 60, 32],
      dtype=int64), 'cur_cost': 109453.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [5, 8, 59, 17, 29, 3, 57, 22, 30, 14, 12, 11, 20, 25, 10, 58, 2, 54, 53, 39, 36, 35, 0, 56, 52, 64, 1, 61, 62, 23, 31, 18, 26, 33, 7, 6, 55, 4, 13, 27, 34, 24, 16, 9, 60, 21, 28, 49, 48, 50, 45, 51, 46, 40, 42, 38, 43, 19, 32, 47, 44, 41, 37, 15, 65, 63], 'cur_cost': 63269.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 14, 19, 16, 18, 12, 22, 23, 13, 20, 21, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12342.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([18,  2, 65, 32, 25, 55, 16, 38, 53, 60,  1, 24, 30,  5, 20, 28, 47,
       11, 44, 42,  6, 64, 23, 56,  8, 39, 62, 52, 40, 37, 29, 27, 19, 43,
       14, 21, 63, 33,  7, 59, 17, 61, 57,  0, 41, 46, 48, 26, 13,  4, 54,
       35, 58, 15, 31,  3, 36, 34, 50, 51, 49, 10, 22,  9, 45, 12],
      dtype=int64), 'cur_cost': 97297.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 13, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12443.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 10, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12413.0}}, {'individual': 10, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 5, 27, 58, 16,  1, 25, 12, 65, 52, 10, 47,  2, 63, 45, 64, 37, 18,
       11, 13, 20, 53, 40, 39, 49, 35, 50, 14, 46, 22, 28, 61, 33, 51, 36,
       56,  8, 23,  4,  3, 17, 60, 38, 48, 62, 54, 42,  7, 29, 32,  0, 24,
       43,  9, 59, 55, 57, 21, 26,  6, 31, 30, 44, 15, 41, 34, 19],
      dtype=int64), 'cur_cost': 106474.0}}, {'individual': 11, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 3, 8, 2, 6, 4, 5, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10356.0}}, {'individual': 12, 'strategy': 'explore', 'path_data': {'new_tour': [54, 12, 29, 23, 13, 21, 11, 62, 7, 26, 46, 18, 47, 42, 33, 36, 9, 41, 48, 16, 49, 32, 24, 65, 19, 57, 15, 10, 61, 58, 51, 17, 50, 27, 39, 30, 35, 40, 28, 34, 43, 20, 52, 64, 3, 56, 60, 2, 22, 45, 55, 4, 44, 25, 1, 38, 37, 6, 53, 0, 31, 5, 63, 8, 59, 14], 'cur_cost': 95168.0}}, {'individual': 13, 'strategy': 'exploit', 'path_data': {'new_tour': array([14, 42, 35, 41, 53, 47, 48, 38, 13, 64, 30, 50,  8, 10,  9, 40, 24,
       26, 49, 54,  6,  0, 44,  1, 57, 11, 29, 18, 23,  2, 63, 51, 32, 56,
       52, 58, 25,  5, 45, 34, 37, 20, 43, 62, 46, 16, 33, 28, 22, 59, 39,
       31, 19, 17, 65, 36, 60, 12, 27, 61,  3, 15, 21, 55,  4,  7],
      dtype=int64), 'cur_cost': 104085.0}}, {'individual': 14, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 19, 18, 16, 23, 22, 12, 15, 14, 20, 21, 13, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12335.0}}, {'individual': 15, 'strategy': 'explore', 'path_data': {'new_tour': [64, 57, 13, 37, 24, 16, 14, 26, 11, 2, 7, 4, 56, 21, 20, 27, 17, 19, 36, 40, 50, 12, 15, 3, 0, 10, 58, 55, 5, 62, 1, 18, 8, 33, 35, 22, 29, 49, 46, 34, 25, 28, 48, 45, 47, 43, 39, 44, 31, 6, 60, 52, 65, 54, 59, 63, 53, 9, 32, 23, 30, 42, 38, 51, 41, 61], 'cur_cost': 53162.0}}, {'individual': 16, 'strategy': 'exploit', 'path_data': {'new_tour': array([19, 17, 24, 42, 37, 11, 47,  1, 58,  6,  7, 31, 33,  2, 61, 13, 48,
       56,  0,  9, 32, 41, 29, 35, 14, 23, 10, 53, 52, 30, 40, 54, 63, 22,
       18, 65, 34, 50,  4, 25, 46, 27,  5, 28, 44, 45, 39,  8, 21, 26, 15,
       59, 43, 57, 12, 38, 62, 51, 20, 60,  3, 55, 49, 36, 64, 16],
      dtype=int64), 'cur_cost': 106728.0}}, {'individual': 17, 'strategy': 'explore', 'path_data': {'new_tour': [6, 10, 58, 3, 61, 14, 15, 7, 9, 5, 4, 12, 28, 13, 37, 0, 60, 1, 24, 35, 20, 18, 23, 26, 32, 29, 34, 11, 57, 55, 54, 62, 22, 40, 19, 16, 2, 63, 53, 59, 17, 49, 45, 38, 46, 43, 50, 51, 36, 48, 21, 25, 27, 8, 64, 47, 39, 42, 41, 44, 30, 33, 56, 65, 52, 31], 'cur_cost': 56935.0}}, {'individual': 18, 'strategy': 'explore', 'path_data': {'new_tour': [23, 1, 19, 57, 18, 63, 5, 6, 15, 16, 40, 43, 7, 53, 36, 27, 31, 34, 33, 64, 25, 8, 28, 41, 48, 49, 32, 52, 9, 24, 65, 59, 44, 26, 47, 45, 10, 61, 35, 37, 14, 21, 11, 39, 50, 20, 42, 29, 56, 0, 38, 12, 54, 2, 30, 3, 13, 22, 60, 17, 55, 58, 51, 4, 62, 46], 'cur_cost': 98556.0}}, {'individual': 19, 'strategy': 'exploit', 'path_data': {'new_tour': array([23, 37, 28, 34, 48, 19, 24, 42, 65,  0, 33, 15, 36, 52, 50, 51, 62,
        3, 20, 58, 25, 43, 22,  9, 47, 32, 31, 41, 55, 21, 17, 60, 56,  7,
       61, 18, 64, 27,  6, 10, 26, 46, 40, 12, 16, 63, 57,  4, 39,  1, 45,
       54, 29, 30, 49, 14, 38, 13,  8,  2, 35, 44, 59, 53, 11,  5],
      dtype=int64), 'cur_cost': 100487.0}}]
2025-08-03 16:34:53,374 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-03 16:34:53,375 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:34:53,391 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=10356.000, 多样性=0.955
2025-08-03 16:34:53,391 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-03 16:34:53,391 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-03 16:34:53,392 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-03 16:34:53,392 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 1, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.05902164305028799, 'best_improvement': -0.03809141940657578}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.0011708622564190073}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.9166666666666666, 'new_diversity': 0.9166666666666666, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-03 16:34:53,393 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-03 16:34:53,398 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_solution.json
2025-08-03 16:34:53,398 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_20250803_163453.solution
2025-08-03 16:34:53,399 - __main__ - INFO - 实例 composite13_66 处理完成
