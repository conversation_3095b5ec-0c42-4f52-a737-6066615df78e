# -*- coding: utf-8 -*-
"""
相似性计算工具模块

提供统一的相似性和多样性计算函数，消除专家模块间的重复实现。
"""

import numpy as np
import logging
from typing import List, Dict, Any, Tuple, Optional
from collections import defaultdict
from utils.analysis_cache import distance_cache, cached_analysis


class SimilarityCalculator:
    """相似性计算工具类"""
    
    @staticmethod
    def hamming_distance(seq1: List[int], seq2: List[int]) -> float:
        """
        计算两个序列的汉明距离（标准化）
        
        参数:
            seq1, seq2: 两个整数序列
            
        返回:
            float: 标准化的汉明距离 (0-1)，1表示完全不同
        """
        if not seq1 or not seq2 or len(seq1) != len(seq2):
            return 0.0
        
        different_count = sum(a != b for a, b in zip(seq1, seq2))
        return different_count / len(seq1)
    
    @staticmethod
    def hamming_similarity(seq1: List[int], seq2: List[int]) -> float:
        """
        计算两个序列的汉明相似度
        
        参数:
            seq1, seq2: 两个整数序列
            
        返回:
            float: 汉明相似度 (0-1)，1表示完全相同
        """
        return 1.0 - SimilarityCalculator.hamming_distance(seq1, seq2)
    
    @staticmethod
    def edge_based_similarity(tour1: List[int], tour2: List[int]) -> float:
        """
        基于边的相似性计算
        
        参数:
            tour1, tour2: 两个路径
            
        返回:
            float: 边相似度 (0-1)，1表示所有边都相同
        """
        if not tour1 or not tour2 or len(tour1) != len(tour2):
            return 0.0
        
        # 提取边集合
        edges1 = set()
        edges2 = set()
        
        for i in range(len(tour1)):
            # 标准化边（较小节点在前）
            edge1 = tuple(sorted([tour1[i], tour1[(i + 1) % len(tour1)]]))
            edge2 = tuple(sorted([tour2[i], tour2[(i + 1) % len(tour2)]]))
            edges1.add(edge1)
            edges2.add(edge2)
        
        # 计算交集大小
        common_edges = len(edges1.intersection(edges2))
        return common_edges / len(tour1)
    
    @staticmethod
    def shared_edge_count(tour1: List[int], tour2: List[int]) -> int:
        """
        计算两个路径共享的边数量
        
        参数:
            tour1, tour2: 两个路径
            
        返回:
            int: 共享边的数量
        """
        if not tour1 or not tour2 or len(tour1) != len(tour2):
            return 0
        
        edges1 = set()
        edges2 = set()
        
        for i in range(len(tour1)):
            edge1 = tuple(sorted([tour1[i], tour1[(i + 1) % len(tour1)]]))
            edge2 = tuple(sorted([tour2[i], tour2[(i + 1) % len(tour2)]]))
            edges1.add(edge1)
            edges2.add(edge2)
        
        return len(edges1.intersection(edges2))
    
    @staticmethod
    def calculate_pairwise_distances(tours: List[List[int]], method: str = 'hamming') -> np.ndarray:
        """
        计算路径列表的成对距离矩阵（支持缓存）

        参数:
            tours: 路径列表
            method: 距离计算方法 ('hamming', 'edge_based')

        返回:
            np.ndarray: 距离矩阵
        """
        # 尝试从缓存获取
        cached_result = distance_cache.get_pairwise_distances(tours, method)
        if cached_result is not None:
            return cached_result

        n = len(tours)
        distance_matrix = np.zeros((n, n))

        for i in range(n):
            for j in range(i + 1, n):
                if method == 'hamming':
                    distance = SimilarityCalculator.hamming_distance(tours[i], tours[j])
                elif method == 'edge_based':
                    similarity = SimilarityCalculator.edge_based_similarity(tours[i], tours[j])
                    distance = 1.0 - similarity
                else:
                    distance = SimilarityCalculator.hamming_distance(tours[i], tours[j])

                distance_matrix[i, j] = distance
                distance_matrix[j, i] = distance

        # 缓存结果
        distance_cache.cache_pairwise_distances(tours, method, distance_matrix)

        return distance_matrix
    
    @staticmethod
    def calculate_population_diversity(tours: List[List[int]], method: str = 'hamming') -> float:
        """
        计算种群的多样性
        
        参数:
            tours: 路径列表
            method: 多样性计算方法 ('hamming', 'edge_based')
            
        返回:
            float: 多样性指标 (0-1)，值越大表示多样性越高
        """
        if len(tours) <= 1:
            return 0.0
        
        try:
            distance_matrix = SimilarityCalculator.calculate_pairwise_distances(tours, method)
            
            # 计算平均距离作为多样性指标
            n = len(tours)
            total_distance = np.sum(distance_matrix) / (n * (n - 1))
            
            return total_distance
        
        except Exception as e:
            logging.warning(f"计算种群多样性时出错: {e}")
            return 0.5  # 返回中等多样性作为默认值


class DiversityAnalyzer:
    """多样性分析工具类"""
    
    @staticmethod
    def analyze_population_diversity(tours: List[List[int]]) -> Dict[str, Any]:
        """
        全面分析种群多样性
        
        参数:
            tours: 路径列表
            
        返回:
            Dict[str, Any]: 多样性分析结果
        """
        if len(tours) <= 1:
            return {
                "hamming_diversity": 0.0,
                "edge_diversity": 0.0,
                "diversity_distribution": [],
                "avg_pairwise_distance": 0.0,
                "min_distance": 0.0,
                "max_distance": 0.0
            }
        
        # 计算不同方法的多样性
        hamming_diversity = SimilarityCalculator.calculate_population_diversity(tours, 'hamming')
        edge_diversity = SimilarityCalculator.calculate_population_diversity(tours, 'edge_based')
        
        # 计算距离分布
        hamming_matrix = SimilarityCalculator.calculate_pairwise_distances(tours, 'hamming')
        
        # 提取上三角矩阵的值（避免重复和对角线）
        upper_triangle = hamming_matrix[np.triu_indices_from(hamming_matrix, k=1)]
        
        return {
            "hamming_diversity": hamming_diversity,
            "edge_diversity": edge_diversity,
            "diversity_distribution": upper_triangle.tolist(),
            "avg_pairwise_distance": np.mean(upper_triangle),
            "min_distance": np.min(upper_triangle),
            "max_distance": np.max(upper_triangle),
            "std_distance": np.std(upper_triangle)
        }
    
    @staticmethod
    def calculate_individual_diversity_contribution(individual_tour: List[int], 
                                                  all_tours: List[List[int]], 
                                                  method: str = 'hamming') -> float:
        """
        计算个体对种群多样性的贡献
        
        参数:
            individual_tour: 个体路径
            all_tours: 所有路径
            method: 计算方法
            
        返回:
            float: 多样性贡献 (0-1)
        """
        if len(all_tours) <= 1:
            return 0.0
        
        try:
            total_distance = 0.0
            count = 0
            
            for other_tour in all_tours:
                if other_tour != individual_tour:
                    if method == 'hamming':
                        distance = SimilarityCalculator.hamming_distance(individual_tour, other_tour)
                    elif method == 'edge_based':
                        similarity = SimilarityCalculator.edge_based_similarity(individual_tour, other_tour)
                        distance = 1.0 - similarity
                    else:
                        distance = SimilarityCalculator.hamming_distance(individual_tour, other_tour)
                    
                    total_distance += distance
                    count += 1
            
            return total_distance / count if count > 0 else 0.0
        
        except Exception as e:
            logging.warning(f"计算个体多样性贡献时出错: {e}")
            return 0.5
    
    @staticmethod
    def find_most_diverse_individuals(tours: List[List[int]], 
                                    top_k: int = 5, 
                                    method: str = 'hamming') -> List[Tuple[int, float]]:
        """
        找出最具多样性的个体
        
        参数:
            tours: 路径列表
            top_k: 返回前k个最具多样性的个体
            method: 计算方法
            
        返回:
            List[Tuple[int, float]]: (索引, 多样性贡献) 的列表
        """
        diversity_contributions = []
        
        for i, tour in enumerate(tours):
            contribution = DiversityAnalyzer.calculate_individual_diversity_contribution(
                tour, tours, method
            )
            diversity_contributions.append((i, contribution))
        
        # 按多样性贡献排序
        diversity_contributions.sort(key=lambda x: x[1], reverse=True)
        
        return diversity_contributions[:top_k]
    
    @staticmethod
    def find_most_similar_pairs(tours: List[List[int]], 
                              top_k: int = 5, 
                              method: str = 'hamming') -> List[Tuple[int, int, float]]:
        """
        找出最相似的路径对
        
        参数:
            tours: 路径列表
            top_k: 返回前k个最相似的路径对
            method: 计算方法
            
        返回:
            List[Tuple[int, int, float]]: (索引1, 索引2, 相似度) 的列表
        """
        similarities = []
        
        for i in range(len(tours)):
            for j in range(i + 1, len(tours)):
                if method == 'hamming':
                    similarity = SimilarityCalculator.hamming_similarity(tours[i], tours[j])
                elif method == 'edge_based':
                    similarity = SimilarityCalculator.edge_based_similarity(tours[i], tours[j])
                else:
                    similarity = SimilarityCalculator.hamming_similarity(tours[i], tours[j])
                
                similarities.append((i, j, similarity))
        
        # 按相似度排序（降序）
        similarities.sort(key=lambda x: x[2], reverse=True)
        
        return similarities[:top_k]


class PatternAnalyzer:
    """模式分析工具类"""
    
    @staticmethod
    def find_common_subsequences(tours: List[List[int]], 
                               min_length: int = 3, 
                               min_frequency: float = 0.3) -> List[Dict[str, Any]]:
        """
        找出路径中的公共子序列模式
        
        参数:
            tours: 路径列表
            min_length: 最小子序列长度
            min_frequency: 最小出现频率
            
        返回:
            List[Dict[str, Any]]: 公共模式列表
        """
        if not tours:
            return []
        
        pattern_counts = defaultdict(int)
        total_tours = len(tours)
        
        # 提取所有可能的子序列
        for tour in tours:
            for length in range(min_length, min(len(tour) + 1, 6)):  # 限制最大长度为5
                for i in range(len(tour)):
                    # 循环子序列
                    pattern = tuple(tour[i:i+length] if i+length <= len(tour) 
                                  else tour[i:] + tour[:i+length-len(tour)])
                    if len(pattern) == length:
                        pattern_counts[pattern] += 1
        
        # 筛选高频模式
        common_patterns = []
        for pattern, count in pattern_counts.items():
            frequency = count / total_tours
            if frequency >= min_frequency:
                common_patterns.append({
                    "pattern": list(pattern),
                    "frequency": frequency,
                    "count": count,
                    "length": len(pattern)
                })
        
        # 按频率排序
        common_patterns.sort(key=lambda x: x["frequency"], reverse=True)
        
        return common_patterns
